<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Smart Test Interface</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        .question-nav{position:fixed;top:70px;right:0;height:calc(100vh - 70px);width:320px;transition:all .3s;z-index:100;display:flex;flex-direction:column}
        .question-content{margin-right:0;transition:all .3s}
        .question-box{width:40px;height:40px;margin:5px;cursor:pointer}
        .attempted{background-color:#3498db!important;color:white}
        .correct{background-color:#2ecc71!important;color:white}
        .incorrect{background-color:#e74c3c!important;color:white}
        .current{border:3px solid #f39c12!important}
        #question-boxes{overflow-y:auto;max-height:calc(100vh - 150px);padding-bottom:15px}
        @media (max-width:768px){.question-nav{width:100%;right:-100%}.question-content{margin-right:0}}
    </style>
</head>
<body>
    <div class="container-fluid p-0">
        <!-- Header with controls -->
        <nav class="navbar navbar-expand-lg navbar-dark bg-primary sticky-top">
            <div class="container-fluid">
                <a class="navbar-brand" href="#">
                    <i class="fas fa-graduation-cap me-2"></i>Smart Test
                </a>
                <!-- Section selector -->
                <div class="navbar-nav me-auto section-tabs">
                    <select class="form-select" id="section-selector" onchange="switchSection(this.value)">
                        <option value="all">All Sections</option>
                    </select>
                </div>
                <div class="d-flex align-items-center">
                    <!-- Timer -->
                    <div class="bg-light text-primary rounded px-3 py-2 me-2 fw-bold" id="timer">
                        <i class="far fa-clock me-2"></i><span id="timer-display">100:00</span>
                    </div>
                    <!-- Submit button -->
                    <button class="btn btn-success me-2" id="submit-btn" onclick="submitTest()">
                        <i class="fas fa-check me-1"></i> Submit
                    </button>
                    <!-- Language toggle -->
                    <button class="btn btn-light me-2" id="lang-btn" onclick="toggleLanguage()">
                        <i class="fas fa-language"></i>
                    </button>
                    <!-- Menu toggle for mobile -->
                    <button class="btn btn-light d-lg-none" id="menu-toggle" onclick="toggleMenu()">
                        <i class="fas fa-th"></i>
                    </button>
                </div>
            </div>
        </nav>
        <div class="row m-0">
            <!-- Main content area -->
            <div class="col p-0 question-content" id="question-content">
                <div class="container py-4">
                    <!-- Question navigation -->
                    <div class="d-flex justify-content-between mb-4">
                        <button class="btn btn-outline-primary" onclick="prevQuestion()">
                            <i class="fas fa-chevron-left me-1"></i> Previous
                        </button>
                        <div class="fw-bold" id="question-counter">Question 1 of 100</div>
                        <button class="btn btn-outline-primary" onclick="nextQuestion()">
                            Next <i class="fas fa-chevron-right ms-1"></i>
                        </button>
                    </div>
                    <!-- Questions will be displayed here -->
                    <div id="questions-container"></div>
                </div>
            </div>
            <!-- Question navigation sidebar -->
            <div class="bg-light question-nav" id="question-nav">
                <div class="p-3">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h5 class="m-0">Questions</h5>
                        <div>
                            <span class="badge bg-secondary me-1" id="attempted-count">0</span>
                            <span class="badge bg-secondary" id="total-count">100</span>
                            <button class="btn btn-sm btn-outline-secondary ms-2 d-lg-none" onclick="toggleMenu()">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    </div>
                    <div class="d-flex flex-wrap" id="question-boxes"></div>
                </div>
            </div>
        </div>
        <!-- Results modal -->
        <div class="modal fade" id="results-modal" tabindex="-1" aria-hidden="true">
            <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content">
                    <div class="modal-header bg-primary text-white">
                        <h5 class="modal-title">Test Results</h5>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <div class="row text-center">
                            <div class="col-6 mb-3">
                                <div class="p-3 bg-primary bg-opacity-10 rounded">
                                    <h3 id="score-value">0</h3>
                                    <div class="small text-muted">Score</div>
                                </div>
                            </div>
                            <div class="col-6 mb-3">
                                <div class="p-3 bg-success bg-opacity-10 rounded">
                                    <h3 id="correct-value">0</h3>
                                    <div class="small text-muted">Correct</div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="p-3 bg-danger bg-opacity-10 rounded">
                                    <h3 id="incorrect-value">0</h3>
                                    <div class="small text-muted">Incorrect</div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="p-3 bg-secondary bg-opacity-10 rounded">
                                    <h3 id="unattempted-value">0</h3>
                                    <div class="small text-muted">Unattempted</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-primary" onclick="reviewTest()">Review Test</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 100 * 60;
        let resultsModal;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            resultsModal = new bootstrap.Modal(document.getElementById('results-modal'));
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 768) {
                document.getElementById('question-content').style.marginRight = '320px';
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["6"] = {
                name: "Reasoning",
                start: 0,
                end: 23
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="6">Reasoning</option>`;
            sections["18"] = {
                name: "General Awareness",
                start: 24,
                end: 48
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="18">General Awareness</option>`;
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 49,
                end: 73
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["7"] = {
                name: "English",
                start: 74,
                end: 98
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="7">English</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 99,
                end: 99
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "6",
                    question_en: "<p>1. In a certain code language,<br>\'M &amp; N\' means \'M is the wife of N\',<br>\'M @ N\' means \'M is the brother of N\',<br>\'M $ N\' means \'M is the daughter of N\',<br>\'M # N\' means \'M is the son of N\'.<br>Based on the above, how is Q related to N if \'N # M @ O $ P &amp; Q\'?</p>",
                    question_hi: "<p>1. एक निश्चित कूट भाषा में,<br>\'M &amp; N\' का अर्थ है, \'M, N की पत्नी है\',<br>\'M @ N\' का अर्थ है, \'M, N का भाई है\',<br>\'M $N\' का अर्थ है, \'M, N की बेटी है\',<br>\'M #N\' का अर्थ है, \'M, N का बेटा है।<br>उपरोक्त के आधार पर, यदि N#M@ O $ P &amp; Q\'है, तो Q का N से क्या संबंध है?</p>",
                    options_en: [
                        "<p>Brother</p>",
                        "<p>Father</p>",
                        "<p>Father\'s father</p>",
                        "<p>Mother\'s father</p>"
                    ],
                    options_hi: [
                        "<p>भाई</p>",
                        "<p>पिता</p>",
                        "<p>पिता के पिता</p>",
                        "<p>माँ के पिता</p>"
                    ],
                    solution_en: "<p>1.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741335129075.png\" alt=\"rId4\" width=\"149\" height=\"144\"><br>Q is the Father&rsquo;s father of N.</p>",
                    solution_hi: "<p>1.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741335129075.png\" alt=\"rId4\" width=\"149\" height=\"144\"><br>Q, N के पिता का पिता है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "6",
                    question_en: "<p>2. Select the correct mirror image of the given figure when the mirror is placed at MN as&nbsp;shown below.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741335129193.png\" alt=\"rId5\" width=\"117\" height=\"148\"></p>",
                    question_hi: "<p>2. दी गई आकृति के उस सही दर्पण प्रतिबिंब को चुनिए, जो नीचे दर्शाए गए अनुसार दर्पण को MN पर रखने पर बनेगा।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741335129193.png\" alt=\"rId5\" width=\"117\" height=\"148\"></p>",
                    options_en: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741335129311.png\" alt=\"rId6\" width=\"100\" height=\"105\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741335129444.png\" alt=\"rId7\" width=\"107\" height=\"99\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741335129551.png\" alt=\"rId8\" width=\"100\" height=\"97\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741335129683.png\" alt=\"rId9\" width=\"110\" height=\"109\"></p>"
                    ],
                    options_hi: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741335129311.png\" alt=\"rId6\" width=\"101\" height=\"106\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741335129444.png\" alt=\"rId7\" width=\"100\" height=\"93\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741335129551.png\" alt=\"rId8\" width=\"100\" height=\"98\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741335129683.png\" alt=\"rId9\" width=\"105\" height=\"104\"></p>"
                    ],
                    solution_en: "<p>2.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741335129683.png\" alt=\"rId9\" width=\"118\" height=\"117\"></p>",
                    solution_hi: "<p>2.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741335129683.png\" alt=\"rId9\" width=\"118\" height=\"117\"></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "6",
                    question_en: "<p>3. The following Venn diagram shows people\'s liking for Tea, Coffee and Soup.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741335129812.png\" alt=\"rId10\" width=\"190\" height=\"159\"> <br>How many people like tea and coffee both?</p>",
                    question_hi: "<p>3. नीचे दिया गया वेन आरेख चाय (Tea), कॉफी (Coffee) और सूप (Soup) के लिए लोगों की पसंद को दर्शाता है।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741335129972.png\" alt=\"rId11\" width=\"184\" height=\"157\"> <br>Tea - चाय, Coffee - कॉफी, Soup - सूप<br>कितने लोग चाय और कॉफी दोनों पसंद करते हैं?</p>",
                    options_en: [
                        "<p>43</p>",
                        "<p>29</p>",
                        "<p>21</p>",
                        "<p>13</p>"
                    ],
                    options_hi: [
                        "<p>43</p>",
                        "<p>29</p>",
                        "<p>21</p>",
                        "<p>13</p>"
                    ],
                    solution_en: "<p>3.(c)<br>Number of People like tea and coffee both = 13 + 8 = 21</p>",
                    solution_hi: "<p>3.(c)<br>चाय और कॉफ़ी दोनों पसंद करने वाले व्यक्तियों की संख्या = 13 + 8 = 21</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "6",
                    question_en: "<p>4. The position of how many letters will remain unchanged if each of the letter in the word &lsquo;JANITOR&rsquo; is arranged in English alphabetical order?</p>",
                    question_hi: "<p>4. यदि शब्द \'JANITOR\' के प्रत्येक अक्षर को अंग्रेजी वर्णमाला क्रम में व्यवस्थित किया जाए, तो कितने अक्षरों की स्थिति अपरिवर्तित रहेगी?</p>",
                    options_en: [
                        "<p>Three</p>",
                        "<p>Two</p>",
                        "<p>One</p>",
                        "<p>Zero</p>"
                    ],
                    options_hi: [
                        "<p>तीन</p>",
                        "<p>दो</p>",
                        "<p>एक</p>",
                        "<p>शून्&zwj;य</p>"
                    ],
                    solution_en: "<p>4.(d) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741335130078.png\" alt=\"rId12\" width=\"247\" height=\"128\"><br>The position of all the letters are changed.</p>",
                    solution_hi: "<p>4.(d) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741335130078.png\" alt=\"rId12\" width=\"247\" height=\"128\"><br>सभी अक्षरों का स्थान बदल दिया गया है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "6",
                    question_en: "<p>5. In this question, three statements are given, followed by three conclusions numbered I, II and III. Assuming the statements to be true, even if they seem to be at variance with commonly known facts, decide which of the conclusion(s) logically follows/follow from the statements.<br><strong>Statements :</strong><br>Some lions are panthers.<br>No panther is a tiger.<br>Some lions are cats.<br><strong>Conclusions :</strong><br>I. No tiger is a cat.<br>II. Some lions are tiger.<br>III. No cat is a panther.</p>",
                    question_hi: "<p>5. इस प्रश्न में तीन कथन दिए गए हैं, जिनके बाद तीन निष्कर्ष I, II और III दिए गए हैं। कथनों को सत्य मानते हुए भले ही वे सामान्य रूप से ज्ञात तथ्यों सेअलग प्रतीत होते हों, निर्धारित करें कि कौन-सा/से निष्कर्ष दिए गए कथनों का तार्किक रूप से अनुसरण करता है/करते हैं।<br><strong>कथन :</strong><br>कुछ शेर, तेंदुए हैं।<br>कोई तेंदुआ, बाघ नहीं है।<br>कुछ शेर, बिल्लियाँ हैं।<br><strong>निष्कर्ष :</strong><br>I. कोई बाघ, बिल्ली नहीं है।<br>II. कुछ शेर, बाघ हैं।<br>III. कोई बिल्ली , तेंदुआ नहीं है।</p>",
                    options_en: [
                        "<p>Only conclusion I follow.</p>",
                        "<p>Neither conclusions I, II nor III follows.</p>",
                        "<p>Both conclusions I and II follow.</p>",
                        "<p>Both conclusions II and III follow.</p>"
                    ],
                    options_hi: [
                        "<p>केवल निष्कर्ष I अनुसरण करता है।</p>",
                        "<p>न तो निष्कर्ष I, II अनुसरण करता है और न ही निष्कर्ष III अनुसरण करता है।</p>",
                        "<p>निष्कर्ष I और II, दोनों अनुसरण करते हैं।</p>",
                        "<p>निष्कर्ष II और III, दोनों अनुसरण करते हैं।</p>"
                    ],
                    solution_en: "<p>5.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741335130212.png\" alt=\"rId13\" width=\"333\" height=\"75\"><br>Neither conclusion I,II nor III follows.</p>",
                    solution_hi: "<p>5.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741335130328.png\" alt=\"rId14\" width=\"304\" height=\"61\"><br>न तो निष्कर्ष I, II और न ही III अनुसरण करता है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "6",
                    question_en: "<p>6. How many semi circles are there in the given figure?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741335130536.png\" alt=\"rId15\" width=\"119\" height=\"120\"></p>",
                    question_hi: "<p>6. दी गई आकृति में कितने अर्धवृत्त हैं?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741335130536.png\" alt=\"rId15\" width=\"119\" height=\"120\"></p>",
                    options_en: [
                        "<p>9</p>",
                        "<p>12</p>",
                        "<p>10</p>",
                        "<p>8</p>"
                    ],
                    options_hi: [
                        "<p>9</p>",
                        "<p>12</p>",
                        "<p>10</p>",
                        "<p>8</p>"
                    ],
                    solution_en: "<p>6.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741335130730.png\" alt=\"rId16\" width=\"147\" height=\"149\"><br>There are 8 semicircle<br>ABC , CDA, ABD, BCD, EFG, GHE, EFH, FGH</p>",
                    solution_hi: "<p>6.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741335130730.png\" alt=\"rId16\" width=\"147\" height=\"149\"><br>8 अर्धवृत्त हैं<br>ABC , CDA, ABD, BCD, EFG, GHE, EFH, FGH</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "6",
                    question_en: "<p>7. &lsquo;A &ndash; B&rsquo; means &lsquo;A is B&rsquo;s wife&rsquo;.<br>&lsquo;A + B&rsquo; means &lsquo;A is B&rsquo;s brother&rsquo;.<br>&lsquo;A &times; B&rsquo; means &lsquo;A is B&rsquo;s father&rsquo;.<br>&lsquo;A &divide; B&rsquo; means &lsquo;A is B&rsquo;s sister&rsquo;.<br>Using the same meaning of the mathematical operators as given above, in &lsquo;T &times; X &divide; Y + Z&rsquo; what is T of Z&rsquo;s ?</p>",
                    question_hi: "<p>7. &lsquo;A - B\' का अर्थ है \'A, B की पत्नी है\'।<br>\'A + B\' का अर्थ है \'A, B का भाई है\'।<br>\'A &times; B\' का अर्थ है \'A, B के पिता है\'।<br>\'A &divide; B\' का अर्थ है \'A, B की बहन है\'।<br>ऊपर दिए गए गणितीय संकारकों के समान अर्थ का उपयोग करते हुए ज्ञात करें कि \'T &times; X &divide; Y + Z\' में T, Z का क्या है?</p>",
                    options_en: [
                        "<p>Father</p>",
                        "<p>Mother</p>",
                        "<p>Son</p>",
                        "<p>Brother</p>"
                    ],
                    options_hi: [
                        "<p>पिता</p>",
                        "<p>माँ</p>",
                        "<p>पुत्र</p>",
                        "<p>भाई</p>"
                    ],
                    solution_en: "<p>7.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741335130862.png\" alt=\"rId17\" width=\"178\" height=\"102\"><br>T is Z&rsquo;s father.</p>",
                    solution_hi: "<p>7.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741335130862.png\" alt=\"rId17\" width=\"178\" height=\"102\"><br>T, Z का पिता है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "6",
                    question_en: "<p>8. What should come in place of ? in the given series?<br>8, 15, 29, 57,113 ?</p>",
                    question_hi: "<p>8. दी गई श्रृंखला में प्रश्न चिह्न \'?\' के स्थान पर क्या आना चाहिए?<br>8, 15, 29, 57,113 ?</p>",
                    options_en: [
                        "<p>223</p>",
                        "<p>225</p>",
                        "<p>222</p>",
                        "<p>224</p>"
                    ],
                    options_hi: [
                        "<p>223</p>",
                        "<p>225</p>",
                        "<p>222</p>",
                        "<p>224</p>"
                    ],
                    solution_en: "<p>8.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741335131108.png\" alt=\"rId18\" width=\"320\" height=\"63\"></p>",
                    solution_hi: "<p>8.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741335131108.png\" alt=\"rId18\" width=\"320\" height=\"63\"></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "6",
                    question_en: "<p>9. Six numbers 5, 3, 6, 7, 9 and 2 are written on different faces of a dice. Two positions of this dice are shown in the figure below. Find the number on the face opposite to the one having 5.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741335131346.png\" alt=\"rId19\" width=\"186\" height=\"111\"></p>",
                    question_hi: "<p>9. एक पासे के विभिन्न फलकों पर छह संख्याएँ 5, 3, 6, 7, 9 और 2 अंकित हैं। इस पासे की दो स्थितियाँ नीचे दी गई आकृति में दर्शाई गई हैं। 5 वाले फलक के विपरीत फलक पर संख्या ज्ञात कीजिए। <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741335131346.png\" alt=\"rId19\" width=\"186\" height=\"111\"></p>",
                    options_en: [
                        "<p>9</p>",
                        "<p>6</p>",
                        "<p>3</p>",
                        "<p>2</p>"
                    ],
                    options_hi: [
                        "<p>9</p>",
                        "<p>6</p>",
                        "<p>3</p>",
                        "<p>2</p>"
                    ],
                    solution_en: "<p>9.(d)<br>From both the dice the opposite faces are -<br>3 &harr; 9 , 5 &harr; 2, 6 &harr; 7</p>",
                    solution_hi: "<p>9.(d)<br>दोनों पासों के विपरीत फलक हैं -<br>3 &harr; 9 , 5 &harr; 2, 6 &harr; 7</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "6",
                    question_en: "<p>10. Five friends, Ashish, Bipin, Chandu, Dinesh and Emmanuel, went on a trek. On the mountain top, they lit a campfire and sat around it in a circle facing the centre (but not necessarily in the same order). It was recalled that there was only one person sitting between Ashish and Chandu, while Chandu himself was sitting second to the left of Bipin.&nbsp;If Dinesh was an immediate neighbour of both Chandu and Bipin, who was sitting to the immediate right of Ashish ?</p>",
                    question_hi: "<p>10. पाँच मित्र, आशीष, बिपिन, चंदू, दिनेश और इमैनुएल, एक ट्रेक (trek) पर गए। उन्होंने पहाड़ी की चोटी पर एक अलाव (campfire) जलाया और एक घेरा बनाकर इसके चारों ओर केंद्र की ओर मुख करके बैठ गए (परंतु जरूरी नहीं कि वे इसी क्रम में बैठे हों)। यह स्मरण होता है कि आशीष और चंदू के बीच में केवल एक व्यक्ति बैठा था, जबकि चंदू स्वयं बिपिन के बाएं से दूसरे स्थान पर बैठा था। यदि चंदू और बिपिन दोनों का निकटतम पड़ोसी दिनेश था, तो आशीष के ठीक दाएं कौन बैठा था?</p>",
                    options_en: [
                        "<p>Chandu</p>",
                        "<p>Dinesh</p>",
                        "<p>Bipin</p>",
                        "<p>Emmanuel</p>"
                    ],
                    options_hi: [
                        "<p>चंदू</p>",
                        "<p>दिनेश</p>",
                        "<p>बिपिन</p>",
                        "<p>इमैनुएल</p>"
                    ],
                    solution_en: "<p>10.(d) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741335131549.png\" alt=\"rId20\" width=\"163\" height=\"130\"></p>",
                    solution_hi: "<p>10.(d) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741335131724.png\" alt=\"rId21\" width=\"167\" height=\"144\"></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "6",
                    question_en: "<p>11. What will come in the place of the question mark (?) in the following equation if \'+\' and \'&divide;\' are interchanged and \'&times;\' and \'-\' are interchanged? <br>39 + 13 &times; 12 &divide; 5 - 4 = ?</p>",
                    question_hi: "<p>11. निम्नलिखित समीकरण में प्रश्न चिह्न (?) के स्थान पर क्या आयेगा यदि &lsquo;+&rsquo; और \'&divide;\' को आपस में बदल दिया जाए और \'&times;\' और \'-\' को आपस में बदल दिया जाए?<br>39 + 13 &times; 12 &divide; 5 - 4 = ?</p>",
                    options_en: [
                        "<p>7</p>",
                        "<p>11</p>",
                        "<p>9</p>",
                        "<p>13</p>"
                    ],
                    options_hi: [
                        "<p>7</p>",
                        "<p>11</p>",
                        "<p>9</p>",
                        "<p>13</p>"
                    ],
                    solution_en: "<p>11.(b) <strong>Given :-</strong> 39 + 13 &times; 12 &divide; 5 - 4<br>As per given instruction after interchanging the &lsquo;+&rsquo; and &lsquo;&divide;&rsquo; and &lsquo;-&rsquo; and &lsquo;&times;&rsquo; we get<br>39 &divide; 13 - 12 + 5 &times; 4<br>3 - 12 + 20 = 11</p>",
                    solution_hi: "<p>11.(b) <strong>दिया गया :-</strong> 39 + 13 &times; 12 &divide; 5 - 4<br>दिए गए निर्देश के अनुसार \'+\' और \'&divide;\' तथा \'-\' और \'&times;\' को आपस में बदलने के बाद हमें प्राप्त होता है<br>39 &divide; 13 - 12 + 5 &times; 4<br>3 - 12 + 20 = 11</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "6",
                    question_en: "<p>12. A paper is folded and cut as shown below. How will it appear when unfolded? <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741335131926.png\" alt=\"rId22\" width=\"250\" height=\"78\"></p>",
                    question_hi: "<p>12. एक कागज को नीचे दिखाए अनुसार मोड़ा और काटा जाता है। खोलने पर यह कैसा दिखाई देगा?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741335131926.png\" alt=\"rId22\" width=\"250\" height=\"78\"></p>",
                    options_en: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741335132135.png\" alt=\"rId23\" width=\"95\" height=\"98\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741335132326.png\" alt=\"rId24\" width=\"95\" height=\"98\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741335132525.png\" alt=\"rId25\" width=\"95\" height=\"98\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741335132705.png\" alt=\"rId26\" width=\"95\" height=\"95\"></p>"
                    ],
                    options_hi: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741335132135.png\" alt=\"rId23\" width=\"96\" height=\"99\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741335132326.png\" alt=\"rId24\" width=\"95\" height=\"98\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741335132525.png\" alt=\"rId25\" width=\"96\" height=\"99\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741335132705.png\" alt=\"rId26\" width=\"95\" height=\"98\"></p>"
                    ],
                    solution_en: "<p>12.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741335132326.png\" alt=\"rId24\" width=\"95\" height=\"98\"></p>",
                    solution_hi: "<p>12.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741335132326.png\" alt=\"rId24\" width=\"95\" height=\"98\"></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "6",
                    question_en: "<p>13. Read the given statements and conclusions carefully. Assuming that the information given in the statements is true, even if it appears to be at variance with commonly known facts, decide which of the given conclusions logically follow(s) from the statements.<br><strong>Statements :</strong><br>Some movies are videos.<br>No video is audio.<br>All audios are songs.<br><strong>Conclusions :</strong><br>(I) All movies can never be audios.<br>(II) No video is a song.</p>",
                    question_hi: "<p>13. दिए गए कथनों और निष्कर्षों को ध्यानपूर्वक पढ़ें। यह मानते हुए कि कथनों में दी गई जानकारी सत्य है, भले ही यह सामान्य रूप से ज्ञात तथ्यों से भिन्न प्रतीत होती हो, निर्णय लें कि दिए गए निष्कर्षों में से कौन सा/से निष्कर्ष कथनों का तार्किक रूप से अनुसरण करता है/करते हैं।<br><strong>कथन :</strong><br>कुछ फिल्में, वीडियो हैं।<br>कोई भी वीडियो, ऑडियो नहीं है।<br>सभी ऑडियो, गाने हैं।<br><strong>निष्कर्ष :</strong><br>(I) सभी फिल्में, कभी भी ऑडियो नहीं हो सकती हैं।<br>(II) कोई भी वीडियो, गाना नहीं है।</p>",
                    options_en: [
                        "<p>Only conclusion II follows</p>",
                        "<p>None of the conclusions follow</p>",
                        "<p>Both conclusions I and II follow</p>",
                        "<p>Only conclusion I follows</p>"
                    ],
                    options_hi: [
                        "<p>केवल निष्कर्ष II अनुसरण करता है</p>",
                        "<p>कोई भी निष्कर्ष अनुसरण नहीं करता है</p>",
                        "<p>दोनों निष्कर्ष I और II अनुसरण करते हैं</p>",
                        "<p>केवल निष्कर्ष I अनुसरण करता है</p>"
                    ],
                    solution_en: "<p>13.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741335132935.png\" alt=\"rId27\" width=\"298\" height=\"63\"><br>Only conclusion I follows.</p>",
                    solution_hi: "<p>13.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741335133342.png\" alt=\"rId28\" width=\"283\" height=\"60\"><br>केवल निष्कर्ष I अनुसरण करता है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "6",
                    question_en: "<p>14. In a certain code language, \'cooler is hot\' is written as \'tp rc dl\' and \'hot weather comes\' is written as \'rc dn nz\'. How is \'hot\' written in the given language?</p>",
                    question_hi: "<p>14. एक निश्चित कूट भाषा में, \'cooler is hot\' को \'tp rc dl\' लिखा जाता है और \'hot weather comes\' को \'rc dn nz\' लिखा जाता है। उसी कूट भाषा में \'hot\' को कैसे लिखा जाएगा?</p>",
                    options_en: [
                        "<p>nz</p>",
                        "<p>dl</p>",
                        "<p>rc</p>",
                        "<p>dn</p>"
                    ],
                    options_hi: [
                        "<p>nz</p>",
                        "<p>dl</p>",
                        "<p>rc</p>",
                        "<p>dn</p>"
                    ],
                    solution_en: "<p>14.(c)<br><strong id=\"docs-internal-guid-e6c5684d-7fff-8530-ad6a-fe6b6f58c174\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXeHuHBjZst1ZDcr5tyfdtMigkZyJMEDxM-iqEY16Peau34Qa9dkuLmCoF1kqjVBwg4W9UWP0To2Ma_eos-tUl3Cw_YqdbNUxnsKIfd0WDD4Jm_nHHjVC3pFH__l6A8H80QqUIuTYg?key=d6iqhulIo7LfVtfWMajg2Zz6\" width=\"392\" height=\"67\"></strong><br>Hence, code for &lsquo;hot&rsquo; is &lsquo;rc&rsquo;.</p>",
                    solution_hi: "<p>14.(c) <br><strong id=\"docs-internal-guid-e6c5684d-7fff-8530-ad6a-fe6b6f58c174\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXeHuHBjZst1ZDcr5tyfdtMigkZyJMEDxM-iqEY16Peau34Qa9dkuLmCoF1kqjVBwg4W9UWP0To2Ma_eos-tUl3Cw_YqdbNUxnsKIfd0WDD4Jm_nHHjVC3pFH__l6A8H80QqUIuTYg?key=d6iqhulIo7LfVtfWMajg2Zz6\" width=\"392\" height=\"67\"></strong><br>अतः , \'hot\' का कूट \'rc\' है ।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "6",
                    question_en: "<p>15. The position of how many letters will remain unchanged if each of the letters in the word &lsquo;JEALOUS&rsquo; is arranged in the English alphabetical order?</p>",
                    question_hi: "<p>15. यदि शब्द \'JEALOUS\' के प्रत्येक अक्षर को अंग्रेजी वर्णमाला के क्रम में व्यवस्थित किया जाए तो कितने अक्षरों की स्थिति अपरिवर्तित रहेगी?</p>",
                    options_en: [
                        "<p>Two</p>",
                        "<p>One</p>",
                        "<p>Three</p>",
                        "<p>Four</p>"
                    ],
                    options_hi: [
                        "<p>दो</p>",
                        "<p>एक</p>",
                        "<p>तीन</p>",
                        "<p>चार</p>"
                    ],
                    solution_en: "<p>15.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741335133761.png\" alt=\"rId30\" width=\"146\" height=\"88\"><br>The position of three letter is changed.</p>",
                    solution_hi: "<p>15.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741335133761.png\" alt=\"rId30\" width=\"146\" height=\"88\"><br>तीन अक्षरों का स्थान बदल दिया गया है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "16",
                    section: "6",
                    question_en: "<p>16. If 9 March 2007 was Friday, then what was the day of the week on 13 March 2012?</p>",
                    question_hi: "<p>16. यदि 9 मार्च 2007 को शुक्रवार था, तो 13 मार्च 2012 को सप्ताह का कौन-सा दिन रहा होगा?</p>",
                    options_en: [
                        "<p>Wednesday</p>",
                        "<p>Monday</p>",
                        "<p>Thursday</p>",
                        "<p>Tuesday</p>"
                    ],
                    options_hi: [
                        "<p>बुधवार</p>",
                        "<p>सोमवार</p>",
                        "<p>गुरुवार</p>",
                        "<p>मंगलवार</p>"
                    ],
                    solution_en: "<p>16.(d) 9 March 2007 is a Friday. On moving from 2007 to 2012 the number of odd days =&nbsp;+2 + 1 + 1 + 1 + 2 = 7. We have reached till 9 March 2012. We have to go to 13 march 2012 . Number of days = 4. Total number of odd days = 11. On dividing 11 by 7 we get remainder = 4. Friday + 4 = Tuesday.</p>",
                    solution_hi: "<p>16.(d) 9 मार्च 2007 शुक्रवार है. 2007 से 2012 तक जाने पर विषम दिनों की संख्या =&nbsp;+ 2 + 1 + 1 + 1 + 2 = 7. हम 9 मार्च 2012 तक पहुँच गए हैं। हमें 13 मार्च 2012 तक जाना है। दिनों की संख्या = 4. विषम दिनों की कुल संख्या = 11 ,11 को 7 से विभाजित करने पर शेषफल = 4 प्राप्त होता है। शुक्रवार + 4 = मंगलवार।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "17",
                    section: "6",
                    question_en: "<p>17. Select the figure from among the given option that can replace the question mark (?) in the following series. <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741335133994.png\" alt=\"rId31\" width=\"354\" height=\"73\"></p>",
                    question_hi: "<p>17. दिए गए विकल्पों में से उस आकृति का चयन कीजिए जो निम्नलिखित शृंखला में प्रश्न चिह्न (?) को प्रतिस्थापित कर सकती है।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741335133994.png\" alt=\"rId31\" width=\"354\" height=\"73\"></p>",
                    options_en: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741335134174.png\" alt=\"rId32\" width=\"95\" height=\"97\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741335134387.png\" alt=\"rId33\" width=\"95\" height=\"97\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741335134546.png\" alt=\"rId34\" width=\"96\" height=\"98\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741335134692.png\" alt=\"rId35\" width=\"95\" height=\"97\"></p>"
                    ],
                    options_hi: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741335134174.png\" alt=\"rId32\" width=\"96\" height=\"98\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741335134387.png\" alt=\"rId33\" width=\"94\" height=\"96\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741335134546.png\" alt=\"rId34\" width=\"96\" height=\"98\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741335134692.png\" alt=\"rId35\" width=\"94\" height=\"96\"></p>"
                    ],
                    solution_en: "<p>17.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741335134387.png\" alt=\"rId33\" width=\"97\" height=\"98\"></p>",
                    solution_hi: "<p>17.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741335134387.png\" alt=\"rId33\" width=\"97\" height=\"98\"></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "18",
                    section: "6",
                    question_en: "<p>18. Three of the following letter-clusters are alike in some manner and hence form a group. Which letter-cluster does not belong to that group? <br>(<strong>Note : </strong>The odd one out is not based on the number of consonants/vowels or their position in the letter cluster.)</p>",
                    question_hi: "<p>18. निम्नलिखित चार अक्षर-समूहों में से तीन अक्षर-समूह एक निश्चित प्रकार से समान हैं और इस प्रकार एक समूह बनाते हैं। वह कौन-सा अक्षर-समूह है जो उस समूह से संबंधित नहीं है?<br>(<strong>ध्यान दें : </strong>असंगत अक्षर-समूह, उस अक्षर-समूह में व्यंजनों/स्वरों की संख्या या उनके स्थान पर आधारित नहीं है।)</p>",
                    options_en: [
                        " CKP ",
                        " TBG ",
                        " EMS",
                        " LTY"
                    ],
                    options_hi: [
                        " CKP ",
                        " TBG ",
                        " EMS",
                        " LTY"
                    ],
                    solution_en: "<p>18.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741335134831.png\" alt=\"rId36\" width=\"121\" height=\"84\">, <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741335135029.png\" alt=\"rId37\" width=\"124\" height=\"88\"> <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741335135233.png\" alt=\"rId38\" width=\"121\" height=\"83\"><br>But,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741335135353.png\" alt=\"rId39\" width=\"122\" height=\"82\"></p>",
                    solution_hi: "<p>18.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741335134831.png\" alt=\"rId36\" width=\"121\" height=\"84\">, <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741335135029.png\" alt=\"rId37\" width=\"124\" height=\"88\"> <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741335135233.png\" alt=\"rId38\" width=\"121\" height=\"83\"><br>लेकिन,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741335135353.png\" alt=\"rId39\" width=\"122\" height=\"82\"></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "19",
                    section: "6",
                    question_en: "<p>19. HPML is related to MURQ in a certain way based on the English alphabetical order. In the same way, CJGF is related to HOLK. To which of the following is RZWV related, following the same logic?</p>",
                    question_hi: "<p>19. अंग्रेजी वर्णमाला क्रम के आधार पर HPML एक निश्चित प्रकार से MURQ से संबंधित है। ठीक उसी प्रकार CJGF, HOLK से संबंधित है। समान तर्क का अनुसरण करते हुए, RZWV निम्नलिखित में से किससे संबंधित है?</p>",
                    options_en: [
                        "<p>WEBA</p>",
                        "<p>ABWE</p>",
                        "<p>ABEW</p>",
                        "<p>WEAB</p>"
                    ],
                    options_hi: [
                        "<p>WEBA</p>",
                        "<p>ABWE</p>",
                        "<p>ABEW</p>",
                        "<p>WEAB</p>"
                    ],
                    solution_en: "<p>19.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741335135593.png\" alt=\"rId40\" width=\"147\" height=\"107\">&nbsp; ,&nbsp; <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741335135868.png\" alt=\"rId41\" width=\"151\" height=\"112\"><br>Similarly,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741335136151.png\" alt=\"rId42\" width=\"141\" height=\"102\"></p>",
                    solution_hi: "<p>19.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741335135593.png\" alt=\"rId40\" width=\"147\" height=\"107\">&nbsp; ,&nbsp; <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741335135868.png\" alt=\"rId41\" width=\"151\" height=\"112\"><br>इसी प्रकार,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741335136151.png\" alt=\"rId42\" width=\"141\" height=\"102\"></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "20",
                    section: "6",
                    question_en: "<p>20. Select the option figure in which the given figure is embedded as its part (rotation is NOT allowed).<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741335136359.png\" alt=\"rId43\" width=\"121\" height=\"93\"></p>",
                    question_hi: "<p>20. उस विकल्प आकृति का चयन कीजिए जिसमें दी गई आकृति उसके भाग के रूप में निहित है (घूर्णन की अनुमति नहीं है)।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741335136359.png\" alt=\"rId43\" width=\"122\" height=\"94\"></p>",
                    options_en: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741335136566.png\" alt=\"rId44\" width=\"104\" height=\"102\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741335136684.png\" alt=\"rId45\" width=\"104\" height=\"104\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741335136801.png\" alt=\"rId46\" width=\"105\" height=\"103\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741335136934.png\" alt=\"rId47\" width=\"105\" height=\"110\"></p>"
                    ],
                    options_hi: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741335136566.png\" alt=\"rId44\" width=\"105\" height=\"103\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741335136684.png\" alt=\"rId45\" width=\"105\" height=\"105\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741335136801.png\" alt=\"rId46\" width=\"105\" height=\"103\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741335136934.png\" alt=\"rId47\" width=\"105\" height=\"110\"></p>"
                    ],
                    solution_en: "<p>20.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741335137072.png\" alt=\"rId48\" width=\"105\" height=\"103\"></p>",
                    solution_hi: "<p>20.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741335137072.png\" alt=\"rId48\" width=\"105\" height=\"103\"></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "21",
                    section: "6",
                    question_en: "<p>21. Three of the following four are alike in a certain way and thus form a group. Which is&nbsp;the one that does NOT belong to that group ?<br>(Note : The odd one out is not based on the number of consonants/vowels or their&nbsp;position in the letter cluster.)</p>",
                    question_hi: "<p>21. निम्नलिखित चार अक्षर-समूहों में से तीन अक्षर-समूह एक निश्चित प्रकार से समान हैं और इस प्रकार एक समूह बनाते हैं। वह कौन-सा अक्षर-समूह है जो उस समूह से संबंधित नहीं है ?<br><strong>(ध्यान दें : </strong>असंगत अक्षर-समूह, उस अक्षर-समूह में व्यंजनों/स्वरों की संख्या या उनके स्थान पर आधारित नहीं है।)</p>",
                    options_en: [
                        "<p>JMP</p>",
                        "<p>BEI</p>",
                        "<p>KNR</p>",
                        "<p>SVZ</p>"
                    ],
                    options_hi: [
                        "<p>JMP</p>",
                        "<p>BEI</p>",
                        "<p>KNR</p>",
                        "<p>SVZ</p>"
                    ],
                    solution_en: "<p>21.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741335137242.png\" alt=\"rId49\" width=\"136\" height=\"79\">, <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741335137349.png\" alt=\"rId50\" width=\"136\" height=\"86\">,&nbsp; <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741335137487.png\" alt=\"rId51\" width=\"149\" height=\"83\"><br>But,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741335137603.png\" alt=\"rId52\" width=\"128\" height=\"83\"></p>",
                    solution_hi: "<p>21.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741335137242.png\" alt=\"rId49\" width=\"136\" height=\"79\">, <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741335137349.png\" alt=\"rId50\" width=\"136\" height=\"86\">,&nbsp; <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741335137487.png\" alt=\"rId51\" width=\"149\" height=\"83\"><br>लेकिन,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741335137603.png\" alt=\"rId52\" width=\"128\" height=\"83\"></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "22",
                    section: "6",
                    question_en: "<p>22. Select the option that is related to the third word in the same way as the second word is related to the first word. <br>(The words must be considered as meaningful English words and must not be related to each other based on the number of letters/number of consonants/vowels in the word) <br>Pilot : Airplane :: Captain : ?</p>",
                    question_hi: "<p>22. उस विकल्प का चयन करें जो तीसरे शब्द से उसी प्रकार संबंधित है जिस प्रकार दूसरा शब्द पहले शब्द से संबंधित है। <br>(शब्दों को अर्थपूर्ण हिंदी शब्दों के रूप में माना जाना चाहिए और शब्द में अक्षरों की संख्या / व्यंजनों/ स्वरों की संख्या के आधार पर एक-दूसरे से संबंधित नहीं किया जाना चाहिए) <br>पायलट : हवाई जहाज़ :: कैप्टन : ?</p>",
                    options_en: [
                        "<p>Swim</p>",
                        "<p>Sea</p>",
                        "<p>Sail</p>",
                        "<p>Ship</p>"
                    ],
                    options_hi: [
                        "<p>तैरना</p>",
                        "<p>समुद्र</p>",
                        "<p>नाव चलाना</p>",
                        "<p>समुद्री जहाज</p>"
                    ],
                    solution_en: "<p>22.(d) As a pilot is a person who is responsible for safe operation of an Airplane, similarly a Captain is a person who is responsible for safe operation of Ship.</p>",
                    solution_hi: "<p>22.(d) जिस प्रकार एक पायलट वह व्यक्ति होता है जो हवाई जहाज के सुरक्षित संचालन के लिए जिम्मेदार होता है, उसी प्रकार एक कैप्टन वह व्यक्ति होता है जो समुद्री जहाज के सुरक्षित संचालन के लिए जिम्मेदार होता है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "23",
                    section: "6",
                    question_en: "<p>23. In a certain code language, \'BILLION\' is coded as \'3264581\' and \'MILLION\' is coded as \'3249581\'. What is the code for \'M\' in that language?</p>",
                    question_hi: "<p>23. एक निश्चित कूट भाषा में, \'BILLION\' को \'3264581\' लिखा जाता है और \'MILLION\' को \'3249581\' लिखा जाता है। तो उस कूट भाषा में \'M\' को कैसे लिखा जाएगा?</p>",
                    options_en: [
                        "<p>6</p>",
                        "<p>5</p>",
                        "<p>9</p>",
                        "<p>3</p>"
                    ],
                    options_hi: [
                        "<p>6</p>",
                        "<p>5</p>",
                        "<p>9</p>",
                        "<p>3</p>"
                    ],
                    solution_en: "<p>23.(c) <br>BILLION &rarr; 3264581&hellip;..(i)<br>MILLION &rarr; 3249581&hellip;&hellip;(ii)<br>From (i) and (ii) &lsquo;ILLION&rsquo; and &lsquo;324581&rsquo; are common. The code of &lsquo;M&rsquo; = &lsquo;9&rsquo;.</p>",
                    solution_hi: "<p>23.(c) <br>BILLION &rarr; 3264581&hellip;..(i)<br>MILLION &rarr; 3249581&hellip;&hellip;(ii)<br>(i) और (ii) से \'ILLION\' और \'324581\' उभयनिष्ठ हैं। \'M\' का कोड = \'9\'.</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "24",
                    section: "6",
                    question_en: "<p>24. The position of how many letters will remain unchanged if all the letters in the word BINDER are arranged in English alphabetical order?</p>",
                    question_hi: "<p>24. यदि BINDER शब्द के सभी अक्षरों को वर्णानुक्रम में व्यवस्थित किया जाए, तो कितने अक्षरों की स्थिति अपरिवर्तित बनी रहेगी?</p>",
                    options_en: [
                        "<p>None</p>",
                        "<p>One</p>",
                        "<p>Two</p>",
                        "<p>Three</p>"
                    ],
                    options_hi: [
                        "<p>किसी का भी नहीं</p>",
                        "<p>एक</p>",
                        "<p>दो</p>",
                        "<p>तीन</p>"
                    ],
                    solution_en: "<p>24.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741335137776.png\" alt=\"rId53\" width=\"194\" height=\"125\"><br>The position of the two letters remain unchanged.</p>",
                    solution_hi: "<p>24.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741335137776.png\" alt=\"rId53\" width=\"194\" height=\"125\"><br>दो अक्षरों का स्थान अपरिवर्तित रहता है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "25",
                    section: "18",
                    question_en: "<p>25. What should come in place of the question mark (?) in the given series? <br>27, 32, 42, 47, 57,?</p>",
                    question_hi: "<p>25. दी गई श्रृंखला में प्रश्न चिह्न (?) के स्थान पर क्या आना चाहिए?<br>27, 32, 42, 47, 57,?</p>",
                    options_en: [
                        "<p>63</p>",
                        "<p>62</p>",
                        "<p>61</p>",
                        "<p>60</p>"
                    ],
                    options_hi: [
                        "<p>63</p>",
                        "<p>62</p>",
                        "<p>61</p>",
                        "<p>60</p>"
                    ],
                    solution_en: "<p>25.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741335137884.png\" alt=\"rId54\" width=\"300\" height=\"92\"></p>",
                    solution_hi: "<p>25.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741335137884.png\" alt=\"rId54\" width=\"300\" height=\"92\"></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "26",
                    section: "18",
                    question_en: "<p>26. Identify the word that means the minimum distance at which objects can be seen most distinctly without strain.</p>",
                    question_hi: "<p>26. उस शब्द की पहचान कीजिए जिसका अर्थ वह न्यूनतम दूरी है जिस पर वस्तुओं को बिना परिश्रम (strain) के सबसे स्पष्ट रूप से देखा जा सकता है।</p>",
                    options_en: [
                        "<p>Least distant of distance vision</p>",
                        "<p>Most distance of distant vision</p>",
                        "<p>Focal length</p>",
                        "<p>Least distance of distinct vision</p>"
                    ],
                    options_hi: [
                        "<p>दूर दृष्टि की न्यूनतम दूरी</p>",
                        "<p>दूर दृष्टि की अधिकतम दूरी</p>",
                        "<p>फोकस दूरी</p>",
                        "<p>स्पष्ट दृष्टि की न्यूनतम दूरी</p>"
                    ],
                    solution_en: "<p>26.(d) <strong>Least distance of distinct vision.</strong> The near point of the eye is the minimum distance at which objects can be seen most distinctly without strain. For a normal eye, the value of the least distance of distinct vision is d = 25 cm. Hypermetropia (far-sightedness) : A person can see objects at a distance but cannot see nearby objects clearly. It can be corrected by convex lenses.</p>",
                    solution_hi: "<p>26.(d) <strong>स्पष्ट दृष्टि की न्यूनतम दूरी।</strong> आँख का निकटतम बिंदु वह न्यूनतम दूरी है जिस पर वस्तुओं को बिना तनाव के सबसे स्पष्ट रूप से देखा जा सकता है। एक सामान्य आंख के लिए, स्पष्ट दृष्टि की न्यूनतम दूरी का मान d = 25 सेमी है। हाइपरमेट्रोपिया (दूरदृष्टि दोष) : व्यक्ति दूर की वस्तुओं को देख सकता है लेकिन पास की वस्तुओं को स्पष्ट रूप से नहीं देख पाता है। इसे उत्तल लेंस द्वारा ठीक किया जा सकता है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "27",
                    section: "18",
                    question_en: "<p>27. Match the following musical instruments with the maestros who play them.<br>a. Santoor&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; i. Bismillah Khan<br>b. Sitar&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;ii. Ustad Binda Khan<br>c. Shehnai&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;iii. Pt Shiv Kumar Sharma<br>d. Sarangi&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; iv. Anushka Shankar</p>",
                    question_hi: "<p>27. निम्नलिखित संगीत वाद्ययंत्रों का मिलान उन्हें बजाने वाले उस्तादों के साथ करें।<br>a. संतूर&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; i. बिस्मिल्लाह खान<br>b. सितार&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; ii. उस्ताद बिंदा खान<br>c. शहनाई&nbsp; &nbsp; &nbsp; &nbsp; iii. पंडित शिव कुमार शर्मा<br>d. सारंगी&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;iv. अनुष्का शंकर</p>",
                    options_en: [
                        "<p>a-ii, b-iii, c-i, d-iv</p>",
                        "<p>a-iv, b-iii, c-ii, d-i</p>",
                        "<p>a-iii, b-iv, c-i, d-ii</p>",
                        "<p>a-i, b-ii, c-iii, d-iv</p>"
                    ],
                    options_hi: [
                        "<p>a-ii, b-iii, c-i, d-iv</p>",
                        "<p>a-iv, b-iii, c-ii, d-i</p>",
                        "<p>a-iii, b-iv, c-i, d-ii</p>",
                        "<p>a-i, b-ii, c-iii, d-iv</p>"
                    ],
                    solution_en: "<p>27.(c) <strong>a-iii, b-iv, c-i, d-ii.</strong> Famous musical instruments and their exponents: Santoor - Bhajan Sopori, Pt Tarun Bhattacharya.&nbsp;Sitar - Ustad Vilayat Khan, Pt Ravi Shankar, Shujaat Hussain Khan, Shahid Parvez Khan, Anushka Shankar, Nikhil Banerjee, Mustaq Ali Khan, Budhaditya Mukherjee. Shehnai - Daya Shankar,Ali Ahmad Hussain. Sarangi: Abdul Latif Khan, Ramesh Mishra, Sultan Khan, Pt Ram Narayan, Shakoor Khan.</p>",
                    solution_hi: "<p>27.(c) <strong>a-iii, b-iv, c-i, d-ii.</strong> प्रसिद्ध संगीत वाद्ययंत्र और उनके प्रतिपादक: संतूर - भजन सोपोरी, पं. तरूण भट्टाचार्य। सितार - उस्ताद विलायत खान, पं. रविशंकर, शुजात हुसैन खान, शाहिद परवेज खान, अनुष्का शंकर, निखिल बनर्जी, मुस्ताक अली खान, बुधादित्य मुखर्जी। शहनाई - दया शंकर, अली अहमद हुसैन। सारंगी: अब्दुल लतीफ खान, रमेश मिश्रा, सुल्तान खान, पं. राम नारायण, शकूर खान।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "28",
                    section: "18",
                    question_en: "<p>28. Which of the following groups represents essential amino acids ?</p>",
                    question_hi: "<p>28. निम्नलिखित में से कौन-सा समूह आवश्यक अमीनो एसिड को निरूपित करता है?</p>",
                    options_en: [
                        "<p>Histidine, isoleucine, leucine, lysine</p>",
                        "<p>Alanine, arginine, asparagine, aspartic acid</p>",
                        "<p>Alanine, isoleucine, leucine, lysine</p>",
                        "<p>Glycine, proline, serine, and tyrosine</p>"
                    ],
                    options_hi: [
                        "<p>हिस्टिडीन, आइसोल्यूसीन, ल्यूसीन, लाइसिन (Histidine, isoleucine, leucine, lysine)</p>",
                        "<p>ऐलेनिन, आर्जिनिन, एसपैराजीन, एसपारटिक अम्ल (Alanine, arginine, asparagine, aspartic acid)</p>",
                        "<p>ऐलेनिन, आइसोल्यूसीन, ल्यूसीन, लाइसिन (Alanine, isoleucine, leucine, lysine)</p>",
                        "<p>ग्लाइसिन, प्रोलाइन, सेरीन और टायरोसिन (Glycine, proline, serine, and tyrosine)</p>"
                    ],
                    solution_en: "<p>28.(a) <strong>Histidine, isoleucine, leucine, lysine</strong>. Amino acids are molecules that join together to form proteins, which are the building blocks of life. When proteins are digested or broken down, they yield amino acids. Histidine, isoleucine, leucine, lysine, methionine, phenylalanine, threonine, tryptophan, and valine are classified as essential amino acids, as the body cannot produce them. Therefore, they must be obtained from food.</p>",
                    solution_hi: "<p>28.(a) <strong>हिस्टिडीन, आइसोल्यूसीन, ल्यूसीन, लाइसिन</strong> (Histidine, isoleucine, leucine, lysine)। अमीनो एसिड अणु होते हैं जो आपस में मिलकर प्रोटीन बनाते हैं, यह जीवन के निर्माण खंड हैं। जब प्रोटीन पचता हैं या टूटता हैं, तो यह अमीनो एसिड का निर्माण करते हैं। हिस्टिडीन, आइसोल्यूसीन, ल्यूसीन, लाइसिन, मेथियोनीन, फेनिलएलनिन, थ्रेओनीन, ट्रिप्टोफैन और वेलिन को आवश्यक अमीनो एसिड के रूप में वर्गीकृत किया जाता है, क्योंकि शरीर इनका उत्पादन नहीं कर सकता है। इसलिए, इन्हें भोजन के माध्यम से प्राप्त किया जाता है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "29",
                    section: "18",
                    question_en: "<p>29. Which of the following is a string instrument commonly featured in the folk music of Gujarat?</p>",
                    question_hi: "<p>29. निम्नलिखित में से कौन-सा एक तार वाला वाद्ययंत्र (string instrument) है जो आम तौर पर गुजरात के लोक संगीत में प्रयोग किया जाता है।</p>",
                    options_en: [
                        "<p>bungal</p>",
                        "<p>turi</p>",
                        "<p>pava</p>",
                        "<p>jantar</p>"
                    ],
                    options_hi: [
                        "<p>बुंगल (bungal)</p>",
                        "<p>तुरी (turi)</p>",
                        "<p>पावा (pava)</p>",
                        "<p>जंतर (jantar)</p>"
                    ],
                    solution_en: "<p>29.(d) <strong>jantar. </strong>Other folk instruments include wind instruments like Turi, Bungal, Pava and string instruments like Ravan Hattho and Ektaro percussion instruments like Manjira and Zanz pot drum are omnipresent in the folk sounds of Gujarat.</p>",
                    solution_hi: "<p>29.(d) <strong>जंतर।</strong> अन्य लोक वाद्ययंत्रों में तूरी, बुंगल, पावा जैसे वायु वाद्ययंत्र और रावण हत्थो तथा एक तारो जैसे तार वाद्ययंत्र शामिल हैं। इसके अलावा मंजीरा और झांज जैसे ताल वाद्ययंत्र गुजरात की लोक ध्वनियों में सर्वव्यापी हैं।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "30",
                    section: "18",
                    question_en: "<p>30. What is the common biological phenomenon by which one organism produces biochemicals that influence the growth, survival, development, and reproduction of other organisms known as?</p>",
                    question_hi: "<p>30. वह सामान्य जैविक परिघटना क्या कहलाती है जिसके द्वारा एक जीव ऐसे जैव रसायन उत्पन्न करता है जो अन्य जीवों की वृद्धि, उत्तरजीविता, विकास और प्रजनन को प्रभावित करते हैं?</p>",
                    options_en: [
                        "<p>Allotropy</p>",
                        "<p>Allopathy</p>",
                        "<p>Allelopathy</p>",
                        "<p>Allelomorph</p>"
                    ],
                    options_hi: [
                        "<p>अपररूपता</p>",
                        "<p>एलोपैथी</p>",
                        "<p>ऐलीलोपैथी</p>",
                        "<p>ऐलीलोमॉर्फ</p>"
                    ],
                    solution_en: "<p>30.(c) <strong>Allelopathy</strong>. Allotropy: Element exists in multiple forms with different properties (e.g., carbon as graphite and diamond). Allopathy: Modern science-based treatment, including medicines and surgeries for diseases. Allelomorphs/Alleles: Genes for contrasting traits.</p>",
                    solution_hi: "<p>30.(c) <strong>ऐलीलोपैथी।</strong> एलोट्रॉपी: तत्व विभिन्न गुणों के साथ कई रूपों में मौजूद होता है (उदाहरण के लिए, ग्रेफाइट और हीरे के रूप में कार्बन)। एलोपैथी: आधुनिक विज्ञान आधारित उपचार, जिसमें बीमारियों के लिए दवाएं और सर्जरी शामिल हैं। एलीलोमोर्फ्स/एलेल्स: विपरीत लक्षणों के लिए जीन।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "31",
                    section: "18",
                    question_en: "<p>31. Which Governor General of India issued the famous Regulation XVII in 1829, which declared the practice of Sati illegal and punishable by the courts?</p>",
                    question_hi: "<p>31. भारत के किस गवर्नर जनरल ने 1829 में प्रसिद्ध विनियमन XVII जारी किया, जिसने सती प्रथा को अवैध और अदालतों द्वारा दंडनीय घोषित किया?</p>",
                    options_en: [
                        "<p>Lord Ellenborough</p>",
                        "<p>Lord William Bentinck</p>",
                        "<p>Lord Napier</p>",
                        "<p>Lord Minto</p>"
                    ],
                    options_hi: [
                        "<p>लॉर्ड एलेनबरो (Lord Ellenborough)</p>",
                        "<p>लॉर्ड विलियम बेंटिक (Lord William Bentinck)</p>",
                        "<p>लॉर्ड नेपियर (Lord Napier)</p>",
                        "<p>लॉर्ड मिंटो (Lord Minto)</p>"
                    ],
                    solution_en: "<p>31.(b) <strong>Lord William Bentinck</strong> (Tenure 1828-1835) was the first governor-general of India. A Regulation for declaring the practice of Sati or of Burning or Burying alive the Widows of Hindus, illegal, and punishable by the Criminal Courts. Enacted - 4 December 1829.</p>",
                    solution_hi: "<p>31.(b) <strong>लॉर्ड विलियम बेंटिक</strong> (कार्यकाल 1828-1835) भारत के पहले गवर्नर-जनरल थे। यह सती प्रथा या हिंदुओं की विधवाओं को जिंदा जलाने या दफनाने को गैरकानूनी और आपराधिक न्यायालयों द्वारा दंडनीय घोषित करने के लिए एक विनियमन था। अधिनियमित - 4 दिसंबर 1829 ।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "32",
                    section: "18",
                    question_en: "<p>32. Sakewa is a religious festival celebrated by the Kirat Khambu Rai community of</p>",
                    question_hi: "<p>32. साकेवा (Sakewa)_______के किरात खंबू राय (Kirat Khambu Rai) समुदाय द्वारा मनाया जाने वाला एक धार्मिक त्योहार है।</p>",
                    options_en: [
                        "<p>Arunachal Pradesh</p>",
                        "<p>Goa</p>",
                        "<p>Sikkim</p>",
                        "<p>Himachal Pradesh</p>"
                    ],
                    options_hi: [
                        "<p>अरुणाचल प्रदेश</p>",
                        "<p>गोवा</p>",
                        "<p>सिक्किम</p>",
                        "<p>हिमाचल प्रदेश</p>"
                    ],
                    solution_en: "<p>32.(c) <strong>Sikkim.</strong> \'Sakewa\' is an annual festival and is celebrated as a tribute to mother nature. The rituals for this particular festival are performed in an open space, which is also known as Bhoomi Puja or Chandi Puja. Other important festivals of Sikkim: Losung, Drupka Tesh, Bumchu, Saga Dawa, Dasin and Phang Lhabsol .</p>",
                    solution_hi: "<p>32.(c) <strong>सिक्किम।</strong> \'साकेवा\' एक वार्षिक त्योहार है और इसे प्रकृति माँ को श्रद्धांजलि के रूप में मनाया जाता है। इस विशेष त्योहार का अनुष्ठान एक खुली जगह में किया जाता हैं, जिसे भूमि पूजा या चंडी पूजा के नाम से भी जाना जाता है। सिक्किम के अन्य महत्त्वपूर्ण त्योहार: लोसूंग, द्रुपका तेशी, बुमचू, सागा दावा, दासिन और फांग ल्हाब्सोल आदि।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "33",
                    section: "18",
                    question_en: "<p>33. Which of the following types of crops are sown after the end of the monsoon season?</p>",
                    question_hi: "<p>33. निम्नलिखित में से किस प्रकार की फसलें मानसून के मौसम के अंत के बाद बोई जाती हैं?</p>",
                    options_en: [
                        "<p>Kharif crops</p>",
                        "<p>Zaid crops</p>",
                        "<p>Rabi crops</p>",
                        "<p>Vital crops</p>"
                    ],
                    options_hi: [
                        "<p>खरीफ की फसलें</p>",
                        "<p>जायद की फसलें</p>",
                        "<p>रबी की फसलें</p>",
                        "<p>महत्वपूर्ण फसलें</p>"
                    ],
                    solution_en: "<p>33.(c) <strong>Rabi crops. </strong>It is an agricultural crop that is sown in the winter and harvested in the spring. Kharif crops are crops that are sown in June or July and harvested in September. Zaid crops are seasonal fruits and vegetables grown in the Indian subcontinent during the summer season, from March to June.</p>",
                    solution_hi: "<p>33.(c) <strong>रबी की फसलें।</strong> यह एक कृषि फसल है जिसे सर्दियों में बोया जाता है और वसंत में काटा जाता है। खरीफ की फसलें ऐसी फसलें हैं जिन्हें जून या जुलाई में बोया जाता है और सितंबर में काटा जाता है। जायद की फसलें भारतीय उपमहाद्वीप में गर्मियों के मौसम में, मार्च से जून तक उगाई जाने वाली मौसमी फल और सब्जियाँ हैं।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "34",
                    section: "18",
                    question_en: "<p>34. Warren Hastings became the Governor-General of Bengal from Governor of Bengal after the passing of which of the following Acts ?</p>",
                    question_hi: "<p>34. निम्नलिखित में से किस अधिनियम के पारित होने के बाद वॉरेन हेस्टिंग्स (Warren Hastings) बंगाल के गवर्नर से बंगाल के गवर्नर-जनरल बन गए थे?</p>",
                    options_en: [
                        "<p>Charter Act, 1813</p>",
                        "<p>Pitt&rsquo;s India Act, 1784</p>",
                        "<p>Indian Council Act of 1861</p>",
                        "<p>Regulating Act, 1773</p>"
                    ],
                    options_hi: [
                        "<p>चार्टर अधिनियम, 1813 (Charter Act, 1813)</p>",
                        "<p>पिट्स इंडिया एक्ट, 1784 (Pitt&rsquo;s India Act, 1784)</p>",
                        "<p>1861 का भारतीय परिषद अधिनियम (Indian Council Act of 1861)</p>",
                        "<p>विनियमन अधिनियम, 1773 (Regulating Act, 1773)</p>"
                    ],
                    solution_en: "<p>34.(d) <strong>Regulating Act</strong>, <strong>1773</strong>. The act was the first step taken by the British government to control and regulate the affairs of East India company in India. Pitt&rsquo;s India Act, 1784 was passed to rectify the defects of the Regulating Act 1773. The Charter Act of 1813 ended the East India Company\'s monopoly over trade in India, except for tea, opium, and trade with China. The Indian Councils Act of 1861 introduced the portfolio system and empowered the Viceroy to issue ordinances.</p>",
                    solution_hi: "<p>34.(d) <strong>विनियमन अधिनियम,1773</strong> । यह अधिनियम भारत में ईस्ट इंडिया कंपनी के मामलों को नियंत्रित और विनियमित करने के लिए ब्रिटिश सरकार द्वारा उठाया गया पहला कदम था। विनियमन अधिनियम 1773 के कमियों को दूर करने के लिए पिट्स इंडिया अधिनियम, 1784 पारित किया गया था। 1813 के चार्टर अधिनियम ने चाय, अफीम और चीन के साथ व्यापार को छोड़कर भारत में व्यापार पर ईस्ट इंडिया कंपनी के एकाधिकार को समाप्त कर दिया। 1861 के भारतीय परिषद अधिनियम ने पोर्टफोलियो प्रणाली की शुरुआत की और वायसराय को अध्यादेश जारी करने का अधिकार दिया।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "35",
                    section: "18",
                    question_en: "<p>35. A standing committee is a committee consisting of members of which of the following houses of Parliament ? <br>1.Rajya Sabha <br>2.Lok Sabha <br>3.Vidhan Sabha</p>",
                    question_hi: "<p>35. एक स्थायी समिति संसद के निम्नलिखित सदनों में से किस सदन के सदस्यों से मिलकर बनी एक समिति है? <br>1. राज्य सभा <br>2. लोक सभा <br>3. विधान सभा</p>",
                    options_en: [
                        "<p>Both Rajya Sabha and Lok Sabha</p>",
                        "<p>Vidhan Sabha</p>",
                        "<p>Rajya Sabha</p>",
                        "<p>Lok Sabha</p>"
                    ],
                    options_hi: [
                        "<p>राज्य सभा और लोक सभा, दोनों</p>",
                        "<p>विधान सभा</p>",
                        "<p>राज्य सभा</p>",
                        "<p>लोक सभा</p>"
                    ],
                    solution_en: "<p>35.(a) <strong>Both Rajya Sabha and Lok Sabha</strong>. Two types of Parliamentary Committees: Standing Committees and Ad Hoc Committees. The Standing Committees are permanent (constituted every year or periodically) and work on a continuous basis. It is classified into six categories - Financial Committees, Departmental Standing Committees, Committees to Enquire, Committees to Scrutinize and Control, Committees Relating to the Day-to-Day Business of the House, House-Keeping Committees or Service Committees. The Ad Hoc Committees are temporary and cease to exist on completion of the task assigned to them.</p>",
                    solution_hi: "<p>35.(a) ​​<strong>राज्यसभा और लोकसभा दोनों।</strong> संसदीय समितियाँ दो प्रकार की होती हैं: स्थायी समितियाँ और तदर्थ समितियाँ। स्थायी समितियाँ (प्रत्येक वर्ष या समय-समय पर गठित होती हैं) स्थायी होती हैं और निरंतर आधार पर काम करती हैं। इसे छह श्रेणियों में वर्गीकृत किया गया है - वित्तीय समितियाँ, विभागीय स्थायी समितियाँ, जाँच करने वाली समितियाँ, जाँच और नियंत्रण करने वाली समितियाँ, सदन के दिन-प्रतिदिन के व्यवसाय से संबंधित समितियाँ, हाउस-कीपिंग समितियाँ या सेवा समितियाँ। तदर्थ समितियाँ अस्थायी होती हैं और उन्हें सौंपा गया कार्य पूरा होने पर उनका अस्तित्व समाप्त हो जाता है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "36",
                    section: "18",
                    question_en: "<p>36. Which of the following statements are true about the positive impact of the Green Revolution (GR) on yield levels in India?<br>i. During the post GR period, the yield of rice grew at a much faster rate than that of wheat.<br>ii. GR technology had penetrated more in wheat crop than in the rice crop.<br>iii. During post GR period, the area under rice achieved a relatively slow growth when compared to the area under wheat.</p>",
                    question_hi: "<p>36. भारत में उपज के स्तर पर हरित क्रांति (GR) के सकारात्मक प्रभाव के संबंध में निम्नलिखित में से कौन-सा कथन सही है?<br>i. हरित क्रांति के बाद, चावल की उपज गेहूं की उपज की तुलना में बहुत तेज दर से बढ़ी।<br>ii. हरित क्रांति तकनीक चावल की फसल की तुलना में गेहूं की फसल में अधिक व्याप्त हो चुकी थी।<br>iii. हरित क्रांति के बाद, गेहूं के तहत क्षेत्र की तुलना में चावल के तहत क्षेत्र ने अपेक्षाकृत धीमी वृद्धि हासिल की।</p>",
                    options_en: [
                        "<p>Only ii and iii</p>",
                        "<p>Only i and iii</p>",
                        "<p>Only i and ii</p>",
                        "<p>i, ii and iii</p>"
                    ],
                    options_hi: [
                        "<p>केवल ii और iii</p>",
                        "<p>केवल i और iii</p>",
                        "<p>केवल i और ii</p>",
                        "<p>i, ii और iii</p>"
                    ],
                    solution_en: "<p>36.(a) <strong>Only ii and iii. </strong>The Green Revolution technology had a greater impact on wheat crops than on rice crops. Important Crops in the Revolution: Main crops were - Wheat, Rice, Jowar, Bajra and Maize. Non-food grains were excluded from the ambit of the new strategy. Wheat remained the mainstay of the Green Revolution for years. Mankombu Sambasivan Swaminathan is known as the \"Father of India\'s Green Revolution\" for his work to increase food production and alleviate world hunger.</p>",
                    solution_hi: "<p>36.(a) <strong>केवल ii और iii. </strong>हरित क्रांति तकनीक का चावल की फसलों की तुलना में गेहूं की फसलों पर अधिक प्रभाव पड़ा। क्रांति में महत्वपूर्ण फसलें: मुख्य फसलें थीं - गेहूं, चावल, ज्वार, बाजरा और मक्का। गैर-खाद्यान्नों को नई रणनीति के दायरे से बाहर रखा गया। गेहूं कई वर्षों तक हरित क्रांति का मुख्य आधार बना रहा। मनकोम्बु सम्बाशिवन स्वामीनाथन को खाद्य उत्पादन बढ़ाने और विश्व में भूखमरी को कम करने के लिए किए गए उनके कार्य के लिए \"भारत की हरित क्रांति के जनक\" के रूप में जाना जाता है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "37",
                    section: "18",
                    question_en: "37. Complete the statement given below with respect to the judge\'s removal.<br />The Indian Constitution :-",
                    question_hi: "37. न्यायाधीश को हटाए जाने के संबंध में नीचे दिए गए कथन को पूरा कीजिए।<br />भारतीय संविधान :-",
                    options_en: [
                        " prescribes a procedure for removal of high court judges only.",
                        " prescribes a very difficult procedure for removal of judges.",
                        " does not prescribe a procedure for removal of judges.",
                        " prescribes a very flexible procedure for removal of judges."
                    ],
                    options_hi: [
                        " यह केवल उच्च न्यायालय के न्यायाधीशों को हटाने की प्रक्रिया निर्धारित करता है।",
                        " न्यायाधीशों को हटाने के लिए बहुत कठिन प्रक्रिया निर्धारित करता है।",
                        " न्यायाधीशों को हटाने के लिए कोई प्रक्रिया निर्धारित नहीं करता है। ",
                        " न्यायाधीशों को हटाने के लिए बहुत लचीली प्रक्रिया निर्धारित करता है।"
                    ],
                    solution_en: "37.(b) prescribes a very difficult procedure for removal of judges. A judge of the Supreme Court  or High Court can be removed only on the ground of proved misbehaviour or incapacity. A motion containing the charges against the judge must be approved by a special majority in both Houses of Parliament. Under Article 124(4) of the Constitution, a judge of the Supreme Court, and under Article 217, a judge of the High Court, can be removed from office by an order of the President. ",
                    solution_hi: "37.(b) न्यायाधीशों को हटाने के लिए बहुत कठिन प्रक्रिया निर्धारित करता है। सर्वोच्च न्यायालय या उच्च न्यायालय के न्यायाधीश को केवल प्रमाणित दुर्व्यवहार या अक्षमता के आधार पर ही हटाया जा सकता है। न्यायाधीश के खिलाफ आरोपों वाले प्रस्ताव को संसद के दोनों सदनों में विशेष बहुमत से अनुमोदित किया जाना चाहिए। संविधान के अनुच्छेद 124(4) के तहत सर्वोच्च न्यायालय के न्यायाधीश और अनुच्छेद 217 के तहत उच्च न्यायालय के न्यायाधीश को राष्ट्रपति के आदेश से पद से हटाया जा सकता है। ",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "38",
                    section: "18",
                    question_en: "<p>38. Which of the following is the outcome of globalization of a country&rsquo;s economy?</p>",
                    question_hi: "<p>38. निम्नलिखित में से कौन-सा किसी देश की अर्थव्यवस्था के वैश्वीकरण का परिणाम है?</p>",
                    options_en: [
                        "<p>Outsourcing</p>",
                        "<p>Monetary policy reforms</p>",
                        "<p>Dis-investment</p>",
                        "<p>Fiscal policy reforms</p>"
                    ],
                    options_hi: [
                        "<p>आउटसोर्सिंग</p>",
                        "<p>मौद्रिक नीति सुधार</p>",
                        "<p>विनिवेश</p>",
                        "<p>राजकोषीय नीति में सुधार</p>"
                    ],
                    solution_en: "<p>38.(a) Outsourcing. Globalization is the integration between countries through foreign trade and foreign investments by multinational corporations (MNCs). Peter Sutherland is known as the \'father of globalization\'. Globalization in India (1991) was done by Manmohan Singh under leadership of PV Narasimha Rao. The three main pillars of Reform were: Liberalization, Globalization, and Privatization.</p>",
                    solution_hi: "<p>38.(a) <strong>आउटसोर्सिंग</strong>। वैश्वीकरण बहुराष्ट्रीय निगमों (MNC) द्वारा विदेशी व्यापार और विदेशी निवेश के माध्यम से देशों के बीच एकीकरण है। पीटर सदरलैंड को \'वैश्वीकरण के जनक\' के रूप में जाना जाता है। भारत में वैश्वीकरण (1991) पी वी नरसिम्हा राव के नेतृत्व में मनमोहन सिंह द्वारा किया गया था। सुधार के तीन मुख्य स्तंभ थे: उदारीकरण, वैश्वीकरण और निजीकरण।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "39",
                    section: "18",
                    question_en: "<p>39. What does &lsquo;F&rsquo; stand for in FII?</p>",
                    question_hi: "<p>39. FII में \'F\' का तात्पर्य किससे है?</p>",
                    options_en: [
                        "<p>Fiduciary</p>",
                        "<p>Foreign</p>",
                        "<p>Fund</p>",
                        "<p>Fast</p>"
                    ],
                    options_hi: [
                        "<p>Fiduciary (फडूशीएरी)</p>",
                        "<p>Foreign (फॉरेन)</p>",
                        "<p>Fund (फंड)</p>",
                        "<p>Fast (फास्ट)</p>"
                    ],
                    solution_en: "<p>39.(b) <strong>Foreign. </strong>FII stands for Foreign Institutional Investor, is an investor or investment fund investing in a country outside of the one in which it is registered or headquartered. Foreign direct investment (FDI) is an ownership stake in a foreign company or project made by an investor, company, or government from another country.</p>",
                    solution_hi: "<p>39.(b)<strong> Foreign </strong>(फॉरेन)। FII का मतलब विदेशी संस्थागत निवेशक है, यह एक निवेशक या निवेश कोष है जो उस देश के बाहर किसी देश में निवेश करता है जहां वह पंजीकृत या मुख्यालय है। प्रत्यक्ष विदेशी निवेश (FDI) किसी विदेशी कंपनी या किसी अन्य देश के निवेशक, कंपनी या सरकार द्वारा बनाई गई परियोजना में स्वामित्व हिस्सेदारी है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "40",
                    section: "18",
                    question_en: "<p>40. Bharat Muni, in his Natya Shastra, categorises musical instruments into four different&nbsp;parts. What is the categorisation of musical instruments by Bharat Muni based on?</p>",
                    question_hi: "<p>40. भरत मुनि ने अपने नाट्य शास्त्र में संगीत-वाद्ययंत्रों को चार अलग-अलग भागों में वर्गीकृत किया है। भरत मुनि ने इन वाद्ययंत्रों का वर्गीकरण किस आधार पर किया है?</p>",
                    options_en: [
                        "<p>Different sounds of the musical instruments</p>",
                        "<p>The shape of the musical instruments</p>",
                        "<p>Different materials used in the musical instruments</p>",
                        "<p>The uses of the musical instruments in different occasions, such as wedding, festivals and public gathering</p>"
                    ],
                    options_hi: [
                        "<p>वाद्ययंत्रों की विभिन्न ध्वनियों</p>",
                        "<p>वाद्ययंत्रों की आकृति</p>",
                        "<p>संगीत-वाद्ययंत्रों में प्रयुक्त विभिन्न सामग्रियों</p>",
                        "<p>विभिन्न अवसरों जैसे शादी, त्यौहार और सार्वजनिक समारोहों में संगीत वाद्ययंत्रों के उपयोग</p>"
                    ],
                    solution_en: "<p>40.(a) <strong>Different sounds of the musical instruments.</strong> Musical Instruments were categorised into four groups in Bharat Muni\'s Natya Shastra (written between 200 BC and 200 AD): Avanaddha Vadya (membranophones or percussion instruments), Ghan Vadya (idiophones or solid instruments), Sushir Vadya (aerophones or wind instruments), and Tat Vadya (chordophones or stringed instruments).</p>",
                    solution_hi: "<p>40.(a) <strong>वाद्ययंत्रों की विभिन्न ध्वनियों। </strong>भरत मुनि के नाट्य शास्त्र (200 ईसा पूर्व और 200 ईस्वी के बीच लिखे गए) में संगीत वाद्ययंत्रों को चार समूहों में वर्गीकृत किया गया था: अवनद्ध वाद्य (मेम्ब्रेनोफ़ोन या ताल वाद्य यंत्र), घन वाद्य (इडियोफोन या ठोस वाद्य यंत्र), सुषिर वाद्य (एरोफोन या वायु वाद्य यंत्र) और तत वाद्य (कॉर्डोफ़ोन या तार वाद्य यंत्र)।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "41",
                    section: "18",
                    question_en: "<p>41. Which of the following is the correct formula for calculating Body Mass Index (BMI)?</p>",
                    question_hi: "<p>41. शरीर द्रव्यमान सूचकांक (बॉडी मास इंडेक्स) (BMI) की गणना के लिए निम्नलिखित में से कौन-सा सूत्र सही है?</p>",
                    options_en: [
                        "<p>BMI = gm/cm&sup2;</p>",
                        "<p>BMI = kg &times; m&sup2;</p>",
                        "<p>BMI = kg/m&sup2;</p>",
                        "<p>BMI = kg &times; cm&sup2;</p>"
                    ],
                    options_hi: [
                        "<p>BMI = gm/cm&sup2;</p>",
                        "<p>BMI = kg &times; m&sup2;</p>",
                        "<p>BMI = kg/m&sup2;</p>",
                        "<p>BMI = kg &times; cm&sup2;</p>"
                    ],
                    solution_en: "<p>41.(c) <strong>BMI = kg/m&sup2;. </strong>Body mass index (BMI) is an estimate of an individual&rsquo;s relative body fat. It is calculated by measuring his/her height and weight using the formula; body weight in kgs/height in metre square (m)<sup>2</sup>. BMI categories are : Underweight (&lt;18.5), Normal (18.6 - 23), Overweight (23.1 - 30), and Obese (&gt;30).</p>",
                    solution_hi: "<p>41.(c)<strong> BMI = kg/m&sup2;.</strong> शरीर द्रव्यमान सूचकांक (Body mass index) किसी व्यक्ति के सापेक्ष शरीर में वसा का अनुमान है। इसकी गणना उसकी ऊंचाई और वजन को मापकर निम्न सूत्र द्वारा की जाती है; शरीर का वजन (kgs)/ ऊंचाई का वर्ग (m&sup2;) । BMI श्रेणियां : कम वजन (&lt;18.5), सामान्य (18.6 - 23), अधिक वजन (23.1 - 30), और मोटापा (&gt;30)।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "42",
                    section: "18",
                    question_en: "<p>42. Which of the following is NOT related to the &lsquo;Trinity of Carnatic Music&rsquo;?</p>",
                    question_hi: "<p>42. निम्नलिखित में से कौन, \'कर्नाटिक संगीत की त्रिमूर्ति\' से संबंधित नहीं है?</p>",
                    options_en: [
                        "<p>Purandara Das</p>",
                        "<p>Syama Shastri</p>",
                        "<p>Muthuswami Dikshitar</p>",
                        "<p>Tyagaraja</p>"
                    ],
                    options_hi: [
                        "<p>पुरंदरदास</p>",
                        "<p>श्यामा शास्त्री</p>",
                        "<p>मुथुस्वामी दीक्षितार</p>",
                        "<p>त्यागराज</p>"
                    ],
                    solution_en: "<p>42.(a) <strong>Purandara Das</strong> (&lsquo;&rsquo;father of Carnatic music&rsquo;&rsquo;) was a saint from Karnataka who contributed to the evolution of Carnatic music in the 18th century. The Trinity of Carnatic Music, also known as the Three Jewels of Carnatic Music, refers to the outstanding trio of composer-musicians of Carnatic music in the 18th century - Tyagaraja, Muthuswami Dikshitar, and Syama Sastri.</p>",
                    solution_hi: "<p>42.(a) <strong>पुरंदर दास</strong> (\'\'कर्नाटक संगीत के जनक\'\') कर्नाटक के एक संत थे जिन्होंने 18वीं शताब्दी में कर्नाटक संगीत के विकास में योगदान दिया। कर्नाटक संगीत की त्रिमूर्ति, जिसे कर्नाटक संगीत के तीन रत्नों के रूप में भी जाना जाता है, 18वीं शताब्दी में कर्नाटक संगीत के संगीतकार-संगीतकारों की उत्कृष्ट तिकड़ी को संदर्भित करता है - त्यागराज, मुथुस्वामी दीक्षितार और श्यामा शास्त्री।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "43",
                    section: "18",
                    question_en: "<p>43. Which country ranks lowest in the 2024 Corruption Perceptions Index released by Transparency International?</p>",
                    question_hi: "<p>43. ट्रांसपेरेंसी इंटरनेशनल द्वारा जारी 2024 के करप्शन परसेप्शन इंडेक्स में सबसे कम रैंक वाला देश कौन सा है?</p>",
                    options_en: [
                        "<p>Syria</p>",
                        "<p>Venezuela</p>",
                        "<p>South Sudan</p>",
                        "<p>Somalia</p>"
                    ],
                    options_hi: [
                        "<p>सीरिया</p>",
                        "<p>वेनज़ुएला</p>",
                        "<p>दक्षिण सूडान</p>",
                        "<p>सोमालिया</p>"
                    ],
                    solution_en: "<p>43.(c) (South Sudan)</p>",
                    solution_hi: "<p>43. (c) दक्षिण सूडान (South Sudan)</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "44",
                    section: "18",
                    question_en: "<p>44. With reference to Saila dance consider the following statements and select the correct option.<br>a) A group comprising of male performers.<br>b) The dance is performed with bamboo sticks.<br>c) It is performed after the cutting of crops.</p>",
                    question_hi: "<p>44. सैला नृत्य के संदर्भ में निम्नलिखित कथनों पर विचार कीजिए और सही विकल्प चुनिए ।<br>a) पुरुष कलाकारों का एक समूह होता है ।<br>b) बांस की डंडियों के साथ यह नृत्य किया जाता है।<br>c) यह फसल कटाई के बाद किया जाता है।</p>",
                    options_en: [
                        "<p>a, b, c</p>",
                        "<p>Both b and c</p>",
                        "<p>Both a and c</p>",
                        "<p>Both a and b</p>"
                    ],
                    options_hi: [
                        "<p>a, b, c</p>",
                        "<p>b और c दोनों</p>",
                        "<p>a और c दोनों</p>",
                        "<p>a और b दोनों</p>"
                    ],
                    solution_en: "<p>44.(a)<strong> a, b, c. </strong>Saila dance (Chattisgarh): This dance is performed only by boys after the harvest season in the Hindu month of Aghan (November-December). Other Famous Folk Dance of Chhattisgarh: Sua, Pandavani, Karma, Panthi, Jhirliti.</p>",
                    solution_hi: "<p>44.(a) <strong>a, b, c</strong>. सैला नृत्य (छत्तीसगढ़): यह नृत्य हिंदू महीने अगहन (नवंबर-दिसंबर) में फसल कटाई के बाद केवल लड़कों द्वारा किया जाता है। छत्तीसगढ़ के अन्य प्रसिद्ध लोक नृत्य: सुआ, पंडवानी, करमा, पंथी, झिरलिटि आदि।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "45",
                    section: "18",
                    question_en: "<p>45. The phenomenon of change of liquid into vapours at any temperature below its boiling point is called:</p>",
                    question_hi: "<p>45. क्वथनांक से नीचे किसी भी तापमान पर, द्रव के वाष्प में परिवर्तित होने की घटना को निम्नलिखित में से क्या कहा जाता है?</p>",
                    options_en: [
                        "<p>Melting</p>",
                        "<p>Fusion</p>",
                        "<p>Sublimation</p>",
                        "<p>Evaporation</p>"
                    ],
                    options_hi: [
                        "<p>गलन</p>",
                        "<p>संलयन</p>",
                        "<p>ऊर्ध्वपातन</p>",
                        "<p>वाष्पीकरण</p>"
                    ],
                    solution_en: "<p>45.(d) <strong>Evaporation. </strong>The reverse action of evaporation, where water vapor becomes liquid, is known as Condensation. Fusion or Melting - It is the process of converting a solid into its liquid state on heating. While solidification is the reverse of fusion. Sublimation - It is the process of changing from a solid to a gas without passing through an intermediate liquid phase. The reverse process of sublimation is called Deposition, where a substance in gas form changes its state to become a solid.</p>",
                    solution_hi: "<p>45.(d) <strong>वाष्पीकरण I </strong>वाष्पीकरण की विपरीत क्रिया, जहां जल वाष्प तरल हो जाता है, संघनन के रूप में जाना जाता है। संलयन या पिघलना - यह गर्म करने पर किसी ठोस को तरल अवस्था में परिवर्तित करने की प्रक्रिया है। जबकि जमना संलयन के विपरीत है। उर्ध्वपातन (Sublimation) - यह किसी मध्यवर्ती तरल चरण से गुजरे बिना ठोस से गैस में बदलने की प्रक्रिया है। उर्ध्वपातन की विपरीत प्रक्रिया को निक्षेपण (Deposition) कहा जाता है, जहां गैस के रूप में कोई पदार्थ ठोस बनने के लिए अपनी अवस्था बदलता है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "46",
                    section: "18",
                    question_en: "<p>46. Who launched the book &ldquo;The World After Gaza&rdquo; in January 2025?</p>",
                    question_hi: "<p>46. जनवरी 2025 में &ldquo;द वर्ल्ड आफ्टर गाजा&rdquo; नामक पुस्तक का विमोचन किसने किया?</p>",
                    options_en: [
                        "<p>Arundhati Roy</p>",
                        "<p>Salman Rushdie</p>",
                        "<p>Pankaj Mishra</p>",
                        "<p>Vikram Seth</p>"
                    ],
                    options_hi: [
                        "<p>अरुंधति रॉय</p>",
                        "<p>सलमान रुश्दी</p>",
                        "<p>पंकज मिश्रा</p>",
                        "<p>विक्रम सेठ</p>"
                    ],
                    solution_en: "<p>46.(c) <strong>Pankaj Mishra.</strong> This book is published by Juggernaut Books; this treatise delves into the atrocities and systematic injustices of the war, presenting a stark reflection on global moral decay. His Other Books include &ldquo;Age of Anger&rdquo;, &ldquo;Run and Hide&rdquo;, and &ldquo;An End to Suffering&rdquo;.</p>",
                    solution_hi: "<p>46.(c) <strong>पंकज मिश्रा। </strong>यह पुस्तक जगरनॉट बुक्स द्वारा प्रकाशित की गई है; यह ग्रंथ युद्ध के अत्याचारों और व्यवस्थित अन्याय पर प्रकाश डालता है, तथा वैश्विक नैतिक पतन पर एक कठोर प्रतिबिंब प्रस्तुत करता है। इनकी अन्य पुस्तकों में &ldquo;एज ऑफ एंगर&rdquo;, &ldquo;रन एंड हाइड&rdquo; और &ldquo;एन एंड टू सफ़रिंग&rdquo; शामिल हैं।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "47",
                    section: "18",
                    question_en: "<p>47. What is known to be &lsquo;ekaharya&rsquo;, where one dancer takes on many roles in a single performance?</p>",
                    question_hi: "<p>47. \'एकहर्य\' किसे कहा जाता है, जहाँ एक नर्तक एक ही प्रदर्शन में कई भूमिकाएँ निभाता है?</p>",
                    options_en: [
                        "<p>Bharatanatyam</p>",
                        "<p>Kathak</p>",
                        "<p>Kuchipudi</p>",
                        "<p>Kathakali</p>"
                    ],
                    options_hi: [
                        "<p>भरतनाट्यम</p>",
                        "<p>कथक</p>",
                        "<p>कुचिपुड़ी</p>",
                        "<p>कथकली</p>"
                    ],
                    solution_en: "<p>47.(a) <strong>Bharatanatyam:</strong> Features - The dance involves transitional movements of the leg, hip and arm. Expressive eye movements and hand gestures are used to convey emotions. It encompasses Bhav, Rag, Ras and Taal. The person who conducts the dance recitation is the Nattuvanar. Exponents - Rukmini Devi Arundale, Alarmel Valli, C.V. Chandrasekhar.</p>",
                    solution_hi: "<p>47.(a) <strong>भरतनाट्यम:</strong> विशेषताएँ - इस नृत्य में पैर, कूल्हे और हाथ की संक्रमणकालीन गतिविधियाँ शामिल हैं। भावों को व्यक्त करने के लिए अभिव्यंजक नेत्र गति और हाथ के इशारों का उपयोग किया जाता है। इसमें भाव, राग, रास और ताल शामिल हैं। नृत्य गायन का संचालन करने वाला व्यक्ति नट्टुवनार होता है। प्रतिपादक - रुक्मिणी देवी अरुंडेल, अलरमेल वल्ली, सी.वी. चंद्रशेखर।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "48",
                    section: "18",
                    question_en: "<p>48. Who flagged off the &lsquo;SANJAY - The Battlefield Surveillance System&rsquo; from New Delhi in January 2025?</p>",
                    question_hi: "<p>48. जनवरी 2025 में नई दिल्ली से &lsquo;संजय - युद्धक्षेत्र निगरानी प्रणाली&rsquo; को किसने हरी झंडी दिखाई?</p>",
                    options_en: [
                        "<p>Narendra Modi</p>",
                        "<p>Rajnath Singh</p>",
                        "<p>Amit Shah</p>",
                        "<p>General Anil Chauhan</p>"
                    ],
                    options_hi: [
                        "<p>नरेंद्र मोदी</p>",
                        "<p>राजनाथ सिंह</p>",
                        "<p>अमित शाह</p>",
                        "<p>जनरल अनिल चौहान</p>"
                    ],
                    solution_en: "<p>48.(b) Rajnath Singh. SANJAY has been indigenously &amp; jointly developed by the Indian Army and Bharat Electronics Limited (BEL) creating a conducive ecosystem towards achieving &lsquo;Aatmanirbharta&rsquo; as a follow up to the Indian Army&rsquo;s &lsquo;Year of Technology Absorption&rsquo;. These systems will be inducted to all operational Brigades, Divisions &amp; Corps of the Indian Army in three phases w.e.f. March to October of 2025, which has been declared as &lsquo;Year of Reforms&rsquo; in the Ministry of Defence (MoD). This system has been developed under the Buy (Indian) category at a cost of Rs 2,402 crore.</p>",
                    solution_hi: "<p>48.(b) राजनाथ सिंह। संजय को भारतीय सेना और भारत इलेक्ट्रॉनिक्स लिमिटेड (BEL) द्वारा स्वदेशी रूप से और संयुक्त रूप से विकसित किया गया है, जो भारतीय सेना के &lsquo;प्रौद्योगिकी अवशोषण वर्ष&rsquo; के अनुवर्ती के रूप में &lsquo;आत्मनिर्भरता&rsquo; प्राप्त करने की दिशा में एक अनुकूल पारिस्थितिकी तंत्र बना रहा है। इन प्रणालियों को भारतीय सेना के सभी परिचालन ब्रिगेड, डिवीजनों और कोर में तीन चरणों में शामिल किया जाएगा। मार्च से अक्टूबर 2025 तक, जिसे रक्षा मंत्रालय (MoD) में &lsquo;सुधारों का वर्ष&rsquo; घोषित किया गया है। इस प्रणाली को 2,402 करोड़ रुपये की लागत से खरीदें (भारतीय) श्रेणी के तहत विकसित किया गया है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "49",
                    section: "18",
                    question_en: "<p>49. The Chau dancers of ___________ organise a festival named Chau-Jhumur Utsav, where an open stage is designed for the performance by Chau groups.</p>",
                    question_hi: "<p>49. _____________ के छउ नृत्य कलाकार, चौ-झुमुर उत्सव (Chau-Jhumur Utsav) नामक एक उत्सव का आयोजन करते हैं, जिसमें छउ समूहों के प्रदर्शन के लिए एक खुला मंच तैयार किया जाता है।</p>",
                    options_en: [
                        "<p>Jharkhand</p>",
                        "<p>West Bengal</p>",
                        "<p>Madhya Pradesh</p>",
                        "<p>Chhattisgarh</p>"
                    ],
                    options_hi: [
                        "<p>झारखंड</p>",
                        "<p>पश्चिम बंगाल</p>",
                        "<p>मध्यप्रदेश</p>",
                        "<p>छत्तीसगढ़</p>"
                    ],
                    solution_en: "<p>49.(b) <strong>West Bengal.</strong> Chhau is performed in three styles across eastern India: Purulia Chhau - West Bengal. Seraikela Chhau - Jharkhand. Mayurbhanj Chhau - Odisha. Folk dance of other states: Jharkhand - Karma, Santhali, Mundari, etc. Madhya Pradesh - Gaur Dance, Tertali, Grida Dance, etc. Chhattisgarh - Panthi, Raut Nacha, Karma, Saila, etc.</p>",
                    solution_hi: "<p>49.(b) <strong>पश्चिम बंगाल।</strong> छऊ पूर्वी भारत में तीन शैलियों में प्रस्तुत किया जाता है: पुरुलिया छऊ - पश्चिम बंगाल। सरायकेला छऊ - झारखंड। मयूरभंज छऊ - ओडिशा। अन्य राज्यों के लोक नृत्य: झारखंड - करमा, संथाली, मुंडारी आदि। मध्य प्रदेश - गौर नृत्य, तरताली, ग्रिडा नृत्य आदि। छत्तीसगढ़ - पंथी, राऊत नाचा, करमा, सैला आदि।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "50",
                    section: "17",
                    question_en: "<p>50. ________ delegates from all over the country congregated for the first session of the Indian National Congress at Bombay in December 1885.</p>",
                    question_hi: "<p>50. दिसम्बर 1885 में बंबई में भारतीय राष्ट्रीय कांग्रेस के पहले अधिवेशन के लिए देश भर से ________ प्रतिनिधि एकत्रित हुए थें।</p>",
                    options_en: [
                        "<p>105</p>",
                        "<p>72</p>",
                        "<p>51</p>",
                        "<p>179</p>"
                    ],
                    options_hi: [
                        "<p>105</p>",
                        "<p>72</p>",
                        "<p>51</p>",
                        "<p>179</p>"
                    ],
                    solution_en: "<p>50.(b) <strong>72. </strong>First Session of the Congress was held on 28 December 1885, at Gokuldas Tejpal Sanskrit College, Bombay. President - Womesh Chandra Bonnerjee. The Second Session of the Congress took place under the leadership of Dadabhai Naoroji in Calcutta. The number of delegates had increased to 434. 3rd Session (Madras): President - Badruddin Tyabji. 16th Session (Lahore): President - N.G. Chandavarkar.</p>",
                    solution_hi: "<p>50.(b) <strong>72 । </strong>कांग्रेस का पहला सत्र 28 दिसंबर 1885 को गोकुलदास तेजपाल संस्कृत कॉलेज, बॉम्बे में आयोजित किया गया था। अध्यक्ष - व्योमेश चंद्र बनर्जी। कांग्रेस का दूसरा अधिवेशन दादाभाई नौरोजी के नेतृत्व में कलकत्ता में हुआ। प्रतिनिधियों की संख्या बढ़कर 434 हो गई थी। तीसरा सत्र (मद्रास): अध्यक्ष - बदरुद्दीन तैयबजी। 16वाँ सत्र (लाहौर): अध्यक्ष - एन.जी. चंदावरकर ।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "51",
                    section: "17",
                    question_en: "<p>51 A company sells goods at a markup of 25% above the cost price. During a sale, the company offers a 10% discount on the marked price. If the customer pays ₹540 after the discount, then what was the original cost price of the goods?</p>",
                    question_hi: "<p>51. एक कंपनी लागत मूल्य से 25% अधिक मूल्य अंकित करती है।&nbsp; सेल के दौरान, कंपनी अंकित मूल्य पर 10% की छूट देती है। यदि ग्राहक छूट के बाद ₹540 का भुगतान करता है, तो सामान का मूल क्रय मूल्य क्या था?</p>",
                    options_en: [
                        "<p>₹470</p>",
                        "<p>₹460</p>",
                        "<p>₹480</p>",
                        "<p>₹450</p>"
                    ],
                    options_hi: [
                        "<p>₹470</p>",
                        "<p>₹460</p>",
                        "<p>₹480</p>",
                        "<p>₹450</p>"
                    ],
                    solution_en: "<p>51.(c)<br>Let the original cost price of the goods = 100 units<br>MP of the goods = 100 &times; 125% = 125 units<br>SP of the goods = 125 &times; 90%&nbsp;= 112.5 units<br>112.5 units = ₹ 540<br>(original cost price) 100 units = <math display=\"inline\"><mfrac><mrow><mn>540</mn></mrow><mrow><mn>112</mn><mo>.</mo><mn>5</mn></mrow></mfrac></math> &times; 100 = ₹ 480</p>",
                    solution_hi: "<p>51.(c)<br>माना वस्तु का मूल लागत मूल्य = 100 इकाई <br>वस्तु का अंकित मूल्य = 100 &times; 125% = 125 इकाई <br>वस्तु का विक्रय मूल्य = 125 &times; 90%&nbsp;= 112.5 इकाई <br>112.5 इकाई = ₹ 540<br>(वस्तु का वास्तविक क्रय मूल्य) 100 इकाई = <math display=\"inline\"><mfrac><mrow><mn>540</mn></mrow><mrow><mn>112</mn><mo>.</mo><mn>5</mn></mrow></mfrac></math> &times; 100 = ₹ 480</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "52",
                    section: "17",
                    question_en: "<p>52. Three friends M, N and R distributed the amount of ₹ 93,775 in the ratio of 2: 4: 5. How much amount does R have more than M?</p>",
                    question_hi: "<p>52. तीन मित्र M, N और R ने ₹93,775 की राशि 2 : 4 : 5 के अनुपात में बांटी। R के पास M से कितनी अधिक राशि (₹ में) है?</p>",
                    options_en: [
                        "<p>35,705</p>",
                        "<p>25,575</p>",
                        "<p>18,775</p>",
                        "<p>41,560</p>"
                    ],
                    options_hi: [
                        "<p>35,705</p>",
                        "<p>25,575</p>",
                        "<p>18,775</p>",
                        "<p>41,560</p>"
                    ],
                    solution_en: "<p>52.(b)<br>M : N : R = 2 : 4 : 5<br>R - M = 5 - 2 = 3 unit<br>11 unit = 93,775<br>3 unit = <math display=\"inline\"><mfrac><mrow><mn>93775</mn></mrow><mrow><mn>11</mn></mrow></mfrac></math> &times; 3 = 8525 &times; 3 = ₹ 25,575<br>Hence, R has ₹ 25,575 more money than M.</p>",
                    solution_hi: "<p>52.(b)<br>M : N : R = 2 : 4 : 5<br>R - M = 5 - 2 = 3 इकाई<br>11 इकाई = 93,775<br>3 इकाई = <math display=\"inline\"><mfrac><mrow><mn>93775</mn></mrow><mrow><mn>11</mn></mrow></mfrac></math> &times; 3 = 8525 &times; 3 = ₹ 25,575<br>अतः, R के पास M से ₹ ​​25,575 अधिक राशि है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "53",
                    section: "17",
                    question_en: "<p>53. The inner and outer radii of a hemispherical wooden bowl are 6 cm and 8 cm, respectively. Its entire surface has to be polished and the cost of polishing &pi; cm<sup>2</sup> is Rs.50. How much will it cost to polish the bowl?</p>",
                    question_hi: "<p>53. एक अर्धगोलाकार लकड़ी के कटोरे की भीतरी और बाहरी त्रिज्या क्रमशः 6 cm और 8 cm हैं। इसकी&nbsp;पूरी सतह को पॉलिश किया जाना है और &pi; cm<sup>2</sup> को पॉलिश करने की लागत Rs. 50 है। कटोरे को&nbsp;पॉलिश करने में कितना खर्च आएगा?</p>",
                    options_en: [
                        "<p>Rs.11,600</p>",
                        "<p>Rs.11,400</p>",
                        "<p>Rs.10,000</p>",
                        "<p>Rs.12,000</p>"
                    ],
                    options_hi: [
                        "<p>Rs.11,600</p>",
                        "<p>Rs.11,400</p>",
                        "<p>Rs.10,000</p>",
                        "<p>Rs.12,000</p>"
                    ],
                    solution_en: "<p>53.(b)<br>Outer area of hemispherical bowl = 2&pi; (R)<sup>2</sup> = 2&pi; (8)<sup>2</sup> = 128&pi; cm<sup>2</sup><br>Inner area of hemispherical bowl = 2&pi; (r)<sup>2</sup> = 2&pi; (6)<sup>2</sup> = 72&pi; cm<sup>2</sup><br>Ring area of the top = &pi;(R<sup>2</sup> - r<sup>2</sup>) = &pi;(64 - 36) = 28&pi; cm<sup>2</sup><br>Total area to polished = 128&pi; + 72&pi; + 28&pi; = 228&pi; cm<sup>2</sup><br>Hence, cost to polish the bowl = 228&pi; = 228 &times; 50 = Rs. 11400</p>",
                    solution_hi: "<p>53..(b)<br>अर्धगोलाकार कटोरे का बाहरी क्षेत्रफल = 2&pi; (R)<sup>2</sup> = 2&pi; (8)<sup>2</sup> = 128&pi; सेमी<sup>2</sup><br>अर्धगोलाकार कटोरे का आंतरिक क्षेत्रफल = 2&pi; (r)<sup>2</sup> = 2&pi; (6)<sup>2</sup> = 72&pi; सेमी<sup>2</sup><br>शीर्ष वलय का क्षेत्रफल = &pi;(R<sup>2</sup> - r<sup>2</sup>) = &pi;(64 - 36) = 28&pi; सेमी<sup>2</sup><br>पॉलिश किया गया कुल क्षेत्रफल = 128&pi; + 72&pi; + 28&pi; = 228&pi; सेमी<sup>2</sup><br>अत:, कटोरे को पॉलिश करने की लागत = 228&pi; = 228 &times; 50 = Rs. 11400</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "54",
                    section: "17",
                    question_en: "<p>54. Find the total amount of debt that will be discharged by 5 equal installments of ₹200 each, if the debt is due in 5 years at 5% p.a. at simple interest.</p>",
                    question_hi: "<p>54. ऋण की वह राशि ज्ञात कीजिए जो प्रत्येक ₹200 की 5 बराबर किस्&zwj;तों में चुकाई जाएगी, यदि ऋण 5% वार्षिक साधारण ब्याज की दर से 5 वर्षों में देय है।</p>",
                    options_en: [
                        "<p>₹1,100</p>",
                        "<p>₹1,200</p>",
                        "<p>₹1,255</p>",
                        "<p>₹1,400</p>"
                    ],
                    options_hi: [
                        "<p>₹1,100</p>",
                        "<p>₹1,200</p>",
                        "<p>₹1,255</p>",
                        "<p>₹1,400</p>"
                    ],
                    solution_en: "<p>54.(a)<br>Rate = 5%<br>Let the installment = 100 units<br>Ratio - installment : amount<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;100&nbsp; &nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; 100<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;100&nbsp; &nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; 105<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;100&nbsp; &nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; 110<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;100&nbsp; &nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; 115<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;100&nbsp; &nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; 120<br>Total amount = 100 + 105 + 110 + 115 + 120 = 550 units<br>100 units = 200<br>550 units = ₹ 1100</p>",
                    solution_hi: "<p>54.(a)<br>दर = 5%<br>माना किस्त = 100 इकाई<br>अनुपात -&nbsp; &nbsp; किस्त&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; राशि <br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;100&nbsp; &nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; 100<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;100&nbsp; &nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; 105<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;100&nbsp; &nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; 110<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;100&nbsp; &nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; 115<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;100&nbsp; &nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; 120<br>कुल राशि = 100 + 105 + 110 + 115 + 120 = 550 इकाई<br>100 इकाई = 200<br>550 इकाई = ₹ 1100</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "55",
                    section: "17",
                    question_en: "<p>55. The probabilities of solving a problem by three students A, B and C are<math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math>, <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>9</mn></mfrac></math> and <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>5</mn></mfrac></math>, respectively. The probability that problem will be solved is:</p>",
                    question_hi: "<p>55. तीन छात्रों A, B और C द्वारा एक समस्या को हल करने की प्रायिकता क्रमशः <math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math>, <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>9</mn></mfrac></math> और <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>5</mn></mfrac></math>है।समस्या के हल होने की प्रायिकता क्या होगी ?</p>",
                    options_en: [
                        "<p><math display=\"inline\"><mfrac><mrow><mn>155</mn></mrow><mrow><mn>315</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>64</mn></mrow><mrow><mn>315</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>251</mn></mrow><mrow><mn>315</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>32</mn></mrow><mrow><mn>315</mn></mrow></mfrac></math></p>"
                    ],
                    options_hi: [
                        "<p><math display=\"inline\"><mfrac><mrow><mn>155</mn></mrow><mrow><mn>315</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>64</mn></mrow><mrow><mn>315</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>251</mn></mrow><mrow><mn>315</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>32</mn></mrow><mrow><mn>315</mn></mrow></mfrac></math></p>"
                    ],
                    solution_en: "<p>55.(c) <br>Probability of A solved the problem P(A)&nbsp;<br>= <math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> &rArr; P(<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mover><mi>A</mi><mo>&#175;</mo></mover></math>) = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>7</mn></mfrac></math><br>Probability of B solved the problem P(B)&nbsp;<br>= <math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>9</mn></mrow></mfrac></math> &rArr; P(<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mover><mi>B</mi><mo>&#175;</mo></mover></math>) = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>9</mn></mfrac></math><br>Probability of C solved the problem P(C)&nbsp;<br>= <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math>&rArr; P(<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mover><mi>C</mi><mo>&#175;</mo></mover></math>) = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>5</mn></mfrac></math><br>Probability that no one can solved the problem <br>= <math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> &times; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>9</mn></mfrac></math> &times; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>5</mn></mfrac></math> = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>64</mn><mn>315</mn></mfrac></math><br>Required Probability = 1 -<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>64</mn><mn>315</mn></mfrac></math> = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>251</mn><mn>315</mn></mfrac></math></p>",
                    solution_hi: "<p>55.(c) <br>A के समस्या हल करने प्रायिकता P(A)&nbsp;<br>= <math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> &rArr; P(<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mover><mi>A</mi><mo>&#175;</mo></mover></math>) = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>7</mn></mfrac></math><br>B के समस्या हल करने प्रायिकता P(B)&nbsp;<br>= <math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>9</mn></mrow></mfrac></math> &rArr; P(<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mover><mi>B</mi><mo>&#175;</mo></mover></math>) = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>9</mn></mfrac></math><br>C के समस्या हल करने प्रायिकता P(C)&nbsp;<br>= <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math>&rArr; P(<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mover><mi>C</mi><mo>&#175;</mo></mover></math>) = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>5</mn></mfrac></math><br>किसी के द्वारा समस्या हल नहीं होने की प्रायिकता <br>= <math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> &times; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>9</mn></mfrac></math> &times; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>5</mn></mfrac></math> = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>64</mn><mn>315</mn></mfrac></math><br>आवश्यक प्रायिकता = 1 - <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>64</mn><mn>315</mn></mfrac></math> = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>251</mn><mn>315</mn></mfrac></math></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "56",
                    section: "17",
                    question_en: "<p>56. In a 100-m race. A beats B by 20 m and B beats C by 5 m. In the same race, find the distance by which A beats C.</p>",
                    question_hi: "<p>56. 100 m की एक दौड़ में, A, B को 20 m से और B, C को 5 m से हराता है। उसी दौड़ में, वह दूरी ज्ञात कीजिए जिससे A, C को हराता है।</p>",
                    options_en: [
                        "<p>26 m</p>",
                        "<p>24 m</p>",
                        "<p>22 m</p>",
                        "<p>25 m</p>"
                    ],
                    options_hi: [
                        "<p>26 m</p>",
                        "<p>24 m</p>",
                        "<p>22 m</p>",
                        "<p>25 m</p>"
                    ],
                    solution_en: "<p>56.(b)<br>Ratio -&nbsp; A&nbsp; :&nbsp; &nbsp;B&nbsp; &nbsp;:&nbsp; C<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 100 :&nbsp; 80&nbsp; :&nbsp;<strong> 80</strong><br>&nbsp; &nbsp; &nbsp; &nbsp; <strong>&nbsp; 100</strong> : 100 :&nbsp; 95<br>-----------------------------------<br>Final - 10000 : 8000 : 7600<br>Distance covered by A and C is 100 and 76 m,<br>So we can say that A beats C 100 - 76 = 24 m</p>",
                    solution_hi: "<p>56.(b)<br>अनुपात - A&nbsp; :&nbsp; &nbsp;B&nbsp; &nbsp;:&nbsp; C<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;100 :&nbsp; 80&nbsp; :&nbsp;<strong> 80</strong><br>&nbsp; &nbsp; &nbsp; &nbsp; <strong>&nbsp; &nbsp;100</strong> : 100 :&nbsp; 95<br>-----------------------------------<br>अंतिम- 10000 : 8000 : 7600<br>A और B द्वारा तय की गयी दूरी 100 और 76 m है<br>तो हम कह सकते है कि A, B को 100 - 76 = 24 m से हराता है</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "57",
                    section: "17",
                    question_en: "<p>57. What is the value of (<math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><msqrt><mn>3</mn></msqrt></mrow></mfrac></math> - cosec60&deg;) ?</p>",
                    question_hi: "<p>57. (<math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><msqrt><mn>3</mn></msqrt></mrow></mfrac></math> - cosec60&deg;) का मान कितना होगा?</p>",
                    options_en: [
                        "<p><math display=\"inline\"><msqrt><mn>3</mn></msqrt></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><msqrt><mn>3</mn></msqrt></mrow></mfrac></math></p>",
                        "<p>3<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><msqrt><mn>3</mn></msqrt></mrow></mfrac></math></p>"
                    ],
                    options_hi: [
                        "<p><math display=\"inline\"><msqrt><mn>3</mn></msqrt></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><msqrt><mn>3</mn></msqrt></mrow></mfrac></math></p>",
                        "<p>3<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><msqrt><mn>3</mn></msqrt></mrow></mfrac></math></p>"
                    ],
                    solution_en: "<p>57.(a)<br>(<math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><msqrt><mn>3</mn></msqrt></mrow></mfrac></math> - cosec60&deg;)<br>(<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><msqrt><mn>3</mn></msqrt></mfrac></math> -&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>2</mn><msqrt><mn>3</mn></msqrt></mfrac></mstyle></math>) = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><msqrt><mn>3</mn></msqrt></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math></p>",
                    solution_hi: "<p>57.(a)<br>(<math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><msqrt><mn>3</mn></msqrt></mrow></mfrac></math> - cosec60&deg;)<br>(<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><msqrt><mn>3</mn></msqrt></mfrac></math> -&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>2</mn><msqrt><mn>3</mn></msqrt></mfrac></mstyle></math>) = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><msqrt><mn>3</mn></msqrt></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "58",
                    section: "17",
                    question_en: "<p>58. A number is divisible by 11 if and only if the difference of the sum of the digits in the even and odd positions in the number is:</p>",
                    question_hi: "<p>58. एक संख्या तब 11 से विभाज्य होती है यदि संख्या में सम और विषम स्थान के अंकों के योग का अंतर&nbsp;केवल और केवल ______ हो।</p>",
                    options_en: [
                        "<p>0 or multiple of 11</p>",
                        "<p>11</p>",
                        "<p>0</p>",
                        "<p>1</p>"
                    ],
                    options_hi: [
                        "<p>0 या 11 का गुणज</p>",
                        "<p>11</p>",
                        "<p>0</p>",
                        "<p>1</p>"
                    ],
                    solution_en: "<p>58.(a)<br>Number is divisible by 11 if and only if the difference of the sum of the digits in the even and odd positions in the number is 0 or multiple of 11</p>",
                    solution_hi: "<p>58.(a)<br>संख्या 11 से विभाज्य है यदि संख्या में सम और विषम स्थानों के अंकों के योग का अंतर 0 है या 11 का गुणज है</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "59",
                    section: "17",
                    question_en: "<p>59. (8<sup>6</sup> + 1) when divided by 7, would leave a remainder of:</p>",
                    question_hi: "<p>59. (8<sup>6</sup> + 1) को 7 से विभाजित करने पर शेषफल के रूप में _______ प्राप्त होगा।</p>",
                    options_en: [
                        "<p>1</p>",
                        "<p>4</p>",
                        "<p>2</p>",
                        "<p>6</p>"
                    ],
                    options_hi: [
                        "<p>1</p>",
                        "<p>4</p>",
                        "<p>2</p>",
                        "<p>6</p>"
                    ],
                    solution_en: "<p>59.(c)<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><msup><mn>8</mn><mn>6</mn></msup><mo>+</mo><mn>1</mn><mo>)</mo></mrow><mn>7</mn></mfrac></math> = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><msup><mn>1</mn><mn>6</mn></msup><mo>+</mo><mn>1</mn><mo>)</mo></mrow><mn>7</mn></mfrac></math> = 2</p>",
                    solution_hi: "<p>59.(c)<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><msup><mn>8</mn><mn>6</mn></msup><mo>+</mo><mn>1</mn><mo>)</mo></mrow><mn>7</mn></mfrac></math> = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><msup><mn>1</mn><mn>6</mn></msup><mo>+</mo><mn>1</mn><mo>)</mo></mrow><mn>7</mn></mfrac></math> = 2</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "60",
                    section: "17",
                    question_en: "<p>60. Simple interest and compound interest (compounding annually) on a principal at a certain rate of interest for 2 years is Rs. 12000 and Rs. 14400 respectively. What is the principal?</p>",
                    question_hi: "<p>60. निश्चित ब्याज दर पर 2 वर्षों के लिए एक मूलधन पर साधारण ब्याज और चक्रवृद्धि ब्याज (वार्षिक रूप से संयोजित) क्रमशः 12000 रुपए और 14400 रुपए है। मूलधन कितना है?</p>",
                    options_en: [
                        "<p>Rs. 15000</p>",
                        "<p>Rs. 22000</p>",
                        "<p>Rs. 17000</p>",
                        "<p>Rs. 18000</p>"
                    ],
                    options_hi: [
                        "<p>15000 रुपए</p>",
                        "<p>22000 रुपए</p>",
                        "<p>17000 रुपए</p>",
                        "<p>18000 रुपए</p>"
                    ],
                    solution_en: "<p>60.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741335138066.png\" alt=\"rId55\" width=\"193\" height=\"139\"><br>(CI - SI) for 2 yrs = 14400 - 12000 = ₹2400<br>SI for 1 yr = <math display=\"inline\"><mfrac><mrow><mn>12000</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> = ₹6000<br>rate% = <math display=\"inline\"><mfrac><mrow><mn>2400</mn></mrow><mrow><mn>6000</mn></mrow></mfrac></math> &times; 100 = 40%<br>Principal amount = 6000 &times; <math display=\"inline\"><mfrac><mrow><mn>100</mn></mrow><mrow><mn>40</mn></mrow></mfrac></math> = ₹15,000</p>",
                    solution_hi: "<p>60.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741335138226.png\" alt=\"rId56\" width=\"209\" height=\"154\"><br>(CI - SI) 2 साल के लिए = 14400 - 12000 = ₹2400<br>1 वर्ष के लिए साधारण ब्याज = <math display=\"inline\"><mfrac><mrow><mn>12000</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> = ₹6000<br>दर% = <math display=\"inline\"><mfrac><mrow><mn>2400</mn></mrow><mrow><mn>6000</mn></mrow></mfrac></math> &times; 100 = 40%<br>मूल राशि = 6000 &times; <math display=\"inline\"><mfrac><mrow><mn>100</mn></mrow><mrow><mn>40</mn></mrow></mfrac></math> = ₹15,000</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "61",
                    section: "17",
                    question_en: "<p>61. If the speed of a boat in still water is 3 km/h and if its speed upstream is 1 km/h, find the speed of the stream.</p>",
                    question_hi: "<p>61. यदि स्थिर जल में एक नाव की चाल 3 km/h है और यदि धारा के प्रतिकूल इसकी चाल 1 km/h है, तो धारा की चाल ज्ञात कीजिए।</p>",
                    options_en: [
                        "<p>0.25 km/h</p>",
                        "<p>1 km/h</p>",
                        "<p>2 km/h</p>",
                        "<p>0.5 km/h</p>"
                    ],
                    options_hi: [
                        "<p>0.25 km/h</p>",
                        "<p>1 km/h</p>",
                        "<p>2 km/h</p>",
                        "<p>0.5 km/h</p>"
                    ],
                    solution_en: "<p>61.(c) <br>Speed of boat in still water (x) = 3 km/h<br>Let speed of stream = y&nbsp;km/h<br>x - y = 1<br>3 - y = 1<br>- y = - 2<br>y = 2 km/h</p>",
                    solution_hi: "<p>61.(c) <br>स्थिर जल में एक नाव की चाल (x) = 3 किमी/घंटा<br>माना धारा की की चाल = y&nbsp;किमी/घंटा<br>x - y = 1<br>3 - y = 1<br>- y = - 2<br>y = 2 किमी/घंटा</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "62",
                    section: "17",
                    question_en: "<p>62. The difference between the Smallest Common Multiple and the Highest Common Factor of 15, 20 and 25 is</p>",
                    question_hi: "<p>62. 15, 20 तथा 25 के सबसे छोटे सार्व गुणज़ और सबसे बड़े सार्व गुणनखंड का अंतर है</p>",
                    options_en: [
                        "<p>115</p>",
                        "<p>140</p>",
                        "<p>275</p>",
                        "<p>295</p>"
                    ],
                    options_hi: [
                        "<p>115</p>",
                        "<p>140</p>",
                        "<p>275</p>",
                        "<p>295</p>"
                    ],
                    solution_en: "<p>62.(d)<br>Factor of 15 = 5<sup>1</sup> &times; 3<sup>1</sup> <br>Factor of 20 = 5<sup>1</sup> &times; 2<sup>2</sup> <br>Factor of 25 = 5<sup>2</sup> <br>H.C.F. = 5<br>L.C.M = 5<sup>2</sup> &times; 2<sup>2</sup> &times; 3<sup>1 </sup>= 25 &times; 4 &times; 3 = 300<br>The difference between L.C.M and H.C.F. = 300 - 5 = 295</p>",
                    solution_hi: "<p>62.(d)<br>15 का गुणनखंड = 5<sup>1</sup> &times; 3<sup>1</sup><br>20 का गुणनखंड = 5<sup>1</sup> &times; 2<sup>2</sup> <br>25 का गुणनखंड = 5<sup>2</sup><br>सबसे बड़ा सार्व गुणज़ = 5<br>सबसे छोटा सार्व गुणज़ = 5<sup>2</sup> &times; 2<sup>2</sup> &times; 3<sup>1 </sup>= 25 &times; 4 &times; 3 = 300<br>सबसे छोटे सार्व गुणज़ और सबसे बड़े सार्व गुणज़ के बीच अंतर = 300 - 5 = 295</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "63",
                    section: "17",
                    question_en: "<p>63. If x[ - 5 { - 4 ( - a ) } ] + 6 [ - 3 { -3 ( - a ) } ] = 6a, then the value of x is:</p>",
                    question_hi: "<p>63. यदि x[ - 5 { - 4 ( - a ) } ] + 6 [ - 3 { -3 ( - a ) } ] = 6a है, तो x का मान ज्ञात कीजिए।</p>",
                    options_en: [
                        "<p>&minus;1</p>",
                        "<p>0</p>",
                        "<p>&minus;3</p>",
                        "<p>2 </p>"
                    ],
                    options_hi: [
                        "<p>&minus;1</p>",
                        "<p>0</p>",
                        "<p>&minus;3</p>",
                        "<p>2</p>"
                    ],
                    solution_en: "<p>63.(c)<br>x [ - 5 { - 4 ( - a ) } ] + 6 [ - 3 { -3 ( - a ) } ] = 6a<br>x [ - 5 { 4a } ] + 6 [ - 3 { 3a } ] = 6a<br>x [ - 20a ] + 6 [ - 9a ] = 6a<br>x [ - 20a ] = 6a + 54a<br>x [ - 20a ] = 60a<br>x = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>60</mn><mi>a</mi></mrow><mrow><mo>-</mo><mn>20</mn><mi>a</mi></mrow></mfrac></math> = -3</p>",
                    solution_hi: "<p>63.(c)<br>x [ - 5 { - 4 ( - a ) } ] + 6 [ - 3 { -3 ( - a ) } ] = 6a<br>x [ - 5 { 4a } ] + 6 [ - 3 { 3a } ] = 6a<br>x [ - 20a ] + 6 [ - 9a ] = 6a<br>x [ - 20a ] = 6a + 54a<br>x [ - 20a ] = 60a<br>x = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>60</mn><mi>a</mi></mrow><mrow><mo>-</mo><mn>20</mn><mi>a</mi></mrow></mfrac></math> = -3</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "64",
                    section: "17",
                    question_en: "<p>64. If (48&deg;+ k) is an acute angle and sin(48&deg;+ k) = cos13&deg;, what is the value of k(in &deg;)?</p>",
                    question_hi: "<p>64. यदि (48&deg;+ k) एक न्यून कोण है और sin(48&deg;+ k) = cos13&deg; है, तो k का मान (&deg; में) कितना होगा?</p>",
                    options_en: [
                        "<p>17</p>",
                        "<p>23</p>",
                        "<p>29</p>",
                        "<p>37</p>"
                    ],
                    options_hi: [
                        "<p>17</p>",
                        "<p>23</p>",
                        "<p>29</p>",
                        "<p>37</p>"
                    ],
                    solution_en: "<p>64.(c)<br>sin(48&deg;+ k) = cos13&deg;<br>sin(48&deg;+ k) = cos(90&deg; -77&deg;)<br>sin(48&deg;+ k) = Sin77&deg; <br>(48&deg;+ k) = 77&deg; <br>k = 77&deg; - 48&deg; = <strong>29</strong></p>",
                    solution_hi: "<p>64.(c)<br>sin(48&deg;+ k) = cos13&deg;<br>sin(48&deg;+ k) = cos(90&deg; -77&deg;)<br>sin(48&deg;+ k) = Sin77&deg; <br>(48&deg;+ k) = 77&deg; <br>k = 77&deg; - 48&deg; = <strong>29</strong></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "65",
                    section: "17",
                    question_en: "<p>65. The average of the first 10 natural numbers is _______.</p>",
                    question_hi: "<p>65. प्रथम 10 प्राकृतिक संख्याओं का औसत _______ है।</p>",
                    options_en: [
                        "<p>5</p>",
                        "<p>6</p>",
                        "<p>6.5</p>",
                        "<p>5.5</p>"
                    ],
                    options_hi: [
                        "<p>5</p>",
                        "<p>6</p>",
                        "<p>6.5</p>",
                        "<p>5.5</p>"
                    ],
                    solution_en: "<p>65.(d)<br>Average of first 10 natural numbers = <math display=\"inline\"><mfrac><mrow><mn>10</mn><mo>+</mo><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> = 5.5</p>",
                    solution_hi: "<p>65.(d)<br>प्रथम 10 प्राकृत संख्याओं का औसत = <math display=\"inline\"><mfrac><mrow><mn>10</mn><mo>+</mo><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> = 5.5</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "66",
                    section: "17",
                    question_en: "<p>66. If the areas of three adjacent faces of a cuboid are 5 cm<sup>2</sup> , 15 cm<sup>2</sup> , 27 cm<sup>2</sup> , respectively, the surface area of the cuboid is:</p>",
                    question_hi: "<p>66. यदि एक घनाभ के तीन आसन्न फलकों का क्षेत्रफल क्रमशः 5 cm<sup>2</sup> , 15 cm<sup>2</sup> , 27 cm<sup>2</sup> है, तो घनाभ का पृष्ठीय क्षेत्रफल क्या होगा?</p>",
                    options_en: [
                        "<p>75 cm<sup>2</sup></p>",
                        "<p>47 cm<sup>2</sup></p>",
                        "<p>45 cm<sup>2</sup></p>",
                        "<p>94 cm<sup>2</sup></p>"
                    ],
                    options_hi: [
                        "<p>75 cm<sup>2</sup></p>",
                        "<p>47 cm<sup>2</sup></p>",
                        "<p>45 cm<sup>2</sup></p>",
                        "<p>94 cm<sup>2</sup></p>"
                    ],
                    solution_en: "<p>66.(d) <br>Surface area of cuboid = 2(lb + bh + hl) <br>= 2(5 + 15 + 27) <br>= 94 cm<sup>2</sup></p>",
                    solution_hi: "<p>66.(d) <br>घनाभ का पृष्ठीय क्षेत्रफल = 2(lb + bh + hl) <br>= 2(5 + 15 + 27) <br>= 94 सेमी<sup>2</sup></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "67",
                    section: "17",
                    question_en: "<p>67. For what positive values of k do the following pair of linear equations have infinitely many solutions ?<br>kx&nbsp;+ 3y &ndash; (k &ndash; 3) = 0<br>12x&nbsp;+ ky &ndash; k = 0</p>",
                    question_hi: "<p>67. K के किस धनात्मक मान के लिए रैखिक समीकरणों के निम्नलिखित युग्म के अनंत रूप से अनेक हल&nbsp;हैं?<br>kx&nbsp;+ 3y &ndash; (k &ndash; 3) = 0<br>12x&nbsp;+ ky &ndash; k = 0</p>",
                    options_en: [
                        "<p>12</p>",
                        "<p>4</p>",
                        "<p>2</p>",
                        "<p>6</p>"
                    ],
                    options_hi: [
                        "<p>12</p>",
                        "<p>4</p>",
                        "<p>2</p>",
                        "<p>6</p>"
                    ],
                    solution_en: "<p>67.(d) <br>When equation has infinite number of solutions then,<br><math display=\"inline\"><mfrac><mrow><msub><mrow><mi>a</mi></mrow><mrow><mn>1</mn></mrow></msub></mrow><mrow><msub><mrow><mi>a</mi></mrow><mrow><mn>2</mn></mrow></msub></mrow></mfrac></math> = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msub><mi>b</mi><mn>1</mn></msub><msub><mi>b</mi><mn>2</mn></msub></mfrac></math> = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msub><mi>c</mi><mn>1</mn></msub><msub><mi>c</mi><mn>2</mn></msub></mfrac></math><br>Hence, <br><math display=\"inline\"><mfrac><mrow><mi>k</mi></mrow><mrow><mn>12</mn></mrow></mfrac></math> = <math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mi>k</mi></mrow></mfrac></math> = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>k</mi><mo>-</mo><mn>3</mn></mrow><mi>k</mi></mfrac></math><br>For <math display=\"inline\"><mfrac><mrow><mi>k</mi></mrow><mrow><mn>12</mn></mrow></mfrac></math> = <math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mi>k</mi></mrow></mfrac></math><br>k<sup>2</sup> = 36<br>k = 6<br>For <math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mi>k</mi></mrow></mfrac></math> = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>k</mi><mo>-</mo><mn>3</mn></mrow><mi>k</mi></mfrac></math><br>k = 6<br>Therefore, k satisfied the both equation,<br>Hence, the value of k is 6</p>",
                    solution_hi: "<p>67.(d) <br>जब समीकरण के अनंत संख्या में हल हों तो,<br><math display=\"inline\"><mfrac><mrow><msub><mrow><mi>a</mi></mrow><mrow><mn>1</mn></mrow></msub></mrow><mrow><msub><mrow><mi>a</mi></mrow><mrow><mn>2</mn></mrow></msub></mrow></mfrac></math> = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msub><mi>b</mi><mn>1</mn></msub><msub><mi>b</mi><mn>2</mn></msub></mfrac></math> = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msub><mi>c</mi><mn>1</mn></msub><msub><mi>c</mi><mn>2</mn></msub></mfrac></math><br>इसलिए, <br><math display=\"inline\"><mfrac><mrow><mi>k</mi></mrow><mrow><mn>12</mn></mrow></mfrac></math> = <math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mi>k</mi></mrow></mfrac></math> = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>k</mi><mo>-</mo><mn>3</mn></mrow><mi>k</mi></mfrac></math><br><math display=\"inline\"><mfrac><mrow><mi>k</mi></mrow><mrow><mn>12</mn></mrow></mfrac></math> = <math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mi>k</mi></mrow></mfrac></math>के लिए<br>k<sup>2</sup> = 36<br>k = 6<br><math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mi>k</mi></mrow></mfrac></math> = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>k</mi><mo>-</mo><mn>3</mn></mrow><mi>k</mi></mfrac></math> के लिए<br>k = 6<br>इसलिए, k ने दोनों समीकरण को संतुष्ट किया,<br>अतः, k का मान 6 है</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "68",
                    section: "17",
                    question_en: "<p>68. The ratio of copper and zinc in brass is 25 : 12. How much amount of copper will be there in 370 kg of brass?</p>",
                    question_hi: "<p>68. पीतल में तांबे और जस्ते का अनुपात 25 : 12 है। 370 kg पीतल में तांबे की कितनी मात्रा होगी?</p>",
                    options_en: [
                        "<p>150 kg</p>",
                        "<p>250 kg</p>",
                        "<p>50 kg</p>",
                        "<p>350 kg</p>"
                    ],
                    options_hi: [
                        "<p>150 kg</p>",
                        "<p>250 kg</p>",
                        "<p>50 kg</p>",
                        "<p>350 kg</p>"
                    ],
                    solution_en: "<p>68.(b)<br>25 + 12 = 37 unit --------------- 370 kg<br>25 unit --------------- <math display=\"inline\"><mfrac><mrow><mn>370</mn></mrow><mrow><mn>37</mn></mrow></mfrac></math> &times; 25 = 250 kg</p>",
                    solution_hi: "<p>68.(b)<br>25 + 12 = 37 इकाई --------------- 370 kg<br>25 इकाई --------------- <math display=\"inline\"><mfrac><mrow><mn>370</mn></mrow><mrow><mn>37</mn></mrow></mfrac></math> &times; 25 = 250 kg</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "69",
                    section: "17",
                    question_en: "<p>69. 18 men can complete <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>5</mn></mfrac></math> of a work in 10 days. How many people are required to complete <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>5</mn></mfrac></math> of the same work in 5 days?</p>",
                    question_hi: "<p>69. 18 लोग किसी काम का <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>5</mn></mfrac></math> भाग 10 दिनों में पूरा कर सकते हैं। उसी काम का <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>5</mn></mfrac></math> भाग 5 दिनों में पूरा करने के लिए कितने लोगों की आवश्यकता होगी?</p>",
                    options_en: [
                        "<p>72</p>",
                        "<p>90</p>",
                        "<p>36</p>",
                        "<p>54</p>"
                    ],
                    options_hi: [
                        "<p>72</p>",
                        "<p>90</p>",
                        "<p>36</p>",
                        "<p>54</p>"
                    ],
                    solution_en: "<p>69.(a)<br>By using MDH formula : -<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>18</mn><mo>&#215;</mo><mn>5</mn><mo>&#215;</mo><mn>10</mn></mrow><mn>1</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5</mn><mo>&#215;</mo><mn>5</mn><mo>&#215;</mo><mi mathvariant=\"normal\">m</mi></mrow><mn>2</mn></mfrac></math><br>m = 72</p>",
                    solution_hi: "<p>69.(a)<br>MDH फार्मूले का उपयोग करके : -<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>18</mn><mo>&#215;</mo><mn>5</mn><mo>&#215;</mo><mn>10</mn></mrow><mn>1</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5</mn><mo>&#215;</mo><mn>5</mn><mo>&#215;</mo><mi mathvariant=\"normal\">m</mi></mrow><mn>2</mn></mfrac></math><br>m = 72</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "70",
                    section: "17",
                    question_en: "<p>70. A tangent is drawn from a point at a distance of 25 cm from the Centre of a circle C(0,r) of radius 7cm. The length of the tangent is ___________ cm.</p>",
                    question_hi: "<p>70. 7 cm त्रिज्या वाले वृत्त के केंद्र C(0, r) से 25 cm की दूरी पर एक बिंदु से एक स्पर्शरेखा खींची गई है। स्पर्शरेखा की लंबाई _________ cm है।</p>",
                    options_en: [
                        "<p>8</p>",
                        "<p>16</p>",
                        "<p>20</p>",
                        "<p>24</p>"
                    ],
                    options_hi: [
                        "<p>8</p>",
                        "<p>16</p>",
                        "<p>20</p>",
                        "<p>24</p>"
                    ],
                    solution_en: "<p>70.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741335138406.png\" alt=\"rId57\" width=\"174\" height=\"119\"><br>Length of tangent (PQ) = <math display=\"inline\"><msqrt><msup><mrow><mfenced separators=\"|\"><mrow><mn>25</mn></mrow></mfenced></mrow><mrow><mn>2</mn></mrow></msup><mo>-</mo><msup><mrow><mfenced separators=\"|\"><mrow><mn>7</mn></mrow></mfenced></mrow><mrow><mn>2</mn></mrow></msup></msqrt></math><br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>625</mn><mo>-</mo><mn>49</mn></msqrt></math><br>= <math display=\"inline\"><msqrt><mn>576</mn></msqrt></math><br>= 24 cm</p>",
                    solution_hi: "<p>70.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741335138406.png\" alt=\"rId57\" width=\"184\" height=\"126\"><br>स्पर्शरेखा(PQ) की लंबाई = <math display=\"inline\"><msqrt><msup><mrow><mfenced separators=\"|\"><mrow><mn>25</mn></mrow></mfenced></mrow><mrow><mn>2</mn></mrow></msup><mo>-</mo><msup><mrow><mfenced separators=\"|\"><mrow><mn>7</mn></mrow></mfenced></mrow><mrow><mn>2</mn></mrow></msup></msqrt></math><br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>625</mn><mo>-</mo><mn>49</mn></msqrt></math><br>= <math display=\"inline\"><msqrt><mn>576</mn></msqrt></math><br>= 24 cm</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "71",
                    section: "17",
                    question_en: "<p>71. A bus driver must complete a 240 km trip in 4 hours. If he averages 70 km an hour for the first 3 hours of his trip, how fast must he travel (in km/h) in the last/final hour?</p>",
                    question_hi: "<p>71. एक बस चालक को 240 किमी की यात्रा 4 घंटे में पूरी करनी है। यदि वह अपनी यात्रा के पहले 3 घंटो में औसतन 70 किमी प्रति घंटे की चाल से चलता है, तो उसे आखिरी/अंतिम घंटे में किस चाल से (किमी/घंटा में) यात्रा करनी होगी?</p>",
                    options_en: [
                        "<p>50</p>",
                        "<p>60</p>",
                        "<p>30</p>",
                        "<p>35</p>"
                    ],
                    options_hi: [
                        "<p>50</p>",
                        "<p>60</p>",
                        "<p>30</p>",
                        "<p>35</p>"
                    ],
                    solution_en: "<p>71.(c)<br>Distance covered in first 3 hours = 3 &times; 70 = 210 km<br>Remaining distance = 240 - 210 = 30 km<br>Remaining time = 4 - 3 = 1 hour<br>Required speed = <math display=\"inline\"><mfrac><mrow><mn>30</mn></mrow><mrow><mn>1</mn></mrow></mfrac></math> = 30 km/h</p>",
                    solution_hi: "<p>71.(c)<br>पहले 3 घंटों में तय की गई दूरी = 3 &times; 70 = 210 km<br>शेष दूरी = 240 - 210 = 30 km<br>शेष समय = 4 - 3 = 1 घंटा<br>आवश्यक गति = <math display=\"inline\"><mfrac><mrow><mn>30</mn></mrow><mrow><mn>1</mn></mrow></mfrac></math> = 30 km/h</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "72",
                    section: "17",
                    question_en: "<p>72. A and B can complete a piece of work in 12 days. B and C can complete the same work in 15 days and C and A in 10 days. How many days will A take to complete the work by himself?</p>",
                    question_hi: "<p>72. यदि A और B एक कार्य को 12 दिनों में पूरा कर सकते हैं। B और C उसी कार्य को 15 दिनों में तथा C और A, 10 दिनों में पूरा कर सकते हैं। A को अकेले उस कार्य को पूरा करने में कितने दिन लगेंगे?</p>",
                    options_en: [
                        "<p>9 <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math></p>",
                        "<p>16 <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math></p>",
                        "<p>17<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math></p>",
                        "<p>13<math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math></p>"
                    ],
                    options_hi: [
                        "<p>9 <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math></p>",
                        "<p>16 <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math></p>",
                        "<p>17<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math></p>",
                        "<p>13<math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math></p>"
                    ],
                    solution_en: "<p>72.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741335138607.png\" alt=\"rId58\" width=\"204\" height=\"104\"><br>Efficiency of A + B + C = <math display=\"inline\"><mfrac><mrow><mn>5</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>4</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>6</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> = 7.5 unit<br>Efficiency of A = 7.5 - 4 = 3.5 unit<br>Hence, required days = <math display=\"inline\"><mfrac><mrow><mn>60</mn></mrow><mrow><mn>3</mn><mo>.</mo><mn>5</mn></mrow></mfrac></math> = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>120</mn><mn>7</mn></mfrac></math> = 17<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>7</mn></mfrac></math> days</p>",
                    solution_hi: "<p>72.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741335138914.png\" alt=\"rId59\" width=\"189\" height=\"113\"><br>A + B + C की दक्षता = <math display=\"inline\"><mfrac><mrow><mn>5</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>4</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>6</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> = 7.5 इकाई<br>A की दक्षता = 7.5 - 4 = 3.5 इकाई<br>अतः, आवश्यक दिन = <math display=\"inline\"><mfrac><mrow><mn>60</mn></mrow><mrow><mn>3</mn><mo>.</mo><mn>5</mn></mrow></mfrac></math> = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>120</mn><mn>7</mn></mfrac></math> = 17<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>7</mn></mfrac></math>&nbsp;दिन</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "73",
                    section: "17",
                    question_en: "<p>73. In a group of 250 persons, 40% are males and 60% are females. If 30 more females join the group, then what will be the percentage of females in the new group? (Correct to 2 decimal places)</p>",
                    question_hi: "<p>73. 250 लोगों के एक समूह में 40% पुरुष और 60% महिलाएँ हैं। यदि समूह में 30 और महिलाएँ शामिल हो जाती हैं, तो नए समूह में महिलाओं का प्रतिशत कितना होगा? (दशमलव के 2 स्थानों तक सही)</p>",
                    options_en: [
                        "<p>90.25%</p>",
                        "<p>62.58%</p>",
                        "<p>64.29%</p>",
                        "<p>72.25%</p>"
                    ],
                    options_hi: [
                        "<p>90.25%</p>",
                        "<p>62.58%</p>",
                        "<p>64.29%</p>",
                        "<p>72.25%</p>"
                    ],
                    solution_en: "<p>73.(c)<br>Total number of person in the group = 250<br>Female initially has in the group = 250 &times; 60% = 150<br>After adding 30 female in the group <br>Total female in the group now = 150 + 30 = 180<br>Total persons in the group now = 250 + 30 = 280<br>Hence, required % = <math display=\"inline\"><mfrac><mrow><mn>180</mn></mrow><mrow><mn>280</mn></mrow></mfrac></math> &times; 100 = 64.29%</p>",
                    solution_hi: "<p>73.(c)<br>समूह में व्यक्तियों की कुल संख्या = 250<br>प्रारंभ में समूह में महिलाओं की संख्या = 250 &times; 60% = 150 <br>समूह में 30 महिलाओं को जोड़ने के बाद <br>अब समूह में कुल महिलाएँ = 150 + 30 = 180<br>अब समूह में कुल व्यक्ति = 250 + 30 = 280<br>अतः, आवश्यक % = <math display=\"inline\"><mfrac><mrow><mn>180</mn></mrow><mrow><mn>280</mn></mrow></mfrac></math> &times; 100 = 64.29%</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "74",
                    section: "17",
                    question_en: "<p>74. If the selling price of 40 articles is equal to the cost price of 50 articles, then the&nbsp;percentage gain is:</p>",
                    question_hi: "<p>74. यदि 40 वस्तुओं का विक्रय मूल्य , 50 वस्तुओं के क्रय मूल्य के बराबर है, तो लाभ प्रतिशत क्या है?</p>",
                    options_en: [
                        "<p>20%</p>",
                        "<p>35%</p>",
                        "<p>25%</p>",
                        "<p>30%</p>"
                    ],
                    options_hi: [
                        "<p>20%</p>",
                        "<p>35%</p>",
                        "<p>25%</p>",
                        "<p>30%</p>"
                    ],
                    solution_en: "<p>74.(c)<br>40 &times; SP = 50 &times; CP<br><math display=\"inline\"><mfrac><mrow><mi>C</mi><mi>P</mi></mrow><mrow><mi>S</mi><mi>P</mi></mrow></mfrac></math> = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>5</mn></mfrac></math><br>Profit % = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> &times; 100 = 25%</p>",
                    solution_hi: "<p>74.(c)<br>40 &times; विक्रय मूल्य = 50 &times; क्रय मूल्य<br><math style=\"font-family:Verdana\" display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2325;&#2381;&#2352;&#2351;</mi><mi mathvariant=\"normal\">&#160;</mi><mi>&#2350;&#2370;&#2354;&#2381;&#2351;</mi></mrow><mrow><mi>&#2357;&#2367;&#2325;&#2381;&#2352;&#2351;</mi><mi mathvariant=\"normal\">&#160;</mi><mi>&#2350;&#2370;&#2354;&#2381;&#2351;</mi><mi mathvariant=\"normal\">&#160;</mi></mrow></mfrac></math> = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>5</mn></mfrac></math><br>लाभ % = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> &times; 100 = 25%</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "75",
                    section: "7",
                    question_en: "<p>75. Study the given bar-graph and answer the question that follows.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741335139035.png\" alt=\"rId60\" width=\"251\" height=\"172\"> <br>What is the ratio of the number of engineers of countries with an above average to the number of engineers of countries with a below average?</p>",
                    question_hi: "<p>75. दिए गए बार-ग्राफ का अध्ययन कीजिए और इसके बाद आगे दिए गए प्रश्न का उत्तर दीजिए।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741335139147.png\" alt=\"rId61\" width=\"281\" height=\"192\"> <br>number of engineers = इंजिनियरों की संख्या<br>Country = देश<br>Bhutan = भूटान, Japan = जापान, Nepal नेपाल, Australia = ऑस्ट्रेलिया, USA = अमेरिका, India = भारत<br>औसत से अधिक इंजीनियर वाले देशों के इंजीनियरों की संख्या और औसत से कम इंजीनियर वाले देशों के इंजीनियरों की संख्या का अनुपात कितना है?</p>",
                    options_en: [
                        "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn><mi>&#160;</mi></mrow><mrow><mn>1</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>39</mn><mi>&#160;</mi></mrow><mrow><mn>71</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>2</mn><mi>&#160;</mi></mrow><mrow><mn>1</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>62</mn><mi>&#160;</mi></mrow><mrow><mn>25</mn></mrow></mfrac></math></p>"
                    ],
                    options_hi: [
                        "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn><mi>&#160;</mi></mrow><mrow><mn>1</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>39</mn><mi>&#160;</mi></mrow><mrow><mn>71</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>2</mn><mi>&#160;</mi></mrow><mrow><mn>1</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>62</mn><mi>&#160;</mi></mrow><mrow><mn>25</mn></mrow></mfrac></math></p>"
                    ],
                    solution_en: "<p>75.(a) Average of the number of engineers = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>20</mn><mo>+</mo><mn>55</mn><mo>+</mo><mn>25</mn><mo>+</mo><mn>65</mn><mo>+</mo><mn>85</mn><mo>+</mo><mn>98</mn></mrow><mn>6</mn></mfrac></math> = 58<br>Number of countries in which the number of engineers is above average = 3<br>Number of countries in which the number of engineers is below average = 3<br>Required ratio <math display=\"inline\"><mo>=</mo></math> 3 : 3 or 1 : 1</p>",
                    solution_hi: "<p>75.(a) इंजीनियरों की संख्या का औसत = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>20</mn><mo>+</mo><mn>55</mn><mo>+</mo><mn>25</mn><mo>+</mo><mn>65</mn><mo>+</mo><mn>85</mn><mo>+</mo><mn>98</mn></mrow><mn>6</mn></mfrac></math>&nbsp;= 58<br>उन देशों की संख्या जिनमें इंजीनियरों की संख्या औसत से अधिक है = 3<br>उन देशों की संख्या जिनमें इंजीनियरों की संख्या औसत से कम है = 3<br>आवश्यक अनुपात <math display=\"inline\"><mo>=</mo></math> 3 : 3 or 1 : 1</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "76",
                    section: "7",
                    question_en: "<p>76. Select the correct active form of the given sentences.<br>Before she came all the work had been completed by me.</p>",
                    question_hi: "<p>76. Select the correct active form of the given sentences.<br>Before she came all the work had been completed by me.</p>",
                    options_en: [
                        "<p>I will complete all my work before she came.</p>",
                        "<p>I had completed all my work before she came.</p>",
                        "<p>I completed all my work before she came.</p>",
                        "<p>I complete all my work before she came.</p>"
                    ],
                    options_hi: [
                        "<p>I will complete all my work before she came.</p>",
                        "<p>I had completed all my work before she came.</p>",
                        "<p>I completed all my work before she came.</p>",
                        "<p>I complete all my work before she came.</p>"
                    ],
                    solution_en: "<p>76.(b) I had completed all my work before she came.<br>(a) I <span style=\"text-decoration: underline;\">will complete</span> all my work before she came. (Incorrect Tense)<br>(c) I <span style=\"text-decoration: underline;\">completed</span> all my work before she came. (Incorrect Tense)<br>(d) I <span style=\"text-decoration: underline;\">complete</span> all my work before she came. (Incorrect Tense)</p>",
                    solution_hi: "<p>76.(b) I had completed all my work before she came.<br>(a) I <span style=\"text-decoration: underline;\">will complete</span> all my work before she came. (गलत tense का प्रयोग)<br>(c) I <span style=\"text-decoration: underline;\">completed</span> all my work before she came. (गलत tense का प्रयोग)<br>(d) I <span style=\"text-decoration: underline;\">complete</span> all my work before she came.(गलत tense का प्रयोग)</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "77",
                    section: "7",
                    question_en: "<p>77. Identify the segment in the sentence which contains grammatical error. Mark &lsquo;no error&rsquo; in case the sentence given is correct. <br>You must take all reasonably precaution to protect yourself and your family.</p>",
                    question_hi: "<p>77. Identify the segment in the sentence which contains grammatical error. Mark &lsquo;no error&rsquo; in case the sentence given is correct. <br>You must take all reasonably precaution to protect yourself and your family.</p>",
                    options_en: [
                        "<p>You must take all</p>",
                        "<p>reasonably precaution</p>",
                        "<p>to protect yourself and your family</p>",
                        "<p>No error</p>"
                    ],
                    options_hi: [
                        "<p>You must take all</p>",
                        "<p>reasonably precaution</p>",
                        "<p>to protect yourself and your family</p>",
                        "<p>No error</p>"
                    ],
                    solution_en: "<p>77.(b) reasonably precaution <br>&ldquo;Reasonably precaution&rdquo; should be replaced by &ldquo;reasonable precaution&rdquo;. The adjective &ldquo;reasonable&rdquo; should be used.</p>",
                    solution_hi: "<p>77.(b) reasonably precaution<br>&ldquo;Reasonably precaution&rdquo; को \"reasonable precaution\" द्वारा प्रतिस्थापित किया जाना चाहिए। \"Reasonable\" adjective का प्रयोग करना चाहिए।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "78",
                    section: "7",
                    question_en: "<p>78. Select the most appropriate option that can substitute the underlined segment in the given sentence.<br>We feel <span style=\"text-decoration: underline;\">warm on</span> the subject</p>",
                    question_hi: "<p>78. Select the most appropriate option that can substitute the underlined segment in the given sentence.<br>We feel <span style=\"text-decoration: underline;\">warm on</span> the subject</p>",
                    options_en: [
                        "<p>warmly about</p>",
                        "<p>warmed on</p>",
                        "<p>warmer on</p>",
                        "<p>warming about</p>"
                    ],
                    options_hi: [
                        "<p>warmly about</p>",
                        "<p>warmed on</p>",
                        "<p>warmer on</p>",
                        "<p>warming about</p>"
                    ],
                    solution_en: "<p>78.(a) <strong>warmly about</strong><br>&lsquo;Warmly&rsquo; means in a very friendly way. The given sentence states that we feel warmly about the subject. Hence, &lsquo;warmly about&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>78.(a)<strong> warmly about</strong><br>&lsquo;Warmly&rsquo; का अर्थ है बहुत मैत्रीपूर्ण तरीके से। दिए गए sentence में बताया गया है कि हम subject के बारे में warmly महसूस करते हैं। अतः, &lsquo;warmly about&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "79",
                    section: "7",
                    question_en: "<p>79. Select the most appropriate meaning of idiom in the sentence.<br>At one&rsquo;s elbow</p>",
                    question_hi: "<p>79. Select the most appropriate meaning of idiom in the sentence.<br>At one&rsquo;s elbow</p>",
                    options_en: [
                        "<p>Next to someone</p>",
                        "<p>Far away</p>",
                        "<p>Strong grip</p>",
                        "<p>Strong bond</p>"
                    ],
                    options_hi: [
                        "<p>Next to someone</p>",
                        "<p>Far away</p>",
                        "<p>Strong grip</p>",
                        "<p>Strong bond</p>"
                    ],
                    solution_en: "<p>79.(a) At one&rsquo;s elbow- next to someone.<br>Example- Raman is standing at her elbow holding her files.</p>",
                    solution_hi: "<p>79.(a) At one&rsquo;s elbow- next to someone.(किसी के बगल में।)<br>Example- Raman is standing at her elbow holding her files.</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "80",
                    section: "7",
                    question_en: "<p>80. Select the option that expresses the given sentence in direct speech. <br>Rekha asked me what I was doing.</p>",
                    question_hi: "<p>80. Select the option that expresses the given sentence in direct speech. <br>Rekha asked me what I was doing.</p>",
                    options_en: [
                        "<p>Rekha said to me, &ldquo;What are you doing?&rdquo;</p>",
                        "<p>Rekha said to me, &ldquo;What are I doing?&rdquo;</p>",
                        "<p>Rekha said to me, &ldquo;What was you doing?&rdquo;</p>",
                        "<p>Rekha said to me, &ldquo;&rsquo;What were you doing?&rdquo;</p>"
                    ],
                    options_hi: [
                        "<p>Rekha said to me, &ldquo;What are you doing?&rdquo;</p>",
                        "<p>Rekha said to me, &ldquo;What are I doing?&rdquo;</p>",
                        "<p>Rekha said to me, &ldquo;What was you doing?&rdquo;</p>",
                        "<p>Rekha said to me, &ldquo;&rsquo;What were you doing?&rdquo;</p>"
                    ],
                    solution_en: "<p>80.(a) Rekha said to me, &ldquo;What are you doing?&rdquo; (Correct)<br>(b) Rekha said to me, &ldquo;What are<span style=\"text-decoration: underline;\"> I </span>doing?&rdquo; (Incorrect Pronoun)<br>(c) Rekha said to me, &ldquo;What <span style=\"text-decoration: underline;\">was</span> you doing?&rdquo; (Incorrect Helping Verb)<br>(d) Rekha said to me, &ldquo;&rsquo;What <span style=\"text-decoration: underline;\">were</span> you doing?&rdquo; (Incorrect Helping Verb)</p>",
                    solution_hi: "<p>80.(a) Rekha said to me, &ldquo;What are you doing?&rdquo; (Correct)<br>(b) Rekha said to me, &ldquo;What are<span style=\"text-decoration: underline;\"> I </span>doing?&rdquo; (गलत Pronoun)<br>(c) Rekha said to me, &ldquo;What <span style=\"text-decoration: underline;\">was</span> you doing?&rdquo; (गलत Helping Verb)<br>(d) Rekha said to me, &ldquo;&rsquo;What <span style=\"text-decoration: underline;\">were</span> you doing?&rdquo; (गलत Helping Verb)</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "81",
                    section: "7",
                    question_en: "<p>81. Find a word that is the synonym of <strong>Viable</strong></p>",
                    question_hi: "<p>81. Find a word that is the synonym of <strong>Viable</strong></p>",
                    options_en: [
                        "<p>Capable</p>",
                        "<p>Useless</p>",
                        "<p>Workable</p>",
                        "<p>Bright</p>"
                    ],
                    options_hi: [
                        "<p>Capable</p>",
                        "<p>Useless</p>",
                        "<p>Workable</p>",
                        "<p>Bright</p>"
                    ],
                    solution_en: "<p>81.(c) workable<br><strong>Viable - </strong>capable of working, functioning, or developing adequately.</p>",
                    solution_hi: "<p>81.(c) workable<br><strong>Viable </strong>का अर्थ है - पर्याप्त रूप से काम करने, कार्य पद्धति या विकसित करने में सक्षम।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "82",
                    section: "7",
                    question_en: "<p>82. Select the option that expresses the given sentence in active voice.<br>The evil king was defeated by Gandolf, the savior.</p>",
                    question_hi: "<p>82. Select the option that expresses the given sentence in active voice.<br>The evil king was defeated by Gandolf, the savior.</p>",
                    options_en: [
                        "<p>Gandolf, the savior, defeated the evil king.</p>",
                        "<p>The savior of Gandolf defeated the evil king.</p>",
                        "<p>Gandolf, the savior, had defeated the evil king.</p>",
                        "<p>Gandolf, the savior, defeats the evil king.</p>"
                    ],
                    options_hi: [
                        "<p>Gandolf, the savior, defeated the evil king.</p>",
                        "<p>The savior of Gandolf defeated the evil king.</p>",
                        "<p>Gandolf, the savior, had defeated the evil king.</p>",
                        "<p>Gandolf, the savior, defeats the evil king.</p>"
                    ],
                    solution_en: "<p>82.(a) Gandolf, the savior, defeated the evil king. <br>(b) The savior of Gandolf defeated the evil king. (Incorrect Sentence Structure)<br>(c) Gandolf, the savior, <span style=\"text-decoration: underline;\">had defeated</span> the evil king. (Incorrect Tense)<br>(d) Gandolf, the savior, <span style=\"text-decoration: underline;\">defeats</span> the evil king. (Incorrect Verb)</p>",
                    solution_hi: "<p>82.(a) Gandolf, the savior, defeated the evil king. <br>(b) The savior of Gandolf defeated the evil king. (गलत Sentence Structure)<br>(c) Gandolf, the savior, <span style=\"text-decoration: underline;\">had defeated</span> the evil king. (गलत Tense)<br>(d) Gandolf, the savior, <span style=\"text-decoration: underline;\">defeats</span> the evil king. (गलत Verb)</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "83",
                    section: "7",
                    question_en: "<p>83. Given below are four jumbled sentences. Pick the option that gives their correct order.<br>A. He had to walk along the new national highway that had come up near his village.<br>B. He did not like the noise so they decided to go back home<br>C. Ramu the farmer set out with his best cows to the cattle fair <br>D. The road had many cars and trucks and all the drivers seemed to be honking loudly.</p>",
                    question_hi: "<p>83. Given below are four jumbled sentences. Pick the option that gives their correct order.<br>A. He had to walk along the new national highway that had come up near his village.<br>B. He did not like the noise so they decided to go back home<br>C. Ramu the farmer set out with his best cows to the cattle fair <br>D. The road had many cars and trucks and all the drivers seemed to be honking loudly.</p>",
                    options_en: [
                        "<p>CDBA</p>",
                        "<p>DBCA</p>",
                        "<p>CADB</p>",
                        "<p>CABD</p>"
                    ],
                    options_hi: [
                        "<p>CDBA</p>",
                        "<p>DBCA</p>",
                        "<p>CADB</p>",
                        "<p>CABD</p>"
                    ],
                    solution_en: "<p>83.(c) CADB<br>Sentence C will be the starting line as it contains the main idea of the parajumble i.e. Ramu, who took his cows to a cattle fair. And, Sentence A states through which route he had gone there. So, A will follow C. Further, Sentence D states about the situation of the road and Sentence B states that he went back home. So, B will follow D. Going through the options, option (c) has the correct sequence.</p>",
                    solution_hi: "<p>83.(c) CADB<br>Sentence C starting line होगी क्योंकि इसमें parajumble का मुख्य विचार &lsquo;Ramu, who took his cows to a cattle fair&rsquo; शामिल है,। Sentence A बताता है कि वह किस रास्ते से वहाँ गया था। तो, C के बाद A आएगा। आगे, Sentence D सड़क की स्थिति के बारे में बताता है और Sentence B कहता है कि वह घर वापस चला गया। तो, D के बाद B आएगा। Options के माध्यम से जाने पर , option (c) में सही sequence है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "84",
                    section: "7",
                    question_en: "<p>84. Pick a word opposite in meaning to Tenacious</p>",
                    question_hi: "<p>84. Pick a word opposite in meaning to Tenacious</p>",
                    options_en: [
                        "<p>Steadfast</p>",
                        "<p>Relentless</p>",
                        "<p>Persistent</p>",
                        "<p>Yielding</p>"
                    ],
                    options_hi: [
                        "<p>Steadfast</p>",
                        "<p>Relentless</p>",
                        "<p>Persistent</p>",
                        "<p>Yielding</p>"
                    ],
                    solution_en: "<p>84.(d) Yielding<br><strong>Tenacious- </strong>not likely to give up or let something go or determined<br><strong>Yielding- </strong>to stop refusing to do something or to obey somebody.<br><strong>Steadfast-</strong> faithful and loyal<br><strong>Relentless-</strong> not stopping or changing<br><strong>Persistent-</strong> lasting for a long time or happening often</p>",
                    solution_hi: "<p>84.(d) Yielding<br><strong>Tenacious-</strong>आसानी से हार न मानने वाला; दृढ़निश्&zwj;चय। <br><strong>Yielding-</strong> किसी के सामने झुक जाना, किसी की बात मान लेना।&nbsp;<br><strong>Steadfast-</strong>विश्&zwj;वसनीय एवं निष्&zwj;ठावान। <br><strong>Relentless-</strong> रुकना या बदलना नहीं। <br><strong>Persistent- </strong>लंबे समय तक रहने वाला या बार-बार होने वाला।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "85",
                    section: "7",
                    question_en: "<p>85. Rectify the sentence by selecting the correct spelling from the options.<br>The advertisement of vacencies in education sector has received huge response.</p>",
                    question_hi: "<p>85. Rectify the sentence by selecting the correct spelling from the options.<br>The advertisement of vacencies in education sector has received huge response.</p>",
                    options_en: [
                        "<p>Vecencies</p>",
                        "<p>Vacansies</p>",
                        "<p>Vecancies</p>",
                        "<p>Vacancies</p>"
                    ],
                    options_hi: [
                        "<p>Vecencies</p>",
                        "<p>Vacansies</p>",
                        "<p>Vecancies</p>",
                        "<p>Vacancies</p>"
                    ],
                    solution_en: "<p>85.(d) Vacancies<br>&lsquo;Vacancies&rsquo; is the correct spelling.</p>",
                    solution_hi: "<p>85.(d) Vacancies<br>&lsquo;Vacancies&rsquo; सही spelling है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "86",
                    section: "7",
                    question_en: "<p>86. Select the most appropriate meaning of the idiom ( in the context).<br>Though he is not a scholar, he wins arguments because he has <span style=\"text-decoration: underline;\"><strong>the gift of the gab</strong></span>.</p>",
                    question_hi: "<p>86. Select the most appropriate meaning of the idiom ( in the context).<br>Though he is not a scholar, he wins arguments because he has <span style=\"text-decoration: underline;\"><strong>the gift of the gab</strong></span>.</p>",
                    options_en: [
                        "<p>gifts from many people</p>",
                        "<p>a lot of money</p>",
                        "<p>a talent for speaking</p>",
                        "<p>a good memory</p>"
                    ],
                    options_hi: [
                        "<p>gifts from many people</p>",
                        "<p>a lot of money</p>",
                        "<p>a talent for speaking</p>",
                        "<p>a good memory</p>"
                    ],
                    solution_en: "<p>86.(c) a talent for speaking.<br>Example- All the news anchors have a gift of the gab.</p>",
                    solution_hi: "<p>86.(c) a talent for speaking./बोलने की प्रतिभा। <br>उदाहरण - All the news anchors have a gift of the gab./ सभी न्यूज़ एंकरों में बोलने का हुनर ​​होता है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "87",
                    section: "7",
                    question_en: "87. The following sentence has been split into four segments. Identify the segment that contains a grammatical error.<br />It was noticed / that he truthful / answered the questions / raised by the Headmistress.",
                    question_hi: "87. The following sentence has been split into four segments. Identify the segment that contains a grammatical error.<br />It was noticed / that he truthful / answered the questions / raised by the Headmistress.",
                    options_en: [
                        " It was noticed",
                        " that he truthful",
                        " raised by the Headmistress.",
                        " answered the questions"
                    ],
                    options_hi: [
                        " It was noticed",
                        " that he truthful",
                        " raised by the Headmistress.",
                        " answered the questions"
                    ],
                    solution_en: "87.(b) that he truthful<br />The given sentence needs an adverb(truthfully) to modify the verb ‘answered’, not the adjective ‘truthful’. Hence, ‘that he truthfully’ is the most appropriate answer.",
                    solution_hi: "87.(b) that he truthful<br />दिए गए sentence में verb ‘answered’ को modify करने के लिए एक adverb (truthfully) की आवश्यकता है, न कि adjective ‘truthful’ की। अतः, ‘that he truthfully’ सबसे उपयुक्त उत्तर है।",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "88",
                    section: "7",
                    question_en: "<p>88. Select the word which means the same as the group of words given<br>The art of good eating</p>",
                    question_hi: "<p>88. Select the word which means the same as the group of words given<br>The art of good eating</p>",
                    options_en: [
                        "<p>gastronomy</p>",
                        "<p>astronomy</p>",
                        "<p>vegetarianism</p>",
                        "<p>gourmet</p>"
                    ],
                    options_hi: [
                        "<p>gastronomy</p>",
                        "<p>astronomy</p>",
                        "<p>vegetarianism</p>",
                        "<p>gourmet</p>"
                    ],
                    solution_en: "<p>88.(a) Gastronomy</p>",
                    solution_hi: "<p>88.(a) Gastronomy</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "89",
                    section: "7",
                    question_en: "<p>89.&nbsp;Out of the four alternatives choose the one which can be substituted.<br>Make impure by adding inferior substances</p>",
                    question_hi: "<p>89.&nbsp;Out of the four alternatives choose the one which can be substituted.<br>Make impure by adding inferior substances</p>",
                    options_en: [
                        "<p>Obviate</p>",
                        "<p>Adulterate</p>",
                        "<p>Adjudicate</p>",
                        "<p>Obliterate</p>"
                    ],
                    options_hi: [
                        "<p>Obviate</p>",
                        "<p>Adulterate</p>",
                        "<p>Adjudicate</p>",
                        "<p>Obliterate</p>"
                    ],
                    solution_en: "<p>89.(b) Adulterate<br><strong>Adulterate </strong>- Make impure by adding inferior substances <br><strong>Obviate - </strong>to remove a difficulty, problem or the need for something<br><strong>Adjudicate -</strong> to act as an official judge in a competition or to decide who is right when two people or groups disagree about something <br><strong>Obliterate - </strong>to remove all signs of something by destroying or covering it completely</p>",
                    solution_hi: "<p>89.(b) Adulterate<br><strong>Adulterate</strong>( मिलावट करना ) - Make impure by adding inferior substances&nbsp;<br><strong>Obviate</strong>( मुक्त हो जाना ) - to remove a difficulty, problem or the need for something<br><strong>Adjudicate</strong> ( न्यायनिर्णय ) - to act as an official judge in a competition or to decide who is right when two people or groups disagree about something&nbsp;<br><strong>Obliterate </strong>( नामो निशान मिटा देना ) - to remove all signs of something by destroying or covering it completely</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "90",
                    section: "7",
                    question_en: "<p>90. Find a word that is the synonym of <strong>Incite</strong></p>",
                    question_hi: "<p>90. Find a word that is the synonym of <strong>Incite</strong></p>",
                    options_en: [
                        "<p>Pacify</p>",
                        "<p>Tighten</p>",
                        "<p>Inflame</p>",
                        "<p>Quote</p>"
                    ],
                    options_hi: [
                        "<p>Pacify</p>",
                        "<p>Tighten</p>",
                        "<p>Inflame</p>",
                        "<p>Quote</p>"
                    ],
                    solution_en: "<p>90.(c) Inflame <br><strong>Incite- </strong>to encourage somebody to do something by making him/her very angry or excited<br><strong>Inflame - </strong>provoke or intensify<br><strong>Pacify - </strong>to make somebody who is angry or upset be calm or quiet<br><strong>Tighten -</strong> to become or to make something tight or tighter<br><strong>Quote - </strong>to repeat exactly something that somebody else has said or written before</p>",
                    solution_hi: "<p>90.(c) Inflame <br><strong>Incite- </strong>किसी को बहुत क्रोधित या उत्तेजित करके कुछ करने के लिए प्रोत्साहित करना<br><strong>Inflame- </strong>भड़काना । <br><strong>Pacify-</strong> किसी को क्रोधित या परेशान करने के लिए शांत या चुप रहना। <br><strong>Tighten- </strong>किसी चीज का कड़ा होना या कड़ा बनाना । <br><strong>Quote- </strong>ठीक उसी बात को दोहराना जो किसी और ने पहले कही या लिखी हो।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "91",
                    section: "7",
                    question_en: "<p>91. Given below are four jumbled sentences. Pick the option that gives their correct order.<br>A. A Tiger sleeping peacefully inside the cave was disturbed by the noise and it came out roaring. <br>B. On seeing the tiger Shantanu was so scared that all his hair fell off his head.<br>C. Shantanu was very sad as it seemed that no one would cut his hair on the annual haircut day.<br>D. He went off by himself, passed the village forest and he start near a cave and cried loudly.</p>",
                    question_hi: "<p>91. Given below are four jumbled sentences. Pick the option that gives their correct order.<br>A. A Tiger sleeping peacefully inside the cave was disturbed by the noise and it came out roaring. <br>B. On seeing the tiger Shantanu was so scared that all his hair fell off his head.<br>C. Shantanu was very sad as it seemed that no one would cut his hair on the annual haircut day.<br>D. He went off by himself, passed the village forest and he start near a cave and cried loudly.</p>",
                    options_en: [
                        "<p>ABCD</p>",
                        "<p>CDAB</p>",
                        "<p>CABD</p>",
                        "<p>ABDC</p>"
                    ],
                    options_hi: [
                        "<p>ABCD</p>",
                        "<p>CDAB</p>",
                        "<p>CABD</p>",
                        "<p>ABDC</p>"
                    ],
                    solution_en: "<p>91. (b) <strong>CDAB</strong><br>Sentence C will be the starting line as it contains the main subject of the parajumble i.e. Shantanu,who was very sad. And, Sentence D states where he went. So, D will follow C. Further, Sentence A states how the tiger reacted to the noise and Sentence B states what happened to Shantanu after being scared. So, B will follow A. Going through the options, option (b) has the correct sequence.</p>",
                    solution_hi: "<p>91. (b)<strong> CDAB</strong><br>Sentence C starting line होगी क्योंकि इसमें parajumble का मुख्य विषय &lsquo;Shantanu,who was very sad&rsquo; शामिल है। Sentence D बताता है कि वह कहाँ गया था। तो, C के बाद D आएगा। आगे, Sentence A बताता है कि बाघ ने शोर पर कैसे प्रतिक्रिया दी और Sentence B बताता है कि डरने के बाद शांतनु के साथ क्या हुआ। तो, A के बाद B आएगा । Options के माध्यम से जाने पर, option (b) में सही sequence है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "92",
                    section: "7",
                    question_en: "92. Select the most appropriate option to fill in the blank.<br />Why do we always have to submit _________ his authority?",
                    question_hi: "92. Select the most appropriate option to fill in the blank.<br />Why do we always have to submit _________ his authority?",
                    options_en: [
                        " under  ",
                        " with  ",
                        " for  ",
                        " to"
                    ],
                    options_hi: [
                        " under  ",
                        " with  ",
                        " for  ",
                        " to"
                    ],
                    solution_en: "92.(d) Preposition ‘to’ should be used with \'submit here.<br />Submit to someone means to accept somebody/something’s power or control over you.",
                    solution_hi: "92.(d) Preposition ‘to’ should be used with \'submit here.<br />Submit (सौंपने) का अर्थ है किसी की शक्ति को स्वीकार करना या खुद पर नियंत्रण करना।",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "93",
                    section: "7",
                    question_en: "<p>93. Pick a word opposite in meaning to <strong>Profound</strong></p>",
                    question_hi: "<p>93. Pick a word opposite in meaning to <strong>Profound</strong></p>",
                    options_en: [
                        "<p>Superficial</p>",
                        "<p>Large</p>",
                        "<p>Less</p>",
                        "<p>Special</p>"
                    ],
                    options_hi: [
                        "<p>Superficial</p>",
                        "<p>Large</p>",
                        "<p>Less</p>",
                        "<p>Special</p>"
                    ],
                    solution_en: "<p>93.(a) Superficial<br><strong>Profound -</strong> Intense or insightful. Superficial is the antonym.</p>",
                    solution_hi: "<p>93.(a) Superficial<br><strong>Profound </strong>का अर्थ है - <strong>गहन </strong>। Superficial इसका विलोम है। </p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "94",
                    section: "7",
                    question_en: "<p>94. Identify the best way to improve the underlined part of the given sentence. If there is no improvement required, select &lsquo;no improvement&rsquo;.<br>Galileo said that the earth <span style=\"text-decoration: underline;\"><strong>revolved around the sun</strong></span>.</p>",
                    question_hi: "<p>94. Identify the best way to improve the underlined part of the given sentence. If there is no improvement required, select &lsquo;no improvement&rsquo;.<br>Galileo said that the earth <span style=\"text-decoration: underline;\"><strong>revolved around the sun</strong></span>.</p>",
                    options_en: [
                        "<p>has revolved around the sun.</p>",
                        "<p>was revolving round the sun.</p>",
                        "<p>revolves around the sun.</p>",
                        "<p>No improvement.</p>"
                    ],
                    options_hi: [
                        "<p>has revolved around the sun.</p>",
                        "<p>was revolving round the sun.</p>",
                        "<p>revolves around the sun.</p>",
                        "<p>No improvement.</p>"
                    ],
                    solution_en: "<p>94.(c) revolves around the sun.<br>&lsquo;Revolves around the sun&rsquo;. In Indirect speech, the tense of universal truth does not change.</p>",
                    solution_hi: "<p>94.(c) revolves around the sun.<br>&lsquo;Revolves around the sun&rsquo;. Indirect speech में, universal truth का tense नहीं बदलता है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "95",
                    section: "7",
                    question_en: "<p>95. Select the most appropriate option to fill in the blank.<br>The master assured her__________ success in the examination.</p>",
                    question_hi: "<p>95. Select the most appropriate option to fill in the blank.<br>The master assured her__________ success in the examination.</p>",
                    options_en: [
                        "<p>of</p>",
                        "<p>in</p>",
                        "<p>for</p>",
                        "<p>with</p>"
                    ],
                    options_hi: [
                        "<p>of</p>",
                        "<p>in</p>",
                        "<p>for</p>",
                        "<p>with</p>"
                    ],
                    solution_en: "<p>95.(a) of<br>Assure someone of something means to promise somebody that something will certainly happen or be true, especially if he/she is worried.</p>",
                    solution_hi: "<p>95.(a) of<br>Assure someone of something का अर्थ है किसी से वादा करना कि कुछ निश्चित रूप से होगा या सच होगा, खासकर अगर वह चिंतित है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "96",
                    section: "7",
                    question_en: "<p>96.<strong> Cloze text :</strong><br>Sustainability is (96)_______ longer just a buzzword. It (97)________ a necessity and the primary agenda of the world today. Like many other countries, India too is witnessing a growth of (98)_________ green economy. It (99)________ estimated that India\'s renewable energy target will create more than 3.4 million new job opportunities (100)________ 2030. In fact, a recent survey stated that a Sustainability Manager is going to be one of the top 10 jobs this decade. <br>In the context of the passage, select the most appropriate option to fill in blank (96)</p>",
                    question_hi: "<p>96. <strong>Cloze text :</strong><br>Sustainability is (96)_______ longer just a buzzword. It (97)________ a necessity and the primary agenda of the world today. Like many other countries, India too is witnessing a growth of (98)_________ green economy. It (99)________ estimated that India\'s renewable energy target will create more than 3.4 million new job opportunities (100)________ 2030. In fact, a recent survey stated that a Sustainability Manager is going to be one of the top 10 jobs this decade. <br>In the context of the passage, select the most appropriate option to fill in blank (96)</p>",
                    options_en: [
                        "<p>any</p>",
                        "<p>never</p>",
                        "<p>no</p>",
                        "<p>not</p>"
                    ],
                    options_hi: [
                        "<p>any</p>",
                        "<p>never</p>",
                        "<p>no</p>",
                        "<p>not</p>"
                    ],
                    solution_en: "<p>96.(c) no<br>The phrase &lsquo;no longer&rsquo; means earlier but not now. The given passage states that sustainability is no longer just a buzzword. Hence, &lsquo;no&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>96.(c) no<br>Phrase &lsquo;no longer&rsquo; का अर्थ है पहले लेकिन अब नहीं। दिए गए passage में कहा गया है कि स्थिरता(sustainability) अब केवल एक चर्चा(buzzword) का विषय नहीं रह गई है। अतः, &lsquo;no&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "97",
                    section: "7",
                    question_en: "<p>97. <strong>Cloze text :</strong><br>Sustainability is (96)_______ longer just a buzzword. It (97)________ a necessity and the primary agenda of the world today. Like many other countries, India too is witnessing a growth of (98)_________ green economy. It (99)________ estimated that India\'s renewable energy target will create more than 3.4 million new job opportunities (100)________ 2030. In fact, a recent survey stated that a Sustainability Manager is going to be one of the top 10 jobs this decade. <br>In the context of the passage, select the most appropriate option to fill in blank (97).</p>",
                    question_hi: "<p>97. <strong>Cloze text :</strong><br>Sustainability is (96)_______ longer just a buzzword. It (97)________ a necessity and the primary agenda of the world today. Like many other countries, India too is witnessing a growth of (98)_________ green economy. It (99)________ estimated that India\'s renewable energy target will create more than 3.4 million new job opportunities (100)________ 2030. In fact, a recent survey stated that a Sustainability Manager is going to be one of the top 10 jobs this decade. <br>In the context of the passage, select the most appropriate option to fill in blank (97).</p>",
                    options_en: [
                        "<p>will be</p>",
                        "<p>is</p>",
                        "<p>might be</p>",
                        "<p>was</p>"
                    ],
                    options_hi: [
                        "<p>will be</p>",
                        "<p>is</p>",
                        "<p>might be</p>",
                        "<p>was</p>"
                    ],
                    solution_en: "<p>97.(b) is<br>Simple present tense will be used as the given sentence is expressing a general truth about sustainability at present. Hence, &lsquo;is&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>97.(b) is<br>Simple present tense का प्रयोग किया जाएगा क्योंकि दिया गया sentence वर्तमान में स्थिरता(sustainability) के बारे में एक सामान्य सत्य व्यक्त कर रहा है। अतः, &lsquo;is&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "98",
                    section: "7",
                    question_en: "<p>98. <strong>Cloze text :</strong><br>Sustainability is (96)_______ longer just a buzzword. It (97)________ a necessity and the primary agenda of the world today. Like many other countries, India too is witnessing a growth of (98)_________ green economy. It (99)________ estimated that India\'s renewable energy target will create more than 3.4 million new job opportunities (100)________ 2030. In fact, a recent survey stated that a Sustainability Manager is going to be one of the top 10 jobs this decade. <br>In the context of the passage, select the most appropriate option to fill in blank (98).</p>",
                    question_hi: "<p>98. <strong>Cloze text :</strong><br>Sustainability is (96)_______ longer just a buzzword. It (97)________ a necessity and the primary agenda of the world today. Like many other countries, India too is witnessing a growth of (98)_________ green economy. It (99)________ estimated that India\'s renewable energy target will create more than 3.4 million new job opportunities (100)________ 2030. In fact, a recent survey stated that a Sustainability Manager is going to be one of the top 10 jobs this decade. <br>In the context of the passage, select the most appropriate option to fill in blank (98).</p>",
                    options_en: [
                        "<p>his</p>",
                        "<p>hers</p>",
                        "<p>its</p>",
                        "<p>their</p>"
                    ],
                    options_hi: [
                        "<p>his</p>",
                        "<p>hers</p>",
                        "<p>its</p>",
                        "<p>their</p>"
                    ],
                    solution_en: "<p>98.(c) its<br>&lsquo;Its&rsquo; is a possessive used for things, places, animals and human children. In the given sentence, &lsquo;its&rsquo; has been used for &lsquo;India&rsquo;. Hence, &lsquo;its&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>98.(c) its<br>&lsquo;Its&rsquo; एक possessive शब्द है जिसका प्रयोग वस्तु, स्थान, जन्तु एवं मानव बच्चों(human children) के लिए किया जाता है। दिए गए sentence में, &lsquo;its&rsquo; का प्रयोग &lsquo;India&rsquo; के लिए किया गया है। अतः, &lsquo;its&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "99",
                    section: "7",
                    question_en: "<p>99. <strong>Cloze text :</strong><br>Sustainability is (96)_______ longer just a buzzword. It (97)________ a necessity and the primary agenda of the world today. Like many other countries, India too is witnessing a growth of (98)_________ green economy. It (99)________ estimated that India\'s renewable energy target will create more than 3.4 million new job opportunities (100)________ 2030. In fact, a recent survey stated that a Sustainability Manager is going to be one of the top 10 jobs this decade. <br>In the context of the passage, select the most appropriate option to fill in blank (99).</p>",
                    question_hi: "<p>99. <strong>Cloze text :</strong><br>Sustainability is (96)_______ longer just a buzzword. It (97)________ a necessity and the primary agenda of the world today. Like many other countries, India too is witnessing a growth of (98)_________ green economy. It (99)________ estimated that India\'s renewable energy target will create more than 3.4 million new job opportunities (100)________ 2030. In fact, a recent survey stated that a Sustainability Manager is going to be one of the top 10 jobs this decade. <br>In the context of the passage, select the most appropriate option to fill in blank (99).</p>",
                    options_en: [
                        "<p>will be</p>",
                        "<p>were</p>",
                        "<p>had been</p>",
                        "<p>is</p>"
                    ],
                    options_hi: [
                        "<p>will be</p>",
                        "<p>were</p>",
                        "<p>had been</p>",
                        "<p>is</p>"
                    ],
                    solution_en: "<p>99.(d) is <br>The given sentence is an example of passive voice of simple present tense and the subject &lsquo;it&rsquo; is singular. &lsquo;Singular Sub + is + V<sub>3</sub>(estimated)&rsquo; is the correct grammatical structure for it. Hence, &lsquo;is&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>99.(d) is <br>दिया गया sentence, simple present tense के passive voice का एक example है और subject &lsquo;it&rsquo; singular है। &lsquo;Singular Sub + is + V<sub>3</sub>(estimated)&rsquo; इसके लिए सही grammatical structure है। अतः, &lsquo;is&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "100",
                    section: "misc",
                    question_en: "<p>100. <strong>Cloze text :</strong><br>Sustainability is (96)_______ longer just a buzzword. It (97)________ a necessity and the primary agenda of the world today. Like many other countries, India too is witnessing a growth of (98)_________ green economy. It (99)________ estimated that India\'s renewable energy target will create more than 3.4 million new job opportunities (100)________ 2030. In fact, a recent survey stated that a Sustainability Manager is going to be one of the top 10 jobs this decade. <br>In the context of the passage, select the most appropriate option to fill in blank (100).</p>",
                    question_hi: "<p>100. <strong>Cloze text :</strong><br>Sustainability is (96)_______ longer just a buzzword. It (97)________ a necessity and the primary agenda of the world today. Like many other countries, India too is witnessing a growth of (98)_________ green economy. It (99)________ estimated that India\'s renewable energy target will create more than 3.4 million new job opportunities (100)________ 2030. In fact, a recent survey stated that a Sustainability Manager is going to be one of the top 10 jobs this decade. <br>In the context of the passage, select the most appropriate option to fill in blank (100).</p>",
                    options_en: [
                        " with ",
                        " since ",
                        " by ",
                        " for"
                    ],
                    options_hi: [
                        " with ",
                        " since ",
                        " by ",
                        " for"
                    ],
                    solution_en: "100.(c) by<br />‘By’ is used to indicate the end point of a time period of an action or event. Similarly, in the given passage, 2030 is the end point of time for creating more than 3.4 million new jobs. Hence, ‘by’ is the most appropriate answer.",
                    solution_hi: "100.(c) by<br />‘By’ का प्रयोग किसी action या event के time period के अंतिम बिंदु को indicate करने के लिए किया जाता है। इसी तरह, दिए गए passage में, 2030 तक, 3.4 मिलियन से अधिक नये रोजगार सृजित होने का अंतिम समय है। अतः, ‘by’ सबसे उपयुक्त उत्तर है।",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'question-box d-flex align-items-center justify-content-center border rounded';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.question-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            const html = `
                <div class="card shadow-sm mb-4">
                    <div class="card-body">
                        <div class="question-text mb-4">
                            <div class="en">${question.question_en}</div>
                            <div class="hi" style="display:none">${question.question_hi}</div>
                        </div>
                        <div class="options">
                            ${['a', 'b', 'c', 'd'].map((opt, i) => `
                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="radio" name="q${index}" 
                                        id="opt-${index}-${opt}" value="${opt}" 
                                        ${answers[question.id] === opt ? 'checked' : ''}
                                        onclick="selectAnswer('${question.id}', '${opt}')">
                                    <label class="form-check-label w-100 py-2 px-3 rounded hover-bg-light" for="opt-${index}-${opt}">
                                        <div class="en">${question.options_en[i]}</div>
                                        <div class="hi" style="display:none">${question.options_hi[i]}</div>
                                    </label>
                                </div>
                            `).join('')}
                        </div>
                        ${submitted ? `
                            <div class="alert ${answers[question.id] === question.correct ? 'alert-success' : 'alert-danger'} mt-3">
                                ${answers[question.id] ? (answers[question.id] === question.correct ? 'Correct!' : 'Incorrect!') : 'Not attempted'}
                                <div class="mt-2">
                                    <strong>Solution:</strong>
                                    <div class="en">${question.solution_en}</div>
                                    <div class="hi" style="display:none">${question.solution_hi}</div>
                                </div>
                            </div>
                        ` : ''}
                    </div>
                </div>
            `;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // If in mobile view, close the menu
            if (window.innerWidth < 768) {
                toggleMenu(false);
            }
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                document.getElementById(`box-${index}`).classList.add('attempted');
            }
        }
        // Navigate to previous question
        function prevQuestion() {
            if (currentQuestion > 0) {
                showQuestion(currentQuestion - 1);
            }
        }
        // Navigate to next question
        function nextQuestion() {
            if (currentQuestion < questions.length - 1) {
                showQuestion(currentQuestion + 1);
            }
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) {
                showQuestion(section.start);
            }
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update button text
            document.getElementById('lang-btn').innerHTML = currentLang === 'en' ? 
                '<i class="fas fa-language"></i> हिंदी' : 
                '<i class="fas fa-language"></i> English';
        }
        // Toggle menu for mobile
        function toggleMenu(show) {
            const nav = document.getElementById('question-nav');
            const content = document.getElementById('question-content');
            if (show === undefined) {
                show = nav.style.right === '-100%' || nav.style.right === '';
            }
            if (show) {
                nav.style.right = '0';
                if (window.innerWidth >= 768) {
                    content.style.marginRight = '320px';
                }
            } else {
                nav.style.right = window.innerWidth < 768 ? '-100%' : '0';
                content.style.marginRight = window.innerWidth < 768 ? '0' : '320px';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
            document.getElementById('total-count').textContent = questions.length;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.remove('attempted');
                    box.classList.add('bg-secondary', 'text-white');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.remove('attempted');
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.remove('attempted');
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            resultsModal.show();
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check-circle me-1"></i> Submitted';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            resultsModal.hide();
            showQuestion(0);
        }
    </script>
</body>
</html>