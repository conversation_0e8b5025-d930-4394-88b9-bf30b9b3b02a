<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">25:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 40</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">40</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 25 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["18"] = {
                name: "General Awareness",
                start: 0,
                end: 38
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="18">General Awareness</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 39,
                end: 39
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "18",
                    question_en: "1. What was the main focus of the first Five Year Plan of India?",
                    question_hi: "1. भारत की प्रथम पंचवर्षीय योजना का मुख्य उद्देश्य क्या था ?",
                    options_en: [" Development of research and development related to space. ", " Development of primary sector with focus on agriculture. ", 
                                " Growth of information technology ", " Rapid development of heavy industries "],
                    options_hi: [" अंतरिक्ष से संबंधित अनुसंधान और विकास की  उन्नति", " कृषि पर फोकस के साथ प्राथमिक क्षेत्र का विकास",
                                " सूचना प्रौद्योगिकी का विकास", " भारी उद्योगों का तेजी से विकास"],
                    solution_en: "1.(b) The First Five-Year Plan was introduced in 1951. It focused primarily on the development of the primary sector, specifically agriculture and irrigation. The goals of Five Year plans are growth, modernisation, self-reliance and equity. The concept of the 5-year plan was borrowed from the USSR into the Indian constitution.",
                    solution_hi: "1.(b) पहली पंचवर्षीय योजना 1951 में शुरू की गई थी। यह प्राथमिक रूप से प्राथमिक क्षेत्र, विशेष रूप से कृषि और सिंचाई के विकास पर केंद्रित थी। पंचवर्षीय योजनाओं के लक्ष्य विकास, आधुनिकीकरण, आत्मनिर्भरता और इक्विटी हैं। 5-वर्षीय योजना की अवधारणा USSR से भारतीय संविधान में उधार ली गई थी।",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "2",
                    section: "18",
                    question_en: "2. What do paintings at Ajanta caves illustrate?",
                    question_hi: "2.अजंता की गुफाओं की पेंटिंग क्या दर्शाती हैं?",
                    options_en: [" Ramayana tales ", " Mahabharata tales ", 
                                " Jataka tales ", " Stories from vedas "],
                    options_hi: [" रामायण की कहानियां", " महाभारत की कहानियां",
                                " जातक कथाएं", " वेदों की कहानियां"],
                    solution_en: "2.(c)  Paintings at Ajanta caves illustrate Jataka tales. The caves also present paintings depicting the past lives and rebirths of the Buddha, pictorial tales from Aryasura\'s Jatakamala, and rock-cut sculptures of Buddhist deities. Ajanta Caves, Maharashtra state, India, designated a World Heritage site in 1983. There are 34 caves at Ellora. The caves at Ajanta are all Buddhist, while the caves at Ellora are a mixture of Buddhist, Hindu and Jain.",
                    solution_hi: "2.(c)  अजंता की गुफाओं के चित्र जातक कथाओं को चित्रित करते हैं। गुफाओं में बुद्ध के पिछले जीवन और पुनर्जन्म, आर्यसुर की जातकमाला की सचित्र कथाएँ और बौद्ध देवताओं की रॉक-कट मूर्तियों को चित्रित करने वाले चित्र भी मौजूद हैं। अजंता गुफाएं, महाराष्ट्र राज्य, भारत, 1983 में एक विश्व धरोहर स्थल नामित। एलोरा में 34 गुफाएं हैं। अजंता की सभी गुफाएं बौद्ध हैं, जबकि एलोरा की गुफाएं बौद्ध, हिंदू और जैन का मिश्रण हैं।",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "3",
                    section: "18",
                    question_en: "3. What is the ancient name of Iran?",
                    question_hi: "3. ईरान का प्राचीन नाम क्या है?",
                    options_en: [" Mesopotamia ", " Ceylon ", 
                                " Gaul ", " Persia "],
                    options_hi: [" मेसोपोटामिया", " सीलोन",
                                " गॉल ", " फारस"],
                    solution_en: "3.(d) For most of history, the tract of land now called Iran was known as Persia. It wasn\'t until 1935 that it adopted its present name. The modern Persian name of Iran means \"the land of Aryans\".",
                    solution_hi: "3.(d) अधिकांश इतिहास के लिए, अब ईरान कहे जाने वाले भूमि के पथ को फारस के नाम से जाना जाता था। यह 1935 तक नहीं था कि इसने अपना वर्तमान नाम अपनाया। ईरान के आधुनिक फारसी नाम का अर्थ है \"आर्यों की भूमि\"।",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "4",
                    section: "18",
                    question_en: "4. Which one of these comprises the Golden Quadrilateral?",
                    question_hi: "4. इनमें से कौन स्वर्णिम चतुर्भुज में शामिल है?",
                    options_en: [" Delhi- Jaipur-Agra-Meerut ", " Delhi- Mumbai-Bangalore-Kolkata ", 
                                " Delhi- Pune-Chennai-Bhubaneshwar ", " Delhi -Mumbai-Chennai - Kolkata "],
                    options_hi: [" दिल्ली-जयपुर-आगरा-मेरठ", " दिल्ली-मुंबई-बैंगलोर-कोलकाता",
                                " दिल्ली-पुणे-चेन्नई-भुवनेश्वर", " दिल्ली-मुंबई-चेन्नई-कोलकाता"],
                    solution_en: "4.(d) Golden Quadrilateral(5846km) is a network of highways connecting India\'s four top metropolitan cities, namely Delhi, Mumbai, Chennai and Kolkata, thereby forming a quadrilateral. The project was planned in 1999, launched in 2001, and was completed in 2013. The Golden Quadrilateral project is managed by the National Highways Authority of India (NHAI) under the Ministry of Road, Transport and Highways.",
                    solution_hi: "4.(d) स्वर्णिम चतुर्भुज (5846 किमी) भारत के चार शीर्ष महानगरीय शहरों, अर्थात् दिल्ली, मुंबई, चेन्नई और कोलकाता को जोड़ने वाले राजमार्गों का एक नेटवर्क है, जिससे एक चतुर्भुज का निर्माण होता है। इस परियोजना की योजना 1999 में बनाई गई थी, जिसे 2001 में लॉन्च किया गया था, और 2013 में पूरा किया गया था। स्वर्णिम चतुर्भुज परियोजना का प्रबंधन सड़क, परिवहन और राजमार्ग मंत्रालय के तहत भारतीय राष्ट्रीय राजमार्ग प्राधिकरण (NHAI) द्वारा किया जाता है।",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "5",
                    section: "18",
                    question_en: "5. Where in India was the most complete fossil of the rare dinosaur-Ichthyosaur found in 2016?",
                    question_hi: "5. 2016 में भारत में दुर्लभ डायनासोर-इचथ्योसौर का सबसे पूर्ण जीवाश्म कहाँ पाया गया था?",
                    options_en: [" Madhya Pradesh ", " Gujarat ", 
                                " Arunachal Pradesh ", " Rajasthan "],
                    options_hi: [" मध्य प्रदेश", " गुजरात",
                                " अरुणाचल प्रदेश", " राजस्थान"],
                    solution_en: "5.(b) 150-million-year-old dinosaur fossil found in Gujarat,the most complete fossil of the rare dinosaur-Ichthyosaur found in 2016. The study of fossils is called paleontology, from the Greek meaning \'ancient (paleo-) being (onto-) study (-logy)\'. Scientists who study fossils are called paleontologists.",
                    solution_hi: "5.(b) गुजरात में मिला 150 मिलियन साल पुराना डायनासोर का जीवाश्म, 2016 में मिला दुर्लभ डायनासोर-इचथ्योसौर का सबसे पूर्ण जीवाश्म। जीवाश्मों के अध्ययन को जीवाश्म विज्ञान कहा जाता है, ग्रीक से अर्थ \'प्राचीन (पैलियो-) होना (पर-) अध्ययन (-लॉजी)\'। जीवाश्मों का अध्ययन करने वाले वैज्ञानिकों को जीवाश्म विज्ञानी कहा जाता है।",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "6",
                    section: "18",
                    question_en: "6. What is the function of alveoli?",
                    question_hi: "6. एल्वियोली का कार्य क्या है?",
                    options_en: [" To help digest food ", " To provide a surface where exchange of gases takes place ", 
                                " To aid in absorption of food ", " To purify blood "],
                    options_hi: [" भोजन पचाने में मदद करने के लिए", " एक सतह प्रदान करने के लिए जहां गैसों का आदान-प्रदान होता है",
                                " भोजन के अवशोषण में सहायता करने के लिए", " रक्त शुद्ध करने के लिए"],
                    solution_en: "6.(b) The alveoli are where the lungs and the blood exchange oxygen and carbon dioxide during the process of breathing in and breathing out. We have about 480 million alveoli, located at the end of bronchial tubes. ",
                    solution_hi: "6.(b) एल्वियोली वह जगह है जहां फेफड़े और रक्त सांस लेने और सांस छोड़ने की प्रक्रिया के दौरान ऑक्सीजन और कार्बन डाइऑक्साइड का आदान-प्रदान करते हैं। हमारे पास लगभग 480 मिलियन एल्वियोली हैं, जो ब्रोन्कियल ट्यूबों के अंत में स्थित हैं।",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "7",
                    section: "18",
                    question_en: "7. Name the strait that divides Morocco and Spain.",
                    question_hi: "7. उस जलडमरूमध्य का नाम बताइए जो मोरक्को और स्पेन को विभाजित करता है",
                    options_en: [" English Channel ", " Strait of Gibraltar ", 
                                " Hudson Strait ", " Bering Strait "],
                    options_hi: [" इंग्लिश चैनल", " जिब्राल्टर की खाड़ी",
                                " हडसन जलडमरूमध्य", " बेरिंग जलडमरूमध्य"],
                    solution_en: "7.(b)  Strait of Gibraltar  divides Morocco and Spain. English Channel,the Atlantic Ocean separating the southern coast of England from the northern coast of France. Hudson Strait Separates Baffin Island from the Ungava Peninsula of Québec. Bering Sea and Strait, northernmost part of the Pacific Ocean, separating the continents of Asia and North America.",
                    solution_hi: "7.(b) जिब्राल्टर जलडमरूमध्य मोरक्को और स्पेन को विभाजित करता है। इंग्लिश चैनल, अटलांटिक महासागर इंग्लैंड के दक्षिणी तट को फ्रांस के उत्तरी तट से अलग करता है। हडसन जलडमरूमध्य बाफिन द्वीप को क्यूबेक के उन्गावा प्रायद्वीप से अलग करता है। बेरिंग सागर और जलडमरूमध्य, प्रशांत महासागर का सबसे उत्तरी भाग, एशिया और उत्तरी अमेरिका के महाद्वीपों को अलग करता है।",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "8",
                    section: "18",
                    question_en: "8. How does most of India’s foreign trade take place?",
                    question_hi: "8.भारत का अधिकांश विदेशी व्यापार कैसे होता है?",
                    options_en: [" Through land and sea routes ", " Through sea and air routes ", 
                                " Through land and air routes", " Equally through land ,air and sea routes each "],
                    options_hi: [" भूमि और समुद्री मार्गों के माध्यम से", " समुद्र और हवाई मार्गों के माध्यम से",
                                " भूमि और हवाई मार्गों के माध्यम से", " समान रूप से भूमि, वायु और समुद्री मार्गों के माध्यम से प्रत्येक"],
                    solution_en: "8.(b) Most of India\'s foreign trade is carried through sea and air routes. However, a small portion is also carried through land routes to neighbouring countries like Nepal, Bhutan, Bangladesh and Pakistan.",
                    solution_hi: "8.(b) भारत का अधिकांश विदेशी व्यापार समुद्री और हवाई मार्गों से होता है। हालांकि, नेपाल, भूटान, बांग्लादेश और पाकिस्तान जैसे पड़ोसी देशों में भूमि मार्गों के माध्यम से एक छोटा सा हिस्सा भी ले जाया जाता है।",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "9",
                    section: "18",
                    question_en: "9. Name the famous Indian writer who has written novels like ‘Untouchable’ and ‘Coolie’.",
                    question_hi: "9. उस प्रसिद्ध भारतीय लेखक का नाम बताइए जिसने \'अछूत\' (‘Untouchable’) और \'कुली\' जैसे उपन्यास लिखे हैं",
                    options_en: [" Anita Desai ", " Mulk Raj Anand ", 
                                " RK Narayan ", " Kamla Das "],
                    options_hi: [" अनीता देसाई", " मुल्क राज आनंद",
                                " आर के नारायण", " कमला दास"],
                    solution_en: "9.(b) .Mulk Raj Anand  has written novels like ‘Untouchable’ and ‘Coolie\'. Famous Books by Mulk Raj Anand,Two Leaves and a Bud,The Village,Across the Black Waters,The Sword and the Sickle,The Big Heart.",
                    solution_hi: "9.(b) मुल्क राज आनंद ने \'अछूत\' और \'कुली\' जैसे उपन्यास लिखे हैं। मुल्क राज आनंद की प्रसिद्ध पुस्तकें, टू लीव्स एंड ए बड, द विलेज, अक्रॉस द ब्लैक वाटर्स, द स्वॉर्ड एंड द सिकल, द बिग हार्ट।",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "10",
                    section: "18",
                    question_en: "10. Who was the founder of the Mizo National Front (MNF)?",
                    question_hi: "10. मिजो नेशनल फ्रंट (MNF) के संस्थापक कौन थे?",
                    options_en: [" Khuangchera ", " Chaweng Bawla ", 
                                " Laldenga ", " Lalnunmawia  "],
                    options_hi: [" खुआंगचेरा", " चावेंग बावला",
                                " लाल्डेंगा", " लालनुनमाविया"],
                    solution_en: "10.(c) The Mizo National Front (abbreviated MNF) is a regional political party in Mizoram, India. MNF emerged from the Mizo National Famine Front, which was formed by Pu Laldenga to protest against the inaction of the Indian central government towards the famine situation in the Mizo areas of the Assam state in 1959.",
                    solution_hi: "10.(c) मिजो नेशनल फ्रंट (संक्षिप्त MNF) मिजोरम, भारत में एक क्षेत्रीय राजनीतिक दल है। MNF मिजो नेशनल फेमिन फ्रंट से उभरा, जिसे पु लालडेंगा ने 1959 में असम राज्य के मिजो क्षेत्रों में अकाल की स्थिति के प्रति भारतीय केंद्र सरकार की निष्क्रियता के विरोध में बनाया था।",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "11",
                    section: "18",
                    question_en: "11. The laws of inheritance were discovered by:",
                    question_hi: "11. उत्तराधिकार के नियमों की खोज किसके द्वारा की गई?",
                    options_en: [" Charles Darwin ", " Archibald Garrod ", 
                                " Hugo de Vries ", " Gregor Mendel "],
                    options_hi: [" चार्ल्स डार्विन", " आर्चीबाल्ड गैरोड",
                                " ह्यूगो डे व्रीस", " ग्रेगर मेंडेल"],
                    solution_en: "11.(d) Gregor Mendel, through his work on pea plants, discovered the fundamental laws of inheritance.Charles Darwin is credited for the theory of natural selection. The botanist Hugo de Vries worked in the fields of heredity and its relation to the origin of species, developing a mutation theory. Archibald Garrod was the first to connect a human disorder with Mendel\'s laws of inheritance.",
                    solution_hi: "11.(d) ग्रेगोर मेंडल ने मटर के पौधों पर अपने काम के माध्यम से वंशानुक्रम के मूलभूत नियमों की खोज की। चार्ल्स डार्विन को प्राकृतिक चयन के सिद्धांत का श्रेय दिया जाता है। वनस्पतिशास्त्री ह्यूगो डी व्रीस ने आनुवंशिकता और प्रजातियों की उत्पत्ति से इसके संबंध के क्षेत्र में काम किया, एक उत्परिवर्तन सिद्धांत विकसित किया। आर्चीबाल्ड गैरोड ने सबसे पहले मानव विकार को मेंडल के वंशानुक्रम के नियमों से जोड़ा था।",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "12",
                    section: "18",
                    question_en: "12. Who out of the following led the Khilafat movement?",
                    question_hi: "12. निम्नलिखित में से किसने खिलाफत आंदोलन का नेतृत्व किया?",
                    options_en: [" Muhammad Ali Jinnah ", " Bakht Khan ", 
                                " Zakir Hussain ", " Shaukat Ali "],
                    options_hi: [" मुहम्मद अली जिन्ना", " बख्त खान",
                                " जाकिर हुसैन", " शौकत अली"],
                    solution_en: "12.(d) The Khilafat movement was started by two Ali brothers. The leaders of this movement were Mohammed Ali and Shaukat Ali - Maulana Azad, Hakim Ajmal Khan and Hasrat Mohani. In 1919, Gandhiji supported the Khilafat Movement to protest against the breakup of Ottoman (Turkish) empire by the British and humiliation caused to Turkish Caliph",
                    solution_hi: "12.(d) खिलाफत आंदोलन की शुरुआत दो अली भाइयों ने की थी। इस आंदोलन के नेता मोहम्मद अली और शौकत अली - मौलाना आजाद, हकीम अजमल खान और हसरत मोहानी थे। 1919 में, गांधीजी ने अंग्रेजों द्वारा तुर्क (तुर्की) साम्राज्य के टूटने और तुर्की खलीफा को हुए अपमान के विरोध में खिलाफत आंदोलन का समर्थन किया।",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "13",
                    section: "18",
                    question_en: "13.  What is the name of the first dedicated Indian astronomy mission that is aimed at studying celestial sources in X-ray. Optical and UV spectral bands simultaneously?",
                    question_hi: "13. पहले समर्पित भारतीय खगोल विज्ञान मिशन का नाम क्या है जिसका उद्देश्य X-रे में खगोलीय स्रोतों का अध्ययन करना है। ऑप्टिकल और UV वर्णक्रमीय बैंड एक साथ?",
                    options_en: [" OpticoSat ", " AstroSat ", 
                                " XtroSat ", " AstroMat "],
                    options_hi: [" ऑप्टिकोसैट", " एस्ट्रोसैट",
                                " एक्स्ट्रासैट", " एस्ट्रोमेट "],
                    solution_en: "13.(b) AstroSat is the first dedicated Indian astronomy mission aimed at studying celestial sources in X-ray, optical and UV spectral bands simultaneously. ISRO built India\'s first satellite, Aryabhata, which was launched by the Soviet Union on 19 April 1975.",
                    solution_hi: "13.(b) एस्ट्रोसैट पहला समर्पित भारतीय खगोल विज्ञान मिशन है जिसका उद्देश्य एक्स-रे, ऑप्टिकल और यूवी स्पेक्ट्रल बैंड में आकाशीय स्रोतों का एक साथ अध्ययन करना है।  ISRO ने भारत का पहला उपग्रह आर्यभट्ट बनाया, जिसे सोवियत संघ ने 19 अप्रैल 1975 को लॉन्च किया था।",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "14",
                    section: "18",
                    question_en: "14.  What is the correct full form of MPEG from below?",
                    question_hi: "14.  नीचे से MPEG का सही पूर्ण रूप क्या है?",
                    options_en: [" Micro Pictures Expert Group", " Many Pictures Expert Group", 
                                " Moving Picture Experts Group", " Motion Pictures Expert Group"],
                    options_hi: [" Micro Pictures Expert Group", " Many Pictures Expert Group",
                                " Moving Picture Experts Group", " Motion Pictures Expert Group"],
                    solution_en: "14.(c) The Moving Picture Experts Group (MPEG) is an alliance of working groups established jointly by ISO and IEC that sets standards for media coding, including compression coding of audio, video, graphics and genomic data, and transmission and file formats for various applications.",
                    solution_hi: "14.(c) मूविंग पिक्चर एक्सपर्ट्स ग्रुप (MPEG) ISO और IEC द्वारा संयुक्त रूप से स्थापित कार्य समूहों का एक गठबंधन है जो मीडिया कोडिंग के लिए मानक निर्धारित करता है, जिसमें ऑडियो, वीडियो, ग्राफिक्स और जीनोमिक डेटा की कम्प्रेशन कोडिंग और विभिन्न अनुप्रयोगों के लिए ट्रांसमिशन और फ़ाइल प्रारूप शामिल हैं।",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "15",
                    section: "18",
                    question_en: "15. The approach based on the idea that ‘organisms which are more distantly related will accumulate a greater number of differences in their DNA’ is called?",
                    question_hi: "15. इस विचार पर आधारित दृष्टिकोण कि \'जीव जो अधिक दूर से संबंधित हैं, उनके DNA’  में अधिक संख्या में अंतर संगृहीत करेंगे\' क्या कहलाते हैं?",
                    options_en: [" The DNA study ", " Morphological approach  ", 
                                " Molecular phylogeny ", " Metamorphosis "],
                    options_hi: [" DNA अध्ययन", " रूपात्मक दृष्टिकोण",
                                " आणविक जातिवृत्त", " कायांतरण"],
                    solution_en: "15.(c) Molecular phylogeny is a technique used to trace the changes in DNA backwards in time and find out where each change diverged from the other does exactly this. This approach is based on the idea that distantly related organisms accumulate a greater number of differences in their DNA.",
                    solution_hi: "15.(c) आणविक फ़ाइलोजेनी एक ऐसी तकनीक है जिसका उपयोग समय में DNA में पीछे की ओर होने वाले परिवर्तनों का पता लगाने के लिए किया जाता है और यह पता लगाया जाता है कि प्रत्येक परिवर्तन दूसरे से अलग कहाँ होता है। यह दृष्टिकोण इस विचार पर आधारित है कि दूर से संबंधित जीव अपने DNA में अधिक संख्या में अंतर जमा करते हैं।",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "16",
                    section: "18",
                    question_en: "16. Valence electrons are present in the :",
                    question_hi: "16. संयोजकता इलेक्ट्रॉन कहाँ उपस्थित होते हैं?",
                    options_en: [" innermost as well as outermost shell of an atom ", " innermost shell of an atom ", 
                                " just outside an atom ", " Outermost shell of an atom "],
                    options_hi: [" एक परमाणु का सबसे भीतरी और साथ ही सबसे बाहरी कोश", " परमाणु का अंतर्तम खोल",
                                " एक परमाणु के ठीक बाहर", " परमाणु का सबसे बाहरी कोश"],
                    solution_en: "16.(d) Valence electrons are the electrons in the outermost shell, or energy level, of an atom.",
                    solution_hi: "16.(d) वैलेंस इलेक्ट्रॉन एक परमाणु के सबसे बाहरी शेल या ऊर्जा स्तर में इलेक्ट्रॉन होते हैं।",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "17",
                    section: "18",
                    question_en: "17. Kharai Camels are known to mainly feed on:",
                    question_hi: "17. खराई ऊंट मुख्य रूप से_________ खाने के लिए जाने जाते हैं",
                    options_en: [" Cactus ", " Mangroves ", 
                                " Joshua tree ", " Brittlebush "],
                    options_hi: [" कैक्टस", " मैंग्रोव ",
                                " जोयूआ ट्री", " ब्रिटलबुश"],
                    solution_en: "17.(b) Kharai camels are known to feed on mangroves on the island offshore. And to eat this salty marine food, they sometimes swim for hours",
                    solution_hi: "17.(b) खराई ऊंट द्वीप के अपतटीय मैंग्रोव पर भोजन करने के लिए जाने जाते हैं। और इस नमकीन समुद्री भोजन को खाने के लिए ये कभी-कभी घंटों तैरते रहते हैं।",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "18",
                    section: "18",
                    question_en: "18. Which of the following country follow more than one time zones?",
                    question_hi: "18.निम्नलिखित में से कौन सा देश एक से अधिक समय क्षेत्रों का अनुसरण करता है?",
                    options_en: [" USA ", " Argentina ", 
                                " China ", " India "],
                    options_hi: [" अमेरिका ", " अर्जेंटीना",
                                " चीन", " भारत"],
                    solution_en: "18.(a) The United States is divided into six time zones: Hawaii-Aleutian time, Alaska time, Pacific time, Mountain time, Central time and Eastern time.",
                    solution_hi: "18.(a) संयुक्त राज्य अमेरिका को छह समय क्षेत्रों में विभाजित किया गया है: हवाई-अलेउतियन समय, अलास्का समय, प्रशांत समय, पर्वतीय समय, मध्य समय और पूर्वी समय।",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "19",
                    section: "18",
                    question_en: "19. Members of Rajya Sabha are elected for the duration of :",
                    question_hi: "19. राज्यसभा के सदस्य कितनी अवधि के लिए चुने जाते हैं?",
                    options_en: [" 5 years ", " 6 years ", 
                                " 3 years", " 4 years "],
                    options_hi: [" 5 वर्ष ", " 6 वर्ष ",
                                " 3 वर्ष ", " 4 वर्ष "],
                    solution_en: "19.(b) Seating capacity of the Rajya Sabha is 250 (238 elected, 12 appointed), according to article 80 of the Indian Constitution. Members sit for staggered terms lasting six years, with elections every year with about a third of the 233 designates up for election every two years, in even-numbered years.",
                    solution_hi: "19.(b) भारतीय संविधान के अनुच्छेद 80 के अनुसार राज्यसभा की बैठने की क्षमता 250 (238 निर्वाचित, 12 नियुक्त) है। सदस्य छह साल तक चलने वाले कंपित पदों के लिए बैठते हैं, हर साल चुनावों के साथ 233 में से लगभग एक तिहाई हर दो साल में, सम-संख्या वाले वर्षों में चुनाव के लिए चुने जाते हैं।",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "20",
                    section: "18",
                    question_en: "20. Who coined the phrase ‘Survival of the fittest’?",
                    question_hi: "20.\'सर्वाइवल ऑफ द फिटेस्ट\' वाक्य किसने दिया ?",
                    options_en: [" Archimedes ", " Herbert Spencer ", 
                                " Charles Darwin ", " Albert Einstein "],
                    options_hi: [" आर्किमिडीज", " हर्बर्ट स्पेंसर",
                                " चार्ल्स डार्विन", " अल्बर्ट आइंस्टीन"],
                    solution_en: "20.(b) The Principles of Biology by Herbert Spencer (1864) looked at biology in terms of themes, such as Function, Adaptation and Variation. In this book Spencer introduced the expression \'survival of the fittest\', in the sense of \'the most appropriate to its environment\'.",
                    solution_hi: "20.(b) हर्बर्ट स्पेंसर (1864) द्वारा जीव विज्ञान के सिद्धांतों ने जीव विज्ञान को विषयों के संदर्भ में देखा, जैसे कि कार्य, अनुकूलन और विविधता। इस पुस्तक में स्पेंसर ने \'सर्वाइवल ऑफ द फिटेस्ट\' की अभिव्यक्ति \'अपने पर्यावरण के लिए सबसे उपयुक्त\' के अर्थ में पेश की।",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "21",
                    section: "18",
                    question_en: "21. Which of the following is a garrison town?",
                    question_hi: "21. निम्नलिखित में से कौन सा एक गैरिसन शहर है?",
                    options_en: [" Surat ", " Udaipur ", 
                                " Jalandhar ", " Kota"],
                    options_hi: [" सूरत", " उदयपुर",
                                " जालंधर", " कोटा"],
                    solution_en: "21.(c). Garrison towns are basically towns where military troops are permanently stationed. Two garrison (cantonment) towns of India - Ambala, Jalandhar.",
                    solution_hi: "21.(c). गैरीसन शहर मूल रूप से ऐसे शहर होते हैं जहां सैन्य सैनिक स्थायी रूप से तैनात होते हैं। भारत के दो गैरीसन (छावनी) शहर - अंबाला, जालंधर।",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "22",
                    section: "18",
                    question_en: "22. How many columns are there in a sheet of Excel 2010?",
                    question_hi: "22.एक्सेल 2010 की एक शीट में कितने कॉलम होते हैं?",
                    options_en: [" 16024", " 1024", 
                                " 16384", " 1600"],
                    options_hi: [" 16024", " 1024",
                                " 16384", " 1600"],
                    solution_en: "22.(c) From Excel 2007 onwards (2010, 2016, etc) we have exactly 10,48,576 rows and 16,384 columns.",
                    solution_hi: "22.(c) एक्सेल 2007 के बाद (2010, 2016 आदि) से हमारे पास ठीक 10,48,576 पंक्तियाँ और 16,384 कॉलम हैं।",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "23",
                    section: "18",
                    question_en: "23. Where are one-horned rhinoceros is found in India?",
                    question_hi: "23. भारत में एक सींग वाला गैंडा कहाँ पाया जाता है?",
                    options_en: [" Assam ", " Madhya Pradesh ", 
                                " Bihar ", " Meghalaya "],
                    options_hi: [" असम", " मध्य प्रदेश",
                                " बिहार", " मेघालय"],
                    solution_en: "23.(a) The Indian state of Assam is home to the largest population of greater-one horned rhinos, with more than 90% in Kaziranga National Park. Kaziranga National Park was inscribed on the World Heritage List in 1985.",
                    solution_hi: "23.(a) काजीरंगा राष्ट्रीय उद्यान में 90% से अधिक के साथ, भारतीय राज्य असम एक सींग वाले गैंडों की सबसे बड़ी आबादी का घर है। 1985 में काजीरंगा राष्ट्रीय उद्यान को विश्व विरासत सूची में अंकित किया गया था।",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "24",
                    section: "18",
                    question_en: "24. There were 12 boys who got trapped in a cave in June 2018. A huge exercise had to be carried out to rescue them. It became an internationally famous incident. Which country did this incident happen in?",
                    question_hi: "24. 12 लड़के थे जो जून 2018 में एक गुफा में फंस गए थे। उन्हें बचाने के लिए एक बड़ी कवायद करनी पड़ी। यह एक अंतरराष्ट्रीय स्तर पर प्रसिद्ध घटना बन गई। यह घटना किस देश में हुई थी?",
                    options_en: [" Thailand ", " Bhutan ", 
                                " Burma ", " Malaysia "],
                    options_hi: [" थाईलैंड", " भूटान",
                                " बर्मा", " मलेशिया"],
                    solution_en: "24.(a) There were 12 boys who got trapped in a cave in June 2018. A huge exercise had to be carried out to rescue them. It became an internationally famous incident. This incident happened in Thailand.",
                    solution_hi: "24.(a) 12 लड़के थे जो जून 2018 में एक गुफा में फंस गए थे। उन्हें बचाने के लिए एक बड़ी कवायद करनी पड़ी। यह अंतरराष्ट्रीय स्तर पर मशहूर घटना बन गई और यह घटना थाईलैंड में घटी थी।",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "25",
                    section: "18",
                    question_en: "25. As of 30th November 2020, how many countries are members of the International Monetary Fund (IMF)?",
                    question_hi: "25. 30 नवंबर 2020 तक, कितने देश अंतर्राष्ट्रीय मुद्रा कोष (IMF) के सदस्य हैं?",
                    options_en: [" 89", " 190", 
                                " 198", " 99"],
                    options_hi: [" 89", " 190",
                                " 198", " 99"],
                    solution_en: "25.(b) The International Monetary Fund (IMF) is an organization of 190 countries, working to foster global monetary cooperation, secure financial stability, facilitate international trade, promote high employment and sustainable economic growth, and reduce poverty around the world.",
                    solution_hi: "25.(b) अंतर्राष्ट्रीय मुद्रा कोष (IMF) 190 देशों का एक संगठन है, जो वैश्विक मौद्रिक सहयोग को बढ़ावा देने, वित्तीय स्थिरता को सुरक्षित करने, अंतर्राष्ट्रीय व्यापार को सुविधाजनक बनाने, उच्च रोजगार और सतत आर्थिक विकास को बढ़ावा देने और दुनिया भर में गरीबी को कम करने के लिए काम कर रहा है।",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "26",
                    section: "18",
                    question_en: "26.Which among the following is the largest unit of memory of a computer system?",
                    question_hi: "26.निम्नलिखित में से कौन कंप्यूटर सिस्टम की मेमोरी की सबसे बड़ी इकाई है?",
                    options_en: [" Exabyte ", " Yottabyte ", 
                                " Petabyte ", " Zetabyte "],
                    options_hi: [" एक्साबाइट", " योट्टाबाइट",
                                " पेटाबाइट", " ज़ेटाबाइट"],
                    solution_en: "26.(b) Zettabytes are used to measure large amounts of data and all the data in the world is just a few zettabytes. One yottabyte is equal to 1,000 zettabytes. It is the largest SI unit of memory measurement.",
                    solution_hi: "26.(b) ज़ेटाबाइट्स का उपयोग बड़ी मात्रा में डेटा को मापने के लिए किया जाता है और दुनिया में सभी डेटा केवल कुछ ज़ेटाबाइट्स हैं। एक योटाबाइट 1,000 ज़ेटाबाइट्स के बराबर होता है। यह मेमोरी मापन की सबसे बड़ी SI इकाई है।",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "27",
                    section: "18",
                    question_en: "27. Which traveller from Uzbekistan visited India in the 11th Century?",
                    question_hi: "27. 11वीं शताब्दी में उज्बेकिस्तान का कौन सा यात्री भारत आया था?",
                    options_en: [" Ibn Battuta ", " Al- Beruni ", 
                                " Mahmd Wali Balkhi ", " Seydi Ali Reis "],
                    options_hi: [" इब्न बतूता", " अल-बरुनी",
                                " महमद वली बल्खी", " सैयदी अली रीइस"],
                    solution_en: "27.(b) Al- Beruni travelled from Uzbekistan visited India in the 11th Century.In 1334, Ibn Battuta arrived in India all the way through the mountains of Afghanistan during the time of the Tughlaq dynasty.Seydi Ali Reis (1498–1563) was a Navigator and an Ottoman admiral.Mahmud Wali Balkhi (1626-31) years spent in India, from Balkh.",
                    solution_hi: "27.(b) उज्बेकिस्तान के अल-बरुनी यात्री ने 11वीं शताब्दी में भारत का दौरा किया। 1334 में, इब्न बतूता तुगलक वंश के समय में अफगानिस्तान के पहाड़ों से होते हुए भारत पहुंचे। सेदी अली रीस (1498-1563) एक नाविक और एक तुर्क नौसेना अध्यक्ष थे। महमूद वली बल्खी (1626-31) वर्ष भारत में बल्ख से व्यतीत हुए।",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "28",
                    section: "18",
                    question_en: "28. What is G-7?",
                    question_hi: "28. G-7 क्या है?",
                    options_en: [" 7 sister states of India ", " 7 seas of the world ", 
                                " 7 continents of the world ", " 7 IMF- described advanced economies in the world "],
                    options_hi: [" भारत के 7 बहन राज्य", " दुनिया के 7 समुद्र",
                                " विश्व के 7 महाद्वीप", " 7 IMF- दुनिया में उन्नत अर्थव्यवस्थाओं का वर्णन किया"],
                    solution_en: "28.(d) The Group of Seven (G-7) is an intergovernmental organization made up of the world\'s largest developed economies France, Germany, Italy, Japan, the United States, the United Kingdom, and Canada.",
                    solution_hi: "28.(d) सात का समूह (जी-7) दुनिया की सबसे बड़ी विकसित अर्थव्यवस्थाओं फ्रांस, जर्मनी, इटली, जापान, संयुक्त राज्य अमेरिका, यूनाइटेड किंगडम और कनाडा से बना एक अंतर-सरकारी संगठन है।",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "29",
                    section: "18",
                    question_en: "29. What is the name of the world’s smallest spacecraft launched by India?",
                    question_hi: "29. भारत द्वारा प्रक्षेपित दुनिया के सबसे छोटे अंतरिक्ष यान का नाम क्या है?",
                    options_en: [" Sparkle ", " Spring ", 
                                " Sprites ", " Star "],
                    options_hi: [" स्पार्कल ", " स्प्रिंग ",
                                " स्प्राइट ", " स्टार "],
                    solution_en: "29.(c) The world\'s smallest spacecraft ever launched have successfully been placed in low Earth orbit by ISRO\'s PSLV rocket. Named Sprites, the 3.5cmx3.5cm space probes weigh 4 grams each and run on sunlight.",
                    solution_hi: "29.(c) दुनिया के अब तक के सबसे छोटे अंतरिक्ष यान को ISRO के PSLV रॉकेट द्वारा पृथ्वी की निचली कक्षा में सफलतापूर्वक स्थापित किया गया है। नामांकित स्प्राइट्स, 3.5cmx3.5cm अंतरिक्ष जांच का वजन 4 ग्राम होता है और यह सूर्य के प्रकाश पर चलता है।",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "30",
                    section: "18",
                    question_en: "30. Who designed the ‘Statue of Unity’, Which was inaugurated by Prime Minister Narendra Modi on 31st October 2018?",
                    question_hi: "30. स्टैच्यू ऑफ यूनिटी ’को किसने डिजाइन किया था, जिसका उद्घाटन 31 अक्टूबर 2018 को प्रधान मंत्री नरेंद्र मोदी ने किया था?",
                    options_en: [" Krishna Reddy ", " Ram Vanji Sutar ", 
                                " S.N. Ganapathy ", " Ramkinkar Baij "],
                    options_hi: [" कृष्णा रेड्डी", " राम वनजी सुतार",
                                " एस.एन. गणपति", " रामकिंकर बैज"],
                    solution_en: "30.(b) The ‘Statue of Unity’, It was designed by Indian sculptor Ram V. Sutar was inaugurated by Indian Prime Minister Narendra Modi on 31 October 2018,Height is 182m.The statue has been built as an ode to the Iron Man of India, Sardar Vallabhbhai Patel, the first home minister of independent India.",
                    solution_hi: "30.(b) \'स्टैच्यू ऑफ यूनिटी\', इसे भारतीय मूर्तिकार राम वी. सुतार द्वारा डिजाइन किया गया था, जिसका उद्घाटन भारतीय प्रधान मंत्री नरेंद्र मोदी ने 31 अक्टूबर 2018 को किया था, ऊंचाई 182 मीटर है। प्रतिमा को भारत के लौह पुरुष, सरदार वल्लभ भाई पटेल, के लिए एक स्मारक के रूप में बनाया गया है। सरदार वल्लभभाई पटेल स्वतंत्र भारत के पहले गृह मंत्री थे।",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "31",
                    section: "18",
                    question_en: "31. In which city of India did the East India Company set up their first trading center?",
                    question_hi: "31. ईस्ट इंडिया कंपनी ने भारत के किस शहर में अपना पहला व्यापारिक केंद्र स्थापित किया था?",
                    options_en: [" Delhi ", " Mumbai ", 
                                " Ahmedabad ", " Surat "],
                    options_hi: [" दिल्ली", " मुंबई",
                                " अहमदाबाद", " सूरत"],
                    solution_en: "31.(d) By January 1613, the first East India Company factory had come up at Surat.East India company landed in the Indian subcontinent on August 24, 1608, at the port of Surat. Mughal emperor Jahangir granted a farman to Captain William Hawkins permitting the English to erect a factory at Surat in 1613.",
                    solution_hi: "31.(d) जनवरी 1613 तक, पहली ईस्ट इंडिया कंपनी का कारखाना सूरत में आ गया था। ईस्ट इंडिया कंपनी 24 अगस्त, 1608 को सूरत के बंदरगाह पर भारतीय उपमहाद्वीप में उतरी। मुगल बादशाह जहांगीर ने कैप्टन विलियम हॉकिन्स को 1613 में सूरत में एक कारखाना लगाने के लिए अंग्रेजों को अनुमति देने के लिए एक फरमान दिया था।",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "32",
                    section: "18",
                    question_en: "32.World Bamboo Day is celebrated every year on _________.",
                    question_hi: "32.विश्व बांस दिवस हर साल कब मनाया जाता है?",
                    options_en: [" 30th December ", " 10th March ", 
                                " 10th July ", " 18th September "],
                    options_hi: [" 30 दिसंबर", " 10 मार्च",
                                " 10 जुलाई", " 18 सितंबर"],
                    solution_en: "32.(d) Every year on September 18, World Bamboo Day is marked as a day of celebration to raise global awareness of bamboo. Bambusa vulgaris, common bamboo, is an open-clump type bamboo species.",
                    solution_hi: "32.(d) हर साल 18 सितंबर को विश्व बांस दिवस को बांस के बारे में वैश्विक जागरूकता बढ़ाने के उत्सव के रूप में मनाया जाता है। बम्बुसा वल्गरिस, आम बांस, एक खुले-क्लंप प्रकार की बांस की प्रजाति है।",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "33",
                    section: "18",
                    question_en: "33.The east coast of India is a:",
                    question_hi: "33.भारत का पूर्वी तट ________है",
                    options_en: [" coast with fjords ", " low sedimentary coast ", 
                                " concordant coastline ", " rocky retreating coast "],
                    options_hi: [" फ़्योर्ड के साथ समुद्रतट  ", " निम्न तलछट तट",
                                " समवर्ती तटरेखा", " चट्टानी प्रतिगमन तट"],
                    solution_en: "33.(b) The east coast of India is a low sedimentary coast. Along with low sedimentary coasts, the rivers appear to extend their length by building coastal plains and deltas.",
                    solution_hi: "33.(b) भारत का पूर्वी तट एक निम्न अवसादी तट है। निम्न तलछटी तटों के साथ, नदियाँ तटीय मैदानों और डेल्टाओं का निर्माण करके अपनी लंबाई बढ़ाती प्रतीत होती हैं।",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "34",
                    section: "18",
                    question_en: "34. When did the Central Industrial Security Force come into existence in India?",
                    question_hi: "34.भारत में केंद्रीय औद्योगिक सुरक्षा बल कब अस्तित्व में आया?",
                    options_en: [" 1989", " 1990", 
                                " 1970", " 1969"],
                    options_hi: [" 1989", " 1990",
                                " 1970", " 1969"],
                    solution_en: "34.(d) The Central Industrial Security Force (CISF) (established in its present form: 15 June 1983) is a Central Armed Police Forces in India. It was set up under an Act of the Parliament of India on 10 March 1969.The CISF is governed by the Union Ministry of Home Affairs, and its headquarters are at New Delhi.",
                    solution_hi: "34.(d)  केंद्रीय औद्योगिक सुरक्षा बल (CISF) (अपने वर्तमान स्वरूप में स्थापित: 15 जून 1983) भारत में एक केंद्रीय सशस्त्र पुलिस बल है। यह 10 मार्च 1969 को भारत की संसद के एक अधिनियम के तहत स्थापित किया गया था। CISF केंद्रीय गृह मंत्रालय द्वारा शासित है, और इसका मुख्यालय नई दिल्ली में है।",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "35",
                    section: "18",
                    question_en: "35. Which of the following is a part of ‘global commons’?",
                    question_hi: "35.निम्नलिखित में से कौन \'ग्लोबल कॉमन्स\' का हिस्सा है?",
                    options_en: [" Asia ", " Africa ", 
                                " Antarctica ", " Australia "],
                    options_hi: [" एशिया", " अफ्रीका",
                                " अंटार्कटिका", " ऑस्ट्रेलिया"],
                    solution_en: "35.(c) International law defines traditionally five global commons: high seas, the deep-sea bed, the atmosphere, Antarctica and Outer Space.",
                    solution_hi: "35.(c) अंतर्राष्ट्रीय कानून पारंपरिक रूप से पांच वैश्विक कॉमन्स को परिभाषित करता है: उच्च समुद्र, गहरे समुद्र का तल, वातावरण, अंटार्कटिका और बाहरी अंतरिक्ष।",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "36",
                    section: "18",
                    question_en: "36. In which year was the British East India Company formed?",
                    question_hi: "36. ब्रिटिश ईस्ट इंडिया कंपनी की स्थापना किस वर्ष हुई थी?",
                    options_en: [" 1700", " 1650", 
                                " 1600", " 1750"],
                    options_hi: [" 1700", " 1650",
                                " 1600", " 1750"],
                    solution_en: "36.(c) In 1600, a group of English businessmen asked Elizabeth I for a royal charter that would let them voyage to the East Indies on behalf of the crown in exchange for a monopoly on trade.",
                    solution_hi: "36(c) 1600 में, अंग्रेजी व्यापारियों के एक समूह ने एलिजाबेथ I से एक शाही चार्टर के लिए कहा जो उन्हें व्यापार पर एकाधिकार के बदले में ताज की ओर से ईस्ट इंडीज की यात्रा करने देगा।",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "37",
                    section: "18",
                    question_en: "37. Congress Party observed 26th January 1930 as:",
                    question_hi: "37. कांग्रेस पार्टी ने 26 जनवरी 1930 को मनाया जाता है?",
                    options_en: [" Republic Day ", " Independence Day ", 
                                " Bharat Diwas ", " Silent Protest Day "],
                    options_hi: [" गणतंत्र दिवस", " स्वतंत्रता दिवस",
                                " भारत दिवस", " मौन विरोध दिवस"],
                    solution_en: "37.(b) On December 31, 1929, Nehru hoisted the tricolour on the banks of the Ravi river and demanded “Poorna Swaraj” or complete self-rule, and the date set for independence was January 26, 1930. ",
                    solution_hi: "37.(b) 31 दिसंबर, 1929 को, नेहरू ने रावी नदी के तट पर तिरंगा फहराया और \"पूर्ण स्वराज\" या पूर्ण स्व-शासन की मांग की, और स्वतंत्रता के लिए निर्धारित तिथि 26 जनवरी, 1930 थी।",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "38",
                    section: "18",
                    question_en: "38. In which Indian city was the first successful Cotton mill set up?",
                    question_hi: "38.भारत के किस शहर में पहली सफल कपास मिल स्थापित की गई थी?",
                    options_en: [" Kolkata", " Mumbai ", 
                                " Chennai ", " Patna "],
                    options_hi: [" कोलकाता", " मुंबई",
                                " चेन्नई", " पटना"],
                    solution_en: "38.(b) The first cotton mill in India was established in 1818 at Fort Gloster near Kolkata but was a commercial failure. The second cotton mill in India was established by KGN Daber in 1854 and was named Bombay(Mumbai) Spinning and Weaving Company.",
                    solution_hi: "38.(b) भारत में पहली कपास मिल 1818 में कोलकाता के पास फोर्ट ग्लोस्टर में स्थापित की गई थी, लेकिन एक व्यावसायिक विफलता थी। भारत में दूसरी कपास मिल 1854 में केजीएन डाबर द्वारा स्थापित की गई थी और इसका नाम बॉम्बे (मुंबई) स्पिनिंग एंड वीविंग कंपनी रखा गया था।",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "39",
                    section: "18",
                    question_en: "39. Where was the Muslim League founded?",
                    question_hi: "39. मुस्लिम लीग की स्थापना कहाँ हुई थी?",
                    options_en: [" Lucknow ", " Kanpur ", 
                                " Dhaka ", " Bihar "],
                    options_hi: [" लखनऊ", " कानपुर",
                                " ढाका", " बिहार"],
                    solution_en: "39.(c) Muslim League founded on 30 December 1906 at Dacca, British India (now Dhaka, Bangladesh), headquarter is at Lucknow. Khwaja salimullah was the founder of muslim league. ",
                    solution_hi: "39.(c) मुस्लिम लीग की स्थापना 30 दिसंबर 1906 को ढाका, ब्रिटिश भारत (अब ढाका, बांग्लादेश) में हुई, जिसका मुख्यालय लखनऊ में है। ख्वाजा सलीमुल्लाह मुस्लिम लीग के संस्थापक थे।",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "40",
                    section: "misc",
                    question_en: "40. Origin of State Bank of India (SBI) goes back to the year:",
                    question_hi: "40. भारतीय स्टेट बैंक (SBI) की उत्पत्ति वर्ष में वापस जाती है",
                    options_en: [" 1806", " 1951", 
                                " 1948", " 1851"],
                    options_hi: [" 1806", " 1951",
                                " 1948", " 1851"],
                    solution_en: "40.(a) The origin of the State Bank of India goes back to the first decade of the nineteenth century with the establishment of the Bank of Calcutta in Calcutta on 2 June 1806. The Government of India nationalized the Imperial Bank of India in the year 1955 with the Reserve Bank of India taking a 60% stake and name was changed to State Bank of India.",
                    solution_hi: "40.(a) भारतीय स्टेट बैंक की उत्पत्ति उन्नीसवीं शताब्दी के पहले दशक में 2 जून 1806 को कलकत्ता में बैंक ऑफ कलकत्ता की स्थापना के साथ हुई। भारत सरकार ने वर्ष 1955 में रिजर्व बैंक के साथ इंपीरियल बैंक ऑफ इंडिया का राष्ट्रीयकरण किया। बैंक ऑफ इंडिया ने 60% हिस्सेदारी ली और नाम बदलकर स्टेट बैंक ऑफ इंडिया कर दिया गया।",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>