<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">10:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 10</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">10</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 10 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["31"] = {
                name: "Computer Knowledge",
                start: 0,
                end: 8
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="31">Computer Knowledge</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 9,
                end: 9
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "31",
                    question_en: "<p>1. Which of the following is a commonly used word processing package?</p>",
                    question_hi: "<p>1. निम्नलिखित में से कौन-सा सामान्यतः इस्तेमाल किया जाने वाला वर्ड प्रोसेसिंग पैकेज है?</p>",
                    options_en: ["<p>Microsoft Word</p>", "<p>Microsoft Excel</p>", 
                                "<p>Google Chrome</p>", "<p>Adobe Photoshop</p>"],
                    options_hi: ["<p>माइक्रोसॉफ्ट वर्ड</p>", "<p>माइक्रोसॉफ्ट एक्सेल</p>",
                                "<p>गूगल क्रोम</p>", "<p>एडोब फोटोशॉप</p>"],
                    solution_en: "<p>1.(a) <strong>Microsoft Word - </strong>A word processor software developed by Microsoft in 1983. Adobe Photoshop - A software that is extensively used for raster image editing, graphic design and digital art. Microsoft Excel enables users to format, organize and calculate data in a spreadsheet. Google Chrome is internet browser.</p>",
                    solution_hi: "<p>1.(a) <strong>माइक्रोसॉफ्ट वर्ड-</strong> 1983 में माइक्रोसॉफ्ट द्वारा विकसित एक वर्ड प्रोसेसर सॉफ़्टवेयर है। एडोब फोटोशॉप (Adobe Photoshop)- एक ऐसा सॉफ्टवेयर है जिसका उपयोग रैस्टर इमेज एडिटिंग (raster image editing), ग्राफिक डिजाइन और डिजिटल आर्ट के लिए बड़े पैमाने पर किया जाता है। माइक्रोसॉफ्ट एक्सेल, उपयोगकर्ताओं को स्प्रेडशीट में डेटा को प्रारूपित करने, व्यवस्थित करने और गणना करने में सक्षम बनाता है। गूगल क्रोम (Google Chrome) एक इंटरनेट ब्राउज़र है।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "2",
                    section: "31",
                    question_en: "<p>2. In MS Excel 365, in which of the following Ribbon tabs does the add rows option reside in MS Excel 365?</p>",
                    question_hi: "<p>2. एमएस एक्सेल 365 (MS Excel 365) में, निम्नलिखित में से किस रिबन टैब में पंक्तियाँ (rows) ऐड करने का विकल्प होता है?</p>",
                    options_en: ["<p>Home <math display=\"inline\"><mo>&#8594;</mo><mi>&#160;</mi></math>Insert</p>", "<p>Home <math display=\"inline\"><mo>&#8594;</mo></math> File</p>", 
                                "<p>Home <math display=\"inline\"><mo>&#8594;</mo><mi>&#160;</mi></math>Edit</p>", "<p>Home <math display=\"inline\"><mo>&#8594;</mo></math> Format</p>"],
                    options_hi: ["<p>Home <math display=\"inline\"><mo>&#8594;</mo><mi>&#160;</mi></math>Insert</p>", "<p>Home <math display=\"inline\"><mo>&#8594;</mo></math> File</p>",
                                "<p>Home <math display=\"inline\"><mo>&#8594;</mo><mi>&#160;</mi></math>Edit</p>", "<p>Home <math display=\"inline\"><mo>&#8594;</mo></math> Format</p>"],
                    solution_en: "<p>2.(a) <strong>Home &rarr;</strong><strong>&nbsp;Insert.</strong> In MS Word 365, Home tab consists of Clipboard, font, paragraph, proofing, editing, voice. Insert tab consists of Pages, tables, picture, illustration, links, bookmarks, media, comments, header and footer, symbols, emojis.</p>",
                    solution_hi: "<p>2.(a) <strong>Home</strong> <strong>&rarr;</strong>&nbsp;<strong>Insert </strong>। एमएस वर्ड (MS Word) 365 के होम टैब में क्लिपबोर्ड, फॉन्ट, पैराग्राफ, प्रूफिंग, एडिटिंग, वॉयस (voice) शामिल होते हैं। इन्सर्ट टैब (Insert tab) में पेज, टेबल, पिक्चर, इलुस्ट्रेशन (illustration), लिंक, बुकमार्क, मीडिया, कमेंट्स, हेडर एवं फ़ुटर , सिंबल्स, एमोजिस शामिल हैं।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "3",
                    section: "31",
                    question_en: "<p>3. Which of the following CANNOT be the default font size in the font-menu dropdown list in MS Word 365 in Windows 10?</p>",
                    question_hi: "<p>3. विंडोज़ 10 में एमएस वर्ड 365 में, फ़ॉन्ट-मेनू ड्रॉपडाउन लिस्ट में निम्नलिखित में से कौन-सा डिफ़ॉल्ट फ़ॉन्ट साइज़ नहीं हो सकता है?</p>",
                    options_en: ["<p>10</p>", "<p>11</p>", 
                                "<p>9</p>", "<p>13</p>"],
                    options_hi: ["<p>10</p>", "<p>11</p>",
                                "<p>9</p>", "<p>13</p>"],
                    solution_en: "<p>3.(d) <strong>13.</strong> Default font size in MS Word 365 is (8, 9, 10, 11, 12, 14, 16, 18, 20, 24, 26, 28, 36, 48, 72).</p>",
                    solution_hi: "<p>3.(d) <strong>13.</strong> MS Word 365 में डिफ़ॉल्ट फ़ॉन्ट आकार (8, 9, 10, 11, 12, 14, 16, 18, 20, 24, 26, 28, 36, 48, 72) है।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "4",
                    section: "31",
                    question_en: "4. Which technology is commonly used for sending and receiving electronic mail?",
                    question_hi: "4.  इलेक्ट्रॉनिक मेल के प्रेषण तथा प्राप्ति हेतु सामान्यतः किस प्रौद्योगिकी का उपयोग किया जाता है?",
                    options_en: [" Telephony", " Postal services", 
                                " Fax machines", " Internet and email protocols"],
                    options_hi: [" टेलीफ़ोनी", " डाक सेवाओं",
                                " फैक्स मशीन", " इंटरनेट और ईमेल प्रोटोकॉल"],
                    solution_en: "<p>4.(d) <strong>Internet and email protocols.</strong> The common protocols for email delivery are Post Office Protocol (POP), Internet Message Access Protocol (IMAP), and Simple Mail Transfer Protocol (SMTP). Telephony is technology associated with interactive communication between two or more physically distant parties via the electronic transmission of data.</p>",
                    solution_hi: "<p>4.(d) <strong>इंटरनेट और ईमेल प्रोटोकॉल।</strong> ईमेल डिलीवरी के लिए सामान्य प्रोटोकॉल पोस्ट ऑफिस प्रोटोकॉल (POP), इंटरनेट मैसेज एक्सेस प्रोटोकॉल (IMAP), और सिंपल मेल ट्रांसफर प्रोटोकॉल (SMTP) हैं। टेलीफ़ोनी (Telephony) डेटा के इलेक्ट्रॉनिक प्रसारण (electronic transmission) के माध्यम से दो या दो से अधिक भौतिक रूप से दूर स्थित पक्षों के बीच इंटरैक्टिव (interactive) संचार से जुड़ी तकनीक है।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "5",
                    section: "31",
                    question_en: "<p>5. In MS Excel 365, which of the following is the shortcut key in MS Excel to print a spreadsheet?</p>",
                    question_hi: "<p>5. एमएस एक्सेल 365 (MS Excel 365) में, एमएस एक्सेल (MS Excel) स्प्रेडशीट (spreadsheet) को प्रिंट करने के लिए की-बोर्ड शॉर्टकट निम्नलिखित में से क्या होता है?</p>",
                    options_en: ["<p>Alt +1</p>", "<p>Ctrl +1</p>", 
                                "<p>Ctrl+P</p>", "<p>Alt + P</p>"],
                    options_hi: ["<p>Alt +1</p>", "<p>Ctrl +1</p>",
                                "<p>Ctrl + P</p>", "<p>Alt + P</p>"],
                    solution_en: "<p>5.(c) <strong>Ctrl + P.</strong> One more print shortcut to print a spreadsheet - Ctrl + Alt + Shift + F2.</p>",
                    solution_hi: "<p>5.(c) <strong>Ctrl + P.</strong> स्प्रेडशीट प्रिंट करने के लिए एक और प्रिंट शॉर्टकट - Ctrl + Alt + Shift + F2</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "6",
                    section: "31",
                    question_en: "6. Which of the following is/are parts of the standard Structure of Electronic mail?",
                    question_hi: "6. निम्नलिखित में से कौन इलेक्ट्रॉनिक मेल (Electronic mail) की मानक संरचना का हिस्सा है?",
                    options_en: [" Both Header and Body", " Only Body", 
                                " Neither Header nor Body", " Only Header"],
                    options_hi: [" हेडर और बॉडी दोनों", " केवल बॉडी ",
                                " न तो हेडर और न ही बॉडी", " केवल हेडर"],
                    solution_en: "<p>6.(a) <strong>Both Header and Body. </strong>Parts of Email - Sender/from, The subject line, The pre-header, The salutation, The email body, Closing line, Signature, Call to Action (CTA), Attachments.</p>",
                    solution_hi: "<p>6.(a) <strong>हेडर और बॉडी दोनों।</strong> ईमेल के भाग - सेन्डर /फ्रॉम (sender/ from) , विषय पंक्ति (subject line), प्री-हेडर (pre-header), अभिवादन (salutation), ईमेल का मुख्य भाग (email body), समापन पंक्ति (closing line), हस्ताक्षर (signature), कॉल टू एक्शन (Call to Action), अनुलग्नक (attachments) ।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "7",
                    section: "31",
                    question_en: "<p>7. The group of buttons or icons below the menu bar in a word processing software are called:</p>",
                    question_hi: "<p>7. वर्ड प्रोसेसिंग (word processing) सॉफ्टवेयर में मेनू बार के नीचे बटन या आइकन के समूह को ________कहा जाता है।</p>",
                    options_en: ["<p>status bars</p>", "<p>dialog boxes</p>", 
                                "<p>toolbars</p>", "<p>menus</p>"],
                    options_hi: ["<p>स्टेटस बार (status bars)</p>", "<p>डायलॉग बॉक्स (dialog boxes)</p>",
                                "<p>टूलबार (toolbars)</p>", "<p>मेनू (menus)</p>"],
                    solution_en: "<p>7.(c) <strong>Toolbars:</strong> These are horizontal or vertical rows of buttons or icons that provide shortcuts to frequently used functions in a software program. Status bars: These are located at the bottom of the window and display information about the current state of the document or program. Dialog boxes: These are pop-up windows that appear in response to a user action and require input or confirmation to complete a specific task.</p>",
                    solution_hi: "<p>7.(c) <strong>टूलबार: </strong>ये बटनों या आइकनों की क्षैतिज या ऊर्ध्वाधर पंक्तियाँ हैं जो किसी सॉफ़्टवेयर प्रोग्राम में अक्सर उपयोग किए जाने वाले फ़ंक्शंस के लिए शॉर्टकट प्रदान करती हैं। स्टेटस बार (status bar): ये विंडो के नीचे स्थित होते हैं और दस्तावेज़ या प्रोग्राम की वर्तमान स्थिति के बारे में जानकारी प्रदर्शित करते हैं। डायलॉग बॉक्स (Dialogue box) : ये पॉप-अप विंडो हैं जो उपयोगकर्ता की कार्रवाई के जवाब में दिखाई देते हैं और किसी विशिष्ट कार्य को पूरा करने के लिए इनपुट या पुष्टि की आवश्यकता होती है।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "8",
                    section: "31",
                    question_en: "<p>8. What is the full form of NetBIOS?</p>",
                    question_hi: "<p>8. NetBIOS का पूर्ण रूप क्या है?</p>",
                    options_en: ["<p>Network Basic Input/Output System</p>", "<p>Network Based Input / Output System</p>", 
                                "<p>Networking Binary Input/Output System</p>", "<p>Net Binary In/Out System</p>"],
                    options_hi: ["<p>Network Basic Input / Output System</p>", "<p>Network Based Input / Output System</p>",
                                "<p>Networking Binary Input/Output System</p>", "<p>Net Binary In / Out System</p>"],
                    solution_en: "<p>8.(a) <strong>Network Basic Input/Output System -</strong> It enables computer communication over a LAN and the sharing of files and printers.</p>",
                    solution_hi: "<p>8.(a) <strong>Network Basic Input / Output System -</strong> यह एक LAN पर कंप्यूटर संचार और फ़ाइलों (files) और प्रिंटरों (printers) को साझा करने में सक्षम बनाता है।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "9",
                    section: "31",
                    question_en: "<p>9. In MS Excel 365, Which of the following shortcut keys is used to save a workbook in MS Excel ?</p>",
                    question_hi: "<p>9. एमएस एक्सेल 365 (MS Excel 365) में, एमएस एक्सेल (MS Excel) में वर्कबुक को सेव करने के लिए निम्नलिखित में से किस शॉर्टकट कुंजी (keys) का उपयोग किया जाता है?</p>",
                    options_en: ["<p>F6</p>", "<p>Ctrl + A</p>", 
                                "<p>Ctrl + S</p>", "<p>F1</p>"],
                    options_hi: ["<p>F6</p>", "<p>Ctrl + A</p>",
                                "<p>Ctrl + S</p>", "<p>F1</p>"],
                    solution_en: "<p>9.(c) <strong>Ctrl + S.</strong> F6: This function key typically navigates between different panes or elements within a program\'s window. Ctrl + A: This shortcut selects all contents of the current worksheet. F1: This function key usually opens the program\'s help menu or context-sensitive help.</p>",
                    solution_hi: "<p>9.(c) <strong>Ctrl + S.</strong> F6: यह फ़ंक्शन कुंजी आम तौर पर प्रोग्राम की विंडो के भीतर विभिन्न पैन या तत्वों के बीच नेविगेट करती है। Ctrl + A: यह शॉर्टकट वर्तमान वर्कशीट की सभी सामग्री (content) का चयन करता है। F1: यह फ़ंक्शन कुंजी आमतौर पर प्रोग्राम के सहायता मेनू या संदर्भ-संवेदनशील सहायता (help) को खोलती है।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "10",
                    section: "misc",
                    question_en: "10. Which of the following is the correct shortcut key used to increase paragraph indention in MS Word 365?",
                    question_hi: "<p>10. एमएस वर्ड 365 (MS Word 365) में पैराग्राफ इंडेंटेशन को बढ़ाने के लिए निम्नलिखित में से किस शॉर्टकट कुंजी (shortcut key) का उपयोग किया जाता है?</p>",
                    options_en: [" Ctrl + L", "<p>Ctrl + Tab</p>", 
                                "<p>Ctrl + M</p>", "<p>Ctrl + I</p>"],
                    options_hi: ["<p>Ctrl + L</p>", "<p>Ctrl + Tab</p>",
                                "<p>Ctrl + M</p>", "<p>Ctrl + I</p>"],
                    solution_en: "<p>10.(c) <strong>Ctrl + M.</strong> Indentation refers to the amount of space added at the beginning of a line of text, paragraph, or list item. It\'s a formatting technique used to: Improve Readability, Organize Content, Create Visual Style. Ctrl + L - Left Align. Ctrl + I - Italic. Ctrl + B - Bold. Ctrl + U - Underline.</p>",
                    solution_hi: "<p>10.(c) <strong>Ctrl + M.</strong> इंडेंटेशन से तात्पर्य टेक्स्ट, पैराग्राफ या सूची आइटम की एक पंक्ति की शुरुआत में जोड़े गए स्थान की मात्रा से है। यह एक फ़ॉर्मेटिंग तकनीक है जिसका उपयोग किया जाता है: पठनीयता में सुधार (Improve Readability), सामग्री को व्यवस्थित करना (Organize Content), दृश्य शैली बनाना (Create Visual Style)। Ctrl + L - लेफ्ट-एलाइन (Left Align)। Ctrl + I - इटैलिक (Italic)। Ctrl + B -बोल्ड (Bold)। Ctrl + U - अंडरलाइन (Underline)।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>