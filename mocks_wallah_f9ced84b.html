<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">9:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 25</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">25</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 9 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["7"] = {
                name: "English",
                start: 0,
                end: 23
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="7">English</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 24,
                end: 24
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "7",
                    question_en: "<p>1. <span style=\"font-family: Cambria Math;\">Select the option that expresses the given sentence in active voice.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">The car was being washed by me.</span></p>\n",
                    question_hi: "<p>1. <span style=\"font-family: Cambria Math;\">Select the option that expresses the given sentence in active voice.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">The car was being washed by me.</span></p>\n",
                    options_en: ["<p>I had been washing the car.</p>\n", "<p>I was washing the car.</p>\n", 
                                "<p>I did the washing car.</p>\n", "<p>I washed the car.</p>\n"],
                    options_hi: ["<p>I had been washing the car.</p>\n", "<p>I was washing the car.</p>\n",
                                "<p>I did the washing car.</p>\n", "<p>I washed the car.</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">1.(b)</span></p>\r\n<p><span style=\"font-weight: 400;\">I </span><span style=\"text-decoration: underline;\"><span style=\"font-weight: 400;\">had been washing</span></span><span style=\"font-weight: 400;\"> the car. (Incorrect Tense)</span></p>\r\n<p><span style=\"font-weight: 400;\">I was washing the car. (Correct)</span></p>\r\n<p><span style=\"font-weight: 400;\">I </span><span style=\"text-decoration: underline;\"><span style=\"font-weight: 400;\">did</span></span><span style=\"font-weight: 400;\"> the washing car. (Incorrect Verb)</span></p>\r\n<p><span style=\"font-weight: 400;\">I </span><span style=\"text-decoration: underline;\"><span style=\"font-weight: 400;\">washed</span></span><span style=\"font-weight: 400;\"> the car. (Incorrect Tense)</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">1.(b)</span></p>\r\n<p><span style=\"font-weight: 400;\">I </span><span style=\"text-decoration: underline;\"><span style=\"font-weight: 400;\">had been washing</span></span><span style=\"font-weight: 400;\"> the car. (Incorrect Tense)</span></p>\r\n<p><span style=\"font-weight: 400;\">I was washing the car. (Correct)</span></p>\r\n<p><span style=\"font-weight: 400;\">I </span><span style=\"text-decoration: underline;\"><span style=\"font-weight: 400;\">did</span></span><span style=\"font-weight: 400;\"> the washing car. (Incorrect Verb)</span></p>\r\n<p><span style=\"font-weight: 400;\">I </span><span style=\"text-decoration: underline;\"><span style=\"font-weight: 400;\">washed</span></span><span style=\"font-weight: 400;\"> the car. (Incorrect Tense)</span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "7",
                    question_en: "<p>2. <span style=\"font-family: Cambria Math;\">The following sentence has been split into four segments. Identify the segment that </span><span style=\"font-family: Cambria Math;\">contains a grammatical error.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Whenever he is / on a holiday, / he travels by foot / to the temple nearby.</span></p>\n",
                    question_hi: "<p>2. <span style=\"font-family: Cambria Math;\">The following sentence has been split into four segments. Identify the segment that </span><span style=\"font-family: Cambria Math;\">contains a grammatical error.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Whenever he is / on a holiday, / he travels by foot / to the temple nearby.</span></p>\n",
                    options_en: ["<p>to the temple nearby</p>\n", "<p>Whenever he is</p>\n", 
                                "<p>he travels by foot</p>\n", "<p>on a holiday</p>\n"],
                    options_hi: ["<p>to the temple nearby</p>\n", "<p>Whenever he is</p>\n",
                                "<p>he travels by foot</p>\n", "<p>on a holiday</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">2.(c)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">There is a prepositional error in the given sentence. The preposition &lsquo;by&rsquo; must be replaced with &lsquo;on&rsquo; because whenever we go somewhere using our foot we use the preposition &lsquo;on&rsquo; before &lsquo;foot&rsquo;. Hence, &lsquo;he travels on foot&rsquo; is the most appropriate answer.</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">2.(c)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">There is a prepositional error in the given sentence. The preposition &lsquo;by&rsquo; must be replaced with &lsquo;on&rsquo; because whenever we go somewhere using our foot we use the preposition &lsquo;on&rsquo; before &lsquo;foot&rsquo;. Hence, &lsquo;he travels on foot&rsquo; is the most appropriate answer.</span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "7",
                    question_en: " <p>3.</span><span style=\"font-family:Cambria Math\"> Select the most appropriate meaning of the given idiom.</span></p> <p><span style=\"font-family:Cambria Math\">Lion’s share</span></p>",
                    question_hi: " <p>3.</span><span style=\"font-family:Cambria Math\"> Select the most appropriate meaning of the given idiom.</span></p> <p><span style=\"font-family:Cambria Math\">Lion’s share</span></p>",
                    options_en: [" <p> Largest share</span></p>", " <p> Animal’s share</span></p>", 
                                " <p> Limited share</span></p>", " <p> Forceful share</span></p>"],
                    options_hi: [" <p> Largest share</span></p>", " <p> Animal’s share</span></p>",
                                " <p> Limited share</span></p>", " <p> Forceful share</span></p>"],
                    solution_en: " <p><span style=\"font-family:Cambria Math\">3.(a)</span></p> <p><span style=\"font-family:Cambria Math\">Lion’s share- largest share</span></p> <p><span style=\"font-family:Cambria Math\">E.g.- Mr. Roy demanded the lion’s share in the company’s shares at the meeting.</span></p>",
                    solution_hi: " <p><span style=\"font-family:Cambria Math\">3.(a)</span></p> <p><span style=\"font-family:Cambria Math\">Lion’s share- largest share</span></p> <p><span style=\"font-family:Cambria Math\">E.g.- Mr. Roy demanded the lion’s share in the company’s shares at the meeting.</span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "7",
                    question_en: "<p>4.<span style=\"font-family: Cambria Math;\"> Select the most appropriate option to fill in the blank.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">If you had consulted me, I would have ________ you.</span></p>\n",
                    question_hi: "<p>4.<span style=\"font-family: Cambria Math;\"> Select the most appropriate option to fill in the blank.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">If you had consulted me, I would have ________ you.</span></p>\n",
                    options_en: ["<p>advising</p>\n", "<p>advises</p>\n", 
                                "<p>advised</p>\n", "<p>advise</p>\n"],
                    options_hi: ["<p>advising</p>\n", "<p>advises</p>\n",
                                "<p>advised</p>\n", "<p>advise</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">4.(c)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&ldquo;Had + </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>V</mi><mn>3</mn></msub></math><span style=\"font-family: Cambria Math;\">(third form of the verb)&hellip;&hellip;.would have + </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>V</mi><mn>3</mn></msub></math><span style=\"font-family: Cambria Math;\">&rdquo; is the correct grammatical structure for the given sentence. Hence, &lsquo;advised(</span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>V</mi><mn>3</mn></msub></math><span style=\"font-family: Cambria Math;\">)&rsquo; is the most appropriate answer.</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">4.(c)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&ldquo;Had + </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>V</mi><mn>3</mn></msub></math><span style=\"font-family: Cambria Math;\">(third form of the verb)&hellip;&hellip;.would have + </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>V</mi><mn>3</mn></msub></math><span style=\"font-family: Cambria Math;\">&rdquo; is the correct grammatical structure for the given sentence. Hence, &lsquo;advised(</span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>V</mi><mn>3</mn></msub></math><span style=\"font-family: Cambria Math;\">)&rsquo; is the most appropriate answer.</span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "7",
                    question_en: "<p>5. <span style=\"font-family: Cambria Math;\">Select the option that expresses the given sentence in an active voice.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">All the electric poles in the suburbs were destroyed by the cyclone last year.</span></p>\n",
                    question_hi: "<p>5. <span style=\"font-family: Cambria Math;\">Select the option that expresses the given sentence in an active voice.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">All the electric poles in the suburbs were destroyed by the cyclone last year.</span></p>\n",
                    options_en: ["<p>The cyclone destroyed all the electric poles in the suburbs last year.</p>\n", "<p>The cyclone was being destroyed with all the electric poles in the suburbs last year.</p>\n", 
                                "<p>The cyclone was destroyed by the all the electric poles in the suburbs last year.</p>\n", "<p>The cyclone destroyed all the suburbs by the electric poles last year.</p>\n"],
                    options_hi: ["<p>The cyclone destroyed all the electric poles in the suburbs last year.</p>\n", "<p>The cyclone was being destroyed with all the electric poles in the suburbs last year.</p>\n",
                                "<p>The cyclone was destroyed by the all the electric poles in the suburbs last year.</p>\n", "<p>The cyclone destroyed all the suburbs by the electric poles last year.</p>\n"],
                    solution_en: "<p><span style=\"font-weight: 400;\">The cyclone destroyed all the electric poles in the suburbs last year. (Correct)</span></p>\r\n<p><span style=\"font-weight: 400;\">The cyclone </span><span style=\"text-decoration: underline;\"><span style=\"font-weight: 400;\">was being destroyed</span></span><span style=\"font-weight: 400;\"> with all the electric poles in the suburbs last year. (Incorrect Tense)</span></p>\r\n<p><span style=\"font-weight: 400;\">The cyclone </span><span style=\"text-decoration: underline;\"><span style=\"font-weight: 400;\">was destroyed</span></span><span style=\"font-weight: 400;\"> by the all the electric poles in the suburbs last year. (Incorrect Tense)</span></p>\r\n<p><span style=\"font-weight: 400;\">The cyclone destroyed all the suburbs by the electric poles last year. (Incorrect Sentence Structure)</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">5.(a)</span></p>\r\n<p><span style=\"font-weight: 400;\">The cyclone destroyed all the electric poles in the suburbs last year. (Correct)</span></p>\r\n<p><span style=\"font-weight: 400;\">The cyclone </span><span style=\"text-decoration: underline;\"><span style=\"font-weight: 400;\">was being destroyed</span></span><span style=\"font-weight: 400;\"> with all the electric poles in the suburbs last year. (Incorrect Tense)</span></p>\r\n<p><span style=\"font-weight: 400;\">The cyclone </span><span style=\"text-decoration: underline;\"><span style=\"font-weight: 400;\">was destroyed</span></span><span style=\"font-weight: 400;\"> by the all the electric poles in the suburbs last year. (Incorrect Tense)</span></p>\r\n<p><span style=\"font-weight: 400;\">The cyclone destroyed all the suburbs by the electric poles last year. (Incorrect Sentence Structure)</span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "7",
                    question_en: "<p>6.<span style=\"font-family: Cambria Math;\"> Sentences of a paragraph are given below in jumbled order. Arrange the sentences in </span><span style=\"font-family: Cambria Math;\">the correct order to form a meaningful and coherent paragraph.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">A. The deeper you immerse it, the greater will be the difference in the water level.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">B. This difference in the levels in the U-tube is a measure of the pressure applied by</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">your palm.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">C. If you immerse the funnel into a bucket of water, you will again see the difference in</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">the water level in the U-tube.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">D. When you gently apply pressure on the rubber film with your palm, you will find that</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">a difference in the water level is created in the manometer.</span></p>\n",
                    question_hi: "<p>6.<span style=\"font-family: Cambria Math;\"> Sentences of a paragraph are given below in jumbled order. Arrange the sentences in </span><span style=\"font-family: Cambria Math;\">the correct order to form a meaningful and coherent paragraph.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">A. The deeper you immerse it, the greater will be the difference in the water level.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">B. This difference in the levels in the U-tube is a measure of the pressure applied by</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">your palm.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">C. If you immerse the funnel into a bucket of water, you will again see the difference in</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">the water level in the U-tube.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">D. When you gently apply pressure on the rubber film with your palm, you will find that</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">a difference in the water level is created in the manometer.</span></p>\n",
                    options_en: ["<p>BACD</p>\n", "<p>CBAD</p>\n", 
                                "<p>DBCA</p>\n", "<p>ABCD</p>\n"],
                    options_hi: ["<p>BACD</p>\n", "<p>CBAD</p>\n",
                                "<p>DBCA</p>\n", "<p>ABCD</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">6.(c)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Sentence D will be the starting line as it contains the main idea of the parajumble i.e. a difference in the water level is created in the manometer by gently applying pressure. However, Sentence B states that this difference in the levels in the U-tube is a measure of the pressure applied by your palm. So, B will follow D. Further, Sentence C states that if you immerse the funnel into a bucket of water, you will again see the difference in the water level in the U-tube.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">and sentence A states that the deeper you immerse it, the greater will be the difference in the water level. So, A will follow C. Going through the options, option c has the correct sequence.</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">6.(c)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Sentence D will be the starting line as it contains the main idea of the parajumble i.e. a difference in the water level is created in the manometer by gently applying pressure. However, Sentence B states that this difference in the levels in the U-tube is a measure of the pressure applied by your palm. So, B will follow D. Further, Sentence C states that if you immerse the funnel into a bucket of water, you will again see the difference in the water level in the U-tube.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">and sentence A states that the deeper you immerse it, the greater will be the difference in the water level. So, A will follow C. Going through the options, option c has the correct sequence.</span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "7",
                    question_en: "<p>7.<span style=\"font-family: Cambria Math;\"> Select the most appropriate spelling that can substitute the underlined word in the given sentence.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">We love to play with our </span><span style=\"text-decoration: underline;\"><span style=\"font-family: Cambria Math;\">neighboar&rsquo;s</span></span><span style=\"font-family: Cambria Math;\"> children</span></p>\n",
                    question_hi: "<p>7.<span style=\"font-family: Cambria Math;\"> Select the most appropriate spelling that can substitute the underlined word in the given sentence.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">We love to play with our </span><span style=\"text-decoration: underline;\"><span style=\"font-family: Cambria Math;\">neighboar&rsquo;s</span></span><span style=\"font-family: Cambria Math;\"> children</span></p>\n",
                    options_en: ["<p>neighbours</p>\n", "<p>naighbour&rsquo;s</p>\n", 
                                "<p>neighboars</p>\n", "<p>neighbour&rsquo;s</p>\n"],
                    options_hi: ["<p>neighbours</p>\n", "<p>naighbour&rsquo;s</p>\n",
                                "<p>neighboars</p>\n", "<p>neighbour&rsquo;s</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">7.(d)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&ldquo;Neighbour&rsquo;s&rdquo; is the correct spelling for the given sentence. </span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">7.(d)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&ldquo;Neighbour&rsquo;s&rdquo; is the correct spelling for the given sentence. </span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "7",
                    question_en: "<p>8. <span style=\"font-family: Cambria Math;\">Select the most appropriate <strong>ANTONYM</strong> of the word in bold in the following sentence.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Her candid remarks impressed the Board of members.</span></p>\n",
                    question_hi: "<p>8. <span style=\"font-family: Cambria Math;\">Select the most appropriate <strong>ANTONYM</strong> of the word in bold in the following sentence.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Her candid remarks impressed the Board of members.</span></p>\n",
                    options_en: ["<p>blunt</p>\n", "<p>sincere</p>\n", 
                                "<p>deceitful</p>\n", "<p>unconventional</p>\n"],
                    options_hi: ["<p>blunt</p>\n", "<p>sincere</p>\n",
                                "<p>deceitful</p>\n", "<p>unconventional</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">8.(c)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Candid- saying exactly what you think</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Deceitful- </span><span style=\"font-family: Cambria Math;\">trying to make somebody believe something that is not true</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Blunt- </span><span style=\"font-family: Cambria Math;\">without a sharp edge or point</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Sincere- </span><span style=\"font-family: Cambria Math;\">really meaning or believing what you say</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Unconventional- </span><span style=\"font-family: Cambria Math;\">not based on or conforming to what is generally done or believed.</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">8.(c)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Candid- saying exactly what you think</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Deceitful- </span><span style=\"font-family: Cambria Math;\">trying to make somebody believe something that is not true</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Blunt- </span><span style=\"font-family: Cambria Math;\">without a sharp edge or point</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Sincere- </span><span style=\"font-family: Cambria Math;\">really meaning or believing what you say</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Unconventional- </span><span style=\"font-family: Cambria Math;\">not based on or conforming to what is generally done or believed.</span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "7",
                    question_en: " <p>9. </span><span style=\"font-family:Cambria Math\">Select the most appropriate synonym of the given word.</span></p> <p><span style=\"font-family:Cambria Math\">Decipher</span></p>",
                    question_hi: " <p>9. </span><span style=\"font-family:Cambria Math\">Select the most appropriate synonym of the given word.</span></p> <p><span style=\"font-family:Cambria Math\">Decipher</span></p>",
                    options_en: [" <p> Interpret</span></p>", " <p> Calculate</span></p>", 
                                " <p> Create</span></p>", " <p> Interrogate</span></p>"],
                    options_hi: [" <p> Interpret</span></p>", " <p> Calculate</span></p>",
                                " <p> Create</span></p>", " <p> Interrogate</span></p>"],
                    solution_en: " <p><span style=\"font-family:Cambria Math\">9.(a)</span></p> <p><span style=\"font-family:Cambria Math\">Decipher- to succeed in reading or understanding something that is not clear</span></p> <p><span style=\"font-family:Cambria Math\">Interpret- </span><span style=\"font-family:Cambria Math\">to explain or understand the meaning of something</span></p> <p><span style=\"font-family:Cambria Math\">Calculate- </span><span style=\"font-family:Cambria Math\">to find something out by using mathematics</span></p> <p><span style=\"font-family:Cambria Math\">Create- </span><span style=\"font-family:Cambria Math\">to cause something new to happen or exist</span></p> <p><span style=\"font-family:Cambria Math\">Interrogate- </span><span style=\"font-family:Cambria Math\">to ask somebody a lot of questions over a long period of time, especially in an aggressive way</span></p>",
                    solution_hi: " <p><span style=\"font-family:Cambria Math\">9.(a)</span></p> <p><span style=\"font-family:Cambria Math\">Decipher- to succeed in reading or understanding something that is not clear</span></p> <p><span style=\"font-family:Cambria Math\">Interpret- </span><span style=\"font-family:Cambria Math\">to explain or understand the meaning of something</span></p> <p><span style=\"font-family:Cambria Math\">Calculate- </span><span style=\"font-family:Cambria Math\">to find something out by using mathematics</span></p> <p><span style=\"font-family:Cambria Math\">Create- </span><span style=\"font-family:Cambria Math\">to cause something new to happen or exist</span></p> <p><span style=\"font-family:Cambria Math\">Interrogate- </span><span style=\"font-family:Cambria Math\">to ask somebody a lot of questions over a long period of time, especially in an aggressive way</span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "7",
                    question_en: "<p>10.<span style=\"font-family: Cambria Math;\"> Select the option that expresses the given sentence in an active voice.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">The utensils have been cleaned.</span></p>\n",
                    question_hi: "<p>10.<span style=\"font-family: Cambria Math;\"> Select the option that expresses the given sentence in an active voice.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">The utensils have been cleaned.</span></p>\n",
                    options_en: ["<p>Someone is cleaning the utensils.</p>\n", "<p>Someone was cleaning the utensils.</p>\n", 
                                "<p>Someone cleaned the utensils.</p>\n", "<p>Someone has cleaned the utensils.</p>\n"],
                    options_hi: ["<p>Someone is cleaning the utensils.</p>\n", "<p>Someone was cleaning the utensils.</p>\n",
                                "<p>Someone cleaned the utensils.</p>\n", "<p>Someone has cleaned the utensils.</p>\n"],
                    solution_en: "<p><span style=\"font-weight: 400;\">Someone </span><span style=\"text-decoration: underline;\"><span style=\"font-weight: 400;\">is cleaning</span></span><span style=\"font-weight: 400;\"> the utensils. (Incorrect Tense)</span></p>\r\n<p><span style=\"font-weight: 400;\">Someone </span><span style=\"text-decoration: underline;\"><span style=\"font-weight: 400;\">was cleaning</span></span><span style=\"font-weight: 400;\"> the utensils. (Incorrect Tense)</span></p>\r\n<p><span style=\"font-weight: 400;\">Someone </span><span style=\"text-decoration: underline;\"><span style=\"font-weight: 400;\">cleaned</span></span><span style=\"font-weight: 400;\"> the utensils. (Incorrect Tense)</span></p>\r\n<p><span style=\"font-weight: 400;\">Someone has cleaned the utensils. (Correct)</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">10.(d)</span></p>\r\n<p><span style=\"font-weight: 400;\">Someone </span><span style=\"text-decoration: underline;\"><span style=\"font-weight: 400;\">is cleaning</span></span><span style=\"font-weight: 400;\"> the utensils. (Incorrect Tense)</span></p>\r\n<p><span style=\"font-weight: 400;\">Someone </span><span style=\"text-decoration: underline;\"><span style=\"font-weight: 400;\">was cleaning</span></span><span style=\"font-weight: 400;\"> the utensils. (Incorrect Tense)</span></p>\r\n<p><span style=\"font-weight: 400;\">Someone </span><span style=\"text-decoration: underline;\"><span style=\"font-weight: 400;\">cleaned</span></span><span style=\"font-weight: 400;\"> the utensils. (Incorrect Tense)</span></p>\r\n<p><span style=\"font-weight: 400;\">Someone has cleaned the utensils. (Correct)</span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "7",
                    question_en: " <p>11.</span><span style=\"font-family:Cambria Math\"> Select the most appropriate option to fill in the blank.</span></p> <p><span style=\"font-family:Cambria Math\">A note of ______ during the leaders\' meeting at the UN.</span></p>",
                    question_hi: " <p>11.</span><span style=\"font-family:Cambria Math\"> Select the most appropriate option to fill in the blank.</span></p> <p><span style=\"font-family:Cambria Math\">A note of ______ during the leaders\' meeting at the UN.</span></p>",
                    options_en: [" <p> friction surfaced</span></p>", " <p> discord surfaced</span></p>", 
                                " <p> grievance surfaced</span></p>", " <p> strife surfaced</span></p>"],
                    options_hi: [" <p> friction surfaced</span></p>", " <p> discord surfaced</span></p>",
                                " <p> grievance surfaced</span></p>", " <p> strife surfaced</span></p>"],
                    solution_en: " <p><span style=\"font-family:Cambria Math\">11.(b)</span></p> <p><span style=\"font-family:Cambria Math\">‘Discord’ means </span><span style=\"font-family:Cambria Math\">disagreement or argument</span><span style=\"font-family:Cambria Math\">. The given sentence states that a note of discord surfaced during the leaders\' meeting at the UN. Hence, ‘discord surfaced’ is the most appropriate answer.</span></p>",
                    solution_hi: " <p><span style=\"font-family:Cambria Math\">11.(b)</span></p> <p><span style=\"font-family:Cambria Math\">‘Discord’ means </span><span style=\"font-family:Cambria Math\">disagreement or argument</span><span style=\"font-family:Cambria Math\">. The given sentence states that a note of discord surfaced during the leaders\' meeting at the UN. Hence, ‘discord surfaced’ is the most appropriate answer.</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "7",
                    question_en: " <p>12.</span><span style=\"font-family:Cambria Math\"> Select the most appropriate synonym of the given word.</span></p> <p><span style=\"font-family:Cambria Math\">Startle</span></p>",
                    question_hi: " <p>12.</span><span style=\"font-family:Cambria Math\"> Select the most appropriate synonym of the given word.</span></p> <p><span style=\"font-family:Cambria Math\">Startle</span></p>",
                    options_en: [" <p> Scare</span></p>", " <p> Humiliate</span></p>", 
                                " <p> Surprise</span></p>", " <p> Shame</span></p>"],
                    options_hi: [" <p> Scare</span></p>", " <p> Humiliate</span></p>",
                                " <p> Surprise</span></p>", " <p> Shame</span></p>"],
                    solution_en: " <p><span style=\"font-family:Cambria Math\">12.(c)</span></p> <p><span style=\"font-family:Cambria Math\">Startle- </span><span style=\"font-family:Cambria Math\">to surprise somebody/something in a way that slightly shocks or frightens him/her/it</span></p> <p><span style=\"font-family:Cambria Math\">Surprise- </span><span style=\"font-family:Cambria Math\">the feeling that you have when something happens that you do not expect</span></p> <p><span style=\"font-family:Cambria Math\">Scare- </span><span style=\"font-family:Cambria Math\">to make a person or an animal frightened</span></p> <p><span style=\"font-family:Cambria Math\">Humiliate- to make somebody feel very embarrassed</span></p> <p><span style=\"font-family:Cambria Math\">Shame- </span><span style=\"font-family:Cambria Math\">a fact or situation that makes you feel disappointed</span></p>",
                    solution_hi: " <p><span style=\"font-family:Cambria Math\">12.(c)</span></p> <p><span style=\"font-family:Cambria Math\">Startle- </span><span style=\"font-family:Cambria Math\">to surprise somebody/something in a way that slightly shocks or frightens him/her/it</span></p> <p><span style=\"font-family:Cambria Math\">Surprise- </span><span style=\"font-family:Cambria Math\">the feeling that you have when something happens that you do not expect</span></p> <p><span style=\"font-family:Cambria Math\">Scare- </span><span style=\"font-family:Cambria Math\">to make a person or an animal frightened</span></p> <p><span style=\"font-family:Cambria Math\">Humiliate- to make somebody feel very embarrassed</span></p> <p><span style=\"font-family:Cambria Math\">Shame- </span><span style=\"font-family:Cambria Math\">a fact or situation that makes you feel disappointed</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "7",
                    question_en: " <p>13.</span><span style=\"font-family:Cambria Math\"> Select the most appropriate meaning of the given idiom.</span></p> <p><span style=\"font-family:Cambria Math\">Get the axe</span></p>",
                    question_hi: " <p>13.</span><span style=\"font-family:Cambria Math\"> Select the most appropriate meaning of the given idiom.</span></p> <p><span style=\"font-family:Cambria Math\">Get the axe</span></p>",
                    options_en: [" <p> Burdened job</span></p>", " <p> Lose the job</span></p>", 
                                " <p> Relaxed job</span></p>", " <p> Get the job</span></p>"],
                    options_hi: [" <p> Burdened job</span></p>", " <p> Lose the job</span></p>",
                                " <p> Relaxed job</span></p>", " <p> Get the job</span></p>"],
                    solution_en: " <p><span style=\"font-family:Cambria Math\">13.(b)</span></p> <p><span style=\"font-family:Cambria Math\">Get the axe- lose the job.</span></p> <p><span style=\"font-family:Cambria Math\">E.g.- Rajat got the axe due to his constantly low performance in the company. </span></p>",
                    solution_hi: " <p><span style=\"font-family:Cambria Math\">13.(b)</span></p> <p><span style=\"font-family:Cambria Math\">Get the axe- lose the job.</span></p> <p><span style=\"font-family:Cambria Math\">E.g.- Rajat got the axe due to his constantly low performance in the company. </span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "7",
                    question_en: " <p>14. </span><span style=\"font-family:Cambria Math\">Select the most appropriate meaning of the given idiom.</span></p> <p><span style=\"font-family:Cambria Math\">Wipe the floor with someone</span></p>",
                    question_hi: " <p>14. </span><span style=\"font-family:Cambria Math\">Select the most appropriate meaning of the given idiom.</span></p> <p><span style=\"font-family:Cambria Math\">Wipe the floor with someone</span></p>",
                    options_en: [" <p> To defeat someone</span></p>", " <p> To criticise someone</span></p>", 
                                " <p> To cheat someone</span></p>", " <p> To help someone</span></p>"],
                    options_hi: [" <p> To defeat someone</span></p>", " <p> To criticise someone</span></p>",
                                " <p> To cheat someone</span></p>", " <p> To help someone</span></p>"],
                    solution_en: " <p><span style=\"font-family:Cambria Math\">14.(a)</span></p> <p><span style=\"font-family:Cambria Math\">Wipe the floor with someone- to defeat someone.</span></p> <p><span style=\"font-family:Cambria Math\">E.g.- Prithviraj Chauhan wiped the floor with Mohammad Ghori in the first battle of Tarain.</span></p>",
                    solution_hi: " <p><span style=\"font-family:Cambria Math\">14.(a)</span></p> <p><span style=\"font-family:Cambria Math\">Wipe the floor with someone- to defeat someone.</span></p> <p><span style=\"font-family:Cambria Math\">E.g.- Prithviraj Chauhan wiped the floor with Mohammad Ghori in the first battle of Tarain.</span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "7",
                    question_en: "<p>15.<span style=\"font-family: Cambria Math;\"> Select the option that can be used as a one-word substitute for the given group of </span><span style=\"font-family: Cambria Math;\">words.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Reject as invalid, especially by legal procedure</span></p>\n",
                    question_hi: "<p>15.<span style=\"font-family: Cambria Math;\"> Select the option that can be used as a one-word substitute for the given group of&nbsp; </span><span style=\"font-family: Cambria Math;\">words.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Reject as invalid, especially by legal procedure</span></p>\n",
                    options_en: ["<p>Reiterate</p>\n", "<p>Putrid</p>\n", 
                                "<p>Putrefy</p>\n", "<p>Quash</p>\n"],
                    options_hi: ["<p>Reiterate</p>\n", "<p>Putrid</p>\n",
                                "<p>Putrefy</p>\n", "<p>Quash</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">15.(d)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Quash- reject as invalid, especially by legal procedure</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Reiterate- </span><span style=\"font-family: Cambria Math;\">to repeat an opinion, statement, etc. that has already been said to make the meaning clear or to emphasize it</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Putrid- </span><span style=\"font-family: Cambria Math;\">smelling bad after being dead for some time</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Putrefy- </span><span style=\"font-family: Cambria Math;\">decay or rot and produce a fetid smell.</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">15.(d)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Quash- reject as invalid, especially by legal procedure</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Reiterate- </span><span style=\"font-family: Cambria Math;\">to repeat an opinion, statement, etc. that has already been said to make the meaning clear or to emphasize it</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Putrid- </span><span style=\"font-family: Cambria Math;\">smelling bad after being dead for some time</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Putrefy- </span><span style=\"font-family: Cambria Math;\">decay or rot and produce a fetid smell.</span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "16",
                    section: "7",
                    question_en: " <p>16.</span><span style=\"font-family:Cambria Math\"> Select the most appropriate option fill in the blank.</span></p> <p><span style=\"font-family:Cambria Math\">The death of her father had made her sullen and __________ .</span></p>",
                    question_hi: " <p>16.</span><span style=\"font-family:Cambria Math\"> Select the most appropriate option fill in the blank.</span></p> <p><span style=\"font-family:Cambria Math\">The death of her father had made her sullen and __________ .</span></p>",
                    options_en: [" <p> jovial</span></p>", " <p> smug</span></p>", 
                                " <p> joyful</span></p>", " <p> melancholic</span></p>"],
                    options_hi: [" <p> jovial</span></p>", " <p> smug</span></p>",
                                " <p> joyful</span></p>", " <p> melancholic</span></p>"],
                    solution_en: " <p><span style=\"font-family:Cambria Math\">16.(d)</span></p> <p><span style=\"font-family:Cambria Math\">‘Melancholic’ means </span><span style=\"font-family:Cambria Math\">feeling or expressing pensive sadness</span><span style=\"font-family:Cambria Math\">. The given sentence states that the death of her father had made her sullen and melancholic. Hence, ‘melancholic’ is the most appropriate answer.</span></p>",
                    solution_hi: " <p><span style=\"font-family:Cambria Math\">16.(d)</span></p> <p><span style=\"font-family:Cambria Math\">‘Melancholic’ means </span><span style=\"font-family:Cambria Math\">feeling or expressing pensive sadness</span><span style=\"font-family:Cambria Math\">. The given sentence states that the death of her father had made her sullen and melancholic. Hence, ‘melancholic’ is the most appropriate answer.</span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "17",
                    section: "7",
                    question_en: "<p>17. <span style=\"font-family: Cambria Math;\">Select the<strong>&nbsp; &nbsp;INCORRECTLY</strong> spelt word.</span></p>\n",
                    question_hi: "<p>17. <span style=\"font-family: Cambria Math;\">Select the&nbsp;<em> </em><strong>INCORRECTLY</strong> spelt word.</span></p>\n",
                    options_en: ["<p>Supercede</p>\n", "<p>Indefatigable</p>\n", 
                                "<p>Autopsy</p>\n", "<p>Anonymous</p>\n"],
                    options_hi: ["<p>Supercede</p>\n", "<p>Indefatigable</p>\n",
                                "<p>Autopsy</p>\n", "<p>Anonymous</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">17.(a)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Supersede is the correct spelling.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Supersede - to replace something older, less effective, or less important or official</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Indefatigable - always determined and energetic&nbsp;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Autopsy - a post-mortem examination to discover the cause of death or the extent of disease</span></p>\r\n<p>&nbsp;</p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">17.(a)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Supersede is the correct spelling.</span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "18",
                    section: "7",
                    question_en: "<p>18. <span style=\"font-family: Cambria Math;\">Select the most appropriate <strong>ANTONYM</strong> of the given word.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Vulgar</span></p>\n",
                    question_hi: "<p>18. <span style=\"font-family: Cambria Math;\">Select the most appropriate <strong>ANTONYM</strong> of the given word.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Vulgar</span></p>\n",
                    options_en: ["<p>Senior</p>\n", "<p>Decreased</p>\n", 
                                "<p>Ignorant</p>\n", "<p>Refined</p>\n"],
                    options_hi: ["<p>Senior</p>\n", "<p>Decreased</p>\n",
                                "<p>Ignorant</p>\n", "<p>Refined</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">18.(d)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Vulgar- </span><span style=\"font-family: Cambria Math;\">not having or showing good judgement about what is attractive or appropriate</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Refined- </span><span style=\"font-family: Cambria Math;\"> that has been made pure by having other substances taken out of it</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Senior- </span><span style=\"font-family: Cambria Math;\">having a high or higher position in a company, organization, etc.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Decreased- </span><span style=\"font-family: Cambria Math;\">to become or to make something smaller or less</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Ignorant- </span><span style=\"font-family: Cambria Math;\">not knowing about something</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">18.(d)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Vulgar- </span><span style=\"font-family: Cambria Math;\">not having or showing good judgement about what is attractive or appropriate</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Refined- </span><span style=\"font-family: Cambria Math;\"> that has been made pure by having other substances taken out of it</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Senior- </span><span style=\"font-family: Cambria Math;\">having a high or higher position in a company, organization, etc.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Decreased- </span><span style=\"font-family: Cambria Math;\">to become or to make something smaller or less</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Ignorant- </span><span style=\"font-family: Cambria Math;\">not knowing about something</span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "19",
                    section: "7",
                    question_en: "<p>19.<span style=\"font-family: Cambria Math;\"> Select the option that can be used as a one-word substitute for the given group of </span><span style=\"font-family: Cambria Math;\">words.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Merry, convivial, hearty and good humoured</span></p>\n",
                    question_hi: "<p>19.<span style=\"font-family: Cambria Math;\"> Select the option that can be used as a one-word substitute for the given group of </span><span style=\"font-family: Cambria Math;\">words.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Merry, convivial, hearty and good humoured</span></p>\n",
                    options_en: ["<p>Jowl</p>\n", "<p>Joyous</p>\n", 
                                "<p>Jovial</p>\n", "<p>Jubilee</p>\n"],
                    options_hi: ["<p>Jowl</p>\n", "<p>Joyous</p>\n",
                                "<p>Jovial</p>\n", "<p>Jubilee</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">19.(c)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Jovial- merry, convivial, hearty, and good-humoured</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Jowl- </span><span style=\"font-family: Cambria Math;\">the lower part of a person\'s or animal\'s cheek, especially when it is fleshy or drooping.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Joyous- very happy or full of joy</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Jubilee- </span><span style=\"font-family: Cambria Math;\">a special anniversary of an event that took place a certain number of years ago,</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">19.(c)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Jovial- merry, convivial, hearty, and good-humoured</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Jowl- </span><span style=\"font-family: Cambria Math;\">the lower part of a person\'s or animal\'s cheek, especially when it is fleshy or drooping.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Joyous- very happy or full of joy</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Jubilee- </span><span style=\"font-family: Cambria Math;\">a special anniversary of an event that took place a certain number of years ago,</span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "20",
                    section: "7",
                    question_en: "<p>20.<span style=\"font-family: Cambria Math;\"> Select the most appropriate synonym of the given word.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Overture</span></p>\n",
                    question_hi: "<p>20.<span style=\"font-family: Cambria Math;\"> Select the most appropriate synonym of the given word.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Overture</span></p>\n",
                    options_en: ["<p>Slander</p>\n", "<p>Conclusion</p>\n", 
                                "<p>Preamble</p>\n", "<p>Plunk</p>\n"],
                    options_hi: ["<p>Slander</p>\n", "<p>Conclusion</p>\n",
                                "<p>Preamble</p>\n", "<p>Plunk</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">20.(c)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Overture- an introduction to something more substantial&nbsp;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Preamble- </span><span style=\"font-family: Cambria Math;\">an introduction or a preface, for example to a book, a written document, speech, etc</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Slander- </span><span style=\"font-family: Cambria Math;\">a spoken statement about somebody that is not true and that is intended to damage the good opinion that other people have of him/her</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Conclusion- </span><span style=\"font-family: Cambria Math;\">an opinion that you reach after thinking about something carefully</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Plunk- </span><span style=\"font-family: Cambria Math;\">the sound made by abruptly plucking a string of a stringed instrument.</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">20.(c)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Overture- an introduction to something more substantial&nbsp;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Preamble- </span><span style=\"font-family: Cambria Math;\">an introduction or a preface, for example to a book, a written document, speech, etc</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Slander- </span><span style=\"font-family: Cambria Math;\">a spoken statement about somebody that is not true and that is intended to damage the good opinion that other people have of him/her</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Conclusion- </span><span style=\"font-family: Cambria Math;\">an opinion that you reach after thinking about something carefully</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Plunk- </span><span style=\"font-family: Cambria Math;\">the sound made by abruptly plucking a string of a stringed instrument.</span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "21",
                    section: "7",
                    question_en: "<p>21. <span style=\"font-family: Cambria Math;\">Comprehension:</span></p>\r\n<p><span style=\"font-weight: 400;\">In the following passage, some words have been deleted. Read the passage carefully and</span></p>\r\n<p><span style=\"font-weight: 400;\">select the most appropriate option to fill in each blank.</span></p>\r\n<p><span style=\"font-weight: 400;\">Most of the valley had been (21)_________ over the ages by the river waters, (22)_________</span></p>\r\n<p><span style=\"font-weight: 400;\">rainfall, and fierce winds. All except for one giant monolith, a (23)_________ tower-like</span></p>\r\n<p><span style=\"font-weight: 400;\">mountain of a single rock. It stood at a proud (24)_________ of eight hundred and fifty metres</span></p>\r\n<p><span style=\"font-weight: 400;\">from the valley floor, towering well above the (25)_________ shoulders</span></p>\r\n<p><span style=\"font-weight: 400;\">Select the most appropriate option to fill in blank number 21.</span></p>\n",
                    question_hi: " <p>21. </span><span style=\"font-family:Cambria Math\">Comprehension:</span></p> <p><span style=\"font-family:Cambria Math\">In the following passage, some words have been deleted. Read the passage carefully and</span></p> <p><span style=\"font-family:Cambria Math\">select the most appropriate option to fill in each blank.</span></p> <p><span style=\"font-family:Cambria Math\">Most of the valley had been (1)_________ over the ages by the river waters, (2)_________</span></p> <p><span style=\"font-family:Cambria Math\">rainfall, and fierce winds. All except for one giant monolith, a (3)_________ tower-like</span></p> <p><span style=\"font-family:Cambria Math\">mountain of a single rock. It stood at a proud (4)_________ of eight hundred and fifty metres</span></p> <p><span style=\"font-family:Cambria Math\">from the valley floor, towering well above the (5)_________ shoulders.</span></p> <p><span style=\"font-family:Cambria Math\">Select the most appropriate option to fill in blank number 1.</span></p>",
                    options_en: ["<p>dried</p>\n", "<p>decoded</p>\n", 
                                "<p>eroded</p>\n", "<p>wiped</p>\n"],
                    options_hi: [" <p> dried</span></p>", " <p> decoded</span></p>",
                                " <p> eroded</span></p>", " <p> wiped</span></p>"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">21.(c)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&lsquo;Eroded&rsquo; means </span><span style=\"font-family: Cambria Math;\">to destroy something slowly. </span><span style=\"font-family: Cambria Math;\">The given passage states that most of the valley had been eroded over the ages by the river waters. Hence, &lsquo;eroded&rsquo; is the most appropriate answer.</span></p>\n",
                    solution_hi: " <p><span style=\"font-family:Cambria Math\">21.(c)</span></p> <p><span style=\"font-family:Cambria Math\">‘Eroded’ means </span><span style=\"font-family:Cambria Math\">to destroy something slowly. </span><span style=\"font-family:Cambria Math\">The given passage states that most of the valley had been eroded over the ages by the river waters. Hence, ‘eroded’ is the most appropriate answer.</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "22",
                    section: "7",
                    question_en: "<p>22.<span style=\"font-family: Cambria Math;\">Comprehension:</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">In the following passage, some words have been deleted. Read the passage carefully and</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">select the most appropriate option to fill in each blank.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Most of the valley had been (21)_________ over the ages by the river waters, (22)_________</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">rainfall, and fierce winds. All except for one giant monolith, a (23)_________ tower-like</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">mountain of a single rock. It stood at a proud (24)_________ of eight hundred and fifty metres</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">from the valley floor, towering well above the (25)_________ shoulders.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Select the most appropriate option to fill in blank number 22.</span></p>\n",
                    question_hi: "<p>22.<span style=\"font-family: Cambria Math;\">Comprehension:</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">In the following passage, some words have been deleted. Read the passage carefully and</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">select the most appropriate option to fill in each blank.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Most of the valley had been (21)_________ over the ages by the river waters, (22)_________</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">rainfall, and fierce winds. All except for one giant monolith, a (23)_________ tower-like</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">mountain of a single rock. It stood at a proud (24)_________ of eight hundred and fifty metres</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">from the valley floor, towering well above the (25)_________ shoulders.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Select the most appropriate option to fill in blank number 22.</span></p>\n",
                    options_en: ["<p>empty</p>\n", "<p>heavy</p>\n", 
                                "<p>less</p>\n", "<p>scanty</p>\n"],
                    options_hi: ["<p>empty</p>\n", "<p>heavy</p>\n",
                                "<p>less</p>\n", "<p>scanty</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">22.(b)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&lsquo;Heavy&rsquo; means </span><span style=\"font-family: Cambria Math;\">weighing a lot. </span><span style=\"font-family: Cambria Math;\">The given passage states that most of the valley had been eroded over the ages by the river waters, heavy rainfall, and fierce winds. Hence, &lsquo;heavy&rsquo; is the most appropriate answer.</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">22.(b)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&lsquo;Heavy&rsquo; means </span><span style=\"font-family: Cambria Math;\">weighing a lot. </span><span style=\"font-family: Cambria Math;\">The given passage states that most of the valley had been eroded over the ages by the river waters, heavy rainfall, and fierce winds. Hence, &lsquo;heavy&rsquo; is the most appropriate answer.</span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "23",
                    section: "7",
                    question_en: "<p>23.<span style=\"font-family: Cambria Math;\">Comprehension:</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">In the following passage, some words have been deleted. Read the passage carefully and</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">select the most appropriate option to fill in each blank.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Most of the valley had been (21)_________ over the ages by the river waters, (22)_________</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">rainfall, and fierce winds. All except for one giant monolith, a (23)_________ tower-like</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">mountain of a single rock. It stood at a proud (24)_________ of eight hundred and fifty metres</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">from the valley floor, towering well above the (25)_________ shoulders.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Select the most appropriate option to fill in blank number 23.</span></p>\n",
                    question_hi: "<p>23.<span style=\"font-family: Cambria Math;\">Comprehension:</span></p>\r\n<p><span style=\"font-weight: 400;\">In the following passage, some words have been deleted. Read the passage carefully and</span></p>\r\n<p><span style=\"font-weight: 400;\">select the most appropriate option to fill in each blank.</span></p>\r\n<p><span style=\"font-weight: 400;\">Most of the valley had been (21)_________ over the ages by the river waters, (22)_________</span></p>\r\n<p><span style=\"font-weight: 400;\">rainfall, and fierce winds. All except for one giant monolith, a (23)_________ tower-like</span></p>\r\n<p><span style=\"font-weight: 400;\">mountain of a single rock. It stood at a proud (24)_________ of eight hundred and fifty metres</span></p>\r\n<p><span style=\"font-weight: 400;\">from the valley floor, towering well above the (25)_________ shoulders</span></p>\r\n<p><span style=\"font-weight: 400;\">Select the most appropriate option to fill in blank number 23.</span></p>\n",
                    options_en: ["<p>humongous</p>\n", "<p>small</p>\n", 
                                "<p>neat</p>\n", "<p>tiny</p>\n"],
                    options_hi: ["<p>humongous</p>\n", "<p>small</p>\n",
                                "<p>neat</p>\n", "<p>tiny</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">23.(a)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&lsquo;Humongous&rsquo; means huge or enormous. The given passage talks about one giant monolith, a humongous tower-like mountain of a single rock. Hence, &lsquo;humongous&rsquo; is the most appropriate answer.</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">23.(a)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&lsquo;Humongous&rsquo; means huge or enormous. The given passage talks about one giant monolith, a humongous tower-like mountain of a single rock. Hence, &lsquo;humongous&rsquo; is the most appropriate answer.</span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "24",
                    section: "7",
                    question_en: "<p>24. <span style=\"font-family: Cambria Math;\">Comprehension:</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">In the following passage, some words have been deleted. Read the passage carefully and</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">select the most appropriate option to fill in each blank.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Most of the valley had been (21)_________ over the ages by the river waters, (22)_________</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">rainfall, and fierce winds. All except for one giant monolith, a (23)_________ tower-like</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">mountain of a single rock. It stood at a proud (24)_________ of eight hundred and fifty metres</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">from the valley floor, towering well above the (25)_________ shoulders.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Select the most appropriate option to fill in blank number 24.</span></p>\n",
                    question_hi: "<p>24.<span style=\"font-family: Cambria Math;\">Comprehension:</span></p>\r\n<p><span style=\"font-weight: 400;\">In the following passage, some words have been deleted. Read the passage carefully and</span></p>\r\n<p><span style=\"font-weight: 400;\">select the most appropriate option to fill in each blank.</span></p>\r\n<p><span style=\"font-weight: 400;\">Most of the valley had been (21)_________ over the ages by the river waters, (22)_________</span></p>\r\n<p><span style=\"font-weight: 400;\">rainfall, and fierce winds. All except for one giant monolith, a (23)_________ tower-like</span></p>\r\n<p><span style=\"font-weight: 400;\">mountain of a single rock. It stood at a proud (24)_________ of eight hundred and fifty metres</span></p>\r\n<p><span style=\"font-weight: 400;\">from the valley floor, towering well above the (25)_________ shoulders</span></p>\r\n<p><span style=\"font-weight: 400;\">Select the most appropriate option to fill in blank number 24.</span></p>\n",
                    options_en: ["<p>weight</p>\n", "<p>height</p>\n", 
                                "<p>length</p>\n", "<p>width</p>\n"],
                    options_hi: ["<p>weight</p>\n", "<p>height</p>\n",
                                "<p>length</p>\n", "<p>width</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">24.(b)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&lsquo;Height&rsquo; means </span><span style=\"font-family: Cambria Math;\">the measurement from the bottom to the top of a person or thing. </span><span style=\"font-family: Cambria Math;\">The given passage states that it stood at a proud height of eight hundred and fifty metres from the valley floor. Hence, &lsquo;height&rsquo; is the most appropriate answer.</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">24.(b)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&lsquo;Height&rsquo; means </span><span style=\"font-family: Cambria Math;\">the measurement from the bottom to the top of a person or thing. </span><span style=\"font-family: Cambria Math;\">The given passage states that it stood at a proud height of eight hundred and fifty metres from the valley floor. Hence, &lsquo;height&rsquo; is the most appropriate answer.</span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "25",
                    section: "misc",
                    question_en: "<p>25. <span style=\"font-family: Cambria Math;\">Comprehension:</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">In the following passage, some words have been deleted. Read the passage carefully and</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">select the most appropriate option to fill in each blank.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Most of the valley had been (21)_________ over the ages by the river waters, (22)_________</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">rainfall, and fierce winds. All except for one giant monolith, a (23)_________ tower-like</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">mountain of a single rock. It stood at a proud (24)_________ of eight hundred and fifty metres</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">from the valley floor, towering well above the (25)_________ shoulders.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Select the most appropriate option to fill in blank number 25.</span></p>\n",
                    question_hi: "<p>25.<span style=\"font-family: Cambria Math;\">Comprehension:</span></p>\r\n<p><span style=\"font-weight: 400;\">In the following passage, some words have been deleted. Read the passage carefully and</span></p>\r\n<p><span style=\"font-weight: 400;\">select the most appropriate option to fill in each blank.</span></p>\r\n<p><span style=\"font-weight: 400;\">Most of the valley had been (21)_________ over the ages by the river waters, (22)_________</span></p>\r\n<p><span style=\"font-weight: 400;\">rainfall, and fierce winds. All except for one giant monolith, a (23)_________ tower-like</span></p>\r\n<p><span style=\"font-weight: 400;\">mountain of a single rock. It stood at a proud (24)_________ of eight hundred and fifty metres</span></p>\r\n<p><span style=\"font-weight: 400;\">from the valley floor, towering well above the (25)_________ shoulders</span></p>\r\n<p><span style=\"font-weight: 400;\">Select the most appropriate option to fill in blank number 25.</span></p>\n",
                    options_en: ["<p>shelf&rsquo;s</p>\n", "<p>mountain</p>\n", 
                                "<p>valley</p>\n", "<p>valley&rsquo;s</p>\n"],
                    options_hi: ["<p>shelf&rsquo;s</p>\n", "<p>mountain</p>\n",
                                "<p>valley</p>\n", "<p>valley&rsquo;s</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">25.(d)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&lsquo;Valley&rsquo; means </span><span style=\"font-family: Cambria Math;\">the low land between two mountains or hills, which often has a river flowing through it.</span><span style=\"font-family: Cambria Math;\"> The given passage states that it stood at a proud height of eight hundred and fifty metres from the valley floor, towering well above the valley&rsquo;s shoulders. Hence, &lsquo;valley&rsquo; is the most appropriate answer.</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">25.(d)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&lsquo;Valley&rsquo; means </span><span style=\"font-family: Cambria Math;\">the low land between two mountains or hills, which often has a river flowing through it.</span><span style=\"font-family: Cambria Math;\"> The given passage states that it stood at a proud height of eight hundred and fifty metres from the valley floor, towering well above the valley&rsquo;s shoulders. Hence, &lsquo;valley&rsquo; is the most appropriate answer.</span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>