<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">8:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 25</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">25</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 8 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["18"] = {
                name: "General Awareness",
                start: 0,
                end: 23
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="18">General Awareness</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 24,
                end: 24
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "18",
                    question_en: "<p>1. India is home to the second oldest cricket club in the world. Where is it situated?</p>",
                    question_hi: "<p>1. विश्व का दूसरा सबसे पुराना क्रिकेट क्लब भारत में है, यह कहां स्थित है?</p>",
                    options_en: ["<p>Mumbai</p>", "<p>Kolkata</p>", 
                                "<p>Hyderabad</p>", "<p>Chennai</p>"],
                    options_hi: ["<p>मुंबई</p>", "<p>कोलकाता</p>",
                                "<p>हैदराबाद</p>", "<p>चेन्नई</p>"],
                    solution_en: "<p>1.(b) <strong>Kolkata</strong>. The world\'s first Cricket club - Hambledon, England (1760). Marylebone Cricket Club (MCC) - 1787. First Cricket club in India - Calcutta Cricket club (1792). First ever international cricket match - US vs Canada in 1844. India played their first International cricket match - Against England in 1932.</p>",
                    solution_hi: "<p>1.(b) <strong>कोलकाता</strong>। दुनिया का पहला क्रिकेट क्लब - हैम्बल्डन, इंग्लैंड (1760)। मैरीलेबोन क्रिकेट क्लब (MCC) - 1787। भारत में पहला क्रिकेट क्लब - कलकत्ता क्रिकेट क्लब (1792)। पहला अंतरराष्ट्रीय क्रिकेट मैच - 1844 में अमेरिका बनाम कनाडा। भारत ने अपना पहला अंतरराष्ट्रीय क्रिकेट मैच 1932 में इंग्लैंड के खिलाफ खेला।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "18",
                    question_en: "<p>2. In which of the following regions of India is the Mundari language spoken?</p>",
                    question_hi: "<p>2. मुंडारी (Mundari) भाषा भारत के निम्नलिखित में से किस क्षेत्र में बोली जाती है?</p>",
                    options_en: ["<p>Chota-Nagpur plateau</p>", "<p>Baghelkhand</p>", 
                                "<p>Thar desert</p>", "<p>Malwa plateau</p>"],
                    options_hi: ["<p>छोटा-नागपुर पठार</p>", "<p>बघेलखण्ड</p>",
                                "<p>थार रेगिस्तान</p>", "<p>मालवा का पठार</p>"],
                    solution_en: "<p>2.(a) <strong>Chota-Nagpur plateau.</strong> This plateau covers parts of Jharkhand, Chhattisgarh, Odisha, and West Bengal. Mundari is a Munda language, belonging to the Austroasiatic language family. Chhota Nagpur plateau is called the &ldquo;Heart of industrial India&rdquo; because it is rich in minerals and power fuels. The highest mountain peak of Chota Nagpur Plateau is Parasnath.</p>",
                    solution_hi: "<p>2.(a) <strong>छोटा-नागपुर पठार।</strong> यह पठार झारखंड, छत्तीसगढ़, ओडिशा और पश्चिम बंगाल के कुछ हिस्सों तक फैली है। मुंडारी एक मुंडा भाषा है, जो ऑस्ट्रोएशियाटिक भाषा परिवार से संबंधित है। छोटा नागपुर पठार को \"औद्योगिक भारत का हृदय\" कहा जाता है क्योंकि यह खनिजों और बिजली ईंधन से समृद्ध है। छोटा नागपुर पठार की सबसे ऊँची पर्वत चोटी पारसनाथ है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "18",
                    question_en: "<p>3. Which of the following statements is/are true with respect to MRTP Act?<br>i) The Monopolies and Restrictive Trade Practices Act (MRTP Act) was repealed and replaced by the Competition Act, 2002.<br>ii) The asset limit for MRTP companies was fixed at Rs. 25 crores by the first/original Monopolies and Restrictive Trade Practices Act (MRTP Act).<br>iii) The Monopolies and Restrictive Trade Practices Act (MRTP Act) was first passed in 1969.</p>",
                    question_hi: "<p>3. MRTP अधिनियम के संबंध में निम्नलिखित में से कौन-सा / कौन-से कथन सत्य है/हैं?<br>i) एकाधिकार तथा प्रतिबंधात्मक व्यापार व्यवहार अधिनियम (MRTP Act) को प्रतिस्पर्धा अधिनियम, 2002 द्वारा निरस्त व प्रतिस्थापित किया गया था।<br>ii) पहले/मूल एकाधिकार तथा प्रतिबंधात्मक व्यापार व्यवहार अधिनियम (MRTP Act) द्वारा MRTP कंपनियों के लिए परिसंपत्ति सीमा (asset limit) 25 करोड़ रुपए निर्धारित की गई थी।<br>iii) एकाधिकार तथा प्रतिबंधात्मक व्यापार व्यवहार अधिनियम (MRTP Act) पहली बार 1969 में पारित किया गया था।</p>",
                    options_en: ["<p>Only iii</p>", "<p>Only i and iii</p>", 
                                "<p>Only i and ii</p>", "<p>Only ii and iii</p>"],
                    options_hi: ["<p>केवल iii</p>", "<p>केवल । और iii</p>",
                                "<p>केवल i और ii</p>", "<p>केवल ii और iii</p>"],
                    solution_en: "<p>3.(b) <strong>Only i and iii. </strong>The MRTP Act was indeed first passed in 1969 and was later repealed and replaced by the Competition Act, 2002 . However, the asset limit for MRTP companies was fixed at Rs. 100 crores.</p>",
                    solution_hi: "<p>3.(b) <strong>केवल । और iii. </strong>MRTP अधिनियम वास्तव में पहली बार 1969 में पारित किया गया था और बाद में इसे निरस्त कर दिया गया और प्रतिस्पर्धा अधिनियम, 2002 द्वारा प्रतिस्थापित किया गया। हालांकि, MRTP कंपनियों के लिए परिसंपत्ति सीमा 100 करोड़ रुपये तय की गई थी।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "18",
                    question_en: "<p>4. \'Pankhida\' songs are sung by peasants of _____ while working on the fields.</p>",
                    question_hi: "<p>4. पंखिड़ा (Pankhida)\' गीत ________ के किसानों द्वारा खेतों में काम करते समय गाए जाते हैं।</p>",
                    options_en: ["<p>Bihar</p>", "<p>Uttar Pradesh</p>", 
                                "<p>Rajasthan</p>", "<p>Odisha</p>"],
                    options_hi: ["<p>बिहार</p>", "<p>उत्तर प्रदेश</p>",
                                "<p>राजस्थान</p>", "<p>ओडिशा</p>"],
                    solution_en: "<p>4.(c) <strong>Rajasthan</strong>. \'Pankhida\' songs are an integral part of Rajasthani folk culture and are typically sung during agricultural activities like sowing, harvesting, and threshing. The peasants sing and speak while playing algoza and manjira. Indian folk music - Bhavageethe (Karnataka and Maharashtra), Bhangra and Giddha (Punjab), Lavani (Maharashtra), Alha (Madhya Pradesh) and Panihari, Pankhida and Maand (Rajasthan).</p>",
                    solution_hi: "<p>4.(c) <strong>राजस्थान</strong>। \'पंखिड़ा\' गीत राजस्थानी लोक संस्कृति का एक अभिन्न अंग हैं और आमतौर पर बुआई, कटाई और मड़ाई जैसी कृषि गतिविधियों के दौरान गाए जाते हैं। किसान अलगोजा और मंजीरा बजाते हुए गाते और बोलते हैं। भारतीय लोक संगीत - भावगीथे (कर्नाटक और महाराष्ट्र), भांगड़ा और गिद्दा (पंजाब), लावणी (महाराष्ट्र), आल्हा (मध्य प्रदेश) और पनिहारी, पंखिड़ा तथा मांड (राजस्थान)।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "18",
                    question_en: "<p>5. ___________ is a musical composition that expresses devotion through madhura bhakti.</p>",
                    question_hi: "<p>5. _______ एक संगीत रचना है, जो मधुर भक्ति (madhura bhakti) के माध्यम से भक्ति को व्यक्त करती है।</p>",
                    options_en: ["<p>Tillana</p>", "<p>Gita</p>", 
                                "<p>Padam</p>", "<p>Kriti </p>"],
                    options_hi: ["<p>तिलाना</p>", "<p>गीता</p>",
                                "<p>पदम</p>", "<p>कृति</p>"],
                    solution_en: "<p>5.(c) <strong>Padam</strong>. A musical composition can refer to an original piece, its structure, or the process of creating it. Tillana is a rhythmic Carnatic piece performed at the end of concerts, focusing on rhythm and tempo. Gita is a hymn, often from texts like the Bhagavad Gita. Kriti, another Carnatic composition, expresses various emotions, not limited to devotion.</p>",
                    solution_hi: "<p>5.(c) <strong>पदम</strong>। संगीत रचना किसी मूल कृति, उसकी संरचना या उसे बनाने की प्रक्रिया को संदर्भित कर सकती है। तिलाना एक लयबद्ध कर्नाटक संगीत रचना है जिसे संगीत समारोहों के अंत में बजाया जाता है, जिसमें लय और ताल पर ध्यान दिया जाता है। गीता एक भजन है, जो अक्सर भगवद गीता जैसे ग्रंथों से लिया जाता है। कृति, एक अन्य कर्नाटक संगीत रचना है, जो भक्ति तक सीमित न होकर विभिन्न भावनाओं को व्यक्त करती है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "18",
                    question_en: "<p>6. Which of the following organisation&rsquo;s initiative is &lsquo;India Justice Report&rsquo;?</p>",
                    question_hi: "<p>6. \'इंडिया जस्टिस रिपोर्ट (India Justice Report)\' निम्नलिखित में से किस संगठन की पहल है?</p>",
                    options_en: ["<p>HCL Foundation</p>", "<p>Infosys Foundation</p>", 
                                "<p>Tata Trusts</p>", "<p>Azim Premji Foundation</p>"],
                    options_hi: ["<p>HCL फाउंडेशन</p>", "<p>इन्फोसिस फाउंडेशन</p>",
                                "<p>टाटा ट्रस्ट</p>", "<p>अजीम प्रेमजी फाउंडेशन</p>"],
                    solution_en: "<p>6.(c) <strong>Tata Trusts. </strong>The India Justice Report (IJR) is an initiative by Tata Trusts, in collaboration with the Centre for Social Justice, Common Cause, and the Commonwealth Human Rights Initiative, among others. It was first published in 2019. Karnataka was the top-ranked state in the 2022 India Justice Report (IJR).</p>",
                    solution_hi: "<p>6.(c) <strong>टाटा ट्रस्ट।</strong> इंडिया जस्टिस रिपोर्ट (IJR), टाटा ट्रस्ट द्वारा सेंटर फॉर सोशल जस्टिस, कॉमन कॉज और कॉमनवेल्थ ह्यूमन राइट्स इनिशिएटिव के सहयोग से बनाई गई एक पहल है। इसे पहली बार 2019 में प्रकाशित किया गया था। 2022 इंडिया जस्टिस रिपोर्ट (IJR) में कर्नाटक शीर्ष स्थान पर था।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "18",
                    question_en: "<p>7. Which set of Articles in the Constitution of India are known as &lsquo;Cultural and Educational Rights&rsquo;?</p>",
                    question_hi: "<p>7. भारत के संविधान में अनुच्छेदों के किस समूह को \'सांस्कृतिक और शैक्षिक अधिकार\' के रूप में जाना जाता है?</p>",
                    options_en: ["<p>Articles 19 - 20</p>", "<p>Articles 23 - 24</p>", 
                                "<p>Articles 29 - 30</p>", "<p>Articles 14 - 15</p>"],
                    options_hi: ["<p>अनुच्छेद 19 - 20</p>", "<p>अनुच्छेद 23 - 24</p>",
                                "<p>अनुच्छेद 29 - 30</p>", "<p>अनुच्छेद 14 - 15</p>"],
                    solution_en: "<p>7.(c) <strong>Articles 29 - 30.</strong> Part III: Fundamental rights (Articles 12 - 35) : Right to Equality (Article 14 - 18), Right to Freedom (Article 19 - 22), Right against Exploitation (Articles 23 - 24), Right to Freedom of Religion (Article 25 - 28), Saving of Certain Laws (Article 31), Right to Constitutional Remedies (Article 32).</p>",
                    solution_hi: "<p>7.(c) <strong>अनुच्छेद 29 - 30.</strong> भाग III: मौलिक अधिकार (अनुच्छेद 12 - 35): समानता का अधिकार (अनुच्छेद 14 - 18), स्वतंत्रता का अधिकार (अनुच्छेद 19 - 22), शोषण के विरुद्ध अधिकार (अनुच्छेद 23 - 24), धार्मिक स्वतंत्रता का अधिकार (अनुच्छेद 25 - 28), कुछ विधियों का संरक्षण (अनुच्छेद 31), संवैधानिक उपचारों का अधिकार (अनुच्छेद 32)।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "18",
                    question_en: "<p>8. Pandit Birju Maharaj was related to:</p>",
                    question_hi: "<p>8. पंडित बिरजू महाराज _________ नृत्य से संबंधित थे।</p>",
                    options_en: ["<p>Ozele</p>", "<p>Odissi</p>", 
                                "<p>Tapu</p>", "<p>Kathak</p>"],
                    options_hi: ["<p>ओज़ीले</p>", "<p>ओडिसी</p>",
                                "<p>टापू</p>", "<p>कथक</p>"],
                    solution_en: "<p>8.(d) <strong>Kathak</strong>. Pandit Birju Maharaj was a leading exponent of the Lucknow Kalka-Bindadin gharana of Kathak. His awards: Sangeet Natak Akademi Award (1964), Padma Vibhushan (1986). Kathak Exponents : Lachhu Maharaj, Shovana Narayan, Madhu Nataraj, Nirupama Rajendra, Shambhu Maharaj, Jitendra Maharaj, Kumudini Lakhia.</p>",
                    solution_hi: "<p>8.(d) <strong>कथक</strong>। पंडित बिरजू महाराज कथक के लखनऊ कालका-बिंदादीन घराने के एक प्रमुख प्रतिपादक थे। उनके पुरस्कार: संगीत नाटक अकादमी पुरस्कार (1964), पद्म विभूषण (1986)। कथक के प्रतिपादक: लच्छू महाराज, शोवना नारायण, मधु नटराज, निरुपमा राजेंद्र, शंभू महाराज, जितेंद्र महाराज, कुमुदिनी लाखिया।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "18",
                    question_en: "<p>9. In the 11th century, under Maḥmūd of Ghazni, Ghaznī, a small town in ______, became&nbsp;the capital of the vast empire of the Ghaznavids.</p>",
                    question_hi: "<p>9. 11वीं शताब्दी में, ग़ज़नी के महमूद के अधीन, गजनी, ________ में एक छोटा-सा शहर, गजनवी के विशाल साम्राज्य की राजधानी बन गया।</p>",
                    options_en: ["<p>Egypt</p>", "<p>Persia</p>", 
                                "<p>Afghanistan</p>", "<p>Turkey</p>"],
                    options_hi: ["<p>मिस्र</p>", "<p>फारस</p>",
                                "<p>अफ़ग़ानिस्तान</p>", "<p>टर्की</p>"],
                    solution_en: "<p>9.(c) <strong>Afghanistan</strong>. Mahmud of Ghazni was a Turkish conqueror who attacked India 17 times between 1000 to 1027 AD. In 1018 he plundered the holy city of Mathura and also attacked Kannauj. In 1019 and 1029 he undertook two raids on Gangetic valley. In 1025 he attacked Somnath (a town on the coast of Kathiawar). His last invasion was in 1027 to punish the Jats who obstructed him on his return journey from Somanath. He died in 1030. </p>",
                    solution_hi: "<p>9.(c) <strong>अफ़गानिस्तान</strong>। महमूद ग़ज़नी एक तुर्की विजेता था जिसने 1000 से 1027 ई. के बीच भारत पर 17 बार आक्रमण किया। 1018 में उसने मथुरा के पवित्र शहर को लूटा और कन्नौज पर भी आक्रमण किया। 1019 और 1029 में उसने गंगा घाटी पर दो बार आक्रमण किया। 1025 में उसने सोमनाथ (काठियावाड़ के तट पर स्थित एक शहर) पर आक्रमण किया। उसका अंतिम आक्रमण 1027 में जाटों को दण्डित करने के लिए हुआ था जिन्होंने सोमनाथ से उसकी वापसी यात्रा में बाधा डाली थी। 1030 में उसकी मृत्यु हो गई।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "18",
                    question_en: "<p>10. Which type of electromagnetic radiation was discovered by Johann Wilhelm Ritter in 1801?</p>",
                    question_hi: "<p>10. 1801 में जोहान विल्हेम रिटर (Johann Wilhelm Ritter) ने किस प्रकार के विद्युत चुंबकीय विकिरण की खोज की?</p>",
                    options_en: ["<p>X-rays</p>", "<p>Infrared rays</p>", 
                                "<p>Gamma rays</p>", "<p>Ultraviolet rays</p>"],
                    options_hi: ["<p>एक्स-रे</p>", "<p>अवरक्त किरणें</p>",
                                "<p>गामा किरणें</p>", "<p>पराबैंगनी किरणें</p>"],
                    solution_en: "<p>10.(d) <strong>Ultraviolet rays.</strong> Johann Wilhelm Ritter observed that a chemical reaction occurred beyond the violet end of the visible spectrum, leading to the discovery of UV radiation. X-rays were discovered by Wilhelm R&ouml;ntgen in 1895. Infrared rays were discovered by William Herschel in 1800.</p>",
                    solution_hi: "<p>10.(d) <strong>पराबैंगनी किरणें। </strong>जोहान विल्हेम रिटर ने देखा कि दृश्यमान स्पेक्ट्रम के बैंगनी छोर से परे एक रासायनिक प्रतिक्रिया हुई, जिससे UV विकिरण की खोज हुई। एक्स-रे की खोज विल्हेम रॉन्टगन ने 1895 में की थी। अवरक्त किरणों की खोज विलियम हर्शेल ने 1800 में की थी।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "18",
                    question_en: "<p>11. A school teacher asks students to stand up every time national anthem is sung.&nbsp;Which Article enshrines this fundamental duty?</p>",
                    question_hi: "<p>11. एक स्कूल शिक्षक हर बार राष्ट्रगान गाए जाने पर छात्रों को खड़े होने के लिए कहता है। कौन-सा अनुच्छेद इस मौलिक कर्तव्य को सुनिश्चित करता है?</p>",
                    options_en: ["<p>Article 51A (a)</p>", "<p>Article 51 (b)</p>", 
                                "<p>Article 51 (d)</p>", "<p>Article 51 (c)</p>"],
                    options_hi: ["<p>अनुच्छेद 51A (a)</p>", "<p>अनुच्छेद 51 (b)</p>",
                                "<p>अनुच्छेद 51 (d)</p>", "<p>अनुच्छेद 51 &copy;</p>"],
                    solution_en: "<p>11.(a) <strong>Article 51A (a).</strong> This article states that it is the duty of every citizen of India \"to abide by the Constitution and respect its ideals and institutions, the National Flag, and the National Anthem.\" Article 51A, added by the 42nd Amendment in 1976, lists the Fundamental Duties of Indian citizens, including respecting the National Anthem.</p>",
                    solution_hi: "<p>11.(a) <strong>अनुच्छेद 51A (a).</strong> इस अनुच्छेद में कहा गया है कि भारत के प्रत्येक नागरिक का यह कर्तव्य है कि वह \"संविधान का पालन करे और उसके आदर्शों और संस्थाओं, राष्ट्रीय ध्वज और राष्ट्रगान का सम्मान करे।\" 1976 में 42वें संशोधन द्वारा जोड़ा गया अनुच्छेद 51A, राष्ट्र गान का सम्मान करने साथ हीं भारतीय नागरिकों के मौलिक कर्तव्यों को सूचीबद्ध करता है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "18",
                    question_en: "<p>12. According to the National Multidimensional poverty index 2021, which of the following states has the lowest percentage of population being multidimensional poor?</p>",
                    question_hi: "<p>12. राष्ट्रीय बहुआयामी गरीबी सूचकांक 2021 के अनुसार, निम्नलिखित में से किस राज्य की जनसंख्या का&nbsp;बहुआयामी गरीब प्रतिशत सबसे कम है?</p>",
                    options_en: ["<p>Punjab</p>", "<p>Maharashtra</p>", 
                                "<p>Sikkim</p>", "<p>Bihar</p>"],
                    options_hi: ["<p>पंजाब</p>", "<p>महाराष्ट्र</p>",
                                "<p>सिक्किम</p>", "<p>बिहार</p>"],
                    solution_en: "<p>12.(c) <strong>Sikkim</strong>. According to the National Multidimensional Poverty Index 2021, Sikkim has the lowest percentage of people experiencing multidimensional poverty, at only 5.27%. In contrast, Bihar has the highest percentage, with 33.76% of its population being multidimensionally poor.</p>",
                    solution_hi: "<p>12.(c) <strong>सिक्किम</strong>। राष्ट्रीय बहुआयामी गरीबी सूचकांक 2021 के अनुसार, सिक्किम में बहुआयामी गरीबी का प्रतिशत सबसे कम है, जो कि केवल 5.27% है। इसके विपरीत, बिहार में सबसे अधिक प्रतिशत है, जहाँ 33.76% आबादी बहुआयामी रूप से गरीब है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "18",
                    question_en: "<p>13. Who founded a reformist organisation called &lsquo;Brahmo Samaj&rsquo; in Calcutta?</p>",
                    question_hi: "<p>13. कलकत्ता में \'ब्रह्म समाज\' नामक सुधारवादी संगठन की स्थापना किसने की?</p>",
                    options_en: ["<p>Jyotiba Phule</p>", "<p>Vinoba Bhave</p>", 
                                "<p>Ishwar Chandra Vidyasagar</p>", "<p>Raja Ram Mohan Roy</p>"],
                    options_hi: ["<p>ज्योतिबा फुले</p>", "<p>विनोबा भावे</p>",
                                "<p>ईश्वर चंद्र विद्यासागर</p>", "<p>राजा राम मोहन राय</p>"],
                    solution_en: "<p>13.(d) <strong>Raja Ram Mohan Roy. </strong>Brahmo Samaj was founded in 1828 in Calcutta (now Kolkata). Jyotiba Phule founded the Satyashodhak Samaj in 1873 in Maharashtra. The Bhoodan Movement (Land Gift Movement) was started by Vinoba Bhave in 1951.</p>",
                    solution_hi: "<p>13.(d) <strong>राजा राम मोहन राय।</strong> ब्रह्म समाज की स्थापना 1828 में कलकत्ता (अब कोलकाता) में हुई थी। ज्योतिबा फुले ने 1873 में महाराष्ट्र में सत्यशोधक समाज की स्थापना की। भूदान आंदोलन (भूमि उपहार आंदोलन) 1951 में विनोबा भावे द्वारा शुरू किया गया था।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "18",
                    question_en: "<p>14. The Government of India&rsquo;s Ministry of Culture organised a programme called Vitasta in January 2023. This programme exhibits rich culture, arts and crafts of which region?</p>",
                    question_hi: "<p>14. भारत सरकार के संस्कृति मंत्रालय ने जनवरी 2023 में वितस्ता (Vitasta) नामक एक कार्यक्रम का आयोजन किया। यह कार्यक्रम किस क्षेत्र की समृद्ध संस्कृति, कला और शिल्प को प्रदर्शित करता है?</p>",
                    options_en: ["<p>Ladakh</p>", "<p>Kashmir</p>", 
                                "<p>Himachal</p>", "<p>Punjab</p>"],
                    options_hi: ["<p>लद्दाख</p>", "<p>कश्मीर</p>",
                                "<p>हिमाचल</p>", "<p>पंजाब</p>"],
                    solution_en: "<p>14.(b) <strong>Kashmir</strong>. The name \"Vitasta\" refers to the ancient Sanskrit name for the Jhelum River, which flows through the Kashmir Valley, symbolizing the region\'s cultural heritage. The event highlighted Kashmir\'s traditions, music, handicrafts, and more, aiming to promote and celebrate the unique identity and history of the region.</p>",
                    solution_hi: "<p>14.(b) <strong>कश्मीर</strong>। \"वितस्ता\" नाम झेलम नदी के प्राचीन संस्कृत नाम को संदर्भित करता है, जो कश्मीर घाटी से होकर बहती है, जो इस क्षेत्र की सांस्कृतिक विरासत का प्रतीक है। इस कार्यक्रम में कश्मीर की परंपराओं, संगीत, हस्तशिल्प और अन्य चीजों पर प्रकाश डाला गया, जिसका उद्देश्य क्षेत्र की अनूठी पहचान और इतिहास को बढ़ावा देना और उसका जश्न मनाना था।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "18",
                    question_en: "<p>15. Indian Railway was introduced in the year __________.</p>",
                    question_hi: "<p>15. भारतीय रेलवे की शुरुआत वर्ष__________ में हुई थी।</p>",
                    options_en: ["<p>1853</p>", "<p>1851</p>", 
                                "<p>1852</p>", "<p>1850</p>"],
                    options_hi: ["<p>1853</p>", "<p>1851</p>",
                                "<p>1852</p>", "<p>1850</p>"],
                    solution_en: "<p>15.(a) <strong>1853</strong>. Indian Railways started the first passenger train on April 16, 1853, from Bori Bunder to Thane covering a distance of 34 km. It was dedicated by Lord Dalhousie. The train consists of 14 carriages and was pulled by three steam locomotive engines namely Sahib, Sindh, and Sultan.</p>",
                    solution_hi: "<p>15.(a) <strong>1853</strong>. भारतीय रेलवे ने 16 अप्रैल, 1853 को बोरी बंदर से ठाणे तक 34 किमी की दूरी तय करने वाली पहली यात्री ट्रेन शुरू की थी। इसे लॉर्ड डलहौजी ने समर्पित किया था। इस रेलगाड़ी में 14 डिब्बे थे, और इसे तीन भाप इंजन साहिब, सिंध और सुल्तान द्वारा खींचा गया था।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "16",
                    section: "18",
                    question_en: "<p>16. Which state secured over all third position in the medals tally at the 36th National Games (2022) of India?</p>",
                    question_hi: "<p>16. भारत के 36वें राष्ट्रीय खेलों (2022) में किस राज्य ने पदक तालिका में तीसरा स्थान हासिल किया था ?</p>",
                    options_en: ["<p>Kerala</p>", "<p>Maharashtra</p>", 
                                "<p>Delhi</p>", "<p>Haryana</p>"],
                    options_hi: ["<p>केरल</p>", "<p>महाराष्ट्र</p>",
                                "<p>दिल्ली</p>", "<p>हरियाणा</p>"],
                    solution_en: "<p>16.(d) <strong>Haryana</strong>. The 36th National Games (2022) of India medals tally: Services secured the first position with 128 medals (61 gold, 35 silver, and 32 bronze). Maharashtra secured the second position with 140 medals (39 gold, 38 silver, and 63 bronze).</p>",
                    solution_hi: "<p>16.(d) <strong>हरियाणा</strong>। भारत के 36वें राष्ट्रीय खेल (2022) की पदक तालिका: सर्विसेज ने 128 पदक (61 स्वर्ण, 35 रजत और 32 कांस्य) के साथ पहला स्थान प्राप्त किया। महाराष्ट्र ने 140 (39 स्वर्ण, 38 रजत और 63 कांस्य) के साथ दूसरा स्थान प्राप्त किया।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "17",
                    section: "18",
                    question_en: "<p>17. In which year did the Indian football team go on its first known official international tour?</p>",
                    question_hi: "<p>17. भारतीय फुटबॉल टीम अपने पहले ज्ञात आधिकारिक अंतर्राष्ट्रीय दौरे पर किस वर्ष गई थी?</p>",
                    options_en: ["<p>1947</p>", "<p>1950</p>", 
                                "<p>1914</p>", "<p>1924</p>"],
                    options_hi: ["<p>1947</p>", "<p>1950</p>",
                                "<p>1914</p>", "<p>1924</p>"],
                    solution_en: "<p>17.(d) <strong>1924</strong>. An Indian team, comprising both Indian and British players, visited Sri Lanka (Ceylon at the time) for what was the Indian football team&rsquo;s first-ever official foreign tour. Legendary footballer Gostha Pal, regarded as the first captain of the Indian football team, led the squad during the trip.</p>",
                    solution_hi: "<p>17.(d) <strong>1924</strong>. भारतीय और ब्रिटिश दोनों खिलाड़ियों वाली एक भारतीय टीम श्रीलंका (उस समय सीलोन) गई थी, जो भारतीय फुटबॉल टीम का पहला आधिकारिक विदेशी दौरा था। इस दौरे में भारतीय फुटबॉल टीम के पहले कप्तान माने जाने वाले दिग्गज फुटबॉलर गोस्था पाल ने टीम का नेतृत्व किया था।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "18",
                    section: "18",
                    question_en: "<p>18. When a new text document is created in MS-Office 2016, the default text size of the document is _____.</p>",
                    question_hi: "<p>18. जब एमएस-ऑफिस (MS-Office) 2016 में एक नया टेक्स्ट डॉक्यूमेंट बनाया जाता है, तो डॉक्यूमेंट का&nbsp;डिफ़ॉल्ट टेक्स्ट साइज _____ होता है।</p>",
                    options_en: ["<p>14 points</p>", "<p>10 points</p>", 
                                "<p>12 points</p>", "<p>11 points</p>"],
                    options_hi: ["<p>14 पॉइंट्स</p>", "<p>10 पॉइंट्स</p>",
                                "<p>12 पॉइंट्स</p>", "<p>11 पॉइंट्स</p>"],
                    solution_en: "<p>18.(d) <strong>11 points.</strong> 11 point size means it is approximately 0.152778 inches. 72 points size means the text size equals 1 inch. The default font style in MS Word 2016 is Calibri. The default font and size can be changed in the Word Options or by selecting a different font or size for the document.</p>",
                    solution_hi: "<p>18.(d)<strong> 11 पॉइंट्स।</strong> 11 पॉइंट्स साइज़ का अर्थ है कि यह लगभग 0.152778 इंच है। 72 पॉइंट्स साइज़ का अर्थ है कि टेक्स्ट का आकार 1 इंच के बराबर है। MS Word 2016 में डिफ़ॉल्ट फ़ॉन्ट स्टाइल कैलिब्री (Calibri) है। डिफ़ॉल्ट फ़ॉन्ट और साइज को Word विकल्प में या डाक्यूमेंट्स के लिए कोई अलग फ़ॉन्ट या साइज सेलेक्ट करके बदला जा सकता है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "19",
                    section: "18",
                    question_en: "<p>19. Which state organises the Lokrang Festival?</p>",
                    question_hi: "<p>19. लोकरंग महोत्सव का आयोजन कौन-सा राज्य करता है?</p>",
                    options_en: ["<p>Arunachal Pradesh</p>", "<p>Himachal Pradesh</p>", 
                                "<p>Uttar Pradesh</p>", "<p>Madhya Pradesh</p>"],
                    options_hi: ["<p>अरुणाचल प्रदेश</p>", "<p>हिमाचल प्रदेश</p>",
                                "<p>उत्तरप्रदेश</p>", "<p>मध्य प्रदेश</p>"],
                    solution_en: "<p>19.(d) <strong>Madhya Pradesh.</strong> It is celebrated in Bhopal. This five-day festival takes place every year on January 26, which is Republic Day. More festivals of Madhya Pradesh : Khajuraho Dance Festival, Tansen Music Festival, Bhagoria Festival, Nagaji Fair, Malwa Utsav, Mandu Festival, Hori Festival (Tribal Holi), Kailash Fair, Chethiyagiri Vihara Festival.</p>",
                    solution_hi: "<p>19.(d) <strong>मध्य प्रदेश। </strong>यह भोपाल में मनाया जाता है। यह पांच दिवसीय उत्सव हर साल 26 जनवरी यानी गणतंत्र दिवस पर होता है। मध्य प्रदेश के और त्यौहार: खजुराहो नृत्य महोत्सव, तानसेन संगीत समारोह, भगोरिया महोत्सव, नागाजी मेला, मालवा उत्सव, मांडू महोत्सव, होरी महोत्सव (आदिवासी होली), कैलाश मेला, चेतियागिरी विहार महोत्सव।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "20",
                    section: "18",
                    question_en: "<p>20. Which cost is considered for calculating the national income in India?</p>",
                    question_hi: "<p>20. भारत में राष्ट्रीय आय की गणना के लिए किस लागत पर विचार किया जाता है ?</p>",
                    options_en: ["<p>Product cost</p>", "<p>Factor cost</p>", 
                                "<p>Market cost</p>", "<p>Sunk cost</p>"],
                    options_hi: ["<p>उत्पाद लागत</p>", "<p>कारक लागत</p>",
                                "<p>बाज़ार लागत</p>", "<p>विफल लागत</p>"],
                    solution_en: "<p>20.(b) <strong>Factor cost </strong>refers to the cost of factors of production such as labor, capital, and entrepreneurship. Market cost: The cost of a product or service in the market, influenced by supply and demand. Sunk cost: A cost that has already been incurred and cannot be changed or avoided.</p>",
                    solution_hi: "<p>20.(b) <strong>कारक लागत </strong>से तात्पर्य उत्पादन के कारकों जैसे श्रम, पूंजी और उद्यमशीलता की लागत से है। बाजार लागत: बाजार में किसी उत्पाद या सेवा की लागत, जो आपूर्ति और मांग से प्रभावित होती है। विफल लागत: एक लागत जो पहले ही खर्च हो चुकी है और जिसे बदला या टाला नहीं जा सकता।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "21",
                    section: "18",
                    question_en: "<p>21. Which of the following types of crops are sown at the beginning of the monsoon season?</p>",
                    question_hi: "<p>21. निम्नलिखित में से किस प्रकार की फसलें मानसून के मौसम की शुरुआत में बोई जाती हैं?</p>",
                    options_en: ["<p>Rabi crops</p>", "<p>Kharif crops</p>", 
                                "<p>Vital crops</p>", "<p>Zaid crops</p>"],
                    options_hi: ["<p>रबी की फसलें</p>", "<p>खरीफ की फसलें</p>",
                                "<p>महत्वपूर्ण फसलें</p>", "<p>जायद की फसलें</p>"],
                    solution_en: "<p>21.(b) <strong>Kharif crops.</strong> These crops are typically planted during June to September and harvested in the post-monsoon season (October to November). Examples of Kharif crops include rice, maize, cotton, and sorghum. Rabi crops are sown after the monsoon season, usually in the winter months (October to December), and harvested in the spring (March to May). Zaid crops are grown in the intervening period between Kharif and Rabi seasons, typically in the summer months (March to June).</p>",
                    solution_hi: "<p>21.(b) <strong>खरीफ की फसलें। </strong>ये फसलें आमतौर पर जून से सितंबर के दौरान बोई जाती हैं और मानसून के बाद (अक्टूबर से नवंबर) में काटी जाती हैं। खरीफ फसलों के उदाहरणों में चावल, मक्का, कपास और ज्वार शामिल हैं। रबी की फसलें मानसून के मौसम के बाद बोई जाती हैं, आमतौर पर सर्दियों के महीनों (अक्टूबर से दिसंबर) में, और वसंत (मार्च से मई) में काटी जाती हैं। जायद फसलें ख़रीफ़ और रबी मौसम के बीच की अवधि में, आमतौर पर गर्मी के महीनों (मार्च से जून) में उगाई जाती हैं।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "22",
                    section: "18",
                    question_en: "<p>22. Which of the following is an input device?</p>",
                    question_hi: "<p>22. निम्नलिखित में से कौन-सी, एक इनपुट डिवाइस है?</p>",
                    options_en: ["<p>Keyboard</p>", "<p>Speaker</p>", 
                                "<p>Monitor</p>", "<p>Printer</p>"],
                    options_hi: ["<p>कीबोर्ड</p>", "<p>स्पीकर</p>",
                                "<p>मॉनीटर</p>", "<p>प्रिंटर</p>"],
                    solution_en: "<p>22.(a) <strong>Keyboard</strong>. Input devices provide data to the computer, while output devices receive data from the computer and present it to the user. Examples of Input devices : keyboards, computer mice, scanners, cameras, joysticks, and microphones, etc. Examples of Output devices : Monitor, Printers, Headphone, Computer Speaker, Projector.</p>",
                    solution_hi: "<p>22.(a) <strong>कीबोर्ड</strong>। इनपुट डिवाइस कंप्यूटर को डेटा प्रदान करते हैं, जबकि आउटपुट डिवाइस कंप्यूटर से डेटा प्राप्त करते हैं और इसे उपयोगकर्ता को प्रस्तुत करते हैं। इनपुट डिवाइस के उदाहरण: कीबोर्ड, कंप्यूटर माउस, स्कैनर, कैमरा, जॉयस्टिक और माइक्रोफोन आदि। आउटपुट डिवाइस के उदाहरण: मॉनिटर, प्रिंटर, हेडफोन, कंप्यूटर स्पीकर, प्रोजेक्टर।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "23",
                    section: "18",
                    question_en: "<p>23. Why is the six membered cyclic structure of glucose called a pyranose structure?</p>",
                    question_hi: "<p>23. ग्लूकोज की छह अवयवों वाली चक्रीय संरचना को पाइरानोज संरचना (pyranose structure) क्यों कहा जाता है?</p>",
                    options_en: ["<p>Pyran is a non-cyclic organic compound, with one oxygen atom and five carbon atoms in the ring.</p>", "<p>Furan is a five membered cyclic compound, with one oxygen and four carbon atoms.</p>", 
                                "<p>Pyran is a cyclic organic compound, with one oxygen atom and five carbon atoms in the ring.</p>", "<p>Furan is a five membered non-cyclic compound, with one oxygen and four carbon atoms.</p>"],
                    options_hi: ["<p>पाइरान एक गैर-चक्रीय कार्बनिक यौगिक है, जिसमें एक ऑक्सीजन परमाणु और पांच कार्बन परमाणु रिंग में होते हैं।</p>", "<p>फ्यूरान एक पांच अवयव वाला चक्रीय यौगिक है, जिसमें एक ऑक्सीजन और चार कार्बन परमाणु होते हैं।</p>",
                                "<p>पाइरान एक चक्रीय कार्बनिक यौगिक है, जिसमें एक ऑक्सीजन परमाणु और पांच कार्बन परमाणु रिंग में होते हैं।</p>", "<p>फ्यूरान एक पांच अवयव वाला गैर-चक्रीय यौगिक है, जिसमें एक ऑक्सीजन और चार कार्बन परमाणु होते हैं।</p>"],
                    solution_en: "<p>23.(c) The six membered cyclic structure of glucose is called pyranose structure in analogy with pyran. Pyran is a cyclic organic compound with one oxygen atom and five carbon atoms in the ring. The cyclic structure of glucose is more correctly represented by the Haworth structure.</p>",
                    solution_hi: "<p>23.(c) ग्लूकोज की छ: अवयव की चक्रीय संरचना को पाइरानोज संरचना कहा जाता है, जो पाइरान के अनुरूप है। पाइरान एक चक्रीय कार्बनिक यौगिक है, जिसमें एक ऑक्सीजन परमाणु और पांच कार्बन परमाणु होते हैं। ग्लूकोज की चक्रीय संरचना को हॉवर्थ संरचना द्वारा अधिक सही ढंग से दर्शाया जाता है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "24",
                    section: "18",
                    question_en: "<p>24. Recently, a bill on Uniform Civil Code (UCC) was introduced. Consider the following&nbsp;statements about UCC and identify the correct statement(s) from them.<br>1. The Uniform Civil Code in India Bill, 2020, was introduced in the Lok Sabha as a private member bill.<br>2. UCC refers to the same set of civil laws applicable to all the citizens of India in all the personal matters.<br>3. The provision for UCC in the Constitution of India is mentioned under Article 44.</p>",
                    question_hi: "<p>24. हाल ही में, समान नागरिक संहिता (UCC) पर एक विधेयक प्रस्तुत किया गया था। समान नागरिक संहिता (UCC) के बारे में निम्नलिखित कथनों पर विचार कीजिए और उनमें से सही कथन/कथनों की पहचान कीजिए।<br>1. भारत में समान नागरिक संहिता विधेयक, 2020 को लोकसभा में निजी सदस्य विधेयक के रूप में प्रस्तुत किया गया।<br>2. समान नागरिक संहिता, सभी व्यक्तिगत मामलों में भारत के सभी नागरिकों के लिए लागू नागरिक कानूनों के समान समुचय को संदर्भित करता है।<br>3. भारत के संविधान में समान नागरिक संहिता के प्रावधान का उल्लेख अनुच्छेद 44 के तहत किया गया है।</p>",
                    options_en: ["<p>Only 1</p>", "<p>Only 3</p>", 
                                "<p>Only 1 and 3</p>", "<p>Only 2 and 3</p>"],
                    options_hi: ["<p>केवल 1</p>", "<p>केवल 3</p>",
                                "<p>केवल 1 और 3</p>", "<p>केवल 2 और 3</p>"],
                    solution_en: "<p>24.(d) <strong>Only 2 and 3. </strong>UCC (Uniform Civil Code) refers to a single set of civil laws for all Indian citizens covering personal matters like marriage, inheritance, and adoption. It is mandated under Article 44 of the Indian Constitution, which directs the state to ensure a uniform civil code.</p>",
                    solution_hi: "<p>24.(d)<strong> केवल 2 और 3</strong>. UCC (समान नागरिक संहिता) सभी भारतीय नागरिकों के लिए नागरिक कानूनों के एक समूह को संदर्भित करता है, जिसमें वंशानुक्रम और दत्तक ग्रहण जैसे व्यक्तिगत मामले शामिल हैं। यह भारतीय संविधान के अनुच्छेद 44 के तहत अनिवार्य है, जो राज्य को एक समान नागरिक संहिता सुनिश्चित करने का निर्देश देता है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "25",
                    section: "misc",
                    question_en: "<p>25. Western disturbances are responsible for:</p>",
                    question_hi: "<p>25. पश्चिमी विक्षोभ __________ के लिए उत्तरदायी है।</p>",
                    options_en: ["<p>dusty storms in eastern costal area of India</p>", "<p>hot winds during summers in northern India</p>", 
                                "<p>extremely chilled weather of north-east India</p>", "<p>rainfall during winters in north-western part of India</p>"],
                    options_hi: ["<p>भारत के पूर्व तटीय क्षो में धूल भरी आंधी</p>", "<p>उत्तरी भारत में गर्मियों में गर्म हवाएँ</p>",
                                "<p>उत्तर-पूर्व भारत में अत्यंत सर्द मौसम</p>", "<p>भारत के उत्तर-पश्चिमी भाग में सर्दियों में वर्षा</p>"],
                    solution_en: "<p>25.(d) The western cyclonic disturbances which enter the Indian subcontinent from the west and the northwest during the winter months, originate over the Mediterranean Sea and are brought into India by the westerly jet stream.</p>",
                    solution_hi: "<p>25.(d) पश्चिमी चक्रवाती विक्षोभ, जो शीतकाल के महीनों में पश्चिम और उत्तर-पश्चिम से भारतीय उपमहाद्वीप में प्रवेश करते हैं, भूमध्य सागर के ऊपर उत्पन्न होते हैं तथा पश्चिमी जेट स्ट्रीम द्वारा भारत में लाए जाते हैं।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>