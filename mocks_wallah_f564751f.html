<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">15:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 15</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">15</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 15 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 0,
                end: 13
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 14,
                end: 14
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "17",
                    question_en: "<p>1. Rashid sells a book at a gain of 20%. If he had sold it at ₹34.51 more, he would have gained 37%. The cost price (in ₹) of the book is:</p>",
                    question_hi: "<p>1. राशिद एक पुस्तक को 20% के लाभ पर बेचता है। यदि उसने इसे ₹34.51 अधिक पर बेचा होता, तो उसे 37% का लाभ होता। पुस्तक का क्रय मूल्य (₹ में) ज्ञात करें।</p>",
                    options_en: ["<p>210</p>", "<p>207</p>", 
                                "<p>203</p>", "<p>220</p>"],
                    options_hi: ["<p>210</p>", "<p>207</p>",
                                "<p>203</p>", "<p>220</p>"],
                    solution_en: "<p>1.(c)<br>Let CP of book be 100%<br>(37 - 20)% = 17% = ₹34.51<br>100% = <math display=\"inline\"><mfrac><mrow><mn>34</mn><mo>.</mo><mn>51</mn></mrow><mrow><mn>17</mn></mrow></mfrac></math> &times; 100 = ₹203</p>",
                    solution_hi: "<p>1.(c)<br>माना पुस्तक का क्रय मूल्य = 100%<br>(37 - 20)% = 17% = ₹34.51<br>100% = <math display=\"inline\"><mfrac><mrow><mn>34</mn><mo>.</mo><mn>51</mn></mrow><mrow><mn>17</mn></mrow></mfrac></math> &times; 100 = ₹203</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "17",
                    question_en: "<p>2. An iron costing ₹758 was sold by Ramesh at a gain of 15%, and it was again sold by Reena, who bought it from Ramesh, at a loss of 8%. Find the selling price of the iron for Reena (correct to two places of decimals).</p>",
                    question_hi: "<p>2. एक इस्&zwj;त्री की कीमत ₹758 थी जिसे रमेश ने 15% के लाभ पर बेचा और इसे रीना ने 8% की हानि पर&nbsp;पुन: बेच दिया, जिसने इसे रमेश से खरीदा था। रीना के लिए इस्&zwj;त्री का विक्रय मूल्य (दशमलव के दो स्थान तक सही) ज्ञात करें।</p>",
                    options_en: ["<p>₹810.96</p>", "<p>₹808.69</p>", 
                                "<p>₹820.69</p>", "<p>₹801.96</p>"],
                    options_hi: ["<p>₹810.96</p>", "<p>₹808.69</p>",
                                "<p>₹820.69</p>", "<p>₹801.96</p>"],
                    solution_en: "<p>2.(d)<br>SP of the iron for Reena = 758 &times; <math display=\"inline\"><mfrac><mrow><mn>23</mn></mrow><mrow><mn>20</mn></mrow></mfrac><mi>&#160;</mi></math>&times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>23</mn><mn>25</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>4</mn><mo>,</mo><mn>00</mn><mo>,</mo><mn>982</mn></mrow><mn>500</mn></mfrac></math> = ₹ 801.96</p>",
                    solution_hi: "<p>2.(d)<br>रीना के लिए इस्&zwj;त्री का विक्रय मूल्य = 758 &times; <math display=\"inline\"><mfrac><mrow><mn>23</mn></mrow><mrow><mn>20</mn></mrow></mfrac><mi>&#160;</mi></math>&times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>23</mn><mn>25</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>4</mn><mo>,</mo><mn>00</mn><mo>,</mo><mn>982</mn></mrow><mn>500</mn></mfrac></math> = ₹ 801.96</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "17",
                    question_en: "<p>3. Rameshwar sold a bike at a loss of 15%. If the selling price had been increased by ₹3,375, there would have been a gain of 12%. Find the cost price of the bike.</p>",
                    question_hi: "<p>3. रामेश्वर ने एक बाइक 15% की हानि पर बेची। यदि विक्रय मूल्य में ₹3,375 की वृद्धि की गई होती, तो&nbsp;12% का लाभ होता। बाइक का क्रय मूल्य ज्ञात करें।</p>",
                    options_en: ["<p>₹25,100</p>", "<p>₹25,200</p>", 
                                "<p>₹12,500</p>", "<p>₹15,200</p>"],
                    options_hi: ["<p>₹25,100</p>", "<p>₹25,200</p>",
                                "<p>₹12,500</p>", "<p>₹15,200</p>"],
                    solution_en: "<p>3.(c)<br>Let CP of the bike be 100%<br>&rArr; 12 - (-15) = 27% = ₹3375<br>&rArr; 100% = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3375</mn><mn>27</mn></mfrac></math> &times; 100 = ₹12500</p>",
                    solution_hi: "<p>3.(c)<br>माना कि बाइक का क्रय मूल्य = 100% <br>&rArr; 12 - (-15) = 27% = ₹3375<br>&rArr; 100% = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3375</mn><mn>27</mn></mfrac></math> &times; 100 = ₹12500</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "17",
                    question_en: "<p>4. If the cost price of a table is ₹400 and the selling price is ₹600, then find the profit percentage.</p>",
                    question_hi: "<p>4. यदि एक मेज का क्रय मूल्य ₹400 और विक्रय मूल्य ₹600 है, तो लाभ प्रतिशत ज्ञात कीजिए।</p>",
                    options_en: ["<p>55%</p>", "<p>45%</p>", 
                                "<p>33%</p>", "<p>50%</p>"],
                    options_hi: ["<p>55%</p>", "<p>45%</p>",
                                "<p>33%</p>", "<p>50%</p>"],
                    solution_en: "<p>4.(d)<br>We know<br>Profit% = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>SP</mi><mo>-</mo><mi>CP</mi></mrow><mrow><mi>CP</mi><mi mathvariant=\"normal\">&#160;</mi></mrow></mfrac></math> &times; 100<br>Given,<br>CP = 400<br>SP = 600 <br>Profit% = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>600</mn><mo>-</mo><mn>400</mn></mrow><mrow><mn>400</mn><mi>&#160;</mi></mrow></mfrac></math> &times; 100<br>= <math display=\"inline\"><mfrac><mrow><mn>200</mn></mrow><mrow><mn>400</mn><mi>&#160;</mi></mrow></mfrac></math> &times; 100 = 50%.</p>",
                    solution_hi: "<p>4.(d)<br>हम जानते है <br>लाभ % = <math style=\"font-family:Verdana\" display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"true\"><mfrac><mrow><mi>&#2357;&#2367;&#2325;&#2381;&#2352;&#2351;</mi><mo>&#160;</mo><mi>&#2350;&#2370;&#2354;&#2381;&#2351;</mi><mo>-</mo><mi>&#2325;&#2381;&#2352;&#2351;</mi><mo>&#160;</mo><mi>&#2350;&#2370;&#2354;&#2381;&#2351;</mi></mrow><mrow><mi>&#2325;&#2381;&#2352;&#2351;</mi><mo>&#160;</mo><mi>&#2350;&#2370;&#2354;&#2381;&#2351;</mi><mo>&#160;</mo></mrow></mfrac></mstyle></math> &times; 100<br>दिया है ,<br>क्रय मूल्य = 400<br>विक्रय मूल्य = 600 <br>लाभ % = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>600</mn><mo>-</mo><mn>400</mn></mrow><mrow><mn>400</mn><mi>&#160;</mi></mrow></mfrac></math>&nbsp;&times; 100<br>= <math display=\"inline\"><mfrac><mrow><mn>200</mn></mrow><mrow><mn>400</mn><mi>&#160;</mi></mrow></mfrac></math> &times; 100 = 50%.</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "17",
                    question_en: "<p>5. A shopkeeper expects a gain of 36% on his cost price. If in a month, his sale was ₹6,14,856, then what was his profit (in ₹)?</p>",
                    question_hi: "<p>5. एक दुकानदार अपने क्रय मूल्य पर 36% लाभ की उम्मीद करता है। यदि एक महीने में उसकी बिक्री ₹6,14,856 थी, तो उसका लाभ (₹ में) कितना था?</p>",
                    options_en: ["<p>1,61,235</p>", "<p>1,63,900</p>", 
                                "<p>1,62,756</p>", "<p>1,62,800</p>"],
                    options_hi: ["<p>1,61,235</p>", "<p>1,63,900</p>",
                                "<p>1,62,756</p>", "<p>1,62,800</p>"],
                    solution_en: "<p>5.(c)<br>Gain % = 36 %<br>If CP = 100 unit &rArr; SP = 136 unit<br>Given, SP = ₹ 6,14,856<br>&rArr; 136 unit = ₹ 6,14,856<br>&rArr; 1 unit = ₹ 4521<br>CP = 100 &times;&nbsp;4521 = ₹ 452100<br>Hence, Profit = SP - CP<br>= 614856 - 452100 = ₹ 1,62,756</p>",
                    solution_hi: "<p>5.(c)<br>लाभ% = 36 %<br>यदि क्रय मूल्य = 100 इकाई &rArr; विक्रय मूल्य = 136 इकाई <br>दिया गया है, विक्रय मूल्य = ₹ 6,14,856<br>&rArr; 136 इकाई = 6,14,856<br>&rArr; 1 इकाई = 4521<br>क्रय मूल्य = 100 &times;&nbsp;4521 = ₹ 452100<br>अतः लाभ = विक्रय मूल्य - क्रय मूल्य<br>= 614856 - 452100 <br>= ₹ 1,62,756</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "17",
                    question_en: "<p>6. A salesman purchases goods at ₹1,250 and is forced to sell it at ₹1,000. Find his loss percentage.</p>",
                    question_hi: "<p>6. एक सेल्समैन ₹1,250 में सामान खरीदता है और उसे ₹1,000 में बेचने के लिए मजबूर किया जाता है। उसका हानि प्रतिशत ज्ञात कीजिए।</p>",
                    options_en: ["<p>25%</p>", "<p>20%</p>", 
                                "<p>30%</p>", "<p>15%</p>"],
                    options_hi: ["<p>25%</p>", "<p>20%</p>",
                                "<p>30%</p>", "<p>15%</p>"],
                    solution_en: "<p>6.(b)<br>Given :<br>CP = 1,250<br>SP = 1,000<br>Hence, loss = CP - SP = 1250 - 1000 = 250<br>Loss % = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>loss</mi><mrow><mi>CP</mi><mi mathvariant=\"normal\">&#160;</mi></mrow></mfrac></math> &times; 100 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>250</mn><mn>1250</mn></mfrac></math> &times; 100 = 20 %</p>",
                    solution_hi: "<p>6.(b)<br>दिया गया है,<br>क्रय मूल्य = 1,250<br>विक्रय मूल्य = 1,000 <br>अतः, हानि = क्रय मूल्य - विक्रय मूल्य = 1250 - 1000 = 250<br>हानि % = <math style=\"font-family:Verdana\" display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"true\"><mfrac><mrow><mi>&#2361;&#2366;&#2344;&#2367;</mi><mo>&#160;</mo><mo>&#160;</mo></mrow><mrow><mi>&#2325;&#2381;&#2352;&#2351;</mi><mo>&#160;</mo><mi>&#2350;&#2370;&#2354;&#2381;&#2351;</mi></mrow></mfrac></mstyle></math> &times; 100 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>250</mn><mn>1250</mn></mfrac></math> &times; 100 = 20 %</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "17",
                    question_en: "<p>7. A shopkeeper marks the price of his goods at 20% higher than the cost price and allows a discount of 10%. What is his profit percentage?</p>",
                    question_hi: "<p>7. एक दुकानदार अपने सामान पर क्रय मूल्य से 20% अधिक मूल्&zwj;य अंकित करता है और 10% की छूट&nbsp;देता है। उसका लाभ प्रतिशत क्या है?</p>",
                    options_en: ["<p>10%</p>", "<p>6%</p>", 
                                "<p>8%</p>", "<p>4%</p>"],
                    options_hi: ["<p>10%</p>", "<p>6%</p>",
                                "<p>8%</p>", "<p>4%</p>"],
                    solution_en: "<p>7.(c)<br>Let CP = 100<br>MP = 120<br>SP = 120 &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>9</mn><mn>10</mn></mfrac></math> = 108<br>&rArr; Profit % = <math xmlns=\"http://www.w3.org/1998/Math/MathML\" class=\"wrs_chemistry\"><mfrac><mrow><mi>SP</mi><mo>-</mo><mi>CP</mi><mo>&#160;</mo></mrow><mi>CP</mi></mfrac></math> &times; 100<br>&rArr; Profit % = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>108</mn><mo>-</mo><mn>100</mn></mrow><mn>100</mn></mfrac></math> &times; 100<br>&rArr; Profit % = 8 %.</p>",
                    solution_hi: "<p>7.(c)<br>माना विक्रय मूल्य = 100<br>अंकित मूल्य = 120<br>विक्रय मूल्य = 120 &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>9</mn><mn>10</mn></mfrac></math> = 108<br>&rArr; लाभ % = <math style=\"font-family:Verdana\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2357;&#2367;&#2325;&#2381;&#2352;&#2351;</mi><mo>&#160;</mo><mi>&#2350;&#2370;&#2354;&#2381;&#2351;</mi><mo>-</mo><mi>&#2325;&#2381;&#2352;&#2351;</mi><mo>&#160;</mo><mi>&#2350;&#2370;&#2354;&#2381;&#2351;</mi><mo>&#160;</mo></mrow><mrow><mi>&#2325;&#2381;&#2352;&#2351;</mi><mo>&#160;</mo><mi>&#2350;&#2370;&#2354;&#2381;&#2351;</mi></mrow></mfrac></math> &times; 100<br>&rArr; लाभ % = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>108</mn><mo>-</mo><mn>100</mn></mrow><mn>100</mn></mfrac></math> &times; 100<br>&rArr; लाभ % = 8 %</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "17",
                    question_en: "<p>8. The percentage profit earned by selling an article for Rs.2,100 is equal to the percentage loss incurred by selling the same article for Rs.1,460. At what price should the article be sold to make 20% profit?</p>",
                    question_hi: "<p>8. एक वस्तु को Rs.2,100 में बेचने पर अर्जित प्रतिशत लाभ उसी वस्तु को Rs.1,460 में बेचने पर हुई&nbsp;प्रतिशत हानि के बराबर है। 20% लाभ कमाने के लिए वस्तु को किस कीमत पर बेचा जाना चाहिए?</p>",
                    options_en: ["<p>Rs.2,156</p>", "<p>Rs.2,056</p>", 
                                "<p>Rs.2,136</p>", "<p>Rs.2,256</p>"],
                    options_hi: ["<p>Rs.2,156</p>", "<p>Rs.2,056</p>",
                                "<p>Rs.2,136</p>", "<p>Rs.2,256</p>"],
                    solution_en: "<p>8.(c)<br>Proft % = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>SP</mi><mo>-</mo><mi>CP</mi></mrow><mrow><mi>CP</mi><mi mathvariant=\"normal\">&#160;</mi></mrow></mfrac></math> &times; 100 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2100</mn><mo>-</mo><mi>CP</mi></mrow><mi>CP</mi></mfrac></math> &times; 100<br>Loss % = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>CP</mi><mo>-</mo><mi>SP</mi></mrow><mrow><mi>CP</mi><mi mathvariant=\"normal\">&#160;</mi></mrow></mfrac></math> &times; 100 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\" class=\"wrs_chemistry\"><mfrac><mrow><mo>&#160;</mo><mi>CP</mi><mo>-</mo><mn>1460</mn><mo>&#160;</mo></mrow><mi>CP</mi></mfrac></math>&times; 100<br>According to question,<br>&rArr; Profit% = Loss %<br>&rArr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2100</mn><mo>-</mo><mi>CP</mi></mrow><mi>CP</mi></mfrac></math> &times; 100 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>CP</mi><mo>-</mo><mn>1460</mn></mrow><mi>CP</mi></mfrac></math> &times; 100<br>&rArr; 2 CP = 2100 + 1460<br>&rArr; 2 CP = 3560<br>&rArr; CP = 1780<br>Now, <br>SP = 1780 &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>120</mn><mn>100</mn></mfrac></math> = ₹ 2136</p>",
                    solution_hi: "<p>8.(c)<br>लाभ% = <math style=\"font-family: Verdana;\" display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"true\"><mfrac><mrow><mi>&#2357;&#2367;&#2325;&#2381;&#2352;&#2351;</mi><mo>&#160;</mo><mi>&#2350;&#2370;&#2354;&#2381;&#2351;</mi><mo>-</mo><mi>&#2325;&#2381;&#2352;&#2351;</mi><mo>&#160;</mo><mi>&#2350;&#2370;&#2354;&#2381;&#2351;</mi></mrow><mrow><mi>&#2325;&#2381;&#2352;&#2351;</mi><mo>&#160;</mo><mi>&#2350;&#2370;&#2354;&#2381;&#2351;</mi><mo>&#160;</mo></mrow></mfrac></mstyle></math> &times; 100 = <math style=\"font-family: Verdana;\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2100</mn><mo>-</mo><mi>&#2325;&#2381;&#2352;&#2351;</mi><mo>&#160;</mo><mi>&#2350;&#2370;&#2354;&#2381;&#2351;</mi></mrow><mrow><mi>&#2325;&#2381;&#2352;&#2351;</mi><mo>&#160;</mo><mi>&#2350;&#2370;&#2354;&#2381;&#2351;</mi></mrow></mfrac></math> &times; 100<br>हानि % = <math style=\"font-family: Verdana;\" display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"true\"><mfrac><mrow><mi>&#2325;&#2381;&#2352;&#2351;</mi><mo>&#160;</mo><mi>&#2350;&#2370;&#2354;&#2381;&#2351;</mi><mo>-</mo><mi>&#2357;&#2367;&#2325;&#2381;&#2352;&#2351;</mi><mo>&#160;</mo><mi>&#2350;&#2370;&#2354;&#2381;&#2351;</mi></mrow><mrow><mi>&#2325;&#2381;&#2352;&#2351;</mi><mo>&#160;</mo><mi>&#2350;&#2370;&#2354;&#2381;&#2351;</mi><mo>&#160;</mo></mrow></mfrac></mstyle></math> &times; 100 = <math style=\"font-family: Verdana;\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2325;&#2381;&#2352;&#2351;</mi><mo>&#160;</mo><mi>&#2350;&#2370;&#2354;&#2381;&#2351;</mi><mo>-</mo><mn>1460</mn></mrow><mrow><mi>&#2325;&#2381;&#2352;&#2351;</mi><mo>&#160;</mo><mi>&#2350;&#2370;&#2354;&#2381;&#2351;</mi></mrow></mfrac></math> &times; 100<br>प्रश्न के अनुसार, <br>&rArr; लाभ% = हानि %<br>&rArr; <math style=\"font-family: Verdana;\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2100</mn><mo>-</mo><mi>&#2325;&#2381;&#2352;&#2351;</mi><mo>&#160;</mo><mi>&#2350;&#2370;&#2354;&#2381;&#2351;</mi></mrow><mrow><mi>&#2325;&#2381;&#2352;&#2351;</mi><mo>&#160;</mo><mi>&#2350;&#2370;&#2354;&#2381;&#2351;</mi></mrow></mfrac></math> &times; 100 = <math style=\"font-family: Verdana;\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2325;&#2381;&#2352;&#2351;</mi><mo>&#160;</mo><mi>&#2350;&#2370;&#2354;&#2381;&#2351;</mi><mo>-</mo><mn>1460</mn></mrow><mrow><mi>&#2325;&#2381;&#2352;&#2351;</mi><mo>&#160;</mo><mi>&#2350;&#2370;&#2354;&#2381;&#2351;</mi></mrow></mfrac></math> &times; 100<br>&rArr; 2 &times; क्रय मूल्य = 2100 + 1460<br>&rArr; 2 &times; क्रय मूल्य = 3560<br>&rArr; क्रय मूल्य = ₹ 1780<br>अब, <br>विक्रय मूल्य = 1780 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>120</mn><mn>100</mn></mfrac></math> = ₹ 2136</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "17",
                    question_en: "<p>9. By selling a mobile phone for ₹23,856, Ramesh gains 20%. If he sells it for ₹20,580, find his gain or loss percentage (correct to two places of decimals).</p>",
                    question_hi: "<p>9. एक मोबाइल फोन को ₹23,856 में बेचने पर रमेश को 20% का लाभ होता है। यदि वह इसे ₹20,580 में&nbsp;बेचता है, तो उसका लाभ या हानि प्रतिशत (दशमलव के दो स्थान तक सही) ज्ञात करें।</p>",
                    options_en: ["<p>Loss 3.52%</p>", "<p>Gain 5.32%</p>", 
                                "<p>Gain 3.52%</p>", "<p>Loss 5.32%</p>"],
                    options_hi: ["<p>3.52% हानि</p>", "<p>5.32% लाभ</p>",
                                "<p>3.52% लाभ</p>", "<p>5.32% हानि</p>"],
                    solution_en: "<p>9.(c)<br>First Selling Price = ₹ 23856<br>Profit % = 20 %<br>&rArr; CP = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>23856</mn><mo>&#215;</mo><mn>100</mn></mrow><mn>120</mn></mfrac></math> = ₹ 19880 <br>New Selling Price = ₹ 20,580 <br>New Profit % = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>SP</mi><mo>-</mo><mi>CP</mi></mrow><mi>CP</mi></mfrac></math> &times; 100<br>&rArr; Profit% = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>20580</mn><mo>-</mo><mn>19880</mn></mrow><mn>19880</mn></mfrac></math> &times; 100<br>&rArr; Profit% = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>700</mn><mn>19880</mn></mfrac></math> &times; 100<br>&rArr; Profit% = 3.52 %</p>",
                    solution_hi: "<p>9.(c)<br>प्रथम विक्रय मूल्य = ₹23856<br>लाभ % = 20 %<br>&rArr; क्रय मूल्य = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>23856</mn><mo>&#215;</mo><mn>100</mn></mrow><mn>120</mn></mfrac></math> = ₹ 19880<br>नया विक्रय मूल्य = ₹ 20,580 <br>नया लाभ % = <math style=\"font-family:Verdana\" display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"true\"><mfrac><mrow><mi>&#2357;&#2367;&#2325;&#2381;&#2352;&#2351;</mi><mo>&#160;</mo><mi>&#2350;&#2370;&#2354;&#2381;&#2351;</mi><mo>-</mo><mi>&#2325;&#2381;&#2352;&#2351;</mi><mo>&#160;</mo><mi>&#2350;&#2370;&#2354;&#2381;&#2351;</mi></mrow><mrow><mi>&#2325;&#2381;&#2352;&#2351;</mi><mo>&#160;</mo><mi>&#2350;&#2370;&#2354;&#2381;&#2351;</mi></mrow></mfrac></mstyle></math> &times; 100<br>&rArr; लाभ % = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>20580</mn><mo>-</mo><mn>19880</mn></mrow><mn>19880</mn></mfrac></math> &times; 100<br>&rArr; लाभ % = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>700</mn><mn>19880</mn></mfrac></math> &times; 100<br>&rArr; लाभ % = 3.52 %</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "17",
                    question_en: "<p>10. The profit earned after selling a bucket for ₹680 is the same as the loss incurred after selling the bucket for ₹532. The cost price (in ₹) of the bucket is:</p>",
                    question_hi: "<p>10. एक बाल्टी को ₹680 में बेचने पर अर्जित लाभ, बाल्टी को ₹532 में बेचने पर हुई हानि के बराबर है। बाल्टी का क्रय मूल्य (₹ में) ज्ञात करें।</p>",
                    options_en: ["<p>606</p>", "<p>612</p>", 
                                "<p>618</p>", "<p>610</p>"],
                    options_hi: ["<p>606</p>", "<p>612</p>",
                                "<p>618</p>", "<p>610</p>"],
                    solution_en: "<p>10.(a)<br>Cost price of the bucket = <math display=\"inline\"><mfrac><mrow><mn>680</mn><mo>+</mo><mn>532</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1212</mn><mn>2</mn></mfrac></math> = ₹606</p>",
                    solution_hi: "<p>10.(a)<br>बाल्टी का क्रय मूल्य = <math display=\"inline\"><mfrac><mrow><mn>680</mn><mo>+</mo><mn>532</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1212</mn><mn>2</mn></mfrac></math> = ₹606</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "17",
                    question_en: "<p>11. In a business, R invests ₹65,000 and he gets a total ₹80,000 back. Out of profit, ₹2,000 are spent as expenses of the business. What is his profit from the business in percentage?</p>",
                    question_hi: "<p>11. R, एक व्यवसाय में ₹65,000 का निवेश करता है और उसे कुल ₹80,000 वापस मिलते हैं। लाभ में से&nbsp;₹2,000 व्यवसाय के व्यय के रूप में खर्च किए जाते हैं। व्यवसाय से उसका लाभ प्रतिशत कितना है?</p>",
                    options_en: ["<p>18%</p>", "<p>25%</p>", 
                                "<p>22%</p>", "<p>20%</p>"],
                    options_hi: ["<p>18%</p>", "<p>25%</p>",
                                "<p>22%</p>", "<p>20%</p>"],
                    solution_en: "<p>11.(d)<br>Total amount invested by R = ₹65,000<br>Remaining amount = 80,000 - ( 65,000 + 2,000 ) = ₹13,000<br>Required profit% = <math display=\"inline\"><mfrac><mrow><mn>13</mn><mo>,</mo><mn>000</mn></mrow><mrow><mn>65</mn><mo>,</mo><mn>000</mn></mrow></mfrac></math> &times; 100 = 20%</p>",
                    solution_hi: "<p>11.(d)<br>R द्वारा निवेश की गई कुल राशि = ₹65,000<br>शेष राशि = 80,000 - ( 65,000 + 2,000 ) = ₹13,000<br>आवश्यक लाभ% = <math display=\"inline\"><mfrac><mrow><mn>13</mn><mo>,</mo><mn>000</mn></mrow><mrow><mn>65</mn><mo>,</mo><mn>000</mn></mrow></mfrac></math> &times; 100 = 20%</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "17",
                    question_en: "<p>12. A shopkeeper purchased an article for ₹7,200 and spent 9% of its cost on repairs. He sold it to Rajan for ₹8,100. What was the profit percentage on selling the article? (Rounded off to 2 decimal places)</p>",
                    question_hi: "<p>12. एक दुकानदार ने ₹7,200 में एक वस्तु खरीदी और उसके क्रय मूल्य का 9% मरम्मत पर खर्च किया। उसने इसे राजन को ₹ 8,100 में बेच दिया। वस्तु को बेचने पर लाभ प्रतिशत क्या था? (दो दशमलव स्थानों तक पूर्णांकित)</p>",
                    options_en: ["<p>2.91%</p>", "<p>3.12%</p>", 
                                "<p>3.21%</p>", "<p>12.5%</p>"],
                    options_hi: ["<p>2.91%</p>", "<p>3.12%</p>",
                                "<p>3.21%</p>", "<p>12.5%</p>"],
                    solution_en: "<p>12.(c)<br>Total CP of an article = ₹7200 + 7200 &times; 9% = 7200 + 648 = ₹7848<br>SP = ₹8100<br>CP : SP = 7848 : 8100 = 218 : 225<br>Required profit% = <math display=\"inline\"><mfrac><mrow><mn>225</mn><mo>-</mo><mn>218</mn></mrow><mrow><mn>218</mn></mrow></mfrac></math> &times; 100 =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>700</mn><mn>218</mn></mfrac></math> = 3.21%</p>",
                    solution_hi: "<p>12.(c)<br>वस्तु का कुल CP = ₹7200 + 7200 &times; 9% = 7200 + 648 = ₹7848<br>SP = ₹8100<br>CP : SP = 7848 : 8100 = 218 : 225<br>आवश्यक लाभ% = <math display=\"inline\"><mfrac><mrow><mn>225</mn><mo>-</mo><mn>218</mn></mrow><mrow><mn>218</mn></mrow></mfrac></math> &times; 100 =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>700</mn><mn>218</mn></mfrac></math> = 3.21%</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "17",
                    question_en: "<p>13. A wristwatch is sold for ₹2,700 at a loss of 10%. What is the cost price of the wristwatch?</p>",
                    question_hi: "<p>13. एक कलाई घड़ी 10% की हानि पर ₹2,700 में बेची जाती है। कलाई घड़ी का क्रय मूल्य कितना है?</p>",
                    options_en: ["<p>₹2,900</p>", "<p>₹3,200</p>", 
                                "<p>₹3,000</p>", "<p>₹3,300</p>"],
                    options_hi: ["<p>₹2,900</p>", "<p>₹3,200</p>",
                                "<p>₹3,000</p>", "<p>₹3,300</p>"],
                    solution_en: "<p>13.(c)<br>Let CP of the wristwatch be 100%<br>&rArr; (100 - 10)% = 90% = 2700<br>&rArr; 100% = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2700</mn><mn>90</mn></mfrac></math> &times; 100 = ₹ 3000</p>",
                    solution_hi: "<p>13.(c)<br>माना कलाई घड़ी का क्रय मूल्य 100% है<br>&rArr; (100 - 10)% = 90% = 2700<br>&rArr; 100% = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2700</mn><mn>90</mn></mfrac></math> &times; 100 = ₹ 3000</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "17",
                    question_en: "<p>14. The ratio of the cost price of an article to its selling price is 432 : 612. The profit percentage (rounded off to 2 decimal places) on it is:</p>",
                    question_hi: "<p>14. किसी वस्तु के क्रय मूल्य का उसके विक्रय मूल्य से अनुपात 432 : 612 है। इस पर लाभ प्रतिशत (2 दशमलव स्थान तक पूर्णांकित) क्या है?</p>",
                    options_en: ["<p>41.66%</p>", "<p>40.25%</p>", 
                                "<p>42.33%</p>", "<p>38.26%</p>"],
                    options_hi: ["<p>41.66%</p>", "<p>40.25%</p>",
                                "<p>42.33%</p>", "<p>38.26%</p>"],
                    solution_en: "<p>14.(a)<br>CP : SP = 432 : 612 = 12 : 17<br>Required profit% = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>17</mn><mo>-</mo><mn>12</mn></mrow><mn>12</mn></mfrac></math> &times; 100 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>500</mn><mn>12</mn></mfrac></math> = 41.66%</p>",
                    solution_hi: "<p>14.(a)<br>क्रयमूल्य : विक्रय मूल्य = 432 : 612 = 12 : 17<br>आवश्यक लाभ% = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>17</mn><mo>-</mo><mn>12</mn></mrow><mn>12</mn></mfrac></math> &times; 100 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>500</mn><mn>12</mn></mfrac></math> = 41.66%</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "misc",
                    question_en: "<p>15. A and B invested money in a business in the ratio of 2 : 3. If 10% of the total profit goes to charity and A\'s share in the profit is ₹4,500, then what is the total profit?</p>",
                    question_hi: "<p>15. A और B ने एक व्यवसाय में 2 : 3 के अनुपात में धन का निवेश किया। यदि कुल लाभ का 10% दान में जाता है और लाभ में A का हिस्सा ₹4,500 है, तो कुल लाभ की गणना करें।</p>",
                    options_en: ["<p>₹13,000</p>", "<p>₹12,500</p>", 
                                "<p>₹12,000</p>", "<p>₹13,500</p>"],
                    options_hi: ["<p>₹13,000</p>", "<p>₹12,500</p>",
                                "<p>₹12,000</p>", "<p>₹13,500</p>"],
                    solution_en: "<p>15.(b)<br>Total profit = 2 + 3 = 5 unit <br>Share of A = 2 &times; 90% = 1.8 unit = ₹4500<br>Then, 5 unit = <math display=\"inline\"><mfrac><mrow><mn>4500</mn></mrow><mrow><mn>1</mn><mo>.</mo><mn>8</mn></mrow></mfrac></math> &times; 5 = ₹12500</p>",
                    solution_hi: "<p>15.(b)<br>कुल लाभ = 2 + 3 = 5 इकाई <br>A का हिस्सा = 2 &times; 90% = 1.8 इकाई = ₹4500<br>तब, 5 इकाई = <math display=\"inline\"><mfrac><mrow><mn>4500</mn></mrow><mrow><mn>1</mn><mo>.</mo><mn>8</mn></mrow></mfrac></math> &times; 5 = ₹12500</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>