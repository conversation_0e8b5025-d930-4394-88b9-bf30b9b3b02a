<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">10:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 10</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">10</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 10 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["31"] = {
                name: "Computer Knowledge",
                start: 0,
                end: 8
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="31">Computer Knowledge</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 9,
                end: 9
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "31",
                    question_en: "<p>1. Which of the following statements is INCORRECT related to sorting emails in Gmail ?</p>",
                    question_hi: "<p>1. जी-मेल में, ई-मेल्स को सॉर्ट करने से संबंधित निम्नलिखित में से कौन-सा कथन गलत है?</p>",
                    options_en: ["<p>Sorting mail can help us remove unwanted emails from your inbox and more easily unsubscribe from mailings we don\'t need.</p>", "<p>Depending on what we use your email address for, sorting emails can also help you keep track of trends and correspondence by looking at messages sorted by specific criteria.</p>", 
                                "<p>We can sort emails by a variety of factors, like the topic, sender, size, or age of the message</p>", "<p>Sorting mail cannot help us remove unwanted emails from our inboxes and more easily unsubscribe from mallings we don\'t need.</p>"],
                    options_hi: ["<p>मेल को सॉर्ट करने से हमें अपने इनबॉक्स से अवांछित ई-मेल्स हटाने में मदद मिल सकती है और इससे उन मेलों को आसानी से अनसब्सक्राइब किया जा सकता है, जिनकी हमें आवश्यकता नहीं होती है।</p>", "<p>इस पर निर्भर करते हुए कि हम अपने ई-मेल एड्रेस का उपयोग किसलिए करते हैं, ई-मेल्स को सॉर्ट करने से आपको विशिष्ट मानदंडों के आधार पर क्रमबद्ध संदेशों को देखकर ट्रेंड्स और पत्राचार पर नज़र रखने में भी मदद मिल सकती है।</p>",
                                "<p>हम ई-मेल्स को विषय, प्रेषक, संदेश आकार (साइज़) या अवधि जैसे विभिन्न कारकों के आधार पर सॉर्ट कर सकते हैं।</p>", "<p>मेल को सॉर्ट करने से हमें अपने इनबॉक्स से अवांछित ई-मेल्स हटाने में मदद नहीं मिल सकती है और इससे उन मेलों को आसानी से अनसब्सक्राइब नहीं किया जा सकता है, जिनकी हमें आवश्यकता नहीं होती है।</p>"],
                    solution_en: "<p>1.(d) Sorting cannot directly delete emails, it provides effective tools for finding and managing unwanted messages, ultimately contributing to a cleaner inbox.</p>",
                    solution_hi: "<p>1.(d) सॉर्टिंग (Sorting) ई-मेल को डायरेक्ट डिलीट नहीं कर सकता, यह अनवांटेड मैसेजेस को फाइन्ड करने और मैनेज करने के लिए प्रभावी टूल प्रदान करता है, अंततः एक क्लीनर इनबॉक्स (cleaner inbox) में Contribute करता है।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "2",
                    section: "31",
                    question_en: "<p>2.___________. involves using a smartphone\'s data connection to provide internet access to other devices&nbsp;such as laptops or tablets.</p>",
                    question_hi: "<p>2. _________ में लैपटॉप या टैबलेट जैसे अन्य उपकरणों को इंटरनेट एक्सेस प्रदान करने के लिए स्मार्टफोन के डेटा कनेक्शन का उपयोग किया जाता है।</p>",
                    options_en: ["<p>Satellite</p>", "<p>Dial up</p>", 
                                "<p>WI-FI</p>", "<p>Tethering</p>"],
                    options_hi: ["<p>सैटेलाइट (Satellite)</p>", "<p>डायल अप (Dial up)</p>",
                                "<p>वाई-फाई (Wi-Fi)</p>", "<p>टेथरिंग (Tethering)</p>"],
                    solution_en: "<p>2.(d) <strong>Tethering.</strong> In dialup connection, the services connect to the internet through a phone line connection. Wi-Fi is a wireless technology used to connect computers, tablets, smartphones and other devices to the internet. Satellite internet uses Artificial satellites to transmit data</p>",
                    solution_hi: "<p>2.(d) <strong>टेथरिंग </strong>(Tethering)। डायलअप कनेक्शन (dial up connection) में, सर्विस फ़ोन लाइन कनेक्शन के माध्यम से इंटरनेट से कनेक्ट होते हैं। Wi-Fi एक वायरलेस टेक्नॉलजी है जिसका उपयोग कंप्यूटर, टैबलेट, स्मार्टफोन एवं अन्य डिवाइस को इंटरनेट से कनेक्ट करने के लिए किया जाता है। सैटेलाइट (Satellite) इंटरनेट डेटा संचारित करने के लिए कृत्रिम उपग्रहों (Artificial satellites) का उपयोग करता है ।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "3",
                    section: "31",
                    question_en: "<p>3. What does the \'Paste\' command do in Microsoft Word 365 ?</p>",
                    question_hi: "<p>3. माइक्रोसॉफ्ट वर्ड 365 में \'पेस्ट\' (Paste) कमांड क्या करता है ?</p>",
                    options_en: ["<p>Deletes the content of the clipboard</p>", "<p>Inserts the content from the clipboard into the document</p>", 
                                "<p>Removes the selected text</p>", "<p>Copies the selected text to the clipboard</p>"],
                    options_hi: ["<p>क्लिपबोर्ड के कंटेंट को डिलीट करता है</p>", "<p>क्लिपबोर्ड से कंटेंट को डॉक्&zwj;यूमेंट में इंसर्ट करता है</p>",
                                "<p>सेलेक्&zwj;ट किए गए टेक्&zwj;स्&zwj;ट को हटा देता है</p>", "<p>सेलेक्&zwj;ट किए गए टेक्&zwj;स्&zwj;ट को क्लिपबोर्ड पर कॉपी करता है</p>"],
                    solution_en: "<p>3.(b) The \"Paste\" (shortcut Key : Ctrl + V) command in Microsoft Word 365 takes the content that has been previously copied or cut (and stored in the clipboard) and inserts it at the current cursor position within the document.</p>",
                    solution_hi: "<p>3.(b) Microsoft Word 365 में \"पेस्ट\" (शॉर्टकट कुंजी: Ctrl + V) कमांड उस कंटेन्ट को लेता है जिसे पहले कॉपी या कट (और क्लिपबोर्ड में स्टोर किया गया है) किया गया है और इसे डॉक्यूमेंट के अंदर करेंट कर्सर पोजीशन में इन्सर्ट करता है।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "4",
                    section: "31",
                    question_en: "<p>4. Which Microsoft Excel 365 function calculates the average of a range of numbers?</p>",
                    question_hi: "<p>4. एमएस एक्सेल 365 (MS Excel 365) का निम्न में से कौन सा फ़ंक्शन संख्याओं की रेंज के औसत की गणना करता है?</p>",
                    options_en: ["<p>AVG</p>", "<p>MEDIAN</p>", 
                                "<p>AVERAGE</p>", "<p>MEAN</p>"],
                    options_hi: ["<p>AVG</p>", "<p>MEDIAN</p>",
                                "<p>AVERAGE</p>", "<p>MEAN</p>"],
                    solution_en: "<p>4.(c) <strong>AVERAGE.</strong> This function measures central tendency, which is the location of the center of a group of numbers in a statistical distribution. The MEDIAN Function calculates the middle value of a given set of numbers.</p>",
                    solution_hi: "<p>4.(c) <strong>AVERAGE।</strong> यह फ़ंक्शन, सेंट्रल टेन्डन्सी (central tendency) को मेजर है, जो सांख्यिकीय वितरण (statistical distribution) में नंबर्स के ग्रुप के सेंटर की लोकेशन है। MEDIAN फ़ंक्शन किसी दिए गए नंबर्स के सेट की मिडल वैल्यू को कैलकुलेट करता है।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "5",
                    section: "31",
                    question_en: "<p>5. Which option in MS-Word 365 allows you to align the text in a paragraph to both the left and right margins, creating a visually appealing document ?</p>",
                    question_hi: "<p>5. एमएस वर्ड 365 (MS Word 365) के निम्नलिखित में से किस विकल्प सेआप एक पैराग्राफ़ में टेक्स्ट को बाएँ और दाएँ दोनों मार्जिन पर संरेखित कर सकते हैं, जिससे एक आकर्षक डॉक्यूमेंट बन सके?</p>",
                    options_en: ["<p>Line Spacing</p>", "<p>Justification</p>", 
                                "<p>Paragraph Alignment</p>", "<p>Text Indentation</p>"],
                    options_hi: ["<p>लाइन स्पेसिंग</p>", "<p>जस्टिफ़िकेशन</p>",
                                "<p>पैराग्राफ़ एलाइनमेंट</p>", "<p>टेक्स्ट इंडेंटेशन</p>"],
                    solution_en: "<p>5.(b) <strong>Justification.</strong> Line spacing determines the amount of vertical space between lines of text in a paragraph. Text alignment - determines the appearance of the text in a whole paragraph. Paragraph Indenting allows you to position your lines in paragraphs further from the margins of the document in the formatting area of the document.</p>",
                    solution_hi: "<p>5.(b) <strong>जस्टिफ़िकेशन </strong>(Justification)। लाइन स्पेसिंग किसी पैराग्राफ में, टेक्स्ट की लाइंस के बीच, वर्टिकल स्पेस के अमाउन्ट को डिटरमाइन करता है। टेक्स्ट एलाइनमेंट (Text alignment) - पूरे पैराग्राफ में टेक्स्ट के अपीयरेंस को डिटरमाइन करता है। पैराग्राफ इंडेंटिंग (Paragraph Indenting) आपको पैराग्राफ के फ़ॉर्मेटिंग एरिया में पैराग्राफ के मार्जिन से आगे पैराग्राफ में अपनी पंक्तियों (lines) को रखने की अनुमति देता है।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "6",
                    section: "31",
                    question_en: "<p>6. Which option allows you to select multiple documents while opening them in Microsoft Word 365 ?</p>",
                    question_hi: "<p>6. कौन-सा ऑप्&zwj;शन आपको माइक्रोसॉफ्ट वर्ड 365 (Microsoft Word 365) में डाक्&zwj;यूमेंट खोलते समय कई डाक्&zwj;यूमेंट्स को सेलेक्&zwj;ट करने की सुविधा देता है?</p>",
                    options_en: ["<p>Hold down the Tab key while clicking on the documents</p>", "<p>Hold down the Windows key while clicking on the documents.</p>", 
                                "<p>Hold down the Shift key while clicking on the documents</p>", "<p>Hold down the Alt key while clicking on the documents.</p>"],
                    options_hi: ["<p>डाक्&zwj;यूमेंट्स पर क्लिक करते समय Tab कुंजी दबाए रखना।</p>", "<p>डाक्&zwj;यूमेंट्स पर क्लिक करते समय Windows कुंजी दबाए रखना।</p>",
                                "<p>डाक्&zwj;यूमेंट्स पर क्लिक करते समय Shift कुंजी दबाए रखना।</p>", "<p>डाक्&zwj;यूमेंट्स पर क्लिक करते समय Alt कुंजी दबाए रखना।</p>"],
                    solution_en: "<p>6.(c) In programs such as Microsoft Word 365, pressing the tab key will move the cursor a fixed distance to the right.</p>",
                    solution_hi: "<p>6.(c) माइक्रोसॉफ्ट वर्ड 365 प्रोग्राम में, टैब की (tab key) दबाने से कर्सर एक निश्चित दूरी तक राइट साइड में चला जाएगा।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "7",
                    section: "31",
                    question_en: "<p>7. In Microsoft Word 365, which option is used to save an existing document with a new name or in a different location ?</p>",
                    question_hi: "<p>7. माइक्रोसॉफ़्ट वर्ड 365 में, किसी मौजूदा डॉक्&zwj;यूमेंट को नए नाम से या किसी अलग स्थान पर सेव करने के लिए किस विकल्प का उपयोग किया जाता है?</p>",
                    options_en: ["<p>Save As</p>", "<p>Save</p>", 
                                "<p>Copy</p>", "<p>Print</p>"],
                    options_hi: ["<p>सेव एज़ (Save As)</p>", "<p>सेव (Save)</p>",
                                "<p>कॉपी (Copy)</p>", "<p>प्रिंट (Print)</p>"],
                    solution_en: "<p>7.(a) <strong>Save As.</strong> Saving a document is a very important step that is to be executed right after the addition of some content in a document.</p>",
                    solution_hi: "<p>7.(a) <strong>सेव एज़</strong> (Save As)। किसी डॉक्यूमेंट को सुरक्षित (Saving) करना एक बहुत ही महत्वपूर्ण स्टेप (step) है जिसे डॉक्यूमेंट में कुछ सामग्री (content) जोड़ने (addition) के तुरंत बाद निष्पादित (executed) किया जाना है।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "8",
                    section: "31",
                    question_en: "<p>8. Which of the following shortcut keys is used to modify selected text as Italic in MS- Word 365 ?</p>",
                    question_hi: "<p>8. एमएस वर्ड 365 (MS Word 365) में चयनित टेक्स्ट को इटैलिक के रूप में मॉडिफाई करने के लिए निम्नलिखित में से किस शॉर्टकट कुं जी (key) का उपयोग किया जाता है?</p>",
                    options_en: ["<p>Ctrl + I</p>", "<p>Ctrl + U</p>", 
                                "<p>Ctrl + O</p>", "<p>Ctrl + B</p>"],
                    options_hi: ["<p>Ctrl + I</p>", "<p>Ctrl + U</p>",
                                "<p>Ctrl + O</p>", "<p>Ctrl + B</p>"],
                    solution_en: "<p>8.(a) <strong>Ctrl + I.</strong> Shortcut keys: Ctrl + B - Bold. Ctrl + U - Underline. Ctrl + O - Open a document.</p>",
                    solution_hi: "<p>8.(a) <strong>Ctrl + I.</strong> Shortcut keys: Ctrl + B - Bold करने क लिए। Ctrl + U - Underline करने क लिए। Ctrl + O - Document, Open करने क लिए ।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "9",
                    section: "31",
                    question_en: "<p>9. In MS Excel 365 which file type used in MS Excel produces a comma-delimited text file that can be read by almost all spreadsheet applications on any operating system ?</p>",
                    question_hi: "<p>9. एमएस एक्सेल (MS Excel) मेंउपयोग किया जाने वाला कौन-सा फ़ाइल प्रकार अल्पविराम-सीमांकित (comma-delimited) टेक्स्ट फ़ाइल बनाता है जिसे किसी भी ऑपरेटिंग सिस्टम पर लगभग सभी स्प्रेडशीट एप्लिकेशनों द्वारा पढ़ा जा सकता है</p>",
                    options_en: ["<p>csv</p>", "<p>docx</p>", 
                                "<p>pdf</p>", "<p>xlsx</p>"],
                    options_hi: ["<p>csv</p>", "<p>docx</p>",
                                "<p>pdf</p>", "<p>xlsx</p>"],
                    solution_en: "<p>9.(a) <strong>csv </strong>(Comma Separated Values) file is a plain text file that stores data by delimiting data entries with commas. DOCX is a file format used for storing documents created in Microsoft Word. XLSX file is a Microsoft Excel Open XML Format Spreadsheet file.</p>",
                    solution_hi: "<p>9.(a) <strong>CSV </strong>कॉमा सेपरेटेड वैल्यूज़) फ़ाइल एक प्लेन टेस्ट (plain text) फ़ाइल है जो डेटा एंट्रीज (data entries) को अल्पविराम (commas) से सीमित करके डेटा स्टोर करता है। DOCX एक फ़ाइल फॉर्मेट है जिसका उपयोग माइक्रोसॉफ्ट वर्ड में बनाए गए डॉक्यूमेंट को स्टोर करने के लिए किया जाता है। XLSX फ़ाइल एक माइक्रोसॉफ्ट एक्सेल ओपन XML Format स्प्रेडशीट फ़ाइल है।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "10",
                    section: "misc",
                    question_en: "10. Suppose you have a worksheet in MS Excel 365 containing sales data for different months. You need to update the sales figure for May, as there was an error in the initial entry. What steps should you follow to edit the sales data for the month of May ?",
                    question_hi: "10. मान लीजिए कि आपके पास एमएस एक्सेल 365 (MS Excel 365) में एक वर्कशीट है जिसमें विभिन्न महीनों के बिक्री डेटा हैं। प्रारंभिक प्रविष्टि में एक त्रुटि के कारण आपको मई के बिक्री आंकड़े अपडेट करने हैं। मई महीने के बिक्री डेटा को एडिट करने के लिए आपको किन चरणों का पालन करना चाहिए? ",
                    options_en: ["  Click on the cell containing the May sales data -> Press F6 to enter edit mode -> Make the necessary changes -> Press Enter to confirm the edit", " Click on the cell containing the May sales data -> Press F2 to enter edit mode -> Make the necessary changes -> Press Enter to confirm the edit", 
                                "  Click on the cell containing the May sales data -> Press F7 to enter edit made -> Make the necessary changes -> Press Enter to confirm the edit", "  Click on the cell containing the May sales data -> Press F1 to enter edit mode -> Make the necessary changes -> Press Enter to confirm the edit"],
                    options_hi: ["  मई बिक्री डेटा वाले सेल पर क्लिक कीजिए -> एडिट मोड मेंजाने के लिए F6 दबाइए -> आवश्यक परिवर्तन कीजिए -> एडिट कन्फर्मकरने के लिए Enter दबाइए ", "<p>मई बिक्री डेटा वाले सेल पर क्लिक कीजिए -&gt; एडिट मोड मेंजाने के लिए F2 दबाइए -&gt; आवश्यक परिवर्तन कीजिए -&gt; एडिट <br>कन्फर्मकरने के लिए Enter दबाइए</p>",
                                "<p>मई बिक्री डेटा वाले सेल पर क्लिक कीजिए -&gt; एडिट मोड मेंजाने के लिए F7 दबाइए -&gt; आवश्यक परिवर्तन कीजिए -&gt; एडिट कन्फर्मकरने के लिए Enter दबाइए</p>", "<p>मई बिक्री डेटा वाले सेल पर क्लिक कीजिए -&gt; एडिट मोड मेंजाने के लिए F1 दबाइए -&gt; आवश्यक परिवर्तन कीजिए -&gt; एडिट कन्फर्मकरने के लिए Enter दबाइए</p>"],
                    solution_en: "<p>10.(b) Other Function key uses : F6 - moves the cursor to the next pane in a split window. F7 - spells and checks the document. F1 - opens the Help menu.</p>",
                    solution_hi: "<p>10.(b) अन्य फ़ंक्शन की का उपयोग : F6 - कर्सर को स्प्लिट विंडो (split window) में अगले फलक (next pane) पर ले जाता है। F7 - डॉक्यूमेंट की स्पेलिंग (spells) और जाँच (checks) करता है। F1 - हेल्प मेनू ओपन करता है।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>