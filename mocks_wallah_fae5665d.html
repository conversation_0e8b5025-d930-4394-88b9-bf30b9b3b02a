<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Smart Test Interface</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        .question-nav{position:fixed;top:70px;right:0;height:calc(100vh - 70px);width:320px;transition:all .3s;z-index:100;display:flex;flex-direction:column}
        .question-content{margin-right:0;transition:all .3s}
        .question-box{width:40px;height:40px;margin:5px;cursor:pointer}
        .attempted{background-color:#3498db!important;color:white}
        .correct{background-color:#2ecc71!important;color:white}
        .incorrect{background-color:#e74c3c!important;color:white}
        .current{border:3px solid #f39c12!important}
        #question-boxes{overflow-y:auto;max-height:calc(100vh - 150px);padding-bottom:15px}
        @media (max-width:768px){.question-nav{width:100%;right:-100%}.question-content{margin-right:0}}
    </style>
</head>
<body>
    <div class="container-fluid p-0">
        <!-- Header with controls -->
        <nav class="navbar navbar-expand-lg navbar-dark bg-primary sticky-top">
            <div class="container-fluid">
                <a class="navbar-brand" href="#">
                    <i class="fas fa-graduation-cap me-2"></i>Smart Test
                </a>
                <!-- Section selector -->
                <div class="navbar-nav me-auto section-tabs">
                    <select class="form-select" id="section-selector" onchange="switchSection(this.value)">
                        <option value="all">All Sections</option>
                    </select>
                </div>
                <div class="d-flex align-items-center">
                    <!-- Timer -->
                    <div class="bg-light text-primary rounded px-3 py-2 me-2 fw-bold" id="timer">
                        <i class="far fa-clock me-2"></i><span id="timer-display">12:00</span>
                    </div>
                    <!-- Submit button -->
                    <button class="btn btn-success me-2" id="submit-btn" onclick="submitTest()">
                        <i class="fas fa-check me-1"></i> Submit
                    </button>
                    <!-- Language toggle -->
                    <button class="btn btn-light me-2" id="lang-btn" onclick="toggleLanguage()">
                        <i class="fas fa-language"></i>
                    </button>
                    <!-- Menu toggle for mobile -->
                    <button class="btn btn-light d-lg-none" id="menu-toggle" onclick="toggleMenu()">
                        <i class="fas fa-th"></i>
                    </button>
                </div>
            </div>
        </nav>
        <div class="row m-0">
            <!-- Main content area -->
            <div class="col p-0 question-content" id="question-content">
                <div class="container py-4">
                    <!-- Question navigation -->
                    <div class="d-flex justify-content-between mb-4">
                        <button class="btn btn-outline-primary" onclick="prevQuestion()">
                            <i class="fas fa-chevron-left me-1"></i> Previous
                        </button>
                        <div class="fw-bold" id="question-counter">Question 1 of 25</div>
                        <button class="btn btn-outline-primary" onclick="nextQuestion()">
                            Next <i class="fas fa-chevron-right ms-1"></i>
                        </button>
                    </div>
                    <!-- Questions will be displayed here -->
                    <div id="questions-container"></div>
                </div>
            </div>
            <!-- Question navigation sidebar -->
            <div class="bg-light question-nav" id="question-nav">
                <div class="p-3">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h5 class="m-0">Questions</h5>
                        <div>
                            <span class="badge bg-secondary me-1" id="attempted-count">0</span>
                            <span class="badge bg-secondary" id="total-count">25</span>
                            <button class="btn btn-sm btn-outline-secondary ms-2 d-lg-none" onclick="toggleMenu()">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    </div>
                    <div class="d-flex flex-wrap" id="question-boxes"></div>
                </div>
            </div>
        </div>
        <!-- Results modal -->
        <div class="modal fade" id="results-modal" tabindex="-1" aria-hidden="true">
            <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content">
                    <div class="modal-header bg-primary text-white">
                        <h5 class="modal-title">Test Results</h5>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <div class="row text-center">
                            <div class="col-6 mb-3">
                                <div class="p-3 bg-primary bg-opacity-10 rounded">
                                    <h3 id="score-value">0</h3>
                                    <div class="small text-muted">Score</div>
                                </div>
                            </div>
                            <div class="col-6 mb-3">
                                <div class="p-3 bg-success bg-opacity-10 rounded">
                                    <h3 id="correct-value">0</h3>
                                    <div class="small text-muted">Correct</div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="p-3 bg-danger bg-opacity-10 rounded">
                                    <h3 id="incorrect-value">0</h3>
                                    <div class="small text-muted">Incorrect</div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="p-3 bg-secondary bg-opacity-10 rounded">
                                    <h3 id="unattempted-value">0</h3>
                                    <div class="small text-muted">Unattempted</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-primary" onclick="reviewTest()">Review Test</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 12 * 60;
        let resultsModal;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            resultsModal = new bootstrap.Modal(document.getElementById('results-modal'));
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 768) {
                document.getElementById('question-content').style.marginRight = '320px';
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["7"] = {
                name: "English",
                start: 0,
                end: 23
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="7">English</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 24,
                end: 24
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "7",
                    question_en: " <p>1.Select the most appropriate meaning of given idiom </span></p> <p><span style=\"font-family:Times New Roman\">Drop the subject ",
                    question_hi: "</span></p>",
                    options_en: [
                        " <p> Change the partner</span><span style=\"font-family:Times New Roman\">  </span></p>",
                        " <p> Change the topic </span></p>",
                        " <p> Change the attire </span><span style=\"font-family:Times New Roman\">  </span></p>",
                        " <p> Change the business</span></p>"
                    ],
                    options_hi: [
                        "",
                        "",
                        "",
                        ""
                    ],
                    solution_en: " <p> </span><span style=\"font-family:Times New Roman\">(b) </span></p> <p><span style=\"font-family:Times New Roman\">Change the topic</span></p> <p><span style=\"font-family:Times New Roman\"> </span></p>",
                    solution_hi: " <p> </span><span style=\"font-family:Times New Roman\">(b) </span></p> <p><span style=\"font-family:Times New Roman\">Change the topic</span></p> <p><span style=\"font-family:Times New Roman\"> </span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "7",
                    question_en: "<p>2.Given are jumbled sentences.Select the option that gives their correct order</p>\r\n<p><span style=\"font-family: Times New Roman;\">1. I went to my friend last week. </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">P. He politely refused to oblige me. </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Q. I did not speak even a single word. </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">R. Actually I wanted his scooter for a day. </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">S. I felt ashamed of myself. </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">6. I was mistaken in assessing a true friendship. </span></p>",
                    question_hi: "</span></p>",
                    options_en: [
                        "<p>RPQS</p>",
                        "<p>PRQS</p>",
                        "<p>SRPQ</p>",
                        "<p>QRSP</p>"
                    ],
                    options_hi: [
                        "",
                        "",
                        "",
                        ""
                    ],
                    solution_en: "<p><span style=\"font-family: Times New Roman;\">(a)</span></p>\r\n<p><span style=\"font-family: Times New Roman;\"> RPQS</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Sentence R mentions why he went to his friend &amp; Sentence P mentions his friend refused. So, P will follow R. Further, Sentence Q mentions he remained silent &amp; Sentence S mentions he felt ashamed. So, S will follow Q Going through the options, option a has the correct sequence.</span></p>",
                    solution_hi: "<p><span style=\"font-family: Times New Roman;\">(a)</span></p>\r\n<p><span style=\"font-family: Times New Roman;\"> RPQS</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Sentence R mentions why he went to his friend &amp; Sentence P mentions his friend refused. So, P will follow R. Further, Sentence Q mentions he remained silent &amp; Sentence S mentions he felt ashamed. So, S will follow Q Going through the options, option a has the correct sequence.</span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "7",
                    question_en: " <p>3.Select the word which means the same as the group of words given</span></p> <p><span style=\"font-family:Times New Roman\">To express in an unclear way",
                    question_hi: "</span></p>",
                    options_en: [
                        " <p> eloquent        </span></p>",
                        " <p>garbled          </span></p>",
                        " <p>lucid          </span></p>",
                        " <p> Intelligible</span></p>"
                    ],
                    options_hi: [
                        "",
                        "",
                        "",
                        ""
                    ],
                    solution_en: " <p> </span><span style=\"font-family:Times New Roman\">(b)</span></p> <p><span style=\"font-family:Times New Roman\"> Garbled- To express in an unclear way</span></p> <p><span style=\"font-family:Times New Roman\">Eloquent- a</span><span style=\"font-family:Times New Roman\">ble to use language and express your opinions well,</span></p> <p><span style=\"font-family:Times New Roman\">Lucid- </span><span style=\"font-family:Times New Roman\">clear and easy to understand</span></p> <p><span style=\"font-family:Times New Roman\">Intelligible- </span><span style=\"font-family:Times New Roman\">possible or easy to understand</span></p>",
                    solution_hi: " <p> </span><span style=\"font-family:Times New Roman\">(b)</span></p> <p><span style=\"font-family:Times New Roman\"> Garbled- To express in an unclear way</span></p> <p><span style=\"font-family:Times New Roman\">Eloquent- a</span><span style=\"font-family:Times New Roman\">ble to use language and express your opinions well,</span></p> <p><span style=\"font-family:Times New Roman\">Lucid- </span><span style=\"font-family:Times New Roman\">clear and easy to understand</span></p> <p><span style=\"font-family:Times New Roman\">Intelligible- </span><span style=\"font-family:Times New Roman\">possible or easy to understand</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "7",
                    question_en: " <p>4.Select the active form of  the given sentence</span></p> <p><span style=\"font-family:Times New Roman\">The most useful training of my career was given to me by my boss.",
                    question_hi: "</span></p>",
                    options_en: [
                        " <p> My boss has been giving me the most useful training of my career.</span></p>",
                        " <p> My boss gives me the most useful training.</span></p>",
                        " <p> My boss is giving me the most useful training.</span></p>",
                        " <p> My boss gave me the most useful training of my career.</span></p>"
                    ],
                    options_hi: [
                        "",
                        "",
                        "",
                        ""
                    ],
                    solution_en: " <p> </span><span style=\"font-family:Times New Roman\">(d) </span></p> <p><span style=\"font-family:Times New Roman\">a. My boss has been giving me the most useful training of my career. (Tense has changed)</span></p> <p><span style=\"font-family:Times New Roman\">b. My boss gives me the most useful training. (Tense has changed)</span></p> <p><span style=\"font-family:Times New Roman\">c. My boss is giving me the most useful training. (Tense has changed)</span></p> <p><span style=\"font-family:Times New Roman\">d. My boss gave me the most useful training of my career. (Correct)</span></p>",
                    solution_hi: " <p> </span><span style=\"font-family:Times New Roman\">(d) </span></p> <p><span style=\"font-family:Times New Roman\">a. My boss has been giving me the most useful training of my career. (Tense has changed)</span></p> <p><span style=\"font-family:Times New Roman\">b. My boss gives me the most useful training. (Tense has changed)</span></p> <p><span style=\"font-family:Times New Roman\">c. My boss is giving me the most useful training. (Tense has changed)</span></p> <p><span style=\"font-family:Times New Roman\">d. My boss gave me the most useful training of my career. (Correct)</span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "7",
                    question_en: " <p>5.Select the antonym of the following word</span></p> <p><span style=\"font-family:Times New Roman\">WANE",
                    question_hi: "</span></p>",
                    options_en: [
                        " <p>swell </span><span style=\"font-family:Times New Roman\">            </span></p>",
                        " <p>prosper</span><span style=\"font-family:Times New Roman\">         </span></p>",
                        " <p>fatten</span><span style=\"font-family:Times New Roman\">       </span></p>",
                        " <p>widen</span></p>"
                    ],
                    options_hi: [
                        "",
                        "",
                        "",
                        ""
                    ],
                    solution_en: " <p> </span><span style=\"font-family:Times New Roman\">(b) </span></p> <p><span style=\"font-family:Times New Roman\">Wane-decline</span></p> <p><span style=\"font-family:Times New Roman\">Prosper- </span><span style=\"font-family:Times New Roman\">to develop in a successful way</span></p> <p><span style=\"font-family:Times New Roman\">Swell- </span><span style=\"font-family:Times New Roman\">to become or to make something bigger, fuller or thicker</span></p> <p><span style=\"font-family:Times New Roman\">Fatten- </span><span style=\"font-family:Times New Roman\">to make somebody/something fatter</span></p> <p><span style=\"font-family:Times New Roman\">Widen- </span><span style=\"font-family:Times New Roman\">to become wider</span></p>",
                    solution_hi: " <p> </span><span style=\"font-family:Times New Roman\">(b) </span></p> <p><span style=\"font-family:Times New Roman\">Wane-decline</span></p> <p><span style=\"font-family:Times New Roman\">Prosper- </span><span style=\"font-family:Times New Roman\">to develop in a successful way</span></p> <p><span style=\"font-family:Times New Roman\">Swell- </span><span style=\"font-family:Times New Roman\">to become or to make something bigger, fuller or thicker</span></p> <p><span style=\"font-family:Times New Roman\">Fatten- </span><span style=\"font-family:Times New Roman\">to make somebody/something fatter</span></p> <p><span style=\"font-family:Times New Roman\">Widen- </span><span style=\"font-family:Times New Roman\">to become wider</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "7",
                    question_en: " <p>6.Select the word which means the same as the group of words given</span></p> <p><span style=\"font-family:Times New Roman\">Cutting off arm/Leg etc. by surgery",
                    question_hi: "</span></p>",
                    options_en: [
                        " <p>Amputation   </span></p>",
                        " <p>Annexation    </span></p>",
                        " <p>Ambulatory   </span></p>",
                        " <p>Tumour</span></p>"
                    ],
                    options_hi: [
                        "",
                        "",
                        "",
                        ""
                    ],
                    solution_en: " <p> </span><span style=\"font-family:Times New Roman\">(a)</span></p> <p><span style=\"font-family:Times New Roman\">Amputation- Cutting off arm/Leg etc. by surgery</span></p> <p><span style=\"font-family:Times New Roman\">Annexation- </span><span style=\"font-family:Times New Roman\"> forcible acquisition of one state\'s territory by another state</span></p> <p><span style=\"font-family:Times New Roman\">Ambulatory- </span><span style=\"font-family:Times New Roman\">relating to or adapted for walking.</span></p>",
                    solution_hi: " <p> </span><span style=\"font-family:Times New Roman\">(a)</span></p> <p><span style=\"font-family:Times New Roman\">Amputation- Cutting off arm/Leg etc. by surgery</span></p> <p><span style=\"font-family:Times New Roman\">Annexation- </span><span style=\"font-family:Times New Roman\"> forcible acquisition of one state\'s territory by another state</span></p> <p><span style=\"font-family:Times New Roman\">Ambulatory- </span><span style=\"font-family:Times New Roman\">relating to or adapted for walking.</span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "7",
                    question_en: "<p>7.Select the most appropriate option to substitute the underlined segment in the given sentence. If no substitution is required ,select no improvement</p>\r\n<p><span style=\"font-family: Times New Roman;\">If you ask nicely,your sister will probably </span><span style=\"text-decoration: underline;\"><span style=\"font-family: Times New Roman;\">allow </span><span style=\"font-family: Times New Roman;\">you have</span></span><span style=\"font-family: Times New Roman;\"> a piece of cake.</span></p>",
                    question_hi: "</span></p>",
                    options_en: [
                        "<p>allow that you have<span style=\"font-family: Times New Roman;\"> </span></p>",
                        "<p>let you have</p>",
                        "<p>let you to have<span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Times New Roman;\"> </span></p>",
                        "<p>no improvement</p>"
                    ],
                    options_hi: [
                        "",
                        "",
                        "",
                        ""
                    ],
                    solution_en: "<p><span style=\"font-family: Times New Roman;\">(b) </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">We will replace \'allow\' with \'let\' to make the given sentence grammatically correct. Hence, &lsquo;let you have&rsquo; is the most appropriate answer.</span></p>",
                    solution_hi: "<p><span style=\"font-family: Times New Roman;\">(b) </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">We will replace \'allow\' with \'let\' to make the given sentence grammatically correct. Hence, &lsquo;let you have&rsquo; is the most appropriate answer.</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "7",
                    question_en: "<p>8.Select the most appropriate option to substitute the underlined segment in the given sentence. If no substitution is required ,select no improvement</p>\r\n<p><span style=\"font-family: Times New Roman;\">His idea of reducing the quality of the article to cope with the increasing market prices was not </span><span style=\"text-decoration: underline;\"><span style=\"font-family: Times New Roman;\">listened to.</span></span></p>",
                    question_hi: "</span></p>",
                    options_en: [
                        "<p>welcomed<span style=\"font-family: Times New Roman;\"> </span></p>",
                        "<p>taken for granted</p>",
                        "<p>agreed to<span style=\"font-family: Times New Roman;\"> </span></p>",
                        "<p>no improvement</p>"
                    ],
                    options_hi: [
                        "",
                        "",
                        "",
                        ""
                    ],
                    solution_en: "<p><span style=\"font-family: Times New Roman;\">(a)</span></p>\r\n<p><span style=\"font-family: Times New Roman;\"> Welcomed means </span><span style=\"font-family: Times New Roman;\">to receive or accept something. The given sentence states that his idea was not accepted. </span><span style=\"font-family: Times New Roman;\">Hence, &lsquo;welcomed&rsquo; is the most appropriate answer.</span></p>",
                    solution_hi: "<p><span style=\"font-family: Times New Roman;\">(a)</span></p>\r\n<p><span style=\"font-family: Times New Roman;\"> Welcomed means </span><span style=\"font-family: Times New Roman;\">to receive or accept something. The given sentence states that his idea was not accepted. </span><span style=\"font-family: Times New Roman;\">Hence, &lsquo;welcomed&rsquo; is the most appropriate answer.</span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "7",
                    question_en: " <p>9.Select the most appropriate option to fill in the blanks</span></p> <p><span style=\"font-family:Times New Roman\">I can identify ______ the problems of a young working mother.",
                    question_hi: "</span></p>",
                    options_en: [
                        " <p> on</span></p>",
                        " <p> with</span></p>",
                        " <p> to</span></p>",
                        " <p> of</span></p>"
                    ],
                    options_hi: [
                        "",
                        "",
                        "",
                        ""
                    ],
                    solution_en: " <p> </span><span style=\"font-family:Times New Roman\">(b)</span></p> <p><span style=\"font-family:Times New Roman\">‘Identify with’ means you have a connection with them and you can understand them and share their feelings. </span><span style=\"font-family:Times New Roman\">The given sentence states that the narrator can understand the </span><span style=\"font-family:Times New Roman\">problems of a young working mother. Hence, ‘with’ is the most appropriate answer.</span></p>",
                    solution_hi: " <p> </span><span style=\"font-family:Times New Roman\">(b)</span></p> <p><span style=\"font-family:Times New Roman\">‘Identify with’ means you have a connection with them and you can understand them and share their feelings. </span><span style=\"font-family:Times New Roman\">The given sentence states that the narrator can understand the </span><span style=\"font-family:Times New Roman\">problems of a young working mother. Hence, ‘with’ is the most appropriate answer.</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "7",
                    question_en: "<p>Directions (10-14): In the following passage word have been deleted.Fill in the blanks with the help of the alternatives given.Select the most appropriate option for each blank</p>\r\n<p><span style=\"font-weight: 400; font-family: \'times new roman\', times, serif;\">Decisions may be&hellip;..(10)...... at either through initiative or through inertia.Many misfortunes in the world come across the bridge of indecision.Power corrupts but weakness also corrupts.The fruits of weakness like fear,bitterness and suspicion can &hellip;..(11).......in a paralysis of decision.Inner decision can produce indecision and indecision results by weakness of character.An occasional wrong decision may be sometimes &hellip;..(12)......... to indecision.The &hellip;..(13)......man is often self centered. He fears making a &hellip;..(14)....... and the fear paralyses him.</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Select the most appropriate option for blank no. 10.</span></p>",
                    question_hi: "</span></p> <p><span style=\"font-family:Times New Roman\">Q10 </span></p>",
                    options_en: [
                        "<p>aimed</p>",
                        "<p>looked</p>",
                        "<p>arrived<span style=\"font-family: Times New Roman;\"> </span></p>",
                        "<p>deferred</p>"
                    ],
                    options_hi: [
                        "",
                        "",
                        "",
                        ""
                    ],
                    solution_en: "<p><span style=\"font-family: Times New Roman;\">(c) </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">We generally reach or arrive at a decision of something. Hence, &lsquo;arrived&rsquo; is the most appropriate answer.</span></p>",
                    solution_hi: "<p><span style=\"font-family: Times New Roman;\">(c) </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">We generally reach or arrive at a decision of something. Hence, &lsquo;arrived&rsquo; is the most appropriate answer.</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "7",
                    question_en: "<p>Directions (10-14): In the following passage word have been deleted.Fill in the blanks with the help of the alternatives given.Select the most appropriate option for each blank</p>\r\n<p><span style=\"font-weight: 400; font-family: \'times new roman\', times, serif;\">Decisions may be&hellip;..(10)...... at either through initiative or through inertia.Many misfortunes in the world come across the bridge of indecision.Power corrupts but weakness also corrupts.The fruits of weakness like fear,bitterness and suspicion can &hellip;..(11).......in a paralysis of decision.Inner decision can produce indecision and indecision results by weakness of character.An occasional wrong decision may be sometimes &hellip;..(12)......... to indecision.The &hellip;..(13)......man is often self centered. He fears making a &hellip;..(14)....... and the fear paralyses him.</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Select the most appropriate option for blank no. 11.</span></p>",
                    question_hi: "</span></p> <p><span style=\"font-family:Times New Roman\">Q</span><span style=\"font-family:Times New Roman\">11.</span></p>",
                    options_en: [
                        "<p>drown</p>",
                        "<p>result</p>",
                        "<p>exist</p>",
                        "<p>indulge</p>"
                    ],
                    options_hi: [
                        "",
                        "",
                        "",
                        ""
                    ],
                    solution_en: "<p><span style=\"font-family: Times New Roman;\">(b) All other options except &lsquo;result&rsquo; do not fit in the context of the passage. Hence, &lsquo;result&rsquo; is the most appropriate answer.</span></p>",
                    solution_hi: "<p><span style=\"font-family: Times New Roman;\">(b) All other options except &lsquo;result&rsquo; do not fit in the context of the passage. Hence, &lsquo;result&rsquo; is the most appropriate answer.</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "7",
                    question_en: "<p>Directions (10-14): In the following passage word have been deleted.Fill in the blanks with the help of the alternatives given.Select the most appropriate option for each blank</p>\r\n<p><span style=\"font-weight: 400; font-family: \'times new roman\', times, serif;\">Decisions may be&hellip;..(10)...... at either through initiative or through inertia.Many misfortunes in the world come across the bridge of indecision.Power corrupts but weakness also corrupts.The fruits of weakness like fear,bitterness and suspicion can &hellip;..(11).......in a paralysis of decision.Inner decision can produce indecision and indecision results by weakness of character.An occasional wrong decision may be sometimes &hellip;..(12)......... to indecision.The &hellip;..(13)......man is often self centered. He fears making a &hellip;..(14)....... and the fear paralyses him.</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Select the most appropriate option for blank no.12.</span></p>",
                    question_hi: "</span></p> <p><span style=\"font-family:Times New Roman\">Q12</span></p>",
                    options_en: [
                        "<p>useful</p>",
                        "<p>preferable</p>",
                        "<p>acceptable</p>",
                        "<p>susceptible</p>"
                    ],
                    options_hi: [
                        "",
                        "",
                        "",
                        ""
                    ],
                    solution_en: "<p><span style=\"font-family: Times New Roman;\">(b) </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Preferable means </span><span style=\"font-family: Times New Roman;\">better or more suitable &amp; prefer takes the preposition &lsquo;to&rsquo;.</span><span style=\"font-family: Times New Roman;\"> Hence, &lsquo;preferable&rsquo; is the most appropriate answer.</span></p>",
                    solution_hi: "<p><span style=\"font-family: Times New Roman;\">(b) </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Preferable means </span><span style=\"font-family: Times New Roman;\">better or more suitable &amp; prefer takes the preposition &lsquo;to&rsquo;.</span><span style=\"font-family: Times New Roman;\"> Hence, &lsquo;preferable&rsquo; is the most appropriate answer.</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "7",
                    question_en: "<p>Directions (10-14): In the following passage word have been deleted.Fill in the blanks with the help of the alternatives given.Select the most appropriate option for each blank</p>\r\n<p><span style=\"font-weight: 400; font-family: \'times new roman\', times, serif;\">Decisions may be&hellip;..(10)...... at either through initiative or through inertia.Many misfortunes in the world come across the bridge of indecision.Power corrupts but weakness also corrupts.The fruits of weakness like fear,bitterness and suspicion can &hellip;..(11).......in a paralysis of decision.Inner decision can produce indecision and indecision results by weakness of character.An occasional wrong decision may be sometimes &hellip;..(12)......... to indecision.The &hellip;..(13)......man is often self centered. He fears making a &hellip;..(14)....... and the fear paralyses him.</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Select the most appropriate option for blank no. 13.</span></p>",
                    question_hi: "</span></p> <p><span style=\"font-family:Times New Roman\">Q13.</span></p>",
                    options_en: [
                        "<p>dishonest</p>",
                        "<p>common</p>",
                        "<p>selfish</p>",
                        "<p>indecisive</p>"
                    ],
                    options_hi: [
                        "",
                        "",
                        "",
                        ""
                    ],
                    solution_en: "<p><span style=\"font-family: Times New Roman;\">(d) </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">&lsquo;Indecisive&rsquo; means </span><span style=\"font-family: Times New Roman;\">not able to make decisions easily</span><span style=\"font-family: Times New Roman;\">. The given passage states that the man who is </span><span style=\"font-family: Times New Roman;\">not able to make decisions easily</span><span style=\"font-family: Times New Roman;\"> is often self-centred. Hence, &lsquo;indecisive&rsquo; is the most appropriate answer.</span></p>",
                    solution_hi: "<p><span style=\"font-family: Times New Roman;\">(d) </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">&lsquo;Indecisive&rsquo; means </span><span style=\"font-family: Times New Roman;\">not able to make decisions easily</span><span style=\"font-family: Times New Roman;\">. The given passage states that the man who is </span><span style=\"font-family: Times New Roman;\">not able to make decisions easily</span><span style=\"font-family: Times New Roman;\"> is often self-centred. Hence, &lsquo;indecisive&rsquo; is the most appropriate answer.</span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "7",
                    question_en: "<p>Directions (10-14): In the following passage word have been deleted.Fill in the blanks with the help of the alternatives given.Select the most appropriate option for each blank</p>\r\n<p><span style=\"font-weight: 400; font-family: \'times new roman\', times, serif;\">Decisions may be&hellip;..(10)...... at either through initiative or through inertia.Many misfortunes in the world come across the bridge of indecision.Power corrupts but weakness also corrupts.The fruits of weakness like fear,bitterness and suspicion can &hellip;..(11).......in a paralysis of decision.Inner decision can produce indecision and indecision results by weakness of character.An occasional wrong decision may be sometimes &hellip;..(12)......... to indecision.The &hellip;..(13)......man is often self centered. He fears making a &hellip;..(14)....... and the fear paralyses him.</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Select the most appropriate option for blank no. 14.</span></p>",
                    question_hi: "</span></p> <p><span style=\"font-family:Times New Roman\">Q</span><span style=\"font-family:Times New Roman\">14.</span></p>",
                    options_en: [
                        "<p>decision</p>",
                        "<p>query</p>",
                        "<p>mistake</p>",
                        "<p>presentation</p>"
                    ],
                    options_hi: [
                        "",
                        "",
                        "",
                        ""
                    ],
                    solution_en: "<p><span style=\"font-family: Times New Roman;\">(a)</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">&lsquo;Decision&rsquo; means </span><span style=\"font-family: Times New Roman;\">a choice or judgement that you make after thinking about various possibilities</span><span style=\"font-family: Times New Roman;\">. Hence, &lsquo;decision &rsquo; is the most appropriate answer.</span></p>",
                    solution_hi: "<p><span style=\"font-family: Times New Roman;\">(a)</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">&lsquo;Decision&rsquo; means </span><span style=\"font-family: Times New Roman;\">a choice or judgement that you make after thinking about various possibilities</span><span style=\"font-family: Times New Roman;\">. Hence, &lsquo;decision &rsquo; is the most appropriate answer.</span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "7",
                    question_en: " <p><span style=\"font-family:Times New Roman\">15.Select the most appropriate option to fill in the blanks</span></p> <p><span style=\"font-family:Times New Roman\">It was too hot so some volunteers handed______ fresh lime soda to everyone.",
                    question_hi: "</span></p>",
                    options_en: [
                        " <p> on</span></p>",
                        " <p> out</span></p>",
                        " <p> around</span></p>",
                        " <p> over</span></p>"
                    ],
                    options_hi: [
                        "",
                        "",
                        "",
                        ""
                    ],
                    solution_en: " <p> </span><span style=\"font-family:Times New Roman\">(c) </span></p> <p><span style=\"font-family:Times New Roman\">‘Hand around’ means to offer around things like drinks/tea/biscuits, you give one to each person in a group(like volunteers did in the given sentence). Hence, ‘around’ is the most appropriate answer.</span></p>",
                    solution_hi: " <p> </span><span style=\"font-family:Times New Roman\">(c) </span></p> <p><span style=\"font-family:Times New Roman\">‘Hand around’ means to offer around things like drinks/tea/biscuits, you give one to each person in a group(like volunteers did in the given sentence). Hence, ‘around’ is the most appropriate answer.</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "16",
                    section: "7",
                    question_en: " <p>16.Select the most appropriate meaning of given idiom</span></p> <p><span style=\"font-family:Times New Roman\">Drop-out of ",
                    question_hi: "</span></p>",
                    options_en: [
                        " <p> Not to attempt                       </span></p>",
                        " <p> Half hearted in the attempt</span></p>",
                        " <p> Falter in the attempt              </span></p>",
                        " <p> Fail in the attempt</span></p>"
                    ],
                    options_hi: [
                        "",
                        "",
                        "",
                        ""
                    ],
                    solution_en: " <p> </span><span style=\"font-family:Times New Roman\">(d) </span></p> <p><span style=\"font-family:Times New Roman\">Fail in the attempt</span></p>",
                    solution_hi: " <p> </span><span style=\"font-family:Times New Roman\">(d) </span></p> <p><span style=\"font-family:Times New Roman\">Fail in the attempt</span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "17",
                    section: "7",
                    question_en: " <p>17.In the sentence identify the segment that contains grammatical error.</span></p> <p><span style=\"font-family:Times New Roman\">Dense fog on the highway was the cause of the collusion between the truck and the school bus.",
                    question_hi: "</span></p>",
                    options_en: [
                        " <p>Dense fog on the highway    </span></p>",
                        " <p>was the cause of the collusion</span></p>",
                        " <p>between the truck and the school bus.       </span></p>",
                        " <p>No error</span></p>"
                    ],
                    options_hi: [
                        "",
                        "",
                        "",
                        ""
                    ],
                    solution_en: " <p> </span><span style=\"font-family:Times New Roman\">(b)</span></p> <p><span style=\"font-family:Times New Roman\"> Collision is when two things(truck and bus in the given sentence) hit violently against each other. Hence, ‘was the cause of the collision’ is the most appropriate answer.</span></p>",
                    solution_hi: " <p> </span><span style=\"font-family:Times New Roman\">(b)</span></p> <p><span style=\"font-family:Times New Roman\"> Collision is when two things(truck and bus in the given sentence) hit violently against each other. Hence, ‘was the cause of the collision’ is the most appropriate answer.</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "18",
                    section: "7",
                    question_en: " <p>18.Select the synonym of the following word</span></p> <p><span style=\"font-family:Times New Roman\">OBVERSE",
                    question_hi: "</span></p>",
                    options_en: [
                        " <p>bitter              </span></p>",
                        " <p>reverse            </span></p>",
                        " <p>opposite          </span></p>",
                        " <p>adverse</span></p>"
                    ],
                    options_hi: [
                        "",
                        "",
                        "",
                        ""
                    ],
                    solution_en: " <p> </span><span style=\"font-family:Times New Roman\">(c) </span></p> <p><span style=\"font-family:Times New Roman\">Obverse- Opposite </span></p> <p><span style=\"font-family:Times New Roman\">Bitter- </span><span style=\"font-family:Times New Roman\">caused by anger or hatred</span></p> <p><span style=\"font-family:Times New Roman\">Reverse- </span><span style=\"font-family:Times New Roman\">to put something in the opposite position to normal or to how it was before</span></p> <p><span style=\"font-family:Times New Roman\">Adverse- </span><span style=\"font-family:Times New Roman\">making something difficult for somebody</span></p>",
                    solution_hi: " <p> </span><span style=\"font-family:Times New Roman\">(c) </span></p> <p><span style=\"font-family:Times New Roman\">Obverse- Opposite </span></p> <p><span style=\"font-family:Times New Roman\">Bitter- </span><span style=\"font-family:Times New Roman\">caused by anger or hatred</span></p> <p><span style=\"font-family:Times New Roman\">Reverse- </span><span style=\"font-family:Times New Roman\">to put something in the opposite position to normal or to how it was before</span></p> <p><span style=\"font-family:Times New Roman\">Adverse- </span><span style=\"font-family:Times New Roman\">making something difficult for somebody</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "19",
                    section: "7",
                    question_en: "<p>19.Select the wrongly spelt word</p>\n",
                    question_hi: "</span></p>",
                    options_en: [
                        "<p>forehead</p>\n",
                        "<p>feign</p>\n",
                        "<p>forfiet</p>\n",
                        "<p>foreign</p>\n"
                    ],
                    options_hi: [
                        "",
                        "",
                        "",
                        ""
                    ],
                    solution_en: "<p><span style=\"font-family: Times New Roman;\">(c) </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Forfeit is the correct spelling</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">feign - pretend to be affected by (a feeling, state, or injury)</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">forfiet - a fine or penalty for wrongdoing</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">foreign&nbsp; - strange and unfamiliar</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Times New Roman;\">(c) </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Forfeit is the correct spelling</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">feign - pretend to be affected by (a feeling, state, or injury)</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">forfiet - a fine or penalty for wrongdoing</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">foreign&nbsp; - strange and unfamiliar</span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "20",
                    section: "7",
                    question_en: "<p>20.Select the synonym of the following word</p>\r\n<p><span style=\"font-family: Times New Roman;\">ELUDE</span></p>",
                    question_hi: "</span></p>",
                    options_en: [
                        "<p>confuse</p>",
                        "<p>dodge</p>",
                        "<p>despair</p>",
                        "<p>mislead</p>"
                    ],
                    options_hi: [
                        "",
                        "",
                        "",
                        ""
                    ],
                    solution_en: "<p><span style=\"font-family: Times New Roman;\">(b) </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Elude/Dodge - to move quickly in order to avoid somebody / something.</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Despair- </span><span style=\"font-family: Times New Roman;\">the state of having lost all hope</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Mislead- </span><span style=\"font-family: Times New Roman;\">to make somebody have the wrong idea or opinion about somebody/something</span></p>",
                    solution_hi: "<p><span style=\"font-family: Times New Roman;\">(b) </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Elude/Dodge - to move quickly in order to avoid somebody / something.</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Despair- </span><span style=\"font-family: Times New Roman;\">the state of having lost all hope</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Mislead- </span><span style=\"font-family: Times New Roman;\">to make somebody have the wrong idea or opinion about somebody/something</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "21",
                    section: "7",
                    question_en: " <p>21.Select the passive form of the given sentence</span></p> <p><span style=\"font-family:Times New Roman\">She asked me if I could answer all the questions in the paper.",
                    question_hi: "</span></p>",
                    options_en: [
                        " <p> I were asked if I could answer all the questions in the paper.</span></p>",
                        " <p> I was asked if I could answer all the question in the paper.</span></p>",
                        " <p> I was asked if I could answer all the questions in the paper.</span></p>",
                        " <p> I was asked if I answered all the questions in the paper.</span></p>"
                    ],
                    options_hi: [
                        "",
                        "",
                        "",
                        ""
                    ],
                    solution_en: " <p> </span><span style=\"font-family:Times New Roman\">(c) </span></p> <p><span style=\"font-family:Times New Roman\">a. I were asked if I could answer all the questions in the paper. (Incorrect helping verb)</span></p> <p><span style=\"font-family:Times New Roman\">b. I was asked if I could answer all the question in the paper. (Incorrect word)</span></p> <p><span style=\"font-family:Times New Roman\">c. I was asked if I could answer all the questions in the paper. (Correct)</span></p> <p><span style=\"font-family:Times New Roman\">d. I was asked if I answered all the questions in the paper. (could is missing)</span></p>",
                    solution_hi: " <p> </span><span style=\"font-family:Times New Roman\">(c) </span></p> <p><span style=\"font-family:Times New Roman\">a. I were asked if I could answer all the questions in the paper. (Incorrect helping verb)</span></p> <p><span style=\"font-family:Times New Roman\">b. I was asked if I could answer all the question in the paper. (Incorrect word)</span></p> <p><span style=\"font-family:Times New Roman\">c. I was asked if I could answer all the questions in the paper. (Correct)</span></p> <p><span style=\"font-family:Times New Roman\">d. I was asked if I answered all the questions in the paper. (could is missing)</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "22",
                    section: "7",
                    question_en: "<p>22.Select the wrongly spelt word</p>\n",
                    question_hi: "</span></p>",
                    options_en: [
                        "<p>naration</p>\n",
                        "<p>neolithic</p>\n",
                        "<p>nostalgia</p>\n",
                        "<p>nocturnal</p>\n"
                    ],
                    options_hi: [
                        "",
                        "",
                        "",
                        ""
                    ],
                    solution_en: "<p><span style=\"font-family: Times New Roman;\">(a)</span></p>\r\n<p><span style=\"font-family: Times New Roman;\"> Narration is the correct spelling</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Narration - the action or process of narrating a story</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">neolithic - the later part of the Stone Age</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">nostalgia - something done or presented in order to evoke feelings of nostalgia</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">nocturnal - active at night</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Times New Roman;\">(a)</span></p>\r\n<p><span style=\"font-family: Times New Roman;\"> Narration is the correct spelling</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Narration - the action or process of narrating a story</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">neolithic - the later part of the Stone Age</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">nostalgia - something done or presented in order to evoke feelings of nostalgia</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">nocturnal - active at night</span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "23",
                    section: "7",
                    question_en: " <p>23.Select the antonym of the following word</span></p> <p><span style=\"font-family:Times New Roman\">PASTEL",
                    question_hi: "</span></p>",
                    options_en: [
                        " <p>urban            </span></p>",
                        " <p>delicate          </span></p>",
                        " <p>bright               </span></p>",
                        " <p>sweet</span></p>"
                    ],
                    options_hi: [
                        "",
                        "",
                        "",
                        ""
                    ],
                    solution_en: " <p> </span><span style=\"font-family:Times New Roman\">(c) </span></p> <p><span style=\"font-family:Times New Roman\">Pastel-a soft and delicate shade of a colour</span></p> <p><span style=\"font-family:Times New Roman\">Bright- </span><span style=\"font-family:Times New Roman\">having a lot of light</span></p> <p><span style=\"font-family:Times New Roman\">Delicate- </span><span style=\"font-family:Times New Roman\">easy to damage or break</span></p>",
                    solution_hi: " <p> </span><span style=\"font-family:Times New Roman\">(c) </span></p> <p><span style=\"font-family:Times New Roman\">Pastel-a soft and delicate shade of a colour</span></p> <p><span style=\"font-family:Times New Roman\">Bright- </span><span style=\"font-family:Times New Roman\">having a lot of light</span></p> <p><span style=\"font-family:Times New Roman\">Delicate- </span><span style=\"font-family:Times New Roman\">easy to damage or break</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "24",
                    section: "7",
                    question_en: " <p>24.In the sentence identify the segment that contains grammatical error</span></p> <p><span style=\"font-family:Times New Roman\">He asked me many time if I could lend him some money as his business was not doing well.",
                    question_hi: "</span></p>",
                    options_en: [
                        " <p>He asked me many time if</span></p>",
                        " <p>I could lend him some money</span></p>",
                        " <p>as his business was not doing well.</span></p>",
                        " <p>No error</span></p>"
                    ],
                    options_hi: [
                        "",
                        "",
                        "",
                        ""
                    ],
                    solution_en: " <p> </span><span style=\"font-family:Times New Roman\">(a)</span></p> <p><span style=\"font-family:Times New Roman\">  We will replace time with times because ‘many’ is used in the given sentence. Hence, ‘he asked me many times’ is the most appropriate answer.</span></p>",
                    solution_hi: " <p> </span><span style=\"font-family:Times New Roman\">(a)</span></p> <p><span style=\"font-family:Times New Roman\">  We will replace time with times because ‘many’ is used in the given sentence. Hence, ‘he asked me many times’ is the most appropriate answer.</span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "25",
                    section: "misc",
                    question_en: "<p>25.Given are jumbled sentences.Select the option that gives their correct order</p>\r\n<p><span style=\"font-family: Times New Roman;\">1. The tiny bacterial plants that live in the soil help to prepare food for the plants we cultivate. </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">P. The farmer works very hard to make the soil favorable. </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Q But these soil bacteria are very necessary and helpful. </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">R. There are millions of bacteria in a cubic inch of fertile soil.</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">S. Some kinds of bacteria are harmful. </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">6. They do not need sunlight as do most plants. </span></p>",
                    question_hi: "</span></p>",
                    options_en: [
                        "<p>SQPR</p>",
                        "<p>QSPR</p>",
                        "<p>RPSQ</p>",
                        "<p>PRQS</p>"
                    ],
                    options_hi: [
                        "",
                        "",
                        "",
                        ""
                    ],
                    solution_en: "<p><span style=\"font-family: Times New Roman;\">(c) RPSQ</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Sentence R mentions how many bacteria are present in a cubic inch of soil &amp; Sentence P mentions efforts of farmers So, P will follow R. Further, Sentence S mentions some bacteria are harmful &amp; Sentence Q mentions beneficial bacteria. So, Q will follow S. Going through the options, option c has the correct sequence.</span></p>",
                    solution_hi: "<p><span style=\"font-family: Times New Roman;\">(c) RPSQ</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Sentence R mentions how many bacteria are present in a cubic inch of soil &amp; Sentence P mentions efforts of farmers So, P will follow R. Further, Sentence S mentions some bacteria are harmful &amp; Sentence Q mentions beneficial bacteria. So, Q will follow S. Going through the options, option c has the correct sequence.</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'question-box d-flex align-items-center justify-content-center border rounded';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.question-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            const html = `
                <div class="card shadow-sm mb-4">
                    <div class="card-body">
                        <div class="question-text mb-4">
                            <div class="en">${question.question_en}</div>
                            <div class="hi" style="display:none">${question.question_hi}</div>
                        </div>
                        <div class="options">
                            ${['a', 'b', 'c', 'd'].map((opt, i) => `
                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="radio" name="q${index}" 
                                        id="opt-${index}-${opt}" value="${opt}" 
                                        ${answers[question.id] === opt ? 'checked' : ''}
                                        onclick="selectAnswer('${question.id}', '${opt}')">
                                    <label class="form-check-label w-100 py-2 px-3 rounded hover-bg-light" for="opt-${index}-${opt}">
                                        <div class="en">${question.options_en[i]}</div>
                                        <div class="hi" style="display:none">${question.options_hi[i]}</div>
                                    </label>
                                </div>
                            `).join('')}
                        </div>
                        ${submitted ? `
                            <div class="alert ${answers[question.id] === question.correct ? 'alert-success' : 'alert-danger'} mt-3">
                                ${answers[question.id] ? (answers[question.id] === question.correct ? 'Correct!' : 'Incorrect!') : 'Not attempted'}
                                <div class="mt-2">
                                    <strong>Solution:</strong>
                                    <div class="en">${question.solution_en}</div>
                                    <div class="hi" style="display:none">${question.solution_hi}</div>
                                </div>
                            </div>
                        ` : ''}
                    </div>
                </div>
            `;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // If in mobile view, close the menu
            if (window.innerWidth < 768) {
                toggleMenu(false);
            }
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                document.getElementById(`box-${index}`).classList.add('attempted');
            }
        }
        // Navigate to previous question
        function prevQuestion() {
            if (currentQuestion > 0) {
                showQuestion(currentQuestion - 1);
            }
        }
        // Navigate to next question
        function nextQuestion() {
            if (currentQuestion < questions.length - 1) {
                showQuestion(currentQuestion + 1);
            }
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) {
                showQuestion(section.start);
            }
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update button text
            document.getElementById('lang-btn').innerHTML = currentLang === 'en' ? 
                '<i class="fas fa-language"></i> हिंदी' : 
                '<i class="fas fa-language"></i> English';
        }
        // Toggle menu for mobile
        function toggleMenu(show) {
            const nav = document.getElementById('question-nav');
            const content = document.getElementById('question-content');
            if (show === undefined) {
                show = nav.style.right === '-100%' || nav.style.right === '';
            }
            if (show) {
                nav.style.right = '0';
                if (window.innerWidth >= 768) {
                    content.style.marginRight = '320px';
                }
            } else {
                nav.style.right = window.innerWidth < 768 ? '-100%' : '0';
                content.style.marginRight = window.innerWidth < 768 ? '0' : '320px';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
            document.getElementById('total-count').textContent = questions.length;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.remove('attempted');
                    box.classList.add('bg-secondary', 'text-white');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.remove('attempted');
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.remove('attempted');
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            resultsModal.show();
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check-circle me-1"></i> Submitted';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            resultsModal.hide();
            showQuestion(0);
        }
    </script>
</body>
</html>