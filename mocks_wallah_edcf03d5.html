<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">30:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 26</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">26</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 30 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 0,
                end: 24
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 25,
                end: 25
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "17",
                    question_en: "<p>1. <span style=\"font-family: Times New Roman;\">A sold a mobile phone to B at a gain of 25% and B sold it to C at a loss of 10%. If C paid ₹5,625 for it, how much did A pay (in ₹) for the phone ?</span></p>",
                    question_hi: "<p>1. <span style=\"font-family: Baloo;\">A ने एक मोबाइल फोन B को 25% के लाभ पर बेचा और B ने इसे C को 10% की हानि पर बेच दिया। यदि C ने इसके लिए ₹5,625 का भुगतान किया, तो A ने फ़ोन के लिए (₹ में) कितना भुगतान किया?</span></p>",
                    options_en: ["<p>5,000</p>", "<p>4,800</p>", 
                                "<p>4,500</p>", "<p>5,100</p>"],
                    options_hi: ["<p>5,000</p>", "<p>4,800</p>",
                                "<p>4,500</p>", "<p>5,100</p>"],
                    solution_en: "<p>1.(a) <span style=\"font-family: Times New Roman;\">Let the CP for A = y</span><br><span style=\"font-family: Times New Roman;\">According to the question,</span><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>y</mi><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mfrac><mrow><mn>100</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>25</mn></mrow><mn>100</mn></mfrac><mo>&#160;</mo><mo>&#215;</mo><mfrac><mrow><mo>&#160;</mo><mn>100</mn><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mn>10</mn></mrow><mn>100</mn></mfrac><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>5625</mn><mo>&#160;</mo></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>y</mi><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mfrac><mn>125</mn><mn>100</mn></mfrac><mo>&#160;</mo><mo>&#215;</mo><mfrac><mrow><mo>&#160;</mo><mn>90</mn><mo>&#160;</mo></mrow><mn>100</mn></mfrac><mo>=</mo><mo>&#160;</mo><mn>5625</mn><mo>&#160;</mo><mo>&#160;</mo></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>y</mi><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mo>&#8377;</mo><mn>5</mn><mo>,</mo><mn>000</mn></math></p>\n<p>&nbsp;</p>",
                    solution_hi: "<p>1.(a) <span style=\"font-family: Baloo;\">माना A का लागत मूल्य = y</span><br><span style=\"font-family: Baloo;\">प्रश्न के अनुसार,</span><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>y</mi><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mfrac><mrow><mn>100</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>25</mn></mrow><mn>100</mn></mfrac><mo>&#160;</mo><mo>&#215;</mo><mfrac><mrow><mo>&#160;</mo><mn>100</mn><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mn>10</mn></mrow><mn>100</mn></mfrac><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>5625</mn><mo>&#160;</mo></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>y</mi><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mfrac><mn>125</mn><mn>100</mn></mfrac><mo>&#160;</mo><mo>&#215;</mo><mfrac><mrow><mo>&#160;</mo><mn>90</mn><mo>&#160;</mo></mrow><mn>100</mn></mfrac><mo>=</mo><mo>&#160;</mo><mn>5625</mn><mo>&#160;</mo><mo>&#160;</mo></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>y</mi><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mo>&#8377;</mo><mn>5</mn><mo>,</mo><mn>000</mn></math></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "17",
                    question_en: "<p>2. <span style=\"font-family: Times New Roman;\">The profit earned by selling an article for ₹832 is equal to the loss incurred when the article is sold for ₹448. What will be the selling price of the article if it is sold at a 10% loss?</span></p>",
                    question_hi: "<p>2. <span style=\"font-family: Baloo;\">किसी वस्तु को ₹832 में बेचने पर अर्जित लाभ उस वस्तु को ₹448 में बेचने पर हुई हानि के बराबर है। यदि वस्तु को 10% हानि पर बेचा जाता है तो उसका विक्रय मूल्य क्या होगा?</span></p>",
                    options_en: ["<p>₹576</p>", "<p>₹540</p>", 
                                "<p>₹625</p>", "<p>₹640</p>"],
                    options_hi: ["<p>₹576</p>", "<p>₹540</p>",
                                "<p>₹625</p>", "<p>₹640</p>"],
                    solution_en: "<p>2.(a) <span style=\"font-family: Times New Roman;\">Let, CP = a</span><br><span style=\"font-family: Times New Roman;\">According to the question,</span><br><span style=\"font-family: Times New Roman;\">832 &ndash; </span><span style=\"font-family: Times New Roman;\">a = a</span><span style=\"font-family: Times New Roman;\"> &ndash; 448</span><br><span style=\"font-family: Times New Roman;\">2a = 1280</span><br><span style=\"font-family: Times New Roman;\">a = 640</span><br><span style=\"font-family: Times New Roman;\">SP at 10% loss </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>=</mo><mo>&#160;</mo><mn>640</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mfrac><mrow><mn>90</mn><mo>&#160;</mo></mrow><mn>100</mn></mfrac><mo>=</mo><mo>&#160;</mo><mo>&#8377;</mo><mn>576</mn></math></p>",
                    solution_hi: "<p>2.(a) <span style=\"font-family: Baloo;\">माना, लागत मूल्य = a</span><br><span style=\"font-family: Baloo;\">प्रश्न के अनुसार,</span><br><span style=\"font-family: Times New Roman;\">832 &ndash; </span><span style=\"font-family: Times New Roman;\">a = a</span><span style=\"font-family: Times New Roman;\"> &ndash; 448</span><br><span style=\"font-family: Times New Roman;\">2a = 1280</span><br><span style=\"font-family: Times New Roman;\">a = 640</span><br><span style=\"font-family: Baloo;\">10% हानि पर विक्रय मूल्य = </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>640</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mfrac><mn>90</mn><mn>100</mn></mfrac><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mo>&#8377;</mo><mn>576</mn></math></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "17",
                    question_en: "<p>3. <span style=\"font-family: Times New Roman;\">A person sold an article at a loss of 18%. Had it been sold for Rs. 960 more, he would have gained 12%. If the article is sold for Rs. 3,840, then how much is the profit percentage?</span></p>",
                    question_hi: "<p>3. <span style=\"font-family: Baloo;\">एक व्यक्ति ने एक वस्तु को 18% की हानि पर बेचा। यदि इसे 960 रुपये अधिक में बेचा जाता, तो उसे 12% का लाभ होता। यदि वस्तु को 3,840 रुपये में बेचा जाता है, तो लाभ प्रतिशत कितना है?</span></p>",
                    options_en: ["<p>15%</p>", "<p>24%</p>", 
                                "<p>20%</p>", "<p>21%</p>"],
                    options_hi: ["<p>15%</p>", "<p>24%</p>",
                                "<p>20%</p>", "<p>21%</p>"],
                    solution_en: "<p>3.(c) <span style=\"font-family: Times New Roman;\">Here, 12% - (-18%) = 30%, which corresponds to </span><span style=\"font-family: Times New Roman;\">₹</span><span style=\"font-family: Times New Roman;\">960.</span><br><span style=\"font-family: Times New Roman;\">So, CP =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>960</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>100</mn></mrow><mrow><mn>30</mn><mo>&#160;</mo></mrow></mfrac></math> </span><span style=\"font-family: Times New Roman;\"> = </span><span style=\"font-family: Times New Roman;\">₹</span><span style=\"font-family: Times New Roman;\">3,200</span><br><span style=\"font-family: Times New Roman;\">Profit percentage = </span><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Times New Roman;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>3840</mn><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mn>3200</mn></mrow><mrow><mo>&#160;</mo><mn>3200</mn></mrow></mfrac><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>100</mn><mo>&#160;</mo></math> = 20%</span></p>",
                    solution_hi: "<p>3.(c) <span style=\"font-family: Baloo;\">यहाँ, 12% &ndash; ( &ndash;18%) = 30%, जो ₹960 के अनुरूप है।</span><br><span style=\"font-family: Baloo;\">अत: क्रय मूल्य = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>960</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>100</mn></mrow><mrow><mn>30</mn><mo>&#160;</mo></mrow></mfrac></math></span><span style=\"font-family: Times New Roman;\"> = ₹3,200</span><br><span style=\"font-family: Baloo;\">लाभ प्रतिशत = </span><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Times New Roman;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>3840</mn><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mn>3200</mn></mrow><mrow><mo>&#160;</mo><mn>3200</mn></mrow></mfrac><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>100</mn><mo>&#160;</mo></math> = 20%</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "17",
                    question_en: "<p>4. <span style=\"font-family: Times New Roman;\">A trader sells an article for Rs. 425 and loses 15%. At what price (in Rs.) should he sell the article to earn a profit of 5%?</span></p>",
                    question_hi: "<p>4. <span style=\"font-family: Baloo;\">एक व्यापारी एक वस्तु को 425 रुपये में बेचता है और उसे 15% का हानि होती है। 5% का लाभ अर्जित करने के लिए उसे वस्तु को किस कीमत पर (रुपयों में) बेचना चाहिए?</span></p>",
                    options_en: ["<p>505</p>", "<p>510</p>", 
                                "<p>445</p>", "<p>525</p>\n<p><span style=\"font-family: Times New Roman;\"> </span></p>"],
                    options_hi: ["<p>505</p>", "<p>510</p>",
                                "<p>445</p>", "<p>525</p>"],
                    solution_en: "<p>4.(d) <span style=\"font-family: Times New Roman;\">Here, </span><span style=\"font-family: Times New Roman;\">₹425 corresponds to 85%,</span><br><span style=\"font-family: Times New Roman;\">so SP at 5% profit = </span><span style=\"font-family: Times New Roman;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>425</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>105</mn><mo>)</mo><mo>&#160;</mo></mrow><mn>85</mn></mfrac><mo>=</mo><mo>&#160;</mo><mo>&#8377;</mo><mn>525</mn></math></span></p>",
                    solution_hi: "<p>4.(d) <span style=\"font-family: Baloo;\">यहां, ₹425, 85% के बराबर है</span><br><span style=\"font-family: Baloo;\">अत: 5% लाभ पर विक्रय मूल्य = </span><span style=\"font-family: Times New Roman;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>425</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>105</mn><mo>)</mo><mo>&#160;</mo></mrow><mn>85</mn></mfrac><mo>=</mo><mo>&#160;</mo><mo>&#8377;</mo><mn>525</mn></math></span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "17",
                    question_en: "<p>5.<span style=\"font-family: Times New Roman;\"> A shopkeeper marks his goods at a price 20% higher than their cost price and allows 10% discount on every item. Find his gain percentage.</span></p>",
                    question_hi: "<p>5.<span style=\"font-family: Baloo;\"> एक दुकानदार अपने माल को उनके क्रय मूल्य से 20% अधिक मूल्य पर अंकित करता है और प्रत्येक वस्तु पर 10% छूट देता है। उसका लाभ प्रतिशत ज्ञात कीजिये।</span></p>",
                    options_en: ["<p>10%</p>", "<p>10.5%</p>", 
                                "<p>9%</p>", "<p>8%</p>"],
                    options_hi: ["<p>10%</p>", "<p>10.5%</p>",
                                "<p>9%</p>", "<p>8%</p>"],
                    solution_en: "<p>5.(d) <span style=\"font-family: Times New Roman;\">Let CP = 100, so MP = </span><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Times New Roman;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>120</mn><mn>100</mn></mfrac><mo>&#215;</mo><mn>100</mn><mo>=</mo><mn>120</mn></math></span><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>S</mi><mi>P</mi><mo>=</mo><mfrac><mrow><mo>&#160;</mo><mn>100</mn><mo>-</mo><mn>10</mn></mrow><mn>100</mn></mfrac><mo>&#215;</mo><mn>120</mn><mo>=</mo><mfrac><mrow><mo>&#160;</mo><mn>90</mn></mrow><mn>100</mn></mfrac><mo>&#215;</mo><mn>120</mn><mo>=</mo><mn>108</mn></math><br><span style=\"font-family: Times New Roman;\">Gain percentage = </span><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Times New Roman;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>108</mn><mo>-</mo><mn>100</mn></mrow><mn>100</mn></mfrac><mo>&#215;</mo><mn>100</mn><mo>=</mo><mn>8</mn><mo>%</mo></math></span></p>",
                    solution_hi: "<p>5.(d) <span style=\"font-family: Baloo;\">मान लीजिये CP = 100, इसलिए MP =&nbsp;</span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>120</mn><mn>100</mn></mfrac><mo>&#215;</mo><mn>100</mn><mo>=</mo><mn>120</mn></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>S</mi><mi>P</mi><mo>=</mo><mfrac><mrow><mo>&#160;</mo><mn>100</mn><mo>-</mo><mn>10</mn></mrow><mn>100</mn></mfrac><mo>&#215;</mo><mn>120</mn><mo>=</mo><mfrac><mrow><mo>&#160;</mo><mn>90</mn></mrow><mn>100</mn></mfrac><mo>&#215;</mo><mn>120</mn><mo>=</mo><mn>108</mn></math><br><span style=\"font-family: Baloo;\">लाभ प्रतिशत = </span><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Times New Roman;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>108</mn><mo>&#160;</mo><mo>-</mo><mn>100</mn></mrow><mn>100</mn></mfrac><mo>&#215;</mo><mn>100</mn><mo>=</mo><mn>8</mn><mo>%</mo></math></span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "17",
                    question_en: "<p>6. <span style=\"font-family: Times New Roman;\">&nbsp;The cost price of two articles A and B are in the ratio 4:5 . While selling these articles ,the shopkeepers gains 10% on article A and 20% on article B and the difference in their selling price is ₹480. The difference in the cost price (in ₹) of article B and A is ; </span></p>",
                    question_hi: "<p>6.<span style=\"font-family: Baloo;\"> दो वस्तुओं A और B का क्रय मूल्य 4:5 के अनुपात में है। इन वस्तुओं को बेचते समय, दुकानदारों को वस्तु A पर 10% और वस्तु B पर 20% का लाभ होता है और उनके विक्रय मूल्य में अंतर ₹480 है। वस्तु B और A के क्रय मूल्य (₹में) में अंतर क्या है?&nbsp;</span></p>",
                    options_en: ["<p>250</p>", "<p>300</p>", 
                                "<p>400</p>", "<p>350</p>"],
                    options_hi: ["<p>250</p>", "<p>300</p>",
                                "<p>400</p>", "<p>350</p>"],
                    solution_en: "<p>6.(b) <span style=\"font-family: Times New Roman;\">CP ratio of A and B = 4 : 5</span><br><span style=\"font-family: Times New Roman;\">Let CP of A = 4x and CP of B = 5x</span><br><span style=\"font-family: Times New Roman;\">According to the question,</span><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>5</mn><mi>x</mi><mo>&#215;</mo><mfrac><mn>120</mn><mn>100</mn></mfrac><mo>&#8211;</mo><mn>4</mn><mi>x</mi><mo>&#215;</mo><mfrac><mrow><mo>&#160;</mo><mn>110</mn></mrow><mn>100</mn></mfrac><mo>=</mo><mn>480</mn><mo>&#160;</mo></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>6</mn><mi>x</mi><mo>&#8211;</mo><mn>4</mn><mo>.</mo><mn>4</mn><mi>x</mi><mo>=</mo><mn>480</mn><mo>&#160;</mo></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>1</mn><mo>.</mo><mn>6</mn><mi>x</mi><mo>=</mo><mn>480</mn><mo>&#160;</mo></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>x</mi><mo>=</mo><mn>300</mn></math><br><span style=\"font-family: Times New Roman;\">Difference in CP of B and A = 5x - 4x = x = </span><span style=\"font-family: Times New Roman;\">₹</span><span style=\"font-family: Times New Roman;\">300</span></p>",
                    solution_hi: "<p>6.(b) <span style=\"font-family: Baloo;\">CP, A और B का अनुपात = 4 : 5</span><br><span style=\"font-family: Baloo;\">मान लीजिये A का CP = 4x और B का CP = 5x</span><br><span style=\"font-family: Baloo;\">प्रश्न के अनुसार,</span><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>5</mn><mi>x</mi><mo>&#215;</mo><mfrac><mn>120</mn><mn>100</mn></mfrac><mo>&#8211;</mo><mn>4</mn><mi>x</mi><mo>&#215;</mo><mfrac><mrow><mo>&#160;</mo><mn>110</mn></mrow><mn>100</mn></mfrac><mo>=</mo><mn>480</mn><mo>&#160;</mo></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>6</mn><mi>x</mi><mo>&#8211;</mo><mn>4</mn><mo>.</mo><mn>4</mn><mi>x</mi><mo>=</mo><mn>48</mn></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>1</mn><mo>.</mo><mn>6</mn><mi>x</mi><mo>=</mo><mn>480</mn><mo>&#160;</mo></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>x</mi><mo>=</mo><mn>300</mn></math><br><span style=\"font-family: Baloo;\">B और A के CP में अंतर = 5x - 4x = x = </span><span style=\"font-family: Times New Roman;\">₹</span><span style=\"font-family: Times New Roman;\">300</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "17",
                    question_en: "<p>7.<span style=\"font-family: Times New Roman;\"> A person&rsquo;s salary was decreased by 50% and subsequently increased by 50% and then again increased by 100%. How much percentage does he lose or gain?</span></p>",
                    question_hi: "<p>7. <span style=\"font-family: Baloo;\">एक व्यक्ति के वेतन में 50% की कमी की गई और बाद में 50% की वृद्धि हुई और फिर से 100% की वृद्धि हुई। उसे कितना प्रतिशत हानि या लाभ होता है?</span></p>",
                    options_en: ["<p>Loss of 40%</p>", "<p>Gain of 50%</p>", 
                                "<p>Gain of 25%</p>", "<p>Loss of 10%</p>"],
                    options_hi: ["<p>40% की हानि</p>", "<p>50% का लाभ</p>",
                                "<p>25% का लाभ</p>", "<p>10% की हानि</p>"],
                    solution_en: "<p>7.(b) <span style=\"font-family: Times New Roman;\">Let original salary = 100</span><br><span style=\"font-family: Times New Roman;\">According to the question,</span><br><span style=\"font-family: Times New Roman;\">New salary after all increase/decrease&nbsp;</span><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>=</mo><mo>&#160;</mo><mn>100</mn><mo>&#215;</mo><mfrac><mrow><mn>100</mn><mo>-</mo><mn>50</mn><mo>&#160;</mo></mrow><mn>100</mn></mfrac><mo>&#215;</mo><mfrac><mrow><mo>&#160;</mo><mn>100</mn><mo>+</mo><mn>50</mn></mrow><mn>100</mn></mfrac><mo>&#215;</mo><mfrac><mrow><mn>100</mn><mo>+</mo><mn>100</mn></mrow><mrow><mn>100</mn><mo>&#160;</mo></mrow></mfrac></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>=</mo><mo>&#160;</mo><mn>100</mn><mo>&#215;</mo><mfrac><mrow><mo>&#160;</mo><mn>50</mn></mrow><mn>100</mn></mfrac><mo>&#215;</mo><mfrac><mn>150</mn><mn>100</mn></mfrac><mo>&#215;</mo><mfrac><mrow><mn>200</mn><mo>&#160;</mo><mo>&#160;</mo></mrow><mn>100</mn></mfrac></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>=</mo><mo>&#160;</mo><mn>150</mn></math><br><span style=\"font-family: Times New Roman;\">So, there is gain = 150 &ndash; 100 = 50</span><br><span style=\"font-family: Times New Roman;\">Percentage gain =</span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>&#160;</mo><mn>50</mn></mrow><mn>100</mn></mfrac><mo>&#215;</mo><mn>100</mn><mo>=</mo><mn>50</mn><mo>%</mo></math></p>",
                    solution_hi: "<p>7.(b) <span style=\"font-family: Baloo;\">माना मूल वेतन = 100</span><br><span style=\"font-family: Baloo;\">प्रश्न के अनुसार,</span><br><span style=\"font-family: Baloo;\">सभी वृद्धि/कमी के बाद नया वेतन</span><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>=</mo><mo>&#160;</mo><mn>100</mn><mo>&#215;</mo><mfrac><mrow><mn>100</mn><mo>-</mo><mn>50</mn><mo>&#160;</mo></mrow><mn>100</mn></mfrac><mo>&#215;</mo><mfrac><mrow><mo>&#160;</mo><mn>100</mn><mo>+</mo><mn>50</mn></mrow><mn>100</mn></mfrac><mo>&#215;</mo><mfrac><mrow><mn>100</mn><mo>+</mo><mn>100</mn></mrow><mrow><mn>100</mn><mo>&#160;</mo></mrow></mfrac></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>=</mo><mn>100</mn><mo>&#215;</mo><mfrac><mrow><mo>&#160;</mo><mn>50</mn></mrow><mn>100</mn></mfrac><mo>&#215;</mo><mfrac><mn>150</mn><mn>100</mn></mfrac><mo>&#215;</mo><mfrac><mrow><mn>200</mn><mo>&#160;</mo><mo>&#160;</mo></mrow><mn>100</mn></mfrac></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>=</mo><mn>150</mn></math><br><span style=\"font-family: Baloo;\">अत: लाभ है = 150 &ndash; 100 = 50</span><br><span style=\"font-family: Baloo;\">लाभ प्रतिशत =</span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>&#160;</mo><mn>50</mn></mrow><mn>100</mn></mfrac><mo>&#215;</mo><mn>100</mn><mo>=</mo><mn>50</mn><mo>%</mo></math></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "17",
                    question_en: "<p>8.<span style=\"font-family: Times New Roman;\"> Aditya sells two wrist watches from his personal collection for </span><span style=\"font-family: Times New Roman;\">₹</span><span style=\"font-family: Times New Roman;\">12,600 each. On the first watch he gains 26% and on the second he loses 10%. Find the overall loss or profit percentage?</span></p>",
                    question_hi: "<p>8.<span style=\"font-family: Baloo;\"> आदित्य अपने निजी संग्रह की दो कलाई घड़ियों मैं से प्रत्येक को ₹12,600 में बेचता हैं। पहली घड़ी में उसे 26% का लाभ होता है और दूसरी मैं उसे 10% की हानि होती है। कुल हानि या लाभ प्रतिशत ज्ञात कीजिये?&nbsp;</span></p>",
                    options_en: ["<p>Gain of 16%</p>", "<p>Gain of 5%</p>", 
                                "<p>Loss of 5%</p>", "<p>Gain of 12%</p>"],
                    options_hi: ["<p>16% का लाभ</p>", "<p>5% का लाभ</p>",
                                "<p>5% की हानि</p>", "<p>12% का लाभ</p>"],
                    solution_en: "<p>8.(b) <span style=\"font-family: Times New Roman;\">CP of first watch = </span><span style=\"font-family: Times New Roman;\"> </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>12600</mn><mo>&#215;</mo><mn>100</mn><mo>&#160;</mo></mrow><mn>126</mn></mfrac><mo>=</mo><mo>&#8377;</mo><mn>10</mn><mo>,</mo><mn>000</mn></math><br><span style=\"font-family: Times New Roman;\">CP of second watch = </span><span style=\"font-family: Times New Roman;\"> </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>12600</mn><mo>&#215;</mo><mn>100</mn></mrow><mn>90</mn></mfrac><mo>=</mo><mo>&#8377;</mo><mn>14</mn><mo>,</mo><mn>000</mn></math><br><span style=\"font-family: Times New Roman;\">Total CP = 10000 + 14000 = </span><span style=\"font-family: Times New Roman;\">₹</span><span style=\"font-family: Times New Roman;\">24,000</span><br><span style=\"font-family: Times New Roman;\">Total SP = 12600<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#215;</mo></math></span><span style=\"font-family: Times New Roman;\">2 = </span><span style=\"font-family: Times New Roman;\">₹</span><span style=\"font-family: Times New Roman;\">25,200</span><br><span style=\"font-family: Times New Roman;\">Percentage gain = </span><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Times New Roman;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>25200</mn><mo>-</mo><mn>24000</mn></mrow><mn>24000</mn></mfrac><mo>&#160;</mo><mo>&#215;</mo><mn>100</mn><mo>=</mo><mn>5</mn><mo>%</mo></math></span></p>",
                    solution_hi: "<p>8.(b) <span style=\"font-family: Baloo;\">पहली घड़ी का क्रय मूल्य = </span><span style=\"font-family: Times New Roman;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>12600</mn><mo>&#215;</mo><mn>100</mn><mo>&#160;</mo></mrow><mn>126</mn></mfrac><mo>=</mo><mo>&#8377;</mo><mn>10</mn><mo>,</mo><mn>000</mn></math></span><br><span style=\"font-family: Baloo;\">दूसरी घड़ी का क्रय मूल्य = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>12600</mn><mo>&#215;</mo><mn>100</mn></mrow><mn>90</mn></mfrac><mo>&#160;</mo><mo>=</mo><mo>&#8377;</mo><mn>14</mn><mo>,</mo><mn>000</mn></math></span><br><span style=\"font-family: Baloo;\">कुल लागत मूल्य = 10000 + 14000 = ₹24,000</span><br><span style=\"font-family: Baloo;\">कुल बिक्री मूल्य = 12600 <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#215;</mo></math></span><span style=\"font-family: Times New Roman;\">2 = ₹25,200</span><br><span style=\"font-family: Baloo;\">प्रतिशत लाभ =&nbsp; </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>25200</mn><mo>-</mo><mn>24000</mn></mrow><mn>24000</mn></mfrac><mo>&#215;</mo><mn>100</mn><mo>=</mo><mn>5</mn><mo>%</mo></math></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "17",
                    question_en: "<p>9.<span style=\"font-family: Times New Roman;\"> A tea seller used to make 50% of profit by selling tea at ₹9 per cup. When the cost of ingredients increased by 25%, he started selling tea at ₹10 per cup. What is his profit percentage now?</span></p>",
                    question_hi: "<p>9.<span style=\"font-family: Baloo;\"> एक चाय विक्रेता प्रति कप ₹ 9 चाय बेचकर लाभ का 50% कमाता था। जब सामग्री की लागत में 25% की वृद्धि हुई, तो उन्होंने प्रति कप ₹ 10 पर चाय बेचना शुरू कर दिया। अब उसका लाभ प्रतिशत क्या है?</span></p>",
                    options_en: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>33</mn><mfrac><mrow><mn>2</mn><mo>&#160;</mo></mrow><mn>3</mn></mfrac></math><span style=\"font-family: Times New Roman;\">%</span></p>", "<p>25 <span style=\"font-family: Times New Roman;\">%</span></p>", 
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>33</mn><mfrac><mn>1</mn><mn>3</mn></mfrac><mo>%</mo></math></p>", "<p>30 <span style=\"font-family: Times New Roman;\">%</span></p>"],
                    options_hi: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>33</mn><mfrac><mrow><mn>2</mn><mo>&#160;</mo></mrow><mn>3</mn></mfrac></math><span style=\"font-family: Times New Roman;\">%</span></p>", "<p>25<span style=\"font-family: Times New Roman;\">%</span></p>",
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>33</mn><mfrac><mn>1</mn><mn>3</mn></mfrac><mo>%</mo></math></p>", "<p>30<span style=\"font-family: Times New Roman;\">%</span></p>"],
                    solution_en: "<p>9.(c) <span style=\"font-family: Times New Roman;\">Here, </span><span style=\"font-family: Times New Roman;\">₹ 9 corresponds to 150%,</span><br><span style=\"font-family: Times New Roman;\">so CP = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>9</mn><mo>&#215;</mo><mn>100</mn></mrow><mn>150</mn></mfrac></math></span><span style=\"font-family: Times New Roman;\"> = </span><span style=\"font-family: Times New Roman;\">₹6</span><br><span style=\"font-family: Times New Roman;\">New CP = 6 <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#215;</mo><mfrac><mn>125</mn><mn>100</mn></mfrac></math></span><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Times New Roman;\">&nbsp;= ₹7.5</span><br><span style=\"font-family: Times New Roman;\">Profit percentage =</span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>10</mn><mo>-</mo><mn>7</mn><mo>.</mo><mn>5</mn></mrow><mrow><mn>7</mn><mo>.</mo><mn>5</mn></mrow></mfrac><mo>&#215;</mo><mn>100</mn><mo>=</mo><mn>33</mn><mfrac><mn>1</mn><mn>3</mn></mfrac><mo>%</mo></math></p>",
                    solution_hi: "<p>9.(c) <span style=\"font-family: Baloo;\">यहाँ, </span><span style=\"font-family: Baloo;\">₹ 9, 150% को संदर्भित करता है,</span><br><span style=\"font-family: Baloo;\">इसलिए, लागत मूल्य = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>9</mn><mo>&#215;</mo><mn>100</mn></mrow><mn>150</mn></mfrac></math></span><span style=\"font-family: Times New Roman;\"> = </span><span style=\"font-family: Times New Roman;\">₹6</span><br><span style=\"font-family: Baloo;\">नया लागत मूल्य = 6 <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#215;</mo><mfrac><mn>125</mn><mn>100</mn></mfrac></math></span><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Times New Roman;\">&nbsp;= ₹7.5</span><br><span style=\"font-family: Baloo;\">लाभ प्रतिशत = </span><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Times New Roman;\"> </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>10</mn><mo>-</mo><mn>7</mn><mo>.</mo><mn>5</mn></mrow><mrow><mn>7</mn><mo>.</mo><mn>5</mn></mrow></mfrac><mo>&#215;</mo><mn>100</mn><mo>=</mo><mn>33</mn><mfrac><mn>1</mn><mn>3</mn></mfrac><mo>%</mo></math></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "17",
                    question_en: "<p>10. <span style=\"font-family: Times New Roman;\">A man bought toffees at 3 for a rupee. How many toffees for a rupee must he sell to gain 50%?</span></p>",
                    question_hi: "<p>10. <span style=\"font-family: Baloo;\">एक आदमी ने एक रुपये में 3 में टॉफी खरीदी। 50% लाभ प्राप्त करने के लिए उसे एक रुपये के लिए कितनी टॉफियां बेचनी चाहिए?</span></p>",
                    options_en: ["<p>4</p>", "<p>1</p>", 
                                "<p>3</p>", "<p>2</p>"],
                    options_hi: ["<p>4</p>", "<p>1</p>",
                                "<p>3</p>", "<p>2</p>"],
                    solution_en: "<p>10.(d) <span style=\"font-family: Times New Roman;\">Formula :</span></p>\n<p><span style=\"font-family: Times New Roman;\"> </span></p>\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>P</mi><mi>r</mi><mi>i</mi><mi>c</mi><mi>e</mi></mrow><mrow><mi>i</mi><mi>t</mi><mi>e</mi><mi>m</mi><mi>s</mi><mo>&#160;</mo><mi>b</mi><mi>o</mi><mi>u</mi><mi>g</mi><mi>h</mi><mi>t</mi><mo>&#215;</mo><mn>100</mn></mrow></mfrac><mo>=</mo><mfrac><mrow><mi>P</mi><mi>r</mi><mi>i</mi><mi>c</mi><mi>e</mi></mrow><mrow><mi>i</mi><mi>t</mi><mi>e</mi><mi>m</mi><mi>s</mi><mo>&#160;</mo><mi>s</mi><mi>o</mi><mi>l</mi><mi>d</mi><mo>&#215;</mo><mo>(</mo><mn>100</mn><mo>&#177;</mo><mi>P</mi><mo>/</mo><mi>L</mi><mo>)</mo></mrow></mfrac></math></p>\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><mn>3</mn><mo>&#215;</mo><mn>100</mn></mrow></mfrac><mo>=</mo><mfrac><mn>1</mn><mrow><mi>n</mi><mo>&#215;</mo><mn>150</mn></mrow></mfrac><mo>&#160;</mo></math></p>\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo><mo>&#160;</mo><mi>n</mi><mo>=</mo><mfrac><mn>300</mn><mrow><mn>150</mn><mo>&#160;</mo></mrow></mfrac><mo>=</mo><mn>2</mn></math></p>",
                    solution_hi: "<p>10.(d) <span style=\"font-family: Baloo;\">सूत्र :</span></p>\n<p><span style=\"font-family: Times New Roman;\"> </span></p>\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>P</mi><mi>r</mi><mi>i</mi><mi>c</mi><mi>e</mi></mrow><mrow><mi>i</mi><mi>t</mi><mi>e</mi><mi>m</mi><mi>s</mi><mo>&#160;</mo><mi>b</mi><mi>o</mi><mi>u</mi><mi>g</mi><mi>h</mi><mi>t</mi><mo>&#215;</mo><mn>100</mn></mrow></mfrac><mo>=</mo><mfrac><mrow><mi>P</mi><mi>r</mi><mi>i</mi><mi>c</mi><mi>e</mi></mrow><mrow><mi>i</mi><mi>t</mi><mi>e</mi><mi>m</mi><mi>s</mi><mo>&#160;</mo><mi>s</mi><mi>o</mi><mi>l</mi><mi>d</mi><mo>&#215;</mo><mo>(</mo><mn>100</mn><mo>&#177;</mo><mi>P</mi><mo>/</mo><mi>L</mi><mo>)</mo></mrow></mfrac></math></p>\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#160;</mo><mfrac><mn>1</mn><mrow><mn>3</mn><mo>&#215;</mo><mn>100</mn></mrow></mfrac><mo>=</mo><mfrac><mn>1</mn><mrow><mi>n</mi><mo>&#215;</mo><mn>150</mn></mrow></mfrac><mo>&#160;</mo></math></p>\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo><mi>n</mi><mo>=</mo><mfrac><mn>300</mn><mrow><mn>150</mn><mo>&#160;</mo></mrow></mfrac><mo>=</mo><mn>2</mn></math></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "17",
                    question_en: "<p>11.<span style=\"font-family: Times New Roman;\"> A shopkeeper marks an article at a price 20% higher than its cost price and allows 10% discount. Find his gain percentage.</span></p>",
                    question_hi: "<p>11.<span style=\"font-family: Baloo;\"> एक दुकानदार एक वस्तु को उसके क्रय मूल्य से 20% अधिक मूल्य पर अंकित करता है और 10% की छूट देता है। उसका लाभ प्रतिशत ज्ञात कीजिए।</span></p>",
                    options_en: ["<p>9.5%</p>", "<p>8%</p>", 
                                "<p>9%</p>", "<p>10%</p>"],
                    options_hi: ["<p>9.5%</p>", "<p>8%</p>",
                                "<p>9%</p>", "<p>10%</p>"],
                    solution_en: "<p>11.(b) <span style=\"font-family: Times New Roman;\">Let CP be 100 units, then MP = 120 units.</span><br><span style=\"font-family: Times New Roman;\">SP = 120&times;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>90</mn><mn>100</mn></mfrac></math></span><span style=\"font-family: Times New Roman;\"> = 108</span><br><span style=\"font-family: Times New Roman;\">Clearly, profit = 8%.</span></p>",
                    solution_hi: "<p>11.(b) <span style=\"font-family: Baloo;\">माना लागत मूल्य 100 इकाई है, फिर अंकित मूल्य = 120 इकाई</span><br><span style=\"font-family: Baloo;\">विक्रय मूल्य = 120&times;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>90</mn><mn>100</mn></mfrac></math></span><span style=\"font-family: Times New Roman;\"> = 108</span><br><span style=\"font-family: Baloo;\">स्पष्ट रूप से, लाभ = 8%.</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "17",
                    question_en: "<p>12.<span style=\"font-family: Times New Roman;\"> A shopkeeper bought 40 pieces of an article at a rate of ₹50 per item. He sold 35 pieces with 20% profit. The remaining 5 Pieces were found to be damaged and he sold them with 10% loss. Find his overall profit percentage.</span></p>",
                    question_hi: "<p>12. <span style=\"font-family: Baloo;\">एक दुकानदार ने एक वस्तु के 40 टुकड़े ₹50 प्रति वस्तु की दर से खरीदे। उसने 35 टुकड़े 20% लाभ पर बेचे। शेष 5 टुकड़े क्षतिग्रस्त पाए गए और उसने उन्हें 10% हानि के साथ बेच दिया। उसका कुल लाभ प्रतिशत ज्ञात कीजिए।</span></p>",
                    options_en: ["<p>30%</p>", "<p>32.5%</p>", 
                                "<p>16.25%</p>", "<p>105%</p>"],
                    options_hi: ["<p>30%</p>", "<p>32.5%</p>",
                                "<p>16.25%</p>", "<p>105%</p>"],
                    solution_en: "<p>12.(c) <span style=\"font-family: Times New Roman;\">Total CP = 40<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#215;</mo></math></span><span style=\"font-family: Times New Roman;\">50 = </span><span style=\"font-family: Times New Roman;\">₹</span><span style=\"font-family: Times New Roman;\">2,000</span><br><span style=\"font-family: Times New Roman;\">SP of 35 pieces at 20% profit = </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>35</mn><mo>&#215;</mo><mo>(</mo><mn>50</mn><mo>&#215;</mo><mfrac><mn>120</mn><mn>100</mn></mfrac><mo>)</mo><mo>=</mo><mn>35</mn><mo>&#215;</mo><mn>60</mn><mo>=</mo><mo>&#8377;</mo><mn>2</mn><mo>,</mo><mn>100</mn><mo>&#160;</mo><mo>&#160;</mo><mo>&#160;</mo></math><br><span style=\"font-family: Times New Roman;\">SP of 5 pieces at 10% loss = </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>=</mo><mo>&#160;</mo><mn>5</mn><mo>&#215;</mo><mo>(</mo><mn>50</mn><mo>&#215;</mo><mfrac><mrow><mo>&#160;</mo><mn>90</mn></mrow><mn>100</mn></mfrac><mo>)</mo><mo>=</mo><mn>5</mn><mo>&#215;</mo><mn>45</mn><mo>=</mo><mo>&#8377;</mo><mn>225</mn><mo>&#160;</mo></math><br><span style=\"font-family: Times New Roman;\">Total SP = 2100 + 225 = ₹2,325</span><br><span style=\"font-family: Times New Roman;\">Overall profit percentage = </span><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Times New Roman;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2325</mn><mo>-</mo><mn>2000</mn></mrow><mn>2000</mn></mfrac><mo>&#215;</mo><mn>100</mn><mo>=</mo><mn>16</mn><mo>.</mo><mn>25</mn><mo>%</mo></math></span></p>",
                    solution_hi: "<p>12.(c) <span style=\"font-family: Baloo;\">कुल लागत मूल्य = 40<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#215;</mo></math></span><span style=\"font-family: Times New Roman;\">50 = ₹2,000</span><br><span style=\"font-family: Baloo;\">20% लाभ पर 35 टुकड़ों का विक्रय मूल्य =</span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>35</mn><mo>&#215;</mo><mo>(</mo><mn>50</mn><mo>&#215;</mo><mfrac><mn>120</mn><mn>100</mn></mfrac><mo>)</mo><mo>=</mo><mn>35</mn><mo>&#215;</mo><mn>60</mn><mo>=</mo><mo>&#8377;</mo><mn>2</mn><mo>,</mo><mn>100</mn><mo>&#160;</mo><mo>&#160;</mo><mo>&#160;</mo></math><br><span style=\"font-family: Baloo;\">10% हानि पर 5 टुकड़ों का विक्रय मूल्य = </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>=</mo><mn>5</mn><mo>&#215;</mo><mo>(</mo><mn>50</mn><mo>&#215;</mo><mfrac><mrow><mo>&#160;</mo><mn>90</mn></mrow><mn>100</mn></mfrac><mo>)</mo><mo>=</mo><mn>5</mn><mo>&#215;</mo><mn>45</mn><mo>=</mo><mo>&#8377;</mo><mn>225</mn><mo>&#160;</mo></math><br><span style=\"font-family: Baloo;\">कुल बिक्री मूल्य = 2100 + 225 = ₹2,325</span><br><span style=\"font-family: Baloo;\">कुल लाभ प्रतिशत =</span><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Times New Roman;\"> </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2325</mn><mo>-</mo><mn>2000</mn></mrow><mn>2000</mn></mfrac><mo>&#215;</mo><mn>100</mn><mo>=</mo><mn>16</mn><mo>.</mo><mn>25</mn><mo>%</mo></math></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "17",
                    question_en: "<p>13.<span style=\"font-family: Times New Roman;\"> A shopkeeper bought a table for Rs. 4,600 and a chair for Rs. 1,800. He sells the table with 10% gain and the chair with 6% gain. Find the overall gain percentage.</span></p>",
                    question_hi: "<p>13.<span style=\"font-family: Baloo;\"> एक दुकानदार ने एक टेबल 4,600 रुपये और 1,800 रूपये एक कुर्सी खरीदी। वह मेज को 10% लाभ और कुर्सी को 6% लाभ पर बेचता है। कुल लाभ प्रतिशत ज्ञात कीजिए।</span></p>",
                    options_en: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#160;</mo><mn>7</mn><mfrac><mn>3</mn><mn>4</mn></mfrac><mo> </mo><mo>%</mo></math></p>", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>8</mn><mfrac><mn>7</mn><mn>8</mn></mfrac><mo>%</mo></math></p>", 
                                "<p>8 %</p>", "<p>16 %</p>"],
                    options_hi: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#160;</mo><mn>7</mn><mfrac><mn>3</mn><mn>4</mn></mfrac><mo>%</mo></math></p>", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>8</mn><mfrac><mn>7</mn><mn>8</mn></mfrac><mo>%</mo></math></p>",
                                "<p>8%</p>", "<p>16%</p>"],
                    solution_en: "<p>13.(b) <span style=\"font-family: Times New Roman;\">Profit on table = </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>4600</mn><mo>&#215;</mo><mfrac><mrow><mn>10</mn><mo>&#160;</mo></mrow><mn>100</mn></mfrac><mo>=</mo><mo>&#8377;</mo><mn>460</mn></math><br><span style=\"font-family: Times New Roman;\">Profit on chair = </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>1800</mn><mo>&#160;</mo><mo>&#215;</mo><mfrac><mn>6</mn><mn>100</mn></mfrac><mo>=</mo><mo>&#8377;</mo><mn>108</mn></math><br><span style=\"font-family: Times New Roman;\">Overall gain percentage = </span><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Times New Roman;\"> </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>460</mn><mo>+</mo><mn>108</mn></mrow><mrow><mn>4600</mn><mo>+</mo><mn>1800</mn><mo>&#160;</mo></mrow></mfrac><mo>&#215;</mo><mn>100</mn><mo>=</mo><mfrac><mn>568</mn><mrow><mo>&#160;</mo><mn>6400</mn><mo>&#160;</mo></mrow></mfrac><mo>&#215;</mo><mn>100</mn><mo>=</mo><mn>8</mn><mfrac><mn>7</mn><mn>8</mn></mfrac><mo>%</mo></math></p>",
                    solution_hi: "<p>13.(b) <span style=\"font-family: Baloo;\">मेज पर लाभ</span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>=</mo><mn>4600</mn><mo>&#160;</mo><mo>&#215;</mo><mfrac><mrow><mn>10</mn><mo>&#160;</mo></mrow><mn>100</mn></mfrac><mo>=</mo><mo>&#8377;</mo><mn>460</mn></math><br><span style=\"font-family: Baloo;\">कुर्सी पर लाभ = </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>1800</mn><mo>&#160;</mo><mo>&#215;</mo><mfrac><mn>6</mn><mn>100</mn></mfrac><mo>=</mo><mo>&#8377;</mo><mn>108</mn></math><br><span style=\"font-family: Baloo;\">कुल लाभ प्रतिशत = </span><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Times New Roman;\"> </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>460</mn><mo>+</mo><mn>108</mn></mrow><mrow><mn>4600</mn><mo>+</mo><mn>1800</mn><mo>&#160;</mo></mrow></mfrac><mo>&#215;</mo><mn>100</mn><mo>&#160;</mo><mo>=</mo><mfrac><mn>568</mn><mrow><mo>&#160;</mo><mn>6400</mn><mo>&#160;</mo></mrow></mfrac><mo>&#215;</mo><mn>100</mn><mo>=</mo><mn>8</mn><mfrac><mn>7</mn><mn>8</mn></mfrac><mo>%</mo></math></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "17",
                    question_en: "<p>14. <span style=\"font-family: Times New Roman;\">&nbsp;The ratio of profit of P and Q is 5:8. What is their investment ratio , if their investment time period ratio is 3:5?</span></p>",
                    question_hi: "<p>14. <span style=\"font-family: Baloo;\">&nbsp;P और Q के लाभ का अनुपात 5:8 है। उनका निवेश अनुपात क्या है, यदि उनका निवेश समय अवधि अनुपात 3:5 है?</span></p>",
                    options_en: ["<p>13:25</p>", "<p>12:25</p>", 
                                "<p>24:25</p>", "<p>25:24</p>"],
                    options_hi: ["<p>13:25</p>", "<p>12:25</p>",
                                "<p>24:25</p>", "<p>25:24</p>"],
                    solution_en: "<p>14.(d) <span style=\"font-family: Times New Roman;\">Let the investment by P and Q be &ldquo;a&rdquo; and &ldquo;b&rdquo;</span><br><span style=\"font-family: Times New Roman;\">Profit = Investment &times; Time period</span><br><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>3</mn><mo>&#215;</mo><mi>a</mi></mrow><mrow><mn>5</mn><mo>&#215;</mo><mi>b</mi></mrow></mfrac><mo>=</mo><mfrac><mrow><mn>5</mn><mo>&#160;</mo></mrow><mn>8</mn></mfrac></math></span><br><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>a</mi><mo>&#160;</mo><mo>:</mo><mo>&#160;</mo><mi>b</mi><mo>=</mo><mn>5</mn><mo>&#215;</mo><mn>5</mn><mo>&#160;</mo><mo>:</mo><mo>&#160;</mo><mn>3</mn><mo>&#215;</mo><mn>8</mn><mo>=</mo><mn>25</mn><mo>&#160;</mo><mo>:</mo><mo>&#160;</mo><mn>24</mn><mo>.</mo></math></span></p>",
                    solution_hi: "<p>14.(d) <span style=\"font-family: Baloo;\">माना P और Q द्वारा किया गया निवेश \"a\" और \"b\" है</span><br><span style=\"font-family: Baloo;\">लाभ = निवेश &times; समय अवधि</span><br><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>3</mn><mo>&#215;</mo><mi>a</mi></mrow><mrow><mn>5</mn><mo>&#215;</mo><mi>b</mi></mrow></mfrac><mo>=</mo><mfrac><mrow><mn>5</mn><mo>&#160;</mo></mrow><mn>8</mn></mfrac></math></span><br><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>a</mi><mo>&#160;</mo><mo>:</mo><mo>&#160;</mo><mi>b</mi><mo>=</mo><mn>5</mn><mo>&#215;</mo><mn>5</mn><mo>&#160;</mo><mo>:</mo><mo>&#160;</mo><mn>3</mn><mo>&#215;</mo><mn>8</mn><mo>=</mo><mn>25</mn><mo>&#160;</mo><mo>:</mo><mo>&#160;</mo><mn>24</mn><mo>.</mo></math></span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "17",
                    question_en: "<p>15.<span style=\"font-family: Times New Roman;\"> The cost prices of two articles A and B are in the ratio 4:5 while selling these articles, the shopkeeper gains 10% on article A and 20% profit on article B, and the difference in their selling price is ₹480. Find 30% of the total cost price ( in ₹ ) of both the articles.</span></p>",
                    question_hi: "<p>15. <span style=\"font-family: Baloo;\">&nbsp;दो वस्तुओं A और B का क्रय मूल्य 4:5 के अनुपात में है, इन वस्तुओं को बेचते समय, दुकानदार को वस्तु A पर 10% और वस्तु B पर 20% लाभ प्राप्त होता है, और उनके विक्रय मूल्य में अंतर ₹480 है। दोनों वस्तुओं के कुल लागत मूल्य का 30% (₹ में) ज्ञात कीजिए।</span></p>",
                    options_en: ["<p>1,250</p>", "<p>1,000</p>", 
                                "<p>900</p>", "<p>810</p>"],
                    options_hi: ["<p>1,250</p>", "<p>1,000</p>",
                                "<p>900</p>", "<p>810</p>"],
                    solution_en: "<p>15.(d) <span style=\"font-family: Times New Roman;\">Let the cost price of articles A and B be 4 units and 5 units respectively.</span><br><span style=\"font-family: Times New Roman;\">SP of A = 110% of CP of A = 4.4 units.</span><br><span style=\"font-family: Times New Roman;\">SP of B = 120% of CP of B = 6 units.</span><br><span style=\"font-family: Times New Roman;\">According to the question, (6 &ndash; 4.4) units = 1.6 units = ₹480</span><br><span style=\"font-family: Times New Roman;\">1 unit = ₹300</span><br><span style=\"font-family: Times New Roman;\">Sum of CP of both the articles = (4 +5) = 9 units</span><br><span style=\"font-family: Times New Roman;\">30% of 9 units = 30% of 9&times;300 = ₹810</span></p>",
                    solution_hi: "<p>15.(d) <span style=\"font-family: Baloo;\">माना वस्तु A और B का क्रय मूल्य क्रमशः 4 इकाई और 5 इकाई है।</span><br><span style=\"font-family: Baloo;\">A का विक्रय मूल्य = A के लागत मूल्य का 110% = 4.4 इकाई।</span><br><span style=\"font-family: Baloo;\">B का विक्रय मूल्य = B के लागत मूल्य का 120% = 6 इकाई।</span><br><span style=\"font-family: Baloo;\">प्रश्न के अनुसार, (6 &ndash; 4.4) इकाई = 1.6 इकाई = ₹480</span><br><span style=\"font-family: Baloo;\">1 इकाई = ₹300</span><br><span style=\"font-family: Baloo;\">दोनों वस्तुओं के क्रय मूल्य का योग = (4 +5) = 9 इकाई</span><br><span style=\"font-family: Baloo;\">9 इकाइयों का 30% = 9&times;300 का 30% = ₹810</span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "16",
                    section: "17",
                    question_en: "<p>16.<span style=\"font-family: Times New Roman;\"> A shopkeeper offers his customer a discount of 10%. On an item marked at a price of Rs. 400, which was a little damaged, he offered an additional discount of 10%. At what price (in Rs.) is the item available to the customer?</span></p>",
                    question_hi: "<p>16.<span style=\"font-family: Baloo;\"> एक दुकानदार अपने ग्राहक को 10% की छूट देता करता है। ₹400, अंकित मूल्य वाली वस्तु वस्तु पर । जो थोड़ी क्षतिग्रस्त हो गई है , उसने 10% की अतिरिक्त छूट की पेशकश की। ग्राहक को वस्तु किस कीमत पर (₹ में) उपलब्ध है?</span></p>",
                    options_en: ["<p>340</p>", "<p>324</p>", 
                                "<p>320</p>", "<p>300</p>"],
                    options_hi: ["<p>340</p>", "<p>324</p>",
                                "<p>320</p>", "<p>300</p>"],
                    solution_en: "<p>16.(b)<br><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>10</mn><mo>%</mo><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mfrac><mn>1</mn><mn>10</mn></mfrac></math></span><br>MP&nbsp; :&nbsp; SP<br>10&nbsp;&nbsp;&nbsp; :&nbsp;&nbsp;&nbsp; 9<br><u>10&nbsp;&nbsp;&nbsp; :&nbsp;&nbsp; 9</u><br><u>100&nbsp; :&nbsp;&nbsp; 81</u><br><span style=\"font-family: Times New Roman;\">100 units = 400 Rs</span><br><span style=\"font-family: Times New Roman;\">1 unit = 4 Rs</span><br><span style=\"font-family: Times New Roman;\">81 units = 81&times;4 = 324 Rs</span></p>",
                    solution_hi: "<p>16.(b)<span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>10</mn><mo>%</mo><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mfrac><mn>1</mn><mn>10</mn></mfrac></math></span><br><span style=\"font-family: Baloo;\">&nbsp;अंकित मूल्य : विक्रय मूल्य</span><br><span style=\"font-family: Times New Roman;\"> 10&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;9</span><br><span style=\"font-family: Times New Roman;\"> 10&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;9</span><br><span style=\"font-family: Times New Roman;\"> 100&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 81</span><br><span style=\"font-family: Baloo;\">100 इकाई = 400 रुपये</span><br><span style=\"font-family: Baloo;\">1 इकाई = 4 रुपये</span><br><span style=\"font-family: Baloo;\">81 इकाई = 81&times;4 = 324 रुपये</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "17",
                    section: "17",
                    question_en: "<p>17. <span style=\"font-family: Times New Roman;\">&nbsp;The profit earned by selling an article for Rs. 832 is equal to the loss incurred when the article is sold for Rs. 448. What should be the selling price to make a profit of 10%?</span></p>",
                    question_hi: "<p>17. <span style=\"font-family: Baloo;\">&nbsp;किसी वस्तु को ₹832 में बेचने पर अर्जित लाभ। उस वस्तु को ₹448 बेचने पर हुए हानि के बराबर है। 10% का लाभ कमाने के लिए विक्रय मूल्य(₹ में ) क्या होना चाहिए?</span></p>",
                    options_en: ["<p>750</p>", "<p>715</p>", 
                                "<p>640</p>", "<p>704</p>"],
                    options_hi: ["<p>750</p>", "<p>715</p>",
                                "<p>640</p>", "<p>704</p>"],
                    solution_en: "<p>17.(d) <span style=\"font-family: Times New Roman;\">Let CP be 100%</span><br><span style=\"font-family: Times New Roman;\">Now if we sell at x% profit , SP₁ = (100 + x)% = 832 Rs</span><br><span style=\"font-family: Times New Roman;\">If we sell at x% loss , SP₂ = (100 &ndash; x)% = 448 Rs</span><br><span style=\"font-family: Times New Roman;\">now</span><br><span style=\"font-family: Times New Roman;\">SP₁ + SP₂ = (100+x)% + (100 &ndash; x)% = 1280 Rs</span><br><span style=\"font-family: Times New Roman;\">200% = 1280 Rs</span><br><span style=\"font-family: Times New Roman;\">100% (C.P.) = 640 Rs</span><br><span style=\"font-family: Times New Roman;\">If we sell article at 10% profit then</span><br><span style=\"font-family: Times New Roman;\">CP : SP</span><br><span style=\"font-family: Times New Roman;\">10 : 11</span><br><span style=\"font-family: Times New Roman;\">10 units = 640 Rs</span><br><span style=\"font-family: Times New Roman;\">1 units = 64 Rs</span><br><span style=\"font-family: Times New Roman;\">11 units = 704 Rs</span></p>",
                    solution_hi: "<p>17.(d) <span style=\"font-family: Baloo;\">मान लीजिए, लागत मूल्य 100% है</span><br><span style=\"font-family: Baloo;\">अब अगर हम x% लाभ पर बेचते हैं, SP₁</span><span style=\"font-family: Baloo;\"> = (100 + x)% = 832 रुपये</span><br><span style=\"font-family: Baloo;\">यदि हम x% हानि पर बेचते हैं, तो SP₂ </span><span style=\"font-family: Baloo;\">= (100 &ndash; x)% = 448 रुपये</span><br><span style=\"font-family: Baloo;\">अब</span><br><span style=\"font-family: Baloo;\">SP₁ + SP₂ = (100 + x)% (100 &ndash; x)% = 1280 रुपये</span><br><span style=\"font-family: Baloo;\">200% = 1280 रु</span><br><span style=\"font-family: Baloo;\">100% (लागत मूल्य) = 640 रुपये</span><br><span style=\"font-family: Baloo;\">यदि हम वस्तु को 10% लाभ पर बेचते हैं तो</span><br><span style=\"font-family: Baloo;\">लागत मूल्य : विक्रय मूल्य</span><br><span style=\"font-family: Times New Roman;\"> 10&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;11</span><br><span style=\"font-family: Baloo;\">10 इकाई = 640 रुपये</span><br><span style=\"font-family: Baloo;\">1 इकाई = 64 रुपये</span><br><span style=\"font-family: Baloo;\">11 इकाई = 704 रुपये</span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "18",
                    section: "17",
                    question_en: "<p>18.<span style=\"font-family: Times New Roman;\"> An article is sold at a profit of </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>13</mn><mfrac><mn>1</mn><mn>4</mn></mfrac><mo>%</mo><mo>.</mo><mo>&#160;</mo></math><span style=\"font-family: Times New Roman;\"> Had it been sold for Rs. 76.70 more, the profit would Have been </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>16</mn><mfrac><mn>1</mn><mn>5</mn></mfrac><mo>%</mo><mo>.</mo><mo>&#160;</mo></math><span style=\"font-family: Times New Roman;\">50% of the cost price of the article (in Rs.) is:</span></p>",
                    question_hi: "<p>18. <span style=\"font-family: Baloo;\"> एक वस्तु को </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>13</mn><mfrac><mn>1</mn><mn>4</mn></mfrac><mo>%</mo><mo>.</mo><mo>&#160;</mo></math><span style=\"font-family: Baloo;\"> के लाभ पर बेचा जाता है। अगर इसे 76.70 रुपये अधिक में बेचा गया होता, तो </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>16</mn><mfrac><mn>1</mn><mn>5</mn></mfrac><mo>%</mo><mo>.</mo><mo>&#160;</mo></math><span style=\"font-family: Baloo;\">का लाभ होता, वस्तु के क्रय मूल्य का 50% (रुपये में) कितना होगा ?</span></p>",
                    options_en: ["<p>1,250</p>", "<p>2,500</p>", 
                                "<p>1,300</p>", "<p>1,500</p>"],
                    options_hi: ["<p>1,250</p>", "<p>2,500</p>",
                                "<p>1,300</p>", "<p>1,500</p>"],
                    solution_en: "<p>18.(c) <span style=\"font-family: Times New Roman;\">Let CP be 100%</span><br><span style=\"font-family: Times New Roman;\">Difference of profit % = 16.20% &ndash; 13.25% = 2.95%</span><br><span style=\"font-family: Times New Roman;\">2.95% = 76.70 Rs</span><br><span style=\"font-family: Times New Roman;\">1% = 26 Rs</span><br><span style=\"font-family: Times New Roman;\">CP = 100% = 2600 Rs</span><br><span style=\"font-family: Times New Roman;\">50% of CP = </span><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac><mo>&#215;</mo><mn>2600</mn><mo>&#160;</mo></math> = 1300 Rs</span></p>",
                    solution_hi: "<p>18.(c) <span style=\"font-family: Baloo;\">मान लीजिए लागत मूल्य 100% है</span><br><span style=\"font-family: Baloo;\">लाभ का अंतर % = 16.20% &ndash; 13.25% = 2.95%</span><br><span style=\"font-family: Baloo;\">2.95% = 76.70 रुपये</span><br><span style=\"font-family: Baloo;\">1% = 26 रुपये</span><br><span style=\"font-family: Baloo;\">लागत मूल्य = 100% = 2600 रुपये</span><br><span style=\"font-family: Baloo;\">लागत मूल्य का 50% = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac><mo>&#215;</mo><mn>2600</mn><mo>&#160;</mo></math> = 1300 रुपये</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "19",
                    section: "17",
                    question_en: "<p>19. <span style=\"font-family: Times New Roman;\">A shopkeeper bought 60 pencils at a rate of 4 for 5₹ and another 60 pencils at a rate of 2 for ₹3 . He mixed the pencils and sold them at a rate of 3 for ₹4. FInd his gain or loss&nbsp; percentage. </span></p>",
                    question_hi: "<p>19.<span style=\"font-family: Baloo;\"> एक दुकानदार ने 60 पेंसिलें खरीदीं 5₹ में 4 की दर से और अन्य 60 पेंसिलें ₹3 में 2 की दर से खरीदीं। उसने पेंसिलों को मिलाया और उन्हें ₹4 में 3 की दर से बेच दिया। उसका लाभ या हानि प्रतिशत ज्ञात कीजिए।</span></p>",
                    options_en: ["<p>Profit <span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>3</mn><mo>&#160;</mo><mfrac><mn>1</mn><mn>8</mn></mfrac><mo>%</mo></math></span></p>", "<p>Loss <span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>3</mn><mo>&#160;</mo><mfrac><mn>1</mn><mn>33</mn></mfrac><mo>%</mo></math></span></p>", 
                                "<p>Profit <span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>2</mn><mfrac><mrow><mo>&#160;</mo><mn>7</mn></mrow><mn>8</mn></mfrac><mo>%</mo></math></span></p>", "<p>Loss <span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>2</mn><mo>&#160;</mo><mfrac><mn>7</mn><mn>8</mn></mfrac><mo>%</mo></math></span></p>"],
                    options_hi: ["<p><span style=\"font-family: Baloo;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>3</mn><mo>&#160;</mo><mfrac><mn>1</mn><mn>8</mn></mfrac><mo>%</mo></math> लाभ </span></p>", "<p><span style=\"font-family: Baloo;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>3</mn><mo>&#160;</mo><mfrac><mn>1</mn><mn>33</mn></mfrac><mo>%</mo></math>हानि </span></p>",
                                "<p><span style=\"font-family: Baloo;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>2</mn><mfrac><mrow><mo>&#160;</mo><mn>7</mn></mrow><mn>8</mn></mfrac><mo>%</mo></math>लाभ </span></p>", "<p><span style=\"font-family: Baloo;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>2</mn><mo>&#160;</mo><mfrac><mn>7</mn><mn>8</mn></mfrac><mo>%</mo></math>हानि </span></p>"],
                    solution_en: "<p>19.(b) <span style=\"font-family: Times New Roman;\">CP of 1st 60 pencils </span><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>=</mo><mfrac><mn>5</mn><mn>4</mn></mfrac><mo>&#215;</mo><mn>60</mn><mo>&#160;</mo></math>= 75 Rs</span><br><span style=\"font-family: Times New Roman;\">CP of 2nd 60 pencils = </span><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>2</mn></mfrac><mo>&#215;</mo><mn>60</mn><mo>&#160;</mo></math>= 90 Rs</span><br><span style=\"font-family: Times New Roman;\">Total CP of 120 pencils = 165 Rs</span><br><span style=\"font-family: Times New Roman;\">SP of 120 pencils = </span><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>3</mn></mfrac><mo>&#215;</mo><mn>120</mn><mo>&#160;</mo></math> = 160 Rs</span><br><span style=\"font-family: Times New Roman;\">Loss = CP &ndash; SP = 165 &ndash; 160 = 5 Rs</span><br><span style=\"font-family: Times New Roman;\">Loss percent </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>=</mo><mfrac><mrow><mo>&#160;</mo><mi>l</mi><mi>o</mi><mi>s</mi><mi>s</mi></mrow><mrow><mi>C</mi><mi>P</mi></mrow></mfrac><mo>&#215;</mo><mn>100</mn><mo>&#160;</mo><mo>&#8658;</mo><mfrac><mrow><mo>&#160;</mo><mn>5</mn></mrow><mn>165</mn></mfrac><mo>&#215;</mo><mn>100</mn><mo>=</mo><mn>3</mn><mfrac><mn>1</mn><mn>33</mn></mfrac><mo>%</mo></math></p>",
                    solution_hi: "<p>19.(b) <span style=\"font-family: Baloo;\">पहली 60 पेंसिल का क्रय मूल्य </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>=</mo><mfrac><mn>5</mn><mn>4</mn></mfrac><mo>&#215;</mo><mn>60</mn><mo>&#160;</mo></math><span style=\"font-family: Baloo;\">= 75 रुपये</span><br><span style=\"font-family: Baloo;\">दूसरी 60 पेंसिल का क्रय मूल्य =</span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>2</mn></mfrac><mo>&#215;</mo><mn>60</mn><mo>&#160;</mo></math><span style=\"font-family: Baloo;\"> = 90 रुपये</span><br><span style=\"font-family: Baloo;\">120 पेंसिलों का कुल क्रय मूल्य = 165 रु</span><br><span style=\"font-family: Baloo;\">120 पेंसिलों का विक्रय मूल्य =</span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>3</mn></mfrac><mo>&#215;</mo><mn>120</mn><mo>&#160;</mo></math><span style=\"font-family: Baloo;\">= 160 रुपये</span><br><span style=\"font-family: Baloo;\">हानि = लागत मूल्य &ndash; विक्रय मूल्य = 165 &ndash; 160 = 5 Rs</span><br><span style=\"font-family: Baloo;\">हानि प्रतिशत&nbsp; </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>=</mo><mfrac><mrow><mo>&#160;</mo><mi>l</mi><mi>o</mi><mi>s</mi><mi>s</mi></mrow><mrow><mi>C</mi><mi>P</mi></mrow></mfrac><mo>&#215;</mo><mn>100</mn><mo>&#160;</mo><mo>&#8658;</mo><mfrac><mrow><mo>&#160;</mo><mn>5</mn></mrow><mn>165</mn></mfrac><mo>&#215;</mo><mn>100</mn><mo>=</mo><mn>3</mn><mfrac><mn>1</mn><mn>33</mn></mfrac><mo>%</mo></math></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "20",
                    section: "17",
                    question_en: "<p>20. <span style=\"font-family: Baloo;\">Manjeet bought a second hand motorbike for र22,000 and spent र3,000 on its overhauling and maintenance . He then sold it with 12% profit.If he had sold it for र500 less, then what would have been his profit percentage ?</span></p>",
                    question_hi: "<p>20. <span style=\"font-family: Baloo;\">मंजीत ने 22,000 रुपये में एक सेकेंड हैंड मोटरसाइकिल खरीदी और इसके ओवरहालिंग और रखरखाव पर 3,000 रुपये खर्च किए। फिर उसने इसे 12% लाभ पर बेच दिया। यदि उसने इसे 500 रुपये कम पर बेचा होता, तो उसका लाभ प्रतिशत क्या होता?</span></p>",
                    options_en: ["<p>10.5%</p>", "<p>10%</p>", 
                                "<p>5%</p>", "<p>8%</p>"],
                    options_hi: ["<p>10.5%</p>", "<p>10%</p>",
                                "<p>5%</p>", "<p>8%</p>"],
                    solution_en: "<p>20.(b) <span style=\"font-family: Times New Roman;\">CP for manjeet = 22000 Rs + 3000 Rs = 25000 Rs</span><br><span style=\"font-family: Times New Roman;\">Now</span><br><span style=\"font-family: Times New Roman;\">If profit is 12% then</span><br><span style=\"font-family: Times New Roman;\">CP : SP</span><br><span style=\"font-family: Times New Roman;\">100: 112</span><br><span style=\"font-family: Times New Roman;\">25 : 28</span><br><span style=\"font-family: Times New Roman;\">CP = 25 units = 25000 Rs</span><br><span style=\"font-family: Times New Roman;\">So SP = 28 units = 28000 Rs</span><br><span style=\"font-family: Times New Roman;\">Profit = SP - CP = 28000 Rs - 25000 Rs = 3000 Rs</span><br><span style=\"font-family: Times New Roman;\">Now if profit is 500 Rs less than</span><br><span style=\"font-family: Times New Roman;\">New profit = 3000 Rs- 500 Rs = 2500 Rs</span><br><span style=\"font-family: Times New Roman;\">New profit percent </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>=</mo><mfrac><mrow><mi>p</mi><mi>r</mi><mi>o</mi><mi>f</mi><mi>i</mi><mi>t</mi></mrow><mrow><mi>C</mi><mi>P</mi></mrow></mfrac><mo>&#215;</mo><mn>100</mn><mo>&#160;</mo><mo>=</mo><mfrac><mrow><mo>&#160;</mo><mn>2500</mn></mrow><mn>25000</mn></mfrac><mo>&#215;</mo><mn>100</mn><mo>=</mo><mn>10</mn><mo>%</mo></math></p>",
                    solution_hi: "<p>20.(b) <span style=\"font-family: Baloo;\">मंजीत का क्रय मूल्य = 22000 रुपये&nbsp; + 3000 रुपये = 25000 रुपये</span><br><span style=\"font-family: Baloo;\">अभी,</span><br><span style=\"font-family: Baloo;\">यदि लाभ 12% है, तो</span><br><span style=\"font-family: Baloo;\">लागत मूल्य : विक्रय मूल्य</span><br><span style=\"font-family: Times New Roman;\"> 100&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp; &nbsp; 112</span><br><span style=\"font-family: Times New Roman;\"> 25&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 28</span><br><span style=\"font-family: Baloo;\">लागत मूल्य = 25 इकाई = 25000 रु</span><br><span style=\"font-family: Baloo;\">अत: विक्रय मूल्य = 28 इकाई = 28000 रु</span><br><span style=\"font-family: Baloo;\">लाभ = विक्रय मूल्य - लागत मूल्य = 28000 रुपये - 25000 रुपये = 3000 रुपये</span><br><span style=\"font-family: Baloo;\">अब, अगर लाभ 500 रुपये से कम है</span><br><span style=\"font-family: Baloo;\">नया लाभ = 3000 रुपये- 500 रुपये = 2500 रुपये</span><br><span style=\"font-family: Baloo;\">नया लाभ प्रतिशत =</span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>=</mo><mfrac><mrow><mi>p</mi><mi>r</mi><mi>o</mi><mi>f</mi><mi>i</mi><mi>t</mi></mrow><mrow><mi>C</mi><mi>P</mi></mrow></mfrac><mo>&#215;</mo><mn>100</mn><mo>=</mo><mfrac><mrow><mo>&#160;</mo><mn>2500</mn></mrow><mn>25000</mn></mfrac><mo>&#215;</mo><mn>100</mn><mo>=</mo><mn>10</mn><mo>%</mo></math></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "21",
                    section: "17",
                    question_en: "<p>21.<span style=\"font-family: Times New Roman;\"> A fruit vendor recovers that cost of 95 oranges by selling 80 oranges. What is the profit percentage ?</span></p>",
                    question_hi: "<p>21.<span style=\"font-family: Baloo;\"> एक फल विक्रेता ने 80 संतरे बेचकर 95 संतरे का वह मूल्य वसूल किया। लाभ प्रतिशत क्या है?</span></p>",
                    options_en: ["<p>18.75%</p>", "<p>20.75%</p>", 
                                "<p>21.25%</p>", "<p>24.25%</p>"],
                    options_hi: ["<p>18.75%</p>", "<p>20.75%</p>",
                                "<p>21.25%</p>", "<p>24.25%</p>"],
                    solution_en: "<p>21.(a) <span style=\"font-family: Times New Roman;\">According to the question</span><br><span style=\"font-family: Times New Roman;\">CP of 95 oranges = SP of 80 oranges</span><br><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>C</mi><mi>P</mi><mo>&#160;</mo></mrow><mrow><mi>S</mi><mi>P</mi></mrow></mfrac><mo>=</mo><mfrac><mrow><mo>&#160;</mo><mn>80</mn><mo>&#160;</mo></mrow><mn>95</mn></mfrac><mo>=</mo><mfrac><mn>16</mn><mn>19</mn></mfrac></math></span><br><span style=\"font-family: Times New Roman;\">profit% </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>=</mo><mfrac><mn>3</mn><mn>16</mn></mfrac><mo>&#215;</mo><mn>100</mn><mo>=</mo><mn>18</mn><mo>.</mo><mn>75</mn><mo>%</mo></math></p>",
                    solution_hi: "<p>21.(a) <span style=\"font-family: Baloo;\">प्रश्न के अनुसार,</span><br><span style=\"font-family: Baloo;\">95 संतरों का क्रय मूल्य = 80 संतरों का विक्रय मूल्य</span><br><span style=\"font-family: Baloo;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>C</mi><mi>P</mi><mo>&#160;</mo></mrow><mrow><mi>S</mi><mi>P</mi></mrow></mfrac><mo>=</mo><mfrac><mrow><mo>&#160;</mo><mn>80</mn><mo>&#160;</mo></mrow><mn>95</mn></mfrac><mo>=</mo><mfrac><mn>16</mn><mn>19</mn></mfrac></math></span><br><span style=\"font-family: Baloo;\">लाभ प्रतिशत </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>=</mo><mfrac><mn>3</mn><mn>16</mn></mfrac><mo>&#215;</mo><mn>100</mn><mo>=</mo><mn>18</mn><mo>.</mo><mn>75</mn><mo>%</mo></math></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "22",
                    section: "17",
                    question_en: "<p>22. <span style=\"font-family: Times New Roman;\">An article is sold at a certain price. If it is sold at 70% of this price, then there is a loss of 10%. What is the percentage profit ,when it is sold at the original selling price ?</span></p>",
                    question_hi: "<p>22. <span style=\"font-family: Baloo;\">एक वस्तु एक निश्चित कीमत पर बेची जाती है। यदि इसे इसके मूल्य के 70% पर बेचा जाता है, तो 10% की हानि होती है। यदि इसे मूल विक्रय मूल्य पर बेचा जाता है, तो प्रतिशत लाभ ज्ञात करे ?</span></p>",
                    options_en: ["<p><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>50</mn><mn>7</mn></mfrac><mo>%</mo></math></span></p>", "<p><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>200</mn><mn>7</mn></mfrac><mo>%</mo></math></span></p>", 
                                "<p><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>300</mn><mn>7</mn></mfrac><mo>%</mo></math></span></p>", "<p><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>100</mn><mn>7</mn></mfrac><mo>%</mo></math></span></p>"],
                    options_hi: ["<p><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>50</mn><mn>7</mn></mfrac><mo>%</mo></math></span></p>", "<p><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>200</mn><mn>7</mn></mfrac><mo>%</mo></math></span></p>",
                                "<p><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>300</mn><mn>7</mn></mfrac><mo>%</mo></math></span></p>", "<p><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>100</mn><mn>7</mn></mfrac><mo>%</mo></math></span></p>"],
                    solution_en: "<p>22.(b) <span style=\"font-family: Times New Roman;\">Let SP of article is 100x</span><br><span style=\"font-family: Times New Roman;\">Now it is sold in 70% of SP</span><br><span style=\"font-family: Times New Roman;\">New SP = 70x</span><br><span style=\"font-family: Times New Roman;\">Now according to the question</span><br><span style=\"font-family: Times New Roman;\">CP : SP</span><br><span style=\"font-family: Times New Roman;\">10 : 9</span><br><span style=\"font-family: Times New Roman;\">9 units = 70x</span><br><span style=\"font-family: Times New Roman;\">Now CP = 10 units = </span><span style=\"font-family: Times New Roman;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>70</mn><mi>x</mi><mo>&#215;</mo><mn>10</mn></mrow><mrow><mn>9</mn><mo>&#160;</mo></mrow></mfrac><mo>=</mo><mfrac><mrow><mn>700</mn><mi>x</mi></mrow><mn>9</mn></mfrac></math></span><br><span style=\"font-family: Times New Roman;\">If it is Sold at original SP = 100x</span><br><span style=\"font-family: Times New Roman;\">Then profit percent</span><br><span style=\"font-family: Times New Roman;\"> </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>100</mn><mo>&#160;</mo><mi>x</mi><mo>-</mo><mstyle displaystyle=\"true\"><mfrac><mrow><mn>700</mn><mi>x</mi></mrow><mn>9</mn></mfrac></mstyle></mrow><mstyle displaystyle=\"true\"><mfrac><mrow><mn>700</mn><mi>x</mi></mrow><mn>9</mn></mfrac></mstyle></mfrac><mo>&#215;</mo><mn>100</mn><mo>=</mo><mfrac><mrow><mo>&#160;</mo><mn>200</mn></mrow><mn>7</mn></mfrac><mo>%</mo></math><br><strong><span style=\"font-family: Times New Roman;\">Shortcut:</span></strong><br><strong><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>M</mi><mi>P</mi></mrow><mrow><mi>C</mi><mi>P</mi></mrow></mfrac><mo>=</mo><mfrac><mrow><mo>&#160;</mo><mn>100</mn><mo>-</mo><mi>l</mi><mi>o</mi><mi>s</mi><mi>s</mi></mrow><mrow><mn>100</mn><mo>-</mo><mi>d</mi><mi>i</mi><mi>s</mi><mi>c</mi><mi>o</mi><mi>u</mi><mi>n</mi><mi>t</mi></mrow></mfrac><mo>=</mo><mfrac><mrow><mn>100</mn><mo>-</mo><mn>10</mn></mrow><mrow><mn>100</mn><mo>-</mo><mn>30</mn></mrow></mfrac><mo>=</mo><mfrac><mrow><mn>90</mn><mo>&#160;</mo></mrow><mn>70</mn></mfrac><mo>&#160;</mo></math></span></strong><br><span style=\"font-family: Times New Roman;\">If it is sold in MP then profit % </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>=</mo><mfrac><mn>20</mn><mn>70</mn></mfrac><mo>&#215;</mo><mn>100</mn><mo>&#160;</mo><mo>=</mo><mfrac><mn>200</mn><mn>7</mn></mfrac><mo>%</mo></math></p>",
                    solution_hi: "<p>22.(b) <span style=\"font-family: Baloo;\">माना, वस्तु का विक्रय मूल्य 100x है।</span><br><span style=\"font-family: Baloo;\">अब, इसे विक्रय मूल्य के 70% में बेचा जाता है।</span><br><span style=\"font-family: Baloo;\">नया विक्रय मूल्य = 70x</span><br><span style=\"font-family: Baloo;\">अब प्रश्न के अनुसार,</span><br><span style=\"font-family: Baloo;\">लागत मूल्य : विक्रय मूल्य</span><br><span style=\"font-family: Times New Roman;\"> 10&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 9</span><br><span style=\"font-family: Baloo;\">9 इकाई = 70x</span><br><span style=\"font-family: Baloo;\">अब लागत मूल्य = 10 इकाई =</span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>70</mn><mi>x</mi><mo>&#215;</mo><mn>10</mn></mrow><mrow><mn>9</mn><mo>&#160;</mo></mrow></mfrac><mo>=</mo><mfrac><mrow><mn>700</mn><mi>x</mi></mrow><mn>9</mn></mfrac></math><br><span style=\"font-family: Baloo;\">यदि इसे मूल विक्रय मूल्य पर बेचा जाता है = 100x</span><br><span style=\"font-family: Baloo;\">तो, लाभ प्रतिशत </span><span style=\"font-family: Times New Roman;\"> =</span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>100</mn><mo>&#160;</mo><mi>x</mi><mo>-</mo><mstyle displaystyle=\"true\"><mfrac><mrow><mn>700</mn><mi>x</mi></mrow><mn>9</mn></mfrac></mstyle></mrow><mstyle displaystyle=\"true\"><mfrac><mrow><mn>700</mn><mi>x</mi></mrow><mn>9</mn></mfrac></mstyle></mfrac><mo>&#215;</mo><mn>100</mn><mo>=</mo><mfrac><mrow><mo>&#160;</mo><mn>200</mn></mrow><mn>7</mn></mfrac><mo>%</mo></math><br><strong><span style=\"font-family: Times New Roman;\">Shortcut:</span></strong><br><strong><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>M</mi><mi>P</mi></mrow><mrow><mi>C</mi><mi>P</mi></mrow></mfrac><mo>=</mo><mfrac><mrow><mo>&#160;</mo><mn>100</mn><mo>-</mo><mi>l</mi><mi>o</mi><mi>s</mi><mi>s</mi></mrow><mrow><mn>100</mn><mo>-</mo><mi>d</mi><mi>i</mi><mi>s</mi><mi>c</mi><mi>o</mi><mi>u</mi><mi>n</mi><mi>t</mi></mrow></mfrac><mo>=</mo><mfrac><mrow><mn>100</mn><mo>-</mo><mn>10</mn></mrow><mrow><mn>100</mn><mo>-</mo><mn>30</mn></mrow></mfrac><mo>=</mo><mfrac><mrow><mn>90</mn><mo>&#160;</mo></mrow><mn>70</mn></mfrac><mo>&#160;</mo></math></span></strong><br><span style=\"font-family: Baloo;\">यदि इसे अंकित मूल्य में बेचा जाता है, तो लाभ प्रतिशत= </span><span style=\"font-family: Times New Roman;\"> </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#160;</mo><mfrac><mn>20</mn><mn>70</mn></mfrac><mo>&#215;</mo><mn>100</mn><mo>=</mo><mfrac><mn>200</mn><mn>7</mn></mfrac><mo>%</mo></math></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "23",
                    section: "17",
                    question_en: "<p>23. <span style=\"font-family: Times New Roman;\">A shopkeeper bought toffees at a rate of 10 for ₹15 and sold them at a rate of 16 for ₹40. Find his profit percentage. (correct to two decimal place)</span></p>",
                    question_hi: "<p>23. <span style=\"font-family: Baloo;\">एक दुकानदार ने 10 की दर से ₹15 में टॉफियां खरीदी और उन्हें ₹40 में 16 की दर से बेच दिया। उसका लाभ प्रतिशत ज्ञात कीजिए। (दो दशमलव स्थान तक सही)</span></p>",
                    options_en: ["<p>65.05%</p>", "<p>33.33%</p>", 
                                "<p>50.55%</p>", "<p>66.67%</p>"],
                    options_hi: ["<p>65.05%</p>", "<p>33.33%</p>",
                                "<p>50.55%</p>", "<p>66.67%</p>"],
                    solution_en: "<p>23.(d) <span style=\"font-family: Times New Roman;\">CP of 10 toffee = </span><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Times New Roman;\">₹15</span><br><span style=\"font-family: Times New Roman;\">CP of 1 toffee =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>15</mn><mn>10</mn></mfrac></math></span><span style=\"font-family: Times New Roman;\">= ₹1.5</span><br><span style=\"font-family: Times New Roman;\">SP of 16 toffee = ₹40</span><br><span style=\"font-family: Times New Roman;\">SP of 1 toffee = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>40</mn><mn>16</mn></mfrac></math></span><span style=\"font-family: Times New Roman;\"> = ₹2.5</span><br><span style=\"font-family: Times New Roman;\">CP : SP</span><br><span style=\"font-family: Times New Roman;\">1.5 : 2.5</span><br><span style=\"font-family: Times New Roman;\"> 3&nbsp; &nbsp; :&nbsp; &nbsp;5</span><br><span style=\"font-family: Times New Roman;\">Profit % =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>3</mn></mfrac><mo>&#215;</mo><mn>100</mn><mo>=</mo><mn>66</mn><mo>.</mo><mn>67</mn><mo>%</mo></math></span></p>",
                    solution_hi: "<p>23.(d) <span style=\"font-family: Baloo;\">10 टॉफ़ी का क्रय मूल्य = ₹15</span><br><span style=\"font-family: Baloo;\">1 टॉफ़ी का क्रय मूल्य = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>15</mn><mn>10</mn></mfrac></math></span><span style=\"font-family: Times New Roman;\"> = ₹1.5</span><br><span style=\"font-family: Baloo;\">16 टॉफ़ी का विक्रय मूल्य = ₹40</span><br><span style=\"font-family: Baloo;\">1 टॉफ़ी का विक्रय मूल्य = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>40</mn><mn>16</mn></mfrac></math></span><span style=\"font-family: Times New Roman;\"> = ₹2.5</span><br><span style=\"font-family: Baloo;\">लागत मूल्य : विक्रय मूल्य</span><br><span style=\"font-family: Times New Roman;\"> 1.5&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 2.5</span><br><span style=\"font-family: Times New Roman;\"> 3&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 5</span><br><span style=\"font-family: Baloo;\">लाभ प्रतिशत =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>3</mn></mfrac><mo>&#215;</mo><mn>100</mn><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>66</mn><mo>.</mo><mn>67</mn><mo>%</mo></math></span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "24",
                    section: "17",
                    question_en: "<p>24.<span style=\"font-family: Times New Roman;\"> The selling price of a mobile phone is ₹59,620 and it was sold at 8.4% profit . the cost price (in ₹) of the mobile phone is :&nbsp;</span></p>",
                    question_hi: "<p>24.<span style=\"font-family: Baloo;\"> एक मोबाइल फोन का विक्रय मूल्य ₹59,620 है और इसे 8.4% लाभ पर बेचा गया। मोबाइल फोन का लागत मूल्य (₹ में) है:</span></p>",
                    options_en: ["<p>52,000</p>", "<p>55,000</p>", 
                                "<p>45,000</p>", "<p>50,000</p>"],
                    options_hi: ["<p>52,000</p>", "<p>55,000</p>",
                                "<p>45,000</p>", "<p>50,000</p>"],
                    solution_en: "<p>24.(b) <span style=\"font-family: Times New Roman;\">Let CP = 100</span><br><span style=\"font-family: Times New Roman;\">SP = CP + profit</span><br><span style=\"font-family: Times New Roman;\">SP = 100 + 8.4 = 108.4</span><br><span style=\"font-family: Times New Roman;\">SP = 108.4 = 59620 Rs</span><br><span style=\"font-family: Times New Roman;\">CP = 100 =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>59620</mn><mrow><mn>108</mn><mo>.</mo><mn>4</mn></mrow></mfrac><mo>&#215;</mo><mn>100</mn></math></span><span style=\"font-family: Times New Roman;\"> = 55,000 Rs</span><br><strong><span style=\"font-family: Times New Roman;\">Alternate:</span></strong><br><span style=\"font-family: Times New Roman;\">Check by options</span><br><span style=\"font-family: Times New Roman;\">CP + 8.4% of CP = 59620</span><br><span style=\"font-family: Times New Roman;\">Option b satisfies the condition</span></p>",
                    solution_hi: "<p>24.(b) <span style=\"font-family: Baloo;\">माना, लागत मूल्य = 100</span><br><span style=\"font-family: Baloo;\">विक्रय मूल्य = लागत मूल्य + लाभ</span><br><span style=\"font-family: Baloo;\">विक्रय मूल्य = 100 + 8.4 = 108.4</span><br><span style=\"font-family: Baloo;\">विक्रय मूल्य = 108.4 = 59620 रु</span><br><span style=\"font-family: Baloo;\">लागत मूल्य = 100 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>59620</mn><mrow><mn>108</mn><mo>.</mo><mn>4</mn></mrow></mfrac><mo>&#215;</mo><mn>100</mn></math></span><span style=\"font-family: Baloo;\"> = 55000 रुपये</span><br><strong><span style=\"font-family: Baloo;\">वैकल्पिक:</span></strong><br><span style=\"font-family: Baloo;\">विकल्पों द्वारा पड़ताल करने पर</span><br><span style=\"font-family: Baloo;\">लागत मूल्य का लागत मूल्य + 8.4% = 59620</span><br><span style=\"font-family: Baloo;\">विकल्प b शर्त को पूरा करता है।</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "25",
                    section: "17",
                    question_en: "<p>25. <span style=\"font-family: Times New Roman;\">A person&rsquo;s salary was increased by 50% and subsequently decreased by 50% . How much percentage does he lose or gain?</span></p>",
                    question_hi: "<p>25. <span style=\"font-family: Baloo;\">एक व्यक्ति के वेतन में 50% की वृद्धि हुई और बाद में 50% की कमी हुई। उसे कितना प्रतिशत हानि या लाभ होता है?</span></p>",
                    options_en: ["<p>Loss of 25%</p>", "<p>Gain of 50%</p>", 
                                "<p>Loss of 30%</p>", "<p>Gain of 20%</p>"],
                    options_hi: ["<p>25% की हानि</p>", "<p>50% का लाभ</p>",
                                "<p>30% की हानि</p>", "<p>20% का लाभ</p>"],
                    solution_en: "<p>25.(a) <span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>50</mn><mo>%</mo><mo>&#160;</mo><mo>=</mo><mfrac><mrow><mo>&#160;</mo><mn>1</mn></mrow><mn>2</mn></mfrac></math></span><br><strong>&nbsp;Initial&nbsp;&nbsp;&nbsp;&nbsp; :&nbsp;&nbsp;&nbsp; final</strong><br>&nbsp;&nbsp; 2&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp; &nbsp; 3<br><u>&nbsp;&nbsp; 2&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp; &nbsp; 1</u><br>&nbsp;<u>&nbsp; 4&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp; &nbsp; 3</u><br><span style=\"font-family: Times New Roman;\">Percentage change =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>-</mo><mn>1</mn></mrow><mn>4</mn></mfrac><mo>&#215;</mo><mn>100</mn></math> </span><span style=\"font-family: Times New Roman;\"> = 25% decreased</span></p>",
                    solution_hi: "<p>25.(a) <span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>50</mn><mo>%</mo><mo>=</mo><mfrac><mrow><mo>&#160;</mo><mn>1</mn></mrow><mn>2</mn></mfrac></math></span><br><span style=\"font-family: Baloo;\">प्रारंभिक : अंतिम</span><br><span style=\"font-family: Times New Roman;\">&nbsp; 2&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp; &nbsp;3</span><br><span style=\"font-family: Times New Roman;\">&nbsp; 2&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp; &nbsp;1</span><br><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Times New Roman;\"> 4&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp; &nbsp; 3</span><br><span style=\"font-family: Baloo;\">प्रतिशत = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>-</mo><mn>1</mn></mrow><mn>4</mn></mfrac><mo>&#215;</mo><mn>100</mn></math></span><span style=\"font-family: Baloo;\"> = 25% की कमी&nbsp;</span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "26",
                    section: "misc",
                    question_en: "<p>26.<span style=\"font-family: Times New Roman;\"> A vegetable vendor sold 1 kg of potatoes for Rs. 25 and earned 25% profit. In the evening, he started selling potatoes with only 10% profit. At what cost (in ₹) per kg did he sell the potatoes in the evening?</span></p>",
                    question_hi: "<p>26.<span style=\"font-family: Baloo;\"> एक सब्जी विक्रेता ने 1 kg आलू ₹25 में बेचा और 25% लाभ अर्जित किया। शाम को, उसने केवल 10% लाभ के साथ आलू बेचना शुरू किया। शाम को उसने आलू को किस कीमत (₹ में) प्रति किलो पर बेचा?</span></p>",
                    options_en: ["<p>21</p>", "<p>20</p>", 
                                "<p>24</p>", "<p>22</p>"],
                    options_hi: ["<p>21</p>", "<p>20</p>",
                                "<p>24</p>", "<p>22</p>"],
                    solution_en: "<p>26.(d) <span style=\"font-family: Times New Roman;\">initially</span><br><span style=\"font-family: Times New Roman;\">CP&nbsp; :&nbsp; &nbsp;SP</span><br><span style=\"font-family: Times New Roman;\">100 : 125</span><br><span style=\"font-family: Times New Roman;\">4&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; 5</span><br><span style=\"font-family: Times New Roman;\">5 units = 25Rs(given)</span><br><span style=\"font-family: Times New Roman;\">4 units = 20 Rs</span><br><span style=\"font-family: Times New Roman;\">Now in evening</span><br><span style=\"font-family: Times New Roman;\">CP&nbsp; :&nbsp; SP</span><br><span style=\"font-family: Times New Roman;\">100 : 110</span><br><span style=\"font-family: Times New Roman;\">10&nbsp; &nbsp;: 11</span><br><span style=\"font-family: Times New Roman;\">10 units = 20 Rs</span><br><span style=\"font-family: Times New Roman;\">11 units = 22 Rs</span></p>",
                    solution_hi: "<p>26.(d) <span style=\"font-family: Baloo;\">शुरू में,</span><br><span style=\"font-family: Baloo;\">लागत मूल्य : विक्रय मूल्य</span><br><span style=\"font-family: Times New Roman;\">100&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp; &nbsp;125</span><br><span style=\"font-family: Times New Roman;\">4&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;5</span><br><span style=\"font-family: Baloo;\">5 इकाई = 25 रुपये (दिया गया)</span><br><span style=\"font-family: Baloo;\">4 इकाई = 20 रुपये</span><br><span style=\"font-family: Baloo;\">अब शाम को,</span><br><span style=\"font-family: Baloo;\">लागत मूल्य : विक्रय मूल्य</span><br><span style=\"font-family: Times New Roman;\"> 100&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;110</span><br><span style=\"font-family: Times New Roman;\"> 10&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 11</span><br><span style=\"font-family: Baloo;\">10 इकाई = 20 रुपये</span><br><span style=\"font-family: Baloo;\">11 इकाई = 22 रुपये </span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>