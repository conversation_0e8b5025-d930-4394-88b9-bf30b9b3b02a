<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">10:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 15</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">15</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 10 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["6"] = {
                name: "Reasoning",
                start: 0,
                end: 13
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="6">Reasoning</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 14,
                end: 14
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "6",
                    question_en: "<p>1. Select the number that will come in the place of the question mark (?), if &lsquo;+&rsquo; and &lsquo; &ndash; &lsquo; are interchanged and &lsquo;&times;&rsquo; and &lsquo;&divide;&rsquo; are interchanged<br>41 &ndash; 54 &times; 6 &divide; 9 + 7 = ?</p>",
                    question_hi: "<p>1. यदि निम्&zwj;नलिखित समीकरण में \'+\' और \'-\' को आपस में बदल दिया जाए और \'&times;\' और \'&divide;\' को आपस में बदल दिया जाए, तो प्रश्न चिह्न (?) के स्थान पर कौन-सी संख्या आएगी ?<br>41 &ndash; 54 &times; 6 &divide; 9 + 7 = ?</p>",
                    options_en: ["<p>108</p>", "<p>119</p>", 
                                "<p>104</p>", "<p>115</p>"],
                    options_hi: ["<p>108</p>", "<p>119</p>",
                                "<p>104</p>", "<p>115</p>"],
                    solution_en: "<p>1.(d) <strong>Given:- </strong>41 - 54 &times; 6 <math display=\"inline\"><mo>&#247;</mo></math> 9 + 7<br>As per given instruction after interchanging &lsquo;+&rsquo; and &lsquo;-&rsquo; and &lsquo;&times;&rsquo; and &lsquo;<math display=\"inline\"><mo>&#247;</mo></math>&rsquo; we get<br>41 + 54 <math display=\"inline\"><mo>&#247;</mo></math> 6 &times; 9 - 7<br>41 + 9 &times; 9 - 7<br>41 + 81 - 7<br>41 + 74 = 115</p>",
                    solution_hi: "<p>1.(d) <strong>दिया गया है:- </strong>41 - 54 &times; 6 <math display=\"inline\"><mo>&#247;</mo></math> 9 + 7<br>दिए गए निर्देश के अनुसार \'+\' और \'-\' तथा \'&times;\' और \'<math display=\"inline\"><mo>&#247;</mo></math>\' को आपस में बदलने पर हमें प्राप्त होता है<br>41 + 54 <math display=\"inline\"><mo>&#247;</mo></math> 6 &times; 9 - 7<br>41 + 9 &times; 9 - 7<br>41 + 81 - 7<br>41 + 74 = 115</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "6",
                    question_en: "<p>2. What will come in the place of the question mark (?) in the following equation, if \'+\' and \'-\' are interchanged and \'<math display=\"inline\"><mo>&#215;</mo></math>\' and \'&divide;\' are interchanged ?&nbsp;<br>304 <math display=\"inline\"><mo>&#215;</mo></math> 19 - 101 + 23 &divide; 5 = ?</p>",
                    question_hi: "<p>2. यदि &lsquo;+&rsquo; और &lsquo;&minus;&rsquo; को आपस में बदल दिया जाए और &lsquo;&times;&rsquo; और &lsquo;&divide;&rsquo; को आपस में बदल दिया जाए, तो निम्नलिखित समीकरण में प्रश्न-चिन्ह (?) के स्थान पर क्या आएगा ?<br>304 <math display=\"inline\"><mo>&#215;</mo></math> 19 - 101 + 23 &divide; 5 = ?</p>",
                    options_en: [" 21 ", " 12 ", 
                                "  2", " 6"],
                    options_hi: [" 21 ", " 12 ",
                                "  2", " 6"],
                    solution_en: "<p>2.(c) <strong>Given:</strong> 304 &times; 19 - 101 + 23 &divide; 5 = ?<br>As per the given instruction after interchanging the symbol \'+\' and \'-\' and \'&times;\' and \'&divide;\' , we get.<br>304 &divide; 19 + 101 - 23 &times; 5<br>16 + 101 - 115<br>117 - 115 = 2</p>",
                    solution_hi: "<p>2.(c)<strong> दिया गया है:</strong> 304 &times; 19 - 101 + 23 &divide; 5 = ?<br>दिए गए निर्देश के अनुसार प्रतीक \'+\' और \'-\' और \'&times;\' और \'&divide;\' को आपस में बदलने के बाद, हमें मिलता है।<br>304 &divide; 19 + 101 - 23 &times; 5<br>16 + 101 - 115<br>117 - 115 = 2</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "6",
                    question_en: "<p>3. What will come in place of the question mark (?) in the following equation, if &lsquo;+&rsquo; and &lsquo;&ndash;&rsquo; are interchanged and &lsquo;&times;&rsquo; and &lsquo;&divide;&rsquo; are interchanged ?<br>164 &ndash; 173 &divide; 110 + 140 &times; 5 = ?</p>",
                    question_hi: "<p>3. यदि &lsquo;+&rsquo; और &lsquo;&ndash;&rsquo; को आपस में बदल दिया जाए और &lsquo;&times;&rsquo; और &lsquo;&divide;&rsquo; को आपस में बदल दिया जाए, तो निम्नलिखित समीकरण में प्रश्न चिह्न (?) के स्थान पर क्या आएगा ?<br>164 &ndash; 173 &divide; 110 + 140 &times; 5 = ?</p>",
                    options_en: [" 19616 ", " 11619 ", 
                                " 19166 ", " 11916"],
                    options_hi: [" 19616 ", " 11619 ",
                                " 19166 ", " 11916"],
                    solution_en: "<p>3.(c)<strong> Given: </strong>164 &ndash; 173 &divide; 110 + 140 &times; 5 = ?<br>As per the given instruction after interchanging the symbol &lsquo;+&rsquo; and &lsquo;&ndash;&rsquo; and &lsquo;&times;&rsquo; and &lsquo;&divide;&rsquo; . we get,<br>164 + 173 &times; 110 - 140 &divide; 5 = ? <br>164 + 19030 - 28 = ?<br>19194 - 28 = 19166</p>",
                    solution_hi: "<p>3.(c) <strong>दिया गया है: </strong>164 &ndash; 173 &divide; 110 + 140 &times; 5 = ?<br>दिए गए निर्देश के अनुसार प्रतीक \'+\' और \'-\' और \'&times;\' और \'&divide;\' को बदलने के बाद। हम पाते हैं,<br>164 + 173 &times; 110 - 140 &divide; 5 = ? <br>164 + 19030 - 28 = ?<br>19194 - 28 = 19166</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "6",
                    question_en: "<p>4. Which two signs should be interchanged to make the following equation correct ?<br>247 &divide; 13 + 16 &times; 3 &minus; 148 = 119</p>",
                    question_hi: "<p>4. निम्नलिखित समीकरण को सही करने के लिए किन दो चिह्नों को आपस में बदलना चाहिए ?<br>247 &divide; 13 + 16 &times; 3 &minus; 148 = 119</p>",
                    options_en: ["<p>+ and &times;</p>", "<p>&minus; and +</p>", 
                                "<p>&minus; and &times;</p>", "<p>&divide; and &times;</p>"],
                    options_hi: ["<p>+ और &times;</p>", "<p>&minus; और +</p>",
                                "<p>&minus; और &times;</p>", "<p>&divide; और &times;</p>"],
                    solution_en: "<p>4.(b) <strong>Given:-</strong> 247 <math display=\"inline\"><mo>&#247;</mo></math> 13 + 16 &times; 3 - 148 = 119<br>After going through all the options, option b satisfied. After interchanging &lsquo;+&rsquo; and &lsquo;-&rsquo;, we get<br>19 - 48 + 148<br>19 + 100 = 119</p>",
                    solution_hi: "<p>4.(b)<strong> दिया गया:-</strong> 247 <math display=\"inline\"><mo>&#247;</mo></math> 13 + 16 &times; 3 - 148 = 119<br>सभी विकल्पों की जांच करने पर विकल्प b संतुष्ट करता है। \'+\' और \'-\' को आपस में बदलने के बाद, हमें मिलता है<br>19 - 48 + 148<br>19 + 100 = 119</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "6",
                    question_en: "<p>5. What will come in the place of the question mark (?) in the following equation, if &lsquo;+&rsquo; and &lsquo;&minus;&rsquo; are interchanged and &lsquo;&times;&rsquo; and &lsquo;&divide;&rsquo; are interchanged ?<br>342 &times; 18 + 79 &minus; 45 &divide; 3 = ?</p>",
                    question_hi: "<p>5. यदि &lsquo;+&rsquo; और &lsquo;&minus;&rsquo; को आपस में बदल दिया जाए और &lsquo;&times;&rsquo; और &lsquo;&divide;&rsquo; को आपस में बदल दिया जाए, तो निम्नलिखित समीकरण में प्रश्न-चिन्ह (?) के स्थान पर क्या आएगा ?<br>342 &times; 18 + 79 &minus; 45 &divide; 3 = ?</p>",
                    options_en: ["<p>65</p>", "<p>75</p>", 
                                "<p>85</p>", "<p>55</p>"],
                    options_hi: ["<p>65</p>", "<p>75</p>",
                                "<p>85</p>", "<p>55</p>"],
                    solution_en: "<p>5.(b) <strong>Given:- </strong>342 &times; 18 + 79 - 45 <math display=\"inline\"><mo>&#247;</mo></math> 3<br>As per given instruction after interchanging &lsquo;+&rsquo; and &lsquo;-&rsquo; and &lsquo;&times;&rsquo; and &lsquo;&divide;&rsquo;, we get<br>342 <math display=\"inline\"><mo>&#247;</mo></math> 18 - 79 + 45 &times; 3<br>19 - 79 + 135 = 75</p>",
                    solution_hi: "<p>5.(b) <strong>दिया गया:- </strong>342 &times; 18 + 79 - 45<math display=\"inline\"><mo>&#247;</mo></math> 3<br>दिए गए निर्देश के अनुसार \'+\' और \'-\' तथा \'&times;\' और \'&divide;\' को आपस में बदलने पर हमें प्राप्त होता है<br>342 <math display=\"inline\"><mo>&#247;</mo></math> 18 - 79 + 45 &times; 3<br>19 - 79 + 135 = 75</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "6",
                    question_en: "<p>6. What will come in place of the question mark (?) in the following equation if &lsquo;+&rsquo; and &lsquo;&minus;&rsquo; are interchanged and &lsquo;&times;&rsquo; and &lsquo;&divide;&rsquo; are interchanged ?<br>16 &divide; 4 + 8 &times; 8 &minus; 4 = ?</p>",
                    question_hi: "<p>6. यदि \'+\' और \'&minus;\' को आपस में बदल दिया जाए और \'&times;\' और \'&divide;\' को आपस बदल दिया जाए, तो निम्नलिखित समीकरण में प्रश्न चिह्न (?) के स्थान पर क्या आएगा ?<br>16 &divide; 4 + 8 &times; 8 &minus; 4 = ?</p>",
                    options_en: ["<p>11</p>", "<p>67</p>", 
                                "<p>42</p>", "<p>36</p>"],
                    options_hi: ["<p>11</p>", "<p>67</p>",
                                "<p>42</p>", "<p>36</p>"],
                    solution_en: "<p>6.(b) <strong>Given : </strong>16 &divide; 4 + 8 &times; 8 &minus; 4 = ?<br>As per the instructions after interchanging the symbol &lsquo;+&rsquo; and &lsquo;&minus;&rsquo; and &lsquo;&times;&rsquo; and &lsquo;&divide;&rsquo;, we get.<br>16 &times; 4 - 8 &divide; 8 + 4 = ?<br>64 - 1 + 4 = 67</p>",
                    solution_hi: "<p>6.(b) <strong>दिया गया है :</strong> 16 &divide; 4 + 8 &times; 8 &minus; 4 = ?<br>निर्देशों के अनुसार प्रतीक \'+\' और \'-\' तथा \'&times;\' और \'&divide;\' को आपस में बदलने के बाद, हमें प्राप्त होता है।<br>16 &times; 4 - 8 &divide; 8 + 4 = ?<br>64 - 1 + 4 = 67</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "6",
                    question_en: "<p>7. Which of the following operations should be interchanged to make the following equation correct ?<br>12 + 12 &minus; 4 &times; 4 &divide; 2 = 22</p>",
                    question_hi: "<p>7. दिए गए समीकरण को सही बनाने (संतुलित करने) के लिए निम्नलिखित में से किन संक्रियाओं को आपस में बदलना होगा ?<br>12 + 12 &minus; 4 &times; 4 &divide; 2 = 22</p>",
                    options_en: [" + and ÷", " ÷ and −", 
                                " − and +", " × and −<br /> "],
                    options_hi: [" + और ÷", " ÷ और −",
                                " − और +", " × और −"],
                    solution_en: "<p>7.(b) <strong>Given : </strong>12 + 12 &minus; 4 &times; 4 &divide; 2 = 22<br>After going through all the options, option (b) satisfies.<br>12 + 12 &divide; 4 &times; 4 - 2 = 22<br>12 + 3 &times; 4 - 2 = 22<br>12 + 12 - 2 = 22<br>12 +10 = 22</p>",
                    solution_hi: "<p>7.(b) <strong>दिया गया है : </strong>12 + 12 &minus; 4 &times; 4 &divide; 2 = 22<br>सभी विकल्पों को देखने के बाद विकल्प (b) संतुष्ट करता है।<br>12 + 12 &divide; 4 &times; 4 - 2 = 22<br>12 + 3 &times; 4 - 2 = 22<br>12 + 12 - 2 = 22<br>12 +10 = 22</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "6",
                    question_en: "<p>8. Which of the following interchanges of numbers (not digits) would make the given equation correct ?<br><math display=\"inline\"><msup><mrow><mn>2</mn></mrow><mrow><mn>57</mn><mo>-</mo><mi>&#160;</mi><mn>33</mn><mo>&#247;</mo><mn>11</mn><mo>&#215;</mo><mn>13</mn><mo>-</mo><mn>2</mn></mrow></msup></math> + 3<sup>64 - 56 &divide; 8 &times; 4 - 44</sup> = 35</p>",
                    question_hi: "<p>8. दिए गए समीकरण को सही करने के लिए किन दो संख्याओं (अंक नहीं) को आपस में बदला जाना चाहिए ?<br><math display=\"inline\"><msup><mrow><mn>2</mn></mrow><mrow><mn>57</mn><mo>-</mo><mi>&#160;</mi><mn>33</mn><mo>&#247;</mo><mn>11</mn><mo>&#215;</mo><mn>13</mn><mo>-</mo><mn>2</mn></mrow></msup></math> + 3<sup>64 - 56 &divide; 8 &times; 4 - 44</sup> = 35</p>",
                    options_en: ["  2 and 4", " 56 and 57 ", 
                                " 57 and 64 ", " 33 and 44 "],
                    options_hi: ["  2 और 4", " 56 और 57 ",
                                " 57 और 64 ", "<p>33 और 44</p>"],
                    solution_en: "<p>8.(d) <strong>Given :-</strong> <math display=\"inline\"><msup><mrow><mn>2</mn></mrow><mrow><mn>57</mn><mo>-</mo><mi>&#160;</mi><mn>33</mn><mo>&#247;</mo><mn>11</mn><mo>&#215;</mo><mn>13</mn><mo>-</mo><mn>2</mn></mrow></msup></math> + 3<sup>64 - 56 &divide; 8 &times; 4 - 44</sup> = 35<br>After going through all the options, option d satisfied. After interchanging 33 and 44 we get,<br><math display=\"inline\"><msup><mrow><mn>2</mn></mrow><mrow><mn>57</mn><mo>-</mo><mi>&#160;</mi><mn>44</mn><mo>&#247;</mo><mn>11</mn><mo>&#215;</mo><mn>13</mn><mo>-</mo><mn>2</mn></mrow></msup></math> + 3<sup>64 - 56 &divide; 8 &times; 4 - 33</sup> = 35<br><math display=\"inline\"><msup><mrow><mn>2</mn></mrow><mrow><mn>57</mn><mo>-</mo><mi>&#160;</mi><mn>4</mn><mo>&#215;</mo><mn>13</mn><mo>-</mo><mn>2</mn></mrow></msup></math> + 3<sup>64 - 28 - 33</sup> = 35<br><math display=\"inline\"><msup><mrow><mn>2</mn></mrow><mrow><mn>57</mn><mo>-</mo><mi>&#160;</mi><mn>52</mn><mo>-</mo><mn>2</mn></mrow></msup></math> + 3<sup>64 - 61</sup> = 35<br><math display=\"inline\"><msup><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></msup></math> + 3<sup>3</sup> = 35<br>8 + 27 = 35<br>35 = 35<br>L.H.S. = R.H.S.</p>",
                    solution_hi: "<p>8.(d) <strong>दिया गया :-</strong> <math display=\"inline\"><msup><mrow><mn>2</mn></mrow><mrow><mn>57</mn><mo>-</mo><mi>&#160;</mi><mn>33</mn><mo>&#247;</mo><mn>11</mn><mo>&#215;</mo><mn>13</mn><mo>-</mo><mn>2</mn></mrow></msup></math> + 3<sup>64 - 56 &divide; 8 &times; 4 - 44</sup> = 35<br>सभी विकल्पों की जांच करने पर विकल्प d संतुष्ट करता है। 33 और 44 को आपस में बदलने पर हमें प्राप्त होता है,<br><math display=\"inline\"><msup><mrow><mn>2</mn></mrow><mrow><mn>57</mn><mo>-</mo><mi>&#160;</mi><mn>44</mn><mo>&#247;</mo><mn>11</mn><mo>&#215;</mo><mn>13</mn><mo>-</mo><mn>2</mn></mrow></msup></math> + 3<sup>64 - 56 &divide; 8 &times; 4 - 33</sup> = 35<br><math display=\"inline\"><msup><mrow><mn>2</mn></mrow><mrow><mn>57</mn><mo>-</mo><mi>&#160;</mi><mn>4</mn><mo>&#215;</mo><mn>13</mn><mo>-</mo><mn>2</mn></mrow></msup></math> + 3<sup>64 - 28 - 33</sup> = 35<br><math display=\"inline\"><msup><mrow><mn>2</mn></mrow><mrow><mn>57</mn><mo>-</mo><mi>&#160;</mi><mn>52</mn><mo>-</mo><mn>2</mn></mrow></msup></math> + 3<sup>64 - 61</sup> = 35<br><math display=\"inline\"><msup><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></msup></math> + 3<sup>3</sup> = 35<br>8 + 27 = 35<br>35 = 35<br>L.H.S. = R.H.S.</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "6",
                    question_en: "<p>9. Which number will come in the place of the question mark (?) in the following equation if \'+\' and \'-\' are interchanged and \'<math display=\"inline\"><mo>&#215;</mo></math>\' and \'&divide;\' are interchanged ? <br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>49</mn><mo>&#215;</mo><mn>7</mn><mo>-</mo><mn>6</mn><mo>&#247;</mo><mn>2</mn><mo>+</mo><mn>15</mn><mo>=</mo><mo>?</mo></math></p>",
                    question_hi: "<p>9. यदि निम्नलिखित समीकरण में \'+\' और \'-\' को आपस में बदल दिया जाता है तथा \'<math display=\"inline\"><mo>&#215;</mo></math>\' और &lsquo;&divide;\' को आपस में बदल दिया जाता है, तो प्रश्न चिह्न (?) के स्थान कौन-सी संख्या आएगी ?<br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>49</mn><mo>&#215;</mo><mn>7</mn><mo>-</mo><mn>6</mn><mo>&#247;</mo><mn>2</mn><mo>+</mo><mn>15</mn><mo>=</mo><mo>?</mo></math></p>",
                    options_en: ["<p>2</p>", "<p>5</p>", 
                                "<p>8</p>", "<p>4</p>"],
                    options_hi: ["<p>2</p>", "<p>5</p>",
                                "<p>8</p>", "<p>4</p>"],
                    solution_en: "<p>9.(d) <strong>Given :- </strong>49 &times; 7 - 6 <math display=\"inline\"><mo>&#247;</mo></math> 2 + 15<br>As per given instruction after interchanging &lsquo;+&rsquo; and &lsquo;-&rsquo; and &lsquo;&times;&rsquo; and &lsquo;<math display=\"inline\"><mo>&#247;</mo></math>&rsquo; we get,<br>49 <math display=\"inline\"><mo>&#247;</mo></math> 7 + 6 &times; 2 - 15<br>7 + 12 - 15<br>19 - 15 = 4</p>",
                    solution_hi: "<p>9.(d) <strong>दिया गया है :-</strong> 49 &times; 7 - 6 <math display=\"inline\"><mo>&#247;</mo></math> 2 + 15<br>दिए गए निर्देश के अनुसार \'+\' और \'-\' तथा \'&times;\' और \'<math display=\"inline\"><mo>&#247;</mo></math>\' को आपस में बदलने के बाद हमें प्राप्त होता है,<br>49 <math display=\"inline\"><mo>&#247;</mo></math> 7 + 6 &times; 2 - 15<br>7 + 12 - 15<br>19 - 15 = 4</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "6",
                    question_en: "<p>10. Which of the following interchanges of numbers (not digits) would make the given equation correct ? <br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>480</mn><mo>&#247;</mo><mn>0</mn><mo>.</mo><mn>3</mn><mo>&#215;</mo><mn>5</mn><mo>+</mo><mn>500</mn></mrow><mrow><mn>190</mn><mo>+</mo><mn>9</mn><mo>&#215;</mo><mn>27</mn><mo>&#247;</mo><mn>1</mn><mo>.</mo><mn>6</mn></mrow></mfrac></math> = 2</p>",
                    question_hi: "<p>10. समीकरण को सही करने के लिए किन दो संख्याओं (अंक नहीं) हीं को आपस में बदला जाना चाहिए ? <br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>480</mn><mo>&#247;</mo><mn>0</mn><mo>.</mo><mn>3</mn><mo>&#215;</mo><mn>5</mn><mo>+</mo><mn>500</mn></mrow><mrow><mn>190</mn><mo>+</mo><mn>9</mn><mo>&#215;</mo><mn>27</mn><mo>&#247;</mo><mn>1</mn><mo>.</mo><mn>6</mn></mrow></mfrac></math> = 2</p>",
                    options_en: ["<p>500 and190</p>", "<p>0.3 and 1.6</p>", 
                                "<p>27 and 480</p>", "<p>5 and 9</p>"],
                    options_hi: ["<p>500 और 190</p>", "<p>0.3 और 1.6</p>",
                                "<p>27 और 480</p>", "<p>5 और 9</p>"],
                    solution_en: "<p>10.(b) <strong>Given:- <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>480</mn><mo>&#247;</mo><mn>0</mn><mo>.</mo><mn>3</mn><mo>&#215;</mo><mn>5</mn><mo>+</mo><mn>500</mn></mrow><mrow><mn>190</mn><mo>+</mo><mn>9</mn><mo>&#215;</mo><mn>27</mn><mo>&#247;</mo><mn>1</mn><mo>.</mo><mn>6</mn></mrow></mfrac></math> </strong>= 2<br>After going through all the options, option b satisfied. After interchanging 0.3 and 1.6 we get,<br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>480</mn><mo>&#247;</mo><mn>1</mn><mo>.</mo><mn>6</mn><mo>&#215;</mo><mn>5</mn><mo>+</mo><mn>500</mn></mrow><mrow><mn>190</mn><mo>+</mo><mn>9</mn><mo>&#215;</mo><mn>27</mn><mo>&#247;</mo><mn>0</mn><mo>.</mo><mn>3</mn></mrow></mfrac></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>300</mn><mo>&#215;</mo><mn>5</mn><mo>+</mo><mn>500</mn></mrow><mrow><mn>190</mn><mo>+</mo><mn>9</mn><mo>&#215;</mo><mn>90</mn></mrow></mfrac></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1500</mn><mo>+</mo><mn>500</mn></mrow><mrow><mn>190</mn><mo>+</mo><mn>810</mn></mrow></mfrac></math><br><math display=\"inline\"><mfrac><mrow><mn>2000</mn></mrow><mrow><mn>1000</mn></mrow></mfrac></math> = 2<br>L.H.S. = R.H.S.</p>",
                    solution_hi: "<p>10.(b)<strong> दिया गया है:-</strong> <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>480</mn><mo>&#247;</mo><mn>0</mn><mo>.</mo><mn>3</mn><mo>&#215;</mo><mn>5</mn><mo>+</mo><mn>500</mn></mrow><mrow><mn>190</mn><mo>+</mo><mn>9</mn><mo>&#215;</mo><mn>27</mn><mo>&#247;</mo><mn>1</mn><mo>.</mo><mn>6</mn></mrow></mfrac></math> = 2<br>सभी विकल्पों की जांच करने पर विकल्प b संतुष्ट करता है। 0.3 और 1.6 को आपस में बदलने पर हमें प्राप्त होता है,<br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>480</mn><mo>&#247;</mo><mn>1</mn><mo>.</mo><mn>6</mn><mo>&#215;</mo><mn>5</mn><mo>+</mo><mn>500</mn></mrow><mrow><mn>190</mn><mo>+</mo><mn>9</mn><mo>&#215;</mo><mn>27</mn><mo>&#247;</mo><mn>0</mn><mo>.</mo><mn>3</mn></mrow></mfrac></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>300</mn><mo>&#215;</mo><mn>5</mn><mo>+</mo><mn>500</mn></mrow><mrow><mn>190</mn><mo>+</mo><mn>9</mn><mo>&#215;</mo><mn>90</mn></mrow></mfrac></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1500</mn><mo>+</mo><mn>500</mn></mrow><mrow><mn>190</mn><mo>+</mo><mn>810</mn></mrow></mfrac></math><br><math display=\"inline\"><mfrac><mrow><mn>2000</mn></mrow><mrow><mn>1000</mn></mrow></mfrac></math> = 2<br>L.H.S. = R.H.S.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "6",
                    question_en: "<p>11. What will come in the place of the question mark (?) in the following equation, if &lsquo;+&rsquo; and &lsquo;&minus;&rsquo; are interchanged and &lsquo;&times;&rsquo; and &lsquo;&divide;&rsquo;are interchanged ?<br>315 &times; 15 &minus; 93 + 16 &divide; 7 = ?</p>",
                    question_hi: "<p>11. यदि &lsquo;+&rsquo; और &lsquo;&minus;&rsquo; को आपस में बदल दिया जाए और &lsquo;&times;&rsquo; और &lsquo;&divide;&rsquo; को आपस में बदल दिया जाए, तो निम्नलिखित समीकरण में प्रश्न-चिन्ह (?) के स्थान पर क्या आएगा ?<br>315 &times; 15 &minus; 93 + 16 &divide; 7 = ?</p>",
                    options_en: ["<p>4</p>", "<p>8</p>", 
                                "<p>2</p>", "<p>12</p>"],
                    options_hi: ["<p>4</p>", "<p>8</p>",
                                "<p>2</p>", "<p>12</p>"],
                    solution_en: "<p>11.(c) <strong>Given:- </strong>315 &times; 15 - 93 + 16 <math display=\"inline\"><mo>&#247;</mo></math> 7<br>As per given instruction after interchanging &lsquo;+&rsquo; and &lsquo;-&rsquo; and &lsquo;&times;&rsquo; and &lsquo;<math display=\"inline\"><mo>&#247;</mo></math>&rsquo; we get<br>315 <math display=\"inline\"><mo>&#247;</mo></math> 15 + 93 - 16 &times; 7<br>21 + 93 - 112<br>114 - 112 = 2</p>",
                    solution_hi: "<p>11.(c) <strong>दिया गया है:- </strong>315 &times; 15 - 93 + 16 <math display=\"inline\"><mo>&#247;</mo></math> 7<br>दिए गए निर्देश के अनुसार \'+\' और \'-\' तथा \'&times;\' और \'<math display=\"inline\"><mo>&#247;</mo></math>\' को आपस में बदलने पर हमें प्राप्त होता है<br>315 <math display=\"inline\"><mo>&#247;</mo></math> 15 + 93 - 16 &times; 7<br>21 + 93 - 112<br>114 - 112 = 2</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "6",
                    question_en: "<p>12. What will come in place of the question mark (?) in the following equation, if &lsquo;+&rsquo; and &lsquo;&ndash;&rsquo; are interchanged and &lsquo;&times;&rsquo; and &lsquo;&divide;&rsquo; are interchanged ? <br>107 + 2028 &times; 6 &ndash; 427 &divide; 7 = ?</p>",
                    question_hi: "<p>12. यदि &lsquo;+&rsquo; और &lsquo;&ndash;&rsquo; को आपस में बदल दिया जाए और &lsquo;&times;&rsquo; और &lsquo;&divide;&rsquo; को आपस में बदल दिया जाए, तो निम्नलिखित समीकरण में प्रश्न चिह्न (?) के स्थान पर क्या आएगा ?<br>107 + 2028 &times; 6 &ndash; 427 &divide; 7 = ?</p>",
                    options_en: ["<p>2758</p>", "<p>2268</p>", 
                                "<p>2785</p>", "<p>2578</p>"],
                    options_hi: ["<p>2758</p>", "<p>2268</p>",
                                "<p>2785</p>", "<p>2578</p>"],
                    solution_en: "<p>12.(a) <strong>Given: </strong>107 + 2028 &times; 6 &ndash; 427 &divide; 7 = ?<br>As per the instructions after interchanging the symbol &lsquo;&times;&rsquo; and &lsquo;&divide;&rsquo; and &lsquo;+&rsquo; and &lsquo;&ndash;&rsquo; we get.<br>107 - 2028 &divide; 6 + 427 &times; 7 = ?<br>107 - 338 + 2989<br>3096 - 338 = 2758</p>",
                    solution_hi: "<p>12.(a)<strong>&nbsp;दिया गया है: </strong>107 + 2028 &times; 6 &ndash; 427 &divide; 7 = ?<br>निर्देशों के अनुसार प्रतीक \'&times;\' और \'&divide;\' तथा \'+\' और \'-\' को आपस में बदलने पर हमें प्राप्त होता है।<br>107 - 2028 &divide; 6 + 427 &times; 7 = ?<br>107 - 338 + 2989<br>3096 - 338 = 2758</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "6",
                    question_en: "<p>13. What will come in place of the question mark (?) in the following equation, if &lsquo;&times;&rsquo; is replaced by &lsquo;&divide;&rsquo;, &lsquo;&divide;&rsquo; is replaced by &lsquo;+&rsquo;, &lsquo;+&rsquo; is replaced by &lsquo;&times;&rsquo; and &lsquo;&minus;&rsquo; remains unchanged ? <br>42 &times; 3 &divide; 4 + 9 &minus; 17 = ?</p>",
                    question_hi: "<p>13. यदि \'&times;\' को \'&divide;\' से बदल दिया जाए, \'&divide;\' को \'+\' से बदल दिया जाए, \'+\' को \'&times;\' से बदल दिया जाए और \'&minus;\' अपरिवर्तित रहे, तो निम्नलिखित समीकरण में प्रश्नवाचक चिह्न (?) के स्थान पर क्या आएगा ? <br>42 &times; 3 &divide; 4 + 9 &minus; 17 = ?</p>",
                    options_en: ["<p>23</p>", "<p>33</p>", 
                                "<p>29</p>", "<p>30</p>"],
                    options_hi: ["<p>23</p>", "<p>33</p>",
                                "<p>29</p>", "<p>30</p>"],
                    solution_en: "<p>13.(b)<strong> Given:</strong> 42 &times; 3 &divide; 4 + 9 &minus; 17 = ?<br>As per the instructions after interchanging the symbol , we get.<br>42 &divide; 3 + 4 &times; 9 &minus; 17 = ? <br>14 + 4 &times; 9 - 17 <br>14 + 36 - 17<br>50 - 17 = 33</p>",
                    solution_hi: "<p>13.(b) <strong>दिया गया है: </strong>42 &times; 3 &divide; 4 + 9 &minus; 17 = ?<br>निर्देशों के अनुसार, चिन्हों को बदलने के बाद हमें प्राप्त होता है।<br>42 &divide; 3 + 4 &times; 9 &minus; 17 = ? <br>14 + 4 &times; 9 - 17 <br>14 + 36 - 17<br>50 - 17 = 33</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "6",
                    question_en: "<p>14. What will come in place of the question mark (?) in the following equation if &lsquo;+&rsquo; and &lsquo;&times;&lsquo; are interchanged and &lsquo;&minus;&rsquo; and &lsquo;&divide;&rsquo; are interchanged ? <br>27 &minus; 6 + 4 &times; 8 &divide; 6 = ?</p>",
                    question_hi: "<p>14. निम्नलिखित समीकरण में यदि \'+\' और \'&times;\' को आपस में बदल दिया जाए और \'-\' और \'&divide;\' को आपस में बदल दिया जाए तो प्रश्न चिह्न (?) के स्थान पर कौन सी संख्&zwj;या आएगी ?<br>27 &minus; 6 + 4 &times; 8 &divide; 6 = ?</p>",
                    options_en: ["<p>22</p>", "<p>18</p>", 
                                "<p>16</p>", "<p>20</p>"],
                    options_hi: ["<p>22</p>", "<p>18</p>",
                                "<p>16</p>", "<p>20</p>"],
                    solution_en: "<p>14.(d) <strong>Given: </strong>27 &minus; 6 + 4 &times; 8 &divide; 6 = ?<br>As per the instruction given in question, after interchanging the symbol &lsquo;+&rsquo; and &lsquo;&times;&lsquo; and &lsquo;&minus;&rsquo; and &lsquo;&divide;&rsquo;<br>We get<br>27 &minus; 6 + 4 &times; 8 &divide; 6 = ? <br>27 &divide; 6 &times; 4 + 8 - 6 <br><math display=\"inline\"><mfrac><mrow><mn>9</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> &times; 4 + 8 - 6<br>18 + 8 - 6 <br>26 - 6 = 20</p>",
                    solution_hi: "<p>14.(d) <strong>दिया गया है:</strong> 27 &minus; 6 +4 &times; 8 &divide; 6 = ?<br>प्रश्न में दिए गए निर्देश के अनुसार, प्रतीक \'+\' और \'&times;\' और \'-\' और \'&divide;\' को आपस में बदलने के बाद<br>हम पाते हैं<br>27 &minus; 6 + 4 &times; 8 &divide; 6 = ? <br>27 &divide; 6 &times; 4 + 8 - 6 <br><math display=\"inline\"><mfrac><mrow><mn>9</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> &times; 4 + 8 - 6<br>18 + 8 - 6 <br>26 - 6 = 20</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "misc",
                    question_en: "<p>15. What will come in place of the question mark (?) in the following equation, if &lsquo;+&rsquo; and &lsquo;&ndash;&rsquo; are interchanged and &lsquo;&times;&rsquo; and &lsquo;&divide;&rsquo; are interchanged ? <br>2864 &times; 4 &ndash; 168 &divide; 2 + 69 = ?</p>",
                    question_hi: "<p>15. यदि &lsquo;+&rsquo; और &lsquo;&ndash;&rsquo; को आपस में बदल दिया जाए और &lsquo;&times;&rsquo; और &lsquo;&divide;&rsquo; को आपस में बदल दिया जाए, तो निम्नलिखित समीकरण में प्रश्न चिह्न (?) के स्थान पर क्या आएगा ? <br>2864 &times; 4 &ndash; 168 &divide; 2 + 69 = ?</p>",
                    options_en: ["<p>389</p>", "<p>983</p>", 
                                "<p>839</p>", "<p>938</p>"],
                    options_hi: ["<p>389</p>", "<p>983</p>",
                                "<p>839</p>", "<p>938</p>"],
                    solution_en: "<p>15.(b) <strong>Given: </strong>2864 &times; 4 &ndash; 168 &divide; 2 + 69 = ? <br>As per the instructions given in the question, after interchanging the symbols &lsquo;+&rsquo; and &lsquo;&ndash;&rsquo; and &lsquo;&times;&rsquo; and &lsquo;&divide;&rsquo; we get.<br>2864 &divide; 4 + 168 &times; 2 - 69 = ? <br>716 +168 &times; 2 - 69<br>716 + 336 - 69<br>1052 - 69 = 983</p>",
                    solution_hi: "<p>15.(b) <strong>दिया गया है:</strong> 2864 &times; 4 &ndash; 168 &divide; 2+ 69 = ?&nbsp;<br>प्रश्न में दिए गए निर्देशों के अनुसार, प्रतीकों \'+\' और \'-\' तथा \'&times;\' और \'&divide;\' को आपस में बदलने पर हमें प्राप्त होता है।<br>2864 &divide; 4 + 168 &times; 2 - 69 = ? <br>716 +168 &times; 2 - 69<br>716 + 336 - 69<br>1052 - 69 = 983</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>