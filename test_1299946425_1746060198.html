<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Smart Test Interface</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        .question-nav{position:fixed;top:70px;right:0;height:calc(100vh - 70px);width:320px;transition:all .3s;z-index:100;display:flex;flex-direction:column}
        .question-content{margin-right:0;transition:all .3s}
        .question-box{width:40px;height:40px;margin:5px;cursor:pointer}
        .attempted{background-color:#3498db!important;color:white}
        .correct{background-color:#2ecc71!important;color:white}
        .incorrect{background-color:#e74c3c!important;color:white}
        .current{border:3px solid #f39c12!important}
        #question-boxes{overflow-y:auto;max-height:calc(100vh - 150px);padding-bottom:15px}
        @media (max-width:768px){.question-nav{width:100%;right:-100%}.question-content{margin-right:0}}
    </style>
</head>
<body>
    <div class="container-fluid p-0">
        <!-- Header with controls -->
        <nav class="navbar navbar-expand-lg navbar-dark bg-primary sticky-top">
            <div class="container-fluid">
                <a class="navbar-brand" href="#">
                    <i class="fas fa-graduation-cap me-2"></i>Smart Test
                </a>
                <!-- Section selector -->
                <div class="navbar-nav me-auto section-tabs">
                    <select class="form-select" id="section-selector" onchange="switchSection(this.value)">
                        <option value="all">All Sections</option>
                    </select>
                </div>
                <div class="d-flex align-items-center">
                    <!-- Timer -->
                    <div class="bg-light text-primary rounded px-3 py-2 me-2 fw-bold" id="timer">
                        <i class="far fa-clock me-2"></i><span id="timer-display">100:00</span>
                    </div>
                    <!-- Submit button -->
                    <button class="btn btn-success me-2" id="submit-btn" onclick="submitTest()">
                        <i class="fas fa-check me-1"></i> Submit
                    </button>
                    <!-- Language toggle -->
                    <button class="btn btn-light me-2" id="lang-btn" onclick="toggleLanguage()">
                        <i class="fas fa-language"></i>
                    </button>
                    <!-- Menu toggle for mobile -->
                    <button class="btn btn-light d-lg-none" id="menu-toggle" onclick="toggleMenu()">
                        <i class="fas fa-th"></i>
                    </button>
                </div>
            </div>
        </nav>
        <div class="row m-0">
            <!-- Main content area -->
            <div class="col p-0 question-content" id="question-content">
                <div class="container py-4">
                    <!-- Question navigation -->
                    <div class="d-flex justify-content-between mb-4">
                        <button class="btn btn-outline-primary" onclick="prevQuestion()">
                            <i class="fas fa-chevron-left me-1"></i> Previous
                        </button>
                        <div class="fw-bold" id="question-counter">Question 1 of 100</div>
                        <button class="btn btn-outline-primary" onclick="nextQuestion()">
                            Next <i class="fas fa-chevron-right ms-1"></i>
                        </button>
                    </div>
                    <!-- Questions will be displayed here -->
                    <div id="questions-container"></div>
                </div>
            </div>
            <!-- Question navigation sidebar -->
            <div class="bg-light question-nav" id="question-nav">
                <div class="p-3">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h5 class="m-0">Questions</h5>
                        <div>
                            <span class="badge bg-secondary me-1" id="attempted-count">0</span>
                            <span class="badge bg-secondary" id="total-count">100</span>
                            <button class="btn btn-sm btn-outline-secondary ms-2 d-lg-none" onclick="toggleMenu()">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    </div>
                    <div class="d-flex flex-wrap" id="question-boxes"></div>
                </div>
            </div>
        </div>
        <!-- Results modal -->
        <div class="modal fade" id="results-modal" tabindex="-1" aria-hidden="true">
            <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content">
                    <div class="modal-header bg-primary text-white">
                        <h5 class="modal-title">Test Results</h5>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <div class="row text-center">
                            <div class="col-6 mb-3">
                                <div class="p-3 bg-primary bg-opacity-10 rounded">
                                    <h3 id="score-value">0</h3>
                                    <div class="small text-muted">Score</div>
                                </div>
                            </div>
                            <div class="col-6 mb-3">
                                <div class="p-3 bg-success bg-opacity-10 rounded">
                                    <h3 id="correct-value">0</h3>
                                    <div class="small text-muted">Correct</div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="p-3 bg-danger bg-opacity-10 rounded">
                                    <h3 id="incorrect-value">0</h3>
                                    <div class="small text-muted">Incorrect</div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="p-3 bg-secondary bg-opacity-10 rounded">
                                    <h3 id="unattempted-value">0</h3>
                                    <div class="small text-muted">Unattempted</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-primary" onclick="reviewTest()">Review Test</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 100 * 60;
        let resultsModal;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            resultsModal = new bootstrap.Modal(document.getElementById('results-modal'));
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 768) {
                document.getElementById('question-content').style.marginRight = '320px';
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["6"] = {
                name: "Reasoning",
                start: 0,
                end: 23
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="6">Reasoning</option>`;
            sections["18"] = {
                name: "General Awareness",
                start: 24,
                end: 48
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="18">General Awareness</option>`;
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 49,
                end: 73
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["7"] = {
                name: "English",
                start: 74,
                end: 98
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="7">English</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 99,
                end: 99
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "6",
                    question_en: "<p>1. Which of the following letter-clusters should replace # and % so that the pattern and relationship followed between the letter-cluster pair on the left side of :: is the same as that on the right side of :: ?<br># : NTR :: LRP : %</p>",
                    question_hi: "<p>1. निम्नलिखित में से किन अक्षर-समूहों द्वारा # और % को प्रतिस्थापित करने पर :: के बाईं ओर के अक्षर-समूह युग्म के बीच का पैटर्न और संबंध :: के दाईं ओर के अक्षर-समूह युग्म के बीच के पैटर्न और संबंध के समान होगा?<br># : NTR :: LRP : %</p>",
                    options_en: [
                        "<p># = IOM, % = QWU</p>",
                        "<p># = PJM, % = KLU</p>",
                        "<p># = ILK, % = QMJ</p>",
                        "<p># = IPU, % = NJH</p>"
                    ],
                    options_hi: [
                        "<p># = IOM, % = QWU</p>",
                        "<p># = PJM, % = KLU</p>",
                        "<p># = ILK, % = QMJ</p>",
                        "<p># = IPU, % = NJH</p>"
                    ],
                    solution_en: "<p>1.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741072354660.png\" alt=\"rId4\" width=\"265\" height=\"85\"></p>",
                    solution_hi: "<p>1.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741072354660.png\" alt=\"rId4\" width=\"265\" height=\"85\"></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "6",
                    question_en: "<p>2. Select the correct mirror image of the given figure when the mirror is placed at MN as shown below.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741072354788.png\" alt=\"rId5\"></p>",
                    question_hi: "<p>2. दी गई आकृति के उस सही दर्पण प्रतिबिंब को चुनिए, जो नीचे दर्शाए गए अनुसार दर्पण को MN पर रखने पर बनेगा।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741072354788.png\" alt=\"rId5\"></p>",
                    options_en: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741072354894.png\" alt=\"rId6\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741072355000.png\" alt=\"rId7\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741072355108.png\" alt=\"rId8\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741072355240.png\" alt=\"rId9\"></p>"
                    ],
                    options_hi: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741072354894.png\" alt=\"rId6\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741072355000.png\" alt=\"rId7\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741072355108.png\" alt=\"rId8\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741072355240.png\" alt=\"rId9\"></p>"
                    ],
                    solution_en: "<p>2.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741072355000.png\" alt=\"rId7\"></p>",
                    solution_hi: "<p>2.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741072355000.png\" alt=\"rId7\"></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "6",
                    question_en: "<p>3. Based on the alphabetical order, three of the following four letter-clusters are alike in a certain way and thus form a group. Which letter-cluster does not belong to that group?<br>(<strong>Note:</strong> The odd one out is not based on the number of consonants/vowels or their position in the letter cluster.)</p>",
                    question_hi: "<p>3. अंग्रेेजी वर्णमाला क्रम केआधार पर, निम्नलिखित चार अक्षर-समूहों में से तीन अक्षर-समूह एक निश्चित प्रकार से समान हैं और इस प्रकार एक समूह बनाते हैं। वह कौन-सा अक्षर-समूह है जो उस समूह से संबंधित नहीं है?<br>(<strong>ध्यान दें:</strong> असंगत अक्षर-समूह, उस अक्षर-समूह में व्यंजनों/स्वरों की संख्या या उनके स्थान पर आधारित नहीं है।)</p>",
                    options_en: [
                        "<p>TVY</p>",
                        "<p>HJL</p>",
                        "<p>PRT</p>",
                        "<p>RTV</p>"
                    ],
                    options_hi: [
                        "<p>TVY</p>",
                        "<p>HJL</p>",
                        "<p>PRT</p>",
                        "<p>RTV</p>"
                    ],
                    solution_en: "<p>3.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741072355359.png\" alt=\"rId10\" width=\"115\" height=\"68\">,&nbsp; <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741072355487.png\" alt=\"rId11\" width=\"119\" height=\"71\">, <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741072355650.png\" alt=\"rId12\" width=\"121\" height=\"71\"><br>But,</p>\n<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741072355812.png\" alt=\"rId13\" width=\"117\" height=\"68\"></p>",
                    solution_hi: "<p>3.(a)</p>\n<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741072355359.png\" alt=\"rId10\" width=\"115\" height=\"68\">,&nbsp; <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741072355487.png\" alt=\"rId11\" width=\"119\" height=\"71\">, <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741072355650.png\" alt=\"rId12\" width=\"121\" height=\"71\"><br>लेकिन,</p>\n<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741072355812.png\" alt=\"rId13\" width=\"117\" height=\"68\"></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "6",
                    question_en: "<p>4. In a code language, &lsquo;I want food&rsquo; is written as \' sickota dongre dujubu&rsquo;, &lsquo;I want drink&rsquo; is written as &lsquo;bluto sickota dujubu\', &lsquo;food and drink&rsquo; is written as &lsquo;dongre peppin bluto&rsquo;, then what is the code for the word \'peppin\' in this language?</p>",
                    question_hi: "<p>4. एक कूट भाषा में, &lsquo;I want food&rsquo; को \'sickota dongre dujubu&rsquo; के रूप में लिखा जाता है, &lsquo;I want drink&rsquo; को &lsquo;bluto sickota dujubu\' के रूप में लिखा जाता है, &lsquo;food and drink&rsquo; को &lsquo;dongre peppin bluto&rsquo; के रूप में लिखा जाता है, तो इसी कूट भाषा में शब्द \'peppin\' के लिए कूट क्या होगा?</p>",
                    options_en: [
                        "<p>and</p>",
                        "<p>drink</p>",
                        "<p>I</p>",
                        "<p>food</p>"
                    ],
                    options_hi: [
                        "<p>and</p>",
                        "<p>drink</p>",
                        "<p>I</p>",
                        "<p>food</p>"
                    ],
                    solution_en: "<p>4.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741072355960.png\" alt=\"rId14\" width=\"309\" height=\"107\"></p>",
                    solution_hi: "<p>4.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741072355960.png\" alt=\"rId14\" width=\"280\" height=\"97\"></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "6",
                    question_en: "<p>5. Based on the position in the English alphabetical order, three of the following letter-clusters are alike in some manner and one is different. Select the odd letter-cluster. <br><strong>Note:</strong> The odd one out is not based on the number of consonants/vowels or their position in the letter cluster.</p>",
                    question_hi: "<p>5. अंग्रेजी वर्णमाला क्रम में स्थिति के आधार पर, निम्नलिखित में से तीन अक्षर-समूह किसी न किसी रूप में संगत हैं और एक असंगत है। असंगत अक्षर-समूह का चयन करें। <br>(<strong>नोट: </strong>असंगत, अक्षर-समूह में व्यंजनों/स्वरों की संख्या या उनकी स्थिति पर आधारित नहीं है।).</p>",
                    options_en: [
                        "<p>SVXZ</p>",
                        "<p>EHJK</p>",
                        "<p>YBDE</p>",
                        "<p>TWYZ</p>"
                    ],
                    options_hi: [
                        "<p>SVXZ</p>",
                        "<p>EHJK</p>",
                        "<p>YBDE</p>",
                        "<p>TWYZ</p>"
                    ],
                    solution_en: "<p>5.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741072356079.png\" alt=\"rId15\" width=\"112\" height=\"51\">,<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741072356231.png\" alt=\"rId16\" width=\"114\" height=\"52\">, <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741072356396.png\" alt=\"rId17\" width=\"114\" height=\"52\"><br>but <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741072356584.png\" alt=\"rId18\" width=\"111\" height=\"51\"></p>",
                    solution_hi: "<p>5.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741072356079.png\" alt=\"rId15\" width=\"112\" height=\"51\">,<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741072356231.png\" alt=\"rId16\" width=\"114\" height=\"52\">, <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741072356396.png\" alt=\"rId17\" width=\"114\" height=\"52\"><br>लेकिन,<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741072356584.png\" alt=\"rId18\" width=\"111\" height=\"51\"><br><br><br></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "6",
                    question_en: "<p>6. Select the term from among the given options that can replace the question mark (?) in the&nbsp;following series.<br>TUR 71 UQT 60 VMV 49 ? XEZ 27</p>",
                    question_hi: "<p>6. दिए गए विकल्पों में से उस पद का चयन कीजिए, जो निम्नलिखित श्रृंखला में प्रश्नवाचक चिह्न (?) के स्थान पर आएगा।<br>TUR 71 UQT 60 VMV 49 ? XEZ 27</p>",
                    options_en: [
                        "<p>WIX 38</p>",
                        "<p>VJX 34</p>",
                        "<p>VIY 35</p>",
                        "<p>UWX 32</p>"
                    ],
                    options_hi: [
                        "<p>WIX 38</p>",
                        "<p>VJX 34</p>",
                        "<p>VIY 35</p>",
                        "<p>UWX 32</p>"
                    ],
                    solution_en: "<p>6.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741072356730.png\" alt=\"rId19\" width=\"310\" height=\"74\"></p>",
                    solution_hi: "<p>6.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741072356730.png\" alt=\"rId19\" width=\"310\" height=\"74\"></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "6",
                    question_en: "<p>7. AQLZ is related to SIDR in a certain way based on the English alphabetical order. In the same way, CPDV is related to UHVN. To which of the following is EBRY related, following the same logic?</p>",
                    question_hi: "<p>7. अंग्रेजी वर्णमाला क्रम के आधार पर AQLZ, SIDR से एक निश्चित प्रकार से संबंधित है। उसी प्रकार, CPDV, UHVN से संबंधित है। समान तर्क का अनुसरण करते हुए EBRY निम्नलिखित में से किससे संबंधित है?</p>",
                    options_en: [
                        "<p>WTJQ</p>",
                        "<p>WQTJ</p>",
                        "<p>WQJT</p>",
                        "<p>WTQJ</p>"
                    ],
                    options_hi: [
                        "<p>WTJQ</p>",
                        "<p>WQTJ</p>",
                        "<p>WQJT</p>",
                        "<p>WTQJ</p>"
                    ],
                    solution_en: "<p>7.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741072356961.png\" alt=\"rId20\" width=\"98\" height=\"78\">,&nbsp; <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741072357181.png\" alt=\"rId21\" width=\"100\" height=\"78\"> Similarly,&nbsp; <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741072357344.png\" alt=\"rId22\" width=\"101\" height=\"80\"></p>",
                    solution_hi: "<p>7.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741072356961.png\" alt=\"rId20\" width=\"98\" height=\"78\">,&nbsp; <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741072357181.png\" alt=\"rId21\" width=\"100\" height=\"78\"> इसी प्रकार, &nbsp;<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741072357344.png\" alt=\"rId22\" width=\"101\" height=\"80\"></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "6",
                    question_en: "<p>8. Three statements are followed by conclusions numbered I, II. You have to consider these statements to be true, even if they seem to be at variance with commonly known facts. Decide which of the given conclusions logically follow/s from the given statement.<br><strong>Statements :</strong><br>All journals are books.<br>Some books are fans.<br>All fans are wood.<br><strong>Conclusion (I) :</strong> Some journals are wood.<br><strong>Conclusion (II) : </strong>Some fans are books.</p>",
                    question_hi: "<p>8. तीन कथनों के बाद I, II क्रमांकित निष्कर्ष दिए गए हैं। आपको इन कथनों को सत्य मानना होगा, चाहे वे सामान्यतः ज्ञात तथ्यों से अलग प्रतीत होते हों। आपको निश्चय करना है की कौन सा/कौन से निष्कर्ष तार्किक रूप से दिए गए कथन/कथनों के अनुसार है/ हैं।<br><strong>कथन :</strong><br>सभी पत्रिकाएं, पुस्तकें हैं।<br>कुछ पुस्तकें, पंखे हैं।<br>सभी पंखे, लकड़ी हैं।<br><strong>निष्कर्ष (I) : </strong>कुछ पत्रिकाएं, लकड़ी हैं।<br><strong>निष्कर्ष (II) : </strong>कुछ पंखे, पुस्तकें हैं।</p>",
                    options_en: [
                        "<p>Only conclusion (I) follows.</p>",
                        "<p>Neither conclusion (I) nor (II) follows.</p>",
                        "<p>Both conclusions (I) and (II) follow.</p>",
                        "<p>Only conclusion (II) follows.</p>"
                    ],
                    options_hi: [
                        "<p>केवल निष्कर्ष (I) कथनों के अनुसार है।</p>",
                        "<p>न तो निष्कर्ष (I) और न ही (II) कथनों के अनुसार है।</p>",
                        "<p>दोनों निष्कर्ष (I) और (II) कथनों के अनुसार हैं।</p>",
                        "<p>केवल निष्कर्ष (II) कथनों के अनुसार है।</p>"
                    ],
                    solution_en: "<p>8.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741072357575.png\" alt=\"rId23\" width=\"253\" height=\"75\"><br>Only conclusion II follows.</p>",
                    solution_hi: "<p>8.(d) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741072357816.png\" alt=\"rId24\" width=\"236\" height=\"70\"><br>केवल निष्कर्ष II अनुसरण करता है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "6",
                    question_en: "<p>9. Select the set in which the numbers are related in the same way as are the numbers of the following sets. <br>(<strong>NOTE : </strong>Operations should be performed on the whole numbers, without breaking down the numbers into its constituent digits. E.g. 13 &ndash; Operations on 13 such as adding/deleting/multiplying etc. to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is not allowed.)<br>(10, 20, 4)<br>(15, 30, 6)</p>",
                    question_hi: "<p>9. उस समुचय का चयन कीजिए जिसमें संख्याएं ठीक उसी प्रकार संबंधित हैं जिस प्रकार दिए गए समुचयों की संख्याएं संबंधित हैं। <br>(<strong>ध्यान दें :</strong> संख्याओं को उनके घटक अंकों में अलग-अलग किए बिना पूर्ण संख्याओं पर संक्रियाएं की जानी चाहिए। उदाहरण के लिए संख्या 13 - संख्या 13 पर संक्रियाएं जैसे 13 को जोड़ना/घटाना/गुणा करना आदि किया जा सकता है। 13 को 1 और 3 में अलग-अलग करने की और फिर 1 और 3 पर गणितीय संक्रियाएं करने की अनुमति नहीं है।) <br>(10, 20, 4)<br>(15, 30, 6)</p>",
                    options_en: [
                        "<p>(20, 30, 8)</p>",
                        "<p>(20, 40, 8)</p>",
                        "<p>(10, 40, 8)</p>",
                        "<p>(20, 40, 6)</p>"
                    ],
                    options_hi: [
                        "<p>(20, 30, 8)</p>",
                        "<p>(20, 40, 8)</p>",
                        "<p>(10, 40, 8)</p>",
                        "<p>(20, 40, 6)</p>"
                    ],
                    solution_en: "<p>9.(b) <strong>Logic :-</strong> (1st number &times; 2) = (3rd number &times; 5) = 2nd number<br>(10, 20, 4) :- (10 &times; 2) = (4 &times; 5) = 20<br>(15, 30, 6) :- (15 &times; 2) = (6 &times; 5) = 30<br>Similarly,<br>(20, 40, 8) :- (20 &times; 2) = (8 &times; 5) = 40</p>",
                    solution_hi: "<p>9.(b)<strong> तर्क :</strong>- (पहली संख्या &times; 2) = (तीसरी संख्या &times; 5) = दूसरी संख्या<br>(10, 20, 4) :- (10 &times; 2) = (4 &times; 5) = 20<br>(15, 30, 6) :- (15 &times; 2) = (6 &times; 5) = 30<br>इसी प्रकार,<br>(20, 40, 8) :- (20 &times; 2) = (8 &times; 5) = 40</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "6",
                    question_en: "10. In a certain code language,<br />M & N means ‘M is the son of N’,<br />M @ N means ‘M is the brother of N’,<br />M $ N means ‘M is the father of N’,<br />M # N means ‘ M is the mother of N’.<br />Based on this, how is E related to A, if ‘A # B $ C @ D & E’?",
                    question_hi: "10. एक निश्चित कूट भाषा में,<br />M & N का अर्थ है \'M, N का बेटा है\',<br />M @ N का अर्थ है \'M, N का भाई है\',<br />M $ N का अर्थ है \'M, N के पिता है\',<br />M # N का अर्थ है \'M, N की माँ है\'।<br />इसके आधार पर, यदि \'A # B $ C @ D & E\' है, तो E का A से क्या संबंध है?",
                    options_en: [
                        " Daughter ",
                        " Son ",
                        " Daughter’s husband ",
                        " Son’s wife"
                    ],
                    options_hi: [
                        " बेटी",
                        " बेटा ",
                        " बेटी का पति ",
                        " बेटे की पत्नी "
                    ],
                    solution_en: "<p>10.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741072357993.png\" alt=\"rId25\" width=\"108\" height=\"169\"><br>E is the wife of A&rsquo;s son.</p>",
                    solution_hi: "<p>10.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741072357993.png\" alt=\"rId25\" width=\"108\" height=\"169\"><br>E, A के बेटे की पत्नी है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "6",
                    question_en: "<p>11. In a certain code language, &lsquo;ZCJV&rsquo; is coded as &lsquo;YBIU&rsquo; and &lsquo;GKMD&rsquo; is coded as &lsquo;FJLC&rsquo;. How will &lsquo;EYOR&rsquo; be coded in that language?</p>",
                    question_hi: "<p>11. एक निश्चित कूट भाषा में, \'ZCJV\' को \'YBIU\' के रूप में कूटबद्ध किया जाता है और \'GKMD\' को \'FJLC\' के रूप में कूटबद्ध किया जाता है। उसी भाषा में \'EYOR\' को किस प्रकार कूटबद्ध किया जाएगा?</p>",
                    options_en: [
                        "<p>QWMP</p>",
                        "<p>DXNQ</p>",
                        "<p>DWMP</p>",
                        "<p>CXNQ</p>"
                    ],
                    options_hi: [
                        "<p>QWMP</p>",
                        "<p>DXNQ</p>",
                        "<p>DWMP</p>",
                        "<p>CXNQ</p>"
                    ],
                    solution_en: "<p>11.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741072358127.png\" alt=\"rId26\" width=\"119\" height=\"95\">and&nbsp; <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741072358263.png\" alt=\"rId27\" width=\"124\" height=\"96\"> Similarly <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741072358436.png\" alt=\"rId28\" width=\"127\" height=\"96\"></p>",
                    solution_hi: "<p>11.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741072358127.png\" alt=\"rId26\" width=\"119\" height=\"95\">और&nbsp;&nbsp;<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741072358263.png\" alt=\"rId27\" width=\"124\" height=\"96\"> इसी प्रकार <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741072358436.png\" alt=\"rId28\" width=\"127\" height=\"96\"></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "6",
                    question_en: "<p>12. A dice has its faces marked by symbols &alpha;, &beta;, &theta;, <math display=\"inline\"><mi>&#947;</mi></math>, &pi; and &delta; . Two positions of the same dice are given below. Which face is opposite to face &beta; ?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741072358638.png\" alt=\"rId29\" width=\"133\" height=\"76\"></p>",
                    question_hi: "<p>12. एक पासे के फलकों को प्रतीक &alpha;, &beta;, &theta;, <math display=\"inline\"><mi>&#947;</mi></math> , &pi; और &delta; द्वारा चिह्नित किया गया है। एक ही पासे की दो स्थितियाँ नीचे दी गई हैं। कौन-सा फलक, फलक &beta; के विपरीत है ?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741072358638.png\" alt=\"rId29\" width=\"133\" height=\"76\"></p>",
                    options_en: [
                        "<p><math display=\"inline\"><mi>&#948;</mi></math></p>",
                        "<p>&alpha;</p>",
                        "<p>&theta;</p>",
                        "<p><math display=\"inline\"><mi>&#947;</mi></math></p>"
                    ],
                    options_hi: [
                        "<p><math display=\"inline\"><mi>&#948;</mi></math></p>",
                        "<p>&alpha;</p>",
                        "<p>&theta;</p>",
                        "<p><math display=\"inline\"><mi>&#947;</mi></math></p>"
                    ],
                    solution_en: "<p>12.(c) From both the dice the opposite face are <br><math display=\"inline\"><mi>&#947;</mi></math> &harr; &pi; , &alpha; &harr; &delta; , &beta; &harr; &theta;</p>",
                    solution_hi: "<p>12.(c) दोनों पासों के विपरीत फलक हैं<br><math display=\"inline\"><mi>&#947;</mi></math> &harr; &pi; , &alpha; &harr; &delta; , &beta; &harr; &theta;</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "6",
                    question_en: "<p>13. In a certain code language, &lsquo;RIVER&rsquo; is coded as &lsquo;SJWFS&rsquo; and &lsquo;SYSTEM&rsquo; is coded as &lsquo;TZTUFN&rsquo;. How is &lsquo;PACK&rsquo; coded in that language ?</p>",
                    question_hi: "<p>13. एक निश्चित कूट भाषा में, &lsquo;RIVER&rsquo; को &lsquo;SJWFS&rsquo; लिखा जाता है और &lsquo;SYSTEM&rsquo; को &lsquo;TZTUFN&rsquo; लिखा&nbsp;जाता है। उसी कूट भाषा में &lsquo;PACK&rsquo; को कैसे लिखा जाएगा ?</p>",
                    options_en: [
                        "<p>QBDL</p>",
                        "<p>RBDL</p>",
                        "<p>RCEK</p>",
                        "<p>QCEL</p>"
                    ],
                    options_hi: [
                        "<p>QBDL</p>",
                        "<p>RBDL</p>",
                        "<p>RCEK</p>",
                        "<p>QCEL</p>"
                    ],
                    solution_en: "<p>13.(a)<br><img src=\"data:image/png;base64,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\" width=\"146\" height=\"99\"> and &nbsp;<img src=\"data:image/png;base64,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\" width=\"157\" height=\"98\"><br>Similarly &nbsp;<img src=\"data:image/png;base64,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\" width=\"133\" height=\"108\"></p>",
                    solution_hi: "<p>13.(a)<br><img src=\"data:image/png;base64,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\" width=\"146\" height=\"99\">और &nbsp;<img src=\"data:image/png;base64,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\" width=\"157\" height=\"98\"><br>इसी प्रकार, &nbsp;<img src=\"data:image/png;base64,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\" width=\"133\" height=\"108\"></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "6",
                    question_en: "<p>14. Six students - A, B, C, D, E and F - are sitting around a circular table, facing the centre.<br>A is the immediate neighbour of both E and F.<br>B is sitting second to the right of E.<br>D is sitting third to the left of F.<br>C is the immediate neighbour of both B and F.<br>Who among the following is the immediate neighbour of both A and D ?</p>",
                    question_hi: "<p>14. छह विद्यार्थी - A, B, C, D, E और F - एक गोल मेज के परित: केंद्र की ओर अभिमुख होकर बैठे हैं।<br>E और F दोनों का निकटतम पड़ोसी A है।<br>B, E के दायें से दूसरे स्थान पर बैठा है।<br>D, F के बायें से तीसरे स्थान पर बैठा है।<br>B और F दोनों का निकटतम पड़ोसी C है।<br>निम्नलिखित में से कौन A और D दोनों का निकटतम पड़ोसी है?</p>",
                    options_en: [
                        "<p>F</p>",
                        "<p>C</p>",
                        "<p>E</p>",
                        "<p>B</p>"
                    ],
                    options_hi: [
                        "<p>F</p>",
                        "<p>C</p>",
                        "<p>E</p>",
                        "<p>B</p>"
                    ],
                    solution_en: "<p>14.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741072359309.png\" alt=\"rId33\" width=\"187\" height=\"139\"></p>",
                    solution_hi: "<p>14.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741072359309.png\" alt=\"rId33\" width=\"187\" height=\"139\"></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "6",
                    question_en: "<p>15. In a certain code language, &lsquo;HUNT&rsquo; is coded as &lsquo;9876&rsquo; and &lsquo;USER&rsquo; is coded as &lsquo;4361&rsquo;. What is the code for &lsquo;U&rsquo; in that language?</p>",
                    question_hi: "<p>15. किसी निश्चित कूट भाषा में, \'HUNT\' को \'9876\' के रूप में कूटबद्ध किया गया है और \'USER\' को \'4361\' के रूप में कूटबद्ध किया गया है। उस भाषा में \'U\' के लिए कूट क्या है?</p>",
                    options_en: [
                        "<p>6</p>",
                        "<p>4</p>",
                        "<p>8</p>",
                        "<p>1</p>"
                    ],
                    options_hi: [
                        "<p>6</p>",
                        "<p>4</p>",
                        "<p>8</p>",
                        "<p>1</p>"
                    ],
                    solution_en: "<p>15.(a)<br>H U N T &rarr;&nbsp;9 8 7 6 &hellip; (i)<br>U S E R &rarr; 4 3 6 1 &hellip; (ii)<br>from (i) and (ii) &lsquo;U&rsquo; and &lsquo;6&rsquo; are common.<br>Hence, the code for &lsquo;U&rsquo; is &lsquo;6&rsquo;.</p>",
                    solution_hi: "<p>15.(a)<br>H U N T &rarr; 9 8 7 6 &hellip; (i)<br>U S E R &rarr; 4 3 6 1 &hellip; (ii)<br>(i) और (ii) से \'U\' और \'6\' उभयनिष्ठ हैं।<br>अतः, \'U\' के लिए कूट \'6\' है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "16",
                    section: "6",
                    question_en: "<p>16. If &lsquo;A&rsquo; stands for &lsquo;&divide;&rsquo;, &lsquo;B&rsquo; stands for &lsquo;&times;&rsquo;, &lsquo;C&rsquo; stands for &lsquo;+&rsquo; and &lsquo;D&rsquo; stands for &lsquo;&ndash;&rsquo;, then the resultant of which of the following will be 59?</p>",
                    question_hi: "<p>16. यदि &lsquo;A&rsquo; का अर्थ &lsquo;&divide;&rsquo; है, &lsquo;B&rsquo; का अर्थ &lsquo;&times;&rsquo; है, &lsquo;C&rsquo; का अर्थ &lsquo;+&rsquo; है और &lsquo;D&rsquo; का अर्थ &lsquo;&ndash;&rsquo; है, तो निम्नलिखित में से किसका परिणाम 59 होगा?</p>",
                    options_en: [
                        "<p>22 B 6 D 190 A 2 C 22</p>",
                        "<p>22 B 6 C 190 A 2 D 22</p>",
                        "<p>22 B 6 D 190 C 2 A 22</p>",
                        "<p>22 A 6 D 190 B 2 C 22</p>"
                    ],
                    options_hi: [
                        "<p>22 B 6 D 190 A 2 C 22</p>",
                        "<p>22 B 6 C 190 A 2 D 22</p>",
                        "<p>22 B 6 D 190 C 2 A 22</p>",
                        "<p>22 A 6 D 190 B 2 C 22</p>"
                    ],
                    solution_en: "<p>16.(a) After checking all the options one by one, only option (a) satisfies.<br>22 B 6 D 190 A 2 C 22<br>After interchanging letter with sign we get<br>22 &times; 6 - 190 &divide; 2 + 22<br>132 - 95 + 22 = 59</p>",
                    solution_hi: "<p>16.(a) सभी विकल्पों को एक-एक करके जांचने के बाद, केवल विकल्प (a) ही संतुष्ट करता है।<br>22 B 6 D 190 A 2 C 22<br>अक्षर को चिन्ह से बदलने पर हमें प्राप्त होता है<br>22 &times; 6 - 190 &divide; 2 + 22<br>132 - 95 + 22 = 59</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "17",
                    section: "6",
                    question_en: "<p>17. Each of the letters in the word FRANCHISE are arranged from left to right in&nbsp;alphabetical order to form a new word. How many letters are there in the English&nbsp;alphabetical series between the alphabet which is fifth from the left and the one which is third from the right in the newly formed word?</p>",
                    question_hi: "<p>17. FRANCHISE शब्द के प्रत्येक अक्षर को एक नया शब्द बनाने के लिए बाएं से दाएं वर्णमाला क्रम में&nbsp;व्यवस्थित किया गया है। नवनिर्मित शब्द में, बाएं से पांचवे स्थान वाले अक्षर और दाएं से तीसरे स्थान वाले अक्षर के बीच अंग्रेजी वर्णमाला श्रृंखला में कितने अक्षर हैं?</p>",
                    options_en: [
                        "<p>Seven</p>",
                        "<p>Six</p>",
                        "<p>Five</p>",
                        "<p>Four</p>"
                    ],
                    options_hi: [
                        "<p>सात</p>",
                        "<p>छह</p>",
                        "<p>पाँच</p>",
                        "<p>चार</p>"
                    ],
                    solution_en: "<p>17.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741072359564.png\" alt=\"rId34\" width=\"207\" height=\"121\"><br>Hence, there are 5 letters in the English alphabetical series between &lsquo;H&rsquo; and &lsquo;N&rsquo;.</p>",
                    solution_hi: "<p>17.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741072359738.png\" alt=\"rId35\" width=\"285\" height=\"140\"><br>अतः, अंग्रेजी वर्णमाला श्रृंखला में \'H\' और \'N\' के बीच 5 अक्षर हैं।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "18",
                    section: "6",
                    question_en: "<p>18. 99 is related to 77.3 following a certain logic. Following the same logic, 84.4 is related&nbsp;to 62.7. To which of the following is 72.6 related, following the same logic?</p>",
                    question_hi: "<p>18. एक निश्चित तर्क के अनुसार 99 का संबंध 77.3 से है। उसी तर्क का अनुसरण करते हुए 84.4 का संबंध 62.7 से है। उसी तर्क का अनुसरण करते हुए 72.6 का संबंध निम्नलिखित में से किससे है?</p>",
                    options_en: [
                        "<p>57.3</p>",
                        "<p>50.9</p>",
                        "<p>52.7</p>",
                        "<p>54.2</p>"
                    ],
                    options_hi: [
                        "<p>57.3</p>",
                        "<p>50.9</p>",
                        "<p>52.7</p>",
                        "<p>54.2</p>"
                    ],
                    solution_en: "<p>18.(b) <strong>Logic :-</strong> 1st number - 21.7 = 2nd number <br>(99 , 77.3) :- (99 - 21.7) = 77.3<br>(84.4 , 62.7) :- (84.4 - 21.7) = 62.7<br>Similarly,<br>(72.6, ?) :- (72.6 - 21.7) = 50.9</p>",
                    solution_hi: "<p>18.(b) <strong>तर्क :- </strong>(पहली संख्या - 21.7) =दूसरी संख्या <br>(99 , 77.3) :- (99 - 21.7) = 77.3<br>(84.4 , 62.7) :- (84.4 - 21.7) = 62.7<br>इसी प्रकार,<br>(72.6, ?) :- (72.6 - 21.7) = 50.9</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "19",
                    section: "6",
                    question_en: "<p>19. If 15 January 2019 was a Tuesday, then what day of the week was it on 29 January 2020?</p>",
                    question_hi: "<p>19. यदि 15 जनवरी 2019 को मंगलवार था, तो 29 जनवरी 2020 को सप्ताह का कौन सा दिन था?</p>",
                    options_en: [
                        "<p>Tuesday</p>",
                        "<p>Thursday</p>",
                        "<p>Wednesday</p>",
                        "<p>Friday</p>"
                    ],
                    options_hi: [
                        "<p>मंगलवार</p>",
                        "<p>गुरूवार</p>",
                        "<p>बुधवार</p>",
                        "<p>शुक्रवार</p>"
                    ],
                    solution_en: "<p>19.(c) 15 January 2019 is Tuesday. On going to 2020 the number of odd day = 1.<br>We have reached till 15 January 2020, we have to reach till 29 January, the number of days between = 14. Total number of odd days = 14 + 1 = 15. On dividing 15 by 7 we get remainder = 1 . Tuesday + 1 = Wednesday.</p>",
                    solution_hi: "<p>19.(c) 15 जनवरी 2019 को मंगलवार है. 2020 में जाने पर विषम दिन की संख्या = 1.<br>हम 15 जनवरी 2020 तक पहुँच चुके हैं, हमें 29 जनवरी तक पहुँचना है, बीच के दिनों की संख्या = 14. विषम दिनों की कुल संख्या = 14 + 1 = 15. 15 को 7 से विभाजित करने पर शेषफल = 1 प्राप्त होता है। मंगलवार + 1 = बुधवार.</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "20",
                    section: "6",
                    question_en: "<p>20. Select the figure from among the given options that can replace the question mark (?) in the following series and complete the pattern.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741072359847.png\" alt=\"rId36\" width=\"346\" height=\"71\"></p>",
                    question_hi: "<p>20. दिए गए विकल्पों में से उस आकृति को चुनिए जो निम्नलिखित श्रृंखला में प्रश्न चिह्न (?) के स्थान पर आ सकती है और पैटर्न को पूरा कर सकती है।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741072359847.png\" alt=\"rId36\" width=\"346\" height=\"71\"></p>",
                    options_en: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741072359949.png\" alt=\"rId37\" width=\"96\" height=\"94\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741072360108.png\" alt=\"rId38\" width=\"96\" height=\"94\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741072360240.png\" alt=\"rId39\" width=\"96\" height=\"93\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741072360398.png\" alt=\"rId40\" width=\"96\" height=\"94\"></p>"
                    ],
                    options_hi: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741072359949.png\" alt=\"rId37\" width=\"95\" height=\"93\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741072360108.png\" alt=\"rId38\" width=\"96\" height=\"94\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741072360240.png\" alt=\"rId39\" width=\"96\" height=\"93\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741072360398.png\" alt=\"rId40\" width=\"96\" height=\"94\"></p>"
                    ],
                    solution_en: "<p>20.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741072360240.png\" alt=\"rId39\" width=\"97\" height=\"94\"></p>",
                    solution_hi: "<p>20.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741072360240.png\" alt=\"rId39\" width=\"96\" height=\"93\"></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "21",
                    section: "6",
                    question_en: "<p>21. Select the correct option that when filled in the blanks in the same sequence will make the series logically complete. <br>TRIC_TRI_KT_IWK_R_TK</p>",
                    question_hi: "<p>21. उस सही विकल्प का चयन कीजिए जिसे दी गई श्रृंखला के रिक्त स्थानों में उसी क्रम में रखने पर श्रृंखला तार्किक रूप से पूरी हो जाएगी। <br>TRIC_TRI_KT_IWK_R_TK</p>",
                    options_en: [
                        "<p>KATIR</p>",
                        "<p>KBTIR</p>",
                        "<p>KIRTA</p>",
                        "<p>KZRTI</p>"
                    ],
                    options_hi: [
                        "<p>KATIR</p>",
                        "<p>KBTIR</p>",
                        "<p>KIRTA</p>",
                        "<p>KZRTI</p>"
                    ],
                    solution_en: "<p>21.(d) In this series all the letters will remain the same but the place value of the fourth letter will decrease by 3.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741072360566.png\" alt=\"rId41\" width=\"295\" height=\"70\"></p>",
                    solution_hi: "<p>21.(d) इस श्रृंखला में सभी अक्षर समान रहेंगे लेकिन चौथे अक्षर के स्थानीय मान में 3 की कमी होगी। <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741072360566.png\" alt=\"rId41\" width=\"295\" height=\"70\"></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "22",
                    section: "6",
                    question_en: "<p>22. What is the maximum number of rectangles in the given figure?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741072360661.png\" alt=\"rId42\" width=\"173\" height=\"108\"></p>",
                    question_hi: "<p>22. दी गई आकृति में आयतों की अधिकतम संख्या कितनी है?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741072360661.png\" alt=\"rId42\" width=\"173\" height=\"108\"></p>",
                    options_en: [
                        "<p>12</p>",
                        "<p>9</p>",
                        "<p>10</p>",
                        "<p>8</p>"
                    ],
                    options_hi: [
                        "<p>12</p>",
                        "<p>9</p>",
                        "<p>10</p>",
                        "<p>8</p>"
                    ],
                    solution_en: "<p>22.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741072360802.png\" alt=\"rId43\" width=\"240\" height=\"176\"><br>There are 9 rectangle <br>ABDC, EFGD , FIHG , EIHD, JKIF, JKHG, EIHD , ILMH, ELMD</p>",
                    solution_hi: "<p>22.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741072360802.png\" alt=\"rId43\" width=\"240\" height=\"176\"><br>यहाँ 9 आयत हैं<br>ABDC, EFGD , FIHG , EIHD, JKIF, JKHG, EIHD , ILMH, ELMD</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "23",
                    section: "6",
                    question_en: "<p>23. Select the number from among the given options that can replace the question mark (?) in the following series.<br>3 , 23 ,? ,1143 ,8003</p>",
                    question_hi: "<p>23. दिए गए विकल्पों में से उस संख्या का चयन कीजिए जो निम्नलिखित श्रृंखला में प्रश्न चिह्न (?) प्रतिस्थापित कर सकती है।<br>3 , 23 , ? ,1143 ,8003</p>",
                    options_en: [
                        "<p>166</p>",
                        "<p>179</p>",
                        "<p>174</p>",
                        "<p>163</p>"
                    ],
                    options_hi: [
                        "<p>166</p>",
                        "<p>179</p>",
                        "<p>174</p>",
                        "<p>163</p>"
                    ],
                    solution_en: "<p>23.(d) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741072361000.png\" alt=\"rId44\" width=\"285\" height=\"61\"></p>",
                    solution_hi: "<p>23.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741072361000.png\" alt=\"rId44\" width=\"285\" height=\"61\"></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "24",
                    section: "6",
                    question_en: "<p>24. Select the option that is embedded in the given figure (rotation is NOT allowed).<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741072361115.png\" alt=\"rId45\" width=\"101\" height=\"104\"></p>",
                    question_hi: "<p>24. उस विकल्प आकृति का चयन कीजिए जो दी गई आकृति में निहित है (घूर्णन की अनुमति नहीं है)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741072361115.png\" alt=\"rId45\" width=\"101\" height=\"104\"></p>",
                    options_en: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741072361265.png\" alt=\"rId46\" width=\"101\" height=\"82\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741072361368.png\" alt=\"rId47\" width=\"100\" height=\"96\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741072361488.png\" alt=\"rId48\" width=\"100\" height=\"94\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741072361579.png\" alt=\"rId49\" width=\"100\" height=\"93\"></p>"
                    ],
                    options_hi: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741072361265.png\" alt=\"rId46\" width=\"103\" height=\"84\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741072361368.png\" alt=\"rId47\" width=\"101\" height=\"97\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741072361488.png\" alt=\"rId48\" width=\"103\" height=\"97\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741072361579.png\" alt=\"rId49\" width=\"101\" height=\"94\"></p>"
                    ],
                    solution_en: "<p>24.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741072361696.png\" alt=\"rId50\" width=\"116\" height=\"160\"></p>",
                    solution_hi: "<p>24.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741072361696.png\" alt=\"rId50\" width=\"116\" height=\"160\"></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "25",
                    section: "18",
                    question_en: "<p>25. In the following number-pairs, the second number is obtained by applying certain mathematical operations to the first number. In three of the four pairs, the same pattern is applied and hence they form a group. Select the number-pair that DOES NOT belong to this group. <br>(<strong>NOTE :</strong> Operations should be performed on the whole numbers, without breaking down the numbers into their constituent digits. E.g. 13 &ndash; Operations on 13 such as adding/subtracting/multiplying to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is not allowed.)</p>",
                    question_hi: "<p>25. निम्नलिखित संख्या-युग्मों में, पहली संख्या पर कुछ गणितीय संक्रियाएँ लागू करके दूसरी संख्या प्राप्त की जाती है। चार में से तीन युग्मों में, समान स्वरूप लागू किया गया है और इसलिए वे एक समूह बनाते हैं। उस संख्या-युग्म का चयन कीजिए जो इस समूह से संबंधित नहीं है। <br>(<strong>नोट: </strong>संख्याओं को उनके घटक अंकों में विभक्त किए बिना पूर्ण संख्याओं पर संक्रियााऍं की जानी चाहिए। उदाहरण के लिए, 13 को लीजिए &ndash; 13 पर संक्रियाऍं, जैसे कि 13 में जोड़ना/घटाना/गुणा करना आदि, की जा सकती हैं। 13 को 1 और 3 में विभक्त करने और फिर 1 और 3 पर गणितीय संक्रियाऍं करने की अनुमति नहीं है।)</p>",
                    options_en: [
                        "<p>335 - 119</p>",
                        "<p>358 - 152</p>",
                        "<p>317 - 101</p>",
                        "<p>308 - 92</p>"
                    ],
                    options_hi: [
                        "<p>335 - 119</p>",
                        "<p>358 - 152</p>",
                        "<p>317 - 101</p>",
                        "<p>308 - 92</p>"
                    ],
                    solution_en: "<p>25.(b) <strong>Logic:-</strong> (1st number - 2nd number) = 216<br>(335 , 119) :- (335 - 119) = 216<br>(317 , 101) :- (317 - 101) = 216<br>(308 , 92) :- (308 - 92) = 216<br>But,<br>(358 , 152) :- (358 - 152) = 206</p>",
                    solution_hi: "<p>25.(b) <strong>तर्क:-</strong> (पहली संख्या - दूसरी संख्या) = 216<br>(335 , 119) :- (335 - 119) = 216<br>(317 , 101) :- (317 - 101) = 216<br>(308 , 92) :- (308 - 92) = 216<br>लेकिन,<br>(358 , 152) :- (358 - 152) = 206</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "26",
                    section: "18",
                    question_en: "<p>26. In which year did Indian Railways start its first computerized reservation in New Delhi?</p>",
                    question_hi: "<p>26. भारतीय रेलवे ने नई दिल्ली में अपनी पहली कम्प्यूटरीकृत यात्री आरक्षण प्रणाली किस वर्ष शुरू की थी?</p>",
                    options_en: [
                        "<p>1992</p>",
                        "<p>1996</p>",
                        "<p>2001</p>",
                        "<p>1986</p>"
                    ],
                    options_hi: [
                        "<p>1992</p>",
                        "<p>1996</p>",
                        "<p>2001</p>",
                        "<p>1986</p>"
                    ],
                    solution_en: "<p>26.(d) <strong>1986.</strong> Facts about the Indian Railways: First railway workshop established at Jamalpur in Bihar. Maximum number of routes emerges from Mathura Junction. Pir Pranjal is India&rsquo;s longest rail tunnel. Howrah Junction is the busiest railway station.</p>",
                    solution_hi: "<p>26.(d) <strong>1986.</strong> भारतीय रेलवे के बारे में तथ्य : बिहार के जमालपुर में पहली रेलवे कार्यशाला स्थापित की गई। सबसे अधिक रूट मथुरा जंक्शन से निकलते हैं। पीर पंजाल भारत की सबसे लंबी रेल सुरंग है। हावड़ा जंक्शन सबसे व्यस्त रेलवे स्टेशन है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "27",
                    section: "18",
                    question_en: "<p>27. In a 100 m Relay Race, how many athletes have to complete a race?</p>",
                    question_hi: "<p>27. 100 m रिले रेस में कितने एथलीटों को एक रेस पूरी करनी होती है?</p>",
                    options_en: [
                        "<p>2</p>",
                        "<p>3</p>",
                        "<p>4</p>",
                        "<p>6</p>"
                    ],
                    options_hi: [
                        "<p>2</p>",
                        "<p>3</p>",
                        "<p>4</p>",
                        "<p>6</p>"
                    ],
                    solution_en: "<p>27.(c) <strong>4.</strong> Relay Race: Team of four athletes run equal distances, passing a baton. Last runner is the \'anchor\'. The two relay races in the Olympics are the 4x100m and 4x400m for both men and women and mixed.</p>",
                    solution_hi: "<p>27.(c) 4. रिले रेस: चार एथलीटों की टीम एक बैटन को पास करते हुए समान दूरी तक दौड़ती है। अंतिम धावक \'एंकर\' है। ओलंपिक में दो रिले दौड़ पुरुषों और महिलाओं दोनों के लिए 4x100 मीटर और 4x400 मीटर और मिश्रित हैं।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "28",
                    section: "18",
                    question_en: "<p>28. The Ramakrishna Mission was founded by _________ to regenerate Indian society by promoting the Vedanta philosophy.</p>",
                    question_hi: "<p>28. वेदांत दर्शन को बढ़ावा देकर भारतीय समाज को पुनर्जीवित करने के लिए रामकृष्ण मिशन की स्थापना ____________ द्वारा की गई थी।</p>",
                    options_en: [
                        "<p>Swami Vivekananda</p>",
                        "<p>Swami Dayananda Saraswati</p>",
                        "<p>Debendranath Tagore</p>",
                        "<p>Raja Ram Mohan Roy</p>"
                    ],
                    options_hi: [
                        "<p>स्वामी विवेकानंद</p>",
                        "<p>स्वामी दयानंद सरस्वती</p>",
                        "<p>देबेंद्रनाथ टैगोर</p>",
                        "<p>राजा राम मोहन राय</p>"
                    ],
                    solution_en: "<p>28.(a) <strong>Swami Vivekananda </strong>(Real name - Narendranath Dutt). He founded the Ramakrishna mission in 1897. Ramkrishna Paramhamsa was the guru of Vivekananda. Swami Dayananda Saraswati: Founder of Arya Samaj in 1875. Debendranath Tagore: Founder of Tattvabodhini Sabha in 1839. Raja Rammohan Roy: Founder of the Brahmo Sabha in 1828.</p>",
                    solution_hi: "<p>28.(a) <strong>स्वामी विवेकानन्द</strong> (असली नाम - नरेन्द्रनाथ दत्त)। उन्होंने 1897 में रामकृष्ण मिशन की स्थापना की। रामकृष्ण परमहंस विवेकानन्द के गुरु थे। स्वामी दयानंद सरस्वती: 1875 में आर्य समाज के संस्थापक। देबेंद्रनाथ टैगोर: 1839 में तत्त्वबोधिनी सभा के संस्थापक। राजा राममोहन राय: 1828 में ब्रह्म सभा के संस्थापक।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "29",
                    section: "18",
                    question_en: "<p>29. In which year did Robert Brown observe the zigzag movement of colloidal particles in&nbsp;solution?</p>",
                    question_hi: "<p>29. रॉबर्ट ब्राउन ने विलयन में कोलॉइडी कणों की टेढ़ि-मेढ़ी (zigzag) गति का निरीक्षण किस वर्ष किया था?</p>",
                    options_en: [
                        "<p>1829</p>",
                        "<p>1827</p>",
                        "<p>1828</p>",
                        "<p>1826</p>"
                    ],
                    options_hi: [
                        "<p>1829</p>",
                        "<p>1827</p>",
                        "<p>1828</p>",
                        "<p>1826</p>"
                    ],
                    solution_en: "<p>29.(b)<strong> 1827.</strong> Robert Brown observed the zigzag movement of colloidal particles, known as Brownian motion. This phenomenon involves the random movement of particles suspended in a fluid, caused by collisions with fast atoms or molecules in the gas or liquid. Brownian motion provided important evidence for the kinetic theory of heat and the existence of atoms and molecules.</p>",
                    solution_hi: "<p>29.(b) 1827. रॉबर्ट ब्राउन ने कोलाइडी कणों की टेढ़ी-मेढ़ी गति देखी, जिसे ब्राउनी गति के नाम से जाना जाता है। इस घटना में तरल पदार्थ में निलंबित कणों की अनियमित गति शामिल होती है, जो गैस या तरल में परमाणुओं या अणुओं के साथ तीव्र टकराव के कारण होती है। ब्राउनी गति ने ऊष्मा के गतिज सिद्धांत तथा परमाणुओं और अणुओं के अस्तित्व के लिए महत्वपूर्ण साक्ष्य प्रदान किया।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "30",
                    section: "18",
                    question_en: "<p>30. The Manipuri dance of north-eastern India is grown out of the ritual worship of Lord _________ .</p>",
                    question_hi: "<p>30. उत्तर-पूर्वी भारत का मणिपुरी नृत्य भगवान _________की अनुष्ठानिक पूजा से विकसित हुआ है।</p>",
                    options_en: [
                        "<p>Vishnu</p>",
                        "<p>Krishna</p>",
                        "<p>Ganesha</p>",
                        "<p>Rama</p>"
                    ],
                    options_hi: [
                        "<p>विष्णु</p>",
                        "<p>कृष्ण</p>",
                        "<p>गणेश</p>",
                        "<p>राम</p>"
                    ],
                    solution_en: "<p>30.(b) <strong>Krishna.</strong> Manipuri dance incorporates both the Tandava and Lasya. Dances and related God: Bharatanatyam (Lord Shiva), Mohiniattyam (Lord Vishnu).</p>",
                    solution_hi: "<p>30.(b) <strong>कृष्ण।</strong> मणिपुरी नृत्य में तांडव और लास्य दोनों का समावेश होता है। नृत्य और संबंधित भगवान: भरतनाट्यम (भगवान शिव), मोहिनीअट्टयम (भगवान विष्णु)।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "31",
                    section: "18",
                    question_en: "<p>31. When is World Population Day observed ?</p>",
                    question_hi: "<p>31. विश्व जनसंख्या दिवस कब मनाया जाता है?</p>",
                    options_en: [
                        "<p>10th July</p>",
                        "<p>12th July</p>",
                        "<p>11th July</p>",
                        "<p>9th July</p>"
                    ],
                    options_hi: [
                        "<p>10 जुलाई</p>",
                        "<p>12 जुलाई</p>",
                        "<p>11 जुलाई</p>",
                        "<p>9 जुलाई</p>"
                    ],
                    solution_en: "<p>31.(c) <strong>11th July.</strong> World Population Day, established by the United Nations in 1989, serves as an important platform to raise awareness about global population issues. The theme for 2024, \"Leave no one behind, count everyone&rdquo;.</p>",
                    solution_hi: "<p>31.(c) <strong>11 जुलाई।</strong> संयुक्त राष्ट्र द्वारा 1989 में स्थापित विश्व जनसंख्या दिवस वैश्विक जनसंख्या मुद्दों के बारे में जागरूकता बढ़ाने के लिए एक महत्वपूर्ण मंच के रूप में कार्य करता है। 2024 का थीम है, \"किसी को पीछे न छोड़ें, सभी की गिनती करें\"।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "32",
                    section: "18",
                    question_en: "<p>32. Which of the following was first introduced in bacterial cells by the American molecular biologist Joshua Lederberg in 1952?</p>",
                    question_hi: "<p>32. 1952 में अमेरिकी आण्विक जीवविज्ञानी जोशुआ लेडरबर्ग (Joshua Lederberg) द्वारा जीवाणु कोशिकाओं में निम्नलिखित में से क्या पहली बार पाया गया था जिसे उन्होंने प्लाज्मिड नाम दिया था?</p>",
                    options_en: [
                        "<p>Pili</p>",
                        "<p>Capsule</p>",
                        "<p>Plasmid</p>",
                        "<p>Cytoplasm</p>"
                    ],
                    options_hi: [
                        "<p>पाइली (Pili)</p>",
                        "<p>संपुटिका (Capsule)</p>",
                        "<p>प्लाज्मिड (Plasmid)</p>",
                        "<p>कोशिका द्रव्य (Cytoplasm)</p>"
                    ],
                    solution_en: "<p>32.(c) <strong>Plasmid. </strong>They are separate DNA molecules in bacteria, capable of independent replication from chromosomal DNA. They are double-stranded and circular, occurring naturally in bacteria and sometimes in eukaryotic organisms. Plasmid sizes range from 1 to over 1,000 kilobase pairs (kbp).</p>",
                    solution_hi: "<p>32.(c) प्लाज्मिड । ये बैक्टीरिया में अलग-अलग DNA अणु होते हैं, जो गुणसूत्रीय DNA से स्वतंत्र प्रतिकृति बनाने में सक्षम होते हैं। ये डबल-स्ट्रैंडेड और गोलाकार होते हैं, जो प्राकृतिक रूप से बैक्टीरिया और कभी-कभी यूकेरियोटिक जीवों में पाए जाते हैं। प्लास्मिड का आकार 1 से लेकर 1,000 किलोबेस जोड़े (kbp) तक होता है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "33",
                    section: "18",
                    question_en: "<p>33. Which of the following pairs is correctly matched?</p>",
                    question_hi: "<p>33. निम्नलिखित में से कौन-सा युग्म सुमेलित है ?</p>",
                    options_en: [
                        "<p>Third man &ndash; Cricket</p>",
                        "<p>Header &ndash; Hockey</p>",
                        "<p>Dribble &ndash; Fencing</p>",
                        "<p>Bicycle kick &ndash; Basketball</p>"
                    ],
                    options_hi: [
                        "<p>थर्ड मैन - क्रिकेट</p>",
                        "<p>हेडर - हॉकी</p>",
                        "<p>ड्रिबल - फेंसिंग</p>",
                        "<p>बाइसकिल किक - बास्केटबॉल</p>"
                    ],
                    solution_en: "<p>33.(a) <strong>Third man &ndash; Cricket. </strong>Sports Terminologies: Cricket - Belter, Dolly, Duck, Leg-Before Wicket (LBW), Leg-bye, Maiden. Field Hockey - Flick, Jink, Long corner, Scoop, Sweep hit. Basketball - Charging, Dunk, Hook shot, Layup, Point guard, Sixth man.</p>",
                    solution_hi: "<p>33.(a) <strong>तीसरा आदमी - क्रिकेट। </strong>खेल शब्दावली: क्रिकेट - बेल्टर, डॉली, डक, लेग-बिफोर विकेट (LBW), लेग-बाय, मेडेन। फील्ड हॉकी - फ्लिक, जिंक, लॉन्ग कॉर्नर, स्कूप, स्वीप हिट। बास्केटबॉल - चार्जिंग, डंक, हुक शॉट, लेअप, प्वाइंट गार्ड, सिक्स्थ मैन।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "34",
                    section: "18",
                    question_en: "<p>34. Which of the following parts of the Indian Constitution is related to amendment of the Constitution?</p>",
                    question_hi: "<p>34. भारतीय संविधान के निम्नलिखित में से किस भाग का संबंध संविधान के संशोधन से है?</p>",
                    options_en: [
                        "<p>Part XIX</p>",
                        "<p>Part XXII</p>",
                        "<p>Part XXI</p>",
                        "<p>Part XX</p>"
                    ],
                    options_hi: [
                        "<p>भाग XIX</p>",
                        "<p>भाग XXII</p>",
                        "<p>भाग XXI</p>",
                        "<p>भाग XX</p>"
                    ],
                    solution_en: "<p>34.(d) <strong>Part XX.</strong> Article 368 in Part XX of the Constitution deals with the power of parliament to amend the constitution and its procedures. It provides for two types of amendments: By a special majority of Parliament, and By a special majority of the Parliament along with the ratification of half of the states legislatures by a simple majority. In the Kesavananda Bharati case (1973), the Supreme Court ruled that the Parliament cannot amend those provisions which form the &lsquo;basic structure&rsquo; of the Constitution.</p>",
                    solution_hi: "<p>34.(d) <strong>भाग XX . संविधा</strong>न के भाग XX में अनुच्छेद 368, संविधान और इसकी प्रक्रियाओं में संशोधन करने की संसद की शक्ति से संबंधित है। इसमें दो प्रकार के संशोधनों का प्रावधान है: संसद के विशेष बहुमत द्वारा, और संसद के विशेष बहुमत के साथ-साथ आधे राज्य विधानमंडलों के साधारण बहुमत के अनुसमर्थन द्वारा। केशवानंद भारती मामले (1973) में, सर्वोच्च न्यायालय ने फैसला सुनाया कि संसद उन प्रावधानों में संशोधन नहीं कर सकती जो संविधान के \'मूल संरचना\' का निर्माण करते हैं।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "35",
                    section: "18",
                    question_en: "<p>35. The Dronacharya award is given to _________.</p>",
                    question_hi: "<p>35. द्रोणाचार्य पुरस्कार किसे प्रदान किया जाता है?</p>",
                    options_en: [
                        "<p>best coach</p>",
                        "<p>best player</p>",
                        "<p>best teacher</p>",
                        "<p>best soldier</p>"
                    ],
                    options_hi: [
                        "<p>सर्वश्रेष्ठ कोच</p>",
                        "<p>सर्वश्रेष्ठ खिलाड़ी</p>",
                        "<p>सर्वश्रेष्ठ शिक्षक</p>",
                        "<p>सर्वश्रेष्ठ सैनिक</p>"
                    ],
                    solution_en: "<p>35.(a) <strong>Best coach</strong>. The Dronacharya Award was instituted in 1985. This award is given by the Ministry of Youth Affairs and Sports, for excellence in sports coaching. The first recipients of the award - Bhalchandra Bhaskar Bhagwat (Wrestling), Om Prakash Bhardwaj (Boxing), and O. M. Nambiar (Athletics). The first woman to win the Dronacharya Award was weightlifting coach Hansa Sharma in 2000.</p>",
                    solution_hi: "<p>35.(a) सर्वश्रेष्ठ कोच। द्रोणाचार्य पुरस्कार को 1985 में स्थापित किया गया था। यह पुरस्कार खेल प्रशिक्षण में उत्कृष्टता के लिए युवा मामले और खेल मंत्रालय द्वारा दिया जाता है। द्रोणाचार्य पुरस्कार के प्रथम प्राप्तकर्ता - भालचंद्र भास्कर भागवत (कुश्ती), ओम प्रकाश भारद्वाज (मुक्केबाजी), और ओ. एम. नांबियार (एथलेटिक्स)। द्रोणाचार्य पुरस्कार (2000 में) पाने वाली प्रथम महिला हंसा शर्मा भारत की एक प्रमुख भारोत्तोलन कोच हैं।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "36",
                    section: "18",
                    question_en: "<p>36. Which of the following two quantities have the same dimensions?</p>",
                    question_hi: "<p>36. निम्नलिखित में से किन दो राशियों की विमाएँ समान हैं?</p>",
                    options_en: [
                        "<p>Work and torque</p>",
                        "<p>Power and moment of inertia</p>",
                        "<p>Work and angular displacement</p>",
                        "<p>Power and radius of circular motion</p>"
                    ],
                    options_hi: [
                        "<p>कार्य और बल आघूर्ण</p>",
                        "<p>शक्ति और जड़त्व आघूर्ण</p>",
                        "<p>कार्य और कोणीय विस्थापन</p>",
                        "<p>शक्ति और वृत्ताकार गति की त्रिज्या</p>"
                    ],
                    solution_en: "<p>36.(a) <strong>Work and torque. </strong>Both work and torque have the same dimensions, expressed as [ML<sup>2</sup>T<sup>&minus;2</sup>]. This is because both work and torque can be represented as the product of force and distance, with work defined as force times displacement (W = F&sdot;d) and torque defined as force times the perpendicular distance from the pivot point (&tau; = F&sdot;r). Since force has dimensions of [MLT<sup>&minus;2</sup>], both work and torque ultimately have the same dimensional expression.</p>",
                    solution_hi: "<p>36.(a) <strong>कार्य और बल आघूर्ण। </strong>कार्य और बल आघूर्ण दोनों के विमाएँ समान हैं, जिन्हें [ML<sup>2</sup>T<sup>&minus;2</sup>] के रूप में व्यक्त किया जाता है। ऐसा इसलिए है क्योंकि कार्य और बल आघूर्ण दोनों को बल और दूरी के गुणनफल के रूप में दर्शाया जाता है, जिसमें कार्य को बल और विस्थापन के गुणनफल (W = F&sdot;d) एवं बल आघूर्ण को बल तथा धुरी बिंदु से लंबवत दूरी के गुणनफल (&tau; = F&sdot;r) के रूप में परिभाषित किया जाता है। चूँकि बल के विमाएँ [MLT<sup>&minus;2</sup>] हैं, इसलिए कार्य और बल आघूर्ण दोनों का अंततः एक ही विमाएँ अभिव्यक्ति है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "37",
                    section: "18",
                    question_en: "<p>37. Chandertal, Renuka and Pong Dam are the three Ramsar wetland sites of which state?</p>",
                    question_hi: "<p>37. चंद्रताल, रेणुका और पोंग बांध किस राज्य के तीन रामसर आर्द्रभूमि स्थल हैं?</p>",
                    options_en: [
                        "<p>Karnataka</p>",
                        "<p>Assam</p>",
                        "<p>Gujarat</p>",
                        "<p>Himachal Pradesh</p>"
                    ],
                    options_hi: [
                        "<p>कर्नाटक</p>",
                        "<p>असम</p>",
                        "<p>गुजरात</p>",
                        "<p>हिमाचल प्रदेश</p>"
                    ],
                    solution_en: "<p>37.(d) <strong>Himachal Pradesh. </strong>The Ramsar Convention: It was adopted in the Iranian city of Ramsar in 1971 and came into force in 1975. India joined it in 1982. Some Ramsar sites in India: Chitrangudi Bird Sanctuary and Karikili Bird Sanctuary (Tamil Nadu), Bakhira Sanctuary and Haiderpur Wetland (Uttar Pradesh), Thol Lake and Wadhvana Wetland (Gujarat), Ranganathittu Bird Sanctuary (Karnataka).</p>",
                    solution_hi: "<p>37.(d) <strong>हिमाचल प्रदेश।</strong> रामसर कन्वेंशन: इसे 1971 में ईरानी शहर रामसर में अपनाया गया और 1975 में लागू हुआ। भारत इसमें 1982 में शामिल हुआ। भारत में कुछ रामसर स्थल: चित्रांगुड़ी पक्षी अभयारण्य और करिकिली पक्षी अभयारण्य (तमिलनाडु), बखिरा अभयारण्य और हैदरपुर आर्द्रभूमि ( उत्तर प्रदेश), थोल झील और वाधवाना आर्द्रभूमि (गुजरात), रंगनाथिटु पक्षी अभयारण्य (कर्नाटक)।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "38",
                    section: "18",
                    question_en: "<p>38. _____ is an asset that the borrower owns (such as land, buildings, vehicles, livestock, bank deposits) and uses as a guarantee to the lender until the loan is repaid.</p>",
                    question_hi: "<p>38. _____ एक ऐसी संपत्ति है, जिसका मालिक कर्जदार है (जैसे भूमि, भवन, वाहन, पशुधन, बैंकों में जमा राशि) और इसका इस्तेमाल वह ऋणदाता को गारंटी देने के रूप में करता है, जब तक की ऋण कि भुगतान नहीं हो जाता।</p>",
                    options_en: [
                        "<p>Liabilities</p>",
                        "<p>Collateral</p>",
                        "<p>loan</p>",
                        "<p>Fixed assets</p>"
                    ],
                    options_hi: [
                        "<p>देनदारियां</p>",
                        "<p>समर्थक ऋणाधार</p>",
                        "<p>ऋण</p>",
                        "<p>स्थायी परिसंपत्तियां</p>"
                    ],
                    solution_en: "<p>38.(b) <strong>Collateral.</strong> Every loan agreement specifies an interest rate that the borrower must pay to the lender along with the repayment of the principal. Additionally, lenders may require collateral (security) against loans. If the borrower fails to repay the loan, the lender has the right to sell the asset or collateral to recover the payment. Liabilities are obligations or debts that an enterprise has to pay at some time in the future.</p>",
                    solution_hi: "<p>38.(b) <strong>समर्थक ऋणाधार। </strong>प्रत्येक ऋण समझौते में एक ब्याज दर निर्दिष्ट की जाती है जिसे उधारकर्ता को मूलधन के पुनर्भुगतान के साथ ऋणदाता को देना होगा। इसके अतिरिक्त, ऋणदाताओं को ऋण के विरुद्ध समर्थक ऋणाधार (security) की आवश्यकता हो सकती है। यदि उधारकर्ता ऋण चुकाने में विफल रहता है, तो ऋणदाता को भुगतान वसूलने के लिए परिसंपत्ति या समर्थक ऋणाधार को बेचने का अधिकार है। देनदारियाँ, प्रतिज्ञापत्र या ऋण हैं जिन्हें किसी उद्यम को भविष्य में किसी समय चुकाना होता है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "39",
                    section: "18",
                    question_en: "<p>39. Who launched the \"VISION Portal\" in November 2024, aimed at nurturing skill development in underprivileged children?</p>",
                    question_hi: "<p>39. नवंबर 2024 में \"विज़न पोर्टल\" किसने लॉन्च किया, जिसका उद्देश्य वंचित बच्चों में कौशल विकास को बढ़ावा देना है?</p>",
                    options_en: [
                        "<p>Smriti Irani</p>",
                        "<p>Amit Shah</p>",
                        "<p>Dr. Jitendra Singh</p>",
                        "<p>Dharmendra Pradhan</p>"
                    ],
                    options_hi: [
                        "<p>स्मृति ईरानी</p>",
                        "<p>अमित शाह</p>",
                        "<p>डॉ. जितेंद्र सिंह</p>",
                        "<p>धर्मेंद्र प्रधान</p>"
                    ],
                    solution_en: "<p>39.(c) <strong>Dr. Jitendra Singh.</strong> The \"VISION Portal,\" focuses on providing skill development opportunities to underprivileged children. This initiative aims to bridge the skill gap and empower youth for better career prospects.</p>",
                    solution_hi: "<p>39.(c) <strong>डॉ. जितेंद्र सिंह।</strong> \"विज़न पोर्टल\" वंचित बच्चों को कौशल विकास के अवसर प्रदान करने पर केंद्रित है। इस पहल का उद्देश्य कौशल अंतर को पाटना और युवाओं को बेहतर करियर संभावनाओं के लिए सशक्त बनाना है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "40",
                    section: "18",
                    question_en: "<p>40. Who among the following artists received the Padma Shri award in 2021 for his contribution to keep alive the tribal folk dance &lsquo;Gussadi&rsquo; ?</p>",
                    question_hi: "<p>40. निम्नलिखित में से किस कलाकार को 2021 में आदिवासी लोक नृत्य \'गुसादी\' को जीवित रखने में उनके योगदान के लिए पद्म श्री पुरस्कार मिला?</p>",
                    options_en: [
                        "<p>Gopal Prasad</p>",
                        "<p>Padmaja Reddy</p>",
                        "<p>R Muthukannammal</p>",
                        "<p>Kanaka Raju</p>"
                    ],
                    options_hi: [
                        "<p>गोपाल प्रसाद</p>",
                        "<p>पद्मजा रेड्ड</p>",
                        "<p>आर मुथुकन्नमल</p>",
                        "<p>कनक राजू</p>"
                    ],
                    solution_en: "<p>40.(d) <strong>Kanaka Raju. </strong>Gusadi is a folk dance performed by &lsquo;Raj Gonds&rsquo; or the Gondulu tribes in the Adilabad district of Telangana. The dance is generally performed during the festival of Diwali. Gaddam Padmaja Reddy - Kuchipudi exponent. Gopal Prasad Dubey - Padma Shri (2012) for his contribution in the field of Chhau dance. R Muthukannammal - A Sadhir dancer who got the Padma Shri in 2022.</p>",
                    solution_hi: "<p>40.(d) <strong>कनक राजू।</strong> गुसादी तेलंगाना के आदिलाबाद जिले में \'राज गोंड\' या गोंडुलु जनजातियों द्वारा किया जाने वाला एक लोक नृत्य है। यह नृत्य आम तौर पर दिवाली के त्योहार के दौरान किया जाता है। गद्दाम पद्मजा रेड्डी - कुचिपुड़ी प्रतिपादक। गोपाल प्रसाद दुबे - छऊ नृत्य के क्षेत्र में उनके योगदान के लिए पद्म श्री (2012)। आर मुथुकन्नाम्मल - एक सधिर नर्तक जिन्हें 2022 में पद्म श्री मिला।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "41",
                    section: "18",
                    question_en: "<p>41. The method of separating a mixture of soluble solids by dissolving them in a suitable&nbsp;hot solvent and then lowering the temperature slowly is called :</p>",
                    question_hi: "<p>41. घुलनशील ठोसों के मिश्रण को एक उपयुक्त गर्म विलायक में घोलकर और फिर तापमान को धीरे-धीरे&nbsp;कम करके उसे पृथक करने की विधि कहलाती है:</p>",
                    options_en: [
                        "<p>azeotropic distillation</p>",
                        "<p>sublimation</p>",
                        "<p>dephlegmation</p>",
                        "<p>fractional crystallisation</p>"
                    ],
                    options_hi: [
                        "<p>एज़ोट्रोपिक आसवन</p>",
                        "<p>उर्ध्वपातन</p>",
                        "<p>प्रभाजी आसवन</p>",
                        "<p>आंशिक क्रिस्टलन</p>"
                    ],
                    solution_en: "<p>41.(d) <strong>fractional crystallisation.</strong> Azeotropic distillation is the process of separating all the components of an azeotropic mixture by the process of distillation. Sublimation is the change of solid state directly to gaseous state without going through liquid state.</p>",
                    solution_hi: "<p>41.(d) <strong>आंशिक क्रिस्टलन। </strong>एज़ोट्रोपिक आसवन, आसवन की प्रक्रिया द्वारा एज़ोट्रोपिक मिश्रण के सभी घटकों को अलग करने की प्रक्रिया है। ऊर्ध्वपातन, ठोस अवस्था से द्रव अवस्था में जाए बिना सीधे गैसीय अवस्था में परिवर्तन है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "42",
                    section: "18",
                    question_en: "<p>42. What does CJI stand for?</p>",
                    question_hi: "<p>42. CJl का क्या अर्थ हैं?</p>",
                    options_en: [
                        "<p>Chief Justice of India</p>",
                        "<p>Chief Justice of Income Tax</p>",
                        "<p>Council Judges of India</p>",
                        "<p>Controller Justice of India</p>"
                    ],
                    options_hi: [
                        "<p>(चीफ जस्टिस ऑफ इंडिया) Chief Justice of India</p>",
                        "<p>(चीफ जस्टिस ऑफ इंकम टैक्स) Chief Justice of Income Tax</p>",
                        "<p>(काउंसिल जजेस ऑफ इंडिया) Council Judges of India</p>",
                        "<p>(कंट्रोलर जस्टिस ऑफ इंडिया) Controller Justice of India</p>"
                    ],
                    solution_en: "<p>42.(a) <strong>Chief Justice of India</strong> (CJI) is the highest-ranking officer of the Indian judiciary and serves as the chief judge of the Supreme Court of India. The Chief Justice of India and the Judges of the Supreme Court are appointed by the President under Article 124(2). The appointed chief justice will serve until they reach the age of 65 or are removed through the constitutional process of impeachment.</p>",
                    solution_hi: "<p>42.(a) <strong>चीफ जस्टिस ऑफ इंडिया </strong>(CJI) भारतीय न्यायपालिका के सर्वोच्च पद के अधिकारी होते हैं और भारत के सर्वोच्च न्यायालय के मुख्य न्यायाधीश के रूप में कार्य करते हैं। भारत के मुख्य न्यायाधीश एवं सर्वोच्च न्यायालय के न्यायाधीशों की नियुक्ति राष्ट्रपति द्वारा अनुच्छेद 124(2) के तहत की जाती है। नियुक्त मुख्य न्यायाधीश 65 वर्ष की आयु तक या महाभियोग की संवैधानिक प्रक्रिया के माध्यम से हटाए जाने तक पद पर बने रहेंगे।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "43",
                    section: "18",
                    question_en: "<p>43. Which of the following rivers is known as Dakshin Ganga?</p>",
                    question_hi: "<p>43. निम्नलिखित में से किस नदी को दक्षिण गंगा के नाम से जाना जाता है?</p>",
                    options_en: [
                        "<p>Tapi</p>",
                        "<p>Narmada</p>",
                        "<p>Kaveri</p>",
                        "<p>Godavari</p>"
                    ],
                    options_hi: [
                        "<p>तापी</p>",
                        "<p>नर्मदा</p>",
                        "<p>कावेरी</p>",
                        "<p>गोदावरी</p>"
                    ],
                    solution_en: "<p>43.(d) <strong>Godavari </strong>is the largest peninsular river of India. It rises from Trimbakeshwar in the Nashik district of Maharashtra and drains into Bay of Bengal. Its length is 1465 km. It is also the second largest river in India after Ganga. Principal Tributaries - Pravara, Purna, Manjra, Penganga, Wardha, Wainganga, Pranhita (combined flow of Wainganga, Penganga, Wardha), Indravati, Maner and Sabri.</p>",
                    solution_hi: "<p>43.(d) <strong>गोदावरी</strong> भारत की सबसे बड़ी प्रायद्वीपीय नदी है। यह महाराष्ट्र के नासिक जिले में त्र्यंबकेश्वर से निकलती है और बंगाल की खाड़ी में गिरती है। इसकी लंबाई 1465 किमी है। यह गंगा के बाद भारत की दूसरी सबसे बड़ी नदी भी है। प्रमुख सहायक नदियाँ - प्रवरा, पूर्णा, मंजरा, पेनगंगा, वर्धा, वैनगंगा, प्राणहिता (वैनगंगा, पेंगंगा, वर्धा का संयुक्त प्रवाह), इंद्रावती, मनेर और साबरी।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "44",
                    section: "18",
                    question_en: "<p>44. Tipu Sultan became the ruler of Mysore in_______.</p>",
                    question_hi: "<p>44. टीपू सुल्तान वर्ष _____में मैसूर का शासक बना।</p>",
                    options_en: [
                        "<p>1798</p>",
                        "<p>1782</p>",
                        "<p>1792</p>",
                        "<p>1786</p>"
                    ],
                    options_hi: [
                        "<p>1798</p>",
                        "<p>1782</p>",
                        "<p>1792</p>",
                        "<p>1786</p>"
                    ],
                    solution_en: "<p>44.(b)<strong> 1782.</strong> Tipu Sultan (The Tiger of Mysore) was the ruler of the Kingdom of Mysore (Karnataka) which he inherited from his father Hyder Ali. He was a pioneer of rocket artillery. On the 4th of May 1799, Tipu Sultan was killed, fighting the British East India Company Army.</p>",
                    solution_hi: "<p>44.(b) <strong>1782.</strong> टीपू सुल्तान (मैसूर का शेर), मैसूर साम्राज्य (कर्नाटक) का शासक था, जो उसे अपने पिता हैदर अली से विरासत में मिला था। वह रॉकेट तोपखाने का अग्रणी था। 4 मई 1799 को टीपू सुल्तान ब्रिटिश ईस्ट इंडिया कंपनी की सेना से लड़ते हुए मारे गए।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "45",
                    section: "18",
                    question_en: "<p>45. Kirti Kisan Party (KKP) was established on ______ at Amritsar.</p>",
                    question_hi: "<p>45. कीर्ति किसान पार्टी (KKP) की स्थापना ______ को अमृतसर में हुई थी।</p>",
                    options_en: [
                        "<p>7<sup>th</sup> February 1927</p>",
                        "<p>13<sup>th</sup> March 1927</p>",
                        "<p>12<sup>th</sup> April 1927</p>",
                        "<p>10<sup>th</sup> May 1927</p>"
                    ],
                    options_hi: [
                        "<p>7 फ़रवरी, 1927</p>",
                        "<p>13 मार्च, 1927</p>",
                        "<p>12 अप्रैल, 1927</p>",
                        "<p>10 मई, 1927</p>"
                    ],
                    solution_en: "<p>45.(c) <strong>12<sup>th</sup> April 1927. </strong>The ideal of the Kirti Kisan Party (KKP) was to establish a socialist republic. This party had Marxist leanings and possessed one of the biggest political networks in rural Punjab during the 1930s and the 40s.</p>",
                    solution_hi: "<p>45.(c) <strong>12 अप्रैल 1927. </strong>कीर्ति किसान पार्टी (KKP) का आदर्श एक समाजवादी गणराज्य की स्थापना करना था। इस पार्टी का रुझान मार्क्सवादी था और 1930 और 40 के दशक के दौरान ग्रामीण पंजाब में इसका सबसे बड़ा राजनीतिक नेटवर्क था।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "46",
                    section: "18",
                    question_en: "<p>46. Which of the following currency note was discarded by the Government of India in November 2016?</p>",
                    question_hi: "<p>46. निम्नलिखित में से कौन सा मुद्रा नोट नवंबर 2016 में भारत सरकार द्वारा बंद कर दिया गया था?</p>",
                    options_en: [
                        "<p>₹500</p>",
                        "<p>₹200</p>",
                        "<p>₹2,000</p>",
                        "<p>₹100</p>"
                    ],
                    options_hi: [
                        "<p>₹500</p>",
                        "<p>₹200</p>",
                        "<p>₹2,000</p>",
                        "<p>₹100</p>"
                    ],
                    solution_en: "<p>46.(a) <strong>₹500</strong>. In India, currency notes of ₹500 and ₹1,000 were declared invalid in 2016. The Government of India announced the demonetisation on 8 November 2016. Government-issued new ₹500 and ₹2,000 currency notes soon after the demonetisation. The newly redesigned series of banknotes ₹500 and ₹2,000 were in circulation since 10 November 2016. The issuance of the currency is controlled by the Reserve Bank of India.</p>",
                    solution_hi: "<p>46.(a)<strong> ₹500. </strong>भारत में 2016 में ₹500 और ₹1,000 के करेंसी नोटों को अमान्य घोषित कर दिया गया था। भारत सरकार ने 8 नवंबर 2016 को विमुद्रीकरण की घोषणा की। विमुद्रीकरण के तुरंत बाद सरकार ने ₹500 और ₹2,000 के नए करेंसी नोट जारी किए। ₹500 और ₹2,000 के बैंक नोटों की नई डिज़ाइन की गई श्रृंखला 10 नवंबर 2016 से प्रचलन में थी। मुद्रा जारी करने का नियंत्रण भारतीय रिज़र्व बैंक द्वारा किया जाता है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "47",
                    section: "18",
                    question_en: "<p>47. Rickets is caused by deficiency of:</p>",
                    question_hi: "<p>47. रिकेट्स किसकी कमी से होता है:</p>",
                    options_en: [
                        "<p>vitamin B</p>",
                        "<p>vitamin D</p>",
                        "<p>vitamin C</p>",
                        "<p>vitamin A</p>"
                    ],
                    options_hi: [
                        "<p>विटामिन B</p>",
                        "<p>विटामिन D</p>",
                        "<p>विटामिन C</p>",
                        "<p>विटामिन A</p>"
                    ],
                    solution_en: "<p>47.(b)<strong> Vitamin D.</strong> Rickets is a childhood disorder caused by a deficiency of vitamin D, calcium or phosphate. Vitamin B deficiency: Causes Beriberi, Pellagra or Anemia. Vitamin C deficiency: Leads to Scurvy (bleeding gums, poor wound healing). Vitamin A deficiency: Causes night blindness, weakened immunity, and skin issues.</p>",
                    solution_hi: "<p>47.(b) <strong>विटामिन D. </strong>रिकेट्स बचपन में होने वाला एक विकार है जो विटामिन D, कैल्शियम या फॉस्फेट की कमी के कारण होता है। विटामिन B की कमी: बेरीबेरी, पेलाग्रा या एनीमिया का कारण बनती है। विटामिन C की कमी: स्कर्वी (मसूड़ों से खून आना, घाव ठीक से न भरना) का कारण बनती है। विटामिन A की कमी: रतौंधी, कमजोर प्रतिरक्षा और त्वचा संबंधी समस्याओं का कारण बनती है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "48",
                    section: "18",
                    question_en: "<p>48. A small object is placed on the focus on the left side of a convex lens. Where will be the image formed?</p>",
                    question_hi: "<p>48. किसी उत्तल लेंस के बायीं ओर फोकस पर एक छोटी वस्तु रखे जाने पर, निम्नलिखित में से कौन-से स्थान पर प्रतिबिम्ब बनेगा?</p>",
                    options_en: [
                        "<p>At the centre on the right side of the lens.</p>",
                        "<p>At infinity on the left side of the lens.</p>",
                        "<p>At infinity on the right side of the lens.</p>",
                        "<p>At the focus on the right side of the lens.</p>"
                    ],
                    options_hi: [
                        "<p>लेंस के दायीं ओर, केंद्र पर</p>",
                        "<p>लेंस के बायीं ओर, अनंत पर</p>",
                        "<p>लेंस के दायीं ओर, अनंत पर</p>",
                        "<p>लेंस के दायीं ओर, फोकस पर</p>"
                    ],
                    solution_en: "<p>48.(c) <strong>At infinity on the right side of the lens.</strong> Object Position Image Formation by Convex Lens: When an object is placed at the focus (F) of a convex lens, its image is formed at infinity. If the object is between F and 2F, the image is real, inverted, and magnified. At 2F, the image is real, inverted, and the same size as the object. Beyond 2F, the image is real, inverted, and reduced. An object at infinity forms an image at the focus (F) of the lens.</p>",
                    solution_hi: "<p>48.(c) <strong>लेंस के दायीं ओर, अनंत पर। </strong>उत्तल लेंस द्वारा वस्तु की स्थिति का प्रतिबिंब निर्माण: जब किसी वस्तु को उत्तल लेंस के फोकस (F) पर रखा जाता है, तो उसका प्रतिबिंब अनंत पर बनता है। यदि वस्तु F और 2F के मध्य है, तो प्रतिबिंब वास्तविक, उल्टा एवं बड़ा होता है। 2F पर, प्रतिबिंब वास्तविक, उल्टा एवं वस्तु के समान आकार की होती है। 2F से दूर, प्रतिबिंब वास्तविक, उल्टा एवं छोटा होता है। अनंत पर स्थित वस्तु लेंस के फोकस (F) पर प्रतिबिंब बनाती है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "49",
                    section: "18",
                    question_en: "<p>49 What is India&rsquo;s score in the AI Preparedness Index 2024?</p>",
                    question_hi: "<p>49. AI प्रीपेरेड्नेस इंडेक्स 2024 में भारत का स्कोर क्या है ?</p>",
                    options_en: [
                        "<p>0.49</p>",
                        "<p>0.52</p>",
                        "<p>0.45</p>",
                        "<p>0.60</p>"
                    ],
                    options_hi: [
                        "<p>0.49</p>",
                        "<p>0.52</p>",
                        "<p>0.45</p>",
                        "<p>0.60</p>"
                    ],
                    solution_en: "<p>49.(a) <strong>0.49. </strong>India ranks 72nd out of 174 countries in the AI Preparedness Index. This report is released by the International Monetary Fund. The index evaluates a country&rsquo;s AI readiness based on four pillars: digital infrastructure, human capital, technological innovation, and legal frameworks. Singapore holds the top rank with a score of 0.80.</p>",
                    solution_hi: "<p>49.(a) <strong>0.49. </strong>AI प्रीपेरेड्नेस इंडेक्स 2024 में भारत 174 देशों में से 72वें स्थान पर है। यह रिपोर्ट अंतर्राष्ट्रीय मुद्रा कोष द्वारा जारी की जाती है। यह सूचकांक चार स्तंभों के आधार पर किसी देश की AI तत्परता का मूल्यांकन करता है: डिजिटल मूल ढांचा, मानव पूंजी, तकनीकी नवाचार और कानूनी ढांचा। सिंगापुर 0.80 के स्कोर के साथ शीर्ष स्थान पर है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "50",
                    section: "17",
                    question_en: "<p>50. &lsquo;My Days&rsquo; is an autobiography written by whom among the following authors?</p>",
                    question_hi: "<p>50. \'माई डेज\' निम्नलिखित लेखकों में से किसके द्वारा लिखी गई आत्मकथा है?</p>",
                    options_en: [
                        "<p>Vaijayantimala</p>",
                        "<p>RK Narayan</p>",
                        "<p>Shashi Kapoor</p>",
                        "<p>Hema Malini</p>"
                    ],
                    options_hi: [
                        "<p>वैजयंतीमाला</p>",
                        "<p>आरके नारायण</p>",
                        "<p>शशि कपूर</p>",
                        "<p>हेमा मालिनी</p>"
                    ],
                    solution_en: "<p>50.(b) <strong>RK Narayan.</strong> His other books: &ldquo;Malgudi Days&rdquo;, &ldquo;Swami and Friends&rdquo;, &rdquo;The Guide&rdquo;, &ldquo;The Vendor of Sweets&rdquo;, &ldquo;The Dark Room&rdquo;, and &ldquo;The Bachelor of Arts&rdquo;. </p>",
                    solution_hi: "<p>50.(b) <strong>आर के नारायण ।</strong> उनकी अन्य पुस्तकें: \"मालगुडी डेज़\", \"स्वामी एंड फ्रेंड्स\", \"द गाइड\", \"द वेंडर ऑफ स्वीट्स\", \"द डार्क रूम\" और \"द बैचलर ऑफ आर्ट्स\"।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "51",
                    section: "17",
                    question_en: "<p>51. If 3sin<sup>2</sup>&theta; + 5cos<sup>2</sup>&theta; = 4 and &theta; is an acute angle, then the value of tan&theta; is:</p>",
                    question_hi: "<p>51. यदि 3sin<sup>2</sup>&theta; + 5cos<sup>2</sup>&theta; = 4 और &theta; एक न्यून कोण है, तो tan &theta; का मान ज्ञात करें।</p>",
                    options_en: [
                        "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><msqrt><mn>2</mn></msqrt></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><msqrt><mn>3</mn></msqrt></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math></p>",
                        "<p>1</p>"
                    ],
                    options_hi: [
                        "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><msqrt><mn>2</mn></msqrt></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><msqrt><mn>3</mn></msqrt></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math></p>",
                        "<p>1</p>"
                    ],
                    solution_en: "<p>51.(d) <br>3sin<sup>2</sup>&theta; + 5cos<sup>2</sup>&theta; = 4&nbsp;<br>&rArr; 3(sin<sup>2</sup>&theta; + cos<sup>2</sup>&theta;) + 2cos<sup>2</sup>&theta; = 4&nbsp;<br>&rArr; 2cos<sup>2</sup>&theta; = 1<br>&rArr; cos&theta; = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><msqrt><mn>2</mn></msqrt></mrow></mfrac></math><br>&rArr; &theta; = 45&deg;<br>&there4; tan&theta; = tan 45&deg; = 1</p>",
                    solution_hi: "<p>51.(d)&nbsp;<br>3sin<sup>2</sup>&theta; + 5cos<sup>2</sup>&theta; = 4&nbsp;<br>&rArr; 3(sin<sup>2</sup>&theta; + cos<sup>2</sup>&theta;) + 2cos<sup>2</sup>&theta; = 4&nbsp;<br>&rArr; 2cos<sup>2</sup>&theta; = 1<br>&rArr; cos&theta; = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><msqrt><mn>2</mn></msqrt></mrow></mfrac></math><br>&rArr; &theta; = 45&deg;<br>&there4; tan&theta; = tan 45&deg; = 1</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "52",
                    section: "17",
                    question_en: "<p>52. The population of the village was 130,000. It increased by 10% in the first year and increased by 25% in the second year. Its population after two years is _______.</p>",
                    question_hi: "<p>52. एक गाँव की जनसंख्या 130000 थी। पहले वर्ष में इसमें 10% की वृद्धि हुई और दूसरे वर्ष में 25% की वृद्धि हुई। दो वर्ष बाद इसकी जनसंख्या _________ है।</p>",
                    options_en: [
                        "<p>175500</p>",
                        "<p>143000</p>",
                        "<p>178750</p>",
                        "<p>162500</p>"
                    ],
                    options_hi: [
                        "<p>175500</p>",
                        "<p>143000</p>",
                        "<p>178750</p>",
                        "<p>162500</p>"
                    ],
                    solution_en: "<p>52.(c)<br>Population after 2 years <br>= 130000 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>11</mn><mn>10</mn></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>4</mn></mfrac></math> <br>= 178750</p>",
                    solution_hi: "<p>52.(c)<br>2 वर्ष बाद जनसंख्या <br>= 130000 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>11</mn><mn>10</mn></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>4</mn></mfrac></math> <br>= 178750</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "53",
                    section: "17",
                    question_en: "<p>53. Given that 26<sup>0.89</sup> = x, 26<sup>0.31 </sup>= y and x<sup>z</sup> = y<sup>2</sup> , then the value of z is close to:</p>",
                    question_hi: "<p>53. दिया गया है कि 26<sup>0.89</sup> = x, 26<sup>0.31 </sup>= y और x<sup>z</sup> = y<sup>2</sup> है, तो z का निकटतम मान कितना है?</p>",
                    options_en: [
                        "<p>-0.41</p>",
                        "<p>0.7</p>",
                        "<p>2.47</p>",
                        "<p>1.33</p>"
                    ],
                    options_hi: [
                        "<p>-0.41</p>",
                        "<p>0.7</p>",
                        "<p>2.47</p>",
                        "<p>1.33</p>"
                    ],
                    solution_en: "<p>53.(b)<br>[26<sup>0.89</sup> = x and 26<sup>0.31 </sup>= y] -------(i)<br>Also, x<sup>z</sup>&nbsp;= y<sup>2</sup>-------(ii)<br>Putting the value of x&nbsp;and y from equation (i) to (ii) we get;<br>26<sup>0.89 &times; z</sup> = 26<sup>0.31 &times; 2</sup> <br>Comparing power both sides<br>(0.89)z = (0.31) &times; 2<br>89z&nbsp;= 62<br>z = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>62</mn><mn>89</mn></mfrac></math> = 0.7</p>",
                    solution_hi: "<p>53.(b)<br>[26<sup>0.89</sup> = x और 26<sup>0.31 </sup>= y] -------(i)<br>तथा, x<sup>z</sup>&nbsp;= y<sup>2</sup>--------(ii)<br>समीकरण (i) से (ii) में x और y का मान रखने पर हमें प्राप्त होता है;<br>26<sup>0.89 &times; z</sup> = 26<sup>0.31 &times; 2</sup><br>दोनों पक्षों के घातों की तुलना करने पर <br>(0.89)z = (0.31) &times; 2<br>89z&nbsp;= 62<br>z = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>62</mn><mn>89</mn></mfrac></math> = 0.7</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "54",
                    section: "17",
                    question_en: "<p>54. Pipes P and Q can completely fill a water tank in 10 hours and 15 hours, respectively. A pipe R can empty a tank filled completely with water in 12 hours. Initially, the tank is empty and only pipes P and Q are opened at 6 a.m. and pipe R is also opened at 9 a.m. By what time will the tank be completely filled?</p>",
                    question_hi: "<p>54. पाइप P और Q एक पानी की टंकी को क्रमशः 10 घंटे और 15 घंटे में पूरी तरह भर सकते हैं। एक पाइप R पानी से भरे उस टैंक को 12 घंटे में खाली कर सकता है। प्रारंभ में, टैंक खाली है और केवल पाइप P और Q 6 a.m को खोले जाते हैं और पाइप R को भी 9 a.m को खोला जाता है। टैंक कितने बजे तक पूरी तरह भर जाएगा?</p>",
                    options_en: [
                        "<p>1 p.m.</p>",
                        "<p>2 p.m.</p>",
                        "<p>11 a.m.</p>",
                        "<p>3 p.m.</p>"
                    ],
                    options_hi: [
                        "<p>1 p.m.</p>",
                        "<p>2 p.m.</p>",
                        "<p>11 a.m.</p>",
                        "<p>3 p.m.</p>"
                    ],
                    solution_en: "<p>54.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741072361873.png\" alt=\"rId51\" width=\"206\" height=\"122\"><br>P and Q worked for three hours = 10 &times; 3 = 30 units<br>Remaining work = 60 - 30 = 30 units<br>Time taken by all pipes for remaining work = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>30</mn><mrow><mn>6</mn><mo>+</mo><mn>4</mn><mo>-</mo><mn>5</mn></mrow></mfrac></math> = 6 hrs<br>&rArr; the tank will be completely filled at 9am + 6 hrs = 3 pm</p>",
                    solution_hi: "<p>54.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741072362037.png\" alt=\"rId52\" width=\"184\" height=\"116\"><br>P और Q ने तीन घंटे काम किया = 10 &times; 3 = 30 इकाई <br>शेष कार्य = 60 - 30 = 30 इकाई <br>शेष कार्य के लिए सभी पाइपों द्वारा लिया गया समय = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>30</mn><mrow><mn>6</mn><mo>+</mo><mn>4</mn><mo>-</mo><mn>5</mn></mrow></mfrac></math>&nbsp;= 6 घंटे<br>पूरी टंकी को भरने मे लगा समय = 9am + 6 hrs = 3 pm</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "55",
                    section: "17",
                    question_en: "<p>55. In a division problem, the divisor is 4 times the quotient and 2 times the remainder. If the remainder is 32, then find the dividend.</p>",
                    question_hi: "<p>55. एक भाग-प्रश्न में भाजक भागफल का 4 गुना और शेषफल का 2 गुना है। यदि शेषफल 32 है, तो भाज्य ज्ञात कीजिए।</p>",
                    options_en: [
                        "<p>1056</p>",
                        "<p>1650</p>",
                        "<p>3240</p>",
                        "<p>1065</p>"
                    ],
                    options_hi: [
                        "<p>1056</p>",
                        "<p>1650</p>",
                        "<p>3240</p>",
                        "<p>1065</p>"
                    ],
                    solution_en: "<p>55.(a) According to the question,<br>Remainder = 32<br>Divisor = 4 &times; quotient<br>Divisor = 2 &times; remainder = 2 &times; 32 = 64<br>So, divisor = 64 , quotient = 16<br>&rArr; dividend = divisor &times; quotient + remainder<br>&rArr; dividend = 64 &times; 16 + 32 = 1056</p>",
                    solution_hi: "<p>55.(a) प्रश्न के अनुसार,<br>शेषफल = 32<br>भाजक = 4 &times; भागफल<br>भाजक = 2 ​​&times; शेषफल = 2 &times; 32 = 64<br>तो, भाजक = 64, भागफल = 16<br>&rArr; भाज्य = भाजक &times; भागफल + शेषफल<br>&rArr; भाज्य = 64 &times; 16 + 32 = 1056</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "56",
                    section: "17",
                    question_en: "<p>56. A shopkeeper buys oranges at the rate of 10 oranges for ₹50 and sells them at the rate of 12 oranges for ₹74. Find his gain or loss percentage.</p>",
                    question_hi: "<p>56. एक दुकानदार ₹50 में 10 संतरे की दर से संतरे खरीदता है और उन्हें ₹74 में 12 संतरे की दर से बेचता है। उसका लाभ या हानि प्रतिशत ज्ञात कीजिए।</p>",
                    options_en: [
                        "<p>23<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math>% gain</p>",
                        "<p>25% gain</p>",
                        "<p>30% loss</p>",
                        "<p>25<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math>% loss </p>"
                    ],
                    options_hi: [
                        "<p>23<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math>% लाभ</p>",
                        "<p>25% लाभ</p>",
                        "<p>30% हानि</p>",
                        "<p>25<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math>% हानि</p>"
                    ],
                    solution_en: "<p>56.(a)<br>C.P. of one orange = <math display=\"inline\"><mfrac><mrow><mn>50</mn></mrow><mrow><mn>10</mn></mrow></mfrac></math> = 5<br>S.P. of one orange = <math display=\"inline\"><mfrac><mrow><mn>74</mn></mrow><mrow><mn>12</mn></mrow></mfrac></math> <br>Profit % = <math display=\"inline\"><mfrac><mrow><mfrac><mrow><mn>74</mn></mrow><mrow><mn>12</mn></mrow></mfrac><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>5</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> &times; 100 <br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>14</mn><mn>60</mn></mfrac></math> &times; 100 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>70</mn><mn>3</mn></mfrac></math>% = 23<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>3</mn></mfrac></math>%</p>",
                    solution_hi: "<p>56.(a)<br>एक संतरे का क्रय मूल्य = <math display=\"inline\"><mfrac><mrow><mn>50</mn></mrow><mrow><mn>10</mn></mrow></mfrac></math>= 5<br>एक संतरे का विक्रय मूल्य = <math display=\"inline\"><mfrac><mrow><mn>74</mn></mrow><mrow><mn>12</mn></mrow></mfrac></math> <br>लाभ % = <math display=\"inline\"><mfrac><mrow><mfrac><mrow><mn>74</mn></mrow><mrow><mn>12</mn></mrow></mfrac><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>5</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> &times; 100 <br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>14</mn><mn>60</mn></mfrac></math> &times; 100 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>70</mn><mn>3</mn></mfrac></math>% = 23<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>3</mn></mfrac></math>%</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "57",
                    section: "17",
                    question_en: "<p>57. Study the given bar graph and answer the question that follows.<br>The following bar graph shows the quarterly profit (in lakhs) of a departmental store from 1997 to 2000.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741072362166.png\" alt=\"rId53\" width=\"408\" height=\"246\"> <br>What was the percentage increase in annual profit of the departmental store from 1997 to 2000?</p>",
                    question_hi: "<p>57. दिए गए दंड आलेख का अध्ययन कीजिए और निम्नलिखित प्रश्न का उत्तर दीजिए।<br>नीचे दिए गए दंडआलेख में एक डिपार्टमेंटल स्टोर के वर्ष 1997 से 2000 तक के तिमाही लाभ (लाख में) को दिखाया गया है।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741072362355.png\" alt=\"rId54\" width=\"375\" height=\"225\"> <br>वर्ष 1997 से 2000 तक डिपार्टमेंटल स्टोर के वार्षिक लाभ में कितने प्रतिशत की वृद्धि हुई?</p>",
                    options_en: [
                        "<p>85<math display=\"inline\"><mfrac><mrow><mn>33</mn></mrow><mrow><mn>41</mn></mrow></mfrac></math>%</p>",
                        "<p>84<math display=\"inline\"><mfrac><mrow><mn>33</mn></mrow><mrow><mn>41</mn></mrow></mfrac></math>%</p>",
                        "<p>87<math display=\"inline\"><mfrac><mrow><mn>33</mn></mrow><mrow><mn>41</mn></mrow></mfrac></math>%</p>",
                        "<p>83<math display=\"inline\"><mfrac><mrow><mn>33</mn></mrow><mrow><mn>41</mn></mrow></mfrac></math>%</p>"
                    ],
                    options_hi: [
                        "<p>85<math display=\"inline\"><mfrac><mrow><mn>33</mn></mrow><mrow><mn>41</mn></mrow></mfrac></math>%</p>",
                        "<p>84<math display=\"inline\"><mfrac><mrow><mn>33</mn></mrow><mrow><mn>41</mn></mrow></mfrac></math>%</p>",
                        "<p>87<math display=\"inline\"><mfrac><mrow><mn>33</mn></mrow><mrow><mn>41</mn></mrow></mfrac></math>%</p>",
                        "<p>83<math display=\"inline\"><mfrac><mrow><mn>3</mn><mn>3</mn></mrow><mrow><mn>41</mn></mrow></mfrac></math>%</p>"
                    ],
                    solution_en: "<p>57.(c) <br>Annual profit in 1997 = 135 + 115 + 90 + 70 = 410 <br>Annual profit in 2000 = 150 + 180 + 210 + 230 = 770<br>percentage increase in annual profit of the departmental store from 1997 to 2000 <br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>770</mn><mo>-</mo><mn>410</mn></mrow><mn>410</mn></mfrac></math> &times; 100<br>= <math display=\"inline\"><mfrac><mrow><mn>3600</mn></mrow><mrow><mn>41</mn></mrow></mfrac></math> = 87<math display=\"inline\"><mfrac><mrow><mn>33</mn></mrow><mrow><mn>41</mn></mrow></mfrac></math>%</p>",
                    solution_hi: "<p>57.(c) <br>1997 में वार्षिक लाभ = 135 + 115 + 90 + 70 = 410 <br>2000 में वार्षिक लाभ = 150 + 180 + 210 + 230 = 770<br>1997 से 2000 तक डिपार्टमेंटल स्टोर के वार्षिक लाभ में प्रतिशत वृद्धि<br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>770</mn><mo>-</mo><mn>410</mn></mrow><mn>410</mn></mfrac></math> &times; 100<br>= <math display=\"inline\"><mfrac><mrow><mn>3600</mn></mrow><mrow><mn>41</mn></mrow></mfrac></math> = 87<math display=\"inline\"><mfrac><mrow><mn>33</mn></mrow><mrow><mn>41</mn></mrow></mfrac></math>%</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "58",
                    section: "17",
                    question_en: "<p>58. What is the area of a quadrilateral ABCD. (shown below) in which sides AB and BC are equal, sides AD and CD are of lengths 5 cm and 13 cm, respectively, and side AD is perpendicular to the diagonal AC ? <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741072362516.png\" alt=\"rId55\" width=\"184\" height=\"116\"></p>",
                    question_hi: "<p>58. उस चतुर्भुज ABCD का क्षेत्रफल क्या है, (नीचे दर्शाया गया है) जिसमें भुजाएं AB और BC समान हैं, भुजाएं AD और CD की लंबाई क्रमशः 5 cm और 13 cm हैं, और भुजा AD, विकर्ण AC के लंबवत है?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741072362516.png\" alt=\"rId55\" width=\"184\" height=\"116\"></p>",
                    options_en: [
                        "<p>78 cm&sup2;</p>",
                        "<p>75 cm&sup2;</p>",
                        "<p>82 cm&sup2;</p>",
                        "<p>80 cm&sup2;</p>"
                    ],
                    options_hi: [
                        "<p>78 cm&sup2;</p>",
                        "<p>75 cm&sup2;</p>",
                        "<p>82 cm&sup2;</p>",
                        "<p>80 cm&sup2;</p>"
                    ],
                    solution_en: "<p>58.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741072362516.png\" alt=\"rId55\" width=\"184\" height=\"116\"><br>△DAC is right triangle. Then, AC = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mn>13</mn><mn>2</mn></msup><mo>-</mo><msup><mn>5</mn><mn>2</mn></msup></msqrt></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>144</mn></msqrt></math> = 12 cm<br>AE = EC = 6 cm (△BCE &cong; △BAE)<br>Now, △BEC is right triangle. Then, BE = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mn>10</mn><mn>2</mn></msup><mo>-</mo><msup><mn>6</mn><mn>2</mn></msup></msqrt></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>64</mn></msqrt></math> = 8 cm<br>Area of quadrilateral = area of (△ABC) + area of (△ADC)<br>= <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> &times; BE &times; AC + <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> &times; AD &times; AC<br>= <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> &times; AC(BE + AD)&nbsp;<br>= <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> &times; 12(8 + 5)&nbsp;<br>= 78 cm<sup>2</sup></p>",
                    solution_hi: "<p>58.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741072362516.png\" alt=\"rId55\" width=\"184\" height=\"116\"><br>△DAC समकोण त्रिभुज है. तब, AC = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mn>13</mn><mn>2</mn></msup><mo>-</mo><msup><mn>5</mn><mn>2</mn></msup></msqrt></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>144</mn></msqrt></math> = 12 cm<br>AE = EC = 6 cm (△BCE &cong; △BAE)<br>अब, △BEC समकोण त्रिभुज है। तब, BE = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mn>10</mn><mn>2</mn></msup><mo>-</mo><msup><mn>6</mn><mn>2</mn></msup></msqrt></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>64</mn></msqrt></math> = 8 cm<br>चतुर्भुज का क्षेत्रफल = (△ABC)का क्षेत्रफल + (△ADC) का क्षेत्रफल <br>= <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> &times; BE &times; AC + <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> &times; AD &times; AC<br>= <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> &times; AC(BE + AD)&nbsp;<br>= <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> &times; 12(8 + 5)&nbsp;<br>= 78 cm<sup>2</sup></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "59",
                    section: "17",
                    question_en: "<p>59. If x = 11 , then the value of (x<sup>5</sup> -12x<sup>4</sup> + 12x<sup>3</sup> - 12x<sup>2</sup> + 12x -1)</p>",
                    question_hi: "<p>59. यदि x&nbsp;= 11 है, तो (x<sup>5</sup> -12x<sup>4</sup> + 12x<sup>3</sup> - 12x<sup>2</sup> + 12x -1) का मान बताइए।</p>",
                    options_en: [
                        "<p>10</p>",
                        "<p>8</p>",
                        "<p>9</p>",
                        "<p>14</p>"
                    ],
                    options_hi: [
                        "<p>10</p>",
                        "<p>8</p>",
                        "<p>9</p>",
                        "<p>14</p>"
                    ],
                    solution_en: "<p>59.(a) x<sup>5</sup> -12x<sup>4</sup> + 12x<sup>3</sup> - 12x<sup>2</sup> + 12x -1<br>x<sup>5</sup> -11x<sup>4</sup> - x<sup>4</sup> + 11x<sup>3</sup> + x<sup>3</sup> - 11x<sup>2</sup> - x<sup>2</sup> + 11x + x -1&nbsp;<br>Now, x&nbsp;= 11 &hellip;.(given)<br>On putting value of 11 as x&nbsp;, we get<br>x<sup>5</sup> - x &times; x<sup>4</sup> - x<sup>4</sup> + x &times; x<sup>3</sup> + x<sup>3</sup> - x &times; x<sup>2</sup> - x<sup>2</sup> + x &times; x + x -1&nbsp;<br>x<sup>5</sup> - x<sup>5</sup> - x<sup>4</sup> + x<sup>4</sup> + x<sup>3</sup> - x<sup>3</sup> - x<sup>2</sup> + x<sup>2</sup> + x - 1<br>&rArr; x - 1&nbsp;= 11 - 1&nbsp;= 10</p>",
                    solution_hi: "<p>59.(a) x<sup>5</sup> -12x<sup>4</sup> + 12x<sup>3</sup> - 12x<sup>2</sup> + 12x -1<br>x<sup>5</sup> -11x<sup>4</sup> - x<sup>4</sup> + 11x<sup>3</sup> + x<sup>3</sup> - 11x<sup>2</sup> - x<sup>2</sup> + 11x + x -1&nbsp;<br>अब, x&nbsp;= 11 &hellip;.(दिया गया है)<br>11 का मान x&nbsp;, रखने पर हमें प्राप्त होता है<br>x<sup>5</sup> - x &times; x<sup>4</sup> - x<sup>4</sup> + x &times; x<sup>3</sup> + x<sup>3</sup> - x &times; x<sup>2</sup> - x<sup>2</sup> + x &times; x + x -1&nbsp;<br>x<sup>5</sup> - x<sup>5</sup> - x<sup>4</sup> + x<sup>4</sup> + x<sup>3</sup> - x<sup>3</sup> - x<sup>2</sup> + x<sup>2</sup> + x - 1<br>&rArr; x - 1 = 11 - 1 = 10</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "60",
                    section: "17",
                    question_en: "<p>60. The length of the largest possible rod that can be placed in a cubical room is 42<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math> m. The surface area (in m&sup2;) of the largest possible sphere that fit within the cubical room is: [Use &pi; = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>22</mn><mn>7</mn></mfrac></math>]</p>",
                    question_hi: "<p>60. एक घनाकार कमरे में रखी जा सकने वाली छड़ की अधिकतम संभावित लंबाई 42<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math> m है। घनाकार कक्ष के भीतर फिट होने वाले गोले का अधिकतम संभावित पृष्ठीय क्षेत्रफल (m&sup2; में) क्या होगा?&nbsp;[&pi; =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>22</mn><mn>7</mn></mfrac></math>&nbsp;लीजिए।]</p>",
                    options_en: [
                        "<p>3590</p>",
                        "<p>4589</p>",
                        "<p>2564</p>",
                        "<p>5544</p>"
                    ],
                    options_hi: [
                        "<p>3590</p>",
                        "<p>4589</p>",
                        "<p>2564</p>",
                        "<p>5544</p>"
                    ],
                    solution_en: "<p>60.(d)<br>Length of the largest possible rod which can be placed in cubical room = <math display=\"inline\"><msqrt><mn>3</mn></msqrt></math>a<br>42<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math> = <math display=\"inline\"><msqrt><mn>3</mn></msqrt></math>a<br>a = 42<br>Required TSA of sphere = 4 &times; <math display=\"inline\"><mfrac><mrow><mn>22</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> &times; (<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>42</mn><mn>2</mn></mfrac></math>)<sup>2</sup> = 5544 m<sup>2</sup></p>",
                    solution_hi: "<p>60.(d)<br>घनाकार कमरे में रखी जा सकने वाली अधिकतम संभव छड़ की लंबाई = <math display=\"inline\"><msqrt><mn>3</mn></msqrt></math>a<br>42<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math> = <math display=\"inline\"><msqrt><mn>3</mn></msqrt></math>a<br>a = 42<br>गोले का आवश्यक कुल पृष्ठीय क्षेत्रफल = 4 &times; <math display=\"inline\"><mfrac><mrow><mn>22</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> &times; (<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>42</mn><mn>2</mn></mfrac></math>)<sup>2</sup> = 5544 m<sup>2</sup></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "61",
                    section: "17",
                    question_en: "<p>61. (The smallest common multiple of 16, 24 and 36) &divide;&nbsp;(the largest common factor of 16, 24 and 36) is equal to:</p>",
                    question_hi: "<p>61. (16, 24 और 36 का सबसे छोटा सार्वगुणज) &divide; (16, 24 और 36 का सबसे बड़ा सार्व गुणनखंड) बराबर है :</p>",
                    options_en: [
                        "<p>36</p>",
                        "<p>48</p>",
                        "<p>16</p>",
                        "<p>24</p>"
                    ],
                    options_hi: [
                        "<p>36</p>",
                        "<p>48</p>",
                        "<p>16</p>",
                        "<p>24</p>"
                    ],
                    solution_en: "<p>61.(a)<br>The LCM of 16, 24 and 36 = 2<sup>4</sup> &times; 3<sup>2</sup> = 144<br>The HCF of 16, 24 and 36 = 2<sup>2</sup> = 4<br>According to question,<br>144 &divide; 4 = 36.</p>",
                    solution_hi: "<p>61.(a)<br>16, 24 और 36 का सबसे लघुत्तम समापवर्त्य = 2<sup>4</sup> &times; 3<sup>2</sup> = 144<br>16, 24 और 36 का महत्तम समापवर्तक = 2<sup>2</sup> = 4<br>प्रश्न के अनुसार,<br>144 &divide; 4 = 36.</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "62",
                    section: "17",
                    question_en: "<p>62. In what ratio must Suhail, a wholesaler, mix wheat worth ₹45 per kg with wheat worth ₹75 per kg,&nbsp;so that by selling the mixture at ₹80 per kg, he may gain 25%?</p>",
                    question_hi: "<p>62. एक थोक विक्रेता सुहैल को ₹45 प्रति किलोग्राम मूल्य वाले गेहूँ को ₹75 प्रति किलोग्राम मूल्य वाले गेहूँ के साथ किस अनुपात में मिलाना चाहिए, ताकि इस मिश्रण को ₹80 प्रति किलोग्राम पर बेचकर वह 25% का लाभ अर्जित कर सके?</p>",
                    options_en: [
                        "<p>13 : 19</p>",
                        "<p>11 : 19</p>",
                        "<p>13 : 17</p>",
                        "<p>11 : 17</p>"
                    ],
                    options_hi: [
                        "<p>13 : 19</p>",
                        "<p>11 : 19</p>",
                        "<p>13 : 17</p>",
                        "<p>11 : 17</p>"
                    ],
                    solution_en: "<p>62.(b) Selling price of the mixture = ₹80/kg , Gain = 25%<br>Cost price of the mixture = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>Selling</mi><mi mathvariant=\"normal\">&#160;</mi><mi>price</mi></mrow><mrow><mn>1</mn><mo>+</mo><mi>Gain</mi><mi mathvariant=\"normal\">&#160;</mi><mi>percentage</mi></mrow></mfrac></math>​ = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>80</mn><mrow><mn>1</mn><mo>+</mo><mn>0</mn><mo>.</mo><mn>25</mn></mrow></mfrac></math>​ ​= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>80</mn><mrow><mn>1</mn><mo>.</mo><mn>25</mn></mrow></mfrac></math>​ ​​= ₹64 per kg<br>Cheaper wheat = ₹45/kg<br>Costlier wheat = ₹75/kg<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741072362823.png\" alt=\"rId57\" width=\"82\" height=\"77\"><br>Required ratio = 11 : 19</p>",
                    solution_hi: "<p>62.(b) मिश्रण का विक्रय मूल्य = ₹80/किग्रा, लाभ = 25%<br>मिश्रण का लागत मूल्य = <math style=\"font-family:Verdana\" display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2357;&#2367;&#2325;&#2381;&#2352;&#2351;</mi><mi mathvariant=\"normal\">&#160;</mi><mi>&#2350;&#2370;&#2354;&#2381;&#2351;</mi></mrow><mrow><mn>1</mn><mo>+</mo><mi>&#2354;&#2366;&#2349;</mi><mo>&#160;%</mo></mrow></mfrac></math>​ = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>80</mn><mrow><mn>1</mn><mo>+</mo><mn>0</mn><mo>.</mo><mn>25</mn></mrow></mfrac></math>​ ​= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>80</mn><mrow><mn>1</mn><mo>.</mo><mn>25</mn></mrow></mfrac></math>​ ​​= ₹64/किग्रा<br>सस्ता गेहूं = ₹45/किग्रा<br>महंगा गेहूं = ₹75/किग्रा<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741072362823.png\" alt=\"rId57\" width=\"82\" height=\"77\"><br>आवश्यक अनुपात = 11 : 19</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "63",
                    section: "17",
                    question_en: "<p>63. The resistance of a wire is directly proportional to its length and inversely proportional to the square of its radius. Two wires of the same material have the same resistance and their radii are in the ratio of 7 : 8. The corresponding lengths of the wires are 147 cm and L cm, respectively. Find the value of L.</p>",
                    question_hi: "<p>63. किसी तार का प्रतिरोध उसकी लंबाई के अनुक्रमानुपाती और उसकी त्रिज्या के वर्ग के व्युत्क्रमानुपाती है। एक ही पदार्थ के दो तारों का प्रतिरोध समान है और उनकी त्रिज्याएँ 7 : 8 के अनुपात में हैं। तारों की संगत लंबाई क्रमशः 147 cm और L cm है। L का मान ज्ञात कीजिए।</p>",
                    options_en: [
                        "<p>168</p>",
                        "<p>192</p>",
                        "<p>128</p>",
                        "<p>158</p>"
                    ],
                    options_hi: [
                        "<p>168</p>",
                        "<p>192</p>",
                        "<p>128</p>",
                        "<p>158</p>"
                    ],
                    solution_en: "<p>63.(b) According to question <br>Resistance (R) &prop; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>Length</mi><mi mathvariant=\"normal\">&#160;</mi></mrow><msup><mrow><mi>radius</mi><mi mathvariant=\"normal\">&#160;</mi></mrow><mn>2</mn></msup></mfrac></math> <br>R = k<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"normal\">l</mi><msup><mi mathvariant=\"normal\">r</mi><mn>2</mn></msup></mfrac></math> &hellip;. (i)<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msub><mi mathvariant=\"normal\">r</mi><mn>1</mn></msub><msub><mi mathvariant=\"normal\">r</mi><mn>2</mn></msub></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>8</mn></mfrac></math><br>&rArr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi mathvariant=\"normal\">k</mi><mo>(</mo><mn>147</mn><mo>)</mo></mrow><mn>49</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi mathvariant=\"normal\">k</mi><mo>(</mo><mi mathvariant=\"normal\">L</mi><mo>)</mo></mrow><mn>64</mn></mfrac></math><br>&rArr; L = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>147</mn><mo>&#215;</mo><mn>64</mn></mrow><mn>49</mn></mfrac></math> = 192cm</p>",
                    solution_hi: "<p>63.(b) प्रश्नानुसार<br>प्रतिरोध (R) &prop; <math style=\"font-family: Verdana;\" display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2354;&#2306;&#2348;&#2366;&#2312;</mi><mi mathvariant=\"normal\">&#160;</mi></mrow><msup><mrow><mi>&#2340;&#2381;&#2352;&#2367;&#2332;&#2381;&#2351;&#2366;</mi><mi mathvariant=\"normal\">&#160;</mi></mrow><mn>2</mn></msup></mfrac></math> <br>R = k<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"normal\">l</mi><msup><mi mathvariant=\"normal\">r</mi><mn>2</mn></msup></mfrac></math> &hellip;. (i)<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msub><mi mathvariant=\"normal\">r</mi><mn>1</mn></msub><msub><mi mathvariant=\"normal\">r</mi><mn>2</mn></msub></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>8</mn></mfrac></math><br>&rArr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi mathvariant=\"normal\">k</mi><mo>(</mo><mn>147</mn><mo>)</mo></mrow><mn>49</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi mathvariant=\"normal\">k</mi><mo>(</mo><mi mathvariant=\"normal\">L</mi><mo>)</mo></mrow><mn>64</mn></mfrac></math><br>&rArr; L = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>147</mn><mo>&#215;</mo><mn>64</mn></mrow><mn>49</mn></mfrac></math> = 192cm</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "64",
                    section: "17",
                    question_en: "<p>64. P and Q together can complete a work in 30 days. Q and R together can complete the same work in 37.5 days. R and P together can complete the same work in 150 days. In how many days can all the three complete the same work while working together ?</p>",
                    question_hi: "<p>64. P और Q मिलकर एक काम को 30 दिनों में पूरा कर सकते हैं। Q और R मिलकर उसी काम को 37.5 दिनों में पूरा कर सकते हैं। R और P मिलकर उसी काम को 150 दिनों में पूरा कर सकते हैं। तीनों एक साथ काम करते हुए उसी काम को कितने दिनों में पूरा कर सकते हैं?</p>",
                    options_en: [
                        "<p>20 days</p>",
                        "<p>25 days</p>",
                        "<p>35 days</p>",
                        "<p>30 days</p>"
                    ],
                    options_hi: [
                        "<p>20 दिन</p>",
                        "<p>25 दिन</p>",
                        "<p>35 दिन</p>",
                        "<p>30 दिन</p>"
                    ],
                    solution_en: "<p>64.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741072362955.png\" alt=\"rId58\" width=\"199\" height=\"121\"><br>Efficiency (P + Q + R) =&nbsp;<math display=\"inline\"><mfrac><mrow><mn>25</mn><mo>+</mo><mn>20</mn><mo>+</mo><mn>5</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>50</mn><mn>2</mn></mfrac></math> = 25 unit<br>Time taken by all of them to complete the whole work = <math display=\"inline\"><mfrac><mrow><mn>750</mn></mrow><mrow><mn>25</mn></mrow></mfrac></math> = 30 days</p>",
                    solution_hi: "<p>64.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741072363143.png\" alt=\"rId59\" width=\"198\" height=\"129\"><br>दक्षता (P + Q + R) =&nbsp;<math display=\"inline\"><mfrac><mrow><mn>25</mn><mo>+</mo><mn>20</mn><mo>+</mo><mn>5</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>50</mn><mn>2</mn></mfrac></math> = 25 इकाई <br>उन सभी द्वारा पूरा कार्य पूरा करने में लिया गया समय = <math display=\"inline\"><mfrac><mrow><mn>750</mn></mrow><mrow><mn>25</mn></mrow></mfrac></math> = 30 दिन</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "65",
                    section: "17",
                    question_en: "<p>65. If sin &theta; = <math display=\"inline\"><mfrac><mrow><mn>11</mn></mrow><mrow><mn>12</mn></mrow></mfrac></math>, then what will be the value of (tan&theta; + sec&theta;) ?</p>",
                    question_hi: "<p>65. यदि sin &theta; = <math display=\"inline\"><mfrac><mrow><mn>11</mn></mrow><mrow><mn>12</mn></mrow></mfrac></math> है, तो (tan&theta; + sec&theta;) का मान कितना होगा ?</p>",
                    options_en: [
                        "<p><math display=\"inline\"><mfrac><mrow><mn>12</mn></mrow><mrow><msqrt><mn>23</mn></msqrt></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>11</mn></mrow><mrow><msqrt><mn>23</mn></msqrt></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><msqrt><mn>23</mn></msqrt></math></p>",
                        "<p>23</p>"
                    ],
                    options_hi: [
                        "<p><math display=\"inline\"><mfrac><mrow><mn>12</mn></mrow><mrow><msqrt><mn>23</mn></msqrt></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>11</mn></mrow><mrow><msqrt><mn>23</mn></msqrt></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><msqrt><mn>23</mn></msqrt></math></p>",
                        "<p>23</p>"
                    ],
                    solution_en: "<p>65.(c)<br>sin &theta; = <math display=\"inline\"><mfrac><mrow><mn>11</mn></mrow><mrow><mn>12</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"normal\">p</mi><mi mathvariant=\"normal\">h</mi></mfrac></math><br>b = <math display=\"inline\"><msqrt><msup><mrow><mo>(</mo><mn>12</mn><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup><mo>-</mo><msup><mrow><mo>(</mo><mn>11</mn><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup></msqrt></math><br>b = <math display=\"inline\"><msqrt><mn>144</mn><mo>-</mo><mn>121</mn></msqrt></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>23</mn></msqrt></math><br>According to the question,<br>&rArr; (tan&theta; + sec&theta;) = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"normal\">p</mi><mi mathvariant=\"normal\">b</mi></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"normal\">h</mi><mi mathvariant=\"normal\">b</mi></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi mathvariant=\"normal\">p</mi><mo>+</mo><mi mathvariant=\"normal\">h</mi></mrow><mi mathvariant=\"normal\">b</mi></mfrac></math><br>&rArr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>11</mn><mo>+</mo><mn>12</mn></mrow><msqrt><mn>23</mn></msqrt></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>23</mn><msqrt><mn>23</mn></msqrt></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>23</mn></msqrt></math></p>",
                    solution_hi: "<p>65.(c)<br>sin &theta; = <math display=\"inline\"><mfrac><mrow><mn>11</mn></mrow><mrow><mn>12</mn></mrow></mfrac></math> =&nbsp;<math style=\"font-family:Verdana\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>&#2354;&#2306;&#2348;</mi><mi>&#2325;&#2352;&#2381;&#2339;</mi></mfrac></math>&nbsp;<br>आधार = <math display=\"inline\"><msqrt><msup><mrow><mo>(</mo><mn>12</mn><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup><mo>-</mo><msup><mrow><mo>(</mo><mn>11</mn><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup></msqrt></math><br>आधार = <math display=\"inline\"><msqrt><mn>144</mn><mo>-</mo><mn>121</mn></msqrt></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>23</mn></msqrt></math><br>प्रश्न के अनुसार,<br>&rArr; (tan&theta; + sec&theta;) = <math style=\"font-family:Verdana\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>&#2354;&#2306;&#2348;</mi><mi>&#2310;&#2343;&#2366;&#2352;</mi></mfrac></math> + <math style=\"font-family:Verdana\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>&#2325;&#2352;&#2381;&#2339;</mi><mi>&#2310;&#2343;&#2366;&#2352;</mi></mfrac></math> = <math style=\"font-family:Verdana\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2354;&#2306;&#2348;</mi><mo>+</mo><mi>&#2325;&#2352;&#2381;&#2339;</mi></mrow><mi>&#2310;&#2343;&#2366;&#2352;</mi></mfrac></math> <br>&rArr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>11</mn><mo>+</mo><mn>12</mn></mrow><msqrt><mn>23</mn></msqrt></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>23</mn><msqrt><mn>23</mn></msqrt></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>23</mn></msqrt></math></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "66",
                    section: "17",
                    question_en: "<p>66. A man walks to a viewpoint and returns to the starting point by his car maintaining constant speed and thus takes a total time of 6 hours 15 minutes. He would have gained 2 hours by driving both ways. How long would it have taken for him to walk both ways with the same walking speed ?</p>",
                    question_hi: "<p>66. एक व्यक्ति एक व्यूपॉइंट तक पैदल जाता है और अपनी कार से अपरिवर्ती चाल बनाए रखते हुए लौटता है और इस प्रकार उसे कुल 6 घंटे 15 मिनट का समय लगता है। दोनों तरफ कार से यात्रा करने में उसे 2 घंटे कम लगतेI दोनों ओर समान चाल से पैदल यात्रा करने में उसे कुल कितना समय लगता ?</p>",
                    options_en: [
                        "<p>8 hours 45 minutes</p>",
                        "<p>9 hours 30 minutes</p>",
                        "<p>7 hours 15 minutes</p>",
                        "<p>8 hours 15 minutes</p>"
                    ],
                    options_hi: [
                        "<p>8 घंटे 45 मिनट</p>",
                        "<p>9 घंटे 30 मिनट</p>",
                        "<p>7 घंटे 15 मिनट</p>",
                        "<p>8 घंटे 15 मिनट</p>"
                    ],
                    solution_en: "<p>66.(d) <br>Let the distance = x&nbsp;km, <br>walking&rsquo;s speed = w km/h <br>and car&rsquo;s speed = c km/h<br>According to question,<br>&rArr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>x</mi><mi>w</mi></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>x</mi><mi>c</mi></mfrac></math>&nbsp;= 6<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>4</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>25</mn><mn>4</mn></mfrac></math> &hellip;&hellip;(i)<br>&rArr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><mi>x</mi></mrow><mi>c</mi></mfrac></math> = 4<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>4</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>17</mn><mn>4</mn></mfrac></math><br>&rArr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>x</mi><mi>c</mi></mfrac></math>&nbsp;= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>17</mn><mn>8</mn></mfrac></math><br>Putting the above value in equation (i)<br>&rArr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>x</mi><mi>w</mi></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>17</mn><mn>8</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>25</mn><mn>4</mn></mfrac></math><br>&rArr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>x</mi><mi>w</mi></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>25</mn><mn>4</mn></mfrac></math> - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>17</mn><mn>8</mn></mfrac></math><br>&rArr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>x</mi><mi>w</mi></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>50</mn><mo>-</mo><mn>17</mn></mrow><mn>8</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>33</mn><mn>8</mn></mfrac></math> hours <br>Time taken by walking both way = 2 &times; <math display=\"inline\"><mfrac><mrow><mn>33</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math> = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>33</mn><mn>4</mn></mfrac></math> = 8 hours 15 minutes</p>",
                    solution_hi: "<p>66.(d) <br>माना दूरी = x&nbsp;km, <br>चलने की गति = w km/h <br>और कार की गति = c km/h<br>प्रश्न के अनुसार,<br>&rArr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>x</mi><mi>w</mi></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>x</mi><mi>c</mi></mfrac></math>&nbsp;= 6<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>4</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>25</mn><mn>4</mn></mfrac></math> &hellip;&hellip;(i)<br>&rArr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><mi>x</mi></mrow><mi>c</mi></mfrac></math> = 4<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>4</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>17</mn><mn>4</mn></mfrac></math><br>&rArr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>x</mi><mi>c</mi></mfrac></math>&nbsp;= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>17</mn><mn>8</mn></mfrac></math><br>उपरोक्त मान को समीकरण में रखना (i)<br>&rArr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>x</mi><mi>w</mi></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>17</mn><mn>8</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>25</mn><mn>4</mn></mfrac></math><br>&rArr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>x</mi><mi>w</mi></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>25</mn><mn>4</mn></mfrac></math> - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>17</mn><mn>8</mn></mfrac></math><br>&rArr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>x</mi><mi>w</mi></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>50</mn><mo>-</mo><mn>17</mn></mrow><mn>8</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>33</mn><mn>8</mn></mfrac></math> घंटे<br>दोनों तरफ चलने में समय लगा = 2 &times; <math display=\"inline\"><mfrac><mrow><mn>33</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math> = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>33</mn><mn>4</mn></mfrac></math> = 8 घंटे 15 मिनट</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "67",
                    section: "17",
                    question_en: "<p>67. From the top and bottom of 200 m high building, the angles of elevation of the top of a tower are 30&deg; and 45&deg; respectively. What is the height (in m) of the tower?</p>",
                    question_hi: "<p>67. 200 मीटर ऊंची इमारत के ऊपर और नीचे से, एक टावर के शीर्ष के उन्नयन कोण क्रमशः 30&deg; और 45&deg; हैं। टावर की ऊंचाई (मी में) क्या है?</p>",
                    options_en: [
                        "<p>100(<math display=\"inline\"><msqrt><mn>3</mn></msqrt><mo>+</mo><mn>1</mn></math>)</p>",
                        "<p>300(<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math>+1)</p>",
                        "<p>100<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math>(<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math>+1)</p>",
                        "<p>100<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math>(<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math>-1)</p>"
                    ],
                    options_hi: [
                        "<p>100(<math display=\"inline\"><msqrt><mn>3</mn></msqrt><mo>+</mo><mn>1</mn></math>)</p>",
                        "<p>300(<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math>+1)</p>",
                        "<p>100<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math>(<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math>+1)</p>",
                        "<p>100<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math>(<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math>-1)</p>"
                    ],
                    solution_en: "<p>67.(c) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741072363291.png\" alt=\"rId60\"><br><strong>Given ,</strong> CD = 200m , BE = 200 m , AE = x meter<br>In triangle ABC <br>tan&theta; = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>A</mi><mi>B</mi></mrow><mrow><mi>B</mi><mi>C</mi></mrow></mfrac></math> <br>tan45&deg; = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>200</mn><mo>+</mo><mi>x</mi></mrow><mrow><mi>B</mi><mi>C</mi></mrow></mfrac></math> <br>1 = <math display=\"inline\"><mfrac><mrow><mn>200</mn><mo>+</mo><mi>x</mi><mi>&#160;</mi></mrow><mrow><mi>B</mi><mi>C</mi><mi>&#160;</mi></mrow></mfrac></math><br>BC = 200 + x<br>ED = BC = 200 + x<br>Then , triangle AED<br>tan&theta; = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>A</mi><mi>E</mi></mrow><mrow><mi>E</mi><mi>D</mi></mrow></mfrac></math> <br>tan 30&deg; = <math display=\"inline\"><mfrac><mrow><mi>x</mi><mi>&#160;</mi></mrow><mrow><mn>200</mn><mo>+</mo><mi>x</mi></mrow></mfrac></math><br><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mo>&#8730;</mo><mn>3</mn></mrow></mfrac></math>= <math display=\"inline\"><mfrac><mrow><mi>x</mi><mi>&#160;</mi></mrow><mrow><mn>200</mn><mo>+</mo><mi>x</mi></mrow></mfrac></math><br>x= <math display=\"inline\"><mfrac><mrow><mn>200</mn></mrow><mrow><mo>(</mo><msqrt><mn>3</mn></msqrt><mo>-</mo><mn>1</mn><mo>)</mo></mrow></mfrac></math><br>x = 100(<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math> +1)<br>Hence , <br>AB = 200 + x <br>= 200 + 100<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math>+100<br>= 300 + 100<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math><br>= 100<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math> (<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math>+1)</p>",
                    solution_hi: "<p>67.(c) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741072363291.png\" alt=\"rId60\" width=\"151\" height=\"128\"><br>दिया गया है, CD = 200m , BE = 200 m , AE = x मीटर <br>त्रिभुज ABC में,<br>tan&theta; = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>A</mi><mi>B</mi></mrow><mrow><mi>B</mi><mi>C</mi></mrow></mfrac></math> <br>tan45&deg; = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>200</mn><mo>+</mo><mi>x</mi></mrow><mrow><mi>B</mi><mi>C</mi></mrow></mfrac></math> <br>1 = <math display=\"inline\"><mfrac><mrow><mn>200</mn><mo>+</mo><mi>x</mi><mi>&#160;</mi></mrow><mrow><mi>B</mi><mi>C</mi><mi>&#160;</mi></mrow></mfrac></math><br>BC = 200 + x<br>ED = BC = 200 + x<br>तब त्रिभुज AED में,<br>tan&theta; = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>A</mi><mi>E</mi></mrow><mrow><mi>E</mi><mi>D</mi></mrow></mfrac></math> <br>tan 30&deg; = <math display=\"inline\"><mfrac><mrow><mi>x</mi><mi>&#160;</mi></mrow><mrow><mn>200</mn><mo>+</mo><mi>x</mi></mrow></mfrac></math><br><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mo>&#8730;</mo><mn>3</mn></mrow></mfrac></math>= <math display=\"inline\"><mfrac><mrow><mi>x</mi><mi>&#160;</mi></mrow><mrow><mn>200</mn><mo>+</mo><mi>x</mi></mrow></mfrac></math><br>x= <math display=\"inline\"><mfrac><mrow><mn>200</mn></mrow><mrow><mo>(</mo><msqrt><mn>3</mn></msqrt><mo>-</mo><mn>1</mn><mo>)</mo></mrow></mfrac></math><br>x = 100(<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math> +1)<br>अत:, <br>AB = 200 + x <br>= 200 + 100<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math>+100<br>= 300 + 100<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math><br>= 100<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math> (<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math>+1)</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "68",
                    section: "17",
                    question_en: "<p>68. 400 oranges were bought at ₹1410 per hundred and were sold at a profit of ₹960. Find the selling price (in ₹) per dozen of oranges.</p>",
                    question_hi: "<p>68. ₹1410 प्रति सैकड़े की दर से 400 संतरे खरीदे गए और ₹960 के लाभ पर बेचे गए। प्रति दर्जन संतरों का विक्रय मूल्य (₹ में) ज्ञात करें।</p>",
                    options_en: [
                        "<p>213</p>",
                        "<p>188</p>",
                        "<p>208</p>",
                        "<p>198 </p>"
                    ],
                    options_hi: [
                        "<p>213</p>",
                        "<p>188</p>",
                        "<p>208</p>",
                        "<p>198</p>"
                    ],
                    solution_en: "<p>68.(d) According to question,<br>CP of 100 oranges = 1410<br>CP of 400 oranges = <math display=\"inline\"><mfrac><mrow><mn>1410</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> &times; 400 = ₹ 5640<br>SP of 400 oranges = 5640 + 960 = ₹ 6600<br>SP of 1 dozen oranges = <math display=\"inline\"><mfrac><mrow><mn>6600</mn></mrow><mrow><mn>400</mn></mrow></mfrac></math> &times; 12 = ₹ 198</p>",
                    solution_hi: "<p>68.(d) प्रश्न के अनुसार,<br>100 संतरे का क्रय मूल्य = 1410<br>400 संतरे का क्रय मूल्य = <math display=\"inline\"><mfrac><mrow><mn>1410</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> &times; 400 = ₹ 5640<br>400 संतरे का विक्रय मूल्य = 5640 + 960 = ₹ 6600<br>1 दर्जन संतरे का विक्रय मूल्य = <math display=\"inline\"><mfrac><mrow><mn>6600</mn></mrow><mrow><mn>400</mn></mrow></mfrac></math> &times; 12 = ₹ 198</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "69",
                    section: "17",
                    question_en: "<p>69. Side BC of a ∆ABC is extended upto D such that CD = AC. If &ang;BAD = 118&deg; and &ang;ACB = 80&deg;, then the value of &ang;ABC is equal to:</p>",
                    question_hi: "<p>69. △ABC की भुजा BC को D तक इस प्रकार बढ़ाया जाता है कि CD = AC होती है। यदि &ang;BAD = 118&deg; है और &ang;ACB = 80&deg; है, तो &ang;ABC का मान होगा।</p>",
                    options_en: [
                        "<p>36&deg;</p>",
                        "<p>27&deg;</p>",
                        "<p>22&deg;</p>",
                        "<p>32&deg;</p>"
                    ],
                    options_hi: [
                        "<p>36&deg;</p>",
                        "<p>27&deg;</p>",
                        "<p>22&deg;</p>",
                        "<p>32&deg;</p>"
                    ],
                    solution_en: "<p>69.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741072363486.png\" alt=\"rId61\" width=\"207\" height=\"100\"><br>&ang;ACD = 180 - 80 = 100&deg; [sum of angles at one side of linear line is 180&deg;]<br>Side, AC = CD <br>Then, &ang;CAD = &ang;ADC<br>In &Delta;CAD<br>2&ang;CAD + 100&deg; = 180&deg; <br>&ang;CAD = 40&deg;<br>Now, &ang;BAC = 118 - 40 = 78&deg;<br>In &Delta;ABC<br>&ang;BAC + &ang;ACB + &ang;CBA = 180&deg;<br>78&deg; + 80&deg; + &ang;ABC = 180&deg;<br>&ang;ABC = 22&deg;</p>",
                    solution_hi: "<p>69.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741072363486.png\" alt=\"rId61\" width=\"207\" height=\"100\"><br>&ang;ACD = 180 - 80 = 100&deg; [रैखिक रेखा के एक ओर के कोणों का योग 180&deg; है]<br>भुजा, AC = CD <br>फिर, &ang;CAD = &ang;ADC<br>&Delta;CAD में<br>2&ang;CAD + 100&deg; = 180&deg; <br>&ang;CAD = 40&deg;<br>अब, &ang;BAC = 118 - 40 = 78&deg;<br>&Delta;ABC में<br>&ang;BAC + &ang;ACB + &ang;CBA = 180&deg;<br>78&deg; + 80&deg; + &ang;ABC = 180&deg;<br>&ang;ABC = 22&deg;</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "70",
                    section: "17",
                    question_en: "<p>70. The price of an article is first increased by 40% and then decreased by 45%, due to reduction in sales. Find the net percentage change in the final price of the article.</p>",
                    question_hi: "<p>70. किसी वस्तु की कीमत में पहले 40% की वृद्धि की जाती है और फिर बिक्री में कमी होने के कारण, 45% की कमी की जाती है। वस्तु के अंतिम मूल्य में कुल प्रतिशत परिवर्तन ज्ञात कीजिए।</p>",
                    options_en: [
                        "<p>-23%</p>",
                        "<p>23%</p>",
                        "<p>13%</p>",
                        "<p>-13%</p>"
                    ],
                    options_hi: [
                        "<p>-23%</p>",
                        "<p>23%</p>",
                        "<p>13%</p>",
                        "<p>-13%</p>"
                    ],
                    solution_en: "<p>70.(a)<br>%Change = 40 - 45 + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>40</mn><mo>&#215;</mo><mo>(</mo><mo>-</mo><mn>45</mn><mo>)</mo></mrow><mn>100</mn></mfrac></math> = - 23%</p>",
                    solution_hi: "<p>70.(a)<br>%परिवर्तन = 40 - 45 + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>40</mn><mo>&#215;</mo><mo>(</mo><mo>-</mo><mn>45</mn><mo>)</mo></mrow><mn>100</mn></mfrac></math>&nbsp;= - 23%</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "71",
                    section: "17",
                    question_en: "<p>71. Find the surface area (in cm&sup2;) of a cardboard box in the shape of a cuboid whose length is 60 cm, breadth is half its length and height is one-third of its breadth.</p>",
                    question_hi: "<p>71. एक घनाभ के आकार में एक गत्ते के डिब्बे का पृष्ठीय क्षेत्रफल (cm&sup2; में) ज्ञात कीजिए, जिसकी लंबाई 60 cm, चौड़ाई उसकी लंबाई की आधी और ऊंचाई उसकी चौड़ाई की एक-तिहाई है।</p>",
                    options_en: [
                        "<p>2700</p>",
                        "<p>10800</p>",
                        "<p>8100</p>",
                        "<p>5400</p>"
                    ],
                    options_hi: [
                        "<p>2700</p>",
                        "<p>10800</p>",
                        "<p>8100</p>",
                        "<p>5400</p>"
                    ],
                    solution_en: "<p>71.(d)<br>Length of cuboid = 60 cm<br>Breadth of cuboid = 60 &times; <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> = 30 cm<br>Height of cuboid = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> &times; 30 = 10 cm<br>TSA of cardboard box = 2(lb + bh + hl)&nbsp;<br>= 2(60 &times; 30 + 30 &times; 10 + 10 &times; 60)<br>= 2(1800 + 300 + 600)&nbsp;<br>= 2 &times; 2700&nbsp;<br>= 5400 cm<sup>2</sup></p>",
                    solution_hi: "<p>71.(d)<br>घनाभ की लंबाई = 60 cm<br>घनाभ की चौड़ाई = 60 &times;<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> = 30 cm<br>घनाभ की ऊँचाई = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math>&times; 30 = 10 cm<br>कोर्टबोर्ड बाॅक्स का कुल पृष्ठीय क्षेत्रफल = 2(lb + bh + hl)&nbsp;<br>= 2(60 &times; 30 + 30 &times; 10 + 10 &times; 60)<br>= 2(1800 + 300 + 600)&nbsp;<br>= 2 &times; 2700&nbsp;<br>= 5400 cm<sup>2</sup></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "72",
                    section: "17",
                    question_en: "<p>72. A part of ₹48,500 is invested at a simple interest of 15% per annum. The rest of the money is invested at the rate of 10% simple interest per annum after 2 years of the first investment. The ratio of interest after 5 years from the time when the first amount was invested is 5 : 3. How much is the second part that was invested( in ₹) at the rate of 10% simple interest ?</p>",
                    question_hi: "<p>72. ₹48,500 का एक हिस्सा 15% प्रति वर्ष के साधारण ब्याज पर निवेश किया जाता है। पहले निवेश के 2 वर्ष बाद, शेष धनराशि 10% वार्षिक साधारण ब्याज की दर से निवेश की जाती है। पहली धनराशि के निवेश के समय से 5 वर्ष बाद, ब्याज का अनुपात 5 : 3 है। 10% साधारण ब्याज की दर पर निवेश किया गया दूसरा हिस्सा (₹ में) कितना है ?</p>",
                    options_en: [
                        "<p>20,940</p>",
                        "<p>29,100</p>",
                        "<p>24,900</p>",
                        "<p>19,400</p>"
                    ],
                    options_hi: [
                        "<p>20,940</p>",
                        "<p>29,100</p>",
                        "<p>24,900</p>",
                        "<p>19,400</p>"
                    ],
                    solution_en: "<p>72.(b)<br>Let 1st part be x&nbsp;and 2<sup>nd</sup> part be y<br>x is invested for 5 years and y is invested for 3 years<br>Interest for x&nbsp;in 5 years = 5 &times; 15% = 75%<br>Interest for y&nbsp;in 3 years = 3 &times; 10% = 30%<br>According to question<br><math display=\"inline\"><mfrac><mrow><mn>75</mn><mi>%</mi><mi>&#160;</mi><mi>o</mi><mi>f</mi><mi>&#160;</mi><mi>x</mi></mrow><mrow><mn>30</mn><mi>%</mi><mi>&#160;</mi><mi>o</mi><mi>f</mi><mi>&#160;</mi><mi>y</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>3</mn></mfrac></math><br><math display=\"inline\"><mfrac><mrow><mi>&#160;</mi><mi>x</mi></mrow><mrow><mi>y</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5</mn><mo>&#215;</mo><mn>30</mn></mrow><mrow><mn>3</mn><mo>&#215;</mo><mn>75</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>3</mn></mfrac></math><br>Now second part (y) = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>5</mn></mfrac></math> &times; 48,500 = ₹ 29100</p>",
                    solution_hi: "<p>72.(b)<br>माना पहला भाग x&nbsp;और दूसरा भाग y है। <br>x को 5 साल के लिए निवेश किया गया और y को 3 साल के लिए निवेश किया गया। <br>5 वर्षों में x&nbsp;पर ब्याज = 5 &times; 15% = 75%<br>3 वर्षों में y&nbsp;पर ब्याज = 3 &times; 10% = 30%<br>प्रश्न के अनुसार,<br><math style=\"font-family:Verdana\" display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi mathvariant=\"normal\">x</mi><mi mathvariant=\"normal\">&#160;</mi><mi>&#2325;&#2366;</mi><mi mathvariant=\"normal\">&#160;</mi><mn>75</mn><mo>%</mo></mrow><mrow><mi mathvariant=\"normal\">y</mi><mi mathvariant=\"normal\">&#160;</mi><mi>&#2325;&#2366;</mi><mi mathvariant=\"normal\">&#160;</mi><mn>30</mn><mi mathvariant=\"normal\">%</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>3</mn></mfrac></math><br><math display=\"inline\"><mfrac><mrow><mi>&#160;</mi><mi>x</mi></mrow><mrow><mi>y</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5</mn><mo>&#215;</mo><mn>30</mn></mrow><mrow><mn>3</mn><mo>&#215;</mo><mn>75</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>3</mn></mfrac></math><br>अब दूसरा भाग (y) = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>5</mn></mfrac></math> &times; 48,500 = ₹ 29100</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "73",
                    section: "17",
                    question_en: "<p>73. The average of 12 numbers is 48. The average of the first 5 numbers is 45 and the average of next 4 numbers is 52. If the 10<sup>th</sup> number is 10 less than the 11<sup>th</sup> number and is 5 more than the 12<sup>th</sup> number, then the average of the 11<sup>th</sup> and 12<sup>th</sup> numbers is:</p>",
                    question_hi: "<p>73. 12 संख्याओं का औसत 48 है। पहली 5 संख्याओं का औसत 45 है और अगली 4 संख्याओं का औसत 52 है। यदि 10वीं संख्या, 11वीं संख्या से 10 कम है और 12वीं संख्या से 5 अधिक है, तो 11वीं संख्या और 12वीं संख्या का औसत क्&zwj;या है?</p>",
                    options_en: [
                        "<p>50.5</p>",
                        "<p>46.5</p>",
                        "<p>47.5</p>",
                        "<p>48.5</p>"
                    ],
                    options_hi: [
                        "<p>50.5</p>",
                        "<p>46.5</p>",
                        "<p>47.5</p>",
                        "<p>48.5</p>"
                    ],
                    solution_en: "<p>73.(d)<br>Sum of last three numbers = 12 &times; 48 - 5 &times; 45 - 4 &times; 52 = 143<br>10th number = x&nbsp;+ 5<br>11th number = x&nbsp;+ 15<br>12th number = x<br>&rArr; x + 5 + x + 15 + x = 143<br>&rArr; 3x + 20 = 143<br>&rArr; x = 41<br>Hence, average of 11th and 12th number = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><mo>(</mo><mn>41</mn><mo>)</mo><mo>+</mo><mn>15</mn></mrow><mn>2</mn></mfrac></math>&nbsp;= 48.5</p>",
                    solution_hi: "<p>73.(d)<br>अंतिम तीन संख्याओं का योग = 12 &times; 48 - 5 &times; 45 - 4 &times; 52 = 143<br>10वीं संख्या = x&nbsp;+ 5<br>11वीं संख्या = x&nbsp;+ 15<br>12वीं संख्या = x<br>&rArr; x + 5 + x + 15 + x = 143<br>&rArr; 3x + 20 = 143<br>&rArr; x = 41<br>अत: 11वीं और 12वीं संख्या का औसत&nbsp;= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><mo>(</mo><mn>41</mn><mo>)</mo><mo>+</mo><mn>15</mn></mrow><mn>2</mn></mfrac></math> = 48.5</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "74",
                    section: "17",
                    question_en: "<p>74. What will be the remainder when 1112024 is divided by 11?</p>",
                    question_hi: "<p>74. 1112024 को 11 से विभाजित करने पर शेषफल कितना प्राप्त होगा?</p>",
                    options_en: [
                        "<p>4</p>",
                        "<p>2</p>",
                        "<p>1</p>",
                        "<p>3</p>"
                    ],
                    options_hi: [
                        "<p>4</p>",
                        "<p>2</p>",
                        "<p>1</p>",
                        "<p>3</p>"
                    ],
                    solution_en: "<p>74.(c)<br><strong>Divisibility of 11 :-</strong> the difference of the sum of digits at odd position and sum of digits at even position in a number is 0 or multiple of 11 then the number divisible by 11. <br>Given number: 1112024<br>Difference = (4 + 0 + 1 + 1) - (2 + 2 + 1&nbsp;<strong>+ 1</strong>) = 0<br>Hence, required no. = 1</p>",
                    solution_hi: "<p>74.(c)<br><strong>11 की विभाज्यता :- </strong>किसी संख्या में विषम स्थिति में अंकों के योग और सम स्थिति में अंकों के योग का अंतर 0 या 11 का गुणक है तो वह संख्या 11 से विभाज्य हो । <br>दिया गया संख्या: 1112024<br>अंतर = (4 + 0 + 1 + 1) - (2 + 2 + 1 <strong>+ 1</strong>) = 0<br>अतः, आवश्यक संख्या = 1</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "75",
                    section: "7",
                    question_en: "<p>75. The following table shows the production of cars in a company from 2016 to 2020.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741072363751.png\" alt=\"rId62\" width=\"268\" height=\"145\"> <br>There was a continuous increase in the production of cars of which type during the period 2016 to 2020?</p>",
                    question_hi: "<p>75. निम्न तालिका 2016 से 2020 तक एक कंपनी में कारों के उत्पादन को दर्शाती है।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741072363872.png\" alt=\"rId63\" width=\"389\" height=\"167\"> <br>2016 से 2020 की अवधि के दौरान किस प्रकार की कारों के उत्पादन में लगातार वृद्धि हुई?</p>",
                    options_en: [
                        "<p>B</p>",
                        "<p>D</p>",
                        "<p>C</p>",
                        "<p>A</p>"
                    ],
                    options_hi: [
                        "<p>B</p>",
                        "<p>D</p>",
                        "<p>C</p>",
                        "<p>A</p>"
                    ],
                    solution_en: "<p>75.(c)&nbsp;We can clearly see that the production of year c is continuously increasing.</p>",
                    solution_hi: "<p>75.(c)&nbsp;हम स्पष्ट रूप से देख सकते हैं कि वर्ष c के उत्पादन में निरंतर वृद्धि हो रही है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "76",
                    section: "7",
                    question_en: "<p>76. Parts of the following sentence have been underlined and given as options. Select the&nbsp;option that contains an error.<br><span style=\"text-decoration: underline;\">If it will rain</span> tomorrow, <span style=\"text-decoration: underline;\">I will</span> stay <span style=\"text-decoration: underline;\">at home</span>.</p>",
                    question_hi: "<p>76. Parts of the following sentence have been underlined and given as options. Select the<br>option that contains an error.<br><span style=\"text-decoration: underline;\">If it will rain</span> tomorrow, <span style=\"text-decoration: underline;\">I will</span> stay <span style=\"text-decoration: underline;\">at home</span>.</p>",
                    options_en: [
                        "<p>If it</p>",
                        "<p>at home</p>",
                        "<p>will rain</p>",
                        "<p>I will</p>"
                    ],
                    options_hi: [
                        "<p>If it</p>",
                        "<p>at home</p>",
                        "<p>will rain</p>",
                        "<p>I will</p>"
                    ],
                    solution_en: "<p>76.(c) will rain <br>&lsquo;Will rain&rsquo; must be replaced with &lsquo;rains&rsquo;. The given sentence is an example of the first conditional sentence and the correct grammatical structure is &ldquo;if + Simple present&hellip;&hellip;. Sub + will +V<sub>1</sub>&rdquo;. Hence, &lsquo;If it rains&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>76.(c) will rain <br>&lsquo;Will rain&rsquo; के स्थान पर &lsquo;rains&rsquo; का प्रयोग होगा। दिया गया sentence first conditional sentence का example है और सही grammatical structure &ldquo;if + Simple present&hellip;&hellip;. Sub + will + V<sub>1</sub>&rdquo; है। अतः, &lsquo;If it rains&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "77",
                    section: "7",
                    question_en: "<p>77. Find a word that is the synonym of&nbsp;Imminent</p>",
                    question_hi: "<p>77 .Find a word that is the synonym of&nbsp;Imminent</p>",
                    options_en: [
                        "<p>Unyielding</p>",
                        "<p>Ignorant</p>",
                        "<p>Impending</p>",
                        "<p>Distant</p>"
                    ],
                    options_hi: [
                        "<p>Unyielding</p>",
                        "<p>Ignorant</p>",
                        "<p>Impending</p>",
                        "<p>Distant</p>"
                    ],
                    solution_en: "<p>77.(c) <strong>Impending-</strong> something that will happen soon<br><strong>Imminent-</strong> almost certain to happen very soon<br><strong>Unyielding- </strong>unlikely to be swayed or resolute.<br><strong>Ignorant- </strong>not knowing about something<br><strong>Distant- </strong>a long way away in space or time</p>",
                    solution_hi: "<p>77.(c) <strong>Impending (आगामी ) - </strong>something that will happen soon<br><strong>Imminent (निकटस्थ) -&nbsp;</strong>almost certain to happen very soon<br><strong>Unyielding (अडिग) -</strong> unlikely to be swayed or resolute.<br><strong>Ignorant (<span class=\"Y2IQFc\" lang=\"hi\">अनजान) </span>-</strong> not knowing about something<br><strong>Distant (<span class=\"Y2IQFc\" lang=\"hi\">दूरस्थ) </span></strong><strong>- </strong>a long way away in space or time<br><br></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "78",
                    section: "7",
                    question_en: "<p>78. Select the most appropriate option that can replace the bracketed word segment in the&nbsp;following sentence.<br>Children should avoid giving out personal details online (which will) identify them or&nbsp;their location.</p>",
                    question_hi: "<p>78. Select the most appropriate option that can replace the bracketed word segment in the&nbsp;following sentence.<br>Children should avoid giving out personal details online (which will) identify them or&nbsp;their location.</p>",
                    options_en: [
                        "<p>that could</p>",
                        "<p>what will</p>",
                        "<p>which might lead</p>",
                        "<p>whichever can</p>"
                    ],
                    options_hi: [
                        "<p>that could</p>",
                        "<p>what will</p>",
                        "<p>which might lead</p>",
                        "<p>whichever can</p>"
                    ],
                    solution_en: "<p>78.(a) that could<br>&lsquo;Could&rsquo; is used to indicate possibility. The given sentence states an advice to children that they should avoid giving out personal details online that could identify them or their location. Hence, &lsquo;that could&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>78.(a) that could<br>&lsquo;Could&rsquo; का प्रयोग possibility को indicate करने के लिए किया जाता है। दिए गए sentence में बच्चों को सलाह दी गई है कि उन्हें ऑनलाइन व्यक्तिगत जानकारी (personal details) देने से बचना चाहिए जिससे उनकी या उनके स्थान की पहचान हो सके। अतः, &lsquo;that could&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "79",
                    section: "7",
                    question_en: "<p>79. From among the words given in bold, select the<strong> INCORRECTLY</strong> spelt word in the following sentence.<br>Kathmandu is vivid,<strong> mercenery,</strong> religious with small <strong>shrines </strong>to flower-adorned <strong>deities</strong> along the <strong>narrowest</strong> and busiest streets.</p>",
                    question_hi: "<p>79. From among the words given in bold, select the<strong> INCORRECTLY</strong> spelt word in the following sentence.<br>Kathmandu is vivid,<strong> mercenery,</strong> religious with small <strong>shrines </strong>to flower-adorned <strong>deities</strong> along the <strong>narrowest</strong> and busiest streets.</p>",
                    options_en: [
                        "<p>shrines</p>",
                        "<p>deities</p>",
                        "<p>mercenery</p>",
                        "<p>narrowest</p>"
                    ],
                    options_hi: [
                        "<p>shrines</p>",
                        "<p>deities</p>",
                        "<p>mercenery</p>",
                        "<p>narrowest</p>"
                    ],
                    solution_en: "<p>79.(c) Mercenary is the correct spelling.</p>",
                    solution_hi: "<p>79.(c) \'Mercenary\'&nbsp; सही spelling है ।&nbsp;</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "80",
                    section: "7",
                    question_en: "<p>80. Select the word which means the same as the group of words given<br>An omission of duty.</p>",
                    question_hi: "<p>80. Select the word which means the same as the group of words given<br>An omission of duty.</p>",
                    options_en: [
                        "<p>Surrender</p>",
                        "<p>Delinquency</p>",
                        "<p>Deliberation</p>",
                        "<p>Delineation</p>"
                    ],
                    options_hi: [
                        "<p>Surrender</p>",
                        "<p>Delinquency</p>",
                        "<p>Deliberation</p>",
                        "<p>Delineation</p>"
                    ],
                    solution_en: "<p>80.(b) <strong>Delinquency-</strong> An omission of duty , crime, guilt <br><strong>Deliberation - </strong>means long and careful consideration or discussion.<br><strong>Delineation -</strong> means the action of describing or portraying something precisely.</p>",
                    solution_hi: "<p>80.(b) <strong>Delinquency (अपराध) -</strong> An omission of duty , crime, guilt<br><strong>Deliberation (विवेचना) -</strong> means long and careful consideration or discussion.<br><strong>Delineation (चित्रण करना) - </strong>means the action of describing or portraying something precisely.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "81",
                    section: "7",
                    question_en: "<p>81. Parts of a sentence are given below in jumbled order. Select the option that gives their correct logical sequence and forms a meaningful sentence.<br>The initial days / are not (P) / of my high school (Q) / to remember (R) / very good (S).</p>",
                    question_hi: "<p>81. Parts of a sentence are given below in jumbled order. Select the option that gives their correct logical sequence and forms a meaningful sentence.<br>The initial days / are not (P) / of my high school (Q) / to remember (R) / very good (S).</p>",
                    options_en: [
                        "<p>P, R, S, Q</p>",
                        "<p>R, Q, P, S</p>",
                        "<p>P, S, R, Q</p>",
                        "<p>Q, P, S, R</p>"
                    ],
                    options_hi: [
                        "<p>P, R, S, Q</p>",
                        "<p>R, Q, P, S</p>",
                        "<p>P, S, R, Q</p>",
                        "<p>Q, P, S, R </p>"
                    ],
                    solution_en: "<p>81.(d) Q, P, S, R<br>The given sentence starts with the phrase &lsquo;The initial days&rsquo;. Part Q will follow this phrase as it completes the subject. Part P contains the main verb &lsquo;are&rsquo; of this sentence and Part S contains the complement &lsquo;very good&rsquo; of the verb &lsquo;are&rsquo;. So, S will follow P. Part R completes the complement by stating that the initial days are not very good to remember. Going through the options, option (d) has the correct sequence.</p>",
                    solution_hi: "<p>81.(d) Q, P, S, R<br>दिया गया sentence phrase &lsquo;The initial days&rsquo; से शुरू होता है। Part Q इस phrase के बाद आयेगा, क्योंकि यह subject को पूरा करता है। Part P में इस sentence की main verb &lsquo;are&rsquo; है और part S में verb &lsquo;are&rsquo; का complement &lsquo;very good&rsquo; है। इसलिए, P के बाद S आयेगा। Part R यह बताकर complement को पूरा करता है कि शुरुआती दिन याद रखने के लिए बहुत अच्छे नहीं हैं। विकल्पों को देखते हुए, option (d) में सही क्रम है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "82",
                    section: "7",
                    question_en: "<p>82. Identify the segment in the sentence, which contains an error.<br>In this book I came on some words the meanings of which I did not know.</p>",
                    question_hi: "<p>82. Identify the segment in the sentence, which contains an error.<br>In this book I came on some words the meanings of which I did not know.</p>",
                    options_en: [
                        "<p>came on</p>",
                        "<p>the meanings of which</p>",
                        "<p>In this book</p>",
                        "<p>did not know</p>"
                    ],
                    options_hi: [
                        "<p>came on</p>",
                        "<p>the meanings of which</p>",
                        "<p>In this book</p>",
                        "<p>did not know</p>"
                    ],
                    solution_en: "<p>82.(a) &ldquo;came on&rdquo;<br>Came across will be the right phrase.Came across means to meet or find something by chance.The correct sentence is - In this book I came across some words the meanings of which I did not know.</p>",
                    solution_hi: "<p>82.(a) &ldquo;came on&rdquo;<br>&ldquo;Came across&rdquo; का प्रयोग होगा। Came across का अर्थ है संयोग से कुछ मिलना। अतः सही वाक्य है - In this book I came across some words the meanings of which I did not know.</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "83",
                    section: "7",
                    question_en: "<p>83. Select the INCORRECTLY spelt word.</p>",
                    question_hi: "<p>83. Select the INCORRECTLY spelt word.</p>",
                    options_en: [
                        "<p>stationery</p>",
                        "<p>marijuena</p>",
                        "<p>lascivious</p>",
                        "<p>dictator</p>"
                    ],
                    options_hi: [
                        "<p>stationery</p>",
                        "<p>marijuena</p>",
                        "<p>lascivious</p>",
                        "<p>dictator</p>"
                    ],
                    solution_en: "<p>83.(b) <strong>marijuena</strong><br>&lsquo;Marijuana&rsquo; is the correct spelling.</p>",
                    solution_hi: "<p>83.(b) <strong>marijuena</strong><br>&lsquo;Marijuana&rsquo; सही spelling है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "84",
                    section: "7",
                    question_en: "<p>84. Identify the best way to improve the underlined part of the given sentence. If there is no improvement required, select &lsquo;no improvement&rsquo;. <br>I can&rsquo;t go out in the rain as <span style=\"text-decoration: underline;\">I am not having an umbrella</span>.</p>",
                    question_hi: "<p>84. Identify the best way to improve the underlined part of the given sentence. If there is no improvement required, select &lsquo;no improvement&rsquo;. <br>I can&rsquo;t go out in the rain as <span style=\"text-decoration: underline;\">I am not having an umbrella</span>.</p>",
                    options_en: [
                        "<p>have no umbrella</p>",
                        "<p>don&rsquo;t have an umbrella</p>",
                        "<p>don&rsquo;t have the umbrella</p>",
                        "<p>No improvement</p>"
                    ],
                    options_hi: [
                        "<p>have no umbrella</p>",
                        "<p>don&rsquo;t have an umbrella</p>",
                        "<p>don&rsquo;t have the umbrella</p>",
                        "<p>No improvement</p>"
                    ],
                    solution_en: "<p>84.(b) don&rsquo;t have an umbrella<br>Have is a stative verb. It means own/hold. It is not used in progressive tenses.</p>",
                    solution_hi: "<p>84.(b) don&rsquo;t have an umbrella<br>Have is a stative verb. इसका अर्थ है own/hold. इसका प्रयोग progressive tenses में नहीं किया जाता है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "85",
                    section: "7",
                    question_en: "<p>85. Select the most appropriate meaning of idiom in the sentence.<br><strong>To pay lip service</strong></p>",
                    question_hi: "<p>85. Select the most appropriate meaning of idiom in the sentence.<br><strong>To pay lip service</strong></p>",
                    options_en: [
                        "<p>To express loyalty, respect, or support for something insincerely</p>",
                        "<p>To face a risk</p>",
                        "<p>To pretend to be very old</p>",
                        "<p>To ignore what someone is saying</p>"
                    ],
                    options_hi: [
                        "<p>To express loyalty, respect, or support for something insincerely</p>",
                        "<p>To face a risk</p>",
                        "<p>To pretend to be very old</p>",
                        "<p>To ignore what someone is saying</p>"
                    ],
                    solution_en: "<p>85.(a) To pay lip service- to express loyalty, respect, or support for something insincerely.<br>Example.- Neha claims to be in favour of the proposal, but so far she has only paid lip service to the idea.</p>",
                    solution_hi: "<p>85.(a) To pay lip service- to express loyalty, respect, or support for something insincerely. (झूठे मन से किसी चीज के प्रति निष्ठा, सम्मान या समर्थन व्यक्त करना।)<br>Example- Neha claims to be in favour of the proposal, but so far she has only paid lip service to the idea.</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "86",
                    section: "7",
                    question_en: "<p>86. Select the option that expresses the given sentence in passive voice.<br>Priya washes her clothes in the evening.</p>",
                    question_hi: "<p>86. Select the option that expresses the given sentence in passive voice.<br>Priya washes her clothes in the evening.</p>",
                    options_en: [
                        "<p>The clothes was washed by Priya in the evening.</p>",
                        "<p>The clothes is washed by Priya in the evening.</p>",
                        "<p>The clothes are washed by Priya in the evening.</p>",
                        "<p>The clothes were washed by Priya in the evening.</p>"
                    ],
                    options_hi: [
                        "<p>The clothes was washed by Priya in the evening.</p>",
                        "<p>The clothes is washed by Priya in the evening.</p>",
                        "<p>The clothes are washed by Priya in the evening.</p>",
                        "<p>The clothes were washed by Priya in the evening.</p>"
                    ],
                    solution_en: "<p>86.(c) The clothes are washed by Priya in the evening. <br>(a) The clothes <span style=\"text-decoration: underline;\">was washed</span> by Priya in the evening. (Incorrect&nbsp;Tense)<br>(b) The clothes <span style=\"text-decoration: underline;\">is washed</span> by Priya in the evening. (Incorrect Tense)<br>(d) The clothes <span style=\"text-decoration: underline;\">were washed</span> by Priya in the evening. (Incorrect Tense)</p>",
                    solution_hi: "<p>86.(c) The clothes are washed by Priya in the evening. <br>(a) The clothes <span style=\"text-decoration: underline;\">was washed</span> by Priya in the evening. (गलत Tense)<br>(b) The clothes <span style=\"text-decoration: underline;\">is washed</span> by Priya in the evening. (गलत Tense)<br>(d) The clothes <span style=\"text-decoration: underline;\">were washed</span> by Priya in the evening. (गलत Tense)</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "87",
                    section: "7",
                    question_en: "<p>87. Select the most appropriate meaning of idiom in the sentence.<br>The Chairman of our company takes care of the <span style=\"text-decoration: underline;\"><strong>rank and file</strong></span> in the company.</p>",
                    question_hi: "<p>87. Select the most appropriate meaning of idiom in the sentence.<br>The Chairman of our company takes care of the <span style=\"text-decoration: underline;\"><strong>rank and file</strong></span> in the company.</p>",
                    options_en: [
                        "<p>Ordinary people</p>",
                        "<p>Documents and files</p>",
                        "<p>Only the top rank people</p>",
                        "<p>Officers</p>"
                    ],
                    options_hi: [
                        "<p>Ordinary people</p>",
                        "<p>Documents and files</p>",
                        "<p>Only the top rank people</p>",
                        "<p>Officers</p>"
                    ],
                    solution_en: "<p>87.(a) ordinary people<br>E.g.- The rank and file feel that the minister is corrupt.</p>",
                    solution_hi: "<p>87.(a) ordinary people/ साधारण लोग । <br>उदाहरण .- The rank and file feel that the minister is corrupt./ साधारण लोगों को लगता है कि मंत्री भ्रष्ट है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "88",
                    section: "7",
                    question_en: "<p>88. Sentences of a paragraph are given below in jumbled order. Arrange the sentences in the correct order to form a meaningful and coherent paragraph.<br>P. Applied arts require an object to be functional, as well as beautiful.<br>Q. They recognised that their work belonged to the higher social classes who had the wealth to purchase art and the leisure time to enjoy it.<br>R. Fine art is categorised as something which only has an aesthetic or conceptual function.<br>S. There are a number of different categories of objects and processes under the umbrella term of art which need to be explored.<br>T. In the twentieth century, artists began to challenge the established notion of art.<br>U. Art is typically divided into two areas: fine art (such as painting, sculpture, music and poetry), and the applied arts (such as pottery, weaving, metal working, furniture making and calligraphy).</p>",
                    question_hi: "<p>88. Sentences of a paragraph are given below in jumbled order. Arrange the sentences in the correct order to form a meaningful and coherent paragraph.<br>P. Applied arts require an object to be functional, as well as beautiful.<br>Q. They recognised that their work belonged to the higher social classes who had the wealth to purchase art and the leisure time to enjoy it.<br>R. Fine art is categorised as something which only has an aesthetic or conceptual function.<br>S. There are a number of different categories of objects and processes under the umbrella term of art which need to be explored.<br>T. In the twentieth century, artists began to challenge the established notion of art.<br>U. Art is typically divided into two areas: fine art (such as painting, sculpture, music and poetry), and the applied arts (such as pottery, weaving, metal working, furniture making and calligraphy).</p>",
                    options_en: [
                        "<p>RPSTUQ</p>",
                        "<p>SURPTQ</p>",
                        "<p>SUPQRT</p>",
                        "<p>SRPUTQ</p>"
                    ],
                    options_hi: [
                        "<p>RPSTUQ</p>",
                        "<p>SURPTQ</p>",
                        "<p>SUPQRT</p>",
                        "<p>SRPUTQ</p>"
                    ],
                    solution_en: "<p>88.(b) SURPTQ<br>Sentence S will be the starting line as it introduces the main idea of the parajumble, i.e. &lsquo;different categories of objects and processes under the umbrella term of art&rsquo;. And, Sentence U talks about the division of art into two areas- fine arts and the applied arts. So, U will follow S. Further, Sentence R describes fine art &amp; Sentence P describes the applied arts. So, P will follow R. However, Sentence T states that artists began to challenge the established notion of art in the twentieth century &amp; Sentence Q states their recognition that their work belonged to the higher social classes. So, Q will follow T. Going through the options, option (b) has the correct sequence.</p>",
                    solution_hi: "<p>88.(b) SURPTQ<br>Sentence S प्रारंभिक line होगी क्योंकि यह parajumble के main idea &lsquo;different categories of objects and processes under the umbrella term of art&rsquo; को प्रस्तुत करता है । और, Sentence U कला के दो क्षेत्रों- ललित कला(fine arts) और अनुप्रयुक्त कलाओं (applied arts) में विभाजन के बारे में बात करता है। इसलिए, S के बाद U आयेगा। इसके अलावा, Sentence R ललित कला का वर्णन करता है और Sentence P अनुप्रयुक्त कलाओं का वर्णन करता है। इसलिए, R के बाद P आयेगा। हालाँकि, Sentence T बताता है कि कलाकारों ने बीसवीं शताब्दी में कला की स्थापित धारणा (notion of art)को चुनौती देना शुरू कर दिया और Sentence Q उनकी मान्यता को बताता है कि उनका काम उच्च सामाजिक वर्गों से संबंधित था। इसलिए, T के बाद Q आयेगा। विकल्पों के माध्यम से जाने पर, option (b) में सही क्रम है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "89",
                    section: "7",
                    question_en: "<p>89. Select the word which means the same as the group of words given<br>A person who works in a shop that sells cut flowers and plants</p>",
                    question_hi: "<p>89. Select the word which means the same as the group of words given<br>A person who works in a shop that sells cut flowers and plants</p>",
                    options_en: [
                        "<p>Dealer</p>",
                        "<p>Vendor</p>",
                        "<p>Hawker</p>",
                        "<p>Florist</p>"
                    ],
                    options_hi: [
                        "<p>Dealer</p>",
                        "<p>Vendor</p>",
                        "<p>Hawker</p>",
                        "<p>Florist</p>"
                    ],
                    solution_en: "<p>89.(d) <strong>Florist-</strong> a person who works in a shop that sells cut flowers and plants.<br><strong>Dealer - </strong>a person whose business is buying and selling things<br><strong>Vendor- </strong>a person who is selling something <br><strong>Hawker-</strong> someone who sells goods informally in public places</p>",
                    solution_hi: "<p>89.(d) <strong>Florist (<span class=\"Y2IQFc\" lang=\"hi\">फूलवाला) </span></strong><strong>-</strong> a person who works in a shop that sells cut flowers and plants.<br><strong>Dealer (व्यापारी) -&nbsp;</strong>a person whose business is buying and selling things<br><strong>Vendor (</strong><span class=\"Y2IQFc\" lang=\"hi\"><strong>विक्रेता)</strong> </span><strong>- </strong>a person who is selling something<br><strong>Hawker (<span class=\"Y2IQFc\" lang=\"hi\">फेरीवाला) </span>- </strong>someone who sells goods informally in public places</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "90",
                    section: "7",
                    question_en: "<p>90. Select the most appropriate option to fill in the blank.<br>It took 4 hours straight to ___________ the deal with the regional company.</p>",
                    question_hi: "<p>90. Select the most appropriate option to fill in the blank.<br>It took 4 hours straight to ___________ the deal with the regional company.</p>",
                    options_en: [
                        "<p>perform</p>",
                        "<p>get</p>",
                        "<p>close</p>",
                        "<p>stop</p>"
                    ],
                    options_hi: [
                        "<p>perform</p>",
                        "<p>get</p>",
                        "<p>close</p>",
                        "<p>stop</p>"
                    ],
                    solution_en: "<p>90.(c) close<br>&lsquo;Close&rsquo; is the correct verb to use with &lsquo;deal&rsquo;. &lsquo;Closing the deal&rsquo; means to make a successful business arrangement with someone. Hence, &lsquo;close&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>90.(c) close<br>&lsquo;Deal&rsquo; के साथ प्रयोग करने के लिए &lsquo;Close&rsquo;, correct verb है। &lsquo;Closing the deal&rsquo; का अर्थ है किसी के साथ एक सफल व्यापारिक arrangement करना। अतः, &lsquo;close&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "91",
                    section: "7",
                    question_en: "<p>91. Select the option that expresses the meaning of the underlined segment.<br>Partly, it may be a desire <span style=\"text-decoration: underline;\">to stop</span> emotions in front of strangers.</p>",
                    question_hi: "<p>91. Select the option that expresses the meaning of the underlined segment.<br>Partly, it may be a desire <span style=\"text-decoration: underline;\">to stop</span> emotions in front of strangers.</p>",
                    options_en: [
                        "<p>Quell</p>",
                        "<p>Put out</p>",
                        "<p>Smash</p>",
                        "<p>Quiet</p>"
                    ],
                    options_hi: [
                        "<p>Quell</p>",
                        "<p>Put out</p>",
                        "<p>Smash</p>",
                        "<p>Quiet</p>"
                    ],
                    solution_en: "<p>91.(a) <strong>Quell-</strong> to completely stop or end something.</p>",
                    solution_hi: "<p>91.(a) <strong>Quell -</strong> to completely stop or end something./किसी चीज़ को पूरी तरह से रोकना या ख़त्म करना।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "92",
                    section: "7",
                    question_en: "<p>92. Pick a word opposite in meaning to<br>Desiderate</p>",
                    question_hi: "<p>92. Pick a word opposite in meaning to<br>Desiderate</p>",
                    options_en: [
                        "<p>Quarrel</p>",
                        "<p>Disregard</p>",
                        "<p>Acquire</p>",
                        "<p>Mandate</p>"
                    ],
                    options_hi: [
                        "<p>Quarrel</p>",
                        "<p>Disregard</p>",
                        "<p>Acquire</p>",
                        "<p>Mandate</p>"
                    ],
                    solution_en: "<p>92.(b) <strong>Disregard - </strong>to take no notice of somebody/something<br><strong>Desiderate -</strong> feel a keen desire for something lacking or absent<br><strong>Quarrel -</strong> an angry argument or disagreement<br><strong>Acquire -</strong> to obtain or buy something<br><strong>Mandate-</strong> the power that is officially given to a group of people to do something</p>",
                    solution_hi: "<p>92.(b)<strong> Disregard (अवहेलना) -&nbsp;</strong>to take no notice of somebody/something<br><strong>Desiderate (<span data-huuid=\"2506466663295241265\">इच्छा करना) </span>-</strong> feel a keen desire for something lacking or absent<br><strong>Quarrel (विवाद) - </strong>an angry argument or disagreement<br><strong>Acquire (प्राप्त करना)&nbsp; </strong>- to obtain or buy something<br><strong>Mandate (आदेश )-&nbsp;</strong>the power that is officially given to a group of people to do something</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "93",
                    section: "7",
                    question_en: "<p>93. Select the most appropriate ANTONYM of the underlined word in the given sentence.<br>The athlete looked <span style=\"text-decoration: underline;\">unsettled</span> before the race.</p>",
                    question_hi: "<p>93. Select the most appropriate ANTONYM of the underlined word in the given sentence.<br>The athlete looked <span style=\"text-decoration: underline;\">unsettled</span> before the race.</p>",
                    options_en: [
                        "<p>Tangent</p>",
                        "<p>Confident</p>",
                        "<p>Triumphant</p>",
                        "<p>Reticent</p>"
                    ],
                    options_hi: [
                        "<p>Tangent</p>",
                        "<p>Confident</p>",
                        "<p>Triumphant</p>",
                        "<p>Reticent</p>"
                    ],
                    solution_en: "<p>93.(b) <strong>Confident -</strong> feeling or showing that you are sure about your own abilities, opinions, etc. <br><strong>Unsettled-</strong> nervous and worried. <br><strong>Tangent- </strong>a completely different line of thought or action.<br><strong>Triumphant- </strong>feeling or showing great happiness because you have won or succeeded at something<br><strong>Reticent- </strong>inclined to keep one\'s thoughts, feelings, or opinions to oneself.</p>",
                    solution_hi: "<p>93.(b) <strong>Confident</strong> (आत्मविश्वासी) - feeling or showing that you are sure about your own abilities, opinions, etc. <br><strong>Unsettled</strong> (अशांत) - nervous and worried.<br><strong>Tangent </strong>(विषय से भटकना) - a completely different line of thought or action.<br><strong>Triumphant </strong>(विजयी) - feeling or showing great happiness because you have won or succeeded at something<br><strong>Reticent </strong>(मितभाषी) - inclined to keep one\'s thoughts, feelings, or opinions to oneself.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "94",
                    section: "7",
                    question_en: "<p>94. Select the most appropriate option to fill in the blank. <br>___________ the last ten years we have been victims of abuse.</p>",
                    question_hi: "<p>94. Select the most appropriate option to fill in the blank. <br>___________ the last ten years we have been victims of abuse.</p>",
                    options_en: [
                        "<p>From</p>",
                        "<p>For</p>",
                        "<p>Before</p>",
                        "<p>Since</p>"
                    ],
                    options_hi: [
                        "<p>From</p>",
                        "<p>For</p>",
                        "<p>Before</p>",
                        "<p>Since</p>"
                    ],
                    solution_en: "<p>94.(b) For. <br>The given sentence is in &ldquo;present perfect continuous tense&rdquo; and we should use &ldquo;for&rdquo; according to its structure as &ldquo;for&rdquo; is used for a given period of time.</p>",
                    solution_hi: "<p>94.(b) For. <br>दिया गया वाक्य &ldquo;present perfect continuous tense&rdquo; में है और हमें इसकी संरचना के अनुसार \"for\" का उपयोग करना चाहिए क्योंकि \"for\" एक निश्चित समय के लिए उपयोग किया जाता है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "95",
                    section: "7",
                    question_en: "<p>95. Select the correct active form of the given sentences.<br>Has the villa been occupied by them?</p>",
                    question_hi: "<p>95. Select the correct active form of the given sentences.<br>Has the villa been occupied by them?</p>",
                    options_en: [
                        "<p>Did they occupied the villa?</p>",
                        "<p>Had they occupied the villa?</p>",
                        "<p>Have they occupied the villa?</p>",
                        "<p>Do they occupied the villa?</p>"
                    ],
                    options_hi: [
                        "<p>Did they occupied the villa?</p>",
                        "<p>Had they occupied the villa?</p>",
                        "<p>Have they occupied the villa?</p>",
                        "<p>Do they occupied the villa?</p>"
                    ],
                    solution_en: "<p>95.(c) Have they occupied the villa? <br>(a) <span style=\"text-decoration: underline;\">Did they occupied</span> the villa? (Incorrect Tense)<br>(b) <span style=\"text-decoration: underline;\">Had they occupied</span> the villa? (Incorrect Tense)<br>(d) <span style=\"text-decoration: underline;\">Do they occupied</span> the villa? (Incorrect Tense)</p>",
                    solution_hi: "<p>95.(c) Have they occupied the villa ? <br>(a) <span style=\"text-decoration: underline;\">Did they occupied</span> the villa? (Tense गलत है)<br>(b) <span style=\"text-decoration: underline;\">Had they occupied</span> the villa? (Tense गलत है)<br>(d) <span style=\"text-decoration: underline;\">Do they occupied</span> the villa? (Tense गलत है)</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "96",
                    section: "7",
                    question_en: "<p>96. <strong>Cloze test:</strong><br>Without water no animals can survive. In desert regions, the greatest (96)______ for living is drying up. But many creatures are able to make use of (97)______ little water that exists in arid areas. One of nature\'s masterpieces, (98)______ creatures equipped to (99)_______ with desert life is the hardy camel. There are several stories describing the (100)_______ endurance of these animals.<br>Select the most appropriate option to fill in the blank no.96</p>",
                    question_hi: "<p>96. <strong>Cloze test:</strong><br>Without water no animals can survive. In desert regions, the greatest (96)______ for living is drying up. But many creatures are able to make use of (97)______ little water that exists in arid areas. One of nature\'s masterpieces, (98)______ creatures equipped to (99)_______ with desert life is the hardy camel. There are several stories describing the (100)_______ endurance of these animals.<br>Select the most appropriate option to fill in the blank no.96</p>",
                    options_en: [
                        "<p>want</p>",
                        "<p>inadequacy</p>",
                        "<p>requirement</p>",
                        "<p>threat</p>"
                    ],
                    options_hi: [
                        "<p>want</p>",
                        "<p>inadequacy</p>",
                        "<p>requirement</p>",
                        "<p>threat</p>"
                    ],
                    solution_en: "<p>96.(c) requirement<br>&lsquo;Requirement&rsquo; means something that you need or that you must do or have. The given passage states that the greatest requirement(that you need) for living is drying up. Hence, &lsquo;requirements&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>96.(c) requirement<br>&lsquo;Requirement&rsquo; का अर्थ कुछ ऐसा है जिसकी आपको आवश्यकता है या जो आपको करना चाहिए या आपके पास होना चाहिए। दिया गया passage बताता है कि जीने के लिए सबसे बड़ी आवश्यकता (जिसकी आपको आवश्यकता है) सूख रही है। इसलिए,&lsquo;requirements&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "97",
                    section: "7",
                    question_en: "<p>97.<strong> Cloze test:</strong><br>Without water no animals can survive. In desert regions, the greatest (96)______ for living is drying up. But many creatures are able to make use of (97)______ little water that exists in arid areas. One of nature\'s masterpieces, (98)______ creatures equipped to (99)_______ with desert life is the hardy camel. There are several stories describing the (100)_______ endurance of these animals.<br>Select the most appropriate option to fill in the blank no.97</p>",
                    question_hi: "<p>97. <strong>Cloze test:</strong><br>Without water no animals can survive. In desert regions, the greatest (96)______ for living is drying up. But many creatures are able to make use of (97)______ little water that exists in arid areas. One of nature\'s masterpieces, (98)______ creatures equipped to (99)_______ with desert life is the hardy camel. There are several stories describing the (100)_______ endurance of these animals.<br>Select the most appropriate option to fill in the blank no.97</p>",
                    options_en: [
                        "<p>what</p>",
                        "<p>the</p>",
                        "<p>very</p>",
                        "<p>that</p>"
                    ],
                    options_hi: [
                        "<p>what</p>",
                        "<p>the</p>",
                        "<p>very</p>",
                        "<p>that</p>"
                    ],
                    solution_en: "<p>97.(b) the <br>We will use the definite article &lsquo;the&rsquo; before &lsquo;little water&rsquo; because it is a specific phrase. The given passage states that many creatures are able to make use of the little water that exists in arid areas. Hence, &lsquo;the&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>97.(b) the <br>हम &lsquo;little water&rsquo; से पहले definite article &lsquo;the&rsquo; का प्रयोग करेंगे क्योंकि यह एक specific phrase (विशिष्ट मुहावरा) है। दिए गए passage में कहा गया है कि कई जीव शुष्क क्षेत्रों में मौजूद थोड़े से पानी का उपयोग करने में सक्षम हैं। इसलिए, &lsquo;the&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "98",
                    section: "7",
                    question_en: "<p>98.<strong> Cloze test:</strong><br>Without water no animals can survive. In desert regions, the greatest (96)______ for living is drying up. But many creatures are able to make use of (97)______ little water that exists in arid areas. One of nature\'s masterpieces, (98)______ creatures equipped to (99)_______ with desert life is the hardy camel. There are several stories describing the (100)_______ endurance of these animals.<br>Select the most appropriate option to fill in the blank no.98</p>",
                    question_hi: "<p>98. <strong>Cloze test:</strong><br>Without water no animals can survive. In desert regions, the greatest (96)______ for living is drying up. But many creatures are able to make use of (97)______ little water that exists in arid areas. One of nature\'s masterpieces, (98)______ creatures equipped to (99)_______ with desert life is the hardy camel. There are several stories describing the (100)_______ endurance of these animals.<br>Select the most appropriate option to fill in the blank no.98</p>",
                    options_en: [
                        "<p>for</p>",
                        "<p>about</p>",
                        "<p>among</p>",
                        "<p>with</p>"
                    ],
                    options_hi: [
                        "<p>for</p>",
                        "<p>about</p>",
                        "<p>among</p>",
                        "<p>with</p>"
                    ],
                    solution_en: "<p>98.(c) <strong>among</strong> <br>&lsquo;Among&rsquo; means concerning a particular group of people or things. The given passage talks about a group of creatures. Hence, &lsquo;among&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>98.(c) <strong>among</strong> <br>&lsquo;Among&rsquo; का अर्थ है लोगों या चीजों के एक विशेष समूह से संबंधित। दिया गया passage प्राणियों के समूह के बारे में बात करता है। इसलिए,&lsquo;among&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "99",
                    section: "7",
                    question_en: "<p>99. <strong>Cloze test:</strong><br>Without water no animals can survive. In desert regions, the greatest (96)______ for living is drying up. But many creatures are able to make use of (97)______ little water that exists in arid areas. One of nature\'s masterpieces, (98)______ creatures equipped to (99)_______ with desert life is the hardy camel. There are several stories describing the (100)_______ endurance of these animals.<br>Select the most appropriate option to fill in the blank no.99</p>",
                    question_hi: "<p>99. <strong>Cloze test:</strong><br>Without water no animals can survive. In desert regions, the greatest (96)______ for living is drying up. But many creatures are able to make use of (97)______ little water that exists in arid areas. One of nature\'s masterpieces, (98)______ creatures equipped to (99)_______ with desert life is the hardy camel. There are several stories describing the (100)_______ endurance of these animals.<br>Select the most appropriate option to fill in the blank no.99</p>",
                    options_en: [
                        "<p>live</p>",
                        "<p>resist</p>",
                        "<p>bear</p>",
                        "<p>cope</p>"
                    ],
                    options_hi: [
                        "<p>live</p>",
                        "<p>resist</p>",
                        "<p>bear</p>",
                        "<p>cope</p>"
                    ],
                    solution_en: "<p>99.(d) cope<br>&lsquo;Cope&rsquo; means to deal successfully with a difficult matter or situation. The given passage talks about camels who can successfully deal with the difficult situations of the desert. Hence, &lsquo;cope&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>99.(d) cope<br>&lsquo;Cope&rsquo; का अर्थ है किसी कठिन मामले या स्थिति से सफलतापूर्वक निपटना। दिया गया passage ऊंटों के बारे में बात करता है जो रेगिस्तान की कठिन परिस्थितियों से सफलतापूर्वक निपट सकते हैं। इसलिए, &lsquo;cope&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "100",
                    section: "misc",
                    question_en: "<p>100. <strong>Cloze test:</strong><br>Without water no animals can survive. In desert regions, the greatest (96)______ for living is drying up. But many creatures are able to make use of (97)______ little water that exists in arid areas. One of nature\'s masterpieces, (98)______ creatures equipped to (99)_______ with desert life is the hardy camel. There are several stories describing the (100)_______ endurance of these animals.<br>Select the most appropriate option to fill in the blank no.100</p>",
                    question_hi: "<p>100. <strong>Cloze test:</strong><br>Without water no animals can survive. In desert regions, the greatest (96)______ for living is drying up. But many creatures are able to make use of (97)______ little water that exists in arid areas. One of nature\'s masterpieces, (98)______ creatures equipped to (99)_______ with desert life is the hardy camel. There are several stories describing the (100)_______ endurance of these animals.<br>Select the most appropriate option to fill in the blank no.100</p>",
                    options_en: [
                        "<p>remarkable</p>",
                        "<p>little</p>",
                        "<p>tolerable</p>",
                        "<p>popular</p>"
                    ],
                    options_hi: [
                        "<p>remarkable</p>",
                        "<p>little</p>",
                        "<p>tolerable</p>",
                        "<p>popular</p>"
                    ],
                    solution_en: "<p>100.(a) remarkable <br>&lsquo;Remarkable&rsquo; means worthy of being or likely to be noticed. The given passage talks about the endurance of camels which is worth to be noticed. Hence, &lsquo;remarkable&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>100.(a) remarkable <br>&lsquo;Remarkable&rsquo; का अर्थ है होने के योग्य या ध्यान देने योग्य। दिया गया passage ऊंटों के धीरज के बारे में बात करता है जो ध्यान देने योग्य है। इसलिए, &lsquo;remarkable&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'question-box d-flex align-items-center justify-content-center border rounded';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.question-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            const html = `
                <div class="card shadow-sm mb-4">
                    <div class="card-body">
                        <div class="question-text mb-4">
                            <div class="en">${question.question_en}</div>
                            <div class="hi" style="display:none">${question.question_hi}</div>
                        </div>
                        <div class="options">
                            ${['a', 'b', 'c', 'd'].map((opt, i) => `
                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="radio" name="q${index}" 
                                        id="opt-${index}-${opt}" value="${opt}" 
                                        ${answers[question.id] === opt ? 'checked' : ''}
                                        onclick="selectAnswer('${question.id}', '${opt}')">
                                    <label class="form-check-label w-100 py-2 px-3 rounded hover-bg-light" for="opt-${index}-${opt}">
                                        <div class="en">${question.options_en[i]}</div>
                                        <div class="hi" style="display:none">${question.options_hi[i]}</div>
                                    </label>
                                </div>
                            `).join('')}
                        </div>
                        ${submitted ? `
                            <div class="alert ${answers[question.id] === question.correct ? 'alert-success' : 'alert-danger'} mt-3">
                                ${answers[question.id] ? (answers[question.id] === question.correct ? 'Correct!' : 'Incorrect!') : 'Not attempted'}
                                <div class="mt-2">
                                    <strong>Solution:</strong>
                                    <div class="en">${question.solution_en}</div>
                                    <div class="hi" style="display:none">${question.solution_hi}</div>
                                </div>
                            </div>
                        ` : ''}
                    </div>
                </div>
            `;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // If in mobile view, close the menu
            if (window.innerWidth < 768) {
                toggleMenu(false);
            }
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                document.getElementById(`box-${index}`).classList.add('attempted');
            }
        }
        // Navigate to previous question
        function prevQuestion() {
            if (currentQuestion > 0) {
                showQuestion(currentQuestion - 1);
            }
        }
        // Navigate to next question
        function nextQuestion() {
            if (currentQuestion < questions.length - 1) {
                showQuestion(currentQuestion + 1);
            }
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) {
                showQuestion(section.start);
            }
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update button text
            document.getElementById('lang-btn').innerHTML = currentLang === 'en' ? 
                '<i class="fas fa-language"></i> हिंदी' : 
                '<i class="fas fa-language"></i> English';
        }
        // Toggle menu for mobile
        function toggleMenu(show) {
            const nav = document.getElementById('question-nav');
            const content = document.getElementById('question-content');
            if (show === undefined) {
                show = nav.style.right === '-100%' || nav.style.right === '';
            }
            if (show) {
                nav.style.right = '0';
                if (window.innerWidth >= 768) {
                    content.style.marginRight = '320px';
                }
            } else {
                nav.style.right = window.innerWidth < 768 ? '-100%' : '0';
                content.style.marginRight = window.innerWidth < 768 ? '0' : '320px';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
            document.getElementById('total-count').textContent = questions.length;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.remove('attempted');
                    box.classList.add('bg-secondary', 'text-white');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.remove('attempted');
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.remove('attempted');
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            resultsModal.show();
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check-circle me-1"></i> Submitted';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            resultsModal.hide();
            showQuestion(0);
        }
    </script>
</body>
</html>