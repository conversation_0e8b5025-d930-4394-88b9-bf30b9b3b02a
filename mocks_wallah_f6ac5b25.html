<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">25:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 40</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">40</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 25 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["18"] = {
                name: "General Awareness",
                start: 0,
                end: 38
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="18">General Awareness</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 39,
                end: 39
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "18",
                    question_en: "1. ‘DigiGaon’ initiative was launched by which department?",
                    question_hi: "1. \'डिजी-गांव\' पहल किस विभाग द्वारा शुरू की गई थी?",
                    options_en: [" Ministry of Agriculture and Farmers Welfare ", " Ministry of Electronics and Information Technology ", 
                                " Ministry of Health and Family Welfare ", " Ministry of Social Justice and Empowerment "],
                    options_hi: [" कृषि और किसान कल्याण मंत्रालय", " इलेक्ट्रॉनिक्स और सूचना प्रौद्योगिकी मंत्रालय",
                                " स्वास्थ्य और परिवार कल्याण मंत्रालय", " सामाजिक न्याय और अधिकारिता मंत्रालय"],
                    solution_en: "1.(b) ‘DigiGaon’ initiative was launched(May 2008) by the Ministry of Electronics and Information Technology. DigiGaon program not only attempts to connect villages with Wi-Fi but also provide digital literacy to its residents and assist in entrepreneurship opportunities such as setting up cottages.",
                    solution_hi: "1.(b) इलेक्ट्रॉनिक्स और सूचना प्रौद्योगिकी मंत्रालय द्वारा \'डिजीगांव\' (मई 2008) पहल शुरू की गई थी। डिजीगांव कार्यक्रम न केवल गांवों को वाई-फाई से जोड़ने का प्रयास करता है बल्कि इसके निवासियों को डिजिटल साक्षरता भी प्रदान करता है और कॉटेज स्थापित करने जैसे उद्यमिता के अवसरों में सहायता करता है।",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "2",
                    section: "18",
                    question_en: "<p>2. Who is the founder of the &lsquo;Youth Strike 4 Climate&rsquo; movement who had been nominated for the Nobel Peace Prize?</p>",
                    question_hi: "<p>2. यूथ स्ट्राइक 4 क्लाइमेट &rsquo;आंदोलन के संस्थापक कौन हैं जिन्हें नोबेल शांति पुरस्कार के लिए नामांकित किया गया था?</p>",
                    options_en: ["<p>Savita Halappanavar</p>", "<p>Greta Thunberg</p>", 
                                "<p>Malala Yousafzai</p>", "<p>Nadia Murad</p>"],
                    options_hi: ["<p>सविता हलप्पनवार</p>", "<p>ग्रेटा थुनबर्ग</p>",
                                "<p>मलाला यूसुफजई</p>", "<p>नादिया मुराद</p>"],
                    solution_en: "<p>2.(b)&nbsp;Greta Thunberg is the founder of the &lsquo;Youth Strike 4 Climate&rsquo; movement and has been nominated for the Nobel Peace Prize. Greta Tintin Eleonora Ernman Thunberg is a Swedish environmental activist who is known for challenging world leaders to take immediate action for climate change mitigation.</p>",
                    solution_hi: "<p>2.(b)&nbsp;ग्रेटा थनबर्ग \'यूथ स्ट्राइक 4 क्लाइमेट\' आंदोलन की संस्थापक हैं, जिन्हें नोबेल शांति पुरस्कार के लिए नामांकित किया गया है। ग्रेटा टिनटिन एलोनोरा एर्नमैन थुनबर्ग एक स्वीडिश पर्यावरण कार्यकर्ता हैं, जो जलवायु परिवर्तन शमन के लिए तत्काल कार्रवाई करने के लिए विश्व नेताओं को चुनौती देने के लिए जाने जाते हैं।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "3",
                    section: "18",
                    question_en: "3. The asthenosphere is the highly viscous, mechanically weak and ductilely deforming region of the upper_______ of Earth.",
                    question_hi: "3. एस्थेनोस्फीयर पृथ्वी के ऊपरी _______ का अत्यधिक चिपचिपा, यांत्रिक रूप से कमजोर और नमनीय रूप से विकृत क्षेत्र है।",
                    options_en: [" crust ", " core ", 
                                " crust and core ", " mantle "],
                    options_hi: [" पपड़ी", " अंतर्भाग",
                                " क्रस्ट और कोर", " आवरण"],
                    solution_en: "3.(d) The asthenosphere is the highly viscous, mechanically weak, and ductile region of the upper mantle of Earth. It lies below the lithosphere, between approximately 80 and 200 km below the surface.",
                    solution_hi: "3.(d) एस्थेनोस्फीयर पृथ्वी के ऊपरी मेंटल का अत्यधिक चिपचिपा, यांत्रिक रूप से कमजोर और नमनीय क्षेत्र है। यह स्थलमंडल के नीचे, सतह से लगभग 80 से 200 किमी नीचे स्थित है।",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "4",
                    section: "18",
                    question_en: "4. Which radioisotope is used for the treatment of metastatic bone cancer?",
                    question_hi: "4. मेटास्टेटिक हड्डी के कैंसर के इलाज के लिए किस विकिरण समस्थानिक का इस्तेमाल किया जाता है?",
                    options_en: [" Phosphorous-32 ", " Carbon-14 ", 
                                " Strontium-89 ", " Iodine- 131 "],
                    options_hi: [" फॉस्फोरस-32", " कार्बन-14",
                                " स्ट्रोंटियम-89", "आयोडीन- 131"],
                    solution_en: "4.(c) The most commonly used isotopes in the treatment of painful bone metastases are strontium-89 and samarium-153. ",
                    solution_hi: "4.(c) दर्दनाक हड्डी मेटास्टेस के उपचार में सबसे अधिक इस्तेमाल किया जाने वाला आइसोटोप स्ट्रोंटियम-89 और समैरियम-153 हैं।",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "5",
                    section: "18",
                    question_en: "5. In which year was the first World Environmental Day (WED) celebrated?",
                    question_hi: "5.पहला विश्व पर्यावरण दिवस (WED) किस वर्ष मनाया गया था?",
                    options_en: [" 1974", " 1972", 
                                " 1979", " 1980"],
                    options_hi: [" 1974", " 1972",
                                " 1979", " 1980"],
                    solution_en: "5.(a) In 1972, the UN General Assembly designated 5 June as World Environment Day (WED). The first celebration, under the slogan “Only One Earth” took place in 1974. The theme for World Environment Day 2021 is “Ecosystem Restoration” and will see the launch of the UN Decade on Ecosystem Restoration.",
                    solution_hi: "5.(a) 1972 में, संयुक्त राष्ट्र महासभा ने 5 जून को विश्व पर्यावरण दिवस (WED) के रूप में नामित किया। पहला उत्सव, \"केवल एक पृथ्वी\" के नारे के तहत 1974 में हुआ था। विश्व पर्यावरण दिवस 2021 का विषय \"पारिस्थितिकी तंत्र की बहाली\" है और इसमें पारिस्थितिकी तंत्र की बहाली पर संयुक्त राष्ट्र दशक का शुभारंभ होगा।",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "6",
                    section: "18",
                    question_en: "6. Which programme was launched to trace missing and destitute children and reunite them with their families?",
                    question_hi: "6.  लापता और बेसहारा बच्चों का पता लगाने और उन्हें उनके परिवारों के साथ पुनर्मिलन के लिए कौन सा कार्यक्रम शुरू किया गया था?",
                    options_en: [" Operation Muskaan ", " Operation Bachpan ", 
                                " Operation Kilkari ", " Operation Rahat "],
                    options_hi: [" ऑपरेशन मुस्कान", " ऑपरेशन बचपन",
                                " ऑपरेशन किलकारी", " ऑपरेशन राहत"],
                    solution_en: "6.(a) Operation Smile also called Operation Muskaan is an initiative of the Ministry of Home Affairs (MHA) to rescue/rehabilitate missing children.",
                    solution_hi: "6.(a) ऑपरेशन मुस्कान जिसे ऑपरेशन मुस्कान भी कहा जाता है, गृह मंत्रालय (MHA) की एक पहल है जो लापता बच्चों को बचाने/पुनर्वास के लिए है।",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "7",
                    section: "18",
                    question_en: "7. Which revolutionary organization in Bengal was established by Barindra Kumar Ghosh?",
                    question_hi: "7. बरिन्द्र कुमार घोष ने बंगाल में किस क्रांतिकारी संगठन की स्थापना की थी?",
                    options_en: [" Swadesh Bandhab Samiti ", " Brati Samiti ", 
                                " Anushilan Samiti ", " Sadhana Samaj "],
                    options_hi: [" स्वदेश बांधब समिति", " ब्राति समिति",
                                " अनुशीलन समिति", " साधना समाज"],
                    solution_en: "7.(c) Anushilan Samiti organization in Bengal was established by Barindra Kumar Ghosh, it was founded in 1902.",
                    solution_hi: "7.(c) बंगाल में अनुशीलन समिति संगठन की स्थापना बरिंद्र कुमार घोष ने की थी, इसकी स्थापना 1902 में हुई थी।",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "8",
                    section: "18",
                    question_en: "8. What is ‘EyeROV Tuna’?",
                    question_hi: "8.  \'EyeROV Tuna’क्या है?",
                    options_en: [" Heliport ", " Missile", 
                                " Underwater drone ", " Monorail "],
                    options_hi: [" हेलीपोर्ट", " मिसाइल",
                                " पानी के अंदर का ड्रोन ", " मोनोरेल"],
                    solution_en: "8.(c) The first commercial remotely operated vehicle (ROV)/ underwater drone, Eyeroc Tuna, was developed by EyeROV Technologies, a company incubating at Maker Village which is the largest hardware incubator in the country.",
                    solution_hi: "8.(c) पहला कमर्शियल रिमोट ऑपरेटेड व्हीकल (ROV) / अंडरवाटर ड्रोन, आईरोक टूना, आईआरओवी (EyeROV)  टेक्नोलॉजीज द्वारा विकसित किया गया था, जो मेकर विलेज में इनक्यूबेट करने वाली कंपनी है जो देश का सबसे बड़ा हार्डवेयर इनक्यूबेटर है।",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "9",
                    section: "18",
                    question_en: "9. Which soil is the largest available and the most intensively cultivated soil in India?",
                    question_hi: "9. भारत में सबसे बड़ी उपलब्ध और सबसे अधिक सघन खेती वाली मिट्टी कौन सी है?",
                    options_en: [" Alluvial soil ", " Desert soil ", 
                                " Red soil ", " Black soil"],
                    options_hi: [" जलोढ़ मिट्टी", " रेगिस्तानी मिट्टी",
                                " लाल मिट्टी", " काली मिट्टी<br /> "],
                    solution_en: "9.(a) Alluvial soils are by far the largest and the most important soil group of India. Alluvial soil is formed by the deposition of silt by the Indo-Gangetic-Brahmaputra rivers. This soil group covers around 46% of the total land area.",
                    solution_hi: "9.(a) जलोढ़ मिट्टी भारत का सबसे महत्वपूर्ण मृदा समूह है। जलोढ़ मिट्टी इंडो-गंगा-ब्रह्मपुत्र नदियों द्वारा गाद के जमाव से बनती है। यह मृदा समूह कुल भूमि क्षेत्र का लगभग 46% है।",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "10",
                    section: "18",
                    question_en: "10. Who said “Neither first, nor national not war of independence” with context to “ The Sepoy Mutiny and Revolt of 1857?",
                    question_hi: "10. 1857 के सिपाही विद्रोह और विद्रोह के संदर्भ में \"न तो पहले, न ही राष्ट्रीय स्वतंत्रता संग्राम\" किसने कहा था?",
                    options_en: [" Veer Savarkar ", " Jawaharlal Nehru ", 
                                " RC majumdar ", " SN Sen "],
                    options_hi: [" वीर सावरकर         ", " जवाहर लाल नेहरू",
                                " आरसी मजूमदार    ", " एस एन सेन"],
                    solution_en: "10.(c) RC Majumdar described the 1857 revolt as neither the first nor national war of independence.",
                    solution_hi: " 10.(c) RC मजूमदार ने 1857 के विद्रोह को न तो पहला और न ही राष्ट्रीय स्वतंत्रता संग्राम बताया।",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "11",
                    section: "18",
                    question_en: "11. What is NRHM?",
                    question_hi: "11. NRHM क्या है?",
                    options_en: [" National Rights Human Mission", " National Rural Health Mission ", 
                                " New Rural Health Mission ", " National Rapid Health Mission "],
                    options_hi: [" राष्ट्रीय अधिकार मानव मिशन", " राष्ट्रीय ग्रामीण स्वास्थ्य मिशन",
                                " नया ग्रामीण स्वास्थ्य मिशन", " राष्ट्रीय रैपिड हेल्थ मिशन"],
                    solution_en: "11.(b) The goals of the NRHM includes (i) reduction in Infant Mortality Rate (IMR) and Maternal Mortality Ratio (MMR); (ii) universal access to integrated comprehensive public health services; (iii) child health, water, sanitation, and hygiene; (iv) prevention and control of communicable and non-communicable diseases.",
                    solution_hi: "11.(b) NRHM के लक्ष्यों में शामिल हैं: (i) शिशु मृत्यु दर (IMR) और मातृ मृत्यु अनुपात (MMR) में कमी; (ii) एकीकृत व्यापक सार्वजनिक स्वास्थ्य सेवाओं तक सार्वभौमिक पहुंच; (iii) बाल स्वास्थ्य, पानी, स्वच्छता और स्वच्छता; (iv) संचारी और गैर-संचारी रोगों की रोकथाम और नियंत्रण।",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "12",
                    section: "18",
                    question_en: "12. Which of the following is related to the 124th Constitutional Amendment Bill?",
                    question_hi: "12. निम्नलिखित में से कौन 124वें संविधान संशोधन विधेयक से संबंधित है?",
                    options_en: [" Right to free and compulsory education till the age of fourteen ", " National Commission for Backward Class ", 
                                " GST Bill ", " 10% reservation to economically weaker sections in the general category "],
                    options_hi: [" चौदह वर्ष की आयु तक निःशुल्क और अनिवार्य शिक्षा का अधिकार", " पिछड़ा वर्ग के लिए राष्ट्रीय आयोग",
                                " GST विधेयक", " सामान्य वर्ग में आर्थिक रूप से कमजोर वर्ग को 10% आरक्षण"],
                    solution_en: "12.(d) Lok Sabha today passed the Constitution (124th Amendment) Bill 2019 to provide 10 per cent reservation in jobs and educational institutions to economically backward sections in the general category. It took the form of the 103rd Constitutional Amendment Act.",
                    solution_hi: "12.(d)  लोकसभा ने आज सामान्य वर्ग के आर्थिक रूप से पिछड़े वर्गों को नौकरियों और शैक्षणिक संस्थानों में 10 प्रतिशत आरक्षण प्रदान करने के लिए संविधान (124वां संशोधन) विधेयक 2019 पारित किया। इसने 103वें संविधान संशोधन अधिनियम का रूप धारण किया।",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "13",
                    section: "18",
                    question_en: "13. Who launched a special programme for school children called ‘Young Scientist Programme (Yuvika-2019)’?",
                    question_hi: "13. स्कूली बच्चों के लिए \'युवा वैज्ञानिक कार्यक्रम (युविका-2019)\' नामक एक विशेष कार्यक्रम किसने शुरू किया?",
                    options_en: [" ISRO", " IRDA", 
                                " DRDO", " NABARD"],
                    options_hi: [" ISRO", " IRDA",
                                " DRDO", " NABARD"],
                    solution_en: "13.(a) Indian Space Research Organisation (ISRO) has launched a special program for School Children called Young Scientist Programme from 2019, in tune with the Government\'s vision \'Jai Vigyan, Jai Anusandhan\'. ",
                    solution_hi: "13.(a) भारतीय अंतरिक्ष अनुसंधान संगठन (ISRO) ने सरकार के दृष्टिकोण \'जय विज्ञान, जय अनुसंधान\' के अनुरूप, 2019 से स्कूली बच्चों के लिए युवा वैज्ञानिक कार्यक्रम नामक एक विशेष कार्यक्रम शुरू किया है।",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "14",
                    section: "18",
                    question_en: "14. Involuntary actions such as breathing , salivation and peristalsis are controlled by the:",
                    question_hi: "14.अनैच्छिक क्रियाएं जैसे श्वास, लार आना और क्रमाकुंचन किसके द्वारा नियंत्रित होते हैं?",
                    options_en: [" cerebrum         ", " cerebellum ", 
                                " medulla oblongata  ", " hypothalamus "],
                    options_hi: [" मस्तिष्क", " अनुमस्तिष्क",
                                " मेरु-मज्जा", " अध:श्चेतक"],
                    solution_en: "14.(c) The medulla oblongata connects the brain to the spinal cord and controls various involuntary actions such as the heartbeat, breathing, blood pressure, and peristaltic movement in the body. Cerebrum, the largest part of the brain. The portion of the brain in the back of the head between the cerebrum and the brain stem. The function of the hypothalamus is to maintain your body\'s internal balance, which is known as homeostasis.",
                    solution_hi: "14.(c) मेडुला ऑबॉन्गाटा मस्तिष्क को रीढ़ की हड्डी से जोड़ता है और शरीर में विभिन्न अनैच्छिक क्रियाओं जैसे दिल की धड़कन, श्वास, रक्तचाप और पेरिस्टाल्टिक गति को नियंत्रित करता है। सेरेब्रम, मस्तिष्क का सबसे बड़ा भाग। सेरेब्रम और ब्रेन स्टेम के बीच सिर के पिछले हिस्से में मस्तिष्क का हिस्सा। हाइपोथैलेमस का कार्य आपके शरीर के आंतरिक संतुलन को बनाए रखना है, जिसे होमोस्टैसिस के रूप में जाना जाता है।",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "15",
                    section: "18",
                    question_en: "15. In 2018, the Uttar Pradesh Government launched the ODOP scheme to promote competitive and staple products from a region so that an economic geographical model can be developed. What does ODOP stand for?",
                    question_hi: "15. 2018 में, उत्तर प्रदेश सरकार ने एक क्षेत्र से प्रतिस्पर्धी और मुख्य उत्पादों को बढ़ावा देने के लिए ODOP योजना शुरू की ताकि एक आर्थिक भौगोलिक मॉडल विकसित किया जा सके। ODOP का पूर्ण रूप क्या है?",
                    options_en: [" One Domain One Product", " One Department One Producer", 
                                " One Division One Producer", " One District One Product"],
                    options_hi: [" One Domain One Product", " One Department One Producer",
                                " One Division One Producer", " One District One Product"],
                    solution_en: " 15.(d)  In 2018, the Uttar Pradesh Government launched the ODOP scheme to promote competitive and staple products from a region so that an economic geographical model can be developed. ODOP stands for One District One Product.",
                    solution_hi: "15.(d) 2018 में, उत्तर प्रदेश सरकार ने एक क्षेत्र से प्रतिस्पर्धी और मुख्य उत्पादों को बढ़ावा देने के लिए ODOP योजना शुरू की ताकि एक आर्थिक भौगोलिक मॉडल विकसित किया जा सके। ODOP का मतलब एक जिला एक उत्पाद है।",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "16",
                    section: "18",
                    question_en: "16. The full form of MB is :",
                    question_hi: "16. MB का पूर्ण रूप क्या है?",
                    options_en: [" Macrobyte ", " Microbyte ", 
                                " Megabyte ", " Minibyte  <br /> "],
                    options_hi: [" मैक्रोबाइट", " माइक्रोबाइट",
                                " मेगाबाइट", " मिनीबाइट<br /> "],
                    solution_en: "16.(c) Megabyte (MB) is a data measurement unit applied to digital computer or media storage. One MB equals one million (10^6 or 1,000,000) bytes. <br /> ",
                    solution_hi: "16.(c) मेगाबाइट (MB) एक डेटा मापन इकाई है जिसे डिजिटल कंप्यूटर या मीडिया स्टोरेज पर लागू किया जाता है। एक MB एक मिलियन (10^6 या 1,000,000) बाइट्स के बराबर होता है।",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "17",
                    section: "18",
                    question_en: "17. The world’s first handwritten needle book is:",
                    question_hi: "17. दुनिया की पहली हस्तलिखित नीडल पुस्तक कौन सी  है?",
                    options_en: [" Devdas ", " Moti Mahal ", 
                                " Madhushala ", " Chitra "],
                    options_hi: [" देवदास", " मोती महल",
                                " मधुशाला", " चित्रा"],
                    solution_en: "17.(c) The world’s first handwritten needle book is Madhushala written by Harivansh Rai Bachan. Devdas written by Srat Chandra Chattopadhyay.Chitra is a one-act play written by Rabindranath Tagore.",
                    solution_hi: "17.(c) दुनिया की पहली हस्तलिखित सुई किताब हरिवंश राय बच्चन द्वारा लिखित मधुशाला है। श्रत चंद्र चट्टोपाध्याय द्वारा लिखित देवदास। चित्रा रवींद्रनाथ टैगोर द्वारा लिखित एक-एकांकी नाटक है।",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "18",
                    section: "18",
                    question_en: "<p>18. Which UN organisation deals with illicit trafficking and drug abuse?</p>",
                    question_hi: "<p>18. संयुक्त राष्ट्र का कौन सा संगठन अवैध तस्करी और नशीली दवाओं के दुरुपयोग से संबंधित है?</p>",
                    options_en: ["<p>UNEP</p>", "<p>UNODC</p>", 
                                "<p>UNICEF</p>", "<p>UNFPA</p>"],
                    options_hi: ["<p>UNEP</p>", "<p>UNODC</p>",
                                "<p>UNICEF</p>", "<p>UNFPA</p>"],
                    solution_en: "<p>18.(b) UNODC(United Nations Office on Drugs and Crime) was established to assist the UN in better addressing a coordinated, comprehensive response to the interrelated issues of illicit trafficking in and abuse. It was established in 1997, headquartered in Vienna, Austria.</p>",
                    solution_hi: "<p>18.(b) UNODC (ड्रग्स एंड क्राइम पर संयुक्त राष्ट्र कार्यालय) की स्थापना संयुक्त राष्ट्र को अवैध तस्करी और दुरुपयोग के परस्पर संबंधित मुद्दों पर एक समन्वित, व्यापक प्रतिक्रिया को बेहतर ढंग से संबोधित करने में सहायता करने के लिए की गई थी। यह 1997 में स्थापित किया गया था, जिसका मुख्यालय वियना, ऑस्ट्रिया में है।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "19",
                    section: "18",
                    question_en: "19. Who is the  first Indian  to make the record by winning two gold medals in the Paralympic Games?",
                    question_hi: "19. पैरालंपिक खेलों में दो स्वर्ण पदक जीतकर रिकॉर्ड बनाने वाले पहले भारतीय कौन हैं?",
                    options_en: [" Neeraj Yadav ", " Devendra Jhajharia ", 
                                " Sandeep Chaudhary ", " Pardeep "],
                    options_hi: [" नीरज यादव", " देवेंद्र झाझरिया",
                                " संदीप चौधरी", " प्रदीप"],
                    solution_en: "19.(b) Devendra Jhajharia is the first Indian to make the record by winning two gold medals in the Paralympic Games. In the Tokyo Paralympic 2021 he won a silver medal.",
                    solution_hi: "19.(b) देवेंद्र झाझरिया पैरालंपिक खेलों में दो स्वर्ण पदक जीतकर रिकॉर्ड बनाने वाले पहले भारतीय हैं। उन्होंने टोक्यो पैरालंपिक 2021 में रजत पदक जीता था।",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "20",
                    section: "18",
                    question_en: "20. What does BHIM stand for:",
                    question_hi: "20.  BHIM का पूर्ण रूप क्या है ?",
                    options_en: [" Bheem Interface for Mobile", " Bharat Interface for Money", 
                                " Bheem Interface for Mobile", " Bharat Interface for Money<br />  "],
                    options_hi: [" Bheem Interface for Mobile", " Bharat Interface for Money",
                                " Bheem Interface for Mobile", " Bharat Interface for Money"],
                    solution_en: "20.(d) Bharat Interface for Money (BHIM) is an app that lets you make simple, easy and quick payment transactions using Unified Payments Interface (UPI).",
                    solution_hi: "20.(d) भारत इंटरफेस फॉर मनी (BHIM) एक ऐसा ऐप है जो आपको यूनिफाइड पेमेंट्स इंटरफेस (UPI) का उपयोग करके सरल, आसान और त्वरित भुगतान लेनदेन करने देता है।",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "21",
                    section: "18",
                    question_en: "21. Name an enzyme used for the conversion of proteins into a peptide.",
                    question_hi: "21. प्रोटीन को पेप्टाइड में बदलने के लिए उपयोग किए जाने वाले एंजाइम का नाम बताइए।",
                    options_en: [" Lactase ", " Invertase ", 
                                " Zymase ", " Pepsin <br /> "],
                    options_hi: [" लैक्टेज", " इन्वर्टेज",
                                " ज़ाइमेज़", " पेप्सिन"],
                    solution_en: "21.(d) Pepsin enzyme is used for the conversion of proteins into a peptide. Lactase is an enzyme. It breaks down lactose, a sugar in milk and milk products. Invertase is an enzyme that catalyzes the hydrolysis (breakdown) of sucrose (table sugar) into fructose and glucose. Zymase, a mixture of enzymes obtained from yeast which catalyze the breakdown of sugars in alcoholic fermentation. ",
                    solution_hi: "21.(d) पेप्सिन एंजाइम का उपयोग प्रोटीन को पेप्टाइड में बदलने के लिए किया जाता है। लैक्टेज एक एंजाइम है। यह लैक्टोज, दूध और दूध उत्पादों में एक चीनी को तोड़ता है। इनवर्टेज एक एंजाइम है जो सुक्रोज (टेबल शुगर) के हाइड्रोलिसिस (ब्रेकडाउन) को फ्रुक्टोज और ग्लूकोज में उत्प्रेरित करता है। ज़ाइमेज़, खमीर से प्राप्त एंजाइमों का मिश्रण जो अल्कोहलिक किण्वन में शर्करा के टूटने को उत्प्रेरित करता है।",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "22",
                    section: "18",
                    question_en: "22. Which of the following pairs is NOT correctly matched (UNESCO Natural Heritage Site to its location)?",
                    question_hi: "22. निम्नलिखित में से कौन सा जोड़ा सही ढंग से मेल नहीं खाता है (UNESCO  प्राकृतिक विरासत स्थल इसके स्थान पर)?",
                    options_en: [" Elephanta Caves- Karnataka ", " Fatehpur Sikri- Uttar Pradesh ", 
                                " Sun Temple- Odisha ", " Sundarbans National Park- West Bengal <br />   "],
                    options_hi: [" एलीफेंटा गुफाएं- कर्नाटक", " फतेहपुर सीकरी- उत्तर प्रदेश",
                                " सूर्य मंदिर- उड़ीसा", " सुंदरवन राष्ट्रीय उद्यान- पश्चिम बंगाल<br /> "],
                    solution_en: "22.(a) The Elephanta Caves are located in Western India on Elephanta Island (otherwise known as the Island of Gharapuri), Maharashtra, which features two hillocks separated by a narrow valley.",
                    solution_hi: "22.(a) एलीफेंटा गुफाएं पश्चिमी भारत में एलीफेंटा द्वीप (अन्यथा घरापुरी द्वीप के रूप में जाना जाता है), महाराष्ट्र पर स्थित हैं, जिसमें एक संकीर्ण घाटी से अलग दो पहाड़ियों की विशेषता है।",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "23",
                    section: "18",
                    question_en: "<p>23. Megaloblastic anaemia is caused by the deficiency of which vitamin?</p>",
                    question_hi: "<p>23. मेगालोब्लास्टिक एनीमिया किस विटामिन की कमी से होता है?</p>",
                    options_en: ["<p>Vitamin-A</p>", "<p>Vitamin- <math display=\"inline\"><msub><mrow><mi>B</mi></mrow><mrow><mn>2</mn></mrow></msub></math></p>", 
                                "<p>Vitamin-C</p>", "<p>Vitamin-<math display=\"inline\"><msub><mrow><mi>B</mi></mrow><mrow><mn>9</mn></mrow></msub></math></p>"],
                    options_hi: ["<p>विटामिन A</p>", "<p>विटामिन - <math display=\"inline\"><msub><mrow><mi>B</mi></mrow><mrow><mn>2</mn></mrow></msub></math></p>",
                                "<p>विटामिन -C</p>", "<p>विटामिन -<math display=\"inline\"><msub><mrow><mi>B</mi></mrow><mrow><mn>9</mn></mrow></msub></math></p>"],
                    solution_en: "<p>23.(d) Megaloblastic anaemia is caused by the deficiency of vitamin B<sub>9</sub>. Low levels of folic acid can cause megaloblastic anaemia. With this condition, red blood cells are larger than normal. There are fewer of these cells.Either a lack of vitamin B-12 or a lack of folate causes a type of anaemia called megaloblastic anaemia (pernicious anaemia).</p>",
                    solution_hi: "<p>23.(d) मेगालोब्लास्टिक एनीमिया विटामिन बी<sub>9</sub> की कमी के कारण होता है। फोलिक एसिड का निम्न स्तर मेगालोब्लास्टिक एनीमिया का कारण बन सकता है। इस स्थिति में, लाल रक्त कोशिकाएं सामान्य से बड़ी हो जाती हैं। इनमें से कम कोशिकाएं हैं। या तो विटामिन बी -12 की कमी या फोलेट की कमी से एक प्रकार का एनीमिया होता है जिसे मेगालोब्लास्टिक एनीमिया (हानिकारक एनीमिया) कहा जाता है।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "24",
                    section: "18",
                    question_en: "24. Which of the following is dedicated as India’s first educational satellite?",
                    question_hi: "24. निम्नलिखित में से कौन भारत के पहले शैक्षिक उपग्रह के रूप में समर्पित है?",
                    options_en: [" GSAT-3  ", " INSAT- 4A ", 
                                " HAMSAT ", " CARTOSAT-1 "],
                    options_hi: [" जीसैट-3", " इन्सैट- 4A",
                                " हैमसेट  ", " कार्टोसैट-1"],
                    solution_en: "24.(a) GSAT-3, also known as EDUSAT, was a communications satellite which was launched on 20 September 2004 by the Indian Space Research Organisation.",
                    solution_hi: "24.(a)  GSAT-3, जिसे EDUSAT के नाम से भी जाना जाता है, एक संचार उपग्रह था जिसे भारतीय अंतरिक्ष अनुसंधान संगठन द्वारा 20 सितंबर 2004 को लॉन्च किया गया था।",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "25",
                    section: "18",
                    question_en: "25. Which of the following is NOT a type of social networking application?",
                    question_hi: "25. निम्न में से कौन सा सोशल नेटवर्किंग एप्लिकेशन का एक प्रकार नहीं है?",
                    options_en: [" Linkedin ", " Twitter ", 
                                " Facebook ", " Haptik "],
                    options_hi: [" लिंक्डइन", " ट्विटर",
                                " फेसबुक", " हैप्टिक "],
                    solution_en: "25.(d) Haptik builds Intelligent Virtual Assistant solutions that enhance customer experience and drive ROI for large brands. ",
                    solution_hi: "25.(d) हैप्टिक  इंटेलिजेंट वर्चुअल असिस्टेंट समाधान बनाता है, जो ग्राहक अनुभव को बढ़ाता है और बड़े ब्रांडों के लिए ROI चलाता है।",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "26",
                    section: "18",
                    question_en: "26. In which year did Dadabhai Naroji become the first Indian member of the House of Commons in the United Kingdom?",
                    question_hi: "26. दादाभाई नरोजी किस वर्ष यूनाइटेड किंगडम में हाउस ऑफ कॉमन्स के पहले भारतीय सदस्य बने थे?",
                    options_en: [" 1896", " 1892", 
                                " 1893", " 1891"],
                    options_hi: [" 1896", " 1892",
                                " 1893", " 1891"],
                    solution_en: "26.(b) Dadabhai Naroji moved to Britain once again and continued his political involvement. Elected to the Liberal Party in Finsbury Central at the 1892 general election, he was the first British Indian MP,member of the House of Commons in the United Kingdom.",
                    solution_hi: "26.(b) दादाभाई नरोजी एक बार फिर ब्रिटेन चले गए और अपनी राजनीतिक भागीदारी जारी रखी। 1892 के आम चुनाव में फिन्सबरी सेंट्रल में लिबरल पार्टी के लिए चुने गए, वह यूनाइटेड किंगडम में हाउस ऑफ कॉमन्स के पहले ब्रिटिश भारतीय सांसद थे।",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "27",
                    section: "18",
                    question_en: "27. Which of the following is a disease caused by bacteria?",
                    question_hi: "27. निम्नलिखित में से कौन बैक्टीरिया के कारण होने वाला रोग है?",
                    options_en: [" small pox ", " Anthrax ", 
                                " AIDS ", " Rabies <br /> "],
                    options_hi: [" चेचक", " एंथ्रेक्स",
                                " एड्स", " रेबीज"],
                    solution_en: "27.(b) Bacterial diseases are Tuberculosis, Anthrax,Tetanus, Leptospirosis, Pneumonia, Cholera, Botulism, Pseudomonas Infection. Viral Disease are,Coronavirus, measles, rubella, chickenpox/shingles, roseola, smallpox,,and chikungunya virus infection.",
                    solution_hi: "27.(b) जीवाणु रोग हैं क्षय रोग, एंथ्रेक्स, टेटनस, लेप्टोस्पायरोसिस, निमोनिया, हैजा, बोटुलिज़्म, स्यूडोमोनास संक्रमण। वायरल रोग हैं, कोरोनावायरस, खसरा, रूबेला, चिकनपॉक्स / दाद, गुलाबोला, चेचक और चिकनगुनिया वायरस का संक्रमण।",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "28",
                    section: "18",
                    question_en: "28. The 78th anniversary of the Quit India Movement was observed on:",
                    question_hi: "28. भारत छोड़ो आंदोलन की 78वीं वर्षगांठ कब मनाई गई?",
                    options_en: [" 15th August 2018 ", " 5th August 2020 ", 
                                " 7th August 2019 ", " 8th August 2020 "],
                    options_hi: [" 15 अगस्त 2018", " 5 अगस्त 2020",
                                " 7 अगस्त 2019", " 8 अगस्त 2020<br /> "],
                    solution_en: "28.(d) The 78th anniversary of the Quit India Movement was observed on 8th August 2020.  On 8 August 1942 at the All-India Congress Committee session in Bombay, Mohandas Karamchand Gandhi launched the \'Quit India\' movement.",
                    solution_hi: "28.(d) 8 अगस्त 2020 को भारत छोड़ो आंदोलन की 78वीं वर्षगांठ मनाई गई। 8 अगस्त 1942 को बॉम्बे में अखिल भारतीय कांग्रेस कमेटी के सत्र में, मोहनदास करमचंद गांधी ने \'भारत छोड़ो\' आंदोलन शुरू किया।",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "29",
                    section: "18",
                    question_en: "29. Which Prime Minister was born after India got independence?",
                    question_hi: "29. भारत को आजादी मिलने के बाद किस प्रधानमंत्री का जन्म हुआ था?",
                    options_en: [" Narendra Modi ", " Atal Bihari Vajpayee ", 
                                " IK Gujral ", " Manmohan Singh <br /> "],
                    options_hi: [" नरेंद्र मोदी", " अटल बिहारी वाजपेयी",
                                " आईके गुजराल", " मनमोहन सिंह<br /> "],
                    solution_en: "29.(a) Prime Minister Narendra Modi, who was born 66 years ago on September 17, 1950, is the first Prime Minister to be born after India\'s Independence in 1947.",
                    solution_hi: "29.(a) प्रधानमंत्री नरेंद्र मोदी, जिनका जन्म 66 साल पहले 17 सितंबर 1950 को हुआ था, 1947 में भारत की आजादी के बाद पैदा होने वाले पहले प्रधानमंत्री हैं।",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "30",
                    section: "18",
                    question_en: "30. Who was the first female judge to be appointed to the Supreme Court of India?",
                    question_hi: "30. भारत के सर्वोच्च न्यायालय में नियुक्त होने वाली पहली महिला न्यायाधीश कौन थी?",
                    options_en: [" Gyan Sudha Misra ", " Indira Banerjee ", 
                                " Fathima Beevi ", " R Banumathi "],
                    options_hi: [" ज्ञान सुधा मिश्रा", " इंदिरा बनर्जी",
                                " फातिमा बीविक", " आर भानुमति"],
                    solution_en: "30.(c) Justice Fathima Beevi was appointed the country\'s first female Supreme Court judge in 1989. Justice Anna Chandy also known as Anna Chandi, was the first female judge and then High Court judge (1959) in India.",
                    solution_hi: "30.(c) जस्टिस फातिमा बीवी को 1989 में देश की पहली महिला सुप्रीम कोर्ट जज नियुक्त किया गया था। जस्टिस अन्ना चांडी जिन्हें अन्ना चंडी के नाम से भी जाना जाता है, भारत में पहली महिला जज और फिर हाई कोर्ट जज (1959) थीं।",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "31",
                    section: "18",
                    question_en: "31. Synapse is the point at which a nervous impulse passes from:",
                    question_hi: "31. सिनैप्स वह बिंदु है जहां से एक तंत्रिका आवेग ________ से गुजरता है",
                    options_en: [" one epithelial cell to another ", " one endocrine gland to another ", 
                                " the brain to the spinal cord ", " one neuron to another "],
                    options_hi: [" एक उपकला कोशिका से दूसरी", " एक अंतःस्रावी ग्रंथि दूसरे को",
                                " मस्तिष्क रीढ़ की हड्डी तक", " एक न्यूरॉन दूसरे में"],
                    solution_en: "31.(d) Synapse, also called neuronal junction, the site of transmission of electric nerve impulses between two nerve cells (neurons) or between a neuron and a gland or muscle cell (effector). ",
                    solution_hi: "31.(d) सिनैप्स, जिसे न्यूरोनल जंक्शन भी कहा जाता है, दो तंत्रिका कोशिकाओं (न्यूरॉन्स) के बीच या एक न्यूरॉन और एक ग्रंथि या मांसपेशी कोशिका (प्रभावक) के बीच विद्युत तंत्रिका आवेगों के संचरण की साइट।",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "32",
                    section: "18",
                    question_en: "32. In which year did a team of German and Italian archaeologists begin surface exploration at Mohenjodaro?",
                    question_hi: "32. जर्मन और इतालवी पुरातत्वविदों की एक टीम ने मोहनजोदड़ो में सतह की खोज किस वर्ष शुरू की थी?",
                    options_en: [" 1970", " 1990", 
                                " 1980", " 1955"],
                    options_hi: [" 1970", " 1990",
                                " 1980", " 1955"],
                    solution_en: "32.(c) In 1980 a team of German and Italian archaeologists began surface exploration at Mohenjodaro. The name Mohenjo-daro is reputed to signify “the mound of the dead.” The archaeological importance of the site was first recognized in 1922 by RD Banerjee.",
                    solution_hi: "32.(c) 1980 में जर्मन और इतालवी पुरातत्वविदों की एक टीम ने मोहनजोदड़ो में सतह की खोज शुरू की। मोहनजोदड़ो नाम \"मृतकों के टीले\" को दर्शाने के लिए प्रतिष्ठित है। साइट के पुरातात्विक महत्व को पहली बार 1922 में आरडी बनर्जी द्वारा पहचाना गया था।",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "33",
                    section: "18",
                    question_en: "33. Where did the first confirmed death from COVID - 19 occur in India?",
                    question_hi: "33. भारत में COVID-19 से पहली पुष्ट मृत्यु कहाँ हुई?",
                    options_en: [" Delhi ", " Kerala ", 
                                " Maharashtra ", " Karnataka "],
                    options_hi: [" दिल्ली", " केरल",
                                " महाराष्ट्र", " कर्नाटक"],
                    solution_en: "33.(d) The first confirmed death from COVID - 19 occurred in India in Karnataka. The outbreak of Novel coronavirus disease (COVID-19) was initially noticed in a seafood market in Wuhan city in Hubei Province of China in mid- December, 2019.",
                    solution_hi: "33.(d) COVID-19 से पहली पुष्ट मौत भारत में कर्नाटक में हुई। नोवेल कोरोनावायरस रोग (COVID-19) का प्रकोप शुरू में दिसंबर, 2019 के मध्य में चीन के हुबेई प्रांत के वुहान शहर में एक समुद्री भोजन बाजार में देखा गया था।",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "34",
                    section: "18",
                    question_en: "34. Vijaya Mulay, who died in May, 2019 was a prominent personality in the field of education. A number of innovative educational animation films on Doordarshan can be attributed to her one of which is:",
                    question_hi: "34. विजया मुले, जिनका मई, 2019 में निधन हो गया, शिक्षा के क्षेत्र में एक प्रमुख व्यक्तित्व थे। दूरदर्शन पर कई नवीन शैक्षिक एनिमेशन फिल्मों का श्रेय उन्हें दिया जा सकता है, जिनमें से एक है:",
                    options_en: [" Baje Sargam har Taraf Se ", " Mile Sur Mera Tumhara ", 
                                " Saare Jahan Se Achcha ", " Ek Anek Aur Ekta "],
                    options_hi: [" बाजे सरगम हर तरफ से", " मिले सुर मेरा तुम्हारा",
                                " सारे जहां से अच्छा", " एक अनेक और एकता"],
                    solution_en: "34.(d) Vijaya Mulay, who died in May, 2019 was a prominent personality in the field of education. A number of innovative educational animation films on Doordarshan can be attributed to her, one of which is Ek Anek Aur Ekta.",
                    solution_hi: "34.(d) विजया मुले, जिनका मई, 2019 में निधन हो गया, शिक्षा के क्षेत्र में एक प्रमुख व्यक्तित्व थे। दूरदर्शन पर कई नवीन शैक्षिक एनीमेशन फिल्मों का श्रेय उन्हें दिया जा सकता है, जिनमें से एक है “एक अनेक और एकता”।",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "35",
                    section: "18",
                    question_en: "35. Who was awarded the Dadasaheb Phalke Award posthumously at the 65th National Film Award?",
                    question_hi: "35. 65वें राष्ट्रीय फिल्म पुरस्कार में मरणोपरांत दादा साहब फाल्के पुरस्कार से किसे सम्मानित किया गया?",
                    options_en: [" Kader Khan ", " Vinod Khanna ", 
                                " Sridevi ", " Shammi Kapoor "],
                    options_hi: [" कादर खान", " विनोद खन्ना",
                                " श्रीदेवी", " शम्मी कपूर"],
                    solution_en: "35.(b) Vinod Khanna was awarded the Dadasaheb Phalke Award posthumously. Amitabh Bachhan won in 2018. Veteran actor Rajinikanth was conferred Indian cinema\'s top honour, the Dadasaheb Phalke Award for 2020. The first recipient of the award was actress Devika Rani.",
                    solution_hi: "35.(b) विनोद खन्ना को मरणोपरांत दादा साहब फाल्के पुरस्कार से सम्मानित किया गया। अमिताभ बच्चन ने 2018 में जीता। अनुभवी अभिनेता रजनीकांत को भारतीय सिनेमा के सर्वोच्च सम्मान, दादा साहब फाल्के पुरस्कार 2020 से सम्मानित किया गया। पुरस्कार की पहली प्राप्तकर्ता अभिनेत्री देविका रानी थीं।",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "36",
                    section: "18",
                    question_en: "36. The glowing surface of the sun is called:",
                    question_hi: "36. सूर्य की चमकीली सतह क्या कहलाती है?",
                    options_en: [" photosphere   ", " chromosphere ", 
                                " lithosphere ", " corona <br /> "],
                    options_hi: [" फोटोस्फीयर", " वर्णमण्डल",
                                " स्थलमंडल", " कोरोना"],
                    solution_en: "36.(a) The glowing surface of the sun is called the photosphere. The lower region of the Sun\'s atmosphere is called the chromosphere. The lithosphere is the rocky outer part of the Earth. Corona is a luminous envelope of plasma that surrounds the Sun and other celestial bodies",
                    solution_hi: "36.(a) सूर्य की चमकती हुई सतह को प्रकाशमंडल कहते हैं। सूर्य के वायुमंडल के निचले क्षेत्र को वर्णमण्डल कहा जाता है। स्थलमंडल पृथ्वी का चट्टानी बाहरी भाग है। कोरोना प्लाज्मा का एक चमकदार आवरण है जो सूर्य और अन्य खगोलीय पिंडों को घेरता है।",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "37",
                    section: "18",
                    question_en: "37. In Tahngka Paintings, images of Lord ___________ are made on cotton or silk cloth.",
                    question_hi: "37. तहंगका पेंटिंग्स में भगवान ___________ के चित्र सूती या रेशमी कपड़े पर बनाए जाते हैं",
                    options_en: [" Mahavira ", " Buddha ", 
                                " Vishnu ", " Shiva <br /> "],
                    options_hi: [" महावीर", " बुद्ध",
                                " विष्णु", " शिव<br /> "],
                    solution_en: "37.(b) In Thangka Paintings, images of Lord Buddha are made on cotton or silk cloth.",
                    solution_hi: "37.(b) थंगका पेंटिंग्स में भगवान बुद्ध के चित्र सूती या रेशमी कपड़े पर बनाए जाते हैं।",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "38",
                    section: "18",
                    question_en: "38. What type of satellite is APPLE?",
                    question_hi: "38.  एप्पल किस प्रकार का उपग्रह है",
                    options_en: [" Meteorology satellite ", " Remote sensing satellite ", 
                                " Earth observation satellite ", " Communication satellite <br /> "],
                    options_hi: [" मौसम विज्ञान उपग्रह", " सुदूर संवेदन उपग्रह",
                                " पृथ्वी अवलोकन उपग्रह", " संचार उपग्रह"],
                    solution_en: "38.(d) The Ariane Passenger Payload Experiment (APPLE) was ISRO\'s first indigenous, experimental communication satellite. APPLE was designed and built as a sandwich passenger-carrying Meteosat on top and CAT (Capsule Ariane Technologique) module below. It was boosted into Geo-Synchronous Orbit (GSO) by ISRO\'s own apogee motor derived from the fourth stage motor of the SLV-3.",
                    solution_hi: "38.(d) एरियन पैसेंजर पेलोड एक्सपेरिमेंट (APPLE)), ISRO का पहला स्वदेशी, प्रायोगिक संचार उपग्रह था। APPLE को शीर्ष पर एक सैंडविच यात्री ले जाने वाले मेटोसैट और नीचे CAT (कैप्सूल एरियन टेक्नोलॉजिक) मॉड्यूल के रूप में डिजाइन और बनाया गया था। इसे SLV-3. के चौथे चरण की मोटर से प्राप्त ISRO की अपनी अपभू मोटर द्वारा भू-तुल्यकालिक कक्षा (GSO) में बढ़ाया गया था।",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "39",
                    section: "18",
                    question_en: "39. The Kolhati language is spoken by people who live in:",
                    question_hi: "39. कोल्हाटी भाषा उन लोगों द्वारा बोली जाती है जो_________में रहते हैं",
                    options_en: [" Maharashtra ", " Bihar ", 
                                " Andhra Pradesh ", " Odisha "],
                    options_hi: [" महाराष्ट्र", " बिहार",
                                " आंध्र प्रदेश", " उड़ीसा"],
                    solution_en: "39.(a)  They are classified as a nomadic tribe by the government of Maharashtra. They have also been employed with tamasha troupes. The kolhati language is spoken in considerable numbers in Pune district.",
                    solution_hi: "39.(a) उन्हें महाराष्ट्र सरकार द्वारा खानाबदोश जनजाति के रूप में वर्गीकृत किया गया है। उन्हें तमाशा मंडली के साथ भी नियुक्त किया गया है। कोल्हाटी भाषा पुणे जिले में काफी संख्या में बोली जाती है।",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "40",
                    section: "misc",
                    question_en: "40. What is the emblem of RBI?",
                    question_hi: "40. RBI का प्रतीक क्या है",
                    options_en: [" Pyramid and an eagle ", " Capital of Ashoka Pillar", 
                                " Bengal tiger in front of a palm tree ", " Dog sitting in a defensive state <br /> "],
                    options_hi: [" पिरामिड और एक चील", " अशोक स्तंभ की राजधानी",
                                " ताड़ के पेड़ के सामने बंगाल टाइगर", " रक्षात्मक अवस्था में बैठा कुत्ता"],
                    solution_en: "40.(c) The RBI Act of 1934 established it as the banker for the central government. The official emblem of the apex bank – a palm tree and a tiger.",
                    solution_hi: "40.(c) 1934 के  RBI अधिनियम ने इसे केंद्र सरकार के लिए बैंकर के रूप में स्थापित किया। शीर्ष बैंक का आधिकारिक प्रतीक - एक ताड़ का पेड़ और एक बाघ।",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>