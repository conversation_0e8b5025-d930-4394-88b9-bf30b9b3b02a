<!DOCTYPE html>
<html>
<head>
    <title>Error</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            background: #f5f6fa;
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: 100vh;
            padding: 20px;
        }
        .error-card {
            background: white;
            border-radius: 16px;
            padding: 30px;
            max-width: 500px;
            width: 100%;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            text-align: center;
        }
        .error-icon {
            font-size: 3rem;
            color: #d63031;
            margin-bottom: 20px;
        }
        .error-title {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 15px;
            color: #2d3436;
        }
        .error-message {
            color: #636e72;
            margin-bottom: 25px;
        }
        .btn-primary {
            background: #6c5ce7;
            border: none;
            padding: 10px 20px;
            border-radius: 10px;
            font-weight: 500;
        }
    </style>
</head>
<body>
    <div class="error-card">
        <div class="error-icon">
            <i class="fas fa-exclamation-circle"></i>
        </div>
        <h3 class="error-title">Error Loading Test</h3>
        <p class="error-message">An error occurred while generating the test interface:</p>
        <p class="text-muted mb-4">Test data must contain both subjects and questions</p>
        <button class="btn btn-primary" onclick="history.back()">
            <i class="fas fa-arrow-left me-2"></i>Go Back
        </button>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>