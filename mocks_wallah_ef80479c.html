<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">8:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 25</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">25</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 8 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["18"] = {
                name: "General Awareness",
                start: 0,
                end: 23
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="18">General Awareness</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 24,
                end: 24
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "18",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">1. </span><span style=\"font-family: Cambria Math;\">The Planning Commission of India was replaced by NITI Aayog in______</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">1. </span><span style=\"font-family: Cambria Math;\">&#2349;&#2366;&#2352;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2351;&#2379;&#2332;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2310;&#2351;&#2379;&#2327;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> _____________ </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2344;&#2368;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2310;&#2351;&#2379;&#2327;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2381;&#2357;&#2366;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2360;&#2381;&#2341;&#2366;&#2346;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2327;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2341;&#2366;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                    options_en: ["<p>2016</p>\n", "<p>2015</p>\n", 
                                "<p>2018</p>\n", "<p>2017</p>\n"],
                    options_hi: ["<p>2016</p>\n", "<p>2015</p>\n",
                                "<p>2018</p>\n", "<p>2017</p>\n"],
                    solution_en: "<p>1.<span style=\"font-family: Cambria Math;\">(b)<strong> 2015. </strong></span><strong>Planning Commission: </strong><span>Formulating five-year plans for economic and social development. </span><strong>Established:</strong><span> 15 March 1950. It is only an advisory body.</span><strong> First chairman -</strong><span> Jawaharlal Nehru. First deputy Chairman (Appointed by the Union Cabinet) - Gulzarilal Nanda. In 2014, Narendra Modi government decided to wind down the Planning Commission. It was replaced by the newly formed NITI Aayog. </span><span>NITI Aayog (National Institution for Transforming India) is neither a constitutional body nor a statutory body.</span></p>\n",
                    solution_hi: "<p>1.<span style=\"font-family: Cambria Math;\">(b)&nbsp;</span><strong>2015&#2404; &#2351;&#2379;&#2332;&#2344;&#2366; &#2310;&#2351;&#2379;&#2327;:</strong><span style=\"font-weight: 400;\"> &#2310;&#2352;&#2381;&#2341;&#2367;&#2325; &#2324;&#2352; &#2360;&#2366;&#2350;&#2366;&#2332;&#2367;&#2325; &#2357;&#2367;&#2325;&#2366;&#2360; &#2325;&#2375; &#2354;&#2367;&#2319; &#2346;&#2306;&#2330;&#2357;&#2352;&#2381;&#2359;&#2368;&#2351; &#2351;&#2379;&#2332;&#2344;&#2366;&#2319;&#2306; &#2340;&#2376;&#2351;&#2366;&#2352; &#2325;&#2352;&#2344;&#2366;&#2404; </span><strong>&#2360;&#2381;&#2341;&#2366;&#2346;&#2344;&#2366;:</strong><span style=\"font-weight: 400;\"> 15 &#2350;&#2366;&#2352;&#2381;&#2330; 1950&#2404; &#2351;&#2361; &#2325;&#2375;&#2357;&#2354; &#2319;&#2325; &#2360;&#2354;&#2366;&#2361;&#2325;&#2366;&#2352; &#2344;&#2367;&#2325;&#2366;&#2351; &#2341;&#2366;&#2404; </span><strong>&#2346;&#2381;&#2352;&#2341;&#2350; &#2309;&#2343;&#2381;&#2351;&#2325;&#2381;&#2359; - </strong><span style=\"font-weight: 400;\">&#2332;&#2357;&#2366;&#2361;&#2352;&#2354;&#2366;&#2354; &#2344;&#2375;&#2361;&#2352;&#2370;&#2404; &#2346;&#2381;&#2352;&#2341;&#2350; &#2313;&#2346;&#2366;&#2343;&#2381;&#2351;&#2325;&#2381;&#2359;&nbsp; (&#2325;&#2375;&#2306;&#2342;&#2381;&#2352;&#2368;&#2351; &#2350;&#2306;&#2340;&#2381;&#2352;&#2367;&#2350;&#2306;&#2337;&#2354; &#2342;&#2381;&#2357;&#2366;&#2352;&#2366; &#2344;&#2367;&#2351;&#2369;&#2325;&#2381;&#2340;) - &#2327;&#2369;&#2354;&#2332;&#2366;&#2352;&#2368;&#2354;&#2366;&#2354; &#2344;&#2306;&#2342;&#2366;&#2404; &#2344;&#2352;&#2375;&#2306;&#2342;&#2381;&#2352; &#2350;&#2379;&#2342;&#2368; &#2360;&#2352;&#2325;&#2366;&#2352; 2014 &#2350;&#2375;&#2306; &#2344;&#2375; &#2351;&#2379;&#2332;&#2344;&#2366; &#2310;&#2351;&#2379;&#2327; &#2325;&#2379; &#2348;&#2306;&#2342; &#2325;&#2352;&#2344;&#2375; &#2325;&#2366; &#2347;&#2376;&#2360;&#2354;&#2366; &#2325;&#2367;&#2351;&#2366;&#2404; &#2311;&#2360;&#2375; &#2344;&#2357;&#2327;&#2336;&#2367;&#2340; NITI &#2310;&#2351;&#2379;&#2327; &#2342;&#2381;&#2357;&#2366;&#2352;&#2366; &#2346;&#2381;&#2352;&#2340;&#2367;&#2360;&#2381;&#2341;&#2366;&#2346;&#2367;&#2340; &#2325;&#2367;&#2351;&#2366; &#2327;&#2351;&#2366; &#2341;&#2366;&#2404; &#2344;&#2368;&#2340;&#2367; &#2310;&#2351;&#2379;&#2327; (&#2344;&#2375;&#2358;&#2344;&#2354; &#2311;&#2306;&#2360;&#2381;&#2335;&#2368;&#2335;&#2381;&#2351;&#2370;&#2358;&#2344; &#2347;&#2377;&#2352; &#2335;&#2381;&#2352;&#2366;&#2306;&#2360;&#2347;&#2377;&#2352;&#2381;&#2350;&#2367;&#2306;&#2327; &#2311;&#2306;&#2337;&#2367;&#2351;&#2366;) &#2344; &#2340;&#2379; &#2360;&#2306;&#2357;&#2376;&#2343;&#2366;&#2344;&#2367;&#2325; &#2344;&#2367;&#2325;&#2366;&#2351; &#2361;&#2376; &#2324;&#2352; &#2344; &#2361;&#2368; &#2357;&#2376;&#2343;&#2366;&#2344;&#2367;&#2325; &#2344;&#2367;&#2325;&#2366;&#2351;&#2404;</span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "18",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">2.</span><span style=\"font-family: Cambria Math;\"> Who was the first female Muslim ruler of India?</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">2.</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2349;&#2366;&#2352;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2361;&#2354;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2361;&#2367;&#2354;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2369;&#2360;&#2381;&#2354;&#2367;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2358;&#2366;&#2360;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2380;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2341;&#2368;</span><span style=\"font-family: Cambria Math;\">? </span></p>\n",
                    options_en: ["<p>Zebunnissa</p>\n", "<p>Razia Sultan</p>\n", 
                                "<p>Chand Bibi</p>\n", "<p>Jahanara</p>\n"],
                    options_hi: ["<p><span style=\"font-family: Cambria Math;\">&#2332;&#2364;&#2375;&#2348;&#2369;&#2344;&#2381;&#2352;&#2367;&#2360;&#2366;</span><span style=\"font-family: Cambria Math;\"> (Zebunnissa) </span></p>\n", "<p><span style=\"font-family: Cambria Math;\">&#2352;&#2332;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2369;&#2354;&#2381;&#2340;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> (Razia Sultana) </span></p>\n",
                                "<p><span style=\"font-family: Cambria Math;\">&#2330;&#2366;&#2306;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2368;&#2348;&#2368;</span><span style=\"font-family: Cambria Math;\"> (Chand Bibi) </span></p>\n", "<p><span style=\"font-family: Cambria Math;\">&#2332;&#2361;&#2366;&#2305;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2310;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\"> (Jahanara) </span></p>\n"],
                    solution_en: "<p>2.<span style=\"font-family: Cambria Math;\">(b)&nbsp;</span><strong>Razia Sultan. </strong><span style=\"font-weight: 400;\">She ascended to the throne of </span><strong>Delhi Sultanate</strong><span style=\"font-weight: 400;\"> </span><strong>(1236 A.D -1240 A.D) </strong><span style=\"font-weight: 400;\">after the death of his father, Iltutmish (Slave dynasty, Rule: 1210-1236).&nbsp; </span><strong>Zeb-un-Nissa:</strong><span style=\"font-weight: 400;\"> Mughal princess</span><strong>,</strong><span style=\"font-weight: 400;\"> Eldest child of Aurangzeb. Also</span><strong> a poet</strong><span style=\"font-weight: 400;\">, who wrote under the pseudonym of \"</span><strong>Makhfi</strong><span style=\"font-weight: 400;\">\". </span><strong>Chand Bibi</strong><span style=\"font-weight: 400;\">: A ruler and military leader of the </span><strong>Deccan region</strong><span style=\"font-weight: 400;\">, known for her zealous defense of her territory during </span><strong>Akbar</strong><span style=\"font-weight: 400;\">\'s invasion. </span><strong>Jahanara Begum:</strong><span style=\"font-weight: 400;\"> Mughal princess, daughter of Emperor Shah Jahan and his wife Mumtaz Mahal.</span></p>\n",
                    solution_hi: "<p>2.<span style=\"font-family: Cambria Math;\">(b)&nbsp;</span><strong>&#2352;&#2332;&#2367;&#2351;&#2366; &#2360;&#2369;&#2354;&#2381;&#2340;&#2366;&#2344; &#2404; </strong><span style=\"font-weight: 400;\">&#2351;&#2361; &#2309;&#2346;&#2344;&#2375; &#2346;&#2367;&#2340;&#2366; &#2311;&#2354;&#2381;&#2340;&#2369;&#2340;&#2350;&#2367;&#2358; &#2325;&#2368; &#2350;&#2371;&#2340;&#2381;&#2351;&#2369; &#2325;&#2375; &#2348;&#2366;&#2342; </span><strong>&#2342;&#2367;&#2354;&#2381;&#2354;&#2368; &#2360;&#2354;&#2381;&#2340;&#2344;&#2340;</strong><span style=\"font-weight: 400;\"> </span><strong>(1236 A.D -1240 A.D)</strong><span style=\"font-weight: 400;\"> &#2325;&#2375; &#2360;&#2367;&#2306;&#2361;&#2366;&#2360;&#2344; &#2346;&#2352; &#2348;&#2376;&#2336;&#2368;&#2404; </span><strong>&#2332;&#2364;&#2375;&#2348;-&#2313;&#2344;-&#2344;&#2367;&#2360;&#2366;</strong><span style=\"font-weight: 400;\"> &#2319;&#2325; </span><strong>&#2350;&#2369;&#2327;&#2364;&#2354; &#2352;&#2366;&#2332;&#2325;&#2369;&#2350;&#2366;&#2352;&#2368;</strong><span style=\"font-weight: 400;\"> &#2324;&#2352; </span><strong>&#2324;&#2352;&#2306;&#2327;&#2332;&#2364;&#2375;&#2348;</strong><span style=\"font-weight: 400;\"> &#2325;&#2368; &#2360;&#2348;&#2360;&#2375; &#2348;&#2337;&#2364;&#2368; &#2360;&#2306;&#2340;&#2366;&#2344; &#2341;&#2368;&#2404;&nbsp; &#2351;&#2361; &#2319;&#2325;</span><strong> &#2325;&#2357;&#2351;&#2367;&#2340;&#2381;&#2352;&#2368; &#2349;&#2368;</strong><span style=\"font-weight: 400;\">, &#2332;&#2367;&#2344;&#2381;&#2361;&#2379;&#2306;&#2344;&#2375; </span><strong>\"&#2350;&#2326;&#2347;&#2368;\" </strong><span style=\"font-weight: 400;\">&#2325;&#2375; &#2331;&#2342;&#2381;&#2350; &#2344;&#2366;&#2350; &#2360;&#2375; &#2354;&#2367;&#2326;&#2366; &#2404;</span><strong> &#2330;&#2366;&#2306;&#2342; &#2348;&#2368;&#2348;&#2368;</strong><span style=\"font-weight: 400;\"> </span><strong>&#2342;&#2325;&#2381;&#2325;&#2344; &#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;</strong><span style=\"font-weight: 400;\"> &#2325;&#2368; &#2319;&#2325; &#2358;&#2366;&#2360;&#2325; &#2324;&#2352; &#2360;&#2376;&#2344;&#2381;&#2351; &#2344;&#2375;&#2340;&#2366; &#2341;&#2368;&#2306;, &#2332;&#2379; </span><strong>&#2309;&#2325;&#2348;&#2352;</strong><span style=\"font-weight: 400;\"> &#2325;&#2375; &#2310;&#2325;&#2381;&#2352;&#2350;&#2339; &#2325;&#2375; &#2342;&#2380;&#2352;&#2366;&#2344; &#2309;&#2346;&#2344;&#2375; &#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352; &#2325;&#2368; &#2332;&#2379;&#2358;&#2368;&#2354;&#2368; &#2352;&#2325;&#2381;&#2359;&#2366; &#2325;&#2375; &#2354;&#2367;&#2319; &#2332;&#2366;&#2344;&#2368; &#2332;&#2366;&#2340;&#2368; &#2341;&#2368;&#2306;&#2404; </span><strong>&#2332;&#2361;&#2366;&#2305;&#2310;&#2352;&#2366; &#2348;&#2375;&#2327;&#2350;: </strong><span style=\"font-weight: 400;\">&#2350;&#2369;&#2327;&#2354; &#2352;&#2366;&#2332;&#2325;&#2369;&#2350;&#2366;&#2352;&#2368;, &#2348;&#2366;&#2342;&#2358;&#2366;&#2361; &#2358;&#2366;&#2361;&#2332;&#2361;&#2366;&#2305; &#2324;&#2352; &#2313;&#2344;&#2325;&#2368; &#2346;&#2340;&#2381;&#2344;&#2368; &#2350;&#2369;&#2350;&#2340;&#2366;&#2332; &#2350;&#2361;&#2354; &#2325;&#2368; &#2348;&#2375;&#2335;&#2368;&#2404;</span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "18",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">3.</span><span style=\"font-family: Cambria Math;\">Who received a Nobel Peace Prize for dismantling apartheid in South Africa?</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">3. </span><span style=\"font-family: Cambria Math;\">&#2342;&#2325;&#2381;&#2359;&#2367;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2309;&#2347;&#2381;&#2352;&#2368;&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2352;&#2306;&#2327;&#2349;&#2375;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2326;&#2340;&#2381;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2352;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2344;&#2379;&#2348;&#2375;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2358;&#2366;&#2306;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2369;&#2352;&#2360;&#2381;&#2325;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2367;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2367;&#2354;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2341;&#2366;</span><span style=\"font-family: Cambria Math;\">? </span></p>\n",
                    options_en: ["<p>Nelson Mandela</p>\n", "<p>Rabindranath Tagore</p>\n", 
                                "<p>Mahatma Gandhi</p>\n", "<p>Albert John Lutuli</p>\n"],
                    options_hi: ["<p><span style=\"font-family: Cambria Math;\">&#2344;&#2375;&#2354;&#2381;&#2360;&#2344; &#2350;&#2306;&#2337;&#2375;&#2354;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><span style=\"font-family: Cambria Math;\">&#2352;&#2348;&#2368;&#2344;&#2381;&#2342;&#2381;&#2352;&#2344;&#2366;&#2341;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2335;&#2376;&#2327;&#2379;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                                "<p><span style=\"font-family: Cambria Math;\">&#2350;&#2361;&#2366;&#2340;&#2381;&#2350;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2327;&#2366;&#2306;&#2343;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><span style=\"font-family: Cambria Math;\">&#2309;&#2354;&#2381;</span><span style=\"font-family: Cambria Math;\">&#2348;&#2352;&#2381;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2332;&#2377;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2354;&#2369;&#2335;&#2369;&#2354;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n"],
                    solution_en: "<p>3.(a) <strong>Nelson Mandela </strong><span style=\"font-weight: 400;\">and </span><strong>Frederik Willem de Klerk</strong><span style=\"font-weight: 400;\"> received the Nobel Peace Prize in 1993. </span><strong>Mahatma Gandhi</strong><span style=\"font-weight: 400;\"> (Symbol of non violence): Nominated for Nobel Peace Prize in </span><strong>1937</strong><span style=\"font-weight: 400;\">, </span><strong>1938</strong><span style=\"font-weight: 400;\">, </span><strong>1939</strong><span style=\"font-weight: 400;\">, </span><strong>1947</strong><span style=\"font-weight: 400;\"> and </span><strong>1948 </strong><span style=\"font-weight: 400;\">but never awarded the prize. </span><strong>Rabindranath Tagore</strong><span style=\"font-weight: 400;\">: First Indian and Asian to win a Nobel Prize awarded in 1913 for literature </span><strong>(Gitanjali). </strong><strong>Albert John Lutuli</strong><span style=\"font-weight: 400;\">: Awarded the </span><strong>1960</strong><span style=\"font-weight: 400;\"> </span><strong>Nobel Peace Prize</strong><span style=\"font-weight: 400;\"> for his role in leading the </span><strong>non-violent anti-apartheid movement</strong><span style=\"font-weight: 400;\">.</span></p>\n",
                    solution_hi: "<p>3.(a)<strong> &nbsp;</strong><strong>&#2344;&#2375;&#2354;&#2381;&#2360;&#2344; &#2350;&#2306;&#2337;&#2375;&#2354;&#2366;</strong><span style=\"font-weight: 400;\"> &#2324;&#2352; </span><strong>&#2347;&#2381;&#2352;&#2375;&#2337;&#2352;&#2367;&#2325; &#2357;&#2367;&#2354;&#2375;&#2350; &#2337;&#2368; &#2325;&#2381;&#2354;&#2352;&#2381;&#2325;</strong><span style=\"font-weight: 400;\"> &#2325;&#2379; 1993 &#2350;&#2375;&#2306; &#2344;&#2379;&#2348;&#2375;&#2354; &#2358;&#2366;&#2306;&#2340;&#2367; &#2346;&#2369;&#2352;&#2360;&#2381;&#2325;&#2366;&#2352; &#2350;&#2367;&#2354;&#2366;&#2404; </span><strong>&#2350;&#2361;&#2366;&#2340;&#2381;&#2350;&#2366; &#2327;&#2366;&#2306;&#2343;&#2368;</strong><span style=\"font-weight: 400;\"> (&#2309;&#2361;&#2367;&#2306;&#2360;&#2366; &#2325;&#2375; &#2346;&#2381;&#2352;&#2340;&#2368;&#2325;): 1937, 1938, 1939, 1947 &#2324;&#2352; 1948 &#2350;&#2375;&#2306; &#2344;&#2379;&#2348;&#2375;&#2354; &#2358;&#2366;&#2306;&#2340;&#2367; &#2346;&#2369;&#2352;&#2360;&#2381;&#2325;&#2366;&#2352; &#2325;&#2375; &#2354;&#2367;&#2319; &#2344;&#2366;&#2350;&#2366;&#2306;&#2325;&#2367;&#2340; &#2354;&#2375;&#2325;&#2367;&#2344; &#2325;&#2349;&#2368; &#2349;&#2368; &#2346;&#2369;&#2352;&#2360;&#2381;&#2325;&#2366;&#2352; &#2360;&#2375; &#2360;&#2350;&#2381;&#2350;&#2366;&#2344;&#2367;&#2340; &#2344;&#2361;&#2368;&#2306; &#2325;&#2367;&#2351;&#2366; &#2327;&#2351;&#2366;&#2404;</span><strong> &#2352;&#2357;&#2368;&#2306;&#2342;&#2381;&#2352;&#2344;&#2366;&#2341; &#2335;&#2376;&#2327;&#2379;&#2352;</strong><span style=\"font-weight: 400;\">: &#2360;&#2366;&#2361;&#2367;&#2340;&#2381;&#2351; (&#2327;&#2368;&#2340;&#2366;&#2306;&#2332;&#2354;&#2367;) &#2325;&#2375; &#2354;&#2367;&#2319; 1913 &#2350;&#2375;&#2306; &#2344;&#2379;&#2348;&#2375;&#2354; &#2346;&#2369;&#2352;&#2360;&#2381;&#2325;&#2366;&#2352; &#2332;&#2368;&#2340;&#2344;&#2375; &#2357;&#2366;&#2354;&#2375; &#2346;&#2361;&#2354;&#2375; &#2349;&#2366;&#2352;&#2340;&#2368;&#2351; &#2324;&#2352; &#2319;&#2358;&#2367;&#2351;&#2366;&#2312;&#2404; </span><strong>&#2309;&#2354;&#2381;&#2348;&#2352;&#2381;&#2335; &#2332;&#2377;&#2344; &#2354;&#2369;&#2335;&#2369;&#2354;&#2368;</strong><span style=\"font-weight: 400;\">: &#2309;&#2361;&#2367;&#2306;&#2360;&#2325; &#2352;&#2306;&#2327;&#2349;&#2375;&#2342; &#2357;&#2367;&#2352;&#2379;&#2343;&#2368; &#2310;&#2306;&#2342;&#2379;&#2354;&#2344; &#2325;&#2366; &#2344;&#2375;&#2340;&#2371;&#2340;&#2381;&#2357; &#2325;&#2352;&#2344;&#2375; &#2350;&#2375;&#2306; &#2313;&#2344;&#2325;&#2368; &#2349;&#2370;&#2350;&#2367;&#2325;&#2366; &#2325;&#2375; &#2354;&#2367;&#2319; 1960 &#2325;&#2375; &#2344;&#2379;&#2348;&#2375;&#2354; &#2358;&#2366;&#2306;&#2340;&#2367; &#2346;&#2369;&#2352;&#2360;&#2381;&#2325;&#2366;&#2352; &#2360;&#2375; &#2360;&#2350;&#2381;&#2350;&#2366;&#2344;&#2367;&#2340;&#2404;</span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "18",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">4. </span><span style=\"font-family: Cambria Math;\">As of 2022, India is the______ largest economy in the world (as measured by nominal GDP).</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">4. </span><span style=\"font-family: Cambria Math;\">2022 </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2381;&#2341;&#2367;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2309;&#2344;&#2369;&#2360;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2349;&#2366;&#2352;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2369;&#2344;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> ______________ </span><span style=\"font-family: Cambria Math;\">&#2360;&#2348;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2337;&#2364;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2309;&#2352;&#2381;&#2341;&#2357;&#2381;&#2351;&#2357;&#2360;&#2381;&#2341;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> (</span><span style=\"font-family: Cambria Math;\">&#2344;&#2366;&#2350;&#2350;&#2366;&#2340;&#2381;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2332;&#2368;&#2337;&#2368;&#2346;&#2368;</span><span style=\"font-family: Cambria Math;\"> (nominal GDP) </span><span style=\"font-family: Cambria Math;\">&#2342;&#2381;&#2357;&#2366;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2366;&#2346;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2327;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\">)</span><span style=\"font-family: Cambria Math;\">&#2404;</span></p>\n",
                    options_en: ["<p>Third</p>\n", "<p>Second</p>\n", 
                                "<p>Fifth</p>\n", "<p>Fourth</p>\n"],
                    options_hi: ["<p><span style=\"font-family: Cambria Math;\">&#2340;&#2368;&#2360;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><span style=\"font-family: Cambria Math;\">&#2342;&#2370;&#2360;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                                "<p><span style=\"font-family: Cambria Math;\">&#2346;&#2366;&#2306;&#2330;&#2357;&#2368;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><span style=\"font-family: Cambria Math;\">&#2330;&#2380;&#2341;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n"],
                    solution_en: "<p>4.(c)&nbsp; <strong>Fifth.</strong><span style=\"font-weight: 400;\"> </span><strong>Gross domestic product</strong><span style=\"font-weight: 400;\"> (GDP) is the total monetary or market value of all the finished goods and services produced within a country&rsquo;s borders in a specific time period. </span><strong>World&rsquo;s largest economy </strong><span style=\"font-weight: 400;\">- United States &gt; China &gt; Japan &gt; Germany &gt; </span><strong>India</strong><span style=\"font-weight: 400;\">.</span></p>\n",
                    solution_hi: "<p>4.(c)&nbsp; <strong>&#2346;&#2366;&#2306;&#2330;&#2357;&#2368;&#2306;</strong><span style=\"font-weight: 400;\"> </span><strong>&#2404; &#2360;&#2325;&#2354; &#2328;&#2352;&#2375;&#2354;&#2370; &#2313;&#2340;&#2381;&#2346;&#2366;&#2342; </strong><span style=\"font-weight: 400;\">(GDP) &#2319;&#2325; &#2357;&#2367;&#2358;&#2367;&#2359;&#2381;&#2335; &#2360;&#2350;&#2351; &#2309;&#2357;&#2343;&#2367; &#2350;&#2375;&#2306; &#2342;&#2375;&#2358; &#2325;&#2368; &#2360;&#2368;&#2350;&#2366;&#2323;&#2306; &#2325;&#2375; &#2349;&#2368;&#2340;&#2352; &#2313;&#2340;&#2381;&#2346;&#2366;&#2342;&#2367;&#2340; &#2360;&#2349;&#2368; &#2340;&#2376;&#2351;&#2366;&#2352; &#2357;&#2360;&#2381;&#2340;&#2369;&#2323;&#2306; &#2324;&#2352; &#2360;&#2375;&#2357;&#2366;&#2323;&#2306; &#2325;&#2366; &#2325;&#2369;&#2354; &#2350;&#2380;&#2342;&#2381;&#2352;&#2367;&#2325; &#2351;&#2366; &#2348;&#2366;&#2332;&#2366;&#2352; &#2350;&#2370;&#2354;&#2381;&#2351; &#2361;&#2376;&#2404; </span><strong>&#2357;&#2367;&#2358;&#2381;&#2357; &#2325;&#2368; &#2360;&#2348;&#2360;&#2375; &#2348;&#2337;&#2364;&#2368; &#2309;&#2352;&#2381;&#2341;&#2357;&#2381;&#2351;&#2357;&#2360;&#2381;&#2341;&#2366;</strong><span style=\"font-weight: 400;\"> - &#2360;&#2306;&#2351;&#2369;&#2325;&#2381;&#2340; &#2352;&#2366;&#2332;&#2381;&#2351; &#2309;&#2350;&#2375;&#2352;&#2367;&#2325;&#2366; &gt; &#2330;&#2368;&#2344; &gt; &#2332;&#2366;&#2346;&#2366;&#2344; &gt; &#2332;&#2352;&#2381;&#2350;&#2344;&#2368; &gt; </span><strong>&#2349;&#2366;&#2352;&#2340;</strong><span style=\"font-weight: 400;\">&#2404;</span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "18",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">5.</span><span style=\"font-family: Cambria Math;\"> Raman wants to use a rear-view mirror in his vehicle. Which type of mirror should he pick for it?</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">5. </span><span style=\"font-family: Cambria Math;\">&#2352;&#2350;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2309;&#2346;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2366;&#2361;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2358;&#2381;&#2330;</span><span style=\"font-family: Cambria Math;\">-</span><span style=\"font-family: Cambria Math;\">&#2361;&#2371;&#2358;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2352;&#2381;&#2346;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2381;&#2352;&#2351;&#2379;&#2327;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2352;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2330;&#2366;&#2361;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2311;&#2360;&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2313;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2367;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2381;&#2352;&#2325;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2352;&#2381;&#2346;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2330;&#2369;&#2344;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2330;&#2366;&#2361;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>Convex Mirrors</p>\n", "<p>Plane Mirrors</p>\n", 
                                "<p>Cylinderical Mirrors</p>\n", "<p>Concave Mirrors</p>\n"],
                    options_hi: ["<p><span style=\"font-family: Cambria Math;\">&#2313;&#2340;&#2381;&#2340;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2352;&#2381;&#2346;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><span style=\"font-family: Cambria Math;\">&#2360;&#2350;&#2340;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2352;&#2381;&#2346;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                                "<p><span style=\"font-family: Cambria Math;\">&#2348;&#2375;&#2354;&#2344;&#2366;&#2325;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2352;&#2381;&#2346;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><span style=\"font-family: Cambria Math;\">&#2309;&#2357;&#2340;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2352;&#2381;&#2346;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n"],
                    solution_en: "<p>5.(a) <strong>Convex Mirrors. </strong><span style=\"font-weight: 400;\">This is because they give an erect, virtual, full size diminished image of distant objects with a wider field of view. Uses - mirrors of motor vehicles, sunglasses, street lights. </span><strong>Uses of Concave Mirrors</strong><span style=\"font-weight: 400;\"> - Vehicle headlights, Shaving mirrors, Solar furnaces, Searchlights, Torches. </span><strong>Uses of Plane Mirrors</strong><span style=\"font-weight: 400;\"> - Torch Lights, Looking Glasses, Solar Cooker. </span></p>\n",
                    solution_hi: "<p>5.(a)&nbsp;<strong>&#2313;&#2340;&#2381;&#2340;&#2354; &#2342;&#2352;&#2381;&#2346;&#2339;&#2404; </strong><span style=\"font-weight: 400;\">&#2311;&#2360;&#2325;&#2366; &#2325;&#2366;&#2352;&#2339; &#2351;&#2361; &#2361;&#2376; &#2325;&#2367; &#2357;&#2375; &#2342;&#2370;&#2352; &#2325;&#2368; &#2357;&#2360;&#2381;&#2340;&#2369;&#2323;&#2306; &#2325;&#2379; &#2342;&#2375;&#2326;&#2344;&#2375; &#2325;&#2375; &#2357;&#2381;&#2351;&#2366;&#2346;&#2325; &#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352; &#2325;&#2375; &#2360;&#2366;&#2341; &#2319;&#2325; &#2360;&#2368;&#2343;&#2366;, &#2310;&#2349;&#2366;&#2360;&#2368;, &#2346;&#2370;&#2352;&#2381;&#2339; &#2310;&#2325;&#2366;&#2352; &#2325;&#2366; &#2331;&#2379;&#2335;&#2366; &#2346;&#2381;&#2352;&#2340;&#2367;&#2348;&#2367;&#2306;&#2348; &#2342;&#2375;&#2340;&#2375; &#2361;&#2376;&#2306;&#2404; </span><strong>&#2313;&#2346;&#2351;&#2379;&#2327; </strong><span style=\"font-weight: 400;\">- &#2350;&#2379;&#2335;&#2352; &#2357;&#2366;&#2361;&#2344;&#2379;&#2306; &#2325;&#2375; &#2342;&#2352;&#2381;&#2346;&#2339;, &#2343;&#2370;&#2346; &#2325;&#2366; &#2330;&#2358;&#2381;&#2350;&#2366;, &#2360;&#2381;&#2335;&#2381;&#2352;&#2368;&#2335; &#2354;&#2366;&#2311;&#2335;&#2404; </span><strong>&#2309;&#2357;&#2340;&#2354; &#2342;&#2352;&#2381;&#2346;&#2339;&#2379;&#2306; &#2325;&#2375; &#2313;&#2346;&#2351;&#2379;&#2327;</strong><span style=\"font-weight: 400;\"> - &#2357;&#2366;&#2361;&#2344; &#2325;&#2368; &#2361;&#2375;&#2337;&#2354;&#2366;&#2311;&#2335;&#2381;&#2360;, &#2358;&#2375;&#2357;&#2367;&#2306;&#2327; &#2342;&#2352;&#2381;&#2346;&#2339;, &#2360;&#2380;&#2352; &#2349;&#2335;&#2381;&#2335;&#2367;&#2351;&#2366;&#2305;, &#2360;&#2352;&#2381;&#2330;&#2354;&#2366;&#2311;&#2335;&#2381;&#2360;, &#2335;&#2377;&#2352;&#2381;&#2330;&#2404; </span><strong>&#2360;&#2350;&#2340;&#2354; &#2342;&#2352;&#2381;&#2346;&#2339;&#2379;&#2306; &#2325;&#2375; &#2313;&#2346;&#2351;&#2379;&#2327;</strong><span style=\"font-weight: 400;\"> - &#2335;&#2377;&#2352;&#2381;&#2330; &#2325;&#2368; &#2352;&#2379;&#2358;&#2344;&#2368;, &#2342;&#2375;&#2326;&#2344;&#2375; &#2357;&#2366;&#2354;&#2366; &#2330;&#2358;&#2381;&#2350;&#2366;, &#2360;&#2380;&#2352; &#2325;&#2369;&#2325;&#2352;&#2404;</span><span style=\"font-weight: 400;\">&nbsp;</span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "18",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">6.</span><span style=\"font-family: Cambria Math;\"> When was the Direct Tax Code Bill introduced in the Parliament of India?</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">6.</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2349;&#2366;&#2352;&#2340;&#2368;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2306;&#2360;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2381;&#2352;&#2340;&#2381;&#2351;&#2325;&#2381;&#2359;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2306;&#2361;&#2367;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2367;&#2343;&#2375;&#2351;&#2325;</span><span style=\"font-family: Cambria Math;\"> (</span><span style=\"font-family: Cambria Math;\">&#2337;&#2366;&#2351;&#2352;&#2375;&#2325;&#2381;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2335;&#2376;&#2325;&#2381;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2379;&#2337;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2367;&#2354;</span><span style=\"font-family: Cambria Math;\">) </span><span style=\"font-family: Cambria Math;\">&#2325;&#2348;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2375;&#2358;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2327;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2341;&#2366;</span><span style=\"font-family: Cambria Math;\">? </span></p>\n",
                    options_en: ["<p>2010</p>\n", "<p>2005</p>\n", 
                                "<p>2020</p>\n", "<p>2015</p>\n"],
                    options_hi: ["<p>2010</p>\n", "<p>2005</p>\n",
                                "<p>2020</p>\n", "<p>2015</p>\n"],
                    solution_en: "<p>6.(a)<strong> 2010. Direct tax<span style=\"font-weight: 400;\">: It is directly paid to the authority imposing the tax. Example -Income tax, real property tax, personal property tax, and taxes on assets. </span>Indirect tax: <span style=\"font-weight: 400;\">It is the tax levied on the consumption of goods and services. Example - Value Added Tax (VAT) or Goods and Services Tax (GST) , Excise duty, Customs duty, Sales tax.</span></strong></p>\n",
                    solution_hi: "<p>6.(a)<strong>&nbsp;</strong><strong>2010 </strong><span style=\"font-weight: 400;\">&#2404; </span><strong>&#2346;&#2381;&#2352;&#2340;&#2381;&#2351;&#2325;&#2381;&#2359; &#2325;&#2352;</strong><span style=\"font-weight: 400;\">: &#2351;&#2361; &#2360;&#2368;&#2343;&#2375; &#2325;&#2352; &#2354;&#2327;&#2366;&#2344;&#2375; &#2357;&#2366;&#2354;&#2375; &#2346;&#2381;&#2352;&#2366;&#2343;&#2367;&#2325;&#2352;&#2339; &#2325;&#2379; &#2349;&#2369;&#2327;&#2340;&#2366;&#2344; &#2325;&#2367;&#2351;&#2366; &#2332;&#2366;&#2340;&#2366; &#2361;&#2376;&#2404; &#2313;&#2342;&#2366;&#2361;&#2352;&#2339; - &#2310;&#2351;&#2325;&#2352; ,&#2357;&#2366;&#2360;&#2381;&#2340;&#2357;&#2367;&#2325; &#2360;&#2306;&#2346;&#2340;&#2381;&#2340;&#2367; &#2325;&#2352;,&#2357;&#2381;&#2351;&#2325;&#2381;&#2340;&#2367;&#2327;&#2340; &#2360;&#2306;&#2346;&#2340;&#2381;&#2340;&#2367; &#2325;&#2352; &#2324;&#2352; &#2360;&#2306;&#2346;&#2340;&#2381;&#2340;&#2367; &#2346;&#2352; &#2325;&#2352;&#2404; </span><strong>&#2309;&#2346;&#2381;&#2352;&#2340;&#2381;&#2351;&#2325;&#2381;&#2359; &#2325;&#2352;</strong><span style=\"font-weight: 400;\">: &#2351;&#2361; &#2357;&#2360;&#2381;&#2340;&#2369;&#2323;&#2306; &#2324;&#2352; &#2360;&#2375;&#2357;&#2366;&#2323;&#2306; &#2325;&#2368; &#2326;&#2346;&#2340; &#2346;&#2352; &#2354;&#2327;&#2366;&#2351;&#2366; &#2332;&#2366;&#2344;&#2375; &#2357;&#2366;&#2354;&#2366; &#2325;&#2352; &#2361;&#2376;&#2404; &#2313;&#2342;&#2366;&#2361;&#2352;&#2339; - &#2350;&#2370;&#2354;&#2381;&#2351; &#2357;&#2352;&#2381;&#2343;&#2367;&#2340; &#2325;&#2352; (</span><span style=\"font-weight: 400;\">VAT</span><span style=\"font-weight: 400;\">) &#2351;&#2366; &#2357;&#2360;&#2381;&#2340;&#2369; &#2319;&#2357;&#2306; &#2360;&#2375;&#2357;&#2366; &#2325;&#2352; (</span><span style=\"font-weight: 400;\">GST</span><span style=\"font-weight: 400;\">), &#2313;&#2340;&#2381;&#2346;&#2366;&#2342; &#2358;&#2369;&#2354;&#2381;&#2325;, &#2360;&#2368;&#2350;&#2366; &#2358;&#2369;&#2354;&#2381;&#2325;, &#2348;&#2367;&#2325;&#2381;&#2352;&#2368; &#2325;&#2352;&#2404;</span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "18",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">7.</span><span style=\"font-family: Cambria Math;\"> Neeti wants to use an input devi</span><span style=\"font-family: Cambria Math;\">ce that works by sensing the user\'s finger movement and downward pressure. Which among the following should she use?</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">7. </span><span style=\"font-family: Cambria Math;\">&#2344;&#2368;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2311;&#2344;&#2346;&#2369;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2337;&#2367;&#2357;&#2366;&#2311;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2313;&#2346;&#2351;&#2379;&#2327;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2352;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2330;&#2366;&#2361;&#2340;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2332;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2313;&#2346;&#2351;&#2379;&#2327;&#2325;&#2352;&#2381;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2313;&#2306;&#2327;&#2354;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2330;&#2366;</span><span style=\"font-family: Cambria Math;\">&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2344;&#2368;&#2330;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2337;&#2364;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2366;&#2354;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2348;&#2366;&#2357;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2349;&#2366;&#2306;&#2346;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2352;&#2340;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2313;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2367;&#2360;&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2313;&#2346;&#2351;&#2379;&#2327;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2352;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2330;&#2366;&#2361;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\">? </span></p>\n",
                    options_en: ["<p>Scanner</p>\n", "<p>Touchpad</p>\n", 
                                "<p>Light pen</p>\n", "<p>Keyboard</p>\n"],
                    options_hi: ["<p><span style=\"font-family: Cambria Math;\">&#2360;&#2381;&#2325;&#2376;&#2344;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><span style=\"font-family: Cambria Math;\">&#2335;&#2330;&#2346;&#2376;&#2337;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                                "<p><span style=\"font-family: Cambria Math;\">&#2354;&#2366;&#2311;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2375;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><span style=\"font-family: Cambria Math;\">&#2325;&#2368;&#2348;&#2379;&#2352;&#2381;&#2337;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n"],
                    solution_en: "<p>7.(b) <strong>Touchpad. Input devices -</strong><span style=\"font-weight: 400;\"> Keyboard, Mouse, JoyStick, Light pen, Track Ball, Scanner</span><strong>, </strong><span style=\"font-weight: 400;\">Graphic Tablet, Microphone.&nbsp; </span><strong>Output devices - </strong><span style=\"font-weight: 400;\">Monitor, Printer, Speaker, Headphone, projector, GPS devices.</span></p>\n",
                    solution_hi: "<p>7.(b)<strong>&nbsp;</strong><strong>&#2335;&#2330;&#2346;&#2376;&#2337;&#2404; &#2311;&#2344;&#2346;&#2369;&#2335; &#2337;&#2367;&#2357;&#2366;&#2311;&#2360;</strong><span style=\"font-weight: 400;\"> -</span><strong> </strong><span style=\"font-weight: 400;\">&#2325;&#2368;&#2348;&#2379;&#2352;&#2381;&#2337;, &#2350;&#2366;&#2313;&#2360;, &#2332;&#2377;&#2351;&#2360;&#2381;&#2335;&#2367;&#2325;, </span><strong>&#2354;&#2366;&#2311;&#2335; &#2346;&#2375;&#2344;</strong><span style=\"font-weight: 400;\">, &#2335;&#2381;&#2352;&#2376;&#2325; &#2348;&#2377;&#2354;, </span><strong>&#2360;&#2381;&#2325;&#2376;&#2344;&#2352;</strong><span style=\"font-weight: 400;\">, &#2327;&#2381;&#2352;&#2366;&#2347;&#2367;&#2325; &#2335;&#2376;&#2348;&#2354;&#2375;&#2335;, &#2350;&#2366;&#2311;&#2325;&#2381;&#2352;&#2379;&#2347;&#2379;&#2344;&#2404; </span><strong>&#2310;&#2313;&#2335;&#2346;&#2369;&#2335; &#2337;&#2367;&#2357;&#2366;&#2311;&#2360; </strong><span style=\"font-weight: 400;\">- &#2350;&#2377;&#2344;&#2367;&#2335;&#2352;, </span><strong>&#2346;&#2381;&#2352;&#2367;&#2306;&#2335;&#2352;</strong><span style=\"font-weight: 400;\">, &#2360;&#2381;&#2346;&#2368;&#2325;&#2352;, &#2361;&#2375;&#2337;&#2347;&#2379;&#2344;, &#2346;&#2381;&#2352;&#2379;&#2332;&#2375;&#2325;&#2381;&#2335;&#2352;, </span><strong>GPS &#2337;&#2367;&#2357;&#2366;&#2311;&#2360;</strong><span style=\"font-weight: 400;\"> &#2404;</span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "18",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">8.</span><span style=\"font-family: Cambria Math;\"> Which country hosted the FIFA U-17 Women\'s World Cup 2022?</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">8. </span><span style=\"font-family: Cambria Math;\">&#2325;&#2367;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2375;&#2358;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2347;&#2368;&#2347;&#2366;</span><span style=\"font-family: Cambria Math;\"> (FIFA) </span><span style=\"font-family: Cambria Math;\">&#2309;&#2306;&#2337;&#2352;</span><span style=\"font-family: Cambria Math;\"> - 17 </span><span style=\"font-family: Cambria Math;\">&#2350;&#2361;&#2367;&#2354;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2367;&#2358;&#2381;&#2357;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2346;</span><span style=\"font-family: Cambria Math;\"> 2022 </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2332;&#2348;&#2366;&#2344;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2341;&#2368;</span><span style=\"font-family: Cambria Math;\">? </span></p>\n",
                    options_en: ["<p>Egypt</p>\n", "<p>China</p>\n", 
                                "<p>France</p>\n", "<p>India</p>\n"],
                    options_hi: ["<p><span style=\"font-family: Cambria Math;\">&#2350;&#2367;&#2358;&#2381;&#2352;</span><span style=\"font-family: Cambria Math;\"> (Egypt) </span></p>\n", "<p><span style=\"font-family: Cambria Math;\">&#2330;&#2368;&#2344;</span><span style=\"font-family: Cambria Math;\"> (China) </span></p>\n",
                                "<p><span style=\"font-family: Cambria Math;\">&#2347;&#2381;&#2352;&#2366;&#2306;&#2360;</span><span style=\"font-family: Cambria Math;\"> (France) </span></p>\n", "<p><span style=\"font-family: Cambria Math;\">&#2349;&#2366;&#2352;&#2340;</span><span style=\"font-family: Cambria Math;\"> (India) </span></p>\n"],
                    solution_en: "<p>8.(d) <strong>India. </strong><span style=\"font-weight: 400;\">FIFA U-17 Women\'s World Cup India 2022 Competition in 3 Venues - Kalinga Stadium in Bhubaneswar (Odisha), Pandit Jawaharlal Nehru Stadium in Margao (Goa) and DY Patil Stadium in Navi Mumbai (Maharashtra). The final match</span><strong> (Navi Mumbai)</strong><span style=\"font-weight: 400;\"> was played between Spain (Winner) and Colombia</span><strong>.&nbsp;</strong></p>\n",
                    solution_hi: "<p>8.(d)&nbsp; <strong>&#2349;&#2366;&#2352;&#2340;&#2404;</strong><span style=\"font-weight: 400;\"> FIFA U-17 &#2350;&#2361;&#2367;&#2354;&#2366; &#2357;&#2367;&#2358;&#2381;&#2357; &#2325;&#2346; &#2349;&#2366;&#2352;&#2340; 2022 &#2346;&#2381;&#2352;&#2340;&#2367;&#2351;&#2379;&#2327;&#2367;&#2340;&#2366; 3 &#2360;&#2381;&#2341;&#2366;&#2344;&#2379;&#2306; &#2350;&#2375;&#2306; - &#2349;&#2369;&#2357;&#2344;&#2375;&#2358;&#2381;&#2357;&#2352; (&#2323;&#2337;&#2367;&#2358;&#2366;) &#2350;&#2375;&#2306; &#2325;&#2354;&#2367;&#2306;&#2327;&#2366; &#2360;&#2381;&#2335;&#2375;&#2337;&#2367;&#2351;&#2350;, &#2350;&#2337;&#2327;&#2366;&#2306;&#2357; (&#2327;&#2379;&#2357;&#2366;) &#2350;&#2375;&#2306; &#2346;&#2306;&#2337;&#2367;&#2340; &#2332;&#2357;&#2366;&#2361;&#2352;&#2354;&#2366;&#2354; &#2344;&#2375;&#2361;&#2352;&#2370; &#2360;&#2381;&#2335;&#2375;&#2337;&#2367;&#2351;&#2350; &#2324;&#2352; </span><strong>&#2344;&#2357;&#2368; &#2350;&#2369;&#2306;&#2348;&#2312;</strong><span style=\"font-weight: 400;\"> (&#2350;&#2361;&#2366;&#2352;&#2366;&#2359;&#2381;&#2335;&#2381;&#2352;) &#2350;&#2375;&#2306; DY &#2346;&#2366;&#2335;&#2367;&#2354; &#2360;&#2381;&#2335;&#2375;&#2337;&#2367;&#2351;&#2350;&#2404; &#2347;&#2366;&#2311;&#2344;&#2354; &#2350;&#2376;&#2330; (&#2344;&#2357;&#2368; &#2350;&#2369;&#2306;&#2348;&#2312;) &#2360;&#2381;&#2346;&#2375;&#2344; (&#2357;&#2367;&#2332;&#2375;&#2340;&#2366;) &#2324;&#2352; &#2325;&#2379;&#2354;&#2306;&#2348;&#2367;&#2351;&#2366; &#2325;&#2375; &#2348;&#2368;&#2330; &#2326;&#2375;&#2354;&#2366; &#2327;&#2351;&#2366; &#2341;&#2366;&#2404;</span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "18",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">9.</span><span style=\"font-family: Cambria Math;\">Which of the following is NOT an application software?</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">9. </span><span style=\"font-family: Cambria Math;\">&#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2380;&#2344;</span><span style=\"font-family: Cambria Math;\"> - </span><span style=\"font-family: Cambria Math;\">&#2360;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2319;&#2346;&#2381;&#2354;&#2367;&#2325;&#2375;&#2358;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2377;&#2347;&#2381;&#2335;&#2357;&#2375;&#2351;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2344;&#2361;&#2368;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">? </span></p>\n",
                    options_en: ["<p>Operating systems</p>\n", "<p>Graphics software</p>\n", 
                                "<p>Spreadsheet software</p>\n", "<p>Word-processing software</p>\n"],
                    options_hi: ["<p><span style=\"font-family: Cambria Math;\">&#2321;&#2346;&#2352;&#2375;&#2335;&#2367;&#2306;&#2327;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2367;&#2360;&#2381;&#2335;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><span style=\"font-family: Cambria Math;\">&#2327;&#2381;&#2352;&#2366;&#2347;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2377;&#2347;&#2381;&#2335;&#2357;&#2375;&#2351;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                                "<p><span style=\"font-family: Cambria Math;\">&#2360;&#2381;&#2346;&#2381;&#2352;&#2375;&#2337;&#2358;&#2368;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2377;&#2347;&#2381;&#2335;&#2357;&#2375;&#2351;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><span style=\"font-family: Cambria Math;\">&#2357;&#2352;&#2381;&#2337;</span><span style=\"font-family: Cambria Math;\"> - </span><span style=\"font-family: Cambria Math;\">&#2346;&#2381;&#2352;&#2379;&#2360;&#2375;&#2360;&#2367;&#2306;&#2327;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2377;&#2347;&#2381;&#2335;&#2357;&#2375;&#2351;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n"],
                    solution_en: "<p>9.(a) <strong>Operating systems.</strong><span style=\"font-weight: 400;\"> It acts as the interface between the user and the system hardware. Example - Windows, Linux, NOS, DOS, Unix. </span><strong>Application Software</strong><span style=\"font-weight: 400;\"> is a type of software that is created to do a certain set of tasks. It is a form of software that runs or executes on the user\'s request. Example - Graphics software, Spreadsheet software, Word-processing software.</span></p>\n",
                    solution_hi: "<p>9.(a)&nbsp;<strong>&#2321;&#2346;&#2352;&#2375;&#2335;&#2367;&#2306;&#2327; &#2360;&#2367;&#2360;&#2381;&#2335;&#2350;&#2404; </strong><span style=\"font-weight: 400;\">&#2351;&#2361; &#2313;&#2346;&#2351;&#2379;&#2327;&#2325;&#2352;&#2381;&#2340;&#2366; &#2324;&#2352; &#2360;&#2367;&#2360;&#2381;&#2335;&#2350; &#2361;&#2366;&#2352;&#2381;&#2337;&#2357;&#2375;&#2351;&#2352; &#2325;&#2375; &#2348;&#2368;&#2330; &#2311;&#2306;&#2335;&#2352;&#2347;&#2375;&#2360; &#2325;&#2375; &#2352;&#2370;&#2346; &#2350;&#2375;&#2306; &#2325;&#2366;&#2352;&#2381;&#2351; &#2325;&#2352;&#2340;&#2366; &#2361;&#2376;&#2404;</span><strong> &#2313;&#2342;&#2366;&#2361;&#2352;&#2339; - Windows, Linux, NOS, DOS, Unix&#2404; &#2319;&#2346;&#2381;&#2354;&#2368;&#2325;&#2375;&#2358;&#2344; &#2360;&#2377;&#2347;&#2381;&#2335;&#2357;&#2375;&#2351;&#2352; </strong><span style=\"font-weight: 400;\">&#2319;&#2325; &#2346;&#2381;&#2352;&#2325;&#2366;&#2352; &#2325;&#2366; &#2320;&#2360;&#2366; &#2360;&#2377;&#2347;&#2381;&#2335;&#2357;&#2375;&#2351;&#2352; &#2361;&#2376; &#2332;&#2379; &#2319;&#2325; &#2344;&#2367;&#2358;&#2381;&#2330;&#2367;&#2340; &#2325;&#2366;&#2352;&#2381;&#2351; &#2325;&#2379; &#2325;&#2352;&#2344;&#2375; &#2325;&#2375; &#2354;&#2367;&#2319; &#2348;&#2344;&#2366;&#2351;&#2366; &#2332;&#2366;&#2340;&#2366; &#2361;&#2376;&#2404; &#2351;&#2361; &#2360;&#2377;&#2347;&#2381;&#2335;&#2357;&#2375;&#2351;&#2352; &#2325;&#2366; &#2319;&#2325; &#2352;&#2370;&#2346; &#2361;&#2376; &#2332;&#2379; &#2313;&#2346;&#2351;&#2379;&#2327;&#2325;&#2352;&#2381;&#2340;&#2366; &#2325;&#2375; &#2309;&#2344;&#2369;&#2352;&#2379;&#2343; &#2346;&#2352; &#2330;&#2354;&#2340;&#2366; &#2361;&#2376; &#2351;&#2366; &#2344;&#2367;&#2359;&#2381;&#2346;&#2366;&#2342;&#2367;&#2340; &#2325;&#2352;&#2340;&#2366; &#2361;&#2376;&#2404; &#2313;&#2342;&#2366;&#2361;&#2352;&#2339; - &#2327;&#2381;&#2352;&#2366;&#2347;&#2367;&#2325;&#2381;&#2360; &#2360;&#2377;&#2347;&#2381;&#2335;&#2357;&#2375;&#2351;&#2352;, </span><strong>&#2360;&#2381;&#2346;&#2381;&#2352;&#2375;&#2337;&#2358;&#2368;&#2335; &#2360;&#2377;&#2347;&#2381;&#2335;&#2357;&#2375;&#2351;&#2352;, </strong><span style=\"font-weight: 400;\">&#2357;&#2352;&#2381;&#2337;-&#2346;&#2381;&#2352;&#2379;&#2360;&#2375;&#2360;&#2367;&#2306;&#2327; &#2360;&#2377;&#2347;&#2381;&#2335;&#2357;&#2375;&#2351;&#2352;&#2404;&nbsp;</span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "18",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">10.</span><span style=\"font-family: Cambria Math;\"> Which among the following is another word for universe?</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">10.</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2381;&#2352;&#2361;&#2381;&#2350;&#2366;&#2306;&#2337;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2370;&#2360;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2358;&#2348;&#2381;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2380;&#2344;</span><span style=\"font-family: Cambria Math;\">-</span><span style=\"font-family: Cambria Math;\">&#2360;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">? </span></p>\n",
                    options_en: ["<p>Big Crunch</p>\n", "<p>Astronomy</p>\n", 
                                "<p>Cosmos</p>\n", "<p>Supernova</p>\n"],
                    options_hi: ["<p><span style=\"font-family: Cambria Math;\">&#2348;&#2367;&#2327;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2381;&#2352;&#2306;&#2330;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><span style=\"font-family: Cambria Math;\">&#2326;&#2327;&#2379;&#2354;</span><span style=\"font-family: Cambria Math;\">-</span><span style=\"font-family: Cambria Math;\">&#2357;&#2367;&#2332;&#2381;&#2334;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                                "<p><span style=\"font-family: Cambria Math;\">&#2325;&#2377;&#2360;&#2350;&#2377;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><span style=\"font-family: Cambria Math;\">&#2360;&#2369;&#2346;&#2352;&#2344;&#2379;&#2357;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n"],
                    solution_en: "<p>10.<span style=\"font-family: Cambria Math;\">(c) <strong>&nbsp;Cosmos</strong><span style=\"font-weight: 400;\"> (Greek word) meaning both \"order\" and \"world\". </span><strong>Astronomy</strong><span style=\"font-weight: 400;\">: Study of everything in the universe beyond Earth\'s atmosphere. </span><strong>Supernova </strong><span style=\"font-weight: 400;\">is a powerful and luminous explosion of a star. The </span><strong>Big Crunch</strong><span style=\"font-weight: 400;\"> is one of the scenarios predicted by scientists in which the Universe may end.</span></span></p>\n",
                    solution_hi: "<p>10.<span style=\"font-family: Cambria Math;\">(c)&nbsp;</span><strong>&#2325;&#2377;&#2360;&#2350;&#2377;&#2360; &#2404; </strong><span style=\"font-weight: 400;\">&#2351;&#2361; &#2350;&#2370;&#2354; &#2352;&#2370;&#2346; &#2360;&#2375; &#2319;&#2325; </span><strong>&#2327;&#2381;&#2352;&#2368;&#2325; &#2358;&#2348;&#2381;&#2342;</strong><span style=\"font-weight: 400;\"> &#2361;&#2376;, &#2332;&#2367;&#2360;&#2325;&#2366; &#2309;&#2352;&#2381;&#2341; \"&#2310;&#2342;&#2375;&#2358;\" &#2324;&#2352; \"&#2360;&#2306;&#2360;&#2366;&#2352;\" &#2342;&#2379;&#2344;&#2379;&#2306; &#2361;&#2376;&#2404; </span><strong>&#2326;&#2327;&#2379;&#2354; &#2357;&#2367;&#2332;&#2381;&#2334;&#2366;&#2344; </strong><span style=\"font-weight: 400;\">&#2346;&#2371;&#2341;&#2381;&#2357;&#2368; &#2325;&#2375; &#2357;&#2366;&#2351;&#2369;&#2350;&#2306;&#2337;&#2354; &#2360;&#2375; &#2346;&#2352;&#2375; &#2348;&#2381;&#2352;&#2361;&#2381;&#2350;&#2366;&#2306;&#2337; &#2350;&#2375;&#2306; &#2361;&#2352; &#2330;&#2368;&#2332; &#2325;&#2366; &#2309;&#2343;&#2381;&#2351;&#2351;&#2344; &#2361;&#2376;&#2404; &#2360;&#2369;&#2346;&#2352;&#2344;&#2379;&#2357;&#2366; &#2351;&#2366; &#2309;&#2343;&#2367;&#2344;&#2357;&#2340;&#2366;&#2352;&#2366; (supernova) &#2325;&#2367;&#2360;&#2368; &#2340;&#2366;&#2352;&#2375; &#2325;&#2375; &#2349;&#2351;&#2306;&#2325;&#2352; &#2357;&#2367;&#2360;&#2381;&#2347;&#2379;&#2335; &#2325;&#2379; &#2325;&#2361;&#2340;&#2375; &#2361;&#2376;&#2306;&#2404;&#2404; &#2342; </span><strong>&#2348;&#2367;&#2327; &#2325;&#2381;&#2352;&#2306;&#2330;</strong><span style=\"font-weight: 400;\"> &#2357;&#2376;&#2332;&#2381;&#2334;&#2366;&#2344;&#2367;&#2325;&#2379;&#2306; &#2342;&#2381;&#2357;&#2366;&#2352;&#2366; &#2349;&#2357;&#2367;&#2359;&#2381;&#2351;&#2357;&#2366;&#2339;&#2368; &#2325;&#2368; &#2327;&#2312; &#2313;&#2344; &#2360;&#2381;&#2341;&#2367;&#2340;&#2367;&#2351;&#2379;&#2306; &#2350;&#2375;&#2306; &#2360;&#2375; &#2319;&#2325; &#2361;&#2376; &#2332;&#2367;&#2360;&#2350;&#2375;&#2306; &#2348;&#2381;&#2352;&#2361;&#2381;&#2350;&#2366;&#2306;&#2337; &#2360;&#2350;&#2366;&#2346;&#2381;&#2340; &#2361;&#2379; &#2360;&#2325;&#2340;&#2366; &#2361;&#2376;&#2404;</span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "18",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">11.</span><span style=\"font-family: Cambria Math;\">\"Pay </span><span style=\"font-family: Cambria Math;\">Roll Automation for Disbursement of Monthly Allowances (PADMA)\" was aunched in June 2022 by Ministry of________</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">11.</span><span style=\"font-family: Cambria Math;\"> \"</span><span style=\"font-family: Cambria Math;\">&#2350;&#2366;&#2360;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2349;&#2340;&#2381;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2367;&#2340;&#2352;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2352;&#2379;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2321;&#2335;&#2379;&#2350;&#2375;&#2358;&#2344;</span><span style=\"font-family: Cambria Math;\"> (</span><span style=\"font-family: Cambria Math;\">&#2346;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2352;&#2379;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2321;&#2335;&#2379;&#2350;&#2375;&#2358;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2347;&#2377;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2337;&#2367;&#2360;&#2381;&#2348;&#2352;&#2381;&#2360;&#2350;&#2375;&#2306;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2321;&#2347;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2306;&#2341;</span><span style=\"font-family: Cambria Math;\">&#2354;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2309;&#2354;&#2366;&#2313;&#2306;&#2360;</span><span style=\"font-family: Cambria Math;\"> (PADMA)\" </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2358;&#2369;&#2349;&#2366;&#2352;&#2306;&#2349;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2332;&#2370;&#2344;</span><span style=\"font-family: Cambria Math;\"> 2022 </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> ________ </span><span style=\"font-family: Cambria Math;\">&#2350;&#2306;&#2340;&#2381;&#2352;&#2366;&#2354;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2381;&#2357;&#2366;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2327;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2341;&#2366;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                    options_en: ["<p>Civil Aviation</p>\n", "<p>Finance</p>\n", 
                                "<p>Defence</p>\n", "<p>Culture</p>\n"],
                    options_hi: ["<p><span style=\"font-family: Cambria Math;\">&#2344;&#2366;&#2327;&#2352;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2313;&#2337;&#2381;&#2337;&#2351;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><span style=\"font-family: Cambria Math;\">&#2357;&#2367;&#2340;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                                "<p><span style=\"font-family: Cambria Math;\">&#2352;&#2325;&#2381;&#2359;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><span style=\"font-family: Cambria Math;\">&#2360;&#2306;&#2360;&#2381;&#2325;&#2371;&#2340;&#2367;</span></p>\n"],
                    solution_en: "<p>11.(c)<strong>&nbsp;</strong><strong>Defence. PADMA</strong><span style=\"font-weight: 400;\">, an automated Pay &amp; Allowances module for the Indian Coast Guard was inaugurated by Shri Rajnish Kumar, Controller General of Defence Accounts (CGDA), </span><strong>Ministry of Defence</strong><span style=\"font-weight: 400;\">. Centralised Pay System for Indian Coast Guard launched.</span></p>\n",
                    solution_hi: "<p>11.(c)&nbsp;<strong>&#2352;&#2325;&#2381;&#2359;&#2366;&#2404; PADMA, </strong><span style=\"font-weight: 400;\">&#2349;&#2366;&#2352;&#2340;&#2368;&#2351; &#2340;&#2335; &#2352;&#2325;&#2381;&#2359;&#2325; &#2325;&#2375; &#2354;&#2367;&#2319; &#2319;&#2325; &#2360;&#2381;&#2357;&#2330;&#2366;&#2354;&#2367;&#2340; &#2357;&#2375;&#2340;&#2344; &#2324;&#2352; &#2349;&#2340;&#2381;&#2340;&#2366; &#2350;&#2377;&#2337;&#2381;&#2351;&#2370;&#2354; &#2325;&#2366; &#2313;&#2342;&#2381;&#2328;&#2366;&#2335;&#2344; </span><strong>&#2352;&#2325;&#2381;&#2359;&#2366; &#2350;&#2306;&#2340;&#2381;&#2352;&#2366;&#2354;&#2351;</strong><span style=\"font-weight: 400;\"> &#2325;&#2375; &#2352;&#2325;&#2381;&#2359;&#2366; &#2354;&#2375;&#2326;&#2366; &#2350;&#2361;&#2366;&#2344;&#2367;&#2351;&#2306;&#2340;&#2381;&#2352;&#2325; (&#2360;&#2368;&#2332;&#2368;&#2337;&#2368;&#2319;) &#2358;&#2381;&#2352;&#2368; &#2352;&#2332;&#2344;&#2368;&#2358; &#2325;&#2369;&#2350;&#2366;&#2352; &#2344;&#2375; &#2325;&#2367;&#2351;&#2366;&#2404; &#2349;&#2366;&#2352;&#2340;&#2368;&#2351; &#2340;&#2335; &#2352;&#2325;&#2381;&#2359;&#2325; &#2325;&#2375; &#2354;&#2367;&#2319; &#2325;&#2375;&#2306;&#2342;&#2381;&#2352;&#2368;&#2325;&#2371;&#2340; &#2357;&#2375;&#2340;&#2344; &#2346;&#2381;&#2352;&#2339;&#2366;&#2354;&#2368; &#2358;&#2369;&#2352;&#2370; &#2325;&#2368; &#2327;&#2312;&#2404;</span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "18",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">12. </span><span style=\"font-family: Cambria Math;\">Who among the following received the OSCAR for the best actor in l</span><span style=\"font-family: Cambria Math;\">eading role in 2022?</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">12.</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2367;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> 2022 </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2381;&#2352;&#2350;&#2369;&#2326;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2349;&#2370;&#2350;&#2367;&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2352;&#2381;&#2357;&#2358;&#2381;&#2352;&#2375;&#2359;&#2381;&#2336;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2309;&#2349;&#2367;&#2344;&#2375;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2321;&#2360;&#2381;&#2325;&#2352;</span><span style=\"font-family: Cambria Math;\"> (0SCAR) </span><span style=\"font-family: Cambria Math;\">&#2346;&#2369;&#2352;&#2360;&#2381;&#2325;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2327;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\">? </span></p>\n",
                    options_en: ["<p>Benedict Cumberbatch</p>\n", "<p>Andrew Garfield</p>\n", 
                                "<p>Denzel Washington</p>\n", "<p>Will Smith</p>\n"],
                    options_hi: ["<p><span style=\"font-family: Cambria Math;\">&#2348;&#2375;&#2344;&#2375;&#2337;&#2367;&#2325;&#2381;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2306;&#2348;&#2352;&#2348;&#2376;&#2330;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><span style=\"font-family: Cambria Math;\">&#2319;&#2306;&#2337;&#2381;&#2352;&#2351;&#2370;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2327;&#2366;&#2352;&#2347;&#2364;&#2368;&#2354;&#2381;&#2337;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                                "<p><span style=\"font-family: Cambria Math;\">&#2337;&#2375;&#2344;&#2332;&#2364;&#2375;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2377;&#2358;&#2367;&#2306;&#2327;&#2335;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><span style=\"font-family: Cambria Math;\">&#2357;&#2367;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2381;&#2350;&#2367;&#2341;</span><span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\n"],
                    solution_en: "<p>12.(d) <strong>Will Smith. </strong><span style=\"font-weight: 400;\">The Oscar Awards were awarded for the first time on 16 May 1929 (</span><strong>Los Angeles</strong><span style=\"font-weight: 400;\">). </span><strong>Indian</strong><span style=\"font-weight: 400;\"> - Bhanu Athaiya (for costume designing, 1982), Satyajit Ray (Honorary Award,1992). </span><strong>94</strong><strong>th</strong><strong> Oscar Awards (2022)</strong><span style=\"font-weight: 400;\"> -</span><span style=\"font-weight: 400;\"> </span><span style=\"font-weight: 400;\">Best Picture (</span><strong>CODA</strong><span style=\"font-weight: 400;\">), Best Director (</span><strong>Jane Campion</strong><span style=\"font-weight: 400;\">). </span><strong>95</strong><strong>th</strong><strong> Oscar Awards</strong><span style=\"font-weight: 400;\"> (2023) - Best original song (</span><strong>Naatu Naatu, RRR</strong><span style=\"font-weight: 400;\">), Best documentary short (</span><strong>The Elephant Whisperers</strong><span style=\"font-weight: 400;\">), Best actor (Brendan Fraser, The Whale), Best film (All Quiet on the Western Front). </span><strong>Denzel Washington</strong><span style=\"font-weight: 400;\"> has won numerous awards including Oscars in 2002, Oscars in 1990 and Golden Globe Award in 2016.</span></p>\n",
                    solution_hi: "<p>12.(d)&nbsp;<strong>&#2357;&#2367;&#2354; &#2360;&#2381;&#2350;&#2367;&#2341;&#2404; </strong><span style=\"font-weight: 400;\">&#2321;&#2360;&#2381;&#2325;&#2352; &#2346;&#2369;&#2352;&#2360;&#2381;&#2325;&#2366;&#2352; &#2346;&#2361;&#2354;&#2368; &#2348;&#2366;&#2352; 16 &#2350;&#2312; 1929 (</span><strong>&#2354;&#2377;&#2360; &#2319;&#2306;&#2332;&#2367;&#2354;&#2367;&#2360;</strong><span style=\"font-weight: 400;\">) &#2350;&#2375;&#2306; &#2342;&#2367;&#2319; &#2327;&#2319; &#2341;&#2375;&#2404; </span><strong>&#2349;&#2366;&#2352;&#2340;&#2368;&#2351;</strong><span style=\"font-weight: 400;\"> - &#2349;&#2366;&#2344;&#2369; &#2309;&#2341;&#2376;&#2351;&#2366; (&#2325;&#2377;&#2360;&#2381;&#2335;&#2381;&#2351;&#2370;&#2350; &#2337;&#2367;&#2332;&#2366;&#2311;&#2344;&#2367;&#2306;&#2327; &#2325;&#2375; &#2354;&#2367;&#2319;, 1982), &#2360;&#2340;&#2381;&#2351;&#2332;&#2368;&#2340; &#2352;&#2375; (&#2350;&#2366;&#2344;&#2342; &#2346;&#2369;&#2352;&#2360;&#2381;&#2325;&#2366;&#2352;, 1992)&#2404; </span><strong>94&#2357;&#2375;&#2306; &#2321;&#2360;&#2381;&#2325;&#2352; &#2309;&#2357;&#2377;&#2352;&#2381;&#2337;&#2381;&#2360; (2022) </strong><span style=\"font-weight: 400;\">- &#2348;&#2375;&#2360;&#2381;&#2335; &#2346;&#2367;&#2325;&#2381;&#2330;&#2352; </span><strong>(CODA)</strong><span style=\"font-weight: 400;\">, &#2348;&#2375;&#2360;&#2381;&#2335; &#2337;&#2366;&#2351;&#2352;&#2375;&#2325;&#2381;&#2335;&#2352; (</span><strong>&#2332;&#2375;&#2344; &#2325;&#2376;&#2306;&#2346;&#2367;&#2351;&#2344;</strong><span style=\"font-weight: 400;\">)&#2404; </span><strong>95&#2357;&#2366;&#2306; &#2321;&#2360;&#2381;&#2325;&#2352; &#2346;&#2369;&#2352;&#2360;&#2381;&#2325;&#2366;&#2352;</strong><span style=\"font-weight: 400;\"> (2023) - &#2360;&#2352;&#2381;&#2357;&#2358;&#2381;&#2352;&#2375;&#2359;&#2381;&#2336; &#2350;&#2370;&#2354; &#2327;&#2368;&#2340; (&#2344;&#2366;&#2340;&#2369; &#2344;&#2366;&#2340;&#2369;, RRR), &#2360;&#2352;&#2381;&#2357;&#2358;&#2381;&#2352;&#2375;&#2359;&#2381;&#2336; &#2358;&#2377;&#2352;&#2381;&#2335; &#2337;&#2366;&#2325;&#2381;&#2351;&#2370;&#2350;&#2375;&#2306;&#2335;&#2381;&#2352;&#2368; (</span><strong>&#2342; &#2319;&#2354;&#2367;&#2347;&#2375;&#2306;&#2335; &#2357;&#2381;&#2361;&#2367;&#2360;&#2381;&#2346;&#2352;&#2352;&#2381;&#2360;</strong><span style=\"font-weight: 400;\">), &#2360;&#2352;&#2381;&#2357;&#2358;&#2381;&#2352;&#2375;&#2359;&#2381;&#2336; &#2309;&#2349;&#2367;&#2344;&#2375;&#2340;&#2366; (&#2348;&#2381;&#2352;&#2375;&#2306;&#2337;&#2344; &#2347;&#2381;&#2352;&#2375;&#2332;&#2352;, &#2342; &#2357;&#2381;&#2361;&#2375;&#2354;), &#2360;&#2352;&#2381;&#2357;&#2358;&#2381;&#2352;&#2375;&#2359;&#2381;&#2336; &#2347;&#2367;&#2354;&#2381;&#2350; (&#2321;&#2354; &#2325;&#2381;&#2357;&#2366;&#2311;&#2335; &#2321;&#2344; &#2342; &#2357;&#2375;&#2360;&#2381;&#2335;&#2352;&#2381;&#2344; &#2347;&#2381;&#2352;&#2306;&#2335;) &#2404; </span><strong>&#2337;&#2375;&#2344;&#2332;&#2375;&#2354; </strong><span style=\"font-weight: 400;\">&#2357;&#2366;&#2358;&#2367;&#2306;&#2327;&#2335;&#2344; &#2344;&#2375; 2002 &#2350;&#2375;&#2306; &#2321;&#2360;&#2381;&#2325;&#2352;, 1990 &#2350;&#2375;&#2306; &#2321;&#2360;&#2381;&#2325;&#2352; &#2324;&#2352; 2016 &#2350;&#2375;&#2306; &#2327;&#2379;&#2354;&#2381;&#2337;&#2344; &#2327;&#2381;&#2354;&#2379;&#2348; &#2309;&#2357;&#2366;&#2352;&#2381;&#2337; &#2360;&#2361;&#2367;&#2340; &#2325;&#2312; &#2346;&#2369;&#2352;&#2360;&#2381;&#2325;&#2366;&#2352; &#2332;&#2368;&#2340;&#2375; &#2361;&#2376;&#2306;&#2404;</span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "18",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">13. </span><span style=\"font-family: Cambria Math;\">In which year was the Dowry Prohibition Act passed in India?</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">13. </span><span style=\"font-family: Cambria Math;\">&#2349;&#2366;&#2352;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2361;&#2375;&#2332;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2359;&#2375;&#2343;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2309;&#2343;&#2367;&#2344;&#2367;&#2351;&#2350;</span><span style=\"font-family: Cambria Math;\"> (Dowry Prohibition Act) </span><span style=\"font-family: Cambria Math;\">&#2325;&#2367;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2352;&#2381;&#2359;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2366;&#2352;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2327;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2341;&#2366;</span><span style=\"font-family: Cambria Math;\">?</span><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                    options_en: ["<p>1973</p>\n", "<p>1982</p>\n", 
                                "<p>1961</p>\n", "<p>1954</p>\n"],
                    options_hi: ["<p>1973</p>\n", "<p>1982</p>\n",
                                "<p>1961</p>\n", "<p>1954</p>\n"],
                    solution_en: "<p>13.(c) <strong>1961. Dowry Prohibition Act:</strong> It extends to the whole of India except the State of Jammu and Kashmir.<strong> Important Acts in India</strong> - Hindu Widow Remarriage Act 1856, Child Marriage Restraint Act 1929, Muslim Women Protection of Rights on Marriage Act 2019 (Triple Talaq Act), Rights of Persons with Disabilities Act 2016, National Security Act 1980.</p>\n",
                    solution_hi: "<p>13.(c)&nbsp;<strong>1961&#2404; &#2342;&#2361;&#2375;&#2332; &#2344;&#2367;&#2359;&#2375;&#2343; &#2309;&#2343;&#2367;&#2344;&#2367;&#2351;&#2350;: </strong><span style=\"font-weight: 400;\">&#2351;&#2361; &#2332;&#2350;&#2381;&#2350;&#2370; &#2324;&#2352; &#2325;&#2358;&#2381;&#2350;&#2368;&#2352; &#2352;&#2366;&#2332;&#2381;&#2351; &#2325;&#2379; &#2331;&#2379;&#2337;&#2364;&#2325;&#2352; &#2346;&#2370;&#2352;&#2375; &#2349;&#2366;&#2352;&#2340; &#2350;&#2375;&#2306; &#2354;&#2366;&#2327;&#2370; &#2361;&#2376;&#2404; &#2349;&#2366;&#2352;&#2340; &#2350;&#2375;&#2306; &#2350;&#2361;&#2340;&#2381;&#2357;&#2346;&#2370;&#2352;&#2381;&#2339; &#2309;&#2343;&#2367;&#2344;&#2367;&#2351;&#2350; - &#2361;&#2367;&#2306;&#2342;&#2370; &#2357;&#2367;&#2343;&#2357;&#2366; &#2346;&#2369;&#2344;&#2352;&#2381;&#2357;&#2367;&#2357;&#2366;&#2361; &#2309;&#2343;&#2367;&#2344;&#2367;&#2351;&#2350; 1856, &#2348;&#2366;&#2354; &#2357;&#2367;&#2357;&#2366;&#2361; &#2352;&#2379;&#2325;&#2341;&#2366;&#2350; &#2309;&#2343;&#2367;&#2344;&#2367;&#2351;&#2350; 1929, &#2350;&#2369;&#2360;&#2381;&#2354;&#2367;&#2350; &#2350;&#2361;&#2367;&#2354;&#2366; &#2357;&#2367;&#2357;&#2366;&#2361; &#2309;&#2343;&#2367;&#2325;&#2366;&#2352;&#2379;&#2306; &#2325;&#2366; &#2360;&#2306;&#2352;&#2325;&#2381;&#2359;&#2339; &#2309;&#2343;&#2367;&#2344;&#2367;&#2351;&#2350; 2019 (&#2340;&#2368;&#2344; &#2340;&#2354;&#2366;&#2325; &#2309;&#2343;&#2367;&#2344;&#2367;&#2351;&#2350;), &#2357;&#2367;&#2325;&#2354;&#2366;&#2306;&#2327; &#2357;&#2381;&#2351;&#2325;&#2381;&#2340;&#2367;&#2351;&#2379;&#2306; &#2325;&#2375; &#2309;&#2343;&#2367;&#2325;&#2366;&#2352; &#2309;&#2343;&#2367;&#2344;&#2367;&#2351;&#2350; 2016, &#2352;&#2366;&#2359;&#2381;&#2335;&#2381;&#2352;&#2368;&#2351; &#2360;&#2369;&#2352;&#2325;&#2381;&#2359;&#2366; &#2309;&#2343;&#2367;&#2344;&#2367;&#2351;&#2350; 1980&#2404;</span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "18",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">14.</span><span style=\"font-family: Cambria Math;\"> PV Sindhu, a famous badminton player of India, won Open title in July 2022.</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">14. </span><span style=\"font-family: Cambria Math;\">&#2346;&#2381;&#2352;&#2360;&#2367;&#2342;&#2381;&#2343;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2349;&#2366;&#2352;&#2340;&#2368;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2376;&#2337;&#2350;&#2367;&#2306;&#2335;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2326;&#2367;&#2354;&#2366;&#2337;&#2364;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2368;&#2357;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2367;&#2306;&#2343;&#2369;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2332;&#2369;&#2354;&#2366;&#2312;</span><span style=\"font-family: Cambria Math;\"> 2022 </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> ________</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2323;&#2346;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2326;&#2367;&#2340;&#2366;&#2348;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2332;&#2368;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2341;&#2366;</span><span style=\"font-family: Cambria Math;\">?</span><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                    options_en: ["<p>Thailand</p>\n", "<p>India</p>\n", 
                                "<p>Denmark</p>\n", "<p>Singapore</p>\n"],
                    options_hi: ["<p><span style=\"font-family: Cambria Math;\">&#2341;&#2366;&#2351;&#2354;&#2376;&#2306;&#2337;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><span style=\"font-family: Cambria Math;\">&#2349;&#2366;&#2352;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                                "<p><span style=\"font-family: Cambria Math;\">&#2337;&#2375;&#2344;&#2350;&#2366;&#2352;&#2381;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><span style=\"font-family: Cambria Math;\">&#2360;&#2367;&#2306;&#2327;&#2366;&#2346;&#2369;&#2352;</span></p>\n"],
                    solution_en: "<p>14.(d)&nbsp;<strong>Singapore. </strong><span style=\"font-weight: 400;\">Pusarla Venkata Sindhu (PV Sindhu):&nbsp; She won the title by defeating </span><strong>Chinese shuttler Wang Ziyi</strong><span style=\"font-weight: 400;\"> in the final. </span><strong>Awards</strong><span style=\"font-weight: 400;\"> - Arjuna Award (2013), Padma Shri (2015), Rajiv Gandhi Khel Ratna (2016), Padma Bhushan (2020).</span></p>\n",
                    solution_hi: "<p>14.(d)&nbsp;<strong>&#2360;&#2367;&#2306;&#2327;&#2366;&#2346;&#2369;&#2352;&#2404; </strong><span style=\"font-weight: 400;\">&#2346;&#2369;&#2360;&#2352;&#2354;&#2366; &#2357;&#2375;&#2306;&#2325;&#2335; &#2360;&#2367;&#2306;&#2343;&#2369; (PV &#2360;&#2367;&#2306;&#2343;&#2369;) &#2319;&#2325; &#2349;&#2366;&#2352;&#2340;&#2368;&#2351; &#2348;&#2376;&#2337;&#2350;&#2367;&#2306;&#2335;&#2344; &#2326;&#2367;&#2354;&#2366;&#2337;&#2364;&#2368; &#2361;&#2376;&#2306;&#2404; &#2311;&#2344;&#2381;&#2361;&#2379;&#2344;&#2375; &#2347;&#2366;&#2311;&#2344;&#2354; &#2350;&#2375;&#2306; </span><strong>&#2330;&#2368;&#2344; &#2325;&#2368;</strong><span style=\"font-weight: 400;\"> </span><strong>&#2358;&#2335;&#2354;&#2352; &#2357;&#2366;&#2306;&#2327; &#2332;&#2367;&#2351;&#2368;</strong><span style=\"font-weight: 400;\"> &#2325;&#2379; &#2361;&#2352;&#2366;&#2325;&#2352; &#2326;&#2367;&#2340;&#2366;&#2348; &#2332;&#2368;&#2340;&#2366; &#2341;&#2366;&#2404; </span><strong>&#2346;&#2369;&#2352;&#2360;&#2381;&#2325;&#2366;&#2352;</strong><span style=\"font-weight: 400;\"> - &#2309;&#2352;&#2381;&#2332;&#2369;&#2344; &#2346;&#2369;&#2352;&#2360;&#2381;&#2325;&#2366;&#2352; (2013), &#2346;&#2342;&#2381;&#2350; &#2358;&#2381;&#2352;&#2368; (2015), &#2352;&#2366;&#2332;&#2368;&#2357; &#2327;&#2366;&#2306;&#2343;&#2368; &#2326;&#2375;&#2354; &#2352;&#2340;&#2381;&#2344; (2016), &#2346;&#2342;&#2381;&#2350; &#2349;&#2370;&#2359;&#2339; (2020)&#2404;</span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "18",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">15.</span><span style=\"font-family: Cambria Math;\"> Who became the first athlete to win Gold in Yogasana at the 36th National Games, India in 2022?</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">15. </span><span style=\"font-family: Cambria Math;\">2022 </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> 36</span><span style=\"font-family: Cambria Math;\">&#2357;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2349;&#2366;&#2352;&#2340;&#2368;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2352;&#2366;&#2359;&#2381;&#2335;&#2381;&#2352;&#2368;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2326;&#2375;&#2354;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2351;&#2379;&#2327;&#2366;&#2360;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2381;&#2357;&#2352;&#2381;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2342;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2332;&#2368;&#2340;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2366;&#2354;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2361;&#2354;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2326;&#2367;&#2354;&#2366;&#2337;&#2364;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2380;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\">? </span></p>\n",
                    options_en: ["<p>Pooja Patel</p>\n", "<p>Manisha Kumari</p>\n", 
                                "<p>Soham Swami</p>\n", "<p>Raja Gupta</p>\n"],
                    options_hi: ["<p><span style=\"font-family: Cambria Math;\">&#2346;&#2370;&#2332;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2335;&#2375;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><span style=\"font-family: Cambria Math;\">&#2350;&#2344;&#2368;&#2359;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2369;&#2350;&#2366;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                                "<p><span style=\"font-family: Cambria Math;\">&#2360;&#2379;&#2361;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2381;&#2357;&#2366;&#2350;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><span style=\"font-family: Cambria Math;\">&#2352;&#2366;&#2332;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2327;&#2369;&#2346;&#2381;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n"],
                    solution_en: "<p>15.<span style=\"font-family: Cambria Math;\">(a)&nbsp;</span><strong>Pooja Patel. </strong><span style=\"font-weight: 400;\">She has become the </span><strong>first athlete</strong><span style=\"font-weight: 400;\"> from Gujarat to win</span><strong> gold</strong><span style=\"font-weight: 400;\"> in Yogasana. The Indian Olympic Games were named as National Games </span><strong>beginning in 1940</strong><span style=\"font-weight: 400;\">. </span><strong>36</strong><strong>th</strong><strong> National Games - </strong><span style=\"font-weight: 400;\">Host State (Gujarat), Mascot </span><strong>- </strong><span style=\"font-weight: 400;\">(SAVAJ - Asiatic lion), Moto (Celebrating unity through sports), Adding game in this year -&nbsp; Mallakhamba and Yogasana and removed - Handball, 1st Rank- Service Sports Control Board (SSCB, Combined team of the Indian Army, Navy and Air force), 2</span><span style=\"font-weight: 400;\">nd</span><span style=\"font-weight: 400;\"> Rank - Maharashtra, 3</span><span style=\"font-weight: 400;\">rd</span><span style=\"font-weight: 400;\">Rank - Haryana.</span><strong> </strong><span style=\"font-weight: 400;\">37</span><span style=\"font-weight: 400;\">th</span><span style=\"font-weight: 400;\"> National Games - </span><strong>Goa</strong><span style=\"font-weight: 400;\">, </span><strong>&nbsp;</strong><span style=\"font-weight: 400;\">38</span><span style=\"font-weight: 400;\">th</span><span style=\"font-weight: 400;\"> National Games</span><strong> - Uttarakhand.</strong></p>\n",
                    solution_hi: "<p>15.<span style=\"font-family: Cambria Math;\">(a) <strong>&#2346;&#2370;&#2332;&#2366; &#2346;&#2335;&#2375;&#2354;&#2404; </strong><span style=\"font-weight: 400;\">&#2351;&#2379;&#2327;&#2366;&#2360;&#2344; &#2350;&#2375;&#2306; </span><strong>&#2360;&#2381;&#2357;&#2352;&#2381;&#2339; </strong><span style=\"font-weight: 400;\">&#2332;&#2368;&#2340;&#2344;&#2375; &#2357;&#2366;&#2354;&#2368; &#2327;&#2369;&#2332;&#2352;&#2366;&#2340; &#2325;&#2368; </span><strong>&#2346;&#2361;&#2354;&#2368; &#2319;&#2341;&#2354;&#2368;&#2335; </strong><span style=\"font-weight: 400;\">&#2348;&#2344; &#2327;&#2312; &#2361;&#2376;&#2306;&#2404; </span><strong>1940 &#2360;&#2375; &#2358;&#2369;&#2352;&#2370;</strong><span style=\"font-weight: 400;\"> &#2361;&#2379;&#2344;&#2375; &#2357;&#2366;&#2354;&#2375; &#2349;&#2366;&#2352;&#2340;&#2368;&#2351; &#2323;&#2354;&#2306;&#2346;&#2367;&#2325; &#2326;&#2375;&#2354;&#2379;&#2306; &#2325;&#2379; &#2352;&#2366;&#2359;&#2381;&#2335;&#2381;&#2352;&#2368;&#2351; &#2326;&#2375;&#2354;&#2379;&#2306; &#2325;&#2366; &#2344;&#2366;&#2350; &#2342;&#2367;&#2351;&#2366; &#2327;&#2351;&#2366;&#2404; </span><strong>36&#2357;&#2375;&#2306; &#2352;&#2366;&#2359;&#2381;&#2335;&#2381;&#2352;&#2368;&#2351; &#2326;&#2375;&#2354;</strong><span style=\"font-weight: 400;\"> - &#2350;&#2375;&#2332;&#2348;&#2366;&#2344; &#2352;&#2366;&#2332;&#2381;&#2351; (&#2327;&#2369;&#2332;&#2352;&#2366;&#2340;), &#2358;&#2369;&#2349;&#2306;&#2325;&#2352; - (SAVAJ - &#2319;&#2358;&#2367;&#2351;&#2366;&#2312; &#2358;&#2375;&#2352; ), &#2350;&#2379;&#2335;&#2379; (&#2326;&#2375;&#2354; &#2325;&#2375; &#2350;&#2366;&#2343;&#2381;&#2351;&#2350; &#2360;&#2375; &#2319;&#2325;&#2340;&#2366; &#2325;&#2366; &#2332;&#2358;&#2381;&#2344;), &#2311;&#2360; &#2357;&#2352;&#2381;&#2359; &#2350;&#2375;&#2306; &#2326;&#2375;&#2354; &#2332;&#2379;&#2337;&#2364;&#2366; &#2327;&#2351;&#2366; - &#2350;&#2354;&#2381;&#2354;&#2326;&#2306;&#2349; &#2324;&#2352; &#2351;&#2379;&#2327;&#2366;&#2360;&#2344; &#2324;&#2352; &#2361;&#2335;&#2366;&#2351;&#2366; &#2327;&#2351;&#2366; - &#2361;&#2376;&#2306;&#2337;&#2348;&#2377;&#2354;, &#2346;&#2361;&#2354;&#2368; &#2352;&#2376;&#2306;&#2325; - &#2360;&#2352;&#2381;&#2357;&#2367;&#2360;&#2375;&#2332;, &#2342;&#2370;&#2360;&#2352;&#2368; &#2352;&#2376;&#2306;&#2325; - &#2350;&#2361;&#2366;&#2352;&#2366;&#2359;&#2381;&#2335;&#2381;&#2352;, &#2340;&#2368;&#2360;&#2352;&#2368; &#2352;&#2376;&#2306;&#2325; - &#2361;&#2352;&#2367;&#2351;&#2366;&#2339;&#2366;&#2404; 37&#2357;&#2375;&#2306; &#2352;&#2366;&#2359;&#2381;&#2335;&#2381;&#2352;&#2368;&#2351; &#2326;&#2375;&#2354; - </span><strong>&#2327;&#2379;&#2357;&#2366;</strong><span style=\"font-weight: 400;\">, 38&#2357;&#2375;&#2306; &#2352;&#2366;&#2359;&#2381;&#2335;&#2381;&#2352;&#2368;&#2351; &#2326;&#2375;&#2354; - </span><strong>&#2313;&#2340;&#2381;&#2340;&#2352;&#2366;&#2326;&#2306;&#2337;</strong><span style=\"font-weight: 400;\">&#2404;</span></span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "16",
                    section: "18",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">16</span><span style=\"font-family: Cambria Math;\">.India, palm, coconut, keora, agar are the common trees of which of the following forests?</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">16.</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2349;&#2366;&#2352;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2340;&#2366;&#2337;&#2364;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2344;&#2366;&#2352;&#2367;&#2351;&#2354;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;&#2357;&#2337;&#2364;&#2366;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2320;&#2306;&#2327;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2367;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2366;&#2350;&#2366;&#2344;&#2381;&#2351;&#2340;&#2307;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2366;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2332;&#2366;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2366;&#2354;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2371;&#2325;&#2381;&#2359;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;&#2306;</span><span style=\"font-family: Cambria Math;\">? </span></p>\n",
                    options_en: ["<p>Mangrove Forests</p>\n", "<p>Tropical Evergreen Forests</p>\n", 
                                "<p>Tropical Deciduous Forests</p>\n", "<p>Montane Forests</p>\n"],
                    options_hi: ["<p><span style=\"font-family: Cambria Math;\">&#2350;&#2376;&#2306;&#2327;&#2381;&#2352;&#2379;&#2357;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><span style=\"font-family: Cambria Math;\">&#2313;&#2359;&#2381;&#2339;&#2325;&#2335;&#2367;&#2348;&#2306;&#2343;&#2368;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2342;&#2366;&#2348;&#2361;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                                "<p><span style=\"font-family: Cambria Math;\">&#2313;&#2359;&#2381;&#2339;&#2325;&#2335;&#2367;&#2348;&#2306;&#2343;&#2368;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2352;&#2381;&#2339;&#2346;&#2366;&#2340;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><span style=\"font-family: Cambria Math;\">&#2346;&#2352;&#2381;&#2357;&#2340;&#2368;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n"],
                    solution_en: "<p>16.(a) <strong>Mangrove Forests:</strong><span style=\"font-weight: 400;\"> It grows by the side of the coast and on the edges of the deltas. The fertile deltas of the Krishna, Mahanadi, Godavari, and Ganga comprise mangrove forests. </span><strong>Montane Forest</strong><span style=\"font-weight: 400;\"> is an ecosystem found in mountains. </span><strong>Tropical Evergreen Forests</strong><span style=\"font-weight: 400;\">&nbsp; (above 200 cm rainfall) are found in </span><strong>Western Ghats</strong><span style=\"font-weight: 400;\">, Lakshadweep and Andaman and Nicobar, north-eastern states. Important trees - ebony, mahogany, rosewood, rubber and cinchona. </span><strong>Tropical Deciduous Forests</strong><span style=\"font-weight: 400;\"> (rainfall between 200 cm and 70 cm) exist in the rainier plains of Bihar and Uttar Pradesh. Important trees - teak, sal, peepal.</span></p>\n",
                    solution_hi: "<p>16.(a) <strong>&#2350;&#2376;&#2306;&#2327;&#2381;&#2352;&#2379;&#2357; &#2357;&#2344;&#2404; </strong><span style=\"font-weight: 400;\">&#2351;&#2375;</span><strong> &#2327;&#2306;&#2327;&#2366;, &#2350;&#2361;&#2366;&#2344;&#2342;&#2368;, &#2325;&#2371;&#2359;&#2381;&#2339;&#2366;</strong><span style=\"font-weight: 400;\">, &#2327;&#2379;&#2342;&#2366;&#2357;&#2352;&#2368; &#2324;&#2352; &#2325;&#2366;&#2357;&#2375;&#2352;&#2368; &#2344;&#2342;&#2367;&#2351;&#2379;&#2306; &#2325;&#2375; &#2337;&#2375;&#2354;&#2381;&#2335;&#2366; &#2350;&#2375;&#2306; &#2346;&#2366;&#2319; &#2332;&#2366;&#2340;&#2375; &#2361;&#2376;&#2306;&#2404;</span><span style=\"font-weight: 400;\"> </span><strong>&#2346;&#2352;&#2381;&#2357;&#2340;&#2368;&#2351; &#2357;&#2344;</strong><span style=\"font-weight: 400;\"> &#2346;&#2361;&#2366;&#2337;&#2364;&#2379;&#2306; &#2350;&#2375;&#2306; &#2346;&#2366;&#2351;&#2366; &#2332;&#2366;&#2344;&#2375; &#2357;&#2366;&#2354;&#2366; &#2319;&#2325; &#2346;&#2366;&#2352;&#2367;&#2360;&#2381;&#2341;&#2367;&#2340;&#2367;&#2325;&#2368; &#2340;&#2306;&#2340;&#2381;&#2352; &#2361;&#2376;&#2404; </span><strong>&#2313;&#2359;&#2381;&#2339;&#2325;&#2335;&#2367;&#2348;&#2306;&#2343;&#2368;&#2351; &#2360;&#2342;&#2366;&#2348;&#2361;&#2366;&#2352; &#2357;&#2344; </strong><span style=\"font-weight: 400;\">(</span><strong>200 &#2360;&#2375;&#2350;&#2368;</strong><span style=\"font-weight: 400;\"> &#2324;&#2352; &#2309;&#2343;&#2367;&#2325; &#2357;&#2352;&#2381;&#2359;&#2366;)</span><strong> &#2346;&#2358;&#2381;&#2330;&#2367;&#2350;&#2368; &#2328;&#2366;&#2335;,</strong><span style=\"font-weight: 400;\"> &#2354;&#2325;&#2381;&#2359;&#2342;&#2381;&#2357;&#2368;&#2346; &#2324;&#2352; &#2309;&#2306;&#2337;&#2350;&#2366;&#2344; &#2324;&#2352; &#2344;&#2367;&#2325;&#2379;&#2348;&#2366;&#2352;, &#2313;&#2340;&#2381;&#2340;&#2352;-&#2346;&#2370;&#2352;&#2381;&#2357;&#2368; &#2352;&#2366;&#2332;&#2381;&#2351;&#2379;&#2306; &#2350;&#2375;&#2306; &#2346;&#2366;&#2319; &#2332;&#2366;&#2340;&#2375; &#2361;&#2376;&#2306;&#2404; &#2350;&#2361;&#2340;&#2381;&#2357;&#2346;&#2370;&#2352;&#2381;&#2339; &#2357;&#2371;&#2325;&#2381;&#2359; - &#2319;&#2348;&#2379;&#2344;&#2368;, &#2350;&#2361;&#2379;&#2327;&#2344;&#2368;, &#2358;&#2368;&#2358;&#2350;, &#2352;&#2348;&#2352; &#2324;&#2352; &#2360;&#2367;&#2344;&#2325;&#2379;&#2344;&#2366;&#2404; </span><strong>&#2313;&#2359;&#2381;&#2339;&#2325;&#2335;&#2367;&#2348;&#2306;&#2343;&#2368;&#2351; &#2346;&#2352;&#2381;&#2339;&#2346;&#2366;&#2340;&#2368; &#2357;&#2344;</strong><span style=\"font-weight: 400;\"> (200 &#2360;&#2375;&#2350;&#2368; &#2324;&#2352; 70 &#2360;&#2375;&#2350;&#2368; &#2325;&#2375; &#2348;&#2368;&#2330; &#2357;&#2352;&#2381;&#2359;&#2366;) &#2348;&#2367;&#2361;&#2366;&#2352; &#2324;&#2352; &#2313;&#2340;&#2381;&#2340;&#2352; &#2346;&#2381;&#2352;&#2342;&#2375;&#2358; &#2325;&#2375; &#2357;&#2352;&#2381;&#2359;&#2366; &#2357;&#2366;&#2354;&#2375; &#2350;&#2376;&#2342;&#2366;&#2344;&#2379;&#2306; &#2350;&#2375;&#2306; &#2350;&#2380;&#2332;&#2370;&#2342; &#2361;&#2376;&#2306;&#2404; &#2350;&#2361;&#2340;&#2381;&#2357;&#2346;&#2370;&#2352;&#2381;&#2339; &#2357;&#2371;&#2325;&#2381;&#2359;-&#2360;&#2366;&#2327;&#2380;&#2344;, &#2360;&#2366;&#2354;, &#2346;&#2368;&#2346;&#2354;&#2404;</span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "17",
                    section: "18",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">17.</span><span style=\"font-family: Cambria Math;\">In October</span><span style=\"font-family: Cambria Math;\"> 2022, who was appointed as the Attorney General of India?</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">17. </span><span style=\"font-family: Cambria Math;\">&#2309;&#2325;&#2381;&#2335;&#2370;&#2348;&#2352;</span><span style=\"font-family: Cambria Math;\"> 2022 </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2349;&#2366;&#2352;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2309;&#2335;&#2377;&#2352;&#2381;&#2344;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2332;&#2344;&#2352;&#2354;</span><span style=\"font-family: Cambria Math;\"> (</span><span style=\"font-family: Cambria Math;\">&#2350;&#2361;&#2366;&#2344;&#2381;&#2351;&#2366;&#2351;&#2357;&#2366;&#2342;&#2368;</span><span style=\"font-family: Cambria Math;\">) </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2352;&#2370;&#2346;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2367;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2344;&#2367;&#2351;&#2369;&#2325;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2327;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2341;&#2366;</span><span style=\"font-family: Cambria Math;\">?</span><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                    options_en: ["<p>Mukul Rohtagi</p>\n", "<p>Ram Jethmalani</p>\n", 
                                "<p>R Venkataramani</p>\n", "<p>KK Venugopal</p>\n"],
                    options_hi: ["<p><span style=\"font-family: Cambria Math;\">&#2350;&#2369;&#2325;&#2369;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2352;&#2379;&#2361;&#2340;&#2327;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><span style=\"font-family: Cambria Math;\">&#2352;&#2366;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2332;&#2375;&#2336;&#2350;&#2354;&#2366;&#2344;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                                "<p><span style=\"font-family: Cambria Math;\">&#2310;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2375;&#2306;&#2325;&#2335;&#2352;&#2350;&#2339;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><span style=\"font-family: Cambria Math;\">&#2325;&#2375;&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2375;&#2339;&#2369;&#2327;&#2379;&#2346;&#2366;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n"],
                    solution_en: "<p>17.<span style=\"font-family: Cambria Math;\">(c)&nbsp;</span><strong>R Venkataramani</strong><span style=\"font-weight: 400;\">. The Attorney General (Article 76, Part V) is appointed by the President on the advice of the government. </span><strong>Mukul Rohatgi</strong><span style=\"font-weight: 400;\"> (June 2014 &ndash; June 2017),&nbsp; </span><strong>K.K. Venugopal </strong><span style=\"font-weight: 400;\">(June 2017 &ndash; September 2022). </span><strong>Ram Jethmalani</strong><span style=\"font-weight: 400;\"> was an Indian lawyer and politician.&nbsp;</span></p>\n",
                    solution_hi: "<p>17.<span style=\"font-family: Cambria Math;\">(c) <strong>R &#2357;&#2375;&#2306;&#2325;&#2335;&#2352;&#2350;&#2339;&#2367; &#2404; </strong><span style=\"font-weight: 400;\">&#2350;&#2361;&#2366;&#2344;&#2381;&#2351;&#2366;&#2351;&#2357;&#2366;&#2342;&#2368;&nbsp; (&#2349;&#2366;&#2327; V &#2325;&#2366; &#2309;&#2344;&#2369;&#2330;&#2381;&#2331;&#2375;&#2342; 76) &#2360;&#2352;&#2325;&#2366;&#2352; &#2325;&#2368; &#2360;&#2354;&#2366;&#2361; &#2346;&#2352; &#2352;&#2366;&#2359;&#2381;&#2335;&#2381;&#2352;&#2346;&#2340;&#2367; &#2342;&#2381;&#2357;&#2366;&#2352;&#2366; &#2344;&#2367;&#2351;&#2369;&#2325;&#2381;&#2340; &#2325;&#2367;&#2351;&#2366; &#2332;&#2366;&#2340;&#2366; &#2361;&#2376;&#2404; </span><strong>&#2350;&#2369;&#2325;&#2369;&#2354; &#2352;&#2379;&#2361;&#2340;&#2327;&#2368;</strong><span style=\"font-weight: 400;\"> (&#2332;&#2370;&#2344; 2014 - &#2332;&#2370;&#2344; 2017),</span><strong> &#2325;&#2375;.&#2325;&#2375;. &#2357;&#2375;&#2339;&#2369;&#2327;&#2379;&#2346;&#2366;&#2354; </strong><span style=\"font-weight: 400;\">(&#2332;&#2370;&#2344; 2017 - &#2360;&#2367;&#2340;&#2306;&#2348;&#2352; 2022)&#2404; </span><strong>&#2352;&#2366;&#2350; &#2332;&#2375;&#2336;&#2350;&#2354;&#2366;&#2344;&#2368;</strong><span style=\"font-weight: 400;\"> &#2319;&#2325; &#2349;&#2366;&#2352;&#2340;&#2368;&#2351; &#2357;&#2325;&#2368;&#2354; &#2324;&#2352; &#2352;&#2366;&#2332;&#2344;&#2368;&#2340;&#2367;&#2332;&#2381;&#2334; &#2341;&#2375;&#2404;</span></span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "18",
                    section: "18",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">18.</span><span style=\"font-family: Cambria Math;\"> Which among the following lakes is also known as the \"Srinagar\'s Jewel\"?</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">18. </span><span style=\"font-family: Cambria Math;\">&#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2367;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2333;&#2368;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> \"</span><span style=\"font-family: Cambria Math;\">&#2358;&#2381;&#2352;&#2368;&#2344;&#2327;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2310;&#2349;&#2370;&#2359;&#2339;</span><span style=\"font-family: Cambria Math;\"> (Srinagar\'s Jewel)\" </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2344;&#2366;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2349;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2332;&#2366;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2332;&#2366;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> ?</span><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                    options_en: ["<p>Tsomgo</p>\n", "<p>Vembanad</p>\n", 
                                "<p>Dal</p>\n", "<p>Loktak</p>\n"],
                    options_hi: ["<p><span style=\"font-family: Cambria Math;\">&#2340;&#2381;&#2360;&#2379;&#2306;&#2327;&#2350;&#2379;</span><span style=\"font-family: Cambria Math;\"> (Tsomgo) </span></p>\n", "<p><span style=\"font-family: Cambria Math;\">&#2357;&#2375;&#2350;&#2381;&#2348;&#2366;&#2344;&#2366;&#2337;</span><span style=\"font-family: Cambria Math;\"> (Vembanad) </span></p>\n",
                                "<p><span style=\"font-family: Cambria Math;\">&#2337;&#2354;</span><span style=\"font-family: Cambria Math;\"> (Dal) </span></p>\n", "<p><span style=\"font-family: Cambria Math;\">&#2354;&#2379;&#2325;&#2335;&#2325;</span><span style=\"font-family: Cambria Math;\"> (Loktak) </span></p>\n"],
                    solution_en: "<p>18.(c) <strong>Dal lake.</strong><span style=\"font-weight: 400;\"> On its banks Tulip Garden (Asia\'s largest), Mughal Garden, Shalimar Bagh and Nishat Bagh are situated. </span><strong>Lakes in India</strong><span style=\"font-weight: 400;\"> - </span><strong>vembanad Lake</strong><span style=\"font-weight: 400;\"> (Kerala), Chilika Lake (Odisha), Shivaji Sagar Lake (Maharashtra), Indira Sagar lake (Madhya Pradesh), Pangong Lake (Ladakh), Pulicat Lake (Andhra Pradesh),Nagarjuna Sagar Lake (Telangana), </span><strong>Loktak Lake</strong><span style=\"font-weight: 400;\"> (Manipur), Wular lake (Jammu and Kashmir).</span></p>\n",
                    solution_hi: "<p>18.(c)&nbsp;<strong>&#2337;&#2354;</strong><span style=\"font-weight: 400;\">&#2404; </span><span style=\"font-weight: 400;\">&nbsp;</span><span style=\"font-weight: 400;\">&#2311;&#2360;&#2325;&#2375; &#2325;&#2367;&#2344;&#2366;&#2352;&#2375; &#2335;&#2381;&#2351;&#2370;&#2354;&#2367;&#2346; &#2327;&#2366;&#2352;&#2381;&#2337;&#2344; (&#2319;&#2358;&#2367;&#2351;&#2366; &#2325;&#2366; &#2360;&#2348;&#2360;&#2375; &#2348;&#2337;&#2364;&#2366;), &#2350;&#2369;&#2327;&#2354; &#2327;&#2366;&#2352;&#2381;&#2337;&#2344;, &#2358;&#2366;&#2354;&#2368;&#2350;&#2366;&#2352; &#2348;&#2366;&#2327; &#2324;&#2352; &#2344;&#2367;&#2358;&#2366;&#2340; &#2348;&#2366;&#2327; &#2361;&#2376;&#2306;&#2404; </span><strong>&#2349;&#2366;&#2352;&#2340; &#2350;&#2375;&#2306; &#2333;&#2368;&#2354;&#2375;&#2306;</strong><span style=\"font-weight: 400;\"> - </span><strong>&#2357;&#2375;&#2350;&#2381;&#2348;&#2344;&#2366;&#2337; &#2333;&#2368;&#2354;</strong><span style=\"font-weight: 400;\"> (&#2325;&#2375;&#2352;&#2354;), &#2330;&#2367;&#2354;&#2381;&#2325;&#2366; &#2333;&#2368;&#2354; (&#2323;&#2337;&#2367;&#2358;&#2366;), &#2358;&#2367;&#2357;&#2366;&#2332;&#2368; &#2360;&#2366;&#2327;&#2352; &#2333;&#2368;&#2354; (&#2350;&#2361;&#2366;&#2352;&#2366;&#2359;&#2381;&#2335;&#2381;&#2352;), &#2311;&#2306;&#2342;&#2367;&#2352;&#2366; &#2360;&#2366;&#2327;&#2352; &#2333;&#2368;&#2354; (&#2350;&#2343;&#2381;&#2351; &#2346;&#2381;&#2352;&#2342;&#2375;&#2358;), &#2346;&#2376;&#2306;&#2327;&#2379;&#2306;&#2327; &#2333;&#2368;&#2354; (&#2354;&#2342;&#2381;&#2342;&#2366;&#2326;), &#2346;&#2369;&#2354;&#2368;&#2325;&#2335; &#2333;&#2368;&#2354; (&#2310;&#2306;&#2343;&#2381;&#2352; &#2346;&#2381;&#2352;&#2342;&#2375;&#2358;), &#2344;&#2366;&#2327;&#2366;&#2352;&#2381;&#2332;&#2369;&#2344; &#2360;&#2366;&#2327;&#2352; &#2333;&#2368;&#2354; (&#2340;&#2375;&#2354;&#2306;&#2327;&#2366;&#2344;&#2366;) , </span><strong>&#2354;&#2379;&#2325;&#2335;&#2325; &#2333;&#2368;&#2354;</strong><span style=\"font-weight: 400;\"> (&#2350;&#2339;&#2367;&#2346;&#2369;&#2352;), &#2357;&#2369;&#2354;&#2352; &#2333;&#2368;&#2354; (&#2332;&#2350;&#2381;&#2350;&#2370; &#2324;&#2352; &#2325;&#2358;&#2381;&#2350;&#2368;&#2352;)&#2404;</span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "19",
                    section: "18",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">19.</span><span style=\"font-family: Cambria Math;\"> Somewhat before </span><span style=\"font-family: Cambria Math;\">the time of Mauryan empire, about_______ years ago, emperors in China began building the Great Wall.</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">19. </span><span style=\"font-family: Cambria Math;\">&#2350;&#2380;&#2352;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2366;&#2350;&#2381;&#2352;&#2366;&#2332;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2369;&#2331;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2350;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2370;&#2352;&#2381;&#2357;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2354;&#2327;&#2349;&#2327;</span><span style=\"font-family: Cambria Math;\"> _________ </span><span style=\"font-family: Cambria Math;\">&#2357;&#2352;&#2381;&#2359;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2361;&#2354;&#2375;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2330;&#2368;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2350;&#2381;&#2352;&#2366;&#2335;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2367;&#2358;&#2366;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2368;&#2357;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> (Great Wa</span><span style=\"font-family: Cambria Math;\">ll) </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2344;&#2367;&#2352;&#2381;&#2350;&#2366;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2358;&#2369;&#2352;&#2370;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2341;&#2366;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                    options_en: ["<p>1200</p>\n", "<p>3500</p>\n", 
                                "<p>2400</p>\n", "<p>600</p>\n"],
                    options_hi: ["<p>1200</p>\n", "<p>3500</p>\n",
                                "<p>2400</p>\n", "<p>600</p>\n"],
                    solution_en: "<p>19.(c)<span style=\"font-family: Cambria Math;\"> <strong>&nbsp;2400. </strong><span style=\"font-weight: 400;\">Emperor </span><strong>Qin Shi Huang</strong><span style=\"font-weight: 400;\"> ordered construction of the Great Wall around</span><strong> 221 B.C.&nbsp; </strong><span style=\"font-weight: 400;\">It was meant to protect the northern frontier of the empire from pastoral people. The </span><strong>Mauryan Empire </strong><span style=\"font-weight: 400;\">(322 BCE - 185 BCE) was established in </span><strong>322 B.C</strong><span style=\"font-weight: 400;\">. by the great king </span><strong>Chandragupta Maurya</strong><span style=\"font-weight: 400;\">.</span></span></p>\n",
                    solution_hi: "<p>19.(c) <strong>2400 &#2404; </strong><span style=\"font-weight: 400;\">&#2360;&#2350;&#2381;&#2352;&#2366;&#2335;</span><strong> &#2325;&#2367;&#2344; &#2358;&#2367; &#2361;&#2369;&#2310;&#2306;&#2327; </strong><span style=\"font-weight: 400;\">&#2344;&#2375; </span><strong>221 &#2312;&#2360;&#2366; &#2346;&#2370;&#2352;&#2381;&#2357; </strong><span style=\"font-weight: 400;\">&#2325;&#2375; &#2310;&#2360;&#2346;&#2366;&#2360; &#2350;&#2361;&#2366;&#2344; &#2342;&#2368;&#2357;&#2366;&#2352; &#2325;&#2375; &#2344;&#2367;&#2352;&#2381;&#2350;&#2366;&#2339; &#2325;&#2366; &#2310;&#2342;&#2375;&#2358; &#2342;&#2367;&#2351;&#2366; &#2341;&#2366;&#2404; &#2311;&#2360;&#2325;&#2366; &#2313;&#2342;&#2381;&#2342;&#2375;&#2358;&#2381;&#2351; &#2360;&#2366;&#2350;&#2381;&#2352;&#2366;&#2332;&#2381;&#2351; &#2325;&#2368; &#2313;&#2340;&#2381;&#2340;&#2352;&#2368; &#2360;&#2368;&#2350;&#2366; &#2325;&#2379; &#2310;&#2325;&#2381;&#2352;&#2366;&#2350;&#2325;&nbsp; &#2354;&#2379;&#2327;&#2379;&#2306; &#2360;&#2375; &#2348;&#2330;&#2366;&#2344;&#2366; &#2341;&#2366;&#2404; </span><strong>&#2350;&#2380;&#2352;&#2381;&#2351; &#2360;&#2366;&#2350;&#2381;&#2352;&#2366;&#2332;&#2381;&#2351;</strong><span style=\"font-weight: 400;\"> (322 &#2312;&#2360;&#2366; &#2346;&#2370;&#2352;&#2381;&#2357; - 185 &#2312;&#2360;&#2366; &#2346;&#2370;&#2352;&#2381;&#2357;) &#2325;&#2368; &#2360;&#2381;&#2341;&#2366;&#2346;&#2344;&#2366; &#2350;&#2361;&#2366;&#2344; &#2352;&#2366;&#2332;&#2366; </span><strong>&#2330;&#2306;&#2342;&#2381;&#2352;&#2327;&#2369;&#2346;&#2381;&#2340; &#2350;&#2380;&#2352;&#2381;&#2351;</strong><span style=\"font-weight: 400;\"> &#2342;&#2381;&#2357;&#2366;&#2352;&#2366;</span><strong> 322 &#2312;&#2360;&#2366; &#2346;&#2370;&#2352;&#2381;&#2357;</strong><span style=\"font-weight: 400;\"> &#2350;&#2375;&#2306; &#2361;&#2369;&#2312; &#2341;&#2368;&#2404; </span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "20",
                    section: "18",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">20. </span><span style=\"font-family: Cambria Math;\">Shri Man</span><span style=\"font-family: Cambria Math;\">i Prasad is associated with which form of music?</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">20.</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2358;&#2381;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2339;&#2367;&#2346;&#2381;&#2352;&#2360;&#2366;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2306;&#2348;&#2306;&#2343;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2367;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2381;&#2352;&#2325;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2306;&#2327;&#2368;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2358;&#2376;&#2354;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> ? </span></p>\n",
                    options_en: ["<p>Fusion Music</p>\n", "<p>Folk Music</p>\n", 
                                "<p>Carnatic vocal music</p>\n", "<p>Hindustani vocal music</p>\n"],
                    options_hi: ["<p><span style=\"font-family: Cambria Math;\">&#2360;&#2350;&#2381;&#2350;&#2367;&#2358;&#2381;&#2352;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2306;&#2327;&#2368;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><span style=\"font-family: Cambria Math;\">&#2354;&#2379;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2306;&#2327;&#2368;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                                "<p><span style=\"font-family: Cambria Math;\">&#2325;&#2352;</span><span style=\"font-family: Cambria Math;\">&#2381;&#2344;&#2366;&#2335;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2327;&#2366;&#2351;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2306;&#2327;&#2368;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><span style=\"font-family: Cambria Math;\">&#2361;&#2367;&#2344;&#2381;&#2342;&#2369;&#2360;&#2381;&#2340;&#2366;&#2344;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2327;&#2366;&#2351;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2306;&#2327;&#2368;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n"],
                    solution_en: "<p>20.(d) <strong>Hindustani vocal music. </strong><span style=\"font-weight: 400;\">Pandit Mani Prasad (1929 - 2023) was an Indian classical vocalist from the </span><strong>Kirana gharana</strong><span style=\"font-weight: 400;\"> (singing style). He was awarded Sangeet Natak Akademi Award in 2019 . </span><strong>Hindustani music</strong><span style=\"font-weight: 400;\"> is the classical music of the Northern parts of the Indian subcontinent. </span><strong>Carnatic Music</strong><span style=\"font-weight: 400;\"> originated from Southern India, the city of Hyderabad and other Dravidian-speaking regions of India.</span></p>\n",
                    solution_hi: "<p>20.(d)&nbsp;<strong>&#2361;&#2367;&#2344;&#2381;&#2342;&#2369;&#2360;&#2381;&#2340;&#2366;&#2344;&#2368; </strong><strong>&#2327;&#2366;&#2351;&#2344; </strong><strong>&#2360;&#2306;&#2327;&#2368;&#2340;&#2404; </strong><span style=\"font-weight: 400;\">&#2346;&#2306;&#2337;&#2367;&#2340; &#2350;&#2339;&#2367; &#2346;&#2381;&#2352;&#2360;&#2366;&#2342; (1929 - 2023) &#2325;&#2367;&#2352;&#2366;&#2344;&#2366; &#2328;&#2352;&#2366;&#2344;&#2375; (&#2327;&#2366;&#2351;&#2344; &#2358;&#2376;&#2354;&#2368;) &#2325;&#2375; &#2319;&#2325; &#2349;&#2366;&#2352;&#2340;&#2368;&#2351; &#2358;&#2366;&#2360;&#2381;&#2340;&#2381;&#2352;&#2368;&#2351; &#2327;&#2366;&#2351;&#2325; &#2341;&#2375;&#2404; &#2313;&#2344;&#2381;&#2361;&#2375;&#2306; 2019 &#2350;&#2375;&#2306; &#2360;&#2306;&#2327;&#2368;&#2340; &#2344;&#2366;&#2335;&#2325; &#2309;&#2325;&#2366;&#2342;&#2350;&#2368; &#2346;&#2369;&#2352;&#2360;&#2381;&#2325;&#2366;&#2352; &#2360;&#2375; &#2360;&#2350;&#2381;&#2350;&#2366;&#2344;&#2367;&#2340; &#2325;&#2367;&#2351;&#2366; &#2327;&#2351;&#2366;&#2404; </span><strong>&nbsp;&#2361;&#2367;&#2306;&#2342;&#2369;&#2360;&#2381;&#2340;&#2366;&#2344;&#2368; &#2360;&#2306;&#2327;&#2368;&#2340; </strong><span style=\"font-weight: 400;\">&#2349;&#2366;&#2352;&#2340;&#2368;&#2351; &#2313;&#2346;&#2350;&#2361;&#2366;&#2342;&#2381;&#2357;&#2368;&#2346; &#2325;&#2375; &#2313;&#2340;&#2381;&#2340;&#2352;&#2368; &#2349;&#2366;&#2327;&#2379;&#2306; &#2325;&#2366; &#2358;&#2366;&#2360;&#2381;&#2340;&#2381;&#2352;&#2368;&#2351; &#2360;&#2306;&#2327;&#2368;&#2340; &#2361;&#2376;&#2404; </span><strong>&#2325;&#2352;&#2381;&#2344;&#2366;&#2335;&#2325; &#2360;&#2306;&#2327;&#2368;&#2340; </strong><span style=\"font-weight: 400;\">&#2325;&#2368; &#2313;&#2340;&#2381;&#2346;&#2340;&#2381;&#2340;&#2367; &#2357;&#2367;&#2358;&#2375;&#2359; &#2352;&#2370;&#2346; &#2360;&#2375; &#2361;&#2376;&#2342;&#2352;&#2366;&#2348;&#2366;&#2342; &#2358;&#2361;&#2352; &#2324;&#2352; &#2342;&#2325;&#2381;&#2359;&#2367;&#2339;&#2368; &#2349;&#2366;&#2352;&#2340; &#2325;&#2375; &#2309;&#2344;&#2381;&#2351; &#2342;&#2381;&#2352;&#2357;&#2367;&#2337;&#2364; &#2349;&#2366;&#2359;&#2368; &#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;&#2379;&#2306; &#2360;&#2375; &#2361;&#2369;&#2312;&#2404;</span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "21",
                    section: "18",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">21.</span><span style=\"font-family: Cambria Math;\">Whi</span><span style=\"font-family: Cambria Math;\">ch among the following peaks is the highest peak of Jharkhand?</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">21.</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2380;&#2344;</span><span style=\"font-family: Cambria Math;\">-</span><span style=\"font-family: Cambria Math;\">&#2360;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2358;&#2367;&#2326;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2333;&#2366;&#2352;&#2326;&#2306;&#2337;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2348;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2314;&#2305;&#2330;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2358;&#2367;&#2326;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">? </span></p>\n",
                    options_en: ["<p>Kangto</p>\n", "<p>Girnar</p>\n", 
                                "<p>Anamudi</p>\n", "<p>Parasnath</p>\n"],
                    options_hi: ["<p><span style=\"font-family: Cambria Math;\">&#2325;&#2366;&#2306;&#2327;&#2381;&#2335;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><span style=\"font-family: Cambria Math;\">&#2327;&#2367;&#2352;&#2344;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                                "<p><span style=\"font-family: Cambria Math;\">&#2309;&#2344;&#2366;&#2350;&#2369;&#2337;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><span style=\"font-family: Cambria Math;\">&#2346;&#2366;&#2352;&#2360;&#2344;&#2366;&#2341;</span></p>\n"],
                    solution_en: "<p>21.<span style=\"font-family: Cambria Math;\">(d)&nbsp;</span><strong>Parasnath.</strong><span style=\"font-weight: 400;\"> It is named after Lord Parshvanath (23rd Jain Tirthankara) in Giridih district (Jharkhand)</span><strong>. Anamudi </strong><span style=\"font-weight: 400;\">peak (Kerala) and </span><strong>Kangto </strong><span style=\"font-weight: 400;\">peak (Arunachal Pradesh). </span><strong>State and Highest Peak </strong><span style=\"font-weight: 400;\">- Odisha (Deomali Peak), Gujarat (</span><strong>Girnar</strong><span style=\"font-weight: 400;\"> Peak), Rajasthan (Guru Shikhar), Andhra Pradesh (Arma Konda), Haryana (Karah Peak), Karnataka (Mullayanagiri Peak).</span></p>\n",
                    solution_hi: "<p>21.<span style=\"font-family: Cambria Math;\">(d)&nbsp;</span><strong>&#2346;&#2366;&#2352;&#2360;&#2344;&#2366;&#2341; </strong><span style=\"font-weight: 400;\">&#2404; &#2311;&#2360;&#2325;&#2366; &#2344;&#2366;&#2350; &#2327;&#2367;&#2352;&#2367;&#2337;&#2368;&#2361; &#2332;&#2367;&#2354;&#2375; (&#2333;&#2366;&#2352;&#2326;&#2306;&#2337;) &#2350;&#2375;&#2306; &#2349;&#2327;&#2357;&#2366;&#2344; &#2346;&#2366;&#2352;&#2381;&#2358;&#2381;&#2357;&#2344;&#2366;&#2341; (23&#2357;&#2375;&#2306; &#2340;&#2368;&#2352;&#2381;&#2341;&#2306;&#2325;&#2352;) &#2325;&#2375; &#2344;&#2366;&#2350; &#2346;&#2352; &#2352;&#2326;&#2366; &#2327;&#2351;&#2366; &#2361;&#2376;&#2404;</span><strong> &#2309;&#2344;&#2366;&#2350;&#2369;&#2337;&#2368; </strong><span style=\"font-weight: 400;\">&#2330;&#2379;&#2335;&#2368; (&#2325;&#2375;&#2352;&#2354;) &#2324;&#2352; </span><strong>&#2325;&#2366;&#2306;&#2327;&#2335;&#2379; </strong><span style=\"font-weight: 400;\">&#2330;&#2379;&#2335;&#2368; (&#2309;&#2352;&#2369;&#2339;&#2366;&#2330;&#2354; &#2346;&#2381;&#2352;&#2342;&#2375;&#2358;)</span><strong>&#2404; &#2352;&#2366;&#2332;&#2381;&#2351; &#2324;&#2352; &#2360;&#2348;&#2360;&#2375; &#2314;&#2305;&#2330;&#2368; &#2330;&#2379;&#2335;&#2368; -</strong><span style=\"font-weight: 400;\"> &#2323;&#2337;&#2367;&#2358;&#2366; (&#2342;&#2375;&#2357;&#2350;&#2366;&#2354;&#2368; &#2330;&#2379;&#2335;&#2368;), &#2327;&#2369;&#2332;&#2352;&#2366;&#2340; (&#2327;&#2367;&#2352;&#2344;&#2366;&#2352; &#2330;&#2379;&#2335;&#2368;), &#2352;&#2366;&#2332;&#2360;&#2381;&#2341;&#2366;&#2344; (&#2327;&#2369;&#2352;&#2369; &#2358;&#2367;&#2326;&#2366;), &#2310;&#2306;&#2343;&#2381;&#2352; &#2346;&#2381;&#2352;&#2342;&#2375;&#2358; (&#2309;&#2352;&#2350;&#2366; &#2325;&#2379;&#2306;&#2337;&#2366;), &#2361;&#2352;&#2367;&#2351;&#2366;&#2339;&#2366; (&#2325;&#2352;&#2366;&#2361; &#2330;&#2379;&#2335;&#2368; ), &#2325;&#2352;&#2381;&#2344;&#2366;&#2335;&#2325;(&#2350;&#2369;&#2354;&#2381;&#2354;&#2351;&#2344;&#2327;&#2367;&#2352;&#2368; &#2330;&#2379;&#2335;&#2368;)</span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "22",
                    section: "18",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">22.</span><span style=\"font-family: Cambria Math;\"> Which pass connects the Kullu Valley with the Lahaul and Spiti Valleys of Himachal Pradesh, India? </span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">22. </span><span style=\"font-family: Cambria Math;\">&#2325;&#2380;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2352;&#2381;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2369;&#2354;&#2381;&#2354;&#2370;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2328;&#2366;&#2335;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2349;&#2366;&#2352;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2367;&#2350;&#2366;&#2330;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2381;&#2352;&#2342;&#2375;&#2358;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2354;&#2366;&#2361;&#2380;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2381;&#2346;&#2368;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2328;&#2366;&#2335;&#2367;&#2351;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2332;&#2379;&#2337;&#2364;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                    options_en: ["<p>Banihal Pass</p>\n", "<p>Thamarassery Pass</p>\n", 
                                "<p>Rohtang Pass</p>\n", "<p>Lipulekh Pass</p>\n"],
                    options_hi: ["<p><span style=\"font-family: Cambria Math;\">&#2348;&#2344;&#2367;&#2361;&#2366;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2352;&#2381;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><span style=\"font-family: Cambria Math;\">&#2341;&#2350;&#2366;&#2352;&#2360;&#2381;&#2360;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2352;&#2381;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                                "<p><span style=\"font-family: Cambria Math;\">&#2352;&#2379;&#2361;&#2340;&#2366;&#2306;&#2327;</span><span style=\"font-family: Cambria Math;\"> &#2342;&#2352;&#2381;&#2352;&#2366;</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">&#2354;&#2367;&#2346;&#2369;&#2354;&#2375;&#2326;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2352;&#2381;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n"],
                    solution_en: "<p>22.(c) <strong>&nbsp;Rohtang Pass: </strong><span style=\"font-weight: 400;\">It lies on the watershed between the Chenab and Beas basins. </span><strong>Mountain Passes in India</strong><span style=\"font-weight: 400;\"> - Nathu La (Sikkim), Shipki La (Himachal Pradesh), Zojila Pass (Kashmir, Ladakh), Lipulekh Pass (Uttarakhand), Thamarassery Pass (Wayanad Kerala), Banihal Pass (Jammu and Kashmir).</span></p>\n",
                    solution_hi: "<p>22.(c)&nbsp;<strong>&#2352;&#2379;&#2361;&#2340;&#2366;&#2306;&#2327; &#2342;&#2352;&#2381;&#2352;&#2366;&#2404; </strong><span style=\"font-weight: 400;\">&#2351;&#2361; &#2342;&#2352;&#2381;&#2352;&#2366; &#2330;&#2367;&#2344;&#2366;&#2348; &#2324;&#2352; &#2348;&#2381;&#2351;&#2366;&#2360; &#2328;&#2366;&#2335;&#2367;&#2351;&#2379;&#2306; &#2325;&#2375; &#2348;&#2368;&#2330; &#2332;&#2354;&#2360;&#2306;&#2349;&#2352; &#2346;&#2352; &#2360;&#2381;&#2341;&#2367;&#2340; &#2361;&#2376;&#2404; </span><strong>&#2349;&#2366;&#2352;&#2340; &#2350;&#2375;&#2306; &#2346;&#2352;&#2381;&#2357;&#2340;&#2368;&#2351; &#2342;&#2352;&#2381;&#2352;&#2375; </strong><span style=\"font-weight: 400;\">- &#2344;&#2366;&#2341;&#2370; &#2354;&#2366; (&#2360;&#2367;&#2325;&#2381;&#2325;&#2367;&#2350;), &#2358;&#2367;&#2346;&#2325;&#2368; &#2354;&#2366; (&#2361;&#2367;&#2350;&#2366;&#2330;&#2354; &#2346;&#2381;&#2352;&#2342;&#2375;&#2358;), &#2332;&#2364;&#2379;&#2332;&#2367;&#2354;&#2366; &#2342;&#2352;&#2381;&#2352;&#2366; (&#2325;&#2358;&#2381;&#2350;&#2368;&#2352;, &#2354;&#2342;&#2381;&#2342;&#2366;&#2326;), &#2354;&#2367;&#2346;&#2369;&#2354;&#2375;&#2326; &#2342;&#2352;&#2381;&#2352;&#2366; (&#2313;&#2340;&#2381;&#2340;&#2352;&#2366;&#2326;&#2306;&#2337;), &#2341;&#2350;&#2366;&#2352;&#2366;&#2360;&#2381;&#2360;&#2375;&#2352;&#2368; &#2342;&#2352;&#2381;&#2352;&#2366; (&#2357;&#2366;&#2351;&#2344;&#2366;&#2337; &#2325;&#2375;&#2352;&#2354;), &#2348;&#2344;&#2367;&#2361;&#2366;&#2354; &#2342;&#2352;&#2381;&#2352;&#2366; (&#2332;&#2350;&#2381;&#2350;&#2370; &#2324;&#2352; &#2325;&#2358;&#2381;&#2350;&#2368;&#2352;)&#2404;</span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "23",
                    section: "18",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">23.</span><span style=\"font-family: Cambria Math;\"> Who among the following was the architect of the Integrated Guided Missile Development Program (IGMDP) in India?</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">23. </span><span style=\"font-family: Cambria Math;\">&#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2380;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2349;&#2366;&#2352;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2311;&#2306;&#2335;&#2368;&#2327;&#2381;&#2352;&#2375;&#2335;&#2375;&#2337;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2327;&#2366;&#2311;&#2337;&#2375;&#2337;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2367;&#2360;&#2366;&#2311;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2337;&#2375;&#2357;&#2354;&#2346;&#2350;&#2375;&#2306;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2381;&#2352;&#2379;&#2327;&#2381;&#2352;&#2366;&#2350;</span><span style=\"font-family: Cambria Math;\"> (IGMDP) </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2366;&#2360;&#2381;&#2340;&#2369;&#2325;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2341;&#2366;</span><span style=\"font-family: Cambria Math;\">? </span></p>\n",
                    options_en: ["<p>Har Gobind Khorana</p>\n", "<p>Vikram Sarabhai</p>\n", 
                                "<p>A. P. J. Abdul Kalam</p>\n", "<p>Homi J. Bhabha</p>\n"],
                    options_hi: ["<p><span style=\"font-family: Cambria Math;\">&#2361;&#2352;&#2327;&#2379;&#2348;&#2367;&#2306;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2326;&#2369;&#2352;&#2366;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><span style=\"font-family: Cambria Math;\">&#2357;&#2367;&#2325;&#2381;&#2352;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2366;&#2352;&#2366;&#2349;&#2366;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                                "<p><span style=\"font-family: Cambria Math;\">&#2319;</span><span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Cambria Math;\">&#2346;&#2368;</span><span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Cambria Math;\">&#2332;&#2375;</span><span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Cambria Math;\">&#2309;&#2348;&#2381;&#2342;&#2369;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2354;&#2366;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><span style=\"font-family: Cambria Math;\">&#2361;&#2379;&#2350;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2332;&#2375;</span><span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Cambria Math;\">&#2349;&#2366;&#2349;&#2366;</span></p>\n"],
                    solution_en: "<p>23.(c) <strong>A. P. J. Abdul Kalam.</strong> IGMDP was an Indian Ministry of Defence programme to research and develop a comprehensive range of missiles. The project started in <strong>1982 - 1983</strong> (complete in <strong>2008</strong>)&nbsp; under the leadership of Dr APJ Abdul Kalam (<strong>missile man of India</strong>). Prithvi, Agni, Trishul, Akash, Nag missiles have been developed under this.</p>\n",
                    solution_hi: "<p>23.(c) <strong>A .P. J. &#2309;&#2348;&#2381;&#2342;&#2369;&#2354; &#2325;&#2354;&#2366;&#2350;&#2404; </strong>IGMDP &#2350;&#2367;&#2360;&#2366;&#2311;&#2354;&#2379;&#2306; &#2325;&#2368; &#2357;&#2381;&#2351;&#2366;&#2346;&#2325; &#2358;&#2381;&#2352;&#2375;&#2339;&#2368; &#2325;&#2375; &#2309;&#2344;&#2369;&#2360;&#2306;&#2343;&#2366;&#2344; &#2324;&#2352; &#2357;&#2367;&#2325;&#2366;&#2360; &#2325;&#2375; &#2354;&#2367;&#2319; &#2319;&#2325; &#2349;&#2366;&#2352;&#2340;&#2368;&#2351; &#2352;&#2325;&#2381;&#2359;&#2366; &#2350;&#2306;&#2340;&#2381;&#2352;&#2366;&#2354;&#2351; &#2325;&#2368; &#2346;&#2352;&#2367;&#2351;&#2379;&#2332;&#2344;&#2366; &#2341;&#2368;&#2404; &#2351;&#2361; &#2346;&#2352;&#2367;&#2351;&#2379;&#2332;&#2344;&#2366; <strong>1982 - 1983 </strong>&#2350;&#2375;&#2306; (2008 &#2350;&#2375;&#2306; &#2346;&#2370;&#2352;&#2381;&#2339;) &#2337;&#2377;&nbsp; A. P. J. &#2309;&#2348;&#2381;&#2342;&#2369;&#2354; &#2325;&#2354;&#2366;&#2350; (<strong>&#2349;&#2366;&#2352;&#2340; &#2325;&#2375; &#2350;&#2367;&#2360;&#2366;&#2311;&#2354; &#2350;&#2376;&#2344;</strong>) &#2325;&#2375; &#2344;&#2375;&#2340;&#2371;&#2340;&#2381;&#2357; &#2350;&#2375;&#2306; &#2358;&#2369;&#2352;&#2370; &#2361;&#2369;&#2312; &#2341;&#2368;&#2404; &#2311;&#2360;&#2325;&#2375; &#2340;&#2361;&#2340; &#2346;&#2371;&#2341;&#2381;&#2357;&#2368;, &#2309;&#2327;&#2381;&#2344;&#2367;, &#2340;&#2381;&#2352;&#2367;&#2358;&#2370;&#2354;, &#2310;&#2325;&#2366;&#2358;, &#2344;&#2366;&#2327; &#2350;&#2367;&#2360;&#2366;&#2311;&#2354;&#2375;&#2306; &#2357;&#2367;&#2325;&#2360;&#2367;&#2340; &#2325;&#2368; &#2327;&#2312; &#2361;&#2376;&#2306;&#2404;</p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "24",
                    section: "18",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">24.</span><span style=\"font-family: Cambria Math;\"> Chikungunya is an infection caused by_______ mosquito.</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">24.</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2330;&#2367;&#2325;&#2344;&#2327;&#2369;&#2344;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\">____________ </span><span style=\"font-family: Cambria Math;\">&#2350;&#2330;&#2381;&#2331;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;&#2352;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2379;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2366;&#2354;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2306;&#2325;&#2381;&#2352;&#2350;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                    options_en: ["<p>Culex</p>\n", "<p>Mansoni<span style=\"font-family: Cambria Math;\">a</span></p>\n", 
                                "<p>Aedes</p>\n", "<p>Anopheles</p>\n"],
                    options_hi: ["<p><span style=\"font-family: Cambria Math;\">&#2325;&#2381;&#2351;&#2370;&#2354;&#2375;&#2325;&#2381;&#2360;</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">&#2350;&#2376;&#2344;&#2360;&#2379;&#2344;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                                "<p><span style=\"font-family: Cambria Math;\">&#2319;&#2337;&#2368;&#2332;</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">&#2319;&#2344;&#2379;&#2347;&#2367;&#2354;&#2368;&#2332;&#2364;</span></p>\n"],
                    solution_en: "<p>24.<span style=\"font-family: Cambria Math;\">(c)&nbsp;<strong>Aedes:</strong><span style=\"font-weight: 400;\"> It is a species of mosquito that causes dengue, yellow fever, chikungunya and zika virus.</span><strong> Anopheles Mosquito</strong><span style=\"font-weight: 400;\"> is a species of mosquito that causes malaria. </span><strong>Culex Mosquito</strong><span style=\"font-weight: 400;\"> is a species of mosquito that spreads viruses that cause Japanese encephalitis, West Nile virus.</span></span></p>\n",
                    solution_hi: "<p>24.<span style=\"font-family: Cambria Math;\">(c)</span><span style=\"font-family: Cambria Math;\"> <strong>&#2319;&#2337;&#2368;&#2332;</strong><span style=\"font-weight: 400;\">&#2404; &#2351;&#2361; &#2350;&#2330;&#2381;&#2331;&#2352; &#2325;&#2368; &#2319;&#2325; &#2346;&#2381;&#2352;&#2332;&#2366;&#2340;&#2367; &#2361;&#2376; &#2332;&#2379; &#2337;&#2375;&#2306;&#2327;&#2370;, &#2346;&#2368;&#2354;&#2366; &#2348;&#2369;&#2326;&#2366;&#2352;, &#2330;&#2367;&#2325;&#2344;&#2327;&#2369;&#2344;&#2367;&#2351;&#2366; &#2324;&#2352; &#2332;&#2368;&#2325;&#2366; &#2357;&#2366;&#2351;&#2352;&#2360; &#2325;&#2366; &#2325;&#2366;&#2352;&#2339; &#2348;&#2344;&#2340;&#2366; &#2361;&#2376;&#2404; </span><strong>&#2319;&#2344;&#2379;&#2347;&#2367;&#2354;&#2368;&#2332;&nbsp; &#2350;&#2330;&#2381;&#2331;&#2352; </strong><span style=\"font-weight: 400;\">&#2319;&#2325; &#2346;&#2381;&#2352;&#2332;&#2366;&#2340;&#2367; &#2361;&#2376; &#2332;&#2379; &#2350;&#2354;&#2375;&#2352;&#2367;&#2351;&#2366; &#2325;&#2366; &#2325;&#2366;&#2352;&#2339; &#2348;&#2344;&#2340;&#2368; &#2361;&#2376;&#2404; </span><strong>&#2325;&#2381;&#2351;&#2370;&#2354;&#2375;&#2325;&#2381;&#2360; &#2350;&#2330;&#2381;&#2331;&#2352; </strong><span style=\"font-weight: 400;\">&#2350;&#2330;&#2381;&#2331;&#2352; &#2325;&#2368; &#2319;&#2325; &#2346;&#2381;&#2352;&#2332;&#2366;&#2340;&#2367; &#2361;&#2376; &#2332;&#2379; &#2332;&#2366;&#2346;&#2366;&#2344;&#2368; &#2319;&#2344;&#2381;&#2360;&#2375;&#2347;&#2354;&#2366;&#2311;&#2335;&#2367;&#2360;, &#2357;&#2375;&#2360;&#2381;&#2335; &#2344;&#2366;&#2311;&#2354; &#2357;&#2366;&#2351;&#2352;&#2360; &#2325;&#2366; &#2325;&#2366;&#2352;&#2339; &#2348;&#2344;&#2344;&#2375; &#2357;&#2366;&#2354;&#2375; &#2357;&#2366;&#2351;&#2352;&#2360; &#2325;&#2379; &#2347;&#2376;&#2354;&#2366;&#2340;&#2366; &#2361;&#2376;&#2404; </span></span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "25",
                    section: "misc",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">25.</span><span style=\"font-family: Cambria Math;\">Aga Khan Palace is located in_______city of Maharashtra.</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">25. </span><span style=\"font-family: Cambria Math;\">&#2310;&#2327;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2326;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2376;&#2354;&#2375;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2361;&#2366;&#2352;&#2366;&#2359;&#2381;&#2335;&#2381;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> _________ </span><span style=\"font-family: Cambria Math;\">&#2358;&#2361;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2381;&#2341;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                    options_en: ["<p>Pune</p>\n", "<p>Aurangabad</p>\n", 
                                "<p>Ahmednagar</p>\n", "<p>Mumbai</p>\n"],
                    options_hi: ["<p><span style=\"font-family: Cambria Math;\">&#2346;&#2369;&#2339;&#2375;</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">&#2324;&#2352;&#2306;&#2327;&#2366;&#2348;&#2366;&#2342;</span></p>\n",
                                "<p><span style=\"font-family: Cambria Math;\">&#2309;&#2361;&#2350;&#2342;&#2344;&#2327;&#2352;</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">&#2350;&#2369;&#2306;&#2348;&#2312;</span></p>\n"],
                    solution_en: "<p>25.<span style=\"font-family: Cambria Math;\">(a)&nbsp;</span><strong>Pune. </strong><span style=\"font-weight: 400;\">This Palace was built in </span><strong>1892</strong><span style=\"font-weight: 400;\"> by </span><strong>Sultan Muhammed Shah Aga Khan III</strong><span style=\"font-weight: 400;\"> in the district of Pune (Maharashtra). Famous Historical Places in India -&nbsp; Qutub Minar ( Delhi), Sanchi Stupa ( Madhya Pradesh), Rani ki Vav (Gujarat), Victoria Memorial( Kolkata), Mehrangarh Fort (Jodhpur), Chola Temples (Tamil Nadu).</span></p>\n",
                    solution_hi: "<p>25.<span style=\"font-family: Cambria Math;\">(a)&nbsp;</span><strong>&#2346;&#2369;&#2339;&#2375;&#2404; </strong><span style=\"font-weight: 400;\">&#2311;&#2360; &#2346;&#2376;&#2354;&#2375;&#2360; &#2325;&#2366; &#2344;&#2367;&#2352;&#2381;&#2350;&#2366;&#2339; </span><strong>1892</strong><span style=\"font-weight: 400;\"> &#2350;&#2375;&#2306; </span><strong>&#2360;&#2369;&#2354;&#2381;&#2340;&#2366;&#2344; &#2350;&#2369;&#2361;&#2350;&#2381;&#2350;&#2342; &#2358;&#2366;&#2361; &#2310;&#2327;&#2366; &#2326;&#2366;&#2344; &#2340;&#2371;&#2340;&#2368;&#2351; </strong><span style=\"font-weight: 400;\">&#2344;&#2375; &#2346;&#2369;&#2339;&#2375; (&#2350;&#2361;&#2366;&#2352;&#2366;&#2359;&#2381;&#2335;&#2381;&#2352;) &#2332;&#2367;&#2354;&#2375; &#2350;&#2375;&#2306; &#2325;&#2352;&#2357;&#2366;&#2351;&#2366; &#2341;&#2366;&#2404; &#2349;&#2366;&#2352;&#2340; &#2350;&#2375;&#2306; &#2346;&#2381;&#2352;&#2360;&#2367;&#2342;&#2381;&#2343; &#2320;&#2340;&#2367;&#2361;&#2366;&#2360;&#2367;&#2325; &#2360;&#2381;&#2341;&#2366;&#2344; - &#2325;&#2369;&#2340;&#2369;&#2348; &#2350;&#2368;&#2344;&#2366;&#2352; (&#2342;&#2367;&#2354;&#2381;&#2354;&#2368;), &#2360;&#2366;&#2306;&#2330;&#2368; &#2325;&#2366;&nbsp; &#2360;&#2381;&#2340;&#2370;&#2346; (&#2350;&#2343;&#2381;&#2351; &#2346;&#2381;&#2352;&#2342;&#2375;&#2358;), &#2352;&#2366;&#2344;&#2368; &#2325;&#2368; &#2357;&#2366;&#2357; (&#2327;&#2369;&#2332;&#2352;&#2366;&#2340;), &#2357;&#2367;&#2325;&#2381;&#2335;&#2379;&#2352;&#2367;&#2351;&#2366; &#2350;&#2375;&#2350;&#2379;&#2352;&#2367;&#2351;&#2354; (&#2325;&#2379;&#2354;&#2325;&#2366;&#2340;&#2366;), &#2350;&#2375;&#2361;&#2352;&#2366;&#2344;&#2327;&#2338;&#2364; &#2325;&#2367;&#2354;&#2366; (&#2332;&#2379;&#2343;&#2346;&#2369;&#2352;), &#2330;&#2379;&#2354; &#2350;&#2306;&#2342;&#2367;&#2352; (&#2340;&#2350;&#2367;&#2354;&#2344;&#2366;&#2337;&#2369;)&#2404;</span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>