<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Smart Test Interface</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        .question-nav{position:fixed;top:70px;right:0;height:calc(100vh - 70px);width:320px;transition:all .3s;z-index:100;display:flex;flex-direction:column}
        .question-content{margin-right:0;transition:all .3s}
        .question-box{width:40px;height:40px;margin:5px;cursor:pointer}
        .attempted{background-color:#3498db!important;color:white}
        .correct{background-color:#2ecc71!important;color:white}
        .incorrect{background-color:#e74c3c!important;color:white}
        .current{border:3px solid #f39c12!important}
        #question-boxes{overflow-y:auto;max-height:calc(100vh - 150px);padding-bottom:15px}
        @media (max-width:768px){.question-nav{width:100%;right:-100%}.question-content{margin-right:0}}
    </style>
</head>
<body>
    <div class="container-fluid p-0">
        <!-- Header with controls -->
        <nav class="navbar navbar-expand-lg navbar-dark bg-primary sticky-top">
            <div class="container-fluid">
                <a class="navbar-brand" href="#">
                    <i class="fas fa-graduation-cap me-2"></i>Smart Test
                </a>
                <!-- Section selector -->
                <div class="navbar-nav me-auto section-tabs">
                    <select class="form-select" id="section-selector" onchange="switchSection(this.value)">
                        <option value="all">All Sections</option>
                    </select>
                </div>
                <div class="d-flex align-items-center">
                    <!-- Timer -->
                    <div class="bg-light text-primary rounded px-3 py-2 me-2 fw-bold" id="timer">
                        <i class="far fa-clock me-2"></i><span id="timer-display">100:00</span>
                    </div>
                    <!-- Submit button -->
                    <button class="btn btn-success me-2" id="submit-btn" onclick="submitTest()">
                        <i class="fas fa-check me-1"></i> Submit
                    </button>
                    <!-- Language toggle -->
                    <button class="btn btn-light me-2" id="lang-btn" onclick="toggleLanguage()">
                        <i class="fas fa-language"></i>
                    </button>
                    <!-- Menu toggle for mobile -->
                    <button class="btn btn-light d-lg-none" id="menu-toggle" onclick="toggleMenu()">
                        <i class="fas fa-th"></i>
                    </button>
                </div>
            </div>
        </nav>
        <div class="row m-0">
            <!-- Main content area -->
            <div class="col p-0 question-content" id="question-content">
                <div class="container py-4">
                    <!-- Question navigation -->
                    <div class="d-flex justify-content-between mb-4">
                        <button class="btn btn-outline-primary" onclick="prevQuestion()">
                            <i class="fas fa-chevron-left me-1"></i> Previous
                        </button>
                        <div class="fw-bold" id="question-counter">Question 1 of 100</div>
                        <button class="btn btn-outline-primary" onclick="nextQuestion()">
                            Next <i class="fas fa-chevron-right ms-1"></i>
                        </button>
                    </div>
                    <!-- Questions will be displayed here -->
                    <div id="questions-container"></div>
                </div>
            </div>
            <!-- Question navigation sidebar -->
            <div class="bg-light question-nav" id="question-nav">
                <div class="p-3">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h5 class="m-0">Questions</h5>
                        <div>
                            <span class="badge bg-secondary me-1" id="attempted-count">0</span>
                            <span class="badge bg-secondary" id="total-count">100</span>
                            <button class="btn btn-sm btn-outline-secondary ms-2 d-lg-none" onclick="toggleMenu()">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    </div>
                    <div class="d-flex flex-wrap" id="question-boxes"></div>
                </div>
            </div>
        </div>
        <!-- Results modal -->
        <div class="modal fade" id="results-modal" tabindex="-1" aria-hidden="true">
            <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content">
                    <div class="modal-header bg-primary text-white">
                        <h5 class="modal-title">Test Results</h5>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <div class="row text-center">
                            <div class="col-6 mb-3">
                                <div class="p-3 bg-primary bg-opacity-10 rounded">
                                    <h3 id="score-value">0</h3>
                                    <div class="small text-muted">Score</div>
                                </div>
                            </div>
                            <div class="col-6 mb-3">
                                <div class="p-3 bg-success bg-opacity-10 rounded">
                                    <h3 id="correct-value">0</h3>
                                    <div class="small text-muted">Correct</div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="p-3 bg-danger bg-opacity-10 rounded">
                                    <h3 id="incorrect-value">0</h3>
                                    <div class="small text-muted">Incorrect</div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="p-3 bg-secondary bg-opacity-10 rounded">
                                    <h3 id="unattempted-value">0</h3>
                                    <div class="small text-muted">Unattempted</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-primary" onclick="reviewTest()">Review Test</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 100 * 60;
        let resultsModal;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            resultsModal = new bootstrap.Modal(document.getElementById('results-modal'));
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 768) {
                document.getElementById('question-content').style.marginRight = '320px';
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["6"] = {
                name: "Reasoning",
                start: 0,
                end: 23
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="6">Reasoning</option>`;
            sections["18"] = {
                name: "General Awareness",
                start: 24,
                end: 48
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="18">General Awareness</option>`;
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 49,
                end: 73
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["7"] = {
                name: "English",
                start: 74,
                end: 98
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="7">English</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 99,
                end: 99
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "6",
                    question_en: "<p>1. Three of the following four are alike in a certain way and thus form a group. Which is the one that does NOT belong to that group ? The odd one out is not based on the number of consonants/vowels or their position in the letter cluster.</p>",
                    question_hi: "<p>1. निम्नलिखित चार विकल्पों में से तीन किसी न किसी रूप में एक समान हैं और इस प्रकार एक समूह का निर्माण करते हैं। उसमें से कौन सा विकल्प उस समूह से संबंधित नहीं है ? अक्षर समूह में, असमान विकल्प व्यंजनों/स्वरों की संख्या या उनकी स्थिति पर आधारित नहीं है।</p>",
                    options_en: [
                        "<p>DHK</p>",
                        "<p>EIM</p>",
                        "<p>BFJ</p>",
                        "<p>IMQ</p>"
                    ],
                    options_hi: [
                        "<p>DHK</p>",
                        "<p>EIM</p>",
                        "<p>BFJ</p>",
                        "<p>IMQ</p>"
                    ],
                    solution_en: "<p>1.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739184125658.png\" alt=\"rId4\" width=\"108\" height=\"56\">&nbsp; &nbsp; ,&nbsp; &nbsp; <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739184125806.png\" alt=\"rId5\" width=\"104\" height=\"55\">&nbsp; &nbsp;,&nbsp; &nbsp; <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739184125950.png\" alt=\"rId6\" width=\"106\" height=\"56\"><br>But,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739184126115.png\" alt=\"rId7\" width=\"104\" height=\"55\"></p>",
                    solution_hi: "<p>1.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739184125658.png\" alt=\"rId4\" width=\"108\" height=\"56\">&nbsp; &nbsp; ,&nbsp; &nbsp; <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739184125806.png\" alt=\"rId5\" width=\"104\" height=\"55\">&nbsp; &nbsp;,&nbsp; &nbsp; <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739184125950.png\" alt=\"rId6\" width=\"106\" height=\"56\"><br>लेकिन,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739184126115.png\" alt=\"rId7\" width=\"104\" height=\"55\"></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "6",
                    question_en: "<p>2. In a certain code language, \'DRAPE\' is coded as \'65432\' and \'TAPED\' is coded as \'24596\'. What is the code for \'R\' in that language ?</p>",
                    question_hi: "<p>2. एक निश्चित कूट भाषा में, \'DRAPE\' को \'65432\' के रूप में कूटबद्ध किया जाता है और \'TAPED\' को \'24596\' के रूप में कूटबद्ध किया जाता है। उसी भाषा में \'R\' के लिए क्या कूट होगा ?</p>",
                    options_en: [
                        "<p>2</p>",
                        "<p>6</p>",
                        "<p>3</p>",
                        "<p>9</p>"
                    ],
                    options_hi: [
                        "<p>2</p>",
                        "<p>6</p>",
                        "<p>3</p>",
                        "<p>9</p>"
                    ],
                    solution_en: "<p>2.(c)<br>D R A P E &rarr;&nbsp;6 5 4 3 2<br>T A P E D &rarr; 2 4 5 9 6<br>From above code for &lsquo; R&rsquo; is 3.</p>",
                    solution_hi: "<p>2.(c)<br>D R A P E &rarr; 6 5 4 3 2<br>T A P E D &rarr; 2 4 5 9 6<br>उपरोक्त कोड से \'R\' के लिए कोड \'3\' है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "6",
                    question_en: "<p>3. In a certain code language, \'try something new\' is coded as \'db kl jk\' and \'something hurt him\' is coded as \'db pt uk. How is \'something\' coded in that language ?</p>",
                    question_hi: "<p>3. एक निश्चित कूट भाषा में, \'try something new\' को \'db kl jk\' लिखा जाता है और \'something hurt him\' को \'db pt uk लिखा जाता है। है। उस कूट भाषा में something\' को कैसे लिखा जाएगा ?</p>",
                    options_en: [
                        "<p>jk</p>",
                        "<p>db</p>",
                        "<p>uk</p>",
                        "<p>kl</p>"
                    ],
                    options_hi: [
                        "<p>jk</p>",
                        "<p>db</p>",
                        "<p>uk</p>",
                        "<p>kl</p>"
                    ],
                    solution_en: "<p>3.(b) try something new &rarr; db kl jk&hellip;&hellip;(i)<br>something hurt him &rarr; db pt uk&hellip;.(ii)<br>From (i) and (ii) &lsquo;something&rsquo; and &lsquo;db&rsquo; are common. The code of &lsquo;something&rsquo; = &lsquo;db&rsquo;.</p>",
                    solution_hi: "<p>3.(b) try something new &rarr; db kl jk&hellip;&hellip;(i)<br>something hurt him &rarr; db pt uk&hellip;.(ii)<br>(i) और (ii) से \'something\' और \'db\' उभयनिष्ठ हैं। \'something\' का कोड = \'db\'।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "6",
                    question_en: "<p>4. Which of the following numbers will replace the question mark (?) in the given series ?<br>12 60 20 100 60 300 260 ?</p>",
                    question_hi: "<p>4. निम्नलिखित में से कौन-सी संख्या दी गई श्रृंखला में प्रश्न चिह्न (?) के स्थान पर आएगी ?<br>12 60 20 100 60 300 260 ?</p>",
                    options_en: [
                        "<p>1300</p>",
                        "<p>1240</p>",
                        "<p>1169</p>",
                        "<p>1280</p>"
                    ],
                    options_hi: [
                        "<p>1300</p>",
                        "<p>1240</p>",
                        "<p>1169</p>",
                        "<p>1280</p>"
                    ],
                    solution_en: "<p>4.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739184126317.png\" alt=\"rId8\" width=\"308\" height=\"45\"></p>",
                    solution_hi: "<p>4.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739184126317.png\" alt=\"rId8\" width=\"308\" height=\"45\"></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "6",
                    question_en: "5.  Select the letter-cluster pair that best represents a similar relationship to the one expressed in the pairs of letter-clusters given below. <br />GKI : MQO <br />CGE : IMK ",
                    question_hi: "5. उस अक्षर-समूह युग्म का चयन कीजिए जो नीचे दिए गए अक्षर-समूह युग्म में व्यक्त किए गए समान संबंध का सबसे अच्छा निरूपण करता है।<br />GKI : MQO <br />CGE : IMK ",
                    options_en: [
                        " HLJ : NRO",
                        " RUT : VZX ",
                        " JOM : PUR",
                        " LPN : RVT"
                    ],
                    options_hi: [
                        " HLJ : NRO",
                        " RUT : VZX ",
                        " JOM : PUR",
                        " LPN : RVT"
                    ],
                    solution_en: "<p>5.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739184126472.png\" alt=\"rId9\" width=\"147\" height=\"92\">&nbsp; &nbsp; &nbsp; ,&nbsp; &nbsp;<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739184126596.png\" alt=\"rId10\" width=\"142\" height=\"86\"><br>Similarly,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739184126833.png\" alt=\"rId11\" width=\"141\" height=\"87\"></p>",
                    solution_hi: "<p>5.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739184126472.png\" alt=\"rId9\" width=\"147\" height=\"92\">&nbsp; &nbsp; &nbsp; ,&nbsp; &nbsp;<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739184126596.png\" alt=\"rId10\" width=\"142\" height=\"86\"><br>इसी प्रकार,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739184126833.png\" alt=\"rId11\" width=\"141\" height=\"87\"></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "6",
                    question_en: "<p>6. Select the set in which the numbers are related in the same way as are the numbers of the following sets. <br>(26, 38, 128) <br>(42, 19, 122)<br>(<strong>NOTE :</strong> Operations should be performed on the whole numbers, without breaking down the numbers into its constituent digits. E.g. 13 &ndash; Operations on 13 such as adding /subtracting /multiplying etc., to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is not allowed.)</p>",
                    question_hi: "<p>6. उस समुच्&zwj;चय का चयन कीजिए जिसमें संख्याएं ठीक उसी प्रकार संबंधित हैं जिस प्रकार दिए गए समुच्&zwj;चयों की संख्याएं संबंधित हैं।<br>(26, 38, 128) <br>(42, 19, 122)<br>(<strong>ध्यान दें : </strong>संख्याओं को उनके घटक अंकों में अलग-अलग किए बिना पूर्ण संख्याओं पर संक्रियाएं की जानी चाहिए। उदाहरण के लिए संख्या 13 - संख्या 13 पर संक्रियाएं जैसे 13 को जोड़ना/घटाना/गुणा करना आदि किया जा सकता है। 13 को 1 और 3 में अलग-अलग करने की और फिर 1 और 3 पर गणितीय संक्रियाएं करने की अनुमति नहीं है।)</p>",
                    options_en: [
                        "<p>(71, 9, 320)</p>",
                        "<p>(33, 23, 112)</p>",
                        "<p>(25, 36, 183)</p>",
                        "<p>(18, 52, 70)</p>"
                    ],
                    options_hi: [
                        "<p>(71, 9, 320)</p>",
                        "<p>(33, 23, 112)</p>",
                        "<p>(25, 36, 183)</p>",
                        "<p>(18, 52, 70)</p>"
                    ],
                    solution_en: "<p>6.(b) <strong>Logic :- </strong>(1st number + 2nd number) &times; 2 = 3rd number<br>(26, 38, 128) :- (26 + 38) &times; 2 &rArr; (64) &times; 2 = 128<br>(42, 19, 122) :- (42 + 19) &times; 2 &rArr; (61) &times; 2 = 122<br>Similarly,<br>(33 ,23 ,112) :- (33 + 23) &times; 2 &rArr; (56) &times; 2 = 112</p>",
                    solution_hi: "<p>6.(b)<strong> तर्क :-</strong> (पहली संख्या + दूसरी संख्या) &times; 2 = तीसरी संख्या<br>(26, 38, 128) :- (26 + 38) &times; 2 &rArr; (64) &times; 2 = 128<br>(42, 19, 122) :- (42 + 19) &times; 2 &rArr; (61) &times; 2 = 122<br>इसी प्रकार,<br>(33 ,23 ,112) :- (33 + 23) &times; 2 &rArr; (56) &times; 2 = 112</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "6",
                    question_en: "<p>7. What should come in place of the question mark (?) in the given series based on the English alphabetical order ?&nbsp;<br>ECF, JHK, OMP, TRU, ?</p>",
                    question_hi: "<p>7. अंग्रेजी वर्णमाला क्रम के आधार पर दी गई श्रृंखला में प्रश्नवाचक चिह्न (?) के स्थान पर क्या आना चाहिए ?<br>ECF, JHK, OMP, TRU, ?</p>",
                    options_en: [
                        "<p>YWZ</p>",
                        "<p>YVZ</p>",
                        "<p>XYZ</p>",
                        "<p>XVZ</p>"
                    ],
                    options_hi: [
                        "<p>YWZ</p>",
                        "<p>YVZ</p>",
                        "<p>XYZ</p>",
                        "<p>XVZ</p>"
                    ],
                    solution_en: "<p>7.(a) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739184127014.png\" alt=\"rId12\" width=\"301\" height=\"95\"></p>",
                    solution_hi: "<p>7.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739184127014.png\" alt=\"rId12\" width=\"301\" height=\"95\"></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "6",
                    question_en: "<p>8. Select the option figure that will replace the question mark (?) in the figure given below and complete the pattern.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739184127139.png\" alt=\"rId13\" width=\"142\" height=\"142\"></p>",
                    question_hi: "<p>8. उस विकल्प आकृति का चयन करें जो नीचे दी गई आकृति में प्रश्न चिह्न (?) को प्रतिस्थापित करेगी और पैटर्न को पूरा करेगी।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739184127139.png\" alt=\"rId13\" width=\"142\" height=\"142\"></p>",
                    options_en: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739184127232.png\" alt=\"rId14\" width=\"81\" height=\"79\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739184127356.png\" alt=\"rId15\" width=\"81\" height=\"84\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739184127510.png\" alt=\"rId16\" width=\"81\" height=\"81\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739184127614.png\" alt=\"rId17\" width=\"81\" height=\"82\"></p>"
                    ],
                    options_hi: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739184127232.png\" alt=\"rId14\" width=\"81\" height=\"80\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739184127356.png\" alt=\"rId15\" width=\"81\" height=\"84\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739184127510.png\" alt=\"rId16\" width=\"81\" height=\"81\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739184127614.png\" alt=\"rId17\" width=\"81\" height=\"82\"></p>"
                    ],
                    solution_en: "<p>8.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739184127356.png\" alt=\"rId15\" width=\"81\" height=\"84\"></p>",
                    solution_hi: "<p>8.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739184127356.png\" alt=\"rId15\" width=\"81\" height=\"84\"></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "6",
                    question_en: "<p>9. Select the option that represents the letters which, when sequentially placed from left to right in the blanks below, will complete the letter series. <br>f h _ h _ g f _ h g _ h _ f f _ g _ h _ f</p>",
                    question_hi: "<p>9. उस विकल्प का चयन कीजिए, जो उन अक्षरों को दर्शाता है, जिन्हें नीचे दिए गए रिक्त स्थानों में बाएँ से दाएँ क्रमिक रूप से रखने पर अक्षर शृंखला पूरी होगी। <br>f h _ h _ g f _ h g _ h _ f f _ g _ h _ f</p>",
                    options_en: [
                        "<p>g h f h g f f g</p>",
                        "<p>g h f h g h f g</p>",
                        "<p>g h f f g h f g</p>",
                        "<p>g h f h g h h g</p>"
                    ],
                    options_hi: [
                        "<p>g h f h g f f g</p>",
                        "<p>g h f h g h f g</p>",
                        "<p>g h f f g h f g</p>",
                        "<p>g h f h g h h g</p>"
                    ],
                    solution_en: "<p>9.(d)<br>f h <span style=\"text-decoration: underline;\"><strong>g</strong></span> h <span style=\"text-decoration: underline;\"><strong>h</strong></span> g f/ <span style=\"text-decoration: underline;\"><strong>f</strong></span> h g <span style=\"text-decoration: underline;\"><strong>h</strong></span> h <span style=\"text-decoration: underline;\"><strong>g</strong></span> f/ f <span style=\"text-decoration: underline;\"><strong>h</strong></span> g <span style=\"text-decoration: underline;\"><strong>h</strong></span> h <span style=\"text-decoration: underline;\"><strong>g</strong></span> f</p>",
                    solution_hi: "<p>9.(d)<br>f h <span style=\"text-decoration: underline;\"><strong>g</strong></span> h <span style=\"text-decoration: underline;\"><strong>h</strong></span> g f/ <span style=\"text-decoration: underline;\"><strong>f</strong></span> h g <span style=\"text-decoration: underline;\"><strong>h</strong></span> h <span style=\"text-decoration: underline;\"><strong>g</strong></span> f/ f <span style=\"text-decoration: underline;\"><strong>h</strong></span> g <span style=\"text-decoration: underline;\"><strong>h</strong></span> h <span style=\"text-decoration: underline;\"><strong>g</strong></span> f</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "6",
                    question_en: "<p>10. In a certain code language, \'CLERK\' is written as \'BKEQJ\' and \'OFFICE\' is written as \'OEEIBE\'. How will \'POST\' be written in that language ?</p>",
                    question_hi: "<p>10. एक निश्चित कोड भाषा में, \'CLERK\' को \'BKEQJ\' लिखा जाता है और \'OFFICE\' को \'OEEIBE\' लिखा जाता है। उसी भाषा में \'POST\' को कैसे लिखा जाएगा ?</p>",
                    options_en: [
                        "<p>OROS</p>",
                        "<p>OORS</p>",
                        "<p>RSOO</p>",
                        "<p>OSOR</p>"
                    ],
                    options_hi: [
                        "<p>OROS</p>",
                        "<p>OORS</p>",
                        "<p>RSOO</p>",
                        "<p>OSOR</p>"
                    ],
                    solution_en: "<p>10.(b)<br><strong>Logic:-</strong> (Consonant - 1) and vowels remain as it is.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739184127795.png\" alt=\"rId18\" width=\"130\" height=\"87\">&nbsp; &nbsp; ,&nbsp; &nbsp; &nbsp;<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739184127933.png\" alt=\"rId19\" width=\"143\" height=\"86\"><br>Similarly,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739184128093.png\" alt=\"rId20\" width=\"124\" height=\"94\"></p>",
                    solution_hi: "<p>10.(b)<br><strong>तर्क:- </strong>(व्यंजन-1) एवं स्वर यथावत रहेंगे ।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739184127795.png\" alt=\"rId18\" width=\"130\" height=\"87\">&nbsp; &nbsp; ,&nbsp; &nbsp; &nbsp;<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739184127933.png\" alt=\"rId19\" width=\"143\" height=\"86\"><br>इसी प्रकार,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739184128093.png\" alt=\"rId20\" width=\"124\" height=\"94\"></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "6",
                    question_en: "<p>11. Three of the following number-pairs are alike in some manner and hence form a group. Which number-pair does not belong to that group ?<br>(<strong>NOTE : </strong>Operations should be performed on the whole numbers, without breaking down the numbers into its constituent digits. E.g. 13 - Operations on 13 such as adding/subtracting/multiplying etc. to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is not allowed.)</p>",
                    question_hi: "<p>11. निम्नलिखित संख्या-युग्मों में से तीन किसी तरह से समान हैं और इसलिए एक समूह बनाते हैं। कौन-सा संख्या-युग्म उस समूह से संबंधित नहीं है ?<br>(<strong>नोट : </strong>संख्याओं को उसके घटक अंकों में तोड़े बिना, पूर्ण संख्याओं पर गणितीय संक्रियाएं की जानी चाहिए। उदाहरण के लिए 13 -13 पर संक्रियाएं जैसे कि 13 में जोड़ना/घटाना/गुणा करना आदि की जा सकती हैं। 13 को 1 और 3 में तोड़ने और फिर 1 और 3 पर गणितीय संक्रियाएँ करने की अनुमति नहीं है।)</p>",
                    options_en: [
                        " 21-420",
                        " 22-462",
                        " 18-306",
                        " 15-240"
                    ],
                    options_hi: [
                        " 21-420",
                        " 22-462",
                        " 18-306",
                        " 15-240"
                    ],
                    solution_en: "<p>11.(d) <strong>Logic:</strong> (1st number)<sup>2</sup> - 1st number = 2nd number<br>21-420 :- (21)<sup>2</sup> -21 &rArr;&nbsp;441 - 21 = 420<br>22-462 :- (22)<sup>2</sup> - 22 &rArr; 484 - 22 = 462<br>18-306 :- (18)<sup>2</sup> - 18 &rArr; 306<br>But<br>15-240 :- (15)<sup>2</sup> - 15 &rArr; 225 - 15 = 210 (&ne;240)</p>",
                    solution_hi: "<p>11.(d) <strong id=\"docs-internal-guid-9c58907d-7fff-d59f-1b72-1be67113d3e0\">तर्क</strong><strong>:</strong> (पहली संख्या)<sup>2</sup> - पहली संख्या = दूसरी संख्या<br>21-420 :- (21)<sup>2</sup> -21 &rArr;&nbsp;441 - 21 = 420<br>22-462 :- (22)<sup>2</sup> - 22 &rArr; 484 - 22 = 462<br>18-306 :- (18)<sup>2</sup> - 18 &rArr; 306<br>लेकिन<br>15-240 :- (15)<sup>2</sup> - 15 &rArr; 225 - 15 = 210 (&ne;240)</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "6",
                    question_en: "<p>12. How many triangles are there in the given figure ?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739184128421.png\" alt=\"rId22\" width=\"132\" height=\"120\"></p>",
                    question_hi: "<p>12. दी गई आकृति में कितने त्रिभुज हैं ?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739184128421.png\" alt=\"rId22\" width=\"132\" height=\"120\"></p>",
                    options_en: [
                        " 10 ",
                        " 11",
                        " 12",
                        " 13<br /> "
                    ],
                    options_hi: [
                        " 10 ",
                        " 11",
                        " 12",
                        " 13"
                    ],
                    solution_en: "<p>12.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739184128548.png\" alt=\"rId23\" width=\"153\" height=\"145\"><br>There are 13 triangles = ABF, ABC, AFG, FCG, FCH, GCH, HDI, DIJ, HDJ, AIJ, AEJ, ACD, ADE.</p>",
                    solution_hi: "<p>12.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739184128548.png\" alt=\"rId23\" width=\"153\" height=\"145\"><br>कुल 13 त्रिभुज हैं = ABF, ABC, AFG, FCG, FCH, GCH, HDI, DIJ, HDJ, AIJ, AEJ, ACD, ADE.</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "6",
                    question_en: "<p>13. Three statements are followed by conclusions numbered I, II. You have to consider these statements to be true, even if they seem to be at variance with commonly known facts. Decide which of the given conclusions logically follow/s from the given statement. <br><strong>Statements :</strong> <br>All space is wind. <br>Some wind is earth. <br>Some fire is earth. <br><strong>Conclusion (I) : </strong>All earth is space. <br><strong>Conclusion (II) : </strong>Some wind is fire</p>",
                    question_hi: "<p>13. तीन कथनों के बाद I, II क्रमांकित निष्कर्ष दिए गए हैं। आपको इन कथनों को सत्य मानना होगा, चाहे वे समान्यतः ज्ञात तथ्यों से अलग प्रतीत होते हों। आपको निश्चय करना है कि कौन-सा/कौन-से निष्कर्ष तार्किक रूप से दिए गए कथन/कथनों के अनुसार है/हैं।<br><strong>कथन :</strong> <br>सभी अंतरिक्ष , पवन हैं। <br>कुछ पवन, पृथ्वी हैं। <br>कुछ अग्नि , पृथ्वी हैं। <br><strong>निष्कर्ष (I) :</strong> सभी पृथ्वी , अंतरिक्ष है। <br><strong>निष्कर्ष (II) : </strong>कुछ पवन, अग्नि हैं।</p>",
                    options_en: [
                        "<p>Only conclusion (I) follows.</p>",
                        "<p>Only conclusion (II) follows</p>",
                        "<p>Both conclusions (I) and (II) follow.</p>",
                        "<p>Neither conclusion (I) nor (II) follows</p>"
                    ],
                    options_hi: [
                        "<p>केवल निष्कर्ष (I) कथनों के अनुसार है।</p>",
                        "<p>केवल निष्कर्ष (II) कथनों के अनुसार है।</p>",
                        "<p>दोनों निष्कर्ष (I) और (II) कथनों के अनुसार हैं।</p>",
                        "<p>न तो निष्कर्ष (I) और न ही (II) कथनों के अनुसार है।</p>"
                    ],
                    solution_en: "<p>13.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739184128662.png\" alt=\"rId24\" width=\"288\" height=\"81\"><br>Neither conclusion I nor II follow.</p>",
                    solution_hi: "<p>13.(d) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739184128878.png\" alt=\"rId25\" width=\"293\" height=\"85\"><br>न तो निष्कर्ष I और न ही II अनुसरण करता है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "6",
                    question_en: "<p>14. In a certain code language,<br>S % T = S is the father of T.<br>X - W = X is the mother of W.<br>U * V = U is the sister of V.<br>Z + Y = Z is the husband of Y.<br>If A + J - B * V % G, find the relation between A and G.</p>",
                    question_hi: "<p>14. एक निश्चित कूट भाषा में,<br>S % T = S, T के पिता है।<br>X - W = X, W की माँ है।<br>U * V = U, V की बहन है।<br>Z + Y = Z, Y का पति है।<br>यदि A + J - B * V % G हो तो A और G के बीच संबंध ज्ञात करें।</p>",
                    options_en: [
                        "<p>Maternal grandfather</p>",
                        "<p>Father</p>",
                        "<p>Mother</p>",
                        "<p>Paternal grandfather</p>"
                    ],
                    options_hi: [
                        "<p>नाना</p>",
                        "<p>पिता</p>",
                        "<p>माँ</p>",
                        "<p>दादा</p>"
                    ],
                    solution_en: "<p>14.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739184129006.png\" alt=\"rId26\" width=\"151\" height=\"137\"><br>A is the Paternal Grandfather of G.</p>",
                    solution_hi: "<p>14.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739184129006.png\" alt=\"rId26\" width=\"151\" height=\"137\"><br>A, G का दादा है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "6",
                    question_en: "<p>15. The following Venn diagram shows Men\'s profession as Doctors and Chess Players. <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739184129131.png\" alt=\"rId27\" width=\"229\" height=\"259\"> <br>How many men are doctors ?</p>",
                    question_hi: "<p>15. नीचे दिया गया वेन आरेख डॉक्टरों (Doctors) और शतरंज खिलाड़ियों (Chess Players) के रूप में पुरुषों (Men) के पेशे को दर्शाता है।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739184129267.png\" alt=\"rId28\" width=\"250\" height=\"290\"> <br>कितने पुरुष डॉक्टर हैं ?</p>",
                    options_en: [
                        "<p>47</p>",
                        "<p>34</p>",
                        "<p>24</p>",
                        "<p>23</p>"
                    ],
                    options_hi: [
                        "<p>47</p>",
                        "<p>34</p>",
                        "<p>24</p>",
                        "<p>23</p>"
                    ],
                    solution_en: "<p>15.(a) Total number of Men who are doctors = 23 + 24 = 47</p>",
                    solution_hi: "<p>15.(a) डॉक्टर बनने वाले पुरुषों की कुल संख्या = 23 + 24 = 47</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "16",
                    section: "6",
                    question_en: "<p>16. Three different positions of the same dice are given. Find the number on the face opposite to the face showing &lsquo;2&rsquo;.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739184129446.png\" alt=\"rId29\" width=\"188\" height=\"60\"></p>",
                    question_hi: "<p>16. एक ही पासे की तीन अलग-अलग स्थितियां दर्शाई गई हैं। &lsquo;2&rsquo; दर्शाने वाले फलक के विपरीत वाले फलक पर कौन-सी संख्या होगी ?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739184129446.png\" alt=\"rId29\" width=\"188\" height=\"60\"></p>",
                    options_en: [
                        "<p>3</p>",
                        "<p>4</p>",
                        "<p>1</p>",
                        "<p>6</p>"
                    ],
                    options_hi: [
                        "<p>3</p>",
                        "<p>4</p>",
                        "<p>1</p>",
                        "<p>6</p>"
                    ],
                    solution_en: "<p>16.(a)<br>From 2<sup>nd</sup> and 3<sup>rd</sup> dice the opposite face are <br>6 &harr; 5 , 4 &harr; 1, 3 &harr; 2</p>",
                    solution_hi: "<p>16.(a)<br>दूसरे और तीसरे पासे से विपरीत फलक हैं,<br>6 &harr; 5 , 4 &harr; 1, 3 &harr; 2</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "17",
                    section: "6",
                    question_en: "<p>17. Six students - Aman, Bobby, Chintu, Gita, Pranay and Ram - are sitting around a circular table, facing the centre.Ram is sitting second to the right of Gita.Pranay is the immediate neighbour of Gita and Bobby.Aman is sitting third to the right of Gita. Who among the following is the immediate neighbour of both Ram and Bobby ?</p>",
                    question_hi: "<p>17. छह विद्यार्थी- अमन, बॉबी, चिंटू, गीता, प्रणय और राम - एक गोल मेज के परितः केंद्र की ओर अभिमुख होकर बैठे हैं।राम, गीता के दायें से दूसरे स्थान पर बैठा है।गीता और बॉबी का निकटतम पड़ोसी प्रणय है।अमन, गीता के दायें से तीसरे स्थान पर बैठा है।निम्नलिखित में से कौन राम और बॉबी दोनों का निकटतम पड़ोसी है ?</p>",
                    options_en: [
                        "<p>Pranay</p>",
                        "<p>Aman</p>",
                        "<p>Gita</p>",
                        "<p>Chintu</p>"
                    ],
                    options_hi: [
                        "<p>प्रणय</p>",
                        "<p>अमन</p>",
                        "<p>गीता</p>",
                        "<p>चिंटू</p>"
                    ],
                    solution_en: "<p>17.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739184129582.png\" alt=\"rId30\" width=\"209\" height=\"122\"><br>Aman is the neighbour of Bobby and Ram.</p>",
                    solution_hi: "<p>17.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739184129693.png\" alt=\"rId31\" width=\"216\" height=\"147\"><br>अमन, बॉबी और राम का पड़ोसी है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "18",
                    section: "6",
                    question_en: "<p>18. Select the option figure in which the given figure (X) is embedded as its part (rotation is NOT allowed).<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739184129802.png\" alt=\"rId32\" width=\"114\" height=\"126\"></p>",
                    question_hi: "<p>18. उस विकल्प आकृति का चयन कीजिए जिसमें दी गई आकृति (X) उसके भाग के रूप में निहित है (घूर्णन की अनुमति नहीं है)।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739184129802.png\" alt=\"rId32\" width=\"114\" height=\"126\"></p>",
                    options_en: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739184129910.png\" alt=\"rId33\" width=\"95\" height=\"90\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739184130046.png\" alt=\"rId34\" width=\"97\" height=\"93\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739184130163.png\" alt=\"rId35\" width=\"89\" height=\"86\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739184130268.png\" alt=\"rId36\" width=\"92\" height=\"93\"></p>"
                    ],
                    options_hi: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739184129910.png\" alt=\"rId33\" width=\"93\" height=\"88\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739184130046.png\" alt=\"rId34\" width=\"94\" height=\"90\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739184130163.png\" alt=\"rId35\" width=\"89\" height=\"86\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739184130268.png\" alt=\"rId36\" width=\"91\" height=\"92\"></p>"
                    ],
                    solution_en: "<p>18.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739184130388.png\" alt=\"rId37\" width=\"94\" height=\"96\"></p>",
                    solution_hi: "<p>18.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739184130388.png\" alt=\"rId37\" width=\"94\" height=\"96\"></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "19",
                    section: "6",
                    question_en: "<p>19. If \'A\' stands for \'<math display=\"inline\"><mo>&#247;</mo></math>\', \'B\' stands for \'&times;\', \'C\' stands for \'+\' and \'D\' stands for \'-\', what will come in place of the question mark (?) in the following equation ?<br>88 B 3 D 12 A 4 C 6 = ?</p>",
                    question_hi: "<p>19. यदि \'A\' का अर्थ \'<math display=\"inline\"><mo>&#247;</mo></math>\', \'B\' का अर्थ \'&times;\', \'C\' का अर्थ \'+\' और \'D\' का अर्थ &lsquo;-&rsquo; है, तो निम्नलिखित समीकरण में प्रश्न चिन्ह (?) के स्थान पर क्या आएगा ?<br>88 B 3 D 12 A 4 C 6 = ?</p>",
                    options_en: [
                        "<p>268</p>",
                        "<p>267</p>",
                        "<p>264</p>",
                        "<p>265</p>"
                    ],
                    options_hi: [
                        "<p>268</p>",
                        "<p>267</p>",
                        "<p>264</p>",
                        "<p>265</p>"
                    ],
                    solution_en: "<p>19.(b)<strong> Given :- </strong>88 B 3 D 12 A 4 C 6<br>As per given instruction after interchanging letter with sign we get<br>88 &times; 3 - 12 &divide;&nbsp;4 + 6<br>264 - 3 + 6 = 267</p>",
                    solution_hi: "<p>19.(b) <strong>दिया गया है :- </strong>88 B 3 D 12 A 4 C 6<br>दिए गए निर्देश के अनुसार अक्षर को चिह्न से बदलने पर हमें प्राप्त होता है<br>88 &times; 3 - 12 &divide; 4 + 6<br>264 - 3 + 6 = 267</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "20",
                    section: "6",
                    question_en: "<p>20. Select the correct mirror image of the given figure when the mirror is placed at MN as shown below.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739184130508.png\" alt=\"rId38\" width=\"111\" height=\"112\"></p>",
                    question_hi: "<p>20. दी गई आकृति के उस सही दर्पण प्रतिबिंब को चुनिए, जो नीचे दर्शाए गए अनुसार दर्पण को MN पर रखने पर बनेगा।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739184130508.png\" alt=\"rId38\" width=\"111\" height=\"112\"></p>",
                    options_en: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739184130622.png\" alt=\"rId39\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739184130724.png\" alt=\"rId40\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739184130893.png\" alt=\"rId41\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739184130988.png\" alt=\"rId42\"></p>"
                    ],
                    options_hi: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739184130622.png\" alt=\"rId39\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739184130724.png\" alt=\"rId40\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739184130893.png\" alt=\"rId41\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739184130988.png\" alt=\"rId42\"></p>"
                    ],
                    solution_en: "<p>20.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739184130724.png\" alt=\"rId40\"></p>",
                    solution_hi: "<p>20.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739184130724.png\" alt=\"rId40\"></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "21",
                    section: "6",
                    question_en: "<p>21. What will come in place of the question mark (?) when + and &minus; are interchanged and 8 and 16 are also interchanged ?&nbsp;<br>3 &times; 6 + 16 &minus; 8 &divide; 2 = ?</p>",
                    question_hi: "<p>21. यदि + और &minus; को आपस में बदल दिया जाए, साथ ही 8 और 16 को भी आपस में बदल दिया जाए, तो प्रश्&zwj;नवाचक चिह्न (?) के स्थान पर क्या आएगा ?<br>3 &times; 6 + 16 &minus; 8 &divide; 2 = ?</p>",
                    options_en: [
                        "<p>18</p>",
                        "<p>20</p>",
                        "<p>19</p>",
                        "<p>17</p>"
                    ],
                    options_hi: [
                        "<p>18</p>",
                        "<p>20</p>",
                        "<p>19</p>",
                        "<p>17</p>"
                    ],
                    solution_en: "<p>21.(a) <strong>Given :- </strong>3 &times; 6 + 16 - 8 &divide;&nbsp;2 <br>As per given instruction after interchanging &lsquo;+&rsquo; and &lsquo;-&rsquo; and 8 and 16 we get<br>3 &times; 6 - 8 + 16 &divide; 2 <br>18 - 8 + 8 = 18</p>",
                    solution_hi: "<p>21.(a) <strong>दिया गया :-</strong> 3 &times; 6 + 16 - 8 &divide; 2<br>दिए गए निर्देश के अनुसार \'+\' और \'-\' तथा 8 और 16 को आपस में बदलने पर हमें मिलता है<br>3 &times; 6 - 8 + 16 &divide; 2 <br>18 - 8 + 8 = 18</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "22",
                    section: "6",
                    question_en: "<p>22. The position of how many letters will remain unchanged if each of the letters in the word &lsquo;POWDERS&rsquo; is arranged in English alphabetical order ?</p>",
                    question_hi: "<p>22. यदि शब्द &lsquo;POWDERS&rsquo; के प्रत्येक अक्षर को अंग्रेजी वर्णानुक्रम में व्यवस्थित किया जाए तो कितने अक्षरों का स्थान अपरिवर्तित रहेगा ?</p>",
                    options_en: [
                        "<p>One</p>",
                        "<p>More than three</p>",
                        "<p>None</p>",
                        "<p>Three</p>"
                    ],
                    options_hi: [
                        "<p>एक</p>",
                        "<p>तीन से अधिक</p>",
                        "<p>एक का भी नहीं</p>",
                        "<p>तीन</p>"
                    ],
                    solution_en: "<p>22.(c) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739184131096.png\" alt=\"rId43\" width=\"195\" height=\"80\"><br>By changing the position of all the letters, no letter will remain unchanged.</p>",
                    solution_hi: "<p>22.(c) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739184131096.png\" alt=\"rId43\" width=\"195\" height=\"80\"><br>सभी अक्षरों का स्थान बदलने पर, कोई भी अक्षर अपरिवर्तित नहीं होगा।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "23",
                    section: "6",
                    question_en: "<p>23. Select the figure from among the given options that can replace the question mark (?) in the following series and complete the pattern.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739184131239.png\" alt=\"rId44\" width=\"393\" height=\"78\"></p>",
                    question_hi: "<p>23. दिए गए विकल्पों में से उस आकृति को चुनिए जो निम्नलिखित श्रृंखला में प्रश्न चिह्न (?) के स्थान पर आ सकती है और पैटर्न को पूरा कर सकती है।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739184131239.png\" alt=\"rId44\" width=\"393\" height=\"78\"></p>",
                    options_en: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739184131362.png\" alt=\"rId45\" width=\"88\" height=\"85\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739184131485.png\" alt=\"rId46\" width=\"88\" height=\"87\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739184131667.png\" alt=\"rId47\" width=\"88\" height=\"90\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739184131776.png\" alt=\"rId48\" width=\"87\" height=\"85\"></p>"
                    ],
                    options_hi: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739184131362.png\" alt=\"rId45\" width=\"88\" height=\"85\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739184131485.png\" alt=\"rId46\" width=\"88\" height=\"87\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739184131667.png\" alt=\"rId47\" width=\"88\" height=\"90\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739184131776.png\" alt=\"rId48\" width=\"88\" height=\"86\"></p>"
                    ],
                    solution_en: "<p>23.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739184131362.png\" alt=\"rId45\" width=\"88\" height=\"85\"></p>",
                    solution_hi: "<p>23.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739184131362.png\" alt=\"rId45\" width=\"88\" height=\"85\"></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "24",
                    section: "6",
                    question_en: "<p>24. A paper is folded and cut as shown below. How will it appear when unfolded ?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739184131909.png\" alt=\"rId49\" width=\"330\" height=\"86\"></p>",
                    question_hi: "<p>24. नीचे दर्शाए गए अनुसार एक कागज को मोड़कर काटा जाता है। खोलने पर यह कैसा दिखाई देगा ?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739184131909.png\" alt=\"rId49\" width=\"330\" height=\"86\"></p>",
                    options_en: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739184132057.png\" alt=\"rId50\" width=\"88\" height=\"85\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739184132201.png\" alt=\"rId51\" width=\"92\" height=\"87\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739184132295.png\" alt=\"rId52\" width=\"88\" height=\"92\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739184132398.png\" alt=\"rId53\" width=\"89\" height=\"90\"></p>"
                    ],
                    options_hi: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739184132057.png\" alt=\"rId50\" width=\"88\" height=\"85\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739184132201.png\" alt=\"rId51\" width=\"92\" height=\"87\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739184132295.png\" alt=\"rId52\" width=\"88\" height=\"92\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739184132398.png\" alt=\"rId53\" width=\"88\" height=\"89\"></p>"
                    ],
                    solution_en: "<p>24.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739184132295.png\" alt=\"rId52\" width=\"88\" height=\"92\"></p>",
                    solution_hi: "<p>24.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739184132295.png\" alt=\"rId52\" width=\"88\" height=\"92\"></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "25",
                    section: "18",
                    question_en: "<p>25. My father is presently 25 years older than me. The sum of our ages 5 years ago was 39 years. Find my present age.</p>",
                    question_hi: "<p>25. मेरे पिता वर्तमान में मुझसे 25 वर्ष बड़े हैं। 5 वर्ष पूर्व हमारी आयु का योग 39 वर्ष था। मेरी वर्तमान आयु ज्ञात कीजिए।</p>",
                    options_en: [
                        "<p>12 years</p>",
                        "<p>20 years</p>",
                        "<p>15 years</p>",
                        "<p>13 years</p>"
                    ],
                    options_hi: [
                        "<p>12 वर्ष</p>",
                        "<p>20 वर्ष</p>",
                        "<p>15 वर्ष</p>",
                        "<p>13 वर्ष</p>"
                    ],
                    solution_en: "<p>25.(a)<br>Let present age of son = x&nbsp;years<br>Present age of father = (x&nbsp;+ 25) years<br>According to question<br>(x - 5) + (x + 25 - 5) = 39<br>2x&nbsp;+ 15 = 39<br>2x = 39 - 15 = 24<br>x = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>24</mn><mn>2</mn></mfrac></math> = 12 years</p>",
                    solution_hi: "<p>25.(a)<br>माना पुत्र की वर्तमान आयु = x&nbsp;वर्ष<br>पिता की वर्तमान आयु = (x&nbsp;+ 25) वर्ष <br>प्रश्न के अनुसार <br>(x - 5) + (x + 25 - 5) = 39<br>2x&nbsp;+ 15 = 39<br>2x = 39 - 15 = 24<br>x = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>24</mn><mn>2</mn></mfrac></math> = 12 वर्ष</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "26",
                    section: "18",
                    question_en: "<p>26. &lsquo;Doabs&rsquo; are found in which physical feature of India ?</p>",
                    question_hi: "<p>26. \'दोआब (Doabs)\' भारत के किस भौतिक भाग में पाए जाते हैं ?</p>",
                    options_en: [
                        "<p>The Peninsular Plateau</p>",
                        "<p>The Himalayan Mountains</p>",
                        "<p>The Northern Plains</p>",
                        "<p>The Coastal Plains</p>"
                    ],
                    options_hi: [
                        "<p>प्रायद्वीपीय पठार</p>",
                        "<p>हिमालय पर्वत</p>",
                        "<p>उत्तरी मैदान</p>",
                        "<p>तटीय मैदान</p>"
                    ],
                    solution_en: "<p>26.(c) <strong>The Northern Plains</strong>. Doabs are a feature of the Northern Plains of India, particularly in the Punjab plains. A doab refers to the land between two rivers. For instance, the Ganga-Yamuna doab is part of the Indo-Gangetic plain in western and southwestern Uttar Pradesh.</p>",
                    solution_hi: "<p>26.(c) <strong>उत्तरी मैदान। </strong>दोआब भारत के उत्तरी मैदानों की एक विशेषता है, खासकर पंजाब के मैदानों की। दोआब का मतलब दो नदियों के बीच की भूमि से है। उदाहरण के लिए, गंगा-यमुना दोआब उत्तर प्रदेश के पश्चिमी और दक्षिण-पश्चिमी हिस्सों में स्थित इंडो-गंगेटिक मैदानी क्षेत्र का हिस्सा है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "27",
                    section: "18",
                    question_en: "<p>27. In which of the following states of India is the Nokrek Biosphere Reserve located ?</p>",
                    question_hi: "<p>27. नोकरेक बायोस्फीयर रिजर्व भारत के निम्नलिखित में से किस राज्य में स्थित है ?</p>",
                    options_en: [
                        "<p>Nagaland</p>",
                        "<p>Meghalaya</p>",
                        "<p>Karnataka</p>",
                        "<p>Telangana</p>"
                    ],
                    options_hi: [
                        "<p>नागालैंड</p>",
                        "<p>मेघालय</p>",
                        "<p>कर्नाटक</p>",
                        "<p>तेलंगाना</p>"
                    ],
                    solution_en: "<p>27.(b) <strong>Meghalaya.</strong> Nokrek National Park: Established - 1986. It was added to UNESCO\'s Biosphere Reserve list in 2009. States and National parks: Meghalaya - Balpakram National Park. Nagaland - Ntangki National Park. Karnataka - Bannerghatta National Park, Bandipur National Park, Nagarhole National Park, Kudremukh national Park, Anshi national Park. Telangana - Mrugavani national Parks, Mahavir Harina Vanasthali National Park.</p>",
                    solution_hi: "<p>27.(b) <strong>मेघालय।</strong> नोकरेक राष्ट्रीय उद्यान: स्थापना - 1986। इसे 2009 में यूनेस्को की बायोस्फीयर रिजर्व सूची में जोड़ा गया था। राज्य और राष्ट्रीय उद्यान: मेघालय - बालपक्रम राष्ट्रीय उद्यान। नागालैंड - नतांगकी राष्ट्रीय उद्यान। कर्नाटक - बन्नेरघट्टा राष्ट्रीय उद्यान, बांदीपुर राष्ट्रीय उद्यान, नागरहोल राष्ट्रीय उद्यान, कुद्रेमुख राष्ट्रीय उद्यान, अंशी राष्ट्रीय उद्यान। तेलंगाना - मृगवनी राष्ट्रीय उद्यान, महावीर हरिणा वनस्थली राष्ट्रीय उद्यान।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "28",
                    section: "18",
                    question_en: "<p>28. The first edition of the Commonwealth Youth Games was held in the year:</p>",
                    question_hi: "<p>28. कॉमनवेल्थ यूथ गेम्स का पहला संस्करण वर्ष _______में आयोजित किया गया था।</p>",
                    options_en: [
                        "<p>2008</p>",
                        "<p>2004</p>",
                        "<p>2000</p>",
                        "<p>2011</p>"
                    ],
                    options_hi: [
                        "<p>2008</p>",
                        "<p>2004</p>",
                        "<p>2000</p>",
                        "<p>2011</p>"
                    ],
                    solution_en: "<p>28.(c) <strong>2000. </strong>Commonwealth Youth Games - The first edition was held in Edinburgh, Scotland. 2nd edition (December 2004) - Bendigo (Australia). The third edition of the Commonwealth Youth Games (2008) was held in Pune, India. The 2023 Commonwealth Youth Games is held in Port of Spain, Trinidad and Tobago. Motto of the 2023 Commonwealth Youth Games - One Heart, One Nation, One Spirit.</p>",
                    solution_hi: "<p>28.(c)<strong> 2000. </strong>राष्ट्रमंडल युवा खेल - पहला संस्करण स्कॉटलैंड के एडिनबर्ग में आयोजित किया गया था। दूसरा संस्करण (दिसंबर 2004) - बेंडिगो (ऑस्ट्रेलिया) में । राष्ट्रमंडल युवा खेलों (2008) का तीसरा संस्करण भारत के पुणे में आयोजित किया गया था। 2023 राष्ट्रमंडल युवा खेल पोर्ट ऑफ स्पेन, त्रिनिदाद और टोबैगो में आयोजित किए गए हैं। 2023 राष्ट्रमंडल युवा खेलों का आदर्श वाक्य - एक हृदय, एक राष्ट्र, एक आत्मा।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "29",
                    section: "18",
                    question_en: "<p>29. The likelihood of a neutral atom gaining an electron is known as:</p>",
                    question_hi: "<p>29. किसी तटस्थ परमाणु के इलेक्ट्रॉन ग्रहण करने की संभाव्यता क्या कहलाती है ?</p>",
                    options_en: [
                        "<p>electronegativity</p>",
                        "<p>electro attraction</p>",
                        "<p>electron affinity</p>",
                        "<p>electro positivity</p>"
                    ],
                    options_hi: [
                        "<p>विद्युत्-ऋणात्मकता</p>",
                        "<p>विद्युत आकर्षण</p>",
                        "<p>इलेक्ट्रान बन्धुता</p>",
                        "<p>विद्युत धनात्मकता</p>"
                    ],
                    solution_en: "<p>29.(c) <strong>Electron affinity.</strong> It refers to the energy change when an electron is added to a neutral gaseous atom, forming a negative ion. Electronegativity describes an atom\'s tendency to attract electrons toward itself. Electrostatic attraction is the force between oppositely charged particles. Electropositivity, mainly seen in alkali and alkaline earth metals, is an atom\'s tendency to donate electrons and form positive cations.</p>",
                    solution_hi: "<p>29.(c) <strong>इलेक्ट्रान बन्धुता।</strong> यह ऊर्जा परिवर्तन को संदर्भित करता है जब एक इलेक्ट्रॉन को एक उदासीन गैसीय परमाणु में जोड़ा जाता है, जिससे एक ऋणात्मक आयन बनता है। विद्युत्-ऋणात्मकता एक परमाणु की इलेक्ट्रॉनों को अपनी ओर आकर्षित करने की प्रवृत्ति का वर्णन करता है। विद्युतस्थैतिक आकर्षण वह बल है जो विपरीत रूप से आवेशित कणों के बीच कार्य करता है। विद्युत धनात्मकता, जो मुख्यतः क्षार धातुओं और क्षारीय मृदा धातुओं में देखी जाती है, एक परमाणु की इलेक्ट्रॉनों को त्याग करने और धनायन बनाने की प्रवृत्ति है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "30",
                    section: "18",
                    question_en: "<p>30. Who was elected as the Prime Minister of France in September 2024 ?</p>",
                    question_hi: "<p>30. सितंबर 2024 में फ्रांस के प्रधानमंत्री के रूप में किसे चुना गया ?</p>",
                    options_en: [
                        "<p>Emmanuel Macron</p>",
                        "<p>Michel Barnier</p>",
                        "<p>Shigeru Ishiba</p>",
                        "<p>Jafar Hassan</p>"
                    ],
                    options_hi: [
                        "<p>इमैनुएल मैक्रोन</p>",
                        "<p>मिशेल बार्नियर</p>",
                        "<p>शिगेरु इशिबा</p>",
                        "<p>जाफ़र हसन</p>"
                    ],
                    solution_en: "<p>30.(b) <strong>Michel Barnier. </strong>France Official Name: French Republic , with its capital in Paris, is located in Western Europe, bordered by Germany, Belgium, Italy, and Spain.</p>",
                    solution_hi: "<p>30.(b)<strong> मिशेल बार्नियर।</strong> फ्रांस का आधिकारिक नाम: फ्रांसीसी गणराज्य, जिसकी राजधानी पेरिस में है, पश्चिमी यूरोप में स्थित है, जिसकी सीमा जर्मनी, बेल्जियम, इटली और स्पेन से लगती है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "31",
                    section: "18",
                    question_en: "<p>31. Pandit Birju Maharaj was an Indian dancer, composer, singer, and exponent of the ______ \"Kalka-Bindadin\" Gharana of Kathak dance in India.</p>",
                    question_hi: "<p>31. पंडित बिरजू महाराज एक भारतीय नर्तक, संगीतकार, गायक और भारत में कथक नृत्य के _____ \"कालका-बिंदादीन\" घराने के प्रतिपादक थे।</p>",
                    options_en: [
                        "<p>Lucknow</p>",
                        "<p>Hyderabad</p>",
                        "<p>Agra</p>",
                        "<p>Gwalior</p>"
                    ],
                    options_hi: [
                        "<p>लखनऊ</p>",
                        "<p>हैदराबाद</p>",
                        "<p>आगरा</p>",
                        "<p>ग्वालियर</p>"
                    ],
                    solution_en: "<p>31.(a) <strong>Lucknow.</strong> Pandit Birju Maharaj (Awards): Padma Vibhushan (1986), Sangeet Natak Akademi Award (1964), Kalidas Samman (1987). Famous Kathak personalities: Janaki Prasad (Benaras Gharana), Ishwari Prasad (Lucknow Gharana), Shambhu Maharaj (Lucknow Gharana), Raja Chakradhar Singh (Raigarh Gharana).</p>",
                    solution_hi: "<p>31.(a) <strong>लखनऊ। </strong>पंडित बिरजू महाराज (पुरस्कार) : पद्म विभूषण (1986), संगीत नाटक अकादमी पुरस्कार (1964), कालिदास सम्मान (1987)। कथक के प्रसिद्ध प्रतिपादक : जानकी प्रसाद (बनारस घराना), ईश्वरी प्रसाद (लखनऊ घराना), शंभू महाराज (लखनऊ घराना), राजा चक्रधर सिंह (रायगढ़ घराना)।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "32",
                    section: "18",
                    question_en: "<p>32. Which of the following states has the largest net - out migrants ?</p>",
                    question_hi: "<p>32. निम्नलिखित में से किस राज्य में सबसे अधिक शुद्ध प्रवासन (net - out migrants) हैं ?</p>",
                    options_en: [
                        "<p>Maharashtra</p>",
                        "<p>Uttar Pradesh</p>",
                        "<p>Haryana</p>",
                        "<p>Gujarat</p>"
                    ],
                    options_hi: [
                        "<p>महाराष्ट्र</p>",
                        "<p>उत्तर प्रदेश</p>",
                        "<p>हरियाणा</p>",
                        "<p>गुजरात</p>"
                    ],
                    solution_en: "<p>32.(b) <strong>Uttar Pradesh.</strong> A net out-migrant state is one where more people migrate out of the state than those that migrate into the state. As of 2011, Uttar Pradesh and Bihar were the largest source of inter-state migrants while Maharashtra and Delhi were the largest receiver states. Around 83 lakh residents of Uttar Pradesh and 63 lakh residents of Bihar had moved either temporarily or permanently to other states. Around 60 lakh people from across India had migrated to Maharashtra by 2011.</p>",
                    solution_hi: "<p>32.(b) <strong>उत्तर प्रदेश।</strong> शुद्ध प्रवासन राज्य वह है जहाँ राज्य से बाहर जाने वाले लोगों की संख्या राज्य में आने वाले लोगों से अधिक है। 2011 तक, उत्तर प्रदेश और बिहार अंतर-राज्यीय प्रवासियों का सबसे बड़े स्रोत थे, जबकि महाराष्ट्र और दिल्ली सबसे बड़े रिसीवर राज्य थे। उत्तर प्रदेश के लगभग 83 लाख निवासी और बिहार के 63 लाख निवासी अस्थायी या स्थायी रूप से दूसरे राज्यों में चले गए। 2011 तक पूरे भारत से लगभग 60 लाख लोग महाराष्ट्र में चले गए थे।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "33",
                    section: "18",
                    question_en: "<p>33. Which of the following classes has the largest number of animals ?</p>",
                    question_hi: "<p>33. निम्नलिखित में से किस वर्ग में जंतुओं की संख्या सबसे अधिक है ?</p>",
                    options_en: [
                        "<p>Insects</p>",
                        "<p>Pisces</p>",
                        "<p>Mammals</p>",
                        "<p>Reptiles</p>"
                    ],
                    options_hi: [
                        "<p>कीट</p>",
                        "<p>मत्स्य</p>",
                        "<p>स्तनधारी</p>",
                        "<p>सरीसृप</p>"
                    ],
                    solution_en: "<p>33.(a) <strong>Insects. </strong>Insects, belonging to the class Insecta, form the largest group within the phylum Arthropoda, which itself is the largest phylum in the animal kingdom. Insects account for approximately 75% of all species in Arthropoda. Their body is divided into three parts: head, thorax, and abdomen, and they possess an exoskeleton made of chitin. Insects have an open circulatory system and are triploblastic in nature, with three embryonic layers.</p>",
                    solution_hi: "<p>33.(a) <strong>कीट।</strong> कीट, इन्सेक्टा वर्ग से संबंधित, संघ आर्थ्रोपोडा के भीतर सबसे बड़ा समूह बनाते हैं, जो स्वयं जन्तु जगत में सबसे बड़ा संघ है। आर्थ्रोपोडा की सभी प्रजातियों में से लगभग 75% कीट हैं। उनका शरीर तीन भागों में विभाजित है: सिर, वक्ष और उदर, और उनके पास काइटिन से बना एक बाह्यकंकाल होता है। कीटों में एक खुला परिसंचरण तंत्र होता है और वे प्रकृति में ट्रिपलोब्लास्टिक होते हैं, जिसमें तीन भ्रूण परतें होती हैं।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "34",
                    section: "18",
                    question_en: "<p>34. Which musical instrument is played by Pt. Ravi Shankar ?</p>",
                    question_hi: "<p>34. पं. रविशंकर किस वाद्ययंत्र के वादक थे ?</p>",
                    options_en: [
                        "<p>Been</p>",
                        "<p>Sarod</p>",
                        "<p>Sarangi</p>",
                        "<p>Sitar</p>"
                    ],
                    options_hi: [
                        "<p>बीन</p>",
                        "<p>सरोद</p>",
                        "<p>सारंगी</p>",
                        "<p>सितार</p>"
                    ],
                    solution_en: "<p>34.(d) <strong>Sitar. </strong>Pt. Ravi Shankar received the Bharat Ratna in 1999, the Padma Vibhushan in 1981, the Padma Bhushan in 1967, and the Sangeet Natak Akademi Award in 1962. Famous sitar players: Wahid Khan, Mushtaq Ali Khan, Nikhil Banerjee, Shamim Ahmed Khan, Shahid Parvez, Budhaditya Mukherjee, Kartick Kumar, and Harvinder Kumar Sharma.</p>",
                    solution_hi: "<p>34.(d) <strong>सितार। </strong>पं.रविशंकर को 1999 में भारत रत्न, 1981 में पद्म विभूषण, 1967 में पद्म भूषण और 1962 में संगीत नाटक अकादमी पुरस्कार से सम्मानित किया गया। प्रसिद्ध सितार वादक: वाहिद खान, मुश्ताक अली खान, निखिल बनर्जी, शमीम अहमद खान, शाहिद परवेज , बुधादित्य मुखर्जी, कार्तिक कुमार, और हरविंदर कुमार शर्मा।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "35",
                    section: "18",
                    question_en: "<p>35. Who was the first speaker of the Lok Sabha after India&rsquo;s Independence ?</p>",
                    question_hi: "<p>35. भारत की आजादी के बाद लोकसभा के पहले/पहली अध्यक्ष कौन थे/थीं ?</p>",
                    options_en: [
                        "<p>Ganesh V Mavalankar</p>",
                        "<p>Meira Kumar</p>",
                        "<p>M Ananthasayanam</p>",
                        "<p>MA Ayyangar</p>"
                    ],
                    options_hi: [
                        "<p>गणेश वी. मावलंकर</p>",
                        "<p>मीरा कुमार</p>",
                        "<p>एम अनंतशयनम</p>",
                        "<p>एमए अय्यंगार</p>"
                    ],
                    solution_en: "<p>35.(a) <strong>Ganesh V Mavalankar</strong> was the Chairman of the Committee on the Functions of the Constituent Assembly of India. Ganesh Vasudev Mavalankar has been conferred the title of \'Father of Lok Sabha\' by Jawaharlal Nehru. M. A. Ayyangar was the first deputy speaker of the Lok Sabha. Meira Kumar was the first woman speaker of Lok Sabha (2009 - 2014).</p>",
                    solution_hi: "<p>35.(a) <strong>गणेश वी. मावलंकर </strong>भारतीय संविधान सभा के कार्यों संबंधी समिति के अध्यक्ष थे। गणेश वासुदेव मावलंकर को जवाहरलाल नेहरू द्वारा \'फादर ऑफ लोकसभा\' की उपाधि दी गई है। एम.ए.अयंगर लोकसभा के प्रथम उपाध्यक्ष थे। मीरा कुमार लोकसभा की प्रथम महिला अध्यक्ष (2009 - 2014) थीं।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "36",
                    section: "18",
                    question_en: "<p>36. Which Act was enacted by the British which regulated the manufacture, sale, possession and transport of firearms ?</p>",
                    question_hi: "<p>36. अंग्रेजों द्वारा कौन-सा अधिनियम बनाया गया था जो आग्नेयास्त्रों (firearms) के निर्माण, बिक्री , अधिकार और परिवहन को नियंत्रित करता था ?</p>",
                    options_en: [
                        "<p>Indian Arms Act, 1878</p>",
                        "<p>The Arms Act,1857</p>",
                        "<p>The Arm Rules, 1839</p>",
                        "<p>The Firearms Act, 1871</p>"
                    ],
                    options_hi: [
                        "<p>भारतीय शस्त्र अधिनियम, 1878 (Indian Arms Act)</p>",
                        "<p>शस्त्र अधिनियम, 1857 (The Arms Act)</p>",
                        "<p>शस्त्र नियम, 1839 (The Arm Rules)</p>",
                        "<p>आग्नेयास्त्र अधिनियम, 1871 (The Firearms Act)</p>"
                    ],
                    solution_en: "<p>36.(a)<strong> Indian Arms Act, 1878.</strong> It was enacted under the authority of Lord Lytton, the then Viceroy of India. It prohibited Indians from possessing, producing, or selling firearms without a government license. The Act was discriminatory as it did not apply to British, Anglo-Indians, Europeans, and certain government personnel.</p>",
                    solution_hi: "<p>36.(a) <strong>भारतीय शस्त्र अधिनियम, 1878। </strong>इसे भारत के तत्कालीन वायसराय लॉर्ड लिटन के अधिकार के तहत अधिनियमित किया गया था। इसने भारतीयों को सरकारी लाइसेंस के बिना आग्नेयास्त्र रखने, उत्पादन करने या बेचने से प्रतिबंधित कर दिया। यह अधिनियम भेदभावपूर्ण था क्योंकि यह ब्रिटिश, एंग्लो-इंडियन, यूरोपीय और कुछ सरकारी कर्मियों पर लागू नहीं होता था।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "37",
                    section: "18",
                    question_en: "<p>37. Which famous experiment was done by Michael Faraday in 1831 ?</p>",
                    question_hi: "<p>37. माइकल फैराडे ने 1831 में कौन सा प्रसिद्ध प्रयोग किया था ?</p>",
                    options_en: [
                        "<p>Discovery of quantum magnetometers</p>",
                        "<p>Discovery of law of elasticity</p>",
                        "<p>Discovery of electromagnetic induction</p>",
                        "<p>Discovery of natural radioactivity</p>"
                    ],
                    options_hi: [
                        "<p>क्वांटम मैग्नेटोमीटर की खोज</p>",
                        "<p>प्रत्यास्थता के नियम की खोज</p>",
                        "<p>विद्युत चुम्बकीय प्रेरण की खोज</p>",
                        "<p>प्राकृतिक रेडियोधर्मिता की खोज</p>"
                    ],
                    solution_en: "<p>37.(c) <strong>Discovery of electromagnetic induction. </strong>Faraday\'s law states that an electromotive force is generated in an electrical conductor encircling a varying magnetic flux. Faraday\'s law of induction underpins the operation of various devices, including generators, transformers, inductors, and electric motors.</p>",
                    solution_hi: "<p>37.(c) <strong>विद्युत चुम्बकीय प्रेरण की खोज।</strong> फैराडे का नियम कहता है कि एक विद्युत चालक में एक विद्युत चालक बल उत्पन्न होता है जो एक परिवर्तनशील चुंबकीय प्रवाह को घेरे रहता है। फैराडे का प्रेरण का नियम जनरेटर, ट्रांसफार्मर, प्रेरक और इलेक्ट्रिक मोटर सहित विभिन्न उपकरणों के संचालन को आधार प्रदान करता है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "38",
                    section: "18",
                    question_en: "<p>38. What is the size of a standard table tennis ball ?</p>",
                    question_hi: "<p>38. एक मानक टेबल टेनिस गेंद का आकार क्या होता है ?</p>",
                    options_en: [
                        "<p>41 mm</p>",
                        "<p>39 mm</p>",
                        "<p>40 mm</p>",
                        "<p>42 mm</p>"
                    ],
                    options_hi: [
                        "<p>41 mm</p>",
                        "<p>39 mm</p>",
                        "<p>40 mm</p>",
                        "<p>42 mm</p>"
                    ],
                    solution_en: "<p>38.(c)<strong> 40 mm. </strong>Table Tennis Equipment: Ball: The ball is spherical, orange or white in colour made of celluloid or similar plastic material with a diameter of 40 mm, weight 2.7g. Racket: Size about 15 cm across and 25cm long including the handle. Scoreboard: The board used for scoring is called Table Tennis Scoreboard. The net is suspended by a cord attached at each end to an upright post 15.25 cm high. The colour of net assembly should be of dark green, dark blue or black and have a white top not more than 15 mm wide.</p>",
                    solution_hi: "<p>38.(c) <strong>40 mm. </strong>टेबल टेनिस उपकरण: गेंद: गोलाकार, नारंगी या सफेद रंग की होती है जो सेल्यूलॉइड या इसी तरह की प्लास्टिक सामग्री से बनी होती है जिसका व्यास 40 mm, वजन 2.7 ग्राम होता है। रैकेट : लगभग 15 सेमी व्यास का और हैंडल सहित 25 सेमी लंबा। स्कोरबोर्ड: स्कोरिंग के लिए उपयोग किए जाने वाले बोर्ड को टेबल टेनिस स्कोरबोर्ड कहा जाता है। नेट को 15.25 सेमी ऊंचे एक सीधे खंभे से प्रत्येक सिरे पर लगी एक रस्सी द्वारा लटकाया जाता है। नेट असेंबली का रंग गहरा हरा, गहरा नीला या काला होना चाहिए और इसका सफेद शीर्ष 15 mm से अधिक चौड़ा नहीं होना चाहिए।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "39",
                    section: "18",
                    question_en: "<p>39. Who established Shantiniketan and when ?</p>",
                    question_hi: "<p>39. शांतिनिकेतन की स्थापना किसने और कब की ?</p>",
                    options_en: [
                        "<p>Mahatma Gandhi, 1910</p>",
                        "<p>Ravindranath Tagore, 1901</p>",
                        "<p>Ravindranath Tagore, 1905</p>",
                        "<p>Devendranath Tagore, 1915</p>"
                    ],
                    options_hi: [
                        "<p>महात्मा गांधी, 1910 में</p>",
                        "<p>रवींद्रनाथ टैगोर,1901 में</p>",
                        "<p>रवींद्रनाथ टैगोर, 1905 में</p>",
                        "<p>देवेंद्रनाथ टैगोर, 1915 में</p>"
                    ],
                    solution_en: "<p>39.(b) <strong>Ravindranath Tagore, 1901.</strong> Shantiniketan Initially founded as an ashram by Debendranath Tagore in 1863 and later expanded by Rabindranath Tagore into a center for learning and culture, it evolved into Visva-Bharati University in 1921. In 2023, Shantiniketan was recognized as India\'s 41st UNESCO World Heritage Site. Other Institutes and Founders : Rishi Valley (1926, Andhra Pradesh) - J. Krishnamurti; Auroville (1968, Tamil Nadu) - Sri Aurobindo; Sabarmati Ashram (1915, Gujarat) - Mahatma Gandhi.</p>",
                    solution_hi: "<p>39.(b) <strong>रवींद्रनाथ टैगोर, 1901 में ।</strong> शांतिनिकेतन की स्थापना 1863 में देबेन्द्रनाथ टैगोर ने एक आश्रम के रूप में की थी और बाद में रवींद्रनाथ टैगोर ने इसे शिक्षा और संस्कृति के केंद्र के रूप में विस्तारित किया, यह 1921 में विश्वभारती विश्वविद्यालय के रूप में विकसित हुआ। 2023 में, शांतिनिकेतन को भारत के 41वें यूनेस्को विश्व धरोहर स्थल के रूप में मान्यता दी गई। अन्य संस्थान और संस्थापक: ऋषि घाटी (1926, आंध्र प्रदेश) - जे. कृष्णमूर्ति; ऑरोविले (1968, तमिलनाडु) - श्री अरबिंदो; साबरमती आश्रम (1915, गुजरात) - महात्मा गांधी।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "40",
                    section: "18",
                    question_en: "<p>40. The Prevention of Seditious Meetings Act was passed in ______by the colonial Government.</p>",
                    question_hi: "<p>40. देशद्रोही बैठकों की रोकथाम अधिनियम को औपनिवेशिक सरकार द्वारा______में पारित किया&nbsp;गया था।</p>",
                    options_en: [
                        "<p>1898</p>",
                        "<p>1903</p>",
                        "<p>1912</p>",
                        "<p>1907</p>"
                    ],
                    options_hi: [
                        "<p>1898</p>",
                        "<p>1903</p>",
                        "<p>1912</p>",
                        "<p>1907</p>"
                    ],
                    solution_en: "<p>40.(d) <strong>1907. </strong>The Prevention of Seditious Meetings Act was brought under the tenure of Lord Minto. The act was intended to prevent public meetings that could promote sedition or disturb public tranquility. The act allowed the District Magistrate or Commissioner of Police to prohibit public meetings in certain areas if they believed the meeting would promote sedition or disaffection.</p>",
                    solution_hi: "<p>40.(d) <strong>1907. </strong>लॉर्ड मिंटो के कार्यकाल में देशद्रोही बैठकों की रोकथाम अधिनियम लाया गया था। इस अधिनियम का उद्देश्य उन सार्वजनिक बैठकों को रोकना था जो राजद्रोह को बढ़ावा दे सकती थीं या सार्वजनिक शांति को भंग कर सकती थीं। इस अधिनियम के तहत जिला मजिस्ट्रेट या पुलिस आयुक्त को कुछ क्षेत्रों में ऐसे सार्वजनिक सभाओं पर रोक लगाने की अनुमति दी गई, जिस सभा से उन्हें लगता हो कि इससे देशद्रोही या असंतोष को बढ़ावा मिलेगा।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "41",
                    section: "18",
                    question_en: "<p>41. &lsquo;Castling&rsquo; is related to which of the following sports ?</p>",
                    question_hi: "<p>41. &lsquo;कैसलिंग (Castling) निम्नलिखित में से किस खेल से संबंधित है ?</p>",
                    options_en: [
                        "<p>Table Tennis</p>",
                        "<p>Billiards</p>",
                        "<p>Tennis</p>",
                        "<p>Chess</p>"
                    ],
                    options_hi: [
                        "<p>टेबल टेनिस</p>",
                        "<p>बिलियर्ड्स</p>",
                        "<p>टेनिस</p>",
                        "<p>शतरंज</p>"
                    ],
                    solution_en: "<p>41.(d) <strong>Chess.</strong> Castling is a unique chess move involving the king and one of the original rooks. Key chess terms include Bishop, Pawn, Knight, Rook, King, Checkmate, Queen, and Zugzwang.</p>",
                    solution_hi: "<p>41.(d) <strong>शतरंज। </strong>&lsquo;कैसलिंग एक अनोखी शतरंज चाल है जिसमें राजा और मूल हाथी में से एक का प्रयोग होता है। प्रमुख शतरंज शब्दावली में बिशप, पॉन, नाइट, रूक, किंग, चेकमेट, क्वीन और ज़ुगज़्वांग शामिल हैं।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "42",
                    section: "18",
                    question_en: "<p>42. The loss of water in the form of water droplets from leaves of plants is called _______.</p>",
                    question_hi: "<p>42. पौधों की पत्तियों में से जल वाष्प के रूप में जल का क्षय (loss) होना ______कहलाता है।</p>",
                    options_en: [
                        "<p>Translocation</p>",
                        "<p>Plasmolysis</p>",
                        "<p>Pressure gradient</p>",
                        "<p>Guttation</p>"
                    ],
                    options_hi: [
                        "<p>स्थानांतरण (Translocation)</p>",
                        "<p>जीवद्रव्य कुंचन (Plasmolysis)</p>",
                        "<p>दाब प्रवणता (Pressure gradient)</p>",
                        "<p>बिंदुस्राव (Guttation)</p>"
                    ],
                    solution_en: "<p>42.(d) <strong>Guttation.</strong> Translocation is the movement of materials from leaves to other tissues throughout the plant. Plasmolysis is defined as the process of contraction or shrinkage of the protoplasm of a plant cell and is caused due to the loss of water in the cell. Pressure gradient refers to the difference in air pressure between two points in the atmosphere or on the surface of the earth.</p>",
                    solution_hi: "<p>42.(d) <strong>बिंदुस्राव। </strong>स्थानांतरण पूरे पौधे में पत्तियों से दूसरे ऊतकों तक पदार्थों का संचरण है। जीवद्रव्य कुंचन को पौधे की कोशिका के जीवद्रव्य के संकुचन की प्रक्रिया के रूप में परिभाषित किया जाता है और यह कोशिका में जल की कमी के कारण होता है। दाब प्रवणता वायुमंडल में या पृथ्वी की सतह पर दो बिंदुओं के बीच वायु दाब में अंतर को संदर्भित करती है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "43",
                    section: "18",
                    question_en: "<p>43. Who among the following is known as &lsquo;Captain Cool&rsquo; in Indian Cricket ?</p>",
                    question_hi: "<p>43. भारतीय क्रिकेट में निम्नलिखित में से किसे \'कैप्टन कूल\' के नाम से जाना जाता है ?</p>",
                    options_en: [
                        "<p>Virat Kohli</p>",
                        "<p>Mahendra Singh Dhoni</p>",
                        "<p>Kapil Dev</p>",
                        "<p>Sachin Tendulkar</p>"
                    ],
                    options_hi: [
                        "<p>विराट कोहली</p>",
                        "<p>महेन्द्र सिंह धोनी</p>",
                        "<p>कपिल देव</p>",
                        "<p>सचिन तेंदुलकर</p>"
                    ],
                    solution_en: "<p>43.(b)<strong> Mahendra Singh Dhoni. </strong>He is also known as \"Thala,\" and \"MSD,\". Virat Kohli, nicknamed \"Cheeku,\" \"King Kohli,\" and \"The Run Machine,\" is renowned for his batting. Kapil Dev, known as the \"Haryana Hurricane,\" was famous for his aggressive play, while Sachin Tendulkar is affectionately called the \"God of Cricket,\" \"Master Blaster,\" and \"Little Master.\"</p>",
                    solution_hi: "<p>43.(b) <strong>महेंद्र सिंह धोनी। </strong>उन्हें \"थाला\" और \"MSD\" के नाम से भी जाना जाता है। विराट कोहली, जिन्हें \"चीकू\", \"किंग कोहली\" और \"द रन मशीन\" के नाम से जाना जाता है, अपनी बल्लेबाजी के लिए प्रसिद्ध हैं। कपिल देव, जिन्हें \"हरियाणा हरिकेन\" के नाम से जाना जाता है, अपने आक्रामक खेल के लिए प्रसिद्ध थे, जबकि सचिन तेंदुलकर को प्यार से \"क्रिकेट का भगवान\", \"मास्टर ब्लास्टर\" और \"लिटिल मास्टर\" कहा जाता है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "44",
                    section: "18",
                    question_en: "<p>44. Match the following items.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739184132522.png\" alt=\"rId54\" width=\"512\" height=\"145\"></p>",
                    question_hi: "<p>44. निम्नलिखित मदों का मिलान कीजिए। <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739184132688.png\" alt=\"rId55\" width=\"460\" height=\"129\"></p>",
                    options_en: [
                        "<p>i (d), ii (a), iii (b), iv (c)</p>",
                        "<p>i (b), ii (a), iii (d), iv (c)</p>",
                        "<p>i (c), ii (d), iii (a), iv (b)</p>",
                        "<p>i (b), ii (c), iii (d), iv (a)</p>"
                    ],
                    options_hi: [
                        "<p>i (d), ii (a), iii (b), iv (c)</p>",
                        "<p>i (b), ii (a), iii (d), iv (c)</p>",
                        "<p>i (c), ii (d), iii (a), iv (b)</p>",
                        "<p>i (b), ii (c), iii (d), iv (a) </p>"
                    ],
                    solution_en: "<p>44.(d) <strong>i (b), ii (c), iii (d), iv (a).</strong> Global System of Trade Preferences (1988) - Promotes trade among developing countries. India-Gulf Cooperation Council Free Trade Agreement (2004) - Aims to liberalize trade relations and explore a Free Trade Agreement. India-Republic of Korea Comprehensive Economic Partnership Agreement (2010) - Enhances economic cooperation by reducing tariffs on goods. 7th Trade Policy Review (2021) - A WTO mechanism for regularly reviewing member countries\' trade policies.</p>",
                    solution_hi: "<p>44.(d) <strong>i (b), ii (c), iii (d), iv (a).</strong> व्यापार प्राथमिकताओं की वैश्विक प्रणाली (1988) - विकासशील देशों के बीच व्यापार को बढ़ावा देती है। भारत-खाड़ी सहयोग परिषद मुक्त व्यापार समझौता (2004) - इसका उद्देश्य व्यापार संबंधों को उदार बनाना और मुक्त व्यापार समझौते की संभावना तलाशना है। भारत-कोरिया गणराज्य व्यापक आर्थिक भागीदारी समझौता (2010) - वस्तुओं पर शुल्क कम करके आर्थिक सहयोग को बढ़ाना है। 7वीं व्यापार नीति समीक्षा (2021) - सदस्य देशों की व्यापार नीतियों की नियमित समीक्षा के लिए एक WTO क्रियाविधि।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "45",
                    section: "18",
                    question_en: "<p>45. Who is the Indian batsman after whom the Ranji Trophy is named ?</p>",
                    question_hi: "<p>45. रणजी ट्रॉफी का नाम किस भारतीय बल्लेबाज के नाम पर रखा गया है ?</p>",
                    options_en: [
                        "<p>Ranjit Singh Bedi</p>",
                        "<p>Ranjit Singhji</p>",
                        "<p>Ranjit Patel</p>",
                        "<p>Ranjit Wadekar</p>"
                    ],
                    options_hi: [
                        "<p>रणजीत सिंह बेदी</p>",
                        "<p>रणजीत सिंह जी</p>",
                        "<p>रंजीत पटेल</p>",
                        "<p>रंजीत वाडेकर</p>"
                    ],
                    solution_en: "<p>45.(b) <strong>Ranjit Singhji.</strong> He was ruler of the princely state of Nawanagar (Gujarat) from 1907 to 1933. Ranji Trophy was started by the Board of Control for Cricket in India (BCCI) in 1934 after his death in 1933. The Ranji Trophy is a premier domestic first-class cricket championship played in India and organized annually by the BCCI (Headquarters - Mumbai).</p>",
                    solution_hi: "<p>45.(b) <strong>रणजीत सिंह जी।</strong> वे 1907 से 1933 तक नवानगर (गुजरात) रियासत के शासक थे। 1933 में उनकी मृत्यु के बाद 1934 में भारतीय क्रिकेट कंट्रोल बोर्ड (BCCI) द्वारा रणजी ट्रॉफी की शुरुआत की गई थी। रणजी ट्रॉफी भारत में खेली जाने वाली एक प्रमुख घरेलू प्रथम श्रेणी क्रिकेट चैंपियनशिप है और इसका आयोजन BCCI (मुख्यालय - मुंबई) द्वारा प्रतिवर्ष किया जाता है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "46",
                    section: "18",
                    question_en: "<p>46. For which of the following minerals are Jharia and Raniganj known ?</p>",
                    question_hi: "<p>46. झरिया (Jharia) और रानीगंज (Raniganj) को निम्नलिखित में से किस खनिज के लिए जाना जाता है ?</p>",
                    options_en: [
                        "<p>Marble</p>",
                        "<p>Coal</p>",
                        "<p>Copper</p>",
                        "<p>Gold</p>"
                    ],
                    options_hi: [
                        "<p>संगमरमर</p>",
                        "<p>कोयला</p>",
                        "<p>कॉपर</p>",
                        "<p>सोना</p>"
                    ],
                    solution_en: "<p>46.(b) <strong>Coal.</strong> Major coal resources in India are found in the Damodar Valley, spanning West Bengal and Jharkhand, with key coalfields at Jharia, Raniganj, and Bokaro. Other significant coal deposits are located in the valleys of the Godavari, Mahanadi, Son, and Wardha. Marble: Found mainly in Rajasthan. Copper: Found in Singhbhum (Jharkhand), Balaghat (Madhya Pradesh), and Khetri (Rajasthan). Gold: Mainly found in Kolar region of Karnataka.</p>",
                    solution_hi: "<p>46.(b) <strong>कोयला। </strong>भारत में प्रमुख कोयला संसाधन दामोदर घाटी में पाए जाते हैं, जो पश्चिम बंगाल और झारखंड में फैली हुई है, जिसमें झरिया, रानीगंज और बोकारो में प्रमुख कोयला क्षेत्र हैं। अन्य महत्वपूर्ण कोयला भंडार गोदावरी, महानदी, सोन और वर्धा की घाटियों में स्थित हैं। संगमरमर: मुख्य रूप से राजस्थान में पाया जाता है। तांबा: सिंहभूम (झारखंड), बालाघाट (मध्य प्रदेश) और खेतड़ी (राजस्थान) में पाया जाता है। सोना: मुख्य रूप से कर्नाटक के कोलार क्षेत्र में पाया जाता है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "47",
                    section: "18",
                    question_en: "<p>47. In ethylene (C<sub>2</sub>H<sub>4</sub>), hybridisation of carbon atoms is:</p>",
                    question_hi: "<p>47. एथिलीन (C<sub>2</sub>H<sub>4</sub>) में, कार्बन परमाणुओं का संकरण क्या है ?</p>",
                    options_en: [
                        "<p>sp<sup>2</sup></p>",
                        "<p>sp<sup>3</sup>d</p>",
                        "<p>Sp</p>",
                        "<p>sp<sup>3</sup></p>"
                    ],
                    options_hi: [
                        "<p>sp<sup>2</sup></p>",
                        "<p>sp<sup>3</sup>d</p>",
                        "<p>Sp</p>",
                        "<p>sp<sup>3</sup></p>"
                    ],
                    solution_en: "<p>47.(a) <strong>sp<sup>2</sup>. </strong>In this hybridization, one \'s\' and two \'p\' orbitals combine to form three new sp&sup2; hybrid orbitals, all with the same shape and energy. Hybridization refers to the process of merging two atomic orbitals to create new hybridized orbitals.</p>",
                    solution_hi: "<p>47.(a) <strong>sp<sup>2</sup>.</strong> इस संकरण में, एक \'s\' और दो \'p\' कक्षक संयोजित होकर तीन नए sp&sup2; संकर कक्षक बनाते हैं, सभी का आकार और ऊर्जा समान होती है। संकरण से तात्पर्य दो परमाणु कक्षाओं को मिलाकर नई संकरित कक्षाएँ बनाने की प्रक्रिया से है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "48",
                    section: "18",
                    question_en: "<p>48. The \'Bathukamma\' festival is associated with which state ?</p>",
                    question_hi: "<p>48. \'बतुकम्मा (Bathukamma)\' त्यौहार किस राज्य से संबंधित है ?</p>",
                    options_en: [
                        "<p>Telangana</p>",
                        "<p>Sikkim</p>",
                        "<p>Punjab</p>",
                        "<p>Tripura</p>"
                    ],
                    options_hi: [
                        "<p>तेलंगाना</p>",
                        "<p>सिक्किम</p>",
                        "<p>पंजाब</p>",
                        "<p>त्रिपुरा</p>"
                    ],
                    solution_en: "<p>48.(a) <strong>Telangana. </strong>Bathukamma is a traditional floral festival celebrated primarily by women in Telangana, typically during the nine days of the Navaratri celebrations. Other states and related festivals: Sikkim - Losar, Saga Dawa. Punjab - Lohri, Baisakhi. Tripura - Kharchi Puja.</p>",
                    solution_hi: "<p>48.(a) <strong>तेलंगाना ।</strong> बतुकम्मा एक पारंपरिक पुष्प त्योहार है जो मुख्य रूप से तेलंगाना में महिलाओं द्वारा मनाया जाता है, आमतौर पर नवरात्रि उत्सव के नौ दिनों के दौरान। अन्य राज्य एवं संबंधित त्योहार : सिक्किम - लोसार, सागा दावा। पंजाब - लोहड़ी, बैसाखी। त्रिपुरा - खर्ची पूजा।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "49",
                    section: "18",
                    question_en: "<p>49. Which team won the Junior Hockey Asia Cup 2024 ?</p>",
                    question_hi: "<p>49. जूनियर हॉकी एशिया कप 2024 किस टीम ने जीता ?</p>",
                    options_en: [
                        "<p>Pakistan</p>",
                        "<p>Malaysia</p>",
                        "<p>India</p>",
                        "<p>Japan</p>"
                    ],
                    options_hi: [
                        "<p>पाकिस्तान</p>",
                        "<p>मलेशिया</p>",
                        "<p>भारत</p>",
                        "<p>जापान</p>"
                    ],
                    solution_en: "<p>49.(c) <strong>India </strong>defeated Pakistan 5-3 in the final held in Muscat to win their fifth Junior Hockey Asia Cup title and their third consecutive championship.</p>",
                    solution_hi: "<p>49.(c) <strong>भारत। </strong>भारत ने मस्कट में आयोजित फाइनल में पाकिस्तान को 5-3 से हराकर अपना पांचवां जूनियर हॉकी एशिया कप खिताब जीता और यह उनकी लगातार तीसरी चैंपियनशिप थी।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "50",
                    section: "17",
                    question_en: "<p>50. Using Fleming&rsquo;s right-hand rule, in which direction will the current flow if the direction&nbsp;of magnetic field is towards north and the conductor is moving vertically upward ?</p>",
                    question_hi: "<p>50. फ्लेमिंग के दायें हाथ के नियम का उपयोग करते हुए, यदि चुंबकीय क्षेत्र की दिशा उत्तर की ओर है और चालक ऊर्ध्वाधर रूप से ऊपर की ओर बढ़ रहा है, तो विद्युत धारा किस दिशा में प्रवाहित होगी ?</p>",
                    options_en: [
                        "<p>Towards west</p>",
                        "<p>Towards south-west</p>",
                        "<p>Towards east</p>",
                        "<p>Towards south</p>"
                    ],
                    options_hi: [
                        "<p>पश्चिम की ओर</p>",
                        "<p>दक्षिण-पश्चिम की ओर</p>",
                        "<p>पूर्व की ओर</p>",
                        "<p>दक्षिण की ओर</p>"
                    ],
                    solution_en: "<p>50.(a) <strong>Towards west.</strong> If the forefinger indicates the direction of the magnetic field and the thumb shows the direction of motion of the conductor, then the middle finger will show the direction of induced current. This simple rule is called Fleming&rsquo;s right-hand rule.</p>",
                    solution_hi: "<p>50.(a) <strong>पश्चिम की ओर। </strong>यदि तर्जनी चुंबकीय क्षेत्र की दिशा को दर्शाती है और अंगूठा चालक की गति की दिशा को दर्शाता है, तो मध्यमा प्रेरित धारा की दिशा दर्शाएगी। इस सरल नियम को फ्लेमिंग के दायाँ हाथ नियम कहते हैं।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "51",
                    section: "17",
                    question_en: "<p>51. The marked price of a mobile phone at a showroom is ₹25,000 and is sold at successive discounts of 20%, 15% and 10%. Find its selling price.</p>",
                    question_hi: "<p>51. एक शोरूम में एक मोबाइल फोन का अंकित मूल्य ₹25,000 है और इसे 20%, 15% और 10% की क्रमिक छूटों पर बेचा जाता है। इसका विक्रय मूल्य ज्ञात कीजिए।</p>",
                    options_en: [
                        "<p>₹15,000</p>",
                        "<p>₹15,300</p>",
                        "<p>₹14,960</p>",
                        "<p>₹14,000 </p>"
                    ],
                    options_hi: [
                        "<p>₹15,000</p>",
                        "<p>₹15,300</p>",
                        "<p>₹14,960</p>",
                        "<p>₹14,000</p>"
                    ],
                    solution_en: "<p>51.(b)<br>Selling Price = 25000 &times;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>80</mn><mn>100</mn></mfrac></math>&nbsp;&times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>85</mn><mn>100</mn></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>90</mn><mn>100</mn></mfrac></math>&nbsp;<br>= 25 &times; 8 &times; 85 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>9</mn><mn>10</mn></mfrac></math> = 15300</p>",
                    solution_hi: "<p>51.(b)<br>Selling Price = 25000 &times;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>80</mn><mn>100</mn></mfrac></math>&nbsp;&times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>85</mn><mn>100</mn></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>90</mn><mn>100</mn></mfrac></math>&nbsp;<br>= 25 &times; 8 &times; 85 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>9</mn><mn>10</mn></mfrac></math> = 15300</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "52",
                    section: "17",
                    question_en: "<p>52. Which of the following can be the value of &lsquo;k&rsquo; so that the number 217924k is divisible by 6 ?</p>",
                    question_hi: "<p>52. निम्नलिखित में से कौन-सा \'k\' का मान हो सकता है जिससे कि संख्या 217924k, 6 से विभाज्य हो ?</p>",
                    options_en: [
                        "<p>4</p>",
                        "<p>6</p>",
                        "<p>2</p>",
                        "<p>0</p>"
                    ],
                    options_hi: [
                        "<p>4</p>",
                        "<p>6</p>",
                        "<p>2</p>",
                        "<p>0</p>"
                    ],
                    solution_en: "<p>52.(c) A number is divisible by 6 if it is divisible by both 2 and 3:<br>Divisibility of 2 : A number is divisible by 2 if it is an even number, <br>Divisibility of 3 : A number is divisible by 3 if the sum of all digits of that number is divisible by 3.<br>Now, 217924k <br>&rArr; 2 + 1 + 7 + 9 + 2 + 4 + k = 25 + k<br>The possible value of k can be 0, 2 , 4 , 6 , 8<br>But the sum of number must be multiple of 3 so the value of k will be <strong>2</strong></p>",
                    solution_hi: "<p>52.(c) एक संख्या 6 से विभाज्य है यदि वह 2 और 3 दोनों से विभाज्य होगा । <br>2 की विभाज्यता: एक संख्या 2 से विभाज्य होती है यदि वह एक सम संख्या है। <br>3 की विभाज्यता: एक संख्या 3 से विभाज्य होती है यदि उस संख्या के सभी अंकों का योग 3 से विभाज्य हो।<br>अब, 217924k <br>&rArr; 2 + 1 + 7 + 9 + 2 + 4 + k = 25 + k<br>K का संभावित मान = 0, 2, 4, 6, 8 <br>लेकिन संख्या का योग 3 का गुणज होना चाहिए इसलिए k का मान <em>2</em> होगा।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "53",
                    section: "17",
                    question_en: "<p>53. Find the HCF of 258, 301, and 387.</p>",
                    question_hi: "<p>53. 258, 301 और 387 का महत्तम समपवर्तक ज्ञात कीजिए।</p>",
                    options_en: [
                        "<p>29</p>",
                        "<p>27</p>",
                        "<p>22</p>",
                        "<p>43</p>"
                    ],
                    options_hi: [
                        "<p>29</p>",
                        "<p>27</p>",
                        "<p>22</p>",
                        "<p>43</p>"
                    ],
                    solution_en: "<p>53.(d)<br>HCF is always either the difference of two numbers or factors of difference of two numbers. <br>Difference of (301 , 258) = 301 - 258 = 43 and difference of 387-301 = 86 , one of the factor of 86 is 43, which completely divides the given no&rsquo;s <br>So, HCF = 43</p>",
                    solution_hi: "<p>53.(d)<br>महत्तम समपवर्तक हमेशा या तो दो संख्याओं का अंतर होता है या दो संख्याओं के अंतर का गुणनखंड होता है।<br>(301, 258) का अंतर = 301 - 258 = 43 और 387-301 का अंतर = 86, 86 का एक गुणनखंड 43 है, जो दी गई संख्या को पूरी तरह से विभाजित करता है<br>तो, महत्तम समपवर्तक = 43</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "54",
                    section: "17",
                    question_en: "<p>54. Simplify the following expression.<br>2<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> &times; 3<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>5</mn></mfrac></math> + 5<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>6</mn></mfrac></math> of 2<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>31</mn></mfrac></math> &divide; 3<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>3</mn></mfrac></math> + 4<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>7</mn></mfrac></math> of 1<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>6</mn><mn>29</mn></mfrac></math>.</p>",
                    question_hi: "<p>54. निम्नलिखित व्यंजक को सरल कीजिए।<br>2<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> &times; 3<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>5</mn></mfrac></math> + 5<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>6</mn></mfrac></math> का 2<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>31</mn></mfrac></math> &divide; 3<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>3</mn></mfrac></math> + 4<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>7</mn></mfrac></math> का&nbsp;1<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>6</mn><mn>29</mn></mfrac></math>.</p>",
                    options_en: [
                        "<p>13<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math></p>",
                        "<p>15<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math></p>",
                        "<p>14<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math></p>",
                        "<p>16<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math></p>"
                    ],
                    options_hi: [
                        "<p>13<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math></p>",
                        "<p>15<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math></p>",
                        "<p>14<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math></p>",
                        "<p>16<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math></p>"
                    ],
                    solution_en: "<p>54.(b)<br>2<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> &times; 3<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>5</mn></mfrac></math> + 5<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>6</mn></mfrac></math> of 2<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>31</mn></mfrac></math> &divide; 3<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>3</mn></mfrac></math> + 4<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>7</mn></mfrac></math> of 1<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>6</mn><mn>29</mn></mfrac></math>.<br><math display=\"inline\"><mfrac><mrow><mn>9</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>16</mn><mn>5</mn></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>31</mn><mn>6</mn></mfrac></math> of <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>66</mn><mn>31</mn></mfrac></math> &divide; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>11</mn><mn>3</mn></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>29</mn><mn>7</mn></mfrac></math> of <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>35</mn><mn>29</mn></mfrac></math><br><math display=\"inline\"><mfrac><mrow><mn>9</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>16</mn><mn>5</mn></mfrac></math> + 11 &divide; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>11</mn><mn>3</mn></mfrac></math> + 5<br><math display=\"inline\"><mfrac><mrow><mn>36</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> + 11 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>11</mn></mfrac></math>+ 5<br><math display=\"inline\"><mfrac><mrow><mn>36</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> + 3 + 5<br>7<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> + 8 = 15<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>5</mn></mfrac></math></p>",
                    solution_hi: "<p>54.(b)<br>2<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> &times; 3<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>5</mn></mfrac></math> + 5<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>6</mn></mfrac></math> का 2<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>31</mn></mfrac></math> &divide; 3<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>3</mn></mfrac></math> + 4<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>7</mn></mfrac></math> का&nbsp;1<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>6</mn><mn>29</mn></mfrac></math><br><math display=\"inline\"><mfrac><mrow><mn>9</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>16</mn><mn>5</mn></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>31</mn><mn>6</mn></mfrac></math> का <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>66</mn><mn>31</mn></mfrac></math> &divide; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>11</mn><mn>3</mn></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>29</mn><mn>7</mn></mfrac></math> का <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>35</mn><mn>29</mn></mfrac></math><br><math display=\"inline\"><mfrac><mrow><mn>9</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>16</mn><mn>5</mn></mfrac></math> + 11 &divide; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>11</mn><mn>3</mn></mfrac></math> + 5<br><math display=\"inline\"><mfrac><mrow><mn>36</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> + 11 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>11</mn></mfrac></math>+ 5<br><math display=\"inline\"><mfrac><mrow><mn>36</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> + 3 + 5<br>7<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> + 8 = 15<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>5</mn></mfrac></math></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "55",
                    section: "17",
                    question_en: "<p>55. If A = 15&deg;, then What is the value of <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>11</mn><msqrt><mn>2</mn></msqrt><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#160;</mi><mn>3</mn><mi>A</mi><mo>+</mo><mn>10</mn><mi>s</mi><mi>i</mi><mi>n</mi><mn>2</mn><mi>A</mi></mrow><mrow><mn>7</mn><msqrt><mn>3</mn></msqrt><mi>s</mi><mi>i</mi><mi>n</mi><mn>4</mn><mi>A</mi></mrow></mfrac></math> ?</p>",
                    question_hi: "<p>55. यदि A = 15&deg;है , तो <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>11</mn><msqrt><mn>2</mn></msqrt><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#160;</mi><mn>3</mn><mi>A</mi><mo>+</mo><mn>10</mn><mi>s</mi><mi>i</mi><mi>n</mi><mn>2</mn><mi>A</mi></mrow><mrow><mn>7</mn><msqrt><mn>3</mn></msqrt><mi>s</mi><mi>i</mi><mi>n</mi><mn>4</mn><mi>A</mi></mrow></mfrac></math> का मान कितना होगा ?</p>",
                    options_en: [
                        "<p><math display=\"inline\"><mfrac><mrow><mn>11</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>21</mn></mrow><mrow><mn>32</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>32</mn></mrow><mrow><mn>21</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>7</mn></mrow><mrow><mn>11</mn></mrow></mfrac></math></p>"
                    ],
                    options_hi: [
                        "<p><math display=\"inline\"><mfrac><mrow><mn>11</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>21</mn></mrow><mrow><mn>32</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>32</mn></mrow><mrow><mn>21</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>7</mn></mrow><mrow><mn>11</mn></mrow></mfrac></math></p>"
                    ],
                    solution_en: "<p>55.(c)<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>11</mn><msqrt><mn>2</mn></msqrt><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#160;</mi><mn>3</mn><mi>A</mi><mo>+</mo><mn>10</mn><mi>s</mi><mi>i</mi><mi>n</mi><mn>2</mn><mi>A</mi></mrow><mrow><mn>7</mn><msqrt><mn>3</mn></msqrt><mi>s</mi><mi>i</mi><mi>n</mi><mn>4</mn><mi>A</mi></mrow></mfrac></math><br>Put A = <math display=\"inline\"><msup><mrow><mn>15</mn></mrow><mrow><mo>&#176;</mo></mrow></msup></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>11</mn><msqrt><mn>2</mn></msqrt><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#160;</mi><mn>3</mn><mo>&#215;</mo><msup><mn>15</mn><mo>&#176;</mo></msup><mo>+</mo><mn>10</mn><mi>&#160;</mi><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#160;</mi><mn>2</mn><mo>&#215;</mo><msup><mn>15</mn><mo>&#176;</mo></msup></mrow><mrow><mn>7</mn><msqrt><mn>3</mn></msqrt><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#160;</mi><mn>4</mn><mo>&#215;</mo><msup><mn>15</mn><mo>&#176;</mo></msup></mrow></mfrac></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>11</mn><msqrt><mn>2</mn></msqrt><mi>c</mi><mi>o</mi><mi>s</mi><msup><mn>45</mn><mo>&#176;</mo></msup><mo>+</mo><mn>10</mn><mi>&#160;</mi><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#160;</mi><msup><mn>30</mn><mo>&#176;</mo></msup></mrow><mrow><mn>7</mn><msqrt><mn>3</mn></msqrt><mi>s</mi><mi>i</mi><mi>n</mi><msup><mn>60</mn><mo>&#176;</mo></msup></mrow></mfrac></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>11</mn><msqrt><mn>2</mn><mi>&#160;</mi></msqrt><mo>&#215;</mo><mfrac><mn>1</mn><msqrt><mn>2</mn></msqrt></mfrac><mo>+</mo><mn>10</mn><mo>&#215;</mo><mfrac><mn>1</mn><mn>2</mn></mfrac></mrow><mrow><mn>7</mn><msqrt><mn>3</mn></msqrt><mo>&#215;</mo><mfrac><msqrt><mn>3</mn></msqrt><mn>2</mn></mfrac></mrow></mfrac></math><br><math display=\"inline\"><mfrac><mrow><mn>11</mn><mo>+</mo><mi>&#160;</mi><mn>5</mn></mrow><mrow><mi>&#160;</mi><mfrac><mrow><mn>21</mn></mrow><mrow><mn>2</mn></mrow></mfrac></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>32</mn><mn>21</mn></mfrac></math></p>",
                    solution_hi: "<p>55.(c)<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>11</mn><msqrt><mn>2</mn></msqrt><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#160;</mi><mn>3</mn><mi>A</mi><mo>+</mo><mn>10</mn><mi>s</mi><mi>i</mi><mi>n</mi><mn>2</mn><mi>A</mi></mrow><mrow><mn>7</mn><msqrt><mn>3</mn></msqrt><mi>s</mi><mi>i</mi><mi>n</mi><mn>4</mn><mi>A</mi></mrow></mfrac></math><br>A = <math display=\"inline\"><msup><mrow><mn>15</mn></mrow><mrow><mo>&#176;</mo></mrow></msup></math> रखने पर <br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>11</mn><msqrt><mn>2</mn></msqrt><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#160;</mi><mn>3</mn><mo>&#215;</mo><msup><mn>15</mn><mo>&#176;</mo></msup><mo>+</mo><mn>10</mn><mi>&#160;</mi><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#160;</mi><mn>2</mn><mo>&#215;</mo><msup><mn>15</mn><mo>&#176;</mo></msup></mrow><mrow><mn>7</mn><msqrt><mn>3</mn></msqrt><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#160;</mi><mn>4</mn><mo>&#215;</mo><msup><mn>15</mn><mo>&#176;</mo></msup></mrow></mfrac></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>11</mn><msqrt><mn>2</mn></msqrt><mi>c</mi><mi>o</mi><mi>s</mi><msup><mn>45</mn><mo>&#176;</mo></msup><mo>+</mo><mn>10</mn><mi>&#160;</mi><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#160;</mi><msup><mn>30</mn><mo>&#176;</mo></msup></mrow><mrow><mn>7</mn><msqrt><mn>3</mn></msqrt><mi>s</mi><mi>i</mi><mi>n</mi><msup><mn>60</mn><mo>&#176;</mo></msup></mrow></mfrac></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>11</mn><msqrt><mn>2</mn><mi>&#160;</mi></msqrt><mo>&#215;</mo><mfrac><mn>1</mn><msqrt><mn>2</mn></msqrt></mfrac><mo>+</mo><mn>10</mn><mo>&#215;</mo><mfrac><mn>1</mn><mn>2</mn></mfrac></mrow><mrow><mn>7</mn><msqrt><mn>3</mn></msqrt><mo>&#215;</mo><mfrac><msqrt><mn>3</mn></msqrt><mn>2</mn></mfrac></mrow></mfrac></math><br><math display=\"inline\"><mfrac><mrow><mn>11</mn><mo>+</mo><mi>&#160;</mi><mn>5</mn></mrow><mrow><mi>&#160;</mi><mfrac><mrow><mn>21</mn></mrow><mrow><mn>2</mn></mrow></mfrac></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>32</mn><mn>21</mn></mfrac></math></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "56",
                    section: "17",
                    question_en: "<p>56. A two-digit number is 12 more than five times the sum of its digits. The number formed by reversing the digits is 9 less than the original number. The number is:</p>",
                    question_hi: "<p>56. दो अंकों की एक संख्या अपने अंकों के योग के पांच गुने से 12 अधिक है। अंकों को उलटने से बनी संख्या मूल संख्या से 9 कम है। वह संख्या ________है।</p>",
                    options_en: [
                        "<p>96</p>",
                        "<p>78</p>",
                        "<p>87</p>",
                        "<p>69</p>"
                    ],
                    options_hi: [
                        "<p>96</p>",
                        "<p>78</p>",
                        "<p>87</p>",
                        "<p>69</p>"
                    ],
                    solution_en: "<p>56.(c)<br>Let number = 10<math display=\"inline\"><mi>x</mi></math> + y<br>According to the question,<br>Number is 12 more than five times the sum of its digits<br>10<math display=\"inline\"><mi>x</mi></math> + y = 5(x + y) + 12<br>5<math display=\"inline\"><mi>x</mi></math> - 4y = 12-------(i)<br>Number formed by reversing the digits is 9 less than the original number<br>10y + <math display=\"inline\"><mi>x</mi></math> = 10x + y - 9<br><math display=\"inline\"><mi>x</mi></math> - y = 1-------(ii)<br>Equation [(i) - 5 &times; (ii)] we get;<br>y = 7<br>Put y = 7 in equation (ii) we get;<br><math display=\"inline\"><mi>x</mi></math> - 7 = 1 &rArr; x = 8<br>Hence, number = 10 &times; 8 + 7 = 87</p>",
                    solution_hi: "<p>56.(c)<br>माना संख्या = 10<math display=\"inline\"><mi>x</mi></math> + y<br>प्रश्न के अनुसार,<br>संख्या अपने अंकों के योग के पांच गुना से 12 अधिक है<br>10<math display=\"inline\"><mi>x</mi></math> + y = 5(x + y) + 12<br>5<math display=\"inline\"><mi>x</mi></math> - 4y = 12-------(i)<br>अंकों को उलटने से बनी संख्या मूल संख्या से 9 कम है<br>10y + <math display=\"inline\"><mi>x</mi></math> = 10x + y - 9<br><math display=\"inline\"><mi>x</mi></math> - y = 1-------(ii)<br>समीकरण [(i) - 5 &times; (ii)] हमें मिलता है;<br>y = 7<br>समीकरण (ii) में y = 7 रखने पर हमें प्राप्त होता है;<br><math display=\"inline\"><mi>x</mi></math> - 7 = 1 &rArr; x = 8<br>अत: संख्या = 10 &times; 8 + 7 = 87</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "57",
                    section: "17",
                    question_en: "<p>57. If x = p secA cos B, y = q secA sinB and z = r tanA, what is the value of the following expression ?<br><math display=\"inline\"><mfrac><mrow><msup><mrow><mi>x</mi></mrow><mrow><mn>2</mn></mrow></msup></mrow><mrow><msup><mrow><mi>p</mi></mrow><mrow><mn>2</mn></mrow></msup></mrow></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msup><mi>y</mi><mn>2</mn></msup><msup><mi>q</mi><mn>2</mn></msup></mfrac></math> - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msup><mi>z</mi><mn>2</mn></msup><msup><mi>r</mi><mn>2</mn></msup></mfrac></math></p>",
                    question_hi: "<p>57. यदि x = p secA cos B, y = q secA sinB और z = r tanA है, तो निम्नलिखित व्यंजक का मान क्या है ?&nbsp;<br><math display=\"inline\"><mfrac><mrow><msup><mrow><mi>x</mi></mrow><mrow><mn>2</mn></mrow></msup></mrow><mrow><msup><mrow><mi>p</mi></mrow><mrow><mn>2</mn></mrow></msup></mrow></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msup><mi>y</mi><mn>2</mn></msup><msup><mi>q</mi><mn>2</mn></msup></mfrac></math> - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msup><mi>z</mi><mn>2</mn></msup><msup><mi>r</mi><mn>2</mn></msup></mfrac></math></p>",
                    options_en: [
                        "<p>1</p>",
                        "<p>p<sup>2 </sup>+ q<sup>2 </sup>- r<sup>2</sup></p>",
                        "<p>p<sup>2 </sup>- q<sup>2 </sup>+ r<sup>2</sup></p>",
                        "<p>0</p>"
                    ],
                    options_hi: [
                        "<p>1</p>",
                        "<p>p<sup>2 </sup>+ q<sup>2 </sup>- r<sup>2</sup></p>",
                        "<p>p<sup>2 </sup>- q<sup>2 </sup>+ r<sup>2</sup></p>",
                        "<p>0</p>"
                    ],
                    solution_en: "<p>57.(a)<br><math display=\"inline\"><mi>x</mi></math> = p secA cosB<br><math display=\"inline\"><mfrac><mrow><mi>x</mi></mrow><mrow><mi>p</mi></mrow></mfrac></math> = secA cosB &hellip; (i)<br><math display=\"inline\"><mi>y</mi></math> = q secA sinB<br><math display=\"inline\"><mfrac><mrow><mi>y</mi></mrow><mrow><mi>q</mi></mrow></mfrac></math> = secA sinB &hellip; (ii)<br>z = r tanA <br><math display=\"inline\"><mfrac><mrow><mi>z</mi></mrow><mrow><mi>r</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>sin</mi><mi>A</mi></mrow><mrow><mi>cos</mi><mi>A</mi></mrow></mfrac></math>&hellip; (iii)<br>On squaring eq . (i), (ii) and (iii) and then adding (i), (ii) and subtracting (iii) we get,<br><math display=\"inline\"><mfrac><mrow><msup><mrow><mi>x</mi></mrow><mrow><mn>2</mn></mrow></msup></mrow><mrow><msup><mrow><mi>p</mi></mrow><mrow><mn>2</mn></mrow></msup></mrow></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msup><mi>y</mi><mn>2</mn></msup><msup><mi>q</mi><mn>2</mn></msup></mfrac></math> - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msup><mi>z</mi><mn>2</mn></msup><msup><mi>r</mi><mn>2</mn></msup></mfrac></math> = sec<sup>2</sup>A cos<sup>2</sup>B + sec<sup>2</sup>A sin<sup>2</sup>B - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi>sin</mi><mn>2</mn></msup><mi>A</mi></mrow><mrow><msup><mi>cos</mi><mn>2</mn></msup><mi>A</mi></mrow></mfrac></math><br>= sec<sup>2</sup>A (cos<sup>2</sup>B + sin<sup>2</sup>B) - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi>sin</mi><mn>2</mn></msup><mi>A</mi></mrow><mrow><msup><mi>cos</mi><mn>2</mn></msup><mi>A</mi></mrow></mfrac></math><br>= <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mi>c</mi><mi>o</mi><msup><mrow><mi>s</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>A</mi></mrow></mfrac></math> -&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi>sin</mi><mn>2</mn></msup><mi>A</mi></mrow><mrow><msup><mi>cos</mi><mn>2</mn></msup><mi>A</mi></mrow></mfrac></math>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1</mn><mo>-</mo><msup><mi>sin</mi><mn>2</mn></msup><mi>A</mi></mrow><mrow><msup><mi>cos</mi><mn>2</mn></msup><mi>A</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi>cos</mi><mn>2</mn></msup><mi>A</mi></mrow><mrow><msup><mi>cos</mi><mn>2</mn></msup><mi>A</mi></mrow></mfrac></math> = 1<br><strong>Short trick:</strong><br>Put A = B = <math display=\"inline\"><msup><mrow><mn>0</mn></mrow><mrow><mo>&#176;</mo></mrow></msup></math><br>then<br><math display=\"inline\"><mi>x</mi></math> = p secA cosB<br><math display=\"inline\"><mfrac><mrow><mi>x</mi></mrow><mrow><mi>p</mi></mrow></mfrac></math> = 1 &hellip;(i)<br><math display=\"inline\"><mi>y</mi></math> = q secA sinB<br><math display=\"inline\"><mfrac><mrow><mi>y</mi></mrow><mrow><mi>q</mi></mrow></mfrac></math> = 0 &hellip; (ii)<br><math display=\"inline\"><mi>z</mi></math> = r tanA <br><math display=\"inline\"><mfrac><mrow><mi>z</mi></mrow><mrow><mi>r</mi></mrow></mfrac></math> = 0 &hellip; (iii)<br>hence,<br><math display=\"inline\"><mfrac><mrow><mi>x</mi></mrow><mrow><mi>p</mi></mrow></mfrac></math> + <math display=\"inline\"><mfrac><mrow><mi>y</mi></mrow><mrow><mi>q</mi></mrow></mfrac></math> - <math display=\"inline\"><mfrac><mrow><mi>z</mi></mrow><mrow><mi>r</mi></mrow></mfrac></math> = 1 + 0 - 0 = 1</p>",
                    solution_hi: "<p>57.(a)<br><math display=\"inline\"><mi>x</mi></math> = p secA cosB<br><math display=\"inline\"><mfrac><mrow><mi>x</mi></mrow><mrow><mi>p</mi></mrow></mfrac></math> = secA cosB &hellip; (i)<br><math display=\"inline\"><mi>y</mi></math> = q secA sinB<br><math display=\"inline\"><mfrac><mrow><mi>y</mi></mrow><mrow><mi>q</mi></mrow></mfrac></math> = secA sinB &hellip; (ii)<br><math display=\"inline\"><mi>z</mi></math> = r tanA <br><math display=\"inline\"><mfrac><mrow><mi>z</mi></mrow><mrow><mi>r</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>sin</mi><mi>A</mi></mrow><mrow><mi>cos</mi><mi>A</mi></mrow></mfrac></math>&hellip; (iii)<br>समीकरण (i), (ii) और (iii) का वर्ग करने पर और फिर (i), (ii) जोड़ने और (iii) घटाने पर हमें मिलता है,<br><math display=\"inline\"><mfrac><mrow><msup><mrow><mi>x</mi></mrow><mrow><mn>2</mn></mrow></msup></mrow><mrow><msup><mrow><mi>p</mi></mrow><mrow><mn>2</mn></mrow></msup></mrow></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msup><mi>y</mi><mn>2</mn></msup><msup><mi>q</mi><mn>2</mn></msup></mfrac></math> - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msup><mi>z</mi><mn>2</mn></msup><msup><mi>r</mi><mn>2</mn></msup></mfrac></math> = sec<sup>2</sup>A cos<sup>2</sup>B + sec<sup>2</sup>A sin<sup>2</sup>B - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi>sin</mi><mn>2</mn></msup><mi>A</mi></mrow><mrow><msup><mi>cos</mi><mn>2</mn></msup><mi>A</mi></mrow></mfrac></math><br>= sec<sup>2</sup>A (cos<sup>2</sup>B + sin<sup>2</sup>B) - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi>sin</mi><mn>2</mn></msup><mi>A</mi></mrow><mrow><msup><mi>cos</mi><mn>2</mn></msup><mi>A</mi></mrow></mfrac></math><br>= <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mi>c</mi><mi>o</mi><msup><mrow><mi>s</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>A</mi></mrow></mfrac></math> -&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi>sin</mi><mn>2</mn></msup><mi>A</mi></mrow><mrow><msup><mi>cos</mi><mn>2</mn></msup><mi>A</mi></mrow></mfrac></math>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1</mn><mo>-</mo><msup><mi>sin</mi><mn>2</mn></msup><mi>A</mi></mrow><mrow><msup><mi>cos</mi><mn>2</mn></msup><mi>A</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi>cos</mi><mn>2</mn></msup><mi>A</mi></mrow><mrow><msup><mi>cos</mi><mn>2</mn></msup><mi>A</mi></mrow></mfrac></math> = 1<br><strong>शॉर्ट ट्रिक :</strong><br>A = B = <math display=\"inline\"><msup><mrow><mn>0</mn></mrow><mrow><mo>&#176;</mo></mrow></msup></math> रखे,<br>तब <br><math display=\"inline\"><mi>x</mi></math> = p secA cosB<br><math display=\"inline\"><mfrac><mrow><mi>x</mi></mrow><mrow><mi>p</mi></mrow></mfrac></math> = 1 &hellip;(i)<br><math display=\"inline\"><mi>y</mi></math> = q secA sinB<br><math display=\"inline\"><mfrac><mrow><mi>y</mi></mrow><mrow><mi>q</mi></mrow></mfrac></math> = 0 &hellip; (ii)<br>z = r tanA <br><math display=\"inline\"><mfrac><mrow><mi>z</mi></mrow><mrow><mi>r</mi></mrow></mfrac></math> = 0 &hellip; (iii)<br>इसलिए,<br><math display=\"inline\"><mfrac><mrow><mi>x</mi></mrow><mrow><mi>p</mi></mrow></mfrac></math> + <math display=\"inline\"><mfrac><mrow><mi>y</mi></mrow><mrow><mi>q</mi></mrow></mfrac></math> - <math display=\"inline\"><mfrac><mrow><mi>z</mi></mrow><mrow><mi>r</mi></mrow></mfrac></math> = 1 + 0 - 0 = 1</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "58",
                    section: "17",
                    question_en: "<p>58. A man standing at point P is watching the top of a tower S, which makes an angle of elevation of 30&deg; with the man&rsquo;s eye. The man walks a distance of 20 m towards the tower to watch its top and the angle of the elevation becomes 45&deg;. What is the distance between the base of the tower and the point P (take<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math> = 1.732) ?</p>",
                    question_hi: "<p>58. बिंदु P पर खड़ा एक व्यक्ति एक टावर S की चोटी को देख रहा है, जो आदमी की आंख से 30&deg; का उन्नयन कोण बनाता है। वह आदमी टावर की चोटी को देखने के लिए 20 मीटर की दूरी तक चलता है और ऊंचाई का कोण 45&deg; हो जाता है। मीनार के आधार और बिंदु P के बीच की दूरी क्या है ?(<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math> = 1.732)</p>",
                    options_en: [
                        "<p>47.32 m</p>",
                        "<p>22.7 m</p>",
                        "<p>14.64 m</p>",
                        "<p>27.32 m</p>"
                    ],
                    options_hi: [
                        "<p>47.32 m</p>",
                        "<p>22.7 m</p>",
                        "<p>14.64 m</p>",
                        "<p>27.32 m</p>"
                    ],
                    solution_en: "<p>58.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739184132815.png\" alt=\"rId56\" width=\"213\" height=\"160\"><br>A/Q,<br>&rArr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math>- 1 unit = 20 m<br>&rArr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math>unit = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>20</mn><mrow><msqrt><mn>3</mn></msqrt><mo>-</mo><mn>1</mn></mrow></mfrac><mo>&#215;</mo><msqrt><mn>3</mn></msqrt></math> <br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>20</mn><mrow><mn>1</mn><mo>.</mo><mn>732</mn><mo>-</mo><mn>1</mn></mrow></mfrac></math> &times; 1.732&nbsp;<br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>20</mn><mo>&#215;</mo><mn>1</mn><mo>.</mo><mn>732</mn></mrow><mrow><mn>0</mn><mo>.</mo><mn>732</mn></mrow></mfrac></math> = 47.32 m</p>",
                    solution_hi: "<p>58.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739184132815.png\" alt=\"rId56\" width=\"213\" height=\"160\"><br>प्रश्न के अनुसार,<br>&rArr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math>- 1 इकाई&nbsp;= 20 m<br>&rArr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math>इकाई = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>20</mn><mrow><msqrt><mn>3</mn></msqrt><mo>-</mo><mn>1</mn></mrow></mfrac><mo>&#215;</mo><msqrt><mn>3</mn></msqrt></math> <br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>20</mn><mrow><mn>1</mn><mo>.</mo><mn>732</mn><mo>-</mo><mn>1</mn></mrow></mfrac></math> &times; 1.732&nbsp;<br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>20</mn><mo>&#215;</mo><mn>1</mn><mo>.</mo><mn>732</mn></mrow><mrow><mn>0</mn><mo>.</mo><mn>732</mn></mrow></mfrac></math> = 47.32 m</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "59",
                    section: "17",
                    question_en: "<p>59. A cylindrical candle, 14 cm in diameter and length 2 cm is melted to form a cuboid candle of dimensions 7 cm &times; 11 cm &times; 2 cm. How many cuboidal candles can be obtained ?</p>",
                    question_hi: "<p>59. एक बेलनाकार मोमबत्ती जिसका व्यास 14 cm और लंबाई 2 cm है, को पिघलाकर 7 cm &times; 11 cm &times; 2 cm विमाओं वाली एक घनाभाकार मोमबत्ती बनाई जाती है। इससे कितनी घनाभाकार मोमबत्तियाँ प्राप्त की जा सकती है ?</p>",
                    options_en: [
                        "<p>2</p>",
                        "<p>5</p>",
                        "<p>4</p>",
                        "<p>3</p>"
                    ],
                    options_hi: [
                        "<p>2</p>",
                        "<p>5</p>",
                        "<p>4</p>",
                        "<p>3</p>"
                    ],
                    solution_en: "<p>59.(a)<br>Number of candles obtained = <math style=\"font-family:Verdana\" display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>Volume</mi><mo>&#160;</mo><mi>of</mi><mo>&#160;</mo><mi>cylindrical</mi><mo>&#160;</mo><mi>candle</mi></mrow><mrow><mi>volume</mi><mo>&#160;</mo><mi>of</mi><mo>&#160;</mo><mi>cuboid</mi><mo>&#160;</mo><mi>candle</mi></mrow></mfrac></math> <br>= <math display=\"inline\"><mfrac><mrow><mi>&#960;</mi><msup><mrow><mi>r</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>h</mi></mrow><mrow><mi>l</mi><mi>b</mi><mi>h</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mstyle displaystyle=\"true\"><mfrac><mn>22</mn><mn>7</mn></mfrac></mstyle><mo>&#215;</mo><msup><mn>7</mn><mn>2</mn></msup><mo>&#215;</mo><mn>2</mn></mrow><mrow><mn>7</mn><mo>&#215;</mo><mn>11</mn><mo>&#215;</mo><mn>2</mn></mrow></mfrac></math> = 2</p>",
                    solution_hi: "<p>59.(a)<br>प्राप्त मोमबत्तियों की संख्या = <math style=\"font-family:Verdana\" display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2348;&#2375;&#2354;&#2344;&#2366;&#2325;&#2366;&#2352;</mi><mo>&#160;</mo><mi>&#2350;&#2379;&#2350;&#2348;&#2340;&#2381;&#2340;&#2368;</mi><mo>&#160;</mo><mi>&#2325;&#2366;</mi><mo>&#160;</mo><mi>&#2310;&#2351;&#2340;&#2344;</mi></mrow><mrow><mi>&#2328;&#2344;&#2366;&#2325;&#2366;&#2352;</mi><mo>&#160;</mo><mi>&#2350;&#2379;&#2350;&#2348;&#2340;&#2381;&#2340;&#2368;</mi><mo>&#160;</mo><mi>&#2325;&#2366;</mi><mo>&#160;</mo><mi>&#2310;&#2351;&#2340;&#2344;</mi></mrow></mfrac></math> <br>= <math display=\"inline\"><mfrac><mrow><mi>&#960;</mi><msup><mrow><mi>r</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>h</mi></mrow><mrow><mi>l</mi><mi>b</mi><mi>h</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mstyle displaystyle=\"true\"><mfrac><mn>22</mn><mn>7</mn></mfrac></mstyle><mo>&#215;</mo><msup><mn>7</mn><mn>2</mn></msup><mo>&#215;</mo><mn>2</mn></mrow><mrow><mn>7</mn><mo>&#215;</mo><mn>11</mn><mo>&#215;</mo><mn>2</mn></mrow></mfrac></math> = 2</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "60",
                    section: "17",
                    question_en: "<p>60. △XYZ is similar to △PQR. If the ratio of the perimeter of △XYZ to the perimeter of △PQR is 16 : 9 and PQ = 3.6 cm, then what is the length (in cm) of XY ?</p>",
                    question_hi: "<p>60. △XYZ, △PQR के समरूप है। यदि △XYZ के परिमाप और △PQR के परिमाप का अनुपात 16 : 9 है तथा PQ = 3.6 cm है, तो XY की लंबाई (cm में) कितनी है ?</p>",
                    options_en: [
                        "<p>3<math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math></p>",
                        "<p>2<math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math></p>",
                        "<p>6<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math></p>",
                        "<p>4<math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math></p>"
                    ],
                    options_hi: [
                        "<p>3<math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math></p>",
                        "<p>2<math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math></p>",
                        "<p>6<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math></p>",
                        "<p>4<math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math></p>"
                    ],
                    solution_en: "<p>60.(c)<br>△XYZ ~ △PQR<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>p</mi><mi>e</mi><mi>r</mi><mi>i</mi><mi>m</mi><mi>e</mi><mi>t</mi><mi>e</mi><mi>r</mi><mi>&#160;</mi><mi>o</mi><mi>f</mi><mo>&#9651;</mo><mi>X</mi><mi>Y</mi><mi>Z</mi></mrow><mrow><mi>p</mi><mi>e</mi><mi>r</mi><mi>i</mi><mi>m</mi><mi>e</mi><mi>t</mi><mi>e</mi><mi>r</mi><mi>&#160;</mi><mi>o</mi><mi>f</mi><mo>&#9651;</mo><mi>P</mi><mi>Q</mi><mi>R</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>X</mi><mi>Y</mi></mrow><mrow><mi>P</mi><mi>Q</mi></mrow></mfrac></math><br><math display=\"inline\"><mfrac><mrow><mi>X</mi><mi>Y</mi></mrow><mrow><mn>3</mn><mo>.</mo><mn>6</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>16</mn><mn>9</mn></mfrac></math> &rArr; XY = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>64</mn><mn>10</mn></mfrac></math> = 6<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>5</mn></mfrac></math></p>",
                    solution_hi: "<p>60.(c)<br>△XYZ ~ △PQR<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>&#9651;</mo><mi>X</mi><mi>Y</mi><mi>Z</mi><mo>&#160;</mo><mi>&#2325;&#2366;</mi><mo>&#160;</mo><mi>&#2346;&#2352;&#2367;&#2350;&#2366;&#2346;</mi></mrow><mrow><mo>&#9651;</mo><mi>P</mi><mi>Q</mi><mi>R</mi><mo>&#160;</mo><mi>&#2325;&#2366;</mi><mo>&#160;</mo><mi>&#2346;&#2352;&#2367;&#2350;&#2366;&#2346;</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>X</mi><mi>Y</mi></mrow><mrow><mi>P</mi><mi>Q</mi></mrow></mfrac></math><br><math display=\"inline\"><mfrac><mrow><mi>X</mi><mi>Y</mi></mrow><mrow><mn>3</mn><mo>.</mo><mn>6</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>16</mn><mn>9</mn></mfrac></math> &rArr; XY = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>64</mn><mn>10</mn></mfrac></math> = 6<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>5</mn></mfrac></math></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "61",
                    section: "17",
                    question_en: "<p>61. Roshan owned a plot of land having an area that was 20% more than the area of the plot owned by Susan, while the area of the plot of land owned by Jacob was 30% more than the area of the plot owned by Roshan. If the area of the plot owned by Susan was 1250 square feet, what was the area (in square feet) of the plot owned by Jacob ?</p>",
                    question_hi: "<p>61. रोशन के पास एक भूखंड है जिसका क्षेत्रफल सुसान के स्वामित्व वाले भूखंड के क्षेत्रफल से 20% अधिक है, जबकि जैकब के स्वामित्व वाले भूखंड का क्षेत्रफल रोशन के स्वामित्व वाले भूखंड के क्षेत्रफल से 30% अधिक है। यदि सुसान के भूखंड का क्षेत्रफल 1250 वर्ग फुट था, तो जैकब के स्वामित्व वाले भूखंड का क्षेत्रफल (वर्ग फुट में) कितना था ?</p>",
                    options_en: [
                        "<p>2050</p>",
                        "<p>1875</p>",
                        "<p>1950</p>",
                        "<p>2020</p>"
                    ],
                    options_hi: [
                        "<p>2050</p>",
                        "<p>1875</p>",
                        "<p>1950</p>",
                        "<p>2020</p>"
                    ],
                    solution_en: "<p>61.(c)<br>Ratio&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&rarr; Roshan : Susan : Jacob<br>Area of plot &rarr;&nbsp; &nbsp; 120&nbsp; &nbsp; :&nbsp; &nbsp;100&nbsp; &nbsp; :&nbsp; 156<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 30&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp;25&nbsp; &nbsp; :&nbsp; &nbsp;39<br>Given Area with Susan = 1250 square feet<br>&rArr; 25 unit = 1250<br>&rArr; 1 unit = 50<br>Hence area with Jacob = 39 &times;&nbsp;50 = 1950 square feet</p>",
                    solution_hi: "<p>61.(c)<br>अनुपात&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&rarr; रोशन&nbsp; :&nbsp; सुसान&nbsp; :&nbsp; जैकब <br>भूमि का क्षेत्रफल &rarr;&nbsp; 120&nbsp; &nbsp;:&nbsp; &nbsp;100&nbsp; &nbsp;:&nbsp; 156<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 30&nbsp; &nbsp; :&nbsp; &nbsp; 25&nbsp; &nbsp; :&nbsp; &nbsp;39<br>सुसान के साथ दिया गया क्षेत्रफल = 1250 वर्ग फुट<br>&rArr; 25 इकाई =1250<br>&rArr;1 इकाई = 50<br>अत: जैकब के साथ क्षेत्रफल = 39 &times; 50 = 1950 वर्ग फुट</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "62",
                    section: "17",
                    question_en: "<p>62. In a circle with center O, the diameter AB extended to the point C, outside the circle. CD is a tangent at the point D on the circle. If the radius of circle is 6 cm and &ang;DBC = 120&deg;, then the length of CD is equal to:</p>",
                    question_hi: "<p>62. O केंद्र वाले एक वृत्त में, व्यास AB को वृत्त के बाहर बिंदु C तक बढ़ाया जाता है। CD, वृत्त पर स्थित बिंदु D पर एक स्पर्श रेखा है। यदि वृत्त की त्रिज्या 6 cm हैऔर &ang;DBC = 120&deg; है, तो CD की लंबाई _______ होगी।</p>",
                    options_en: [
                        "<p>6<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math> cm</p>",
                        "<p>4<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math> cm</p>",
                        "<p>5<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math> cm</p>",
                        "<p>3<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math> cm</p>"
                    ],
                    options_hi: [
                        "<p>6<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math> cm</p>",
                        "<p>4<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math> cm</p>",
                        "<p>5<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math> cm</p>",
                        "<p>3<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math> cm</p>"
                    ],
                    solution_en: "<p>62.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739184133065.png\" alt=\"rId57\" width=\"290\" height=\"130\"><br>From the figure,<br>&ang;OBD = 180&deg; - 120&deg; = 60&deg;&nbsp; &nbsp; &nbsp; &nbsp; [angle of one side of line]<br>&there4; &ang;OBD = &ang;ODB = 60&deg;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;[opposite angle of same side (radius)]<br>Then, &ang;BOD = 180&deg; - (60&deg; + 60&deg;) = 60&deg;<br>Each angle of <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8710;</mo></math>DOB is 60&deg;<br>So, <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8710;</mo></math>DOB is equilateral<br>Then, side DB = 6 cm<br>Now, in <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8710;</mo></math>DBC<br>&ang;D : &ang;C : &ang;B = 30&deg; : 30&deg; : 120&deg;<br>Side ratio DB : BC : DC = 1 : 1 : <math display=\"inline\"><msqrt><mn>3</mn></msqrt></math><br>Hence, DC = 6 &times; <math display=\"inline\"><msqrt><mn>3</mn></msqrt></math> = 6<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math> cm</p>",
                    solution_hi: "<p>62.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739184133065.png\" alt=\"rId57\" width=\"290\" height=\"130\"><br>चित्र से,<br>&ang;OBD = 180&deg; - 120&deg; = 60&deg;&nbsp; &nbsp; &nbsp; &nbsp; [रेखा के एक तरफ का कोण]<br>&there4; &ang;OBD = &ang;ODB = 60&deg;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;[समान भुजा (त्रिज्या) का विपरीत कोण]<br>फिर, &ang;BOD = 180&deg; - (60&deg; + 60&deg;) = 60&deg;<br>DOB का प्रत्येक कोण 60&deg; का होता है<br>अतः, <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8710;</mo></math>DOB समबाहु है<br>तो , भुजा DB = 6 सेमी <br>अब , <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8710;</mo></math>DBC में <br>&ang;D : &ang;C : &ang;B = 30&deg; : 30&deg; : 120&deg;<br>भुजा DB : BC : DC का अनुपात = 1 : 1 : <math display=\"inline\"><msqrt><mn>3</mn></msqrt></math><br>अतः, DC = 6 &times; <math display=\"inline\"><msqrt><mn>3</mn></msqrt></math> = 6<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math> सेमी</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "63",
                    section: "17",
                    question_en: "<p>63. If <math display=\"inline\"><msqrt><mi>x</mi></msqrt></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msqrt><mi>x</mi></msqrt></mfrac></math> = 7, then the value of x + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi>x</mi></mfrac></math> is equal to:</p>",
                    question_hi: "<p>63. यदि <math display=\"inline\"><msqrt><mi>x</mi></msqrt></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msqrt><mi>x</mi></msqrt></mfrac></math> = 7 है, तो x + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi>x</mi></mfrac></math> का मान निम्न में से किसके बराबर होगा ?</p>",
                    options_en: [
                        "<p>47</p>",
                        "<p>49</p>",
                        "<p>51</p>",
                        "<p>45</p>"
                    ],
                    options_hi: [
                        "<p>47</p>",
                        "<p>49</p>",
                        "<p>51</p>",
                        "<p>45</p>"
                    ],
                    solution_en: "<p>63.(a) <br>&rArr; <math display=\"inline\"><msqrt><mi>x</mi></msqrt></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msqrt><mi>x</mi></msqrt></mfrac></math> = 7<br>On squaring both side, <br>&rArr; x + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi>x</mi></mfrac></math> + 2 = 49<br>&rArr; x + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi>x</mi></mfrac></math> = 47</p>",
                    solution_hi: "<p>63.(a) <br>&rArr; <math display=\"inline\"><msqrt><mi>x</mi></msqrt></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msqrt><mi>x</mi></msqrt></mfrac></math> = 7<br>दोनो पक्षो का वर्ग करने पर, <br>&rArr; x + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi>x</mi></mfrac></math> + 2 = 49<br>&rArr; x + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi>x</mi></mfrac></math> = 47</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "64",
                    section: "17",
                    question_en: "<p>64. A person divided a certain sum among his three sons in the ratio 3 : 4 : 5. Had he divided the sum in the ratio <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>3</mn></mfrac></math> : <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>4</mn></mfrac></math> : <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>5</mn></mfrac></math> the son, who got the least share earlier, would have got ₹1,188 more. The sum (in ₹) was:</p>",
                    question_hi: "<p>64. एक व्यक्ति ने एक निश्चित राशि को अपने तीन पुत्रों में 3 : 4 : 5 के अनुपात में विभाजित किया। यदि वह राशि को <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>3</mn></mfrac></math> : <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>4</mn></mfrac></math> : <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>5</mn></mfrac></math>&nbsp;के अनुपात में विभाजित करता, तो वह पुत्र, जिसे पहले सबसे कम हिस्सा मिलता, उसे ₹1,188 अधिक मिलते। राशि की गणना करें (₹ में)।</p>",
                    options_en: [
                        "<p>6768</p>",
                        "<p>6876</p>",
                        "<p>6678</p>",
                        "<p>6687</p>"
                    ],
                    options_hi: [
                        "<p>6768</p>",
                        "<p>6876</p>",
                        "<p>6678</p>",
                        "<p>6687</p>"
                    ],
                    solution_en: "<p>64.(a) Initial ratio &rarr;&nbsp;3 : 4 : 5<br>New the ratio &rarr; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>3</mn></mfrac></math> : <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>4</mn></mfrac></math> : <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>5</mn></mfrac></math> or 20 : 15 : 12</p>\n<p>Share of first son = <math display=\"inline\"><mfrac><mrow><mn>20</mn></mrow><mrow><mn>47</mn></mrow></mfrac></math>&times; Total share<br>Share of the first son = <math display=\"inline\"><mfrac><mrow><mn>20</mn></mrow><mrow><mn>47</mn></mrow></mfrac></math> &times; 12 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>240</mn><mn>47</mn></mfrac></math><br>According to question,<br>(<math display=\"inline\"><mfrac><mrow><mn>240</mn></mrow><mrow><mn>47</mn></mrow></mfrac></math> - 3) units = 1188<br>12 units = <math display=\"inline\"><mfrac><mrow><mn>1188</mn></mrow><mrow><mn>99</mn></mrow></mfrac></math> &times; 12 &times; 47 = 6768</p>",
                    solution_hi: "<p>64.(a) प्रारंभिक अनुपात &rarr; 3 : 4 : 5<br>नया अनुपात &rarr; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>3</mn></mfrac></math> : <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>4</mn></mfrac></math> : <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>5</mn></mfrac></math> या 20 : 15 : 12</p>\n<p>पहले बेटे का हिस्सा = <math display=\"inline\"><mfrac><mrow><mn>20</mn></mrow><mrow><mn>47</mn></mrow></mfrac></math>&times; कुल राशि<br>पहले बेटे का हिस्सा = <math display=\"inline\"><mfrac><mrow><mn>20</mn></mrow><mrow><mn>47</mn></mrow></mfrac></math> &times; 12 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>240</mn><mn>47</mn></mfrac></math><br>प्रश्न के अनुसार,<br>(<math display=\"inline\"><mfrac><mrow><mn>240</mn></mrow><mrow><mn>47</mn></mrow></mfrac></math> - 3) इकाई = 1188<br>12 इकाई = <math display=\"inline\"><mfrac><mrow><mn>1188</mn></mrow><mrow><mn>99</mn></mrow></mfrac></math> &times; 12 &times; 47 = 6768</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "65",
                    section: "17",
                    question_en: "<p>65. A milk vendor has 75 litres of cow milk, 45 litres of toned milk and 30 litres of full cream milk. These should be placed in different trays such that each tray contains the same quantity of milk and two kinds of milk should not be mixed in a tray. What is the least number of trays required to place all the milk ?</p>",
                    question_hi: "<p>65. एक दूध विक्रेता के पास 75 लीटर गाय का दूध, 45 लीटर टोंड दूध और 30 लीटर फुल क्रीम दूध है। इन्हें अलग- अलग ट्रे में इस प्रकार रखना चाहिए कि प्रत्येक ट्रे में समान मात्रा में दूध हो और एक ट्रे में दो प्रकार का दूध नहीं मिलाना चाहिए। सारा दूध रखने के लिए कम से कम कितनी ट्रे की आवश्यकता होगी ?</p>",
                    options_en: [
                        "<p>15</p>",
                        "<p>25</p>",
                        "<p>10</p>",
                        "<p>20</p>"
                    ],
                    options_hi: [
                        "<p>15</p>",
                        "<p>25</p>",
                        "<p>10</p>",
                        "<p>20</p>"
                    ],
                    solution_en: "<p>65.(c) <br>For same quantity of milk = HCF of (75, 45, 30) = 15 liter<br>Trays for cow milk = <math display=\"inline\"><mfrac><mrow><mn>75</mn></mrow><mrow><mn>15</mn></mrow></mfrac></math> = 5 <br>Trays for toned milk = <math display=\"inline\"><mfrac><mrow><mn>45</mn></mrow><mrow><mn>15</mn></mrow></mfrac></math> = 3 <br>Trays for full cream milk = <math display=\"inline\"><mfrac><mrow><mn>30</mn></mrow><mrow><mn>15</mn></mrow></mfrac></math> = 2 <br>Total trays = 5 + 3 + 2 = 10 <br>The least number of trays required to place all the milk is 10.</p>",
                    solution_hi: "<p>65.(c) <br>दूध की समान मात्रा के लिए = (75, 45, 30) का H.C.F. = 15 लीटर <br>गाय के दूध के लिए ट्रे = <math display=\"inline\"><mfrac><mrow><mn>75</mn></mrow><mrow><mn>15</mn></mrow></mfrac></math> = 5 <br>टोंड दूध के लिए ट्रे = <math display=\"inline\"><mfrac><mrow><mn>45</mn></mrow><mrow><mn>15</mn></mrow></mfrac></math> = 3 <br>फुल क्रीम दूध के लिए ट्रे = <math display=\"inline\"><mfrac><mrow><mn>30</mn></mrow><mrow><mn>15</mn></mrow></mfrac></math> = 2<br>कुल ट्रे = 5 + 3 + 2 = 10 <br>अतः सारा दूध रखने के लिए आवश्यक ट्रे की न्यूनतम संख्या = 10</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "66",
                    section: "17",
                    question_en: "<p>66. A and B can do a work in 10 days and 40 days, respectively. How long will it take together to complete the work ?</p>",
                    question_hi: "<p>66. A और B एक काम को क्रमश: 10 दिन और 40 दिन में पूरा कर सकते हैं। उन्हें साथ मिलकर काम को पूरा करने में कितना समय लगेगा ?</p>",
                    options_en: [
                        "<p>15 days</p>",
                        "<p>20 days</p>",
                        "<p>8 days</p>",
                        "<p>12 days</p>"
                    ],
                    options_hi: [
                        "<p>15 दिन</p>",
                        "<p>20 दिन</p>",
                        "<p>8 दिन</p>",
                        "<p>12 दिन</p>"
                    ],
                    solution_en: "<p>66.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739184133187.png\" alt=\"rId58\" width=\"282\" height=\"167\"><br>Required time = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>40</mn><mrow><mn>4</mn><mo>+</mo><mn>1</mn></mrow></mfrac></math> = 8 days</p>",
                    solution_hi: "<p>66.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739184133343.png\" alt=\"rId59\" width=\"280\" height=\"177\"><br>आवश्यक समय = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>40</mn><mrow><mn>4</mn><mo>+</mo><mn>1</mn></mrow></mfrac></math> = 8 दिन</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "67",
                    section: "17",
                    question_en: "<p>67. In an election between two candidates A and B, A got 33% of total votes casted and still lost by 55,522 votes. Find the total number of votes got by B.</p>",
                    question_hi: "<p>67. दो उम्मीदवारों A और B के बीच हुए एक चुनाव में A को डाले गए कुल मतों के 33% मत मिले और फिर भी वह 55,522 मतों से चुनाव हार गया। B को कुल कितने मत मिले ?</p>",
                    options_en: [
                        "<p>1,09,411</p>",
                        "<p>1,04,911</p>",
                        "<p>1,09,141</p>",
                        "<p>1,09,114</p>"
                    ],
                    options_hi: [
                        "<p>1,09,411</p>",
                        "<p>1,04,911</p>",
                        "<p>1,09,141</p>",
                        "<p>1,09,114</p>"
                    ],
                    solution_en: "<p>67.(a) <br>Votes got by B = (100 - 33)% = 67%<br>Difference = (67 - 33)% = 34%<br>34% = 55522<br>(Vote got by B) 67% = <math display=\"inline\"><mfrac><mrow><mn>55522</mn></mrow><mrow><mn>34</mn></mrow></mfrac></math> &times; 67 = 109411</p>",
                    solution_hi: "<p>67.(a) <br>B को मिले वोट = (100 - 33)% = 67%<br>अंतर = (67 - 33)% = 34%<br>34% = 55522<br>(B को मिले वोट) 67% = <math display=\"inline\"><mfrac><mrow><mn>55522</mn></mrow><mrow><mn>34</mn></mrow></mfrac></math> &times; 67 = 109411</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "68",
                    section: "17",
                    question_en: "<p>68. 5% of 2% of 3 is equal to:</p>",
                    question_hi: "<p>68. संख्या 3 के 2% का 5% किसके बराबर होगा ?</p>",
                    options_en: [
                        "<p>3</p>",
                        "<p>0.3</p>",
                        "<p>0.003</p>",
                        "<p>0.03</p>"
                    ],
                    options_hi: [
                        "<p>3</p>",
                        "<p>0.3</p>",
                        "<p>0.003</p>",
                        "<p>0.03</p>"
                    ],
                    solution_en: "<p>68.(c)<br>5% of 2% of 3 = <math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>100</mn></mfrac></math> &times; 3 <br>= <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>20</mn></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>50</mn></mfrac></math> &times; 3 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>1000</mn></mfrac></math> = 0.003</p>",
                    solution_hi: "<p>68.(c)<br>3 के 2% का 5% = 3 &times; <math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>100</mn></mfrac></math><br>= 3 &times; <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>20</mn></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>50</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>1000</mn></mfrac></math> = 0.003</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "69",
                    section: "17",
                    question_en: "<p>69. Pipe L can fill a pool in 30 hours and pipe M in 45 hours. If both the pipes are opened in an empty pool, how much time will they take to fill it ?</p>",
                    question_hi: "<p>69. पाइप L एक पूल को 30 घंटे में और पाइप M इसे 45 घंटे में भर सकता है। यदि दोनों पाइपों को एक खाली पूल में खोल दिया जाए, तो उन्हें इसे भरने में कितना समय लगेगा ?</p>",
                    options_en: [
                        "<p>24 hours</p>",
                        "<p>17 hours</p>",
                        "<p>18 hours</p>",
                        "<p>20 hours</p>"
                    ],
                    options_hi: [
                        "<p>24 घंटे</p>",
                        "<p>17 घंटे</p>",
                        "<p>18 घंटे</p>",
                        "<p>20 घंटे</p>"
                    ],
                    solution_en: "<p>69.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739184133494.png\" alt=\"rId60\" width=\"251\" height=\"173\"><br>Both pipe take time to fill the pool = <math display=\"inline\"><mfrac><mrow><mn>90</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> = 18 hours</p>",
                    solution_hi: "<p>69.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739184133619.png\" alt=\"rId61\" width=\"257\" height=\"179\"><br>दोनो पाइपों को खाली पूल भरने मे लगा समय = <math display=\"inline\"><mfrac><mrow><mn>90</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> = 18 घंटे</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "70",
                    section: "17",
                    question_en: "<p>70. What is the discount that Rohan should offer on the remaining ₹ 8,000 of a laptop priced at ₹ 48,000, given that, he has already given a 12% discount on the first ₹ 28,000 and 8% discount on the next ₹ 12,000 to match the discount amount of 9.5% given on the total price ?</p>",
                    question_hi: "<p>70. रोहन को ₹48,000 के मूल्य वाले लैपटॉप के शेष ₹8,000 पर कितनी छूट देनी होगी, यदि वह पहले ₹28,000 के भुगतान पर 12% की छूट और अगले ₹12,000 के भुगतान पर 8% की छूट पहले ही दे चुका है ताकि लैपटॉप के कुल मूल्य पर दी गई 9.5% की छूट की बराबरी की जा सके ?</p>",
                    options_en: [
                        "<p>₹ 402</p>",
                        "<p>₹ 204</p>",
                        "<p>₹ 420</p>",
                        "<p>₹ 240</p>"
                    ],
                    options_hi: [
                        "<p>₹ 402</p>",
                        "<p>₹ 420</p>",
                        "<p>₹ 204</p>",
                        "<p>₹ 240</p>"
                    ],
                    solution_en: "<p>70.(d)<br>Overall discount given = 48000 &times; 9.5% = ₹4560<br>Discount on first ₹28000 = 28000 &times; 12% = ₹3360<br>Discount on next ₹12000 = 12000 &times; 8% = ₹960<br>Hence, required discount = 4560 - (3360 + 960) = ₹240</p>",
                    solution_hi: "<p>70.(d)<br>दी गई कुल छूट = 48000 &times; 9.5% = ₹4560<br>पहले ₹28000 पर छूट = 28000 &times; 12% = ₹3360<br>अगले ₹12000 पर छूट = 12000 &times; 8% = ₹960<br>अतः, आवश्यक छूट = 4560 - (3360 + 960) = ₹240</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "71",
                    section: "17",
                    question_en: "<p>71. An amount becomes double in 7 years on simple interest. The amount would be four times in ______ years on the same rate of simple interest.</p>",
                    question_hi: "<p>71. साधारण ब्याज पर एक धनराशि 7 वर्ष में दोगुनी हो जाती है। साधारण ब्याज की समान दर पर धनराशि कितने वर्षों में चार गुना हो जाएगी ?</p>",
                    options_en: [
                        "<p>14</p>",
                        "<p>18</p>",
                        "<p>21</p>",
                        "<p>28</p>"
                    ],
                    options_hi: [
                        "<p>14</p>",
                        "<p>18</p>",
                        "<p>21</p>",
                        "<p>28</p>"
                    ],
                    solution_en: "<p>71.(c)<br>SI = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>p</mi><mo>&#215;</mo><mi>r</mi><mo>&#215;</mo><mi>t</mi></mrow><mn>100</mn></mfrac></math><br>Let the principal is 1 units, <br>According to the question,<br>&rArr; 1 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1</mn><mo>&#215;</mo><mi>r</mi><mo>&#215;</mo><mn>7</mn></mrow><mn>100</mn></mfrac></math><br>&rArr; r = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>100</mn><mn>7</mn></mfrac></math><br>When the principal is 4 times then,<br>3 = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1</mn><mo>&#215;</mo><mn>100</mn><mo>&#215;</mo><mi>t</mi></mrow><mrow><mn>100</mn><mo>&#215;</mo><mn>7</mn></mrow></mfrac></math><br>t (time) = 21 years</p>",
                    solution_hi: "<p>71.(c)<br>साधारण ब्याज = <math style=\"font-family:Verdana\" display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2350;&#2370;&#2354;&#2343;&#2344;</mi><mo>&#215;</mo><mi>&#2342;&#2352;</mi><mo>&#215;</mo><mi>&#2360;&#2350;&#2351;</mi></mrow><mn>100</mn></mfrac></math><br>माना मूलधन 1 इकाई है, <br>प्रश्ननानुसार <br>&rArr; 1 = <math style=\"font-family:Verdana\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1</mn><mo>&#215;</mo><mi>&#2342;&#2352;</mi><mo>&#215;</mo><mn>7</mn></mrow><mn>100</mn></mfrac></math><br>&rArr; r = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>100</mn><mn>7</mn></mfrac></math><br>जब मूलधन 4 गुना हो तो,<br>3 = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1</mn><mo>&#215;</mo><mn>100</mn><mo>&#215;</mo><mi>t</mi></mrow><mrow><mn>100</mn><mo>&#215;</mo><mn>7</mn></mrow></mfrac></math><br>t (समय) = 21 वर्ष</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "72",
                    section: "17",
                    question_en: "<p>72. In two successive years, 75 and 50 employees of a company appeared at the departmental examination. Respectively, 84% and 52% of them passed. The average rate of pass percentage is:</p>",
                    question_hi: "<p>72. दो क्रमिक वर्षों में एक कंपनी के 75 और 50 कर्मचारी विभागीय परीक्षा में उपस्थित हुए। उनमें से क्रमशः 84% और 52% उत्तीर्ण हुए। उत्तीर्ण प्रतिशत की औसत दर कितनी है ?</p>",
                    options_en: [
                        "<p>71%</p>",
                        "<p>41%</p>",
                        "<p>71<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math>%</p>",
                        "<p>41<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math>%</p>"
                    ],
                    options_hi: [
                        "<p>71%</p>",
                        "<p>41%</p>",
                        "<p>71<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math>%</p>",
                        "<p>41<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math>%</p>"
                    ],
                    solution_en: "<p>72.(c)<br>Average rate of pass % = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mfrac><mn>84</mn><mn>100</mn></mfrac><mo>&#215;</mo><mn>75</mn><mo>+</mo><mfrac><mn>52</mn><mn>100</mn></mfrac><mo>&#215;</mo><mn>50</mn></mrow><mrow><mn>75</mn><mo>+</mo><mn>50</mn></mrow></mfrac></math> &times; 100<br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>21</mn><mo>&#215;</mo><mn>3</mn><mo>+</mo><mn>26</mn></mrow><mn>125</mn></mfrac></math> &times; 100&nbsp;<br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>63</mn><mo>+</mo><mn>26</mn></mrow><mn>125</mn></mfrac></math> &times;100<br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>89</mn><mn>5</mn></mfrac></math> &times; 4 = 71<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>5</mn></mfrac></math></p>",
                    solution_hi: "<p>72.(c)<br>उत्तीर्ण होने की औसत दर % = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mfrac><mn>84</mn><mn>100</mn></mfrac><mo>&#215;</mo><mn>75</mn><mo>+</mo><mfrac><mn>52</mn><mn>100</mn></mfrac><mo>&#215;</mo><mn>50</mn></mrow><mrow><mn>75</mn><mo>+</mo><mn>50</mn></mrow></mfrac></math> &times; 100<br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>21</mn><mo>&#215;</mo><mn>3</mn><mo>+</mo><mn>26</mn></mrow><mn>125</mn></mfrac></math> &times; 100&nbsp;<br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>63</mn><mo>+</mo><mn>26</mn></mrow><mn>125</mn></mfrac></math> &times;100<br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>89</mn><mn>5</mn></mfrac></math> &times; 4 = 71<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>5</mn></mfrac></math></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "73",
                    section: "17",
                    question_en: "<p>73. Study the given table and answer the question that follows. The table represents the number of scooters (in thousands) manufactured by four companies A, B, C and D from the period 2012 to 2015.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739184133867.png\" alt=\"rId62\" width=\"282\" height=\"126\"> <br>The average number of scooters produced by company D is what percent (rounded off to 2 decimal places) more than the combined average number of scooters produced by the three companies A, B, and C ?</p>",
                    question_hi: "<p>73. दी गई तालिका का अध्ययन कीजिए और आगे आने वाले प्रश्न का उत्तर दीजिए। <br>तालिका 2012 से 2015 की अवधि के दौरान चार कंपनियों A, B, C और D द्वारा निर्मित स्कूटरों की संख्या (हजार में) दर्शाती है।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739184134027.png\" alt=\"rId63\" width=\"292\" height=\"138\"> <br>कंपनी D द्वारा उत्पादित स्कूटरों की औसत संख्या, तीन कंपनियों A, B और C द्वारा उत्पादित स्कूटरों की संयुक्त औसत संख्या से कितने प्रतिशत 2 दशमलव स्थानों तक पूर्णाकित) अधिक है ?</p>",
                    options_en: [
                        "<p>52.75%</p>",
                        "<p>27.25%</p>",
                        "<p>44.66%</p>",
                        "<p>35.35%</p>"
                    ],
                    options_hi: [
                        "<p>52.75%</p>",
                        "<p>27.25%</p>",
                        "<p>44.66%</p>",
                        "<p>35.35%</p>"
                    ],
                    solution_en: "<p>73.(d) <br>Total scooter produced by company A = 360 + 580 + 1000 + 1240 = 3180<br>Average scooter produced by company A = <math display=\"inline\"><mfrac><mrow><mn>3180</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> = 795<br>Total scooter produced by company B = 420 + 940 + 780 + 950 = 3090<br>Average scooter produced by company B = <math display=\"inline\"><mfrac><mrow><mn>3090</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> = 772.5<br>Total scooter produced by company C = 380 + 570 + 1020 + 1180 = 3150<br>Average scooter produced by company C = <math display=\"inline\"><mfrac><mrow><mn>3150</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> = 787.5<br>Combined average of scooter produced by A, B, C = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>795</mn><mo>+</mo><mn>772</mn><mo>.</mo><mn>5</mn><mo>+</mo><mn>787</mn><mo>.</mo><mn>5</mn></mrow><mn>3</mn></mfrac></math><br>= <math display=\"inline\"><mfrac><mrow><mn>2355</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> = 785<br>Total scooter produced by company D = 520 + 980 + 1090 + 1660 = 4250<br>Average scooter produced by company D = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>520</mn><mo>+</mo><mn>980</mn><mo>+</mo><mn>1090</mn><mo>+</mo><mn>1660</mn></mrow><mn>4</mn></mfrac></math> <br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4250</mn><mn>4</mn></mfrac></math> = 1062.5<br>Required % = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1062</mn><mo>.</mo><mn>5</mn><mo>-</mo><mn>785</mn></mrow><mn>785</mn></mfrac></math> &times; 100<br>= <math display=\"inline\"><mfrac><mrow><mn>277</mn><mo>.</mo><mn>5</mn></mrow><mrow><mn>785</mn></mrow></mfrac></math> &times; 100 = 35.35 %</p>",
                    solution_hi: "<p>73.(d) <br>कंपनी A द्वारा उत्पादित कुल स्कूटर = 360 + 580 + 1000 + 1240 = 3180<br>कंपनी A द्वारा उत्पादित औसत स्कूटर = <math display=\"inline\"><mfrac><mrow><mn>3180</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> = 795<br>कंपनी B द्वारा उत्पादित कुल स्कूटर = 420 + 940 + 780 + 950 = 3090<br>कंपनी B द्वारा उत्पादित औसत स्कूटर= <math display=\"inline\"><mfrac><mrow><mn>3090</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> = 772.5<br>कंपनी C द्वारा उत्पादित कुल स्कूटर = 380 + 570 + 1020 + 1180 = 3150<br>कंपनी C द्वारा उत्पादित औसत स्कूटर = <math display=\"inline\"><mfrac><mrow><mn>3150</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> = 787.5<br>A, B, C द्वारा उत्पादित स्कूटर का संयुक्त औसत = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>795</mn><mo>+</mo><mn>772</mn><mo>.</mo><mn>5</mn><mo>+</mo><mn>787</mn><mo>.</mo><mn>5</mn></mrow><mn>3</mn></mfrac></math><br>= <math display=\"inline\"><mfrac><mrow><mn>2355</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> = 785<br>कंपनी D द्वारा उत्पादित कुल स्कूटर = 520 + 980 + 1090 + 1660 = 4250<br>कंपनी D द्वारा निर्मित औसत स्कूटर = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>520</mn><mo>+</mo><mn>980</mn><mo>+</mo><mn>1090</mn><mo>+</mo><mn>1660</mn></mrow><mn>4</mn></mfrac></math> <br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4250</mn><mn>4</mn></mfrac></math> = 1062.5<br>आवश्यक % = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1062</mn><mo>.</mo><mn>5</mn><mo>-</mo><mn>785</mn></mrow><mn>785</mn></mfrac></math> &times; 100 <br>= <math display=\"inline\"><mfrac><mrow><mn>277</mn><mo>.</mo><mn>5</mn></mrow><mrow><mn>785</mn></mrow></mfrac></math> &times; 100 = 35.35%</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "74",
                    section: "17",
                    question_en: "<p>74. Find the average of the prime numbers lying between 50 and 76.</p>",
                    question_hi: "<p>74. 50 और 76 के बीच आने वाली अभाज्य संख्याओं का औसत ज्ञात करें।</p>",
                    options_en: [
                        "<p>66</p>",
                        "<p>60</p>",
                        "<p>62</p>",
                        "<p>64</p>"
                    ],
                    options_hi: [
                        "<p>66</p>",
                        "<p>60</p>",
                        "<p>62</p>",
                        "<p>64</p>"
                    ],
                    solution_en: "<p>74.(d)<br>Prime no&rsquo;s lying between 50 &amp; 76 = 53, 59, 61, 67, 71, 73<br>Required average = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>53</mn><mo>+</mo><mn>59</mn><mo>+</mo><mn>61</mn><mo>+</mo><mn>67</mn><mo>+</mo><mn>71</mn><mo>+</mo><mn>73</mn></mrow><mn>6</mn></mfrac></math> <br>= <math display=\"inline\"><mfrac><mrow><mn>384</mn></mrow><mrow><mn>6</mn></mrow></mfrac></math> = 64</p>",
                    solution_hi: "<p>74.(d)<br>अभाज्य संख्या 50 और 76 के बीच = 53, 59, 61, 67, 71, 73<br>आवश्यक औसत = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>53</mn><mo>+</mo><mn>59</mn><mo>+</mo><mn>61</mn><mo>+</mo><mn>67</mn><mo>+</mo><mn>71</mn><mo>+</mo><mn>73</mn></mrow><mn>6</mn></mfrac></math> <br>= <math display=\"inline\"><mfrac><mrow><mn>384</mn></mrow><mrow><mn>6</mn></mrow></mfrac></math> = 64</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "75",
                    section: "7",
                    question_en: "<p>75. The pie-chart below shows the expenditures (in ₹ lakh) of a country on various sports during a particular year. Study the graph carefully and answer the question given below it.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739184134192.png\" alt=\"rId64\" width=\"350\" height=\"334\"> <br>What is the average expenditure (in ₹ lakh) on tennis, golf, swimming, and basketball, if the expenditure on cricket is ₹72 lakh ?</p>",
                    question_hi: "<p>75. दिया गया पाई चार्ट किसी विशेष वर्ष के दौरान विभिन्न खेलों पर देश के व्ययों (₹ लाख में) को दर्शाता है। पाई चार्ट का ध्यानपूर्वक अध्ययन कीजिए और निम्नलिखित प्रश्न का उत्तर दीजिए।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739184134307.png\" alt=\"rId65\" width=\"363\" height=\"346\"> <br>यदि क्रिकेट पर व्यय ₹ 72 लाख है तो टेनिस, गोल्फ, स्वीमिंग, और बास्केटबॉल पर औसत व्यय (₹ लाख में) कितना है ?</p>",
                    options_en: [
                        "<p>138</p>",
                        "<p>44</p>",
                        "<p>69</p>",
                        "<p>120</p>"
                    ],
                    options_hi: [
                        "<p>138</p>",
                        "<p>44</p>",
                        "<p>69</p>",
                        "<p>120</p>"
                    ],
                    solution_en: "<p>75.(c) Average of (tennis, golf, swimming and basketball) = <math display=\"inline\"><mfrac><mrow><mn>85</mn><mo>+</mo><mn>48</mn><mo>+</mo><mn>45</mn><mo>+</mo><mn>52</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math><br>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>230</mn><mn>4</mn></mfrac></math>= 57.5&deg;<br>Now,<br>&rArr; 60&deg; = ₹ 72<br>&rArr; 57.5&deg; = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>&#8377;</mo><mn>72</mn></mrow><mrow><mn>60</mn><mo>&#176;</mo></mrow></mfrac></math> &times; 57.5&deg; = ₹ 69</p>",
                    solution_hi: "<p>75.(c) (टेनिस, गोल्फ, तैराकी और बास्केटबॉल) का औसत = <math display=\"inline\"><mfrac><mrow><mn>85</mn><mo>+</mo><mn>48</mn><mo>+</mo><mn>45</mn><mo>+</mo><mn>52</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math><br>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>230</mn><mn>4</mn></mfrac></math>= 57.5&deg;<br>अब,<br>&rArr; 60&deg; = ₹ 72<br>&rArr; 57.5&deg; = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>&#8377;</mo><mn>72</mn></mrow><mrow><mn>60</mn><mo>&#176;</mo></mrow></mfrac></math> &times; 57.5&deg; = ₹ 69</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "76",
                    section: "7",
                    question_en: "<p>76. Identify the segment in the sentence, which contains the grammatical error. <br>Tarun was laying down when the doorbell rang.</p>",
                    question_hi: "<p>76. Identify the segment in the sentence, which contains the grammatical error. <br>Tarun was laying down when the doorbell rang.</p>",
                    options_en: [
                        "<p>I was</p>",
                        "<p>laying down</p>",
                        "<p>when the doorbell rang</p>",
                        "<p>No error</p>"
                    ],
                    options_hi: [
                        "<p>I was</p>",
                        "<p>laying down</p>",
                        "<p>when the doorbell rang</p>",
                        "<p>No error</p>"
                    ],
                    solution_en: "<p>76.(b) laying down. <br>The phrase &lsquo;Laying down&rsquo; is incorrect in the context of the given sentence because &lsquo;Lay&rsquo; is used when a hen lays eggs. However, the phrase &ldquo;lying down&rdquo; means to take a rest. Similarly, in the given sentence, Tarun was taking a rest when the bell rang. Hence, &lsquo;lying down&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>76.(b) laying down. <br>दिए गए वाक्य के संदर्भ में \'Laying down\' गलत phrase है क्योंकि मुर्गी के अंडे देने पर \'Lay\' का प्रयोग किया जाता है। हालाँकि, \'Laying down\' phrase का अर्थ है - लेटना । इसी प्रकार, दिए गए वाक्य में, Tarun was taking a rest when the bell rang( जब घंटी बजी तब तरुण आराम कर रहा था।) इसलिए, &lsquo;lying down&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "77",
                    section: "7",
                    question_en: "<p>77. Select the most appropriate article to fill in the blank (&oslash;-no article).<br>_____SDO is the head of the sub-division of _____ government organisation.</p>",
                    question_hi: "<p>77. Select the most appropriate article to fill in the blank (&oslash;-no article).<br>______SDO is the head of the sub-division of _____ government organisation.</p>",
                    options_en: [
                        "<p>an; a</p>",
                        "<p>a; a</p>",
                        "<p>&Oslash;; a</p>",
                        "<p>an; &oslash;</p>"
                    ],
                    options_hi: [
                        "<p>an; a</p>",
                        "<p>a; a</p>",
                        "<p>&Oslash;; a</p>",
                        "<p>an; &oslash;</p>"
                    ],
                    solution_en: "<p>77.(a) an; a<br>Article &lsquo;an&rsquo; is used before the words beginning with a vowel sound. Article &lsquo;a&rsquo; is used before the words beginning with a consonant sound. In the given sentence, the abbreviation &lsquo;SDO&rsquo; begins with a vowel sound and the word &lsquo;government&rsquo; begins with a consonant sound. Hence, &lsquo;an; a&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>77.(a) an; a<br>Article &lsquo;an&rsquo; का प्रयोग vowel sound से प्रारम्भ होने वाले words से पहले किया जाता है। Article &lsquo;a&rsquo; का प्रयोग consonant sound से प्रारम्भ होने वाले शब्दों से पहले किया जाता है। दिए गए sentence में, संक्षिप्त नाम (abbreviation) &lsquo;SDO&rsquo; vowel sound से प्रारम्भ होता है और word &lsquo;government&rsquo; consonant sound से प्रारम्भ होता है। इसलिए, &lsquo;an; a&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "78",
                    section: "7",
                    question_en: "<p>78. Select the most appropriate meaning of the given idiom.<br>Apple of discord</p>",
                    question_hi: "<p>78. Select the most appropriate meaning of the given idiom.<br>Apple of discord</p>",
                    options_en: [
                        "<p>Solution of dispute</p>",
                        "<p>Matter of dispute</p>",
                        "<p>Cause of happiness</p>",
                        "<p>Disruption of communication</p>"
                    ],
                    options_hi: [
                        "<p>Solution of dispute</p>",
                        "<p>Matter of dispute</p>",
                        "<p>Cause of happiness</p>",
                        "<p>Disruption of communication</p>"
                    ],
                    solution_en: "<p>78.(b)<strong> Apple of discord- </strong>matter of dispute.<br>E.g.- The promotion became an apple of discord among the team members, as everyone believed they deserved it.</p>",
                    solution_hi: "<p>78.(b) <strong>Apple of discord-</strong> matter of dispute./विवाद का विषय।<br>E.g.- The promotion became an apple of discord among the team members, as everyone believed they deserved it.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "79",
                    section: "7",
                    question_en: "<p>79. Select the correct option to substitute the underlined segment in the given sentence. If no substitution is required, select \'No substitution\'. <br>The train <span style=\"text-decoration: underline;\">left before I reached</span> the station.</p>",
                    question_hi: "<p>79. Select the correct option to substitute the underlined segment in the given sentence. If no substitution is required, select \'No substitution\'. <br>The train <span style=\"text-decoration: underline;\">left before I reached</span> the station.</p>",
                    options_en: [
                        "<p>left before I had reached</p>",
                        "<p>had left before I reached</p>",
                        "<p>left before I have reached</p>",
                        "<p>No substitution</p>"
                    ],
                    options_hi: [
                        "<p>left before I had reached</p>",
                        "<p>had left before I reached</p>",
                        "<p>left before I have reached</p>",
                        "<p>No substitution</p>"
                    ],
                    solution_en: "<p>79.(b) had left before I reached<br>If two actions took place in the past then the 1st action must be in the Past perfect tense (Had + V<sub>3</sub>) and the 2nd action must be in the Simple Past tense(V<sub>2</sub>). In the given sentence, &lsquo;train leaving&rsquo; is the first action, so it will be written in past perfect tense. Hence, &lsquo;had left(V<sub>3</sub>) before I reached (V<sub>2</sub>)&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>79.(b) had left before I reached<br>यदि दो action past में हुए हैं, तो पहला action Past perfect tense (Had + V<sub>3</sub>) में तथा दूसरा action Simple Past tense(V<sub>2</sub>) में होना चाहिए। दिए गए sentence में, &lsquo;train leaving&rsquo; पहला action है, इसलिए इसे past perfect tense ​​में लिखा जाएगा। अतः , &lsquo;had left(V<sub>3</sub>) before I reached(V<sub>2</sub>)&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "80",
                    section: "7",
                    question_en: "<p>80. Choose the most appropriate option to change the narration (direct/indirect) of the given sentence<br>\"What a good idea!\", Seema remarked.</p>",
                    question_hi: "<p>80. Choose the most appropriate option to change the narration (direct/indirect) of the given sentence<br>\"What a good idea!\", Seema remarked.</p>",
                    options_en: [
                        "<p>Seema said what a good idea it is.</p>",
                        "<p>Seema told what an idea!</p>",
                        "<p>Seema exclaimed that it was a very good idea.</p>",
                        "<p>Seema exclaimed that the idea is good.</p>"
                    ],
                    options_hi: [
                        "<p>Seema said what a good idea it is.</p>",
                        "<p>Seema told what an idea!</p>",
                        "<p>Seema exclaimed that it was a very good idea.</p>",
                        "<p>Seema exclaimed that the idea is good.</p>"
                    ],
                    solution_en: "<p>80.(c) Seema exclaimed that it was a very good idea.<br>(a) Seema said what a good idea it <span style=\"text-decoration: underline;\">is</span>. (&lsquo;That&rsquo; is missing and Incorrect Verb)<br>(b) Seema <span style=\"text-decoration: underline;\">told</span> what an idea!. (Incorrect Reported Verb)<br>(d) Seema exclaimed that the idea <span style=\"text-decoration: underline;\">is</span> good. (Incorrect Verb)</p>",
                    solution_hi: "<p>80.(c) Seema exclaimed that it was a very good idea.<br>(a) Seema said what a good idea it <span style=\"text-decoration: underline;\">is</span>. (&lsquo;That\' का प्रयोग नहीं किया गया है और Verb गलत है)<br>(b) Seema <span style=\"text-decoration: underline;\">told</span> what an idea!.( Reported Verb गलत है )<br>(d) Seema exclaimed that the idea <span style=\"text-decoration: underline;\">is</span> good. ( Verb गलत है )</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "81",
                    section: "7",
                    question_en: "<p>81. Select the option that expresses the given sentence in active voice.<br>It will be done by me.</p>",
                    question_hi: "<p>81. Select the option that expresses the given sentence in active voice.<br>It will be done by me.</p>",
                    options_en: [
                        "<p>I will do it.</p>",
                        "<p>I do it.</p>",
                        "<p>I did it.</p>",
                        "<p>I have done it.</p>"
                    ],
                    options_hi: [
                        "<p>I will do it.</p>",
                        "<p>I do it.</p>",
                        "<p>I did it.</p>",
                        "<p>I have done it.</p>"
                    ],
                    solution_en: "<p>81.(a) I will do it.(Correct)<br>(b) I <span style=\"text-decoration: underline;\">do</span> it.(Incorrect Tense)<br>(c) I <span style=\"text-decoration: underline;\">did</span> it.(Incorrect Tense)<br>(d) I <span style=\"text-decoration: underline;\">have done</span> it.(Incorrect Tense)</p>",
                    solution_hi: "<p>81.(a) I will do it.(Correct)<br>(b) I <span style=\"text-decoration: underline;\">do</span> it.(गलत Tense)<br>(c) I <span style=\"text-decoration: underline;\">did</span> it.(गलत Tense)<br>(d) I <span style=\"text-decoration: underline;\">have done</span> it.(गलत Tense)</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "82",
                    section: "7",
                    question_en: "<p>82. Identify the best way to improve the underlined part of the given sentence. If there is no improvement required, select &lsquo;no improvement&rsquo;.<br>The workers of this textile factory <strong><span style=\"text-decoration: underline;\">demand higher wages</span></strong> for a long time.</p>",
                    question_hi: "<p>82. Identify the best way to improve the underlined part of the given sentence. If there is no improvement required, select &lsquo;no improvement&rsquo;.<br>The workers of this textile factory <span style=\"text-decoration: underline;\"><strong>demand higher wages</strong></span> for a long time.</p>",
                    options_en: [
                        "<p>have been demanding higher wages</p>",
                        "<p>demanded higher wages</p>",
                        "<p>No improvement</p>",
                        "<p>has demanded higher wages</p>"
                    ],
                    options_hi: [
                        "<p>have been demanding higher wages</p>",
                        "<p>demanded higher wages</p>",
                        "<p>No improvement</p>",
                        "<p>has demanded higher wages</p>"
                    ],
                    solution_en: "<p>82.(a) have been demanding higher wages. <br>When an activity began in the past and is still going on at the time of speaking, we use the present perfect continuous tense(has/have been + V-ing). Hence, &lsquo;have been demanding higher wages&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>82.(a) have been demanding higher wages. <br>जब कोई गतिविधि past में शुरू होती है और अभी भी चल रही है, तो हम present perfect continuous tense(has/have been + V-ing) का प्रयोग करेंगे । इसलिए, &lsquo;have been demanding higher wages&rsquo; (\'अधिक वेतन की मांग कर रहे हैं\') सबसे उपयुक्त उत्तर है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "83",
                    section: "7",
                    question_en: "<p>83. Given below are four jumbled sentences. Find the correct order: <br>P. The communication network cut off the quake hit villages from the rest of the world.<br>Q . A devastating earthquake that struck Maharashtra and parts of Karnataka measured 6.6 on the Richter scale.<br>R. Ten thousand people were killed and an equal number of people were injured.<br>S. Forty villages of Maharashtra were destroyed completely.</p>",
                    question_hi: "<p>83. Given below are four jumbled sentences. Find the correct order: <br>P. The communication network cut off the quake hit villages from the rest of the world.<br>Q . A devastating earthquake that struck Maharashtra and parts of Karnataka measured 6.6 on the Richter scale.<br>R. Ten thousand people were killed and an equal number of people were injured.<br>S. Forty villages of Maharashtra were destroyed completely.</p>",
                    options_en: [
                        "<p>SPQR</p>",
                        "<p>SQPR</p>",
                        "<p>QRSP</p>",
                        "<p>PQSR</p>"
                    ],
                    options_hi: [
                        "<p>SPQR</p>",
                        "<p>SQPR</p>",
                        "<p>QRSP</p>",
                        "<p>PQSR</p>"
                    ],
                    solution_en: "<p>83.(c) QRSP<br>Q will come first. It tells about the earthquake and intensity of the devastating earthquake. All the other three sentences are about the harm caused by the earthquake. With Q beginning we have option (c) only. So the answer is (c) QRSP</p>",
                    solution_hi: "<p>83.(c) QRSP<br>Q पहले आएगा। यह भूकंप और विनाशकारी भूकंप की तीव्रता के बारे में बताता है। अन्य तीनों वाक्य भूकंप से हुए नुकसान के बारे में हैं। Q की शुरुआत के साथ हमारे पास केवल विकल्प (c) है। तो उत्तर (c) QRSP है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "84",
                    section: "7",
                    question_en: "<p>84. Select the sentence that has NO spelling errors.</p>",
                    question_hi: "<p>84. Select the sentence that has NO spelling errors.</p>",
                    options_en: [
                        "<p>The clear blue waters of the laggon called to me, inviting me to dieve into its peaceful embrece.</p>",
                        "<p>The clear blue waters of the lagoon caled to me, inviting me to dieve into its peacefull embrace.</p>",
                        "<p>The clear blue waters of the lagon called to me, inviting me to dive into its peacefull embrece.</p>",
                        "<p>The clear blue waters of the lagoon called to me, inviting me to dive into its peaceful embrace.</p>"
                    ],
                    options_hi: [
                        "<p>The clear blue waters of the laggon called to me, inviting me to dieve into its peaceful embrece.</p>",
                        "<p>The clear blue waters of the lagoon caled to me, inviting me to dieve into its peacefull embrace.</p>",
                        "<p>The clear blue waters of the lagon called to me, inviting me to dive into its peacefull embrece.</p>",
                        "<p>The clear blue waters of the lagoon called to me, inviting me to dive into its peaceful embrace.</p>"
                    ],
                    solution_en: "<p>84.(d) The clear blue waters of the lagoon called to me, inviting me to dive into its peaceful embrace.</p>",
                    solution_hi: "<p>84.(d) The clear blue waters of the lagoon called to me, inviting me to dive into its peaceful embrace.</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "85",
                    section: "7",
                    question_en: "<p>85. Select the most appropriate meaning of the idiom given in the following Question.<br>An axe to grind</p>",
                    question_hi: "<p>85. Select the most appropriate meaning of the idiom given in the following Question.<br>An axe to grind</p>",
                    options_en: [
                        "<p>a blunt axe</p>",
                        "<p>a sharp tongue</p>",
                        "<p>a private interest to serve</p>",
                        "<p>a tendency to fight</p>"
                    ],
                    options_hi: [
                        "<p>a blunt axe</p>",
                        "<p>a sharp tongue</p>",
                        "<p>a private interest to serve</p>",
                        "<p>a tendency to fight</p>"
                    ],
                    solution_en: "<p>85.(c) a private interest to serve. <br>Example - Rahul knew Raj had an axe to grind and turned down his offer of help.</p>",
                    solution_hi: "<p>85.(c) a private interest to serve./ सेवा करने के लिए एक निजी हित।<br>उदाहरण - Rahul knew Raj had an axe to grind and turned down his offer of help./राहुल जानता था कि राज के सेवा करने में उसका निजी स्वार्थ है इसलिए उसने उनके मदद के प्रस्ताव को ठुकरा दिया।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "86",
                    section: "7",
                    question_en: "<p>86. Select the option that can be used as a one-word substitute for the given group of words.<br>Crime of killing a king</p>",
                    question_hi: "<p>86. Select the option that can be used as a one-word substitute for the given group of words.<br>Crime of killing a king</p>",
                    options_en: [
                        "<p>Pesticide</p>",
                        "<p>Regicide</p>",
                        "<p>Homicide</p>",
                        "<p>Genocide</p>"
                    ],
                    options_hi: [
                        "<p>Pesticide</p>",
                        "<p>Regicide</p>",
                        "<p>Homicide</p>",
                        "<p>Genocide</p>"
                    ],
                    solution_en: "<p>86.(b) <strong>Regicide</strong>- crime of killing a king.<br><strong>Pesticide-</strong> a chemical substance used to control, destroy, or prevent pests, diseases, or unwanted plants.<br><strong>Homicide-</strong> the crime of killing a person.<br><strong>Genocide-</strong> the deliberate killing of a large number of people from a particular nation or ethnic group with the aim of destroying that nation or group.</p>",
                    solution_hi: "<p>86.(b) <strong>Regicide</strong> (राज-हत्या)- crime of killing a king.<br><strong>Pesticide</strong> (कीटनाशक)- a chemical substance used to control, destroy, or prevent pests, diseases, or unwanted plants.<br><strong>Homicide</strong> (मानव हत्या)- the crime of killing a person.<br><strong>Genocide</strong> (जनसंहार)- the deliberate killing of a large number of people from a particular nation or ethnic group with the aim of destroying that nation or group.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "87",
                    section: "7",
                    question_en: "87. The following sentence has been divided into parts. One of them may contain an error.<br />Select the part that contains the error from the given options. If you don\'t find any error, mark \'No error\' as your answer.<br />By the time she retires, / she has been teaching / at the university for 30 years.",
                    question_hi: "87. The following sentence has been divided into parts. One of them may contain an error.<br />Select the part that contains the error from the given options. If you don\'t find any error, mark \'No error\' as your answer.<br />By the time she retires, / she has been teaching / at the university for 30 years.",
                    options_en: [
                        " she has been teaching",
                        " at the university for 30 years",
                        " By the time she retires ",
                        " No error"
                    ],
                    options_hi: [
                        " she has been teaching",
                        " at the university for 30 years",
                        " By the time she retires ",
                        " No error"
                    ],
                    solution_en: "87.(a) she has been teaching<br />The future perfect tense is used to express an action that will be completed before another point in the future. The correct structure for future tense is ‘By the time + Simple present tense, … Future perfect tense’. Hence, ‘she will have taught’ is the most appropriate answer.",
                    solution_hi: "87.(a) she has been teaching<br />Future perfect tense का प्रयोग तब किया जाता है जब कोई action भविष्य में किसी अन्य घटना(event) या समय से पहले पूरा हो चुका होगा। ‘By the time + Simple present tense, … Future perfect tense’, Future tense के लिए सही structure है। अतः, ‘she will have taught’ सबसे उपयुक्त उत्तर है।",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "88",
                    section: "7",
                    question_en: "<p>88. Select the most appropriate synonym of the given word.<br>Insolent</p>",
                    question_hi: "<p>88. Select the most appropriate synonym of the given word.<br>Insolent</p>",
                    options_en: [
                        "<p>Humble</p>",
                        "<p>Dutiful</p>",
                        "<p>Dignified</p>",
                        "<p>Disrespectful</p>"
                    ],
                    options_hi: [
                        "<p>Humble</p>",
                        "<p>Dutiful</p>",
                        "<p>Dignified</p>",
                        "<p>Disrespectful</p>"
                    ],
                    solution_en: "<p>88.(d) <strong>Disrespectful</strong><br><strong>Insolent</strong>- disrespectful in speech or behaviour.<br><strong>Humble</strong>- not thinking that you are better or more important than other people.<br><strong>Dutiful</strong>- happy to respect and obey somebody.<br><strong>Dignified</strong>- behaving in a calm, serious way that makes other people respect you.</p>",
                    solution_hi: "<p>88.(d) <strong>Disrespectful</strong><br><strong>Insolent </strong>(असभ्य)- disrespectful in speech or behaviour.<br><strong>Humble </strong>(विनम्र)- not thinking that you are better or more important than other people.<br><strong>Dutiful </strong>(कर्तव्य परायण)- happy to respect and obey somebody.<br><strong>Dignified </strong>(गरिमापूर्ण)- behaving in a calm, serious way that makes other people respect you. </p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "89",
                    section: "7",
                    question_en: "<p>89. Parts of a sentence are given below in jumbled order. Arrange the parts in the correct order to form a meaningful sentence.<br>(O) as it was the last one of the evening<br>(P) the city bus was overcrowded<br>(Q) and this gave an opportunity<br>(R) to the pickpockets<br>(S) to try their skill</p>",
                    question_hi: "<p>89. Parts of a sentence are given below in jumbled order. Arrange the parts in the correct order to form a meaningful sentence.<br>(O) as it was the last one of the evening<br>(P) the city bus was overcrowded<br>(Q) and this gave an opportunity<br>(R) to the pickpockets<br>(S) to try their skill</p>",
                    options_en: [
                        "<p>SRPOQ</p>",
                        "<p>PSORQ</p>",
                        "<p>POQRS</p>",
                        "<p>SROPQ</p>"
                    ],
                    options_hi: [
                        "<p>SRPOQ</p>",
                        "<p>PSORQ</p>",
                        "<p>POQRS</p>",
                        "<p>SROPQ</p>"
                    ],
                    solution_en: "<p>89.(c) POQRS<br>The given sentence starts with Part P as it introduces the main idea of the sentence, i.e. &lsquo;The city bus was crowded&rsquo;. Part O states the reason for the overcrowding of the bus and Part Q states the opportunity arising out of this situation. So, Q will follow O. Further, Part R states that there is an opportunity for the pickpockets and Part S states that the pickpockets used the opportunity to try their skills. So, S will follow R. Going through the options, option &lsquo;c&rsquo; has the correct sequence.</p>",
                    solution_hi: "<p>89.(c) POQRS<br>दिया गया वाक्य Part P से प्रारंभ होगा क्योंकि इसमे sentence का मुख्य विचार &lsquo;The city bus was crowded&rsquo; शामिल है। Part O, बस में भीड़भाड़ का कारण बताता है और Part Q इस स्थिति से उत्पन्न अवसर को बताता है। इसलिए, O के बाद Q आएगा। इसके अलावा, Part R बताता है कि जेबकतरों के लिए एक अवसर है और Part S बताता है कि जेबकतरों ने अपने कौशल को आजमाने के लिए अवसर का उपयोग किया। इसलिए, R के बाद S आएगा। Options के मध्यम से जाने पर option &lsquo;c&rsquo; में सही sequence है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "90",
                    section: "7",
                    question_en: "<p>90. Select the option that expresses the given sentence in active voice.<br>My pencils have been stolen.</p>",
                    question_hi: "<p>90. Select the option that expresses the given sentence in active voice.<br>My pencils have been stolen.</p>",
                    options_en: [
                        "<p>Somebody have stolen my pencils.</p>",
                        "<p>Somebody steals my pencils.</p>",
                        "<p>Somebody has stolen my pencils.</p>",
                        "<p>Somebody stole my pencils.</p>"
                    ],
                    options_hi: [
                        "<p>Somebody have stolen my pencils.</p>",
                        "<p>Somebody steals my pencils.</p>",
                        "<p>Somebody has stolen my pencils.</p>",
                        "<p>Somebody stole my pencils.</p>"
                    ],
                    solution_en: "<p>90.(c) Somebody has stolen my pencils.(Correct)<br>(a) Somebody <span style=\"text-decoration: underline;\">have</span> stolen my pencils.(Incorrect Helping Verb)<br>(b) Somebody <span style=\"text-decoration: underline;\">steals</span> my pencils.(Incorrect Tense)<br>(d) Somebody <span style=\"text-decoration: underline;\">stole</span> my pencils.(Incorrect Tense)</p>",
                    solution_hi: "<p>90.(c) Somebody has stolen my pencils.(Correct)<br>(a) Somebody <span style=\"text-decoration: underline;\">have</span> stolen my pencils.(गलत Helping Verb)<br>(b) Somebody <span style=\"text-decoration: underline;\">steals</span> my pencils.(गलत Tense)<br>(d) Somebody <span style=\"text-decoration: underline;\">stole</span> my pencils.(गलत Tense)</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "91",
                    section: "7",
                    question_en: "<p>91. Select the option that is similar in meaning to the underlined word in the following sentence.<br>A good critic should also be a person of great <span style=\"text-decoration: underline;\">candour</span>.</p>",
                    question_hi: "<p>91. Select the option that is similar in meaning to the underlined word in the following sentence.<br>A good critic should also be a person of great <span style=\"text-decoration: underline;\">candour</span>.</p>",
                    options_en: [
                        "<p>will</p>",
                        "<p>resolution</p>",
                        "<p>honesty</p>",
                        "<p>power</p>"
                    ],
                    options_hi: [
                        "<p>will</p>",
                        "<p>resolution</p>",
                        "<p>honesty</p>",
                        "<p>power</p>"
                    ],
                    solution_en: "<p>91.(c) honesty <br><strong>Candour </strong>- the quality of being honest.</p>",
                    solution_hi: "<p>91.(c) honesty <br><strong>Candour </strong>- the quality of being honest./ईमानदार होने का गुण</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "92",
                    section: "7",
                    question_en: "<p>92. Select the most appropriate ANTONYM of the given word.<br>Mighty</p>",
                    question_hi: "<p>92. Select the most appropriate ANTONYM of the given word.<br>Mighty</p>",
                    options_en: [
                        "<p>Haughty</p>",
                        "<p>Forceful</p>",
                        "<p>Forcible</p>",
                        "<p>Weak</p>"
                    ],
                    options_hi: [
                        "<p>Haughty</p>",
                        "<p>Forceful</p>",
                        "<p>Forcible</p>",
                        "<p>Weak</p>"
                    ],
                    solution_en: "<p>92.(d) <strong>Weak</strong>- lacking strength or power.<br><strong>Mighty-</strong> having great strength or power.<br><strong>Haughty-</strong> arrogantly superior or disdainful.<br><strong>Forceful-</strong> strong and assertive.<br><strong>Forcible-</strong> achieved through the use of physical power or force.</p>",
                    solution_hi: "<p>92.(d) <strong>Weak</strong> (कमजोर) - lacking strength or power.<br><strong>Mighty</strong> (शक्तिशाली) - having great strength or power.<br><strong>Haughty</strong> (अभिमानी) - arrogantly superior or disdainful.<br><strong>Forceful</strong> (प्रभावशाली) - strong and assertive.<br><strong>Forcible</strong> (बलपूर्वक) - achieved through the use of physical power or force.</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "93",
                    section: "7",
                    question_en: "<p>93. Select the option that can be used as a one-word substitute for the given group of words.<br>Weak and cowardly</p>",
                    question_hi: "<p>93. Select the option that can be used as a one-word substitute for the given group of words.<br>Weak and cowardly</p>",
                    options_en: [
                        "<p>Timid</p>",
                        "<p>Meek</p>",
                        "<p>Pusillanimous</p>",
                        "<p>Anxious</p>"
                    ],
                    options_hi: [
                        "<p>Timid</p>",
                        "<p>Meek</p>",
                        "<p>Pusillanimous</p>",
                        "<p>Anxious</p>"
                    ],
                    solution_en: "<p>93.(c) <strong>Pusillanimous</strong>- weak and cowardly.<br><strong>Timid-</strong> showing a lack of courage or confidence.<br><strong>Meek-</strong> quiet, gentle, and not willing to argue or express your opinions in a forceful way.<br><strong>Anxious-</strong> feeling or showing worry, nervousness, or unease about something with an uncertain outcome.</p>",
                    solution_hi: "<p>93.(c) <strong>Pusillanimous </strong>(कायर) - weak and cowardly.<br><strong>Timid</strong> (डरपोक) - showing a lack of courage or confidence.<br><strong>Meek</strong> (विनम्र) - quiet, gentle, and not willing to argue or express your opinions in a forceful way.<br><strong>Anxious</strong> (चिंतित) - feeling or showing worry, nervousness, or unease about something with an uncertain outcome.</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "94",
                    section: "7",
                    question_en: "<p>94. Select the most appropriate option to fill in the blank. <br>I never could quite get the idea _______my mind that I should do some teaching, yet I felt a great deal of satisfaction with the pastorate.</p>",
                    question_hi: "<p>94. Select the most appropriate option to fill in the blank. <br>I never could quite get the idea _______my mind that I should do some teaching, yet I felt a great deal of satisfaction with the pastorate.</p>",
                    options_en: [
                        "<p>out of</p>",
                        "<p>out act</p>",
                        "<p>out come</p>",
                        "<p>out cast</p>"
                    ],
                    options_hi: [
                        "<p>out of</p>",
                        "<p>out act</p>",
                        "<p>out come</p>",
                        "<p>out cast</p>"
                    ],
                    solution_en: "<p>94.(a) &lsquo;Out of my mind&rdquo; means to make yourself stop thinking about something.</p>",
                    solution_hi: "<p>94.(a) &lsquo;Out of my mind\' का अर्थ है अपने आप को किसी चीज़ के बारे में सोचने से रोकना।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "95",
                    section: "7",
                    question_en: "<p>95. Select the most appropriate ANTONYM of the given word.<br>Despondency</p>",
                    question_hi: "<p>95. Select the most appropriate ANTONYM of the given word.<br>Despondency</p>",
                    options_en: [
                        "<p>Hopelessness</p>",
                        "<p>Cheerfulness</p>",
                        "<p>Satisfaction</p>",
                        "<p>Dejection</p>"
                    ],
                    options_hi: [
                        "<p>Hopelessness</p>",
                        "<p>Cheerfulness</p>",
                        "<p>Satisfaction</p>",
                        "<p>Dejection</p>"
                    ],
                    solution_en: "<p>95.(b) <strong>Cheerfulness</strong>- the quality or state of being happy/pleasant. <br><strong>Despondency-</strong> unhappy and having no enthusiasm.<br><strong>Hopelessness-</strong> lack of hope.<br><strong>Satisfaction-</strong> a happy or pleasant feeling.<br><strong>Dejection-</strong> the feeling of being unhappy, disappointed, or without hope.</p>",
                    solution_hi: "<p>95.(b) <strong>Cheerfulness</strong> (प्रसन्नता) - the quality or state of being happy/pleasant. <br><strong>Despondency</strong> (निराशा) - unhappy and having no enthusiasm.<br><strong>Hopelessness</strong> (निराशा) - lack of hope.<br><strong>Satisfaction</strong> (संतुष्टि) - a happy or pleasant feeling.<br><strong>Dejection</strong> (निराशा) - the feeling of being unhappy, disappointed, or without hope.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "96",
                    section: "7",
                    question_en: "<p>96. <strong>Cloze Test :</strong><br>Sustainability is ____(96)___ longer just a buzzword. It ____ (97) ____ a necessity and the primary agenda of the world today. Like many other countries, India too is witnessing a growth of ____(98)_____ green economy. It ____(99)____ estimated that India\'s renewable energy target will create more than 3.4 million new job opportunities ____(100)____2030. In fact, a recent survey stated that a Sustainability Manager is going to be one of the top 10 jobs this decade.<br>In the context of the passage, select the most appropriate option to fill in blank (96)</p>",
                    question_hi: "<p>96. <strong>Cloze Test :</strong><br>Sustainability is ____(96)___ longer just a buzzword. It ____ (97) ____ a necessity and the primary agenda of the world today. Like many other countries, India too is witnessing a growth of ____(98)_____ green economy. It ____(99)____ estimated that India\'s renewable energy target will create more than 3.4 million new job opportunities ____(100)____ 2030. In fact, a recent survey stated that a Sustainability Manager is going to be one of the top 10 jobs this decade.<br>In the context of the passage, select the most appropriate option to fill in blank (96)</p>",
                    options_en: [
                        "<p>any</p>",
                        "<p>never</p>",
                        "<p>no</p>",
                        "<p>not</p>"
                    ],
                    options_hi: [
                        "<p>any</p>",
                        "<p>never</p>",
                        "<p>no</p>",
                        "<p>not</p>"
                    ],
                    solution_en: "<p>96.(c) no<br>The phrase &lsquo;no longer&rsquo; means earlier but not now. The given passage states that sustainability is no longer just a buzzword. Hence, &lsquo;no&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>96.(c) no<br>Phrase &lsquo;no longer&rsquo; का अर्थ है पहले लेकिन अब नहीं। दिए गए passage में कहा गया है कि स्थिरता(sustainability) अब केवल एक चर्चा(buzzword) का विषय नहीं रह गई है। अतः, &lsquo;no&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "97",
                    section: "7",
                    question_en: "<p>97. <strong>Cloze Test :</strong><br>Sustainability is ____(96)___ longer just a buzzword. It ____ (97) ____ a necessity and the primary agenda of the world today. Like many other countries, India too is witnessing a growth of ____(98)_____ green economy. It ____(99)____ estimated that India\'s renewable energy target will create more than 3.4 million new job opportunities ____(100)____ 2030. In fact, a recent survey stated that a Sustainability Manager is going to be one of the top 10 jobs this decade.<br>In the context of the passage, select the most appropriate option to fill in blank (97).</p>",
                    question_hi: "<p>97.<strong> Cloze Test :</strong><br>Sustainability is ____(96)___ longer just a buzzword. It ____ (97) ____ a necessity and the primary agenda of the world today. Like many other countries, India too is witnessing a growth of ____(98)_____ green economy. It ____(99)____ estimated that India\'s renewable energy target will create more than 3.4 million new job opportunities ____(100)____ 2030. In fact, a recent survey stated that a Sustainability Manager is going to be one of the top 10 jobs this decade.<br>In the context of the passage, select the most appropriate option to fill in blank (97).</p>",
                    options_en: [
                        "<p>will be</p>",
                        "<p>is</p>",
                        "<p>might be</p>",
                        "<p>was</p>"
                    ],
                    options_hi: [
                        "<p>will be</p>",
                        "<p>is</p>",
                        "<p>might be</p>",
                        "<p>was</p>"
                    ],
                    solution_en: "<p>97.(b) is<br>Simple present tense will be used as the given sentence is expressing a general truth about sustainability at present. Hence, &lsquo;is&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>97.(b) is<br>Simple present tense का प्रयोग किया जाएगा क्योंकि दिया गया sentence वर्तमान में स्थिरता(sustainability) के बारे में एक सामान्य सत्य व्यक्त कर रहा है। अतः, &lsquo;is&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "98",
                    section: "7",
                    question_en: "<p>98. <strong>Cloze Test :</strong><br>Sustainability is ____(96)___ longer just a buzzword. It ____ (97) ____ a necessity and the primary agenda of the world today. Like many other countries, India too is witnessing a growth of ____(98)_____ green economy. It ____(99)____ estimated that India\'s renewable energy target will create more than 3.4 million new job opportunities ____(100)____ 2030. In fact, a recent survey stated that a Sustainability Manager is going to be one of the top 10 jobs this decade.<br>In the context of the passage, select the most appropriate option to fill in blank (98).</p>",
                    question_hi: "<p>98. <strong>Cloze Test :</strong><br>Sustainability is ____(96)___ longer just a buzzword. It ____ (97) ____ a necessity and the primary agenda of the world today. Like many other countries, India too is witnessing a growth of ____(98)_____ green economy. It ____(99)____ estimated that India\'s renewable energy target will create more than 3.4 million new job opportunities ____(100)____ 2030. In fact, a recent survey stated that a Sustainability Manager is going to be one of the top 10 jobs this decade.<br>In the context of the passage, select the most appropriate option to fill in blank (98).</p>",
                    options_en: [
                        "<p>his</p>",
                        "<p>hers</p>",
                        "<p>its</p>",
                        "<p>their</p>"
                    ],
                    options_hi: [
                        "<p>his</p>",
                        "<p>hers</p>",
                        "<p>its</p>",
                        "<p>their</p>"
                    ],
                    solution_en: "<p>98.(c) its<br>&lsquo;Its&rsquo; is a possessive used for things, places, animals and human children. In the given sentence, &lsquo;its&rsquo; has been used for &lsquo;India&rsquo;. Hence, &lsquo;its&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>98.(c) its<br>&lsquo;Its&rsquo; एक possessive शब्द है जिसका प्रयोग वस्तु, स्थान, जन्तु एवं मानव बच्चों(human children) के लिए किया जाता है। दिए गए sentence में, &lsquo;its&rsquo; का प्रयोग &lsquo;India&rsquo; के लिए किया गया है। अतः, &lsquo;its&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "99",
                    section: "7",
                    question_en: "<p>99. <strong>Cloze Test :</strong><br>Sustainability is ____(96)___ longer just a buzzword. It ____ (97) ____ a necessity and the primary agenda of the world today. Like many other countries, India too is witnessing a growth of ____(98)_____ green economy. It ____(99)____ estimated that India\'s renewable energy target will create more than 3.4 million new job opportunities ____(100)____ 2030. In fact, a recent survey stated that a Sustainability Manager is going to be one of the top 10 jobs this decade.<br>In the context of the passage, select the most appropriate option to fill in blank (99).</p>",
                    question_hi: "<p>99. <strong>Cloze Test :</strong><br>Sustainability is ____(96)___ longer just a buzzword. It ____ (97) ____ a necessity and the primary agenda of the world today. Like many other countries, India too is witnessing a growth of ____(98)_____ green economy. It ____(99)____ estimated that India\'s renewable energy target will create more than 3.4 million new job opportunities ____(100)____ 2030. In fact, a recent survey stated that a Sustainability Manager is going to be one of the top 10 jobs this decade.<br>In the context of the passage, select the most appropriate option to fill in blank (99).</p>",
                    options_en: [
                        "<p>will be</p>",
                        "<p>were</p>",
                        "<p>had been</p>",
                        "<p>is</p>"
                    ],
                    options_hi: [
                        "<p>will be</p>",
                        "<p>were</p>",
                        "<p>had been</p>",
                        "<p>is</p>"
                    ],
                    solution_en: "<p>99.(d) is <br>The given sentence is an example of passive voice of simple present tense and the subject &lsquo;it&rsquo; is singular. &lsquo;Singular Sub + is + V<sub>3</sub>(estimated)&rsquo; is the correct grammatical structure for it. Hence, &lsquo;is&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>99.(d) is <br>दिया गया sentence, simple present tense के passive voice का एक example है और subject &lsquo;it&rsquo; singular है। &lsquo;Singular Sub + is + V<sub>3</sub>(estimated)&rsquo; इसके लिए सही grammatical structure है। अतः, &lsquo;is&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "100",
                    section: "misc",
                    question_en: "<p>100. <strong>Cloze Test :</strong><br>Sustainability is ____(96)___ longer just a buzzword. It ____ (97) ____ a necessity and the primary agenda of the world today. Like many other countries, India too is witnessing a growth of ____(98)_____ green economy. It ____(99)____ estimated that India\'s renewable energy target will create more than 3.4 million new job opportunities ____(100)____ 2030. In fact, a recent survey stated that a Sustainability Manager is going to be one of the top 10 jobs this decade.<br>In the context of the passage, select the most appropriate option to fill in blank (100).</p>",
                    question_hi: "<p>100. <strong>Cloze Test :</strong><br>Sustainability is ____(96)___ longer just a buzzword. It ____ (97) ____ a necessity and the primary agenda of the world today. Like many other countries, India too is witnessing a growth of ____(98)_____ green economy. It ____(99)____ estimated that India\'s renewable energy target will create more than 3.4 million new job opportunities ____(100)____ 2030. In fact, a recent survey stated that a Sustainability Manager is going to be one of the top 10 jobs this decade.<br>In the context of the passage, select the most appropriate option to fill in blank (100).</p>",
                    options_en: [
                        "<p>with</p>",
                        "<p>since</p>",
                        "<p>by</p>",
                        "<p>for</p>"
                    ],
                    options_hi: [
                        "<p>with</p>",
                        "<p>since</p>",
                        "<p>by</p>",
                        "<p>for</p>"
                    ],
                    solution_en: "<p>100.(c) by<br>&lsquo;By&rsquo; is used to indicate the end point of a time period of an action or event. Similarly, in the given passage, 2030 is the end point of time for creating more than 3.4 million new jobs. Hence, &lsquo;by&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>100.(c) by<br>&lsquo;By&rsquo; का प्रयोग किसी action या event के time period के अंतिम बिंदु को indicate करने के लिए किया जाता है। इसी तरह, दिए गए passage में, 2030 तक, 3.4 मिलियन से अधिक नये रोजगार सृजित होने का अंतिम समय है। अतः, &lsquo;by&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'question-box d-flex align-items-center justify-content-center border rounded';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.question-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            const html = `
                <div class="card shadow-sm mb-4">
                    <div class="card-body">
                        <div class="question-text mb-4">
                            <div class="en">${question.question_en}</div>
                            <div class="hi" style="display:none">${question.question_hi}</div>
                        </div>
                        <div class="options">
                            ${['a', 'b', 'c', 'd'].map((opt, i) => `
                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="radio" name="q${index}" 
                                        id="opt-${index}-${opt}" value="${opt}" 
                                        ${answers[question.id] === opt ? 'checked' : ''}
                                        onclick="selectAnswer('${question.id}', '${opt}')">
                                    <label class="form-check-label w-100 py-2 px-3 rounded hover-bg-light" for="opt-${index}-${opt}">
                                        <div class="en">${question.options_en[i]}</div>
                                        <div class="hi" style="display:none">${question.options_hi[i]}</div>
                                    </label>
                                </div>
                            `).join('')}
                        </div>
                        ${submitted ? `
                            <div class="alert ${answers[question.id] === question.correct ? 'alert-success' : 'alert-danger'} mt-3">
                                ${answers[question.id] ? (answers[question.id] === question.correct ? 'Correct!' : 'Incorrect!') : 'Not attempted'}
                                <div class="mt-2">
                                    <strong>Solution:</strong>
                                    <div class="en">${question.solution_en}</div>
                                    <div class="hi" style="display:none">${question.solution_hi}</div>
                                </div>
                            </div>
                        ` : ''}
                    </div>
                </div>
            `;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // If in mobile view, close the menu
            if (window.innerWidth < 768) {
                toggleMenu(false);
            }
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                document.getElementById(`box-${index}`).classList.add('attempted');
            }
        }
        // Navigate to previous question
        function prevQuestion() {
            if (currentQuestion > 0) {
                showQuestion(currentQuestion - 1);
            }
        }
        // Navigate to next question
        function nextQuestion() {
            if (currentQuestion < questions.length - 1) {
                showQuestion(currentQuestion + 1);
            }
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) {
                showQuestion(section.start);
            }
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update button text
            document.getElementById('lang-btn').innerHTML = currentLang === 'en' ? 
                '<i class="fas fa-language"></i> हिंदी' : 
                '<i class="fas fa-language"></i> English';
        }
        // Toggle menu for mobile
        function toggleMenu(show) {
            const nav = document.getElementById('question-nav');
            const content = document.getElementById('question-content');
            if (show === undefined) {
                show = nav.style.right === '-100%' || nav.style.right === '';
            }
            if (show) {
                nav.style.right = '0';
                if (window.innerWidth >= 768) {
                    content.style.marginRight = '320px';
                }
            } else {
                nav.style.right = window.innerWidth < 768 ? '-100%' : '0';
                content.style.marginRight = window.innerWidth < 768 ? '0' : '320px';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
            document.getElementById('total-count').textContent = questions.length;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.remove('attempted');
                    box.classList.add('bg-secondary', 'text-white');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.remove('attempted');
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.remove('attempted');
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            resultsModal.show();
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check-circle me-1"></i> Submitted';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            resultsModal.hide();
            showQuestion(0);
        }
    </script>
</body>
</html>