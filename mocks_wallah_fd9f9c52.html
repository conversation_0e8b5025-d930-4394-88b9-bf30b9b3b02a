<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">30:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 30</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">30</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 30 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["6"] = {
                name: "Reasoning",
                start: 0,
                end: 28
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="6">Reasoning</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 29,
                end: 29
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "6",
                    question_en: "1. Pick the odd one out, Eastern  Railway, Western Railway , West Coast Railway, East Coast Railway",
                    question_hi: "1. विषम को चुनें, ईस्टर्न रेलवे, वेस्टर्न रेलवे, वेस्ट कोस्ट रेलवे, ईस्ट कोस्ट रेलवे",
                    options_en: [" Western Railway", " Eastern Railway", 
                                " West Coast Railway", " East Coast Railway"],
                    options_hi: [" पश्चिम रेलवे", " पूर्वी रेलवे",
                                " वेस्ट कोस्ट रेलवे", " पूर्वी तट रेलवे"],
                    solution_en: "1.(c) There is no zone named West Coast Railway in India whereas all others are zones of Railway.",
                    solution_hi: "1.(c) भारत में वेस्ट कोस्ट रेलवे नाम का कोई ज़ोन नहीं है जबकि अन्य सभी रेलवे के ज़ोन हैं।",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "2",
                    section: "6",
                    question_en: "<p>2. Select the option that will fill in the blank and complete the given series. <br>A9B, B125C, C49D, D729E,___</p>",
                    question_hi: "<p>2. उस विकल्प का चयन करें जो रिक्त स्थान को भरेगा और दी गई श्रृंखला को पूरा करेगा।<br>A9B, B125C, C49D, D729E,___</p>",
                    options_en: ["<p>E36F</p>", "<p>G221H</p>", 
                                "<p>E121F</p>", "<p>F122G</p>"],
                    options_hi: ["<p>E36F</p>", "<p>G221H</p>",
                                "<p>E121F</p>", "<p>F122G</p>"],
                    solution_en: "<p>2.(c) <strong>Logic :-</strong><br>A&nbsp; + 1 = B, B + 1 = C, C + 1 = D, D + 1 = E <br>3<sup>2</sup> = 9, 5<sup>3</sup> = 125, 7<sup>2</sup> = 49, 27<sup>2</sup> = 729, 11<sup>2</sup> = 121 (3 + 4 = 7 , 7 + 4 = 11)<br>B + 1 = C, C + 1 = D, D + 1 = E, E + 1 = F</p>",
                    solution_hi: "<p>2.(c) <strong>तर्क :-</strong><br>A&nbsp; + 1 = B, B + 1 = C, C + 1 = D, D + 1 = E&nbsp;<br>3<sup>2 </sup>= 9, 5<sup>3</sup> = 125, 7<sup>2</sup> = 49, 27<sup>2</sup> = 729, 11<sup>2</sup> = 121 (3 + 4 = 7 , 7 + 4 = 11)<br>B + 1 = C, C + 1 = D, D + 1 = E, E + 1 = F</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "3",
                    section: "6",
                    question_en: "<p>3. Read the given statements and conclusions carefully. Assuming that the information given in the statements is true, even if it appears to be at variance with commonly known facts, decide which of the given conclusions logically follows from the statements.<br><strong>Statements:</strong><br>All boys are chefs.<br>All chefs are villagers.<br><strong>Conclusions:</strong><br>1. All boys are villagers.<br>2. All chefs are boys</p>",
                    question_hi: "<p>3. दिए गए कथनों और निष्कर्षों को ध्यानपूर्वक पढ़िए। यह मानते हुए कि कथनों में दी गई जानकारी सत्य है, भले ही वह सामान्य रूप से ज्ञात तथ्यों से भिन्न प्रतीत होती हो, तय करें कि दिए गए निष्कर्षों में से कौन सा कथनों का तार्किक रूप से अनुसरण करता है।<br><strong>कथन:</strong><br>सभी लड़के शेफ हैं।<br>सभी रसोइया ग्रामीण हैं।<br><strong>निष्कर्ष:</strong><br>1. सभी लड़के ग्रामीण हैं।<br>2. सभी शेफ लड़के हैं</p>",
                    options_en: ["<p>Only conclusion 2 follows</p>", "<p>Only conclusion 1 follows</p>", 
                                "<p>Neither conclusion 1 nor 2 follows</p>", "<p>Both the conclusions follow</p>"],
                    options_hi: ["<p>केवल निष्कर्ष 2 अनुसरण करता है</p>", "<p>केवल निष्कर्ष 1 अनुसरण करता है</p>",
                                "<p>न तो निष्कर्ष 1 और न ही 2 अनुसरण करता है</p>", "<p>दोनों निष्कर्ष अनुसरण करते हैं</p>"],
                    solution_en: "<p>3.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729934020160.png\" alt=\"rId5\" width=\"213\" height=\"91\"></p>",
                    solution_hi: "<p>3.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729934020160.png\" alt=\"rId5\" width=\"213\" height=\"91\"></p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "4",
                    section: "6",
                    question_en: "4. Consider 4th March to be a Wednesday in 2018. What day of the week will it be on 7th March in 2019? ",
                    question_hi: "4. 2018 में 4 मार्च को बुधवार मानें। 2019 में 7 मार्च को सप्ताह का कौन सा दिन होगा?",
                    options_en: [" Wednesday", " Tuesday", 
                                " Monday", " Sunday"],
                    options_hi: [" बुधवार", " मंगलवार",
                                " सोमवार", " रविवार"],
                    solution_en: "4.(d) Total days from 4th march 2018 to 7 march 2019 = 365 + 3 = 368 days <br />When we divide 369 by 7 we get 4 as remainder <br />Wednesday + 4 day = Sunday",
                    solution_hi: "4.(d) 4 मार्च 2018 से 7 मार्च 2019 तक कुल दिन = 365+ 3 = 368 दिन<br />जब हम 369 को 7 से भाग देते हैं तो हमें 4 शेषफल मिलता है<br />बुधवार + 4 दिन = रविवार",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "5",
                    section: "6",
                    question_en: "<p>5. Consider the given statement and decide which of the assumptions is/are implicit in the statement.<br><strong>Statement:</strong><br>RTE has mandated compulsory enrollment of all children of the age 6 to 14 years in the schools.<br><strong>Assumptions:</strong><br>I. Enrollment needs to be made compulsory as children don&rsquo;t like education.<br>II. Each child of the country will get quality Education.</p>",
                    question_hi: "<p>5. दिए गए कथन पर विचार करें और तय करें कि कौन सी धारणा कथन में निहित है/हैं।<br><strong>कथन:</strong><br>RTE ने स्कूलों में 6 से 14 वर्ष की आयु के सभी बच्चों का अनिवार्य नामांकन अनिवार्य कर दिया है।<br><strong>धारणाएं:</strong><br>I. नामांकन अनिवार्य किया जाना चाहिए क्योंकि बच्चों को शिक्षा पसंद नहीं है।<br>II. देश के हर बच्चे को गुणवत्तापूर्ण शिक्षा मिलेगी।</p>",
                    options_en: ["<p>Either assumption I or assumption II is implicit.</p>", "<p>Only assumption II is implicit.</p>", 
                                "<p>Neither assumption I nor assumption II is implicit.</p>", "<p>Only assumption I is implicit.</p>"],
                    options_hi: ["<p>या तो धारणा I या धारणा II निहित है।</p>", "<p>केवल धारणा II निहित है।</p>",
                                "<p>न तो धारणा I और न ही धारणा II निहित है।</p>", "<p>केवल धारणा I निहित है।</p>"],
                    solution_en: "<p>5.(c) Neither assumption I nor assumption II is implicit.<br>Both assumptions are different from statements .</p>",
                    solution_hi: "<p>5.(c) न तो धारणा I और न ही धारणा II निहित है।<br>दोनों धारणाएँ कथनों से भिन्न हैं।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "6",
                    section: "6",
                    question_en: "6. Pick the odd one out.<br />Cow, Goat, Rabbit, Cat",
                    question_hi: "6. विषम को चुनें।<br />गाय, बकरी, खरगोश, बिल्ली",
                    options_en: [" Goat", " Cow", 
                                " Cat", " Rabbit"],
                    options_hi: [" बकरा", " गाय",
                                " कैट", " खरगोश"],
                    solution_en: "6.(c) Cats are different from others .",
                    solution_hi: "6.(c) बिल्लियाँ दूसरों से अलग होती हैं।",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "7",
                    section: "6",
                    question_en: "7. In a certain code language , SCHOOL is coded as the number 72, What number will Flower be coded as in that language",
                    question_hi: "7. एक निश्चित कूट भाषा में, SCHOOL को संख्या 72 के रूप में कोडित किया जाता है, उस भाषा में फूल को किस संख्या में कोडित किया जाएगा?",
                    options_en: [" 54", " 71", 
                                " 79", " 89"],
                    options_hi: [" 54", " 71",
                                " 79", " 89"],
                    solution_en: "7.(c) Addition of alphabetic place value .<br />S + C + H + O + O + L = 72<br />Similarly FLOWER = 79  ",
                    solution_hi: "7.(c) वर्णमाला के स्थानीय मान का जोड़।<br />S + C + H + O + O + L = 72<br />इसी प्रकार FLOWER = 79",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "8",
                    section: "6",
                    question_en: "8. Select the option that is related to the third term in the same way as the second term is related to the first term.<br />Music : Concert : : Acting:?",
                    question_hi: "8. उस विकल्प का चयन करें जो तीसरे पद से उसी प्रकार संबंधित है जैसे दूसरा पद पहले पद से संबंधित है।<br />संगीत : संगीत कार्यक्रम : : अभिनय :?",
                    options_en: [" Song", " Theater", 
                                " Dance", " Gymnastic"],
                    options_hi: [" गीत", " रंगमंच",
                                " नृत्य", " जिमनास्टिक"],
                    solution_en: "8.(b) Concert is a performance of music <br />Similarly, In Theatre  we perform acts .",
                    solution_hi: "8.(b) कॉन्सर्ट संगीत का एक प्रदर्शन है<br />इसी तरह, रंगमंच में हम अभिनय करते हैं।",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "9",
                    section: "6",
                    question_en: "<p>9. What will be the correct sequence of the following words if they are arranged as per their order in an English dictionary?<br>Process, Possess, Purpose, Propose</p>",
                    question_hi: "<p>9. निम्नलिखित शब्दों का सही क्रम क्या होगा यदि उन्हें अंग्रेजी शब्दकोश में उनके क्रम के अनुसार व्यवस्थित किया जाए?<br>Process, Possess, Purpose, Propose</p>",
                    options_en: ["<p>Possess, Purpose, propose, Process</p>", "<p>Possess, Process, Purpose, Propose</p>", 
                                "<p>Possess, Process Propose, Purpose</p>", "<p>Possess, Propose, Process, Purpose</p>"],
                    options_hi: ["<p>Possess, Purpose, propose, Process</p>", "<p>Possess, Process, Purpose, Propose</p>",
                                "<p>&nbsp;Possess, Process Propose, Purpose</p>", "<p>Possess, Propose, Process, Purpose</p>"],
                    solution_en: "<p>9.(c) Arranging order = Possess, Process, Propose, Purpose</p>",
                    solution_hi: "<p>9.(c)<br>व्यवस्थित क्रम = Possess , Process, Propose, Purpose</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "10",
                    section: "6",
                    question_en: "10. Select the number that will come next in the following series.<br />4,27,16,125,?",
                    question_hi: "<p>10. निम्नलिखित श्रृंखला में आगे आने वाली संख्या का चयन करें।<br>4,27,16,125,?</p>",
                    options_en: ["<p>18</p>", "<p>216</p>", 
                                "<p>36</p>", "<p>24</p>"],
                    options_hi: ["<p>18</p>", "<p>216</p>",
                                "<p>36</p>", "<p>24</p>"],
                    solution_en: "<p>10.(c) <br><strong>Logic :-</strong> <br><math display=\"block\"><msup><mrow><mn>2</mn></mrow><mrow><mn>2</mn></mrow></msup><mo>=</mo><mi>&#160;</mi><mn>4</mn><mi>&#160;</mi></math><br><math display=\"block\"><msup><mrow><mn>3</mn></mrow><mrow><mn>3</mn></mrow></msup><mo>=</mo><mi>&#160;</mi><mn>27</mn></math><br><math display=\"block\"><msup><mrow><mn>4</mn></mrow><mrow><mn>2</mn></mrow></msup><mo>=</mo><mi>&#160;</mi><mn>16</mn><mi>&#160;</mi></math><br><math display=\"block\"><msup><mrow><mn>5</mn></mrow><mrow><mn>3</mn></mrow></msup><mo>=</mo><mi>&#160;</mi><mn>125</mn></math><br><math display=\"block\"><msup><mrow><mn>6</mn></mrow><mrow><mn>2</mn></mrow></msup><mo>=</mo><mi>&#160;</mi><mn>36</mn><mi>&#160;</mi></math></p>",
                    solution_hi: "<p>10.(c) <br><strong>तर्क :-</strong> <br><math display=\"block\"><msup><mrow><mn>2</mn></mrow><mrow><mn>2</mn></mrow></msup><mo>=</mo><mi>&#160;</mi><mn>4</mn><mi>&#160;</mi></math><br><math display=\"block\"><msup><mrow><mn>3</mn></mrow><mrow><mn>3</mn></mrow></msup><mo>=</mo><mi>&#160;</mi><mn>27</mn></math><br><math display=\"block\"><msup><mrow><mn>4</mn></mrow><mrow><mn>2</mn></mrow></msup><mo>=</mo><mi>&#160;</mi><mn>16</mn><mi>&#160;</mi></math><br><math display=\"block\"><msup><mrow><mn>5</mn></mrow><mrow><mn>3</mn></mrow></msup><mo>=</mo><mi>&#160;</mi><mn>125</mn></math><br><math display=\"block\"><msup><mrow><mn>6</mn></mrow><mrow><mn>2</mn></mrow></msup><mo>=</mo><mi>&#160;</mi><mn>36</mn><mi>&#160;</mi></math></p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "11",
                    section: "6",
                    question_en: "<p>11. Select the option that will fill in the blank and complete the given series.<br>MU,KS,IQ, GO,__</p>",
                    question_hi: "<p>11. उस विकल्प का चयन करें जो रिक्त स्थान को भरेगा और दी गई श्रृंखला को पूरा करेगा।<br>MU,KS,IQ, GO,__</p>",
                    options_en: ["<p>DM</p>", "<p>IQ</p>", 
                                "<p>EL</p>", "<p>EM</p>"],
                    options_hi: ["<p>DM</p>", "<p>IQ</p>",
                                "<p>EL</p>", "<p>EM</p>"],
                    solution_en: "<p>11.(d) <br><strong>Logic :-</strong>&nbsp; subtract 2 each alphabet.<br>M - 2 = K, K - 2 = I, I - 2 = G, G - 2 = E <br>U - 2 = S, S - 2 = Q, Q - 2 = O, O - 2 = M</p>",
                    solution_hi: "<p>11.(d) <br><strong>तर्क:-</strong> प्रत्येक अक्षर में से 2 को घटाएं।<br>M - 2 = K, K - 2 = I, I - 2 = G, G - 2 = E <br>U - 2 = S, S - 2 = Q, Q - 2 = O, O - 2 = M</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "12",
                    section: "6",
                    question_en: "<p>12. Select the number that will come next in the following series.<br>2,6,12,20,?</p>",
                    question_hi: "<p>12. निम्नलिखित श्रृंखला में आगे आने वाली संख्या का चयन करें।<br>2,6,12,20,?</p>",
                    options_en: ["<p>35</p>", "<p>28</p>", 
                                "<p>30</p>", "<p>25</p>"],
                    options_hi: ["<p>35</p>", "<p>28</p>",
                                "<p>30</p>", "<p>25</p>"],
                    solution_en: "<p>12.(c) <br><strong>Logic :-</strong> <br>2 + (2 &times; 2) = 6 <br>6 + (2 &times; 3) = 12 <br>12 + (2 &times; 4) = 20 <br>20 + (2 &times; 5) = 30</p>",
                    solution_hi: "<p>12.(c) <br><strong>तर्क :-</strong> <br>2 + (2 &times; 2) = 6 <br>6 + (2 &times; 3) = 12 <br>12 + (2 &times; 4) = 20 <br>20 + (2 &times; 5) = 30 </p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "13",
                    section: "6",
                    question_en: "13. Select the option that is related to the third term in the same way as the second term is related to the first term.<br />15 August : Freedom :: 26 January: ?",
                    question_hi: "13. उस विकल्प का चयन करें जो तीसरे पद से उसी प्रकार संबंधित है जैसे दूसरा पद पहले पद से संबंधित है।<br />15 अगस्त : आज़ादी :: 26 जनवरी : ?",
                    options_en: [" Secularism", " Constitution", 
                                " Parliament", " Democracy"],
                    options_hi: [" धर्मनिरपेक्षता", " संविधान",
                                " संसद", " लोकतंत्र"],
                    solution_en: "13.(b) <br />On 15 August, India got freedom similarly on 26 January Constitution of India came into force.<br /> ",
                    solution_hi: "13.(b) <br />15 अगस्त को भारत को आजादी मिली इसी तरह 26 जनवरी को भारत का संविधान लागू हुआ।",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "14",
                    section: "6",
                    question_en: "<p>14. Read the given statements and conclusions carefully. Assuming that the information given in the statements is true, even if it appears to be at variance with commonly known facts, decide which of the given conclusions logically follows from the statements.<br><strong>Statements:</strong><br>All goats are cats.<br>All cats are rats.<br><strong>Conclusions:</strong><br>1. Some rats are cats<br>2. All rats are goat</p>",
                    question_hi: "<p>14. दिए गए कथनों और निष्कर्षों को ध्यानपूर्वक पढ़िए। यह मानते हुए कि कथनों में दी गई जानकारी सत्य है, भले ही वह सामान्य रूप से ज्ञात तथ्यों से भिन्न प्रतीत होती हो, तय करें कि दिए गए निष्कर्षों में से कौन सा कथनों का तार्किक रूप से अनुसरण करता है।<br><strong>कथन:</strong><br>सभी बकरियां बिल्लियां हैं।<br>सभी बिल्लियाँ चूहे हैं।<br><strong>निष्कर्ष:</strong><br>1. कुछ चूहे बिल्लियाँ हैं<br>2. सभी चूहे बकरी हैं</p>",
                    options_en: ["<p>Neither conclusion 1 nor 2 follows</p>", "<p>Both conclusions follow</p>", 
                                "<p>Only conclusion 2 follows</p>", "<p>Only conclusion 1 follows</p>"],
                    options_hi: ["<p>न तो निष्कर्ष 1 और न ही 2 अनुसरण करता है</p>", "<p>दोनों निष्कर्ष अनुसरण करते हैं</p>",
                                "<p>केवल निष्कर्ष 2 अनुसरण करता है</p>", "<p>केवल निष्कर्ष 1 अनुसरण करता है</p>"],
                    solution_en: "<p>14.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729934020394.png\" alt=\"rId6\" width=\"187\" height=\"106\"></p>",
                    solution_hi: "<p>14.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729934020394.png\" alt=\"rId6\" width=\"187\" height=\"106\"></p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "15",
                    section: "6",
                    question_en: "15. If today is a Tuesday, what will be the 64th day from today?",
                    question_hi: "15. यदि आज मंगलवार है तो आज से 64वां दिन क्या होगा?",
                    options_en: [" Tuesday", " Friday", 
                                " Thursday", " Wednesday"],
                    options_hi: [" मंगलवार", " शुक्रवार",
                                " गुरुवार", " बुधवार"],
                    solution_en: "15.(d) Today is Tuesday, then after the 64th day it will be Wednesday. As we have 7 days in a week so 63rd day will be also Tuesday as 63 is the multiple of 7.",
                    solution_hi: "15.(d) आज मंगलवार है तो 64वें दिन के बाद बुधवार होगा.<br />जिस प्रकार हमारे पास सप्ताह में 7 दिन होते हैं, उसी प्रकार 63वां दिन भी मंगलवार होगा क्योंकि 63, 7 का गुणज है।",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "16",
                    section: "6",
                    question_en: "<p>16. Eight friends F1,F2,F3,F4,F5,F6,F7 and F8 are sitting around a circular table facing towards the centre (not necessarily in the same order).F2 is not the neighbour of F1 or F8 . Only F6 is between F2 and F5. F2 is second to the left of F7. F4 is second to the right of F3.<br>Which of the following could be correct position of F1?</p>",
                    question_hi: "<p>16. आठ मित्र F1,F2,F3,F4,F5,F6,F7 और F8 एक वृत्ताकार मेज के चारों ओर केंद्र की ओर मुख करके बैठे हैं (जरूरी नहीं कि इसी क्रम में)। F2 F1 या F8 का पड़ोसी नहीं है। केवल F6, F2 और F5 के बीच में है। F2, F7 के बाएं से दूसरे स्थान पर है। F4, F3 के दायें से दूसरे स्थान पर है।<br>निम्नलिखित में से कौन F1 की सही स्थिति हो सकती है?</p>",
                    options_en: ["<p>Third to the left of F5</p>", "<p>Third to the left of F6</p>", 
                                "<p>Second to the left of F7</p>", "<p>Immediate left of F4</p>"],
                    options_hi: ["<p>F5 के बाईं ओर तीसरा</p>", "<p>F6 के बाईं ओर तीसरा</p>",
                                "<p>F7 के बाईं ओर दूसरा</p>", "<p>F4 . के तत्काल बाएं</p>"],
                    solution_en: "<p>16.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729934020533.png\" alt=\"rId7\" width=\"109\" height=\"89\"></p>",
                    solution_hi: "<p>16.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729934020533.png\" alt=\"rId7\" width=\"109\" height=\"89\"></p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "17",
                    section: "6",
                    question_en: "<p>17. Select the option that is related to the third letter -cluster in the same way as the second letter-cluster is related to the first letter-cluster.<br>ACEG,: PRTV : : SUWY : ?</p>",
                    question_hi: "<p>17. उस विकल्प का चयन करें जो तीसरे अक्षर-समूह से उसी प्रकार संबंधित है जैसे दूसरा अक्षर-समूह पहले अक्षर-समूह से संबंधित है।<br>ACEG,: PRTV : : SUWY : ?</p>",
                    options_en: ["<p>HJLN</p>", "<p>GIKM</p>", 
                                "<p>HKLM</p>", "<p>HFDB</p>"],
                    options_hi: ["<p>HJLN</p>", "<p>GIKM</p>",
                                "<p>HKLM</p>", "<p>HFDB</p>"],
                    solution_en: "<p>17.(a) <strong>Logic :-</strong><br>A + 15 = P<br>C + 15 = R<br>E + 15 = T<br>G + 15 = V<br>Similarly ,SUWY is written as HJLN</p>",
                    solution_hi: "<p>17.(a) <strong>तर्क :-</strong><br>A + 15 = P<br>C + 15 = R<br>E + 15 = T<br>G + 15 = V<br>इसी तरह, SUWY को HJLN के रूप में लिखा जाता है</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "18",
                    section: "6",
                    question_en: "<p>18. A question is given ,followed by two arguments. Decide which of the arguments is/are strong with respect to the question.<br><strong>Question:</strong><br>Should the sale and use of tobacco be completely banned in India?<br><strong>Arguments:</strong><br>I. Yes, Tobacco causes oral cancer and other diseases.<br>II. No, Millions of workers will lose their jobs.</p>",
                    question_hi: "<p>18. एक प्रश्न दिया गया है, जिसके बाद दो तर्क दिए गए हैं। निर्णय लें कि प्रश्न के संबंध में कौन-सा/से तर्क प्रबल है/हैं।<br><strong>प्रश्न:</strong><br>क्या भारत में तंबाकू की बिक्री और उपयोग पर पूरी तरह से प्रतिबंध लगा देना चाहिए?<br><strong>तर्क:</strong><br>I. जी हां, तंबाकू मुंह के कैंसर और अन्य बीमारियों का कारण बनता है।<br>II. नहीं, लाखों कर्मचारी अपनी नौकरी खो देंगे।</p>",
                    options_en: ["<p>Both , (I) and (II) are strong.</p>", "<p>Only argument (II) is strong.</p>", 
                                "<p>Neither (I) nor (II) is strong.</p>", "<p>Only argument (I) is strong.</p>"],
                    options_hi: ["<p>दोनों, (I)और (II) मजबूत हैं।</p>", "<p>केवल तर्क (II)मजबूत है।</p>",
                                "<p>न तो (I) और न ही (II) मजबूत है।</p>", "<p>केवल तर्क (I) मजबूत है।</p>"],
                    solution_en: "<p>18.(a) Both (I) and (II) Arguments are strong.</p>",
                    solution_hi: "<p>18.(a) दोनों (I) और (II) तर्क मजबूत हैं।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "19",
                    section: "6",
                    question_en: "<p>19. Seven boxes S,P,L,Q,R,M and N are placed one above the other (not necessarily in the same order).Only three boxes are placed between M and L. M and L are placed neither at the bottom nor at the top. Only two boxes are placed between L and N. S is immediately below R. There is only one box between S and Q,M is placed above L. How many boxes are placed between P and N?</p>",
                    question_hi: "<p>19. सात बॉक्स S,P,L,Q,R,M और N को एक के ऊपर एक रखा गया है (जरूरी नहीं कि इसी क्रम में)। M और L के बीच केवल तीन बॉक्स रखे गए हैं। M और L को न तो नीचे रखा गया है और न ही शीर्ष पर रखा गया है। L और N के बीच केवल दो बॉक्स रखे गए हैं। S, R के ठीक नीचे है। S और Q के बीच केवल एक बॉक्स है। M, L के ऊपर रखा गया है। P और N के बीच कितने बॉक्स रखे गए हैं?</p>",
                    options_en: ["<p>Three</p>", "<p>One</p>", 
                                "<p>Two</p>", "<p>Four</p>"],
                    options_hi: ["<p>तीन</p>", "<p>एक</p>",
                                "<p>दो</p>", "<p>चार</p>"],
                    solution_en: "<p>19.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729934020672.png\" alt=\"rId8\"><br>One box is placed between P and N .</p>",
                    solution_hi: "<p>19.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729934020672.png\" alt=\"rId8\"><br>P और N के बीच एक बॉक्स रखा गया है।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "20",
                    section: "6",
                    question_en: "<p>20. Pointing to a photograph, a woman said to a man,&rdquo;She is the only child of the sister of the only son of your paternal grandfather&rdquo;. How is the man related to the person in the picture?</p>",
                    question_hi: "<p>20. एक तस्वीर की ओर इशारा करते हुए, एक महिला ने एक पुरुष से कहा, \"वह आपके दादा के इकलौते बेटे की बहन की इकलौती संतान है\"। वह व्यक्ति चित्र में दिख रहे व्यक्ति से किस प्रकार संबंधित है?</p>",
                    options_en: ["<p>Sister</p>", "<p>Brother</p>", 
                                "<p>Father&rsquo;s Sister&rsquo;s Son</p>", "<p>Mother&rsquo;s Brother&rsquo;s Son</p>"],
                    options_hi: ["<p>बहन</p>", "<p>भाई</p>",
                                "<p>पिता की बहन का बेटा</p>", "<p>मां के भाई का बेटा</p>"],
                    solution_en: "<p>20.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729934020878.png\" alt=\"rId9\" width=\"120\" height=\"140\"></p>",
                    solution_hi: "<p>20.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729934020878.png\" alt=\"rId9\" width=\"120\" height=\"140\"></p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "21",
                    section: "6",
                    question_en: "<p>21. In a code language, CHILD is written as 412983. How would you write the word ADULT in the same code language?</p>",
                    question_hi: "<p>21. एक कोड भाषा में, CHILD को 412983 लिखा जाता है। आप उसी कोड भाषा में ADULT शब्द को कैसे लिखेंगे?</p>",
                    options_en: ["<p>142112</p>", "<p>14211220</p>", 
                                "<p>124983</p>", "<p>20122141</p>"],
                    options_hi: ["<p>142112</p>", "<p>14211220</p>",
                                "<p>124983</p>", "<p>20122141</p>"],
                    solution_en: "<p>21.(d) <strong>logic:-</strong><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729934021055.png\" alt=\"rId10\" width=\"142\" height=\"86\"><br>Similarly ADULT is written as 20122141</p>",
                    solution_hi: "<p>21.(d) <strong>तर्क:-</strong><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729934021055.png\" alt=\"rId10\" width=\"142\" height=\"86\"><br>इसी प्रकार ADULT को 20122141 लिखा जाता है</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "22",
                    section: "6",
                    question_en: "22. Pick the odd one out.<br />Kathmandu, Thimphu, Dhaka, Karachi",
                    question_hi: "22.विषम को चुनें।<br />काठमांडू, थिम्पू, ढाका, कराची",
                    options_en: [" Kathmandu", " Thimphu", 
                                " Karachi", " Dhaka"],
                    options_hi: [" काठमांडू", " थिम्फू",
                                " कराची", " ढाका"],
                    solution_en: "22.(c) Except Karachi, all are  capital of any country.",
                    solution_hi: "22.(c) कराची को छोड़कर, सभी किसी भी देश की राजधानी हैं।",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "23",
                    section: "6",
                    question_en: "<p>23. Select the number from the given options that will replace the question mark in the given series.<br>2, 4, 3, 3, 9, 6, 4, 16, ?, 5, 25, 15</p>",
                    question_hi: "<p>23. दिए गए विकल्पों में से उस संख्या का चयन करें जो दी गई श्रृंखला में प्रश्नवाचक चिह्न का स्थान लेगी।<br>2, 4, 3, 3, 9, 6, 4, 16, ?, 5, 25, 15</p>",
                    options_en: ["<p>20</p>", "<p>10</p>", 
                                "<p>15</p>", "<p>18</p>"],
                    options_hi: ["<p>20</p>", "<p>10</p>",
                                "<p>15</p>", "<p>18</p>"],
                    solution_en: "<p>23.(b) 2,4,3 / 3,9,6 / 4,16,? / 5,25,15<br>Firstly arrange in 4 part with 3 numbers <br>In this part = 2 &times; 2 (1st digit ) = 4(2nd digit ) , <br>3rd digit = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#160;</mi><mn>1</mn><mi>s</mi><mi>t</mi><mi>&#160;</mi><mi>d</mi><mi>i</mi><mi>g</mi><mi>i</mi><mi>t</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>2</mn><mi>n</mi><mi>d</mi><mi>&#160;</mi><mi>d</mi><mi>i</mi><mi>g</mi><mi>i</mi><mi>t</mi><mi>&#160;</mi></mrow><mn>2</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><mo>+</mo><mn>4</mn></mrow><mn>2</mn></mfrac></math>&nbsp; = 3 <br>Similarly, 4 &times; 4 = 16, 3rd digit = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#160;</mi><mi>&#160;</mi><mn>4</mn><mi>&#160;</mi><mo>+</mo><mo>&#160;</mo><mn>16</mn><mi>&#160;</mi><mi>&#160;</mi></mrow><mn>2</mn></mfrac></math> = 10</p>",
                    solution_hi: "<p>23.(b) 2,4,3 / 3,9,6 / 4,16,? / 5,25,15<br>सबसे पहले 3 नंबरों के साथ 4 भाग में व्यवस्थित करें<br>इस भाग में = 2 &times; 2 (पहला अंक) = 4 (दूसरा अंक) है,<br>तीसरा अंक = <math style=\"font-family:Verdana\" display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#160;</mi><mi>&#2346;</mi><mi>&#2361;</mi><mi>&#2354;</mi><mi>&#2366;</mi><mi>&#160;</mi><mi>&#2309;</mi><mi>&#2306;</mi><mi>&#2325;</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>&#2342;</mi><mi>&#2370;</mi><mi>&#2360;</mi><mi>&#2352;</mi><mi>&#2366;</mi><mi>&#160;</mi><mi>&#2309;</mi><mi>&#2306;</mi><mi>&#2325;</mi><mi>&#160;</mi></mrow><mn>2</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><mo>+</mo><mn>4</mn></mrow><mn>2</mn></mfrac></math>&nbsp; = 3 <br>इसी प्रकार, 4 &times; 4 = 16, तीसरा अंक = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#160;</mi><mi>&#160;</mi><mn>4</mn><mi>&#160;</mi><mo>+</mo><mo>&#160;</mo><mn>16</mn><mi>&#160;</mi><mi>&#160;</mi></mrow><mn>2</mn></mfrac></math>&nbsp;= 10</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "24",
                    section: "6",
                    question_en: "<p>24. Read the given information and answer the question that follows.<br>C is the sister of F. A is the father of B, husband of E and only son of D. D is the paternal grandfather of F and A does not have any sisters. How many children does A have?</p>",
                    question_hi: "<p>24. दी गई जानकारी को पढ़िए और नीचे दिए गए प्रश्न का उत्तर दीजिए।<br>C, F की बहन है। A, B का पिता है, E का पति है और D का इकलौता पुत्र है। D, F का दादा है और A की कोई बहन नहीं है। A के कितने बच्चे हैं?</p>",
                    options_en: ["<p>4</p>", "<p>3</p>", 
                                "<p>2</p>", "<p>1</p>"],
                    options_hi: ["<p>4</p>", "<p>3</p>",
                                "<p>2</p>", "<p>1</p>"],
                    solution_en: "<p>24.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729934021162.png\" alt=\"rId11\" width=\"135\" height=\"113\"><br>&ldquo;A&rdquo; has 3 children.</p>",
                    solution_hi: "<p>24.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729934021162.png\" alt=\"rId11\" width=\"135\" height=\"113\"><br>\"A\" के 3 बच्चे हैं।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "25",
                    section: "6",
                    question_en: "25. Pick the odd one out.<br />3-6, 4-8, 6-18, 8-32, 10-50",
                    question_hi: "25. विषम को चुनें।<br />3-6, 4-8, 6-18, 8-32, 10-50",
                    options_en: [" 8-32", " 6-18", 
                                " 4-8", " 3-6"],
                    options_hi: [" 8-32", " 6-18",
                                " 4-8", " 3-6"],
                    solution_en: "25.(d) 3-6 is different from others because <br />4 <math display=\"inline\"><mo>×</mo></math> 2 = 8<br />(4 + 2) <math display=\"inline\"><mo>×</mo><mn>3</mn></math> = 6 × 3 = 18<br />(6 + 2) <math display=\"inline\"><mo>×</mo></math> 4 = 8 × 4 = 32<br />(8 + 2) <math display=\"inline\"><mo>×</mo></math> 5 = 10 × 5 = 50",
                    solution_hi: "25.(d) 3-6 दूसरों से अलग है क्योंकि<br />4 <math display=\"inline\"><mo>×</mo></math> 2 = 8<br />(4 + 2) <math display=\"inline\"><mo>×</mo><mn>3</mn></math> = 6 × 3 = 18<br />(6 + 2) <math display=\"inline\"><mo>×</mo></math> 4 = 8 × 4 = 32<br />(8 + 2) <math display=\"inline\"><mo>×</mo></math> 5 = 10 × 5 = 50",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "26",
                    section: "6",
                    question_en: "<p>26. If 24 B 6 B 12 = 42 and 31 B 7 B 13 = 51, then 64 B 9 B 4 = ?</p>",
                    question_hi: "<p>26. यदि 24 B 6 B 12 = 42 और 31 B 7 B 13 = 51 तो 64 B 9 B 4 = ?</p>",
                    options_en: ["<p>78</p>", "<p>77</p>", 
                                "<p>76</p>", "<p>79</p>"],
                    options_hi: ["<p>78</p>", "<p>77</p>",
                                "<p>76</p>", "<p>79</p>"],
                    solution_en: "<p>26.(b) Where, B = + <br>24 + 6 + 12 = 42<br>42 = 42 ( LHS = RHS )<br>So, 64 + 9 + 4 = 77</p>",
                    solution_hi: "<p>26.(b) जहाँ, B = + <br>24 + 6 + 12 = 42<br>42 = 42 ( LHS = RHS )<br>इसलिए, 64 + 9 + 4 = 77</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "27",
                    section: "6",
                    question_en: "27. Select the option that is related to the third term in the same way as the second term is related to the first term.<br />Bed : Pillow : : Sofa : ?",
                    question_hi: "27. उस विकल्प का चयन करें जो तीसरे पद से उसी प्रकार संबंधित है जैसे दूसरा पद पहले पद से संबंधित है।<br />बिस्तर : तकिया : : सोफ़ा : ?",
                    options_en: [" Covers", " Cushions", 
                                " Sheets", " Upholstery"],
                    options_hi: [" कवर", " कुशन",
                                " शीट्स", " उपहोल्स्टरी "],
                    solution_en: "27.(b) Bed is related to pillow similarly sofa is related Cushions.",
                    solution_hi: "27.(b) बिस्तर तकिए से संबंधित है उसी तरह सोफा संबंधित कुशन से संबंधित है।",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "28",
                    section: "6",
                    question_en: "<p>28. Read the given information carefully and answer the question that follows.<br>Five children P,Q,R,S and T, are wearing different coloured clothes,red, yellow, pink, green and purple, but not necessarily in the same order. Each one likes only one fruit out of mangoes, bananas, grapes, apples, and pears, R is in red clothes. Q does not like bananas and grapes. S likes apples and he is in purple clothes. The child in pink coloured clothes like bananas. T likes mangoes, but is not in green or pink clothes. R likes grapes. Who likes pears?</p>",
                    question_hi: "<p>28. दी गई जानकारी को ध्यान से पढ़ें और नीचे दिए गए प्रश्न का उत्तर दें।<br>पांच बच्चे P, Q, R, S और T, अलग-अलग रंग के कपड़े लाल, पीले, गुलाबी, हरे और बैंगनी पहने हुए हैं, लेकिन जरूरी नहीं कि इसी क्रम में हों। प्रत्येक को आम, केला, अंगूर, सेब और नाशपाती में से केवल एक फल पसंद है, R लाल कपड़ों में है। Q को केला और अंगूर पसंद नहीं है। S को सेब पसंद है और वह बैंगनी रंग के कपड़ों में है। गुलाबी रंग के कपड़ों में बच्चे को केला पसंद है। T को आम पसंद है, लेकिन वह हरे या गुलाबी रंग के कपड़ों में नहीं है। R अंगूर पसंद करता है। नाशपाती किसे पसंद है?</p>",
                    options_en: ["<p>S</p>", "<p>Q</p>", 
                                "<p>P</p>", "<p>R</p>"],
                    options_hi: ["<p>S</p>", "<p>Q</p>",
                                "<p>P</p>", "<p>R</p>"],
                    solution_en: "<p>28.(b) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729934021393.png\" alt=\"rId12\" width=\"225\" height=\"152\"></p>",
                    solution_hi: "<p>28.(b) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729934021514.png\" alt=\"rId13\" width=\"241\" height=\"167\"></p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "29",
                    section: "6",
                    question_en: "<p>29. In a certain code language, &lsquo;QZDH&rsquo; is written as &lsquo;51&rsquo;, &lsquo;PLMQ&rsquo; is written as &lsquo;54&rsquo;. What is the code for &lsquo;DNRB&rsquo; in that code language?</p>",
                    question_hi: "<p>29. एक निश्चित कोड भाषा में, \'QZDH\' को \'51\' लिखा जाता है, \'PLMQ\' को \'54\' लिखा जाता है। उस कूट भाषा में &lsquo;DNRB&rsquo; के लिए कूट क्या है?</p>",
                    options_en: ["<p>33</p>", "<p>37</p>", 
                                "<p>36</p>", "<p>34</p>"],
                    options_hi: ["<p>33</p>", "<p>37</p>",
                                "<p>36</p>", "<p>34</p>"],
                    solution_en: "<p>29.(d) <strong>Logic :-</strong> (Sum of the algebraic place value ) - 4 <br>Q + Z + D + H = 17 + 26 + 4 + 8 = 55 - 4 = 51 <br>D + N + R + B = 4 + 14 + 18 + 2 = 38 - 4 = 34</p>",
                    solution_hi: "<p>29.(d) <strong>तर्क :-</strong> (बीजीय स्थानीय मान का योग) - 4<br>Q + Z + D + H = 17 + 26 + 4 + 8 = 55 - 4 = 51 <br>D + N + R + B = 4 + 14 + 18 + 2 = 38 - 4 = 34</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "30",
                    section: "misc",
                    question_en: "<p>30. Select the option that is related to the third letter cluster in the same way as the second number is related to the first letter cluster.<br>ACE : 1925 :: BDF : ?</p>",
                    question_hi: "<p>30. उस विकल्प का चयन करें जो तीसरे अक्षर क्लस्टर से उसी तरह संबंधित है जैसे दूसरा नंबर पहले अक्षर क्लस्टर से संबंधित है।<br>ACE : 1925 :: BDF : ?</p>",
                    options_en: ["<p>24661</p>", "<p>42536</p>", 
                                "<p>41636</p>", "<p>41635</p>"],
                    options_hi: ["<p>24661</p>", "<p>42536</p>",
                                "<p>41636</p>", "<p>41635</p>"],
                    solution_en: "<p>30.(c) Square of the place value <br>A = <math display=\"inline\"><msup><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></msup></math>= 1 <br>C =<math display=\"inline\"><mi>&#160;</mi><msup><mrow><mn>3</mn></mrow><mrow><mn>2</mn></mrow></msup></math>= 9 <br>E = <math display=\"inline\"><msup><mrow><mn>5</mn></mrow><mrow><mn>2</mn></mrow></msup></math>= 25</p>",
                    solution_hi: "<p>30.(c) स्थानीय मान का वर्ग<br>A = <math display=\"inline\"><msup><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></msup></math>= 1 <br>C =<math display=\"inline\"><mi>&#160;</mi><msup><mrow><mn>3</mn></mrow><mrow><mn>2</mn></mrow></msup></math>= 9 <br>E = <math display=\"inline\"><msup><mrow><mn>5</mn></mrow><mrow><mn>2</mn></mrow></msup></math>= 25</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>