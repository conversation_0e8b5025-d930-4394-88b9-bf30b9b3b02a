<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #7209b7;
            --secondary: #4361ee;
            --accent: #f72585;
            --success: #06d6a0;
            --warning: #ffd166;
            --danger: #ef476f;
            --light: #f8f9fa;
            --dark: #212529;
        }
        body {
            background-color: #f0f2f5;
            font-size: 16px;
            padding-bottom: 60px; /* Space for fixed navigation */
        }
        .navbar {
            background: linear-gradient(135deg, var(--primary), var(--secondary)) !important;
            box-shadow: 0 2px 10px rgba(0,0,0,.1);
            padding: 0.5rem 1rem;
        }
        .navbar-brand {
            font-size: 1.1rem;
            margin-right: 0;
        }
        .navbar .container-fluid {
            justify-content: space-between;
        }
        .controls-group {
            display: flex;
            align-items: center;
            flex-wrap: nowrap;
        }
        .timer-display {
            background: linear-gradient(135deg, #4361ee, #3a0ca3);
            color: white;
            border-radius: 50px;
            box-shadow: 0 3px 8px rgba(0,0,0,.1);
            padding: 0.25rem 0.75rem;
            font-size: 0.875rem;
            white-space: nowrap;
        }
        .nav-btn {
            padding: 0.25rem 0.75rem;
            font-size: 0.875rem;
            margin-left: 0.25rem;
        }
        .question-nav {
            position: fixed;
            top: 56px;
            right: 0;
            height: calc(100vh - 56px);
            width: 280px;
            transform: translateX(280px);
            transition: transform .3s;
            z-index: 100;
            background: #fff;
            box-shadow: -5px 0 15px rgba(0,0,0,.1);
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .main-content {
            transition: margin .3s;
            min-height: calc(100vh - 116px);
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.5;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(32px, 1fr));
            gap: 6px;
        }
        .q-box {
            width: 35px;
            height: 35px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 5px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid #dee2e6;
            background: #fff;
            transition: all .2s;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: 0 3px 5px rgba(0,0,0,.1);
        }
        .q-box.attempted {
            background-color: var(--secondary);
            color: white;
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,.15);
        }
        .card {
            border-radius: 12px;
            overflow: hidden;
            border: none;
            box-shadow: 0 4px 12px rgba(0,0,0,.08);
        }
        .option-label {
            border-radius: 8px;
            transition: all .2s;
            border: 1px solid #dee2e6;
            padding: 0.75rem 1rem;
            font-size: 1rem;
        }
        .option-label:hover {
            background-color: #f8f9fa;
            transform: translateY(-2px);
            box-shadow: 0 3px 8px rgba(0,0,0,.05);
        }
        .form-check-input:checked + .option-label {
            border-color: var(--primary);
            background-color: rgba(114,9,183,.05);
        }
        .btn-primary {
            background-color: var(--primary);
            border-color: var(--primary);
        }
        .btn-outline-primary {
            color: var(--primary);
            border-color: var(--primary);
        }
        .btn-outline-primary:hover {
            background-color: var(--primary);
            color: white;
        }
        /* Bottom fixed navigation for mobile */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: white;
            box-shadow: 0 -2px 10px rgba(0,0,0,.1);
            padding: 10px 15px;
            display: flex;
            justify-content: space-between;
            z-index: 90;
        }
        @media (min-width: 992px) {
            .main-content.with-nav {
                margin-right: 280px;
            }
            .question-nav {
                transform: translateX(0);
            }
            .nav-toggle-btn {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(30px, 1fr));
            }
            .option-label {
                padding: 0.625rem 0.875rem;
                font-size: 0.95rem;
            }
            .question-text {
                font-size: 1rem;
            }
            .navbar-brand {
                font-size: 1rem;
            }
            .timer-display {
                font-size: 0.8125rem;
                padding: 0.2rem 0.6rem;
            }
            .nav-btn {
                padding: 0.2rem 0.6rem;
                font-size: 0.8125rem;
            }
            .container {
                padding-left: 10px;
                padding-right: 10px;
            }
        }
    </style>
</head>
<body>
    <!-- Header navbar -->
    <nav class="navbar navbar-dark sticky-top">
        <div class="container-fluid px-2">
            <a class="navbar-brand fw-bold" href="#"><i class="fas fa-graduation-cap me-1"></i></a>
            <div class="controls-group">
                <!-- Timer -->
                <div class="timer-display me-1 fw-bold" id="timer">
                    <i class="far fa-clock me-1"></i><span id="timer-display">90:00</span>
                </div>
                <!-- Submit button -->
                <button class="btn btn-light btn-sm nav-btn me-1" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Language toggle -->
                <button class="btn btn-light btn-sm nav-btn me-1" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="btn btn-light btn-sm nav-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </nav>
    <!-- Main content -->
    <div class="main-content" id="main-content">
        <div class="container py-3">
            <!-- Question counter -->
            <div class="fw-bold text-center mb-3" id="question-counter">Question 1 of 25</div>
            <!-- Questions will be displayed here -->
            <div id="questions-container"></div>
        </div>
    </div>
    <!-- Bottom navigation for mobile -->
    <div class="bottom-nav d-lg-none">
        <button class="btn btn-outline-primary px-3" onclick="prevQuestion()">
            <i class="fas fa-chevron-left me-1"></i> Previous
        </button>
        <button class="btn btn-outline-primary px-3" onclick="nextQuestion()">
            Next <i class="fas fa-chevron-right ms-1"></i>
        </button>
    </div>
    <!-- Question navigation sidebar -->
    <div class="question-nav" id="question-nav">
        <div class="p-3">
            <div class="d-flex justify-content-between align-items-center mb-3">
                <h5 class="m-0">Questions</h5>
                <div>
                    <span class="badge bg-secondary me-1" id="attempted-count">0</span>
                    <span class="badge bg-secondary" id="total-count">25</span>
                    <button class="btn btn-sm btn-outline-secondary ms-2 d-lg-none" onclick="toggleNav()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
            <!-- Section selector moved here -->
            <div class="mb-3">
                <label for="section-selector" class="form-label small text-muted">Jump to Section</label>
                <select class="form-select form-select-sm" id="section-selector" onchange="switchSection(this.value)">
                    <option value="all">All Sections</option>
                </select>
            </div>
            <div class="question-grid" id="question-boxes"></div>
        </div>
    </div>
    <!-- Results modal -->
    <div class="modal fade" id="results-modal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content border-0">
                <div class="modal-header border-0" style="background:linear-gradient(135deg,var(--primary),var(--secondary));color:white">
                    <h5 class="modal-title">Test Results</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body p-4">
                    <div class="row text-center g-2">
                        <div class="col-6">
                            <div class="p-3 rounded shadow-sm" style="background:linear-gradient(135deg,#4361ee,#3a0ca3);color:white">
                                <h3 id="score-value" class="mb-0 fw-bold">0</h3>
                                <div class="small">Score</div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="p-3 rounded shadow-sm" style="background:linear-gradient(135deg,#06d6a0,#1b9aaa);color:white">
                                <h3 id="correct-value" class="mb-0 fw-bold">0</h3>
                                <div class="small">Correct</div>
                            </div>
                        </div>
                        <div class="col-6 mt-2">
                            <div class="p-3 rounded shadow-sm" style="background:linear-gradient(135deg,#ef476f,#f72585);color:white">
                                <h3 id="incorrect-value" class="mb-0 fw-bold">0</h3>
                                <div class="small">Incorrect</div>
                            </div>
                        </div>
                        <div class="col-6 mt-2">
                            <div class="p-3 rounded shadow-sm" style="background:linear-gradient(135deg,#6c757d,#495057);color:white">
                                <h3 id="unattempted-value" class="mb-0 fw-bold">0</h3>
                                <div class="small">Unattempted</div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer border-0">
                    <button type="button" class="btn btn-primary" onclick="reviewTest()">Review Test</button>
                </div>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 90 * 60;
        let resultsModal;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            resultsModal = new bootstrap.Modal(document.getElementById('results-modal'));
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["6"] = {
                name: "Reasoning",
                start: 0,
                end: 23
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="6">Reasoning</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 24,
                end: 24
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "6",
                    question_en: "<p>1.<span style=\"font-family: Times New Roman;\"> Select the option figure which contains figure X embedded in it as its part. (rotation is NOT allowed).</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646808302/word/media/image24.png\" /></p>",
                    question_hi: " <p>1.</span><span style=\"font-family:Baloo\"> उस विकल्प आकृति का चयन करें जिसमें उसके भाग के रूप में आकृति X अंतर्निहित है। (रोटेशन की अनुमति नहीं है)।</span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646808302/word/media/image24.png\"/></p>",
                    options_en: ["<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646808302/word/media/image1.png\" /></p>", "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646808302/word/media/image28.png\" /></p>", 
                                "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646808302/word/media/image2.png\" /></p>", "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646808302/word/media/image18.png\" /></p>"],
                    options_hi: [" <p> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646808302/word/media/image1.png\"/></p>", " <p> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646808302/word/media/image28.png\"/></p>",
                                " <p> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646808302/word/media/image2.png\"/></p>", " <p> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646808302/word/media/image18.png\"/></p>"],
                    solution_en: "<p>1.(c)</p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646808302/word/media/image6.png\" width=\"122\" height=\"122\" /></p>",
                    solution_hi: " <p>1.(c)</span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646808302/word/media/image6.png\"/></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "6",
                    question_en: " <p>2.</span><span style=\"font-family:Times New Roman\"> Two statements are given, followed by three conclusions numbered I, II and III. Assuming the statements to be true, even if they seem to be at variance with commonly known facts, decide which of the conclusions logically follow(s) from the statements. </span></p> <p><span style=\"font-family:Times New Roman\">Statements: </span></p> <p><span style=\"font-family:Times New Roman\">All bottles are utensils. </span></p> <p><span style=\"font-family:Times New Roman\">All kitchens are utensils. </span></p> <p><span style=\"font-family:Times New Roman\">Conclusions: </span></p> <p><span style=\"font-family:Times New Roman\">I. No bottle is a kitchen. </span></p> <p><span style=\"font-family:Times New Roman\">II. Some bottles are kitchens. </span></p> <p><span style=\"font-family:Times New Roman\">III. No utensil is a bottle. </span></p>",
                    question_hi: " <p>2.</span><span style=\"font-family:Baloo\"> दो कथन दिए गए हैं, जिसके बाद तीन निष्कर्ष I, II और III दिए गए हैं। कथनों को सत्य मानते हुए, भले ही वे सामान्य रूप से ज्ञात तथ्यों से भिन्न प्रतीत होते हों, निर्णय लें कि कौन सा निष्कर्ष कथनों का तार्किक रूप से अनुसरण करता है।</span></p> <p><span style=\"font-family:Baloo\">कथन:</span></p> <p><span style=\"font-family:Baloo\">सभी बोतल बर्तन हैं।</span></p> <p><span style=\"font-family:Baloo\">सभी किचन बर्तन हैं।</span></p> <p><span style=\"font-family:Baloo\">निष्कर्ष:</span></p> <p><span style=\"font-family:Baloo\">I. कोई बोतल किचन नहीं है।</span></p> <p><span style=\"font-family:Baloo\">II. कुछ बोतलें किचन हैं।</span></p> <p><span style=\"font-family:Baloo\">III. कोई बर्तन बोतल नहीं है।</span></p>",
                    options_en: [" <p> Either conclusion I or II follows </span></p>", " <p> Only conclusions I and II follow </span></p>", 
                                " <p> All the conclusions follow </span></p>", " <p> Only conclusions II and III follow </span></p>"],
                    options_hi: [" <p> या तो निष्कर्ष I या II अनुसरण करता है। </span></p>", " <p> केवल निष्कर्ष I और II अनुसरण करते हैं। </span></p>",
                                " <p> सभी निष्कर्ष अनुसरण करते हैं। </span></p>", " <p> केवल निष्कर्ष II और III अनुसरण करते हैं। </span></p>"],
                    solution_en: " <p>2.(a)</span><span style=\"font-family:Times New Roman\"> According to the statements, There are two possibilities. So, Either conclusion I or II follows. </span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646808302/word/media/image14.png\"/></p>",
                    solution_hi: " <p>2.(a) </span><span style=\"font-family:Baloo\">कथन के मुताबिक, दो संभावनाएं हैं। इसलिए, या तो निष्कर्ष I या II अनुसरण करता है।</span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646808302/word/media/image14.png\"/></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "6",
                    question_en: " <p>3.</span><span style=\"font-family:Times New Roman\"> Mahendra is the son of Naman, who is the brother of Reema’s mother. How is Mahendra  related to Reema? </span></p>",
                    question_hi: " <p>3.</span><span style=\"font-family:Baloo\"> महेंद्र नमन का पुत्र है, जो रीमा की माता का भाई है। महेंद्र रीमा से किस प्रकार संबंधित है?</span></p>",
                    options_en: [" <p> Cousin </span><span style=\"font-family:Times New Roman\"> </span></p>", " <p> Son </span></p>", 
                                " <p> Uncle </span><span style=\"font-family:Times New Roman\">  </span></p>", " <p> Brother </span></p>"],
                    options_hi: [" <p> चचेरा भाई / बहिन </span></p>", " <p> बेटा </span></p>",
                                " <p> चाचा</span><span style=\"font-family:Baloo\">              </span></p>", " <p> भाई</span></p>"],
                    solution_en: " <p>3.(a) </span><span style=\"font-family:Times New Roman\">According to the following family chart, Mahendra is the cousin of Reema.</span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646808302/word/media/image22.png\"/></p>",
                    solution_hi: " <p><span style=\"font-family:Baloo\">3.(a) निम्नलिखित पारिवारिक चार्ट के अनुसार, महेंद्र रीमा का चचेरा भाई है।</span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646808302/word/media/image22.png\"/></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "6",
                    question_en: "<p>4.<span style=\"font-family: Times New Roman;\"> Select the option in which the numbers are related in the same way as are the numbers of </span><span style=\"font-family: Times New Roman;\">the following set. </span></p>\r\n<p>(15, 7, 252)</p>",
                    question_hi: "<p>4.<span style=\"font-family: Baloo;\"> उस विकल्प का चयन करें जिसमें संख्याएँ उसी प्रकार संबंधित हैं जैसे निम्नलिखित सेट की संख्याएँ हैं।</span></p>\r\n<p><span style=\"font-family: Baloo;\"><span style=\"font-weight: 400;\">(15, 7, 252)</span></span></p>\r\n<p>&nbsp;</p>",
                    options_en: ["<p>(12, 16, 372) <span style=\"font-family: Times New Roman;\"> </span></p>", "<p>(23, 9, 510)</p>", 
                                "<p>(27, 10, 520) <span style=\"font-family: Times New Roman;\"> </span></p>", "<p>(16, 14, 451)</p>"],
                    options_hi: ["<p>(12, 16, 372) <span style=\"font-family: Times New Roman;\"> </span></p>", "<p>(23, 9, 510)</p>",
                                "<p>(27, 10, 520) <span style=\"font-family: Times New Roman;\"> </span></p>", "<p>(16, 14, 451)</p>"],
                    solution_en: "<p>4.(a)</p>\r\n<p><span style=\"font-family: Times New Roman;\">Logic : a, b, a&sup2;</span><span style=\"font-family: Times New Roman;\">+b&sup2;</span><span style=\"font-family: Times New Roman;\">-(a+b)</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">So, 12&sup2;</span><span style=\"font-family: Times New Roman;\"> + 16&sup2;</span><span style=\"font-family: Times New Roman;\"> = 400 - 28 = 372</span></p>",
                    solution_hi: "<p>4.(a)</p>\r\n<p><span style=\"font-family: Times New Roman;\">Logic : a, b, <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>a</mi><mn>2</mn></msup><mo>+</mo><msup><mi>b</mi><mn>2</mn></msup></math></span><span style=\"font-family: Times New Roman;\">-(a+b)</span></p>\r\n<p><span style=\"font-family: Baloo;\">इसलिए, <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>12</mn><mn>2</mn></msup><mo>+</mo><msup><mn>16</mn><mn>2</mn></msup></math></span><span style=\"font-family: Times New Roman;\">&nbsp;= 400 - 28 = 372</span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "6",
                    question_en: "<p>5. <span style=\"font-family: Times New Roman;\">Three of the following four words are alike in a certain way and one is different. Select the </span><span style=\"font-family: Times New Roman;\">odd one. </span></p>",
                    question_hi: " <p>5.</span><span style=\"font-family:Baloo\"> निम्नलिखित चार शब्दों में से तीन एक निश्चित तरीके से एक जैसे हैं और एक अलग है। विषम का चयन करें।</span></p>",
                    options_en: ["<p>Dry Fruits <span style=\"font-family: Times New Roman;\"> </span></p>", "<p>Almond</p>", 
                                "<p>Cashew <span style=\"font-family: Times New Roman;\"> </span></p>", "<p>Walnuts</p>"],
                    options_hi: [" <p> सूखे मेवा </span></p>", " <p> बादाम</span></p>",
                                " <p> काजू</span><span style=\"font-family:Baloo\">  </span></p>", " <p> अखरोट</span></p>"],
                    solution_en: "<p>5.(a)</p>\r\n<p><span style=\"font-family: Times New Roman;\">Except dry fruits all other options comes under category of dry fruits </span></p>",
                    solution_hi: " <p>5.(a)</span></p> <p><span style=\"font-family:Baloo\">सूखे मेवों को छोड़कर अन्य सभी  विकल्प सूखे मेवों की श्रेणी में आते हैं। </span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "6",
                    question_en: " <p>6.</span><span style=\"font-family:Times New Roman\"> Select the option that is related to the third letter-cluster in the same way as the second letter-cluster is related to the first letter-cluster.  </span></p> <p><span style=\"font-family:Times New Roman\">FOR : MVY : : DEX : ? </span></p>",
                    question_hi: " <p>6.</span><span style=\"font-family:Baloo\"> उस विकल्प का चयन करें जो तीसरे अक्षर-समूह से उसी प्रकार संबंधित है जैसे दूसरा अक्षर-समूह पहले अक्षर-समूह से संबंधित है।</span></p> <p><span style=\"font-family:Times New Roman\">FOR : MVY : : DEX : ? </span></p>",
                    options_en: [" <p> KLE </span></p>", " <p> LLF </span></p>", 
                                " <p> KME </span></p>", " <p> KLF </span></p>"],
                    options_hi: [" <p> KLE </span></p>", " <p> LLF </span></p>",
                                " <p> KME </span></p>", " <p> KLF </span></p>"],
                    solution_en: " <p>6.(a)</span></p> <p><span style=\"font-family:Times New Roman\">Logic : +7 rule is used</span></p> <p><span style=\"font-family:Times New Roman\">So, D +7 = K, E+7=L and X+7 is 31-26 =E </span></p> <p><span style=\"font-family:Times New Roman\">So, KLE is the correct answer. </span></p>",
                    solution_hi: " <p>6.(a)</span></p> <p><span style=\"font-family:Times New Roman\">Logic : +7 rule is used</span></p> <p><span style=\"font-family:Baloo\">इसलिए, D +7 = K, E+7=L तथा X+7 is 31-26 =E </span></p> <p><span style=\"font-family:Baloo\">तो, KLE सही उत्तर है।</span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "6",
                    question_en: " <p>7.</span><span style=\"font-family:Times New Roman\"> Select the combination of letters that when sequentially placed in the blanks of the given letter series will complete the series.  </span></p> <p><span style=\"font-family:Times New Roman\">p _ q q r _ p p q _ r r _ p q _ _ r </span></p>",
                    question_hi: " <p>7.</span><span style=\"font-family:Baloo\"> अक्षरों के उस संयोजन का चयन करें जिसे दी गई अक्षर श्रंखला के रिक्त स्थानों में क्रमिक रूप से रखने पर श्रंखला पूरी हो जाएगी।</span></p> <p><span style=\"font-family:Times New Roman\">p _ q q r _ p p q _ r r _ p q _ _ r </span></p>",
                    options_en: [" <p> p r q p q r </span><span style=\"font-family:Times New Roman\">   </span></p>", " <p> r p q r p q </span></p>", 
                                " <p> q q p p r r </span><span style=\"font-family:Times New Roman\">   </span></p>", " <p> q p r q r q </span></p>"],
                    options_hi: [" <p> p r q p q r </span><span style=\"font-family:Times New Roman\">    </span></p>", " <p> r p q r p q </span></p>",
                                " <p> q q p p r r </span><span style=\"font-family:Times New Roman\">    </span></p>", " <p> q p r q r q </span></p>"],
                    solution_en: " <p>7.(a)</span></p> <p><span style=\"font-family:Times New Roman\">Repetitive unit is ppqqrr</span></p>",
                    solution_hi: " <p>7.(a)</span></p> <p><span style=\"font-family:Baloo\">दोहराव इकाई ppqqrr है। </span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "6",
                    question_en: " <p>8.</span><span style=\"font-family:Times New Roman\"> Select the option in which the words share the same relationship as that shared by the given pair of words.  </span></p> <p><span style=\"font-family:Times New Roman\">Axe : Chop </span></p>",
                    question_hi: " <p>8.</span><span style=\"font-family:Baloo\"> उस विकल्प का चयन करें जिसमें शब्द वही संबंध साझा करते हैं जो दिए गए शब्दों के जोड़े द्वारा साझा किए गए हैं।</span></p> <p><span style=\"font-family:Baloo\">कुल्हाड़ी: कटाई</span></p>",
                    options_en: [" <p> Scalpel : Stitching </span></p>", " <p> Chisel : Carve </span></p>", 
                                " <p> Scissor : Design </span></p>", " <p> Compass : Architecture </span></p>"],
                    options_hi: [" <p> खोपड़ी: सिलाई</span></p>", " <p> छेनी: नक्काशी</span></p>",
                                " <p> कैंची: डिजाइन</span></p>", " <p> कम्पास: वास्तुकला</span></p>"],
                    solution_en: " <p>8.(b)</span></p> <p><span style=\"font-family:Times New Roman\">As Axe is used for chopping similarly chisel is used for carving.</span></p>",
                    solution_hi: " <p>8.(b)</span></p> <p><span style=\"font-family:Baloo\">जिस प्रकार कुल्हाड़ी का उपयोग काटने के लिए किया जाता है उसी प्रकार छेनी का उपयोग नक्काशी के लिए किया जाता है।</span></p> <p><span style=\"font-family:Times New Roman\"> </span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "6",
                    question_en: " <p>9.</span><span style=\"font-family:Times New Roman\"> Four letter-clusters have been given, out of which three are alike in some manner and one is different. Select the letter-cluster that is different. </span></p>",
                    question_hi: " <p>9.</span><span style=\"font-family:Baloo\"> चार अक्षर-समूह दिए गए हैं, जिनमें से तीन किसी न किसी रूप में एक जैसे हैं और एक भिन्न है। उस अक्षर-समूह का चयन करें जो भिन्न हो।</span></p>",
                    options_en: [" <p> XZCG  </span><span style=\"font-family:Times New Roman\">  </span></p>", " <p> BDGK </span></p>", 
                                " <p> VYAE </span><span style=\"font-family:Times New Roman\">  </span></p>", " <p> MORV </span></p>"],
                    options_hi: [" <p> XZCG </span></p>", " <p> BDGK </span></p>",
                                " <p> VYAE </span></p>", " <p> MORV </span></p>"],
                    solution_en: " <p>9.(c)</span></p> <p><span style=\"font-family:Times New Roman\">Logic : +2, +3, +4 pattern is used</span></p> <p><span style=\"font-family:Times New Roman\">But in option c VYAE, V +2 = X(not Y).</span></p>",
                    solution_hi: " <p>9.(c)</span></p> <p><span style=\"font-family:Baloo\">Logic : +2, +3, +4 पैटर्न का उपयोग किया गया है। लेकिन विकल्प (c) में, VYAE, V +2 = X(not Y).</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "6",
                    question_en: " <p>10.</span><span style=\"font-family:Times New Roman\"> In a code language, MAGIC is written as NZTRX. How will LAYER be written in that language? </span></p>",
                    question_hi: " <p>10.</span><span style=\"font-family:Baloo\"> एक कूट भाषा में MAGIC को NZTRX लिखा जाता है। उस भाषा में LAYER को कैसे लिखा जाएगा?</span></p>",
                    options_en: [" <p> OZBVH </span><span style=\"font-family:Times New Roman\"> </span></p>", " <p> NZBVQ </span></p>", 
                                " <p> OZBVI </span><span style=\"font-family:Times New Roman\">  </span></p>", " <p> PZPVI </span></p>"],
                    options_hi: [" <p> OZBVH </span><span style=\"font-family:Times New Roman\">  </span></p>", " <p> NZBVQ </span></p>",
                                " <p> OZBVI </span><span style=\"font-family:Times New Roman\">  </span></p>", " <p> PZPVI </span></p>"],
                    solution_en: " <p>10.(c)</span></p> <p><span style=\"font-family:Cardo\">Logic : coded as per their reverse alphabetical positioning.As, L↔O, A↔Z, Y↔B, E↔V and R↔I  so code of LAYER is </span><span style=\"font-family:Times New Roman\">OZBVI</span><span style=\"font-family:Times New Roman\">.</span></p>",
                    solution_hi: " <p>10.(c)</span></p> <p><span style=\"font-family:Baloo\">Logic : उनके विपरीत वर्णमाला स्थिति के अनुसार कोडित किया गया है। </span></p> <p><span style=\"font-family:Arial Unicode MS\">चूँकि, L↔O, A↔Z, Y↔B, E↔V and R↔I  अतः LAYER का कोड OZBVI है।</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "6",
                    question_en: " <p>11.</span><span style=\"font-family:Times New Roman\"> In a certain code language, \'FOSTER\' is coded as \'61219202218\' and \'CARGO\' is coded as \'32618712\'. How will \'MARRIAGE\' be coded in that language? </span></p>",
                    question_hi: " <p>11. </span><span style=\"font-family:Baloo\">एक निश्चित कूट भाषा में, \'FOSTER\' को \'61219202218\' और \'CARGO\' को \'32618712\' के रूप में कोडित किया जाता है। उसी भाषा में \'MARRIAGE\' को किस प्रकार कोडित किया जाएगा?</span></p>",
                    options_en: [" <p> 132618180926705 </span></p>", " <p> 132618181826722 </span></p>", 
                                " <p> 140118181801722 </span></p>", " <p> 132609091826705 </span></p>"],
                    options_hi: [" <p> 132618180926705 </span></p>", " <p> 132618181826722 </span></p>",
                                " <p> 140118181801722 </span></p>", " <p> 132609091826705 </span></p>"],
                    solution_en: " <p>11.(b)</span></p> <p><span style=\"font-family:Times New Roman\">Logic : alphabetical position of consonant and reverse alphabetical position of vowels is used.</span></p> <p><span style=\"font-family:Times New Roman\">MARRIAGE is coded as 13-26-18-18-18-26-7-22</span></p>",
                    solution_hi: " <p>11.(b)</span></p> <p><span style=\"font-family:Baloo\">Logic : व्यंजन की वर्णानुक्रमिक स्थिति और स्वरों की विपरीत वर्णानुक्रमिक स्थिति का उपयोग किया जाता है।</span></p> <p><span style=\"font-family:Baloo\">MARRIAGE को 13-26-18-18-18-26-7-22 के रूप में कोडित किया गया है। </span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "6",
                    question_en: " <p>12.</span><span style=\"font-family:Times New Roman\"> How many triangles are there in the given figure?</span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646808302/word/media/image15.png\"/></p>",
                    question_hi: " <p>12.</span><span style=\"font-family:Baloo\"> दी गई आकृति में कितने त्रिभुज हैं?</span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646808302/word/media/image15.png\"/></p>",
                    options_en: [" <p> 16</span></p>", " <p> 15</span></p>", 
                                " <p> 17</span></p>", " <p> 14</span></p>"],
                    options_hi: [" <p> 16</span></p>", " <p> 15</span></p>",
                                " <p> 17</span></p>", " <p> 14</span></p>"],
                    solution_en: " <p>12.(b)</span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646808302/word/media/image3.png\"/></p> <p><span style=\"font-family:Times New Roman\">There are 8 individual triangles numbered from 1 to 8. Other 7 triangles are the combination of (1 & 2), (1 & 4), (3 & 2), (3 & 4), (3, 4, 5 & 6), (5 & 6) and (7 & 9). </span></p>",
                    solution_hi: " <p>12.(b)</span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646808302/word/media/image3.png\"/></p> <p><span style=\"font-family:Baloo\">1 से 8 तक संख्यांकित 8 व्यक्तिगत त्रिभुज हैं। अन्य 7 त्रिभुज (1 & 2), (1 & 4), (3 & 2), (3 & 4), (3, 4, 5 & 6), (5 & 6) और (7 & 9) का संयोजन हैं। </span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "6",
                    question_en: "<p>13.<span style=\"font-family: Times New Roman;\"> Four number-pairs have been given, out of which three are alike in some manner and one is different. Select the number-pair that is different. </span></p>",
                    question_hi: "<p>13.<span style=\"font-family: Baloo;\"> चार संख्या-जोड़े दिए गए हैं, जिनमें से तीन किसी न किसी रूप में एक जैसे हैं और एक भिन्न है। उस संख्या-युग्म का चयन करें जो भिन्न है।</span></p>",
                    options_en: ["<p>4 : 70 <span style=\"font-family: Times New Roman;\"> </span></p>", "<p>7 : 349</p>", 
                                "<p>3 : 30 <span style=\"font-family: Times New Roman;\"> </span></p>", "<p>8 : 518</p>"],
                    options_hi: ["<p>4 : 70 <span style=\"font-family: Times New Roman;\"> </span></p>", "<p>7 : 349</p>",
                                "<p>3 : 30 <span style=\"font-family: Times New Roman;\"> </span></p>", "<p>8 : 518</p>"],
                    solution_en: "<p>13.(c)<span style=\"font-family: Times New Roman;\"> Logic : n : n&sup3;</span><span style=\"font-family: Times New Roman;\"> +6</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">But 3&sup3;</span><span style=\"font-family: Times New Roman;\"> + 6 = 33 (not 30)</span></p>",
                    solution_hi: "<p>13.(c)<span style=\"font-family: Times New Roman;\"> Logic : n : <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>n</mi><mn>3</mn></msup></math></span><span style=\"font-family: Times New Roman;\">&nbsp;+6</span></p>\r\n<p><span style=\"font-family: Baloo;\">लेकिन, <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>3</mn><mn>3</mn></msup></math></span><span style=\"font-family: Times New Roman;\">&nbsp;+ 6 = 33 (not 30)</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "6",
                    question_en: " <p>14. </span><span style=\"font-family:Times New Roman\">A piece of paper is folded and cut as shown below in the question figures. Select from the given answer figures , how it will appear when unfolded.</span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646808302/word/media/image26.png\"/></p>",
                    question_hi: " <p>14.</span><span style=\"font-family:Baloo\"> कागज के एक टुकड़े को मोड़ा और काटा जाता है जैसा कि नीचे प्रश्न आकृति में दिखाया गया है। दिए गए उत्तर आकृतियों में से चुनें कि जब इसे खोला जाएगा तो यह कैसा दिखाई देगा।</span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646808302/word/media/image26.png\"/></p>",
                    options_en: [" <p> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646808302/word/media/image16.png\"/></p>", " <p> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646808302/word/media/image30.png\"/></p>", 
                                " <p> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646808302/word/media/image10.png\"/></p>", " <p> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646808302/word/media/image29.png\"/></p>"],
                    options_hi: [" <p> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646808302/word/media/image16.png\"/></p>", " <p> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646808302/word/media/image30.png\"/></p>",
                                " <p> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646808302/word/media/image10.png\"/></p>", " <p> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646808302/word/media/image29.png\"/></p>"],
                    solution_en: " <p>14.(b)</span></p> <p><span style=\"font-family:Times New Roman\">After unfolding the paper, The figure will look like the figure given below,</span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646808302/word/media/image4.png\"/></p>",
                    solution_hi: " <p>14.(b)</span></p> <p><span style=\"font-family:Baloo\">कागज को खोलने के बाद, आकृति नीचे दी गई आकृति की तरह दिखेगी। </span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646808302/word/media/image4.png\"/></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "6",
                    question_en: " <p>15.</span><span style=\"font-family:Times New Roman\"> Ankit is 30 years younger than his father. After 4 years Ankit\'s sister will be half the age of her father. </span><span style=\"font-family:Times New Roman\">The total of the</span><span style=\"font-family:Times New Roman\"> present ages of Ankit and his sister is 67 years. What is the present age of Ankit\'s father? </span></p>",
                    question_hi: "<p>5. <span style=\"font-family: Baloo;\">अंकित अपने पिता से 30 वर्ष छोटा है। 4 वर्ष बाद अंकित की बहन की आयु अपने पिता की आयु से आधी होगी। अंकित और उसकी बहन की वर्तमान आयु का योग 67 वर्ष है। अंकित के पिता की वर्तमान आयु क्या है?</span></p>",
                    options_en: [" <p> 66 years </span><span style=\"font-family:Times New Roman\">  </span></p>", " <p> 48 years </span></p>", 
                                " <p> 60 years </span></p>", " <p> 52 years </span></p>"],
                    options_hi: ["<p>66 वर्ष<span style=\"font-family: Baloo;\"> </span></p>", "<p>48 वर्ष</p>",
                                "<p>60 वर्ष<span style=\"font-family: Baloo;\"> </span></p>", "<p>52 वर्ष</p>"],
                    solution_en: " <p>15.(a)</span></p> <p><span style=\"font-family:Times New Roman\">Let Ankit = A , Father = F and Sister = S</span></p> <p><span style=\"font-family:Times New Roman\">A + 30 = F ……(i)</span></p> <p><span style=\"font-family:Times New Roman\">2s + 4 = F …..(ii)</span></p> <p><span style=\"font-family:Times New Roman\">After solving both the equations we get,</span></p> <p><span style=\"font-family:Times New Roman\">2s - A = 26  ……(iii)</span></p> <p><span style=\"font-family:Times New Roman\">S + A = 67 ……..(iv)  (Given)</span></p> <p><span style=\"font-family:Times New Roman\">After solving both the equations,</span></p> <p><span style=\"font-family:Times New Roman\">S = 31</span></p> <p><span style=\"font-family:Times New Roman\">Put the value of S in eq.(ii) </span></p> <p><span style=\"font-family:Times New Roman\">Age of the father is 66 years.</span></p>",
                    solution_hi: "<p>15.(a)</p>\r\n<p><span style=\"font-family: Baloo;\">माना अंकित = A , पिता = F और बहन = S</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">A + 30 = F &hellip;&hellip;(i)</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">2S + 4 = F &hellip;..(ii)</span></p>\r\n<p><span style=\"font-family: Baloo;\">दोनों समीकरणों को हल करने के बाद हम पाते हैं,</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">2S - A = 26 &hellip;&hellip;(iii)</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">S + A = 67 &hellip;.(iv)(Given)</span></p>\r\n<p><span style=\"font-family: Baloo;\">दोनों समीकरणों को हल करने के बाद,</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">S = 31</span></p>\r\n<p><span style=\"font-family: Baloo;\">S का मान eq (ii) में रखें। </span></p>\r\n<p><span style=\"font-family: Baloo;\">पिता की आयु 66 वर्ष है।</span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "16",
                    section: "6",
                    question_en: "<p>16.<span style=\"font-family: Times New Roman;\"> Select the correct option that indicates the arrangement of the given words in the order in </span><span style=\"font-family: Times New Roman;\">which they appear</span><span style=\"font-family: Times New Roman;\"> in an English dictionary. </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">1. Mosaic </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">2. Mosquito </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">3. Mortgage </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">4. Moratorium </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">5. Morphed </span></p>",
                    question_hi: "<p>16.<span style=\"font-family: Baloo;\"> सही विकल्प का चयन करें जो दिए गए शब्दों की व्यवस्था को उसी क्रम में इंगित करता है जिस क्रम में वे अंग्रेजी शब्दकोश में दिखाई देते हैं।</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">1. Mosaic </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">2. Mosquito </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">3. Mortgage </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">4. Moratorium </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">5. Morphed </span></p>",
                    options_en: ["<p>4, 5, 3, 1, 2 <span style=\"font-family: Times New Roman;\"> </span></p>", "<p>1, 4, 3, 2, 5</p>", 
                                "<p>4, 3, 1, 2, 5 <span style=\"font-family: Times New Roman;\"> </span></p>", "<p>4, 1, 3, 5, 2</p>"],
                    options_hi: ["<p>4, 5, 3, 1, 2 <span style=\"font-family: Times New Roman;\"> </span></p>", "<p>1, 4, 3, 2, 5</p>",
                                "<p>4, 3, 1, 2, 5 <span style=\"font-family: Times New Roman;\"> </span></p>", "<p>4, 1, 3, 5, 2</p>"],
                    solution_en: "<p>16.(a)</p>\r\n<p><span style=\"font-family: Times New Roman;\">Correct order is 4, 5, 3, 1, 2</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Moratorium<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8596;</mo></math></span><span style=\"font-family: Times New Roman;\">Morphed<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8596;</mo></math></span><span style=\"font-family: Times New Roman;\">Mortgage<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8596;</mo></math></span><span style=\"font-family: Times New Roman;\">Mosaic<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8596;</mo></math></span><span style=\"font-family: Times New Roman;\">Mosquito</span><span style=\"font-family: Times New Roman;\">. </span></p>",
                    solution_hi: "<p>16.(a)</p>\r\n<p><span style=\"font-family: Baloo;\">सही क्रम है: 4, 5, 3, 1, 2</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Moratorium&rarr;</span><span style=\"font-family: Times New Roman;\">Morphed&rarr;</span><span style=\"font-family: Times New Roman;\">Mortgage&rarr;</span><span style=\"font-family: Times New Roman;\">Mosaic&rarr;</span><span style=\"font-family: Times New Roman;\">Mosquito</span><span style=\"font-family: Times New Roman;\">. </span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "17",
                    section: "6",
                    question_en: " <p>17.</span><span style=\"font-family:Times New Roman\"> Select the option that is related to the fourth number in the same way as the first number is related to the second number and the fifth number is related to the sixth number. </span></p> <p><span style=\"font-family:Times New Roman\">26 : 182 : : ? : 203 : : 38 : 266 </span></p>",
                    question_hi: " <p>17.</span><span style=\"font-family:Baloo\"> उस विकल्प का चयन करें जो चौथी संख्या से उसी प्रकार संबंधित है जैसे पहली संख्या दूसरी संख्या से संबंधित है और पांचवीं संख्या छठी संख्या से संबंधित है।</span></p> <p><span style=\"font-family:Times New Roman\">26 : 182 : : ? : 203 : : 38 : 266 </span></p>",
                    options_en: [" <p> 35 </span></p>", " <p> 29 </span></p>", 
                                " <p> 33 </span></p>", " <p> 27 </span></p>"],
                    options_hi: [" <p> 35 </span></p>", " <p> 29 </span></p>",
                                " <p> 33 </span></p>", " <p> 27 </span></p>"],
                    solution_en: " <p>17.(b)</span></p> <p><span style=\"font-family:Times New Roman\">Logic is : n : 7n </span></p> <p><span style=\"font-family:Times New Roman\">So, 203/7 = 29</span></p>",
                    solution_hi: " <p>17.(b)</span></p> <p><span style=\"font-family:Times New Roman\">Logic is : n : 7n </span></p> <p><span style=\"font-family:Baloo\">इसलिए, 203/7 = 29</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "18",
                    section: "6",
                    question_en: " <p>18. </span><span style=\"font-family:Times New Roman\">Which two signs and two numbers should be interchanged to make the given equation correct?  </span></p> <p><span style=\"font-family:Times New Roman\">12 × 5 + 7 ÷ 4 – 6 = 25 </span></p>",
                    question_hi: " <p>18.</span><span style=\"font-family:Baloo\"> दिए गए समीकरण को सही बनाने के लिए किन दो चिह्नों और दो संख्याओं को आपस में बदलना चाहिए?</span></p> <p><span style=\"font-family:Times New Roman\">12 × 5 + 7 ÷ 4 – 6 = 25 </span></p>",
                    options_en: [" <p> × and ÷, 7 and 6 </span></p>", " <p> × and −, 6 and 5 </span></p>", 
                                " <p> ÷ and −, 4 and 12 </span></p>", " <p> × and +, 12 and 4 </span></p>"],
                    options_hi: [" <p> × and ÷, 7 and 6 </span></p>", " <p> × and −, 6 and 5 </span></p>",
                                " <p> ÷ and −, 4 and 12 </span></p>", " <p> × and +, 12 and 4 </span></p>"],
                    solution_en: " <p>18.(c)</span></p> <p><span style=\"font-family:Times New Roman\">After applying hit and trial method</span></p> <p><span style=\"font-family:Times New Roman\">12 × 5 + 7 ÷ 4 – 6 = 25</span></p> <p><span style=\"font-family:Times New Roman\">4 × 5 + 7 - 12 ÷  6 = 25</span></p> <p><span style=\"font-family:Times New Roman\">20 + 7 -2 =25</span></p>",
                    solution_hi: " <p>18.(c)</span></p> <p><span style=\"font-family:Baloo\">हिट एंड ट्रायल विधि लागू करने के बाद,</span></p> <p><span style=\"font-family:Times New Roman\">12 × 5 + 7 ÷ 4 – 6 = 25</span></p> <p><span style=\"font-family:Times New Roman\">4 × 5 + 7 - 12 ÷  6 = 25</span></p> <p><span style=\"font-family:Times New Roman\">20 + 7 -2 =25</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "19",
                    section: "6",
                    question_en: " <p>19.</span><span style=\"font-family:Times New Roman\"> Select the cubes that can be formed by folding the given sheet along the lines.</span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646808302/word/media/image5.png\"/></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646808302/word/media/image7.png\"/></p>",
                    question_hi: " <p>19.</span><span style=\"font-family:Baloo\"> उन घनों का चयन कीजिए जो दी गई शीट को रेखाओं के अनुदिश मोड़कर बनाए जा सकते हैं।</span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646808302/word/media/image5.png\"/></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646808302/word/media/image7.png\"/></p>",
                    options_en: [" <p> Only A and D</span></p>", " <p> Only B, C and D</span></p>", 
                                " <p> Only A , C and D</span></p>", " <p> Only B and C</span></p>"],
                    options_hi: [" <p> केवल A और D</span></p>", " <p> केवल B, C और D</span></p>",
                                " <p> केवल A, C और D</span></p>", " <p> केवल B और C</span></p>"],
                    solution_en: " <p>19.(a)</span></p> <p><span style=\"font-family:Times New Roman\">Opposite pairs are (‘m’ and ‘o’),(‘a’ and ‘c’) and (‘d’ and ‘e’)And both the opposite sides of the cube can’t be visible at the same time. In figure B ‘o’ and ‘m’ and in figure C  ‘a’ and ‘c’ are visible so A and D is the correct answer.</span></p>",
                    solution_hi: " <p>19.(a)</span></p> <p><span style=\"font-family:Baloo\">विपरीत जोड़े हैं (\'m\' और \'o\'),(\'a\' और \'c\') और (\'d\' और \'e\') और घन के दोनों विपरीत पक्ष एक ही समय में दिखाई नहीं दे सकते। आकृति B में \'O\' और \'M\' और आकृति सी में \'A\' और \'C\' दिखाई दे रहे हैं, इसलिए A और D सही उत्तर है।</span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "20",
                    section: "6",
                    question_en: " <p>20.</span><span style=\"font-family:Times New Roman\"> Select the correct mirror image of the given letter cluster when a mirror is placed on the right side of the cluster. </span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646808302/word/media/image8.png\"/></p>",
                    question_hi: " <p>20.</span><span style=\"font-family:Baloo\"> दिए गए अक्षर क्लस्टर की सही दर्पण छवि का चयन करें जब एक दर्पण क्लस्टर के दाईं ओर रखा जाता है।</span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646808302/word/media/image8.png\"/></p>",
                    options_en: [" <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646808302/word/media/image19.png\"/></p>", " <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646808302/word/media/image31.png\"/></p>", 
                                " <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646808302/word/media/image27.png\"/></p>", " <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646808302/word/media/image11.png\"/></p>"],
                    options_hi: [" <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646808302/word/media/image19.png\"/></p>", " <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646808302/word/media/image31.png\"/></p>",
                                " <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646808302/word/media/image27.png\"/></p>", " <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646808302/word/media/image11.png\"/></p>"],
                    solution_en: " <p>20.(c)</span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646808302/word/media/image17.png\"/></p>",
                    solution_hi: " <p>20.(c)</span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646808302/word/media/image17.png\"/></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "21",
                    section: "6",
                    question_en: "<p>21.<span style=\"font-family: Times New Roman;\"> Select the option in which the numbers share the same relationship as that shared by the </span><span style=\"font-family: Times New Roman;\">numbers in the given set. </span></p>\r\n<p>(6, 8, 192)</p>",
                    question_hi: "<p>21.<span style=\"font-family: Baloo;\"> उस विकल्प का चयन करें जिसमें संख्याएं वही संबंध साझा करती हैं जो दिए गए सेट में संख्याओं द्वारा साझा किया जाता है।</span></p>\r\n<p><span style=\"font-weight: 400;\">(6, 8, 192) </span></p>",
                    options_en: ["<p>(11, 9, 396) <span style=\"font-family: Times New Roman;\"> </span></p>", "<p>(7, 5, 78)</p>", 
                                "<p>(12, 13, 263) <span style=\"font-family: Times New Roman;\"> </span></p>", "<p>(8, 11, 180)</p>"],
                    options_hi: ["<p>(11, 9, 396) <span style=\"font-family: Times New Roman;\"> </span></p>", "<p>(7, 5, 78)</p>",
                                "<p>(12, 13, 263) <span style=\"font-family: Times New Roman;\"> </span></p>", "<p>(8, 11, 180)</p>"],
                    solution_en: "<p>21.(a)</p>\r\n<p><span style=\"font-family: Times New Roman;\">Logic : (a, b, 4ab)</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">So, 4&times;11&times;9 =396</span></p>",
                    solution_hi: "<p>21.(a)</p>\r\n<p><span style=\"font-family: Times New Roman;\">Logic : (a, b, 4ab)</span></p>\r\n<p><span style=\"font-family: Baloo;\">इसलिए, 4&times;11&times;9 =396</span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "22",
                    section: "6",
                    question_en: " <p>22.</span><span style=\"font-family:Times New Roman\"> Select the figure from among the given options that can replace the question mark(?) in the following series.</span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646808302/word/media/image12.png\"/></p>",
                    question_hi: " <p>22.</span><span style=\"font-family:Baloo\"> दिए गए विकल्पों में से उस आकृति का चयन करें जो निम्नलिखित श्रृंखला में प्रश्न चिह्न (?) को प्रतिस्थापित कर सकता है।</span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646808302/word/media/image12.png\"/></p>",
                    options_en: [" <p> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646808302/word/media/image9.png\"/></p>", " <p> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646808302/word/media/image25.png\"/></p>", 
                                " <p> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646808302/word/media/image23.png\"/></p>", " <p> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646808302/word/media/image21.png\"/></p>"],
                    options_hi: [" <p> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646808302/word/media/image9.png\"/></p>", " <p> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646808302/word/media/image25.png\"/></p>",
                                " <p> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646808302/word/media/image23.png\"/></p>", " <p> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646808302/word/media/image21.png\"/></p>"],
                    solution_en: " <p>22.(d)</span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646808302/word/media/image20.png\"/></p>",
                    solution_hi: " <p>22.(d)</span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646808302/word/media/image20.png\"/></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "23",
                    section: "6",
                    question_en: " <p>23.</span><span style=\"font-family:Times New Roman\"> Select the number that can replace the question mark (?) in the following series.  </span></p> <p><span style=\"font-family:Times New Roman\">14, 29, 60, 123, ? , 505 </span></p>",
                    question_hi: " <p>23.</span><span style=\"font-family:Baloo\"> उस संख्या का चयन करें जो निम्नलिखित श्रृंखला में प्रश्नवाचक चिन्ह (?) को प्रतिस्थापित कर सकती है।</span></p> <p><span style=\"font-family:Times New Roman\">14, 29, 60, 123, ? , 505 </span></p>",
                    options_en: [" <p> 305 </span></p>", " <p> 250 </span></p>", 
                                " <p> 280 </span></p>", " <p> 150 </span></p>"],
                    options_hi: [" <p> 305 </span></p>", " <p> 250 </span></p>",
                                " <p> 280 </span></p>", " <p> 150 </span></p>"],
                    solution_en: " <p>23.(b)</span></p> <p><span style=\"font-family:Times New Roman\">Logic : ×2 +1, ×2+2, ×2+3 and so on…</span></p> <p><span style=\"font-family:Times New Roman\">123 ×2 +4 = 246 + 4 =250. </span></p>",
                    solution_hi: " <p>23.(b)</span></p> <p><span style=\"font-family:Baloo\">Logic : ×2 +1, ×2+2, ×2+3 और इसी तरह…123 ×2 +4 = 246 + 4 =250. </span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "24",
                    section: "6",
                    question_en: " <p>24.</span><span style=\"font-family:Times New Roman\"> ‘Soldier’ is related to ‘Security’ in the same way as ‘Farmer’ is related to ‘________’. </span></p>",
                    question_hi: " <p>24. </span><span style=\"font-family:Baloo\">\'सिपाही\' का संबंध \'सुरक्षा\' से उसी प्रकार है जैसे \'किसान\' का संबंध \'________\' से है।</span></p>",
                    options_en: [" <p> Army </span><span style=\"font-family:Times New Roman\">  </span></p>", " <p> Agriculture </span></p>", 
                                " <p> Poverty </span><span style=\"font-family:Times New Roman\">  </span></p>", " <p> Rain </span></p>"],
                    options_hi: [" <p> सेना</span></p>", " <p> कृषि</span></p>",
                                " <p> गरीबी</span></p>", " <p> नाली</span></p>"],
                    solution_en: " <p>24.(b)</span></p> <p><span style=\"font-family:Times New Roman\">As a soldier\'s profession is to provide security similarly, </span><span style=\"font-family:Times New Roman\">farmer’s</span><span style=\"font-family:Times New Roman\"> profession is Agriculture.</span></p>",
                    solution_hi: " <p>24.(b)</span></p> <p><span style=\"font-family:Baloo\">जिस प्रकार एक सैनिक का पेशा सुरक्षा प्रदान करना है उसी तरह किसान का पेशा कृषि है।</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "25",
                    section: "misc",
                    question_en: " <p>25.</span><span style=\"font-family:Times New Roman\"> In the following Venn diagram, the pentagon stands for ‘Librarians’, the rectangle stands for ‘Income tax payers’, and the circle stands for ‘Post-graduates’. The given number represents the number of persons in that particular category.    </span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646808302/word/media/image13.png\"/></p> <p><span style=\"font-family:Times New Roman\">How many librarians are there who are also post graduates?</span></p>",
                    question_hi: " <p>25.</span><span style=\"font-family:Baloo\"> निम्नलिखित वेन आरेख में, पेंटागन \'लाइब्रेरियन\' के लिए खड़ा है, आयत \'आयकर दाताओं\' के लिए है, और वृत्त \'स्नातकोत्तर\' के लिए है। दी गई संख्या उस विशेष श्रेणी में व्यक्तियों की संख्या को दर्शाती है।   </span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646808302/word/media/image13.png\"/></p> <p><span style=\"font-family:Baloo\">कितने पुस्तकालयाध्यक्ष हैं जो स्नातकोत्तर भी हैं?</span></p>",
                    options_en: [" <p> 14</span></p>", " <p> 55</span></p>", 
                                " <p> 30</span></p>", " <p> 25</span></p>"],
                    options_hi: [" <p> 14</span></p>", " <p> 55</span></p>",
                                " <p> 30</span></p>", " <p> 25</span></p>"],
                    solution_en: " <p>25.(b)</span></p> <p><span style=\"font-family:Times New Roman\">librarians are there who are also post </span><span style=\"font-family:Times New Roman\">graduates are</span><span style=\"font-family:Times New Roman\"> represented by the common area of circle and pentagon.</span></p> <p><span style=\"font-family:Times New Roman\">So, 25 + 30 =55.</span></p>",
                    solution_hi: " <p>25.(b)</span></p> <p><span style=\"font-family:Baloo\">पुस्तकालयाध्यक्ष हैं जो स्नातकोत्तर भी हैं जिनका प्रतिनिधित्व वृत्त और पेंटागन के सामान्य क्षेत्र द्वारा किया जाता है।</span></p> <p><span style=\"font-family:Baloo\">इसलिए, 25 + 30 =55.</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML - more compact
            let html = `
                <div class="card mb-3">
                    <div class="card-body">
                        <div class="question-text mb-3">
                            <div class="en">${question.question_en}</div>
                            <div class="hi" style="display:none">${question.question_hi}</div>
                        </div>
                        <div class="options">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = 'option-label';
                // When reviewing, highlight user's choice and correct answer
                if (submitted) {
                    if (opt === question.correct) {
                        optionClass += ' border-success bg-success bg-opacity-10';
                    }
                    if (answers[question.id] === opt && opt !== question.correct) {
                        optionClass += ' border-danger bg-danger bg-opacity-10';
                    }
                }
                html += `
                    <div class="form-check mb-2">
                        <input class="form-check-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${answers[question.id] === opt ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                        <label class="form-check-label w-100 ${optionClass}" for="opt-${index}-${opt}">
                            ${submitted && opt === question.correct ? '<i class="fas fa-check-circle text-success me-1"></i>' : ''}
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const alertClass = isCorrect ? 'alert-success' : 'alert-danger';
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="alert ${alertClass} mt-3 py-2">
                        <strong>${resultText}</strong>
                        <div class="mt-1 small">
                            <strong>Solution:</strong>
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                document.getElementById(`box-${index}`).classList.add('attempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { if (currentQuestion > 0) showQuestion(currentQuestion - 1); }
        function nextQuestion() { if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
            // Update button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const content = document.getElementById('main-content');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                if (window.innerWidth >= 992) {
                    content.classList.add('with-nav');
                }
            } else {
                nav.classList.remove('show');
                if (window.innerWidth >= 992) {
                    content.classList.remove('with-nav');
                }
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer - compact
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            if (confirm(`You have attempted ${attempted} of ${questions.length} questions.
${remaining} questions are unattempted.
Are you sure you want to submit?`)) {
                submitTest();
            }
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('bg-secondary', 'text-white');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            resultsModal.show();
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.classList.replace('btn-light', 'btn-success');
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            resultsModal.hide();
            showQuestion(0);
        }
        // Add event listener for window resize
        window.addEventListener('resize', function() {
            if (window.innerWidth >= 992) {
                if (document.getElementById('question-nav').classList.contains('show')) {
                    document.getElementById('main-content').classList.add('with-nav');
                }
            } else {
                document.getElementById('main-content').classList.remove('with-nav');
            }
        });
    </script>
</body>
</html>