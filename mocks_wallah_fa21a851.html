<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">15:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 15</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">15</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 15 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 0,
                end: 13
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 14,
                end: 14
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "17",
                    question_en: "<p>1.<span style=\"font-family: Roboto;\"> In a two-digit number, the digit in the unit\'s place is four times the digit in ten\'s place and sum of the digits is equal to 10. Find the number.</span></p>\n",
                    question_hi: "<p>1.<span style=\"font-family: Palanquin;\"> &#2342;&#2379; &#2309;&#2306;&#2325;&#2379;&#2306; &#2325;&#2368; &#2360;&#2306;&#2326;&#2381;&#2351;&#2366; &#2350;&#2375;&#2306;, &#2311;&#2325;&#2366;&#2312; &#2325;&#2366; &#2309;&#2306;&#2325; &#2342;&#2361;&#2366;&#2312; &#2325;&#2375; &#2309;&#2306;&#2325; &#2325;&#2366; &#2330;&#2366;&#2352; &#2327;&#2369;&#2344;&#2366; &#2361;&#2376; &#2324;&#2352; &#2309;&#2306;&#2325;&#2379;&#2306; &#2325;&#2366; &#2351;&#2379;&#2327; 10 &#2325;&#2375; &#2348;&#2352;&#2366;&#2348;&#2352; &#2361;&#2376;&#2404; &#2360;&#2306;&#2326;&#2381;&#2351;&#2366; &#2332;&#2381;&#2334;&#2366;&#2340; &#2325;&#2368;&#2332;&#2367;&#2319;&#2404;</span></p>\n",
                    options_en: ["<p>28</p>\n", "<p>14</p>\n", 
                                "<p>82</p>\n", "<p>41</p>\n"],
                    options_hi: ["<p>28</p>\n", "<p>14</p>\n",
                                "<p>82</p>\n", "<p>41</p>\n"],
                    solution_en: "<p>1.(a)</p>\r\n<p><span style=\"font-family: Roboto;\">Let, unit digit of a two digit number is 4x and the digit in ten&rsquo;s place is x</span></p>\r\n<p><span style=\"font-family: Roboto;\">According to question,</span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfenced><mrow><mi>x</mi><mo>&nbsp;</mo><mo>+</mo><mn>4</mn><mi>x</mi></mrow></mfenced><mo>=</mo><mn>10</mn></math></p>\r\n<p><span style=\"font-family: Roboto;\">&rArr; 5x =10 </span></p>\r\n<p><span style=\"font-family: Roboto;\">&rArr; </span><span style=\"font-family: Roboto;\">x = 2</span></p>\r\n<p><span style=\"font-family: Roboto;\">So, unit digit is </span><span style=\"font-family: Roboto;\">4x =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&nbsp;</mo><mfenced><mrow><mn>4</mn><mo>&times;</mo><mn>2</mn></mrow></mfenced><mo>&nbsp;</mo></math> </span><span style=\"font-family: Roboto;\"> = 8</span><span style=\"font-family: Roboto;\"> and </span><span style=\"font-family: Roboto;\"> digit in ten&rsquo;s place is 2</span></p>\r\n<p><span style=\"font-family: Roboto;\">Therefore number = </span><span style=\"font-family: Roboto;\">28</span></p>\n",
                    solution_hi: "<p>1.(a)</p>\r\n<p><span style=\"font-family: Palanquin Dark;\">&#2350;&#2366;&#2344; &#2354;&#2368;&#2332;&#2367;&#2319;, &#2342;&#2379; &#2309;&#2306;&#2325;&#2379;&#2306; &#2325;&#2368; &#2360;&#2306;&#2326;&#2381;&#2351;&#2366; &#2325;&#2366; &#2311;&#2325;&#2366;&#2312; &#2309;&#2306;&#2325; 4x &#2361;&#2376; &#2324;&#2352; &#2342;&#2361;&#2366;&#2312; &#2325;&#2375; &#2360;&#2381;&#2341;&#2366;&#2344; &#2325;&#2366; &#2309;&#2306;&#2325; x &#2361;&#2376;</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">&#2346;&#2381;&#2352;&#2358;&#2381;&#2344; &#2325;&#2375; &#2309;&#2344;&#2369;&#2360;&#2366;&#2352;,</span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfenced><mrow><mi>x</mi><mo>+</mo><mn>4</mn><mi>x</mi></mrow></mfenced><mo>&nbsp;</mo><mo>=</mo><mn>10</mn></math></p>\r\n<p><span style=\"font-family: Roboto;\">&rArr; 5x = 10</span></p>\r\n<p><span style=\"font-family: Roboto;\">&rArr; </span><span style=\"font-family: Roboto;\">x = 2</span></p>\r\n<p><span style=\"font-family: Palanquin;\">&#2309;&#2340;&#2307; &#2311;&#2325;&#2366;&#2312; &#2325;&#2366; &#2309;&#2306;&#2325; 4x</span><span style=\"font-family: Palanquin;\"> =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&nbsp;</mo><mfenced><mrow><mn>4</mn><mo>&times;</mo><mn>2</mn></mrow></mfenced><mo>&nbsp;</mo></math> </span><span style=\"font-family: Palanquin;\"> = 8</span><span style=\"font-family: Palanquin;\"> &#2324;&#2352; &#2342;&#2361;&#2366;&#2312; &#2325;&#2366; &#2309;&#2306;&#2325; 2 &#2361;&#2376;</span></p>\r\n<p><span style=\"font-family: Roboto;\"> </span><span style=\"font-family: Palanquin Dark;\">&#2311;&#2360;&#2354;&#2367;&#2319; &#2360;&#2306;&#2326;&#2381;&#2351;&#2366; = </span><span style=\"font-family: Palanquin Dark;\">28</span></p>\n",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "2",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Roboto;\">2</span><span style=\"font-family: Roboto;\">. When the digits of a two-digit number are reversed, the value of the number is increased by 45. The sum of the digits is 11. What is the original number?</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Roboto;\">2. </span><span style=\"font-family: Palanquin;\">&#2332;&#2348; &#2342;&#2379; &#2309;&#2306;&#2325;&#2379;&#2306; &#2325;&#2368; &#2360;&#2306;&#2326;&#2381;&#2351;&#2366; &#2325;&#2375; &#2309;&#2306;&#2325; &#2310;&#2346;&#2360; &#2350;&#2375;&#2306; &#2348;&#2342;&#2354; &#2342;&#2367;&#2319; &#2332;&#2366;&#2340;&#2375; &#2361;&#2376;&#2306;, &#2340;&#2379; &#2360;&#2306;&#2326;&#2381;&#2351;&#2366; &#2325;&#2366; &#2350;&#2366;&#2344; 45 &#2348;&#2338;&#2364; &#2332;&#2366;&#2340;&#2366; &#2361;&#2376;&#2404; &#2309;&#2306;&#2325;&#2379;&#2306; &#2325;&#2366; &#2351;&#2379;&#2327;&#2347;&#2354; 11 &#2361;&#2376;&#2404; &#2350;&#2370;&#2354; &#2360;&#2306;&#2326;&#2381;&#2351;&#2366; &#2325;&#2381;&#2351;&#2366; &#2361;&#2376;?</span></p>\n",
                    options_en: ["<p>38</p>\n", "<p>83</p>\n", 
                                "<p>24</p>\n", "<p>65</p>\n"],
                    options_hi: ["<p>38</p>\n", "<p>83</p>\n",
                                "<p>24</p>\n", "<p>65</p>\n"],
                    solution_en: "<p>2.(a)</p>\r\n<p><span style=\"font-family: Roboto;\">Let the unit digit of a number is x and ten&rsquo;s digit is y so number will become </span></p>\r\n<p><span style=\"font-family: Roboto;\">According to question, </span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfenced><mrow><mn>10</mn><mi>x</mi><mo>+</mo><mi>y</mi></mrow></mfenced><mo>&nbsp;</mo><mo>-</mo><mfenced><mrow><mn>10</mn><mi>y</mi><mo>&nbsp;</mo><mo>+</mo><mi>x</mi></mrow></mfenced><mo>&nbsp;</mo><mo>=</mo><mn>45</mn></math></p>\r\n<p><span style=\"font-family: Roboto;\"><span style=\"font-weight: 400;\">&rArr; 9x </span><span style=\"font-weight: 400;\">-</span><span style=\"font-weight: 400;\"> 9y = 45 </span></span></p>\r\n<p><span style=\"font-family: Roboto;\"><span style=\"font-weight: 400;\">&nbsp;&rArr;&nbsp; </span><span style=\"font-weight: 400;\">x</span><span style=\"font-weight: 400;\"> </span><span style=\"font-weight: 400;\">- y</span><span style=\"font-weight: 400;\"> = 5</span>&mdash;-------eq.(1)</span></p>\r\n<p><span style=\"font-family: Roboto;\">Sum of digit =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfenced><mrow><mi>x</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mi>y</mi></mrow></mfenced></math></span><span style=\"font-family: Roboto;\"> = 11</span><span style=\"font-family: Roboto;\">&mdash;-------eq.(2)</span></p>\r\n<p><span style=\"font-family: Roboto;\">Solving the eq.(1) and eq.(2)</span></p>\r\n<p><span style=\"font-family: Roboto;\">2x = 16 <span style=\"font-weight: 400;\">&rArr; </span>&nbsp;</span><span style=\"font-family: Roboto;\">x = 8 and y = 3</span></p>\r\n<p><span style=\"font-family: Roboto;\">So, number =</span><span style=\"font-family: Roboto;\"> 38</span></p>\n",
                    solution_hi: "<p>2.(a)</p>\r\n<p><span style=\"font-family: Palanquin Dark;\">&#2350;&#2366;&#2344;&#2366; &#2325;&#2367;&#2360;&#2368; &#2360;&#2306;&#2326;&#2381;&#2351;&#2366; &#2325;&#2368; &#2311;&#2325;&#2366;&#2312; &#2325;&#2366; &#2309;&#2306;&#2325; x &#2324;&#2352; &#2342;&#2361;&#2366;&#2312; &#2325;&#2366; &#2309;&#2306;&#2325; y &#2361;&#2376; &#2340;&#2379; &#2360;&#2306;&#2326;&#2381;&#2351;&#2366; 10y + x &#2361;&#2379; &#2332;&#2366;&#2319;&#2327;&#2368;</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">&#2346;&#2381;&#2352;&#2358;&#2381;&#2344; &#2325;&#2375; &#2309;&#2344;&#2369;&#2360;&#2366;&#2352;,</span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfenced><mrow><mn>10</mn><mi>x</mi><mo>+</mo><mi>y</mi></mrow></mfenced><mo>-</mo><mfenced><mrow><mn>10</mn><mi>y</mi><mo>+</mo><mi>x</mi></mrow></mfenced><mo>=</mo><mn>45</mn></math></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">&rArr; 9x<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>-</mo></math> </span><span style=\"font-family: Palanquin Dark;\"> 9y = 45 </span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">&rArr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfenced><mrow><mi>x</mi><mo>-</mo><mi>y</mi></mrow></mfenced></math> </span><span style=\"font-family: Palanquin Dark;\"> </span><span style=\"font-family: Palanquin Dark;\"> </span><span style=\"font-family: Palanquin Dark;\"> = 5</span><span style=\"font-family: Palanquin Dark;\">&mdash;-------&#2360;&#2350;&#2368;&#2325;&#2352;&#2339;(1)</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">&#2309;&#2306;&#2325;&#2379;&#2306; &#2325;&#2366; &#2351;&#2379;&#2327; =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&nbsp;</mo><mfenced><mrow><mi>x</mi><mo>+</mo><mi>y</mi></mrow></mfenced></math></span><span style=\"font-family: Palanquin Dark;\"> = 11</span><span style=\"font-family: Palanquin Dark;\">&mdash;-------&#2360;&#2350;&#2368;&#2325;&#2352;&#2339;(2)</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">&#2360;&#2350;&#2368;&#2325;&#2352;&#2339; (1) &#2324;&#2352; &#2360;&#2350;&#2368;&#2325;&#2352;&#2339; (2) &#2325;&#2379; &#2361;&#2354; &#2325;&#2352;&#2344;&#2375; &#2346;&#2352;,&nbsp;</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">2x = 16 &rArr; &nbsp;</span><span style=\"font-family: Palanquin Dark;\">x = 8 &#2324;&#2352; y = 3</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">&#2309;&#2340;: &#2360;&#2306;&#2326;&#2381;&#2351;&#2366; =</span><span style=\"font-family: Palanquin Dark;\"> 38</span></p>\n",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "3",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Roboto;\">3. </span><span style=\"font-family: Roboto;\">A number when divided by 15 leaves the remainder 12. Another number when divided by 5 leaves the remainder 2. What is the remainder when their sum is divided by 5? </span></p>\n",
                    question_hi: "<p><span style=\"font-family: Roboto;\">3. </span><span style=\"font-family: Palanquin;\">&#2319;&#2325; &#2360;&#2306;&#2326;&#2381;&#2351;&#2366; &#2325;&#2379; 15 &#2360;&#2375; &#2357;&#2367;&#2349;&#2366;&#2332;&#2367;&#2340; &#2325;&#2352;&#2344;&#2375; &#2346;&#2352; &#2358;&#2375;&#2359;&#2347;&#2354; 12 &#2348;&#2330;&#2340;&#2366; &#2361;&#2376;&#2404; &#2342;&#2370;&#2360;&#2352;&#2368; &#2360;&#2306;&#2326;&#2381;&#2351;&#2366; &#2325;&#2379; 5 &#2360;&#2375; &#2357;&#2367;&#2349;&#2366;&#2332;&#2367;&#2340; &#2325;&#2352;&#2344;&#2375; &#2346;&#2352; &#2358;&#2375;&#2359;&#2347;&#2354; 2 &#2348;&#2330;&#2340;&#2366; &#2361;&#2376;&#2404; &#2313;&#2344; &#2360;&#2306;&#2326;&#2381;&#2351;&#2366;&#2323;&#2306; &#2325;&#2375; &#2351;&#2379;&#2327; &#2325;&#2379; 5 &#2360;&#2375; &#2357;&#2367;&#2349;&#2366;&#2332;&#2367;&#2340; &#2325;&#2352;&#2344;&#2375; &#2346;&#2352; &#2346;&#2381;&#2352;&#2366;&#2346;&#2381;&#2340; &#2358;&#2375;&#2359;&#2347;&#2354; &#2325;&#2381;&#2351;&#2366; &#2361;&#2379;&#2327;&#2366;?</span></p>\n",
                    options_en: ["<p>3</p>\n", "<p>1</p>\n", 
                                "<p>2</p>\n", "<p>4</p>\n"],
                    options_hi: ["<p>3</p>\n", "<p>1</p>\n",
                                "<p>2</p>\n", "<p>4</p>\n"],
                    solution_en: "<p>3.(d)</p>\r\n<p><span style=\"font-family: Roboto;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>1</mn><mi>s</mi><mi>t</mi><mo>&nbsp;</mo><mi>n</mi><mi>o</mi><mo>.</mo><mo>&nbsp;</mo><mo>(</mo><msub><mi>N</mi><mn>1</mn></msub><mo>)</mo><mo>=</mo><mfenced><mrow><mn>15</mn><mi>a</mi><mo>+</mo><mn>12</mn></mrow></mfenced><mo>&nbsp;</mo><mi>a</mi><mi>n</mi><mi>d</mi><mo>&nbsp;</mo><mn>2</mn><mi>n</mi><mi>d</mi><mo>&nbsp;</mo><mi>n</mi><mi>o</mi><mo>.</mo><mo>(</mo><msub><mi>N</mi><mn>2</mn></msub><mo>)</mo><mo>&nbsp;</mo><mo>=</mo><mfenced><mrow><mn>5</mn><mi>a</mi><mo>&nbsp;</mo><mo>+</mo><mn>2</mn></mrow></mfenced></math></span></p>\r\n<p><span style=\"font-family: Roboto;\">A.T.Q,</span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msub><mi>N</mi><mn>1</mn></msub><mo>&nbsp;</mo><mo>+</mo><msub><mi>N</mi><mn>2</mn></msub></mrow><mrow><mn>5</mn><mo>&nbsp;</mo><mo>&nbsp;</mo></mrow></mfrac><mo>=</mo><mfrac><mrow><mfenced><mrow><mn>15</mn><mi>a</mi><mo>+</mo><mn>12</mn></mrow></mfenced><mo>+</mo><mfenced><mrow><mn>5</mn><mi>a</mi><mo>&nbsp;</mo><mo>+</mo><mn>2</mn></mrow></mfenced></mrow><mrow><mn>5</mn><mo>&nbsp;</mo></mrow></mfrac></math></p>\r\n<p><span style=\"font-family: Roboto;\">= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>20</mn><mi>a</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>14</mn></mrow><mrow><mn>5</mn><mo>&nbsp;</mo><mo>&nbsp;</mo></mrow></mfrac></math> &nbsp;&rArr; </span><span style=\"font-family: Roboto;\">Rem. 4</span></p>\n",
                    solution_hi: "<p>3.(d)</p>\r\n<p><span style=\"font-family: Palanquin Dark;\">&#2346;&#2361;&#2354;&#2368; &#2360;&#2306;&#2326;&#2381;&#2351;&#2366; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&nbsp;</mo><mo>(</mo><msub><mi>N</mi><mn>1</mn></msub><mo>)</mo><mo>=</mo><mfenced><mrow><mn>15</mn><mi>a</mi><mo>+</mo><mn>12</mn></mrow></mfenced></math></span><span style=\"font-family: Palanquin Dark;\">&nbsp;</span><span style=\"font-family: Palanquin Dark;\">&#2324;&#2352; &#2342;&#2370;&#2360;&#2352;&#2368; &#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Palanquin Dark;\">&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><msub><mi>N</mi><mn>2</mn></msub><mo>)</mo><mo>=</mo><mfenced><mrow><mn>5</mn><mi>a</mi><mo>&nbsp;</mo><mo>+</mo><mn>2</mn></mrow></mfenced></math></span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">&#2346;&#2381;&#2352;&#2358;&#2381;&#2344; &#2325;&#2375; &#2309;&#2344;&#2369;&#2360;&#2366;&#2352;,</span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msub><mi>N</mi><mn>1</mn></msub><mo>&nbsp;</mo><mo>+</mo><msub><mi>N</mi><mn>2</mn></msub></mrow><mrow><mn>5</mn><mo>&nbsp;</mo><mo>&nbsp;</mo></mrow></mfrac><mo>=</mo><mfrac><mrow><mfenced><mrow><mn>15</mn><mi>a</mi><mo>+</mo><mn>12</mn></mrow></mfenced><mo>+</mo><mfenced><mrow><mn>5</mn><mi>a</mi><mo>&nbsp;</mo><mo>+</mo><mn>2</mn></mrow></mfenced></mrow><mrow><mn>5</mn><mo>&nbsp;</mo></mrow></mfrac></math></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>20</mn><mi>a</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>14</mn></mrow><mrow><mn>5</mn><mo>&nbsp;</mo><mo>&nbsp;</mo></mrow></mfrac></math> &rArr; </span><span style=\"font-family: Palanquin Dark;\">4 (&#2358;&#2375;&#2359;&#2347;&#2354; )</span></p>\n",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "4",
                    section: "17",
                    question_en: "<p>4.<span style=\"font-family: Roboto;\"> Find the value of <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>1</mn><mn>2</mn></msup><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><msup><mn>2</mn><mn>2</mn></msup><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><msup><mn>3</mn><mn>2</mn></msup></math></span><span style=\"font-family: Roboto;\"> + &hellip;&hellip;&hellip; +&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>10</mn><mn>2</mn></msup></math></span></p>\n",
                    question_hi: "<p>4.<span style=\"font-family: Roboto;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>1</mn><mn>2</mn></msup><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><msup><mn>2</mn><mrow><mn>2</mn><mo>&nbsp;</mo></mrow></msup><mo>+</mo><mo>&nbsp;</mo><msup><mn>3</mn><mn>2</mn></msup></math></span><span style=\"font-family: Roboto;\"> + &hellip;&hellip;&hellip; + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>10</mn><mn>2</mn></msup></math></span><span style=\"font-family: Roboto;\">&nbsp;</span><span style=\"font-family: Palanquin;\">&#2325;&#2366; &#2350;&#2366;&#2344; &#2332;&#2381;&#2334;&#2366;&#2340; &#2325;&#2352;&#2375;&#2306;&#2404;</span></p>\n",
                    options_en: ["<p>305</p>\n", "<p>265</p>\n", 
                                "<p>285</p>\n", "<p>385</p>\n"],
                    options_hi: ["<p>305</p>\n", "<p>265</p>\n",
                                "<p>285</p>\n", "<p>385</p>\n"],
                    solution_en: "<p>4.(d)</p>\r\n<p><span style=\"font-family: Roboto;\">We know that sum of squares of n natural numbers is </span><span style=\"font-family: Roboto;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>6</mn></mfrac><mi>n</mi><mo>&nbsp;</mo><mfenced><mrow><mi>n</mi><mo>+</mo><mn>1</mn></mrow></mfenced><mo>&nbsp;</mo><mfenced><mrow><mn>2</mn><mi>n</mi><mo>+</mo><mn>1</mn></mrow></mfenced></math></span></p>\r\n<p><span style=\"font-family: Roboto;\">&rArr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>6</mn></mfrac><mo>&times;</mo><mn>10</mn><mo>&times;</mo><mn>11</mn><mo>&times;</mo><mn>21</mn><mo>=</mo><mn>385</mn></math></span></p>\n",
                    solution_hi: "<p>4.(d)</p>\r\n<p><span style=\"font-family: Palanquin Dark;\">&#2361;&#2350; &#2332;&#2366;&#2344;&#2340;&#2375; &#2361;&#2376;&#2306; &#2325;&#2367; n &#2346;&#2381;&#2352;&#2366;&#2325;&#2371;&#2340; &#2360;&#2306;&#2326;&#2381;&#2351;&#2366;&#2323;&#2306; &#2325;&#2375; &#2357;&#2352;&#2381;&#2327;&#2379;&#2306; &#2325;&#2366; &#2351;&#2379;&#2327; = </span><span style=\"font-family: Palanquin Dark;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>6</mn></mfrac><mi>n</mi><mo>&nbsp;</mo><mfenced><mrow><mi>n</mi><mo>+</mo><mn>1</mn></mrow></mfenced><mo>&nbsp;</mo><mfenced><mrow><mn>2</mn><mi>n</mi><mo>+</mo><mn>1</mn></mrow></mfenced></math></span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">&rArr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>6</mn></mfrac><mo>&times;</mo><mn>10</mn><mo>&times;</mo><mn>11</mn><mo>&times;</mo><mn>21</mn><mo>=</mo><mn>385</mn></math></span></p>\n",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "5",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Roboto;\">5.</span><span style=\"font-family: Roboto;\"> If a and b are coprime, then <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>a</mi><mn>2</mn></msup></math></span><span style=\"font-family: Roboto;\"> and <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>b</mi><mn>2</mn></msup></math></span><span style=\"font-family: Roboto;\">&nbsp;are-</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Roboto;\">5. </span><span style=\"font-family: Palanquin;\">&#2351;&#2342;&#2367; a &#2324;&#2352; b &#2309;&#2360;&#2361;&#2349;&#2366;&#2332;&#2381;&#2351; &#2360;&#2306;&#2326;&#2381;&#2351;&#2366; &#2361;&#2376;&#2306;, &#2340;&#2379; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>a</mi><mn>2</mn></msup></math></span><span style=\"font-family: Palanquin;\"> &#2324;&#2352; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>b</mi><mn>2</mn></msup></math></span><span style=\"font-family: Palanquin;\">&nbsp;______________&#2361;&#2376; |</span><span style=\"font-family: Roboto;\"> </span></p>\n",
                    options_en: ["<p>Both odd</p>\n", "<p>Need not be coprime</p>\n", 
                                "<p>Both even</p>\n", "<p>Coprime</p>\n"],
                    options_hi: ["<p>&#2342;&#2379;&#2344;&#2379;&#2306; &#2357;&#2367;&#2359;&#2350;</p>\n", "<p>&#2332;&#2352;&#2370;&#2352;&#2368; &#2344;&#2361;&#2368;&#2306; &#2361;&#2376;, &#2325;&#2367; &#2309;&#2360;&#2361;&#2349;&#2366;&#2332;&#2381;&#2351; &#2360;&#2306;&#2326;&#2381;&#2351;&#2366; &#2361;&#2368; &#2361;&#2379;&#2306;</p>\n",
                                "<p>&#2342;&#2379;&#2344;&#2379;&#2306; &#2360;&#2350;</p>\n", "<p>&#2309;&#2360;&#2361;&#2349;&#2366;&#2332;&#2381;&#2351;</p>\n"],
                    solution_en: "<p>5.(d)</p>\r\n<p><span style=\"font-family: Roboto;\">If a and b are coprime then&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>a</mi><mn>2</mn></msup><mo>&nbsp;</mo><mi>a</mi><mi>n</mi><mi>d</mi><mo>&nbsp;</mo><msup><mi>b</mi><mn>2</mn></msup><mo>&nbsp;</mo><mi>a</mi><mi>r</mi><mi>e</mi><mo>&nbsp;</mo><mi>a</mi><mi>l</mi><mi>s</mi><mi>o</mi><mo>&nbsp;</mo></math></span></p>\r\n<p><span style=\"font-family: Roboto;\">Co-prime numbers</span></p>\n",
                    solution_hi: "<p>5.(d)</p>\r\n<p><span style=\"font-family: Palanquin Dark;\">&#2351;&#2342;&#2367; a &#2324;&#2352; b &#2360;&#2361;&#2309;&#2349;&#2366;&#2332;&#2381;&#2351; &#2361;&#2376;&#2306; &#2340;&#2379;&nbsp;<math style=\"font-family: Verdana;\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>a</mi><mn>2</mn></msup><mo>&nbsp;</mo><mi>&#2324;&#2352;</mi><mo>&nbsp;</mo><msup><mi>b</mi><mn>2</mn></msup><mo>&nbsp;</mo></math> </span><span style=\"font-family: Palanquin Dark;\">&#2349;&#2368;</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">&#2360;&#2361;-&#2309;&#2349;&#2366;&#2332;&#2381;&#2351; &#2360;&#2306;&#2326;&#2381;&#2351;&#2366; &#2361;&#2376;&#2404; </span></p>\n",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "6",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Roboto;\">6.</span><span style=\"font-family: Roboto;\">The difference between two integers is 5 and their product is 500. Find the numbers.</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Roboto;\">6. </span><span style=\"font-family: Palanquin;\">&#2342;&#2379; &#2346;&#2370;&#2352;&#2381;&#2339;&#2366;&#2306;&#2325;&#2379;&#2306; &#2325;&#2375; &#2348;&#2368;&#2330; &#2325;&#2366; &#2309;&#2306;&#2340;&#2352; 5 &#2361;&#2376; &#2324;&#2352; &#2313;&#2344;&#2325;&#2366; &#2327;&#2369;&#2339;&#2344;&#2347;&#2354; 500 &#2361;&#2376;&#2404; &#2360;&#2306;&#2326;&#2381;&#2351;&#2366; &#2332;&#2381;&#2334;&#2366;&#2340; &#2325;&#2368;&#2332;&#2367;&#2319; &#2404;</span></p>\n",
                    options_en: ["<p>30, 25</p>\n", "<p>25, 20</p>\n", 
                                "<p>15, 20</p>\n", "<p>21, 26</p>\n"],
                    options_hi: ["<p>30, 25</p>\n", "<p>25, 20</p>\n",
                                "<p>15, 20</p>\n", "<p>21, 26</p>\n"],
                    solution_en: "<p>6.(b)</p>\r\n<p><span style=\"font-family: Roboto;\">Let the two integer x and y </span></p>\r\n<p><span style=\"font-family: Roboto;\">According to question , </span></p>\r\n<p><span style=\"font-family: Roboto;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>x</mi><mo>-</mo><mi>y</mi><mo>=</mo><mn>5</mn><mo>&nbsp;</mo></math>&nbsp;&hellip;&hellip;.</span><span style=\"font-family: Roboto;\">eq</span><span style=\"font-family: Roboto;\">.(1) </span></p>\r\n<p><span style=\"font-family: Roboto;\">xy = 500</span></p>\r\n<p><span style=\"font-family: Roboto;\">We know that , </span><span style=\"font-family: Roboto;\"> </span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mfenced><mrow><mi>x</mi><mo>+</mo><mi>y</mi></mrow></mfenced><mn>2</mn></msup><mo>=</mo><msup><mfenced><mrow><mi>x</mi><mo>-</mo><mi>y</mi></mrow></mfenced><mn>2</mn></msup><mo>+</mo><mn>4</mn><mi>x</mi><mi>y</mi></math></p>\r\n<p><span style=\"font-family: Roboto;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mfenced><mrow><mi>x</mi><mo>+</mo><mi>y</mi></mrow></mfenced><mn>2</mn></msup><mo>=</mo><msup><mfenced><mn>5</mn></mfenced><mn>2</mn></msup><mo>+</mo><mn>4</mn><mfenced><mn>500</mn></mfenced></math></span></p>\r\n<p><span style=\"font-family: Roboto;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>x</mi><mo>+</mo><mi>y</mi><mo>=</mo><msqrt><mn>2025</mn></msqrt></math>&nbsp;</span><span style=\"font-family: Roboto;\">= 45 &hellip;&hellip;.</span><span style=\"font-family: Roboto;\">eq</span><span style=\"font-family: Roboto;\">.(2) </span></p>\r\n<p><span style=\"font-family: Roboto;\">Solving the </span><span style=\"font-family: Roboto;\">eq</span><span style=\"font-family: Roboto;\">.(1) and </span><span style=\"font-family: Roboto;\">eq</span><span style=\"font-family: Roboto;\">.(2) , we get </span></p>\r\n<p><span style=\"font-family: Roboto;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>x</mi><mo>=</mo><mo>&nbsp;</mo><mfrac><mrow><mn>5</mn><mo>+</mo><mn>45</mn></mrow><mn>2</mn></mfrac></math>&nbsp;</span><span style=\"font-family: Roboto;\">= 25 and y =</span><span style=\"font-family: Roboto;\"> 20.</span></p>\n",
                    solution_hi: "<p>6.(b)</p>\r\n<p><span style=\"font-family: Palanquin Dark;\">&#2350;&#2366;&#2344;&#2366; &#2342;&#2379; &#2346;&#2370;&#2352;&#2381;&#2339;&#2366;&#2306;&#2325; x &#2324;&#2352; y &#2361;&#2376;&#2306;</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">&#2346;&#2381;&#2352;&#2358;&#2381;&#2344; &#2325;&#2375; &#2309;&#2344;&#2369;&#2360;&#2366;&#2352;,</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>x</mi><mo>-</mo><mi>y</mi><mo>=</mo><mn>5</mn><mo>&nbsp;</mo></math>&nbsp;&hellip;&hellip;.</span><span style=\"font-family: Palanquin Dark;\">&#2360;&#2350;&#2368;&#2325;&#2352;&#2339;</span><span style=\"font-family: Palanquin Dark;\">(1) </span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">xy = 500</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">&#2361;&#2350; &#2332;&#2366;&#2344;&#2340;&#2375; &#2361;&#2376;&#2306; ,</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mfenced><mrow><mi>x</mi><mo>+</mo><mi>y</mi></mrow></mfenced><mn>2</mn></msup><mo>=</mo><msup><mfenced><mrow><mi>x</mi><mo>-</mo><mi>y</mi></mrow></mfenced><mn>2</mn></msup><mo>+</mo><mn>4</mn><mi>x</mi><mi>y</mi></math></span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mfenced><mrow><mi>x</mi><mo>+</mo><mi>y</mi></mrow></mfenced><mn>2</mn></msup><mo>=</mo><msup><mfenced><mn>5</mn></mfenced><mn>2</mn></msup><mo>+</mo><mn>4</mn><mfenced><mn>500</mn></mfenced></math></span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>x</mi><mo>+</mo><mi>y</mi><mo>=</mo><msqrt><mn>2025</mn></msqrt></math>&nbsp;</span><span style=\"font-family: Palanquin Dark;\">= 45 &hellip;&hellip;.</span><span style=\"font-family: Palanquin Dark;\">&#2360;&#2350;&#2368;&#2325;&#2352;&#2339;</span><span style=\"font-family: Palanquin Dark;\">(2)</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">&#2360;&#2350;&#2368;&#2325;&#2352;&#2339; (1) &#2324;&#2352; &#2360;&#2350;&#2368;&#2325;&#2352;&#2339; (2) &#2325;&#2379; &#2361;&#2354; &#2325;&#2352;&#2344;&#2375; &#2346;&#2352;, &#2361;&#2350;&#2375;&#2306; &#2350;&#2367;&#2354;&#2340;&#2366; &#2361;&#2376;&nbsp;</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">x </span><span style=\"font-family: Palanquin Dark;\">=<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5</mn><mo>+</mo><mn>45</mn></mrow><mn>2</mn></mfrac><mo>=</mo></math>25 &#2324;&#2352;&nbsp;y =</span><span style=\"font-family: Palanquin Dark;\"> 20.</span></p>\n",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "7",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Roboto;\">7.</span><span style=\"font-family: Roboto;\"> Arrange the following fractions in ascending order.</span></p>\r\n<p><span style=\"font-family: Roboto;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>9</mn></mfrac><mo>,</mo><mfrac><mn>8</mn><mn>3</mn></mfrac><mo>,</mo><mfrac><mn>7</mn><mn>5</mn></mfrac><mo>,</mo><mfrac><mn>3</mn><mn>5</mn></mfrac><mo>,</mo><mfrac><mn>1</mn><mn>9</mn></mfrac></math></span></p>\n",
                    question_hi: "<p><span style=\"font-family: Roboto;\">7.</span><span style=\"font-family: Palanquin;\"> &#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340; &#2349;&#2367;&#2344;&#2381;&#2344;&#2379;&#2306; &#2325;&#2379; &#2310;&#2352;&#2379;&#2361;&#2368; &#2325;&#2381;&#2352;&#2350; &#2350;&#2375;&#2306; &#2357;&#2381;&#2351;&#2357;&#2360;&#2381;&#2341;&#2367;&#2340; &#2325;&#2368;&#2332;&#2367;<span style=\"font-weight: 400;\">&#2319;</span></span></p>\r\n<p><span style=\"font-family: Palanquin;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>9</mn></mfrac><mo>,</mo><mfrac><mn>8</mn><mn>3</mn></mfrac><mo>,</mo><mfrac><mn>7</mn><mn>5</mn></mfrac><mo>,</mo><mfrac><mn>3</mn><mn>5</mn></mfrac><mo>,</mo><mfrac><mn>1</mn><mn>9</mn></mfrac></math></span></p>\n",
                    options_en: ["<p><span style=\"font-family: Roboto;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>8</mn><mn>3</mn></mfrac><mo>,</mo><mfrac><mn>7</mn><mn>5</mn></mfrac><mo>,</mo><mfrac><mn>3</mn><mn>5</mn></mfrac><mo>,</mo><mfrac><mn>5</mn><mn>9</mn></mfrac><mo>,</mo><mfrac><mn>1</mn><mn>9</mn></mfrac></math></span></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>9</mn></mfrac><mo>,</mo><mfrac><mn>5</mn><mn>9</mn></mfrac><mo>,</mo><mfrac><mn>7</mn><mn>5</mn></mfrac><mo>,</mo><mfrac><mn>3</mn><mn>5</mn></mfrac><mo>,</mo><mfrac><mn>8</mn><mn>3</mn></mfrac></math></p>\n", 
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>9</mn></mfrac><mo>,</mo><mfrac><mn>5</mn><mn>9</mn></mfrac><mo>,</mo><mfrac><mn>3</mn><mn>5</mn></mfrac><mo>,</mo><mfrac><mn>7</mn><mn>5</mn></mfrac><mo>,</mo><mfrac><mn>8</mn><mn>3</mn></mfrac></math></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>9</mn></mfrac><mo>,</mo><mfrac><mn>7</mn><mn>5</mn></mfrac><mo>,</mo><mfrac><mn>3</mn><mn>5</mn></mfrac><mo>,</mo><mfrac><mn>1</mn><mn>9</mn></mfrac><mo>,</mo><mfrac><mn>8</mn><mn>3</mn></mfrac></math></p>\n"],
                    options_hi: ["<p><span style=\"font-family: Roboto;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>8</mn><mn>3</mn></mfrac><mo>,</mo><mfrac><mn>7</mn><mn>5</mn></mfrac><mo>,</mo><mfrac><mn>3</mn><mn>5</mn></mfrac><mo>,</mo><mfrac><mn>5</mn><mn>9</mn></mfrac><mo>,</mo><mfrac><mn>1</mn><mn>9</mn></mfrac></math></span></p>\n", "<p><span style=\"font-family: Roboto;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>9</mn></mfrac><mo>,</mo><mfrac><mn>5</mn><mn>9</mn></mfrac><mo>,</mo><mfrac><mn>7</mn><mn>5</mn></mfrac><mo>,</mo><mfrac><mn>3</mn><mn>5</mn></mfrac><mo>,</mo><mfrac><mn>8</mn><mn>3</mn></mfrac></math></span></p>\n",
                                "<p><span style=\"font-family: Roboto;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>9</mn></mfrac><mo>,</mo><mfrac><mn>5</mn><mn>9</mn></mfrac><mo>,</mo><mfrac><mn>3</mn><mn>5</mn></mfrac><mo>,</mo><mfrac><mn>7</mn><mn>5</mn></mfrac><mo>,</mo><mfrac><mn>8</mn><mn>3</mn></mfrac></math></span></p>\n", "<p><span style=\"font-family: Roboto;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>9</mn></mfrac><mo>,</mo><mfrac><mn>7</mn><mn>5</mn></mfrac><mo>,</mo><mfrac><mn>3</mn><mn>5</mn></mfrac><mo>,</mo><mfrac><mn>1</mn><mn>9</mn></mfrac><mo>,</mo><mfrac><mn>8</mn><mn>3</mn></mfrac></math></span></p>\n"],
                    solution_en: "<p>7.(c)</p>\r\n<p><span style=\"font-family: Roboto;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>9</mn></mfrac></math></span><span style=\"font-family: Roboto;\">= 0.55 ,<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>8</mn><mn>3</mn></mfrac></math></span><span style=\"font-family: Roboto;\">= 2.66 , <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>5</mn></mfrac></math></span><span style=\"font-family: Roboto;\"> = 1.4 ,<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>5</mn></mfrac></math> </span><span style=\"font-family: Roboto;\"> = 0.6 , <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>9</mn></mfrac></math></span><span style=\"font-family: Roboto;\"> = 0.11</span></p>\r\n<p><span style=\"font-family: Roboto;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>0</mn><mo>.</mo><mn>11</mn><mo>&lt;</mo><mn>0</mn><mo>.</mo><mn>55</mn><mo>&lt;</mo><mn>0</mn><mo>.</mo><mn>6</mn><mo>&lt;</mo><mn>1</mn><mo>.</mo><mn>4</mn><mo>&lt;</mo><mn>2</mn><mo>.</mo><mn>66</mn></math></span></p>\r\n<p><span style=\"font-family: Roboto;\">So , in ascending order =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>9</mn></mfrac><mo>,</mo><mfrac><mn>5</mn><mn>9</mn></mfrac><mo>,</mo><mfrac><mn>3</mn><mn>5</mn></mfrac><mo>,</mo><mfrac><mn>7</mn><mn>5</mn></mfrac><mo>,</mo><mfrac><mn>8</mn><mn>3</mn></mfrac></math> </span><span style=\"font-family: Roboto;\"> </span></p>\n",
                    solution_hi: "<p>7.(c)</p>\r\n<p><span style=\"font-family: Roboto;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>9</mn></mfrac></math></span><span style=\"font-family: Roboto;\">= 0.55 ,<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>8</mn><mn>3</mn></mfrac></math></span><span style=\"font-family: Roboto;\">= 2.66 , <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>5</mn></mfrac></math></span><span style=\"font-family: Roboto;\"> = 1.4 , <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>5</mn></mfrac></math></span><span style=\"font-family: Roboto;\"> = 0.6 , <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>9</mn></mfrac></math></span><span style=\"font-family: Roboto;\"> = 0.11</span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>0</mn><mo>.</mo><mn>11</mn><mo>&lt;</mo><mn>0</mn><mo>.</mo><mn>55</mn><mo>&lt;</mo><mn>0</mn><mo>.</mo><mn>6</mn><mo>&lt;</mo><mn>1</mn><mo>.</mo><mn>4</mn><mo>&lt;</mo><mn>2</mn><mo>.</mo><mn>66</mn></math></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">&#2340;&#2379;, &#2348;&#2338;&#2364;&#2340;&#2375; &#2325;&#2381;&#2352;&#2350; &#2350;&#2375;&#2306; =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>9</mn></mfrac><mo>,</mo><mfrac><mn>5</mn><mn>9</mn></mfrac><mo>,</mo><mfrac><mn>3</mn><mn>5</mn></mfrac><mo>,</mo><mfrac><mn>7</mn><mn>5</mn></mfrac><mo>,</mo><mfrac><mn>8</mn><mn>3</mn></mfrac></math> </span></p>\n",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "8",
                    section: "17",
                    question_en: "<p>8.<span style=\"font-family: Roboto;\"> Find the product of first 10 whole numbers.</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Roboto;\">8.</span><span style=\"font-family: Palanquin;\"> &#2346;&#2381;&#2352;&#2341;&#2350; 10 &#2346;&#2370;&#2352;&#2381;&#2339; &#2360;&#2306;&#2326;&#2381;&#2351;&#2366;&#2323;&#2306; &#2325;&#2366; &#2327;&#2369;&#2339;&#2344;&#2347;&#2354; &#2332;&#2381;&#2334;&#2366;&#2340; &#2325;&#2368;&#2332;&#2367;&#2319; &#2404;</span></p>\n",
                    options_en: ["<p>100</p>\n", "<p>0</p>\n", 
                                "<p>55</p>\n", "<p>101</p>\n"],
                    options_hi: ["<p>100</p>\n", "<p>0</p>\n",
                                "<p>55</p>\n", "<p>101</p>\n"],
                    solution_en: "<p>8.(b)</p>\r\n<p><span style=\"font-family: Roboto;\">First 10 whole number are&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfenced><mrow><mn>0</mn><mo>,</mo><mn>1</mn><mo>,</mo><mn>2</mn><mo>,</mo><mn>3</mn><mo>,</mo><mn>4</mn><mo>,</mo><mn>5</mn><mo>,</mo><mn>6</mn><mo>,</mo><mn>7</mn><mo>,</mo><mn>8</mn><mo>,</mo><mn>9</mn></mrow></mfenced></math></span></p>\r\n<p><span style=\"font-family: Roboto;\">Product of these number = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfenced><mrow><mn>0</mn><mo>&times;</mo><mn>1</mn><mo>&times;</mo><mn>2</mn><mo>&times;</mo><mn>3</mn><mo>&times;</mo><mn>4</mn><mo>&times;</mo><mn>5</mn><mo>&times;</mo><mn>6</mn><mo>&times;</mo><mn>7</mn><mo>&times;</mo><mn>8</mn><mo>&times;</mo><mn>9</mn></mrow></mfenced><mo>=</mo><mn>0</mn></math></span></p>\n",
                    solution_hi: "<p>8.(b)</p>\r\n<p><span style=\"font-family: Palanquin Dark;\">&#2346;&#2381;&#2352;&#2341;&#2350; 10 &#2346;&#2370;&#2352;&#2381;&#2339; &#2360;&#2306;&#2326;&#2381;&#2351;&#2366;&#2319;&#2305;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfenced><mrow><mn>0</mn><mo>,</mo><mn>1</mn><mo>,</mo><mn>2</mn><mo>,</mo><mn>3</mn><mo>,</mo><mn>4</mn><mo>,</mo><mn>5</mn><mo>,</mo><mn>6</mn><mo>,</mo><mn>7</mn><mo>,</mo><mn>8</mn><mo>,</mo><mn>9</mn></mrow></mfenced></math></span></p>\r\n<p><span style=\"font-family: Palanquin;\">10 &#2346;&#2370;&#2352;&#2381;&#2339; &#2360;&#2306;&#2326;&#2381;&#2351;&#2366;&#2323;&#2306; &#2325;&#2366; &#2327;&#2369;&#2339;&#2344;&#2347;&#2354;</span><span style=\"font-family: Palanquin;\"> </span><span style=\"font-family: Palanquin;\">=<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfenced><mrow><mn>0</mn><mo>&times;</mo><mn>1</mn><mo>&times;</mo><mn>2</mn><mo>&times;</mo><mn>3</mn><mo>&times;</mo><mn>4</mn><mo>&times;</mo><mn>5</mn><mo>&times;</mo><mn>6</mn><mo>&times;</mo><mn>7</mn><mo>&times;</mo><mn>8</mn><mo>&times;</mo><mn>9</mn></mrow></mfenced><mo>=</mo><mn>0</mn></math></span></p>\n",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "9",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Roboto;\">9.</span><span style=\"font-weight: 400;\">The product of two numbers is 192 and their sum is 28. Find the smallest of these two numbers</span></p>\n",
                    question_hi: "<p><strong>9.</strong><span style=\"font-weight: 400;\">&#2342;&#2379; &#2360;&#2306;&#2326;&#2381;&#2351;&#2366;&#2323;&#2306; &#2325;&#2366; &#2327;&#2369;&#2339;&#2344;&#2347;&#2354; 192 &#2361;&#2376; &#2324;&#2352; &#2313;&#2344;&#2325;&#2366; &#2351;&#2379;&#2327;&#2347;&#2354; 28 &#2361;&#2376;&#2404; &#2311;&#2344; &#2342;&#2379;&#2344;&#2379;&#2306; &#2360;&#2306;&#2326;&#2381;&#2351;&#2366;&#2323;&#2306; &#2350;&#2375;&#2306; &#2360;&#2375; &#2360;&#2348;&#2360;&#2375; &#2331;&#2379;&#2335;&#2368; &#2360;&#2306;&#2326;&#2381;&#2351;&#2366; &#2332;&#2381;&#2334;&#2366;&#2340; &#2325;&#2352;&#2375;&#2306;&#2404;</span></p>\n",
                    options_en: ["<p>16</p>\n", "<p>14</p>\n", 
                                "<p>12</p>\n", "<p>18</p>\n"],
                    options_hi: ["<p>16</p>\n", "<p>14</p>\n",
                                "<p>12</p>\n", "<p>18</p>\n"],
                    solution_en: "<p>9.(c)</p>\r\n<p><span style=\"font-family: Roboto;\">According to the question,</span></p>\r\n<p><span style=\"font-family: Roboto;\">Let the numbers be x and 28 - x</span></p>\r\n<p><span style=\"font-family: Roboto;\">Therefore x&times;(28 - x) = 192 </span></p>\r\n<p><span style=\"font-family: Roboto;\"> </span><span style=\"font-family: Roboto;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&rarr;</mo><msup><mi>x</mi><mn>2</mn></msup><mo>-</mo><mn>28</mn><mi>x</mi><mo>+</mo><mn>192</mn><mo>=</mo><mn>0</mn></math></span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&rarr;</mo><mo>(</mo><mi>x</mi><mo>-</mo><mn>12</mn><mo>)</mo><mo>(</mo><mi>x</mi><mo>-</mo><mn>16</mn><mo>)</mo><mo>=</mo><mn>0</mn><mo>&rarr;</mo><mi>x</mi><mo>=</mo><mn>12</mn><mo>,</mo><mn>16</mn></math></p>\r\n<p><span style=\"font-family: Roboto;\">So, the smallest number be<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&rarr;</mo></math></span><span style=\"font-family: Roboto;\">12</span></p>\n",
                    solution_hi: "<p>9.(c)</p>\r\n<p><span style=\"font-family: Palanquin Dark;\">&#2346;&#2381;&#2352;&#2358;&#2381;&#2344; &#2325;&#2375; &#2309;&#2344;&#2369;&#2360;&#2366;&#2352;,</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">&#2350;&#2366;&#2344;&#2366; &#2360;&#2306;&#2326;&#2381;&#2351;&#2366;&#2319;&#2305; x &#2324;&#2352; 28 - x &#2361;&#2376;&#2306;</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">&#2311;&#2360;&#2354;&#2367;&#2319; x&times;(28 - x) = 192 </span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&rarr;</mo><msup><mi>x</mi><mn>2</mn></msup><mo>-</mo><mn>28</mn><mi>x</mi><mo>+</mo><mn>192</mn></math></span><span style=\"font-family: Palanquin Dark;\"> = 0</span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&rarr;</mo><mfenced><mrow><mi>x</mi><mo>-</mo><mn>12</mn></mrow></mfenced></math>(x-16)=0</p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&rarr;</mo></math> <span style=\"font-family: Palanquin Dark;\"> x = 12,16</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">&#2309;&#2340;: &#2360;&#2348;&#2360;&#2375; &#2331;&#2379;&#2335;&#2368; &#2360;&#2306;&#2326;&#2381;&#2351;&#2366; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&rarr;</mo></math></span><span style=\"font-family: Palanquin Dark;\">12</span></p>\n",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "10",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Roboto;\">10. </span><span style=\"font-family: Roboto;\">Which of these is a perfect square?</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Roboto;\">10. </span><span style=\"font-family: Palanquin;\">&#2311;&#2344;&#2350;&#2375;&#2306; &#2360;&#2375; &#2325;&#2380;&#2344; &#2360;&#2366; &#2346;&#2370;&#2352;&#2381;&#2339; &#2357;&#2352;&#2381;&#2327; &#2361;&#2376;?</span></p>\n",
                    options_en: ["<p>9801</p>\n", "<p>9016</p>\n", 
                                "<p>9013</p>\n", "<p>9887</p>\n"],
                    options_hi: ["<p>9801</p>\n", "<p>9016</p>\n",
                                "<p>9013</p>\n", "<p>9887</p>\n"],
                    solution_en: "<p>10.(a)</p>\r\n<p>99 is the square root of 9801 so it is a perfect square.</p>\n",
                    solution_hi: "<p>10.(a)</p>\r\n<p>9801 &#2325;&#2366; &#2357;&#2352;&#2381;&#2327;&#2350;&#2370;&#2354; 99 &#2361;&#2376; &#2311;&#2360;&#2354;&#2367;&#2319; &#2351;&#2361; &#2319;&#2325; &#2346;&#2370;&#2352;&#2381;&#2339; &#2357;&#2352;&#2381;&#2327; &#2361;&#2376;&#2404;</p>\n",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "11",
                    section: "17",
                    question_en: "<p>11. <span style=\"font-family: Roboto;\"> Find the largest 4 digit number that is exactly divisible by 88.</span></p>\n",
                    question_hi: "<p>11. <span style=\"font-family: Palanquin;\">4 &#2309;&#2306;&#2325;&#2379;&#2306; &#2325;&#2368; &#2360;&#2348;&#2360;&#2375; &#2348;&#2396;&#2368; &#2360;&#2306;&#2326;&#2381;&#2351;&#2366; &#2332;&#2381;&#2334;&#2366;&#2340; &#2325;&#2368;&#2332;&#2367;&#2319; &#2332;&#2379; 88 &#2360;&#2375; &#2346;&#2370;&#2352;&#2381;&#2339;&#2340;&#2307; &#2357;&#2367;&#2349;&#2366;&#2332;&#2381;&#2351; &#2361;&#2379; &#2404;</span></p>\n",
                    options_en: ["<p>9944</p>\n", "<p>9844</p>\n", 
                                "<p>9868</p>\n", "<p>8894</p>\n"],
                    options_hi: ["<p>9944</p>\n", "<p>9844</p>\n",
                                "<p>9768</p>\n", "<p>8894</p>\n"],
                    solution_en: "<p>11<span style=\"font-family: Roboto;\">.(a)</span></p>\r\n<p><span style=\"font-family: Roboto;\">Largest 4-digit no. be 9999.</span></p>\r\n<p><span style=\"font-family: Roboto;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>9999</mn><mn>88</mn></mfrac></math>= 88&times;113+55<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&rArr;</mo></math></span><span style=\"font-family: Roboto;\">rem.(55)</span></p>\r\n<p><span style=\"font-family: Roboto;\">Required no. = 9999<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>-</mo></math></span><span style=\"font-family: Roboto;\">55 = 9944</span></p>\n",
                    solution_hi: "<p>11.<span style=\"font-family: Roboto;\">(a)</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">&#2360;&#2348;&#2360;&#2375; &#2348;&#2337;&#2364;&#2368; 4 &#2309;&#2306;&#2325;&#2379;&#2306; &#2325;&#2368; &#2360;&#2306;&#2326;&#2381;&#2351;&#2366; 9999 &#2361;&#2376;&#2404;</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>9999</mn><mn>88</mn></mfrac></math>= 88&times;113+55<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&rArr;</mo></math></span><span style=\"font-family: Palanquin Dark;\">&#2358;&#2375;&#2359; (55)</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">&#2310;&#2357;&#2358;&#2381;&#2351;&#2325; &#2360;&#2306;&#2326;&#2381;&#2351;&#2366; = 9999<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>-</mo></math></span><span style=\"font-family: Palanquin Dark;\">55 = 9944</span></p>\n",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "12",
                    section: "17",
                    question_en: "<p>12.<span style=\"font-family: Roboto;\"> Find the product of (a&sup2;) &times; (2<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>a</mi><mn>22</mn></msup></math></span><span style=\"font-family: Roboto;\">) &times; (4<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>a</mi><mn>23</mn></msup></math></span><span style=\"font-family: Roboto;\">).</span></p>\n",
                    question_hi: "<p>12.&nbsp;<span style=\"font-family: Roboto;\"> (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>a</mi><mn>2</mn></msup></math></span><span style=\"font-family: Roboto;\">) &times; (2<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>a</mi><mn>22</mn></msup></math></span><span style=\"font-family: Roboto;\">) &times; (4<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>a</mi><mn>23</mn></msup></math></span><span style=\"font-family: Palanquin;\">&nbsp;) &#2325;&#2366; &#2327;&#2369;&#2339;&#2344;&#2347;&#2354; &#2332;&#2381;&#2334;&#2366;&#2340; &#2325;&#2368;&#2332;&#2367;&#2319; |</span></p>\n",
                    options_en: ["<p>4<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>a</mi><mn>47</mn></msup></math></p>\n", "<p>8<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>a</mi><mn>47</mn></msup></math></p>\n", 
                                "<p>8<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>a</mi><mn>22</mn></msup></math></p>\n", "<p>4<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>a</mi><mn>46</mn></msup></math></p>\n"],
                    options_hi: ["<p>4<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>a</mi><mn>47</mn></msup></math></p>\n", "<p>8<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>a</mi><mn>47</mn></msup></math></p>\n",
                                "<p>8<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>a</mi><mn>22</mn></msup></math></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>4</mn><msup><mi>a</mi><mn>46</mn></msup></math></p>\n"],
                    solution_en: "<p>12.(b)</p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><msup><mi>a</mi><mn>2</mn></msup><mo>)</mo><mo>&times;</mo><mo>(</mo><mn>2</mn><msup><mi>a</mi><mn>22</mn></msup><mo>)</mo><mo>&times;</mo><mo>(</mo><mn>4</mn><msup><mi>a</mi><mn>23</mn></msup><mo>)</mo></math></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>=</mo><mn>8</mn><msup><mi>a</mi><mrow><mn>2</mn><mo>+</mo><mn>22</mn><mo>+</mo><mn>23</mn></mrow></msup><mspace linebreak=\"newline\"></mspace><mo>=</mo><mn>8</mn><msup><mi>a</mi><mn>47</mn></msup></math></p>\n",
                    solution_hi: "<p>12.(b)</p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><msup><mi>a</mi><mn>2</mn></msup><mo>)</mo><mo>&times;</mo><mo>(</mo><mn>2</mn><msup><mi>a</mi><mn>22</mn></msup><mo>)</mo><mo>&times;</mo><mo>(</mo><mn>4</mn><msup><mi>a</mi><mn>23</mn></msup><mo>)</mo></math></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>=</mo><mn>8</mn><msup><mi>a</mi><mrow><mn>2</mn><mo>+</mo><mn>22</mn><mo>+</mo><mn>23</mn></mrow></msup><mspace linebreak=\"newline\"></mspace><mo>=</mo><mn>8</mn><msup><mi>a</mi><mn>47</mn></msup></math></p>\n",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "13",
                    section: "17",
                    question_en: "<p>13.<span style=\"font-family: Roboto;\"> Which of the following numbers is exactly divisible by 4?</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Roboto;\">13.</span><span style=\"font-family: Palanquin;\"> &#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340; &#2350;&#2375;&#2306; &#2360;&#2375; &#2325;&#2380;&#2344; &#2360;&#2368; &#2360;&#2306;&#2326;&#2381;&#2351;&#2366; 4 &#2360;&#2375; &#2346;&#2370;&#2352;&#2381;&#2339;&#2340;&#2307; &#2357;&#2367;&#2349;&#2366;&#2332;&#2381;&#2351; &#2361;&#2376;?</span></p>\n",
                    options_en: ["<p>10010</p>\n", "<p>10002</p>\n", 
                                "<p>10012</p>\n", "<p>10006</p>\n"],
                    options_hi: ["<p>10010</p>\n", "<p>10002</p>\n",
                                "<p>10012</p>\n", "<p>10006</p>\n"],
                    solution_en: "<p>13.(c)</p>\r\n<p><span style=\"font-weight: 400;\">The last two digits of 10012 are divisible by 4. Hence 10012 will be exactly divisible by 4.</span></p>\n",
                    solution_hi: "<p>13.(c)</p>\r\n<p><span style=\"font-weight: 400;\">10012 &#2325;&#2375; &#2309;&#2306;&#2340;&#2367;&#2350; &#2342;&#2379; &#2309;&#2306;&#2325; 4 &#2360;&#2375; &#2357;&#2367;&#2349;&#2366;&#2332;&#2381;&#2351; &#2361;&#2376;&#2306;&#2404; &#2311;&#2360;&#2354;&#2367;&#2319; 10012, 4&nbsp; &#2360;&#2375; &#2346;&#2370;&#2352;&#2381;&#2339;&#2340;&#2307; &#2357;&#2367;&#2349;&#2366;&#2332;&#2381;&#2351; &#2361;&#2379;&#2327;&#2368;&#2404;</span></p>\n",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "14",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Roboto;\">14.</span><span style=\"font-family: Roboto;\"> Find the representation of prime factors of 240.</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Roboto;\">14.</span><span style=\"font-family: Palanquin;\"> 240 &#2325;&#2375; &#2309;&#2349;&#2366;&#2332;&#2381;&#2351; &#2327;&#2369;&#2339;&#2339;&#2326;&#2339;&#2381;&#2337;&#2379;&#2306; &#2325;&#2366; &#2344;&#2367;&#2352;&#2370;&#2346;&#2339; &#2325;&#2368;&#2332;&#2367;&#2319; &#2404;</span></p>\n",
                    options_en: ["<p>2 &times; 2 &times; 2 &times; 3 &times; 5</p>\n", "<p>2 &times; 2 &times; 2 &times; 2 &times; 5</p>\n", 
                                "<p>2 &times; 2 &times; 3 &times; 3 &times; 5</p>\n", "<p>2 &times; 2 &times; 2 &times; 2 &times; 3 &times; 5</p>\n"],
                    options_hi: ["<p>2 &times; 2 &times; 2 &times; 3 &times; 5</p>\n", "<p>2 &times; 2 &times; 2 &times; 2 &times; 5</p>\n",
                                "<p>2 &times; 2 &times; 3 &times; 3 &times; 5</p>\n", "<p>2 &times; 2 &times; 2 &times; 2 &times; 3 &times; 5</p>\n"],
                    solution_en: "<p>14.(d)</p>\r\n<p><span style=\"font-family: Roboto;\">prime factors of 240</span><span style=\"font-family: Roboto;\"> = 2 &times; 2 &times; 2 &times; 3 &times; 5 &times; 2</span></p>\n",
                    solution_hi: "<p>14.(d)</p>\r\n<p><span style=\"font-family: Palanquin;\"> 240 &#2325;&#2375; &#2309;&#2349;&#2366;&#2332;&#2381;&#2351; &#2327;&#2369;&#2339;&#2339;&#2326;&#2339;&#2381;&#2337; </span><span style=\"font-family: Palanquin;\">= 2 &times; 2 &times; 2 &times; 3 &times; 5 &times; 2</span></p>\n",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "15",
                    section: "misc",
                    question_en: "<p><span style=\"font-family: Roboto;\">15. <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><mo>&nbsp;</mo><msup><mn>2</mn><mn>25</mn></msup><mo>+</mo><msup><mn>2</mn><mn>26</mn></msup><mo>+</mo><msup><mn>2</mn><mn>27</mn></msup><mo>+</mo><msup><mn>2</mn><mn>28</mn></msup><mo>&nbsp;</mo><mo>)</mo><mo>&nbsp;</mo></math></span><span style=\"font-family: Roboto;\">is a multiple of which of the following numbers?</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Roboto;\">15. </span><span style=\"font-family: Roboto;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><mo>&nbsp;</mo><msup><mn>2</mn><mn>25</mn></msup><mo>+</mo><msup><mn>2</mn><mn>26</mn></msup><mo>+</mo><msup><mn>2</mn><mn>27</mn></msup><mo>+</mo><msup><mn>2</mn><mn>28</mn></msup><mo>&nbsp;</mo><mo>)</mo><mo>&nbsp;</mo></math></span><span style=\"font-family: Palanquin;\">&nbsp;&#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340; &#2350;&#2375;&#2306; &#2360;&#2375; &#2325;&#2367;&#2360; &#2360;&#2306;&#2326;&#2381;&#2351;&#2366; &#2325;&#2366; &#2327;&#2369;&#2339;&#2332; &#2361;&#2376;?</span></p>\n",
                    options_en: ["<p>7</p>\n", "<p>9</p>\n", 
                                "<p>11</p>\n", "<p>15</p>\n"],
                    options_hi: ["<p>7</p>\n", "<p>9</p>\n",
                                "<p>11</p>\n", "<p>15</p>\n"],
                    solution_en: "<p>15.(d)</p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><mo>&nbsp;</mo><msup><mn>2</mn><mn>25</mn></msup><mo>+</mo><msup><mn>2</mn><mn>26</mn></msup><mo>+</mo><msup><mn>2</mn><mn>27</mn></msup><mo>+</mo><msup><mn>2</mn><mn>28</mn></msup><mo>&nbsp;</mo><mo>)</mo><mo>&nbsp;</mo></math></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&rArr;</mo><msup><mn>2</mn><mn>25</mn></msup><mo>(</mo><mn>1</mn><mo>+</mo><msup><mn>2</mn><mn>1</mn></msup><mo>+</mo><msup><mn>2</mn><mn>2</mn></msup><mo>+</mo><msup><mn>2</mn><mn>3</mn></msup><mo>)</mo></math></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&rArr;</mo><msup><mn>2</mn><mn>25</mn></msup><mo>(</mo><mn>1</mn><mo>+</mo><mn>2</mn><mo>+</mo><mn>4</mn><mo>+</mo><mn>8</mn><mo>)</mo><mspace linebreak=\"newline\"></mspace><mo>&rArr;</mo><msup><mn>2</mn><mn>25</mn></msup><mo>(</mo><mn>15</mn><mo>)</mo><mo>&nbsp;</mo></math></p>\r\n<p><span style=\"font-family: Roboto;\">Therefore,<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><mo>&nbsp;</mo><msup><mn>2</mn><mn>25</mn></msup><mo>+</mo><msup><mn>2</mn><mn>26</mn></msup><mo>+</mo><msup><mn>2</mn><mn>27</mn></msup><mo>+</mo><msup><mn>2</mn><mn>28</mn></msup><mo>&nbsp;</mo><mo>)</mo><mo>&nbsp;</mo></math></span><span style=\"font-family: Roboto;\">is the multiple of 15.</span></p>\n",
                    solution_hi: "<p>15.(d)</p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><mo>&nbsp;</mo><msup><mn>2</mn><mn>25</mn></msup><mo>+</mo><msup><mn>2</mn><mn>26</mn></msup><mo>+</mo><msup><mn>2</mn><mn>27</mn></msup><mo>+</mo><msup><mn>2</mn><mn>28</mn></msup><mo>&nbsp;</mo><mo>)</mo><mo>&nbsp;</mo></math></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&rArr;</mo><msup><mn>2</mn><mn>25</mn></msup><mo>(</mo><mn>1</mn><mo>+</mo><msup><mn>2</mn><mn>1</mn></msup><mo>+</mo><msup><mn>2</mn><mn>2</mn></msup><mo>+</mo><msup><mn>2</mn><mn>3</mn></msup><mo>)</mo></math></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&rArr;</mo><msup><mn>2</mn><mn>25</mn></msup><mo>(</mo><mn>1</mn><mo>+</mo><mn>2</mn><mo>+</mo><mn>4</mn><mo>+</mo><mn>8</mn><mo>)</mo><mspace linebreak=\"newline\"></mspace><mo>&rArr;</mo><msup><mn>2</mn><mn>25</mn></msup><mo>(</mo><mn>15</mn><mo>)</mo><mo>&nbsp;</mo></math></p>\r\n<p><span style=\"font-family: Palanquin;\">&#2309;&#2340;&#2307;, <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><mo>&nbsp;</mo><msup><mn>2</mn><mn>25</mn></msup><mo>+</mo><msup><mn>2</mn><mn>26</mn></msup><mo>+</mo><msup><mn>2</mn><mn>27</mn></msup><mo>+</mo><msup><mn>2</mn><mn>28</mn></msup><mo>&nbsp;</mo><mo>)</mo></math>,</span><span style=\"font-family: Palanquin;\">15 &#2325;&#2366; &#2327;&#2369;&#2339;&#2325; &#2361;&#2376;&#2404;</span></p>\n",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>