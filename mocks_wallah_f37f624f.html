<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">8:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 25</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">25</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 8 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["18"] = {
                name: "General Awareness",
                start: 0,
                end: 23
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="18">General Awareness</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 24,
                end: 24
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "18",
                    question_en: "<p>1. Pandit Omkar Nath Thakur was a student of ____________.</p>",
                    question_hi: "<p>1. पंडित ओंकार नाथ ठाकुर _________के शिष्&zwj;य थे।</p>",
                    options_en: ["<p>Ustad Abdul Karim Khan</p>", "<p>Pandit Vishnu Digambar Paluskar</p>", 
                                "<p>Pandit Gopal Shankar Mishra</p>", "<p>Pandit Vishnu Narayan Bhatkhande</p>"],
                    options_hi: ["<p>उस्ताद अब्दुल करीम खान</p>", "<p>पंडित विष्णु दिगंबर पलुस्कर</p>",
                                "<p>पंडित गोपाल शंकर मिश्र</p>", "<p>पंडित विष्णु नारायण भातखंडे</p>"],
                    solution_en: "<p>1.(b) <strong>Pandit Vishnu Digambar Paluskar.</strong> He was a Hindustani musician, known for singing the original version of the bhajan \"Raghupati Raghava Raja Ram\" and founded the Gandharva Mahavidyalaya in 1901. Pandit Omkarnath Thakur, a noted music teacher, musicologist, and Hindustani classical singer, was associated with the Gwalior Gharana, the oldest Khayal Gayaki style.</p>",
                    solution_hi: "<p>1.(b) <strong>पंडित विष्णु दिगंबर पलुस्कर।</strong> वे एक हिंदुस्तानी संगीतकार थे, जिन्हें \"रघुपति राघव राजा राम\" भजन के मूल संस्करण के गायन के लिए जाना जाता था और उन्होंने 1901 में गंधर्व महाविद्यालय की स्थापना की थी। पंडित ओंकार नाथ ठाकुर, एक प्रसिद्ध संगीत शिक्षक, संगीतज्ञ और हिंदुस्तानी शास्त्रीय गायक थे, जिनका संबंध ग्वालियर घराने से था, जो सबसे पुरानी ख्याल गायकी शैली थी।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "18",
                    question_en: "<p>2. As per a report of the Ministry of Labour and Employment, what was the rate of unemployment in Maharashtra for the year 2021-22?</p>",
                    question_hi: "<p>2. श्रम एवं रोजगार मंत्रालय की एक रिपोर्ट के अनुसार, वर्ष 2021-22 में महाराष्ट्र में बेरोजगारी दर कितनी थी?</p>",
                    options_en: ["<p>3.5%</p>", "<p>3.01%</p>", 
                                "<p>4.2%</p>", "<p>4.4%</p>"],
                    options_hi: ["<p>3.5%</p>", "<p>3.01%</p>",
                                "<p>4.2%</p>", "<p>4.4%</p>"],
                    solution_en: "<p>2.(a) <strong>3.5%.</strong> According to the Annual Periodic Labour Force Survey, the unemployment rate for people aged 15 and above was 4.2% in 2020-21, 4.1% in 2021-22, and 3.2% in 2022-23. In 2022-23, State/UT-wise unemployment rates included Andhra Pradesh (4.1%), Delhi (1.9%), Bihar (3.9%), Uttar Pradesh (2.4%), Gujarat (1.7%), Karnataka (2.4%), and Odisha (3.9%).</p>",
                    solution_hi: "<p>2.(a) <strong>3.5%.</strong> वार्षिक आवधिक श्रम बल सर्वेक्षण के अनुसार, 15 वर्ष और उससे अधिक आयु के लोगों के लिए बेरोजगारी दर 2020-21 में 4.2%, 2021-22 में 4.1% और 2022-23 में 3.2% थी। 2022-23 में, राज्य/केंद्र शासित प्रदेश-वार बेरोजगारी दरों में आंध्र प्रदेश (4.1%), दिल्ली (1.9%), बिहार (3.9%), उत्तर प्रदेश (2.4%), गुजरात (1.7%), कर्नाटक (2.4%) और ओडिशा (3.9%) शामिल थे।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "18",
                    question_en: "<p>3. Navratri festival is dedicated to which of the following Goddesses?</p>",
                    question_hi: "<p>3. नवरात्रि उत्सव निम्नलिखित में से किस देवी को समर्पित है?</p>",
                    options_en: ["<p>Jawala</p>", "<p>Laxmi</p>", 
                                "<p>Durga</p>", "<p>Saraswati</p>"],
                    options_hi: ["<p>ज्वाला</p>", "<p>लक्ष्मी</p>",
                                "<p>दुर्गा</p>", "<p>सरस्वती</p>"],
                    solution_en: "<p>3.(c) <strong>Durga.</strong> Navratri, meaning &lsquo;nine nights&rsquo; in Sanskrit, is a festival where people fast and pray to the nine forms of Goddess Durga, an incarnation of Parvati, who defeated Mahishasura. The two popular folk dances of Gujarat, the Garba and Dandia are performed by both men and women during this occasion on every night.</p>",
                    solution_hi: "<p>3.(c) <strong>दुर्गा।</strong> नवरात्रि, जिसका संस्कृत में अर्थ है &lsquo;नौ रातें&rsquo;, एक ऐसा त्योहार है जिसमें लोग उपवास करते हैं और देवी दुर्गा के नौ रूपों की पूजा करते हैं, जो पार्वती का अवतार हैं, जिन्होंने महिषासुर को पराजित किया था। गुजरात के दो लोकप्रिय लोक नृत्य, गरबा और डांडिया, इस अवसर पर प्रत्येक रात पुरुषों और महिलाओं दोनों द्वारा किए जाते हैं।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "18",
                    question_en: "4.  Khudai Khidmatgar, a voluntary organisation, was established by which of the following leaders of India?",
                    question_hi: "4.  भारत के निम्नलिखित में से किस नेता द्वारा एक स्वैच्छिक संगठन - खुदाई खिदमतगार स्थापित किया गया था?",
                    options_en: [" Muhammad Ali Jinnah", " Khan Abdul Ghaffar Khan", 
                                " Hakim Ajmal Khan", " Maulana Muhammad Ali "],
                    options_hi: ["  मुहम्मद अली जिन्ना", "  खान अब्दुल गफ्फार खान",
                                "  हकीम अज़मल खान", " मौलाना मुहम्मद अली"],
                    solution_en: "<p>4.(b) <strong>Khan Abdul Ghaffar Khan, </strong>also known as Badshah Khan or \"Frontier Gandhi,\" founded the Khudai Khidmatgar (\"Servants of God\") movement in 1929. This group of Pashtuns used non-violent methods to fight against British rule. Maulana Muhammad Ali was a prominent Indian Muslim leader, journalist, and scholar who played a key role in the Khilafat movement. Muhammad Ali Jinnah was the leader of the All-India Muslim League and later became the Governor-General of Pakistan. He is honored with the title \'Quaid-i-Azam.\'</p>",
                    solution_hi: "<p>4.(b) <strong>खान अब्दुल गफ्फार खान</strong>, जिन्हें बादशाह खान या \"सीमांत गांधी\" के नाम से भी जाना जाता है, इन्होंने 1929 में खुदाई खिदमतगार (\"ईश्वर के सेवक\") आंदोलन की शुरुआत की थी। पश्तूनों के इस समूह ने ब्रिटिश शासन के खिलाफ लड़ने के लिए अहिंसक तरीकों का प्रयोग किया था। मौलाना मुहम्मद अली एक प्रमुख भारतीय मुस्लिम नेता, पत्रकार और विद्वान थे जिन्होंने खिलाफत आंदोलन में महत्वपूर्ण भूमिका निभाई थी। मुहम्मद अली जिन्ना अखिल भारतीय मुस्लिम लीग के नेता थे और बाद में पाकिस्तान के गवर्नर-जनरल बने। उन्हें \'कायदे-ए-आजम\' की उपाधि से सम्मानित किया गया था।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "18",
                    question_en: "<p>5. Vijender Singh has represented India in Olympics for how many times?</p>",
                    question_hi: "<p>5. विजेंदर सिंह ने कितनी बार ओलंपिक खेलों में भारत का प्रतिनिधित्व किया है?</p>",
                    options_en: ["<p>4</p>", "<p>5</p>", 
                                "<p>3</p>", "<p>2</p>"],
                    options_hi: ["<p>4</p>", "<p>5</p>",
                                "<p>3</p>", "<p>2</p>"],
                    solution_en: "<p>5.(c) <strong>3</strong>. Vijender Singh, an Indian professional boxer, made history as the first Indian boxer to win an Olympic medal, earning bronze at the 2008 Beijing Olympics. He also won bronze at the 2009 World Championships and 2010 Commonwealth Games, along with silver at the 2006 and 2014 Commonwealth Games, all in the middleweight category.</p>",
                    solution_hi: "<p>5.(c) <strong>3</strong>. विजेंदर सिंह एक भारतीय पेशेवर मुक्केबाज हैं, जिन्होंने 2008 बीजिंग ओलंपिक में कांस्य पदक जीतकर ओलंपिक पदक जीतने वाले पहले भारतीय मुक्केबाज के रूप में इतिहास रचा। उन्होंने 2009 विश्व चैंपियनशिप और 2010 राष्ट्रमंडल खेलों में कांस्य पदक जीता, तथा 2006 और 2014 राष्ट्रमंडल खेलों में मध्यम भार वर्ग में रजत पदक जीता।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "18",
                    question_en: "<p>6. In which sport did India win its first individual Olympic medal?</p>",
                    question_hi: "<p>6. भारत ने अपना पहला व्यक्तिगत ओलंपिक पदक (individual Olympic medal) किस खेल में जीता था?</p>",
                    options_en: ["<p>Swimming</p>", "<p>Judo</p>", 
                                "<p>Wrestling</p>", "<p>Shooting</p>"],
                    options_hi: ["<p>तैराकी</p>", "<p>जूडो</p>",
                                "<p>कुश्ती</p>", "<p>निशानेबाजी</p>"],
                    solution_en: "<p>6.(c) <strong>Wrestling.</strong> K.D. Jadhav was India&rsquo;s first athlete from independent India to win an individual Olympic medal, securing bronze in wrestling at the 1952 Helsinki Olympics. India sent its first team to the Summer Olympic Games in 1920 and won its first gold medal in 1928 in field hockey (team event). The first Indian to win an individual gold medal at the Olympic Games was Abhinav Bindra.</p>",
                    solution_hi: "<p>6.(c) <strong>कुश्ती।</strong> के.डी. जाधव स्वतंत्र भारत के प्रथम एथलीट थे जिन्होंने व्यक्तिगत ओलंपिक पदक जीता था, उन्होंने 1952 के हेलसिंकी ओलंपिक में कुश्ती में कांस्य पदक जीता था। भारत ने 1920 में ग्रीष्मकालीन ओलंपिक खेलों में अपनी पहली टीम भेजी और 1928 में फील्ड हॉकी (टीम स्पर्धा) में अपना प्रथम स्वर्ण पदक जीता था। ओलंपिक खेलों में व्यक्तिगत स्वर्ण पदक जीतने वाले प्रथम भारतीय अभिनव बिंद्रा थे।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "18",
                    question_en: "<p>7. Who among the following was one of the recipients of the inaugural &lsquo;Prof CR Rao Centenary Gold Medal&rsquo; award in 2021?</p>",
                    question_hi: "<p>7. निम्नलिखित में से कौन 2021 में प्रथम \'प्रोफेसर सी.आर. राव शताब्दी स्वर्ण पदक\' पुरस्कार प्राप्त करने वालों में से एक थे?</p>",
                    options_en: ["<p>Raghuram Rajan</p>", "<p>Surjit Bhalla</p>", 
                                "<p>Amartya Sen</p>", "<p>Jagdish Bhagwati</p>"],
                    options_hi: ["<p>रघुराम राजन</p>", "<p>सुरजीत भल्ला</p>",
                                "<p>अमर्त्य सेन</p>", "<p>जगदीश भगवती</p>"],
                    solution_en: "<p>7.(d) <strong>Jagdish Bhagwati.</strong> He is an Indian-born naturalized American economist and one of the most influential trade theorists of his generation. The Prof. C.R. Rao Centenary Gold Medal (CGM) is awarded every two years by the Indian Econometric Society (TIES) Trust to an Indian or Indian-origin scholar for their lifetime contributions to the theoretical and applied aspects of quantitative economics and official statistics.</p>",
                    solution_hi: "<p>7.(d) <strong>जगदीश भगवती।</strong> वे भारतीय मूल के अमेरिकी अर्थशास्त्री हैं और अपनी पीढ़ी के सबसे प्रभावशाली व्यापार सिद्धांतकारों में से एक हैं। प्रो. सी.आर. राव शताब्दी स्वर्ण पदक (CGM) प्रत्येक दो वर्ष में भारतीय अर्थमिति सोसायटी (TIES) ट्रस्ट द्वारा किसी भारतीय या भारतीय मूल के विद्वान को मात्रात्मक अर्थशास्त्र और आधिकारिक सांख्यिकी के सैद्धांतिक और व्यावहारिक पहलुओं में उनके आजीवन योगदान के लिए प्रदान किया जाता है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "18",
                    question_en: "<p>8. Who among the following classical dancers is NOT related to Odissi?</p>",
                    question_hi: "<p>8. निम्नलिखित शास्त्रीय नर्तकियों में से कौन ओडिसी से संबंधित नहीं है?</p>",
                    options_en: ["<p>Geetanjali Lal</p>", "<p>Mayadhar Raut</p>", 
                                "<p>Kiran Segal</p>", "<p>Kumkum Mohanty</p>"],
                    options_hi: ["<p>गीतांजलि लाल</p>", "<p>मायाधर राऊत</p>",
                                "<p>किरण सहगल</p>", "<p>कुमकुम मोहंती</p>"],
                    solution_en: "<p>8.(a) <strong>Geetanjali Lal.</strong> She is an Indian Kathak dancer and choreographer. Indian classical dance (Shastriya Nritya) includes various regionally specific traditions rooted in Hindu musical theatre, tracing back to the Sanskrit text Natya Shastra. Odissi, also known as Orissi in older literature, is a major classical dance that originated in the temples of Odisha, an eastern coastal state of India. Notable Odissi dancers include Kelucharan Mohapatra, Deba Prasad Das, Madhavi Mudgal, Sonal Mansingh, Pankaj Charan Das, Aruna Mohanty, and Ileana Citaristi.</p>",
                    solution_hi: "<p>8.(a) <strong>गीतांजलि लाल।</strong> वह एक भारतीय कथक नर्तकी और कोरियोग्राफर हैं। भारतीय शास्त्रीय नृत्य में हिंदू संगीत रंगमंच में निहित विभिन्न क्षेत्रीय विशिष्ट परंपराएँ शामिल हैं, जिनका संबंध संस्कृत ग्रंथ नाट्य शास्त्र से हैं। ओडिसी, जिसे पुराने साहित्य में ओरिस्सी के नाम से भी जाना जाता है, एक प्रमुख शास्त्रीय नृत्य है जिसकी उत्पत्ति भारत के पूर्वी तटीय राज्य ओडिशा के मंदिरों में हुई थी। उल्लेखनीय ओडिसी नर्तकों में केलुचरण महापात्रा, देबा प्रसाद दास, माधवी मुद्गल, सोनल मानसिंह, पंकज चरण दास, अरुणा मोहंती और इलियाना सिटारिस्टी शामिल हैं।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "18",
                    question_en: "<p>9. Which of the following Articles states that the Prime Minister should be appointed by the President?</p>",
                    question_hi: "<p>9. निम्नलिखित में से कौन-सा अनुच्छेद बताता है कि प्रधानमंत्री को राष्ट्रपति द्वारा नियुक्त किया जाना चाहिए?</p>",
                    options_en: ["<p>Article 71</p>", "<p>Article 66</p>", 
                                "<p>Article 75</p>", "<p>Article 62</p>"],
                    options_hi: ["<p>अनुच्छेद 71</p>", "<p>अनुच्छेद 66</p>",
                                "<p>अनुच्छेद 75</p>", "<p>अनुच्छेद 62</p>"],
                    solution_en: "<p>9.(c) <strong>Article 75.</strong> Article 62 - Time of holding the election to fill the vacancy in the office of President and the term of office of the person elected to fill the casual vacancy. Article 66 - Election of Vice-President. Article 71 - Matters relating to, or connected with, the election of a President or Vice-President.</p>",
                    solution_hi: "<p>9.(a) <strong>अनुच्छेद 75.</strong> अनुच्छेद 62 - राष्ट्रपति के पद की रिक्ति को भरने के लिए चुनाव कराने का समय और आकस्मिक रिक्ति को पूर्ण करने के लिए निर्वाचित व्यक्ति की पदावधि। अनुच्छेद 66 - उपराष्ट्रपति का निर्वाचन। अनुच्छेद 71 - राष्ट्रपति या उपराष्ट्रपति के निर्वाचन से संबंधित या उससे जुड़े मामले।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "18",
                    question_en: "<p>10. The oath of office to the Vice-President is administered by the ________.</p>",
                    question_hi: "<p>10.उपराष्ट्रपति को पद की शपथ ________ द्वारा दिलाई जाती है।</p>",
                    options_en: ["<p>Chief Justice of India</p>", "<p>President</p>", 
                                "<p>CAG</p>", "<p>Prime Minister</p>"],
                    options_hi: ["<p>भारत के मुख्य न्यायाधीश</p>", "<p>राष्ट्रपति</p>",
                                "<p>सी.ए.जी.</p>", "<p>प्रधानमंत्री</p>"],
                    solution_en: "<p>10.(b) <strong>President</strong> - The head of state of the Republic of India. The President is the formal head of the executive, legislature, and judiciary of India and is also the commander-in-chief of the Indian Armed Forces. Article 52 : There shall be a President of India. The Vice-President of India holds the second-highest constitutional office in the country, serving a term of five years. Article 63 : There shall be a Vice-President of India. Article 69 : The oath of office for the Vice-President is administered by the President.</p>",
                    solution_hi: "<p>10.(b) <strong>राष्ट्रपति</strong> - भारत गणराज्य के राष्ट्राध्यक्ष। राष्ट्रपति भारत की कार्यपालिका, विधायिका और न्यायपालिका का औपचारिक प्रमुख होता है और भारतीय सशस्त्र बलों का कमांडर-इन-चीफ भी होता है। अनुच्छेद 52 : भारत का एक राष्ट्रपति होगा। भारत का उप-राष्ट्रपति देश का दूसरा सबसे बड़ा संवैधानिक पद है, जिसका कार्यकाल पाँच वर्ष का होता है। अनुच्छेद 63 : भारत का एक उप-राष्ट्रपति होगा। अनुच्छेद 69 : उप-राष्ट्रपति को पद की शपथ राष्ट्रपति द्वारा दिलायी जाती है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "18",
                    question_en: "<p>11. What was the name of the wind measuring instrument invented for the first time in 1450?</p>",
                    question_hi: "<p>11. 1450 में पहली बार आविष्कृत पवन मापक यंत्र का क्या नाम था?</p>",
                    options_en: ["<p>Transmissometer</p>", "<p>Dropsonde</p>", 
                                "<p>Anemometer</p>", "<p>Ceiling Projector</p>"],
                    options_hi: ["<p>संचरणलेखित्र (Transmissometer)</p>", "<p>अधःपाती सोंड (Dropsonde)</p>",
                                "<p>पवनवेगमापी (Anemometer)</p>", "<p>सीलिंग प्रोजेक्टर (Ceiling Projector)</p>"],
                    solution_en: "<p>11.(c) <strong>Anemometer.</strong> It is an instrument that measures wind speed and wind pressure. A transmissometer measures the attenuation of light as it passes through a fluid, such as water or the atmosphere. A dropsonde is a weather device dropped from an aircraft at specified altitudes, falling to Earth due to gravity. A ceiling projector is a portable device used to project images onto a ceiling.</p>",
                    solution_hi: "<p>11.(c) <strong>पवनवेगमापी</strong> (Anemometer) । यह एक ऐसा उपकरण है जो वायु की गति और वायु के दाब को मापता है। ट्रांसमिसोमीटर प्रकाश के क्षीणन को मापता है जब यह किसी द्रव पदार्थ, जैसे जल या वायुमंडल से होकर गुजरता है। ड्रॉपसॉन्ड एक मौसम संबंधी उपकरण है जिसे विमान से निर्दिष्ट ऊंचाई पर गिराया जाता है, जो गुरुत्वाकर्षण के कारण पृथ्वी पर गिरता है। सीलिंग प्रोजेक्टर एक पोर्टेबल डिवाइस है जिसका उपयोग छत पर छवियों को प्रोजेक्ट करने के लिए किया जाता है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "18",
                    question_en: "<p>12. Due to which of the following events did Rabindranath Tagore return the title of &lsquo;Knighthood&rsquo; to the British Government while expressing his anguish ?</p>",
                    question_hi: "<p>12. निम्नलिखित में से किस घटना के कारण रवीन्द्रनाथ टैगोर ने अपनी पीड़ा व्यक्त करते हुए ब्रिटिश सरकार को \'नाइटहुड\' की उपाधि लौटा दी थी?</p>",
                    options_en: ["<p>Charan Paduka Incident</p>", "<p>Partition of Bengal</p>", 
                                "<p>Jallianwala Bagh massacre</p>", "<p>Chauri Chaura Incident</p>"],
                    options_hi: ["<p>चरण पादुका प्रसंग</p>", "<p>बंगाल का विभाजन</p>",
                                "<p>जलियांवाला बाग हत्याकांड</p>", "<p>चौरी चौरा कांड</p>"],
                    solution_en: "<p>12.(c) <strong>Jallianwala Bagh massacre.</strong> It was a tragic event that occurred on April 13, 1919, in Amritsar during British colonial rule. British troops, under the command of General Reginald Dyer, opened fire on a large crowd of unarmed Indian civilians who had gathered at Jallianwala Bagh to peacefully protest the repressive Rowlatt Act. Chauri Chaura incident - It occurred at Chauri Chaura in the Gorakhpur district of the United Province, (modern Uttar Pradesh) in British India in 1922. Partition of Bengal - It was announced by the then Viceroy Lord Curzon in 1905.</p>",
                    solution_hi: "<p>12.(c) <strong>जलियाँवाला बाग हत्याकांड।</strong> यह 13 अप्रैल, 1919 को अमृतसर में ब्रिटिश औपनिवेशिक शासन के दौरान घटित एक दुखद घटना थी। ब्रिटिश सेना ने जनरल रेजिनाल डायर के नेतृत्व में, जालियाँवाला बाग में एकत्रित निर्दोष भारतीय नागरिकों की भीड़ पर अंधाधुंध गोलीबारी कर दी। ये लोग दमनकारी रोलेट एक्ट का विरोध करने के लिए शांतिपूर्ण प्रदर्शन कर रहे थे। चौरी चौरा कांड - यह 1922 में ब्रिटिश भारत के संयुक्त प्रांत (आधुनिक उत्तर प्रदेश) के गोरखपुर जिले के चौरी चौरा में घटित हुई थी। बंगाल का विभाजन - इसकी घोषणा तत्कालीन वायसराय लॉर्ड कर्जन ने 1905 में की थी।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "18",
                    question_en: "<p>13. Tarun Bhattacharya, who was awarded the Sangeet Natak Akademi Award in 2018, is known for playing which musical instrument?</p>",
                    question_hi: "<p>13. तरुण भट्टाचार्य, जिन्हें 2018 में संगीत नाटक अकादमी पुरस्कार से सम्मानित किया गया था, किस संगीत वाद्ययंत्र को बजाने के लिए जाने जाते हैं?</p>",
                    options_en: ["<p>Santoor</p>", "<p>Violin</p>", 
                                "<p>Tabla</p>", "<p>Sitar</p>"],
                    options_hi: ["<p>संतूर</p>", "<p>वायलिन</p>",
                                "<p>तबला</p>", "<p>सितार</p>"],
                    solution_en: "<p>13.(a) <strong>Santoor.</strong> The Indian santoor is a trapezoid-shaped hammered dulcimer, a variation of the Iranian santoor. Notable Indian santoor players include Pandit Shivkumar Sharma, Pandit Bhajan Sopori, and Pandit Ulhas Bapat. The Sangeet Natak Akademi Award, or Akademi Puraskar, is the highest Indian honor for performing arts, conferred by the Sangeet Natak Akademi, India\'s National Academy of Music, Dance, and Drama.</p>",
                    solution_hi: "<p>13.(a) <strong>संतूर।</strong> भारतीय संतूर एक समलम्ब आकार का हथौड़े से बजाया जाने वाला डुलसीमर जैसा वाद्य यंत्र है, जो ईरानी संतूर का एक रूपांतर है। प्रसिद्ध भारतीय संतूर वादकों में पंडित शिवकुमार शर्मा, पंडित भजन सोपोरी, और पंडित उल्हास बापट शामिल हैं। संगीत नाटक अकादमी पुरस्कार, या अकादमी पुरस्कार, प्रदर्शन कलाओं के लिए भारत का सर्वोच्च सम्मान है, जो संगीत नाटक अकादमी, भारत की राष्ट्रीय संगीत, नृत्य और नाटक अकादमी द्वारा प्रदान किया जाता है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "18",
                    question_en: "<p>14. Which of the following is an example of Phylum Arthropoda?</p>",
                    question_hi: "<p>14. निम्नलिखित में से कौन-सा संघ आर्थ्रोपोडा का उदाहरण है?</p>",
                    options_en: ["<p>Nereis</p>", "<p>Butterfly</p>", 
                                "<p>Hirudinaria</p>", "<p>Pila</p>"],
                    options_hi: ["<p>नेरीस (Nereis)</p>", "<p>तितली (Butterfly)</p>",
                                "<p>जोंकजों (Hirudinaria)</p>", "<p>घोंघा (Pila)</p>"],
                    solution_en: "<p>14.(b) <strong>Butterfly.</strong> Phylum Arthropoda is the largest phylum in the animal kingdom, first classified by Von Seibold in 1845. Approximately two-thirds of all named species belong to this phylum. Arthropods have a chitinous exoskeleton, jointed appendages, and various respiratory organs, including gills, book gills, book lungs, or a tracheal system. They possess an open circulatory system, and excretion occurs through Malpighian tubules. Examples include cockroaches, prawns, crabs, scorpions, houseflies, and mites.</p>",
                    solution_hi: "<p>14.(b) <strong>तितली</strong> (Butterfly)। संघ आर्थ्रोपोडा प्राणी जगत का सबसे बड़ा संघ है, जिसे पहली बार वॉन सीबोल्ड ने 1845 में वर्गीकृत किया था। लगभग दो-तिहाई नामित प्रजातियाँ इस संघ से संबंधित हैं। आर्थ्रोपोड्स में काइटिन युक्त बाह्य कंकाल, संधियुक्त उपांग, और विभिन्न श्वसन अंग होते हैं, जिनमें गिल्स, पुस्त गलफड़े, पुस्त फेफड़े, या श्वासनलि प्रणाली शामिल हैं। उनमें खुला परिसंचरण तंत्र होता है, और उत्सर्जन मैलपीगी नलिकाओं के माध्यम से होता है। उदाहरणों में कॉकरोच, झींगा, केकड़े, बिच्छू, घरेलू मक्खियाँ, और माइट्स शामिल हैं।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "18",
                    question_en: "<p>15. As per the Timurid tradition, Humayun had to share power with his brothers. Humayun had the control of Delhi, Agra and Central India, while his brother Kamran controlled which of the following regions?</p>",
                    question_hi: "<p>15. तैमूरी परंपरा के अनुसार, हुमायूँ को अपने भाइयों के साथ सत्ता साझा करनी थी। हुमायूँ का दिल्ली, आगरा और मध्य भारत पर नियंत्रण था, जबकि उसके भाई कामरान का नियंत्रण निम्नलिखित में से किस क्षेत्र पर था?</p>",
                    options_en: ["<p>Afghanistan and Punjab</p>", "<p>Deccan</p>", 
                                "<p>Gujarat and Rajasthan</p>", "<p>Bengal and Bihar</p>"],
                    options_hi: ["<p>अफगानिस्तान और पंजाब</p>", "<p>दक्कन</p>",
                                "<p>गुजरात और राजस्थान</p>", "<p>बंगाल और बिहार</p>"],
                    solution_en: "<p>15.(a) <strong>Afghanistan and Punjab.</strong> Kamran Mirza was the second son of Babur, the founder of the Mughal Empire. He was the brother of Humayun, who was born Nasir-ud-Din Muhammad in Kabul. Humayun was the second Emperor of the Mughal Empire, ruling over present-day Pakistan, Northern India, Afghanistan, and Bangladesh from 1530 to 1540 and again from 1555 to 1556.</p>",
                    solution_hi: "<p>15.(a) <strong>अफ़गानिस्तान और पंजाब</strong>। कामरान मिर्जा मुगल साम्राज्य के संस्थापक बाबर के दूसरे पुत्र थे। वे हुमायूँ के भाई थे, जिनका जन्म काबुल में नासिर-उद-दीन मुहम्मद के रूप में हुआ था । हुमायूँ मुगल साम्राज्य के दूसरे सम्राट थे, जिन्होंने वर्तमान पाकिस्तान, उत्तरी भारत, अफगानिस्तान और बांग्लादेश पर 1530 से 1540 तक और फिर 1555 से 1556 तक शासन किया था।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "16",
                    section: "18",
                    question_en: "<p>16. Which two organic chemists are known for observing the peroxide effect in adding reagents to unsaturated compounds in 1933?</p>",
                    question_hi: "<p>16. 1933 में असंतृप्त यौगिकों में अभिकर्मकों को मिलाने पर परॉक्साइड प्रभाव को देखने के लिए कौन-से दो कार्बनिक रसायनज्ञ जाने जाते हैं?</p>",
                    options_en: ["<p>Morris S Kharasch and Frank R Mayo</p>", "<p>C John Cadogan and Luis M Campos</p>", 
                                "<p>B Steven Bachrach and Roald Hoffmann</p>", "<p>Justus von Liebig and Friedrich W&ouml;hler</p>"],
                    options_hi: ["<p>मॉरिस एस खराश और फ्रैंक आर मेयो (Morris S Kharasch and Frank R Mayo)</p>", "<p>सी जॉन कैडोगन और लुइस एम कैम्पोस (C John Cadogan and Luis M Campos)</p>",
                                "<p>बी स्टीवन बैचराच और रोआल्ड हॉफमैन (B Steven Bachrach and Roald Hoffmann)</p>", "<p>जस्टस वॉन लिबिग और फ्रेडरिक वोहलर (Justus von Liebig and Friedrich W&ouml;hler)</p>"],
                    solution_en: "<p>16.(a) <strong>Morris S Kharasch and Frank R Mayo.</strong> The peroxide effect, or Kharasch effect, is an exception to&nbsp;Markovnikov\'s rule, describing the addition of hydrogen bromide (HBr) to unsymmetrical alkenes in the presence of peroxides. This effect results in products that differ from those predicted by Markovnikov\'s rule. Kharasch and Mayo\'s paper, \"The Peroxide Effect in the Addition of Reagents to Unsaturated Compounds. I. The Addition of Hydrogen Bromide to Allyl Bromide,\" was published in the Journal of the American Chemical Society in 1933.</p>",
                    solution_hi: "<p>16.(a) <strong>मॉरिस एस खराश और फ्रैंक आर मेयो </strong>(Morris S Kharasch and Frank R Mayo)। पेरोक्साइड प्रभाव, या खराश प्रभाव, मार्कोवनिकोव के नियम का एक अपवाद है, जो पेरोक्साइड की उपस्थिति में असममित एल्कीन पर हाइड्रोजन ब्रोमाइड (HBr) के योग का वर्णन करता है। इस प्रभाव के परिणामस्वरूप ऐसे उत्पाद बनते हैं जो मार्कोवनिकोव के नियम द्वारा अनुमानित उत्पादों से भिन्न होते हैं। खराश और मेयो का शोधपत्र, \"असंतृप्त यौगिकों में अभिकर्मकों के योग में पेरोक्साइड प्रभाव। एलिल ब्रोमाइड में हाइड्रोजन ब्रोमाइड का योग,\" जर्नल ऑफ द अमेरिकन केमिकल सोसाइटी में 1933 में प्रकाशित हुआ था।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "17",
                    section: "18",
                    question_en: "<p>17. Which of the following is NOT a Fundamental Duty?</p>",
                    question_hi: "<p>17. निम्नलिखित में से कौन-सा एक मौलिक कर्तव्य नहीं है?</p>",
                    options_en: ["<p>To prohibit trade in rare and endangered species</p>", "<p>To value and preserve rich heritage of the country&rsquo;s composite culture</p>", 
                                "<p>To safeguard public property and to abjure violence</p>", "<p>To uphold and protect the sovereignty, unity and integrity of India</p>"],
                    options_hi: ["<p>दुर्लभ और लुप्तप्राय प्रजातियों के व्यापार को निषेध बनाना</p>", "<p>देश की सामासिक संस्कृति की गौरवशाली परंपरा को महत्त्व देना और उसका परिरक्षण करना</p>",
                                "<p>सार्वजनिक संपत्ति को सुरक्षित रखना और हिंसा का परित्याग करना</p>", "<p>भारत की संप्रभुता, एकता और अखंडता को बनाए रखना और उसकी रक्षा करना</p>"],
                    solution_en: "<p>17.(a) <strong>To prohibit trade in rare and endangered species</strong>. Fundamental Duties are a set of moral and ethical&nbsp;obligations that are enshrined in Part IV-A (Article 51A) of the Constitution of India. Article 51A (c) - To uphold and protect the sovereignty, unity, and integrity of India. Article 51A (f) - To value and preserve the rich heritage of our composite culture. Article 51A (i) - To safeguard public property and to abjure violence.</p>",
                    solution_hi: "<p>17.(a) <strong>दुर्लभ और लुप्तप्राय प्रजातियों के व्यापार को निषेध बनाना</strong>। मौलिक कर्तव्य नैतिक और नैतिक दायित्वों का एक समूह है जो भारत के संविधान के भाग IV-A (अनुच्छेद 51A) में निहित है। अनुच्छेद 51A (c) - भारत की संप्रभुता, एकता और अखंडता को बनाए रखना और उसकी रक्षा करना। अनुच्छेद 51A (f) - हमारी समग्र संस्कृति की समृद्ध विरासत को महत्व देना और उसका संरक्षण करना। अनुच्छेद 51A (i) - सार्वजनिक संपत्ति की सुरक्षा करना और हिंसा से दूर रहना।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "18",
                    section: "18",
                    question_en: "<p>18. What were the main obstacles during the industrial development of India at the time of independence?</p>",
                    question_hi: "<p>18. स्वतंत्रता के समय भारत के औद्योगिक विकास में मुख्य बाधाएँ क्या थीं?</p>",
                    options_en: ["<p>Market for industrial produce</p>", "<p>Capital investment in industries</p>", 
                                "<p>Employment required for industries</p>", "<p>Land for industrial establishment</p>"],
                    options_hi: ["<p>औद्योगिक उत्पादों के लिए बाज़ार</p>", "<p>उद्योगों में पूंजी निवेश</p>",
                                "<p>उद्योगों के लिए आवश्&zwj;यक रोजगार</p>", "<p>औद्योगिक स्थापना हेतु भूमि</p>"],
                    solution_en: "<p>18.(b) <strong>Capital investment in industries.</strong> At independence, India faced challenges like illiteracy, poverty, low per capita income, industrial backwardness, and unemployment. The government implemented industrial policies that marked a turning point in India\'s industrial history. By promoting public sector industries, it helped spread industrialization to different regions and generated more employment opportunities.</p>",
                    solution_hi: "<p>18.(b) <strong>उद्योगों में पूंजी निवेश।</strong> स्वतंत्रता के समय भारत को निरक्षरता, गरीबी, प्रति व्यक्ति कम आय, औद्योगिक पिछड़ापन और बेरोजगारी जैसी चुनौतियों का सामना करना पड़ा। सरकार ने औद्योगिक नीतियों को लागू किया जिसने भारत के औद्योगिक इतिहास में एक महत्वपूर्ण मोड़ ला दिया। सार्वजनिक क्षेत्र के उद्योगों को बढ़ावा देकर इसने विभिन्न क्षेत्रों में औद्योगीकरण को फैलाने में मदद की और अधिक रोजगार के अवसर पैदा किए।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "19",
                    section: "18",
                    question_en: "<p>19. Which of the following is NOT a unit of energy?</p>",
                    question_hi: "<p>19. निम्नलिखित में से कौन-सी ऊर्जा की इकाई नहीं है?</p>",
                    options_en: ["<p>Joule</p>", "<p>Calorie</p>", 
                                "<p>Newton</p>", "<p>Kilowatt hour</p>"],
                    options_hi: ["<p>जूल</p>", "<p>कैलोरी</p>",
                                "<p>न्यूटन</p>", "<p>किलोवाट घंटा</p>"],
                    solution_en: "<p>19.(c) <strong>Newton.</strong> The SI unit of force is the Newton. Horsepower (hp) is a unit of measurement of power, with 1 horsepower equaling 746 watts, where Watt is the SI unit of power. Joule is the SI unit of energy, and kilowatt-hour is a commercial unit representing the energy used by a 1-kilowatt device in one hour. A calorie, also a unit of energy, is the amount of heat needed to raise the temperature of 1 gram of water by 1&deg;C or 1 Kelvin.</p>",
                    solution_hi: "<p>19.(c) <strong>न्यूटन।</strong> बल का SI मात्रक न्यूटन है। अश्वशक्ति (hp) शक्ति मापने की एक इकाई है, जिसमें 1 अश्वशक्ति 746 वाट के बराबर होता है, जहाँ वाट शक्ति का SI मात्रक है। जूल ऊर्जा का SI मात्रक है, और किलोवाट-घंटा एक वाणिज्यिक इकाई है जो एक घंटे में 1-किलोवाट डिवाइस द्वारा उपयोग की जाने वाली ऊर्जा का प्रतिनिधित्व करती है। एक कैलोरी, जो ऊर्जा की एक इकाई भी है, 1 ग्राम जल के तापमान को 1&deg;C या 1 केल्विन बढ़ाने के लिए आवश्यक ऊष्मा की मात्रा है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "20",
                    section: "18",
                    question_en: "20. Which of the following is a component of the SHG-bank linkage program in India?",
                    question_hi: "20. निम्नलिखित में से कौन-सा भारत में SHG-बैंक लिंकेज प्रोग्राम का एक घटक है?",
                    options_en: ["  Microfinance institutions providing loans directly to SHGs", "  Banks providing loans to individual members of SHGs", 
                                "  Non-governmental organisations providing capacity building training to SHGs", " State government providing subsidies to SHGs for credit"],
                    options_hi: [" सूक्ष्म वित्त संस्थाएँ, SHGs को सीधे ऋण प्रदान करती हैं", " बैंक SHGs के व्यक्तिगत सदस्यों को ऋण प्रदान करते हैं",
                                "  गैर-सरकारी संगठन SHGs को क्षमता निर्माण प्रशिक्षण प्रदान करते हैं ", " राज्य सरकार SHGs को ऋण के लिए अनुदान प्रदान करती है"],
                    solution_en: "20.(b) Self-Help Groups (SHGs) in India began in the late 1980s as a poverty alleviation initiative by NGOs and development agencies. The SHG-Bank Linkage Model involves banks (Commercial, Rural, and Cooperative) directly lending to SHGs, which manage and guarantee loans for members. MFI - Bank Linkage Model: This model covers financing of Micro Finance Institutions (MFIs) by banking agencies for on-lending to SHGs and other small borrowers.",
                    solution_hi: "20.(b) भारत में स्वयं सहायता समूह (SHG) की शुरुआत 1980 के दशक के अंत में गैर सरकारी संगठनों (NGO) और विकास एजेंसियों द्वारा गरीबी उन्मूलन पहल के रूप में हुई थी। SHG-बैंक लिंकेज मॉडल में बैंक (वाणिज्यिक, ग्रामीण और सहकारी) सीधे SHG को ऋण प्रदान करता हैं, जो सदस्यों के लिए ऋण का प्रबंधन और गारंटी देता हैं। MFI - बैंक लिंकेज मॉडल: यह मॉडल SHG और अन्य छोटे उधारकर्ताओं को ऋण देने के लिए बैंकिंग एजेंसियों द्वारा माइक्रो फाइनेंस इंस्टीट्यूशंस (MFI) के वित्तपोषण को शामिल करता है।<br /> ",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "21",
                    section: "18",
                    question_en: "<p>21. In which of the following years was the Right of Persons with Disabilities Act passed?</p>",
                    question_hi: "<p>21. निम्नलिखित में से किस वर्ष में दिव्यांगजन अधिकार अधिनियम पारित किया गया था?</p>",
                    options_en: ["<p>2010</p>", "<p>2002</p>", 
                                "<p>2016</p>", "<p>2019</p>"],
                    options_hi: ["<p>2010</p>", "<p>2002</p>",
                                "<p>2016</p>", "<p>2019</p>"],
                    solution_en: "<p>21.(c) <strong>2016.</strong> The Rights of Persons with Disabilities (RPwD) Act came into force from 19th April, 2017. It replaced the Persons with Disabilities (Equal Opportunities, Protection of Rights and Full Participation) Act, 1995. Objective - To ensure that all persons with disabilities can lead their lives with dignity, without discrimination and with equal opportunities.</p>",
                    solution_hi: "<p>21.(c) <strong>2016.</strong> दिव्यांगजन अधिकार अधिनियम (RPwD) 19 अप्रैल, 2017 से लागू हुआ। इसने दिव्यांगजन (समान अवसर, अधिकारों का संरक्षण और पूर्ण भागीदारी) अधिनियम, 1995 का स्थान लिया। उद्देश्य - यह सुनिश्चित करना कि सभी दिव्यांगजन बिना किसी भेदभाव के और समान अवसरों के साथ सम्मान के साथ अपना जीवन जी सकें।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "22",
                    section: "18",
                    question_en: "<p>22. The Wardha is a tributary of which of the following rivers?</p>",
                    question_hi: "<p>22. वर्धा निम्नलिखित में से किस नदी की सहायक नदी है?</p>",
                    options_en: ["<p>Mahanadi river</p>", "<p>Tapi river</p>", 
                                "<p>Godavari river</p>", "<p>Narmada river</p>"],
                    options_hi: ["<p>महानदी</p>", "<p>तापी नदी</p>",
                                "<p>गोदावरी नदी</p>", "<p>नर्मदा नदी</p>"],
                    solution_en: "<p>22.(c) <strong>Godavari river.</strong> The Godavari, India\'s second-largest river, originates near Trimbakeshwar in Nashik, Maharashtra, and flows east across the Deccan Plateau to the Bay of Bengal. Known as Dakshin Ganga. Its main tributaries are the Pravara, Purna, Manjra and Indravati rivers. The Wardha River rises in the Satpura Range near Multai, Betul district in Madhya Pradesh before entering Maharashtra.</p>",
                    solution_hi: "<p>22.(c) <strong>गोदावरी नदी।</strong> गोदावरी, भारत की दूसरी सबसे बड़ी नदी, नासिक, महाराष्ट्र में त्र्यंबकेश्वर के निकट उत्पन्न होती है और दक्कन पठार के पार पूर्व की ओर बहकर बंगाल की खाड़ी में मिलती है। इसे दक्षिण गंगा के नाम से जाना जाता है। इसकी मुख्य सहायक नदियाँ प्रवरा, पूर्णा, मंजरा और इंद्रावती नदियाँ हैं। वर्धा नदी महाराष्ट्र में प्रवेश करने से पहले मध्य प्रदेश के बैतूल जिले के मुलताई के पास सतपुड़ा पर्वत श्रृंखला से निकलती है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "23",
                    section: "18",
                    question_en: "<p>23. During which of the following rules did Buddhism get split into two schools - Hinayana and Mahayana in the fourth Buddhist council?</p>",
                    question_hi: "<p>23. निम्नलिखित में से किस शासन के दौरान चतुर्थ बौद्ध संगीति में बौद्ध धर्म दो विचारधाराओं - हीनयान और महायान में विभाजित हो गया?</p>",
                    options_en: ["<p>Kushana</p>", "<p>Parthian</p>", 
                                "<p>Gupta</p>", "<p>Shaka</p>"],
                    options_hi: ["<p>कुषाण</p>", "<p>पार्थियन</p>",
                                "<p>गुप्त</p>", "<p>शक</p>"],
                    solution_en: "<p>23.(a) <strong>Kushana.</strong> The Fourth Buddhist Council was held in 72 AD at Kundalvana, Kashmir, under the Kushan king Kanishka. It was led by Vasumitra, with Asvaghosa as his deputy, and marked the division of Buddhism into two sects: Mahayana and Hinayana. Hinayana is a belief system without a deity, where karma takes the place of God, and its oldest school is Sthaviravada. Mahayana, known as the Greater Wheel, was founded by Nagarjuna. Its followers believed that salvation could be attained with the help of Buddha and Bodhisattvas.</p>",
                    solution_hi: "<p>23.(a) <strong>कुषाण।</strong> चौथी बौद्ध परिषद 72 ई. में कुषाण राजा कनिष्क के शासनकाल में कश्मीर के कुंडलवन में आयोजित की गई थी। इसका नेतृत्व वसुमित्र ने किया था, अश्वघोष उनके प्रतिनिधि थे, और इसने बौद्ध धर्म को दो संप्रदायों में विभाजित कर दिया: महायान और हीनयान। हीनयान एक ऐसा विश्वास तंत्र है जिसमें कोई देवता नहीं है, जहाँ कर्म ईश्वर का स्थान ले लेता है, और इसका सबसे पुराना संप्रदाय स्थविरवाद है। महायान, जिसे महान चक्र के रूप में जाना जाता है, की स्थापना नागार्जुन ने की थी। इसके अनुयायियों का मानना ​​था कि बुद्ध और बोधिसत्वों की मदद से मोक्ष प्राप्त किया जा सकता है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "24",
                    section: "18",
                    question_en: "<p>24. As of Financial year 2019, which of the following states has the highest road density in India?</p>",
                    question_hi: "<p>24. वित्तीय वर्ष 2019 तक प्राप्त जानकारी के अनुसार, भारत में निम्नलिखित में से किस राज्य का सड़क घनत्व सबसे अधिक है?</p>",
                    options_en: ["<p>Haryana</p>", "<p>Tamil Nadu</p>", 
                                "<p>Kerala</p>", "<p>Punjab</p>"],
                    options_hi: ["<p>हरियाणा</p>", "<p>तमिलनाडु</p>",
                                "<p>केरल</p>", "<p>पंजाब</p>"],
                    solution_en: "<p>24.(c) <strong>Kerala.</strong> As of the financial year 2019, Kerala had the highest road density among states, with 6.7 thousand kilometers of roads per one thousand square kilometers. Among union territories, Chandigarh had the highest road density in India, with over 22.6 thousand kilometers per one thousand square kilometers. Road density refers to the total length of all roads in a country, including highways, national roads, and city or rural roads, compared to the country\'s land area.</p>",
                    solution_hi: "<p>24.(c) <strong>केरल।</strong> वित्तीय वर्ष 2019 तक, केरल में राज्यों के बीच सबसे अधिक सड़क घनत्व था, जहाँ प्रति एक हज़ार वर्ग किलोमीटर में 6.7 हज़ार किलोमीटर सड़कें थीं। केंद्र शासित प्रदेशों में, चंडीगढ़ में भारत में सबसे अधिक सड़क घनत्व था, जहाँ प्रति एक हज़ार वर्ग किलोमीटर में 22.6 हज़ार किलोमीटर से अधिक सड़कें थीं। सड़क घनत्व देश के भूमि क्षेत्र की तुलना में राजमार्गों, राष्ट्रीय सड़कों और शहर या ग्रामीण सड़कों सहित देश में सभी सड़कों की कुल लंबाई को संदर्भित करता है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "25",
                    section: "misc",
                    question_en: "<p>25. What is the other name of tetanus caused by toxin producing bacteria called clostridium tetani?</p>",
                    question_hi: "<p>25. क्लास्ट्रिडियम टेटेनाई नामक विष उत्पन्न करने वाले जीवाणु से होने वाले टिटनेस का दूसरा नाम क्या है?</p>",
                    options_en: ["<p>Lock jaw</p>", "<p>Snap jaw</p>", 
                                "<p>Cleft jaw</p>", "<p>Broken jaw</p>"],
                    options_hi: ["<p>धनुस्तंभ (Lock jaw)</p>", "<p>स्फुटन जबड़ा (Snap jaw)</p>",
                                "<p>विदलित जबड़ा (Cleft jaw)</p>", "<p>खंडित जबड़ा (Broken jaw)</p>"],
                    solution_en: "<p>25.(a) <strong>Lock jaw.</strong> Tetanus, also called lockjaw, is a bacterial infection that causes our neck and jaw muscles to lock up. It occurs when a bacteria found in the environment called Clostridium tetani enter our body through a break in our skin. Other dangerous bacterial diseases include cholera, meningitis, tuberculosis (TB), and Lyme disease.</p>",
                    solution_hi: "<p>25.(a) <strong>धनुस्तंभ (Lockjaw)।</strong> टिटनेस, जिसे लॉकजॉ भी कहा जाता है, एक जीवाणु संक्रमण है जो हमारी गर्दन और जबड़े की मांसपेशियों को जकड़ देता है। यह तब होता है जब वातावरण में पाए जाने वाले क्लोस्ट्रीडियम टेटानी नामक बैक्टीरिया हमारी त्वचा में किसी चोट के माध्यम से हमारे शरीर में प्रवेश करते हैं। अन्य खतरनाक जीवाणु रोगों में हैजा, मेनिनजाइटिस, तपेदिक (TB), और लाइम रोग शामिल हैं।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>