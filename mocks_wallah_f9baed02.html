<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">15:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 15</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">15</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 15 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 0,
                end: 13
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 14,
                end: 14
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "17",
                    question_en: "<p>1. Monthly salary of Jitvik in the month of December 2020 was ₹24,800. Every year his&nbsp;salary increases by 5% from the month of January. What was his salary (in ₹) in the&nbsp;month of February 2022 ?</p>",
                    question_hi: "<p>1. दिसंबर 2020 में जित्विक का मासिक वेतन ₹24,800 था। हर साल उनके वेतन में जनवरी के महीने से&nbsp;5% की वृद्धि होती है। फरवरी 2022 के महीने में उसका वेतन (₹ में) कितना था?</p>",
                    options_en: ["<p>27180</p>", "<p>27486</p>", 
                                "<p>26485</p>", "<p>27342</p>"],
                    options_hi: ["<p>27180</p>", "<p>27486</p>",
                                "<p>26485</p>", "<p>27342</p>"],
                    solution_en: "<p>1.(d) According to question,<br>Jitvik salary in month of february 2022 = 24800 &times; <math display=\"inline\"><mfrac><mrow><mn>21</mn></mrow><mrow><mn>20</mn></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>21</mn><mn>20</mn></mfrac></math><br>= ₹ 27342</p>",
                    solution_hi: "<p>1.(d) प्रश्न के अनुसार,<br>फरवरी 2022 के महीने में जित्विक का वेतन = 24800 &times; <math display=\"inline\"><mfrac><mrow><mn>21</mn></mrow><mrow><mn>20</mn></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>21</mn><mn>20</mn></mfrac></math><br>= ₹ 27342</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "17",
                    question_en: "<p>2. The difference between 31% and 26% of votes for the same party at different places is&nbsp;2350. What is 7% of those votes?</p>",
                    question_hi: "<p>2. एक ही पार्टी को अलग-अलग थानों पर प्राप्त मतों के 31% और 26% मतों के बीच का अंतर 2350 है।&nbsp;इन मतों का 7% कितना है?</p>",
                    options_en: ["<p>2270</p>", "<p>4090</p>", 
                                "<p>3090</p>", "<p>3290</p>"],
                    options_hi: ["<p>2270</p>", "<p>4090</p>",
                                "<p>3090</p>", "<p>3290</p>"],
                    solution_en: "<p>2.(d) According to question,<br><math display=\"inline\"><mo>&#8658;</mo></math> 31% - 26% = 5% = 2350<br><math display=\"inline\"><mo>&#8658;</mo></math> 100% = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2350</mn><mn>5</mn></mfrac></math> &times; 100 = 47000<br>Now, <br><math display=\"inline\"><mo>&#8658;</mo></math> 47000 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>100</mn></mfrac></math> = 3290</p>",
                    solution_hi: "<p>2.(d) प्रश्न के अनुसार,<br><math display=\"inline\"><mo>&#8658;</mo></math> 31% - 26% = 5% = 2350<br><math display=\"inline\"><mo>&#8658;</mo></math> 100% = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2350</mn><mn>5</mn></mfrac></math> &times; 100 = 47000<br>अब, <br><math display=\"inline\"><mo>&#8658;</mo></math> 47000 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>100</mn></mfrac></math> = 3290</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "17",
                    question_en: "<p>3. The population of a town first decreased by 16% due to migration to a big city for&nbsp;better job opportunities. The next year, the population of the town increased by 21% as&nbsp;there were better facilities for jobs. What is the net percentage change (correct to 2&nbsp;decimal places) in the population?</p>",
                    question_hi: "<p>3. बेहतर नौकरी के अवसरों के लिए एक बड़े शहर में प्रवास के कारण पहले एक कस्बे की आबादी में 16% की कमी आई। अगले वर्ष, कस्बे की आबादी में 21% की वृद्धि हुई क्योंकि वहां नौकरियों के लिए बेहतर&nbsp;अवसर मिलने लगे। जनसंख्या में निवल प्रतिशत परिवर्तन (दशमलव के 2 स्थानों तक सही) कितना है?</p>",
                    options_en: ["<p>Decrease by 2.54%</p>", "<p>Increase by 2.54%</p>", 
                                "<p>Decrease by 1.64%</p>", "<p>Increase by 1.64%</p>"],
                    options_hi: ["<p>2.54% की कमी</p>", "<p>2.54% की वृद्धि</p>",
                                "<p>1.64% की कमी</p>", "<p>1.64% की वृद्धि</p>"],
                    solution_en: "<p>3.(d) Let the population of a town = 100 <br>According to question,<br>Population after two year = 100 &times; <math display=\"inline\"><mfrac><mrow><mn>21</mn></mrow><mrow><mn>25</mn></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>121</mn><mn>100</mn></mfrac></math> = 101.64 <br>Net increase change % = <math display=\"inline\"><mfrac><mrow><mn>101</mn><mo>.</mo><mn>64</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>100</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> &times; 100 = 1.64%</p>",
                    solution_hi: "<p>3.(d) माना कि एक कस्बे की जनसंख्या = 100 है <br>प्रश्न के अनुसार,<br>दो वर्ष बाद जनसंख्या = 100 &times; <math display=\"inline\"><mfrac><mrow><mn>21</mn></mrow><mrow><mn>25</mn></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>121</mn><mn>100</mn></mfrac></math> = 101.64 <br>शुद्ध वृद्धि परिवर्तन % = <math display=\"inline\"><mfrac><mrow><mn>101</mn><mo>.</mo><mn>64</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>100</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> &times; 100 = 1.64%</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "17",
                    question_en: "4.  In an election between two candidates, one candidate gets 72% of the total votes cast. If the total votes are 1000, by how many votes does the winner win the election?",
                    question_hi: "4.  दो उम्मीदवारों के बीच एक चुनाव में एक उम्मीदवार को डाले गए कुल मतों का 72% प्राप्त होता है। यदि कुल मत 1000 हैं, तो विजेता कितने मतों से चुनाव जीतता है?",
                    options_en: [" 360", " 720", 
                                " 250", " 440"],
                    options_hi: [" 360", " 720",
                                " 250", " 440"],
                    solution_en: "4.(d)<br />Votes got by looser candidate = 28%<br />Votes got by winner candidate = 72%<br />Difference = 72% - 28% = 44%<br />(total number of votes) 100% = 1000 votes<br />(no. of votes by which winner won the election) 44% = 440 votes",
                    solution_hi: "4.(d)<br />हारे हुए उम्मीदवार को मिले वोट = 28%<br />विजेता उम्मीदवार को मिले वोट = 72%<br />अंतर = 72% - 28% = 44%<br />(मतों की कुल संख्या) 100% = 1000 मत<br />(वोटों की संख्या जिनसे विजेता ने चुनाव जीता) 44% = 440 वोट",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "17",
                    question_en: "5. A number is first increased by 44% and then decreased by 30%. The net percentage change (rounded off to the nearest integer) in the original number is: ",
                    question_hi: "5. एक संख्या में पहले 44% की वृद्धि की जाती है और फिर उसमें 30% की कमी की जाती है। मूल संख्या में निवल प्रतिशत परिवर्तन (निकटतम पूर्णांक तक सन्नि कटित) कितना है?",
                    options_en: [" increase by 1%", " increase by 4%", 
                                " decrease by 1%", " decrease by 4% "],
                    options_hi: [" 1% की वृद्धि", " 4% की वृद्ध",
                                " 1% की कमी", " 4% की कमी"],
                    solution_en: "5.(a)<br />According to the question,<br />44 - 30 + <math display=\"inline\"><mfrac><mrow><mn>44</mn><mi>&nbsp;</mi><mo>×</mo><mi>&nbsp;</mi><mo>(</mo><mo>-</mo><mn>30</mn><mo>)</mo></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 14 - 13.2 = 0.8    (approx 1% increase)",
                    solution_hi: "5.(a)<br />प्रश्न के अनुसार,<br />44 - 30 + <math display=\"inline\"><mfrac><mrow><mn>44</mn><mi>&nbsp;</mi><mo>×</mo><mi>&nbsp;</mi><mo>(</mo><mo>-</mo><mn>30</mn><mo>)</mo></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 14 - 13.2 = 0.8    (लगभग 1% वृद्धि)",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "17",
                    question_en: "<p>6. Raju spends 80% of his income. His income increased by 20%, while his spending rise by 10%. The percentage of increase in his savings is:</p>",
                    question_hi: "<p>6. राजू अपनी आय का 80% खर्च करता है। उसकी आय में 20% की वृद्धि हुई, जबकि उसके खर्च में 10% की वृद्धि हुई। उसकी बचत में वृद्धि का प्रतिशत क्या है?</p>",
                    options_en: ["<p>48%</p>", "<p>30%</p>", 
                                "<p>60%</p>", "<p>40%</p>"],
                    options_hi: ["<p>48%</p>", "<p>30%</p>",
                                "<p>60%</p>", "<p>40%</p>"],
                    solution_en: "<p>6.(c) <br>According to the question,<br>Let the income of the Raju be 100 units<br>Ratio - income : expenditure : saving<br>Before -&nbsp; 100&nbsp; &nbsp;:&nbsp; &nbsp; &nbsp; &nbsp;80&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; 20<br>After -&nbsp; &nbsp; &nbsp;120&nbsp; &nbsp;:&nbsp; &nbsp; &nbsp; &nbsp;88&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; 32<br>require% = <math display=\"inline\"><mfrac><mrow><mn>32</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>20</mn></mrow><mrow><mn>20</mn></mrow></mfrac></math> &times; 100 = 60%</p>",
                    solution_hi: "<p>6.(c) <br>प्रश्न के अनुसार,<br>माना , राजू की आय = 100 इकाई <br>अनुपात -&nbsp; &nbsp;आय&nbsp; : व्यय : बचत<br>पहले -&nbsp; &nbsp; &nbsp; &nbsp;100&nbsp; :&nbsp; 80&nbsp; :&nbsp; &nbsp;20<br>बाद में -&nbsp; &nbsp; &nbsp;120&nbsp; :&nbsp; 88&nbsp; :&nbsp; &nbsp;32<br>आवश्यक% = <math display=\"inline\"><mfrac><mrow><mn>32</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>20</mn></mrow><mrow><mn>20</mn></mrow></mfrac></math> &times; 100 = 60%</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "17",
                    question_en: "<p>7. R gives <math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math> of the toffees he has with him to S. If now R has k% of the toffies with S, then find the value of k.</p>",
                    question_hi: "<p>7. R अपने पास मौजूद टॉफ़ी का <math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math> भाग S को देता है। यदि अब R के पास S के पास मौजूद टॉफ़ी का k% है, तो k का मान ज्ञात कीजिए।</p>",
                    options_en: ["<p><math display=\"inline\"><mfrac><mrow><mn>500</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>200</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>", 
                                "<p>140</p>", "<p>200</p>"],
                    options_hi: ["<p><math display=\"inline\"><mfrac><mrow><mn>500</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>200</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>",
                                "<p>140</p>", "<p>200</p>"],
                    solution_en: "<p>7.(a)<br>Let R has x toffees <br>Toffees given by R to S = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>3</mn><mi mathvariant=\"normal\">x</mi></mrow><mn>8</mn></mfrac></math><br>Then, now R has toffees = x - <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>3</mn><mi mathvariant=\"normal\">x</mi></mrow><mn>8</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5</mn><mi mathvariant=\"normal\">x</mi></mrow><mn>8</mn></mfrac></math><br>Hence, k% = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mfrac><mrow><mn>5</mn><mi mathvariant=\"normal\">x</mi></mrow><mn>8</mn></mfrac><mfrac><mrow><mn>3</mn><mi mathvariant=\"normal\">x</mi></mrow><mn>8</mn></mfrac></mfrac></math> &times; 100 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>3</mn></mfrac></math> &times; 100 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>500</mn><mn>3</mn></mfrac></math></p>",
                    solution_hi: "<p>7.(a)<br>माना R के पास x टॉफियाँ हैं <br>R द्वारा S को दी गई टॉफ़ी = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>3</mn><mi mathvariant=\"normal\">x</mi></mrow><mn>8</mn></mfrac></math><br>फिर, अब R के पास टॉफ़ी = x - <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>3</mn><mi mathvariant=\"normal\">x</mi></mrow><mn>8</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5</mn><mi mathvariant=\"normal\">x</mi></mrow><mn>8</mn></mfrac></math><br>अत:, k% = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mfrac><mrow><mn>5</mn><mi mathvariant=\"normal\">x</mi></mrow><mn>8</mn></mfrac><mfrac><mrow><mn>3</mn><mi mathvariant=\"normal\">x</mi></mrow><mn>8</mn></mfrac></mfrac></math> &times; 100 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>3</mn></mfrac></math> &times; 100 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>500</mn><mn>3</mn></mfrac></math></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "17",
                    question_en: "<p>8. In an assembly election, a candidate got 60% of the total valid votes. 3% of the total votes were declared invalid. If the total number of voters is 2,40,000, then find the number of valid votes polled in favour of the candidate.</p>",
                    question_hi: "<p>8. एक विधानसभा चुनाव में एक उम्मीदवार को कुल वैध मतों का 60% प्राप्त हुआ। कुल मतों का 3% अवैध घोषित किया गया। यदि मतदाताओं की कुल संख्या 2,40,000 है, तो उम्मीदवार के पक्ष में डाले गए वैध मतों की संख्या ज्ञात कीजिए।</p>",
                    options_en: ["<p>193680</p>", "<p>139860</p>", 
                                "<p>139680</p>", "<p>139608</p>"],
                    options_hi: ["<p>193680</p>", "<p>139860</p>",
                                "<p>139680</p>", "<p>139608</p>"],
                    solution_en: "<p>8.(c)<br>Required votes = 240000 &times; <math display=\"inline\"><mfrac><mrow><mn>97</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>5</mn></mfrac></math> = 139680</p>",
                    solution_hi: "<p>8.(c)<br>आवश्यक वोट = 240000 &times; <math display=\"inline\"><mfrac><mrow><mn>97</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>5</mn></mfrac></math> = 139680</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "17",
                    question_en: "<p>9. A quantity <math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> is changed to <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>6</mn></mfrac></math>. How much percentage change is given to the quantity?</p>",
                    question_hi: "<p>9. एक भिन्न <math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> को परिवर्तित करके <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>6</mn></mfrac></math> कर दिया जाता है। भिन्न में कितना प्रतिशत परिवर्तन किया गया है?</p>",
                    options_en: ["<p>4.17%</p>", "<p>4.71%</p>", 
                                "<p>4.73%</p>", "<p>4.37%</p>"],
                    options_hi: ["<p>4.17%</p>", "<p>4.71%</p>",
                                "<p>4.73%</p>", "<p>4.37%</p>"],
                    solution_en: "<p>9.(a)<br>Change in the fraction = <math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>6</mn></mrow></mfrac></math> - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>5</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>30</mn></mfrac></math><br>Required change = <math display=\"inline\"><mfrac><mrow><mfrac><mrow><mn>1</mn></mrow><mrow><mn>30</mn></mrow></mfrac></mrow><mrow><mfrac><mrow><mn>4</mn></mrow><mrow><mn>5</mn></mrow></mfrac></mrow></mfrac></math> &times; 100 = 4.17%</p>",
                    solution_hi: "<p>9.(a)<br>भिन्न में परिवर्तन = <math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>6</mn></mrow></mfrac></math> - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>5</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>30</mn></mfrac></math><br>आवश्यक परिवर्तन = <math display=\"inline\"><mfrac><mrow><mfrac><mrow><mn>1</mn></mrow><mrow><mn>30</mn></mrow></mfrac></mrow><mrow><mfrac><mrow><mn>4</mn></mrow><mrow><mn>5</mn></mrow></mfrac></mrow></mfrac></math> &times; 100 = 4.17%</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "17",
                    question_en: "10. The price of a motorcycle was $750 last year. The price increased by 20% this year. What is the price of the motorcycle this year?",
                    question_hi: "10. एक मोटरसाइकिल की कीमत पिछले साल $750 थी। इस साल कीमत में 20% की वृद्धि हुई। इस साल मोटरसाइकिल की कीमत क्या है?",
                    options_en: [" $600", " $790", 
                                " $750", " $900"],
                    options_hi: [" $600", " $790",
                                " $750", " $900"],
                    solution_en: "10.(d) <br />Price of the motor cycle this year = $750 × <math display=\"inline\"><mfrac><mrow><mn>6</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> = $900 ",
                    solution_hi: "10.(d) <br />इस वर्ष मोटर साइकिल की कीमत = $750 × <math display=\"inline\"><mfrac><mrow><mn>6</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> = $900 ",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "17",
                    question_en: "11. A person spends 60% of his salary on family expenses, 10% on medical expenses, 5% on charity, and saves the remaining amount. If the amount on savings is ₹4,000, find his monthly salary.",
                    question_hi: "11. एक व्यक्ति अपने वेतन का 60% पारिवारिक व्यय पर, 10% चिकित्सा व्यय पर, 5% दान पर खर्च करता है, और शेष धनराशि बचाता है। यदि बचत राशि ₹4,000 है, तो उसका मासिक वेतन ज्ञात कीजिए।",
                    options_en: [" ₹12,000", " ₹15,000", 
                                " ₹14,000", " ₹16,000"],
                    options_hi: [" ₹12,000", " ₹15,000",
                                " ₹14,000", " ₹16,000"],
                    solution_en: "11.(d) Let the monthly salary be 100%<br />According to question,<br />Saving = 100 - (60 + 10 + 5) = 25%<br />Now,<br /><math display=\"inline\"><mo>⇒</mo></math> 25% = 4000<br /><math display=\"inline\"><mo>⇒</mo></math> 100 % = ₹ 16000  ",
                    solution_hi: "11.(d) मासिक वेतन 100% है। <br />प्रश्न के अनुसार,<br />बचत = 100 - (60 + 10 + 5) = 25%<br />अब, <br /><math display=\"inline\"><mo>⇒</mo></math> 25% = 4000<br /><math display=\"inline\"><mo>⇒</mo></math> 100 % = ₹ 16000  ",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "17",
                    question_en: "<p>12. A shopkeeper charges his customer 14% more than the cost price. If a customer paid&nbsp;₹18,240 for a mobile phone, then the cost price of the mobile phone was:</p>",
                    question_hi: "<p>12. एक दुकानदार अपने ग्राहक से क्रय मूल्य से 14% अधिक शुल्क लेता है। यदि एक ग्राहक ने एक&nbsp;मोबाइल फोन के लिए ₹18,240 का भुगतान किया, तो मोबाइल फोन का क्रय मूल्य क्या था?</p>",
                    options_en: ["<p>₹16,600</p>", "<p>₹15,686.40</p>", 
                                "<p>₹20,793.60</p>", "<p>₹16,000</p>"],
                    options_hi: ["<p>₹16,600</p>", "<p>₹15,686.40</p>",
                                "<p>₹20,793.60</p>", "<p>₹16,000</p>"],
                    solution_en: "<p>12.(d) <br>According to the question,<br>(SP) 114% = ₹ 18240<br>(CP) 100% = <math display=\"inline\"><mfrac><mrow><mn>18240</mn></mrow><mrow><mn>114</mn></mrow></mfrac></math> &times; 100 = ₹ 16000</p>",
                    solution_hi: "<p>12.(d) <br>प्रश्न के अनुसार,<br>(विक्रय मूल्य) 114% = ₹ 18240<br>(क्रय मूल्य) 100% = <math display=\"inline\"><mfrac><mrow><mn>18240</mn></mrow><mrow><mn>114</mn></mrow></mfrac></math> &times; 100 = ₹ 16000</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "17",
                    question_en: "<p>13. If the price of rice is reduced by 24%, it enables Alok to buy 10 kg more rice for ₹2,500.&nbsp;The reduced rate of rice per kg is:</p>",
                    question_hi: "<p>13. यदि चावल की कीमत में 24% की कमी होती है, तो इससे आलोक ₹2,500 में 10 kg चावल अधिक&nbsp;खरीद पाता है। चावल की प्रति kg घटी हुई दर क्या है?</p>",
                    options_en: ["<p>₹25</p>", "<p>₹60</p>", 
                                "<p>₹50</p>", "<p>₹75</p>"],
                    options_hi: ["<p>₹25</p>", "<p>₹60</p>",
                                "<p>₹50</p>", "<p>₹75</p>"],
                    solution_en: "<p>13.(b) <br>Expenditure = price &times; quantity<br>Ratio -&nbsp; &nbsp; &nbsp;old&nbsp; &nbsp;:&nbsp; &nbsp;new<br>Price -&nbsp; &nbsp; &nbsp; 25&nbsp; &nbsp;:&nbsp; &nbsp;19<br>Quantity - 19&nbsp; &nbsp;:&nbsp; &nbsp;25<br>Difference = 25 - 19 = 6 units<br>6 units = 10 kg<br>(New) 25 units = <math display=\"inline\"><mfrac><mrow><mn>10</mn></mrow><mrow><mn>6</mn></mrow></mfrac></math> &times; 25 kg<br>New price per kg = <math display=\"inline\"><mfrac><mrow><mn>2500</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>6</mn></mrow><mrow><mn>10</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>25</mn></mrow></mfrac></math> = ₹ 60 per kg</p>",
                    solution_hi: "<p>13.(b) <br>व्यय = कीमत &times; मात्रा<br>अनुपात -&nbsp; &nbsp;पुराना&nbsp; &nbsp;:&nbsp; &nbsp; नया<br>मूल्य -&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;25&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; 19<br>मात्रा -&nbsp; &nbsp; &nbsp; &nbsp; 19&nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; 25<br>अंतर = 25 - 19 = 6 इकाई <br>6 इकाई = 10 kg<br>(नया) 25 इकाई = <math display=\"inline\"><mfrac><mrow><mn>10</mn></mrow><mrow><mn>6</mn></mrow></mfrac></math> &times; 25 kg<br>नई कीमत प्रति किलो = <math display=\"inline\"><mfrac><mrow><mn>2500</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>6</mn></mrow><mrow><mn>10</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>25</mn></mrow></mfrac></math> = ₹ 60 per kg</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "17",
                    question_en: "<p>14. An interval of 2 hours 25 minutes is wrongly estimated by 2 hours 30.5 minutes. The error percentage is :</p>",
                    question_hi: "<p>14. 2 घंटे 25 मिनट के अंतराल को गलती से 2 घंटे 30.5 मिनट आकलित कर लिया गया। त्रुटि प्रतिशत ज्ञात कीजिए।</p>",
                    options_en: ["<p>3<math display=\"inline\"><mfrac><mrow><mn>23</mn></mrow><mrow><mn>29</mn></mrow></mfrac></math>%</p>", "<p>2<math display=\"inline\"><mfrac><mrow><mn>27</mn></mrow><mrow><mn>29</mn></mrow></mfrac></math>%</p>", 
                                "<p>3<math display=\"inline\"><mfrac><mrow><mn>19</mn></mrow><mrow><mn>29</mn></mrow></mfrac></math>%</p>", "<p>2<math display=\"inline\"><mfrac><mrow><mn>21</mn></mrow><mrow><mn>29</mn></mrow></mfrac></math>%</p>"],
                    options_hi: ["<p>3<math display=\"inline\"><mfrac><mrow><mn>23</mn></mrow><mrow><mn>29</mn></mrow></mfrac></math>%</p>", "<p>2<math display=\"inline\"><mfrac><mrow><mn>27</mn></mrow><mrow><mn>29</mn></mrow></mfrac></math>%</p>",
                                "<p>3<math display=\"inline\"><mfrac><mrow><mn>19</mn></mrow><mrow><mn>29</mn></mrow></mfrac></math>%</p>", "<p>2<math display=\"inline\"><mfrac><mrow><mn>21</mn></mrow><mrow><mn>29</mn></mrow></mfrac></math>%</p>"],
                    solution_en: "<p>14.(a)<br>2 hours 25 min = 145 min<br>2 hours 30.5 min = 150.5 min<br>error% = <math display=\"inline\"><mfrac><mrow><mn>150</mn><mo>.</mo><mn>5</mn><mo>-</mo><mn>145</mn></mrow><mrow><mn>145</mn></mrow></mfrac></math> &times; 100 = 3<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>23</mn><mn>29</mn></mfrac></math>%</p>",
                    solution_hi: "<p>14.(a)<br>2 घंटे 25 मिनट = 145 मिनट<br>2 घंटे 30.5 मिनट = 150.5 मिनट<br>त्रुटि% = <math display=\"inline\"><mfrac><mrow><mn>150</mn><mo>.</mo><mn>5</mn><mo>-</mo><mn>145</mn></mrow><mrow><mn>145</mn></mrow></mfrac></math> &times; 100 = 3<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>23</mn><mn>29</mn></mfrac></math>%</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "misc",
                    question_en: "<p>15. The price of a car is decreased by 15% and 20% in two successive years respectively.&nbsp;What percentage of the price of the car is decreased after two years ?</p>",
                    question_hi: "<p>15. किसी कार का मूल्य दो क्रमागत वर्षों में क्रमशः 15% और 20% कम हो जाता है। दो वर्ष बाद कार के&nbsp;मूल्य में कितने प्रतिशत की कमी हुई ?</p>",
                    options_en: ["<p>46%</p>", "<p>25%</p>", 
                                "<p>32%</p>", "<p>15%</p>"],
                    options_hi: ["<p>46%</p>", "<p>25%</p>",
                                "<p>32%</p>", "<p>15%</p>"],
                    solution_en: "<p>15.(c) <br>decreased% after 2 years = - 15% - 20% + <math display=\"inline\"><mfrac><mrow><mo>(</mo><mo>-</mo><mn>15</mn><mo>)</mo><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mo>(</mo><mo>-</mo><mn>20</mn><mi>%</mi><mo>)</mo></mrow><mrow><mn>100</mn></mrow></mfrac></math></p>\n<p>= - 35% + 3% = - 32%</p>",
                    solution_hi: "<p>15.(c) <br>दो वर्ष बाद कार के मूल्य में कमी का प्रतिशत = - 15% - 20% + <math display=\"inline\"><mfrac><mrow><mo>(</mo><mo>-</mo><mn>15</mn><mo>)</mo><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mo>(</mo><mo>-</mo><mn>20</mn><mi>%</mi><mo>)</mo></mrow><mrow><mn>100</mn></mrow></mfrac></math></p>\n<p>= - 35% + 3% = - 32%</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>