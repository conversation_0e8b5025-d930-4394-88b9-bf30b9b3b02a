<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">20:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 15</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">15</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 20 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 0,
                end: 13
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 14,
                end: 14
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "17",
                    question_en: "<p>1.<span style=\"font-family: Cambria Math;\"> If <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">X</mi><mo>+</mo><mfrac><mn>1</mn><mi mathvariant=\"normal\">X</mi></mfrac><mo>=</mo><mo>-</mo><mn>14</mn></math> </span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">, and x &lt; -1, what will be the value of <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi mathvariant=\"normal\">X</mi><mn>2</mn></msup><mo>-</mo><mo>&#160;</mo><mfrac><mn>1</mn><mrow><mo>&#160;</mo><msup><mi mathvariant=\"normal\">X</mi><mrow><mn>2</mn><mo>&#160;</mo></mrow></msup></mrow></mfrac></math></span><span style=\"font-family: Cambria Math;\"> ?</span></p>",
                    question_hi: "<p>1.<span style=\"font-family: Cambria Math;\">यदि&nbsp; </span><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">X</mi><mo>+</mo><mfrac><mn>1</mn><mi mathvariant=\"normal\">X</mi></mfrac><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mo>-</mo><mn>14</mn></math></span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">और&nbsp;</span><span style=\"font-family: Cambria Math;\"> x &lt; -1&nbsp; </span><span style=\"font-family: Cambria Math;\">है</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">तो&nbsp;&nbsp;</span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi mathvariant=\"normal\">X</mi><mn>2</mn></msup><mo>-</mo><mo>&#160;</mo><mfrac><mn>1</mn><mrow><mo>&#160;</mo><msup><mi mathvariant=\"normal\">X</mi><mrow><mn>2</mn><mo>&#160;</mo></mrow></msup></mrow></mfrac></math>&nbsp; &nbsp;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">का</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">मान</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">क्या</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">होगा</span><span style=\"font-family: Cambria Math;\">?</span></p>",
                    options_en: ["<p>-112<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math></p>", "<p>112<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math></p>", 
                                "<p>-140<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>2</mn></msqrt></math></p>", "<p>140<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>2</mn></msqrt></math></p>"],
                    options_hi: ["<p>-112<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math></p>", "<p>112<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math></p>",
                                "<p>-140<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>2</mn></msqrt></math></p>", "<p>140<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>2</mn></msqrt></math></p>"],
                    solution_en: "<p>1.(b) <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#215;</mo><mo>+</mo><mfrac><mn>1</mn><mo>&#215;</mo></mfrac><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mo>-</mo><mn>14</mn><mo>&#160;</mo><mi>or</mi><mo>&#160;</mo><mo>&#160;</mo><mfrac><mn>1</mn><mo>&#215;</mo></mfrac><mo>+</mo><mo>&#215;</mo><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mo>-</mo><mn>14</mn></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>Now</mi><mo>&#160;</mo><mfrac><mn>1</mn><mo>&#215;</mo></mfrac><mo>-</mo><mo>&#215;</mo><mo>&#160;</mo><mo>=</mo><msqrt><msup><mrow><mo>(</mo><mo>-</mo><mn>14</mn><mo>)</mo></mrow><mn>2</mn></msup><mo>-</mo><mn>4</mn></msqrt><mo>=</mo><mo>&#160;</mo><mn>8</mn><msqrt><mn>3</mn></msqrt></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msup><mo>&#215;</mo><mn>2</mn></msup></mfrac><mo>-</mo><mo>&#160;</mo><msup><mo>&#215;</mo><mrow><mn>2</mn><mo>&#160;</mo></mrow></msup><mo>=</mo><mo>&#160;</mo><mo>(</mo><mfrac><mn>1</mn><mo>&#215;</mo></mfrac><mo>+</mo><mo>&#215;</mo><mo>)</mo><mo>&#160;</mo><mo>(</mo><mfrac><mn>1</mn><mo>&#215;</mo></mfrac><mo>-</mo><mo>&#215;</mo><mo>)</mo><mo>&#160;</mo></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>=</mo><mo>&#160;</mo><mo>(</mo><mo>-</mo><mn>14</mn><mo>)</mo><mo>(</mo><mn>8</mn><msqrt><mn>3</mn></msqrt><mo>)</mo><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mo>-</mo><mn>112</mn><msqrt><mn>3</mn></msqrt></math></p>",
                    solution_hi: "<p>1.(b)&nbsp; <math style=\"font-family: Verdana;\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#215;</mo><mo>+</mo><mfrac><mn>1</mn><mo>&#215;</mo></mfrac><mo>=</mo><mo>-</mo><mn>14</mn><mo>&#160;</mo><mo>&#160;</mo><mo>&#160;</mo><mi>o</mi><mi>r</mi><mo>&#160;</mo><mo>&#160;</mo><mfrac><mn>1</mn><mo>&#215;</mo></mfrac><mo>+</mo><mo>&#215;</mo><mo>=</mo><mo>&#160;</mo><mo>-</mo><mn>14</mn><mo>&#160;</mo></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#2309;&#2348;</mi><mo>&#160;</mo><mo>,</mo><mo>&#160;</mo><mfrac><mn>1</mn><mo>&#215;</mo></mfrac><mo>-</mo><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mo>&#160;</mo><msqrt><msup><mrow><mo>(</mo><mo>-</mo><mn>14</mn><mo>)</mo></mrow><mn>2</mn></msup><mo>-</mo><mn>4</mn><mo>&#160;</mo></msqrt><mo>=</mo><mo>&#160;</mo><mo>&#160;</mo><mn>8</mn><msqrt><mn>3</mn></msqrt></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msup><mo>&#215;</mo><mn>2</mn></msup></mfrac><mo>-</mo><mo>&#160;</mo><msup><mo>&#215;</mo><mrow><mn>2</mn><mo>&#160;</mo></mrow></msup><mo>&#160;</mo><mo>=</mo><mo>(</mo><mo>&#160;</mo><mfrac><mn>1</mn><mo>&#215;</mo></mfrac><mo>+</mo><mo>&#160;</mo><mo>&#215;</mo><mo>)</mo><mo>&#160;</mo><mo>(</mo><mfrac><mn>1</mn><mo>&#215;</mo></mfrac><mo>-</mo><mo>&#215;</mo><mo>)</mo><mo>&#160;</mo></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>=</mo><mo>(</mo><mo>-</mo><mn>14</mn><mo>)</mo><mo>(</mo><mn>8</mn><msqrt><mn>3</mn></msqrt><mo>)</mo><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mo>-</mo><mn>112</mn><msqrt><mn>3</mn></msqrt></math></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "17",
                    question_en: "<p>2.<span style=\"font-family: Cambria Math;\"> If k <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi mathvariant=\"normal\">k</mi><mn>4</mn></msup></math></span><span style=\"font-family: Cambria Math;\">+ <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msup><mi mathvariant=\"normal\">k</mi><mn>4</mn></msup></mfrac></math> </span><span style=\"font-family: Cambria Math;\">=&nbsp; 47, then what is the value of&nbsp; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi mathvariant=\"normal\">k</mi><mn>3</mn></msup></math> </span><span style=\"font-family: Cambria Math;\">+ <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msup><mi mathvariant=\"normal\">k</mi><mn>3</mn></msup></mfrac></math></span><span style=\"font-family: Cambria Math;\"> ?</span></p>",
                    question_hi: "<p>2.<span style=\"font-family: Cambria Math;\">यदि</span><span style=\"font-family: Cambria Math;\">&nbsp; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi mathvariant=\"normal\">k</mi><mn>4</mn></msup><mo>&#160;</mo><mo>+</mo><mfrac><mn>1</mn><msup><mi mathvariant=\"normal\">k</mi><mn>4</mn></msup></mfrac></math>&nbsp;</span><span style=\"font-family: Cambria Math;\">= 47&nbsp;</span><span style=\"font-family: Cambria Math;\">है</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">तो</span><span style=\"font-family: Cambria Math;\">&nbsp; k<sup>3</sup> </span><span style=\"font-family: Cambria Math;\">+ <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msup><mi mathvariant=\"normal\">k</mi><mn>3</mn></msup></mfrac></math></span><span style=\"font-family: Cambria Math;\">का</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">मान</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">क्या</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">है</span><span style=\"font-family: Cambria Math;\">?</span></p>",
                    options_en: ["<p>4.5</p>", "<p>54</p>", 
                                "<p>18</p>", "<p>9</p>"],
                    options_hi: ["<p>4.5</p>", "<p>54</p>",
                                "<p>18</p>", "<p>9</p>"],
                    solution_en: "<p>2.(c) <span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi mathvariant=\"normal\">k</mi><mn>4</mn></msup><mo>+</mo><mfrac><mn>1</mn><msup><mi mathvariant=\"normal\">k</mi><mrow><mn>4</mn><mo>&#160;</mo></mrow></msup></mfrac><mo>=</mo><mo>&#160;</mo><mn>47</mn><mo>&#160;</mo></math>&hellip;..(Eq. 1)</span><br><span style=\"font-family: Cambria Math;\">Add (+2) in equation(Eq. 1) , we get</span><br><span style=\"font-family: Cambria Math;\">k<sup>2</sup>+ <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msup><mi mathvariant=\"normal\">k</mi><mn>2</mn></msup></mfrac></math> = 7 ,&nbsp;</span><br><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo></math>k +&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi mathvariant=\"normal\">k</mi></mfrac></math> = 3</span><br><span style=\"font-family: Cambria Math;\">Now, k<sup>3</sup> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msup><mi mathvariant=\"normal\">k</mi><mn>3</mn></msup></mfrac></math> &nbsp; </span><span style=\"font-family: Cambria Math;\">= (3)<sup>3</sup>&nbsp; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>-</mo></math> 3<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#215;</mo><mn>3</mn></math>&nbsp; &nbsp;= 18 </span></p>",
                    solution_hi: "<p>2.(c) <span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi mathvariant=\"normal\">k</mi><mn>4</mn></msup></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msup><mi mathvariant=\"normal\">k</mi><mn>4</mn></msup></mfrac></math> =&nbsp; 47 &hellip;..</span><span style=\"font-family: Cambria Math;\">समीकरण</span><span style=\"font-family: Cambria Math;\"> (1)</span><br><span style=\"font-family: Cambria Math;\">समीकरण</span><span style=\"font-family: Cambria Math;\"> (1) </span><span style=\"font-family: Cambria Math;\">में</span><span style=\"font-family: Cambria Math;\"> (+2) </span><span style=\"font-family: Cambria Math;\">जोड़ने</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">पर</span><span style=\"font-family: Cambria Math;\"> , </span><span style=\"font-family: Cambria Math;\">हमे</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">प्राप्त</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">हुआ</span><span style=\"font-family: Cambria Math;\"> ,</span><br><span style=\"font-family: Cambria Math;\">k<sup>2</sup> +&nbsp; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msup><mi mathvariant=\"normal\">k</mi><mn>2</mn></msup></mfrac></math> = 7&nbsp;</span><br><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo></math>k +&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi mathvariant=\"normal\">k</mi></mfrac></math> = 3</span><br><span style=\"font-family: Cambria Math;\">k<sup>3</sup>&nbsp; + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msup><mi mathvariant=\"normal\">k</mi><mn>3</mn></msup></mfrac></math> = (3)<sup>3</sup> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>-</mo><mo>&#160;</mo><mn>3</mn><mo>&#215;</mo><mn>3</mn></math>&nbsp; =&nbsp; 18</span></p>\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\n<p>&nbsp;</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "17",
                    question_en: "<p>3.<span style=\"font-family: Cambria Math;\"> .The simplified form of&nbsp; ( x&nbsp; </span><span style=\"font-family: Cambria Math;\">+ 2y )<sup>3 </sup>+ ( x &ndash;</span><span style=\"font-family: Cambria Math;\">&nbsp; 2y)<sup>3</sup>&nbsp; is:</span></p>",
                    question_hi: "<p>3.<span style=\"font-family: Cambria Math;\"> ( x&nbsp; + 2y )<sup>3</sup> + ( x &ndash;&nbsp; 2y)<sup>3</sup>&nbsp; &nbsp;</span><span style=\"font-family: Cambria Math;\">का</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">सरलीकृत</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">रूप</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">क्या</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">होगा</span><span style=\"font-family: Cambria Math;\">?</span></p>",
                    options_en: ["<p><span style=\"font-weight: 400;\">2x<sup>3</sup></span><span style=\"font-weight: 400;\">&nbsp;</span><span style=\"font-weight: 400;\">+ </span><span style=\"font-weight: 400;\">24xy<sup>2</sup></span></p>", "<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">2x<sup>3</sup></span><span style=\"font-weight: 400;\">&nbsp;</span><span style=\"font-weight: 400;\">- </span><span style=\"font-weight: 400;\">24xy<sup>2</sup></span></span></p>", 
                                "<p>x<sup>3</sup> - 8y<sup><span style=\"font-family: Cambria Math;\">3</span></sup></p>", "<p>x<sup>3</sup> +8y<sup>3</sup></p>"],
                    options_hi: ["<p><span style=\"font-weight: 400;\">2x<sup>3</sup></span><span style=\"font-weight: 400;\">&nbsp;</span><span style=\"font-weight: 400;\">+ </span><span style=\"font-weight: 400;\">24xy<sup>2</sup></span></p>", "<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">2x<sup>3</sup></span><sup><span style=\"font-weight: 400;\">&nbsp;</span></sup><span style=\"font-weight: 400;\">- </span><span style=\"font-weight: 400;\">24xy<sup>2</sup></span></span></p>",
                                "<p>x<sup>3</sup> - 8y<sup>3</sup></p>", "<p>x<sup>3</sup> + 8y<sup>3</sup></p>"],
                    solution_en: "<p>3.(a) <span style=\"font-family: Cambria Math;\">As we know that ,</span><br><span style=\"font-weight: 400;\">(a+b)<sup>3</sup></span><span style=\"font-weight: 400;\"><span style=\"font-family: Cambria Math;\">&nbsp;</span></span><span style=\"font-weight: 400;\">+ (</span><span style=\"font-weight: 400;\">a-b)<sup>3</sup></span><span style=\"font-weight: 400;\"><span style=\"font-family: Cambria Math;\">&nbsp;</span></span><span style=\"font-weight: 400;\"> = </span><span style=\"font-weight: 400;\">2(</span><span style=\"font-weight: 400;\">a<sup>3</sup></span><span style=\"font-weight: 400;\">+3</span><span style=\"font-weight: 400;\">b<sup>2</sup></span><span style=\"font-weight: 400;\">a)</span><br><span style=\"font-family: Cambria Math;\">Now, </span><span style=\"font-weight: 400;\">(x+2y)<sup>3</sup><span style=\"font-family: Cambria Math;\">&nbsp;</span></span><span style=\"font-weight: 400;\">+ (</span><span style=\"font-weight: 400;\">x-2y)<sup>3</sup></span><span style=\"font-weight: 400;\"> = </span><span style=\"font-weight: 400;\">2(</span><span style=\"font-weight: 400;\">x<sup>3</sup></span><span style=\"font-weight: 400;\">+3(</span><span style=\"font-weight: 400;\">2y)<sup>2</sup></span><span style=\"font-weight: 400;\">x)</span><span style=\"font-weight: 400;\"> = </span><span style=\"font-weight: 400;\">2x<sup>3</sup></span><span style=\"font-weight: 400;\">+24x</span><span style=\"font-weight: 400;\">y<sup>2</sup></span></p>",
                    solution_hi: "<p>3.(a) <span style=\"font-family: Cambria Math;\">जैसा</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">कि</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">हम</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">जानते</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">हैं</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">कि</span><span style=\"font-family: Cambria Math;\">,</span><br><span style=\"font-weight: 400;\">(a+b)<sup>3</sup></span><span style=\"font-weight: 400;\"><span style=\"font-family: Cambria Math;\">&nbsp;</span></span><span style=\"font-weight: 400;\">+ (</span><span style=\"font-weight: 400;\">a-b)<sup>3</sup></span><span style=\"font-weight: 400;\"><span style=\"font-family: Cambria Math;\">&nbsp;</span></span><span style=\"font-weight: 400;\"> =&nbsp; </span><span style=\"font-weight: 400;\">2(</span><span style=\"font-weight: 400;\">a<sup>3</sup></span><span style=\"font-weight: 400;\">+3</span><span style=\"font-weight: 400;\">b<sup>2</sup></span><span style=\"font-weight: 400;\">a)</span><br><span style=\"font-weight: 400;\">अब, </span><span style=\"font-weight: 400;\">(x+2y)<sup>3<span style=\"font-family: Cambria Math;\">&nbsp;</span></sup></span><span style=\"font-weight: 400;\">+ (</span><span style=\"font-weight: 400;\">x-2y)<sup>3</sup></span><span style=\"font-weight: 400;\"> = </span><span style=\"font-weight: 400;\">2(</span><span style=\"font-weight: 400;\">x<sup>3</sup></span><span style=\"font-weight: 400;\">+3(</span><span style=\"font-weight: 400;\">2y)<sup>2</sup></span><span style=\"font-weight: 400;\">x)</span><span style=\"font-weight: 400;\"> = </span><span style=\"font-weight: 400;\">2x<sup>3</sup></span><span style=\"font-weight: 400;\">+24x</span><span style=\"font-weight: 400;\">y<sup>2</sup></span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "17",
                    question_en: "<p>4.<span style=\"font-family: Cambria Math;\"> If a &ndash; b = 2 and a<sup>3</sup></span><span style=\"font-family: Cambria Math;\"> &ndash; b<sup>3</sup></span><span style=\"font-family: Cambria Math;\">&nbsp;= 80, then what will be the value</span><span style=\"font-family: Cambria Math;\"> of ab?</span></p>",
                    question_hi: "<p>4.<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">यदि</span><span style=\"font-family: Cambria Math;\">&nbsp; a - b =&nbsp; 2 </span><span style=\"font-family: Cambria Math;\">और</span><span style=\"font-family: Cambria Math;\"> a<sup>3</sup></span><span style=\"font-family: Cambria Math;\">&ndash; b<sup>3</sup></span><span style=\"font-family: Cambria Math;\">&nbsp;=&nbsp; 80 </span><span style=\"font-family: Cambria Math;\">हो</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">तो</span><span style=\"font-family: Cambria Math;\"> ab </span><span style=\"font-family: Cambria Math;\">का</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">मान</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">क्या</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">होगा</span><span style=\"font-family: Cambria Math;\"> ?</span></p>",
                    options_en: ["<p>12</p>", "<p>&minus;24</p>", 
                                "<p>24</p>", "<p>&minus;12</p>"],
                    options_hi: ["<p>12</p>", "<p>&minus;24</p>",
                                "<p>24</p>", "<p>&minus;12</p>"],
                    solution_en: "<p>4.(a) <span style=\"font-family: Cambria Math;\">Formula:-&nbsp; </span><strong>(</strong><strong>a-b)<sup>3</sup> </strong><strong>= </strong><strong>a<sup>3</sup> </strong><strong>-</strong><strong>b<sup>3</sup> </strong><strong>- 3ab(a-b)</strong><br><span style=\"font-weight: 400;\">a - b =2</span><span style=\"font-weight: 400;\"> and&nbsp; </span><span style=\"font-weight: 400;\">a<sup>3</sup><strong>&nbsp;</strong></span><span style=\"font-weight: 400;\">-</span><span style=\"font-weight: 400;\">b<sup>3</sup></span><span style=\"font-weight: 400;\">&nbsp;=80</span><span style=\"font-weight: 400;\"> &hellip;&hellip;(given)</span><br><span style=\"font-weight: 400;\">Now , </span><span style=\"font-weight: 400;\">2<sup>3</sup> </span><span style=\"font-weight: 400;\">=&nbsp; 80-3ab (2)</span><br><span style=\"font-weight: 400;\">ab&nbsp; = 12</span></p>",
                    solution_hi: "<p>4.(a) <span style=\"font-family: Cambria Math;\">सूत्र</span><span style=\"font-family: Cambria Math;\"> :- </span><strong>(</strong><strong>a-b)<sup>3</sup> </strong><strong>= </strong><strong>a<sup>3</sup> </strong><strong>-</strong><strong>b<sup>3</sup> </strong><strong>- 3ab(a-b)</strong><br><span style=\"font-weight: 400;\">a - b = 2</span><span style=\"font-weight: 400;\"> और &nbsp; </span><span style=\"font-weight: 400;\">a<sup>3</sup></span><span style=\"font-weight: 400;\">-</span><span style=\"font-weight: 400;\">b<sup>3</sup></span><span style=\"font-weight: 400;\">&nbsp;= 80</span><span style=\"font-weight: 400;\"> &hellip;&hellip;</span><span style=\"font-weight: 400;\">(दिया गया है)</span><br><span style=\"font-weight: 400;\">अब , </span><span style=\"font-weight: 400;\">2<sup>3</sup> </span><span style=\"font-weight: 400;\">=&nbsp; 80 - 3ab (2)</span><br><span style=\"font-weight: 400;\">ab&nbsp; = 12</span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "17",
                    question_en: "<p>5.<span style=\"font-family: Cambria Math;\"> If &nbsp;<span style=\"font-weight: 400;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup><mo>+</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">a</mi><mn>2</mn></msup></mrow></mfrac></math></span>&nbsp;</span><span style=\"font-family: Cambria Math;\"> =&nbsp; x<sup>2</sup>&nbsp; </span><span style=\"font-family: Cambria Math;\">-&nbsp; a<sup>2</sup> </span><span style=\"font-family: Cambria Math;\">, then the value of x is:</span></p>",
                    question_hi: "<p>5<span style=\"font-family: Cambria Math;\">.</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">यदि&nbsp; </span><span style=\"font-family: Cambria Math;\">&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup><mo>+</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">a</mi><mn>2</mn></msup></mrow></mfrac><mo>=</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">x</mi><mrow><mn>2</mn><mo>&#160;</mo></mrow></msup><mo>-</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">a</mi><mn>2</mn></msup></math> &nbsp;</span><span style=\"font-family: Cambria Math;\"> &nbsp;</span><span style=\"font-family: Cambria Math;\">है </span><span style=\"font-family: Cambria Math;\">&nbsp;</span><span style=\"font-family: Cambria Math;\">तो</span><span style=\"font-family: Cambria Math;\">&nbsp; x&nbsp; </span><span style=\"font-family: Cambria Math;\">का</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">मान</span><span style=\"font-family: Cambria Math;\"> _____ </span><span style=\"font-family: Cambria Math;\">है।</span></p>",
                    options_en: ["<p><span style=\"font-weight: 400;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><mn>1</mn><mo>-</mo><msup><mi mathvariant=\"normal\">a</mi><mn>4</mn></msup><mo>)</mo></mrow><mfrac><mn>1</mn><mn>4</mn></mfrac></msup></math></span></p>", "<p>a</p>", 
                                "<p><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><msup><mi mathvariant=\"normal\">a</mi><mn>4</mn></msup><mo>-</mo><mn>1</mn><mo>)</mo></mrow><mfrac><mn>1</mn><mn>4</mn></mfrac></msup></math></span></p>", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><msup><mi mathvariant=\"normal\">a</mi><mrow><mn>4</mn><mo>&#160;</mo></mrow></msup><mo>+</mo><mo>&#160;</mo><mn>1</mn><mo>)</mo><msup><mo>&#160;</mo><mfrac><mn>1</mn><mn>4</mn></mfrac></msup></math></p>"],
                    options_hi: ["<p><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><mn>1</mn><mo>-</mo><msup><mi>a</mi><mn>4</mn></msup><mo>)</mo></mrow><mfrac><mn>1</mn><mn>4</mn></mfrac></msup></math></span></p>", "<p>a</p>",
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><msup><mi>a</mi><mrow><mn>4</mn><mo>&#160;</mo></mrow></msup><mo>-</mo><mo>&#160;</mo><mn>1</mn><mo>)</mo></mrow><mfrac><mn>1</mn><mn>4</mn></mfrac></msup></math></p>", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><msup><mi>a</mi><mn>4</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>1</mn><mo>&#160;</mo><mo>)</mo></mrow><mfrac><mn>1</mn><mn>4</mn></mfrac></msup></math></p>"],
                    solution_en: "<p>5.(d) <strong><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup><mo>+</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">a</mi><mn>2</mn></msup></mrow></mfrac></math></strong><strong> </strong>=&nbsp; x<sup>2</sup>-&nbsp; a<sup>2</sup>&nbsp;<br><span style=\"font-weight: 400;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi mathvariant=\"normal\">x</mi><mn>4</mn></msup><mo>-</mo><msup><mi mathvariant=\"normal\">a</mi><mn>4</mn></msup></math> </span><span style=\"font-weight: 400;\">=&nbsp; 1</span><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>x</mi><mn>4</mn></msup><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><msup><mi>a</mi><mrow><mn>4</mn><mo>&#160;</mo></mrow></msup><mo>+</mo><mn>1</mn></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>x</mi><mo>&#160;</mo><mo>=</mo><mroot><mrow><msup><mi>a</mi><mn>4</mn></msup><mo>+</mo><mn>1</mn></mrow><mn>4</mn></mroot></math></p>",
                    solution_hi: "<p>5.(d) <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup><mo>+</mo><msup><mi mathvariant=\"normal\">a</mi><mn>2</mn></msup></mrow></mfrac></math> =&nbsp; x<sup>2</sup>-&nbsp; a<sup>2</sup><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>x</mi><mn>4</mn></msup><mo>&#160;</mo><mo>-</mo><msup><mi>a</mi><mrow><mn>4</mn><mo>&#160;</mo></mrow></msup><mo>=</mo><mn>1</mn></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>x</mi><mn>4</mn></msup><mo>&#160;</mo><mo>=</mo><msup><mi>a</mi><mrow><mn>4</mn><mo>&#160;</mo></mrow></msup><mo>+</mo><mn>1</mn></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>x</mi><mo>=</mo><mroot><mrow><msup><mi>a</mi><mrow><mn>4</mn><mo>&#160;</mo></mrow></msup><mo>+</mo><mn>1</mn></mrow><mn>4</mn></mroot></math></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "17",
                    question_en: "<p>6.<span style=\"font-family: Cambria Math;\"> Which of the following statement is correct?<br></span><span style=\"font-family: Cambria Math;\">I. The value of&nbsp; &nbsp;100<sup>2</sup></span><span style=\"font-family: Cambria Math;\">-99<sup>2</sup></span><span style=\"font-family: Cambria Math;\">+ 98<sup>2</sup></span><span style=\"font-family: Cambria Math;\">-97<sup>2</sup></span><span style=\"font-family: Cambria Math;\">+ 96<sup>2</sup></span><span style=\"font-family: Cambria Math;\">-95<sup>2</sup> </span><span style=\"font-family: Cambria Math;\">+ 94<sup>2</sup></span><span style=\"font-family: Cambria Math;\">-93<sup>2</sup> </span><span style=\"font-family: Cambria Math;\">+......+ 22<sup>2</sup> </span><span style=\"font-family: Cambria Math;\">- 21<sup>2</sup></span><span style=\"font-family: Cambria Math;\"> is 4840.</span><br><span style=\"font-family: Cambria Math;\">II. The value of&nbsp; &nbsp;(k&sup2; + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msup><mi>k</mi><mn>2</mn></msup></mfrac></math>)(k +<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi>k</mi></mfrac></math>)(&nbsp; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi mathvariant=\"normal\">k</mi><mn>4</mn></msup><mo>+</mo><mfrac><mn>1</mn><msup><mi mathvariant=\"normal\">k</mi><mn>4</mn></msup></mfrac></math>)(k + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi>k</mi></mfrac></math>)</span><span style=\"font-family: Cambria Math;\">(<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi mathvariant=\"normal\">k</mi><mn>4</mn></msup><mo>-</mo><mfrac><mn>1</mn><mrow><msup><mi mathvariant=\"normal\">k</mi><mn>4</mn></msup><mo>&#160;</mo></mrow></mfrac></math>)&nbsp; &nbsp;</span><span style=\"font-family: Cambria Math;\">is &nbsp; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi mathvariant=\"normal\">k</mi><mn>16</mn></msup><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mfrac><mn>1</mn><msup><mi mathvariant=\"normal\">k</mi><mn>16</mn></msup></mfrac></math> .&nbsp; &nbsp;</span></p>",
                    question_hi: "<p>6.<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">निम्नलिखित</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">में</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">से</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">कौन</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">सा</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">कथन</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">सही</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">है</span><span style=\"font-family: Cambria Math;\">?</span><br><span style=\"font-family: Cambria Math;\">I.100<sup>2</sup>-99<sup>2</sup>+ 98<sup>2</sup>-97<sup>2</sup>+ 96<sup>2</sup>-95<sup>2</sup> + 94<sup>2</sup>-93<sup>2</sup> +......+ 22<sup>2</sup> - 21<sup>2</sup> </span><span style=\"font-weight: 400;\">का मान 4840 है।</span><br><span style=\"font-family: Cambria Math;\">II. ( k&sup2; + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msup><mi mathvariant=\"normal\">k</mi><mn>2</mn></msup></mfrac></math>)(k&nbsp; - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi>k</mi></mfrac></math>)(<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi mathvariant=\"normal\">k</mi><mrow><mn>4</mn><mo>&#160;</mo></mrow></msup><mo>+</mo><mfrac><mn>1</mn><msup><mi mathvariant=\"normal\">k</mi><mn>4</mn></msup></mfrac></math>)(k+ <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi>k</mi></mfrac></math>)(<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi mathvariant=\"normal\">k</mi><mrow><mn>4</mn><mo>&#160;</mo></mrow></msup><mo>-</mo><mfrac><mn>1</mn><msup><mi mathvariant=\"normal\">k</mi><mn>4</mn></msup></mfrac></math>) </span><span style=\"font-family: Cambria Math;\">का </span><span style=\"font-family: Cambria Math;\">(<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi mathvariant=\"normal\">k</mi><mn>16</mn></msup><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mfrac><mn>1</mn><msup><mi mathvariant=\"normal\">k</mi><mn>16</mn></msup></mfrac></math>)&nbsp; </span><span style=\"font-family: Cambria Math;\">है</span><span style=\"font-family: Cambria Math;\"> |</span></p>",
                    options_en: ["<p>Neither I nor II</p>", "<p>Only II</p>", 
                                "<p>Only I</p>", "<p>Both I a<span style=\"font-family: Cambria Math;\">nd II</span></p>"],
                    options_hi: ["<p><span style=\"font-family: Cambria Math;\">ना</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">ही</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">।</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">ना</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">ही</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">॥</span></p>", "<p><span style=\"font-family: Cambria Math;\">केवल</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">॥</span></p>",
                                "<p><span style=\"font-family: Cambria Math;\">केवल</span><span style=\"font-family: Cambria Math;\"> I</span></p>", "<p><span style=\"font-family: Cambria Math;\">।</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">तथा</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">।</span><span style=\"font-family: Cambria Math;\">I </span><span style=\"font-family: Cambria Math;\">दोनों</span></p>"],
                    solution_en: "<p>6.(c) <span style=\"font-family: Cambria Math;\">Statement 1.&nbsp; &nbsp;</span><strong>&nbsp;&rArr;</strong><span style=\"font-weight: 400;\">100<sup>2</sup><span style=\"font-family: Cambria Math;\"> </span></span><span style=\"font-weight: 400;\">- </span><span style=\"font-weight: 400;\">99<sup>2</sup><span style=\"font-family: Cambria Math;\"> </span></span><span style=\"font-weight: 400;\">+ </span><span style=\"font-weight: 400;\">98<sup>2</sup><span style=\"font-family: Cambria Math;\">&nbsp;</span></span><span style=\"font-weight: 400;\">- </span><span style=\"font-weight: 400;\">97<sup>2</sup></span><span style=\"font-weight: 400;\">.......</span><span style=\"font-weight: 400;\">+&nbsp; 22<span style=\"font-family: Cambria Math;\"><sup>2</sup> </span></span><span style=\"font-weight: 400;\">- </span><span style=\"font-weight: 400;\">21<sup><span style=\"font-family: Cambria Math;\">2</span></sup></span><br><span style=\"font-weight: 400;\">&nbsp;(100+99)(100-99) + (98+97)(98-97) &hellip;&hellip;&hellip;+(22+21)(22-21)</span><br><span style=\"font-weight: 400;\">100+99+98+97&hellip;&hellip;.22+21</span><br><span style=\"font-weight: 400;\">Sum = </span><span style=\"font-weight: 400;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>n</mi><mn>2</mn></mfrac></math></span><span style=\"font-weight: 400;\">&times;( a + l)</span><span style=\"font-weight: 400;\">&nbsp; = </span><span style=\"font-weight: 400;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>80</mn><mn>2</mn></mfrac></math> </span><span style=\"font-weight: 400;\">(100 + 21)</span><span style=\"font-weight: 400;\"> =&nbsp; 40&times;121 =&nbsp; 4840&nbsp;</span><br><strong>Statement 2.&nbsp;&nbsp;</strong><strong> <span style=\"font-family: Cambria Math;\">(k<sup>2</sup> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msup><mi mathvariant=\"normal\">k</mi><mn>2</mn></msup></mfrac></math>)( k&nbsp; -&nbsp; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><mi mathvariant=\"normal\">k</mi><mo>&#160;</mo></mrow></mfrac></math>) (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi mathvariant=\"normal\">k</mi><mn>4</mn></msup></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msup><mi mathvariant=\"normal\">k</mi><mn>4</mn></msup></mfrac></math>)(k + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi mathvariant=\"normal\">k</mi></mfrac></math>)</span></strong><strong><span style=\"font-family: Cambria Math;\">(&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi mathvariant=\"normal\">k</mi><mn>4</mn></msup><mo>-</mo><mfrac><mn>1</mn><msup><mi mathvariant=\"normal\">k</mi><mn>4</mn></msup></mfrac></math>)</span></strong><br><strong><span style=\"font-family: Cambria Math;\">=&nbsp; ( k<sup>2</sup> +<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msup><mi mathvariant=\"normal\">k</mi><mn>2</mn></msup></mfrac></math>)( k<sup>2</sup></span></strong><span style=\"font-family: Cambria Math;\">&nbsp; </span><strong><span style=\"font-family: Cambria Math;\">-&nbsp; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msup><mi mathvariant=\"normal\">k</mi><mn>2</mn></msup></mfrac></math>)(<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi mathvariant=\"normal\">k</mi><mn>4</mn></msup><mo>+</mo><mfrac><mn>1</mn><msup><mi mathvariant=\"normal\">k</mi><mn>4</mn></msup></mfrac></math> )(<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi mathvariant=\"normal\">k</mi><mn>4</mn></msup><mo>-</mo><mfrac><mn>1</mn><msup><mi mathvariant=\"normal\">k</mi><mn>4</mn></msup></mfrac></math>)</span></strong><br><strong><span style=\"font-family: Cambria Math;\">=&nbsp; (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi mathvariant=\"normal\">k</mi><mrow><mn>4</mn><mo>&#160;</mo></mrow></msup><mo>-</mo><mo>&#160;</mo><mfrac><mn>1</mn><msup><mi mathvariant=\"normal\">k</mi><mrow><mn>4</mn><mo>&#160;</mo></mrow></msup></mfrac></math>)( <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi mathvariant=\"normal\">k</mi><mrow><mn>8</mn><mo>&#160;</mo></mrow></msup><mo>-</mo><mo>&#160;</mo><mfrac><mn>1</mn><msup><mi mathvariant=\"normal\">k</mi><mn>8</mn></msup></mfrac></math>)&nbsp; ........(Eq.1)</span></strong><br><span style=\"font-weight: 400;\">Now,&nbsp; &nbsp;(<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi mathvariant=\"normal\">k</mi><mn>16</mn></msup><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mfrac><mn>1</mn><msup><mi mathvariant=\"normal\">k</mi><mrow><mn>16</mn><mo>&#160;</mo></mrow></msup></mfrac></math>)&nbsp; = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#160;</mo><mo>(</mo><msup><mi mathvariant=\"normal\">k</mi><mn>8</mn></msup><mo>-</mo><mfrac><mn>1</mn><msup><mi mathvariant=\"normal\">k</mi><mn>8</mn></msup></mfrac><mo>)</mo><mo>(</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">k</mi><mn>8</mn></msup><mo>+</mo><mfrac><mn>1</mn><msup><mi mathvariant=\"normal\">k</mi><mn>8</mn></msup></mfrac><mo>)</mo></math>&nbsp; &nbsp;......<strong><span style=\"font-family: Cambria Math;\">(Eq.2)</span></strong></span><br><span style=\"font-weight: 400;\">Where , <strong><span style=\"font-family: Cambria Math;\">(Eq. 1)&nbsp; &nbsp;&ne;&nbsp; &nbsp;</span></strong><strong><span style=\"font-family: Cambria Math;\">(Eq. 2)</span></strong></span></p>",
                    solution_hi: "<p>6.(c) <strong>कथन 1.&nbsp;</strong><br><strong>&rArr; </strong><span style=\"font-weight: 400;\">100<sup>2</sup><span style=\"font-family: Cambria Math;\"><sup>&nbsp;</sup>&nbsp;</span></span><span style=\"font-weight: 400;\">-&nbsp; </span><span style=\"font-weight: 400;\">99<sup>2</sup><span style=\"font-family: Cambria Math;\">&nbsp;&nbsp;</span></span><span style=\"font-weight: 400;\">+&nbsp; </span><span style=\"font-weight: 400;\">98<sup>2</sup><span style=\"font-family: Cambria Math;\">&nbsp;&nbsp;</span></span><span style=\"font-weight: 400;\">-&nbsp; </span><span style=\"font-weight: 400;\">97<sup>2</sup></span><span style=\"font-weight: 400;\">.......</span><span style=\"font-weight: 400;\">+&nbsp; 22<sup>2</sup><span style=\"font-family: Cambria Math;\">&nbsp;&nbsp;</span></span><span style=\"font-weight: 400;\">-&nbsp; </span><span style=\"font-weight: 400;\">21<sup>2</sup></span><br><span style=\"font-weight: 400;\">&nbsp;(100+99)(100-99) + (98+97)(98-97) &hellip;&hellip;&hellip;+(22+21)(22-21)</span><br><span style=\"font-weight: 400;\">100+99+98+97&hellip;&hellip;.22+21</span><br><span style=\"font-weight: 400;\">योग =&nbsp;</span><span style=\"font-weight: 400;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"normal\">n</mi><mn>2</mn></mfrac></math></span><span style=\"font-weight: 400;\">&times;( a + l)</span><span style=\"font-weight: 400;\">&nbsp; = </span><span style=\"font-weight: 400;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>80</mn><mn>2</mn></mfrac></math> </span><span style=\"font-weight: 400;\">(100 + 21)</span><span style=\"font-weight: 400;\"> =&nbsp; 40 &times; 121 =&nbsp; 4840&nbsp;</span><br><span style=\"font-family: Cambria Math;\">कथन</span><span style=\"font-family: Cambria Math;\"> 2. &nbsp;&nbsp; &rArr;&nbsp; (k<sup>2</sup> +&nbsp; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msup><mi mathvariant=\"normal\">k</mi><mn>2</mn></msup></mfrac></math>)( k&nbsp; &ndash; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi mathvariant=\"normal\">k</mi></mfrac></math>)( <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi mathvariant=\"normal\">k</mi><mrow><mn>4</mn><mo>&#160;</mo></mrow></msup><mo>+</mo><mfrac><mn>1</mn><msup><mi mathvariant=\"normal\">k</mi><mn>4</mn></msup></mfrac></math>)(k +&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><mi mathvariant=\"normal\">k</mi><mo>&#160;</mo></mrow></mfrac></math>)</span><span style=\"font-family: Cambria Math;\">(<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi mathvariant=\"normal\">k</mi><mrow><mn>4</mn><mo>&#160;</mo></mrow></msup><mo>-</mo><mo>&#160;</mo><mfrac><mn>1</mn><msup><mi mathvariant=\"normal\">k</mi><mn>4</mn></msup></mfrac></math>)&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;</span><br><span style=\"font-family: Cambria Math;\">= (k<sup>2 </sup>+ <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><msup><mi mathvariant=\"normal\">k</mi><mn>2</mn></msup><mo>&#160;</mo></mrow></mfrac></math>)( k<sup>2</sup> - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msup><mi mathvariant=\"normal\">k</mi><mn>2</mn></msup></mfrac></math>)(<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi mathvariant=\"normal\">k</mi><mn>4</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mfrac><mn>1</mn><msup><mi mathvariant=\"normal\">k</mi><mn>4</mn></msup></mfrac></math>)(<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi mathvariant=\"normal\">k</mi><mn>4</mn></msup><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mfrac><mn>1</mn><msup><mi mathvariant=\"normal\">k</mi><mn>4</mn></msup></mfrac></math>)&nbsp;</span><br><span style=\"font-family: Cambria Math;\">= (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi mathvariant=\"normal\">k</mi><mn>4</mn></msup><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mfrac><mn>1</mn><msup><mi mathvariant=\"normal\">k</mi><mn>4</mn></msup></mfrac></math>)&nbsp; (&nbsp; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi mathvariant=\"normal\">k</mi><mrow><mn>8</mn><mo>&#160;</mo></mrow></msup><mo>-</mo><mo>&#160;</mo><mfrac><mn>1</mn><msup><mi mathvariant=\"normal\">k</mi><mn>8</mn></msup></mfrac></math>).....&hellip;..समी(1)</span><br><span style=\"font-family: Cambria Math;\">अब &nbsp;&nbsp; ( <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi mathvariant=\"normal\">k</mi><mrow><mn>16</mn><mo>&#160;</mo></mrow></msup><mo>-</mo><mfrac><mn>1</mn><msup><mi mathvariant=\"normal\">k</mi><mn>16</mn></msup></mfrac></math>) </span><span style=\"font-family: Cambria Math;\">=&nbsp; (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi mathvariant=\"normal\">k</mi><mrow><mn>8</mn><mo>&#160;</mo></mrow></msup><mo>-</mo><mo>&#160;</mo><mfrac><mn>1</mn><msup><mi mathvariant=\"normal\">k</mi><mn>8</mn></msup></mfrac></math>)</span><span style=\"font-family: Cambria Math;\">(<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi mathvariant=\"normal\">k</mi><mn>8</mn></msup><mo>&#160;</mo><mo>+</mo><mfrac><mn>1</mn><msup><mi mathvariant=\"normal\">k</mi><mn>8</mn></msup></mfrac></math>) &hellip;.. </span><span style=\"font-family: Cambria Math;\">समी</span><span style=\"font-family: Cambria Math;\">(2)</span><br><span style=\"font-family: Cambria Math;\">जहां</span><span style=\"font-family: Cambria Math;\"> , </span><span style=\"font-family: Cambria Math;\">समी</span><span style=\"font-family: Cambria Math;\">(1) &nbsp;&ne;&nbsp; </span><span style=\"font-family: Cambria Math;\">सम</span><span style=\"font-family: Cambria Math;\">ी</span><span style=\"font-family: Cambria Math;\">(2)</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "17",
                    question_en: "<p>7.<span style=\"font-family: Cambria Math;\"> Select the correct algebraic expression.</span></p>",
                    question_hi: "<p>7.<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">सही</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">बीजीय</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">व्यंजक</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">का</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">चयन</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">कीजिए।</span></p>",
                    options_en: ["<p><span style=\"font-weight: 400;\">ab &ndash; a &ndash; b + 1 = (a &ndash; 1) (b &ndash; 1)</span></p>", "<p><span style=\"font-weight: 400;\">ab + a &ndash; b + 1 = (1 &ndash; a) (1 &ndash; b) (1 &ndash; a) (1 + b)</span></p>", 
                                "<p><span style=\"font-weight: 400;\">ab &ndash; a &ndash; b + 1 = (1 &ndash; a) (b &ndash; 1)</span></p>", "<p><span style=\"font-weight: 400;\">&nbsp;ab &ndash; a &ndash; b + 1 = (a &ndash; 1) (1 &ndash; b)</span></p>"],
                    options_hi: ["<p><span style=\"font-weight: 400;\">ab &ndash; a &ndash; b + 1 = (a &ndash; 1)(b &ndash; 1)</span></p>", "<p><span style=\"font-weight: 400;\">&nbsp;ab + a &ndash; b + 1 = (1 &ndash; a) (1 &ndash; b) (1 &ndash; a) (1 + b)</span></p>",
                                "<p><span style=\"font-weight: 400;\">ab &ndash; a &ndash; b + 1 = (1 &ndash; a) (b &ndash; 1)</span></p>", "<p><span style=\"font-weight: 400;\">&nbsp;ab &ndash; a &ndash; b + 1 = (a &ndash; 1) (1 &ndash; b)</span></p>"],
                    solution_en: "<p>7.(a) <span style=\"font-family: Cambria Math;\">Clearly , option &lsquo;a&rsquo; is the correct </span><span style=\"font-family: Cambria Math;\">algebraic expression.</span><br><span style=\"font-weight: 400;\">(a-1)</span>(b-1) = ab -a -b +1</p>",
                    solution_hi: "<p>7.(a) <span style=\"font-family: Cambria Math;\">स्पष्ट</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">रूप</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">से</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">विकल्प</span><span style=\"font-family: Cambria Math;\"> \'a\' </span><span style=\"font-family: Cambria Math;\">सही</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">बीजगणितीय</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">व्यंजक</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">है।</span><br><span style=\"font-weight: 400;\">(a-1)</span>(b-1) = ab - a - b +1</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">8.</span><span style=\"font-family: Cambria Math;\"> If x</span><span style=\"font-family: Cambria Math;\">&sup2;</span><span style=\"font-family: Cambria Math;\"> + y&sup2; </span><span style=\"font-family: Cambria Math;\">&nbsp;+ z&sup2; </span><span style=\"font-family: Cambria Math;\">&nbsp;= xy + yz + zx and x = 1, then find the value of </span><span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>&#160;</mo><mn>10</mn><msup><mi>x</mi><mn>4</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>5</mn><msup><mi>y</mi><mn>4</mn></msup><mo>&#160;</mo><mo>+</mo><mn>7</mn><msup><mi>z</mi><mn>4</mn></msup></mrow><mrow><mo>&#160;</mo><mn>13</mn><msup><mi>x</mi><mn>2</mn></msup><msup><mi>y</mi><mn>2</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>6</mn><msup><mi>y</mi><mn>2</mn></msup><msup><mi>z</mi><mn>2</mn></msup><mo>&#160;</mo><mo>+</mo><mn>3</mn><mi>z</mi><msup><mo>&#160;</mo><mn>2</mn></msup><msup><mi>x</mi><mn>2</mn></msup></mrow></mfrac></math></span></span></p>",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">8.</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">यदि</span><span style=\"font-family: Cambria Math;\"> x&sup2; + y&sup2; &nbsp;+ z&sup2; &nbsp;= xy + yz + zx</span><span style=\"font-family: Cambria Math;\">&nbsp;</span><span style=\"font-family: Cambria Math;\">और</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">x =1 </span><span style=\"font-family: Cambria Math;\">है</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">तो</span><span style=\"font-family: Cambria Math;\">&nbsp; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>10</mn><msup><mi>x</mi><mn>4</mn></msup><mo>+</mo><mo>&#160;</mo><mn>5</mn><msup><mi>y</mi><mn>4</mn></msup><mo>&#160;</mo><mo>+</mo><mn>7</mn><msup><mi>z</mi><mn>4</mn></msup><mo>&#160;</mo></mrow><mrow><mn>13</mn><mo>&#160;</mo><msup><mi>x</mi><mn>2</mn></msup><msup><mi>y</mi><mn>2</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>6</mn><msup><mi>y</mi><mn>2</mn></msup><msup><mi>z</mi><mn>2</mn></msup><mo>&#160;</mo><mo>+</mo><mn>3</mn><msup><mi>z</mi><mn>2</mn></msup><msup><mi>x</mi><mn>2</mn></msup></mrow></mfrac></math>&nbsp; &nbsp;</span><span style=\"font-family: Cambria Math;\">का</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">मान</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">ज्ञात</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">कीजिए।</span><span style=\"font-family: Cambria Math;\"> </span></p>",
                    options_en: ["<p>2</p>", "<p>0</p>", 
                                "<p>-1</p>", "<p>1</p>"],
                    options_hi: ["<p>2</p>", "<p>0</p>",
                                "<p>-1</p>", "<p>1</p>"],
                    solution_en: "<p>8.(d) <span style=\"font-family: Cambria Math;\">x = 1 &hellip;..(given)</span><br><span style=\"font-family: Cambria Math;\">&nbsp;x&sup2; + y&sup2; &nbsp;+ z&sup2; &nbsp;= xy + yz + zx ....</span><span style=\"font-family: Cambria Math;\">&hellip;..(given)</span><br><span style=\"font-family: Cambria Math;\">Put , y = 1 and z = 1 . which satisfies the given condition.</span><br><span style=\"font-family: Cambria Math;\">&nbsp;<span style=\"font-weight: 400;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>10</mn><msup><mi>x</mi><mn>4</mn></msup><mo>+</mo><mn>5</mn><msup><mi>y</mi><mn>4</mn></msup><mo>+</mo><mn>7</mn><msup><mi>z</mi><mn>4</mn></msup></mrow><mrow><mn>13</mn><mo>&#160;</mo><msup><mi>x</mi><mn>2</mn></msup><msup><mi>y</mi><mn>2</mn></msup><mo>+</mo><mn>6</mn><msup><mi>y</mi><mn>2</mn></msup><msup><mi>z</mi><mn>2</mn></msup><mo>+</mo><mn>3</mn><msup><mi>z</mi><mn>2</mn></msup><msup><mi>x</mi><mn>2</mn></msup></mrow></mfrac></math>&nbsp; </span>=&nbsp; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>10</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>5</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>7</mn></mrow><mrow><mn>13</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>6</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>3</mn><mo>&#160;</mo></mrow></mfrac></math> </span><span style=\"font-family: Cambria Math;\">=&nbsp; 1</span></p>",
                    solution_hi: "<p>8.(d) <span style=\"font-family: Cambria Math;\">x = 1 &hellip;..(</span><span style=\"font-family: Cambria Math;\">दिया</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">गया</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">है</span><span style=\"font-family: Cambria Math;\">)</span><br><span style=\"font-family: Cambria Math;\">&nbsp; x&sup2; + y&sup2; &nbsp;+ z&sup2; &nbsp;= xy + yz + zx&nbsp;</span><span style=\"font-family: Cambria Math;\">&hellip;..(</span><span style=\"font-family: Cambria Math;\">दिया</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">गया</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">है</span><span style=\"font-family: Cambria Math;\">)</span><br><span style=\"font-family: Cambria Math;\">&nbsp;y = 1 </span><span style=\"font-family: Cambria Math;\">और</span><span style=\"font-family: Cambria Math;\"> z = 1 </span><span style=\"font-family: Cambria Math;\">रखने</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">पर</span><span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Cambria Math;\">जो</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">दी</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">गई</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">शर्त</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">को</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">पूरा</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">करता</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">है।</span><br><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>10</mn><msup><mi>x</mi><mn>4</mn></msup><mo>+</mo><mo>&#160;</mo><mn>5</mn><msup><mi>y</mi><mn>4</mn></msup><mo>&#160;</mo><mo>+</mo><mn>7</mn><msup><mi>z</mi><mn>4</mn></msup><mo>&#160;</mo></mrow><mrow><mn>13</mn><mo>&#160;</mo><msup><mi>x</mi><mn>2</mn></msup><msup><mi>y</mi><mn>2</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>6</mn><msup><mi>y</mi><mn>2</mn></msup><msup><mi>z</mi><mn>2</mn></msup><mo>&#160;</mo><mo>+</mo><mn>3</mn><msup><mi>z</mi><mn>2</mn></msup><msup><mi>x</mi><mn>2</mn></msup></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>10</mn><mo>+</mo><mn>5</mn><mo>+</mo><mn>7</mn></mrow><mrow><mn>13</mn><mo>+</mo><mn>6</mn><mo>+</mo><mn>3</mn></mrow></mfrac></math></span><span style=\"font-family: Cambria Math;\">&nbsp; =&nbsp; 1</span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">9. </span><span style=\"font-family: Cambria Math;\">If&nbsp; &nbsp;x +&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><mn>2</mn><mi>x</mi></mrow></mfrac></math> </span><span style=\"font-family: Cambria Math;\">= 3, then evaluate&nbsp; &nbsp;8x</span><span style=\"font-family: Cambria Math;\">&sup3;</span><span style=\"font-family: Cambria Math;\"> + </span><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msup><mi mathvariant=\"normal\">x</mi><mn>3</mn></msup></mfrac></math>&nbsp; .</span></p>",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">9. </span><span style=\"font-family: Cambria Math;\">यदि&nbsp;&nbsp;</span><span style=\"font-family: Cambria Math;\"> x&nbsp; + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><mn>2</mn><mi mathvariant=\"normal\">x</mi></mrow></mfrac></math> </span><span style=\"font-family: Cambria Math;\">=&nbsp; 3 </span><span style=\"font-family: Cambria Math;\">है</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">तो</span><span style=\"font-family: Cambria Math;\">&nbsp; &nbsp;8x&sup3;</span><span style=\"font-family: Cambria Math;\"> +&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msup><mi mathvariant=\"normal\">x</mi><mn>3</mn></msup></mfrac></math>&nbsp;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">का</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">मान</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">ज्ञात</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">कीजिए।</span><span style=\"font-family: Cambria Math;\"> </span></p>",
                    options_en: ["<p>212</p>", "<p>216</p>", 
                                "<p>180</p>", "<p>196</p>"],
                    options_hi: ["<p>212</p>", "<p>216</p>",
                                "<p>180</p>", "<p>196</p>"],
                    solution_en: "<p>9.(c)&nbsp;x&nbsp; <strong>+ <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><mn>2</mn><mi mathvariant=\"normal\">x</mi></mrow></mfrac></math> &nbsp;</strong><strong>=</strong> &nbsp;3 &nbsp; <span style=\"font-weight: 400;\">&nbsp;&hellip;....(Eq. 1)</span><br><span style=\"font-weight: 400;\">2x+</span><span style=\"font-weight: 400;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi>x</mi></mfrac></math> </span><span style=\"font-weight: 400;\">=&nbsp; 6</span><span style=\"font-weight: 400;\">&nbsp;</span><br><span style=\"font-weight: 400;\">Now,&nbsp; </span><span style=\"font-weight: 400;\">( </span><span style=\"font-weight: 400;\">2x+</span><span style=\"font-weight: 400;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi>x</mi></mfrac></math> </span><span style=\"font-weight: 400;\">)<sup>3</sup></span><sup><span style=\"font-weight: 400;\">&nbsp;</span></sup><span style=\"font-weight: 400;\"> =&nbsp; </span>8x<sup>3<span style=\"font-weight: 400;\">&nbsp;</span></sup>+ <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msup><mi>x</mi><mn>3</mn></msup></mfrac></math> <span style=\"font-weight: 400;\">+ 6</span><strong> ( </strong><span style=\"font-weight: 400;\">2x&nbsp; + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi>x</mi></mfrac></math> </span><strong>)</strong><br>6<sup>3</sup><span style=\"font-weight: 400;\">&nbsp;</span>&nbsp;<span style=\"font-weight: 400;\">- 6&times;6&nbsp; =</span> &nbsp;8x<sup>3</sup><span style=\"font-weight: 400;\">&nbsp;</span><strong>+&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msup><mi>x</mi><mn>3</mn></msup></mfrac></math> &nbsp;&nbsp;</strong><br><span style=\"font-weight: 400;\">Hence,</span>&nbsp; &nbsp;8x<sup>3</sup><span style=\"font-weight: 400;\">&nbsp;+ <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msup><mi>x</mi><mn>3</mn></msup></mfrac></math> </span><span style=\"font-weight: 400;\"> = 216 - 36&nbsp; = </span>180</p>",
                    solution_hi: "<p>9.(c)&nbsp;x <strong>+ <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><mn>2</mn><mi>x</mi></mrow></mfrac></math> </strong><strong>=</strong> 3 &nbsp; <span style=\"font-weight: 400;\">&nbsp;&hellip;.. (Eq. 1)</span><br><span style=\"font-weight: 400;\">2x + </span><span style=\"font-weight: 400;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi>x</mi></mfrac></math> </span><span style=\"font-weight: 400;\">=&nbsp; 6</span><br><span style=\"font-weight: 400;\">अब,&nbsp;</span><br><span style=\"font-weight: 400;\">( </span><span style=\"font-weight: 400;\">2x + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi>x</mi></mfrac></math></span><span style=\"font-weight: 400;\">)<sup>3</sup></span><span style=\"font-weight: 400;\">&nbsp; =&nbsp; </span>8x<sup>3</sup><span style=\"font-weight: 400;\">&nbsp;&nbsp;</span>+ <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msup><mi>x</mi><mn>3</mn></msup></mfrac></math>&nbsp;<span style=\"font-weight: 400;\">+ 6</span><strong> ( </strong><span style=\"font-weight: 400;\">2x + </span><span style=\"font-weight: 400;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi>x</mi></mfrac></math></span><strong>)</strong><br>6<sup>3</sup>&nbsp;<span style=\"font-weight: 400;\">- 6&times;6&nbsp;</span><strong> =</strong>&nbsp; 8x<sup>3</sup><span style=\"font-weight: 400;\">&nbsp;</span><strong>+<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msup><mi>x</mi><mn>3</mn></msup></mfrac></math></strong><br><span style=\"font-weight: 400;\">अत,</span>&nbsp; 8x<sup>3</sup><span style=\"font-weight: 400;\">&nbsp;</span><strong>+</strong>&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><mo>&#160;</mo><msup><mi>x</mi><mn>3</mn></msup></mrow></mfrac></math><strong>&nbsp;</strong><span style=\"font-weight: 400;\">=&nbsp; 216 - 36&nbsp;</span><strong> =&nbsp;</strong> 180</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">10.&nbsp; </span><span style=\"font-family: Cambria Math;\">If&nbsp; &nbsp;x&sup2;&nbsp;</span><span style=\"font-family: Cambria Math;\"> - 2xy&nbsp; =&nbsp; 84 and&nbsp; x - y&nbsp; &nbsp;=&nbsp; - 10, then the value of y is:</span></p>",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">10. </span><span style=\"font-family: Cambria Math;\">यदि&nbsp;&nbsp;</span><span style=\"font-family: Cambria Math;\"> x&sup2; - 2xy&nbsp; = 84&nbsp; &nbsp;</span><span style=\"font-family: Cambria Math;\">और&nbsp;&nbsp;</span><span style=\"font-family: Cambria Math;\"> x - y = -10&nbsp; </span><span style=\"font-family: Cambria Math;\">है</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">तो</span><span style=\"font-family: Cambria Math;\"> y </span><span style=\"font-family: Cambria Math;\">का</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">मान</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">क्या</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">है</span><span style=\"font-family: Cambria Math;\">?</span></p>",
                    options_en: ["<p>2</p>", "<p>1</p>", 
                                "<p>4</p>", "<p>3</p>"],
                    options_hi: ["<p>2</p>", "<p>1</p>",
                                "<p>4</p>", "<p>3</p>"],
                    solution_en: "<p>10.(c)&nbsp; <span style=\"font-family: Cambria Math;\">x - y&nbsp; =&nbsp; - 10</span><br><span style=\"font-family: Cambria Math;\">x&sup2;&nbsp; -&nbsp; 2xy&nbsp; =&nbsp; 84</span><br><span style=\"font-family: Cambria Math;\">Adding&nbsp; y&sup2; </span><span style=\"font-family: Cambria Math;\">&nbsp;both sides</span><br><span style=\"font-family: Cambria Math;\">&nbsp;x&sup2; - 2xy&nbsp; +&nbsp; y&sup2;&nbsp; =&nbsp; 84 &nbsp;+&nbsp; y&sup2;</span><br><span style=\"font-family: Cambria Math;\">(<span style=\"font-weight: 400;\">x - y )</span><span style=\"font-weight: 400;\">&sup2; </span><span style=\"font-weight: 400;\">=&nbsp; 84 + y&sup2;</span></span><br><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">y&sup2; =&nbsp; 100&nbsp; -&nbsp; 84 = 16</span></span><br><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">y&nbsp; =&nbsp; 4</span></span></p>",
                    solution_hi: "<p>10.(c)&nbsp; <span style=\"font-family: Cambria Math;\">x - y&nbsp; =&nbsp; - 10</span><br><span style=\"font-family: Cambria Math;\">x&sup2;&nbsp; -&nbsp; 2xy&nbsp; =&nbsp; 84</span><br><span style=\"font-weight: 400;\">दोनों पक्षों में </span><span style=\"font-weight: 400;\">y</span><span style=\"font-weight: 400;\">&sup2; </span><span style=\"font-weight: 400;\"> जोड़ने पर&nbsp;</span><br><span style=\"font-family: Cambria Math;\">&nbsp;x&sup2;&nbsp; -&nbsp; 2xy&nbsp; + y&sup2;&nbsp; =&nbsp; 84 &nbsp;+&nbsp; y&sup2;</span><br><span style=\"font-family: Cambria Math;\">(<span style=\"font-weight: 400;\">x - y )</span><span style=\"font-weight: 400;\">&sup2; </span><span style=\"font-weight: 400;\">=&nbsp; 84 &nbsp;+ y&sup2;</span></span><br><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">y&sup2;&nbsp; =&nbsp; 100&nbsp; -&nbsp; 84&nbsp; &nbsp;=&nbsp; 16</span></span><br><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">y = 4</span></span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">11.If&nbsp; &nbsp;x&nbsp; + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi mathvariant=\"normal\">x</mi></mfrac></math> &nbsp;</span><span style=\"font-family: Cambria Math;\">=&nbsp; </span><span style=\"font-family: Cambria Math;\">1, then the value of&nbsp; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>x</mi><mn>12</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><msup><mi>x</mi><mn>9</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><msup><mi>x</mi><mn>6</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><msup><mi>x</mi><mrow><mn>3</mn><mo>&#160;</mo></mrow></msup><mo>+</mo><mo>&#160;</mo><mn>1</mn></math>&nbsp; &nbsp;&nbsp;</span><span style=\"font-family: Cambria Math;\"> is:</span></p>",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">11. </span><span style=\"font-family: Cambria Math;\">यदि</span><span style=\"font-family: Cambria Math;\">&nbsp; &nbsp; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">x</mi><mo>+</mo><mfrac><mn>1</mn><mi mathvariant=\"normal\">x</mi></mfrac></math> &nbsp;</span><span style=\"font-family: Cambria Math;\">= 1, </span><span style=\"font-family: Cambria Math;\">है</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">तो&nbsp;</span><span style=\"font-family: Cambria Math;\"> &nbsp;<span style=\"font-weight: 400;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>x</mi><mrow><mo>&#160;</mo><mn>12</mn></mrow></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><msup><mi>x</mi><mn>9</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><msup><mi>x</mi><mrow><mn>6</mn><mo>&#160;</mo></mrow></msup><mo>+</mo><mo>&#160;</mo><msup><mi>x</mi><mn>3</mn></msup><mo>&#160;</mo><mo>+</mo><mn>1</mn></math>&nbsp; </span>&nbsp;</span><span style=\"font-family: Cambria Math;\">का</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">मान</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">क्या</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">है</span><span style=\"font-family: Cambria Math;\">?</span></p>",
                    options_en: ["<p>1</p>", "<p>-1</p>", 
                                "<p>0</p>", "<p>-2</p>"],
                    options_hi: ["<p>1</p>", "<p>-1</p>",
                                "<p>0</p>", "<p>-2</p>"],
                    solution_en: "<p>11.(a) <span style=\"font-weight: 400;\">When ,&nbsp; <span style=\"font-family: Cambria Math;\">x&nbsp; +&nbsp; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi mathvariant=\"normal\">x</mi></mfrac></math>&nbsp; &nbsp;</span><span style=\"font-family: Cambria Math;\">=&nbsp; </span><span style=\"font-family: Cambria Math;\">1</span></span><span style=\"font-weight: 400;\">&nbsp; , then </span><span style=\"font-weight: 400;\">x</span><span style=\"font-weight: 400;\">&sup3;&nbsp; </span><span style=\"font-weight: 400;\">= -1</span><br><span style=\"font-weight: 400;\">x&sup3;&nbsp; </span><span style=\"font-weight: 400;\">+ </span><span style=\"font-weight: 400;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>x</mi><mn>0</mn></msup></math> &nbsp;</span><span style=\"font-weight: 400;\">= 0&nbsp; &nbsp; &hellip;&hellip;(Eq.1)</span><br><span style=\"font-weight: 400;\">Now, </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#160;</mo><msup><mi>x</mi><mn>12</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><msup><mi>x</mi><mn>9</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><msup><mi>x</mi><mrow><mn>6</mn><mo>&#160;</mo></mrow></msup><mo>+</mo><mo>&#160;</mo><msup><mi>x</mi><mn>3</mn></msup><mo>+</mo><mn>1</mn></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>x</mi><mn>9</mn></msup><mo>(</mo><msup><mi>x</mi><mn>3</mn></msup><mo>+</mo><mn>1</mn><mo>)</mo><mo>+</mo><msup><mi>x</mi><mn>3</mn></msup><mo>(</mo><msup><mi>x</mi><mn>3</mn></msup><mo>+</mo><mn>1</mn><mo>)</mo><mo>+</mo><mn>1</mn></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>x</mi><mn>9</mn></msup><mo>&#160;</mo><mo>(</mo><msup><mi>x</mi><mn>3</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><msup><mi>x</mi><mn>0</mn></msup><mo>)</mo><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><msup><mi>x</mi><mrow><mo>&#160;</mo><mn>3</mn></mrow></msup><mo>&#160;</mo><mo>(</mo><msup><mi>x</mi><mn>3</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><msup><mi>x</mi><mn>0</mn></msup><mo>)</mo><mo>&#160;</mo><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mo>&#160;</mo><mn>1</mn><mo>&#160;</mo><mo>&#160;</mo><mo>&#8658;</mo><mo>&#160;</mo><mo>&#160;</mo><mn>1</mn></math></p>",
                    solution_hi: "<p>11.(a) <span style=\"font-family: Cambria Math;\">जब</span><span style=\"font-family: Cambria Math;\"> ,&nbsp; &nbsp;x&nbsp; + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><mi mathvariant=\"normal\">x</mi><mo>&#160;</mo></mrow></mfrac></math>&nbsp; = 1 </span><span style=\"font-family: Cambria Math;\">,&nbsp; &nbsp;x&sup3; =&nbsp; -1&nbsp; </span><span style=\"font-family: Cambria Math;\">फिर</span><br><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">x&sup3;&nbsp; </span><span style=\"font-weight: 400;\">+ </span><span style=\"font-weight: 400;\">&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>x</mi><mn>0</mn></msup></math></span><span style=\"font-weight: 400;\"> =&nbsp; 0&nbsp; ............(Eq.1)</span></span><br><span style=\"font-family: Cambria Math;\">अब</span><span style=\"font-family: Cambria Math;\">,</span><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>x</mi><mn>12</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><msup><mi>x</mi><mn>9</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><msup><mi>x</mi><mn>6</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><msup><mi>x</mi><mrow><mn>3</mn><mo>&#160;</mo></mrow></msup><mo>+</mo><mo>&#160;</mo><mn>1</mn></math><br><span style=\"font-weight: 400;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>x</mi><mrow><mn>9</mn><mo>&#160;</mo></mrow></msup><mo>(</mo><msup><mi>x</mi><mrow><mn>3</mn><mo>&#160;</mo></mrow></msup><mo>+</mo><mo>&#160;</mo><msup><mi>x</mi><mrow><mn>0</mn><mo>&#160;</mo></mrow></msup><mo>)</mo><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><msup><mi>x</mi><mn>3</mn></msup><mo>&#160;</mo><mo>(</mo><msup><mi>x</mi><mn>3</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><msup><mi>x</mi><mn>0</mn></msup><mo>)</mo><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>1</mn><mo>&#160;</mo><mo>&#160;</mo><mo>&#8658;</mo><mo>&#160;</mo><mo>&#160;</mo><mn>1</mn></math></span></p>\n<p>&nbsp;</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">12.&nbsp; </span><span style=\"font-family: Cambria Math;\">If&nbsp; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"normal\">a</mi><mi mathvariant=\"normal\">b</mi></mfrac><mo>+</mo><mfrac><mi mathvariant=\"normal\">b</mi><mi mathvariant=\"normal\">a</mi></mfrac></math>&nbsp;</span><span style=\"font-family: Cambria Math;\">= 1 and a + b = 2, then the value of a</span><span style=\"font-family: Cambria Math;\">&sup3;</span><span style=\"font-family: Cambria Math;\"> + b&sup3; is:</span></p>",
                    question_hi: "<p>12.<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">यदि&nbsp;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"normal\">a</mi><mi mathvariant=\"normal\">b</mi></mfrac><mo>+</mo><mfrac><mi mathvariant=\"normal\">b</mi><mi mathvariant=\"normal\">a</mi></mfrac></math> </span><span style=\"font-family: Cambria Math;\">=&nbsp; 1 </span><span style=\"font-family: Cambria Math;\">और&nbsp;</span><span style=\"font-family: Cambria Math;\"> a + b&nbsp; =&nbsp; 2 </span><span style=\"font-family: Cambria Math;\">है</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">तो</span><span style=\"font-family: Cambria Math;\"> &nbsp;</span><span style=\"font-family: Cambria Math;\">a&sup3;&nbsp;</span><span style=\"font-family: Cambria Math;\">+ b&sup3;&nbsp;</span><span style=\"font-family: Cambria Math;\"> &nbsp;</span><span style=\"font-family: Cambria Math;\">का</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">मा</span><span style=\"font-family: Cambria Math;\">न</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">ज्ञात</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">कीजिए।</span></p>",
                    options_en: ["<p>0</p>", "<p>3</p>", 
                                "<p>1</p>", "<p>2</p>"],
                    options_hi: ["<p>0</p>", "<p>3</p>",
                                "<p>1</p>", "<p>2</p>"],
                    solution_en: "<p>12.(a) <span style=\"font-family: Cambria Math;\">a+b = 2 &hellip;&hellip;(Eq. 1)</span><br><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"normal\">a</mi><mi mathvariant=\"normal\">b</mi></mfrac><mo>+</mo><mfrac><mi mathvariant=\"normal\">b</mi><mi mathvariant=\"normal\">a</mi></mfrac><mo>&#160;</mo><mo>=</mo><mn>1</mn><mo>&#160;</mo></math></span><br><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>a</mi><mo>&#178;</mo><mo>+</mo><mi>b</mi><mo>&#178;</mo></mrow><mrow><mi>a</mi><mi>b</mi></mrow></mfrac></math> = 1 &nbsp;&rArr; &nbsp;</span><span style=\"font-weight: 400;\">a&sup2; </span><span style=\"font-weight: 400;\">+ </span><span style=\"font-weight: 400;\">b&sup2; </span><span style=\"font-weight: 400;\">&nbsp; =&nbsp; ab ........<span style=\"font-family: Cambria Math;\">(Eq. 2)</span></span><br><span style=\"font-family: Cambria Math;\">a</span><span style=\"font-family: Cambria Math;\">&sup3; </span><span style=\"font-family: Cambria Math;\">+&nbsp; b&sup3; =&nbsp; (a + b) ( a&sup2;</span><span style=\"font-family: Cambria Math;\">+ b&sup2; </span><span style=\"font-family: Cambria Math;\">- ab )</span><br><span style=\"font-family: Cambria Math;\">From <span style=\"font-weight: 400;\">(Eq. 1)</span> and<span style=\"font-weight: 400;\">(Eq. 2)</span></span><br><span style=\"font-family: Cambria Math;\">a&sup3; </span><span style=\"font-family: Cambria Math;\">+ b&sup3; = 0</span></p>",
                    solution_hi: "<p>12.(a) <span style=\"font-family: Cambria Math;\">a+b&nbsp; =&nbsp; 2 &hellip;&hellip;</span><span style=\"font-family: Cambria Math;\">समीकरण</span><span style=\"font-family: Cambria Math;\"> (1)</span><br><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"normal\">a</mi><mi mathvariant=\"normal\">b</mi></mfrac><mo>+</mo><mfrac><mi mathvariant=\"normal\">b</mi><mi mathvariant=\"normal\">a</mi></mfrac></math> &nbsp; </span><span style=\"font-family: Cambria Math;\">= 1</span><br><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi mathvariant=\"normal\">a</mi><mn>2</mn></msup><mo>+</mo><msup><mi mathvariant=\"normal\">b</mi><mrow><mn>2</mn><mo>&#160;</mo></mrow></msup></mrow><mrow><mi>ab</mi><mo>&#160;</mo></mrow></mfrac></math> = 1 &nbsp; &nbsp;&rArr;&nbsp; a&sup2;&nbsp; +&nbsp; b&sup2;&nbsp; =&nbsp; ab&nbsp;</span><br><span style=\"font-family: Cambria Math;\">&nbsp;<span style=\"font-weight: 400;\">a&sup2;&nbsp; </span><span style=\"font-weight: 400;\">+&nbsp; </span><span style=\"font-weight: 400;\">b&sup2;&nbsp; </span><span style=\"font-weight: 400;\">- ab&nbsp; </span>&nbsp;=&nbsp; 0 &hellip;&hellip;</span><span style=\"font-family: Cambria Math;\">समीकरण</span><span style=\"font-family: Cambria Math;\"> (2)</span><br><span style=\"font-family: Cambria Math;\">a</span><span style=\"font-family: Cambria Math;\">&sup3;</span><span style=\"font-family: Cambria Math;\"> + b&sup3; = (a + b) ( a<span style=\"font-weight: 400;\">&sup2;</span></span><span style=\"font-family: Cambria Math;\"> + b<span style=\"font-weight: 400;\">&sup2;</span></span><span style=\"font-family: Cambria Math;\"> - ab )</span><br><span style=\"font-family: Cambria Math;\">समीकरण</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">(1) </span><span style=\"font-family: Cambria Math;\">और</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">समीकरण</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">(2) </span><span style=\"font-family: Cambria Math;\">से</span><span style=\"font-family: Cambria Math;\">,</span><br><span style=\"font-family: Cambria Math;\">a&sup3; </span><span style=\"font-family: Cambria Math;\"> + b&sup3; = 0</span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">13.</span><span style=\"font-family: Cambria Math;\"> If&nbsp; &nbsp;4x</span><span style=\"font-family: Cambria Math;\">&sup2; </span><span style=\"font-family: Cambria Math;\">+ y&sup2; </span><span style=\"font-family: Cambria Math;\">=&nbsp; 40 and xy = 6, find the positive value of&nbsp; 2x + y. </span></p>",
                    question_hi: "<p>13.<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">यदि&nbsp; &nbsp;4x&sup2; + y&sup2; =&nbsp; 40</span><span style=\"font-family: Cambria Math;\">&nbsp;</span><span style=\"font-family: Cambria Math;\">और</span><span style=\"font-family: Cambria Math;\"> xy = 6 </span><span style=\"font-family: Cambria Math;\">है</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">तो</span><span style=\"font-family: Cambria Math;\"> 2x + y </span><span style=\"font-family: Cambria Math;\">का</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">धनात्मक</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">मान</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">ज्ञात</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">कीजिए।</span></p>",
                    options_en: ["<p>8</p>", "<p>6</p>", 
                                "<p>5</p>", "<p>4</p>"],
                    options_hi: ["<p>8</p>", "<p>6</p>",
                                "<p>5</p>", "<p>4</p>"],
                    solution_en: "<p>13.(a) <span style=\"font-weight: 400;\">4</span><span style=\"font-weight: 400;\">x&sup2;</span><span style=\"font-weight: 400;\">&nbsp;+ </span><span style=\"font-weight: 400;\">y&sup2;&nbsp;</span><span style=\"font-weight: 400;\"> = 40&nbsp;</span><br><span style=\"font-weight: 400;\">Adding (4xy) both sides , we get&nbsp;&nbsp;</span><br><span style=\"font-weight: 400;\"> (2x)&sup2;</span><span style=\"font-weight: 400;\">&nbsp;+ </span><span style=\"font-weight: 400;\">y&sup2; + 4xy </span><span style=\"font-weight: 400;\">&nbsp; = 40&nbsp; +&nbsp; 4xy&nbsp; &nbsp; ...xy = 6&nbsp; (given)</span><br><span style=\"font-weight: 400;\">(2x + y)&sup2;&nbsp;</span><span style=\"font-weight: 400;\"> =&nbsp; 64</span><br><span style=\"font-family: Cambria Math;\">2x+y = 8</span><br><span style=\"font-family: Cambria Math;\">Short tricks :-</span><br><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">4</span><span style=\"font-weight: 400;\">x&sup2;</span><span style=\"font-weight: 400;\"> + </span><span style=\"font-weight: 400;\">y&sup2; </span>=&nbsp; 40&nbsp; and&nbsp; &nbsp;xy&nbsp; =&nbsp; 6 &hellip;.(given)</span><br><span style=\"font-family: Cambria Math;\">Put&nbsp; x =&nbsp; 3 and y = 2 which satisfy the both given equations .</span><br><span style=\"font-family: Cambria Math;\">Now , the value of&nbsp; 2x+y&nbsp; =&nbsp; 2&times;3+2&nbsp; =&nbsp; 8</span></p>",
                    solution_hi: "<p>13.(a) <span style=\"font-weight: 400;\">4</span><span style=\"font-weight: 400;\">x&sup2;</span><span style=\"font-weight: 400;\">&nbsp;+ </span><span style=\"font-weight: 400;\">y&sup2;&nbsp;</span><span style=\"font-weight: 400;\"> = 40&nbsp;</span><br><span style=\"font-weight: 400;\">दोनों पक्षों में (4xy) जोड़ने पर, हम प्राप्त करते हैं&nbsp;</span><br><span style=\"font-weight: 400;\"> (2x)&sup2;</span><span style=\"font-weight: 400;\">&nbsp;+ </span><span style=\"font-weight: 400;\">y&sup2; + 4xy </span><span style=\"font-weight: 400;\">&nbsp; = 40&nbsp; +&nbsp; 4xy&nbsp; &nbsp; ...xy = 6 </span><span style=\"font-weight: 400;\">( दिया गया है )</span><br><span style=\"font-weight: 400;\">(2x + y)&sup2;&nbsp;</span><span style=\"font-weight: 400;\"> =&nbsp; 64</span><br><span style=\"font-family: Cambria Math;\">2x+y = 8</span><br><strong>शॉर्ट ट्रिक्स :-</strong><br><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">4</span><span style=\"font-weight: 400;\">x&sup2;</span><span style=\"font-weight: 400;\"> + </span><span style=\"font-weight: 400;\">y&sup2; </span>=&nbsp; 40&nbsp; &nbsp;</span><span style=\"font-weight: 400;\">और&nbsp; xy = 6 &hellip;.(दिया गया है)</span><br><span style=\"font-weight: 400;\">&nbsp;x= 3 और y = 2 रखने पर,&nbsp; जो दिए गए दोनों समीकरणों को संतुष्ट करते हैं।</span><br><span style=\"font-weight: 400;\">अब , 2x+y का मान&nbsp; = 2&times;3+2 = 8&nbsp;</span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">14.</span><span style=\"font-family: Cambria Math;\"> If&nbsp; &nbsp;p = 7 + </span><span style=\"font-family: Cambria Math;\"> 4 <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math></span><span style=\"font-family: Cambria Math;\">,&nbsp; &nbsp;then what is the value of&nbsp; &nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi mathvariant=\"normal\">p</mi><mn>6</mn></msup><mo>+</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">p</mi><mn>4</mn></msup><mo>+</mo><mo>&#160;</mo><mi mathvariant=\"normal\">p</mi><mo>&#178;</mo><mo>+</mo><mn>1</mn></mrow><mrow><mo>&#160;</mo><msup><mi mathvariant=\"normal\">p</mi><mn>3</mn></msup></mrow></mfrac></math> </span><span style=\"font-family: Cambria Math;\">?</span></p>",
                    question_hi: "<p>14.<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">यदि</span><span style=\"font-family: Cambria Math;\">&nbsp; p = 7 + 4<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math></span><span style=\"font-family: Cambria Math;\">,&nbsp; </span><span style=\"font-family: Cambria Math;\">तो</span><span style=\"font-family: Cambria Math;\">&nbsp; &nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi mathvariant=\"normal\">p</mi><mn>6</mn></msup><mo>+</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">p</mi><mn>4</mn></msup><mo>+</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">p</mi><mn>2</mn></msup><mo>+</mo><mo>&#160;</mo><mn>1</mn><mo>&#160;</mo></mrow><mrow><mo>&#160;</mo><msup><mi mathvariant=\"normal\">p</mi><mn>3</mn></msup></mrow></mfrac></math>&nbsp;</span><span style=\"font-family: Cambria Math;\">का</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">मान</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">कितना</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">है</span><span style=\"font-family: Cambria Math;\"> ?</span></p>",
                    options_en: ["<p>2617</p>", "<p>2167</p>", 
                                "<p>2716</p>", "<p>2176</p>"],
                    options_hi: ["<p>2617</p>", "<p>2167</p>",
                                "<p>2716</p>", "<p>2176</p>"],
                    solution_en: "<p>14.(c) <span style=\"font-weight: 400;\">p = </span><span style=\"font-weight: 400;\">7 + 4</span><span style=\"font-weight: 400;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math></span><span style=\"font-weight: 400;\">&nbsp; &nbsp; &hellip;(given)</span><br><span style=\"font-weight: 400;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi>p</mi></mfrac></math></span><span style=\"font-weight: 400;\"> = </span><span style=\"font-weight: 400;\">7 - 4<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math></span><span style=\"font-weight: 400;\">&nbsp; &nbsp;&hellip;(conjugate pairs)&nbsp;</span><br><span style=\"font-weight: 400;\"> </span><span style=\"font-weight: 400;\">p + </span><span style=\"font-weight: 400;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi>p</mi></mfrac></math></span><span style=\"font-weight: 400;\"> = 14</span><br><span style=\"font-weight: 400;\"> </span><span style=\"font-weight: 400;\">p&sup3;</span><span style=\"font-weight: 400;\">+&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msup><mi>p</mi><mn>3</mn></msup></mfrac></math></span><span style=\"font-weight: 400;\"> = </span><span style=\"font-weight: 400;\">14&sup3; </span><span style=\"font-weight: 400;\">- 3&times;14</span><span style=\"font-weight: 400;\"> = 2702</span><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi>p</mi><mn>6</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><msup><mi>p</mi><mrow><mn>4</mn><mo>&#160;</mo></mrow></msup><mo>+</mo><mo>&#160;</mo><msup><mi>p</mi><mrow><mn>2</mn><mo>&#160;</mo></mrow></msup><mo>+</mo><mo>&#160;</mo><mn>1</mn></mrow><msup><mi>p</mi><mn>3</mn></msup></mfrac></math> = &nbsp;<span style=\"font-weight: 400;\">p&sup3; + p +&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi>p</mi></mfrac></math> +&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msup><mi>p</mi><mn>3</mn></msup></mfrac></math></span><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>p</mi><mrow><mn>3</mn><mo>&#160;</mo></mrow></msup><mo>+</mo><mo>&#160;</mo><mfrac><mn>1</mn><msup><mi>p</mi><mn>3</mn></msup></mfrac><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mi>p</mi><mo>&#160;</mo><mo>+</mo><mfrac><mn>1</mn><mrow><mi>p</mi><mo>&#160;</mo></mrow></mfrac></math> = 2702 + 14&nbsp; = 2716</p>",
                    solution_hi: "<p>14.(c) <span style=\"font-weight: 400;\">p = </span><span style=\"font-weight: 400;\">7 + 4</span><span style=\"font-weight: 400;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math></span><span style=\"font-weight: 400;\">&nbsp; &nbsp; &hellip;(दिया गया है)</span><br><span style=\"font-weight: 400;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi>p</mi></mfrac></math></span><span style=\"font-weight: 400;\"> = </span><span style=\"font-weight: 400;\">7 - 4</span><span style=\"font-weight: 400;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math></span><span style=\"font-weight: 400;\"> &nbsp; &hellip;(संयुग्मित जोड़ा)&nbsp;</span><br><span style=\"font-weight: 400;\"> </span><span style=\"font-weight: 400;\">p +&nbsp;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi>p</mi></mfrac></math></span><span style=\"font-weight: 400;\"> = 14</span><br><span style=\"font-weight: 400;\"> </span><span style=\"font-weight: 400;\">p&sup3; </span><span style=\"font-weight: 400;\">+</span><span style=\"font-weight: 400;\">&nbsp; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msup><mi>p</mi><mn>3</mn></msup></mfrac></math> = </span><span style=\"font-weight: 400;\">14</span><span style=\"font-weight: 400;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math> </span><span style=\"font-weight: 400;\">-3 &times; 14</span><span style=\"font-weight: 400;\"> = 2702</span><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi mathvariant=\"normal\">p</mi><mn>6</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">p</mi><mn>4</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">p</mi><mn>2</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>1</mn></mrow><msup><mi mathvariant=\"normal\">p</mi><mn>3</mn></msup></mfrac><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">p</mi><mn>3</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mi mathvariant=\"normal\">p</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mfrac><mn>1</mn><mi mathvariant=\"normal\">p</mi></mfrac><mo>+</mo><mfrac><mn>1</mn><msup><mi mathvariant=\"normal\">p</mi><mn>3</mn></msup></mfrac><mo>&#160;</mo></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">p</mi><mrow><mn>3</mn><mo>&#160;</mo></mrow></msup><mo>+</mo><mo>&#160;</mo><mfrac><mn>1</mn><msup><mi mathvariant=\"normal\">p</mi><mn>3</mn></msup></mfrac><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mi mathvariant=\"normal\">p</mi><mo>&#160;</mo><mo>+</mo><mfrac><mn>1</mn><mrow><mi mathvariant=\"normal\">p</mi><mo>&#160;</mo></mrow></mfrac></math><br><span style=\"font-weight: 400;\">= 2702 + 14&nbsp; = 2716</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "misc",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">15. </span><span style=\"font-family: Cambria Math;\">If&nbsp; 2a + 3b = 10 and ab = 3, then find the value of 4a</span><span style=\"font-family: Cambria Math;\">&sup2;</span><span style=\"font-family: Cambria Math;\"> + 9b&sup2;</span><span style=\"font-family: Cambria Math;\">.</span></p>\n<p><span style=\"font-family: Cambria Math;\"> </span></p>",
                    question_hi: "<p>15.<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">यदि</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">2a + 3b = 10 </span><span style=\"font-family: Cambria Math;\">और</span><span style=\"font-family: Cambria Math;\"> ab = 3 </span><span style=\"font-family: Cambria Math;\">है</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">तो&nbsp; 4a&sup2; + 9b&sup2;.</span><span style=\"font-family: Cambria Math;\">&nbsp;</span><span style=\"font-family: Cambria Math;\">का</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">मान</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">ज्ञात</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">कीजिए</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">।</span></p>",
                    options_en: ["<p>60</p>", "<p>62</p>", 
                                "<p>64</p>", "<p>66</p>"],
                    options_hi: ["<p>60</p>", "<p>62</p>",
                                "<p>64</p>", "<p>66</p>"],
                    solution_en: "<p>15.(c) <span style=\"font-family: Cambria Math;\">ab = 3 &hellip;(given)</span><br><span style=\"font-family: Cambria Math;\">2a+3b = 10</span><br><span style=\"font-family: Cambria Math;\">On squaring ,</span><br><span style=\"font-family: Cambria Math;\">&nbsp;<span style=\"font-weight: 400;\">4a</span><span style=\"font-weight: 400;\">&sup2;</span><span style=\"font-weight: 400;\">+</span><span style=\"font-weight: 400;\">9b&sup2;</span><span style=\"font-weight: 400;\">+12ab </span>= 100</span><br><span style=\"font-family: Cambria Math;\">&nbsp;<span style=\"font-weight: 400;\">4a</span><span style=\"font-weight: 400;\">&sup2;</span><span style=\"font-weight: 400;\">+</span><span style=\"font-weight: 400;\">9b&sup2;&nbsp; </span>= 100 - 12&times;3 = 64</span></p>",
                    solution_hi: "<p>15.(c) <span style=\"font-family: Cambria Math;\">ab = 3 &hellip; </span><span style=\"font-weight: 400;\">(दिया गया है)</span><br><span style=\"font-family: Cambria Math;\">2a+3b = 10</span><br><span style=\"font-weight: 400;\">वर्ग करने पर,</span><br><span style=\"font-family: Cambria Math;\">&nbsp;<span style=\"font-weight: 400;\">4a</span><span style=\"font-weight: 400;\">&sup2;</span><span style=\"font-weight: 400;\">+</span><span style=\"font-weight: 400;\">9b&sup2;</span><span style=\"font-weight: 400;\">+12ab </span>= 100</span><br><span style=\"font-family: Cambria Math;\">&nbsp;<span style=\"font-weight: 400;\">4a</span><span style=\"font-weight: 400;\">&sup2;</span><span style=\"font-weight: 400;\">+</span><span style=\"font-weight: 400;\">9b&sup2;&nbsp; </span>= 100 - 12&times;3 = 64</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>