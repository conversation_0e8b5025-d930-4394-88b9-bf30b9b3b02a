<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">12:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 25</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">25</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 12 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["7"] = {
                name: "English",
                start: 0,
                end: 23
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="7">English</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 24,
                end: 24
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "7",
                    question_en: "<p>1. <span style=\"font-family: Cambria Math;\">Select the most appropriate idiom for the underlined segment in the given sentence.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">When he made fun of the sage, who was already infuriated, it </span><span style=\"text-decoration: underline;\"><span style=\"font-family: Cambria Math;\">made a bad situation </span><span style=\"font-family: Cambria Math;\">worse</span></span><span style=\"font-family: Cambria Math;\">.</span></p>\\n",
                    question_hi: "<p>1. <span style=\"font-family: Cambria Math;\">Select the most appropriate idiom for the underlined segment in the given sentence.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">When he made fun of the sage, who was already infuriated, it </span><span style=\"text-decoration: underline;\"><span style=\"font-family: Cambria Math;\">made a bad situation </span><span style=\"font-family: Cambria Math;\">worse</span></span><span style=\"font-family: Cambria Math;\">.</span></p>\\n",
                    options_en: ["<p>Made them at the loggerheads</p>\\n", "<p>Pulled someone&rsquo;s legs</p>\\n", 
                                "<p>Added fuel to the fire</p>\\n", "<p>Budged up</p>\\r\\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\\n"],
                    options_hi: ["<p>Made them at the loggerheads</p>\\n", "<p>Pulled someone&rsquo;s legs</p>\\n",
                                "<p>Added fuel to the fire</p>\\n", "<p>Budged up</p>\\n"],
                    solution_en: "<p>1.(c)<span style=\"font-family: Cambria Math;\"> Added fuel to the fire- made a bad situation worse.</span></p>\\n",
                    solution_hi: "<p>1.(c)<span style=\"font-family: Cambria Math;\"> Added fuel to the fire- made a bad situation worse./</span><span style=\"font-family: Kokila;\">&#2348;&#2369;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2381;&#2341;&#2367;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2349;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2348;&#2342;&#2340;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2348;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2342;&#2375;&#2344;&#2366;&#2404;</span></p>\\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "7",
                    question_en: "<p>2. <span style=\"font-family: Cambria Math;\">Select the most appropriate synonym of the given word.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">Quell</span></p>\\n",
                    question_hi: "<p>2. <span style=\"font-family: Cambria Math;\">Select the most appropriate synonym of the given word.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">Quell</span></p>\\n",
                    options_en: ["<p>Invite</p>\\n", "<p>Beseech</p>\\n", 
                                "<p>Encourage</p>\\n", "<p>Suppress</p>\\r\\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\\n"],
                    options_hi: ["<p>Invite</p>\\n", "<p>Beseech</p>\\n",
                                "<p>Encourage</p>\\n", "<p>Suppress</p>\\n"],
                    solution_en: "<p>2.(d) <strong>Suppress</strong><span style=\"font-family: Cambria Math;\">- to restrain or prevent something from happening by using force.</span></p>\\r\\n<p><strong><span style=\"font-family: Cambria Math;\">Quell</span></strong><span style=\"font-family: Cambria Math;\">- to calm or suppress something.</span></p>\\r\\n<p><strong><span style=\"font-family: Cambria Math;\">Invite</span></strong><span style=\"font-family: Cambria Math;\">- to request or ask someone to participate in an event.</span></p>\\r\\n<p><strong><span style=\"font-family: Cambria Math;\">Beseech</span></strong><span style=\"font-family: Cambria Math;\">- to urgently and sincerely request for something.</span></p>\\r\\n<p><strong><span style=\"font-family: Cambria Math;\">Encourage</span></strong><span style=\"font-family: Cambria Math;\">- to give support, confidence, or motivation to someone.</span></p>\\n",
                    solution_hi: "<p>2.(d) <strong>Suppress </strong><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Kokila;\">&#2352;&#2379;&#2325;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\">) - to restrain or prevent something from happening by using force.</span></p>\\r\\n<p><strong><span style=\"font-family: Cambria Math;\">Quell </span></strong><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Kokila;\">&#2358;&#2366;&#2306;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2352;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\">) - to calm or suppress something.</span></p>\\r\\n<p><strong><span style=\"font-family: Cambria Math;\">Invite </span></strong><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Kokila;\">&#2310;&#2350;&#2306;&#2340;&#2381;&#2352;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2352;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\">) - to request or ask someone to participate in an event.</span></p>\\r\\n<p><strong><span style=\"font-family: Cambria Math;\">Beseech</span></strong><span style=\"font-family: Cambria Math;\"><strong> </strong>(</span><span style=\"font-family: Kokila;\">&#2357;&#2367;&#2344;&#2340;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2352;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\">) - to urgently and sincerely request for something.</span></p>\\r\\n<p><strong><span style=\"font-family: Cambria Math;\">Encourage </span></strong><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Kokila;\">&#2346;&#2381;&#2352;&#2379;&#2340;&#2381;&#2360;&#2366;&#2361;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2352;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\">) - to give support, confidence, or motivation to someone.</span></p>\\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "7",
                    question_en: " <p>3.</span><span style=\"font-family:Cambria Math\"> Select the most appropriate idiom for the following sentence.</span></p> <p><span style=\"font-family:Cambria Math\">The recent strike of the employees left the factory and its machinery in disorder.</span></p>",
                    question_hi: " <p>3.</span><span style=\"font-family:Cambria Math\"> Select the most appropriate idiom for the following sentence.</span></p> <p><span style=\"font-family:Cambria Math\">The recent strike of the employees left the factory and its machinery in disorder.</span></p>",
                    options_en: [" <p> Need of the hour</span></p>", " <p> Out of gear</span></p>", 
                                " <p> Pin-money</span></p>", " <p> Lock and key</span></p> <p><span style=\"font-family:Cambria Math\"> </span></p>"],
                    options_hi: [" <p> Need of the hour</span></p>", " <p> Out of gear</span></p>",
                                " <p> Pin-money</span></p>", " <p> Lock and key</span></p>"],
                    solution_en: " <p>3.(b)</span><span style=\"font-family:Cambria Math\"> Out of gear- in a state of disorder; not functioning properly.</span></p>",
                    solution_hi: " <p>3.(b)</span><span style=\"font-family:Cambria Math\"> Out of gear- in a state of disorder; not functioning properly./</span><span style=\"font-family:Kokila\">अव्यवस्था</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">की</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">स्थिति</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">में</span><span style=\"font-family:Cambria Math\">; </span><span style=\"font-family:Kokila\">ठीक</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">से</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">काम</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">नहीं</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">कर</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">पाना।</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "7",
                    question_en: "<p>4.<span style=\"font-family: Cambria Math;\"> Select the option that can be used as a one-word substitute for the underlined group </span><span style=\"font-family: Cambria Math;\">of words.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">&lsquo;A Red, Red Rose&rsquo; by Robert Burns is </span><span style=\"text-decoration: underline;\"><span style=\"font-family: Cambria Math;\">a poem that tells a story and has a regular </span><span style=\"font-family: Cambria Math;\">rhythm and rhyme scheme</span></span><span style=\"font-family: Cambria Math;\">.</span></p>\\n",
                    question_hi: "<p>4.<span style=\"font-family: Cambria Math;\"> Select the option that can be used as a one-word substitute for the underlined group </span><span style=\"font-family: Cambria Math;\">of words.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">&lsquo;A Red, Red Rose&rsquo; by Robert Burns is </span><span style=\"text-decoration: underline;\"><span style=\"font-family: Cambria Math;\">a poem that tells a story and has a regular </span><span style=\"font-family: Cambria Math;\">rhythm and rhyme scheme</span></span><span style=\"font-family: Cambria Math;\">.</span></p>\\n",
                    options_en: ["<p>epic</p>\\n", "<p>haiku</p>\\n", 
                                "<p>ballad</p>\\n", "<p>sonnet</p>\\r\\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\\n"],
                    options_hi: ["<p>epic</p>\\n", "<p>haiku</p>\\n",
                                "<p>ballad</p>\\n", "<p>sonnet</p>\\n"],
                    solution_en: "<p>4.(c)<span style=\"font-family: Cambria Math;\"> </span><strong><span style=\"font-family: Cambria Math;\">Ballad</span></strong><span style=\"font-family: Cambria Math;\">- a poem that tells a story and has a regular rhythm and rhyme scheme.</span></p>\\r\\n<p><strong><span style=\"font-family: Cambria Math;\">Epic</span></strong><span style=\"font-family: Cambria Math;\">- a long poem narrating the deeds and adventures of heroic or legendary figures.</span></p>\\r\\n<p><strong><span style=\"font-family: Cambria Math;\">Haiku</span></strong><span style=\"font-family: Cambria Math;\">- an unrhymed Japanese poem that consists of 17 syllables arranged in three lines containing five, seven, and five syllables.</span></p>\\r\\n<p><strong><span style=\"font-family: Cambria Math;\">Sonnet</span></strong><span style=\"font-family: Cambria Math;\">- a poem that has 14 lines and a particular pattern of rhyme.</span></p>\\n",
                    solution_hi: "<p>4.(c)<span style=\"font-family: Cambria Math;\"> </span><strong><span style=\"font-family: Cambria Math;\">Ballad </span></strong><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Kokila;\">&#2327;&#2366;&#2341;&#2366;&#2327;&#2368;&#2340;</span><span style=\"font-family: Cambria Math;\">) - a poem that tells a story and has a regular rhythm and rhyme scheme.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\"><strong>Epic</strong> </span><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Kokila;\">&#2350;&#2361;&#2366;&#2325;&#2366;&#2357;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\">) - a long poem narrating the deeds and adventures of heroic or legendary figures.</span></p>\\r\\n<p><strong><span style=\"font-family: Cambria Math;\">Haiku </span></strong><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Kokila;\">&#2361;&#2366;&#2311;&#2325;&#2369;</span><span style=\"font-family: Cambria Math;\">/</span><span style=\"font-family: Kokila;\">&#2332;&#2366;&#2346;&#2366;&#2344;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2357;&#2367;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\">) - an unrhymed Japanese poem that consists of 17 syllables arranged in three lines containing five, seven, and five syllables.</span></p>\\r\\n<p><strong><span style=\"font-family: Cambria Math;\">Sonnet </span></strong><span style=\"font-family: Cambria Math;\">(14 </span><span style=\"font-family: Kokila;\">&#2346;&#2306;&#2325;&#2381;&#2340;&#2367;&#2351;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2357;&#2367;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\">)</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">- a poem that has 14 lines and a particular pattern of rhyme.</span></p>\\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "7",
                    question_en: "<p>5. <span style=\"font-family: Cambria Math;\">Select the most appropriate homonym to fill in the blank.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">Should we go shopping for a ____ fan for our new house?</span></p>\\n",
                    question_hi: "<p>5. <span style=\"font-family: Cambria Math;\">Select the most appropriate homonym to fill in the blank.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">Should we go shopping for a ____ fan for our new house?</span></p>\\n",
                    options_en: ["<p>ceiling</p>\\n", "<p>sealink</p>\\n", 
                                "<p>sealing</p>\\n", "<p>selling</p>\\r\\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\\n"],
                    options_hi: ["<p>ceiling</p>\\n", "<p>sealink</p>\\n",
                                "<p>sealing</p>\\n", "<p>selling</p>\\n"],
                    solution_en: "<p>5.(a)<span style=\"font-family: Cambria Math;\"> Ceiling</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">&lsquo;Ceiling&rsquo; means the upper surface of a room. The given sentence states a question, asking about going shopping for a ceiling fan. Hence, &lsquo;ceiling&rsquo; is the most appropriate answer.</span></p>\\n",
                    solution_hi: "<p>5.(a)<span style=\"font-family: Cambria Math;\"> Ceiling</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">&lsquo;Ceiling&rsquo; </span><span style=\"font-family: Kokila;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2309;&#2352;&#2381;&#2341;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2350;&#2352;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2314;&#2346;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2340;&#2361;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2342;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2327;&#2319;</span><span style=\"font-family: Cambria Math;\"> sentence </span><span style=\"font-family: Kokila;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2381;&#2352;&#2358;&#2381;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2348;&#2340;&#2366;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2327;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Kokila;\">&#2332;&#2367;&#2360;&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2331;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2306;&#2326;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2326;&#2352;&#2368;&#2342;&#2366;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2332;&#2366;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2348;&#2366;&#2352;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2370;&#2331;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2327;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2309;&#2340;&#2307;</span><span style=\"font-family: Cambria Math;\">, &lsquo;ceiling&rsquo; </span><span style=\"font-family: Kokila;\">&#2360;&#2348;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2313;&#2346;&#2351;&#2369;&#2325;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2313;&#2340;&#2381;&#2340;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;&#2404;</span></p>\\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "7",
                    question_en: " <p>6. </span><span style=\"font-family:Cambria Math\">The following sentence has been divided into parts. One of them may contain an error.</span></p> <p><span style=\"font-family:Cambria Math\">Select the part that contains the error from the given options. If you don’t find any error, mark ‘No error’ as your answer.</span></p> <p><span style=\"font-family:Cambria Math\">A crocodile / was swimming / in a river.</span></p>",
                    question_hi: " <p>6. </span><span style=\"font-family:Cambria Math\">The following sentence has been divided into parts. One of them may contain an error.</span></p> <p><span style=\"font-family:Cambria Math\">Select the part that contains the error from the given options. If you don’t find any error, mark ‘No error’ as your answer.</span></p> <p><span style=\"font-family:Cambria Math\">A crocodile / was swimming / in a river.</span></p>",
                    options_en: [" <p> A crocodile</span></p>", " <p> was swimming</span></p>", 
                                " <p> No error</span></p>", " <p> in a river</span></p> <p><span style=\"font-family:Cambria Math\"> </span></p>"],
                    options_hi: [" <p> A crocodile</span></p>", " <p> was swimming</span></p>",
                                " <p> No error</span></p>", " <p> in a river</span></p>"],
                    solution_en: " <p>6.(c)</span><span style=\"font-family:Cambria Math\"> No error</span></p> <p><span style=\"font-family:Cambria Math\">The given sentence is grammatically correct.</span></p>",
                    solution_hi: " <p>6.(c)</span><span style=\"font-family:Cambria Math\"> No error</span></p> <p><span style=\"font-family:Kokila\">दिया</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">गया</span><span style=\"font-family:Cambria Math\"> sentence, grammatically </span><span style=\"font-family:Kokila\">सही</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">है।</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "7",
                    question_en: "<p>7. <span style=\"font-family: Cambria Math;\">Select the most appropriate option that can substitute the underlined segment in the </span><span style=\"font-family: Cambria Math;\">given sentence.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">The performance of the musicians was rather </span><span style=\"text-decoration: underline;\"><span style=\"font-family: Cambria Math;\">worst than ex</span><span style=\"font-family: Cambria Math;\">p</span><span style=\"font-family: Cambria Math;\">ected</span></span><span style=\"font-family: Cambria Math;\"> by the audience.</span></p>\\n",
                    question_hi: "<p>7. <span style=\"font-family: Cambria Math;\">Select the most appropriate option that can substitute the underlined segment in the </span><span style=\"font-family: Cambria Math;\">given sentence.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">The performance of the musicians was rather </span><span style=\"text-decoration: underline;\"><span style=\"font-family: Cambria Math;\">worst than ex</span><span style=\"font-family: Cambria Math;\">p</span><span style=\"font-family: Cambria Math;\">ected</span></span><span style=\"font-family: Cambria Math;\"> by the audience.</span></p>\\n",
                    options_en: ["<p>worst than expectation</p>\\n", "<p>worse was expected</p>\\n", 
                                "<p>worse than expected</p>\\n", "<p>bad as expected</p>\\r\\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\\n"],
                    options_hi: ["<p>worst than expectation</p>\\n", "<p>worse was expected</p>\\n",
                                "<p>worse than expected</p>\\n", "<p>bad as expected</p>\\n"],
                    solution_en: "<p>7.(c)<span style=\"font-family: Cambria Math;\"> worse than expected</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">We always use &lsquo;than&rsquo; after a comparative degree when we do a comparison between two things. Similarly in the given sentence, there is a comparison between the actual performance and the expected performance of the musicians. And, &lsquo;worse&rsquo; is the comparative degree of &lsquo;bad&rsquo;. Hence, &lsquo;worse than expected&rsquo; is the most appropriate structure.</span></p>\\n",
                    solution_hi: "<p>7.(c)<span style=\"font-family: Cambria Math;\"> worse than expected</span></p>\\r\\n<p><span style=\"font-family: Kokila;\">&#2332;&#2348;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2342;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2330;&#2368;&#2332;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2348;&#2368;&#2330;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2340;&#2369;&#2354;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2352;&#2340;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2350;&#2375;&#2358;&#2366;</span><span style=\"font-family: Cambria Math;\"> comparative degree </span><span style=\"font-family: Kokila;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2348;&#2366;&#2342;</span><span style=\"font-family: Cambria Math;\"> &lsquo;than&rsquo; </span><span style=\"font-family: Kokila;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2381;&#2352;&#2351;&#2379;&#2327;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2352;&#2340;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;&#2306;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2311;&#2360;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2381;&#2352;&#2325;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2342;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2327;&#2319;</span><span style=\"font-family: Cambria Math;\"> sentence</span><span style=\"font-family: Kokila;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2306;&#2327;&#2368;&#2340;&#2325;&#2366;&#2352;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2357;&#2366;&#2360;&#2381;&#2340;&#2357;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2381;&#2352;&#2342;&#2352;&#2381;&#2358;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2309;&#2346;&#2375;&#2325;&#2381;&#2359;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2381;&#2352;&#2342;&#2352;&#2381;&#2358;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2348;&#2368;&#2330;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2340;&#2369;&#2354;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\">, &lsquo;worse&rsquo;, &lsquo;bad&rsquo; </span><span style=\"font-family: Kokila;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> comparative degree </span><span style=\"font-family: Kokila;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2309;&#2340;&#2307;</span><span style=\"font-family: Cambria Math;\">, &lsquo;worse than expected&rsquo; </span><span style=\"font-family: Kokila;\">&#2360;&#2348;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2313;&#2346;&#2351;&#2369;&#2325;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\"> structure </span><span style=\"font-family: Kokila;\">&#2361;&#2376;&#2404;</span></p>\\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "7",
                    question_en: "<p>8. <span style=\"font-family: Cambria Math;\">Select the most appropriate synonym of the given word from the following sentence.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">Damage</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">Every war in the history originates because of the greed of the rulers which was </span><span style=\"font-family: Cambria Math;\">satisfied after mass destruction.</span></p>\\n",
                    question_hi: "<p>8. <span style=\"font-family: Cambria Math;\">Select the most appropriate synonym of the given word from the following sentence.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">Damage</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">Every war in the history originates because of the greed of the rulers which was </span><span style=\"font-family: Cambria Math;\">satisfied after mass destruction.</span></p>\\n",
                    options_en: ["<p>Satisfied</p>\\n", "<p>Destruction</p>\\n", 
                                "<p>Greed</p>\\n", "<p>Originates</p>\\r\\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\\n"],
                    options_hi: ["<p>Satisfied</p>\\n", "<p>Destruction</p>\\n",
                                "<p>Greed</p>\\n", "<p>Originates</p>\\n"],
                    solution_en: "<p>8.(b) <strong>Destruction</strong><span style=\"font-family: Cambria Math;\">- the act of causing severe damage or ruining something.</span></p>\\r\\n<p><strong><span style=\"font-family: Cambria Math;\">Damage</span></strong><span style=\"font-family: Cambria Math;\">- harm or injury to something.</span></p>\\r\\n<p><strong><span style=\"font-family: Cambria Math;\">Satisfied</span></strong><span style=\"font-family: Cambria Math;\">- content and pleased with something.</span></p>\\r\\n<p><strong><span style=\"font-family: Cambria Math;\">Greed</span></strong><span style=\"font-family: Cambria Math;\">- intense and selfish desire for something, especially wealth, power, or food.</span></p>\\r\\n<p><strong><span style=\"font-family: Cambria Math;\">Originates</span></strong><span style=\"font-family: Cambria Math;\"><strong>-</strong> comes from a particular source.</span></p>\\n",
                    solution_hi: "<p>8.(b) <strong>Destruction </strong><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Kokila;\">&#2357;&#2367;&#2344;&#2366;&#2358;</span><span style=\"font-family: Cambria Math;\">) - the act of causing severe damage or ruining something.</span></p>\\r\\n<p><strong><span style=\"font-family: Cambria Math;\">Damage </span></strong><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Kokila;\">&#2325;&#2381;&#2359;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\">) - harm or injury to something.</span></p>\\r\\n<p><strong><span style=\"font-family: Cambria Math;\">Satisfied</span></strong><span style=\"font-family: Cambria Math;\"><strong> </strong>(</span><span style=\"font-family: Kokila;\">&#2360;&#2306;&#2340;&#2369;&#2359;&#2381;&#2335;</span><span style=\"font-family: Cambria Math;\">) - content and pleased with something.</span></p>\\r\\n<p><strong><span style=\"font-family: Cambria Math;\">Greed </span></strong><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Kokila;\">&#2354;&#2366;&#2354;&#2330;</span><span style=\"font-family: Cambria Math;\">) - intense and selfish desire for something, especially wealth, power, or food.</span></p>\\r\\n<p><strong><span style=\"font-family: Cambria Math;\">Originates </span></strong><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Kokila;\">&#2313;&#2340;&#2381;&#2346;&#2340;&#2381;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2379;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\">) - comes from a particular source.</span></p>\\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "7",
                    question_en: "<p>9.<span style=\"font-family: Cambria Math;\"> Select the most appropriate synonym of the given word.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">Repercussion</span></p>\\n",
                    question_hi: "<p>9.<span style=\"font-family: Cambria Math;\"> Select the most appropriate synonym of the given word.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">Repercussion</span></p>\\n",
                    options_en: ["<p>Duplication</p>\\n", "<p>Consequence</p>\\n", 
                                "<p>Recusation</p>\\n", "<p>Apprehension</p>\\r\\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\\n"],
                    options_hi: ["<p>Duplication</p>\\n", "<p>Consequence</p>\\n",
                                "<p>Recusation</p>\\n", "<p>Apprehension</p>\\n"],
                    solution_en: "<p>9.(b) <strong>Consequence</strong><span style=\"font-family: Cambria Math;\">- the result or outcome of a particular action or event.</span></p>\\r\\n<p><strong><span style=\"font-family: Cambria Math;\">Repercussion</span></strong><span style=\"font-family: Cambria Math;\">- an indirect consequence or result of an action or event.</span></p>\\r\\n<p><strong><span style=\"font-family: Cambria Math;\">Duplication</span></strong><span style=\"font-family: Cambria Math;\">- the act of making an exact copy or replica of something.</span></p>\\r\\n<p><strong><span style=\"font-family: Cambria Math;\">Recusation</span></strong><span style=\"font-family: Cambria Math;\">-the act of disqualifying a judge on the grounds of partiality.</span></p>\\r\\n<p><strong><span style=\"font-family: Cambria Math;\">Apprehension</span></strong><span style=\"font-family: Cambria Math;\">- a feeling of anxiety or fear about a future event or outcome.</span></p>\\n",
                    solution_hi: "<p>9.(b) <strong>Consequence</strong><span style=\"font-family: Cambria Math;\"><strong> </strong>(</span><span style=\"font-family: Kokila;\">&#2346;&#2352;&#2367;&#2339;&#2366;&#2350;</span><span style=\"font-family: Cambria Math;\">) - the result or outcome of a particular action or event.</span></p>\\r\\n<p><strong><span style=\"font-family: Cambria Math;\">Repercussion </span></strong><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Kokila;\">&#2309;&#2346;&#2381;&#2352;&#2340;&#2381;&#2351;&#2325;&#2381;&#2359;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2352;&#2367;&#2339;&#2366;&#2350;</span><span style=\"font-family: Cambria Math;\">) - an indirect consequence or result of an action or event.</span></p>\\r\\n<p><strong><span style=\"font-family: Cambria Math;\">Duplication </span></strong><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Kokila;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2354;&#2367;&#2346;&#2367;</span><span style=\"font-family: Cambria Math;\">) - the act of making an exact copy or replica of something.</span></p>\\r\\n<p><strong><span style=\"font-family: Cambria Math;\">Recusation</span></strong><span style=\"font-family: Cambria Math;\"><strong> </strong>(</span><span style=\"font-family: Kokila;\">&#2346;&#2369;&#2344;&#2352;&#2381;&#2357;&#2367;&#2330;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\">) - the act of disqualifying a judge on the grounds of partiality.</span></p>\\r\\n<p><strong><span style=\"font-family: Cambria Math;\">Apprehension </span></strong><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Kokila;\">&#2310;&#2358;&#2306;&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\">/</span><span style=\"font-family: Kokila;\">&#2349;&#2351;</span><span style=\"font-family: Cambria Math;\">) - a feeling of anxiety or fear about a future event or outcome.</span></p>\\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "7",
                    question_en: "<p>10. <span style=\"font-family: Cambria Math;\">Select the option that expresses the given sentence in active voice.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">It will be done by me.</span></p>\\n",
                    question_hi: "<p>10. <span style=\"font-family: Cambria Math;\">Select the option that expresses the given sentence in active voice.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">It will be done by me.</span></p>\\n",
                    options_en: ["<p>I will do it.</p>\\n", "<p>I do it.</p>\\n", 
                                "<p>I did it.</p>\\n", "<p>I have done it.</p>\\r\\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\\n"],
                    options_hi: ["<p>I will do it.</p>\\n", "<p>I do it.</p>\\n",
                                "<p>I did it.</p>\\n", "<p>I have done it.</p>\\n"],
                    solution_en: "<p>10.(a)<span style=\"font-family: Cambria Math;\"> I will do it.(Correct)</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">(b) I </span><span style=\"text-decoration: underline;\"><span style=\"font-weight: 400;\">do</span></span><span style=\"font-weight: 400;\"> it.(Incorrect Tense)</span></span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">(c) I </span><span style=\"text-decoration: underline;\"><span style=\"font-weight: 400;\">did</span></span><span style=\"font-weight: 400;\"> it.(Incorrect Tense)</span></span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">(d) I </span><span style=\"text-decoration: underline;\"><span style=\"font-weight: 400;\">have done</span></span><span style=\"font-weight: 400;\"> it.(Incorrect Tense)</span></span></p>\\n",
                    solution_hi: "<p>10.(a)<span style=\"font-family: Cambria Math;\"> I will do it.(Correct)</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">(b) I </span><span style=\"text-decoration: underline;\"><span style=\"font-weight: 400;\">do</span></span><span style=\"font-weight: 400;\"> it.(Incorrect Tense)</span></span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">(c) I </span><span style=\"text-decoration: underline;\"><span style=\"font-weight: 400;\">did</span></span><span style=\"font-weight: 400;\"> it.(Incorrect Tense)</span></span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">(d) I </span><span style=\"text-decoration: underline;\"><span style=\"font-weight: 400;\">have done</span></span><span style=\"font-weight: 400;\"> it.(Incorrect Tense)</span></span></p>\\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "7",
                    question_en: "<p>11. <span style=\"font-family: Cambria Math;\">Select the most appropriate synonym of the underlined word in the given sentence.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">Let us </span><span style=\"text-decoration: underline;\"><span style=\"font-family: Cambria Math;\">ad</span><span style=\"font-family: Cambria Math;\">j</span><span style=\"font-family: Cambria Math;\">ourn</span></span><span style=\"font-family: Cambria Math;\"> the meeting here, as it is lunchtime now.</span></p>\\n",
                    question_hi: "<p>11. <span style=\"font-family: Cambria Math;\">Select the most appropriate synonym of the underlined word in the given sentence.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">Let us </span><span style=\"text-decoration: underline;\"><span style=\"font-family: Cambria Math;\">ad</span><span style=\"font-family: Cambria Math;\">j</span><span style=\"font-family: Cambria Math;\">ourn</span></span><span style=\"font-family: Cambria Math;\"> the meeting here, as it is lunchtime now.</span></p>\\n",
                    options_en: ["<p>Postpone</p>\\n", "<p>Prohibit</p>\\n", 
                                "<p>Stimulate</p>\\n", "<p>Expedite</p>\\r\\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\\n"],
                    options_hi: ["<p>Postpone</p>\\n", "<p>Prohibit</p>\\n",
                                "<p>Stimulate</p>\\n", "<p>Expedite</p>\\n"],
                    solution_en: "<p>11.(a) <strong>Postpone</strong><span style=\"font-family: Cambria Math;\">- to delay an event or action to a later time.</span></p>\\r\\n<p><strong><span style=\"font-family: Cambria Math;\">Adjourn</span></strong><span style=\"font-family: Cambria Math;\"><strong>-</strong> to postpone or delay a meeting or legal case.</span></p>\\r\\n<p><strong><span style=\"font-family: Cambria Math;\">Prohibit</span></strong><span style=\"font-family: Cambria Math;\">- to forbid or ban something, making it illegal or against the rules.</span></p>\\r\\n<p><strong><span style=\"font-family: Cambria Math;\">Stimulate</span></strong><span style=\"font-family: Cambria Math;\">- to encourage or incite activity by increasing interest or excitement.</span></p>\\r\\n<p><strong><span style=\"font-family: Cambria Math;\">Expedite</span></strong><span style=\"font-family: Cambria Math;\">- to make a process or action happen more quickly or efficiently.</span></p>\\n",
                    solution_hi: "<p>11.(a) <strong>Postpone</strong><span style=\"font-family: Cambria Math;\"><strong> </strong>(</span><span style=\"font-family: Kokila;\">&#2360;&#2381;&#2341;&#2327;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2352;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\">) - to delay an event or action to a later time.</span></p>\\r\\n<p><strong><span style=\"font-family: Cambria Math;\">Adjourn </span></strong><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Kokila;\">&#2360;&#2381;&#2341;&#2327;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2352;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\">) - to postpone or delay a meeting or legal case.</span></p>\\r\\n<p><strong><span style=\"font-family: Cambria Math;\">Prohibit </span></strong><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Kokila;\">&#2344;&#2367;&#2359;&#2375;&#2343;</span><span style=\"font-family: Cambria Math;\">) - to forbid or ban something, making it illegal or against the rules.</span></p>\\r\\n<p><strong><span style=\"font-family: Cambria Math;\">Stimulate </span></strong><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Kokila;\">&#2313;&#2340;&#2381;&#2340;&#2375;&#2332;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2352;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\">) - to encourage or incite activity by increasing interest or excitement.</span></p>\\r\\n<p><strong><span style=\"font-family: Cambria Math;\">Expedite</span></strong><span style=\"font-family: Cambria Math;\"> (</span><span style=\"font-family: Kokila;\">&#2358;&#2368;&#2328;&#2381;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2352;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\">) - to make a process or action happen more quickly or efficiently.</span></p>\\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "7",
                    question_en: "<p>12. <span style=\"font-family: Cambria Math;\">Parts of a sentence are given below in jumbled order, except the first part. Arrange the </span><span style=\"font-family: Cambria Math;\">parts in the correct order to form a meaningful sentence.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">As for the first historical fallacy,...</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\"><strong>A. </strong>only some specialists can unravel its inscrutable mysteries</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\"><strong>B. </strong>that the past was so different</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\"><strong>C. </strong>from the present that</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\"><strong>D.</strong> many people wrongly believe</span></p>\\n",
                    question_hi: "<p>12. <span style=\"font-family: Cambria Math;\">Parts of a sentence are given below in jumbled order, except the first part. Arrange the </span><span style=\"font-family: Cambria Math;\">parts in the correct order to form a meaningful sentence.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">As for the first historical fallacy,...</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\"><strong>A. </strong>only some specialists can unravel its inscrutable mysteries</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\"><strong>B. </strong>that the past was so different</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\"><strong>C. </strong>from the present that</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\"><strong>D.</strong> many people wrongly believe</span></p>\\n",
                    options_en: ["<p>BCDA</p>\\n", "<p>DCBA</p>\\n", 
                                "<p>DBCA</p>\\n", "<p>ABCD</p>\\r\\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\\n"],
                    options_hi: ["<p>BCDA</p>\\n", "<p>DCBA</p>\\n",
                                "<p>DBCA</p>\\n", "<p>ABCD</p>\\n"],
                    solution_en: "<p>12.(c)<span style=\"font-family: Cambria Math;\"> DBCA</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">The given sentence starts with the phrase &lsquo;as for the first historical fallacy&rsquo;. Part D talks about the wrong beliefs of many people &amp; Part B states the wrong belief, i.e. the past was different. So, B will follow D. Further, Part C talks about differences in past and present &amp; Part A states the wrong perception that only some specialist can solve its mysteries. So, A will follow C. Going through the options, option &lsquo;c&rsquo; has the correct sequence.</span></p>\\n",
                    solution_hi: "<p>12.(c)<span style=\"font-family: Cambria Math;\"> DBCA</span></p>\\r\\n<p><span style=\"font-family: Kokila;\">&#2342;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2327;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2357;&#2366;&#2325;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> phrase &lsquo;as for the first historical fallacy&rsquo; </span><span style=\"font-family: Kokila;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2358;&#2369;&#2352;&#2370;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2379;&#2327;&#2366;&#2404;</span><span style=\"font-family: Cambria Math;\"> Part D </span><span style=\"font-family: Kokila;\">&#2325;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2354;&#2379;&#2327;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2327;&#2354;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2366;&#2344;&#2381;&#2351;&#2340;&#2366;&#2323;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2348;&#2366;&#2352;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2348;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2352;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> Part B </span><span style=\"font-family: Kokila;\">&#2327;&#2354;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2343;&#2366;&#2352;&#2339;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2351;&#2366;&#2344;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2309;&#2340;&#2368;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2309;&#2354;&#2327;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2341;&#2366;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Kokila;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2348;&#2366;&#2352;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2348;&#2340;&#2366;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2311;&#2360;&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\">, D </span><span style=\"font-family: Kokila;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2348;&#2366;&#2342;</span><span style=\"font-family: Cambria Math;\"> B </span><span style=\"font-family: Kokila;\">&#2310;&#2319;&#2327;&#2366;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2311;&#2360;&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2309;&#2354;&#2366;&#2357;&#2366;</span><span style=\"font-family: Cambria Math;\">, Part C </span><span style=\"font-family: Kokila;\">&#2309;&#2340;&#2368;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2357;&#2352;&#2381;&#2340;&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2309;&#2306;&#2340;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2348;&#2366;&#2352;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2348;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2352;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> Part A </span><span style=\"font-family: Kokila;\">&#2327;&#2354;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2343;&#2366;&#2352;&#2339;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2348;&#2340;&#2366;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2375;&#2357;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2369;&#2331;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2357;&#2367;&#2358;&#2375;&#2359;&#2332;&#2381;&#2334;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2311;&#2360;&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2352;&#2361;&#2360;&#2381;&#2351;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2369;&#2354;&#2333;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2325;&#2340;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;&#2306;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2311;&#2360;&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\">, C </span><span style=\"font-family: Kokila;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2348;&#2366;&#2342;</span><span style=\"font-family: Cambria Math;\"> A </span><span style=\"font-family: Kokila;\">&#2310;&#2319;&#2327;&#2366;&#2404;</span><span style=\"font-family: Cambria Math;\"> Options </span><span style=\"font-family: Kokila;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2366;&#2343;&#2381;&#2351;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2332;&#2366;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\">, option &lsquo;c&rsquo; </span><span style=\"font-family: Kokila;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2361;&#2368;</span><span style=\"font-family: Cambria Math;\"> sequence </span><span style=\"font-family: Kokila;\">&#2361;&#2376;&#2404;</span></p>\\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "7",
                    question_en: "<p>13. <span style=\"font-family: Cambria Math;\">Select the most appropriate synonym of the given word.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">Hideous</span></p>\\n",
                    question_hi: "<p>13. <span style=\"font-family: Cambria Math;\">Select the most appropriate synonym of the given word.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">Hideous</span></p>\\n",
                    options_en: ["<p>Repulsive</p>\\n", "<p>Pretty</p>\\n", 
                                "<p>Attractive</p>\\n", "<p>Plain</p>\\r\\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\\n"],
                    options_hi: ["<p>Repulsive</p>\\n", "<p>Pretty</p>\\n",
                                "<p>Attractive</p>\\n", "<p>Plain</p>\\n"],
                    solution_en: "<p>13.(a) <strong>Repulsive</strong><span style=\"font-family: Cambria Math;\">- extremely unpleasant or unacceptable.</span></p>\\r\\n<p><strong><span style=\"font-family: Cambria Math;\">Hideous</span></strong><span style=\"font-family: Cambria Math;\">- extremely ugly or unpleasant in appearance.</span></p>\\r\\n<p><strong><span style=\"font-family: Cambria Math;\">Pretty</span></strong><span style=\"font-family: Cambria Math;\">- attractive or pleasant in a delicate way.</span></p>\\r\\n<p><strong><span style=\"font-family: Cambria Math;\">Attractive</span></strong><span style=\"font-family: Cambria Math;\">- appealing and pleasing in appearance or personality.</span></p>\\r\\n<p><strong><span style=\"font-family: Cambria Math;\">Plain</span></strong><span style=\"font-family: Cambria Math;\">- simple or basic in character.</span></p>\\n",
                    solution_hi: "<p>13.(a) <strong>Repulsive </strong><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Kokila;\">&#2328;&#2371;&#2339;&#2366;&#2360;&#2381;&#2346;&#2342;</span><span style=\"font-family: Cambria Math;\">)</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">- extremely unpleasant or unacceptable.</span></p>\\r\\n<p><strong><span style=\"font-family: Cambria Math;\">Hideous </span></strong><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Kokila;\">&#2349;&#2351;&#2306;&#2325;&#2352;</span><span style=\"font-family: Cambria Math;\">)</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">- extremely ugly or unpleasant in appearance.</span></p>\\r\\n<p><strong><span style=\"font-family: Cambria Math;\">Pretty </span></strong><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Kokila;\">&#2360;&#2369;&#2306;&#2342;&#2352;</span><span style=\"font-family: Cambria Math;\">)</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">- attractive or pleasant in a delicate way.</span></p>\\r\\n<p><strong><span style=\"font-family: Cambria Math;\">Attractive </span></strong><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Kokila;\">&#2310;&#2325;&#2352;&#2381;&#2359;&#2325;</span><span style=\"font-family: Cambria Math;\">)</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">- appealing and pleasing in appearance or personality.</span></p>\\r\\n<p><strong><span style=\"font-family: Cambria Math;\">Plain </span></strong><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Kokila;\">&#2360;&#2366;&#2342;&#2366;</span><span style=\"font-family: Cambria Math;\">)</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">- simple or basic in character.</span></p>\\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "7",
                    question_en: "<p>14.<span style=\"font-family: Cambria Math;\"> The following sentence has been split into four segments. Identify the segment that </span><span style=\"font-family: Cambria Math;\">contains a grammatical error.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">An old man whom / we met while coming back / from our college lives / at my uncle&rsquo;s </span><span style=\"font-family: Cambria Math;\">place.</span></p>\\n",
                    question_hi: "<p>14.<span style=\"font-family: Cambria Math;\"> The following sentence has been split into four segments. Identify the segment that </span><span style=\"font-family: Cambria Math;\">contains a grammatical error.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">An old man whom / we met while coming back / from our college lives / at my uncle&rsquo;s </span><span style=\"font-family: Cambria Math;\">place.</span></p>\\n",
                    options_en: ["<p>An old man whom</p>\\n", "<p>at my uncle&rsquo;s place</p>\\n", 
                                "<p>we met while coming back</p>\\n", "<p>from our college lives</p>\\r\\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\\n"],
                    options_hi: ["<p>An old man whom</p>\\n", "<p>at my uncle&rsquo;s place</p>\\n",
                                "<p>we met while coming back</p>\\n", "<p>from our college lives</p>\\n"],
                    solution_en: "<p>14.(a)<span style=\"font-family: Cambria Math;\"> An old man whom</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">&lsquo;An old man&rsquo; must be replaced with &lsquo;the old man&rsquo;. </span><span style=\"font-family: Cambria Math;\">The article &lsquo;the&rsquo;</span><span style=\"font-family: Cambria Math;\"> is used to refer to specific or particular nouns. Similarly, the given sentence talks about a particular old man. Hence, &lsquo;the old man whom&rsquo; is the most appropriate answer.</span></p>\\n",
                    solution_hi: "<p>14.(a)<span style=\"font-family: Cambria Math;\"> An old man whom</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">&lsquo;An old man&rsquo; </span><span style=\"font-family: Kokila;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2381;&#2341;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> &lsquo;the old man&rsquo; </span><span style=\"font-family: Kokila;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2381;&#2352;&#2351;&#2379;&#2327;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2379;&#2327;&#2366;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">Article \'the\' </span><span style=\"font-family: Kokila;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2381;&#2352;&#2351;&#2379;&#2327;</span><span style=\"font-family: Cambria Math;\"> specific or particular nouns </span><span style=\"font-family: Kokila;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2332;&#2366;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2311;&#2360;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2381;&#2352;&#2325;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Kokila;\">&#2342;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2327;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> sentence </span><span style=\"font-family: Kokila;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> particular old man </span><span style=\"font-family: Kokila;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2348;&#2366;&#2352;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2348;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2352;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2309;&#2340;&#2307;</span><span style=\"font-family: Cambria Math;\">, &lsquo;the old man whom&rsquo; </span><span style=\"font-family: Kokila;\">&#2360;&#2348;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2313;&#2346;&#2351;&#2369;&#2325;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2313;&#2340;&#2381;&#2340;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;&#2404;</span></p>\\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "7",
                    question_en: "<p>15. <span style=\"font-family: Cambria Math;\">Select the INCORRECTLY spelt word from the given sentence.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">The crowd jerred and laughed, but the young prince sprang to the gate with his face </span><span style=\"font-family: Cambria Math;\">flushed.</span></p>\\n",
                    question_hi: "<p>15. <span style=\"font-family: Cambria Math;\">Select the INCORRECTLY spelt word from the given sentence.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">The crowd jerred and laughed, but the young prince sprang to the gate with his face </span><span style=\"font-family: Cambria Math;\">flushed.</span></p>\\n",
                    options_en: ["<p>Prince</p>\\n", "<p>Laughed</p>\\n", 
                                "<p>Sprang</p>\\n", "<p>Jerred</p>\\r\\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\\n"],
                    options_hi: ["<p>Prince</p>\\n", "<p>Laughed</p>\\n",
                                "<p>Sprang</p>\\n", "<p>Jerred</p>\\n"],
                    solution_en: "<p>15.(d)<span style=\"font-family: Cambria Math;\"> Jerred</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">&lsquo;Jarred&rsquo; is the correct spelling.</span></p>\\n",
                    solution_hi: "<p>15.(d)<span style=\"font-family: Cambria Math;\"> Jerred</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">&lsquo;Jarred&rsquo; </span><span style=\"font-family: Kokila;\">&#2360;&#2361;&#2368;</span><span style=\"font-family: Cambria Math;\"> spelling </span><span style=\"font-family: Kokila;\">&#2361;&#2376;&#2404;</span></p>\\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "16",
                    section: "7",
                    question_en: "<p>16. <span style=\"font-family: Cambria Math;\">Sentences of a paragraph are given below in jumbled order. Arrange the sentences in </span><span style=\"font-family: Cambria Math;\">the correct order to form a meaningful and coherent paragraph.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\"><strong>A.</strong> Despite numerous setbacks and rejections, he refused to give up on his dream of </span><span style=\"font-family: Cambria Math;\">making it big.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\"><strong>B.</strong> John had always been a talented musician, but he struggled to find his place in the </span><span style=\"font-family: Cambria Math;\">competitive and cutthroat music industry.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\"><strong>C. </strong>With the help of a supportive community of fellow musicians and fans, John </span><span style=\"font-family: Cambria Math;\">continued to hone his craft and develop his own unique sound.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\"><strong>D. </strong>Eventually, his hard work paid off and he landed a record deal with a major label.</span></p>\\n",
                    question_hi: "<p>16. <span style=\"font-family: Cambria Math;\">Sentences of a paragraph are given below in jumbled order. Arrange the sentences in </span><span style=\"font-family: Cambria Math;\">the correct order to form a meaningful and coherent paragraph.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\"><strong>A.</strong> Despite numerous setbacks and rejections, he refused to give up on his dream of </span><span style=\"font-family: Cambria Math;\">making it big.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\"><strong>B.</strong> John had always been a talented musician, but he struggled to find his place in the </span><span style=\"font-family: Cambria Math;\">competitive and cutthroat music industry.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\"><strong>C. </strong>With the help of a supportive community of fellow musicians and fans, John </span><span style=\"font-family: Cambria Math;\">continued to hone his craft and develop his own unique sound.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\"><strong>D. </strong>Eventually, his hard work paid off and he landed a record deal with a major label.</span></p>\\n",
                    options_en: ["<p>BACD</p>\\n", "<p>DCAB</p>\\n", 
                                "<p>BDAC</p>\\n", "<p>CBAD</p>\\n"],
                    options_hi: ["<p>BACD</p>\\n", "<p>DCAB</p>\\n",
                                "<p>BDAC</p>\\n", "<p>CBAD</p>\\n"],
                    solution_en: "<p>16.(a)<span style=\"font-family: Cambria Math;\"> BACD</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">Sentence B will be the starting line as it introduces the main idea of the parajumble, i.e. John&rsquo;s struggle to find a place in the music industry despite being talented. And, Sentence A states his determination for the dream in spite of numerous failures. So, A will follow B. Further, Sentence C states that he was able to develop his own unique sound with the help of fellow musicians &amp; Sentence D states that he eventually succeeded. So, D will follow C. Going through the options, option &lsquo;a&rsquo; has the correct sequence.</span></p>\\n",
                    solution_hi: "<p>16.(a)<span style=\"font-family: Cambria Math;\"> BACD</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">Sentence B </span><span style=\"font-family: Kokila;\">&#2346;&#2381;&#2352;&#2366;&#2352;&#2350;&#2381;&#2349;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> line </span><span style=\"font-family: Kokila;\">&#2361;&#2379;&#2327;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2381;&#2351;&#2379;&#2306;&#2325;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2351;&#2361;</span><span style=\"font-family: Cambria Math;\"> parajumble </span><span style=\"font-family: Kokila;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2369;&#2326;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2357;&#2367;&#2330;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> &lsquo;John&rsquo;s struggle to find a place in the music industry despite being talented&rsquo; </span><span style=\"font-family: Kokila;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2352;&#2367;&#2330;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2342;&#2375;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> Sentence A </span><span style=\"font-family: Kokila;\">&#2325;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2309;&#2360;&#2347;&#2354;&#2340;&#2366;&#2323;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2348;&#2366;&#2357;&#2332;&#2370;&#2342;</span><span style=\"font-family: Cambria Math;\"> dream </span><span style=\"font-family: Kokila;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2313;&#2360;&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2342;&#2371;&#2338;&#2364;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2306;&#2325;&#2354;&#2381;&#2346;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2348;&#2340;&#2366;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2311;&#2360;&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> , B </span><span style=\"font-family: Kokila;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2348;&#2366;&#2342;</span><span style=\"font-family: Cambria Math;\"> A </span><span style=\"font-family: Kokila;\">&#2310;&#2319;&#2327;&#2366;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2311;&#2360;&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2309;&#2354;&#2366;&#2357;&#2366;</span><span style=\"font-family: Cambria Math;\">, Sentence C </span><span style=\"font-family: Kokila;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2361;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2327;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2357;&#2361;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2366;&#2341;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2306;&#2327;&#2368;&#2340;&#2325;&#2366;&#2352;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2342;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2309;&#2346;&#2344;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2309;&#2344;&#2370;&#2336;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2310;&#2357;&#2366;&#2332;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2357;&#2367;&#2325;&#2360;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2352;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2366;&#2350;&#2351;&#2366;&#2348;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2327;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2341;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> Sentence D </span><span style=\"font-family: Kokila;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2361;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2327;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2309;&#2306;&#2340;&#2340;&#2307;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2313;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2347;&#2354;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2367;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2327;&#2351;&#2368;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2311;&#2360;&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\">, C </span><span style=\"font-family: Kokila;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2348;&#2366;&#2342;</span><span style=\"font-family: Cambria Math;\"> D </span><span style=\"font-family: Kokila;\">&#2310;&#2319;&#2327;&#2366;&#2404;</span><span style=\"font-family: Cambria Math;\"> Options </span><span style=\"font-family: Kokila;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2366;&#2343;&#2381;&#2351;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2332;&#2366;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\">, option &lsquo;a&rsquo; </span><span style=\"font-family: Kokila;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2361;&#2368;</span><span style=\"font-family: Cambria Math;\"> sequence </span><span style=\"font-family: Kokila;\">&#2361;&#2376;&#2404;</span></p>\\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "17",
                    section: "7",
                    question_en: "<p>17. <span style=\"font-family: Cambria Math;\">Select the option that can be used as a one-word substitute for the given group of </span><span style=\"font-family: Cambria Math;\">words.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">One who eats human flesh</span></p>\\n",
                    question_hi: "<p>17. <span style=\"font-family: Cambria Math;\">Select the option that can be used as a one-word substitute for the given group of </span><span style=\"font-family: Cambria Math;\">words.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">One who eats human flesh</span></p>\\n",
                    options_en: ["<p>Mammal</p>\\n", "<p>Vegetarian</p>\\n", 
                                "<p>Carnivore</p>\\n", "<p>Cannibal</p>\\r\\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\\n"],
                    options_hi: ["<p>Mammal</p>\\n", "<p>Vegetarian</p>\\n",
                                "<p>Carnivore</p>\\n", "<p>Cannibal</p>\\n"],
                    solution_en: "<p>17.(d)<span style=\"font-family: Cambria Math;\"> </span><strong><span style=\"font-family: Cambria Math;\">Cannibal</span></strong><span style=\"font-family: Cambria Math;\">- one who eats human flesh.</span></p>\\r\\n<p><strong><span style=\"font-family: Cambria Math;\">Mammal</span></strong><span style=\"font-family: Cambria Math;\">- a warm-blooded animal with hair or fur.</span></p>\\r\\n<p><strong><span style=\"font-family: Cambria Math;\">Vegetarian</span></strong><span style=\"font-family: Cambria Math;\">- a person who does not eat meat and relies only on vegetation.</span></p>\\r\\n<p><strong><span style=\"font-family: Cambria Math;\">Carnivore</span></strong><span style=\"font-family: Cambria Math;\">- an animal that feeds on other animals.</span></p>\\n",
                    solution_hi: "<p>17.(d)<span style=\"font-family: Cambria Math;\"> </span><strong><span style=\"font-family: Cambria Math;\">Cannibal</span></strong><span style=\"font-family: Cambria Math;\"><strong> </strong>(</span><span style=\"font-family: Kokila;\">&#2344;&#2352;&#2349;&#2325;&#2381;&#2359;&#2368;</span><span style=\"font-family: Cambria Math;\">) - one who eats human flesh.</span></p>\\r\\n<p><strong><span style=\"font-family: Cambria Math;\">Mammal</span></strong><span style=\"font-family: Cambria Math;\"> (</span><span style=\"font-family: Kokila;\">&#2360;&#2381;&#2340;&#2344;&#2343;&#2366;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\">) - a warm-blooded animal with hair or fur.</span></p>\\r\\n<p><strong><span style=\"font-family: Cambria Math;\">Vegetarian</span></strong><span style=\"font-family: Cambria Math;\"><strong> </strong>(</span><span style=\"font-family: Kokila;\">&#2358;&#2366;&#2325;&#2366;&#2361;&#2366;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\">) - a person who does not eat meat and relies only on vegetation.</span></p>\\r\\n<p><strong><span style=\"font-family: Cambria Math;\">Carnivore</span></strong><span style=\"font-family: Cambria Math;\"><strong> </strong>(</span><span style=\"font-family: Kokila;\">&#2350;&#2366;&#2306;&#2360;&#2366;&#2361;&#2366;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\">) - an animal that feeds on other animals.</span></p>\\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "18",
                    section: "7",
                    question_en: " <p>18.</span><span style=\"font-family:Cambria Math\"> Select the INCORRECTLY spelt word.</span></p>",
                    question_hi: " <p>18.</span><span style=\"font-family:Cambria Math\"> Select the INCORRECTLY spelt word.</span></p>",
                    options_en: [" <p> Acknowledgment</span></p>", " <p> Accomodate</span></p>", 
                                " <p> Address</span></p>", " <p> Acquire</span></p> <p><span style=\"font-family:Cambria Math\"> </span></p>"],
                    options_hi: [" <p> Acknowledgment</span></p>", " <p> Accomodate</span></p>",
                                " <p> Address</span></p>", " <p> Acquire</span></p>"],
                    solution_en: " <p>18.(b)</span><span style=\"font-family:Cambria Math\"> Accomodate</span></p> <p><span style=\"font-family:Cambria Math\">‘Accommodate’ is the correct spelling.</span></p>",
                    solution_hi: " <p>18.(b)</span><span style=\"font-family:Cambria Math\"> Accomodate</span></p> <p><span style=\"font-family:Cambria Math\">‘Accommodate’ </span><span style=\"font-family:Kokila\">सही</span><span style=\"font-family:Cambria Math\"> spelling </span><span style=\"font-family:Kokila\">है।</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "19",
                    section: "7",
                    question_en: "<p>19.<span style=\"font-family: Cambria Math;\"> Choose the correct meaning of the underlined word in the given sentence.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">My friend works as a </span><span style=\"text-decoration: underline;\"><span style=\"font-family: Cambria Math;\">florist</span></span><span style=\"font-family: Cambria Math;\"> at the shop down the street.</span></p>\\n",
                    question_hi: "<p>19.<span style=\"font-family: Cambria Math;\"> Choose the correct meaning of the underlined word in the given sentence.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">My friend works as a </span><span style=\"text-decoration: underline;\"><span style=\"font-family: Cambria Math;\">florist</span></span><span style=\"font-family: Cambria Math;\"> at the shop down the street.</span></p>\\n",
                    options_en: ["<p>A person who sells and arranges cut flowers</p>\\n", "<p>A person who sells sweets</p>\\n", 
                                "<p>Someone who grows flowers</p>\\n", "<p>Someone who works in a bank</p>\\r\\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\\n"],
                    options_hi: ["<p>A person who sells and arranges cut flowers</p>\\n", "<p>A person who sells sweets</p>\\n",
                                "<p>Someone who grows flowers</p>\\n", "<p>Someone who works in a bank</p>\\n"],
                    solution_en: "<p>19.(a)<span style=\"font-family: Cambria Math;\"> Florist- a person who sells and arranges cut flowers.</span></p>\\n",
                    solution_hi: "<p>19.(a)<span style=\"font-family: Cambria Math;\"> Florist (</span><span style=\"font-family: Kokila;\">&#2347;&#2370;&#2354;&#2357;&#2367;&#2325;&#2381;&#2352;&#2375;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\">) - a person who sells and arranges cut flowers.</span></p>\\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "20",
                    section: "7",
                    question_en: "<p>20. <span style=\"font-family: Cambria Math;\">Select the option that expresses the given sentence in active voice.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">My pencils have been stolen.</span></p>\\n",
                    question_hi: "<p>20. <span style=\"font-family: Cambria Math;\">Select the option that expresses the given sentence in active voice.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">My pencils have been stolen.</span></p>\\n",
                    options_en: ["<p>Somebody have stolen my pencils.</p>\\n", "<p>Somebody steals my pencils.</p>\\n", 
                                "<p>Somebody has stolen my pencils.</p>\\n", "<p>Somebody stole my pencils.</p>\\r\\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\\n"],
                    options_hi: ["<p>Somebody have stolen my pencils.</p>\\n", "<p>Somebody steals my pencils.</p>\\n",
                                "<p>Somebody has stolen my pencils.</p>\\n", "<p>Somebody stole my pencils.</p>\\n"],
                    solution_en: "<p>20.(c)<span style=\"font-family: Cambria Math;\"> Somebody has stolen my pencils.(Correct)</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">(a) Somebody </span><span style=\"text-decoration: underline;\"><span style=\"font-weight: 400;\">have</span></span><span style=\"font-weight: 400;\"> stolen my pencils.(Incorrect Helping Verb)</span></span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">(b) Somebody </span><span style=\"text-decoration: underline;\"><span style=\"font-weight: 400;\">steals</span></span><span style=\"font-weight: 400;\"> my pencils.(Incorrect Tense)</span></span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">(d) Somebody </span><span style=\"text-decoration: underline;\"><span style=\"font-weight: 400;\">stole</span></span><span style=\"font-weight: 400;\"> my pencils.(Incorrect Tense)</span></span></p>\\n",
                    solution_hi: "<p>20.(c)<span style=\"font-family: Cambria Math;\"> Somebody has stolen my pencils.(Correct)</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">(a) Somebody </span><span style=\"text-decoration: underline;\"><span style=\"font-weight: 400;\">have</span></span><span style=\"font-weight: 400;\"> stolen my pencils.(&#2327;&#2354;&#2340; Helping Verb)</span></span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">(b) Somebody </span><span style=\"text-decoration: underline;\"><span style=\"font-weight: 400;\">steals</span></span><span style=\"font-weight: 400;\"> my pencils.(&#2327;&#2354;&#2340; Tense)</span></span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">(d) Somebody </span><span style=\"text-decoration: underline;\"><span style=\"font-weight: 400;\">stole</span></span><span style=\"font-weight: 400;\"> my pencils.(&#2327;&#2354;&#2340; Tense)</span></span></p>\\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "21",
                    section: "7",
                    question_en: "<p>21. <strong>Cloze Test:</strong></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">Harry Potter was first introduced in the novel, &lsquo;Harry Potter and the Philosopher&rsquo;s Stone&rsquo;, as </span><span style=\"font-family: Cambria Math;\">an orphan who is (21)________ by his guardian aunt and uncle and their son. On his 11th </span><span style=\"font-family: Cambria Math;\">birthday Harry discovers that his parents were a witch and a wizard and that he, a wizard </span><span style=\"font-family: Cambria Math;\">himself, has been invited to (22)______ Hogwarts School of Witchcraft and Wizardry. He also </span><span style=\"font-family: Cambria Math;\">learns that his parents had not (23)_______ in a car accident, as his aunt and uncle had told </span><span style=\"font-family: Cambria Math;\">him, but that they instead had been murdered by an evil wizard named Voldemort. Harry was </span><span style=\"font-family: Cambria Math;\">the only person to have ever (24) ______ an attack by Voldemort&mdash;by somehow (25)_______ </span><span style=\"font-family: Cambria Math;\">the latter&rsquo;s &lsquo;killing curse&rsquo;&mdash;which left him with a lightning-bolt-shaped scar on his forehead.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">Select the most appropriate option to fill in blank number 21.</span></p>\\n",
                    question_hi: "<p>21.&nbsp; <strong>Cloze Test:</strong></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">Harry Potter was first introduced in the novel, &lsquo;Harry Potter and the Philosopher&rsquo;s Stone&rsquo;, as </span><span style=\"font-family: Cambria Math;\">an orphan who is (21)________ by his guardian aunt and uncle and their son. On his 11th </span><span style=\"font-family: Cambria Math;\">birthday Harry discovers that his parents were a witch and a wizard and that he, a wizard </span><span style=\"font-family: Cambria Math;\">himself, has been invited to (22)______ Hogwarts School of Witchcraft and Wizardry. He also </span><span style=\"font-family: Cambria Math;\">learns that his parents had not (23)_______ in a car accident, as his aunt and uncle had told </span><span style=\"font-family: Cambria Math;\">him, but that they instead had been murdered by an evil wizard named Voldemort. Harry was </span><span style=\"font-family: Cambria Math;\">the only person to have ever (24) ______ an attack by Voldemort&mdash;by somehow (25)_______ </span><span style=\"font-family: Cambria Math;\">the latter&rsquo;s &lsquo;killing curse&rsquo;&mdash;which left him with a lightning-bolt-shaped scar on his forehead.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">Select the most appropriate option to fill in blank number 21.</span></p>\\n",
                    options_en: ["<p>crushed</p>\\n", "<p>mistreated</p>\\n", 
                                "<p>dispirited</p>\\n", "<p>bowed</p>\\n"],
                    options_hi: ["<p>crushed</p>\\n", "<p>mistreated</p>\\n",
                                "<p>dispirited</p>\\n", "<p>bowed</p>\\n"],
                    solution_en: "<p>21.(b)<span style=\"font-family: Cambria Math;\"> Mistreated</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">&lsquo;Mistreat&rsquo; means to treat a person badly, unfairly or cruelly. The given passage states that Harry Potter was an orphan who was treated cruelly by his aunt, uncle and their son. Hence, &lsquo;mistreated&rsquo; is the most appropriate answer.</span></p>\\n",
                    solution_hi: "<p>21.(b)<span style=\"font-family: Cambria Math;\"> Mistreated</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">\'Mistreat\' </span><span style=\"font-family: Kokila;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2309;&#2352;&#2381;&#2341;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2367;&#2360;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2357;&#2381;&#2351;&#2325;&#2381;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2366;&#2341;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2348;&#2369;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Kokila;\">&#2309;&#2344;&#2369;&#2330;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2381;&#2352;&#2370;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2357;&#2381;&#2351;&#2357;&#2361;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2352;&#2344;&#2366;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2342;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2327;&#2319;</span><span style=\"font-family: Cambria Math;\"> passage </span><span style=\"font-family: Kokila;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2361;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2327;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2377;&#2335;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2309;&#2344;&#2366;&#2341;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2341;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2332;&#2367;&#2360;&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2366;&#2341;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2313;&#2360;&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2330;&#2366;&#2330;&#2368;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Kokila;\">&#2330;&#2366;&#2330;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2313;&#2344;&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2348;&#2375;&#2335;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2381;&#2352;&#2370;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2357;&#2381;&#2351;&#2357;&#2361;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2341;&#2366;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2309;&#2340;&#2307;</span><span style=\"font-family: Cambria Math;\">, \'mistreated\' </span><span style=\"font-family: Kokila;\">&#2360;&#2348;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2313;&#2346;&#2351;&#2369;&#2325;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2313;&#2340;&#2381;&#2340;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;&#2404;</span></p>\\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "22",
                    section: "7",
                    question_en: "<p>22.&nbsp;<strong>Cloze Test:</strong></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">Harry Potter was first introduced in the novel, &lsquo;Harry Potter and the Philosopher&rsquo;s Stone&rsquo;, as </span><span style=\"font-family: Cambria Math;\">an orphan who is (21)________ by his guardian aunt and uncle and their son. On his 11th </span><span style=\"font-family: Cambria Math;\">birthday Harry discovers that his parents were a witch and a wizard and that he, a wizard </span><span style=\"font-family: Cambria Math;\">himself, has been invited to (22)______ Hogwarts School of Witchcraft and Wizardry. He also </span><span style=\"font-family: Cambria Math;\">learns that his parents had not (23)_______ in a car accident, as his aunt and uncle had told </span><span style=\"font-family: Cambria Math;\">him, but that they instead had been murdered by an evil wizard named Voldemort. Harry was </span><span style=\"font-family: Cambria Math;\">the only person to have ever (24) ______ an attack by Voldemort&mdash;by somehow (25)_______ </span><span style=\"font-family: Cambria Math;\">the latter&rsquo;s &lsquo;killing curse&rsquo;&mdash;which left him with a lightning-bolt-shaped scar on his forehead.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">Select the most appropriate option to fill in blank number 22.</span></p>\\n",
                    question_hi: "<p>22.&nbsp;<strong>Cloze Test:</strong></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">Harry Potter was first introduced in the novel, &lsquo;Harry Potter and the Philosopher&rsquo;s Stone&rsquo;, as </span><span style=\"font-family: Cambria Math;\">an orphan who is (21)________ by his guardian aunt and uncle and their son. On his 11th </span><span style=\"font-family: Cambria Math;\">birthday Harry discovers that his parents were a witch and a wizard and that he, a wizard </span><span style=\"font-family: Cambria Math;\">himself, has been invited to (22)______ Hogwarts School of Witchcraft and Wizardry. He also </span><span style=\"font-family: Cambria Math;\">learns that his parents had not (23)_______ in a car accident, as his aunt and uncle had told </span><span style=\"font-family: Cambria Math;\">him, but that they instead had been murdered by an evil wizard named Voldemort. Harry was </span><span style=\"font-family: Cambria Math;\">the only person to have ever (24) ______ an attack by Voldemort&mdash;by somehow (25)_______ </span><span style=\"font-family: Cambria Math;\">the latter&rsquo;s &lsquo;killing curse&rsquo;&mdash;which left him with a lightning-bolt-shaped scar on his forehead.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">Select the most appropriate option to fill in blank number 22.</span></p>\\n",
                    options_en: ["<p>see</p>\\n", "<p>escort</p>\\n", 
                                "<p>accompany</p>\\n", "<p>attend</p>\\r\\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\\n"],
                    options_hi: ["<p>see</p>\\n", "<p>escort</p>\\n",
                                "<p>accompany</p>\\n", "<p>attend</p>\\n"],
                    solution_en: "<p>22.(d)<span style=\"font-family: Cambria Math;\"> Attend</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">&lsquo;Attend&rsquo; means to go to an event or a place. The given passage states that Harry Potter had been invited to attend Hogwarts School of Witchcraft and Wizardry. Hence, &lsquo;attend&rsquo; is the most appropriate answer.</span></p>\\n",
                    solution_hi: "<p>22.(d)<span style=\"font-family: Cambria Math;\"> Attend</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">\'Attend\' </span><span style=\"font-family: Kokila;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2309;&#2352;&#2381;&#2341;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2367;&#2360;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2366;&#2352;&#2381;&#2351;&#2325;&#2381;&#2352;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2381;&#2341;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2332;&#2366;&#2344;&#2366;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2342;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2327;&#2319;</span><span style=\"font-family: Cambria Math;\"> passage </span><span style=\"font-family: Kokila;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2361;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2327;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2377;&#2335;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> Hogwarts School of Witchcraft and Wizardry </span><span style=\"font-family: Kokila;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2332;&#2366;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2310;&#2350;&#2306;&#2340;&#2381;&#2352;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2327;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2341;&#2366;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2309;&#2340;&#2307;</span><span style=\"font-family: Cambria Math;\">, \'attend\' </span><span style=\"font-family: Kokila;\">&#2360;&#2348;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2313;&#2346;&#2351;&#2369;&#2325;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2313;&#2340;&#2381;&#2340;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;&#2404;</span></p>\\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "23",
                    section: "7",
                    question_en: "<p>23.&nbsp; <strong>Cloze Test:</strong></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">Harry Potter was first introduced in the novel, &lsquo;Harry Potter and the Philosopher&rsquo;s Stone&rsquo;, as </span><span style=\"font-family: Cambria Math;\">an orphan who is (21)________ by his guardian aunt and uncle and their son. On his 11th </span><span style=\"font-family: Cambria Math;\">birthday Harry discovers that his parents were a witch and a wizard and that he, a wizard </span><span style=\"font-family: Cambria Math;\">himself, has been invited to (22)______ Hogwarts School of Witchcraft and Wizardry. He also </span><span style=\"font-family: Cambria Math;\">learns that his parents had not (23)_______ in a car accident, as his aunt and uncle had told </span><span style=\"font-family: Cambria Math;\">him, but that they instead had been murdered by an evil wizard named Voldemort. Harry was </span><span style=\"font-family: Cambria Math;\">the only person to have ever (24) ______ an attack by Voldemort&mdash;by somehow (25)_______ </span><span style=\"font-family: Cambria Math;\">the latter&rsquo;s &lsquo;killing curse&rsquo;&mdash;which left him with a lightning-bolt-shaped scar on his forehead.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">Select the most appropriate option to fill in blank number 23.</span></p>\\n",
                    question_hi: "<p>23.&nbsp; <strong>Cloze Test:</strong></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">Harry Potter was first introduced in the novel, &lsquo;Harry Potter and the Philosopher&rsquo;s Stone&rsquo;, as </span><span style=\"font-family: Cambria Math;\">an orphan who is (21)________ by his guardian aunt and uncle and their son. On his 11th </span><span style=\"font-family: Cambria Math;\">birthday Harry discovers that his parents were a witch and a wizard and that he, a wizard </span><span style=\"font-family: Cambria Math;\">himself, has been invited to (22)______ Hogwarts School of Witchcraft and Wizardry. He also </span><span style=\"font-family: Cambria Math;\">learns that his parents had not (23)_______ in a car accident, as his aunt and uncle had told </span><span style=\"font-family: Cambria Math;\">him, but that they instead had been murdered by an evil wizard named Voldemort. Harry was </span><span style=\"font-family: Cambria Math;\">the only person to have ever (24) ______ an attack by Voldemort&mdash;by somehow (25)_______ </span><span style=\"font-family: Cambria Math;\">the latter&rsquo;s &lsquo;killing curse&rsquo;&mdash;which left him with a lightning-bolt-shaped scar on his forehead.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">Select the most appropriate option to fill in blank number 23.</span></p>\\n",
                    options_en: ["<p>perished</p>\\n", "<p>departed</p>\\n", 
                                "<p>lost</p>\\n", "<p>vanished</p>\\r\\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\\n"],
                    options_hi: ["<p>perished</p>\\n", "<p>departed</p>\\n",
                                "<p>lost</p>\\n", "<p>vanished</p>\\n"],
                    solution_en: "<p>23.(a)<span style=\"font-family: Cambria Math;\"> Perished</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">&lsquo;Perish&rsquo; means to die, especially in a violent or sudden way. The given passage states that Harry Potter&rsquo;s parents had not died in a car accident. Hence, &lsquo;perished&rsquo; is the most appropriate answer.</span></p>\\n",
                    solution_hi: "<p>23.(a)<span style=\"font-family: Cambria Math;\"> Perished</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">\'<span style=\"font-weight: 400;\">\'Perish\' </span>\' </span><span style=\"font-family: Kokila;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2309;&#2352;&#2381;&#2341;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2352;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Kokila;\">&#2357;&#2367;&#2358;&#2375;&#2359;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2352;&#2370;&#2346;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2367;&#2306;&#2360;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2309;&#2330;&#2366;&#2344;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2340;&#2352;&#2368;&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2375;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2342;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2327;&#2319;</span><span style=\"font-family: Cambria Math;\"> passage </span><span style=\"font-family: Kokila;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2361;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2327;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2377;&#2335;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2366;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\">-</span><span style=\"font-family: Kokila;\">&#2346;&#2367;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2371;&#2340;&#2381;&#2351;&#2369;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2342;&#2369;&#2352;&#2381;&#2328;&#2335;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2344;&#2361;&#2368;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2369;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2341;&#2368;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2309;&#2340;&#2307;</span><span style=\"font-family: Cambria Math;\">, \'Perished\' </span><span style=\"font-family: Kokila;\">&#2360;&#2348;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2313;&#2346;&#2351;&#2369;&#2325;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2313;&#2340;&#2381;&#2340;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;&#2404;</span></p>\\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "24",
                    section: "7",
                    question_en: "<p>24.&nbsp; <strong>Cloze Test:</strong></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">Harry Potter was first introduced in the novel, &lsquo;Harry Potter and the Philosopher&rsquo;s Stone&rsquo;, as </span><span style=\"font-family: Cambria Math;\">an orphan who is (21)________ by his guardian aunt and uncle and their son. On his 11th </span><span style=\"font-family: Cambria Math;\">birthday Harry discovers that his parents were a witch and a wizard and that he, a wizard </span><span style=\"font-family: Cambria Math;\">himself, has been invited to (22)______ Hogwarts School of Witchcraft and Wizardry. He also </span><span style=\"font-family: Cambria Math;\">learns that his parents had not (23)_______ in a car accident, as his aunt and uncle had told </span><span style=\"font-family: Cambria Math;\">him, but that they instead had been murdered by an evil wizard named Voldemort. Harry was </span><span style=\"font-family: Cambria Math;\">the only person to have ever (24) ______ an attack by Voldemort&mdash;by somehow (25)_______ </span><span style=\"font-family: Cambria Math;\">the latter&rsquo;s &lsquo;killing curse&rsquo;&mdash;which left him with a lightning-bolt-shaped scar on his forehead.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">Select the most appropriate option to fill in blank number 24.</span></p>\\n",
                    question_hi: "<p>24.&nbsp;<strong>Cloze Test:</strong></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">Harry Potter was first introduced in the novel, &lsquo;Harry Potter and the Philosopher&rsquo;s Stone&rsquo;, as </span><span style=\"font-family: Cambria Math;\">an orphan who is (21)________ by his guardian aunt and uncle and their son. On his 11th </span><span style=\"font-family: Cambria Math;\">birthday Harry discovers that his parents were a witch and a wizard and that he, a wizard </span><span style=\"font-family: Cambria Math;\">himself, has been invited to (22)______ Hogwarts School of Witchcraft and Wizardry. He also </span><span style=\"font-family: Cambria Math;\">learns that his parents had not (23)_______ in a car accident, as his aunt and uncle had told </span><span style=\"font-family: Cambria Math;\">him, but that they instead had been murdered by an evil wizard named Voldemort. Harry was </span><span style=\"font-family: Cambria Math;\">the only person to have ever (24) ______ an attack by Voldemort&mdash;by somehow (25)_______ </span><span style=\"font-family: Cambria Math;\">the latter&rsquo;s &lsquo;killing curse&rsquo;&mdash;which left him with a lightning-bolt-shaped scar on his forehead.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">Select the most appropriate option to fill in blank number 24.</span></p>\\n",
                    options_en: ["<p>survived</p>\\n", "<p>thrived</p>\\n", 
                                "<p>continued</p>\\n", "<p>tolerated</p>\\r\\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\\n"],
                    options_hi: ["<p>survived</p>\\n", "<p>thrived</p>\\n",
                                "<p>continued</p>\\n", "<p>tolerated</p>\\n"],
                    solution_en: "<p>24.(a)<span style=\"font-family: Cambria Math;\"> Survived</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">&lsquo;Survive&rsquo; means to continue to live in spite of danger. The given passage states that Harry Potter was the only person who survived after being attacked by Voldemort. Hence, &lsquo;survived&rsquo; is the most appropriate answer.</span></p>\\n",
                    solution_hi: "<p>24.(a)<span style=\"font-family: Cambria Math;\"> Survived</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">\'Survive\' </span><span style=\"font-family: Kokila;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2309;&#2352;&#2381;&#2341;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2326;&#2340;&#2352;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2348;&#2366;&#2357;&#2332;&#2370;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2332;&#2368;&#2357;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2352;&#2361;&#2344;&#2366;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2342;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2327;&#2319;</span><span style=\"font-family: Cambria Math;\"> passage </span><span style=\"font-family: Kokila;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2361;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2327;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2377;&#2335;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2319;&#2325;&#2350;&#2366;&#2340;&#2381;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2357;&#2381;&#2351;&#2325;&#2381;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2341;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2332;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2357;&#2379;&#2354;&#2381;&#2337;&#2375;&#2350;&#2377;&#2352;&#2381;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2342;&#2381;&#2357;&#2366;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2350;&#2354;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2332;&#2366;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2348;&#2366;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2348;&#2330;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2327;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2341;&#2366;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2309;&#2340;&#2307;</span><span style=\"font-family: Cambria Math;\">, \'survived\' </span><span style=\"font-family: Kokila;\">&#2360;&#2348;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2313;&#2346;&#2351;&#2369;&#2325;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2313;&#2340;&#2381;&#2340;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;&#2404;</span></p>\\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "25",
                    section: "misc",
                    question_en: "<p>25.&nbsp;<strong>Cloze Test:</strong></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">Harry Potter was first introduced in the novel, &lsquo;Harry Potter and the Philosopher&rsquo;s Stone&rsquo;, as </span><span style=\"font-family: Cambria Math;\">an orphan who is (21)________ by his guardian aunt and uncle and their son. On his 11th </span><span style=\"font-family: Cambria Math;\">birthday Harry discovers that his parents were a witch and a wizard and that he, a wizard </span><span style=\"font-family: Cambria Math;\">himself, has been invited to (22)______ Hogwarts School of Witchcraft and Wizardry. He also </span><span style=\"font-family: Cambria Math;\">learns that his parents had not (23)_______ in a car accident, as his aunt and uncle had told </span><span style=\"font-family: Cambria Math;\">him, but that they instead had been murdered by an evil wizard named Voldemort. Harry was </span><span style=\"font-family: Cambria Math;\">the only person to have ever (24) ______ an attack by Voldemort&mdash;by somehow (25)_______ </span><span style=\"font-family: Cambria Math;\">the latter&rsquo;s &lsquo;killing curse&rsquo;&mdash;which left him with a lightning-bolt-shaped scar on his forehead.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">Select the most appropriate option to fill in blank number 25.</span></p>\\n",
                    question_hi: "<p>25.&nbsp;<strong>Cloze Test:</strong></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">Harry Potter was first introduced in the novel, &lsquo;Harry Potter and the Philosopher&rsquo;s Stone&rsquo;, as </span><span style=\"font-family: Cambria Math;\">an orphan who is (21)________ by his guardian aunt and uncle and their son. On his 11th </span><span style=\"font-family: Cambria Math;\">birthday Harry discovers that his parents were a witch and a wizard and that he, a wizard </span><span style=\"font-family: Cambria Math;\">himself, has been invited to (22)______ Hogwarts School of Witchcraft and Wizardry. He also </span><span style=\"font-family: Cambria Math;\">learns that his parents had not (23)_______ in a car accident, as his aunt and uncle had told </span><span style=\"font-family: Cambria Math;\">him, but that they instead had been murdered by an evil wizard named Voldemort. Harry was </span><span style=\"font-family: Cambria Math;\">the only person to have ever (24) ______ an attack by Voldemort&mdash;by somehow (25)_______ </span><span style=\"font-family: Cambria Math;\">the latter&rsquo;s &lsquo;killing curse&rsquo;&mdash;which left him with a lightning-bolt-shaped scar on his forehead.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">Select the most appropriate option to fill in blank number 25.</span></p>\\n",
                    options_en: ["<p>rallying</p>\\n", "<p>recovering</p>\\n", 
                                "<p>failing</p>\\n", "<p>rebounding</p>\\n"],
                    options_hi: ["<p>rallying</p>\\n", "<p>recovering</p>\\n",
                                "<p>failing</p>\\n", "<p>rebounding</p>\\n"],
                    solution_en: "<p>25.(d)<span style=\"font-family: Cambria Math;\"> Rebounding</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">&lsquo;Rebounding a curse&rsquo; means to redirect the curse towards the person who cast it. The given passage states that Harry Potter survived after being attacked by Voldemort by redirecting the curse towards Voldemort. Hence, &lsquo;rebounding&rsquo; is the most appropriate answer.</span></p>\\n",
                    solution_hi: "<p>25.(d)<span style=\"font-family: Cambria Math;\"> Rebounding</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">\'Rebounding a curse\' </span><span style=\"font-family: Kokila;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2309;&#2352;&#2381;&#2341;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2358;&#2381;&#2352;&#2366;&#2346;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2313;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2357;&#2381;&#2351;&#2325;&#2381;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2323;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2379;&#2337;&#2364;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2332;&#2367;&#2360;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2311;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2342;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2341;&#2366;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2342;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2327;&#2319;</span><span style=\"font-family: Cambria Math;\"> passage </span><span style=\"font-family: Kokila;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2361;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2327;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2357;&#2379;&#2354;&#2381;&#2337;&#2375;&#2350;&#2377;&#2352;&#2381;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2342;&#2381;&#2357;&#2366;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2350;&#2354;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2332;&#2366;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2348;&#2366;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2377;&#2335;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2358;&#2381;&#2352;&#2366;&#2346;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2357;&#2379;&#2354;&#2381;&#2337;&#2375;&#2350;&#2377;&#2352;&#2381;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2323;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2379;&#2337;&#2364;&#2325;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2348;&#2330;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2327;&#2351;&#2366;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2309;&#2340;&#2307;</span><span style=\"font-family: Cambria Math;\">, \'rebounding\' </span><span style=\"font-family: Kokila;\">&#2360;&#2348;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2313;&#2346;&#2351;&#2369;&#2325;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2313;&#2340;&#2381;&#2340;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;&#2404;</span></p>\\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>