<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">15:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 15</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">15</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 15 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 0,
                end: 13
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 14,
                end: 14
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "17",
                    question_en: "<p>1. If 7 cosA = 6, then the numerical value of <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>cosecA</mi><mi mathvariant=\"normal\">&#160;</mi><mo>+</mo><mi mathvariant=\"normal\">&#160;</mi><mi>cotA</mi></mrow><mrow><mi>cosecA</mi><mo>-</mo><mi>cotA</mi></mrow></mfrac></math> is:</p>",
                    question_hi: "<p>1. यदि 7 cosA = 6 है, तो <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>cosecA</mi><mi mathvariant=\"normal\">&#160;</mi><mo>+</mo><mi mathvariant=\"normal\">&#160;</mi><mi>cotA</mi></mrow><mrow><mi>cosecA</mi><mo>-</mo><mi>cotA</mi></mrow></mfrac></math> का संख्&zwj;यात्&zwj;मक मान कितना होगा ?</p>",
                    options_en: ["<p>1</p>", "<p>0</p>", 
                                "<p>13</p>", "<p>-13</p>"],
                    options_hi: ["<p>1</p>", "<p>0</p>",
                                "<p>13</p>", "<p>-13</p>"],
                    solution_en: "<p>1.(c) Given, 7 cosA = 6&nbsp;<br>Cos A = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>6</mn><mn>7</mn></mfrac></math><br>Here B = 6 , H = 7 , P = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>49</mn><mo>-</mo><mn>36</mn></msqrt></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>13</mn></msqrt></math><br>Now , <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>cosecA</mi><mo>+</mo><mi>cotA</mi></mrow><mrow><mi>cosecA</mi><mo>-</mo><mi>cotA</mi></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mstyle displaystyle=\"true\"><mfrac><mn>7</mn><msqrt><mn>13</mn></msqrt></mfrac></mstyle><mo>&#160;</mo><mo>+</mo><mstyle displaystyle=\"true\"><mfrac><mn>6</mn><msqrt><mn>13</mn></msqrt></mfrac></mstyle></mrow><mrow><mstyle displaystyle=\"true\"><mfrac><mn>7</mn><msqrt><mn>13</mn></msqrt></mfrac></mstyle><mo>-</mo><mstyle displaystyle=\"true\"><mfrac><mn>6</mn><msqrt><mn>13</mn></msqrt></mfrac></mstyle></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>13</mn><mn>1</mn></mfrac></math> = 13</p>",
                    solution_hi: "<p>1.(c) दिया गया है, 7 cosA = 6 <br>Cos A = <math display=\"inline\"><mfrac><mrow><mn>6</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math><br>यहां , B = 6 , H = 7 , P = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>49</mn><mo>-</mo><mn>36</mn></msqrt></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>13</mn></msqrt></math><br>अब, <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>cosecA</mi><mo>+</mo><mi>cotA</mi></mrow><mrow><mi>cosecA</mi><mo>-</mo><mi>cotA</mi></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mstyle displaystyle=\"true\"><mfrac><mn>7</mn><msqrt><mn>13</mn></msqrt></mfrac></mstyle><mo>&#160;</mo><mo>+</mo><mstyle displaystyle=\"true\"><mfrac><mn>6</mn><msqrt><mn>13</mn></msqrt></mfrac></mstyle></mrow><mrow><mstyle displaystyle=\"true\"><mfrac><mn>7</mn><msqrt><mn>13</mn></msqrt></mfrac></mstyle><mo>-</mo><mstyle displaystyle=\"true\"><mfrac><mn>6</mn><msqrt><mn>13</mn></msqrt></mfrac></mstyle></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>13</mn><mn>1</mn></mfrac></math> = 13</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "17",
                    question_en: "<p>2. If p(<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math> + cot 30&deg;) = tan<sup>3</sup>60&deg; - 2sin60&deg;, then the value of p is:</p>",
                    question_hi: "<p>2. यदि p(<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math> + cot 30&deg;) = tan<sup>3</sup>60&deg; - 2sin60&deg;, है, तो P का मान ज्ञात करें |</p>",
                    options_en: ["<p>-1</p>", "<p>2<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math></p>", 
                                "<p>1</p>", "<p><math display=\"inline\"><mi mathvariant=\"bold-italic\">&#160;</mi><msqrt><mn>3</mn></msqrt></math></p>"],
                    options_hi: ["<p>-1</p>", "<p>2<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math></p>",
                                "<p>1</p>", "<p><math display=\"inline\"><mi mathvariant=\"bold-italic\">&#160;</mi><msqrt><mn>3</mn></msqrt></math></p>"],
                    solution_en: "<p>2.(c) p (<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math> + cot 30&deg;) = tan<sup>3</sup>60&deg; - 2sin60&deg;<br>Put the value of cot 30&deg; = <math display=\"inline\"><msqrt><mn>3</mn></msqrt></math> , tan 60&deg; =&nbsp;<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math> and sin 60&deg; = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msqrt><mn>3</mn></msqrt><mn>2</mn></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math> p (<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math> + <math display=\"inline\"><msqrt><mn>3</mn></msqrt></math>) = (<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math>)<sup>3</sup> - 2 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msqrt><mn>3</mn></msqrt><mn>2</mn></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math> 2<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math>p = 3<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math> - <math display=\"inline\"><msqrt><mn>3</mn></msqrt></math><br><math display=\"inline\"><mo>&#8658;</mo></math> 2<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math>p = 2<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math><br><math display=\"inline\"><mo>&#8658;</mo></math> p = 1</p>",
                    solution_hi: "<p>2.(c) p(<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math> + cot 30&deg;) = tan<sup>3</sup>60&deg; - 2sin60&deg;<br>cot 30&deg; = <math display=\"inline\"><msqrt><mn>3</mn></msqrt></math> , tan 60&deg; =&nbsp;<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math> और Sin 60&deg; = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msqrt><mn>3</mn></msqrt><mn>2</mn></mfrac></math> के मान रखने पर<br><math display=\"inline\"><mo>&#8658;</mo></math> p (<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math> + <math display=\"inline\"><msqrt><mn>3</mn></msqrt></math>) = (<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math>)<sup>3</sup> - 2 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msqrt><mn>3</mn></msqrt><mn>2</mn></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math> 2<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math>p = 3<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math> - <math display=\"inline\"><msqrt><mn>3</mn></msqrt></math><br><math display=\"inline\"><mo>&#8658;</mo></math> 2<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math>p = 2<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math><br><math display=\"inline\"><mo>&#8658;</mo></math> p = 1</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "17",
                    question_en: "<p>3. If <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><mn>1</mn><mo>-</mo><mi>sin</mi><mi mathvariant=\"normal\">&#160;</mi><mi mathvariant=\"normal\">&#952;</mi></mrow></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><mn>1</mn><mo>+</mo><mi>sin&#952;</mi></mrow></mfrac></math> = 4 sec &theta;, (0 &lt; &theta; &lt; 90&deg;), then the value of (cot&theta; + cosec&theta; ) is:</p>",
                    question_hi: "<p>3. यदि <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><mn>1</mn><mo>-</mo><mi>sin</mi><mi mathvariant=\"normal\">&#160;</mi><mi mathvariant=\"normal\">&#952;</mi></mrow></mfrac></math> +&nbsp; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><mn>1</mn><mo>+</mo><mi>sin&#952;</mi></mrow></mfrac></math> = 4 sec &theta; है , जहां , (0 &lt; &theta; &lt; 90&deg;) है , तो (cot &theta; + cosec &theta; ) का मान ज्ञात कीजिए |</p>",
                    options_en: ["<p><math display=\"inline\"><msqrt><mn>3</mn></msqrt></math></p>", "<p><math display=\"inline\"><msqrt><mn>6</mn></msqrt></math></p>", 
                                "<p><math display=\"inline\"><msqrt><mn>2</mn></msqrt></math></p>", "<p><math display=\"inline\"><msqrt><mn>5</mn></msqrt></math></p>"],
                    options_hi: ["<p><math display=\"inline\"><msqrt><mn>3</mn></msqrt></math></p>", "<p><math display=\"inline\"><msqrt><mn>6</mn></msqrt></math></p>",
                                "<p><math display=\"inline\"><msqrt><mn>2</mn></msqrt></math></p>", "<p><math display=\"inline\"><msqrt><mn>5</mn></msqrt></math></p>"],
                    solution_en: "<p>3.(a) <br><math display=\"inline\"><mo>&#8658;</mo></math>&nbsp;<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><mn>1</mn><mo>-</mo><mi>sin</mi><mi mathvariant=\"normal\">&#160;</mi><mi mathvariant=\"normal\">&#952;</mi></mrow></mfrac></math> +&nbsp; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><mn>1</mn><mo>+</mo><mi>sin&#952;</mi></mrow></mfrac></math> = 4 sec&theta;<br><math display=\"inline\"><mo>&#8658;</mo></math>&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1</mn><mo>+</mo><mi>sin&#952;</mi><mo>+</mo><mn>1</mn><mo>-</mo><mi>sin&#952;</mi></mrow><mrow><mn>1</mn><mo>-</mo><msup><mi>sin</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&#952;</mi></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mi>cos&#952;</mi></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math>&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mrow><msup><mi>cos</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&#952;</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mi>cos&#952;</mi></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math> cos&theta; = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math>= cos 60&deg;<br><math display=\"inline\"><mo>&#8658;</mo></math> &theta; = 60&deg;<br>Now,<br>(cot&theta; + cosec&theta; )<br>= cot 60&deg; + cosec 60&deg;<br>= <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><msqrt><mn>3</mn></msqrt></mrow></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><msqrt><mn>3</mn></msqrt></mfrac></math><br>= <math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><msqrt><mn>3</mn></msqrt></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msqrt><mn>3</mn></msqrt><msqrt><mn>3</mn></msqrt></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math></p>",
                    solution_hi: "<p>3.(a) <br><math display=\"inline\"><mo>&#8658;</mo></math>&nbsp;<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><mn>1</mn><mo>-</mo><mi>sin</mi><mi mathvariant=\"normal\">&#160;</mi><mi mathvariant=\"normal\">&#952;</mi></mrow></mfrac></math> +&nbsp; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><mn>1</mn><mo>+</mo><mi>sin&#952;</mi></mrow></mfrac></math> = 4 sec&theta;<br><math display=\"inline\"><mo>&#8658;</mo></math>&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1</mn><mo>+</mo><mi>sin&#952;</mi><mo>+</mo><mn>1</mn><mo>-</mo><mi>sin&#952;</mi></mrow><mrow><mn>1</mn><mo>-</mo><msup><mi>sin</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&#952;</mi></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mi>cos&#952;</mi></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math>&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mrow><msup><mi>cos</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&#952;</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mi>cos&#952;</mi></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math> cos&theta; = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math>= cos 60&deg;<br><math display=\"inline\"><mo>&#8658;</mo></math> &theta; = 60&deg;<br>अब,<br>(cot&theta; + cosec&theta; )<br>= cot 60&deg; + cosec 60&deg;<br>= <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><msqrt><mn>3</mn></msqrt></mrow></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><msqrt><mn>3</mn></msqrt></mfrac></math><br>= <math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><msqrt><mn>3</mn></msqrt></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msqrt><mn>3</mn></msqrt><msqrt><mn>3</mn></msqrt></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "17",
                    question_en: "<p>4. If sin 31&deg; = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">&#945;</mi></math>, then the value of cot 59&deg; is:</p>",
                    question_hi: "<p>4. यदि sin 31&deg; =&nbsp;<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">&#945;</mi></math>, है, तो cot 59&deg; का मान ज्ञात कीजिए।</p>",
                    options_en: ["<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msqrt><mn>1</mn><mo>-</mo><mi mathvariant=\"normal\">&#160;</mi><msup><mi mathvariant=\"normal\">&#945;</mi><mn>2</mn></msup></msqrt><mrow><mn>2</mn><mi mathvariant=\"normal\">&#945;</mi></mrow></mfrac></math></p>", "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"normal\">&#945;</mi><msqrt><mn>1</mn><mo>-</mo><mi mathvariant=\"normal\">&#160;</mi><msup><mi mathvariant=\"normal\">&#945;</mi><mn>2</mn></msup></msqrt></mfrac></math></p>", 
                                "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msqrt><mn>1</mn><mo>+</mo><mi mathvariant=\"normal\">&#160;</mi><msup><mi mathvariant=\"normal\">&#945;</mi><mn>2</mn></msup></msqrt><mi mathvariant=\"normal\">&#945;</mi></mfrac></math></p>", "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"normal\">&#945;</mi><msqrt><mn>1</mn><mo>+</mo><mi mathvariant=\"normal\">&#160;</mi><msup><mi mathvariant=\"normal\">&#945;</mi><mn>2</mn></msup></msqrt></mfrac></math></p>"],
                    options_hi: ["<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msqrt><mn>1</mn><mo>-</mo><mi mathvariant=\"normal\">&#160;</mi><msup><mi mathvariant=\"normal\">&#945;</mi><mn>2</mn></msup></msqrt><mrow><mn>2</mn><mi mathvariant=\"normal\">&#945;</mi></mrow></mfrac></math></p>", "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"normal\">&#945;</mi><msqrt><mn>1</mn><mo>-</mo><mi mathvariant=\"normal\">&#160;</mi><msup><mi mathvariant=\"normal\">&#945;</mi><mn>2</mn></msup></msqrt></mfrac></math></p>",
                                "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msqrt><mn>1</mn><mo>+</mo><mi mathvariant=\"normal\">&#160;</mi><msup><mi mathvariant=\"normal\">&#945;</mi><mn>2</mn></msup></msqrt><mi mathvariant=\"normal\">&#945;</mi></mfrac></math></p>", "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"normal\">&#945;</mi><msqrt><mn>1</mn><mo>+</mo><mi mathvariant=\"normal\">&#160;</mi><msup><mi mathvariant=\"normal\">&#945;</mi><mn>2</mn></msup></msqrt></mfrac></math></p>"],
                    solution_en: "<p>4.(b) sin 31&deg; = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">&#945;</mi></math><br>sin (90&deg; - 59&deg;) =&nbsp;<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">&#945;</mi></math><br>cos 59&deg; =&nbsp;<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">&#945;</mi></math><br>sin 59&deg; = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>1</mn><mo>-</mo><msup><mi>cos</mi><mn>2</mn></msup><mn>59</mn><mo>&#176;</mo></msqrt></math><br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>1</mn><mo>-</mo><msup><mi mathvariant=\"normal\">&#945;</mi><mn>2</mn></msup></msqrt></math><br>Now,<br>cot 59&deg; =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>cos</mi><mo>&#160;</mo><mn>59</mn><mo>&#176;</mo></mrow><mrow><mi>sin</mi><mo>&#160;</mo><mn>59</mn><mo>&#176;</mo></mrow></mfrac></math> = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"normal\">&#945;</mi><msqrt><mn>1</mn><mo>-</mo><mi mathvariant=\"normal\">&#160;</mi><msup><mi mathvariant=\"normal\">&#945;</mi><mn>2</mn></msup></msqrt></mfrac></math></p>",
                    solution_hi: "<p>4.(b) sin 31&deg; = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">&#945;</mi></math><br>sin (90&deg; - 59&deg;) =&nbsp;<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">&#945;</mi></math><br>cos 59&deg; =&nbsp;<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">&#945;</mi></math><br>sin 59&deg; = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>1</mn><mo>-</mo><msup><mi>cos</mi><mn>2</mn></msup><mn>59</mn><mo>&#176;</mo></msqrt></math><br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>1</mn><mo>-</mo><msup><mi mathvariant=\"normal\">&#945;</mi><mn>2</mn></msup></msqrt></math><br>अब,<br>cot 59&deg; =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>cos</mi><mo>&#160;</mo><mn>59</mn><mo>&#176;</mo></mrow><mrow><mi>sin</mi><mo>&#160;</mo><mn>59</mn><mo>&#176;</mo></mrow></mfrac></math> = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"normal\">&#945;</mi><msqrt><mn>1</mn><mo>-</mo><mi mathvariant=\"normal\">&#160;</mi><msup><mi mathvariant=\"normal\">&#945;</mi><mn>2</mn></msup></msqrt></mfrac></math></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "17",
                    question_en: "<p>5. If <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>tan</mi><mo>(</mo><mi mathvariant=\"normal\">x</mi><mo>)</mo></mrow><mrow><mi>sec</mi><mo>(</mo><mi mathvariant=\"normal\">x</mi><mo>)</mo></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math>, then the value of (sin(x) + cos(x))<sup>2</sup> is _____.</p>",
                    question_hi: "<p>5. यदि <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>tan</mi><mo>(</mo><mi mathvariant=\"normal\">x</mi><mo>)</mo></mrow><mrow><mi>sec</mi><mo>(</mo><mi mathvariant=\"normal\">x</mi><mo>)</mo></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math> है, तो (sin(x) + cos(x ))<sup>2</sup> _____ का मान ज्ञात कीजिए।</p>",
                    options_en: ["<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><mo>+</mo><msqrt><mn>3</mn></msqrt></mrow><mn>3</mn></mfrac></math></p>", "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><mo>+</mo><msqrt><mn>3</mn></msqrt></mrow><mn>2</mn></mfrac></math></p>", 
                                "<p><math display=\"inline\"><mfrac><mrow><mn>2</mn><mo>-</mo><msqrt><mn>3</mn></msqrt></mrow><mrow><mn>2</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>2</mn><mo>-</mo><msqrt><mn>3</mn></msqrt></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>"],
                    options_hi: ["<p><math display=\"inline\"><mfrac><mrow><mn>2</mn><mo>+</mo><msqrt><mn>3</mn></msqrt></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>2</mn><mo>+</mo><msqrt><mn>3</mn></msqrt></mrow><mrow><mn>2</mn></mrow></mfrac></math></p>",
                                "<p><math display=\"inline\"><mfrac><mrow><mn>2</mn><mo>-</mo><msqrt><mn>3</mn></msqrt></mrow><mrow><mn>2</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>2</mn><mo>-</mo><msqrt><mn>3</mn></msqrt></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>"],
                    solution_en: "<p>5.(b) <br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>tan</mi><mo>(</mo><mi mathvariant=\"normal\">x</mi><mo>)</mo></mrow><mrow><mi>sec</mi><mo>(</mo><mi mathvariant=\"normal\">x</mi><mo>)</mo></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math><br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mfrac><mrow><mi>sin</mi><mi mathvariant=\"normal\">&#160;</mi><mi mathvariant=\"normal\">x</mi></mrow><mrow><mi>cos</mi><mi mathvariant=\"normal\">&#160;</mi><mi mathvariant=\"normal\">x</mi></mrow></mfrac><mfrac><mn>1</mn><mrow><mi>cos</mi><mi mathvariant=\"normal\">&#160;</mi><mi mathvariant=\"normal\">x</mi></mrow></mfrac></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math><br>sin x =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math><br>sin x = sin 30&deg; &rArr; x = 30&deg;<br>Now,<br>(sin x&nbsp;+ cos x)<sup>2</sup><br>sin<sup>2</sup> x + cos<sup>2</sup> x + 2sin x.cos x<br>1 + 2sin x.cos x<br>1 + 2 &times;&nbsp;sin30&deg; &times; cos30&deg;<br>1 + 2 &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math> &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msqrt><mn>3</mn></msqrt><mn>2</mn></mfrac></math> &rArr;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><mo>+</mo><msqrt><mn>3</mn></msqrt></mrow><mn>2</mn></mfrac></math></p>",
                    solution_hi: "<p>5.(b) <br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>tan</mi><mo>(</mo><mi mathvariant=\"normal\">x</mi><mo>)</mo></mrow><mrow><mi>sec</mi><mo>(</mo><mi mathvariant=\"normal\">x</mi><mo>)</mo></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math><br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mfrac><mrow><mi>sin</mi><mi mathvariant=\"normal\">&#160;</mi><mi mathvariant=\"normal\">x</mi></mrow><mrow><mi>cos</mi><mi mathvariant=\"normal\">&#160;</mi><mi mathvariant=\"normal\">x</mi></mrow></mfrac><mfrac><mn>1</mn><mrow><mi>cos</mi><mi mathvariant=\"normal\">&#160;</mi><mi mathvariant=\"normal\">x</mi></mrow></mfrac></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math><br>sin x =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math><br>sin x = sin 30&deg; &rArr; x = 30&deg;<br>अब,<br>(sin x&nbsp;+ cos x)<sup>2</sup><br>sin<sup>2</sup> x + cos<sup>2</sup> x + 2sin x.cos x<br>1 + 2sin x.cos x<br>1 + 2 &times;&nbsp;sin30&deg; &times; cos30&deg;<br>1 + 2 &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math> &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msqrt><mn>3</mn></msqrt><mn>2</mn></mfrac></math> &rArr;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><mo>+</mo><msqrt><mn>3</mn></msqrt></mrow><mn>2</mn></mfrac></math></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "17",
                    question_en: "<p>6. If tanA = <math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math>, then find the value of&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>7</mn><mi>sinA</mi><mo>-</mo><mn>3</mn><mi>cosA</mi></mrow><mrow><mn>7</mn><mi>sinA</mi><mo>+</mo><mn>3</mn><mi>cosA</mi></mrow></mfrac></math> + 4.</p>",
                    question_hi: "<p>6. यदि tanA = <math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> है, तो <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>7</mn><mi>sinA</mi><mo>-</mo><mn>3</mn><mi>cosA</mi></mrow><mrow><mn>7</mn><mi>sinA</mi><mo>+</mo><mn>3</mn><mi>cosA</mi></mrow></mfrac></math> + 4 का मान ज्ञात कीजिए।</p>",
                    options_en: ["<p>4<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math></p>", "<p>3<math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math></p>", 
                                "<p>3<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math></p>", "<p>7<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>"],
                    options_hi: ["<p>4<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math></p>", "<p>3<math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math></p>",
                                "<p>3<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math></p>", "<p>7<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>"],
                    solution_en: "<p>6.(a)<br>tanA = <math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math><br>Now,<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>7</mn><mi>sinA</mi><mi mathvariant=\"normal\">&#160;</mi><mo>-</mo><mi mathvariant=\"normal\">&#160;</mi><mn>3</mn><mi>cosA</mi></mrow><mrow><mn>7</mn><mi>sinA</mi><mo>+</mo><mi mathvariant=\"normal\">&#160;</mi><mn>3</mn><mi>cosA</mi></mrow></mfrac></math> + 4<br>On dividing by cos A&nbsp;in fraction we get,<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>7</mn><mi mathvariant=\"normal\">&#160;</mi><mi>tan</mi><mi mathvariant=\"normal\">&#160;</mi><mi mathvariant=\"normal\">A</mi><mo>-</mo><mn>3</mn></mrow><mrow><mn>7</mn><mi mathvariant=\"normal\">&#160;</mi><mi>tan</mi><mi mathvariant=\"normal\">&#160;</mi><mi mathvariant=\"normal\">A</mi><mo>+</mo><mn>3</mn></mrow></mfrac></math> + 4<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>7</mn><mo>&#215;</mo><mfrac><mn>5</mn><mn>7</mn></mfrac><mo>-</mo><mn>3</mn></mrow><mrow><mn>7</mn><mo>&#215;</mo><mfrac><mn>5</mn><mn>7</mn></mfrac><mo>+</mo><mn>3</mn></mrow></mfrac></math> + 4<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5</mn><mo>-</mo><mn>3</mn></mrow><mrow><mn>5</mn><mo>+</mo><mn>3</mn></mrow></mfrac></math> + 4<br><math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math> + 4 = 4<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>4</mn></mfrac></math></p>",
                    solution_hi: "<p>6.(a)<br>tanA = <math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math><br>अब,<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>7</mn><mi>sinA</mi><mi mathvariant=\"normal\">&#160;</mi><mo>-</mo><mi mathvariant=\"normal\">&#160;</mi><mn>3</mn><mi>cosA</mi></mrow><mrow><mn>7</mn><mi>sinA</mi><mo>+</mo><mi mathvariant=\"normal\">&#160;</mi><mn>3</mn><mi>cosA</mi></mrow></mfrac></math> + 4<br>भिन्न में Cos A&nbsp;से भाग देने पर हमें प्राप्त होता है, <br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>7</mn><mi mathvariant=\"normal\">&#160;</mi><mi>tan</mi><mi mathvariant=\"normal\">&#160;</mi><mi mathvariant=\"normal\">A</mi><mo>-</mo><mn>3</mn></mrow><mrow><mn>7</mn><mi mathvariant=\"normal\">&#160;</mi><mi>tan</mi><mi mathvariant=\"normal\">&#160;</mi><mi mathvariant=\"normal\">A</mi><mo>+</mo><mn>3</mn></mrow></mfrac></math> + 4<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>7</mn><mo>&#215;</mo><mfrac><mn>5</mn><mn>7</mn></mfrac><mo>-</mo><mn>3</mn></mrow><mrow><mn>7</mn><mo>&#215;</mo><mfrac><mn>5</mn><mn>7</mn></mfrac><mo>+</mo><mn>3</mn></mrow></mfrac></math> + 4<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5</mn><mo>-</mo><mn>3</mn></mrow><mrow><mn>5</mn><mo>+</mo><mn>3</mn></mrow></mfrac></math> + 4<br><math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math> + 4 = 4<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>4</mn></mfrac></math></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "17",
                    question_en: "<p>7. If sinB = <math display=\"inline\"><mfrac><mrow><mn>15</mn></mrow><mrow><mn>17</mn></mrow></mfrac></math>, what is the value of cosB(sec B - tan B)? Given that 0 &lt; B &lt;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"normal\">&#960;</mi><mn>2</mn></mfrac></math></p>",
                    question_hi: "<p>7. यदि sinB = <math display=\"inline\"><mfrac><mrow><mn>15</mn></mrow><mrow><mn>17</mn></mrow></mfrac></math> है, तो cosB(secB - tanB) का मान कितना होगा? दिया गया है कि 0 &lt; B &lt; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"normal\">&#960;</mi><mn>2</mn></mfrac></math> है।</p>",
                    options_en: ["<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>16</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>15</mn></mrow></mfrac></math></p>", 
                                "<p><math display=\"inline\"><mfrac><mrow><mn>8</mn></mrow><mrow><mn>15</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>17</mn></mrow></mfrac></math></p>"],
                    options_hi: ["<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>16</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>15</mn></mrow></mfrac></math></p>",
                                "<p><math display=\"inline\"><mfrac><mrow><mn>8</mn></mrow><mrow><mn>15</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>17</mn></mrow></mfrac></math></p>"],
                    solution_en: "<p>7.(d)<br>SinB = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>15</mn><mn>17</mn></mfrac></math> <br>now,<br>cosB(secB - tanB)<br>cosB &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi>cosB</mi></mfrac></math> - cos B &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>sinB</mi><mi>cosB</mi></mfrac></math><br>= 1 - sinB<br>= 1 - <math display=\"inline\"><mfrac><mrow><mn>15</mn></mrow><mrow><mn>17</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>17</mn></mfrac></math></p>",
                    solution_hi: "<p>7.(d)<br>SinB = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>15</mn><mn>17</mn></mfrac></math><br>अब,<br>cosB(secB - tanB)<br>cosB &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi>cosB</mi></mfrac></math> - cos B &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>sinB</mi><mi>cosB</mi></mfrac></math><br>= 1 - sinB<br>= 1 - <math display=\"inline\"><mfrac><mrow><mn>15</mn></mrow><mrow><mn>17</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>17</mn></mfrac></math></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "17",
                    question_en: "<p>8. The value of (<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>cosecA</mi><mrow><mi>cosecA</mi><mo>-</mo><mn>1</mn><mi mathvariant=\"normal\">&#160;</mi></mrow></mfrac></math> +&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>cosecA</mi><mrow><mi>cosecA</mi><mo>+</mo><mn>1</mn></mrow></mfrac></math>) (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>secA</mi><mrow><mi>secA</mi><mo>-</mo><mn>1</mn></mrow></mfrac></math> +&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>secA</mi><mrow><mi>secA</mi><mo>+</mo><mn>1</mn></mrow></mfrac></math> )<sup>-1</sup> , when A = 60&deg; is:</p>",
                    question_hi: "<p>8. A = 60&deg; होने पर (<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>cosecA</mi><mrow><mi>cosecA</mi><mo>-</mo><mn>1</mn><mi mathvariant=\"normal\">&#160;</mi></mrow></mfrac></math> +&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>cosecA</mi><mrow><mi>cosecA</mi><mo>+</mo><mn>1</mn></mrow></mfrac></math>) (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>secA</mi><mrow><mi>secA</mi><mo>-</mo><mn>1</mn></mrow></mfrac></math> +&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>secA</mi><mrow><mi>secA</mi><mo>+</mo><mn>1</mn></mrow></mfrac></math> )<sup>-1</sup>&nbsp; का मान क्या होगा?</p>",
                    options_en: ["<p>4</p>", "<p>3</p>", 
                                "<p>1</p>", "<p>2</p>"],
                    options_hi: ["<p>4</p>", "<p>3</p>",
                                "<p>1</p>", "<p>2</p>"],
                    solution_en: "<p>8.(b) <br>(<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>cosecA</mi><mrow><mi>cosecA</mi><mo>-</mo><mn>1</mn><mi mathvariant=\"normal\">&#160;</mi></mrow></mfrac></math> +&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>cosecA</mi><mrow><mi>cosecA</mi><mo>+</mo><mn>1</mn></mrow></mfrac></math>) (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>secA</mi><mrow><mi>secA</mi><mo>-</mo><mn>1</mn></mrow></mfrac></math> +&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>secA</mi><mrow><mi>secA</mi><mo>+</mo><mn>1</mn></mrow></mfrac></math> )<sup>-1</sup> ,<br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mfrac><mrow><msup><mi>cosec</mi><mn>2</mn></msup><mi mathvariant=\"normal\">A</mi><mo>+</mo><mi>cosecA</mi><mo>+</mo><msup><mi>cosec</mi><mn>2</mn></msup><mi mathvariant=\"normal\">A</mi><mo>-</mo><mi>cosecA</mi><mi mathvariant=\"normal\">&#160;</mi></mrow><mrow><msup><mi>cosec</mi><mn>2</mn></msup><mi mathvariant=\"normal\">A</mi><mo>-</mo><msup><mn>1</mn><mn>2</mn></msup></mrow></mfrac><mfrac><mrow><msup><mi>sec</mi><mn>2</mn></msup><mi mathvariant=\"normal\">A</mi><mo>+</mo><mi>secA</mi><mo>+</mo><msup><mi>sec</mi><mn>2</mn></msup><mi mathvariant=\"normal\">A</mi><mo>-</mo><mi>secA</mi><mo>&#160;&#160;</mo><mi mathvariant=\"normal\">&#160;</mi></mrow><mrow><msup><mi>sec</mi><mn>2</mn></msup><mi mathvariant=\"normal\">A</mi><mo>-</mo><msup><mn>1</mn><mn>2</mn></msup><mi mathvariant=\"normal\">&#160;</mi></mrow></mfrac></mfrac></math><br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><mi mathvariant=\"normal\">&#160;</mi><msup><mi>cosec</mi><mn>2</mn></msup><mi mathvariant=\"normal\">A</mi></mrow><mrow><msup><mi>cosec</mi><mn>2</mn></msup><mi mathvariant=\"normal\">A</mi><mo>-</mo><mn>1</mn></mrow></mfrac></math> &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi>sec</mi><mn>2</mn></msup><mi mathvariant=\"normal\">A</mi><mo>-</mo><mn>1</mn></mrow><mrow><mn>2</mn><msup><mi>sec</mi><mn>2</mn></msup><mi mathvariant=\"normal\">A</mi></mrow></mfrac></math><br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi>cosec</mi><mn>2</mn></msup><mi mathvariant=\"normal\">A</mi></mrow><mrow><msup><mi>cot</mi><mn>2</mn></msup><mi mathvariant=\"normal\">A</mi></mrow></mfrac></math> &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi>tan</mi><mn>2</mn></msup><mi mathvariant=\"normal\">A</mi></mrow><mrow><msup><mi>sec</mi><mn>2</mn></msup><mi mathvariant=\"normal\">A</mi></mrow></mfrac></math><br>Now , A = 60&deg;<br>= <math display=\"inline\"><mfrac><mrow><mfrac><mrow><mn>4</mn></mrow><mrow><mn>3</mn></mrow></mfrac></mrow><mrow><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>4</mn></mfrac></math> = 4 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>4</mn></mfrac></math> = 3</p>",
                    solution_hi: "<p>8.(b) <br>(<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>cosecA</mi><mrow><mi>cosecA</mi><mo>-</mo><mn>1</mn><mi mathvariant=\"normal\">&#160;</mi></mrow></mfrac></math> +&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>cosecA</mi><mrow><mi>cosecA</mi><mo>+</mo><mn>1</mn></mrow></mfrac></math>) (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>secA</mi><mrow><mi>secA</mi><mo>-</mo><mn>1</mn></mrow></mfrac></math> +&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>secA</mi><mrow><mi>secA</mi><mo>+</mo><mn>1</mn></mrow></mfrac></math> )<sup>-1</sup> ,<br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mfrac><mrow><msup><mi>cosec</mi><mn>2</mn></msup><mi mathvariant=\"normal\">A</mi><mo>+</mo><mi>cosecA</mi><mo>+</mo><msup><mi>cosec</mi><mn>2</mn></msup><mi mathvariant=\"normal\">A</mi><mo>-</mo><mi>cosecA</mi><mi mathvariant=\"normal\">&#160;</mi></mrow><mrow><msup><mi>cosec</mi><mn>2</mn></msup><mi mathvariant=\"normal\">A</mi><mo>-</mo><msup><mn>1</mn><mn>2</mn></msup></mrow></mfrac><mfrac><mrow><msup><mi>sec</mi><mn>2</mn></msup><mi mathvariant=\"normal\">A</mi><mo>+</mo><mi>secA</mi><mo>+</mo><msup><mi>sec</mi><mn>2</mn></msup><mi mathvariant=\"normal\">A</mi><mo>-</mo><mi>secA</mi><mo>&#160;&#160;</mo><mi mathvariant=\"normal\">&#160;</mi></mrow><mrow><msup><mi>sec</mi><mn>2</mn></msup><mi mathvariant=\"normal\">A</mi><mo>-</mo><msup><mn>1</mn><mn>2</mn></msup><mi mathvariant=\"normal\">&#160;</mi></mrow></mfrac></mfrac></math><br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><mi mathvariant=\"normal\">&#160;</mi><msup><mi>cosec</mi><mn>2</mn></msup><mi mathvariant=\"normal\">A</mi></mrow><mrow><msup><mi>cosec</mi><mn>2</mn></msup><mi mathvariant=\"normal\">A</mi><mo>-</mo><mn>1</mn></mrow></mfrac></math> &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi>sec</mi><mn>2</mn></msup><mi mathvariant=\"normal\">A</mi><mo>-</mo><mn>1</mn></mrow><mrow><mn>2</mn><msup><mi>sec</mi><mn>2</mn></msup><mi mathvariant=\"normal\">A</mi></mrow></mfrac></math><br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi>cosec</mi><mn>2</mn></msup><mi mathvariant=\"normal\">A</mi></mrow><mrow><msup><mi>cot</mi><mn>2</mn></msup><mi mathvariant=\"normal\">A</mi></mrow></mfrac></math> &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi>tan</mi><mn>2</mn></msup><mi mathvariant=\"normal\">A</mi></mrow><mrow><msup><mi>sec</mi><mn>2</mn></msup><mi mathvariant=\"normal\">A</mi></mrow></mfrac></math><br>Now , A = 60&deg;<br>= <math display=\"inline\"><mfrac><mrow><mfrac><mrow><mn>4</mn></mrow><mrow><mn>3</mn></mrow></mfrac></mrow><mrow><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>4</mn></mfrac></math> = 4 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>4</mn></mfrac></math> = 3</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "17",
                    question_en: "<p>9. In a right angle <math display=\"inline\"><mi>&#916;</mi></math> ABC, &ang;B = 90&deg;, if tanA = 1, then 4 sinA cosA = __________.</p>",
                    question_hi: "<p>9. एक समकोण <math display=\"inline\"><mi>&#916;</mi></math>ABC में &ang;B = 90&deg; है, यदि tanA = 1 है, तो 4 sinA cos A = ______ होगा।</p>",
                    options_en: ["<p>1</p>", "<p>2</p>", 
                                "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math></p>", "<p>4</p>"],
                    options_hi: ["<p>1</p>", "<p>2</p>",
                                "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math></p>", "<p>4</p>"],
                    solution_en: "<p>9.(b) In right &Delta;ABC, &ang;B = 90&deg;<br><math display=\"inline\"><mo>&#8658;</mo></math> tan A = 1<br><math display=\"inline\"><mo>&#8658;</mo></math> tan A = tan 45&deg;<br><math display=\"inline\"><mo>&#8658;</mo></math> A = 45&deg;<br>Now,<br>4sinA.cosA<br>= 4 &times; sin45&deg; &times; cos45&deg;<br>= 4 &times; <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><msqrt><mn>2</mn></msqrt></mrow></mfrac></math> &times; <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><msqrt><mn>2</mn></msqrt></mrow></mfrac></math><br>= <math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> = 2</p>",
                    solution_hi: "<p>9.(b) समकोण &Delta;ABC में , &ang;B = 90&deg;<br><math display=\"inline\"><mo>&#8658;</mo></math> tan A = 1<br><math display=\"inline\"><mo>&#8658;</mo></math> tan A = tan 45&deg;<br><math display=\"inline\"><mo>&#8658;</mo></math> A = 45&deg;<br>अब,<br>4sinA.cosA<br>= 4 &times; sin45&deg; &times; cos45&deg;<br>= 4 &times; <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><msqrt><mn>2</mn></msqrt></mrow></mfrac></math> &times; <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><msqrt><mn>2</mn></msqrt></mrow></mfrac></math><br>= <math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> = 2</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "17",
                    question_en: "<p>10. If sec&theta; = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>29</mn><mn>20</mn></mfrac></math> where 0 &lt; &theta; &lt; 90&deg;, then what is the value of 3cosec&theta; + 3cot&theta;?</p>",
                    question_hi: "<p>10. यदि sec&theta; = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>29</mn><mn>20</mn></mfrac></math> है, जहाँ 0 &lt; &theta; &lt; 90&deg;है, तो 3cosec&theta; + 3cot&theta; का मान क्या होगा ?</p>",
                    options_en: ["<p><math display=\"inline\"><mfrac><mrow><mn>7</mn><mi>&#160;</mi></mrow><mrow><mn>3</mn><mi>&#160;</mi></mrow></mfrac></math></p>", "<p>7</p>", 
                                "<p>14</p>", "<p>49</p>"],
                    options_hi: ["<p><math display=\"inline\"><mfrac><mrow><mn>7</mn><mi>&#160;</mi></mrow><mrow><mn>3</mn><mi>&#160;</mi></mrow></mfrac></math></p>", "<p>7</p>",
                                "<p>14</p>", "<p>49</p>"],
                    solution_en: "<p>10.(b) Given : sec&theta; = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>29</mn><mn>20</mn></mfrac></math>&nbsp;=&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>hypotenuse</mi><mi>base</mi></mfrac></math><br>By pythagorean triplet (20 , 21 , 29)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731904634651.png\" alt=\"rId4\" width=\"171\" height=\"137\"><br>Then,<br>3cosec&theta;&nbsp;+ 3cot&theta;<br>= 3 &times; <math display=\"inline\"><mfrac><mrow><mn>29</mn></mrow><mrow><mn>21</mn></mrow></mfrac></math> + 3 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>20</mn><mn>21</mn></mfrac></math><br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>87</mn><mo>+</mo><mn>60</mn></mrow><mn>21</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>147</mn><mn>21</mn></mfrac></math>= 7</p>",
                    solution_hi: "<p>10.(b) दिया गया है : sec&theta; = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>29</mn><mn>20</mn></mfrac></math> =&nbsp; <math style=\"font-family:Verdana\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>&#160;</mo><mi>&#2325;&#2352;&#2381;&#2339;</mi><mo>&#160;</mo></mrow><mrow><mi>&#2310;&#2343;&#2366;&#2352;</mi><mo>&#160;</mo></mrow></mfrac></math><br>(20 , 21 , 29) पायथागॉरियन त्रिक द्वारा<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731904634651.png\" alt=\"rId4\" width=\"195\" height=\"156\"><br>इसलिए ,<br>3cosec&theta;&nbsp;+ 3cot&theta;<br>= 3 &times; <math display=\"inline\"><mfrac><mrow><mn>29</mn></mrow><mrow><mn>21</mn></mrow></mfrac></math> + 3 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>20</mn><mn>21</mn></mfrac></math><br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>87</mn><mo>+</mo><mn>60</mn></mrow><mn>21</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>147</mn><mn>21</mn></mfrac></math>= 7</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "17",
                    question_en: "<p>11. If cos24&deg; = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"normal\">m</mi><mi mathvariant=\"normal\">n</mi></mfrac></math>, then the value of (cosec 24&deg; - cos 66&deg;) is:</p>",
                    question_hi: "<p>11. यदि cos24&deg; = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"normal\">m</mi><mi mathvariant=\"normal\">n</mi></mfrac></math>, तो (cosec 24&deg; - cos 66&deg;) का मान क्या होगा?</p>",
                    options_en: ["<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msup><mi mathvariant=\"normal\">m</mi><mn>2</mn></msup><mrow><mi mathvariant=\"normal\">n</mi><msqrt><msup><mi mathvariant=\"normal\">m</mi><mn>2</mn></msup><mo>-</mo><msup><mi mathvariant=\"normal\">n</mi><mn>2</mn></msup></msqrt></mrow></mfrac></math></p>", "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msup><mi mathvariant=\"normal\">n</mi><mn>2</mn></msup><mrow><mi mathvariant=\"normal\">m</mi><msqrt><msup><mi mathvariant=\"normal\">m</mi><mn>2</mn></msup><mo>-</mo><msup><mi mathvariant=\"normal\">n</mi><mn>2</mn></msup></msqrt></mrow></mfrac></math></p>", 
                                "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msup><mi mathvariant=\"normal\">n</mi><mn>2</mn></msup><mrow><mi mathvariant=\"normal\">m</mi><msqrt><msup><mi mathvariant=\"normal\">n</mi><mn>2</mn></msup><mo>-</mo><msup><mi mathvariant=\"normal\">m</mi><mn>2</mn></msup></msqrt></mrow></mfrac></math></p>", "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msup><mi mathvariant=\"normal\">m</mi><mn>2</mn></msup><mrow><mi mathvariant=\"normal\">n</mi><msqrt><msup><mi mathvariant=\"normal\">n</mi><mn>2</mn></msup><mo>-</mo><msup><mi mathvariant=\"normal\">m</mi><mn>2</mn></msup></msqrt></mrow></mfrac></math></p>"],
                    options_hi: ["<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msup><mi mathvariant=\"normal\">m</mi><mn>2</mn></msup><mrow><mi mathvariant=\"normal\">n</mi><msqrt><msup><mi mathvariant=\"normal\">m</mi><mn>2</mn></msup><mo>-</mo><msup><mi mathvariant=\"normal\">n</mi><mn>2</mn></msup></msqrt></mrow></mfrac></math></p>", "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msup><mi mathvariant=\"normal\">n</mi><mn>2</mn></msup><mrow><mi mathvariant=\"normal\">m</mi><msqrt><msup><mi mathvariant=\"normal\">m</mi><mn>2</mn></msup><mo>-</mo><msup><mi mathvariant=\"normal\">n</mi><mn>2</mn></msup></msqrt></mrow></mfrac></math></p>",
                                "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msup><mi mathvariant=\"normal\">n</mi><mn>2</mn></msup><mrow><mi mathvariant=\"normal\">m</mi><msqrt><msup><mi mathvariant=\"normal\">n</mi><mn>2</mn></msup><mo>-</mo><msup><mi mathvariant=\"normal\">m</mi><mn>2</mn></msup></msqrt></mrow></mfrac></math></p>", "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msup><mi mathvariant=\"normal\">m</mi><mn>2</mn></msup><mrow><mi mathvariant=\"normal\">n</mi><msqrt><msup><mi mathvariant=\"normal\">n</mi><mn>2</mn></msup><mo>-</mo><msup><mi mathvariant=\"normal\">m</mi><mn>2</mn></msup></msqrt></mrow></mfrac></math></p>"],
                    solution_en: "<p>11.(d) <strong>Given:</strong> cos24&deg; = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"normal\">m</mi><mi mathvariant=\"normal\">n</mi></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>base</mi><mi>hypotenuse</mi></mfrac></math><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731904634771.png\" alt=\"rId5\" width=\"169\" height=\"141\"><br>By using pythagoras theorem,<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>AB</mi><mn>2</mn></msup></math> = AC<sup>2</sup> - BC<sup>2</sup><br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>AB</mi><mn>2</mn></msup></math> = n<sup>2</sup> - m<sup>2</sup><br>AB = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mi mathvariant=\"normal\">n</mi><mn>2</mn></msup><mo>-</mo><msup><mi mathvariant=\"normal\">m</mi><mn>2</mn></msup></msqrt></math><br>Now,<br>(cosec 24&deg; - cos 66&deg;)<br>= cosec 24&deg; - cos (90&deg;- 24&deg;)<br>= cosec 24&deg; - sin 24&deg;<br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>hypotenuse</mi><mi>perpendicular</mi></mfrac></math> -&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>perpendicular</mi><mi>hypotenuse</mi></mfrac></math><br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"normal\">n</mi><msqrt><msup><mi mathvariant=\"normal\">n</mi><mn>2</mn></msup><mo>-</mo><msup><mi mathvariant=\"normal\">m</mi><mn>2</mn></msup></msqrt></mfrac></math> -&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi mathvariant=\"normal\">n</mi><mn>2</mn></msup><mo>-</mo><msup><mi mathvariant=\"normal\">m</mi><mn>2</mn></msup></mrow><mi mathvariant=\"normal\">n</mi></mfrac></math><br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi mathvariant=\"normal\">n</mi><mn>2</mn></msup><mo>-</mo><msup><mi mathvariant=\"normal\">n</mi><mn>2</mn></msup><mo>+</mo><msup><mi mathvariant=\"normal\">m</mi><mn>2</mn></msup></mrow><mrow><mi mathvariant=\"normal\">n</mi><msqrt><msup><mi mathvariant=\"normal\">n</mi><mn>2</mn></msup><mo>-</mo><msup><mi mathvariant=\"normal\">m</mi><mn>2</mn></msup></msqrt></mrow></mfrac></math> <br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi mathvariant=\"normal\">&#160;</mi><msup><mi mathvariant=\"normal\">m</mi><mn>2</mn></msup></mrow><mrow><mi mathvariant=\"normal\">n</mi><msqrt><msup><mi mathvariant=\"normal\">n</mi><mn>2</mn></msup><mo>-</mo><msup><mi mathvariant=\"normal\">m</mi><mn>2</mn></msup></msqrt></mrow></mfrac></math></p>",
                    solution_hi: "<p>11.(d) दिया गया है: cos24&deg; = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"normal\">m</mi><mi mathvariant=\"normal\">n</mi></mfrac></math>=&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>&#2310;&#2343;&#2366;&#2352;</mi><mi>&#2325;&#2352;&#2381;&#2339;</mi></mfrac></math><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731904634771.png\" alt=\"rId5\" width=\"169\" height=\"141\"><br>पाइथागोरस प्रमेय का उपयोग करने पर -<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>AB</mi><mn>2</mn></msup></math> = AC<sup>2</sup> - BC<sup>2</sup><br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>AB</mi><mn>2</mn></msup></math> = n<sup>2</sup> - m<sup>2</sup><br>AB = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mi mathvariant=\"normal\">n</mi><mn>2</mn></msup><mo>-</mo><msup><mi mathvariant=\"normal\">m</mi><mn>2</mn></msup></msqrt></math><br>अब,<br>(cosec 24&deg; - cos 66&deg;)<br>= cosec 24&deg; - cos (90&deg;- 24&deg;)<br>= cosec 24&deg; - sin 24&deg;<br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>&#2325;&#2352;&#2381;&#2339;</mi><mi>&#2354;&#2306;&#2348;&#2357;&#2340;</mi></mfrac></math> -&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>&#2354;&#2306;&#2348;&#2357;&#2340;</mi><mi>&#2325;&#2352;&#2381;&#2339;</mi></mfrac></math><br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"normal\">n</mi><msqrt><msup><mi mathvariant=\"normal\">n</mi><mn>2</mn></msup><mo>-</mo><msup><mi mathvariant=\"normal\">m</mi><mn>2</mn></msup></msqrt></mfrac></math> -&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi mathvariant=\"normal\">n</mi><mn>2</mn></msup><mo>-</mo><msup><mi mathvariant=\"normal\">m</mi><mn>2</mn></msup></mrow><mi mathvariant=\"normal\">n</mi></mfrac></math><br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi mathvariant=\"normal\">n</mi><mn>2</mn></msup><mo>-</mo><msup><mi mathvariant=\"normal\">n</mi><mn>2</mn></msup><mo>+</mo><msup><mi mathvariant=\"normal\">m</mi><mn>2</mn></msup></mrow><mrow><mi mathvariant=\"normal\">n</mi><msqrt><msup><mi mathvariant=\"normal\">n</mi><mn>2</mn></msup><mo>-</mo><msup><mi mathvariant=\"normal\">m</mi><mn>2</mn></msup></msqrt></mrow></mfrac></math> <br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi mathvariant=\"normal\">&#160;</mi><msup><mi mathvariant=\"normal\">m</mi><mn>2</mn></msup></mrow><mrow><mi mathvariant=\"normal\">n</mi><msqrt><msup><mi mathvariant=\"normal\">n</mi><mn>2</mn></msup><mo>-</mo><msup><mi mathvariant=\"normal\">m</mi><mn>2</mn></msup></msqrt></mrow></mfrac></math></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "17",
                    question_en: "<p>12. Find the value of<br>[<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>4</mn><mi mathvariant=\"normal\">&#160;</mi><mi>cos</mi><mo>(</mo><mn>90</mn><mo>-</mo><mi mathvariant=\"normal\">&#952;</mi><mo>)</mo><msup><mi>sin</mi><mn>3</mn></msup><mo>(</mo><mn>90</mn><mo>+</mo><mi mathvariant=\"normal\">&#952;</mi><mo>)</mo><mo>-</mo><mn>4</mn><mi>sin</mi><mo>(</mo><mn>90</mn><mo>+</mo><mi mathvariant=\"normal\">&#952;</mi><mo>)</mo><msup><mi>cos</mi><mn>3</mn></msup><mo>(</mo><mn>90</mn><mo>-</mo><mi mathvariant=\"normal\">&#952;</mi><mo>)</mo><mi mathvariant=\"normal\">&#160;</mi></mrow><mrow><mi>cos</mi><mo>(</mo><mfrac><mrow><mi mathvariant=\"normal\">&#160;</mi><mn>180</mn><mi mathvariant=\"normal\">&#160;</mi><mo>+</mo><mi mathvariant=\"normal\">&#160;</mi><mn>8</mn><mi mathvariant=\"normal\">&#952;</mi></mrow><mn>2</mn></mfrac><mo>)</mo></mrow></mfrac></math>]</p>",
                    question_hi: "<p>12.&nbsp;[<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>4</mn><mi mathvariant=\"normal\">&#160;</mi><mi>cos</mi><mo>(</mo><mn>90</mn><mo>-</mo><mi mathvariant=\"normal\">&#952;</mi><mo>)</mo><msup><mi>sin</mi><mn>3</mn></msup><mo>(</mo><mn>90</mn><mo>+</mo><mi mathvariant=\"normal\">&#952;</mi><mo>)</mo><mo>-</mo><mn>4</mn><mi>sin</mi><mo>(</mo><mn>90</mn><mo>+</mo><mi mathvariant=\"normal\">&#952;</mi><mo>)</mo><msup><mi>cos</mi><mn>3</mn></msup><mo>(</mo><mn>90</mn><mo>-</mo><mi mathvariant=\"normal\">&#952;</mi><mo>)</mo><mi mathvariant=\"normal\">&#160;</mi></mrow><mrow><mi>cos</mi><mo>(</mo><mfrac><mrow><mi mathvariant=\"normal\">&#160;</mi><mn>180</mn><mo>+</mo><mn>8</mn><mi mathvariant=\"normal\">&#952;</mi></mrow><mn>2</mn></mfrac><mo>)</mo></mrow></mfrac></math>] का मान ज्ञात कीजिए।</p>",
                    options_en: ["<p>-1</p>", "<p>1</p>", 
                                "<p>2</p>", "<p>0</p>"],
                    options_hi: ["<p>-1</p>", "<p>1</p>",
                                "<p>2</p>", "<p>0</p>"],
                    solution_en: "<p>12.(a) <br>[<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>4</mn><mi mathvariant=\"normal\">&#160;</mi><mi>cos</mi><mo>(</mo><mn>90</mn><mo>-</mo><mi mathvariant=\"normal\">&#952;</mi><mo>)</mo><msup><mi>sin</mi><mn>3</mn></msup><mo>(</mo><mn>90</mn><mo>+</mo><mi mathvariant=\"normal\">&#952;</mi><mo>)</mo><mo>-</mo><mn>4</mn><mi>sin</mi><mo>(</mo><mn>90</mn><mo>+</mo><mi mathvariant=\"normal\">&#952;</mi><mo>)</mo><msup><mi>cos</mi><mn>3</mn></msup><mo>(</mo><mn>90</mn><mo>-</mo><mi mathvariant=\"normal\">&#952;</mi><mo>)</mo><mi mathvariant=\"normal\">&#160;</mi></mrow><mrow><mi>cos</mi><mo>(</mo><mfrac><mrow><mi mathvariant=\"normal\">&#160;</mi><mn>180</mn><mo>+</mo><mn>8</mn><mi mathvariant=\"normal\">&#952;</mi></mrow><mn>2</mn></mfrac><mo>)</mo></mrow></mfrac></math>]<br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>4</mn><mi>sin&#952;</mi><msup><mrow><mo>&#215;</mo><mi>cos</mi></mrow><mn>3</mn></msup><mi mathvariant=\"normal\">&#952;</mi><mo>-</mo><mn>4</mn><mi>cos&#952;</mi><mo>&#215;</mo><msup><mi>sin</mi><mn>3</mn></msup><mi mathvariant=\"normal\">&#952;</mi></mrow><mrow><mo>-</mo><mi mathvariant=\"normal\">&#160;</mi><mi>sin</mi><mi mathvariant=\"normal\">&#160;</mi><mn>4</mn><mi mathvariant=\"normal\">&#952;</mi></mrow></mfrac></math> <br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>4</mn><mi>Sin&#952;Cos&#952;</mi><mo>(</mo><msup><mi>cos</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&#952;</mi><mo>-</mo><msup><mi>sin</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&#952;</mi><mo>)</mo></mrow><mrow><mo>-</mo><mi mathvariant=\"normal\">&#160;</mi><mi>sin</mi><mn>2</mn><mo>&#215;</mo><mo>(</mo><mn>2</mn><mi mathvariant=\"normal\">&#952;</mi><mo>)</mo></mrow></mfrac></math><br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>4</mn><mi>Sin&#952;Cos&#952;</mi><mi mathvariant=\"normal\">&#160;</mi><mo>(</mo><msup><mi>cos</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&#952;</mi><mo>-</mo><msup><mi>sin</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&#952;</mi><mo>)</mo></mrow><mrow><mo>-</mo><mi mathvariant=\"normal\">&#160;</mi><mn>2</mn><mi mathvariant=\"normal\">&#160;</mi><mi>Sin</mi><mn>2</mn><mi>&#952;Cos</mi><mn>2</mn><mi mathvariant=\"normal\">&#952;</mi></mrow></mfrac></math> <br>(∵Cos2&theta; = cos<sup>2</sup>&theta; - sin<sup>2</sup>&theta;)&nbsp;<br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><mi>sin&#952;Cos&#952;</mi></mrow><mrow><mo>-</mo><mi>Sin</mi><mn>2</mn><mi mathvariant=\"normal\">&#952;</mi></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><mi>sin&#952;Cos&#952;</mi></mrow><mrow><mo>-</mo><mn>2</mn><mi>sin&#952;Cos&#952;</mi></mrow></mfrac></math> = -1<br>&nbsp;(∵Sin2&theta; = 2Sin&theta;Cos&theta;)</p>",
                    solution_hi: "<p>12.(a) <br>[<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>4</mn><mi mathvariant=\"normal\">&#160;</mi><mi>cos</mi><mo>(</mo><mn>90</mn><mo>-</mo><mi mathvariant=\"normal\">&#952;</mi><mo>)</mo><msup><mi>sin</mi><mn>3</mn></msup><mo>(</mo><mn>90</mn><mo>+</mo><mi mathvariant=\"normal\">&#952;</mi><mo>)</mo><mo>-</mo><mn>4</mn><mi>sin</mi><mo>(</mo><mn>90</mn><mo>+</mo><mi mathvariant=\"normal\">&#952;</mi><mo>)</mo><msup><mi>cos</mi><mn>3</mn></msup><mo>(</mo><mn>90</mn><mo>-</mo><mi mathvariant=\"normal\">&#952;</mi><mo>)</mo><mi mathvariant=\"normal\">&#160;</mi></mrow><mrow><mi>cos</mi><mo>(</mo><mfrac><mrow><mi mathvariant=\"normal\">&#160;</mi><mn>180</mn><mo>+</mo><mn>8</mn><mi mathvariant=\"normal\">&#952;</mi></mrow><mn>2</mn></mfrac><mo>)</mo></mrow></mfrac></math>]<br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>4</mn><mi>sin&#952;</mi><msup><mrow><mo>&#215;</mo><mi>cos</mi></mrow><mn>3</mn></msup><mi mathvariant=\"normal\">&#952;</mi><mo>-</mo><mn>4</mn><mi>cos&#952;</mi><mo>&#215;</mo><msup><mi>sin</mi><mn>3</mn></msup><mi mathvariant=\"normal\">&#952;</mi></mrow><mrow><mo>-</mo><mi mathvariant=\"normal\">&#160;</mi><mi>sin</mi><mi mathvariant=\"normal\">&#160;</mi><mn>4</mn><mi mathvariant=\"normal\">&#952;</mi></mrow></mfrac></math> <br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>4</mn><mi>Sin&#952;Cos&#952;</mi><mo>(</mo><msup><mi>cos</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&#952;</mi><mo>-</mo><msup><mi>sin</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&#952;</mi><mo>)</mo></mrow><mrow><mo>-</mo><mi mathvariant=\"normal\">&#160;</mi><mi>sin</mi><mn>2</mn><mo>&#215;</mo><mo>(</mo><mn>2</mn><mi mathvariant=\"normal\">&#952;</mi><mo>)</mo></mrow></mfrac></math><br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>4</mn><mi>Sin&#952;Cos&#952;</mi><mi mathvariant=\"normal\">&#160;</mi><mo>(</mo><msup><mi>cos</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&#952;</mi><mo>-</mo><msup><mi>sin</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&#952;</mi><mo>)</mo></mrow><mrow><mo>-</mo><mi mathvariant=\"normal\">&#160;</mi><mn>2</mn><mi mathvariant=\"normal\">&#160;</mi><mi>Sin</mi><mn>2</mn><mi>&#952;Cos</mi><mn>2</mn><mi mathvariant=\"normal\">&#952;</mi></mrow></mfrac></math> <br>(∵Cos2&theta; = cos<sup>2</sup>&theta; - sin<sup>2</sup>&theta;)&nbsp;<br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><mi>sin&#952;Cos&#952;</mi></mrow><mrow><mo>-</mo><mi>Sin</mi><mn>2</mn><mi mathvariant=\"normal\">&#952;</mi></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><mi>sin&#952;Cos&#952;</mi></mrow><mrow><mo>-</mo><mn>2</mn><mi>sin&#952;Cos&#952;</mi></mrow></mfrac></math> = -1<br>&nbsp;(∵Sin2&theta; = 2Sin&theta;Cos&theta;)</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "17",
                    question_en: "<p>13. If sinA + cosA = <math display=\"inline\"><mfrac><mrow><mi>&#160;</mi><mn>1</mn></mrow><mrow><mn>2</mn><msqrt><mn>2</mn></msqrt></mrow></mfrac></math>, then find the value of sin<sup>4</sup>A + cos<sup>4</sup>A.</p>",
                    question_hi: "<p>13. यदि sinA + cosA = <math display=\"inline\"><mfrac><mrow><mi>&#160;</mi><mn>1</mn></mrow><mrow><mn>2</mn><msqrt><mn>2</mn></msqrt></mrow></mfrac></math>है, तो sin<sup>4</sup>A + cos<sup>4</sup>A का मान ज्ञात करें।</p>",
                    options_en: ["<p><math display=\"inline\"><mfrac><mrow><mn>79</mn><mi>&#160;</mi></mrow><mrow><mn>128</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mi>&#160;</mi><mn>7</mn></mrow><mrow><mn>13</mn></mrow></mfrac></math></p>", 
                                "<p><math display=\"inline\"><mfrac><mrow><mi>&#160;</mi><mn>79</mn></mrow><mrow><mn>126</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mi>&#160;</mi><mn>5</mn></mrow><mrow><mn>13</mn></mrow></mfrac></math></p>"],
                    options_hi: ["<p><math display=\"inline\"><mfrac><mrow><mn>79</mn><mi>&#160;</mi></mrow><mrow><mn>128</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mi>&#160;</mi><mn>7</mn></mrow><mrow><mn>13</mn></mrow></mfrac></math></p>",
                                "<p><math display=\"inline\"><mfrac><mrow><mi>&#160;</mi><mn>79</mn></mrow><mrow><mn>126</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mi>&#160;</mi><mn>5</mn></mrow><mrow><mn>13</mn></mrow></mfrac></math></p>"],
                    solution_en: "<p>13.(a) sinA + cosA = <math display=\"inline\"><mfrac><mrow><mi>&#160;</mi><mn>1</mn></mrow><mrow><mn>2</mn><msqrt><mn>2</mn></msqrt></mrow></mfrac></math> <br>Squaring both side , we get <br><math display=\"inline\"><mo>&#8658;</mo></math> Sin<sup>2</sup>A+Cos<sup>2</sup>A + 2 SinA Cos A = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>8</mn></mfrac></math>&nbsp;<br><math display=\"inline\"><mo>&#8658;</mo></math> 2 SinA Cos A = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>8</mn></mfrac></math> - 1 =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>-</mo><mn>7</mn></mrow><mn>8</mn></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math> SinA Cos A =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>-</mo><mn>7</mn></mrow><mn>16</mn></mfrac></math><br>Now , we know that <br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>Sin</mi><mn>2</mn></msup><mi mathvariant=\"normal\">A</mi><mo>+</mo><msup><mi>Cos</mi><mn>2</mn></msup><mi mathvariant=\"normal\">A</mi></math> = 1<br>Again Squaring both side , we get <br><math display=\"inline\"><mo>&#8658;</mo></math> sin<sup>4</sup>A + cos<sup>4</sup>A + 2 Sin<sup>2</sup>A Cos<sup>2</sup>A = 1<br><math display=\"inline\"><mo>&#8658;</mo></math> sin<sup>4</sup>A + cos<sup>4</sup>A + 2 &times; (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>49</mn><mn>256</mn></mfrac></math>) = 1<br><math display=\"inline\"><mo>&#8658;</mo></math> sin<sup>4</sup>A + cos<sup>4</sup>A = 1 -&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>49</mn><mn>128</mn></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>79</mn><mn>128</mn></mfrac></math></p>",
                    solution_hi: "<p>13.(a) sinA + cosA = <math display=\"inline\"><mfrac><mrow><mi>&#160;</mi><mn>1</mn></mrow><mrow><mn>2</mn><msqrt><mn>2</mn></msqrt></mrow></mfrac></math><br>दोनों पक्षों का वर्ग करने पर, हमें प्राप्त होता है <br><math display=\"inline\"><mo>&#8658;</mo></math> Sin<sup>2</sup>A+Cos<sup>2</sup>A + 2 SinA Cos A = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>8</mn></mfrac></math>&nbsp;<br><math display=\"inline\"><mo>&#8658;</mo></math> 2 SinA Cos A = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>8</mn></mfrac></math> - 1 =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>-</mo><mn>7</mn></mrow><mn>8</mn></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math> SinA Cos A =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>-</mo><mn>7</mn></mrow><mn>16</mn></mfrac></math><br>अब, हम यह जानते हैं <br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>Sin</mi><mn>2</mn></msup><mi mathvariant=\"normal\">A</mi><mo>+</mo><msup><mi>Cos</mi><mn>2</mn></msup><mi mathvariant=\"normal\">A</mi></math> = 1<br>पुनः दोनों पक्षों का वर्ग करने पर, हमें प्राप्त होता है <br><math display=\"inline\"><mo>&#8658;</mo></math> sin<sup>4</sup>A + cos<sup>4</sup>A + 2 Sin<sup>2</sup>A Cos<sup>2</sup>A = 1<br><math display=\"inline\"><mo>&#8658;</mo></math> sin<sup>4</sup>A + cos<sup>4</sup>A + 2 &times; (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>49</mn><mn>256</mn></mfrac></math>) = 1<br><math display=\"inline\"><mo>&#8658;</mo></math> sin<sup>4</sup>A + cos<sup>4</sup>A = 1 -&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>49</mn><mn>128</mn></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>79</mn><mn>128</mn></mfrac></math></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "17",
                    question_en: "<p>14. If a = xcos&theta; + ysin&theta; and b = xsin&theta; - ycos&theta; , then a<sup>2</sup>+ b<sup>2</sup> is equal to:</p>",
                    question_hi: "<p>14. यदि a = xcos&theta; + ysin&theta; और b = xsin&theta; - ycos&theta; हैं, तो a<sup>2</sup> + b<sup>2</sup> का मान कितना होगा?</p>",
                    options_en: ["<p><math display=\"inline\"><mi>x</mi></math> + y</p>", "<p>x<sup>2</sup> - y<sup>2</sup></p>", 
                                "<p>x<sup>2</sup> + y<sup>2</sup></p>", "<p><math display=\"inline\"><mi>x</mi></math> - y</p>"],
                    options_hi: ["<p><math display=\"inline\"><mi>x</mi></math> + y</p>", "<p>x<sup>2</sup> - y<sup>2</sup></p>",
                                "<p>x<sup>2</sup> + y<sup>2</sup></p>", "<p>x - y</p>"],
                    solution_en: "<p>14.(c) if <br>a = xcos&theta; + ysin&theta; and b = xsin&theta; - ycos&theta;&nbsp;<br>Then a<sup>2</sup> + b<sup>2</sup> = x<sup>2</sup> + y<sup>2</sup> (by using direct result)</p>",
                    solution_hi: "<p>14.(c) यदि a = xcos&theta; + ysin&theta; और b&nbsp;= xsin&theta; - ycos&theta;</p>\n<p>फिर&nbsp;<br>a<sup>2</sup> + b<sup>2</sup> = x<sup>2 </sup>+ y<sup>2 </sup>(प्रत्यक्ष परिणाम का उपयोग करके )</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "misc",
                    question_en: "<p>15. If cosec x = <math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math>, then the value of&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5</mn><mo>&#160;</mo><msup><mi>sin</mi><mn>2</mn></msup><mi mathvariant=\"normal\">x</mi><mo>+</mo><mn>8</mn><mo>&#160;</mo><msup><mi>cot</mi><mn>2</mn></msup><mi mathvariant=\"normal\">x</mi></mrow><mrow><mn>27</mn><mo>&#160;</mo><msup><mi>tan</mi><mn>2</mn></msup><mi mathvariant=\"normal\">x</mi><mo>-</mo><mn>125</mn><mo>&#160;</mo><msup><mi>cos</mi><mn>2</mn></msup><mi mathvariant=\"normal\">x</mi></mrow></mfrac></math> is:</p>",
                    question_hi: "<p>15. यदि cosec x = <math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> है, तो <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5</mn><mo>&#160;</mo><msup><mi>sin</mi><mn>2</mn></msup><mi mathvariant=\"normal\">x</mi><mo>+</mo><mn>8</mn><mo>&#160;</mo><msup><mi>cot</mi><mn>2</mn></msup><mi mathvariant=\"normal\">x</mi></mrow><mrow><mn>27</mn><mo>&#160;</mo><msup><mi>tan</mi><mn>2</mn></msup><mi mathvariant=\"normal\">x</mi><mo>-</mo><mn>125</mn><mo>&#160;</mo><msup><mi>cos</mi><mn>2</mn></msup><mi mathvariant=\"normal\">x</mi></mrow></mfrac></math> का मान ज्ञात कीजिए।</p>",
                    options_en: ["<p><math display=\"inline\"><mfrac><mrow><mn>33</mn></mrow><mrow><mn>91</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>77</mn></mrow><mrow><mn>30</mn></mrow></mfrac></math></p>", 
                                "<p><math display=\"inline\"><mfrac><mrow><mn>55</mn></mrow><mrow><mn>87</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>99</mn></mrow><mrow><mn>47</mn></mrow></mfrac></math></p>"],
                    options_hi: ["<p><math display=\"inline\"><mfrac><mrow><mn>33</mn></mrow><mrow><mn>91</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>77</mn></mrow><mrow><mn>30</mn></mrow></mfrac></math></p>",
                                "<p><math display=\"inline\"><mfrac><mrow><mn>55</mn></mrow><mrow><mn>87</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>99</mn></mrow><mrow><mn>47</mn></mrow></mfrac></math></p>"],
                    solution_en: "<p>15.(b) Given, cosec x = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>hypotenuse</mi><mi>perpendicular</mi></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>4</mn></mfrac></math><br>By using pythagorean triplet (4, 3, 5) , base = 3<br>Now,<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mrow><mn>5</mn><mi mathvariant=\"normal\">&#160;</mi><mi>sin</mi></mrow><mn>2</mn></msup><mi mathvariant=\"normal\">x</mi><mo>+</mo><msup><mrow><mn>8</mn><mi mathvariant=\"normal\">&#160;</mi><mi>cot</mi></mrow><mn>2</mn></msup><mi mathvariant=\"normal\">x</mi></mrow><mrow><msup><mrow><mn>27</mn><mi mathvariant=\"normal\">&#160;</mi><mi>tan</mi></mrow><mn>2</mn></msup><mi mathvariant=\"normal\">x</mi><mo>-</mo><msup><mrow><mn>125</mn><mi mathvariant=\"normal\">&#160;</mi><mi>cos</mi></mrow><mn>2</mn></msup><mi mathvariant=\"normal\">x</mi></mrow></mfrac></math> <br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5</mn><mo>&#215;</mo><msup><mrow><mo>(</mo><mfrac><mn>4</mn><mn>5</mn></mfrac><mo>)</mo></mrow><mn>2</mn></msup><mo>+</mo><mn>8</mn><mo>&#215;</mo><msup><mrow><mo>(</mo><mfrac><mn>3</mn><mn>4</mn></mfrac><mo>)</mo></mrow><mn>2</mn></msup></mrow><mrow><mn>27</mn><mo>&#215;</mo><msup><mrow><mo>(</mo><mfrac><mn>4</mn><mn>3</mn></mfrac><mo>)</mo></mrow><mn>2</mn></msup><mo>-</mo><mn>125</mn><mo>&#215;</mo><msup><mrow><mo>(</mo><mfrac><mn>3</mn><mn>5</mn></mfrac><mo>)</mo></mrow><mn>2</mn></msup></mrow></mfrac></math><br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5</mn><mo>&#215;</mo><mfrac><mn>16</mn><mn>25</mn></mfrac><mo>+</mo><mn>8</mn><mo>&#215;</mo><mfrac><mn>9</mn><mn>16</mn></mfrac></mrow><mrow><mn>27</mn><mo>&#215;</mo><mfrac><mn>16</mn><mn>9</mn></mfrac><mo>-</mo><mn>125</mn><mo>&#215;</mo><mfrac><mn>9</mn><mn>25</mn></mfrac></mrow></mfrac></math><br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mfrac><mn>16</mn><mn>5</mn></mfrac><mo>+</mo><mfrac><mn>9</mn><mn>2</mn></mfrac></mrow><mrow><mn>48</mn><mo>-</mo><mn>45</mn></mrow></mfrac></math><br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>32</mn><mo>+</mo><mn>45</mn></mrow><mrow><mn>3</mn><mo>&#215;</mo><mn>10</mn></mrow></mfrac></math><br>= <math display=\"inline\"><mfrac><mrow><mn>77</mn></mrow><mrow><mn>30</mn></mrow></mfrac></math></p>",
                    solution_hi: "<p>15.(b) दिया गया है, cosec x = <math style=\"font-family:Verdana\" display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2325;&#2352;&#2381;&#2339;</mi><mo>&#160;</mo></mrow><mrow><mi>&#2354;&#2306;&#2348;</mi><mi mathvariant=\"normal\">&#160;</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>4</mn></mfrac></math><br>पायथागॉरियन त्रिक (4, 3, 5) का उपयोग करके , आधार = 3<br>अब,<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mrow><mn>5</mn><mi mathvariant=\"normal\">&#160;</mi><mi>sin</mi></mrow><mn>2</mn></msup><mi mathvariant=\"normal\">x</mi><mo>+</mo><msup><mrow><mn>8</mn><mi mathvariant=\"normal\">&#160;</mi><mi>cot</mi></mrow><mn>2</mn></msup><mi mathvariant=\"normal\">x</mi></mrow><mrow><msup><mrow><mn>27</mn><mi mathvariant=\"normal\">&#160;</mi><mi>tan</mi></mrow><mn>2</mn></msup><mi mathvariant=\"normal\">x</mi><mo>-</mo><msup><mrow><mn>125</mn><mi mathvariant=\"normal\">&#160;</mi><mi>cos</mi></mrow><mn>2</mn></msup><mi mathvariant=\"normal\">x</mi></mrow></mfrac></math> <br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5</mn><mo>&#215;</mo><msup><mrow><mo>(</mo><mfrac><mn>4</mn><mn>5</mn></mfrac><mo>)</mo></mrow><mn>2</mn></msup><mo>+</mo><mn>8</mn><mo>&#215;</mo><msup><mrow><mo>(</mo><mfrac><mn>3</mn><mn>4</mn></mfrac><mo>)</mo></mrow><mn>2</mn></msup></mrow><mrow><mn>27</mn><mo>&#215;</mo><msup><mrow><mo>(</mo><mfrac><mn>4</mn><mn>3</mn></mfrac><mo>)</mo></mrow><mn>2</mn></msup><mo>-</mo><mn>125</mn><mo>&#215;</mo><msup><mrow><mo>(</mo><mfrac><mn>3</mn><mn>5</mn></mfrac><mo>)</mo></mrow><mn>2</mn></msup></mrow></mfrac></math><br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5</mn><mo>&#215;</mo><mfrac><mn>16</mn><mn>25</mn></mfrac><mo>+</mo><mn>8</mn><mo>&#215;</mo><mfrac><mn>9</mn><mn>16</mn></mfrac></mrow><mrow><mn>27</mn><mo>&#215;</mo><mfrac><mn>16</mn><mn>9</mn></mfrac><mo>-</mo><mn>125</mn><mo>&#215;</mo><mfrac><mn>9</mn><mn>25</mn></mfrac></mrow></mfrac></math><br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mfrac><mn>16</mn><mn>5</mn></mfrac><mo>+</mo><mfrac><mn>9</mn><mn>2</mn></mfrac></mrow><mrow><mn>48</mn><mo>-</mo><mn>45</mn></mrow></mfrac></math><br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>32</mn><mo>+</mo><mn>45</mn></mrow><mrow><mn>3</mn><mo>&#215;</mo><mn>10</mn></mrow></mfrac></math><br>= <math display=\"inline\"><mfrac><mrow><mn>77</mn></mrow><mrow><mn>30</mn></mrow></mfrac></math></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>