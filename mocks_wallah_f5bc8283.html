<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">135:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 150</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">150</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 135 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["misc"] = {
                name: "Miscellaneous",
                start: 0,
                end: 149
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "misc",
                    question_en: "<p>1. If <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>[</mo><msup><mrow><mrow><mo>{</mo><msup><mrow><mo>(</mo><mfrac><mn>2</mn><mn>3</mn></mfrac><mo>)</mo></mrow><mn>3</mn></msup></mrow><mo>}</mo></mrow><mrow><mo>(</mo><mn>2</mn><mi mathvariant=\"normal\">x</mi><mo>+</mo><mn>3</mn><mo>)</mo></mrow></msup><mo>]</mo></mrow><mfrac><mn>3</mn><mn>4</mn></mfrac></msup><mo>=</mo><mo>[</mo><msup><mrow><mo>{</mo><msup><mrow><mo>(</mo><mfrac><mn>2</mn><mn>3</mn></mfrac><mo>)</mo></mrow><mfrac><mn>2</mn><mn>3</mn></mfrac></msup><mo>}</mo></mrow><mrow><mo>(</mo><mn>3</mn><mi mathvariant=\"normal\">x</mi><mo>+</mo><mn>7</mn><mo>)</mo></mrow></msup><msup><mo>]</mo><mrow><mfrac><mrow><mn>6</mn></mrow><mrow><mn>5</mn></mrow></mfrac></mrow></msup></math> then the value of <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msqrt><mn>2</mn><mo>-</mo><mn>42</mn><mi mathvariant=\"normal\">x</mi></msqrt><mn>5</mn></mfrac></math> is:-</p>",
                    question_hi: "<p>1. यदि <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>[</mo><msup><mrow><mrow><mo>{</mo><msup><mrow><mo>(</mo><mfrac><mn>2</mn><mn>3</mn></mfrac><mo>)</mo></mrow><mn>3</mn></msup></mrow><mo>}</mo></mrow><mrow><mo>(</mo><mn>2</mn><mi mathvariant=\"normal\">x</mi><mo>+</mo><mn>3</mn><mo>)</mo></mrow></msup><mo>]</mo></mrow><mfrac><mn>3</mn><mn>4</mn></mfrac></msup><mo>=</mo><mo>[</mo><msup><mrow><mo>{</mo><msup><mrow><mo>(</mo><mfrac><mn>2</mn><mn>3</mn></mfrac><mo>)</mo></mrow><mfrac><mn>2</mn><mn>3</mn></mfrac></msup><mo>}</mo></mrow><mrow><mo>(</mo><mn>3</mn><mi mathvariant=\"normal\">x</mi><mo>+</mo><mn>7</mn><mo>)</mo></mrow></msup><msup><mo>]</mo><mrow><mfrac><mrow><mn>6</mn></mrow><mrow><mn>5</mn></mrow></mfrac></mrow></msup></math> तो <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msqrt><mn>2</mn><mo>-</mo><mn>42</mn><mi mathvariant=\"normal\">x</mi></msqrt><mn>5</mn></mfrac></math> का मान क्या होगा ?</p>",
                    options_en: ["<p>5</p>", "<p>1</p>", 
                                "<p>2</p>", "<p>4</p>"],
                    options_hi: ["<p>5</p>", "<p>1</p>",
                                "<p>2</p>", "<p>4</p>"],
                    solution_en: "<p>1.(b)<br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>[</mo><msup><mrow><mrow><mo>{</mo><msup><mrow><mo>(</mo><mfrac><mn>2</mn><mn>3</mn></mfrac><mo>)</mo></mrow><mn>3</mn></msup></mrow><mo>}</mo></mrow><mrow><mo>(</mo><mn>2</mn><mi mathvariant=\"normal\">x</mi><mo>+</mo><mn>3</mn><mo>)</mo></mrow></msup><mo>]</mo></mrow><mfrac><mn>3</mn><mn>4</mn></mfrac></msup><mo>=</mo><mo>[</mo><msup><mrow><mo>{</mo><msup><mrow><mo>(</mo><mfrac><mn>2</mn><mn>3</mn></mfrac><mo>)</mo></mrow><mfrac><mn>2</mn><mn>3</mn></mfrac></msup><mo>}</mo></mrow><mrow><mo>(</mo><mn>3</mn><mi mathvariant=\"normal\">x</mi><mo>+</mo><mn>7</mn><mo>)</mo></mrow></msup><msup><mo>]</mo><mrow><mfrac><mrow><mn>6</mn></mrow><mrow><mn>5</mn></mrow></mfrac></mrow></msup></math><br><math display=\"inline\"><mo>&#8658;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>[</mo><msup><mrow><mo>{</mo><msup><mrow><mo>(</mo><mfrac><mn>2</mn><mn>3</mn></mfrac><mo>)</mo></mrow><mn>3</mn></msup><mo>}</mo></mrow><mfrac><mrow><mn>3</mn><mo>(</mo><mn>2</mn><mi mathvariant=\"normal\">x</mi><mo>+</mo><mn>3</mn><mo>)</mo></mrow><mn>4</mn></mfrac></msup><mo>]</mo><mo>=</mo><mo>[</mo><msup><mrow><mo>{</mo><msup><mrow><mo>(</mo><mfrac><mn>2</mn><mn>3</mn></mfrac><mo>)</mo></mrow><mfrac><mn>2</mn><mn>3</mn></mfrac></msup><mo>}</mo></mrow><mfrac><mrow><mn>6</mn><mo>(</mo><mn>3</mn><mi mathvariant=\"normal\">x</mi><mo>+</mo><mn>7</mn><mo>)</mo></mrow><mn>5</mn></mfrac></msup><mo>]</mo></math> <br><math display=\"inline\"><mo>&#8658;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>[</mo><mo>{</mo><msup><mrow><msup><mrow><mo>(</mo><mfrac><mn>2</mn><mn>3</mn></mfrac><mo>)</mo></mrow><mn>3</mn></msup><mo>}</mo></mrow><mfrac><mrow><mn>6</mn><mi mathvariant=\"normal\">x</mi><mo>+</mo><mn>9</mn></mrow><mn>4</mn></mfrac></msup><mo>]</mo><mo>=</mo><mo>[</mo><msup><mrow><mrow><mo>{</mo><msup><mrow><mo>(</mo><mfrac><mn>2</mn><mn>3</mn></mfrac><mo>)</mo></mrow><mfrac><mn>2</mn><mn>3</mn></mfrac></msup></mrow><mo>}</mo></mrow><mfrac><mrow><mn>18</mn><mi mathvariant=\"normal\">x</mi><mo>+</mo><mn>42</mn></mrow><mn>5</mn></mfrac></msup><mo>]</mo></math><br><math display=\"inline\"><mo>&#8658;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>[</mo><mrow><mo>{</mo><msup><mrow><mo>(</mo><mfrac><mn>2</mn><mn>3</mn></mfrac><mo>)</mo></mrow><mfrac><mrow><mn>3</mn><mo>(</mo><mn>6</mn><mi mathvariant=\"normal\">x</mi><mo>+</mo><mn>9</mn><mo>)</mo></mrow><mn>4</mn></mfrac></msup></mrow><mo>}</mo><mo>]</mo><mo>=</mo><mo>[</mo><mrow><mo>{</mo><msup><mrow><mo>(</mo><mfrac><mn>2</mn><mn>3</mn></mfrac><mo>)</mo></mrow><mfrac><mrow><mn>2</mn><mo>(</mo><mn>18</mn><mi mathvariant=\"normal\">x</mi><mo>+</mo><mn>42</mn><mo>)</mo></mrow><mrow><mn>3</mn><mo>&#215;</mo><mn>5</mn></mrow></mfrac></msup></mrow><mo>}</mo><mo>]</mo></math> <br><math display=\"inline\"><mo>&#8658;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>[</mo><mo>{</mo><mrow><msup><mrow><mo>(</mo><mfrac><mn>2</mn><mn>3</mn></mfrac><mo>)</mo></mrow><mfrac><mrow><mn>18</mn><mi mathvariant=\"normal\">x</mi><mo>+</mo><mn>27</mn></mrow><mn>4</mn></mfrac></msup><mo>}</mo></mrow><mo>]</mo><mo>=</mo><mo>[</mo><mrow><mo>{</mo><msup><mrow><mo>(</mo><mfrac><mn>2</mn><mn>3</mn></mfrac><mo>)</mo></mrow><mfrac><mrow><mn>36</mn><mi mathvariant=\"normal\">x</mi><mo>+</mo><mn>84</mn></mrow><mn>15</mn></mfrac></msup></mrow><mo>}</mo><mo>]</mo></math><br><math display=\"inline\"><mo>&#8658;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>18</mn><mi mathvariant=\"normal\">x</mi><mo>+</mo><mn>27</mn></mrow><mn>4</mn></mfrac><mo>=</mo><mfrac><mrow><mn>36</mn><mi mathvariant=\"normal\">x</mi><mo>+</mo><mn>84</mn></mrow><mn>15</mn></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math> 15(18x + 27) = 4(36x + 84)<br><math display=\"inline\"><mo>&#8658;</mo><mi>&#160;</mi></math> 270x + 405 = 144x + 336<br><math display=\"inline\"><mo>&#8658;</mo></math> 270x - 144x = 336 - 405<br><math display=\"inline\"><mo>&#8658;</mo></math> 126x = -69<br><math display=\"inline\"><mo>&#8658;</mo></math> x = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>-</mo><mfrac><mn>69</mn><mn>126</mn></mfrac><mo>=</mo><mo>-</mo><mfrac><mn>23</mn><mn>42</mn></mfrac></math><br>Now put the value of x in given equation<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"true\"><mfrac><msqrt><mn>2</mn><mo>-</mo><mn>42</mn><mi mathvariant=\"normal\">x</mi></msqrt><mn>5</mn></mfrac></mstyle><mo>=</mo><mstyle displaystyle=\"true\"><mfrac><msqrt><mn>2</mn><mo>-</mo><mn>42</mn><mo>&#215;</mo><mo>(</mo><mo>-</mo><mstyle displaystyle=\"true\"><mfrac><mn>23</mn><mn>42</mn></mfrac></mstyle><mo>)</mo></msqrt><mn>5</mn></mfrac></mstyle></math><br><math display=\"inline\"><mo>&#8658;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msqrt><mn>2</mn><mo>+</mo><mn>23</mn></msqrt><mn>5</mn></mfrac></math> = 1</p>",
                    solution_hi: "<p>1.(b)<br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>[</mo><msup><mrow><mrow><mo>{</mo><msup><mrow><mo>(</mo><mfrac><mn>2</mn><mn>3</mn></mfrac><mo>)</mo></mrow><mn>3</mn></msup></mrow><mo>}</mo></mrow><mrow><mo>(</mo><mn>2</mn><mi mathvariant=\"normal\">x</mi><mo>+</mo><mn>3</mn><mo>)</mo></mrow></msup><mo>]</mo></mrow><mfrac><mn>3</mn><mn>4</mn></mfrac></msup><mo>=</mo><mo>[</mo><msup><mrow><mo>{</mo><msup><mrow><mo>(</mo><mfrac><mn>2</mn><mn>3</mn></mfrac><mo>)</mo></mrow><mfrac><mn>2</mn><mn>3</mn></mfrac></msup><mo>}</mo></mrow><mrow><mo>(</mo><mn>3</mn><mi mathvariant=\"normal\">x</mi><mo>+</mo><mn>7</mn><mo>)</mo></mrow></msup><msup><mo>]</mo><mrow><mfrac><mrow><mn>6</mn></mrow><mrow><mn>5</mn></mrow></mfrac></mrow></msup></math><br><math display=\"inline\"><mo>&#8658;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>[</mo><msup><mrow><mo>{</mo><msup><mrow><mo>(</mo><mfrac><mn>2</mn><mn>3</mn></mfrac><mo>)</mo></mrow><mn>3</mn></msup><mo>}</mo></mrow><mfrac><mrow><mn>3</mn><mo>(</mo><mn>2</mn><mi mathvariant=\"normal\">x</mi><mo>+</mo><mn>3</mn><mo>)</mo></mrow><mn>4</mn></mfrac></msup><mo>]</mo><mo>=</mo><mo>[</mo><msup><mrow><mo>{</mo><msup><mrow><mo>(</mo><mfrac><mn>2</mn><mn>3</mn></mfrac><mo>)</mo></mrow><mfrac><mn>2</mn><mn>3</mn></mfrac></msup><mo>}</mo></mrow><mfrac><mrow><mn>6</mn><mo>(</mo><mn>3</mn><mi mathvariant=\"normal\">x</mi><mo>+</mo><mn>7</mn><mo>)</mo></mrow><mn>5</mn></mfrac></msup><mo>]</mo></math> <br><math display=\"inline\"><mo>&#8658;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>[</mo><mo>{</mo><msup><mrow><msup><mrow><mo>(</mo><mfrac><mn>2</mn><mn>3</mn></mfrac><mo>)</mo></mrow><mn>3</mn></msup><mo>}</mo></mrow><mfrac><mrow><mn>6</mn><mi mathvariant=\"normal\">x</mi><mo>+</mo><mn>9</mn></mrow><mn>4</mn></mfrac></msup><mo>]</mo><mo>=</mo><mo>[</mo><msup><mrow><mrow><mo>{</mo><msup><mrow><mo>(</mo><mfrac><mn>2</mn><mn>3</mn></mfrac><mo>)</mo></mrow><mfrac><mn>2</mn><mn>3</mn></mfrac></msup></mrow><mo>}</mo></mrow><mfrac><mrow><mn>18</mn><mi mathvariant=\"normal\">x</mi><mo>+</mo><mn>42</mn></mrow><mn>5</mn></mfrac></msup><mo>]</mo></math> <br><math display=\"inline\"><mo>&#8658;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>[</mo><mrow><mo>{</mo><msup><mrow><mo>(</mo><mfrac><mn>2</mn><mn>3</mn></mfrac><mo>)</mo></mrow><mfrac><mrow><mn>3</mn><mo>(</mo><mn>6</mn><mi mathvariant=\"normal\">x</mi><mo>+</mo><mn>9</mn><mo>)</mo></mrow><mn>4</mn></mfrac></msup></mrow><mo>}</mo><mo>]</mo><mo>=</mo><mo>[</mo><mrow><mo>{</mo><msup><mrow><mo>(</mo><mfrac><mn>2</mn><mn>3</mn></mfrac><mo>)</mo></mrow><mfrac><mrow><mn>2</mn><mo>(</mo><mn>18</mn><mi mathvariant=\"normal\">x</mi><mo>+</mo><mn>42</mn><mo>)</mo></mrow><mrow><mn>3</mn><mo>&#215;</mo><mn>5</mn></mrow></mfrac></msup></mrow><mo>}</mo><mo>]</mo></math> <br><math display=\"inline\"><mo>&#8658;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>[</mo><mo>{</mo><mrow><msup><mrow><mo>(</mo><mfrac><mn>2</mn><mn>3</mn></mfrac><mo>)</mo></mrow><mfrac><mrow><mn>18</mn><mi mathvariant=\"normal\">x</mi><mo>+</mo><mn>27</mn></mrow><mn>4</mn></mfrac></msup><mo>}</mo></mrow><mo>]</mo><mo>=</mo><mo>[</mo><mrow><mo>{</mo><msup><mrow><mo>(</mo><mfrac><mn>2</mn><mn>3</mn></mfrac><mo>)</mo></mrow><mfrac><mrow><mn>36</mn><mi mathvariant=\"normal\">x</mi><mo>+</mo><mn>84</mn></mrow><mn>15</mn></mfrac></msup></mrow><mo>}</mo><mo>]</mo></math><br><math display=\"inline\"><mo>&#8658;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>18</mn><mi mathvariant=\"normal\">x</mi><mo>+</mo><mn>27</mn></mrow><mn>4</mn></mfrac><mo>=</mo><mfrac><mrow><mn>36</mn><mi mathvariant=\"normal\">x</mi><mo>+</mo><mn>84</mn></mrow><mn>15</mn></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math> 15(18x + 27) = 4(36x + 84)<br><math display=\"inline\"><mo>&#8658;</mo><mi>&#160;</mi></math> 270x + 405 = 144x + 336<br><math display=\"inline\"><mo>&#8658;</mo></math> 270x - 144x = 336 - 405<br><math display=\"inline\"><mo>&#8658;</mo></math> 126x = -69<br><math display=\"inline\"><mo>&#8658;</mo></math> x = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>-</mo><mfrac><mn>69</mn><mn>126</mn></mfrac><mo>=</mo><mo>-</mo><mfrac><mn>23</mn><mn>42</mn></mfrac></math><br>अब x का मान दिए गए समीकरण में रखें,<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"true\"><mfrac><msqrt><mn>2</mn><mo>-</mo><mn>42</mn><mi mathvariant=\"normal\">x</mi></msqrt><mn>5</mn></mfrac></mstyle><mo>=</mo><mstyle displaystyle=\"true\"><mfrac><msqrt><mn>2</mn><mo>-</mo><mn>42</mn><mo>&#215;</mo><mo>(</mo><mo>-</mo><mstyle displaystyle=\"true\"><mfrac><mn>23</mn><mn>42</mn></mfrac></mstyle><mo>)</mo></msqrt><mn>5</mn></mfrac></mstyle></math><br><math display=\"inline\"><mo>&#8658;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msqrt><mn>2</mn><mo>+</mo><mn>23</mn></msqrt><mn>5</mn></mfrac></math> = 1</p>",
                    correct: "b",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "2",
                    section: "misc",
                    question_en: "<p>2. If <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><mi>cos</mi></mrow><mn>6</mn></msup><mi mathvariant=\"normal\">&#952;</mi><mo>+</mo><msup><mi>sin</mi><mn>6</mn></msup><mi mathvariant=\"normal\">&#952;</mi><mo>-</mo><mn>1</mn><mo>)</mo></math>(tan&sup2;&theta; + cot&sup2;&theta; + 2) + 4(sec&theta; + tan&theta;)(sec&theta; - tan&theta;)cos&theta; = 4k - 3 and&nbsp; 0&deg; &lt; <math display=\"inline\"><mi>&#952;</mi></math> &lt; 90&deg;, then k = ?</p>",
                    question_hi: "<p>2. यदि <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><mi>cos</mi></mrow><mn>6</mn></msup><mi mathvariant=\"normal\">&#952;</mi><mo>+</mo><msup><mi>sin</mi><mn>6</mn></msup><mi mathvariant=\"normal\">&#952;</mi><mo>-</mo><mn>1</mn><mo>)</mo></math>(tan&sup2;&theta; + cot&sup2;&theta; + 2) + 4(sec&theta; + tan&theta;)(sec&theta; - tan&theta;)cos&theta; = 4k - 3,&nbsp; और 0&deg; &lt; <math display=\"inline\"><mi>&#952;</mi></math> &lt; 90&deg;, तब k = ?</p>",
                    options_en: ["<p>sin<math display=\"inline\"><mi>&#952;</mi></math></p>", "<p>cos<math display=\"inline\"><mi>&#952;</mi></math></p>", 
                                "<p>sec<math display=\"inline\"><mi>&#952;</mi></math></p>", "<p>cosec<math display=\"inline\"><mi>&#952;</mi></math></p>"],
                    options_hi: ["<p>sin<math display=\"inline\"><mi>&#952;</mi></math></p>", "<p>cos<math display=\"inline\"><mi>&#952;</mi></math></p>",
                                "<p>sec<math display=\"inline\"><mi>&#952;</mi></math></p>", "<p>cosec<math display=\"inline\"><mi>&#952;</mi></math></p>"],
                    solution_en: "<p>2.(b) <strong>As we know the formula :-</strong><br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>cos</mi><mn>6</mn></msup><mi mathvariant=\"normal\">&#952;</mi><mo>+</mo><msup><mi>sin</mi><mn>6</mn></msup><mi mathvariant=\"normal\">&#952;</mi></math> = 1 - 3sin&sup2;&theta; &times; cos&sup2;&theta; ,<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>sec</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&#952;</mi><mo>+</mo><msup><mi>cosec</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&#952;</mi></math> = sec&sup2;&theta; &times; cosec&sup2;&theta;<br>Now,<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><mi>cos</mi></mrow><mn>6</mn></msup><mi mathvariant=\"normal\">&#952;</mi><mo>+</mo><msup><mi>sin</mi><mn>6</mn></msup><mi mathvariant=\"normal\">&#952;</mi><mo>-</mo><mn>1</mn><mo>)</mo></math>(tan&sup2;&theta; + cot&sup2;&theta; + 2) + 4(sec&theta; + tan&theta;)(sec&theta; - tan&theta;)cos&theta; = 4k - 3&nbsp;<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfenced separators=\"|\"><mrow><mo>-</mo><mn>3</mn><msup><mi>sin</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&#952;</mi><msup><mrow><mo>&#215;</mo><mi>cos</mi></mrow><mn>2</mn></msup><mi mathvariant=\"normal\">&#952;</mi></mrow></mfenced></math>(sec&sup2;&theta; + cosec&sup2;&theta;) + 4(sec&sup2;&theta; - tan&sup2;&theta;)cos&theta; = 4k - 3&nbsp;<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfenced separators=\"|\"><mrow><mo>-</mo><mn>3</mn><msup><mi>sin</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&#952;</mi><msup><mrow><mo>&#215;</mo><mi>cos</mi></mrow><mn>2</mn></msup><mi mathvariant=\"normal\">&#952;</mi></mrow></mfenced></math>(sec&sup2;&theta; + cosec&sup2;&theta;) + 4cos&theta; = 4k - 3&nbsp;<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfenced separators=\"|\"><mrow><mo>-</mo><mn>3</mn><msup><mi>sin</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&#952;</mi><msup><mrow><mo>&#215;</mo><mi>cos</mi></mrow><mn>2</mn></msup><mi mathvariant=\"normal\">&#952;</mi></mrow></mfenced></math>(sec&sup2;&theta; &times; cosec&sup2;&theta;) + 4cos&theta; = 4k - 3&nbsp;<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mfenced separators=\"|\"><mrow><mo>-</mo><mn>3</mn><msup><mi>sin</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&#952;</mi><msup><mrow><mo>&#215;</mo><mi>cos</mi></mrow><mn>2</mn></msup><mi mathvariant=\"normal\">&#952;</mi></mrow></mfenced><mfenced separators=\"|\"><mrow><msup><mi>sin</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&#952;</mi><msup><mrow><mo>&#215;</mo><mi>cos</mi></mrow><mn>2</mn></msup><mi mathvariant=\"normal\">&#952;</mi></mrow></mfenced></mfrac></math> + 4cos&theta; = 4cos&theta; - 3 = 4k - 3<br>By comparing we can see that, k = cos<math display=\"inline\"><mi>&#952;</mi></math></p>",
                    solution_hi: "<p>2.(b)&nbsp;<strong>जैसा कि हम सूत्र जानते हैं:-</strong><br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>cos</mi><mn>6</mn></msup><mi mathvariant=\"normal\">&#952;</mi><mo>+</mo><msup><mi>sin</mi><mn>6</mn></msup><mi mathvariant=\"normal\">&#952;</mi></math> = 1 - 3sin&sup2;&theta; &times; cos&sup2;&theta; ,<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>sec</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&#952;</mi><mo>+</mo><msup><mi>cosec</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&#952;</mi></math> = sec&sup2;&theta; &times; cosec&sup2;&theta;<br>अब,<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><mi>cos</mi></mrow><mn>6</mn></msup><mi mathvariant=\"normal\">&#952;</mi><mo>+</mo><msup><mi>sin</mi><mn>6</mn></msup><mi mathvariant=\"normal\">&#952;</mi><mo>-</mo><mn>1</mn><mo>)</mo></math>(tan&sup2;&theta; + cot&sup2;&theta; + 2) + 4(sec&theta; + tan&theta;)(sec&theta; - tan&theta;)cos&theta; = 4k - 3&nbsp;<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfenced separators=\"|\"><mrow><mo>-</mo><mn>3</mn><msup><mi>sin</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&#952;</mi><msup><mrow><mo>&#215;</mo><mi>cos</mi></mrow><mn>2</mn></msup><mi mathvariant=\"normal\">&#952;</mi></mrow></mfenced></math>(sec&sup2;&theta; + cosec&sup2;&theta;) + 4(sec&sup2;&theta; - tan&sup2;&theta;)cos&theta; = 4k - 3&nbsp;<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfenced separators=\"|\"><mrow><mo>-</mo><mn>3</mn><msup><mi>sin</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&#952;</mi><msup><mrow><mo>&#215;</mo><mi>cos</mi></mrow><mn>2</mn></msup><mi mathvariant=\"normal\">&#952;</mi></mrow></mfenced></math>(sec&sup2;&theta; + cosec&sup2;&theta;) + 4cos&theta; = 4k - 3&nbsp;<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfenced separators=\"|\"><mrow><mo>-</mo><mn>3</mn><msup><mi>sin</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&#952;</mi><msup><mrow><mo>&#215;</mo><mi>cos</mi></mrow><mn>2</mn></msup><mi mathvariant=\"normal\">&#952;</mi></mrow></mfenced></math>(sec&sup2;&theta; &times; cosec&sup2;&theta;) + 4cos&theta; = 4k - 3&nbsp;<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mfenced separators=\"|\"><mrow><mo>-</mo><mn>3</mn><msup><mi>sin</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&#952;</mi><msup><mrow><mo>&#215;</mo><mi>cos</mi></mrow><mn>2</mn></msup><mi mathvariant=\"normal\">&#952;</mi></mrow></mfenced><mfenced separators=\"|\"><mrow><msup><mi>sin</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&#952;</mi><msup><mrow><mo>&#215;</mo><mi>cos</mi></mrow><mn>2</mn></msup><mi mathvariant=\"normal\">&#952;</mi></mrow></mfenced></mfrac></math> + 4cos&theta; = 4cos&theta; - 3 = 4k - 3<br>तुलना करके हम देख सकते हैं कि,<br>k = cos<math display=\"inline\"><mi>&#952;</mi></math></p>",
                    correct: "b",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "3",
                    section: "misc",
                    question_en: "<p>3. A train without stoppage travels with an average speed of 70 km/h, and with stoppage, it travels with an average speed of 56 km/h. How many minutes does the train stop on an average per hour?</p>",
                    question_hi: "<p>3.बिना रुके कोई ट्रेन 70 किमी/घंटा की औसत चाल से चलती है तथा रुक-रुक कर, यह 56 किमी/घंटा की औसत चाल से चलती है | प्रति घंटे यह ट्रेन औसतन कितने मिनट रूकती है ?</p>",
                    options_en: ["<p>12</p>", "<p>14</p>", 
                                "<p>16</p>", "<p>15</p>"],
                    options_hi: ["<p>12</p>", "<p>14</p>",
                                "<p>16</p>", "<p>15</p>"],
                    solution_en: "<p>3.(a)&nbsp;<strong>Tricky approach :</strong><br>t = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi mathvariant=\"normal\">f</mi><mo>-</mo><mi mathvariant=\"normal\">s</mi></mrow><mi mathvariant=\"normal\">f</mi></mfrac></math>hours<br>Here, t = time of stoppage<br>f = speed without stoppage<br>s = speed with stoppage<br>t = <math display=\"inline\"><mfrac><mrow><mn>70</mn><mo>-</mo><mn>56</mn></mrow><mrow><mn>70</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>5</mn></mfrac></math>hour = 12 minutes</p>",
                    solution_hi: "<p>3.(a) <strong>सरल तरीका:</strong><br>t = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi mathvariant=\"normal\">f</mi><mo>-</mo><mi mathvariant=\"normal\">s</mi></mrow><mi mathvariant=\"normal\">f</mi></mfrac></math> घंटे <br>यहाँ, t = रुकने का समय<br>f = बिना रुके गति<br>s = ठहराव के साथ गति<br>t = <math display=\"inline\"><mfrac><mrow><mn>70</mn><mo>-</mo><mn>56</mn></mrow><mrow><mn>70</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>5</mn></mfrac></math> घंटा = 12 मिनट</p>",
                    correct: "a",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "4",
                    section: "misc",
                    question_en: "<p>4. In finding the HCF of two numbers by division method, the last divisor is 17 and the quotients are 1, 11 and 2, respectively. What is the sum of the two numbers ?</p>",
                    question_hi: "<p>4. भाग विधि द्वारा दो संख्याओं का HCF ज्ञात करने पर , अंतिम भाजक 17 प्राप्त होता है और भागफल क्रमशः 1, 11 और 2 प्राप्त होते हैं | दोनों संख्याओं का योग क्या है ?</p>",
                    options_en: ["<p>833</p>", "<p>867</p>", 
                                "<p>816</p>", "<p>901</p>"],
                    options_hi: ["<p>833</p>", "<p>867</p>",
                                "<p>816</p>", "<p>901</p>"],
                    solution_en: "<p>4.(c)&nbsp;Since the last divisor is 17 and the quotient is 2, we have the dividend = 17 &times; 2 = 34.(Because the last divisor will not leave any remainder)<br>Now, 34 will become the divisor and 11 will become the quotient and 17 will be the remainder. Therefore the dividend will be 34 &times; 11 + 17 = 391<br>Now, 391 will become the divisor and 1 will be the quotient and 34 will be the remainder. Therefore the dividend will be 391 &times; 1 + 34 = 425<br>So, the numbers are 391 and 425<br>Required sum = 391 + 425 = 816</p>",
                    solution_hi: "<p>4.(c)&nbsp;चूंकि अंतिम भाजक 17 है और भागफल 2 है, इसलिए हमारे पास भाज्य = 17 &times; 2 = 34 है। (क्योंकि अंतिम भाजक कोई शेष नहीं छोड़ेगा)<br>अब, 34 भाजक बन जाएगा और 11 भागफल बन जाएगा और 17 शेषफल होगा। इसलिए भाज्य 34 &times; 11 + 17 = 391 होगा ।<br>अब, 391 भाजक बन जाएगा और 1 भागफल होगा और 34 शेषफल होगा। अतः भाज्य 391 &times; 1 + 34 = 425 होगा<br>तो, संख्याएँ 391 और 425 . हैं |<br>अभीष्ट योग = 391 + 425 = 816</p>",
                    correct: "c",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "5",
                    section: "misc",
                    question_en: "<p>5. The total surface area of a solid hemisphere is 16632 <math display=\"inline\"><msup><mrow><mi>c</mi><mi>m</mi></mrow><mrow><mn>2</mn></mrow></msup></math> Its volume is: (Take &pi; = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>22</mn><mn>7</mn></mfrac></math>)</p>",
                    question_hi: "<p>5. एक ठोस अर्धगोले का कुल पृष्ठीय क्षेत्रफल 16632 <math display=\"inline\"><msup><mrow><mi>c</mi><mi>m</mi></mrow><mrow><mn>2</mn></mrow></msup></math> है, तो इसका आयतन ज्ञात कीजिए। ( &pi; = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>22</mn><mn>7</mn></mfrac></math> लें)</p>",
                    options_en: ["<p>145232 <math display=\"inline\"><msup><mrow><mi>c</mi><mi>m</mi></mrow><mrow><mn>3</mn></mrow></msup></math></p>", "<p>140232 <math display=\"inline\"><msup><mrow><mi>c</mi><mi>m</mi></mrow><mrow><mn>3</mn></mrow></msup></math></p>", 
                                "<p>150032 <math display=\"inline\"><msup><mrow><mi>c</mi><mi>m</mi></mrow><mrow><mn>3</mn></mrow></msup></math></p>", "<p>155232 <math display=\"inline\"><msup><mrow><mi>c</mi><mi>m</mi></mrow><mrow><mn>3</mn></mrow></msup></math></p>"],
                    options_hi: ["<p>145232 <math display=\"inline\"><msup><mrow><mi>c</mi><mi>m</mi></mrow><mrow><mn>3</mn></mrow></msup></math></p>", "<p>140232 <math display=\"inline\"><msup><mrow><mi>c</mi><mi>m</mi></mrow><mrow><mn>3</mn></mrow></msup></math></p>",
                                "<p>150032 <math display=\"inline\"><msup><mrow><mi>c</mi><mi>m</mi></mrow><mrow><mn>3</mn></mrow></msup></math></p>", "<p>155232 <math display=\"inline\"><msup><mrow><mi>c</mi><mi>m</mi></mrow><mrow><mn>3</mn></mrow></msup></math></p>"],
                    solution_en: "<p>5.(d)&nbsp;Total surface area of a solid hemisphere = 16632 <math display=\"inline\"><msup><mrow><mi>c</mi><mi>m</mi></mrow><mrow><mn>2</mn></mrow></msup></math><br>3<math display=\"inline\"><mi>&#960;</mi></math>r&sup2; = 16632 <br>3<math display=\"inline\"><mo>&#215;</mo><mfrac><mrow><mn>22</mn></mrow><mrow><mn>7</mn></mrow></mfrac><mo>&#215;</mo><msup><mrow><mi>r</mi></mrow><mrow><mn>2</mn></mrow></msup></math> = 16632<br>r&sup2; = 1764<br>r = 42<br>Volume of hemisphere = <math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac><mo>&#215;</mo><mfrac><mrow><mn>22</mn></mrow><mrow><mn>7</mn></mrow></mfrac><mo>&#215;</mo><mn>42</mn><mo>&#215;</mo><mn>42</mn><mo>&#215;</mo><mn>42</mn></math> = 155232 <math display=\"inline\"><msup><mrow><mi>c</mi><mi>m</mi></mrow><mrow><mn>3</mn></mrow></msup></math></p>",
                    solution_hi: "<p>5.(d)&nbsp;एक ठोस अर्धगोले का कुल पृष्ठीय क्षेत्रफल = 16632 <math display=\"inline\"><msup><mrow><mi>c</mi><mi>m</mi></mrow><mrow><mn>2</mn></mrow></msup></math><br>3<math display=\"inline\"><mi>&#960;</mi></math>r&sup2; = 16632 <br>3<math display=\"inline\"><mo>&#215;</mo><mfrac><mrow><mn>22</mn></mrow><mrow><mn>7</mn></mrow></mfrac><mo>&#215;</mo><msup><mrow><mi>r</mi></mrow><mrow><mn>2</mn></mrow></msup></math> = 16632<br>r&sup2; = 1764<br>r = 42<br>अर्धगोले का आयतन = <math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac><mo>&#215;</mo><mfrac><mrow><mn>22</mn></mrow><mrow><mn>7</mn></mrow></mfrac><mo>&#215;</mo><mn>42</mn><mo>&#215;</mo><mn>42</mn><mo>&#215;</mo><mn>42</mn></math> = 155232 <math display=\"inline\"><msup><mrow><mi>c</mi><mi>m</mi></mrow><mrow><mn>3</mn></mrow></msup></math></p>",
                    correct: "d",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "6",
                    section: "misc",
                    question_en: "<p>6. Study the given pie chart and answer the question that follows.<br>The pie chart shows the distribution (degree-wise) of the students who appeared in the annual examination from institutes P, Q, R, S and T in 2020. The total number of students who appeared is 3000.<br><strong id=\"docs-internal-guid-0f80330f-7fff-e933-f8fd-03872e6fb5b5\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfVqfkw5fk-hmVP0AziNjNJiuF1K_bEAlCFgRlkqVp9-CrlMUEopPCqB7dQ9UKowCM8R0YtSrjU8nCuw59QOUftEAlKPEenBnFNGE18lvKT4uhZILEkkzFrjs8q_M3T7XUVXU_SNHRCYW-9R08uuaXRQJo?key=7Eq3yv6nd6p1_hMEgz8-iw\" width=\"180\" height=\"178\"></strong><br>The ratio of the numbers of boys and girls who appeared in the examination from institutes S and T are 11 : 12 and 5 : 4, respectively. What is the difference between the total number of boys who appeared from institutes S and T and the total number of girls who appeared from these two institutes?</p>",
                    question_hi: "<p>6. दिए गए पाई चार्ट का अध्ययन करें और नीचे दिए गए प्रश्न का उत्तर दें।<br>पाई चार्ट 2020 में संस्थान P, Q, R, S और T से वार्षिक परीक्षा में उपस्थित होने वाले छात्रों के वितरण (डिग्री अनुसार) को दर्शाता है। उपस्थित होने वाले छात्रों की कुल संख्या 3000 है। <br><strong id=\"docs-internal-guid-0f80330f-7fff-e933-f8fd-03872e6fb5b5\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfVqfkw5fk-hmVP0AziNjNJiuF1K_bEAlCFgRlkqVp9-CrlMUEopPCqB7dQ9UKowCM8R0YtSrjU8nCuw59QOUftEAlKPEenBnFNGE18lvKT4uhZILEkkzFrjs8q_M3T7XUVXU_SNHRCYW-9R08uuaXRQJo?key=7Eq3yv6nd6p1_hMEgz8-iw\" width=\"180\" height=\"178\"></strong><br>संस्थान S और T से परीक्षा में उपस्थित लड़कों और लड़कियों की संख्या का अनुपात क्रमशः 11 : 12 और 5 : 4 है। संस्थान S और T से उपस्थित लड़कों की कुल संख्या और इन दोनों संस्थानों से उपस्थित होने वाली लड़कियों की कुल संख्या के बीच का अंतर कितना है?</p>",
                    options_en: ["<p>20</p>", "<p>23</p>", 
                                "<p>17</p>", "<p>24</p>"],
                    options_hi: ["<p>20</p>", "<p>23</p>",
                                "<p>17</p>", "<p>24</p>"],
                    solution_en: "<p>6.(a)&nbsp;For institute S :<br>Number of boys (in degree) = <math display=\"inline\"><mfrac><mrow><mn>11</mn></mrow><mrow><mn>23</mn></mrow></mfrac></math> &times; 82.8 = 39.6&deg; <br>Number of girls (in degree) = <math display=\"inline\"><mfrac><mrow><mn>12</mn></mrow><mrow><mn>23</mn></mrow></mfrac></math> &times; 82.8 = 43.2&deg; <br>For institute T :<br>Number of boys (in degree) = <math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>9</mn></mrow></mfrac></math> &times; 54 = 30&deg; <br>Number of girls (in degree) = <math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>9</mn></mrow></mfrac></math> &times; 54 = 24&deg; <br>Total boys in S and T (in degree) = 39.6&deg; + 30&deg; = 69.6&deg;<br>Total girls in S and T (in degree) = 43.2&deg; + 24&deg; = 67.2&deg;<br>Required difference (in degree) = 69.6&deg; &ndash; 67.2&deg; = 2.4&deg;<br>360&deg; corresponds to 3000, so required difference = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><mo>.</mo><mn>4</mn><mo>&#215;</mo><mn>3000</mn><mi>&#160;</mi></mrow><mn>360</mn></mfrac></math> = 20</p>",
                    solution_hi: "<p>6.(a)&nbsp;संस्थान S के लिए :<br>लड़कों की संख्या (डिग्री में) = <math display=\"inline\"><mfrac><mrow><mn>11</mn></mrow><mrow><mn>23</mn></mrow></mfrac></math> &times; 82.8 = 39.6&deg; <br>लड़कियों की संख्या (डिग्री में) = <math display=\"inline\"><mfrac><mrow><mn>12</mn></mrow><mrow><mn>23</mn></mrow></mfrac></math> &times; 82.8 = 43.2&deg; <br>संस्थान T के लिए:<br>लड़कों की संख्या (डिग्री में) = <math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>9</mn></mrow></mfrac></math> &times; 54 = 30&deg; <br>लड़कियों की संख्या (डिग्री में) = <math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>9</mn></mrow></mfrac></math> &times; 54 = 24&deg; <br>S और T में कुल लड़के (डिग्री में) = 39.6&deg; + 30&deg; = 69.6&deg;<br>S और T में कुल लड़कियां (डिग्री में) = 43.2&deg; + 24&deg; = 67.2&deg;<br>अभीष्ट अंतर (डिग्री में) = 69.6&deg; &ndash; 67.2&deg; = 2.4&deg;<br>360&deg;, 3000 के बराबर है, इसलिए अभीष्ट अंतर = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><mo>.</mo><mn>4</mn><mo>&#215;</mo><mn>3000</mn><mi>&#160;</mi></mrow><mn>360</mn></mfrac></math> = 20</p>",
                    correct: "a",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "7",
                    section: "misc",
                    question_en: "<p>7. The price of an article is raised by 45% and then two successive discounts of 15% each are allowed. Ultimately the price of the article is _______.</p>",
                    question_hi: "<p>7. एक वस्तु के मूल्य में 45% की वृद्धि की जाती है और फिर प्रत्येक 15% की दो क्रमिक छूट दी जाती है। अंततः वस्तु के मूल्य में कितने प्रतिशत की वृद्धि/कमी होगी ?</p>",
                    options_en: ["<p>decreased by 7.7625%</p>", "<p>increased by 4.7625%</p>", 
                                "<p>decreased by 4.7625%</p>", "<p>increased by 7.7625%</p>"],
                    options_hi: ["<p>7.7625% की कमी</p>", "<p>4.7625% की वृद्धि</p>",
                                "<p>4.7625% की कमी</p>", "<p>7.7625% की वृद्धि</p>"],
                    solution_en: "<p>7.(b)<br>45% increase = 20 : 29<br>15% discount = 20 : 17<br>15% discount = 20 : 17<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;_________<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;8000 : 8381<br>Percentage increase = <math display=\"inline\"><mfrac><mrow><mn>381</mn></mrow><mrow><mn>8000</mn></mrow></mfrac><mo>&#215;</mo><mn>100</mn></math> = 4.7625%</p>",
                    solution_hi: "<p>7.(b)<br>45% वृद्धि = 20 : 29<br>15% छूट&nbsp; = 20 : 17<br>15% छूट&nbsp; = 20 : 17<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; _________<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 8000 : 8381<br>प्रतिशत वृद्धि = <math display=\"inline\"><mfrac><mrow><mn>381</mn></mrow><mrow><mn>8000</mn></mrow></mfrac><mo>&#215;</mo><mn>100</mn></math> = 4.7625%</p>",
                    correct: "b",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "8",
                    section: "misc",
                    question_en: "<p>8. Find the value of the given expression:- <br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><mn>4</mn><mo>-</mo><msqrt><mn>15</mn></msqrt></mrow></mfrac><mo>-</mo><mfrac><mn>1</mn><mrow><msqrt><mn>15</mn></msqrt><mo>-</mo><msqrt><mn>14</mn></msqrt></mrow></mfrac><mo>+</mo><mfrac><mn>1</mn><mrow><msqrt><mn>14</mn></msqrt><mo>-</mo><msqrt><mn>13</mn></msqrt></mrow></mfrac><mo>-</mo><mfrac><mn>1</mn><mrow><msqrt><mn>13</mn></msqrt><mo>-</mo><msqrt><mn>12</mn></msqrt></mrow></mfrac><mo>+</mo><mfrac><mn>1</mn><mrow><mn>4</mn><mo>-</mo><msqrt><mn>15</mn></msqrt></mrow></mfrac><mo>+</mo><mfrac><mn>1</mn><mrow><msqrt><mn>12</mn></msqrt><mo>-</mo><msqrt><mn>11</mn></msqrt></mrow></mfrac><mo>-</mo><mfrac><mn>1</mn><mrow><msqrt><mn>11</mn></msqrt><mo>-</mo><msqrt><mn>10</mn></msqrt></mrow></mfrac><mo>+</mo><mfrac><mn>1</mn><mrow><msqrt><mn>10</mn></msqrt><mo>-</mo><mn>3</mn></mrow></mfrac><mo>-</mo><mfrac><mn>1</mn><mrow><mn>3</mn><mo>-</mo><msqrt><mn>8</mn></msqrt></mrow></mfrac><mo>&#160;</mo><mo>&#160;</mo></math></p>",
                    question_hi: "<p>8. दिए गए व्यंजक का मान ज्ञात कीजिए:-<br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><mn>4</mn><mo>-</mo><msqrt><mn>15</mn></msqrt></mrow></mfrac><mo>-</mo><mfrac><mn>1</mn><mrow><msqrt><mn>15</mn></msqrt><mo>-</mo><msqrt><mn>14</mn></msqrt></mrow></mfrac><mo>+</mo><mfrac><mn>1</mn><mrow><msqrt><mn>14</mn></msqrt><mo>-</mo><msqrt><mn>13</mn></msqrt></mrow></mfrac><mo>-</mo><mfrac><mn>1</mn><mrow><msqrt><mn>13</mn></msqrt><mo>-</mo><msqrt><mn>12</mn></msqrt></mrow></mfrac><mo>+</mo><mfrac><mn>1</mn><mrow><mn>4</mn><mo>-</mo><msqrt><mn>15</mn></msqrt></mrow></mfrac><mo>+</mo><mfrac><mn>1</mn><mrow><msqrt><mn>12</mn></msqrt><mo>-</mo><msqrt><mn>11</mn></msqrt></mrow></mfrac><mo>-</mo><mfrac><mn>1</mn><mrow><msqrt><mn>11</mn></msqrt><mo>-</mo><msqrt><mn>10</mn></msqrt></mrow></mfrac><mo>+</mo><mfrac><mn>1</mn><mrow><msqrt><mn>10</mn></msqrt><mo>-</mo><mn>3</mn></mrow></mfrac><mo>-</mo><mfrac><mn>1</mn><mrow><mn>3</mn><mo>-</mo><msqrt><mn>8</mn></msqrt></mrow></mfrac><mo>&#160;</mo><mo>&#160;</mo></math></p>",
                    options_en: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#160;</mo><mn>8</mn><mo>-</mo><mn>2</mn><msqrt><mn>2</mn></msqrt><mo>+</mo><msqrt><mn>15</mn></msqrt></math></p>", "<p>2 + 2<math display=\"inline\"><msqrt><mn>2</mn></msqrt></math></p>", 
                                "<p>4 - 2<math display=\"inline\"><msqrt><mn>2</mn></msqrt></math></p>", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>2</mn><mo>-</mo><mn>2</mn><msqrt><mn>2</mn></msqrt><mo>+</mo><msqrt><mn>15</mn></msqrt></math></p>"],
                    options_hi: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>8</mn><mo>-</mo><mn>2</mn><msqrt><mn>2</mn></msqrt><mo>+</mo><msqrt><mn>15</mn></msqrt></math></p>", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>2</mn><mo>+</mo><mn>2</mn><msqrt><mn>2</mn></msqrt></math></p>",
                                "<p>4 - 2<math display=\"inline\"><msqrt><mn>2</mn></msqrt></math></p>", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>2</mn><mo>-</mo><mn>2</mn><msqrt><mn>2</mn></msqrt><mo>+</mo><msqrt><mn>15</mn></msqrt></math></p>"],
                    solution_en: "<p>8.(a) <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><mn>4</mn><mo>-</mo><msqrt><mn>15</mn></msqrt></mrow></mfrac><mo>=</mo><mfrac><mrow><mo>(</mo><mn>4</mn><mo>+</mo><msqrt><mn>15</mn></msqrt><mo>)</mo></mrow><mrow><mo>(</mo><mn>4</mn><mo>-</mo><msqrt><mn>15</mn></msqrt><mo>)</mo><mo>&#160;</mo><mo>(</mo><mn>4</mn><mo>+</mo><msqrt><mn>15</mn></msqrt><mo>)</mo></mrow></mfrac></math> <br>= 4 + <math display=\"inline\"><msqrt><mn>15</mn></msqrt></math><br>Similarly, we get conjugate of all values,<br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>4</mn><mo>+</mo><msqrt><mn>15</mn></msqrt><mo>-</mo><msqrt><mn>15</mn></msqrt><mo>-</mo><msqrt><mn>14</mn></msqrt><mo>+</mo><msqrt><mn>14</mn></msqrt><mo>+</mo><msqrt><mn>13</mn></msqrt><mo>-</mo><msqrt><mn>13</mn></msqrt><mo>-</mo><msqrt><mn>12</mn></msqrt><mo>+</mo><mn>4</mn><mo>+</mo><msqrt><mn>15</mn></msqrt><mo>+</mo><msqrt><mn>12</mn></msqrt><mo>+</mo><msqrt><mn>11</mn></msqrt><mo>-</mo><msqrt><mn>11</mn></msqrt><mo>-</mo><msqrt><mn>10</mn></msqrt><mo>+</mo><msqrt><mn>10</mn></msqrt><mo>+</mo><mn>3</mn><mo>-</mo><mn>3</mn><mo>-</mo><msqrt><mn>8</mn></msqrt></math><br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>4</mn><mo>-</mo><msqrt><mn>8</mn></msqrt><mo>+</mo><mn>4</mn><mo>+</mo><msqrt><mn>15</mn></msqrt><mo>=</mo><mn>8</mn><mo>-</mo><mn>2</mn><msqrt><mn>2</mn></msqrt><mo>+</mo><msqrt><mn>15</mn></msqrt></math></p>",
                    solution_hi: "<p>8.(a) <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><mn>4</mn><mo>-</mo><msqrt><mn>15</mn></msqrt></mrow></mfrac><mo>=</mo><mfrac><mrow><mo>(</mo><mn>4</mn><mo>+</mo><msqrt><mn>15</mn></msqrt><mo>)</mo></mrow><mrow><mo>(</mo><mn>4</mn><mo>-</mo><msqrt><mn>15</mn></msqrt><mo>)</mo><mo>&#160;</mo><mo>(</mo><mn>4</mn><mo>+</mo><msqrt><mn>15</mn></msqrt><mo>)</mo></mrow></mfrac></math><br>= 4 + <math display=\"inline\"><msqrt><mn>15</mn></msqrt></math><br>इसी प्रकार, हम सभी मूल्यों के संयुग्मी प्राप्त करते हैं,<br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>4</mn><mo>+</mo><msqrt><mn>15</mn></msqrt><mo>-</mo><msqrt><mn>15</mn></msqrt><mo>-</mo><msqrt><mn>14</mn></msqrt><mo>+</mo><msqrt><mn>14</mn></msqrt><mo>+</mo><msqrt><mn>13</mn></msqrt><mo>-</mo><msqrt><mn>13</mn></msqrt><mo>-</mo><msqrt><mn>12</mn></msqrt><mo>+</mo><mn>4</mn><mo>+</mo><msqrt><mn>15</mn></msqrt><mo>+</mo><msqrt><mn>12</mn></msqrt><mo>+</mo><msqrt><mn>11</mn></msqrt><mo>-</mo><msqrt><mn>11</mn></msqrt><mo>-</mo><msqrt><mn>10</mn></msqrt><mo>+</mo><msqrt><mn>10</mn></msqrt><mo>+</mo><mn>3</mn><mo>-</mo><mn>3</mn><mo>-</mo><msqrt><mn>8</mn></msqrt></math><br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>4</mn><mo>-</mo><msqrt><mn>8</mn></msqrt><mo>+</mo><mn>4</mn><mo>+</mo><msqrt><mn>15</mn></msqrt><mo>=</mo><mn>8</mn><mo>-</mo><mn>2</mn><msqrt><mn>2</mn></msqrt><mo>+</mo><msqrt><mn>15</mn></msqrt></math></p>",
                    correct: "a",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "9",
                    section: "misc",
                    question_en: "<p>9. Find the approximate value of area of trapezium if sum and difference of parallel side is 24cm and 8 cm and unequal sides are 5cm and 9cm.</p>",
                    question_hi: "<p>9. यदि समांतर भुजाओं का योग और अंतर 24 सेमी और 8 सेमी है और असमान भुजाएं 5 सेमी और 9 सेमी हैं, तो समलम्ब के क्षेत्रफल का अनुमानित मान ज्ञात कीजिए।</p>",
                    options_en: ["<p>60</p>", "<p>42.55</p>", 
                                "<p>80</p>", "<p>55.5</p>"],
                    options_hi: ["<p>60</p>", "<p>42.55</p>",
                                "<p>80</p>", "<p>55.5</p>"],
                    solution_en: "<p>9.(a)<br><strong id=\"docs-internal-guid-f73f9366-7fff-71a8-acb1-760b5800d232\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdLIhD5xlePS2aA06wygsWwXXXSYwvxVdnac57GfwCTspz4fm7EySel3PPYQDNYUxZNrnTKK--rcrBpu1-MFTTFhc2RrTtTiCCthfL4Gu7JMQ4KwhO4tB0G2AnGQtZmYcsLjIRhLqcCIp4d5kg83Ro8LWF7?key=7Eq3yv6nd6p1_hMEgz8-iw\" width=\"180\" height=\"99\"></strong><br>As , we know the Formula For Area of Trapezium using its sides and diagonal <br>= <math display=\"inline\"><mfrac><mrow><mfenced separators=\"|\"><mrow><mi>a</mi><mo>+</mo><mi>b</mi></mrow></mfenced></mrow><mrow><mfenced separators=\"|\"><mrow><mi>a</mi><mo>-</mo><mi>b</mi></mrow></mfenced></mrow></mfrac></math><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mo>(</mo><mi mathvariant=\"normal\">s</mi><mo>-</mo><mi mathvariant=\"normal\">a</mi><mo>)</mo><mo>(</mo><mi mathvariant=\"normal\">s</mi><mo>-</mo><mi mathvariant=\"normal\">b</mi><mo>)</mo><mo>(</mo><mi mathvariant=\"normal\">s</mi><mo>-</mo><mi mathvariant=\"normal\">b</mi><mo>-</mo><mi mathvariant=\"normal\">c</mi><mo>)</mo><mo>(</mo><mi mathvariant=\"normal\">s</mi><mo>-</mo><mi mathvariant=\"normal\">b</mi><mo>-</mo><mi mathvariant=\"normal\">d</mi><mo>)</mo></msqrt></math><br>Here , S = Semiperimeter of trapezium<br>a = longer parallel side <br>b = shorter parallel side<br>a + b = 24 and a - b = 8<br>Then, a = 16 , b = 8<br>S = <math display=\"inline\"><mfrac><mrow><mn>16</mn><mo>+</mo><mn>8</mn><mo>+</mo><mn>5</mn><mo>+</mo><mn>9</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> = 19<br>Now, Area of Trapezium = <math display=\"inline\"><mfrac><mrow><mn>24</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mo>(</mo><mn>19</mn><mo>-</mo><mn>16</mn><mo>)</mo><mo>(</mo><mn>19</mn><mo>-</mo><mn>8</mn><mo>)</mo><mo>(</mo><mn>19</mn><mo>-</mo><mn>8</mn><mo>-</mo><mn>9</mn><mo>)</mo><mo>(</mo><mn>19</mn><mo>-</mo><mn>8</mn><mo>-</mo><mn>5</mn><mo>)</mo></msqrt></math><br>= 3<math display=\"inline\"><msqrt><mn>3</mn><mo>&#215;</mo><mn>11</mn><mo>&#215;</mo><mn>2</mn><mo>&#215;</mo><mn>6</mn></msqrt></math> = 3<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>396</mn></msqrt></math> &asymp; 60 cm&sup2;</p>",
                    solution_hi: "<p>9.(a)<br><strong id=\"docs-internal-guid-f73f9366-7fff-71a8-acb1-760b5800d232\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdLIhD5xlePS2aA06wygsWwXXXSYwvxVdnac57GfwCTspz4fm7EySel3PPYQDNYUxZNrnTKK--rcrBpu1-MFTTFhc2RrTtTiCCthfL4Gu7JMQ4KwhO4tB0G2AnGQtZmYcsLjIRhLqcCIp4d5kg83Ro8LWF7?key=7Eq3yv6nd6p1_hMEgz8-iw\" width=\"180\" height=\"99\"></strong><br>जैसा कि, हम समलंब चतुर्भुज के क्षेत्रफल का सूत्र उसकी भुजाओं और विकर्ण का उपयोग करके जानते हैं<br>= <math display=\"inline\"><mfrac><mrow><mfenced separators=\"|\"><mrow><mi>a</mi><mo>+</mo><mi>b</mi></mrow></mfenced></mrow><mrow><mfenced separators=\"|\"><mrow><mi>a</mi><mo>-</mo><mi>b</mi></mrow></mfenced></mrow></mfrac></math><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mo>(</mo><mi mathvariant=\"normal\">s</mi><mo>-</mo><mi mathvariant=\"normal\">a</mi><mo>)</mo><mo>(</mo><mi mathvariant=\"normal\">s</mi><mo>-</mo><mi mathvariant=\"normal\">b</mi><mo>)</mo><mo>(</mo><mi mathvariant=\"normal\">s</mi><mo>-</mo><mi mathvariant=\"normal\">b</mi><mo>-</mo><mi mathvariant=\"normal\">c</mi><mo>)</mo><mo>(</mo><mi mathvariant=\"normal\">s</mi><mo>-</mo><mi mathvariant=\"normal\">b</mi><mo>-</mo><mi mathvariant=\"normal\">d</mi><mo>)</mo></msqrt></math><br>यहाँ, S = समलम्ब चतुर्भुज की अर्धपरिमाप<br>a = लंबी समानांतर भुजा<br>b = छोटी समानांतर भुजा<br>a + b = 24 और a - b = 8<br>फिर, a = 16 , b = 8<br>S = <math display=\"inline\"><mfrac><mrow><mn>16</mn><mo>+</mo><mn>8</mn><mo>+</mo><mn>5</mn><mo>+</mo><mn>9</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> = 19<br>अब, समलंब चतुर्भुज का क्षेत्रफल = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>24</mn><mn>8</mn></mfrac></math><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mo>(</mo><mn>19</mn><mo>-</mo><mn>16</mn><mo>)</mo><mo>(</mo><mn>19</mn><mo>-</mo><mn>8</mn><mo>)</mo><mo>(</mo><mn>19</mn><mo>-</mo><mn>8</mn><mo>-</mo><mn>9</mn><mo>)</mo><mo>(</mo><mn>19</mn><mo>-</mo><mn>8</mn><mo>-</mo><mn>5</mn><mo>)</mo></msqrt></math><br>= 3<math display=\"inline\"><msqrt><mn>3</mn><mo>&#215;</mo><mn>11</mn><mo>&#215;</mo><mn>2</mn><mo>&#215;</mo><mn>6</mn></msqrt></math> = 3<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>396</mn></msqrt></math> &asymp; 60 सेमी&sup2;</p>",
                    correct: "a",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "10",
                    section: "misc",
                    question_en: "<p>10. From a point h metre above a lake the angle of elevation of a cloud is a and the angle of depression of its reflection is b. The height of the cloud is :-</p>",
                    question_hi: "<p>10. एक झील के ऊपर एक बिंदु h मीटर से एक बादल का उन्नयन कोण a है और इसके प्रतिबिंब का अवनमन कोण b है। बादल की ऊँचाई है :-</p>",
                    options_en: ["<p>h &times; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>tana</mi><mo>+</mo><mi>tanb</mi></mrow><mrow><mi>tanb</mi><mo>-</mo><mi>tana</mi></mrow></mfrac></math></p>", "<p>h &times; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>tana</mi><mo>+</mo><mi>tanb</mi></mrow><mrow><mi>tana</mi><mo>-</mo><mi>tanb</mi></mrow></mfrac></math></p>", 
                                "<p>h &times; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><mi>tana</mi><mo>+</mo><mi>tanb</mi></mrow><mrow><mi>tanb</mi><mo>-</mo><mi>tana</mi></mrow></mfrac></math></p>", "<p>2h &times;&nbsp;<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>tana</mi><mo>+</mo><mi>tanb</mi></mrow><mrow><mi>tana</mi><mo>-</mo><mi>tanb</mi></mrow></mfrac></math></p>"],
                    options_hi: ["<p>h &times; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>tana</mi><mo>+</mo><mi>tanb</mi></mrow><mrow><mi>tanb</mi><mo>-</mo><mi>tana</mi></mrow></mfrac></math></p>", "<p>h &times; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>tana</mi><mo>+</mo><mi>tanb</mi></mrow><mrow><mi>tana</mi><mo>-</mo><mi>tanb</mi></mrow></mfrac></math></p>",
                                "<p>h &times; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><mi>tana</mi><mo>+</mo><mi>tanb</mi></mrow><mrow><mi>tanb</mi><mo>-</mo><mi>tana</mi></mrow></mfrac></math></p>", "<p>2h &times; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>tana</mi><mo>+</mo><mi>tanb</mi></mrow><mrow><mi>tana</mi><mo>-</mo><mi>tanb</mi></mrow></mfrac></math></p>"],
                    solution_en: "<p>10.(a)<br><strong id=\"docs-internal-guid-2e032baf-7fff-d93a-a8ea-a56150181e6c\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXd7WB5J0Q-Y9W0hb8ocaC-stjBTKHHvks4DyQTz0DbWk8TdwHzaX-uBs2HClXFpUYu9pkHQeqeWSTJOnxvtwv71gPx4p6sh2ASt1hVPi5g00OD_tY91QzHGFjVBWUN5nWSWbQUq1niyMX3ld04Ytu1_wO-9?key=7Eq3yv6nd6p1_hMEgz8-iw\" width=\"200\" height=\"223\"></strong><br>Let H be the height of the cloud .<br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>tana</mi><mo>=</mo><mfrac><mrow><mi mathvariant=\"normal\">H</mi><mo>-</mo><mi mathvariant=\"normal\">h</mi></mrow><mi>AB</mi></mfrac></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>tanb</mi><mo>=</mo><mfrac><mrow><mi mathvariant=\"normal\">H</mi><mo>+</mo><mi mathvariant=\"normal\">h</mi></mrow><mi>AB</mi></mfrac></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>tana</mi><mi>tanb</mi></mfrac><mo>=</mo><mfrac><mrow><mi mathvariant=\"normal\">H</mi><mo>-</mo><mi mathvariant=\"normal\">h</mi></mrow><mi>AB</mi></mfrac><mo>&#215;</mo><mfrac><mi>AB</mi><mrow><mi mathvariant=\"normal\">H</mi><mo>+</mo><mi mathvariant=\"normal\">h</mi></mrow></mfrac><mo>=</mo><mfrac><mrow><mi mathvariant=\"normal\">H</mi><mo>-</mo><mi mathvariant=\"normal\">h</mi></mrow><mrow><mi mathvariant=\"normal\">H</mi><mo>+</mo><mi mathvariant=\"normal\">h</mi></mrow></mfrac></math><br>Using componendo and dividendo,<br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>tana</mi><mo>+</mo><mi>tanb</mi></mrow><mrow><mi>tana</mi><mo>-</mo><mi>tanb</mi></mrow></mfrac><mo>=</mo><mfrac><mrow><mi mathvariant=\"normal\">H</mi><mo>-</mo><mi mathvariant=\"normal\">h</mi><mo>+</mo><mi mathvariant=\"normal\">H</mi><mo>+</mo><mi mathvariant=\"normal\">h</mi></mrow><mrow><mi mathvariant=\"normal\">H</mi><mo>-</mo><mi mathvariant=\"normal\">h</mi><mo>-</mo><mi mathvariant=\"normal\">H</mi><mo>-</mo><mi mathvariant=\"normal\">h</mi></mrow></mfrac><mo>=</mo><mfrac><mrow><mn>2</mn><mi mathvariant=\"normal\">H</mi></mrow><mrow><mo>-</mo><mn>2</mn><mi mathvariant=\"normal\">h</mi></mrow></mfrac><mo>=</mo><mfrac><mrow><mo>-</mo><mi mathvariant=\"normal\">H</mi></mrow><mi mathvariant=\"normal\">h</mi></mfrac></math> <br>H = h &times;&nbsp;<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"true\"><mfrac><mrow><mi>tana</mi><mo>+</mo><mi>tanb</mi></mrow><mrow><mi>tanb</mi><mo>-</mo><mi>tana</mi></mrow></mfrac></mstyle></math></p>",
                    solution_hi: "<p>10.(a)<br><strong id=\"docs-internal-guid-2e032baf-7fff-d93a-a8ea-a56150181e6c\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXd7WB5J0Q-Y9W0hb8ocaC-stjBTKHHvks4DyQTz0DbWk8TdwHzaX-uBs2HClXFpUYu9pkHQeqeWSTJOnxvtwv71gPx4p6sh2ASt1hVPi5g00OD_tY91QzHGFjVBWUN5nWSWbQUq1niyMX3ld04Ytu1_wO-9?key=7Eq3yv6nd6p1_hMEgz8-iw\" width=\"200\" height=\"223\"></strong><br>मान लीजिए H बादल की ऊंचाई है।<br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>tana</mi><mo>=</mo><mfrac><mrow><mi mathvariant=\"normal\">H</mi><mo>-</mo><mi mathvariant=\"normal\">h</mi></mrow><mi>AB</mi></mfrac></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>tanb</mi><mo>=</mo><mfrac><mrow><mi mathvariant=\"normal\">H</mi><mo>+</mo><mi mathvariant=\"normal\">h</mi></mrow><mi>AB</mi></mfrac></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>tana</mi><mi>tanb</mi></mfrac><mo>=</mo><mfrac><mrow><mi mathvariant=\"normal\">H</mi><mo>-</mo><mi mathvariant=\"normal\">h</mi></mrow><mi>AB</mi></mfrac><mo>&#215;</mo><mfrac><mi>AB</mi><mrow><mi mathvariant=\"normal\">H</mi><mo>+</mo><mi mathvariant=\"normal\">h</mi></mrow></mfrac><mo>=</mo><mfrac><mrow><mi mathvariant=\"normal\">H</mi><mo>-</mo><mi mathvariant=\"normal\">h</mi></mrow><mrow><mi mathvariant=\"normal\">H</mi><mo>+</mo><mi mathvariant=\"normal\">h</mi></mrow></mfrac></math><br>योगांतरानुपात का उपयोग करने पर,<br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>tana</mi><mo>+</mo><mi>tanb</mi></mrow><mrow><mi>tana</mi><mo>-</mo><mi>tanb</mi></mrow></mfrac><mo>=</mo><mfrac><mrow><mi mathvariant=\"normal\">H</mi><mo>-</mo><mi mathvariant=\"normal\">h</mi><mo>+</mo><mi mathvariant=\"normal\">H</mi><mo>+</mo><mi mathvariant=\"normal\">h</mi></mrow><mrow><mi mathvariant=\"normal\">H</mi><mo>-</mo><mi mathvariant=\"normal\">h</mi><mo>-</mo><mi mathvariant=\"normal\">H</mi><mo>-</mo><mi mathvariant=\"normal\">h</mi></mrow></mfrac><mo>=</mo><mfrac><mrow><mn>2</mn><mi mathvariant=\"normal\">H</mi></mrow><mrow><mo>-</mo><mn>2</mn><mi mathvariant=\"normal\">h</mi></mrow></mfrac><mo>=</mo><mfrac><mrow><mo>-</mo><mi mathvariant=\"normal\">H</mi></mrow><mi mathvariant=\"normal\">h</mi></mfrac></math> <br>H = h &times;&nbsp;<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"true\"><mfrac><mrow><mi>tana</mi><mo>+</mo><mi>tanb</mi></mrow><mrow><mi>tanb</mi><mo>-</mo><mi>tana</mi></mrow></mfrac></mstyle></math></p>",
                    correct: "a",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "11",
                    section: "misc",
                    question_en: "<p>11. Fresh fruit contains 68% water and dry fruit contains 20% water. How much dry fruit can be obtained from 100 kg of fresh fruits?</p>",
                    question_hi: "<p>11.ताज़े फल में 68% पानी और सूखे फल में 20% पानी होता है | 100 किलो ग्राम ताज़े फल से कितने किलोग्राम सूखे फल प्राप्त किये जा सकते हैं ?</p>",
                    options_en: ["<p>80</p>", "<p>60</p>", 
                                "<p>40</p>", "<p>20</p>"],
                    options_hi: ["<p>80</p>", "<p>60</p>",
                                "<p>40</p>", "<p>20</p>"],
                    solution_en: "<p>11.(c)<br>68% = <math display=\"inline\"><mfrac><mrow><mn>17</mn></mrow><mrow><mn>25</mn></mrow></mfrac></math> and 20% = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>5</mn></mfrac></math> <br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; Pulp : Water<br>Fresh fruit&nbsp; &nbsp; &nbsp; 8&nbsp; &nbsp; :&nbsp; &nbsp;17<br>Dry fruit&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 4&nbsp; &nbsp; :&nbsp; &nbsp;1<br>Balancing the ratio for pulp<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; Pulp : Water<br>Fresh fruit&nbsp; &nbsp; &nbsp;32&nbsp; &nbsp;:&nbsp; &nbsp;68<br>Dry fruit&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;32&nbsp; :&nbsp; &nbsp; 8<br>According to the question,<br>(32 + 68) unit = 100 kg<br><math display=\"inline\"><mn>1</mn></math> unit = 1kg<br>40 unit = 40 kg</p>",
                    solution_hi: "<p>11.(c)<br>68% = <math display=\"inline\"><mfrac><mrow><mn>17</mn></mrow><mrow><mn>25</mn></mrow></mfrac></math> और 20% = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>5</mn></mfrac></math><br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;पल्प : पानी<br>ताजा फल&nbsp; &nbsp; &nbsp; 8&nbsp; :&nbsp; 17<br>सूखे फल&nbsp; &nbsp; &nbsp; &nbsp;4&nbsp; :&nbsp; &nbsp;1<br>पल्प के अनुपात को संतुलित करना,<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; पल्प : पानी<br>ताजा फल&nbsp; &nbsp; 32 : 68<br>सूखे फल&nbsp; &nbsp; &nbsp;32 : 8<br>प्रश्न के अनुसार,<br>(32 + 68) इकाई = 100 किग्रा<br>1 इकाई = 1 किलो<br>40 इकाई = 40 किग्रा</p>",
                    correct: "c",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "12",
                    section: "misc",
                    question_en: "<p>12. If mean is 57<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> % of median and mode is 7.14% of the mean. If sum of mean , median and mode is 316. Then find the average of mean , median and mode?</p>",
                    question_hi: "<p>12. यदि माध्य माध्यिका का 57<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> % है और बहुलक माध्य का 7.14% है। यदि माध्य, माध्यिका और बहुलक का योग 316 है। तो माध्य, माध्यिका और बहुलक का औसत ज्ञात कीजिए।</p>",
                    options_en: ["<p>105.33</p>", "<p>109.45</p>", 
                                "<p>111.11</p>", "<p>117.31</p>"],
                    options_hi: ["<p>105.33</p>", "<p>109.45</p>",
                                "<p>111.11</p>", "<p>117.31</p>"],
                    solution_en: "<p>12.(a) 57<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> % = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>7</mn></mfrac></math>, and 7.14% = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>14</mn></mfrac></math><br>Mean : Median = 4 : 7 <br>and, Mean : Mode = 14 : 1&nbsp;<br>Now , equating the value of mean , median and mode ,&nbsp;<br>mode&nbsp; :&nbsp; mean : median&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 4&nbsp; &nbsp; : &nbsp; &nbsp; &nbsp; 7<br>1&nbsp; &nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; 14<br>4&nbsp; &nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; 56&nbsp; &nbsp; &nbsp;: &nbsp; &nbsp; 98&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<br><strong id=\"docs-internal-guid-2efa8894-7fff-c70c-e098-cdd9a8962ae9\"></strong>Given that&nbsp;<math display=\"inline\"><mo>&#8658;</mo></math> 158 unit = 316 &rArr; 1 unit = 2<br>Then , the average of mean , median and mode = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>112</mn><mo>+</mo><mn>196</mn><mo>+</mo><mn>8</mn></mrow><mn>3</mn></mfrac></math> = 105.33</p>",
                    solution_hi: "<p>12. (a) :- 57<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> % = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>7</mn></mfrac></math>, और 7.14% =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>14</mn></mfrac></math><br>माध्य : माध्यिका = 4 : 7 <br>और, माध्य : बहुलक = 14 : 1&nbsp;<br>अब, माध्य, माध्यिका और बहुलक के मान को समान करने पर,<br>बहुलक : माध्य : माध्यिका <br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 4&nbsp; &nbsp;:&nbsp; &nbsp; 7<br>&nbsp; &nbsp;1&nbsp; &nbsp; &nbsp; :&nbsp; 14<br>&nbsp; &nbsp;4&nbsp; &nbsp; &nbsp; :&nbsp; 56&nbsp; &nbsp;:&nbsp; &nbsp;98 <br>दिया गया है कि <math display=\"inline\"><mo>&#8658;</mo></math> 158 यूनिट = 316 &rArr; 1 यूनिट = 2<br>फिर, माध्य, माध्यिका और बहुलक का औसत = <math display=\"inline\"><mfrac><mrow><mn>112</mn><mo>+</mo><mn>196</mn><mo>+</mo><mn>8</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> = 105.33</p>",
                    correct: "a",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "13",
                    section: "misc",
                    question_en: "<p>13. A sum of money was borrowed and paid back in two equal annual instalments of Rs. 980, allowing 4% compound interest. The sum (in Rs. to the nearest tens) borrowed was:</p>",
                    question_hi: "<p>13. एक राशि उधार ली गई थी और 4% चक्रवृद्धि ब्याज की दर से , 980 रुपये की दो समान वार्षिक किश्तों में वापस भुगतान किया गया था। उधार ली गई राशि (दहाई अंक के निकटतम रुपयों में ) क्या है?</p>",
                    options_en: ["<p>1,850</p>", "<p>1,960</p>", 
                                "<p>1,760</p>", "<p>2,050</p>"],
                    options_hi: ["<p>1,850</p>", "<p>1,960</p>",
                                "<p>1,760</p>", "<p>2,050</p>"],
                    solution_en: "<p>13.(a) According to the question,<br>Sum borrowed&nbsp;= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>980</mn><msup><mrow><mo>(</mo><mn>1</mn><mo>+</mo><mstyle displaystyle=\"true\"><mfrac><mn>4</mn><mn>100</mn></mfrac></mstyle><mo>)</mo></mrow><mn>2</mn></msup></mfrac><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mfrac><mn>980</mn><mrow><mo>(</mo><mn>1</mn><mo>+</mo><mfrac><mn>4</mn><mn>100</mn></mfrac><mo>)</mo></mrow></mfrac></math> <br>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>980</mn><mstyle displaystyle=\"true\"><mfrac><mn>676</mn><mn>625</mn></mfrac></mstyle></mfrac><mo>+</mo><mfrac><mn>980</mn><mstyle displaystyle=\"true\"><mfrac><mn>26</mn><mn>25</mn></mfrac></mstyle></mfrac></math> <br>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>980</mn><mo>&#215;</mo><mn>625</mn></mrow><mn>676</mn></mfrac><mo>+</mo><mfrac><mrow><mn>980</mn><mo>&#215;</mo><mn>25</mn></mrow><mn>26</mn></mfrac></math> <br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>980</mn><mo>&#215;</mo><mn>25</mn><mo>&#215;</mo><mo>(</mo><mn>25</mn><mo>+</mo><mn>26</mn><mo>)</mo></mrow><mn>676</mn></mfrac></math><br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>980</mn><mo>&#215;</mo><mn>25</mn><mo>&#215;</mo><mo>(</mo><mn>51</mn><mo>)</mo></mrow><mn>676</mn></mfrac></math> = 1848.37 <math display=\"inline\"><mo>&#8776;</mo></math> ₹1,850 (nearest to tens)</p>",
                    solution_hi: "<p>13.(a) प्रश्न के अनुसार,<br>उधार ली गई राशि&nbsp;= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>980</mn><msup><mrow><mo>(</mo><mn>1</mn><mo>+</mo><mstyle displaystyle=\"true\"><mfrac><mn>4</mn><mn>100</mn></mfrac></mstyle><mo>)</mo></mrow><mn>2</mn></msup></mfrac><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mfrac><mn>980</mn><mrow><mo>(</mo><mn>1</mn><mo>+</mo><mfrac><mn>4</mn><mn>100</mn></mfrac><mo>)</mo></mrow></mfrac></math> <br>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>980</mn><mstyle displaystyle=\"true\"><mfrac><mn>676</mn><mn>625</mn></mfrac></mstyle></mfrac><mo>+</mo><mfrac><mn>980</mn><mstyle displaystyle=\"true\"><mfrac><mn>26</mn><mn>25</mn></mfrac></mstyle></mfrac></math> <br>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>980</mn><mo>&#215;</mo><mn>625</mn></mrow><mn>676</mn></mfrac><mo>+</mo><mfrac><mrow><mn>980</mn><mo>&#215;</mo><mn>25</mn></mrow><mn>26</mn></mfrac></math> <br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>980</mn><mo>&#215;</mo><mn>25</mn><mo>&#215;</mo><mo>(</mo><mn>25</mn><mo>+</mo><mn>26</mn><mo>)</mo></mrow><mn>676</mn></mfrac></math><br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>980</mn><mo>&#215;</mo><mn>25</mn><mo>&#215;</mo><mo>(</mo><mn>51</mn><mo>)</mo></mrow><mn>676</mn></mfrac></math>= 1848.37 &asymp; ₹1,850 (दहाई के निकटतम)</p>",
                    correct: "a",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "14",
                    section: "misc",
                    question_en: "<p>14. Study the given graph and answer the question that follows.<br><strong id=\"docs-internal-guid-345ae897-7fff-0ed9-b0e1-fede27cc89df\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdJ622xyyh6n0LZpjc8ivIJ7h8Z8exk60Aq1p_EffmPu6mWiHSxb5eUasThm2RKA62k9Zwj_kieK2uLmVxnHz-kAP2tRNcopqYs5_q9SYuJC6HKAmhLltoXI_WnzXJifgXx_ej_VINFZJFQay-DYNO6k4ZA?key=7Eq3yv6nd6p1_hMEgz8-iw\" width=\"250\" height=\"272\"></strong><br>The average exports of country ABC in 2015, 2017 and 2018 are what percentage of the total imports from 2015 to 2018 (correct to one decimal place)?</p>",
                    question_hi: "<p>14. दिए गए ग्राफ का अध्ययन करें और नीचे दिए गए प्रश्न का उत्तर दें।<br><strong id=\"docs-internal-guid-345ae897-7fff-0ed9-b0e1-fede27cc89df\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdJ622xyyh6n0LZpjc8ivIJ7h8Z8exk60Aq1p_EffmPu6mWiHSxb5eUasThm2RKA62k9Zwj_kieK2uLmVxnHz-kAP2tRNcopqYs5_q9SYuJC6HKAmhLltoXI_WnzXJifgXx_ej_VINFZJFQay-DYNO6k4ZA?key=7Eq3yv6nd6p1_hMEgz8-iw\" width=\"250\" height=\"272\"></strong><br><strong>Import and Export of country ABC from 2015 to 19 (in Rs. crores) </strong>- 2015 से 19 तक देश एबीसी का आयात और निर्यात (करोड़ रुपये में),&nbsp;<strong>Import </strong>- आयात, <strong>Export </strong>- निर्यात<br>2015, 2017 और 2018 में देश ABC का औसत निर्यात 2015 से 2018 तक कुल आयात का कितना प्रतिशत है ( एक दशमलव स्थान तक सही) ?</p>",
                    options_en: ["<p>22.8%</p>", "<p>20.6%</p>", 
                                "<p>24.4%</p>", "<p>22.1%</p>"],
                    options_hi: ["<p>22.8%</p>", "<p>20.6%</p>",
                                "<p>24.4%</p>", "<p>22.1%</p>"],
                    solution_en: "<p>14.(d)<br>Average exports country ABC in 2015, 2017 and 2018 = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>150</mn><mo>+</mo><mn>200</mn><mo>+</mo><mn>280</mn></mrow><mn>3</mn></mfrac></math> = 210<br>Total imports from 2015 to 2018&nbsp;= 180 + 200 + 250 + 320 = 950<br>Required percentage = <math display=\"inline\"><mfrac><mrow><mn>210</mn></mrow><mrow><mn>950</mn></mrow></mfrac></math> &times; 100 = 22.1 %</p>",
                    solution_hi: "<p>14.(d)<br>देश ABC का 2015, 2017 और 2018 में औसत निर्यात = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>150</mn><mo>+</mo><mn>200</mn><mo>+</mo><mn>280</mn></mrow><mn>3</mn></mfrac></math> = 210<br>2015 से 2018 तक कुल आयात&nbsp;= 180 + 200 + 250 + 320 = 950<br>आवश्यक प्रतिशत = <math display=\"inline\"><mfrac><mrow><mn>210</mn></mrow><mrow><mn>950</mn></mrow></mfrac></math> &times; 100 = 22.1 %</p>",
                    correct: "d",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "15",
                    section: "misc",
                    question_en: "<p>15. If a shopkeeper purchases a certain number of pens for a certain amount and sells a fraction of the number for the same amount, then his profit is 500%. What is the fraction?</p>",
                    question_hi: "<p>15. यदि एक दुकानदार एक निश्चित राशि के लिए एक निश्चित संख्या में पेन खरीदता है और उसी संख्या के एक अंश को उसी राशि में बेचता है, तो उसका लाभ 500% है। अंश क्या है?</p>",
                    options_en: ["<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>6</mn></mrow></mfrac></math></p>", 
                                "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>9</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math></p>"],
                    options_hi: ["<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>6</mn></mrow></mfrac></math></p>",
                                "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>9</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math></p>"],
                    solution_en: "<p>15.(b)&nbsp;Let the shopkeeper purchase P unit at ₹Q and sell R unit at ₹Q .<br>CP of each item = ₹<math display=\"inline\"><mfrac><mrow><mi>Q</mi></mrow><mrow><mi>P</mi></mrow></mfrac></math> <br>SP of each item = ₹<math display=\"inline\"><mfrac><mrow><mi>Q</mi></mrow><mrow><mi>R</mi></mrow></mfrac></math><br>ATQ,<br><math display=\"inline\"><mo>&#8658;</mo></math><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mstyle displaystyle=\"true\"><mfrac><mi mathvariant=\"normal\">Q</mi><mi mathvariant=\"normal\">R</mi></mfrac></mstyle><mo>-</mo><mstyle displaystyle=\"true\"><mfrac><mi mathvariant=\"normal\">Q</mi><mi mathvariant=\"normal\">P</mi></mfrac></mstyle></mrow><mfrac><mi mathvariant=\"normal\">Q</mi><mi mathvariant=\"normal\">P</mi></mfrac></mfrac><mo>&#215;</mo><mn>100</mn><mo>=</mo><mn>500</mn></math><br><math display=\"inline\"><mo>&#8658;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi mathvariant=\"normal\">P</mi><mo>-</mo><mi mathvariant=\"normal\">R</mi></mrow><mi mathvariant=\"normal\">R</mi></mfrac><mo>=</mo><mn>5</mn><mo>&#8658;</mo><mfrac><mi mathvariant=\"normal\">P</mi><mi mathvariant=\"normal\">R</mi></mfrac><mo>-</mo><mn>1</mn><mo>=</mo><mn>5</mn></math><br><math display=\"inline\"><mo>&#8658;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>P</mi><mi>R</mi></mfrac><mo>=</mo><mfrac><mn>6</mn><mn>1</mn></mfrac><mo>&#8658;</mo><mfrac><mi>R</mi><mi>P</mi></mfrac><mo>=</mo><mfrac><mn>1</mn><mn>6</mn></mfrac></math></p>",
                    solution_hi: "<p>15.(b)&nbsp;बता दें कि दुकानदार ₹Q पर P यूनिट खरीदता है और ₹Q पर R यूनिट बेचता है।<br>प्रत्येक वस्तु का क्रय मूल्य = ₹<math display=\"inline\"><mfrac><mrow><mi>Q</mi></mrow><mrow><mi>P</mi></mrow></mfrac></math> <br>प्रत्येक वस्तु का विक्रय मूल्य = ₹<math display=\"inline\"><mfrac><mrow><mi>Q</mi></mrow><mrow><mi>R</mi></mrow></mfrac></math><br>प्रश्न के अनुसार,<br><math display=\"inline\"><mo>&#8658;</mo></math><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mstyle displaystyle=\"true\"><mfrac><mi mathvariant=\"normal\">Q</mi><mi mathvariant=\"normal\">R</mi></mfrac></mstyle><mo>-</mo><mstyle displaystyle=\"true\"><mfrac><mi mathvariant=\"normal\">Q</mi><mi mathvariant=\"normal\">P</mi></mfrac></mstyle></mrow><mfrac><mi mathvariant=\"normal\">Q</mi><mi mathvariant=\"normal\">P</mi></mfrac></mfrac><mo>&#215;</mo><mn>100</mn><mo>=</mo><mn>500</mn></math><br><math display=\"inline\"><mo>&#8658;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi mathvariant=\"normal\">P</mi><mo>-</mo><mi mathvariant=\"normal\">R</mi></mrow><mi mathvariant=\"normal\">R</mi></mfrac><mo>=</mo><mn>5</mn><mo>&#8658;</mo><mfrac><mi mathvariant=\"normal\">P</mi><mi mathvariant=\"normal\">R</mi></mfrac><mo>-</mo><mn>1</mn><mo>=</mo><mn>5</mn></math><br><math display=\"inline\"><mo>&#8658;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>P</mi><mi>R</mi></mfrac><mo>=</mo><mfrac><mn>6</mn><mn>1</mn></mfrac><mo>&#8658;</mo><mfrac><mi>R</mi><mi>P</mi></mfrac><mo>=</mo><mfrac><mn>1</mn><mn>6</mn></mfrac></math></p>",
                    correct: "b",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "16",
                    section: "misc",
                    question_en: "<p>16. In △ABC, &ang;A = 90&deg;. If BL and CM are medians, They meet at point O such that OL is 4.34% of BO and another median CM is thrice of median BL, then find the Approximate length of BC&nbsp;</p>",
                    question_hi: "<p>16. △ABC में, &ang;A = 90&deg;, यदि BL और CM माध्यिकाएँ हैं, तो वे बिंदु O पर इस प्रकार मिलती हैं कि OL, BO का 4.34% है। और एक अन्य माध्यिका CM माध्यिका BL से तीन गुना है, तो BC की अनुमानित लंबाई ज्ञात कीजिए&nbsp;</p>",
                    options_en: ["<p>75.12 unit</p>", "<p>67.88 unit</p>", 
                                "<p>65.32 unit</p>", "<p>63.88 unit</p>"],
                    options_hi: ["<p>75.12 इकाई</p>", "<p>67.88 इकाई</p>",
                                "<p>65.32 इकाई</p>", "<p>63.88 इकाई</p>"],
                    solution_en: "<p>16.(b)<br><strong id=\"docs-internal-guid-ea7172a8-7fff-021c-87b0-a21988401823\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfCF4EOr7dMEo95M9OgLjrhoOWiclKjsuM0xnhJfIW9iqMt1Q079zVlmYnQfilCbOlCGzNdBHuWjjHHHcgHpSOma9vmROnjNPVHdZEm_QXXZAMj7kEheZ8Zv8akR97PMWWyxszKqgz5qbpusHXxMfsJiB5N?key=7Eq3yv6nd6p1_hMEgz8-iw\" width=\"150\" height=\"127\"></strong><br>Let , the length of BO = 23 unit Then OL = 1 unit<br>Now, CM = 24 &times; 3 unit = 72 cm<br><strong>By Using Formula :</strong> 4(BL&sup2; + CM&sup2;) = 5BC&sup2;<br><math display=\"inline\"><mo>&#8658;</mo></math> 4 &times; (24&sup2; + 72&sup2;) = 5BC&sup2;<br><math display=\"inline\"><mo>&#8658;</mo></math> 4 &times; (576 + 5184) = 5BC&sup2;&nbsp;<br><math display=\"inline\"><mo>&#8658;</mo></math> BC&sup2; = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>4</mn><mo>&#215;</mo><mn>5760</mn></mrow><mn>5</mn></mfrac><mo>=</mo><mfrac><mn>23040</mn><mn>5</mn></mfrac></math> = 4608&nbsp;<br><math display=\"inline\"><mo>&#8658;</mo></math> BC = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>4608</mn></msqrt></math> &asymp; 67.88 unit</p>",
                    solution_hi: "<p>16.(b)<br><strong id=\"docs-internal-guid-ea7172a8-7fff-021c-87b0-a21988401823\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfCF4EOr7dMEo95M9OgLjrhoOWiclKjsuM0xnhJfIW9iqMt1Q079zVlmYnQfilCbOlCGzNdBHuWjjHHHcgHpSOma9vmROnjNPVHdZEm_QXXZAMj7kEheZ8Zv8akR97PMWWyxszKqgz5qbpusHXxMfsJiB5N?key=7Eq3yv6nd6p1_hMEgz8-iw\" width=\"150\" height=\"127\"></strong><br>माना, BO की लंबाई = 23 इकाई, तब OL = 1 इकाई<br>अब, CM = 24 &times; 3 इकाई = 72 इकाई<br><strong>सूत्र का उपयोग करके:</strong> 4(BL&sup2; + CM&sup2;) = 5BC&sup2;<br><math display=\"inline\"><mo>&#8658;</mo></math> 4 &times; (24&sup2; + 72&sup2;) = 5BC&sup2;<br><math display=\"inline\"><mo>&#8658;</mo></math> 4 &times; (576 + 5184) = 5BC&sup2;&nbsp;<br><math display=\"inline\"><mo>&#8658;</mo></math> BC&sup2; = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>4</mn><mo>&#215;</mo><mn>5760</mn></mrow><mn>5</mn></mfrac><mo>=</mo><mfrac><mn>23040</mn><mn>5</mn></mfrac></math> = 4608&nbsp;<br><math display=\"inline\"><mo>&#8658;</mo></math> BC = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>4608</mn></msqrt></math> &asymp; 67.88इकाई</p>",
                    correct: "b",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "17",
                    section: "misc",
                    question_en: "<p>17. The average age of 60 students in a group is 27.12 years, 35% of the number of students are girls and the rest are boys. If the ratio of the average age of boys and girls is 6 : 5, then what is the average age (in years) of the girls?</p>",
                    question_hi: "<p>17. एक समूह में 60 छात्रों की औसत उम्र 27.12 वर्ष है | छात्रों की संख्या में 35% लड़कियाँ हैं तथा शेष लड़के हैं | यदि लड़कों और लड़कियों की औसत उम्र का अनुपात 6 : 5 है, तो लड़कियों की औसत उम्र ( वर्ष में ) कितनी है?</p>",
                    options_en: ["<p>23</p>", "<p>22</p>", 
                                "<p>24</p>", "<p>26</p>"],
                    options_hi: ["<p>23</p>", "<p>22</p>",
                                "<p>24</p>", "<p>26</p>"],
                    solution_en: "<p>17.(c)&nbsp;Percentage of girls = 35<br><math display=\"inline\"><mo>&#8658;</mo></math> percentage of boys = 65<br><math display=\"inline\"><mo>&#8658;</mo></math> Boys : Girls = 13 : 7<br>Let the average age of boys = 6k and <br>the average age of girls = 5k<br>According to the question<br><math display=\"inline\"><mo>&#8658;</mo></math> 13(6k) + 7(5k) = (13+7) &times; 27.12<br><math display=\"inline\"><mo>&#8658;</mo></math>k = <math display=\"inline\"><mfrac><mrow><mn>24</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math><br>Average age of girls (5k) = 5 &times; <math display=\"inline\"><mfrac><mrow><mn>24</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> = 24</p>",
                    solution_hi: "<p>17.(c)&nbsp;लड़कियों का प्रतिशत = 35<br><math display=\"inline\"><mo>&#8658;</mo></math> लड़कों का प्रतिशत = 65<br><math display=\"inline\"><mo>&#8658;</mo></math> लड़के : लड़कियाँ = 13 : 7<br>माना लड़कों की औसत आयु = 6k और <br>लड़कियों की औसत आयु = 5k<br>प्रश्न के अनुसार,<br><math display=\"inline\"><mo>&#8658;</mo></math> 13(6k) + 7(5k) = (13 + 7) &times; 27.12<br><math display=\"inline\"><mo>&#8658;</mo></math>k = <math display=\"inline\"><mfrac><mrow><mn>24</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math><br>लड़कियों की औसत आयु (5k) = 5 &times; <math display=\"inline\"><mfrac><mrow><mn>24</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> = 24</p>",
                    correct: "c",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "18",
                    section: "misc",
                    question_en: "<p>18. A person spends 40% of his monthly income. The monthly income of the person is increased by 15% and his expenditure is decreased by 22%. What is the approximate percentage increase in his savings?</p>",
                    question_hi: "<p>18. एक व्यक्ति अपनी मासिक आय का 40% खर्च करता है। व्यक्ति की मासिक आय में 15% की वृद्धि होती है और उसके व्यय में 22% की कमी आती है। उसकी बचत में अनुमानित कितने प्रतिशत वृद्धि हुई है?</p>",
                    options_en: ["<p>40%</p>", "<p>44%</p>", 
                                "<p>32%</p>", "<p>36%</p>"],
                    options_hi: ["<p>40%</p>", "<p>44%</p>",
                                "<p>32%</p>", "<p>36%</p>"],
                    solution_en: "<p>18.(a)&nbsp; &nbsp; &nbsp; Saving = Income : Expenditure&nbsp;<br>Original &rarr;&nbsp; &nbsp;60&nbsp; &nbsp;=&nbsp; &nbsp;100&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp;40<br>New&nbsp; &nbsp; &nbsp; &nbsp;&rarr; 83.8 =&nbsp; &nbsp;115&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp;31.2<br>Percentage change in saving = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>83</mn><mo>.</mo><mn>8</mn><mo>-</mo><mn>60</mn></mrow><mn>60</mn></mfrac><mo>&#215;</mo><mn>100</mn><mo>=</mo><mfrac><mrow><mn>23</mn><mo>.</mo><mn>8</mn></mrow><mn>60</mn></mfrac><mo>&#215;</mo><mn>100</mn></math> = 39.6 &asymp; 40%</p>",
                    solution_hi: "<p>18.(a)&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;बचत = आय : व्यय<br>वास्तविक &rarr;&nbsp; 60&nbsp; &nbsp;= 100 : 40<br>नया&nbsp; &nbsp; &nbsp; &nbsp; &rarr;&nbsp; 83.8 = 115 : 31.2<br>बचत में प्रतिशत परिवर्तन = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>83</mn><mo>.</mo><mn>8</mn><mo>-</mo><mn>60</mn></mrow><mn>60</mn></mfrac><mo>&#215;</mo><mn>100</mn><mo>=</mo><mfrac><mrow><mn>23</mn><mo>.</mo><mn>8</mn></mrow><mn>60</mn></mfrac><mo>&#215;</mo><mn>100</mn></math>&nbsp;= 39.6 &asymp; 40%</p>",
                    correct: "a",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "19",
                    section: "misc",
                    question_en: "<p>19. Simplify the following expression.<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>59</mn><mo>&#215;</mo><mn>59</mn><mo>&#215;</mo><mn>59</mn><mo>)</mo><mo>+</mo><mo>(</mo><mn>54</mn><mo>&#215;</mo><mn>54</mn><mo>&#215;</mo><mn>54</mn><mo>)</mo><mo>+</mo><mo>(</mo><mn>57</mn><mo>&#215;</mo><mn>57</mn><mo>&#215;</mo><mn>57</mn><mo>)</mo><mo>-</mo><mn>3</mn><mo>(</mo><mn>59</mn><mo>)</mo><mo>(</mo><mn>54</mn><mo>)</mo><mo>(</mo><mn>57</mn><mo>)</mo></mrow><mrow><mo>(</mo><mn>59</mn><mo>+</mo><mn>54</mn><mo>+</mo><mn>57</mn><mo>)</mo></mrow></mfrac></math></p>",
                    question_hi: "<p>19. निम्नलिखित व्यंजक को सरल कीजिए।<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>59</mn><mo>&#215;</mo><mn>59</mn><mo>&#215;</mo><mn>59</mn><mo>)</mo><mo>+</mo><mo>(</mo><mn>54</mn><mo>&#215;</mo><mn>54</mn><mo>&#215;</mo><mn>54</mn><mo>)</mo><mo>+</mo><mo>(</mo><mn>57</mn><mo>&#215;</mo><mn>57</mn><mo>&#215;</mo><mn>57</mn><mo>)</mo><mo>-</mo><mn>3</mn><mo>(</mo><mn>59</mn><mo>)</mo><mo>(</mo><mn>54</mn><mo>)</mo><mo>(</mo><mn>57</mn><mo>)</mo></mrow><mrow><mo>(</mo><mn>59</mn><mo>+</mo><mn>54</mn><mo>+</mo><mn>57</mn><mo>)</mo></mrow></mfrac></math></p>",
                    options_en: ["<p>38</p>", "<p>76</p>", 
                                "<p>170</p>", "<p>19</p>"],
                    options_hi: ["<p>38</p>", "<p>76</p>",
                                "<p>170</p>", "<p>19</p>"],
                    solution_en: "<p>19.(d)<br><strong>Formula used :</strong> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi mathvariant=\"normal\">a</mi><mn>3</mn></msup><mo>+</mo><msup><mi mathvariant=\"normal\">b</mi><mn>3</mn></msup><mo>+</mo><msup><mi mathvariant=\"normal\">c</mi><mn>3</mn></msup><mo>-</mo><mn>3</mn><mi>abc</mi><mo>=</mo><mo>(</mo><mi mathvariant=\"normal\">a</mi><mo>+</mo><mi mathvariant=\"normal\">b</mi><mo>+</mo><mi mathvariant=\"normal\">c</mi><mo>)</mo><mo>&#215;</mo><mfrac><mn>1</mn><mn>2</mn></mfrac><mo>[</mo><msup><mrow><mo>(</mo><mi mathvariant=\"normal\">a</mi><mo>-</mo><mi mathvariant=\"normal\">b</mi><mo>)</mo></mrow><mn>2</mn></msup><mo>+</mo><msup><mrow><mo>(</mo><mi mathvariant=\"normal\">b</mi><mo>-</mo><mi mathvariant=\"normal\">c</mi><mo>)</mo></mrow><mn>2</mn></msup><mo>+</mo><msup><mrow><mo>(</mo><mi mathvariant=\"normal\">c</mi><mo>-</mo><mi mathvariant=\"normal\">a</mi><mo>)</mo></mrow><mn>2</mn></msup><mo>]</mo></math> <br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>59</mn><mo>&#215;</mo><mn>59</mn><mo>&#215;</mo><mn>59</mn><mo>)</mo><mo>+</mo><mo>(</mo><mn>54</mn><mo>&#215;</mo><mn>54</mn><mo>&#215;</mo><mn>54</mn><mo>)</mo><mo>+</mo><mo>(</mo><mn>57</mn><mo>&#215;</mo><mn>57</mn><mo>&#215;</mo><mn>57</mn><mo>)</mo><mo>-</mo><mn>3</mn><mo>(</mo><mn>59</mn><mo>)</mo><mo>(</mo><mn>54</mn><mo>)</mo><mo>(</mo><mn>57</mn><mo>)</mo></mrow><mrow><mo>(</mo><mn>59</mn><mo>+</mo><mn>54</mn><mo>+</mo><mn>57</mn><mo>)</mo></mrow></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mn>59</mn><mn>3</mn></msup><mo>+</mo><msup><mn>54</mn><mn>3</mn></msup><mo>+</mo><msup><mn>57</mn><mn>3</mn></msup><mo>-</mo><mn>3</mn><mo>(</mo><mn>59</mn><mo>)</mo><mo>(</mo><mn>54</mn><mo>)</mo><mo>(</mo><mn>57</mn><mo>)</mo></mrow><mrow><mo>(</mo><mn>59</mn><mo>+</mo><mn>54</mn><mo>+</mo><mn>57</mn><mo>)</mo></mrow></mfrac></math> <br><math display=\"inline\"><mo>&#8658;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mstyle displaystyle=\"true\"><mfrac><mn>1</mn><mn>2</mn></mfrac></mstyle><mo>&#215;</mo><mo>(</mo><mn>59</mn><mo>+</mo><mn>54</mn><mo>+</mo><mn>57</mn><mo>)</mo><mo>[</mo><msup><mn>5</mn><mn>2</mn></msup><mo>+</mo><msup><mrow><mo>(</mo><mo>-</mo><mn>3</mn><mo>)</mo></mrow><mn>2</mn></msup><mo>+</mo><msup><mrow><mo>(</mo><mo>-</mo><mn>2</mn><mo>)</mo></mrow><mn>2</mn></msup><mo>]</mo></mrow><mrow><mo>(</mo><mn>59</mn><mo>+</mo><mn>54</mn><mo>+</mo><mn>57</mn><mo>)</mo></mrow></mfrac><mo>=</mo><mfrac><mn>1</mn><mn>2</mn></mfrac><mo>&#215;</mo><mo>[</mo><mn>25</mn><mo>+</mo><mn>9</mn><mo>+</mo><mn>4</mn><mo>]</mo><mo>=</mo><mfrac><mn>1</mn><mn>2</mn></mfrac><mo>&#215;</mo><mn>38</mn><mo>=</mo><mn>19</mn></math></p>",
                    solution_hi: "<p>19.(d)<br><strong>प्रयुक्त सूत्र :</strong> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi mathvariant=\"normal\">a</mi><mn>3</mn></msup><mo>+</mo><msup><mi mathvariant=\"normal\">b</mi><mn>3</mn></msup><mo>+</mo><msup><mi mathvariant=\"normal\">c</mi><mn>3</mn></msup><mo>-</mo><mn>3</mn><mi>abc</mi><mo>=</mo><mo>(</mo><mi mathvariant=\"normal\">a</mi><mo>+</mo><mi mathvariant=\"normal\">b</mi><mo>+</mo><mi mathvariant=\"normal\">c</mi><mo>)</mo><mo>&#215;</mo><mfrac><mn>1</mn><mn>2</mn></mfrac><mo>[</mo><msup><mrow><mo>(</mo><mi mathvariant=\"normal\">a</mi><mo>-</mo><mi mathvariant=\"normal\">b</mi><mo>)</mo></mrow><mn>2</mn></msup><mo>+</mo><msup><mrow><mo>(</mo><mi mathvariant=\"normal\">b</mi><mo>-</mo><mi mathvariant=\"normal\">c</mi><mo>)</mo></mrow><mn>2</mn></msup><mo>+</mo><msup><mrow><mo>(</mo><mi mathvariant=\"normal\">c</mi><mo>-</mo><mi mathvariant=\"normal\">a</mi><mo>)</mo></mrow><mn>2</mn></msup><mo>]</mo></math> <br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>59</mn><mo>&#215;</mo><mn>59</mn><mo>&#215;</mo><mn>59</mn><mo>)</mo><mo>+</mo><mo>(</mo><mn>54</mn><mo>&#215;</mo><mn>54</mn><mo>&#215;</mo><mn>54</mn><mo>)</mo><mo>+</mo><mo>(</mo><mn>57</mn><mo>&#215;</mo><mn>57</mn><mo>&#215;</mo><mn>57</mn><mo>)</mo><mo>-</mo><mn>3</mn><mo>(</mo><mn>59</mn><mo>)</mo><mo>(</mo><mn>54</mn><mo>)</mo><mo>(</mo><mn>57</mn><mo>)</mo></mrow><mrow><mo>(</mo><mn>59</mn><mo>+</mo><mn>54</mn><mo>+</mo><mn>57</mn><mo>)</mo></mrow></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mn>59</mn><mn>3</mn></msup><mo>+</mo><msup><mn>54</mn><mn>3</mn></msup><mo>+</mo><msup><mn>57</mn><mn>3</mn></msup><mo>-</mo><mn>3</mn><mo>(</mo><mn>59</mn><mo>)</mo><mo>(</mo><mn>54</mn><mo>)</mo><mo>(</mo><mn>57</mn><mo>)</mo></mrow><mrow><mo>(</mo><mn>59</mn><mo>+</mo><mn>54</mn><mo>+</mo><mn>57</mn><mo>)</mo></mrow></mfrac></math> <br><math display=\"inline\"><mo>&#8658;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mstyle displaystyle=\"true\"><mfrac><mn>1</mn><mn>2</mn></mfrac></mstyle><mo>&#215;</mo><mo>(</mo><mn>59</mn><mo>+</mo><mn>54</mn><mo>+</mo><mn>57</mn><mo>)</mo><mo>[</mo><msup><mn>5</mn><mn>2</mn></msup><mo>+</mo><msup><mrow><mo>(</mo><mo>-</mo><mn>3</mn><mo>)</mo></mrow><mn>2</mn></msup><mo>+</mo><msup><mrow><mo>(</mo><mo>-</mo><mn>2</mn><mo>)</mo></mrow><mn>2</mn></msup><mo>]</mo></mrow><mrow><mo>(</mo><mn>59</mn><mo>+</mo><mn>54</mn><mo>+</mo><mn>57</mn><mo>)</mo></mrow></mfrac><mo>=</mo><mfrac><mn>1</mn><mn>2</mn></mfrac><mo>&#215;</mo><mo>[</mo><mn>25</mn><mo>+</mo><mn>9</mn><mo>+</mo><mn>4</mn><mo>]</mo><mo>=</mo><mfrac><mn>1</mn><mn>2</mn></mfrac><mo>&#215;</mo><mn>38</mn><mo>=</mo><mn>19</mn></math></p>",
                    correct: "d",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "20",
                    section: "misc",
                    question_en: "<p>20. A sum of money becomes two times of itself in 8 years at simple interest, and it becomes four times of itself in 2 years at compound interest, when interest is compounded annually. Find the ratio of the rate of simple interest to the rate of compound interest offered per year.</p>",
                    question_hi: "<p>20. कोई धनराशि साधारण ब्याज पर 8 वर्ष में स्वयं की दोगुनी हो जाती है, और चक्रवृद्धि ब्याज पर 2 वर्ष में स्वयं की चार गुनी हो जाती है, जब ब्याज वार्षिक संयोजित होता है। साधारण ब्याज की दर का प्रति वर्ष दिए जाने वाले चक्रवृद्धि ब्याज की दर से अनुपात ज्ञात कीजिए।</p>",
                    options_en: ["<p>2 : 3</p>", "<p>3 : 5</p>", 
                                "<p>5 : 3</p>", "<p>1 : 8</p>"],
                    options_hi: ["<p>2 : 3</p>", "<p>3 : 5</p>",
                                "<p>5 : 3</p>", "<p>1 : 8</p>"],
                    solution_en: "<p>20.(d)&nbsp;Let the principal be ₹100<br>Then, the amount will be ₹200 at SI and 400 at CI<br>Now, the rate of simple interest = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>200</mn><mo>-</mo><mn>100</mn><mo>)</mo><mo>&#215;</mo><mn>100</mn><mi>&#160;</mi></mrow><mrow><mn>100</mn><mo>&#215;</mo><mn>8</mn></mrow></mfrac></math> = 12.5%<br>And, the rate of Compound interest = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mfrac><mn>400</mn><mn>100</mn></mfrac></msqrt><mo>=</mo><msqrt><mn>4</mn></msqrt><mo>=</mo><mfrac><mn>2</mn><mn>1</mn></mfrac><mo>=</mo><mfrac><mrow><mn>2</mn><mo>-</mo><mn>1</mn></mrow><mn>1</mn></mfrac><mo>&#215;</mo><mn>100</mn></math> = 100%<br>Required ratio = 12.5 : 100 = 1 : 8</p>",
                    solution_hi: "<p>20.(d)&nbsp;माना मूलधन = ₹100 <br>फिर, राशि साधारण ब्याज पर ₹200 और चक्रवृद्धि ब्याज पर 400 होगी<br>अब, साधारण ब्याज की दर = <math display=\"inline\"><mfrac><mrow><mo>(</mo><mn>200</mn><mo>-</mo><mn>100</mn><mo>)</mo><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>100</mn><mi>&#160;</mi></mrow><mrow><mn>100</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>8</mn></mrow></mfrac></math>= 12.5%<br>और, चक्रवृद्धि ब्याज की दर = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mfrac><mn>400</mn><mn>100</mn></mfrac></msqrt><mo>=</mo><msqrt><mn>4</mn></msqrt><mo>=</mo><mfrac><mn>2</mn><mn>1</mn></mfrac><mo>=</mo><mfrac><mrow><mn>2</mn><mo>-</mo><mn>1</mn></mrow><mn>1</mn></mfrac><mo>&#215;</mo><mn>100</mn></math>&nbsp;= 100%<br>आवश्यक अनुपात = 12.5 : 100 = 1 : 8</p>",
                    correct: "d",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "21",
                    section: "misc",
                    question_en: "<p>21. During the rainy season, a huge inflow of water takes place into a reservoir. Measures are taken to clear the reservoir while water keeps flowing into it at a constant rate. It has been observed that 7 and 5 men can clear the reservoir in 20 and 50 days, respectively, with the initial quantity of water in the reservoir being 24 and 36 kilolitres, respectively. What is the rate of inflow of water into the reservoir in litres per day?</p>",
                    question_hi: "<p>21. बरसात के मौसम में, जलाशय में पानी का एक तेज प्रवाह(inflow)होता है। जलाशय को साफ करने के उपाय किए जाते हैं जबकि पानी निरंतर दर से बहता रहता है। यह देखा गया है कि जलाशय में पानी की प्रारंभिक मात्रा क्रमश: 24 और 36 किलोलीटर है| सात और पांच आदमी क्रमश: 20 और 50 दिनों में जलाशय को साफ कर सकते हैं। प्रति दिन (लीटर में) जलाशय में पानी के प्रवाह की दर क्या है?</p>",
                    options_en: ["<p>480</p>", "<p>540</p>", 
                                "<p>600</p>", "<p>640</p>"],
                    options_hi: ["<p>480</p>", "<p>540</p>",
                                "<p>600</p>", "<p>640</p>"],
                    solution_en: "<p>21.(a)&nbsp;Let per day inflow of water in each tank = x lit/day<br>So , water inflow in 20 days in tank = 20x <br>And water inflow in 50 days in second tank = 50x<br>Now, Water inflow = water outflow <br>And efficiency of 1st tank = 24 kilo ltr + 20x = 7man &times;&nbsp;20 days&hellip;.(1)<br>Efficiency of 2nd tank = 36 kilo ltr + 50x = 5man &times;&nbsp;50 days&hellip;&hellip;(2)<br>From equ. (1) <math display=\"inline\"><mo>&#247;</mo></math> (2), we get <br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>24</mn><mi>kl</mi><mo>+</mo><mn>20</mn><mi mathvariant=\"normal\">x</mi></mrow><mrow><mn>36</mn><mi>kl</mi><mo>+</mo><mn>50</mn><mi mathvariant=\"normal\">x</mi></mrow></mfrac><mo>=</mo><mfrac><mrow><mn>7</mn><mo>&#215;</mo><mn>20</mn></mrow><mrow><mn>5</mn><mo>&#215;</mo><mn>50</mn></mrow></mfrac></math>&hellip;&hellip;&hellip;. ( put 24kilo ltr = 24000 ltr , 36kilo ltr = 36000 ltr )<br>After solving this , We find that x = 480 ltr</p>",
                    solution_hi: "<p>21.(a) <br>माना कि प्रत्येक टैंक में प्रतिदिन पानी की आवक = x लीटर/दिन<br>इसलिए, टैंक में 20 दिनों में पानी की आवक = 20x<br>और दूसरी टंकी में 50 दिनों में पानी की आवक = 50x<br>अब, पानी का आवक = पानी का निकास <br>और पहले टैंक की क्षमता = 24 किलो लीटर + 20x = 7 आदमी &times; 20 दिन....(1)<br>दूसरे टैंक की क्षमता = 36 किलो लीटर + 50x = 5 आदमी &times; 50 दिन&hellip;&hellip;(2)<br>समीकरण (1) <math display=\"inline\"><mo>&#247;</mo></math> (2) से हम पाते हैं,<br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>24</mn><mi>kl</mi><mo>+</mo><mn>20</mn><mi mathvariant=\"normal\">x</mi></mrow><mrow><mn>36</mn><mi>kl</mi><mo>+</mo><mn>50</mn><mi mathvariant=\"normal\">x</mi></mrow></mfrac><mo>=</mo><mfrac><mrow><mn>7</mn><mo>&#215;</mo><mn>20</mn></mrow><mrow><mn>5</mn><mo>&#215;</mo><mn>50</mn></mrow></mfrac></math>&hellip;&hellip;&hellip;. (24 किलो लीटर = 24000 लीटर, 36 किलो लीटर = 36000 लीटर का प्रयोग करें )<br>इसे हल करने के बाद, हम पाते हैं कि x = 480 लीटर</p>",
                    correct: "a",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "22",
                    section: "misc",
                    question_en: "<p>22. On the top of a hemispherical dome of radius r , there stands a flag of height h . From a point on the ground , the angle of elevation of top of flag is 30&deg; . After moving a distance d towards the dome , when flag is just visible , the angle of elevation is 45&deg;. The ratio of h to r is equal to:</p>",
                    question_hi: "<p>22. त्रिज्या r के एक अर्धगोलाकार गुंबद के शीर्ष पर h ऊँचाई का एक झंडा खड़ा है, जमीन के एक बिंदु से ध्वज के शीर्ष का उन्नयन कोण 30&deg;है। d दूरी तक गुंबद की ओर बढ़ने के बाद, जब झंडा ठीक दिखाई देता है, तो उन्नयन कोण 45&deg; है। h से r का अनुपात क्या है ?</p>",
                    options_en: ["<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>2</mn></msqrt><mo>-</mo><mn>1</mn></math></p>", "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>2</mn></msqrt><mo>+</mo><mn>1</mn></math></p>", 
                                "<p><math display=\"inline\"><mfrac><mrow><msqrt><mn>3</mn></msqrt><mo>+</mo><mn>1</mn></mrow><mrow><mn>2</mn><msqrt><mn>2</mn></msqrt></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><msqrt><mn>3</mn></msqrt><mo>-</mo><mn>1</mn></mrow><mrow><mn>2</mn><msqrt><mn>2</mn></msqrt></mrow></mfrac></math></p>"],
                    options_hi: ["<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>2</mn></msqrt><mo>-</mo><mn>1</mn></math></p>", "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>2</mn></msqrt><mo>+</mo><mn>1</mn></math></p>",
                                "<p><math display=\"inline\"><mfrac><mrow><msqrt><mn>3</mn></msqrt><mo>+</mo><mn>1</mn></mrow><mrow><mn>2</mn><msqrt><mn>2</mn></msqrt></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><msqrt><mn>3</mn></msqrt><mo>-</mo><mn>1</mn></mrow><mrow><mn>2</mn><msqrt><mn>2</mn></msqrt></mrow></mfrac></math></p>"],
                    solution_en: "<p>22.(a)&nbsp;Let h be the height of flag standing on the top of hemispherical dome of radius r and the flag is just visible from point O. <br><strong id=\"docs-internal-guid-5af2d67a-7fff-6f9f-0a56-1ea8ad3f1b80\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfPH90BrUBJS_fp0CC50UIb2iTdLgp1rRLUQjg3pcO6rIOfvOu9d5Y0eTwM_BcpK32Q6sUz9KJc_gCb-uaqVyChcjScrKYw7Dp5CFmyNKoiIxG07n0pQww32tLW_FkcQLA6lWszHI3Zvs_x-1aHF3SqMzza?key=7Eq3yv6nd6p1_hMEgz8-iw\" width=\"200\" height=\"113\"></strong><br>In <math display=\"inline\"><mi>&#916;</mi></math>CMB, we have :<br>tan45&deg; = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>CM</mi><mi>MB</mi></mfrac></math><br>1 = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>CM</mi><mi>MB</mi></mfrac></math> &rArr; MB = CM = r + h<br>Also, &ang;BCM = 180 - (90&deg; + 45&deg;) = 45&deg;.&nbsp;<br>Now, considering <math display=\"inline\"><mi>&#916;</mi></math>OCM and &Delta;OMB, we have <br>&ang;OMC = &ang;OMB = 45&deg;<br>Therefore, OM = OB = r and OM = OC = r<br>BC = OC + OB = r + r = 2r<br>Again, In <math display=\"inline\"><mi>&#916;</mi></math>CMB, we have :<br>MB&sup2; + CM&sup2; = BC&sup2;<br>(r + h)&sup2; + (r + h)&sup2; = (2r)&sup2;<br>2(r + h)&sup2; = 4r&sup2;<br>(r + h)&sup2; = 2r&sup2;<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><mfrac><mrow><mi mathvariant=\"normal\">r</mi><mo>+</mo><mi mathvariant=\"normal\">h</mi></mrow><mi mathvariant=\"normal\">r</mi></mfrac><msup><mo>)</mo><mn>2</mn></msup></math> = 2<br><math display=\"inline\"><mfrac><mrow><mi>r</mi><mo>+</mo><mi>h</mi></mrow><mrow><mi>r</mi></mrow></mfrac></math>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>2</mn></msqrt></math><br>1 + <math display=\"inline\"><mfrac><mrow><mi>h</mi></mrow><mrow><mi>r</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>2</mn></msqrt></math><br><math display=\"inline\"><mfrac><mrow><mi>h</mi></mrow><mrow><mi>r</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>2</mn></msqrt></math> - 1</p>",
                    solution_hi: "<p>22.(a)&nbsp;मान लीजिए r त्रिज्या के अर्धगोलाकार गुंबद के शीर्ष पर खड़े ध्वज की ऊंचाई h है और ध्वज बिंदु O से ठीक दिखाई देता है।<br><strong id=\"docs-internal-guid-5af2d67a-7fff-6f9f-0a56-1ea8ad3f1b80\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfPH90BrUBJS_fp0CC50UIb2iTdLgp1rRLUQjg3pcO6rIOfvOu9d5Y0eTwM_BcpK32Q6sUz9KJc_gCb-uaqVyChcjScrKYw7Dp5CFmyNKoiIxG07n0pQww32tLW_FkcQLA6lWszHI3Zvs_x-1aHF3SqMzza?key=7Eq3yv6nd6p1_hMEgz8-iw\" width=\"200\" height=\"113\"></strong><br><math display=\"inline\"><mi>&#916;</mi></math>CMB में, हमारे पास है:<br>tan45&deg; = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>CM</mi><mi>MB</mi></mfrac></math><br>1 = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>CM</mi><mi>MB</mi></mfrac></math> &rArr; MB = CM = r + h<br>साथ ही, &ang;BCM = 180 - (90&deg; + 45&deg;) = 45&deg;<br>अब, <math display=\"inline\"><mi>&#916;</mi></math>OCM और &Delta;OMB पर विचार करते हुए, हमारे पास है<br>&ang;OMC = &ang;OMB = 45&deg;<br>इसलिए, OM = OB = r और OM = OC = r<br>BC = OC + OB = r + r = 2r<br>दोबारा, <math display=\"inline\"><mi>&#916;</mi></math>CMB में, हमारे पास है:<br>MB&sup2; + CM&sup2; = BC&sup2;<br>(r + h)&sup2; + (r + h)&sup2; = (2r)&sup2;<br>2(r + h)&sup2; = 4r&sup2;<br>(r + h)&sup2; = 2r&sup2;<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><mfrac><mrow><mi mathvariant=\"normal\">r</mi><mo>+</mo><mi mathvariant=\"normal\">h</mi></mrow><mi mathvariant=\"normal\">r</mi></mfrac><msup><mo>)</mo><mn>2</mn></msup></math> = 2<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi mathvariant=\"normal\">r</mi><mo>+</mo><mi mathvariant=\"normal\">h</mi></mrow><mi mathvariant=\"normal\">r</mi></mfrac></math>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>2</mn></msqrt></math><br>1 + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"normal\">h</mi><mi mathvariant=\"normal\">r</mi></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>2</mn></msqrt></math><br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"normal\">h</mi><mi mathvariant=\"normal\">r</mi></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>2</mn></msqrt></math> - 1</p>",
                    correct: "a",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "23",
                    section: "misc",
                    question_en: "<p>23. A person can row at a speed of 11 km/h in still water. The speed of the stream is <math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>6</mn></mrow></mfrac></math> m/s. How much time will he take to row a distance of 36 km upstream?</p>",
                    question_hi: "<p>23. एक व्यक्ति स्थिर पानी में 11 km/h की गति से नाव चला सकता है। धारा की गति <math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>6</mn></mrow></mfrac></math> m/s है। धारा के प्रतिकूल 36 km की दूरी तय करने में उसे कितना समय लगेगा?</p>",
                    options_en: ["<p>4 hrs 15 min</p>", "<p>4 hrs 30 min</p>", 
                                "<p>4 hrs 20 min</p>", "<p>4 hrs 36 min</p>"],
                    options_hi: ["<p>4 घंटे 15 मिनट</p>", "<p>4 घंटे 30 मिनट</p>",
                                "<p>4 घंटे 20 मिनट</p>", "<p>4 घंटे 36 मिनट</p>"],
                    solution_en: "<p>23.(b)<br>Speed of stream = <math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>6</mn></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>18</mn><mn>5</mn></mfrac></math>km/h = 3 km/h<br>Rowing speed of man upstream = (11 - 3) km/h = 8 km/h<br>Required time = <math display=\"inline\"><mfrac><mrow><mn>36</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math> = 4<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math> hours = 4 hours 30 minutes.</p>",
                    solution_hi: "<p>23(b)<br>धारा की चाल = <math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>6</mn></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>18</mn><mn>5</mn></mfrac></math>km/h = 3 km/h<br>आदमी की धारा के विपरीत चाल = (11 - 3) km/h = 8 km/h<br>आवश्यक समय = <math display=\"inline\"><mfrac><mrow><mn>36</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math> = 4<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math> घंटे = 4 घंटे 30 मिनट .</p>",
                    correct: "b",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "24",
                    section: "misc",
                    question_en: "<p>24. A container has 30 litres of milk, from which 3 litres of milk is taken out and replaced with water. The process is done three times. What is the final ratio of the water and the milk in the container?</p>",
                    question_hi: "<p>24. एक पात्र में 30 लीटर दूध है, जिसमें से 3 लीटर दूध निकालकर पानी से बदल दिया जाता है। प्रक्रिया तीन बार दोहराई जाती है। पात्र में पानी और दूध का अंतिम अनुपात क्या है?</p>",
                    options_en: ["<p>729 : 271</p>", "<p>19 : 81</p>", 
                                "<p>81 : 19</p>", "<p>271 : 729</p>"],
                    options_hi: ["<p>729 : 271</p>", "<p>19 : 81</p>",
                                "<p>81 : 19</p>", "<p>271 : 729</p>"],
                    solution_en: "<p>24.(d)<br>Final quantity = Initial quantity(1 - <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"normal\">x</mi><mi mathvariant=\"normal\">c</mi></mfrac><msup><mo>)</mo><mi mathvariant=\"normal\">n</mi></msup></math><br>Here,<br>x = replaced liquid,&nbsp;<br>c = total liquid at beginning and <br>n = no of times the process is repeated.<br>Final quantity &rArr; 30 &times; (1 -&nbsp;<math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>30</mn></mrow></mfrac><msup><mrow><mo>)</mo></mrow><mrow><mn>3</mn></mrow></msup></math> = 30 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>729</mn><mn>1000</mn></mfrac></math> = 21.87<br>Final Milk = 21.87 litre and water = 30 - 21.87 = 8.13litre<br>Ratio of milk and water = 8.13 : 21.87&nbsp;= 271 : 729</p>",
                    solution_hi: "<p>24.(d)<br>अंतिम मात्रा = प्रारंभिक मात्रा (1 - <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"normal\">x</mi><mi mathvariant=\"normal\">c</mi></mfrac><msup><mo>)</mo><mi mathvariant=\"normal\">n</mi></msup></math><br>यहां,<br>x = प्रतिस्थापित तरल,<br>c = शुरुआत में कुल तरल और<br>n = प्रक्रिया के दोहराए जाने की संख्या<br>अंतिम मात्रा &rArr; 30 &times; (1 -&nbsp;<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>30</mn></mfrac><msup><mo>)</mo><mn>3</mn></msup></math> = 30 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>729</mn><mn>1000</mn></mfrac></math> = 21.87<br>अंतिम दूध = 21.87 लीटर और पानी = 30 - 21.87 = 8.13 लीटर<br>दूध और पानी का अनुपात = 8.13 : 21.87&nbsp;= 271 : 729</p>",
                    correct: "d",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "25",
                    section: "misc",
                    question_en: "<p>25. In a college union, there are 48 students. The ratio of the number of boys to the number of girls is 5 : 3. The number of girls to be added in the union, so that the number of boys to girls in 6 : 5 is</p>",
                    question_hi: "<p>25. एक कॉलेज यूनियन में विद्यार्थियों की संख्या 48 है, लड़कों और लड़कियों की संख्या का अनुपात 5 : 3 है | इस यूनियन में कितनी लड़कियों की संख्या जोड़ी जाए कि लड़के और लड़कियों का अनुपात 6 : 5 हो जाए|</p>",
                    options_en: ["<p>6</p>", "<p>7</p>", 
                                "<p>12</p>", "<p>17</p>"],
                    options_hi: ["<p>6</p>", "<p>7</p>",
                                "<p>12</p>", "<p>17</p>"],
                    solution_en: "<p>25.(b)&nbsp;Let number of boys = 5x<br>Number of girls = 3x<br>5x + 3x = 48<br>x = 6<br>So, there are 30 boys and 18 girls<br>Now, <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>30</mn><mrow><mn>18</mn><mo>+</mo><mi mathvariant=\"normal\">y</mi></mrow></mfrac><mo>=</mo><mfrac><mn>6</mn><mn>5</mn></mfrac></math><br>18 + y = 25<br>y = 7<br>So, 7 girls must be added.</p>",
                    solution_hi: "<p>25.(b)&nbsp;माना, लड़कों की संख्या = 5x<br>लड़कियों की संख्या = 3x<br>5x + 3x = 48<br>x = 6<br>तो, 30 लड़के और 18 लड़कियां हैं<br>अब, <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>30</mn><mrow><mn>18</mn><mo>+</mo><mi mathvariant=\"normal\">y</mi></mrow></mfrac><mo>=</mo><mfrac><mn>6</mn><mn>5</mn></mfrac></math><br>18 + y = 25<br>y = 7<br>तो, 7 लड़कियों को जोड़ा जाना चाहिए।</p>",
                    correct: "b",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "26",
                    section: "misc",
                    question_en: "<p>26. A and B start an enterprise together, with A as an active partner. A invests Rs 4000 and Rs 2000 more after 8 months. B invests Rs 5000 and withdraws Rs 2000 after 9 months. Being an active partner, A takes Rs 100 per month as allowance, from the profit. What is the share of B if the profit for the year is Rs 6700.</p>",
                    question_hi: "<p>26. A और B दोनों मिलकर एक उद्दम शुरू करते हैं, जिसमें A सक्रिय भागीदार के रूप में कार्य करता है| A इसमें 4000 रु निवेश करता है और 8 महीने बाद फिर 2000 रु निवेश करता है| B इसमें 5000 रु निवेश करता है लेकिन 9 महीने बाद इसमें से 2000 रु निकाल लेता है | सक्रिय भागीदार होने के नाते A लाभ में से भत्ते के रूप में प्रतिमाह 100 रु लेता है| यदि वर्ष में 6700 रु का लाभ हुआ है तो B का शेयर क्या होगा?</p>",
                    options_en: ["<p>Rs 3350</p>", "<p>Rs 3250</p>", 
                                "<p>Rs 2700</p>", "<p>Rs 2800</p>"],
                    options_hi: ["<p>3350 रु</p>", "<p>3250 रु</p>",
                                "<p>2700 रु</p>", "<p>2800 रु</p>"],
                    solution_en: "<p>26.(c)&nbsp;A invests Rs.4000 for 8 months and Rs.(4000 + 2000) = 6000 for the last 4 months<br>B invests Rs. 5000 for 9 months and Rs.(5000 - 2000) = Rs.3000 for the last 3 months<br>Ratio of effective investments of A and B for the whole year:-<br>(4000 &times; 8 + 6000 &times; 4) : (5000 &times; 9 + 3000 &times; 3)<br>= (32 + 24) : (45 + 9) = 28 : 27<br>A takes Rs.100 per month from the profit<br>Total allowance which A takes = 100 &times; 12 = 1200<br>Profit which will be shared = 6700 - 1200 = 5500<br>Share of B = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>27</mn><mrow><mn>28</mn><mo>+</mo><mn>27</mn></mrow></mfrac><mo>&#215;</mo><mn>5500</mn></math> = 2700</p>",
                    solution_hi: "<p>26.(c)&nbsp;<br>A 8 महीने के लिए 4000 रुपये और अगले 4 महीनों के लिए (4000 + 2000) = 6000 रुपये का निवेश करता है|<br>B 9 महीने के लिए 5000 रुपये और अगले 3 महीनों के लिए (5000 - 2000) = 3000 रुपये का निवेश करता है|<br>पूरे वर्ष के लिए A और B के प्रभावी निवेश का अनुपात:-<br>(4000 &times; 8 + 6000 &times; 4) : (5000 &times; 9 + 3000 &times; 3) <br>= (32 + 24) : (45 + 9) = 28 : 27<br>A लाभ से प्रति माह 100 रुपये लेता है<br>कुल भत्ता जो A लेता है = 100 &times; 12 = 1200<br>लाभ जो साझा किया जाएगा = 6700 - 1200 = 5500<br>B का हिस्सा = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>27</mn><mrow><mn>28</mn><mo>+</mo><mn>27</mn></mrow></mfrac><mo>&#215;</mo><mn>5500</mn></math>&nbsp;= 2700</p>",
                    correct: "c",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "27",
                    section: "misc",
                    question_en: "<p>27. A shopkeeper buys a product of Rs 150 per Kg. 15% of product was damaged. At what price (per Kg) should he sell the remaining so as to earn a profit of 20%?</p>",
                    question_hi: "<p>27. एक दुकानदार 150 रु प्रति किलो ग्राम की दर से एक वस्तु खरीदता है | उसमें से 15% वस्तु नष्ट हो गई | 20% लाभ प्राप्त करने के लिए उसे शेष वस्तु किस कीमत (प्रति किलो ग्राम) पर बेचनी चाहिए?</p>",
                    options_en: ["<p>Rs.<math display=\"inline\"><mn>211</mn><mfrac><mrow><mn>3</mn></mrow><mrow><mn>17</mn></mrow></mfrac></math></p>", "<p>Rs.<math display=\"inline\"><mn>207</mn><mfrac><mrow><mn>13</mn></mrow><mrow><mn>17</mn></mrow></mfrac></math></p>", 
                                "<p>Rs.<math display=\"inline\"><mn>207</mn><mfrac><mrow><mn>3</mn></mrow><mrow><mn>17</mn></mrow></mfrac></math></p>", "<p>Rs.<math display=\"inline\"><mn>211</mn><mfrac><mrow><mn>13</mn></mrow><mrow><mn>17</mn></mrow></mfrac></math></p>"],
                    options_hi: ["<p>Rs.<math display=\"inline\"><mn>211</mn><mfrac><mrow><mn>3</mn></mrow><mrow><mn>17</mn></mrow></mfrac></math></p>", "<p>Rs.<math display=\"inline\"><mn>207</mn><mfrac><mrow><mn>13</mn></mrow><mrow><mn>17</mn></mrow></mfrac></math></p>",
                                "<p>Rs.<math display=\"inline\"><mn>207</mn><mfrac><mrow><mn>3</mn></mrow><mrow><mn>17</mn></mrow></mfrac></math></p>", "<p>Rs.<math display=\"inline\"><mn>211</mn><mfrac><mrow><mn>13</mn></mrow><mrow><mn>17</mn></mrow></mfrac></math></p>"],
                    solution_en: "<p>27.(d)&nbsp;Let the amount of product bought by shopkeeper = 1 kg<br>Amount of product left after 15% is damaged = <math display=\"inline\"><mfrac><mrow><mn>85</mn></mrow><mrow><mn>100</mn></mrow></mfrac><mo>&#215;</mo><mn>1</mn></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>85</mn><mn>100</mn></mfrac></math>kg<br>To earn 20% profit the product should be sold at = <math display=\"inline\"><mfrac><mrow><mn>120</mn></mrow><mrow><mn>100</mn></mrow></mfrac><mo>&#215;</mo><mn>150</mn></math> = 180<br>Price of the remaining amount of product (per kg) = <math display=\"inline\"><mfrac><mrow><mn>180</mn></mrow><mrow><mfrac><mrow><mn>85</mn></mrow><mrow><mn>100</mn></mrow></mfrac></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3600</mn><mn>17</mn></mfrac><mo>=</mo><mn>211</mn><mfrac><mn>13</mn><mn>17</mn></mfrac></math></p>",
                    solution_hi: "<p>27.(d)&nbsp;माना, दुकानदार द्वारा खरीदे गए उत्पाद की मात्रा = 1 किग्रा<br>15% क्षतिग्रस्त होने के बाद बचे उत्पाद की मात्रा = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>85</mn><mn>100</mn></mfrac><mo>&#215;</mo><mn>1</mn></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>85</mn><mn>100</mn></mfrac></math>किग्रा<br>20% लाभ अर्जित करने के लिए उत्पाद को जिस मूल्य पर बेचा जाना चाहिए |<br>= <math display=\"inline\"><mfrac><mrow><mn>120</mn></mrow><mrow><mn>100</mn></mrow></mfrac><mo>&#215;</mo><mn>150</mn></math> = 180 <br>उत्पाद की शेष मात्रा का मूल्य (प्रति किग्रा) = <math display=\"inline\"><mfrac><mrow><mn>180</mn></mrow><mrow><mfrac><mrow><mn>85</mn></mrow><mrow><mn>100</mn></mrow></mfrac></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3600</mn><mn>17</mn></mfrac><mo>=</mo><mn>211</mn><mfrac><mn>13</mn><mn>17</mn></mfrac></math></p>",
                    correct: "d",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "28",
                    section: "misc",
                    question_en: "<p>28. If&nbsp;<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><mi mathvariant=\"normal\">x</mi><mo>-</mo><mfrac><mn>1</mn><mi mathvariant=\"normal\">x</mi></mfrac><msup><mo>)</mo><mn>2</mn></msup><mo>=</mo><mn>3</mn></math>, then the value of <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#160;</mo><msup><mi mathvariant=\"normal\">x</mi><mn>6</mn></msup><mo>+</mo><mfrac><mn>1</mn><msup><mi mathvariant=\"normal\">x</mi><mn>6</mn></msup></mfrac></math> equals to</p>",
                    question_hi: "<p>28. यदि&nbsp;<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><mi mathvariant=\"normal\">x</mi><mo>-</mo><mfrac><mn>1</mn><mi mathvariant=\"normal\">x</mi></mfrac><msup><mo>)</mo><mn>2</mn></msup><mo>=</mo><mn>3</mn></math>, तो <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#160;</mo><msup><mi mathvariant=\"normal\">x</mi><mn>6</mn></msup><mo>+</mo><mfrac><mn>1</mn><msup><mi mathvariant=\"normal\">x</mi><mn>6</mn></msup></mfrac></math> का मान निकालें |</p>",
                    options_en: ["<p>90</p>", "<p>100</p>", 
                                "<p>110</p>", "<p>120</p>"],
                    options_hi: ["<p>90</p>", "<p>100</p>",
                                "<p>110</p>", "<p>120</p>"],
                    solution_en: "<p>28.(c)<br>(<math display=\"inline\"><mi>x</mi><mo>-</mo><mfrac><mrow><mn>1</mn></mrow><mrow><mi>x</mi></mrow></mfrac><msup><mrow><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup><mo>=</mo><mn>3</mn></math><br><math display=\"inline\"><msup><mrow><mi>x</mi></mrow><mrow><mn>2</mn></mrow></msup><mo>+</mo><mfrac><mrow><mn>1</mn></mrow><mrow><msup><mrow><mi>x</mi></mrow><mrow><mn>2</mn></mrow></msup></mrow></mfrac><mo>-</mo><mn>2</mn><mo>=</mo><mn>3</mn></math><br><math display=\"inline\"><msup><mrow><mi>x</mi></mrow><mrow><mn>2</mn></mrow></msup><mo>+</mo><mfrac><mrow><mn>1</mn></mrow><mrow><msup><mrow><mi>x</mi></mrow><mrow><mn>2</mn></mrow></msup></mrow></mfrac><mo>=</mo><mn>5</mn></math><br><math display=\"inline\"><msup><mrow><mo>(</mo><mi>x</mi></mrow><mrow><mn>2</mn></mrow></msup><mo>+</mo><mfrac><mrow><mn>1</mn></mrow><mrow><msup><mrow><mi>x</mi></mrow><mrow><mn>2</mn></mrow></msup></mrow></mfrac><msup><mrow><mo>)</mo></mrow><mrow><mn>3</mn></mrow></msup><mo>=</mo><msup><mrow><mi>x</mi></mrow><mrow><mn>6</mn></mrow></msup><mo>+</mo><mfrac><mrow><mn>1</mn></mrow><mrow><msup><mrow><mi>x</mi></mrow><mrow><mn>6</mn></mrow></msup></mrow></mfrac><mo>+</mo><mn>3</mn><msup><mrow><mi>x</mi></mrow><mrow><mn>2</mn></mrow></msup><mo>&#215;</mo><mfrac><mrow><mn>1</mn></mrow><mrow><msup><mrow><mi>x</mi></mrow><mrow><mn>2</mn></mrow></msup></mrow></mfrac><mo>(</mo><msup><mrow><mi>x</mi></mrow><mrow><mn>2</mn></mrow></msup><mo>+</mo><mfrac><mrow><mn>1</mn></mrow><mrow><msup><mrow><mi>x</mi></mrow><mrow><mn>2</mn></mrow></msup></mrow></mfrac><mo>)</mo></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>5</mn><mn>3</mn></msup><mo>=</mo><msup><mi mathvariant=\"normal\">x</mi><mn>6</mn></msup><mo>+</mo><mo>&#160;</mo><mfrac><mn>1</mn><msup><mi mathvariant=\"normal\">x</mi><mn>6</mn></msup></mfrac><mo>+</mo><mn>3</mn><mo>(</mo><mn>5</mn><mo>)</mo><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">x</mi><mn>6</mn></msup><mo>+</mo><mfrac><mn>1</mn><msup><mi mathvariant=\"normal\">x</mi><mn>6</mn></msup></mfrac><mo>+</mo><mn>15</mn></math><br>125 - 15 = 110</p>",
                    solution_hi: "<p>28.(c)<br>(<math display=\"inline\"><mi>x</mi><mo>-</mo><mfrac><mrow><mn>1</mn></mrow><mrow><mi>x</mi></mrow></mfrac><msup><mrow><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup><mo>=</mo><mn>3</mn></math><br><math display=\"inline\"><msup><mrow><mi>x</mi></mrow><mrow><mn>2</mn></mrow></msup><mo>+</mo><mfrac><mrow><mn>1</mn></mrow><mrow><msup><mrow><mi>x</mi></mrow><mrow><mn>2</mn></mrow></msup></mrow></mfrac><mo>-</mo><mn>2</mn><mo>=</mo><mn>3</mn></math><br><math display=\"inline\"><msup><mrow><mi>x</mi></mrow><mrow><mn>2</mn></mrow></msup><mo>+</mo><mfrac><mrow><mn>1</mn></mrow><mrow><msup><mrow><mi>x</mi></mrow><mrow><mn>2</mn></mrow></msup></mrow></mfrac><mo>=</mo><mn>5</mn></math><br><math display=\"inline\"><msup><mrow><mo>(</mo><mi>x</mi></mrow><mrow><mn>2</mn></mrow></msup><mo>+</mo><mfrac><mrow><mn>1</mn></mrow><mrow><msup><mrow><mi>x</mi></mrow><mrow><mn>2</mn></mrow></msup></mrow></mfrac><msup><mrow><mo>)</mo></mrow><mrow><mn>3</mn></mrow></msup><mo>=</mo><msup><mrow><mi>x</mi></mrow><mrow><mn>6</mn></mrow></msup><mo>+</mo><mfrac><mrow><mn>1</mn></mrow><mrow><msup><mrow><mi>x</mi></mrow><mrow><mn>6</mn></mrow></msup></mrow></mfrac><mo>+</mo><mn>3</mn><msup><mrow><mi>x</mi></mrow><mrow><mn>2</mn></mrow></msup><mo>&#215;</mo><mfrac><mrow><mn>1</mn></mrow><mrow><msup><mrow><mi>x</mi></mrow><mrow><mn>2</mn></mrow></msup></mrow></mfrac><mo>(</mo><msup><mrow><mi>x</mi></mrow><mrow><mn>2</mn></mrow></msup><mo>+</mo><mfrac><mrow><mn>1</mn></mrow><mrow><msup><mrow><mi>x</mi></mrow><mrow><mn>2</mn></mrow></msup></mrow></mfrac><mo>)</mo></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>5</mn><mn>3</mn></msup><mo>=</mo><msup><mi mathvariant=\"normal\">x</mi><mn>6</mn></msup><mo>+</mo><mfrac><mn>1</mn><msup><mi mathvariant=\"normal\">x</mi><mn>6</mn></msup></mfrac><mo>+</mo><mn>3</mn><mo>(</mo><mn>5</mn><mo>)</mo><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">x</mi><mn>6</mn></msup><mo>+</mo><mfrac><mn>1</mn><msup><mi mathvariant=\"normal\">x</mi><mn>6</mn></msup></mfrac><mo>+</mo><mn>15</mn></math><br>125 - 15 = 110</p>",
                    correct: "c",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "29",
                    section: "misc",
                    question_en: "<p>29. The following pie chart shows the percentage quantity of fruits as two fruit shops A and B.<br><strong id=\"docs-internal-guid-f7fa91c4-7fff-e2f8-ec82-81d129b55a06\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdsO0B_Nn7B9Nq31_ta1BiqnU0WV-i6BrCwwvLPZMIKYSFXkxVB7K-GB7Gg4eZ7izEWTn6acvQ-P4zH8d1Z5Ex6klB5Iv8LR8797oHT0tqoP5nGHweOSXgKsdhjkBMDLT-gWP5joP0IHiZT0v6srRmeARk?key=7Eq3yv6nd6p1_hMEgz8-iw\" width=\"175\" height=\"217\"></strong><strong id=\"docs-internal-guid-70b7de9e-7fff-ef88-2357-24422b2a2f52\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXce0iN4e78YW75z5jZ1q7vs3anFEqd5F5w4oMEcpda-k0G6m_D7U7jHPkL6QhNUfw3xOCKEdIFLpvPqGiI9Y2sEr0EEhLnWrq9Lyqgox6gCHRK6UAdC5gHQnWttPL8XlCD8Bm6L7G4G--EUX0BhGjNHqEk0?key=7Eq3yv6nd6p1_hMEgz8-iw\" width=\"180\" height=\"202\"></strong><br>If the price of apples is Rs.50 per kg and that of oranges is Rs.25 per kg, then what is the ratio of their costs at shop A?</p>",
                    question_hi: "<p>29. निम्नलिखित पाई चार्ट दो फलों की दुकानों A और B के रूप में फलों की प्रतिशत मात्रा को दर्शाता है।<br><br><strong id=\"docs-internal-guid-f7fa91c4-7fff-e2f8-ec82-81d129b55a06\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdsO0B_Nn7B9Nq31_ta1BiqnU0WV-i6BrCwwvLPZMIKYSFXkxVB7K-GB7Gg4eZ7izEWTn6acvQ-P4zH8d1Z5Ex6klB5Iv8LR8797oHT0tqoP5nGHweOSXgKsdhjkBMDLT-gWP5joP0IHiZT0v6srRmeARk?key=7Eq3yv6nd6p1_hMEgz8-iw\" width=\"175\" height=\"217\"></strong><strong id=\"docs-internal-guid-70b7de9e-7fff-ef88-2357-24422b2a2f52\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXce0iN4e78YW75z5jZ1q7vs3anFEqd5F5w4oMEcpda-k0G6m_D7U7jHPkL6QhNUfw3xOCKEdIFLpvPqGiI9Y2sEr0EEhLnWrq9Lyqgox6gCHRK6UAdC5gHQnWttPL8XlCD8Bm6L7G4G--EUX0BhGjNHqEk0?key=7Eq3yv6nd6p1_hMEgz8-iw\" width=\"180\" height=\"202\"></strong><br>यदि सेब की कीमत 50 रुपये प्रति kg है और संतरे की कीमत 25 रुपये प्रति kg है, तो दुकान A पर उनकी लागत का अनुपात क्या है?</p>",
                    options_en: ["<p>5 : 4</p>", "<p>7 : 5</p>", 
                                "<p>4 : 3</p>", "<p>7 : 6</p>"],
                    options_hi: ["<p>5 : 4</p>", "<p>7 : 5</p>",
                                "<p>4 : 3</p>", "<p>7 : 6</p>"],
                    solution_en: "<p>29.(c)&nbsp;Given that, <br>In shop A, Total quantity = 1400 kg<br>So, The number of apples = 1400 &times; 16% = 14 &times; 16<br>The cost of (14 &times; 16) apples = 14 &times; 16 &times; 50<br>Now, The number of oranges = 1400 &times; 24% = 14 &times; 24<br>The cost of the oranges = 14 &times; 24 &times; 25<br>So, The required ratio = 14 &times; 16 &times; 50 : 14 &times; 24 &times; 25 = 4 : 3</p>",
                    solution_hi: "<p>29.(c)&nbsp;दुकान A में, कुल मात्रा = 1400 kg (दिया गया है )<br>अतः सेबों की संख्या = 1400 &times; 16% = 14 &times; 16 <br>(14 &times; 16) सेब का मूल्य = 14 &times; 16 &times; 50<br>अब, संतरों की संख्या = 1400 &times; 24% = 14 &times; 24<br>संतरों का मूल्य = 14 &times; 24 &times; 25<br>अतः अभीष्ट अनुपात = 14 &times; 16 &times; 50 : 14 &times; 24 &times; 25 = 4: 3</p>",
                    correct: "c",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "30",
                    section: "misc",
                    question_en: "<p>30. Given that A and B are second quadrant angles. sin A = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> and sin B = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>5</mn></mfrac></math>, then find the value of cos(A - B).</p>",
                    question_hi: "<p>30. दिया गया है कि A और B दूसरे चतुर्थांश कोण हैं| sin A = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> और sin B = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>5</mn></mfrac></math>, तो cos (A - B) का मान ज्ञात कीजिए।</p>",
                    options_en: ["<p><math display=\"inline\"><mfrac><mrow><mn>4</mn><msqrt><mn>3</mn></msqrt><mo>+</mo><mn>1</mn></mrow><mrow><mn>15</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>8</mn><msqrt><mn>3</mn></msqrt><mo>-</mo><mn>1</mn></mrow><mrow><mn>15</mn></mrow></mfrac></math></p>", 
                                "<p><math display=\"inline\"><mfrac><mrow><mn>8</mn><msqrt><mn>3</mn></msqrt><mo>+</mo><mn>1</mn></mrow><mrow><mn>15</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>4</mn><msqrt><mn>3</mn></msqrt><mo>-</mo><mn>1</mn></mrow><mrow><mn>15</mn></mrow></mfrac></math></p>"],
                    options_hi: ["<p><math display=\"inline\"><mfrac><mrow><mn>4</mn><msqrt><mn>3</mn></msqrt><mo>+</mo><mn>1</mn></mrow><mrow><mn>15</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>8</mn><msqrt><mn>3</mn></msqrt><mo>-</mo><mn>1</mn></mrow><mrow><mn>15</mn></mrow></mfrac></math></p>",
                                "<p><math display=\"inline\"><mfrac><mrow><mn>8</mn><msqrt><mn>3</mn></msqrt><mo>+</mo><mn>1</mn></mrow><mrow><mn>15</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>4</mn><msqrt><mn>3</mn></msqrt><mo>-</mo><mn>1</mn></mrow><mrow><mn>15</mn></mrow></mfrac></math></p>"],
                    solution_en: "<p>30.(c)&nbsp;As we know that,<br>cos&sup2;&theta; + sin&sup2;&theta; = 1<br>Then, cosA = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>1</mn><mo>-</mo><msup><mrow><mo>(</mo><mfrac><mn>1</mn><mn>3</mn></mfrac><mo>)</mo></mrow><mn>2</mn></msup></msqrt><mo>=</mo><msqrt><mfrac><mn>8</mn><mn>9</mn></mfrac></msqrt><mo>=</mo><mo>-</mo><mfrac><mrow><mn>2</mn><msqrt><mn>2</mn></msqrt></mrow><mn>3</mn></mfrac></math> and cosB = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>1</mn><mo>-</mo><msup><mrow><mo>(</mo><mfrac><mn>1</mn><mn>5</mn></mfrac><mo>)</mo></mrow><mn>2</mn></msup></msqrt><mo>=</mo><msqrt><mfrac><mn>24</mn><mn>25</mn></mfrac></msqrt><mo>=</mo><mo>-</mo><mfrac><mrow><mn>2</mn><msqrt><mn>6</mn></msqrt></mrow><mn>5</mn></mfrac></math> (∵ A and B are in second quadrant)<br>cos(A - B) = cosA.cosB + sinAsinB = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><mo>-</mo><mfrac><mrow><mn>2</mn><msqrt><mn>2</mn></msqrt></mrow><mn>3</mn></mfrac><mo>)</mo><mo>&#215;</mo><mo>(</mo><mo>-</mo><mfrac><mrow><mn>2</mn><msqrt><mn>6</mn></msqrt></mrow><mn>5</mn></mfrac><mo>)</mo><mo>+</mo><mfrac><mn>1</mn><mn>3</mn></mfrac><mo>&#215;</mo><mfrac><mn>1</mn><mn>5</mn></mfrac><mo>=</mo><mfrac><mrow><mn>8</mn><msqrt><mn>3</mn></msqrt><mo>+</mo><mn>1</mn></mrow><mn>15</mn></mfrac></math></p>",
                    solution_hi: "<p>30.(c)&nbsp;जैसा कि हम जानते हैं कि,<br>cos&sup2;&theta; + sin&sup2;&theta; = 1<br>फिर, cosA = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>1</mn><mo>-</mo><msup><mrow><mo>(</mo><mfrac><mn>1</mn><mn>3</mn></mfrac><mo>)</mo></mrow><mn>2</mn></msup></msqrt><mo>=</mo><msqrt><mfrac><mn>8</mn><mn>9</mn></mfrac></msqrt><mo>=</mo><mo>-</mo><mfrac><mrow><mn>2</mn><msqrt><mn>2</mn></msqrt></mrow><mn>3</mn></mfrac></math> और cosB = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>1</mn><mo>-</mo><msup><mrow><mo>(</mo><mfrac><mn>1</mn><mn>5</mn></mfrac><mo>)</mo></mrow><mn>2</mn></msup></msqrt><mo>=</mo><msqrt><mfrac><mn>24</mn><mn>25</mn></mfrac></msqrt><mo>=</mo><mo>-</mo><mfrac><mrow><mn>2</mn><msqrt><mn>6</mn></msqrt></mrow><mn>5</mn></mfrac></math>(∵ A और B दूसरे चतुर्थांश में हैं)<br>cos(A - B) = cosA.cosB + sinAsinB = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><mo>-</mo><mfrac><mrow><mn>2</mn><msqrt><mn>2</mn></msqrt></mrow><mn>3</mn></mfrac><mo>)</mo><mo>&#215;</mo><mo>(</mo><mo>-</mo><mfrac><mrow><mn>2</mn><msqrt><mn>6</mn></msqrt></mrow><mn>5</mn></mfrac><mo>)</mo><mo>+</mo><mfrac><mn>1</mn><mn>3</mn></mfrac><mo>&#215;</mo><mfrac><mn>1</mn><mn>5</mn></mfrac><mo>=</mo><mfrac><mrow><mn>8</mn><msqrt><mn>3</mn></msqrt><mo>+</mo><mn>1</mn></mrow><mn>15</mn></mfrac></math></p>",
                    correct: "c",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "31",
                    section: "misc",
                    question_en: "<p>31. In a certain code language, \'GASTRIC\' is coded as 7119201833 and &lsquo;FREEDOM&rsquo; is&nbsp;coded as 618224413. How will \'BOARDING\' be coded in that language?</p>",
                    question_hi: "<p>31. एक निश्चित कूट भाषा में, \'GASTRIC\' को 7119201833 और \'FREEDOM\' को 618224413 के रूप में कोडित किया गया है। उस भाषा में \'BOARDING\' को कैसे कोडित किया जाएगा?</p>",
                    options_en: ["<p>2411849137</p>", "<p>2411843147</p>", 
                                "<p>2411744147</p>", "<p>2611943147</p>"],
                    options_hi: ["<p>2411849137</p>", "<p>2411843147</p>",
                                "<p>2411744147</p>", "<p>2611943147</p>"],
                    solution_en: "<p>31.(b) <strong>Logic : </strong>Each consonant is coded as its place value in alphabetical series while vowels A, E, I, O, U is coded as 1, 2, 3, 4 and 5 respectively.<br>GASTRIC is coded as 7119201833 : G &rarr;&nbsp;7, A &rarr; 1, S &rarr; 19, T &rarr; 20, R &rarr; 18, I &rarr; 3, C &rarr; 3<br>Similarly, for BOARDING : B <math display=\"inline\"><mo>&#8594;</mo></math> 2, O &rarr; 4, A &rarr; 1, R &rarr; 18, D &rarr; 4, I &rarr; 3, N &rarr; 14, G &rarr; 7</p>",
                    solution_hi: "<p>31.(b) <strong>तर्क: </strong>प्रत्येक व्यंजन को वर्णानुक्रम में उसके स्थानीय मान के रूप में कोडित किया जाता है जबकि स्वर A, E, I, O, U को क्रमशः 1, 2, 3, 4 और 5 के रूप में कोडित किया जाता है।<br>GASTRIC को 7119201833 के रूप में कोडित किया गया है: G &rarr;&nbsp;7, A &rarr; 1, S &rarr; 19, T &rarr; 20, R &rarr; 18, I &rarr; 3, C &rarr; 3<br>इसी तरह, BOARDING के लिए : B <math display=\"inline\"><mo>&#8594;</mo></math> 2, O &rarr; 4, A &rarr; 1, R &rarr; 18, D &rarr; 4, I &rarr; 3, N &rarr; 14, G &rarr; 7</p>",
                    correct: "b",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "32",
                    section: "misc",
                    question_en: "<p>32.Select the correct alternative to indicate the arrangement of the following words in a logical and meaningful order.<br>1. Probation <br>2. Selection <br>3. Application <br>4. Confirmation. <br>5. Appointment</p>",
                    question_hi: "<p>32. सही विकल्प का चयन करें जो निम्नलिखित शब्दों के एक तर्कपूर्ण एवं अर्थपूर्ण क्रम को दर्शाता है । <br>1. परिवीक्षा <br>2. चयन <br>3. आवेदन <br>4. स्थायीकरण <br>5. नियुक्ति</p>",
                    options_en: ["<p>3,2,5,1,4</p>", "<p>5,1,4,2,3</p>", 
                                "<p>4,1,3,2,5</p>", "<p>3,2,5,4,1</p>"],
                    options_hi: ["<p>3,2,5,1,4</p>", "<p>5,1,4,2,3</p>",
                                "<p>4,1,3,2,5</p>", "<p>3,2,5,4,1</p>"],
                    solution_en: "<p>32.(a) The correct and meaningful order of the question is:<br>(3) Application <math display=\"inline\"><mo>&#8658;</mo></math> (2) Selection &rArr; (5) Appointment &rArr; (1) Probation &rArr; (4) Confirmation<br>So, option A is the correct answer.</p>",
                    solution_hi: "<p>32.(a) प्रश्न का सही और सार्थक क्रम है: <br>(3) आवेदन <math display=\"inline\"><mo>&#8658;</mo></math> (2) चयन &rArr; (5) नियुक्ति &rArr; (1) परिवीक्षा &rArr; (4) पुष्टि<br>अत: विकल्प A सही उत्तर है।</p>",
                    correct: "a",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "33",
                    section: "misc",
                    question_en: "<p>33. Three of the following four letter-clusters are alike in a certain way and one is different. Find the odd one out.</p>",
                    question_hi: "<p>33. चार अक्षरों के निम्नलिखित चार में से तीन समूह किसी निश्चित प्रकार से एक जैसे हैं तथा एक अलग है । अलग समूह की पहचान करें।</p>",
                    options_en: ["<p>PYRU</p>", "<p>CLEH</p>", 
                                "<p>MKOH</p>", "<p>TRVN</p>"],
                    options_hi: ["<p>PYRU</p>", "<p>CLEH</p>",
                                "<p>MKOH</p>", "<p>TRVN</p>"],
                    solution_en: "<p>33.(c) The gap between the first and the third letter is 2 &amp; the gap between the second and the fourth letter is 4. <br>But in option C, MKOH( in M and O gap is 2 but in H and K is 3)</p>",
                    solution_hi: "<p>33.(c) पहले और तीसरे अक्षर के बीच का अंतर 2 है और दूसरे और चौथे अक्षर के बीच का अंतर 4 है।<br>लेकिन विकल्प C में, MKOH (M और O में अंतर 2 है लेकिन H और K में 3 है)।</p>",
                    correct: "c",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "34",
                    section: "misc",
                    question_en: "<p>34. How many triangles are there in the following figure?<br><strong id=\"docs-internal-guid-cc4b9a49-7fff-88ec-1a19-74776be6b4e9\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcgPZ1QXLlVXBsRoTrhxNh6XsDbhQ9jzs8pYGygwWeJQ1ko-4fSTddZzNCagzlKRWOYJDJ6DxAvYrKKhazG1IrhIs5pYEM9Y9m8BNBINDR4MSrbQNHZhQUnnwJH08RnGxfapvjxCtGCw94B0HjmGMzSpAph?key=7Eq3yv6nd6p1_hMEgz8-iw\" width=\"120\" height=\"73\"></strong></p>",
                    question_hi: "<p>34. निम्नलिखित आकृति में कितने त्रिभुज हैं ? <br><strong id=\"docs-internal-guid-cc4b9a49-7fff-88ec-1a19-74776be6b4e9\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcgPZ1QXLlVXBsRoTrhxNh6XsDbhQ9jzs8pYGygwWeJQ1ko-4fSTddZzNCagzlKRWOYJDJ6DxAvYrKKhazG1IrhIs5pYEM9Y9m8BNBINDR4MSrbQNHZhQUnnwJH08RnGxfapvjxCtGCw94B0HjmGMzSpAph?key=7Eq3yv6nd6p1_hMEgz8-iw\" width=\"120\" height=\"73\"></strong></p>",
                    options_en: ["<p>35</p>", "<p>36</p>", 
                                "<p>34</p>", "<p>30</p>"],
                    options_hi: ["<p>35</p>", "<p>36</p>",
                                "<p>34</p>", "<p>30</p>"],
                    solution_en: "<p>34 (a) Here, the number of triangles are 35.<br><strong id=\"docs-internal-guid-214d3da9-7fff-2852-7b77-24caddc21735\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdsJJ3kn_MiSGsR6nP1G4rXIq6O1QP-gGSyg6IN9EULq1r2IFPRoUjcnRO149vyolAAEYbD1Nsm6r3ygfHf1J0d4bSkc67VRtnPrdnbvOa3wf7N8nBucXo8CZK7bzI3-SirHg3qFOuhlfTAVhlTPIHnAhW0?key=7Eq3yv6nd6p1_hMEgz8-iw\" width=\"160\" height=\"108\"></strong><br>Triangles in rectangle AILE(8): AJI, IJL, LJE, AJE, AIL, ILE, LEA, AEI<br>Triangles in rectangle IDGL (4): ILK, LKG, ILG, IDG <br>Triangles in rectangle EFGH (7):ELM, EMF, LMG, EMG, EFG, FMN, FGH<br>Triangles in rectangle FBCH (4): FCB, FNC, NHC, FHC<br>AME, AMF, ANF, ACF, ACB, CGM, CGL, CDA, CGF, CFM, GIE, EJM,</p>",
                    solution_hi: "<p>34 (a) यहाँ त्रिभुजों की संख्या 35 है।<br><strong id=\"docs-internal-guid-214d3da9-7fff-2852-7b77-24caddc21735\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdsJJ3kn_MiSGsR6nP1G4rXIq6O1QP-gGSyg6IN9EULq1r2IFPRoUjcnRO149vyolAAEYbD1Nsm6r3ygfHf1J0d4bSkc67VRtnPrdnbvOa3wf7N8nBucXo8CZK7bzI3-SirHg3qFOuhlfTAVhlTPIHnAhW0?key=7Eq3yv6nd6p1_hMEgz8-iw\" width=\"160\" height=\"108\"></strong><br>आयत AILE में त्रिभुज (8): AJI, IJL, LJE, AJE, AIL, ILE, LEA, AEI<br>आयत IDGL में त्रिभुज (4): ILK, LKG, ILG, IDG <br>आयत EFGH में त्रिभुज (7):ELM, EMF, LMG, EMG, EFG, FMN, FGH<br>आयत FBCH में त्रिभुज (4): FCB, FNC, NHC, FHC<br>AME, AMF, ANF, ACF, ACB, CGM, CGL, CDA, CGF, CFM, GIE, EJM,</p>",
                    correct: "a",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "35",
                    section: "misc",
                    question_en: "<p>35. Read the given statements and conclusions carefully. Assuming that the information given<br>in the statements is true, even if it appears to be at variance with commonly known facts,<br>decide which of the given conclusions logically follow from the statements.<br><strong>Statements:</strong><br>A. All pots are coals.<br>B. All coals are rounds.<br>C. All rounds are stones.<br><strong>Conclusions:</strong><br>I. All pots are stones.<br>II. All stones are coals.<br>III. All rounds are pots.<br>IV .All coals are stones.</p>",
                    question_hi: "<p>35. दिए गए कथनों और निष्कर्षों को ध्यानपूर्वक पढ़ें। यह मानते हुए कि कथनों में दी गई जानकारी सत्य है, भले ही वह सामान्यतः ज्ञात तथ्यों से भिन्न प्रतीत होती हो, बताइये कि दिए गए निष्कर्षों में से कौन सा कथनों का तार्किक रूप से अनुसरण करता है।<br><strong>कथन:</strong><br>A. सभी बर्तन कोयले हैं।<br>B. सभी कोयले गोल हैं।<br>C. सभी गोल स्टोन हैं।<br><strong>निष्कर्ष:</strong><br>I. सभी बर्तन पत्थर हैं।<br>II. सभी पत्थर कोयले हैं।<br>III. सभी गोल बर्तन हैं।<br>IV. सभी कोयले पत्थर हैं।</p>",
                    options_en: ["<p>Only conclusions I and IV follow</p>", "<p>Only conclusions II and III follow</p>", 
                                "<p>All the conclusions follow</p>", "<p>Only conclusions I and II follow</p>"],
                    options_hi: ["<p>केवल निष्कर्ष I और IV अनुसरण करते हैं</p>", "<p>केवल निष्कर्ष II और III अनुसरण करते हैं</p>",
                                "<p>सभी निष्कर्ष अनुसरण करते हैं</p>", "<p>केवल निष्कर्ष I और II अनुसरण करते हैं</p>"],
                    solution_en: "<p>35.(a) <br><strong id=\"docs-internal-guid-820733d7-7fff-cbb2-4ab9-77783faac603\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfY16Oxpvo3C_9LcweeRPqL09YSybF7E2jNVOtFWGhNEZvVSKoa7i3N-hYAywwkI6O0T6OauVAnWLEnCe_cSQjvL9CenB43kctAYyFuDUUTPabxSj99T3j9XpNovWNWto6mIKvnxbynWQ2ZHcPhWa0KvN4?key=7Eq3yv6nd6p1_hMEgz8-iw\" width=\"150\" height=\"147\"></strong><br>Only Conclusion I and IV follows.</p>",
                    solution_hi: "<p>35.(a) <br><img src=\"https://chat.google.com/u/0/api/get_attachment_url?url_type=FIFE_URL&amp;content_type=image%2Fpng&amp;attachment_token=AOo0EEVyZtQjbRt%2BfuKYYx%2FW6oZuozfaC%2Ff2Qzq1L04ktZfyYIxwqooKSQB%2B0vlUbIjg1jByHdqIHVty9MSbL5Xkc9xl%2B3usxpAfGUeNVWgd5c92IvqbbbE02sN9%2FzJZZImBZzDwOREC44VU5BM%2FOG24sluOL1qxfU6pYrxWl23jEIOjZRWk0H3LRPC8YREgk%2BF1uSNPDVKSVgOfXGR1aeKLT11nNJZSFWm8m3r3E05MQW4HjgWGM%2FX%2BtaEVEGFXP%2FXVYb7DRGbR6FjgV4pXwGSbotU8RMxif91yWSav3yqGk7G%2BGKTDvmTtyGc3Y0hVN6KXWsiLkeCHqgkkF5IxNHdEQEyHUgmWpHWLC%2BTwMzW6Oa05hjJC6CRjySWfKVCcHQ1f8O4kv3gouC8OLcYPKU49S61M4Mgjcqi3Ff06VodkXfSU8a2wL%2BBtNObK%2FxU4jlwxp0%2Fx60PRUOSHHC4nYagA5AsMfSNvgVy8bSTypMM537uj0OjcAAs7L4CGOlF%2FRnaqY6r1tiY3S4IcccygjvhKumA8cuvbuG9EO4RqZJoZSlpaPZlGECbzuD39lfBogg%3D%3D&amp;sz=w512\" alt=\"image.png\" width=\"150\" height=\"151\"><br>केवल निष्कर्ष I और IV अनुसरण करते हैं।</p>",
                    correct: "a",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "36",
                    section: "misc",
                    question_en: "<p>36. Different faces of the same dice are displayed. What is the number against the number \'1\'?<br><strong id=\"docs-internal-guid-408e8a2c-7fff-633c-f0e1-85c5c47022fa\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcPchSSnTr3JBU6Oh8-ImwVkm8Rdf_uNR5ViLw-XHXZ8T2pnfDKR5XLr9mqWmOew7Yad8PM-Ck7G9uD7_U9m8BTIkQ4G_5lS3HI0Uyfx9BF3EjPLVB2R6DytWl-KIuSEeYKxtC1JBLKM3DQsYK9m80KqBg?key=7Eq3yv6nd6p1_hMEgz8-iw\" width=\"200\" height=\"67\"></strong></p>",
                    question_hi: "<p>36. एक ही पासे की अलग अलग स्थितियाँ दर्शायी गयी है।संख्या \'1\' के विपरीत कौन-सी संख्या होगी ?<br><strong id=\"docs-internal-guid-408e8a2c-7fff-633c-f0e1-85c5c47022fa\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcPchSSnTr3JBU6Oh8-ImwVkm8Rdf_uNR5ViLw-XHXZ8T2pnfDKR5XLr9mqWmOew7Yad8PM-Ck7G9uD7_U9m8BTIkQ4G_5lS3HI0Uyfx9BF3EjPLVB2R6DytWl-KIuSEeYKxtC1JBLKM3DQsYK9m80KqBg?key=7Eq3yv6nd6p1_hMEgz8-iw\" width=\"200\" height=\"67\"></strong></p>",
                    options_en: ["<p>6</p>", "<p>4</p>", 
                                "<p>3</p>", "<p>2</p>"],
                    options_hi: ["<p>6</p>", "<p>4</p>",
                                "<p>3</p>", "<p>2</p>"],
                    solution_en: "<p>36.(b) First, find the common digit in the given dice.<br>Now, write them in clockwise direction.<br>5 &rarr; 3 &rarr; 1<br>5 &rarr; 6 &rarr; 4<br>The opposite face to 1 is 4.</p>",
                    solution_hi: "<p>36.(b) सबसे पहले, दिए गए पासे में उभयनिष्ठ अंक ज्ञात कीजिए<br>अब इन्हें दक्षिणावर्त दिशा में लिखें।<br>5 &rarr; 3 &rarr; 1<br>5 &rarr; 6 &rarr; 4<br>1 का विपरीत फलक 4 है।</p>",
                    correct: "b",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "37",
                    section: "misc",
                    question_en: "<p>37. Select the correct mirror image of the given figure when the mirror is placed to the right of the figure.<br><strong id=\"docs-internal-guid-2d763b06-7fff-e4c8-5284-19100bf96a4b\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdpGu4hEXshSDch7B7deRCNdi08tmGmZPiQjjOJ88FRaOwe-aoZJrHyrXwBePHwjAy6Z4wCTZdVeERLolcbm2saBK3Tle0_OxM27RguWX1Pvg-KPwZzaxsl0UIkEyltkG9mv0JGb15NoJhlt-W928EcbUXS?key=7Eq3yv6nd6p1_hMEgz8-iw\" width=\"100\" height=\"90\"></strong></p>",
                    question_hi: "<p>37. दी गयी आकृति के सही दर्पण प्रतिबिंब का चयन करें जब दर्पण को आकृति के दायीं ओर रखा जाता है ।<br><strong id=\"docs-internal-guid-fe0163fb-7fff-47a9-8449-37bd813947b7\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdpGu4hEXshSDch7B7deRCNdi08tmGmZPiQjjOJ88FRaOwe-aoZJrHyrXwBePHwjAy6Z4wCTZdVeERLolcbm2saBK3Tle0_OxM27RguWX1Pvg-KPwZzaxsl0UIkEyltkG9mv0JGb15NoJhlt-W928EcbUXS?key=7Eq3yv6nd6p1_hMEgz8-iw\" width=\"100\" height=\"90\"></strong></p>",
                    options_en: ["<p><strong id=\"docs-internal-guid-56148daf-7fff-8667-1ffe-7e836a630b52\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXehJs82COHolpvrgbpAeUwegnEhlHfpG0KucBaHLKSZpYH2QYzpQVmsEakWO0e4gK3GOLvQDRwVUAHGKUJ20L355TaqNsmj6XKD9mTe-Ybkkm7cPuVJz1F4nyo0aMwHtwyBKFVIMBZvyczox_HP41H1ueA?key=7Eq3yv6nd6p1_hMEgz8-iw\" width=\"90\" height=\"75\"></strong></p>", "<p><strong id=\"docs-internal-guid-636d13f6-7fff-b2df-f5f5-0affa14e051f\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcpnXbalL5tPVChsuUNqzjBzgh0iT_6GKBiUVp_k3Il2YQYZPE6zxAQH0rSzZ8_YmqB7ygGa418DF5i5SUa0nUrbHufYj7434KzrOLT7RWp_1GGdDwMUlhOsMwUUIlW5O4L_5U1HbxFFK1r2Z9-p_ONogxK?key=7Eq3yv6nd6p1_hMEgz8-iw\" width=\"90\" height=\"79\"></strong></p>", 
                                "<p><strong id=\"docs-internal-guid-159d7583-7fff-e828-1bc3-a4250d68bb8a\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdTLzIi8aQDVKdPYMi4tGpsvzbmf7JpWfo5TnleHILDPBALZ51KpmXj9fFMHHafjqXYzua0SaqiuHBqPcqghaTz-WQizLy6xgFlJJs0AYApNFEOEf9Oy7JB_CUphXlc5G4WgoOa1ed0BmG6IzlbnumjsOaw?key=7Eq3yv6nd6p1_hMEgz8-iw\" width=\"90\" height=\"79\"></strong></p>", "<p><strong id=\"docs-internal-guid-c4e0f716-7fff-1c55-6aad-49a12c4c0cf9\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfKywkh1xC88HHqGmLKHIfeTlH6PxyDjsoQ1iIxe2aab8NJMZenvCFyC3_E27g8yKmPj4XFxqC_MJKmuXfImgFlXVZzOt1Jgx5udQcrxRkRCOPgAk7P4FZYDIIIfnI4-1rpKg_3_YKahJUF3rqhw5vlo4QG?key=7Eq3yv6nd6p1_hMEgz8-iw\" width=\"90\" height=\"75\"></strong></p>"],
                    options_hi: ["<p><strong id=\"docs-internal-guid-e28b0a0c-7fff-7d1a-ae1f-9c70fe99d59d\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXehJs82COHolpvrgbpAeUwegnEhlHfpG0KucBaHLKSZpYH2QYzpQVmsEakWO0e4gK3GOLvQDRwVUAHGKUJ20L355TaqNsmj6XKD9mTe-Ybkkm7cPuVJz1F4nyo0aMwHtwyBKFVIMBZvyczox_HP41H1ueA?key=7Eq3yv6nd6p1_hMEgz8-iw\" width=\"90\" height=\"75\"></strong></p>", "<p><strong id=\"docs-internal-guid-e1a6f3bd-7fff-a2b9-750f-15416da007fe\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcpnXbalL5tPVChsuUNqzjBzgh0iT_6GKBiUVp_k3Il2YQYZPE6zxAQH0rSzZ8_YmqB7ygGa418DF5i5SUa0nUrbHufYj7434KzrOLT7RWp_1GGdDwMUlhOsMwUUIlW5O4L_5U1HbxFFK1r2Z9-p_ONogxK?key=7Eq3yv6nd6p1_hMEgz8-iw\" width=\"90\" height=\"79\"></strong></p>",
                                "<p><strong id=\"docs-internal-guid-83bb1d16-7fff-d12e-8a84-1b6b7aed2381\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdTLzIi8aQDVKdPYMi4tGpsvzbmf7JpWfo5TnleHILDPBALZ51KpmXj9fFMHHafjqXYzua0SaqiuHBqPcqghaTz-WQizLy6xgFlJJs0AYApNFEOEf9Oy7JB_CUphXlc5G4WgoOa1ed0BmG6IzlbnumjsOaw?key=7Eq3yv6nd6p1_hMEgz8-iw\" width=\"90\" height=\"79\"></strong></p>", "<p><strong id=\"docs-internal-guid-acc8726f-7fff-013c-531f-0c4c58162a61\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfKywkh1xC88HHqGmLKHIfeTlH6PxyDjsoQ1iIxe2aab8NJMZenvCFyC3_E27g8yKmPj4XFxqC_MJKmuXfImgFlXVZzOt1Jgx5udQcrxRkRCOPgAk7P4FZYDIIIfnI4-1rpKg_3_YKahJUF3rqhw5vlo4QG?key=7Eq3yv6nd6p1_hMEgz8-iw\" width=\"90\" height=\"75\"></strong></p>"],
                    solution_en: "<p>37. (c) <br><strong id=\"docs-internal-guid-68a6f618-7fff-37d4-1e11-0c934b79a3ef\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdTLzIi8aQDVKdPYMi4tGpsvzbmf7JpWfo5TnleHILDPBALZ51KpmXj9fFMHHafjqXYzua0SaqiuHBqPcqghaTz-WQizLy6xgFlJJs0AYApNFEOEf9Oy7JB_CUphXlc5G4WgoOa1ed0BmG6IzlbnumjsOaw?key=7Eq3yv6nd6p1_hMEgz8-iw\" width=\"90\" height=\"79\"></strong></p>",
                    solution_hi: "<p>37. (c) <br><strong id=\"docs-internal-guid-68a6f618-7fff-37d4-1e11-0c934b79a3ef\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdTLzIi8aQDVKdPYMi4tGpsvzbmf7JpWfo5TnleHILDPBALZ51KpmXj9fFMHHafjqXYzua0SaqiuHBqPcqghaTz-WQizLy6xgFlJJs0AYApNFEOEf9Oy7JB_CUphXlc5G4WgoOa1ed0BmG6IzlbnumjsOaw?key=7Eq3yv6nd6p1_hMEgz8-iw\" width=\"90\" height=\"79\"></strong></p>",
                    correct: "c",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "38",
                    section: "misc",
                    question_en: "<p>38 Select the figure that will replace the question mark (?) in the following figure series.<br><strong id=\"docs-internal-guid-0eea6eb2-7fff-bdce-05d9-aa3bbb9362f0\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXd2MsTsOs4Nq_6ASezaPqWAlBuHKYhHPEU38FJPBjG9HWvRUvm-frVHhP8G6nfzonDqmYmJHsDrpqxETt6x2zYIfBSov9JNG9td8b0yLsOVmA-0QiSiEEzW9QT0AH0RR0kkyZHKGI7j_6VNF9oQ6CKKNUYU?key=7Eq3yv6nd6p1_hMEgz8-iw\" width=\"280\" height=\"74\"></strong></p>",
                    question_hi: "<p>38 उस आकृति का चयन करें जो निम्नलिखित आकृति श्रृंखला में प्रश्न चिह्न (?) को प्रतिस्थापित करेगी।<br><strong id=\"docs-internal-guid-de2fdc12-7fff-b474-5b76-036e8f9333d6\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXd2MsTsOs4Nq_6ASezaPqWAlBuHKYhHPEU38FJPBjG9HWvRUvm-frVHhP8G6nfzonDqmYmJHsDrpqxETt6x2zYIfBSov9JNG9td8b0yLsOVmA-0QiSiEEzW9QT0AH0RR0kkyZHKGI7j_6VNF9oQ6CKKNUYU?key=7Eq3yv6nd6p1_hMEgz8-iw\" width=\"280\" height=\"74\"></strong></p>",
                    options_en: ["<p><strong id=\"docs-internal-guid-30ccb7af-7fff-1671-178d-6db3a5154b5d\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXd_dsO9-94XunnQfDTXPMyeRb4N8jiRQIT80BZbyfHUwk3yXJM7hUtkf3dADOx0sR0-JUkMuGJDTz38d27P5cnDfJlMkgyzxu9Hy1VRvG2SqzSVKfnHMjnyZxTv3MXkYyqIe_3-1W3P2zpV6rvxSBeH1J8e?key=7Eq3yv6nd6p1_hMEgz8-iw\" width=\"90\" height=\"85\"></strong></p>", "<p><strong id=\"docs-internal-guid-fe4d96b8-7fff-4202-f250-e15622f5f30c\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXeneDMV7wH503RQP-PTDJCh_Vab3bMn9A-x3Xq32KrapXriTIqBmDIpacpk36P71KbsIkb3l7bBSJAx1gKIrq4bcOe3aqSaYDnyvkjDMp_lMFcjp9wQ7hhFBcnk6K76q2qp_IfS0R4oII-RBRp5Q8bJ-keU?key=7Eq3yv6nd6p1_hMEgz8-iw\" width=\"90\" height=\"85\"></strong></p>", 
                                "<p><strong id=\"docs-internal-guid-7de54287-7fff-694b-9ee9-b84f290ddbde\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXd1Go4Lq35UHsYoDwTVMe2V1SEuMByG6fZRvdhlbMs_ZexC1lhO1vxNL1KMQ4qb-UirFobHgN3X0iLn_JsuLTvTCs7YytVXus3RXvN9_KSZnHu_up3WUdbfYTzjeK1NgJNMrM0A4OhON7QajmjW85mB9qoA?key=7Eq3yv6nd6p1_hMEgz8-iw\" width=\"90\" height=\"85\"></strong></p>", "<p><strong id=\"docs-internal-guid-1c403c82-7fff-9437-70b5-a6f3fe465abd\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfMtCvWswqVDtVW9o1XI8E_mwCl8qebZcPyBIadbWuBLcdZoGX7o_gfoFw4NCvi29kNZGLnnd0GEeEMEye3vD9F42e5H3Emc9uzSb8fuIysB_ahPsiaWRBws1kds5oYqabeNkYt6BX92YjgKVb2UV6_D_uX?key=7Eq3yv6nd6p1_hMEgz8-iw\" width=\"90\" height=\"85\"></strong></p>"],
                    options_hi: ["<p><strong id=\"docs-internal-guid-259be727-7fff-ee62-da04-cc249e48f443\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXd_dsO9-94XunnQfDTXPMyeRb4N8jiRQIT80BZbyfHUwk3yXJM7hUtkf3dADOx0sR0-JUkMuGJDTz38d27P5cnDfJlMkgyzxu9Hy1VRvG2SqzSVKfnHMjnyZxTv3MXkYyqIe_3-1W3P2zpV6rvxSBeH1J8e?key=7Eq3yv6nd6p1_hMEgz8-iw\" width=\"90\" height=\"85\"></strong></p>", "<p><strong id=\"docs-internal-guid-5c61d3f2-7fff-10f6-33ca-fa035d21d72e\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXeneDMV7wH503RQP-PTDJCh_Vab3bMn9A-x3Xq32KrapXriTIqBmDIpacpk36P71KbsIkb3l7bBSJAx1gKIrq4bcOe3aqSaYDnyvkjDMp_lMFcjp9wQ7hhFBcnk6K76q2qp_IfS0R4oII-RBRp5Q8bJ-keU?key=7Eq3yv6nd6p1_hMEgz8-iw\" width=\"90\" height=\"85\"></strong></p>",
                                "<p><strong id=\"docs-internal-guid-3cbe947b-7fff-2f36-a5d6-e914b553858e\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXd1Go4Lq35UHsYoDwTVMe2V1SEuMByG6fZRvdhlbMs_ZexC1lhO1vxNL1KMQ4qb-UirFobHgN3X0iLn_JsuLTvTCs7YytVXus3RXvN9_KSZnHu_up3WUdbfYTzjeK1NgJNMrM0A4OhON7QajmjW85mB9qoA?key=7Eq3yv6nd6p1_hMEgz8-iw\" width=\"90\" height=\"85\"></strong></p>", "<p><strong id=\"docs-internal-guid-55d8717b-7fff-4467-1549-0a7bff649eff\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfMtCvWswqVDtVW9o1XI8E_mwCl8qebZcPyBIadbWuBLcdZoGX7o_gfoFw4NCvi29kNZGLnnd0GEeEMEye3vD9F42e5H3Emc9uzSb8fuIysB_ahPsiaWRBws1kds5oYqabeNkYt6BX92YjgKVb2UV6_D_uX?key=7Eq3yv6nd6p1_hMEgz8-iw\" width=\"90\" height=\"85\"></strong></p>"],
                    solution_en: "<p>38.(b)<br><strong id=\"docs-internal-guid-e421e101-7fff-7fdd-db10-b495b3b184c8\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXeneDMV7wH503RQP-PTDJCh_Vab3bMn9A-x3Xq32KrapXriTIqBmDIpacpk36P71KbsIkb3l7bBSJAx1gKIrq4bcOe3aqSaYDnyvkjDMp_lMFcjp9wQ7hhFBcnk6K76q2qp_IfS0R4oII-RBRp5Q8bJ-keU?key=7Eq3yv6nd6p1_hMEgz8-iw\" width=\"90\" height=\"85\"></strong></p>",
                    solution_hi: "<p>38.(b)<br><strong id=\"docs-internal-guid-e421e101-7fff-7fdd-db10-b495b3b184c8\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXeneDMV7wH503RQP-PTDJCh_Vab3bMn9A-x3Xq32KrapXriTIqBmDIpacpk36P71KbsIkb3l7bBSJAx1gKIrq4bcOe3aqSaYDnyvkjDMp_lMFcjp9wQ7hhFBcnk6K76q2qp_IfS0R4oII-RBRp5Q8bJ-keU?key=7Eq3yv6nd6p1_hMEgz8-iw\" width=\"90\" height=\"85\"></strong></p>",
                    correct: "b",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "39",
                    section: "misc",
                    question_en: "<p>39. In a family of 8 persons with two couples, P is the son of Q and brother of R. S is the daughter of M, who is married to R. T is the aunt of S and mother of V. R&rsquo;s nephew W is the son of P and has a sister V. How is V related to M?</p>",
                    question_hi: "<p>39. दो युग्मों वाले एक परिवार जिसमें आठ सदस्य हैं, उसमें P, Q का बेटा और R का भाई है । S, M की बेटी है जिसकी शादी R से हुई है। T, S की आंटी है और V की माँ है । R का भतीजा W, P का बेटा है तथा उसकी एक बहन V है । V, M से किस प्रकार संबंधित है ?</p>",
                    options_en: ["<p>Nephew</p>", "<p>Daughter</p>", 
                                "<p>Niece</p>", "<p>Cousin Sister</p>"],
                    options_hi: ["<p>भतीजा</p>", "<p>बेटी</p>",
                                "<p>भतीजी</p>", "<p>चचेरी-मौसेरी बहन</p>"],
                    solution_en: "<p>39.(c) By observing the family chart given below we can easily say that V is the niece of M.<br><strong id=\"docs-internal-guid-1112877a-7fff-8f32-075d-0aa3a0dbf84c\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXf3PbBpZZLV_4i27ko36-jEMRnYqI2xe2wcBJ6UnCWqlAAr1YFUisFL2a_prlAmbxkQHaU64MDtP9902XVh7gchs_bsDtuwUI9SCSEkrNSijg-7GyP7B1hmm2SnReNMPjAkyWDl0zOQwCHoyYcydBm62lYS?key=7Eq3yv6nd6p1_hMEgz8-iw\" width=\"150\" height=\"116\"></strong></p>",
                    solution_hi: "<p>39.(c) नीचे दिए गए परिवार चार्ट को देखकर हम आसानी से कह सकते हैं कि V, M की भतीजी है।<br><strong id=\"docs-internal-guid-1112877a-7fff-8f32-075d-0aa3a0dbf84c\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXf3PbBpZZLV_4i27ko36-jEMRnYqI2xe2wcBJ6UnCWqlAAr1YFUisFL2a_prlAmbxkQHaU64MDtP9902XVh7gchs_bsDtuwUI9SCSEkrNSijg-7GyP7B1hmm2SnReNMPjAkyWDl0zOQwCHoyYcydBm62lYS?key=7Eq3yv6nd6p1_hMEgz8-iw\" width=\"150\" height=\"116\"></strong></p>",
                    correct: "c",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "40",
                    section: "misc",
                    question_en: "<p>40. Five phones, H, M, R, T and V, are kept one above the other (not necessarily in the same order). The number of phones above T is same as the number of phones below V. R is just <br>above H. V is at the bottom. There are two phones between M and V. Which of the following phones are above R?</p>",
                    question_hi: "<p>40. पांच फोन - H, M, R, T और V एक दूसरे के ऊपर रखे गए हैं, लेकिन ज़रूरी नहीं कि इसी क्रम में हो। T के ऊपर रखे गए फोन की संख्या V के नीचे रखे गए फ़ोन की संख्या के बराबर है । R, H के ठीक ऊपर है । V तल पर है । M और V के बीच दो फ़ोन हैं । R के ऊपर इनमें से कौन से फ़ोन हैं ?</p>",
                    options_en: ["<p>M and T</p>", "<p>H and T</p>", 
                                "<p>M and V</p>", "<p>M and H</p>"],
                    options_hi: ["<p>M और T</p>", "<p>H और T</p>",
                                "<p>M और V</p>", "<p>M और H</p>"],
                    solution_en: "<p>40 (a) As per given directions, following arrangement is obtained:<br><strong id=\"docs-internal-guid-590dbf11-7fff-8557-0e68-665eae349e5d\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXeKkpd2xW_N_hb2_nAkRPaNfVnMpEX8dXvzmbPpZkUe8vzmnlUpGpTyQ1SImXbmBWFL-L0VtkgXx-u_liILcfLONv5tVSuoOomS_UH5AjD4u9OQ4d8lqH5RfBT-_gmpByeToXs7v5Hyd5aMt3kM-ecgjRbC?key=7Eq3yv6nd6p1_hMEgz8-iw\" width=\"50\" height=\"149\"></strong><br>Hence, M and T are above R.</p>",
                    solution_hi: "<p>40 (a) दिए गए निर्देशों के अनुसार निम्नलिखित व्यवस्था प्राप्त होती है:<br><strong id=\"docs-internal-guid-590dbf11-7fff-8557-0e68-665eae349e5d\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXeKkpd2xW_N_hb2_nAkRPaNfVnMpEX8dXvzmbPpZkUe8vzmnlUpGpTyQ1SImXbmBWFL-L0VtkgXx-u_liILcfLONv5tVSuoOomS_UH5AjD4u9OQ4d8lqH5RfBT-_gmpByeToXs7v5Hyd5aMt3kM-ecgjRbC?key=7Eq3yv6nd6p1_hMEgz8-iw\" width=\"50\" height=\"149\"></strong><br>इसलिए, M और T, R से ऊपर हैं।</p>",
                    correct: "a",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "41",
                    section: "misc",
                    question_en: "<p>41. In the following diagram, the triangle represents the players, the circle represents the \'students\', the rectangle represents the \'high achievers\'. The numbers given in the different sections indicate the number of people.<br><strong id=\"docs-internal-guid-f04c18d0-7fff-093e-e815-6b008952af3c\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdkeF6FYnnx2H_CkNfwQJJwTInzEEU9_wk2VhKOalYGiTrNy31VsDA-JDAj0RF2dTxPiIKPm7jF3RQJEvsAXgSGIj8GDKmQmYndUDkaHG15QZp5HX3zIsO9FUqW1z_ufq9bESFzFGFcx0517iWC72kIg8N0?key=7Eq3yv6nd6p1_hMEgz8-iw\" width=\"200\" height=\"122\"></strong><br>How many great achievers are students but not players?</p>",
                    question_hi: "<p>41. निम्नलिखित आरेख में त्रिभुज खिलाड़ियों को दर्शाता है, वृत्त &lsquo;छात्रों\' को दर्शाता है, आयत &lsquo;बड़ी उपलब्धि हासिल करने वाले लोगों\' को दर्शाता है। विभिन्न खण्डों में दी गयी संख्याएं लोगों की संख्या को दर्शाती हैं। <br><strong id=\"docs-internal-guid-f04c18d0-7fff-093e-e815-6b008952af3c\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdkeF6FYnnx2H_CkNfwQJJwTInzEEU9_wk2VhKOalYGiTrNy31VsDA-JDAj0RF2dTxPiIKPm7jF3RQJEvsAXgSGIj8GDKmQmYndUDkaHG15QZp5HX3zIsO9FUqW1z_ufq9bESFzFGFcx0517iWC72kIg8N0?key=7Eq3yv6nd6p1_hMEgz8-iw\" width=\"200\" height=\"122\"></strong><br>बड़ी उपलब्धि हासिल करने वाले कितने छात्र खिलाड़ी नहीं हैं ?</p>",
                    options_en: ["<p>15</p>", "<p>13</p>", 
                                "<p>12</p>", "<p>3</p>"],
                    options_hi: ["<p>15</p>", "<p>13</p>",
                                "<p>12</p>", "<p>3</p>"],
                    solution_en: "<p>41.(c) In the given equation, we have to tell high achieving students are not players.<br>Rectangle represents the High achievers, circle represents the students.<br>Now we have to take the common number in the circle as well as the rectangle.</p>",
                    solution_hi: "<p>41.(c) दिए गए समीकरण में, हमें यह बताना होगा कि उच्च उपलब्धि प्राप्त करने वाले छात्र खिलाड़ी नहीं हैं।<br>आयत उच्च प्राप्तकर्ताओं का प्रतिनिधित्व करता है, वृत्त छात्रों का प्रतिनिधित्व करता है।<br>अब हमें वृत्त के साथ-साथ आयत में भी उभयनिष्ठ संख्या लेनी है।</p>",
                    correct: "c",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "42",
                    section: "misc",
                    question_en: "<p>42. 10 years ago, a father&rsquo;s age was <math display=\"inline\"><mn>3</mn></math><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math> times that of his son and 10 years from now, the father&rsquo;s age will be 2<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>4</mn></mfrac></math> times that of the son. What will be the sum of the ages of the father and the son at present?</p>",
                    question_hi: "<p>42. 10 वर्ष पहले, पिता की आयु उसके बेटे की आयु से <math display=\"inline\"><mn>3</mn></math><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math> गुना थी तथा अब से 10 साल बाद पिता की आयु बेटे की आयु से 2<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>4</mn></mfrac></math> गुना हो जाएगी। वर्तमान में पिता एवं पुत्र की उम्र का योग क्या होगा ?</p>",
                    options_en: ["<p>100 years</p>", "<p>110 years</p>", 
                                "<p>115 years</p>", "<p>120 years</p>"],
                    options_hi: ["<p>100 वर्ष</p>", "<p>110 वर्ष</p>",
                                "<p>115 वर्ष</p>", "<p>120 वर्ष</p>"],
                    solution_en: "<p>42.(b)&nbsp;Let the present age of the son = x years and that of the father = y years. <br>According to question, <br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>2</mn></mfrac></math> (x - 10) = y - 10 ; <br>from here we get the equation , <br>7x - 2y = 50 &mdash;---------- (i)<br>Again, <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>9</mn><mn>4</mn></mfrac></math> (x + 10) = y + 10 ; <br>here the equation will be as follows :<br>9x - 4y = -50&mdash;------------(ii)<br>On solving equations (i) and (ii) , <br>we get x = 30 years = Age of Son ; <br>and Y = 80 years = Age of Father<br>Sum of present age of Father and Son = 80 + 30<br>= 110 years.</p>",
                    solution_hi: "<p>42.(b)&nbsp;माना पुत्र की वर्तमान आयु = x वर्ष और पिता की वर्तमान आयु = y वर्ष <br>प्रश्न के अनुसार, <br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>2</mn></mfrac></math> (x - 10) = y - 10;<br>यहाँ से हमें समीकरण मिलता है,<br>7x - 2y = 50 &mdash;---------- (i)<br>फिर से, <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>9</mn><mn>4</mn></mfrac></math> (x + 10) = y + 10; <br>यहाँ समीकरण इस प्रकार होगा:<br>9x - 4y = -50&mdash;------------(ii)<br>समीकरण (i) और (ii) को हल करने पर,<br>x = 30 वर्ष = पुत्र की आयु; <br>तथा Y = 80 वर्ष = पिता की आयु<br>पिता और पुत्र की वर्तमान आयु का योग = 80 + 30<br>= 110 वर्ष</p>",
                    correct: "b",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "43",
                    section: "misc",
                    question_en: "<p>43. After starting from his house, Naren walked a few meters towards the east. From there, he took a right turn and walked 80m, and then took a left turn and walked 20m. Finally, he took a right turn again and walked 40m to reach the hospital. If the air distance between his house and the hospital is 130m, how far did Naren walk towards the east initially?</p>",
                    question_hi: "<p>43. अपने घर से शुरू करने के बाद, नरेन पूर्व की ओर कुछ मीटर की दूरी तय करता है। वहाँ से, वह दाईं तरफ मुड़ा और 80 m की दुरी तय की और फिर बाईं तरफ मुड़ा और 20 m चला । अंत में, यह फिर से दाईं तरफ मुड़ा और अस्पताल तक पहुँचने के लिए 40 m चला। यदि उसके घर और अस्पताल के बीच की हवाई दूरी 130 मीटर है, तो शुरुआती बिंदु से नरेन पूर्व की ओर कितनी दूरी पर था?</p>",
                    options_en: ["<p>50m</p>", "<p>25m</p>", 
                                "<p>40m</p>", "<p>30m</p>"],
                    options_hi: ["<p>50m</p>", "<p>25m</p>",
                                "<p>40m</p>", "<p>30m</p>"],
                    solution_en: "<p>43.(d)&nbsp;From the figure formed:<br><strong id=\"docs-internal-guid-bda8b7b8-7fff-ee08-5747-ec4836f11499\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdo6yF-K13Ld3ggzW4cM_L9hY_ntwjnGuMOz2CgN1p-KQPqVHeyoQbavecAKcKIlAd77XPQhB35GR-Gq4Gc--KyvxlLU2BqKd42k68LX3D7-9H9JSCiTADuA5hyejjF3z9LHDKSvcQk7H2-3-5T5GPoDrzC?key=7Eq3yv6nd6p1_hMEgz8-iw\" width=\"180\" height=\"173\"></strong><br>The triangle forms a right angle triangle, with pythagoras triplet of 130, 120 and 50.<br>Therefore, x + 20 = 50<br><math display=\"inline\"><mo>&#8658;</mo></math> x = 30 m</p>",
                    solution_hi: "<p>43.(d)&nbsp;बनायीं गयी आकृति से:<br><strong id=\"docs-internal-guid-b56f83bf-7fff-3854-8ea0-************\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcJG_7EIJCbKBTK-j9CN0RNd9Cmh2ESjD773kt_lpIInF68QPQRl2vCI9fyG7uZ-3HYigz1E3S1cX_DLGyFiEZYScCCci28aWm1P4zuAuaF5vKU9SAoW0kgRoMbq7ybrLPL1zi3b02E4NoYbaJ5saarsbbL?key=7Eq3yv6nd6p1_hMEgz8-iw\" width=\"160\" height=\"155\"></strong><br>त्रिभुज एक समकोण त्रिभुज बनाता है, जिसमें पाइथागोरस त्रिक 130, 120 और 50 का होता है।<br>इसलिए, x + 20 = 50<br><math display=\"inline\"><mo>&#8658;</mo></math> x = 30 मीटर</p>",
                    correct: "d",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "44",
                    section: "misc",
                    question_en: "<p>44. Which number will replace the question mark(?) in the following series?<br>3, 5, 7, 8, 15, ?, 31, 26</p>",
                    question_hi: "<p>44. निम्नलिखित श्रृंखला में प्रश्न चिन्ह (?) के स्थान पर कौन सी संख्या आएगी ?<br>3, 5, 7, 8, 15, ?, 31, 26</p>",
                    options_en: ["<p>24</p>", "<p>14</p>", 
                                "<p>21</p>", "<p>18</p>"],
                    options_hi: ["<p>24</p>", "<p>14</p>",
                                "<p>21</p>", "<p>18</p>"],
                    solution_en: "<p>44.(b) <br><strong id=\"docs-internal-guid-7e55c725-7fff-cf29-a7f8-c60fea027c57\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXeeL7hA7G1dYZ9boJ1X1jnCJfHztT3kbbTDCYvlhdd3eXdUyuFvdrL2AC5Je0xGFDzYMgbXf1xX9Gj-x_kk5L0OBNMC1_DSuO3rQL5cONs0F-BKItz2bQ_9LvFmLJOy7b-tFb3IEVwWX8qPzSTDZjLlSlE?key=7Eq3yv6nd6p1_hMEgz8-iw\" width=\"200\" height=\"82\"></strong></p>",
                    solution_hi: "<p>44.(b)<br><strong id=\"docs-internal-guid-7e55c725-7fff-cf29-a7f8-c60fea027c57\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXeeL7hA7G1dYZ9boJ1X1jnCJfHztT3kbbTDCYvlhdd3eXdUyuFvdrL2AC5Je0xGFDzYMgbXf1xX9Gj-x_kk5L0OBNMC1_DSuO3rQL5cONs0F-BKItz2bQ_9LvFmLJOy7b-tFb3IEVwWX8qPzSTDZjLlSlE?key=7Eq3yv6nd6p1_hMEgz8-iw\" width=\"200\" height=\"82\"></strong></p>",
                    correct: "b",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "45",
                    section: "misc",
                    question_en: "<p>45. If \'DONKEY\' is coded as \'88\' and \'SPARROW\' is coded as \'79\', then how will \'PEGION\' be coded?</p>",
                    question_hi: "<p>45. यदि \'DONKEY\' को \'88\' और \'SPARROW\' को \'79\' के रूप में कोडित किया जाता है, तो \'PEGION\' को कैसे कोड किया जाएगा?</p>",
                    options_en: ["<p>73</p>", "<p>96</p>", 
                                "<p>85</p>", "<p>91</p>"],
                    options_hi: ["<p>73</p>", "<p>96</p>",
                                "<p>85</p>", "<p>91</p>"],
                    solution_en: "<p>45.(b)&nbsp;<strong>Logic</strong>: DONKEY = 88, 88 is the sum of opposites letters place value of DONKEY.<br>Opposite letters of DONKEY is WLMPVB<br>W L M P V B = 23 + 12 + 13 + 16 + 22 + 2 =88<br>Similarly PEGION becomes KVTRLM<br>11 + 22 + 20 + 18 + 12 + 13 = 96</p>",
                    solution_hi: "<p>45.(b) <strong>तर्क :</strong> DONKEY=88, 88 DONKEY के विपरीत अक्षरों के स्थानीय मान का योग है।<br>DONKEY के विपरीत अक्षर WLMPVB है<br>W L M P V B = 23 + 12 + 13 + 16 + 22 + 2 = 88<br>इसी तरह PEGION, KVTRLM बन जाता है <br>11 + 22 + 20 + 18 + 12 + 13 = 96</p>",
                    correct: "b",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "46",
                    section: "misc",
                    question_en: "<p>46. Select the set of numbers that is similar to the following set.<br>{10, 19, 32}</p>",
                    question_hi: "<p>46. उन संख्याओं के समूह का चयन करें जो निम्न सेट के समान है।<br>{10, 19, 32}</p>",
                    options_en: ["<p>{5, 14, 19}</p>", "<p>{12, 25, 49}</p>", 
                                "<p>{3, 7, 10}</p>", "<p>{7, 13, 23}</p>"],
                    options_hi: ["<p>{5, 14, 19}</p>", "<p>{12, 25, 49}</p>",
                                "<p>{3, 7, 10}</p>", "<p>{7, 13, 23}</p>"],
                    solution_en: "<p>46.(d) <strong>Given:</strong> 10 + 19 = 29, 29 + 3 = 32.<br>Similarly, 7 + 13 = 20, 20 + 3 = 23.</p>",
                    solution_hi: "<p>46.(d) <strong>दिया गया है:</strong> 10 + 19 = 29, 29 + 3 = 32.<br>इसी तरह, 7 + 13 = 20, 20 + 3 = 23.</p>",
                    correct: "d",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "47",
                    section: "misc",
                    question_en: "<p>47. In a family of 5 members, X is the sister of Y. M has 2 children and he is the son of E, who is the father-in-law of H. H has only one son. Y is not the grand-daughter of E. How is X related to E ?</p>",
                    question_hi: "<p>47. 5 सदस्यों वाले परिवार में, X, Y की बहन है। M के 2 बच्चे हैं और वह E का बेटा है, जो H का ससुर है, जिसका केवल एक ही बेटा है। Y, E की पोती नहीं है। X, E से कैसे संबंधित है ?</p>",
                    options_en: ["<p>Sister</p>", "<p>Daughter</p>", 
                                "<p>Grand-daughter</p>", "<p>Grandson</p>"],
                    options_hi: ["<p>बहन</p>", "<p>बेटी</p>",
                                "<p>पोती</p>", "<p>पोता</p>"],
                    solution_en: "<p>47.(c) According to the following family chart X is the granddaughter of E.<br><strong id=\"docs-internal-guid-3e97b96e-7fff-b2b8-c191-edda26cfa486\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXf4aCMBDa46l47wIUkotr3k1HE4F0f3vFIrU2FUu2sTxCESYmEwhCtMLRrOAgSBpiELnvniJmriu6fCrytVxx5_b1CWERWS16okppaxKg_wr690TPsf0lrnlMK7-Lw1YXbkkSaP57aw7yAmmHU4NSNf774?key=7Eq3yv6nd6p1_hMEgz8-iw\" width=\"80\" height=\"126\"></strong></p>",
                    solution_hi: "<p>47.(c) निम्नलिखित परिवार चार्ट के अनुसार X, E की पोती है।<br><strong id=\"docs-internal-guid-3e97b96e-7fff-b2b8-c191-edda26cfa486\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXf4aCMBDa46l47wIUkotr3k1HE4F0f3vFIrU2FUu2sTxCESYmEwhCtMLRrOAgSBpiELnvniJmriu6fCrytVxx5_b1CWERWS16okppaxKg_wr690TPsf0lrnlMK7-Lw1YXbkkSaP57aw7yAmmHU4NSNf774?key=7Eq3yv6nd6p1_hMEgz8-iw\" width=\"80\" height=\"126\"></strong></p>",
                    correct: "c",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "48",
                    section: "misc",
                    question_en: "<p>48. The sequence of folding a piece of square paper and the manner in which the folded paper has been cut is shown in the figures X,Y and Z. How would z look if it is unfolded?<br><strong id=\"docs-internal-guid-8c81c3b6-7fff-3e38-5961-6ab053e25df4\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdYOvGBlBv9npCa9LpndZ0EOsKx9CaSp0TVRz-zaaAMa-k6hh6WqrUlRHN79Hhq3C3NUwcuEuIHYoj_vzyPzrZB88ScXY_ZvdVjID0zTHx8ErpWYzRtCqmc_n163igwVp11fw_MPqnhk6qXB4lmG4Lvrfug?key=7Eq3yv6nd6p1_hMEgz8-iw\" width=\"250\" height=\"97\"></strong></p>",
                    question_hi: "<p>48. एक कागज़ को मोड़ने का क्रम तथा मोड़ने के बाद इसे काटने का तरीका आकृति X, Y और Z में दर्शाया गया है। खुलने के बाद यह कागज़ कैसा दिखेगा ?<br><strong id=\"docs-internal-guid-676e60fb-7fff-e4c7-5be9-7fe639b2898d\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdYOvGBlBv9npCa9LpndZ0EOsKx9CaSp0TVRz-zaaAMa-k6hh6WqrUlRHN79Hhq3C3NUwcuEuIHYoj_vzyPzrZB88ScXY_ZvdVjID0zTHx8ErpWYzRtCqmc_n163igwVp11fw_MPqnhk6qXB4lmG4Lvrfug?key=7Eq3yv6nd6p1_hMEgz8-iw\" width=\"250\" height=\"97\"></strong></p>",
                    options_en: ["<p><strong id=\"docs-internal-guid-2c39082f-7fff-6fc9-76f4-d2d9634e90dc\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfObC1nhKUt22V9BcyhUu8h369J-89JfpOpnb_ZcmJU_NP-WEkEu7Pmaelwd3h6m8HhvXUyjwYZHrBEFLxY5_YLDowc8amrsdN2Cbvqfr0NLCC5zZsVMtn2c1cPsNa0oH-j3xhReSLSHBjgp_bF-VzCs1uU?key=7Eq3yv6nd6p1_hMEgz8-iw\" width=\"90\" height=\"89\"></strong></p>", "<p><strong id=\"docs-internal-guid-efff9905-7fff-a700-8ba4-8090161e4478\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfWKwe5z17AhDeDqUX2OneNIrXiqHo9-Zpc9UniELyfXjAiIMh5VduptjENiCy9_2rNI4Csn8sCEwMQ2A8jxtHDFAen_rUmoAda34hH-PK4-5B8jLthiLcdfvuu42biZHNK95D3DSi2nq8fc_pWJnTwdIVA?key=7Eq3yv6nd6p1_hMEgz8-iw\" width=\"90\" height=\"92\"></strong></p>", 
                                "<p><strong id=\"docs-internal-guid-d3b94637-7fff-5f48-0bea-27d9627f3f31\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXepWO8EjEwxhhOPVxpdn95_N6WER5XZMyl753IDBUhlC6atpLzeWQExPwHMh3pFaL3H96idkEORFuU7CjMaG-m4UyLlXH7ByKpwOqfucLp_0MMHJXXqKIXyn45XJUosMvc98FQljUsLQGWGMbiwkObwpQM?key=7Eq3yv6nd6p1_hMEgz8-iw\" width=\"90\" height=\"110\"></strong></p>", "<p><strong id=\"docs-internal-guid-15c5a3f9-7fff-9e67-431d-b43f1eba159b\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcyv0ZurjtuT4LxlcNoTVCPU90mIVLnVaICUFO_63KMV7QQCppA4C5sWLVp9AKjEolNDK9Nl9g110i43NBOv3SDUwIKyZcKzoSEUAXSITTA0GrOC6Azzu-O8IfhV9WzqyY9dYJ4neiCd97YejWHpU8-K7ge?key=7Eq3yv6nd6p1_hMEgz8-iw\" width=\"90\" height=\"80\"></strong></p>"],
                    options_hi: ["<p><strong id=\"docs-internal-guid-addc17f3-7fff-18fd-6456-8733afdd4a9c\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfObC1nhKUt22V9BcyhUu8h369J-89JfpOpnb_ZcmJU_NP-WEkEu7Pmaelwd3h6m8HhvXUyjwYZHrBEFLxY5_YLDowc8amrsdN2Cbvqfr0NLCC5zZsVMtn2c1cPsNa0oH-j3xhReSLSHBjgp_bF-VzCs1uU?key=7Eq3yv6nd6p1_hMEgz8-iw\" width=\"90\" height=\"89\"></strong></p>", "<p><strong id=\"docs-internal-guid-be9d2d4c-7fff-41a6-ebec-74546c40d9d8\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfWKwe5z17AhDeDqUX2OneNIrXiqHo9-Zpc9UniELyfXjAiIMh5VduptjENiCy9_2rNI4Csn8sCEwMQ2A8jxtHDFAen_rUmoAda34hH-PK4-5B8jLthiLcdfvuu42biZHNK95D3DSi2nq8fc_pWJnTwdIVA?key=7Eq3yv6nd6p1_hMEgz8-iw\" width=\"90\" height=\"92\"></strong></p>",
                                "<p><strong id=\"docs-internal-guid-b3091dfa-7fff-78c9-afe0-f31a247143ca\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXepWO8EjEwxhhOPVxpdn95_N6WER5XZMyl753IDBUhlC6atpLzeWQExPwHMh3pFaL3H96idkEORFuU7CjMaG-m4UyLlXH7ByKpwOqfucLp_0MMHJXXqKIXyn45XJUosMvc98FQljUsLQGWGMbiwkObwpQM?key=7Eq3yv6nd6p1_hMEgz8-iw\" width=\"90\" height=\"110\"></strong></p>", "<p><strong id=\"docs-internal-guid-d79d3dc7-7fff-a0b8-b9ca-44e0b81e2470\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcyv0ZurjtuT4LxlcNoTVCPU90mIVLnVaICUFO_63KMV7QQCppA4C5sWLVp9AKjEolNDK9Nl9g110i43NBOv3SDUwIKyZcKzoSEUAXSITTA0GrOC6Azzu-O8IfhV9WzqyY9dYJ4neiCd97YejWHpU8-K7ge?key=7Eq3yv6nd6p1_hMEgz8-iw\" width=\"90\" height=\"80\"></strong></p>"],
                    solution_en: "<p>48.(d) In the given question, when we fold the paper and after cutting it from corners. <br><strong id=\"docs-internal-guid-721531ac-7fff-d26d-01d4-12cf238fae37\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcyv0ZurjtuT4LxlcNoTVCPU90mIVLnVaICUFO_63KMV7QQCppA4C5sWLVp9AKjEolNDK9Nl9g110i43NBOv3SDUwIKyZcKzoSEUAXSITTA0GrOC6Azzu-O8IfhV9WzqyY9dYJ4neiCd97YejWHpU8-K7ge?key=7Eq3yv6nd6p1_hMEgz8-iw\" width=\"90\" height=\"80\"></strong></p>",
                    solution_hi: "<p>48.(d) दिए गए प्रश्न में, हम कागज को जब मोड़ते हैं और कोनों से काटते हैं।<br><strong id=\"docs-internal-guid-721531ac-7fff-d26d-01d4-12cf238fae37\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcyv0ZurjtuT4LxlcNoTVCPU90mIVLnVaICUFO_63KMV7QQCppA4C5sWLVp9AKjEolNDK9Nl9g110i43NBOv3SDUwIKyZcKzoSEUAXSITTA0GrOC6Azzu-O8IfhV9WzqyY9dYJ4neiCd97YejWHpU8-K7ge?key=7Eq3yv6nd6p1_hMEgz8-iw\" width=\"90\" height=\"80\"></strong></p>",
                    correct: "d",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "49",
                    section: "misc",
                    question_en: "<p>49. Three of the following four number-pairs are alike in a certain way and one is different. Find the odd one out:</p>",
                    question_hi: "<p>49. निम्नलिखित चार में से तीन संख्या-युग्म किसी निश्चित प्रकार से एक जैसे हैं तथा कोई एक अलग है । अलग युग्म की पहचान करें ।</p>",
                    options_en: ["<p>20 : 30</p>", "<p>12 : 20</p>", 
                                "<p>30 : 40</p>", "<p>42 : 56</p>"],
                    options_hi: ["<p>20 : 30</p>", "<p>12 : 20</p>",
                                "<p>30 : 40</p>", "<p>42 : 56</p>"],
                    solution_en: "<p>49.(c) <br>20 : 30 = 4 &times;&nbsp;5 : 5 &times; 6<br>12 : 20 = 3 &times; 4 : 4 &times; 5<br><strong>30 : 40 = 5 &times; 6 : 5 &times; 8</strong><br>42 : 56 = 6 &times; 7 : 7 &times; 8<br>Clearly, option c is odd one out.</p>",
                    solution_hi: "<p>49.(c) <br>20 : 30 = 4 &times;&nbsp;5 : 5 &times; 6<br>12 : 20 = 3 &times; 4 : 4 &times; 5<br><strong>30 : 40 = 5 &times;&nbsp;6 : 5 &times; 8</strong><br>42 : 56 = 6 &times; 7 : 7 &times; 8<br>स्पष्ट रूप से, विकल्प c एक विषम है।</p>",
                    correct: "c",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "50",
                    section: "misc",
                    question_en: "<p>50. Find the missing number from the given options.<br>19&nbsp; &nbsp;78&nbsp; &nbsp;20<br>25&nbsp; 144&nbsp; 47<br>16&nbsp; &nbsp;?&nbsp; &nbsp; &nbsp;13<br><br></p>",
                    question_hi: "<p>50. दिए गए विकल्पों में से लुप्त संख्या ज्ञात करें । <br>19&nbsp; &nbsp;78&nbsp; &nbsp;20<br>25&nbsp; 144&nbsp; 47<br>16&nbsp; &nbsp;?&nbsp; &nbsp; &nbsp;13</p>",
                    options_en: ["<p>96</p>", "<p>76</p>", 
                                "<p>58</p>", "<p>29</p>"],
                    options_hi: ["<p>96</p>", "<p>76</p>",
                                "<p>58</p>", "<p>29</p>"],
                    solution_en: "<p>50. (c)<br>19 + 20 = 39 <math display=\"inline\"><mo>&#215;</mo></math> 2 = 78 <br>25 + 47 = 72 <math display=\"inline\"><mo>&#215;</mo></math> 2 = 144 <br>16 + 13 = 29 <math display=\"inline\"><mo>&#215;</mo></math> 2 = 58.</p>",
                    solution_hi: "<p>50. (c)<br>19 + 20 = 39 <math display=\"inline\"><mo>&#215;</mo></math> 2 = 78 <br>25 + 47 = 72 <math display=\"inline\"><mo>&#215;</mo></math> 2 = 144 <br>16 + 13 = 29 <math display=\"inline\"><mo>&#215;</mo></math> 2 = 58.</p>",
                    correct: "c",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "51",
                    section: "misc",
                    question_en: "<p>51. Six persons, L, M, N, P, Q and R, are sitting in a row facing towards the north (not necessarily in the same order). There are three person between P and M.P is not at any of the ends. There are three persons between L and Q. N is to the left of R. L is at one of the ends. M is to the right of P. How many person are sitting to the left of N?</p>",
                    question_hi: "<p>51. छः व्यक्ति - L, M, N, P, Q और R उत्तर की ओर मुख करके एक पंक्ति में बैठे हुए हैं, (लेकिन ज़रूरी नहीं कि इसी क्रम में हों)। P और M के बीच तीन लोग हैं । P किसी भी छोर पर नहीं है । L और Q के बीच केवल तीन लोग हैं । N, R के बाएं ओर है । L किसी एक छोर पर है । M,P के दायें ओर है । N के बाएं ओर कितने लोग हैं ?</p>",
                    options_en: ["<p>4</p>", "<p>1</p>", 
                                "<p>3</p>", "<p>2</p>"],
                    options_hi: ["<p>4</p>", "<p>1</p>",
                                "<p>3</p>", "<p>2</p>"],
                    solution_en: "<p>51(d). As per given direction, following arrangement is obtained:<br><strong id=\"docs-internal-guid-e677d4c5-7fff-7ee9-7f19-626c16b85c53\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdnSg6-R8i3PJjkqspF7hfwEA-L-l2jIWSn4uDWJbjQnPdV2H4A2g3s0mCaWGFQTb7dOGhyjdVDO10T-T95RjVVmwDYpfM-r7tmV8lYcjDsMPv_nXaUBTy0oespPzSLzyGC2LA_A-M5enE0FIJ8Tsh4_c_f?key=7Eq3yv6nd6p1_hMEgz8-iw\" width=\"250\" height=\"37\"></strong><br>Hence, there are only two people (namely P and L) on the left on N.</p>",
                    solution_hi: "<p>51(d). दिए गए निर्देश के अनुसार निम्नलिखित व्यवस्था प्राप्त होती है:<br><strong id=\"docs-internal-guid-e677d4c5-7fff-7ee9-7f19-626c16b85c53\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdnSg6-R8i3PJjkqspF7hfwEA-L-l2jIWSn4uDWJbjQnPdV2H4A2g3s0mCaWGFQTb7dOGhyjdVDO10T-T95RjVVmwDYpfM-r7tmV8lYcjDsMPv_nXaUBTy0oespPzSLzyGC2LA_A-M5enE0FIJ8Tsh4_c_f?key=7Eq3yv6nd6p1_hMEgz8-iw\" width=\"250\" height=\"37\"></strong><br>इसलिए, N के बाईं ओर केवल दो व्यक्ति (अर्थात् P और L) हैं।</p>",
                    correct: "d",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "52",
                    section: "misc",
                    question_en: "<p>52. If FLOWER is coded as 14 and DISTASTE is coded as 18, then how will BUREAUCRAT be coded as?</p>",
                    question_hi: "<p>52. यदि FLOWER का कूट 14 तथा DISTASTE का कूट 18 है, तो BUREAUCRAT को किस प्रकार कूटबद्ध किया जाएगा ?</p>",
                    options_en: ["<p>22</p>", "<p>18</p>", 
                                "<p>20</p>", "<p>28</p>"],
                    options_hi: ["<p>22</p>", "<p>18</p>",
                                "<p>20</p>", "<p>28</p>"],
                    solution_en: "<p>52.(a)&nbsp;<strong>Logic is :</strong> 2 &times; (number of letters + 1). Similarly, BUREAUCRAT has 10 letters. <br>10 + 1 = 11 &times;&nbsp;2 = 22.</p>",
                    solution_hi: "<p>52.(a) <strong>तर्क :-</strong> 2 &times;&nbsp;(अक्षरों की संख्या + 1) इसी तरह, BUREAUCRAT में 10 अक्षर होते हैं।<br>10 + 1 = 11 &times;&nbsp;2 = 22.</p>",
                    correct: "a",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "53",
                    section: "misc",
                    question_en: "<p>53. Twenty teams take part in a football tournament. Each team has to play every other team. How many games would be played in the tournament ?</p>",
                    question_hi: "<p>53. एक फुटबॉल प्रतियोगिता में बीस टीमों ने भाग लिया। प्रत्येक टीम को हर दूसरी टीम से खेलना है। इस प्रतियोगिता में कितने मैच खेले जायेंगे ?</p>",
                    options_en: ["<p>195</p>", "<p>190</p>", 
                                "<p>170</p>", "<p>180</p>"],
                    options_hi: ["<p>195</p>", "<p>190</p>",
                                "<p>170</p>", "<p>180</p>"],
                    solution_en: "<p>53.(b) <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mmultiscripts><mi mathvariant=\"normal\">C</mi><mprescripts></mprescripts><none></none><mn>20</mn></mmultiscripts><mn>2</mn></msub></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mmultiscripts><mi mathvariant=\"normal\">C</mi><mprescripts/><none/><mn>20</mn></mmultiscripts><mn>2</mn></msub></math> is the maximum possible way.<br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mmultiscripts><mi mathvariant=\"normal\">C</mi><mprescripts></mprescripts><none></none><mn>20</mn></mmultiscripts><mn>2</mn></msub></math> =&nbsp;<math display=\"inline\"><mfrac><mrow><mn>20</mn><mo>&#215;</mo><mn>19</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> = 190</p>",
                    solution_hi: "<p>53.(b) तर्क है : <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mmultiscripts><mi mathvariant=\"normal\">C</mi><mprescripts></mprescripts><none></none><mn>20</mn></mmultiscripts><mn>2</mn></msub></math> अधिकतम संभव तरीका है।<br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mmultiscripts><mi mathvariant=\"normal\">C</mi><mprescripts></mprescripts><none></none><mn>20</mn></mmultiscripts><mn>2</mn></msub></math> =&nbsp;<math display=\"inline\"><mfrac><mrow><mn>20</mn><mo>&#215;</mo><mn>19</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> = 190</p>",
                    correct: "b",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "54",
                    section: "misc",
                    question_en: "<p>54. Which fraction will replace the question mark(?) in the following series?<br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>4</mn></mfrac><mo>,</mo><mfrac><mn>3</mn><mn>5</mn></mfrac><mo>,</mo><mfrac><mn>6</mn><mn>7</mn></mfrac><mo>,</mo><mfrac><mn>10</mn><mn>11</mn></mfrac></math>,?</p>",
                    question_hi: "<p>54. निम्नलिखित श्रृंखला में प्रश्न चिन्ह (?) के स्थान पर कौन सा भिन्न आएगा ? <br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>4</mn></mfrac><mo>,</mo><mfrac><mn>3</mn><mn>5</mn></mfrac><mo>,</mo><mfrac><mn>6</mn><mn>7</mn></mfrac><mo>,</mo><mfrac><mn>10</mn><mn>11</mn></mfrac></math>,?</p>",
                    options_en: ["<p><math display=\"inline\"><mfrac><mrow><mn>15</mn></mrow><mrow><mn>23</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>15</mn></mrow><mrow><mn>19</mn></mrow></mfrac></math></p>", 
                                "<p><math display=\"inline\"><mfrac><mrow><mn>17</mn></mrow><mrow><mn>27</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>17</mn></mrow><mrow><mn>21</mn></mrow></mfrac></math></p>"],
                    options_hi: ["<p><math display=\"inline\"><mfrac><mrow><mn>15</mn></mrow><mrow><mn>23</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>15</mn></mrow><mrow><mn>19</mn></mrow></mfrac></math></p>",
                                "<p><math display=\"inline\"><mfrac><mrow><mn>17</mn></mrow><mrow><mn>27</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>17</mn></mrow><mrow><mn>21</mn></mrow></mfrac></math></p>"],
                    solution_en: "<p>54.(b) <br><strong id=\"docs-internal-guid-24e8644e-7fff-d358-d6eb-4db05790a7ca\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXe_0ZPcrAHiGqoacHYrXkyLJ-Nl9IB-Wn2K26qt4oAPRbtWdebjvaoGs6xQDsOqCFw-oFpercSXNbHjB8TmuHwCnMuR729fztZY0VTtWFs4dojVUEAU3IEv7ZZe_VxTQAlrbZ7HOVrNjnMnIsBL54Va0akJ?key=7Eq3yv6nd6p1_hMEgz8-iw\" width=\"150\" height=\"97\"></strong><br>So, answer will be <math display=\"inline\"><mfrac><mrow><mn>15</mn></mrow><mrow><mn>19</mn></mrow></mfrac></math></p>",
                    solution_hi: "<p>54.(b)<br><strong id=\"docs-internal-guid-24e8644e-7fff-d358-d6eb-4db05790a7ca\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXe_0ZPcrAHiGqoacHYrXkyLJ-Nl9IB-Wn2K26qt4oAPRbtWdebjvaoGs6xQDsOqCFw-oFpercSXNbHjB8TmuHwCnMuR729fztZY0VTtWFs4dojVUEAU3IEv7ZZe_VxTQAlrbZ7HOVrNjnMnIsBL54Va0akJ?key=7Eq3yv6nd6p1_hMEgz8-iw\" width=\"150\" height=\"97\"></strong><br>तो, उत्तर होगा <math display=\"inline\"><mfrac><mrow><mn>15</mn></mrow><mrow><mn>19</mn></mrow></mfrac></math>.</p>",
                    correct: "b",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "55",
                    section: "misc",
                    question_en: "<p>55. Study the given pattern carefully and select the figure that will complete the pattern given in the question figure.<br><strong id=\"docs-internal-guid-4d95a849-7fff-e74d-0d24-1b46db918b7d\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfXMQ5i6fxX_PVsLALeTTqqGGoe4fRPpp-R5F7JJbuvHcqjs4ChKGVpipNEvvLF8hD3WorqiKT7sSNXNAWoyf2auCRnVZ7j1JrP_nbPofyHjMenQASz-D52P2WhoY_pesiou1zF1oWYkN0myx16y6CC4fH3?key=7Eq3yv6nd6p1_hMEgz8-iw\" width=\"120\" height=\"120\"></strong></p>",
                    question_hi: "<p>55. दिए गए पैटर्न का ध्यानपूर्वक अध्ययन करें और उस आंकड़े का चयन करें जो प्रश्न आकृति में दिए गए पैटर्न को पूरा करेगा।<br><strong id=\"docs-internal-guid-0e429f5e-7fff-43dd-f3ec-2a6d102930f6\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfXMQ5i6fxX_PVsLALeTTqqGGoe4fRPpp-R5F7JJbuvHcqjs4ChKGVpipNEvvLF8hD3WorqiKT7sSNXNAWoyf2auCRnVZ7j1JrP_nbPofyHjMenQASz-D52P2WhoY_pesiou1zF1oWYkN0myx16y6CC4fH3?key=7Eq3yv6nd6p1_hMEgz8-iw\" width=\"120\" height=\"120\"></strong></p>",
                    options_en: ["<p><strong id=\"docs-internal-guid-11fe32cd-7fff-138b-58e6-2c7b57e6d8d3\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXeckX9ixyT47y0JxU8eQfFOEzgl4cGevzI5iccpG5IljhkjkkiEE7Wdpkf8poBChFp4MOSK17ewuOH3HzbpBYbHbZ0xxMOKLGu637bhrGP-C1V_rCvb0a4pB1f31h9ckWs7u4g4XHebzfvu6r0RBjOIiDFA?key=7Eq3yv6nd6p1_hMEgz8-iw\" width=\"90\" height=\"86\"></strong></p>", "<p><strong id=\"docs-internal-guid-a2f15f9d-7fff-8c62-1507-ff31cd6e1331\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXeT4L-quTuzg4Z-hYz0uNIuVwZwMdao3mDrRPOEH2-Vr92pXItQNRgU9pMNpkWh5hKwaje0827ZyVeUtoBVqc-IyH9_hwl6z3E2nadjsZpJKopPckQoF_02etatm4SqylpcCICKtY46wtI_LA5bwrjeAwFe?key=7Eq3yv6nd6p1_hMEgz8-iw\" width=\"90\" height=\"85\"></strong></p>", 
                                "<p><strong id=\"docs-internal-guid-b8768483-7fff-ddbb-7b68-dd72e455a2ac\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfgzq76eGgXPvyHGex1wkoHGr4y27IDZJ7B2T_FqW3QDQC0-zIE0HvH0VT77Az4JyzOTkGx1qHmKRmdpxxqVVspr7YbXZrrKEPz0_Zq_aavLCjMS99FOgVmy_uk6_lW5H-TkMfbYQNw78EzfvtzfnkJTjY?key=7Eq3yv6nd6p1_hMEgz8-iw\" width=\"90\" height=\"90\"></strong></p>", "<p><strong id=\"docs-internal-guid-18ab68bc-7fff-0ab6-ffe9-b0e2090d86fd\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfPy3_S404qNN5CMzRUSZ995VBCaZhMBluxyssrcvHM8IToNJhf8ZUI6RMP-JZeTgILLz_nzt7KjDtUgcmg3HGELem7CzfBTVeApqxYJR0sCKnETE1H09_yv0djQVSZ4I8FVgOT5Sv4Er2KwCC8vnsEiV8?key=7Eq3yv6nd6p1_hMEgz8-iw\" width=\"90\" height=\"88\"></strong></p>"],
                    options_hi: ["<p><strong id=\"docs-internal-guid-52abe6d6-7fff-7c5a-b00b-737d83580ea9\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXeckX9ixyT47y0JxU8eQfFOEzgl4cGevzI5iccpG5IljhkjkkiEE7Wdpkf8poBChFp4MOSK17ewuOH3HzbpBYbHbZ0xxMOKLGu637bhrGP-C1V_rCvb0a4pB1f31h9ckWs7u4g4XHebzfvu6r0RBjOIiDFA?key=7Eq3yv6nd6p1_hMEgz8-iw\" width=\"90\" height=\"86\"></strong></p>", "<p><strong id=\"docs-internal-guid-3f3dd20e-7fff-a8be-886a-204c1b8558f4\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXeT4L-quTuzg4Z-hYz0uNIuVwZwMdao3mDrRPOEH2-Vr92pXItQNRgU9pMNpkWh5hKwaje0827ZyVeUtoBVqc-IyH9_hwl6z3E2nadjsZpJKopPckQoF_02etatm4SqylpcCICKtY46wtI_LA5bwrjeAwFe?key=7Eq3yv6nd6p1_hMEgz8-iw\" width=\"90\" height=\"85\"></strong></p>",
                                "<p><strong id=\"docs-internal-guid-40b9bdd0-7fff-0468-0d43-6be9148d0574\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfgzq76eGgXPvyHGex1wkoHGr4y27IDZJ7B2T_FqW3QDQC0-zIE0HvH0VT77Az4JyzOTkGx1qHmKRmdpxxqVVspr7YbXZrrKEPz0_Zq_aavLCjMS99FOgVmy_uk6_lW5H-TkMfbYQNw78EzfvtzfnkJTjY?key=7Eq3yv6nd6p1_hMEgz8-iw\" width=\"90\" height=\"90\"></strong></p>", "<p><strong id=\"docs-internal-guid-cb8f2849-7fff-cebb-b284-896e922dd3f8\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfPy3_S404qNN5CMzRUSZ995VBCaZhMBluxyssrcvHM8IToNJhf8ZUI6RMP-JZeTgILLz_nzt7KjDtUgcmg3HGELem7CzfBTVeApqxYJR0sCKnETE1H09_yv0djQVSZ4I8FVgOT5Sv4Er2KwCC8vnsEiV8?key=7Eq3yv6nd6p1_hMEgz8-iw\" width=\"90\" height=\"88\"></strong></p>"],
                    solution_en: "<p>55.(d)<br><strong id=\"docs-internal-guid-cb8f2849-7fff-cebb-b284-896e922dd3f8\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfPy3_S404qNN5CMzRUSZ995VBCaZhMBluxyssrcvHM8IToNJhf8ZUI6RMP-JZeTgILLz_nzt7KjDtUgcmg3HGELem7CzfBTVeApqxYJR0sCKnETE1H09_yv0djQVSZ4I8FVgOT5Sv4Er2KwCC8vnsEiV8?key=7Eq3yv6nd6p1_hMEgz8-iw\" width=\"90\" height=\"88\"></strong></p>",
                    solution_hi: "<p>55.(d)<br><strong id=\"docs-internal-guid-cb8f2849-7fff-cebb-b284-896e922dd3f8\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfPy3_S404qNN5CMzRUSZ995VBCaZhMBluxyssrcvHM8IToNJhf8ZUI6RMP-JZeTgILLz_nzt7KjDtUgcmg3HGELem7CzfBTVeApqxYJR0sCKnETE1H09_yv0djQVSZ4I8FVgOT5Sv4Er2KwCC8vnsEiV8?key=7Eq3yv6nd6p1_hMEgz8-iw\" width=\"90\" height=\"88\"></strong></p>",
                    correct: "d",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "56",
                    section: "misc",
                    question_en: "<p>56. Find the missing number from the given options.<br>&nbsp;9&nbsp; &nbsp; 28&nbsp; &nbsp; 85<br>12&nbsp; &nbsp;37&nbsp; &nbsp; &nbsp;?<br>16&nbsp; &nbsp;49&nbsp; &nbsp;148</p>",
                    question_hi: "<p>56. दिए गए विकल्पों में से लुप्त संख्या ज्ञात करें ।<br>&nbsp;9&nbsp; &nbsp; 28&nbsp; &nbsp; 85<br>12&nbsp; &nbsp;37&nbsp; &nbsp; &nbsp;?<br>16&nbsp; &nbsp;49&nbsp; &nbsp;148</p>",
                    options_en: ["<p>134</p>", "<p>112</p>", 
                                "<p>96</p>", "<p>140</p>"],
                    options_hi: ["<p>134</p>", "<p>112</p>",
                                "<p>96</p>", "<p>140</p>"],
                    solution_en: "<p>56. (b)&nbsp;9 &times; 3 = 27 + 1 = 28 ; 28 &times; 3 = 84 + 1 = 85.<br>Similarly, 12 &times; 3 = 36 + 1 = 37 ; 37 &times; 3 = 111 + 1 = 112.</p>",
                    solution_hi: "<p>56. (b)&nbsp;9 &times; 3 = 27 + 1 = 28 ; 28 &times; 3 = 84 + 1 = 85.<br>उसी प्रकार, 12 &times;&nbsp;3 = 36 + 1 = 37 ; 37 &times; 3 = 111 + 1 = 112.</p>",
                    correct: "b",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "57",
                    section: "misc",
                    question_en: "<p>57. Select the figure in which the given figure is embedded.<br><strong id=\"docs-internal-guid-2bf93a93-7fff-d0da-2f3c-76868b1391e1\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXeMHeAiSENHXoQ6xplniFARo0sUwcVQ9USruDD-WcWNHizk9rSuG-viS6DTBx3642cXgKYW2wKBxbFS6geZ064Jv44EXvUDIFE9Rhs9iEql0tS-CAXaNCWt908xjSVgIuZL3PoRru548uAYZKYMK8ehnLM0?key=7Eq3yv6nd6p1_hMEgz8-iw\" width=\"150\" height=\"81\"></strong></p>",
                    question_hi: "<p>57. विकल्प का चयन करें जिसमें दी गयी आकृति समाहित है ।<br><strong id=\"docs-internal-guid-7493ffb1-7fff-e49a-e805-2e27136ede69\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXeMHeAiSENHXoQ6xplniFARo0sUwcVQ9USruDD-WcWNHizk9rSuG-viS6DTBx3642cXgKYW2wKBxbFS6geZ064Jv44EXvUDIFE9Rhs9iEql0tS-CAXaNCWt908xjSVgIuZL3PoRru548uAYZKYMK8ehnLM0?key=7Eq3yv6nd6p1_hMEgz8-iw\" width=\"150\" height=\"81\"></strong></p>",
                    options_en: ["<p><strong id=\"docs-internal-guid-04c4c9a3-7fff-1b89-2cd3-3cd772c1bb86\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXceoLP3UI1NcDKrZ3cbnqasm5QgzgoZO7ECNKU9M8DLVZ3zMc_JGeLs1hVPkZbTjKPq8JATjTY_YC14ucg7EioZml9Q6tVr9PsnzQBuQK5T89pIy4udaAycYq8fviSkRHb5voFq_ohPcJ1RqdfdUGRcY7ka?key=7Eq3yv6nd6p1_hMEgz8-iw\" width=\"90\" height=\"83\"></strong></p>", "<p><strong id=\"docs-internal-guid-a448e62b-7fff-5163-579e-4a6bf3aa7b51\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcB-WGX00ltFybTC3neuzsnJ6EkNSJIo49_fDtA7_fQLjzI6OUgZFCxEPC5eBal1B0LrQHWoTGd7eAxtH27Qxdh1z5FNI-Qhsctzpbkq2wQ3Zpc5r_k0WRuU4IyWsCscBxTx_TWqnbaTcGS3mNbBu0XZSc?key=7Eq3yv6nd6p1_hMEgz8-iw\" width=\"90\" height=\"84\"></strong></p>", 
                                "<p><strong id=\"docs-internal-guid-eca4c06d-7fff-7e2f-5f00-c6d82fce0043\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfxm28APWi-Ciw3BmuU2KX2adh1S2yzYASHCNQdoU0MAztYn0VnVaI6kq-OxwsIOX6bY43ZCCdDloz07_L9MOL8WGpBQkZExRi2sZ-iJp4za0VJ1xCxt2Pca_h-mCOOccta7mOlkfAr5HjV6Jr5ymjfAR6R?key=7Eq3yv6nd6p1_hMEgz8-iw\" width=\"90\" height=\"83\"></strong></p>", "<p><strong id=\"docs-internal-guid-6db5a406-7fff-a7aa-d14f-476c20931ee4\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfMyRJFD6vayNH5X9oCPIMRONZK1sTZYKoX303CDM4yjeaXdKOwV2vl6sp_xjs5-xm88l9eyaYPiNZAmITZclUYMuaKpwGNCayUIesz2EROns4b-6oGwebfjYkl8DYXLGrA-C9hH2jlqCUMcIeG2-hSH3Zv?key=7Eq3yv6nd6p1_hMEgz8-iw\" width=\"90\" height=\"86\"></strong></p>"],
                    options_hi: ["<p><strong id=\"docs-internal-guid-a101419d-7fff-0c71-4844-261440c3480f\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXceoLP3UI1NcDKrZ3cbnqasm5QgzgoZO7ECNKU9M8DLVZ3zMc_JGeLs1hVPkZbTjKPq8JATjTY_YC14ucg7EioZml9Q6tVr9PsnzQBuQK5T89pIy4udaAycYq8fviSkRHb5voFq_ohPcJ1RqdfdUGRcY7ka?key=7Eq3yv6nd6p1_hMEgz8-iw\" width=\"90\" height=\"83\"></strong></p>", "<p><strong id=\"docs-internal-guid-6ec93ce9-7fff-3e37-f11e-5bd30ed9495f\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcB-WGX00ltFybTC3neuzsnJ6EkNSJIo49_fDtA7_fQLjzI6OUgZFCxEPC5eBal1B0LrQHWoTGd7eAxtH27Qxdh1z5FNI-Qhsctzpbkq2wQ3Zpc5r_k0WRuU4IyWsCscBxTx_TWqnbaTcGS3mNbBu0XZSc?key=7Eq3yv6nd6p1_hMEgz8-iw\" width=\"90\" height=\"84\"></strong></p>",
                                "<p><strong id=\"docs-internal-guid-d504b409-7fff-83e0-5084-a325906956bb\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfxm28APWi-Ciw3BmuU2KX2adh1S2yzYASHCNQdoU0MAztYn0VnVaI6kq-OxwsIOX6bY43ZCCdDloz07_L9MOL8WGpBQkZExRi2sZ-iJp4za0VJ1xCxt2Pca_h-mCOOccta7mOlkfAr5HjV6Jr5ymjfAR6R?key=7Eq3yv6nd6p1_hMEgz8-iw\" width=\"90\" height=\"83\"></strong></p>", "<p><strong id=\"docs-internal-guid-b8401a09-7fff-aabd-7f77-04f2fe22098f\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfMyRJFD6vayNH5X9oCPIMRONZK1sTZYKoX303CDM4yjeaXdKOwV2vl6sp_xjs5-xm88l9eyaYPiNZAmITZclUYMuaKpwGNCayUIesz2EROns4b-6oGwebfjYkl8DYXLGrA-C9hH2jlqCUMcIeG2-hSH3Zv?key=7Eq3yv6nd6p1_hMEgz8-iw\" width=\"90\" height=\"86\"></strong></p>"],
                    solution_en: "<p>57. (c)<br><strong id=\"docs-internal-guid-1c1fc647-7fff-d94b-f1b2-2412ba8f634b\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfxm28APWi-Ciw3BmuU2KX2adh1S2yzYASHCNQdoU0MAztYn0VnVaI6kq-OxwsIOX6bY43ZCCdDloz07_L9MOL8WGpBQkZExRi2sZ-iJp4za0VJ1xCxt2Pca_h-mCOOccta7mOlkfAr5HjV6Jr5ymjfAR6R?key=7Eq3yv6nd6p1_hMEgz8-iw\" width=\"90\" height=\"83\"></strong></p>",
                    solution_hi: "<p>57. (c)<br><strong id=\"docs-internal-guid-1c1fc647-7fff-d94b-f1b2-2412ba8f634b\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfxm28APWi-Ciw3BmuU2KX2adh1S2yzYASHCNQdoU0MAztYn0VnVaI6kq-OxwsIOX6bY43ZCCdDloz07_L9MOL8WGpBQkZExRi2sZ-iJp4za0VJ1xCxt2Pca_h-mCOOccta7mOlkfAr5HjV6Jr5ymjfAR6R?key=7Eq3yv6nd6p1_hMEgz8-iw\" width=\"90\" height=\"83\"></strong></p>",
                    correct: "c",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "58",
                    section: "misc",
                    question_en: "<p>58 If it was a Saturday on 10 November 2018, what was the day of the week on 15 August 2017?</p>",
                    question_hi: "<p>58 यदि 10 नवंबर 2018 को शनिवार था, तो 15 अगस्त 2017 को सप्ताह का दिन क्या था?</p>",
                    options_en: ["<p>Monday</p>", "<p>Tuesday</p>", 
                                "<p>Sunday</p>", "<p>Friday</p>"],
                    options_hi: ["<p>सोमवार</p>", "<p>मंगलवार</p>",
                                "<p>रविवार</p>", "<p>शुक्रवार</p>"],
                    solution_en: "<p>58. (b)&nbsp;10 November 2018 - Saturday&nbsp;<br>10 November 2017 - friday ( -1 if normal year decrease )<br>Total odd day between 10 Nov 2017 and 15 Aug 2017 = <math display=\"inline\"><mfrac><mrow><mn>10</mn><mo>+</mo><mn>31</mn><mo>+</mo><mn>30</mn><mo>+</mo><mn>16</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> = 3 days <br>So, day of week on 15 aug 2017 = Friday - 3 = Tuesday</p>",
                    solution_hi: "<p>58. (b)&nbsp;10 नवंबर 2018 - शनिवार<br>10 नवंबर 2017 - शुक्रवार (-1 यदि सामान्य वर्ष घटता है)<br>10 नवंबर 2017 और 15 अगस्त 2017 के बीच कुल विषम दिन = <math display=\"inline\"><mfrac><mrow><mn>10</mn><mo>+</mo><mn>31</mn><mo>+</mo><mn>30</mn><mo>+</mo><mn>16</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> = 3 दिन<br>तो, 15 अगस्त 2017 को सप्ताह का दिन = शुक्रवार - 3 = मंगलवार</p>",
                    correct: "b",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "59",
                    section: "misc",
                    question_en: "<p>59. Select the option that is related to the third image on the same basis as the second image is related to the first image.<br><strong id=\"docs-internal-guid-9bb39050-7fff-549b-6b80-8e0941e24454\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcExBENIebAIS8vR-oiBsPOeLvnrCbPGe7-F2riBsFJbRosSFs9NVUWMypdv3N5p9waC7VczGigk-Ye6BO8OkEvrwavwDQrJ5FRCBalWXe40JAJgh3vJWr_TJ-dcQ_sFzJA-5GUW-efprKA3GXCjBbys28?key=7Eq3yv6nd6p1_hMEgz8-iw\" width=\"300\" height=\"58\"></strong></p>",
                    question_hi: "<p>59. उस विकल्प का चयन करें जो तीसरी छवि से उसी आधार पर संबंधित है जैसे दूसरी छवि पहली छवि से संबंधित है।<br><strong id=\"docs-internal-guid-b0ffba21-7fff-a1e8-df02-233eff50e3c1\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcExBENIebAIS8vR-oiBsPOeLvnrCbPGe7-F2riBsFJbRosSFs9NVUWMypdv3N5p9waC7VczGigk-Ye6BO8OkEvrwavwDQrJ5FRCBalWXe40JAJgh3vJWr_TJ-dcQ_sFzJA-5GUW-efprKA3GXCjBbys28?key=7Eq3yv6nd6p1_hMEgz8-iw\" width=\"300\" height=\"58\"></strong></p>",
                    options_en: ["<p><strong id=\"docs-internal-guid-b03bd1db-7fff-7fab-d028-8e8a609c057e\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdeicyWtSc8K_SKIHxDvjAxHzafi0fnK6fP-mK_ONHMOR7EcY4B4gKcTKk7WHmY9gse1HtNK1dUU4NOdieYVATJ7JGh5MUa8wnCd-UxPNaMPrINMRjR6aFmpYdU2JLJjnXNzl9j1VzjsC3GfBR_yrjVqe5b?key=7Eq3yv6nd6p1_hMEgz8-iw\" width=\"90\" height=\"92\"></strong></p>", "<p><strong id=\"docs-internal-guid-23a4f590-7fff-c698-856b-e6e923f586b3\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfevHoUDoHQPJ-SXR5h2zyTc8EIW-z_aPobiVQ2L-YsXal5T8G4-tkxjnepPiaKYdKahswZaxSlPBoiywfKjln-NXB32V3soY2MFoy9fU6jQfDznALy7VA7lmydoxJjGHYriru7vtZZRXljyfIhmdwDfFsR?key=7Eq3yv6nd6p1_hMEgz8-iw\" width=\"90\" height=\"91\"></strong></p>", 
                                "<p><strong id=\"docs-internal-guid-0b0e1b13-7fff-e8b0-f7c1-c47fea822ba3\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXd0k9yyqLSNhakWMkg1d261beXDw-L9aFXRUFjmY0ecjFgDChGKIr3ViFZ266jenbOTtQmBEfXE9-tAw6q5NXj0C8quuOeuf1mFXBsb1bAkuICLdgnTWp6i1flPmRr3aRLA1KhXTEKXFntLP5_OXYdPpUOj?key=7Eq3yv6nd6p1_hMEgz8-iw\" width=\"90\" height=\"93\"></strong></p>", "<p><strong id=\"docs-internal-guid-0bc6d2a2-7fff-4d2a-207e-a583a33e768d\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfb6GTWyN1EJFNS8wcWNiIc1boKxS0Cp5p9iuVbtCkzblbrN9NopuJIGFz3af7XeYsf8FaN2ETmTAt9HyZc7QlPGe4bYEuJuibSp51xmldtf5bjL4kNL7F2_7Be3-FgkZC7LP0iyjBPH6ADwBuuAlTIBnrb?key=7Eq3yv6nd6p1_hMEgz8-iw\" width=\"90\" height=\"92\"></strong></p>"],
                    options_hi: ["<p><strong id=\"docs-internal-guid-150acc3e-7fff-cdb6-cfad-c45a3867b555\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdeicyWtSc8K_SKIHxDvjAxHzafi0fnK6fP-mK_ONHMOR7EcY4B4gKcTKk7WHmY9gse1HtNK1dUU4NOdieYVATJ7JGh5MUa8wnCd-UxPNaMPrINMRjR6aFmpYdU2JLJjnXNzl9j1VzjsC3GfBR_yrjVqe5b?key=7Eq3yv6nd6p1_hMEgz8-iw\" width=\"90\" height=\"92\"></strong></p>", "<p><strong id=\"docs-internal-guid-acc57495-7fff-e304-33ce-60defab3c2a4\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfevHoUDoHQPJ-SXR5h2zyTc8EIW-z_aPobiVQ2L-YsXal5T8G4-tkxjnepPiaKYdKahswZaxSlPBoiywfKjln-NXB32V3soY2MFoy9fU6jQfDznALy7VA7lmydoxJjGHYriru7vtZZRXljyfIhmdwDfFsR?key=7Eq3yv6nd6p1_hMEgz8-iw\" width=\"90\" height=\"91\"></strong></p>",
                                "<p><strong id=\"docs-internal-guid-e2e6c542-7fff-f8be-560c-************\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXd0k9yyqLSNhakWMkg1d261beXDw-L9aFXRUFjmY0ecjFgDChGKIr3ViFZ266jenbOTtQmBEfXE9-tAw6q5NXj0C8quuOeuf1mFXBsb1bAkuICLdgnTWp6i1flPmRr3aRLA1KhXTEKXFntLP5_OXYdPpUOj?key=7Eq3yv6nd6p1_hMEgz8-iw\" width=\"90\" height=\"93\"></strong></p>", "<p><strong id=\"docs-internal-guid-3675d27a-7fff-14c5-4fee-a823b3357775\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfb6GTWyN1EJFNS8wcWNiIc1boKxS0Cp5p9iuVbtCkzblbrN9NopuJIGFz3af7XeYsf8FaN2ETmTAt9HyZc7QlPGe4bYEuJuibSp51xmldtf5bjL4kNL7F2_7Be3-FgkZC7LP0iyjBPH6ADwBuuAlTIBnrb?key=7Eq3yv6nd6p1_hMEgz8-iw\" width=\"90\" height=\"92\"></strong></p>"],
                    solution_en: "<p>59.(d)<br><strong id=\"docs-internal-guid-3675d27a-7fff-14c5-4fee-a823b3357775\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfb6GTWyN1EJFNS8wcWNiIc1boKxS0Cp5p9iuVbtCkzblbrN9NopuJIGFz3af7XeYsf8FaN2ETmTAt9HyZc7QlPGe4bYEuJuibSp51xmldtf5bjL4kNL7F2_7Be3-FgkZC7LP0iyjBPH6ADwBuuAlTIBnrb?key=7Eq3yv6nd6p1_hMEgz8-iw\" width=\"90\" height=\"92\"></strong></p>",
                    solution_hi: "<p>59.(d)<br><strong id=\"docs-internal-guid-3675d27a-7fff-14c5-4fee-a823b3357775\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfb6GTWyN1EJFNS8wcWNiIc1boKxS0Cp5p9iuVbtCkzblbrN9NopuJIGFz3af7XeYsf8FaN2ETmTAt9HyZc7QlPGe4bYEuJuibSp51xmldtf5bjL4kNL7F2_7Be3-FgkZC7LP0iyjBPH6ADwBuuAlTIBnrb?key=7Eq3yv6nd6p1_hMEgz8-iw\" width=\"90\" height=\"92\"></strong></p>",
                    correct: "d",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "60",
                    section: "misc",
                    question_en: "<p>60. Which number will replace the question mark (?) in the following series?<br>3, 5, 10, 18, 29, ?, 60</p>",
                    question_hi: "<p>60. निम्नलिखित श्रृंखला में प्रश्न चिन्ह (?) के स्थान पर कौन सी संख्या आएगी ? <br>3, 5, 10, 18, 29, ?, 60</p>",
                    options_en: ["<p>46</p>", "<p>37</p>", 
                                "<p>45</p>", "<p>43</p>"],
                    options_hi: ["<p>46</p>", "<p>37</p>",
                                "<p>45</p>", "<p>43</p>"],
                    solution_en: "<p>60.(d)&nbsp;According to the question 3,5,10,18,29..??..60<br><strong id=\"docs-internal-guid-fd9a05e9-7fff-05cb-6e55-7abbe4a9ea67\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXegndJc9oVdKHHynA4OkfBMrak8tSij4nNGS1KPHgxk0A_VVPk8nc_xPF-vO6m6BncffSDAInf76EwMw0t7vJzOrSDvgd9jWN1NDwdWiuOS_DBAceImoYEPKYslpWmC_QGwokbI5YRpSMmQ_pnMROuYfscI?key=7Eq3yv6nd6p1_hMEgz8-iw\" width=\"200\" height=\"78\"></strong></p>",
                    solution_hi: "<p>60 (d)&nbsp;प्रश्न के अनुसार 3,5,10,18,29..??..60<br><strong id=\"docs-internal-guid-fd9a05e9-7fff-05cb-6e55-7abbe4a9ea67\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXegndJc9oVdKHHynA4OkfBMrak8tSij4nNGS1KPHgxk0A_VVPk8nc_xPF-vO6m6BncffSDAInf76EwMw0t7vJzOrSDvgd9jWN1NDwdWiuOS_DBAceImoYEPKYslpWmC_QGwokbI5YRpSMmQ_pnMROuYfscI?key=7Eq3yv6nd6p1_hMEgz8-iw\" width=\"200\" height=\"78\"></strong></p>",
                    correct: "d",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "61",
                    section: "misc",
                    question_en: "<p>1. <strong>Comprehension</strong>:<br>The very nature of the mind is restlessness. It cannot stay at one place or hold one thought for long. For every thought that appears, there are comments, judgements and associations. Thinking is a continuous activity with the mind jumping from one thought to another from morning till night. Like clouds in the sky or waves in the ocean, thoughts appear and disappear as if in ceaseless activity.<br>However, all thoughts that pass through our mind do not affect us. But we get affected when our ego is hit. Then the mind whirls and creates a tornado of restlessness within. A variety of probable scenarios crop up \'how dare he insult me; what does she think of herself? Where I am not respected, I will not go; if he speaks thus, I will reply so\'. And so it goes on and on.<br>We have an inbuilt filter in our mind which chooses the types of thoughts or subjects that we like to brood upon. We are not born with this filter but we acquire it over the years with the kind of books we read, the company we keep and the subjects we are interested in. That is why some people are obsessed with football, cricket or fashion while others could not care less for such things. This filter is built day by day by our actions, suggestions, teachings and influence of others. We can ultimately choose our own filter. So let us learn to build our filter wisely and strengthen it daily.<br>Thoughts affect us when our</p>",
                    question_hi: "<p>1. <strong>Comprehension</strong>:<br>The very nature of the mind is restlessness. It cannot stay at one place or hold one thought for long. For every thought that appears, there are comments, judgements and associations. Thinking is a continuous activity with the mind jumping from one thought to another from morning till night. Like clouds in the sky or waves in the ocean, thoughts appear and disappear as if in ceaseless activity.<br>However, all thoughts that pass through our mind do not affect us. But we get affected when our ego is hit. Then the mind whirls and creates a tornado of restlessness within. A variety of probable scenarios crop up \'how dare he insult me; what does she think of herself? Where I am not respected, I will not go; if he speaks thus, I will reply so\'. And so it goes on and on.<br>We have an inbuilt filter in our mind which chooses the types of thoughts or subjects that we like to brood upon. We are not born with this filter but we acquire it over the years with the kind of books we read, the company we keep and the subjects we are interested in. That is why some people are obsessed with football, cricket or fashion while others could not care less for such things. This filter is built day by day by our actions, suggestions, teachings and influence of others. We can ultimately choose our own filter. So let us learn to build our filter wisely and strengthen it daily.<br>Thoughts affect us when our</p>",
                    options_en: ["<p>job is affected.</p>", "<p>sleep is disturbed.</p>", 
                                "<p>learning is affected.</p>", "<p>pride is hurt.</p>"],
                    options_hi: ["<p>job is affected.</p>", "<p>sleep is disturbed.</p>",
                                "<p>learning is affected.</p>", "<p>pride is hurt.</p>"],
                    solution_en: "<p>1.(d) pride is hurt.<br>(Line/s from the passage - However, all thoughts that pass through our mind do not affect us. But we get affected when our ego is hit.)</p>",
                    solution_hi: "<p>1.(d) pride is hurt.<br>(Passage से ली गई lines - However, all thoughts that pass through our mind do not affect us. But we get affected when our ego is hit./हालाँकि, हमारे दिमाग से गुजरने वाले सभी विचार हमें प्रभावित नहीं करते हैं। लेकिन जब हमारे अहंकार पर चोट लगती है तो हम प्रभावित हो जाते हैं।)</p>",
                    correct: "d",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "62",
                    section: "misc",
                    question_en: "<p>2. <strong>Comprehension</strong>:<br>The very nature of the mind is restlessness. It cannot stay at one place or hold one thought for long. For every thought that appears, there are comments, judgements and associations. Thinking is a continuous activity with the mind jumping from one thought to another from morning till night. Like clouds in the sky or waves in the ocean, thoughts appear and disappear as if in ceaseless activity.<br>However, all thoughts that pass through our mind do not affect us. But we get affected when our ego is hit. Then the mind whirls and creates a tornado of restlessness within. A variety of probable scenarios crop up \'how dare he insult me; what does she think of herself? Where I am not respected, I will not go; if he speaks thus, I will reply so\'. And so it goes on and on.<br>We have an inbuilt filter in our mind which chooses the types of thoughts or subjects that we like to brood upon. We are not born with this filter but we acquire it over the years with the kind of books we read, the company we keep and the subjects we are interested in. That is why some people are obsessed with football, cricket or fashion while others could not care less for such things. This filter is built day by day by our actions, suggestions, teachings and influence of others. We can ultimately choose our own filter. So let us learn to build our filter wisely and strengthen it daily.<br>Read the following statements and determine which of these is correct ?<br>A.Our reading decides the filter in our minds.<br>B.The filter in our mind controls your likes but not dislikes.</p>",
                    question_hi: "<p>2. <strong>Comprehension</strong>:<br>The very nature of the mind is restlessness. It cannot stay at one place or hold one thought for long. For every thought that appears, there are comments, judgements and associations. Thinking is a continuous activity with the mind jumping from one thought to another from morning till night. Like clouds in the sky or waves in the ocean, thoughts appear and disappear as if in ceaseless activity.<br>However, all thoughts that pass through our mind do not affect us. But we get affected when our ego is hit. Then the mind whirls and creates a tornado of restlessness within. A variety of probable scenarios crop up \'how dare he insult me; what does she think of herself? Where I am not respected, I will not go; if he speaks thus, I will reply so\'. And so it goes on and on.<br>We have an inbuilt filter in our mind which chooses the types of thoughts or subjects that we like to brood upon. We are not born with this filter but we acquire it over the years with the kind of books we read, the company we keep and the subjects we are interested in. That is why some people are obsessed with football, cricket or fashion while others could not care less for such things. This filter is built day by day by our actions, suggestions, teachings and influence of others. We can ultimately choose our own filter. So let us learn to build our filter wisely and strengthen it daily.<br>Read the following statements and determine which of these is correct ?<br>A.Our reading decides the filter in our minds.<br>B.The filter in our mind controls your likes but not dislikes.</p>",
                    options_en: ["<p>Both A and B are correct.</p>", "<p>Both A and B are incorrect.</p>", 
                                "<p>A is correct and B is incorrect.</p>", "<p>A is incorrect and B is correct.</p>"],
                    options_hi: ["<p>Both A and B are correct.</p>", "<p>Both A and B are incorrect.</p>",
                                "<p>A is correct and B is incorrect.</p>", "<p>A is incorrect and B is correct.</p>"],
                    solution_en: "<p>2.(b) Both A and B are incorrect.<br>It can be inferred from the lines of the passage - (We are not born with this filter but we acquire it over the years with the kind of books we read, the company we keep and the subjects we are interested in.That is why some people are obsessed with football, cricket or fashion while others could not care less for such things.)</p>",
                    solution_hi: "<p>2.(b) Both A and B are incorrect.<br>Passage की पंक्तियों से इसका अनुमान लगाया जा सकता है - (हम इस गुण के साथ पैदा नहीं हुए हैं, लेकिन जिस तरह की किताबें हम पढ़ते हैं, जिस तरह की संगति रखते हैं और जिन विषयों में हमारी रुचि होती है, उससे हम इसे वर्षों में हासिल करते हैं। यही कारण है कि कुछ लोग फ़ुटबॉल, क्रिकेट या फ़ैशन के दीवाने हैं जबकि दूसरे लोग ऐसी चीज़ों की परवाह नहीं कर सकते।)</p>",
                    correct: "b",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "63",
                    section: "misc",
                    question_en: "<p>3. <strong>Comprehension</strong>:<br>The very nature of the mind is restlessness. It cannot stay at one place or hold one thought for long. For every thought that appears, there are comments, judgements and associations. Thinking is a continuous activity with the mind jumping from one thought to another from morning till night. Like clouds in the sky or waves in the ocean, thoughts appear and disappear as if in ceaseless activity.<br>However, all thoughts that pass through our mind do not affect us. But we get affected when our ego is hit. Then the mind whirls and creates a tornado of restlessness within. A variety of probable scenarios crop up \'how dare he insult me; what does she think of herself? Where I am not respected, I will not go; if he speaks thus, I will reply so\'. And so it goes on and on.<br>We have an inbuilt filter in our mind which chooses the types of thoughts or subjects that we like to brood upon. We are not born with this filter but we acquire it over the years with the kind of books we read, the company we keep and the subjects we are interested in. That is why some people are obsessed with football, cricket or fashion while others could not care less for such things. This filter is built day by day by our actions, suggestions, teachings and influence of others. We can ultimately choose our own filter. So let us learn to build our filter wisely and strengthen it daily.<br>Which of the following statements is incorrect?</p>",
                    question_hi: "<p>3. <strong>Comprehension:</strong><br>The very nature of the mind is restlessness. It cannot stay at one place or hold one thought for long. For every thought that appears, there are comments, judgements and associations. Thinking is a continuous activity with the mind jumping from one thought to another from morning till night. Like clouds in the sky or waves in the ocean, thoughts appear and disappear as if in ceaseless activity.<br>However, all thoughts that pass through our mind do not affect us. But we get affected when our ego is hit. Then the mind whirls and creates a tornado of restlessness within. A variety of probable scenarios crop up \'how dare he insult me; what does she think of herself? Where I am not respected, I will not go; if he speaks thus, I will reply so\'. And so it goes on and on.<br>We have an inbuilt filter in our mind which chooses the types of thoughts or subjects that we like to brood upon. We are not born with this filter but we acquire it over the years with the kind of books we read, the company we keep and the subjects we are interested in. That is why some people are obsessed with football, cricket or fashion while others could not care less for such things. This filter is built day by day by our actions, suggestions, teachings and influence of others. We can ultimately choose our own filter. So let us learn to build our filter wisely and strengthen it daily.<br>Which of the following statements is incorrect?</p>",
                    options_en: ["<p>Each one of us has an inborn filter in our mind.</p>", "<p>We like to be respected when we go somewhere.</p>", 
                                "<p>The filter in our minds influences our actions.</p>", "<p>Our thoughts do not remain stuck at one point.</p>"],
                    options_hi: ["<p>Each one of us has an inborn filter in our mind.</p>", "<p>We like to be respected when we go somewhere.</p>",
                                "<p>The filter in our minds influences our actions.</p>", "<p>Our thoughts do not remain stuck at one point.</p>"],
                    solution_en: "<p>3. (a) Each one of us has an inborn filter in our mind.<br>(Line/s from the passage - We are not born with this filter but we acquire it over the years with the kind of books we read, the company we keep and the subjects we are interested in.)</p>",
                    solution_hi: "<p>3. (a) Each one of us has an inborn filter in our mind.<br>(Passage से ली गई lines - We are not born with this filter but we acquire it over the years with the kind of books we read, the company we keep and the subjects we are interested in./हम इस गुण के साथ पैदा नहीं हुए हैं, लेकिन जिस तरह की किताबें हम पढ़ते हैं, जिस तरह की संगति रखते हैं और जिन विषयों में हमारी रुचि होती है, उससे हम इसे वर्षों में हासिल करते हैं।)</p>",
                    correct: "a",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "64",
                    section: "misc",
                    question_en: "<p>4. <strong>Comprehension:</strong><br>The very nature of the mind is restlessness. It cannot stay at one place or hold one thought for long. For every thought that appears, there are comments, judgements and associations. Thinking is a continuous activity with the mind jumping from one thought to another from morning till night. Like clouds in the sky or waves in the ocean, thoughts appear and disappear as if in ceaseless activity.<br>However, all thoughts that pass through our mind do not affect us. But we get affected when our ego is hit. Then the mind whirls and creates a tornado of restlessness within. A variety of probable scenarios crop up \'how dare he insult me; what does she think of herself? Where I am not respected, I will not go; if he speaks thus, I will reply so\'. And so it goes on and on.<br>We have an inbuilt filter in our mind which chooses the types of thoughts or subjects that we like to brood upon. We are not born with this filter but we acquire it over the years with the kind of books we read, the company we keep and the subjects we are interested in. That is why some people are obsessed with football, cricket or fashion while others could not care less for such things. This filter is built day by day by our actions, suggestions, teachings and influence of others. We can ultimately choose our own filter. So let us learn to build our filter wisely and strengthen it daily.<br>Which part of speech is the underlined word in the following expression?<br>\'But we get <span style=\"text-decoration: underline;\">affected\'</span></p>",
                    question_hi: "<p>4. <strong>Comprehension:</strong><br>The very nature of the mind is restlessness. It cannot stay at one place or hold one thought for long. For every thought that appears, there are comments, judgements and associations. Thinking is a continuous activity with the mind jumping from one thought to another from morning till night. Like clouds in the sky or waves in the ocean, thoughts appear and disappear as if in ceaseless activity.<br>However, all thoughts that pass through our mind do not affect us. But we get affected when our ego is hit. Then the mind whirls and creates a tornado of restlessness within. A variety of probable scenarios crop up \'how dare he insult me; what does she think of herself? Where I am not respected, I will not go; if he speaks thus, I will reply so\'. And so it goes on and on.<br>We have an inbuilt filter in our mind which chooses the types of thoughts or subjects that we like to brood upon. We are not born with this filter but we acquire it over the years with the kind of books we read, the company we keep and the subjects we are interested in. That is why some people are obsessed with football, cricket or fashion while others could not care less for such things. This filter is built day by day by our actions, suggestions, teachings and influence of others. We can ultimately choose our own filter. So let us learn to build our filter wisely and strengthen it daily.<br>Which part of speech is the underlined word in the following expression?<br>\'But we get <span style=\"text-decoration: underline;\">affected</span>\'</p>",
                    options_en: ["<p>Verb</p>", "<p>Adjective</p>", 
                                "<p>Adverb</p>", "<p>Particle</p>"],
                    options_hi: ["<p>Verb</p>", "<p>Adjective</p>",
                                "<p>Adverb</p>", "<p>Particle</p>"],
                    solution_en: "<p>4.(b) Adjective<br>&lsquo;Affected&rsquo; is an &lsquo;past participle adjective&rsquo; as it is describing the subject &lsquo;We&rsquo; and it is used as a subject complement here. A subject complement usually follows a &lsquo;linking verb&rsquo; which is &ldquo;get&rdquo; in the given sentence. Therefore option (b) is the most appropriate answer.</p>",
                    solution_hi: "<p>4.(b) Adjective<br>&lsquo;Affected&rsquo; एक &lsquo;past participle adjective&rsquo; है क्योंकि यह subject &lsquo;We&rsquo; का वर्णन कर रहा है और इसका उपयोग यहाँ एक subject complement के रूप में किया गया है। एक subject complement आमतौर पर &lsquo;linking verb&rsquo; के बाद आता है जो दिए गए वाक्य में &ldquo;get&rdquo; है। इसलिए विकल्प (b) सबसे उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "65",
                    section: "misc",
                    question_en: "<p>5. <strong>Comprehension:</strong><br>The very nature of the mind is restlessness. It cannot stay at one place or hold one thought for long. For every thought that appears, there are comments, judgements and associations. Thinking is a continuous activity with the mind jumping from one thought to another from morning till night. Like clouds in the sky or waves in the ocean, thoughts appear and disappear as if in ceaseless activity.<br>However, all thoughts that pass through our mind do not affect us. But we get affected when our ego is hit. Then the mind whirls and creates a tornado of restlessness within. A variety of probable scenarios crop up \'how dare he insult me; what does she think of herself? Where I am not respected, I will not go; if he speaks thus, I will reply so\'. And so it goes on and on.<br>We have an inbuilt filter in our mind which chooses the types of thoughts or subjects that we like to brood upon. We are not born with this filter but we acquire it over the years with the kind of books we read, the company we keep and the subjects we are interested in. That is why some people are obsessed with football, cricket or fashion while others could not care less for such things. This filter is built day by day by our actions, suggestions, teachings and influence of others. We can ultimately choose our own filter. So let us learn to build our filter wisely and strengthen it daily.<br>Which part of speech is the underlined word in the following expression?<br>\'It cannot stay at <span style=\"text-decoration: underline;\">one</span> place.\'</p>",
                    question_hi: "<p>5. <strong>Comprehension:</strong><br>The very nature of the mind is restlessness. It cannot stay at one place or hold one thought for long. For every thought that appears, there are comments, judgements and associations. Thinking is a continuous activity with the mind jumping from one thought to another from morning till night. Like clouds in the sky or waves in the ocean, thoughts appear and disappear as if in ceaseless activity.<br>However, all thoughts that pass through our mind do not affect us. But we get affected when our ego is hit. Then the mind whirls and creates a tornado of restlessness within. A variety of probable scenarios crop up \'how dare he insult me; what does she think of herself? Where I am not respected, I will not go; if he speaks thus, I will reply so\'. And so it goes on and on.<br>We have an inbuilt filter in our mind which chooses the types of thoughts or subjects that we like to brood upon. We are not born with this filter but we acquire it over the years with the kind of books we read, the company we keep and the subjects we are interested in. That is why some people are obsessed with football, cricket or fashion while others could not care less for such things. This filter is built day by day by our actions, suggestions, teachings and influence of others. We can ultimately choose our own filter. So let us learn to build our filter wisely and strengthen it daily.<br>Which part of speech is the underlined word in the following expression?<br>\'It cannot stay at <span style=\"text-decoration: underline;\">one</span> place.\'</p>",
                    options_en: ["<p>Determiner</p>", "<p>Pronoun</p>", 
                                "<p>Particle</p>", "<p>Adverb</p>"],
                    options_hi: ["<p>Determiner</p>", "<p>Pronoun</p>",
                                "<p>Particle</p>", "<p>Adverb</p>"],
                    solution_en: "<p>5.(a) Determiner<br>&lsquo;Determiner&rsquo; - a modifying word that is used before a noun that determines the kind of reference a noun has. Here, the underlined part &ldquo;one&rdquo; determines the reference of the noun &lsquo;place&rsquo;. Therefore, option (a) is the most appropriate answer.</p>",
                    solution_hi: "<p>5.(a) Determiner<br>&lsquo;Determiner&rsquo; - एक संशोधित शब्द जो एक noun से पहले प्रयोग किया जाता है जो एक noun के प्रकार को निर्धारित करता है। यहाँ, रेखांकित भाग &ldquo;one&rdquo; noun &lsquo;place&rsquo; को निर्धारित करता है। इसलिए, विकल्प (a) सबसे उपयुक्त उत्तर है।</p>",
                    correct: "a",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "66",
                    section: "misc",
                    question_en: "<p>6. <strong>Comprehension:</strong><br>The very nature of the mind is restlessness. It cannot stay at one place or hold one thought for long. For every thought that appears, there are comments, judgements and associations. Thinking is a continuous activity with the mind jumping from one thought to another from morning till night. Like clouds in the sky or waves in the ocean, thoughts appear and disappear as if in ceaseless activity.<br>However, all thoughts that pass through our mind do not affect us. But we get affected when our ego is hit. Then the mind whirls and creates a tornado of restlessness within. A variety of probable scenarios crop up \'how dare he insult me; what does she think of herself? Where I am not respected, I will not go; if he speaks thus, I will reply so\'. And so it goes on and on.<br>We have an inbuilt filter in our mind which chooses the types of thoughts or subjects that we like to brood upon. We are not born with this filter but we acquire it over the years with the kind of books we read, the company we keep and the subjects we are interested in. That is why some people are obsessed with football, cricket or fashion while others could not care less for such things. This filter is built day by day by our actions, suggestions, teachings and influence of others. We can ultimately choose our own filter. So let us learn to build our filter wisely and strengthen it daily.<br>As if in ceaseless activity The word \'ceaseless\' means</p>",
                    question_hi: "<p>6.<strong> Comprehension:</strong><br>The very nature of the mind is restlessness. It cannot stay at one place or hold one thought for long. For every thought that appears, there are comments, judgements and associations. Thinking is a continuous activity with the mind jumping from one thought to another from morning till night. Like clouds in the sky or waves in the ocean, thoughts appear and disappear as if in ceaseless activity.<br>However, all thoughts that pass through our mind do not affect us. But we get affected when our ego is hit. Then the mind whirls and creates a tornado of restlessness within. A variety of probable scenarios crop up \'how dare he insult me; what does she think of herself? Where I am not respected, I will not go; if he speaks thus, I will reply so\'. And so it goes on and on.<br>We have an inbuilt filter in our mind which chooses the types of thoughts or subjects that we like to brood upon. We are not born with this filter but we acquire it over the years with the kind of books we read, the company we keep and the subjects we are interested in. That is why some people are obsessed with football, cricket or fashion while others could not care less for such things. This filter is built day by day by our actions, suggestions, teachings and influence of others. We can ultimately choose our own filter. So let us learn to build our filter wisely and strengthen it daily.<br>As if in ceaseless activity The word \'ceaseless\' means</p>",
                    options_en: ["<p>permanent</p>", "<p>continuous</p>", 
                                "<p>temporary</p>", "<p>flawless</p>"],
                    options_hi: ["<p>permanent</p>", "<p>continuous</p>",
                                "<p>temporary</p>", "<p>flawless</p>"],
                    solution_en: "<p>6.(b) <strong>continuous</strong><br>&lsquo;<strong>Ceaseless</strong>&rsquo; - constant and unending<br>&lsquo;<strong>Flawless</strong>&rsquo; - perfect or without mistakes<br>&lsquo;<strong>Temporary</strong>&rsquo; - lasting for only a limited period of time; not permanent.<br><strong>&lsquo;Permanent&rsquo;</strong> - lasting for a long time or for ever</p>",
                    solution_hi: "<p>6. (b) <strong>continuous</strong><br>&lsquo;<strong>Ceaseless</strong>&rsquo; - निरंतर और अंतहीन<br>&lsquo;<strong>Flawless</strong>&rsquo; - पूर्ण या त्रुटि रहित<br>&lsquo;<strong>Temporary</strong>&rsquo; - केवल एक सीमित अवधि के लिए स्थायी; अस्थिर<br>&lsquo;<strong>Permanent</strong>&rsquo; - लंबे समय तक या हमेशा के लिए चलने वाला</p>",
                    correct: "b",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "67",
                    section: "misc",
                    question_en: "<p>7. <strong>Comprehension:</strong><br>The very nature of the mind is restlessness. It cannot stay at one place or hold one thought for long. For every thought that appears, there are comments, judgements and associations. Thinking is a continuous activity with the mind jumping from one thought to another from morning till night. Like clouds in the sky or waves in the ocean, thoughts appear and disappear as if in ceaseless activity.<br>However, all thoughts that pass through our mind do not affect us. But we get affected when our ego is hit. Then the mind whirls and creates a tornado of restlessness within. A variety of probable scenarios crop up \'how dare he insult me; what does she think of herself? Where I am not respected, I will not go; if he speaks thus, I will reply so\'. And so it goes on and on.<br>We have an inbuilt filter in our mind which chooses the types of thoughts or subjects that we like to brood upon. We are not born with this filter but we acquire it over the years with the kind of books we read, the company we keep and the subjects we are interested in. That is why some people are obsessed with football, cricket or fashion while others could not care less for such things. This filter is built day by day by our actions, suggestions, teachings and influence of others. We can ultimately choose our own filter. So let us learn to build our filter wisely and strengthen it daily.<br>\'Creates a tornado of restlessness The word \'tornado\' here means</p>",
                    question_hi: "<p>7. <strong>Comprehension:</strong><br>The very nature of the mind is restlessness. It cannot stay at one place or hold one thought for long. For every thought that appears, there are comments, judgements and associations. Thinking is a continuous activity with the mind jumping from one thought to another from morning till night. Like clouds in the sky or waves in the ocean, thoughts appear and disappear as if in ceaseless activity.<br>However, all thoughts that pass through our mind do not affect us. But we get affected when our ego is hit. Then the mind whirls and creates a tornado of restlessness within. A variety of probable scenarios crop up \'how dare he insult me; what does she think of herself? Where I am not respected, I will not go; if he speaks thus, I will reply so\'. And so it goes on and on.<br>We have an inbuilt filter in our mind which chooses the types of thoughts or subjects that we like to brood upon. We are not born with this filter but we acquire it over the years with the kind of books we read, the company we keep and the subjects we are interested in. That is why some people are obsessed with football, cricket or fashion while others could not care less for such things. This filter is built day by day by our actions, suggestions, teachings and influence of others. We can ultimately choose our own filter. So let us learn to build our filter wisely and strengthen it daily.<br>Creates a tornado of restlessness The word \'tornado\' here means</p>",
                    options_en: ["<p>waterfall</p>", "<p>confusion</p>", 
                                "<p>storm</p>", "<p>mixture</p>"],
                    options_hi: ["<p>waterfall</p>", "<p>confusion</p>",
                                "<p>storm</p>", "<p>mixture</p>"],
                    solution_en: "<p>7.(c) storm - a violent disturbance of the atmosphere with strong winds and usually rain, thunder, lightning, or snow. <br>&lsquo;Tornado&rsquo; - A violently rotating column of air touching the ground, usually attached to the base of a thunderstorm.</p>",
                    solution_hi: "<p>7.(c) storm - तूफ़ान ।<br>&lsquo;Tornado&rsquo; - बवंडर।</p>",
                    correct: "c",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "68",
                    section: "misc",
                    question_en: "<p>8. <strong>Comprehension:</strong><br>The very nature of the mind is restlessness. It cannot stay at one place or hold one thought for long. For every thought that appears, there are comments, judgements and associations. Thinking is a continuous activity with the mind jumping from one thought to another from morning till night. Like clouds in the sky or waves in the ocean, thoughts appear and disappear as if in ceaseless activity.<br>However, all thoughts that pass through our mind do not affect us. But we get affected when our ego is hit. Then the mind whirls and creates a tornado of restlessness within. A variety of probable scenarios crop up \'how dare he insult me; what does she think of herself? Where I am not respected, I will not go; if he speaks thus, I will reply so\'. And so it goes on and on.<br>We have an inbuilt filter in our mind which chooses the types of thoughts or subjects that we like to brood upon. We are not born with this filter but we acquire it over the years with the kind of books we read, the company we keep and the subjects we are interested in. That is why some people are obsessed with football, cricket or fashion while others could not care less for such things. This filter is built day by day by our actions, suggestions, teachings and influence of others. We can ultimately choose our own filter. So let us learn to build our filter wisely and strengthen it daily.<br>Thoughts are compared to</p>",
                    question_hi: "<p>8. <strong>Comprehension:</strong><br>The very nature of the mind is restlessness. It cannot stay at one place or hold one thought for long. For every thought that appears, there are comments, judgements and associations. Thinking is a continuous activity with the mind jumping from one thought to another from morning till night. Like clouds in the sky or waves in the ocean, thoughts appear and disappear as if in ceaseless activity.<br>However, all thoughts that pass through our mind do not affect us. But we get affected when our ego is hit. Then the mind whirls and creates a tornado of restlessness within. A variety of probable scenarios crop up \'how dare he insult me; what does she think of herself? Where I am not respected, I will not go; if he speaks thus, I will reply so\'. And so it goes on and on.<br>We have an inbuilt filter in our mind which chooses the types of thoughts or subjects that we like to brood upon. We are not born with this filter but we acquire it over the years with the kind of books we read, the company we keep and the subjects we are interested in. That is why some people are obsessed with football, cricket or fashion while others could not care less for such things. This filter is built day by day by our actions, suggestions, teachings and influence of others. We can ultimately choose our own filter. So let us learn to build our filter wisely and strengthen it daily.<br>Thoughts are compared to</p>",
                    options_en: ["<p>comments</p>", "<p>judgements</p>", 
                                "<p>associations</p>", "<p>clouds</p>"],
                    options_hi: ["<p>comments</p>", "<p>judgements</p>",
                                "<p>associations</p>", "<p>clouds</p>"],
                    solution_en: "<p>8.(d) clouds<br>(Line/s from the passage - Thinking is a continuous activity with the mind jumping from one thought to another from morning till night. Like clouds in the sky or waves in the ocean, thoughts appear and disappear as if in ceaseless activity.)</p>",
                    solution_hi: "<p>8.(d) clouds<br>(Passage से ली गई lines - Thinking is a continuous activity with the mind jumping from one thought to another from morning till night. Like clouds in the sky or waves in the ocean, thoughts appear and disappear as if in ceaseless activity./ Thinking एक continuous activity है जिसमें मन सुबह से रात तक एक विचार से दूसरे विचार के बारे में सोचता रहता है। आकाश में बादलों की तरह या समुद्र में लहरों की तरह, विचार निरंतर प्रकट होते हैं और गायब हो जाते हैं।)</p>",
                    correct: "d",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "69",
                    section: "misc",
                    question_en: "<p>9. <strong>Comprehension:</strong><br>The very nature of the mind is restlessness. It cannot stay at one place or hold one thought for long. For every thought that appears, there are comments, judgements and associations. Thinking is a continuous activity with the mind jumping from one thought to another from morning till night. Like clouds in the sky or waves in the ocean, thoughts appear and disappear as if in ceaseless activity.<br>However, all thoughts that pass through our mind do not affect us. But we get affected when our ego is hit. Then the mind whirls and creates a tornado of restlessness within. A variety of probable scenarios crop up \'how dare he insult me; what does she think of herself? Where I am not respected, I will not go; if he speaks thus, I will reply so\'. And so it goes on and on.<br>We have an inbuilt filter in our mind which chooses the types of thoughts or subjects that we like to brood upon. We are not born with this filter but we acquire it over the years with the kind of books we read, the company we keep and the subjects we are interested in. That is why some people are obsessed with football, cricket or fashion while others could not care less for such things. This filter is built day by day by our actions, suggestions, teachings and influence of others. We can ultimately choose our own filter. So let us learn to build our filter wisely and strengthen it daily.<br>The process of thinking continues from</p>",
                    question_hi: "<p>9. <strong>Comprehension:</strong><br>The very nature of the mind is restlessness. It cannot stay at one place or hold one thought for long. For every thought that appears, there are comments, judgements and associations. Thinking is a continuous activity with the mind jumping from one thought to another from morning till night. Like clouds in the sky or waves in the ocean, thoughts appear and disappear as if in ceaseless activity.<br>However, all thoughts that pass through our mind do not affect us. But we get affected when our ego is hit. Then the mind whirls and creates a tornado of restlessness within. A variety of probable scenarios crop up \'how dare he insult me; what does she think of herself? Where I am not respected, I will not go; if he speaks thus, I will reply so\'. And so it goes on and on.<br>We have an inbuilt filter in our mind which chooses the types of thoughts or subjects that we like to brood upon. We are not born with this filter but we acquire it over the years with the kind of books we read, the company we keep and the subjects we are interested in. That is why some people are obsessed with football, cricket or fashion while others could not care less for such things. This filter is built day by day by our actions, suggestions, teachings and influence of others. We can ultimately choose our own filter. So let us learn to build our filter wisely and strengthen it daily.<br>The process of thinking continues from</p>",
                    options_en: ["<p>week to week</p>", "<p>morning to night</p>", 
                                "<p>day to day</p>", "<p>year to year</p>"],
                    options_hi: ["<p>week to week</p>", "<p>morning to night</p>",
                                "<p>day to day</p>", "<p>year to year</p>"],
                    solution_en: "<p>9.(b) morning to night<br>(Line/s from the passage - Thinking is a continuous activity with the mind jumping from one thought to another from morning till night)</p>",
                    solution_hi: "<p>9.(b) morning to night<br>(Passage से ली गई lines - Thinking is a continuous activity with the mind jumping from one thought to another from morning till night./Thinking एक continuous activity है जिसमें मन सुबह से रात तक एक विचार से दूसरे विचार के बारे में सोचता रहता है।)</p>",
                    correct: "b",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "70",
                    section: "misc",
                    question_en: "<p>10. <strong>Comprehension:</strong><br>The very nature of the mind is restlessness. It cannot stay at one place or hold one thought for long. For every thought that appears, there are comments, judgements and associations. Thinking is a continuous activity with the mind jumping from one thought to another from morning till night. Like clouds in the sky or waves in the ocean, thoughts appear and disappear as if in ceaseless activity.<br>However, all thoughts that pass through our mind do not affect us. But we get affected when our ego is hit. Then the mind whirls and creates a tornado of restlessness within. A variety of probable scenarios crop up \'how dare he insult me; what does she think of herself? Where I am not respected, I will not go; if he speaks thus, I will reply so\'. And so it goes on and on.<br>We have an inbuilt filter in our mind which chooses the types of thoughts or subjects that we like to brood upon. We are not born with this filter but we acquire it over the years with the kind of books we read, the company we keep and the subjects we are interested in. That is why some people are obsessed with football, cricket or fashion while others could not care less for such things. This filter is built day by day by our actions, suggestions, teachings and influence of others. We can ultimately choose our own filter. So let us learn to build our filter wisely and strengthen it daily.<br>What kind of scenarios are not formed in mind when our pride is hurt?</p>",
                    question_hi: "<p>10.<strong> Comprehension:</strong><br>The very nature of the mind is restlessness. It cannot stay at one place or hold one thought for long. For every thought that appears, there are comments, judgements and associations. Thinking is a continuous activity with the mind jumping from one thought to another from morning till night. Like clouds in the sky or waves in the ocean, thoughts appear and disappear as if in ceaseless activity.<br>However, all thoughts that pass through our mind do not affect us. But we get affected when our ego is hit. Then the mind whirls and creates a tornado of restlessness within. A variety of probable scenarios crop up \'how dare he insult me; what does she think of herself? Where I am not respected, I will not go; if he speaks thus, I will reply so\'. And so it goes on and on.<br>We have an inbuilt filter in our mind which chooses the types of thoughts or subjects that we like to brood upon. We are not born with this filter but we acquire it over the years with the kind of books we read, the company we keep and the subjects we are interested in. That is why some people are obsessed with football, cricket or fashion while others could not care less for such things. This filter is built day by day by our actions, suggestions, teachings and influence of others. We can ultimately choose our own filter. So let us learn to build our filter wisely and strengthen it daily.<br>What kind of scenarios are not formed in mind when our pride is hurt?</p>",
                    options_en: ["<p>how dare he insult me</p>", "<p>if he speaks thus, I will reply so</p>", 
                                "<p>no matter whatever happens i will be at peace</p>", "<p>Where I am not respected, I will not go</p>"],
                    options_hi: ["<p>how dare he insult me</p>", "<p>if he speaks thus, I will reply so</p>",
                                "<p>no matter whatever happens i will be at peace</p>", "<p>Where I am not respected, I will not go</p>"],
                    solution_en: "<p>10.(c) no matter whatever happens i will be at peace<br>(Line/s from the passage - A variety of probable scenarios crop up \'how dare he insult me; what does she think of herself? Where I am not respected, I will not go; if he speaks thus, I will reply so\'. And so it goes on and on.)</p>",
                    solution_hi: "<p>10.(c) no matter whatever happens i will be at peace<br>(Passage से ली गई lines - A variety of probable scenarios crop up \'how dare he insult me; what does she think of herself? Where I am not respected, I will not go; if he speaks thus, I will reply so\'. And so it goes on and on./तरह-तरह के संभावित परिदृश्य सामने आते हैं \'उसकी हिम्मत कैसे हुई मेरा अपमान करने की; वह अपने बारे में क्या सोचती है? जहां मेरा सम्मान नहीं है, वहां मैं नहीं जाऊंगा; यदि वह ऐसा बोलता है, तो मैं ऐसा उत्तर दूंगा\'। और यह इसी तरह चलता रहता है।)</p>",
                    correct: "c",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "71",
                    section: "misc",
                    question_en: "<p>11. <strong>Comprehension:</strong><br>As to happiness, I am not so sure. Birds, it is true, die of hunger in large numbers during the winter, if they are not birds of passage. But during the summer they do not foresee this catastrophe, or remember how nearly it befell them in the previous winter. With human beings the matter is otherwise. I doubt whether the percentage of birds that will have died of hunger during the present winter (1946-47) is as great as the percentage of human beings that will have died from this cause in India and central Europe during the same period. But every human death by starvation is preceded by a long period of anxiety, and surrounded by the corresponding anxiety of neighbours. We suffer not only the evils that actually befall us, but all those that our intelligence tells us we have reason to fear.<br>The curbing of impulses to which we are led by forethought averts physical disaster at the cost of worry, and general lack of joy. I do not think that the learned men of my acquaintance, even when they enjoy a secure income, are as happy as the mice that eat the crumbs from their tables while the erudite gentlemen snooze. In this respect, therefore, I am not convinced that there has been any progress at all.<br>The birds die of hunger in winter because</p>",
                    question_hi: "<p>11.<strong> Comprehension:</strong><br>As to happiness, I am not so sure. Birds, it is true, die of hunger in large numbers during the winter, if they are not birds of passage. But during the summer they do not foresee this catastrophe, or remember how nearly it befell them in the previous winter. With human beings the matter is otherwise. I doubt whether the percentage of birds that will have died of hunger during the present winter (1946-47) is as great as the percentage of human beings that will have died from this cause in India and central Europe during the same period. But every human death by starvation is preceded by a long period of anxiety, and surrounded by the corresponding anxiety of neighbours. We suffer not only the evils that actually befall us, but all those that our intelligence tells us we have reason to fear.<br>The curbing of impulses to which we are led by forethought averts physical disaster at the cost of worry, and general lack of joy. I do not think that the learned men of my acquaintance, even when they enjoy a secure income, are as happy as the mice that eat the crumbs from their tables while the erudite gentlemen snooze. In this respect, therefore, I am not convinced that there has been any progress at all.<br>The birds die of hunger in winter because</p>",
                    options_en: ["<p>they do not move to warmer places</p>", "<p>people do not feed them</p>", 
                                "<p>they do not get the food of their</p>", "<p>they are too young to get the food</p>"],
                    options_hi: ["<p>they do not move to warmer places</p>", "<p>people do not feed them</p>",
                                "<p>they do not get the food of their</p>", "<p>they are too young to get the food</p>"],
                    solution_en: "<p>11.(a) they do not move to warmer places<br>(Line/s form the passage - Birds, it is true, die of hunger in large numbers during the winter, if they are not birds of passage.)</p>",
                    solution_hi: "<p>11.(a) they do not move to warmer places<br>(Passage से ली गई lines - Birds, it is true, die of hunger in large numbers during the winter, if they are not birds of passage./यह सच है कि इस दौरान सर्दी में बड़ी संख्या में पक्षी भूख से मरते हैं, अगर वे प्रवासी पक्षी नहीं हैं।)</p>",
                    correct: "a",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "72",
                    section: "misc",
                    question_en: "<p>12.<strong> Comprehension:</strong><br>As to happiness, I am not so sure. Birds, it is true, die of hunger in large numbers during the winter, if they are not birds of passage. But during the summer they do not foresee this catastrophe, or remember how nearly it befell them in the previous winter. With human beings the matter is otherwise. I doubt whether the percentage of birds that will have died of hunger during the present winter (1946-47) is as great as the percentage of beings that will have died from this cause in India and central Europe during the same period. But every human death by starvation is preceded by a long period of anxiety, and surrounded by the corresponding anxiety of neighbours. We suffer not only the evils that actually befall us, but all those that our intelligence tells us we have reason to fear.<br>The curbing of impulses to which we are led by forethought averts physical disaster at the cost of worry, and general lack of joy. I do not think that the learned men of my acquaintance, even when they enjoy a secure income, are as happy as the mice that eat the crumbs from their tables while the erudite gentlemen snooze. In this respect, therefore, I am not convinced that there has been any progress at all.<br>The birds do not foresee the cata-strophe because they</p>",
                    question_hi: "<p>12. <strong>Comprehension:</strong><br>As to happiness, I am not so sure. Birds, it is true, die of hunger in large numbers during the winter, if they are not birds of passage. But during the summer they do not foresee this catastrophe, or remember how nearly it befell them in the previous winter. With human beings the matter is otherwise. I doubt whether the percentage of birds that will have died of hunger during the present winter (1946-47) is as great as the percentage of human beings that will have died from this cause in India and central Europe during the same period. But every human death by starvation is preceded by a long period of anxiety, and surrounded by the corresponding anxiety of neighbours. We suffer not only the evils that actually befall us, but all those that our intelligence tells us we have reason to fear.<br>The curbing of impulses to which we are led by forethought averts physical disaster at the cost of worry, and general lack of joy. I do not think that the learned men of my acquaintance, even when they enjoy a secure income, are as happy as the mice that eat the crumbs from their tables while the erudite gentlemen snooze. In this respect, therefore, I am not convinced that there has been any progress at all.<br>The birds do not foresee the cata-strophe because they</p>",
                    options_en: ["<p>cannot predict an accident</p>", "<p>overlook a difficult situation</p>", 
                                "<p>cannot expect a sudden disaster</p>", "<p>ignore the problems</p>"],
                    options_hi: ["<p>cannot predict an accident</p>", "<p>overlook a difficult situation</p>",
                                "<p>cannot expect a sudden disaster</p>", "<p>ignore the problems</p>"],
                    solution_en: "<p>12. (a) cannot predict an accident<br>(Line/s from the passage - But during the summer they do not foresee this catastrophe, or remember how nearly it befell them in the previous winter.)</p>",
                    solution_hi: "<p>12. (a) cannot predict an accident<br>(Passage से ली गई lines - But during the summer they do not foresee this catastrophe, or remember how nearly it befell them in the previous winter./लेकिन गर्मियों के दौरान वे इस तबाही की उम्मीद नहीं करते हैं, या यह याद नहीं रखते हैं कि पिछली सर्दियों में यह उन पर कितना भारी पड़ा था।)</p>",
                    correct: "a",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "73",
                    section: "misc",
                    question_en: "<p>13. <strong>Comprehension:</strong><br>As to happiness, I am not so sure. Birds, it is true, die of hunger in large numbers during the winter, if they are not birds of passage. But during the summer they do not foresee this catastrophe, or remember how nearly it befell them in the previous winter. With human beings the matter is otherwise. I doubt whether the percentage of birds that will have died of hunger during the present winter (1946-47) is as great as the percentage of human beings that will have died from this cause in India and central Europe during the same period. But every human death by starvation is preceded by a long period of anxiety, and surrounded by the corresponding anxiety of neighbours. We suffer not only the evils that actually befall us, but all those that our intelligence tells us we have reason to fear. <br>The curbing of impulses to which we are led by forethought averts physical disaster at the cost of worry, and general lack of joy. I do not think that the learned men of my acquaintance, even when they enjoy a secure income, are as happy as the mice that eat the crumbs from their tables while the erudite gentlemen snooze. In this respect, therefore, I am not convinced that there has been any progress at all.<br>Human beings cannot be happy because they</p>",
                    question_hi: "<p>13. Comprehension:<br>As to happiness, I am not so sure. Birds, it is true, die of hunger in large numbers during the winter, if they are not birds of passage. But during the summer they do not foresee this catastrophe, or remember how nearly it befell them in the previous winter. With human beings the matter is otherwise. I doubt whether the percentage of birds that will have died of hunger during the present winter (1946-47) is as great as the percentage of human beings that will have died from this cause in India and central Europe during the same period. But every human death by starvation is preceded by a long period of anxiety, and surrounded by the corresponding anxiety of neighbours. We suffer not only the evils that actually befall us, but all those that our intelligence tells us we have reason to fear. <br>The curbing of impulses to which we are led by forethought averts physical disaster at the cost of worry, and general lack of joy. I do not think that the learned men of my acquaintance, even when they enjoy a secure income, are as happy as the mice that eat the crumbs from their tables while the erudite gentlemen snooze. In this respect, therefore, I am not convinced that there has been any progress at all.<br>Human beings cannot be happy because they</p>",
                    options_en: ["<p>do not get time to enjoy</p>", "<p>worry too much about their work</p>", 
                                "<p>are not healthy</p>", "<p>worry too much about future</p>"],
                    options_hi: ["<p>do not get time to enjoy</p>", "<p>worry too much about their work</p>",
                                "<p>are not healthy</p>", "<p>worry too much about future</p>"],
                    solution_en: "<p>13.(d) worry too much about future<br>(Line/s from the passage -The curbing of impulses to which we are led by forethought averts physical disaster at the cost of worry, and general lack of joy.)</p>",
                    solution_hi: "<p>13.(d) worry too much about future<br>(Passage से ली गई lines -The curbing of impulses to which we are led by forethought averts physical disaster at the cost of worry, and general lack of joy./उन आवेगों पर अंकुश लगाना, जिनकी हम अगुवाई करते हैं, चिंता की कीमत पर शारीरिक आपदा को टालते हैं, और जीवन का कम आनंद ले पाते है।)</p>",
                    correct: "d",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "74",
                    section: "misc",
                    question_en: "<p>14. <strong>Comprehension:</strong><br>As to happiness, I am not so sure. Birds, it is true, die of hunger in large numbers during the winter, if they are not birds of passage. But during the summer they do not foresee this catastrophe, or remember how nearly it befell them in the previous winter. With human beings the matter is otherwise. I doubt whether the percentage of birds that will have died of hunger during the present winter (1946-47) is as great as the percentage of human beings that will have died from this cause in India and central Europe during the same period. But every human death by starvation is preceded by a long period of anxiety, and surrounded by the corresponding anxiety of neighbours. We suffer not only the evils that actually befall us, but all those that our intelligence tells us we have reason to fear. <br>The curbing of impulses to which we are led by forethought averts physical disaster at the cost of worry, and general lack of joy. I do not think that the learned men of my acquaintance, even when they enjoy a secure income, are as happy as the mice that eat the crumbs from their tables while the erudite gentlemen snooze. In this respect, therefore, I am not convinced that there has been any progress at all.<br>Which one of the following is the antonym of the word \'erudite\' in the&nbsp;passage?</p>",
                    question_hi: "<p>14. <strong>Comprehension:</strong><br>As to happiness, I am not so sure. Birds, it is true, die of hunger in large numbers during the winter, if they are not birds of passage. But during the summer they do not foresee this catastrophe, or remember how nearly it befell them in the previous winter. With human beings the matter is otherwise. I doubt whether the percentage of birds that will have died of hunger during the present winter (1946-47) is as great as the percentage of human beings that will have died from this cause in India and central Europe during the same period. But every human death by starvation is preceded by a long period of anxiety, and surrounded by the corresponding anxiety of neighbours. We suffer not only the evils that actually befall us, but all those that our intelligence tells us we have reason to fear. <br>The curbing of impulses to which we are led by forethought averts physical disaster at the cost of worry, and general lack of joy. I do not think that the learned men of my acquaintance, even when they enjoy a secure income, are as happy as the mice that eat the crumbs from their tables while the erudite gentlemen snooze. In this respect, therefore, I am not convinced that there has been any progress at all.<br>Which one of the following is the antonym of the word \'erudite\' in the&nbsp;passage?</p>",
                    options_en: ["<p>Qualified</p>", "<p>ill-educated</p>", 
                                "<p>Logical</p>", "<p>Learned</p>"],
                    options_hi: ["<p>Qualified</p>", "<p>ill-educated</p>",
                                "<p>Logical</p>", "<p>Learned</p>"],
                    solution_en: "<p>14.(b) ill-educated</p>",
                    solution_hi: "<p>14.(b) ill-educated<br>\'Erudite\' - विद्वान<br>&lsquo;Qualified&rsquo; - योग्य<br>&lsquo;Learned&rsquo; - किसी व्यक्ति का अध्ययन के माध्यम से बहुत ज्ञान प्राप्त करना।<br>&lsquo;Logical&rsquo; - तर्क और विचारों पर आधारित</p>",
                    correct: "b",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "75",
                    section: "misc",
                    question_en: "<p>15. <strong>Comprehension:</strong><br>As to happiness, I am not so sure. Birds, it is true, die of hunger in large numbers during the winter, if they are not birds of passage. But during the summer they do not foresee this catastrophe, or remember how nearly it befell them in the previous winter. With human beings the matter is otherwise. I doubt whether the percentage of birds that will have died of hunger during the present winter (1946-47) is as great as the percentage of human beings that will have died from this cause in India and central Europe during the same period. But every human death by starvation is preceded by a long period of anxiety, and surrounded by the corresponding anxiety of neighbours. We suffer not only the evils that actually befall us, but all those that our intelligence tells us we have reason to fear. <br>The curbing of impulses to which we are led by forethought averts physical disaster at the cost of worry, and general lack of joy. I do not think that the learned men of my acquaintance, even when they enjoy a secure income, are as happy as the mice that eat the crumbs from their tables while the erudite gentlemen snooze. In this respect, therefore, I am not convinced that there has been any progress at all.<br>Which one of the following is the central theme of the passage?</p>",
                    question_hi: "<p>15. <strong>Comprehension:</strong><br>As to happiness, I am not so sure. Birds, it is true, die of hunger in large numbers during the winter, if they are not birds of passage. But during the summer they do not foresee this catastrophe, or remember how nearly it befell them in the previous winter. With human beings the matter is otherwise. I doubt whether the percentage of birds that will have died of hunger during the present winter (1946-47) is as great as the percentage of human beings that will have died from this cause in India and central Europe during the same period. But every human death by starvation is preceded by a long period of anxiety, and surrounded by the corresponding anxiety of neighbours. We suffer not only the evils that actually befall us, but all those that our intelligence tells us we have reason to fear. <br>The curbing of impulses to which we are led by forethought averts physical disaster at the cost of worry, and general lack of joy. I do not think that the learned men of my acquaintance, even when they enjoy a secure income, are as happy as the mice that eat the crumbs from their tables while the erudite gentlemen snooze. In this respect, therefore, I am not convinced that there has been any progress at all.<br>Which one of the following is the central theme of the passage?</p>",
                    options_en: ["<p>Life of the birds and the mice</p>", "<p>Starvation in India and central Europe</p>", 
                                "<p>Progress of mankind</p>", "<p>Disasters in 1946-47</p>"],
                    options_hi: ["<p>Life of the birds and the mice</p>", "<p>Starvation in India and central Europe</p>",
                                "<p>Progress of mankind</p>", "<p>Disasters in 1946-47</p>"],
                    solution_en: "<p>15.(c) Progress of mankind<br>It can be inferred from the lines of the passage &ldquo;In this respect, therefore, I am not convinced that there has been any progress at all.&rdquo; that although the progress of mankind had been made so far, but the author was still not convinced.</p>",
                    solution_hi: "<p>15.(c) Progress of mankind<br>इसका अनुमान passage की पंक्तियों से लगाया जा सकता है &ldquo;In this respect, therefore, I am not convinced that there has been any progress at all.&rdquo; यद्यपि मानव जाति की प्रगति अब हो चुकी है, फिर भी लेखक आश्वस्त नहीं था।</p>",
                    correct: "c",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "76",
                    section: "misc",
                    question_en: "<p>16. <strong>Comprehension:</strong><br>How wonderful is the living world ! The wide range of the living types is amazing. The extraordinary habitats in which we find living organisms, be it cold mountains, deciduous forests, oceans, freshwater lakes, deserts or hot springs, leave us speechless. The beauty of a galloping horse, or a migrating bird, the valley of flowers or the attacking shark evokes awe and a deep sense of wonder. The ecological conflict and cooperation among members of a population and among populations of a community or even a molecular&nbsp;traffic inside a cell make us deeply reflect on what indeed is life? This question has two implicit questions within it. The first is a technical one and seeks answer to what living is as opposed to the non-living, and the second is the philosophical one, and seeks&nbsp;answer to what the purpose of life is. What is living? When we try to define \'living\', we conventionally look for distinctive characteristics exhibited by living organisms. Growth, reproduction, ability to sense environment and mount a suitable response come to our mind immediately as unique features of living organisms. One can add a few more features like metabolism, ability to self-replicate, self-organize, interact and emergence to this list.<br>Why are the living types amazing?</p>",
                    question_hi: "<p>16. <strong>Comprehension:</strong><br>How wonderful is the living world ! The wide range of the living types is amazing. The extraordinary habitats in which we find living organisms, be it cold mountains, deciduous forests, oceans, freshwater lakes, deserts or hot springs, leave us speechless. The beauty of a galloping horse, or a migrating bird, the valley of flowers or the attacking shark evokes awe and a deep sense of wonder. The ecological conflict and cooperation among members of a population and among populations of a community or even a molecular&nbsp;traffic inside a cell make us deeply reflect on what indeed is life? This question has two implicit questions within it. The first is a technical one and seeks answer to what living is as opposed to the non-living, and the second is the philosophical one, and seeks&nbsp;answer to what the purpose of life is. What is living? When we try to define \'living\', we conventionally look for distinctive characteristics exhibited by living organisms. Growth, reproduction, ability to sense environment and mount a suitable response come to our mind immediately as unique features of living organisms. One can add a few more features like metabolism, ability to self-replicate, self-organize, interact and emergence to this list.<br>Why are the living types amazing?</p>",
                    options_en: ["<p>The extraordinary diversity of habitats makes it amazing</p>", "<p>The living organisms are acting as per their interests</p>", 
                                "<p>The human thinking makes the living types amazing</p>", "<p>The evolution of life makes it amazing</p>"],
                    options_hi: ["<p>The extraordinary diversity of habitats makes it amazing</p>", "<p>The living organisms are acting as per their interests</p>",
                                "<p>The human thinking makes the living types amazing</p>", "<p>The evolution of life makes it amazing</p>"],
                    solution_en: "<p>16.(a) The extraordinary diversity of habitats makes it amazing<br>(Line/s from the passage - The extraordinary habitats in which we find living organisms, be it cold mountains, deciduous forests, oceans, freshwater lakes, deserts or hot springs, leave us speechless.)</p>",
                    solution_hi: "<p>16.(a) The extraordinary diversity of habitats makes it amazing<br>(Passage से ली गई lines - The extraordinary habitats in which we find living organisms, be it cold mountains, deciduous forests, oceans, freshwater lakes, deserts or hot springs, leave us speechless./असाधारण habitats जैसे ठंडे पहाड़, पर्णपाती वन, महासागर, मीठे पानी की झीलें, रेगिस्तान या गर्म झरने, जिनमें हम जीवित जीवों को पाते हैं, हमें अवाक छोड़ देते हैं।)</p>",
                    correct: "a",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "77",
                    section: "misc",
                    question_en: "<p>17. <strong>Comprehension:</strong><br>How wonderful is the living world! The wide range of the living types is amazing. The extraordinary habitats in which we find living organisms, be it cold mountains, deciduous forests, oceans, freshwater lakes, deserts or hot springs, leave us speechless. The beauty&nbsp;of a galloping horse, or a migrating bird, the valley of flowers or the attacking shark evokes awe and a deep sense of wonder. The ecological conflict and cooperation among members of a population and among populations of a community or even a molecular&nbsp;traffic inside a cell make us deeply reflect on what indeed is life? This question has\\ two implicit questions within it. The first is a technical one and seeks answer to what living is as opposed to the non-living, and the second is the philosophical one, and seeks&nbsp;answer to what the purpose of life is. What is living? When we try to define \'living\', we conventionally look for distinctive characteristics exhibited by living organisms. Growth, reproduction, ability to sense environment and mount a suitable response come to our mind immediately as unique features of living organisms. One can add a few more features like metabolism, ability to self-replicate, self-organize, interact and emergence to this list.<br>Why does the author say, \'ecological conflict and cooperation\' ?</p>",
                    question_hi: "<p>17. <strong>Comprehension:</strong><br>How wonderful is the living world! The wide range of the living types is amazing. The extraordinary habitats in which we find living organisms, be it cold mountains, deciduous forests, oceans, freshwater lakes, deserts or hot springs, leave us speechless. The beauty&nbsp;of a galloping horse, or a migrating bird, the valley of flowers or the attacking shark evokes awe and a deep sense of wonder. The ecological conflict and cooperation among members of a population and among populations of a community or even a molecular&nbsp;traffic inside a cell make us deeply reflect on what indeed is life? This question has two implicit questions within it. The first is a technical one and seeks answer to what living is as opposed to the non-living, and the second is the philosophical one, and seeks&nbsp;answer to what the purpose of life is. What is living? When we try to define \'living\', we conventionally look for distinctive characteristics exhibited by living organisms. Growth, reproduction, ability to sense environment and mount a suitable response come to our mind immediately as unique features of living organisms. One can add a few more features like metabolism, ability to self-replicate, self-organize, interact and emergence to this list.<br>Why does the author say, \'ecological conflict and cooperation\' ?</p>",
                    options_en: ["<p>Because living organisms are structured this way</p>", "<p>Because ecological mechanism works with conflict and coopera-tion</p>", 
                                "<p>Because humans want to fight and live together</p>", "<p>Because living organisms some- times fight and sometimes live together</p>"],
                    options_hi: ["<p>Because living organisms are structured this way</p>", "<p>Because ecological mechanism works with conflict and coopera-tion</p>",
                                "<p>Because humans want to fight and live together</p>", "<p>Because living organisms some- times fight and sometimes live together</p>"],
                    solution_en: "<p>17.(d) Because living organisms some- times fight and sometimes live together<br>(Line/s from the passage - The ecological conflict and cooperation among members of a population and among populations of a community or even a molecular traffic inside a cell make us deeply reflect on what indeed is life)</p>",
                    solution_hi: "<p>17.(d) Because living organisms some- times fight and sometimes live together<br>(Passage से ली गई lines - The ecological conflict and cooperation among members of a population and among populations of a community or even a molecular traffic inside a cell make us deeply reflect on what indeed is life./ एक आबादी के सदस्यों के बीच और एक समुदाय की आबादी के बीच पारिस्थितिक संघर्ष और सहयोग या यहां तक ​​कि एक सेल के अंदर एक आणविक गतिविधि हमें इस बात पर गहराई से सोचने पर मजबूर करती है कि वास्तव में जीवन क्या है।)</p>",
                    correct: "d",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "78",
                    section: "misc",
                    question_en: "<p>18. <strong>Comprehension:</strong><br>How wonderful is the living world! The wide range of the living types is amazing. The extraordinary habitats in which we find living organisms, be it cold mountains, deciduous forests, oceans, freshwater lakes, deserts or hot springs, leave us speechless. The beauty&nbsp;of a galloping horse, or a migrating bird, the valley of flowers or the attacking shark evokes awe and a deep sense of wonder. The ecological conflict and cooperation among members of a population and among populations of a community or even a molecular&nbsp;traffic inside a cell make us deeply reflect on what indeed is life? This question has two implicit questions within it. The first is a technical one and seeks answer to what living is as opposed to the non-living, and the second is the philosophical one, and seeks&nbsp;answer to what the purpose of life is. What is living? When we try to define \'living\', we conventionally look for distinctive characteristics exhibited by living organisms. Growth, reproduction, ability to sense environment and mount a suitable response come to our mind immediately as unique features of living organisms. One can add a few more features like metabolism, ability to self-replicate, self-organize, interact and emergence to this list.<br>Which of the following statements is true of the passage?</p>",
                    question_hi: "<p>18. <strong>Comprehension:</strong><br>How wonderful is the living world! The wide range of the living types is amazing. The extraordinary habitats in which we find living organisms, be it cold mountains, deciduous forests, oceans, freshwater lakes, deserts or hot springs, leave us speechless. The beauty&nbsp;of a galloping horse, or a migrating bird, the valley of flowers or the attacking shark evokes awe and a deep sense of wonder. The ecological conflict and cooperation among members of a population and among populations of a community or even a molecular&nbsp;traffic inside a cell make us deeply reflect on what indeed is life? This question has two implicit questions within it. The first is a technical one and seeks answer to what living is as opposed to the non-living, and the second is the philosophical one, and seeks&nbsp;answer to what the purpose of life is. What is living? When we try to define \'living\', we conventionally look for distinctive characteristics exhibited by living organisms. Growth, reproduction, ability to sense environment and mount a suitable response come to our mind immediately as unique features of living organisms. One can add a few more features like metabolism, ability to self-replicate, self-organize, interact and emergence to this list.<br>Which of the following statements is true of the passage?</p>",
                    options_en: ["<p>Meaning of life could be reflected as to what living is as opposed to the non-living and what the purpose of life is</p>", "<p>Meaning of life could be reflected as to how living organisms live and non-living organisms exist</p>", 
                                "<p>Meaning of life could be reflected as to where the life begins and where it ends</p>", "<p>Meaning of life could be reflected on how various living organisms differ</p>"],
                    options_hi: ["<p>Meaning of life could be reflected as to what living is as opposed to the non-living and what the purpose of life is</p>", "<p>Meaning of life could be reflected as to how living organisms live and non-living organisms exist</p>",
                                "<p>Meaning of life could be reflected as to where the life begins and where it ends</p>", "<p>Meaning of life could be reflected on how various living organisms differ</p>"],
                    solution_en: "<p>18.(a) Meaning of life could be reflected as to what living is as opposed to the non-living and what the purpose of life is<br>(Line/s from the passage - The first is a technical one and seeks answer to what living is as opposed to the non-living, and the second is the philosophical one, and seeks answer to what the purpose of life is.)</p>",
                    solution_hi: "<p>18.(a) Meaning of life could be reflected as to what living is as opposed to the non-living and what the purpose of life is<br>(Passage से ली गई lines - The first is a technical one and seeks answer to what living is as opposed to the non-living, and the second is the philosophical one, and seeks answer to what the purpose of life is./पहला एक तकनीकी है और इसका उत्तर तलाशता है कि निर्जीव के विपरीत जीवित क्या है, और दूसरा दार्शनिक है, और इसका उत्तर चाहता है कि जीवन का उद्देश्य क्या है।)</p>",
                    correct: "a",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "79",
                    section: "misc",
                    question_en: "<p>19. <strong>Comprehension:</strong><br>How wonderful is the living world! The wide range of the living types is amazing. The extraordinary habitats in which we find living organisms, be it cold mountains, deciduous forests, oceans, freshwater lakes, deserts or hot springs, leave us speechless. The beauty&nbsp;of a galloping horse, or a migrating bird, the valley of flowers or the attacking shark evokes awe and a deep sense of wonder. The ecological conflict and cooperation among members of a population and among populations of a community or even a molecular&nbsp;traffic inside a cell make us deeply reflect on what indeed is life? This question has two implicit questions within it. The first is a technical one and seeks answer to what living is as opposed to the non-living, and the second is the philosophical one, and seeks&nbsp;answer to what the purpose of life is. What is living? When we try to define \'living\', we conventionally look for distinctive characteristics exhibited by living organisms. Growth, reproduction, ability to sense environment and mount a suitable response come to our mind immediately as unique features of living organisms. One can add a few more features like metabolism, ability to self-replicate, self-organize, interact and emergence to this list.<br>Distinctive characteristics exhibited by organisms indicate that</p>",
                    question_hi: "<p>19. <strong>Comprehension:</strong><br>How wonderful is the living world! The wide range of the living types is amazing. The extraordinary habitats in which we find living organisms, be it cold mountains, deciduous forests, oceans, freshwater lakes, deserts or hot springs, leave us speechless. The beauty&nbsp;of a galloping horse, or a migrating bird, the valley of flowers or the attacking shark evokes awe and a deep sense of wonder. The ecological conflict and cooperation among members of a population and among populations of a community or even a molecular&nbsp;traffic inside a cell make us deeply reflect on what indeed is life? This question has two implicit questions within it. The first is a technical one and seeks answer to what living is as opposed to the non-living, and the second is the philosophical one, and seeks&nbsp;answer to what the purpose of life is. What is living? When we try to define \'living\', we conventionally look for distinctive characteristics exhibited by living organisms. Growth, reproduction, ability to sense environment and mount a suitable response come to our mind immediately as unique features of living organisms. One can add a few more features like metabolism, ability to self-replicate, self-organize, interact and emergence to this list.<br>Distinctive characteristics exhibited by organisms indicate that</p>",
                    options_en: ["<p>they are living organisms</p>", "<p>they are non-living organisms</p>", 
                                "<p>they can be either living orga-nisms or non-living organisms</p>", "<p>they know the purpose of life</p>"],
                    options_hi: ["<p>they are living organisms</p>", "<p>they are non-living organisms</p>",
                                "<p>they can be either living orga-nisms or non-living organisms</p>", "<p>they know the purpose of life</p>"],
                    solution_en: "<p>19.(a) they are living organisms<br>(Line/s from the passage - When we try to define \'living\', we conventionally look for distinctive characteristics exhibited by living organisms.)</p>",
                    solution_hi: "<p>19.(a) they are living organisms<br>(Passage से ली गई lines - When we try to define \'living\', we conventionally look for distinctive characteristics exhibited by living organisms./ जब हम \'living\' को परिभाषित करने का प्रयास करते हैं, तो हम परंपरागत रूप से जीवित जीवों द्वारा प्रदर्शित विशिष्ट विशेषताओं की तलाश करते हैं।)</p>",
                    correct: "a",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "80",
                    section: "misc",
                    question_en: "<p>20.<strong> Comprehension:</strong><br>How wonderful is the living world! The wide range of the living types is amazing. The extraordinary habitats in which we find living organisms, be it cold mountains, deciduous forests, oceans, freshwater lakes, deserts or hot springs, leave us speechless. The beauty&nbsp;of a galloping horse, or a migrating bird, the valley of flowers or the attacking shark evokes awe and a deep sense of wonder. The ecological conflict and cooperation among members of a population and among populations of a community or even a molecular&nbsp;traffic inside a cell make us deeply reflect on what indeed is life? This question has two implicit questions within it. The first is a technical one and seeks answer to what living is as opposed to the non-living, and the second is the philosophical one, and seeks&nbsp;answer to what the purpose of life is. What is living? When we try to define \'living\', we conventionally look for distinctive characteristics exhibited by living organisms. Growth, reproduction, ability to sense environment and mount a suitable response come to our mind immediately as unique features of living organisms. One can add a few more features like metabolism, ability to self-replicate, self-organize, interact and emergence to this list.<br>Which word in the passage means \'unique\'?</p>",
                    question_hi: "<p>20. <strong>Comprehension:</strong><br>How wonderful is the living world! The wide range of the living types is amazing. The extraordinary habitats in which we find living organisms, be it cold mountains, deciduous forests, oceans, freshwater lakes, deserts or hot springs, leave us speechless. The beauty&nbsp;of a galloping horse, or a migrating bird, the valley of flowers or the attacking shark evokes awe and a deep sense of wonder. The ecological conflict and cooperation among members of a population and among populations of a community or even a molecular&nbsp;traffic inside a cell make us deeply reflect on what indeed is life? This question has two implicit questions within it. The first is a technical one and seeks answer to what living is as opposed to the non-living, and the second is the philosophical one, and seeks&nbsp;answer to what the purpose of life is. What is living? When we try to define \'living\', we conventionally look for distinctive characteristics exhibited by living organisms. Growth, reproduction, ability to sense environment and mount a suitable response come to our mind immediately as unique features of living organisms. One can add a few more features like metabolism, ability to self-replicate, self-organize, interact and emergence to this list.<br>Which word in the passage means \'unique\'?</p>",
                    options_en: ["<p>common</p>", "<p>characteristics</p>", 
                                "<p>distinctive</p>", "<p>general</p>"],
                    options_hi: ["<p>common</p>", "<p>characteristics</p>",
                                "<p>distinctive</p>", "<p>general</p>"],
                    solution_en: "<p>20. (c) <strong>distinctive</strong><br>&lsquo;<strong>Unique</strong>&rsquo; - being the only one of its kind.<br>&lsquo;<strong>Common</strong>&rsquo; - occurring, found, or done often.<br>&lsquo;<strong>Characteristics</strong>&rsquo; - a feature or quality belonging typically to a person, place, or thing and serving to identify them. <br>&lsquo;<strong>General</strong>&rsquo; - affecting or concerning all or most people or things.</p>",
                    solution_hi: "<p>20. (c) <strong>distinctive</strong><br>&lsquo;<strong>Unique</strong>&rsquo; - अपनी तरह का एकमात्र होना; अद्वितीय। <br>&lsquo;<strong>Common</strong>&rsquo; -घटित होना, पाया जाना या अक्सर किया जाना।<br>&lsquo;<strong>Characteristics</strong>&rsquo; - आम तौर पर किसी व्यक्ति, स्थान या चीज़ से संबंधित एक विशेषता या गुणवत्ता या उनकी पहचान। <br>&lsquo;<strong>General</strong>&rsquo; - सभी या अधिकांश लोगों या चीजों को प्रभावित करना।</p>",
                    correct: "c",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "81",
                    section: "misc",
                    question_en: "<p>21. <strong>Cloze test :</strong> <br>I was friends with the artist Bhupen Khakar. He was one of the (21) ______ human beings I have ever met. I learnt (22) ______ from him. When we were in Baroda we (23) ______ meals together all the time. I _____ (24) ______ remember how his caretaker (25) ______ family to him and he would often drop his children to school and taught them mathematics.<br>Select the most appropriate option to fill in the blank no.21</p>",
                    question_hi: "<p>21. <strong>Cloze test :</strong> <br>I was friends with the artist Bhupen Khakar. He was one of the (21) ______ human beings I have ever met. I learnt (22) ______ from him. When we were in Baroda we (23) ______ meals together all the time. I _____ (24) ______ remember how his caretaker (25) ______ family to him and he would often drop his children to school and taught them mathematics.<br>Select the most appropriate option to fill in the blank no.21</p>",
                    options_en: ["<p>finer</p>", "<p>ﬁnest</p>", 
                                "<p>few</p>", "<p>ﬁne</p>"],
                    options_hi: ["<p>finer</p>", "<p>ﬁnest</p>",
                                "<p>few</p>", "<p>ﬁne</p>"],
                    solution_en: "<p>21.(b) ﬁnest<br>The definite article &lsquo;The&rsquo; is used before every &ldquo;superlative degree adjective&rdquo;. However, &lsquo;finest&rsquo; is the superlative degree adjective in the given passage. Hence, &lsquo;ﬁnest&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>21.(b) ﬁnest<br>Definite article &lsquo;The&rsquo; का प्रयोग &ldquo;superlative degree adjective&rdquo; से पहले किया जाता है। दिए गए Passage में &lsquo;finest&rsquo; एक superlative degree adjective है. अतः, &lsquo;ﬁnest&rsquo; सर्वाधिक उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "82",
                    section: "misc",
                    question_en: "<p>22.<strong> Cloze test:</strong><br>I was friends with the artist Bhupen Khakar. He was one of the (21) ______ human beings I have ever met. I learnt (22) ______ from him. When we were in Baroda we (23) ______ meals together all the time. I _____ (24) ______ remember how his caretaker (25) ______ family to him and he would often drop his children to school and taught them mathematics.<br>Select the most appropriate option to fill in the blank no.22</p>",
                    question_hi: "<p>22. <strong>Cloze test:</strong><br>I was friends with the artist Bhupen Khakar. He was one of the (21) ______ human beings I have ever met. I learnt (22) ______ from him. When we were in Baroda we (23) ______ meals together all the time. I _____ (24) ______ remember how his caretaker (25) ______ family to him and he would often drop his children to school and taught them mathematics.<br>Select the most appropriate option to fill in the blank no.22</p>",
                    options_en: ["<p>a lot</p>", "<p>the more</p>", 
                                "<p>a lots</p>", "<p>the less</p>"],
                    options_hi: ["<p>a lot</p>", "<p>the more</p>",
                                "<p>a lots</p>", "<p>the less</p>"],
                    solution_en: "<p>22.(a) a lot<br>Both the phrases &lsquo;A lot of&rsquo; and &lsquo;lots of&rsquo; can be used with plural countable nouns. Hence, &lsquo;a lot of&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>22.(a) a lot<br>दोनों phrases &lsquo;A lot of&rsquo; और &lsquo;lots of&rsquo; का प्रयोग plural countable nouns के लिए किया जाता है। अतः, &lsquo;a lot of&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "a",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "83",
                    section: "misc",
                    question_en: "<p>23. <strong>Cloze test:</strong><br>I was friends with the artist Bhupen Khakar. He was one of the (21) ______ human beings I have ever met. I learnt (22) ______ from him. When we were in Baroda we (23) ______ meals together all the time. I _____ (24) ______ remember how his caretaker (25) ______ family to him and he would often drop his children to school and taught them mathematics.<br>Select the most appropriate option to fill in the blank no.23</p>",
                    question_hi: "<p>23.<strong> Cloze test:</strong><br>I was friends with the artist Bhupen Khakar. He was one of the (21) ______ human beings I have ever met. I learnt (22) ______ from him. When we were in Baroda we (23) ______ meals together all the time. I _____ (24) ______ remember how his caretaker (25) ______ family to him and he would often drop his children to school and taught them mathematics.<br>Select the most appropriate option to fill in the blank no.23</p>",
                    options_en: ["<p>would had</p>", "<p>having had</p>", 
                                "<p>has had</p>", "<p>would have</p>"],
                    options_hi: ["<p>would had</p>", "<p>having had</p>",
                                "<p>has had</p>", "<p>would have</p>"],
                    solution_en: "<p>23.(d) would have<br>The given sentence is in the past tense so it must have a verb in its past form(would). Hence, &lsquo;would have&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>23.(d) would have<br>दिया गया वाक्य past tense का है इसलिए verb का past form(would) में होना आवश्यक है। इसलिए, &lsquo;would have&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "d",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "84",
                    section: "misc",
                    question_en: "<p>24. <strong>Cloze test:</strong><br>I was friends with the artist Bhupen Khakar. He was one of the (21) ______ human beings I have ever met. I learnt (22) ______ from him. When we were in Baroda we (23) ______ meals together all the time. I _____ (24) ______ remember how his caretaker (25) ______ family to him and he would often drop his children to school and taught them mathematics.<br>Select the most appropriate option to fill in the blank no.24</p>",
                    question_hi: "<p>24.<strong> Cloze test:</strong><br>I was friends with the artist Bhupen Khakar. He was one of the (21) ______ human beings I have ever met. I learnt (22) ______ from him. When we were in Baroda we (23) ______ meals together all the time. I _____ (24) ______ remember how his caretaker (25) ______ family to him and he would often drop his children to school and taught them mathematics.<br>Select the most appropriate option to fill in the blank no.24</p>",
                    options_en: ["<p>still</p>", "<p>until</p>", 
                                "<p>never</p>", "<p>alone</p>"],
                    options_hi: ["<p>still</p>", "<p>until</p>",
                                "<p>never</p>", "<p>alone</p>"],
                    solution_en: "<p>24.(a) still<br>&lsquo;Still&rsquo; means up to and including the present or the time mentioned. The given passage states that the narrator remembers the caretaker in the present time too. Hence, &lsquo;still&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>24.(a) still<br>&lsquo;Still&rsquo; - Present से mentioned समय तक या इसमें शामिल। दिए गए passage में कहा गया है कि narrator वर्तमान समय में भी caretaker को याद करता है। इसलिए, &lsquo;still&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "a",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "85",
                    section: "misc",
                    question_en: "<p>25. <strong>Cloze test:</strong><br>I was friends with the artist Bhupen Khakar. He was one of the (21) ______ human beings I have ever met. I learnt (22) ______ from him. When we were in Baroda we (23) ______ meals together all the time. I _____ (24) ______ remember how his caretaker (25) ______ family to him and he would often drop his children to school and taught them mathematics.<br>Select the most appropriate option to fill in the blank no.25</p>",
                    question_hi: "<p>25.<strong> Cloze test:</strong><br>I was friends with the artist Bhupen Khakar. He was one of the (21) ______ human beings I have ever met. I learnt (22) ______ from him. When we were in Baroda we (23) ______ meals together all the time. I _____ (24) ______ remember how his caretaker (25) ______ family to him and he would often drop his children to school and taught them mathematics.<br>Select the most appropriate option to fill in the blank no.25</p>",
                    options_en: ["<p>is</p>", "<p>was</p>", 
                                "<p>be</p>", "<p>were</p>"],
                    options_hi: ["<p>is</p>", "<p>was</p>",
                                "<p>be</p>", "<p>were</p>"],
                    solution_en: "<p>25.(b) was<br>The given sentence is in the past tense and the subject is singular &lsquo;I&rsquo; so it will take a singular verb. Hence, &lsquo;was&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>25.(b) was<br>दिया गया वाक्य past tense में है और subject singular \'I\' है, इसलिए इसमें singular verb होगी। इसलिए, &lsquo;was&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "86",
                    section: "misc",
                    question_en: "26. Select the most appropriate meaning of the idiom given in the following question<br />Put up with",
                    question_hi: "26. Select the most appropriate meaning of the idiom given in the following question<br />Put up with",
                    options_en: [" excuse", " refuse", 
                                " accept ", " tolerate"],
                    options_hi: [" excuse", " refuse",
                                " accept ", " tolerate"],
                    solution_en: "26.(d) tolerate <br />Put up with- to tolerate <br />E.g.- The teacher told the student that she cannot put up with his misconduct any longer.",
                    solution_hi: "26.(d) tolerate<br />Put up with- सहना । <br />E.g.- The teacher told the student that she cannot put up with his misconduct any longer./ शिक्षिका ने छात्रा से कहा कि वह अब उसके दुराचार (misconduct) को सहन नहीं कर सकती।",
                    correct: "d",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "87",
                    section: "misc",
                    question_en: "27. Select the most appropriate meaning of the idiom given in the following Question<br />Beating about the bush",
                    question_hi: "27. Select the most appropriate meaning of the idiom given in the following Question<br />Beating about the bush",
                    options_en: [" repeating the same thing again and again", " tell in a confusing way", 
                                " tell in a roundabout way", " secretive"],
                    options_hi: [" repeating the same thing again and again", " tell in a confusing way",
                                " tell in a roundabout way", " secretive"],
                    solution_en: "27.(c)  tell in a roundabout way<br />Beating about the bush- tell in a roundabout way<br />E.g.- My friend didn’t tell me directly about his whereabouts, but kept beating about the bush.",
                    solution_hi: "27.(c) tell in a roundabout way<br />Beating about the bush- tell in a roundabout way/ गुमा-फिरा कर बात करना। <br />E.g.- My friend didn’t tell me directly about his whereabouts, but kept beating about the bush./ मेरे दोस्त ने मुझे सीधे अपने ठिकाने के बारे में नहीं बताया, वह इधर-उधर की बातें करता रहा।",
                    correct: "c",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "88",
                    section: "misc",
                    question_en: "28. Given below are four jumbled sentences. Pick the option that gives their correct order.<br />P. Adult education is essential for Democracy as the number of grown up illiterates is great. <br />Q. All college and senior School students should come forward to educate the illiterates.<br />R. They should visit villages in the summer vacation.<br />S. Each one will teach one there. ",
                    question_hi: "28. Given below are four jumbled sentences. Pick the option that gives their correct order.<br />P. Adult education is essential for Democracy as the number of grown up illiterates is great. <br />Q. All college and senior School students should come forward to educate the illiterates.<br />R. They should visit villages in the summer vacation.<br />S. Each one will teach one there. ",
                    options_en: [" PQRS", " PRQS", 
                                " SQRP", " QRPS"],
                    options_hi: [" PQRS", " PRQS",
                                " SQRP", " QRPS"],
                    solution_en: "28.(a)  PQRS<br />Sentence P will be the starting line as it contains the main idea of the parajumble i.e. Adult education is essential for Democracy. However, Sentence Q states that all college and senior School students should come forward to educate the illiterate. So, Q will follow P. Further, Sentence R states that they should visit villages in the summer vacation & Sentence S states that each one will teach one there. So, S will follow R. Going through the options, option a has the correct sequence.  ",
                    solution_hi: "28.(a)  PQRS<br />वाक्य P प्रारंभिक पंक्ति होगी क्योंकि इसमें parajumble का मुख्य विचार है अर्थात लोकतंत्र (Democracy) के लिए Adult education आवश्यक है। वाक्य Q में कहा गया है कि सभी कॉलेज और सीनियर स्कूल को निरक्षर छात्रों को शिक्षित करने के लिए आगे आना चाहिए। P के बाद Q आयेगा। आगे, R वाक्य कहता है कि उन्हें गर्मियों की छुट्टी में गांवों का दौरा करना चाहिए और वाक्य S बताता है कि वहां हर कोई किसी एक को पढ़ाएगा। तो, R के बाद S आयेगा। विकल्पों के माध्यम से, विकल्प a में सही क्रम है।",
                    correct: "a",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "89",
                    section: "misc",
                    question_en: "<p>29. Choose the most appropriate option to change the narration (direct/indirect) of the given sentence.<br>The officer told me that I needed to work harder to meet my targets otherwise I would have to leave the company.</p>",
                    question_hi: "<p>29. Choose the most appropriate option to change the narration (direct/indirect) of the given sentence.<br>The officer told me that I needed to work harder to meet my targets otherwise I would have to leave the company.</p>",
                    options_en: ["<p>The officer said to me,\"I need to work harder to meet my targets otherwise I will have to leave the company.\"</p>", "<p>The officer said to me,\" Work harder to meet my targets otherwise be ready to leave the company.\"</p>", 
                                "<p>The officer said to me,\"You have to work harder to meet our targets otherwise I will have to leave the company.\"</p>", "<p>The officer said to me,\"You need to work harder to meet your targets otherwise you will have to leave the company.\"</p>"],
                    options_hi: ["<p>The officer said to me,\"I need to work harder to meet my targets otherwise I will have to leave the company.\"</p>", "<p>The officer said to me,\" Work harder to meet my targets otherwise be ready to leave the company.\"</p>",
                                "<p>The officer said to me,\"You have to work harder to meet our targets otherwise I will have to leave the company.\"</p>", "<p>The officer said to me,\"You need to work harder to meet your targets otherwise you will have to leave the company.\"</p>"],
                    solution_en: "<p>29.(d) The officer said to me,\"You need to work harder to meet your targets otherwise you will have to leave the company.\"<br>(a) The officer said to me,\"I need to work harder to meet my targets otherwise I will have to leave the company.\" (Incorrect Pronouns &lsquo;I&rsquo; &amp; &lsquo;my&rsquo;)<br>(b) The officer said to me,\" Work harder to meet my targets otherwise be ready to leave the company.\" (Incorrect Sentence Structure)<br>(c) The officer said to me,\"You <span style=\"text-decoration: underline;\">have</span> to work harder to meet our targets otherwise I will have to leave the company.\" (Incorrect Verb)</p>",
                    solution_hi: "<p>29.(d) The officer said to me,\"You need to work harder to meet your targets otherwise you will have to leave the company.\"<br>(a) The officer said to me,\"I need to work harder to meet my targets otherwise I will have to leave the company.\" (गलत Pronouns- &lsquo;I&rsquo; और &lsquo;my&rsquo; का प्रयोग किया गया है।) <br>(b) The officer said to me,\" Work harder to meet my targets otherwise be ready to leave the company.\" (गलत Sentence Structure का प्रयोग किया गया है।)<br>(c) The officer said to me,\"You <span style=\"text-decoration: underline;\">have</span> to work harder to meet our targets otherwise I will have to leave the company.\" (गलत Verb का प्रयोग किया गया है।)</p>",
                    correct: "d",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "90",
                    section: "misc",
                    question_en: "<p>30. Choose the most appropriate option to change the voice (active/passive) form of the given sentence.<br>Why does the child always make unnecessary demands?</p>",
                    question_hi: "<p>30. Choose the most appropriate option to change the voice (active/passive) form of the given sentence.<br>Why does the child always make unnecessary demands?</p>",
                    options_en: ["<p>Why are unnecessary demands always made by the child?</p>", "<p>Why is unnecessary demand always made by the child?</p>", 
                                "<p>Why unnecessary demands are always made by the child?</p>", "<p>Why unnecessary demands always made by the child?</p>"],
                    options_hi: ["<p>Why are unnecessary demands always made by the child?</p>", "<p>Why is unnecessary demand always made by the child?</p>",
                                "<p>Why unnecessary demands are always made by the child?</p>", "<p>Why unnecessary demands always made by the child?</p>"],
                    solution_en: "<p>30.(a) Why are unnecessary demands always made by the child?<br>(b) Why <span style=\"text-decoration: underline;\">is</span> unnecessary demand always made by the child? (Incorrect Verb)<br>(c) Why unnecessary demands are always made by the child? (Incorrect Interrogative Structure)<br>(d) Why unnecessary demands always made by the child? (Helping Verb &lsquo;are&rsquo; is missing)</p>",
                    solution_hi: "<p>30.(a) Why are unnecessary demands always made by the child?<br>(b) Why <span style=\"text-decoration: underline;\">is</span> unnecessary demand always made by the child? (गलत Verb का प्रयोग किया गया है।)<br>(c) Why unnecessary demands are always made by the child? (गलत Interrogative Structure का प्रयोग किया गया है।)<br>(d) Why unnecessary demands always made by the child? (Helping Verb &lsquo;are&rsquo;का प्रयोग नहीं किया गया है।)</p>",
                    correct: "a",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "91",
                    section: "misc",
                    question_en: "<p>31. Select the most appropriate synonym of the given word.<br>Knave</p>",
                    question_hi: "<p>31. Select the most appropriate synonym of the given word.<br>Knave</p>",
                    options_en: ["<p>emperor</p>", "<p>enchanter</p>", 
                                "<p>soldier</p>", "<p>scoundrel</p>"],
                    options_hi: ["<p>emperor</p>", "<p>enchanter</p>",
                                "<p>soldier</p>", "<p>scoundrel</p>"],
                    solution_en: "<p>31.(d) <strong>scoundrel</strong>- a dishonest or unscrupulous person; a rogue, reprobate<br><strong>Knave</strong>- a dishonest person<br><strong>Enchanter</strong>- a person who can captivate and attract<br><strong>Soldier</strong>- a person who serves in the army. Warrior, trooper</p>",
                    solution_hi: "<p>31.(d) <strong>Scoundrel</strong>- एक बेईमान व्यक्ति<br><strong>Knave</strong>- एक बेईमान व्यक्ति<br><strong>Enchanter</strong>- मंत्रमुग्ध करने वाला व्यक्ति <br><strong>Soldier</strong>- एक व्यक्ति जो सेना में सेवा देता है- सिपाही</p>",
                    correct: "d",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "92",
                    section: "misc",
                    question_en: "<p>32. select the most appropriate antonym of the given word.<br>Innocuous</p>",
                    question_hi: "<p>32. select the most appropriate antonym of the given word.<br>Innocuous</p>",
                    options_en: ["<p>harmful</p>", "<p>coaxing</p>", 
                                "<p>spotless</p>", "<p>taint</p>"],
                    options_hi: ["<p>harmful</p>", "<p>coaxing</p>",
                                "<p>spotless</p>", "<p>taint</p>"],
                    solution_en: "<p>32.(a) <strong>Harmful</strong>- causing harm<br><strong>Innocuous</strong>- not harmful or offensive <br><strong>Coaxing</strong>- to persuade gently or by flattery <br><strong>Spotless</strong>- absolutely clean or pure; immaculate.<br><strong>Taint</strong>- the effect of something bad or unpleasant</p>",
                    solution_hi: "<p>32.(a) <strong>Harmful</strong>- हानिकारक<br><strong>Innocuous</strong>- नुकसान न पहुंचानेवाला<br><strong>Coaxing</strong>- धीरे से या चापलूसी से राजी करना<br><strong>Spotless-</strong> बेदाग।<br><strong>Taint-</strong> किसी बुरी या अप्रिय बात का प्रभाव</p>",
                    correct: "a",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "93",
                    section: "misc",
                    question_en: "33.Given below are four jumbled sentences. Pick the option that gives their correct order. <br />A. It provides training for the development of oral and aural skills and is a great supplement to classroom teaching.<br />B. Technology is growing very fast day by day and is contributing a lot towards improving communication skills.<br />C. Thus, it is the responsibility of a teacher to make the learners understand and their aim is to master the language, not the technology.<br />D. However, it can not be substitute for the conventional method of language teaching.",
                    question_hi: "33.Given below are four jumbled sentences. Pick the option that gives their correct order. <br />A. It provides training for the development of oral and aural skills and is a great supplement to classroom teaching.<br />B. Technology is growing very fast day by day and is contributing a lot towards improving communication skills.<br />C. Thus, it is the responsibility of a teacher to make the learners understand and their aim is to master the language, not the technology.<br />D. However, it can not be substitute for the conventional method of language teaching.",
                    options_en: [" BCDA       ", " ACBD      ", 
                                " BADC      ", " DACB"],
                    options_hi: [" BCDA       ", " ACBD      ",
                                " BADC      ", " DACB"],
                    solution_en: "33.(c) BADC <br />Sentence B will be the starting line as it contains the main idea of the parajumble i.e. Technology is growing very fast day by day. However, Sentence A states that it provides training for the development of oral and aural skills and is a great supplement to classroom teaching. So, A will follow B. Further, Sentence D states that it can not be substituted for the conventional method of language teaching & Sentence C states that it is the responsibility of a teacher to make the learners understand and their aim is to master the language, not the technology. So, C will follow D. Going through the options, option c has the correct sequence.",
                    solution_hi: "33.(c) BADC <br />वाक्य B शुरूआती line होगी क्योंकि इसमें Parajumble का main idea है- Technology is growing very fast day by day. वाक्य A बताता है कि यह मौखिक और श्रव्य कौशल(aural skills) के विकास के लिए प्रशिक्षण (training) प्रदान करता है और class room teaching के लिए एक महान पूरक है। इसलिए, B के बाद A आयेगा। आगे, वाक्य D कहता है कि इसे भाषा शिक्षण की पारंपरिक पद्धति के लिए substitute नहीं किया जा सकता है। फिर वाक्य C कहता है कि शिक्षार्थियों को समझाना एक शिक्षक की जिम्मेदारी है और उनका उद्देश्य भाषा में निपुणता हासिल करना है, न कि तकनीक में। तो, D के बाद C आयेगा। विकल्पों के माध्यम से जाने पर, विकल्प c में सही क्रम है।",
                    correct: "c",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "94",
                    section: "misc",
                    question_en: "34. Identify the segment in the sentence, which contains grammatical errors.<br />Tarun was annoyed with his friends in the college as they laughed with his dress",
                    question_hi: "34. Identify the segment in the sentence, which contains grammatical errors.<br />Tarun was annoyed with his friends in the college as they laughed with his dress",
                    options_en: [" Tarun was annoyed with his friends", " as they laughed", 
                                " with his dress", " No error"],
                    options_hi: [" Tarun was annoyed with his friends", " as they laughed",
                                " with his dress", " No error"],
                    solution_en: "34.(c) with his dress<br />The phrase ‘Laugh with’ means to laugh with someone and ‘laugh at’ means to laugh at something. Similarly, in the given sentence, they laughed at his dress. Hence, ‘with’ must be replaced with ‘at’ & ‘laughed at his dress’ becomes the most appropriate answer.",
                    solution_hi: "34.(c) with his dress<br />Phrase ‘Laugh with’ का अर्थ है -  किसी के साथ हंसना और ‘laugh at’ का अर्थ है - किसी पर हंसना। इसी प्रकार, दिए गए वाक्य में, वे उसके पहनावे पर हँसे। इसलिए, \'with\' को \'at\' से बदला जाना चाहिए।",
                    correct: "c",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "95",
                    section: "misc",
                    question_en: "<p>35. Identify the best way to improve the underlined part of the given sentence. If there is no improvement required, select &lsquo;no improvement&rsquo;.<br>The suspected couple was <span style=\"text-decoration: underline;\">taken</span> away from the airport through a side entrance to the&nbsp;police station for interrogation.</p>",
                    question_hi: "<p>35. Identify the best way to improve the underlined part of the given sentence. If there is no improvement required, select &lsquo;no improvement&rsquo;.<br>The suspected couple was <span style=\"text-decoration: underline;\">taken</span> away from the airport through a side entrance to the&nbsp;police station for interrogation.</p>",
                    options_en: ["<p>whisked</p>", "<p>rushed</p>", 
                                "<p>guided</p>", "<p>No improvement</p>"],
                    options_hi: ["<p>whisked</p>", "<p>rushed</p>",
                                "<p>guided</p>", "<p>No improvement</p>"],
                    solution_en: "<p>35.(d) No improvement. The given sentence is grammatically correct.</p>",
                    solution_hi: "<p>35.(d) No improvement. दिया गया वाक्य सही है।</p>",
                    correct: "d",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "96",
                    section: "misc",
                    question_en: "<p>36. Choose the most appropriate option to change the narration (direct / indirect) of the given sentence.<br>He said, &ldquo;I am going to college just now.&rdquo;</p>",
                    question_hi: "<p>36. Choose the most appropriate option to change the narration (direct / indirect) of the given sentence.<br>He said, &ldquo;I am going to college just now.&rdquo;</p>",
                    options_en: ["<p>He said that he was going to college just now</p>", "<p>He asked that he was going to college just then</p>", 
                                "<p>He said that he was going to college just then</p>", "<p>He asked that he was going to college just now</p>"],
                    options_hi: ["<p>He said that he was going to college just now</p>", "<p>He asked that he was going to college just then</p>",
                                "<p>He said that he was going to college just then</p>", "<p>He asked that he was going to college just now</p>"],
                    solution_en: "<p>36.(c)He said that he was going to college just then.(Correct)<br>(a) He said that he was going to college just <span style=\"text-decoration: underline;\">now</span>. (&lsquo;now&rsquo; must change to &lsquo;then&rsquo;)<br>(b) He <span style=\"text-decoration: underline;\">asked</span> that he was going to college just then. (Incorrect Reporting Verb)<br>(d) He asked that he was going to college just now. (&lsquo;asked&rsquo; and &lsquo;that&rsquo; can&rsquo;t be used together)</p>",
                    solution_hi: "<p>36.(c)He said that he was going to college just then.(Correct)<br>(a) He said that he was going to college just <span style=\"text-decoration: underline;\">now</span>. (&lsquo;Now&rsquo; को &lsquo;then&rsquo; से बदले)<br>(b) He <span style=\"text-decoration: underline;\">asked</span> that he was going to college just then. (गलत Reporting Verb)<br>(d) He asked that he was going to college just now. (&lsquo;asked&rsquo; और &lsquo;that&rsquo; एक साथ प्रयोग नहीं हो सकते )</p>",
                    correct: "c",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "97",
                    section: "misc",
                    question_en: "<p>37. Choose the most appropriate option to change the voice (active/passive) form of the given sentence.<br>We feel that God himself controls all our actions.</p>",
                    question_hi: "<p>37. Choose the most appropriate option to change the voice (active/passive) form of the given sentence.<br>We feel that God himself controls all our actions.</p>",
                    options_en: ["<p>We feel that all actions are themselves controlled by God.</p>", "<p>We feel that our actions are themselves controlled by God.</p>", 
                                "<p>It is felt that all our actions are controlled by God himself.</p>", "<p>It is felt that all our actions are themselves control by God.</p>"],
                    options_hi: ["<p>We feel that all actions are themselves controlled by God.</p>", "<p>We feel that our actions are themselves controlled by God.</p>",
                                "<p>It is felt that all our actions are controlled by God himself.</p>", "<p>It is felt that all our actions are themselves control by God.</p>"],
                    solution_en: "<p>37.(c) It is felt that all our actions are controlled by God himself. <br>(a) We feel that all actions are themselves controlled by God. (Incorrect Sentence Structure)<br>(b) We feel that our actions are themselves controlled by God. (Incorrect Sentence Structure)<br>(d) It is felt that all our actions are themselves <span style=\"text-decoration: underline;\">control</span> by God. (Incorrect Verb)</p>",
                    solution_hi: "<p>37.(c) It is felt that all our actions are controlled by God himself.<br>(a) We feel that all actions are themselves controlled by God. (गलत Sentence Structure का गलत प्रयोग किया गया है। )<br>(b) We feel that our actions are themselves controlled by God. (गलत Sentence Structure का गलत प्रयोग किया गया है। )<br>(d) It is felt that all our actions are themselves <span style=\"text-decoration: underline;\">control</span> by God. (गलत Verb का गलत प्रयोग किया गया है।)</p>",
                    correct: "c",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "98",
                    section: "misc",
                    question_en: "<p>38. Select the most appropriate option to fill in the blank.<br>The main culprit together with his associates ___________ arrested.</p>",
                    question_hi: "<p>38. Select the most appropriate option to fill in the blank.<br>The main culprit together with his associates ___________ arrested.</p>",
                    options_en: ["<p>are</p>", "<p>was</p>", 
                                "<p>were</p>", "<p>have</p>"],
                    options_hi: ["<p>are</p>", "<p>was</p>",
                                "<p>were</p>", "<p>have</p>"],
                    solution_en: "<p>38.(b) was<br>According to the &ldquo;<span style=\"text-decoration: underline;\">Subject-Verb Agreement Rule&rdquo;, &ldquo;1st Subject + with/ along with/ together with/ as well as + 2nd Subject + Helping Verb</span>&rdquo; is grammatically the correct structure in which the helping verb is supplied according to the first subject. Similarly, in the given sentence, &lsquo;main culprit&rsquo; is the first subject that is in the singular form and will take &lsquo;was&rsquo; as a singular verb. Hence, &lsquo;was&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>38.(b) was<br>&ldquo;<span style=\"text-decoration: underline;\">Subject-Verb Agreement Rule</span>&rdquo; के अनुसार, &ldquo;<span style=\"text-decoration: underline;\">1st Subject + with/ along with/ together with/ as well as + 2nd Subject + Helping Verb</span>&rdquo; grammatically सही structure है जिसमे helping verb पहले subject के अनुसार होगी।इसी प्रकार, दिए गए वाक्य में, &lsquo;main culprit&rsquo; पहला विषय है जो एकवचन रूप में है और &lsquo;was&rsquo; एकवचन क्रिया लगेगी । <br>इसलिए, &lsquo;was&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "99",
                    section: "misc",
                    question_en: "39.Given below are four jumbled sentences. Pick the option that gives their correct order.<br />P. Advertising affects our lives so much that brand names are common household words.<br />Q. We start each day using toothpaste, soap, and breakfast foods promoted by advertisers.<br />R. Ads have made the cars we drive signs of our success.<br />S. Our choices of food, dress, and entertainment are swayed by ads.",
                    question_hi: "39.Given below are four jumbled sentences. Pick the option that gives their correct order.<br />P. Advertising affects our lives so much that brand names are common household words.<br />Q. We start each day using toothpaste, soap, and breakfast foods promoted by advertisers.<br />R. Ads have made the cars we drive signs of our success.<br />S. Our choices of food, dress, and entertainment are swayed by ads.",
                    options_en: [" PQRS", " PRQS", 
                                " PSQR", " QPSR  "],
                    options_hi: [" PQRS", " PRQS",
                                " PSQR", " QPSR  "],
                    solution_en: "39.(a) PQRS<br />Sentence P will be the starting line as it contains the main idea of the parajumble i.e. Advertising affects our lives. However, Sentence Q states that we start each day using toothpaste, soap, and breakfast foods promoted by advertisers. So, Q will follow P. Further, Sentence R states that Ads have made the cars we drive signs of our success & Sentence S states that our choices of food, dress, and entertainment are swayed by ads. So, S will follow R. Going through the options, option a has the correct sequence.",
                    solution_hi: "39.(a) PQRS<br />वाक्य P शुरूआती line होगी क्योंकि इसमें parajumble का main idea है -Advertising affects our lives. वाक्य Q कहता है कि हम विज्ञापनदाताओं द्वारा प्रचारित टूथपेस्ट, साबुन और नाश्ते के खाद्य पदार्थों का उपयोग करके प्रत्येक दिन की शुरुआत करते हैं। P के बाद Q आयेगा। आगे, वाक्य R कहता है कि विज्ञापनों ने उन कारों को सफलता का संकेत बना दिया है और वाक्य S बताता है कि भोजन, पोशाक और मनोरंजन के हमारे विकल्प विज्ञापनों से प्रभावित होते हैं। तो, R के बाद S आयेगा। विकल्पों के माध्यम से, विकल्प a में सही क्रम है।",
                    correct: "a",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "100",
                    section: "misc",
                    question_en: "<p>40. Select the word which means the same as the group of words given. <br>One who makes an official examination of accounts</p>",
                    question_hi: "<p>40. Select the word which means the same as the group of words given. <br>One who makes an official examination of accounts</p>",
                    options_en: ["<p>auditor</p>", "<p>accountant</p>", 
                                "<p>clerk</p>", "<p>official</p>"],
                    options_hi: ["<p>auditor</p>", "<p>accountant</p>",
                                "<p>clerk</p>", "<p>official</p>"],
                    solution_en: "<p>40.(a) <strong>Auditor</strong>- one who makes an official examination of accounts<br><strong>Accountant</strong>- a person whose job is to keep or examine the financial accounts of a business, etc.<br><strong>Clerk</strong>- a person whose job is to do written work or look after records or accounts in an office, bank, court of law, etc.<br><strong>Official</strong>- connected with the position of somebody in authority</p>",
                    solution_hi: "<p>40.(a) <strong>Auditor</strong>- वह जो खातों की आधिकारिक जांच करता है<br><strong>Accountant</strong>- एक व्यक्ति जिसका काम किसी व्यवसाय आदि के वित्तीय खातों को रखना या जांचना है।<br><strong>Clerk</strong>- एक व्यक्ति जिसका काम किसी कार्यालय, बैंक, कानून की अदालत आदि में लिखित कार्य करना है या रिकॉर्ड या खातों की देखभाल करना है।<br><strong>Official</strong>- आधिकारिक</p>",
                    correct: "a",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "101",
                    section: "misc",
                    question_en: "<p>41. Select the word which means the same as the group of words given. <br>Lasting only for a moment</p>",
                    question_hi: "<p>41. Select the word which means the same as the group of words given. <br>Lasting only for a moment</p>",
                    options_en: ["<p>momentous</p>", "<p>momentary</p>", 
                                "<p>trivial</p>", "<p>petty</p>"],
                    options_hi: ["<p>momentous</p>", "<p>momentary</p>",
                                "<p>trivial</p>", "<p>petty</p>"],
                    solution_en: "<p>41.(b) <strong>Momentary</strong>- Transient, lasting for a very short time<br><strong>Momentous</strong>- very Important<br><strong>Trivial</strong>- of little importance, insignificant, unimportant<br><strong>Petty</strong>- of little importance; trivial.</p>",
                    solution_hi: "<p>41.(b) <strong>Momentary</strong>- क्षणिक, बहुत कम समय तक चलने वाला<br><strong>Momentous</strong>- बहुत ज़रूरी<br><strong>Trivial</strong>- महत्वहीन<br><strong>Petty</strong>- कम महत्व का; मामूली।</p>",
                    correct: "b",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "102",
                    section: "misc",
                    question_en: "42. Identify the segments in the sentence, which contains the grammatical error.<br />Dense fog on the highway was the cause of the collusion between the truck and the school bus.",
                    question_hi: "42. Identify the segments in the sentence, which contains the grammatical error.<br />Dense fog on the highway was the cause of the collusion between the truck and the school bus.",
                    options_en: [" Dense fog on the highway", " was the cause of the collusion", 
                                " between the truck and the school bus", " No error"],
                    options_hi: [" Dense fog on the highway", " was the cause of the collusion",
                                " between the truck and the school bus", " No error"],
                    solution_en: "42.(b)  was the cause of the collusion<br />‘Collusion’ means a conspiracy to do something wrong and ‘Collision’ means two things violently hitting against each other. However, in the given sentence, the truck and the school bus hit each other due to dense fog. Hence, ‘the cause of the collision’ is the most appropriate answer.",
                    solution_hi: "42.(b)  was the cause of the collusion<br />‘Collusion’ - कुछ गलत करने की साजिश। और  ‘Collision’- दो चीजें हिंसक रूप से एक दूसरे से टकरा रही हैं। हालांकि, दिए गए वाक्य में, घने कोहरे के कारण ट्रक और स्कूल बस एक दूसरे से टकरा गए। अतः, ‘the cause of the collision’ सर्वाधिक उपयुक्त उत्तर है।",
                    correct: "b",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "103",
                    section: "misc",
                    question_en: "<p>43. Identify the best way to improve the underlined part of the given sentence. If there is no improvement required, select &lsquo;no improvement&rsquo;.<br><span style=\"text-decoration: underline;\"><strong>One of my friend work</strong></span> in the Irrigation Department as an Assistant Engineer.</p>",
                    question_hi: "<p>43. Identify the best way to improve the underlined part of the given sentence. If there is no improvement required, select &lsquo;no improvement&rsquo;.<br><span style=\"text-decoration: underline;\"><strong>One of my friend work</strong></span> in the Irrigation Department as an Assistant Engineer.</p>",
                    options_en: ["<p>One of my friends works</p>", "<p>One among my friends</p>", 
                                "<p>One of friend of mine</p>", "<p>No improvement</p>"],
                    options_hi: ["<p>One of my friends works</p>", "<p>One among my friends</p>",
                                "<p>One of friend of mine</p>", "<p>No improvement</p>"],
                    solution_en: "<p>43.(a) One of my friends works<br>According to the &ldquo;<span style=\"text-decoration: underline;\">Subject-Verb Agreement Rule&rdquo;, &lsquo;One of/ Each of/ Most of/ None of + Plural Subject + Singular verb</span>&rsquo; is grammatically the correct structure. The given sentence must have &lsquo;friends&rsquo; as a plural subject and &lsquo;works&rsquo; as a singular verb. Hence, &lsquo;One of my friends works&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>43.(a) One of my friends works<br>&ldquo;<span style=\"text-decoration: underline;\">Subject-Verb Agreement Rule</span>&rdquo; के अनुसार, &lsquo;<span style=\"text-decoration: underline;\">One of/ Each of/ Most of/ None of + Plural Subject + Singular verb</span>&rsquo; सही grammatically structure है। दिए गए वाक्य में plural subject के रूप में \'दोस्त\' और singular verb के रूप में &lsquo;works&rsquo; होना चाहिए। अतः, &lsquo;One of my friends works&rsquo; सर्वाधिक उपयुक्त उत्तर है।</p>",
                    correct: "a",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "104",
                    section: "misc",
                    question_en: "44. Identify the word that is misspelt. ",
                    question_hi: "44. Identify the word that is misspelt. ",
                    options_en: [" Expenditure", " Exscitement ", 
                                " Eccentricity", " Exclamation "],
                    options_hi: [" Expenditure", " Exscitement ",
                                " Eccentricity", " Exclamation "],
                    solution_en: "44.(b) Exscitement<br />‘Excitement’ is the correct spelling.",
                    solution_hi: "44.(b) Exscitement<br />‘Excitement’ सही spelling है। ",
                    correct: "b",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "105",
                    section: "misc",
                    question_en: "45. Given below are four jumbled sentences. Pick the option that gives their correct order. <br />P. It goes about collecting honey.<br />Q. The bee has never learnt to spend.<br />R. As though starvation was staring it at its face.<br />S. Even with stocks sufficient for 20 generations.",
                    question_hi: "45. Given below are four jumbled sentences. Pick the option that gives their correct order. <br />P. It goes about collecting honey.<br />Q. The bee has never learnt to spend.<br />R. As though starvation was staring it at its face.<br />S. Even with stocks sufficient for 20 generations.",
                    options_en: [" PRQS ", " RSQP ", 
                                " QSPR ", " QRPS"],
                    options_hi: [" PRQS ", " RSQP ",
                                " QSPR ", " QRPS"],
                    solution_en: "45.(c) QSPR<br />Sentence Q will be the starting line as it contains the main idea of the parajumble i.e. The bee has never learnt to spend. However, Sentence S states that even with stocks sufficient for 20 generations. So, S will follow Q. Further, Sentence P states that it goes about collecting honey & Sentence R states that as though starvation was staring it at its face. So, R will follow P. Going through the options, option c has the correct sequence",
                    solution_hi: "45.(c) QSPR<br />वाक्य Q शुरुआती लाइन होगी क्योंकि इसमें parajumble का main idea है - The bee has never learnt to spend. वाक्य S बताता है कि 20 पीढ़ी के लिए पर्याप्त स्टॉक है  तो, Q के बाद S आयेगा । आगे, वाक्य P कहता है कि वह किस तरह शहद इकट्ठा करती है और वाक्य R कहता है वह इस तरह शहद इकट्ठा करती है मानो की भुखमरी हो। तो, P के बाद R आयेगा विकल्पों के माध्यम से, विकल्प c में सही क्रम है।",
                    correct: "c",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "106",
                    section: "misc",
                    question_en: "<p>46. The famous musician, Appa Jalgaonkar is associated with which musical instrument ?</p>",
                    question_hi: "<p>46. प्रसिद्ध संगीतकार, अप्पा जलगाँवकर किस वाद्य यंत्र से जुड़े हैं ?</p>",
                    options_en: ["<p>Harmonium</p>", "<p>Tabla</p>", 
                                "<p>Sarod</p>", "<p>Violin</p>"],
                    options_hi: ["<p>हारमोनियम</p>", "<p>तबला</p>",
                                "<p>सरोद</p>", "<p>वायलिन</p>"],
                    solution_en: "<p>46.(a) He was known as the <strong>Harmonium</strong> master. He won the Sangeet Natak Akademi award in 1999-2000. <strong>Tulsidas Vasant</strong> Borkar is also a famous Harmonium Player of India. He won The Sangeet Natak Akademi award in 2005. A harmonium, also called a \"melodeon\", \"reed organ\" or \"pump organ\", is a keyboard instrument that is a lot like an organ.</p>",
                    solution_hi: "<p>46.(a) उन्हें <strong>हारमोनियम</strong> मास्टर के नाम से भी जाना जाता था। उन्होंने 1999-2000 में संगीत नाटक अकादमी पुरस्कार जीता। <strong>तुलसीदास वसंत</strong> <strong>बोरकर </strong>भी भारत के प्रसिद्ध हारमोनियम वादक हैं। उन्होंने 2005 में संगीत नाटक अकादमी पुरस्कार जीता। हारमोनियम, जिसे \"मेलोडियन\", \"रीड ऑर्गन\" या \"पंप ऑर्गन\" भी कहा जाता है, एक कीबोर्ड उपकरण है जो एक आर्गन (organ) की तरह है।</p>",
                    correct: "a",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "107",
                    section: "misc",
                    question_en: "<p>47. Which Bahmani ruler changed his capital from Gulbarga to Bidar?</p>",
                    question_hi: "<p>47. किस बहमनी शासक ने अपनी राजधानी गुलबर्गा से बदलकर बीदर कर दी?</p>",
                    options_en: ["<p>Humayun Shah</p>", "<p>Alauddin Hasan Bahman Shah</p>", 
                                "<p>Muhammmad Shah</p>", "<p>Ahmad Shah</p>"],
                    options_hi: ["<p>हुमायूं शाह</p>", "<p>अलाउद्दीन हसन बहमन शाह</p>",
                                "<p>मुहम्मद शाह</p>", "<p>अहमद शाह</p>"],
                    solution_en: "<p>47.(d) <strong>Ahmad Shah</strong> ruled the Bahmani kingdom from 1422 to 1436 A.D. He conquered the kingdom of Warangal. He succeeded Feroz Shah Bahmani. Other rulers of <strong>Bahmani Kingdom: Ala-ud-Din Hasan</strong> Bahman Shah (also known as Zafar Khan) founded the Bahmani Kingdom in 1347 A.D. <strong>Muhammad Shah-I </strong>defeated Kapaya Nayaks of Warangal and the Vijayanagar ruler Bukka-I. <strong>Muhammad Shah-ll</strong> built many mosques, madrasas (a place of learning) and hospitals. Feroz Shah Bahmani defeated the Vijayanagar ruler Deva Raya I. <strong>Muhammad Shah-lll</strong> defeated the rulers of Konkan, Orissa, Sangameshwar, and Vijayanagar. <strong>Humayun Shah</strong> granted the title of \'Chief of the Merchants\' or Malikut-Tujjar to Mahmud Gawan.</p>",
                    solution_hi: "<p>47.(d) <strong>अहमद शाह</strong> ने 1422 से 1436 ईसवी तक बहमनी राज्य पर शासन किया। उसने वारंगल राज्य पर विजय प्राप्त की। उन्होंने फिरोज शाह बहमनी का स्थान लिया। <strong>बहमनी साम्राज्य के अन्य शासक:</strong> अला-उद-दीन हसन बहमन शाह (जिन्हें जफर खान के नाम से भी जाना जाता है) ने 1347 ई. में बहमनी साम्राज्य की स्थापना की। <strong>मुहम्मद शाह-I</strong> ने वारंगल के कपया नायकों और विजयनगर के शासक बुक्का-I को हराया।<strong>मुहम्मद शाह-II</strong> ने कई मस्जिदों, मदरसों (शिक्षा का स्थान) और अस्पतालों का निर्माण किया। फ़िरोज़ शाह बहमनी ने विजयनगर के शासक देव राय प्रथम को हराया।<strong> मुहम्मद शाह-III</strong> ने कोंकण, उड़ीसा, संगमेश्वर और विजयनगर के शासकों को हराया। हुमायूँ शाह ने महमूद गावन को \'व्यापारियों के प्रमुख\' या मलिकुत-तुज्जर की उपाधि प्रदान की।</p>",
                    correct: "b",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "108",
                    section: "misc",
                    question_en: "<p>48. Who coined the term Bharatnatyam for the Sadir dance ?</p>",
                    question_hi: "<p>48. सदिर नृत्य के लिए भरतनाट्यम शब्द किसने गढ़ा?</p>",
                    options_en: ["<p>E. Krishna Iyer</p>", "<p>Rukmini Devi Arundale</p>", 
                                "<p>Kelucharan Mohapatra</p>", "<p>Bharata Muni</p>"],
                    options_hi: ["<p>ई कृष्णा अय्यर</p>", "<p>रुक्मिणी देवी अरुंडेल</p>",
                                "<p>केलुचरण महापात्र</p>", "<p>भरत मुनि</p>"],
                    solution_en: "<p>48.(a) <strong>E.Krishna Iyer.</strong> The origin of Bharatanatyam is linked to the ancient dance of Sadir Attam. It is strictly based on the Natya Shastra, originated from within the temple complexes. Natya Shastra was written by Bharatmuni. Rukmini Devi Arundale (Bharatnatyam dancer). Kelucharan Mohapatra (Odissi Dancer).</p>",
                    solution_hi: "<p>48.(a) <strong>ई.कृष्णा अय्यर</strong>। भरतनाट्यम का उद्भव सादिर अट्टम के प्राचीन नृत्य से जुड़ा हुआ है। यह सख्ती से नाट्य शास्त्र पर आधारित है, जिसकी उत्पत्ति मंदिर परिसर के भीतर से हुई है। नाट्य शास्त्र की रचना भरतमुनि ने की थी। रुक्मिणी देवी अरुंडेल (भरतनाट्यम नर्तकी)। केलुचरण महापात्रा (ओडिसी नर्तक)।</p>",
                    correct: "a",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "109",
                    section: "misc",
                    question_en: "<p>49.The Mettur Dam is on which of the following rivers?</p>",
                    question_hi: "<p>49. मेट्टूर बांध निम्नलिखित में से किस नदी पर है ?</p>",
                    options_en: ["<p>Cauvery</p>", "<p>Krishna</p>", 
                                "<p>Vaigai</p>", "<p>Periyar</p>"],
                    options_hi: ["<p>कावेरी</p>", "<p>कृष्ण</p>",
                                "<p>वैगई</p>", "<p>पेरियार</p>"],
                    solution_en: "<p>49.(a) The <strong>Mettur Dam</strong> is the largest in Tamil Nadu, located across the river <strong>Cauvery</strong>. Another dam located on Cauvery river is <strong>Krishnarajasagar Dam </strong>in Karnataka. Krishna River (Basava Sagar Dam - Karnataka, Nagarjuna Sagar Dam and Srisailam Dam - Border between Telangana and Andhra Pradesh); <strong>Vaigai </strong>river (Vaigai Dam); <strong>Periyar </strong>River (Mullaperiyar Dam - Kerala).</p>",
                    solution_hi: "<p>49.(a) तमिलनाडु में <strong>कावेरी </strong>नदी के पर स्थित <strong>मेट्टूर बांध</strong> सबसे बड़ा बांध है। कावेरी नदी पर स्थित एक अन्य बांध कर्नाटक में <strong>कृष्णराजसागर बांध </strong>है। कृष्णा नदी (बसवा सागर बांध - कर्नाटक, नागार्जुन सागर बांध और श्रीशैलम बांध - तेलंगाना और आंध्र प्रदेश के बीच की सीमा); <strong>वैगई </strong>नदी (वैगई बांध); <strong>पेरियार </strong>नदी (मुल्लापेरियार बांध - केरल)।</p>",
                    correct: "a",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "110",
                    section: "misc",
                    question_en: "<p>50. Harivansh Rai Bacchan won the Sahitya Akademi Award for &lsquo;Do Chattanen&rsquo; (Poetry) in a year ?</p>",
                    question_hi: "<p>50. हरिवंश राय बच्चन ने किस वर्ष में \'दो चट्टानें\' (कविता) के लिए साहित्य अकादमी पुरस्कार जीता था ?</p>",
                    options_en: ["<p>1959</p>", "<p>1968</p>", 
                                "<p>1963</p>", "<p>1964</p>"],
                    options_hi: ["<p>1959</p>", "<p>1968</p>",
                                "<p>1963</p>", "<p>1964</p>"],
                    solution_en: "<p>50.(b) <strong>1968. Other Awards:</strong> Padma Bhusan (1976) and Yash Bharti Samman (1994). Famous <strong>works </strong>by him are :- &lsquo;Madhushala&rsquo; , &lsquo;Madhukalash&rsquo;, &lsquo;Milan Yamini&rsquo;, &lsquo;Jala Sameta&rsquo;, &lsquo;Neeli Chidiya&rsquo;, &lsquo;Janamdin ki Bhent&rsquo; , &lsquo;Madhshala&rsquo;, &lsquo;Satarangini&rsquo;, &lsquo;Kya Bhulu Kya Yaad Karu&rsquo;, &lsquo;Neerh Ka Nirman Phir&rsquo;, &lsquo;Basere Se Door&rsquo;, &lsquo;Dashdwaar se Sopaan tak&rsquo;, &lsquo;Tera Haar&rsquo;, &lsquo;Madhubala&rsquo;, &lsquo;Nisha Nimantran&rsquo;, &lsquo;Agneepath&rsquo;, &lsquo;Aakul Antar&rsquo; etc..</p>",
                    solution_hi: "<p>50.(b) <strong>1968</strong>। <strong>अन्य पुरस्कार: </strong>पद्म भूषण (1976) और यश भारती सम्मान (1994)। उनकी प्रसिद्ध <strong>कृतियाँ </strong>हैं:- \'मधुशाला\', \'मधुकलश\', \'मिलन यामिनी\', \'जला समेटा\', \'नीली चिड़िया\', \'जनमदीन की भेंट\', \'मधशाला\', \'सतरंगिणी\', \'क्या भुलु क्या याद करू\' \', \'नीर का निर्माण फिर\', \'बसेरे से दूर\', \'दशद्वार से सोपान तक\', \'तेरा हार\', \'मधुबाला\', \'निशा निमंत्रन\', \'अग्निपथ\', \'आकुल अंतर\' आदि ।</p>",
                    correct: "b",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "111",
                    section: "misc",
                    question_en: "51. Consider the following statements regarding ordinances.<br />1. Article 123 of the Constitution of India grants the President certain law-making powers to promulgate when either of the two Houses of the Parliament is not in session.<br />2. An ordinance must be converted into legislative within 35 days of the commencement of the Parliament session or else it will lapse.<br />3. Article 213 of the Constitution of India grants the Governor of India the power to issue ordinances when the state legislature is not in session.<br />Which of the statements given above is/are correct?",
                    question_hi: "51. अध्यादेशों के संबंध में निम्नलिखित कथनों पर विचार करें।<br />1. भारत के संविधान का अनुच्छेद 123 संसद के दोनों सदनों में से किसी एक के सत्र में नहीं होने पर राष्ट्रपति को कानून बनाने की कुछ शक्तियां प्रदान करता है।<br />2. संसद सत्र शुरू होने के 35 दिनों के भीतर एक अध्यादेश को विधायी में परिवर्तित किया जाना चाहिए अन्यथा यह समाप्त हो जाएगा।<br />3. भारत के संविधान का अनुच्छेद 213 भारत के राज्यपाल को अध्यादेश जारी करने की शक्ति प्रदान करता है जब राज्य विधानमंडल सत्र में नहीं होता है।<br />ऊपर दिए गए कथनों में से कौन सा सही हैं ?",
                    options_en: [" Only 1 and 3", " 1, 2 and 3", 
                                " Only 1", " Only 1 and 2"],
                    options_hi: [" केवल 1 और 3", " 1, 2 और 3",
                                " केवल 1", " केवल 1 और 2"],
                    solution_en: "51.(a) President and Governor can promulgate ordinances when houses are not in session under Articles 123 and 213 respectively. The maximum validity of an ordinance is 6 months and 6 weeks. An ordinance will expire after 6 weeks (42 days) once both houses of the Parliament are in session.",
                    solution_hi: "51.(a) राष्ट्रपति और राज्यपाल क्रमश: अनुच्छेद 123 और 213 के तहत सदनों के सत्र में नहीं होने पर अध्यादेश जारी कर सकते हैं। अध्यादेश की अधिकतम वैधता 6 महीने और 6 सप्ताह है। संसद के दोनों सदनों के सत्र चलने के बाद 6 सप्ताह (42 दिन) के बाद एक अध्यादेश समाप्त हो जाएगा।",
                    correct: "a",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "112",
                    section: "misc",
                    question_en: "<p>52. Haymaker is the sports terminology used in which of the following sports ?</p>",
                    question_hi: "<p>52. निम्नलिखित में से &lsquo;हेमेकर&rsquo; किस खेल में प्रयुक्त होने वाली खेल शब्दावली है?</p>",
                    options_en: ["<p>Badminton</p>", "<p>Baseball</p>", 
                                "<p>Boxing</p>", "<p>Bridge</p>"],
                    options_hi: ["<p>बैडमिंटन</p>", "<p>बेसबॉल</p>",
                                "<p>मुक्केबाजी</p>", "<p>ब्रिज</p>"],
                    solution_en: "<p>52.(c) <strong>Boxing Terminology</strong>:- Cross, Cutman, Dive, Eight Count, Glass Jaw, Haymaker, Liver Shot, Low Blow, Mauler, Neutral Corner, Plodder, Ring Generalship, Roughhousing, Southpaw, Spar, Stablemate, Technical Knockout, Walkout Bout, Whiskers, Knock out, Ring Stoppage, Punch, Round, Upper-cut, Kidney punch, Timing, Footwork, Accidental Butt, Bleeder, Bolo Punch, Bout, Brawler, Break, Buckle, Canvas, Card, Caught Cold, Clinch, Corkscrew Punch, Cornerman, Counterpunch etc.</p>",
                    solution_hi: "<p>52.(c) <strong>बॉक्सिंग शब्दावली</strong>:- क्रॉस, कटमैन, डाइव, आठ काउंट, ग्लास जॉ, हेमेकर, लीवर शॉट, लो ब्लो, मौलर, न्यूट्रल कॉर्नर, प्लोडर, रिंग जनरलशिप, रफहाउसिंग, साउथपॉ, स्पर, स्टेबलमेट, टेक्निकल नॉकआउट, वॉकआउट बाउट, व्हिस्कर्स, नॉक आउट, रिंग स्टॉपेज, पंच, राउंड, अपर-कट, किडनी पंच, टाइमिंग, फुटवर्क, एक्सीडेंटल बट, ब्लीडर, बोलो पंच, बाउट, ब्रॉलर, ब्रेक, बकल, कैनवस, कार्ड, कोट कोल्ड , क्लिंच, कॉर्कस्क्रू पंच, कॉर्नरमैन, काउंटरपंच इत्यादि ।</p>",
                    correct: "c",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "113",
                    section: "misc",
                    question_en: "<p>53. Mawsynram is the wettest place on earth and it is situated in</p>",
                    question_hi: "<p>53. मासिनराम पृथ्वी पर सबसे नम स्थान है और यह स्थित है ?</p>",
                    options_en: ["<p>Rajmahal Hills</p>", "<p>Khasi Hills</p>", 
                                "<p>Mahadeo Hills</p>", "<p>Lushai Hills</p>"],
                    options_hi: ["<p>राजमहल हिल्स</p>", "<p>खासी हिल्स</p>",
                                "<p>महादेव हिल्स</p>", "<p>लुशाई हिल्स</p>"],
                    solution_en: "<p>53.(b) <strong>Mawsynram </strong>is located in the East Khasi Hills district of Meghalaya. Mawsynram receives the highest rainfall in India. The <strong>Rajmahal hills </strong>are named after the town of Rajmahal which lies in the eastern Jharkhand. <strong>Mahadeo Hills</strong>, sandstone hills located in the northern part of the Satpura Range, in southern Madhya Pradesh. The <strong>Lushai Hills </strong>(Mizo Hills) are a mountain range in Mizoram and Manipur, India. The range is part of the Patkai range system and its highest point is Phawngpui (\'Blue Mountain\').</p>",
                    solution_hi: "<p>53.(b) <strong>मासिनराम </strong>मेघालय के पूर्वी खासी हिल्स जिले में स्थित है। मासिनराम भारत में सबसे अधिक वर्षा प्राप्त करता है। राजमहल पहाड़ियों का नाम राजमहल शहर के नाम पर रखा गया है जो पूर्वी झारखंड में स्थित है। <strong>महादेव पहाड़ियाँ</strong>, दक्षिणी मध्य प्रदेश में सतपुड़ा रेंज के उत्तरी भाग में स्थित बलुआ पत्थर की पहाड़ियाँ है । <strong>लुशाई हिल्स </strong>(मिज़ो हिल्स) मिज़ोरम और मणिपुर, भारत में एक पर्वत श्रृंखला है। यह श्रेणी पटकाई श्रेणी प्रणाली का हिस्सा है और इसका उच्चतम बिंदु फौंगपुई (\'ब्लू माउंटेन\') है</p>",
                    correct: "b",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "114",
                    section: "misc",
                    question_en: "<p>54. Who is the founder of Banaras Gharana?</p>",
                    question_hi: "<p>54. बनारस घराने के संस्थापक कौन हैं ?</p>",
                    options_en: ["<p>Pt Gopal Mishra</p>", "<p>Ghagge Nazir Khan</p>", 
                                "<p>Ustad Chajju Khan</p>", "<p>Ustad Mamman Khan</p>"],
                    options_hi: ["<p>पंडित गोपाल मिश्रा</p>", "<p>घग्गे नजीर खान</p>",
                                "<p>उस्ताद छज्जू खान</p>", "<p>उस्ताद मम्मन खान</p>"],
                    solution_en: "<p>54.(a) <strong>Pt Gopal Mishra. </strong>The Benaras Gharana evolved as a result of the great lilting style of khayal singing known by Thumri singers of Benaras and Gaya.<strong> Pandit Ram Sahai,</strong> the founder of the Benares style (&ldquo;gharana&rdquo;) of tabla playing.The chief <strong>exponents </strong>of the Benaras Gharana are Rajan Mishra, Sajan Mishra, Girija Devi etc. Ghagge Nazir Khan (Mewati Gharana), Ustad Chajju Khan (Bhendi Baazaar), Ustad Mamman Khan (Sursagar - combination of Sarangi, Sitar and Surbahar).</p>",
                    solution_hi: "<p>54.(a)<strong> पंडित गोपाल मिश्रा</strong> । बनारस घराना बनारस और गया के ठुमरी गायकों द्वारा ज्ञात ख्याल गायन की महान प्रफुल्लित करने वाली शैली के परिणामस्वरूप विकसित हुआ। तबला वादन की बनारस शैली (\"घराना\") के संस्थापक <strong>पंडित राम सहाय</strong> है।बनारस घराने के प्रमुख <strong>प्रतिपादक </strong>राजन मिश्रा, साजन मिश्रा, गिरिजा देवी आदि हैं। घग्गे नजीर खान (मेवाती घराना), उस्ताद छज्जू खान (भेंडी बाजार), उस्ताद मम्मन खान (सूरसागर - सारंगी, सितार और सुरबहार का मेल) ।</p>",
                    correct: "a",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "115",
                    section: "misc",
                    question_en: "<p>55. Abdur Razzak was a _______ traveler who visited India from January 1442 to January 1445.</p>",
                    question_hi: "<p>55. अब्दुर रज्जाक एक _______ यात्री था जिसने जनवरी 1442 से जनवरी 1445 तक भारत का दौरा किया था।</p>",
                    options_en: ["<p>Moroccan</p>", "<p>Chinese</p>", 
                                "<p>Persian</p>", "<p>French</p>"],
                    options_hi: ["<p>मोरक्कन</p>", "<p>चीनी</p>",
                                "<p>फारसी</p>", "<p>फ्रेंच</p>"],
                    solution_en: "<p>55.(c)<strong> Abdur Razzak </strong>was a <strong>Persian </strong>scholar and also an ambassador of Persia who visited India during the rule of Deva Raya II of Vijayanagar. He mentioned India in his book &ldquo;Matla-us-Sadain wa Majma-ul-Bahrain\'\'. <strong>Nicolo Conti</strong> was an Italian merchant who visited India during the reign of <strong>Deva Raya </strong>I of <strong>Vijayanagar </strong>between 1420 A.D. &ndash; 1421 A.D.</p>",
                    solution_hi: "<p>55.(c) <strong>अब्दुर रज्जाक </strong>एक <strong>फ़ारसी </strong>विद्वान था और फारस का एक राजदूत भी था जिसने विजयनगर के देव राय द्वितीय के शासन के दौरान भारत का दौरा किया था। उन्होंने अपनी किताब \'मतला-उस-सदैन व मजमा-उल-बहरीन\' में भारत का जिक्र किया है। <strong>निकोलो कोंटी</strong> एक इतालवी व्यापारी था जिसने 1420 ई. से 1421 ई. के बीच विजयनगर के देव राय प्रथम के शासनकाल के दौरान भारत का दौरा किया था।</p>",
                    correct: "c",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "116",
                    section: "misc",
                    question_en: "<p>56. Which state topped the Northeast Olympic Games (2022) ?</p>",
                    question_hi: "<p>56. पूर्वोत्तर ओलंपिक खेलों (2022) में कौन सा राज्य शीर्ष पर है ?</p>",
                    options_en: ["<p>Assam</p>", "<p>Manipur</p>", 
                                "<p>Arunachal Pradesh</p>", "<p>Meghalaya</p>"],
                    options_hi: ["<p>असम</p>", "<p>मणिपुर</p>",
                                "<p>अरुणाचल प्रदेश</p>", "<p>मेघालय</p>"],
                    solution_en: "<p>56.(b) <strong>Manipur</strong> gets the top position in the Northeast Olympic Games for the second consecutive time with 237 medals, including 85 gold, 76 silver, and 77 bronze as the second edition of the regional multi-sport event.</p>",
                    solution_hi: "<p>56.(b) <strong>मणिपुर </strong>ने क्षेत्रीय बहु-खेल आयोजन के दूसरे संस्करण के रूप में 85 स्वर्ण, 76 रजत और 77 कांस्य सहित 237 पदकों के साथ लगातार दूसरी बार पूर्वोत्तर ओलंपिक खेलों में शीर्ष स्थान प्राप्त किया है।</p>",
                    correct: "b",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "117",
                    section: "misc",
                    question_en: "<p>57. _______law states that the amount of radiation emitted by a black body per unit area is directly proportional to the fourth power of the temperature.</p>",
                    question_hi: "<p>57. _______ नियम बताता है कि प्रति इकाई क्षेत्र में एक कृष्णिका द्वारा उत्सर्जित विकिरण की मात्रा तापमान की चौथी घात के सीधे सानुपातिक है।</p>",
                    options_en: ["<p>Pascal\'s Law</p>", "<p>Hooke\'s Law</p>", 
                                "<p>Stefan-Boltzmann</p>", "<p>Coulomb\'s Law</p>"],
                    options_hi: ["<p>पास्कल का नियम</p>", "<p>हुक का नियम</p>",
                                "<p>स्टीफन-बोल्टज़मान</p>", "<p>कूलम्ब का नियम</p>"],
                    solution_en: "<p>57.(c) Stefan-Boltzmann Law. <strong>Pascal\'s law</strong> says that pressure applied to an enclosed fluid will be transmitted without a change in magnitude to every point of the fluid and to the walls of the container. <strong>Hooke\'s law</strong> states that the strain of the material is proportional to the applied stress within the elastic limit of that material. <strong>Coulomb\'s</strong> inverse-square law, is an experimental law of physics that quantifies the amount of force between two stationary, electrically charged particles.</p>",
                    solution_hi: "<p>57.(c) स्टीफन-बोल्ट्जमैन नियम। <strong>पास्कल का नियम</strong> कहता है कि एक बंद तरल पदार्थ पर लगाया गया दबाव द्रव के प्रत्येक बिंदु और कंटेनर की दीवारों पर परिमाण में बदलाव के बिना प्रेषित किया जाएगा। <strong>हुक का नियम</strong> कहता है कि सामग्री का तनाव उस सामग्री की लोचदार सीमा के भीतर लागू तनाव के समानुपाती होता है। <strong>कूलम्ब का व्युत्क्रम-वर्ग नियम</strong>, भौतिकी का एक प्रायोगिक नियम है जो दो स्थिर, विद्युत आवेशित कणों के बीच बल की मात्रा को निर्धारित करता है।</p>",
                    correct: "c",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "118",
                    section: "misc",
                    question_en: "<p>58. &lsquo;Singpho&rsquo; tribes belong to which state?</p>",
                    question_hi: "<p>58. \'सिंगफो\' जनजाति किस राज्य से संबंधित है ?</p>",
                    options_en: ["<p>Arunachal Pradesh</p>", "<p>Assam</p>", 
                                "<p>Manipur</p>", "<p>Meghalaya</p>"],
                    options_hi: ["<p>अरुणाचल प्रदेश</p>", "<p>असम</p>",
                                "<p>मणिपुर</p>", "<p>मेघालय</p>"],
                    solution_en: "<p>58.(a) <strong>More tribes of Arunachal Pradesh </strong>are :- Apatanis, Abor, Mishmi, Idu, Taroan, Tagin, Adi, Monpa, Galong, Momba, Sherdukpen, Nyishi, Wancho. <strong>Assam</strong>: Chakma, Boro, Boro Kachari, Kachari, Sonwal, Miri, Rabha, Garo, Dimasa, Hajong, Garos, Khasis, Gangte, Karbi. <strong>Manipur</strong>: Naga, Kuki, Meitei, Aimol, Angami, Chiru, Maram, Monsang, Paite, Purum, Thadou, Anal, Mao, Tangkhul, Thadou, Poumai Naga. <strong>Meghalaya</strong>: Chakma, Garos, Hajong, Jaintias Khasis, Lakher, Pawai, Raba, Mikir.</p>",
                    solution_hi: "<p>58.(a) <strong>अरुणाचल प्रदेश की अन्य जनजातियाँ</strong>:- अपतानी, अबोर, मिश्मी, इडु, तारोआँ, तागिन, आदि, मोनपा, गैलोंग, मोम्बा, शेरडुकपेन, न्यिशी, वांचो। <strong>असम</strong>: चकमा, बोरो, बोरो कचहरी, कचहरी, सोनवाल, मिरी, राभा, गारो, दिमासा, हजोंग, गारो, खासी, गंगटे, कार्बी। <strong>मणिपुर</strong>: नागा, कूकी, मैतेई, ऐमोल, अंगामी, चिरू, मरम, मोनसांग, पैते, पुरुम, थडौ, अनल, माओ, तांगखुल, थडौ, पौमई नागा। <strong>मेघालय</strong>: चकमा, गारो, हाजोंग, खासी जयंतियां, लखेर, पवई, राबा, मिकिर।</p>",
                    correct: "a",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "119",
                    section: "misc",
                    question_en: "59. What is the scientific name of ‘Night Jasmine’ ?",
                    question_hi: "59. ‘ नाइट जैसमीन\' का वैज्ञानिक नाम क्या है ?",
                    options_en: [" Butea monosperma", " Nyctanthes arbortristis", 
                                " Helianthus annus ", " Lilium"],
                    options_hi: [" ब्यूटिया मोनोस्पर्मा (Butea monosperma)", " निक्टेंथस अरबोर्ट्रिस्टिस (Nyctanthes arbortristis)",
                                " हेलियनथस एनस (Helianthus annus)", " लिलियम (Lilium)"],
                    solution_en: "59.(b)  Nyctanthes arbortristis ⇒’Night Jasmine’, Butea monosperma ⇒ Palash,  Helianthus annus⇒ Sunflower, Lilium ⇒ Lily, Some more flowers and their scientific names:- Lotus ⇒ Nelumbium speciosum, Marigold ⇒Tagetes, Chameli ⇒ Jasminum grandiflorum,  Daffodil ⇒ Narcissus,  Rose ⇒ Rosa Rubiginosa.",
                    solution_hi: "59.(b) निक्टेंथस अरबोर्ट्रिस्टिस  ⇒ \'नाईट जैसमीन \', ब्यूटिया मोनोस्पर्मा ⇒ पलाश, हेलियनथस एनस ⇒ सूरजमुखी, लिलियम ⇒ लिली, कुछ और फूल और उनके वैज्ञानिक नाम:- कमल ⇒ नेलुम्बियम स्पेसिओसम, गेंदे का फूल ⇒ टैगेटेस, चमेली ⇒ जैस्मीनम ग्रैंडिफ्लोरम, डैफोडिल ⇒ नार्सिसस, गुलाब ⇒ रोजा रुबिगिनोसा।",
                    correct: "b",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "120",
                    section: "misc",
                    question_en: "<p>60. Shanti Swarup Bhatnagar prize was awarded first time in which year?</p>",
                    question_hi: "<p>60. शांति स्वरूप भटनागर पुरस्कार पहली बार किस वर्ष प्रदान किया गया था ?</p>",
                    options_en: ["<p>1957</p>", "<p>1958</p>", 
                                "<p>1959</p>", "<p>1960</p>"],
                    options_hi: ["<p>1957</p>", "<p>1958</p>",
                                "<p>1959</p>", "<p>1960</p>"],
                    solution_en: "<p>60.(b) <strong>1958</strong>. Shanti Swarup Bhatnagar prize is awarded in Science &amp; Technology. <strong>Kariamanickam </strong>Srinivasa Krishnan was first awarded in 1958 for Physical science. <strong>2021</strong>- Arun Kumar Shukla (Structural Biology, Cellular Signaling, Pharmacology Membrane Proteins, G protein-Coupled Receptors), Amit Singh (Microbiology and Biochemistry).</p>",
                    solution_hi: "<p>60.(b) <strong>1958 </strong>| शांति स्वरूप भटनागर पुरस्कार विज्ञान और प्रौद्योगिकी में प्रदान किया जाता है। <strong>कर्मनिक्कम </strong>श्रीनिवास कृष्णन को पहली बार 1958 में भौतिक विज्ञान के लिए सम्मानित किया गया था। <strong>2021</strong>- अरुण कुमार शुक्ला (स्ट्रक्चरल बायोलॉजी, सेल्युलर सिग्नलिंग, फार्माकोलॉजी मेम्ब्रेन प्रोटीन, जी प्रोटीन-युग्मित रिसेप्टर्स), अमित सिंह (माइक्रोबायोलॉजी और बायोकेमिस्ट्री)।</p>",
                    correct: "b",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "121",
                    section: "misc",
                    question_en: "<p>61. Which of the following items is included in National income?</p>",
                    question_hi: "<p>61. निम्नलिखित में से कौन सी वस्तु राष्ट्रीय आय में शामिल है ?</p>",
                    options_en: ["<p>unemployment allowance</p>", "<p>Windfall gains</p>", 
                                "<p>shares</p>", "<p>Estimated rent of the property</p>"],
                    options_hi: ["<p>बेरोजगारी भत्ता</p>", "<p>अप्रत्याशित लाभ</p>",
                                "<p>शेयर</p>", "<p>संपत्ति का अनुमानित किराया</p>"],
                    solution_en: "<p>61.(d) Items <strong>included in National income:</strong> Goods produced for self-consumption and Estimated rent of the self-occupied property. Items <strong>not included</strong> in National Income: Transfer payments (unilateral payments made without expectations of return; like gifts, unemployment allowance, donations, etc), Sale and purchase of old goods and existing services (shares are not included unless they are through an IPO), Windfall gains (lottery income), Black money, and Work done by housewives.</p>",
                    solution_hi: "<p>61.(d) <strong>राष्ट्रीय आय में शामिल वस्तुएं:</strong> स्व-उपभोग के लिए उत्पादित वस्तुएं और स्व-अधिकृत संपत्ति का अनुमानित किराया। मदें (Items) जो राष्ट्रीय आय में <strong>शामिल नहीं हैं:</strong> स्थानांतरण भुगतान (वापसी की उम्मीद के बिना किए गए एकतरफा भुगतान; जैसे उपहार, बेरोजगारी भत्ता, दान, आदि), पुराने सामान और मौजूदा सेवाओं की बिक्री और खरीद (शेयर तब तक शामिल नहीं हैं जब तक कि वे IPO के माध्यम से न हों) , अप्रत्याशित लाभ (लॉटरी आय), काला धन, और गृहिणियों द्वारा किया गया कार्य।</p>",
                    correct: "d",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "122",
                    section: "misc",
                    question_en: "62. ___ dance has been performed in Odisha for centuries by young boys, who dress as women to praise Jagannath and Krishna.",
                    question_hi: "62. ___ उड़ीसा में युवा लड़कों द्वारा सदियों से नृत्य किया जाता रहा है, जो जगन्नाथ और कृष्ण की स्तुति करने के लिए महिलाओं के रूप में कपड़े पहनते हैं।",
                    options_en: [" Sattriya      ", " Lavani ", 
                                " Gotipua     ", "  Chhau"],
                    options_hi: [" सत्त्रिया ", " लावणी",
                                " गोटीपुआ ", " छऊ"],
                    solution_en: "62.(c) Gotipua (Odisha).  Sattriya (Assam), Lavani (Maharashtra), Chhau (Jharkhand, West Bengal and Odisha).",
                    solution_hi: "62.(c) गोटीपुआ (ओडिशा)। सत्त्रिया (असम), लावणी (महाराष्ट्र), छाऊ (झारखंड, पश्चिम बंगाल और ओडिशा)।",
                    correct: "c",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "123",
                    section: "misc",
                    question_en: "63. Who won a bronze medal in the Asian Cup Table Tennis tournament in Bangkok?",
                    question_hi: "63. बैंकॉक में एशियाई कप टेबल टेनिस टूर्नामेंट में कांस्य पदक किसने जीता ?",
                    options_en: [" Poulomi Ghatak", " Chen Xingtong", 
                                " Hina Hayata                ", " Manika Batra<br /> "],
                    options_hi: [" पोलोमी घटक               ", " चेन जिंगटोंग",
                                " हिना हयाता                 ", " मनिका बत्रा"],
                    solution_en: "63.(d) Indian Table Tennis Player Manika Batra defeated World No. 6 Hina Hayata of Japan in the Asian Cup 2022 women’s singles bronze medal match in Bangkok, Thailand on November 18, 2022.",
                    solution_hi: "63.(d) भारतीय टेबल टेनिस खिलाड़ी मनिका बत्रा ने 18 नवंबर, 2022 को बैंकाक, थाईलैंड में एशियाई कप 2022 महिला एकल कांस्य पदक मैच में जापान की विश्व नंबर 6 हिना हयाता को हराया।",
                    correct: "d",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "124",
                    section: "misc",
                    question_en: "<p>64. ______ refers to the formation of the plant from a seed without fertilization or normal sexual reproduction.</p>",
                    question_hi: "<p>64. ______ निषेचन या सामान्य यौन प्रजनन के बिना बीज से पौधे के गठन को संदर्भित करता है।</p>",
                    options_en: ["<p>Gametogamy</p>", "<p>Parthenocarpy</p>", 
                                "<p>Hybridogenesis</p>", "<p>Apomixis</p>"],
                    options_hi: ["<p>गैमेटोगैमी</p>", "<p>पार्थेनोकार्पी</p>",
                                "<p>हाइब्रिडोजेनेसिस</p>", "<p>एपोमिक्सिस</p>"],
                    solution_en: "<p>64.(d)&nbsp;<strong>Apomixis </strong>is an asexual reproduction that occurs without fertilization but producing embryos and seeds. One example of apomixis is apomictic parthenogenesis. <strong>Gametogamy </strong>is sexual reproduction involving the formation of male and female reproductive cells which join together to form a zygote; also called syngamy. <strong>Parthenocarpy </strong>refers to the development of fruit without fertilization. <strong>Hybridogenesis </strong>is an unusual form of reproduction that is found in hybrids between different species.</p>",
                    solution_hi: "<p>64.(d) <strong>एपोमिक्सिस </strong>(<strong>apomixis</strong>) एक अलैंगिक प्रजनन है जो निषेचन के बिना होता है लेकिन भ्रूण और बीज पैदा करता है। एपोमिक्सिस का एक उदाहरण एपोमिक्टिक पार्थेनोजेनेसिस (apomictic parthenogenesis) है। <strong>गैमेटोगैमी </strong>(<strong>gametogamy</strong>) यौन प्रजनन है जिसमें पुरुष और महिला प्रजनन कोशिकाओं का निर्माण होता है जो एक साथ जुड़कर एक युग्मज (Zygote) बनाते हैं जिसे युग्मक भी कहा जाता है। <strong>पार्थेनोकार्पी </strong>(<strong>parthenocarpy</strong>) बिना निषेचन के फल के विकास को संदर्भित करता है। <strong>हाइब्रिडोजेनेसिस </strong>(<strong>Hybridogenesis</strong>) प्रजनन का एक असामान्य रूप है जो विभिन्न प्रजातियों के बीच संकरों में पाया जाता है।</p>",
                    correct: "d",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "125",
                    section: "misc",
                    question_en: "<p>65. Puanchei is the traditional dress of which state ?</p>",
                    question_hi: "<p>65. पुआनचेई किस राज्य की पारंपरिक पोशाक है ?</p>",
                    options_en: ["<p>Meghalaya</p>", "<p>Mizoram</p>", 
                                "<p>Assam</p>", "<p>Kerala</p>"],
                    options_hi: ["<p>मेघालय</p>", "<p>मिजोरम</p>",
                                "<p>असम</p>", "<p>केरल</p>"],
                    solution_en: "<p>65.(b)<strong> Puanchei</strong>, the gorgeous dress of <strong>Mizo girls </strong>is a must during weddings and festivals such as &lsquo;Chapchar Kut&rsquo; and &lsquo;Pawl Kut &lsquo;. <strong>Assam </strong>(&lsquo;Mekhela-Chador&lsquo; or &lsquo;Riha-Mekhela&rsquo;, Chiarchin, Suria, Seleng, Muga Silk ). <strong>Meghalaya </strong>(Jainsem or Dhara, Dakmanda, Thoh Karawang). <strong>Kerala </strong>(Mundum Neriyathum, neriyathu, Mundu).</p>",
                    solution_hi: "<p>65.(b) <strong>पुआनचेई, मिज़ो लड़कियों </strong>की भव्य पोशाक शादियों और त्योहारों जैसे \'चापचर कुट\' और \'पावल कुट\' के दौरान बहुत जरूरी होती है। <strong>असम </strong>(\'मेखला-चादोर\' या \'रिहा-मेखला\', चियार्चिन, सूरिया, सेलेंग, मुगा सिल्क)। <strong>मेघालय </strong>(जैनसेम या धारा, डाकमांडा, थो करावांग)। <strong>केरल </strong>(मुंडम नेरियथुम, नेरियाथु, मुंडू)।</p>",
                    correct: "b",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "126",
                    section: "misc",
                    question_en: "<p>66. In _______ Mahadevi Verma was awarded for poetry collection Yama in Hindi language.</p>",
                    question_hi: "<p>66. _______ में महादेवी वर्मा को हिंदी भाषा में कविता संग्रह &lsquo; यामा&rsquo; (Yama) के लिए सम्मानित किया गया।</p>",
                    options_en: ["<p>1984</p>", "<p>1982</p>", 
                                "<p>1985</p>", "<p>1988</p>"],
                    options_hi: ["<p>1984</p>", "<p>1982</p>",
                                "<p>1985</p>", "<p>1988</p>"],
                    solution_en: "<p>66.(b) <strong>1982. Mahadevi Varma </strong>was an Indian Hindi-language poet who has been also addressed as the Modern Meera. Her other awards include 1956 (Padma Bhushan), 1979 (Sahitya Akademi Fellowship), and 1988 (Padma Vibhushan). Her important <strong>works </strong>include &lsquo;Nihaar&rsquo; (1930), &lsquo;Rashmi&rsquo; (1932), &lsquo;Niraja&rsquo; (1934), &lsquo;Sandhya geet&rsquo; (1936), &lsquo;Smriti ki rekhayen&rsquo; (1943), &lsquo;Path ke saathi&rsquo; (1956), and &lsquo;Mera parivaar&rsquo; (1971), etc.</p>",
                    solution_hi: "<p>66.(b) <strong>1982. महादेवी वर्मा </strong>एक भारतीय हिंदी भाषा की कवयित्री थीं जिन्हें आधुनिक मीरा के नाम से भी संबोधित किया गया है। उनके अन्य पुरस्कारों में 1956 (पद्म भूषण), 1979 (साहित्य अकादमी फैलोशिप), और 1988 (पद्म विभूषण) शामिल हैं। उनकी महत्वपूर्ण <strong>रचनाओं </strong>में \'निहार\' (1930), \'रश्मि\' (1932), \'नीरजा\' (1934), \'संध्या गीत\' (1936), \'स्मृति की रेखाएँ\' (1943), \'पथ के साथी\' (1956) और \'मेरा परिवार\' (1971) आदि शामिल हैं।</p>",
                    correct: "b",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "127",
                    section: "misc",
                    question_en: "<p>67. The second member of the alkyne group of hydrocarbons is:</p>",
                    question_hi: "<p>67. हाइड्रोकार्बन के ऐल्काइन समूह का दूसरा सदस्य है:-</p>",
                    options_en: ["<p>butyne</p>", "<p>propyne</p>", 
                                "<p>methyne</p>", "<p>ethyne</p>"],
                    options_hi: ["<p>ब्यूटेन</p>", "<p>प्रोपाइन</p>",
                                "<p>मिथाइन</p>", "<p>एथीन</p>"],
                    solution_en: "<p>67.(b) <strong>propyne</strong>. The molecular formulas and names of the first ten carbon straight chain alkynes: Ethyne (<math xmlns=\"http://www.w3.org/1998/Math/MathML\" class=\"wrs_chemistry\"><msub><mi mathvariant=\"normal\">C</mi><mn>2</mn></msub><msub><mi mathvariant=\"normal\">H</mi><mn>2</mn></msub></math>), Propyne (<math xmlns=\"http://www.w3.org/1998/Math/MathML\" class=\"wrs_chemistry\"><msub><mi mathvariant=\"normal\">C</mi><mn>3</mn></msub><msub><mi mathvariant=\"normal\">H</mi><mn>4</mn></msub></math>), 1-Butyne (<math xmlns=\"http://www.w3.org/1998/Math/MathML\" class=\"wrs_chemistry\"><msub><mi mathvariant=\"normal\">C</mi><mn>4</mn></msub><msub><mi mathvariant=\"normal\">H</mi><mn>6</mn></msub></math>), 1-Pentyne (<math xmlns=\"http://www.w3.org/1998/Math/MathML\" class=\"wrs_chemistry\"><msub><mi mathvariant=\"normal\">C</mi><mn>5</mn></msub><msub><mi mathvariant=\"normal\">H</mi><mn>8</mn></msub></math>), 1-Hexyne (<math xmlns=\"http://www.w3.org/1998/Math/MathML\" class=\"wrs_chemistry\"><msub><mi mathvariant=\"normal\">C</mi><mn>6</mn></msub><msub><mi mathvariant=\"normal\">H</mi><mn>10</mn></msub></math>), 1-Heptyne (<math xmlns=\"http://www.w3.org/1998/Math/MathML\" class=\"wrs_chemistry\"><msub><mi mathvariant=\"normal\">C</mi><mn>7</mn></msub><msub><mi mathvariant=\"normal\">H</mi><mn>12</mn></msub></math>), 1-Octyne (<math xmlns=\"http://www.w3.org/1998/Math/MathML\" class=\"wrs_chemistry\"><msub><mi mathvariant=\"normal\">C</mi><mn>8</mn></msub><msub><mi mathvariant=\"normal\">H</mi><mn>14</mn></msub></math>), 1-Nonyne (<math xmlns=\"http://www.w3.org/1998/Math/MathML\" class=\"wrs_chemistry\"><msub><mi mathvariant=\"normal\">C</mi><mn>9</mn></msub><msub><mi mathvariant=\"normal\">H</mi><mn>16</mn></msub></math>), 1-Decyne (<math xmlns=\"http://www.w3.org/1998/Math/MathML\" class=\"wrs_chemistry\"><msub><mi mathvariant=\"normal\">C</mi><mn>10</mn></msub><msub><mi mathvariant=\"normal\">H</mi><mn>18</mn></msub></math>).</p>",
                    solution_hi: "<p>67.(b) <strong>प्रोपाइन</strong>| आण्विक सूत्र और पहले दस कार्बन स्ट्रेट चेन एल्काइन्स के नाम: एथाइन (<math xmlns=\"http://www.w3.org/1998/Math/MathML\" class=\"wrs_chemistry\"><msub><mi mathvariant=\"normal\">C</mi><mn>2</mn></msub><msub><mi mathvariant=\"normal\">H</mi><mn>2</mn></msub></math>), प्रोपाइन (<math xmlns=\"http://www.w3.org/1998/Math/MathML\" class=\"wrs_chemistry\"><msub><mi mathvariant=\"normal\">C</mi><mn>3</mn></msub><msub><mi mathvariant=\"normal\">H</mi><mn>4</mn></msub></math>), 1-ब्यूटाइन (<math xmlns=\"http://www.w3.org/1998/Math/MathML\" class=\"wrs_chemistry\"><msub><mi mathvariant=\"normal\">C</mi><mn>4</mn></msub><msub><mi mathvariant=\"normal\">H</mi><mn>6</mn></msub></math>), 1-पेन्टाइन (<math xmlns=\"http://www.w3.org/1998/Math/MathML\" class=\"wrs_chemistry\"><msub><mi mathvariant=\"normal\">C</mi><mn>5</mn></msub><msub><mi mathvariant=\"normal\">H</mi><mn>8</mn></msub></math>), 1-हेक्साइन (<math xmlns=\"http://www.w3.org/1998/Math/MathML\" class=\"wrs_chemistry\"><msub><mi mathvariant=\"normal\">C</mi><mn>6</mn></msub><msub><mi mathvariant=\"normal\">H</mi><mn>10</mn></msub></math>), 1-हेपटाइन (<math xmlns=\"http://www.w3.org/1998/Math/MathML\" class=\"wrs_chemistry\"><msub><mi mathvariant=\"normal\">C</mi><mn>7</mn></msub><msub><mi mathvariant=\"normal\">H</mi><mn>12</mn></msub></math>), 1-ओकटाइन (<math xmlns=\"http://www.w3.org/1998/Math/MathML\" class=\"wrs_chemistry\"><msub><mi mathvariant=\"normal\">C</mi><mn>8</mn></msub><msub><mi mathvariant=\"normal\">H</mi><mn>14</mn></msub></math>), 1-नोनयइन (<math xmlns=\"http://www.w3.org/1998/Math/MathML\" class=\"wrs_chemistry\"><msub><mi mathvariant=\"normal\">C</mi><mn>9</mn></msub><msub><mi mathvariant=\"normal\">H</mi><mn>16</mn></msub></math>) ), 1-डेकन (decyne) (<math xmlns=\"http://www.w3.org/1998/Math/MathML\" class=\"wrs_chemistry\"><msub><mi mathvariant=\"normal\">C</mi><mn>10</mn></msub><msub><mi mathvariant=\"normal\">H</mi><mn>18</mn></msub></math>)।</p>",
                    correct: "b",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "128",
                    section: "misc",
                    question_en: "<p>68. Who wrote the book titled, &lsquo;Nalanda &ndash; Until we meet again&rsquo;?</p>",
                    question_hi: "<p>68. \'नालंदा -अंटिल वि मीट अगेन\' नामक पुस्तक किसने लिखी है ?</p>",
                    options_en: ["<p>Gautam Borah</p>", "<p>Navtej Sarna</p>", 
                                "<p>Arundhati Rao</p>", "<p>Amitav Ghosh</p>"],
                    options_hi: ["<p>गौतम बोरा</p>", "<p>नवतेज सरना</p>",
                                "<p>अरुंधती राव</p>", "<p>अमिताभ घोष</p>"],
                    solution_en: "<p>68.(a) <strong>Gautam Borah </strong>also wrote &lsquo;Monetising Innovation&rsquo;. Books by <strong>Navtej Sarna</strong>:- &lsquo;The Exile&rsquo;, &lsquo;The Book of Nanak&rsquo;, &lsquo;Winter Evenings&rsquo; etc. <strong>Amitav Ghosh</strong>:- &lsquo;Sea of Poppies&rsquo;, &lsquo;The Hungry Tide&rsquo;, &lsquo;The Shadow Lines&rsquo;, &lsquo;The Glass Palace&rsquo;, &lsquo;River of Smoke&rsquo;, &lsquo;Gun Island&rsquo;, &lsquo;The Calcutta Chromosome&rsquo;, &lsquo;Flood of Fire&rsquo;, &lsquo;The Circle of Reason&rsquo;, &lsquo;Jungle Nama&rsquo;, &lsquo;Countdown&rsquo;, &lsquo;The Living Mountain&rsquo;. <strong>Arundhati Roy</strong>:- &lsquo;God of Small Things&rsquo;, &lsquo;The Ministry of Utmost Happiness&rsquo;, &lsquo;Azadi&rsquo;, &lsquo;The End of Imagination&rsquo;, &lsquo;Come September&rsquo;, &lsquo;The Cost of Living&rsquo;, &lsquo;Walking with the Comrades&rsquo;, &lsquo;The Algebra of Infinite Justice&rsquo;, &lsquo;Capitalism: A Ghost Story&rsquo;, &lsquo;Power Politics&rsquo; etc.</p>",
                    solution_hi: "<p>68.(a) <strong>गौतम बोरा</strong> ने \'मॉनेटाइजिंग इनोवेशन\' भी लिखा था। <strong>नवतेज सरना </strong>की पुस्तकें: - \'द एक्साइल\', \'द बुक ऑफ नानक\', \'विंटर इवनिंग्स\' आदि। <strong>अमिताव घोष</strong>:- \'सी ऑफ पोपीज\', \'द हंग्री टाइड\', \'द शैडो लाइन्स\', \'द ग्लास पैलेस\', \'रिवर ऑफ स्मोक\', \'गन आइलैंड\', \'द कलकत्ता क्रोमोसोम\', \'फ्लड ऑफ फायर\' , \'द सर्कल ऑफ़ रीज़न\', \'जंगल नामा\', \'काउंटडाउन\', \'द लिविंग माउंटेन\'। <strong>अरुंधति रॉय</strong>:- \'गॉड ऑफ स्मॉल थिंग्स\', \'द मिनिस्ट्री ऑफ अटमोस्ट हैप्पीनेस\', \'आजादी\', \'द एंड ऑफ इमेजिनेशन\', \'कम सितंबर\', \'द कॉस्ट ऑफ लिविंग\', \'वॉकिंग विद द कॉमरेड्स\', \'द अलजेब्रा ऑफ़ इनफिनिट जस्टिस\', \'कैपिटलिज्म: अ घोस्ट स्टोरी\', \'पावर पॉलिटिक्स&rsquo;\' आदि।</p>",
                    correct: "a",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "129",
                    section: "misc",
                    question_en: "<p>69. &lsquo;Ponung&rsquo; and &lsquo;Tapu&rsquo; are popular dance forms performed by _____ tribe of Arunachal Pradesh.</p>",
                    question_hi: "<p>69. अरुणाचल प्रदेश की _____ जनजाति के \'पोनुंग\' और \'टापू\' लोकप्रिय नृत्य रूप हैं।</p>",
                    options_en: ["<p>Santhal</p>", "<p>Apatani</p>", 
                                "<p>Adi</p>", "<p>Karbis</p>"],
                    options_hi: ["<p>संथाल</p>", "<p>अपातानी</p>",
                                "<p>आदि</p>", "<p>कार्बिस</p>"],
                    solution_en: "<p>69.(c) <strong>Adi Tribe</strong>. <strong>Santhal </strong>(Bihar, Jharkhand, West Bengal, and Orissa). <strong>Apatani </strong>(Arunachal Pradesh), <strong>Karbis </strong>(Assam). Some popular folk <strong>dances </strong>in Arunachal Pradesh are Aji Lamu, Chalo, Hiirii Khaniing, Popir, Ponung, Pasi Kongki, Rekham Pada, Roppi, Lion and Peacock dance.</p>",
                    solution_hi: "<p>69.(c) <strong>आदि जनजाति</strong>। <strong>संथाल </strong>(बिहार, झारखंड, पश्चिम बंगाल और उड़ीसा)। <strong>अपातानी </strong>(अरुणाचल प्रदेश), <strong>करबीस </strong>(असम)। अरुणाचल प्रदेश में कुछ लोकप्रिय लोक <strong>नृत्य </strong>हैं अजी लामू, चलो, हिरी खानिंग, पोपिर, पोनुंग, पासी कोंगकी, रेखम पाडा, रोप्पी, शेर और मोर नृत्य।</p>",
                    correct: "c",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "130",
                    section: "misc",
                    question_en: "<p>70. Cheriyal Scroll Painting style is a modified version of Nakashi art that originated in ______.</p>",
                    question_hi: "<p>70. चेरियल स्क्रॉल पेंटिंग शैली नकाशी कला का एक संशोधित संस्करण है जिसकी उत्पत्ति ______ में हुई थी।</p>",
                    options_en: ["<p>Tamil Nadu</p>", "<p>Karnataka</p>", 
                                "<p>Telangana</p>", "<p>Kerala</p>"],
                    options_hi: ["<p>तमिलनाडु</p>", "<p>कर्नाटक</p>",
                                "<p>तेलंगाना</p>", "<p>केरल</p>"],
                    solution_en: "<p>70.(c) Folk Art and Paintings :-<strong>Telangana </strong>(Cheriyal Scroll Art, Nirmal Arts, Deccani Paintings, Kalamkari). <strong>Tamil Nadu</strong> (Tanjore Painting, Mica, Mural Paintings), <strong>Karnataka </strong>(Mysore Paintings, Chittara, Ganjifa Art, Samavasaran), <strong>Kerala </strong>(Kalamezhuthu Pattu, Kerala mural, Kathakali Body painting, Theyyam).</p>",
                    solution_hi: "<p>70.(c) लोक कला और पेंटिंग: <strong>तेलंगाना </strong>(चेरियल स्क्रॉल आर्ट, निर्मल कला, डेक्कनी पेंटिंग, कलमकारी)। <strong>तमिलनाडु </strong>(तंजौर पेंटिंग, अभ्रक, भित्ति चित्र), <strong>कर्नाटक </strong>(मैसूर पेंटिंग, चित्तारा, गंजीफा कला, समवसरण), <strong>केरल </strong>(कलमेझुथु पट्टू, केरल म्यूरल, कथकली बॉडी पेंटिंग, तेय्यम)।</p>",
                    correct: "c",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "131",
                    section: "misc",
                    question_en: "<p>1. When a document of MS-Word 2010 is already open, which keyboard shortcut is used to open a new document ?</p>",
                    question_hi: "<p>1. जब MS-Word 2010 का कोई डॉक्यूमेंट पहले से खुला हो, तो नया डॉक्यूमेंट खोलने के लिए किस कीबोर्ड शॉर्टकट का उपयोग किया जाता है ?</p>",
                    options_en: ["<p>Ctrl + X</p>", "<p>Ctrl + Y</p>", 
                                "<p>Ctrl + N</p>", "<p>Ctrl + V</p>"],
                    options_hi: ["<p>Ctrl + X</p>", "<p>Ctrl + Y</p>",
                                "<p>Ctrl + N</p>", "<p>Ctrl + V</p>"],
                    solution_en: "<p>1.(c) <strong>Ctrl + N </strong>&rarr; It is used to create a new document.<strong> Ctrl + X</strong>&rarr; It is used to cut selected text and save it to the clipboard ready to paste elsewhere. <strong>Ctrl + Y</strong>&rarr; It is used to to reverse your last undo. <strong>Ctrl + V</strong>&rarr;It is used to paste the contents of the clipboard into the current cursor location.</p>",
                    solution_hi: "<p>1.(c) <strong>Ctrl + N</strong>&rarr; इसका उपयोग एक नया डॉक्यूमेंट (document) बनाने के लिए किया जाता है। <strong>Ctrl + X</strong>&rarr; इसका उपयोग सिलेक्टेड टेक्स्ट को कट (cut) करने और कहीं और पेस्ट (paste) के लिए तैयार क्लिपबोर्ड (clipboard) पर सेव करने के लिए किया जाता है। <strong>Ctrl + Y</strong>&rarr; इसका उपयोग आपके पिछले अनडू (undo) को रिवर्स करने के लिए किया जाता है।<strong> Ctrl + V</strong>&rarr; इसका उपयोग क्लिपबोर्ड के कंटेंट (clipboard content) को वर्तमान कर्सर लोकेशन (current cursor location) में पेस्ट के लिए किया जाता है।</p>",
                    correct: "c",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "132",
                    section: "misc",
                    question_en: "2. GPRS stands for:",
                    question_hi: "2. GPRS का पूर्ण रूप है:",
                    options_en: [" Guiding Packet Radio Systems", " General Popular Radio Systems", 
                                " General Packet Radio Services", " General Payment Radio Services"],
                    options_hi: [" Guiding Packet Radio Systems", " General Popular Radio Systems",
                                " General Packet Radio Services", " General Payment Radio Services"],
                    solution_en: "2.(c) GPRS stands for: General Packet Radio Services that promises data rates from 56 up to 114 Kbps and continuous connection to the Internet for mobile phone and computer users.",
                    solution_hi: "2.(c) GPRS का अर्थ है: General Packet Radio Services जो 56 से 114 केबीपीएस (kbps) तक डेटा दरों और मोबाइल फोन और कंप्यूटर उपयोगकर्ताओं के लिए इंटरनेट से निरंतर कनेक्शन का वादा करती हैं।",
                    correct: "c",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "133",
                    section: "misc",
                    question_en: "<p>3. ______ shortcut key is used in MS-Excel 2010 to go to the view tab.</p>",
                    question_hi: "<p>3. व्यू टैब (view tab) पर जाने के लिए, (MS-Excel 2010) में ______शॉर्टकट (shortcut) की (key) का उपयोग किया जाता है।</p>",
                    options_en: ["<p>ALT + W</p>", "<p>ALT + A</p>", 
                                "<p>ALT + N</p>", "<p>ALT + I</p>"],
                    options_hi: ["<p>ALT + W</p>", "<p>ALT + A</p>",
                                "<p>ALT + N</p>", "<p>ALT + I</p>"],
                    solution_en: "<p>3.(a) <strong>ALT + W</strong>&rarr; It opens the view tab in the Ribbon. <strong>ALT + A</strong>&rarr; Alt + A is a keyboard shortcut often used to open the Data tab in Excel. <strong>ALT + N</strong>&rarr; It is used to insert a tab. <strong>Alt + </strong>I<strong>&rarr; </strong>is a keyboard shortcut often used to open the Insert file menu.</p>",
                    solution_hi: "<p>3.(a) <strong>ALT + W</strong>&rarr; यह रिबन (Ribbon) में व्यू टैब ( view tab) खोलता है। <strong>ALT + A</strong>&rarr; Alt + A एक कीबोर्ड (keyboard) शॉर्टकट (shortcut) है जिसका उपयोग अक्सर एक्सेल (Excel) में डेटा टैब (Data tab) खोलने (open) के लिए किया जाता है। <strong>ALT + N</strong>&rarr; इसका उपयोग टैब (tab) को इन्सर्ट (insert) करने के लिए किया जाता है। <strong>Alt + I</strong> <strong>&rarr; </strong>एक कीबोर्ड शॉर्टकट है जिसका उपयोग अक्सर फ़ाइल सम्मिलित करें मेनू खोलने के लिए किया जाता है।</p>",
                    correct: "a",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "134",
                    section: "misc",
                    question_en: "<p>4. In MS-Word 2010, which shortcut key is used to delete a character to the right side ?</p>",
                    question_hi: "<p>4. MS-Word 2010 में दायी (right) ओर के कॅरक्टर को डिलीट करने के लिए किस शॉर्टकट की (key) का उपयोग किया जाता है ?</p>",
                    options_en: ["<p>Control + Alt + Delete</p>", "<p>Delete</p>", 
                                "<p>Ctrl + Backspace</p>", "<p>Alt + Backspace</p>"],
                    options_hi: ["<p>Control + Alt + Delete</p>", "<p>Delete</p>",
                                "<p>Ctrl + Backspace</p>", "<p>Alt + Backspace</p>"],
                    solution_en: "<p>4.(b) Pressing <strong>Delete </strong>removes characters to the right of the cursor. <strong>Ctrl + Backspace</strong>&rarr; It deletes the word to the left of the cursor.<strong> Alt + Backspace</strong>&rarr; Alt + Backspace is generally mapped to the \"Undo\" command. <strong>Control + Alt + Delete</strong> is the combination of the Ctrl key, the Alt key and Del key that a user can press at the same time to terminate an application task or to reboot the operating system.</p>",
                    solution_hi: "<p>4.(b) <strong>Delete </strong>दबाने से कर्सर के दाईं (right) ओर के अक्षर डिलीट हो जाते हैं। <strong>Ctrl + Backspace</strong>&rarr; यह कर्सर के बाईं (left) ओर के शब्द को डिलीट कर देता है। <strong>Alt + Backspace</strong>&rarr; Alt + Backspace को आम तौर पर \"अनडू \" कमांड (undo command) के लिए किया जाता है। <strong>Control + Alt + Delete,</strong> Ctrl कुंजी, ऑल्ट कुंजी और डेल कुंजी का संयोजन है जिसे एक उपयोगकर्ता एक ही समय में किसी एप्लिकेशन कार्य को समाप्त करने या ऑपरेटिंग सिस्टम को रिबूट करने के लिए दबा सकता है।</p>",
                    correct: "b",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "135",
                    section: "misc",
                    question_en: "<p>5. Which of the following keyboard shortcuts is used to center align the selected text in MS-Word 2010 ?</p>",
                    question_hi: "<p>5. एमएस-वर्ड 2010 (MS-Word 2010) मे सेलेक्ट किये हुए टेक्स्ट को सेंटर अलाइन (center align) करने के लिए निम्न में से किस कीबोर्ड शॉर्टकट का उपयोग किया जाता है ?</p>",
                    options_en: ["<p>Ctrl + W</p>", "<p>Ctrl + C</p>", 
                                "<p>Ctrl + A</p>", "<p>Ctrl + E</p>"],
                    options_hi: ["<p>Ctrl + W</p>", "<p>Ctrl + C</p>",
                                "<p>Ctrl + A</p>", "<p>Ctrl + E</p>"],
                    solution_en: "<p>5.(d) <strong>Ctrl + E</strong>&rarr; It is used to center align the text. <strong>Ctrl + W</strong>&rarr; It is used to close the document. <strong>Ctrl + C</strong>&rarr; It is used to copy the selected content to the clipboard. <strong>Ctrl + A</strong>&rarr; It is used to select all document content.</p>",
                    solution_hi: "<p>5.(d) <strong>Ctrl + E</strong>&rarr; इसका उपयोग टेक्स्ट को सेंटर अलाइन (center align) करने के लिए किया जाता है। <strong>Ctrl + W</strong>&rarr; इसका उपयोग डॉक्यूमेंट को बंद (document close) करने के लिए किया जाता है। <strong>Ctrl + C</strong>&rarr; इसका उपयोग सेलेक्ट कंटेंट (selected content) को क्लिपबोर्ड (clipboard) पर कॉपी (copy) करने के लिए किया जाता है। <strong>Ctrl + A</strong>&rarr; इसका उपयोग सभी डॉक्यूमेंट कंटेंट (all documents contents) को सेलेक्ट (select) करने के लिए किया जाता है।</p>",
                    correct: "d",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "136",
                    section: "misc",
                    question_en: "<p>6. Choose the wrong option with respect to MS-Excel 2010.</p>",
                    question_hi: "<p>6. MS-Excel 2010 के संबंध में गलत विकल्प का चयन कीजिये।</p>",
                    options_en: ["<p>In MS-Excel 2010, Ctrl + R is used in replacing text within the sheet.</p>", "<p>In MS-Excel 2010, Ctrl + H is used in replacing text within the sheet.</p>", 
                                "<p>In MS-Excel 2010, home tab in ribbon bar contains the most frequently used commands such as copying and pasting, sorting and filtering, formatting, etc</p>", "<p>Alt + Enter shortcut key is used in MS-Excel 2010 to enter a new line in the text of a particular cell.</p>"],
                    options_hi: ["<p>MS-Excel 2010 में, Ctrl + R का उपयोग शीट में टेक्स्ट (text) को प्रतिस्थापित (Replace) करने के लिए किया जाता है।</p>", "<p>MS-Excel 2010 में, Ctrl + H का उपयोग शीट (sheet) के भीतर टेक्स्ट (text) को प्रतिस्थापित (Replace) करने के लिए किया जाता है।</p>",
                                "<p>MS-Excel 2010 में, रिबन बार(ribbon bar) में होम टैब (Home tab) में सबसे अधिक उपयोग किये जाने वाले कमांड (command) होते है जैसे की कॉपी और पेस्ट करना (copy or paste), सॉर्ट (sort) करना और फ़िल्टर (filter) करना, फॉर्मेट (format) करना आदि।</p>", "<p>Alt + Enter शॉर्टकट (shortcut) कीज़ (key) का उपयोग MS-Excel 2010 में किसी विशेष सेल वाले टेक्स्ट (text) में एक नई लाइन प्रविष्ट (enter) करने के लिए किया जाता है।</p>"],
                    solution_en: "<p>6.(a) <strong>Ctrl + R </strong>&rarr; It is used for renaming the table.</p>",
                    solution_hi: "<p>6.(a) <strong>Ctrl + R </strong>&rarr; इसका उपयोग टेबल (table) का नाम बदलने के लिए किया जाता है।</p>",
                    correct: "a",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "137",
                    section: "misc",
                    question_en: "<p>7. Which of the following keyboard shortcut is used to open the Downloads window in Chrome browser ?</p>",
                    question_hi: "<p>7. क्रोम ब्राउज़र में डाउनलोड विंडो (download window) खोलने (open) के लिए निम्न में से किस कीबोर्ड शॉर्टकट (keyboard shortcut ) का उपयोग किया जाता है ?</p>",
                    options_en: ["<p>Alt + D</p>", "<p>Alt + J</p>", 
                                "<p>Ctrl + J</p>", "<p>Ctrl + D</p>"],
                    options_hi: ["<p>Alt + D</p>", "<p>Alt + J</p>",
                                "<p>Ctrl + J</p>", "<p>Ctrl + D</p>"],
                    solution_en: "<p>7.(c) <strong>Ctrl + J</strong>&rarr; Open the Downloads page in a new tab. <strong>Alt + D</strong>&rarr; It selects the URL in the address bar in the browser. <strong>Alt + J</strong>&rarr; Alt + J is a keyboard shortcut most often used to adjust the justification of text in a file. <strong>Ctrl + D</strong>&rarr; It is used to save your current webpage as a bookmark.</p>",
                    solution_hi: "<p>7.(c) <strong>Ctrl + J</strong>&rarr; इसका उपयोग डाउनलोड पेज (download page) को एक नए टैब (tab) में खोलने (open) के लिए किया जाता है।<strong> Alt + D</strong>&rarr; यह ब्राउज़र (browser) में एड्रेस बार (address bar) में URL को सेलेक्ट (select) करता है। <strong>Alt + J</strong>&rarr; Alt + J एक कीबोर्ड शॉर्टकट (keyboard shortcut) है जिसका उपयोग किसी फ़ाइल (file) में टेक्स्ट के जस्टिफिकेशन (justification) को एडजस्ट (adjust) करने के लिए किया जाता है। <strong>Ctrl + D</strong>&rarr; इसका उपयोग आपके वर्तमान वेबपेज (webpage) को बुकमार्क (bookmark) के रूप में सेव (save) करने के लिए किया जाता है।</p>",
                    correct: "c",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "138",
                    section: "misc",
                    question_en: "8. Which of the following options is correct about enabling edit mode in worksheets of MS Excel-2010 and above ?",
                    question_hi: "8. MS-Excel-2010 और उससे ऊपर की  वर्कशीट (worksheet) में एडिट मोड (edit mode) को इनेबल (enable) करने के संदर्भ में निम्नलिखित में से <br />कौनसा विकल्प सही है ",
                    options_en: [" Click File > Options > Trust Center and select the Allow editing directly in cells checkbox", " Click File > Options > Customized Ribbon and select the Allow editing directly in cells checkbox", 
                                " Click File > Options > Advanced and under Editing options select the Allow editing directly in cells checkbox", " Click File > Options > Customized Ribbon and under Editing options select the Allow editing directly in cells checkbox"],
                    options_hi: [" File पर क्लिक करे > Options पर क्लिक (click) करे > Trust Center पर क्लिक करे और Allow editing directly in cells चेक बॉक्स (check box) को सेलेक्ट करे। ", " File पर क्लिक करे > Options पर क्लिक करे > Customized Ribbon पर क्लिक करे और Allow editing directly in cells चेक बॉक्स (check box) को सेलेक्ट करे। ",
                                " File पर क्लिक करे > Options पर क्लिक करे > Advanced पर क्लिक करे और Editing options के अंतर्गत Allow editing directly in cells चेक बॉक्स (check box) को सेलेक्ट करे।", " File पर क्लिक करे > Options पर क्लिक करे > Customized Ribbon पर क्लिक करे और Editing options के अंतर्गत Allow editing directly in cells चेक बॉक्स (check box) को सेलेक्ट करे। "],
                    solution_en: "8.(c) Click File > Options > Advanced. , click Excel Options, and then click the Advanced category. Under Editing options, do one of the following: To enable Edit mode, select the Allow editing directly in the cell\'s checkbox.",
                    solution_hi: "8.(c) क्लिक फ़ाइल > ऑप्शन > एडवांस्ड (Click File > Options > Advanced). पर क्लिक करें। , एक्सेल ऑप्शन पर क्लिक करें और फिर एडवांस्ड केटेगरी (advanced category) पर क्लिक करें। एडिटिंग ऑप्शन मोड (Editing option mode) के अंतर्गत, निम्न में से कोई एक कार्य करें: एडिट मोड (edit mode) को सक्षम (enable) करने के लिए, सीधे सेल के चेकबॉक्स (checkbox) में एलाओ एडिटिंग (Allow editing) का चयन करें।",
                    correct: "c",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "139",
                    section: "misc",
                    question_en: "<p>9. Which of the following is NOT a valid MS-Excel 2010 Math function?</p>",
                    question_hi: "<p>9. निम्नलिखित में से कौन सा एक वैध (valid) एमएस-एक्सेल 2010 (MS -Excel 2010) मैथ फ़ंक्शन (Math function) नहीं है?</p>",
                    options_en: ["<p>BASE()</p>", "<p>OFF()</p>", 
                                "<p>MOD()</p>", "<p>ODD()</p>"],
                    options_hi: ["<p>BASE()</p>", "<p>OFF()</p>",
                                "<p>MOD()</p>", "<p>ODD()</p>"],
                    solution_en: "<p>9.(b) Valid mathematical functions in MS Excel are BASE(), MOD(), ODD(). <strong>BASE</strong>()&rarr;The Base function is available under Excel Mathematical and Trigonometric functions. The function returns a text representation of the calculated value and will convert a number into the supplied base (radix). <strong>MOD</strong>()&rarr; It returns the remainder after the number is divided by the divisor. <strong>ODD</strong>()&rarr; It returns a number rounded up to the nearest odd integer.</p>",
                    solution_hi: "<p>9.(b) MS Excel में मान्य गणितीय फंक्शन (mathematical functions) <strong>BASE</strong>(), MOD(), ODD() हैं। BASE () &rarr; बेस फंक्शन एक्सेल मैथमैटिकल और ट्रिग्नोमेट्रिक फंक्शंस (excel mathematical and trigonometric functions) के तहत उपलब्ध है। फ़ंक्शन कॅल्क्युलेटेड मान (calculated value) का एक टेक्स्ट प्रतिनिधित्व (representation) देता है और एक संख्या को सपलाइड बेस (supplied base) (रेडिक्स; radix ) में बदल देगा। <strong>MOD</strong>()&rarr; यह संख्या (number) को भाजक (divisor) से विभाजित करने के बाद शेषफल (remainder) देता है। <strong>ODD</strong>()&rarr; यह एक संख्या को निकटतम विषम पूर्णांक (nearest odd integer) तक पूर्णांकित करता है।</p>",
                    correct: "b",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "140",
                    section: "misc",
                    question_en: "<p>10. Under which of the following menu tabs are Page Setup options usually available in MS-Word 2007?</p>",
                    question_hi: "<p>10. निम्न MS-वर्ड 2007 में से किस मेनू टैब (menu tab) के अंतर्गत पेज सेटअप विकल्प (page setup options) आमतौर पर उपलब्ध होते हैं?</p>",
                    options_en: ["<p>Review</p>", "<p>Page Layout</p>", 
                                "<p>Home</p>", "<p>Insert</p>"],
                    options_hi: ["<p>रिव्यु (Review)</p>", "<p>पेज लेआउट (Page layout)</p>",
                                "<p>होम (Home)</p>", "<p>इन्सर्ट (insert)</p>"],
                    solution_en: "<p>10.(b) Page Setup options usually available in <strong>Page layout</strong>. Options under page layout- You can set margins, apply themes, control of page orientation and size, add sections and line breaks, display line numbers, and set paragraph indentation and lines.</p>",
                    solution_hi: "<p>10.(b) पेज सेटअप विकल्प आमतौर पर पेज लेआउट में उपलब्ध होते हैं। पेज लेआउट के तहत विकल्प- आप मार्जिन सेट (margin set) कर सकते हैं, थीम (theme) अप्लाई कर सकते हैं, पेज ओरिएंटेशन (page orientation) और आकार पर नियंत्रण कर सकते हैं, सेक्शन (section) और लाइन ब्रेक (line break) जोड़ सकते हैं, लाइन नंबर (line number) डिस्प्ले (display) कर सकते हैं और पैराग्राफ इंडेंटेशन (paragraph indentation) और लाइन सेट (line set) कर सकते हैं।</p>",
                    correct: "b",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "141",
                    section: "misc",
                    question_en: "<p>11. What is the address of the cell at the intersection of fifth row and fifth column in MS-Excel 2010 ?</p>",
                    question_hi: "<p>11. MS Excel 2010 में पांचवी रो (row) और पांचवे कॉलम (column) के इंटरसेक्शन (intersection) पर सेल का एड्रेस (address) क्या है ?</p>",
                    options_en: ["<p>$E5$</p>", "<p>$E$5</p>", 
                                "<p>5E</p>", "<p>E5</p>"],
                    options_hi: ["<p>$E5$</p>", "<p>$E$5</p>",
                                "<p>5E</p>", "<p>E5</p>"],
                    solution_en: "<p>11.(d) A <strong>cell address</strong> is a combination of a column letter and a row number that identifies a cell on a worksheet. For example, A1 refers to the cell at the intersection of column A and row 1; B2 refers to the second cell in column B, and so on.</p>",
                    solution_hi: "<p>11.(d) एक <strong>सेल एड्रेस </strong>(cell address) एक कॉलम अक्षर और एक रो संख्या का संयोजन (combination) होता है जो वर्कशीट (worksheet) पर एक सेल की पहचान करता है। उदाहरण के लिए, A1 कॉलम (column) A और रो (row)1 के इंटरसेक्शन (intersection) पर सेल को संदर्भित करता है; B2 कॉलम B में दूसरे सेल को संदर्भित करता है ।</p>",
                    correct: "d",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "142",
                    section: "misc",
                    question_en: "12 In which of the following tabs, the formatting tasks such as bolding, italicizing, changing the font and size of text found in MS-Word 2007?",
                    question_hi: "12. MS-वर्ड  2007 में निम्न में से किस टैब (tab) में बोल्डिंग(bolding), इटैलिकाइज़िंग (italicizing), टेक्स्ट के फ़ॉन्ट और आकार (font and size) को बदलने जैसे फॉर्मेटिंग फंक्शन्स (formatting functions)  पाए जाते हैं?",
                    options_en: [" Page Layout", " References", 
                                " Home", " Insert"],
                    options_hi: [" पेज लेआउट (Page Layout)", " रेफरेन्सेस (References)",
                                " होम (Home)", " इन्सर्ट (Insert)"],
                    solution_en: "12.(c) The formatting tasks such as bolding, italicizing, changing the font and size of text found in Home Tabs. The Page Layout Tab arranges our document pages just the way we want them. Reference Tab allows us to create a table of contents, footnotes, cross-references etc. Insert Tab used to  insert images, shapes etc. ",
                    solution_hi: "12.(c) होम (Home) टैब्स में पाए जाने वाले फ़ॉर्मेटिंग फंक्शन्स जैसे बोल्डिंग (bolding), इटैलिकाइज़िंग (italicizing), टेक्स्ट का फ़ॉन्ट और आकार बदलना। पेज लेआउट टैब हमारे डॉक्यूमेंट पेजों (document pages) को वैसे ही व्यवस्थित करता है जैसे हम उन्हें चाहते हैं। रेफेरेंस टैब हमें कंटेंट्स (contents), फुटनोट (footnote), क्रॉस-रेफरेंस (cross reference) इत्यादि की एक टेबल (table)बनाने की अनुमति देता है। इंसर्ट टैब (tab) का उपयोग चित्र, आकार ( images, shapes) आदि इन्सर्ट करने (insert) के लिए किया जाता है।",
                    correct: "c",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "143",
                    section: "misc",
                    question_en: "<p>13. In MS-Word 2010, ______ cells means that the adjacent cells are thereafter treated as a single cell, even though they did not start as a single cell.</p>",
                    question_hi: "<p>13. एमएस-वर्ड 2010 में, ______ सेलों का अर्थ है कि समीपवर्ती सेलों को उसके बाद एकल सेल के रूप में माना जाता है, भले ही वे एकल सेल के रूप में शुरू नहीं हुई हों।</p>",
                    options_en: ["<p>Merging</p>", "<p>Formatting</p>", 
                                "<p>Editing</p>", "<p>Splitting</p>"],
                    options_hi: ["<p>मर्जिंग</p>", "<p>फोर्मेटिंग</p>",
                                "<p>एडिटिंग</p>", "<p>स्प्लिटिंग</p>"],
                    solution_en: "<p>13.(a) <strong>Merging cells</strong>&rarr;We can combine two or more table cells located in the same row or column into a single cell. <strong>Cells Formatting</strong>&rarr;The Format Cells dialog contains tabs Number, Alignment, Font, Border, Background, Protection, and Validation.</p>",
                    solution_hi: "<p>13.(a) <strong>मर्जिंग सेल </strong>&rarr; हम एक ही पंक्ति या कॉलम में स्थित दो या दो से अधिक टेबल सेल को एक सेल में जोड़ सकते हैं। <strong>सेल फॉर्मेटिंग </strong>&rarr; फॉर्मेट सेल डायलॉग में टैब नंबर, एलाइनमेंट, फॉन्ट, बॉर्डर, बैकग्राउंड, प्रोटेक्शन और वैलिडेशन शामिल हैं।</p>",
                    correct: "a",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "144",
                    section: "misc",
                    question_en: "14. Which of the following is a Computer utility programme?",
                    question_hi: "14. निम्नलिखित में से कौन एक कंप्यूटर उपयोगिता कार्यक्रम है? ",
                    options_en: [" Inventory control system", " Shareware", 
                                " Package software", " Disk defragmenter"],
                    options_hi: [" सूची नियंत्रण प्रणाली (Inventory control system) ", " शेयरवेयर (Shareware)",
                                " पैकेज सॉफ्टवेयर (Package software)        ", " डिस्क डीफ़्रेग्मेंटर (Disk defragmenter)"],
                    solution_en: "14.(d) Computer utility programs are Antivirus, File Management System, Disk Management tools, Compression tools, Disk cleanup tool, File Management System, Disk Defragmenter, Backup utility.",
                    solution_hi: "14.(d) कंप्यूटर यूटिलिटी  प्रोग्राम्स एंटीवायरस, फ़ाइल मैनेजमेंट  प्रणाली ( File Management System),  डिस्क मैनेजमेंट  उपकरण (Disk Management tools), कम्प्रेशन टूल्स (Compression tools) , डिस्क क्लीनअप टूल्स (Disk cleanup tool), फ़ाइल मैनेजमेंट प्रणाली (File Management System,), डिस्क डीफ़्रेग्मेंटर (defragmenter),  बैकअप यूटिलिटी (Backup utility) हैं।",
                    correct: "d",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "145",
                    section: "misc",
                    question_en: "<p>15. Which of the following best describes - &lsquo;A method of storing and transmitting data in a particular coded form so that only those can read and process it for whom it is intended. It includes encoding and decoding of data?</p>",
                    question_hi: "<p>15. निम्नलिखित में से कौन सबसे अच्छा वर्णन करता है - \'डेटा (Data) को एक विशेष कोडित रूप में संग्रहीत और प्रसारित (storing and transmitting )करने की एक विधि ताकि केवल वे ही इसे पढ़ और संसाधित कर सकें जिनके लिए यह अभिप्रेत है। इसमें डेटा (Data) की एन्कोडिंग (encoding ) और डिकोडिंग (decoding) शामिल है?</p>",
                    options_en: ["<p>Blockchain</p>", "<p>Programming</p>", 
                                "<p>Cryptography</p>", "<p>Cloud computing</p>"],
                    options_hi: ["<p>ब्लॉक चेन(block chain)</p>", "<p>प्रोग्रामिंग (programming)</p>",
                                "<p>क्रिप्टोग्राफी (Cryptography)</p>", "<p>क्लाउड कंप्यूटिंग(Cloud computing)</p>"],
                    solution_en: "<p>15.(c) <strong>Cryptography </strong>is the study of secure communications techniques that allow only the sender and intended recipient of a message to view its contents. <strong>Blockchain </strong>is a system of recording information in a way that makes it difficult or impossible to change, hack, or cheat the system. <strong>Cloud computing</strong> is a general term for anything that involves delivering hosted services over the internet.</p>",
                    solution_hi: "<p>15.(c) <strong>क्रिप्टोग्राफी </strong>(Cryptography) सुरक्षित संचार तकनीकों का अध्ययन है जो केवल प्रेषक और संदेश के इच्छित प्राप्तकर्ता को इसकी सामग्री देखने की अनुमति देता है।<strong>ब्लॉकचैन </strong>(Block Chain) एक तरह से जानकारी रिकॉर्ड करने की एक प्रणाली है जिससे सिस्टम को बदलना, हैक करना या धोखा देना मुश्किल या असंभव हो जाता है। <strong>क्लाउड कंप्यूटिंग </strong>(Cloud Computing) किसी भी चीज़ के लिए एक सामान्य शब्द है जिसमें इंटरनेट पर होस्ट की गई सेवाएं प्रदान करना शामिल है।</p>",
                    correct: "c",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "146",
                    section: "misc",
                    question_en: "<p>16. ______ is a type of computing that performs computation, storage and even applications as a service across a network.</p>",
                    question_hi: "<p>16. कंप्यूटिंग (computing) का कौन-सा प्रकार कम्प्यूटेशन, स्टोरेज (computation, storage) और यहां तक ​​कि एप्लीकेशन (application) को भी पुरे नेटवर्क में सर्विस के रूप में करता है।</p>",
                    options_en: ["<p>Cloud computing</p>", "<p>Distributed computing</p>", 
                                "<p>Parallel computing</p>", "<p>Virtual computing</p>"],
                    options_hi: ["<p>क्लाउड कम्प्यूटिंग (cloud computing)</p>", "<p>डिस्ट्रिब्यूटेड कंप्यूटिंग (distributed computing)</p>",
                                "<p>पैरेलल कंप्यूटिंग (parallel computing)</p>", "<p>वर्चुअल कंप्यूटिंग (virtual computing)</p>"],
                    solution_en: "<p>16.(b) <strong>Cloud networking </strong>focuses on the ability of a cloud customer or cloud service provider to design, configure and manage the underlying network in a cloud service.</p>",
                    solution_hi: "<p>16.(b) <strong>क्लाउड नेटवर्किंग </strong>(cloud networking) क्लाउड सेवा में अंतर्निहित नेटवर्क को डिज़ाइन, कॉन्फ़िगर और प्रबंधित करने के लिए क्लाउड ग्राहक या क्लाउड सेवा प्रदाता ( cloud service provider)की क्षमता पर केंद्रित है।</p>",
                    correct: "b",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "147",
                    section: "misc",
                    question_en: "17. The trial test of a computer or software before the commercial launch is called:",
                    question_hi: "17. कमर्शियल लॉन्च (commercial launch) से पहले कंप्यूटर या सॉफ्टवेयर के परीक्षण को_________ कहा जाता है।",
                    options_en: [" Beta test ", " Alpha test", 
                                " Delta test ", " e-test  "],
                    options_hi: [" बीटा परीक्षण  (Beta test )", " अल्फा परीक्षण (Alpha test)",
                                " डेल्टा परीक्षण (Delta test)", " e-परीक्षण (e-test  )"],
                    solution_en: "17.(a) The trial test of a computer or software before the commercial launch is called Beta test. Alpha Testing is a type of software testing performed to identify bugs before releasing the product to real users or to the public. Delta testing is usually done when we quickly want to ship the product and there are less risks associated with the change/addition. An e-test is any test that replicates or replaces paper based tests with a computer screen, also referred to as on-screen tests. ",
                    solution_hi: "17.(a) कमर्शियल लॉन्च से पहले किसी कंप्यूटर या सॉफ्टवेयर के ट्रायल टेस्ट को बीटा (Beta) टेस्ट कहा जाता है। अल्फा (Alpha) टेस्टिंग एक प्रकार का सॉफ्टवेयर परीक्षण है जो वास्तविक उपयोगकर्ताओं या जनता को उत्पाद जारी करने से पहले त्रुटि(error) की पहचान करने के लिए किया जाता है। डेल्टा (Delta) परीक्षण आमतौर पर तब किया जाता है जब हम उत्पाद को जल्दी से शिप (ship) करना चाहते हैं और परिवर्तन/जोड़ (change/addition) से जुड़े रिस्क (risk) कम होते हैं। एक ई-टेस्ट (e-test) कोई भी परीक्षण है जो कंप्यूटर स्क्रीन के साथ पेपर आधारित परीक्षणों की कॉपी  बनाता है या प्रतिस्थापित (replace) करता है, जिसे ऑन-स्क्रीन (on-screen) परीक्षण भी कहा जाता है।",
                    correct: "a",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "148",
                    section: "misc",
                    question_en: "<p>18. In MS-Excel 2010 ______ is used in replacing text within the sheet.</p>",
                    question_hi: "<p>18. MS-Excel 2010 में ____ का उपयोग शीट के भीतर टेक्स्ट (text) को प्रतिस्थापित (Replace) करने के लिए किया जाता है।</p>",
                    options_en: ["<p>Alt + R</p>", "<p>Ctrl + H</p>", 
                                "<p>Alt + F</p>", "<p>Ctrl + F</p>"],
                    options_hi: ["<p>Alt + R</p>", "<p>Ctrl + H</p>",
                                "<p>Alt + F</p>", "<p>Ctrl + F</p>"],
                    solution_en: "<p>18.(b) <strong>Ctrl + H</strong>&rarr; It displays the Find and Replace dialog box (with Replace selected). <strong>Alt + R</strong>&rarr; It opens the review tab in the Ribbon. <strong>Alt + F</strong>&rarr; It opens the file menu or file tab in the Ribbon. <strong>Ctrl + F</strong>&rarr; It displays the Find and Replace dialog box (with Find selected).</p>",
                    solution_hi: "<p>18.(b) <strong>Ctrl + H</strong>&rarr; यह फाइंड एंड रिप्लेस डायलॉग बॉक्स (Find and Replace dialog box) को दर्शाता है (सिलेक्टेड रिप्लेस (replace) )। <strong>Alt + R</strong>&rarr; यह रिबन (ribbon) में रिव्यु टैब (review tab) खोलता है। <strong>Alt + F</strong>&rarr; यह रिबन (ribbon) में फ़ाइल मेनू या फ़ाइल टैब (file menu or file tab) खोलता है। <strong>Ctrl + F</strong>&rarr; यह फाइंड एंड रिप्लेस डायलॉग बॉक्स (Find and Replace dialog box) को दर्शाताहै (सिलेक्टेड फाइंड (selected find))।</p>",
                    correct: "b",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "149",
                    section: "misc",
                    question_en: "19. Which of the following does NOT appear as a part of the \'Clipboard\' command group under the Home menu in recent versions of MS Word, such as Word 2016?",
                    question_hi: "19. निम्नलिखित में से कौन-सा MS Word के हाल के संस्करणों, जैसे Word 2016 में होम (Home) मेनू के अंतर्गत \'क्लिपबोर्ड\' (clipboard) कमांड समूह  (command group) के भाग के रूप में प्रकट नहीं होता है?",
                    options_en: [" Cut    ", " Show/Hide", 
                                " Format Painter    ", " Paste"],
                    options_hi: [" Cut    ", " Show/Hide",
                                " Format Painter   ", " Paste"],
                    solution_en: "19.(b) Show/Hide does not appear as a part of the \'Clipboard\' command group under the Home menu in recent versions of MS Word, such as Word 2016.",
                    solution_hi: ".19(b) MS वर्ड के हाल के संस्करणों जैसे वर्ड 2016 में होम मेनू (Home menu) के अंतर्गत \'क्लिपबोर्ड\' कमांड समूह के भाग के रूप में  (Show/Hide) प्रकट नहीं होते हैं।",
                    correct: "b",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "150",
                    section: "misc",
                    question_en: "<p>20. Which of the following is not an option under the Home tab in MS Word?</p>",
                    question_hi: "<p>20. MS Word में निम्नलिखित में से कौन सा होम (Home) टैब (tab) के अंतर्गत आने वाला एक विकल्प नहीं है ?</p>",
                    options_en: ["<p>Font</p>", "<p>Table</p>", 
                                "<p>Style</p>", "<p>Paragraph</p>"],
                    options_hi: ["<p>फॉन्ट (font)</p>", "<p>टेबल (table)</p>",
                                "<p>स्टाइल (style)</p>", "<p>पैराग्राफ (paragraph)</p>"],
                    solution_en: "<p>.20.(b) <strong>The Table</strong> button is present on the Insert tab. The <strong>Home tab</strong> is the default tab in Microsoft Word. It has five groups of related commands; Clipboard, Font, Paragraph, Styles, and Editing.</p>",
                    solution_hi: "<p>.20.(b) <strong>टेबल </strong>(<strong>table</strong>) <strong>बटन </strong>इन्सर्ट(insert) टैब पर मौजूद है। <strong>होम टैब </strong>माइक्रोसॉफ्ट वर्ड (microsoft word) में डिफ़ॉल्ट टैब (default tab) है। इसमें संबंधित कमांड (command) के पांच समूह हैं; क्लिपबोर्ड (clipboard) , फ़ॉन्ट (font) , पैराग्राफ (paragraph) , स्टाइल (style) और एडिटिंग (editing) ।</p>",
                    correct: "b",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>