<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">15:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 25</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">25</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 15 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["18"] = {
                name: "General Awareness",
                start: 0,
                end: 23
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="18">General Awareness</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 24,
                end: 24
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "18",
                    question_en: "<p>1. <span style=\"font-family: Roboto;\">Which Chinese traveler visited India during the reign of Harshavardhana?</span></p>\\n",
                    question_hi: "<p>1. <span style=\"font-family: Palanquin;\">&#2361;&#2352;&#2381;&#2359;&#2357;&#2352;&#2381;&#2343;&#2344; &#2325;&#2375; &#2358;&#2366;&#2360;&#2344;&#2325;&#2366;&#2354; &#2350;&#2375;&#2306; &#2325;&#2367;&#2360; &#2330;&#2368;&#2344;&#2368; &#2351;&#2366;&#2340;&#2381;&#2352;&#2368; &#2344;&#2375; &#2349;&#2366;&#2352;&#2340; &#2325;&#2368; &#2351;&#2366;&#2340;&#2381;&#2352;&#2366; &#2325;&#2368; &#2341;&#2368;?</span></p>\\n",
                    options_en: ["<p>Hsuan Tsang</p>\\n", "<p>Etsing</p>\\n", 
                                "<p>Faxian</p>\\n", "<p>Ibn Battuta</p>\\n"],
                    options_hi: ["<p><span style=\"font-family: Palanquin;\">&#2361;&#2381;&#2357;&#2375;&#2344;&#2360;&#2366;&#2306;&#2327;</span></p>\\n", "<p><span style=\"font-family: Palanquin;\">&#2311;&#2340;&#2381;&#2360;&#2367;&#2306;&#2327;</span></p>\\n",
                                "<p><span style=\"font-family: Palanquin;\">&#2347;&#2364;&#2366;&#2361;&#2367;&#2351;&#2366;&#2344;</span></p>\\n", "<p><span style=\"font-family: Palanquin;\">&#2311;&#2348;&#2381;&#2344; &#2348;&#2340;&#2370;&#2340;&#2366;</span></p>\\n"],
                    solution_en: "<p>1.(a)<strong> Hsuan Tsang (Book : &lsquo;Si-yu-ki&rsquo;)</strong>. <span style=\"font-family: Roboto;\">Harshavardhana was the son of Prabhakar Vardhana, the founder of the Pushyabhuti Dynasty or the Vardhana Dynasty.<strong> </strong></span><strong><span style=\"font-family: Roboto;\">Travelers visited India</span></strong><span style=\"font-family: Roboto;\"> : Fa-Hien (Chinese) - During the reign of Vikramaditya or Chandragupta II, Ibn Battuta (Moroccan) - Mohammed Bin Tughlaq, Megasthenes (Greece) - Chandragupta Maurya. I Tsing (Chinese) resided at the Nalanda monastery for ten years devoting himself to the study of Vinya.</span></p>\\n",
                    solution_hi: "<p>1.(a) <strong>&#2361;&#2381;&#2357;&#2375;&#2344;&#2340;&#2381;&#2360;&#2366;&#2306;&#2327; (&#2346;&#2369;&#2360;&#2381;&#2340;&#2325;: &lsquo;&#2360;&#2368;-&#2351;&#2370;-&#2325;&#2368;&rsquo;)</strong><span style=\"font-family: Palanquin;\">&#2404; &#2361;&#2352;&#2381;&#2359;&#2357;&#2352;&#2381;&#2342;&#2381;&#2343;&#2344; &#2346;&#2369;&#2359;&#2381;&#2351;&#2349;&#2370;&#2340;&#2367; &#2352;&#2366;&#2332;&#2357;&#2306;&#2358; &#2351;&#2366; &#2357;&#2352;&#2381;&#2343;&#2344; &#2352;&#2366;&#2332;&#2357;&#2306;&#2358; &#2325;&#2375; &#2360;&#2306;&#2360;&#2381;&#2341;&#2366;&#2346;&#2325; &#2346;&#2381;&#2352;&#2349;&#2366;&#2325;&#2352; &#2357;&#2352;&#2381;&#2343;&#2344; &#2325;&#2375; &#2346;&#2369;&#2340;&#2381;&#2352; &#2341;&#2375;&#2404; </span><strong><span style=\"font-family: Palanquin;\">&#2349;&#2366;&#2352;&#2340; &#2310;&#2319; &#2351;&#2366;&#2340;&#2381;&#2352;&#2368; </span></strong><span style=\"font-family: Palanquin;\"><strong>:</strong> &#2347;&#2366;&#2361;&#2381;&#2351;&#2366;&#2344; (&#2330;&#2368;&#2344;&#2368;) - &#2357;&#2367;&#2325;&#2381;&#2352;&#2350;&#2366;&#2342;&#2367;&#2340;&#2381;&#2351; &#2351;&#2366; &#2330;&#2306;&#2342;&#2381;&#2352;&#2327;&#2369;&#2346;&#2381;&#2340; &#2342;&#2381;&#2357;&#2367;&#2340;&#2368;&#2351; &#2325;&#2375; &#2358;&#2366;&#2360;&#2344;&#2325;&#2366;&#2354; &#2325;&#2375; &#2342;&#2380;&#2352;&#2366;&#2344;, &#2311;&#2348;&#2381;&#2344;&#2348;&#2340;&#2370;&#2340;&#2366; (&#2350;&#2379;&#2352;&#2325;&#2381;&#2325;&#2379;) - &#2350;&#2379;&#2361;&#2350;&#2381;&#2350;&#2342; &#2348;&#2367;&#2344; &#2340;&#2369;&#2327;&#2354;&#2325;, &#2350;&#2375;&#2327;&#2360;&#2381;&#2341;&#2344;&#2368;&#2332; (&#2327;&#2381;&#2352;&#2368;&#2360;) - &#2330;&#2306;&#2342;&#2381;&#2352;&#2327;&#2369;&#2346;&#2381;&#2340; &#2350;&#2380;&#2352;&#2381;&#2351;&#2404; &#2311;&#2340;&#2381;&#2360;&#2367;&#2306;&#2327; (&#2330;&#2368;&#2344;&#2368;) &#2344;&#2375; &#2357;&#2367;&#2344;&#2381;&#2351;&#2366; &#2325;&#2375; &#2309;&#2343;&#2381;&#2351;&#2351;&#2344; &#2325;&#2375; &#2354;&#2367;&#2319; &#2360;&#2381;&#2357;&#2351;&#2306; &#2325;&#2366; &#2360;&#2350;&#2352;&#2381;&#2346;&#2339; &#2325;&#2352;&#2340;&#2375; &#2361;&#2369;&#2319; &#2342;&#2360; &#2357;&#2352;&#2381;&#2359;&#2379;&#2306; &#2340;&#2325; &#2344;&#2366;&#2354;&#2306;&#2342;&#2366; &#2350;&#2336; &#2350;&#2375;&#2306; &#2344;&#2367;&#2357;&#2366;&#2360; &#2325;&#2367;&#2351;&#2366;&#2404;</span></p>\\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "18",
                    question_en: "<p>2.<span style=\"font-family: Roboto;\"> Thomas Cup is a/an________.</span></p>\\n",
                    question_hi: "<p>2.<span style=\"font-family: Palanquin;\"> &#2341;&#2377;&#2350;&#2360; &#2325;&#2346; &#2319;&#2325; ______ &#2361;&#2376;&#2404;</span></p>\\n",
                    options_en: ["<p>annual event</p>\\n", "<p>biennial event</p>\\n", 
                                "<p>quarterly event</p>\\n", "<p>triennial event</p>\\n"],
                    options_hi: ["<p><span style=\"font-family: Palanquin;\">&#2357;&#2366;&#2352;&#2381;&#2359;&#2367;&#2325; &#2310;&#2351;&#2379;&#2332;&#2344;</span></p>\\n", "<p><span style=\"font-family: Palanquin;\">&#2342;&#2381;&#2357;&#2367;&#2357;&#2366;&#2352;&#2381;&#2359;&#2367;&#2325; &#2310;&#2351;&#2379;&#2332;&#2344;</span></p>\\n",
                                "<p><span style=\"font-family: Palanquin;\">&#2340;&#2367;&#2350;&#2366;&#2361;&#2368; &#2310;&#2351;&#2379;&#2332;&#2344;</span></p>\\n", "<p><span style=\"font-family: Palanquin;\">&#2340;&#2381;&#2352;&#2367;&#2357;&#2366;&#2352;&#2381;&#2359;&#2367;&#2325; &#2310;&#2351;&#2379;&#2332;&#2344;</span></p>\\n"],
                    solution_en: "<p>2.(b) <strong>Biennial event. Thomas Cup </strong>- <span style=\"font-family: Roboto;\">The first tournament was held in 1948 - 49 and won by Malaya. India&rsquo;s men&rsquo;s badminton team won the Thomas Cup title for the first time ever in 2022 by defeating Indonesia. </span><span style=\"font-family: Roboto;\"><strong>Uber Cup</strong> - </span><span style=\"font-family: Roboto;\">First held in 1956&ndash;1957 and contested at three year intervals, it has been contested every two years since 1984. </span></p>\\n",
                    solution_hi: "<p>2.(b)<strong> &#2342;&#2381;&#2357;&#2367;&#2357;&#2366;&#2352;&#2381;&#2359;&#2367;&#2325; &#2310;&#2351;&#2379;&#2332;&#2344;&#2404; &#2341;&#2377;&#2350;&#2360; &#2325;&#2346; </strong>-<span style=\"font-family: Palanquin;\"> &#2346;&#2361;&#2354;&#2366; &#2335;&#2370;&#2352;&#2381;&#2344;&#2366;&#2350;&#2375;&#2306;&#2335; 1948 - 49 &#2350;&#2375;&#2306; &#2310;&#2351;&#2379;&#2332;&#2367;&#2340; &#2325;&#2367;&#2351;&#2366; &#2327;&#2351;&#2366; &#2341;&#2366; &#2324;&#2352; &#2350;&#2354;&#2366;&#2351;&#2366; &#2344;&#2375; &#2332;&#2368;&#2340;&#2366; &#2341;&#2366;&#2404; &#2349;&#2366;&#2352;&#2340; &#2325;&#2368; &#2346;&#2369;&#2352;&#2369;&#2359; &#2348;&#2376;&#2337;&#2350;&#2367;&#2306;&#2335;&#2344; &#2335;&#2368;&#2350; &#2344;&#2375; &#2311;&#2306;&#2337;&#2379;&#2344;&#2375;&#2358;&#2367;&#2351;&#2366; &#2325;&#2379; &#2361;&#2352;&#2366;&#2325;&#2352; 2022 &#2350;&#2375;&#2306; &#2346;&#2361;&#2354;&#2368; &#2348;&#2366;&#2352; &#2341;&#2377;&#2350;&#2360; &#2325;&#2346; &#2325;&#2366; &#2326;&#2367;&#2340;&#2366;&#2348; &#2332;&#2368;&#2340;&#2366; &#2341;&#2366; &#2404; </span><strong><span style=\"font-family: Palanquin;\">&#2313;&#2348;&#2375;&#2352; &#2325;&#2346;</span></strong><span style=\"font-family: Palanquin;\"><strong> </strong>- &#2346;&#2361;&#2354;&#2368; &#2348;&#2366;&#2352; 1956-1957 &#2350;&#2375;&#2306; &#2310;&#2351;&#2379;&#2332;&#2367;&#2340; &#2325;&#2367;&#2351;&#2366; &#2327;&#2351;&#2366; &#2341;&#2366; &#2324;&#2352; &#2340;&#2368;&#2344; &#2360;&#2366;&#2354; &#2325;&#2375; &#2309;&#2306;&#2340;&#2352;&#2366;&#2354; &#2346;&#2352; &#2310;&#2351;&#2379;&#2332;&#2367;&#2340; &#2325;&#2367;&#2351;&#2366; &#2327;&#2351;&#2366; &#2341;&#2366;, 1984 &#2325;&#2375; &#2348;&#2366;&#2342; &#2360;&#2375; &#2311;&#2360;&#2375; &#2346;&#2381;&#2352;&#2340;&#2381;&#2351;&#2375;&#2325; &#2342;&#2379; &#2357;&#2352;&#2381;&#2359; &#2325;&#2375; &#2309;&#2306;&#2340;&#2352;&#2366;&#2354; &#2350;&#2375;&#2306; &#2310;&#2351;&#2379;&#2332;&#2367;&#2340; &#2325;&#2367;&#2351;&#2366; &#2332;&#2366;&#2344;&#2375; &#2354;&#2327;&#2366;&#2404; </span></p>\\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "18",
                    question_en: "<p>3. <span style=\"font-family: Roboto;\">\'Penalty Corner\' is related to which of the following sports?</span></p>\\n",
                    question_hi: "<p>3. <span style=\"font-family: Palanquin;\">&#2346;&#2375;&#2344;&#2366;&#2354;&#2381;&#2335;&#2368; &#2325;&#2377;&#2352;&#2381;&#2344;&#2352;\' &#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340; &#2350;&#2375;&#2306; &#2360;&#2375; &#2325;&#2367;&#2360; &#2326;&#2375;&#2354; &#2360;&#2375; &#2360;&#2306;&#2348;&#2306;&#2343;&#2367;&#2340; &#2361;&#2376;?</span></p>\\n",
                    options_en: ["<p>Chess</p>\\n", "<p>Tennis</p>\\n", 
                                "<p>Badminton</p>\\n", "<p>Hockey</p>\\n"],
                    options_hi: ["<p>&#2358;&#2340;&#2352;&#2306;&#2332;</p>\\n", "<p>&#2335;&#2375;&#2344;&#2367;&#2360;</p>\\n",
                                "<p>&#2348;&#2376;&#2337;&#2350;&#2367;&#2306;&#2335;&#2344;</p>\\n", "<p>&#2361;&#2377;&#2325;&#2368;</p>\\n"],
                    solution_en: "<p>3.(d)&nbsp;<strong>&nbsp;Hockey. </strong><span style=\"font-weight: 400;\">Terms related to </span><strong>Hockey</strong><span style=\"font-weight: 400;\"> - Cross, Dangerous Play, Defense (Defender), Drive, Flagrant Foul, Flick, Goal-line, Long Hit, Penalty Stroke, Raised Ball, Scoop, Striker, Tackle, Undercutting. </span><strong>Chess</strong><span style=\"font-weight: 400;\"> - Grandmaster, Back-rank, checkmate. </span><strong>Tennis</strong><span style=\"font-weight: 400;\"> - Alley, Backhand, Double Fault, Lob, Stance, Topspin. </span><strong>Badminton </strong><span style=\"font-weight: 400;\">- Hairpin Net Shot, Rally, Flick.</span></p>\\n",
                    solution_hi: "<p>3.(d)<strong>&nbsp;&#2361;&#2377;&#2325;&#2368;&#2404; &#2361;&#2377;&#2325;&#2368; </strong><span style=\"font-weight: 400;\">&#2360;&#2375; &#2360;&#2306;&#2348;&#2306;&#2343;&#2367;&#2340; &#2358;&#2348;&#2381;&#2342; - &#2325;&#2381;&#2352;&#2377;&#2360;, &#2337;&#2375;&#2306;&#2332;&#2352;&#2360; &#2346;&#2381;&#2354;&#2375;, &#2337;&#2367;&#2347;&#2375;&#2306;&#2360; (&#2337;&#2367;&#2347;&#2375;&#2306;&#2337;&#2352;), &#2337;&#2381;&#2352;&#2366;&#2311;&#2357;, &#2347;&#2381;&#2354;&#2376;&#2327;&#2352;&#2375;&#2306;&#2335; &#2347;&#2366;&#2313;&#2354;, &#2347;&#2381;&#2354;&#2367;&#2325;, &#2327;&#2379;&#2354;-&#2354;&#2366;&#2311;&#2344;, &#2354;&#2377;&#2344;&#2381;&#2327; &#2361;&#2367;&#2335;, &#2346;&#2375;&#2344;&#2354;&#2381;&#2335;&#2368; &#2360;&#2381;&#2335;&#2381;&#2352;&#2379;&#2325;, &#2352;&#2375;&#2332;&#2381;&#2337; &#2348;&#2377;&#2354;, &#2360;&#2381;&#2325;&#2370;&#2346;, &#2360;&#2381;&#2335;&#2381;&#2352;&#2366;&#2311;&#2325;&#2352;, &#2335;&#2376;&#2325;&#2354;, &#2309;&#2306;&#2337;&#2352;&#2325;&#2335;&#2367;&#2306;&#2327;&#2404; </span><strong>&#2358;&#2340;&#2352;&#2306;&#2332; </strong><span style=\"font-weight: 400;\">- &#2327;&#2381;&#2352;&#2376;&#2306;&#2337;&#2350;&#2366;&#2360;&#2381;&#2335;&#2352;, &#2348;&#2376;&#2325;-&#2352;&#2376;&#2306;&#2325;, &#2330;&#2375;&#2325;&#2350;&#2375;&#2335;&#2404; </span><strong>&#2335;&#2375;&#2344;&#2367;&#2360; </strong><span style=\"font-weight: 400;\">- &#2319;&#2354;&#2368;, &#2348;&#2376;&#2325;&#2361;&#2376;&#2306;&#2337;, &#2337;&#2348;&#2354; &#2347;&#2377;&#2354;&#2381;&#2335;, &#2354;&#2379;&#2348;, &#2360;&#2381;&#2335;&#2366;&#2306;&#2360;, &#2335;&#2377;&#2346;&#2360;&#2381;&#2346;&#2367;&#2344;&#2404; </span><strong>&#2348;&#2376;&#2337;&#2350;&#2367;&#2306;&#2335;&#2344; </strong><span style=\"font-weight: 400;\">- &#2361;&#2375;&#2351;&#2352;&#2346;&#2367;&#2344; &#2344;&#2375;&#2335; &#2358;&#2377;&#2335;, &#2352;&#2376;&#2354;&#2368;, &#2347;&#2381;&#2354;&#2367;&#2325;&#2404;</span></p>\\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "18",
                    question_en: "<p>4. <span style=\"font-family: Roboto;\">Which is the only living representative of Sphenopsida that has an underground, creeping and perennial rhizome that gives off aerial as well as underground branches?</span></p>\\n",
                    question_hi: "<p>4.<span style=\"font-family: Palanquin;\"> &#2360;&#2381;&#2347;&#2375;&#2344;&#2379;&#2346;&#2381;&#2360;&#2367;&#2337;&#2366; &#2325;&#2366; &#2319;&#2325;&#2350;&#2366;&#2340;&#2381;&#2352; &#2332;&#2368;&#2357;&#2367;&#2340; &#2346;&#2381;&#2352;&#2340;&#2367;&#2344;&#2367;&#2343;&#2367; &#2325;&#2380;&#2344;-&#2360;&#2366; &#2361;&#2376;, &#2332;&#2367;&#2360;&#2350;&#2375;&#2306; &#2319;&#2325; &#2349;&#2370;&#2350;&#2367;&#2327;&#2340;, &#2343;&#2368;&#2352;&#2375;-&#2343;&#2368;&#2352;&#2375; &#2348;&#2338;&#2364;&#2344;&#2375; &#2357;&#2366;&#2354;&#2366; &#2324;&#2352; &#2348;&#2366;&#2352;&#2361;&#2350;&#2366;&#2360;&#2368; &#2346;&#2381;&#2352;&#2325;&#2306;&#2342; &#2361;&#2379;&#2340;&#2366; &#2361;&#2376; &#2324;&#2352; &#2332;&#2379; &#2361;&#2357;&#2366;&#2312; &#2325;&#2375; &#2360;&#2366;&#2341;-&#2360;&#2366;&#2341; &#2349;&#2370;&#2350;&#2367;&#2327;&#2340; &#2358;&#2366;&#2326;&#2366;&#2319;&#2306; &#2349;&#2368; &#2342;&#2375;&#2340;&#2366; &#2361;&#2376;?</span></p>\\n",
                    options_en: ["<p>Adiantum</p>\\n", "<p>Dryopteris</p>\\n", 
                                "<p>Selaginella</p>\\n", "<p>Equisetum</p>\\n"],
                    options_hi: ["<p><span style=\"font-weight: 400;\">&#2319;&#2337;&#2367;&#2351;&#2306;&#2335;&#2350; (</span><span style=\"font-weight: 400;\">Adiantum</span><span style=\"font-weight: 400;\">)</span></p>\\n", "<p><span style=\"font-weight: 400;\">&#2337;&#2381;&#2352;&#2366;&#2351;&#2379;&#2346;&#2381;&#2335;&#2375;&#2352;&#2367;&#2360; (</span><span style=\"font-weight: 400;\">Dryopteris</span><span style=\"font-weight: 400;\">)</span></p>\\n",
                                "<p><span style=\"font-weight: 400;\">&#2360;&#2375;&#2354;&#2381;&#2332;&#2367;&#2344;&#2375;&#2354;&#2366; (</span><span style=\"font-weight: 400;\">Selaginella</span><span style=\"font-weight: 400;\">)</span></p>\\n", "<p><span style=\"font-weight: 400;\">&#2311;&#2325;&#2381;&#2357;&#2367;&#2360;&#2375;&#2335;&#2350; (</span><span style=\"font-weight: 400;\">Equisetum</span><span style=\"font-weight: 400;\">)</span></p>\\n"],
                    solution_en: "<p>4.(d)<strong> Equisetum</strong> - <span style=\"font-family: Roboto;\">The aerial branches are herbaceous and usually grow to a height of 10-60 cm, but they can attain more height in some species.</span><span style=\"font-family: Roboto;\"> </span><span style=\"font-family: Roboto;\">The pteridophytes are classified into Psilopsida (Psilotum), Lycopsida (Selaginella, Lycopodium), Sphenopsida (Equisetum) and Pteropsida (Dryopteris, Pteris, Adiantum). </span></p>\\n",
                    solution_hi: "<p>4.(d) <strong>&#2311;&#2325;&#2381;&#2357;&#2367;&#2360;&#2375;&#2335;&#2350; (Equisetum)</strong> <span style=\"font-family: Palanquin;\">- &#2357;&#2366;&#2351;&#2357;&#2368;&#2351; &#2358;&#2366;&#2326;&#2366;&#2319;&#2306; &#2332;&#2337;&#2364;&#2368;-&#2348;&#2370;&#2335;&#2368; &#2357;&#2366;&#2354;&#2368; &#2361;&#2379;&#2340;&#2368; &#2361;&#2376;&#2306; &#2324;&#2352; &#2310;&#2350;&#2340;&#2380;&#2352; &#2346;&#2352; 10-60 &#2360;&#2375;&#2350;&#2368; &#2325;&#2368; &#2314;&#2306;&#2330;&#2366;&#2312; &#2340;&#2325; &#2348;&#2338;&#2364;&#2340;&#2368; &#2361;&#2376;&#2306;, &#2354;&#2375;&#2325;&#2367;&#2344; &#2325;&#2369;&#2331; &#2346;&#2381;&#2352;&#2332;&#2366;&#2340;&#2367;&#2351;&#2379;&#2306; &#2350;&#2375;&#2306; &#2357;&#2375; &#2309;&#2343;&#2367;&#2325; &#2314;&#2306;&#2330;&#2366;&#2312; &#2346;&#2381;&#2352;&#2366;&#2346;&#2381;&#2340; &#2325;&#2352; &#2360;&#2325;&#2340;&#2368; &#2361;&#2376;&#2306;&#2404; &#2335;&#2375;&#2352;&#2367;&#2337;&#2379;&#2347;&#2366;&#2311;&#2335;&#2381;&#2360; &#2325;&#2379; &#2360;&#2366;&#2311;&#2354;&#2379;&#2346;&#2381;&#2360;&#2367;&#2337;&#2366; (&#2360;&#2366;&#2311;&#2354;&#2379;&#2335;&#2350;), &#2354;&#2366;&#2311;&#2325;&#2379;&#2346;&#2381;&#2360;&#2367;&#2337;&#2366; (&#2360;&#2375;&#2354;&#2366;&#2332;&#2367;&#2344;&#2375;&#2354;&#2366;, &#2354;&#2366;&#2311;&#2325;&#2379;&#2346;&#2379;&#2337;&#2367;&#2351;&#2350;), &#2360;&#2381;&#2347;&#2375;&#2344;&#2379;&#2346;&#2381;&#2360;&#2367;&#2337;&#2366; (&#2311;&#2325;&#2381;&#2357;&#2367;&#2360;&#2375;&#2335;&#2350;) &#2324;&#2352; &#2335;&#2375;&#2352;&#2379;&#2346;&#2381;&#2360;&#2367;&#2337;&#2366; (&#2337;&#2381;&#2352;&#2366;&#2351;&#2379;&#2346;&#2381;&#2335;&#2375;&#2352;&#2367;&#2360;, &#2335;&#2375;&#2352;&#2367;&#2360;, &#2319;&#2337;&#2367;&#2319;&#2306;&#2335;&#2350;) &#2350;&#2375;&#2306; &#2357;&#2352;&#2381;&#2327;&#2368;&#2325;&#2371;&#2340; &#2325;&#2367;&#2351;&#2366; &#2327;&#2351;&#2366; &#2361;&#2376;&#2404;</span></p>\\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "18",
                    question_en: "<p><span style=\"font-family: Roboto;\">5. </span><span style=\"font-family: Roboto;\">Trichloromethane is better known as:</span></p>\\n",
                    question_hi: "<p><span style=\"font-family: Roboto;\">5. </span><span style=\"font-family: Palanquin;\">&#2335;&#2381;&#2352;&#2366;&#2311;&#2325;&#2381;&#2354;&#2379;&#2352;&#2379;&#2350;&#2375;&#2341;&#2375;&#2344; &#2325;&#2366; &#2346;&#2381;&#2352;&#2330;&#2354;&#2367;&#2340; &#2344;&#2366;&#2350; &#2325;&#2381;&#2351;&#2366; &#2361;&#2376; ?</span></p>\\n",
                    options_en: ["<p>chloroform</p>\\n", "<p>butanes</p>\\n", 
                                "<p>LPG</p>\\n", "<p>laughing gas</p>\\n"],
                    options_hi: ["<p>&#2325;&#2381;&#2354;&#2379;&#2352;&#2379;&#2347;&#2366;&#2352;&#2381;&#2350;</p>\\n", "<p>&#2348;&#2381;&#2351;&#2370;&#2335;&#2375;&#2344;</p>\\n",
                                "<p>&#2319;&#2354;&#2346;&#2368;&#2332;&#2368;</p>\\n", "<p>&#2354;&#2366;&#2347;&#2364;&#2367;&#2306;&#2327; &#2327;&#2376;&#2360;</p>\\n"],
                    solution_en: "<p>5.(a)&nbsp;<strong>Chloroform (</strong><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>CHCl</mi><mn>3</mn></msub></math><strong>) - </strong><span style=\"font-weight: 400;\">It is used as an ideal anesthetic. It is a colorless, sweet-smelling dense liquid. Other names - Formyl Trichloride, TriChloroform. </span><strong>LPG</strong><span style=\"font-weight: 400;\"> (Liquefied petroleum gas) is any of multiple liquid mixtures of the volatile hydrocarbons propane (</span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi mathvariant=\"normal\">C</mi><mn>3</mn></msub><msub><mi mathvariant=\"normal\">H</mi><mn>8</mn></msub></math><span style=\"font-weight: 400;\">), propene (</span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi mathvariant=\"normal\">C</mi><mn>3</mn></msub><msub><mi mathvariant=\"normal\">H</mi><mn>6</mn></msub></math><span style=\"font-weight: 400;\">), butane (</span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi mathvariant=\"normal\">C</mi><mn>4</mn></msub><msub><mi mathvariant=\"normal\">H</mi><mn>10</mn></msub></math><span style=\"font-weight: 400;\">), and butene (</span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi mathvariant=\"normal\">C</mi><mn>4</mn></msub><msub><mi mathvariant=\"normal\">H</mi><mn>8</mn></msub></math><span style=\"font-weight: 400;\">). </span><strong>Laughing gas</strong><span style=\"font-weight: 400;\"> - Nitrous oxide (</span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi mathvariant=\"normal\">N</mi><mn>2</mn></msub><mi mathvariant=\"normal\">O</mi></math><span style=\"font-weight: 400;\">) - Colorless, non-flammable gas.&nbsp;</span></p>\\n",
                    solution_hi: "<p>5.(a)<strong> &#2325;&#2381;&#2354;&#2379;&#2352;&#2379;&#2347;&#2377;&#2352;&#2381;&#2350; (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>CHCl</mi><mn>3</mn></msub></math></strong><span style=\"font-family: Roboto;\"><strong>)</strong> - </span><span style=\"font-family: Palanquin;\">&#2311;&#2360;&#2325;&#2366; &#2313;&#2346;&#2351;&#2379;&#2327; &#2319;&#2325; &#2309;&#2330;&#2375;&#2340;&#2344; &#2309;&#2357;&#2360;&#2381;&#2341;&#2366; &#2325;&#2375; &#2352;&#2370;&#2346; &#2350;&#2375;&#2306; &#2325;&#2367;&#2351;&#2366; &#2332;&#2366;&#2340;&#2366; &#2361;&#2376;&#2404; &#2351;&#2361; &#2319;&#2325; &#2352;&#2306;&#2327;&#2361;&#2368;&#2344;, &#2350;&#2368;&#2336;&#2368; &#2327;&#2306;&#2343; &#2357;&#2366;&#2354;&#2366; &#2327;&#2366;&#2338;&#2364;&#2366; &#2342;&#2381;&#2352;&#2357; &#2346;&#2342;&#2366;&#2352;&#2381;&#2341; &#2361;&#2376;&#2404; &#2309;&#2344;&#2381;&#2351; &#2344;&#2366;&#2350; - &#2347;&#2377;&#2352;&#2381;&#2350;&#2367;&#2354; &#2335;&#2381;&#2352;&#2366;&#2311;&#2325;&#2381;&#2354;&#2379;&#2352;&#2366;&#2311;&#2337;, &#2335;&#2381;&#2352;&#2366;&#2311;&#2325;&#2381;&#2354;&#2379;&#2352;&#2379;&#2347;&#2377;&#2352;&#2381;&#2350;&#2404;<strong> </strong></span><strong><span style=\"font-family: Roboto;\">LPG</span></strong><span style=\"font-family: Palanquin;\"> (&#2342;&#2381;&#2352;&#2357;&#2367;&#2340; &#2346;&#2375;&#2335;&#2381;&#2352;&#2379;&#2354;&#2367;&#2351;&#2350; &#2327;&#2376;&#2360;) &#2357;&#2366;&#2359;&#2381;&#2346;&#2358;&#2368;&#2354; &#2361;&#2366;&#2311;&#2337;&#2381;&#2352;&#2379;&#2325;&#2366;&#2352;&#2381;&#2348;&#2344; &#2346;&#2381;&#2352;&#2379;&#2346;&#2375;&#2344; (</span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi mathvariant=\"normal\">C</mi><mn>3</mn></msub><msub><mi mathvariant=\"normal\">H</mi><mn>8</mn></msub></math><span style=\"font-family: Palanquin;\">), &#2346;&#2381;&#2352;&#2379;&#2346;&#2368;&#2344; (</span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi mathvariant=\"normal\">C</mi><mn>3</mn></msub><msub><mi mathvariant=\"normal\">H</mi><mn>6</mn></msub></math><span style=\"font-family: Palanquin;\">), &#2348;&#2381;&#2351;&#2370;&#2335;&#2375;&#2344; (</span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi mathvariant=\"normal\">C</mi><mn>4</mn></msub><msub><mi mathvariant=\"normal\">H</mi><mn>10</mn></msub></math><span style=\"font-family: Palanquin;\">), &#2324;&#2352; &#2348;&#2381;&#2351;&#2369;&#2335;&#2368;&#2344; (</span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi mathvariant=\"normal\">C</mi><mn>4</mn></msub><msub><mi mathvariant=\"normal\">H</mi><mn>8</mn></msub></math><span style=\"font-family: Palanquin;\">) &#2325;&#2375; &#2325;&#2312; &#2342;&#2381;&#2352;&#2357; &#2350;&#2367;&#2358;&#2381;&#2352;&#2339;&#2379;&#2306; &#2350;&#2375;&#2306; &#2360;&#2375; &#2319;&#2325; &#2361;&#2376;&#2404; <strong>&#2354;&#2366;&#2347;&#2367;&#2306;&#2327; &#2327;&#2376;&#2360; -</strong> &#2344;&#2366;&#2311;&#2335;&#2381;&#2352;&#2360; &#2321;&#2325;&#2381;&#2360;&#2366;&#2311;&#2337; (</span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi mathvariant=\"normal\">N</mi><mn>2</mn></msub><mi mathvariant=\"normal\">O</mi></math><span style=\"font-family: Palanquin;\">) - &#2352;&#2306;&#2327;&#2361;&#2368;&#2344;, &#2309;&#2332;&#2381;&#2357;&#2354;&#2344;&#2358;&#2368;&#2354; &#2327;&#2376;&#2360;&#2404;</span></p>\\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "18",
                    question_en: "<p><span style=\"font-family: Roboto;\">6. </span><span style=\"font-family: Roboto;\">Which Constitutional Amendment added Fundamental Duties in the Constitution?</span></p>\\n",
                    question_hi: "<p><span style=\"font-family: Roboto;\">6. </span><span style=\"font-family: Palanquin;\">&#2325;&#2367;&#2360; &#2360;&#2306;&#2357;&#2376;&#2343;&#2366;&#2344;&#2367;&#2325; &#2360;&#2306;&#2358;&#2379;&#2343;&#2344; &#2342;&#2381;&#2357;&#2366;&#2352;&#2366; &#2360;&#2306;&#2357;&#2367;&#2343;&#2366;&#2344; &#2350;&#2375;&#2306; &#2350;&#2380;&#2354;&#2367;&#2325; &#2325;&#2352;&#2381;&#2340;&#2357;&#2381;&#2351;&#2379;&#2306; &#2325;&#2379; &#2358;&#2366;&#2350;&#2367;&#2354; &#2325;&#2367;&#2351;&#2366; &#2327;&#2351;&#2366; &#2341;&#2366;?</span></p>\\n",
                    options_en: ["<p>Forty-Second Amendment Act, 1976</p>\\n", "<p>Forty-Seventh Amendment, 1984</p>\\n", 
                                "<p>Forty-Fourth Amendment Act 1978</p>\\n", "<p>Fifty-Second Amendment, 1985</p>\\n"],
                    options_hi: ["<p>&#2348;&#2351;&#2366;&#2354;&#2368;&#2360;&#2357;&#2366;&#2306; &#2360;&#2306;&#2357;&#2367;&#2343;&#2366;&#2344; &#2360;&#2306;&#2358;&#2379;&#2343;&#2344; &#2309;&#2343;&#2367;&#2344;&#2367;&#2351;&#2350;, 1976</p>\\n", "<p>&#2360;&#2376;&#2306;&#2340;&#2366;&#2354;&#2368;&#2360;&#2357;&#2366;&#2306; &#2360;&#2306;&#2357;&#2367;&#2343;&#2366;&#2344; &#2360;&#2306;&#2358;&#2379;&#2343;&#2344;, 1984</p>\\n",
                                "<p>&#2330;&#2380;&#2357;&#2366;&#2354;&#2368;&#2360;&#2357;&#2366;&#2306; &#2360;&#2306;&#2357;&#2367;&#2343;&#2366;&#2344; &#2360;&#2306;&#2358;&#2379;&#2343;&#2344; &#2309;&#2343;&#2367;&#2344;&#2367;&#2351;&#2350;, 1978</p>\\n", "<p>&#2348;&#2366;&#2357;&#2344;&#2357;&#2366;&#2306; &#2360;&#2306;&#2357;&#2367;&#2343;&#2366;&#2344; &#2360;&#2306;&#2358;&#2379;&#2343;&#2344;, 1985</p>\\n"],
                    solution_en: "<p>6.(a)<strong> Forty-Second Amendment Act, 1976 </strong><span style=\"font-family: Roboto;\">(Mini Constitution)</span><span style=\"font-family: Roboto;\">. </span><span style=\"font-family: Roboto;\">Some Other Provisions : Added three new words (Socialist, Secular and Integrity) in the Preamble, Added three New Directive Principles of state policy (Equal justice and free legal aid, Participation of workers in the management of industries and protection of the environment, Forests and wildlife), Shifted five subjects from the state list to the concurrent list.</span></p>\\n",
                    solution_hi: "<p>6.(a) <strong>&#2348;&#2351;&#2366;&#2354;&#2368;&#2360;&#2357;&#2366;&#2305; &#2360;&#2306;&#2358;&#2379;&#2343;&#2344; &#2309;&#2343;&#2367;&#2344;&#2367;&#2351;&#2350;, 1976 </strong><span style=\"font-family: Palanquin;\">(&#2354;&#2328;&#2369; &#2360;&#2306;&#2357;&#2367;&#2343;&#2366;&#2344;)&#2404; &#2325;&#2369;&#2331; &#2309;&#2344;&#2381;&#2351; &#2346;&#2381;&#2352;&#2366;&#2357;&#2343;&#2366;&#2344;: &#2346;&#2381;&#2352;&#2360;&#2381;&#2340;&#2366;&#2357;&#2344;&#2366; &#2350;&#2375;&#2306; &#2340;&#2368;&#2344; &#2344;&#2319; &#2358;&#2348;&#2381;&#2342; (&#2360;&#2350;&#2366;&#2332;&#2357;&#2366;&#2342;&#2368;, &#2343;&#2352;&#2381;&#2350;&#2344;&#2367;&#2352;&#2346;&#2375;&#2325;&#2381;&#2359; &#2324;&#2352; &#2309;&#2326;&#2306;&#2337;&#2340;&#2366;) &#2332;&#2379;&#2337;&#2364;&#2375; &#2327;&#2319;, &#2352;&#2366;&#2332;&#2381;&#2351; &#2325;&#2375; &#2344;&#2368;&#2340;&#2367; &#2344;&#2367;&#2342;&#2375;&#2358;&#2325; &#2340;&#2340;&#2381;&#2357;&#2379;&#2306; &#2350;&#2375;&#2306; &#2340;&#2368;&#2344; &#2344;&#2351;&#2375; &#2360;&#2367;&#2342;&#2381;&#2343;&#2366;&#2306;&#2340; &#2332;&#2379;&#2337;&#2364;&#2375; &#2327;&#2351;&#2375; (&#2360;&#2350;&#2366;&#2344; &#2344;&#2381;&#2351;&#2366;&#2351; &#2324;&#2352; &#2350;&#2369;&#2347;&#2381;&#2340; &#2325;&#2366;&#2344;&#2370;&#2344;&#2368; &#2360;&#2361;&#2366;&#2351;&#2340;&#2366;, &#2313;&#2342;&#2381;&#2351;&#2379;&#2327;&#2379;&#2306; &#2325;&#2375; &#2346;&#2381;&#2352;&#2348;&#2306;&#2343;&#2344; &#2324;&#2352; &#2346;&#2352;&#2381;&#2351;&#2366;&#2357;&#2352;&#2339;, &#2357;&#2344; &#2324;&#2352; &#2357;&#2344;&#2381;&#2351; &#2332;&#2368;&#2357;&#2344; &#2325;&#2368; &#2360;&#2369;&#2352;&#2325;&#2381;&#2359;&#2366; &#2350;&#2375;&#2306; &#2358;&#2381;&#2352;&#2350;&#2367;&#2325;&#2379;&#2306; &#2325;&#2368; &#2349;&#2366;&#2327;&#2368;&#2342;&#2366;&#2352;&#2368;),&#2346;&#2366;&#2306;&#2330; &#2357;&#2367;&#2359;&#2351;&#2379;&#2306; &#2325;&#2379; &#2352;&#2366;&#2332;&#2381;&#2351; &#2360;&#2370;&#2330;&#2368; &#2360;&#2375; &#2360;&#2350;&#2357;&#2352;&#2381;&#2340;&#2368; &#2360;&#2370;&#2330;&#2368; &#2350;&#2375;&#2306; &#2360;&#2381;&#2341;&#2366;&#2344;&#2366;&#2306;&#2340;&#2352;&#2367;&#2340; &#2325;&#2352; &#2342;&#2367;&#2351;&#2366; &#2327;&#2351;&#2366;&#2404;</span></p>\\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "18",
                    question_en: "<p><span style=\"font-family: Roboto;\">7</span><span style=\"font-family: Roboto;\">. Match the columns.</span></p>\\r\\n<p><span style=\"font-family: Roboto;\"> </span><span style=\"font-family: Roboto;\">States&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;</span><span style=\"font-family: Roboto;\">Hills</span></p>\\r\\n<p><span style=\"font-family: Roboto;\">1. Kerala&nbsp; &nbsp;&nbsp;</span><span style=\"font-family: Roboto;\">&nbsp; &nbsp; &nbsp; &nbsp; a. Anaimalai Hills</span></p>\\r\\n<p><span style=\"font-family: Roboto;\">2. Meghalaya&nbsp; &nbsp; &nbsp;b. Garo Hills</span></p>\\r\\n<p><span style=\"font-family: Roboto;\">3. Mizoram</span><span style=\"font-family: Roboto;\">&nbsp; &nbsp; &nbsp; &nbsp; c. Lushai Hills</span></p>\\n",
                    question_hi: "<p><span style=\"font-family: Roboto;\">7</span><span style=\"font-family: Palanquin;\">. &#2360;&#2381;&#2340;&#2306;&#2349;&#2379;&#2306; &#2325;&#2366; &#2350;&#2367;&#2354;&#2366;&#2344; &#2325;&#2352;&#2375;&#2306;&#2404; </span></p>\\r\\n<p><span style=\"font-family: Roboto;\"> </span><span style=\"font-family: Palanquin;\"><strong>&#2352;&#2366;&#2332;&#2381;&#2351;&nbsp;</strong> &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;</span><strong><span style=\"font-family: Palanquin;\">&#2346;&#2361;&#2366;&#2337;&#2367;&#2351;&#2366;&#2305;</span></strong></p>\\r\\n<p><span style=\"font-family: Palanquin;\">1. &#2325;&#2375;&#2352;&#2354;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp;</span><span style=\"font-family: Palanquin;\"> a. &#2309;&#2344;&#2381;&#2344;&#2366;&#2350;&#2354;&#2366;&#2312; &#2346;&#2361;&#2366;&#2337;&#2364;&#2367;&#2351;&#2366;&#2305; </span></p>\\r\\n<p><span style=\"font-family: Palanquin;\">2. &#2350;&#2375;&#2328;&#2366;&#2354;&#2351;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp;</span><span style=\"font-family: Palanquin;\"> b. &#2327;&#2366;&#2352;&#2379; &#2346;&#2361;&#2366;&#2337;&#2364;&#2367;&#2351;&#2366;&#2305;</span></p>\\r\\n<p><span style=\"font-family: Palanquin;\">3. &#2350;&#2367;&#2332;&#2379;&#2352;&#2350;</span><span style=\"font-family: Palanquin;\">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; c. &#2354;&#2369;&#2358;&#2366;&#2312; &#2346;&#2361;&#2366;&#2337;&#2364;&#2367;&#2351;&#2366;&#2305; </span></p>\\n",
                    options_en: ["<p>1-a, 2-b, 3-c</p>\\n", "<p>1-b, 2-c, 3-a</p>\\n", 
                                "<p>1-b, 2-a, 3-c</p>\\n", "<p>1-c, 2-b, 3-a</p>\\n"],
                    options_hi: ["<p>1-a, 2-b, 3-c</p>\\n", "<p>1-b, 2-c, 3-a</p>\\n",
                                "<p>1-b, 2-a, 3-c</p>\\n", "<p>1-c, 2-b, 3-a</p>\\n"],
                    solution_en: "<p>7.(a) <strong>1-a, 2-b, 3-c.</strong><span style=\"font-family: Roboto;\"> Some Other Hills : Aravalli hills - Originate in Gujarat and extend till Haryana. Vindhya range - Gujarat to Bihar. Satpura range - Madhya Pradesh and Chhattisgarh. Kaimur Range - Madhya Pradesh, Uttar Pradesh, Bihar. Mahadeo Range - Madhya Pradesh. Ajanta Range - Maharashtra. Rajmahal Hills - Jharkhand. Mikir Hills - Assam. Harishchandra Range - Maharashtra. Baba Budan Giri hills - Karnataka.</span></p>\\n",
                    solution_hi: "<p>7.(a)<strong> 1-a, 2-b, 3-c</strong><span style=\"font-family: Palanquin;\">&#2404; &#2325;&#2369;&#2331; &#2309;&#2344;&#2381;&#2351; &#2346;&#2361;&#2366;&#2337;&#2364;&#2367;&#2351;&#2366;&#2305;: &#2309;&#2352;&#2366;&#2357;&#2354;&#2368; &#2346;&#2361;&#2366;&#2337;&#2364;&#2367;&#2351;&#2366;&#2305; - &#2327;&#2369;&#2332;&#2352;&#2366;&#2340; &#2360;&#2375; &#2346;&#2381;&#2352;&#2366;&#2352;&#2350;&#2381;&#2349; &#2361;&#2379;&#2340;&#2368; &#2361;&#2376;&#2306; &#2324;&#2352; &#2361;&#2352;&#2367;&#2351;&#2366;&#2339;&#2366; &#2340;&#2325; &#2347;&#2376;&#2354;&#2368; &#2361;&#2369;&#2312; &#2361;&#2376;&#2306;&#2404; &#2357;&#2367;&#2306;&#2343;&#2381;&#2351; &#2346;&#2352;&#2381;&#2357;&#2340;&#2350;&#2366;&#2354;&#2366; - &#2327;&#2369;&#2332;&#2352;&#2366;&#2340; &#2360;&#2375; &#2348;&#2367;&#2361;&#2366;&#2352; &#2340;&#2325;&#2404; &#2360;&#2340;&#2346;&#2369;&#2337;&#2364;&#2366; &#2358;&#2381;&#2352;&#2375;&#2339;&#2368; - &#2350;&#2343;&#2381;&#2351; &#2346;&#2381;&#2352;&#2342;&#2375;&#2358; &#2324;&#2352; &#2331;&#2340;&#2381;&#2340;&#2368;&#2360;&#2327;&#2338;&#2364;&#2404; &#2325;&#2376;&#2350;&#2370;&#2352; &#2352;&#2375;&#2306;&#2332; - &#2350;&#2343;&#2381;&#2351; &#2346;&#2381;&#2352;&#2342;&#2375;&#2358;, &#2313;&#2340;&#2381;&#2340;&#2352; &#2346;&#2381;&#2352;&#2342;&#2375;&#2358;, &#2348;&#2367;&#2361;&#2366;&#2352;&#2404; &#2350;&#2361;&#2366;&#2342;&#2375;&#2357; &#2358;&#2381;&#2352;&#2375;&#2339;&#2368; - &#2350;&#2343;&#2381;&#2351; &#2346;&#2381;&#2352;&#2342;&#2375;&#2358;&#2404; &#2309;&#2332;&#2306;&#2340;&#2366; &#2352;&#2375;&#2306;&#2332; - &#2350;&#2361;&#2366;&#2352;&#2366;&#2359;&#2381;&#2335;&#2381;&#2352;&#2404; &#2352;&#2366;&#2332;&#2350;&#2361;&#2354; &#2346;&#2361;&#2366;&#2337;&#2364;&#2367;&#2351;&#2366;&#2305; - &#2333;&#2366;&#2352;&#2326;&#2339;&#2381;&#2337;&#2404; &#2350;&#2367;&#2325;&#2367;&#2352; &#2361;&#2367;&#2354;&#2381;&#2360; - &#2309;&#2360;&#2350;&#2404; &#2361;&#2352;&#2367;&#2358;&#2381;&#2330;&#2306;&#2342;&#2381;&#2352; &#2352;&#2375;&#2306;&#2332; - &#2350;&#2361;&#2366;&#2352;&#2366;&#2359;&#2381;&#2335;&#2381;&#2352;&#2404; &#2348;&#2366;&#2348;&#2366; &#2348;&#2369;&#2342;&#2344; &#2327;&#2367;&#2352;&#2367; &#2346;&#2361;&#2366;&#2337;&#2364;&#2367;&#2351;&#2366;&#2305; - &#2325;&#2352;&#2381;&#2344;&#2366;&#2335;&#2325;&#2404;</span></p>\\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "18",
                    question_en: "<p><span style=\"font-family: Roboto;\">8. </span><span style=\"font-family: Roboto;\">Vijayanagara </span><span style=\"font-family: Roboto;\">emperor</span><span style=\"font-family: Roboto;\"> Krishnadeva Raya founded a suburban township near </span><span style=\"font-family: Roboto;\">Vijayanagara</span><span style=\"font-family: Roboto;\"> called Nagalapuram after his__________..</span></p>\\n",
                    question_hi: "<p><span style=\"font-family: Roboto;\">8. </span><span style=\"font-family: Palanquin;\">&#2357;&#2367;&#2332;&#2351;&#2344;&#2327;&#2352; &#2325;&#2375; &#2360;&#2350;&#2381;&#2352;&#2366;&#2335; &#2325;&#2371;&#2359;&#2381;&#2339;&#2342;&#2375;&#2357; &#2352;&#2366;&#2351; &#2344;&#2375; &#2309;&#2346;&#2344;&#2368;/&#2309;&#2346;&#2344;&#2375; ________ &#2325;&#2375; &#2344;&#2366;&#2350; &#2346;&#2352; &#2357;&#2367;&#2332;&#2351;&#2344;&#2327;&#2352; &#2325;&#2375; &#2360;&#2350;&#2368;&#2346; </span><span style=\"font-family: Palanquin;\">&#2344;&#2327;&#2354;&#2346;&#2369;&#2352;&#2350; &#2344;&#2366;&#2350;&#2325; &#2319;&#2325; &#2313;&#2346;&#2344;&#2327;&#2352; &#2325;&#2368; &#2360;&#2381;&#2341;&#2366;&#2346;&#2344;&#2366; &#2325;&#2368; &#2341;&#2368;&#2404;</span></p>\\n",
                    options_en: ["<p>father</p>\\n", "<p>teacher</p>\\n", 
                                "<p>sister</p>\\n", "<p>mother</p>\\n"],
                    options_hi: ["<p>&#2346;&#2367;&#2340;&#2366;</p>\\n", "<p>&#2327;&#2369;&#2352;&#2369;</p>\\n",
                                "<p>&#2348;&#2361;&#2344;</p>\\n", "<p>&#2350;&#2366;&#2340;&#2366;</p>\\n"],
                    solution_en: "<p>8.(d)<strong> Mother</strong>. <span style=\"font-family: Roboto;\">Vijayanagara Empire (City of victory) : Founded in 1336 AD by Harihara and Bukka of the Sangama dynasty. Capital - Hampi (along the river Tungabhadra). Vijayanagar Empire was ruled by Sangama, Saluva, Tuluva, Aravidu Dynasties.<strong> </strong></span><strong><span style=\"font-family: Roboto;\">Krishnadevaraya</span></strong><span style=\"font-family: Roboto;\"><strong> :</strong> Ruler of the Tuluva dynasty of Vijayanagar empire (1509-29 AD), Book - &ldquo;Amuktamalyada&rdquo;.</span></p>\\n",
                    solution_hi: "<p>8.(d)<strong> &#2350;&#2366;&#2340;&#2366;</strong>&#2404; <span style=\"font-family: Palanquin;\">&#2357;&#2367;&#2332;&#2351;&#2344;&#2327;&#2352; &#2360;&#2366;&#2350;&#2381;&#2352;&#2366;&#2332;&#2381;&#2351; (&#2360;&#2367;&#2335;&#2368; &#2321;&#2398; &#2357;&#2367;&#2325;&#2381;&#2335;&#2381;&#2352;&#2368; ): 1336 &#2312;. &#2350;&#2375;&#2306; &#2360;&#2306;&#2327;&#2350; &#2357;&#2306;&#2358; &#2325;&#2375; &#2361;&#2352;&#2367;&#2361;&#2352; &#2324;&#2352; &#2348;&#2369;&#2325;&#2381;&#2325;&#2366; &#2342;&#2381;&#2357;&#2366;&#2352;&#2366; &#2360;&#2381;&#2341;&#2366;&#2346;&#2367;&#2340; &#2325;&#2367;&#2351;&#2366; &#2327;&#2351;&#2366; &#2341;&#2366; &#2404; &#2352;&#2366;&#2332;&#2343;&#2366;&#2344;&#2368; - &#2361;&#2350;&#2381;&#2346;&#2368; (&#2340;&#2369;&#2306;&#2327;&#2349;&#2342;&#2381;&#2352;&#2366; &#2344;&#2342;&#2368; &#2325;&#2375; &#2325;&#2367;&#2344;&#2366;&#2352;&#2375; &#2360;&#2381;&#2341;&#2367;&#2340; &#2361;&#2376; )&#2404; &#2357;&#2367;&#2332;&#2351;&#2344;&#2327;&#2352; &#2360;&#2366;&#2350;&#2381;&#2352;&#2366;&#2332;&#2381;&#2351; &#2346;&#2352; &#2360;&#2306;&#2327;&#2350;, &#2360;&#2366;&#2354;&#2369;&#2357;, &#2340;&#2369;&#2354;&#2369;&#2357;, &#2309;&#2352;&#2366;&#2357;&#2367;&#2342;&#2369; &#2352;&#2366;&#2332;&#2357;&#2306;&#2358;&#2379;&#2306; &#2325;&#2366; &#2358;&#2366;&#2360;&#2344; &#2341;&#2366;&#2404; </span><strong><span style=\"font-family: Palanquin;\">&#2325;&#2371;&#2359;&#2381;&#2339;&#2342;&#2375;&#2357;&#2352;&#2366;&#2351;</span></strong><span style=\"font-family: Palanquin;\"><strong>:</strong> &#2357;&#2367;&#2332;&#2351;&#2344;&#2327;&#2352; &#2360;&#2366;&#2350;&#2381;&#2352;&#2366;&#2332;&#2381;&#2351; &#2325;&#2375; &#2340;&#2369;&#2354;&#2369;&#2357; &#2357;&#2306;&#2358; &#2325;&#2375; &#2358;&#2366;&#2360;&#2325; (1509-29 &#2312;.), &#2346;&#2369;&#2360;&#2381;&#2340;&#2325; - &ldquo;&#2309;&#2350;&#2369;&#2325;&#2381;&#2340;&#2350;&#2366;&#2354;&#2381;&#2351;&#2342;&rdquo;&#2404;</span></p>\\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "18",
                    question_en: "<p><span style=\"font-family: Roboto;\">9. </span><span style=\"font-family: Roboto;\">How many nominated members are there in the Legislative Assembly of Telangana?</span></p>\\n",
                    question_hi: "<p><span style=\"font-family: Roboto;\">9. </span><span style=\"font-family: Palanquin;\">&#2340;&#2375;&#2354;&#2306;&#2327;&#2366;&#2344;&#2366; &#2357;&#2367;&#2343;&#2366;&#2344; &#2360;&#2349;&#2366; &#2350;&#2375;&#2306; &#2325;&#2367;&#2340;&#2344;&#2375; &#2350;&#2344;&#2379;&#2344;&#2368;&#2340; &#2360;&#2342;&#2360;&#2381;&#2351; &#2361;&#2376;&#2306;?</span></p>\\n",
                    options_en: ["<p>Four</p>\\n", "<p>One</p>\\n", 
                                "<p>Two</p>\\n", "<p>Three</p>\\n"],
                    options_hi: ["<p>&#2330;&#2366;&#2352;</p>\\n", "<p>&#2319;&#2325;</p>\\n",
                                "<p>&#2342;&#2379;</p>\\n", "<p>&#2340;&#2368;&#2344;</p>\\n"],
                    solution_en: "<p>9.(b)<strong> One</strong>. <span style=\"font-family: Roboto;\">Legislative Assembly (Lower house of the State Legislature) - Members of the Legislative Assembly are directly elected by the people for a term of 5 years. Minimum age to become a member of the legislative assembly is 25 years. Article 170 : Composition of the Legislative Assemblies in States. Seats of legislative assembly - Telangana (119), Andhra Pradesh (175), Maharashtra (288), Uttar Pradesh (403), Karnataka (224), Bihar (243).</span></p>\\n",
                    solution_hi: "<p>9.(b)<strong> &#2319;&#2325;</strong><span style=\"font-family: Palanquin;\"><strong>&#2404;</strong> &#2357;&#2367;&#2343;&#2366;&#2344; &#2360;&#2349;&#2366; (&#2352;&#2366;&#2332;&#2381;&#2351; &#2357;&#2367;&#2343;&#2366;&#2344;&#2350;&#2306;&#2337;&#2354; &#2325;&#2366; &#2344;&#2367;&#2330;&#2354;&#2366; &#2360;&#2342;&#2344;) - &#2357;&#2367;&#2343;&#2366;&#2344; &#2360;&#2349;&#2366; &#2325;&#2375; &#2360;&#2342;&#2360;&#2381;&#2351;&#2379;&#2306; &#2325;&#2379; 5 &#2360;&#2366;&#2354; &#2325;&#2368; &#2309;&#2357;&#2343;&#2367; &#2325;&#2375; &#2354;&#2367;&#2319; &#2360;&#2368;&#2343;&#2375; &#2354;&#2379;&#2327;&#2379;&#2306; &#2342;&#2381;&#2357;&#2366;&#2352;&#2366; &#2330;&#2369;&#2344;&#2366; &#2332;&#2366;&#2340;&#2366; &#2361;&#2376;&#2404; &#2357;&#2367;&#2343;&#2366;&#2344; &#2360;&#2349;&#2366; &#2325;&#2366; &#2360;&#2342;&#2360;&#2381;&#2351; &#2348;&#2344;&#2344;&#2375; &#2325;&#2375; &#2354;&#2367;&#2319; &#2344;&#2381;&#2351;&#2370;&#2344;&#2340;&#2350; &#2310;&#2351;&#2369; 25 &#2357;&#2352;&#2381;&#2359; &#2361;&#2376;&#2404; &#2309;&#2344;&#2369;&#2330;&#2381;&#2331;&#2375;&#2342; 170 : &#2352;&#2366;&#2332;&#2381;&#2351;&#2379;&#2306; &#2350;&#2375;&#2306; &#2357;&#2367;&#2343;&#2366;&#2344; &#2360;&#2349;&#2366;&#2323;&#2306; &#2325;&#2368; &#2360;&#2306;&#2352;&#2330;&#2344;&#2366;&#2404; &#2357;&#2367;&#2343;&#2366;&#2344; &#2360;&#2349;&#2366; &#2325;&#2368; &#2360;&#2368;&#2335;&#2375;&#2306; - &#2340;&#2375;&#2354;&#2306;&#2327;&#2366;&#2344;&#2366; (119), &#2310;&#2306;&#2343;&#2381;&#2352; &#2346;&#2381;&#2352;&#2342;&#2375;&#2358; (175), &#2350;&#2361;&#2366;&#2352;&#2366;&#2359;&#2381;&#2335;&#2381;&#2352; (288), &#2313;&#2340;&#2381;&#2340;&#2352; &#2346;&#2381;&#2352;&#2342;&#2375;&#2358; (403), &#2325;&#2352;&#2381;&#2344;&#2366;&#2335;&#2325; (224), &#2348;&#2367;&#2361;&#2366;&#2352; (243)&#2404;</span></p>\\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "18",
                    question_en: "<p><span style=\"font-family: Roboto;\">10. </span><span style=\"font-family: Roboto;\">On International Women\'s Day 2022, the Ministry of MSME launched an entrepreneurship drive for women by which of the following names?</span></p>\\n",
                    question_hi: "<p><span style=\"font-family: Roboto;\">10. </span><span style=\"font-family: Palanquin;\">&#2309;&#2306;&#2340;&#2352;&#2381;&#2352;&#2366;&#2359;&#2381;&#2335;&#2381;&#2352;&#2368;&#2351; &#2350;&#2361;&#2367;&#2354;&#2366; &#2342;&#2367;&#2357;&#2360; 2022 &#2346;&#2352;, &#2319;&#2350;&#2319;&#2360;&#2319;&#2350;&#2312; &#2350;&#2306;&#2340;&#2381;&#2352;&#2366;&#2354;&#2351; (Ministry of MSME) &#2344;&#2375; &#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340; &#2350;&#2375;&#2306; &#2360;&#2375; &#2325;&#2367;&#2360; &#2344;&#2366;&#2350; &#2360;&#2375; &#2350;&#2361;&#2367;&#2354;&#2366;&#2323;&#2306; &#2325;&#2375; &#2354;&#2367;&#2319; &#2319;&#2325; &#2313;&#2342;&#2381;&#2351;&#2350;&#2367;&#2340;&#2366; &#2309;&#2349;&#2367;&#2351;&#2366;&#2344; &#2325;&#2366; &#2358;&#2369;&#2349;&#2366;&#2352;&#2306;&#2349; &#2325;&#2367;&#2351;&#2366;?</span></p>\\n",
                    options_en: ["<p>SAMARTH</p>\\n", "<p>NIRBHAYA</p>\\n", 
                                "<p>SWADHAR</p>\\n", "<p>UJJAWALA</p>\\n"],
                    options_hi: ["<p><span style=\"font-weight: 400;\">&nbsp;&#2360;&#2350;&#2352;&#2381;&#2341; (SAMARTH)</span></p>\\n", "<p><span style=\"font-weight: 400;\">&#2344;&#2367;&#2352;&#2381;&#2349;&#2351;&#2366; (NIRBHAYA)</span></p>\\n",
                                "<p><span style=\"font-weight: 400;\">&nbsp;&#2360;&#2381;&#2357;&#2366;&#2343;&#2366;&#2352; (SWADHAR)</span></p>\\n", "<p><span style=\"font-weight: 400;\">&#2313;&#2332;&#2381;&zwj;&#2332;&#2381;&zwj;&#2357;&#2354;&#2366; (UJJAWALA)</span></p>\\n"],
                    solution_en: "<p><span style=\"font-family: Roboto;\">10</span><span style=\"font-family: Roboto;\">.(a)&nbsp;</span><strong>&nbsp;</strong><strong>SAMARTH - </strong><span style=\"font-weight: 400;\">It is a special Drive for registration of women-owned MSMEs under Udyam Registration. </span><strong>Swadhar Greh Scheme (2001)</strong><span style=\"font-weight: 400;\">: It envisages providing shelter, food, clothing and health as well as economic and social security for the women victims of difficult circumstances which includes widows, destitute women and aged women. </span><strong>Ujjawala Scheme (2007) :</strong><span style=\"font-weight: 400;\"> A comprehensive scheme for prevention of trafficking, with five specific components&ndash; Prevention, Rescue, Rehabilitation, Re-Integration and Repatriation of victims of trafficking.</span></p>\\n",
                    solution_hi: "<p><span style=\"font-family: Roboto;\">10</span><span style=\"font-family: Palanquin;\">.(a) </span><strong>&#2360;&#2350;&#2352;&#2381;&#2341;</strong><span style=\"font-weight: 400;\"> (SAMARTH)</span><strong> -</strong><span style=\"font-weight: 400;\"> &#2351;&#2361; &#2313;&#2342;&#2381;&#2351;&#2350; &#2346;&#2306;&#2332;&#2368;&#2325;&#2352;&#2339; &#2325;&#2375; &#2340;&#2361;&#2340; &#2350;&#2361;&#2367;&#2354;&#2366;&#2323;&#2306; &#2325;&#2375; &#2360;&#2381;&#2357;&#2366;&#2350;&#2367;&#2340;&#2381;&#2357; &#2357;&#2366;&#2354;&#2375; MSME &#2325;&#2375; &#2346;&#2306;&#2332;&#2368;&#2325;&#2352;&#2339; &#2325;&#2375; &#2354;&#2367;&#2319; &#2319;&#2325; &#2357;&#2367;&#2358;&#2375;&#2359; &#2309;&#2349;&#2367;&#2351;&#2366;&#2344; &#2361;&#2376;&#2404; </span><strong>&#2360;&#2381;&#2357;&#2366;&#2343;&#2366;&#2352; &#2327;&#2371;&#2361; &#2351;&#2379;&#2332;&#2344;&#2366; </strong><span style=\"font-weight: 400;\">(2001): &#2311;&#2360;&#2350;&#2375;&#2306; &#2325;&#2336;&#2367;&#2344; &#2346;&#2352;&#2367;&#2360;&#2381;&#2341;&#2367;&#2340;&#2367;&#2351;&#2379;&#2306; &#2360;&#2375; &#2346;&#2368;&#2337;&#2364;&#2367;&#2340; &#2350;&#2361;&#2367;&#2354;&#2366;&#2323;&#2306; &#2325;&#2379; &#2310;&#2358;&#2381;&#2352;&#2351;, &#2349;&#2379;&#2332;&#2344;, &#2325;&#2346;&#2337;&#2364;&#2375; &#2324;&#2352; &#2360;&#2381;&#2357;&#2366;&#2360;&#2381;&#2341;&#2381;&#2351; &#2325;&#2375; &#2360;&#2366;&#2341;-&#2360;&#2366;&#2341; &#2310;&#2352;&#2381;&#2341;&#2367;&#2325; &#2324;&#2352; &#2360;&#2366;&#2350;&#2366;&#2332;&#2367;&#2325; &#2360;&#2369;&#2352;&#2325;&#2381;&#2359;&#2366; &#2346;&#2381;&#2352;&#2342;&#2366;&#2344; &#2325;&#2352;&#2344;&#2375; &#2325;&#2368; &#2346;&#2352;&#2367;&#2325;&#2354;&#2381;&#2346;&#2344;&#2366; &#2325;&#2368; &#2327;&#2312; &#2361;&#2376;, &#2332;&#2367;&#2360;&#2350;&#2375;&#2306; &#2357;&#2367;&#2343;&#2357;&#2366;, &#2344;&#2367;&#2352;&#2366;&#2358;&#2381;&#2352;&#2367;&#2340; &#2350;&#2361;&#2367;&#2354;&#2366;&#2319;&#2306; &#2324;&#2352; &#2357;&#2371;&#2342;&#2381;&#2343;&#2366; &#2350;&#2361;&#2367;&#2354;&#2366;&#2319;&#2306; &#2358;&#2366;&#2350;&#2367;&#2354; &#2361;&#2376;&#2306;&#2404; </span><strong>&#2313;&#2332;&#2381;&#2332;&#2381;&#2357;&#2354;&#2366; &#2351;&#2379;&#2332;&#2344;&#2366; </strong><span style=\"font-weight: 400;\">(2007)</span><strong>:</strong><span style=\"font-weight: 400;\"> &#2340;&#2360;&#2381;&#2325;&#2352;&#2368; &#2325;&#2368; &#2352;&#2379;&#2325;&#2341;&#2366;&#2350; &#2325;&#2375; &#2354;&#2367;&#2319; &#2319;&#2325; &#2357;&#2381;&#2351;&#2366;&#2346;&#2325; &#2351;&#2379;&#2332;&#2344;&#2366;, &#2332;&#2367;&#2360;&#2350;&#2375;&#2306; &#2346;&#2366;&#2306;&#2330; &#2357;&#2367;&#2358;&#2367;&#2359;&#2381;&#2335; &#2328;&#2335;&#2325; &#2361;&#2376;&#2306;- &#2352;&#2379;&#2325;&#2341;&#2366;&#2350;, &#2348;&#2330;&#2366;&#2357;, &#2346;&#2369;&#2344;&#2352;&#2381;&#2357;&#2366;&#2360;, &#2340;&#2360;&#2381;&#2325;&#2352;&#2368; &#2325;&#2375; &#2346;&#2368;&#2337;&#2364;&#2367;&#2340;&#2379;&#2306; &#2325;&#2366; &#2346;&#2369;&#2344;: &#2319;&#2325;&#2368;&#2325;&#2352;&#2339; &#2324;&#2352; &#2346;&#2381;&#2352;&#2340;&#2381;&#2351;&#2366;&#2357;&#2352;&#2381;&#2340;&#2344;&#2404;</span></p>\\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "18",
                    question_en: "<p><span style=\"font-family: Roboto;\">11. </span><span style=\"font-family: Roboto;\">What are the main energy sources for earth\'s internal heat engine?</span><span style=\"font-family: Roboto;\"> </span></p>\\n",
                    question_hi: "<p><span style=\"font-family: Roboto;\">11. </span><span style=\"font-family: Palanquin;\">&#2346;&#2371;&#2341;&#2381;&#2357;&#2368; &#2325;&#2375; &#2310;&#2306;&#2340;&#2352;&#2367;&#2325; &#2314;&#2359;&#2381;&#2350;&#2366; &#2311;&#2306;&#2332;&#2344; &#2325;&#2375; &#2354;&#2367;&#2319; &#2350;&#2369;&#2326;&#2381;&#2351; &#2314;&#2352;&#2381;&#2332;&#2366; &#2360;&#2381;&#2352;&#2379;&#2340; &#2325;&#2381;&#2351;&#2366; &#2361;&#2376;&#2306;?</span></p>\\n",
                    options_en: ["<p>Radiogenic heat and oceanic tide heat</p>\\n", "<p>Heat from volcanoes and solar heat</p>\\n", 
                                "<p>Solar heat and oceanic tide heat</p>\\n", "<p>Radiogenic heat and primordial heat</p>\\r\\n<p><span style=\"font-family: Roboto;\"> </span></p>\\n"],
                    options_hi: ["<p><span style=\"font-weight: 400;\">&#2352;&#2375;&#2337;&#2367;&#2351;&#2379;&#2332;&#2344;&#2367;&#2340; &#2314;&#2359;&#2381;&#2350;&#2366; &#2324;&#2352; &#2350;&#2361;&#2366;&#2360;&#2366;&#2327;&#2352;&#2368;&#2351; &#2332;&#2381;&#2357;&#2366;&#2352;&#2368;&#2351; &#2314;&#2359;&#2381;&#2350;&#2366; (Radiogenic heat and oceanic tide heat)</span></p>\\n", "<p><span style=\"font-weight: 400;\">&#2332;&#2381;&#2357;&#2366;&#2354;&#2366;&#2350;&#2369;&#2326;&#2368;&#2351; &#2314;&#2359;&#2381;&#2350;&#2366; &#2324;&#2352; &#2360;&#2380;&#2352; &#2314;&#2359;&#2381;&#2350;&#2366; (Heat from volcanoes and solar heat)</span></p>\\n",
                                "<p><span style=\"font-weight: 400;\">&#2360;&#2380;&#2352; &#2314;&#2359;&#2381;&#2350;&#2366; &#2324;&#2352; &#2350;&#2361;&#2366;&#2360;&#2366;&#2327;&#2352;&#2368;&#2351; &#2332;&#2381;&#2357;&#2366;&#2352;&#2368;&#2351; &#2314;&#2359;&#2381;&#2350;&#2366; (Solar heat and oceanic tide heat)</span></p>\\n", "<p><span style=\"font-weight: 400;\">&#2352;&#2375;&#2337;&#2367;&#2351;&#2379;&#2332;&#2344;&#2367;&#2340; &#2314;&#2359;&#2381;&#2350;&#2366; &#2324;&#2352; &#2346;&#2381;&#2352;&#2366;&#2352;&#2306;&#2349;&#2367;&#2325; &#2314;&#2359;&#2381;&#2350;&#2366; (Radiogenic heat and primordial heat)</span></p>\\n"],
                    solution_en: "<p><span style=\"font-family: Roboto;\">11</span><span style=\"font-family: Roboto;\">.(d) <strong>Radiogenic heat and primordial heat.</strong></span><span style=\"font-family: Roboto;\"><strong> </strong>The Earth\'s heat is the internal heat that arises from two sources : The decay of radioactive isotopes in crustal rocks and the mantle, and primordial heat left over from the planet\'s fiery formation. Geothermal energy is the heat produced deep in the Earth\'s core. Geothermal energy is a clean, renewable resource that can be harnessed for use as heat and electricity produced.</span></p>\\n",
                    solution_hi: "<p><span style=\"font-family: Roboto;\">11</span><span style=\"font-family: Palanquin;\">.(d) <strong>&#2352;&#2375;&#2337;&#2367;&#2351;&#2379;&#2332;&#2344;&#2367;&#2340; &#2314;&#2359;&#2381;&#2350;&#2366; &#2324;&#2352; &#2346;&#2381;&#2352;&#2366;&#2352;&#2306;&#2349;&#2367;&#2325; &#2314;&#2359;&#2381;&#2350;&#2366;</strong> </span><span style=\"font-family: Palanquin;\">(Radiogenic heat and primordial heat)&#2404; &#2346;&#2371;&#2341;&#2381;&#2357;&#2368; &#2325;&#2368; &#2314;&#2359;&#2381;&#2350;&#2366; &#2310;&#2306;&#2340;&#2352;&#2367;&#2325; &#2314;&#2359;&#2381;&#2350;&#2366; &#2361;&#2376; &#2332;&#2379; &#2342;&#2379; &#2360;&#2381;&#2352;&#2379;&#2340;&#2379;&#2306; &#2360;&#2375; &#2313;&#2340;&#2381;&#2346;&#2344;&#2381;&#2344; &#2361;&#2379;&#2340;&#2368; &#2361;&#2376;: &#2325;&#2381;&#2352;&#2360;&#2381;&#2335;&#2354; &#2330;&#2335;&#2381;&#2335;&#2366;&#2344;&#2379;&#2306; &#2324;&#2352; &#2350;&#2375;&#2306;&#2335;&#2354; &#2350;&#2375;&#2306; &#2352;&#2375;&#2337;&#2367;&#2351;&#2379;&#2343;&#2352;&#2381;&#2350;&#2368; &#2310;&#2311;&#2360;&#2379;&#2335;&#2379;&#2346; &#2325;&#2375; &#2325;&#2381;&#2359;&#2351; &#2360;&#2375;, &#2324;&#2352; &#2327;&#2381;&#2352;&#2361; &#2325;&#2375; &#2346;&#2381;&#2352;&#2332;&#2381;&#2357;&#2354;&#2367;&#2340; &#2360;&#2306;&#2352;&#2330;&#2344;&#2366; &#2360;&#2375; &#2348;&#2330;&#2368; &#2361;&#2369;&#2312; &#2346;&#2381;&#2352;&#2366;&#2352;&#2306;&#2349;&#2367;&#2325; &#2314;&#2359;&#2381;&#2350;&#2366;&#2404; &#2349;&#2370;&#2340;&#2366;&#2346;&#2368;&#2351; &#2314;&#2352;&#2381;&#2332;&#2366; &#2346;&#2371;&#2341;&#2381;&#2357;&#2368; &#2325;&#2368; &#2327;&#2361;&#2352;&#2366;&#2312; &#2350;&#2375;&#2306; &#2313;&#2340;&#2381;&#2346;&#2344;&#2381;&#2344; &#2361;&#2379;&#2344;&#2375; &#2357;&#2366;&#2354;&#2368; &#2314;&#2359;&#2381;&#2350;&#2366; &#2361;&#2376;&#2404; &#2349;&#2370;&#2340;&#2366;&#2346;&#2368;&#2351; &#2314;&#2352;&#2381;&#2332;&#2366; &#2319;&#2325; &#2360;&#2381;&#2357;&#2330;&#2381;&#2331;, &#2344;&#2357;&#2368;&#2325;&#2352;&#2339;&#2368;&#2351; &#2360;&#2306;&#2360;&#2366;&#2343;&#2344; &#2361;&#2376; &#2332;&#2367;&#2360;&#2325;&#2366; &#2313;&#2346;&#2351;&#2379;&#2327; &#2340;&#2366;&#2346; &#2324;&#2352; &#2357;&#2367;&#2342;&#2381;&#2351;&#2369;&#2340; &#2313;&#2340;&#2381;&#2346;&#2366;&#2342;&#2344; &#2325;&#2375; &#2352;&#2370;&#2346; &#2350;&#2375;&#2306; &#2325;&#2367;&#2351;&#2366; &#2332;&#2366; &#2360;&#2325;&#2340;&#2366; &#2361;&#2376;&#2404;</span></p>\\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "18",
                    question_en: "<p><span style=\"font-family: Roboto;\">12. </span><span style=\"font-family: Roboto;\">Select the correct statement about the achievements of Sruti Bandyopadhyay, an eminent Manipuri dancer.</span></p>\\n",
                    question_hi: "<p><span style=\"font-family: Roboto;\">12. </span><span style=\"font-family: Palanquin;\">&#2346;&#2381;&#2352;&#2360;&#2367;&#2342;&#2381;&#2343; &#2350;&#2339;&#2367;&#2346;&#2369;&#2352;&#2368; &#2344;&#2371;&#2340;&#2381;&#2351;&#2366;&#2306;&#2327;&#2344;&#2366;, &#2358;&#2381;&#2352;&#2369;&#2340;&#2367; &#2348;&#2306;&#2342;&#2379;&#2346;&#2366;&#2343;&#2381;&#2351;&#2366;&#2351; &#2325;&#2368; &#2313;&#2346;&#2354;&#2348;&#2381;&#2343;&#2367;&#2351;&#2379;&#2306; &#2325;&#2375; &#2360;&#2306;&#2342;&#2352;&#2381;&#2349; &#2350;&#2375;&#2306; &#2360;&#2361;&#2368; &#2325;&#2341;&#2344; &#2325;&#2366; &#2330;&#2351;&#2344; &#2325;&#2352;&#2375;&#2306;&#2404;</span></p>\\n",
                    options_en: ["<p>She received Padma Shri for the year 2020.</p>\\n", "<p>She received Sangeet Natak Akademi Award for the year 2020.</p>\\n", 
                                "<p>She received Kalidas Samman for the year 2020.</p>\\n", "<p>She received Padma Vibhushan for the year 2020.</p>\\n"],
                    options_hi: ["<p>&#2313;&#2344;&#2381;&#2361;&#2375;&#2306; &#2357;&#2352;&#2381;&#2359; 2020 &#2325;&#2375; &#2354;&#2367;&#2319; &#2346;&#2342;&#2381;&#2350; &#2358;&#2381;&#2352;&#2368; &#2350;&#2367;&#2354;&#2366;&#2404;</p>\\n", "<p>&#2313;&#2344;&#2381;&#2361;&#2375;&#2306; &#2357;&#2352;&#2381;&#2359; 2020 &#2325;&#2366; &#2360;&#2306;&#2327;&#2368;&#2340; &#2344;&#2366;&#2335;&#2325; &#2309;&#2325;&#2366;&#2342;&#2350;&#2368; &#2346;&#2369;&#2352;&#2360;&#2381;&#2325;&#2366;&#2352; &#2350;&#2367;&#2354;&#2366;&#2404;</p>\\n",
                                "<p>&#2313;&#2344;&#2381;&#2361;&#2375;&#2306; &#2357;&#2352;&#2381;&#2359; 2020 &#2325;&#2375; &#2354;&#2367;&#2319; &#2325;&#2366;&#2354;&#2367;&#2342;&#2366;&#2360; &#2360;&#2350;&#2381;&#2350;&#2366;&#2344; &#2350;&#2367;&#2354;&#2366;&#2404;</p>\\n", "<p>&#2313;&#2344;&#2381;&#2361;&#2375;&#2306; &#2357;&#2352;&#2381;&#2359; 2020 &#2325;&#2375; &#2354;&#2367;&#2319; &#2346;&#2342;&#2381;&#2350; &#2357;&#2367;&#2349;&#2370;&#2359;&#2339; &#2350;&#2367;&#2354;&#2366;&#2404;</p>\\n"],
                    solution_en: "<p><span style=\"font-family: Roboto;\">12</span><span style=\"font-family: Roboto;\">.(b)</span><span style=\"font-family: Roboto;\">&nbsp;</span><span style=\"font-weight: 400;\">&nbsp;Other Dance forms and their dancers : </span><strong>Manipuri</strong><span style=\"font-weight: 400;\"> - Nayana, Suverna, Ranjana, and Darshana, Guru Bipin Singh, Amala Shankar. </span><strong>Odissi</strong><span style=\"font-weight: 400;\"> - Guru Pankaj Charan Das, Sonal Mansingh, Kelucharan Mohapatra, Geeta Mahalik, Dr. Minati Mishra, Madhavi Mudgal. </span><strong>Sattriya</strong><span style=\"font-weight: 400;\"> - Ghanakanta Bora, Jatin Goswami, Srimanta Sankardev.</span></p>\\n",
                    solution_hi: "<p><span style=\"font-family: Roboto;\">12</span><span style=\"font-family: Roboto;\">.(b)</span><span style=\"font-family: Palanquin;\"> <span style=\"font-weight: 400;\">&#2309;&#2344;&#2381;&#2351; &#2344;&#2371;&#2340;&#2381;&#2351; &#2352;&#2370;&#2346; &#2324;&#2352; &#2313;&#2344;&#2325;&#2375; &#2344;&#2352;&#2381;&#2340;&#2325;:</span><strong> &#2350;&#2339;&#2367;&#2346;&#2369;&#2352;&#2368; </strong><span style=\"font-weight: 400;\">- &#2344;&#2351;&#2344;&#2366;, &#2360;&#2369;&#2357;&#2352;&#2381;&#2339;&#2366;, &#2352;&#2306;&#2332;&#2344;&#2366;, &#2324;&#2352; &#2342;&#2352;&#2381;&#2358;&#2344;, &#2327;&#2369;&#2352;&#2369; &#2348;&#2367;&#2346;&#2367;&#2344; &#2360;&#2367;&#2306;&#2361;, &#2309;&#2350;&#2354;&#2366; &#2358;&#2306;&#2325;&#2352;&#2404; </span><strong>&#2323;&#2337;&#2367;&#2360;&#2368; </strong><span style=\"font-weight: 400;\">- &#2327;&#2369;&#2352;&#2369; &#2346;&#2306;&#2325;&#2332; &#2330;&#2352;&#2339; &#2342;&#2366;&#2360;, &#2360;&#2379;&#2344;&#2354; &#2350;&#2366;&#2344;&#2360;&#2367;&#2306;&#2361;, &#2325;&#2375;&#2354;&#2369;&#2330;&#2352;&#2339; &#2350;&#2361;&#2366;&#2346;&#2366;&#2340;&#2381;&#2352;, &#2327;&#2368;&#2340;&#2366; &#2350;&#2361;&#2366;&#2354;&#2367;&#2325;, &#2337;&#2377;. &#2350;&#2367;&#2344;&#2340;&#2368; &#2350;&#2367;&#2358;&#2381;&#2352;&#2366;, &#2350;&#2366;&#2343;&#2357;&#2368; &#2350;&#2369;&#2342;&#2381;&#2327;&#2354;&#2404; </span><strong>&#2360;&#2340;&#2381;&#2340;&#2381;&#2352;&#2367;&#2351;&#2366; </strong><span style=\"font-weight: 400;\">- &#2328;&#2344;&#2325;&#2366;&#2306;&#2340; &#2348;&#2379;&#2352;&#2366;, &#2332;&#2340;&#2367;&#2344; &#2327;&#2379;&#2360;&#2381;&#2357;&#2366;&#2350;&#2368;, &#2358;&#2381;&#2352;&#2368;&#2350;&#2306;&#2340; &#2358;&#2306;&#2325;&#2352;&#2342;&#2375;&#2357;&#2404;</span></span></p>\\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "18",
                    question_en: "<p><span style=\"font-family: Roboto;\">13. </span><span style=\"font-family: Roboto;\">Which of the given personalities is NOT associated with Carnatic Music?</span></p>\\n",
                    question_hi: "<p><span style=\"font-family: Roboto;\">13. </span><span style=\"font-family: Palanquin;\">&#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340; &#2350;&#2375;&#2306; &#2360;&#2375; &#2325;&#2380;&#2344; &#2360;&#2368; &#2358;&#2326;&#2381;&#2360;&#2367;&#2351;&#2340; &#2325;&#2352;&#2381;&#2344;&#2366;&#2335;&#2325; &#2360;&#2306;&#2327;&#2368;&#2340; &#2360;&#2375; &#2360;&#2306;&#2348;&#2306;&#2343;&#2367;&#2340; &#2344;&#2361;&#2368;&#2306; &#2361;&#2376;?</span></p>\\n",
                    options_en: ["<p>Kalyani Varadarajan</p>\\n", "<p>MS Subbulakshmi</p>\\n", 
                                "<p>Vishnu Narayan Bhatkhande</p>\\n", "<p>Subramania Bharathiyar</p>\\n"],
                    options_hi: ["<p>&#2325;&#2354;&#2381;&#2351;&#2366;&#2339;&#2368; &#2357;&#2352;&#2342;&#2352;&#2366;&#2332;&#2344;</p>\\n", "<p>&#2319;&#2350;. &#2319;&#2360;. &#2360;&#2369;&#2348;&#2381;&#2348;&#2369;&#2354;&#2325;&#2381;&#2359;&#2381;&#2350;&#2368;</p>\\n",
                                "<p>&#2357;&#2367;&#2359;&#2381;&#2339;&#2369; &#2344;&#2366;&#2352;&#2366;&#2351;&#2339; &#2349;&#2366;&#2340;&#2326;&#2306;&#2337;&#2375;</p>\\n", "<p>&#2360;&#2369;&#2348;&#2381;&#2352;&#2361;&#2381;&#2350;&#2339;&#2381;&#2351; &#2349;&#2352;&#2340;&#2367;&#2351;&#2366;&#2352;</p>\\n"],
                    solution_en: "<p><span style=\"font-family: Roboto;\">13</span><span style=\"font-family: Roboto;\">.(c) <strong>Vishnu Narayan Bhatkhande</strong></span><span style=\"font-family: Roboto;\"><strong> </strong>- He was related to Hindustani classical music. Other personalities related to Carnatic music - Purandaradasa (Father of Carnatic music), Kanaka Dasa, Muthu Thandavar, Tyagaraja, Muthuswami Dikshitar, Syama Sastri, Aruna Sairam, Semmangudi Srinivasa Iyer, Lalgudi Jayaraman.</span></p>\\n",
                    solution_hi: "<p><span style=\"font-family: Roboto;\">13</span><span style=\"font-family: Palanquin;\">.(c) <strong>&#2357;&#2367;&#2359;&#2381;&#2339;&#2369; &#2344;&#2366;&#2352;&#2366;&#2351;&#2339; &#2349;&#2366;&#2340;&#2326;&#2306;&#2337;&#2375; </strong></span><span style=\"font-family: Palanquin;\">- &#2311;&#2344;&#2325;&#2366; &#2360;&#2306;&#2348;&#2306;&#2343; &#2361;&#2367;&#2306;&#2342;&#2369;&#2360;&#2381;&#2340;&#2366;&#2344;&#2368; &#2358;&#2366;&#2360;&#2381;&#2340;&#2381;&#2352;&#2368;&#2351; &#2360;&#2306;&#2327;&#2368;&#2340; &#2360;&#2375; &#2341;&#2366;&#2404; &#2325;&#2352;&#2381;&#2344;&#2366;&#2335;&#2325; &#2360;&#2306;&#2327;&#2368;&#2340; &#2360;&#2375; &#2360;&#2306;&#2348;&#2306;&#2343;&#2367;&#2340; &#2309;&#2344;&#2381;&#2351; &#2361;&#2360;&#2381;&#2340;&#2367;&#2351;&#2366;&#2305; - &#2346;&#2369;&#2352;&#2306;&#2342;&#2352;&#2342;&#2366;&#2360; (&#2325;&#2352;&#2381;&#2344;&#2366;&#2335;&#2325; &#2360;&#2306;&#2327;&#2368;&#2340; &#2325;&#2375; &#2332;&#2344;&#2325;), &#2325;&#2344;&#2325; &#2342;&#2366;&#2360;, &#2350;&#2369;&#2341;&#2369; &#2341;&#2306;&#2337;&#2366;&#2357;&#2352;, &#2340;&#2381;&#2351;&#2366;&#2327;&#2352;&#2366;&#2332;, &#2350;&#2369;&#2341;&#2369;&#2360;&#2381;&#2357;&#2366;&#2350;&#2368; &#2342;&#2368;&#2325;&#2381;&#2359;&#2367;&#2340;&#2352;, &#2358;&#2381;&#2351;&#2366;&#2350;&#2366; &#2358;&#2366;&#2360;&#2381;&#2340;&#2381;&#2352;&#2368;, &#2309;&#2352;&#2369;&#2339;&#2366; &#2360;&#2366;&#2312;&#2352;&#2366;&#2350;, &#2360;&#2375;&#2350;&#2381;&#2350;&#2344;&#2327;&#2369;&#2337;&#2368; &#2358;&#2381;&#2352;&#2368;&#2344;&#2367;&#2357;&#2366;&#2360; &#2309;&#2351;&#2381;&#2351;&#2352;, &#2354;&#2366;&#2354;&#2327;&#2369;&#2337;&#2368; &#2332;&#2351;&#2366;&#2352;&#2350;&#2344;&#2404;</span></p>\\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "18",
                    question_en: "<p><span style=\"font-family: Roboto;\">14. </span><span style=\"font-family: Roboto;\">Which of the following Acts was introduced to regulate Foreign Exchange in India in 1973?</span></p>\\n",
                    question_hi: "<p><span style=\"font-family: Roboto;\">14.</span><span style=\"font-family: Palanquin;\"> 1973 &#2350;&#2375;&#2306; &#2349;&#2366;&#2352;&#2340; &#2350;&#2375;&#2306; &#2357;&#2367;&#2342;&#2375;&#2358;&#2368; &#2350;&#2369;&#2342;&#2381;&#2352;&#2366; &#2325;&#2379; &#2357;&#2367;&#2344;&#2367;&#2351;&#2350;&#2367;&#2340; &#2325;&#2352;&#2344;&#2375; &#2325;&#2375; &#2354;&#2367;&#2319; &#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340; &#2350;&#2375;&#2306; &#2360;&#2375; &#2325;&#2380;&#2344; &#2360;&#2366; &#2309;&#2343;&#2367;&#2344;&#2367;&#2351;&#2350; &#2346;&#2375;&#2358; &#2325;&#2367;&#2351;&#2366; &#2327;&#2351;&#2366; &#2341;&#2366;?</span></p>\\n",
                    options_en: ["<p>FERA</p>\\n", "<p>FEMA</p>\\n", 
                                "<p>FRBM</p>\\n", "<p>SARFESI</p>\\n"],
                    options_hi: ["<p><span style=\"font-weight: 400;\">&#2347;&#2375;&#2352;&#2366; (FERA)</span></p>\\n", "<p>&#2347;&#2375;&#2350;&#2366; (FEMA)</p>\\n",
                                "<p><span style=\"font-weight: 400;\">&#2319;&#2347;&#2310;&#2352;&#2348;&#2368;&#2319;&#2350; (FRBM)</span></p>\\n", "<p><span style=\"font-weight: 400;\">&#2360;&#2352;&#2347;&#2375;&#2360;&#2368; (SARFESI)</span></p>\\n"],
                    solution_en: "<p><span style=\"font-family: Roboto;\">14</span><span style=\"font-family: Roboto;\">.(a)</span><strong>&nbsp;FERA </strong><span style=\"font-weight: 400;\">(Foreign Exchange Regulation Act)</span><strong> -</strong><span style=\"font-weight: 400;\"> It came into effect on 01 January 1974. This act was passed to regulate and monitor foreign securities and exchange transactions. It was repealed and replaced by a new law - the Foreign Exchange Management Act, 1999 (FEMA) with effect from June 1, 2000. </span><strong>FRBM</strong><span style=\"font-weight: 400;\"> - Fiscal Responsibility &amp; Budget Management. </span><strong>SARFESI</strong><span style=\"font-weight: 400;\"> - The Securitisation and Reconstruction of Financial Assets and Enforcement of Security Interest Act, 2002.</span></p>\\n",
                    solution_hi: "<p><span style=\"font-family: Roboto;\">14</span><span style=\"font-family: Roboto;\">.(a)&nbsp;</span><strong>FERA </strong><span style=\"font-weight: 400;\">(&#2357;&#2367;&#2342;&#2375;&#2358;&#2368; &#2350;&#2369;&#2342;&#2381;&#2352;&#2366; &#2357;&#2367;&#2344;&#2367;&#2351;&#2350;&#2344; &#2309;&#2343;&#2367;&#2344;&#2367;&#2351;&#2350;) - &#2351;&#2361; 01 &#2332;&#2344;&#2357;&#2352;&#2368; 1974 &#2325;&#2379; &#2354;&#2366;&#2327;&#2370; &#2361;&#2369;&#2310;&#2404; &#2351;&#2361; &#2309;&#2343;&#2367;&#2344;&#2367;&#2351;&#2350; &#2357;&#2367;&#2342;&#2375;&#2358;&#2368; &#2346;&#2381;&#2352;&#2340;&#2367;&#2349;&#2370;&#2340;&#2367;&#2351;&#2379;&#2306; &#2324;&#2352; &#2357;&#2367;&#2344;&#2367;&#2350;&#2351; &#2354;&#2375;&#2344;&#2342;&#2375;&#2344; &#2325;&#2379; &#2357;&#2367;&#2344;&#2367;&#2351;&#2350;&#2367;&#2340; &#2324;&#2352; &#2344;&#2367;&#2327;&#2352;&#2366;&#2344;&#2368; &#2325;&#2352;&#2344;&#2375; &#2325;&#2375; &#2354;&#2367;&#2319; &#2346;&#2366;&#2352;&#2367;&#2340; &#2325;&#2367;&#2351;&#2366; &#2327;&#2351;&#2366; &#2341;&#2366;&#2404; &#2311;&#2360;&#2375; &#2344;&#2367;&#2352;&#2360;&#2381;&#2340; &#2325;&#2352; &#2342;&#2367;&#2351;&#2366; &#2327;&#2351;&#2366; &#2324;&#2352; &#2319;&#2325; &#2344;&#2319; &#2325;&#2366;&#2344;&#2370;&#2344; &#2342;&#2381;&#2357;&#2366;&#2352;&#2366; &#2346;&#2381;&#2352;&#2340;&#2367;&#2360;&#2381;&#2341;&#2366;&#2346;&#2367;&#2340; &#2325;&#2367;&#2351;&#2366; &#2327;&#2351;&#2366; - &#2357;&#2367;&#2342;&#2375;&#2358;&#2368; &#2350;&#2369;&#2342;&#2381;&#2352;&#2366; &#2346;&#2381;&#2352;&#2348;&#2306;&#2343;&#2344; &#2309;&#2343;&#2367;&#2344;&#2367;&#2351;&#2350;, 1999 (&#2347;&#2375;&#2350;&#2366;) - 1 &#2332;&#2370;&#2344; 2000 &#2360;&#2375; &#2346;&#2381;&#2352;&#2349;&#2366;&#2357;&#2368;&#2404; </span><strong>FRBM</strong><span style=\"font-weight: 400;\"> - &#2352;&#2366;&#2332;&#2325;&#2379;&#2359;&#2368;&#2351; &#2313;&#2340;&#2381;&#2340;&#2352;&#2342;&#2366;&#2351;&#2367;&#2340;&#2381;&#2357; &#2319;&#2357;&#2306; &#2348;&#2332;&#2335; &#2346;&#2381;&#2352;&#2348;&#2306;&#2343;&#2344;&#2404;</span><strong> SARFESI</strong><span style=\"font-weight: 400;\"> - &#2357;&#2367;&#2340;&#2381;&#2340;&#2368;&#2351; &#2360;&#2306;&#2346;&#2340;&#2381;&#2340;&#2367;&#2351;&#2379;&#2306; &#2325;&#2366; &#2346;&#2381;&#2352;&#2340;&#2367;&#2349;&#2370;&#2340;&#2367;&#2325;&#2352;&#2339; &#2324;&#2352; &#2346;&#2369;&#2344;&#2352;&#2381;&#2344;&#2367;&#2352;&#2381;&#2350;&#2366;&#2339; &#2324;&#2352; &#2360;&#2369;&#2352;&#2325;&#2381;&#2359;&#2366; &#2361;&#2367;&#2340; &#2325;&#2366; &#2346;&#2381;&#2352;&#2357;&#2352;&#2381;&#2340;&#2344; &#2309;&#2343;&#2367;&#2344;&#2367;&#2351;&#2350;, 2002&#2404;</span></p>\\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "18",
                    question_en: "<p><span style=\"font-family: Roboto;\">15. </span><span style=\"font-family: Roboto;\">Which of the following dances is performed by the Santhal tribe of Jharkhand?</span></p>\\n",
                    question_hi: "<p><span style=\"font-family: Roboto;\">15. </span><span style=\"font-family: Palanquin;\">&#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340; &#2350;&#2375;&#2306; &#2360;&#2375; &#2333;&#2366;&#2352;&#2326;&#2306;&#2337; &#2325;&#2368; &#2360;&#2306;&#2341;&#2366;&#2354; &#2332;&#2344;&#2332;&#2366;&#2340;&#2367; &#2342;&#2381;&#2357;&#2366;&#2352;&#2366; &#2325;&#2380;&#2344;-&#2360;&#2366; &#2344;&#2371;&#2340;&#2381;&#2351; &#2325;&#2367;&#2351;&#2366; &#2332;&#2366;&#2340;&#2366; &#2361;&#2376;?</span></p>\\n",
                    options_en: ["<p>Jhika <span style=\"font-family: Roboto;\">Dasain</span></p>\\n", "<p>Kolkali</p>\\n", 
                                "<p>Ghumar</p>\\n", "<p>Koli</p>\\n"],
                    options_hi: ["<p>&#2333;&#2367;&#2325;&#2366; &#2342;&#2360;&#2366;&#2312;</p>\\n", "<p>&#2325;&#2379;&#2354;&#2325;&#2354;&#2368;</p>\\n",
                                "<p>&#2328;&#2370;&#2350;&#2352;</p>\\n", "<p>&#2325;&#2379;&#2354;&#2368;</p>\\n"],
                    solution_en: "<p><span style=\"font-family: Roboto;\">15</span><span style=\"font-family: Roboto;\">.(a) <strong>Jhika Dashain. </strong></span><span style=\"font-family: Roboto;\">It is performed a few days before Dussehra.</span><span style=\"font-family: Roboto;\"> </span><span style=\"font-family: Roboto;\">States and their Folk dances</span><span style=\"font-family: Roboto;\"> - </span><span style=\"font-family: Roboto;\">Jharkhand : Alkap, Karma Munda, Agni, Jhumar, Janani Jhumar, Mardana Jhumar, Paika, Phagua. </span><strong><span style=\"font-family: Roboto;\">Bihar </span></strong><span style=\"font-family: Roboto;\">: Jata-Jatin, Bakho-Bakhain, Panwariya. </span><strong><span style=\"font-family: Roboto;\">Chhattisgarh</span></strong><span style=\"font-family: Roboto;\"><strong> : </strong>Gaur Maria, Panthi, Raut Nacha, Pandwani, Vedamati, Kapalik.</span></p>\\n",
                    solution_hi: "<p><span style=\"font-family: Roboto;\">15</span><span style=\"font-family: Palanquin;\">.(a)&nbsp;</span><strong>&#2333;&#2367;&#2325;&#2366; &#2342;&#2360;&#2366;&#2312;&#2404; </strong><span style=\"font-weight: 400;\">&#2311;&#2360;&#2325;&#2366; &#2310;&#2351;&#2379;&#2332;&#2344; &#2342;&#2358;&#2361;&#2352;&#2375; &#2360;&#2375; &#2325;&#2369;&#2331; &#2342;&#2367;&#2344; &#2346;&#2361;&#2354;&#2375; &#2325;&#2367;&#2351;&#2366; &#2332;&#2366;&#2340;&#2366; &#2361;&#2376;&#2404; </span><strong>&#2352;&#2366;&#2332;&#2381;&#2351; &#2324;&#2352; &#2313;&#2344;&#2325;&#2375; &#2354;&#2379;&#2325; &#2344;&#2371;&#2340;&#2381;&#2351;</strong><span style=\"font-weight: 400;\"> - </span><strong>&#2333;&#2366;&#2352;&#2326;&#2306;&#2337;</strong><span style=\"font-weight: 400;\">: &#2309;&#2354;&#2325;&#2346;, &#2325;&#2352;&#2350;&#2366; &#2350;&#2369;&#2306;&#2337;&#2366;, &#2309;&#2327;&#2381;&#2344;&#2367;, &#2333;&#2370;&#2350;&#2352;, &#2332;&#2344;&#2344;&#2368; &#2333;&#2370;&#2350;&#2352;, &#2350;&#2352;&#2381;&#2342;&#2366;&#2344;&#2366; &#2333;&#2370;&#2350;&#2352;, &#2346;&#2366;&#2311;&#2325;&#2366;, &#2347;&#2327;&#2369;&#2310;&#2404; </span><strong>&#2348;&#2367;&#2361;&#2366;&#2352; </strong><span style=\"font-weight: 400;\">: &#2332;&#2335;-&#2332;&#2335;&#2367;&#2344;, &#2348;&#2326;&#2379;-&#2348;&#2326;&#2376;&#2344;, &#2346;&#2306;&#2357;&#2352;&#2367;&#2351;&#2366;&#2404; </span><strong>&#2331;&#2340;&#2381;&#2340;&#2368;&#2360;&#2327;&#2338;&#2364;</strong><span style=\"font-weight: 400;\">: &#2327;&#2380;&#2352; &#2350;&#2366;&#2337;&#2364;&#2367;&#2351;&#2366;, &#2346;&#2306;&#2341;&#2368;, &#2352;&#2366;&#2314;&#2340; &#2344;&#2366;&#2330;&#2366;, &#2346;&#2306;&#2337;&#2357;&#2366;&#2344;&#2368;, &#2357;&#2375;&#2342;&#2350;&#2340;&#2368;, &#2325;&#2366;&#2346;&#2366;&#2354;&#2367;&#2325;&#2404;</span></p>\\r\\n<p><span style=\"font-family: Roboto;\"> </span></p>\\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "16",
                    section: "18",
                    question_en: "<p><span style=\"font-family: Roboto;\">16. </span><span style=\"font-family: Roboto;\">Which of the following cell organelles is made up of ribosomal RNA and protein?</span></p>\\n",
                    question_hi: "<p><span style=\"font-family: Roboto;\">16. </span><span style=\"font-family: Palanquin;\">&#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340; &#2350;&#2375;&#2306; &#2360;&#2375; &#2325;&#2380;&#2344;-&#2360;&#2366; &#2325;&#2379;&#2358;&#2367;&#2325;&#2366;&#2306;&#2327; &#2352;&#2366;&#2311;&#2348;&#2379;&#2360;&#2379;&#2350;&#2354; RNA &#2324;&#2352; &#2346;&#2381;&#2352;&#2379;&#2335;&#2368;&#2344; &#2360;&#2375; &#2348;&#2344;&#2366; &#2361;&#2379;&#2340;&#2366; &#2361;&#2376;?</span></p>\\n",
                    options_en: ["<p>Ribosome</p>\\n", "<p>Nucleus</p>\\n", 
                                "<p>Golgi body</p>\\n", "<p>Lysosome</p>\\n"],
                    options_hi: ["<p>&#2352;&#2366;&#2311;&#2348;&#2379;&#2360;&#2379;&#2350;</p>\\n", "<p>&#2325;&#2375;&#2306;&#2342;&#2381;&#2352;&#2325;</p>\\n",
                                "<p>&#2327;&#2377;&#2354;&#2381;&#2332;&#2368;&#2325;&#2366;&#2351;</p>\\n", "<p>&#2354;&#2366;&#2311;&#2360;&#2379;&#2360;&#2379;&#2350;</p>\\n"],
                    solution_en: "<p><span style=\"font-family: Roboto;\">16</span><span style=\"font-family: Roboto;\">.(a)</span><strong>&nbsp;Ribosome. </strong><span style=\"font-weight: 400;\">Cell is the structural and functional unit of all living organisms. It has three main parts - Cell membrane, Cytoplasm organelles, and Nucleus. </span><strong>Nucleus</strong><span style=\"font-weight: 400;\"> is the membrane-enclosed organelle within a cell that contains the chromosomes. </span><strong>Lysosomes</strong><span style=\"font-weight: 400;\"> (Suicidal bags) help in digestion and remove of dead cells. </span><strong>Golgi body </strong><span style=\"font-weight: 400;\">transfers the protein products it receives from the endoplasmic reticulum.</span></p>\\n",
                    solution_hi: "<p><span style=\"font-family: Roboto;\">16</span><span style=\"font-family: Palanquin;\">.(a)&nbsp;</span><strong>&#2352;&#2366;&#2311;&#2348;&#2379;&#2360;&#2379;&#2350;</strong><span style=\"font-weight: 400;\">&#2404; &#2325;&#2379;&#2358;&#2367;&#2325;&#2366; &#2360;&#2349;&#2368; &#2332;&#2368;&#2357;&#2367;&#2340; &#2346;&#2381;&#2352;&#2366;&#2339;&#2367;&#2351;&#2379;&#2306; &#2325;&#2368; &#2360;&#2306;&#2352;&#2330;&#2344;&#2366;&#2340;&#2381;&#2350;&#2325; &#2319;&#2357;&#2306; &#2325;&#2366;&#2352;&#2381;&#2351;&#2366;&#2340;&#2381;&#2350;&#2325; &#2311;&#2325;&#2366;&#2312; &#2361;&#2376;&#2404; &#2311;&#2360;&#2325;&#2375; &#2340;&#2368;&#2344; &#2350;&#2369;&#2326;&#2381;&#2351; &#2349;&#2366;&#2327; &#2361;&#2376;&#2306; - &#2325;&#2379;&#2358;&#2367;&#2325;&#2366; &#2333;&#2367;&#2354;&#2381;&#2354;&#2368;, &#2360;&#2366;&#2311;&#2335;&#2379;&#2346;&#2381;&#2354;&#2366;&#2332;&#2381;&#2350; &#2321;&#2352;&#2381;&#2327;&#2375;&#2344;&#2375;&#2354; &#2324;&#2352; &#2344;&#2381;&#2351;&#2370;&#2325;&#2381;&#2354;&#2367;&#2351;&#2360;&#2404; </span><strong>&#2344;&#2381;&#2351;&#2370;&#2325;&#2381;&#2354;&#2367;&#2351;&#2360; </strong><span style=\"font-weight: 400;\">&#2319;&#2325; &#2325;&#2379;&#2358;&#2367;&#2325;&#2366; &#2325;&#2375; &#2349;&#2368;&#2340;&#2352; &#2333;&#2367;&#2354;&#2381;&#2354;&#2368; &#2360;&#2375; &#2328;&#2367;&#2352;&#2366; &#2309;&#2306;&#2327; &#2361;&#2376; &#2332;&#2367;&#2360;&#2350;&#2375;&#2306; &#2327;&#2369;&#2339;&#2360;&#2370;&#2340;&#2381;&#2352; &#2361;&#2379;&#2340;&#2375; &#2361;&#2376;&#2306;&#2404; </span><strong>&#2354;&#2366;&#2311;&#2360;&#2379;&#2360;&#2379;&#2350; </strong><span style=\"font-weight: 400;\">(&#2310;&#2340;&#2381;&#2350;&#2328;&#2366;&#2340;&#2368; &#2341;&#2376;&#2354;&#2368;) &#2346;&#2366;&#2330;&#2344; &#2350;&#2375;&#2306; &#2360;&#2361;&#2366;&#2351;&#2340;&#2366; &#2325;&#2352;&#2340;&#2366; &#2361;&#2376;&#2306; &#2324;&#2352; &#2350;&#2371;&#2340; &#2325;&#2379;&#2358;&#2367;&#2325;&#2366;&#2323;&#2306; &#2325;&#2366; &#2344;&#2367;&#2359;&#2381;&#2325;&#2366;&#2360;&#2344; &#2325;&#2352;&#2340;&#2366; &#2361;&#2376;&#2306;&#2404;&#2404; </span><strong>&#2327;&#2379;&#2354;&#2381;&#2332;&#2368;&#2325;&#2366;&#2351; </strong><span style=\"font-weight: 400;\">&#2319;&#2306;&#2337;&#2379;&#2346;&#2381;&#2354;&#2366;&#2332;&#2381;&#2350;&#2367;&#2325; &#2352;&#2375;&#2335;&#2367;&#2325;&#2369;&#2354;&#2350; &#2360;&#2375; &#2346;&#2381;&#2352;&#2366;&#2346;&#2381;&#2340; &#2346;&#2381;&#2352;&#2379;&#2335;&#2368;&#2344; &#2313;&#2340;&#2381;&#2346;&#2366;&#2342;&#2379;&#2306; &#2325;&#2379; &#2360;&#2381;&#2341;&#2366;&#2344;&#2366;&#2306;&#2340;&#2352;&#2367;&#2340; &#2325;&#2352;&#2340;&#2366; &#2361;&#2376;&#2404;</span></p>\\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "17",
                    section: "18",
                    question_en: "<p><span style=\"font-family: Roboto;\">17.</span><span style=\"font-family: Roboto;\"> Shri Sagar Kailas Ovhalkar was in news for being conferred with Arjuna Award 2022 for his outstanding contribution in which of the following sports?</span></p>\\n",
                    question_hi: "<p><span style=\"font-family: Roboto;\">17.</span><span style=\"font-family: Palanquin;\"> &#2358;&#2381;&#2352;&#2368; &#2360;&#2366;&#2327;&#2352; &#2325;&#2376;&#2354;&#2366;&#2360; &#2323;&#2357;&#2354;&#2325;&#2352; &#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340; &#2350;&#2375;&#2306; &#2360;&#2375; &#2325;&#2367;&#2360; &#2326;&#2375;&#2354; &#2350;&#2375;&#2306; &#2313;&#2340;&#2381;&#2325;&#2371;&#2359;&#2381;&#2335; &#2351;&#2379;&#2327;&#2342;&#2366;&#2344; &#2325;&#2375; &#2354;&#2367;&#2319; &#2309;&#2352;&#2381;&#2332;&#2369;&#2344; &#2346;&#2369;&#2352;&#2360;&#2381;&#2325;&#2366;&#2352; 2022 &#2360;&#2375; &#2360;&#2350;&#2381;&#2350;&#2366;&#2344;&#2367;&#2340; &#2361;&#2379;&#2344;&#2375; &#2325;&#2375; &#2325;&#2366;&#2352;&#2339; &#2326;&#2348;&#2352;&#2379;&#2306; &#2350;&#2375;&#2306; &#2341;&#2375;?</span></p>\\n",
                    options_en: ["<p><span style=\"font-family: Roboto;\">Kabbadi</span><span style=\"font-family: Roboto;\"> </span></p>\\n", "<p>Chess</p>\\n", 
                                "<p>Judo</p>\\n", "<p>Mallakhamb</p>\\n"],
                    options_hi: ["<p>&#2325;&#2348;&#2337;&#2381;&#2337;&#2368;</p>\\n", "<p>&#2358;&#2340;&#2352;&#2306;&#2332;</p>\\n",
                                "<p>&#2332;&#2370;&#2337;&#2379;</p>\\n", "<p>&#2350;&#2354;&#2326;&#2306;&#2348;</p>\\n"],
                    solution_en: "<p><span style=\"font-family: Roboto;\">17</span><span style=\"font-family: Roboto;\">.(d)&nbsp;</span><strong>Mallakhamb </strong><span style=\"font-weight: 400;\">(Martial art)</span><strong> - </strong><span style=\"font-weight: 400;\">It is practiced mainly in Madhya Pradesh and Maharashtra.</span><strong> Arjuna Awards</strong><span style=\"font-weight: 400;\"> - Established in 1961 and is awarded annually by the Ministry of Youth Affairs and Sports&nbsp; for outstanding performance in Sports and Games. </span><strong>First recipient</strong><span style=\"font-weight: 400;\"> - Krishna Das (Archery in 1961). </span><strong>Meena Shah</strong><span style=\"font-weight: 400;\"> (Badminton) was the first woman to get the Arjuna award in 1962. Prize money of Arjuna Award has been enhanced to Rs 15 lakh from Rs 5 lakh.&nbsp;&nbsp;</span></p>\\n",
                    solution_hi: "<p><span style=\"font-family: Roboto;\">17</span><span style=\"font-family: Palanquin;\">.(d) </span><strong>&#2350;&#2354;&#2381;&#2354;&#2326;&#2306;&#2348; </strong><span style=\"font-weight: 400;\">(&#2350;&#2366;&#2352;&#2381;&#2358;&#2354; &#2310;&#2352;&#2381;&#2335;) - &#2351;&#2361; &#2350;&#2343;&#2381;&#2351; &#2346;&#2381;&#2352;&#2342;&#2375;&#2358; &#2324;&#2352; &#2350;&#2361;&#2366;&#2352;&#2366;&#2359;&#2381;&#2335;&#2381;&#2352; &#2360;&#2375; &#2360;&#2306;&#2348;&#2306;&#2343;&#2367;&#2340; &#2361;&#2376;&#2404; </span><strong>&#2309;&#2352;&#2381;&#2332;&#2369;&#2344; &#2346;&#2369;&#2352;&#2360;&#2381;&#2325;&#2366;&#2352;</strong><span style=\"font-weight: 400;\"> - 1961 &#2350;&#2375;&#2306; &#2360;&#2381;&#2341;&#2366;&#2346;&#2367;&#2340; &#2324;&#2352; &#2326;&#2375;&#2354; &#2324;&#2352; &#2326;&#2375;&#2354;&#2379;&#2306; &#2350;&#2375;&#2306; &#2313;&#2340;&#2381;&#2325;&#2371;&#2359;&#2381;&#2335; &#2346;&#2381;&#2352;&#2342;&#2352;&#2381;&#2358;&#2344; &#2325;&#2375; &#2354;&#2367;&#2319; &#2351;&#2369;&#2357;&#2366; &#2350;&#2366;&#2350;&#2354;&#2375; &#2324;&#2352; &#2326;&#2375;&#2354; &#2350;&#2306;&#2340;&#2381;&#2352;&#2366;&#2354;&#2351; &#2342;&#2381;&#2357;&#2366;&#2352;&#2366; &#2346;&#2381;&#2352;&#2340;&#2367;&#2357;&#2352;&#2381;&#2359; &#2346;&#2381;&#2352;&#2342;&#2366;&#2344; &#2325;&#2367;&#2351;&#2366; &#2332;&#2366;&#2340;&#2366; &#2361;&#2376;&#2404; </span><strong>&#2346;&#2381;&#2352;&#2341;&#2350; &#2346;&#2381;&#2352;&#2366;&#2346;&#2381;&#2340;&#2325;&#2352;&#2381;&#2340;&#2366;</strong><span style=\"font-weight: 400;\"> - &#2325;&#2371;&#2359;&#2381;&#2339;&#2366; &#2342;&#2366;&#2360; (1961 &#2350;&#2375;&#2306; &#2340;&#2368;&#2352;&#2306;&#2342;&#2366;&#2332;&#2368;)&#2404; </span><strong>&#2350;&#2368;&#2344;&#2366; &#2358;&#2366;&#2361;</strong><span style=\"font-weight: 400;\"> (&#2348;&#2376;&#2337;&#2350;&#2367;&#2306;&#2335;&#2344;) 1962 &#2350;&#2375;&#2306; &#2309;&#2352;&#2381;&#2332;&#2369;&#2344; &#2346;&#2369;&#2352;&#2360;&#2381;&#2325;&#2366;&#2352; &#2346;&#2366;&#2344;&#2375; &#2357;&#2366;&#2354;&#2368; &#2346;&#2361;&#2354;&#2368; &#2350;&#2361;&#2367;&#2354;&#2366; &#2341;&#2368;&#2306;&#2404; &#2309;&#2352;&#2381;&#2332;&#2369;&#2344; &#2346;&#2369;&#2352;&#2360;&#2381;&#2325;&#2366;&#2352; &#2325;&#2368; &#2346;&#2369;&#2352;&#2360;&#2381;&#2325;&#2366;&#2352; &#2352;&#2366;&#2358;&#2367; 5 &#2354;&#2366;&#2326; &#2352;&#2369;&#2346;&#2351;&#2375; &#2360;&#2375; &#2348;&#2338;&#2364;&#2366;&#2325;&#2352; 15 &#2354;&#2366;&#2326; &#2352;&#2369;&#2346;&#2351;&#2375; &#2325;&#2352; &#2342;&#2368; &#2327;&#2312; &#2361;&#2376;&#2404;</span></p>\\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "18",
                    section: "18",
                    question_en: "<p><span style=\"font-family: Roboto;\">18. </span><span style=\"font-family: Roboto;\">Who among the following was the first person to discuss the in pre - independent India?</span></p>\\n",
                    question_hi: "<p><span style=\"font-family: Roboto;\">18. </span><span style=\"font-family: Palanquin;\">&#2360;&#2381;&#2357;&#2340;&#2306;&#2340;&#2381;&#2352;&#2340;&#2366; - &#2346;&#2370;&#2352;&#2381;&#2357; &#2349;&#2366;&#2352;&#2340; &#2350;&#2375;&#2306; &#2327;&#2352;&#2368;&#2348;&#2368; &#2352;&#2375;&#2326;&#2366; &#2346;&#2352; &#2330;&#2352;&#2381;&#2330;&#2366; &#2325;&#2352;&#2344;&#2375; &#2357;&#2366;&#2354;&#2366; &#2346;&#2361;&#2354;&#2366; &#2357;&#2381;&#2351;&#2325;&#2381;&#2340;&#2367; &#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340; &#2350;&#2375;&#2306; &#2360;&#2375; &#2325;&#2380;&#2344; &#2341;&#2366; ?</span></p>\\n",
                    options_en: ["<p>Dadabhai Naoroji</p>\\n", "<p>Mahalanobis</p>\\n", 
                                "<p>Dr. BR Ambedkar</p>\\n", "<p>RK Shanmukham Chetty</p>\\n"],
                    options_hi: ["<p>&#2342;&#2366;&#2342;&#2366;&#2349;&#2366;&#2312; &#2344;&#2380;&#2352;&#2379;&#2332;&#2368;</p>\\n", "<p>&#2350;&#2361;&#2366;&#2354;&#2344;&#2379;&#2348;&#2367;&#2360;</p>\\n",
                                "<p>&#2337;&#2377;. &#2348;&#2368;.&#2310;&#2352;. &#2309;&#2350;&#2381;&#2348;&#2375;&#2337;&#2325;&#2352;</p>\\n", "<p>&#2310;&#2352;.&#2325;&#2375;. &#2358;&#2344;&#2350;&#2369;&#2326;&#2350; &#2330;&#2375;&#2335;&#2381;&#2335;&#2368;</p>\\n"],
                    solution_en: "<p><span style=\"font-family: Roboto;\">18</span><span style=\"font-family: Roboto;\">.(a)</span><strong>&nbsp;Dadabhai Naoroji </strong><span style=\"font-weight: 400;\">(Grand old man of India)</span><strong>- </strong><span style=\"font-weight: 400;\">He formed the London India Society in 1865 and East India Association in 1867. He was the first Asian to become a British Member of Parliament. Book : Poverty and Un-British Rule in India.</span><strong> RK Shanmukham Chetty - </strong><span style=\"font-weight: 400;\">Finance Minister of India when the country\'s first budget was tabled in Parliament on 26 November 1947. </span><strong>&nbsp;Dr. BR Ambedkar - </strong><span style=\"font-weight: 400;\">Father of Indian Constitution.</span></p>\\n",
                    solution_hi: "<p><span style=\"font-family: Roboto;\">18</span><span style=\"font-family: Roboto;\">.(a) </span><strong>&#2342;&#2366;&#2342;&#2366;&#2349;&#2366;&#2312; &#2344;&#2380;&#2352;&#2379;&#2332;&#2368;</strong><span style=\"font-weight: 400;\"> (&#2349;&#2366;&#2352;&#2340; &#2325;&#2375; &#2327;&#2381;&#2352;&#2376;&#2306;&#2337; &#2323;&#2354;&#2381;&#2337; &#2350;&#2376;&#2344;) - &#2313;&#2344;&#2381;&#2361;&#2379;&#2306;&#2344;&#2375; 1865 &#2350;&#2375;&#2306; &#2354;&#2306;&#2342;&#2344; &#2311;&#2306;&#2337;&#2367;&#2351;&#2366; &#2360;&#2379;&#2360;&#2366;&#2311;&#2335;&#2368; &#2324;&#2352; 1867 &#2350;&#2375;&#2306; &#2312;&#2360;&#2381;&#2335; &#2311;&#2306;&#2337;&#2367;&#2351;&#2366; &#2319;&#2360;&#2379;&#2360;&#2367;&#2319;&#2358;&#2344; &#2325;&#2366; &#2327;&#2336;&#2344; &#2325;&#2367;&#2351;&#2366;&#2404; &#2357;&#2361; &#2348;&#2381;&#2352;&#2367;&#2335;&#2367;&#2358; &#2360;&#2306;&#2360;&#2342; &#2360;&#2342;&#2360;&#2381;&#2351; &#2348;&#2344;&#2344;&#2375; &#2357;&#2366;&#2354;&#2375; &#2346;&#2361;&#2354;&#2375; &#2319;&#2358;&#2367;&#2351;&#2366;&#2312; &#2341;&#2375;&#2404; &#2346;&#2369;&#2360;&#2381;&#2340;&#2325;: &#2349;&#2366;&#2352;&#2340; &#2350;&#2375;&#2306; &#2327;&#2352;&#2368;&#2348;&#2368; &#2324;&#2352; &#2327;&#2376;&#2352;-&#2348;&#2381;&#2352;&#2367;&#2335;&#2367;&#2358; &#2358;&#2366;&#2360;&#2344;&#2404; </span><strong>&#2310;&#2352; &#2325;&#2375;&nbsp; &#2358;&#2344;&#2350;&#2369;&#2326;&#2350; &#2330;&#2375;&#2335;&#2381;&#2335;&#2368;</strong><span style=\"font-weight: 400;\"> - &#2349;&#2366;&#2352;&#2340; &#2325;&#2375; &#2357;&#2367;&#2340;&#2381;&#2340; &#2350;&#2306;&#2340;&#2381;&#2352;&#2368; &#2332;&#2348; 26 &#2344;&#2357;&#2306;&#2348;&#2352; 1947 &#2325;&#2379; &#2342;&#2375;&#2358; &#2325;&#2366; &#2346;&#2361;&#2354;&#2366; &#2348;&#2332;&#2335; &#2360;&#2306;&#2360;&#2342; &#2350;&#2375;&#2306; &#2346;&#2375;&#2358; &#2325;&#2367;&#2351;&#2366; &#2327;&#2351;&#2366; &#2341;&#2366;&#2404; </span><strong>&#2337;&#2377;. &#2348;&#2368;. &#2310;&#2352; &#2309;&#2306;&#2348;&#2375;&#2337;&#2325;&#2352;</strong><span style=\"font-weight: 400;\"> - &#2349;&#2366;&#2352;&#2340;&#2368;&#2351; &#2360;&#2306;&#2357;&#2367;&#2343;&#2366;&#2344; &#2325;&#2375; &#2332;&#2344;&#2325;&#2404;</span></p>\\r\\n<p><span style=\"font-family: Roboto;\"> </span></p>\\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "19",
                    section: "18",
                    question_en: "<p><span style=\"font-family: Roboto;\">19. </span><span style=\"font-family: Roboto;\">Identify the group of states that are covered in the Sardar Sarovar Project.</span></p>\\n",
                    question_hi: "<p><span style=\"font-family: Roboto;\">19. </span><span style=\"font-family: Palanquin;\">&#2360;&#2352;&#2342;&#2366;&#2352; &#2360;&#2352;&#2379;&#2357;&#2352; &#2346;&#2352;&#2367;&#2351;&#2379;&#2332;&#2344;&#2366; &#2350;&#2375;&#2306; &#2358;&#2366;&#2350;&#2367;&#2354; &#2352;&#2366;&#2332;&#2381;&#2351;&#2379;&#2306; &#2325;&#2375; &#2360;&#2350;&#2370;&#2361; &#2325;&#2368; &#2346;&#2361;&#2330;&#2366;&#2344; &#2325;&#2352;&#2375;&#2306;&#2404;</span></p>\\n",
                    options_en: ["<p>Maharashtra, Madhya Pradesh, Chhattisgarh and Jharkhand</p>\\n", "<p>Maharashtra, Madhya Pradesh, Gujarat and Rajasthan</p>\\n", 
                                "<p>Gujarat, Punjab, Haryana, Rajasthan</p>\\n", "<p>Gujarat, Rajasthan, Karnataka and Telangana</p>\\n"],
                    options_hi: ["<p>&#2350;&#2361;&#2366;&#2352;&#2366;&#2359;&#2381;&#2335;&#2381;&#2352;, &#2350;&#2343;&#2381;&#2351; &#2346;&#2381;&#2352;&#2342;&#2375;&#2358;, &#2331;&#2340;&#2381;&#2340;&#2368;&#2360;&#2327;&#2338;&#2364; &#2324;&#2352; &#2333;&#2366;&#2352;&#2326;&#2306;&#2337;</p>\\n", "<p>&#2350;&#2361;&#2366;&#2352;&#2366;&#2359;&#2381;&#2335;&#2381;&#2352;, &#2350;&#2343;&#2381;&#2351; &#2346;&#2381;&#2352;&#2342;&#2375;&#2358;, &#2327;&#2369;&#2332;&#2352;&#2366;&#2340; &#2324;&#2352; &#2352;&#2366;&#2332;&#2360;&#2381;&#2341;&#2366;&#2344;</p>\\n",
                                "<p>&#2327;&#2369;&#2332;&#2352;&#2366;&#2340;, &#2346;&#2306;&#2332;&#2366;&#2348;, &#2361;&#2352;&#2367;&#2351;&#2366;&#2339;&#2366;, &#2352;&#2366;&#2332;&#2360;&#2381;&#2341;&#2366;&#2344;</p>\\n", "<p>&#2327;&#2369;&#2332;&#2352;&#2366;&#2340;, &#2352;&#2366;&#2332;&#2360;&#2381;&#2341;&#2366;&#2344;, &#2325;&#2352;&#2381;&#2344;&#2366;&#2335;&#2325; &#2324;&#2352; &#2340;&#2375;&#2354;&#2306;&#2327;&#2366;&#2344;&#2366;</p>\\n"],
                    solution_en: "<p><span style=\"font-family: Roboto;\">19</span><span style=\"font-family: Roboto;\">.(b)</span><strong><span style=\"font-family: Roboto;\"> </span></strong><span style=\"font-family: Roboto;\"><strong>Sardar Sarovar Project -</strong> </span><span style=\"font-family: Roboto;\">It is one of the largest water resources projects of India on Narmada River. </span><span style=\"font-family: Roboto;\"><strong>Other projects :</strong> </span><span style=\"font-family: Roboto;\">Tehri Dam, Highest (Bhagirathi River) - Uttarakhand. Hirakud Dam, Longest (Mahanadi) - Odisha. Kallanai Dam (Kaveri) - Tamil Nadu, Rihand Dam (Rihand River) - Uttar Pradesh, Maithon Dam (Barakar) - Jharkhand, Mettur Dam (Kaveri) - Tamil Nadu, Krishnarajasagar Dam (Kaveri) - Karnataka, Indira Sagar Dam (Narmada) - Madhya Pradesh. </span></p>\\n",
                    solution_hi: "<p><span style=\"font-family: Roboto;\">19</span><span style=\"font-family: Palanquin;\">.(b) </span><strong>&#2360;&#2352;&#2342;&#2366;&#2352; &#2360;&#2352;&#2379;&#2357;&#2352; &#2346;&#2352;&#2367;&#2351;&#2379;&#2332;&#2344;&#2366; -</strong><span style=\"font-weight: 400;\"> &#2351;&#2361; &#2344;&#2352;&#2381;&#2350;&#2342;&#2366; &#2344;&#2342;&#2368; &#2346;&#2352; &#2349;&#2366;&#2352;&#2340; &#2325;&#2368; &#2360;&#2348;&#2360;&#2375; &#2348;&#2337;&#2364;&#2368; &#2332;&#2354; &#2360;&#2306;&#2360;&#2366;&#2343;&#2344; &#2346;&#2352;&#2367;&#2351;&#2379;&#2332;&#2344;&#2366;&#2323;&#2306; &#2350;&#2375;&#2306; &#2360;&#2375; &#2319;&#2325; &#2361;&#2376;&#2404; </span><strong>&#2309;&#2344;&#2381;&#2351; &#2346;&#2352;&#2367;&#2351;&#2379;&#2332;&#2344;&#2366;&#2319;&#2306;:</strong><span style=\"font-weight: 400;\"> &#2335;&#2375;&#2361;&#2352;&#2368; &#2348;&#2366;&#2306;&#2343;, &#2360;&#2348;&#2360;&#2375; &#2314;&#2306;&#2330;&#2368; (&#2349;&#2366;&#2327;&#2368;&#2352;&#2341;&#2368; &#2344;&#2342;&#2368;) - &#2313;&#2340;&#2381;&#2340;&#2352;&#2366;&#2326;&#2306;&#2337;&#2404; &#2361;&#2368;&#2352;&#2366;&#2325;&#2369;&#2306;&#2337; &#2348;&#2366;&#2306;&#2343;, &#2360;&#2348;&#2360;&#2375; &#2354;&#2306;&#2348;&#2366; (&#2350;&#2361;&#2366;&#2344;&#2342;&#2368;) - &#2323;&#2337;&#2367;&#2358;&#2366;&#2404; &#2325;&#2354;&#2381;&#2354;&#2344;&#2312; &#2348;&#2366;&#2306;&#2343; (&#2325;&#2366;&#2357;&#2375;&#2352;&#2368;) - &#2340;&#2350;&#2367;&#2354;&#2344;&#2366;&#2337;&#2369;, &#2352;&#2367;&#2361;&#2306;&#2342; &#2348;&#2366;&#2306;&#2343; (&#2352;&#2367;&#2361;&#2306;&#2342; &#2344;&#2342;&#2368;) - &#2313;&#2340;&#2381;&#2340;&#2352; &#2346;&#2381;&#2352;&#2342;&#2375;&#2358;, &#2350;&#2376;&#2341;&#2344; &#2348;&#2366;&#2306;&#2343; (&#2348;&#2352;&#2366;&#2325;&#2352;) - &#2333;&#2366;&#2352;&#2326;&#2306;&#2337;, &#2350;&#2375;&#2335;&#2381;&#2335;&#2370;&#2352; &#2348;&#2366;&#2306;&#2343; (&#2325;&#2366;&#2357;&#2375;&#2352;&#2368;) - &#2340;&#2350;&#2367;&#2354;&#2344;&#2366;&#2337;&#2369;, &#2325;&#2371;&#2359;&#2381;&#2339;&#2352;&#2366;&#2332;&#2360;&#2366;&#2327;&#2352; &#2348;&#2366;&#2306;&#2343; (&#2325;&#2366;&#2357;&#2375;&#2352;&#2368;) - &#2325;&#2352;&#2381;&#2344;&#2366;&#2335;&#2325;, &#2311;&#2306;&#2342;&#2367;&#2352;&#2366; &#2360;&#2366;&#2327;&#2352; &#2348;&#2366;&#2306;&#2343; (&#2344;&#2352;&#2381;&#2350;&#2342;&#2366;) - &#2350;&#2343;&#2381;&#2351; &#2346;&#2381;&#2352;&#2342;&#2375;&#2358;&#2404;</span></p>\\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "20",
                    section: "18",
                    question_en: "<p><span style=\"font-family: Roboto;\">20. </span><span style=\"font-family: Roboto;\">Which German chemist and physicist proposed that an aromatic compound must have an odd number of pairs of electrons, Which can mathematically be written as 4n + 2 (n = 0,1,2,3, etc.) in 1931?</span></p>\\n",
                    question_hi: "<p><span style=\"font-family: Roboto;\">20. </span><span style=\"font-family: Palanquin;\">1931 &#2350;&#2375;&#2306; &#2325;&#2367;&#2360; &#2332;&#2352;&#2381;&#2350;&#2344; &#2352;&#2360;&#2366;&#2351;&#2344;&#2332;&#2381;&#2334; &#2324;&#2352; &#2349;&#2380;&#2340;&#2367;&#2325; &#2357;&#2367;&#2332;&#2381;&#2334;&#2366;&#2344;&#2368; &#2344;&#2375; &#2351;&#2361; &#2346;&#2381;&#2352;&#2360;&#2381;&#2340;&#2366;&#2357;&#2367;&#2340; &#2325;&#2367;&#2351;&#2366; &#2325;&#2367; &#2319;&#2325; &#2319;&#2352;&#2379;&#2350;&#2376;&#2335;&#2367;&#2325; &#2351;&#2380;&#2327;&#2367;&#2325; &#2350;&#2375;&#2306; &#2357;&#2367;&#2359;&#2350; &#2360;&#2306;&#2326;&#2381;&zwj;&#2351;&#2366; &#2350;&#2375;&#2306; &#2311;&#2354;&#2375;&#2325;&#2381;&#2335;&#2381;&#2352;&#2377;&#2344;&#2379;&#2306; &#2325;&#2375; &#2351;&#2369;&#2327;&#2381;&#2350; &#2361;&#2379;&#2344;&#2375; &#2330;&#2366;&#2361;&#2367;&#2319;, &#2332;&#2367;&#2360;&#2375; &#2327;&#2339;&#2367;&#2340;&#2368;&#2351; &#2352;&#2370;&#2346; &#2350;&#2375;&#2306; 4n + 2 (n = 0, 1, 2, 3, &#2310;&#2342;&#2367;) &#2325;&#2375; &#2352;&#2370;&#2346; &#2350;&#2375;&#2306; &#2354;&#2367;&#2326;&#2366; &#2332;&#2366; &#2360;&#2325;&#2340;&#2366; &#2361;&#2376;?</span></p>\\n",
                    options_en: ["<p>Friedrich Kekule</p>\\n", "<p>Rosalind Franklin</p>\\n", 
                                "<p>Erich Huckel</p>\\n", "<p>Antoine Lavoisier</p>\\n"],
                    options_hi: ["<p><span style=\"font-weight: 400;\">&#2347;&#2381;&#2352;&#2375;&#2337;&#2352;&#2367;&#2325; &#2325;&#2375;&#2325;&#2369;&#2354;&#2375; (Friedrich Kekule)</span></p>\\n", "<p>&#2352;&#2379;&#2332;&#2364;&#2354;&#2367;&#2306;&#2337; &#2347;&#2381;&#2352;&#2376;&#2306;&#2325;&#2354;&#2367;&#2344; (Rosalind Franklin)</p>\\n",
                                "<p><span style=\"font-weight: 400;\">&#2319;&#2352;&#2367;&#2330; &#2361;&#2325;&#2354; (Erich Huckel)</span></p>\\n", "<p>&#2319;&#2306;&#2335;&#2379;&#2344;&#2368; &#2354;&#2357;&#2377;&#2332;&#2364;&#2367;&#2319;&#2352; (Antoine Lavoisier)</p>\\n"],
                    solution_en: "<p><span style=\"font-family: Roboto;\">20</span><span style=\"font-family: Roboto;\">.(c)&nbsp;</span><strong>&nbsp;Erich Huckel. Friedrich Kekule</strong><span style=\"font-weight: 400;\"> gave the Kekule structure in 1865, that is the ring structure (Benzene) with alternate double bonds. Diffraction data produced by Maurice Wilkins and </span><strong>Rosalind Franklin</strong><span style=\"font-weight: 400;\">, proposed a very simple but famous Double Helix model for the structure of DNA. </span><strong>Antonie Lavoisier</strong><span style=\"font-weight: 400;\"> recognized hydrogen and named it and observed that air contains more than one gas.</span></p>\\n",
                    solution_hi: "<p><span style=\"font-family: Roboto;\">20</span><span style=\"font-family: Roboto;\">.(c) <strong>&#2319;&#2352;&#2367;&#2330; &#2361;&#2325;&#2375;&#2354; (Erich Huckel)</strong><span style=\"font-weight: 400;\">&#2404;</span><strong> &#2347;&#2381;&#2352;&#2375;&#2337;&#2352;&#2367;&#2325; &#2325;&#2375;&#2325;&#2369;&#2354;&#2375;</strong><span style=\"font-weight: 400;\"> &#2344;&#2375; 1865 &#2350;&#2375;&#2306; &#2325;&#2375;&#2325;&#2369;&#2354;&#2375; &#2360;&#2306;&#2352;&#2330;&#2344;&#2366; &#2342;&#2368;, &#2332;&#2379; &#2325;&#2367; &#2357;&#2376;&#2325;&#2354;&#2381;&#2346;&#2367;&#2325; &#2342;&#2379;&#2361;&#2352;&#2375; &#2348;&#2306;&#2343; &#2357;&#2366;&#2354;&#2368; &#2352;&#2367;&#2306;&#2327; &#2360;&#2306;&#2352;&#2330;&#2344;&#2366; (&#2348;&#2375;&#2306;&#2332;&#2368;&#2344;) &#2361;&#2376;&#2404; &#2350;&#2380;&#2352;&#2367;&#2360; &#2357;&#2367;&#2354;&#2381;&#2325;&#2367;&#2306;&#2360; &#2324;&#2352; </span><strong>&#2352;&#2379;&#2332;&#2364;&#2354;&#2367;&#2306;&#2337; &#2347;&#2381;&#2352;&#2376;&#2306;&#2325;&#2354;&#2367;&#2344;</strong><span style=\"font-weight: 400;\"> &#2342;&#2381;&#2357;&#2366;&#2352;&#2366; &#2344;&#2367;&#2352;&#2381;&#2350;&#2367;&#2340; &#2357;&#2367;&#2357;&#2352;&#2381;&#2340;&#2344; &#2337;&#2375;&#2335;&#2366; &#2325;&#2375; &#2360;&#2366;&#2341; DNA &#2325;&#2368; &#2360;&#2306;&#2352;&#2330;&#2344;&#2366; &#2325;&#2375; &#2354;&#2367;&#2319; &#2319;&#2325; &#2348;&#2361;&#2369;&#2340; &#2361;&#2368; &#2360;&#2352;&#2354; &#2346;&#2381;&#2352;&#2360;&#2367;&#2342;&#2381;&#2343; &#2342;&#2381;&#2357;&#2367;&#2325;&#2369;&#2306;&#2337;&#2354;&#2368; &#2361;&#2375;&#2354;&#2367;&#2325;&#2381;&#2360; &#2350;&#2377;&#2337;&#2354; &#2325;&#2366; &#2346;&#2381;&#2352;&#2360;&#2381;&#2340;&#2366;&#2357; &#2352;&#2326;&#2366;&#2404; </span><strong>&#2319;&#2306;&#2335;&#2379;&#2344;&#2368; &#2354;&#2375;&#2357;&#2377;&#2332;&#2364;&#2367;&#2351;&#2352;</strong><span style=\"font-weight: 400;\"> &#2344;&#2375; &#2361;&#2366;&#2311;&#2337;&#2381;&#2352;&#2379;&#2332;&#2344; &#2325;&#2379; &#2346;&#2361;&#2330;&#2366;&#2344;&#2366; &#2324;&#2352; &#2311;&#2360;&#2325;&#2366; &#2344;&#2366;&#2350; &#2352;&#2326;&#2366; &#2324;&#2352; &#2342;&#2375;&#2326;&#2366; &#2325;&#2367; &#2357;&#2366;&#2351;&#2369; &#2350;&#2375;&#2306; &#2319;&#2325; &#2360;&#2375; &#2309;&#2343;&#2367;&#2325; &#2327;&#2376;&#2360;&#2375;&#2306; &#2350;&#2380;&#2332;&#2370;&#2342; &#2361;&#2379;&#2340;&#2368;&nbsp; &#2361;&#2376;&#2306;&#2404;</span></span></p>\\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "21",
                    section: "18",
                    question_en: "<p><span style=\"font-family: Roboto;\">21. </span><span style=\"font-family: Roboto;\">Under whom was CP Ilbert a law member of the council?</span></p>\\n",
                    question_hi: "<p><span style=\"font-family: Roboto;\">21. </span><span style=\"font-family: Palanquin;\">&#2360;&#2368;&#2346;&#2368; &#2311;&#2354;&#2381;&#2348;&#2352;&#2381;&#2335; (CP Ilbert) &#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340; &#2350;&#2375;&#2306; &#2360;&#2375; &#2325;&#2367;&#2360;&#2325;&#2375; &#2309;&#2343;&#2368;&#2344; &#2346;&#2352;&#2367;&#2359;&#2342; &#2325;&#2375; &#2357;&#2367;&#2343;&#2367; &#2360;&#2342;&#2360;&#2381;&#2351; &#2341;&#2375;?</span></p>\\n",
                    options_en: ["<p>Lord Lytton</p>\\n", "<p>Lord Cornwallis</p>\\n", 
                                "<p>Lord Ripon</p>\\n", "<p>Lord Curzon</p>\\n"],
                    options_hi: ["<p><span style=\"font-weight: 400;\">&#2354;&#2377;&#2352;&#2381;&#2337; &#2354;&#2367;&#2335;&#2344; (Lord Lytton)</span></p>\\n", "<p><span style=\"font-weight: 400;\">&nbsp;&#2354;&#2377;&#2352;&#2381;&#2337; &#2325;&#2366;&#2352;&#2381;&#2344;&#2357;&#2366;&#2354;&#2367;&#2360; (Lord Cornwallis)</span></p>\\n",
                                "<p>&#2354;&#2366;&#2352;&#2381;&#2337; &#2352;&#2367;&#2346;&#2344; (Lord Ripon)</p>\\n", "<p><span style=\"font-weight: 400;\">&#2354;&#2377;&#2352;&#2381;&#2337; &#2325;&#2352;&#2381;&#2332;&#2344; (Lord Curzon)</span></p>\\n"],
                    solution_en: "<p><span style=\"font-family: Roboto;\">21</span><span style=\"font-family: Roboto;\">.(c)&nbsp;</span><strong>Lord Ripon - </strong><span style=\"font-weight: 400;\">He introduced the IIbert Bill that would have granted the right of Indian judges to judge Europeans in court. He repealed the Vernacular Press Act of 1878 which was Introduced by </span><strong>Lord Lytton</strong><span style=\"font-weight: 400;\">. </span><strong>Lord Cornwallis </strong><span style=\"font-weight: 400;\">introduced the Permanent Settlement Act 1793. In July 1905, </span><strong>Curzon </strong><span style=\"font-weight: 400;\">announced the partition of the undivided Bengal Presidency.</span></p>\\n",
                    solution_hi: "<p><span style=\"font-family: Roboto;\">21</span><span style=\"font-family: Palanquin;\">.(c)&nbsp; </span><strong>&#2354;&#2377;&#2352;&#2381;&#2337; &#2352;&#2367;&#2346;&#2344; (Lord Ripon) - </strong><span style=\"font-weight: 400;\">&#2313;&#2344;&#2381;&#2361;&#2379;&#2306;&#2344;&#2375; &#2311;&#2354;&#2381;&#2348;&#2352;&#2381;&#2335; &#2348;&#2367;&#2354; &#2346;&#2375;&#2358; &#2325;&#2367;&#2351;&#2366; &#2332;&#2379; &#2349;&#2366;&#2352;&#2340;&#2368;&#2351; &#2344;&#2381;&#2351;&#2366;&#2351;&#2366;&#2343;&#2368;&#2358;&#2379;&#2306; &#2325;&#2379; &#2309;&#2342;&#2366;&#2354;&#2340; &#2350;&#2375;&#2306; &#2351;&#2370;&#2352;&#2379;&#2346;&#2368;&#2351; &#2354;&#2379;&#2327;&#2379;&#2306; &#2325;&#2366; &#2344;&#2381;&#2351;&#2366;&#2351; &#2325;&#2352;&#2344;&#2375; &#2325;&#2366; &#2309;&#2343;&#2367;&#2325;&#2366;&#2352; &#2346;&#2381;&#2352;&#2342;&#2366;&#2344; &#2325;&#2352;&#2340;&#2366; &#2361;&#2376;&#2404; &#2313;&#2344;&#2381;&#2361;&#2379;&#2306;&#2344;&#2375; 1878 &#2325;&#2375; &#2357;&#2352;&#2381;&#2344;&#2366;&#2325;&#2381;&#2351;&#2370;&#2354;&#2352; &#2346;&#2381;&#2352;&#2375;&#2360; &#2309;&#2343;&#2367;&#2344;&#2367;&#2351;&#2350; &#2325;&#2379; &#2344;&#2367;&#2352;&#2360;&#2381;&#2340; &#2325;&#2352; &#2342;&#2367;&#2351;&#2366; &#2332;&#2379; </span><strong>&#2354;&#2377;&#2352;&#2381;&#2337; &#2354;&#2367;&#2335;&#2344;</strong><span style=\"font-weight: 400;\"> &#2342;&#2381;&#2357;&#2366;&#2352;&#2366; &#2346;&#2381;&#2352;&#2360;&#2381;&#2340;&#2366;&#2357;&#2367;&#2340; &#2325;&#2367;&#2351;&#2366; &#2327;&#2351;&#2366; &#2341;&#2366;&#2404; </span><strong>&#2354;&#2377;&#2352;&#2381;&#2337; &#2325;&#2377;&#2352;&#2381;&#2344;&#2357;&#2366;&#2354;&#2367;&#2360;</strong><span style=\"font-weight: 400;\"> &#2344;&#2375; &#2360;&#2381;&#2341;&#2366;&#2351;&#2368; &#2348;&#2306;&#2342;&#2379;&#2348;&#2360;&#2381;&#2340; &#2309;&#2343;&#2367;&#2344;&#2367;&#2351;&#2350; 1793 &#2346;&#2375;&#2358; &#2325;&#2367;&#2351;&#2366; &#2341;&#2366;&#2404; &#2332;&#2369;&#2354;&#2366;&#2312; 1905 &#2350;&#2375;&#2306; </span><strong>&#2325;&#2352;&#2381;&#2332;&#2344; </strong><span style=\"font-weight: 400;\">&#2344;&#2375; &#2309;&#2357;&#2367;&#2349;&#2366;&#2332;&#2367;&#2340; &#2348;&#2306;&#2327;&#2366;&#2354; &#2346;&#2381;&#2352;&#2375;&#2360;&#2368;&#2337;&#2375;&#2306;&#2360;&#2368; &#2325;&#2375; &#2357;&#2367;&#2349;&#2366;&#2332;&#2344; &#2325;&#2368; &#2328;&#2379;&#2359;&#2339;&#2366; &#2325;&#2368;&#2404;</span></p>\\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "22",
                    section: "18",
                    question_en: "<p><span style=\"font-family: Roboto;\">22. </span><span style=\"font-family: Roboto;\">Who among the following is the author of the book \'The Buddha and his Dhamma\', which appeared in 1957 after the death of its author?</span></p>\\n",
                    question_hi: "<p><span style=\"font-family: Roboto;\">22. </span><span style=\"font-family: Palanquin;\">&#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340; &#2350;&#2375;&#2306; &#2360;&#2375; &#2325;&#2380;&#2344; \'&#2342; &#2348;&#2369;&#2342;&#2381;&#2343; &#2319;&#2306;&#2337; &#2361;&#2367;&#2332; &#2343;&#2350;&#2381;&#2350;&#2366;\' &#2346;&#2369;&#2360;&#2381;&#2340;&#2325; &#2325;&#2375; &#2354;&#2375;&#2326;&#2325; &#2361;&#2376;&#2306;, &#2332;&#2379; 1957 &#2350;&#2375;&#2306; &#2354;&#2375;&#2326;&#2325; &#2325;&#2368; &#2350;&#2371;&#2340;&#2381;&#2351;&#2369; &#2325;&#2375; &#2348;&#2366;&#2342; &#2346;&#2381;&#2352;&#2325;&#2366;&#2358;&#2367;&#2340; &#2361;&#2369;&#2312; &#2341;&#2368; ?</span></p>\\n",
                    options_en: ["<p>Subhas Chandra Bose</p>\\n", "<p>BR Ambedkar</p>\\n", 
                                "<p>Mahatma Gandhi</p>\\n", "<p>Motilal Nehru</p>\\n"],
                    options_hi: ["<p>&#2360;&#2369;&#2349;&#2366;&#2359; &#2330;&#2306;&#2342;&#2381;&#2352; &#2348;&#2379;&#2360;</p>\\n", "<p>&#2348;&#2368;.&#2310;&#2352;. &#2309;&#2306;&#2348;&#2375;&#2337;&#2325;&#2352;</p>\\n",
                                "<p>&#2350;&#2361;&#2366;&#2340;&#2381;&#2350;&#2366; &#2327;&#2366;&#2306;&#2343;&#2368;</p>\\n", "<p>&#2350;&#2379;&#2340;&#2368;&#2354;&#2366;&#2354; &#2344;&#2375;&#2361;&#2352;&#2370;</p>\\n"],
                    solution_en: "<p><span style=\"font-family: Roboto;\">22</span><span style=\"font-family: Roboto;\">.(b)<strong> BR Ambedkar. Other Books -</strong></span><span style=\"font-family: Roboto;\"> &ldquo;Castes in India&rdquo; (1916), &ldquo;The Problem of the Rupee&rdquo; (1923), &ldquo;Bahishkrit Bharat&rdquo; (1927), &ldquo;The Annihilation of Caste&rdquo; (1936), &ldquo;Federation Versus Freedom&rdquo; (1939), &ldquo;Thoughts on Pakistan&rdquo; (1940), &ldquo;Ranade, Gandhi and Jinnah&rdquo; (1943), &ldquo;Mr. Gandhi and Emancipation of Untouchables&rdquo; (1943), &ldquo;Pakistan Or Partition Of India&rdquo; (1945), &ldquo;Who were the Shudras&rdquo; (1948), &ldquo;The Untouchables&rdquo; (1948), &ldquo;Buddha Or Karl Marx&rdquo; (1956). </span></p>\\n",
                    solution_hi: "<p><span style=\"font-family: Roboto;\">22</span><span style=\"font-family: Palanquin;\">.(b)<strong> &#2348;&#2368;. &#2310;&#2352;. &#2309;&#2306;&#2348;&#2375;&#2337;&#2325;&#2352;&#2404; &#2309;&#2344;&#2381;&#2351; &#2346;&#2369;&#2360;&#2381;&#2340;&#2325;&#2375;&#2306;</strong></span><span style=\"font-family: Palanquin;\"><strong> - </strong>\"&#2325;&#2366;&#2360;&#2381;&#2335;&#2381;&#2360; &#2311;&#2344; &#2311;&#2306;&#2337;&#2367;&#2351;&#2366;\" (1916), \"&#2342; &#2346;&#2381;&#2352;&#2377;&#2348;&#2381;&#2354;&#2350; &#2321;&#2347; &#2342; &#2352;&#2369;&#2346;&#2368;\" (1923), \"&#2348;&#2361;&#2367;&#2359;&#2381;&#2325;&#2371;&#2340; &#2349;&#2366;&#2352;&#2340;\" (1927), \"&#2342; &#2319;&#2344;&#2367;&#2361;&#2367;&#2354;&#2375;&#2358;&#2344; &#2321;&#2347; &#2325;&#2366;&#2360;&#2381;&#2335;\" (1936), \"&#2347;&#2375;&#2337;&#2352;&#2375;&#2358;&#2344; &#2357;&#2352;&#2381;&#2360;&#2375;&#2360; &#2347;&#2381;&#2352;&#2368;&#2337;&#2350;\" (1939), \"&#2341;&#2377;&#2335;&#2381;&#2360; &#2321;&#2344; &#2346;&#2366;&#2325;&#2367;&#2360;&#2381;&#2340;&#2366;&#2344;\" (1940), \"&#2352;&#2366;&#2344;&#2366;&#2337;&#2375;, &#2327;&#2366;&#2306;&#2343;&#2368; &#2319;&#2339;&#2381;&#2337; &#2332;&#2367;&#2344;&#2381;&#2344;&#2366;\" (1943), \"&#2350;&#2367;&#2360;&#2381;&#2335;&#2352; &#2327;&#2366;&#2306;&#2343;&#2368; &#2319;&#2339;&#2381;&#2337; &#2311;&#2350;&#2376;&#2344;&#2381;&#2360;&#2367;&#2346;&#2375;&#2358;&#2344; &#2321;&#2347; &#2309;&#2344;&#2335;&#2330;&#2375;&#2348;&#2354;&#2381;&#2360; &rdquo; (1943), &ldquo; &#2346;&#2366;&#2325;&#2367;&#2360;&#2381;&#2340;&#2366;&#2344; &#2310;&#2352; &#2346;&#2366;&#2352;&#2381;&#2335;&#2367;&#2358;&#2344; &#2321;&#2347; &#2311;&#2306;&#2337;&#2367;&#2351;&#2366;&rdquo; (1945), &ldquo;&#2357;&#2381;&#2361;&#2370; &#2357;&#2352; &#2342; &#2358;&#2370;&#2342;&#2381;&#2352;&#2360; &rdquo; (1948), &ldquo;&#2342; &#2309;&#2344;&#2335;&#2330;&#2375;&#2348;&#2354;&#2381;&#2360; &rdquo; (1948), &ldquo;&#2348;&#2369;&#2342;&#2381;&#2343; &#2324;&#2352; &#2325;&#2366;&#2352;&#2381;&#2354; &#2350;&#2366;&#2352;&#2381;&#2325;&#2381;&#2360;&rdquo; (1956)&#2404;</span></p>\\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "23",
                    section: "18",
                    question_en: "<p><span style=\"font-family: Roboto;\">23.</span><span style=\"font-family: Roboto;\"> Nag Panchami is celebrated in which month of Hindu Calendar?</span></p>\\n",
                    question_hi: "<p><span style=\"font-family: Roboto;\">23. </span><span style=\"font-family: Palanquin;\">&#2344;&#2366;&#2327; &#2346;&#2306;&#2330;&#2350;&#2368; &#2361;&#2367;&#2306;&#2342;&#2370; &#2325;&#2376;&#2354;&#2375;&#2306;&#2337;&#2352; &#2325;&#2375; &#2325;&#2367;&#2360; &#2350;&#2361;&#2368;&#2344;&#2375; &#2350;&#2375;&#2306; &#2350;&#2344;&#2366;&#2312; &#2332;&#2366;&#2340;&#2368; &#2361;&#2376;?</span></p>\\n",
                    options_en: ["<p>Shravan</p>\\n", "<p>Kartik</p>\\n", 
                                "<p>Sharad</p>\\n", "<p>Asadh</p>\\n"],
                    options_hi: ["<p>&#2358;&#2381;&#2352;&#2366;&#2357;&#2339;</p>\\n", "<p>&#2325;&#2366;&#2352;&#2381;&#2340;&#2367;&#2325;</p>\\n",
                                "<p>&#2358;&#2352;&#2342;</p>\\n", "<p>&#2310;&#2359;&#2366;&#2338;&#2364;</p>\\n"],
                    solution_en: "<p><span style=\"font-family: Roboto;\">23</span><span style=\"font-family: Roboto;\">.(a)&nbsp;</span><strong>Shravan. </strong><span style=\"font-weight: 400;\">Other festivals in Shravan month - Raksha Bandhan, Hariyali Teej, Narali Purnima.</span><strong> Kartik </strong><span style=\"font-weight: 400;\">Month festival - Karwa Chauth, Rama Ekadashi, Dhanteras, Lakshmi Puja (Deewali), Bhaiya Dooj, Chhath Puja. </span><strong>Ashad</strong><span style=\"font-weight: 400;\"> - Jagannath Rathyatra, Guru Purnima, Yogini Ekadashi. </span><strong>Sharad Purnima </strong><span style=\"font-weight: 400;\">is the harvest festival that is celebrated during the holy month of Ashwin in the Hindu lunar calendar.</span></p>\\n",
                    solution_hi: "<p><span style=\"font-family: Roboto;\">23</span><span style=\"font-family: Palanquin;\">.(a) </span><strong>&#2358;&#2381;&#2352;&#2357;&#2339;&#2404; </strong><span style=\"font-weight: 400;\">&#2358;&#2381;&#2352;&#2366;&#2357;&#2339; &#2350;&#2366;&#2360; &#2325;&#2375; &#2309;&#2344;&#2381;&#2351; &#2340;&#2381;&#2351;&#2380;&#2361;&#2366;&#2352; - &#2352;&#2325;&#2381;&#2359;&#2366; &#2348;&#2306;&#2343;&#2344;, &#2361;&#2352;&#2367;&#2351;&#2366;&#2354;&#2368; &#2340;&#2368;&#2332;, &#2344;&#2366;&#2352;&#2354;&#2368; &#2346;&#2370;&#2352;&#2381;&#2339;&#2367;&#2350;&#2366;&#2404; </span><strong>&#2325;&#2366;&#2352;&#2381;&#2340;&#2367;&#2325; </strong><span style=\"font-weight: 400;\">&#2350;&#2366;&#2361; &#2325;&#2375; &#2340;&#2381;&#2351;&#2380;&#2361;&#2366;&#2352; - &#2325;&#2352;&#2357;&#2366; &#2330;&#2380;&#2341;, &#2352;&#2350;&#2366; &#2319;&#2325;&#2366;&#2342;&#2358;&#2368;, &#2343;&#2344;&#2340;&#2375;&#2352;&#2360;, &#2354;&#2325;&#2381;&#2359;&#2381;&#2350;&#2368; &#2346;&#2370;&#2332;&#2366; (&#2342;&#2368;&#2357;&#2366;&#2354;&#2368;), &#2349;&#2376;&#2351;&#2366; &#2342;&#2370;&#2332;, &#2331;&#2336; &#2346;&#2370;&#2332;&#2366;&#2404; </span><strong>&#2310;&#2359;&#2366;&#2338;&#2364; </strong><span style=\"font-weight: 400;\">- &#2332;&#2327;&#2344;&#2381;&#2344;&#2366;&#2341; &#2352;&#2341;&#2351;&#2366;&#2340;&#2381;&#2352;&#2366;, &#2327;&#2369;&#2352;&#2369; &#2346;&#2370;&#2352;&#2381;&#2339;&#2367;&#2350;&#2366;, &#2351;&#2379;&#2327;&#2367;&#2344;&#2368; &#2319;&#2325;&#2366;&#2342;&#2358;&#2368;&#2404; </span><strong>&#2358;&#2352;&#2342; &#2346;&#2370;&#2352;&#2381;&#2339;&#2367;&#2350;&#2366; </strong><span style=\"font-weight: 400;\">&#2347;&#2360;&#2354; &#2313;&#2340;&#2381;&#2360;&#2357; &#2361;&#2376; &#2332;&#2379; &#2361;&#2367;&#2306;&#2342;&#2370; &#2330;&#2306;&#2342;&#2381;&#2352; &#2325;&#2376;&#2354;&#2375;&#2306;&#2337;&#2352; &#2325;&#2375; &#2346;&#2357;&#2367;&#2340;&#2381;&#2352; &#2350;&#2361;&#2368;&#2344;&#2375; &#2309;&#2358;&#2381;&#2357;&#2367;&#2344; &#2325;&#2375; &#2342;&#2380;&#2352;&#2366;&#2344; &#2350;&#2344;&#2366;&#2351;&#2366; &#2332;&#2366;&#2340;&#2366; &#2361;&#2376;&#2404;</span></p>\\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "24",
                    section: "18",
                    question_en: "<p><span style=\"font-family: Roboto;\">24</span><span style=\"font-family: Roboto;\">.__________ became the first swimmer from India to breach the Olympic Qualification Time (A cut) in swimming as he capped the qualification period with an outstanding performance in the Sette Colli Trophy in Rome, Italy in June 2021.</span></p>\\n",
                    question_hi: "<p><span style=\"font-family: Roboto;\">24</span><span style=\"font-family: Palanquin;\">.________ &#2340;&#2376;&#2352;&#2366;&#2325;&#2368; &#2350;&#2375;&#2306; &#2323;&#2354;&#2306;&#2346;&#2367;&#2325; &#2351;&#2379;&#2327;&#2381;&#2351;&#2340;&#2366; &#2360;&#2350;&#2351; (A &#2325;&#2335;) &#2325;&#2366; &#2352;&#2367;&#2325;&#2377;&#2352;&#2381;&#2337; &#2340;&#2379;&#2337;&#2364;&#2344;&#2375; &#2357;&#2366;&#2354;&#2375; &#2349;&#2366;&#2352;&#2340; &#2325;&#2375; &#2346;&#2361;&#2354;&#2375; &#2340;&#2376;&#2352;&#2366;&#2325; &#2348;&#2344; &#2327;&#2319; &#2325;&#2381;&#2351;&#2379;&#2306;&#2325;&#2367; &#2313;&#2344;&#2381;&#2361;&#2379;&#2306;&#2344;&#2375; &#2332;&#2370;&#2344; 2021 &#2350;&#2375;&#2306; &#2352;&#2379;&#2350;, &#2311;&#2335;&#2354;&#2368; &#2350;&#2375;&#2306; &#2360;&#2375;&#2335;&#2375; &#2325;&#2379;&#2354;&#2368; &#2335;&#2381;&#2352;&#2377;&#2347;&#2368; &#2350;&#2375;&#2306; &#2313;&#2340;&#2381;&#2325;&#2371;&#2359;&#2381;&#2335; &#2346;&#2381;&#2352;&#2342;&#2352;&#2381;&#2358;&#2344; &#2325;&#2375; &#2360;&#2366;&#2341; &#2309;&#2352;&#2381;&#2361;&#2340;&#2366; &#2309;&#2357;&#2343;&#2367; &#2325;&#2379; &#2346;&#2370;&#2352;&#2366; &#2325;&#2367;&#2351;&#2366;&#2404;</span></p>\\n",
                    options_en: ["<p>Sajan Prakash</p>\\n", "<p>Srihari Nataraj</p>\\n", 
                                "<p>Anand Anilkumar Shylaja</p>\\n", "<p>Virdhawal Khade</p>\\n"],
                    options_hi: ["<p>&#2360;&#2366;&#2332;&#2344; &#2346;&#2381;&#2352;&#2325;&#2366;&#2358;</p>\\n", "<p>&#2358;&#2381;&#2352;&#2368;&#2361;&#2352;&#2367; &#2344;&#2335;&#2352;&#2366;&#2332;</p>\\n",
                                "<p>&#2310;&#2344;&#2306;&#2342; &#2309;&#2344;&#2367;&#2354;&#2325;&#2369;&#2350;&#2366;&#2352; &#2358;&#2376;&#2354;&#2332;&#2366;</p>\\n", "<p>&#2357;&#2368;&#2352;&#2343;&#2357;&#2354; &#2326;&#2366;&#2337;&#2364;&#2375;</p>\\n"],
                    solution_en: "<p><span style=\"font-family: Roboto;\">24</span><span style=\"font-family: Roboto;\">.(a) <strong>Sajan Prakash -</strong></span><span style=\"font-family: Roboto;\"> He won gold medal in 100m butterfly stroke and silver medal in 200m freestyle at 36th National Games held in Gujarat. Srihari Nataraj, Anand Anilkumar Shylaja and Virdhawal Khade are swimmers.</span></p>\\n",
                    solution_hi: "<p><span style=\"font-family: Roboto;\">24</span><span style=\"font-family: Palanquin;\">.(a) <strong>&#2360;&#2366;&#2332;&#2344; &#2346;&#2381;&#2352;&#2325;&#2366;&#2358; </strong>- </span><span style=\"font-family: Palanquin;\">&#2313;&#2344;&#2381;&#2361;&#2379;&#2306;&#2344;&#2375; &#2327;&#2369;&#2332;&#2352;&#2366;&#2340; &#2350;&#2375;&#2306; &#2310;&#2351;&#2379;&#2332;&#2367;&#2340; 36&#2357;&#2375;&#2306; &#2352;&#2366;&#2359;&#2381;&#2335;&#2381;&#2352;&#2368;&#2351; &#2326;&#2375;&#2354;&#2379;&#2306; &#2350;&#2375;&#2306; 100 &#2350;&#2368;&#2335;&#2352; &#2348;&#2335;&#2352;&#2347;&#2381;&#2354;&#2366;&#2312; &#2360;&#2381;&#2335;&#2381;&#2352;&#2379;&#2325; &#2350;&#2375;&#2306; &#2360;&#2381;&#2357;&#2352;&#2381;&#2339; &#2346;&#2342;&#2325; &#2324;&#2352; 200 &#2350;&#2368;&#2335;&#2352; &#2347;&#2381;&#2352;&#2368;&#2360;&#2381;&#2335;&#2366;&#2311;&#2354; &#2350;&#2375;&#2306; &#2352;&#2332;&#2340; &#2346;&#2342;&#2325; &#2332;&#2368;&#2340;&#2366;&#2404; &#2358;&#2381;&#2352;&#2368;&#2361;&#2352;&#2367; &#2344;&#2335;&#2352;&#2366;&#2332;, &#2310;&#2344;&#2306;&#2342; &#2309;&#2344;&#2367;&#2354;&#2325;&#2369;&#2350;&#2366;&#2352; &#2358;&#2376;&#2354;&#2332;&#2366; &#2324;&#2352; &#2357;&#2368;&#2352;&#2343;&#2357;&#2354; &#2326;&#2366;&#2337;&#2364;&#2375; &#2340;&#2376;&#2352;&#2366;&#2325; &#2361;&#2376;&#2306;&#2404;</span></p>\\r\\n<p><span style=\"font-family: Roboto;\"> </span></p>\\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "25",
                    section: "misc",
                    question_en: "<p><span style=\"font-family: Roboto;\">25. </span><span style=\"font-family: Roboto;\">Who is the regulator of Micro Finance Institutions in India?</span></p>\\n",
                    question_hi: "<p><span style=\"font-family: Roboto;\">25. </span><span style=\"font-family: Palanquin;\">&#2349;&#2366;&#2352;&#2340; &#2350;&#2375;&#2306; &#2360;&#2370;&#2325;&#2381;&#2359;&#2381;&#2350; &#2357;&#2367;&#2340;&#2381;&#2340; &#2360;&#2306;&#2360;&#2381;&zwj;&#2341;&#2366;&#2323;&#2306; &#2325;&#2366; &#2357;&#2367;&#2344;&#2367;&#2351;&#2366;&#2350;&#2325; &#2325;&#2380;&#2344; &#2361;&#2376;?</span></p>\\n",
                    options_en: ["<p>SEBI</p>\\n", "<p>RBI</p>\\n", 
                                "<p>NABARD</p>\\n", "<p>SBI</p>\\n"],
                    options_hi: ["<p><span style=\"font-weight: 400;\">&#2360;&#2375;&#2348;&#2368; (SEBI)</span></p>\\n", "<p>&#2310;&#2352;.&#2348;&#2368;.&#2310;&#2312;. (RBI)</p>\\n",
                                "<p><span style=\"font-weight: 400;\">&#2344;&#2366;&#2348;&#2366;&#2352;&#2381;&#2337; (NABARD)</span></p>\\n", "<p><span style=\"font-weight: 400;\">&#2319;&#2360;.&#2348;&#2368;.&#2310;&#2312;. (SBI)</span></p>\\n"],
                    solution_en: "<p><span style=\"font-family: Roboto;\">25</span><span style=\"font-family: Roboto;\">.(b) </span><strong>RBI </strong><span style=\"font-weight: 400;\">(Reserve Bank of India)</span><strong>. Establishment - </strong><span style=\"font-weight: 400;\">1st</span><strong> </strong><span style=\"font-weight: 400;\">April 1935.</span><strong> Headquarter - </strong><span style=\"font-weight: 400;\">&nbsp;Mumbai.</span><strong> Functions of RBI:- </strong><span style=\"font-weight: 400;\">It implements and monitors the monetary policy and ensures price stability, Manager of Foreign Exchange, Issuer of Currency, Financial Inclusion. </span><strong>SEBI</strong><span style=\"font-weight: 400;\"> (Securities and Exchange Board of India) -&nbsp; Established in 1988 as a non-statutory body. </span><strong>NABARD</strong><span style=\"font-weight: 400;\"> (National Bank for Agriculture and Rural Development) - Established in 1982. </span><strong>SBI </strong><span style=\"font-weight: 400;\">(State Bank of India) - Established on 1 July 1955.</span></p>\\n",
                    solution_hi: "<p><span style=\"font-family: Roboto;\">25</span><span style=\"font-family: Roboto;\">.(b)&nbsp;</span><strong>&nbsp;RBI </strong><span style=\"font-weight: 400;\">(&#2349;&#2366;&#2352;&#2340;&#2368;&#2351; &#2352;&#2367;&#2332;&#2352;&#2381;&#2357; &#2348;&#2376;&#2306;&#2325;)&#2404; </span><strong>&#2360;&#2381;&#2341;&#2366;&#2346;&#2344;&#2366; </strong><span style=\"font-weight: 400;\">- 1 &#2309;&#2346;&#2381;&#2352;&#2376;&#2354; 1935&#2404; </span><strong>&#2350;&#2369;&#2326;&#2381;&#2351;&#2366;&#2354;&#2351; </strong><span style=\"font-weight: 400;\">- &#2350;&#2369;&#2306;&#2348;&#2312;&#2404;</span><strong> RBI &#2325;&#2375; &#2325;&#2366;&#2352;&#2381;&#2351;:</strong><span style=\"font-weight: 400;\"> &#2351;&#2361; &#2350;&#2380;&#2342;&#2381;&#2352;&#2367;&#2325; &#2344;&#2368;&#2340;&#2367; &#2325;&#2379; &#2354;&#2366;&#2327;&#2370; &#2324;&#2352; &#2350;&#2377;&#2344;&#2367;&#2335;&#2352; &#2325;&#2352;&#2340;&#2366; &#2361;&#2376; &#2324;&#2352; &#2350;&#2370;&#2354;&#2381;&#2351; &#2360;&#2381;&#2341;&#2367;&#2352;&#2340;&#2366;, &#2357;&#2367;&#2342;&#2375;&#2358;&#2368; &#2350;&#2369;&#2342;&#2381;&#2352;&#2366; &#2346;&#2381;&#2352;&#2348;&#2306;&#2343;&#2325;, &#2350;&#2369;&#2342;&#2381;&#2352;&#2366; &#2332;&#2366;&#2352;&#2368;&#2325;&#2352;&#2381;&#2340;&#2366;, &#2357;&#2367;&#2340;&#2381;&#2340;&#2368;&#2351; &#2360;&#2350;&#2366;&#2357;&#2375;&#2358;&#2344; &#2360;&#2369;&#2344;&#2367;&#2358;&#2381;&#2330;&#2367;&#2340; &#2325;&#2352;&#2340;&#2366; &#2361;&#2376;&#2404; </span><strong>SEBI</strong><span style=\"font-weight: 400;\"> (&#2349;&#2366;&#2352;&#2340;&#2368;&#2351; &#2346;&#2381;&#2352;&#2340;&#2367;&#2349;&#2370;&#2340;&#2367; &#2324;&#2352; &#2357;&#2367;&#2344;&#2367;&#2350;&#2351; &#2348;&#2379;&#2352;&#2381;&#2337;) - 1988 &#2350;&#2375;&#2306; &#2319;&#2325; &#2327;&#2376;&#2352;-&#2357;&#2376;&#2343;&#2366;&#2344;&#2367;&#2325; &#2344;&#2367;&#2325;&#2366;&#2351; &#2325;&#2375; &#2352;&#2370;&#2346; &#2350;&#2375;&#2306; &#2360;&#2381;&#2341;&#2366;&#2346;&#2367;&#2340;&#2404; </span><strong>NABARD</strong><span style=\"font-weight: 400;\"> (&#2352;&#2366;&#2359;&#2381;&#2335;&#2381;&#2352;&#2368;&#2351; &#2325;&#2371;&#2359;&#2367; &#2324;&#2352; &#2327;&#2381;&#2352;&#2366;&#2350;&#2368;&#2339; &#2357;&#2367;&#2325;&#2366;&#2360; &#2348;&#2376;&#2306;&#2325;) - 1982 &#2350;&#2375;&#2306; &#2360;&#2381;&#2341;&#2366;&#2346;&#2367;&#2340;&#2404; </span><strong>SBI </strong><span style=\"font-weight: 400;\">(&#2349;&#2366;&#2352;&#2340;&#2368;&#2351; &#2360;&#2381;&#2335;&#2375;&#2335; &#2348;&#2376;&#2306;&#2325;) - 1 &#2332;&#2369;&#2354;&#2366;&#2312; 1955 &#2325;&#2379; &#2360;&#2381;&#2341;&#2366;&#2346;&#2344;&#2366; &#2325;&#2368; &#2327;&#2312;&#2404;</span></p>\\r\\n<p><span style=\"font-family: Roboto;\"> </span></p>\\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>