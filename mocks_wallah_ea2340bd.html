<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">10:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 10</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">10</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 10 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 0,
                end: 8
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 9,
                end: 9
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "17",
                    question_en: "<p>1. Three partners invested in a business in the ratio 6 : 7 : 3. They invested their capitals for 8 months, 3 months and 10 months, respectively. What was the ratio of their profits?</p>",
                    question_hi: "<p>1. तीन साझेदारों ने एक व्यवसाय में 6 : 7 : 3 के अनुपात में निवेश किया। उन्होंने अपनी पूंजी क्रमशः 8 महीने, 3 महीने और 10 महीने के लिए निवेश की। उनके लाभ का अनुपात कितना था?</p>",
                    options_en: ["<p>17:7:10</p>", "<p>14:7:10</p>", 
                                "<p>16:7:10</p>", "<p>13:7:10</p>"],
                    options_hi: ["<p>17:7:10</p>", "<p>14:7:10</p>",
                                "<p>16:7:10</p>", "<p>13:7:10</p>"],
                    solution_en: "<p>1.(c) Let three partners A , B and C invested in a business <br>Persons <math display=\"inline\"><mo>&#8594;</mo></math>&nbsp; &nbsp; &nbsp; A&nbsp; : B&nbsp; : C<br>Investment <math display=\"inline\"><mo>&#8594;</mo></math> 6&nbsp; :&nbsp; 7&nbsp; : 3<br>Time&nbsp; &nbsp; &nbsp;<math display=\"inline\"><mo>&#8594;</mo></math>&nbsp; &nbsp; &nbsp; &nbsp;8&nbsp; :&nbsp; 3&nbsp; : 10<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &mdash;---------------------------<br>Profit <math display=\"inline\"><mo>&#8594;</mo></math> 48 : 21 : 30 Or 16 : 7 : 10</p>",
                    solution_hi: "<p>1.(c) मान लीजिए कि तीन साझेदार A, B और C ने एक व्यवसाय में निवेश किया है <br>व्यक्ति <math display=\"inline\"><mo>&#8594;</mo></math>&nbsp; A :&nbsp; B&nbsp; : C<br>निवेश <math display=\"inline\"><mo>&#8594;</mo></math>&nbsp; 6&nbsp; :&nbsp; 7&nbsp; : 3<br>समय <math display=\"inline\"><mo>&#8594;</mo></math>&nbsp; 8&nbsp; :&nbsp; 3&nbsp; &nbsp;: 10<br>&nbsp; &nbsp; &nbsp;&mdash;---------------------------<br>लाभ <math display=\"inline\"><mo>&#8594;</mo></math> 48 : 21 : 30 या 16 : 7 : 10</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "2",
                    section: "17",
                    question_en: "<p>2. Parvati and Pari invest in a business in the ratio 7 : 15. If total profit is Rs. 1166, then what is&nbsp;difference between the profit (in Rs.) of Parvati and Pari?</p>",
                    question_hi: "2. पार्वती और परी एक व्यवसाय में 7:15 के अनुपात में निवेश करती हैं। यदि कुल लाभ ₹1166 है, तो पार्वती और परी को प्राप्त लाभों (₹ में) में कितना अंतर है?",
                    options_en: [" 274  ", " 324   ", 
                                " 524  ", " 424 "],
                    options_hi: [" 274  ", " 324   ",
                                " 524  ", " 424 "],
                    solution_en: "<p>2.(d) Parvati&nbsp; : Pari<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;7&nbsp; &nbsp;: 15<br>Total invest ratio = 7 + 15 = 22 unit <br>Total profit = Rs. 1166<br>Parvati&rsquo;s profit = <math display=\"inline\"><mfrac><mrow><mn>7</mn></mrow><mrow><mn>22</mn></mrow></mfrac></math> &times; 1166 = 371 Rs.<br>Pari&rsquo;s profit = <math display=\"inline\"><mfrac><mrow><mn>15</mn></mrow><mrow><mn>22</mn></mrow></mfrac></math>&nbsp;&times; 1166 = 795 Rs.<br>Difference = 795 &minus; 371 = 424 Rs.</p>",
                    solution_hi: "<p>2.(d) पार्वती&nbsp; :&nbsp; परी<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;7&nbsp; &nbsp; :&nbsp; 15<br>कुल निवेश अनुपात = 7 + 15 = 22 इकाई <br>कुल लाभ = Rs. 1166<br>पार्वती का लाभ = <math display=\"inline\"><mfrac><mrow><mn>7</mn></mrow><mrow><mn>22</mn></mrow></mfrac></math> &times; 1166 = 371 Rs.<br>परी का लाभ = <math display=\"inline\"><mfrac><mrow><mn>15</mn></mrow><mrow><mn>22</mn></mrow></mfrac></math> &times; 1166 = 795 Rs.<br>अंतर = 795 &minus; 371 = 424 Rs.</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "3",
                    section: "17",
                    question_en: "<p>3. Isha and Anshu together invested ₹44000 in a business. At the end of the year, out of a total&nbsp;profit of ₹8800, Isha\'s share was ₹1100. What was the investment of Anshu?</p>",
                    question_hi: "<p>3. ईशा और अंशू ने मिलकर एक व्यवसाय में ₹44000 का निवेश किया। वर्ष के अंत में, ₹8800 के कुल लाभ में से, ईशा का हिस्सा ₹1100 था I अंशु ने कितना निवेश किया था?</p>",
                    options_en: ["<p>₹40115</p>", "<p>₹38500</p>", 
                                "<p>₹37420</p>", "<p>₹38110</p>"],
                    options_hi: ["<p>₹40115</p>", "<p>₹38500</p>",
                                "<p>₹37420</p>", "<p>₹38110</p>"],
                    solution_en: "<p>3.(b) Total profit = ₹8800<br>Isha&rsquo;s share = ₹1100<br>The ratio of Isha&rsquo;s share to the total profit = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1100</mn><mn>8800</mn></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>1</mn><mn>8</mn></mfrac></mstyle></math><br>So, Anshu\'s investment ratio = <math display=\"inline\"><mfrac><mrow><mn>7</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math> (since their total investment ratio must be 1).<br>Total investment = ₹44,000.<br>Now, Anshu&rsquo;s investment is <math display=\"inline\"><mfrac><mrow><mn>7</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math> of ₹44,000:<br>Anshu&rsquo;s investment = <math display=\"inline\"><mfrac><mrow><mn>7</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math> &times; 44,000 = 38,500</p>",
                    solution_hi: "<p>3.(b) कुल लाभ = ₹8800<br>ईशा का हिस्सा = ₹1100<br>ईशा के लाभ का अनुपात कुल लाभ से = <math display=\"inline\"><mfrac><mrow><mn>1100</mn></mrow><mrow><mn>8800</mn></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>1</mn><mn>8</mn></mfrac></mstyle></math><br>इसलिए, अंशु का निवेश अनुपात = <math display=\"inline\"><mfrac><mrow><mn>7</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math> होगा (क्योंकि कुल निवेश का अनुपात 1 है)<br>कुल निवेश = ₹44,000.<br>अब, अंशु का निवेश ₹44,000 का <math display=\"inline\"><mfrac><mrow><mn>7</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math>​ होगा<br>अंशु का निवेश = <math display=\"inline\"><mfrac><mrow><mn>7</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math> &times; 44,000 = 38,500</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "4",
                    section: "17",
                    question_en: "<p>4. Mamta and Geeta invest in a business in the ratio 1 : 11. If total profit is Rs. 4500, then what is the difference between the profit (in Rs.) of Mamta and Geeta?</p>",
                    question_hi: "<p>4. ममता और गीता एक व्यवसाय में 1:11 के अनुपात में निवेश करती हैं। यदि कुल लाभ ₹4500 है, तो ममता और गीता को प्राप्त लाभों (₹ में) में कितना अंतर है?</p>",
                    options_en: ["<p>3900</p>", "<p>3850</p>", 
                                "<p>3750</p>", "<p>3700</p>"],
                    options_hi: ["<p>3900</p>", "<p>3850</p>",
                                "<p>3750</p>", "<p>3700</p>"],
                    solution_en: "<p>4.(c)&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; Mamta&nbsp; :&nbsp; Geeta<br>&nbsp; Investment <math display=\"inline\"><mo>&#8594;</mo></math> 1&nbsp; &nbsp;:&nbsp; &nbsp;11<br>Total invest ratio = 1 + 11 = 12<br>Total profit = Rs. 4500<br>Mamta&rsquo;s profit = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>12</mn></mrow></mfrac></math> &times; 4500 = 375<br>Geeta&rsquo;s profit = <math display=\"inline\"><mfrac><mrow><mn>11</mn></mrow><mrow><mn>12</mn></mrow></mfrac></math> &times; 4500 = 4125<br>Difference = 4125 &minus; 375 = 3750 Rs.</p>",
                    solution_hi: "<p>4.(c)&nbsp; &nbsp; ममता&nbsp; :&nbsp; गीता<br>निवेश <math display=\"inline\"><mo>&#8594;</mo></math> 1&nbsp; &nbsp;:&nbsp; 11<br>कुल निवेश अनुपात = 1 + 11 = 12<br>कुल लाभ = रु. 4500<br>ममता का लाभ = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>12</mn></mrow></mfrac></math> &times; 4500 = 375<br>गीता का लाभ = <math display=\"inline\"><mfrac><mrow><mn>11</mn></mrow><mrow><mn>12</mn></mrow></mfrac></math> &times; 4500 = 4125<br>अंतर = 4125 &minus; 375 = रु. 3750</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "5",
                    section: "17",
                    question_en: "<p>5. Three partners invested in a business in the ratio 6 : 3 : 2. They invested their capitals for 7 months, 4 months and 3 months, respectively. What was the ratio of their profits?</p>",
                    question_hi: "<p>5. तीन साझेदारों ने एक व्यवसाय में 6 : 3 : 2 के अनुपात में निवेश किया। उन्होंने अपनी पूंजी क्रमशः 7 महीने, 4 महीने और 3 महीने के लिए निवेश की। उनके लाभ का अनुपात कितना था? ,</p>",
                    options_en: ["<p>8:2:1</p>", "<p>7:2:1</p>", 
                                "<p>6:2:1</p>", "<p>10:2:1</p>"],
                    options_hi: ["<p>8:2:1</p>", "<p>7:2:1</p>",
                                "<p>6:2:1</p>", "<p>10:2:1</p>"],
                    solution_en: "<p>5.(b) Profit = (Investment &times; Time),<br>Partner 1 : 6 &times; 7 = 42<br>Partner 2 : 3 &times; 4 = 12 <br>Partner 3 : 2 &times; 3 = 6 <br>Profit ratio : 42 : 12 : 6 = 7 : 2 : 1</p>",
                    solution_hi: "<p>5.(b) लाभ = (निवेश &times; समय ),<br>साझेदार 1 : 6 &times; 7 = 42<br>साझेदार 2 : 3 &times; 4 = 12 <br>साझेदार 3 : 2 &times; 3 = 6 <br>लाभ का अनुपात : 42 : 12 : 6 = 7 : 2 : 1</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "6",
                    section: "17",
                    question_en: "<p>6. Three partners invested in a business in the ratio 2 : 7 : 8. They invested their capitals for 4 months, 1 months and 10 months, respectively. What was the ratio of their profits?</p>",
                    question_hi: "<p>6. तीन साझेदारों ने एक व्यवसाय में 2 : 7 : 8 के अनुपात में निवेश किया। उन्होंने अपनी पूंजी क्रमशः 4 महीने, 1 महीने और 10 महीने के लिए निवेश की। उनके लाभ का अनुपात कितना था?</p>",
                    options_en: ["<p>8 : 7 : 80</p>", "<p>10 : 7 : 80</p>", 
                                "<p>6 : 7 : 80</p>", "<p>7 : 7 : 80</p>"],
                    options_hi: ["<p>8 : 7 : 80</p>", "<p>10 : 7 : 80</p>",
                                "<p>6 : 7 : 80</p>", "<p>7 : 7 : 80</p>"],
                    solution_en: "<p>6.(a) Profit = Investment &times; Time,<br>Partner 1 : 2 &times; 4 = 8<br>Partner 2 : 7 &times; 1 = 7 <br>Partner 3 : 8 &times; 10 = 80<br>Profit ratio = 8 : 7 : 80</p>",
                    solution_hi: "<p>6.(a) लाभ = निवेश &times; समय ,<br>साझेदार 1 : 2 &times; 4 = 8<br>साझेदार 2 : 7 &times; 1 = 7 <br>साझेदार 3 : 8 &times; 10 = 80<br>लाभ का अनुपात = 8 : 7 : 80</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "7",
                    section: "17",
                    question_en: "<p>7. Isha and Anshu together invested ₹44300 in a business. At the end of the year, out of a total profit of ₹7000, Isha\'s share was ₹1400. What was the investment of Anshu?</p>",
                    question_hi: "<p>7. ईशा और अंशू ने मिलकर एक व्यवसाय में ₹44300 का निवेश किया। वर्ष के अंत में, ₹7000 के कुल लाभ में से, ईशा का हिस्सा ₹1400 था। अंशू ने कितना निवेश किया था?</p>",
                    options_en: ["<p>₹37060</p>", "<p>₹35440</p>", 
                                "<p>₹35190</p>", "<p>₹33845</p>"],
                    options_hi: ["<p>₹37060</p>", "<p>₹35440</p>",
                                "<p>₹35190</p>", "<p>₹33845</p>"],
                    solution_en: "<p>7.(b) Total profit = ₹7000<br>Isha&rsquo;s share = ₹1400<br>The ratio of Isha&rsquo;s share to the total profit = <math display=\"inline\"><mfrac><mrow><mn>1400</mn></mrow><mrow><mn>7000</mn></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>1</mn><mn>5</mn></mfrac></mstyle></math><br>So, Anshu\'s investment ratio = <math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> (since their total investment ratio must be 1).<br>Total investment = ₹44,300.<br>Now, Anshu&rsquo;s investment is <math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math>​ of ₹44,300:<br>Anshu&rsquo;s investment = <math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> &times; 44,300 = 35,440</p>",
                    solution_hi: "<p>7.(b) कुल लाभ = ₹7000<br>ईशा का हिस्सा = ₹1400<br>ईशा के लाभ का अनुपात कुल लाभ से = <math display=\"inline\"><mfrac><mrow><mn>1400</mn></mrow><mrow><mn>7000</mn></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>1</mn><mn>5</mn></mfrac></mstyle></math><br>इसलिए, अंशु का निवेश अनुपात = <math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> होगा (क्योंकि कुल निवेश का अनुपात 1 है)<br>कुल निवेश = ₹44,300.<br>अब, अंशु का निवेश ₹44,300 का <math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math>​ होगा<br>अंशु का निवेश = <math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> &times; 44,300 = 35,440</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "8",
                    section: "17",
                    question_en: "<p>8. Trupti and Suhani invest in a business in the ratio 2 : 6. If total profit is Rs. 2480, then what is the difference between the profit (in Rs.) of Trupti and Suhani ?</p>",
                    question_hi: "<p>8. तृप्ति और सुहानी एक व्यवसाय में 2 : 6 के अनुपात में निवेश करती हैं। यदि कुल लाभ ₹2480 है, तो तृप्ति और सुहानी को प्राप्त लाभों (₹ में) में कितना अंतर है ?</p>",
                    options_en: ["<p>1190</p>", "<p>1240</p>", 
                                "<p>1340</p>", "<p>1390</p>"],
                    options_hi: ["<p>1190</p>", "<p>1240</p>",
                                "<p>1340</p>", "<p>1390</p>"],
                    solution_en: "<p>8.(b) Ratio of investment <math display=\"inline\"><mo>&#8594;</mo></math> 2 : 6<br>According to question,<br>8 units = 2480<br>(6 - 2) = 4 units = <math display=\"inline\"><mfrac><mrow><mn>2480</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math> &times; 4 = ₹ 1240</p>",
                    solution_hi: "<p>8.(b) निवेश का अनुपात <math display=\"inline\"><mo>&#8594;</mo></math> 2 : 6<br>प्रश्न के अनुसार,<br>8 इकाई = 2480<br>(6 - 2) = 4 इकाई = <math display=\"inline\"><mfrac><mrow><mn>2480</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math> &times; 4 = ₹ 1240</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "9",
                    section: "17",
                    question_en: "<p>9. Three partners invested in a business in the ratio 8 : 4 : 6. They invested their capitals for 10 months, 4 months and 12 months, respectively. What was the ratio of their profits ?</p>",
                    question_hi: "<p>9. तीन साझेदारों ने एक व्यवसाय में 8 : 4 : : 6 के अनुपात में निवेश किया। उन्होंने अपनी पूंजी क्रमशः 10 महीने, 4 महीने और 12 महीने के लिए निवेश की। उनके लाभ का अनुपात कितना था ?</p>",
                    options_en: ["<p>11 : 2 : 9</p>", "<p>8 : 2 : 9</p>", 
                                "<p>10 : 2 : 9</p>", "<p>12 : 2 : 9</p>"],
                    options_hi: ["<p>11 : 2 : 9</p>", "<p>8 : 2 : 9</p>",
                                "<p>10 : 2 : 9</p>", "<p>12 : 2 : 9</p>"],
                    solution_en: "<p>9.(c) Ratio of investment <math display=\"inline\"><mo>&#8594;</mo></math> 8 : 4 : 6<br>Investment their capital for 10 months, 4 months and 12 months<br>Ratio of investment <math display=\"inline\"><mo>&#8594;</mo></math> 8 &times; 10 : 4 &times; 4 : 6 &times; 12 or 10 : 2 : 9<br>So, Ratio of profit <math display=\"inline\"><mo>=</mo></math> 10 : 2 : 9</p>",
                    solution_hi: "<p>9.(c) निवेश का अनुपात <math display=\"inline\"><mo>&#8594;</mo></math> 8 : 4 : 6<br>10 महीने, 4 महीने और 12 महीने के लिए उनकी पूंजी का निवेश करने पर,<br>निवेश का अनुपात <math display=\"inline\"><mo>&#8594;</mo></math> 8 &times; 10 : 4 &times; 4 : 6 &times; 12 or 10 : 2 : 9<br>तो, लाभ का अनुपात <math display=\"inline\"><mo>=</mo></math> 10 : 2 : 9</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "10",
                    section: "misc",
                    question_en: "<p>10. Three partners invested in a business in the ratio 5 : 1 : 9. They invested their capitals for 2 months, 9 months and 11 months, respectively. What was the ratio of their profits?</p>",
                    question_hi: "<p>10. तीन साझेदारों ने एक व्यवसाय में 5: 1: 9 केअनुपात में निवेश किया। उन्होंने अपनी पूंजी क्रमशः 2 महीने, 9 महीने और 11 महीने के लिए निवेश की। उनके लाभ का अनुपात कितना था?</p>",
                    options_en: ["<p>9 : 9 : 99</p>", "<p>13 : 9 : 99</p>", 
                                "<p>10 : 9 : 99</p>", "<p>8 : 9 : 99</p>"],
                    options_hi: ["<p>9 : 9 : 99</p>", "<p>13 : 9 : 99</p>",
                                "<p>10 : 9 : 99</p>", "<p>8 : 9 : 99</p>"],
                    solution_en: "<p>10.(c) Ratio of investment <math display=\"inline\"><mo>&#8594;</mo></math> 5 : 1 : 9<br>Investment their capital for 2 months, 9 months and 11 months<br>Ratio of investment <math display=\"inline\"><mo>&#8594;</mo></math> 5 &times; 2 : 1 &times; 9 : 9 &times; 11 or 10 : 9 : 99<br>Profit &prop; Investment<br>So, Ratio of profit <math display=\"inline\"><mo>=</mo></math> 10 : 9 : 99</p>",
                    solution_hi: "<p>10.(c) निवेश का अनुपात <math display=\"inline\"><mo>&#8594;</mo></math> 5 : 1 : 9<br>2 महीने, 9 महीने और 11 महीने के लिए उनकी पूंजी का निवेश करने पर,<br>निवेश का अनुपात <math display=\"inline\"><mo>&#8594;</mo></math> 5 &times; 2 : 1 &times; 9 : 9 &times; 11 या 10 : 9 : 99<br>लाभ &prop; निवेश<br>तो, लाभ का अनुपात <math display=\"inline\"><mo>=</mo></math> 10 : 9 : 99</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>