<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">25:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 21</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">21</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 25 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 0,
                end: 19
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 20,
                end: 20
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "17",
                    question_en: "<p>1. A can finish a piece of work in 16 days and B can finish it in 12 days. They worked together for 4 days and then A left. B finished the remaining work. For how many total number of days did B work to finish the work completely?</p>",
                    question_hi: "<p>1.<span style=\"font-family: Palanquin Dark;\"> A एक कार्य को 16 दिनों में समाप्त कर सकता है और B उसे 12 दिनों में समाप्त कर सकता है। उन्होंने 4 दिनों तक एक साथ काम किया और फिर A चला गया। B ने शेष कार्य पूरा किया। कार्य को पूर्ण रूप से समाप्त करने के लिए B ने कितने दिनों तक कार्य किया ?</span></p>",
                    options_en: ["<p>6</p>", "<p>9</p>", 
                                "<p>4</p>", "<p>8</p>"],
                    options_hi: ["<p>6</p>", "<p>9</p>",
                                "<p>4</p>", "<p>8</p>"],
                    solution_en: "<p>1.(b)<br><span style=\"font-family: Palanquin Dark;\"><strong>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;A&nbsp; &nbsp;:&nbsp; &nbsp;B</strong></span><br><span style=\"font-family: Palanquin Dark;\">Efficiency :&nbsp; (3) : (4)</span><br><span style=\"font-family: Palanquin Dark;\">Time taken : 16 : 12</span><br><span style=\"font-family: Palanquin Dark;\">Total work = LCM of time taken = LCM (16, 12) = 48 units</span><br><span style=\"font-family: Palanquin Dark;\">Work completed by A and B together in 4 days = </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><mn>3</mn><mo>+</mo><mn>4</mn><mo>)</mo><mo>&#215;</mo><mn>4</mn><mo>&#160;</mo></math><span style=\"font-family: Palanquin Dark;\"> = 28 units</span><br><span style=\"font-family: Palanquin Dark;\">Time taken by B to finish the remaining work alone </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>=</mo><mfrac><mrow><mn>48</mn><mo>-</mo><mn>28</mn></mrow><mn>4</mn></mfrac><mo>=</mo><mfrac><mn>20</mn><mn>4</mn></mfrac></math><span style=\"font-family: Palanquin Dark;\"> = 5 days</span><br><span style=\"font-family: Palanquin Dark;\">So, required time = 4 + 5 = 9 days</span></p>",
                    solution_hi: "<p>1.(b)<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; <strong>A&nbsp;&nbsp;&nbsp; :&nbsp;&nbsp;&nbsp;&nbsp; B </strong>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<br>दक्षता&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; (3)&nbsp;&nbsp; :&nbsp;&nbsp; (4)&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<br>लिया गया समय&nbsp; &nbsp; &nbsp; &nbsp;16&nbsp;&nbsp; :&nbsp;&nbsp; 12&nbsp;<br><span style=\"font-family: Palanquin Dark;\">कुल कार्य = लिए गए समय का LCM = LCM (16, 12) = 48 इकाई</span><br><span style=\"font-family: Palanquin Dark;\">A और B द्वारा 4 दिनों में पूरा किया गया कार्य = </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><mn>3</mn><mo>+</mo><mn>4</mn><mo>)</mo><mo>&#215;</mo><mn>4</mn><mo>&#160;</mo></math><span style=\"font-family: Palanquin Dark;\">= 28 इकाई</span><br><span style=\"font-family: Palanquin Dark;\">शेष कार्य को अकेले पूरा करने में B द्वारा लिया गया समय = </span><span style=\"font-family: Palanquin Dark;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>48</mn><mo>-</mo><mn>28</mn></mrow><mn>4</mn></mfrac><mo>=</mo><mfrac><mn>20</mn><mn>4</mn></mfrac><mo>&#160;</mo></math> </span><span style=\"font-family: Palanquin Dark;\">&nbsp;= 5 दिन</span><br><span style=\"font-family: Palanquin Dark;\">अत: अभीष्ट समय = 4 + 5 = 9 दिन</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "17",
                    question_en: "<p>2.<span style=\"font-family: Palanquin Dark;\"> X, Y and Z can do a piece of work in 46 days, 92 days and 23 days, respectively. X started the work. Y joined him after 2 days. If Z joined them after 8 days from the beginning, then for how many days did X work?</span></p>",
                    question_hi: "<p>2.<span style=\"font-family: Palanquin Dark;\"> X, Y और Z एक कार्य को क्रमशः 46 दिन, 92 दिन और 23 दिन में पूरा कर सकते हैं। x ने काम शुरू किया। Y, 2 दिनों के बाद उसके साथ जुड़ गया। यदि Z प्रारंभ से 8 दिनों के बाद उनके साथ जुड़ता है, तो X ने कितने दिनों तक कार्य किया?</span></p>",
                    options_en: ["<p>16</p>", "<p>21</p>", 
                                "<p>18</p>", "<p>13</p>"],
                    options_hi: ["<p>16</p>", "<p>21</p>",
                                "<p>18</p>", "<p>13</p>"],
                    solution_en: "<p>2.(c)<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; <strong>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;X&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; Y&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; Z&nbsp;&nbsp; </strong>&nbsp;&nbsp;&nbsp;&nbsp;<br>Efficiency&nbsp; &nbsp; &nbsp; &nbsp;(2)&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; (1)&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; (4)&nbsp;&nbsp;<br>Time taken&nbsp; &nbsp; 46&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; 92&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; 23&nbsp;<br><span style=\"font-family: Palanquin Dark;\">Total work = LCM of time taken = LCM (46, 92, 23) = 92 units</span><br><span style=\"font-family: Palanquin Dark;\">Work done by X in 2 days =</span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>2</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>2</mn><mo>&#160;</mo></math><span style=\"font-family: Palanquin Dark;\"> = 4 units</span><br><span style=\"font-family: Palanquin Dark;\">Work done by X and Y in next 6 days = </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><mn>2</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>1</mn><mo>)</mo><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>6</mn><mo>&#160;</mo></math><span style=\"font-family: Palanquin Dark;\"> = 18 units</span><br><span style=\"font-family: Palanquin Dark;\">Remaining work = 92 &ndash; (4 + 18) = 92 &ndash; 22 = 70 units</span><br><span style=\"font-family: Palanquin Dark;\">Let the number of days Z work = n</span><br><span style=\"font-family: Palanquin Dark;\">So, (2 + 1 + 4) </span><span style=\"font-family: Palanquin Dark;\">&nbsp;n = 70</span><br><span style=\"font-family: Palanquin Dark;\">7n = 70</span><br><span style=\"font-family: Palanquin Dark;\">n = 10 days</span><br><span style=\"font-family: Palanquin Dark;\">Number of days for which X worked = 2 + 6 + 10 = 18 days</span></p>",
                    solution_hi: "<p>2.(c)<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; <strong>&nbsp;&nbsp;&nbsp;&nbsp;X&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; Y&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; Z&nbsp;&nbsp;&nbsp;&nbsp; </strong>&nbsp;&nbsp;<br>दक्षता&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; (2)&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; (1)&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; (4)&nbsp;&nbsp;<br>लिया गया समय&nbsp; &nbsp; &nbsp; 46&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; 92&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; 23<br><span style=\"font-family: Palanquin Dark;\">कुल कार्य = लिए गए समय का LCM = LCM (46, 92, 23) = 92 इकाई</span><br><span style=\"font-family: Palanquin Dark;\">X द्वारा 2 दिनों में किया गया कार्य = </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>2</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>2</mn><mo>&#160;</mo></math><span style=\"font-family: Palanquin Dark;\"> = 4 इकाई</span><br><span style=\"font-family: Palanquin Dark;\">अगले 6 दिनों में X और Y द्वारा किया गया कार्य = </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><mn>2</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>1</mn><mo>)</mo><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>6</mn><mo>&#160;</mo></math><span style=\"font-family: Palanquin Dark;\"> = 18 इकाई</span><br><span style=\"font-family: Palanquin Dark;\">शेष कार्य = 92 &ndash; (4 + 18) = 92 &ndash; 22 = 70 इकाई</span><br><span style=\"font-family: Palanquin Dark;\">माना , Z द्वारा काम को करने में लिया गया समय ( दिनों की संख्या ) = n</span><br><span style=\"font-family: Palanquin Dark;\">तो, (2 + 1 + 4) </span><span style=\"font-family: Palanquin Dark;\">&nbsp;n = 70</span><br><span style=\"font-family: Palanquin Dark;\">7n = 70</span><br><span style=\"font-family: Palanquin Dark;\">n = 10 दिन</span><br><span style=\"font-family: Palanquin Dark;\">&lsquo;X&rsquo; ने जितने दिनों तक काम किया = 2 + 6 + 10 = 18 दिन</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "17",
                    question_en: "<p>3.<span style=\"font-family: Palanquin Dark;\"> A can finish a piece of work in 48 days and B can finish it in 60 days. They work together </span><span style=\"font-family: Palanquin Dark;\">for 12 days and then A goes away. In how much time (in days and hours) will B finish </span><span style=\"font-family: Palanquin Dark;\">25% of the remaining work?</span></p>",
                    question_hi: "<p>3.<span style=\"font-family: Palanquin Dark;\"> A एक काम को 48 दिनों में पूरा कर सकता है और B उसे 60 दिनों में पूरा कर सकता है। वे 12 दिनों तक एक साथ कार्य करते हैं और फिर A चला जाता है। B शेष कार्य का 25% कितने समय में (दिनों और घंटों में) पूरा करेगा?</span></p>",
                    options_en: ["<p><span style=\"font-family: Palanquin Dark;\">6 days and 4 hours</span></p>", "<p>8 days and 8 hours</p>", 
                                "<p>6 days and 6 hours</p>", "<p>8 days and 6 hours</p>"],
                    options_hi: ["<p>6 दिन और 4 घंटे</p>", "<p>8 दिन और 8 घंटे</p>",
                                "<p>6 दिन और 6 घंटे</p>", "<p>8 दिन और 6 घंटे</p>"],
                    solution_en: "<p>3.(d)<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; <strong>&nbsp; &nbsp; &nbsp; A&nbsp;&nbsp;&nbsp;&nbsp; :&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; B&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </strong>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<br>Efficiency&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;(5)&nbsp;&nbsp;&nbsp; :&nbsp;&nbsp;&nbsp;&nbsp; (4)&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<br>Time taken&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;48&nbsp;&nbsp;&nbsp; :&nbsp;&nbsp;&nbsp;&nbsp; 60&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<br><span style=\"font-family: Palanquin Dark;\">Total work = LCM of time taken = LCM (48, 60) = 240 units</span><br><span style=\"font-family: Palanquin Dark;\">Work done by A and B in 12 days </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>=</mo><mo>&#160;</mo><mo>(</mo><mn>5</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>4</mn><mo>)</mo><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>12</mn><mo>&#160;</mo></math><span style=\"font-family: Palanquin Dark;\"> = 108 units</span><br><span style=\"font-family: Palanquin Dark;\">Time taken by B to finish 25% of remaining work</span><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>=</mo><mo>&#160;</mo><mfrac><mrow><mstyle displaystyle=\"true\"><mfrac><mn>25</mn><mn>100</mn></mfrac></mstyle><mo>&#215;</mo><mo>(</mo><mn>240</mn><mo>-</mo><mn>108</mn><mo>)</mo></mrow><mn>4</mn></mfrac><mo>=</mo><mfrac><mrow><mstyle displaystyle=\"true\"><mfrac><mn>1</mn><mn>4</mn></mfrac></mstyle><mo>&#215;</mo><mn>132</mn></mrow><mn>4</mn></mfrac><mo>=</mo><mfrac><mrow><mn>33</mn><mo>&#160;</mo></mrow><mn>4</mn></mfrac><mo>=</mo><mn>8</mn><mfrac><mn>1</mn><mn>4</mn></mfrac><mo>&#160;</mo></math><br><span style=\"font-family: Palanquin Dark;\">= 8 days 6 hours</span></p>",
                    solution_hi: "<p>3.(d)<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; <strong>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;A&nbsp;&nbsp;&nbsp;&nbsp; :&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; B</strong> &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<br>दक्षता&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; (5)&nbsp;&nbsp;&nbsp; :&nbsp;&nbsp;&nbsp;&nbsp; (4)&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<br>लिया गया समय&nbsp; &nbsp; &nbsp;48&nbsp; &nbsp; &nbsp; :&nbsp;&nbsp;&nbsp;&nbsp; 60&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<br><span style=\"font-family: Palanquin Dark;\">कुल कार्य = लिए गए समय का LCM = LCM (48, 60) = 240 इकाई</span><br><span style=\"font-family: Palanquin Dark;\">A और B द्वारा 12 दिनों में किया गया कार्य = (5 + 4) <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#215;</mo></math></span><span style=\"font-family: Palanquin Dark;\"> 12 = 108 इकाई</span><br><span style=\"font-family: Palanquin Dark;\">शेष कार्य का 25% पूरा करने में B द्वारा लिया गया समय</span><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>=</mo><mfrac><mrow><mstyle displaystyle=\"true\"><mfrac><mn>25</mn><mn>100</mn></mfrac></mstyle><mo>&#215;</mo><mo>(</mo><mn>240</mn><mo>&#160;</mo><mo>-</mo><mn>108</mn><mo>)</mo></mrow><mn>4</mn></mfrac><mo>=</mo><mfrac><mrow><mstyle displaystyle=\"true\"><mfrac><mrow><mo>&#160;</mo><mn>1</mn></mrow><mn>4</mn></mfrac></mstyle><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mo>(</mo><mn>132</mn><mo>)</mo></mrow><mn>4</mn></mfrac><mo>=</mo><mo>&#160;</mo><mfrac><mn>33</mn><mn>4</mn></mfrac><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>8</mn><mfrac><mn>1</mn><mn>4</mn></mfrac><mo>=</mo><mo>&#160;</mo></math><span style=\"font-family: Palanquin Dark;\"> 8 दिन 6 घंटे</span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "17",
                    question_en: "<p>4.<span style=\"font-family: Palanquin Dark;\"> A, B and C can do a piece of work </span><span style=\"font-family: Palanquin Dark;\">in 8</span><span style=\"font-family: Palanquin Dark;\">, 10 and 12 days, respectively. After completing the work together, they received Rs. 5,550. What is the share of B(in Rs.) in the amount received?</span></p>",
                    question_hi: "<p>4. <span style=\"font-family: Palanquin Dark;\">A, B और C एक कार्य को क्रमशः 8, 10 और 12 दिनों में पूरा कर सकते हैं। एक साथ काम पूरा करने के बाद, उन्हें 5,550 रुपये मिले। प्राप्त राशि में B का हिस्सा (रुपयों में) कितना है?</span></p>",
                    options_en: ["<p>1,500</p>", "<p>1,580</p>", 
                                "<p>1,800</p>", "<p>1,696</p>"],
                    options_hi: ["<p>1,500</p>", "<p>1,580</p>",
                                "<p>1,800</p>", "<p>1,696</p>"],
                    solution_en: "<p>4.(c)<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; <strong>&nbsp;A&nbsp; &nbsp; &nbsp;:&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; B&nbsp;&nbsp;&nbsp; :&nbsp;&nbsp;&nbsp;&nbsp; C&nbsp;&nbsp;&nbsp; </strong>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<br>Efficiency&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;(15)&nbsp; :&nbsp;&nbsp;&nbsp; (12)&nbsp; :&nbsp; (10)&nbsp; &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<br>Time taken&nbsp; &nbsp; &nbsp; 8&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; :&nbsp;&nbsp; 10&nbsp;&nbsp;&nbsp;&nbsp; :&nbsp; 12<br><span style=\"font-family: Palanquin Dark;\">Total work = LCM of time taken = LCM (8, 10, 12) = 120 units</span><br><span style=\"font-family: Palanquin Dark;\">Ratio of distribution = 15 : 12 : 10</span><br><span style=\"font-family: Palanquin Dark;\">Share of B </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>=</mo><mo>&#160;</mo><mfrac><mn>12</mn><mn>37</mn></mfrac><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>5550</mn><mo>&#160;</mo></math><span style=\"font-family: Palanquin Dark;\"> = </span><span style=\"font-family: Palanquin Dark;\">₹</span><span style=\"font-family: Palanquin Dark;\">1,800</span></p>",
                    solution_hi: "<p>4.(c)<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; <strong>&nbsp;&nbsp;&nbsp;&nbsp;A&nbsp;&nbsp;&nbsp;&nbsp; :&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; B&nbsp;&nbsp;&nbsp; :&nbsp;&nbsp;&nbsp; C&nbsp;&nbsp;&nbsp;&nbsp; &nbsp;</strong> &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<br>दक्षता&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; (15)&nbsp; :&nbsp;&nbsp; (12)&nbsp;&nbsp; :&nbsp;&nbsp; (10)&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<br>लिया गया समय&nbsp; &nbsp; &nbsp; 8&nbsp;&nbsp;&nbsp;&nbsp; :&nbsp;&nbsp;&nbsp; 10&nbsp;&nbsp;&nbsp;&nbsp; :&nbsp; 12<br><span style=\"font-family: Palanquin Dark;\">कुल कार्य = लिए गए समय का LCM = LCM (8, 10, 12) = 120 इकाई</span><br><span style=\"font-family: Palanquin Dark;\">वितरण का अनुपात = 15 : 12 : 10</span><br><span style=\"font-family: Palanquin Dark;\">B का हिस्सा </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>=</mo><mo>&#160;</mo><mfrac><mn>12</mn><mrow><mn>37</mn><mo>&#160;</mo></mrow></mfrac><mo>&#215;</mo><mo>&#160;</mo><mn>5550</mn><mo>&#160;</mo></math><span style=\"font-family: Palanquin Dark;\">= </span><span style=\"font-family: Palanquin Dark;\">₹</span><span style=\"font-family: Palanquin Dark;\">1,800</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "17",
                    question_en: "<p>5. <span style=\"font-family: Palanquin Dark;\">&nbsp;A can complete 25% of a work in 15 days . He works for 15 days and then B alone finishes the remaining work in 30 days. In how many days will A and B working together finish 50% of the same work ?</span></p>",
                    question_hi: "<p>5.<span style=\"font-family: Palanquin Dark;\"> A एक कार्य का 25% भाग 15 दिनों में पूरा कर सकता है। वह 15 दिनों के लिए काम करता है और फिर B अकेले शेष काम को 30 दिनों में पूरा करता है। A और B एक साथ काम करते हुए समान कार्य का 50% कितने दिनों में पूरा करेंगे?</span></p>",
                    options_en: ["<p>24</p>", "<p>20</p>", 
                                "<p>12</p>", "<p>25</p>"],
                    options_hi: ["<p>24</p>", "<p>20</p>",
                                "<p>12</p>", "<p>25</p>"],
                    solution_en: "<p>5.(c) <span style=\"font-family: Palanquin Dark;\">Time taken by A to complete whole work = 15<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#215;</mo></math> </span><span style=\"font-family: Palanquin Dark;\">&nbsp;4 = 60 days</span><br><span style=\"font-family: Palanquin Dark;\">Let efficiency of A = 1, so total work = 60 <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#215;</mo></math></span><span style=\"font-family: Palanquin Dark;\"> 1 = 60 units</span><br><span style=\"font-family: Palanquin Dark;\">Work done by A in 15 days = 15 <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#215;</mo></math></span><span style=\"font-family: Palanquin Dark;\"> 1 = 15 units</span><br><span style=\"font-family: Palanquin Dark;\">Efficiency of B = </span><span style=\"font-family: Palanquin Dark;\"> </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>60</mn><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mn>15</mn></mrow><mn>30</mn></mfrac><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mfrac><mn>45</mn><mn>30</mn></mfrac><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>1</mn><mo>.</mo><mn>5</mn><mo>&#160;</mo></math><br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<strong>&nbsp;&nbsp;&nbsp;&nbsp;A&nbsp;&nbsp;&nbsp;&nbsp; :&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; B&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </strong>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<br>Efficiency&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;(1)&nbsp;&nbsp;&nbsp; :&nbsp;&nbsp;&nbsp; (1.5)&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<br>Time taken&nbsp; &nbsp; &nbsp; &nbsp;60&nbsp;&nbsp;&nbsp; :&nbsp;&nbsp;&nbsp;&nbsp; 40&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<br><span style=\"font-family: Palanquin Dark;\">Required time </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>=</mo><mfrac><mrow><mstyle displaystyle=\"true\"><mfrac><mn>1</mn><mn>2</mn></mfrac></mstyle><mo>&#160;</mo><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>60</mn><mo>&#160;</mo></mrow><mrow><mn>1</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>1</mn><mo>.</mo><mn>5</mn></mrow></mfrac><mo>=</mo><mfrac><mrow><mo>&#160;</mo><mn>30</mn></mrow><mrow><mn>2</mn><mo>.</mo><mn>5</mn></mrow></mfrac><mo>&#160;</mo></math><span style=\"font-family: Palanquin Dark;\">&nbsp;</span><span style=\"font-family: Palanquin Dark;\">= 12 days</span></p>",
                    solution_hi: "<p>5.(c) <span style=\"font-family: Palanquin Dark;\">A द्वारा पूरे कार्य को पूरा करने में लिया गया समय = 15<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#215;</mo></math> </span><span style=\"font-family: Palanquin Dark;\">&nbsp;4 = 60 दिन</span><br><span style=\"font-family: Palanquin Dark;\">माना A = 1 की दक्षता, अत: कुल कार्य = 60<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#215;</mo></math> </span><span style=\"font-family: Palanquin Dark;\">&nbsp;1 = 60 इकाई</span><br><span style=\"font-family: Palanquin Dark;\">A द्वारा 15 दिनों में किया गया कार्य = 15<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#215;</mo></math> </span><span style=\"font-family: Palanquin Dark;\">&nbsp;1 = 15 इकाई</span><br><span style=\"font-family: Palanquin Dark;\">B की दक्षता = </span><span style=\"font-family: Palanquin Dark;\"> </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>60</mn><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mn>15</mn></mrow><mn>30</mn></mfrac><mo>&#160;</mo><mo>=</mo><mfrac><mrow><mo>&#160;</mo><mn>45</mn></mrow><mn>30</mn></mfrac><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>1</mn><mo>.</mo><mn>5</mn><mo>&#160;</mo></math><br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; <strong>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;A&nbsp;&nbsp;&nbsp;&nbsp; :&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; B&nbsp;&nbsp;&nbsp;&nbsp; </strong>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<br>दक्षता&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; (1)&nbsp;&nbsp;&nbsp; :&nbsp;&nbsp; (1.5)&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<br>लिया गया समय&nbsp; &nbsp; &nbsp;60&nbsp;&nbsp;&nbsp; :&nbsp;&nbsp;&nbsp; 40&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<br><span style=\"font-family: Palanquin Dark;\">आवश्यक समय = </span><span style=\"font-family: Palanquin Dark;\"> </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mstyle displaystyle=\"true\"><mfrac><mn>1</mn><mn>2</mn></mfrac></mstyle><mo>&#160;</mo><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>60</mn></mrow><mrow><mn>1</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>1</mn><mo>.</mo><mn>5</mn></mrow></mfrac><mo>&#160;</mo><mo>=</mo><mfrac><mrow><mo>&#160;</mo><mn>30</mn></mrow><mrow><mn>2</mn><mo>.</mo><mn>5</mn><mo>&#160;</mo></mrow></mfrac><mo>=</mo><mo>&#160;</mo></math><span style=\"font-family: Palanquin Dark;\">12 दिन</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "17",
                    question_en: "<p>6.<span style=\"font-family: Palanquin Dark;\"> 5 men and 2 boys can do in 30 days as much work as 7 men and 10 boys can do in 15 days. How many boys should join 40 men to do the same work in 4 days?</span></p>",
                    question_hi: "<p>6.<span style=\"font-family: Palanquin Dark;\"> 5 पुरुष और 2 लड़के 30 दिनों में उतना काम कर सकते हैं जितना कि 7 पुरुष और 10 लड़के 15 दिनों में कर सकते हैं। उसी काम को 4 दिनों में करने के लिए कितने लड़कों को 40 पुरुषों के साथ जुड़ना चाहिए?</span></p>",
                    options_en: ["<p>10</p>", "<p>15</p>", 
                                "<p>12</p>", "<p>14</p>"],
                    options_hi: ["<p>10</p>", "<p>15</p>",
                                "<p>12</p>", "<p>14</p>"],
                    solution_en: "<p>6.(a) <span style=\"font-family: Palanquin Dark;\">Let efficiency of men = M and efficiency of boys = B</span><br><span style=\"font-family: Palanquin Dark;\">According to the question,</span><br>(5M + 2B) 30 = (7M + 10B) 15<br>10M + 4B = 7M + 10B<br>3M = 6B<br>M = 2B<br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>&#160;</mo><mi>M</mi></mrow><mi>B</mi></mfrac><mo>&#160;</mo><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mfrac><mn>2</mn><mn>1</mn></mfrac></math><br><span style=\"font-family: Palanquin Dark;\">Total work = <strong id=\"docs-internal-guid-9eaab264-7fff-1d7b-ce08-79ec041c9303\">[</strong>5M + 2B] 30 = [5(2) + 2(1)] &times; 30 = 12 &times; 30 = 360 units</span><br><span style=\"font-family: Palanquin Dark;\">Let the number of boys required = n</span><br>[40M + n] 4 = 360<br>[40(2) + n] = 90<br>80 + n = 90&nbsp;<br>n = 10 boys</p>",
                    solution_hi: "<p>6.(a) <span style=\"font-family: Palanquin Dark;\">माना पुरुषों की क्षमता = M और लड़कों की दक्षता = B</span><br><span style=\"font-family: Palanquin Dark;\">प्रश्न के अनुसार,<br>(5M + 2B) 30 = (7M + 10B) 15<br>10M + 4B = 7M + 10B<br>3M = 6B<br>M = 2B</span><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>M</mi><mo>&#160;</mo></mrow><mi>B</mi></mfrac><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mfrac><mn>2</mn><mn>1</mn></mfrac></math><br><span style=\"font-family: Palanquin Dark;\">कुल कार्य = </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>[</mo><mn>5</mn><mi>M</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>2</mn><mi>B</mi><mo>]</mo><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>30</mn><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mo>[</mo><mn>5</mn><mo>(</mo><mn>2</mn><mo>)</mo><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>2</mn><mo>(</mo><mn>1</mn><mo>)</mo><mo>]</mo><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>30</mn><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>12</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>30</mn><mo>&#160;</mo></math><span style=\"font-family: Palanquin Dark;\"> = 360 यूनिट</span><br><span style=\"font-family: Palanquin Dark;\">माना लड़कों की संख्या = n<br>[40M + n] 4 = 360<br>[40(2) + n] = 90<br>80 + n = 90&nbsp;<br>n = 10 </span><span style=\"font-family: Palanquin Dark;\">लड़के</span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "17",
                    question_en: "<p>7.<span style=\"font-family: Palanquin Dark;\"> If 4 men and 6 boys can do work in 8 days and 6 men and 4 boys can do the same work in 7 days. Then how many will 5 men and 4 boys take to do the same work?</span></p>",
                    question_hi: "<p>7.<span style=\"font-family: Palanquin Dark;\"> यदि 4 पुरुष और 6 लड़के 8 दिनों में काम कर सकते हैं और 6 पुरुष और 4 लड़के उसी काम को 7 दिनों में कर सकते हैं। तो 5 पुरुष और 4 लड़के समान कार्य करने में कितने लोग लेंगे?</span></p>",
                    options_en: ["<p>6</p>", "<p>8</p>", 
                                "<p>5</p>", "<p>7</p>"],
                    options_hi: ["<p>6</p>", "<p>8</p>",
                                "<p>5</p>", "<p>7</p>"],
                    solution_en: "<p>7.(b) <span style=\"font-family: Palanquin Dark;\">Let the efficiency of men = M and efficiency of boys = B</span><br><span style=\"font-family: Palanquin Dark;\">According to the question,</span><br>(4M + 6B) 8 = (6M + 4B) 7<br>32M + 48B = 42M + 28B<br>10M = 20B<br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>M</mi><mi>B</mi></mfrac><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mfrac><mn>2</mn><mn>1</mn></mfrac></math><br><span style=\"font-family: Times New Roman;\">Total work <strong id=\"docs-internal-guid-1ef1e3dc-7fff-0d2b-8c69-44eba6fa30d6\">&nbsp;</strong>= [4M + 6B] 8 = [4(2) + 6(1)] 8 = 14 8&nbsp; = 112 </span><span style=\"font-family: Times New Roman;\">= 112 units</span><br><span style=\"font-family: Times New Roman;\">Required number of days </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>=</mo><mo>&#160;</mo><mfrac><mn>112</mn><mrow><mo>[</mo><mn>4</mn><mo>(</mo><mn>2</mn><mo>)</mo><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>6</mn><mo>(</mo><mn>1</mn><mo>)</mo><mo>]</mo></mrow></mfrac><mo>&#160;</mo><mo>=</mo><mfrac><mrow><mo>&#160;</mo><mn>112</mn></mrow><mn>14</mn></mfrac></math><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Times New Roman;\"> = 8 days</span></p>",
                    solution_hi: "<p>7.(b) <span style=\"font-family: Palanquin Dark;\">माना पुरुषों की दक्षता = M और लड़कों की दक्षता = B</span><br><span style=\"font-family: Palanquin Dark;\">प्रश्न के अनुसार,<br>(4M + 6B) 8 = (6M + 4B) 7<br>32M + 48B = 42M + 28B<br>10M = 20B<br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>M</mi><mi>B</mi></mfrac><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mfrac><mn>2</mn><mn>1</mn></mfrac></math></span><br><span style=\"font-family: Baloo;\">कुल कार्य &nbsp;= [4M + 6B] 8 = [4(2) + 6(1)] 8 = 14 8&nbsp; = 112</span><span style=\"font-family: Baloo;\">&nbsp;इकाई</span><br><span style=\"font-family: Baloo;\">दिनों की आवश्यक संख्या =&nbsp;</span><span style=\"font-family: Times New Roman;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>112</mn><mrow><mo>[</mo><mn>4</mn><mo>(</mo><mn>2</mn><mo>)</mo><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>6</mn><mo>(</mo><mn>1</mn><mo>)</mo><mo>]</mo></mrow></mfrac><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mfrac><mrow><mn>112</mn><mo>&#160;</mo></mrow><mn>14</mn></mfrac></math> </span><span style=\"font-family: Baloo;\"> = 8 दिन </span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "17",
                    question_en: "<p>8. <span style=\"font-family: Baloo;\">A tyre has 3 punctures. The first puncture alone would have made the tyre flat in 9 minutes, the second alone would have done it in 18 minutes, the third alone would have done it in 6 minutes. If the air leaks out at a constant rate, then how long (in minutes) does it take for all the punctures together to make it flat?</span></p>",
                    question_hi: "<p>8. <span style=\"font-family: Palanquin Dark;\">एक टायर में 3 पंचर होते हैं। अकेले पहले पंचर ने टायर को 9 मिनट में फ्लैट कर दिया होगा, दूसरे ने अकेले इसे 18 मिनट में किया होगा, तीसरे ने अकेले इसे 6 मिनट में किया होगा। यदि हवा एक स्थिर दर से बाहर निकलती है, तो इसे सपाट बनाने के लिए सभी पंचरों को एक साथ कितना समय (मिनटों में) लगता है?</span></p>",
                    options_en: ["<p>2</p>", "<p>6</p>", 
                                "<p>4</p>", "<p>3</p>"],
                    options_hi: ["<p>2</p>", "<p>6</p>",
                                "<p>4</p>", "<p>3</p>"],
                    solution_en: "<p>8.(d) <span style=\"font-family: Palanquin Dark;\">Let the three punctures be A, B and C.</span><br><strong id=\"docs-internal-guid-866ac6f3-7fff-552f-3789-2e0d27408b93\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdjGmXA2X9XdHK0WDxL3MT5FaWLXAyknl7izzbTQNXp5wAtLZ7Dzu1FwJi0lAyT--mOlLaczvYzWLLxtvpRUw3e7ZxTJHosy18Y0CHTpzDRPdnzuRUfQ2H-2m465XlsrNCHRRbiWw?key=mo0geGJ8DttffZ2nsxtkeQ\" width=\"114\" height=\"127\"></strong><br><span style=\"font-family: Palanquin Dark;\">Total time to flatten the tyre </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>=</mo><mo>&#160;</mo><mfrac><mrow><mn>18</mn><mo>&#160;</mo></mrow><mrow><mn>2</mn><mo>+</mo><mn>1</mn><mo>+</mo><mn>3</mn></mrow></mfrac><mo>=</mo><mo>&#160;</mo><mfrac><mrow><mn>18</mn><mo>&#160;</mo></mrow><mn>6</mn></mfrac></math><span style=\"font-family: Palanquin Dark;\"> = 3 minutes.</span></p>",
                    solution_hi: "<p>8.(d) <span style=\"font-family: Palanquin Dark;\">मान लीजिए कि तीन पंचर A, B और C हैं।</span><br><strong id=\"docs-internal-guid-6ac45683-7fff-784d-5b1d-de5eb61100b5\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXd8OOGHsr4QT2efaC_jwLh9-Z4-8Nb4aVSDSeHrXL7RdcqRNmnRUMcwCL2RVd3fqTFFuqkoEnU3la7x9IJ5s2DkXHT5TOWyPdse31kV4sezpet2zYYtj6Rbt7y_vKGq3zP1_lJq6g?key=mo0geGJ8DttffZ2nsxtkeQ\" width=\"108\" height=\"124\"></strong><br><span style=\"font-family: Palanquin Dark;\">टायर को समतल करने के लिए कुल समय</span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>=</mo><mo>&#160;</mo><mfrac><mrow><mn>18</mn><mo>&#160;</mo></mrow><mrow><mn>2</mn><mo>+</mo><mn>1</mn><mo>+</mo><mn>3</mn></mrow></mfrac><mo>=</mo><mo>&#160;</mo><mfrac><mn>18</mn><mn>6</mn></mfrac><mo>&#160;</mo></math><br><span style=\"font-family: Palanquin Dark;\"> = 3 मिनट</span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "17",
                    question_en: "<p>9. <span style=\"font-family: Palanquin Dark;\">Person A can do one - fifth of the work in 3 days , while B&rsquo;s efficiency is half of that of A. In how many days A and B working together can do half of the work?</span></p>",
                    question_hi: "<p>9.<span style=\"font-family: Palanquin Dark;\"> व्यक्ति A एक-पांचवें कार्य को 3 दिनों में कर सकता है, जबकि B की दक्षता A की आधी है। A और B एक साथ कार्य करते हुए कितने दिनों में आधा कार्य कर सकते हैं?</span></p>",
                    options_en: ["<p>4</p>", "<p>7</p>", 
                                "<p>6</p>", "<p>5</p>"],
                    options_hi: ["<p>4</p>", "<p>7</p>",
                                "<p>6</p>", "<p>5</p>"],
                    solution_en: "<p>9.(d) <span style=\"font-family: Palanquin Dark;\">Let efficiency of A = 2, so efficiency of B = 1</span><br><span style=\"font-family: Palanquin Dark;\">Time taken by A to complete whole work = 3 <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#215;</mo></math></span><span style=\"font-family: Palanquin Dark;\"> 5 = 15 days</span><br><span style=\"font-family: Palanquin Dark;\">So, total work = 2 <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#215;</mo></math></span><span style=\"font-family: Palanquin Dark;\"> 15 = 30 units</span><br><span style=\"font-family: Palanquin Dark;\">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; A&nbsp; &nbsp; &nbsp; B</span><br><span style=\"font-family: Palanquin Dark;\">Efficiency&nbsp; &nbsp; &nbsp;</span><span style=\"font-family: Palanquin Dark;\">(2)&nbsp; &nbsp;(1)</span><br><span style=\"font-family: Palanquin Dark;\">Time taken&nbsp; &nbsp; </span><span style=\"font-family: Palanquin Dark;\">15&nbsp; &nbsp; 30</span><br><span style=\"font-family: Palanquin Dark;\">Required time</span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>=</mo><mfrac><mrow><mo>&#160;</mo><mstyle displaystyle=\"true\"><mfrac><mn>1</mn><mn>2</mn></mfrac></mstyle><mo>&#160;</mo><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>30</mn></mrow><mrow><mn>2</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>1</mn></mrow></mfrac><mo>=</mo><mo>&#160;</mo><mfrac><mn>15</mn><mn>3</mn></mfrac><mo>&#160;</mo></math><span style=\"font-family: Palanquin Dark;\"> = 5 days</span></p>",
                    solution_hi: "<p>9.(d) <span style=\"font-family: Palanquin Dark;\">माना A की दक्षता = 2, अतः B की दक्षता = 1</span><br><span style=\"font-family: Palanquin Dark;\">A द्वारा पूरा कार्य पूरा करने में लिया गया समय = 3 <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#215;</mo></math></span><span style=\"font-family: Palanquin Dark;\"> 5 = 15 दिन</span><br><span style=\"font-family: Palanquin Dark;\">अत: कुल कार्य = 2<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#215;</mo></math> </span><span style=\"font-family: Palanquin Dark;\">&nbsp;15 = 30 इकाई</span><br><strong>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </strong>A&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; B<strong>&nbsp;&nbsp;&nbsp;&nbsp; &nbsp;&nbsp;&nbsp;&nbsp;</strong> &nbsp;&nbsp;&nbsp;<br>दक्षता&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; (2)&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; (1)&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<br>लिया गया समय&nbsp; &nbsp; &nbsp; &nbsp;15&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; 30&nbsp;&nbsp;&nbsp;&nbsp;<br><span style=\"font-family: Palanquin Dark;\">आवश्यक समय</span><span style=\"font-family: Palanquin Dark;\"> = </span><span style=\"font-family: Palanquin Dark;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mstyle displaystyle=\"true\"><mfrac><mn>1</mn><mn>2</mn></mfrac></mstyle><mo>&#160;</mo><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>30</mn></mrow><mrow><mn>2</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>1</mn></mrow></mfrac><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mfrac><mn>15</mn><mn>3</mn></mfrac></math> </span><span style=\"font-family: Palanquin Dark;\"> = 5 दिन</span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "17",
                    question_en: "<p>10. <span style=\"font-family: Palanquin Dark;\">A man, a woman and a boy can complete a work in 3, 4 and 12 days, respectively. How many boys must assist one man and one woman to complete the same work in one day?</span></p>",
                    question_hi: "<p>10. <span style=\"font-family: Palanquin Dark;\">एक पुरुष, एक महिला और एक लड़का एक काम को क्रमशः 3, 4 और 12 दिनों में पूरा कर सकते हैं। एक ही कार्य को एक दिन में पूरा करने के लिए कितने लड़कों को एक पुरुष और एक महिला की सहायता करनी चाहिए?</span></p>",
                    options_en: ["<p>5</p>", "<p>7</p>", 
                                "<p>4</p>", "<p>9</p>"],
                    options_hi: ["<p>5</p>", "<p>7</p>",
                                "<p>4</p>", "<p>9</p>"],
                    solution_en: "<p>10.(a)<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; Men&nbsp; &nbsp; &nbsp; Women&nbsp;&nbsp; Boys<br>Efficiency&nbsp; &nbsp;:&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; (4)&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; (3)&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; (1)<br>Time taken :&nbsp; &nbsp; &nbsp; 3&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;4&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; 12<br><span style=\"font-family: Palanquin Dark;\">Total work = LCM of time taken = LCM (3, 4, 12) = 12 units</span><br><span style=\"font-family: Palanquin Dark;\">Let the required number of boys = n</span><br><span style=\"font-family: Palanquin Dark;\">So, [4(1) + 3(1) + n(1)] = 12</span><br><span style=\"font-family: Palanquin Dark;\">4 + 3 + n = 12</span><br><span style=\"font-family: Palanquin Dark;\">n = 12 &ndash; 7 = 5 boys</span></p>",
                    solution_hi: "<p>10.(a)<br><strong>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;</strong>पुरुष&nbsp; &nbsp;महिला&nbsp; लड़के<br>दक्षता :&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;(4)&nbsp;&nbsp;&nbsp;&nbsp; (3)&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; (1)<br>लिया गया समय :&nbsp; &nbsp; &nbsp; 3&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; 4&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; 12<br><span style=\"font-family: Palanquin Dark;\">कुल कार्य = लिए गए समय का LCM = LCM (3, 4, 12) = 12 इकाई</span><br><span style=\"font-family: Palanquin Dark;\">माना लड़कों की अभीष्ट संख्या = n</span><br><span style=\"font-family: Palanquin Dark;\">इसलिए, [4(1) + 3(1) + n(1)] = 12</span><br><span style=\"font-family: Palanquin Dark;\">4 + 3 + n = 12</span><br><span style=\"font-family: Palanquin Dark;\">n = 12 &ndash; 7 = 5 लड़के</span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "17",
                    question_en: "<p>11.<span style=\"font-family: Palanquin Dark;\"> A can complete work in 25 days and B can complete the same work in 20days . They started the work together but B left after 4 days and A continued to work . In how many days will the entire work be completed?</span></p>",
                    question_hi: "<p>11.<span style=\"font-family: Palanquin Dark;\"> A 25 दिनों में काम पूरा कर सकता है और B उसी काम को 20 दिनों में पूरा कर सकता है। उन्होंने एक साथ काम शुरू किया लेकिन 4 दिनों के बाद B ने काम छोड़ दिया और A ने काम करना जारी रखा। पूरा कार्य कितने दिनों में पूरा होगा?</span></p>",
                    options_en: ["<p>25</p>", "<p>20</p>", 
                                "<p>28</p>", "<p>22</p>"],
                    options_hi: ["<p>25</p>", "<p>20</p>",
                                "<p>28</p>", "<p>22</p>"],
                    solution_en: "<p>11.(b)<br><strong id=\"docs-internal-guid-4f140111-7fff-ee93-6917-df89a46b6630\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXerT6cTSQKDs3xLycG5QEc1LPQtYi78mwnL3AxpwznHGXk_QhKJwuuJmnkL1Or0X7eduyhp_2Mt_dshDKx8uPPa_8JQ-2ujufYxgPz_DHHZH6wHEwqeZnhLCWv95dj4UNDU7YtwSQ?key=fXw9v-ClrzterRqTh9VoCIFw\" width=\"127\" height=\"114\"></strong><br><span style=\"font-family: Palanquin Dark;\">Work done by (A + B) in 4 days = (5 + 4) &times; 4 = 36 units</span><br><span style=\"font-family: Palanquin Dark;\">Remaining work = 100 &ndash; 36 = 64 units</span><br><span style=\"font-family: Palanquin Dark;\">Time taken by A to complete the remaining work <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>=</mo><mo>&#160;</mo><mfrac><mn>64</mn><mn>4</mn></mfrac></math> </span><span style=\"font-family: Palanquin Dark;\">&nbsp;= 16 days.</span><br><span style=\"font-family: Palanquin Dark;\">Time taken to complete the entire work = 16 + 4 = 20 days.</span></p>",
                    solution_hi: "<p>11.(b)<br><strong id=\"docs-internal-guid-f970b805-7fff-faf2-f86b-4e58532ad337\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXco76tFbifgSpZzBX3BbYwu3CVS4mRphU9aRqXEUK88jPX-58hhD2tRB_8Q_Bbr4Bmuou-10O5z7bTMQZzA8QilnoOJGk8xQ7BwV8QpE4LtCGfpp057uCM6wMPhA1dAoRl8cWdAbg?key=fXw9v-ClrzterRqTh9VoCIFw\" width=\"117\" height=\"108\"></strong><br>(A + B) द्वारा 4 दिनों में किया गया कार्य = (5 + 4) &times; 4 = 36 इकाई<br><span style=\"font-family: Palanquin Dark;\">शेष कार्य = 100 &ndash; 36 = 64 इकाई</span><br><span style=\"font-family: Palanquin Dark;\">शेष कार्य को पूरा करने में A द्वारा लिया गया समय =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>64</mn><mn>4</mn></mfrac><mo>&#160;</mo></math> </span><span style=\"font-family: Palanquin Dark;\">&nbsp;= 16 दिन।</span><br><span style=\"font-family: Palanquin Dark;\">पूरे कार्य को पूरा करने में लगा समय = 16 + 4 = 20 दिन।</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "17",
                    question_en: "<p>12. <span style=\"font-family: Palanquin Dark;\">&nbsp;A can do a piece of work in 15 days, while B can do the same work in 21 days. If they work together, then in how many days will the same work be completed?</span></p>",
                    question_hi: "<p>12.<span style=\"font-family: Palanquin Dark;\"> A एक काम को 15 दिनों में कर सकता है, जबकि B उसी काम को 21 दिनों में कर सकता है। यदि वे एक साथ कार्य करते हैं, तो समान कार्य कितने दिनों में पूरा होगा?</span></p>",
                    options_en: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>7</mn><mfrac><mn>3</mn><mn>4</mn></mfrac><mo>&#160;</mo></math><span style=\"font-family: Palanquin Dark;\"> </span></p>", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>9</mn><mfrac><mrow><mn>3</mn><mo>&#160;</mo></mrow><mn>4</mn></mfrac></math><span style=\"font-family: Palanquin Dark;\"> </span></p>", 
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>6</mn><mfrac><mn>3</mn><mn>4</mn></mfrac><mo>&#160;</mo></math><span style=\"font-family: Palanquin Dark;\"> </span></p>", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>8</mn><mfrac><mn>3</mn><mrow><mn>4</mn><mo>&#160;</mo></mrow></mfrac></math><span style=\"font-family: Palanquin Dark;\"> </span></p>"],
                    options_hi: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>7</mn><mfrac><mn>3</mn><mn>4</mn></mfrac></math><span style=\"font-family: Palanquin Dark;\"> </span></p>", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>9</mn><mfrac><mn>3</mn><mn>4</mn></mfrac><mo>&#160;</mo></math><span style=\"font-family: Palanquin Dark;\"> </span></p>",
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>6</mn><mfrac><mn>3</mn><mn>4</mn></mfrac><mo>&#160;</mo></math><span style=\"font-family: Palanquin Dark;\"> </span></p>", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>8</mn><mfrac><mn>3</mn><mn>4</mn></mfrac></math><span style=\"font-family: Palanquin Dark;\"> </span></p>"],
                    solution_en: "<p>12.(d)<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;A&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; B&nbsp;&nbsp;<br>Efficiency :&nbsp; &nbsp; &nbsp; &nbsp;(7)&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; (5)&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<br>Time taken :&nbsp;&nbsp;&nbsp; 15&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; 21&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<br><span style=\"font-family: Palanquin Dark;\">Total work = LCM of time taken = LCM (15, 21) = 105 units</span><br><span style=\"font-family: Palanquin Dark;\">Time taken to complete the work together = </span><span style=\"font-family: Palanquin Dark;\"> </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>105</mn><mo>&#160;</mo></mrow><mrow><mn>7</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>5</mn></mrow></mfrac><mo>=</mo><mfrac><mrow><mo>&#160;</mo><mn>105</mn></mrow><mn>12</mn></mfrac><mo>&#160;</mo><mo>=</mo><mfrac><mrow><mo>&#160;</mo><mn>35</mn><mo>&#160;</mo></mrow><mn>4</mn></mfrac><mo>=</mo><mo>&#160;</mo><mn>8</mn><mfrac><mrow><mn>3</mn><mo>&#160;</mo></mrow><mn>4</mn></mfrac><mi>days</mi></math></p>",
                    solution_hi: "<p>12.(d)<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; A&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; B&nbsp;&nbsp;<br>दक्षता :&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; (7)&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; (5)&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<br>लिया गया समय :&nbsp;&nbsp;&nbsp; 15&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; 21&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<br><span style=\"font-family: Palanquin Dark;\">कुल कार्य = लिए गए समय का LCM = LCM (15, 21) = 105 इकाई</span><br><span style=\"font-family: Palanquin Dark;\">एक साथ कार्य को पूरा करने में लिया गया समय </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>=</mo><mo>&#160;</mo><mfrac><mn>105</mn><mrow><mn>7</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>5</mn></mrow></mfrac><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mfrac><mn>105</mn><mn>12</mn></mfrac><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mfrac><mn>35</mn><mn>4</mn></mfrac><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>8</mn><mfrac><mn>3</mn><mn>4</mn></mfrac><mo>&#160;</mo></math><span style=\"font-family: Palanquin Dark;\"> दिन</span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "17",
                    question_en: "<p>13.<span style=\"font-family: Palanquin Dark;\"> 14 men can complete a work in 15 days. If 21 men are employed then in how many days will they complete the same work?</span></p>",
                    question_hi: "<p>13.<span style=\"font-family: Palanquin Dark;\"> 14 आदमी एक काम को 15 दिनों में पूरा कर सकते हैं। यदि 21 आदमी काम कर रहे है हैं तो वे उसी काम को कितने दिनों में पूरा करेंगे?</span></p>",
                    options_en: ["<p>10</p>", "<p>14</p>", 
                                "<p>12</p>", "<p>15</p>"],
                    options_hi: ["<p>10</p>", "<p>14</p>",
                                "<p>12</p>", "<p>15</p>"],
                    solution_en: "<p>13.(a) <span style=\"font-family: Palanquin Dark;\">M₁&times;D₁ = M₂&times; D₂</span><br><span style=\"font-family: Palanquin Dark;\">14&times;15 = 21&times;D₂</span><br><span style=\"font-family: Palanquin Dark;\">D₂ = 10 days</span></p>",
                    solution_hi: "<p>13.(a) <span style=\"font-family: Palanquin Dark;\">M₁&times;D₁ = M₂&times; D₂</span><br><span style=\"font-family: Palanquin Dark;\">14&times;15 = 21&times;D₂</span><br><span style=\"font-family: Arial Unicode MS;\">D₂ = 10 दिन</span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "17",
                    question_en: "<p>14.<span style=\"font-family: Arial Unicode MS;\"> X, Y and Z can complete a piece of work in 46 days, 92 days and 23 days, respectively. X started the work, Y joined him after 7 days. If Z joined them after 8 days from the beginning, then for how many days did Y work?</span></p>",
                    question_hi: "<p>14.<span style=\"font-family: Palanquin Dark;\"> X, Y और Z एक कार्य को क्रमशः 46 दिन, 92 दिन और 23 दिन में पूरा कर सकते हैं। X ने कार्य शुरू किया, 7 दिनों के बाद Y उसके साथ जुड़ गया। यदि Z प्रारंभ से 8 दिनों के बाद उनके साथ जुड़ता है, तो Y ने कितने दिनों तक कार्य किया?</span></p>",
                    options_en: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>12</mn><mfrac><mn>5</mn><mn>7</mn></mfrac></math></p>", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>11</mn><mfrac><mn>5</mn><mn>7</mn></mfrac></math></p>", 
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>10</mn><mfrac><mn>5</mn><mn>7</mn></mfrac></math></p>", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>9</mn><mfrac><mn>5</mn><mn>7</mn></mfrac></math></p>"],
                    options_hi: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>12</mn><mfrac><mn>5</mn><mn>7</mn></mfrac></math></p>", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>11</mn><mfrac><mn>5</mn><mn>7</mn></mfrac></math></p>",
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>10</mn><mfrac><mn>5</mn><mn>7</mn></mfrac></math></p>", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>9</mn><mfrac><mn>5</mn><mn>7</mn></mfrac></math></p>"],
                    solution_en: "<p>14.(b) <span style=\"font-family: Palanquin Dark;\">LCM of 46, 92 and 23 is 92</span><br><span style=\"font-family: Palanquin Dark;\">Let total work 92 units</span><br><span style=\"font-family: Palanquin Dark;\">So efficiency of x, y, z is 2, 1, 4 respectively</span><br><strong id=\"docs-internal-guid-eb403dee-7fff-a406-c2bb-4101b1b2060e\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXe6ZuStZDhjrH0QXJWBvqASYLIGp7zMOQZAT3nonrszYbo9vHWP-KlMTp_XkAdW3x7B6uUMmwRW-9nbbcvJSEevzcstEEEU3_zVbyw0RZK5B9D62F-LoI66dDnFFi6keEweG2k5Fw?key=GqzvR84WLuKsY9oZ1QD7ct7p\" width=\"195\" height=\"92\"></strong><br><span style=\"font-family: Palanquin Dark;\">Let total work completed in n days</span><br><span style=\"font-family: Palanquin Dark;\">Now according to the question x work for n days</span><br><span style=\"font-family: Palanquin Dark;\">y for n &ndash; 7 days and z for n &ndash; 8 days</span><br><span style=\"font-family: Palanquin Dark;\">n &times; 2 + (n &ndash; 7) &times; 1 + (n &ndash; 8) &times; 4 = 92 units</span><br><span style=\"font-family: Palanquin Dark;\">&nbsp;2n + n &ndash; 7 + 4n &ndash; 32 = 92</span><br><span style=\"font-family: Palanquin Dark;\">&nbsp;7n = 131</span><br><span style=\"font-family: Palanquin Dark;\"> n =</span><span style=\"font-family: Palanquin Dark;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>131</mn><mn>7</mn></mfrac></math></span><span style=\"font-family: Palanquin Dark;\"> </span><span style=\"font-family: Palanquin Dark;\">days</span><br><span style=\"font-family: Palanquin Dark;\">No of days y worked </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>=</mo><mo>&#160;</mo><mi>n</mi><mo>&#8211;</mo><mn>7</mn><mo>&#160;</mo><mo>=</mo><mfrac><mn>131</mn><mn>7</mn></mfrac><mo>&#8211;</mo><mn>7</mn><mo>&#8658;</mo><mfrac><mn>82</mn><mn>7</mn></mfrac><mo>&#8658;</mo><mn>11</mn><mfrac><mn>5</mn><mn>7</mn></mfrac><mo>&#160;</mo><mi>days</mi></math></p>",
                    solution_hi: "<p>14.(b) <span style=\"font-family: Palanquin Dark;\">46, 92 और 23 का LCM 92 है</span><br><span style=\"font-family: Palanquin Dark;\">माना कुल कार्य 92 इकाई</span><br><span style=\"font-family: Palanquin Dark;\">अतः x, y, z की दक्षता क्रमशः 2, 1, 4 है</span><br><strong id=\"docs-internal-guid-39e7fd94-7fff-4939-142e-6f8a673c07cb\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXc411kCZ8abxBXMWOLKyz3r6b4XN-WtdJQqomYZA9E4SnnevKvAHIV8Grmggx6gvSgdPm-NaVtib6rU0QcsavBt0Fu-IGhnb5UgGqWYuJlB-XKL7cZsFCA3jqWse4CSZcHdfoitCQ?key=GqzvR84WLuKsY9oZ1QD7ct7p\" width=\"180\" height=\"102\"></strong><br><span style=\"font-family: Palanquin Dark;\">माना कुल कार्य n दिनों में पूरा होता है</span><br><span style=\"font-family: Palanquin Dark;\">अब प्रश्न के अनुसार x, n दिनों के लिए कार्य करता है</span><br><span style=\"font-family: Palanquin Dark;\">n &ndash; 7 दिनों के लिए y और n &ndash; 8 दिनों के लिए z</span><br><span style=\"font-family: Palanquin Dark;\">n &times; 2 + (n &ndash; 7) &times; 1 + (n &ndash; 8) &times; 4 = 92 units</span><br><span style=\"font-family: Palanquin Dark;\">&nbsp;2n + n &ndash; 7 + 4n &ndash; 32 = 92</span><br><span style=\"font-family: Palanquin Dark;\">&nbsp;7n = 131</span><br><span style=\"font-family: Palanquin Dark;\"> n =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>131</mn><mn>7</mn></mfrac></math> </span><span style=\"font-family: Palanquin Dark;\">&nbsp;दिन</span><br><span style=\"font-family: Palanquin Dark;\">दिनों की संख्या y ने काम किया </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>=</mo><mo>&#160;</mo><mi>n</mi><mo>&#8211;</mo><mn>7</mn><mo>&#160;</mo><mo>=</mo><mfrac><mn>131</mn><mn>7</mn></mfrac><mo>&#8211;</mo><mn>7</mn><mo>&#8658;</mo><mfrac><mrow><mo>&#160;</mo><mn>82</mn></mrow><mn>7</mn></mfrac><mo>&#8658;</mo><mn>11</mn><mfrac><mn>5</mn><mn>7</mn></mfrac></math><span style=\"font-family: Palanquin Dark;\"> दिन</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "17",
                    question_en: "<p>15. <span style=\"font-family: Palanquin Dark;\">20 men can finish a work in 30 days . They started working , but 4 men left the work after 10 days . In how many days would the work be completed ?</span></p>",
                    question_hi: "<p>15. <span style=\"font-family: Palanquin Dark;\">20 आदमी एक काम को 30 दिनों में पूरा कर सकते हैं। उन्होंने काम करना शुरू कर दिया, लेकिन 4 आदमी 10 दिनों के बाद काम छोड़ देते हैं। कार्य कितने दिनों में पूरा होगा?</span></p>",
                    options_en: ["<p>30</p>", "<p>25</p>", 
                                "<p>35</p>", "<p>28</p>"],
                    options_hi: ["<p>30</p>", "<p>25</p>",
                                "<p>35</p>", "<p>28</p>"],
                    solution_en: "<p>15.(c) <span style=\"font-family: Palanquin Dark;\">According to the question 4 men left after 10 days so work of only 20 days of 20 men is left which is done by 16 man in x days</span><br><span style=\"font-family: Palanquin Dark;\">Men₁ &times; days₁ = men₂ &times;days₂</span><br><span style=\"font-family: Palanquin Dark;\">20&times;20 = (20 &ndash; 4)&times;x</span><br><span style=\"font-family: Palanquin Dark;\">x = 25 days</span><br><span style=\"font-family: Palanquin Dark;\">Total days to complete the work = 10 + 25 = 35 days</span></p>",
                    solution_hi: "<p>15.(c) <span style=\"font-family: Palanquin Dark;\">प्रश्न के अनुसार 4 आदमी 10 दिनों के बाद छोड़ देते हैं इसलिए 20 आदमी का 20 दिनों का काम बचा है जिसे 16 आदमी x दिनों में पूरा करते हैं</span><br><span style=\"font-family: Arial Unicode MS;\">आदमी₁ &times; दिन₁ = आदमी₂ &times; दिन₂</span><br><span style=\"font-family: Arial Unicode MS;\">20&times;20 = (20 &ndash; 4) &times; x</span><br><span style=\"font-family: Palanquin Dark;\">x = 25 दिन</span><br><span style=\"font-family: Palanquin Dark;\">कार्य पूरा करने के लिए कुल दिन = 10+ 25 = 35 दिन</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "16",
                    section: "17",
                    question_en: "<p>16. <span style=\"font-family: Palanquin Dark;\">A college hostel mess has provisions for 25 days for 350 boys. At the end of 10 days, when some boys were shifted to another hostel, it was found that now the provisions will last for 21 more days. How many boys were shifted to another hostel?</span></p>",
                    question_hi: "<p>16. <span style=\"font-family: Palanquin Dark;\">एक कॉलेज छात्रावास मेस में 350 लड़कों के लिए 25 दिनों का प्रावधान है। 10 दिन बाद जब कुछ लड़कों को दूसरे हॉस्टल में शिफ्ट किया गया तो पता चला कि अब 21 दिन और रहेंगे। कितने लड़कों को दूसरे छात्रावास में स्थानांतरित कर दिया गया?</span></p>",
                    options_en: ["<p>92</p>", "<p>110</p>", 
                                "<p>98</p>", "<p>100</p>"],
                    options_hi: ["<p>92</p>", "<p>110</p>",
                                "<p>98</p>", "<p>100</p>"],
                    solution_en: "<p>16.(d) <span style=\"font-family: Palanquin Dark;\">According to the question some boys shifted to another hostel after 10 days so food left of 15 days of 350 boys consumed by remaining boys in 21 days</span><br><span style=\"font-family: Palanquin Dark;\">Days1 &times; number of boys initially = Days2 &times; number of remaining boys</span><br><span style=\"font-family: Palanquin Dark;\">15 &times;350 = 21&times;number of remaining boys</span><br><span style=\"font-family: Palanquin Dark;\">number of remaining boys = 250</span><br><span style=\"font-family: Palanquin Dark;\">So initially boys are 350 now remaining boys are 250</span><br><span style=\"font-family: Palanquin Dark;\">Boys shifted to another hostel = 350 - 250 = 100</span></p>",
                    solution_hi: "<p>16.(d) <span style=\"font-family: Palanquin Dark;\">प्रश्न के अनुसार, कुछ लड़के 10 दिनों के बाद दूसरे छात्रावास में स्थानांतरित हो गए, इसलिए 350 लड़कों में से 15 दिनों का खाना शेष लड़कों ने 21 दिनों में खा लिया।</span><br><span style=\"font-family: Palanquin Dark;\">दिन1 &times; प्रारंभ में लड़कों की संख्या = दिन2 &times; शेष लड़कों की संख्या</span><br><span style=\"font-family: Palanquin Dark;\">15 &times;350 = 21&times;शेष लड़कों की संख्या</span><br><span style=\"font-family: Palanquin Dark;\">शेष लड़कों की संख्या = 250</span><br><span style=\"font-family: Palanquin Dark;\">तो, शुरू में लड़के 350 होते हैं ।</span><br><span style=\"font-family: Palanquin Dark;\">अब बाकी लड़के 250 हैं।</span><br><span style=\"font-family: Palanquin Dark;\">लड़के दूसरे छात्रावास में शिफ्ट हो गए = 350 - 250 = 100</span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "17",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Times New Roman;\">17.</span><span style=\"font-family: Times New Roman;\"> P and Q completed a work together and were paid ₹1080 and ₹1,440, respectively. If P can do the entire work in 20 days how many days did they take to complete the work together?</span></p>",
                    question_hi: "<p><span style=\"font-family: Times New Roman;\">17. </span><span style=\"font-family: Baloo;\">P और Q ने एक साथ एक काम पूरा किया और उन्हें क्रमशः ₹1080 और ₹1,440 का भुगतान किया गया। यदि P पूरे कार्य को 20 दिनों में कर सकता है, तो उन्होंने एक साथ कार्य को पूरा करने में कितने दिन का समय लिया?</span></p>",
                    options_en: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>6</mn><mfrac><mn>4</mn><mn>7</mn></mfrac></math></p>", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>6</mn><mfrac><mn>3</mn><mn>7</mn></mfrac></math></p>", 
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>8</mn><mfrac><mn>3</mn><mn>7</mn></mfrac></math></p>", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>8</mn><mfrac><mn>4</mn><mn>7</mn></mfrac></math></p>"],
                    options_hi: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>6</mn><mfrac><mn>4</mn><mn>7</mn></mfrac></math></p>", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>6</mn><mfrac><mn>3</mn><mn>7</mn></mfrac></math></p>",
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>8</mn><mfrac><mn>3</mn><mn>7</mn></mfrac></math></p>", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>8</mn><mfrac><mn>4</mn><mn>7</mn></mfrac></math></p>"],
                    solution_en: "<p>17.(d)<span style=\"font-family: Times New Roman;\">Wages are always paid in ratio of efficiency</span><br><span style=\"font-family: Times New Roman;\">So,<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>Efficiency</mi><mo>&#160;</mo><mi>of</mi><mo>&#160;</mo><mi mathvariant=\"normal\">P</mi></mrow><mrow><mi>Efficiency</mi><mo>&#160;</mo><mi>of</mi><mo>&#160;</mo><mi mathvariant=\"normal\">Q</mi></mrow></mfrac><mo>=</mo><mfrac><mrow><mo>&#160;</mo><mi>wages</mi><mo>&#160;</mo><mi>of</mi><mo>&#160;</mo><mi mathvariant=\"normal\">P</mi><mo>&#160;</mo></mrow><mrow><mi>wages</mi><mo>&#160;</mo><mi>of</mi><mo>&#160;</mo><mi mathvariant=\"normal\">Q</mi></mrow></mfrac><mo>=</mo><mfrac><mrow><mn>1080</mn><mo>&#160;</mo></mrow><mrow><mn>1440</mn><mo>&#160;</mo></mrow></mfrac><mo>=</mo><mfrac><mn>3</mn><mn>4</mn></mfrac></math></span><br><span style=\"font-family: Times New Roman;\">P completes work in 20 days</span><br><span style=\"font-family: Times New Roman;\">so total work = </span><span style=\"font-family: Times New Roman;\">20 days &times; efficiency of P = 20&times;3 = 60 units</span><br><span style=\"font-family: Times New Roman;\">P and Q together completes 60 units of work in </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>=</mo><mo>&#160;</mo><mfrac><mrow><mn>60</mn><mo>&#160;</mo></mrow><mrow><mn>3</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>4</mn></mrow></mfrac><mo>=</mo><mo>&#160;</mo><mn>8</mn><mfrac><mn>4</mn><mn>7</mn></mfrac><mo>&#160;</mo><mi>d</mi><mi>a</mi><mi>y</mi><mi>s</mi></math></p>",
                    solution_hi: "<p>17.(d) <span style=\"font-family: Baloo;\">मजदूरी का भुगतान हमेशा दक्षता के अनुपात में किया जाता है।</span><br><span style=\"font-family: Baloo;\">तो,<math style=\"font-family:Verdana\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi mathvariant=\"normal\">P</mi><mo>&#160;</mo><mi>&#2325;&#2368;</mi><mo>&#160;</mo><mi>&#2342;&#2325;&#2381;&#2359;&#2340;&#2366;</mi></mrow><mrow><mi mathvariant=\"normal\">Q</mi><mo>&#160;</mo><mi>&#2325;&#2368;</mi><mo>&#160;</mo><mi>&#2342;&#2325;&#2381;&#2359;&#2340;&#2366;</mi></mrow></mfrac><mo>=</mo><mfrac><mrow><mo>&#160;</mo><mi mathvariant=\"normal\">P</mi><mo>&#160;</mo><mi>&#2325;&#2368;</mi><mo>&#160;</mo><mi>&#2350;&#2332;&#2342;&#2370;&#2352;&#2368;</mi><mo>&#160;</mo></mrow><mrow><mi mathvariant=\"normal\">Q</mi><mo>&#160;</mo><mi>&#2325;&#2368;</mi><mo>&#160;</mo><mi>&#2350;&#2332;&#2342;&#2370;&#2352;&#2368;</mi></mrow></mfrac><mo>=</mo><mfrac><mrow><mn>1080</mn><mo>&#160;</mo></mrow><mrow><mn>1440</mn><mo>&#160;</mo></mrow></mfrac><mo>=</mo><mfrac><mn>3</mn><mn>4</mn></mfrac></math></span><br><span style=\"font-family: Baloo;\">P, 20 दिनों में कार्य पूरा करता है, इसलिए कुल कार्य</span><br><span style=\"font-family: Baloo;\">20 दिन &times; P की दक्षता = 20&times;3 = 60 इकाई</span><br><span style=\"font-family: Baloo;\">P और Q मिलकर 60 इकाई का काम = </span><span style=\"font-family: Times New Roman;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>60</mn><mrow><mn>3</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>4</mn></mrow></mfrac><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>8</mn><mfrac><mn>4</mn><mn>7</mn></mfrac><mo>&#160;</mo></math></span><span style=\"font-family: Baloo;\"> दिनों में पूरा करते हैं।</span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "18",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Baloo;\">18. </span><span style=\"font-family: Baloo;\">A and B can do a work in 12 days and 18 days, respectively ,they worked together for 4 days after which B was replaced by C and the remaining work was completed by A and C in the next 4 days . In how many days will C alone complete 50% of the same work?</span></p>",
                    question_hi: "<p>18. <span style=\"font-family: Palanquin Dark;\">A और B एक काम को क्रमश: 12 और 18 दिनों में कर सकते हैं, उन्होंने एक साथ 4 दिन काम किया जिसके बाद B को C से बदल दिया गया और शेष काम A और C ने अगले 4 दिनों में पूरा किया। C अकेले उसी कार्य का 50% भाग कितने दिनों में पूरा करेगा?</span></p>",
                    options_en: ["<p>24</p>", "<p>18</p>", 
                                "<p>21</p>", "<p>36</p>"],
                    options_hi: ["<p>24</p>", "<p>18</p>",
                                "<p>21</p>", "<p>36</p>"],
                    solution_en: "<p>18.(b) <span style=\"font-family: Times New Roman;\">LCM of 12 and 18 is 36</span><br><span style=\"font-family: Times New Roman;\">Efficiency of A is 3 units and B is 2 units</span><br><strong id=\"docs-internal-guid-8289b666-7fff-eabd-bdd5-d06c16eca5f7\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXe2tC_d8djuYNgTXyrQz9XHKYwLrofvheX3rgPwGYV0P3_azoU_lruZXx18U-ro78a_HPFk6eHUTyeLt1eUZx0CwB4X_Y8Qejvdoz-PgYxUTEu7tr2mLcoILdZBx6KAkGGZPYgy9A?key=jtvRBx7GXjxr9j5yH7UqF15R\" width=\"225\" height=\"112\"></strong><br><span style=\"font-family: Times New Roman;\">Now according to the question</span><br><span style=\"font-family: Times New Roman;\">Work completed by A and B in 4 days</span><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><mi>A</mi><mo>+</mo><mi>B</mi><mo>)</mo><mo>&#215;</mo><mn>4</mn><mo>&#160;</mo><mi>d</mi><mi>a</mi><mi>y</mi><mi>s</mi><mo>=</mo><mn>5</mn><mo>&#215;</mo><mn>4</mn><mo>=</mo><mn>20</mn><mo>&#160;</mo><mi>u</mi><mi>n</mi><mi>i</mi><mi>t</mi><mi>s</mi><mo>&#160;</mo></math><br><span style=\"font-family: Times New Roman;\">Remaining Work = 36 - 20 = 16 units</span><br><span style=\"font-family: Times New Roman;\">Remaining work completed by A and C in 4 days</span><br><span style=\"font-family: Times New Roman;\">Let efficiency of C is c</span><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><mi>A</mi><mo>+</mo><mi>C</mi><mo>)</mo><mo>&#215;</mo><mn>4</mn><mo>&#160;</mo><mi>d</mi><mi>a</mi><mi>y</mi><mi>s</mi><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mo>&#160;</mo><mo>(</mo><mn>3</mn><mo>+</mo><mi>c</mi><mo>)</mo><mo>&#215;</mo><mn>4</mn><mo>=</mo><mn>16</mn><mo>&#8658;</mo><mi>c</mi><mo>=</mo><mn>1</mn><mi>u</mi><mi>n</mi><mi>i</mi><mi>t</mi><mi>s</mi><mo>&#160;</mo></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>50</mn><mo>%</mo><mo>&#160;</mo><mi>o</mi><mi>f</mi><mo>&#160;</mo><mi>w</mi><mi>o</mi><mi>r</mi><mi>k</mi><mo>&#160;</mo><mo>=</mo><mfrac><mn>36</mn><mn>2</mn></mfrac><mo>=</mo><mn>18</mn><mo>&#160;</mo><mi>u</mi><mi>n</mi><mi>i</mi><mi>t</mi><mi>s</mi></math><br><span style=\"font-family: Times New Roman;\">C completes 50% of work in =</span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>18</mn><mrow><mi>c</mi><mo>&#160;</mo></mrow></mfrac><mo>=</mo><mfrac><mn>18</mn><mn>1</mn></mfrac><mo>&#160;</mo><mo>&#160;</mo><mo>&#160;</mo></math><span style=\"font-family: Times New Roman;\"> 18 days</span></p>",
                    solution_hi: "<p>18.(b) <span style=\"font-family: Baloo;\">12 और 18 का LCM 36 है।</span><br><span style=\"font-family: Baloo;\">A की क्षमता 3 इकाई है और B 2 इकाई है।</span><br><strong id=\"docs-internal-guid-ab1c7a70-7fff-67b1-f1fe-842d7216790b\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXca3veYUcCDCiGnlaugFdXGQSUqAHKmgaUAWip5lmjCI1JXSpQhLsQ2Re-PNa45X77IEOpbVBT-W8x1dkC3ZSBWDHq2xT97bS66vXRNLz7x_oefnAx2TfuZcSAjDOmRB1HMOqAgQA?key=jtvRBx7GXjxr9j5yH7UqF15R\" width=\"233\" height=\"129\"></strong><br><span style=\"font-family: Baloo;\">अब प्रश्न के अनुसार,</span><br><span style=\"font-family: Baloo;\">कार्य A और B द्वारा 4 दिनों में पूरा किया जाता है।</span><br>(A + B) &times; 4 दिन = 5 &times; 4 = 20 इकाई<br><span style=\"font-family: Baloo;\">शेष कार्य = 36 - 20 = 16 इकाई</span><br><span style=\"font-family: Baloo;\">शेष कार्य A और C द्वारा 4 दिनों में पूरा किया गया।</span><br><span style=\"font-family: Baloo;\">माना, C की दक्षता c है।</span><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><mi>A</mi><mo>+</mo><mi>C</mi><mo>)</mo><mo>&#215;</mo></math>4 दिन = (3 + c)&times;4 = 16 <span style=\"font-family: Baloo;\">&nbsp;c= 1 इकाई</span><br><span style=\"font-family: Baloo;\">कार्य का 50% = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>36</mn><mn>2</mn></mfrac></math></span><span style=\"font-family: Baloo;\"> = 18 इकाई</span><br><span style=\"font-family: Baloo;\">C, 50% कार्य को = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>18</mn><mo>&#160;</mo></mrow><mi>c</mi></mfrac><mo>=</mo><mfrac><mn>18</mn><mn>1</mn></mfrac></math></span><span style=\"font-family: Baloo;\">= 18 दिनों में पूरा करता है।</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "19",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Baloo;\">19. </span><span style=\"font-family: Baloo;\">A and B working alone can complete a work in 8 days and 12 days respectively., They started working together, but A left 2 days before completion of the work. In how many </span><span style=\"font-family: Baloo;\">days was</span><span style=\"font-family: Baloo;\"> the </span><span style=\"font-family: Baloo;\">work</span><span style=\"font-family: Baloo;\"> completed?</span></p>",
                    question_hi: "<p>19. <span style=\"font-family: Palanquin Dark;\">A और B अकेले काम करते हुए एक काम को क्रमशः 8 दिन और 12 दिन में पूरा कर सकते हैं। उन्होंने एक साथ काम करना शुरू किया, लेकिन A ने काम पूरा होने से 2 दिन पहले काम छोड़ दिया। कार्य कितने दिनों में पूरा हुआ?</span></p>",
                    options_en: ["<p>6</p>", "<p>5</p>", 
                                "<p>8</p>", "<p>10</p>"],
                    options_hi: ["<p>6</p>", "<p>5</p>",
                                "<p>8</p>", "<p>10</p>"],
                    solution_en: "<p>19.(a) <span style=\"font-family: Times New Roman;\">Let total work be 24 so the efficiency of A and B is 3 and 2 units respectively</span><br><strong id=\"docs-internal-guid-340cfe38-7fff-a6a2-1d03-32b916e30490\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdQxJmzuw_-27K69rxo7bcf4p7klzEUPDPGONvTm-WTu1d2oMUFjhYy62PR6sG2gqca3AEx6HHkdmFYFnLQywfK_YDQrl4epRUMa5_gaP3xvkVm2M4fWuNPSm8Y0mgOZx4UyOt6Gg?key=jaX7qvR187IabjuCUoAziNA2\" width=\"223\" height=\"123\"></strong><br><span style=\"font-family: Times New Roman;\">Let work completed in x days</span><br><span style=\"font-family: Times New Roman;\">So A work for (x-2) days and B for x days</span><br><span style=\"font-family: Times New Roman;\">Now according to the question&nbsp;</span><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><mi>x</mi><mo>-</mo><mn>2</mn><mo>)</mo><mo>&#215;</mo><mn>3</mn><mo>+</mo><mi>x</mi><mo>&#215;</mo><mn>2</mn><mo>=</mo><mn>24</mn><mo>&#160;</mo></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>3</mn><mi>x</mi><mo>-</mo><mn>6</mn><mo>+</mo><mn>2</mn><mi>x</mi><mo>=</mo><mn>24</mn><mo>&#160;</mo></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>5</mn><mi>x</mi><mo>=</mo><mn>30</mn><mo>&#160;</mo><mi>s</mi><mi>o</mi><mo>&#160;</mo><mi>x</mi><mo>=</mo><mn>6</mn><mo>&#160;</mo><mi>d</mi><mi>a</mi><mi>y</mi><mi>s</mi></math></p>",
                    solution_hi: "<p>19.(a)&nbsp; <span style=\"font-family: Baloo;\">मान लीजिए कि, कुल कार्य 24 है, तो A और B की दक्षता क्रमशः 3 और 2 इकाई है।</span><br><strong id=\"docs-internal-guid-44de2fe6-7fff-bfff-2160-82f3d61c03bf\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdqttLSc44g1GWDYWCg8wzkEdzl2kOxIn5IzQl6O5jcnQXkTTgyk-Nw8rA6-NXpZEnCgSTTLjPaUSXv_JxqEvQTPBHBLlBc5a5_lLc-kJ2pO1flfA0fAFfvLtj6tXbGYIRzsg3soA?key=jaX7qvR187IabjuCUoAziNA2\" width=\"207\" height=\"121\"></strong><br><span style=\"font-family: Baloo;\">माना, कार्य x दिनों में पूरा होता है।</span><br><span style=\"font-family: Baloo;\">तो A (x-2) दिनों के लिए काम करता है, और B x दिनों के लिए काम करता है।</span><br><span style=\"font-family: Baloo;\">अब प्रश्न के अनुसार,</span><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><mi>x</mi><mo>-</mo><mn>2</mn><mo>)</mo><mo>&#215;</mo></math>3 + x&times;2 = 24<br><span style=\"font-family: Times New Roman;\">3x - 6 + 2x = 24</span><br><span style=\"font-family: Baloo;\">5x = 30 इसलिए, x = 6 दिन</span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "20",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Times New Roman;\">20.</span><span style=\"font-family: Times New Roman;\"> A tyre has two punctures. The first puncture alone would have made the tyre flat in 45 minutes, and the second puncture alone would have done itn 90 minutes. If air leaks out at a constant rate, then how long (in minutes) does it take for both the punctures together to make the tyre flat?</span></p>",
                    question_hi: "<p><span style=\"font-family: Times New Roman;\">20.</span><span style=\"font-family: Baloo;\"> एक टायर में दो पंक्चर होते हैं। अकेले पहले पंचर ने टायर को 45 मिनट में समतल कर दिया होता, और दूसरा पंचर अकेले 90 मिनट में कर देता। यदि हवा एक स्थिर दर से बाहर निकलती है, तो दोनों पंक्चरों को एक साथ टायर को सपाट बनाने में कितना समय (मिनटों में) लगता है?</span></p>",
                    options_en: ["<p>30</p>", "<p>15</p>", 
                                "<p>40</p>", "<p>45</p>"],
                    options_hi: ["<p>30</p>", "<p>15</p>",
                                "<p>40</p>", "<p>45</p>"],
                    solution_en: "<p>20.(a) <span style=\"font-family: Times New Roman;\">According to the question LCM of 45 and 90 is 90</span><br><strong id=\"docs-internal-guid-ca7a9743-7fff-17b5-3154-44e4f9ced227\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcMso4gLEAgveh-hli4icscecOkOrYIT0zu6SrpUtTQBklktDYN5XoTOTuAIQaSV2Z5WNe7ss-mxusyH8oK7s4opkT6WPnmvmp0vV6ww76oO5dTLA5FifHf3tFTSvZtvUlOiiT5kQ?key=gaYRg4VpqCqLXvcuhmL-Ig\" width=\"215\" height=\"120\"></strong><br><span style=\"font-family: Times New Roman;\">Efficiency of puncture1 = 2units</span><br><span style=\"font-family: Times New Roman;\">Efficiency of puncture 2 = 1units</span><br><span style=\"font-family: Times New Roman;\">Total work = 90 units</span><br><span style=\"font-family: Times New Roman;\">Total efficiency of puncture 1 and 2 is (2 + 1 ) = 3 units</span><br><span style=\"font-family: Times New Roman;\">Time taken to flatten the tyre by puncture 1 and 2 =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>time</mi><mo>&#160;</mo><mi>taken</mi><mo>=</mo><mfrac><mrow><mi>total</mi><mo>&#160;</mo><mi>work</mi><mo>&#160;</mo></mrow><mrow><mi>total</mi><mo>&#160;</mo><mi>efficiency</mi></mrow></mfrac></math></span><br><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>=</mo><mfrac><mrow><mo>&#160;</mo><mn>90</mn></mrow><mrow><mn>1</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>2</mn><mo>&#160;</mo></mrow></mfrac><mo>=</mo><mfrac><mn>90</mn><mn>3</mn></mfrac><mo>=</mo><mn>30</mn><mo>&#160;</mo><mi>m</mi><mi>i</mi><mi>n</mi><mi>u</mi><mi>t</mi><mi>e</mi><mi>s</mi><mo>&#160;</mo></math></span></p>",
                    solution_hi: "<p>20.(a) <span style=\"font-family: Baloo;\">प्रश्न के अनुसार, 45 और 90 का LCM 90 है।</span><br><strong id=\"docs-internal-guid-bd01bdae-7fff-5f5c-31c5-9db15cd5e21f\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcXkldiB3lBVL0sBGAEVfnaCfPHehXyTp6J8zdo5lTAM2fbbeAnt11klpwhlaA_RF1FQcX5ZSA96YRbtGxEElBpIoov-H_wBTlIj_pHsumaXxqGDyym35_3HEuOjce4lOOA_fNo?key=gaYRg4VpqCqLXvcuhmL-Ig\" width=\"222\" height=\"144\"></strong><br><span style=\"font-family: Baloo;\">पंचर 1 की क्षमता = 2 इकाई</span><br><span style=\"font-family: Baloo;\">पंचर 2 की क्षमता = 1 इकाई</span><br><span style=\"font-family: Baloo;\">कुल कार्य = 90 इकाई</span><br><span style=\"font-family: Baloo;\">पंचर 1 और 2 की कुल दक्षता है (2 + 1 ) = 3 इकाई</span><br><span style=\"font-family: Baloo;\">पंचर 1 और 2 द्वारा टायर को समतल करने में लगने वाला समय&nbsp;<br></span><span style=\"font-family: Baloo;\">=&nbsp;</span><span style=\"font-family: Baloo;\">लिया गया समय = <math style=\"font-family:Verdana\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#160;</mo><mfrac><mrow><mi>&#2325;&#2369;&#2354;</mi><mo>&#160;</mo><mi>&#2325;&#2366;&#2352;&#2381;&#2351;</mi></mrow><mrow><mi>&#2325;&#2369;&#2354;</mi><mo>&#160;</mo><mi>&#2342;&#2325;&#2381;&#2359;&#2340;&#2366;</mi><mo>&#160;</mo></mrow></mfrac><mo>=</mo><mfrac><mn>90</mn><mrow><mn>1</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>2</mn><mo>&#160;</mo></mrow></mfrac><mo>=</mo><mfrac><mn>90</mn><mn>3</mn></mfrac><mo>=</mo><mn>30</mn><mo>&#160;</mo></math></span><span style=\"font-family: Baloo;\"> मिनट।</span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "21",
                    section: "misc",
                    question_en: "<p><span style=\"font-family: Baloo;\">21.</span><span style=\"font-family: Baloo;\"> If 35 men can finish a work in 6 days, then in how many days can 7 men do half of the same work?</span></p>",
                    question_hi: "<p>21. <span style=\"font-family: Palanquin Dark;\">यदि 35 आदमी एक काम को 6 दिनों में पूरा कर सकते हैं, तो 7 आदमी उसी काम का आधा हिस्सा कितने दिनों में पूरा कर सकते हैं?</span></p>",
                    options_en: ["<p>30</p>", "<p>15</p>", 
                                "<p>60</p>", "<p>17</p>"],
                    options_hi: ["<p>30</p>", "<p>15</p>",
                                "<p>60</p>", "<p>17</p>"],
                    solution_en: "<p>21.(b) <span style=\"font-family: Palanquin Dark;\">Let no of required days to complete the work in 2nd condition is x</span><br><span style=\"font-family: Palanquin Dark;\">Now according to the question&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>m</mi><mi>a</mi><mi>n</mi><mn>1</mn><mo>&#215;</mo><mi>d</mi><mi>a</mi><mi>y</mi><mi>s</mi><mn>1</mn></mrow><mrow><mi>w</mi><mi>o</mi><mi>r</mi><mi>k</mi><mn>1</mn></mrow></mfrac><mo>=</mo><mfrac><mrow><mi>m</mi><mi>a</mi><mi>n</mi><mo>&#160;</mo><mn>2</mn><mo>&#215;</mo><mi>d</mi><mi>a</mi><mi>y</mi><mi>s</mi><mn>2</mn><mo>&#160;</mo></mrow><mrow><mi>w</mi><mi>o</mi><mi>r</mi><mi>k</mi><mo>&#160;</mo><mn>2</mn></mrow></mfrac><mo>&#160;</mo></math></span><br><span style=\"font-family: Palanquin Dark;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>35</mn><mo>&#215;</mo><mo>&#160;</mo><mn>6</mn></mrow><mn>1</mn></mfrac><mo>=</mo><mfrac><mrow><mn>7</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mi>x</mi></mrow><mstyle displaystyle=\"true\"><mfrac><mn>1</mn><mn>2</mn></mfrac></mstyle></mfrac><mo>=</mo></math>x = 15days</span></p>",
                    solution_hi: "<p>21.(b) <span style=\"font-family: Palanquin Dark;\">माना, कार्य को दूसरी स्थिति में पूरा करने के लिए आवश्यक दिनों की संख्या x है।</span><br><span style=\"font-family: Palanquin Dark;\">अब प्रश्न के अनुसार,</span><br><span style=\"font-family: Palanquin Dark;\"> <math style=\"font-family:Verdana\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1</mn><mo>&#160;</mo><mi>&#2310;&#2342;&#2350;&#2368;</mi><mo>&#160;</mo><mo>&#160;</mo><mo>&#215;</mo><mn>1</mn><mo>&#160;</mo><mi>&#2342;&#2367;&#2344;</mi></mrow><mrow><mn>1</mn><mo>&#160;</mo><mi>&#2325;&#2366;&#2352;&#2381;&#2351;</mi></mrow></mfrac><mo>=</mo><mfrac><mrow><mn>2</mn><mo>&#160;</mo><mi>&#2310;&#2342;&#2350;&#2368;</mi><mo>&#160;</mo><mo>&#160;</mo><mo>&#215;</mo><mn>2</mn><mo>&#160;</mo><mi>&#2342;&#2367;&#2344;</mi></mrow><mrow><mn>2</mn><mo>&#160;</mo><mi>&#2325;&#2366;&#2352;&#2381;&#2351;</mi></mrow></mfrac><mo>&#160;</mo><mo>&#160;</mo></math></span><br><span style=\"font-family: Palanquin Dark;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>35</mn><mo>&#215;</mo><mn>6</mn></mrow><mn>1</mn></mfrac><mo>=</mo><mfrac><mrow><mn>7</mn><mo>&#215;</mo><mi>x</mi></mrow><mstyle displaystyle=\"true\"><mfrac><mn>1</mn><mn>2</mn></mfrac></mstyle></mfrac><mo>=</mo></math>x = 15 दिन</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>