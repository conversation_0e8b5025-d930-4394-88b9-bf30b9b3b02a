<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">5:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 15</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">15</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 5 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["18"] = {
                name: "General Awareness",
                start: 0,
                end: 13
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="18">General Awareness</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 14,
                end: 14
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "18",
                    question_en: "<p>1. <span style=\"font-family: Cambria Math;\">In the 1930s, which of the following theories was propounded by Arthur Holmes about the </span><span style=\"font-family: Cambria Math;\">mantle ?</span></p>\n",
                    question_hi: "<p>1. <span style=\"font-family: Cambria Math;\">1930 </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2358;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2352;&#2381;&#2341;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2379;&#2350;&#2381;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2376;&#2335;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2366;&#2352;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2380;&#2344;</span><span style=\"font-family: Cambria Math;\">-</span><span style=\"font-family: Nirmala UI;\">&#2360;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2367;&#2342;&#2381;&#2343;&#2366;&#2306;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2346;&#2366;&#2342;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2341;</span><span style=\"font-family: Nirmala UI;\">&#2366;</span><span style=\"font-family: Cambria Math;\"> ?</span></p>\n",
                    options_en: ["<p>Theory of Faunal Succession</p>\n", "<p>Slab Pull Theory</p>\n", 
                                "<p>Convectional Current Theory</p>\n", "<p>Theory of Faulting</p>\n"],
                    options_hi: ["<p><span style=\"font-weight: 400;\">&#2332;&#2368;&#2357;-&#2332;&#2344;&#2381;&#2340;&#2369; &#2313;&#2340;&#2381;&#2340;&#2352;&#2366;&#2343;&#2367;&#2325;&#2366;&#2352; &#2325;&#2366; &#2360;&#2367;&#2342;&#2381;&#2343;&#2366;&#2306;&#2340; (Theory of Faunal Succession)</span></p>\n", "<p><span style=\"font-weight: 400;\">&#2360;&#2381;&#2354;&#2376;&#2348; &#2346;&#2369;&#2354; &#2360;&#2367;&#2342;&#2381;&#2343;&#2366;&#2306;&#2340; (Slab Pull Theory)</span></p>\n",
                                "<p><span style=\"font-weight: 400;\">&#2360;&#2306;&#2357;&#2361;&#2344; &#2343;&#2352;&#2366;&#2323;&#2306; &#2325;&#2366; &#2360;&#2367;&#2342;&#2381;&#2343;&#2366;&#2306;&#2340; (Convectional Current Theory)</span></p>\n", "<p><span style=\"font-weight: 400;\">&#2349;&#2381;&#2352;&#2306;&#2358; &#2325;&#2366; &#2360;&#2367;&#2342;&#2381;&#2343;&#2366;&#2306;&#2340; (Theory of Faulting)</span></p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">1.(</span><span style=\"font-family: Cambria Math;\">c) </span><strong>Convectional current Theory. The Convectional currents</strong><span style=\"font-weight: 400;\"> are generated due to radioactive elements causing thermal differences in the mantle portion. </span><strong>Arthur Holmes - </strong><span style=\"font-weight: 400;\">The first earth scientist to grasp the mechanical and thermal implications of mantle convection, which led eventually to the acceptance of plate tectonics. </span><strong>Book by Arthur Holmes : </strong><span style=\"font-weight: 400;\">Holmes Principles of Physical Geology, The age of the earth.&nbsp;</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">1.(</span><span style=\"font-family: Cambria Math;\">c)&nbsp;</span><strong>&#2360;&#2306;&#2357;&#2361;&#2344;-&#2343;&#2366;&#2352;&#2366;&#2323;&#2306; &#2325;&#2366; &#2360;&#2367;&#2342;&#2381;&#2343;&#2366;&#2306;&#2340;&#2404; </strong><span style=\"font-weight: 400;\">&#2360;&#2306;&#2357;&#2361;&#2344; &#2343;&#2366;&#2352;&#2366;&#2319;&#2305; &#2352;&#2375;&#2337;&#2367;&#2351;&#2379;&#2343;&#2352;&#2381;&#2350;&#2368; &#2340;&#2340;&#2381;&#2357;&#2379;&#2306; &#2325;&#2375; &#2325;&#2366;&#2352;&#2339; &#2313;&#2340;&#2381;&#2346;&#2344;&#2381;&#2344; &#2361;&#2379;&#2340;&#2368; &#2361;&#2376;&#2306; &#2332;&#2379; &#2350;&#2375;&#2306;&#2335;&#2354; &#2349;&#2366;&#2327; &#2350;&#2375;&#2306; &#2313;&#2359;&#2381;&#2350;&#2368;&#2351; &#2309;&#2306;&#2340;&#2352; &#2313;&#2340;&#2381;&#2346;&#2344;&#2381;&#2344; &#2325;&#2352;&#2340;&#2368; &#2361;&#2376;&#2306;&#2404; </span><strong>&#2310;&#2352;&#2381;&#2341;&#2352; &#2361;&#2379;&#2350;&#2381;&#2360; - </strong><span style=\"font-weight: 400;\">&#2350;&#2375;&#2306;&#2335;&#2354; &#2360;&#2306;&#2357;&#2361;&#2344; &#2325;&#2375; &#2351;&#2366;&#2306;&#2340;&#2381;&#2352;&#2367;&#2325; &#2324;&#2352; &#2341;&#2352;&#2381;&#2350;&#2354; &#2346;&#2381;&#2352;&#2349;&#2366;&#2357; &#2325;&#2379; &#2360;&#2350;&#2333;&#2344;&#2375; &#2357;&#2366;&#2354;&#2375; &#2346;&#2381;&#2352;&#2341;&#2350; &#2349;&#2370;-&#2357;&#2376;&#2332;&#2381;&#2334;&#2366;&#2344;&#2367;&#2325;, &#2332;&#2367;&#2360;&#2325;&#2375; &#2325;&#2366;&#2352;&#2339; &#2309;&#2306;&#2340;&#2340;&#2307; &#2346;&#2381;&#2354;&#2375;&#2335; &#2335;&#2375;&#2325;&#2381;&#2335;&#2379;&#2344;&#2367;&#2325;&#2381;&#2360; &#2325;&#2379; &#2360;&#2381;&#2357;&#2368;&#2325;&#2366;&#2352; &#2325;&#2367;&#2351;&#2366; &#2327;&#2351;&#2366;&#2404;</span><strong> &#2310;&#2352;&#2381;&#2341;&#2352; &#2361;&#2379;&#2350;&#2381;&#2360; &#2325;&#2368; &#2346;&#2369;&#2360;&#2381;&#2340;&#2325; : </strong><span style=\"font-weight: 400;\">&#2361;&#2379;&#2350;&#2381;&#2360; &#2346;&#2381;&#2352;&#2367;&#2306;&#2360;&#2367;&#2346;&#2354;&#2381;&#2360; &#2321;&#2347; &#2347;&#2367;&#2332;&#2367;&#2325;&#2354; &#2332;&#2367;&#2351;&#2379;&#2354;&#2377;&#2332;&#2368;, &#2342; &#2319;&#2332; &#2321;&#2347; &#2342; &#2309;&#2352;&#2381;&#2341;&#2404;</span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "18",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">2.</span><span style=\"font-family: Cambria Math;\"> 54.6% of the total workforce is engaged in______ and allied sector activites (Census of India 2011).</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">2.</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2369;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2358;&#2381;&#2352;&#2350;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> 54.6%__________ </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2360;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2348;&#2306;&#2343;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2340;&#2367;&#2357;&#2367;&#2343;&#2367;&#2351;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2358;&#2366;&#2350;&#2367;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2306;&#2404;</span><span style=\"font-family: Cambria Math;\"> (2011 </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2349;&#2366;&#2352;&#2340;&#2368;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2344;&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2344;&#2369;&#2360;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\">)</span></p>\n",
                    options_en: ["<p>Hunting and gathering</p>\n", "<p>Manufacturing</p>\n", 
                                "<p>Agriculture</p>\n", "<p>Mining</p>\n"],
                    options_hi: ["<p><span style=\"font-family: Nirmala UI;\">&#2358;&#2367;&#2325;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2311;&#2325;&#2335;&#2381;&#2336;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><span style=\"font-family: Nirmala UI;\">&#2313;&#2340;&#2381;&#2346;&#2366;&#2342;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                                "<p><span style=\"font-family: Nirmala UI;\">&#2325;&#2371;&#2359;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><span style=\"font-family: Nirmala UI;\">&#2326;&#2344;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">2.(</span><span style=\"font-family: Cambria Math;\">c) </span><strong>Agriculture. </strong><span style=\"font-weight: 400;\">Census 2011 was conducted by the Registrar General of India. The total number of agricultural workers in the country : 263.1 million (118.8 million cultivators and 144.3 million agricultural labourers) in 2011. The workforce participation rate for females was 25.51% against 53.26% for males. Government has</span><strong> taken measures</strong><span style=\"font-weight: 400;\"> to retain farmers in agriculture : Pradhan Mantri Krishi Sinchayee Yojana (PMKSY), provision of Price Stabilization Fund(PSF) to mitigate price volatility in agricultural produce, Scheme for Soil Health Cards, setting up of Agri-tech Infrastructure Fund.</span></p>\r\n<p>&nbsp;</p>\n",
                    solution_hi: "<p>2.(c)&nbsp;<strong>&#2325;&#2371;&#2359;&#2367; &#2404; </strong><span style=\"font-weight: 400;\">&#2332;&#2344;&#2327;&#2339;&#2344;&#2366; 2011 &#2349;&#2366;&#2352;&#2340; &#2325;&#2375; &#2352;&#2332;&#2367;&#2360;&#2381;&#2335;&#2381;&#2352;&#2366;&#2352; &#2332;&#2344;&#2352;&#2354; &#2342;&#2381;&#2357;&#2366;&#2352;&#2366; &#2310;&#2351;&#2379;&#2332;&#2367;&#2340; &#2325;&#2368; &#2327;&#2312; &#2341;&#2368;&#2404; 2011 &#2350;&#2375;&#2306; &#2342;&#2375;&#2358; &#2350;&#2375;&#2306; &#2325;&#2371;&#2359;&#2367; &#2358;&#2381;&#2352;&#2350;&#2367;&#2325;&#2379;&#2306; &#2325;&#2368; &#2325;&#2369;&#2354; &#2360;&#2306;&#2326;&#2381;&#2351;&#2366;: 263.1 &#2350;&#2367;&#2354;&#2367;&#2351;&#2344; (118.8 &#2350;&#2367;&#2354;&#2367;&#2351;&#2344; &#2325;&#2371;&#2359;&#2325; &#2324;&#2352; 144.3 &#2350;&#2367;&#2354;&#2367;&#2351;&#2344; &#2325;&#2371;&#2359;&#2367; &#2350;&#2332;&#2342;&#2370;&#2352;)&#2404; &#2350;&#2361;&#2367;&#2354;&#2366;&#2323;&#2306; &#2325;&#2375; &#2354;&#2367;&#2319; &#2325;&#2366;&#2352;&#2381;&#2351;&#2348;&#2354; &#2349;&#2366;&#2327;&#2368;&#2342;&#2366;&#2352;&#2368; &#2342;&#2352; 25.51% &#2341;&#2368; &#2332;&#2348;&#2325;&#2367; &#2346;&#2369;&#2352;&#2369;&#2359;&#2379;&#2306; &#2325;&#2375; &#2354;&#2367;&#2319; 53.26% &#2341;&#2368;&#2404; &#2360;&#2352;&#2325;&#2366;&#2352; &#2342;&#2381;&#2357;&#2366;&#2352;&#2366; &#2325;&#2367;&#2360;&#2366;&#2344;&#2379;&#2306; &#2325;&#2379; &#2325;&#2371;&#2359;&#2367; &#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352; &#2350;&#2375;&#2306; &#2348;&#2344;&#2366;&#2319; &#2352;&#2326;&#2344;&#2375; &#2325;&#2375; &#2354;&#2367;&#2319; </span><strong>&#2325;&#2367;&#2351;&#2375; &#2327;&#2319; &#2313;&#2346;&#2366;&#2351;</strong><span style=\"font-weight: 400;\">: &#2346;&#2381;&#2352;&#2343;&#2366;&#2344; &#2350;&#2306;&#2340;&#2381;&#2352;&#2368; &#2325;&#2371;&#2359;&#2367; &#2360;&#2367;&#2306;&#2330;&#2366;&#2312; &#2351;&#2379;&#2332;&#2344;&#2366; (PMKSY), &#2325;&#2371;&#2359;&#2367; &#2313;&#2346;&#2332; &#2350;&#2375;&#2306; &#2350;&#2370;&#2354;&#2381;&#2351; &#2309;&#2360;&#2381;&#2341;&#2367;&#2352;&#2340;&#2366; &#2325;&#2379; &#2325;&#2350; &#2325;&#2352;&#2344;&#2375; &#2325;&#2375; &#2354;&#2367;&#2319; &#2350;&#2370;&#2354;&#2381;&#2351; &#2360;&#2381;&#2341;&#2367;&#2352;&#2368;&#2325;&#2352;&#2339; &#2344;&#2367;&#2343;&#2367; (PSF) &#2325;&#2366; &#2346;&#2381;&#2352;&#2366;&#2357;&#2343;&#2366;&#2344;, &#2350;&#2371;&#2342;&#2366; &#2360;&#2381;&#2357;&#2366;&#2360;&#2381;&#2341;&#2381;&#2351; &#2325;&#2366;&#2352;&#2381;&#2337; &#2325;&#2375; &#2354;&#2367;&#2319; &#2351;&#2379;&#2332;&#2344;&#2366;, &#2325;&#2371;&#2359;&#2367;-&#2340;&#2325;&#2344;&#2368;&#2325;&#2368; &#2310;&#2343;&#2366;&#2352;&#2349;&#2370;&#2340;-&#2360;&#2306;&#2352;&#2330;&#2344;&#2366; &#2344;&#2367;&#2343;&#2367; &#2325;&#2368; &#2360;&#2381;&#2341;&#2366;&#2346;&#2344;&#2366; &#2325;&#2368; &#2327;&#2312; &#2404;</span></p>\r\n<p>&nbsp;</p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "18",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">3. </span><span style=\"font-family: Cambria Math;\">According to Census of India 2011, which state in India has the highest proportion of its population living in the urban areas?</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">3. </span><span style=\"font-family: Nirmala UI;\">&#2349;&#2366;&#2352;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2344;&#2327;&#2339;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> 2011 </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2344;&#2369;&#2360;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2349;&#2366;&#2352;&#2340;&#2368;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2352;&#2366;&#2332;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2327;&#2352;&#2368;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2344;&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2344;&#2369;&#2346;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2352;&#2381;&#2357;&#2366;&#2343;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>Kerala</p>\n", "<p>Haryana</p>\n", 
                                "<p>Goa</p>\n", "<p>Tamil Nadu</p>\n"],
                    options_hi: ["<p><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;&#2352;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><span style=\"font-family: Nirmala UI;\">&#2361;&#2352;&#2367;&#2351;&#2366;&#2339;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                                "<p><span style=\"font-family: Nirmala UI;\">&#2327;&#2379;&#2357;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><span style=\"font-family: Nirmala UI;\">&#2340;&#2350;&#2367;&#2354;&#2344;&#2366;&#2337;&#2369;</span></p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">3.(</span><span style=\"font-family: Cambria Math;\">c) <strong>Goa</strong> </span><span style=\"font-family: Cambria Math;\">(62.2% urban population)</span><span style=\"font-family: Cambria Math;\">. <strong>Lower level of urbanization</strong>: </span><span style=\"font-family: Cambria Math;\">Bihar, Odisha, Assam, and Uttar Pradesh. </span><strong><span style=\"font-family: Cambria Math;\">Union Territories</span></strong><span style=\"font-family: Cambria Math;\">: The NCT of Delhi (97.5%)</span><span style=\"font-family: Cambria Math;\"> and Chandigarh (97.25%) are most urbanized. India\'s Population: 1,210.6 million, Uttar Pradesh (Most populous state), Sikkim (lowest population </span><span style=\"font-family: Cambria Math;\">state )</span><span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">3.(</span><span style=\"font-family: Cambria Math;\">c) </span><strong>&#2327;&#2379;&#2357;&#2366; </strong><span style=\"font-weight: 400;\">(62.2% &#2358;&#2361;&#2352;&#2368; &#2332;&#2344;&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;)&#2404; </span><strong>&#2358;&#2361;&#2352;&#2368;&#2325;&#2352;&#2339; &#2325;&#2366; &#2344;&#2367;&#2350;&#2381;&#2344; &#2360;&#2381;&#2340;&#2352;: </strong><span style=\"font-weight: 400;\">&#2348;&#2367;&#2361;&#2366;&#2352;, &#2323;&#2337;&#2367;&#2358;&#2366;, &#2309;&#2360;&#2350; &#2324;&#2352; &#2313;&#2340;&#2381;&#2340;&#2352; &#2346;&#2381;&#2352;&#2342;&#2375;&#2358;&#2404; </span><strong>&#2325;&#2375;&#2306;&#2342;&#2381;&#2352; &#2358;&#2366;&#2360;&#2367;&#2340; &#2346;&#2381;&#2352;&#2342;&#2375;&#2358;: </strong><span style=\"font-weight: 400;\">&#2352;&#2366;&#2359;&#2381;&#2335;&#2381;&#2352;&#2368;&#2351; &#2352;&#2366;&#2332;&#2343;&#2366;&#2344;&#2368; &#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352; &#2342;&#2367;&#2354;&#2381;&#2354;&#2368; (97.5%) &#2324;&#2352; &#2330;&#2306;&#2337;&#2368;&#2327;&#2338;&#2364; (97.25%) &#2360;&#2348;&#2360;&#2375; &#2309;&#2343;&#2367;&#2325; &#2358;&#2361;&#2352;&#2368;&#2325;&#2371;&#2340; &#2361;&#2376;&#2306;&#2404; &#2349;&#2366;&#2352;&#2340; &#2325;&#2368; &#2332;&#2344;&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;: 1,210.6 &#2350;&#2367;&#2354;&#2367;&#2351;&#2344;, &#2313;&#2340;&#2381;&#2340;&#2352; &#2346;&#2381;&#2352;&#2342;&#2375;&#2358; (&#2360;&#2348;&#2360;&#2375; &#2309;&#2343;&#2367;&#2325; &#2310;&#2348;&#2366;&#2342;&#2368; &#2357;&#2366;&#2354;&#2366; &#2352;&#2366;&#2332;&#2381;&#2351;), &#2360;&#2367;&#2325;&#2381;&#2325;&#2367;&#2350; (&#2360;&#2348;&#2360;&#2375; &#2325;&#2350; &#2310;&#2348;&#2366;&#2342;&#2368; &#2357;&#2366;&#2354;&#2366; &#2352;&#2366;&#2332;&#2381;&#2351; )&#2404;</span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "18",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">4.</span><span style=\"font-family: Cambria Math;\"> Which is the biggest tributary of the upper Brahmaputra that originates in the Tibetan Himalayas and winds its way into India through Arunachal </span><span style=\"font-family: Cambria Math;\">Pradesh ?</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">4. </span><span style=\"font-family: Nirmala UI;\">&#2314;&#2346;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2381;&#2352;&#2361;&#2381;&#2350;&#2346;&#2369;&#2340;&#2381;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2348;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2337;&#2364;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2361;&#2366;&#2351;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2342;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2380;&#2344;</span><span style=\"font-family: Cambria Math;\">-</span><span style=\"font-family: Nirmala UI;\">&#2360;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2367;&#2348;&#2381;&#2348;&#2340;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2367;&#2350;&#2366;&#2354;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2367;&#2325;&#2354;&#2340;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2352;&#2369;&#2339;&#2366;&#2330;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2342;&#2375;&#2358;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2379;&#2340;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2369;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2349;&#2366;&#2352;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2357;&#2375;&#2358;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;&#2340;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>Subansiri</p>\n", "<p>Yerla</p>\n", 
                                "<p>Musi</p>\n", "<p>Aner</p>\n"],
                    options_hi: ["<p><span style=\"font-family: Nirmala UI;\">&#2360;&#2369;&#2348;&#2344;&#2360;&#2367;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><span style=\"font-family: Nirmala UI;\">&#2351;&#2375;&#2352;&#2354;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                                "<p><span style=\"font-family: Nirmala UI;\">&#2350;&#2370;&#2360;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><span style=\"font-family: Nirmala UI;\">&#2309;&#2344;&#2375;&#2352;</span></p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">4.(</span><span style=\"font-family: Cambria Math;\">a) <strong>Subansiri. Brahmaputra River</strong> </span><span style=\"font-family: Cambria Math;\">originates in the Chemayungdung glacier of the Kailash range near the Mansarovar </span><span style=\"font-family: Cambria Math;\">lake</span><span style=\"font-family: Cambria Math;\"> in Tibet. It enters India through Arunachal Pradesh, Assam and then enters Bangladesh and finally falls in the Bay of Bengal. </span><span style=\"font-family: Cambria Math;\"><strong>Tributaries:</strong> </span><span style=\"font-family: Cambria Math;\"> Ronganadi, Dikrong, Buroi, Bo</span><span style=\"font-family: Cambria Math;\">rgong, Jiabharali, Dhansiri (North), Puthimari, Manas, Beki, Sonkosh are the main tributaries on the North while the Noadehing, Buridehing, Desang, Dikhow, Bhogdoi, Dhansiri (South), Kopilli, Kulsi, Krishnai, Dhdhnoi, Jinjiran are the main tributaries on t</span><span style=\"font-family: Cambria Math;\">he South. </span></p>\n",
                    solution_hi: "<p>4.(a)<strong> <span style=\"font-family: Nirmala UI;\">&#2360;&#2369;&#2348;&#2344;&#2360;&#2367;&#2352;&#2368;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2381;&#2352;&#2361;&#2381;&#2350;&#2346;&#2369;&#2340;&#2381;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2342;&#2368;</span></strong><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2342;&#2381;&#2327;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2367;&#2348;&#2381;&#2348;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2344;&#2360;&#2352;&#2379;&#2357;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2333;&#2368;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2366;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2376;&#2354;&#2366;&#2358;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;&#2381;&#2357;&#2340;&#2350;&#2366;&#2354;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2330;&#2375;&#2350;&#2366;&#2351;&#2369;&#2306;&#2327;&#2337;&#2369;&#2306;&#2327;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2381;&#2354;&#2375;&#2358;&#2367;&#2351;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2379;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2351;&#2361;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2352;&#2369;&#2339;&#2366;&#2330;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2342;&#2375;&#2358;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2360;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2379;&#2340;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2369;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2349;&#2366;&#2352;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2357;&#2375;&#2358;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;&#2340;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2347;&#2367;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2366;&#2306;&#2327;&#2381;&#2354;&#2366;&#2342;&#2375;&#2358;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2357;&#2375;&#2358;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;&#2340;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2344;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2306;&#2327;&#2366;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2326;&#2366;&#2337;&#2364;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2367;&#2352;&#2340;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><strong><span style=\"font-family: Nirmala UI;\">&#2360;&#2361;&#2366;&#2351;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2342;&#2367;&#2351;&#2366;&#2305;</span></strong><span style=\"font-family: Cambria Math;\">:</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2352;&#2379;&#2306;&#2327;&#2344;&#2366;&#2337;&#2368;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2367;&#2325;&#2352;&#2379;&#2306;&#2327;</span><span style=\"font-family: Cambria Math;\"> , </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2369;&#2352;&#2379;&#2312;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2379;&#2352;&#2381;&#2327;&#2379;&#2306;&#2327;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2367;&#2351;&#2366;&#2349;&#2352;&#2366;&#2354;&#2368;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2343;&#2344;&#2360;&#2367;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\"> (</span><span style=\"font-family: Nirmala UI;\">&#2313;&#2340;&#2381;&#2340;&#2352;</span><span style=\"font-family: Cambria Math;\">), </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2369;&#2336;&#2367;&#2350;&#2366;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2344;&#2360;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2375;&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2325;&#2379;&#2358;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2340;&#2381;&#2340;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2350;&#2369;&#2326;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2361;&#2366;&#2351;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2342;&#2367;&#2351;&#2366;&#2305;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2348;&#2325;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2379;&#2310;</span><span style=\"font-family: Cambria Math;\">-</span><span style=\"font-family: Nirmala UI;\">&#2342;&#2367;&#2361;&#2367;&#2306;&#2327;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2370;&#2338;&#2364;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2367;&#2361;&#2367;&#2306;&#2327;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2375;&#2360;&#2366;&#2306;&#2327;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2367;&#2326;&#2380;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2342;&#2368;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2349;&#2379;&#2327;&#2342;&#2379;&#2312;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2343;&#2344;&#2360;&#2368;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\"> (</span><span style=\"font-family: Nirmala UI;\">&#2342;&#2325;&#2381;&#2359;&#2367;&#2339;</span><span style=\"font-family: Cambria Math;\">), </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;&#2346;&#2367;&#2354;&#2381;&#2354;&#2368;</span><span style=\"font-family: Cambria Math;\"> , </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2369;&#2354;&#2360;&#2368;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2325;</span><span style=\"font-family: Nirmala UI;\">&#2371;&#2359;&#2381;&#2339;&#2366;&#2312;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2343;&#2343;&#2344;&#2379;&#2312;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2367;&#2344;&#2332;&#2367;&#2352;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2325;&#2381;&#2359;&#2367;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2350;&#2369;&#2326;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2361;&#2366;&#2351;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2342;&#2367;&#2351;&#2366;&#2305;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2306;&#2404;</span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "18",
                    question_en: "<p>5.<span style=\"font-family: Cambria Math;\"> Which of the following states is NOT a part of the Peninsular Plateau in </span><span style=\"font-family: Cambria Math;\">India ?</span></p>\n",
                    question_hi: "<p>5.<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2380;&#2344;</span><span style=\"font-family: Cambria Math;\">-</span><span style=\"font-family: Nirmala UI;\">&#2360;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2349;&#2366;&#2352;&#2340;&#2368;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2352;&#2366;&#2332;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2366;&#2351;&#2342;&#2381;&#2357;&#2368;&#2346;&#2368;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2336;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2349;&#2366;&#2327;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2361;&#2368;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>Maharashtra</p>\n", "<p>Madhya Pradesh</p>\n", 
                                "<p>Punjab</p>\n", "<p>Tamil Nadu</p>\n"],
                    options_hi: ["<p><span style=\"font-family: Nirmala UI;\">&#2350;&#2361;&#2366;&#2352;&#2366;&#2359;&#2381;&#2335;&#2381;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><span style=\"font-family: Nirmala UI;\">&#2350;&#2343;&#2381;&#2351;&#2346;&#2381;&#2352;&#2342;&#2375;&#2358;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                                "<p><span style=\"font-family: Nirmala UI;\">&#2346;&#2306;&#2332;&#2366;&#2348;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><span style=\"font-family: Nirmala UI;\">&#2340;&#2350;&#2367;&#2354;&#2344;&#2366;&#2337;&#2369;</span></p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">5.(</span><span style=\"font-family: Cambria Math;\">c)<strong> Punjab. Peninsular plateau</strong>: </span><span style=\"font-family: Cambria Math;\">Triangular in shape and bounded on all sides by hill ranges. It covers ei</span><span style=\"font-family: Cambria Math;\">ght states of India, namely &ndash; Rajasthan, Maharashtra, Gujarat, Telangana, Karnataka, Tamil Nadu, Kerala, and Odisha.<strong> </strong></span><span style=\"font-family: Cambria Math;\"><strong>Different Plateaus in India</strong>:</span><span style=\"font-family: Cambria Math;\"> Marwar Plateau, Central Highland, Bundelkhand Upland, Malwa Plateau, Baghelkhand, Chota Nagpur Plateau, Meghal</span><span style=\"font-family: Cambria Math;\">aya Plateau, Deccan Plateau, and Maharashtra Plateau. </span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">5.(</span><span style=\"font-family: Cambria Math;\">c) </span><strong><span style=\"font-family: Nirmala UI;\">&#2346;&#2306;&#2332;&#2366;&#2348;</span></strong><span style=\"font-family: Nirmala UI;\">&#2404;</span><strong><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2366;&#2351;&#2342;&#2381;&#2357;&#2368;&#2346;&#2368;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2336;&#2366;&#2352;</span></strong><span style=\"font-family: Cambria Math;\">:</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2325;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2381;&#2352;&#2367;&#2325;&#2379;&#2339;&#2368;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2349;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2352;&#2347;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2361;&#2366;&#2337;&#2364;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;&#2381;&#2357;&#2340;&#2350;&#2366;&#2354;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2328;&#2367;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2369;&#2310;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2311;&#2360;&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2349;&#2366;&#2352;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2336;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2352;&#2366;&#2332;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2358;&#2366;&#2350;&#2367;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2306;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2352;&#2366;&#2332;&#2360;&#2381;&#2341;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2361;&#2366;&#2352;&#2366;&#2359;&#2381;&#2335;&#2381;&#2352;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2369;&#2332;&#2352;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2375;&#2354;&#2306;&#2327;&#2366;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;&#2381;&#2344;&#2366;&#2335;&#2325;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2350;&#2367;&#2354;&#2344;&#2366;&#2337;&#2369;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Nirmala UI;\">&#2352;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2323;&#2337;&#2367;&#2358;&#2366;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><strong><span style=\"font-family: Nirmala UI;\">&#2349;&#2366;&#2352;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2349;&#2367;&#2344;&#2381;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2336;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\">:</span></strong><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2352;&#2357;&#2366;&#2337;&#2364;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2336;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2343;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2330;&#2381;&#2330;&#2349;&#2370;&#2350;&#2367;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2369;&#2344;&#2381;&#2342;&#2375;&#2354;&#2326;&#2339;&#2381;&#2337;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2346;&#2349;&#2370;&#2350;&#2367;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2354;&#2357;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2336;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2366;&#2328;&#2375;&#2354;&#2326;&#2306;&#2337;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2331;&#2379;&#2335;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2366;&#2327;&#2346;&#2369;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2336;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2328;&#2366;&#2354;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2336;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2325;&#2381;&#2325;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2336;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2361;&#2366;&#2352;&#2366;&#2359;&#2381;&#2335;&#2381;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2336;&#2366;&#2352;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "18",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">6. </span><span style=\"font-family: Cambria Math;\">What is the type of soil composed of silt and </span><span style=\"font-family: Cambria Math;\">dust ?</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">6. </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2366;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2343;&#2370;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2344;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2367;&#2335;&#2381;&#2335;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2325;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2379;&#2340;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>Loam</p>\n", "<p>Marl</p>\n", 
                                "<p>Loess</p>\n", "<p>Podsol</p>\n"],
                    options_hi: ["<p><span style=\"font-family: Nirmala UI;\">&#2354;&#2379;&#2350;</span><span style=\"font-family: Cambria Math;\"> (Loam) </span></p>\n", "<p><span style=\"font-weight: 400;\">&#2350;&#2366;&#2352;&#2381;&#2354; (Marl)</span></p>\n",
                                "<p><span style=\"font-family: Nirmala UI;\">&#2354;&#2379;&#2319;&#2360;</span><span style=\"font-family: Cambria Math;\"> (Loess) </span></p>\n", "<p><span style=\"font-weight: 400;\">&#2346;&#2377;&#2337;&#2360;&#2377;&#2354; (Podsol)</span></p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">6.(</span><span style=\"font-family: Cambria Math;\">c) <strong>Loess.</strong></span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">Properties :</span><span style=\"font-family: Cambria Math;\"> Colour - Yellowish Brown, Homogeneous and Highly Porous. Mostly </span><span style=\"font-family: Cambria Math;\">Found (India) - Kashmir Valley. </span><span style=\"font-family: Cambria Math;\"><strong>Loam</strong> </span><span style=\"font-family: Cambria Math;\">-The type of Soil composed of sand, silt, and clay soil. </span><span style=\"font-family: Cambria Math;\"><strong>Marl</strong> </span><span style=\"font-family: Cambria Math;\">- The type of soil composed of Clays and Slit. </span><span style=\"font-family: Cambria Math;\"><strong>Podsol</strong> </span><span style=\"font-family: Cambria Math;\">- The soil derives from either quartz-rich sands and sandstone or sedimentary debris from magmatic rocks. </span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">6.(</span><span style=\"font-family: Cambria Math;\">c)</span><strong>&nbsp;&#2354;&#2379;&#2319;&#2360;</strong><span style=\"font-weight: 400;\"> (Loess) &#2404; &#2327;&#2369;&#2339; : &#2352;&#2306;&#2327; - &#2346;&#2368;&#2354;&#2366; &#2349;&#2370;&#2352;&#2366;, &#2360;&#2332;&#2366;&#2340;&#2368;&#2351; &#2324;&#2352; &#2309;&#2340;&#2381;&#2351;&#2343;&#2367;&#2325; &#2331;&#2367;&#2342;&#2381;&#2352;&#2346;&#2370;&#2352;&#2381;&#2339;&#2404; &#2351;&#2361; &#2332;&#2381;&#2351;&#2366;&#2342;&#2366;&#2340;&#2352; &#2349;&#2366;&#2352;&#2340; &#2325;&#2368; &#2325;&#2358;&#2381;&#2350;&#2368;&#2352; &#2328;&#2366;&#2335;&#2368; &#2350;&#2375;&#2306; &#2346;&#2366;&#2351;&#2368; &#2332;&#2366;&#2340;&#2368;&nbsp; &#2361;&#2376;&#2404; </span><strong>&#2354;&#2379;&#2350;</strong><span style=\"font-weight: 400;\"> (Loam) - &#2351;&#2361; &#2352;&#2375;&#2340;, &#2327;&#2366;&#2342; &#2324;&#2352; &#2330;&#2367;&#2325;&#2344;&#2368; &#2350;&#2367;&#2335;&#2381;&#2335;&#2368; &#2360;&#2375; &#2348;&#2344;&#2366; &#2350;&#2367;&#2335;&#2381;&#2335;&#2368; &#2325;&#2366; &#2319;&#2325; &#2346;&#2381;&#2352;&#2325;&#2366;&#2352; &#2361;&#2376;&#2404; </span><strong>&#2350;&#2366;&#2352;&#2381;&#2354; </strong><span style=\"font-weight: 400;\">(Marl) - &#2330;&#2367;&#2325;&#2344;&#2368; &#2350;&#2367;&#2335;&#2381;&#2335;&#2368; &#2324;&#2352; &#2360;&#2381;&#2354;&#2367;&#2335; &#2360;&#2375; &#2348;&#2344;&#2368; &#2350;&#2367;&#2335;&#2381;&#2335;&#2368; &#2325;&#2366; &#2319;&#2325; &#2346;&#2381;&#2352;&#2325;&#2366;&#2352;&#2404; </span><strong>&#2346;&#2377;&#2337;&#2360;&#2377;&#2354;</strong><span style=\"font-weight: 400;\"> (Podsol) - &#2350;&#2367;&#2335;&#2381;&#2335;&#2368; &#2351;&#2366; &#2340;&#2379; &#2325;&#2381;&#2357;&#2366;&#2352;&#2381;&#2335;&#2381;&#2332;-&#2351;&#2369;&#2325;&#2381;&#2340;&nbsp; (quartz-rich) &#2352;&#2375;&#2340; &#2324;&#2352; &#2348;&#2354;&#2369;&#2310; &#2346;&#2340;&#2381;&#2341;&#2352; &#2351;&#2366; &#2350;&#2376;&#2327;&#2381;&#2350;&#2376;&#2335;&#2367;&#2325; &#2330;&#2335;&#2381;&#2335;&#2366;&#2344;&#2379;&#2306; &#2360;&#2375; &#2340;&#2354;&#2331;&#2335;&#2368; &#2350;&#2354;&#2348;&#2375; &#2360;&#2375; &#2346;&#2381;&#2352;&#2366;&#2346;&#2381;&#2340; &#2361;&#2379;&#2340;&#2368; &#2361;&#2376;&#2404;</span></p>\r\n<p>&nbsp;</p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "18",
                    question_en: "<p>7. <span style=\"font-family: Cambria Math;\"> Which state in India had the lowest sex ratio according to the 2011 </span><span style=\"font-family: Cambria Math;\">census ?</span></p>\n",
                    question_hi: "<p>7. <span style=\"font-family: Cambria Math;\">2011 </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2344;&#2327;&#2339;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2344;&#2369;&#2360;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2349;&#2366;&#2352;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2352;&#2366;&#2332;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2367;&#2306;&#2327;&#2366;&#2344;&#2369;&#2346;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2348;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2341;&#2366;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>Punjab</p>\n", "<p>Rajasthan</p>\n", 
                                "<p>Bihar</p>\n", "<p>Haryana</p>\n"],
                    options_hi: ["<p><span style=\"font-family: Nirmala UI;\">&#2346;&#2306;&#2332;&#2366;&#2348;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><span style=\"font-family: Nirmala UI;\">&#2352;&#2366;&#2332;&#2360;&#2381;&#2341;&#2366;&#2344;</span></p>\n",
                                "<p><span style=\"font-family: Nirmala UI;\">&#2348;&#2367;&#2361;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><span style=\"font-family: Nirmala UI;\">&#2361;&#2352;&#2367;&#2351;&#2366;&#2339;&#2366;</span></p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">7.(</span><span style=\"font-family: Cambria Math;\">d) <strong>Haryana</strong> - </span><span style=\"font-family: Cambria Math;\">Sex ratio - 879 Females per 1000 Males. Highest Sex Ratio - Kerala (1084 females per 1000 males). According to Census 2011 - Most populous state (Uttar Pradesh), Least populous state/UT (Lakshadweep), Highest Population UT (Delhi), Highest Literacy (Kerala</span><span style=\"font-family: Cambria Math;\">), Highest population density state (Bihar).</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">7.(</span><span style=\"font-family: Cambria Math;\">d) </span><strong><span style=\"font-family: Nirmala UI;\">&#2361;&#2352;&#2367;&#2351;&#2366;&#2339;&#2366;</span></strong><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">- </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2367;&#2306;&#2327;&#2366;&#2344;&#2369;&#2346;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> - </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\"> 1000 </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2369;&#2352;&#2369;&#2359;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> 879 </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2361;&#2367;&#2354;&#2366;&#2319;&#2306;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2330;&#2381;&#2330;&#2340;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2367;&#2306;&#2327;&#2366;&#2344;&#2369;&#2346;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> - </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;&#2352;&#2354;</span><span style=\"font-family: Cambria Math;\"> (</span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\"> 1000 </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2369;&#2352;&#2369;&#2359;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> 1084 </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2361;&#2367;&#2354;&#2366;&#2319;&#2305;</span><span style=\"font-family: Cambria Math;\">)</span><span style=\"font-family: Nirmala UI;\">&#2404;</span><span style=\"font-family: Cambria Math;\"> 2011 </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2344;&#2327;&#2339;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2344;&#2369;&#2360;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> - </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2352;&#2381;&#2357;&#2366;&#2343;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2348;&#2366;&#2342;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2366;&#2354;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2352;&#2366;&#2332;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> (</span><span style=\"font-family: Nirmala UI;\">&#2313;&#2340;&#2381;&#2340;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2342;&#2375;&#2358;</span><span style=\"font-family: Cambria Math;\">), </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2381;&#2351;&#2370;&#2344;&#2340;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2348;&#2366;&#2342;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2366;&#2354;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2352;&#2366;&#2332;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\">/</span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;&#2306;&#2342;&#2381;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2358;&#2366;&#2360;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2342;&#2375;&#2358;</span><span style=\"font-family: Cambria Math;\"> (</span><span style=\"font-family: Nirmala UI;\">&#2354;&#2325;&#2381;&#2359;&#2342;&#2381;&#2357;&#2368;&#2346;</span><span style=\"font-family: Cambria Math;\">), </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2352;&#2381;&#2357;&#2366;&#2343;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2348;&#2366;&#2342;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2366;&#2354;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;&#2306;&#2342;&#2381;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2358;&#2366;&#2360;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2342;&#2375;&#2358;</span><span style=\"font-family: Cambria Math;\"> (</span><span style=\"font-family: Nirmala UI;\">&#2342;&#2367;&#2354;&#2381;&#2354;&#2368;</span><span style=\"font-family: Cambria Math;\">), </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2352;&#2381;&#2357;&#2366;&#2343;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2366;&#2325;&#2381;&#2359;&#2352;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> (</span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;&#2352;&#2354;</span><span style=\"font-family: Cambria Math;\">), </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2352;&#2381;&#2357;&#2366;&#2343;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2344;&#2360;&#2305;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2328;&#2344;&#2340;&#2381;&#2357;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2366;&#2354;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2352;&#2366;&#2332;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> (</span><span style=\"font-family: Nirmala UI;\">&#2348;&#2367;&#2361;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\">)</span><span style=\"font-family: Nirmala UI;\">&#2404;</span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "18",
                    question_en: "<p>8.<span style=\"font-family: Cambria Math;\"> What is the phenomenon called when rain fails to occur for one or more weeks du</span><span style=\"font-family: Cambria Math;\">ring the south-west monsoon </span><span style=\"font-family: Cambria Math;\">period ?</span></p>\n",
                    question_hi: "<p>8.<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2325;&#2381;&#2359;&#2367;&#2339;</span><span style=\"font-family: Cambria Math;\">-</span><span style=\"font-family: Nirmala UI;\">&#2346;&#2358;&#2381;&#2330;&#2367;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2344;&#2360;&#2370;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2348;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\">-</span><span style=\"font-family: Nirmala UI;\">&#2342;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2346;&#2381;&#2340;&#2366;&#2361;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2352;&#2381;&#2359;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2311;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;&#2367;&#2328;&#2335;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2361;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2366;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>Withdrawal of the monsoon</p>\n", "<p>Break in the monsoon</p>\n", 
                                "<p>El Nino</p>\n", "<p>Monsoon depression</p>\n"],
                    options_hi: ["<p><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2344;&#2360;&#2370;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2366;&#2346;&#2360;&#2368;</span></p>\n", "<p><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2344;&#2360;&#2370;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2330;&#2381;&#2331;&#2375;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                                "<p><span style=\"font-family: Nirmala UI;\">&#2319;&#2354;</span><span style=\"font-family: Cambria Math;\">-</span><span style=\"font-family: Nirmala UI;\">&#2344;&#2367;&#2344;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2344;&#2360;&#2370;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2357;&#2360;&#2366;&#2342;</span></p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">8.(</span><span style=\"font-family: Cambria Math;\">b)<strong> Break in the monsoon</strong>. </span><span style=\"font-family: Cambria Math;\">The southwest monsoon season - Rainfall received from the southwest monsoons is seasonal in character, which occurs between June and Sep</span><span style=\"font-family: Cambria Math;\">tember. The North-East Monsoons (retreating monsoon</span><span style=\"font-family: Cambria Math;\">) -</span><span style=\"font-family: Cambria Math;\"> The months of October and November. </span><span style=\"font-family: Cambria Math;\">This monsoon</span><span style=\"font-family: Cambria Math;\"> is limited to south India, bringing rain to Tamil Nadu, Puducherry, Karaikal, Yanam, Andhra Pradesh, Kerala, Mahe.</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">8.(</span><span style=\"font-family: Cambria Math;\">b) </span><strong><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2344;&#2360;&#2370;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span></strong><span style=\"font-family: Nirmala UI;\"><strong>&#2357;&#2367;&#2330;&#2381;&#2331;&#2375;&#2342;</strong>&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2325;&#2381;&#2359;&#2367;&#2339;</span><span style=\"font-family: Cambria Math;\">-</span><span style=\"font-family: Nirmala UI;\">&#2346;&#2358;&#2381;&#2330;&#2367;&#2350;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2344;&#2360;&#2370;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2315;&#2340;&#2369;</span><span style=\"font-family: Cambria Math;\"> - </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2325;&#2381;&#2359;&#2367;&#2339;</span><span style=\"font-family: Cambria Math;\">-</span><span style=\"font-family: Nirmala UI;\">&#2346;&#2358;&#2381;&#2330;&#2367;&#2350;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2344;&#2360;&#2370;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2366;&#2346;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2352;&#2381;&#2359;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2325;&#2371;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2380;&#2360;&#2350;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2379;&#2340;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2370;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2367;&#2340;&#2306;&#2348;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2368;&#2330;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2379;&#2340;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2340;&#2381;&#2340;&#2352;</span><span style=\"font-family: Cambria Math;\">-</span><span style=\"font-family: Nirmala UI;\">&#2346;&#2370;&#2352;&#2381;&#2357;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2344;&#2360;&#2370;&#2344;</span><span style=\"font-family: Cambria Math;\"> (</span><span style=\"font-family: Nirmala UI;\">&#2357;&#2366;&#2346;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2380;&#2335;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2344;&#2360;&#2370;&#2344;</span><span style=\"font-family: Cambria Math;\">) - </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2325;&#2381;&#2335;&#2370;&#2348;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2357;&#2306;&#2348;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2361;&#2368;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2379;&#2340;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2351;&#2361;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2344;&#2360;&#2370;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2325;&#2381;&#2359;&#2367;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2349;&#2366;&#2352;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2368;&#2350;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2367;&#2360;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2350;&#2367;&#2354;&#2344;</span><span style=\"font-family: Nirmala UI;\">&#2366;&#2337;&#2369;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2369;&#2342;&#2369;&#2330;&#2375;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;&#2366;&#2312;&#2325;&#2354;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2351;&#2366;&#2344;&#2350;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2306;&#2343;&#2381;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2342;&#2375;&#2358;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;&#2352;&#2354;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2361;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2352;&#2381;&#2359;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2379;&#2340;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "18",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">9.</span><span style=\"font-family: Cambria Math;\"> Which state in India has the highest literacy rate as per Census of 2011?</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">9. </span><span style=\"font-family: Cambria Math;\">2011 </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2344;&#2327;&#2339;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2344;&#2369;&#2360;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2349;&#2366;&#2352;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2352;&#2366;&#2332;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2366;&#2325;&#2381;&#2359;&#2352;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2352;&#2381;&#2357;&#2366;&#2343;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>Bihar</p>\n", "<p>Maharashtra</p>\n", 
                                "<p>Kerala</p>\n", "<p>Tamil Nadu</p>\n"],
                    options_hi: ["<p><span style=\"font-family: Nirmala UI;\">&#2348;&#2367;&#2361;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><span style=\"font-family: Nirmala UI;\">&#2350;&#2361;&#2366;&#2352;&#2366;&#2359;&#2381;&#2335;&#2381;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                                "<p><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;&#2352;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><span style=\"font-family: Nirmala UI;\">&#2340;&#2350;&#2367;&#2354;&#2344;&#2366;&#2337;&#2369;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">9.(</span><span style=\"font-family: Cambria Math;\">c)<strong> Kerala (94%). Census 2011</strong>: </span><span style=\"font-family: Cambria Math;\">Literacy: India - 74.04%. Lowest Literacy rate - Bihar (61.80%). Sex Ratio -</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">India </span><span style=\"font-family: Cambria Math;\">(943)</span><span style=\"font-family: Cambria Math;\">. In state: </span><span style=\"font-family: Cambria Math;\">Highest Sex Ratio - Kerala (1084), while Lowest Sex Ratio - Haryana (879). Union Territories: Highest - Puducherry (1037)</span><span style=\"font-family: Cambria Math;\">, lowest</span><span style=\"font-family: Cambria Math;\"> - Daman and Diu (618). The Decadal growth rate of population in terms of percentage was highest in India in</span><span style=\"font-family: Cambria Math;\"> 1971. The population density of India - 382 per sq km.</span></p>\n",
                    solution_hi: "<p>9.(c)<strong> <span style=\"font-family: Nirmala UI;\">&#2325;&#2375;&#2352;&#2354;</span><span style=\"font-family: Cambria Math;\"> (94%)</span><span style=\"font-family: Nirmala UI;\">&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2344;&#2327;&#2339;&#2344;&#2366;</span></strong><span style=\"font-family: Cambria Math;\"><strong> 2011</strong>:</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2366;&#2325;&#2381;&#2359;&#2352;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\">: </span><span style=\"font-family: Nirmala UI;\">&#2349;&#2366;&#2352;&#2340;</span><span style=\"font-family: Cambria Math;\"> - 74.04%</span><span style=\"font-family: Nirmala UI;\">&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2381;&#2351;&#2370;&#2344;&#2340;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2366;&#2325;&#2381;&#2359;&#2352;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2352;</span><span style=\"font-family: Cambria Math;\"> - </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2367;&#2361;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> (61.80%)</span><span style=\"font-family: Nirmala UI;\">&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2367;&#2306;&#2327;&#2366;&#2344;&#2369;&#2346;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> - </span><span style=\"font-family: Nirmala UI;\">&#2349;&#2366;&#2352;&#2340;</span><span style=\"font-family: Cambria Math;\"> (943)</span><span style=\"font-family: Nirmala UI;\">&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2352;&#2366;&#2332;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\">: </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2330;&#2381;&#2330;&#2340;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2367;&#2306;&#2327;&#2366;&#2344;&#2369;&#2346;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> - </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;&#2352;&#2354;</span><span style=\"font-family: Cambria Math;\"> (1084), </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2348;&#2325;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2348;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2367;&#2306;&#2327;&#2366;&#2344;&#2369;&#2346;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> - </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2352;&#2367;&#2351;</span><span style=\"font-family: Nirmala UI;\">&#2366;&#2339;&#2366;</span><span style=\"font-family: Cambria Math;\"> (879)</span><span style=\"font-family: Nirmala UI;\">&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;&#2306;&#2342;&#2381;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2358;&#2366;&#2360;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2342;&#2375;&#2358;</span><span style=\"font-family: Cambria Math;\">: </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2330;&#2381;&#2330;&#2340;&#2350;</span><span style=\"font-family: Cambria Math;\"> - </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2369;&#2337;&#2369;&#2330;&#2375;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\"> (1037), </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2348;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2350;</span><span style=\"font-family: Cambria Math;\"> - </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2350;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2368;&#2357;</span><span style=\"font-family: Cambria Math;\"> (618)</span><span style=\"font-family: Nirmala UI;\">&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2352;&#2370;&#2346;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2344;&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2358;&#2325;&#2368;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2371;&#2342;&#2381;&#2343;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2352;</span><span style=\"font-family: Cambria Math;\"> 1971 </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2349;&#2366;&#2352;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2348;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2343;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2341;&#2368;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2349;&#2366;&#2352;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2344;&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2328;&#2344;&#2340;&#2381;&#2357;</span><span style=\"font-family: Cambria Math;\"> - 382 </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2352;&#2381;&#2327;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2350;&#2368;&#2404;</span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "18",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">10</span><span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Cambria Math;\">Bokajan is a cement plant located in </span><span style=\"font-family: Cambria Math;\">which of the following states?</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">10</span><span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2379;&#2325;&#2366;&#2332;&#2344;</span><span style=\"font-family: Cambria Math;\"> (Bokajan) </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2368;&#2350;&#2375;&#2306;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2351;&#2306;&#2340;&#2381;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2352;&#2366;&#2332;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2381;&#2341;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>Bihar</p>\n", "<p>Assam</p>\n", 
                                "<p>Madhya Pradesh</p>\n", "<p>Andhra Pradesh</p>\n"],
                    options_hi: ["<p><span style=\"font-family: Nirmala UI;\">&#2348;&#2367;&#2361;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><span style=\"font-family: Nirmala UI;\">&#2309;&#2360;&#2350;</span></p>\n",
                                "<p><span style=\"font-family: Nirmala UI;\">&#2350;&#2343;&#2381;&#2351;&#2346;&#2381;&#2352;&#2342;&#2375;&#2358;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><span style=\"font-family: Nirmala UI;\">&#2310;&#2306;&#2343;&#2381;&#2352;&#2346;&#2381;&#2352;&#2342;&#2375;&#2358;</span></p>\n"],
                    solution_en: "<p>10<span style=\"font-family: Cambria Math;\">.(b)&nbsp;</span><strong>Assam. </strong><span style=\"font-weight: 400;\">The first cement plant was set up in Chennai in 1904. </span><strong>Cement Corporation of India Limited (CCI)</strong><span style=\"font-weight: 400;\"> : It is the only cement manufacturing Public Sector Undertaking of Government of India. Founded - 1965. Headquarters - New Delhi, India. Its units are spread throughout the country: East (Bokajan in Assam), West (Akaltara, Mandhar in Chhattisgarh and Nayagaon in Madhya Pradesh), North (Rajban in Himachal Pradesh and Charkhi Dadri in Haryana) and South (Kurkunta in Karnataka and Adilabad, Tandur in Telangana), with one cement grinding unit in Delhi.</span></p>\n",
                    solution_hi: "<p>10<span style=\"font-family: Cambria Math;\">.(b)</span><strong>&nbsp;&#2309;&#2360;&#2350;&#2404;</strong><span style=\"font-weight: 400;\"> &#2346;&#2381;&#2352;&#2341;&#2350; &#2360;&#2368;&#2350;&#2375;&#2306;&#2335; &#2360;&#2306;&#2351;&#2306;&#2340;&#2381;&#2352; 1904 &#2350;&#2375;&#2306; &#2330;&#2375;&#2344;&#2381;&#2344;&#2312; &#2350;&#2375;&#2306; &#2360;&#2381;&#2341;&#2366;&#2346;&#2367;&#2340; &#2325;&#2367;&#2351;&#2366; &#2327;&#2351;&#2366; &#2341;&#2366;&#2404;</span><strong> &#2360;&#2368;&#2350;&#2375;&#2306;&#2335; &#2325;&#2377;&#2352;&#2381;&#2346;&#2379;&#2352;&#2375;&#2358;&#2344; &#2321;&#2347; &#2311;&#2306;&#2337;&#2367;&#2351;&#2366; &#2354;&#2367;&#2350;&#2367;&#2335;&#2375;&#2337; (CCI): </strong><span style=\"font-weight: 400;\">&#2351;&#2361; &#2349;&#2366;&#2352;&#2340; &#2360;&#2352;&#2325;&#2366;&#2352; &#2325;&#2366; &#2319;&#2325;&#2350;&#2366;&#2340;&#2381;&#2352; &#2360;&#2368;&#2350;&#2375;&#2306;&#2335; &#2357;&#2367;&#2344;&#2367;&#2352;&#2381;&#2350;&#2366;&#2339; &#2360;&#2366;&#2352;&#2381;&#2357;&#2332;&#2344;&#2367;&#2325; &#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352; &#2325;&#2366; &#2324;&#2342;&#2381;&#2351;&#2379;&#2327;&#2367;&#2325; &#2360;&#2306;&#2360;&#2381;&#2341;&#2366;&#2344; &#2361;&#2376;&#2404; &#2360;&#2381;&#2341;&#2366;&#2346;&#2367;&#2340; - 1965. &#2350;&#2369;&#2326;&#2381;&#2351;&#2366;&#2354;&#2351; - &#2344;&#2312; &#2342;&#2367;&#2354;&#2381;&#2354;&#2368;, &#2349;&#2366;&#2352;&#2340;&#2404; &#2311;&#2360;&#2325;&#2368; &#2311;&#2325;&#2366;&#2311;&#2351;&#2366;&#2305; &#2346;&#2370;&#2352;&#2375; &#2342;&#2375;&#2358; &#2350;&#2375;&#2306; &#2347;&#2376;&#2354;&#2368; &#2361;&#2369;&#2312; &#2361;&#2376;&#2306;: &#2346;&#2370;&#2352;&#2381;&#2357; (&#2309;&#2360;&#2350; &#2350;&#2375;&#2306; &#2348;&#2379;&#2325;&#2366;&#2332;&#2344;), &#2346;&#2358;&#2381;&#2330;&#2367;&#2350; (&#2331;&#2340;&#2381;&#2340;&#2368;&#2360;&#2327;&#2338;&#2364; &#2350;&#2375;&#2306; &#2309;&#2325;&#2354;&#2340;&#2366;&#2352;&#2366;, &#2350;&#2366;&#2306;&#2338;&#2352; &#2324;&#2352; &#2350;&#2343;&#2381;&#2351; &#2346;&#2381;&#2352;&#2342;&#2375;&#2358; &#2350;&#2375;&#2306; &#2344;&#2351;&#2366;&#2327;&#2366;&#2305;&#2357;), &#2313;&#2340;&#2381;&#2340;&#2352; (&#2361;&#2367;&#2350;&#2366;&#2330;&#2354; &#2346;&#2381;&#2352;&#2342;&#2375;&#2358; &#2350;&#2375;&#2306; &#2352;&#2366;&#2332;&#2348;&#2344; &#2324;&#2352; &#2361;&#2352;&#2367;&#2351;&#2366;&#2339;&#2366; &#2350;&#2375;&#2306; &#2330;&#2352;&#2326;&#2368; &#2342;&#2366;&#2342;&#2352;&#2368;) &#2324;&#2352; &#2342;&#2325;&#2381;&#2359;&#2367;&#2339; (&#2325;&#2352;&#2381;&#2344;&#2366;&#2335;&#2325; &#2350;&#2375;&#2306; &#2325;&#2369;&#2352;&#2325;&#2369;&#2306;&#2335;&#2366; &#2324;&#2352; &#2310;&#2342;&#2367;&#2354;&#2366;&#2348;&#2366;&#2342;, &#2340;&#2306;&#2342;&#2370;&#2352; (&#2340;&#2375;&#2354;&#2306;&#2327;&#2366;&#2344;&#2366; &#2350;&#2375;&#2306;), &#2342;&#2367;&#2354;&#2381;&#2354;&#2368; &#2350;&#2375;&#2306; &#2319;&#2325; &#2360;&#2368;&#2350;&#2375;&#2306;&#2335; &#2327;&#2381;&#2352;&#2366;&#2311;&#2306;&#2337;&#2367;&#2306;&#2327; &#2311;&#2325;&#2366;&#2312; &#2325;&#2375; &#2360;&#2366;&#2341;&#2404;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "18",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">11</span><span style=\"font-family: Cambria Math;\">.</span><span style=\"font-family: Cambria Math;\"> What is the name of a type of waterfall with an enormous volume of water?</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">11</span><span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2354;&#2346;&#2381;&#2352;&#2346;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2366;&#2344;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2351;&#2340;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2348;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2343;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>Cascade</p>\n", "<p>Rapid</p>\n", 
                                "<p>Cataract</p>\n", "<p>Chute</p>\n"],
                    options_hi: ["<p><span style=\"font-weight: 400;\">&#2325;&#2376;&#2360;&#2381;&#2325;&#2375;&#2337; (</span><span style=\"font-weight: 400;\">Cascade </span><span style=\"font-weight: 400;\">)</span></p>\n", "<p><span style=\"font-weight: 400;\">&nbsp;&#2352;&#2376;&#2346;&#2367;&#2337; (</span><span style=\"font-weight: 400;\">Rapid </span><span style=\"font-weight: 400;\">)</span></p>\n",
                                "<p><span style=\"font-weight: 400;\">&#2325;&#2376;&#2335;&#2352;&#2375;&#2325;&#2381;&#2335; (</span><span style=\"font-weight: 400;\">Cataract</span><span style=\"font-weight: 400;\">)</span></p>\n", "<p><span style=\"font-weight: 400;\">&#2358;&#2370;&#2335;&#2381;&zwnj; (Chute)</span></p>\n"],
                    solution_en: "<p>11<span style=\"font-family: Cambria Math;\">.(c) </span><strong>Cataract. </strong><span style=\"font-weight: 400;\">Types of Waterfalls: </span><strong>Horsetail</strong><span style=\"font-weight: 400;\"> - Descending water maintains some contact with bedrock. </span><strong>Plunge</strong><span style=\"font-weight: 400;\"> - Water descends vertically, losing contact with the bedrock surface. </span><strong>Block</strong><span style=\"font-weight: 400;\"> - Water descends from a relatively wide stream or river. </span><strong>Multi-step</strong><span style=\"font-weight: 400;\"> - A series of waterfalls one after another of roughly the same size each with its own sunken plunge pool. </span><strong>Segmented</strong><span style=\"font-weight: 400;\"> - Distinctly separate flows of water form as it descends. </span><strong>Cascade</strong><span style=\"font-weight: 400;\"> - Water descends a series of rock steps. </span><strong>Punchbowl </strong><span style=\"font-weight: 400;\">- Water descends in a constricted form and then spreads out in a wider pool.&nbsp;</span></p>\n",
                    solution_hi: "<p>11.(c)&nbsp;<strong>&#2325;&#2376;&#2335;&#2352;&#2375;&#2325;&#2381;&#2335; (Cataract)&#2404; </strong><span style=\"font-weight: 400;\">&#2333;&#2352;&#2344;&#2379;&#2306; &#2325;&#2375; &#2346;&#2381;&#2352;&#2325;&#2366;&#2352;: </span><strong>&#2361;&#2377;&#2352;&#2381;&#2360;&#2335;&#2375;&#2354; </strong><span style=\"font-weight: 400;\">- &#2348;&#2361;&#2340;&#2366; &#2361;&#2369;&#2310; (&#2309;&#2357;&#2352;&#2379;&#2361;&#2368;) &#2332;&#2354;&nbsp; &#2330;&#2335;&#2381;&#2335;&#2366;&#2344; &#2325;&#2368; &#2360;&#2340;&#2361; &#2325;&#2375; &#2360;&#2366;&#2341; &#2360;&#2306;&#2346;&#2352;&#2381;&#2325; &#2348;&#2344;&#2366;&#2319; &#2352;&#2326;&#2340;&#2366; &#2361;&#2376;&#2404; </span><strong>&#2346;&#2381;&#2354;&#2306;&#2332; </strong><span style=\"font-weight: 400;\">- &#2332;&#2354; &#2354;&#2306;&#2348;&#2357;&#2340; &#2352;&#2370;&#2346; &#2360;&#2375; &#2344;&#2368;&#2330;&#2375; &#2327;&#2367;&#2352;&#2340;&#2366; &#2361;&#2376;, &#2332;&#2367;&#2360;&#2360;&#2375; &#2330;&#2335;&#2381;&#2335;&#2366;&#2344; &#2325;&#2368; &#2360;&#2340;&#2361; &#2360;&#2375; &#2360;&#2306;&#2346;&#2352;&#2381;&#2325; &#2335;&#2370;&#2335; &#2332;&#2366;&#2340;&#2366; &#2361;&#2376;&#2404; </span><strong>&#2348;&#2381;&#2354;&#2377;&#2325; </strong><span style=\"font-weight: 400;\">- &#2332;&#2354; &#2309;&#2346;&#2375;&#2325;&#2381;&#2359;&#2366;&#2325;&#2371;&#2340; &#2330;&#2380;&#2337;&#2364;&#2368; &#2343;&#2366;&#2352;&#2366; &#2351;&#2366; &#2344;&#2342;&#2368; &#2360;&#2375; &#2327;&#2367;&#2352;&#2340;&#2366; &#2361;&#2376;&#2404; </span><strong>&#2350;&#2354;&#2381;&#2335;&#2368;-&#2360;&#2381;&#2335;&#2375;&#2346; </strong><span style=\"font-weight: 400;\">- &#2354;&#2327;&#2349;&#2327; &#2319;&#2325; &#2361;&#2368; &#2310;&#2325;&#2366;&#2352; &#2325;&#2375; &#2319;&#2325; &#2325;&#2375; &#2348;&#2366;&#2342; &#2319;&#2325; &#2333;&#2352;&#2344;&#2379;&#2306; &#2325;&#2368; &#2319;&#2325; &#2358;&#2381;&#2352;&#2371;&#2306;&#2326;&#2354;&#2366;, &#2332;&#2367;&#2344;&#2350;&#2375;&#2306; &#2360;&#2375; &#2346;&#2381;&#2352;&#2340;&#2381;&#2351;&#2375;&#2325; &#2325;&#2366; &#2309;&#2346;&#2344;&#2366; &#2337;&#2370;&#2348;&#2366; &#2361;&#2369;&#2310; &#2346;&#2381;&#2354;&#2306;&#2332; &#2346;&#2370;&#2354; &#2361;&#2376;&#2404; </span><strong>&#2360;&#2375;&#2327;&#2350;&#2375;&#2306;&#2335;&#2375;&#2337; </strong><span style=\"font-weight: 400;\">- &#2344;&#2368;&#2330;&#2375; &#2327;&#2367;&#2352;&#2340;&#2375; &#2360;&#2350;&#2351; &#2332;&#2354; &#2325;&#2366; &#2309;&#2354;&#2327;-&#2309;&#2354;&#2327; &#2346;&#2381;&#2352;&#2357;&#2366;&#2361; &#2348;&#2344;&#2340;&#2366; &#2361;&#2376;&#2404; </span><strong>&#2325;&#2376;&#2360;&#2381;&#2325;&#2375;&#2337;</strong><span style=\"font-weight: 400;\">- &#2332;&#2354; &#2330;&#2335;&#2381;&#2335;&#2366;&#2344;&#2368; &#2360;&#2368;&#2338;&#2364;&#2367;&#2351;&#2379;&#2306; &#2325;&#2368; &#2358;&#2371;&#2306;&#2326;&#2354;&#2366; &#2360;&#2375; &#2344;&#2368;&#2330;&#2375; &#2310;&#2340;&#2366; &#2361;&#2376;&#2404; </span><strong>&#2346;&#2306;&#2330; &#2348;&#2366;&#2313;&#2354; -</strong><span style=\"font-weight: 400;\"> &#2332;&#2354; &#2360;&#2306;&#2325;&#2369;&#2330;&#2367;&#2340; &#2352;&#2370;&#2346; &#2350;&#2375;&#2306; &#2327;&#2367;&#2352;&#2340;&#2366; &#2361;&#2376; &#2324;&#2352; &#2347;&#2367;&#2352; &#2319;&#2325; &#2357;&#2367;&#2360;&#2381;&#2340;&#2371;&#2340; &#2325;&#2369;&#2306;&#2337; &#2350;&#2375;&#2306; &#2347;&#2376;&#2354; &#2332;&#2366;&#2340;&#2366; &#2361;&#2376;&#2404;</span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "18",
                    question_en: "<p>12<span style=\"font-family: Cambria Math;\">.</span><span style=\"font-family: Cambria Math;\"> Which of these is NOT true about the green revolution?</span></p>\n",
                    question_hi: "<p>12<span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2352;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2352;&#2366;&#2306;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2348;&#2306;&#2343;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2367;&#2350;&#2381;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2380;&#2344;</span><span style=\"font-family: Cambria Math;\">-</span><span style=\"font-family: Nirmala UI;\">&#2360;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2341;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2361;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2361;&#2368;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>It was funded by international agencies.</p>\n", "<p>It was a private-sector programme for agricultural modernization.</p>\n", 
                                "<p>It was targeted mainly in the wheat and rice-growing areas.</p>\n", "<p>It was introduced only in areas that had assured irrigation.</p>\n"],
                    options_hi: ["<p><span style=\"font-family: Nirmala UI;\">&#2351;&#2361;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2306;&#2340;&#2352;&#2352;&#2366;&#2359;&#2381;&#2335;&#2381;&#2352;&#2368;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2319;&#2332;&#2375;&#2306;&#2360;&#2367;&#2351;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2381;&#2357;&#2366;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2340;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2379;&#2359;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2351;</span><span style=\"font-family: Nirmala UI;\">&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2341;&#2366;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><span style=\"font-family: Nirmala UI;\">&#2351;&#2361;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2371;&#2359;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2343;&#2369;&#2344;&#2367;&#2325;&#2368;&#2325;&#2352;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2367;&#2332;&#2368;</span><span style=\"font-family: Cambria Math;\">-</span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;&#2352;&#2381;&#2351;&#2325;&#2381;&#2352;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2341;&#2366;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                                "<p><span style=\"font-family: Nirmala UI;\">&#2351;&#2361;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2369;&#2326;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2352;&#2370;&#2346;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2375;&#2361;&#2370;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2330;&#2366;&#2357;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2327;&#2366;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2366;&#2354;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2325;&#2381;&#2359;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2341;&#2366;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><span style=\"font-family: Nirmala UI;\">&#2351;&#2361;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;&#2357;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2358;&#2369;&#2352;&#2370;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2341;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2361;&#2366;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2367;&#2306;&#2330;&#2366;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2358;&#2381;&#2357;&#2366;&#2360;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2341;&#2366;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n"],
                    solution_en: "<p>12<span style=\"font-family: Cambria Math;\">.(b) <strong>Green revolution</strong>: </span><span style=\"font-family: Cambria Math;\">Dr. Norman Borlaug - \"Father of the Green Revolution in the world\". Focus Crops: Wheat and Rice. M.S. Swaminathan - \"Father of the Green Revolution in India&rsquo;&rsquo;. Other agricultural Revolutions: White Revolution - Milk production</span><span style=\"font-family: Cambria Math;\">. Red revolution- Meat or tomato production, Pink revolution - poultry, meat, onion, prawns. Blue revolution - Fish production. Black revolution - Petroleum production.</span></p>\n",
                    solution_hi: "<p>12<span style=\"font-family: Cambria Math;\">.(b) </span><strong><span style=\"font-family: Nirmala UI;\">&#2361;&#2352;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2352;&#2366;&#2306;&#2340;&#2367;</span></strong><span style=\"font-family: Cambria Math;\">:</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2337;&#2377;</span><span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2377;&#2352;&#2381;&#2350;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2379;&#2352;&#2354;&#2377;&#2327;</span><span style=\"font-family: Cambria Math;\"> - \"</span><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2358;&#2381;</span><span style=\"font-family: Nirmala UI;\">&#2357;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;</span><span style=\"font-family: Nirmala UI;\">&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2352;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2352;&#2366;&#2306;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2344;&#2325;</span><span style=\"font-family: Cambria Math;\">\"</span><span style=\"font-family: Nirmala UI;\">&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2343;&#2381;&#2351;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;&#2306;&#2342;&#2381;&#2352;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2347;&#2360;&#2354;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\">: </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2375;&#2361;&#2370;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2330;&#2366;&#2357;&#2354;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2319;&#2350;</span><span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Nirmala UI;\">&#2319;&#2360;</span><span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2381;&#2357;&#2366;&#2350;&#2368;&#2344;&#2366;&#2341;&#2344;</span><span style=\"font-family: Cambria Math;\"> - \"</span><span style=\"font-family: Nirmala UI;\">&#2349;&#2366;&#2352;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2352;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2352;&#2366;&#2306;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2344;&#2325;</span><span style=\"font-family: Cambria Math;\">\'\'</span><span style=\"font-family: Nirmala UI;\">&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2344;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2371;&#2359;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2352;&#2366;&#2306;&#2340;&#2367;&#2351;&#2366;&#2305;</span><span style=\"font-family: Cambria Math;\">: </span><span style=\"font-family: Nirmala UI;\">&#2358;&#2381;&#2357;&#2375;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2352;&#2366;&#2306;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\"> - </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2369;&#2327;&#2381;&#2343;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2340;&#2381;&#2346;&#2366;&#2342;&#2344;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2366;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2352;&#2366;&#2306;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\"> - </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2306;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2335;&#2350;&#2366;&#2335;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2340;&#2381;&#2346;&#2366;&#2342;&#2344;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2369;&#2354;&#2366;&#2348;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2352;&#2366;&#2306;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\"> - </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2369;&#2352;&#2381;&#2327;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2306;&#2360;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2368;&#2354;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2352;&#2366;&#2306;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\"> - </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2331;&#2354;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2340;&#2381;&#2346;&#2366;&#2342;&#2344;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;&#2354;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2352;&#2366;&#2306;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\"> - </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2375;&#2335;</span><span style=\"font-family: Nirmala UI;\">&#2381;&#2352;&#2379;&#2354;&#2367;&#2351;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2340;&#2381;&#2346;&#2366;&#2342;&#2344;&#2404;</span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "18",
                    question_en: "<p>13<span style=\"font-family: Cambria Math;\">.</span><span style=\"font-family: Cambria Math;\"> Bailadila range of hills are famous for which of the following minerals? </span></p>\n",
                    question_hi: "<p>13<span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2376;&#2354;&#2366;&#2337;&#2368;&#2354;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2361;&#2366;&#2337;&#2364;&#2367;&#2351;&#2366;&#2306;</span><span style=\"font-family: Cambria Math;\"> (Bailadila range) </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2326;&#2344;&#2367;&#2332;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2360;&#2367;&#2342;&#2381;&#2343;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2306;</span><span style=\"font-family: Cambria Math;\">? </span></p>\n",
                    options_en: ["<p>Iron ore</p>\n", "<p>Zinc</p>\n", 
                                "<p>Bauxite</p>\n", "<p>Manganese</p>\n"],
                    options_hi: ["<p><span style=\"font-family: Nirmala UI;\">&#2354;&#2380;&#2361;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2351;&#2360;&#2381;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><span style=\"font-family: Nirmala UI;\">&#2332;&#2360;&#2381;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                                "<p><span style=\"font-family: Nirmala UI;\">&#2348;&#2377;&#2325;&#2381;&#2360;&#2366;&#2311;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><span style=\"font-family: Nirmala UI;\">&#2350;&#2376;&#2306;&#2327;&#2344;&#2368;&#2332;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n"],
                    solution_en: "<p>13<span style=\"font-family: Cambria Math;\">.(a)&nbsp;</span><strong>Iron Ore: </strong><span style=\"font-weight: 400;\">Haematite and Magnetite are India&rsquo;s two main types of ore. Its mines are close to the coal fields in the northeastern plateau area. Odisha, Goa, Chhattisgarh, Jharkhand, Telangana, Tamil Nadu, Karnataka, and Andhra Pradesh have nearly 95 % of total iron ore reserves. </span><strong>Manganese</strong><span style=\"font-weight: 400;\">: It is found in Madhya Pradesh, Maharashtra, Odisha, Karnataka, Andhra Pradesh etc. </span><strong>Bauxite -</strong><span style=\"font-weight: 400;\"> As of&nbsp; 2023, Odisha is the largest Bauxite producing state. The Himalayan belt is another mineral belt where copper, lead, zinc, cobalt and tungsten are known to occur.</span></p>\n",
                    solution_hi: "<p>13.(a)<strong>&nbsp;&#2354;&#2380;&#2361; &#2309;&#2351;&#2360;&#2381;&#2325;:</strong><span style=\"font-weight: 400;\"> &#2361;&#2375;&#2350;&#2375;&#2335;&#2366;&#2311;&#2335; &#2324;&#2352; &#2350;&#2376;&#2327;&#2381;&#2344;&#2375;&#2335;&#2366;&#2311;&#2335; &#2349;&#2366;&#2352;&#2340; &#2325;&#2375; &#2342;&#2379; &#2350;&#2369;&#2326;&#2381;&#2351; &#2346;&#2381;&#2352;&#2325;&#2366;&#2352; &#2325;&#2375; &#2309;&#2351;&#2360;&#2381;&#2325; &#2361;&#2376;&#2306;&#2404; &#2311;&#2360;&#2325;&#2368; &#2326;&#2342;&#2366;&#2344;&#2375;&#2306; &#2313;&#2340;&#2381;&#2340;&#2352;&#2346;&#2370;&#2352;&#2381;&#2357;&#2368; &#2346;&#2336;&#2366;&#2352;&#2368; &#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352; &#2350;&#2375;&#2306; &#2325;&#2379;&#2351;&#2354;&#2366; &#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;&#2379;&#2306; &#2325;&#2375; &#2325;&#2352;&#2368;&#2348; &#2361;&#2376;&#2306;&#2404; &#2323;&#2337;&#2367;&#2358;&#2366;, &#2327;&#2379;&#2357;&#2366;, &#2331;&#2340;&#2381;&#2340;&#2368;&#2360;&#2327;&#2338;&#2364;, &#2333;&#2366;&#2352;&#2326;&#2306;&#2337;, &#2340;&#2375;&#2354;&#2306;&#2327;&#2366;&#2344;&#2366;, &#2340;&#2350;&#2367;&#2354;&#2344;&#2366;&#2337;&#2369;, &#2325;&#2352;&#2381;&#2344;&#2366;&#2335;&#2325; &#2324;&#2352; &#2310;&#2306;&#2343;&#2381;&#2352; &#2346;&#2381;&#2352;&#2342;&#2375;&#2358; &#2350;&#2375;&#2306; &#2325;&#2369;&#2354; &#2354;&#2380;&#2361; &#2309;&#2351;&#2360;&#2381;&#2325; &#2349;&#2306;&#2337;&#2366;&#2352; &#2325;&#2366; &#2354;&#2327;&#2349;&#2327; 95% &#2361;&#2367;&#2360;&#2381;&#2360;&#2366; &#2361;&#2376;&#2404; </span><strong>&#2350;&#2376;&#2306;&#2327;&#2344;&#2368;&#2332;</strong><span style=\"font-weight: 400;\">: &#2351;&#2361; &#2350;&#2343;&#2381;&#2351; &#2346;&#2381;&#2352;&#2342;&#2375;&#2358;, &#2350;&#2361;&#2366;&#2352;&#2366;&#2359;&#2381;&#2335;&#2381;&#2352;, &#2323;&#2337;&#2367;&#2358;&#2366;, &#2325;&#2352;&#2381;&#2344;&#2366;&#2335;&#2325;, &#2310;&#2306;&#2343;&#2381;&#2352; &#2346;&#2381;&#2352;&#2342;&#2375;&#2358; &#2310;&#2342;&#2367; &#2350;&#2375;&#2306; &#2346;&#2366;&#2351;&#2366; &#2332;&#2366;&#2340;&#2366; &#2361;&#2376;&#2404; </span><strong>&#2348;&#2377;&#2325;&#2381;&#2360;&#2366;&#2311;&#2335; </strong><span style=\"font-weight: 400;\">- 2023 &#2340;&#2325;, &#2323;&#2337;&#2367;&#2358;&#2366; &#2360;&#2348;&#2360;&#2375; &#2348;&#2337;&#2364;&#2366; &#2348;&#2377;&#2325;&#2381;&#2360;&#2366;&#2311;&#2335; &#2313;&#2340;&#2381;&#2346;&#2366;&#2342;&#2325; &#2352;&#2366;&#2332;&#2381;&#2351; &#2361;&#2376;&#2404; &#2361;&#2367;&#2350;&#2366;&#2354;&#2351; &#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352; &#2319;&#2325; &#2309;&#2344;&#2381;&#2351; &#2326;&#2344;&#2367;&#2332; &#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352; &#2361;&#2376; &#2332;&#2361;&#2366;&#2306; &#2340;&#2366;&#2306;&#2348;&#2366;, &#2360;&#2368;&#2360;&#2366;, &#2332;&#2360;&#2381;&#2340;&#2366;, &#2325;&#2379;&#2348;&#2366;&#2354;&#2381;&#2335; &#2324;&#2352; &#2335;&#2306;&#2327;&#2360;&#2381;&#2335;&#2344; &#2346;&#2366;&#2319; &#2332;&#2366;&#2340;&#2375; &#2361;&#2376;&#2306;&#2404;</span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "18",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">14</span><span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Cambria Math;\">According to Census of India 2011, which state in India has th</span><span style=\"font-family: Cambria Math;\">e highest population density?</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">14</span><span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Nirmala UI;\">&#2349;&#2366;&#2352;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2344;&#2327;&#2339;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> 2011 </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2344;&#2369;&#2360;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2349;&#2366;&#2352;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2352;&#2366;&#2332;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2344;&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2328;&#2344;&#2340;&#2381;&#2357;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2352;&#2381;&#2357;&#2366;&#2343;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>Uttar Pradesh</p>\n", "<p>Bihar</p>\n", 
                                "<p>West Bengal</p>\n", "<p>Kerala</p>\n"],
                    options_hi: ["<p><span style=\"font-family: Nirmala UI;\">&#2313;&#2340;&#2381;&#2340;&#2352;&#2346;&#2381;&#2352;&#2342;&#2375;&#2358;</span></p>\n", "<p><span style=\"font-family: Nirmala UI;\">&#2348;&#2367;&#2361;&#2366;&#2352;</span></p>\n",
                                "<p><span style=\"font-family: Nirmala UI;\">&#2346;&#2358;&#2381;&#2330;&#2367;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2306;&#2327;&#2366;&#2354;</span></p>\n", "<p><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;&#2352;&#2354;</span></p>\n"],
                    solution_en: "<p>14<span style=\"font-family: Cambria Math;\">.(b) <strong>Bihar.</strong></span><span style=\"font-family: Cambria Math;\"> The population density of India - 382 per sq km (2011). States: Highest in Bihar (1106 per Sq km) and Lowest in Arunachal Pradesh (17 per Sq km). Union Territory: Highest in Delhi (11320 per Sq km) lowest in Andaman and Nicobar Islands </span><span style=\"font-family: Cambria Math;\">(46 per Sq km). Highest Population (State) - Uttar Pradesh, Lowest - Sikkim. Highest Population (Union Territory) - Delhi, Lowest - Lakshaweep. </span></p>\n",
                    solution_hi: "<p>14.(b) <strong><span style=\"font-family: Nirmala UI;\">&#2348;&#2367;&#2361;&#2366;&#2352;</span><span style=\"font-family: Nirmala UI;\">&#2404;</span></strong><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2349;&#2366;&#2352;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2344;&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2328;&#2344;&#2340;&#2381;&#2357;</span><span style=\"font-family: Cambria Math;\"> - 382 </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2352;&#2381;&#2327;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2350;&#2368;</span><span style=\"font-family: Cambria Math;\"> (2011)</span><span style=\"font-family: Nirmala UI;\">&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2352;&#2366;&#2332;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\">: </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2367;&#2361;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2330;&#2381;&#2330;&#2340;&#2350;</span><span style=\"font-family: Cambria Math;\"> (1106 </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;</span><span style=\"font-family: Nirmala UI;\">&#2352;&#2381;&#2327;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2350;&#2368;</span><span style=\"font-family: Cambria Math;\">) </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2348;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2352;&#2369;&#2339;&#2366;&#2330;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2342;&#2375;&#2358;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> (17 </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2352;&#2381;&#2327;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2350;&#2368;</span><span style=\"font-family: Cambria Math;\">)</span><span style=\"font-family: Nirmala UI;\">&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;&#2306;&#2342;&#2381;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2358;&#2366;&#2360;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2342;&#2375;&#2358;</span><span style=\"font-family: Cambria Math;\">: </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2367;&#2354;&#2381;&#2354;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2348;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2330;&#2381;&#2330;&#2340;&#2350;</span><span style=\"font-family: Cambria Math;\"> (11320 </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2352;&#2381;&#2327;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2350;&#2368;</span><span style=\"font-family: Cambria Math;\">) </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2306;&#2337;&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2367;&#2325;&#2379;&#2348;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2381;&#2357;&#2368;&#2346;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2350;&#2370;&#2361;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2348;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2350;</span><span style=\"font-family: Cambria Math;\"> (46 </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2352;&#2381;&#2327;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2350;&#2368;</span><span style=\"font-family: Cambria Math;\">)</span><span style=\"font-family: Nirmala UI;\">&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2352;&#2381;&#2357;&#2366;&#2343;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2344;&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> (</span><span style=\"font-family: Nirmala UI;\">&#2352;&#2366;&#2332;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\">) - </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2340;&#2381;&#2340;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2342;&#2375;&#2358;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2348;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2350;</span><span style=\"font-family: Cambria Math;\"> - </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2367;&#2325;&#2381;&#2325;&#2367;&#2350;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2352;</span><span style=\"font-family: Nirmala UI;\">&#2381;&#2357;&#2366;&#2343;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2344;&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> (</span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;&#2306;&#2342;&#2381;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2358;&#2366;&#2360;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2342;&#2375;&#2358;</span><span style=\"font-family: Cambria Math;\">) - </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2367;&#2354;&#2381;&#2354;&#2368;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2348;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2350;</span><span style=\"font-family: Cambria Math;\"> - </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2325;&#2381;&#2359;&#2342;&#2381;&#2357;&#2368;&#2346;&#2404;</span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "misc",
                    question_en: "<p>15<span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Cambria Math;\">Which of the following is a type of vegetation found in the Himalayan region?</span></p>\n",
                    question_hi: "<p>15<span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2325;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2344;&#2360;&#2381;&#2346;</span><span style=\"font-family: Nirmala UI;\">&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2367;&#2350;&#2366;&#2354;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2366;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2366;&#2340;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p><span style=\"font-family: Cambria Math;\">Mangrove</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">Pampas</span></p>\n", 
                                "<p><span style=\"font-family: Cambria Math;\">Savannah</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">Alpine </span></p>\n"],
                    options_hi: ["<p><span style=\"font-family: Nirmala UI;\">&#2350;&#2376;&#2306;&#2327;&#2381;&#2352;&#2379;&#2357;</span></p>\n", "<p><span style=\"font-family: Nirmala UI;\">&#2346;&#2306;&#2346;&#2366;&#2360;</span></p>\n",
                                "<p><span style=\"font-family: Nirmala UI;\">&#2360;&#2357;&#2366;&#2344;&#2366;</span></p>\n", "<p><span style=\"font-family: Nirmala UI;\">&#2309;&#2354;&#2381;&#2346;&#2366;&#2311;&#2344;</span></p>\n"],
                    solution_en: "<p>15<span style=\"font-family: Cambria Math;\">.(d)</span><strong>&nbsp;Alpine.</strong><span style=\"font-weight: 400;\"> Himalayan vegetation can be broadly classified into 4 types - Tropical, Subtropical, Temperate, and Alpine. </span><strong>Mangroves</strong><span style=\"font-weight: 400;\"> - A group of trees and shrubs that live in the coastal intertidal zone.</span><strong> Pampas </strong><span style=\"font-weight: 400;\">- The large area of flat, grassy land in South America.</span><strong> Savannah - </strong><span style=\"font-weight: 400;\">An area of Grassland with dispersed trees or clusters of trees.&nbsp;</span></p>\n",
                    solution_hi: "<p>15<span style=\"font-family: Cambria Math;\">.(d)</span><strong>&#2309;&#2354;&#2381;&#2346;&#2366;&#2311;&#2344;</strong><span style=\"font-weight: 400;\">&#2404; &#2361;&#2367;&#2350;&#2366;&#2354;&#2351;&#2368; &#2357;&#2344;&#2360;&#2381;&#2346;&#2340;&#2367; &#2325;&#2379; &#2360;&#2366;&#2350;&#2366;&#2344;&#2381;&#2351;&#2340;&#2307;&nbsp; 4 &#2346;&#2381;&#2352;&#2325;&#2366;&#2352;&#2379;&#2306; &#2350;&#2375;&#2306; &#2357;&#2352;&#2381;&#2327;&#2368;&#2325;&#2371;&#2340; &#2325;&#2367;&#2351;&#2366; &#2332;&#2366; &#2360;&#2325;&#2340;&#2366; &#2361;&#2376; - &#2313;&#2359;&#2381;&#2339;&#2325;&#2335;&#2367;&#2348;&#2306;&#2343;&#2368;&#2351;, &#2313;&#2346;&#2379;&#2359;&#2381;&#2339;&#2325;&#2335;&#2367;&#2348;&#2306;&#2343;&#2368;&#2351;, &#2358;&#2368;&#2340;&#2379;&#2359;&#2381;&#2339; &#2324;&#2352; &#2309;&#2354;&#2381;&#2346;&#2366;&#2311;&#2344;&#2404; </span><strong>&#2350;&#2376;&#2306;&#2327;&#2381;&#2352;&#2379;&#2357; </strong><span style=\"font-weight: 400;\">- &#2346;&#2375;&#2337;&#2364;&#2379;&#2306; &#2324;&#2352; &#2333;&#2366;&#2337;&#2364;&#2367;&#2351;&#2379;&#2306; &#2325;&#2366; &#2319;&#2325; &#2360;&#2350;&#2370;&#2361; &#2332;&#2379; &#2340;&#2335;&#2368;&#2351; &#2309;&#2306;&#2340;&#2352;&#2381;&#2332;&#2381;&#2357;&#2366;&#2352;&#2368;&#2351; &#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352; &#2350;&#2375;&#2306; &#2346;&#2366;&#2319; &#2332;&#2366;&#2340;&#2375; &#2361;&#2376;&#2306;&#2404; </span><strong>&#2346;&#2350;&#2381;&#2346;&#2366;&#2360; </strong><span style=\"font-weight: 400;\">- &#2342;&#2325;&#2381;&#2359;&#2367;&#2339; &#2309;&#2350;&#2375;&#2352;&#2367;&#2325;&#2366; &#2350;&#2375;&#2306; &#2360;&#2350;&#2340;&#2354;, &#2328;&#2366;&#2360;&#2351;&#2369;&#2325;&#2381;&#2340; &#2349;&#2370;&#2350;&#2367; &#2325;&#2366; &#2348;&#2337;&#2364;&#2366; &#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;&#2404; </span><strong>&#2360;&#2357;&#2366;&#2344;&#2366; </strong><span style=\"font-weight: 400;\">- &#2328;&#2366;&#2360; &#2325;&#2375; &#2350;&#2376;&#2342;&#2366;&#2344; &#2325;&#2366; &#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352; &#2332;&#2361;&#2366;&#2305; &#2348;&#2367;&#2326;&#2352;&#2375; &#2361;&#2369;&#2319; &#2357;&#2371;&#2325;&#2381;&#2359;&#2379;&#2306; &#2351;&#2366; &#2357;&#2371;&#2325;&#2381;&#2359;&#2379;&#2306; &#2325;&#2375; &#2360;&#2350;&#2370;&#2361; &#2361;&#2379;&#2306;&#2404;</span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>