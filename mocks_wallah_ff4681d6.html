<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">8:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 15</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">15</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 8 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["18"] = {
                name: "General Awareness",
                start: 0,
                end: 13
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="18">General Awareness</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 14,
                end: 14
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "18",
                    question_en: "<p>1. What protects the earth from the ultraviolet radiation of the sun?</p>",
                    question_hi: "<p>1. सूर्य के पराबैंगनी विकिरण से पृथ्वी की रक्षा कौन करता है?</p>",
                    options_en: ["<p>Nitrogen</p>", "<p>Ozone</p>", 
                                "<p>Oxygen</p>", "<p>Magnesium</p>"],
                    options_hi: ["<p>नाइट्रोजन</p>", "<p>ओजोन</p>",
                                "<p>ऑक्सीजन</p>", "<p>मैगनीशियम</p>"],
                    solution_en: "<p>1.(b) <strong>Ozone (O</strong><sub>3</sub><strong>)</strong>: Ozone layer (Stratosphere). <strong>Ozone Depleting Substances</strong> - Chlorofluorocarbons (CFCs), Hydrochlorofluorocarbons (HCFCs), Carbon tetrachloride, Methyl chloroform, Hydrobromofluorocarbons, Halons etc. <strong>The Montreal Protocol</strong>: Signed - 1987. Effective - 1989. <strong>Aim</strong> - To reduce the production and consumption of these gasses in order to protect the ozone layer.</p>",
                    solution_hi: "<p>1.(b) <strong>ओजोन</strong> (O<sub>3</sub>) - ओजोन परत (समताप मंडल)। <strong>ओजोन क्षयकारी पदार्थ</strong> - क्लोरोफ्लोरोकार्बन (CFC), हाइड्रोक्लोरोफ्लोरोकार्बन (HCFC), कार्बन टेट्राक्लोराइड, मिथाइल क्लोरोफॉर्म, हाइड्रोब्रोमोफ्लोरोकार्बन, हैलोन्स आदि।<strong> मॉन्ट्रियल प्रोटोकॉल</strong> पर 1987 में हस्ताक्षर किए गए और 1989 में प्रभावी हुआ। <strong>उद्देश्य</strong> - ओजोन परत की रक्षा के लिए इन गैसों के उत्पादन और खपत को कम करना।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "2",
                    section: "18",
                    question_en: "<p>2. Which of the following is <strong>NOT</strong> an environmental concern?</p>",
                    question_hi: "<p>2. निम्नलिखित में से कौन एक पर्यावरणीय सरोकार नहीं है?</p>",
                    options_en: ["<p>Increasing consumption and use of plastics</p>", "<p>Raising ozone layer</p>", 
                                "<p>Global warming</p>", "<p>Growing pollution levels</p>"],
                    options_hi: ["<p>प्लास्टिक की बढ़ती खपत और उपयोग</p>", "<p>ओजोन परत का ऊपर उठना</p>",
                                "<p>भूमंडलीय ऊष्मीकरण</p>", "<p>बढ़ रहा प्रदूषण स्तर</p>"],
                    solution_en: "<p>2.(b) <strong>Raising ozone layer. Ozone layer depletion </strong>- When chlorine and bromine atoms come into contact with ozone in the stratosphere then destroy ozone molecules which are damaging to human health by increasing skin cancers, eye cataracts and immune deficiency disorders due to ultraviolet rays. <strong>Environmental concerns</strong> include pollution, overpopulation, waste disposal, climate change, global warming (long-term heating of Earth\'s surface), the greenhouse effect, etc.</p>",
                    solution_hi: "<p>2.(b) <strong>ओजोन परत का ऊपर उठना।</strong> <strong>ओजोन परत का क्षरण</strong> - जब क्लोरीन और ब्रोमीन परमाणु समताप मंडल में ओजोन के संपर्क में आते हैं तो ओजोन अणुओं को नष्ट कर देता हैं जो पराबैंगनी किरणों के कारण त्वचा कैंसर, आंखों के मोतियाबिंद और प्रतिरक्षा की कमी संबंधी विकारों को बढ़ाकर मानव स्वास्थ्य के लिए हानिकारक होते हैं। <strong>पर्यावरणीय चिंताओं</strong> में प्रदूषण, अधिक जनसंख्या, अपशिष्ट निपटान, जलवायु परिवर्तन, ग्लोबल वार्मिंग (पृथ्वी की सतह का लंबे समय तक गर्म होना), ग्रीनहाउस प्रभाव आदि शामिल हैं।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "3",
                    section: "18",
                    question_en: "<p>3. The &lsquo;ozone hole&rsquo; is formed every year in Spring time over ______.</p>",
                    question_hi: "<p>3. \'ओजोन छिद्र\' हर साल बसंत के समय ___________में बनता है |</p>",
                    options_en: ["<p>China</p>", "<p>Antarctica</p>", 
                                "<p>South America</p>", "<p>Japan</p>"],
                    options_hi: ["<p>चीन</p>", "<p>अंटार्कटिका</p>",
                                "<p>दक्षिण अमेरिका</p>", "<p>जापान</p>"],
                    solution_en: "<p>3.(b) <strong>Antarctica.</strong> The term \'ozone hole\' refers to the depletion of the protective ozone layer in the upper atmosphere (stratosphere) over Earth\'s polar regions. The primary chemicals involved in Ozone Depletion - chlorofluorocarbons (CFC), halons, and carbon tetrachloride .</p>",
                    solution_hi: "<p>3.(b) <strong>अंटार्कटिका।</strong> \'ओजोन छिद्र\' शब्द का तात्पर्य पृथ्वी के ध्रुवीय क्षेत्रों के ऊपरी वायुमंडल (समतापमंडल) में सुरक्षात्मक ओजोन परत के ह्रास से है। ओजोन ह्रास में शामिल प्राथमिक रसायन - क्लोरोफ्लोरोकार्बन (CFC), हैलोन और कार्बन टेट्राक्लोराइड।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "4",
                    section: "18",
                    question_en: "<p>4. Which of the following is <strong>NOT</strong> an environmental concern?</p>",
                    question_hi: "<p>4. निम्नलिखित में से कौन एक पर्यावरणीय सरोकार नहीं है?</p>",
                    options_en: ["<p>Global warming</p>", "<p>Increasing consumption and use of plastics</p>", 
                                "<p>Growing pollution levels</p>", "<p>Raising ozone layer</p>"],
                    options_hi: ["<p>भूमंडलीय ऊष्मीकरण</p>", "<p>प्लास्टिक की बढ़ती खपत और उपयोग</p>",
                                "<p>बढ़ता प्रदूषण का स्तर</p>", "<p>ओजोन परत का ऊपर उठना</p>"],
                    solution_en: "<p>4.(d) <strong>Raising ozone layer.</strong> Ozone depletion is a major environmental problem because it increases the amount of ultraviolet (UV) radiation that reaches Earth\'s surface and causes skin cancer, eye cataracts, genetic and immune system damage. <strong>Global warming</strong>: Gradual increase in the earth\'s temperature due to the greenhouse effect caused by increased levels of carbon dioxide, CFCs and other pollutants, Which causes changes in climate patterns across the globe.</p>",
                    solution_hi: "<p>4.(d) <strong>ओजोन परत का ऊपर उठना।</strong> ओजोन ह्रास एक प्रमुख पर्यावरणीय संकटहै क्योंकि यह पराबैंगनी (UV) विकिरण की मात्रा को बढ़ाता है जो पृथ्वी की सतह तक पहुंचता है और त्वचा कैंसर, आंख मोतियाबिंद, आनुवंशिक और प्रतिरक्षा प्रणाली को नुकसान पहुंचाता है। <strong>ग्लोबल वार्मिंग</strong>: कार्बन डाइऑक्साइड, CFC और अन्य प्रदूषकों के बढ़ते स्तर के कारण, ग्रीनहाउस प्रभाव के कारण पृथ्वी के तापमान में धीरे-धीरे वृद्धि हो रही है, जो दुनिया भर में जलवायु पैटर्न में परिवर्तन का कारण बनती जा रही है।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "5",
                    section: "18",
                    question_en: "<p>5. Which of the following mainly causes the depletion of the ozone layer?</p>",
                    question_hi: "<p>5. निम्नलिखित में से कौन मुख्य रूप से ओजोन परत के ह्रास का कारण बनता है?</p>",
                    options_en: ["<p>Chlorofluorocarbons</p>", "<p>Aviation fuels</p>", 
                                "<p>Radioactive rays</p>", "<p>Volcanic eruptions</p>"],
                    options_hi: ["<p>क्लोरोफ्लोरोकार्बन</p>", "<p>विमानन ईंधन</p>",
                                "<p>रेडियोधर्मी किरणें</p>", "<p>ज्वालामुखी विस्फोट</p>"],
                    solution_en: "<p>5.(a) <strong>Chlorofluorocarbons (CFCs)</strong> - A group of inert, nontoxic, nonflammable, and cheaply manufactured liquid compounds. They are solvents and aerosol propellants in refrigeration, air conditioning, packaging, and insulation (medical and other devices). Ozone-depleting substances - Carbon Tetrachloride (CCl4), Hydrochlorofluorocarbons, and Methyl chloroform (C2H3Cl3), Halons (CF2ClBr), Methyl bromide (CH3Br), and Hydro Bromofluorocarbons. The Montreal Protocol was proposed in 1987 to prohibit the use, production, and import of ozone-depleting substances.</p>",
                    solution_hi: "<p>5. (a)<strong> क्लोरोफ्लोरोकार्बन (CFC)</strong> - अक्रिय, गैर विषैले, गैर ज्वलनशील और सस्ते में निर्मित तरल यौगिकों का एक समूह है। वे प्रशीतन (refrigeration), एयर कंडीशनिंग, पैकेजिंग और इन्सुलेशन (चिकित्सा और अन्य उपकरणों) में सॉल्वैंट्स और एयरोसोल प्रणोदक हैं। ओजोन को नष्ट करने वाले पदार्थ - कार्बन टेट्राक्लोराइड (CCl4), हाइड्रोक्लोरोफ्लोरोकार्बन, और मिथाइल क्लोरोफॉर्म (C2H3Cl3), हैलोन्स (CF2ClBr), मिथाइल ब्रोमाइड (CH3Br), और हाइड्रो ब्रोमोफ्लोरोकार्बन। ओजोन - क्षयकारी पदार्थों के उपयोग, उत्पादन और आयात पर रोक लगाने के लिए 1987 में मॉन्ट्रियल प्रोटोकॉल प्रस्तावित किया गया था।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "6",
                    section: "18",
                    question_en: "<p>6. Which of the following statements is correct?</p>",
                    question_hi: "<p>6. निम्नलिखित कथनों में से कौन सही है</p>",
                    options_en: ["<p>The main cause of ozone depletion and the ozone hole is chlorofluorocarbons (CFCs).</p>", "<p>Oxygen and ozone are the two main components of acid rain.</p>", 
                                "<p>DDT is a bio-control agent.</p>", "<p><math display=\"inline\"><msub><mrow><mi>C</mi><mi>O</mi></mrow><mrow><mn>2</mn></mrow></msub><mi>&#160;</mi><mi>i</mi><mi>s</mi><mi>&#160;</mi><mi>n</mi><mi>o</mi><mi>t</mi></math> responsible for the green - house effect</p>"],
                    options_hi: ["<p>ओजोन रिक्तीकरण और ओजोन छिद्र का मुख्य कारण क्लोरोफ्लोरोकार्बन है।</p>", "<p>ऑक्सीजन और ओजोन अम्लीय वर्षा के दो मुख्य घटक हैं।</p>",
                                "<p>DDT एक जैव नियंत्रण अभिकारक है।</p>", "<p><math display=\"inline\"><msub><mrow><mi>C</mi><mi>O</mi></mrow><mrow><mn>2</mn></mrow></msub></math>हरित गृह प्रभाव के लिए जिम्मेदार नहीं है।</p>"],
                    solution_en: "<p>6.(a) Ozone depletion refers to the gradual thinning or reduction in the concentration of ozone (O3) molecules in the Earth\'s stratosphere. <strong>Acid rain\'s primary</strong> components are sulfur dioxide (SO2) and nitrogen oxides (NOx). <strong>Dichloro Diphenyl Trichloroethane</strong> (DDT), is a synthetic pesticide used in controlling various insect pests. <strong>Greenhouse effect</strong> - Main gas responsible is carbon dioxide (CO2). Other greenhouse gases include methane (CH4), nitrous oxide (N2O), and various fluorinated gases.</p>",
                    solution_hi: "<p>6. (a) ओजोन रिक्तीकरण से तात्पर्य पृथ्वी के समताप मंडल में ओजोन (O3) अणुओं की सांद्रता में धीरे-धीरे कमी से है। <strong>अम्लीय वर्षा के प्राथमिक</strong> घटक सल्फर डाइऑक्साइड (SO2) और नाइट्रोजन ऑक्साइड (NOx) हैं। <strong>डाइक्लोरो डिफेनिल ट्राइक्लोरोइथेन </strong>(DDT), एक सिंथेटिक कीटनाशक है जिसका उपयोग विभिन्न कीटों को नियंत्रित करने में किया जाता है। <strong>ग्रीनहाउस प्रभाव</strong> - मुख्य जिम्मेदार गैस कार्बन डाइऑक्साइड (CO2) है। अन्य ग्रीनहाउस गैसों में मीथेन (CH4), नाइट्रस ऑक्साइड (N2O), और विभिन्न फ्लोरिनेटेड गैसें शामिल हैं।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "7",
                    section: "18",
                    question_en: "<p>7.Ozone layer is responsible for preventing harmful UV rays to enter the earth&rsquo;s atmosphere. Which of the hydrocarbons is/are not responsible for depletion of the ozone layer?</p>",
                    question_hi: "<p>7. ओजोन परत हानिकारक UV किरणों को पृथ्वी के वायुमंडल में प्रवेश करने से रोकने के लिए जिम्मेदार है। ओजोन परत के ह्रास के लिए कौन-सा हाइड्रोकार्बन जिम्मेदार है/ नहीं हैं?</p>",
                    options_en: ["<p>Asphalt</p>", "<p>propellants</p>", 
                                "<p>Refrigerants</p>", "<p>Foam-blowing agents</p>"],
                    options_hi: ["<p>डामर</p>", "<p>प्रणोदक</p>",
                                "<p>रेफ्रिजरेंट</p>", "<p>फोम उड़ाने वाले एजेंट</p>"],
                    solution_en: "<p>7.(a) <strong>Asphalt. Ozone (O<sub>3</sub>)</strong> protects earth from UV radiation in the form of Ozone layer (naturally occurs in the stratosphere). Chemical compounds that cause ozone layer depletion are called <strong>Ozone Depleting Substances (ODSs)</strong>. (Examples - Chlorofluorocarbons (CFCs), Hydrochlorofluorocarbons (HCFCs), Carbon tetrachloride, Methyl chloroform, Halons).</p>",
                    solution_hi: "<p>7.(a) <strong>डामर। ओजोन (O<sub>3</sub>)</strong> ओजोन परत (प्राकृतिक रूप से समताप मंडल में होता है) के रूप में पृथ्वी को पराबैंगनी विकिरणों से बचाता है। रासायनिक यौगिक जो ओजोन परत के क्षय का कारण बनते हैं, उन्हें <strong>ओजोन क्षयकारी पदार्थ (ODS)</strong> कहा जाता है। (उदाहरण - क्लोरोफ्लोरोकार्बन (CFC), हाइड्रोक्लोरोफ्लोरोकार्बन (HCFC), कार्बन टेट्राक्लोराइड, मिथाइल क्लोरोफॉर्म, हेलोन)।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "8",
                    section: "18",
                    question_en: "<p>8. Australia has one of the highest skin cancer incidence and mortality rates in the world. It is caused mainly due to the exposure to UV rays. Which of the following UV rays are responsible for this?</p>",
                    question_hi: "<p>8. ऑस्ट्रेलिया दुनिया में सबसे अधिक त्वचा कैंसर की घटनाओं और मृत्यु दर वाले देशो में से एक है। यह मुख्य रूप से UV किरणों के संपर्क में आने के कारण होता है। निम्नलिखित में से कौन सी UV किरणें इसके लिए जिम्मेदार हैं?</p>",
                    options_en: ["<p>UV-A</p>", "<p>UV-B</p>", 
                                "<p>Both UV-A &amp; UV-B</p>", "<p>UV rays is not responsible</p>"],
                    options_hi: ["<p>UV-A</p>", "<p>UV-B</p>",
                                "<p>UV-A और UV-B दोनों</p>", "<p>UV किरणें जिम्मेदार नहीं हैं</p>"],
                    solution_en: "<p>8.(c) <strong>Both UV-A &amp; UV-B.</strong> UV-A rays penetrate deeper into the skin, while UV-B rays are more likely to cause sunburn. Both types of UV rays can damage DNA, which can lead to skin cancer.<strong> Australia</strong> has one of the highest rates of skin cancer in the world. This is due to a number of factors, including the country\'s location near the <strong>equator</strong>, the <strong>high levels of UV</strong> radiation, and the <strong>fair skin</strong> of many Australians.</p>",
                    solution_hi: "<p>8.(c) <strong>UV-A &amp; UV-B दोनों</strong> । UV-A किरणें त्वचा में गहराई तक प्रवेश करती हैं, जबकि UV-B किरणों से धूप से झुलसने(sunburn) की संभावना अधिक होती है। दोनों प्रकार की UV किरणें DNA को नुकसान पहुंचा सकती हैं, जिससे त्वचा कैंसर हो सकता है। <strong>ऑस्ट्रेलिया</strong> में त्वचा कैंसर की दर दुनिया में सबसे अधिक है। यह कई कारकों के कारण होता है, जिनमें <strong>भूमध्य रेखा</strong> के निकट देश की स्थिति, <strong>UV विकिरण का उच्च स्तर</strong> और कई ऑस्ट्रेलियाई लोगों की <strong>गोरी त्वचा</strong> शामिल है।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "9",
                    section: "18",
                    question_en: "<p>9. What is the ozone layer composed of?</p>",
                    question_hi: "<p>9. ओजोन परत किससे बनी है?</p>",
                    options_en: ["<p>Tri-oxygen</p>", "<p>Di-oxygen</p>", 
                                "<p>Tetra-oxygen</p>", "<p>Mono-oxygen</p>"],
                    options_hi: ["<p>ट्राई-ऑक्सीजन</p>", "<p>डाई -ऑक्सीजन</p>",
                                "<p>टेट्रा -ऑक्सीजन</p>", "<p>मोनो-ऑक्सीजन</p>"],
                    solution_en: "<p>9.(a)<strong> Tri-Oxygen.</strong> Ozone is a gas that is made up of three oxygen atoms. Ozone in the stratosphere is a product of UV radiations acting on dioxygen (O<sub>2</sub>) molecules. The UV radiations split apart molecular oxygen into free oxygen (O) atoms.These oxygen atoms combine with the molecular oxygen to form ozone.</p>",
                    solution_hi: "<p>9.(a) <strong>ट्राई-ऑक्सीजन।</strong> ओजोन एक गैस है जो तीन ऑक्सीजन परमाणुओं से बनी होती है। समताप मंडल में ओजोन डाइऑक्सीजन (O<sub>2)</sub> अणुओं पर कार्य करने वाले UV विकिरण का एक उत्पाद है। UV विकिरण आणविक ऑक्सीजन को मुक्त ऑक्सीजन (O) परमाणुओं में विभाजित कर देता है। ये ऑक्सीजन परमाणु आणविक ऑक्सीजन के साथ मिलकर ओजोन बनाते हैं।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "10",
                    section: "18",
                    question_en: "<p>10. The largest holes in the ozone layer have been observed in 2020 over which of the following continents?</p>",
                    question_hi: "<p>10. 2020 में ओजोन परत में सबसे बड़ा छिद्र निम्नलिखित में से किस महाद्वीप में देखा गया है?</p>",
                    options_en: ["<p>Europe</p>", "<p>Antarctica</p>", 
                                "<p>North America</p>", "<p>Asia</p>"],
                    options_hi: ["<p>यूरोप</p>", "<p>अंटार्कटिका</p>",
                                "<p>उत्तरी अमेरिका</p>", "<p>एशिया</p>"],
                    solution_en: "<p>10.(b) <strong>Antarctica.</strong> It was the longest-lasting and one of the largest and deepest holes since the ozone layer monitoring began 40 years ago. The <strong>ozone layer</strong> is a thin part of Earth\'s atmosphere that absorbs almost all of the sun\'s harmful ultraviolet light. <strong>Chlorofluorocarbons or CFCs</strong> are the main cause of ozone layer depletion.</p>",
                    solution_hi: "<p>10.(b) <strong>अंटार्कटिका।</strong> 40 साल पहले ओजोन परत की निगरानी शुरू होने के बाद से यह सबसे लंबे समय तक चलने वाला और सबसे बड़े और गहरे छिद्रों में से एक था।<strong> ओजोन परत</strong> पृथ्वी के वायुमंडल का एक पतला हिस्सा है जो सूर्य की लगभग सभी हानिकारक पराबैंगनी प्रकाश को अवशोषित करती है। <strong>क्लोरोफ्लोरोकार्बन या CFC</strong> ओजोन परत के क्षरण का मुख्य कारण हैं।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "11",
                    section: "18",
                    question_en: "<p>11. Which of the following greenhouse gases is NOT included under the Kyoto Protocol?</p>",
                    question_hi: "<p>11. निम्नलिखित में से कौन सी ग्रीनहाउस गैस क्योटो प्रोटोकॉल के तहत शामिल नहीं है?</p>",
                    options_en: ["<p>Methane</p>", "<p>Carbon dioxide</p>", 
                                "<p>Nitrous oxide</p>", "<p>Ozone</p>"],
                    options_hi: ["<p>मीथेन</p>", "<p>कार्बन डाइऑक्साइड</p>",
                                "<p>नाइट्रस ऑक्साइड</p>", "<p>ओजोन</p>"],
                    solution_en: "<p>11.(d) <strong>Ozone. The Montreal Protocol</strong> on Substances that Deplete the Ozone Layer, was done in 1987. <strong>The Kyoto Protocol </strong>covers six categories of greenhouse gas (GHG) emissions &ndash; carbon dioxide (CO2), methane (CH4), nitrous oxide (NO), hydrofluorocarbons (HFCs), perfluorocarbons (PFCs) and sulfur hexafluoride (SF6). The Kyoto Protocol was adopted on<strong> 11 December 1997</strong>. Due to a complicated ratification process, it came into force on<strong> 16 February 2005</strong>.</p>",
                    solution_hi: "<p>11.(d) <strong>ओजोन। ओजोन परत</strong> को नष्ट करने वाले पदार्थों पर मॉन्ट्रियल प्रोटोकॉल 1987 में किया गया था। <strong>क्योटो प्रोटोकॉल</strong> में ग्रीनहाउस गैस (GHG) उत्सर्जन की छह श्रेणियां शामिल हैं - कार्बन डाइऑक्साइड (CO2), मीथेन (CH4), नाइट्रस ऑक्साइड (NO), हाइड्रोफ्लोरोकार्बन (HFC), पेरफ्लोरोकार्बन (PFC) और सल्फर हेक्साफ्लोराइड (SF6)। क्योटो प्रोटोकॉल<strong> 11 दिसंबर 1997</strong> को अपनाया गया था। एक जटिल अनुसमर्थन प्रक्रिया के कारण, यह <strong>16 फरवरी 2005</strong> को लागू हुआ था।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "12",
                    section: "18",
                    question_en: "<p>12. What was the main objective of the agreement made by UNEP in 1987?</p>",
                    question_hi: "<p>12. 1987 में UNEP द्वारा किए गए समझौते का मुख्य उद्देश्य क्या था?</p>",
                    options_en: ["<p>Preventing water pollution</p>", "<p>Use of more CFCs in fire extinguishers</p>", 
                                "<p>stop the emission of greenhouse gases</p>", "<p>Stabilizing the production of CFCs at the level of 1986</p>"],
                    options_hi: ["<p>जल प्रदूषण को रोकना</p>", "<p>अग्निशामक यंत्रों में अधिक CFCs का उपयोग</p>",
                                "<p>ग्रीनहाउस गैसों के उत्सर्जन को रोकना</p>", "<p>CFCs के उत्पादन को 1986 के स्तर पर स्थिर करना</p>"],
                    solution_en: "<p>12.(d) <strong>Ozone</strong> is made up of three atoms of oxygen combined together by the action of ultraviolet (UV) radiation on oxygen gas. The ozone layer protects us from the harmful effects of UV radiation.</p>",
                    solution_hi: "<p>12.(d) <strong>ओजोन</strong> ऑक्सीजन गैस पर पराबैंगनी (UV) विकिरण की क्रिया द्वारा एक साथ संयुक्त ऑक्सीजन के तीन परमाणुओं से बना है। ओजोन परत हमें UV विकिरण के हानिकारक प्रभावों से रक्षा करती है।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "13",
                    section: "18",
                    question_en: "<p>13. The manufacturing of refrigerators that do not release chlorofluorocarbons has been made mandatory throughout the world. How will this help to prevent ozone depletion?</p>",
                    question_hi: "<p>13. क्लोरोफ्लोरोकार्बन निर्गमित न करने वाले रेफ्रिजरेटरों का निर्माण संपूर्ण विश्व में अनिवार्य कर दिया गया है। यह ओजोन के क्षरण को रोकने में किस प्रकार सहायक होगा?</p>",
                    options_en: ["<p>It will reduce the release of CFCs that react with ozone molecules.</p>", "<p>It will help convert oxygen molecules into ozone.</p>", 
                                "<p>CFCs will change into ozone molecules.</p>", "<p>It will reduce the production of CFCs from oxygen molecules.</p>"],
                    options_hi: ["<p>यह ओजोन अणुओं के साथ अभिक्रिया करने वाले CFC के निर्गमन को कम करेगा।</p>", "<p>यह ऑक्सीजन के अणुओं को ओजोन में बदलने में सहायता करेगा।</p>",
                                "<p>CFC ओजोन अणुओं में बदल जाएंगे।</p>", "<p>यह ऑक्सीजन अणुओं से CFC के उत्पादन को कम करेगा।</p>"],
                    solution_en: "<p>13.(a) <strong>Chlorofluorocarbons (CFCs)</strong> - They break up only through sunlight, which divides their molecules, causing the release of chlorine (Cl). Once the chlorine is released, it is able to react with ozone (O3), to form chlorine monoxide (ClO) and oxygen (O2) i.e. Cl + O3 = ClO + O2. In 1987, the United Nations Environment Programme (UNEP) succeeded in forging an agreement to freeze CFC production at 1986 levels. Ozone (O3) is a molecule, and is mostly found in the stratosphere, where it protects us from the Sun&rsquo;s harmful ultraviolet (UV) radiation.</p>",
                    solution_hi: "<p>13.(a) <strong>क्लोरोफ्लोरोकार्बन (CFCs)</strong> - ये केवल सूर्य के प्रकाश के माध्यम से टूटते हैं, जो उनके अणुओं को विभाजित करता है, जिससे क्लोरीन (Cl) निकलता है। एक बार जब क्लोरीन मुक्त हो जाती है, तो यह ओजोन (O3) के साथ अभिक्रिया कर क्लोरीन मोनोऑक्साइड (ClO) और ऑक्सीजन (O2) बनाती है, अर्थात Cl + O3 = ClO + O2 । 1987 में, संयुक्त राष्ट्र पर्यावरण कार्यक्रम (UNEP) 1986 के स्तर पर CFC उत्पादन को स्थिर करने के लिए एक समझौता करने में सफल रहा। ओजोन (O3) एक अणु है, और ज्यादातर समताप मंडल में पाया जाता है, जहां यह हमें सूर्य के हानिकारक पराबैंगनी (UV) विकिरण से बचाता है।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "14",
                    section: "18",
                    question_en: "<p>14. What is the product of UV radiations acting on oxygen molecules at higher levels of the atmosphere?</p>",
                    question_hi: "<p>14. वायुमंडल के उच्चतर स्तरों पर ऑक्सीजन के अणुओं पर UV विकिरणों की क्रिया का उत्पाद क्या होता है?</p>",
                    options_en: ["<p>Ozone</p>", "<p>Carbon dioxide</p>", 
                                "<p>Chlorofluorocarbons</p>", "<p>Water</p>"],
                    options_hi: ["<p>ओजोन (ozone)</p>", "<p>कार्बन डाइऑक्साइड (carbon dioxide)</p>",
                                "<p>क्लोरोफ्लोरोकार्बन (chlorofluorocarbons)</p>", "<p>जल (water)</p>"],
                    solution_en: "<p>14.(a) <strong>Ozone</strong> (O<sub>3</sub>) is created primarily by ultraviolet radiation in the stratosphere. When high-energy ultraviolet rays strike ordinary oxygen molecules (O<sub>2</sub>), they split the molecule into two single oxygen atoms, known as atomic oxygen. A freed oxygen atom combines with another oxygen molecule to form a molecule of ozone.</p>",
                    solution_hi: "<p>14.(a) <strong>ओजोन</strong> (O<sub>3</sub>) मुख्य रूप से समताप मंडल में पराबैंगनी विकिरण द्वारा निर्मित होता है। जब उच्च-ऊर्जा वाली पराबैंगनी किरणें सामान्य ऑक्सीजन अणुओं (O<sub>2</sub>) से टकराती हैं, तो वे अणु को दो एकल ऑक्सीजन परमाणुओं में विभाजित कर देती हैं, जिन्हें परमाण्वीय ऑक्सीजन कहा जाता है। एक मुक्त ऑक्सीजन परमाणु, एक अन्य ऑक्सीजन अणु के साथ मिलकर ओजोन का एक अणु बनाता है।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "15",
                    section: "misc",
                    question_en: "<p>15. When did the ozone levels in the atmosphere start to reduce?</p>",
                    question_hi: "<p>15. वायुमंडल में ओजोन का स्तर कम होना कब शुरू हुआ ?</p>",
                    options_en: ["<p>1995</p>", "<p>1965</p>", 
                                "<p>1980</p>", "<p>1990</p>"],
                    options_hi: ["<p>1995</p>", "<p>1965</p>",
                                "<p>1980</p>", "<p>1990</p>"],
                    solution_en: "<p>15.(c) <strong>1980</strong> . <strong>Ozone layer</strong> - a region in the earth&rsquo;s stratosphere that contains high concentrations of ozone (O3) and protects the earth from the harmful ultraviolet radiations of the sun. Ozone layer depletion - gradual thinning of the earth&rsquo;s ozone layer in the upper atmosphere caused due to the release of chemical compounds. September 16 - International Day for the Preservation of the Ozone Layer.</p>",
                    solution_hi: "<p>15.(c)<strong> 1980</strong> । <strong>ओजोन परत</strong> - पृथ्वी के समताप मंडल में एक क्षेत्र जिसमें ओजोन (O3) की उच्च सांद्रता होती है और सूर्य की हानिकारक पराबैंगनी विकिरणों से पृथ्वी की रक्षा करती है। ओजोन परत का क्षरण - रासायनिक यौगिकों के निकलने के कारण ऊपरी वायुमंडल में पृथ्वी की ओजोन परत का धीरे-धीरे पतला होना। 16 सितंबर - ओजोन परत के संरक्षण के लिए अंतर्राष्ट्रीय दिवस।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>