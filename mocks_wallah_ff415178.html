<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #7209b7;
            --secondary: #4361ee;
            --accent: #f72585;
            --success: #06d6a0;
            --warning: #ffd166;
            --danger: #ef476f;
            --light: #f8f9fa;
            --dark: #212529;
        }
        body {
            background-color: #f0f2f5;
            font-size: 16px;
            padding-bottom: 60px; /* Space for fixed navigation */
        }
        .navbar {
            background: linear-gradient(135deg, var(--primary), var(--secondary)) !important;
            box-shadow: 0 2px 10px rgba(0,0,0,.1);
            padding: 0.5rem 1rem;
        }
        .navbar-brand {
            font-size: 1.1rem;
            margin-right: 0;
        }
        .navbar .container-fluid {
            justify-content: space-between;
        }
        .controls-group {
            display: flex;
            align-items: center;
            flex-wrap: nowrap;
        }
        .timer-display {
            background: linear-gradient(135deg, #4361ee, #3a0ca3);
            color: white;
            border-radius: 50px;
            box-shadow: 0 3px 8px rgba(0,0,0,.1);
            padding: 0.25rem 0.75rem;
            font-size: 0.875rem;
            white-space: nowrap;
        }
        .nav-btn {
            padding: 0.25rem 0.75rem;
            font-size: 0.875rem;
            margin-left: 0.25rem;
        }
        .question-nav {
            position: fixed;
            top: 56px;
            right: 0;
            height: calc(100vh - 56px);
            width: 280px;
            transform: translateX(280px);
            transition: transform .3s;
            z-index: 100;
            background: #fff;
            box-shadow: -5px 0 15px rgba(0,0,0,.1);
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .main-content {
            transition: margin .3s;
            min-height: calc(100vh - 116px);
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.5;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(32px, 1fr));
            gap: 6px;
        }
        .q-box {
            width: 35px;
            height: 35px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 5px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid #dee2e6;
            background: #fff;
            transition: all .2s;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: 0 3px 5px rgba(0,0,0,.1);
        }
        .q-box.attempted {
            background-color: var(--secondary);
            color: white;
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,.15);
        }
        .card {
            border-radius: 12px;
            overflow: hidden;
            border: none;
            box-shadow: 0 4px 12px rgba(0,0,0,.08);
        }
        .option-label {
            border-radius: 8px;
            transition: all .2s;
            border: 1px solid #dee2e6;
            padding: 0.75rem 1rem;
            font-size: 1rem;
        }
        .option-label:hover {
            background-color: #f8f9fa;
            transform: translateY(-2px);
            box-shadow: 0 3px 8px rgba(0,0,0,.05);
        }
        .form-check-input:checked + .option-label {
            border-color: var(--primary);
            background-color: rgba(114,9,183,.05);
        }
        .btn-primary {
            background-color: var(--primary);
            border-color: var(--primary);
        }
        .btn-outline-primary {
            color: var(--primary);
            border-color: var(--primary);
        }
        .btn-outline-primary:hover {
            background-color: var(--primary);
            color: white;
        }
        /* Bottom fixed navigation for mobile */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: white;
            box-shadow: 0 -2px 10px rgba(0,0,0,.1);
            padding: 10px 15px;
            display: flex;
            justify-content: space-between;
            z-index: 90;
        }
        @media (min-width: 992px) {
            .main-content.with-nav {
                margin-right: 280px;
            }
            .question-nav {
                transform: translateX(0);
            }
            .nav-toggle-btn {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(30px, 1fr));
            }
            .option-label {
                padding: 0.625rem 0.875rem;
                font-size: 0.95rem;
            }
            .question-text {
                font-size: 1rem;
            }
            .navbar-brand {
                font-size: 1rem;
            }
            .timer-display {
                font-size: 0.8125rem;
                padding: 0.2rem 0.6rem;
            }
            .nav-btn {
                padding: 0.2rem 0.6rem;
                font-size: 0.8125rem;
            }
            .container {
                padding-left: 10px;
                padding-right: 10px;
            }
        }
    </style>
</head>
<body>
    <!-- Header navbar -->
    <nav class="navbar navbar-dark sticky-top">
        <div class="container-fluid px-2">
            <a class="navbar-brand fw-bold" href="#"><i class="fas fa-graduation-cap me-1"></i></a>
            <div class="controls-group">
                <!-- Timer -->
                <div class="timer-display me-1 fw-bold" id="timer">
                    <i class="far fa-clock me-1"></i><span id="timer-display">8:00</span>
                </div>
                <!-- Submit button -->
                <button class="btn btn-light btn-sm nav-btn me-1" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Language toggle -->
                <button class="btn btn-light btn-sm nav-btn me-1" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="btn btn-light btn-sm nav-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </nav>
    <!-- Main content -->
    <div class="main-content" id="main-content">
        <div class="container py-3">
            <!-- Question counter -->
            <div class="fw-bold text-center mb-3" id="question-counter">Question 1 of 25</div>
            <!-- Questions will be displayed here -->
            <div id="questions-container"></div>
        </div>
    </div>
    <!-- Bottom navigation for mobile -->
    <div class="bottom-nav d-lg-none">
        <button class="btn btn-outline-primary px-3" onclick="prevQuestion()">
            <i class="fas fa-chevron-left me-1"></i> Previous
        </button>
        <button class="btn btn-outline-primary px-3" onclick="nextQuestion()">
            Next <i class="fas fa-chevron-right ms-1"></i>
        </button>
    </div>
    <!-- Question navigation sidebar -->
    <div class="question-nav" id="question-nav">
        <div class="p-3">
            <div class="d-flex justify-content-between align-items-center mb-3">
                <h5 class="m-0">Questions</h5>
                <div>
                    <span class="badge bg-secondary me-1" id="attempted-count">0</span>
                    <span class="badge bg-secondary" id="total-count">25</span>
                    <button class="btn btn-sm btn-outline-secondary ms-2 d-lg-none" onclick="toggleNav()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
            <!-- Section selector moved here -->
            <div class="mb-3">
                <label for="section-selector" class="form-label small text-muted">Jump to Section</label>
                <select class="form-select form-select-sm" id="section-selector" onchange="switchSection(this.value)">
                    <option value="all">All Sections</option>
                </select>
            </div>
            <div class="question-grid" id="question-boxes"></div>
        </div>
    </div>
    <!-- Results modal -->
    <div class="modal fade" id="results-modal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content border-0">
                <div class="modal-header border-0" style="background:linear-gradient(135deg,var(--primary),var(--secondary));color:white">
                    <h5 class="modal-title">Test Results</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body p-4">
                    <div class="row text-center g-2">
                        <div class="col-6">
                            <div class="p-3 rounded shadow-sm" style="background:linear-gradient(135deg,#4361ee,#3a0ca3);color:white">
                                <h3 id="score-value" class="mb-0 fw-bold">0</h3>
                                <div class="small">Score</div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="p-3 rounded shadow-sm" style="background:linear-gradient(135deg,#06d6a0,#1b9aaa);color:white">
                                <h3 id="correct-value" class="mb-0 fw-bold">0</h3>
                                <div class="small">Correct</div>
                            </div>
                        </div>
                        <div class="col-6 mt-2">
                            <div class="p-3 rounded shadow-sm" style="background:linear-gradient(135deg,#ef476f,#f72585);color:white">
                                <h3 id="incorrect-value" class="mb-0 fw-bold">0</h3>
                                <div class="small">Incorrect</div>
                            </div>
                        </div>
                        <div class="col-6 mt-2">
                            <div class="p-3 rounded shadow-sm" style="background:linear-gradient(135deg,#6c757d,#495057);color:white">
                                <h3 id="unattempted-value" class="mb-0 fw-bold">0</h3>
                                <div class="small">Unattempted</div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer border-0">
                    <button type="button" class="btn btn-primary" onclick="reviewTest()">Review Test</button>
                </div>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 8 * 60;
        let resultsModal;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            resultsModal = new bootstrap.Modal(document.getElementById('results-modal'));
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["18"] = {
                name: "General Awareness",
                start: 0,
                end: 23
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="18">General Awareness</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 24,
                end: 24
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "18",
                    question_en: " <p>1. Who among the following persons was announced as the head of the Committee to protect language, culture, and land in Ladakh by the Central Government in January 2021?</p> <p> SSC CGL 17/08/21(Morning)</p>",
                    question_hi: " <p>1. निम्नलिखित व्यक्तियों में से किसे जनवरी 2021 में केंद्र सरकार द्वारा लद्दाख में भाषा, संस्कृति और भूमि की रक्षा के लिए समिति के प्रमुख के रूप में घोषित किया गया था? </span></p> <p><span style=\"font-family:Palanquin Dark\">SSC CGL 17/08/21(Morning)</span></p>",
                    options_en: [" <p> G Kishan Reddy </p>", " <p> Piyush Goyal </p>", 
                                " <p> Arjun Munda </p>", " <p> Raj Kumar Singh </p>"],
                    options_hi: [" <p> जी किशन रेड्डी</span></p>", " <p>  पीयूष गोयल</span></p>",
                                " <p>  अर्जुन मुंडा</span></p>", " <p> राज कुमार सिंह</span></p>"],
                    solution_en: " <p>1.(a) G Kishan Reddy was announced as the head of the Committee to protect language, culture and land in Ladakh by the Central Government in January 2021. He is an Indian politician who is currently serving as Minister of Tourism, Culture and Development of North Eastern Region of India.</span></p>",
                    solution_hi: " <p>1.(a) जी किशन रेड्डी को जनवरी 2021 में केंद्र सरकार द्वारा लद्दाख में भाषा, संस्कृति और भूमि की रक्षा के लिए समिति के प्रमुख के रूप में घोषित किया गया था। वह एक भारतीय राजनेता हैं जो वर्तमान में पर्यटन, संस्कृति मंत्री एवं भारत के उत्तर पूर्वी क्षेत्र का विकास मंत्री के रूप में कार्यरत हैं।</span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "18",
                    question_en: "<p>2. Which two great footballers, who were members of the gold medal-winning team of the 1962 Asian Games, passed away in 2020?</p>\n<p><span style=\"font-family: Palanquin Dark;\">SSC CGL 17/08/21(Morning) </span></p>",
                    question_hi: "<p>2. 1962 के एशियाई खेलों की स्वर्ण पदक विजेता टीम के सदस्य कौन से दो महान फुटबॉलर का 2020 में निधन हो गया?</p>\n<p><span style=\"font-family: Palanquin Dark;\"> SSC CGL 17/08/21(Morning)</span></p>",
                    options_en: ["<p>Ram Bahadur and Jarnail Singh</p>", "<p>Krishanu Dey and C Prasad</p>", 
                                "<p>Chuni Goswami and PK Banerjee</p>", "<p>Yousuf Khan and TA Rahaman</p>"],
                    options_hi: ["<p>राम बहादुर और जरनैल सिंह</p>", "<p>कृष्णु डे और सी प्रसाद</p>",
                                "<p>चुन्नी गोस्वामी और पीके बनर्जी</p>", "<p>यूसुफ खान और टीए रहमान</p>"],
                    solution_en: "<p>2.(c) Chuni Goswami was a professional footballer from Kolkata who held the position of former &ldquo;Sheriff of Kolkata&rdquo;, died on 30 April 2020.<br><span style=\"font-family: Palanquin Dark;\">PK Banerjee was the striker of Indian National Football team from Jamshedpur, Jharkhand. He was one of the recipients of the Arjun Award, died on 20 march 2020.</span></p>",
                    solution_hi: "<p>2.(c) चुन्नी गोस्वामी कोलकाता के एक पेशेवर फुटबॉलर थे, जिन्होंने पूर्व \"कोलकाता के शेरिफ\" का पद संभाला था, उनका 30 अप्रैल 2020 को निधन हो गया।<br><span style=\"font-family: Palanquin Dark;\">पीके बनर्जी झारखंड के जमशेदपुर से भारतीय राष्ट्रीय फुटबॉल टीम के स्ट्राइकर थे। वह अर्जुन पुरस्कार प्राप्त करने वालों में से एक थे, जिनका 20 मार्च 2020 को निधन हो गया।</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "18",
                    question_en: " <p>3. Which of the following is one of the three \'Domestic Systemically Important Banks\' in India?</span></p> <p><span style=\"font-family:Palanquin Dark\"> SSC CGL 17/08/21(Morning)</span></p>",
                    question_hi: "<p>3. निम्नलिखित में से कौन भारत में तीन \'घरेलू व्यवस्थित रूप से महत्वपूर्ण बैंकों\' में से एक है?</p>\r\n<p><span style=\"font-family: Palanquin Dark;\"> SSC CGL 17/08/21(Morning)</span></p>",
                    options_en: [" <p> State Bank of India (SBI) </span></p>", " <p> Federal Bank </span></p>", 
                                " <p> RBL Bank </span></p>", " <p> Axis Bank </span></p>"],
                    options_hi: ["<p><span style=\"font-weight: 400;\">State Bank of India (SBI)</span></p>", "<p>फेडरल बैंक</p>",
                                "<p>RBL बैंक</p>", "<p>एक्सिस बैंक</p>"],
                    solution_en: " <p>3.(a) State Bank of India (SBI) is one of the three \'Domestic Systemically Important Banks\' (D-SIB) in India. Systemically important </span><span style=\"font-family:Roboto\"> banks are</span><span style=\"font-family:Roboto\"> </span><span style=\"font-family:Roboto\">considered as “too big to fail”</span><span style=\"font-family:Roboto\">. </span><span style=\"font-family:Roboto\">These banks are large in size and have complex judicial systems. SBI, ICICI Bank and HDFC Bank are D-SIBs, which means that the distress or failure of these banks will have an impact on the domestic economy. </span></p>",
                    solution_hi: "<p>3.(a) भारतीय स्टेट बैंक (SBI) भारत में तीन \'घरेलू व्यवस्थित रूप से महत्वपूर्ण बैंकों\' (D-SIB) में से एक है। व्यवस्थित रूप से महत्वपूर्ण बैंकों को \"विफल होने के लिए बहुत बड़ा\" माना जाता है। ये बैंक आकार में बड़े हैं और इनमें जटिल न्यायिक प्रणालियाँ हैं। <span style=\"font-family: Roboto;\">SBI</span><span style=\"font-family: Roboto;\">, </span><span style=\"font-family: Roboto;\">ICICI </span><span style=\"font-family: Palanquin Dark;\">बैंक और </span><span style=\"font-family: Roboto;\">HDFC </span><span style=\"font-family: Palanquin Dark;\">बैंक </span><span style=\"font-family: Roboto;\">D-SIBs</span><span style=\"font-family: Palanquin Dark;\"> हैं, जिसका मतलब है कि इन बैंकों के संकट या विफलता का असर घरेलू अर्थव्यवस्था पर पड़ेगा।</span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "18",
                    question_en: " <p>4. In which of the following years was the Indian Association established? </span></p> <p><span style=\"font-family:Palanquin Dark\">SSC CGL 17/08/21(Morning)</span></p>",
                    question_hi: " <p>4. इंडियन एसोसिएशन की स्थापना निम्नलिखित में से किस वर्ष में हुई थी? </span></p> <p><span style=\"font-family:Palanquin Dark\">SSC CGL 17/08/21(Morning)</span></p>",
                    options_en: [" <p> 1876 </span></p>", " <p> 1881 </span></p>", 
                                " <p> 1903 </span></p>", " <p> 1856 </span></p>"],
                    options_hi: [" <p> 1876</span></p>", " <p> 1881</span></p>",
                                " <p> 1903</span></p>", " <p> 1856</span></p>"],
                    solution_en: " <p>4.(a) The Indian Association was established in 1876. It was founded by Surendranath Banerjee and Ananda Mohan Bose. The objectives of this association were to promote by every legitimate means, the political, intellectual and material advancement of the people.</span></p>",
                    solution_hi: " <p>4.(a) इंडियन एसोसिएशन की स्थापना 1876 में हुई थी। इसकी स्थापना सुरेंद्रनाथ बनर्जी और आनंद मोहन बोस ने की थी। इस एसोसिएशन का उद्देश्य लोगों की राजनीतिक, बौद्धिक और भौतिक उन्नति को हर वैध तरीके से बढ़ावा देना था।</span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "18",
                    question_en: " <p>5. Who among the following wrote the basic text of Vaisheshika philosophy? </span></p> <p><span style=\"font-family:Palanquin Dark\">SSC CGL 17/08/21(Morning)</span></p>",
                    question_hi: "<p>5. &#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340; &#2350;&#2375;&#2306; &#2360;&#2375; &#2325;&#2367;&#2360;&#2344;&#2375; &#2357;&#2376;&#2358;&#2375;&#2359;&#2367;&#2325; &#2342;&#2352;&#2381;&#2358;&#2344; &#2325;&#2366; &#2350;&#2370;&#2354; &#2346;&#2366;&#2336; &#2354;&#2367;&#2326;&#2366; &#2341;&#2366;?</p>\r\n<p><span style=\"font-family: Palanquin Dark;\">SSC CGL 17/08/21(Morning)</span></p>\n",
                    options_en: [" <p> Kanada </span></p>", " <p> Shankaracharya </span></p>", 
                                " <p> Patanjali </span></p>", " <p> Jaimini </span></p>"],
                    options_hi: ["<p>&#2325;&#2339;&#2366;&#2342;&nbsp;</p>\n", "<p>&#2358;&#2306;&#2325;&#2352;&#2366;&#2330;&#2366;&#2352;&#2381;&#2351;</p>\n",
                                "<p>&#2346;&#2340;&#2306;&#2332;&#2354;&#2367;</p>\n", "<p>&#2332;&#2376;&#2350;&#2367;&#2344;&#2368;</p>\n"],
                    solution_en: " <p>5.(a) Kanada wrote the basic text of Vaisheshika philosophy. It is an ancient Sanskrit text at the foundation of the Vaisheshika school of Hindu philosophy. The sutra was authored by the Hindu sage Kanada, also known as Kashyapa. </span></p>",
                    solution_hi: "<p>5.(a) &#2325;&#2339;&#2366;&#2342; &#2344;&#2375; &#2357;&#2376;&#2358;&#2375;&#2359;&#2367;&#2325; &#2342;&#2352;&#2381;&#2358;&#2344; &#2325;&#2366; &#2350;&#2370;&#2354; &#2346;&#2366;&#2336; &#2354;&#2367;&#2326;&#2366;&#2404; &#2351;&#2361; &#2361;&#2367;&#2306;&#2342;&#2370; &#2342;&#2352;&#2381;&#2358;&#2344; &#2325;&#2375; &#2357;&#2376;&#2358;&#2375;&#2359;&#2367;&#2325; &#2360;&#2381;&#2325;&#2370;&#2354; &#2325;&#2368; &#2344;&#2368;&#2306;&#2357; &#2346;&#2352; &#2319;&#2325; &#2346;&#2381;&#2352;&#2366;&#2330;&#2368;&#2344; &#2360;&#2306;&#2360;&#2381;&#2325;&#2371;&#2340; &#2346;&#2366;&#2336; &#2361;&#2376;&#2404; &#2360;&#2370;&#2340;&#2381;&#2352; &#2361;&#2367;&#2306;&#2342;&#2370; &#2315;&#2359;&#2367; &#2325;&#2339;&#2366;&#2342; &#2342;&#2381;&#2357;&#2366;&#2352;&#2366; &#2354;&#2367;&#2326;&#2366; &#2327;&#2351;&#2366; &#2341;&#2366;, &#2332;&#2367;&#2360;&#2375; &#2325;&#2358;&#2381;&#2351;&#2346; &#2325;&#2375; &#2344;&#2366;&#2350; &#2360;&#2375; &#2349;&#2368; &#2332;&#2366;&#2344;&#2366; &#2332;&#2366;&#2340;&#2366; &#2361;&#2376;&#2404;</p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "18",
                    question_en: "<p>6. A person\'s Vedic approach and his relationship with society are determined by the four goals of life. Which of the following is not among these goals?</p>\n<p><span style=\"font-family: Palanquin Dark;\">SSC CGL 17/08/21(Morning)</span></p>",
                    question_hi: "<p>6. एक व्यक्ति का वैदिक दृष्टिकोण और समाज के साथ उसका संबंध जीवन के चार लक्ष्यों से निर्धारित होता है। निम्नलिखित में से कौन इन लक्ष्यों में से नहीं है?</p>\n<p><span style=\"font-family: Palanquin Dark;\"> SSC CGL 17/08/21(Morning)</span></p>",
                    options_en: ["<p>sidhi</p>", "<p>artha</p>", 
                                "<p>moksha</p>", "<p>dharma</p>"],
                    options_hi: ["<p>सिद्धि</p>", "<p>अर्थ</p>",
                                "<p>मोक्ष</p>", "<p>धर्म</p>"],
                    solution_en: "<p>6.(a) Sidhi is not the goal which determines vedic approach of a person and its relationship with the society.<br><span style=\"font-family: Palanquin Dark;\">The four goals determining a person&rsquo;s approach are Kama, Artha, Dharma and Moksha. </span></p>",
                    solution_hi: "<p>6.(a) सिद्धि वह लक्ष्य नहीं है जो किसी व्यक्ति के वैदिक दृष्टिकोण और समाज के साथ उसके संबंध को निर्धारित करता है।<br><span style=\"font-family: Palanquin Dark;\">किसी व्यक्ति के दृष्टिकोण को निर्धारित करने वाले चार लक्ष्य काम, अर्थ, धर्म और मोक्ष हैं।</span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "18",
                    question_en: " <p>7. Which of the following elements’ salts are most soluble? </span></p> <p><span style=\"font-family:Palanquin Dark\">SSC CGL 17/08/21(Morning)</span></p>",
                    question_hi: " <p>7. निम्नलिखित में से किस तत्व के लवण सबसे अधिक घुलनशील हैं? </span></p> <p><span style=\"font-family:Palanquin Dark\">SSC CGL 17/08/21(Morning)</span></p>",
                    options_en: [" <p> Lithium </span></p>", " <p> Sodium </span></p>", 
                                " <p> Rubidium </span></p>", " <p> Potassium </span></p>"],
                    options_hi: [" <p> लिथियम</span></p>", " <p>  सोडियम</span></p>",
                                " <p>  रूबिडियम</span></p>", " <p> पोटेशियम</span></p>"],
                    solution_en: " <p>7.(b) Sodium salts are the most soluble because sodium readily combines with water forming covalent bonds.</span></p>",
                    solution_hi: " <p>7.(b) सोडियम लवण सबसे अधिक घुलनशील होते हैं क्योंकि सोडियम आसानी से पानी के साथ मिलकर सहसंयोजक बंध बनाता है।</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "18",
                    question_en: "<p>8. Pushyabhuti, who ruled from Thaneswar, was the founder of ______ dynasty.</p>\n<p><span style=\"font-family: Palanquin Dark;\"> SSC CGL 17/08/21(Morning)</span></p>",
                    question_hi: "<p>8. थानेश्वर से शासन करने वाले पुष्यभूति ________ वंश के संस्थापक थे।</p>\n<p><span style=\"font-family: Palanquin Dark;\">SSC CGL 17/08/21(Morning)</span></p>",
                    options_en: ["<p>Vardhana</p>", "<p>Chera</p>", 
                                "<p>Pandya</p>", "<p>Chalukya</p>"],
                    options_hi: ["<p>वर्धन</p>", "<p>चेरा</p>",
                                "<p>पांड्या</p>", "<p>चालुक्य:</p>"],
                    solution_en: "<p>8.(a) Pushyabhuti was the founder of Vardhana dynasty. Prabhakar Vardhana was the first notable king of the Vardhana Dynasty. The dynasty reached its glory under the last king of this dynasty, Harshavardhana.</p>",
                    solution_hi: "<p>8.(a) पुष्यभूति वर्धन वंश का संस्थापक था। प्रभाकर वर्धन वर्धन वंश के उल्लेखनीय राजा थे। इस राजवंश के अंतिम राजा हर्षवर्धन के अधीन राजवंश अपने गौरव पर पहुँच गया।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "18",
                    question_en: " <p>9. All matches of the 2020-21 edition of Hero Indian Super League are being played in which of the following states? </span></p> <p><span style=\"font-family:Palanquin Dark\">SSC CGL 17/08/21(Morning)</span></p>",
                    question_hi: " <p>9. हीरो इंडियन सुपर लीग के 2020-21 संस्करण के सभी मैच निम्नलिखित में से किस राज्य में खेले जा रहे हैं? </span></p> <p><span style=\"font-family:Palanquin Dark\">SSC CGL 17/08/21(Morning)</span></p>",
                    options_en: [" <p> West Bengal </span></p>", " <p> Karnataka </span></p>", 
                                " <p> Goa </span></p>", " <p> Kerala </span></p>"],
                    options_hi: [" <p> पश्चिम बंगाल</span></p>", " <p>  कर्नाटक</span></p>",
                                " <p>  गोवा</span></p>", " <p> केरल</span></p>"],
                    solution_en: " <p>9.(c) All matches of the 2020-21 edition of Hero Indian Super League are being played in Goa. Mumbai City FC won the Hero Indian Super League.</span></p>",
                    solution_hi: " <p>9.(c) हीरो इंडियन सुपर लीग के 2020-21 संस्करण के सभी मैच गोवा में खेले जा रहे हैं। मुंबई सिटी FC ने हीरो इंडियन सुपर लीग जीती।</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "18",
                    question_en: " <p>10. In which of the following states is the Bhitarkanika National Park situated?</span></p> <p><span style=\"font-family:Palanquin Dark\"> SSC CGL 17/08/21(Morning)</span></p>",
                    question_hi: " <p>10. भितरकनिका राष्ट्रीय उद्यान निम्नलिखित में से किस राज्य में स्थित है?</span></p> <p><span style=\"font-family:Palanquin Dark\"> SSC CGL 17/08/21(Morning) </span></p>",
                    options_en: [" <p> Kerala </span></p>", " <p> Meghalaya </span></p>", 
                                " <p> Goa </span></p>", " <p> Odisha </span></p>"],
                    options_hi: [" <p> केरल</span></p>", " <p>  मेघालय</span></p>",
                                " <p>  गोवा</span></p>", " <p> ओडिशा</span></p>"],
                    solution_en: " <p>10.(d) Bhitarkanika National Park is situated in Odisha. It is located in Odisha\'s Kendrapara district which shares borders with famous Gahirmatha beach and is surrounded by the Bhitarkanika Wildlife Sanctuary.</span></p>",
                    solution_hi: " <p>10.(d) भितरकनिका राष्ट्रीय उद्यान ओडिशा में स्थित है। यह ओडिशा के केंद्रपाड़ा जिले में स्थित है जो प्रसिद्ध गहिरमाथा समुद्र तट के साथ सीमा साझा करता है और भितरकनिका वन्यजीव अभयारण्य से घिरा हुआ है।</span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "18",
                    question_en: "<p>11. Which of the following Articles of the Constitution of India adopted Hindi in Devanagari script as the official language of the Union?</p>\r\n<p><span style=\"font-family: Palanquin Dark;\">SSC CGL 17/08/21(Morning) </span></p>\n",
                    question_hi: "<p>11. &#2349;&#2366;&#2352;&#2340; &#2325;&#2375; &#2360;&#2306;&#2357;&#2367;&#2343;&#2366;&#2344; &#2325;&#2375; &#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340; &#2350;&#2375;&#2306; &#2360;&#2375; &#2325;&#2367;&#2360; &#2309;&#2344;&#2369;&#2330;&#2381;&#2331;&#2375;&#2342; &#2344;&#2375; &#2342;&#2375;&#2357;&#2344;&#2366;&#2327;&#2352;&#2368; &#2354;&#2367;&#2346;&#2367; &#2350;&#2375;&#2306; &#2361;&#2367;&#2306;&#2342;&#2368; &#2325;&#2379; &#2360;&#2306;&#2328; &#2325;&#2368; &#2310;&#2343;&#2367;&#2325;&#2366;&#2352;&#2367;&#2325; &#2349;&#2366;&#2359;&#2366; &#2325;&#2375; &#2352;&#2370;&#2346; &#2350;&#2375;&#2306; &#2309;&#2346;&#2344;&#2366;&#2351;&#2366;?</p>\r\n<p><span style=\"font-family: Palanquin Dark;\"> SSC CGL 17/08/21(Morning)</span></p>\n",
                    options_en: ["<p>Article 343 (1)</p>\n", "<p>Article 231</p>\n", 
                                "<p>Article 108</p>\n", "<p>Article 123</p>\n"],
                    options_hi: ["<p>&nbsp;343 (1)</p>\n", "<p>&#2309;&#2344;&#2369;&#2330;&#2381;&#2331;&#2375;&#2342; 231</p>\n",
                                "<p>&#2309;&#2344;&#2369;&#2330;&#2381;&#2331;&#2375;&#2342; 108</p>\n", "<p>&#2309;&#2344;&#2369;&#2330;&#2381;&#2331;&#2375;&#2342; 123</p>\n"],
                    solution_en: "<p>11.(a) Article 343 (1) of the Constitution of India adopted Hindi in Devanagari script as the official language of the Union.</p>\n",
                    solution_hi: "<p>11.(a) &#2349;&#2366;&#2352;&#2340; &#2325;&#2375; &#2360;&#2306;&#2357;&#2367;&#2343;&#2366;&#2344; &#2325;&#2375; &#2309;&#2344;&#2369;&#2330;&#2381;&#2331;&#2375;&#2342; 343 (1) &#2344;&#2375; &#2342;&#2375;&#2357;&#2344;&#2366;&#2327;&#2352;&#2368; &#2354;&#2367;&#2346;&#2367; &#2350;&#2375;&#2306; &#2361;&#2367;&#2306;&#2342;&#2368; &#2325;&#2379; &#2360;&#2306;&#2328; &#2325;&#2368; &#2310;&#2343;&#2367;&#2325;&#2366;&#2352;&#2367;&#2325; &#2349;&#2366;&#2359;&#2366; &#2325;&#2375; &#2352;&#2370;&#2346; &#2350;&#2375;&#2306; &#2309;&#2346;&#2344;&#2366;&#2351;&#2366;&#2404;</p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "18",
                    question_en: " <p>12. ______ is the ability of a single cell to produce a fertile, adult individual.</span></p> <p><span style=\"font-family:Palanquin Dark\"> SSC CGL 17/08/21(Morning). </span></p>",
                    question_hi: " <p>12. ________ एक एकल कोशिका की एक उर्वर, वयस्क जीव पैदा करने की क्षमता है। </span></p> <p><span style=\"font-family:Palanquin Dark\">SSC CGL 17/08/21(Morning)</span></p>",
                    options_en: [" <p> Pluripotency </span></p>", " <p> Totipotency </span></p>", 
                                " <p> Cloning </span></p>", " <p> Mutation </span></p>"],
                    options_hi: [" <p> प्लुरिपोटेंसी</span></p>", " <p>  टोटिपोटेंसी</span></p>",
                                " <p>  क्लोनिंग</span></p>", " <p> उत्परिवर्तन</span></p>"],
                    solution_en: " <p>12.(b) Totipotency is the ability of a single cell to produce a fertile adult individual. Pluripotency is defined as the capacity of individual cells to initiate all lineages of the mature organism. Cloning is a technique scientists use to make exact genetic copies of living things. A mutation occurs when a DNA gene is damaged or changed in such a way as to alter the genetic message carried by that gene.</span></p>",
                    solution_hi: " <p>12.(b) टोटिपोटेंसी (पूर्णशक्तता) एक एकल कोशिका की एक उपजाऊ वयस्क जीव पैदा करने की क्षमता है। प्लुरिपोटेंसी को परिपक्व जीव के सभी वंशों को आरंभ करने के लिए एकल कोशिकाओं की क्षमता के रूप में परिभाषित किया गया है। क्लोनिंग एक तकनीक है जिसका उपयोग वैज्ञानिक जीवित चीजों की सटीक आनुवंशिक प्रतियां बनाने के लिए करते हैं। एक उत्परिवर्तन तब होता है जब एक डीएनए जीन क्षतिग्रस्त हो जाता है या इस तरह से बदल जाता है कि उस जीन द्वारा किए गए आनुवंशिक संदेश को बदल दिया जाता है।</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "18",
                    question_en: " <p>13. Whose administration became a model that was followed by Akbar when he consolidated the Mughal Empire? </span></p> <p><span style=\"font-family:Palanquin Dark\">SSC CGL 17/08/21(Morning)</span></p>",
                    question_hi: " <p>13. किसका प्रशासन एक मॉडल बन गया जिसका अनुसरण अकबर ने किया जब उसने मुगल साम्राज्य को मजबूत किया? </span></p> <p><span style=\"font-family:Palanquin Dark\">SSC CGL 17/08/21(Morning)</span></p>",
                    options_en: [" <p> Sher Shah Suri </span></p>", " <p> Ghiyas ud din Balban </span></p>", 
                                " <p> Iltutmish </span></p>", " <p> Bahlul Lodi </span></p>"],
                    options_hi: [" <p> शेर शाह सूरी</span></p>", " <p>  गयास उद दीन बलबन</span></p>",
                                " <p>  इल्तुतमिश</span></p>", " <p> बहलुल लोदी</span></p>"],
                    solution_en: " <p>13.(a) Sher Shah Suri’s administration became a model for Akbar. The Suri dynasty ruled for only fifteen years (1540-1555). He introduced an administration that borrowed elements from Alauddin Khalji and made them more efficient. </span></p>",
                    solution_hi: " <p>13.(a) शेर शाह सूरी का प्रशासन अकबर के लिए एक आदर्श बन गया। सूरी वंश ने केवल पंद्रह वर्षों (1540-1555) तक शासन किया। उन्होंने एक ऐसे प्रशासन की शुरुआत की जिसने अलाउद्दीन खिलजी से तत्वों को उधार लिया और उन्हें और अधिक कुशल बनाया।</span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "18",
                    question_en: " <p>14. Which of the following is another name for \'white ant\'? </span></p> <p><span style=\"font-family:Palanquin Dark\">SSC CGL 17/08/21(Morning)</span></p>",
                    question_hi: " <p>14. निम्नलिखित में से कौन \'सफेद चींटी\' का दूसरा नाम क्या है? </span></p> <p><span style=\"font-family:Palanquin Dark\">SSC CGL 17/08/21(Morning)</span></p>",
                    options_en: [" <p> Termite </span></p>", " <p> Mosquito </span></p>", 
                                " <p> Wasp </span></p>", " <p> Housefly </span></p>"],
                    options_hi: [" <p> दीमक</span></p>", " <p>  मच्छर</span></p>",
                                " <p>  ततैया</span></p>", " <p> घरेलू मक्खी</span></p>"],
                    solution_en: " <p>14.(a) Termite is another name for \'white ant\' or ‘wood ant’. A termite nest is known as a termitary or termitarium. </span></p>",
                    solution_hi: " <p>14.(a) दीमक \'सफेद चींटी\' या \'लकड़ी की चींटी\' का दूसरा नाम है। दीमक के घोंसले को दीमक या दीमक के रूप में जाना जाता है।</span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "18",
                    question_en: " <p>15. Who wrote the book \'The Quest for Equity in Development\'? </span></p> <p><span style=\"font-family:Palanquin Dark\">SSC CGL 17/08/21(Morning)</span></p>",
                    question_hi: " <p>15. \'द क्वेस्ट फॉर इक्विटी इन डेवलपमेंट\' पुस्तक किसने लिखी है?</span></p> <p><span style=\"font-family:Palanquin Dark\"> SSC CGL 17/08/21(Morning)</span></p>",
                    options_en: [" <p> Amartya Sen </span></p>", " <p> Montek Singh Ahluwalia </span></p>", 
                                " <p> Raghuram Rajan </span></p>", " <p> Manmohan Singh </span></p>"],
                    options_hi: [" <p> अमर्त्य सेन</span></p>", " <p>  मोंटेक सिंह अहलूवालिया</span></p>",
                                " <p>  रघुराम राजन</span></p>", " <p> मनमोहन सिंह</span></p>"],
                    solution_en: " <p>15.(d) The Quest for Equity in Development was written by Manmohan Singh. He is the former Prime Minister of India. </span></p>",
                    solution_hi: " <p>15.(d) द क्वेस्ट फॉर इक्विटी इन डेवलपमेंट मनमोहन सिंह द्वारा लिखी गई थी। वह भारत के पूर्व प्रधानमंत्री हैं।</span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "16",
                    section: "18",
                    question_en: " <p>16. Which of the following is NOT a greenhouse gas? </span></p> <p><span style=\"font-family:Palanquin Dark\">SSC CGL 17/08/21(Morning)</span></p>",
                    question_hi: " <p>16. निम्न में से कौन ग्रीन हाउस गैस नहीं है? </span></p> <p><span style=\"font-family:Palanquin Dark\">SSC CGL 17/08/21(Morning)</span></p>",
                    options_en: [" <p> Helium  </span></p>", " <p> Water vapour </span></p>", 
                                " <p> Surface-level ozone </span></p>", " <p> Nitrous oxide </span></p>"],
                    options_hi: [" <p> हीलियम</span></p>", " <p> जल वाष्प</span></p>",
                                " <p> सतह स्तर ओजोन</span></p>", " <p> नाइट्रस ऑक्साइड</span></p>"],
                    solution_en: " <p>16.(a) Helium is NOT a greenhouse gas. Greenhouse gases include water vapour, carbon dioxide, methane, nitrous oxide, ozone and some artificial chemicals such as chlorofluorocarbons (CFCs). </span></p>",
                    solution_hi: " <p>16.(a) हीलियम ग्रीनहाउस गैस नहीं है। ग्रीनहाउस गैसों में जल वाष्प, कार्बन डाइऑक्साइड, मीथेन, नाइट्रस ऑक्साइड, ओजोन और कुछ कृत्रिम रसायन जैसे क्लोरोफ्लोरोकार्बन (CFCs) शामिल हैं।</span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "17",
                    section: "18",
                    question_en: " <p>17. Almost 98% of earth\'s crust is made up of eight elements. Which of the following does NOT figure among these elements? </span></p> <p><span style=\"font-family:Palanquin Dark\">SSC CGL 17/08/21(Morning)</span></p>",
                    question_hi: " <p>17. पृथ्वी की पपड़ी का लगभग 98% भाग आठ तत्वों से बना है। निम्नलिखित में से कौन इन तत्वों में नहीं आता है? </span></p> <p><span style=\"font-family:Palanquin Dark\">SSC CGL 17/08/21(Morning) </span></p>",
                    options_en: [" <p> Carbon</span></p>", " <p> Oxygen </span></p>", 
                                " <p> Calcium </span></p>", " <p> Silicon</span></p>"],
                    options_hi: [" <p> कार्बन</span></p>", " <p>  ऑक्सीजन</span></p>",
                                " <p>  कैल्शियम</span></p>", " <p> सिलिकॉन</span></p>"],
                    solution_en: " <p>17.(a) Almost 98% of Earth\'s crust is made up of eight elements-– oxygen, silicon, aluminum, iron, calcium, sodium, potassium, and magnesium. Carbon is not present among these elements. </span></p>",
                    solution_hi: " <p>17.(a) पृथ्वी की पपड़ी का लगभग 98% भाग आठ तत्वों - ऑक्सीजन, सिलिकॉन, एल्यूमीनियम, लोहा, कैल्शियम, सोडियम, पोटेशियम और मैग्नीशियम से बना है। इन तत्वों में कार्बन मौजूद नहीं है।</span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "18",
                    section: "18",
                    question_en: " <p>18. What do you call the amount of monetary or other returns, either earned or unearned, accruing over a period of time? </span></p> <p><span style=\"font-family:Palanquin Dark\">SSC CGL 17/08/21(Morning)</span></p>",
                    question_hi: " <p>18. समय की अवधि में अर्जित या अनर्जित मौद्रिक या अन्य रिटर्न की राशि को क्या कहते हैं?</span></p> <p><span style=\"font-family:Palanquin Dark\"> SSC CGL 17/08/21(Morning)</span></p>",
                    options_en: [" <p> Price </span></p>", " <p> Value </span></p>", 
                                " <p> Services </span></p>", " <p> Income </span></p>"],
                    options_hi: [" <p>  मूल्य</span></p>", " <p>  क़ीमत</span></p>",
                                " <p>  सेवाएं</span></p>", " <p> आय</span></p>"],
                    solution_en: " <p>18.(d) The amount of monetary or other returns, either earned or unearned, accruing over a period of time is called Income. There are three types of income- earned, portfolio and passive. Earned income consists of income you earn while you are working a full-time job or running a business. Passive income is income earned from rents, royalties, and stakes in limited partnerships. Portfolio income is income from dividends, interest, and capital gains from stock sales.</span></p>",
                    solution_hi: " <p>18.(d) समय की अवधि में अर्जित या अनर्जित मौद्रिक या अन्य रिटर्न की राशि को आय कहा जाता है। आय तीन प्रकार की होती है- अर्जित, पोर्टफोलियो और निष्क्रिय। अर्जित आय में वह आय होती है जो आप पूर्णकालिक नौकरी करते हुए या व्यवसाय चलाते समय अर्जित करते हैं। निष्क्रिय आय सीमित भागीदारी में किराए, रॉयल्टी और दांव से अर्जित आय है। पोर्टफोलियो आय लाभांश, ब्याज और स्टॉक बिक्री से पूंजीगत लाभ से होने वाली आय है।</span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "19",
                    section: "18",
                    question_en: " <p>19. Till 20 January 1972, which present-day Indian state was known as the North-East Frontier Agency (NEFA)? </span></p> <p><span style=\"font-family:Palanquin Dark\">SSC CGL 17/08/21(Morning)</span></p>",
                    question_hi: " <p>19. 20 जनवरी 1972 तक किस वर्तमान भारतीय राज्य को नॉर्थ-ईस्ट फ्रंटियर एजेंसी (NEFA) के नाम से जाना जाता था? </span></p> <p><span style=\"font-family:Palanquin Dark\">SSC CGL 17/08/21(Morning)</span></p>",
                    options_en: [" <p> Assam </span></p>", " <p> Nagaland </span></p>", 
                                " <p> Arunachal Pradesh </span></p>", " <p> Sikkim </span></p>"],
                    options_hi: [" <p> असम</span></p>", " <p> नागालैंड</span></p>",
                                " <p> अरुणाचल प्रदेश</span></p>", " <p> सिक्किम</span></p>"],
                    solution_en: " <p>19.(c) Till 20 January 1972, Arunachal Pradesh was known as the North-East Frontier Agency (NEFA). NEFA was part of Assam until it was made the Indian union territory of Arunachal Pradesh in 1972, and in 1987 it was conferred with statehood.</span></p>",
                    solution_hi: " <p>19.(c) 20 जनवरी 1972 तक, अरुणाचल प्रदेश को नॉर्थ-ईस्ट फ्रंटियर एजेंसी (NEFA) के रूप में जाना जाता था। NEFA 1972 में अरुणाचल प्रदेश का भारतीय केंद्र शासित प्रदेश बनने तक असम का हिस्सा था, और 1987 में इसे राज्य का दर्जा दिया गया था।</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "20",
                    section: "18",
                    question_en: " <p>20. What is represented by the product of force with displacement in the direction of force? </span></p> <p><span style=\"font-family:Palanquin Dark\">SSC CGL 17/08/21(Morning)</span></p>",
                    question_hi: " <p>20. बल की दिशा में विस्थापन के साथ बल के गुणनफल द्वारा क्या दर्शाया जाता है?</span></p> <p><span style=\"font-family:Palanquin Dark\"> SSC CGL 17/08/21(Morning)</span></p>",
                    options_en: [" <p> Momentum </span></p>", " <p> Power </span></p>", 
                                " <p> Impulse </span></p>", " <p> Work </span></p>"],
                    options_hi: [" <p> संवेग </span></p>", " <p>  शक्ति </span></p>",
                                " <p>  आवेग</span></p>", " <p> कार्य</span></p>"],
                    solution_en: " <p>20.(d) Work is represented by the product of force with displacement in the direction of the force. The SI unit of work is Joule(J).</span></p>",
                    solution_hi: " <p>20.(d) कार्य को बल की दिशा में विस्थापन के साथ बल के गुणन द्वारा दर्शाया जाता है। कार्य की SI इकाई जूल (J) है|</span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "21",
                    section: "18",
                    question_en: "<p>21. In which year did Atal Bihari Vajpayee undertake a bus journey to Lahore to sign a Peace <span style=\"font-family: Palanquin Dark;\">Declaration? </span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">SSC CGL 17/08/21(Morning)</span></p>",
                    question_hi: " <p>21. अटल बिहारी वाजपेयी ने शांति घोषणा पर हस्ताक्षर करने के लिए लाहौर की बस यात्रा किस वर्ष की थी?</span></p> <p><span style=\"font-family:Palanquin Dark\"> SSC CGL 17/08/21(Morning)</span></p>",
                    options_en: ["<p>1994</p>", "<p>1991</p>", 
                                "<p>1999</p>", "<p>1997</p>"],
                    options_hi: [" <p> 1994</span></p>", " <p> 1991</span></p>",
                                " <p> 1999</span></p>", " <p> 1997</span></p>"],
                    solution_en: "<p>21.(c) Atal Bihari Vajpayee undertook a bus journey to Lahore in 1999 to sign a Peace Declaration also known as Lahore Declaration on 21 February 1999. It is a bilateral agreement and governance treaty between India and Pakistan.</p>",
                    solution_hi: " <p>21.(c) अटल बिहारी वाजपेयी ने 1999 में एक शांति घोषणा पर हस्ताक्षर करने के लिए लाहौर की बस यात्रा की, जिसे 21 फरवरी 1999 को लाहौर घोषणा के रूप में भी जाना जाता है। यह भारत और पाकिस्तान के बीच एक द्विपक्षीय समझौता और शासन संधि है।</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "22",
                    section: "18",
                    question_en: " <p>22. How is Dhanpat Rai Srivastava better known?</span></p> <p><span style=\"font-family:Palanquin Dark\"> SSC CGL 17/08/21(Morning)</span></p>",
                    question_hi: " <p>22. धनपत राय श्रीवास्तव को किस नाम से जाना जाता है?</span></p> <p><span style=\"font-family:Palanquin Dark\"> SSC CGL 17/08/21(Morning)</span></p>",
                    options_en: [" <p> Agyeya </span></p>", " <p> Dinkar </span></p>", 
                                " <p> Nirala </span></p>", " <p> Premchand </span></p>"],
                    options_hi: [" <p> अज्ञेय</span></p>", " <p>  दिनकर</span></p>",
                                " <p>  निराला</span></p>", " <p> प्रेमचंद</span></p>"],
                    solution_en: " <p>22.(d) Dhanpat Rai Srivastava is better known as Premchand. Premchand was an Indian writer known for his modern Hindustani literature. Some of his works include Godaan, Karambhoomi, Gaban, Mansarovar, Idgah.</span></p>",
                    solution_hi: " <p>22.(d) धनपत राय श्रीवास्तव को प्रेमचंद के नाम से जाना जाता है। प्रेमचंद एक भारतीय लेखक थे जो अपने आधुनिक हिंदुस्तानी साहित्य के लिए जाने जाते थे। उनके कुछ कार्यों में गोदान, कर्मभूमि, गबान, मानसरोवर, ईदगाह शामिल हैं।</span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "23",
                    section: "18",
                    question_en: " <p>23. Which of the following countries will host the 2022 FIFA World Cup? </span></p> <p><span style=\"font-family:Palanquin Dark\">SSC CGL 17/08/21(Morning)</span></p>",
                    question_hi: " <p>23. निम्नलिखित में से कौन सा देश 2022 फीफा विश्व कप की मेजबानी करेगा?</span></p> <p><span style=\"font-family:Palanquin Dark\"> SSC CGL 17/08/21(Morning)</span></p>",
                    options_en: [" <p> Brazil </span></p>", " <p> Qatar </span></p>", 
                                " <p> Russia </span></p>", " <p> The US </span></p>"],
                    options_hi: [" <p> ब्राजील</span></p>", " <p> कतर</span></p>",
                                " <p> रूस</span></p>", " <p> भारत</span></p>"],
                    solution_en: " <p>23.(b) Qatar will host the 2022 FIFA World Cup which is scheduled to be held from 21 November to 18 December 2022. This is the first world cup to be held in the Arab world.</span></p>",
                    solution_hi: " <p>23.(b) कतर 2022 फीफा विश्व कप की मेजबानी करेगा जो 21 नवंबर से 18 दिसंबर 2022 तक आयोजित होने वाला है। यह अरब दुनिया में आयोजित होने वाला पहला विश्व कप है।</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "24",
                    section: "18",
                    question_en: " <p>24. In January 2021, who among the following was appointed as the President of the Asian Cricket Council? </span></p> <p><span style=\"font-family:Palanquin Dark\">SSC CGL 17/08/21(Morning)</span></p>",
                    question_hi: " <p>24. जनवरी 2021 में, निम्नलिखित में से किसे एशियाई क्रिकेट परिषद के अध्यक्ष के रूप में नियुक्त किया गया था?</span></p> <p><span style=\"font-family:Palanquin Dark\"> SSC CGL 17/08/21(Morning) </span></p>",
                    options_en: [" <p> Sourav Ganguly </span></p>", " <p> Rahul Dravid </span></p>", 
                                " <p> Jay Shah </span></p>", " <p> Anil Kumble </span></p>"],
                    options_hi: [" <p> सौरव गांगुली</span></p>", " <p> राहुल द्रविड़</span></p>",
                                " <p> जय शाह</span></p>", " <p> अनिल कुंबले</span></p>"],
                    solution_en: " <p>24.(c) Jay Shah was appointed as the President of the Asian Cricket Council. Jay Shah is an Indian businessman and the son of Amit Shah, current Home Minister of India.</span></p>",
                    solution_hi: " <p>24.(c) जय शाह को एशियाई क्रिकेट परिषद का अध्यक्ष नियुक्त किया गया। जय शाह एक भारतीय व्यवसायी हैं और भारत के वर्तमान गृह मंत्री अमित शाह के पुत्र हैं।</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "25",
                    section: "misc",
                    question_en: " <p>25. In October 2020, the world\'s longest highway tunnel was inaugurated in the state of ______. </span></p> <p><span style=\"font-family:Palanquin Dark\">SSC CGL 17/08/21(Morning)</span></p>",
                    question_hi: " <p>25. अक्टूबर 2020 में, ________ राज्य में दुनिया की सबसे लंबी राजमार्ग सुरंग का उद्घाटन किया गया। </span></p> <p><span style=\"font-family:Palanquin Dark\">SSC CGL 17/08/21(Morning)</span></p>",
                    options_en: [" <p> Uttar Pradesh </span></p>", " <p> Arunachal Pradesh </span></p>", 
                                " <p> Himachal Pradesh </span></p>", " <p> Madhya Pradesh</span></p>"],
                    options_hi: [" <p> उत्तर प्रदेश</span></p>", " <p> अरुणाचल प्रदेश</span></p>",
                                " <p> हिमाचल प्रदेश</span></p>", " <p> मध्य प्रदेश</span></p>"],
                    solution_en: " <p>25.(c)  In October 2020, the world\'s longest highway tunnel was inaugurated in the state of Himachal Pradesh with a length of 9.02 km. This tunnel, also known as Atal Tunnel, is a highway tunnel built under Rohtang Pass in the eastern Pir panjal range of the Himalayas on the Leh-Manali highway.</span></p>",
                    solution_hi: " <p>25(c) अक्टूबर 2020 में, हिमाचल प्रदेश राज्य में 9.02 km की लंबाई के साथ दुनिया की सबसे लंबी राजमार्ग सुरंग का उद्घाटन किया गया था। यह सुरंग, जिसे अटल सुरंग के नाम से भी जाना जाता है, लेह-मनाली राजमार्ग पर हिमालय के पूर्वी पीर पंजाल रेंज में रोहतांग दर्रे के नीचे बनी एक राजमार्ग सुरंग है।</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML - more compact
            let html = `
                <div class="card mb-3">
                    <div class="card-body">
                        <div class="question-text mb-3">
                            <div class="en">${question.question_en}</div>
                            <div class="hi" style="display:none">${question.question_hi}</div>
                        </div>
                        <div class="options">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = 'option-label';
                // When reviewing, highlight user's choice and correct answer
                if (submitted) {
                    if (opt === question.correct) {
                        optionClass += ' border-success bg-success bg-opacity-10';
                    }
                    if (answers[question.id] === opt && opt !== question.correct) {
                        optionClass += ' border-danger bg-danger bg-opacity-10';
                    }
                }
                html += `
                    <div class="form-check mb-2">
                        <input class="form-check-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${answers[question.id] === opt ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                        <label class="form-check-label w-100 ${optionClass}" for="opt-${index}-${opt}">
                            ${submitted && opt === question.correct ? '<i class="fas fa-check-circle text-success me-1"></i>' : ''}
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const alertClass = isCorrect ? 'alert-success' : 'alert-danger';
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="alert ${alertClass} mt-3 py-2">
                        <strong>${resultText}</strong>
                        <div class="mt-1 small">
                            <strong>Solution:</strong>
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                document.getElementById(`box-${index}`).classList.add('attempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { if (currentQuestion > 0) showQuestion(currentQuestion - 1); }
        function nextQuestion() { if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
            // Update button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const content = document.getElementById('main-content');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                if (window.innerWidth >= 992) {
                    content.classList.add('with-nav');
                }
            } else {
                nav.classList.remove('show');
                if (window.innerWidth >= 992) {
                    content.classList.remove('with-nav');
                }
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer - compact
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            if (confirm(`You have attempted ${attempted} of ${questions.length} questions.
${remaining} questions are unattempted.
Are you sure you want to submit?`)) {
                submitTest();
            }
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('bg-secondary', 'text-white');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            resultsModal.show();
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.classList.replace('btn-light', 'btn-success');
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            resultsModal.hide();
            showQuestion(0);
        }
        // Add event listener for window resize
        window.addEventListener('resize', function() {
            if (window.innerWidth >= 992) {
                if (document.getElementById('question-nav').classList.contains('show')) {
                    document.getElementById('main-content').classList.add('with-nav');
                }
            } else {
                document.getElementById('main-content').classList.remove('with-nav');
            }
        });
    </script>
</body>
</html>