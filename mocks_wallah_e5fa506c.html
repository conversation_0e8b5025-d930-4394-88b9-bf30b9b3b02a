<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">15:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 15</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">15</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 15 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 0,
                end: 13
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 14,
                end: 14
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">1. </span><span style=\"font-family: Cambria Math;\">What is the circumference of a circle whose radius is 10.5 cm ? </span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">1. </span><span style=\"font-family: Kokila;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2357;&#2371;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2340;&#2381;&#2352;&#2367;&#2332;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> 10.5 cm </span><span style=\"font-family: Kokila;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2313;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2357;&#2371;&#2340;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2352;&#2367;&#2343;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> ?</span></p>\n",
                    options_en: ["<p>21 cm</p>\n", "<p>66 cm</p>\n", 
                                "<p>30 cm</p>\n", "<p>110 cm</p>\n"],
                    options_hi: ["<p>21 cm</p>\n", "<p>66 cm</p>\n",
                                "<p>30 cm</p>\n", "<p>110 cm</p>\n"],
                    solution_en: "<p>1.(b)</p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Formula </strong>:- </span><span style=\"font-family: Cambria Math;\">Circumference of circle = 2&pi;</span><span style=\"font-family: Cambria Math;\">r </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">= 2 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>22</mn><mn>7</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\">&times; 10.5 = 66 cm</span></p>\n",
                    solution_hi: "<p>1.(b)</p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>&#2360;&#2370;&#2340;&#2381;&#2352; :- </strong><span style=\"font-weight: 400;\">&nbsp;&#2357;&#2371;&#2340;&#2381;&#2340; &#2325;&#2368; </span><span style=\"font-weight: 400;\">&#2346;&#2352;&#2367;&#2343;&#2367;</span> = 2&pi;</span><span style=\"font-family: Cambria Math;\">r </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">= 2 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>22</mn><mn>7</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\">&times; 10.5 = 66 cm</span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">2. </span><span style=\"font-family: Cambria Math;\">In a cyclic quadrilateral MNOP, &ang;M is opposite to &ang;O, If 2&ang;O = 3&ang;M, then what is the value of &ang;M ?</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">2. </span><span style=\"font-family: Kokila;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2330;&#2325;&#2381;&#2352;&#2368;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2330;&#2340;&#2369;&#2352;&#2381;&#2349;&#2369;&#2332;</span><span style=\"font-family: Cambria Math;\"> MNOP </span><span style=\"font-family: Kokila;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\">, &ang;M, &ang;O </span><span style=\"font-family: Kokila;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2350;&#2381;&#2350;&#2369;&#2326;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Kokila;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> 2&ang;O = 3&ang;M </span><span style=\"font-family: Kokila;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Kokila;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> &ang;M </span><span style=\"font-family: Kokila;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> ?</span></p>\n",
                    options_en: ["<p>88 degree</p>\n", "<p>108 degree</p>\n", 
                                "<p>72 degree</p>\n", "<p>96 degree</p>\n"],
                    options_hi: ["<p>88 <span style=\"font-family: Kokila;\">&#2337;&#2367;&#2327;&#2381;&#2352;&#2368;</span></p>\n", "<p>108 <span style=\"font-family: Kokila;\">&#2337;&#2367;&#2327;&#2381;&#2352;&#2368;</span></p>\n",
                                "<p>72 <span style=\"font-family: Kokila;\">&#2337;&#2367;&#2327;&#2381;&#2352;&#2368;</span></p>\n", "<p>96 <span style=\"font-family: Kokila;\">&#2337;&#2367;&#2327;&#2381;&#2352;&#2368;</span></p>\n"],
                    solution_en: "<p>2.(c)</p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1703747517/word/media/image1.png\" width=\"111\" height=\"90\"></p>\r\n<p><span style=\"font-family: Cambria Math;\">Theorem: </span><span style=\"font-family: Cambria Math;\"> In a cyclic quadrilateral sum of opposite angle = 180&deg;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">According to question;</span></p>\r\n<p><span style=\"font-weight: 400;\">2</span><span style=\"font-weight: 400;\">x</span><span style=\"font-weight: 400;\"> + 3</span><span style=\"font-weight: 400;\">x</span><span style=\"font-weight: 400;\"> = 180&deg;</span></p>\r\n<p><span style=\"font-weight: 400;\">x</span><span style=\"font-weight: 400;\"> = 36&deg;</span></p>\r\n<p><span style=\"font-weight: 400;\">&rArr;&ang;M = 2</span><span style=\"font-weight: 400;\">x</span><span style=\"font-weight: 400;\"> = 2&times; 36 = 72&deg;</span></p>\n",
                    solution_hi: "<p>2.(c)</p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1703747517/word/media/image1.png\" width=\"147\" height=\"119\"></p>\r\n<p><span style=\"font-family: Kokila;\">&#2346;&#2381;&#2352;&#2350;&#2375;&#2351;</span><span style=\"font-family: Cambria Math;\">: </span><span style=\"font-family: Kokila;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2330;&#2325;&#2381;&#2352;&#2368;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2330;&#2340;&#2369;&#2352;&#2381;&#2349;&#2369;&#2332;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2350;&#2381;&#2350;&#2369;&#2326;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2379;&#2339;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2351;&#2379;&#2327;</span><span style=\"font-family: Cambria Math;\"> = 180&deg;</span></p>\r\n<p><span style=\"font-family: Kokila;\">&#2346;&#2381;&#2352;&#2358;&#2381;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2309;&#2344;&#2369;&#2360;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\">;</span></p>\r\n<p><span style=\"font-weight: 400;\">2</span><span style=\"font-weight: 400;\">x</span><span style=\"font-weight: 400;\"> + 3</span><span style=\"font-weight: 400;\">x</span><span style=\"font-weight: 400;\"> = 180&deg;</span></p>\r\n<p><span style=\"font-weight: 400;\">x</span><span style=\"font-weight: 400;\"> = 36&deg;</span></p>\r\n<p><span style=\"font-weight: 400;\">&rArr;&ang;M = 2</span><span style=\"font-weight: 400;\">x</span><span style=\"font-weight: 400;\"> = 2&times; 36 = 72&deg;</span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">3. </span><span style=\"font-family: Cambria Math;\">AC is the diameter of the circle. B is a point on the circumference. If angle BAC is 40&deg;, then what is angle BCA ?</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">3. </span><span style=\"font-family: Kokila;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2357;&#2371;&#2340;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2357;&#2381;&#2351;&#2366;&#2360;</span><span style=\"font-family: Cambria Math;\"> AC </span><span style=\"font-family: Kokila;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> B </span><span style=\"font-family: Kokila;\">&#2313;&#2360;&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2352;&#2367;&#2343;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2348;&#2367;&#2306;&#2342;&#2369;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2379;&#2339;</span><span style=\"font-family: Cambria Math;\"> BAC </span><span style=\"font-family: Kokila;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> 40&deg; </span><span style=\"font-family: Kokila;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Kokila;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2379;&#2339;</span><span style=\"font-family: Cambria Math;\"> BCA </span><span style=\"font-family: Kokila;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>50&deg;</p>\n", "<p>90&deg;</p>\n", 
                                "<p>55&deg;</p>\n", "<p>40&deg;</p>\n"],
                    options_hi: ["<p>50&deg;</p>\n", "<p>90&deg;</p>\n",
                                "<p>55&deg;</p>\n", "<p>40&deg;</p>\n"],
                    solution_en: "<p>3.(a)</p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1703747517/word/media/image2.png\" width=\"173\" height=\"143\"></p>\r\n<p><span style=\"font-family: Cambria Math;\">The angle subtended by the diameter of a circle at the circumference is (90&deg;).</span></p>\r\n<p><span style=\"font-weight: 400;\">&ang;BCA = 180&deg; </span><span style=\"font-weight: 400;\">-</span><span style=\"font-weight: 400;\"> (&ang;ABC + &ang;BAC)&nbsp;</span></p>\r\n<p><span style=\"font-weight: 400;\">&ang;BCA = 180&deg; </span><span style=\"font-weight: 400;\">-</span><span style=\"font-weight: 400;\"> (90&deg; + 40&deg;)</span></p>\r\n<p><span style=\"font-weight: 400;\">&ang;BCA= 50&deg;</span></p>\n",
                    solution_hi: "<p>3.(a)</p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1703747517/word/media/image2.png\" width=\"163\" height=\"134\"></p>\r\n<p><span style=\"font-family: Kokila;\">&#2357;&#2371;&#2340;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2357;&#2381;&#2351;&#2366;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2342;&#2381;&#2357;&#2366;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2352;&#2367;&#2343;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2348;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2379;&#2339;</span><span style=\"font-family: Cambria Math;\"> (90&deg;) </span><span style=\"font-family: Kokila;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2379;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><span style=\"font-weight: 400;\">&ang;BCA = 180&deg; </span><span style=\"font-weight: 400;\">-</span><span style=\"font-weight: 400;\"> (&ang;ABC + &ang;BAC)&nbsp;</span></p>\r\n<p><span style=\"font-weight: 400;\">&ang;BCA = 180&deg; </span><span style=\"font-weight: 400;\">-</span><span style=\"font-weight: 400;\"> (90&deg; + 40&deg;)</span></p>\r\n<p><span style=\"font-weight: 400;\">&ang;BCA= 50&deg;</span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">4. </span><span style=\"font-family: Cambria Math;\">I is the incentre of the &Delta;</span><span style=\"font-family: Cambria Math;\">XYZ. If &ang;YIZ = 115 degree, then what is the &ang;YXZ?</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">4.</span><span style=\"font-family: Cambria Math;\"> <span style=\"font-weight: 400;\">&Delta;</span></span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">XYZ </span><span style=\"font-family: Kokila;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2309;&#2306;&#2340;&#2307;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2375;&#2306;&#2342;&#2381;&#2352;</span><span style=\"font-family: Cambria Math;\"> &lsquo; I &lsquo; </span><span style=\"font-family: Kokila;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> &ang;YIZ = 115 </span><span style=\"font-family: Kokila;\">&#2337;&#2367;&#2327;&#2381;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Kokila;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> &ang;YXZ </span><span style=\"font-family: Kokila;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>55 degree</p>\n", "<p>45 degree</p>\n", 
                                "<p>50 degree</p>\n", "<p>60 degree</p>\n"],
                    options_hi: ["<p>55 <span style=\"font-family: Kokila;\">&#2337;&#2367;&#2327;&#2381;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p>45 <span style=\"font-family: Kokila;\">&#2337;&#2367;&#2327;&#2381;&#2352;&#2368;</span></p>\n",
                                "<p>50 <span style=\"font-family: Kokila;\">&#2337;&#2367;&#2327;&#2381;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p>60 <span style=\"font-family: Kokila;\">&#2337;&#2367;&#2327;&#2381;&#2352;&#2368;</span></p>\n"],
                    solution_en: "<p>4.(c)</p>\r\n<p><span style=\"font-family: Cambria Math;\">Incenter of &Delta;</span><span style=\"font-family: Cambria Math;\"> = 90&deg; +&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>x</mi><mn>2</mn></mfrac></math></span></p>\r\n<p><span style=\"font-family: Cambria Math;\">115&deg; = 90&deg; +&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>x</mi><mn>2</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\">&nbsp;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">x =</span><span style=\"font-family: Cambria Math;\">50&deg;</span></p>\n",
                    solution_hi: "<p>4.(c)</p>\r\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\"> </span><span style=\"font-weight: 400;\">&nbsp;&Delta;&#2325;&#2366;&nbsp; &#2309;&#2306;&#2340;&#2307; &#2325;&#2375;&#2306;&#2342;&#2381;&#2352;&nbsp;</span> = 90&deg; + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>x</mi><mn>2</mn></mfrac></math></span></p>\r\n<p><span style=\"font-family: Cambria Math;\">115&deg; = 90&deg; +&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>x</mi><mn>2</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\">&nbsp;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">x =</span><span style=\"font-family: Cambria Math;\">50&deg;</span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">5.</span><span style=\"font-family: Cambria Math;\"> Radii of two circles are 20 cm and 4 cm. Length of direct common tangent is 30 cm. What is the distance between their centres?</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">5.</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2342;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2348;&#2371;&#2340;&#2381;&#2340;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2340;&#2381;&#2352;&#2367;&#2332;&#2381;&#2351;&#2366;&#2319;&#2305;</span><span style=\"font-family: Cambria Math;\"> 20 cm </span><span style=\"font-family: Kokila;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> 4 cm </span><span style=\"font-family: Kokila;\">&#2361;&#2376;&#2306;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2368;&#2343;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2313;&#2349;&#2351;&#2344;&#2367;&#2359;&#2381;&#2336;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2381;&#2346;&#2352;&#2381;&#2358;&#2352;&#2375;&#2326;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2354;&#2306;&#2348;&#2366;&#2312;</span><span style=\"font-family: Cambria Math;\"> 30 cm </span><span style=\"font-family: Kokila;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2313;&#2344;&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2375;&#2306;&#2342;&#2381;&#2352;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2348;&#2368;&#2330;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2342;&#2370;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2367;&#2340;&#2344;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>36 cm</p>\n", "<p>38 cm</p>\n", 
                                "<p>34 cm</p>\n", "<p>32 cm</p>\n"],
                    options_hi: ["<p>36 cm</p>\n", "<p>38 cm</p>\n",
                                "<p>34 cm</p>\n", "<p>32 cm</p>\n"],
                    solution_en: "<p>5.(c)</p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1703747517/word/media/image3.png\" width=\"176\" height=\"111\"></p>\r\n<p><span style=\"font-family: Cambria Math;\">Let AB be the direct common tangent </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Then, AB =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mrow><mo>(</mo><mi>d</mi><mi>i</mi><mi>s</mi><mi>tan</mi><mi>c</mi><mi>e</mi><mo>&nbsp;</mo><mi>b</mi><mi>e</mi><mi>t</mi><mi>w</mi><mi>e</mi><mi>e</mi><mi>n</mi><mo>&nbsp;</mo><mi>t</mi><mi>h</mi><mi>e</mi><mo>&nbsp;</mo><mo>&nbsp;</mo><mi>c</mi><mi>e</mi><mi>n</mi><mi>t</mi><mi>r</mi><mi>e</mi><mi>s</mi><mo>)</mo></mrow><mn>2</mn></msup><mo>-</mo><mi>d</mi><mi>i</mi><mi>f</mi><mi>f</mi><mi>e</mi><mi>r</mi><mi>e</mi><mi>n</mi><mi>c</mi><mi>e</mi><mo>&nbsp;</mo><mi>b</mi><mi>e</mi><mi>t</mi><mi>w</mi><mi>e</mi><mi>e</mi><mi>n</mi><mo>&nbsp;</mo><mi>r</mi><mi>a</mi><mi>d</mi><mi>i</mi><mi>u</mi><mi>s</mi><mo>&nbsp;</mo><mi>o</mi><mi>f</mi><mo>&nbsp;</mo><mi>b</mi><mi>o</mi><mi>t</mi><mi>h</mi><mo>&nbsp;</mo><mi>c</mi><mi>i</mi><mi>r</mi><mi>c</mi><mi>l</mi><msup><mi>e</mi><mn>2</mn></msup></msqrt></math></span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> = <span style=\"font-weight: 400;\">(30)&sup2;</span><span style=\"font-weight: 400;\">&nbsp;= </span><span style=\"font-weight: 400;\">(OO\')&sup2; </span><span style=\"font-weight: 400;\">- </span><span style=\"font-weight: 400;\">(20 - 4 )&sup2; </span></span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">&rArr; </span><span style=\"font-weight: 400;\">(OO\')&sup2;</span><span style=\"font-weight: 400;\">&nbsp;= 1156 </span></span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">&rArr;(</span><span style=\"font-weight: 400;\">OO\'</span><span style=\"font-weight: 400;\">) </span></span><span style=\"font-family: Cambria Math;\">= 34 cm</span></p>\n",
                    solution_hi: "<p>5.(c)</p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1703747517/word/media/image3.png\" width=\"181\" height=\"114\"></p>\r\n<p><span style=\"font-family: Kokila;\">&#2350;&#2366;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> AB </span><span style=\"font-family: Kokila;\">&#2360;&#2368;&#2343;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2313;&#2349;&#2351;&#2344;&#2367;&#2359;&#2381;&#2336;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2381;&#2346;&#2352;&#2381;&#2358;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2352;&#2375;&#2326;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Then, AB =&nbsp;<math style=\"font-family:Verdana\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mrow><mo>(</mo><mi>&#2325;&#2375;&#2306;&#2342;&#2381;&#2352;&#2379;&#2306;</mi><mo>&nbsp;</mo><mi>&#2325;&#2375;</mi><mo>&nbsp;</mo><mi>&#2348;&#2368;&#2330;</mi><mo>&nbsp;</mo><mi>&#2325;&#2368;</mi><mo>&nbsp;</mo><mi>&#2342;&#2370;&#2352;&#2368;</mi><mo>)</mo></mrow><mn>2</mn></msup><mo>-</mo><mi>&#2342;&#2379;&#2344;&#2379;&#2306;</mi><mo>&nbsp;</mo><mi>&#2357;&#2371;&#2340;&#2381;&#2340;&#2379;&#2306;</mi><mo>&nbsp;</mo><mi>&#2325;&#2368;</mi><mo>&nbsp;</mo><mi>&#2340;&#2381;&#2352;&#2367;&#2332;&#2381;&#2351;&#2366;&#2323;&#2306;</mi><mo>&nbsp;</mo><mi>&#2325;&#2375;</mi><mo>&nbsp;</mo><mi>&#2348;&#2368;&#2330;</mi><mo>&nbsp;</mo><mi>&#2325;&#2366;</mi><mo>&nbsp;</mo><msup><mi>&#2309;&#2306;&#2340;&#2352;</mi><mn>2</mn></msup></msqrt></math></span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> = <span style=\"font-weight: 400;\">(30)&sup2;</span><span style=\"font-weight: 400;\">&nbsp;= </span><span style=\"font-weight: 400;\">(OO\')&sup2; </span><span style=\"font-weight: 400;\">- </span><span style=\"font-weight: 400;\">(20 - 4 )&sup2; </span></span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">&rArr; </span><span style=\"font-weight: 400;\">(OO\')&sup2;</span><span style=\"font-weight: 400;\">&nbsp;= 1156 </span></span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">&rArr;(</span><span style=\"font-weight: 400;\">OO\'</span><span style=\"font-weight: 400;\">) </span></span><span style=\"font-family: Cambria Math;\">= 34 cm</span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">6.</span><span style=\"font-family: Cambria Math;\"> In &Delta;</span><span style=\"font-family: Cambria Math;\">PQR, PN is the median on QR. If PN=QN, then what is the value of</span><span style=\"font-family: Cambria Math;\"> <span style=\"font-weight: 400;\">&ang;</span>QPR ?</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">6.</span><span style=\"font-family: Cambria Math;\"> &Delta;</span><span style=\"font-family: Cambria Math;\">PQR </span><span style=\"font-family: Kokila;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\">, PN, QR </span><span style=\"font-family: Kokila;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2366;&#2343;&#2381;&#2351;&#2367;&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> PN =QN </span><span style=\"font-family: Kokila;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Kokila;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> <span style=\"font-weight: 400;\">&ang;</span></span><span style=\"font-family: Cambria Math;\"> QPR </span><span style=\"font-family: Kokila;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>90 degree</p>\n", "<p>80 degree</p>\n", 
                                "<p>60 degree</p>\n", "<p>75 degree</p>\n"],
                    options_hi: ["<p>90 <span style=\"font-family: Kokila;\">&#2337;&#2367;&#2327;&#2381;&#2352;&#2368;</span></p>\n", "<p>80 <span style=\"font-family: Kokila;\">&#2337;&#2367;&#2327;&#2381;&#2352;&#2368;</span></p>\n",
                                "<p>60<span style=\"font-family: Kokila;\">&#2337;&#2367;&#2327;&#2381;&#2352;&#2368;</span></p>\n", "<p>75 <span style=\"font-family: Kokila;\">&#2337;&#2367;&#2327;&#2381;&#2352;&#2368;</span></p>\n"],
                    solution_en: "<p>6.(a)</p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1703747517/word/media/image4.png\" width=\"170\" height=\"151\"></p>\r\n<p><span style=\"font-family: Cambria Math;\">According to the question,</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">In the &Delta;</span><span style=\"font-family: Cambria Math;\">PQR </span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">&ang;</span><span style=\"font-weight: 400;\"> P + </span><span style=\"font-weight: 400;\">&ang;</span><span style=\"font-weight: 400;\"> Q + </span><span style=\"font-weight: 400;\">&ang;</span><span style=\"font-weight: 400;\"> R</span> = 180&deg; </span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">&rArr; (&alpha; + &beta;)+ &beta;&nbsp;</span><span style=\"font-weight: 400;\"> + &alpha; </span><span style=\"font-weight: 400;\">&nbsp;</span>= 180&deg; </span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">2(&alpha; + &beta;)</span><span style=\"font-weight: 400;\">&nbsp; = 180&deg; &nbsp; </span></span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">(&alpha; + &beta;)</span>&nbsp;</span><span style=\"font-family: Cambria Math;\">= 90&deg;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Then, <span style=\"font-weight: 400;\">&ang;</span></span><span style=\"font-family: Cambria Math;\"> QPR </span><span style=\"font-family: Cambria Math;\">= 90&deg;</span></p>\n",
                    solution_hi: "<p>6.(a)</p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1703747517/word/media/image4.png\" width=\"101\" height=\"90\"></p>\r\n<p><span style=\"font-family: Kokila;\">&#2346;&#2381;&#2352;&#2358;&#2381;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2309;&#2344;&#2369;&#2360;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\">,</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&Delta;PQR </span><span style=\"font-family: Kokila;\">&#2350;&#2375;&#2306;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">&ang;</span><span style=\"font-weight: 400;\"> P + </span><span style=\"font-weight: 400;\">&ang;</span><span style=\"font-weight: 400;\"> Q + </span><span style=\"font-weight: 400;\">&ang;</span><span style=\"font-weight: 400;\"> R</span> = 180&deg; </span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">&rArr; (&alpha; + &beta;)+ &beta;&nbsp;</span><span style=\"font-weight: 400;\"> + &alpha; </span><span style=\"font-weight: 400;\">&nbsp;</span>= 180&deg; </span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">2(&alpha; + &beta;)</span><span style=\"font-weight: 400;\">&nbsp; = 180&deg; &nbsp; </span></span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">(&alpha; + &beta;)</span>&nbsp;</span><span style=\"font-family: Cambria Math;\">= 90&deg;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">&#2340;&#2348;</span>, <span style=\"font-weight: 400;\">&ang;</span></span><span style=\"font-family: Cambria Math;\"> QPR </span><span style=\"font-family: Cambria Math;\">= 90&deg;</span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">7.</span><span style=\"font-family: Cambria Math;\">L is the incentre of &Delta;</span><span style=\"font-family: Cambria Math;\">ONP. If <span style=\"font-weight: 400;\">&ang;</span><span style=\"font-weight: 400;\">OLP +&nbsp; &ang;ONP</span></span><span style=\"font-family: Cambria Math;\">&nbsp;= 195 degree, then what will be the value of &ang;OLP ?</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">7. &Delta;</span><span style=\"font-family: Cambria Math;\">ONP </span><span style=\"font-family: Kokila;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2309;&#2306;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2375;&#2306;&#2342;&#2381;&#2352;</span><span style=\"font-family: Cambria Math;\"> L </span><span style=\"font-family: Kokila;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> <span style=\"font-weight: 400;\">&ang;</span><span style=\"font-weight: 400;\">OLP + &ang;ONP</span></span><span style=\"font-family: Cambria Math;\">&nbsp;= 195 </span><span style=\"font-family: Kokila;\">&#2337;&#2367;&#2327;&#2381;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Kokila;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> &ang;OLP </span><span style=\"font-family: Kokila;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2379;&#2327;&#2366;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>115 degree</p>\n", "<p>140 degree</p>\n", 
                                "<p>135 degree</p>\n", "<p>125 degree</p>\n"],
                    options_hi: ["<p>115 <span style=\"font-family: Kokila;\">&#2337;&#2367;&#2327;&#2381;&#2352;&#2368;</span></p>\n", "<p>140 <span style=\"font-family: Kokila;\">&#2337;&#2367;&#2327;&#2381;&#2352;&#2368;</span></p>\n",
                                "<p>135 <span style=\"font-family: Kokila;\">&#2337;&#2367;&#2327;&#2381;&#2352;&#2368;</span></p>\n", "<p>125 <span style=\"font-family: Kokila;\">&#2337;&#2367;&#2327;&#2381;&#2352;&#2368;</span></p>\n"],
                    solution_en: "<p>7.(d)</p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1703747517/word/media/image5.png\" width=\"132\" height=\"133\"></p>\r\n<p><span style=\"font-family: Cambria Math;\">L is the incentre of &Delta;</span><span style=\"font-family: Cambria Math;\">ONP </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">if <span style=\"font-weight: 400;\">&ang;</span></span><span style=\"font-family: Cambria Math;\">ONP = &theta;</span><span style=\"font-family: Cambria Math;\"> Then, <span style=\"font-weight: 400;\">&ang; </span></span><span style=\"font-family: Cambria Math;\">OLP = <span style=\"font-weight: 400;\">90 +&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>&theta;</mi><mn>2</mn></mfrac></math></span></span></p>\r\n<p><span style=\"font-family: Cambria Math;\">According to the question,</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&nbsp;<span style=\"font-weight: 400;\">90 + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&theta;</mi><mo>&nbsp;</mo></mrow><mn>2</mn></mfrac></math></span>+ &theta;</span><span style=\"font-family: Cambria Math;\"> = 195&deg; </span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&nbsp;&theta; = 70&deg;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Then, <span style=\"font-weight: 400;\">&ang;</span></span><span style=\"font-family: Cambria Math;\">OLP = 90 +<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>&theta;</mi><mn>2</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">= 90 + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>70</mn><mn>2</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> =</span><span style=\"font-family: Cambria Math;\">125&deg;</span></p>\n",
                    solution_hi: "<p>7.(d)</p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1703747517/word/media/image5.png\" width=\"132\" height=\"133\"></p>\r\n<p><span style=\"font-family: Cambria Math;\">L , &Delta;</span><span style=\"font-family: Cambria Math;\">ONP </span><span style=\"font-weight: 400;\">&#2325;&#2366; &#2309;&#2344;&#2381;&#2340;&#2307; &#2325;&#2375;&#2306;&#2342;&#2381;&#2352; &#2361;&#2376;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&nbsp;<span style=\"font-weight: 400;\">&#2351;&#2342;&#2367; </span></span><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">&ang;</span></span><span style=\"font-family: Cambria Math;\">ONP = &theta;</span><span style=\"font-family: Cambria Math;\"> <span style=\"font-weight: 400;\">&#2340;&#2379; </span>, <span style=\"font-weight: 400;\">&ang; </span></span><span style=\"font-family: Cambria Math;\">OLP = <span style=\"font-weight: 400;\">90 +&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>&theta;</mi><mn>2</mn></mfrac></math></span></span></p>\r\n<p><span style=\"font-weight: 400;\">&#2346;&#2381;&#2352;&#2358;&#2381;&#2344; &#2325;&#2375; &#2309;&#2344;&#2369;&#2360;&#2366;&#2352;,</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&nbsp;<span style=\"font-weight: 400;\">90 + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&theta;</mi><mo>&nbsp;</mo></mrow><mn>2</mn></mfrac></math></span>+ &theta;</span><span style=\"font-family: Cambria Math;\"> = 195&deg; </span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&nbsp;&theta; = 70&deg;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">&#2340;&#2348; </span>, <span style=\"font-weight: 400;\">&ang;</span></span><span style=\"font-family: Cambria Math;\">OLP = 90 +<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>&theta;</mi><mn>2</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">= 90 + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>70</mn><mn>2</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> =</span><span style=\"font-family: Cambria Math;\">125&deg;</span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">8. </span><span style=\"font-family: Cambria Math;\">Ratio of the angles of a triangle are 2 : 2 : 5. What is the largest angle? </span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">98. </span><span style=\"font-family: Kokila;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2340;&#2381;&#2352;&#2367;&#2349;&#2369;&#2332;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2379;&#2339;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2309;&#2344;&#2369;&#2346;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> 2 : 2 : 5 </span><span style=\"font-family: Kokila;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2348;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2348;&#2337;&#2364;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2379;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2367;&#2340;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>120 degree</p>\n", "<p>80 degree</p>\n", 
                                "<p>110 degree</p>\n", "<p>100 degree</p>\n"],
                    options_hi: ["<p>120 <span style=\"font-family: Kokila;\">&#2337;&#2367;&#2327;&#2381;&#2352;&#2368;</span></p>\n", "<p>80 <span style=\"font-family: Kokila;\">&#2337;&#2367;&#2327;&#2381;&#2352;&#2368;</span></p>\n",
                                "<p>110 <span style=\"font-family: Kokila;\">&#2337;&#2367;&#2327;&#2381;&#2352;&#2368;</span></p>\n", "<p>100 <span style=\"font-family: Kokila;\">&#2337;&#2367;&#2327;&#2381;&#2352;&#2368;</span></p>\n"],
                    solution_en: "<p>8.(d)</p>\r\n<p><span style=\"font-family: Cambria Math;\">Let the angle of the triangle = </span><span style=\"font-family: Cambria Math;\">2x , 2x , and 5x</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Then, (2x + 2x + 5x) = 180&deg; &rArr;</span><span style=\"font-family: Cambria Math;\"> x = 20&deg;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Therefore, the largest angle (5x) = 100&deg;</span></p>\n",
                    solution_hi: "<p>8.(d)</p>\r\n<p><span style=\"font-family: Kokila;\">&#2350;&#2366;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2340;&#2381;&#2352;&#2367;&#2349;&#2369;&#2332;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2379;&#2339; &rarr;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">2x, 2x </span><span style=\"font-family: Kokila;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> 5x </span><span style=\"font-family: Kokila;\">&#2361;&#2376;</span></p>\r\n<p><span style=\"font-family: Kokila;\">&#2340;&#2348;</span><span style=\"font-family: Cambria Math;\"> , (2x + 2x + 5x) = 180&deg; &rArr;</span><span style=\"font-family: Cambria Math;\"> x = 20&deg;</span></p>\r\n<p><span style=\"font-family: Kokila;\">&#2311;&#2360;&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Kokila;\">&#2360;&#2348;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2348;&#2337;&#2364;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2379;&#2339;</span><span style=\"font-family: Cambria Math;\"> (5x) = 100&deg;</span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">9.</span><span style=\"font-family: Cambria Math;\"> ABCD is a cyclic quadrilateral in which angle B is opposite to angle D. If &ang;</span><span style=\"font-family: Cambria Math;\">B = x + 10 degree and &ang;</span><span style=\"font-family: Cambria Math;\">D = 2x + 35 degree, then what is the value of x?</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">9.</span><span style=\"font-family: Cambria Math;\"> ABCD </span><span style=\"font-family: Kokila;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2330;&#2325;&#2381;&#2352;&#2368;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2330;&#2340;&#2369;&#2352;&#2381;&#2349;&#2369;&#2332;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2332;&#2367;&#2360;&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2379;&#2339;</span><span style=\"font-family: Cambria Math;\"> B </span><span style=\"font-family: Kokila;\">&#2325;&#2379;&#2339;</span><span style=\"font-family: Cambria Math;\"> D </span><span style=\"font-family: Kokila;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2350;&#2381;&#2350;&#2369;&#2326;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> &ang;</span><span style=\"font-family: Cambria Math;\">B = x + 10 </span><span style=\"font-family: Kokila;\">&#2337;&#2367;&#2327;&#2381;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> &nbsp;&ang;</span><span style=\"font-family: Cambria Math;\">D = 2x + 35 </span><span style=\"font-family: Kokila;\">&#2337;&#2367;&#2327;&#2381;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> x </span><span style=\"font-family: Kokila;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>35 degree</p>\n", "<p>40 degree</p>\n", 
                                "<p>50 degree</p>\n", "<p>45 degree</p>\n"],
                    options_hi: ["<p>35 <span style=\"font-family: Kokila;\">&#2337;&#2367;&#2327;&#2381;&#2352;&#2368;</span></p>\n", "<p>40 <span style=\"font-family: Kokila;\">&#2337;&#2367;&#2327;&#2381;&#2352;&#2368;</span></p>\n",
                                "<p>50 <span style=\"font-family: Kokila;\">&#2337;&#2367;&#2327;&#2381;&#2352;&#2368;</span></p>\n", "<p>45 <span style=\"font-family: Kokila;\">&#2337;&#2367;&#2327;&#2381;&#2352;&#2368;</span></p>\n"],
                    solution_en: "<p>9.(d)</p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1703747517/word/media/image6.png\" width=\"110\" height=\"101\"></p>\r\n<p><span style=\"font-family: Cambria Math;\">In the cyclic quadrilateral sum of the opposite angle is always be 180&deg;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Therefore, &ang;</span><span style=\"font-family: Cambria Math;\">B + &ang;</span><span style=\"font-family: Cambria Math;\">D = 180&deg; </span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> 3x + 45 = 180 </span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> x = 45&deg;</span></p>\n",
                    solution_hi: "<p>9.(d)</p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1703747517/word/media/image6.png\" width=\"128\" height=\"117\"></p>\r\n<p><span style=\"font-family: Kokila;\">&#2330;&#2325;&#2381;&#2352;&#2368;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2330;&#2340;&#2369;&#2352;&#2381;&#2349;&#2369;&#2332;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2350;&#2381;&#2350;&#2369;&#2326;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2379;&#2339;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2351;&#2379;&#2327;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2342;&#2376;&#2357;</span><span style=\"font-family: Cambria Math;\"> 180&deg; </span><span style=\"font-family: Kokila;\">&#2361;&#2379;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;</span></p>\r\n<p><span style=\"font-family: Kokila;\">&#2311;&#2360;&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\">,&nbsp;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&ang;</span><span style=\"font-family: Cambria Math;\">B + &ang;</span><span style=\"font-family: Cambria Math;\">D = 180&deg; </span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> 3x + 45 = 180 </span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> x = 45&deg;</span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">10.</span><span style=\"font-family: Cambria Math;\"> The perimeter of an isosceles triangle is 91 cm. If one of the equal sides measures 28 cm, then what is the valve of the other non-equal side? </span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">10. </span><span style=\"font-family: Kokila;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2350;&#2342;&#2381;&#2357;&#2367;&#2348;&#2366;&#2361;&#2369;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2340;&#2381;&#2352;&#2367;&#2349;&#2369;&#2332;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2352;&#2367;&#2350;&#2366;&#2346;</span><span style=\"font-family: Cambria Math;\"> 91 cm </span><span style=\"font-family: Kokila;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2349;&#2369;&#2332;&#2366;&#2323;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2366;&#2346;</span><span style=\"font-family: Cambria Math;\"> 28 cm </span><span style=\"font-family: Kokila;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Kokila;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2309;&#2360;&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2349;&#2369;&#2332;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>56 cm</p>\n", "<p>42 cm</p>\n", 
                                "<p>14 cm</p>\n", "<p>35 cm</p>\n"],
                    options_hi: ["<p>56 cm</p>\n", "<p>42 cm</p>\n",
                                "<p>14 cm</p>\n", "<p>35 cm</p>\n"],
                    solution_en: "<p>10.(d)</p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1703747517/word/media/image7.png\" width=\"150\" height=\"116\"></p>\r\n<p><span style=\"font-family: Cambria Math;\">According to the question,</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Value of the non - equal side = 91 - </span><span style=\"font-family: Cambria Math;\">(28 + 28 ) = 35 cm</span></p>\n",
                    solution_hi: "<p>10.(d)</p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1703747517/word/media/image7.png\" width=\"144\" height=\"111\"></p>\r\n<p><span style=\"font-family: Kokila;\">&#2346;&#2381;&#2352;&#2358;&#2381;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2309;&#2344;&#2369;&#2360;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\">,</span></p>\r\n<p><span style=\"font-family: Kokila;\">&#2309;&#2360;&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2349;&#2369;&#2332;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> = 91 - (28 + 28 ) = 35 </span><span style=\"font-family: Kokila;\">&#2360;&#2375;&#2350;&#2368;</span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">11.</span><span style=\"font-family: Cambria Math;\"> In &Delta;</span><span style=\"font-family: Cambria Math;\">XYZ, I is the incentre of the&Delta; </span><span style=\"font-family: Cambria Math;\">XYZ. If &ang;</span><span style=\"font-family: Cambria Math;\">XYZ = 40 degree. then what is the value of&nbsp; </span><span style=\"font-family: Cambria Math;\">&ang;XIZ? </span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">11. &Delta;</span><span style=\"font-family: Cambria Math;\">XYZ </span><span style=\"font-family: Kokila;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\">, I, &Delta;</span><span style=\"font-family: Cambria Math;\">XYZ </span><span style=\"font-family: Kokila;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2309;&#2306;&#2340;</span><span style=\"font-family: Cambria Math;\">: </span><span style=\"font-family: Kokila;\">&#2325;&#2375;&#2306;&#2342;&#2381;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> &ang;</span><span style=\"font-family: Cambria Math;\">XYZ = 40 </span><span style=\"font-family: Kokila;\">&#2337;&#2367;&#2327;&#2381;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Kokila;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> &ang;</span><span style=\"font-family: Cambria Math;\">XIZ </span><span style=\"font-family: Kokila;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">? </span></p>\n",
                    options_en: ["<p>115 degree</p>\n", "<p>110 degree</p>\n", 
                                "<p>120 degree</p>\n", "<p>130 degree</p>\n"],
                    options_hi: ["<p>115 <span style=\"font-family: Kokila;\">&#2337;&#2367;&#2327;&#2381;&#2352;&#2368;</span></p>\n", "<p>110 <span style=\"font-family: Kokila;\">&#2337;&#2367;&#2327;&#2381;&#2352;&#2368;</span></p>\n",
                                "<p>120 <span style=\"font-family: Kokila;\">&#2337;&#2367;&#2327;&#2381;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p>130 <span style=\"font-family: Kokila;\">&#2337;&#2367;&#2327;&#2381;&#2352;&#2368;</span></p>\n"],
                    solution_en: "<p>11.(b)</p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1703747517/word/media/image8.png\" width=\"155\" height=\"114\"></p>\r\n<p><span style=\"font-family: Cambria Math;\">I is the incentre of the &Delta;</span><span style=\"font-family: Cambria Math;\">XYZ</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">therefore , &ang;</span><span style=\"font-family: Cambria Math;\">XIZ = 90 + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>40</mn><mn>2</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">= 110&deg;</span></p>\n",
                    solution_hi: "<p>11.(b)</p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1703747517/word/media/image8.png\" width=\"170\" height=\"125\"></p>\r\n<p><span style=\"font-family: Cambria Math;\">&nbsp;&Delta;XYZ </span><span style=\"font-family: Kokila;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2309;&#2344;&#2381;&#2340;&#2307;&#2325;&#2375;&#2306;&#2342;&#2381;&#2352;</span><span style=\"font-family: Cambria Math;\"> I </span><span style=\"font-family: Kokila;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">&#2311;&#2360;&#2354;&#2367;&#2319;</span><span style=\"font-weight: 400;\"> </span>, &ang;</span><span style=\"font-family: Cambria Math;\">XIZ = 90 + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>40</mn><mn>2</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">= 110&deg;</span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">12.</span><span style=\"font-family: Cambria Math;\"> Sides of a triangle are 9 cm, 6 cm and 5 cm. What is the value of circumradius of this triangle? </span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">12.</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2340;&#2381;&#2352;&#2367;&#2349;&#2369;&#2332;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2349;&#2369;&#2332;&#2366;&#2319;&#2306;</span><span style=\"font-family: Cambria Math;\"> 9 cm, 6 cm </span><span style=\"font-family: Kokila;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> 5 cm </span><span style=\"font-family: Kokila;\">&#2361;&#2376;&#2306;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2311;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2340;&#2381;&#2352;&#2367;&#2349;&#2369;&#2332;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2352;&#2367;&#2340;&#2381;&#2352;&#2367;&#2332;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>9</mn><msqrt><mn>3</mn></msqrt></mrow><mn>4</mn></mfrac></math>cm</span></p>\n", "<p><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>9</mn><msqrt><mn>3</mn></msqrt></mrow><mn>5</mn></mfrac></math>cm</span></p>\n", 
                                "<p><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>27</mn><msqrt><mn>2</mn></msqrt></mrow><mn>8</mn></mfrac></math>cm</span></p>\n", "<p><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>9</mn><msqrt><mn>2</mn></msqrt></mrow><mn>2</mn></mfrac></math>cm</span></p>\n"],
                    options_hi: ["<p><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>9</mn><msqrt><mn>3</mn></msqrt></mrow><mn>4</mn></mfrac></math> cm</span></p>\n", "<p><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>9</mn><msqrt><mn>3</mn></msqrt></mrow><mn>5</mn></mfrac></math>cm</span></p>\n",
                                "<p><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>27</mn><msqrt><mn>2</mn></msqrt></mrow><mn>8</mn></mfrac></math>cm</span></p>\n", "<p><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>9</mn><msqrt><mn>2</mn></msqrt></mrow><mn>2</mn></mfrac></math>cm</span></p>\n"],
                    solution_en: "<p>12.(c)</p>\r\n<p><span style=\"font-family: Cambria Math;\">Semi perimeter of the triangle (s) = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>9</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>6</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>5</mn><mo>&nbsp;</mo></mrow><mn>2</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> = 10 cm</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">circumradius </span><span style=\"font-family: Cambria Math;\">of the scalene triangle = </span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>a</mi><mi>b</mi><mi>c</mi></mrow><mrow><mn>4</mn><mo>&nbsp;</mo><mi>&Delta;</mi></mrow></mfrac><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mfrac><mn>270</mn><mrow><mn>4</mn><mo>&nbsp;</mo><mo>&times;</mo><msqrt><mn>10</mn><mo>(</mo><mn>1</mn><mo>)</mo><mo>(</mo><mn>4</mn><mo>)</mo><mo>(</mo><mn>5</mn><mo>)</mo></msqrt></mrow></mfrac><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mfrac><mrow><mn>270</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><msqrt><mn>2</mn></msqrt></mrow><mrow><mn>4</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>5</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>2</mn><msqrt><mn>2</mn></msqrt><mo>&nbsp;</mo><mo>&times;</mo><msqrt><mn>2</mn></msqrt></mrow></mfrac><mo>&nbsp;</mo><mo>&rArr;</mo><mo>&nbsp;</mo><mfrac><mrow><mn>27</mn><msqrt><mn>2</mn></msqrt></mrow><mn>8</mn></mfrac></math> </span><span style=\"font-family: Cambria Math;\"> = </span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> cm</span></p>\n",
                    solution_hi: "<p>12.(c)</p>\r\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">&#2340;&#2381;&#2352;&#2367;&#2349;&#2369;&#2332; &#2325;&#2366; &#2309;&#2352;&#2381;&#2343; &#2346;&#2352;&#2367;&#2350;&#2366;&#2346;</span> (s) = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>9</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>6</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>5</mn><mo>&nbsp;</mo></mrow><mn>2</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> = 10 <span style=\"font-weight: 400;\">&#2360;&#2375;&#2350;&#2368;</span></span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">&#2357;&#2367;&#2359;&#2350;&#2348;&#2366;&#2361;&#2369; &#2340;&#2381;&#2352;&#2367;&#2349;&#2369;&#2332; &#2325;&#2368; </span><span style=\"font-weight: 400;\">&#2346;&#2352;&#2367;&#2340;&#2381;&#2352;&#2367;&#2332;&#2381;&#2351;&#2366; </span>= </span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>a</mi><mi>b</mi><mi>c</mi></mrow><mrow><mn>4</mn><mo>&nbsp;</mo><mi>&Delta;</mi></mrow></mfrac><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mfrac><mn>270</mn><mrow><mn>4</mn><mo>&nbsp;</mo><mo>&times;</mo><msqrt><mn>10</mn><mo>(</mo><mn>1</mn><mo>)</mo><mo>(</mo><mn>4</mn><mo>)</mo><mo>(</mo><mn>5</mn><mo>)</mo></msqrt></mrow></mfrac><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mfrac><mrow><mn>270</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><msqrt><mn>2</mn></msqrt></mrow><mrow><mn>4</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>5</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>2</mn><msqrt><mn>2</mn></msqrt><mo>&nbsp;</mo><mo>&times;</mo><msqrt><mn>2</mn></msqrt></mrow></mfrac><mo>&nbsp;</mo><mo>&rArr;</mo><mo>&nbsp;</mo><mfrac><mrow><mn>27</mn><msqrt><mn>2</mn></msqrt></mrow><mn>8</mn></mfrac></math> </span><span style=\"font-family: Cambria Math;\"> = <span style=\"font-weight: 400;\">&#2360;&#2375;&#2350;&#2368;</span></span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">13. </span><span style=\"font-family: Cambria Math;\">Two circles having equal radius intersect each other such that each passes through the center of the other. Sum of diameter of these two circles is 84 cm. What is the length of the common chord?</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">13.</span><span style=\"font-family: Kokila;\">&#2360;&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2340;&#2381;&#2352;&#2367;&#2332;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2357;&#2366;&#2354;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2342;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2357;&#2371;&#2340;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\">-</span><span style=\"font-family: Kokila;\">&#2342;&#2370;&#2360;&#2352;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2311;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2381;&#2352;&#2325;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2330;&#2381;&#2331;&#2375;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2352;&#2340;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2381;&#2352;&#2340;&#2381;&#2351;&#2375;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2357;&#2371;&#2340;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2342;&#2370;&#2360;&#2352;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2375;&#2306;&#2342;&#2381;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2379;&#2325;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2327;&#2369;&#2332;&#2352;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2311;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2342;&#2379;&#2344;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2348;&#2371;&#2340;&#2381;&#2340;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2357;&#2381;&#2351;&#2366;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2351;&#2379;&#2327;</span><span style=\"font-family: Cambria Math;\"> 84 cm </span><span style=\"font-family: Kokila;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2311;&#2360;&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2313;&#2349;&#2351;&#2344;&#2367;&#2359;&#2381;&#2336;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2332;&#2368;&#2357;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2354;&#2306;&#2348;&#2366;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2367;&#2340;&#2344;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>21<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math><span style=\"font-family: Cambria Math;\"> cm</span></p>\n", "<p>14<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math><span style=\"font-family: Cambria Math;\"> cm</span></p>\n", 
                                "<p>28<span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math>cm</span></p>\n", "<p>24<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math><span style=\"font-family: Cambria Math;\"> cm</span></p>\n"],
                    options_hi: ["<p>21<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math><span style=\"font-family: Cambria Math;\"> cm</span></p>\n", "<p>14<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math><span style=\"font-family: Cambria Math;\"> cm</span></p>\n",
                                "<p>28<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math><span style=\"font-family: Cambria Math;\"> cm</span></p>\n", "<p>24<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math><span style=\"font-family: Cambria Math;\"> cm</span></p>\n"],
                    solution_en: "<p>13.(a)</p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1703747517/word/media/image9.png\" width=\"169\" height=\"140\"></p>\r\n<p><span style=\"font-family: Cambria Math;\">In </span><span style=\"font-family: Cambria Math;\"> ,<span style=\"font-weight: 400;\">&Delta;ABC</span>&nbsp;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">AB = BC = CA &hellip; (radius)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">So ,</span><span style=\"font-family: Cambria Math;\"> &Delta;<span style=\"font-weight: 400;\">ABC&nbsp;</span></span><span style=\"font-family: Cambria Math;\"> is an equilateral triangle with side equals to 21 cm.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Height of equilateral triangle = </span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>3</mn><mo>&nbsp;</mo><mo>&times;</mo><mi>s</mi><mi>i</mi><mi>d</mi><mi>e</mi></mrow><mn>2</mn></mfrac><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mfrac><mrow><mn>21</mn><msqrt><mn>3</mn></msqrt></mrow><mn>2</mn></mfrac></math> </span><span style=\"font-family: Cambria Math;\"> cm.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Now, Length of chord CD = 2 &times; CO = 21<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt><mo>&nbsp;</mo></math></span><span style=\"font-family: Cambria Math;\"> cm.</span></p>\n",
                    solution_hi: "<p>13.(a)</p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1703747517/word/media/image9.png\" width=\"174\" height=\"144\"></p>\r\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">&Delta;ABC</span><span style=\"font-weight: 400;\"> &#2350;&#2375;&#2306;</span></span></p>\r\n<p><span style=\"font-family: Cambria Math;\">AB = BC = CA &hellip; (</span><span style=\"font-family: Kokila;\">&#2340;&#2381;&#2352;&#2367;&#2332;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\">)</span></p>\r\n<p><span style=\"font-family: Kokila;\">&#2311;&#2360;&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\">, &Delta;</span><span style=\"font-family: Cambria Math;\">ABC </span><span style=\"font-family: Kokila;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2350;&#2348;&#2366;&#2361;&#2369;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2340;&#2381;&#2352;&#2367;&#2349;&#2369;&#2332;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2332;&#2367;&#2360;&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2349;&#2369;&#2332;&#2366;</span><span style=\"font-family: Cambria Math;\"> 21 </span><span style=\"font-family: Kokila;\">&#2360;&#2375;&#2350;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2348;&#2352;&#2366;&#2348;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;&#2404;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">&#2360;&#2350;&#2348;&#2366;&#2361;&#2369; &#2340;&#2381;&#2352;&#2367;&#2349;&#2369;&#2332; &#2325;&#2368; &#2314;&#2305;&#2330;&#2366;&#2312;</span> = </span><span style=\"font-family: Cambria Math;\"> <math style=\"font-family: Verdana;\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>3</mn><mo>&nbsp;</mo><mo>&times;</mo><mi>&#2349;&#2369;&#2332;&#2366;</mi></mrow><mn>2</mn></mfrac><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mfrac><mrow><mn>21</mn><msqrt><mn>3</mn></msqrt></mrow><mn>2</mn></mfrac></math> <span style=\"font-weight: 400;\">&#2360;&#2375;&#2350;&#2368;</span></span><span style=\"font-family: Cambria Math;\">.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">&#2309;&#2348; &#2332;&#2368;&#2357;&#2366; CD &#2325;&#2368; &#2354;&#2306;&#2348;&#2366;&#2312;</span> = 2 &times; CO = 21<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt><mo>&nbsp;</mo></math></span><span style=\"font-family: Cambria Math;\"> <span style=\"font-weight: 400;\">&#2360;&#2375;&#2350;&#2368;</span>.</span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">14.</span><span style=\"font-family: Cambria Math;\"> In &Delta;</span><span style=\"font-family: Cambria Math;\">XYZ, P is a point on side YZ and XY = XZ. If &ang;XPY = 90 degree and YP = 9 cm, then what is the </span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">14.</span><span style=\"font-family: Cambria Math;\"> &Delta;</span><span style=\"font-family: Cambria Math;\">XYZ </span><span style=\"font-family: Kokila;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\">, P </span><span style=\"font-family: Kokila;\">&#2349;&#2369;&#2332;&#2366;</span><span style=\"font-family: Cambria Math;\"> YZ </span><span style=\"font-family: Kokila;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2381;&#2341;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2348;&#2367;&#2306;&#2342;&#2369;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> XY = XZ </span><span style=\"font-family: Kokila;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> &ang;XPY = 90 </span><span style=\"font-family: Kokila;\">&#2337;&#2367;&#2327;&#2381;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> YP = 9cm, </span><span style=\"font-family: Kokila;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> YZ </span><span style=\"font-family: Kokila;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2354;&#2306;&#2348;&#2366;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2367;&#2340;&#2344;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>17 cm</p>\n", "<p>18 cm</p>\n", 
                                "<p>12 cm</p>\n", "<p>15 cm</p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\n"],
                    options_hi: ["<p>17 cm</p>\n", "<p>18 cm</p>\n",
                                "<p>12 cm</p>\n", "<p>15 cm</p>\n"],
                    solution_en: "<p>14.(b)</p>\r\n<p><span style=\"font-family: Cambria Math;\">In an isosceles triangle the altitude drawn to the base is the median and the angle bisector . </span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1703747517/word/media/image10.png\"></p>\r\n<p><span style=\"font-family: Cambria Math;\">Length of YZ =&nbsp;</span><span style=\"font-family: Cambria Math;\"> 2&times; YP = 2 &times; 9 = 18 cm</span></p>\n",
                    solution_hi: "<p>14.(b)</p>\r\n<p><span style=\"font-family: Kokila;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2350;&#2342;&#2381;&#2357;&#2367;&#2348;&#2366;&#2361;&#2369;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2340;&#2381;&#2352;&#2367;&#2349;&#2369;&#2332;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2310;&#2343;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2326;&#2368;&#2306;&#2330;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2327;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2314;&#2305;&#2330;&#2366;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2366;&#2343;&#2381;&#2351;&#2367;&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2379;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2342;&#2381;&#2357;&#2367;&#2349;&#2366;&#2332;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2342;&#2379;&#2344;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2379;&#2340;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1703747517/word/media/image10.png\"><span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">YZ </span><span style=\"font-family: Kokila;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2354;&#2306;&#2348;&#2366;&#2312;</span><span style=\"font-family: Cambria Math;\">&nbsp; = </span><span style=\"font-family: Cambria Math;\">2&times; YP = 2 &times; 9 = 18 </span><span style=\"font-family: Kokila;\">&#2360;&#2375;&#2350;&#2368;</span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "misc",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">15. </span><span style=\"font-family: Cambria Math;\"> In an equilateral triangle ABC, P is the centroid of this triangle. Side of &Delta;</span><span style=\"font-family: Cambria Math;\">ABC is 16<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math></span><span style=\"font-family: Cambria Math;\"> cm. What is the distance of point P from side BC ?</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">15. </span><span style=\"font-family: Kokila;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2350;&#2348;&#2366;&#2361;&#2369;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2340;&#2381;&#2352;&#2367;&#2349;&#2369;&#2332;</span><span style=\"font-family: Cambria Math;\"> ABC </span><span style=\"font-family: Kokila;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\">, P </span><span style=\"font-family: Kokila;\">&#2311;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2340;&#2381;&#2352;&#2367;&#2349;&#2369;&#2332;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2375;&#2344;&#2381;&#2342;&#2381;&#2352;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> &Delta;</span><span style=\"font-family: Cambria Math;\">ABC </span><span style=\"font-family: Kokila;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2349;&#2369;&#2332;&#2366;</span><span style=\"font-family: Cambria Math;\"> 16<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math></span><span style=\"font-family: Cambria Math;\"> cm </span><span style=\"font-family: Kokila;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2349;&#2369;&#2332;&#2366;</span><span style=\"font-family: Cambria Math;\"> BC </span><span style=\"font-family: Kokila;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2348;&#2367;&#2306;&#2342;&#2369;</span><span style=\"font-family: Cambria Math;\"> P </span><span style=\"font-family: Kokila;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2342;&#2370;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2367;&#2340;&#2344;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>8 cm</p>\n", "<p>12 cm</p>\n", 
                                "<p>9 cm</p>\n", "<p>10 cm</p>\n"],
                    options_hi: ["<p>8 cm</p>\n", "<p>12 cm</p>\n",
                                "<p>9 cm</p>\n", "<p>10 cm</p>\n"],
                    solution_en: "<p>15.(a)</p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1703747517/word/media/image11.png\"></p>\r\n<p><span style=\"font-family: Cambria Math;\">Using pythagoras theorem in &Delta;</span><span style=\"font-family: Cambria Math;\">AOB;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">AO = </span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mi>A</mi><msup><mi>B</mi><mn>2</mn></msup><mo>-</mo><mi>O</mi><msup><mi>B</mi><mn>2</mn></msup></msqrt><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><msqrt><msup><mrow><mo>(</mo><mn>16</mn><msqrt><mn>3</mn></msqrt><mo>)</mo></mrow><mn>2</mn></msup><mo>-</mo><msup><mrow><mo>(</mo><mn>8</mn><msqrt><mn>3</mn></msqrt><mo>)</mo></mrow><mn>2</mn></msup></msqrt></math> </span><span style=\"font-family: Cambria Math;\"> = 24 cm</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Centroid divides the median in 2 : 1 </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">So , Length of PO = 24 &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&nbsp;</mo><mfrac><mn>1</mn><mn>3</mn></mfrac><mo>&nbsp;</mo></math></span><span style=\"font-family: Cambria Math;\"> = 8 cm. </span></p>\n",
                    solution_hi: "<p>15.(a)</p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1703747517/word/media/image11.png\"></p>\r\n<p><span style=\"font-family: Cambria Math;\">&Delta;AOB </span><span style=\"font-family: Kokila;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2366;&#2311;&#2341;&#2366;&#2327;&#2379;&#2352;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2381;&#2352;&#2350;&#2375;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2381;&#2352;&#2351;&#2379;&#2327;</span><span style=\"font-family: Cambria Math;\"> ;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">AO = </span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mi>A</mi><msup><mi>B</mi><mn>2</mn></msup><mo>-</mo><mi>O</mi><msup><mi>B</mi><mn>2</mn></msup></msqrt><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><msqrt><msup><mrow><mo>(</mo><mn>16</mn><msqrt><mn>3</mn></msqrt><mo>)</mo></mrow><mn>2</mn></msup><mo>-</mo><msup><mrow><mo>(</mo><mn>8</mn><msqrt><mn>3</mn></msqrt><mo>)</mo></mrow><mn>2</mn></msup></msqrt></math> </span><span style=\"font-family: Cambria Math;\"> = 24 <span style=\"font-weight: 400;\">&#2360;&#2375;&#2350;&#2368;</span></span></p>\r\n<p><span style=\"font-weight: 400;\">&#2325;&#2375;&#2344;&#2381;&#2342;&#2381;&#2352;&#2325; &#2350;&#2366;&#2343;&#2381;&#2351;&#2367;&#2325;&#2366; &#2325;&#2379; 2:1 &#2350;&#2375;&#2306; &#2357;&#2367;&#2349;&#2366;&#2332;&#2367;&#2340; &#2325;&#2352;&#2340;&#2366; &#2361;&#2376;&#2404;</span></p>\r\n<p><span style=\"font-weight: 400;\">&#2309;&#2340;&#2307;, PO &#2325;&#2368; &#2354;&#2306;&#2348;&#2366;&#2312; </span><span style=\"font-family: Cambria Math;\">= 24 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&nbsp;</mo><mfrac><mn>1</mn><mn>3</mn></mfrac><mo>&nbsp;</mo></math></span><span style=\"font-family: Cambria Math;\"> = 8 <span style=\"font-weight: 400;\">&#2360;&#2375;&#2350;&#2368;</span>.</span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>