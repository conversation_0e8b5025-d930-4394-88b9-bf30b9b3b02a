<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">15:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 15</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">15</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 15 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 0,
                end: 13
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 14,
                end: 14
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "17",
                    question_en: "<p>1. PQ and RS are two chords of a circle such that PQ = 12 cm and RS = 20 cm and PQ is parallel to RS. If the distance between PQ and RS is 4 cm, find the diameter of the Circle.</p>",
                    question_hi: "<p>1. PQ और RS एक वृत्त की दो जीवाएँ इस प्रकार हैं कि PQ = 12 cm और RS = 20 cm है और PQ, RS के समानांतर है। यदि PQ और RS के बीच की दूरी 4 cm है, तो वृत्त का व्यास ज्ञात कीजिए।</p>",
                    options_en: ["<p>4 <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>34</mn></msqrt></math> cm</p>", "<p>2<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>34</mn></msqrt></math> cm</p>", 
                                "<p>3<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>34</mn></msqrt></math> cm</p>", "<p>6<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>34</mn></msqrt></math> cm</p>"],
                    options_hi: ["<p>4 <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>34</mn></msqrt></math> cm</p>", "<p>2<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>34</mn></msqrt></math> cm</p>",
                                "<p>3<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>34</mn></msqrt></math> cm</p>", "<p>6<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>34</mn></msqrt></math> cm</p>"],
                    solution_en: "<p>1.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736760522052.png\" alt=\"rId5\" width=\"179\" height=\"167\"><br>OP&sup2; = x&sup2; + 36 --------(i)<br>OR&sup2; = (4 - x)&sup2; + 100<br>OP = OR (Radius)<br>Then, x&sup2; + 36 = (4 - x)&sup2; + 100<br>x&sup2; + 36 = 16 + x&sup2; - 8x + 100<br>-8x&nbsp;= - 80 <br>x = 10<br>Putting x = 10 in equation (i) we get;<br>OP&sup2; = 100 + 36 = 136<br>OP = <math display=\"inline\"><msqrt><mn>136</mn></msqrt></math> = 2<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>34</mn></msqrt></math> cm<br>Diameter = 2OP = 4<math display=\"inline\"><msqrt><mn>34</mn></msqrt></math> cm</p>",
                    solution_hi: "<p>1.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736760522052.png\" alt=\"rId5\" width=\"179\" height=\"167\"><br>OP&sup2; = x&sup2; + 36 --------(i)<br>OR&sup2; = (4 - x)&sup2; + 100<br>OP = OR (त्रिज्या)<br>फिर, x&sup2; + 36 = (4 - x)&sup2; + 100<br>x&sup2; + 36 = 16 + x&sup2; - 8x + 100<br>-8x&nbsp;= - 80 <br>x = 10<br>समीकरण (i) में <math display=\"inline\"><mi>x</mi></math> = 10 रखने पर हमें प्राप्त होता है;<br>OP&sup2; = 100 + 36 = 136<br>OP = <math display=\"inline\"><msqrt><mn>136</mn></msqrt></math> = 2<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>34</mn></msqrt></math> cm<br>व्यास = 2OP = 4<math display=\"inline\"><msqrt><mn>34</mn></msqrt></math> cm</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "17",
                    question_en: "<p>2. △XYZ is similar to △PQR. If the ratio of the perimeter of △XYZ to the perimeter of △PQR is 16 : 9 and PQ = 3.6 cm, then what is the length (in cm) of XY?</p>",
                    question_hi: "<p>2. △XYZ, △PQR के समरूप है। यदि △XYZ के परिमाप और △PQR के परिमाप का अनुपात 16 : 9 है तथा PQ = 3.6 cm है, तो XY की लंबाई (cm में) कितनी है?</p>",
                    options_en: ["<p>3<math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math></p>", "<p>2<math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math></p>", 
                                "<p>6<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math></p>", "<p>4<math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math></p>"],
                    options_hi: ["<p>3 <math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math></p>", "<p>2 <math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math></p>",
                                "<p>6 <math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math></p>", "<p>4 <math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math></p>"],
                    solution_en: "<p>2.(c)<br>△XYZ ~ △PQR<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>perimeter</mi><mi mathvariant=\"normal\">&#160;</mi><mi>of</mi><mi mathvariant=\"normal\">&#160;</mi><mo>&#9651;</mo><mi>XYZ</mi><mi mathvariant=\"normal\">&#160;</mi></mrow><mrow><mi>perimeter</mi><mi mathvariant=\"normal\">&#160;</mi><mi>of</mi><mi mathvariant=\"normal\">&#160;</mi><mo>&#9651;</mo><mi>PQR</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>XY</mi><mi>PQ</mi></mfrac></math><br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>XY</mi><mrow><mn>3</mn><mo>.</mo><mn>6</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>16</mn><mn>9</mn></mfrac></math> &rArr; XY = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>64</mn><mn>10</mn></mfrac></math> = 6<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>5</mn></mfrac></math></p>",
                    solution_hi: "<p>2.(c)<br>△XYZ ~ △PQR<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>&#9651;</mo><mi>XYZ</mi><mi mathvariant=\"normal\">&#160;</mi><mi>&#2325;&#2366;</mi><mi mathvariant=\"normal\">&#160;</mi><mi>&#2346;&#2352;&#2367;&#2350;&#2366;&#2346;</mi><mi mathvariant=\"normal\">&#160;</mi></mrow><mrow><mo>&#9651;</mo><mi>PQR</mi><mi mathvariant=\"normal\">&#160;</mi><mi>&#2325;&#2366;</mi><mi mathvariant=\"normal\">&#160;</mi><mi>&#2346;&#2352;&#2367;&#2350;&#2366;&#2346;</mi><mi mathvariant=\"normal\">&#160;</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>XY</mi><mi>PQ</mi></mfrac></math><br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>XY</mi><mrow><mn>3</mn><mo>.</mo><mn>6</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>16</mn><mn>9</mn></mfrac></math> &rArr; XY = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>64</mn><mn>10</mn></mfrac></math> = 6<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>5</mn></mfrac></math></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "17",
                    question_en: "<p>3. Consider AD is a tangent to a circle of radius 6 cm. AC is a secant meeting the circle at B and CD is a diameter. If AB is 7 cm, then the value of AC (in cm) is:</p>",
                    question_hi: "<p>3. मान लीजिए कि AD एक 6 cm त्रिज्या वाले वृत्त की स्पर्श रेखा है। AC एक छेदक रेखा है जो वृत्त से बिन्दु B पर मिलती है और CD व्यास है। यदि AB का मान 7 cm है, तो AC का मान (cm में) क्या है?</p>",
                    options_en: ["<p>16</p>", "<p>20</p>", 
                                "<p>9</p>", "<p>18</p>"],
                    options_hi: ["<p>16</p>", "<p>20</p>",
                                "<p>9</p>", "<p>18</p>"],
                    solution_en: "<p>3.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736760522379.png\" alt=\"rId6\" width=\"247\" height=\"126\"><br>Let BC = x<br>In <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">&#916;</mi></math>ADC <br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>AC</mi><mn>2</mn></msup></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>AD</mi><mn>2</mn></msup><mo>+</mo><msup><mi>CD</mi><mn>2</mn></msup></math> ( pythagoras theorem )<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><mn>7</mn><mo>+</mo><mi mathvariant=\"normal\">x</mi><mo>)</mo></mrow><mn>2</mn></msup></math> = AD&sup2; + (12)&sup2;<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>AD</mi><mn>2</mn></msup></math> = (7 + x)&sup2; - (12)&sup2; -----(i)<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>AD</mi><mn>2</mn></msup></math> = 7 (7 + x) ---- (ii) [ ∵ AD&sup2; = AB &times; AC ]<br>Now <br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><mn>7</mn><mo>+</mo><mi mathvariant=\"normal\">x</mi><mo>)</mo></mrow><mn>2</mn></msup></math> - (12)&sup2; = 7 (7 + x)&nbsp;<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mi mathvariant=\"normal\">&#160;</mi><mi mathvariant=\"normal\">x</mi></mrow><mn>2</mn></msup></math> + 7x -144 = 0 <br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mi mathvariant=\"normal\">&#160;</mi><mi mathvariant=\"normal\">x</mi></mrow><mn>2</mn></msup></math> + 16x - 9x - 144 = 0<br>x(x + 16 ) - 9(x + 16 ) = 0<br><math display=\"inline\"></math>(x - 9 ) (x + 16 ) = 0<br>x = 9 , -16 (∵ side cannot be negative )<br>x = 9 <br>AC = AB + BC = 7 + 9 = 16cm</p>",
                    solution_hi: "<p>3.(a)<br><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736760522379.png\" alt=\"rId6\" width=\"247\" height=\"126\"><br>माना , BC = x<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">&#916;</mi></math>ADC में <br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>AC</mi><mn>2</mn></msup></math> = AD&sup2; + CD&sup2; ( पाइथागोरस प्रमेय )<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><mn>7</mn><mi mathvariant=\"normal\">&#160;</mi><mo>+</mo><mi mathvariant=\"normal\">&#160;</mi><mi mathvariant=\"normal\">x</mi><mo>)</mo></mrow><mn>2</mn></msup></math> = AD&sup2; + (12)&sup2;<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>AD</mi><mn>2</mn></msup></math> = (7 + x)&sup2; - (12)&sup2; -----(i)<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>AD</mi><mn>2</mn></msup></math> = 7 (7 + x) ---- (ii) [ ∵ AD&sup2; = AB &times; AC ]<br>अब <br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><mn>7</mn><mi mathvariant=\"normal\">&#160;</mi><mo>+</mo><mi mathvariant=\"normal\">&#160;</mi><mi mathvariant=\"normal\">x</mi><mo>)</mo></mrow><mn>2</mn></msup></math> - (12)&sup2; = 7 (7 + x)&nbsp;<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mi mathvariant=\"normal\">&#160;</mi><mi mathvariant=\"normal\">x</mi></mrow><mn>2</mn></msup></math> + 7x -144 = 0 <br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mi mathvariant=\"normal\">&#160;</mi><mi mathvariant=\"normal\">x</mi></mrow><mn>2</mn></msup></math> + 16x - 9x - 144 = 0<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">x</mi></math>(x + 16 ) - 9 (x + 16 ) = 0<br><math display=\"inline\"></math>(x - 9 )(x + 16 ) = 0<br>x = 9 , -16 (∵ भुजा ,ऋणात्मक नहीं हो सकती )<br>x = 9 <br>AC = AB + BC = 7 + 9 = 16cm</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "17",
                    question_en: "<p>4. In a right angle triangle with sides 12 cm and 16 cm and hypotenuse 20 cm, the length of altitude drawn on the hypotenuse from the opposite vertex is K. Find the value of K.</p>",
                    question_hi: "<p>4. 12 cm और 16 cm की भुजाओं और 20 cm कर्ण वाले एक समकोण त्रिभुज में, सम्मुख शीर्ष से कर्ण पर खींचा गया शीर्षलंब K है। K का मान ज्ञात करें।</p>",
                    options_en: ["<p>6.8 cm</p>", "<p>9.6 cm</p>", 
                                "<p>8.6 cm</p>", "<p>6.9 cm</p>"],
                    options_hi: ["<p>6.8 cm</p>", "<p>9.6 cm</p>",
                                "<p>8.6 cm</p>", "<p>6.9 cm</p>"],
                    solution_en: "<p>4.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736760522557.png\" alt=\"rId7\" width=\"176\" height=\"180\"><br>Theorem :- AC &times; DB = AB &times; BC<br>20 &times; DB = 12 &times; 16<br>DB = <math display=\"inline\"><mfrac><mrow><mn>12</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>16</mn></mrow><mrow><mn>20</mn></mrow></mfrac></math> = 9.6 cm</p>",
                    solution_hi: "<p>4.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736760522557.png\" alt=\"rId7\" width=\"176\" height=\"180\"><br>प्रमेय :- AC &times; DB = AB &times; BC<br>20 &times; DB = 12 &times; 16<br>DB = <math display=\"inline\"><mfrac><mrow><mn>12</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>16</mn></mrow><mrow><mn>20</mn></mrow></mfrac></math> = 9.6 cm</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "17",
                    question_en: "<p>5. A tangent is drawn from a point that is at a distance of 37 cm from center of the circle O, and diameter is 24 cm. The length (in cm) of the tangent is:</p>",
                    question_hi: "<p>5. एक स्पर्शरेखा उस बिंदु से खींची जाती है जो वृत्त के केंद्र O से 37 cm की दूरी पर है और व्यास 24 cm है। स्पर्शरेखा की लंबाई (cm में) कितनी है?</p>",
                    options_en: ["<p>30</p>", "<p>35</p>", 
                                "<p>28.16</p>", "<p>37</p>"],
                    options_hi: ["<p>30</p>", "<p>35</p>",
                                "<p>28.16</p>", "<p>37</p>"],
                    solution_en: "<p>5.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736760522740.png\" alt=\"rId8\" width=\"244\" height=\"173\"><br>Length of tangent (PQ) = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mrow><mo>(</mo><mn>37</mn><mo>)</mo></mrow><mn>2</mn></msup><mo>-</mo><msup><mrow><mo>(</mo><mn>12</mn><mo>)</mo></mrow><mn>2</mn></msup></msqrt></math><br>= <math display=\"inline\"><msqrt><mn>1369</mn><mo>-</mo><mn>144</mn></msqrt></math><br>= <math display=\"inline\"><msqrt><mn>1225</mn></msqrt></math><br>= 35 cm</p>",
                    solution_hi: "<p>5.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736760522740.png\" alt=\"rId8\" width=\"244\" height=\"173\"><br>स्पर्शरेखा की लंबाई (PQ) = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mrow><mo>(</mo><mn>37</mn><mo>)</mo></mrow><mn>2</mn></msup><mo>-</mo><msup><mrow><mo>(</mo><mn>12</mn><mo>)</mo></mrow><mn>2</mn></msup></msqrt></math><br>= <math display=\"inline\"><msqrt><mn>1369</mn><mo>-</mo><mn>144</mn></msqrt></math><br>= <math display=\"inline\"><msqrt><mn>1225</mn></msqrt></math><br>= 35 cm</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "17",
                    question_en: "<p>6. The sides ST and TR and the median SU of the &Delta; STR are equal to the sides MN and NJ and the median MV of the &Delta;MNJ, respectively. If &ang;TSU = 46&deg;, &ang;NMJ = 79&deg;and &ang;MVN = 88&deg;, what is the degree measure of <math display=\"inline\"><mfrac><mrow><mi>&#160;</mi><mn>7</mn></mrow><mrow><mn>11</mn></mrow></mfrac></math>&ang;SRT?</p>",
                    question_hi: "<p>6. &Delta;STR की भुजाएँ ST और TR और माध्यिका SU क्रमशः &Delta;MNJ की भुजाओं MN और NJ और माध्यिका MV के बराबर हैं। यदि &ang;TSU= 46&deg;, &ang;NMJ = 79&deg; और &ang;MVN = 88&deg; है, तो <math display=\"inline\"><mfrac><mrow><mi>&#160;</mi><mn>7</mn></mrow><mrow><mn>11</mn></mrow></mfrac></math>&ang;SRT का अंश माप क्या है?</p>",
                    options_en: ["<p>35&deg;</p>", "<p>56&deg;</p>", 
                                "<p>47&deg;</p>", "<p>55&deg;</p>"],
                    options_hi: ["<p>35&deg;</p>", "<p>56&deg;</p>",
                                "<p>47&deg;</p>", "<p>55&deg;</p>"],
                    solution_en: "<p>6.(a) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736760522851.png\" alt=\"rId9\" width=\"348\" height=\"172\"><br>&ang;STU = 180&deg; - (46&deg; + 88&deg;) = 46&deg;<br>&ang;SRT = 180&deg; - (46&deg; + 79&deg;) = 55&deg;<br><math display=\"inline\"><mo>&#8658;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>11</mn></mfrac></math>&ang;SRT = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>11</mn></mfrac></math> &times; 55&deg; = 35&deg;</p>",
                    solution_hi: "<p>6.(a) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736760522851.png\" alt=\"rId9\" width=\"348\" height=\"172\"><br>&ang;STU = 180&deg; - (46&deg; + 88&deg;) = 46&deg;<br>&ang;SRT = 180&deg; - (46&deg; + 79&deg;) = 55&deg;<br><math display=\"inline\"><mo>&#8658;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>11</mn></mfrac></math>&ang;SRT = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>11</mn></mfrac></math> &times; 55&deg; = 35&deg;</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "17",
                    question_en: "<p>7. A tangent is drawn from a point at a distance of 25 cm from the Centre of a circle C(0,r) of radius 7cm. The length of the tangent is ___________ cm.</p>",
                    question_hi: "<p>7. 7 cm त्रिज्या वाले वृत्त के केंद्र C(0, r) से 25 cm की दूरी पर एक बिंदु से एक स्पर्शरेखा खींची गई है। स्पर्शरेखा की लंबाई _________ cm है।</p>",
                    options_en: ["<p>8</p>", "<p>16</p>", 
                                "<p>20</p>", "<p>24</p>"],
                    options_hi: ["<p>8</p>", "<p>16</p>",
                                "<p>20</p>", "<p>24</p>"],
                    solution_en: "<p>7.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736760523049.png\" alt=\"rId10\" width=\"264\" height=\"181\"><br>Length of tangent (PQ) = <math display=\"inline\"><msqrt><msup><mrow><mfenced separators=\"|\"><mrow><mn>25</mn></mrow></mfenced></mrow><mrow><mn>2</mn></mrow></msup><mo>-</mo><msup><mrow><mfenced separators=\"|\"><mrow><mn>7</mn></mrow></mfenced></mrow><mrow><mn>2</mn></mrow></msup></msqrt></math><br>= <math display=\"inline\"><msqrt><mn>625</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>49</mn></msqrt></math><br>= <math display=\"inline\"><msqrt><mn>576</mn></msqrt></math><br>= 24 cm</p>",
                    solution_hi: "<p>7.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736760523049.png\" alt=\"rId10\" width=\"264\" height=\"181\"><br>स्पर्शरेखा(PQ) की लंबाई = <math display=\"inline\"><msqrt><msup><mrow><mfenced separators=\"|\"><mrow><mn>25</mn></mrow></mfenced></mrow><mrow><mn>2</mn></mrow></msup><mo>-</mo><msup><mrow><mfenced separators=\"|\"><mrow><mn>7</mn></mrow></mfenced></mrow><mrow><mn>2</mn></mrow></msup></msqrt></math><br>= <math display=\"inline\"><msqrt><mn>625</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>49</mn></msqrt></math><br>= <math display=\"inline\"><msqrt><mn>576</mn></msqrt></math><br>= 24 cm</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "17",
                    question_en: "<p>8. In the diagram, if <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><menclose notation=\"top\"><mi>AB</mi></menclose></math>||<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><menclose notation=\"top\"><mi>CE</mi></menclose></mstyle></math>, AG = GD = DH = HB, then which of the following is correct?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736760523173.png\" alt=\"rId11\" width=\"203\" height=\"172\"> <br>I) <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">&#916;</mi></math>ACG &cong; &Delta;&Beta;&Epsilon;&Eta; <br>II) <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">&#916;</mi></math>CDE &cong; &Delta;DEB &cong; &Delta;DCA <br>III) <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">&#916;</mi></math>HFB &cong; &Delta;AFG</p>",
                    question_hi: "<p>8. दिए गए आरेख में, यदि <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><menclose notation=\"top\"><mi>AB</mi></menclose></math>||<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><menclose notation=\"top\"><mi>CE</mi></menclose></mstyle></math>, AG = GD = DH = HB है, तो निम्न में से कौन सा/से सही है/हैं?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736760523173.png\" alt=\"rId11\" width=\"203\" height=\"172\"><br>I) <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">&#916;</mi></math>ACG &cong; &Delta;&Beta;&Epsilon;&Eta; <br>II) <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">&#916;</mi></math>CDE &cong; &Delta;DEB &cong; &Delta;DCA <br>III) <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">&#916;</mi></math>HFB &cong; &Delta;AFG</p>",
                    options_en: ["<p>I, II and III</p>", "<p>I and III</p>", 
                                "<p>Only I</p>", "<p>I and II</p>"],
                    options_hi: ["<p>I, II और III</p>", "<p>I और III</p>",
                                "<p>केवल I</p>", "<p>I और II</p>"],
                    solution_en: "<p>8.(d)</p>\n<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736760523173.png\" alt=\"rId11\" width=\"203\" height=\"172\"></p>\n<p dir=\"ltr\"><strong>I)</strong> For, &Delta;ACG &cong; &Delta;&Beta;&Epsilon;&Eta;&nbsp;</p>\n<p dir=\"ltr\">In the &Delta;AGC and &Delta;EHB</p>\n<p dir=\"ltr\">&ang;AGC = &ang;EHB = 90&deg;</p>\n<p dir=\"ltr\">Side (AG) = side(HB) &nbsp; &nbsp; [given]</p>\n<p dir=\"ltr\">And, CG = EH [lengths between two parallel lines]<br>So, by SAS theorem,<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">&#916;</mi></math>ACG &cong; &Delta;&Beta;&Epsilon;&Eta; (true)<br><strong>II)</strong> Now, for <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">&#916;</mi></math>CDE &cong; &Delta;DEB &cong; &Delta;DCA <br>In the <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">&#916;</mi></math>CDE and &Delta;DEB , CE ॥ AB (given)<br>DE = DE [common]<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">&#8736;</mi></math>CED = &ang;BDE [alternate interior angle]<br>CE = DB<br>So, <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">&#916;</mi></math>CDE &cong; &Delta;DEB by SAS theorem,<br>Similarly, <br>In <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">&#916;</mi></math>CDE and &Delta;DCA , CE ॥ AB (given)<br>CD = CD [common]<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">&#8736;</mi></math>ECD = &ang;ADC [alternate interior angle]<br>CE = AD<br>So, <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">&#916;</mi></math>CDE &cong; &Delta;DCA by SAS theorem,<br>Hence, <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">&#916;</mi></math>CDE &cong; &Delta;DEB &cong; &Delta;DCA [true]<br><strong>III)</strong> For, <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">&#916;</mi></math>HFB &cong; &Delta;AFG [false]<br>Only AG = HB [given]<br>∵ No other side or angles are equal <br>Therefore, option (d) is the correct answer.</p>",
                    solution_hi: "<p>8.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736760523173.png\" alt=\"rId11\" width=\"203\" height=\"172\"></p>\n<p dir=\"ltr\"><strong>I)</strong> &Delta;ACG &cong; &Delta;&Beta;&Epsilon;&Eta; के लिए,</p>\n<p dir=\"ltr\">&Delta;AGC और &Delta;EHB में&nbsp;</p>\n<p dir=\"ltr\">&ang;AGC = &ang;EHB = 90&deg;</p>\n<p dir=\"ltr\">भुजा (AG) = भुजा (HB) &nbsp; &nbsp; [दिया गया]</p>\n<p dir=\"ltr\">और , CG = EH &nbsp; [दो समांतर रेखाओं के बीच की दूरी]</p>\n<p dir=\"ltr\">तो , SAS प्रमेय द्वारा&nbsp;</p>\n<p dir=\"ltr\">&Delta;ACG &cong; &Delta;&Beta;&Epsilon;&Eta; (सत्य)</p>\n<p dir=\"ltr\"><strong>II)</strong> अब, &Delta;CDE &cong; &Delta;DEB &cong; &Delta;DCA के लिए&nbsp;</p>\n<p dir=\"ltr\">&Delta;CDE और &Delta;DEB मे&nbsp; , CE ॥ AB (दिया गया)</p>\n<p dir=\"ltr\">DE = DE&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; [उभयनिष्ठ ]</p>\n<p dir=\"ltr\">&ang;CED = &ang;BDE&nbsp; &nbsp; &nbsp; [एकान्तर अंतः कोण]</p>\n<p dir=\"ltr\">CE = DB</p>\n<p dir=\"ltr\">तो, SAS प्रमेय द्वारा &Delta;CDE &cong; &Delta;DEB,</p>\n<p dir=\"ltr\">इसी प्रकार,&nbsp;</p>\n<p dir=\"ltr\">&Delta;CDE और &Delta;DCA में , CE ॥ AB (दिया गया)</p>\n<p dir=\"ltr\">CD = CD &nbsp; [उभयनिष्ठ]</p>\n<p dir=\"ltr\">&ang;ECD = &ang;ADC &nbsp; &nbsp; &nbsp; &nbsp; [एकान्तर अंतः कोण]</p>\n<p dir=\"ltr\">CE = AD</p>\n<p dir=\"ltr\">तो , &Delta;CDE &cong; &Delta;DCA ,SAS प्रमेय द्वारा&nbsp;</p>\n<p dir=\"ltr\">अतः , &Delta;CDE &cong; &Delta;DEB &cong; &Delta;DCA [सत्य]</p>\n<p dir=\"ltr\"><strong>III)</strong> &Delta;HFB &cong; &Delta;AFG के लिए [असत्य]</p>\n<p dir=\"ltr\">केवल AG = HB &nbsp; &nbsp; [दिया गया ]</p>\n<p dir=\"ltr\">∵कोई अन्य भुजा या कोण समान नहीं हैं&nbsp;</p>\n<p dir=\"ltr\">इसलिए, विकल्प (d) सही उत्तर है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "17",
                    question_en: "<p>9. The lengths of the three sides of a triangle are 19 cm, 25 cm and y cm. Which of the following options most accurately gives the possible values of &lsquo;y&rsquo;?</p>",
                    question_hi: "<p>9. एक त्रिभुज की तीन भुजाओं की लंबाई 19 cm, 25 cm और y cm है। निम्नलिखित में से कौन-सा विकल्प अधिक सटीकता से \'y\' के संभावित मान देता है?</p>",
                    options_en: ["<p>6 &le; y &le; 44</p>", "<p>6 &lt; y &lt; 44</p>", 
                                "<p>6 &lt; y &le; 44</p>", "<p>6 &le; y &lt; 44</p>"],
                    options_hi: ["<p>6 &le; y &le; 44</p>", "<p>6 &lt; y &lt; 44</p>",
                                "<p>6 &lt; y &le; 44</p>", "<p>6 &le; y &lt; 44</p>"],
                    solution_en: "<p>9.(b)<br>Concept:- if any triangle has three sides x, y and z <br>Then, (x + y) &gt; z &gt; (x - y)<br>According to question,<br>(25-19) &lt; y &lt; (19 + 25) <br>6 &lt; y &lt; 44</p>",
                    solution_hi: "<p>9.(b)<br>संकल्पना:- यदि किसी त्रिभुज की तीन भुजाएँ x, y और z हैं <br>फिर, (x + y) &gt; z &gt; (x - y)<br>प्रश्न के अनुसार,<br>(25-19) &lt; y &lt; (19 + 25) <br>6 &lt; y &lt; 44</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "17",
                    question_en: "<p>10. In a circle with center O, the diameter AB extended to the point C, outside the circle. CD is a tangent at the point D on the circle. If the radius of circle is 6 cm and &ang;DBC = 120&deg;, then the length of CD is equal to:</p>",
                    question_hi: "<p>10. O केंद्र वाले एक वृत्त में, व्यास AB को वृत्त के बाहर बिंदु C तक बढ़ाया जाता है। CD, वृत्त पर स्थित बिंदु D पर एक स्पर्श रेखा है। यदि वृत्त की त्रिज्या 6 cm हैऔर &ang;DBC = 120&deg; है, तो CD की लंबाई _______ होगी।</p>",
                    options_en: ["<p>6<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math> cm</p>", "<p>4<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math> cm</p>", 
                                "<p>5<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math> cm</p>", "<p>3<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math>&nbsp;cm</p>"],
                    options_hi: ["<p>6<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math> cm</p>", "<p>4<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math> cm</p>",
                                "<p>5<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math> cm</p>", "<p>3<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math>&nbsp;cm</p>"],
                    solution_en: "<p>10.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736760523467.png\" alt=\"rId12\" width=\"258\" height=\"115\"><br>From the figure,<br>&ang;OBD = 180&deg; - 120&deg; = 60&deg; [angle of one side of line]<br>&there4; &ang;OBD = &ang;ODB = 60&deg; [opposite angle of same side (radius)]<br>Then, &ang;BOD = 180&deg; - (60&deg; + 60&deg;) = 60&deg;<br>Each angle of <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">&#916;</mi></math>DOB is 60&deg;<br>So, <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">&#916;</mi></math>DOB is equilateral<br>Then, side DB = 6 cm<br>Now, in <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">&#916;</mi></math>DBC<br>&ang;D : &ang;C : &ang;B = 30&deg; : 30&deg; : 120&deg;<br>Side ratio DB : BC : DC = 1 : 1 : <math display=\"inline\"><msqrt><mn>3</mn></msqrt></math><br>Hence, DC = 6 &times; <math display=\"inline\"><msqrt><mn>3</mn></msqrt></math> = 6<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math> cm</p>",
                    solution_hi: "<p>10.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736760523467.png\" alt=\"rId12\" width=\"258\" height=\"115\"><br>चित्र से,<br>&ang;OBD = 180&deg; - 120&deg; = 60&deg; [रेखा के एक तरफ का कोण]<br>&there4; &ang;OBD = &ang;ODB = 60&deg; [समान भुजा (त्रिज्या) का विपरीत कोण]<br>फिर, &ang;BOD = 180&deg; - (60&deg; + 60&deg;) = 60&deg;<br>DOB का प्रत्येक कोण 60&deg; का होता है<br>अतः, <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">&#916;</mi></math>DOB समबाहु है<br>तो , भुजा DB = 6 सेमी <br>अब , <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">&#916;</mi></math>DBC में <br>&ang;D : &ang;C : &ang;B = 30&deg; : 30&deg; : 120&deg;<br>भुजा DB : BC : DC का अनुपात = 1 : 1 : <math display=\"inline\"><msqrt><mn>3</mn></msqrt></math><br>अतः, DC = 6 &times; <math display=\"inline\"><msqrt><mn>3</mn></msqrt></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math> सेमी</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "17",
                    question_en: "<p>11. If three sides of a triangle are 6 cm, 8 cm and 10 cm, then the area of the triangle is:</p>",
                    question_hi: "<p>11. यदि एक त्रिभुज की तीन भुजाएँ 6 cm, 8 cm और 10 cm की हैं, तो त्रिभुज का क्षेत्रफल ______ है।</p>",
                    options_en: ["<p>24 cm&sup2;</p>", "<p>12 <math display=\"inline\"><msqrt><mn>3</mn></msqrt></math> cm&sup2;</p>", 
                                "<p>24 <math display=\"inline\"><msqrt><mn>3</mn></msqrt></math> cm&sup2;</p>", "<p>30 cm&sup2;</p>"],
                    options_hi: ["<p>24 cm&sup2;</p>", "<p>12<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math> cm&sup2;</p>",
                                "<p>24<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math> cm&sup2;</p>", "<p>30 cm&sup2;</p>"],
                    solution_en: "<p>11.(a)<br>(6 , 8 , 10) are the pythagorean triplet<br>So, area of triangle = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> &times; 6 &times; 8 = 24 cm&sup2;</p>",
                    solution_hi: "<p>11.(a)<br>(6 , 8 , 10) पाइथागोरस त्रिक हैं<br>अत: त्रिभुज का क्षेत्रफल = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> &times; 6 &times; 8 = 24 सेमी&sup2;</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "17",
                    question_en: "<p>12. By decreasing 17&deg; from each angle of a triangle, the ratio of their angles is 3 : 4 : 7. The radian measure of the greatest angle is:</p>",
                    question_hi: "<p>12. एक त्रिभुज के प्रत्येक कोण से 17&deg; घटाने पर, उनके कोणों का अनुपात 3 : 4 : 7 है। सबसे बड़े कोण का रेडियन माप क्या होगा ?</p>",
                    options_en: ["<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>153</mn><mi mathvariant=\"normal\">&#960;</mi></mrow><mn>360</mn></mfrac></math></p>", "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>167</mn><mi mathvariant=\"normal\">&#960;</mi></mrow><mn>180</mn></mfrac></math></p>", 
                                "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>163</mn><mi mathvariant=\"normal\">&#960;</mi></mrow><mn>180</mn></mfrac></math></p>", "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>163</mn><mi mathvariant=\"normal\">&#960;</mi></mrow><mn>360</mn></mfrac></math></p>"],
                    options_hi: ["<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>153</mn><mi mathvariant=\"normal\">&#960;</mi></mrow><mn>360</mn></mfrac></math></p>", "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>167</mn><mi mathvariant=\"normal\">&#960;</mi></mrow><mn>180</mn></mfrac></math></p>",
                                "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>163</mn><mi mathvariant=\"normal\">&#960;</mi></mrow><mn>180</mn></mfrac></math></p>", "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>163</mn><mi mathvariant=\"normal\">&#960;</mi></mrow><mn>360</mn></mfrac></math></p>"],
                    solution_en: "<p>12.(d)<br>Let initial angles are 3x + 17&deg;,4x + 17&deg; and 7x+ 17&deg;<br>Then, 14x + 51&deg; = 180&deg;<br>14x = 129&deg;<br>x = <math display=\"inline\"><mfrac><mrow><mn>129</mn><mo>&#176;</mo></mrow><mrow><mn>14</mn></mrow></mfrac></math> <br>Largest angle (7x + 17) = 7 &times; <math display=\"inline\"><mfrac><mrow><mn>129</mn><mo>&#176;</mo></mrow><mrow><mn>14</mn></mrow></mfrac></math> + 17 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>163</mn><mo>&#176;</mo></mrow><mn>2</mn></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>163</mn><mo>&#176;</mo></mrow><mn>2</mn></mfrac><mo>&#215;</mo><mfrac><mi mathvariant=\"normal\">&#960;</mi><mn>180</mn></mfrac><mo>=</mo><mfrac><mrow><mn>163</mn><mi mathvariant=\"normal\">&#960;</mi></mrow><mn>360</mn></mfrac></math> radian.</p>",
                    solution_hi: "<p>12.(d)<br>माना प्रारंभिक कोण 3x + 17&deg;,4x + 17&deg; और 7x + 17&deg; है&nbsp;<br>तो , 14x + 51&deg; = 180&deg;<br>14x = 129&deg;<br>x = <math display=\"inline\"><mfrac><mrow><mn>129</mn><mo>&#176;</mo></mrow><mrow><mn>14</mn></mrow></mfrac></math> <br>सबसे बड़ा कोण (7x + 17) = 7 &times; <math display=\"inline\"><mfrac><mrow><mn>129</mn><mo>&#176;</mo></mrow><mrow><mn>14</mn></mrow></mfrac></math> + 17 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>163</mn><mo>&#176;</mo></mrow><mn>2</mn></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>163</mn><mo>&#176;</mo></mrow><mn>2</mn></mfrac><mo>&#215;</mo><mfrac><mi mathvariant=\"normal\">&#960;</mi><mn>180</mn></mfrac><mo>=</mo><mfrac><mrow><mn>163</mn><mi mathvariant=\"normal\">&#960;</mi></mrow><mn>360</mn></mfrac></math> रेडियन</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "17",
                    question_en: "<p>13. The radii of the two circles are 7 cm and 4 cm. If the distance between their centres is 25 cm, then the length of the transverse common tangent is equal to:</p>",
                    question_hi: "<p>13. दो वृत्तोंकी त्रिज्याएं 7 cm और 4 cm हैं। यदि उनके केंद्रों के बीच की दूरी 25 cm है, तो अनुप्रस्थ उभयनिष्ठ स्पर्श रेखा (transverse common tangent) की लंबाई _______ होगी।</p>",
                    options_en: ["<p>6<math display=\"inline\"><msqrt><mn>11</mn></msqrt></math> cm</p>", "<p>6<math display=\"inline\"><msqrt><mn>12</mn></msqrt></math> cm</p>", 
                                "<p>6<math display=\"inline\"><msqrt><mn>14</mn></msqrt></math> cm</p>", "<p>6<math display=\"inline\"><msqrt><mn>13</mn></msqrt></math> cm</p>"],
                    options_hi: ["<p>6<math display=\"inline\"><msqrt><mn>11</mn></msqrt></math> cm</p>", "<p>6<math display=\"inline\"><msqrt><mn>12</mn></msqrt></math> cm</p>",
                                "<p>6<math display=\"inline\"><msqrt><mn>14</mn></msqrt></math> cm</p>", "<p>6<math display=\"inline\"><msqrt><mn>13</mn></msqrt></math> cm</p>"],
                    solution_en: "<p>13.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736760523661.png\" alt=\"rId13\" width=\"326\" height=\"164\"><br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">l</mi></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mi mathvariant=\"normal\">d</mi><mn>2</mn></msup><mo>-</mo><msup><mrow><mo>(</mo><msub><mi mathvariant=\"normal\">r</mi><mn>1</mn></msub><mo>+</mo><mo>&#160;</mo><msub><mi mathvariant=\"normal\">r</mi><mn>2</mn></msub><mo>)</mo></mrow><mn>2</mn></msup></msqrt></math><br>= <math display=\"inline\"><msqrt><msup><mrow><mn>25</mn></mrow><mrow><mn>2</mn></mrow></msup><mo>-</mo><mo>(</mo><mn>7</mn><mo>+</mo><mi>&#160;</mi><mn>4</mn><msup><mrow><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup></msqrt></math><br>= <math display=\"inline\"><msqrt><mn>625</mn><mo>-</mo><mn>121</mn></msqrt></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>504</mn></msqrt></math> = 6<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>14</mn></msqrt></math> cm</p>",
                    solution_hi: "<p>13.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736760523661.png\" alt=\"rId13\" width=\"326\" height=\"164\"><br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">l</mi></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mi mathvariant=\"normal\">d</mi><mn>2</mn></msup><mo>-</mo><msup><mrow><mo>(</mo><msub><mi mathvariant=\"normal\">r</mi><mn>1</mn></msub><mo>+</mo><mo>&#160;</mo><msub><mi mathvariant=\"normal\">r</mi><mn>2</mn></msub><mo>)</mo></mrow><mn>2</mn></msup></msqrt></math><br>= <math display=\"inline\"><msqrt><msup><mrow><mn>25</mn></mrow><mrow><mn>2</mn></mrow></msup><mo>-</mo><mo>(</mo><mn>7</mn><mo>+</mo><mi>&#160;</mi><mn>4</mn><msup><mrow><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup></msqrt></math><br>= <math display=\"inline\"><msqrt><mn>625</mn><mo>-</mo><mn>121</mn></msqrt></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>504</mn></msqrt></math> = 6<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>14</mn></msqrt></math> cm</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "17",
                    question_en: "<p>14. R wishes to use a stick of length 6 units as common internal tangent to two circles of radius two units and three units. What can be the maximum distance (in units) between the centres of the circle?</p>",
                    question_hi: "<p>14. R, दो इकाई और तीन इकाई त्रिज्या वाले दो वृत्तों की उभयनिष्ठ आंतरिक स्पर्श रेखा के रूप में 6 इकाई लंबाई की एक छड़ी का उपयोग करना चाहता है। वृत्त के केन्द्रों के बीच अधिकतम दूरी कितनी हो सकती है?</p>",
                    options_en: ["<p><math display=\"inline\"><msqrt><mn>67</mn></msqrt></math></p>", "<p><math display=\"inline\"><msqrt><mn>33</mn></msqrt></math></p>", 
                                "<p><math display=\"inline\"><msqrt><mn>37</mn></msqrt></math></p>", "<p><math display=\"inline\"><msqrt><mn>61</mn></msqrt></math></p>"],
                    options_hi: ["<p><math display=\"inline\"><msqrt><mn>67</mn></msqrt></math></p>", "<p><math display=\"inline\"><msqrt><mn>33</mn></msqrt></math></p>",
                                "<p><math display=\"inline\"><msqrt><mn>37</mn></msqrt></math></p>", "<p><math display=\"inline\"><msqrt><mn>61</mn></msqrt></math></p>"],
                    solution_en: "<p>14.(d) <br><strong>Concept used :</strong> <br>For maximum distance between the centres of the circle, we need to draw the transverse common tangent .<br>For the minimum distance between the centres of the circle , we need to draw the direct common tangent.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736760523762.png\" alt=\"rId14\" width=\"232\" height=\"132\"><br>Let the maximum distance between the centres be &lsquo;L&rsquo;<br>Transverse common tangent = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mi mathvariant=\"normal\">L</mi><mn>2</mn></msup><mo>-</mo><mo>(</mo><mi mathvariant=\"normal\">R</mi><mo>+</mo><mi mathvariant=\"normal\">r</mi><msup><mo>)</mo><mn>2</mn></msup></msqrt></math><br>6 = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mi mathvariant=\"normal\">L</mi><mn>2</mn></msup><mo>-</mo><mo>(</mo><mn>3</mn><mo>+</mo><mn>2</mn><msup><mo>)</mo><mn>2</mn></msup></msqrt></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mi mathvariant=\"normal\">L</mi><mn>2</mn></msup><mo>-</mo><msup><mn>5</mn><mn>2</mn></msup></msqrt></math><br>36 = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi mathvariant=\"normal\">L</mi><mn>2</mn></msup></math> - 25<br>61 = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi mathvariant=\"normal\">L</mi><mn>2</mn></msup></math><br><math display=\"inline\"><mo>&#8658;</mo></math> L = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>61</mn></msqrt></math> Units</p>",
                    solution_hi: "<p>14.(d) <br><strong>प्रयुक्त अवधारणा:</strong> <br>वृत्त के केन्द्रों के बीच अधिकतम दूरी के लिए, हमें अनुप्रस्थ उभयनिष्ठ स्पर्श रेखा खींचनी होगी।<br>वृत्त के केन्द्रों के बीच न्यूनतम दूरी के लिए, हमें सीधी उभयनिष्ठ स्पर्शरेखा खींचनी होगी।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736760523762.png\" alt=\"rId14\" width=\"232\" height=\"132\"><br>माना केन्द्रों के बीच अधिकतम दूरी \'L\' है<br>अनुप्रस्थ उभयनिष्ठ स्पर्शरेखा = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mi mathvariant=\"normal\">L</mi><mn>2</mn></msup><mo>-</mo><mo>(</mo><mi mathvariant=\"normal\">R</mi><mo>+</mo><mi mathvariant=\"normal\">r</mi><msup><mo>)</mo><mn>2</mn></msup></msqrt></math><br>6 = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mi mathvariant=\"normal\">L</mi><mn>2</mn></msup><mo>-</mo><mo>(</mo><mn>3</mn><mo>+</mo><mn>2</mn><msup><mo>)</mo><mn>2</mn></msup></msqrt></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mi mathvariant=\"normal\">L</mi><mn>2</mn></msup><mo>-</mo><msup><mn>5</mn><mn>2</mn></msup></msqrt></math><br>36 = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi mathvariant=\"normal\">L</mi><mn>2</mn></msup></math> - 25<br>61 = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi mathvariant=\"normal\">L</mi><mn>2</mn></msup></math><br><math display=\"inline\"><mo>&#8658;</mo></math> L = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>61</mn></msqrt></math> इकाई<strong id=\"docs-internal-guid-1d6ede02-7fff-cd00-d370-658fb382c88c\"> </strong></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "misc",
                    question_en: "<p>15. In an isosceles triangle ABC, AB = AC. D is a point inside the triangle such that &ang;BAD = 20&deg; = &ang;DCB, &ang;CAD = 80&deg;. The value of &ang;ABC is:</p>",
                    question_hi: "<p>15. एक समद्विबाहु त्रिभुज ABC में, AB = AC है। त्रिभुज के भीतर एक बिंदु D इस प्रकार है कि &ang;BAD = 20&deg; = &ang;DCB, &ang;CAD = 80&deg; है। &ang;ABC का मान_______है।</p>",
                    options_en: ["<p>20&deg;</p>", "<p>25&deg;</p>", 
                                "<p>15&deg;</p>", "<p>40&deg;</p>"],
                    options_hi: ["<p>20&deg;</p>", "<p>25&deg;</p>",
                                "<p>15&deg;</p>", "<p>40&deg;</p>"],
                    solution_en: "<p>15.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736760523972.png\" alt=\"rId15\" width=\"200\" height=\"219\"><br>&ang;A = 20 + 80 = 100&deg;<br>Let &ang;B and &ang;C be x<br>Then, &ang;A + &ang;B + &ang;C = 180&deg;<br><math display=\"inline\"><mo>&#8658;</mo></math> 100&deg; + 2x = 180&deg;<br><math display=\"inline\"><mo>&#8658;</mo></math> x = 40&deg;<br>Hence, &ang;ABC = 40&deg;</p>",
                    solution_hi: "<p>15.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736760523972.png\" alt=\"rId15\" width=\"200\" height=\"219\"><br>&ang;A = 20 + 80 = 100&deg;<br>माना प्रत्येक &ang;B और &ang;C का मान x&nbsp;है <br>फिर, &ang;A + &ang;B + &ang;C = 180&deg;<br><math display=\"inline\"><mo>&#8658;</mo></math> 100&deg; + 2x = 180&deg;<br><math display=\"inline\"><mo>&#8658;</mo></math> x = 40&deg;<br>अतः &ang;ABC = 40&deg;</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>