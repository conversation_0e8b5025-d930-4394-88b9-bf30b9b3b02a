<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">15:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 15</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">15</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 15 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 0,
                end: 13
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 14,
                end: 14
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "17",
                    question_en: "<p>1. Three numbers A, B and Care in the ratio of 5 : 4 : 3 respectively. If C is 18 less than the sum of A and B, then what is the value of B ?</p>",
                    question_hi: "<p>1. तीन संख्याएं A, B और C क्रमशः 5 : 4 : 3 के अनुपात में हैं। यदि C, A और B के योग से 18 कम है, तो का मान कितना है?</p>",
                    options_en: ["<p>18</p>", "<p>12</p>", 
                                "<p>15</p>", "<p>9</p>"],
                    options_hi: ["<p>18</p>", "<p>12</p>",
                                "<p>15</p>", "<p>9</p>"],
                    solution_en: "<p>1.(b) Three number A, B, and C are respectively 5<math display=\"inline\"><mi>x</mi></math> , 4x and 3x <br>Sum of A and B = 9<math display=\"inline\"><mi>x</mi></math> <br>According to questions, <br>9<math display=\"inline\"><mi>x</mi></math> - 3x = 18 <br><math display=\"inline\"><mi>x</mi></math> = 3 <br>Hence , the value of B = 3 <math display=\"inline\"><mo>&#215;</mo></math> 4 = 12</p>",
                    solution_hi: "<p>1.(b) तीन संख्याएं A, B, और C क्रमश :&nbsp;5<math display=\"inline\"><mi>x</mi></math> , 4 x , 3x<br>A और B का योग = 9<math display=\"inline\"><mi>x</mi></math><br>प्रश्नों के अनुसार,<br>9<math display=\"inline\"><mi>x</mi></math> - 3x = 18 <br><math display=\"inline\"><mi>x</mi></math> = 3 <br>अतः, B का मान = 3 <math display=\"inline\"><mo>&#215;</mo></math> 4 = 12</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "2",
                    section: "17",
                    question_en: "<p>2. The ratio of marks of two candidates M and N is 10 : 11. If M\'s marks are 200, then what are N\'s marks?</p>",
                    question_hi: "<p>2. दो परीक्षार्थियों M और N के अंकों का अनुपात 10 : 11 हैं। यदि M के अंक 200 हैं, तो N के अंक कितने हैं?</p>",
                    options_en: ["<p>200</p>", "<p>260</p>", 
                                "<p>240</p>", "<p>220</p>"],
                    options_hi: ["<p>200</p>", "<p>260</p>",
                                "<p>240</p>", "<p>220</p>"],
                    solution_en: "<p>2.(d) According to question,<br><math display=\"inline\"><mo>&#8658;</mo></math> 10 unit = 200<br>Then 11 unit = <math display=\"inline\"><mfrac><mrow><mn>200</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>11</mn></mrow><mrow><mn>10</mn></mrow></mfrac></math> = 220<br>Therefore, marks of N = 220</p>",
                    solution_hi: "<p>2.(d) प्रश्न के अनुसार,<br><math display=\"inline\"><mo>&#8658;</mo></math> 10 इकाई = 200<br>तब 11 इकाई = <math display=\"inline\"><mfrac><mrow><mn>200</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>11</mn></mrow><mrow><mn>10</mn></mrow></mfrac></math> = 220<br>इसलिए, N के अंक = 220</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "3",
                    section: "17",
                    question_en: "<p>3. Two numbers are in the ratio 49 : 64. If both the numbers are increased by 10, their ratio becomes 54 : 69. What is the difference between the two numbers?</p>",
                    question_hi: "<p>3. दो संख्याएं 49 : 64 के अनुपात में हैं। यदि दोनों संख्याओं में 10 की वृद्धि की जाती है, तो उनका अनुपात 54 : 69 हो जाता है। दोनों संख्याओं के बीच का अंतर कितना है?</p>",
                    options_en: ["<p>60</p>", "<p>30</p>", 
                                "<p>15</p>", "<p>95</p>"],
                    options_hi: ["<p>60</p>", "<p>30</p>",
                                "<p>15</p>", "<p>95</p>"],
                    solution_en: "<p>3.(b) Initial ratio <math display=\"inline\"><mo>&#8594;</mo></math> 49 : 64<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; New ratio <math display=\"inline\"><mo>&#8594;</mo></math> 54 : 69<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;___________________<br>Because the difference in both condition is equal, (54 - 49 = 69 - 64 = 5) <br>So,<br><math display=\"inline\"><mo>&#8658;</mo></math> 5 unit = 10<br><math display=\"inline\"><mo>&#8658;</mo></math> (64 - 49) unit = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>10</mn><mn>5</mn></mfrac></math>&nbsp;&times; 15 = 30</p>",
                    solution_hi: "<p>3.(b) प्रारंभिक अनुपात <math display=\"inline\"><mo>&#8594;</mo></math> 49 : 64<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;नया अनुपात <math display=\"inline\"><mo>&#8594;</mo></math> 54 : 69<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; ___________________<br>क्योंकि दोनों स्थितियों में अंतर बराबर है, (54 - 49 = 69 - 64 = 5) <br>इसलिए,<br><math display=\"inline\"><mo>&#8658;</mo></math> 5 इकाई = 10<br><math display=\"inline\"><mo>&#8658;</mo></math> (64 - 49) इकाई =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>10</mn><mn>5</mn></mfrac></math> &times; 15 = 30</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "4",
                    section: "17",
                    question_en: "<p>4. If A : B = 7 : 4, then what is the value of (A + B) : (A - B) ?</p>",
                    question_hi: "<p>4. यदि A : B = 7 : 4 है, तो (A + B) : (A - B) का मान कितना है?</p>",
                    options_en: ["<p>11 : 4</p>", "<p>11 : 6</p>", 
                                "<p>11 : 3</p>", "<p>11 : 5</p>"],
                    options_hi: ["<p>11 : 4</p>", "<p>11 : 6</p>",
                                "<p>11 : 3</p>", "<p>11 : 5</p>"],
                    solution_en: "<p>4.(c) (A + B) : (A - B)<br>= (7 + 4) : (7 - 4)<br>= 11 : 3</p>",
                    solution_hi: "<p>4.(c) (A + B) : (A - B)<br>= (7 + 4) : (7 - 4)<br>= 11 : 3</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "5",
                    section: "17",
                    question_en: "<p>5. If (5m + n) : (5m - n) = 5 : 2, then what is the value of m : n ?</p>",
                    question_hi: "<p>5. यदि (5m + n) : (5m - n) = 5 : 2 तो m : n का मान कितना है?</p>",
                    options_en: ["<p>9 : 17</p>", "<p>7 : 15</p>", 
                                "<p>5 : 9</p>", "<p>8 : 9</p>"],
                    options_hi: ["<p>9 : 17</p>", "<p>7 : 15</p>",
                                "<p>5 : 9</p>", "<p>8 : 9</p>"],
                    solution_en: "<p>5.(b)<br><math display=\"inline\"><mfrac><mrow><mo>(</mo><mn>5</mn><mi>m</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>n</mi><mo>)</mo></mrow><mrow><mo>(</mo><mn>5</mn><mi>m</mi><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mi>n</mi><mo>)</mo></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>2</mn></mfrac></math><br>10m + 2n = 25m - 5n<br>7n = 15m <math display=\"inline\"><mo>&#8658;</mo></math> m : n = 7 : 15</p>",
                    solution_hi: "<p>5.(b)<br><math display=\"inline\"><mfrac><mrow><mo>(</mo><mn>5</mn><mi>m</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>n</mi><mo>)</mo></mrow><mrow><mo>(</mo><mn>5</mn><mi>m</mi><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mi>n</mi><mo>)</mo></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>2</mn></mfrac></math><br>10m + 2n = 25m - 5n<br>7n = 15m <math display=\"inline\"><mo>&#8658;</mo></math> m : n = 7 : 15</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "6",
                    section: "17",
                    question_en: "<p>6. What number should be added to each of 12, 46, 11 and 43 so that the resulting numbers will be in proportion?</p>",
                    question_hi: "<p>6. 12, 46, 11 और 43 में से प्रत्येक में कौन-सी संख्या जोड़ी जानी चाहिए ताकि परिणामी संख्याएं अनुपात में हो?</p>",
                    options_en: ["<p>3</p>", "<p>5</p>", 
                                "<p>4</p>", "<p>2</p>"],
                    options_hi: ["<p>3</p>", "<p>5</p>",
                                "<p>4</p>", "<p>2</p>"],
                    solution_en: "<p>6.(b)<br>Let the number be a.<br>According to question,<br><math display=\"inline\"><mfrac><mrow><mn>12</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>a</mi></mrow><mrow><mn>46</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>a</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>11</mn><mo>+</mo><mi>a</mi></mrow><mrow><mn>43</mn><mo>+</mo><mi>a</mi></mrow></mfrac></math><br>516 + 55a + a&sup2; = 506 + 57a + a&sup2;<br>2a = 10<br>a = 5</p>",
                    solution_hi: "<p>6.(b)<br>माना संख्या a है।<br>प्रश्न के अनुसार,<br><math display=\"inline\"><mfrac><mrow><mn>12</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>a</mi></mrow><mrow><mn>46</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>a</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>11</mn><mo>+</mo><mi>a</mi></mrow><mrow><mn>43</mn><mo>+</mo><mi>a</mi></mrow></mfrac></math><br>516 + 55a + a&sup2; = 506 + 57a + a&sup2;<br>2a = 10<br>a = 5</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "7",
                    section: "17",
                    question_en: "<p>7. The numbers are in the ratio of 6 : 5. If their sum is 22, then what is the sum of the squares of the two numbers ?</p>",
                    question_hi: "<p>7. दो संख्याएं 6 : 5 के अनुपात में हैं। यदि उनका योग 22 है, तो दोनों संख्याओं के वर्गों का योग कितना है ?</p>",
                    options_en: ["<p>234</p>", "<p>244</p>", 
                                "<p>196</p>", "<p>256</p>"],
                    options_hi: ["<p>234</p>", "<p>244</p>",
                                "<p>196</p>", "<p>256</p>"],
                    solution_en: "<p>7.(b)<br>Let the numbers be 6a and 5a.<br>According to question,<br>6a + 5a = 22, a = 2<br>Then the numbers will be 12 and 10.<br>Therefore, required sum = (12)&sup2; + (10)&sup2; = 244.</p>",
                    solution_hi: "<p>7.(b)<br>माना संख्याएँ 6a और 5a हैं।<br>प्रश्न के अनुसार,<br>6a + 5a = 22, a = 2<br>तो संख्याएँ 12 और 10 होगी।<br>अत: अभीष्ट योग = (12)&sup2; + (10)&sup2; = 244.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "8",
                    section: "17",
                    question_en: "<p>8. If A : B : C = 2 : 3 : 5, then what is the value of (A + B) : (B + C) : (C + A) ?</p>",
                    question_hi: "<p>8. यदि A : B : C = 2 : 3 : 5, है, तो (A + B) : (B + C) : (C + A) का मान कितना है ?</p>",
                    options_en: ["<p>5 : 8 : 8</p>", "<p>7 : 6 : 5</p>", 
                                "<p>5 : 8 : 7</p>", "<p>7 : 8 : 7</p>"],
                    options_hi: ["<p>5 : 8 : 8</p>", "<p>7 : 6 : 5</p>",
                                "<p>5 : 8 : 7</p>", "<p>7 : 8 : 7</p>"],
                    solution_en: "<p>8.(c)<br>Let A, B and C be 2, 3 and 5 units respectively.<br>(A + B) : (B + C) : (C + A) = (2 + 3) : (3 + 5) : (5 + 2) = 5 : 8 : 7</p>",
                    solution_hi: "<p>8.(c)<br>माना A, B और C क्रमशः 2, 3 और 5 इकाई हैं।<br>(A + B) : (B + C) : (C + A) = (2 + 3) : (3 + 5) : (5 + 2) = 5 : 8 : 7</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "9",
                    section: "17",
                    question_en: "<p>9. L\'s income is 125 percent of M\'s income and L\'s expenditure is 120 percent of M\'s expenditure. If L\'s income is 180 percent of M\'s expenditure, then what is the respective ratio of L\'s savings and M\'s savings ?</p>",
                    question_hi: "<p>9. L की आय, M की आय का 125 प्रतिशत है और L का व्यय, M के व्यय का 120 प्रतिशत है। यदि L की आय, M के व्यय का 180 प्रतिशत है, तो L की बचत और M की बचत का क्रमशः अनुपात कितना है ?</p>",
                    options_en: ["<p>17 : 23</p>", "<p>12 : 5</p>", 
                                "<p>15 : 11</p>", "<p>5 : 9</p>"],
                    options_hi: ["<p>17 : 23</p>", "<p>12 : 5</p>",
                                "<p>15 : 11</p>", "<p>5 : 9</p>"],
                    solution_en: "<p>9.(c) according to question, <br><math display=\"inline\"><mfrac><mrow><mi>L</mi><mi>\'</mi><mi>s</mi><mi>&#160;</mi><mi>i</mi><mi>n</mi><mi>c</mi><mi>o</mi><mi>m</mi><mi>e</mi><mi>&#160;</mi></mrow><mrow><mi>M</mi><mi>\'</mi><mi>s</mi><mi>&#160;</mi><mi>i</mi><mi>n</mi><mi>c</mi><mi>o</mi><mi>m</mi><mi>e</mi><mi>&#160;</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>125</mn><mn>100</mn></mfrac><mo>=</mo><mfrac><mrow><mn>5</mn><mo>&#215;</mo><mn>9</mn></mrow><mrow><mn>4</mn><mo>&#215;</mo><mn>9</mn></mrow></mfrac><mo>=</mo><mfrac><mn>45</mn><mn>36</mn></mfrac></math><br><math display=\"inline\"><mfrac><mrow><mi>L</mi><mi>\'</mi><mi>s</mi><mi>&#160;</mi><mi>e</mi><mi>x</mi><mi>p</mi><mi>e</mi><mi>n</mi><mi>d</mi><mi>i</mi><mi>t</mi><mi>u</mi><mi>r</mi><mi>e</mi><mi>&#160;</mi></mrow><mrow><mi>M</mi><mi>\'</mi><mi>s</mi><mi>&#160;</mi><mi>e</mi><mi>x</mi><mi>p</mi><mi>e</mi><mi>n</mi><mi>d</mi><mi>i</mi><mi>t</mi><mi>u</mi><mi>r</mi><mi>e</mi><mi>&#160;</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>120</mn><mn>100</mn></mfrac><mo>=</mo><mfrac><mrow><mn>6</mn><mo>&#215;</mo><mn>5</mn></mrow><mrow><mn>5</mn><mo>&#215;</mo><mn>5</mn></mrow></mfrac><mo>=</mo><mfrac><mn>30</mn><mn>25</mn></mfrac></math><br><math display=\"inline\"><mfrac><mrow><mi>L</mi><mi>\'</mi><mi>s</mi><mi>&#160;</mi><mi>I</mi><mi>n</mi><mi>c</mi><mi>o</mi><mi>m</mi><mi>e</mi><mi>&#160;</mi><mi>&#160;</mi></mrow><mrow><mi>M</mi><mi>\'</mi><mi>s</mi><mi>&#160;</mi><mi>e</mi><mi>x</mi><mi>p</mi><mi>e</mi><mi>n</mi><mi>d</mi><mi>i</mi><mi>t</mi><mi>u</mi><mi>r</mi><mi>e</mi><mi>&#160;</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>180</mn><mn>100</mn></mfrac><mo>=</mo><mfrac><mrow><mn>9</mn><mo>&#215;</mo><mn>5</mn></mrow><mrow><mn>5</mn><mo>&#215;</mo><mn>5</mn></mrow></mfrac><mo>=</mo><mfrac><mn>45</mn><mn>25</mn></mfrac></math><br>Therefore , <math display=\"inline\"><mfrac><mrow><mi>L</mi><mi>\'</mi><mi>s</mi><mi>&#160;</mi><mi>s</mi><mi>a</mi><mi>v</mi><mi>i</mi><mi>n</mi><mi>g</mi><mi>s</mi></mrow><mrow><mi>M</mi><mi>\'</mi><mi>s</mi><mi>&#160;</mi><mi>s</mi><mi>a</mi><mi>v</mi><mi>i</mi><mi>n</mi><mi>g</mi><mi>s</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>45</mn><mo>-</mo><mn>30</mn></mrow><mrow><mn>36</mn><mo>-</mo><mn>25</mn></mrow></mfrac></math>=&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>15</mn><mn>11</mn></mfrac></math></p>",
                    solution_hi: "<p>9.(c) प्रश्न के अनुसार,<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>L</mi><mi>&#160;</mi><mi>&#2325;&#2368;</mi><mo>&#160;</mo><mo>&#160;</mo><mi>&#2310;</mi><mi>&#2351;</mi><mi>&#160;</mi></mrow><mrow><mi>M</mi><mi>&#160;</mi><mi>&#2325;&#2368;</mi><mo>&#160;</mo><mo>&#160;</mo><mi>&#2310;</mi><mi>&#2351;</mi><mi>&#160;</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>125</mn><mn>100</mn></mfrac><mo>=</mo><mfrac><mrow><mn>5</mn><mo>&#215;</mo><mn>9</mn></mrow><mrow><mn>4</mn><mo>&#215;</mo><mn>9</mn></mrow></mfrac><mo>=</mo><mfrac><mn>45</mn><mn>36</mn></mfrac></math><br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>L</mi><mo>&#160;</mo><mi>&#2325;&#2366;</mi><mo>&#160;</mo><mo>&#160;</mo><mi>&#2310;&#2351;</mi><mo>&#160;</mo><mi>&#160;</mi></mrow><mrow><mi>M</mi><mi>&#160;</mi><mi>&#2325;&#2366;</mi><mo>&#160;</mo><mo>&#160;</mo><mi>&#2310;&#2351;</mi><mo>&#160;</mo></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>120</mn><mn>100</mn></mfrac><mo>=</mo><mfrac><mrow><mn>6</mn><mo>&#215;</mo><mn>5</mn></mrow><mrow><mn>5</mn><mo>&#215;</mo><mn>5</mn></mrow></mfrac><mo>=</mo><mfrac><mn>30</mn><mn>25</mn></mfrac></math><br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>L</mi><mi>&#160;</mi><mi>&#2325;&#2368;</mi><mo>&#160;</mo><mo>&#160;</mo><mi>&#2310;</mi><mi>&#2351;</mi><mi>&#160;</mi></mrow><mrow><mi>M</mi><mi>&#160;</mi><mi>&#2325;&#2368;</mi><mo>&#160;</mo><mo>&#160;</mo><mi>&#2310;&#2351;</mi><mo>&#160;</mo><mi>&#160;</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>180</mn><mn>100</mn></mfrac><mo>=</mo><mfrac><mrow><mn>9</mn><mo>&#215;</mo><mn>5</mn></mrow><mrow><mn>5</mn><mo>&#215;</mo><mn>5</mn></mrow></mfrac><mo>=</mo><mfrac><mn>45</mn><mn>25</mn></mfrac></math><br>Therefore , <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>L</mi><mi>&#160;</mi><mi>&#2325;&#2368;</mi><mo>&#160;</mo><mi>&#160;</mi><mi>&#2348;</mi><mi>&#2330;</mi><mi>&#2340;</mi></mrow><mrow><mi>M</mi><mi>&#160;</mi><mi>&#2325;&#2368;</mi><mo>&#160;</mo><mi>&#2348;</mi><mi>&#2330;</mi><mi>&#2340;</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>45</mn><mo>-</mo><mn>30</mn></mrow><mrow><mn>36</mn><mo>-</mo><mn>25</mn></mrow></mfrac></math>=&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>15</mn><mn>11</mn></mfrac></math></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "10",
                    section: "17",
                    question_en: "<p>10. Find the mean proportion between 21 and 189.</p>",
                    question_hi: "<p>10. 21 और 189 का माध्यानुपाती ज्ञात कीजिए।</p>",
                    options_en: ["<p>66</p>", "<p>68</p>", 
                                "<p>62</p>", "<p>63</p>"],
                    options_hi: ["<p>66</p>", "<p>68</p>",
                                "<p>62</p>", "<p>63</p>"],
                    solution_en: "<p>10.(d) mean proportion = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>21</mn><mo>&#215;</mo><mn>189</mn></msqrt></math> <br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>21</mn><mo>&#215;</mo><mn>21</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>9</mn></msqrt></math> = 63</p>",
                    solution_hi: "<p>10.(d) माध्यानुपाती = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>21</mn><mo>&#215;</mo><mn>189</mn></msqrt></math> <br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>21</mn><mo>&#215;</mo><mn>21</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>9</mn></msqrt></math> = 63</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "11",
                    section: "17",
                    question_en: "<p>11. If X : Y = 1 : 5, then what is the value of X : (X + Y)?</p>",
                    question_hi: "<p>11. यदि X : Y = 1 : 5 है, तो X : (X + Y) का मान कितना है ?</p>",
                    options_en: ["<p>5 : 6</p>", "<p>1 : 6</p>", 
                                "<p>6 : 1</p>", "<p>1 : 5</p>"],
                    options_hi: ["<p>5 : 6</p>", "<p>1 : 6</p>",
                                "<p>6 : 1</p>", "<p>1 : 5</p>"],
                    solution_en: "<p>11.(b) given, <math display=\"inline\"><mfrac><mrow><mi>X</mi></mrow><mrow><mi>Y</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1</mn><mo>&#160;</mo></mrow><mrow><mn>5</mn><mo>&#160;</mo></mrow></mfrac></math><br>Hence , <math display=\"inline\"><mfrac><mrow><mi>X</mi></mrow><mrow><mo>(</mo><mi>X</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>Y</mi><mo>)</mo></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1</mn><mo>&#160;</mo></mrow><mrow><mn>1</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>5</mn><mo>&#160;</mo></mrow></mfrac><mo>=</mo><mfrac><mrow><mn>1</mn><mo>&#160;</mo></mrow><mrow><mn>6</mn><mo>&#160;</mo></mrow></mfrac></math></p>",
                    solution_hi: "<p>11.(b) दिया है,&nbsp;<math display=\"inline\"><mfrac><mrow><mi>X</mi></mrow><mrow><mi>Y</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1</mn><mo>&#160;</mo></mrow><mrow><mn>5</mn><mo>&#160;</mo></mrow></mfrac></math><br>अतः , <math display=\"inline\"><mfrac><mrow><mi>X</mi></mrow><mrow><mo>(</mo><mi>X</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>Y</mi><mo>)</mo></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1</mn><mo>&#160;</mo></mrow><mrow><mn>1</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>5</mn><mo>&#160;</mo></mrow></mfrac><mo>=</mo><mfrac><mrow><mn>1</mn><mo>&#160;</mo></mrow><mrow><mn>6</mn><mo>&#160;</mo></mrow></mfrac></math></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "12",
                    section: "17",
                    question_en: "<p>12. If the cost of 8 apples and 9 mangoes is same as the cost of 10 apples and 6 mangoes, then what is the ratio of the price of a mango and the price of an apple?</p>",
                    question_hi: "<p>12. यदि 8 सेब और 9 आम की कीमत 10 सेब और 6 आम की कीमत के बराबर है, तो एक आम की कीमत और एक सेब की कीमत का अनुपात कितना है?</p>",
                    options_en: ["<p>2 : 3</p>", "<p>3 : 2</p>", 
                                "<p>1 : 2</p>", "<p>2 : 5</p>"],
                    options_hi: ["<p>2 : 3</p>", "<p>3 : 2</p>",
                                "<p>1 : 2</p>", "<p>2 : 5</p>"],
                    solution_en: "<p>12.(a) <br>Let the price of each apple and mango be a and m respectively.<br>According to question,<br>8a + 9m = 10a + 6m<br>3m = 2a<br>m : a = 2 : 3.</p>",
                    solution_hi: "<p>12.(a) <br>माना प्रत्येक सेब और आम की कीमत क्रमशः a और m है।<br>प्रश्न के अनुसार,<br>8a + 9m = 10a + 6m<br>3m = 2a<br>m : a = 2 : 3.</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "13",
                    section: "17",
                    question_en: "<p>13. If a : b = 7 : 4, then what is the value of (6a + b) : (6a - b)?</p>",
                    question_hi: "<p>13. यदि a : b = 7 : 4 है, तो (6a + b) : (6a - b) का मान कितना है?</p>",
                    options_en: ["<p>17 : 13</p>", "<p>25 : 19</p>", 
                                "<p>33 : 28</p>", "<p>23 : 19</p>"],
                    options_hi: ["<p>17 : 13</p>", "<p>25 : 19</p>",
                                "<p>33 : 28</p>", "<p>23 : 19</p>"],
                    solution_en: "<p>13.(d) (6a + b) : (6a - b)<br><math display=\"inline\"><mo>&#8658;</mo></math> (6 &times; 7 + 4) : (6 &times; 7 - 4)<br><math display=\"inline\"><mo>&#8658;</mo></math> 46 : 38<br><math display=\"inline\"><mo>&#8658;</mo></math> 23 : 19</p>",
                    solution_hi: "<p>13.(d) (6a + b) : (6a - b)<br><math display=\"inline\"><mo>&#8658;</mo></math> (6 &times; 7 + 4) : (6 &times; 7 - 4)<br><math display=\"inline\"><mo>&#8658;</mo></math> 46 : 38<br><math display=\"inline\"><mo>&#8658;</mo></math> 23 : 19</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "14",
                    section: "17",
                    question_en: "<p>14. The ratio of two numbers is 5 : 6. If their product is 1920, then what is the sum of both the numbers?</p>",
                    question_hi: "<p>14. दो संख्याओं का अनुपात 5 : 6 है। यदि उनका गुणनफल 1920 है, तो दोनों संख्याओं का योग कितना है?</p>",
                    options_en: ["<p>77</p>", "<p>99</p>", 
                                "<p>84</p>", "<p>88</p>"],
                    options_hi: ["<p>77</p>", "<p>99</p>",
                                "<p>84</p>", "<p>88</p>"],
                    solution_en: "<p>14.(d) Let the numbers be 5<math display=\"inline\"><mi>x</mi></math> and 6x<br>According to question,<br><math display=\"inline\"><mo>&#8658;</mo></math> 5x &times; 6x = 1920<br><math display=\"inline\"><mo>&#8658;</mo></math> x<sup>2</sup> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1920</mn><mn>30</mn></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math> x = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>64</mn><mo>&#160;</mo></msqrt></math>&nbsp;&rArr; x = 8<br>Required sum of both number = 5<math display=\"inline\"><mi>x</mi></math> + 6x = 40 + 48 = 88</p>",
                    solution_hi: "<p>14.(d) माना संख्याएँ 5<math display=\"inline\"><mi>x</mi></math> और 6x हैं<br>प्रश्न के अनुसार,<br><math display=\"inline\"><mo>&#8658;</mo></math> 5x &times; 6x = 1920<br><math display=\"inline\"><mo>&#8658;</mo></math> x<sup>2</sup> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1920</mn><mn>30</mn></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math> x = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>64</mn><mo>&#160;</mo></msqrt></math> &rArr; x = 8<br>दोनों संख्याओं का योग = 5<math display=\"inline\"><mi>x</mi></math> + 6x = 40 + 48 = 88</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "15",
                    section: "misc",
                    question_en: "<p>15. The ratio between two numbers is 7 : 8. If each number is increased by 4, the ratio becomes 9 : 10, then find the difference between the numbers.</p>",
                    question_hi: "<p>15. दो संख्याओं के बीच का अनुपात 7 : 8 है। यदि प्रत्येक संख्या में 4 की वृद्धि की जाती है, तो अनुपात 9 : 10 हो जाता है, तो संख्याओं के बीच का अंतर ज्ञात कीजिए।</p>",
                    options_en: ["<p>10</p>", "<p>4</p>", 
                                "<p>8</p>", "<p>2</p>"],
                    options_hi: ["<p>10</p>", "<p>4</p>",
                                "<p>8</p>", "<p>2</p>"],
                    solution_en: "<p>15.(d)<br>Let numbers be 7<math display=\"inline\"><mi>x</mi></math> and 8x respectively,<br>According to the question,<br><math display=\"inline\"><mfrac><mrow><mn>7</mn><mi>x</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>4</mn></mrow><mrow><mn>8</mn><mi>x</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>4</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>9</mn><mn>10</mn></mfrac></math><br>70<math display=\"inline\"><mi>x</mi></math> + 40 = 72x + 36<br>2<math display=\"inline\"><mi>x</mi></math> = 4 &rArr; x = 2<br>Difference between numbers = 8<math display=\"inline\"><mi>x</mi></math> - 7x = x<br>Hence, required difference = 2</p>",
                    solution_hi: "<p>15.(d)<br>माना संख्याएँ क्रमशः 7<math display=\"inline\"><mi>x</mi></math> और 8x हैं,<br>प्रश्न के अनुसार,<br><math display=\"inline\"><mfrac><mrow><mn>7</mn><mi>x</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>4</mn></mrow><mrow><mn>8</mn><mi>x</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>4</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>9</mn><mn>10</mn></mfrac></math><br>70<math display=\"inline\"><mi>x</mi></math> + 40 = 72x + 36<br>2<math display=\"inline\"><mi>x</mi></math> = 4 &rArr; x = 2<br>संख्याओं के बीच अंतर = 8<math display=\"inline\"><mi>x</mi></math> - 7x = x<br>अत: अभीष्ट अंतर = 2</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>