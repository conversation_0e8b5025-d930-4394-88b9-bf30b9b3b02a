<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">12:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 25</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">25</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 12 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["7"] = {
                name: "English",
                start: 0,
                end: 23
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="7">English</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 24,
                end: 24
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "7",
                    question_en: "1. The following sentence has been divided into three segments. One of them may contain an error. Select the option that has the segment with the error. If you don’t find any error, select ‘No error’ as your answer. <br />Mr. Ali’s shop / is further to / the hospital.",
                    question_hi: "1. The following sentence has been divided into three segments. One of them may contain an error. Select the option that has the segment with the error. If you don’t find any error, select ‘No error’ as your answer. <br />Mr. Ali’s shop / is further to / the hospital. ",
                    options_en: [" is further to ", " No error   ", 
                                " Mr. Ali’s shop   ", " the hospital. "],
                    options_hi: [" is further to ", " No error   ",
                                " Mr. Ali’s shop   ", " the hospital. "],
                    solution_en: "1.(a) is further to<br />‘Farther than’ will be used instead of ‘further to’. ‘Further’ is used for metaphorical distance where ‘farther’ is used for physical distance. The given sentence is talking about the physical distance between Mr. Ali’s shop and the hospital. Hence, ‘farther than’ is the most appropriate answer.",
                    solution_hi: "1.(a) is further to<br />‘Further to’ के स्थान पर ‘Farther than’ का प्रयोग होगा। ‘Further’ का प्रयोग metaphorical distance के लिए किया जाता है, जबकि ‘farther’ का प्रयोग physical distance के लिए किया जाता है। दिया गया sentence,  Mr. Ali की दुकान और अस्पताल के बीच की physical distance के बारे में बात कर रहा है। अतः, ‘farther than’ सबसे उपयुक्त उत्तर है।",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "7",
                    question_en: "<p>2. Select the most appropriate synonym of the underlined word in the given sentence. <br>She is a very <span style=\"text-decoration: underline;\">creative</span> artist and one can see that in her paintings.</p>",
                    question_hi: "<p>2. Select the most appropriate synonym of the underlined word in the given sentence. <br>She is a very <span style=\"text-decoration: underline;\">creative </span>artist and one can see that in her paintings.</p>",
                    options_en: ["<p>Innovative</p>", "<p>Likeable</p>", 
                                "<p>Pedantic</p>", "<p>Good</p>"],
                    options_hi: ["<p>Innovative</p>", "<p>Likeable</p>",
                                "<p>Pedantic</p>", "<p>Good</p>"],
                    solution_en: "<p>2.(a) <strong>Innovative</strong>- introducing new ideas or methods.<br><strong>Creative</strong>- having the ability to produce original ideas or things.<br><strong>Likeable</strong>- easy to like.<br><strong>Pedantic</strong>- overly concerned with minor details or rules.<br><strong>Good</strong>- morally right or beneficial.</p>",
                    solution_hi: "<p>2.(a) <strong>Innovative </strong>(नवीन)- introducing new ideas or methods.<br><strong>Creative </strong>(सृजनात्मक)- having the ability to produce original ideas or things.<br><strong>Likeable </strong>(रुचिकर)- easy to like.<br><strong>Pedantic </strong>(रूढ़िवादी)- overly concerned with minor details or rules.<br><strong>Good </strong>(उपयुक्त)- morally right or beneficial.</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "7",
                    question_en: "3. Parts of a sentence are given below in jumbled order. Arrange the parts in the correct order to form a meaningful sentence. <br />(A) a lot <br />(B) my daily routine <br />(C) i have missed <br />(D) since my retirement <br />(E) at the workplace ",
                    question_hi: "3. Parts of a sentence are given below in jumbled order. Arrange the parts in the correct order to form a meaningful sentence. <br />(A) a lot <br />(B) my daily routine <br />(C) i have missed <br />(D) since my retirement <br />(E) at the workplace  ",
                    options_en: [" CBEAD ", " BACED  ", 
                                " BEADC ", " BADEC "],
                    options_hi: [" CBEAD ", " BACED  ",
                                " BEADC ", " BADEC "],
                    solution_en: "3.(a) CBEAD<br />The correct sentence is : “I have missed my daily routine at the workplace a lot since my retirement.”",
                    solution_hi: "3.(a) CBEAD<br />“I have missed my daily routine at the workplace a lot since my retirement.” सही sentence है। ",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "7",
                    question_en: "<p>4. Identity the most appropriate ANTONYM of the underlined word in the following sentence. <br>I love the <span style=\"text-decoration: underline;\">stunning </span>view of the mountain.</p>",
                    question_hi: "<p>4. Identity the most appropriate ANTONYM of the underlined word in the following sentence. <br>I love the <span style=\"text-decoration: underline;\">stunning </span>view of the mountain.</p>",
                    options_en: ["<p>Toxic</p>", "<p>Superfluous</p>", 
                                "<p>Smashing</p>", "<p>Mundane</p>"],
                    options_hi: ["<p>Toxic</p>", "<p>Superfluous</p>",
                                "<p>Smashing</p>", "<p>Mundane</p>"],
                    solution_en: "<p>4.(d) <strong>Mundane</strong>- lacking interest or excitement.<br><strong>Stunning</strong>- extremely impressive or attractive.<br><strong>Toxic</strong>- harmful or poisonous.<br><strong>Superfluous</strong>- unnecessary, especially through being more than enough.<br><strong>Smashing</strong>- excellent or very impressive.</p>",
                    solution_hi: "<p>4.(d) <strong>Mundane </strong>(नीरस)- lacking interest or excitement.<br><strong>Stunning </strong>(आकर्षक)- extremely impressive or attractive.<br><strong>Toxic </strong>(विषैला)- harmful or poisonous.<br><strong>Superfluous </strong>(अनावश्यक)- unnecessary, especially through being more than enough.<br><strong>Smashing </strong>(उत्कृष्ट)- excellent or very impressive.</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "7",
                    question_en: "<p>5. Select the option that expresses the following sentence in active voice. <br>The concert tickets were bought by my friend</p>",
                    question_hi: "<p>5. Select the option that expresses the following sentence in active voice. <br>The concert tickets were bought by my friend</p>",
                    options_en: ["<p>My friend has bought the concert tickets.</p>", "<p>My friend bought the concert tickets.</p>", 
                                "<p>My friend buys the concert tickets</p>", "<p>My friend is buying the concert tickets </p>"],
                    options_hi: ["<p>My friend has bought the concert tickets.</p>", "<p>My friend bought the concert tickets.</p>",
                                "<p>My friend buys the concert tickets</p>", "<p>My friend is buying the concert tickets</p>"],
                    solution_en: "<p>5.(b) My friend bought the concert tickets. (Correct)<br>(a) My friend <span style=\"text-decoration: underline;\">has bought</span> the concert tickets. (Incorrect Tense)<br>(c) My friend <span style=\"text-decoration: underline;\">buys </span>the concert tickets. (Incorrect Tense)<br>(d) My friend<span style=\"text-decoration: underline;\"> is buying</span> the concert tickets. (Incorrect Tense)</p>",
                    solution_hi: "<p>5.(b) My friend bought the concert tickets. (Correct)<br>(a) My friend <span style=\"text-decoration: underline;\">has bought</span> the concert tickets. (गलत Tense)<br>(c) My friend <span style=\"text-decoration: underline;\">buys </span>the concert tickets. (गलत Tense)<br>(d) My friend <span style=\"text-decoration: underline;\">is buying</span> the concert tickets. (गलत Tense)</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "7",
                    question_en: "<p>6. Identify from the given options the word which is similar in meaning to the following word.<br>Morbid</p>",
                    question_hi: "<p>6. Identify from the given options the word which is similar in meaning to the following word.<br>Morbid</p>",
                    options_en: ["<p>Blithe</p>", "<p>Gloomy</p>", 
                                "<p>Grand</p>", "<p>Bet</p>"],
                    options_hi: ["<p>Blithe</p>", "<p>Gloomy</p>",
                                "<p>Grand</p>", "<p>Bet</p>"],
                    solution_en: "<p>6.(b) <strong>Gloomy</strong>- feeling or appearing sad or depressed.<br><strong>Morbid</strong>- having an abnormal interest in disturbing subjects like death.<br><strong>Blithe</strong>- showing a casual and cheerful indifference.<br><strong>Grand</strong>- impressive in size, appearance, or style.<br><strong>Bet</strong>- an agreement to risk something on the outcome of a future event.</p>",
                    solution_hi: "<p>6.(b) <strong>Gloomy </strong>(निराशाजनक)- feeling or appearing sad or depressed.<br><strong>Morbid </strong>(विकृत)- having an abnormal interest in disturbing subjects like death.<br><strong>Blithe </strong>( पुलकित/प्रसन्नचित)- showing a casual and cheerful indifference.<br><strong>Grand </strong>(भव्य)- impressive in size, appearance, or style.<br><strong>Bet</strong> (शर्त)- an agreement to risk something on the outcome of a future event.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "7",
                    question_en: "<p>7. Select the most appropriate meaning of the underlined idiom/phrase. <br>I had heard of <span style=\"text-decoration: underline;\">henpecked husbands</span> and now, I felt that I saw one.</p>",
                    question_hi: "<p>7. Select the most appropriate meaning of the underlined idiom/phrase. <br>I had heard of <span style=\"text-decoration: underline;\">henpecked husbands</span> and now, I felt that I saw one.</p>",
                    options_en: ["<p>Controlling husband</p>", "<p>Bossy husbands</p>", 
                                "<p>Oppressed husband</p>", "<p>Dominating husbands</p>"],
                    options_hi: ["<p>Controlling husband</p>", "<p>Bossy husbands</p>",
                                "<p>Oppressed husband</p>", "<p>Dominating husbands</p>"],
                    solution_en: "<p>7.(c) Henpecked husbands- oppressed husband.</p>",
                    solution_hi: "<p>7.(c) Henpecked husbands- oppressed husband./प्रताड़ित पति।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "7",
                    question_en: "8. The following sentence has been split into four segments. Identify the segment that contains a spelling error. <br />Big Data has inandated / the education industry. / It has transformed it / by leaps and bounds.",
                    question_hi: "8. The following sentence has been split into four segments. Identify the segment that contains a spelling error. <br />Big Data has inandated / the education industry. / It has transformed it / by leaps and bounds.",
                    options_en: [" It has transformed it  ", " by leaps and bounds ", 
                                " the education industry ", " Big Data has inandated"],
                    options_hi: [" It has transformed it  ", " by leaps and bounds ",
                                " the education industry ", " Big Data has inandated"],
                    solution_en: "8.(d) Big Data has inandated<br />\'Inundated\' is the correct spelling.",
                    solution_hi: "8.(d) Big Data has inandated<br />\'Inundated\' सही spelling है।",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "7",
                    question_en: "<p>9. Select the most appropriate ANTONYM of the given word. <br><strong>Hinder</strong></p>",
                    question_hi: "<p>9. Select the most appropriate ANTONYM of the given word. <br><strong>Hinder</strong></p>",
                    options_en: ["<p>Praise</p>", "<p>Facilitate</p>", 
                                "<p>Joy</p>", "<p>Block</p>"],
                    options_hi: ["<p>Praise</p>", "<p>Facilitate</p>",
                                "<p>Joy</p>", "<p>Block</p>"],
                    solution_en: "<p>9.(b) <strong>Facilitate</strong>- to make an action or process easier.<br><strong>Hinder</strong>- to create difficulties that result in delay or obstruction.<br><strong>Praise</strong>- to express approval or admiration.<br><strong>Joy</strong>- a feeling of great happiness.<br><strong>Block</strong>- to obstruct or prevent movement or progress.</p>",
                    solution_hi: "<p>9.(b) <strong>Facilitate </strong>(सुगम बनाना)- to make an action or process easier.<br><strong>Hinder </strong>(अड़चन डालना)- to create difficulties that result in delay or obstruction.<br><strong>Praise </strong>(प्रशंसा)- to express approval or admiration.<br><strong>Joy </strong>(आनंद)- a feeling of great happiness.<br><strong>Block </strong>(बाधा डालना)- to obstruct or prevent movement or progress.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "7",
                    question_en: "<p>10. Select the most appropriate ANTONYM of the given word.<br><strong>Inevitable</strong></p>",
                    question_hi: "<p>10. Select the most appropriate ANTONYM of the given word.<br><strong>Inevitable</strong></p>",
                    options_en: ["<p>Inexorable</p>", "<p>Clear</p>", 
                                "<p>Avoidable</p>", "<p>Confident</p>"],
                    options_hi: ["<p>Inexorable</p>", "<p>Clear</p>",
                                "<p>Avoidable</p>", "<p>Confident</p>"],
                    solution_en: "<p>10.(c) <strong>Avoidable</strong>- able to be prevented or avoided.<br><strong>Inevitable</strong>- certain to happen.<br><strong>Inexorable</strong>- impossible to stop or prevent.<br><strong>Clear</strong>- easy to perceive or understand.<br><strong>Confident</strong>- feeling or showing certainty in oneself or something.</p>",
                    solution_hi: "<p>10.(c) <strong>Avoidable </strong>(परिहार्य)- able to be prevented or avoided.<br><strong>Inevitable </strong>(अनिवार्य)- certain to happen.<br><strong>Inexorable </strong>(अटल)- impossible to stop or prevent.<br><strong>Clear </strong>(स्पष्ट)- easy to perceive or understand.<br><strong>Confident </strong>(आत्मविश्वासी)- feeling or showing certainty in oneself or something.</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "7",
                    question_en: "<p>11. Select the option that can be used as a one-word substitute for the given group of words. <br>The statistical study of human populations.</p>",
                    question_hi: "<p>11. Select the option that can be used as a one-word substitute for the given group of words. <br>The statistical study of human populations.</p>",
                    options_en: ["<p>Photography</p>", "<p>Palaeography</p>", 
                                "<p>Astronomy</p>", "<p>Demography</p>"],
                    options_hi: ["<p>Photography</p>", "<p>Palaeography</p>",
                                "<p>Astronomy</p>", "<p>Demography</p>"],
                    solution_en: "<p>11.(d) <strong>Demography</strong>- the statistical study of human populations.<br><strong>Photography</strong>- the art or practice of taking and processing photographs.<br><strong>Palaeography</strong>- the study of ancient writing systems and the deciphering and dating of historical manuscripts.<br><strong>Astronomy</strong>- the scientific study of the universe and of objects that exist naturally in space, such as the moon, the sun, planets, and stars.</p>",
                    solution_hi: "<p>11.(d) <strong>Demography </strong>(जनसांख्यिकी)- the statistical study of human populations.<br><strong>Photography </strong>(छायाचित्रण)- the art or practice of taking and processing photographs.<br><strong>Palaeography </strong>(पुरालेखशास्त्र )- the study of ancient writing systems and the deciphering and dating of historical manuscripts.<br><strong>Astronomy </strong>(खगोलशास्त्र)- the scientific study of the universe and of objects that exist naturally in space, such as the moon, the sun, planets, and stars.</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "7",
                    question_en: "<p>12. Parts of the following sentence have been given as options. Select the option that contains an error.<br>It rain a lot in this part of the country.</p>",
                    question_hi: "<p>12. Parts of the following sentence have been given as options. Select the option that contains an error.<br>It rain a lot in this part of the country.</p>",
                    options_en: ["<p>of the country</p>", "<p>It rain</p>", 
                                "<p>a lot</p>", "<p>in this part</p>"],
                    options_hi: ["<p>of the country</p>", "<p>It rain</p>",
                                "<p>a lot</p>", "<p>in this part</p>"],
                    solution_en: "<p>12.(b) It rain<br>According to the &ldquo;Subject-Verb Agreement Rule&rdquo;, a singular subject always takes a singular verb and a plural subject always takes a plural verb. At the end of a singular verb, s/es is used. In the given sentence, &lsquo;it&rsquo; is a singular subject that will take &lsquo;rains&rsquo; as a singular verb. Hence, &lsquo;It rains&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>12.(b) It rain<br>\"Subject verb agreement&rdquo; नियम के अनुसार, singular subject के साथ हमेशा singular verb का प्रयोग होता है और plural subject के साथ हमेशा plural verb का प्रयोग होता है। Singular verb के अंत में s/es का प्रयोग होता है। दिए गए sentence में, &lsquo;it&rsquo; एक singular subject है जिसके साथ singular verb, &lsquo;rains&rsquo; का प्रयोग होगा। अतः, &lsquo;It rains&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "7",
                    question_en: "13. Parts of a sentence are given below in jumbled order. Arrange the parts in the correct order to form a meaningful sentence. <br />A. all the plants and animals of today <br />B. it is written that Brahma is the God of creation <br />C. and it is He who has made <br />D. in the book of Hindu mythology",
                    question_hi: "13. Parts of a sentence are given below in jumbled order. Arrange the parts in the correct order to form a meaningful sentence. <br />A. all the plants and animals of today <br />B. it is written that Brahma is the God of creation <br />C. and it is He who has made <br />D. in the book of Hindu mythology",
                    options_en: [" DACB ", " DBCA ", 
                                " DCBA", " CABD"],
                    options_hi: [" DACB ", " DBCA ",
                                " DCBA", " CABD"],
                    solution_en: "13.(b) DBCA <br />The given sentence starts with Part D as it introduces the main idea of the sentence, i.e. ‘the book of Hindu mythology’. Part D will be followed by Part B as it states that it is written that Brahma is the God of creation. Further, Part C begins to talk about something he has made & Part A states that he has made all the plants and plants of today. So, A will follow C. Going through the options, option ‘b’ has the correct sequence.",
                    solution_hi: "13.(b) DBCA <br />दिया गया sentence, Part D से शुरू होता है, क्योंकि यह sentence के मुख्य विचार, ‘the book of Hindu mythology’ का परिचय देता है। Part D के बाद Part B आएगा क्योंकि इसमें बताया गया है कि ब्रह्मा सृष्टि(creation) के देवता हैं। इसके अलावा, Part C में उनके द्वारा बनाई गई किसी चीज़ के बारे में बात शुरू होती है और Part A में बताया गया है कि आज के सभी पेड़-पौधे उन्होंने ही बनाए हैं। इसलिए, C के बाद A आएगा। Options के माध्यम से जाने पर, option \'b\' में सही sequence है।",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "7",
                    question_en: "14. Select the correctly spelled sentence. ",
                    question_hi: "14. Select the correctly spelled sentence.",
                    options_en: [" The athlete’s achievements are impressive.  ", " The athlete\'s acheivements are impressive. ", 
                                " The athlete’s achievements are imprressive. ", " The athletees achievements are impressive. "],
                    options_hi: [" The athlete’s achievements are impressive.  ", " The athlete\'s acheivements are impressive. ",
                                " The athlete’s achievements are imprressive. ", " The athletees achievements are impressive."],
                    solution_en: "14.(a) The athlete’s achievements are impressive.",
                    solution_hi: "14.(a) The athlete’s achievements are impressive.",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "7",
                    question_en: "<p>15. Select the option that expresses the given sentence in passive voice.<br>Inayat did not greet her.</p>",
                    question_hi: "<p>15. Select the option that expresses the given sentence in passive voice.<br>Inayat did not greet her.</p>",
                    options_en: ["<p>She was not going to be greeted by Inayat.</p>", "<p>She was not at all greeted by Inayat.</p>", 
                                "<p>She was not greeted by Inayat.</p>", "<p>She was not being greeted by Inayat.</p>"],
                    options_hi: ["<p>She was not going to be greeted by Inayat.</p>", "<p>She was not at all greeted by Inayat.</p>",
                                "<p>She was not greeted by Inayat.</p>", "<p>She was not being greeted by Inayat.</p>"],
                    solution_en: "<p>15.(c) She was not greeted by Inayat. (Correct)<br>(a) She was not <span style=\"text-decoration: underline;\">going to be</span> greeted by Inayat. (Incorrect Verb)<br>(b) She was not <span style=\"text-decoration: underline;\">at all </span>greeted by Inayat. (Additional Words)<br>(d) She <span style=\"text-decoration: underline;\">was not being</span> greeted by Inayat.(Incorrect Helping Verb)</p>",
                    solution_hi: "<p>15.(c) She was not greeted by Inayat. (Correct)<br>(a) She was not <span style=\"text-decoration: underline;\">going to be</span> greeted by Inayat. (गलत Verb)<br>(b) She was not <span style=\"text-decoration: underline;\">at all </span>greeted by Inayat. (अतिरिक्त Words)<br>(d) She <span style=\"text-decoration: underline;\">was not being</span> greeted by Inayat.(गलत Helping Verb)</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "16",
                    section: "7",
                    question_en: "<p>16. Select the most appropriate option that can substitute the underlined segment in the given sentence. She was foolish enough to think that she <span style=\"text-decoration: underline;\">be completely safe</span> now.</p>",
                    question_hi: "<p>16. Select the most appropriate option that can substitute the underlined segment in the given sentence. She was foolish enough to think that she <span style=\"text-decoration: underline;\">be completely safe</span> now.</p>",
                    options_en: ["<p>has being completely safe</p>", "<p>have been completely safe</p>", 
                                "<p>is completely safe</p>", "<p>were completely safe</p>"],
                    options_hi: ["<p>has being completely safe</p>", "<p>have been completely safe</p>",
                                "<p>is completely safe</p>", "<p>were completely safe</p>"],
                    solution_en: "<p>16.(c) is completely safe<br>The use of the adverb &lsquo;now&rsquo; indicates present tense and the present tense form of &lsquo;be&rsquo; is &lsquo;is&rsquo; for the singular subject (she). Hence, &lsquo;is completely safe&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>16.(c) is completely safe<br>Adverb &lsquo;now&rsquo; का प्रयोग present tense को दर्शाता है तथा singular subject (she) के लिए \'be\' का present tense form \'is\' है। अतः, &lsquo;is completely safe&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "17",
                    section: "7",
                    question_en: "<p>17. Select the option that can be used as a one-word substitute for the underlined group of words.<br>In industries, it is very common to observe that people show <span style=\"text-decoration: underline;\">undue favour to one&rsquo;s own relative during the</span> <span style=\"text-decoration: underline;\">hiring process.</span></p>",
                    question_hi: "<p>17. Select the option that can be used as a one-word substitute for the underlined group of words.<br>In industries, it is very common to observe that people show <span style=\"text-decoration: underline;\">undue favour to one&rsquo;s own relative during the hiring process.</span></p>",
                    options_en: ["<p>nepotism</p>", "<p>racism</p>", 
                                "<p>liberalism</p>", "<p>communism</p>"],
                    options_hi: ["<p>nepotism</p>", "<p>racism</p>",
                                "<p>liberalism</p>", "<p>communism</p>"],
                    solution_en: "<p>17.(a) <strong>Nepotism</strong>- undue favour to one&rsquo;s own relative during the hiring process.<br><strong>Racism</strong>- the practice of discriminating against people based on their race or ethnic background.<br><strong>Liberalism</strong>- a political and social philosophy that promotes individual rights, civil liberties, democracy, and free enterprise.<br><strong>Communism</strong>- the belief in a society without different social classes.</p>",
                    solution_hi: "<p>17.(a) <strong>Nepotism </strong>(भाई-भतीजावाद)- undue favour to one&rsquo;s own relative during the hiring process.<br><strong>Racism </strong>(जातिवाद)- the practice of discriminating against people based on their race or ethnic background.<br><strong>Liberalism </strong>(उदारवाद)- a political and social philosophy that promotes individual rights, civil liberties, democracy, and free enterprise.<br><strong>Communism </strong>(साम्यवाद)- the belief in a society without different social classes.</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "18",
                    section: "7",
                    question_en: "<p>18. Select the most appropriate idiom that can substitute the underlined segment in the given sentence. Roshan, it&rsquo;s time for you to give your speech to the whole school, <span style=\"text-decoration: underline;\">wishing you luck to do well.</span></p>",
                    question_hi: "<p>18. Select the most appropriate idiom that can substitute the underlined segment in the given sentence. Roshan, it&rsquo;s time for you to give your speech to the whole school, <span style=\"text-decoration: underline;\">wishing you luck to do well.</span></p>",
                    options_en: ["<p>down in the dumps</p>", "<p>break a leg</p>", 
                                "<p>high five</p>", "<p>hands down</p>"],
                    options_hi: ["<p>down in the dumps</p>", "<p>break a leg</p>",
                                "<p>high five</p>", "<p>hands down</p>"],
                    solution_en: "<p>18.(b) <strong>Break a leg-</strong> wishing you luck to do well.</p>",
                    solution_hi: "<p>18.(b) <strong>Break a leg-</strong> wishing you luck to do well./आपको अच्छे प्रदर्शन के लिए शुभकामनाएँ देना।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "19",
                    section: "7",
                    question_en: "<p>19. Select the most appropriate synonym of the underlined word. <br>It takes time to <span style=\"text-decoration: underline;\">adapt </span>to a new environment.</p>",
                    question_hi: "<p>19. Select the most appropriate synonym of the underlined word. <br>It takes time to <span style=\"text-decoration: underline;\">adapt </span>to a new environment.</p>",
                    options_en: ["<p>Apply</p>", "<p>Adjust</p>", 
                                "<p>Understand</p>", "<p>Control</p>"],
                    options_hi: ["<p>Apply</p>", "<p>Adjust</p>",
                                "<p>Understand</p>", "<p>Control</p>"],
                    solution_en: "<p>19.(b) <strong>Adjust</strong>- to become more familiar with a new situation.<br><strong>Adapt</strong>- become adjusted to new conditions.<br><strong>Apply</strong>- to put something into operation or use.<br><strong>Understand</strong>- to grasp the meaning or significance of something.<br><strong>Control</strong>- to exercise power or influence over something.</p>",
                    solution_hi: "<p>19.(b) <strong>Adjust </strong>(समायोजित होना)- to become more familiar with a new situation.<br><strong>Adapt </strong>(अनुकूलन)- become adjusted to new conditions.<br><strong>Apply </strong>(आवेदन/लागू करना)- to put something into operation or use.<br><strong>Understand </strong>(समझना)- to grasp the meaning or significance of something.<br><strong>Control </strong>(नियंत्रण/ शासन करना)- to exercise power or influence over something.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "20",
                    section: "7",
                    question_en: "<p>20. Select the option that can be used as a one-word substitute for the given group of words. <br>One who tends to look at the dark side of things</p>",
                    question_hi: "<p>20. Select the option that can be used as a one-word substitute for the given group of words. <br>One who tends to look at the dark side of things</p>",
                    options_en: ["<p>Dynamist</p>", "<p>Polemist</p>", 
                                "<p>Optimist</p>", "<p>Pessimist</p>"],
                    options_hi: ["<p>Dynamist</p>", "<p>Polemist</p>",
                                "<p>Optimist</p>", "<p>Pessimist</p>"],
                    solution_en: "<p>20.(d) <strong>Pessimist</strong>- one who tends to look at the dark side of things.<br><strong>Dynamist</strong>- a proponent or advocate of a philosophical theory that explains phenomena in terms of an immanent force or energy.<br><strong>Polemist</strong>- a person who strongly attacks or defends a particular opinion, person, idea, or set of beliefs.<br><strong>Optimist</strong>- a person who is inclined to be hopeful and to expect good outcomes.</p>",
                    solution_hi: "<p>20.(d) <strong>Pessimist </strong>(निराशावादी)- one who tends to look at the dark side of things.<br><strong>Dynamist </strong>(गतिकामी)- a proponent or advocate of a philosophical theory that explains phenomena in terms of an immanent force or energy.<br><strong>Polemist </strong>(विवादक)- a person who strongly attacks or defends a particular opinion, person, idea, or set of beliefs.<br><strong>Optimist </strong>(आशावादी)- a person who is inclined to be hopeful and to expect good outcomes.</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "21",
                    section: "7",
                    question_en: "<p>21.<strong>Cloze Test:</strong><br>Recently, the (21)________ of heart-attacks in young Indians (22)________ increased tremendously. Doctors are surprised to note that young men and women with seemingly healthy lifestyle are suffering deadly heart attacks. (23)________ diet and exercise play a (24)________ role in a person&rsquo;s overall wellbeing, mental health is also a valuable factor. The (25)________ urban lifestyle is causing anxiety and depression in young Indians which may be a major factor behind the increased numbers.<br>Select the most appropriate option to fill in blank number 21.</p>",
                    question_hi: "<p>21.<strong>Cloze Test:</strong><br>Recently, the (21)________ of heart-attacks in young Indians (22)________ increased tremendously. Doctors are surprised to note that young men and women with seemingly healthy lifestyle are suffering deadly heart attacks. (23)________ diet and exercise play a (24)________ role in a person&rsquo;s overall wellbeing, mental health is also a valuable factor. The (25)________ urban lifestyle is causing anxiety and depression in young Indians which may be a major factor behind the increased numbers.<br>Select the most appropriate option to fill in blank number 21.</p>",
                    options_en: ["<p>amount</p>", "<p>number</p>", 
                                "<p>percent</p>", "<p>height</p>"],
                    options_hi: ["<p>amount</p>", "<p>number</p>",
                                "<p>percent</p>", "<p>height</p>"],
                    solution_en: "<p>21.(b) number<br>The given passage talks about the recent number of heart-attacks in young Indians. Hence, &lsquo;number&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>21.(b) number<br>दिए गए passage में युवा भारतीयों में हाल ही में आए heart-attacks की संख्या के बारे में बताया गया है। अतः, &lsquo;number&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "22",
                    section: "7",
                    question_en: "<p>22. <strong>Cloze Test:</strong><br>Recently, the (21)________ of heart-attacks in young Indians (22)________ increased tremendously. Doctors are surprised to note that young men and women with seemingly healthy lifestyle are suffering deadly heart attacks. (23)________ diet and exercise play a (24)________ role in a person&rsquo;s overall wellbeing, mental health is also a valuable factor. The (25)________ urban lifestyle is causing anxiety and depression in young Indians which may be a major factor behind the increased numbers.<br>Select the most appropriate option to fill in blank number 22.</p>",
                    question_hi: "<p>22. <strong>Cloze Test:</strong><br>Recently, the (21)________ of heart-attacks in young Indians (22)________ increased tremendously. Doctors are surprised to note that young men and women with seemingly healthy lifestyle are suffering deadly heart attacks. (23)________ diet and exercise play a (24)________ role in a person&rsquo;s overall wellbeing, mental health is also a valuable factor. The (25)________ urban lifestyle is causing anxiety and depression in young Indians which may be a major factor behind the increased numbers.<br>Select the most appropriate option to fill in blank number 22.</p>",
                    options_en: [" have been ", " had ", 
                                " has  ", " have"],
                    options_hi: [" have been ", " had ",
                                " has  ", " have"],
                    solution_en: "22.(c) has<br />‘The number of + plural noun + singular verb’ is the correct grammatical structure. Therefore, the singular verb ‘has’ will be used. Hence, \'has\' is the most appropriate answer. ",
                    solution_hi: "22.(c) has<br />‘The number of + plural noun + singular verb’ सही grammatical structure है। इसलिए, singular verb ‘has’ का प्रयोग होगा। अतः, \'has\' सबसे उपयुक्त उत्तर है।",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "23",
                    section: "7",
                    question_en: "<p>23. <strong>Cloze Test:</strong><br>Recently, the (21)________ of heart-attacks in young Indians (22)________ increased tremendously. Doctors are surprised to note that young men and women with seemingly healthy lifestyle are suffering deadly heart attacks. (23)________ diet and exercise play a (24)________ role in a person&rsquo;s overall wellbeing, mental health is also a valuable factor. The (25)________ urban lifestyle is causing anxiety and depression in young Indians which may be a major factor behind the increased numbers.<br>Select the most appropriate option to fill in blank number 23.</p>",
                    question_hi: "<p>23. <strong>Cloze Test:</strong><br>Recently, the (21)________ of heart-attacks in young Indians (22)________ increased tremendously. Doctors are surprised to note that young men and women with seemingly healthy lifestyle are suffering deadly heart attacks. (23)________ diet and exercise play a (24)________ role in a person&rsquo;s overall wellbeing, mental health is also a valuable factor. The (25)________ urban lifestyle is causing anxiety and depression in young Indians which may be a major factor behind the increased numbers.<br>Select the most appropriate option to fill in blank number 23.</p>",
                    options_en: ["<p>Since</p>", "<p>Although</p>", 
                                "<p>In spite of</p>", "<p>However</p>"],
                    options_hi: ["<p>Since</p>", "<p>Although</p>",
                                "<p>In spite of</p>", "<p>However</p>"],
                    solution_en: "<p>23.(b) Although<br>&lsquo;Although&rsquo; is used to indicate a contrasting idea. The given passage states that mental health is also important, which contrasts with the statement that diet and exercise play a monumental role. Hence, \'Although\' is the most appropriate answer.</p>",
                    solution_hi: "<p>23.(b) Although<br>&lsquo;Although&rsquo; का प्रयोग एक विरोधाभाषी विचार को व्यक्त करने के लिए किया जाता है। दिए गए passage में कहा गया है कि mental health भी महत्वपूर्ण है, जो इस statement का विपरीत है कि diet और exercises एक महत्वपूर्ण भूमिका निभाते हैं। अतः, \'Although\' सबसे उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "24",
                    section: "7",
                    question_en: "<p>24. <strong>Cloze Test:</strong><br>Recently, the (21)________ of heart-attacks in young Indians (22)________ increased tremendously. Doctors are surprised to note that young men and women with seemingly healthy lifestyle are suffering deadly heart attacks. (23)________ diet and exercise play a (24)________ role in a person&rsquo;s overall wellbeing, mental health is also a valuable factor. The (25)________ urban lifestyle is causing anxiety and depression in young Indians which may be a major factor behind the increased numbers.<br>Select the most appropriate option to fill in blank number 24.</p>",
                    question_hi: "<p>24. <strong>Cloze Test:</strong><br>Recently, the (21)________ of heart-attacks in young Indians (22)________ increased tremendously. Doctors are surprised to note that young men and women with seemingly healthy lifestyle are suffering deadly heart attacks. (23)________ diet and exercise play a (24)________ role in a person&rsquo;s overall wellbeing, mental health is also a valuable factor. The (25)________ urban lifestyle is causing anxiety and depression in young Indians which may be a major factor behind the increased numbers.<br>Select the most appropriate option to fill in blank number 24.</p>",
                    options_en: [" historic  ", " epic ", 
                                " monumental ", " increased"],
                    options_hi: [" historic  ", " epic ",
                                " monumental ", " increased"],
                    solution_en: "24.(c) monumental<br />‘Monumental’ means great in importance, extent, or size. The given passage states that diet and exercise play a monumental role in a person’s overall wellbeing. Hence, \'monumental\' is the most appropriate answer.",
                    solution_hi: "24.(c) monumental<br />‘Monumental’ का अर्थ है महत्व, विस्तार या आकार में बड़ा। दिए गए passage में कहा गया है कि diet और exercise किसी व्यक्ति की overall wellbeing में एक महत्वपूर्ण भूमिका निभाते हैं। अतः, \'monumental\' सबसे उपयुक्त उत्तर है।",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "25",
                    section: "misc",
                    question_en: "<p>25. <strong>Cloze Test:</strong><br>Recently, the (21)________ of heart-attacks in young Indians (22)________ increased tremendously. Doctors are surprised to note that young men and women with seemingly healthy lifestyle are suffering deadly heart attacks. (23)________ diet and exercise play a (24)________ role in a person&rsquo;s overall wellbeing, mental health is also a valuable factor. The (25)________ urban lifestyle is causing anxiety and depression in young Indians which may be a major factor behind the increased numbers.<br>Select the most appropriate option to fill in blank number 25.</p>",
                    question_hi: "<p>25. <strong>Cloze Test:</strong><br>Recently, the (21)________ of heart-attacks in young Indians (22)________ increased tremendously. Doctors are surprised to note that young men and women with seemingly healthy lifestyle are suffering deadly heart attacks. (23)________ diet and exercise play a (24)________ role in a person&rsquo;s overall wellbeing, mental health is also a valuable factor. The (25)________ urban lifestyle is causing anxiety and depression in young Indians which may be a major factor behind the increased numbers.<br>Select the most appropriate option to fill in blank number 25.</p>",
                    options_en: ["<p>fast-paced</p>", "<p>cosmic</p>", 
                                "<p>rapidly</p>", "<p>wilderness</p>"],
                    options_hi: ["<p>fast-paced</p>", "<p>cosmic</p>",
                                "<p>rapidly</p>", "<p>wilderness</p>"],
                    solution_en: "<p>25.(a) fast-paced<br>&lsquo;Fast-paced lifestyle&rsquo; means a way of life in which one is constantly busy and has little time for leisure or relaxation. The given passage states that the fast-paced urban lifestyle is causing anxiety and depression in young Indians. Hence, \'fast-paced\' is the most appropriate answer.</p>",
                    solution_hi: "<p>25.(a) fast-paced<br>&lsquo;Fast-paced lifestyle&rsquo; का अर्थ है ऐसी जीवनशैली जिसमें व्यक्ति लगातार busy रहता है और उसके पास आराम या विश्राम के लिए बहुत कम समय होता है। दिए गए passage में कहा गया है कि fast-paced urban lifestyle युवा भारतीयों में anxiety और depression का कारण बन रही है। अतः, \'fast-paced\' सबसे उपयुक्त उत्तर है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>