<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">12:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 12</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">12</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 12 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 0,
                end: 10
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 11,
                end: 11
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">1. </span><span style=\"font-family: Cambria Math;\">Ramesh bought a house for &#8377;2</span><span style=\"font-family: Cambria Math;\">,00,000</span><span style=\"font-family: Cambria Math;\">. At the end of the first year, he sold it at a loss of 10% on his investment. He invested the money thus obtained at a rate of 20% p.a. interest compounded annually for 2 years. The value of this investment would amount </span><span style=\"font-family: Cambria Math;\">to ___________.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC </span><span style=\"font-family: Cambria Math;\">CHSL 08</span><span style=\"font-family: Cambria Math;\">/06/2022 ( Morning )</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">1. </span><span style=\"font-family: Cambria Math;\">&#2352;&#2350;&#2375;&#2358;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> &#8377;2</span><span style=\"font-family: Cambria Math;\">,00,000</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2328;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2326;&#2352;&#2368;&#2342;&#2366;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2361;&#2354;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2352;&#2381;&#2359;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2309;&#2306;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2313;&#2360;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2311;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2309;&#2346;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2344;&#2367;&#2357;&#2375;&#2358;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> 10% </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2366;&#2344;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2375;&#2330;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2367;&#2351;&#2366;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2313;&#2360;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2311;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2381;&#2352;&#2325;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2381;&#2352;&#2366;&#2346;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2343;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> 20% </span><span style=\"font-family: Cambria Math;\">&#2357;&#2366;</span><span style=\"font-family: Cambria Math;\">&#2352;&#2381;&#2359;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> 2 </span><span style=\"font-family: Cambria Math;\">&#2357;&#2352;&#2381;&#2359;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2330;&#2325;&#2381;&#2352;&#2357;&#2371;&#2342;&#2381;&#2343;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2381;&#2351;&#2366;&#2332;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2344;&#2367;&#2357;&#2375;&#2358;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2367;&#2351;&#2366;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2311;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2344;&#2367;&#2357;&#2375;&#2358;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> ___________ </span><span style=\"font-family: Cambria Math;\">&#2361;&#2379;&#2327;&#2366;&#2404;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC </span><span style=\"font-family: Cambria Math;\">CHSL 08</span><span style=\"font-family: Cambria Math;\">/06/2022 ( Morning )</span></p>\n",
                    options_en: ["<p>&#8377;2<span style=\"font-family: Cambria Math;\">,59,200</span></p>\n", "<p>&#8377;2<span style=\"font-family: Cambria Math;\">,52,300</span></p>\n", 
                                "<p>&#8377;2<span style=\"font-family: Cambria Math;\">,56,900</span></p>\n", "<p>&#8377;2<span style=\"font-family: Cambria Math;\">,54,600</span></p>\n"],
                    options_hi: ["<p>&#8377;2<span style=\"font-family: Cambria Math;\">,59,200</span></p>\n", "<p>&#8377;2<span style=\"font-family: Cambria Math;\">,52,300</span></p>\n",
                                "<p>&#8377;2<span style=\"font-family: Cambria Math;\">,56,900</span></p>\n", "<p>&#8377;2<span style=\"font-family: Cambria Math;\">,54,600</span></p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">1.(</span><span style=\"font-family: Cambria Math;\">a)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Selling price of the house = 2</span><span style=\"font-family: Cambria Math;\">,00,000</span><span style=\"font-family: Cambria Math;\"> &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>90</mn><mn>100</mn></mfrac></math> </span><span style=\"font-family: Cambria Math;\">= 1,</span><span style=\"font-family: Cambria Math;\">80,000</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Principle = 1</span><span style=\"font-family: Cambria Math;\">,80,000</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Amount = 1</span><span style=\"font-family: Cambria Math;\">,80,000</span><span style=\"font-family: Cambria Math;\"> &times;&nbsp; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>120</mn><mrow><mn>100</mn><mo>&nbsp;</mo></mrow></mfrac><mo>&times;</mo><mo>&nbsp;</mo><mfrac><mn>120</mn><mrow><mn>100</mn><mo>&nbsp;</mo></mrow></mfrac><mo>&nbsp;</mo></math></span><span style=\"font-family: Cambria Math;\">= 2,59,200</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">1.(</span><span style=\"font-family: Cambria Math;\">a)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&#2328;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2367;&#2325;&#2381;&#2352;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2370;&#2354;&#2381;</span><span style=\"font-family: Cambria Math;\">&#2351;</span><span style=\"font-family: Cambria Math;\"> =</span><span style=\"font-family: Cambria Math;\"> 2,00,000 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>90</mn><mn>100</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\">= 1,80,000</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&#2350;&#2370;&#2354;&#2343;&#2344;</span><span style=\"font-family: Cambria Math;\"> = 1</span><span style=\"font-family: Cambria Math;\">,80,000</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&#2352;&#2366;&#2358;&#2367;</span><span style=\"font-family: Cambria Math;\"> = 1</span><span style=\"font-family: Cambria Math;\">,80,000</span><span style=\"font-family: Cambria Math;\"> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>120</mn><mrow><mn>100</mn><mo>&nbsp;</mo></mrow></mfrac><mo>&times;</mo><mo>&nbsp;</mo><mfrac><mn>120</mn><mrow><mn>100</mn><mo>&nbsp;</mo></mrow></mfrac><mo>&nbsp;</mo></math></span><span style=\"font-family: Cambria Math;\">= 2,59,200</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "17",
                    question_en: "<p>2.<span style=\"font-family: Cambria Math;\"> In what time will &#8377;3</span><span style=\"font-family: Cambria Math;\">,90,625</span><span style=\"font-family: Cambria Math;\"> amount to &#8377;4,56,976 at 8 % p</span><span style=\"font-family: Cambria Math;\">er annum, interest being </span><span style=\"font-family: Cambria Math;\">compounded</span><span style=\"font-family: Cambria Math;\"> half-yearly?</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC </span><span style=\"font-family: Cambria Math;\">CHSL 08</span><span style=\"font-family: Cambria Math;\">/06/2022 ( Afternoon )</span></p>\n",
                    question_hi: "<p>2.<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#8377;</span><span style=\"font-family: Cambria Math;\">3,90,625 </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2352;&#2366;&#2358;&#2367;</span><span style=\"font-family: Cambria Math;\"> 8% </span><span style=\"font-family: Cambria Math;\">&#2346;&#2381;&#2352;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2352;&#2381;&#2359;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2367;&#2340;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2350;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#8377;</span><span style=\"font-family: Cambria Math;\">4,56,976 </span><span style=\"font-family: Cambria Math;\">&#2361;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2332;&#2366;&#2319;&#2327;&#2368;</span><span style=\"font-family: Cambria Math;\"> , </span><span style=\"font-family: Cambria Math;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2381;&#2351;&#2366;&#2332;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2309;&#2352;&#2381;&#2343;</span><span style=\"font-family: Cambria Math;\">-</span><span style=\"font-family: Cambria Math;\">&#2357;&#2366;&#2352;&#2381;&#2359;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2352;&#2370;&#2346;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2306;&#2351;&#2379;&#2332;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2332;&#2366;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2379;</span><span style=\"font-family: Cambria Math;\"> ?</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC </span><span style=\"font-family: Cambria Math;\">CHSL 08</span><span style=\"font-family: Cambria Math;\">/06/2022 ( Afternoon )</span></p>\n",
                    options_en: ["<p>2 years</p>\n", "<p><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>1</mn><mfrac><mn>1</mn><mn>2</mn></mfrac></math> years </span></p>\n", 
                                "<p><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>2</mn><mfrac><mn>1</mn><mn>2</mn></mfrac></math> years </span></p>\n", "<p>1 years</p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\n"],
                    options_hi: ["<p>2 <span style=\"font-family: Cambria Math;\">&#2357;&#2352;&#2381;&#2359;</span></p>\n", "<p><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>1</mn><mfrac><mn>1</mn><mn>2</mn></mfrac></math> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2352;&#2381;&#2359;</span></p>\n",
                                "<p><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>2</mn><mfrac><mn>1</mn><mn>2</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\">&#2357;&#2352;&#2381;&#2359;</span></p>\n", "<p>1 <span style=\"font-family: Cambria Math;\">&#2357;&#2352;&#2381;&#2359;</span></p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">2.(</span><span style=\"font-family: Cambria Math;\">a)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Principle = &#8377;3</span><span style=\"font-family: Cambria Math;\">,90,</span><span style=\"font-family: Cambria Math;\">625</span><span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Amount = &#8377;4</span><span style=\"font-family: Cambria Math;\">,56,976</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Rate = 8%</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Interest being compounded half yearly,</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">New rate = 4%</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">A = </span><span style=\"font-family: Cambria Math;\">P<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mfenced><mrow><mn>1</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mfrac><mi>r</mi><mn>100</mn></mfrac></mrow></mfenced><mi>n</mi></msup></math></span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>456976</mn><mn>390625</mn></mfrac><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><msup><mfenced><mrow><mn>1</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mfrac><mn>4</mn><mn>100</mn></mfrac></mrow></mfenced><mi>n</mi></msup></math></span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>456976</mn><mn>390625</mn></mfrac><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><msup><mfenced><mfrac><mn>26</mn><mn>25</mn></mfrac></mfenced><mi>n</mi></msup></math></span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mfenced><mfrac><mn>26</mn><mn>25</mn></mfrac></mfenced><mn>4</mn></msup><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><msup><mfenced><mfrac><mn>26</mn><mn>25</mn></mfrac></mfenced><mi>n</mi></msup></math></span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mfenced><mfrac><mn>26</mn><mn>25</mn></mfrac></mfenced><mn>2</mn></msup><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><msup><mfenced><mfrac><mn>26</mn><mn>25</mn></mfrac></mfenced><mi>n</mi></msup></math></span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">n = 4 years</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Interest being compounded half yearly,</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">So, n = 2 years</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">2.(</span><span style=\"font-family: Cambria Math;\">a)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&#2350;&#2370;</span><span style=\"font-family: Cambria Math;\">&#2354;&#2343;&#2344;</span><span style=\"font-family: Cambria Math;\"> =</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#8377;</span><span style=\"font-family: Cambria Math;\">3,90,625 </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&#2352;&#2366;&#2358;&#2367;</span><span style=\"font-family: Cambria Math;\"> = </span><span style=\"font-family: Cambria Math;\">&#8377;</span><span style=\"font-family: Cambria Math;\">4</span><span style=\"font-family: Cambria Math;\">,56,976</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&#2342;&#2352;</span><span style=\"font-family: Cambria Math;\"> = 8%</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&#2331;&#2350;&#2366;&#2361;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2330;&#2325;&#2381;&#2352;&#2357;&#2371;&#2342;&#2381;&#2343;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2381;&#2351;&#2366;&#2332;</span><span style=\"font-family: Cambria Math;\">,</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&#2344;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2352;</span><span style=\"font-family: Cambria Math;\"> = 4%</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">A = </span><span style=\"font-family: Cambria Math;\">P<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mfenced><mrow><mn>1</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mfrac><mi>r</mi><mn>100</mn></mfrac></mrow></mfenced><mi>n</mi></msup></math></span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>456976</mn><mn>390625</mn></mfrac><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><msup><mfenced><mrow><mn>1</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mfrac><mn>4</mn><mn>100</mn></mfrac></mrow></mfenced><mi>n</mi></msup></math></span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>456976</mn><mn>390625</mn></mfrac><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><msup><mfenced><mfrac><mn>26</mn><mn>25</mn></mfrac></mfenced><mi>n</mi></msup></math></span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mfenced><mfrac><mn>26</mn><mn>25</mn></mfrac></mfenced><mn>4</mn></msup><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><msup><mfenced><mfrac><mn>26</mn><mn>25</mn></mfrac></mfenced><mi>n</mi></msup></math></span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mfenced><mfrac><mn>26</mn><mn>25</mn></mfrac></mfenced><mn>2</mn></msup><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><msup><mfenced><mfrac><mn>26</mn><mn>25</mn></mfrac></mfenced><mi>n</mi></msup></math></span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">n = 4 </span><span style=\"font-family: Cambria Math;\">&#2357;&#2352;&#2381;&#2359;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&#2331;&#2350;&#2366;&#2361;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2330;&#2325;&#2381;&#2352;&#2357;&#2371;&#2342;&#2381;&#2343;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2381;&#2351;&#2366;&#2332;</span><span style=\"font-family: Cambria Math;\">,</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&#2309;&#2340;&#2307;</span><span style=\"font-family: Cambria Math;\">, n = 2 </span><span style=\"font-family: Cambria Math;\">&#2357;&#2352;&#2381;&#2359;</span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">3. </span><span style=\"font-family: Cambria Math;\">Find the compound interest on &#8377;1</span><span style=\"font-family: Cambria Math;\">,60,000</span><span style=\"font-family: Cambria Math;\"> at 10% per annum for 2 years if the interest is compou</span><span style=\"font-family: Cambria Math;\">nded half-yearly.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC </span><span style=\"font-family: Cambria Math;\">CHSL 08</span><span style=\"font-family: Cambria Math;\">/06/2022 ( Afternoon )</span></p>\n",
                    question_hi: "<p>3. <span style=\"font-family: Cambria Math;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2381;&#2351;&#2366;&#2332;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2309;&#2352;&#2381;&#2343;&#2357;&#2366;&#2352;&#2381;&#2359;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2352;&#2370;&#2346;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2306;&#2351;&#2379;&#2332;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2332;&#2366;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#8377;</span><span style=\"font-family: Cambria Math;\">1</span><span style=\"font-family: Cambria Math;\">,60,000</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> 10% </span><span style=\"font-family: Cambria Math;\">&#2346;&#2381;&#2352;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2352;&#2381;&#2359;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> 2 </span><span style=\"font-family: Cambria Math;\">&#2357;&#2352;&#2381;&#2359;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2330;&#2325;&#2381;&#2352;&#2357;&#2371;&#2342;&#2381;&#2343;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2381;&#2351;&#2366;&#2332;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2332;&#2381;&#2334;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;&#2332;&#2367;&#2319;&#2404;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC </span><span style=\"font-family: Cambria Math;\">CHSL 08</span><span style=\"font-family: Cambria Math;\">/06/2022 ( Afternoon )</span></p>\n",
                    options_en: ["<p>&#8377;32,282</p>\n", "<p>&#8377;34,481</p>\n", 
                                "<p>&#8377;33,458</p>\n", "<p>&#8377;35,550</p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\n"],
                    options_hi: ["<p>&#8377;32,282</p>\n", "<p>&#8377;34,481</p>\n",
                                "<p>&#8377;33,458</p>\n", "<p>&#8377;35,550</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">3.(</span><span style=\"font-family: Cambria Math;\">b)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Principle = 1</span><span style=\"font-family: Cambria Math;\">,60,000</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Rate = 10%</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">T= 2 years</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Interest is compounded half yearly,</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">New Rate = 5%, T = 4 years</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">A = 1</span><span style=\"font-family: Cambria Math;\">,60,000</span><span style=\"font-family: Cambria Math;\"> &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mfenced><mrow><mn>1</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mfrac><mn>5</mn><mn>100</mn></mfrac></mrow></mfenced><mn>4</mn></msup></math></span></p>\r\n<p><span style=\"font-family: Cambria Math;\">A = 1</span><span style=\"font-family: Cambria Math;\">,60,000</span><span style=\"font-family: Cambria Math;\"> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>21</mn><mn>20</mn></mfrac><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mfrac><mn>21</mn><mn>20</mn></mfrac><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mfrac><mn>21</mn><mn>20</mn></mfrac><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mfrac><mn>21</mn><mn>20</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\">&nbsp;</span><span style=\"font-family: Cambria Math;\">= 1,94,481</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Compound interest = 1</span><span style=\"font-family: Cambria Math;\">,94,481</span><span style=\"font-family: Cambria Math;\"> - 1,60,000 = 34,481</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">3.(</span><span style=\"font-family: Cambria Math;\">b)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&#2350;&#2370;&#2354;&#2343;&#2344;</span><span style=\"font-family: Cambria Math;\"> = 1</span><span style=\"font-family: Cambria Math;\">,60,000</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&#2342;&#2352;</span><span style=\"font-family: Cambria Math;\"> = 10%</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">T= 2 years</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&#2348;&#2381;&#2351;&#2366;&#2332;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2309;&#2352;&#2381;&#2343;&#2357;&#2366;&#2352;&#2381;&#2359;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2306;&#2351;&#2379;&#2332;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2379;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">,</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&#2344;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2352;</span><span style=\"font-family: Cambria Math;\"> = 5%, T = 4 </span><span style=\"font-family: Cambria Math;\">&#2357;&#2352;&#2381;&#2359;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">A = 1</span><span style=\"font-family: Cambria Math;\">,60,000</span><span style=\"font-family: Cambria Math;\"> &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mfenced><mrow><mn>1</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mfrac><mn>5</mn><mn>100</mn></mfrac></mrow></mfenced><mn>4</mn></msup></math></span></p>\r\n<p><span style=\"font-family: Cambria Math;\">A = 1</span><span style=\"font-family: Cambria Math;\">,60,000</span><span style=\"font-family: Cambria Math;\"> &times; </span><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>21</mn><mn>20</mn></mfrac><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mfrac><mn>21</mn><mn>20</mn></mfrac><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mfrac><mn>21</mn><mn>20</mn></mfrac><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mfrac><mn>21</mn><mn>20</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\">&nbsp;</span><span style=\"font-family: Cambria Math;\">= 1,94,481</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&#2330;&#2325;&#2381;&#2352;&#2357;&#2371;&#2342;&#2381;&#2343;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2381;&#2351;&#2366;&#2332;</span><span style=\"font-family: Cambria Math;\"> = 1</span><span style=\"font-family: Cambria Math;\">,94,481</span><span style=\"font-family: Cambria Math;\"> - 1,60,000 = 34,481</span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "17",
                    question_en: "<p>4. <span style=\"font-family: Cambria Math;\">Sundar lends a sum of &#8377;6,000 to Mahesh at an interest rate of 10% p.a</span><span style=\"font-family: Cambria Math;\">., compounded annually. What will be the amount at the end of 2 years?</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC </span><span style=\"font-family: Cambria Math;\">CHSL 08</span><span style=\"font-family: Cambria Math;\">/06/2022 ( Evening )</span></p>\n",
                    question_hi: "<p>4. <span style=\"font-family: Cambria Math;\">&#2360;&#2369;&#2306;&#2342;&#2352;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2350;&#2361;&#2375;&#2358;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> 10% </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2366;&#2352;&#2381;&#2359;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2381;&#2351;&#2366;&#2332;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#8377;</span><span style=\"font-family: Cambria Math;\">6,000 </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2352;&#2366;&#2358;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2313;&#2343;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2375;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2332;&#2367;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2381;&#2351;&#2366;&#2332;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2366;&#2352;&#2381;&#2359;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2352;&#2370;&#2346;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;</span><span style=\"font-family: Cambria Math;\">&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2330;&#2325;&#2381;&#2352;&#2357;&#2371;&#2342;&#2381;&#2343;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2379;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> 2 </span><span style=\"font-family: Cambria Math;\">&#2357;&#2352;&#2381;&#2359;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2309;&#2306;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2351;&#2361;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2352;&#2366;&#2358;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2367;&#2340;&#2344;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2332;&#2366;&#2319;&#2327;&#2368;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC </span><span style=\"font-family: Cambria Math;\">CHSL 08</span><span style=\"font-family: Cambria Math;\">/06/2022 ( Evening )</span></p>\n",
                    options_en: ["<p>&#8377;7,160</p>\n", "<p>&#8377;7,250</p>\n", 
                                "<p>&#8377;7,200</p>\n", "<p>&#8377;7,260</p>\n"],
                    options_hi: ["<p>&#8377;7,160</p>\n", "<p>&#8377;7,250</p>\n",
                                "<p>&#8377;7,200</p>\n", "<p>&#8377;7,260</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">4.(</span><span style=\"font-family: Cambria Math;\">d)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Amount after</span><span style=\"font-family: Cambria Math;\"> 2 years at the rate of 10% Compounded annually </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">= 6000 </span><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mfenced><mrow><mn>1</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mfrac><mn>1</mn><mn>10</mn></mfrac></mrow></mfenced><mn>2</mn></msup></math></span><span style=\"font-family: Cambria Math;\">= 6000 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mfenced><mfrac><mn>11</mn><mn>10</mn></mfrac></mfenced><mn>2</mn></msup></math></span><span style=\"font-family: Cambria Math;\"> = 6000 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>121</mn><mn>100</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> = &#8377;7,260</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">4.(</span><span style=\"font-family: Cambria Math;\">d)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">10% </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> 2 </span><span style=\"font-family: Cambria Math;\">&#2357;&#2352;&#2381;&#2359;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2366;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2352;&#2366;&#2358;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2366;&#2352;&#2381;&#2359;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2352;&#2370;&#2346;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2330;&#2325;&#2381;&#2352;&#2357;&#2371;&#2342;&#2381;&#2343;&#2367;</span><span style=\"font-family: Cambria Math;\">:</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">= 6000 </span><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mfenced><mrow><mn>1</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mfrac><mn>1</mn><mn>10</mn></mfrac></mrow></mfenced><mn>2</mn></msup></math></span><span style=\"font-family: Cambria Math;\">&nbsp;</span><span style=\"font-family: Cambria Math;\">= 6000 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mfenced><mfrac><mn>11</mn><mn>10</mn></mfrac></mfenced><mn>2</mn></msup></math></span><span style=\"font-family: Cambria Math;\"> = 6000 &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>121</mn><mn>100</mn></mfrac></math>&nbsp;</span><span style=\"font-family: Cambria Math;\"> = &#8377;7,260</span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "17",
                    question_en: "<p>5.<span style=\"font-family: Cambria Math;\"> Find the compound interest on &#8377;35,000 in 2 years at 6% per annum, the interest being </span><span style=\"font-family: Cambria Math;\">compounded</span><span style=\"font-family: Cambria Math;\"> half-yearly (nearest to a &#8377;):</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC </span><span style=\"font-family: Cambria Math;\">CHSL 09</span><span style=\"font-family: Cambria Math;\">/06/2022 ( Morning)</span></p>\n",
                    question_hi: "<p>5. <span style=\"font-family: Cambria Math;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2381;&#2351;&#2366;&#2332;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2309;&#2352;&#2381;&#2343;</span><span style=\"font-family: Cambria Math;\">-</span><span style=\"font-family: Cambria Math;\">&#2357;&#2366;&#2352;&#2381;&#2359;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2352;&#2370;&#2346;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2306;&#2351;&#2379;&#2332;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2332;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2352;&#2361;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2379;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2340;</span><span style=\"font-family: Cambria Math;\">&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#8377;</span><span style=\"font-family: Cambria Math;\">35,000 </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2352;&#2366;&#2358;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> 2 </span><span style=\"font-family: Cambria Math;\">&#2357;&#2352;&#2381;&#2359;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> 6% </span><span style=\"font-family: Cambria Math;\">&#2346;&#2381;&#2352;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2352;&#2381;&#2359;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2330;&#2325;&#2381;&#2352;&#2357;&#2371;&#2342;&#2381;&#2343;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2381;&#2351;&#2366;&#2332;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2332;&#2381;&#2334;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;&#2332;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\">, (</span><span style=\"font-family: Cambria Math;\">&#8377;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2344;&#2367;&#2325;&#2335;&#2340;&#2350;</span><span style=\"font-family: Cambria Math;\">):</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC </span><span style=\"font-family: Cambria Math;\">CHSL 09</span><span style=\"font-family: Cambria Math;\">/06/2022 ( Morning)</span></p>\n",
                    options_en: ["<p>&#8377;4,000</p>\n", "<p>&#8377;4,193</p>\n", 
                                "<p>&#8377;4,393</p>\n", "<p>&#8377;4,388</p>\n"],
                    options_hi: ["<p>&#8377;4,000</p>\n", "<p>&#8377;4,193</p>\n",
                                "<p>&#8377;4,393</p>\n", "<p>&#8377;4,388</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">5.(</span><span style=\"font-family: Cambria Math;\">c)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">CI after 2 yrs </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">= 35000&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfenced open=\"[\" close=\"]\"><mrow><msup><mfenced><mrow><mn>1</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mfrac><mstyle displaystyle=\"true\"><mfrac bevelled=\"true\"><mn>6</mn><mn>2</mn></mfrac></mstyle><mn>100</mn></mfrac></mrow></mfenced><mrow><mn>2</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>2</mn></mrow></msup><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>1</mn></mrow></mfenced></math></span></p>\r\n<p><span style=\"font-family: Cambria Math;\">= 35000&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfenced open=\"[\" close=\"]\"><mrow><msup><mfenced><mrow><mn>1</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mfrac><mstyle displaystyle=\"true\"><mn>3</mn></mstyle><mn>100</mn></mfrac></mrow></mfenced><mn>4</mn></msup><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>1</mn></mrow></mfenced></math></span></p>\r\n<p><span style=\"font-family: Cambria Math;\">= 35000<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfenced open=\"[\" close=\"]\"><mrow><msup><mfenced><mfrac><mstyle displaystyle=\"true\"><mn>103</mn></mstyle><mn>100</mn></mfrac></mfenced><mn>4</mn></msup><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>1</mn></mrow></mfenced></math> </span><span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">= 35000&nbsp; &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>12550881</mn><mrow><mn>10</mn><mo>,</mo><mn>000</mn><mo>,</mo><mn>0000</mn></mrow></mfrac></math></span></p>\r\n<p><span style=\"font-family: Cambria Math;\">= 4392.81&asymp; </span><span style=\"font-family: Cambria Math;\">&nbsp;</span><span style=\"font-family: Cambria Math;\">&#8377;</span><span style=\"font-family: Cambria Math;\">4,393</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">5.(</span><span style=\"font-family: Cambria Math;\">c)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">2 </span><span style=\"font-family: Cambria Math;\">&#2360;&#2366;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2330;&#2325;&#2381;&#2352;&#2357;&#2371;&#2342;&#2381;&#2343;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2381;&#2351;&#2366;&#2332;</span><span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">= 35000&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfenced open=\"[\" close=\"]\"><mrow><msup><mfenced><mrow><mn>1</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mfrac><mstyle displaystyle=\"true\"><mfrac bevelled=\"true\"><mn>6</mn><mn>2</mn></mfrac></mstyle><mn>100</mn></mfrac></mrow></mfenced><mrow><mn>2</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>2</mn></mrow></msup><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>1</mn></mrow></mfenced></math></span></p>\r\n<p><span style=\"font-family: Cambria Math;\">= 35000<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfenced open=\"[\" close=\"]\"><mrow><msup><mfenced><mrow><mn>1</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mfrac><mstyle displaystyle=\"true\"><mn>3</mn></mstyle><mn>100</mn></mfrac></mrow></mfenced><mn>4</mn></msup><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>1</mn></mrow></mfenced></math> </span><span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">= 35000&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfenced open=\"[\" close=\"]\"><mrow><msup><mfenced><mfrac><mstyle displaystyle=\"true\"><mn>103</mn></mstyle><mn>100</mn></mfrac></mfenced><mn>4</mn></msup><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>1</mn></mrow></mfenced></math></span></p>\r\n<p><span style=\"font-family: Cambria Math;\">= 35000 &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>12550881</mn><mrow><mn>10</mn><mo>,</mo><mn>000</mn><mo>,</mo><mn>0000</mn></mrow></mfrac></math></span></p>\r\n<p><span style=\"font-family: Cambria Math;\">= 4392.81 &asymp;&nbsp;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#8377;</span><span style=\"font-family: Cambria Math;\">4,393</span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">6. </span><span style=\"font-family: Cambria Math;\">On a sum of money, when invested for 2 years, compound interest and simple interest are &#8377;300 and &#8377;250, respectively. For both simple and compound interests the rate of interest per annum is the same, and for compound interest, interest is comp</span><span style=\"font-family: Cambria Math;\">ounded annually. Find the rate of interest per annum.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC </span><span style=\"font-family: Cambria Math;\">CHSL 09</span><span style=\"font-family: Cambria Math;\">/06/2022 (Afternoon)</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">6.</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2343;&#2344;&#2352;&#2366;&#2358;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2332;&#2348;</span><span style=\"font-family: Cambria Math;\"> 2 </span><span style=\"font-family: Cambria Math;\">&#2357;&#2352;&#2381;&#2359;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2344;&#2367;&#2357;&#2375;&#2358;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2332;&#2366;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2330;&#2325;&#2381;&#2352;&#2357;&#2371;&#2342;&#2381;&#2343;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2381;&#2351;&#2366;&#2332;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2366;&#2343;&#2366;&#2352;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2381;&#2351;&#2366;&#2332;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2381;&#2352;&#2350;&#2358;&#2307;</span><span style=\"font-family: Cambria Math;\"> &#8377;300 </span><span style=\"font-family: Cambria Math;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> &#8377;250 </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;&#2306;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2366;&#2343;&#2366;&#2352;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2330;&#2325;&#2381;&#2352;</span><span style=\"font-family: Cambria Math;\">&#2357;&#2371;&#2342;&#2381;&#2343;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2381;&#2351;&#2366;&#2332;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2379;&#2344;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2381;&#2352;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2352;&#2381;&#2359;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2381;&#2351;&#2366;&#2332;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2330;&#2325;&#2381;&#2352;&#2357;&#2371;&#2342;&#2381;&#2343;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2381;&#2351;&#2366;&#2332;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2348;&#2381;&#2351;&#2366;&#2332;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2366;&#2352;&#2381;&#2359;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2352;&#2370;&#2346;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2306;&#2351;&#2379;&#2332;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2379;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2366;&#2352;&#2381;&#2359;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2381;&#2351;&#2366;&#2332;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2332;&#2381;&#2334;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;&#2332;&#2367;&#2319;&#2404;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC </span><span style=\"font-family: Cambria Math;\">CHSL 09</span><span style=\"font-family: Cambria Math;\">/06/2022 (Afternoon)</span></p>\n",
                    options_en: ["<p>10%</p>\n", "<p>20%</p>\n", 
                                "<p>40%</p>\n", "<p>30%</p>\n"],
                    options_hi: ["<p>10%</p>\n", "<p>20%</p>\n",
                                "<p>40%</p>\n", "<p>30%</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">6.(</span><span style=\"font-family: Cambria Math;\">c)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SI for 2 years = &#8377;250</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SI for 1 year = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>250</mn><mn>2</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> = &#8377;125</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">CI for 2nd </span><span style=\"font-family: Cambria Math;\">year =</span><span style=\"font-family: Cambria Math;\"> 300 &ndash; 250 = &#8377;50</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Since the rate of CI and SI is the same.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">So, rate of interest per annum = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>50</mn><mn>125</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> &times; 100 = 40%</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">6.(</span><span style=\"font-family: Cambria Math;\">c)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">2 </span><span style=\"font-family: Cambria Math;\">&#2360;&#2366;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2366;&#2343;&#2366;&#2352;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2381;&#2351;&#2366;&#2332;</span><span style=\"font-family: Cambria Math;\"> = </span><span style=\"font-family: Cambria Math;\">&#8377;</span><span style=\"font-family: Cambria Math;\">250</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">1 </span><span style=\"font-family: Cambria Math;\">&#2357;&#2352;&#2381;&#2359;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2366;&#2343;&#2366;&#2352;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2381;&#2351;&#2366;&#2332;</span><span style=\"font-family: Cambria Math;\"> =&nbsp; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>250</mn><mn>2</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\">= &#8377;</span><span style=\"font-family: Cambria Math;\">125</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&#2342;&#2370;&#2360;&#2352;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2352;&#2381;&#2359;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2330;&#2325;&#2381;&#2352;&#2357;&#2371;&#2342;&#2381;&#2343;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2381;&#2351;&#2366;&#2332;</span><span style=\"font-family: Cambria Math;\"> = 300 &ndash; 250 = </span><span style=\"font-family: Cambria Math;\">&#8377;</span><span style=\"font-family: Cambria Math;\">50</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&#2330;&#2370;&#2306;&#2325;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2330;&#2325;&#2381;&#2352;&#2357;&#2371;&#2342;&#2381;&#2343;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2381;&#2351;&#2366;&#2332;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2366;&#2343;&#2366;&#2352;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2381;&#2351;&#2366;&#2332;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;&#2404;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&#2309;&#2340;</span><span style=\"font-family: Cambria Math;\">: </span><span style=\"font-family: Cambria Math;\">&#2357;&#2366;&#2352;&#2381;&#2359;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2381;&#2351;&#2366;&#2332;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2352;</span><span style=\"font-family: Cambria Math;\"> =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>50</mn><mn>125</mn></mfrac></math> </span><span style=\"font-family: Cambria Math;\"> &times; 100 = 40%</span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">7. </span><span style=\"font-family: Cambria Math;\">A man borrows a sum of &#8377;1,025 and pays back in two equal yearly installments. If the rate of inter</span><span style=\"font-family: Cambria Math;\">est is 5% p.a, compounded yearly, then how much is each installment?</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC </span><span style=\"font-family: Cambria Math;\">CHSL 09</span><span style=\"font-family: Cambria Math;\">/06/2022 (Evening)</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">7. </span><span style=\"font-family: Cambria Math;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2381;&#2351;&#2325;&#2381;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\"> &#8377;1,025 </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2352;&#2366;&#2358;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2313;&#2343;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2354;&#2375;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2366;&#2352;&#2381;&#2359;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2367;&#2358;&#2381;&#2340;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2366;&#2346;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2349;&#2369;&#2327;&#2340;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2352;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;&#2404; </span><span style=\"font-family: Cambria Math;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2313;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2381;&#2351;&#2366;&#2332;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2352;</span><span style=\"font-family: Cambria Math;\"> 5% </span><span style=\"font-family: Cambria Math;\">&#2346;&#2381;&#2352;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2352;&#2381;&#2359;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2330;&#2325;&#2381;&#2352;&#2357;&#2371;&#2342;&#2381;&#2343;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2366;&#2352;&#2381;&#2359;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2352;&#2370;&#2346;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2306;&#2351;&#2379;&#2332;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2379;&#2340;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2381;&#2352;&#2340;&#2381;&#2351;&#2375;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2367;&#2358;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2352;&#2366;&#2358;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2332;&#2381;&#2334;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;&#2332;&#2367;&#2319;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC </span><span style=\"font-family: Cambria Math;\">CHSL 09</span><span style=\"font-family: Cambria Math;\">/06/2022 (Evening)</span></p>\n",
                    options_en: ["<p>&#8377;425.25</p>\n", "<p>&#8377;525.32</p>\n", 
                                "<p>&#8377;451.32</p>\n", "<p>&#8377;551.25</p>\n"],
                    options_hi: ["<p>&#8377;425.25</p>\n", "<p>&#8377;525.32</p>\n",
                                "<p>&#8377;451.32</p>\n", "<p>&#8377;551.25</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">7.(</span><span style=\"font-family: Cambria Math;\">d)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Let each instalment </span><span style=\"font-family: Cambria Math;\">be y</span><span style=\"font-family: Cambria Math;\">.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">According to the question,</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>y</mi><msup><mfenced><mrow><mn>1</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mstyle displaystyle=\"true\"><mfrac><mn>5</mn><mn>100</mn></mfrac></mstyle></mrow></mfenced><mn>2</mn></msup></mfrac><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mfrac><mi>y</mi><mfenced><mrow><mn>1</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mstyle displaystyle=\"true\"><mfrac><mn>5</mn><mn>100</mn></mfrac></mstyle></mrow></mfenced></mfrac></math>&nbsp;</span><span style=\"font-family: Cambria Math;\">= 1025</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>y</mi><msup><mfenced><mstyle displaystyle=\"true\"><mfrac><mn>21</mn><mn>20</mn></mfrac></mstyle></mfenced><mn>2</mn></msup></mfrac><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mfrac><mi>y</mi><mfenced><mstyle displaystyle=\"true\"><mfrac><mn>21</mn><mn>20</mn></mfrac></mstyle></mfenced></mfrac></math></span><span style=\"font-family: Cambria Math;\">= 1025</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>400</mn><mi>y</mi></mrow><mn>441</mn></mfrac><mo>&nbsp;</mo><mo>+</mo><mfrac><mrow><mo>&nbsp;</mo><mn>20</mn><mi>y</mi></mrow><mn>21</mn></mfrac></math>&nbsp;</span><span style=\"font-family: Cambria Math;\">= 1025</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>400</mn><mi>y</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>420</mn><mi>y</mi></mrow><mn>441</mn></mfrac></math>= 1025</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">820y</span><span style=\"font-family: Cambria Math;\"> = 1025 &times; </span><span style=\"font-family: Cambria Math;\">441</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">y = </span><span style=\"font-family: Cambria Math;\">&#8377;</span><span style=\"font-family: Cambria Math;\">551.25</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">7.(</span><span style=\"font-family: Cambria Math;\">d)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&#2350;&#2366;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2381;&#2352;&#2340;&#2381;&#2351;&#2375;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2367;&#2358;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\"> y </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;&#2404;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&#2346;&#2381;&#2352;&#2358;&#2381;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2309;&#2344;&#2369;&#2360;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\">,</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>y</mi><msup><mfenced><mrow><mn>1</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mstyle displaystyle=\"true\"><mfrac><mn>5</mn><mn>100</mn></mfrac></mstyle></mrow></mfenced><mn>2</mn></msup></mfrac><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mfrac><mi>y</mi><mfenced><mrow><mn>1</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mstyle displaystyle=\"true\"><mfrac><mn>5</mn><mn>100</mn></mfrac></mstyle></mrow></mfenced></mfrac></math> = 1025</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>y</mi><msup><mfenced><mstyle displaystyle=\"true\"><mfrac><mn>21</mn><mn>20</mn></mfrac></mstyle></mfenced><mn>2</mn></msup></mfrac><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mfrac><mi>y</mi><mfenced><mstyle displaystyle=\"true\"><mfrac><mn>21</mn><mn>20</mn></mfrac></mstyle></mfenced></mfrac></math>= 1025</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>400</mn><mi>y</mi></mrow><mn>441</mn></mfrac><mo>&nbsp;</mo><mo>+</mo><mfrac><mrow><mo>&nbsp;</mo><mn>20</mn><mi>y</mi></mrow><mn>21</mn></mfrac></math>= 1025</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>400</mn><mi>y</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>420</mn><mi>y</mi></mrow><mn>441</mn></mfrac></math>= 1025</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">820y</span><span style=\"font-family: Cambria Math;\">= 1025 &times;&nbsp;</span><span style=\"font-family: Cambria Math;\"> 441</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">y = </span><span style=\"font-family: Cambria Math;\">&#8377;</span><span style=\"font-family: Cambria Math;\">551.25</span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "17",
                    question_en: "<p>8. <span style=\"font-family: Cambria Math;\">A sum of money is invested at a rate of compounded interest that is paid out yearly. In the first two years, the interest was </span><span style=\"font-family: Cambria Math;\">&#8377;</span><span style=\"font-family: Cambria Math;\">400 and &#8377;420 respectively.The sum is:</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC </span><span style=\"font-family: Cambria Math;\">CHSL 10</span><span style=\"font-family: Cambria Math;\">/06/2022 ( Morning)</span></p>\n",
                    question_hi: "<p>8. <span style=\"font-family: Cambria Math;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2352;&#2366;&#2358;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2330;&#2325;&#2381;&#2352;&#2357;&#2371;&#2342;&#2381;&#2343;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2381;&#2351;&#2366;&#2332;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2344;&#2367;&#2357;&#2375;&#2358;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2332;&#2366;&#2340;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2332;&#2367;&#2360;&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2349;&#2369;&#2327;&#2340;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2366;&#2352;&#2381;&#2359;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2352;&#2370;&#2346;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2332;&#2366;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2361;&#2354;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2352;&#2381;&#2359;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2348;&#2381;&#2351;&#2366;&#2332;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2381;&#2352;&#2350;&#2358;&#2307;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#8377;</span><span style=\"font-family: Cambria Math;\">400 </span><span style=\"font-family: Cambria Math;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#8377;</span><span style=\"font-family: Cambria Math;\">420 </span><span style=\"font-family: Cambria Math;\">&#2341;&#2366;&#2404;&#2352;&#2366;&#2358;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC </span><span style=\"font-family: Cambria Math;\">CHSL 10</span><span style=\"font-family: Cambria Math;\">/06/2022 ( Morning)</span></p>\n",
                    options_en: ["<p>&#8377;7,500</p>\n", "<p>&#8377;9,530</p>\n", 
                                "<p>&#8377;8,000</p>\n", "<p>&#8377;8,765</p>\n"],
                    options_hi: ["<p>&#8377;7,500</p>\n", "<p>&#8377;9,530</p>\n",
                                "<p>&#8377;8,000</p>\n", "<p>&#8377;8,765</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">8.(</span><span style=\"font-family: Cambria Math;\">c)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Interest rate = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>420</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>400</mn></mrow><mn>400</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> &times; 100 = 5%</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">According to the question,</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">5% = 400</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">100% = 8,000</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Principal = 8,000</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">8.(</span><span style=\"font-family: Cambria Math;\">c)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&#2348;&#2381;&#2351;&#2366;&#2332;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2352;</span><span style=\"font-family: Cambria Math;\"> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>420</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>400</mn></mrow><mn>400</mn></mfrac></math> &times; 100 </span><span style=\"font-family: Cambria Math;\">= 5%</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&#2346;&#2381;&#2352;&#2358;&#2381;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2309;&#2344;&#2369;&#2360;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\">,</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">5% = 400</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">100% = 8,000</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&#2350;&#2370;&#2354;&#2343;&#2344;</span><span style=\"font-family: Cambria Math;\"> = 8,000</span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "17",
                    question_en: "<p>9. <span style=\"font-family: Cambria Math;\">If the compound interest on a principal for one year is &#8377;350 and the compound interest for the second year is &#8377;420, find the rate of interest.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC </span><span style=\"font-family: Cambria Math;\">CHSL 10</span><span style=\"font-family: Cambria Math;\">/06/2022 ( Morning)</span></p>\n",
                    question_hi: "<p>9. <span style=\"font-family: Cambria Math;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2352;&#2381;&#2359;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2370;&#2354;&#2343;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2330;&#2325;&#2381;&#2352;&#2357;&#2371;&#2342;&#2381;&#2343;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2381;&#2351;&#2366;&#2332;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#8377;</span><span style=\"font-family: Cambria Math;\">350 </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2370;&#2360;&#2352;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2352;&#2381;&#2359;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2330;&#2325;&#2381;&#2352;&#2357;&#2371;&#2342;&#2381;&#2343;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2381;&#2351;&#2366;&#2332;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#8377;</span><span style=\"font-family: Cambria Math;\">420 </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2381;&#2351;&#2366;&#2332;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2332;&#2381;&#2334;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;&#2332;&#2367;&#2319;&#2404;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC </span><span style=\"font-family: Cambria Math;\">CHSL 10</span><span style=\"font-family: Cambria Math;\">/06/2022 ( Morning)</span></p>\n",
                    options_en: ["<p>30%</p>\n", "<p>20%</p>\n", 
                                "<p>25%</p>\n", "<p>15%</p>\n"],
                    options_hi: ["<p>30%</p>\n", "<p>20%</p>\n",
                                "<p>25%</p>\n", "<p>15%</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">9.(</span><span style=\"font-family: Cambria Math;\">b)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Interest of 2nd year = 420 - 350 = 70</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Ra</span><span style=\"font-family: Cambria Math;\">te of interest =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>70</mn><mn>350</mn></mfrac><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>100</mn></math>&nbsp;</span><span style=\"font-family: Cambria Math;\"> = 20%</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">9.(</span><span style=\"font-family: Cambria Math;\">b)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&#2342;&#2370;&#2360;&#2352;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2352;&#2381;&#2359;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\">&#2332;</span><span style=\"font-family: Cambria Math;\"> =</span><span style=\"font-family: Cambria Math;\"> 420 - 350 = 70</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&#2348;&#2381;&#2351;&#2366;&#2332;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2352;</span><span style=\"font-family: Cambria Math;\"> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>70</mn><mn>350</mn></mfrac><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>100</mn></math></span><span style=\"font-family: Cambria Math;\"> = 20%</span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "17",
                    question_en: "<p>10<span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Cambria Math;\">A sum of &#8377;5,000 was deposited for 3 years at 10% per annum, compounded annually. The difference between the interest for 2 years and that</span><span style=\"font-family: Cambria Math;\"> for 3 years is:</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC </span><span style=\"font-family: Cambria Math;\">CHSL 10</span><span style=\"font-family: Cambria Math;\">/06/2022 (Afternoon)</span></p>\n",
                    question_hi: "<p>10<span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Cambria Math;\">&#8377;</span><span style=\"font-family: Cambria Math;\">5,000 </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2352;&#2366;&#2358;&#2367;</span><span style=\"font-family: Cambria Math;\"> 10% </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2366;&#2352;&#2381;&#2359;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2381;&#2351;&#2366;&#2332;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> 3 </span><span style=\"font-family: Cambria Math;\">&#2357;&#2352;&#2381;&#2359;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2332;&#2350;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2327;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2332;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2366;&#2352;&#2381;&#2359;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2352;&#2370;&#2346;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2330;&#2325;&#2381;&#2352;&#2357;&#2371;&#2342;&#2381;&#2343;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2379;&#2340;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> 2 </span><span style=\"font-family: Cambria Math;\">&#2357;&#2352;&#2381;&#2359;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> 3 </span><span style=\"font-family: Cambria Math;\">&#2357;&#2352;&#2381;&#2359;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2381;&#2351;&#2366;&#2332;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2309;&#2306;&#2340;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2367;&#2340;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2379;&#2327;&#2366;</span><span style=\"font-family: Cambria Math;\">?</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC </span><span style=\"font-family: Cambria Math;\">CHSL 10</span><span style=\"font-family: Cambria Math;\">/06/2022 (Afternoon)</span></p>\n",
                    options_en: ["<p>&#8377;560</p>\n", "<p>&#8377;506</p>\n", 
                                "<p>&#8377;650</p>\n", "<p>&#8377;605</p>\n"],
                    options_hi: ["<p>&#8377;560</p>\n", "<p>&#8377;506</p>\n",
                                "<p>&#8377;650</p>\n", "<p>&#8377;605</p>\n"],
                    solution_en: "<p>10<span style=\"font-family: Cambria Math;\">.(d)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Effective interest rate in 3 year = 33.1%</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Effective interest rate in 2 year = 21%</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Difference = 33.1 - 21 = 12.1%</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Interest = 5000 &times;&nbsp; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>12</mn><mo>.</mo><mn>1</mn></mrow><mn>100</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\">= 605</span></p>\n",
                    solution_hi: "<p>10<span style=\"font-family: Cambria Math;\">.(d)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">3 </span><span style=\"font-family: Cambria Math;\">&#2360;&#2366;</span><span style=\"font-family: Cambria Math;\">&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2381;&#2352;&#2349;&#2366;&#2357;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2381;&#2351;&#2366;&#2332;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2352;</span><span style=\"font-family: Cambria Math;\"> = 33.1%</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">2 </span><span style=\"font-family: Cambria Math;\">&#2360;&#2366;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2381;&#2352;&#2349;&#2366;&#2357;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2381;&#2351;&#2366;&#2332;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2352;</span><span style=\"font-family: Cambria Math;\"> = 21%</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&#2309;&#2306;&#2340;&#2352;</span><span style=\"font-family: Cambria Math;\"> = 33.1 - 21 = 12.1%</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&#2348;&#2381;&#2351;&#2366;&#2332;</span><span style=\"font-family: Cambria Math;\"> = 5000 &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>12</mn><mo>.</mo><mn>1</mn></mrow><mn>100</mn></mfrac></math> </span><span style=\"font-family: Cambria Math;\">= 605</span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">11</span><span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Cambria Math;\">Find the compound interest on &#8377;5</span><span style=\"font-family: Cambria Math;\">,50,000</span><span style=\"font-family: Cambria Math;\"> at 7% per annum for 2 years, compounded annually.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC </span><span style=\"font-family: Cambria Math;\">CHSL 10</span><span style=\"font-family: Cambria Math;\">/06/2022 (Afternoon)</span></p>\n",
                    question_hi: "<p>11<span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Cambria Math;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2381;&#2351;&#2366;&#2332;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2366;&#2352;&#2381;&#2359;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2352;&#2370;&#2346;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2330;&#2325;&#2381;&#2352;&#2357;&#2371;&#2342;&#2381;&#2343;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2379;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#8377;</span><span style=\"font-family: Cambria Math;\">5</span><span style=\"font-family: Cambria Math;\">,50,000</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2352;&#2366;&#2358;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> 7% </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2366;&#2352;&#2381;&#2359;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> 2 </span><span style=\"font-family: Cambria Math;\">&#2357;&#2352;&#2381;&#2359;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2381;&#2352;&#2366;&#2346;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2379;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2366;&#2354;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2330;&#2325;&#2381;&#2352;&#2357;&#2371;&#2342;&#2381;&#2343;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2381;&#2351;&#2366;&#2332;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2332;&#2381;&#2334;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;&#2367;&#2332;&#2319;&#2404;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC </span><span style=\"font-family: Cambria Math;\">CHSL 10</span><span style=\"font-family: Cambria Math;\">/06/2022 (Afternoon)</span></p>\n",
                    options_en: ["<p>&#8377;80,<span style=\"font-family: Cambria Math;\">605</span></p>\n", "<p>&#8377;62,695</p>\n", 
                                "<p>&#8377;79,690</p>\n", "<p>&#8377;79,695</p>\n"],
                    options_hi: ["<p>&#8377;80,605</p>\n", "<p>&#8377;62,695</p>\n",
                                "<p>&#8377;79,690</p>\n", "<p>&#8377;79,695</p>\n"],
                    solution_en: "<p>11<span style=\"font-family: Cambria Math;\">.(d)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Required CI </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">= 550000 <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfenced open=\"[\" close=\"]\"><mrow><msup><mfenced><mrow><mn>1</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mfrac><mn>7</mn><mn>100</mn></mfrac></mrow></mfenced><mn>2</mn></msup><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>1</mn></mrow></mfenced></math></span><span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">= 550000 <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfenced open=\"[\" close=\"]\"><mrow><msup><mfenced><mrow><mo>&nbsp;</mo><mfrac><mn>107</mn><mn>100</mn></mfrac></mrow></mfenced><mn>2</mn></msup><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>1</mn></mrow></mfenced></math></span><span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">= 550000 <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfenced open=\"[\" close=\"]\"><mrow><mfrac><mn>11449</mn><mn>10000</mn></mfrac><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>1</mn></mrow></mfenced></math></span><span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">= 550000 <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfenced open=\"[\" close=\"]\"><mfrac><mn>1449</mn><mn>10000</mn></mfrac></mfenced></math></span></p>\r\n<p><span style=\"font-family: Cambria Math;\">= </span><span style=\"font-family: Cambria Math;\">&#8377;</span><span style=\"font-family: Cambria Math;\">79,695</span></p>\n",
                    solution_hi: "<p>11<span style=\"font-family: Cambria Math;\">.(d)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&#2310;&#2357;&#2358;&#2381;&#2351;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2330;&#2325;&#2381;&#2352;&#2357;&#2371;&#2342;&#2381;&#2343;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2381;&#2351;&#2366;&#2332;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">= 550000 <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfenced open=\"[\" close=\"]\"><mrow><msup><mfenced><mrow><mn>1</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mfrac><mn>7</mn><mn>100</mn></mfrac></mrow></mfenced><mn>2</mn></msup><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>1</mn></mrow></mfenced></math></span><span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">= 550000 <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfenced open=\"[\" close=\"]\"><mrow><msup><mfenced><mrow><mo>&nbsp;</mo><mfrac><mn>107</mn><mn>100</mn></mfrac></mrow></mfenced><mn>2</mn></msup><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>1</mn></mrow></mfenced></math></span></p>\r\n<p><span style=\"font-family: Cambria Math;\">= 550000 <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfenced open=\"[\" close=\"]\"><mrow><mfrac><mn>11449</mn><mn>10000</mn></mfrac><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>1</mn></mrow></mfenced></math></span><span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">= 550000 <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfenced open=\"[\" close=\"]\"><mfrac><mn>1449</mn><mn>10000</mn></mfrac></mfenced></math></span><span style=\"font-family: Cambria Math;\">&nbsp;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">= </span><span style=\"font-family: Cambria Math;\">&#8377;</span><span style=\"font-family: Cambria Math;\">79,695</span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "misc",
                    question_en: "<p>12<span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Cambria Math;\">A sum of &#8377;14,375, when invested at r% interest per year compounded annually, amounts to &#8377;16,767 after two years. What is the value of r?</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC </span><span style=\"font-family: Cambria Math;\">CHSL 10</span><span style=\"font-family: Cambria Math;\">/06/2022 (E</span><span style=\"font-family: Cambria Math;\">vening)</span></p>\n",
                    question_hi: "<p>12<span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Cambria Math;\">&#8377;</span><span style=\"font-family: Cambria Math;\">14,375 </span><span style=\"font-family: Mangal;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2352;&#2366;&#2358;&#2367;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Mangal;\">&#2332;&#2348;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2357;&#2366;&#2352;&#2381;&#2359;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2310;&#2343;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2330;&#2325;&#2381;&#2352;&#2357;&#2371;&#2342;&#2381;&#2343;</span><span style=\"font-family: Mangal;\">&#2367;</span><span style=\"font-family: Cambria Math;\"> r</span><span style=\"font-family: Cambria Math;\">% </span><span style=\"font-family: Mangal;\">&#2348;&#2381;&#2351;&#2366;&#2332;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2344;&#2367;&#2357;&#2375;&#2358;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2332;&#2366;&#2340;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Mangal;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2342;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2357;&#2352;&#2381;&#2359;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2348;&#2366;&#2342;</span><span style=\"font-family: Cambria Math;\"> &#8377;16,767 </span><span style=\"font-family: Mangal;\">&#2361;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2332;&#2366;&#2340;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> r </span><span style=\"font-family: Mangal;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2332;&#2381;&#2334;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2368;&#2332;&#2367;&#2319;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC </span><span style=\"font-family: Cambria Math;\">CHSL 10</span><span style=\"font-family: Cambria Math;\">/06/2022 (Evening)</span></p>\n",
                    options_en: ["<p>9</p>\n", "<p>6</p>\n", 
                                "<p>7</p>\n", "<p>8</p>\n"],
                    options_hi: ["<p>9</p>\n", "<p>6</p>\n",
                                "<p>7</p>\n", "<p>8</p>\n"],
                    solution_en: "<p>12<span style=\"font-family: Cambria Math;\">.(d)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Principle = 14,375</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Amount = 16,767</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">P :</span><span style=\"font-family: Cambria Math;\"> A = 16,767 : 14,375</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">16,767 = 14,375<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mfenced><mrow><mn>1</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mfrac><mi>r</mi><mn>100</mn></mfrac></mrow></mfenced><mn>2</mn></msup></math></span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>16767</mn><mn>14375</mn></mfrac><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><msup><mfenced><mrow><mn>1</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mfrac><mi>r</mi><mn>100</mn></mfrac></mrow></mfenced><mn>2</mn></msup></math></span></p>\r\n<p><span style=\"font-family: Cambria Math;\">1.1664 =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mfenced><mrow><mn>1</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mfrac><mi>r</mi><mn>100</mn></mfrac></mrow></mfenced><mn>2</mn></msup></math></span></p>\r\n<p><span style=\"font-family: Cambria Math;\">1.08 =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfenced><mrow><mn>1</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mfrac><mi>r</mi><mn>100</mn></mfrac></mrow></mfenced></math></span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>r</mi><mn>100</mn></mfrac></math>&nbsp; = 0.08</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">r = 8%</span></p>\n",
                    solution_hi: "<p>12<span style=\"font-family: Cambria Math;\">.(d)</span></p>\r\n<p><span style=\"font-family: Mangal;\">&#2350;&#2370;</span><span style=\"font-family: Mangal;\">&#2354;&#2343;&#2344;</span><span style=\"font-family: Cambria Math;\"> =</span><span style=\"font-family: Cambria Math;\"> 14,375</span></p>\r\n<p><span style=\"font-family: Mangal;\">&#2352;&#2366;&#2358;&#2367;</span><span style=\"font-family: Cambria Math;\"> = 16,767</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">P :</span><span style=\"font-family: Cambria Math;\"> A = 16,767 : 14,375</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">16,767 </span><span style=\"font-family: Cambria Math;\">= 14,375<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mfenced><mrow><mn>1</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mfrac><mi>r</mi><mn>100</mn></mfrac></mrow></mfenced><mn>2</mn></msup></math></span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>16767</mn><mn>14375</mn></mfrac><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><msup><mfenced><mrow><mn>1</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mfrac><mi>r</mi><mn>100</mn></mfrac></mrow></mfenced><mn>2</mn></msup></math></span></p>\r\n<p><span style=\"font-family: Cambria Math;\">1.1664 =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&nbsp;</mo><msup><mfenced><mrow><mn>1</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mfrac><mi>r</mi><mn>100</mn></mfrac></mrow></mfenced><mn>2</mn></msup></math></span></p>\r\n<p><span style=\"font-family: Cambria Math;\">1.08 =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfenced><mrow><mn>1</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mfrac><mi>r</mi><mn>100</mn></mfrac></mrow></mfenced></math></span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>r</mi><mn>100</mn></mfrac></math>= 0.08</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">r = 8%</span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>