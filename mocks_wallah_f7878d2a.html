<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">12:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 25</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">25</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 12 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["7"] = {
                name: "English",
                start: 0,
                end: 23
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="7">English</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 24,
                end: 24
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "7",
                    question_en: "<p>1. Select the most appropriate option to substitute the underlined segment in the given sentence. <br>It is very hard for her to provide <span style=\"text-decoration: underline;\">the necessities of her family</span>.</p>",
                    question_hi: "<p>1. Select the most appropriate option to substitute the underlined segment in the given sentence. <br>It is very hard for her to provide <span style=\"text-decoration: underline;\">the necessities of her family</span>.</p>",
                    options_en: ["<p>the necessities to her family</p>", "<p>the necessities by her family</p>", 
                                "<p>the necessity of her family</p>", "<p>the necessities from her families</p>"],
                    options_hi: ["<p>the necessities to her family</p>", "<p>the necessities by her family</p>",
                                "<p>the necessity of her family</p>", "<p>the necessities from her families</p>"],
                    solution_en: "<p>1.(a) the necessities to her family<br>&lsquo;To&rsquo; is used to indicate the receiver of an action or object. In the given sentence, &lsquo;her family&rsquo; is the receiver of the necessities. Hence, &lsquo;the necessities to her family&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>1.(a) the necessities to her family<br>&lsquo;To&rsquo; का use किसी क्रिया (action) या वस्तु (object) के प्राप्तकर्ता (receiver) को indicate करने के लिए किया जाता है। दिए गए sentence में, &lsquo;her family,&rsquo; necessities की receiver है। अतः, &lsquo;the necessities to her family&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "7",
                    question_en: "2. The following sentence has been divided into four segments. Identify the segment in which the article has been used INCORRECTLY. <br />UNESCO works to / strengthen an intellectual / and moral solidarity / of humankind.",
                    question_hi: "2. The following sentence has been divided into four segments. Identify the segment in which the article has been used INCORRECTLY. <br />UNESCO works to / strengthen an intellectual / and moral solidarity / of humankind.",
                    options_en: [" strengthen an intellectual", " of humankind.", 
                                " UNESCO works to", " and moral solidarity"],
                    options_hi: [" strengthen an intellectual", " of humankind.",
                                " UNESCO works to", " and moral solidarity"],
                    solution_en: "2.(a) strengthen an intellectual<br />Indefinite article ‘a/an’ is not used for an abstract noun(solidarity). The definite article ‘the’ is used before a specific or particular noun. The given sentence is referring to a specific type of solidarity. Hence, ‘strengthen the intellectual and moral solidarity’ is the most appropriate answer.",
                    solution_hi: "2.(a) strengthen an intellectual<br />Indefinite article ‘a/an’ का प्रयोग abstract noun (solidarity) के लिए नहीं किया जाता है। Definite article ‘the’ का प्रयोग specific या particular noun से पहले किया जाता है। दिया गया sentence, specific प्रकार की एकजुटता(solidarity) को संदर्भित कर रहा है। अतः, ‘strengthen the intellectual and moral solidarity’ सबसे उपयुक्त उत्तर है।",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "7",
                    question_en: "<p>3. Select the option that can be used as a one-word substitute for the given group of words.</p>\n<p>The study of coins</p>",
                    question_hi: "<p>3. Select the option that can be used as a one-word substitute for the given group of words.<br>The study of coins</p>",
                    options_en: ["<p>Numismatics</p>", "<p>Choreography</p>", 
                                "<p>Informatics</p>", "<p>Cartography</p>"],
                    options_hi: ["<p>Numismatics</p>", "<p>Choreography</p>",
                                "<p>Informatics</p>", "<p>Cartography</p>"],
                    solution_en: "<p>3.(a) <strong>Numismatics-</strong> the study of coins.<br><strong>Choreography-</strong> the sequence of steps and movements in dance or figure skating, especially in a ballet or other staged dance.<br><strong>Informatics-</strong> the science of processing data for storage and retrieval.<br><strong>Cartography-</strong> the science or practice of drawing maps.</p>",
                    solution_hi: "<p>3.(a) <strong>Numismatics</strong> (मुद्राशास्त्र)- the study of coins.<br><strong>Choreography</strong> (नृत्यकला)- the sequence of steps and movements in dance or figure skating, especially in a ballet or other staged dance.<br><strong>Informatics</strong> (सूचना प्रौद्योगिकी)- the science of processing data for storage and retrieval.<br><strong>Cartography</strong> (मानचित्रण)- the science or practice of drawing maps.</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "7",
                    question_en: "4. Select the INCORRECTLY spelt word.",
                    question_hi: "4. Select the INCORRECTLY spelt word.",
                    options_en: [" Eficient", " Contribution", 
                                " Miscalculation", " Mediation"],
                    options_hi: [" Eficient", " Contribution",
                                " Miscalculation", " Mediation"],
                    solution_en: "4.(a) Eficient<br />\'Efficient\' is the correct spelling.",
                    solution_hi: "4.(a) Eficient<br />\'Efficient\' सही spelling है। ",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "7",
                    question_en: "5. Select the option that can be used as a one-word substitute for the given group of words. A person who is inclined to see the worst aspect of things",
                    question_hi: "5. Select the option that can be used as a one-word substitute for the given group of words.<br />A person who is inclined to see the worst aspect of things",
                    options_en: [" Pessimist", " Philanthropist", 
                                " Optimist", " Altruist"],
                    options_hi: [" Pessimist", " Philanthropist",
                                " Optimist", " Altruist"],
                    solution_en: "<p>5.(a) <strong>Pessimist-</strong> a person who is inclined to see the worst aspect of things.<br><strong>Philanthropist-</strong> a person who seeks to promote the welfare of others, especially by the generous donation of money to good causes.<br><strong>Optimist-</strong> a person who is inclined to be hopeful and to expect good outcomes.<br><strong>Altruist-</strong> a person who cares about others and helps them despite not gaining anything by doing this.</p>",
                    solution_hi: "<p>5.(a) <strong>Pessimist</strong> (निराशावादी)- a person who is inclined to see the worst aspect of things.<br><strong>Philanthropist</strong> (परोपकारी)- a person who seeks to promote the welfare of others, especially by the generous donation of money to good causes.<br><strong>Optimist</strong> (आशावादी)- a person who is inclined to be hopeful and to expect good outcomes.<br><strong>Altruist</strong> (परोपकारी)- a person who cares about others and helps them despite not gaining anything by doing this.</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "7",
                    question_en: "<p>6. Select the option that expresses the given sentence in passive voice. <br>He will give a lecture on personality development.</p>",
                    question_hi: "<p>6. Select the option that expresses the given sentence in passive voice. <br>He will give a lecture on personality development.</p>",
                    options_en: ["<p>A lecture on personality development can be given by him.</p>", "<p>A lecture will be given on personality development.</p>", 
                                "<p>A lecture on personality development will be given by him.</p>", "<p>A lecture on personality development will be giving by him.</p>"],
                    options_hi: ["<p>A lecture on personality development can be given by him.</p>", "<p>A lecture will be given on personality development.</p>",
                                "<p>A lecture on personality development will be given by him.</p>", "<p>A lecture on personality development will be giving by him.</p>"],
                    solution_en: "<p>6.(c) A lecture on personality development will be given by him. (Correct)<br>(a) A lecture on personality development <span style=\"text-decoration: underline;\">can</span> be given by him. (Incorrect Modal)<br>(b) A lecture will be given on personality development. (Incorrect Sentence Structure)<br>(c) A lecture on personality development will be <span style=\"text-decoration: underline;\">giving</span> by him. (Incorrect form of the Verb)</p>",
                    solution_hi: "<p>6.(c) A lecture on personality development will be given by him. (Correct)<br>(a) A lecture on personality development <span style=\"text-decoration: underline;\">can</span> be given by him. (गलत Modal)<br>(b) A lecture will be given on personality development. (गलत Sentence Structure)<br>(c) A lecture on personality development will be <span style=\"text-decoration: underline;\">giving</span> by him. (Verb की गलत form)</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "7",
                    question_en: "7. Sentences of a paragraph are given below in jumbled order. Arrange the sentences in the correct order to form a meaningful and coherent paragraph. Note: This is an advertisement of an ice-cream brand. <br />(A) That’s why Silky is deliciously different and exceptionally versatile, natural, thick and durable ice cream. <br />(B) Beautifully firm, nice, silky rather than thin and watery, it stays in shape and stands on top of fruits or pastries without soaking in. <br />(C) Silky thick double ice cream has a unique, fresh taste and texture",
                    question_hi: "7. Sentences of a paragraph are given below in jumbled order. Arrange the sentences in the correct order to form a meaningful and coherent paragraph. Note: This is an advertisement of an ice-cream brand. <br />(A) That’s why Silky is deliciously different and exceptionally versatile, natural, thick and durable ice cream. <br />(B) Beautifully firm, nice, silky rather than thin and watery, it stays in shape and stands on top of fruits or pastries without soaking in. <br />(C) Silky thick double ice cream has a unique, fresh taste and texture",
                    options_en: [" ABC", " BAC", 
                                " CBA", " BCA"],
                    options_hi: [" ABC", " BAC",
                                " CBA", " BCA"],
                    solution_en: "7.(c) CBA<br />Sentence C will be the starting line as it introduces the main idea of the parajumble, i.e. “Silky thick double ice cream has a unique, fresh taste and texture”. And Sentence B states that it stays in shape and stands on top of fruits or pastries due to being beautifully firm. So, B will follow C. Finally, Sentence A concludes the parajumble by stating that Silky is deliciously different and exceptionally versatile for the aforementioned reasons. Going through the options, option ‘c’ has the correct sequence.",
                    solution_hi: "7.(c) CBA<br />Sentence C प्रारंभिक line होगी क्योंकि यह parajumble के मुख्य विचार  “Silky thick double ice cream has a unique, fresh taste and texture” का परिचय देता है। और Sentence B बताता है कि यह shape में बना रहती है और खूबसूरती से दृढ़ होने के कारण fruits या pastries के ऊपर दटी रहती है। इसलिए, C के बाद B आएगा। अंत में, Sentence A यह बताकर parajumble का समापन करता है कि उपर्युक्त कारणों से Silky स्वादिष्ट रूप से भिन्न और असाधारण रूप से versatile है। अतः options के माध्यम से जाने पर,  option ‘c’ में सही sequence है।",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "7",
                    question_en: "<p>8. Select the most appropriate synonym of the given word. <br>Bully</p>",
                    question_hi: "<p>8. Select the most appropriate synonym of the given word. <br>Bully</p>",
                    options_en: ["<p>Plough</p>", "<p>Oxen</p>", 
                                "<p>Coddle</p>", "<p>Oppressor</p>"],
                    options_hi: ["<p>Plough</p>", "<p>Oxen</p>",
                                "<p>Coddle</p>", "<p>Oppressor</p>"],
                    solution_en: "<p>8.(d) <strong>Oppressor-</strong> one who exercises harsh control or authority over others.<br><strong>Bully-</strong> someone who intimidates people, usually those who are weaker or smaller.<br><strong>Plough-</strong> a farming tool used for cutting, lifting, and turning over soil.<br><strong>Oxen-</strong> domesticated cattle, typically used to pull ploughs.<br><strong>Coddle-</strong> to treat someone in an overly protective or indulgent manner.</p>",
                    solution_hi: "<p>8.(d) <strong>Oppressor</strong> (अत्याचारी)- one who exercises harsh control or authority over others.<br><strong>Bully</strong> (धौंसिया/दबंग)- someone who intimidates people, usually those who are weaker or smaller.<br><strong>Plough</strong> (हल)- a farming tool used for cutting, lifting, and turning over soil.<br><strong>Oxen</strong> (बैल/वृषभ)- domesticated cattle, typically used to pull ploughs.<br><strong>Coddle</strong> (लाड़ प्यार करना)- to treat someone in an overly protective or indulgent manner.</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "7",
                    question_en: "<p>9. Select the most appropriate synonym of the given word. <br>Thankful</p>",
                    question_hi: "<p>9. Select the most appropriate synonym of the given word. <br>Thankful</p>",
                    options_en: ["<p>Ungrateful</p>", "<p>Affirmation</p>", 
                                "<p>Obliged</p>", "<p>Obscure</p>"],
                    options_hi: ["<p>Ungrateful</p>", "<p>Affirmation</p>",
                                "<p>Obliged</p>", "<p>Obscure</p>"],
                    solution_en: "<p>9.(c) <strong>Obliged-</strong> feeling indebted or grateful.<br><strong>Thankful-</strong> expressing gratitude or appreciation.<br><strong>Ungrateful-</strong> not showing or expressing thanks.<br><strong>Affirmation-</strong> a positive assertion or confirmation.<br><strong>Obscure-</strong> not clear or difficult to understand.</p>",
                    solution_hi: "<p>9.(c) <strong>Obliged</strong> (कृतज्ञ होना)- feeling indebted or grateful.<br><strong>Thankful</strong> (आभारी)- expressing gratitude or appreciation.<br><strong>Ungrateful</strong> (अहसान फरामोश)- not showing or expressing thanks.<br><strong>Affirmation</strong> (पुष्टिकरण)- a positive assertion or confirmation.<br><strong>Obscure</strong> (अस्पष्ट)- not clear or difficult to understand.</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "7",
                    question_en: "10. Select the most appropriate option to fill in the blank.  <br />Ram went to _____ an ice cream.",
                    question_hi: "10. Select the most appropriate option to fill in the blank.  <br />Ram went to _____ an ice cream.",
                    options_en: [" buy", " by", 
                                " byre", " bye"],
                    options_hi: [" buy", " by",
                                " byre", " bye"],
                    solution_en: "10.(a) buy<br />The given sentence states that Ram went to buy an ice cream. Hence, ‘buy’ is the most appropriate answer.",
                    solution_hi: "10.(a) buy<br />दिए गए sentence में कहा गया है कि राम ice cream खरीदने गया था। अतः, ‘buy’ सबसे उपयुक्त उत्तर है।",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "7",
                    question_en: "11. The following sentence has been split into four segments. Identify the segment that contains an error. <br />Dr. Sinha has / brought about a new / book on social / impact of child labour. ",
                    question_hi: "11. The following sentence has been split into four segments. Identify the segment that contains an error. <br />Dr. Sinha has / brought about a new / book on social / impact of child labour. ",
                    options_en: [" impact of child labour", " book on social ", 
                                " brought about a new", " Dr. Sinha has"],
                    options_hi: [" impact of child labour", " book on social ",
                                " brought about a new", " Dr. Sinha has"],
                    solution_en: "11.(c) brought about a new<br />‘Brought out’ is the correct phrasal verb here, which means ‘published’. The given sentence is talking about publishing a new book. Hence, ‘brought out a new’ is the most appropriate answer.",
                    solution_hi: "11.(c) brought about a new<br />‘Brought out’ यहाँ सही phrasal verb है, जिसका अर्थ है ‘प्रकाशित करना’। दिया गया sentence एक नई पुस्तक के प्रकाशन के बारे में बात कर रहा है। अतः, ‘brought out a new’ सबसे उपयुक्त उत्तर है।",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "7",
                    question_en: "12. Select the option that expresses the given sentence in passive voice. <br />His behaviour vexes me sometimes.  ",
                    question_hi: "12. Select the option that expresses the given sentence in passive voice. <br />His behaviour vexes me sometimes.  ",
                    options_en: [" He is being vexing me with his behaviour.", " Sometimes he vexes me with his behaviour.", 
                                " I am sometimes vexed by his behaviour.", " His behaviour is vexed."],
                    options_hi: [" He is being vexing me with his behaviour", " Sometimes he vexes me with his behaviour.",
                                " I am sometimes vexed by his behaviour.", " His behaviour is vexed."],
                    solution_en: "12.(c) I am sometimes vexed by his behaviour. (Correct)<br />(a) He is being vexing me with his behaviour. (Incorrect Sentence Structure)<br />(b) Sometimes he vexes me with his behaviour. (Incorrect Sentence Structure)<br />(d) His behaviour is vexed. (Incorrect Sentence Structure)",
                    solution_hi: "12.(c) I am sometimes vexed by his behaviour. (Correct)<br />(a) He is being vexing me with his behaviour. (गलत Sentence Structure)<br />(b) Sometimes he vexes me with his behaviour. (गलत Sentence Structure)<br />(d) His behaviour is vexed. (गलत Sentence Structure)",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "7",
                    question_en: "<p>13. Select the sentence that contains a spelling error.</p>",
                    question_hi: "<p>13. Select the sentence that contains a spelling error.</p>",
                    options_en: ["<p>The host institution must provide the delegates with accomodation at a subsidised rate.</p>", "<p>The occurrence of the lunar eclipse is often a mystery for children.</p>", 
                                "<p>The boss addressed the meeting.</p>", "<p>Rail lines go parallel.</p>"],
                    options_hi: ["<p>The host institution must provide the delegates with accomodation at a subsidised rate.</p>", "<p>The occurrence of the lunar eclipse is often a mystery for children.</p>",
                                "<p>The boss addressed the meeting.</p>", "<p>Rail lines go parallel.</p>"],
                    solution_en: "<p>13.(a) The host institution must provide the delegates with <span style=\"text-decoration: underline;\">accomodation</span> at a subsidised rate.<br>&lsquo;Accommodation&rsquo; is the correct spelling.</p>",
                    solution_hi: "<p>13.(a) The host institution must provide the delegates with <span style=\"text-decoration: underline;\">accomodation</span> at a subsidised rate.<br>&lsquo;Accommodation&rsquo; सही spelling है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "7",
                    question_en: "<p>14. Select the most appropriate synonym of the given word. <br>Labour</p>",
                    question_hi: "<p>14. Select the most appropriate synonym of the given word. <br>Labour</p>",
                    options_en: ["<p>Mesh</p>", "<p>Toil</p>", 
                                "<p>Dalliance</p>", "<p>Relaxation</p>"],
                    options_hi: ["<p>Mesh</p>", "<p>Toil</p>",
                                "<p>Dalliance</p>", "<p>Relaxation</p>"],
                    solution_en: "<p>14.(b) <strong>Toil-</strong> hard and continuous work.<br><strong>Labour-</strong> physical or mental effort.<br><strong>Mesh-</strong> material made of a network of wire or thread.<br><strong>Dalliance-</strong> a casual or non-serious involvement.<br><strong>Relaxation-</strong> a state of rest or ease.</p>",
                    solution_hi: "<p>14.(b) <strong>Toil</strong> (कठिन परिश्रम)- hard and continuous work.<br><strong>Labour</strong> (श्रम)- physical or mental effort.<br><strong>Mesh</strong> (जाल)- material made of a network of wire or thread.<br><strong>Dalliance</strong> (विलास)- a casual or non-serious involvement.<br><strong>Relaxation</strong> (विश्राम/आराम)- a state of rest or ease.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "7",
                    question_en: "<p>15. Select the most appropriate option that can substitute the underlined segment in the given sentence. <br>To the general public, William was best known as a crusader <span style=\"text-decoration: underline;\">for peace and as an admired critic about</span> social, political and ethical subjects.</p>",
                    question_hi: "<p>15. Select the most appropriate option that can substitute the underlined segment in the given sentence. <br>To the general public, William was best known as a crusader <span style=\"text-decoration: underline;\">for peace and as an admired critic about</span> social, political and ethical subjects.</p>",
                    options_en: ["<p>for peace and as an admired critic by</p>", "<p>for peace and as an admired critic on</p>", 
                                "<p>about peace and as an admired critic about</p>", "<p>in peace and as an admired critic about</p>"],
                    options_hi: ["<p>for peace and as an admired critic by</p>", "<p>for peace and as an admired critic on</p>",
                                "<p>about peace and as an admired critic about</p>", "<p>in peace and as an admired critic about</p>"],
                    solution_en: "<p>15.(b) for peace and as an admired critic on<br>&lsquo;On&rsquo; is generally used to indicate the topic or subject of discussion. Hence, &lsquo;on&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>15.(b) for peace and as an admired critic on<br>&lsquo;On&rsquo; का प्रयोग सामान्यतः discussion के topic या subject को indicate करने के लिए किया जाता है। अतः, &lsquo;on&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "16",
                    section: "7",
                    question_en: "<p>16. Select the most appropriate meaning of the underlined idiom. <br>In those days, we did not expect luxuries; we were thankful if we could <span style=\"text-decoration: underline;\">keep the wolf from the door</span>.</p>",
                    question_hi: "<p>16. Select the most appropriate meaning of the underlined idiom. <br>In those days, we did not expect luxuries; we were thankful if we could <span style=\"text-decoration: underline;\">keep the wolf from the door</span>.</p>",
                    options_en: ["<p>Avoid starvation</p>", "<p>Wallow in poverty</p>", 
                                "<p>Relish food</p>", "<p>Encourage starvation</p>"],
                    options_hi: ["<p>Avoid starvation</p>", "<p>Wallow in poverty</p>",
                                "<p>Relish food</p>", "<p>Encourage starvation</p>"],
                    solution_en: "<p>16.(a) <strong>Keep the wolf from the door-</strong> avoid starvation.</p>",
                    solution_hi: "<p>16.(a) <strong>Keep the wolf from the door-</strong> avoid starvation./भुखमरी से बचना।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "17",
                    section: "7",
                    question_en: "<p>17. Select the most appropriate ANTONYM of the given word. <br>Entire</p>",
                    question_hi: "<p>17. Select the most appropriate ANTONYM of the given word. <br>Entire</p>",
                    options_en: ["<p>Ingress</p>", "<p>Thorough</p>", 
                                "<p>Incomplete</p>", "<p>Undivided</p>"],
                    options_hi: ["<p>Ingress</p>", "<p>Thorough</p>",
                                "<p>Incomplete</p>", "<p>Undivided</p>"],
                    solution_en: "<p>17.(c) <strong>Incomplete-</strong> lacking some parts or elements.<br><strong>Entire-</strong> whole and complete.<br><strong>Ingress-</strong> the act of entering.<br><strong>Thorough-</strong> detailed and comprehensive.<br><strong>Undivided-</strong> not separated or broken into parts.</p>",
                    solution_hi: "<p>17.(c) <strong>Incomplete</strong> (अपूर्ण/अधूरा)- lacking some parts or elements.<br><strong>Entire</strong> (पूर्ण/सम्पूर्ण)- whole and complete.<br><strong>Ingress</strong> (प्रवेश)- the act of entering.<br><strong>Thorough</strong> (विस्तृत)- detailed and comprehensive.<br><strong>Undivided</strong> (अविभाजित)- not separated or broken into parts.</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "18",
                    section: "7",
                    question_en: "<p>18. Select the most appropriate option that can substitute the underlined segment in the given sentence. If no substitution is required, select &lsquo;No substitution&rsquo;. <br>No matter how <span style=\"text-decoration: underline;\">hard work you</span>, your boss will not recognise your efforts.</p>",
                    question_hi: "<p>18. Select the most appropriate option that can substitute the underlined segment in the given sentence. If no substitution is required, select &lsquo;No substitution&rsquo;. <br>No matter how <span style=\"text-decoration: underline;\">hard work you</span>, your boss will not recognise your efforts.</p>",
                    options_en: ["<p>hard you work</p>", "<p>work you do hard</p>", 
                                "<p>hardly you work</p>", "<p>no substitution</p>"],
                    options_hi: ["<p>hard you work</p>", "<p>work you do hard</p>",
                                "<p>hardly you work</p>", "<p>no substitution</p>"],
                    solution_en: "<p>18.(a) hard you work<br>&lsquo;Subject + main verb&rsquo; is the correct order for the sentence. &lsquo;You&rsquo; is the subject and &lsquo;work&rsquo; is its main verb. Hence, &lsquo;hard you work&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>18.(a) hard you work<br>Sentence का सही order &lsquo;subject + main verb&rsquo; है। &lsquo;You&rsquo; subject है और &lsquo;work&rsquo; इसकी main verb है। अतः, &lsquo;hard you work&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "19",
                    section: "7",
                    question_en: "<p>19. Select the most appropriate option that can substitute the underlined segment in the given sentence.<br>My cousin works <span style=\"text-decoration: underline;\">day and night</span> to achieve success in life.</p>",
                    question_hi: "<p>19. Select the most appropriate option that can substitute the underlined segment in the given sentence.<br>My cousin works <span style=\"text-decoration: underline;\">day and night</span> to achieve success in life.</p>",
                    options_en: ["<p>vale of tears</p>", "<p>around the clock</p>", 
                                "<p>great dealing</p>", "<p>a handful</p>"],
                    options_hi: ["<p>vale of tears</p>", "<p>around the clock</p>",
                                "<p>great dealing</p>", "<p>a handful</p>"],
                    solution_en: "<p>19.(b) Around the clock (idiom) - day and night.</p>",
                    solution_hi: "<p>19.(b) Around the clock (idiom) - day and night./दिन-रात।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "20",
                    section: "7",
                    question_en: "<p>20. Select the word OPPOSITE in meaning to the underlined word in the given sentence. <br>Feeling <span style=\"text-decoration: underline;\">discouraged,</span> she finally finds the motivation for pursuing her goal.</p>",
                    question_hi: "<p>20. Select the word OPPOSITE in meaning to the underlined word in the given sentence. <br>Feeling <span style=\"text-decoration: underline;\">discouraged,</span> she finally finds the motivation for pursuing her goal.</p>",
                    options_en: ["<p>inspired</p>", "<p>energetic</p>", 
                                "<p>admired</p>", "<p>disappointed</p>"],
                    options_hi: ["<p>inspired</p>", "<p>energetic</p>",
                                "<p>admired</p>", "<p>disappointed</p>"],
                    solution_en: "<p>20.(a) <strong>Inspired-</strong> filled with motivation or creativity.<br><strong>Discouraged-</strong> lacking confidence or hope.<br><strong>Energetic-</strong> full of energy and enthusiasm.<br><strong>Admired-</strong> regarded with respect or approval.<br><strong>Disappointed-</strong> saddened by unmet expectations.</p>",
                    solution_hi: "<p>20.(a) <strong>Inspired</strong> (प्रेरित)- filled with motivation or creativity.<br><strong>Discouraged</strong> (हतोत्साहित)- lacking confidence or hope.<br><strong>Energetic</strong> (ऊर्जावान)- full of energy and enthusiasm.<br><strong>Admired</strong> (प्रशंसित)- regarded with respect or approval.<br><strong>Disappointed</strong> (निराश)- saddened by unmet expectations.</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "21",
                    section: "7",
                    question_en: "<p>21. <strong>Cloze Test:</strong> <br>In the following passage, some words have been deleted. Read the passage carefully and select the most appropriate option to fill in each blank. <br>I have a recurring dream. Actually, I have a few: one is about dismembering a body , I&rsquo;d rather not get into it, but the (21) ________ pertinent one is about college. It&rsquo;s the end of the semester, and I suddenly (22) ________ that there is a class I forgot to attend, ever, and now I have to sit for the final exam. I wake up panicked, my GPA in peril. How could I have done this? Why do I so (23) ________ dissatisfy myself? Then I remember I haven&rsquo;t been in college in more than a decade. <br>Someone with (24) ________ knowledge of my academic career might point out that this nightmare scenario is not that far removed from my actual collegiate experience, and that at certain times in my life, it did not take the magic of slumber to find me completely unprepared for a final. And, well, (25) ________ of what may or may not be true of my personal scholastic rigour, I suspect the schoolstress dream is quite a common one. <br>Select the most appropriate option to fill in blank number 21.</p>",
                    question_hi: "<p>21. <strong>Cloze Test:</strong> <br>In the following passage, some words have been deleted. Read the passage carefully and select the most appropriate option to fill in each blank. <br>I have a recurring dream. Actually, I have a few: one is about dismembering a body , I&rsquo;d rather not get into it, but the (21) ________ pertinent one is about college. It&rsquo;s the end of the semester, and I suddenly (22) ________ that there is a class I forgot to attend, ever, and now I have to sit for the final exam. I wake up panicked, my GPA in peril. How could I have done this? Why do I so (23) ________ dissatisfy myself? Then I remember I haven&rsquo;t been in college in more than a decade. <br>Someone with (24) ________ knowledge of my academic career might point out that this nightmare scenario is not that far removed from my actual collegiate experience, and that at certain times in my life, it did not take the magic of slumber to find me completely unprepared for a final. And, well, (25) ________ of what may or may not be true of my personal scholastic rigour, I suspect the schoolstress dream is quite a common one. <br>Select the most appropriate option to fill in blank number 21.</p>",
                    options_en: ["<p>better</p>", "<p>best</p>", 
                                "<p>strong</p>", "<p>more</p>"],
                    options_hi: ["<p>better</p>", "<p>best</p>",
                                "<p>strong</p>", "<p>more</p>"],
                    solution_en: "<p>21.(d) more<br>&lsquo;More&rsquo; is a comparative adjective used to make a comparison between two things. Similarly, in the given passage, there is a comparison between two dreams. One dream is more pertinent than the other. Hence, &lsquo;more&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>21.(d) more<br>&lsquo;More&rsquo; एक comparative adjective है जिसका प्रयोग दो चीजों के बीच तुलना करने के लिए किया जाता है। इसी तरह, दिए गए passage में, दो dreams के बीच तुलना की गई है। एक dream दूसरे से अधिक प्रासंगिक (pertinent) है। अतः, &lsquo;more&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "22",
                    section: "7",
                    question_en: "<p>22. <strong>Cloze Test:</strong> <br>In the following passage, some words have been deleted. Read the passage carefully and select the most appropriate option to fill in each blank. <br>I have a recurring dream. Actually, I have a few: one is about dismembering a body , I&rsquo;d rather not get into it, but the (21) ________ pertinent one is about college. It&rsquo;s the end of the semester, and I suddenly (22) ________ that there is a class I forgot to attend, ever, and now I have to sit for the final exam. I wake up panicked, my GPA in peril. How could I have done this? Why do I so (23) ________ dissatisfy myself? Then I remember I haven&rsquo;t been in college in more than a decade. <br>Someone with (24) ________ knowledge of my academic career might point out that this nightmare scenario is not that far removed from my actual collegiate experience, and that at certain times in my life, it did not take the magic of slumber to find me completely unprepared for a final. And, well, (25) ________ of what may or may not be true of my personal scholastic rigour, I suspect the schoolstress dream is quite a common one. <br>Select the most appropriate option to fill in blank number 22.</p>",
                    question_hi: "<p>22. <strong>Cloze Test:</strong> <br>In the following passage, some words have been deleted. Read the passage carefully and select the most appropriate option to fill in each blank. <br>I have a recurring dream. Actually, I have a few: one is about dismembering a body , I&rsquo;d rather not get into it, but the (21) ________ pertinent one is about college. It&rsquo;s the end of the semester, and I suddenly (22) ________ that there is a class I forgot to attend, ever, and now I have to sit for the final exam. I wake up panicked, my GPA in peril. How could I have done this? Why do I so (23) ________ dissatisfy myself? Then I remember I haven&rsquo;t been in college in more than a decade. <br>Someone with (24) ________ knowledge of my academic career might point out that this nightmare scenario is not that far removed from my actual collegiate experience, and that at certain times in my life, it did not take the magic of slumber to find me completely unprepared for a final. And, well, (25) ________ of what may or may not be true of my personal scholastic rigour, I suspect the schoolstress dream is quite a common one. <br>Select the most appropriate option to fill in blank number 22.</p>",
                    options_en: ["<p>realise</p>", "<p>praise</p>", 
                                "<p>offer</p>", "<p>promise</p>"],
                    options_hi: ["<p>realise</p>", "<p>praise</p>",
                                "<p>offer</p>", "<p>promise</p>"],
                    solution_en: "<p>22.(a) realise<br>&lsquo;Realise&rsquo; means to become fully aware of something. The given passage states that I suddenly realise that there is a class I forgot to attend. Hence, &lsquo;realise&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>22.(a) realise<br>&lsquo;Realise&rsquo; का अर्थ है किसी के बारे में पूरी तरह से जागरूक होना। दिए गए passage में बताया गया है कि मुझे अचानक एहसास होता है कि एक class है जिसमें मैं जाना भूल गया था। अतः, &lsquo;realise&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "23",
                    section: "7",
                    question_en: "<p>23. <strong>Cloze Test:</strong> <br>In the following passage, some words have been deleted. Read the passage carefully and select the most appropriate option to fill in each blank. <br>I have a recurring dream. Actually, I have a few: one is about dismembering a body , I&rsquo;d rather not get into it, but the (21) ________ pertinent one is about college. It&rsquo;s the end of the semester, and I suddenly (22) ________ that there is a class I forgot to attend, ever, and now I have to sit for the final exam. I wake up panicked, my GPA in peril. How could I have done this? Why do I so (23) ________ dissatisfy myself? Then I remember I haven&rsquo;t been in college in more than a decade. <br>Someone with (24) ________ knowledge of my academic career might point out that this nightmare scenario is not that far removed from my actual collegiate experience, and that at certain times in my life, it did not take the magic of slumber to find me completely unprepared for a final. And, well, (25) ________ of what may or may not be true of my personal scholastic rigour, I suspect the schoolstress dream is quite a common one. <br>Select the most appropriate option to fill in blank number 23.</p>",
                    question_hi: "<p>23.<strong> Cloze Test:</strong> <br>In the following passage, some words have been deleted. Read the passage carefully and select the most appropriate option to fill in each blank. <br>I have a recurring dream. Actually, I have a few: one is about dismembering a body , I&rsquo;d rather not get into it, but the (21) ________ pertinent one is about college. It&rsquo;s the end of the semester, and I suddenly (22) ________ that there is a class I forgot to attend, ever, and now I have to sit for the final exam. I wake up panicked, my GPA in peril. How could I have done this? Why do I so (23) ________ dissatisfy myself? Then I remember I haven&rsquo;t been in college in more than a decade. <br>Someone with (24) ________ knowledge of my academic career might point out that this nightmare scenario is not that far removed from my actual collegiate experience, and that at certain times in my life, it did not take the magic of slumber to find me completely unprepared for a final. And, well, (25) ________ of what may or may not be true of my personal scholastic rigour, I suspect the schoolstress dream is quite a common one. <br>Select the most appropriate option to fill in blank number 23.</p>",
                    options_en: ["<p>unpredictability</p>", "<p>consistently</p>", 
                                "<p>conflictingly</p>", "<p>clumsily</p>"],
                    options_hi: ["<p>unpredictability</p>", "<p>consistently</p>",
                                "<p>conflictingly</p>", "<p>clumsily</p>"],
                    solution_en: "<p>23.(b) consistently<br>&lsquo;Consistently&rsquo; means continuously in the same manner. The given passage states a question that the person is asking why he so consistently dissatisfies himself. Hence, &lsquo;consistently&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>23.(b) consistently<br>&lsquo;Consistently&rsquo; का अर्थ है लगातार एक ही तरीके से। दिए गए passage में एक प्रश्न पूछा गया है कि वह लगातार खुद को असंतुष्ट क्यों रखता है। अतः, &lsquo;consistently&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "24",
                    section: "7",
                    question_en: "<p>24. <strong>Cloze Test:</strong> <br>In the following passage, some words have been deleted. Read the passage carefully and select the most appropriate option to fill in each blank. <br>I have a recurring dream. Actually, I have a few: one is about dismembering a body , I&rsquo;d rather not get into it, but the (21) ________ pertinent one is about college. It&rsquo;s the end of the semester, and I suddenly (22) ________ that there is a class I forgot to attend, ever, and now I have to sit for the final exam. I wake up panicked, my GPA in peril. How could I have done this? Why do I so (23) ________ dissatisfy myself? Then I remember I haven&rsquo;t been in college in more than a decade. <br>Someone with (24) ________ knowledge of my academic career might point out that this nightmare scenario is not that far removed from my actual collegiate experience, and that at certain times in my life, it did not take the magic of slumber to find me completely unprepared for a final. And, well, (25) ________ of what may or may not be true of my personal scholastic rigour, I suspect the schoolstress dream is quite a common one. <br>Select the most appropriate option to fill in blank number 24.</p>",
                    question_hi: "<p>24. <strong>Cloze Test:</strong> <br>In the following passage, some words have been deleted. Read the passage carefully and select the most appropriate option to fill in each blank. <br>I have a recurring dream. Actually, I have a few: one is about dismembering a body , I&rsquo;d rather not get into it, but the (21) ________ pertinent one is about college. It&rsquo;s the end of the semester, and I suddenly (22) ________ that there is a class I forgot to attend, ever, and now I have to sit for the final exam. I wake up panicked, my GPA in peril. How could I have done this? Why do I so (23) ________ dissatisfy myself? Then I remember I haven&rsquo;t been in college in more than a decade. <br>Someone with (24) ________ knowledge of my academic career might point out that this nightmare scenario is not that far removed from my actual collegiate experience, and that at certain times in my life, it did not take the magic of slumber to find me completely unprepared for a final. And, well, (25) ________ of what may or may not be true of my personal scholastic rigour, I suspect the schoolstress dream is quite a common one. <br>Select the most appropriate option to fill in blank number 24.</p>",
                    options_en: ["<p>instigate</p>", "<p>distant</p>", 
                                "<p>remote</p>", "<p>bleak</p>"],
                    options_hi: ["<p>instigate</p>", "<p>distant</p>",
                                "<p>remote</p>", "<p>bleak</p>"],
                    solution_en: "<p>24.(c) remote<br>&lsquo;Remote&rsquo; means slight. The given passage states that someone with slight knowledge of my academic career might point out that this nightmare scenario is not far removed from my actual collegiate experience. Hence, &lsquo;remote&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>24.(c) remote<br>&lsquo;Remote&rsquo; का अर्थ है मामूली या थोड़ी सी। दिए गए passage में कहा गया है कि मेरे academic career के बारे में थोड़ा-बहुत जानने वाला कोई व्यक्ति यह बता सकता है कि यह दुःस्वप्न परिदृश्य (nightmare scenario) मेरे वास्तविक college ke अनुभव से बहुत दूर नहीं है। अतः, &lsquo;remote&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "25",
                    section: "misc",
                    question_en: "<p>25.<strong> Cloze Test:</strong> <br>In the following passage, some words have been deleted. Read the passage carefully and select the most appropriate option to fill in each blank. <br>I have a recurring dream. Actually, I have a few: one is about dismembering a body , I&rsquo;d rather not get into it, but the (21) ________ pertinent one is about college. It&rsquo;s the end of the semester, and I suddenly (22) ________ that there is a class I forgot to attend, ever, and now I have to sit for the final exam. I wake up panicked, my GPA in peril. How could I have done this? Why do I so (23) ________ dissatisfy myself? Then I remember I haven&rsquo;t been in college in more than a decade. <br>Someone with (24) ________ knowledge of my academic career might point out that this nightmare scenario is not that far removed from my actual collegiate experience, and that at certain times in my life, it did not take the magic of slumber to find me completely unprepared for a final. And, well, (25) ________ of what may or may not be true of my personal scholastic rigour, I suspect the schoolstress dream is quite a common one. <br>Select the most appropriate option to fill in blank number 25.</p>",
                    question_hi: "<p>25.<strong> Cloze Test:</strong> <br>In the following passage, some words have been deleted. Read the passage carefully and select the most appropriate option to fill in each blank. <br>I have a recurring dream. Actually, I have a few: one is about dismembering a body , I&rsquo;d rather not get into it, but the (21) ________ pertinent one is about college. It&rsquo;s the end of the semester, and I suddenly (22) ________ that there is a class I forgot to attend, ever, and now I have to sit for the final exam. I wake up panicked, my GPA in peril. How could I have done this? Why do I so (23) ________ dissatisfy myself? Then I remember I haven&rsquo;t been in college in more than a decade. <br>Someone with (24) ________ knowledge of my academic career might point out that this nightmare scenario is not that far removed from my actual collegiate experience, and that at certain times in my life, it did not take the magic of slumber to find me completely unprepared for a final. And, well, (25) ________ of what may or may not be true of my personal scholastic rigour, I suspect the schoolstress dream is quite a common one. <br>Select the most appropriate option to fill in blank number 25.</p>",
                    options_en: ["<p>nevertheless</p>", "<p>notwithstanding</p>", 
                                "<p>regardless</p>", "<p>inasmuch</p>"],
                    options_hi: ["<p>nevertheless</p>", "<p>notwithstanding</p>",
                                "<p>regardless</p>", "<p>inasmuch</p>"],
                    solution_en: "<p>25.(c) regardless<br>&lsquo;Regardless&rsquo; means without regard or consideration for. The given passage states that regardless of what may or may not be true of my personal scholastic rigour, I suspect the schoolstress dream is quite a common one. Hence, &lsquo;regardless&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>25.(c) regardless<br>&lsquo;Regardless&rsquo; का अर्थ है किसी चीज की परवाह किये बिना। दिए गए passage में कहा गया है कि इसकी परवाह किये बिना कि मेरी व्यक्तिगत शैक्षिक कठिनाई(rigour) के बारे में कुछ भी सच हो या न हो, मुझे शक है कि स्कूली तनाव का सपना (schoolstress dream) काफी common है। अतः, &lsquo;regardless\' सबसे उपयुक्त उत्तर है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>