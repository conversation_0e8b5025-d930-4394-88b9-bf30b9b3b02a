<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">15:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 15</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">15</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 15 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 0,
                end: 13
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 14,
                end: 14
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "17",
                    question_en: "1. A man buys 15 mangoes for a rupee. How many mangoes had he sold for a rupee so that there is a loss of 25 percent?",
                    question_hi: "1. एक आदमी एक रुपए में 15 आम खरीदता है। एक रुपए में कितने आम बेचने पर उसे 25 प्रतिशत की हानि होगी?",
                    options_en: [" 17", " 20", 
                                " 38", " 28"],
                    options_hi: [" 17", " 20",
                                " 38", " 28"],
                    solution_en: "1.(b)<br />CP of 15 mangoes = 1 Rs<br />SP of 15 mangoes = 0.75 Rs<br />Quantity of Mangoes sell in 1 Rs = <math display=\"inline\"><mfrac><mrow><mn>15</mn></mrow><mrow><mn>0</mn><mo>.</mo><mn>75</mn></mrow></mfrac></math> × 1 = 20 ",
                    solution_hi: "1.(b)<br />15 आमों का क्रय मूल्य = 1 Rs<br />15 आमों का विक्रय मूल्य = 0.75 Rs<br />1 Rs में बेचे गये आमों की संख्या = <math display=\"inline\"><mfrac><mrow><mn>15</mn></mrow><mrow><mn>0</mn><mo>.</mo><mn>75</mn></mrow></mfrac></math> × 1 = 20 ",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "2",
                    section: "17",
                    question_en: "<p>2. On Selling a watch at <math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>10</mn></mrow></mfrac></math> of the marked price there is a loss of 16 percent. What will be the ratio of the marked price and cost price of the watch?</p>",
                    question_hi: "<p>2. अंकित मूल्य के <math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>10</mn></mrow></mfrac></math> पर एक घड़ी को बेचने पर 16 प्रतिशत की हानि होती है। घड़ी के अंकित मूल्य और क्रय मूल्य का अनुपात कितना होगा?</p>",
                    options_en: ["<p>7:4</p>", "<p>8:5</p>", 
                                "<p>14:5</p>", "<p>19:8</p>"],
                    options_hi: ["<p>7:4</p>", "<p>8:5</p>",
                                "<p>14:5</p>", "<p>19:8</p>"],
                    solution_en: "<p>2.(c)<br>Let the MP of the watch = 10<math display=\"inline\"><mi>x</mi></math><br>Then SP will = 10<math display=\"inline\"><mi>x</mi></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>10</mn></mfrac></math>= 3x<br>CP = <math display=\"inline\"><mfrac><mrow><mn>3</mn><mi>x</mi></mrow><mrow><mn>21</mn></mrow></mfrac></math> &times; 25 =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>25</mn><mi>x</mi></mrow><mrow><mo>&#160;</mo><mn>7</mn><mo>&#160;</mo></mrow></mfrac></math><br>Required ratio = 10<math display=\"inline\"><mi>x</mi></math> : <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>25</mn><mi>x</mi></mrow><mrow><mo>&#160;</mo><mn>7</mn><mo>&#160;</mo></mrow></mfrac></math>= 14 : 5</p>",
                    solution_hi: "<p>2.(c)<br>माना घड़ी का अंकित मूल्य = 10<math display=\"inline\"><mi>x</mi></math><br>तब विक्रय मूल्य = 10<math display=\"inline\"><mi>x</mi></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>10</mn></mfrac></math>= 3x होगा<br>क्रय मूल्य = <math display=\"inline\"><mfrac><mrow><mn>3</mn><mi>x</mi></mrow><mrow><mn>21</mn></mrow></mfrac></math> &times; 25 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>25</mn><mi>x</mi></mrow><mrow><mo>&#160;</mo><mn>7</mn><mo>&#160;</mo></mrow></mfrac></math><br>आवश्यक अनुपात = 10<math display=\"inline\"><mi>x</mi></math> : <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>25</mn><mi>x</mi></mrow><mrow><mo>&#160;</mo><mn>7</mn><mo>&#160;</mo></mrow></mfrac></math>= 14 : 5</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "3",
                    section: "17",
                    question_en: "3. A shirt was sold for $40, incurring a loss of 20%. Find the cost price.",
                    question_hi: "3. एक शर्ट $40 में 20% की हानि पर बेची गई। शर्ट का क्रय मूल्य ज्ञात कीजिए।",
                    options_en: [" $55", " $50 ", 
                                " $35 ", " $30"],
                    options_hi: [" $55", " $50 ",
                                " $35 ", " $30"],
                    solution_en: "3.(b) cost price = <math display=\"inline\"><mfrac><mrow><mn>40</mn><mi>&nbsp;</mi><mo>×</mo><mi>&nbsp;</mi><mn>100</mn></mrow><mrow><mn>80</mn></mrow></mfrac></math> = $50 ",
                    solution_hi: "3.(b) क्रय मूल्य  = <math display=\"inline\"><mfrac><mrow><mn>40</mn><mi>&nbsp;</mi><mo>×</mo><mi>&nbsp;</mi><mn>100</mn></mrow><mrow><mn>80</mn></mrow></mfrac></math> = $50 ",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "4",
                    section: "17",
                    question_en: "4. A person loses 35 percent on selling a book for Rs. 2860. At what price should he sell the book to earn a profit of 20 percent?",
                    question_hi: "4. एक व्यक्ति को 2860 रुपए में एक पुस्तक बेचने पर 35 प्रतिशत की हानि होती है। 20 प्रतिशत का लाभ अर्जित करने के लिए उसे पुस्तक को किस मूल्य पर बेचना चाहिए?",
                    options_en: [" Rs. 5460", " Rs. 6460", 
                                " Rs. 5280", " Rs. 5540"],
                    options_hi: [" 5460 रुपए", " 6460 रुपए",
                                " 5280 रुपए", " 5540 रुपए"],
                    solution_en: "4.(c) cost price of book = <math display=\"inline\"><mfrac><mrow><mn>100</mn><mi>&nbsp;</mi><mo>×</mo><mi>&nbsp;</mi><mn>2860</mn></mrow><mrow><mn>65</mn></mrow></mfrac></math> = 4400<br />Selling price of book to earn 20 % profit = <math display=\"inline\"><mfrac><mrow><mn>4400</mn><mi>&nbsp;</mi><mo>×</mo><mi>&nbsp;</mi><mn>120</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = Rs. 5280",
                    solution_hi: "4.(c) पुस्तक का क्रय मूल्य  = <math display=\"inline\"><mfrac><mrow><mn>100</mn><mi>&nbsp;</mi><mo>×</mo><mi>&nbsp;</mi><mn>2860</mn></mrow><mrow><mn>65</mn></mrow></mfrac></math> = 4400<br />20% लाभ अर्जित करने के लिए पुस्तक का विक्रय मूल्य = <math display=\"inline\"><mfrac><mrow><mn>4400</mn><mi>&nbsp;</mi><mo>×</mo><mi>&nbsp;</mi><mn>120</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = Rs. 5280",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "5",
                    section: "17",
                    question_en: "5. If the selling price and cost price of a shirt are Rs 1050 and Rs 900 respectively, then what is the profit percentage?",
                    question_hi: "5. यदि एक शर्ट का विक्रय मूल्य और क्रय मूल्य क्रमशः 1050 रुपए और 900 रुपए है, तो लाभ प्रतिशत क्या है?",
                    options_en: [" 25 percent", " 20 percent", 
                                " 16.66 percent", " 18.5 percent"],
                    options_hi: [" 25 प्रतिशत ", " 20 प्रतिशत ",
                                " 16.66 प्रतिशत ", " 18.5 प्रतिशत"],
                    solution_en: "5.(c) <br />Profit % = <math display=\"inline\"><mfrac><mrow><mn>1050</mn><mi>&nbsp;</mi><mo>-</mo><mi>&nbsp;</mi><mn>900</mn></mrow><mrow><mn>900</mn></mrow></mfrac><mo>×</mo><mn>100</mn></math> = 16.66%",
                    solution_hi: "5.(c)  <br />लाभ % = <math display=\"inline\"><mfrac><mrow><mn>1050</mn><mi>&nbsp;</mi><mo>-</mo><mi>&nbsp;</mi><mn>900</mn></mrow><mrow><mn>900</mn></mrow></mfrac><mo>×</mo><mn>100</mn></math> = 16.66%",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "6",
                    section: "17",
                    question_en: "<p>6. If 80 percent of the total goods are sold at a profit of 20 percent and the remaining goods are sold at a loss of 30 percent, then what will be the total profit percentage?</p>",
                    question_hi: "<p>6. यदि कुल वस्तुओं का 80 प्रतिशत, 20 प्रतिशत के लाभ पर बेचा जाता है और शेष वस्तुओं को 30 प्रतिशत की हानि पर बेचा जाता है, तो कुल लाभ प्रतिशत कितना होगा?</p>",
                    options_en: ["<p>14 percent</p>", "<p>10 percent</p>", 
                                "<p>12 percent</p>", "<p>15 percent</p>"],
                    options_hi: ["<p>14 प्रतिशत</p>", "<p>10 प्रतिशत</p>",
                                "<p>12 प्रतिशत</p>", "<p>15 प्रतिशत</p>"],
                    solution_en: "<p>6.(b)<br>Let the price of each of 100 goods be Rs.1.<br>Then total cost price = 100 Rs.<br>According to question,<br>Total selling price =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfenced><mfrac><mrow><mn>80</mn><mo>&#215;</mo><mn>120</mn></mrow><mn>100</mn></mfrac></mfenced><mo>+</mo><mfenced><mfrac><mrow><mn>20</mn><mo>&#215;</mo><mn>70</mn></mrow><mn>100</mn></mfrac></mfenced></math>&nbsp;= (96) + (14) = 110 Rs.<br>Then, Profit % = <math display=\"inline\"><mfrac><mrow><mn>110</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>100</mn></mrow><mrow><mn>100</mn></mrow></mfrac><mo>&#215;</mo><mn>100</mn></math> = 10%</p>",
                    solution_hi: "<p>6.(b)<br>माना कि 100 वस्तुओं में से प्रत्येक की कीमत 1 रुपये है।<br>तो कुल लागत मूल्य = 100 रु.<br>प्रश्न के अनुसार,<br>कुल विक्रय मूल्य = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfenced><mfrac><mrow><mn>80</mn><mo>&#215;</mo><mn>120</mn></mrow><mn>100</mn></mfrac></mfenced><mo>+</mo><mfenced><mfrac><mrow><mn>20</mn><mo>&#215;</mo><mn>70</mn></mrow><mn>100</mn></mfrac></mfenced></math>&nbsp;= (96) + (14) = 110 रु.<br>फिर, लाभ % = <math display=\"inline\"><mfrac><mrow><mn>110</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>100</mn></mrow><mrow><mn>100</mn></mrow></mfrac><mo>&#215;</mo><mn>100</mn></math> = 10%</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "7",
                    section: "17",
                    question_en: "<p>7. If 35% of the marked price of an article is equal to 45% of the cost price and if no discount is given on that article then find the profit percentage.</p>",
                    question_hi: "<p>7. यदि किसी वस्तु के अंकित मूल्य का 35%, क्रय मूल्य के 45% के बराबर है और यदि उस वस्तु पर कोई छूट नहीं दी जाती है तो लाभ प्रतिशत ज्ञात कीजिए।</p>",
                    options_en: ["<p>27.56%</p>", "<p>33.5%</p>", 
                                "<p>22.5%</p>", "<p>28.57%</p>"],
                    options_hi: ["<p>27.56%</p>", "<p>33.5%</p>",
                                "<p>22.5%</p>", "<p>28.57%</p>"],
                    solution_en: "<p>7.(d) According to question,<br><math display=\"inline\"><mo>&#8658;</mo></math> M.P &times; 35% = CP &times; 45%<br><math display=\"inline\"><mo>&#8658;</mo></math><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>M</mi><mi>P</mi></mrow><mrow><mi>C</mi><mi>P</mi></mrow></mfrac></math>&nbsp; = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>45</mn><mn>35</mn></mfrac></math>&nbsp;= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>9</mn><mn>7</mn></mfrac></math><br>Profit percentage = <math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> &times; 100<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; = 28.57%</p>",
                    solution_hi: "<p>7.(d) प्रश्न के अनुसार,<br>अंकित मूल्य &times; 35% = क्रय मूल्य &times; 45%<br><math display=\"inline\"><mo>&#8658;</mo></math><math style=\"font-family:Verdana\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2309;&#2306;&#2325;&#2367;&#2340;</mi><mo>&#160;</mo><mo>&#160;</mo><mi>&#2350;&#2370;&#2354;&#2381;&#2351;</mi></mrow><mrow><mo>&#160;</mo><mi>&#2325;&#2381;&#2352;&#2351;</mi><mo>&#160;</mo><mo>&#160;</mo><mi>&#2350;&#2370;&#2354;&#2381;&#2351;</mi></mrow></mfrac></math>&nbsp;= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>45</mn><mn>35</mn></mfrac></math>&nbsp;= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>9</mn><mn>7</mn></mfrac></math><br>लाभ प्रतिशत = <math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> &times; 100<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;= 28.57%</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "8",
                    section: "17",
                    question_en: "<p>8. If 60 tables are purchased for Rs 14800, how many tables should be sold for Rs 14800 to get a profit of 20 percent?</p>",
                    question_hi: "<p>8. यदि 60 मेज 14800 रुपए में खरीदी जाती है, तो 20 प्रतिशत का लाभ प्राप्त करने के लिए 14800 रुपए में कितनी मेज बेची जानी चाहिए?</p>",
                    options_en: ["<p>40</p>", "<p>54</p>", 
                                "<p>50</p>", "<p>48</p>"],
                    options_hi: ["<p>40</p>", "<p>54</p>",
                                "<p>50</p>", "<p>48</p>"],
                    solution_en: "<p>8.(c) Cost price of 60 table = 14800<br>Cost price of 1 table = <math display=\"inline\"><mfrac><mrow><mn>14800</mn></mrow><mrow><mn>60</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>740</mn><mn>3</mn></mfrac></math><br>According to question,<br>Selling price of 1 table = <math display=\"inline\"><mfrac><mrow><mn>740</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>6</mn><mn>5</mn></mfrac></math>= 296<br>Now,<br>Number of table = <math display=\"inline\"><mfrac><mrow><mn>14800</mn></mrow><mrow><mn>296</mn></mrow></mfrac></math> = 50</p>",
                    solution_hi: "<p>8.(c) 60 मेज का क्रय मूल्य = 14800<br>1 मेज का क्रय मूल्य = <math display=\"inline\"><mfrac><mrow><mn>14800</mn></mrow><mrow><mn>60</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>740</mn><mn>3</mn></mfrac></math><br>प्रश्न के अनुसार,<br>1 मेज का विक्रय मूल्य = <math display=\"inline\"><mfrac><mrow><mn>740</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>6</mn><mn>5</mn></mfrac></math> = 296<br>अब,<br>मेजो की संख्या = <math display=\"inline\"><mfrac><mrow><mn>14800</mn></mrow><mrow><mn>296</mn></mrow></mfrac></math> = 50</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "9",
                    section: "17",
                    question_en: "9. If the selling price and cost price of a shirt are Rs 1430 and Rs 1000 respectively, then what is the profit percentage?",
                    question_hi: "9. यदि एक शर्ट का विक्रय मूल्य और क्रय मूल्य क्रमशः 1430 रुपए और 1000 रुपए है, तो लाभ प्रतिशत कितना है?",
                    options_en: [" 43 percent", " 37 percent", 
                                " 44 percent", " 40 percent"],
                    options_hi: [" 43 प्रतिशत", " 37 प्रतिशत",
                                " 44 प्रतिशत", " 40 प्रतिशत"],
                    solution_en: "9.(a) SP = 1430 and CP = 1000<br />Profit percentage = <math display=\"inline\"><mfrac><mrow><mo>(</mo><mn>1430</mn><mi>&nbsp;</mi><mo>-</mo><mi>&nbsp;</mi><mn>1000</mn><mo>)</mo></mrow><mrow><mn>1000</mn></mrow></mfrac></math> × 100 = 43%",
                    solution_hi: "9.(a) विक्रय मूल्य = 1430 और  क्रय मूल्य = 1000<br /> लाभ प्रतिशत = <math display=\"inline\"><mfrac><mrow><mo>(</mo><mn>1430</mn><mi>&nbsp;</mi><mo>-</mo><mi>&nbsp;</mi><mn>1000</mn><mo>)</mo></mrow><mrow><mn>1000</mn></mrow></mfrac></math> × 100 = 43%",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "10",
                    section: "17",
                    question_en: "<p>10. A shirt is bought for $30 and sold for $40. Find the profit percentage</p>",
                    question_hi: "<p>10. एक शर्ट $30 में खरीदी जाती है और $40 में बेची जाती है। लाभ प्रतिशत ज्ञात कीजिए।</p>",
                    options_en: ["<p>25%</p>", "<p>11.11%</p>", 
                                "<p>30%</p>", "<p>33.33%</p>"],
                    options_hi: ["<p>25%</p>", "<p>11.11%</p>",
                                "<p>30%</p>", "<p>33.33%</p>"],
                    solution_en: "<p>10.(d)<br>According to question,<br>Profit % = <math display=\"inline\"><mfrac><mrow><mn>40</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>30</mn></mrow><mrow><mn>30</mn></mrow></mfrac><mo>&#215;</mo><mn>100</mn></math> = 33.33%</p>",
                    solution_hi: "<p>10.(d)<br>प्रश्न के अनुसार,<br>लाभ % = <math display=\"inline\"><mfrac><mrow><mn>40</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>30</mn></mrow><mrow><mn>30</mn></mrow></mfrac><mo>&#215;</mo><mn>100</mn></math> = 33.33%</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "11",
                    section: "17",
                    question_en: "11.  40 percent of the cost price of an article is equal to the 30 percent of its selling price. What is the profit percentage?",
                    question_hi: "11. किसी वस्तु के क्रय मूल्य का 40 प्रतिशत उसके विक्रय मूल्य के 30 प्रतिशत के बराबर है। वस्तु पर लाभ प्रतिशत कितना है?",
                    options_en: ["  30 percent", "  33.33 percent", 
                                "  50 percent", "  66.66 percent"],
                    options_hi: ["  30 प्रतिशत", "  33.33 प्रतिशत",
                                "  50 प्रतिशत", "  66.66 प्रतिशत"],
                    solution_en: "11.(b)<br />CP × 40% = SP × 30%<br />CP : SP = 3 : 4<br />Hence, profit % = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> × 100 = 33.33 %",
                    solution_hi: "11.(b)<br />क्रय मूल्य × 40% = विक्रय मूल्य × 30%<br />क्रय मूल्य : विक्रय मूल्य = 3 : 4<br />अतः , लाभ % = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> × 100 = 33.33 %",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "12",
                    section: "17",
                    question_en: "12. An item costing ₹ 500 is being sold at a 17% loss. If the price is further reduced by 20%, then find the selling price.",
                    question_hi: "12. एक वस्तु, जिसका क्रय मूल्य ₹ 500 है, 17% की हानि पर बेची जाती है। यदि उस वस्तु के मूल्य में 20% की कमी और की जाती है, तो विक्रय मूल्य ज्ञात कीजिए।",
                    options_en: [" ₹332 ", " ₹290 ", 
                                " ₹452 ", " ₹302"],
                    options_hi: [" ₹332 ", " ₹290 ",
                                " ₹452 ", " ₹302"],
                    solution_en: "12.(a)<br />According to question,<br />Effective % = - 17 - 20 + <math display=\"inline\"><mfrac><mrow><mn>17</mn><mi>&nbsp;</mi><mo>×</mo><mi>&nbsp;</mi><mn>20</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = - 33.6%<br />selling price of the item = 500 × <math display=\"inline\"><mfrac><mrow><mn>66</mn><mo>.</mo><mn>4</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 332 Rs.",
                    solution_hi: "12.(a)<br />प्रश्न के अनुसार,<br />प्रभावी % =  - 17 - 20 + <math display=\"inline\"><mfrac><mrow><mn>17</mn><mi>&nbsp;</mi><mo>×</mo><mi>&nbsp;</mi><mn>20</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = - 33.6%<br />वस्तु का विक्रय मूल्य = 500 × <math display=\"inline\"><mfrac><mrow><mn>66</mn><mo>.</mo><mn>4</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 332 रु.",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "13",
                    section: "17",
                    question_en: "13. A sells a bottle to B at the profit of 30 percent and B sells it to C at a loss of 20 percent. If C pays Rs. 3744 for the bottle, then what is the cost price of bottle for A ?",
                    question_hi: "13. A, B को 30 प्रतिशत के लाभ पर एक बोतल बेचता है और B उसे C को 20 प्रतिशत की हानि पर बेचता है। यदि C बोतल के लिए 3744 रुपए का भुगतान करता है, तो A के लिए बोतल का क्रय मूल्य कितना है ?",
                    options_en: [" Rs. 3600", " Rs. 3000", 
                                " Rs. 3200", " Rs. 3500"],
                    options_hi: [" 3600 रुपए ", " 3000 रुपए",
                                " 3200 रुपए ", " 3500 रुपए"],
                    solution_en: "13.(a)<br />According to question,<br />Effective % = 30 - 20 - <math display=\"inline\"><mfrac><mrow><mn>30</mn><mi>&nbsp;</mi><mo>×</mo><mi>&nbsp;</mi><mn>20</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 4%<br />Required price of bottle = 3744 × <math display=\"inline\"><mfrac><mrow><mn>100</mn></mrow><mrow><mn>104</mn></mrow></mfrac></math> = 3600 Rs.",
                    solution_hi: "13.(a)<br />प्रश्न के अनुसार,<br />प्रभावी % = 30 - 20 - <math display=\"inline\"><mfrac><mrow><mn>30</mn><mi>&nbsp;</mi><mo>×</mo><mi>&nbsp;</mi><mn>20</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 4%<br />बोतल की आवश्यक कीमत = 3744 × <math display=\"inline\"><mfrac><mrow><mn>100</mn></mrow><mrow><mn>104</mn></mrow></mfrac></math> = 3600 रुपए",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "14",
                    section: "17",
                    question_en: "<p>14. Vikas sells 2 tables at the rate of Rs. 504 per table. He earns 40 percent profit on first table and suffers a loss of 40 percent on second table. What is the total cost price of both the tables ?</p>",
                    question_hi: "<p>14. विकास 504 रुपए प्रति मेज की दर से 2 मेज बेचता है। वह पहली मेज पर 40 प्रतिशत का लाभ अर्जित करता है और दूसरी मेज पर 40 प्रतिशत की हानि उठाता है। दोनों मेज का कुल क्रय मूल्य कितना है ?</p>",
                    options_en: ["<p>Rs. 1200</p>", "<p>Rs. 1400</p>", 
                                "<p>Rs. 1000</p>", "<p>Rs. 1500</p>"],
                    options_hi: ["<p>1200 रुपए</p>", "<p>1400 रुपए</p>",
                                "<p>1000 रुपए</p>", "<p>1500 रुपए</p>"],
                    solution_en: "<p>14.(a) Total loss = <math display=\"inline\"><mfrac><mrow><msup><mrow><mi>x</mi></mrow><mrow><mn>2</mn></mrow></msup></mrow><mrow><mn>100</mn></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msup><mn>40</mn><mn>2</mn></msup><mn>100</mn></mfrac></math> = 16%<br>Let the cost price of both table = 100% <br>according to question, <br>84% = 1008 <br>1 % = 12 <br>100% = 12 <math display=\"inline\"><mo>&#215;</mo></math> 100 = Rs.1200</p>",
                    solution_hi: "<p>14.(a) कुल हानि = <math display=\"inline\"><mfrac><mrow><msup><mrow><mi>x</mi></mrow><mrow><mn>2</mn></mrow></msup></mrow><mrow><mn>100</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msup><mn>40</mn><mn>2</mn></msup><mn>100</mn></mfrac></math>= 16%<br>माना दोनों टेबल का क्रय मूल्य = 100% <br>प्रश्न के अनुसार, <br>84% = 1008 <br>1 % = 12 <br>100% = 12 <math display=\"inline\"><mo>&#215;</mo></math> 100 = 1200 रूपये</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "15",
                    section: "misc",
                    question_en: "<p>15. If the selling price of a bottle is Rs. 408 and loss percentage is 32 percent, then what is the cost price of the bottle ?</p>",
                    question_hi: "<p>15. यदि एक बोतल का विक्रय मूल्य 408 रुपए है और हानि प्रतिशत 32 प्रतिशत है, तो बोतल का क्रय मूल्य कितना है ?</p>",
                    options_en: ["<p>Rs. 650</p>", "<p>Rs. 700</p>", 
                                "<p>Rs. 720</p>", "<p>Rs. 600</p>"],
                    options_hi: ["<p>650 रुपए</p>", "<p>700 रुपए</p>",
                                "<p>720 रुपए</p>", "<p>600 रुपए</p>"],
                    solution_en: "<p>15.(d) cost price of bottle = <math display=\"inline\"><mfrac><mrow><mn>100</mn><mi>&#160;</mi><mo>&#215;</mo><mn>408</mn></mrow><mrow><mn>68</mn></mrow></mfrac></math><br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; = Rs. 600</p>",
                    solution_hi: "<p>15.(d) बोतल का क्रय मूल्य = <math display=\"inline\"><mfrac><mrow><mn>100</mn><mi>&#160;</mi><mo>&#215;</mo><mn>408</mn></mrow><mrow><mn>68</mn></mrow></mfrac></math><br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; = Rs. 600</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>