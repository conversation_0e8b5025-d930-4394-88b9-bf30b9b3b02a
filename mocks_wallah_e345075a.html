<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">15:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 15</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">15</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 15 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 0,
                end: 13
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 14,
                end: 14
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "17",
                    question_en: "<p>1. The following graph shows the data of the production of cloths (in lakh tonnes) by three different companies A, B and C over the years.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736837004660.png\" alt=\"rId4\" width=\"260\" height=\"204\"> <br>What is the ratio of the average production of Company A in the period 2012-2014 to the average production of Company B in the same period?</p>",
                    question_hi: "<p>1. निम्नलिखित ग्राफ कुछ वर्षों में तीन अलग-अलग कंपनियों A, B और द्वारा कपड़े के उत्पादन (लाख टन में) के आंकड़ों को दर्शाता है।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736837004778.png\" alt=\"rId5\" width=\"243\" height=\"189\"> <br>संदर्भ: Production of clothes by three companies over the years - कुछ वर्षों में तीन कंपनियों द्वारा कपड़ों का उत्पादन <br>2012-2014 की अवधि में कंपनी A के औसत उत्पादन का समान अवधि में कंपनी B के औसत उत्पादन से अनुपात कितना है?</p>",
                    options_en: ["<p>5 : 3</p>", "<p>3 : 5</p>", 
                                "<p>25 : 23</p>", "<p>23 : 25</p>"],
                    options_hi: ["<p>5 : 3</p>", "<p>3 : 5</p>",
                                "<p>25 : 23</p>", "<p>23 : 25</p>"],
                    solution_en: "<p>1.(d) <br>Average production of Company A = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>25</mn><mo>+</mo><mn>50</mn><mo>+</mo><mn>40</mn></mrow><mn>3</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>115</mn><mn>3</mn></mfrac></math><br>Average production of Company B = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>35</mn><mo>+</mo><mn>40</mn><mo>+</mo><mn>50</mn></mrow><mn>3</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>125</mn><mn>3</mn></mfrac></math><br>Required ratio &rarr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>115</mn><mn>3</mn></mfrac></math> : <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>125</mn><mn>3</mn></mfrac></math> or 23 : 25</p>",
                    solution_hi: "<p>1.(d) <br>कंपनी A का औसत उत्पादन = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>25</mn><mo>+</mo><mn>50</mn><mo>+</mo><mn>40</mn></mrow><mn>3</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>115</mn><mn>3</mn></mfrac></math><br>कंपनी B का औसत उत्पादन = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>35</mn><mo>+</mo><mn>40</mn><mo>+</mo><mn>50</mn></mrow><mn>3</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>125</mn><mn>3</mn></mfrac></math><br>आवश्यक अनुपात &rarr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>115</mn><mn>3</mn></mfrac></math> : <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>125</mn><mn>3</mn></mfrac></math>&nbsp;या 23 : 25</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "17",
                    question_en: "<p>2. The number of subjects in which the students of a class have failed is given in the following table.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736837005017.png\" alt=\"rId6\" width=\"432\" height=\"65\"> <br>Find the total number of students who have failed in more than 3 subjects.</p>",
                    question_hi: "<p>2. एक कक्षा के विद्यार्थी जिन विषयों में अनुत्तीर्ण हुए हैं उनकी संख्या निम्न तालिका में दी गई है।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736837005165.png\" alt=\"rId7\" width=\"387\" height=\"61\"> <br>3 से अधिक विषयों में अनुत्तीर्ण होने वाले विद्यार्थियों की कुल संख्या ज्ञात कीजिए।</p>",
                    options_en: ["<p>10</p>", "<p>17</p>", 
                                "<p>11</p>", "<p>12</p>"],
                    options_hi: ["<p>10</p>", "<p>17</p>",
                                "<p>11</p>", "<p>12</p>"],
                    solution_en: "<p>2.(c)<br>Number of students failed in more than 3 subjects = 7 + 4 = 11</p>",
                    solution_hi: "<p>2.(c)<br>3 से अधिक विषयों में अनुत्तीर्ण विद्यार्थियों की संख्या = 7 + 4 = 11</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "17",
                    question_en: "<p>3. Study the given bar-graph carefully and answer the question that follows.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736837005302.png\" alt=\"rId8\" width=\"240\" height=\"181\"> <br>For how many years was the export above - average for the period, 2013 - 2019?</p>",
                    question_hi: "<p>3. दिए गए दंड-आलेख का ध्यानपूर्वक अध्ययन कीजिए और नीचे दिए गए प्रश्न का उत्तर दीजिए।<br>दंड आलेख दर्शाता है&hellip;<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736837005417.png\" alt=\"rId9\" width=\"258\" height=\"197\"> <br>2013-2019 की अवधि के लिए निर्यात कितने वर्षों के लिए औसत से अधिक था?</p>",
                    options_en: ["<p>3</p>", "<p>2</p>", 
                                "<p>4</p>", "<p>1</p>"],
                    options_hi: ["<p>3</p>", "<p>2</p>",
                                "<p>4</p>", "<p>1</p>"],
                    solution_en: "<p>3.(c) <br>Average of Export = <math display=\"inline\"><mfrac><mrow><mn>9</mn><mo>.</mo><mn>9</mn><mo>+</mo><mn>10</mn><mo>.</mo><mn>8</mn><mo>+</mo><mn>7</mn><mo>.</mo><mn>8</mn><mo>+</mo><mn>5</mn><mo>.</mo><mn>2</mn><mo>+</mo><mn>6</mn><mo>.</mo><mn>5</mn><mo>+</mo><mn>9</mn><mo>.</mo><mn>5</mn><mo>+</mo><mn>11</mn><mo>.</mo><mn>4</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> &asymp; 8.72<br>It is clear from the above expression 4 years the export above from the average of export.</p>",
                    solution_hi: "<p>3.(c) <br>निर्यात का औसत = <math display=\"inline\"><mfrac><mrow><mn>9</mn><mo>.</mo><mn>9</mn><mo>+</mo><mn>10</mn><mo>.</mo><mn>8</mn><mo>+</mo><mn>7</mn><mo>.</mo><mn>8</mn><mo>+</mo><mn>5</mn><mo>.</mo><mn>2</mn><mo>+</mo><mn>6</mn><mo>.</mo><mn>5</mn><mo>+</mo><mn>9</mn><mo>.</mo><mn>5</mn><mo>+</mo><mn>11</mn><mo>.</mo><mn>4</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> &asymp; 8.72<br>उपरोक्त अभिव्यक्ति से यह स्पष्ट है कि निर्यात के औसत से 4 वर्ष अधिक निर्यात हुआ।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "17",
                    question_en: "<p>4. The given table shows the number of entertainment shows (in hundreds) held in various cities. Study the table and answer the question that follows.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736837005539.png\" alt=\"rId10\" width=\"227\" height=\"124\"> <br>What is the average number of entertainment shows held in city P ?</p>",
                    question_hi: "<p>4. दी गई तालिका विभिन्न शहरों में आयोजित मनोरंजक कार्यक्रम की संख्या (सैकड़ों में) दर्शाती है। तालिका का अध्ययन करें और नीचे दिए गए प्रश्न का उत्तर दें।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736837005689.png\" alt=\"rId11\" width=\"207\" height=\"126\"> <br>शहर P में आयोजित मनोरंजक कार्यक्रमों की औसत संख्या ज्ञात करें।</p>",
                    options_en: ["<p>700</p>", "<p>750</p>", 
                                "<p>756</p>", "<p>766</p>"],
                    options_hi: ["<p>700</p>", "<p>750</p>",
                                "<p>756</p>", "<p>766</p>"],
                    solution_en: "<p>4.(c)<br>Average number of entertainment shows held in city P <br>= (<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>11</mn><mo>.</mo><mn>3</mn><mo>+</mo><mn>6</mn><mo>+</mo><mn>18</mn><mo>+</mo><mn>1</mn><mo>+</mo><mn>1</mn><mo>.</mo><mn>5</mn></mrow><mn>5</mn></mfrac></math>) &times; 100 <br>= 756</p>",
                    solution_hi: "<p>4.(c)<br>शहर P में आयोजित मनोरंजक कार्यक्रमों की औसत संख्या <br>= (<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>11</mn><mo>.</mo><mn>3</mn><mo>+</mo><mn>6</mn><mo>+</mo><mn>18</mn><mo>+</mo><mn>1</mn><mo>+</mo><mn>1</mn><mo>.</mo><mn>5</mn></mrow><mn>5</mn></mfrac></math>) &times; 100 <br>= 756</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "17",
                    question_en: "<p>5. Study the given pie-charts and answer the question that follows.<br>The pie-charts show the characteristics of foreign tourists visiting India during a given year.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736837005801.png\" alt=\"rId12\" width=\"180\" height=\"180\">,<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736837005923.png\" alt=\"rId13\" width=\"203\" height=\"173\"> <br>If in a given year, 2,50,000 tourists visited India and the age wise distribution of data applies to all the countries, then the number of Russian tourists who visited India during the year and were in the age group above 50 years is:</p>",
                    question_hi: "<p>5. दिए गए पाई-चार्ट का अध्ययन कीजिए और नीचे दिए गए प्रश्न का उत्तर दीजिए।<br>पाई-चार्ट किसी दिए गए वर्ष के दौरान भारत में आने वाले विदेशी पर्यटकों की विशेषताओं को दर्शाता है।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736837006078.png\" alt=\"rId14\" width=\"206\" height=\"207\">,<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736837006199.png\" alt=\"rId15\" width=\"205\" height=\"198\"> <br>यदि किसी दिए गए वर्ष में 2,50,000 पर्यटक भारत आए और डेटा का आयु-वार वितरण सभी देशों पर लागू होता है, तो वर्ष के दौरान भारत आने वाले रूसी पर्यटकों की संख्या, जो 50 वर्ष से अधिक आयु वर्ग में थे, है।</p>",
                    options_en: ["<p>375</p>", "<p>385</p>", 
                                "<p>3,750</p>", "<p>3,850</p>"],
                    options_hi: ["<p>375</p>", "<p>385</p>",
                                "<p>3,750</p>", "<p>3,850</p>"],
                    solution_en: "<p>5.(c)<br>Total number of tourist from Russia who visited india = <math display=\"inline\"><mfrac><mrow><mn>250000</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> &times; 15 = 37500<br>Russian tourist whose age above 50 = 37500 &times; <math display=\"inline\"><mfrac><mrow><mn>10</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> <br>= 3750</p>",
                    solution_hi: "<p>5.(c)<br>रूस से भारत आने वाले पर्यटकों की कुल संख्या = <math display=\"inline\"><mfrac><mrow><mn>250000</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> &times; 15 = 37500<br>रूसी पर्यटक जिसकी आयु 50 से अधिक है = 37500 &times; <math display=\"inline\"><mfrac><mrow><mn>10</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> <br>= 3750</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "17",
                    question_en: "<p>6. The given pie-diagram shows the expenditure incurred on the preparation of a book by a publisher, under various heads. Study the pie-diagram and answer the question that follows.<br>Various Expenditures (in percentage) incurred in Publishing a Book<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736837006354.png\" alt=\"rId16\" width=\"262\" height=\"163\"> <br>The marked price of a book is 20% more than the CP. If the marked price of the book is ₹30, then what is the cost of paper used in a single copy of the book ?</p>",
                    question_hi: "<p>6. दिया गया पाई-आरेख एक प्रकाशक द्वारा पुस्तक तैयार करने के लिए विभिन्न मदों के अंतर्गत किए गए व्यय को दर्शाता है। पाई-आरेख का अध्ययन कीजिए और नीचे दिए गए प्रश्नो के उत्तर दीजिए।<br>किसी पुस्तक के प्रकाशन में होने वाले विभिन्न व्यय (प्रतिशत में)।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736837006466.png\" alt=\"rId17\" width=\"273\" height=\"165\"> <br>एक पुस्तक का अंकित मूल्य, लागत मूल्य (CP) से 20% अधिक है। यदि पुस्तक का अंकित मूल्य ₹30 है, तो पुस्तक की एक प्रति में प्रयुक्त कागज की लागत क्या है ?</p>",
                    options_en: ["<p>₹6.50</p>", "<p>₹4.50</p>", 
                                "<p>₹5</p>", "<p>₹6</p>"],
                    options_hi: ["<p>₹6.50</p>", "<p>₹4.50</p>",
                                "<p>₹5</p>", "<p>₹6</p>"],
                    solution_en: "<p>6.(c)<br>Ratio - CP : MP<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;5&nbsp; &nbsp;:&nbsp; 6<br>(MP) 6 units = ₹ 30<br>(CP) 5 units = ₹ 25<br>cost of paper used in a single copy of the book = 25 &times; 20% = ₹ 5</p>",
                    solution_hi: "<p>6.(c)<br>अनुपात - क्रय मूल्य : अंकित मूल्य<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;5&nbsp; &nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;6<br>(अंकित मूल्य) 6 इकाई = ₹ 30<br>(क्रय मूल्य) 5 इकाई = ₹ 25<br>पुस्तक की एक प्रति में प्रयुक्त कागज की लागत = 25 &times; 20% = ₹ 5</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "17",
                    question_en: "<p>7. Study the given graph carefully and answer the question that follows. The graph shows the demand and production of different companies.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736837006773.png\" alt=\"rId18\" width=\"294\" height=\"152\"> <br>The ratio between the companies having more production than demand and more demand than the production is:</p>",
                    question_hi: "<p>7. दिए गए ग्राफ़ का ध्यानपूर्वक अध्ययन कीजिए और निम्नलिखित प्रश्न का उत्तर दीजिए। ग्राफ़ विभिन्न कंपनियों की मांग और उत्पादन को दर्शाता है।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736837007023.png\" alt=\"rId19\" width=\"253\" height=\"139\"> <br>मांग से अधिक उत्पादन और उत्पादन से अधिक मांग वाली कंपनियों के बीच का अनुपात क्या है?</p>",
                    options_en: ["<p>4 : 1</p>", "<p>2 : 1</p>", 
                                "<p>1 : 2</p>", "<p>1 : 4</p>"],
                    options_hi: ["<p>4 : 1</p>", "<p>2 : 1</p>",
                                "<p>1 : 2</p>", "<p>1 : 4</p>"],
                    solution_en: "<p>7.(b) <br>companies having more production than demand = 4<br>companies having more demand than the production = 2<br>Required ratio = 4 : 2 = 2 : 1</p>",
                    solution_hi: "<p>7.(b) <br>मांग से अधिक उत्पादन वाली कंपनियाँ = 4<br>उत्पादन से अधिक मांग वाली कंपनियाँ = 2<br>आवश्यक अनुपात = 4 : 2 = 2 : 1</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "17",
                    question_en: "<p>8. Study the given graph and answer the question that follows. The graph represent the depreciation of a car from 2001 to 2007.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736837007309.png\" alt=\"rId20\" width=\"206\" height=\"183\"> <br>What is the depreciation of the car from 2001 to 2007 in percentage?</p>",
                    question_hi: "<p>8. निम्नलिखित ग्राफ का अध्ययन कीजिए और नीचे दिए गए प्रश्न का उत्तर दीजिए। <br>ग्राफ में वर्ष 2001 से वर्ष 2007 तक एक कार के मूल्यह्रास को दर्शाया गया है।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736837007547.png\" alt=\"rId21\" width=\"224\" height=\"194\"> <br>वर्ष 2001 से वर्ष 2007 तक कार का कितने प्रतिशत मूल्यह्रास हुआ था?</p>",
                    options_en: ["<p>81.32%</p>", "<p>62%</p>", 
                                "<p>58%</p>", "<p>75.83%</p>"],
                    options_hi: ["<p>81.32%</p>", "<p>62%</p>",
                                "<p>58%</p>", "<p>75.83%</p>"],
                    solution_en: "<p>8.(d)<br>Change in % = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>24000</mn><mo>-</mo><mn>5800</mn></mrow><mn>24000</mn></mfrac></math> &times; 100 <br>= 75.83%</p>",
                    solution_hi: "<p>8.(d)<br>% में परिवर्तन = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>24000</mn><mo>-</mo><mn>5800</mn></mrow><mn>24000</mn></mfrac></math> &times; 100 <br>= 75.83%</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "17",
                    question_en: "<p>9. The following line graph shows the ratio of imports to exports of two companies over the years. Study the graph carefully and answer the question that follows. <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736837007765.png\" alt=\"rId22\" width=\"324\" height=\"186\"> <br>The exports of Company M with relation to imports was the maximum in which of the following years?</p>",
                    question_hi: "<p>9. निम्नलिखित रेखा आलेख दिए गए वर्षों में दो कंपनियों के आयात और निर्यात का अनुपात दर्शाता है। आलेख का ध्यानपूर्वक अध्ययन कीजिए और नीचे दिए गए प्रश्न का उत्तर दीजिए।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736837007765.png\" alt=\"rId22\" width=\"319\" height=\"183\"> <br>आयात के संबंध में कंपनी M का निर्यात निम्नलिखित में से किस वर्ष में अधिकतम था?</p>",
                    options_en: ["<p>2008</p>", "<p>2009</p>", 
                                "<p>2006</p>", "<p>2005</p>"],
                    options_hi: ["<p>2008</p>", "<p>2009</p>",
                                "<p>2006</p>", "<p>2005</p>"],
                    solution_en: "<p>9.(d)<br>Ratio of imports to export for company M when checking options one by one - <br>In 2008 :- <math display=\"inline\"><mfrac><mrow><mi>i</mi><mi>m</mi><mi>p</mi><mi>o</mi><mi>r</mi><mi>t</mi></mrow><mrow><mi>e</mi><mi>x</mi><mi>p</mi><mi>o</mi><mi>r</mi><mi>t</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1</mn><mpadded lspace=\"-1px\"><mo rspace=\"1\">.</mo></mpadded><mn>4</mn></mrow><mn>1</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>14</mn><mn>10</mn></mfrac></math><br>In 2009 :- <math display=\"inline\"><mfrac><mrow><mi>i</mi><mi>m</mi><mi>p</mi><mi>o</mi><mi>r</mi><mi>t</mi></mrow><mrow><mi>e</mi><mi>x</mi><mi>p</mi><mi>o</mi><mi>r</mi><mi>t</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1</mn><mo>.</mo><mn>3</mn></mrow><mn>1</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>13</mn><mn>10</mn></mfrac></math><br>In 2006 :- <math display=\"inline\"><mfrac><mrow><mi>i</mi><mi>m</mi><mi>p</mi><mi>o</mi><mi>r</mi><mi>t</mi></mrow><mrow><mi>e</mi><mi>x</mi><mi>p</mi><mi>o</mi><mi>r</mi><mi>t</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1</mn><mo>.</mo><mn>2</mn></mrow><mn>1</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>12</mn><mn>10</mn></mfrac></math><br>In 2005 :- <math display=\"inline\"><mfrac><mrow><mi>i</mi><mi>m</mi><mi>p</mi><mi>o</mi><mi>r</mi><mi>t</mi></mrow><mrow><mi>e</mi><mi>x</mi><mi>p</mi><mi>o</mi><mi>r</mi><mi>t</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>0</mn><mo>.</mo><mn>8</mn></mrow><mn>1</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>8</mn><mn>10</mn></mfrac></math><br>Clearly, in the year 2005 exports of company M with relation to imports was the maximum in the given years.</p>",
                    solution_hi: "<p>9.(d)<br>एक-एक करके विकल्पों की जाँच करने पर कंपनी M के लिए आयात और निर्यात का अनुपात - <br>2008 में :- <math style=\"font-family:Verdana\" display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>&#2310;&#2351;&#2366;&#2340;</mi><mi>&#2344;&#2367;&#2352;&#2381;&#2351;&#2366;&#2340;</mi></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1</mn><mpadded lspace=\"-1px\"><mo rspace=\"1\">.</mo></mpadded><mn>4</mn></mrow><mn>1</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>14</mn><mn>10</mn></mfrac></math><br>2009 में :- <math style=\"font-family:Verdana\" display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>&#2310;&#2351;&#2366;&#2340;</mi><mi>&#2344;&#2367;&#2352;&#2381;&#2351;&#2366;&#2340;</mi></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1</mn><mo>.</mo><mn>3</mn></mrow><mn>1</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>13</mn><mn>10</mn></mfrac></math><br>2006 में :- <math style=\"font-family:Verdana\" display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>&#2310;&#2351;&#2366;&#2340;</mi><mi>&#2344;&#2367;&#2352;&#2381;&#2351;&#2366;&#2340;</mi></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1</mn><mo>.</mo><mn>2</mn></mrow><mn>1</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>12</mn><mn>10</mn></mfrac></math><br>2005 में :- <math style=\"font-family:Verdana\" display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>&#2310;&#2351;&#2366;&#2340;</mi><mi>&#2344;&#2367;&#2352;&#2381;&#2351;&#2366;&#2340;</mi></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>0.8</mn><mn>1</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>8</mn><mn>10</mn></mfrac></math><br>स्पष्ट रूप से, वर्ष 2005 में आयात के संबंध में कंपनी M का निर्यात दिए गए वर्षों में अधिकतम था।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "17",
                    question_en: "<p>10. Study the given pie-chart and answer the question that follows.<br>The pie-chart represents the total number of valid votes obtained by four students who contested for school leadership. The total number of valid votes polled was 720.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736837007900.png\" alt=\"rId23\" width=\"175\" height=\"170\"> <br>Who won the election?</p>",
                    question_hi: "<p>10. निम्नलिखित पाई-चार्ट का अध्ययन कीजिए और नीचे दिए गए प्रश्न का उत्तर दीजिए।<br>यह पाई-चार्ट स्कूल लीडरशिप के लिए चुनाव लड़ने वाले चार विद्यार्थियों द्वारा प्राप्त वैध मतों की कुल संख्या का निरूपण करता है। डाले गए वैध मर्तों की कुल संख्या 720 थी।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736837008046.png\" alt=\"rId24\" width=\"205\" height=\"181\"> <br>चुनाव किसने जीता?</p>",
                    options_en: ["<p>Yasin</p>", "<p>Paramjit Kaur</p>", 
                                "<p>Vishwanath</p>", "<p>Siva Raman</p>"],
                    options_hi: ["<p>यासीन</p>", "<p>परमजीत कौर</p>",
                                "<p>विश्वनाथ</p>", "<p>शिव रमन</p>"],
                    solution_en: "<p>10.(d)<br>Clearly, from given pie-chart<br>Siva Raman won the election as he has highest share (120&deg;)</p>",
                    solution_hi: "<p>10.(d)<br>स्पष्ट रूप से, दिए गए पाई-चार्ट से<br>शिव रमन ने चुनाव जीता क्योंकि उनके पास सबसे अधिक हिस्सेदारी (120&deg;) है</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "17",
                    question_en: "<p>11. The given table represents the percentage marks of three students in three subjects. Study the table and answer the question.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736837008164.png\" alt=\"rId25\" width=\"280\" height=\"101\"> <br>The average marks obtain by Sohan in all three subject is equal to:</p>",
                    question_hi: "<p>11. दी गई तालिका तीन विषयों में तीन छात्रों के प्रतिशत अंकों को दर्शाती है। तालिका का अध्ययन कीजिए और प्रश्न का उत्तर दीजिए। <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736837008265.png\" alt=\"rId26\" width=\"200\" height=\"105\"> <br>सोहन द्वारा सभी तीनों विषयों में प्राप्त किए गए औसत अंक _______ के बराबर हैं।</p>",
                    options_en: ["<p>82</p>", "<p>84</p>", 
                                "<p>78</p>", "<p>80</p>"],
                    options_hi: ["<p>82</p>", "<p>84</p>",
                                "<p>78</p>", "<p>80</p>"],
                    solution_en: "<p>11.(a) <br>Average = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>84</mn><mo>+</mo><mn>80</mn><mo>+</mo><mn>82</mn></mrow><mn>3</mn></mfrac></math> <br>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>246</mn><mn>3</mn></mfrac></math> <br>= 82</p>",
                    solution_hi: "<p>11.(a) <br>औसत = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>84</mn><mo>+</mo><mn>80</mn><mo>+</mo><mn>82</mn></mrow><mn>3</mn></mfrac></math> <br>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>246</mn><mn>3</mn></mfrac></math> <br>= 82</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "17",
                    question_en: "<p>12. Study the given bar-graph and answer the question that follows.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736837008377.png\" alt=\"rId27\" width=\"274\" height=\"225\"> <br>Which age group has the least number of people?</p>",
                    question_hi: "<p>12. दिए गए दंड आलेख का अध्ययन कीजिए और नीचे दिए गए प्रश्न का उत्तर दीजिए।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736837008479.png\" alt=\"rId28\" width=\"269\" height=\"221\"> <br>किस आयु वर्ग के लोगों की संख्या न्यूनतम है?</p>",
                    options_en: ["<p>80+</p>", "<p>30-39</p>", 
                                "<p>50-59</p>", "<p>&lt;18</p>"],
                    options_hi: ["<p>80+</p>", "<p>30-39</p>",
                                "<p>50-59</p>", "<p>&lt;18</p>"],
                    solution_en: "<p>12.(d)<br>Age group of &lt;18(less than 18) has the least number of people.</p>",
                    solution_hi: "<p>12.(d)<br>&lt;18 (18 से कम) आयु वर्ग में लोगों की संख्या सबसे कम है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "17",
                    question_en: "<p>13. Study the given graph carefully and answer the question that follows. <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736837008722.png\" alt=\"rId29\" width=\"321\" height=\"185\"> <br>On the basis of this multiple bar graph, What is the change in the number of Literature books from the year 2020 to the year 2023.</p>",
                    question_hi: "<p>13. दिए गए ग्राफ का ध्यानपूर्वक अध्ययन कीजिए और नीचे दिए गए प्रश्न का उत्तर दीजिए।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736837008990.png\" alt=\"rId30\" width=\"316\" height=\"191\"> <br>इस बहु दंड आलेख (multiple bar graph) के आधार पर, वर्ष 2020 से वर्ष 2023 तक साहित्य की पुस्तकों की संख्या में क्या परिवर्तन हुआ?</p>",
                    options_en: ["<p>1000 more</p>", "<p>2000 more</p>", 
                                "<p>2000 less</p>", "<p>No change</p>"],
                    options_hi: ["<p>1000 अधिक</p>", "<p>2000 अधिक</p>",
                                "<p>2000 कम</p>", "<p>कोई परिवर्तन नहीं</p>"],
                    solution_en: "<p>13.(c)<br>Change in the number of literature book = 5000 - 3000 <br>= 2000 less</p>",
                    solution_hi: "<p>13.(c)<br>साहित्य पुस्तक की संख्या में परिवर्तन = 5000 - 3000 <br>= 2000 कम</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "17",
                    question_en: "<p>14. Study the given graph carefully and answer the questions that follows. The graph shows the demand and production of different companies.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736837006773.png\" alt=\"rId18\" width=\"347\" height=\"179\"> <br>Lowest production is what percentage of the highest demand ?</p>",
                    question_hi: "<p>14. दिए गए ग्राफ़ का ध्यानपूर्वक अध्ययन कीजिए और निम्नलिखित प्रश्न का उत्तर दीजिए। ग्राफ़ विभिन्न कंपनियों की मांग और उत्पादन को दर्शाता है।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736837007023.png\" alt=\"rId19\" width=\"291\" height=\"160\"> <br>न्यूनतम उत्पादन सर्वाधिक मांग का कितना प्रतिशत है?</p>",
                    options_en: ["<p>29%</p>", "<p>30%</p>", 
                                "<p>71%</p>", "<p>70%</p>"],
                    options_hi: ["<p>29%</p>", "<p>30%</p>",
                                "<p>71%</p>", "<p>70%</p>"],
                    solution_en: "<p>14.(a) <br>required% = <math display=\"inline\"><mfrac><mrow><mn>1450</mn></mrow><mrow><mn>5000</mn></mrow></mfrac></math> &times; 100 <br>= 29%</p>",
                    solution_hi: "<p>14.(a) <br>आवश्यक% = <math display=\"inline\"><mfrac><mrow><mn>1450</mn></mrow><mrow><mn>5000</mn></mrow></mfrac></math> &times; 100 <br>= 29%</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "misc",
                    question_en: "<p>15. A battery manufacturer manufactures five different types of batteries. The total revenue for the year 2020 is ₹25,00,000 and 20,000 units were exported in 2020. The distribution of revenue and units for the five different types of batteries is shown in the charts<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736837009173.png\" alt=\"rId31\" width=\"231\" height=\"178\">,<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736837009289.png\" alt=\"rId32\" width=\"204\" height=\"172\"> <br>Which type of battery provides the lowest revenue per unit?</p>",
                    question_hi: "<p>15. एक बैटरी निर्माता पांच अलग-अलग प्रकार की बैटरियों का निर्माण करता है। वर्ष 2020 के लिए कुल राजस्व ₹25,00,000 है और 2020 में 20,000 इकाइयों का निर्यात किया गया था। पांच अलग-अलग प्रकार की बैटरियों के लिए राजस्व और इकाइयों का वितरण चार्ट में दिखाया गया है।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736837009419.png\" alt=\"rId33\" width=\"231\" height=\"178\">,<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736837009535.png\" alt=\"rId34\" width=\"194\" height=\"174\"> <br>किस प्रकार की बैटरी प्रति इकाई सबसे कम राजस्व प्रदान करती है?</p>",
                    options_en: ["<p>Sodium</p>", "<p>Li-ion</p>", 
                                "<p>M-Air</p>", "<p>Lead acid</p>"],
                    options_hi: ["<p>सोडियम</p>", "<p>ली-आयन</p>",
                                "<p>एम-एयर</p>", "<p>लेड ऐसिड</p>"],
                    solution_en: "<p>15.(d)<br>Export units of sodium = 20000 &times; <math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 400<br>Export units of Li-ion = 200 &times; 30 = 6000<br>Export units of M-Air = 200 &times; 20 = 4000<br>Export units of Lead acid = 200 &times; 35&nbsp;= 7000<br>revenue of sodium per units = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2500000</mn><mo>&#215;</mo><mn>5</mn></mrow><mrow><mn>100</mn><mo>&#215;</mo><mn>400</mn></mrow></mfrac></math> = 312.5<br>revenue of Li-ion per units = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2500000</mn><mo>&#215;</mo><mn>35</mn></mrow><mrow><mn>100</mn><mo>&#215;</mo><mn>6000</mn></mrow></mfrac></math> = 145.83<br>revenue of M-Air per units = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2500000</mn><mo>&#215;</mo><mn>20</mn></mrow><mrow><mn>100</mn><mo>&#215;</mo><mn>4000</mn></mrow></mfrac></math> = 125<br>revenue of Lead acid per units = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2500000</mn><mo>&#215;</mo><mn>25</mn></mrow><mrow><mn>100</mn><mo>&#215;</mo><mn>7000</mn></mrow></mfrac></math> = 89.28<br>We can clearly see that the revenue per unit of lead acid battery is the lowest.</p>",
                    solution_hi: "<p>15.(d)<br>सोडियम की निर्यात इकाइयाँ = 20000 &times; <math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 400<br>ली-आयन की निर्यात इकाइयाँ = 200 &times; 30 = 6000<br>एम-एयर की निर्यात इकाइयाँ = 200 &times; 20 = 4000<br>लेड एसिड की निर्यात इकाइयाँ = 200 &times; 35&nbsp;= 7000<br>प्रति यूनिट सोडियम का राजस्व = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2500000</mn><mo>&#215;</mo><mn>5</mn></mrow><mrow><mn>100</mn><mo>&#215;</mo><mn>400</mn></mrow></mfrac></math>&nbsp;= 312.5<br>प्रति इकाई ली-आयन का राजस्व = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2500000</mn><mo>&#215;</mo><mn>35</mn></mrow><mrow><mn>100</mn><mo>&#215;</mo><mn>6000</mn></mrow></mfrac></math>&nbsp;= 145.83<br>प्रति यूनिट एम-एयर का राजस्व = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2500000</mn><mo>&#215;</mo><mn>20</mn></mrow><mrow><mn>100</mn><mo>&#215;</mo><mn>4000</mn></mrow></mfrac></math>&nbsp;= 125<br>प्रति यूनिट लेड एसिड का राजस्व = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2500000</mn><mo>&#215;</mo><mn>25</mn></mrow><mrow><mn>100</mn><mo>&#215;</mo><mn>7000</mn></mrow></mfrac></math>&nbsp;= 89.28<br>हम स्पष्ट रूप से देख सकते हैं कि लेड एसिड बैटरी की प्रति यूनिट आय सबसे कम है</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>