<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">60:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 100</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">100</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 60 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["7"] = {
                name: "English",
                start: 0,
                end: 23
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="7">English</option>`;
            sections["6"] = {
                name: "Reasoning",
                start: 24,
                end: 48
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="6">Reasoning</option>`;
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 49,
                end: 73
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["18"] = {
                name: "General Awareness",
                start: 74,
                end: 98
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="18">General Awareness</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 99,
                end: 99
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "7",
                    question_en: "<p>1. The following sentence has been split into four segments. Identify the segment that contains an error.<br>If she exercises daily, / she would / remain energetic / and fit forever.</p>",
                    question_hi: "<p>1. The following sentence has been split into four segments. Identify the segment that contains an error.<br>If she exercises daily, / she would / remain energetic / and fit forever.</p>",
                    options_en: ["<p>she would</p>", "<p>If she exercises daily</p>", 
                                "<p>and fit forever</p>", "<p>remain energetic</p>"],
                    options_hi: ["<p>she would</p>", "<p>If she exercises daily</p>",
                                "<p>and fit forever</p>", "<p>remain energetic</p>"],
                    solution_en: "<p>1.(a) she would <br>The given sentence is an example of the first conditional sentence and the correct grammatical structure is &ldquo;if + simple present&hellip;&hellip;.will +V<sub>1</sub>&rdquo;. Hence &lsquo;she will&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>1.(a) she would <br>दिया गया Sentence, First conditional sentence का एक Example है तथा &ldquo;if + simple present&hellip;&hellip;.will +V<sub>1</sub>&rdquo; सही Grammatical structure&rdquo; है। अतः &lsquo;she will&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "7",
                    question_en: "<p>2. The following sentence has been split into four segments. Identify the segment that contains a grammatical error.<br>The teacher will never / give you punishment / if you will / come on time.</p>",
                    question_hi: "<p>2. The following sentence has been split into four segments. Identify the segment that contains a grammatical error.<br>The teacher will never / give you punishment / if you will / come on time.</p>",
                    options_en: ["<p>come on time</p>", "<p>the teacher will never</p>", 
                                "<p>if you will</p>", "<p>give you punishment</p>"],
                    options_hi: ["<p>come on time</p>", "<p>the teacher will never</p>",
                                "<p>if you will</p>", "<p>give you punishment</p>"],
                    solution_en: "<p>2.(c) if you will<br>The given sentence is an example of the first conditional sentence and the correct grammatical structure is &ldquo;if + simple present&hellip;&hellip;.will +V<sub>1</sub>&rdquo;. Therefore, &lsquo;will&rsquo; must be removed. Hence &lsquo;if you come on time&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>2.(c) if you will<br>दिया गया Sentence, First conditional sentence का एक Example है तथा &ldquo;if + simple present&hellip;&hellip;.will +V<sub>1</sub>&rdquo; सही Grammatical structure\" है। इसलिए, &lsquo;will&rsquo; को हटा दिया जाएगा। अतः &lsquo;if you come on time&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "7",
                    question_en: "<p>3. The following sentence has been split into four segments. Identify the segment that contains a grammatical error.<br>With hard work, commitment / and proper guidance, / Sivaram has become / a IPS officer.</p>",
                    question_hi: "<p>3. The following sentence has been split into four segments. Identify the segment that contains a grammatical error.<br>With hard work, commitment / and proper guidance, / Sivaram has become / a IPS officer.</p>",
                    options_en: ["<p>a IPS officer</p>", "<p>and proper guidance,</p>", 
                                "<p>Sivaram has become</p>", "<p>With hard work, commitment</p>"],
                    options_hi: ["<p>a IPS officer</p>", "<p>and proper guidance,</p>",
                                "<p>Sivaram has become</p>", "<p>With hard work, commitment</p>"],
                    solution_en: "<p>3.(a) a IPS officer<br>Article &lsquo;A&rsquo; is used before the words starting with a consonant sound - (a conversation, a story). Whereas, the article &lsquo;An&rsquo; is used before the words starting with a vowel sound (an imminent danger, an apple). In the given sentence, the abbreviation &lsquo;IPS&rsquo; starts with a vowel sound. Hence, &lsquo;an IPS officer&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>3.(a) a IPS officer<br>Consonant sound से प्रारंभ होने वाले Words से पहले Article &lsquo;A&rsquo; का प्रयोग किया जाता है, (a conversation, a story)। जबकि Vowel sound से प्रारंभ होने वाले Words से पहले Article &lsquo;An&rsquo; का प्रयोग किया जाता है, (an imminent danger, an apple)। दिए गए Sentence में, संक्षिप्त नाम &lsquo;IPS&rsquo; एक Vowel sound से प्रारंभ होता है। अतः, &lsquo;an IPS officer&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "7",
                    question_en: "4. Parts of the following sentence have been given as options. Select the option that contains an error.<br />A owl is a bird which has wide eyes, asymmetrical ears and is found mostly in forests.",
                    question_hi: "4. Parts of the following sentence have been given as options. Select the option that contains an error.<br />A owl is a bird which has wide eyes, asymmetrical ears and is found mostly in forests.",
                    options_en: [" which has wide eyes, ", " asymmetrical ears ", 
                                " and is found mostly in forests. ", " A owl is a bird"],
                    options_hi: [" which has wide eyes, ", " asymmetrical ears ",
                                " and is found mostly in forests. ", " A owl is a bird"],
                    solution_en: "4.(d) A owl is a bird<br />Article ‘A’ is used before the words starting from a consonant sound - (a conversation, a story). Whereas, the article ‘An’ is used before the words starting from a vowel sound(an imminent danger, an apple). In the given sentence, the word ‘owl’ starts with a vowel sound. Hence, ‘An owl is a bird’ is the most appropriate answer.",
                    solution_hi: "4.(d) A owl is a bird<br />Consonant sound से प्रारंभ होने वाले Words से पहले Article ‘A’ का प्रयोग किया जाता है, (a conversation, a story)। जबकि Vowel sound से प्रारंभ होने वाले Words से पहले Article ‘An’ का प्रयोग किया जाता है, (an imminent danger, an apple)। दिए गए Sentence में, Word ‘owl’ एक Vowel sound से प्रारंभ होता है। अतः, ‘An owl is a bird’ सबसे उपयुक्त उत्तर है।",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "7",
                    question_en: "<p>5. <strong>Cloze Test:</strong><br>Romantic poetry is a literary movement that emerged in the late 18th century and continued through the mid-19th century. It is (5)________ by an emphasis on emotion, imagination and individualism. Romantic poets sought to express their innermost thoughts and feelings through their poetry, often exploring (6)_______ such as love, beauty and mortality. One of the defining features of Romantic poetry is its use of language and imagery to evoke powerful emotions in the reader. Many Romantic poets, such as William Wordsworth and Samuel Taylor Coleridge (7)________ that poetry should be written in the language of common people, rather than in the lofty, formal language of the past. They also incorporated vivid descriptions of nature and the natural world into their poetry, using it as a source of inspiration and spiritual renewal. (8)______ important aspect of Romantic poetry is its focus on the individual and the subjective experience. Romantic poets believed that each person\'s experience of the world was unique and valuable, and sought to capture this individuality in their writing. This led to a greater emphasis on personal expression and introspection in poetry, (9)_______ a rejection of the traditional poetic forms and structures of the past.<br>Select the most appropriate option to fill in blank number 5.</p>",
                    question_hi: "<p>5. <strong>Cloze Test:</strong><br>Romantic poetry is a literary movement that emerged in the late 18th century and continued through the mid-19th century. It is (5)________ by an emphasis on emotion, imagination and individualism. Romantic poets sought to express their innermost thoughts and feelings through their poetry, often exploring (6)_______ such as love, beauty and mortality. One of the defining features of Romantic poetry is its use of language and imagery to evoke powerful emotions in the reader. Many Romantic poets, such as William Wordsworth and Samuel Taylor Coleridge (7)________ that poetry should be written in the language of common people, rather than in the lofty, formal language of the past. They also incorporated vivid descriptions of nature and the natural world into their poetry, using it as a source of inspiration and spiritual renewal. (8)______ important aspect of Romantic poetry is its focus on the individual and the subjective experience. Romantic poets believed that each person\'s experience of the world was unique and valuable, and sought to capture this individuality in their writing. This led to a greater emphasis on personal expression and introspection in poetry, (9)_______ a rejection of the traditional poetic forms and structures of the past.<br>Select the most appropriate option to fill in blank number 5.</p>",
                    options_en: ["<p>characterising</p>", "<p>characterises</p>", 
                                "<p>characteristic</p>", "<p>characterised</p>"],
                    options_hi: ["<p>characterising</p>", "<p>characterises</p>",
                                "<p>characteristic</p>", "<p>characterised</p>"],
                    solution_en: "<p>5.(d) characterised <br>&lsquo;Characterised&rsquo; means to describe the quality of something. The given passage states that it is characterised by an emphasis on emotion, imagination and individualism. Hence &lsquo;characterised&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>5.(d) characterised <br>&lsquo;Characterised&rsquo; का अर्थ है किसी चीज़ की Quality को Describe करना। दिए गए Passage में कहा गया है कि इसकी विशेषता यह है कि इसमें भावना (emotion), कल्पना (imagination) और व्यक्तिवाद (individualism) पर जोर दिया जाता है। अतः &lsquo;characterised&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "7",
                    question_en: "<p>6. <strong>Cloze Test:</strong><br>Romantic poetry is a literary movement that emerged in the late 18th century and continued through the mid-19th century. It is (5)________ by an emphasis on emotion, imagination and individualism. Romantic poets sought to express their innermost thoughts and feelings through their poetry, often exploring (6)_______ such as love, beauty and mortality. One of the defining features of Romantic poetry is its use of language and imagery to evoke powerful emotions in the reader. Many Romantic poets, such as William Wordsworth and Samuel Taylor Coleridge (7)________ that poetry should be written in the language of common people, rather than in the lofty, formal language of the past. They also incorporated vivid descriptions of nature and the natural world into their poetry, using it as a source of inspiration and spiritual renewal. (8)______ important aspect of Romantic poetry is its focus on the individual and the subjective experience. Romantic poets believed that each person\'s experience of the world was unique and valuable, and sought to capture this individuality in their writing. This led to a greater emphasis on personal expression and introspection in poetry, (9)_______ a rejection of the traditional poetic forms and structures of the past.<br>Select the most appropriate option to fill in blank number 6.</p>",
                    question_hi: "<p>6. <strong>Cloze Test:</strong><br>Romantic poetry is a literary movement that emerged in the late 18th century and continued through the mid-19th century. It is (5)________ by an emphasis on emotion, imagination and individualism. Romantic poets sought to express their innermost thoughts and feelings through their poetry, often exploring (6)_______ such as love, beauty and mortality. One of the defining features of Romantic poetry is its use of language and imagery to evoke powerful emotions in the reader. Many Romantic poets, such as William Wordsworth and Samuel Taylor Coleridge (7)________ that poetry should be written in the language of common people, rather than in the lofty, formal language of the past. They also incorporated vivid descriptions of nature and the natural world into their poetry, using it as a source of inspiration and spiritual renewal. (8)______ important aspect of Romantic poetry is its focus on the individual and the subjective experience. Romantic poets believed that each person\'s experience of the world was unique and valuable, and sought to capture this individuality in their writing. This led to a greater emphasis on personal expression and introspection in poetry, (9)_______ a rejection of the traditional poetic forms and structures of the past.<br>Select the most appropriate option to fill in blank number 6.</p>",
                    options_en: ["<p>arguments</p>", "<p>concerns</p>", 
                                "<p>problems</p>", "<p>themes</p>"],
                    options_hi: ["<p>arguments</p>", "<p>concerns</p>",
                                "<p>problems</p>", "<p>themes</p>"],
                    solution_en: "<p>6.(d) themes<br>&lsquo;Theme&rsquo; means an idea that recurs in a work of art or literature. The given passage states that romantic poets sought to express their innermost thoughts and feelings through their poetry, often exploring themes such as love, beauty and mortality. Hence &lsquo;themes&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>6.(d) themes<br>&lsquo;Theme&rsquo; का अर्थ है एक ऐसा विचार जो कला (art) या साहित्य (literature) के किसी कार्य में बार-बार आता है। दिए गए Passage में कहा गया है कि रोमांटिक कवियों ने अपनी कविता के माध्यम से अपने अंतरतम विचारों (innermost thoughts) और भावनाओं (feelings) को व्यक्त करने का प्रयास किया, अक्सर प्रेम, सौंदर्य और नश्वरता जैसे विषयों की खोज की। अतः &lsquo;themes&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "7",
                    question_en: "<p>7. <strong>Cloze Test:</strong><br>Romantic poetry is a literary movement that emerged in the late 18th century and continued through the mid-19th century. It is (5)________ by an emphasis on emotion, imagination and individualism. Romantic poets sought to express their innermost thoughts and feelings through their poetry, often exploring (6)_______ such as love, beauty and mortality. One of the defining features of Romantic poetry is its use of language and imagery to evoke powerful emotions in the reader. Many Romantic poets, such as William Wordsworth and Samuel Taylor Coleridge (7)________ that poetry should be written in the language of common people, rather than in the lofty, formal language of the past. They also incorporated vivid descriptions of nature and the natural world into their poetry, using it as a source of inspiration and spiritual renewal. (8)______ important aspect of Romantic poetry is its focus on the individual and the subjective experience. Romantic poets believed that each person\'s experience of the world was unique and valuable, and sought to capture this individuality in their writing. This led to a greater emphasis on personal expression and introspection in poetry, (9)_______ a rejection of the traditional poetic forms and structures of the past.<br>Select the most appropriate option to fill in blank number 7.</p>",
                    question_hi: "<p>7. <strong>Cloze Test:</strong><br>Romantic poetry is a literary movement that emerged in the late 18th century and continued through the mid-19th century. It is (5)________ by an emphasis on emotion, imagination and individualism. Romantic poets sought to express their innermost thoughts and feelings through their poetry, often exploring (6)_______ such as love, beauty and mortality. One of the defining features of Romantic poetry is its use of language and imagery to evoke powerful emotions in the reader. Many Romantic poets, such as William Wordsworth and Samuel Taylor Coleridge (7)________ that poetry should be written in the language of common people, rather than in the lofty, formal language of the past. They also incorporated vivid descriptions of nature and the natural world into their poetry, using it as a source of inspiration and spiritual renewal. (8)______ important aspect of Romantic poetry is its focus on the individual and the subjective experience. Romantic poets believed that each person\'s experience of the world was unique and valuable, and sought to capture this individuality in their writing. This led to a greater emphasis on personal expression and introspection in poetry, (9)_______ a rejection of the traditional poetic forms and structures of the past.<br>Select the most appropriate option to fill in blank number 7.</p>",
                    options_en: ["<p>could believe</p>", "<p>believes</p>", 
                                "<p>believed</p>", "<p>will believe</p>"],
                    options_hi: ["<p>could believe</p>", "<p>believes</p>",
                                "<p>believed</p>", "<p>will believe</p>"],
                    solution_en: "<p>7.(c) believed<br>The given passage is expressing a belief of romantic poets in the past. Therefore, past form (V<sub>2</sub>) of the verb will be used to indicate the past tense. Hence &lsquo;believed (V<sub>2</sub>)&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>7.(c) believed<br>दिया गया Passage, Past में रोमांटिक कवियों की एक धारणा (belief) को Express करता है। इसलिए, Past tense को indicate करने के लिए, Verb की past form (V<sub>2</sub>) का प्रयोग किया जाएगा। अतः &lsquo;believed (V<sub>2</sub>)&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "7",
                    question_en: "<p>8. <strong>Cloze Test:</strong><br>Romantic poetry is a literary movement that emerged in the late 18th century and continued through the mid-19th century. It is (5)________ by an emphasis on emotion, imagination and individualism. Romantic poets sought to express their innermost thoughts and feelings through their poetry, often exploring (6)_______ such as love, beauty and mortality. One of the defining features of Romantic poetry is its use of language and imagery to evoke powerful emotions in the reader. Many Romantic poets, such as William Wordsworth and Samuel Taylor Coleridge (7)________ that poetry should be written in the language of common people, rather than in the lofty, formal language of the past. They also incorporated vivid descriptions of nature and the natural world into their poetry, using it as a source of inspiration and spiritual renewal. (8)______ important aspect of Romantic poetry is its focus on the individual and the subjective experience. Romantic poets believed that each person\'s experience of the world was unique and valuable, and sought to capture this individuality in their writing. This led to a greater emphasis on personal expression and introspection in poetry, (9)_______ a rejection of the traditional poetic forms and structures of the past.<br>Select the most appropriate option to fill in blank number 8.</p>",
                    question_hi: "<p>8. <strong>Cloze Test:</strong><br>Romantic poetry is a literary movement that emerged in the late 18th century and continued through the mid-19th century. It is (5)________ by an emphasis on emotion, imagination and individualism. Romantic poets sought to express their innermost thoughts and feelings through their poetry, often exploring (6)_______ such as love, beauty and mortality. One of the defining features of Romantic poetry is its use of language and imagery to evoke powerful emotions in the reader. Many Romantic poets, such as William Wordsworth and Samuel Taylor Coleridge (7)________ that poetry should be written in the language of common people, rather than in the lofty, formal language of the past. They also incorporated vivid descriptions of nature and the natural world into their poetry, using it as a source of inspiration and spiritual renewal. (8)______ important aspect of Romantic poetry is its focus on the individual and the subjective experience. Romantic poets believed that each person\'s experience of the world was unique and valuable, and sought to capture this individuality in their writing. This led to a greater emphasis on personal expression and introspection in poetry, (9)_______ a rejection of the traditional poetic forms and structures of the past.<br>Select the most appropriate option to fill in blank number 8.</p>",
                    options_en: ["<p>Mostly</p>", "<p>Another</p>", 
                                "<p>Farther</p>", "<p>Additional</p>"],
                    options_hi: ["<p>Mostly</p>", "<p>Another</p>",
                                "<p>Farther</p>", "<p>Additional</p>"],
                    solution_en: "<p>8.(b) another<br>&lsquo;Another&rsquo; means one more. The given passage states that another important aspect of Romantic poetry is its focus on the individual and the subjective experience. Hence &lsquo;another&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>8.(b) another<br>&lsquo;Another&rsquo; का अर्थ है एक और (one more)। दिए गए Passage में कहा गया है कि Romantic poetry का एक और महत्वपूर्ण पहलू (important aspect) इसका व्यक्ति (individual) एवं व्यक्तिपरक अनुभव (subjective experience) पर ध्यान केंद्रित करना है। अतः &lsquo;another&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "7",
                    question_en: "<p>9. <strong>Cloze Test:</strong><br>Romantic poetry is a literary movement that emerged in the late 18th century and continued through the mid-19th century. It is (5)________ by an emphasis on emotion, imagination and individualism. Romantic poets sought to express their innermost thoughts and feelings through their poetry, often exploring (6)_______ such as love, beauty and mortality. One of the defining features of Romantic poetry is its use of language and imagery to evoke powerful emotions in the reader. Many Romantic poets, such as William Wordsworth and Samuel Taylor Coleridge (7)________ that poetry should be written in the language of common people, rather than in the lofty, formal language of the past. They also incorporated vivid descriptions of nature and the natural world into their poetry, using it as a source of inspiration and spiritual renewal. (8)______ important aspect of Romantic poetry is its focus on the individual and the subjective experience. Romantic poets believed that each person\'s experience of the world was unique and valuable, and sought to capture this individuality in their writing. This led to a greater emphasis on personal expression and introspection in poetry, (9)_______ a rejection of the traditional poetic forms and structures of the past.<br>Select the most appropriate option to fill in blank number 9.</p>",
                    question_hi: "<p>9. <strong>Cloze Test:</strong><br>Romantic poetry is a literary movement that emerged in the late 18th century and continued through the mid-19th century. It is (5)________ by an emphasis on emotion, imagination and individualism. Romantic poets sought to express their innermost thoughts and feelings through their poetry, often exploring (6)_______ such as love, beauty and mortality. One of the defining features of Romantic poetry is its use of language and imagery to evoke powerful emotions in the reader. Many Romantic poets, such as William Wordsworth and Samuel Taylor Coleridge (7)________ that poetry should be written in the language of common people, rather than in the lofty, formal language of the past. They also incorporated vivid descriptions of nature and the natural world into their poetry, using it as a source of inspiration and spiritual renewal. (8)______ important aspect of Romantic poetry is its focus on the individual and the subjective experience. Romantic poets believed that each person\'s experience of the world was unique and valuable, and sought to capture this individuality in their writing. This led to a greater emphasis on personal expression and introspection in poetry, (9)_______ a rejection of the traditional poetic forms and structures of the past.<br>Select the most appropriate option to fill in blank number 9.</p>",
                    options_en: ["<p>whereas</p>", "<p>but</p>", 
                                "<p>therefore</p>", "<p>as well as</p>"],
                    options_hi: ["<p>whereas</p>", "<p>but</p>",
                                "<p>therefore</p>", "<p>as well as</p>"],
                    solution_en: "<p>9.(d) as well as<br>&lsquo;As well as&rsquo; is used to mention another item connected with the subject after conjunction &lsquo;and&rsquo;. The given passage states that this led to a greater emphasis on personal expression and introspection in poetry, as well as a rejection of the traditional poetic forms and structures of the past. Hence &lsquo;as well as&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>9.(d) as well as<br>&lsquo;As well as&rsquo; का प्रयोग Conjunction &lsquo;and&rsquo; के बाद Subject से जुड़ी किसी अन्य वस्तु (item) का उल्लेख करने के लिए किया जाता है। दिए गए Passage में कहा गया है कि इससे कविता में व्यक्तिगत अभिव्यक्ति (personal expression) तथा आत्ममंथन (introspection) पर अधिक जोर दिया गया, साथ ही Past के पारंपरिक काव्य रूपों (traditional poetic forms) और संरचनाओं को अस्वीकार कर दिया गया। अतः &lsquo;as well as&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "7",
                    question_en: "<p>10. Select the most appropriate ANTONYM of the given word.<br>Comical</p>",
                    question_hi: "<p>10. Select the most appropriate ANTONYM of the given word.<br>Comical</p>",
                    options_en: ["<p>Remedial</p>", "<p>Tragic</p>", 
                                "<p>Rebellious</p>", "<p>Farcical</p>"],
                    options_hi: ["<p>Remedial</p>", "<p>Tragic</p>",
                                "<p>Rebellious</p>", "<p>Farcical</p>"],
                    solution_en: "<p>10.(b) <strong>Tragic</strong>- very sad, often involving death and suffering.<br><strong>Comical</strong>- funny in a strange or silly way.<br><strong>Remedial</strong>- done to correct or improve something.<br><strong>Rebellious</strong>- resisting control or authority.<br><strong>Farcical</strong>- silly and not worth taking seriously.</p>",
                    solution_hi: "<p>10.(b) <strong>Tragic </strong>(दुखद) - very sad, often involving death and suffering.<br><strong>Comical </strong>(हास्यप्रद) - funny in a strange or silly way.<br><strong>Remedial </strong>(उपचारात्मक) - done to correct or improve something.<br><strong>Rebellious </strong>(विद्रोही) - resisting control or authority.<br><strong>Farcical </strong>(हास्यास्पद) - silly and not worth taking seriously.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "7",
                    question_en: "<p>11. Select the most appropriate option that can substitute the underlined words in the following sentence.<br>If you do hard work, you will have the <span style=\"text-decoration: underline;\">expected results.</span></p>",
                    question_hi: "<p>11. Select the most appropriate option that can substitute the underlined words in the following sentence.<br>If you do hard work, you will have the <span style=\"text-decoration: underline;\">expected results.</span></p>",
                    options_en: ["<p>bad results</p>", "<p>desired results</p>", 
                                "<p>required results</p>", "<p>good results</p>"],
                    options_hi: ["<p>bad results</p>", "<p>desired results</p>",
                                "<p>required results</p>", "<p>good results</p>"],
                    solution_en: "<p>11.(b) desired results<br>&lsquo;Desired result&rsquo; is the outcome or goal that one hopes to achieve. The given sentence states that if you do hard work, you will have the desired results. Hence, &lsquo;desired results&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>11.(b) desired results<br>&lsquo;Desired result&rsquo; वह परिणाम (outcome) या लक्ष्य (goal) है जिसे कोई व्यक्ति प्राप्त करने की आशा करता है। दिए गए Sentence में कहा गया है कि यदि आप कड़ी मेहनत (hard work) करते हैं, तो आपको वांछित परिणाम (desired results) मिलेंगे। अतः, &lsquo;desired results&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "7",
                    question_en: "<p>12. Select the most appropriate option that can substitute the underlined segment in the given sentence.<br>Planets in our solar system <span style=\"text-decoration: underline;\">close and big</span> for them to twinkle.</p>",
                    question_hi: "<p>12. Select the most appropriate option that can substitute the underlined segment in the given sentence.<br>Planets in our solar system <span style=\"text-decoration: underline;\">close and big</span> for them to twinkle.</p>",
                    options_en: ["<p>very close and big</p>", "<p>are quite close and big</p>", 
                                "<p>are too close and big</p>", "<p>is close and big</p>"],
                    options_hi: ["<p>very close and big</p>", "<p>are quite close and big</p>",
                                "<p>are too close and big</p>", "<p>is close and big</p>"],
                    solution_en: "<p>12.(c) are too close and big<br>&lsquo;Too + adjective + to + V<sub>1</sub>(twinkle)&rsquo; is the correct grammatical structure. &lsquo;Close&rsquo; and &lsquo;big&rsquo; are adjectives. Hence &lsquo;are too close and big&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>12.(c) are too close and big<br>&lsquo;Too + adjective + to + V<sub>1</sub>(twinkle)&rsquo; सही Grammatical structure है। &lsquo;Close&rsquo; तथा &lsquo;big&rsquo; Adjective हैं। अतः &lsquo;are too close and big&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "7",
                    question_en: "<p>13. Select the most appropriate meaning of the given idiom.<br>bread and butter</p>",
                    question_hi: "<p>13. Select the most appropriate meaning of the given idiom.<br>bread and butter</p>",
                    options_en: ["<p>An activity you do to get money for basic needs</p>", "<p>An activity you do to get help others</p>", 
                                "<p>An activity you do to improve your culinary skills</p>", "<p>An activity you do with determination</p>"],
                    options_hi: ["<p>An activity you do to get money for basic needs</p>", "<p>An activity you do to get help others</p>",
                                "<p>An activity you do to improve your culinary skills</p>", "<p>An activity you do with determination</p>"],
                    solution_en: "<p>13.(a) <strong>Bread and butter-</strong> an activity you do to get money for basic needs.<br>E.g.- Farming has been their bread and butter for generations.</p>",
                    solution_hi: "<p>13.(a) <strong>Bread and butter- </strong>an activity you do to get money for basic needs./एक गतिविधि जो आप बुनियादी जरूरतों के लिए धन प्राप्त करने के लिए करते हैं।<br>E.g.- Farming has been their bread and butter for generations.</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "7",
                    question_en: "<p>14. Select the most appropriate ANTONYM of the given word.<br>Skeptic</p>",
                    question_hi: "<p>14. Select the most appropriate ANTONYM of the given word.<br>Skeptic</p>",
                    options_en: ["<p>Atheist</p>", "<p>Careful</p>", 
                                "<p>Believer</p>", "<p>Infidel</p>"],
                    options_hi: ["<p>Atheist</p>", "<p>Careful</p>",
                                "<p>Believer</p>", "<p>Infidel</p>"],
                    solution_en: "<p>14.(c) <strong>Believe</strong>- to accept something as true.<br><strong>Skeptic</strong>- a person inclined to question or doubt accepted opinions.<br><strong>Atheist</strong>- one who does not believe in god<br><strong>Careful</strong>- paying attention and avoiding mistakes.<br><strong>Infidel</strong>- a person who has no religion.</p>",
                    solution_hi: "<p>14.(c) <strong>Believe </strong>(विश्वास) - to accept something as true.<br><strong>Skeptic </strong>(संशयवादी) - a person inclined to question or doubt accepted opinions.<br><strong>Atheist </strong>(नास्तिक) - one who does not believe in god<br><strong>Careful</strong> (सावधान) - paying attention and avoiding mistakes.<br><strong>Infidel </strong>(काफिर) - a person who has no religion.</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "7",
                    question_en: "<p>15. Select the most appropriate option to fill in the blank.<br>The bride had a big villa in comparison to the bridegroom&rsquo;s ______ house.</p>",
                    question_hi: "<p>15. Select the most appropriate option to fill in the blank.<br>The bride had a big villa in comparison to the bridegroom&rsquo;s ______ house.</p>",
                    options_en: ["<p>distinct</p>", "<p>different</p>", 
                                "<p>large</p>", "<p>small</p>"],
                    options_hi: ["<p>distinct</p>", "<p>different</p>",
                                "<p>large</p>", "<p>small</p>"],
                    solution_en: "<p>15.(d) <strong>Small</strong><br>The sentence is making a comparison between two villas. One villa is big, so the other villa will be comparatively small. Hence &lsquo;small&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>15.(d) <strong>Small</strong><br>दिया गया Sentence दो विला(villas) के बीच comparison कर रहा है। एक विला बड़ा है, इसलिए दूसरा विला तुलनात्मक रूप से छोटा होगा। अतः &lsquo;small&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "16",
                    section: "7",
                    question_en: "16. Select the most appropriate connotation to fill in the blank.<br />Raju is such a _________always prying into other people\'s business!",
                    question_hi: "16. Select the most appropriate connotation to fill in the blank.<br />Raju is such a _________always prying into other people\'s business!",
                    options_en: [" cheeky ", " stingy ", 
                                " perky ", " nosy"],
                    options_hi: [" cheeky ", " stingy ",
                                " perky ", " nosy"],
                    solution_en: "16.(d) Nosy<br />‘Nosy’ meaning over interested in other people’s business. The given sentence states that Raju is such a nosy always prying into other people\'s business. Hence ‘nosy’ is the most appropriate answer.",
                    solution_hi: "16.(d) Nosy<br />‘Nosy’ का अर्थ है दूसरों के काम में बहुत ज़्यादा दिलचस्पी लेना। दिए गए Sentence में कहा गया है कि राजू ऐसा नासमझ है जो हमेशा दूसरे लोगों के काम में ताक-झांक (prying) करता रहता है। अतः ‘nosy’ सबसे उपयुक्त उत्तर है।",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "17",
                    section: "7",
                    question_en: "<p>17. Select the most appropriate option that can substitute the underlined segment in the given sentence. If there is no need to substitute it, select &lsquo;No substitution required&rsquo;.<br>He asked his father when <span style=\"text-decoration: underline;\">would come the next letter.</span></p>",
                    question_hi: "<p>17. Select the most appropriate option that can substitute the underlined segment in the given sentence. If there is no need to substitute it, select &lsquo;No substitution required&rsquo;.<br>He asked his father when <span style=\"text-decoration: underline;\">would come the next letter.</span></p>",
                    options_en: ["<p>come the next letter</p>", "<p>the next letter would come</p>", 
                                "<p>No substitution required</p>", "<p>would the next letter come</p>"],
                    options_hi: ["<p>come the next letter</p>", "<p>the next letter would come</p>",
                                "<p>No substitution required</p>", "<p>would the next letter come</p>"],
                    solution_en: "<p>17.(b) the next letter would come<br>The correct structure of a clause is &lsquo;Subject + modal + main verb&rsquo;. &lsquo;The next letter&rsquo; is the subject of the clause, so it will be followed by modal &lsquo;would&rsquo; and the main verb &lsquo;come&rsquo;. Hence &lsquo;the next letter would come&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>17.(b) the next letter would come<br>&lsquo;Subject + modal + main verb&rsquo; एक Clause के लिए सही Structure है। &lsquo;The next letter&rsquo;, Clause का Subject है, इसलिए इसके बाद Modal &lsquo;would&rsquo; और Main verb &lsquo;come&rsquo; आएगा। अतः &lsquo;the next letter would come&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "18",
                    section: "7",
                    question_en: "<p>18. Select the most appropriate option that can substitute the underlined words in the given sentence.<br>Although I have a lot of work to do, I am determined to finish it all by the end of the day so that I can enjoy my weekend <span style=\"text-decoration: underline;\">not having any imminent due dates</span>.</p>",
                    question_hi: "<p>18. Select the most appropriate option that can substitute the underlined words in the given sentence.<br>Although I have a lot of work to do, I am determined to finish it all by the end of the day so that I can enjoy my weekend <span style=\"text-decoration: underline;\">not having any imminent due dates.</span></p>",
                    options_en: ["<p>with no pending reservations</p>", "<p>not with any pressure of due date</p>", 
                                "<p>not leaving any work incomplete</p>", "<p>without any looming deadlines</p>"],
                    options_hi: ["<p>with no pending reservations</p>", "<p>not with any pressure of due date</p>",
                                "<p>not leaving any work incomplete</p>", "<p>without any looming deadlines</p>"],
                    solution_en: "<p>18.(d) without any looming deadlines<br>&lsquo;Looming deadline\' means a deadline that is coming soon and must be met. &lsquo;Imminent due date&rsquo; means a due date that is about to happen very soon. Hence, &lsquo;without any looking deadlines&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>18.(d) without any looming deadlines<br>&lsquo;Looming deadline\' का अर्थ है निकट आती समय सीमा (deadline), जिसे पूरा किया जाना चाहिए। &lsquo;Imminent due date&rsquo; का अर्थ है एक नियत देय तिथि (due date) जो बहुत जल्द होने वाली है। अतः, &lsquo;without any looking deadlines&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "19",
                    section: "7",
                    question_en: "<p>19. Select the most appropriate option that can substitute the underlined word in the given sentence.<br>The professor did an <span style=\"text-decoration: underline;\">objective</span> evaluation.</p>",
                    question_hi: "<p>19. Select the most appropriate option that can substitute the underlined word in the given sentence.<br>The professor did an <span style=\"text-decoration: underline;\">objective</span> evaluation.</p>",
                    options_en: ["<p>unjust</p>", "<p>prejudiced</p>", 
                                "<p>unbiased</p>", "<p>unfair</p>"],
                    options_hi: ["<p>unjust</p>", "<p>prejudiced</p>",
                                "<p>unbiased</p>", "<p>unfair</p>"],
                    solution_en: "<p>19.(c) unbiased<br>&lsquo;Objective&rsquo; means not influenced by personal feelings or opinions. &lsquo;Unbiased&rsquo; means able to judge fairly because you are not influenced by your own opinions. Hence, &lsquo;unbiased&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>19.(c) unbiased<br>&lsquo;Objective&rsquo; का अर्थ है व्यक्तिगत भावनाओं (personal feelings) या राय (opinion) से प्रभावित न होना। &lsquo;Unbiased&rsquo; का अर्थ है निष्पक्ष रूप से(fairly) निर्णय लेने में सक्षम होना क्योंकि आप अपनी राय से प्रभावित नहीं होते। अतः, &lsquo;unbiased&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "20",
                    section: "7",
                    question_en: "<p>20. Which of the following sentences contains the ANTONYM of the word &lsquo;ignore&rsquo;?&nbsp;</p>",
                    question_hi: "<p>20. Which of the following sentences contains the ANTONYM of the word &lsquo;ignore&rsquo; ?</p>",
                    options_en: ["<p>To keep a tidy kitchen, do not neglect washing your dishes.</p>", "<p>Avoid foods which make you sick.</p>", 
                                "<p>Ritika recognised Kavita in the gathering.</p>", "<p>He did not succeed in gaining custody of his daughter.</p>"],
                    options_hi: ["<p>To keep a tidy kitchen, do not neglect washing your dishes.</p>", "<p>Avoid foods which make you sick.</p>",
                                "<p>Ritika recognised Kavita in the gathering.</p>", "<p>He did not succeed in gaining custody of his daughter.</p>"],
                    solution_en: "<p>20.(c) Ritika recognised Kavita in the gathering. <br><strong>Ignore</strong>- to deliberately pay no attention<br><strong>Recognised</strong>- to identify someone or something</p>",
                    solution_hi: "<p>20.(c) Ritika recognised Kavita in the gathering. <br><strong>Ignore </strong>(अनदेखा करना) - to deliberately pay no attention<br><strong>Recognised </strong>(पहचानना) - to identify someone or something</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "21",
                    section: "7",
                    question_en: "<p>21. Select the most appropriate idiomatic expression that can substitute the underlined segment in the given sentence.<br>Last year art critics praised Animesh as if he were a master, but he turned out to be a <span style=\"text-decoration: underline;\">pleasure for a short time.</span></p>",
                    question_hi: "<p>21. Select the most appropriate idiomatic expression that can substitute the underlined segment in the given sentence.<br>Last year art critics praised Animesh as if he were a master, but he turned out to be a <span style=\"text-decoration: underline;\">pleasure for a short time.</span></p>",
                    options_en: ["<p>a nine days&rsquo; wonder</p>", "<p>a bull in a china shop</p>", 
                                "<p>a house of cards</p>", "<p>a dish fit for Gods</p>"],
                    options_hi: ["<p>a nine days&rsquo; wonder</p>", "<p>a bull in a china shop</p>",
                                "<p>a house of cards</p>", "<p>a dish fit for Gods</p>"],
                    solution_en: "<p>21.(a) <strong>A nine day&rsquo;s wonder-</strong> pleasure for a short time.</p>",
                    solution_hi: "<p>21.(a) <strong>A nine day&rsquo;s wonder-</strong> pleasure for a short time./क्षणिक सुख।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "22",
                    section: "7",
                    question_en: "22. Select the INCORRECTLY spelt word.",
                    question_hi: "22. Select the INCORRECTLY spelt word.",
                    options_en: [" Accurate ", "  Mantain", 
                                "  Excellence", " Aquarium"],
                    options_hi: [" Accurate ", "  Mantain",
                                "  Excellence", " Aquarium"],
                    solution_en: "22.(b) Mantain<br />‘Maintain’ is the correct spelling",
                    solution_hi: "22.(b) Mantain<br />‘Maintain’ सही spelling है। ",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "23",
                    section: "7",
                    question_en: "23. Identify the incorrectly spelt word and select its correct spelling.<br />It is his previlage to present all the candidates for ordination to the bishop of the diocese.",
                    question_hi: "23. Identify the incorrectly spelt word and select its correct spelling.<br />It is his previlage to present all the candidates for ordination to the bishop of the diocese.",
                    options_en: [" odination ", " deocease ", 
                                "  privilege", " prevalage"],
                    options_hi: [" odination ", " deocease ",
                                "  privilege", " prevalage"],
                    solution_en: "23.(c) previlage<br />‘Privilege’ is the correct spelling",
                    solution_hi: "23.(c) previlage<br />‘Privilege’ सही spelling है। ",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "24",
                    section: "7",
                    question_en: "<p>24. Select the most appropriate ANTONYM of the word given in brackets to fill in the blank.<br>Her voice, soft and ________, (shrill) flowed effortlessly through the room.</p>",
                    question_hi: "<p>24. Select the most appropriate ANTONYM of the word given in brackets to fill in the blank.<br>Her voice, soft and ________, (shrill) flowed effortlessly through the room.</p>",
                    options_en: ["<p>croaky</p>", "<p>nasal</p>", 
                                "<p>piercing</p>", "<p>mellow</p>"],
                    options_hi: ["<p>croaky</p>", "<p>nasal</p>",
                                "<p>piercing</p>", "<p>mellow</p>"],
                    solution_en: "<p>24.(d) <strong>Mellow</strong>- smooth and soft in tone.<br><strong>Shrill</strong>- high-pitched and piercing in sound.<br><strong>Croaky</strong>- deep and hoarse in tone.<br><strong>Nasal</strong>- sounding as if spoken through the nose.<br><strong>Piercing-</strong> sharp and intense in sound.</p>",
                    solution_hi: "<p>24.(d) <strong>Mellow </strong>(मधुर) - smooth and soft in tone.<br><strong>Shrill</strong> (तीक्ष्ण) - high-pitched and piercing in sound.<br><strong>Croaky</strong> (कर्कश) - deep and hoarse in tone.<br><strong>Nasal</strong> (अनुनासिक) - sounding as if spoken through the nose.<br><strong>Piercing</strong> (तीक्ष्ण) - sharp and intense in sound.</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "25",
                    section: "6",
                    question_en: "<p>25. Select the most appropriate ANTONYM for the underlined word in the given sentence.<br>As a punishment for his <span style=\"text-decoration: underline;\">betrayal</span> to his country, the spy who sold secrets was sent to prison.</p>",
                    question_hi: "<p>25. Select the most appropriate ANTONYM for the underlined word in the given sentence.<br>As a punishment for his <span style=\"text-decoration: underline;\">betrayal</span> to his country, the spy who sold secrets was sent to prison.</p>",
                    options_en: ["<p>Perilous</p>", "<p>Infidelity</p>", 
                                "<p>Nasty</p>", "<p>Loyalty</p>"],
                    options_hi: ["<p>Perilous</p>", "<p>Infidelity</p>",
                                "<p>Nasty</p>", "<p>Loyalty</p>"],
                    solution_en: "<p>25.(d) <strong>Loyalty</strong>- a strong feeling of support.<br><strong>Betrayal</strong>- the act of being disloyal.<br><strong>Perilous</strong>- full of danger or risk.<br><strong>Infidelity-</strong> unfaithfulness to a partner or commitment.<br><strong>Nasty-</strong> unpleasant or offensive.</p>",
                    solution_hi: "<p>25.(d) <strong>Loyalty </strong>(वफ़ादारी) - a strong feeling of support.<br><strong>Betrayal</strong> (विश्वासघात) - the act of being disloyal.<br><strong>Perilous</strong> (संकटपूर्ण/ख़तरनाक) - full of danger or risk.<br><strong>Infidelity</strong> (विश्वासघात) - unfaithfulness to a partner or commitment.<br><strong>Nasty</strong> (दुष्ट/बुरा) - unpleasant or offensive.</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "26",
                    section: "6",
                    question_en: "26. In a certain code language, ‘AUDIO’ is coded as ‘59416’ and ‘BEND’ is coded as ‘6238’. How is ‘D’ coded in that language?",
                    question_hi: "26. एक निश्चित कूट भाषा में, ‘AUDIO’ को ‘59416’ लिखा जाता है और ‘BEND’ को ‘6238’ लिखा जाता है। उसी कूट भाषा में ‘D’ को कैसे लिखा जाएगा?",
                    options_en: [" 1 ", " 2", 
                                " 6", " 4"],
                    options_hi: [" 1 ", " 2",
                                " 6", " 4"],
                    solution_en: "26.(c) AUDIO → 59416…….(i)<br />                 BEND → 6238…….(ii)<br />From (i) and (ii) ‘D’ and ‘6’ are common. The code of ‘D’ = ‘6’.",
                    solution_hi: "26.(c)  AUDIO → 59416…….(i)<br />                   BEND → 6238…….(ii)<br />(i) और (ii) से \'D\' और \'6\' उभयनिष्ठ हैं। \'D\' का कोड = \'6\'.",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "27",
                    section: "6",
                    question_en: "<p>27. Select the correct mirror image of the given figure when the mirror is placed at MN as shown below.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734509792738.png\" alt=\"rId6\" width=\"127\" height=\"110\"></p>",
                    question_hi: "<p>27. दी गई आकृति के उस सही दर्पण प्रतिबिंब को चुनिए, जो नीचे दर्शाए गए अनुसार दर्पण को MN पर रखने पर बनेगा।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734509792738.png\" alt=\"rId6\" width=\"127\" height=\"110\"></p>",
                    options_en: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734509792844.png\" alt=\"rId7\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734509792958.png\" alt=\"rId8\"></p>", 
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734509793070.png\" alt=\"rId9\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734509793177.png\" alt=\"rId10\"></p>"],
                    options_hi: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734509792844.png\" alt=\"rId7\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734509792958.png\" alt=\"rId8\"></p>",
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734509793070.png\" alt=\"rId9\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734509793177.png\" alt=\"rId10\"></p>"],
                    solution_en: "<p>27.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734509792844.png\" alt=\"rId7\"></p>",
                    solution_hi: "<p>27.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734509792844.png\" alt=\"rId7\"></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "28",
                    section: "6",
                    question_en: "<p>28. Select the set in which the numbers are related in the same way as are the numbers of the following sets.<br>(<strong>NOTE : </strong>Operations should be performed on the whole numbers, without breaking down the numbers into its constituent digits. E.g. 13 &ndash; Operations on 13 such as adding/subtracting/multiplying etc. to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is not allowed.)<br>(12, 132, 11)<br>(13, 104, 8)</p>",
                    question_hi: "<p>28. उस समुच्चय का चयन कीजिए जिसमें संख्याएं ठीक उसी प्रकार संबंधित हैं जिस प्रकार दिए गए समुच्चयों की संख्याएं संबंधित हैं।<br>(<strong>ध्यान दें :</strong> संख्याओं को उनके घटक अंकों में अलग-अलग किए बिना पूर्ण संख्याओं पर संक्रियाएं की जानी चाहिए। उदाहरण के लिए संख्या 13 - संख्या 13 पर संक्रियाएं जैसे 13 को जोड़ना/घटाना/गुणा करना आदि किया जा सकता है। 13 को 1 और 3 में अलग-अलग करने की और फिर 1 और 3 पर गणितीय संक्रियाएं करने की अनुमति नहीं है।)<br>(12, 132, 11)<br>(13, 104, 8)</p>",
                    options_en: ["<p>(7, 105, 15)</p>", "<p>(9, 128, 14)</p>", 
                                "<p>(16, 146, 9)</p>", "<p>(18, 132, 7)</p>"],
                    options_hi: ["<p>(7, 105, 15)</p>", "<p>(9, 128, 14)</p>",
                                "<p>(16, 146, 9)</p>", "<p>(18, 132, 7)</p>"],
                    solution_en: "<p>28.(a)<strong> Logic :- </strong>(1st number &times; 3rd number) = (2nd number)<br>(12, 132, 11) :- (12 &times; 11) = 132<br>(13, 104, 8) :- (13 &times; 8) = 104<br>Similarly,<br>(7, 105,15 ) :- (7 &times; 15) = 105</p>",
                    solution_hi: "<p>28.(a) <strong>तर्क :-</strong> (पहली संख्या &times; तीसरी संख्या) = (दूसरी संख्या)<br>(12, 132, 11) :- (12 &times; 11) = 132<br>(13, 104, 8) :- (13 &times; 8) = 104<br>इसी प्रकार,<br>(7, 105,15 ) :- (7 &times; 15) = 105</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "29",
                    section: "6",
                    question_en: "<p>29. Which two numbers should be interchanged to make the given equation correct ?<br>136 &minus; (176 &divide; 11) &times; 8 + 22 &times; 3 = 105<br>(<strong>NOTE :</strong> Numbers must be interchanged and not the constituent digits e.g. if 2 and 3 are to be interchanged in the equation 43 &times; 3 + 4 &divide; 2, then interchanged equation is 43 &times; 2 + 4 &divide; 3)</p>",
                    question_hi: "<p>29. दिए गए समीकरण को सही बनाने के लिए किन दो संख्याओं को आपस में बदला जाना चाहिए ?<br>136 &minus; (176 &divide; 11) &times; 8 + 22 &times; 3 = 105<br>(<strong>ध्यान दें : </strong>संख्याओं को आपस में बदला जाना चाहिए और घटक अंकों को नहीं बदला जाना चाहिए। उदा. यदि समीकरण 43 &times; 3 + 4 &divide; 2 में 2 और 3 को आपस में बदलना है, तो बदला हुआ समीकरण 43 &times; 2 + 4 &divide; 3 होगा)</p>",
                    options_en: ["  8 and 11", " 11 and 22 ", 
                                "  8 and 3", " 22 and 176"],
                    options_hi: ["  8 और 11", " 11 और 22 ",
                                "  8 और 3", " 22 और 176"],
                    solution_en: "<p>29.(b) <strong>Given :-</strong> 136 - (176 <math display=\"inline\"><mo>&#247;</mo></math> 11) &times; 8 + 22 &times; 3 = 105<br>After going through all the options, option (b) satisfies. After interchanging 11 and 22 we get<br>136 - (176 <math display=\"inline\"><mo>&#247;</mo></math> 22) &times; 8 + 11 &times; 3<br>136 - (8) &times; 8 + 33<br>136 - 64 + 33 <br>136 - 31 = 105<br>L.H.S. = R.H.S.</p>",
                    solution_hi: "<p>29.(b) <strong>दिया गया :- </strong>136 - (176 11) &times; 8 + 22 &times; 3 = 105<br>सभी विकल्पों की जांच करने पर विकल्प (b) संतुष्ट करता है। 11 और 22 को आपस में बदलने पर हमें प्राप्त होता है<br>136 - (176 <math display=\"inline\"><mo>&#247;</mo></math> 22) &times; 8 + 11 &times; 3<br>136 - (8) &times; 8 + 33<br>136 - 64 + 33 <br>136 - 31 = 105<br>L.H.S. = R.H.S.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "30",
                    section: "6",
                    question_en: "<p>30. What should come in place of the question mark (?) in the given series? <br>23, 27, 35, 47, 63, ?</p>",
                    question_hi: "<p>30. दी गई श्रृंखला में प्रश्न चिह्न (?) के स्थान पर क्या आना चाहिए ?<br>23, 27, 35, 47, 63, ?</p>",
                    options_en: ["<p>85</p>", "<p>82</p>", 
                                "<p>84</p>", "<p>83</p>"],
                    options_hi: ["<p>85</p>", "<p>82</p>",
                                "<p>84</p>", "<p>83</p>"],
                    solution_en: "<p>30.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734509793278.png\" alt=\"rId11\" width=\"297\" height=\"83\"></p>",
                    solution_hi: "<p>30.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734509793278.png\" alt=\"rId11\" width=\"297\" height=\"83\"></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "31",
                    section: "6",
                    question_en: "<p>31. 14 is related to 126 following a certain logic. Following the same logic, 21 is related to 189. To which of the following is 32 related following the same logic?<br>(<strong>NOTE : </strong>Operations should be performed on the whole numbers, without breaking down the numbers into its constituent digits. E.g. 13 &ndash; Operations on 13 such as adding /deleting /multiplying etc., to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is not allowed.)</p>",
                    question_hi: "<p>31. एक निश्चित तर्क का अनुसरण करते हुए 14, 126 से संबंधित है। उसी तर्क का अनुसरण करते हुए 21, 189 से संबंधित है। उसी तर्क का अनुसरण करते हुए, 32 निम्नलिखित में से किससे संबंधित है ?<br>(<strong>ध्यान दें : </strong>संख्याओं को उनके घटक अंकों में अलग-अलग किए बिना पूर्ण संख्याओं पर संक्रियाएं की जानी चाहिए। उदाहरण के लिए संख्या 13 - संख्या 13 पर संक्रियाएं जैसे 13 को जोड़ना/घटाना/गुणा करना आदि किया जा सकता है। 13 को 1 और 3 में अलग-अलग करने की और फिर 1 और 3 पर गणितीय संक्रियाएं करने की अनुमति नहीं है।)</p>",
                    options_en: ["<p>320</p>", "<p>286</p>", 
                                "<p>318</p>", "<p>288</p>"],
                    options_hi: ["<p>320</p>", "<p>286</p>",
                                "<p>318</p>", "<p>288</p>"],
                    solution_en: "<p>31.(d) <strong>Logic :-</strong> (1st number &times; 9) = 2nd number<br>(14 ,126) :- (14 &times; 9) = 126<br>(21, 189) :- (21 &times; 9) = 189<br>Similarly,<br>(32, ?) :- (32 &times; 9) = 288</p>",
                    solution_hi: "<p>31.(d) <strong>तर्क :-</strong> (पहली संख्या &times; 9) = दूसरी संख्या<br>(14 ,126) :- (14 &times; 9) = 126<br>(21, 189) :- (21 &times; 9) = 189<br>इसी प्रकार,<br>(32, ?) :- (32 &times; 9) = 288</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "32",
                    section: "6",
                    question_en: "<p>32. What should come in place of? in the given series based on the English alphabetical order ?<br>TMF UNE VOD ? XQB</p>",
                    question_hi: "<p>32. अंग्रेजी वर्णमाला क्रम के आधार पर दी गई श्रृंखला में ?\' के स्थान पर क्या आना चाहिए ?<br>TMF UNE VOD ? XQB</p>",
                    options_en: ["<p>WPC</p>", "<p>ACE</p>", 
                                "<p>WPI</p>", "<p>KOT</p>"],
                    options_hi: ["<p>WPC</p>", "<p>ACE</p>",
                                "<p>WPI</p>", "<p>KOT</p>"],
                    solution_en: "<p>32.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734509793408.png\" alt=\"rId12\" width=\"326\" height=\"108\"></p>",
                    solution_hi: "<p>32.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734509793408.png\" alt=\"rId12\" width=\"326\" height=\"108\"></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "33",
                    section: "6",
                    question_en: "<p>33. Select the word-pair that best represents a similar relationship to the one expressed in the pair of words given below.<br>(The words must be considered as meaningful English words and must not be related to each other based on the number of letters/number of consonants/vowels in the word.)<br>Light : Blind</p>",
                    question_hi: "<p>33. उस शब्द -युग्म का चयन कीजिए जो नीचे दिए गए शब्दों के युग्म में व्यक्त किए गए समान संबंध का सबसे अच्छा निरूपण करता है।<br>(शब्दों को अर्थपूर्ण हिंदी शब्दों के रूप में माना जाना चाहिए और उन्हें शब्द में अक्षरों की संख्या /व्यंजन/ स्वरों की संख्या के आधार पर एक दूसरे से संबंधित नहीं किया जाना चाहिए।)<br>प्रकाश : अंधा</p>",
                    options_en: ["<p>Tongue : Sound</p>", "<p>Voice : Vibration</p>", 
                                "<p>Speech : Dumb</p>", "<p>Language : Deaf</p>"],
                    options_hi: ["<p>जीभ : ध्वनि</p>", "<p>आवाज : कंपन</p>",
                                "<p>संवाद : गूंगा</p>", "<p>भाषा : बहरा</p>"],
                    solution_en: "<p>33.(c) As the Blind can&rsquo;t see the Light, similarly the Dumb can&rsquo;t do Speech.</p>",
                    solution_hi: "<p>33.(c) जैसे अंधा प्रकाश नहीं देख सकता, वैसे ही गूंगा संवाद नहीं कर सकता।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "34",
                    section: "6",
                    question_en: "<p>34. What would be the word on the opposite side of \'Feb\' if the given sheet is folded to form a cube ?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734509793548.png\" alt=\"rId13\" width=\"142\" height=\"170\"></p>",
                    question_hi: "<p>34. यदि नीचे दी गई शीट को मोड़कर एक घन बनाया जाए तो Feb के विपरीत फलक पर कौन-सा शब्द होगा ?&nbsp;<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734509793548.png\" alt=\"rId13\" width=\"142\" height=\"170\"></p>",
                    options_en: ["<p>Jan</p>", "<p>June</p>", 
                                "<p>May</p>", "<p>Mar</p>"],
                    options_hi: ["<p>Jan</p>", "<p>June</p>",
                                "<p>May</p>", "<p>Mar</p>"],
                    solution_en: "<p>34.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734509793659.png\" alt=\"rId14\" width=\"199\" height=\"250\"><br><strong>The opposite faces are :-</strong> March &harr; April, May &harr; February , Jan &harr; June.</p>",
                    solution_hi: "<p>34.(c) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734509793659.png\" alt=\"rId14\" width=\"199\" height=\"250\"><br><strong>विपरीत फलक हैं:-</strong> March &harr; April, May &harr; February , Jan &harr; June.</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "35",
                    section: "6",
                    question_en: "<p>35. What would be the word on the opposite side of \'Kiwi\' if the given sheet is folded to form a cube ?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734509793854.png\" alt=\"rId15\" width=\"156\" height=\"185\"></p>",
                    question_hi: "<p>35. यदि नीचे दी गई शीट को मोड़कर एक घन बनाया जाए तो Kiwi\' शब्द के विपरीत फलक पर कौन-सा शब्द होगा ?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734509793854.png\" alt=\"rId15\" width=\"156\" height=\"185\"></p>",
                    options_en: ["<p>Melon</p>", "<p>Plum</p>", 
                                "<p>Lime</p>", "<p>Mango</p>"],
                    options_hi: ["<p>Melon</p>", "<p>Plum</p>",
                                "<p>Lime</p>", "<p>Mango</p>"],
                    solution_en: "<p>35.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734509793983.png\" alt=\"rId16\" width=\"165\" height=\"216\"><br><strong>The opposite faces are :-</strong> Plum &harr; Pear, Mango &harr; Kiwi , Lime &harr; Melon</p>",
                    solution_hi: "<p>35.(d) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734509793983.png\" alt=\"rId16\" width=\"165\" height=\"216\"><br><strong>विपरीत फलक हैं:-</strong> Plum &harr; Pear, Mango &harr; Kiwi , Lime &harr; Melon</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "36",
                    section: "6",
                    question_en: "<p>36. &lsquo;A &divide; B&rsquo; means &lsquo;A is B&rsquo;s son&rsquo;.<br>&lsquo;A &times; B&rsquo; means &lsquo;A is B&rsquo;s sister&rsquo;.<br>&lsquo;A + B&rsquo; means &lsquo;A is B&rsquo;s brother&rsquo;.<br>&lsquo;A &ndash; B&rsquo; means &lsquo;A is B&rsquo;s mother&rsquo;.<br>Using the mathematical operators meaning the same as given above, which of the following means that &lsquo;S is T&rsquo;s husband&rsquo; ?</p>",
                    question_hi: "<p>36. A &divide; B\' का अर्थ है \'A, B का पुत्र है\'।<br>\'A &times; B\' का अर्थ है \'A, B की बहन है\'।<br>\'A + B\' का अर्थ है \'A, B का भाई है\'।<br>\'A - B\' का अर्थ है \'A, B की माँ है\'।<br>ऊपर दिए गए गणितीय संकारकों के समान अर्थ का उपयोग करते हुए ज्ञात करें कि निम्नलिखित में से किसका अर्थ हैकि \'S, T का पति है\' ?</p>",
                    options_en: ["<p>T &ndash; R + V &divide; S</p>", "<p>T &divide; R &times; V + S</p>", 
                                "<p>T &ndash; R &divide; V &times; S</p>", "<p>T &times; R &ndash; V + S</p>"],
                    options_hi: ["<p>T &ndash; R + V &divide; S</p>", "<p>T &divide; R &times; V + S</p>",
                                "<p>T &ndash; R &divide; V &times; S</p>", "<p>T &times; R &ndash; V + S</p>"],
                    solution_en: "<p>36.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734509794096.png\" alt=\"rId17\" width=\"138\" height=\"137\"><br>S is T&rsquo;s husband.</p>",
                    solution_hi: "<p>36.(a) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734509794096.png\" alt=\"rId17\" width=\"138\" height=\"137\"><br>S, T का पति है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "37",
                    section: "6",
                    question_en: "<p>37. Select the correct mirror image of the given figure when the mirror is placed at MN.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734509794219.png\" alt=\"rId18\" width=\"113\" height=\"123\"></p>",
                    question_hi: "<p>37. दर्पण को MN पर रखे जाने पर दी गई आकृति का दर्पण में नि&zwj;र्मित सही प्रतिबिंब का चयन करें।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734509794219.png\" alt=\"rId18\" width=\"113\" height=\"123\"></p>",
                    options_en: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734509794336.png\" alt=\"rId19\" width=\"85\" height=\"92\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734509794449.png\" alt=\"rId20\" width=\"84\" height=\"81\"></p>", 
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734509794560.png\" alt=\"rId21\" width=\"86\" height=\"82\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734509794651.png\" alt=\"rId22\" width=\"85\" height=\"88\"></p>"],
                    options_hi: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734509794336.png\" alt=\"rId19\" width=\"84\" height=\"91\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734509794449.png\" alt=\"rId20\" width=\"85\" height=\"82\"></p>",
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734509794560.png\" alt=\"rId21\" width=\"85\" height=\"81\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734509794651.png\" alt=\"rId22\" width=\"85\" height=\"88\"></p>"],
                    solution_en: "<p>37.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734509794336.png\" alt=\"rId19\" width=\"85\" height=\"92\"></p>",
                    solution_hi: "<p>37.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734509794336.png\" alt=\"rId19\" width=\"85\" height=\"92\"></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "38",
                    section: "6",
                    question_en: "<p>38. What will come in the place of the question mark (?) in the following equation, if &lsquo;+&rsquo; and &lsquo;-&rsquo; are interchanged and &lsquo;&times;&rsquo; and &lsquo;&divide;&rsquo; are interchanged ?<br>13 &divide; 3 + 2 &times; 2 - 11 = ?</p>",
                    question_hi: "<p>38. यदि &lsquo;+&rsquo; और &rsquo;-&rsquo; को आपस में बदल दिया जाए तथा \'&times;\' और \'&divide;\' को आपस में बदल दिया जाए तो निम्नलिखित समीकरण में \'?\' के स्थान पर क्या आएगा ?<br>13 &divide; 3 + 2 &times; 2 - 11 = ?</p>",
                    options_en: ["<p>59</p>", "<p>39</p>", 
                                "<p>29</p>", "<p>49</p>"],
                    options_hi: ["<p>59</p>", "<p>39</p>",
                                "<p>29</p>", "<p>49</p>"],
                    solution_en: "<p>38.(d) <strong>Given :-</strong> 13 <math display=\"inline\"><mo>&#247;</mo></math> 3 + 2 &times; 2 - 11<br>As per given instruction after interchanging &lsquo;+&rsquo; and &lsquo;-&rsquo; and &lsquo;&times;&rsquo; and &lsquo;<math display=\"inline\"><mo>&#247;</mo></math>&rsquo; we get<br>13 &times; 3 - 2 <math display=\"inline\"><mo>&#247;</mo></math> 2 + 11<br>39 - 1 + 11 = 49</p>",
                    solution_hi: "<p>38.(d) <strong>दिया गया :-</strong> 13 <math display=\"inline\"><mo>&#247;</mo></math> 3 + 2 &times; 2 - 11<br>दिए गए निर्देश के अनुसार \'+\' और \'-\' तथा \'&times;\' और \'<math display=\"inline\"><mo>&#247;</mo></math>\' को आपस में बदलने पर हमें प्राप्त होता है<br>13 &times; 3 - 2 <math display=\"inline\"><mo>&#247;</mo></math> 2 + 11<br>39 - 1 + 11 = 49</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "39",
                    section: "6",
                    question_en: "<p>39. Three of the following four are alike in a certain way and thus form a group. Which is the one that does NOT belong to that group ?<br>(<strong>Note : </strong>The odd one out is not based on the number of consonants/vowels or their position in the letter cluster.)</p>",
                    question_hi: "<p>39. निम्नलिखित चार अक्षर-समूहों में से तीन अक्षर-समूह एक निश्चित प्रकार से समान हैं और इस प्रकार एक समूह बनाते हैं। वह कौन-सा अक्षर-समूह है जो उस समूह से संबंधित नहीं है ?<br>(<strong>ध्यान दें :</strong> असंगत अक्षर - समूह, उस अक्षर - समूह में व्यंजनों/स्वरों की संख्या या उनके स्थान पर आधारित नहीं है।)</p>",
                    options_en: ["<p>DGJ</p>", "<p>HKN</p>", 
                                "<p>PSU</p>", "<p>TWZ</p>"],
                    options_hi: ["<p>DGJ</p>", "<p>HKN</p>",
                                "<p>PSU</p>", "<p>TWZ</p>"],
                    solution_en: "<p>39.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734509794759.png\" alt=\"rId23\" width=\"110\" height=\"84\"> , <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734509794860.png\" alt=\"rId24\" width=\"113\" height=\"82\">&nbsp; , <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734509794969.png\" alt=\"rId25\" width=\"122\" height=\"81\"><br>but,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734509795089.png\" alt=\"rId26\" width=\"112\" height=\"84\"></p>",
                    solution_hi: "<p>39.(c) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734509794759.png\" alt=\"rId23\" width=\"110\" height=\"84\"> , <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734509794860.png\" alt=\"rId24\" width=\"113\" height=\"82\">&nbsp; , <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734509794969.png\" alt=\"rId25\" width=\"122\" height=\"81\"><br>लेकिन, <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734509795089.png\" alt=\"rId26\" width=\"112\" height=\"84\"></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "40",
                    section: "6",
                    question_en: "<p>40. YZWT is related to DEAX in a certain way based on the English alphabetical order. In the same way, QROL is related to VWSP. To which of the following is RSPM related, following the same logic ?</p>",
                    question_hi: "<p>40. अंग्रेजी वर्णमाला क्रम केआधार पर YZWT, DEAX से एक निश्चित प्रकार से संबंधित है। उसी प्रकार, QROL, VWSP से संबंधित है। समान तर्क का अनुसरण करते हुए, RSPM निम्नलिखित में से किससे संबंधित है ?</p>",
                    options_en: ["<p>WXUQ</p>", "<p>WXTQ</p>", 
                                "<p>WYUQ</p>", "<p>WTXQ</p>"],
                    options_hi: ["<p>WXUQ</p>", "<p>WXTQ</p>",
                                "<p>WYUQ</p>", "<p>WTXQ</p>"],
                    solution_en: "<p>40.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734509795336.png\" alt=\"rId27\" width=\"136\" height=\"101\">&nbsp; , <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734509795440.png\" alt=\"rId28\" width=\"135\" height=\"103\"><br>Similarly,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734509795537.png\" alt=\"rId29\" width=\"139\" height=\"111\"></p>",
                    solution_hi: "<p>40.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734509795336.png\" alt=\"rId27\" width=\"136\" height=\"101\">&nbsp; , <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734509795440.png\" alt=\"rId28\" width=\"135\" height=\"103\"><br>इसी प्रकार,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734509795537.png\" alt=\"rId29\" width=\"139\" height=\"111\"></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "41",
                    section: "6",
                    question_en: "<p>41. How many triangles are there in the given figure ?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734509795627.png\" alt=\"rId30\" width=\"127\" height=\"117\"></p>",
                    question_hi: "<p>41. दी गई आकृति में कितने त्रिभुज हैं ?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734509795627.png\" alt=\"rId30\" width=\"127\" height=\"117\"></p>",
                    options_en: ["<p>15</p>", "<p>16</p>", 
                                "<p>13</p>", "<p>14</p>"],
                    options_hi: ["<p>15</p>", "<p>16</p>",
                                "<p>13</p>", "<p>14</p>"],
                    solution_en: "<p>41.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734509795746.png\" alt=\"rId31\" width=\"144\" height=\"143\"><br>There are 15 triangle<br>ABC , BDC, CDE, CFI , IFH, FGH,CFO, CFH, KIN, LMN, IHN, EJI, ACD, CKI , JIL</p>",
                    solution_hi: "<p>41.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734509795746.png\" alt=\"rId31\" width=\"144\" height=\"143\"><br>15 त्रिभुज हैं<br>ABC , BDC, CDE, CFI , IFH, FGH,CFO, CFH, KIN, LMN, IHN, EJI, ACD, CKI , JIL</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "42",
                    section: "6",
                    question_en: "<p>42. If 15 August 2006 is a Tuesday, then what will the day of the week on 7 September 2017 ?</p>",
                    question_hi: "<p>42. यदि 15 अगस्त 2006 को मंगलवार है, तो 7 सितम्बर 2017 को सप्ताह का कौन-सा दिन होगा ?</p>",
                    options_en: ["<p>Wednesday</p>", "<p>Monday</p>", 
                                "<p>Thursday</p>", "<p>Sunday</p>"],
                    options_hi: ["<p>बुधवार</p>", "<p>सोमवार</p>",
                                "<p>गुरुवार</p>", "<p>रविवार</p>"],
                    solution_en: "<p>42.(c) 15 August 2006 is Tuesday. On moving to 2017 the number of odd days = <br>+1 + 2 + 1 + 1 + 1 + 2 + 1 +1 + 1 +2 + 1 = 14. We have reached till 15 August 2017 but we have to reach till 7 September , the number of days in between = 23. Total number of days = 23 + 14 = 37. On dividing 37 by 7, the remainder = 2. Tuesday + 2 = Thursday.</p>",
                    solution_hi: "<p>42.(c) 15 अगस्त 2006 को मंगलवार है. 2017 में जाने पर विषम दिनों की संख्या = <br>+1 + 2 + 1 + 1 + 1 + 2 + 1 +1 + 1 +2 + 1 = 14. हम 15 अगस्त 2017 तक पहुंच गए हैं लेकिन हमें 7 सितंबर तक पहुंचना है, बीच में दिनों की संख्या = 23. दिनों की कुल संख्या = 23 + 14 = 37, 37 को 7 से विभाजित करने पर शेषफल = 2, मंगलवार + 2 = गुरुवार।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "43",
                    section: "6",
                    question_en: "<p>43. What will come in the place of the question mark (?) in the following equation if \'+\' and \'&divide;\' are interchanged and \'&times;\' and \'-\' are interchanged ?&nbsp;<br>39 + 13 &times; 12 &divide; 5 - 4 = ?</p>",
                    question_hi: "<p>43. निम्नलिखित समीकरण में प्रश्न चिह्न (?) के स्थान पर क्या आयेगा यदि &lsquo;+&rsquo; और \'&divide;\' को आपस में बदल दिया जाए और \'&times;\' और \'-\' को आपस में बदल दिया जाए ?<br>39 + 13 &times; 12 &divide; 5 - 4 = ?</p>",
                    options_en: ["<p>7</p>", "<p>11</p>", 
                                "<p>9</p>", "<p>13</p>"],
                    options_hi: ["<p>7</p>", "<p>11</p>",
                                "<p>9</p>", "<p>13</p>"],
                    solution_en: "<p>43.(b)<strong> Given :-</strong> 39 + 13 &times; 12 <math display=\"inline\"><mo>&#247;</mo></math> 5 - 4<br>As per given instruction after interchanging the &lsquo;+&rsquo; and &lsquo;<math display=\"inline\"><mo>&#247;</mo></math>&rsquo; and &lsquo;-&rsquo; and &lsquo;&times;&rsquo; we get<br>39 <math display=\"inline\"><mo>&#247;</mo></math> 13 - 12 + 5 &times; 4<br>3 - 12 + 20 = 11</p>",
                    solution_hi: "<p>43.(b)<strong> दिया गया :- </strong>39 + 13 &times; 12<math display=\"inline\"><mo>&#247;</mo></math> 5 - 4<br>दिए गए निर्देश के अनुसार \'+\' और \'<math display=\"inline\"><mo>&#247;</mo></math>\' तथा \'-\' और \'&times;\' को आपस में बदलने के बाद हमें प्राप्त होता है<br>39 <math display=\"inline\"><mo>&#247;</mo></math> 13 - 12 + 5 &times; 4<br>3 - 12 + 20 = 11</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "44",
                    section: "6",
                    question_en: "<p>44. Identify the figure given in the options that when put in place of the question mark (?) will logically complete the series.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734509795864.png\" alt=\"rId32\" width=\"315\" height=\"76\"></p>",
                    question_hi: "<p>44. विकल्पों में दी गई उस आकृति को पहचानिए जिसे प्रश्न चिह्न (?) के स्थान पर रखने पर श्रृंखला तार्किक रूप से पूरी हो जाएगी।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734509795864.png\" alt=\"rId32\" width=\"315\" height=\"76\"></p>",
                    options_en: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734509796033.png\" alt=\"rId33\" width=\"86\" height=\"78\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734509796194.png\" alt=\"rId34\" width=\"78\" height=\"75\"></p>", 
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734509796298.png\" alt=\"rId35\" width=\"82\" height=\"78\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734509796391.png\" alt=\"rId36\" width=\"89\" height=\"81\"></p>"],
                    options_hi: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734509796033.png\" alt=\"rId33\" width=\"82\" height=\"75\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734509796194.png\" alt=\"rId34\" width=\"82\" height=\"79\"></p>",
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734509796298.png\" alt=\"rId35\" width=\"83\" height=\"79\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734509796391.png\" alt=\"rId36\" width=\"83\" height=\"76\"></p>"],
                    solution_en: "<p>44.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734509796194.png\" alt=\"rId34\" width=\"82\" height=\"79\"></p>",
                    solution_hi: "<p>44.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734509796194.png\" alt=\"rId34\" width=\"82\" height=\"79\"></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "45",
                    section: "6",
                    question_en: "<p>45. A square sheet of paper is folded along the dotted line successively along the directions shown and is then punched in the last. How would the paper look when unfolded ?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734509796515.png\" alt=\"rId37\" width=\"244\" height=\"75\"></p>",
                    question_hi: "<p>45. कागज की एक वर्गाकार शीट को दर्शाई गई दिशाओ में बिंदीदार रेखा पर अनुक्रमशः मोड़ा जाता है और आखिर में उसमें छेद किया जाता है। खोलने पर यह कागज कैसा दिखाई देगा ?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734509796515.png\" alt=\"rId37\" width=\"244\" height=\"75\"></p>",
                    options_en: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734509796621.png\" alt=\"rId38\" width=\"85\" height=\"80\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734509796753.png\" alt=\"rId39\" width=\"84\" height=\"83\"></p>", 
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734509796854.png\" alt=\"rId40\" width=\"84\" height=\"77\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734509796996.png\" alt=\"rId41\" width=\"84\" height=\"81\"></p>"],
                    options_hi: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734509796621.png\" alt=\"rId38\" width=\"84\" height=\"79\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734509796753.png\" alt=\"rId39\" width=\"82\" height=\"81\"></p>",
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734509796854.png\" alt=\"rId40\" width=\"84\" height=\"77\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734509796996.png\" alt=\"rId41\" width=\"83\" height=\"80\"></p>"],
                    solution_en: "<p>45.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734509796621.png\" alt=\"rId38\" width=\"92\" height=\"86\"></p>",
                    solution_hi: "<p>45.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734509796621.png\" alt=\"rId38\" width=\"92\" height=\"86\"></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "46",
                    section: "6",
                    question_en: "<p>46. What should come in place of the question mark (?) in the given series based on the English alphabetical order ?<br>CPW, HRA, MTE, RVI, ?</p>",
                    question_hi: "<p>46. अंग्रेजी वर्णमाला क्रम के आधार पर दी गई श्रृंखला में प्रश्न-चिह्न (?) के स्थान पर क्या आना चाहिए ?<br>CPW, HRA, MTE, RVI, ?</p>",
                    options_en: ["<p>VYN</p>", "<p>VXN</p>", 
                                "<p>XXM</p>", "<p>WXM</p>"],
                    options_hi: ["<p>VYN</p>", "<p>VXN</p>",
                                "<p>XXM</p>", "<p>WXM</p>"],
                    solution_en: "<p>46.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734509797381.png\" alt=\"rId42\" width=\"341\" height=\"97\"></p>",
                    solution_hi: "<p>46.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734509797381.png\" alt=\"rId42\" width=\"341\" height=\"97\"></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "47",
                    section: "6",
                    question_en: "<p>47. If A stands for &lsquo;&divide;\', \'B\' stands for \'&times;\', \'C\' stands for \'+\' and \'D\' stands for &lsquo;-&rsquo; what will come in place of the question mark (?) in the following equation ?<br>28 B 7 D 176 A 8 C 13 = ?</p>",
                    question_hi: "<p>47. यदि \'A\' का अर्थ\', &divide;\' B\' का अर्थ \'&times;\', \'C\' का अर्थ \'+\' और \'D\' का अर्थ &lsquo;-&rsquo; है, तो निम्नलिखित समीकरण में प्रश्न चिन्ह (?) के स्थान पर क्या आएगा ?<br>28 B 7 D 176 A 8 C 13 = ?</p>",
                    options_en: ["<p>187</p>", "<p>186</p>", 
                                "<p>184</p>", "<p>185</p>"],
                    options_hi: ["<p>187</p>", "<p>186</p>",
                                "<p>184</p>", "<p>185</p>"],
                    solution_en: "<p>47.(a) <strong>Given :-</strong> 28 B 7 D 176 A 8 C 13 <br>As per given instruction after interchanging the letter with sign we get<br>28 &times; 7 - 176 <math display=\"inline\"><mo>&#247;</mo></math> 8 + 13<br>196 - 22 + 13 <br>196 - 9 = 187</p>",
                    solution_hi: "<p>47.(a) <strong>दिया गया :- </strong>28 B 7 D 176 A 8 C 13 <br>दिए गए निर्देश के अनुसार अक्षर को चिन्ह से बदलने पर हमें प्राप्त होता है<br>28 &times; 7 - 176 <math display=\"inline\"><mo>&#247;</mo></math> 8 + 13<br>196 - 22 + 13 <br>196 - 9 = 187</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "48",
                    section: "6",
                    question_en: "<p>48. ZBRL is related to UWMG in a certain way based on the English alphabetical order. In the same way, IDSQ is related to DYNL. To which of the following is EVNA related, following the same logic ?</p>",
                    question_hi: "<p>48. अंग्रेजी वर्णमाला क्रम के आधार पर ZBRL, UWMG से एक निश्चित प्रकार से संबंधित है। उसी प्रकार, IDSQ, DYNL से संबंधित है। समान तर्क का अनुसरण करते हुए EVNA निम्नलिखित में से किससे संबंधित है ?</p>",
                    options_en: ["<p>ZIVQ</p>", "<p>ZQIV</p>", 
                                "<p>ZIQV</p>", "<p>ZQVI</p>"],
                    options_hi: ["<p>ZIVQ</p>", "<p>ZQIV</p>",
                                "<p>ZIQV</p>", "<p>ZQVI</p>"],
                    solution_en: "<p>48.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734509797488.png\" alt=\"rId43\" width=\"122\" height=\"102\">&nbsp; ,&nbsp; <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734509797593.png\" alt=\"rId44\" width=\"126\" height=\"98\"><br>Similarly,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734509797729.png\" alt=\"rId45\" width=\"127\" height=\"102\"></p>",
                    solution_hi: "<p>48.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734509797488.png\" alt=\"rId43\" width=\"122\" height=\"102\">&nbsp; ,&nbsp; <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734509797593.png\" alt=\"rId44\" width=\"126\" height=\"98\"><br>इसी प्रकार,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734509797729.png\" alt=\"rId45\" width=\"127\" height=\"102\"></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "49",
                    section: "6",
                    question_en: "49. In a certain code language, RUIN\' is coded as \'3098\' and \'TURN\' is coded as \'9630\'. How is \'I\' coded in the given language?",
                    question_hi: "49. एक निश्चित कूट भाषा में, \'RUIN\' को 3098\' लिखा जाता है और \'TURN\' को 9630\' लिखा जाता है। उसी कूट भाषा में \'I\' को कैसे लिखा जाएगा?",
                    options_en: [" 8 ", " 6 ", 
                                " 3 ", " 9"],
                    options_hi: [" 8 ", " 6 ",
                                " 3 ", " 9"],
                    solution_en: "49.(a) RUIN → 3098……(i)<br />               TURN → 9630……(ii)<br />From (i) and (ii) ‘R’ , ‘U’ , ‘N’ and ‘9’ , ‘3’ , ‘0’ are common. The code of ‘I’ = ‘8’.",
                    solution_hi: "49.(a) RUIN → 3098……(i)<br />               TURN → 9630……(ii)<br />(i) और (ii) से \'R\', \'U\', \'N\' और \'9\', \'3\', \'0\' उभयनिष्ठ हैं। \'I\' का कोड = \'8\'.",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "50",
                    section: "17",
                    question_en: "<p>50. In this question, three statements are given, followed by three conclusions numbered I, II and III. Assuming the statements to be true, even if they seem to be at variance with commonly known facts, decide which of the conclusion(s) logically follows/follow from the statements.<br><strong>Statements :</strong><br>Some lions are panthers.<br>No panther is a tiger.<br>Some lions are cats.<br><strong>Conclusions :</strong><br>I. No tiger is a cat.<br>II. Some lions are tiger.<br>III. No cat is a panther.</p>",
                    question_hi: "<p>50. इस प्रश्न में तीन कथन दिए गए हैं, जिनके बाद तीन निष्कर्ष I, II और III दिए गए हैं। कथनों को सत्य मानते हुए भले ही वे सामान्य रूप से ज्ञात तथ्यों सेअलग प्रतीत होते हों, निर्धारित करें कि कौन-सा/से निष्कर्ष दिए गए कथनों का तार्किक रूप से अनुसरण करता है/करते हैं।<br><strong>कथन :</strong><br>कुछ शेर, तेंदुए हैं।<br>कोई तेंदुआ, बाघ नहीं है।<br>कुछ शेर, बिल्लियाँ हैं।<br><strong>निष्कर्ष :</strong><br>I. कोई बाघ, बिल्ली नहीं है।<br>II. कुछ शेर, बाघ हैं।<br>III. कोई बिल्ली , तेंदुआ नहीं है।</p>",
                    options_en: ["<p>Only conclusion I follow.</p>", "<p>Neither conclusions I, II nor III follows.</p>", 
                                "<p>Both conclusions I and II follow.</p>", "<p>Both conclusions II and III follow.</p>"],
                    options_hi: ["<p>केवल निष्कर्ष I अनुसरण करता है।</p>", "<p>न तो निष्कर्ष I, II अनुसरण करता है और न ही निष्कर्ष III अनुसरण करता है।</p>",
                                "<p>निष्कर्ष I और II, दोनों अनुसरण करते हैं।</p>", "<p>निष्कर्ष II और III, दोनों अनुसरण करते हैं।</p>"],
                    solution_en: "<p>50.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734509797849.png\" alt=\"rId46\" width=\"328\" height=\"74\"><br>Neither conclusion I,II nor III follows.</p>",
                    solution_hi: "<p>50.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734509797990.png\" alt=\"rId47\" width=\"354\" height=\"71\"><br>न तो निष्कर्ष I, II और न ही III अनुसरण करता है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "51",
                    section: "17",
                    question_en: "<p>51. If <math display=\"inline\"><mi>&#952;</mi></math> be acute angle and cos &theta; = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>24</mn><mn>25</mn></mfrac></math>, then the value of cot(90 - &theta;) is equal to:</p>",
                    question_hi: "<p>51. यदि <math display=\"inline\"><mi>&#952;</mi></math> एक न्यून कोण है और cos &theta; = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>24</mn><mn>25</mn></mfrac></math> है, तो cot (90 - &theta;) का मान किसके बराबर है ?</p>",
                    options_en: ["<p><math display=\"inline\"><mfrac><mrow><mn>6</mn></mrow><mrow><mn>25</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>7</mn></mrow><mrow><mn>24</mn></mrow></mfrac></math></p>", 
                                "<p><math display=\"inline\"><mfrac><mrow><mn>7</mn></mrow><mrow><mn>25</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>24</mn></mrow></mfrac></math></p>"],
                    options_hi: ["<p><math display=\"inline\"><mfrac><mrow><mn>6</mn></mrow><mrow><mn>25</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>7</mn></mrow><mrow><mn>24</mn></mrow></mfrac></math></p>",
                                "<p><math display=\"inline\"><mfrac><mrow><mn>7</mn></mrow><mrow><mn>25</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>24</mn></mrow></mfrac></math></p>"],
                    solution_en: "<p>51.(b)<br>cos <math display=\"inline\"><mi>&#952;</mi></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>b</mi><mi>a</mi><mi>s</mi><mi>e</mi></mrow><mrow><mi>h</mi><mi>y</mi><mi>p</mi><mi>o</mi><mi>t</mi><mi>e</mi><mi>n</mi><mi>u</mi><mi>s</mi><mi>e</mi></mrow></mfrac><mo>=</mo><mfrac><mn>24</mn><mn>25</mn></mfrac></math><br>Perpendicular = <math display=\"inline\"><msqrt><msup><mrow><mo>(</mo><mn>25</mn><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup><mo>-</mo><msup><mrow><mo>(</mo><mn>24</mn><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup></msqrt></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>625</mn><mo>-</mo><mn>576</mn></msqrt><mo>&#160;</mo><mo>=</mo><msqrt><mn>49</mn></msqrt></math> = 7 <br>Now, cot (90 - <math display=\"inline\"><mi>&#952;</mi></math>) = tan&theta; = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>p</mi><mi>e</mi><mi>r</mi><mi>p</mi><mi>e</mi><mi>n</mi><mi>d</mi><mi>i</mi><mi>c</mi><mi>u</mi><mi>l</mi><mi>a</mi><mi>r</mi></mrow><mrow><mi>b</mi><mi>a</mi><mi>s</mi><mi>e</mi><mo>&#160;</mo></mrow></mfrac><mo>=</mo><mfrac><mn>7</mn><mn>24</mn></mfrac></math></p>",
                    solution_hi: "<p>51.(b)<br>cos <math display=\"inline\"><mi>&#952;</mi></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>&#2310;&#2343;&#2366;&#2352;</mi><mi>&#2325;&#2352;&#2381;&#2339;</mi></mfrac><mo>=</mo><mfrac><mn>24</mn><mn>25</mn></mfrac></math><br>लंब = <math display=\"inline\"><msqrt><msup><mrow><mo>(</mo><mn>25</mn><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup><mo>-</mo><msup><mrow><mo>(</mo><mn>24</mn><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup></msqrt></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>625</mn><mo>-</mo><mn>576</mn></msqrt><mo>&#160;</mo><mo>=</mo><msqrt><mn>49</mn></msqrt></math> = 7<br>अब, cot (90 - <math display=\"inline\"><mi>&#952;</mi></math>) = tan&theta; = <math style=\"font-family: \'Times New Roman\';\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>&#2354;&#2306;&#2348;</mi><mi>&#2310;&#2343;&#2366;&#2352;</mi></mfrac><mo>=</mo><mfrac><mn>7</mn><mn>24</mn></mfrac></math></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "52",
                    section: "17",
                    question_en: "<p>52. R gives <math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math> of the toffees he has with him to S. If now R has k% of the toffies with S, then find the value of k.</p>",
                    question_hi: "<p>52. R अपने पास मौजूद टॉफ़ी का <math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math> भाग S को देता है। यदि अब R के पास S के पास मौजूद टॉफ़ी का k% है, तो k का मान ज्ञात कीजिए।</p>",
                    options_en: ["<p><math display=\"inline\"><mfrac><mrow><mn>500</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>200</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>", 
                                "<p>140</p>", "<p>200</p>"],
                    options_hi: ["<p><math display=\"inline\"><mfrac><mrow><mn>500</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>200</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>",
                                "<p>140</p>", "<p>200</p>"],
                    solution_en: "<p>52.(a)<br>Let R has x toffees <br>Toffees given by R to S = <math display=\"inline\"><mfrac><mrow><mn>3</mn><mi>x</mi></mrow><mrow><mn>8</mn></mrow></mfrac></math><br>Then, now R has toffees = x - <math display=\"inline\"><mfrac><mrow><mn>3</mn><mi>x</mi></mrow><mrow><mn>8</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5</mn><mi>x</mi></mrow><mn>8</mn></mfrac></math><br>Hence, k% = <math display=\"inline\"><mfrac><mrow><mfrac><mrow><mn>5</mn><mi>x</mi></mrow><mrow><mn>8</mn></mrow></mfrac></mrow><mrow><mfrac><mrow><mn>3</mn><mi>x</mi></mrow><mrow><mn>8</mn></mrow></mfrac></mrow></mfrac></math> &times; 100 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>3</mn></mfrac></math>&times; 100 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>500</mn><mn>3</mn></mfrac></math></p>",
                    solution_hi: "<p>52.(a)<br>माना R के पास x टॉफियाँ हैं <br>R द्वारा S को दी गई टॉफ़ी = <math display=\"inline\"><mfrac><mrow><mn>3</mn><mi>x</mi></mrow><mrow><mn>8</mn></mrow></mfrac></math><br>फिर, अब R के पास टॉफ़ी = x - <math display=\"inline\"><mfrac><mrow><mn>3</mn><mi>x</mi></mrow><mrow><mn>8</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5</mn><mi>x</mi></mrow><mn>8</mn></mfrac></math><br>अत:, k% = <math display=\"inline\"><mfrac><mrow><mfrac><mrow><mn>5</mn><mi>x</mi></mrow><mrow><mn>8</mn></mrow></mfrac></mrow><mrow><mfrac><mrow><mn>3</mn><mi>x</mi></mrow><mrow><mn>8</mn></mrow></mfrac></mrow></mfrac></math> &times; 100 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>3</mn></mfrac></math>&times; 100 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>500</mn><mn>3</mn></mfrac></math></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "53",
                    section: "17",
                    question_en: "<p>53. In an assembly election, a candidate got 60% of the total valid votes. 3% of the total votes were declared invalid. If the total number of voters is 2,40,000, then find the number of valid votes polled in favour of the candidate.</p>",
                    question_hi: "<p>53. एक विधानसभा चुनाव में एक उम्मीदवार को कुल वैध मतों का 60% प्राप्त हुआ। कुल मतों का 3% अवैध घोषित किया गया। यदि मतदाताओं की कुल संख्या 2,40,000 है, तो उम्मीदवार के पक्ष में डाले गए वैध मतों की संख्या ज्ञात कीजिए।</p>",
                    options_en: ["<p>193680</p>", "<p>139860</p>", 
                                "<p>139680</p>", "<p>139608</p>"],
                    options_hi: ["<p>193680</p>", "<p>139860</p>",
                                "<p>139680</p>", "<p>139608</p>"],
                    solution_en: "<p>53.(c)<br>Required votes = 240000 &times; <math display=\"inline\"><mfrac><mrow><mn>97</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>5</mn></mfrac></math> = 139680</p>",
                    solution_hi: "<p>53.(c)<br>आवश्यक वोट = 240000 &times; <math display=\"inline\"><mfrac><mrow><mn>97</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>5</mn></mfrac></math> = 139680</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "54",
                    section: "17",
                    question_en: "<p>54. The mean proportion of 0.14 and 2.24 is:</p>",
                    question_hi: "<p>54. 0.14 और 2.24 का माध्यानुपाती ज्ञात कीजिए।</p>",
                    options_en: ["<p>0.56</p>", "<p>-2.39</p>", 
                                "<p>1.12</p>", "<p>1.02</p>"],
                    options_hi: ["<p>0.56</p>", "<p>-2.39</p>",
                                "<p>1.12</p>", "<p>1.02</p>"],
                    solution_en: "<p>54.(a)<br>Mean proportion = <math display=\"inline\"><msqrt><mn>0</mn><mo>.</mo><mn>14</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>2</mn><mo>.</mo><mn>24</mn></msqrt></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mfrac><mn>14</mn><mn>100</mn></mfrac><mo>&#215;</mo><mfrac><mn>224</mn><mn>100</mn></mfrac></msqrt><mo>=</mo><msqrt><mfrac><mn>14</mn><mn>100</mn></mfrac><mo>&#215;</mo><mfrac><mrow><mn>14</mn><mo>&#215;</mo><mn>16</mn></mrow><mn>100</mn></mfrac></msqrt></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>56</mn><mn>100</mn></mfrac></math>= 0.56</p>",
                    solution_hi: "<p>54.(a)<br>माध्य अनुपात = <math display=\"inline\"><msqrt><mn>0</mn><mo>.</mo><mn>14</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>2</mn><mo>.</mo><mn>24</mn></msqrt></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mfrac><mn>14</mn><mn>100</mn></mfrac><mo>&#215;</mo><mfrac><mn>224</mn><mn>100</mn></mfrac></msqrt><mo>=</mo><msqrt><mfrac><mn>14</mn><mn>100</mn></mfrac><mo>&#215;</mo><mfrac><mrow><mn>14</mn><mo>&#215;</mo><mn>16</mn></mrow><mn>100</mn></mfrac></msqrt></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>56</mn><mn>100</mn></mfrac></math><br>= 0.56</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "55",
                    section: "17",
                    question_en: "<p>55. A quantity <math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> is changed to <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>6</mn></mfrac></math>. How much percentage change is given to the quantity ?</p>",
                    question_hi: "<p>55. एक भिन्न <math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> को परिवर्तित करके <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>6</mn></mfrac></math> कर दिया जाता है। भिन्न में कितना प्रतिशत परिवर्तन किया गया है ?</p>",
                    options_en: ["<p>4.17%</p>", "<p>4.71%</p>", 
                                "<p>4.73%</p>", "<p>4.37%</p>"],
                    options_hi: ["<p>4.17%</p>", "<p>4.71%</p>",
                                "<p>4.73%</p>", "<p>4.37%</p>"],
                    solution_en: "<p>55.(a)<br>Change in the fraction = <math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>6</mn></mrow></mfrac></math> - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>5</mn></mfrac><mo>=</mo><mfrac><mn>1</mn><mn>30</mn></mfrac></math><br>Required change = <math display=\"inline\"><mfrac><mrow><mfrac><mrow><mn>1</mn></mrow><mrow><mn>30</mn></mrow></mfrac></mrow><mrow><mfrac><mrow><mn>4</mn></mrow><mrow><mn>5</mn></mrow></mfrac></mrow></mfrac></math> &times; 100 = 4.17%</p>",
                    solution_hi: "<p>55.(a)<br>भिन्न में परिवर्तन = <math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>6</mn></mrow></mfrac></math> - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>5</mn></mfrac><mo>=</mo><mfrac><mn>1</mn><mn>30</mn></mfrac></math><br>आवश्यक परिवर्तन = <math display=\"inline\"><mfrac><mrow><mfrac><mrow><mn>1</mn></mrow><mrow><mn>30</mn></mrow></mfrac></mrow><mrow><mfrac><mrow><mn>4</mn></mrow><mrow><mn>5</mn></mrow></mfrac></mrow></mfrac></math> &times; 100 = 4.17%</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "56",
                    section: "17",
                    question_en: "<p>56. Study the given graph and answer the question that follows. <br>The graph shows the number of projects completed by five companies<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734509798150.png\" alt=\"rId48\" width=\"317\" height=\"231\"> <br>Which company has the highest growth in percentage in completed projects from 2017-18 to 2020-21 ?</p>",
                    question_hi: "<p>56. दिए गए ग्राफ का अध्ययन करें और नीचे दिए गए प्रश्न का उत्तर दें। <br>यह ग्राफ पांच कंपनियों द्वारा पूरी की गई परियोजनाओं की संख्या को दर्शाता है।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734509798150.png\" alt=\"rId48\" width=\"317\" height=\"231\"><br>2017-18 से 2020-21 तक पूरी की गई परियोजनाओं में प्रतिशत में सबसे अधिक वृद्धि किस कंपनी की है ?</p>",
                    options_en: ["<p>C</p>", "<p>D</p>", 
                                "<p>A</p>", "<p>B</p>"],
                    options_hi: ["<p>C</p>", "<p>D</p>",
                                "<p>A</p>", "<p>B</p>"],
                    solution_en: "<p>56.(c)<br>Percentage growth in company C from 2017 - 18 to 2020 - 21 = <math display=\"inline\"><mfrac><mrow><mn>12</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>9</mn></mrow><mrow><mn>9</mn></mrow></mfrac><mo>&#215;</mo><mn>100</mn><mi>&#160;</mi><mo>=</mo><mi>&#160;</mi></math>33.33%<br>Percentage growth in company D from 2017 - 18 to 2020 - 21 = <math display=\"inline\"><mfrac><mrow><mn>5</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>4</mn></mrow><mrow><mn>4</mn></mrow></mfrac><mo>&#215;</mo><mn>100</mn><mi>&#160;</mi><mi>&#160;</mi><mo>=</mo><mi>&#160;</mi></math>25%<br>Percentage growth in company A from 2017 - 18 to 2020 - 21 = <math display=\"inline\"><mfrac><mrow><mn>5</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>2</mn></mrow><mrow><mn>2</mn></mrow></mfrac><mo>&#215;</mo><mn>100</mn><mi>&#160;</mi><mo>=</mo><mi>&#160;</mi></math>150%<br>Percentage growth in company B from 2017 - 18 to 2020 - 21 = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>32</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>17</mn></mrow><mn>17</mn></mfrac><mo>&#215;</mo><mn>100</mn></math>=&nbsp;88.23%<br>Hence, company A has the highest growth in percentage in completed projects from 2017 - 18 to 2020 - 21.</p>",
                    solution_hi: "<p>56.(c)<br>2017 - 18 से 2020 - 21 तक कंपनी C में प्रतिशत वृद्धि = <math display=\"inline\"><mfrac><mrow><mn>12</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>9</mn></mrow><mrow><mn>9</mn></mrow></mfrac><mo>&#215;</mo><mn>100</mn><mi>&#160;</mi><mo>=</mo><mi>&#160;</mi></math>33.33%<br>2017 - 18 से 2020 - 21 तक कंपनी D में प्रतिशत वृद्धि = <math display=\"inline\"><mfrac><mrow><mn>5</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>4</mn></mrow><mrow><mn>4</mn></mrow></mfrac><mo>&#215;</mo><mn>100</mn><mi>&#160;</mi><mi>&#160;</mi><mo>=</mo><mi>&#160;</mi></math>25%<br>2017 - 18 से 2020 - 21 तक कंपनी A में प्रतिशत वृद्धि = <math display=\"inline\"><mfrac><mrow><mn>5</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>2</mn></mrow><mrow><mn>2</mn></mrow></mfrac><mo>&#215;</mo><mn>100</mn><mi>&#160;</mi><mi>&#160;</mi><mo>=</mo><mi>&#160;</mi></math>150%<br>2017 - 18 से 2020 - 21 तक कंपनी B में प्रतिशत वृद्धि = <math display=\"inline\"><mfrac><mrow><mn>32</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>17</mn></mrow><mrow><mn>17</mn></mrow></mfrac><mo>&#215;</mo><mn>100</mn><mi>&#160;</mi><mo>=</mo><mi>&#160;</mi></math>88.23%<br>अतः , कंपनी A की 2017 - 18 से 2020 - 21 तक पूर्ण परियोजनाओं में प्रतिशत में सबसे अधिक वृद्धि हुई है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "57",
                    section: "17",
                    question_en: "57. A retailer gets a discount of 40% on the printed price of an article. If the retailer sells it at the printed price, then his gain percentage is:",
                    question_hi: "57. एक खुदरा विक्रेता को एक वस्तु के अंकित मूल्य पर 40% की छूट मिलती है। यदि खुदरा विक्रेता इसे अंकित मूल्य पर बेचता है, तो उसका लाभ प्रतिशत कितना है?",
                    options_en: [" 33<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> %", " 37<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> %  ", 
                                " 66<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> % ", " 66<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> %"],
                    options_hi: [" 33<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> %", " 37<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> %  ",
                                " 66<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> % ", " 66<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> %"],
                    solution_en: "57.(d) <br />Let MP = 100<br />Then, SP = 100 × 60% = 60<br />Now, if retailer sells it at the printed price<br />CP for retailer = 60<br />SP for the retailer = 100<br />Hence gain% = <math display=\"inline\"><mfrac><mrow><mn>100</mn><mi>&nbsp;</mi><mo>-</mo><mi>&nbsp;</mi><mn>60</mn></mrow><mrow><mn>60</mn></mrow></mfrac></math> × 100 = 6623 %",
                    solution_hi: "57.(d) <br />मान लीजिए अंकित मूल्य = 100<br />तब, विक्रय मूल्य = 100 × 60% = 60<br />अब, यदि खुदरा विक्रेता इसे मुद्रित मूल्य पर बेचता है<br />खुदरा विक्रेता के लिए क्रय मूल्य = 60<br />खुदरा विक्रेता के लिए विक्रय मूल्य = 100<br />अतः लाभ% = <math display=\"inline\"><mfrac><mrow><mn>100</mn><mi>&nbsp;</mi><mo>-</mo><mi>&nbsp;</mi><mn>60</mn></mrow><mrow><mn>60</mn></mrow></mfrac></math> × 100 = 6623 %",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "58",
                    section: "17",
                    question_en: "<p>58. Two parallel chords are drawn in a circle of radius 25 cm. The distance between the two chords is 27 cm. If the length of one chord is 48 cm, then length of the other chord is equal to:</p>",
                    question_hi: "<p>58. 25 cm त्रिज्या वाले एक वृत्त में दो समानांतर जीवाएँ खींची गई हैं। दोनों जीवाओं के बीच की दूरी 27 cm है। यदि एक जीवा की लंबाई 48 cm है, तो दूसरी जीवा की लंबाई किसके बराबर है ?</p>",
                    options_en: ["<p>42 cm</p>", "<p>48 cm</p>", 
                                "<p>30 cm</p>", "<p>36 cm</p>"],
                    options_hi: ["<p>42 cm</p>", "<p>48 cm</p>",
                                "<p>30 cm</p>", "<p>36 cm</p>"],
                    solution_en: "<p>58.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734509798304.png\" alt=\"rId49\" width=\"185\" height=\"168\"><br>Applying pythagorean theorem in the right angle <math display=\"inline\"><mi>&#916;</mi></math> ACO<br>OC<sup>2</sup> = AO<sup>2</sup> - AC<sup>2</sup><br>OC<sup>2</sup> = (25)<sup>2</sup> - (24)<sup>2 </sup>= 49<br>OC = 7 cm<br>Now, OD = CD - OC = 27 - 7 = 20 cm<br>Applying pythagorean theorem in the right angle <math display=\"inline\"><mi>&#916;</mi></math> ODE<br>ED<sup>2</sup> = OE<sup>2</sup> - OD<sup>2</sup><br>ED<sup>2</sup> = (25)<sup>2</sup> - (20)<sup>2</sup> = 225<br>ED = 15 cm<br>So, the length of chord (EF) = 15 &times; 2 = 30 cm</p>",
                    solution_hi: "<p>58.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734509798304.png\" alt=\"rId49\" width=\"185\" height=\"168\"><br>समकोण <math display=\"inline\"><mi>&#916;</mi></math>ACO में पाइथागोरस प्रमेय लागू करने पर <br>OC<sup>2</sup> = AO<sup>2</sup> - AC<sup>2</sup><br>OC<sup>2</sup> = (25)<sup>2</sup> - (24)<sup>2 </sup>= 49<br>OC = 7 सेमी<br>अब, OD = CD - OC = 27 - 7 = 20 सेमी<br>समकोण <math display=\"inline\"><mi>&#916;</mi></math>ODE में पाइथागोरस प्रमेय लागू करने पर <br>ED<sup>2</sup> = OE<sup>2</sup> - OD<sup>2</sup><br>ED<sup>2</sup> = (25)<sup>2</sup> - (20)<sup>2</sup> = 225<br>ED = 15 सेमी<br>अतः, जीवा (EF) की लंम्बाई = 15 &times; 2 = 30 cm</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "59",
                    section: "17",
                    question_en: "<p>59. In a triangle ABC, BD is perpendicular to AC. E is a point on BC such that angle BEA = x&deg;. If angle EAC = 38&deg; and angle EBD = 40&deg;, then the value of x is:</p>",
                    question_hi: "<p>59. त्रिभुज ABC में, BD, AC पर लंबवत है। E, BC पर एक बिंदु इस प्रकार है कि कोण BEA = x&deg; है। यदि कोण EAC = 38&deg; और कोण EBD = 40&deg; है, तो x का मान ज्ञात कीजिए।</p>",
                    options_en: ["<p>88</p>", "<p>82</p>", 
                                "<p>48</p>", "<p>96</p>"],
                    options_hi: ["<p>88</p>", "<p>82</p>",
                                "<p>48</p>", "<p>96</p>"],
                    solution_en: "<p>59.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734509798417.png\" alt=\"rId50\" width=\"188\" height=\"158\"><br>In the right angle <math display=\"inline\"><mi>&#916;</mi></math> AFD,<br>38&deg; + 90&deg; + &ang;ADF = 180&deg;<br>&ang;ADF = 52&deg;<br>Now, &ang;ADF = &ang;BDE = 52&deg; [vertically opposite angle]<br>In the <math display=\"inline\"><mi>&#916;</mi></math>BDE<br>40&deg; + 52&deg; + &ang;BED = 180&deg;<br>&ang;BED = 88&deg;</p>",
                    solution_hi: "<p>59.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734509798417.png\" alt=\"rId50\" width=\"188\" height=\"158\"><br>समकोण <math display=\"inline\"><mi>&#916;</mi></math>AFD में,<br>38&deg; + 90&deg; + &ang;ADF = 180&deg;<br>&ang;ADF = 52&deg;<br>अब, &ang;ADF = &ang;BDE = 52&deg; [शीर्षाभिमुख कोण]<br><math display=\"inline\"><mi>&#916;</mi></math>BDE में,<br>40&deg; + 52&deg; + &ang;BED = 180&deg;<br>&ang;BED = 88&deg;</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "60",
                    section: "17",
                    question_en: "<p>60. The perpendicular length from the origin to the line 6x + 8y - 48 = 0 is:</p>",
                    question_hi: "<p>60. मूल बिंदु से रेखा 6x + 8y - 48 = 0 तक की लंबवत लंबाई ज्ञात कीजिए।</p>",
                    options_en: ["<p>4.8 unit</p>", "<p>2.6 unit</p>", 
                                "<p>8.3 unit</p>", "<p>5.2 unit</p>"],
                    options_hi: ["<p>4.8 इकाई</p>", "<p>2.6 इकाई</p>",
                                "<p>8.3 इकाई</p>", "<p>5.2 इकाई</p>"],
                    solution_en: "<p>60.(a)<br>Perpendicular distance from the origin to the line Ax + By + C = 0<br>d = <math display=\"inline\"><mfrac><mrow><mfenced open=\"|\" close=\"|\" separators=\"|\"><mrow><mi>A</mi><mi>x</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>B</mi><mi>y</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>C</mi></mrow></mfenced></mrow><mrow><msqrt><msup><mrow><mi>A</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><msup><mrow><mi>B</mi></mrow><mrow><mn>2</mn></mrow></msup></msqrt></mrow></mfrac></math><br>Line = 6x + 8y - 48 = 0<br>A = 6 , B = 8 , C = - 48<br>Coordinates of the origin (<math display=\"inline\"><msub><mrow><mi>x</mi></mrow><mrow><mn>1</mn></mrow></msub><mo>,</mo><msub><mrow><mi>y</mi></mrow><mrow><mn>1</mn></mrow></msub></math>) = (0 , 0)<br>d = <math display=\"inline\"><mfrac><mrow><mfenced open=\"|\" close=\"|\" separators=\"|\"><mrow><mn>6</mn><mo>(</mo><mn>0</mn><mo>)</mo><mo>+</mo><mn>8</mn><mo>(</mo><mn>0</mn><mo>)</mo><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>48</mn></mrow></mfenced></mrow><mrow><msqrt><msup><mrow><mo>(</mo><mn>6</mn><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><msup><mrow><mo>(</mo><mn>8</mn><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup></msqrt></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>48</mn><mn>10</mn></mfrac></math> = 4.8 unit</p>",
                    solution_hi: "<p>60.(a)<br>मूल बिंदु से रेखा Ax + By + C = 0 तक की लंबवत दूरी<br>d = <math display=\"inline\"><mfrac><mrow><mfenced open=\"|\" close=\"|\" separators=\"|\"><mrow><mi>A</mi><mi>x</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>B</mi><mi>y</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>C</mi></mrow></mfenced></mrow><mrow><msqrt><msup><mrow><mi>A</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><msup><mrow><mi>B</mi></mrow><mrow><mn>2</mn></mrow></msup></msqrt></mrow></mfrac></math><br>रेखा = 6x + 8y - 48 = 0<br>A = 6 , B = 8 , C = - 48<br>मूल बिंदु के निर्देशांक (<math display=\"inline\"><msub><mrow><mi>x</mi></mrow><mrow><mn>1</mn></mrow></msub><mo>,</mo><msub><mrow><mi>y</mi></mrow><mrow><mn>1</mn></mrow></msub></math>) = (0 , 0)<br>d = <math display=\"inline\"><mfrac><mrow><mfenced open=\"|\" close=\"|\" separators=\"|\"><mrow><mn>6</mn><mo>(</mo><mn>0</mn><mo>)</mo><mo>+</mo><mn>8</mn><mo>(</mo><mn>0</mn><mo>)</mo><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>48</mn></mrow></mfenced></mrow><mrow><msqrt><msup><mrow><mo>(</mo><mn>6</mn><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><msup><mrow><mo>(</mo><mn>8</mn><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup></msqrt></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>48</mn><mn>10</mn></mfrac></math> = 4.8 इकाई</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "61",
                    section: "17",
                    question_en: "<p>61. Following Pie chart presents monthly expenditure by Sushma and Preeti in different heads. Sushma and Preeti make equal expenditure every month.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734509798543.png\" alt=\"rId51\" width=\"241\" height=\"205\"> &nbsp;<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734509798728.png\" alt=\"rId52\" width=\"200\" height=\"190\"> <br>What is the difference between the central angle corresponding to the expenditure incurred on Health by Sushma and Preeti ?</p>",
                    question_hi: "<p>61. निम्नलिखित पाई-चार्ट में सुषमा और प्रीति द्वारा विभिन्न मदों में मासिक व्यय प्रस्तुत किया गया है। सुषमा और प्रीति हर महीने एक बराबर खर्च करती हैं।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734509798865.png\" alt=\"rId53\" width=\"221\" height=\"205\"> &nbsp;<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734509799009.png\" alt=\"rId54\" width=\"198\" height=\"199\"> <br>सुषमा और प्रीति द्वारा स्वास्थ पर किए गए खर्च के संगत केंद्रीय कोण के बीच का अंतर कितना है ?</p>",
                    options_en: ["<p>112&deg;</p>", "<p>12&deg;</p>", 
                                "<p>102&deg;</p>", "<p>18&deg;</p>"],
                    options_hi: ["<p>112&deg;</p>", "<p>12&deg;</p>",
                                "<p>102&deg;</p>", "<p>18&deg;</p>"],
                    solution_en: "<p>61.(d)<br>Total total expenditure of Sushma = 7500 + 1800 + 5000 + 6000 + 7200 + 2500 = 30,000<br>Sushma&rsquo;s expenditure on health = <math display=\"inline\"><mfrac><mrow><mn>7500</mn></mrow><mrow><mn>30000</mn></mrow></mfrac></math> &times; 100 = 25%<br>Preeti&rsquo;s expenditure on health = 20%<br>Required difference = 5% = 5 &times; 3.6&deg; = 18&deg;</p>",
                    solution_hi: "<p>61.(d)<br>सुषमा का कुल खर्च = 7500 + 1800 + 5000 + 6000 + 7200 + 2500 = 30,000<br>सुषमा का स्वास्थ्य पर खर्च = <math display=\"inline\"><mfrac><mrow><mn>7500</mn></mrow><mrow><mn>30000</mn></mrow></mfrac></math> &times; 100 = 25%<br>प्रीति का स्वास्थ्य पर व्यय = 20%<br>आवश्यक अंतर = 5% = 5 &times; 3.6&deg; = 18&deg;</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "62",
                    section: "17",
                    question_en: "<p>62. Evaluate the given expression.<br>[7 + 9 - 2{(3 &divide; <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math>) - 3} + 27 &divide; 3 + (27 &divide; 9 + 3) - 6] + 2</p>",
                    question_hi: "<p>62. दिए गए व्यंजक का मान ज्ञात कीजिए।<br>[7 + 9 - 2{(3 &divide; <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math>) - 3} + 27 &divide; 3 + (27 &divide; 9 + 3) - 6] + 2</p>",
                    options_en: ["<p>15</p>", "<p>20</p>", 
                                "<p>22</p>", "<p>19</p>"],
                    options_hi: ["<p>15</p>", "<p>20</p>",
                                "<p>22</p>", "<p>19</p>"],
                    solution_en: "<p>62.(a)<br>[7 + 9 - 2{(3 &divide; <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math>) - 3} + 27 &divide; 3 + (27 &divide; 9 + 3) - 6] + 2<br>[7 + 9 - 2{9 - 3} + 9 + (3 + 3) - 6] + 2<br>[7 + 9 -12 + 9 + 6 - 6] + 2<br>[13] + 2 = 15</p>",
                    solution_hi: "<p>62.(a)<br>[7 + 9 - 2{(3 &divide; <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math>) - 3} + 27 &divide; 3 + (27 &divide; 9 + 3) - 6] + 2<br>[7 + 9 - 2{9 - 3} + 9 + (3 + 3) - 6] + 2<br>[7 + 9 -12 + 9 + 6 - 6] + 2<br>[13] + 2 = 15</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "63",
                    section: "17",
                    question_en: "<p>63. The following table represents the number of computers (in thousands) manufactured by four companies during the period from 2010 to 2014:<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734509799162.png\" alt=\"rId55\" width=\"340\" height=\"118\"> <br>What is the difference (in thousands) between the number of computers manufactured by company A and C during the period from 2010 to 2014 ?</p>",
                    question_hi: "<p>63. दी गई तालिका दो कंपनियों द्वारा 2010 से 2014 की अवधि के दौरान निर्मित कंप्यूटरों की संख्या (हजारों में) दर्शाती है:<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734509799368.png\" alt=\"rId56\" width=\"351\" height=\"127\"> <br>2010 से 2014 की अवधि के दौरान कंपनी A और C द्वारा निर्मित कंप्यूटरों की संख्या के बीच का अंतर (हजारों में) ज्ञात करें।</p>",
                    options_en: ["<p>340</p>", "<p>320</p>", 
                                "<p>230</p>", "<p>430</p>"],
                    options_hi: ["<p>340</p>", "<p>320</p>",
                                "<p>230</p>", "<p>430</p>"],
                    solution_en: "<p>63.(a)<br>Computer manufactured by company A during 2010 to 2014 = (240 + 300 + 1250 + 470 + 320) = 2580 thousands<br>Computer manufactured by company C during 2010 to 2014 = (270 + 490 + 430 + 1180 + 550) = 2920 thousands<br>Difference = 2920 - 2580 = 340 thousands</p>",
                    solution_hi: "<p>63.(a)<br>2010 से 2014 के दौरान कंपनी A द्वारा निर्मित कंप्यूटर = (240 + 300 + 1250 + 470 + 320) = 2580 हजार<br>2010 से 2014 के दौरान कंपनी C द्वारा निर्मित कंप्यूटर = (270 + 490 + 430 + 1180 + 550) = 2920 हजार<br>अंतर = 2920 - 2580 = 340 हजार</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "64",
                    section: "17",
                    question_en: "<p>64. If the angle of the sector with radius 7 cm is 120&deg; then find the length of arc (in cm). (Use <math display=\"inline\"><mi>&#960;</mi></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>22</mn><mn>7</mn></mfrac></math>)</p>",
                    question_hi: "<p>64. यदि 7 cm त्रिज्या वाले त्रिज्यखंड का कोण 120&deg; है, तो चाप की लंबाई (cm में) ज्ञात कीजिए। (उपयोग करें <math display=\"inline\"><mi>&#960;</mi></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>22</mn><mn>7</mn></mfrac></math>)</p>",
                    options_en: ["<p><math display=\"inline\"><mfrac><mrow><mn>67</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>22</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>", 
                                "<p><math display=\"inline\"><mfrac><mrow><mn>47</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>44</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>"],
                    options_hi: ["<p><math display=\"inline\"><mfrac><mrow><mn>67</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>22</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>",
                                "<p><math display=\"inline\"><mfrac><mrow><mn>47</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>44</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>"],
                    solution_en: "<p>64.(d)<br>Length of arc = <math display=\"inline\"><mfrac><mrow><mi>&#952;</mi></mrow><mrow><mn>360</mn></mrow></mfrac></math> &times; 2&pi;r<br>= <math display=\"inline\"><mfrac><mrow><mn>120</mn></mrow><mrow><mn>360</mn></mrow></mfrac></math> &times; 2 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>22</mn><mn>7</mn></mfrac></math> &times; 7&nbsp;<br>= <math display=\"inline\"><mfrac><mrow><mn>44</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> cm</p>",
                    solution_hi: "<p>64.(d)<br>चाप की लंबाई = <math display=\"inline\"><mfrac><mrow><mi>&#952;</mi></mrow><mrow><mn>360</mn></mrow></mfrac></math> &times; 2&pi;r<br>= <math display=\"inline\"><mfrac><mrow><mn>120</mn></mrow><mrow><mn>360</mn></mrow></mfrac></math> &times; 2 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>22</mn><mn>7</mn></mfrac></math>&times; 7&nbsp;<br>= <math display=\"inline\"><mfrac><mrow><mn>44</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> सेमी</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "65",
                    section: "17",
                    question_en: "<p>65. A motorcyclist rides his motorcycle at a speed of 90 km/hr in the first one hour and the next two hours at a speed of 120 km/hr. Find the average speed of the rider.</p>",
                    question_hi: "<p>65. एक मोटरसाइकिल चालक पहले एक घंटे में 90 km/hr की चाल से और अगले दो घंटों में 120 km/hr की चाल से अपनी मोटरसाइकिल चलाता है। चालक की औसत चाल ज्ञात कीजिए।</p>",
                    options_en: ["<p>115 km/hr</p>", "<p>120 km/hr</p>", 
                                "<p>105 km/hr</p>", "<p>110 km/hr</p>"],
                    options_hi: ["<p>115 km/hr</p>", "<p>120 km/hr</p>",
                                "<p>105 km/hr</p>", "<p>110 km/hr</p>"],
                    solution_en: "<p>65.(d)<br>Average speed = <math display=\"inline\"><mfrac><mrow><mi>t</mi><mi>o</mi><mi>t</mi><mi>a</mi><mi>l</mi><mi>&#160;</mi><mi>d</mi><mi>i</mi><mi>s</mi><mi>t</mi><mi>a</mi><mi>n</mi><mi>c</mi><mi>e</mi><mi>&#160;</mi></mrow><mrow><mi>t</mi><mi>o</mi><mi>t</mi><mi>a</mi><mi>l</mi><mi>&#160;</mi><mi>t</mi><mi>i</mi><mi>m</mi><mi>e</mi></mrow></mfrac></math><br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>90</mn><mo>&#215;</mo><mn>1</mn><mo>+</mo><mn>120</mn><mo>&#215;</mo><mn>2</mn></mrow><mrow><mn>1</mn><mo>+</mo><mn>2</mn></mrow></mfrac><mo>=</mo><mstyle displaystyle=\"true\"><mfrac><mn>330</mn><mn>3</mn></mfrac></mstyle></math> = 110 km/hr</p>",
                    solution_hi: "<p>65.(d)<br>औसत गति = <math style=\"font-family:Verdana\" display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2325;&#2369;&#2354;</mi><mo>&#160;</mo><mi>&#2342;&#2370;&#2352;&#2368;</mi></mrow><mrow><mi>&#2325;&#2369;&#2354;</mi><mo>&#160;</mo><mi>&#2360;</mi><mi>&#2350;</mi><mi>&#2351;</mi></mrow></mfrac></math><br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>90</mn><mo>&#215;</mo><mn>1</mn><mo>+</mo><mn>120</mn><mo>&#215;</mo><mn>2</mn></mrow><mrow><mn>1</mn><mo>+</mo><mn>2</mn></mrow></mfrac><mo>=</mo><mstyle displaystyle=\"true\"><mfrac><mn>330</mn><mn>3</mn></mfrac></mstyle></math> = 110 किमी/घंटा</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "66",
                    section: "17",
                    question_en: "<p>66. If the ratio of principal and simple interest for 5 years is 10 : 7, then the rate of interest per annum is:</p>",
                    question_hi: "<p>66. यदि 5 वर्ष के लिए मूलधन और साधारण ब्याज का अनुपात 10 : 7 है, तो वार्षिक ब्याज की दर क्या है ?</p>",
                    options_en: ["<p>10%</p>", "<p>11%</p>", 
                                "<p>15%</p>", "<p>14%</p>"],
                    options_hi: ["<p>10%</p>", "<p>11%</p>",
                                "<p>15%</p>", "<p>14%</p>"],
                    solution_en: "<p>66.(d) <br><math display=\"inline\"><mfrac><mrow><mi mathvariant=\"bold-italic\">P</mi></mrow><mrow><mi mathvariant=\"bold-italic\">S</mi><mi mathvariant=\"bold-italic\">I</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>10</mn><mn>7</mn></mfrac></math><br><math display=\"inline\"><mfrac><mrow><mi mathvariant=\"bold-italic\">P</mi></mrow><mrow><mfrac><mrow><mi mathvariant=\"bold-italic\">P</mi><mi mathvariant=\"bold-italic\">&#160;</mi><mo>&#215;</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mi mathvariant=\"bold-italic\">R</mi><mi mathvariant=\"bold-italic\">&#160;</mi><mo>&#215;</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mn>5</mn></mrow><mrow><mn>100</mn></mrow></mfrac></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>10</mn><mn>7</mn></mfrac></math><br><math display=\"inline\"><mfrac><mrow><mn>100</mn></mrow><mrow><mn>5</mn><mi mathvariant=\"bold-italic\">R</mi></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>10</mn><mn>7</mn></mfrac></math><br>R = 14%</p>",
                    solution_hi: "<p>66.(d) <br><math style=\"font-family:Verdana\" display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>&#2350;&#2370;&#2354;&#2343;&#2344;</mi><mrow><mi>&#2360;&#2366;&#2343;&#2366;&#2352;&#2339;</mi><mo>&#160;</mo><mi>&#2348;&#2381;&#2351;&#2366;&#2332;</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>10</mn><mn>7</mn></mfrac></math><br><math style=\"font-family:Verdana\" display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"true\"><mfrac><mi>&#2350;&#2370;&#2354;&#2343;&#2344;</mi><mstyle displaystyle=\"true\"><mfrac><mrow><mi>&#2350;&#2370;&#2354;&#2343;&#2344;</mi><mo>&#215;</mo><mi>&#2342;&#2352;</mi><mo>&#215;</mo><mn>5</mn></mrow><mn>100</mn></mfrac></mstyle></mfrac></mstyle></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>10</mn><mn>7</mn></mfrac></math><br><math display=\"inline\"><mfrac><mrow><mn>100</mn></mrow><mrow><mn>5</mn><mi mathvariant=\"bold-italic\">&#160;</mi><mo>&#215;</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mi mathvariant=\"bold-italic\">&#2342;</mi><mi mathvariant=\"bold-italic\">&#2352;</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>10</mn><mn>7</mn></mfrac></math><br>दर = 14%</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "67",
                    section: "17",
                    question_en: "<p>67. Find the percentage increase in surface area of a cube by triple each side.</p>",
                    question_hi: "<p>67. यदि किसी घन की भुजा को तिगुना कर दिया जाए तो, उस घन के पृष्ठीय क्षेत्रफल में प्रतिशत वृद्धि ज्ञात कीजिए।</p>",
                    options_en: ["<p>700%</p>", "<p>800%</p>", 
                                "<p>650%</p>", "<p>500%</p>"],
                    options_hi: ["<p>700%</p>", "<p>800%</p>",
                                "<p>650%</p>", "<p>500%</p>"],
                    solution_en: "<p>67.(b) <br>Total Surface area of cube = 6a<sup>2</sup><br>Ratio - initial : new<br>Side -&nbsp; &nbsp; &nbsp;a&nbsp; &nbsp; :&nbsp; &nbsp;3a<br>TSA -&nbsp; &nbsp; &nbsp;a<sup>2</sup>&nbsp; &nbsp;:&nbsp; &nbsp;9a<sup>2</sup><br>------------------------------&nbsp;<br>Required % = <math display=\"inline\"><mfrac><mrow><mn>9</mn><msup><mrow><mi>a</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><msup><mrow><mi>a</mi></mrow><mrow><mn>2</mn></mrow></msup></mrow><mrow><msup><mrow><mi>a</mi></mrow><mrow><mn>2</mn></mrow></msup></mrow></mfrac></math> &times; 100 = 800%</p>",
                    solution_hi: "<p>67.(b) <br>घन का कुल पृष्ठीय क्षेत्रफल = 6a<sup>2</sup><br>अनुपात&nbsp; -&nbsp; &nbsp; प्रारंभिक&nbsp; :&nbsp; नया<br>भुजा&nbsp; &nbsp; &nbsp; -&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;a&nbsp; &nbsp; &nbsp; &nbsp;:&nbsp; 3a<br>कुल पृष्ठीय क्षे. - a<sup>2</sup>&nbsp; &nbsp; &nbsp;:&nbsp; 9a<sup>2</sup><br>------------------------------------- <br>आवश्यक % = <math display=\"inline\"><mfrac><mrow><mn>9</mn><msup><mrow><mi>a</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><msup><mrow><mi>a</mi></mrow><mrow><mn>2</mn></mrow></msup></mrow><mrow><msup><mrow><mi>a</mi></mrow><mrow><mn>2</mn></mrow></msup></mrow></mfrac></math> &times; 100 = 800%</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "68",
                    section: "17",
                    question_en: "<p>68. If the length, breadth and total surface area of a cuboid are 30 cm, 20 cm and 2600 cm<sup>2</sup>, respectively, then find its height.</p>",
                    question_hi: "<p>68. यदि किसी घनाभ की लंबाई, चौड़ाई और कुल पृष्ठीय क्षेत्रफल क्रमशः 30 cm, 20 cm और 2600 cm<sup>2</sup> है, तो इसकी ऊंचाई ज्ञात कीजिए।</p>",
                    options_en: ["<p>50 cm</p>", "<p>30 cm</p>", 
                                "<p>14 cm</p>", "<p>15 cm</p>"],
                    options_hi: ["<p>50 cm</p>", "<p>30 cm</p>",
                                "<p>14 cm</p>", "<p>15 cm</p>"],
                    solution_en: "<p>68.(c) <br>Total surface area of cuboid = 2(lb + bh + hl)<br>2600 = 2(30 &times; 20 + 20&times; h + h &times; 30)<br>2600 = 2(600 + 50h)<br>2600 = 1200 + 100h<br>100h = 1400 <math display=\"inline\"><mo>&#8658;</mo></math> height (h) = 14 cm</p>",
                    solution_hi: "<p>68.(c) <br>घनाभ का कुल सतह क्षेत्रफल = 2(lb + bh + hl)<br>2600 = 2(30 &times; 20 + 20&times; h + h &times; 30)<br>2600 = 2(600 + 50h)<br>2600 = 1200 + 100h<br>100h = 1400<math display=\"inline\"><mo>&#8658;</mo></math>ऊंचाई (h) = 14 सेमी</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "69",
                    section: "17",
                    question_en: "<p>69. After the division of a number successively by 2,3 and 5, the remainders are 1, 2 and 3, respectively. What will be the remainder, if 13 divides the same number (if the last quotient is 1) ?</p>",
                    question_hi: "<p>69. किसी संख्या को क्रमशः 2, 3 और 5 से विभाजित करने पर शेषफल क्रमशः 1, 2 और 3 प्राप्त होता हैं। यदि उसी संख्या को 13 से विभाजित किया जाए तो शेषफल क्या होगा (यदि अंतिम भागफल 1 है) ?</p>",
                    options_en: ["<p>2</p>", "<p>0</p>", 
                                "<p>3</p>", "<p>1</p>"],
                    options_hi: ["<p>2</p>", "<p>0</p>",
                                "<p>3</p>", "<p>1</p>"],
                    solution_en: "<p>69.(d) <br>In this type of questions, we start the calculation from the last and come towards the initial position.<br>The number which when divided by 5 leaves remainder 3 and quotient is 1 <br>= 5 &times; 1 + 3 = 8<br>When a number is divided by 3 , the remainder is 2<br>= 8 &times; 3 + 2 = 26<br>When a number is divided by 2, the remainder is 1 <br>= 26 &times; 2 + 1 = 53<br>So, number is 53<br>Hence, rem. <math display=\"inline\"><mfrac><mrow><mn>53</mn></mrow><mrow><mn>13</mn></mrow></mfrac></math> = 1<br>Short trick:- <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734509799491.png\" alt=\"rId57\" width=\"322\" height=\"156\"><br>Hence, rem. <math display=\"inline\"><mfrac><mrow><mn>53</mn></mrow><mrow><mn>13</mn></mrow></mfrac></math> = 1</p>",
                    solution_hi: "<p>69.(d) <br>इस प्रकार के प्रश्नों में हम गणना आखिरी से शुरू करके प्रारंभिक स्थिति की ओर आते हैं।<br>वह संख्या जिसे 5 से विभाजित करने पर शेषफल 3 तथा भागफल 1 प्राप्त होता है <br>= 5 &times; 1 + 3 = 8<br>जब किसी संख्या को 3 से विभाजित किया जाता है, तो शेषफल 2 होता है<br>= 8&times; 3 + 2 = 26<br>जब किसी संख्या को 2 से विभाजित किया जाता है, तो शेषफल 1 होता है <br>= 26 &times; 2 + 1 = 53<br>तो, संख्या 53 है<br>इसलिए, शेष <math display=\"inline\"><mfrac><mrow><mn>53</mn></mrow><mrow><mn>13</mn></mrow></mfrac></math> = 1<br>शॉर्ट ट्रिक:-<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734509799663.png\" alt=\"rId58\" width=\"298\" height=\"138\"><br>इसलिए, शेष <math display=\"inline\"><mfrac><mrow><mn>53</mn></mrow><mrow><mn>13</mn></mrow></mfrac></math> = 1</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "70",
                    section: "17",
                    question_en: "<p>70. A work can be done by a man and a woman in 12 days and 8 days, respectively. In how many days will the work be done by 6 men and 4 women ?</p>",
                    question_hi: "<p>70. एक पुरुष और एक महिला द्वारा एक काम को क्रमशः 12 दिनों और 8 दिनों में किया जा सकता है। 6 पुरुष और 4 महिलाएं इस काम को कितने दिनों में पूरा करेंगे ?</p>",
                    options_en: ["<p>2</p>", "<p><math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math></p>", 
                                "<p>1</p>", "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math></p>"],
                    options_hi: ["<p>2</p>", "<p><math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math></p>",
                                "<p>1</p>", "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math></p>"],
                    solution_en: "<p>70.(c) <br>Total work = LCM (12 , 8) = 24 unit<br>Efficiency of man = <math display=\"inline\"><mfrac><mrow><mn>24</mn></mrow><mrow><mn>12</mn></mrow></mfrac></math> = 2 unit<br>Efficiency of women = <math display=\"inline\"><mfrac><mrow><mn>24</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math> = 3 unit<br>So, time taken to 6 men and 4 women to complete the whole work = <math display=\"inline\"><mfrac><mrow><mn>24</mn></mrow><mrow><mn>6</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>2</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>4</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>3</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>24</mn><mn>24</mn></mfrac><mo>&#160;</mo></math>= 1 day</p>",
                    solution_hi: "<p>70.(c) <br>कुल कार्य = LCM (12 , 8) = 24 इकाई<br>पुरुष की दक्षता = <math display=\"inline\"><mfrac><mrow><mn>24</mn></mrow><mrow><mn>12</mn></mrow></mfrac></math> = 2 इकाई<br>महिला की दक्षता = <math display=\"inline\"><mfrac><mrow><mn>24</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math> = 3 इकाई<br>तो, 6 पुरुषों और 4 महिलाओं द्वारा कार्य पूरा करने में लगा समय = <math display=\"inline\"><mfrac><mrow><mn>24</mn></mrow><mrow><mn>6</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>2</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>4</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>3</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>24</mn><mn>24</mn></mfrac><mo>&#160;</mo></math>= 1 दिन</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "71",
                    section: "17",
                    question_en: "<p>71. A mobile phone dealer buys a phone for ₹10,000 and sells it for ₹12,000. Later,&nbsp; he realizes that he could have sold it for ₹13,000. What is the percentage loss that he incurs ?</p>",
                    question_hi: "<p>71. एक मोबाइल फोन व्यापारी एक फोन को ₹10,000 में खरीदता है और ₹12,000 में बेचता है। बाद में, उसे ज्ञात होता है कि वह इसे ₹13,000 में बेच सकता था। उसे कितने प्रतिशत की हानि होती है ?</p>",
                    options_en: ["  10%", "  20%", 
                                "  15%", " 25%"],
                    options_hi: ["  10%", "  20%",
                                "  15%", " 25%"],
                    solution_en: "<p>71.(a) <br>Cost price = 10,000<br>SP<sub>1</sub> = 12,000 <br>SP<sub>2</sub> = 13,000<br>Loss = SP<sub>2</sub> - SP<sub>1</sub> = 1000<br>Loss % = <math display=\"inline\"><mfrac><mrow><mn>1000</mn></mrow><mrow><mn>10000</mn></mrow></mfrac></math> &times; 100 = 10%</p>",
                    solution_hi: "<p>71.(a) <br>लागत मूल्य = 10,000<br>(विक्रय मूल्य)<sub>1</sub> = 12,000 <br>(विक्रय मूल्य)<sub>2</sub> = 13,000<br>हानि = (विक्रय मूल्य)<sub>2</sub> - (विक्रय मूल्य)<sub>1</sub> = 1000<br>हानि % = <math display=\"inline\"><mfrac><mrow><mn>1000</mn></mrow><mrow><mn>10000</mn></mrow></mfrac></math> &times; 100 = 10%</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "72",
                    section: "17",
                    question_en: "72. A train 550 metres long is running at a speed of 78 km/h. How many seconds will it take to cross a 350 metres long train running in the opposite direction at a speed of 30 km/h ?",
                    question_hi: "72. 550 मीटर लंबी एक ट्रेन 78 km/h की चाल से चल रही है। 30 km/h की चाल से विपरीत दिशा में चल रही 350 मीटर लंबी एक दूसरी ट्रेन को पार करने में इसे कितने सेकंड का समय लगेगा ?",
                    options_en: [" 50 seconds ", " 20 seconds ", 
                                " 40 seconds ", " 30 seconds"],
                    options_hi: [" 50 सेकंड", " 20 सेकंड",
                                " 40 सेकंड", " 30 सेकंड"],
                    solution_en: "72.(d)<br />Relative speed = (78 + 30) × <math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>18</mn></mrow></mfrac></math> = 30 m/sec<br />So, required time = <math display=\"inline\"><mfrac><mrow><mn>550</mn><mi>&nbsp;</mi><mo>+</mo><mi>&nbsp;</mi><mn>350</mn></mrow><mrow><mn>30</mn></mrow></mfrac></math> = 30 seconds",
                    solution_hi: "72.(d)<br />सापेक्ष गति = (78 + 30) × <math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>18</mn></mrow></mfrac></math> = 30 मीटर/सेकंड<br />तो, आवश्यक समय = <math display=\"inline\"><mfrac><mrow><mn>550</mn><mi>&nbsp;</mi><mo>+</mo><mi>&nbsp;</mi><mn>350</mn></mrow><mrow><mn>30</mn></mrow></mfrac></math> = 30 सेकंड",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "73",
                    section: "17",
                    question_en: "<p>73. What is the area (in cm<sup>2</sup>) of an equilateral triangle whose perimeter is 24 cm?<br>Note: Round off to the nearest two decimal places.</p>",
                    question_hi: "<p>73. 24 cm परिमाप वाले एक समबाहु त्रिभुज का क्षेत्रफल (cm<sup>2</sup> में) कितना होगा ?<br>ध्यान दें: दशमलव के निकटतम दो स्थानों तक पूर्णांकित करें।</p>",
                    options_en: ["<p>27.71</p>", "<p>13.86</p>", 
                                "<p>28.14</p>", "<p>27.14</p>"],
                    options_hi: ["<p>27.71</p>", "<p>13.86</p>",
                                "<p>28.14</p>", "<p>27.14</p>"],
                    solution_en: "<p>73.(a)<br>Each side of equilateral triangle = <math display=\"inline\"><mfrac><mrow><mn>24</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> = 8 cm<br>Area = <math display=\"inline\"><mfrac><mrow><msqrt><mn>3</mn></msqrt></mrow><mrow><mn>4</mn></mrow></mfrac></math> &times; 8 &times; 8 = 16 &times; 1.732 = 27.71 cm<sup>2</sup></p>",
                    solution_hi: "<p>73.(a)<br>समबाहु त्रिभुज की प्रत्येक भुजा = <math display=\"inline\"><mfrac><mrow><mn>24</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> = 8 सेमी<br>क्षेत्रफल = <math display=\"inline\"><mfrac><mrow><msqrt><mn>3</mn></msqrt></mrow><mrow><mn>4</mn></mrow></mfrac></math> &times; 8 &times; 8 = 16 &times; 1.732 = 27.71 सेमी<sup>2</sup></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "74",
                    section: "17",
                    question_en: "<p>74. Study the given table and answer that question that follows. The given table shows the loan disbursed by five banks (in crores) over four years.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734509799999.png\" alt=\"rId59\" width=\"327\" height=\"114\"> <br>Which two banks have disbursed the same sum of loan over all four years ?</p>",
                    question_hi: "<p>74. दी गई तालिका का अध्ययन करके प्रश्न का उत्तर दीजिए। <br>दी गई तालिका चार वर्षों में पांच बैंकों द्वारा संवितरित ऋण राशि (करोड़ में) दर्शाती है।<br><strong id=\"docs-internal-guid-6d19ad21-7fff-35c5-4d31-6e30831aeb3e\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcgqlIKTyzLncOIutQJ3ygkFUZbDCs1M8BoZ6W09WC6Zfxom4B8n7YFJh5-J91dBonSWjihCqhekw4DXlnditn9F0TbHW88-oISWGcs0Kv6AJQ5vJWbUK78DdADze8P0AjaJjlejw?key=Izmg76fadiaMFn8sBii5oP2D\" width=\"372\" height=\"112\"></strong><br>किन दो बैंकों ने सभी चार वर्षों में समान ऋण राशि संवितरित की है ?</p>",
                    options_en: ["<p>A and B</p>", "<p>C and D</p>", 
                                "<p>B and C</p>", "<p>B and D</p>"],
                    options_hi: ["<p>A और B</p>", "<p>C और D</p>",
                                "<p>B और C</p>", "<p>B और D</p>"],
                    solution_en: "<p>74.(b)<br>By checking the options one by one<br>Loan disbursed by the bank A over all four years = (15 + 20 + 10 + 12) = 57<br>Loan disbursed by the bank B over all four years = (15 + 20 + 15 + 20) = 70<br>Loan disbursed by the bank C over all four years = (20 + 25 + 15 + 20) = 80<br>Loan disbursed by the bank D over all four years = (15 + 20 + 35 + 10) = 80<br>Hence, the required bank be C and D.</p>",
                    solution_hi: "<p>74.(b)<br>एक-एक करके विकल्पों की जाँच करके<br>बैंक A द्वारा सभी चार वर्षों में वितरित ऋण = (15 + 20 + 10 + 12) = 57<br>बैंक B द्वारा सभी चार वर्षों में वितरित ऋण = (15 + 20 + 15 + 20) = 70<br>बैंक C द्वारा सभी चार वर्षों में वितरित ऋण = (20 + 25 + 15 + 20) = 80<br>बैंक D द्वारा सभी चार वर्षों में वितरित ऋण = (15 + 20 + 35 + 10) = 80<br>अतः, आवश्यक बैंक C और D हैं।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "75",
                    section: "18",
                    question_en: "<p>75. If three spheres of radius 75 cm, 60 cm and 45 cm are melted into one big sphere, then the radius of the big sphere is:</p>",
                    question_hi: "<p>75. यदि 75 cm, 60 cm और 45 cm त्रिज्या वाले तीन गोले पिघलाकर एक बड़ा गोला बनाया जाए तो बड़े गोले की त्रिज्या ______होगी।</p>",
                    options_en: ["<p>95 cm</p>", "<p>85 cm</p>", 
                                "<p>80 cm</p>", "<p>90 cm</p>"],
                    options_hi: ["<p>95 cm</p>", "<p>85 cm</p>",
                                "<p>80 cm</p>", "<p>90 cm</p>"],
                    solution_en: "<p>75.(d) <br>According to question<br><math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>3</mn></mrow></mfrac><mi>&#960;</mi></math>(<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msubsup><mi>r</mi><mn>3</mn><mn>1</mn></msubsup><mo>+</mo><msubsup><mi>r</mi><mn>3</mn><mn>2</mn></msubsup><mo>+</mo><msubsup><mi>r</mi><mn>3</mn><mn>3</mn></msubsup></math>) = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>3</mn></mfrac></math>&pi;R<sup>3</sup><br><math display=\"inline\"><msubsup><mrow><mi>r</mi></mrow><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></msubsup><mo>+</mo><msubsup><mrow><mi>r</mi></mrow><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></msubsup><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><msubsup><mrow><mi>r</mi></mrow><mrow><mn>3</mn></mrow><mrow><mn>3</mn></mrow></msubsup></math> = R<sup>3</sup><br><math display=\"inline\"><mo>(</mo><mn>75</mn><msup><mrow><mo>)</mo></mrow><mrow><mn>3</mn></mrow></msup><mo>+</mo><mo>(</mo><mn>60</mn><msup><mrow><mo>)</mo></mrow><mrow><mn>3</mn></mrow></msup><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mo>(</mo><mn>45</mn><msup><mrow><mo>)</mo></mrow><mrow><mn>3</mn></mrow></msup></math> = R<sup>3</sup><br>421875 +216000 + 91125 =&nbsp;<math display=\"inline\"><msup><mrow><mi>R</mi></mrow><mrow><mn>3</mn></mrow></msup></math><br>729000 = <math display=\"inline\"><msup><mrow><mi>R</mi></mrow><mrow><mn>3</mn></mrow></msup></math><br><math display=\"inline\"><mi>R</mi></math> = 90 cm<br><strong>Short trick:</strong><br>Answer will be multiple of 15 as 15 is common in radius of three sphere.</p>",
                    solution_hi: "<p>75.(d) <br>प्रश्न के अनुसार<br><math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>3</mn></mrow></mfrac><mi>&#960;</mi></math>(<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msubsup><mi>r</mi><mn>3</mn><mn>1</mn></msubsup><mo>+</mo><msubsup><mi>r</mi><mn>3</mn><mn>2</mn></msubsup><mo>+</mo><msubsup><mi>r</mi><mn>3</mn><mn>3</mn></msubsup></math>) = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>3</mn></mfrac></math>&pi;R<sup>3</sup><br><math display=\"inline\"><msubsup><mrow><mi>r</mi></mrow><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></msubsup><mo>+</mo><msubsup><mrow><mi>r</mi></mrow><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></msubsup><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><msubsup><mrow><mi>r</mi></mrow><mrow><mn>3</mn></mrow><mrow><mn>3</mn></mrow></msubsup></math> = R<sup>3</sup><br><math display=\"inline\"><mo>(</mo><mn>75</mn><msup><mrow><mo>)</mo></mrow><mrow><mn>3</mn></mrow></msup><mo>+</mo><mo>(</mo><mn>60</mn><msup><mrow><mo>)</mo></mrow><mrow><mn>3</mn></mrow></msup><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mo>(</mo><mn>45</mn><msup><mrow><mo>)</mo></mrow><mrow><mn>3</mn></mrow></msup></math> = R<sup>3</sup><br>421875 +216000 + 91125 =&nbsp;<math display=\"inline\"><msup><mrow><mi>R</mi></mrow><mrow><mn>3</mn></mrow></msup></math><br>729000 = <math display=\"inline\"><msup><mrow><mi>R</mi></mrow><mrow><mn>3</mn></mrow></msup></math><br><math display=\"inline\"><mi>R</mi></math> = 90 <br><strong>शॉर्ट ट्रिक:</strong><br>उत्तर 15 का गुणज होगा क्योंकि तीन गोले की त्रिज्या में 15 उभयनिष्ठ है। </p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "76",
                    section: "18",
                    question_en: "<p>76. Which of the following Articles in the Constitution of India provides protection to the President and Governors and Rajpramukhs ?</p>",
                    question_hi: "<p>76. भारतीय संविधान का निम्नलिखित में से कौन-सा अनुच्छेद राष्ट्रपति और राज्यपालों और राजप्रमुखों को सुरक्षा प्रदान करता है ?</p>",
                    options_en: ["<p>Article 352</p>", "<p>Article 356</p>", 
                                "<p>Article 361</p>", "<p>Article 370</p>"],
                    options_hi: ["<p>अनुच्छेद 352</p>", "<p>अनुच्छेद 356</p>",
                                "<p>अनुच्छेद 361</p>", "<p>अनुच्छेद 370</p>"],
                    solution_en: "<p>76.(c) <strong>Article 361.</strong> Other Articles: Article 352: Deals with the proclamation of Emergency due to war, external aggression, or internal disturbance. Article 356: Relates to the President\'s rule in a state, where the President takes over the governance of a state due to failure of the constitutional machinery. Article 370: Provided special status to the state of Jammu and Kashmir, which was repealed in 2019.</p>",
                    solution_hi: "<p>76.(c) <strong>अनुच्छेद 361. </strong>अन्य अनुच्छेद: अनुच्छेद 352: युद्ध, बाहरी आक्रमण या आंतरिक अशांति के कारण आपातकाल की घोषणा से संबंधित है। अनुच्छेद 356: किसी राज्य में राष्ट्रपति शासन से संबंधित है, जहाँ संवैधानिक तंत्र की विफलता के कारण राष्ट्रपति राज्य का शासन अपने अधीन कर लेता है। अनुच्छेद 370: जम्मू और कश्मीर राज्य को विशेष दर्जा प्रदान करता था, जिसे 2019 में निरस्त कर दिया गया।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "77",
                    section: "18",
                    question_en: "<p>77. From which of the following Constitutions has the India Constitution adopted the system of &lsquo;First Past the Post&rsquo; ?</p>",
                    question_hi: "<p>77. निम्नलिखित में से किस संविधान से भारतीय संविधान ने \'&lsquo;बहुलवादी (First Past the Post)&rsquo;\' की प्रणाली को अपनाया है ?</p>",
                    options_en: ["<p>Irish Constitution</p>", "<p>French Constitution</p>", 
                                "<p>United States Constitution</p>", "<p>British Constitution</p>"],
                    options_hi: ["<p>आयरिश संविधान</p>", "<p>फ्रांसीसी संविधान</p>",
                                "<p>संयुक्त राज्य अमेरिका का संविधान</p>", "<p>ब्रिटिश संविधान</p>"],
                    solution_en: "<p>77.(d) <strong>British Constitution.</strong> The First Past the Post (FPTP) system, also known as the simple majority system, is used in India for elections to the Lok Sabha and State Legislative Assemblies. Irish Constitution: Ireland uses the Single Transferable Vote (STV) system, a form of proportional representation. French Constitution: France employs a two-round system, where a candidate must win an absolute majority (over 50%) in the first round or a relative majority in the second round.</p>",
                    solution_hi: "<p>77.(d) <strong>ब्रिटिश संविधान। </strong>बहुलवादी (फर्स्ट पास्ट द पोस्ट (FPTP)) प्रणाली, जिसे साधारण बहुमत प्रणाली भी कहा जाता है, जिसका उपयोग भारत में लोकसभा और राज्य विधान सभाओं के चुनावों के लिए किया जाता है। आयरिश संविधान: आयरलैंड एकल संक्रमणीय मत (STV) प्रणाली का उपयोग करता है, जो आनुपातिक प्रतिनिधित्व का एक रूप है। फ्रांसीसी संविधान: फ्रांस दो-चरण प्रणाली का उपयोग करता है, जहाँ उम्मीदवार को पहले चरण में पूर्ण बहुमत (50% से अधिक) या दूसरे चरण में सापेक्ष बहुमत प्राप्त करना होता है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "78",
                    section: "18",
                    question_en: "<p>78. The district of Shivamogga that is famous for its Iron-Ore and Manganese mineral is located in which state of India ?</p>",
                    question_hi: "<p>78. लौह-अयस्क और मैंगनीज खनिज के लिए प्रसिद्ध शिवमोग्गा (Shivamogga) जिला भारत के किस राज्य में स्थित है ?</p>",
                    options_en: ["<p>Karnataka</p>", "<p>Jharkhand</p>", 
                                "<p>Maharashtra</p>", "<p>Rajasthan</p>"],
                    options_hi: ["<p>कर्नाटक</p>", "<p>झारखंड</p>",
                                "<p>महाराष्ट्र</p>", "<p>राजस्थान</p>"],
                    solution_en: "<p>78.(a)<strong> Karnataka. </strong>Other famous mines in India: Panna and Bunder Project - Madhya Pradesh. West Singhbhum, Pahadia, Kunderkocha and Parasi - Jharkhand. Bailadila iron ore and Dalli-Rajhara - Chhattisgarh. Ratnagiri - Maharashtra. Sundergarh, Joda, Keonjhar and Jajpur - Odisha.</p>",
                    solution_hi: "<p>78.(a) <strong>कर्नाटक। </strong>भारत के अन्य प्रसिद्ध खदानें: पन्ना और बंदर परियोजना - मध्य प्रदेश। पश्चिमी सिंहभूम, पहाड़िया, कुंदरकोचा और परासी - झारखंड। बैलाडीला लौह अयस्क एवं दल्ली-राजहरा - छत्तीसगढ़। रत्नागिरी - महाराष्ट्र। सुंदरगढ़, जोडा, क्योंझर और जाजपुर - ओडिशा।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "79",
                    section: "18",
                    question_en: "<p>79. The Ayush Ministry has collaborated with the UK&rsquo;s London School of Hygiene and Tropical Medicine (LSHTM) to conduct a study on ____ for promoting recovery from COVID-19.</p>",
                    question_hi: "<p>79. आयुष मंत्रालय ने कोविड-19 से रिकवरी को बढ़ावा देने के लिए _____पर एक अध्ययन करने के लिए यूके के लंदन स्कूल ऑफ हाइजीन एंड ट्रॉपिकल मेडिसिन (London School of Hygiene and Tropical Medicine - LSHTM) के साथ सहयोग किया है।</p>",
                    options_en: ["<p>Boswellia</p>", "<p>Brahmi</p>", 
                                "<p>Gotu Kola</p>", "<p>Ashwagandha</p>"],
                    options_hi: ["<p>बोसवेलिया</p>", "<p>ब्राह्मी</p>",
                                "<p>गोटू कोला</p>", "<p>अश्वगंधा</p>"],
                    solution_en: "<p>79.(d) <strong>Ashwagandha </strong>(Withania somnifera) is an herb used in Ayurvedic medicine to boost immunity, reduce stress, and improve overall well-being. Boswellia (frankincense) is an herbal extract used in Ayurvedic medicine to reduce inflammation and pain. Brahmi (Bacopa monnieri) is an herb used in Ayurvedic medicine to enhance memory, cognitive function, and brain health. Gotu Kola (Centella asiatica) is an herb used in Ayurvedic medicine to improve wound healing, skin health, and cognitive function.</p>",
                    solution_hi: "<p>79.(d) <strong>अश्वगंधा </strong>(विथानिया सोम्नीफेरा) एक जड़ी बूटी है जिसका उपयोग आयुर्वेदिक चिकित्सा में प्रतिरक्षा को बढ़ाने, तनाव को कम करने और समग्र स्वास्थ्य को बेहतर बनाने के लिए किया जाता है। बोसवेलिया (लोबान या लोहबान) एक हर्बल अर्क (extract) है जिसका उपयोग आयुर्वेदिक चिकित्सा में सूजन और दर्द को कम करने के लिए किया जाता है। ब्राह्मी (बेकोपा मोनिएरी) एक जड़ी बूटी है जिसका उपयोग आयुर्वेदिक चिकित्सा में स्मृति, संज्ञानात्मक कार्य और मस्तिष्क स्वास्थ्य को बढ़ाने के लिए उपयोग किया जाता है। गोटू कोला (सेंटेला एशियाटिका) एक जड़ी बूटी है जिसका उपयोग आयुर्वेदिक चिकित्सा में घाव भरने, त्वचा के स्वास्थ्य और संज्ञानात्मक कार्य को बेहतर बनाने के लिए किया जाता है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "80",
                    section: "18",
                    question_en: "<p>80. Which was the first dynasty of the Delhi Sultanate?</p>",
                    question_hi: "<p>80. दिल्ली सल्तनत का पहला राजवंश कौन-सा था ?</p>",
                    options_en: ["<p>Mamluk dynasty</p>", "<p>Tughlaq dynasty</p>", 
                                "<p>Khilji dynasty</p>", "<p>Lodi dynasty </p>"],
                    options_hi: ["<p>मामलुक राजवंश</p>", "<p>तुगलक राजवंश</p>",
                                "<p>खिलजी राजवंश</p>", "<p>लोदी राजवंश</p>"],
                    solution_en: "<p>80.(a) <strong>Mamluk dynasty: </strong>Also known as the Slave Dynasty, founded by Qutb-ud-din Aibak, who became the first Sultan of Delhi in 1206. The dynasty ruled Delhi from 1206 to 1290. Khilji Dynasty: The second dynasty of the Delhi Sultanate, ruling from 1290 to 1320. Tughlaq Dynasty: The third dynasty of the Delhi Sultanate, ruling from 1320 to 1414. Lodi Dynasty: The last dynasty of the Delhi Sultanate, ruling from 1451 to 1526.</p>",
                    solution_hi: "<p>80.(a)<strong> मामलुक राजवंश: </strong>इसे गुलाम राजवंश के नाम से भी जाना जाता है, जिसकी स्थापना कुतुबुद्दीन ऐबक ने की थी, जो 1206 में दिल्ली के पहले सुल्तान बने। इस राजवंश ने 1206 से 1290 तक दिल्ली पर शासन किया। खिलजी राजवंश: दिल्ली सल्तनत का दूसरा राजवंश, जिसने 1290 से 1320 तक शासन किया। तुगलक राजवंश: दिल्ली सल्तनत का तीसरा राजवंश, जिसने 1320 से 1414 तक शासन किया। लोदी राजवंश: दिल्ली सल्तनत का अंतिम राजवंश, जिसने 1451 से 1526 तक शासन किया।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "81",
                    section: "18",
                    question_en: "<p>81. During 2015, in ______ sector, Government of India had allowed about 49% of FDI.</p>",
                    question_hi: "<p>81. 2015 के दौरान, ______क्षेत्र में भारत सरकार ने लगभग 49%, FDI की अनुमति दी।</p>",
                    options_en: ["<p>defence</p>", "<p>education</p>", 
                                "<p>manufacture of medical devices</p>", "<p>food products</p>"],
                    options_hi: ["<p>रक्षा</p>", "<p>शिक्षा</p>",
                                "<p>चिकित्सा उपकरणों का विनिर्माण</p>", "<p>खाद्य उत्पाद</p>"],
                    solution_en: "<p>81.(a) <strong>defense. </strong>FDI Policy Reforms in sectors listed under Make in India since May 2014-15 : India has liberalized FDI policies in various sectors including food retail, rail infrastructure, construction, and manufacturing, allowing up to 100% foreign investment to boost economic growth and job creation.</p>",
                    solution_hi: "<p>81.(a) <strong>रक्षा। </strong>मई 2014-15 से मेक इन इंडिया के अंतर्गत सूचीबद्ध क्षेत्रों में FDI नीति में सुधार: भारत ने खाद्य खुदरा, रेल अवसंरचना, निर्माण और विनिर्माण सहित विभिन्न क्षेत्रों में एफडीआई नीतियों को उदार बनाया है, जिससे आर्थिक विकास और रोजगार सृजन को बढ़ावा देने के लिए 100% तक विदेशी निवेश की अनुमति मिल गई है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "82",
                    section: "18",
                    question_en: "<p>82. Which city hosted the Asian Champions Trophy hockey tournament in 2023 ?</p>",
                    question_hi: "<p>82. 2023 में किस शहर ने एशियाई चैंपियंस ट्रॉफी हॉकी टूर्नामेंट (Asian Champions Trophy hockey tournament) की मेजबानी की ?</p>",
                    options_en: ["<p>Chennai</p>", "<p>Surat</p>", 
                                "<p>Ahmedabad</p>", "<p>Delhi</p>"],
                    options_hi: ["<p>चेन्नई</p>", "<p>सूरत</p>",
                                "<p>अहमदाबाद</p>", "<p>दिल्ली</p>"],
                    solution_en: "<p>82.(a) <strong>Chennai. </strong>India won a record fourth title in the 7th Asian Champions Trophy hockey tournament by defeating Malaysia in the final. The hockey trophies include the Agha Khan Cup, Beighton Cup, Dhyan Chand Trophy, and Lady Ratan Tata Trophy.</p>",
                    solution_hi: "<p>82.(a) <strong>चेन्नई।</strong> भारत ने 7वें एशियाई चैंपियंस ट्रॉफी हॉकी टूर्नामेंट में फाइनल में मलेशिया को हराकर रिकॉर्ड चौथा खिताब जीता। हॉकी ट्रॉफियों में आगा खान कप, बेटन कप, ध्यानचंद ट्रॉफी और लेडी रतन टाटा ट्रॉफी शामिल हैं।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "83",
                    section: "18",
                    question_en: "<p>83. As on 31 March 2024, apart from the Ministry of Home Affairs, which of the following ministries is headed by Shri Amit Shah ?</p>",
                    question_hi: "<p>83. 31 मार्च 2024 तक की स्&zwj;थिति के अनुसार, गृह मंत्रालय के अलावा, निम्नलिखित में से किस मंत्रालय का नेतृत्व श्री अमित शाह कर रहे हैं ?</p>",
                    options_en: ["<p>Ministry of Coal</p>", "<p>Ministry of Cooperation</p>", 
                                "<p>Ministry of Minority Affairs</p>", "<p>Ministry of Corporate Affairs</p>"],
                    options_hi: ["<p>कोयला मंत्रालय</p>", "<p>सहकारिता मंत्रालय</p>",
                                "<p>अल्पसंख्यक कार्य मंत्रालय</p>", "<p>कॉर्पोरेट कार्य मंत्रालय</p>"],
                    solution_en: "<p>83.(b) <strong>Ministry of Cooperation. </strong>Cabinet Ministers of India 2024: Nitin Gadkari- Minister for Road Transport &amp; Highways, J. P. Nadda- Ministry of Health and Family Welfare, Chirag Paswan - Ministry of Food Processing Industries, Annapurna Devi- Ministry of Women and Child Development .</p>",
                    solution_hi: "<p>83.(b) <strong>सहकारिता मंत्रालय। </strong>भारत के कैबिनेट मंत्री 2024: नितिन गडकरी- सड़क परिवहन एवं राजमार्ग मंत्री, जे. पी. नड्डा- स्वास्थ्य एवं परिवार कल्याण मंत्रालय, चिराग पासवान - खाद्य प्रसंस्करण उद्योग मंत्रालय, अन्नपूर्णा देवी- महिला और बाल विकास मंत्रालय।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "84",
                    section: "18",
                    question_en: "<p>84. Which of the following exchange rates is determined by the market forces of demand and supply ?</p>",
                    question_hi: "<p>84. निम्नलिखित में से कौन-सी विनिमय दर मांग और आपूर्ति की बाज़ार शक्तियों द्वारा निर्धारित की जाती है ?</p>",
                    options_en: ["<p>Soft pegged exchange rate</p>", "<p>Floating exchange rate</p>", 
                                "<p>Hard pegged exchange rate</p>", "<p>Fixed exchange rate</p>"],
                    options_hi: ["<p>मृदु नियंत्रित विनिमय दर</p>", "<p>अस्थायी विनिमय दर</p>",
                                "<p>कठोर नियंत्रित विनिमय दर</p>", "<p>निश्चित विनिमय दर</p>"],
                    solution_en: "<p>84.(b) <strong>Floating exchange rate:</strong> The exchange rate is determined by the interactions of buyers and sellers in the foreign exchange market. Soft Pegged Exchange Rate: The rate is fixed but can be adjusted by the government. Hard Pegged Exchange Rate: The rate is fixed and cannot be changed. Fixed Exchange Rate: The rate is set by the government or central bank and maintained at a fixed level against a major currency or a basket of currencies.</p>",
                    solution_hi: "<p>84.(b) <strong>अस्थायी विनिमय दर:</strong> विनिमय दर विदेशी मुद्रा बाजार में खरीदारों और विक्रेताओं के पारस्परिक विचार-विमर्श से निर्धारित होती है। मृदु नियंत्रित विनिमय दर: यह दर निश्चित होती है लेकिन सरकार द्वारा इसे समायोजित किया जा सकता है। कठोर नियंत्रित विनिमय दर: यह दर निश्चित होती है और इसे बदला नहीं जा सकता। निश्चित विनिमय दर: यह दर सरकार या केंद्रीय बैंक द्वारा निर्धारित की जाती है और किसी प्रमुख मुद्रा या मुद्रा समूहों के प्रति एक निश्चित स्तर पर बनाए रखी जाती है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "85",
                    section: "18",
                    question_en: "<p>85. The Central Office of the Reserve Bank of India was initially established in which present city in 1935 ?</p>",
                    question_hi: "<p>85. भारतीय रिज़र्व बैंक का केंद्रीय कार्यालय प्रारंभ में 1935 में किस वर्तमान शहर में स्थापित किया गया था ?</p>",
                    options_en: ["<p>Mumbai</p>", "<p>Chennai</p>", 
                                "<p>Delhi</p>", "<p>Kolkata</p>"],
                    options_hi: ["<p>मुंबई</p>", "<p>चेन्नई</p>",
                                "<p>दिल्ली</p>", "<p>कोलकाता</p>"],
                    solution_en: "<p>85.(d) <strong>Kolkata. </strong>Reserve Bank of India was established on 1 April 1935 on the recommendations of Hilton Young Commission. Headquarters : Mumbai, Maharashtra. Nationalization: On 1 January 1949. Currency: Issues all currency notes except for one rupee. Initially, the Central Office of the RBI was established in Calcutta but was moved to Mumbai in 1937.</p>",
                    solution_hi: "<p>85.(d) <strong>कोलकाता।</strong> भारतीय रिज़र्व बैंक की स्थापना 1 अप्रैल 1935 को हिल्टन यंग कमीशन की सिफारिशों पर की गई थी। मुख्यालय: मुंबई, महाराष्ट्र। राष्ट्रीयकरण: 1 जनवरी 1949 को। आरबीआई (RBI) एक रुपये को छोड़कर सभी करेंसी नोट जारी करता है। प्रारंभ में, आरबीआई (RBI) का केंद्रीय कार्यालय कलकत्ता में स्थापित किया गया था लेकिन 1937 में इसे मुंबई स्थानांतरित कर दिया गया।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "86",
                    section: "18",
                    question_en: "<p>86. Which of the following reactions leads to the formation of glucose pentaacetate from acetic anhydride ?</p>",
                    question_hi: "<p>86. निम्नलिखित में से कौन-सी अभिक्रिया द्वारा एसिटिक एनहाइड्राइड (acetic anhydride) से ग्लूकोज पेंटाऐसीटेट (glucose pentaacetate) का निर्माण होता है ?</p>",
                    options_en: ["<p>Acetylation of glucose</p>", "<p>Reduction</p>", 
                                "<p>Addition of ketone</p>", "<p>Oxidation</p>"],
                    options_hi: ["<p>ग्लूकोज का एसिटिलीकरण (Acetylation of glucose)</p>", "<p>अपचयन (Reduction)</p>",
                                "<p>कीटोन का परिवर्धन (Addition of ketone)</p>", "<p>ऑक्सीकरण (Oxidation)</p>"],
                    solution_en: "<p>86.(a) <strong>Acetylation of glucose.</strong> Acetylation is a chemical reaction in which an acetyl group (CH<sub>3</sub>CO-) is introduced into a molecule. For glucose, acetylation occurs when glucose reacts with acetic anhydride, producing glucose pentaacetate. The reaction can be represented as: Glucose + 5 Acetic Anhydride &rarr; Glucose Pentaacetate + 5 Acetic Acid.</p>",
                    solution_hi: "<p>86.(a) <strong>ग्लूकोज का एसिटिलीकरण। </strong>एसिटिलीकरण एक रासायनिक अभिक्रिया है जिसमें एक एसिटिल समूह (CH<sub>3</sub>CO-) को अणु में प्रवेश किया जाता है। ग्लूकोज के लिए, एसिटिलीकरण तब होता है जब ग्लूकोज एसिटिक एनहाइड्राइड के साथ अभिक्रिया करता है, जिससे ग्लूकोज पेंटाएसीटेट का निर्माण होता है। इस अभिक्रिया को इस प्रकार दर्शाया जा सकता है: ग्लूकोज + 5 एसिटिक एनहाइड्राइड &rarr; ग्लूकोज पेंटाएसीटेट + 5 एसिटिक एसिड।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "87",
                    section: "18",
                    question_en: "<p>87. Sangeeta Shankar was awarded Sangeet Natak Akademi Award 2021 for her contribution in playing which musical instrument ?</p>",
                    question_hi: "<p>87. संगीता शंकर (Sangeeta Shankar) को किस संगीत वाद्ययंत्र को बजाने में उनके योगदान के लिए संगीत नाटक अकादमी पुरस्कार 2021 से सम्मानित किया गया ?</p>",
                    options_en: ["<p>Violin</p>", "<p>Santoor</p>", 
                                "<p>Veena</p>", "<p>Flute</p>"],
                    options_hi: ["<p>वायलिन</p>", "<p>संतूर</p>",
                                "<p>वीणा</p>", "<p>बांसुरी</p>"],
                    solution_en: "<p>87.(a) <strong>Violin. </strong>Instruments and their exponents: Violin: MS Gopalakrishnan, M Chandrasekharan. Guitar: Braj Bhushan Kabra. Santoor: Pt. Shiv Kumar Sharma, Bhajan Sopori, Tarun Bhattacharya. Veena : Ayyagari Syamasundaram, Doraiswamy Iyengar, Suma Sudhindra. Flute: Hari Prasad Chaurasia, Pannalal Ghosh, Palladam Sanjiva Rao.</p>",
                    solution_hi: "<p>87.(a) <strong>वायलिन। </strong>वाद्ययंत्र एवं उनके प्रतिपादक : वायलिन - एम.एस. गोपालकृष्णन, एम. चंद्रशेखरन। गिटार - ब्रज भूषण काबरा। संतूर - पं. शिव कुमार शर्मा, भजन सोपोरी, तरूण भट्टाचार्य। वीणा - अय्यागरी श्यामसुंदरम, डोरईस्वामी अयंगर, सुमा सुधींद्र। बांसुरी - हरि प्रसाद चौरसिया, पन्नालाल घोष, पल्लदम संजीव राव।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "88",
                    section: "18",
                    question_en: "<p>88. As per the Census of India-2011, the density of population per km2 was ______ persons.</p>",
                    question_hi: "<p>88. भारत की जनगणना-2011 के अनुसार, प्रति वर्ग किमी (per km2) जनसंख्या का घनत्व _____व्यक्ति था।</p>",
                    options_en: ["<p>382</p>", "<p>394</p>", 
                                "<p>345</p>", "<p>320</p>"],
                    options_hi: ["<p>382</p>", "<p>394</p>",
                                "<p>345</p>", "<p>320</p>"],
                    solution_en: "<p>88.(a) <strong>382.</strong> The density of population is the ratio between the numbers of people to the size of land. It is usually measured in persons per square kilometer. As per Census of India 2011, Bihar has the highest population density (1,106) in India while Arunachal Pradesh has the lowest population density in India with 17 people per square kilometer.</p>",
                    solution_hi: "<p>88.(a)<strong> 382.</strong> जनसंख्या घनत्व लोगों की संख्या और भूमि के आकार के बीच का अनुपात है। इसे आमतौर पर प्रति वर्ग किलोमीटर व्यक्तियों में मापा जाता है। भारत की जनगणना 2011 के अनुसार, बिहार में भारत में सबसे अधिक जनसंख्या घनत्व (1,106) है, जबकि अरुणाचल प्रदेश में प्रति वर्ग किलोमीटर 17 लोगों के साथ भारत में सबसे कम जनसंख्या घनत्व है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "89",
                    section: "18",
                    question_en: "<p>89. Of which of the following states/UTs is Rauf a folk dance ?</p>",
                    question_hi: "<p>89. रउफ़ निम्नलिखित में से किस राज्य केंद्र शासित प्रदेश का एक लोकनृत्य है ?</p>",
                    options_en: ["<p>Karnataka</p>", "<p>Kerala</p>", 
                                "<p>Lakshadweep</p>", "<p>Jammu and Kashmir</p>"],
                    options_hi: ["<p>कर्नाटक</p>", "<p>केरल</p>",
                                "<p>लक्षद्वीप</p>", "<p>जम्मू और कश्मीर</p>"],
                    solution_en: "<p>89.(d) <strong>Jammu and Kashmir. </strong>Rauf is a popular dance form performed by women during weddings and other celebrations. Other States and their Dances : Jammu and Kashmir - Kud Dance, Rouf Dance, Dumhal Dance, Hafiza Dance. Karnataka - Yakshagana, Suggi, Karga. Kerala - Kathakali (Classical), Mohiniattam, Kaikottikali. Lakshadweep - Lava, Kolkali, Parichakali.</p>",
                    solution_hi: "<p>89.(d) <strong>जम्मू और कश्मीर।</strong> रउफ़ शादियों और अन्य समारोहों के दौरान महिलाओं द्वारा किया जाने वाला एक लोकप्रिय नृत्य है। अन्य राज्य और उनके नृत्य : जम्मू और कश्मीर - कुद नृत्य, दुमहाल नृत्य, हाफ़िज़ा नृत्य। कर्नाटक - यक्षगान, सुग्गी, करगा। केरल - कथकली (शास्त्रीय), मोहिनीअट्टम, कैकोट्टिकली। लक्षद्वीप - लावा, कोलकली, परिचाकली।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "90",
                    section: "18",
                    question_en: "<p>90. &lsquo;Ujjwala&rsquo;, a _____, was the official mascot of Khelo India &ndash; Para Games 2023.</p>",
                    question_hi: "<p>90. \'उज्ज्वला (Ujjwala)\', एक ______ खेलो इंडिया &ndash; पैरा गेम्स 2023 का आधिकारिक मैस्कॉट (mascot) था।</p>",
                    options_en: ["<p>goose</p>", "<p>sparrow</p>", 
                                "<p>duck</p>", "<p>hen</p>"],
                    options_hi: ["<p>राजहंस (goose)</p>", "<p>गौरैया(sparrow)</p>",
                                "<p>बत्तख (duck)</p>", "<p>मुर्गी (hen)</p>"],
                    solution_en: "<p>90.(b) <strong>Sparrow.</strong> Khelo India Para Games 2023: Concluded in New Delhi on December 17. Haryana topped the medal tally with 105 medals (40 gold, 39 silver, and 26 bronze) in the inaugural edition of the Khelo India Para Games 2023 .</p>",
                    solution_hi: "<p>90.(b) <strong>गौरैया </strong>(sparrow)। खेलो इंडिया पैरा गेम्स 2023: 17 दिसंबर को नई दिल्ली में संपन्न हुआ। खेलो इंडिया पैरा गेम्स 2023 के उद्घाटन संस्करण में हरियाणा 105 पदक (40 स्वर्ण, 39 रजत और 26 कांस्य) के साथ पदक तालिका में शीर्ष पर रहा।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "91",
                    section: "18",
                    question_en: "<p>91. Which device is used to measure the current flow using needle deflection caused by a magnetic field force acting upon a current-carrying wire ?</p>",
                    question_hi: "<p>91. धारावाही तार पर चुंबकीय क्षेत्र बल के कारण सुई विक्षेपण का उपयोग करके धारा प्रवाह को मापने के लिए किस उपकरण का उपयोग किया जाता है ?</p>",
                    options_en: ["<p>Galvanometer</p>", "<p>Ammeter</p>", 
                                "<p>Psophometer</p>", "<p>Potentiometer</p>"],
                    options_hi: ["<p>गैल्वेनोमीटर</p>", "<p>ऐमीटर</p>",
                                "<p>सोफ़ोमीटर</p>", "<p>पोटेंशियोमीटर</p>"],
                    solution_en: "<p>91.(a) <strong>Galvanometer: </strong>An electrical measuring instrument used to detect current flow by measuring the needle deflection caused by the magnetic field acting on a current-carrying wire. The deflection is proportional to the current. Ammeter: Measures current using a shunt resistor. Psophometer: Measures sound levels. Potentiometer: Measures voltage.</p>",
                    solution_hi: "<p>91.(a) <strong>गैल्वेनोमीटर </strong>एक विद्युत मापक उपकरण है जिसका उपयोग धारा प्रवाह का पता लगाने के लिए किया जाता है, जो धारा-वाही तार पर कार्य करने वाले चुंबकीय क्षेत्र के कारण सुई के विक्षेपण को मापता है। विक्षेपण विद्युत धारा के समानुपाती होता है। एमीटर: शंट प्रतिरोधक का उपयोग करके विद्युत धारा को मापता है। सोफ़ोमीटर: ध्वनि के स्तर को मापता है। पोटेंशियोमीटर: वोल्टेज को मापता है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "92",
                    section: "18",
                    question_en: "<p>92. Community tradition is related to which of the following music streams ?</p>",
                    question_hi: "<p>92. सामुदायिक परंपरा निम्नलिखित में से किस संगीत धाराओं (music streams) से संबंधित है ?</p>",
                    options_en: ["<p>Hindustani Classical Music</p>", "<p>Karnataka Classical Music</p>", 
                                "<p>Hindustani Lokgeet</p>", "<p>Karnataka Lokgeet</p>"],
                    options_hi: ["<p>हिन्दुस्तानी शास्त्रीय संगीत</p>", "<p>कर्नाटक शास्त्रीय संगीत</p>",
                                "<p>हिन्दुस्तानी लोकगीत</p>", "<p>कर्नाटक लोकगीत</p>"],
                    solution_en: "<p>92.(a)<strong> Hindustani Classical Music </strong>is traditionally passed down through oral traditions in a teacher-student (guru-shishya) relationship within Gharanas (musical schools), which preserve unique styles and reflect community traditions across different regions.</p>",
                    solution_hi: "<p>92.(a) <strong>हिंदुस्तानी शास्त्रीय संगीत</strong> पारंपरिक रूप से घरानों (संगीत विद्यालयों) में शिक्षक-छात्र (गुरु-शिष्य) संबंध में मौखिक परंपराओं के माध्यम से आगे बढ़ाया जाता है, जो अद्वितीय शैलियों को संरक्षित करता है और विभिन्न क्षेत्रों में सामुदायिक परंपराओं को प्रतिबिंबित करता है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "93",
                    section: "18",
                    question_en: "<p>93. When was air transport nationalised in India ?</p>",
                    question_hi: "<p>93. भारत में हवाई परिवहन का राष्ट्रीयकरण कब किया गया था ?</p>",
                    options_en: ["<p>1950</p>", "<p>1952</p>", 
                                "<p>1951</p>", "<p>1953</p>"],
                    options_hi: ["<p>1950</p>", "<p>1952</p>",
                                "<p>1951</p>", "<p>1953</p>"],
                    solution_en: "<p>93.(d) <strong>1953. </strong>The Air Corporations Act of 1953 led to the nationalization of Indian airlines and the formation of two corporations: Indian Airlines for domestic routes and Air India International for international routes. The purpose of the act was to improve air transport services for the public and to facilitate the acquisition of existing air companies. The Air Corporations (Amendment) Act of 1962 amended the Air Corporations Act of 1953. The name of Air India International was shortened to Air India in 1962.</p>",
                    solution_hi: "<p>93.(d) <strong>1953 </strong>के एयर कॉर्पोरेशन अधिनियम के कारण भारतीय एयरलाइनों का राष्ट्रीयकरण हुआ और दो निगमों का गठन हुआ: घरेलू मार्गों के लिए इंडियन एयरलाइंस और अंतर्राष्ट्रीय मार्गों के लिए एयर इंडिया इंटरनेशनल। अधिनियम का उद्देश्य जनता के लिए हवाई परिवहन सेवाओं में सुधार करना और मौजूदा हवाई कंपनियों के अधिग्रहण की सुविधा प्रदान करना था। 1962 के एयर कॉरपोरेशन (संशोधन) अधिनियम ने 1953 के एयर कॉरपोरेशन अधिनियम में संशोधन किया। 1962 में एयर इंडिया इंटरनेशनल का नाम छोटा करके एयर इंडिया कर दिया गया।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "94",
                    section: "18",
                    question_en: "<p>94. Former Karnataka CM ______ received the Padma Vibhushan at Rashtrapati Bhavan in New Delhi for his contribution in the field of Public Affairs in 2023.</p>",
                    question_hi: "<p>94. कर्नाटक के पूर्व मुख्यमंत्री _______को 2023 में सार्वजनिक मामलों (Public Affairs) के क्षेत्र में उनके योगदान के लिए नई दिल्ली के राष्ट्रपति भवन में पद्म विभूषण प्राप्त हुआ।</p>",
                    options_en: ["<p>D Devaraj Urs</p>", "<p>SM Krishna</p>", 
                                "<p>HD Deve Gowda</p>", "<p>S Nijalingappa</p>"],
                    options_hi: ["<p>डी देवराज उर्स</p>", "<p>एस.एम. कृष्णा</p>",
                                "<p>एच.डी. देवे गौड़ा</p>", "<p>एस निजलिंगप्पा</p>"],
                    solution_en: "<p>94.(b) <strong>SM Krishna</strong> was Chief Minister of Karnataka from October 1999 to May 2004. HD Deve Gowda was 11th Prime Minister of India. Karnataka: Capital - Bangalore, Legislative Assembly - 224 seats, Legislative Council - 75 seats, Rajya Sabha - 12 seats, Lok Sabha - 28 seats, State Bird - Indian Roller, State Flower - Lotus, State Tree - Sandalwood.</p>",
                    solution_hi: "<p>94.(b) <strong>एस.एम. कृष्णा,</strong> अक्टूबर 1999 से मई 2004 तक कर्नाटक के मुख्यमंत्री रहे। एच.डी. देवेगौड़ा भारत के 11वें प्रधानमंत्री थे। कर्नाटक: राजधानी - बैंगलोर, विधान सभा - 224 सीटें, विधान परिषद - 75 सीटें, राज्यसभा - 12 सीटें, लोकसभा - 28 सीटें, राजकीय पक्षी - इंडियन रोलर, राजकीय पुष्प - कमल, राजकीय वृक्ष - चंदन।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "95",
                    section: "18",
                    question_en: "<p>95. Which view in MS PowerPoint is useful for editing or formatting a single slide at a time ?</p>",
                    question_hi: "<p>95. MS PowerPoint में कौन सा व्यू (view) एक समय में एक ही स्लाइड के एडिटिंग या फ़ॉर्मेटिंग के लिए उपयोगी है ?</p>",
                    options_en: ["<p>Normal view</p>", "<p>Reading view</p>", 
                                "<p>Outline view</p>", "<p>Slide Sorter view</p>"],
                    options_hi: ["<p>नार्मल व्यू (Normal view)</p>", "<p>रीडिंग व्यू (Reading view )</p>",
                                "<p>आउटलाइन व्यू (Outline view )</p>", "<p>स्लाइड सॉर्टर व्यू (Slide Sorter view)</p>"],
                    solution_en: "<p>95.(a) <strong>Normal view: </strong>Allows you to edit text and images, format slide elements, add animations and transitions, and focus on a single slide without distractions. Reading View: Optimized for reading and presenting slides. Outline View: Displays the presentation\'s outline in text form. Slide Sorter View: Used for arranging and organizing slides in a presentation.</p>",
                    solution_hi: "<p>95.(a) <strong>सामान्य दृश्य</strong> (Normal view) आपको टेक्स्ट और इमेज को संपादित (edit) करने, स्लाइड एलिमेंट्स को फ़ॉर्मेट करने, एनिमेशन और ट्रांज़िशन जोड़ने (add) और बिना किसी व्यवधान (distractions) के सिंगल स्लाइड पर फ़ोकस करने की अनुमति देता है। रीडिंग व्यू (Reading View): स्लाइड पढ़ने (reading) और प्रस्तुत (presenting) करने के लिए अनुकूलित (Optimized) । आउटलाइन व्यू (Outline View): प्रेजेंटेशन की आउटलाइन को टेक्स्ट फ़ॉर्म में प्रदर्शित (Displays) करता है। स्लाइड सॉर्टर व्यू (Slide Sorter View): प्रेजेंटेशन में स्लाइड को व्यवस्थित (arranging) करने और संगठित (organizing) करने के लिए उपयोग किया जाता है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "96",
                    section: "18",
                    question_en: "96. Which of the following represents the Two-Factor Authentication (2FA)?",
                    question_hi: "96. निम्न में से कौन टू-फैक्टर ऑथेंटिकेशन (Two-Factor Authentication - 2FA) को निरूपित करता है?",
                    options_en: [" The use of two different credentials to verify identity ", " A tool for optimising system resources ", 
                                " A security measure to prevent physical damage to a PC ", " A technique for organising files and folders on a PC"],
                    options_hi: [" पहचान सत्यापित करने के लिए दो अलग-अलग क्रेडेंशियल्स (credentials) का उपयोग ", " सिस्टम संसाधनों को अनुकूलित करने के लिए एक उपकरण ",
                                " PC को भौतिक क्षति से बचाने के लिए एक सुरक्षा उपाय ", " PC पर फ़ाइलों और फ़ोल्डरों को व्यवस्थित करने की एक तकनीक"],
                    solution_en: "96.(a) Two-Factor Authentication (2FA) is a security process that requires two different credentials to verify an individual\'s identity. These credentials can be: Something you know (password, PIN), Something you have (smartphone, token), Something you are (biometric data, fingerprint). 2FA provides an extra layer of security, making it more difficult for attackers to gain unauthorized access to sensitive information.",
                    solution_hi: "96.(a) टू-फैक्टर ऑथेंटिकेशन (2FA) एक सुरक्षा प्रक्रिया है जिसमें किसी व्यक्ति की पहचान सत्यापित करने के लिए दो अलग-अलग क्रेडेंशियल (credentials) की आवश्यकता होती है। ये क्रेडेंशियल कुछ ऐसे हो सकते हैं जिन्हें आप जानते हैं (पासवर्ड, पिन), कुछ ऐसा जो आपके पास है (स्मार्टफोन, टोकन), कुछ ऐसा जो आप हैं (बायोमेट्रिक डेटा, फिंगरप्रिंट)। 2FA सुरक्षा की एक अतिरिक्त लेयर प्रदान करता है, जिससे अटैकर (attackers) के लिए संवेदनशील जानकारी तक अनधिकृत पहुँच प्राप्त करना अधिक कठिन हो जाता है।",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "97",
                    section: "18",
                    question_en: "<p>97. In kabaddi, when a raider touches any body part of any defender or any part of their clothing, it is called:</p>",
                    question_hi: "<p>97. कबड्डी में, जब कोई रेडर किसी डिफेंडर के शरीर के किसी हिस्से या उनके कपड़ों के किसी हिस्से को छूता है, तो इसे क्या कहा जाता है ?</p>",
                    options_en: ["<p>catch</p>", "<p>tag</p>", 
                                "<p>raid</p>", "<p>pursuit</p>"],
                    options_hi: ["<p>कैच (catch)</p>", "<p>टैग (tag)</p>",
                                "<p>रेड (raid)</p>", "<p>पुरसूट (pursuit)</p>"],
                    solution_en: "<p>97.(b) <strong>A tag </strong>is a crucial aspect of the game, as it allows the raider to score points by touching multiple defenders during a raid. The raider must then return to their own side of the court before taking a breath, making the tag a high-pressure and thrilling moment in the game.</p>",
                    solution_hi: "<p>97.(b)<strong> टैग </strong>(tag) खेल का एक महत्वपूर्ण पहलू है, क्योंकि यह रेडर को रेड के दौरान कई डिफेंडरों को छूकर अंक अर्जित करने की अनुमति देता है। रेडर को सांस लेने से पहले कोर्ट के अपने हिस्से में वापस लौटना होता है, जिससे टैग (tag) खेल में एक उच्च दबाव वाला और रोमांचक क्षण (thrilling moment) बन जाता है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "98",
                    section: "18",
                    question_en: "<p>98. Which of the following is one of the largest spiral galaxies ?</p>",
                    question_hi: "<p>98. निम्नलिखित में से कौन-सी सबसे बड़ी सर्पिल आकाशगंगाओं (spiral galaxies) में से एक है ?</p>",
                    options_en: ["<p>NGC 6872</p>", "<p>Milky Way</p>", 
                                "<p>Cygnus A</p>", "<p>Maffei 1</p>"],
                    options_hi: ["<p>एनजीसी 6872 (NGC 6872)</p>", "<p>आकाशगंगा (Milky Way)</p>",
                                "<p>सिग्नस A (Cygnus A)</p>", "<p>मैफ़ी 1 (Maffei 1)</p>"],
                    solution_en: "<p>98.(a) <strong>NGC 6872, </strong>which is one of the largest spiral galaxies, spanning 522,000 light-years across, approximately five times the size of the Milky Way. It is located 212 million light-years from Earth in the southern constellation Pavo.</p>",
                    solution_hi: "<p>98.(a) <strong>NGC 6872, </strong>जो सबसे बड़ी सर्पिल आकाशगंगाओं में से एक है, जिसका आकार 522,000 प्रकाश वर्ष है, जो मिल्की वे ( Milky Way) के आकार से लगभग पाँच गुना बड़ा है। यह पृथ्वी से 212 मिलियन प्रकाश-वर्ष की दूरी पर दक्षिणी नक्षत्र पैवो में स्थित है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "99",
                    section: "18",
                    question_en: "<p>99. According to the Census of India-2011, which state has the lowest sex ratio ?</p>",
                    question_hi: "<p>99. भारत की जनगणना-2011 के अनुसार, किस राज्य का लिंगानुपात सबसे कम है ?</p>",
                    options_en: ["<p>Rajasthan</p>", "<p>Bihar</p>", 
                                "<p>Haryana</p>", "<p>Maharashtra</p>"],
                    options_hi: ["<p>राजस्थान</p>", "<p>बिहार</p>",
                                "<p>हरियाणा</p>", "<p>महाराष्ट्र</p>"],
                    solution_en: "<p>99.(c) <strong>Haryana </strong>(879 females per 1000 males). Sex ratio - The number of females per 1,000 males in the population of a society. According to census 2011 (15th), Sex ratio in India is 943. Other States Sex ratio : Rajasthan - 928, Bihar - 918, Maharashtra - 929.</p>",
                    solution_hi: "<p>99.(c) <strong>हरियाणा </strong>(प्रति 1000 पुरुषों पर 879 महिलाएं)। लिंगानुपात - किसी समाज की जनसंख्या में प्रति 1,000 पुरुषों पर महिलाओं की संख्या। जनगणना 2011 (15वीं) के अनुसार, भारत में लिंगानुपात 943 है। अन्य राज्यों का लिंगानुपात : राजस्थान - 928, बिहार - 918, महाराष्ट्र - 929।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "100",
                    section: "misc",
                    question_en: "<p>100. With which of the following communities is the word \'Langar\' (community kitchen) mainly associated ?</p>",
                    question_hi: "<p>100. शब्द \'लंगर\' (सामुदायिक रसोई) मुख्य रूप से निम्नलिखत में से किस समुदाय से संबंधित है ?</p>",
                    options_en: ["<p>Sikhism</p>", "<p>Hinduism</p>", 
                                "<p>Jainism</p>", "<p>Buddhism</p>"],
                    options_hi: ["<p>सिख धर्म</p>", "<p>हिन्दू धर्म</p>",
                                "<p>जैन धर्म</p>", "<p>बुद्ध धर्म</p>"],
                    solution_en: "<p>100.(a) <strong>Sikhism. </strong>The concept of &ldquo;Langar&rdquo; is an integral part of Sikh culture, symbolizing equality, service, and community bonding. It is commonly served at Gurdwaras (Sikh temples) and was introduced by Guru Nanak (1469-1539). Similar tradition of Annadanam (food donation) exist in Hinduism.</p>",
                    solution_hi: "<p>100.(a) <strong>सिख धर्म।</strong> \"लंगर\" की अवधारणा सिख संस्कृति का एक अभिन्न अंग है, जो समानता, सेवा और सामुदायिक बंधन का प्रतीक है। यह सामान्यतः गुरुद्वारों (सिख मंदिरों) में परोसा जाता है और इसे गुरु नानक (1469-1539) द्वारा शुरू किया गया था। अन्नदानम (भोजन दान) की ऐसी ही परंपरा हिंदू धर्म में मौजूद है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>