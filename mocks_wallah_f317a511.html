<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">15:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 20</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">20</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 15 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["7"] = {
                name: "English",
                start: 0,
                end: 18
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="7">English</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 19,
                end: 19
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "7",
                    question_en: "<p>1. Select the most appropriate synonym of the given word. <br><strong>Generic</strong></p>",
                    question_hi: "<p>1. Select the most appropriate synonym of the given word. <br><strong>Generic</strong></p>",
                    options_en: ["<p>Specific</p>", "<p>Precise</p>", 
                                "<p>Definite</p>", "<p>Universal</p>"],
                    options_hi: ["<p>Specific</p>", "<p>Precise</p>",
                                "<p>Definite</p>", "<p>Universal</p>"],
                    solution_en: "<p>1.(d) <br><strong>Universal </strong>- connected with, done by, or affecting everyone in the world or everyone in a particular group or widespread or ubiquitous.<br><strong>Generic </strong>- common or collective or typical of a whole group of things <br><strong>Specific </strong>- detailed or exact, particular or distinct<br><strong>Precise </strong>- clear and accurate or explicit<br><strong>Definite </strong>- fixed and unlikely to change, certain </p>",
                    solution_hi: "<p>1.(d) <br><strong>Universal </strong>(सार्वभौमिक) - connected with, done by, or affecting everyone in the world or everyone in a particular group or widespread or ubiquitous.<br><strong>Generic </strong>(सामान्य) - common or collective or typical of a whole group of things <br><strong>Specific </strong>(विशिष्ट) - detailed or exact, particular or distinct<br><strong>Precise </strong>(स्पष्ट) - clear and accurate or explicit<br><strong>Definite </strong>(निश्चित) - fixed and unlikely to change, certain</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "7",
                    question_en: "<p>2. Select the most appropriate synonym of the given word. <br><strong>Regime</strong></p>",
                    question_hi: "<p>2. Select the most appropriate synonym of the given word. <br><strong>Regime</strong></p>",
                    options_en: ["<p>Territory</p>", "<p>Rule</p>", 
                                "<p>Space</p>", "<p>Country</p>"],
                    options_hi: ["<p>Territory</p>", "<p>Rule</p>",
                                "<p>Space</p>", "<p>Country</p>"],
                    solution_en: "<p>2.(b)<br><strong>Rule </strong>- control of or dominion over an area or people; jurisdiction, administration<br><strong>Regime </strong>- a method or system of government, especially one that has not been elected in a fair way <br><strong>Territory </strong>- an area of land that belongs to one country <br><strong>Space </strong>- a place or an area that is empty or not used<br><strong>Country </strong>- a nation with its own government, occupying a particular territory</p>",
                    solution_hi: "<p>2.(b)<br><strong>Rule </strong>(शासन) - control of or dominion over an area or people; jurisdiction, administration<br><strong>Regime </strong>(शासन प्रणाली) - a method or system of government, especially one that has not been elected in a fair way <br><strong>Territory </strong>(क्षेत्र) - an area of land that belongs to one country <br><strong>Space </strong>(स्थान/क्षेत्र) - a place or an area that is empty or not used<br><strong>Country </strong>(देश) - a nation with its own government, occupying a particular territory</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "7",
                    question_en: "<p>3. Select the most appropriate synonym of the given word. <br>Dedicated</p>",
                    question_hi: "<p>3. Select the most appropriate synonym of the given word. <br>Dedicated</p>",
                    options_en: ["<p>Boring</p>", "<p>Committed</p>", 
                                "<p>Tedious</p>", "<p>Dreary</p>"],
                    options_hi: ["<p>Boring</p>", "<p>Committed</p>",
                                "<p>Tedious</p>", "<p>Dreary</p>"],
                    solution_en: "<p>3.(b) <br><strong>Committed </strong>- prepared to give a lot of your time and attention to something because you believe it is right or important ; devoted or resolute.<br><strong>Dedicated </strong>- Devoted or committed to a task or purpose.<br><strong>Boring </strong>- not at all interesting or dull.<br><strong>Tedious </strong>- boring and lasting for a long time.<br><strong>Dreary </strong>- not at all interesting or attractive, boring.</p>",
                    solution_hi: "<p>3.(b) <br><strong>Committed </strong>(प्रतिबद्ध) - prepared to give a lot of your time and attention to something because you believe it is right or important ; devoted or resolute.<br><strong>Dedicated </strong>(समर्पित) - Devoted or committed to a task or purpose.<br><strong>Boring </strong>(उबाऊ) - not at all interesting or dull.<br><strong>Tedious </strong>(थकाऊ) - boring and lasting for a long time.<br><strong>Dreary </strong>(उदास) - not at all interesting or attractive, boring.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "7",
                    question_en: "<p>4. Select the most appropriate synonym of the given word. <br>Prompt</p>",
                    question_hi: "<p>4.Select the most appropriate synonym of the given word. <br>Prompt</p>",
                    options_en: ["<p>Quick</p>", "<p>Gradual</p>", 
                                "<p>Slow</p>", "<p>Lengthy</p>"],
                    options_hi: ["<p>Quick</p>", "<p>Gradual</p>",
                                "<p>Slow</p>", "<p>Lengthy</p>"],
                    solution_en: "<p>4.(a) <br><strong>Quick </strong>- done with speed, taking or lasting a short time <br><strong>Prompt </strong>- immediate or done without delay <br><strong>Gradual </strong>- happening slowly or over a long period of time<br><strong>Lengthy </strong>- very long</p>",
                    solution_hi: "<p>4.(a) <br><strong>Quick </strong>(त्वरित) - done with speed, taking or lasting a short time <br><strong>Prompt </strong>(तुरंत) - immediate or done without delay <br><strong>Gradual </strong>(धीरे-धीरे) - happening slowly or over a long period of time<br><strong>Lengthy </strong>(लंबा) - very long</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "7",
                    question_en: "<p>5. Select the most appropriate synonym of the given word. <br>Yield (n)</p>",
                    question_hi: "<p>5. Select the most appropriate synonym of the given word. <br>Yield (n)</p>",
                    options_en: ["<p>Plantation</p>", "<p>Garden</p>", 
                                "<p>Orchard</p>", "<p>Harvest</p>"],
                    options_hi: ["<p>Plantation</p>", "<p>Garden</p>",
                                "<p>Orchard</p>", "<p>Harvest</p>"],
                    solution_en: "<p>5.(d) <br><strong>Harvest </strong>- Yield. <br><strong>Plantation</strong>- a large area of land, especially in a hot country, where tea, cotton, tobacco, etc. are grown. <br><strong>Garden</strong>- a piece of land next to a house where flowers and vegetables can be grown, usually with a piece of grass. <br><strong>Orchard</strong>- a piece of land on which fruit trees are grown.</p>",
                    solution_hi: "<p>5.(d) <br><strong>Harvest </strong>(फसल) - Yield. <br><strong>Plantation </strong>(वृक्षारोपण) - a large area of land, especially in a hot country, where tea, cotton, tobacco, etc. are grown. <br><strong>Garden </strong>(बगीचा) - a piece of land next to a house where flowers and vegetables can be grown, usually with a piece of grass. <br><strong>Orchard </strong>(फलोद्यान) - a piece of land on which fruit trees are grown.</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "7",
                    question_en: "<p>6. Select the most appropriate synonym of the given word. <br>Iota</p>",
                    question_hi: "<p>6. Select the most appropriate synonym of the given word. <br>Iota</p>",
                    options_en: ["<p>Bit</p>", "<p>Box</p>", 
                                "<p>Lot</p>", "<p>Bag</p>"],
                    options_hi: ["<p>Bit</p>", "<p>Box</p>",
                                "<p>Lot</p>", "<p>Bag</p>"],
                    solution_en: "<p>6.(a) <br><strong>Bit </strong>- slightly or a little<br><strong>Iota </strong>- a very small amount <br><strong>Box </strong>- a square or rectangular container for solid objects <br><strong>Lot </strong>- a large amount or number of things or people <br><strong>Bag </strong>- a strong container made of paper or thin plastic that opens at the top</p>",
                    solution_hi: "<p>6.(a) <br><strong>Bit </strong>(थोड़ा सा) - slightly or a little<br><strong>Iota </strong>(रत्ती भर) - a very small amount <br><strong>Box</strong> (डिब्बा) - a square or rectangular container for solid objects <br><strong>Lot </strong>(अत्यधिक) - a large amount or number of things or people <br><strong>Bag </strong>(थैला) - a strong container made of paper or thin plastic that opens at the top</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "7",
                    question_en: "<p>7. Select the most appropriate synonym of the given word. <br>Transmit</p>",
                    question_hi: "<p>7. Select the most appropriate synonym of the given word. <br>Transmit</p>",
                    options_en: ["<p>Convey</p>", "<p>Accept</p>", 
                                "<p>Catch</p>", "<p>Receive</p>"],
                    options_hi: ["<p>Convey</p>", "<p>Accept</p>",
                                "<p>Catch</p>", "<p>Receive</p>"],
                    solution_en: "<p>7.(a) <br><strong>Convey </strong>- to make ideas, thoughts, feelings, etc. known to somebody; transfer <br><strong>Transmit</strong> - to send out or to pass on television or radio programs, electronic signals, etc.<br><strong>Accept </strong>- to agree to take something that somebody offers you<br><strong>Catch </strong>- to take hold of something that is moving, usually with your hand or hands</p>",
                    solution_hi: "<p>7.(a) <br><strong>Convey </strong>(पहुँचाना) - to make ideas, thoughts, feelings, etc. known to somebody; transfer <br><strong>Transmit </strong>(प्रसारित करना) - to send out or to pass on television or radio programs, electronic signals, etc.<br><strong>Accept </strong>(स्वीकार करना) - to agree to take something that somebody offers you<br><strong>Catch </strong>(पकड़ना) - to take hold of something that is moving, usually with your hand or hands</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "7",
                    question_en: "<p>8. Select the most appropriate synonym of the given word. <br>Numerous</p>",
                    question_hi: "<p>8. Select the most appropriate synonym of the given word. <br>Numerous</p>",
                    options_en: ["<p>Totalled</p>", "<p>Few</p>", 
                                "<p>Several</p>", "<p>Numbered</p>"],
                    options_hi: ["<p>Totalled</p>", "<p>Few</p>",
                                "<p>Several</p>", "<p>Numbered</p>"],
                    solution_en: "<p>8.(c) <br><strong>Several</strong> - more than two but not very many<br><strong>Numerous</strong> - existing in large numbers <br><strong>Totalled </strong>- comprising the whole<br><strong>Few </strong>- a small number of <br><strong>Numbered </strong>- to give a number to something</p>",
                    solution_hi: "<p>8.(c) <br><strong>Several </strong>(अनेक) - more than two but not very many<br><strong>Numerous </strong>(अनेक) - existing in large numbers <br><strong>Totalled </strong>(कुल) - comprising the whole<br><strong>Few </strong>(कुछ) - a small number of <br><strong>Numbered </strong>(क्रमांकित) - to give a number to something</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "7",
                    question_en: "<p>9. Select the most appropriate synonym of the given word. <br>Chaos</p>",
                    question_hi: "<p>9. Select the most appropriate synonym of the given word. <br>Chaos</p>",
                    options_en: ["<p>Confusion</p>", "<p>Fusion</p>", 
                                "<p>Tension</p>", "<p>Dirt</p>"],
                    options_hi: ["<p>Confusion</p>", "<p>Fusion</p>",
                                "<p>Tension</p>", "<p>Dirt</p>"],
                    solution_en: "<p>9.(a) <br><strong>Confusion</strong>- the state of not being able to think clearly or not understanding something<br><strong>Chaos</strong>- a state of great disorder; disarray; confusion<br><strong>Fusion</strong>- the process of joining different things together to form one<br><strong>Tension</strong>- the condition of not being able to relax because you are worried <br><strong>Dirt</strong>- a substance that is not clean, such as dust or mud</p>",
                    solution_hi: "<p>9.(a) <br><strong>Confusion </strong>(भ्रम) - the state of not being able to think clearly or not understanding something<br><strong>Chaos </strong>(अराजकता) - a state of great disorder; disarray; confusion<br><strong>Fusion</strong> (संलयन) - the process of joining different things together to form one<br><strong>Tension </strong>(तनाव) - the condition of not being able to relax because you are worried <br><strong>Dirt </strong>(धूल) - a substance that is not clean, such as dust or mud</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "7",
                    question_en: "<p>10. Select the most appropriate synonym of the given word. <br>Rejuvenate</p>",
                    question_hi: "<p>10. Select the most appropriate synonym of the given word. <br>Rejuvenate</p>",
                    options_en: ["<p>Retell</p>", "<p>Retake</p>", 
                                "<p>Reset</p>", "<p>Refresh</p>"],
                    options_hi: ["<p>Retell</p>", "<p>Retake</p>",
                                "<p>Reset</p>", "<p>Refresh</p>"],
                    solution_en: "<p>10.(d)<br><strong>Refresh</strong>- to make somebody/something feel less tired or to give new energy<br><strong>Rejuvenate</strong>- to make somebody/something feel or look younger; revitalize ; refresh<br><strong>Retell</strong>- tell again or differently.<br><strong>Retake</strong>- take something again.<br><strong>Reset</strong>- set again or differently.</p>",
                    solution_hi: "<p>10.(d) <br><strong>Refresh </strong>(तरोताज़ा करना) - to make somebody/something feel less tired or to give new energy<br><strong>Rejuvenate </strong>(पुनर्जीवित करना) - to make somebody/something feel or look younger; revitalize ; refresh<br><strong>Retell </strong>(दोहराना) - tell again or differently.<br><strong>Retake </strong>(पुन: लेना) - take something again.<br><strong>Reset </strong>(पुनर्स्थापित करना) - set again or differently.</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "7",
                    question_en: "<p>11. Select the most appropriate synonym of the given word. <br>Adjust</p>",
                    question_hi: "<p>11. Select the most appropriate synonym of the given word. <br>Adjust</p>",
                    options_en: ["<p>Change</p>", "<p>Shift</p>", 
                                "<p>Adapt</p>", "<p>Move</p>"],
                    options_hi: ["<p>Change</p>", "<p>Shift</p>",
                                "<p>Adapt</p>", "<p>Move</p>"],
                    solution_en: "<p>11.(c) <br><strong>Adapt </strong>- to become familiar with a new situation and to change your behavior accordingly <br><strong>Adjust </strong>- to alter something slightly; become used to new situation; modify <br><strong>Shift </strong>- to move or be moved from one position or place to another <br><strong>Move </strong>- to change position or to put something in a different position</p>",
                    solution_hi: "<p>11.(c) <br><strong>Adapt </strong>(अनुकूल बनाना) - to become familiar with a new situation and to change your behavior accordingly <br><strong>Adjust </strong>(समायोजित करना) - to alter something slightly; become used to new situation; modify <br><strong>Shift </strong>(खिसकना ) - to move or be moved from one position or place to another <br><strong>Move </strong>(हिलना) - to change position or to put something in a different position</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "7",
                    question_en: "<p>12. Select the most appropriate synonym of the given word. <br>Slack</p>",
                    question_hi: "<p>12. Select the most appropriate synonym of the given word. <br>Slack</p>",
                    options_en: ["<p>Active</p>", "<p>Lively</p>", 
                                "<p>Careless</p>", "<p>Keen</p>"],
                    options_hi: ["<p>Active</p>", "<p>Lively</p>",
                                "<p>Careless</p>", "<p>Keen</p>"],
                    solution_en: "<p>12.(c) <br><strong>Careless </strong>- not giving sufficient attention to avoid errors; sloppy<br><strong>Slack </strong>- loose, not tightly stretched<br><strong>Active </strong>- involved in activity, lively<br><strong>Keen </strong>- very interested in something</p>",
                    solution_hi: "<p>12.(c)<br><strong>Careless</strong> (लापरवाह) - not giving sufficient attention to avoid errors; sloppy<br><strong>Slack </strong>(ढीला) - loose, not tightly stretched<br><strong>Active </strong>(सक्रिय) - involved in activity, lively<br><strong>Keen </strong>(उत्सुक) - very interested in something</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "7",
                    question_en: "13. Select the most appropriate synonym of the given word. <br />Munch ",
                    question_hi: "13. Select the most appropriate synonym of the given word. <br />Munch ",
                    options_en: [" Chew ", " Flew ", 
                                " Burp ", " Drew "],
                    options_hi: [" Chew ", " Flew ",
                                " Burp ", " Drew "],
                    solution_en: "<p>13.(a) <br><strong>Chew </strong>- to bite something continuously with the back teeth<br><strong>Munch </strong>- to bite and eat something noisily<br><strong>Flew </strong>- to move through the air <br><strong>Burp</strong>- to make a noise with the mouth when air rises from the stomach and is forced out<br><strong>Drew </strong>- to do a picture or diagram of something with a pencil, pen, etc.</p>",
                    solution_hi: "<p>13.(a) <br><strong>Chew </strong>(चबाना) - to bite something continuously with the back teeth<br><strong>Munch </strong>(कुतरना) - to bite and eat something noisily<br><strong>Flew </strong>(उड़ा) - to move through the air <br><strong>Burp </strong>(डकार लेना) - to make a noise with the mouth when air rises from the stomach and is forced out<br><strong>Drew</strong> (खींचा) - to do a picture or diagram of something with a pencil, pen, etc.</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "7",
                    question_en: "<p>14. Select the most appropriate synonym of the given word. <br>Termination</p>",
                    question_hi: "<p>14. Select the most appropriate synonym of the given word. <br>Termination</p>",
                    options_en: ["<p>Consolation</p>", "<p>Conviction</p>", 
                                "<p>Conduction</p>", "<p>Conclusion</p>"],
                    options_hi: ["<p>Consolation</p>", "<p>Conviction</p>",
                                "<p>Conduction</p>", "<p>Conclusion</p>"],
                    solution_en: "<p>14.(d) <br><strong>Conclusion </strong>- an end to something <br><strong>Termination </strong>- the act of ending something<br><strong>Consolation </strong>- a thing or person that makes you feel better when you are sad <br><strong>Conviction </strong>- the action of finding somebody guilty of a crime in a court of law <br><strong>Conduction </strong>- the process by which heat or electricity passes through a material</p>",
                    solution_hi: "<p>14.(d) <br><strong>Conclusion </strong>(निष्कर्ष) - an end to something <br><strong>Termination </strong>(समाप्ति) - the act of ending something<br><strong>Consolation </strong>(सांत्वना) - a thing or person that makes you feel better when you are sad <br><strong>Conviction </strong>(दोषसिद्धि) - the action of finding somebody guilty of a crime in a court of law <br><strong>Conduction </strong>(चालकता) - the process by which heat or electricity passes through a material</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "7",
                    question_en: "<p>15. Select the most appropriate synonym of the given word. <br>Debilitate</p>",
                    question_hi: "<p>15. Select the most appropriate synonym of the given word. <br>Debilitate</p>",
                    options_en: ["<p>Vitalise</p>", "<p>Animate</p>", 
                                "<p>Weaken</p>", "<p>Sustain</p>"],
                    options_hi: ["<p>Vitalise</p>", "<p>Animate</p>",
                                "<p>Weaken</p>", "<p>Sustain</p>"],
                    solution_en: "<p>15.(c) <br><strong>Weaken</strong>- to become less strong<br><strong>Debilitate</strong>- to make somebody&rsquo;s body or mind weaker <br><strong>Vitalise</strong>- give strength and energy to. <br><strong>Animate</strong>- to make something have more life and energy <br><strong>Sustain</strong>- to keep somebody/something alive or healthy</p>",
                    solution_hi: "<p>15.(c) <br><strong>Weaken </strong>(कमजोर होना) - to become less strong<br><strong>Debilitate</strong> (दुर्बल करना) - to make somebody&rsquo;s body or mind weaker <br><strong>Vitalise </strong>(शक्ति या ऊर्जा देना) - give strength and energy to. <br><strong>Animate </strong>(सजीव करना या जान डालना)) - to make something have more life and energy <br><strong>Sustain </strong>(बनाए/जीवित/स्वस्थ रखना) - to keep somebody/something alive or healthy</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "16",
                    section: "7",
                    question_en: "<p>16. Select the most appropriate synonym of the given word. <br>Celebrate</p>",
                    question_hi: "<p>16. Select the most appropriate synonym of the given word. <br>Celebrate</p>",
                    options_en: ["<p>Honour</p>", "<p>Humiliate</p>", 
                                "<p>Publish</p>", "<p>Circulate</p>"],
                    options_hi: ["<p>Honour</p>", "<p>Humiliate</p>",
                                "<p>Publish</p>", "<p>Circulate</p>"],
                    solution_en: "<p>16.(a) <br><strong>Honour</strong>- the respect from other people that a person, country, etc. gets because of high standards of moral character<br><strong>Celebrate</strong>- to do something to show that you are happy about something that has happened<br><strong>Humiliate</strong>- to make somebody feel very embarrassed<br><strong>Publish</strong>- to prepare and print a book, magazine, etc<br><strong>Circulate</strong>- to go or be passed from one person to another</p>",
                    solution_hi: "<p>16.(a) <br><strong>Honour </strong>(सम्मान) - the respect from other people that a person, country, etc. gets because of high standards of moral character<br><strong>Celebrate </strong>(उत्सव मनाना) - to do something to show that you are happy about something that has happened<br><strong>Humiliate </strong>(अपमानित करना) - to make somebody feel very embarrassed<br><strong>Publish </strong>(प्रकाशित करना) - to prepare and print a book, magazine, etc<br><strong>Circulate </strong>(प्रसारित करना) - to go or be passed from one person to another</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "17",
                    section: "7",
                    question_en: "<p>17. Select the most appropriate synonym of the given word. <br>Wander</p>",
                    question_hi: "<p>17. Select the most appropriate synonym of the given word. <br>Wander</p>",
                    options_en: ["<p>Run</p>", "<p>Think</p>", 
                                "<p>Roam</p>", "<p>Sing</p>"],
                    options_hi: ["<p>Run</p>", "<p>Think</p>",
                                "<p>Roam</p>", "<p>Sing</p>"],
                    solution_en: "<p>17.(c) <br><strong>Roam </strong>- to walk or travel with no particular plan or aim<br><strong>Wander </strong>- to walk around slowly in a relaxed way or without any clear purpose or direction.<br><strong>Think </strong>- to have a particular idea or opinion about something/somebody<br><strong>Run </strong>- going faster than a walk</p>",
                    solution_hi: "<p>17.(c) <br><strong>Roam </strong>- (घूमना) - to walk or travel with no particular plan or aim<br><strong>Wander </strong>(भटकना) - to walk around slowly in a relaxed way or without any clear purpose or direction.<br><strong>Think </strong>(सोचना) - to have a particular idea or opinion about something/somebody<br><strong>Run </strong>(दौड़ना) - going faster than a walk</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "18",
                    section: "7",
                    question_en: "<p>18. Select the most appropriate synonym of the given word. <br>Grumble</p>",
                    question_hi: "<p>18. Select the most appropriate synonym of the given word. <br>Grumble</p>",
                    options_en: ["<p>Grin</p>", "<p>Call</p>", 
                                "<p>Groan</p>", "<p>Ring</p>"],
                    options_hi: ["<p>Grin</p>", "<p>Call</p>",
                                "<p>Groan</p>", "<p>Ring</p>"],
                    solution_en: "<p>18.(c) <br><strong>Groan </strong>- to make a deep sad sound because you are in pain; say something in miserable tone<br><strong>Grumble </strong>- to complain in a bad-tempered way ; groan<br><strong>Grin </strong>- to give a broad smile <br><strong>Ring </strong>- a piece of jewelry that you wear on your finger</p>",
                    solution_hi: "<p>18.(c) <br><strong>Groan </strong>(कराहना) - to make a deep sad sound because you are in pain; say something in miserable tone<br><strong>Grumble </strong>(बड़बड़ाना) - to complain in a bad-tempered way ; groan<br><strong>Grin </strong>(दाँत दिखाना) - to give a broad smile <br><strong>Ring </strong>(अंगूठी) - a piece of jewelry that you wear on your finger</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "19",
                    section: "7",
                    question_en: "<p>19. Select the most appropriate synonym of the given word. <br>Testify</p>",
                    question_hi: "<p>19. Select the most appropriate synonym of the given word. <br>Testify</p>",
                    options_en: ["<p>Affirm</p>", "<p>Oppose</p>", 
                                "<p>Disprove</p>", "<p>Invalidate</p>"],
                    options_hi: ["<p>Affirm</p>", "<p>Oppose</p>",
                                "<p>Disprove</p>", "<p>Invalidate</p>"],
                    solution_en: "<p>19.(a) <br><strong>Affirm </strong>- to say formally or clearly that something is true<br><strong>Testify </strong>- to make a formal statement that something is true <br><strong>Oppose </strong>- to disagree with somebody&rsquo;s beliefs, actions or plans and to try to change or stop them <br><strong>Disprove </strong>- to show that something is not true <br><strong>Invalidate </strong>- to show that an idea, a story, an argument, etc. is wrong</p>",
                    solution_hi: "<p>19.(a) <br><strong>Affirm </strong>(पुष्टि करना) - to say formally or clearly that something is true<br><strong>Testify </strong>(गवाही देना) - to make a formal statement that something is true <br><strong>Oppose </strong>(विरोध करना) - to disagree with somebody&rsquo;s beliefs, actions or plans and to try to change or stop them <br><strong>Disprove </strong>(खंडन करना) - to show that something is not true <br><strong>Invalidate </strong>(अमान्य करना) - to show that an idea, a story, an argument, etc. is wrong</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "20",
                    section: "misc",
                    question_en: "<p>20. Select the most appropriate synonym of the given word. <br>Abolish</p>",
                    question_hi: "<p>20. Select the most appropriate synonym of the given word. <br>Abolish</p>",
                    options_en: ["<p>Eliminate</p>", "<p>Establish</p>", 
                                "<p>Continue</p>", "<p>Introduce</p>"],
                    options_hi: ["<p>Eliminate</p>", "<p>Establish</p>",
                                "<p>Continue</p>", "<p>Introduce</p>"],
                    solution_en: "<p>20.(a)<br><strong>Eliminate </strong>- to remove somebody/something that is not wanted<br><strong>Abolish </strong>- to end law or system officially <br><strong>Establish</strong> - to start or create an organization, a system, etc.<br><strong>Continue </strong>- to keep doing something without stopping<br><strong>Introduce</strong> - to bring in something new, use something, or take something to a place for the first time</p>",
                    solution_hi: "<p>20.(a) <br><strong>Eliminate </strong>(हटाना या निकाल देना) - to remove somebody/something that is not wanted<br><strong>Abolish </strong>(समाप्त या उन्मूलन करना) - to end law or system officially <br><strong>Establish </strong>(स्थापित करना) - to start or create an organization, a system, etc.<br><strong>Continue </strong>(जारी रखना) - to keep doing something without stopping<br><strong>Introduce</strong> (प्रस्तुत कराना) - to bring in something new, use something, or take something to a place for the first time</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>