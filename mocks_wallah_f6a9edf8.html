<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">25:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 40</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">40</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 25 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["18"] = {
                name: "General Awareness",
                start: 0,
                end: 38
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="18">General Awareness</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 39,
                end: 39
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "18",
                    question_en: "1. The world’s largest drainage basin is:",
                    question_hi: "1. विश्व का सबसे बड़ा जल निकासी बेसिन कौन सा है?",
                    options_en: [" Amazon Basin ", " Ganga Basin ", 
                                " Nile Basin ", " Mississippi Basin "],
                    options_hi: [" अमेज़ॅन बेसिन", " गंगा बेसिन",
                                " नील बेसिन ", " मिसिसिपी बेसिन"],
                    solution_en: "1.(a)  The world’s largest drainage basin is the Amazon Basin. It is located in Bolivia, Brazil, Colombia, Ecuador, French Guiana (France), Guyana, Peru, Suriname, and Venezuela. Ganga Basin(India), Nile Basin( Africa) , Mississippi Basin(United States).",
                    solution_hi: "1.(a)  दुनिया का सबसे बड़ा जल निकासी बेसिन अमेज़न बेसिन है। यह बोलीविया, ब्राजील, कोलंबिया, इक्वाडोर, फ्रेंच गयाना (फ्रांस), गुयाना, पेरू, सूरीनाम और वेनेजुएला में स्थित है। गंगा बेसिन (भारत), नील बेसिन (अफ्रीका), मिसिसिपी बेसिन (संयुक्त राज्य)।",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "2",
                    section: "18",
                    question_en: "2. The Postal Index Number (PIN) is a six digit code used by Indian postal department, what does the first two digit of the code (taken together) stands for:",
                    question_hi: "2. पोस्टल इंडेक्स नंबर (पिन) भारतीय डाक विभाग द्वारा इस्तेमाल किया जाने वाला छह अंकों का कोड है, कोड के पहले दो अंक (एक साथ लिया गया) का क्या मतलब है",
                    options_en: [" District", " Delivery Post office ", 
                                " Municipal corporation ", " Sub region or one of the postal circles "],
                    options_hi: [" जिला", " डिलीवरी पोस्ट ऑफिस",
                                " नगर निगम", " उप क्षेत्र या डाक मंडलों में से एक"],
                    solution_en: "2.(d) The first digit of PIN  Indicates the zone, Second digits Indicates the sub zone, the first two digits of the code (taken together) stands for the Sub region or one of the postal circles. The final three digits are assigned to individual post offices within the sorting district.",
                    solution_hi: "2.(d) PIN का पहला अंक क्षेत्र को इंगित करता है, दूसरा अंक उप क्षेत्र को इंगित करता है, कोड के पहले दो अंक (एक साथ लिया गया) उप क्षेत्र या डाक मंडलों में से एक के लिए है। अंतिम तीन अंक छँटाई जिले के भीतर अलग-अलग डाकघरों को सौंपे जाते हैं।",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "3",
                    section: "18",
                    question_en: "3. Shri Guru Gobind Singh Ji founded the ‘Khalsa Panth’ in the year _______.",
                    question_hi: "3. श्री गुरु गोबिंद सिंह जी ने _________ में \'खालसा पंथ\' की स्थापना की।",
                    options_en: [" 1701", " 1697", 
                                " 1705", " 1699"],
                    options_hi: [" 1701", " 1697",
                                " 1705", " 1699"],
                    solution_en: "3.(d) Shri Guru Gobind Singh Ji founded the ‘Khalsa Panth’ in the year 1699 AD.The Sikhs celebrate Baisakhi every year on 13th April as the birth of Khalsa Panth.",
                    solution_hi: "3.(d)  श्री गुरु गोबिंद सिंह जी ने 1699 ईस्वी में \'खालसा पंथ\' की स्थापना की थी। सिख हर साल 13 अप्रैल को खालसा पंथ के जन्म के रूप में बैसाखी मनाते हैं।",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "4",
                    section: "18",
                    question_en: "4. An international treaty designed to protect human health and eliminate the use of mercury was signed in __________during 2013.",
                    question_hi: "4. मानव स्वास्थय की सुरक्षा और पारे की उपयोग को चलन से बाहर करने के लिए तयारी की गई एक अंतरास्ट्रीय संधि पर 2013 के दौरान ____________ में हस्ताक्षर किये गए थे।  ",
                    options_en: [" Tianjin, China ", " Minamata, Japan ", 
                                " Betio, Kiribati ", " The Hague, Netherlands "],
                    options_hi: [" तिआनजिन, चीन", " मिनामाता, जापान",
                                " बेटियो, किरिबाती", " हेग, नीदरलैंड्स"],
                    solution_en: "4.(b) An international treaty designed to protect human health and eliminate the use of mercury was signed during 2013 in Minamata Japan. Minamata disease is a methylmercury poisoning with neurological symptoms and caused by the daily consump- tion of large quantities of fish and shellfish.",
                    solution_hi: "4.(b) मानव स्वास्थ्य की रक्षा और पारे के उपयोग को समाप्त करने के लिए डिज़ाइन की गई एक अंतर्राष्ट्रीय संधि पर 2013 के दौरान मिनामाता जापान में हस्ताक्षर किए गए थे। मिनामाता रोग न्यूरोलॉजिकल लक्षणों के साथ एक मिथाइलमेरकरी विषाक्तता है और बड़ी मात्रा में मछली और शंख की दैनिक खपत के कारण होता है।",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "5",
                    section: "18",
                    question_en: "5. What is the function of insulin in the human body?",
                    question_hi: "5. मानव शरीर में इंसुलिन का क्या कार्य है?",
                    options_en: [" It regulates how the body uses and stores glucose and fat ", " It supplies filtered blood to the heart ", 
                                " It supplies oxygen to the lungs ", " It regulates the flow of blood  "],
                    options_hi: [" यह कैसे शरीर का उपयोग करता है और ग्लूकोज और वसा स्टोर को नियंत्रित करता है", " यह हृदय को फ़िल्टर रक्त की आपूर्ति करता है",
                                " यह फेफड़ों को ऑक्सीजन की आपूर्ति करता है", " यह रक्त के प्रवाह को नियंत्रित करता है"],
                    solution_en: "5.(a)  The function of insulin in the human body is that  it regulates how the body uses and stores glucose and fat. Insulin deficiency results in hyperglycemia, Type 1 diabetes. Insulin deficiency leads to accelerated protein catabolism as well as diminished protein synthesis.",
                    solution_hi: "5.(a) मानव शरीर में इंसुलिन का कार्य यह है, कि यह नियंत्रित करता है कि शरीर ग्लूकोज और वसा का उपयोग और भंडारण कैसे करता है। इंसुलिन की कमी से हाइपरग्लेसेमिया, टाइप 1 मधुमेह हो जाता है। इंसुलिन की कमी से त्वरित प्रोटीन अपचय और साथ ही कम प्रोटीन संश्लेषण होता है।",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "6",
                    section: "18",
                    question_en: "6. India’s constitutional provision of ‘Indirect election of Rajya Sabha members’ was adopted from which country?’",
                    question_hi: "6. भारत के \'राज्यसभा सदस्यों के अप्रत्यक्ष चुनाव\' के संवैधानिक प्रावधान को किस देश से अपनाया गया था?",
                    options_en: [" Germany  ", " South Africa ", 
                                " USA ", " England "],
                    options_hi: [" जर्मनी", " दक्षिण अफ्रीका",
                                " अमेरीका", " इंगलैंड"],
                    solution_en: "6.(b) India’s constitutional provision of ‘Indirect election of Rajya Sabha members’ was adopted from South Africa and also an amendment of the Constitution. Fundamental Rights suspended during Emergency are borrowed from Germany. Borrowed features from the UK are Single Citizenship, Cabinet System, Rule of Law, Parliamentary Government,Bicameralism. USA- Fundamental Rights, Judicial review, Impeachment of President, Removal of Supreme Court and High Court judges",
                    solution_hi: "6.(b) भारत के \'राज्य सभा सदस्यों के अप्रत्यक्ष चुनाव\' के संवैधानिक प्रावधान को दक्षिण अफ्रीका से अपनाया गया था और संविधान में संशोधन भी किया गया था। आपातकाल के दौरान निलंबित किए गए मौलिक अधिकार जर्मनी से अपनाया गया हैं। UK से उधार ली गई विशेषताएं एकल नागरिकता, कैबिनेट प्रणाली, कानून का शासन, संसदीय सरकार, द्विसदनीयता हैं। USA- मौलिक अधिकार, न्यायिक समीक्षा, राष्ट्रपति का महाभियोग, सर्वोच्च न्यायालय और उच्च न्यायालय के न्यायाधीशों को हटाना।",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "7",
                    section: "18",
                    question_en: "7. A  flame is made up of three parts: the innermost part, the middle part and the outermost part. Why is the innermost part black?",
                    question_hi: "7. ज्वाला तीन भागों से बनी होती है: अंतरतम भाग, मध्य भाग और बाहरी भाग। अंतरतम भाग काला क्यों होता है?",
                    options_en: [" Because of complete combustion of fuel ", " Because of the presence of unburnt carbon particles ", 
                                " Because of the incomplete combustion of fuel ", " Because of the presence of oxygen "],
                    options_hi: [" ईंधन के पूर्ण दहन के कारण", " जले हुए कार्बन कणों की उपस्थिति के कारण",
                                " ईंधन के अधूरे दहन के कारण", " ऑक्सीजन की उपस्थिति के कारण"],
                    solution_en: "7.(b) A  flame is made up of three parts: the innermost part, the middle part and the outermost part. The innermost part black because of the presence of unburnt carbon particles.",
                    solution_hi: "7.(b) ज्वाला तीन भागों से बनी होती है: अंतरतम भाग, मध्य भाग और बाहरी भाग। असिंचित कार्बन कणों की उपस्थिति के कारण अंतरतम भाग काला हो जाता है।",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "8",
                    section: "18",
                    question_en: "8. _________ is a famous Kathak dancer.",
                    question_hi: "8. _________ एक प्रसिद्ध कथक नर्तक है।",
                    options_en: [" Geeta Chandran ", " Padma Subrahmanyam ", 
                                " Josyula Seetharamaiah ", " Birju Maharaj "],
                    options_hi: [" गीता चंद्राणी", " पद्मा सुब्रह्मण्यम",
                                " जोस्युला सीतारमैया", " बिरजू महाराज"],
                    solution_en: "8.(d)  Birju Maharaj is a famous Kathak dancer. Geeta Chandran is an Indian Bharatanatyam dancer and vocalist. Padma Subrahmanyam is Indian Classical Bharatnatyam Dancer. Josyula Seetharamaiahis is a kuchipudi dancer.",
                    solution_hi: "8.(d) बिरजू महाराज एक प्रसिद्ध कथक नर्तक हैं। गीता चंद्रन एक भारतीय भरतनाट्यम नृत्यांगना और गायिका हैं। पद्मा सुब्रह्मण्यम भारतीय शास्त्रीय भरतनाट्यम नृत्यांगना हैं। जोस्युला सीतारमैया एक कुचिपुड़ी नृत्यांगना हैं।",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "9",
                    section: "18",
                    question_en: "9. In which year was The Right of Children to Free and Compulsory Education Act introduced in India?",
                    question_hi: "9. भारत में बच्चों का मुफ्त और अनिवार्य शिक्षा का अधिकार अधिनियम किस वर्ष पेश किया गया था?",
                    options_en: [" 2009", " 2011", 
                                " 2002", " 2005"],
                    options_hi: [" 2009", " 2011",
                                " 2002", " 2005"],
                    solution_en: "9.(a)  In 2009 was The Right of Children to Free and Compulsory Education Act introduced in India. An Act to provide for free and compulsory education to all children of the age of six to fourteen years.",
                    solution_hi: "9.(a) 2009 में भारत में बच्चों के नि:शुल्क और अनिवार्य शिक्षा का अधिकार अधिनियम पेश किया गया था। छह से चौदह वर्ष की आयु के सभी बच्चों को मुफ्त और अनिवार्य शिक्षा प्रदान करने वाला अधिनियम।",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "10",
                    section: "18",
                    question_en: "10. Which of the following countries assisted India to launch its first artificial satellite in 1975?",
                    question_hi: "10. निम्नलिखित में से किस देश ने 1975 में अपना पहला कृत्रिम उपग्रह लॉन्च करने में भारत की सहायता की?",
                    options_en: [" Soviet Union ", " France ", 
                                " USA ", " UK "],
                    options_hi: [" सोवियत संघ", " फ्रांस",
                                " अमेरिका ", " इंग्लैंड "],
                    solution_en: "10.(a) Soviet Union(USSR) assisted India to launch its first artificial satellite(Aryabhata) by Soviet Kosmos-3M rocket  in 1975. In 1957 USSR launched Sputnik, the first artificial satellite to orbit Earth.",
                    solution_hi: "10.(a) सोवियत संघ (USSR) ने 1975 में सोवियत कॉसमॉस -3M रॉकेट द्वारा अपना पहला कृत्रिम उपग्रह (आर्यभट्ट) लॉन्च करने में भारत की सहायता की। 1957 में USSR ने पृथ्वी की परिक्रमा करने वाला पहला कृत्रिम उपग्रह स्पुतनिक लॉन्च किया।",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "11",
                    section: "18",
                    question_en: "<p>11. The study of the universe is known as_____.</p>",
                    question_hi: "<p>11. ब्रह्मांड के अध्ययन को_______ के रूप में जाना जाता है</p>",
                    options_en: ["<p>Anthropology</p>", "<p>Etymology</p>", 
                                "<p>Cosmology</p>", "<p>Anatomy</p>"],
                    options_hi: ["<p>नृविज्ञान</p>", "<p>व्युत्पत्ति विज्ञान</p>",
                                "<p>ब्रह्माण्डविज्ञान</p>", "<p>शरीर-रचना-विज्ञान</p>"],
                    solution_en: "<p>11.(c) The study of the universe is known as Cosmology. Anthropology is the study of people, past and present. Etymology is the study of the origins of words.Anatomy is the science that studies the structure of the body.</p>",
                    solution_hi: "<p>11.(c) ब्रह्मांड के अध्ययन को कॉस्मोलॉजी के रूप में जाना जाता है। नृविज्ञान लोगों, अतीत और वर्तमान का अध्ययन है। व्युत्पत्ति विज्ञान शब्दों की उत्पत्ति का अध्ययन है। एनाटॉमी वह विज्ञान है जो शरीर की संरचना का अध्ययन करता है।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "12",
                    section: "18",
                    question_en: "12. Who led India’s first expedition to the Antarctic in 1982?",
                    question_hi: "12. 1982 में अंटार्कटिक में भारत के पहले अभियान का नेतृत्व किसने किया था?",
                    options_en: [" Dr. APJ Abdul Kalam ", " Dr. Paramjit Singh ", 
                                " Dr. Zahoor Qasim ", " Dr. SZ Qasim "],
                    options_hi: [" डाक्टर ए.पी.जे. अब्दुल कलाम", " डॉ. परमजीत सिंह",
                                " डॉ. जहूर कासिम", " डॉ. एसजेड कासिम"],
                    solution_en: "12.(d) Dr. SZ Qasim led India’s first expedition to the Antarctic in 1982. The Indian Antarctic expeditions began in 1981. The first trip consisted of a team of 21 scientists and support staff led by Dr SZ Qasim.",
                    solution_hi: "12.(d) डॉ. एसजेड कासिम ने 1982 में अंटार्कटिक के लिए भारत के पहले अभियान का नेतृत्व किया। भारतीय अंटार्कटिक अभियान 1981 में शुरू हुआ। पहली यात्रा में डॉ एसजेड कासिम के नेतृत्व में 21 वैज्ञानिकों और सहायक कर्मचारियों की एक टीम शामिल थी।",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "13",
                    section: "18",
                    question_en: "13. Which of the following pairs is NOT correctly matched (instrument to the artist)?",
                    question_hi: "13. निम्नलिखित में से कौन सा युग्म सही सुमेलित नहीं है (कलाकार के लिए वाद्य यंत्र)",
                    options_en: [" Pakhawaj - Shiv Kumar Sharma ", " Sitar - Pt. Ravi Shankar ", 
                                " Sarod - Amjad Ali Khan ", " Tabla - Kishan Maharaj "],
                    options_hi: [" पखवाज - शिव कुमार शर्मा", " सितार - पं. रवि शंकर",
                                " सरोद - अमजद अली खान", " तबला - किशन महाराज"],
                    solution_en: "13.(a) Shiv Kumar Sharma is santoor player.",
                    solution_hi: "13.(a) शिव कुमार शर्मा संतूर वादक हैं।",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "14",
                    section: "18",
                    question_en: "14. The ‘National War Memorial’ inaugurated by Prime Minister Narendra Modi is in:",
                    question_hi: "14. प्रधान मंत्री नरेंद्र मोदी द्वारा उद्घाटन किया गया \'राष्ट्रीय युद्ध स्मारक\' कहाँ है?",
                    options_en: [" Kolkata ", " New Delhi ", 
                                " Mumbai ", " Bengaluru "],
                    options_hi: [" कोलकाता", " नई दिल्ली",
                                " मुंबई", " बेंगलुरु"],
                    solution_en: "14.(b) The ‘National War Memorial’ inaugurated(Feb 2019) by Prime Minister Narendra Modi is in New Delhi. The memorial is spread in The India Gate complex. Designed by Yogesh Chandrahasan, WeBe Design Lab, Chennai.",
                    solution_hi: "14.(b) प्रधान मंत्री नरेंद्र मोदी द्वारा उद्घाटन (फरवरी 2019) \'राष्ट्रीय युद्ध स्मारक\' नई दिल्ली में है। स्मारक इंडिया गेट परिसर में फैला हुआ है। योगेश चंद्रहासन, वेब डिज़ाइन लैब, चेन्नई द्वारा डिज़ाइन किया गया।",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "15",
                    section: "18",
                    question_en: "15. Which of the following companies owns KUKA Robotics (Industrial Robots)?",
                    question_hi: "15. निम्नलिखित में से कौन सी कंपनी KUKA रोबोटिक्स (औद्योगिक रोबोट) की मालिक है?",
                    options_en: [" Paslin Group, USA  ", " Kion Group, China ", 
                                " Hocoma Group, Switzerland ", " Midea Group, China "],
                    options_hi: [" पास्लिन ग्रुप, अमेरिका", " कियोन ग्रुप, चीन",
                                " होकोमा ग्रुप, स्विट्ज़रलैंड", " मिडिया ग्रुप, चीन"],
                    solution_en: "15.(d)  Midea Group, China  has owned KUKA Robotics (Industrial Robots) since ",
                    solution_hi: "15.(d) 2016.Midea Group, चीन के पास 2016 से KUKA रोबोटिक्स (औद्योगिक रोबोट) का स्वामित्व है।",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "16",
                    section: "18",
                    question_en: "16. Google Drive, SkyDrive and Dropbox are perfect examples of:",
                    question_hi: "16. गूगल ड्राइव, स्काईड्राइव और ड्रॉपबॉक्स किसके आदर्श उदाहरण हैं?",
                    options_en: [" internet computing  ", " virtual drives ", 
                                " virtual reality ", " cloud storage services "],
                    options_hi: [" इंटरनेट कंप्यूटिंग", " वर्चुअल ड्राइव ",
                                " वर्चुअल रियलिटी ", " क्लाउड स्टोरेज सेवाएं"],
                    solution_en: "16.(d) Google Drive, SkyDrive and Dropbox are perfect examples of cloud storage services.",
                    solution_hi: "16.(d) Google ड्राइव, स्काईड्राइव और ड्रॉपबॉक्स क्लाउड स्टोरेज सेवाओं के आदर्श उदाहरण हैं।",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "17",
                    section: "18",
                    question_en: "17. Which galaxy is the nearest to the Milky Way?",
                    question_hi: "17. आकाशगंगा के सबसे नजदीक कौन सी आकाशगंगा है?",
                    options_en: [" Whirlpool Galaxy ", " Andromeda Galaxy ", 
                                " Virgo Stellar Stream ", " Triangulum Galaxy "],
                    options_hi: [" व्हर्लपूल गैलेक्सी", " एंड्रोमेडा गैलेक्सी",
                                " विर्गो स्टेल्लर स्ट्रीम ", " त्रिकोणीय आकाशगंगा"],
                    solution_en: "17.(b) Andromeda Galaxy is the nearest to the Milky Way galaxy. It is a huge collection of Gas, dust and stars. It is a spiral galaxy.",
                    solution_hi: "17.(b) एंड्रोमेडा गैलेक्सी आकाशगंगा आकाशगंगा के सबसे नजदीक है। यह गैस, धूल और तारों का विशाल संग्रह है। यह एक सर्पिल आकाशगंगा है।",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "18",
                    section: "18",
                    question_en: "18. Which nation hosted the <math display=\"inline\"><msup><mrow><mn>14</mn></mrow><mrow><mi>t</mi><mi>h</mi></mrow></msup></math> Conference of Parties (COP) of the United Nation Convention to Combat Desertification (UNCCD)? ",
                    question_hi: "18. संयुक्त राष्ट्र कन्वेंशन टू कॉम्बैट डेजर्टिफिकेशन (UNCCD) के 14 वें सम्मेलन की पार्टियों (COP) की मेजबानी किस देश ने की?",
                    options_en: [" South Africa ", " UAE ", 
                                " China ", " India "],
                    options_hi: [" दक्षिण अफ्रीका", " संयुक्त अरब अमीरात",
                                " चीन", " भारत"],
                    solution_en: "18.(d) India hosted the <math display=\"inline\"><msup><mrow><mn>14</mn></mrow><mrow><mi>t</mi><mi>h</mi></mrow></msup></math> Conference of Parties (COP) of the United Nation Convention to Combat Desertification (UNCCD). The United Nations Convention to Combat Desertification in Those Countries Experiencing Serious Drought and/or Desertification,",
                    solution_hi: "18.(d) भारत ने मरुस्थलीकरण (UNCCD) से निपटने के लिए संयुक्त राष्ट्र सम्मेलन के पार्टियों (COP) के 14वें सम्मेलन की मेजबानी की। गंभीर सूखे और/या मरुस्थलीकरण का अनुभव करने वाले देशों में मरुस्थलीकरण का मुकाबला करने के लिए संयुक्त राष्ट्र सम्मेलन।",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "19",
                    section: "18",
                    question_en: "<p>19. In which the of the following cities was the 33 ft tall bronze statue of Swami Vivekananda inaugurated on National Youth Day in 2019?</p>",
                    question_hi: "<p>19.निम्नलिखित में से किस शहर में 2019 में राष्ट्रीय युवा दिवस पर स्वामी विवेकानंद की 33 फीट ऊंची कांस्य प्रतिमा का उद्घाटन किया गया?</p>",
                    options_en: ["<p>Vadodara, Gujarat</p>", "<p>Ranchi, Jharkhand</p>", 
                                "<p>Gwalior, Madhya Pradesh</p>", "<p>Warangal, Telangana</p>"],
                    options_hi: ["<p>वडोदरा, गुजरात</p>", "<p>रांची, झारखंड</p>",
                                "<p>ग्वालियर, मध्य प्रदेश</p>", "<p>वारंगल, तेलंगाना</p>"],
                    solution_en: "<p>19.(b) In Ranchi Lake the 33 ft tall bronze statue of Swami Vivekananda inaugurated on National Youth Day in 2019.</p>",
                    solution_hi: "<p>19.(b)&nbsp;रांची झील में स्वामी विवेकानंद की 33 फीट ऊंची कांस्य प्रतिमा का उद्घाटन 2019 में राष्ट्रीय युवा दिवस पर किया गया।</p>\n<p>&nbsp;</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "20",
                    section: "18",
                    question_en: "20. Name the scheme that was launched on 8 April 2015, under which a loan of up to Rs. 50,000 is given under sub-scheme ‘Shishu’; between Rs.50,000 to Rs. 5.0 lakh under sub-scheme ‘Kishore’; and between Rs.5.0 lakh to Rs. 10.0 lakh under sub-scheme ‘Tarun’.",
                    question_hi: "20. उस योजना का नाम बताइए जिसे 8 अप्रैल 2015 को शुरू किया गया था, जिसके तहत उप-योजना \'शिशु\' के तहत 50,000 रूपये दिए जाते हैं; उप-योजना \'किशोर\' के तहत 50,000 रुपये से 5.0 लाख रुपये के बीच मिलता है और उप-योजना \'तरुण\' के तहत 5.0 लाख से 10.0 लाख रूपये मिलता है",
                    options_en: [" Pradhan Mantri Jeeven Jyoti Bima Yojana ", " Atal Pension Yojana ", 
                                " Pradhan Mantri Surksha Bima Yojana ", " Pradhan Mantri Mudra Yojana "],
                    options_hi: [" प्रधानमंत्री जीवन ज्योति बीमा योजना (PMJJBY)", " अटल पेंशन योजना (APY)",
                                " प्रधानमंत्री सुरक्षा बीमा योजना (PMSBY)", " प्रधानमंत्री मुद्रा योजना (PMMY)"],
                    solution_en: "20.(d) Pradhan Mantri Mudra Yojana (PMMY) is a scheme set up by the Government of India (GoI) through MUDRA (a subsidiary of SIDBI) that helps in facilitating micro credit upto Rs. 10 lakh to small business owners.",
                    solution_hi: "20.(d) प्रधान मंत्री मुद्रा योजना (PMMY) भारत सरकार (GoI) द्वारा मुद्रा (SIDBI की सहायक कंपनी) के माध्यम से स्थापित एक योजना है जो रुपये तक के माइक्रो क्रेडिट की सुविधा में मदद करती है। छोटे कारोबारियों को 10 लाख।",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "21",
                    section: "18",
                    question_en: "21. ‘Nuclear Fusion’ is the process of combining two or more light nuclei. What is the required temperature (approximately) for nuclear fusion?",
                    question_hi: "21.नाभिकीय संलयन\' दो या दो से अधिक प्रकाश नाभिकों के संयोजन की प्रक्रिया है। परमाणु संलयन के लिए आवश्यक तापमान (लगभग) क्या है?",
                    options_en: [" 100 million K ", " 812 million K ", 
                                " 10 Million K ", " 810 million K "],
                    options_hi: [" 100 मिलियन केल्विन ", " 812 मिलियन केल्विन ",
                                " 10 मिलियन केल्विन ", " 810 मिलियन केल्विन "],
                    solution_en: "21.(a) Nuclear Fusion’ is the process of combining two or more light nuclei.100 Million Kelvin required temperature (approximately) for nuclear fusion. The specific type of Nuclear fusion that occurs inside of the Sun is known as proton-proton fusion.",
                    solution_hi: "21.(a) परमाणु संलयन\' दो या दो से अधिक प्रकाश नाभिकों के संयोजन की प्रक्रिया है। परमाणु संलयन के लिए 100 मिलियन केल्विन आवश्यक तापमान (लगभग) है। सूर्य के अंदर होने वाले विशिष्ट प्रकार के परमाणु संलयन को प्रोटॉन-प्रोटॉन संलयन के रूप में जाना जाता है।",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "22",
                    section: "18",
                    question_en: "22. What is the meaning of ‘Lokayukta’ in the Constitution of India?",
                    question_hi: "22. भारत के संविधान में \'लोकायुक्त\' का क्या अर्थ है?",
                    options_en: [" A body created to look into the complaints made with respect to frauds in the financial sector  ", " A body operating at the state level to investigate an individual\'s complaint against public servants or any politician with respect to corruption ", 
                                " A body operating at the central level to investigate the civil servant or politician with respect to complaint made against them for corruption ", " It is also called ‘Ombudsman’ "],
                    options_hi: [" वित्तीय क्षेत्र में धोखाधड़ी के संबंध में की गई शिकायतों को देखने के लिए गठित एक निकाय", " भ्रष्टाचार के संबंध में लोक सेवकों या किसी राजनेता के खिलाफ किसी व्यक्ति की शिकायत की जांच करने के लिए राज्य स्तर पर संचालित एक निकाय",
                                " भ्रष्टाचार के लिए उनके खिलाफ की गई शिकायत के संबंध में सिविल सेवक या राजनेता की जांच करने के लिए केंद्रीय स्तर पर संचालित एक निकाय", " इसे लोकपाल भी कहा जाता है।"],
                    solution_en: "22.(c) The meaning of ‘Lokayukta’ in the Constitution of India is a body operating at the central level to investigate the civil servant or politician with respect to complaints made against them for corruption.",
                    solution_hi: "22.(c) भारत के संविधान में \'लोकायुक्त\' का अर्थ केंद्रीय स्तर पर कार्यरत एक निकाय है जो भ्रष्टाचार के लिए उनके खिलाफ की गई शिकायतों के संबंध में सिविल सेवक या राजनेता की जांच करता है।",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "23",
                    section: "18",
                    question_en: "23. As of October 2020, how many IIMs (Indian Institutes of Management) are there?                                                                 ",
                    question_hi: "23.अक्टूबर 2020 तक, कितने IIM (भारतीय प्रबंधन संस्थान) हैं?",
                    options_en: [" 16", " 20", 
                                " 24", " 12"],
                    options_hi: [" 16", " 20",
                                " 24", " 12"],
                    solution_en: "23.(b) As of October 2020, 20 IIMs (Indian Institutes of Management) are there in India.",
                    solution_hi: "23.(b) अक्टूबर 2020 तक, भारत में 20 IIM (भारतीय प्रबंधन संस्थान) हैं।",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "24",
                    section: "18",
                    question_en: "24. Which country unveiled the world’s first floating nuclear power station?",
                    question_hi: "24. किस देश ने दुनिया के पहले तैरते परमाणु ऊर्जा स्टेशन का अनावरण किया?",
                    options_en: [" Russia", " USA ", 
                                " India ", " China "],
                    options_hi: [" रूस", " अमेरीका",
                                " भारत", " चीन"],
                    solution_en: "24.(a) Russia unveiled the world’s first floating nuclear power station. Akademik Lomonosov, the world\'s first floating nuclear power plant (NPP), has been fully commissioned in Pevek, Chukotka region in the Russian Far East.",
                    solution_hi: "24.(a) रूस ने दुनिया के पहले तैरते परमाणु ऊर्जा स्टेशन का अनावरण किया। अकादमिक लोमोनोसोव, दुनिया का पहला तैरता हुआ परमाणु ऊर्जा संयंत्र (NPP), रूसी सुदूर पूर्व में चुकोटका क्षेत्र के पेवेक में पूरी तरह से चालू हो गया है।",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "25",
                    section: "18",
                    question_en: "25. Which of the following freedom fighters was a co-founder of Swaraj Party along with Motilal Nehru?",
                    question_hi: "25. निम्नलिखित में से कौन सा स्वतंत्रता सेनानी मोतीलाल नेहरू के साथ स्वराज पार्टी के सह-संस्थापक थे?",
                    options_en: [" Rash Behari Ghosh ", " Ambica Charan Mazumdar ", 
                                " Chittaranjan Das ", " Bhupendra Nath Bose"],
                    options_hi: [" रास बिहारी घोष", " अंबिका चरण मजूमदार",
                                " चित्तरंजन दास ", " भूपेंद्र नाथ बोस"],
                    solution_en: "25.(c) Chittaranjan Das was a co-founder of Swaraj Party along with Motilal Nehru in 1January 1923.",
                    solution_hi: "25.(c) चित्तरंजन दास 1 जनवरी 1923 में मोतीलाल नेहरू के साथ स्वराज पार्टी के सह-संस्थापक थे।",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "26",
                    section: "18",
                    question_en: "26. Who amongst the following became the Acting Prime Minister of India twice?",
                    question_hi: "26.निम्नलिखित में से कौन दो बार भारत के कार्यवाहक प्रधान मंत्री बने?",
                    options_en: [" Indira Gandhi ", " Morarji Desai", 
                                " Charan Singh ", " Gulzarilal Nanda "],
                    options_hi: [" इंदिरा गांधी", " मोरारजी देसाई",
                                " चरण सिंह", " गुलजारीलाल नंद"],
                    solution_en: "26.(d)  Gulzarilal Nanda  became the Acting Prime Minister of India twice. He was the Prime Minister of India for two short periods following the deaths of Jawaharlal Nehru in 1964 and Lal Bahadur Shastri in 1966 respectively.",
                    solution_hi: "26.(d)  गुलजारीलाल नंदा दो बार भारत के कार्यवाहक प्रधानमंत्री बने। 1964 में जवाहरलाल नेहरू और 1966 में लाल बहादुर शास्त्री की मृत्यु के बाद वे दो छोटी अवधि के लिए भारत के प्रधान मंत्री थे।",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "27",
                    section: "18",
                    question_en: "27. Who among the following composed the National Song of India?",
                    question_hi: "27. निम्नलिखित में से किसने भारत के राष्ट्रीय गीत की रचना की?",
                    options_en: [" Rabindranath Tagore ", " Sri Aurobindo ", 
                                " Bankim Chandra Chattopadhyay ", " Sarojini Naidu "],
                    options_hi: [" रविंद्रनाथ टैगोर", " श्री अरबिंदो",
                                " बंकिम चंद्र चट्टोपाध्याय", " सरोजिनी नायडू"],
                    solution_en: "27.(c) Bankim Chandra Chattopadhyay composed the National Song of India. The song Vande Mataram, composed in Sanskrit. It has an equal status with Jana-gana-mana. On January 24, 1950. The poem was first sung by Rabindranath Tagore in the 1896 session of the Indian National Congress.",
                    solution_hi: "27.(c) बंकिम चंद्र चट्टोपाध्याय ने भारत के राष्ट्रीय गीत की रचना की। वंदे मातरम गीत संस्कृत में रचा गया है। इसे जन-गण-मन के समान दर्जा प्राप्त है। 24 जनवरी 1950 को। कविता को पहली बार रवींद्रनाथ टैगोर ने भारतीय राष्ट्रीय कांग्रेस के 1896 के सत्र में गाया था।",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "28",
                    section: "18",
                    question_en: "28. With which of the following sports is the English professional club Chelsea associated?",
                    question_hi: "28.अंग्रेजी पेशेवर क्लब चेल्सी निम्नलिखित में से किस खेल से संबंधित है?",
                    options_en: [" Cricket ", " Rugby ", 
                                " Football", " Hockey "],
                    options_hi: [" क्रिकेट", " रग्बी",
                                " फ़ुटबॉल", " हॉकी"],
                    solution_en: "28.(c)  The English professional club Chelsea is associated with football.",
                    solution_hi: "28.(c)  अंग्रेजी पेशेवर क्लब चेल्सी फुटबॉल से जुड़ा है।",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "29",
                    section: "18",
                    question_en: "29. What is the correct full form of “VIRUS” in computer programming?",
                    question_hi: "29. कंप्यूटर प्रोग्रामिंग में \"VIRUS\" का सही पूर्ण रूप क्या है?",
                    options_en: [" Vital Inter Change Result Until Source", " Vital Information Resources Under Seize ", 
                                " Vital Information Recognize Search", " Vital Information Record User Seize"],
                    options_hi: [" Vital Inter Change Result Until Source", " Vital Information Resources Under Seize ",
                                " Vital Information Recognize Search", " Vital Information Record User Seize"],
                    solution_en: "29.(b)  Full form of “VIRUS” in computer programming is Vital Information Resources Under Siege. Virus can replicate itself and spread from one computer to another and affect files and systems. <br />Some widespread viruses are MorrisWorm,Nimda,SQLSlammer,Stuxnet,CryptoLocker,Conficker,Tinba.",
                    solution_hi: "29.(b) कंप्यूटर प्रोग्रामिंग में \"VIRUS\" का फुल फॉर्म वाइटल इंफॉर्मेशन रिसोर्सेज अंडर सीज है। वायरस खुद को दोहरा सकता है और एक कंप्यूटर से दूसरे कंप्यूटर में फैल सकता है और फाइलों और सिस्टम को प्रभावित कर सकता है।<br />कुछ व्यापक वायरस मॉरिसवॉर्म, निमडा, एसक्यूएलस्लैमर, स्टक्सनेट, क्रिप्टो लॉकर, कॉन्फिकर, टिनबा हैं।",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "30",
                    section: "18",
                    question_en: "30.  Where is the Nuclear Fuel Complex of India situated?",
                    question_hi: "30. भारत का परमाणु ईंधन परिसर कहाँ स्थित है?",
                    options_en: [" Hyderabad ", " Hubli ", 
                                " Chennai ", " Visakhapatnam "],
                    options_hi: [" हैदराबाद", " हुबली ",
                                " चेन्नई", " विशाखापटनम"],
                    solution_en: "30.(a) The Nuclear Fuel Complex of India situated in Hyderabad.Fuels are fissile, and the most common nuclear fuels are the radioactive metals uranium-235 and plutonium-239.",
                    solution_hi: "30.(a) हैदराबाद में स्थित भारत का परमाणु ईंधन परिसर। ईंधन विखंडनीय हैं, और सबसे आम परमाणु ईंधन रेडियोधर्मी धातु यूरेनियम -235 और प्लूटोनियम -239 हैं।",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "31",
                    section: "18",
                    question_en: "31. Which of the following carries blood from the heart to the kidney?",
                    question_hi: "31.निम्नलिखित में से कौन हृदय से वृक्क तक रक्त पहुँचाता है?",
                    options_en: [" Renal vein ", " Coronary artery ", 
                                " Renal artery  ", " Vena cava "],
                    options_hi: [" गुर्दे की नस", " कोरोनरी धमनी",
                                " गुर्दे की धमनी", " वीना कावा"],
                    solution_en: "31.(c) Renal arteries carry blood from the heart to the kidney. Coronary arteries supply blood to the heart muscle. The renal veins are blood vessels that return blood to the heart from the kidney. Vena Cava, a large vein that carries blood to the heart from other areas of the body.",
                    solution_hi: "31.(c) गुर्दे की धमनियां रक्त को हृदय से गुर्दे तक ले जाती हैं। कोरोनरी धमनियां हृदय की मांसपेशियों को रक्त की आपूर्ति करती हैं। गुर्दे की नसें रक्त वाहिकाएं होती हैं जो गुर्दे से हृदय में रक्त लौटाती हैं। वेना कावा, एक बड़ी शिरा जो शरीर के अन्य क्षेत्रों से हृदय तक रक्त पहुँचाती है।",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "32",
                    section: "18",
                    question_en: "32. Which term was used to denote poll tax on non-muslims subjects during the reign of the Mughal Empire?",
                    question_hi: "32. मुगल साम्राज्य के शासनकाल के दौरान गैर-मुस्लिम विषयों पर मतदान कर को दर्शाने के लिए किस शब्द का इस्तेमाल किया गया था?",
                    options_en: [" Sharia ", " Shafi’ i ", 
                                " Jahiliyah ", " Jizyah "],
                    options_hi: [" शरीयत", " शफी \'आई \'",
                                " जाहिलिया", " जजियाह"],
                    solution_en: "32.(d) Jizyah was used to denote poll tax on non-muslims subjects during the reign of the Mughal Empire.",
                    solution_hi: "32.(d) मुग़ल साम्राज्य के शासनकाल के दौरान गैर-मुस्लिम विषयों पर मतदान कर को दर्शाने के लिए जजिया का इस्तेमाल किया जाता था।",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "33",
                    section: "18",
                    question_en: "<p>33. What is the agenda behind the Government of India&rsquo;s flagship programmes Pradhan mantri Vaya Vandan Yojana?</p>",
                    question_hi: "<p>33. भारत सरकार के प्रमुख कार्यक्रम \'प्रधानमंत्री वय वंदन योजना\' का मुख्य एजंडा क्या है?</p>",
                    options_en: ["<p>To provide a life cover of Rs 200,000 against a premium of Rs 330 per annum.</p>", "<p>Assured pension on a guaranteed return of 8% after making a minimum initial investment for Senior Citizen</p>", 
                                "<p>To provide a minimum monthly pension of Rs 1000 or Rs 2000</p>", "<p>To provide affordable housing to all</p>"],
                    options_hi: ["<p>Rs.330 वार्षिक प्रीमियम के एवज में Rs.200,000 का जीवन बिमा कवर प्रदान करना</p>", "<p>वार्षिक नागरिक के लिय न्यूनतम आरम्भिक निवेश करने के बाद 8% की गारंटी वापसी पर सुनिचित पैंसन</p>",
                                "<p>Rs 1000 या Rs. 2000 की न्यूनतम मासिक पैंसन प्रदान करना</p>", "<p>सभी को किफायती आवास प्रदान करना</p>"],
                    solution_en: "<p>33.(b) Pradhan Mantri Vaya Vandana Yojana (PMVVY) is a retirement cum pension scheme announced by the Indian Government.The scheme pays out regular pension and the frequency can be monthly, quarterly, or yearly.</p>",
                    solution_hi: "<p>33.(b) प्रधान मंत्री वय वंदना योजना (PMVVY)) भारत सरकार द्वारा घोषित एक सेवानिवृत्ति सह पेंशन योजना है। यह योजना नियमित पेंशन का भुगतान करती है और आवृत्ति मासिक, त्रैमासिक या वार्षिक हो सकती है।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "34",
                    section: "18",
                    question_en: "34. The Vedic time period in India lasted from:",
                    question_hi: "34. भारत में वैदिक काल अवधि क्या है?",
                    options_en: [" 1500 to 500 BC ", " 336 to 323 BC ", 
                                " 3000 to 2600 BC ", " 550 to 323 BC "],
                    options_hi: [" 1500 से 500 BC", " 336 से 323 BC",
                                " 3000 से 2600 BC", " 550 से 323 BC"],
                    solution_en: "34.(a) The Vedic time period in India lasted from 1500 to 500 BC. The four Vedas are the Rigveda (Knowledge of the Verses), the Yajurveda, the Samaveda, and the Atharvaveda.",
                    solution_hi: "34.(a) भारत में वैदिक काल की अवधि 1500 से 500 ईसा पूर्व तक थी। चार वेद ऋग्वेद (श्लोकों का ज्ञान), यजुर्वेद, सामवेद और अथर्ववेद हैं।",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "35",
                    section: "18",
                    question_en: "35. India’s largest salt water lake is situated in :",
                    question_hi: "35. भारत की सबसे बड़ी खारे पानी की झील कहाँ स्थित है?",
                    options_en: [" Thar Desert Region  ", " Krishna Delta", 
                                " Ganga Delta ", " Mahanadi Delta "],
                    options_hi: [" थार मरुस्थल क्षेत्र", " कृष्णा डेल्टा",
                                " गंगा डेल्टा", " महानदी डेल्टा"],
                    solution_en: "35.(d) India’s largest salt water lake is situated in Mahanadi Delta. Chilika Lake, the largest saltwater lake in India in the state of Odisha, lies to the south of the Mahanadi Delta",
                    solution_hi: "35.(d) भारत की सबसे बड़ी खारे पानी की झील महानदी डेल्टा में स्थित है। चिल्का झील, उड़ीसा राज्य में भारत की सबसे बड़ी खारे पानी की झील, महानदी डेल्टा के दक्षिण में स्थित है।",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "36",
                    section: "18",
                    question_en: "36. In which year was the European Union established?",
                    question_hi: "36. यूरोपीय संघ की स्थापना किस वर्ष हुई थी?",
                    options_en: [" 1992", " 1989", 
                                " 1993", " 1994"],
                    options_hi: [" 1992", " 1989",
                                " 1993", " 1994"],
                    solution_en: "36.(c) In 1993 the European Union was established at Maastricht, Netherlands.The European Union is a political and economic union of 27 member states that are located primarily in Europe.",
                    solution_hi: "36.(c) 1993 में यूरोपीय संघ की स्थापना मास्ट्रिच, नीदरलैंड में हुई थी। यूरोपीय संघ 27 सदस्य राज्यों का एक राजनीतिक और आर्थिक संघ है जो मुख्य रूप से यूरोप में स्थित हैं।",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "37",
                    section: "18",
                    question_en: "37. Which of the following options has the correct types of Planetary winds?",
                    question_hi: "37. निम्नलिखित में से किस विकल्प में ग्रहीय पवनों के सही प्रकार हैं?",
                    options_en: [" Local Winds and Periodic Winds ", " Trade Winds and Periodic Winds ", 
                                " Trade Winds, Westerlies and Polar Easterlies ", " Polar Winds and Westerlies "],
                    options_hi: [" स्थानीय हवाएं और आवधिक हवाएं", " व्यापारिक हवाएं और आवधिक हवाएं",
                                " व्यापारिक हवाएँ, पछुआ हवाएँ और ध्रुवीय पूर्वी हवाएँ", " ध्रुवीय हवाएं और पछुआ हवाएं"],
                    solution_en: "37.(c) Trade Winds, Westerlies and Polar Easterlies are the correct types of Planetary winds. Any wind system of the earth\'s atmosphere which owes its existence and direction to solar radiation and to the rotation of the earth is called planetary winds.",
                    solution_hi: "37.(c) व्यापारिक पवनें, पछुआ हवाएँ और ध्रुवीय पूर्वी पवनें ग्रहीय पवनों के सही प्रकार हैं। पृथ्वी के वायुमंडल की कोई भी पवन प्रणाली जो अपने अस्तित्व और दिशा के लिए सौर विकिरण और पृथ्वी के घूर्णन के कारण होती है, ग्रहीय हवाएं कहलाती है।",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "38",
                    section: "18",
                    question_en: "38. Which of the following is NOT a non - renewable resource?",
                    question_hi: "38. निम्नलिखित में से कौन एक गैर-नवीकरणीय संसाधन नहीं है?",
                    options_en: [" Coal", " Solar Energy ", 
                                " Liquified Petroleum Gas ", " Natural Gas "],
                    options_hi: [" कोयला ", " सौर ऊर्जा",
                                " तरल पेट्रोलियम गैस", " प्राकृतिक गैस"],
                    solution_en: "38.(b) Solar Energy is an Inexhaustible and renewable source of Energy.The most popular renewable energy sources currently are:Solar energy,Wind energy,Hydro energy,Tidal energy,Geothermal energy,Biomass energy.",
                    solution_hi: "38.(b) सौर ऊर्जा ऊर्जा का एक अटूट और नवीकरणीय स्रोत है। वर्तमान में सबसे लोकप्रिय अक्षय ऊर्जा स्रोत हैं: सौर ऊर्जा, पवन ऊर्जा, जल ऊर्जा, ज्वारीय ऊर्जा, भूतापीय ऊर्जा, बायोमास ऊर्जा।",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "39",
                    section: "18",
                    question_en: " 39. What is the full form of LASER?",
                    question_hi: "39.LASER का पूर्ण रूप क्या है?",
                    options_en: [" Lower Application of System Emission of Radioactivity", " Learning to Amplify and Stimulate Emission of Radiation ", 
                                " Light Addition to Systematic Electromagnetic Radiation", " Light Amplification by Stimulated Emission of Radiation"],
                    options_hi: [" Lower Application of System Emission of Radioactivity", " Learning to Amplify and Stimulate Emission of Radiation ",
                                " Light Addition to Systematic Electromagnetic Radiation", " Light Amplification by Stimulated Emission of Radiation"],
                    solution_en: "39.(d) The full form of LASER is Light Amplification by Stimulated Emission of Radiation. Types of lasers,Solid-state laser,Gas laser,Liquid laser,Semiconductor laser. LASER invented by Gould, Maiman.",
                    solution_hi: "39.(d) LASER  (Light Amplification by Stimulated Emission of Radiation) लाइट एम्प्लीफिकेशन बाय स्टिम्युलेटेड एमिशन ऑफ रेडिएशन। LASER के प्रकार, सॉलिड-स्टेट लेजर, गैस लेजर, लिक्विड लेजर, सेमीकंडक्टर लेजर। लेजर का आविष्कार गोल्ड, मैमन ने किया था।",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "40",
                    section: "misc",
                    question_en: "40. Who among the following sportsmen has the nickname ‘CR7’?",
                    question_hi: "40. निम्नलिखित में से किस खिलाड़ी का उपनाम \'CR7\' है?",
                    options_en: [" Chris Paul ", " Cristiano Ronaldo ", 
                                " Chris Gayle ", " Chris Rea "],
                    options_hi: [" क्रिस पॉल", " क्रिस्टियानो रोनाल्डो",
                                " क्रिस गेल", " क्रिस रिया"],
                    solution_en: "40.(b) Cristiano Ronaldo, Soccer Player  has the nickname ‘CR7\' and he is from Portuguese.",
                    solution_hi: "40.(b) क्रिस्टियानो रोनाल्डो, सॉकर प्लेयर का उपनाम \'CR7\' है और वह पुर्तगाली से है।",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>