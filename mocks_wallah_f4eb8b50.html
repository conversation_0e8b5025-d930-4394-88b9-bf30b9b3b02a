<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">8:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 25</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">25</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 8 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["18"] = {
                name: "General Awareness",
                start: 0,
                end: 23
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="18">General Awareness</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 24,
                end: 24
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "18",
                    question_en: "<p>1. Rudrasena II of which of the following dynasties married Prabhavatigupta, the daughter of Chandragupta II of the Gupta dynasty?</p>",
                    question_hi: "<p>1. निम्नलिखित में से किस राजवंश के रुद्रसेन द्वितीय ने गुप्त राजवंश के चंद्रगुप्त द्वितीय की बेटी प्रभावतीगुप्ता से विवाह किया था?</p>",
                    options_en: ["<p>Pushyabhuti</p>", "<p>Vakataka</p>", 
                                "<p>Chalukya</p>", "<p>Pallava</p>"],
                    options_hi: ["<p>पुष्यभूति</p>", "<p>वाकाटक</p>",
                                "<p>चालुक्य</p>", "<p>पल्लव</p>"],
                    solution_en: "<p>1.(b) <strong>Vakataka.</strong> The Vakataka dynasty: Founded by Vindhyashakti around 250-270 CE. The Vakatakas ruled over Central and Western India. Vakataka Rulers - Pravarasena I, Narendrasena, Prithvisena I, Rudrasena I, Divakarasena, Rudrasena II, Pravarasena II, Narendrasena II. Prabhavati Gupta was the daughter of Kuberanaga and Chandragupta II. Chandragupta II - He was the son of Samudragupta and Datta Devi. He ruled the Gupta Empire from 380 to 415 AD during the Golden Age of India.</p>",
                    solution_hi: "<p>1.(b)<strong> वाकाटक।</strong> वाकाटक राजवंश: विंध्यशक्ति द्वारा लगभग 250-270 ई. में स्थापित किया गया था। वाकाटकों ने मध्य और पश्चिमी भारत पर शासन किया। वाकाटक शासक - प्रवरसेन प्रथम, नरेंद्रसेन, पृथ्वीसेन प्रथम, रुद्रसेन प्रथम, दिवाकरसेन, रुद्रसेन द्वितीय, प्रवरसेन द्वितीय, नरेंद्रसेन द्वितीय। प्रभावती गुप्त कुबेरनाग और चंद्रगुप्त द्वितीय की पुत्री थीं। चंद्रगुप्त द्वितीय - समुद्रगुप्त और दत्त देवी का पुत्र था। उसने भारत के स्वर्ण युग के दौरान 380 से 415 ईस्वी तक गुप्त साम्राज्य पर शासन किया।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "18",
                    question_en: "<p>2. Madhuri Barthwal, a folk singer and Padma Shri 2022 awardee, belongs to which of the following states of India?</p>",
                    question_hi: "<p>2. पद्म श्री 2022 से सम्मानित लोक गायिका माधुरी बर्थवाल भारत के निम्नलिखित में से किस राज्य से संबंधित हैं?</p>",
                    options_en: ["<p>Uttarakhand</p>", "<p>Himachal Pradesh</p>", 
                                "<p>Uttar Pradesh</p>", "<p>Maharashtra</p>"],
                    options_hi: ["<p>उत्तराखंड</p>", "<p>हिमाचल प्रदेश</p>",
                                "<p>उत्तर प्रदेश</p>", "<p>महाराष्ट्र</p>"],
                    solution_en: "<p>2.(a) <strong>Uttarakhand.</strong> Madhuri Barthwal is the first woman to be a music composer in All India Radio. On International Women\'s Day in 2019, she was awarded the Nari Shakti Puraskar by Ram Nath Kovind. Other folk singers of Uttarakhand: Narendra Singh Negi, Pritam Bhartwan, Basanti Devi Bisht.</p>",
                    solution_hi: "<p>2.(a) <strong>उत्तराखंड।</strong> माधुरी बर्थवाल ऑल इंडिया रेडियो में संगीत रचयिता बनने वाली प्रथम महिला हैं। 2019 में अंतर्राष्ट्रीय महिला दिवस पर उन्हें रामनाथ कोविंद द्वारा नारी शक्ति पुरस्कार से सम्मानित किया गया था। उत्तराखंड के अन्य लोक गायक: नरेंद्र सिंह नेगी, प्रीतम भरतवाण, बसंती देवी बिष्ट आदि।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "18",
                    question_en: "<p>3. Which of the following options is the legislative organ of the Union government?</p>",
                    question_hi: "<p>3. निम्नलिखित में से कौन-सा विकल्प केंद्र सरकार का विधायी अंग है?</p>",
                    options_en: ["<p>Judiciary</p>", "<p>Legislative assembly</p>", 
                                "<p>Executive</p>", "<p>Parliament</p>"],
                    options_hi: ["<p>न्यायपालिका</p>", "<p>विधान सभा</p>",
                                "<p>कार्यपालिका</p>", "<p>संसद</p>"],
                    solution_en: "<p>3.(d) <strong>Parliament</strong>. The Indian Parliament is a bicameral legislature, consisting of two houses : the Lok Sabha (House of the People) and the Rajya Sabha (Council of States), along with the President. The three organs of the Union Government of India are the legislative, executive (Implements the laws enacted by the legislature), and judiciary (interprets the laws made by Parliament).</p>",
                    solution_hi: "<p>3.(d) <strong>संसद। </strong>भारतीय संसद एक द्विसदनीय विधायिका है, जिसमें राष्ट्रपति के साथ दो सदन होते हैं: लोकसभा (House of the People) और राज्यसभा (Council of States)। भारत की संघीय सरकार के तीन अंग हैं: विधायिका, कार्यपालिका (विधायिका द्वारा बनाए गए कानूनों को लागू करता है), और न्यायपालिका (संसद द्वारा बनाए गए कानूनों की व्याख्या करता है)।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "18",
                    question_en: "<p>4. Which of the following elements is a metalloid?</p>",
                    question_hi: "<p>4. निम्नलिखित में से कौन-सा तत्व एक उपधातु है?</p>",
                    options_en: ["<p>iron</p>", "<p>Oxygen</p>", 
                                "<p>Sodium</p>", "<p>Silicon</p>"],
                    options_hi: ["<p>आयरन</p>", "<p>ऑक्सीजन</p>",
                                "<p>सोडियम</p>", "<p>सिलिकॉन</p>"],
                    solution_en: "<p>4.(d)<strong> Silicon.</strong> Metalloids are elements that exhibit properties of both metals and non-metals. The Modern Periodic Table features a zig-zag line separating metals from non-metals. Seven elements along this borderline exhibit intermediate properties and are classified as metalloids or semi-metals : Boron (B), Germanium (Ge), Arsenic (As), Antimony (Sb), Tellurium (Te), Polonium (Po).</p>",
                    solution_hi: "<p>4.(d) <strong>सिलिकॉन।</strong> उपधातु ऐसे तत्व हैं जो धातु और अधातु दोनों के गुण प्रदर्शित करते हैं। आधुनिक आवर्त सारणी में धातुओं को अधातु से अलग करने वाली एक ज़िग-ज़ैग रेखा है। इस सीमा रेखा के साथ सात तत्व मध्यवर्ती गुण प्रदर्शित करते हैं और उन्हें उपधातु या अर्ध-धातु के रूप में वर्गीकृत किया जाता है: बोरॉन (B), जर्मेनियम (Ge), आर्सेनिक (As), एंटीमनी (Sb), टेल्यूरियम (Te), पोलोनियम (Po)।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "18",
                    question_en: "<p>5. Which country has the Republic feature of the Indian Constitution been borrowed from?</p>",
                    question_hi: "<p>5. भारतीय संविधान की गणतंत्रात्मक विशेषता किस देश के संविधान से ली गई है</p>",
                    options_en: ["<p>French Constitution</p>", "<p>irish Constitution</p>", 
                                "<p>British Constitution</p>", "<p>Australian Constitution</p>"],
                    options_hi: ["<p>फ्रांस के संविधान</p>", "<p>आयरलैंड के संविधान</p>",
                                "<p>ब्रिटिश संविधान</p>", "<p>ऑस्ट्रेलिया के संविधान</p>"],
                    solution_en: "<p>5.(a) <strong>French Constitution</strong>. Borrowed Features of Indian Constitution: France- Ideals of liberty, equality, and fraternity in Preamble. Australia - Concurrent list, Freedom of trade, commerce, and intercourse, Joint-sitting of Parliament. UK - Parliamentary government, Rule of Law, Legislative procedure, Single Citizenship, Cabinet system, Prerogative writs, Parliamentary privileges, Bicameralism. Ireland - Directive Principles of State Policy, Nomination of members to Rajya Sabha, Method of election of the president.</p>",
                    solution_hi: "<p>5.(a) <strong>फ्रांस के संविधान।</strong> भारतीय संविधान में अन्य देशों से अपनाई गई विशेषताएँ: फ्रांस - प्रस्तावना में स्वतंत्रता, समानता और बंधुत्व के आदर्श। ऑस्ट्रेलिया - समवर्ती सूची, व्यापार, वाणिज्य और समागम की स्वतंत्रता, संसद की संयुक्त बैठक। ब्रिटेन - संसदीय सरकार, विधि का शासन, विधायी प्रक्रिया, एकल नागरिकता, कैबिनेट प्रणाली, विशेषाधिकार रिट, संसदीय विशेषाधिकार, द्विसदनीयता। आयरलैंड - राज्य के नीति निर्देशक सिद्धांत, राज्यसभा के सदस्यों का नामांकन, राष्ट्रपति के चुनाव की विधि।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "18",
                    question_en: "<p>6. Which of the following States has declared a social security scheme for registered newspaper hawkers in the State in 2021 considering the losses they faced during the pandemic and that they are a part of the unorganised sector ?</p>",
                    question_hi: "<p>6. निम्नलिखित में से किस राज्य ने राज्य में पंजीकृत समाचार पत्र हॉकरों को महामारी के दौरान हुए नुकसान और उनके असंगठित क्षेत्र का हिस्सा होने पर विचार करते हुए 2021 में एक सामाजिक सुरक्षा योजना की घोषणा की थी?</p>",
                    options_en: ["<p>Gujarat</p>", "<p>Maharashtra</p>", 
                                "<p>Odisha</p>", "<p>Tamil Nadu</p>"],
                    options_hi: ["<p>गुजरात</p>", "<p>महाराष्ट्र</p>",
                                "<p>ओड़िशा</p>", "<p>तमिलनाडु</p>"],
                    solution_en: "<p>6.(c) <strong>Odisha</strong>. The scheme provides financial assistance to hawkers and their families, including accidental death benefits of Rs 2 lakh and natural death benefits of Rs 1 lakh. Additionally, hawkers will receive Rs 1.5 lakh in compensation for <strong>complete</strong> disability. <strong>Department</strong> of Information and Public Relations, Odisha has prepared and registered digital databases of 7,300 hawkers in the state.</p>",
                    solution_hi: "<p>6.(c) <strong>ओडिशा</strong>। इस योजना के तहत हॉकरों और उनके परिवारों को 2 लाख रुपये की दुर्घटना मृत्यु लाभ और 1 लाख रुपये की प्राकृतिक मृत्यु लाभ सहित वित्तीय सहायता प्रदान की जाती है। इसके अतिरिक्त, हॉकरों को पूर्ण विकलांगता के लिए 1.5 लाख रुपये का मुआवजा मिलेगा। सूचना और जनसंपर्क विभाग, ओडिशा ने राज्य में 7,300 हॉकरों का डिजिटल डेटाबेस तैयार और पंजीकृत किया है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "18",
                    question_en: "<p>7. Which of the following is the main cause of land degradation in Jharkhand and Chhattisgarh?</p>",
                    question_hi: "<p>7. निम्नलिखित में से क्या, झारखंड और छत्तीसगढ़ में भूमि निम्नीकरण का मुख्य कारण है?</p>",
                    options_en: ["<p>Intensive use of manure</p>", "<p>Over-irrigation</p>", 
                                "<p>Deforestation due to mining</p>", "<p>Overgrazing</p>"],
                    options_hi: ["<p>खाद का अधिक उपयोग</p>", "<p>अति-सिंचाई</p>",
                                "<p>खनन के कारण वनोन्मूलन</p>", "<p>अतिचारण</p>"],
                    solution_en: "<p>7.(c) <strong>Deforestation due to mining.</strong> Human activities like deforestation, over-grazing, mining, and quarrying significantly contribute to land degradation. Deforestation due to mining has severely impacted states like Jharkhand and Odisha. Over-grazing affects Gujarat, Rajasthan, and Madhya Pradesh, while excessive irrigation causes waterlogging and soil degradation in Punjab, Haryana, and western Uttar Pradesh.</p>",
                    solution_hi: "<p>7.(c) <strong>खनन के कारण वनोन्मूलन।</strong> वनोन्मूलन, अत्यधिक चराई, खनन और उत्खनन जैसी मानवीय गतिविधियाँ भूमि क्षरण में महत्वपूर्ण योगदान देती हैं। खनन के कारण वनोन्मूलन ने झारखंड और ओडिशा जैसे राज्यों को बहुत अधिक प्रभावित किया है। अत्यधिक चराई गुजरात, राजस्थान और मध्य प्रदेश को प्रभावित करती है, जबकि अत्यधिक सिंचाई के कारण पंजाब, हरियाणा और पश्चिमी उत्तर प्रदेश में जलभराव और मिट्टी के क्षरण का कारण बनती है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "18",
                    question_en: "<p>8. The Accelerated Female Literacy Programme was initiated in districts where the female literacy rate, based on the 2001 census, was _______.</p>",
                    question_hi: "<p>8. 2001 की जनगणना के आधार पर, त्वरित महिला साक्षरता कार्यक्रम उन जिलों में शुरू किया गया था जहाँ महिला साक्षरता दर _______ थी।</p>",
                    options_en: ["<p>below 35%</p>", "<p>below 45%</p>", 
                                "<p>below 30%</p>", "<p>below 50%</p>"],
                    options_hi: ["<p>35% से कम</p>", "<p>45% से कम</p>",
                                "<p>30% से कम</p>", "<p>50% से कम</p>"],
                    solution_en: "<p>8.(c) <strong>below 30%.</strong> According to the 2001 Census, 47 districts in India had a female literacy rate below 30%. Most of these districts were located in Bihar, Jharkhand, Uttar Pradesh, and Odisha. The Accelerated Female Literacy Programme aims to empower women by helping them develop literacy skills and gain access to information and services.</p>",
                    solution_hi: "<p>8.(c) <strong>30% से कम।</strong> 2001 की जनगणना के अनुसार, भारत के 47 जिलों में महिला साक्षरता दर 30% से कम थी। इनमें से ज़्यादातर जिले बिहार, झारखंड, उत्तर प्रदेश और ओडिशा में स्थित थे। त्वरित महिला साक्षरता कार्यक्रम का उद्देश्य महिलाओं को साक्षरता कौशल विकसित करने और सूचना एवं सेवाओं तक पहुँच प्राप्त करने में मदद करके उन्हें सशक्त बनाना है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "18",
                    question_en: "<p>9. As of 2021, which is the world\'s largest drainage basin with an area of about 70,00,000 km<sup>2</sup> ?</p>",
                    question_hi: "<p>9. 2021 की स्&zwj;थिति के अनुसार, लगभग 70,00,000 km<sup>2</sup> के क्षेत्रफल के साथ दुनिया का सबसे बड़ा जल निकासी बेसिन कौन-सा है?</p>",
                    options_en: ["<p>Congo basin</p>", "<p>Amur basin</p>", 
                                "<p>Amazon basin</p>", "<p>Nile basin</p>"],
                    options_hi: ["<p>कांगो बेसिन</p>", "<p>अमूर बेसिन</p>",
                                "<p>एमेज़ान बेसिन</p>", "<p>नील बेसिन</p>"],
                    solution_en: "<p>9.(c) <strong>Amazon basin.</strong> The Amazon Basin, formed by numerous tributaries, drains parts of Brazil, Peru, Bolivia, Ecuador, Colombia, and a small section of Venezuela. It lies directly on the equator and experiences a hot, humid climate year-round, with both day and night feeling equally warm and sticky. High rainfall, high temperature, and high humidity are the features of the basin.</p>",
                    solution_hi: "<p>9.(c) <strong>एमेज़ान बेसिन।</strong> अमेज़न बेसिन, जो अनेक सहायक नदियों द्वारा निर्मित है, ब्राज़ील, पेरू, बोलीविया, इक्वाडोर, कोलंबिया और वेनेजुएला के एक छोटे से हिस्से को जल से भरता है। यह भूमध्य रेखा पर स्थित है और यहाँ वर्ष भर गर्म, आर्द्र जलवायु रहती है, जहाँ दिन और रात दोनों ही समान रूप से गर्म और चिपचिपे होते हैं। उच्च वर्षा, उच्च तापमान और उच्च आर्द्रता इस बेसिन की विशेषताएँ हैं।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "18",
                    question_en: "<p>10. National Sports University is situated in ________.</p>",
                    question_hi: "<p>10. राष्ट्रीय खेल विश्वविद्यालय ________ में स्थित है।</p>",
                    options_en: ["<p>Mizoram</p>", "<p>Tripura</p>", 
                                "<p>Meghalaya</p>", "<p>Manipur</p>"],
                    options_hi: ["<p>मिज़ोरम</p>", "<p>त्रिपुरा</p>",
                                "<p>मेघालय</p>", "<p>मणिपुर</p>"],
                    solution_en: "<p>10.(d) <strong>Manipur.</strong> National Sports University: Established in 2018. Other Sports Universities : Netaji Subhas National Institute of Sports (Patiala), Lakshmibai National College of Physical Education (Kerala), and Tamil Nadu Physical Education and Sports University (Chennai).</p>",
                    solution_hi: "<p>10.(d)<strong> मणिपुर।</strong> राष्ट्रीय खेल विश्वविद्यालय: 2018 में स्थापित किया गया था। अन्य खेल विश्वविद्यालय: नेताजी सुभाष राष्ट्रीय खेल संस्थान (पटियाला), लक्ष्मीबाई राष्ट्रीय शारीरिक शिक्षा महाविद्यालय (केरल), और तमिलनाडु शारीरिक शिक्षा एवं खेल विश्वविद्यालय (चेन्नई)।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "18",
                    question_en: "11.  Which of the following characteristics is NOT of Aves ?",
                    question_hi: "11. निम्नलिखित में से कौन-सी विशेषता, एवीज़ (Aves) की नहीं है?",
                    options_en: [" They have a four-chambered heart.", " They give birth to live young ones with some exceptions that lay eggs.", 
                                "  These are warm-blooded animals.", " They breathe through the lungs"],
                    options_hi: [" इनका हृदय चार कक्षीय होता है।", " ये जीवित बच्चों को जन्म देते हैं, कुछ अपवादों को छोड़कर जो कि अंडे देते हैं। ",
                                "  ये गर्म खून वाले जीव होते हैं।", " ये फेफड़ों से सांस लेते हैं।"],
                    solution_en: "11.(b) The characteristic features of Aves (birds) are the presence of feathers and most of them can fly except flightless birds (e.g., Ostrich). The forelimbs are modified into wings. The hind limbs generally have scales and are modified for walking, swimming or clasping the tree branches. Endoskeleton is fully ossified (bony) and the long bones are hollow with air cavities (pneumatic). Examples- Corvus (Crow), Columba (Pigeon), Psittacula (Parrot), Struthio (Ostrich), Pavo (Peacock), Aptenodytes (Penguin), Neophron (Vulture).",
                    solution_hi: "11.(b) एवीज़ (पक्षियों) की विशिष्ट विशेषताएँ पंखों की उपस्थिति हैं और उड़ने में असमर्थ पक्षियों (उदाहरण के लिए, शुतुरमुर्ग) को छोड़कर उनमें से अधिकांश उड़ सकते हैं। अग्र पाद पंखों में रूपांतरित हो गए हैं। पिछले पैरों में आमतौर पर शल्क होते हैं और वे चलने, तैरने या पेड़ की शाखाओं को पकड़ने के लिए संशोधित होते हैं। अंतःकंकाल पूर्णतः अस्थिकृत (bony) होता है तथा लम्बी हड्डियां वायुगुहाओं (pneumatic) सहित खोखली होती हैं। उदाहरण - कोरवस (कौआ), कोलंबा (कबूतर), सिटाकुला (तोता), स्ट्रूथियो (शुतुरमुर्ग), पावो (मोर), एप्टेनोडाइट्स (पेंगुइन), नियोफ्रोन (गिद्ध)।",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "18",
                    question_en: "<p>12. In which of the following states is the development of rail transport less, due to its geographical conditions?</p>",
                    question_hi: "<p>12. निम्नलिखित में से किस राज्य में भौगोलिक परिस्थितियों के कारण रेल परिवहन का विकास कम हुआ है?</p>",
                    options_en: ["<p>Sikkim</p>", "<p>Haryana</p>", 
                                "<p>Bihar</p>", "<p>Uttar Pradesh</p>"],
                    options_hi: ["<p>सिक्किम</p>", "<p>हरियाणा</p>",
                                "<p>बिहार</p>", "<p>उत्तर प्रदेश</p>"],
                    solution_en: "<p>12.(a) <strong>Sikkim </strong>is predominantly mountainous, characterized by steep slopes and rugged terrain, which complicates the construction of rail infrastructure. The lack of flat land makes it difficult to lay tracks and build stations. Furthermore, the dense forests and protected wildlife areas in Sikkim pose additional obstacles to rail development. Rangpo railway station : This station is under construction on the Sevoke-Rangpo Railway Line in Sikkim\'s Pakyong District.</p>",
                    solution_hi: "<p>12.(a) <strong>सिक्किम </strong>मुख्य रूप से एक पर्वतीय प्रदेश है, जिसकी विशेषता खड़ी ढलान और ऊबड़-खाबड़ क्षेत्र है, जिसके कारण रेल अवसंरचना का निर्माण जटिल हो जाता है। समतल भूमि की कमी के कारण पटरियाँ बिछाना और स्टेशन बनाना मुश्किल हो जाता है। इसके अलावा, सिक्किम में घने जंगल और संरक्षित वन्यजीव क्षेत्र रेल विकास में अतिरिक्त बाधाएँ उत्पन्न करते हैं। रंगपो रेलवे स्टेशन: यह स्टेशन सिक्किम के पाकयोंग जिले में सेवोके-रंगपो रेलवे लाइन पर निर्माणाधीन है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "18",
                    question_en: "13.  Which of the following books is INCORRECTLY paired with its respective author?",
                    question_hi: "13. निम्नलिखित में से कौन-सी पुस्तक अपने संबंधित लेखक के साथ गलत रूप से युग्मित है? ",
                    options_en: [" Hind Swaraj – Mahatma Gandhi", " Gitanjali – Rabindranath Tagore", 
                                " The Discovery of India – Subhash Chandra Bose", " Why I am an Atheist – Bhagat Singh"],
                    options_hi: [" हिन्द स्वराज - महात्मा गांधी", " गीतांजलि - रवीन्द्रनाथ टैगोर",
                                " द डिस्कवरी ऑफ इंडिया - सुभाष चंद्र बोस", " व्हाई आई एम एन एथीस्ट - भगत सिंह"],
                    solution_en: "13.(c) ‘’The Discovery of India’’ is a book by Jawaharlal Nehru. Books and Authors: <AUTHORS>
                    solution_hi: "13.(c) ‘द डिस्कवरी ऑफ इंडिया’ जवाहरलाल नेहरू द्वारा लिखी गई पुस्तक है। लेखक एवं उनकी पुस्तकें और : सुभाष चंद्र बोस - ‘एन इंडियन पिल्ग्रिम: एन अनफ़िनीश्ड आटोबायोग्राफी। महात्मा गांधी - ‘नॉन वायलेन्ट रेजिसटेन्स’, ‘गांधी: सेलेक्टेड पॉलिटिकल राइटिंग्स, ‘इंडियन होम रूल ’, ‘हिंदू धर्म’। रवींद्रनाथ टैगोर - ‘काबुलीवाला’, ‘सोनार तोरी’, ‘गीताबितं’, ‘नेशनलिज़्म’।",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "18",
                    question_en: "<p>14. Who among the following was a poet who won the Nobel Prize for literature in 1913 ?</p>",
                    question_hi: "<p>14. निम्नलिखित में से वह कौन-सा कवि था जिसने 1913 में साहित्य के लिए नोबेल पुरस्कार प्राप्त किया था?</p>",
                    options_en: ["<p>Aurobindo Ghose</p>", "<p>Rudyard Kipling</p>", 
                                "<p>bankim Chandra Chattopadhyay</p>", "<p>Rabindranath Tagore</p>"],
                    options_hi: ["<p>अरबिंदो घोष</p>", "<p>रूडयार्ड किपलिंग</p>",
                                "<p>बंकिन चंद्र चट्टोपाध्याय</p>", "<p>रवीन्द्रनाथ टैगोर</p>"],
                    solution_en: "<p>14.(d) <strong>Rabindranath Tagore.</strong> He was also referred to as &lsquo;Gurudev&rsquo;, &lsquo;Kabiguru&rsquo;, and &lsquo;Biswakabi&rsquo;. He got Nobel Prize for his work Gitanjali. He was a Bengali poet, novelist, and painter, who was highly influential in introducing Indian culture to the west. He was the first non-European to receive the Nobel Prize.</p>",
                    solution_hi: "<p>14.(d) <strong>रवींद्रनाथ टैगोर </strong>को &lsquo;गुरुदेव&rsquo;, &lsquo;कबीगुरु&rsquo; और &lsquo;विश्वकबी&rsquo; के नाम से भी जाना जाता था। उनकी कृति गीतांजलि के लिए उन्हें नोबेल पुरस्कार मिला था । वे एक बंगाली कवि, उपन्यासकार और चित्रकार थे, जिन्होंने भारतीय संस्कृति को पश्चिम में पेश करने में बहुत प्रभावशाली भूमिका निभाई थी। वे नोबेल पुरस्कार से सम्मानित किये जाने वाले प्रथम गैर-यूरोपीय व्यक्ति थे।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "18",
                    question_en: "<p>15. In which year was the National Institute of Kathak Dance established?</p>",
                    question_hi: "<p>15. राष्ट्रीय कथक नृत्य संस्थान की स्थापना किस वर्ष हुई थी?</p>",
                    options_en: ["<p>1964</p>", "<p>1961</p>", 
                                "<p>1954</p>", "<p>1969</p>"],
                    options_hi: ["<p>1964</p>", "<p>1961</p>",
                                "<p>1954</p>", "<p>1969</p>"],
                    solution_en: "<p>15.(a) <strong>1964</strong>. National Institute of Kathak Dance is also known as Kathak Kendra. It is a unit of the Sangeet Natak Akademy. Other renowned Dance Institutes : Jawaharlal Nehru Manipur Dance Academy (Manipur), Nalanda Nritya Kala Mahavidyalaya (Mumbai), Ballet Repertoire Academy of India (Mumbai), Sri Thyagaraja College of Music and Dance (Hyderabad), and Nrityanjali Institute of Performing Arts (Mumbai).</p>",
                    solution_hi: "<p>15.(a) <strong>1964</strong>. राष्ट्रीय कथक नृत्य संस्थान को कथक केंद्र के नाम से भी जाना जाता है। यह संगीत नाटक अकादमी की एक इकाई है। अन्य प्रसिद्ध नृत्य संस्थान: जवाहरलाल नेहरू मणिपुर नृत्य अकादमी (मणिपुर), नालंदा नृत्य कला महाविद्यालय (मुंबई), बैले रिपर्टरी एकेडमी ऑफ इंडिया (मुंबई), श्री त्यागराज कॉलेज ऑफ म्यूजिक एंड डांस (हैदराबाद) और नृत्यंजलि इंस्टीट्यूट ऑफ परफॉर्मिंग आर्ट्स (मुंबई)।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "16",
                    section: "18",
                    question_en: "16. As for the year 2020-21, which of the following states had the lowest unemployment rate among persons aged 15 years and above?",
                    question_hi: "<p>16. वर्ष 2020-21 तक प्राप्त जानकारी के अनुसार, निम्नलिखित में से किस राज्य में 15 वर्ष और उससे अधिक आयु के व्यक्तियों के बीच बेरोजगारी दर सबसे कम थी?</p>",
                    options_en: ["<p>Sikkim</p>", "<p>Madhya Pradesh</p>", 
                                "<p>Gujarat</p>", "<p>Chhattisgarh</p>"],
                    options_hi: ["<p>सिक्किम</p>", "<p>मध्य प्रदेश</p>",
                                "<p>गुजरात</p>", "<p>छत्तीसगढ</p>"],
                    solution_en: "<p>16.(c) <strong>Gujarat</strong>. During 2022-23, Unemployment Rate (UR) on usual status for persons of age 15 years and above : Goa (9.7%), Kerala (7.0%), Haryana (6.1%), Meghalaya (6.0%), Lakshadweep (11.1%), Tripura (1.4%), Madhya Pradesh (1.6%), Assam (1.7%), Chhattisgarh (2.4%), Sikkim (2.2%).</p>",
                    solution_hi: "<p>16.(c) <strong>गुजरात</strong>। 2022-23 के दौरान, 15 वर्ष और उससे अधिक आयु के व्यक्तियों के लिए सामान्य स्थिति पर बेरोजगारी दर (UR): गोवा (9.7%), केरल (7.0%), हरियाणा (6.1%), मेघालय (6.0%), लक्षद्वीप (11.1%), त्रिपुरा (1.4%), मध्य प्रदेश (1.6%), असम (1.7%), छत्तीसगढ़ (2.4%), सिक्किम (2.2%)।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "17",
                    section: "18",
                    question_en: "17. When was the Indian Hockey Federation established?",
                    question_hi: "<p>17. भारतीय हॉकी महासंघ की स्थापना कब हुई थी?</p>",
                    options_en: ["<p>1925</p>", "<p>1955</p>", 
                                "<p>1945</p>", "<p>1935</p>"],
                    options_hi: ["<p>1925 में</p>", "<p>1955 में</p>",
                                "<p>1945 में</p>", "<p>1935 में</p>"],
                    solution_en: "<p>17.(a) <strong>1925</strong>. Indian Hockey Federation was the administrative body of field hockey in India. Headquarters - New Delhi. The International Hockey Federation was established in Paris in 1924, initiated by Paul Leautey.</p>",
                    solution_hi: "<p>17.(a) <strong>1925</strong>. भारतीय हॉकी महासंघ भारत में फील्ड हॉकी का प्रशासनिक निकाय था। मुख्यालय - नई दिल्ली। अंतर्राष्ट्रीय हॉकी महासंघ की स्थापना 1924 में पेरिस में पॉल लेउटे द्वारा की गई थी।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "18",
                    section: "18",
                    question_en: "<p>18. Which cell organelle is defined as the small round organelle that undergoes oxidation reaction to produce hydrogen peroxide?</p>",
                    question_hi: "<p>18. किस कोशिका अंगक को छोटे गोलाकार कोशिकांग के रूप में परिभाषित किया गया है जो हाइड्रोजन पेरोक्साइड का उत्पादन करने के लिए ऑक्सीकरण अभिक्रिया से गुजरता है?</p>",
                    options_en: ["<p>Centrosome</p>", "<p>Vacuole</p>", 
                                "<p>Nucleus</p>", "<p>Peroxisomes</p>"],
                    options_hi: ["<p>सेंट्रोसोम</p>", "<p>वैकुओल</p>",
                                "<p>न्यूक्लियस</p>", "<p>पेरोक्सीसोम्स</p>"],
                    solution_en: "<p>18.(d) <strong>Peroxisomes</strong>. Peroxisomes have multiple functions like lipid metabolism, processing reactive oxygen species, oxidative processes, and catabolism of D-amino acids, polyamines, and bile acids, with enzymes like peroxidase and catalase converting harmful peroxides to water; in plants, peroxisomes facilitate photosynthesis, seed germination, and efficient carbon fixation.</p>",
                    solution_hi: "<p>18.(d) <strong>पेरोक्सीसोम्स </strong>। पेरोक्सीसोम्स के कई कार्य हैं, जैसे लिपिड चयापचय, क्रियाशील ऑक्सीजन प्रजातियों का प्रसंस्करण, ऑक्सीडेटिव प्रक्रियाएं, और डी-अमीनो एसिड, पॉलीमाइन और पित्त एसिड का अपचय, पेरोक्सीडेज और कैटालेज जैसे एंजाइम हानिकारक पेरोक्साइड को जल में परिवर्तित करते हैं; तथा पौधों में, पेरोक्सीसोम्स प्रकाश संश्लेषण, बीज अंकुरण और कुशल कार्बन निर्धारण की सुविधा प्रदान करते हैं।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "19",
                    section: "18",
                    question_en: "19.  The cause of a redox reaction is the:",
                    question_hi: "<p>19. रेडॉक्स अभिक्रिया का कारण क्या है?</p>",
                    options_en: ["<p>transfer of electrons between two reactants</p>", "<p>transfer of electrons between two products</p>", 
                                "<p>transfer of neutrons between two reactants</p>", "<p>exchange of halogens between two reactants</p>"],
                    options_hi: ["<p>दो अभिकारकों के बीच इलेक्ट्रॉनों का स्थानांतरण</p>", "<p>दो उत्पादों के बीच इलेक्ट्रॉनों का स्थानांतरण</p>",
                                "<p>दो अभिकारकों के बीच न्यूट्रॉनों का स्थानांतरण</p>", "<p>दो अभिकारकों के बीच हैलोजन का विनिमय</p>"],
                    solution_en: "<p>19.(a)<strong> Transfer of electrons between two reactants.</strong> 2Na(s) + S(s) &rarr; Na2S(s) are redox reactions because in each of these reactions sodium is oxidised due to the addition of either oxygen or more electronegative element to sodium. Simultaneously, chlorine, oxygen and sulphur are reduced because to each of these, the electropositive element sodium has been added.</p>",
                    solution_hi: "<p>19.(a) <strong>दो अभिकारकों के बीच इलेक्ट्रॉनों का स्थानांतरण।</strong> 2Na(s) + S(s) &rarr; Na2S(s) रेडॉक्स अभिक्रियाएँ हैं क्योंकि इनमें से प्रत्येक अभिक्रिया में सोडियम में ऑक्सीजन या अधिक विद्युत-ऋणात्मक तत्व के जुड़ने के कारण सोडियम का ऑक्सीकरण होता है। इसके साथ ही, क्लोरीन, ऑक्सीजन और सल्फर अपचयित हो जाते हैं क्योंकि इनमें से प्रत्येक में विद्युत-धनात्मक तत्व सोडियम मिलाया गया है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "20",
                    section: "18",
                    question_en: "20.  ‘Sangken’ is a festival celebrated in Arunachal Pradesh by people of which of the following religions?",
                    question_hi: "<p>20. &lsquo;सांगकेन\' निम्नलिखित में से किस धर्म के लोगों द्वारा अरुणाचल प्रदेश में मनाया जाने वाला त्योहार है?</p>",
                    options_en: ["<p>islam</p>", "<p>Buddhism</p>", 
                                "<p>Jainism</p>", "<p>Hinduism</p>"],
                    options_hi: ["<p>इस्लाम</p>", "<p>बौद्ध धर्म</p>",
                                "<p>जैन धर्म</p>", "<p>हिन्दू धर्म</p>"],
                    solution_en: "<p>20.(b) <strong>Buddhism</strong>. Sangken (festival of water) is an annual three-day long festival. The Khampti and Singpho tribes of the Lohit district in Arunachal Pradesh celebrate it. It is celebrated by the Theravada Buddhist Communities. It is celebrated as Songkran in other Southeast Asian countries. Songkran is the Thai New Year&rsquo;s national holiday. More Buddhists festivals include Buddha Purnima, Hemis Festival, Losar Festival, Lumbini Festival, Parinirvana Day, Rumtek Chaam Festival and Mani Rimdu Festival.</p>",
                    solution_hi: "<p>20.(b) <strong>बौद्ध धर्म।</strong> सांगकेन (जल का त्यौहार) तीन दिन तक चलने वाला वार्षिक त्यौहार है। अरुणाचल प्रदेश के लोहित जिले की खाम्पती और सिंगफो जनजातियाँ इसे मनाती हैं। इसे थेरवाद बौद्ध समुदाय द्वारा मनाया जाता है। इसे अन्य दक्षिण-पूर्व एशियाई देशों में सोंगक्रान के रूप में मनाया जाता है। सोंगक्रान थाई नववर्ष का राष्ट्रीय अवकाश है। बौद्ध धर्म के अन्य त्यौहारों में बुद्ध पूर्णिमा, हेमिस त्यौहार, लोसर त्यौहार, लुम्बिनी त्यौहार, परिनिर्वाण दिवस, रुमटेक चाम त्यौहार और मणि रिमदु त्यौहार शामिल हैं।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "21",
                    section: "18",
                    question_en: "21.  Which group of people will be benefited by the Mukhya Mantri Sukh Ashray Yojana approved by Himachal Pradesh?",
                    question_hi: "21. हिमाचल प्रदेश द्वारा शुरू की गई \'मुख्यमंत्री सुख आश्रय योजना\' से किस समूह के लोग लाभान्वित होंगे ?",
                    options_en: [" Students and teachers", " Orphans, specially abled children, destitute women and senior citizens", 
                                " Unemployed youth", " Farmers and agricultural labourers"],
                    options_hi: [" विद्यार्थी और शिक्षक", " अनाथ, दिव्यांग बच्चे, निराश्रित महिलाएँ और वरिष्ठ नागरिक",
                                " बेरोजगार युवा", " कृषक और खेतिहर श्रमिक"],
                    solution_en: "21.(b) Mukhya Mantri Sukh Aashray Kosh: The outlay budget of the scheme is 101 crores. The fund has been started with a vision to help and assist needy students and destitute women to get higher education. This would also help them to lead a life of dignity. A specific amount would be given to these students and women which would help them in acquiring desired higher education. ",
                    solution_hi: "21.(b) मुख्यमंत्री सुख आश्रय कोष: इस योजना का परिव्यय बजट 101 करोड़ है। इस कोष की शुरुआत जरूरतमंद छात्रों और बेसहारा महिलाओं को उच्च शिक्षा प्राप्त करने में सहायता करने के उद्देश्य से की गई है। इससे उन्हें सम्मानपूर्वक जीवन जीने में भी मदद मिलेगी। इन छात्रों और महिलाओं को एक निश्चित राशि दी जाएगी जिससे उन्हें वांछित उच्च शिक्षा प्राप्त करने में मदद मिलेगी।",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "22",
                    section: "18",
                    question_en: "<p>22. What is the aim of the PM Ujjwala 2.0 Yojna launched in August 2021 ?</p>",
                    question_hi: "<p>22. अगस्त 2021 में शुरू की गई पी.एम. उज्ज्वला 2.0 योजना का उद्देश्य क्या है?</p>",
                    options_en: ["<p>Free LPG connections for commercial use</p>", "<p>20 million LPG connections will be provided to the beneficiaries</p>", 
                                "<p>10 million LPG connections will be provided to the beneficiaries</p>", "<p>Free gas stove for BPL Households</p>"],
                    options_hi: ["<p>व्यावसायिक उपयोग के लिए मुफ्त एल.पी.जी. (LPG) कनेक्शन</p>", "<p>लाभार्थियों को 20 मिलियन एल.पी.जी. (LPG) कनेक्शन प्रदान किए जाएंगे</p>",
                                "<p>लाभार्थियों को 10 मिलियन एल.पी.जी. (LPG) कनेक्शन प्रदान किए जाएंगे</p>", "<p>बी.पी.एल. (BPL) परिवारों के लिए मुफ्त गैस चूल्हा</p>"],
                    solution_en: "<p>22.(c) <strong>10 million LPG connections will be provided to the beneficiaries. </strong>The Prime Minister launched the Ujjwala Scheme II on 10 August 2021 to provide fuel to 1 crore families who were left out of the first scheme. Other schemes: The Jan Dhan Yojna (2014), Beti Bachao Beti Padhao (2015), Start-up India (2016), Prime Minister Ujjwala&rsquo;s Plan (2016).</p>",
                    solution_hi: "<p>22.(c) <strong>लाभार्थियों को 10 मिलियन एल.पी.जी. (LPG) कनेक्शन प्रदान किए जाएंगे। </strong>प्रधानमंत्री ने 10 अगस्त 2021 को उज्ज्वला योजना II की शुरुआत की, ताकि पहली योजना से छूटे हुए 1 करोड़ परिवारों को ईंधन उपलब्ध कराया जा सके। अन्य योजनाएँ: जन धन योजना (2014), बेटी बचाओ बेटी पढ़ाओ (2015), स्टार्ट-अप इंडिया (2016), प्रधानमंत्री उज्ज्वला योजना (2016)।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "23",
                    section: "18",
                    question_en: "<p>23. MG Music Awards is presented annually by which of the following states?</p>",
                    question_hi: "<p>23. एम.जी. संगीत पुरस्कार प्रतिवर्ष निम्नलिखित में से किस राज्य द्वारा प्रदान किया जाता है?</p>",
                    options_en: ["<p>Mizoram</p>", "<p>Nagaland</p>", 
                                "<p>Manipur</p>", "<p>Chhattisgarh</p>"],
                    options_hi: ["<p>मिज़ोरम</p>", "<p>नागालैंड</p>",
                                "<p>मणिपुर</p>", "<p>छत्तीसगढ़</p>"],
                    solution_en: "<p>23.(b) <strong>Nagaland</strong>. The MG Music Awards is an annual music awards ceremony organized by the Musicians Guild Nagaland. The first edition of the awards was held online on June 21, 2021, due to the COVID-19 pandemic. The second edition was held offline at the RCEMPA Hall in Jotsoma, Nagaland in 2022.</p>",
                    solution_hi: "<p>23.(b) <strong>नागालैंड</strong>। एम.जी. म्यूजिक अवार्ड्स म्यूजिशियन गिल्ड नागालैंड द्वारा आयोजित एक वार्षिक संगीत पुरस्कार समारोह है। पुरस्कारों का प्रथम संस्करण कोविड-19 महामारी के कारण 21 जून, 2021 को ऑनलाइन आयोजित किया गया था। दूसरा संस्करण 2022 में नागालैंड के जोत्सोमा में RCEMPA हॉल में ऑफ़लाइन आयोजित किया गया था।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "24",
                    section: "18",
                    question_en: "<p>24. If assets of a bank are greater than liabilities, it will be recorded as:</p>",
                    question_hi: "<p>24. यदि किसी बैंक की परिसंपत्ति उसकी देनदारियों से अधिक है, तो इसे किस प्रकार दर्ज किया जाएगा ?</p>",
                    options_en: ["<p>liabilities</p>", "<p>net worth</p>", 
                                "<p>reserves</p>", "<p>assets</p>"],
                    options_hi: ["<p>देनदारियां</p>", "<p>शुद्ध संपत्ति (नेट वर्थ)</p>",
                                "<p>रिज़र्व</p>", "<p>परिसंपत्तियां</p>"],
                    solution_en: "<p>24.(b) <strong>net worth.</strong> The amount by which the value of the assets exceed the liabilities is the net worth (equity) of the business. The net worth reflects the amount of ownership of the business by the owners. The formula for computing net worth is: Assets - Liabilities = Net Worth.</p>",
                    solution_hi: "<p>24.(b) <strong>शुद्ध संपत्ति (नेट वर्थ)।</strong> वह राशि जिससे परिसंपत्तियों का मूल्य देनदारियों से अधिक होता है, व्यवसाय का नेट वर्थ (इक्विटी) है। नेट वर्थ मालिकों द्वारा व्यवसाय के स्वामित्व की मात्रा को दर्शाता है। नेट वर्थ की गणना करने का सूत्र है: परिसंपत्तियाँ - देनदारियाँ = नेट वर्थ।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "25",
                    section: "misc",
                    question_en: "<p>25. Which of the following departments of Delhi Sultanate was reorganized by Balban?</p>",
                    question_hi: "<p>25. बलबन द्वारा दिल्ली सल्तनत के निम्नलिखित में से किस विभाग का पुनर्गठन किया गया था?</p>",
                    options_en: ["<p>Diwan-i-Ishtiaq</p>", "<p>Diwan-i-Arz</p>", 
                                "<p>Diwan-i-Risalat</p>", "<p>Diwan-i-Mustakhraj</p>"],
                    options_hi: ["<p>दीवान-ए-इश्तियाक</p>", "<p>दीवान-ए-अर्ज़</p>",
                                "<p>दीवान-ए-रिसालत</p>", "<p>दीवान-ए-मुस्तखराज</p>"],
                    solution_en: "<p>25.(b) <strong>Diwan-i-Arz (Department of Military).</strong> Central Departments of Delhi Sultanates: &lsquo;Diwan-i-Risalat&rsquo; - Department of Appeals, &lsquo;Diwan-i-Bandagan&rsquo;- Department of Slaves, &lsquo;Diwan-i-Qaza-i-Mamalik&rsquo; - Department of Justice, &lsquo;Diwan-i-Ishtiaq&rsquo; - Department of Pensions, &lsquo;Diwan - i - Mustakhraj&rsquo;- Departments of Arrears, &lsquo;Diwan-i-Khairat&rsquo; - Department of Charity, &lsquo;Diwan-i-Kohi&rsquo;- Department of Agriculture, &lsquo;Diwan -i-Wizarat&rsquo; - Head Wazir, &lsquo;Diwan -i-Waqoof&rsquo; - Department of Revenue.</p>",
                    solution_hi: "<p>25.(b) <strong>दीवान-ए-अर्ज़ (सैन्य विभाग)।</strong> दिल्ली सल्तनत के केंद्रीय विभाग: \'दीवान-ए-रसालत\' - अपील विभाग, \'दीवान-ए-बंदगान\'- दास विभाग, \'दीवान-ए-कजा-ए-ममालिक\' - न्याय विभाग, \'दीवान-ए-इश्तियाक\' - पेंशन विभाग, \'दीवान-ए-मुस्तखराज\'- बकाया विभाग, \'दीवान-ए-खैरात\' - दान विभाग, \'दीवान-ए-कोही\'- कृषि विभाग, \'दीवान-ए-विजारत\' - मुख्य वजीर, \'दीवान-ए-वकूफ\' - राजस्व विभाग।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>