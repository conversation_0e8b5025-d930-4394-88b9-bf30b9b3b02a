<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">15:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 15</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">15</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 15 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 0,
                end: 13
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 14,
                end: 14
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">1</span><span style=\"font-family: Cambria Math;\">. If tan (5<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">&theta;</mi></math></span><span style=\"font-family: Cambria Math;\"> - 10&deg;) = cot (5</span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&phi;</mi></math>+ 20&deg;), then the value of</span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">&theta;</mi><mo>+</mo><mi mathvariant=\"normal\">&phi;</mi></math> </span><span style=\"font-family: Cambria Math;\"> is:</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">1. </span><span style=\"font-family: Cambria Math;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> tan (5&theta; - 10&deg;) = cot (5</span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&phi;</mi></math>+ 20&deg;) </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&theta;</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mi>&phi;</mi></math> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>16&deg;</p>\n", "<p>18&deg;</p>\n", 
                                "<p>15&deg;</p>\n", "<p>20&deg;</p>\n"],
                    options_hi: ["<p>16&deg;</p>\n", "<p>18&deg;</p>\n",
                                "<p>15&deg;</p>\n", "<p>20&deg;</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">1.(</span><span style=\"font-family: Cambria Math;\">a)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>tan</mi><mi>&alpha;</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mi>c</mi><mi>o</mi><mi>t</mi><mi>&beta;</mi><mo>&rArr;</mo><mi>&alpha;</mi><mo>=</mo><mi>&beta;</mi><mo>&nbsp;</mo><mo>=</mo><mn>45</mn><mo>&deg;</mo><mspace linebreak=\"newline\"></mspace><mi>Here</mi><mo>,</mo><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mi>tan</mi><mo>(</mo><mn>5</mn><mi>&theta;</mi><mo>-</mo><mn>10</mn><mo>&deg;</mo><mo>)</mo><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mi>c</mi><mi>o</mi><mi>t</mi><mo>(</mo><mn>5</mn><mi>&phi;</mi><mo>+</mo><mn>20</mn><mo>)</mo><mo>&deg;</mo><mspace linebreak=\"newline\"></mspace><mo>(</mo><mn>5</mn><mi>&theta;</mi><mo>-</mo><mn>10</mn><mo>&deg;</mo><mo>)</mo><mo>=</mo><mn>45</mn><mo>&deg;</mo><mo>&rArr;</mo><mi>&theta;</mi><mo>=</mo><mn>11</mn><mo>&deg;</mo><mspace linebreak=\"newline\"></mspace><mo>(</mo><mn>5</mn><mi>&phi;</mi><mo>+</mo><mn>20</mn><mo>&deg;</mo><mo>)</mo><mo>=</mo><mn>45</mn><mo>&deg;</mo><mo>&rArr;</mo><mi>&phi;</mi><mo>=</mo><mn>5</mn><mo>&deg;</mo><mspace linebreak=\"newline\"></mspace><mi>N</mi><mi>o</mi><mi>w</mi><mo>&nbsp;</mo><mo>,</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mi>&theta;</mi><mo>+</mo><mo>&nbsp;</mo><mi>&phi;</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>16</mn><mo>&deg;</mo></math></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">1.(</span><span style=\"font-family: Cambria Math;\">a)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><math style=\"font-family:Verdana\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>tan</mi><mi>&alpha;</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mi>c</mi><mi>o</mi><mi>t</mi><mi>&beta;</mi><mo>&rArr;</mo><mi>&alpha;</mi><mo>=</mo><mi>&beta;</mi><mo>&nbsp;</mo><mo>=</mo><mn>45</mn><mo>&deg;</mo><mspace linebreak=\"newline\"></mspace><mi>&#2351;&#2361;&#2366;&#2305;</mi><mo>&nbsp;</mo><mo>,</mo><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mi>tan</mi><mo>(</mo><mn>5</mn><mi>&theta;</mi><mo>-</mo><mn>10</mn><mo>&deg;</mo><mo>)</mo><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mi>c</mi><mi>o</mi><mi>t</mi><mo>(</mo><mn>5</mn><mi>&phi;</mi><mo>+</mo><mn>20</mn><mo>&deg;</mo><mo>)</mo><mspace linebreak=\"newline\"></mspace><mo>(</mo><mn>5</mn><mi>&theta;</mi><mo>-</mo><mn>10</mn><mo>&deg;</mo><mo>)</mo><mo>=</mo><mn>45</mn><mo>&deg;</mo><mo>&rArr;</mo><mi>&theta;</mi><mo>=</mo><mn>11</mn><mo>&deg;</mo><mspace linebreak=\"newline\"></mspace><mo>(</mo><mn>5</mn><mi>&phi;</mi><mo>+</mo><mn>20</mn><mo>&deg;</mo><mo>)</mo><mo>=</mo><mn>45</mn><mo>&deg;</mo><mo>&rArr;</mo><mi>&phi;</mi><mo>=</mo><mn>5</mn><mo>&deg;</mo><mspace linebreak=\"newline\"></mspace><mi>&#2309;&#2348;</mi><mo>,</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mi>&theta;</mi><mo>+</mo><mo>&nbsp;</mo><mi>&phi;</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>16</mn><mo>&deg;</mo></math></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">2.</span><span style=\"font-family: Cambria Math;\"> If &theta; </span><span style=\"font-family: Cambria Math;\">is an acute angle and</span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&nbsp;</mo><mi>sin&theta;</mi><mo>&nbsp;</mo><mi>cos&theta;</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>2</mn><mo>&nbsp;</mo><msup><mi>cos</mi><mn>3</mn></msup><mi mathvariant=\"normal\">&theta;</mi><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mfrac><mn>1</mn><mn>4</mn></mfrac><mi>cos&theta;</mi></math> , <span style=\"font-family: Cambria Math;\">then the value of sin&theta;</span><span style=\"font-family: Cambria Math;\"> is:</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">2.</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> &#1256; </span><span style=\"font-family: Cambria Math;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2344;&#2381;&#2351;&#2370;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2379;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&nbsp;</mo><mi>sin&theta;</mi><mo>&nbsp;</mo><mi>cos&theta;</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>2</mn><mo>&nbsp;</mo><msup><mi>cos</mi><mn>3</mn></msup><mi mathvariant=\"normal\">&theta;</mi><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mfrac><mn>1</mn><mn>4</mn></mfrac><mi>cos&theta;</mi></math><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">sin&#1256;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msqrt><mn>15</mn></msqrt><mo>-</mo><mn>1</mn></mrow><mn>8</mn></mfrac></math><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msqrt><mn>15</mn></msqrt><mo>-</mo><mn>1</mn></mrow><mn>4</mn></mfrac></math><span style=\"font-family: Cambria Math;\"> </span></p>\n", 
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msqrt><mn>15</mn></msqrt><mo>+</mo><mn>1</mn></mrow><mn>4</mn></mfrac></math><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msqrt><mn>15</mn></msqrt><mo>-</mo><mn>1</mn></mrow><mn>2</mn></mfrac></math></p>\n"],
                    options_hi: ["<p><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msqrt><mn>15</mn></msqrt><mo>-</mo><mn>1</mn></mrow><mn>8</mn></mfrac></math> </span></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msqrt><mn>15</mn></msqrt><mo>-</mo><mn>1</mn></mrow><mn>4</mn></mfrac></math><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                                "<p><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msqrt><mn>15</mn></msqrt><mo>+</mo><mn>1</mn></mrow><mn>4</mn></mfrac></math> </span></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msqrt><mn>15</mn></msqrt><mo>-</mo><mn>1</mn></mrow><mn>2</mn></mfrac></math></p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">2.(</span><span style=\"font-family: Cambria Math;\">b)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>sin&theta;</mi><mo>&nbsp;</mo><mo>.</mo><mo>&nbsp;</mo><mi>cos&theta;</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>2</mn><mo>&nbsp;</mo><msup><mi>cos</mi><mn>3</mn></msup><mi mathvariant=\"normal\">&theta;</mi><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mfrac><mn>1</mn><mn>4</mn></mfrac><mi>cos&theta;</mi><mspace linebreak=\"newline\"></mspace><mn>4</mn><mo>&nbsp;</mo><mi>sin&theta;</mi><mo>&nbsp;</mo><mo>.</mo><mo>&nbsp;</mo><mi>cos&theta;</mi><mo>&nbsp;</mo><mo>=</mo><mn>8</mn><msup><mi>cos</mi><mn>3</mn></msup><mi mathvariant=\"normal\">&theta;</mi><mo>-</mo><mi>cos&theta;</mi><mspace linebreak=\"newline\"></mspace><mn>4</mn><mi>sin&theta;</mi><mo>=</mo><mn>8</mn><mo>&nbsp;</mo><msup><mi>cos</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&theta;</mi><mo>-</mo><mn>1</mn><mspace linebreak=\"newline\"></mspace><mn>4</mn><mi>sin&theta;</mi><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>8</mn><mo>-</mo><mn>8</mn><msup><mi>sin</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&theta;</mi><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>1</mn><mspace linebreak=\"newline\"></mspace><mn>8</mn><msup><mi>sin</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&theta;</mi><mo>+</mo><mn>4</mn><mi>sin&theta;</mi><mo>-</mo><mn>7</mn><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>0</mn><mspace linebreak=\"newline\"></mspace><mi>sin&theta;</mi><mo>=</mo><mfrac><mrow><mo>-</mo><mi mathvariant=\"normal\">b</mi><mo>&plusmn;</mo><msqrt><msup><mi mathvariant=\"normal\">b</mi><mn>2</mn></msup><mo>-</mo><mn>4</mn><mi>ac</mi></msqrt></mrow><mrow><mn>2</mn><mi mathvariant=\"normal\">a</mi></mrow></mfrac><mo>=</mo><mfrac><mrow><mo>-</mo><mn>4</mn><mo>&plusmn;</mo><msqrt><msup><mn>4</mn><mn>2</mn></msup><mo>-</mo><mn>4</mn><mo>&times;</mo><mn>8</mn><mo>&times;</mo><mo>(</mo><mo>-</mo><mn>7</mn><mo>)</mo></msqrt></mrow><mrow><mn>2</mn><mo>&times;</mo><mn>8</mn></mrow></mfrac><mspace linebreak=\"newline\"></mspace><mo>=</mo><mfrac><mrow><mo>-</mo><mn>4</mn><mo>&plusmn;</mo><msqrt><mn>240</mn></msqrt></mrow><mrow><mn>2</mn><mo>&times;</mo><mn>8</mn></mrow></mfrac><mo>=</mo><mfrac><mrow><mo>-</mo><mn>1</mn><mo>&plusmn;</mo><msqrt><mn>15</mn></msqrt></mrow><mn>4</mn></mfrac><mspace linebreak=\"newline\"></mspace><mi>Here</mi><mo>&nbsp;</mo><mi mathvariant=\"normal\">&theta;</mi><mo>&nbsp;</mo><mi>is</mi><mo>&nbsp;</mo><mi>acute</mi><mo>&nbsp;</mo><mi>angle</mi><mo>&nbsp;</mo><mo>,</mo><mo>&nbsp;</mo><mi>so</mi><mo>&nbsp;</mo><mo>&nbsp;</mo><mi>sin</mi><mo>&nbsp;</mo><mi mathvariant=\"normal\">&theta;</mi><mo>&nbsp;</mo><mo>=</mo><mfrac><mrow><msqrt><mn>15</mn></msqrt><mo>-</mo><mn>1</mn></mrow><mn>4</mn></mfrac></math></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">2.(</span><span style=\"font-family: Cambria Math;\">b)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><math style=\"font-family:Verdana\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>sin&theta;</mi><mo>&nbsp;</mo><mo>.</mo><mo>&nbsp;</mo><mi>cos&theta;</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>2</mn><mo>&nbsp;</mo><msup><mi>cos</mi><mn>3</mn></msup><mi mathvariant=\"normal\">&theta;</mi><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mfrac><mn>1</mn><mn>4</mn></mfrac><mi>cos&theta;</mi><mspace linebreak=\"newline\"></mspace><mn>4</mn><mi>sin&theta;</mi><mo>&nbsp;</mo><mo>.</mo><mo>&nbsp;</mo><mi>cos&theta;</mi><mo>&nbsp;</mo><mo>=</mo><mn>8</mn><msup><mi>cos</mi><mn>3</mn></msup><mi mathvariant=\"normal\">&theta;</mi><mo>-</mo><mi>cos&theta;</mi><mspace linebreak=\"newline\"></mspace><mn>4</mn><mi>sin&theta;</mi><mo>=</mo><mn>8</mn><mo>&nbsp;</mo><msup><mi>cos</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&theta;</mi><mo>-</mo><mn>1</mn><mspace linebreak=\"newline\"></mspace><mn>4</mn><mi>sin&theta;</mi><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>8</mn><mo>-</mo><mn>8</mn><msup><mi>sin</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&theta;</mi><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>1</mn><mspace linebreak=\"newline\"></mspace><mn>8</mn><msup><mi>sin</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&theta;</mi><mo>+</mo><mn>4</mn><mi>sin&theta;</mi><mo>-</mo><mn>7</mn><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>0</mn><mspace linebreak=\"newline\"></mspace><mi>sin&theta;</mi><mo>=</mo><mfrac><mrow><mo>-</mo><mi mathvariant=\"normal\">b</mi><mo>&plusmn;</mo><msqrt><msup><mi mathvariant=\"normal\">b</mi><mn>2</mn></msup><mo>-</mo><mn>4</mn><mi>ac</mi></msqrt></mrow><mrow><mn>2</mn><mi mathvariant=\"normal\">a</mi></mrow></mfrac><mo>=</mo><mfrac><mrow><mo>-</mo><mn>4</mn><mo>&plusmn;</mo><msqrt><msup><mn>4</mn><mn>2</mn></msup><mo>-</mo><mn>4</mn><mo>&times;</mo><mn>8</mn><mo>&times;</mo><mo>(</mo><mo>-</mo><mn>7</mn><mo>)</mo></msqrt></mrow><mrow><mn>2</mn><mo>&times;</mo><mn>8</mn></mrow></mfrac><mspace linebreak=\"newline\"></mspace><mo>=</mo><mfrac><mrow><mo>-</mo><mn>4</mn><mo>&plusmn;</mo><msqrt><mn>240</mn></msqrt></mrow><mrow><mn>2</mn><mo>&times;</mo><mn>8</mn></mrow></mfrac><mo>=</mo><mfrac><mrow><mo>-</mo><mn>1</mn><mo>&plusmn;</mo><msqrt><mn>15</mn></msqrt></mrow><mn>4</mn></mfrac><mspace linebreak=\"newline\"></mspace><mi>&#2351;&#2361;&#2366;&#2305;</mi><mo>&nbsp;</mo><mo>&nbsp;</mo><mi>&#2344;&#2381;&#2351;&#2370;&#2344;&#2325;&#2379;&#2339;</mi><mo>&nbsp;</mo><mi>&#2361;&#2376;</mi><mo>,</mo><mo>&nbsp;</mo><mi>&#2309;&#2340;&#2307;</mi><mo>&nbsp;</mo><mo>&nbsp;</mo><mi>sin</mi><mo>&nbsp;</mo><mi mathvariant=\"normal\">&theta;</mi><mo>&nbsp;</mo><mo>=</mo><mfrac><mrow><msqrt><mn>15</mn></msqrt><mo>-</mo><mn>1</mn></mrow><mn>4</mn></mfrac></math></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">3. </span><span style=\"font-family: Cambria Math;\">The value of (sin 45&deg; + </span><span style=\"font-family: Cambria Math;\">cos</span><span style=\"font-family: Cambria Math;\"> 45&deg;) is________.</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">3. </span><span style=\"font-family: Cambria Math;\">(sin 45&deg; + </span><span style=\"font-family: Cambria Math;\">cos</span><span style=\"font-family: Cambria Math;\"> 45&deg;) </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> ___________</span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span></p>\n",
                    options_en: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><msqrt><mn>2</mn></msqrt></mfrac></math><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msqrt><mn>2</mn></msqrt></mfrac></math><span style=\"font-family: Cambria Math;\"> </span></p>\n", 
                                "<p>1</p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>2</mn></msqrt></math></p>\n"],
                    options_hi: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><msqrt><mn>2</mn></msqrt></mfrac></math><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msqrt><mn>2</mn></msqrt></mfrac></math> </span></p>\n",
                                "<p>1</p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>2</mn></msqrt></math></p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">3.(</span><span style=\"font-family: Cambria Math;\">d)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><mi>sin</mi><mo>&nbsp;</mo><mn>45</mn><mo>&deg;</mo><mo>+</mo><mi>cos</mi><mo>&nbsp;</mo><mn>45</mn><mo>&deg;</mo><mo>)</mo><mo>&nbsp;</mo><mo>=</mo><mfrac><mn>1</mn><msqrt><mn>2</mn></msqrt></mfrac><mo>+</mo><mfrac><mn>1</mn><msqrt><mn>2</mn></msqrt></mfrac><mspace linebreak=\"newline\"></mspace><mo>=</mo><msqrt><mn>2</mn></msqrt></math></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">3.(</span><span style=\"font-family: Cambria Math;\">d)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><mi>sin</mi><mo>&nbsp;</mo><mn>45</mn><mo>&deg;</mo><mo>+</mo><mi>cos</mi><mo>&nbsp;</mo><mn>45</mn><mo>&deg;</mo><mo>)</mo><mo>&nbsp;</mo><mo>=</mo><mfrac><mn>1</mn><msqrt><mn>2</mn></msqrt></mfrac><mo>+</mo><mfrac><mn>1</mn><msqrt><mn>2</mn></msqrt></mfrac><mspace linebreak=\"newline\"></mspace><mo>=</mo><msqrt><mn>2</mn></msqrt></math></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "17",
                    question_en: "<p>4. <span style=\"font-family: Cambria Math;\">Evaluate the given expression: <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>cos</mi><mn>2</mn></msup><mn>36</mn><mo>&deg;</mo><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mi>cos</mi><mo>&nbsp;</mo><mn>54</mn><mo>&deg;</mo><mo>&nbsp;</mo><mo>.</mo><mo>&nbsp;</mo><mi>sin</mi><mo>&nbsp;</mo><mn>36</mn><mo>&deg;</mo><mo>&nbsp;</mo><mo>+</mo><mo>(</mo><mfrac><mrow><mi>tan</mi><mo>&nbsp;</mo><mn>26</mn><mo>&deg;</mo></mrow><mrow><mi>c</mi><mi>o</mi><mi>t</mi><mo>&nbsp;</mo><mn>64</mn><mo>&deg;</mo></mrow></mfrac><mo>)</mo></math> </span></p>\n",
                    question_hi: "<p>4. <span style=\"font-family: Cambria Math;\">&#2342;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2327;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2381;&#2351;&#2306;&#2332;&#2325;</span><span style=\"font-family: Cambria Math;\"> : <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>cos</mi><mn>2</mn></msup><mn>36</mn><mo>&deg;</mo><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mi>cos</mi><mo>&nbsp;</mo><mn>54</mn><mo>&deg;</mo><mo>&nbsp;</mo><mo>.</mo><mo>&nbsp;</mo><mi>sin</mi><mo>&nbsp;</mo><mn>36</mn><mo>&deg;</mo><mo>&nbsp;</mo><mo>+</mo><mo>(</mo><mfrac><mrow><mi>tan</mi><mo>&nbsp;</mo><mn>26</mn><mo>&deg;</mo></mrow><mrow><mi>c</mi><mi>o</mi><mi>t</mi><mo>&nbsp;</mo><mn>64</mn><mo>&deg;</mo></mrow></mfrac><mo>)</mo></math></span><span style=\"font-family: Cambria Math;\"> ___________ </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2352;&#2366;&#2348;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> I</span></p>\n",
                    options_en: ["<p>4</p>\n", "<p>3</p>\n", 
                                "<p>1</p>\n", "<p>2</p>\n"],
                    options_hi: ["<p>4</p>\n", "<p>3</p>\n",
                                "<p>1</p>\n", "<p>2</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">4.(</span><span style=\"font-family: Cambria Math;\">d)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>cos</mi><mn>2</mn></msup><mn>36</mn><mo>&deg;</mo><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mi>cos</mi><mo>&nbsp;</mo><mn>54</mn><mo>&deg;</mo><mo>&nbsp;</mo><mo>.</mo><mo>&nbsp;</mo><mi>sin</mi><mo>&nbsp;</mo><mn>36</mn><mo>&deg;</mo><mo>&nbsp;</mo><mo>+</mo><mo>(</mo><mfrac><mrow><mi>tan</mi><mo>&nbsp;</mo><mn>26</mn><mo>&deg;</mo></mrow><mrow><mi>c</mi><mi>o</mi><mi>t</mi><mo>&nbsp;</mo><mn>64</mn><mo>&deg;</mo></mrow></mfrac><mo>)</mo><mspace linebreak=\"newline\"></mspace><mo>=</mo><msup><mi>cos</mi><mn>2</mn></msup><mn>36</mn><mo>&deg;</mo><mo>&nbsp;</mo><mo>+</mo><mi>sin</mi><mo>&nbsp;</mo><mn>36</mn><mo>&deg;</mo><mo>&nbsp;</mo><mo>.</mo><mo>&nbsp;</mo><mi>sin</mi><mo>&nbsp;</mo><mn>36</mn><mo>&deg;</mo><mo>&nbsp;</mo><mo>+</mo><mo>(</mo><mfrac><mrow><mi>tan</mi><mo>&nbsp;</mo><mn>26</mn><mo>&deg;</mo></mrow><mrow><mi>tan</mi><mo>&nbsp;</mo><mn>26</mn><mo>&deg;</mo></mrow></mfrac><mo>)</mo><mspace linebreak=\"newline\"></mspace><mo>=</mo><msup><mi>cos</mi><mn>2</mn></msup><mn>36</mn><mo>&deg;</mo><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><msup><mi>sin</mi><mn>2</mn></msup><mn>36</mn><mo>&deg;</mo><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mo>(</mo><mn>1</mn><mo>)</mo><mspace linebreak=\"newline\"></mspace><mo>=</mo><mo>&nbsp;</mo><mo>(</mo><mn>1</mn><mo>)</mo><mo>+</mo><mo>&nbsp;</mo><mo>(</mo><mn>1</mn><mo>)</mo><mo>=</mo><mn>2</mn></math></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">4.(</span><span style=\"font-family: Cambria Math;\">d)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>cos</mi><mn>2</mn></msup><mn>36</mn><mo>&deg;</mo><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mi>cos</mi><mo>&nbsp;</mo><mn>54</mn><mo>&deg;</mo><mo>&nbsp;</mo><mo>.</mo><mo>&nbsp;</mo><mi>sin</mi><mo>&nbsp;</mo><mn>36</mn><mo>&deg;</mo><mo>&nbsp;</mo><mo>+</mo><mo>(</mo><mfrac><mrow><mi>tan</mi><mo>&nbsp;</mo><mn>26</mn><mo>&deg;</mo></mrow><mrow><mi>c</mi><mi>o</mi><mi>t</mi><mo>&nbsp;</mo><mn>64</mn><mo>&deg;</mo></mrow></mfrac><mo>)</mo><mspace linebreak=\"newline\"></mspace><mo>=</mo><msup><mi>cos</mi><mn>2</mn></msup><mn>36</mn><mo>&deg;</mo><mo>&nbsp;</mo><mo>+</mo><mi>sin</mi><mo>&nbsp;</mo><mn>36</mn><mo>&deg;</mo><mo>&nbsp;</mo><mo>.</mo><mo>&nbsp;</mo><mi>sin</mi><mo>&nbsp;</mo><mn>36</mn><mo>&deg;</mo><mo>&nbsp;</mo><mo>+</mo><mo>(</mo><mfrac><mrow><mi>tan</mi><mo>&nbsp;</mo><mn>26</mn><mo>&deg;</mo></mrow><mrow><mi>tan</mi><mo>&nbsp;</mo><mn>26</mn><mo>&deg;</mo></mrow></mfrac><mo>)</mo><mspace linebreak=\"newline\"></mspace><mo>=</mo><msup><mi>cos</mi><mn>2</mn></msup><mn>36</mn><mo>&deg;</mo><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><msup><mi>sin</mi><mn>2</mn></msup><mn>36</mn><mo>&deg;</mo><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mo>(</mo><mn>1</mn><mo>)</mo><mspace linebreak=\"newline\"></mspace><mo>=</mo><mo>&nbsp;</mo><mo>(</mo><mn>1</mn><mo>)</mo><mo>+</mo><mo>&nbsp;</mo><mo>(</mo><mn>1</mn><mo>)</mo><mo>=</mo><mn>2</mn></math></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "17",
                    question_en: "<p>5. <span style=\"font-family: Cambria Math;\">&Delta;RMS is right-angled at M. The length of the base, RM = 4 cm and the length of perpendicular MS = 3 cm. Find the value of sec R.</span></p>\n",
                    question_hi: "<p>5. <span style=\"font-family: Cambria Math;\">&Delta;RMS </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2379;&#2339;</span><span style=\"font-family: Cambria Math;\"> M </span><span style=\"font-family: Cambria Math;\">&#2360;&#2350;&#2325;&#2379;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2310;&#2343;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> RM </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2354;&#2306;&#2348;&#2366;&#2312;</span><span style=\"font-family: Cambria Math;\"> = 4 cm </span><span style=\"font-family: Cambria Math;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2354;&#2306;&#2348;</span><span style=\"font-family: Cambria Math;\"> MS </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2354;&#2306;&#2348;&#2366;&#2312;</span><span style=\"font-family: Cambria Math;\"> = 3 cm </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> sec R </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2332;&#2381;&#2334;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;&#2332;&#2367;&#2319;&#2404;</span></p>\n",
                    options_en: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>4</mn></mfrac></math></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>4</mn></mfrac></math></p>\n", 
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>5</mn></mfrac></math></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>5</mn></mfrac></math></p>\n"],
                    options_hi: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>4</mn></mfrac></math></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>4</mn></mfrac></math></p>\n",
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>5</mn></mfrac></math></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>5</mn></mfrac></math></p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">5.(</span><span style=\"font-family: Cambria Math;\">b)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Pythagoras triplets :- (3</span><span style=\"font-family: Cambria Math;\">,4,5</span><span style=\"font-family: Cambria Math;\">)</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1698665330/word/media/image2.png\" width=\"105\" height=\"112\"></p>\r\n<p><span style=\"font-family: Cambria Math;\">Sec R = </span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>H</mi><mi>y</mi><mi>p</mi><mi>o</mi><mi>t</mi><mi>e</mi><mi>n</mi><mi>u</mi><mi>s</mi><mi>e</mi></mrow><mrow><mi>B</mi><mi>a</mi><mi>s</mi><mi>e</mi></mrow></mfrac><mo>=</mo><mfrac><mn>5</mn><mn>4</mn></mfrac></math> </span><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">5.(</span><span style=\"font-family: Cambria Math;\">b)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&#2346;&#2366;&#2311;&#2341;&#2366;&#2327;&#2379;&#2352;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2340;&#2381;&#2352;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> :- (3,4,5)</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1698665330/word/media/image2.png\" width=\"105\" height=\"112\"></p>\r\n<p><span style=\"font-family: Cambria Math;\">Sec R = </span><span style=\"font-family: Cambria Math;\"> <math style=\"font-family:Verdana\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>&#2325;&#2352;&#2381;&#2339;</mi><mi>&#2310;&#2343;&#2366;&#2352;</mi></mfrac><mo>=</mo><mfrac><mn>5</mn><mn>4</mn></mfrac></math> </span><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "17",
                    question_en: "<p>6. <span style=\"font-family: Cambria Math;\">If A = 60&deg; and B = 30&deg;, find the value of<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mi>tan</mi><mo>&nbsp;</mo><mi>A</mi><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mi>tan</mi><mo>&nbsp;</mo><mi>B</mi><mo>)</mo></mrow><mrow><mo>(</mo><mn>1</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mi>tan</mi><mi>A</mi><mo>&nbsp;</mo><mi>tan</mi><mo>&nbsp;</mo><mi>B</mi><mo>)</mo><mo>&nbsp;</mo><mo>&nbsp;</mo></mrow></mfrac></math> </span><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                    question_hi: "<p>6. <span style=\"font-family: Cambria Math;\">&#2351;&#2342;</span><span style=\"font-family: Cambria Math;\">&#2367;</span><span style=\"font-family: Cambria Math;\"> A</span><span style=\"font-family: Cambria Math;\"> = 60&deg; </span><span style=\"font-family: Cambria Math;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> B = 30&deg;, </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mi>tan</mi><mo>&nbsp;</mo><mi>A</mi><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mi>tan</mi><mo>&nbsp;</mo><mi>B</mi><mo>)</mo></mrow><mrow><mo>(</mo><mn>1</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mi>tan</mi><mi>A</mi><mo>&nbsp;</mo><mi>tan</mi><mo>&nbsp;</mo><mi>B</mi><mo>)</mo><mo>&nbsp;</mo><mo>&nbsp;</mo></mrow></mfrac></math></span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2332;&#2381;&#2334;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;&#2332;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> I</span></p>\n",
                    options_en: ["<p>3</p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msqrt><mn>3</mn></msqrt></mfrac></math></p>\n", 
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math></p>\n", "<p>1</p>\n"],
                    options_hi: ["<p>3</p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msqrt><mn>3</mn></msqrt></mfrac></math></p>\n",
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math></p>\n", "<p>1</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">6.(</span><span style=\"font-family: Cambria Math;\">b)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mi>tan</mi><mo>&nbsp;</mo><mi>A</mi><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mi>tan</mi><mo>&nbsp;</mo><mi>B</mi><mo>)</mo></mrow><mrow><mo>(</mo><mn>1</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mi>tan</mi><mi>A</mi><mo>&nbsp;</mo><mi>tan</mi><mo>&nbsp;</mo><mi>B</mi><mo>)</mo><mo>&nbsp;</mo><mo>&nbsp;</mo></mrow></mfrac><mo>=</mo><mo>&nbsp;</mo><mi>tan</mi><mo>&nbsp;</mo><mo>(</mo><mi>A</mi><mo>-</mo><mi>B</mi><mo>)</mo><mspace linebreak=\"newline\"></mspace><mo>=</mo><mo>&nbsp;</mo><mi>tan</mi><mo>&nbsp;</mo><mo>(</mo><mn>60</mn><mo>&deg;</mo><mo>-</mo><mn>30</mn><mo>&deg;</mo><mo>)</mo><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mi>tan</mi><mo>(</mo><mo>&nbsp;</mo><mn>30</mn><mo>&deg;</mo><mo>)</mo><mo>&nbsp;</mo><mo>=</mo><mfrac><mrow><mo>&nbsp;</mo><mn>1</mn></mrow><msqrt><mn>3</mn></msqrt></mfrac></math></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">6.(</span><span style=\"font-family: Cambria Math;\">b)</span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mi>tan</mi><mo>&nbsp;</mo><mi>A</mi><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mi>tan</mi><mo>&nbsp;</mo><mi>B</mi><mo>)</mo></mrow><mrow><mo>(</mo><mn>1</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mi>tan</mi><mi>A</mi><mo>&nbsp;</mo><mi>tan</mi><mo>&nbsp;</mo><mi>B</mi><mo>)</mo><mo>&nbsp;</mo><mo>&nbsp;</mo></mrow></mfrac><mo>=</mo><mo>&nbsp;</mo><mi>tan</mi><mo>&nbsp;</mo><mo>(</mo><mi>A</mi><mo>-</mo><mi>B</mi><mo>)</mo><mspace linebreak=\"newline\"></mspace><mo>=</mo><mo>&nbsp;</mo><mi>tan</mi><mo>&nbsp;</mo><mo>(</mo><mn>60</mn><mo>&deg;</mo><mo>-</mo><mn>30</mn><mo>&deg;</mo><mo>)</mo><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mi>tan</mi><mo>(</mo><mo>&nbsp;</mo><mn>30</mn><mo>&deg;</mo><mo>)</mo><mo>&nbsp;</mo><mo>=</mo><mfrac><mrow><mo>&nbsp;</mo><mn>1</mn></mrow><msqrt><mn>3</mn></msqrt></mfrac></math></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">7.</span><span style=\"font-family: Cambria Math;\"> Find the value of the following expression.</span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>2</mn><mo>(</mo><msup><mi>tan</mi><mn>2</mn></msup><mi>A</mi><mo>&nbsp;</mo><msup><mi>cos</mi><mn>2</mn></msup><mi>A</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><msup><mi>cos</mi><mn>2</mn></msup><mi>A</mi><mo>)</mo></math></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">7.</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2381;&#2351;&#2306;&#2332;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2332;&#2381;&#2334;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;&#2332;&#2367;&#2319;&#2404;</span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>2</mn><mo>(</mo><msup><mi>tan</mi><mn>2</mn></msup><mi>A</mi><mo>&nbsp;</mo><msup><mi>cos</mi><mn>2</mn></msup><mi>A</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><msup><mi>cos</mi><mn>2</mn></msup><mi>A</mi><mo>)</mo></math></p>\n",
                    options_en: ["<p>4</p>\n", "<p>1</p>\n", 
                                "<p>3</p>\n", "<p>2</p>\n"],
                    options_hi: ["<p>4</p>\n", "<p>1</p>\n",
                                "<p>3</p>\n", "<p>2</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">7.(</span><span style=\"font-family: Cambria Math;\">d)</span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>2</mn><mo>(</mo><msup><mi>tan</mi><mn>2</mn></msup><mi>A</mi><mo>&nbsp;</mo><mo>.</mo><mo>&nbsp;</mo><msup><mi>cos</mi><mn>2</mn></msup><mi>A</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><msup><mi>cos</mi><mn>2</mn></msup><mi>A</mi><mo>)</mo><mspace linebreak=\"newline\"></mspace><mo>=</mo><mo>&nbsp;</mo><mn>2</mn><msup><mi>cos</mi><mn>2</mn></msup><mi>A</mi><mo>(</mo><msup><mi>tan</mi><mn>2</mn></msup><mi>A</mi><mo>&nbsp;</mo><mo>+</mo><mn>1</mn><mo>)</mo><mspace linebreak=\"newline\"></mspace><mo>=</mo><mn>2</mn><msup><mi>cos</mi><mn>2</mn></msup><mi>A</mi><mo>(</mo><mi>s</mi><mi>e</mi><msup><mi>c</mi><mn>2</mn></msup><mi>A</mi><mo>)</mo><mo>=</mo><mn>2</mn></math></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">7.(</span><span style=\"font-family: Cambria Math;\">d)</span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>2</mn><mo>(</mo><msup><mi>tan</mi><mn>2</mn></msup><mi>A</mi><mo>&nbsp;</mo><mo>.</mo><mo>&nbsp;</mo><msup><mi>cos</mi><mn>2</mn></msup><mi>A</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><msup><mi>cos</mi><mn>2</mn></msup><mi>A</mi><mo>)</mo><mspace linebreak=\"newline\"></mspace><mo>=</mo><mo>&nbsp;</mo><mn>2</mn><msup><mi>cos</mi><mn>2</mn></msup><mi>A</mi><mo>(</mo><msup><mi>tan</mi><mn>2</mn></msup><mi>A</mi><mo>&nbsp;</mo><mo>+</mo><mn>1</mn><mo>)</mo><mspace linebreak=\"newline\"></mspace><mo>=</mo><mn>2</mn><msup><mi>cos</mi><mn>2</mn></msup><mi>A</mi><mo>(</mo><mi>s</mi><mi>e</mi><msup><mi>c</mi><mn>2</mn></msup><mi>A</mi><mo>)</mo><mo>=</mo><mn>2</mn></math></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">8.</span><span style=\"font-family: Cambria Math;\"> If A, B, C are the acute angles, such that tan (A + B &ndash; C) =</span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msqrt><mn>3</mn></msqrt></mfrac></math>, cos (B + C &ndash; A) = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> and sin (C + A - B) </span><span style=\"font-family: Cambria Math;\">=<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msqrt><mn>2</mn></msqrt></mfrac></math> </span><span style=\"font-family: Cambria Math;\"> . The value of A + B + C will be equal to:</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">8.</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> A, B, C </span><span style=\"font-family: Cambria Math;\">&#2344;&#2381;&#2351;&#2370;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2379;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2311;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2381;&#2352;&#2325;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2367;</span><span style=\"font-family: Cambria Math;\"> tan (A + B &ndash; C</span><span style=\"font-family: Cambria Math;\">) =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msqrt><mn>3</mn></msqrt></mfrac></math></span><span style=\"font-family: Cambria Math;\">, cos (B + C &ndash; A) </span><span style=\"font-family: Cambria Math;\">= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> sin(C + A &ndash; B) = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msqrt><mn>2</mn></msqrt></mfrac></math></span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> A + B + C </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\">_____ </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2352;&#2366;&#2348;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2379;&#2327;&#2366;&#2404;</span></p>\n",
                    options_en: ["<p>90&deg;</p>\n", "<p>110&deg;</p>\n", 
                                "<p>170 &deg;</p>\n", "<p>135&deg;</p>\n"],
                    options_hi: ["<p>90&deg;</p>\n", "<p>110&deg;</p>\n",
                                "<p>170 &deg;</p>\n", "<p>135&deg;</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">8.(</span><span style=\"font-family: Cambria Math;\">d)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>tan</mi><mo>(</mo><mi>A</mi><mo>+</mo><mi>B</mi><mo>-</mo><mi>C</mi><mo>)</mo><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mfrac><mn>1</mn><msqrt><mn>3</mn></msqrt></mfrac><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mi>tan</mi><mo>&nbsp;</mo><mn>30</mn><mo>&deg;</mo><mspace linebreak=\"newline\"></mspace><mo>&nbsp;</mo><mo>&rArr;</mo><mo>(</mo><mi>A</mi><mo>+</mo><mi>B</mi><mo>-</mo><mi>C</mi><mo>)</mo><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mn>30</mn><mo>&deg;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&hellip;</mo><mo>.</mo><mo>.</mo><mi>e</mi><mo>.</mo><mi>q</mi><mo>.</mo><mo>(</mo><mn>1</mn><mo>)</mo><mspace linebreak=\"newline\"></mspace><mi>cos</mi><mo>(</mo><mi>B</mi><mo>+</mo><mi>C</mi><mo>-</mo><mi>A</mi><mo>)</mo><mo>&nbsp;</mo><mo>=</mo><mfrac><mrow><mo>&nbsp;</mo><mn>1</mn><mo>&nbsp;</mo></mrow><mrow><mn>2</mn><mo>&nbsp;</mo></mrow></mfrac><mo>=</mo><mo>&nbsp;</mo><mi>cos</mi><mo>&nbsp;</mo><mn>60</mn><mo>&deg;</mo><mspace linebreak=\"newline\"></mspace><mo>&rArr;</mo><mo>&nbsp;</mo><mo>(</mo><mi>B</mi><mo>+</mo><mi>C</mi><mo>-</mo><mi>A</mi><mo>)</mo><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mn>60</mn><mo>&deg;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&hellip;</mo><mo>.</mo><mo>.</mo><mo>.</mo><mo>.</mo><mi>e</mi><mo>.</mo><mi>q</mi><mo>.</mo><mo>(</mo><mn>2</mn><mo>)</mo><mspace linebreak=\"newline\"></mspace><mi>sin</mi><mo>(</mo><mi>C</mi><mo>+</mo><mi>A</mi><mo>-</mo><mi>B</mi><mo>)</mo><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mfrac><mn>1</mn><msqrt><mn>2</mn></msqrt></mfrac><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mi>sin</mi><mo>&nbsp;</mo><mn>45</mn><mo>&deg;</mo><mspace linebreak=\"newline\"></mspace><mo>&rArr;</mo><mo>&nbsp;</mo><mo>(</mo><mi>C</mi><mo>+</mo><mi>A</mi><mo>-</mo><mi>B</mi><mo>)</mo><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mn>45</mn><mo>&deg;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&hellip;</mo><mo>.</mo><mi>e</mi><mo>.</mo><mi>q</mi><mo>.</mo><mo>(</mo><mn>3</mn><mo>)</mo><mspace linebreak=\"newline\"></mspace><mi>Adding</mi><mo>&nbsp;</mo><mi mathvariant=\"normal\">e</mi><mo>.</mo><mi mathvariant=\"normal\">q</mi><mo>.</mo><mo>(</mo><mn>1</mn><mo>)</mo><mo>&nbsp;</mo><mo>,</mo><mo>&nbsp;</mo><mi mathvariant=\"normal\">e</mi><mo>.</mo><mi mathvariant=\"normal\">q</mi><mo>.</mo><mo>(</mo><mn>2</mn><mo>)</mo><mo>&nbsp;</mo><mi>and</mi><mo>&nbsp;</mo><mi mathvariant=\"normal\">e</mi><mo>.</mo><mi mathvariant=\"normal\">q</mi><mo>.</mo><mo>(</mo><mn>3</mn><mo>)</mo><mo>&nbsp;</mo><mo>,</mo><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mo>&nbsp;</mo><mi>we</mi><mo>&nbsp;</mo><mi>get</mi><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mi>A</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mi>B</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mi>C</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mn>30</mn><mo>&deg;</mo><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mn>60</mn><mo>&deg;</mo><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mn>45</mn><mo>&deg;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mn>135</mn><mo>&deg;</mo></math></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">8.(</span><span style=\"font-family: Cambria Math;\">d)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><math style=\"font-family:Verdana\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>tan</mi><mo>(</mo><mi>A</mi><mo>+</mo><mi>B</mi><mo>-</mo><mi>C</mi><mo>)</mo><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mfrac><mn>1</mn><msqrt><mn>3</mn></msqrt></mfrac><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mi>tan</mi><mo>&nbsp;</mo><mn>30</mn><mo>&deg;</mo><mspace linebreak=\"newline\"></mspace><mo>&nbsp;</mo><mo>&rArr;</mo><mo>(</mo><mi>A</mi><mo>+</mo><mi>B</mi><mo>-</mo><mi>C</mi><mo>)</mo><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mn>30</mn><mo>&deg;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&hellip;</mo><mo>.</mo><mo>.</mo><mi>&#2360;&#2350;&#2368;</mi><mo>.</mo><mo>(</mo><mn>1</mn><mo>)</mo><mspace linebreak=\"newline\"></mspace><mi>cos</mi><mo>(</mo><mi>B</mi><mo>+</mo><mi>C</mi><mo>-</mo><mi>A</mi><mo>)</mo><mo>&nbsp;</mo><mo>=</mo><mfrac><mrow><mo>&nbsp;</mo><mn>1</mn><mo>&nbsp;</mo></mrow><mrow><mn>2</mn><mo>&nbsp;</mo></mrow></mfrac><mo>=</mo><mo>&nbsp;</mo><mi>cos</mi><mo>&nbsp;</mo><mn>60</mn><mo>&deg;</mo><mspace linebreak=\"newline\"></mspace><mo>&rArr;</mo><mo>&nbsp;</mo><mo>(</mo><mi>B</mi><mo>+</mo><mi>C</mi><mo>-</mo><mi>A</mi><mo>)</mo><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mn>60</mn><mo>&deg;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&hellip;</mo><mo>.</mo><mo>.</mo><mi>&#2360;&#2350;&#2368;</mi><mo>.</mo><mo>(</mo><mn>2</mn><mo>)</mo><mspace linebreak=\"newline\"></mspace><mi>sin</mi><mo>(</mo><mi>C</mi><mo>+</mo><mi>A</mi><mo>-</mo><mi>B</mi><mo>)</mo><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mfrac><mn>1</mn><msqrt><mn>2</mn></msqrt></mfrac><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mi>sin</mi><mo>&nbsp;</mo><mn>45</mn><mo>&deg;</mo><mspace linebreak=\"newline\"></mspace><mo>&rArr;</mo><mo>&nbsp;</mo><mo>(</mo><mi>C</mi><mo>+</mo><mi>A</mi><mo>-</mo><mi>B</mi><mo>)</mo><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mn>45</mn><mo>&deg;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&hellip;</mo><mo>.</mo><mo>.</mo><mi>&#2360;&#2350;&#2368;</mi><mo>(</mo><mn>3</mn><mo>)</mo><mspace linebreak=\"newline\"></mspace><mi>&#2360;&#2350;&#2368;&#2325;&#2352;&#2339;</mi><mo>&nbsp;</mo><mo>(</mo><mn>1</mn><mo>)</mo><mo>&nbsp;</mo><mo>,</mo><mo>&nbsp;</mo><mi>&#2360;&#2350;&#2368;&#2325;&#2352;&#2339;</mi><mo>&nbsp;</mo><mo>(</mo><mn>2</mn><mo>)</mo><mo>&nbsp;</mo><mi>&#2324;&#2352;</mi><mo>&nbsp;</mo><mo>&nbsp;</mo><mi>&#2360;&#2350;&#2368;&#2325;&#2352;&#2339;</mi><mo>(</mo><mn>3</mn><mo>)</mo><mo>&nbsp;</mo><mi>&#2332;&#2379;&#2337;&#2364;&#2344;&#2375;</mi><mo>&nbsp;</mo><mo>&nbsp;</mo><mi>&#2346;&#2352;</mi><mo>&nbsp;</mo><mo>&nbsp;</mo><mi>&#2361;&#2350;&#2375;&#2306;</mi><mo>&nbsp;</mo><mo>&nbsp;</mo><mi>&#2346;&#2381;&#2352;&#2366;&#2346;&#2381;&#2340;</mi><mo>&nbsp;</mo><mo>&nbsp;</mo><mi>&#2361;&#2379;&#2340;&#2366;</mi><mo>&nbsp;</mo><mo>&nbsp;</mo><mi>&#2361;&#2376;</mi><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mi>A</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mi>B</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mi>C</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mn>30</mn><mo>&deg;</mo><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mn>60</mn><mo>&deg;</mo><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mn>45</mn><mo>&deg;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mn>135</mn><mo>&deg;</mo></math></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">9. </span><span style=\"font-family: Cambria Math;\">Find the value of the following expression.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>1</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mi>s</mi><mi>e</mi><mi>c</mi><mi>&Oslash;</mi><mo>)</mo></mrow><mrow><mo>&nbsp;</mo><mo>(</mo><mi>s</mi><mi>e</mi><mi>c</mi><mi>&Oslash;</mi><mo>)</mo><mo>&nbsp;</mo></mrow></mfrac><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>(</mo><mn>1</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mi>cos</mi><mi>&Oslash;</mi><mo>)</mo></math></span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">9. </span><span style=\"font-family: Cambria Math;\">&#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2381;&#2351;&#2306;&#2332;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2332;&#2381;&#2334;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;&#2332;&#2367;&#2319;&#2404;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>1</mn><mo>+</mo><mo>&nbsp;</mo><mi>s</mi><mi>e</mi><mi>c</mi><mi>&Oslash;</mi><mo>)</mo></mrow><mrow><mo>&nbsp;</mo><mo>(</mo><mi>s</mi><mi>e</mi><mi>c</mi><mi>&Oslash;</mi><mo>)</mo><mo>&nbsp;</mo></mrow></mfrac><mo>(</mo><mn>1</mn><mo>-</mo><mi>cos</mi><mi>&Oslash;</mi><mo>)</mo></math></span></p>\n",
                    options_en: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>cos</mi><mn>2</mn></msup><mi>&Oslash;</mi></math></p>\n", "<p>sec&sup2;&Oslash;</p>\n", 
                                "<p>cot&sup2;&Oslash;</p>\n", "<p>sin&sup2;&Oslash;</p>\n"],
                    options_hi: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>cos</mi><mn>2</mn></msup><mi>&Oslash;</mi></math></p>\n", "<p>sec&sup2;&Oslash;</p>\n",
                                "<p>cot&sup2;&Oslash;</p>\n", "<p>sin&sup2;&Oslash;</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">9.(</span><span style=\"font-family: Cambria Math;\">d)</span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>1</mn><mo>+</mo><mo>&nbsp;</mo><mi>sec&Oslash;</mi><mo>)</mo></mrow><mrow><mo>&nbsp;</mo><mo>(</mo><mi>sec&Oslash;</mi><mo>)</mo><mo>&nbsp;</mo></mrow></mfrac><mo>&times;</mo><mo>(</mo><mn>1</mn><mo>-</mo><mi>cos&Oslash;</mi><mo>)</mo><mspace linebreak=\"newline\"></mspace><mo>=</mo><mo>&nbsp;</mo><mo>(</mo><mn>1</mn><mo>+</mo><mi>cos&Oslash;</mi><mo>)</mo><mo>&nbsp;</mo><mo>(</mo><mn>1</mn><mo>-</mo><mi>cos&Oslash;</mi><mo>)</mo><mspace linebreak=\"newline\"></mspace><mo>=</mo><mo>&nbsp;</mo><mo>(</mo><mn>1</mn><mo>-</mo><msup><mi>cos</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&Oslash;</mi><mo>)</mo><mo>=</mo><msup><mi>sin</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&Oslash;</mi></math></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">9.(</span><span style=\"font-family: Cambria Math;\">d)</span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>1</mn><mo>+</mo><mo>&nbsp;</mo><mi>sec&Oslash;</mi><mo>)</mo></mrow><mrow><mo>&nbsp;</mo><mo>(</mo><mi>sec&Oslash;</mi><mo>)</mo><mo>&nbsp;</mo></mrow></mfrac><mo>&times;</mo><mo>(</mo><mn>1</mn><mo>-</mo><mi>cos&Oslash;</mi><mo>)</mo><mspace linebreak=\"newline\"></mspace><mo>=</mo><mo>&nbsp;</mo><mo>(</mo><mn>1</mn><mo>+</mo><mi>cos&Oslash;</mi><mo>)</mo><mo>&nbsp;</mo><mo>(</mo><mn>1</mn><mo>-</mo><mi>cos&Oslash;</mi><mo>)</mo><mspace linebreak=\"newline\"></mspace><mo>=</mo><mo>&nbsp;</mo><mo>(</mo><mn>1</mn><mo>-</mo><msup><mi>cos</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&Oslash;</mi><mo>)</mo><mo>=</mo><msup><mi>sin</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&Oslash;</mi></math></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">10</span><span style=\"font-family: Cambria Math;\">.</span><span style=\"font-family: Cambria Math;\"> Let ABC be a right-angled triangle where</span><span style=\"font-family: Cambria Math;\"> &ang;A = 90&deg; and &ang;</span><span style=\"font-family: Cambria Math;\">C = 45&deg;. Find the value of sec C + sin C sec C.</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">10</span><span style=\"font-family: Cambria Math;\">.</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2354;&#2368;&#2332;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> ABC </span><span style=\"font-family: Cambria Math;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2350;&#2325;&#2379;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2340;&#2381;&#2352;&#2367;&#2349;&#2369;&#2332;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2332;&#2361;&#2366;&#2305;</span><span style=\"font-family: Cambria Math;\"> &ang;</span><span style=\"font-family: Cambria Math;\"> A= 90 </span><span style=\"font-family: Cambria Math;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> &ang;</span><span style=\"font-family: Cambria Math;\">C = 45&deg; </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> sec C + sin C sec C </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2332;&#2381;&#2334;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;&#2332;&#2367;</span><span style=\"font-family: Cambria Math;\">&#2319;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                    options_en: ["<p>1</p>\n", "<p>1- <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>2</mn></msqrt></math><span style=\"font-family: Cambria Math;\"> </span></p>\n", 
                                "<p>1+<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>2</mn></msqrt></math></p>\n", "<p><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>2</mn></msqrt></math>-1</span></p>\n"],
                    options_hi: ["<p>1</p>\n", "<p>1- <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>2</mn></msqrt></math><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                                "<p>1+<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>2</mn></msqrt></math></p>\n", "<p><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>2</mn></msqrt></math>- 1</span></p>\n"],
                    solution_en: "<p>10<span style=\"font-family: Cambria Math;\">.(c)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>s</mi><mi>e</mi><mi>c</mi><mo>&nbsp;</mo><mi>C</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mi>sin</mi><mo>&nbsp;</mo><mi>C</mi><mo>&nbsp;</mo><mo>.</mo><mo>&nbsp;</mo><mi>s</mi><mi>e</mi><mi>c</mi><mo>&nbsp;</mo><mi>C</mi><mo>&nbsp;</mo><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mo>=</mo><mo>&nbsp;</mo><mi>s</mi><mi>e</mi><mi>c</mi><mo>&nbsp;</mo><mi>C</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mi>tan</mi><mo>&nbsp;</mo><mi>C</mi><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mi>s</mi><mi>e</mi><mi>c</mi><mo>&nbsp;</mo><mn>45</mn><mo>&deg;</mo><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mi>tan</mi><mo>&nbsp;</mo><mn>45</mn><mo>&deg;</mo><mspace linebreak=\"newline\"></mspace><mo>=</mo><mo>&nbsp;</mo><msqrt><mn>2</mn></msqrt><mo>+</mo><mn>1</mn></math></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                    solution_hi: "<p>10<span style=\"font-family: Cambria Math;\">.(c)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>s</mi><mi>e</mi><mi>c</mi><mo>&nbsp;</mo><mi>C</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mi>sin</mi><mo>&nbsp;</mo><mi>C</mi><mo>&nbsp;</mo><mo>.</mo><mo>&nbsp;</mo><mi>s</mi><mi>e</mi><mi>c</mi><mo>&nbsp;</mo><mi>C</mi><mo>&nbsp;</mo><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mo>=</mo><mo>&nbsp;</mo><mi>s</mi><mi>e</mi><mi>c</mi><mo>&nbsp;</mo><mi>C</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mi>tan</mi><mo>&nbsp;</mo><mi>C</mi><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mi>s</mi><mi>e</mi><mi>c</mi><mo>&nbsp;</mo><mn>45</mn><mo>&deg;</mo><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mi>tan</mi><mo>&nbsp;</mo><mn>45</mn><mo>&deg;</mo><mspace linebreak=\"newline\"></mspace><mo>=</mo><mo>&nbsp;</mo><msqrt><mn>2</mn></msqrt><mo>+</mo><mn>1</mn></math></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">11</span><span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Cambria Math;\">If (4 sin&theta; </span><span style=\"font-family: Cambria Math;\">+ 5 cos&theta;</span><span style=\"font-family: Cambria Math;\">) = 3, then the value of (4 cos&theta;</span><span style=\"font-family: Cambria Math;\">-5 sin&theta;</span><span style=\"font-family: Cambria Math;\">) is:</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">11</span><span style=\"font-family: Cambria Math;\">.</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> (4 sin&theta;&nbsp;</span><span style=\"font-family: Cambria Math;\"> + 5 cos&theta; </span><span style=\"font-family: Cambria Math;\">) = 3, </span><span style=\"font-family: Cambria Math;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> (4 </span><span style=\"font-family: Cambria Math;\">cos&theta;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">- 5 sin&theta;</span><span style=\"font-family: Cambria Math;\">) </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>3<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>2</mn></msqrt></math></p>\n", "<p>4<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>2</mn></msqrt></math></p>\n", 
                                "<p>2<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math></p>\n", "<p>2<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>5</mn></msqrt></math></p>\n"],
                    options_hi: ["<p>3<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>2</mn></msqrt></math></p>\n", "<p>4<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>2</mn></msqrt></math></p>\n",
                                "<p>2<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math></p>\n", "<p>2<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>5</mn></msqrt></math></p>\n"],
                    solution_en: "<p>11<span style=\"font-family: Cambria Math;\">.(b)</span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>4</mn><mi>sin&theta;</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>5</mn><mi>cos</mi><mi>&theta;</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mn>3</mn><mspace linebreak=\"newline\"></mspace><mn>4</mn><mi>cos</mi><mi>&theta;</mi><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>5</mn><mi>sin</mi><mo>&nbsp;</mo><mi>&theta;</mi><mo>=</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mi>x</mi><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mi>_</mi><mi>_</mi><mi>_</mi><mi>_</mi><mi>_</mi><mi>_</mi><mi>_</mi><mi>_</mi><mi>_</mi><mi>_</mi><mi>_</mi><mi>_</mi><mi>_</mi><mi>_</mi><mi>_</mi><mi>_</mi><mi>_</mi><mi>_</mi><mspace linebreak=\"newline\"></mspace><msup><mrow><mo>(</mo><mn>4</mn><mo>)</mo></mrow><mn>2</mn></msup><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><msup><mrow><mo>(</mo><mn>5</mn><mo>)</mo></mrow><mn>2</mn></msup><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><msup><mrow><mo>(</mo><mn>3</mn><mo>)</mo></mrow><mn>2</mn></msup><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><msup><mrow><mo>(</mo><mi>x</mi><mo>)</mo></mrow><mn>2</mn></msup><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mi>x</mi><mo>&nbsp;</mo><mo>=</mo><msqrt><mo>&nbsp;</mo><mn>16</mn><mo>+</mo><mn>25</mn><mo>-</mo><mn>9</mn><mo>&nbsp;</mo></msqrt><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><msqrt><mn>32</mn><mo>&nbsp;</mo></msqrt><mspace linebreak=\"newline\"></mspace><mo>=</mo><mo>&nbsp;</mo><mn>4</mn><msqrt><mn>2</mn></msqrt></math></p>\n",
                    solution_hi: "<p>11<span style=\"font-family: Cambria Math;\">.(b)</span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>4</mn><mi>sin&theta;</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>5</mn><mi>cos</mi><mi>&theta;</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mn>3</mn><mspace linebreak=\"newline\"></mspace><mn>4</mn><mi>cos</mi><mi>&theta;</mi><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>5</mn><mi>sin</mi><mo>&nbsp;</mo><mi>&theta;</mi><mo>=</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mi>x</mi><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mi>_</mi><mi>_</mi><mi>_</mi><mi>_</mi><mi>_</mi><mi>_</mi><mi>_</mi><mi>_</mi><mi>_</mi><mi>_</mi><mi>_</mi><mi>_</mi><mi>_</mi><mi>_</mi><mi>_</mi><mi>_</mi><mi>_</mi><mspace linebreak=\"newline\"></mspace><msup><mrow><mo>(</mo><mn>4</mn><mo>)</mo></mrow><mn>2</mn></msup><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><msup><mrow><mo>(</mo><mn>5</mn><mo>)</mo></mrow><mn>2</mn></msup><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><msup><mrow><mo>(</mo><mn>3</mn><mo>)</mo></mrow><mn>2</mn></msup><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><msup><mrow><mo>(</mo><mi>x</mi><mo>)</mo></mrow><mn>2</mn></msup><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mi>x</mi><mo>&nbsp;</mo><mo>=</mo><msqrt><mo>&nbsp;</mo><mn>16</mn><mo>+</mo><mn>25</mn><mo>-</mo><mn>9</mn><mo>&nbsp;</mo></msqrt><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><msqrt><mn>32</mn><mo>&nbsp;</mo></msqrt><mspace linebreak=\"newline\"></mspace><mo>=</mo><mo>&nbsp;</mo><mn>4</mn><msqrt><mn>2</mn></msqrt></math></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">12</span><span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Cambria Math;\">If</span><span style=\"font-family: Cambria Math;\"> Cosec&theta; -</span><span style=\"font-family: Cambria Math;\"> cot&theta;=<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>2</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\">, then the value of Cosec&theta;</span><span style=\"font-family: Cambria Math;\"> will be:</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">12</span><span style=\"font-family: Cambria Math;\">.</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> Cosec&theta;&nbsp;</span><span style=\"font-family: Cambria Math;\"> - cot&theta;&nbsp;</span><span style=\"font-family: Cambria Math;\"> =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>2</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> Cosec&theta;&nbsp;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2379;&#2327;&#2366;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>49</mn><mn>28</mn></mfrac></math><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>21</mn><mn>28</mn></mfrac></math> </span></p>\n", 
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>47</mn><mn>28</mn></mfrac></math></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>53</mn><mn>28</mn></mfrac></math></p>\n"],
                    options_hi: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>49</mn><mn>28</mn></mfrac></math><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>21</mn><mn>28</mn></mfrac></math> </span></p>\n",
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>47</mn><mn>28</mn></mfrac></math></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>53</mn><mn>28</mn></mfrac></math></p>\n"],
                    solution_en: "<p>12<span style=\"font-family: Cambria Math;\">.(d)</span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>If</mi><mo>&nbsp;</mo><mi>cosec&theta;</mi><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mi>cot&theta;</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mfrac><mn>7</mn><mn>2</mn></mfrac><mo>&nbsp;</mo><mo>,</mo><mo>&nbsp;</mo><mi>then</mi><mo>&nbsp;</mo><mi>cosec&theta;</mi><mo>+</mo><mo>&nbsp;</mo><mi>cot&theta;</mi><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mfrac><mn>2</mn><mn>7</mn></mfrac><mspace linebreak=\"newline\"></mspace><mi>Adding</mi><mo>&nbsp;</mo><mi>both</mi><mo>&nbsp;</mo><mi>the</mi><mo>&nbsp;</mo><mi>equation</mi><mo>&nbsp;</mo><mo>,</mo><mo>&nbsp;</mo><mi>we</mi><mo>&nbsp;</mo><mi>get</mi><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mn>2</mn><mi>cosec&theta;</mi><mo>&nbsp;</mo><mo>=</mo><mfrac><mrow><mo>&nbsp;</mo><mn>7</mn></mrow><mn>2</mn></mfrac><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mfrac><mn>2</mn><mn>7</mn></mfrac><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mfrac><mn>53</mn><mn>14</mn></mfrac><mspace linebreak=\"newline\"></mspace><mi>cosec&theta;</mi><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mfrac><mn>53</mn><mn>28</mn></mfrac></math></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                    solution_hi: "<p>12<span style=\"font-family: Cambria Math;\">.(d)</span></p>\r\n<p><math style=\"font-family:Verdana\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#2351;&#2342;&#2367;</mi><mo>&nbsp;</mo><mo>&nbsp;</mo><mi>cosec&theta;</mi><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mi>cot&theta;</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mfrac><mn>7</mn><mn>2</mn></mfrac><mo>&nbsp;</mo><mo>,</mo><mo>&nbsp;</mo><mi>&#2340;&#2348;</mi><mo>&nbsp;</mo><mo>&nbsp;</mo><mi>cosec&theta;</mi><mo>+</mo><mo>&nbsp;</mo><mi>cot&theta;</mi><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mfrac><mn>2</mn><mn>7</mn></mfrac><mspace linebreak=\"newline\"></mspace><mi>&#2342;&#2379;&#2344;&#2379;</mi><mo>&nbsp;</mo><mo>&nbsp;</mo><mi>&#2360;&#2350;&#2368;&#2325;&#2352;&#2339;&#2379;</mi><mo>&nbsp;</mo><mi>&#2325;&#2379;</mi><mo>&nbsp;</mo><mo>&nbsp;</mo><mi>&#2332;&#2379;&#2337;&#2364;&#2344;&#2375;</mi><mo>&nbsp;</mo><mo>&nbsp;</mo><mi>&#2346;&#2352;</mi><mo>,</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mi>&#2361;&#2350;&#2375;&#2306;</mi><mo>&nbsp;</mo><mo>&nbsp;</mo><mi>&#2346;&#2381;&#2352;&#2366;&#2346;&#2381;&#2340;</mi><mo>&nbsp;</mo><mo>&nbsp;</mo><mi>&#2361;&#2379;&#2340;&#2366;</mi><mo>&nbsp;</mo><mo>&nbsp;</mo><mi>&#2361;&#2376;</mi><mspace linebreak=\"newline\"></mspace><mn>2</mn><mi>cosec&theta;</mi><mo>&nbsp;</mo><mo>=</mo><mfrac><mrow><mo>&nbsp;</mo><mn>7</mn></mrow><mn>2</mn></mfrac><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mfrac><mn>2</mn><mn>7</mn></mfrac><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mfrac><mn>53</mn><mn>14</mn></mfrac><mspace linebreak=\"newline\"></mspace><mi>cosec&theta;</mi><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mfrac><mn>53</mn><mn>28</mn></mfrac></math></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "17",
                    question_en: "<p>13<span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Cambria Math;\">If <span style=\"font-weight: 400;\">tan 2&theta; </span><span style=\"font-weight: 400;\">= cot (&theta; </span><span style=\"font-weight: 400;\">- 36&deg;)</span></span><span style=\"font-family: Cambria Math;\">, where 2<span style=\"font-weight: 400;\">&theta; </span></span><span style=\"font-family: Cambria Math;\">is an actual angle, then the value <span style=\"font-weight: 400;\">&theta; </span>of </span><span style=\"font-family: Cambria Math;\"> is: </span></p>\n",
                    question_hi: "<p>13<span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Cambria Math;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">, <span style=\"font-weight: 400;\">tan 2&theta; </span><span style=\"font-weight: 400;\">=cot (&theta; </span><span style=\"font-weight: 400;\">- 36&deg;) </span></span><span style=\"font-family: Cambria Math;\">&#2332;&#2361;&#2366;&#2306;</span><span style=\"font-family: Cambria Math;\"> 2<span style=\"font-weight: 400;\">&theta; </span></span><span style=\"font-family: Cambria Math;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2344;&#2381;&#2351;&#2370;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2379;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> <span style=\"font-weight: 400;\">&theta;&nbsp;</span></span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2332;&#2381;&#2334;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;&#2332;&#2367;&#2319;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                    options_en: ["<p>18&deg;</p>\n", "<p>36&deg;</p>\n", 
                                "<p>30&deg;</p>\n", "<p>42&deg;</p>\n"],
                    options_hi: ["<p>18&deg;</p>\n", "<p>36&deg;</p>\n",
                                "<p>30&deg;</p>\n", "<p>42&deg;</p>\n"],
                    solution_en: "<p>13<span style=\"font-family: Cambria Math;\">.(d)</span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>If</mi><mo>&nbsp;</mo><mo>(</mo><mi mathvariant=\"normal\">A</mi><mo>+</mo><mi mathvariant=\"normal\">B</mi><mo>)</mo><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>90</mn><mo>&deg;</mo><mo>&nbsp;</mo><mo>,</mo><mo>&nbsp;</mo><mi>then</mi><mo>&nbsp;</mo><mi>tan</mi><mo>&nbsp;</mo><mi mathvariant=\"normal\">A</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mi>cot</mi><mo>&nbsp;</mo><mi mathvariant=\"normal\">B</mi><mspace linebreak=\"newline\"></mspace><mi>Here</mi><mo>&nbsp;</mo><mo>,</mo><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mo>(</mo><mn>2</mn><mi mathvariant=\"normal\">&theta;</mi><mo>)</mo><mo>&nbsp;</mo><mo>+</mo><mo>(</mo><mi mathvariant=\"normal\">&theta;</mi><mo>&nbsp;</mo><mo>-</mo><mn>36</mn><mo>&deg;</mo><mo>)</mo><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>90</mn><mo>&deg;</mo><mspace linebreak=\"newline\"></mspace><mn>3</mn><mi mathvariant=\"normal\">&theta;</mi><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>126</mn><mo>&deg;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&rArr;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mi mathvariant=\"normal\">&theta;</mi><mo>=</mo><mo>&nbsp;</mo><mn>42</mn><mo>&deg;</mo></math></p>\n",
                    solution_hi: "<p>13<span style=\"font-family: Cambria Math;\">.(d)</span></p>\r\n<p><math style=\"font-family: Verdana;\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#2351;&#2342;&#2367;</mi><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>(</mo><mi>A</mi><mo>+</mo><mi>B</mi><mo>)</mo><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>90</mn><mo>&deg;</mo><mo>&nbsp;</mo><mi>&#2361;&#2376;</mi><mo>,</mo><mo>&nbsp;</mo><mi>&#2340;&#2379;</mi><mo>&nbsp;</mo><mo>&nbsp;</mo><mi>t</mi><mi>a</mi><mi>n</mi><mo>&nbsp;</mo><mi>A</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mi>c</mi><mi>o</mi><mi>t</mi><mo>&nbsp;</mo><mi>B</mi><mspace linebreak=\"newline\"></mspace><mi>&#2351;&#2361;&#2366;&#2305;</mi><mo>&nbsp;</mo><mo>,</mo><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mo>(</mo><mn>2</mn><mi>&theta;</mi><mo>)</mo><mo>&nbsp;</mo><mo>+</mo><mo>(</mo><mi>&theta;</mi><mo>&nbsp;</mo><mo>-</mo><mn>36</mn><mo>&deg;</mo><mo>)</mo><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>90</mn><mo>&deg;</mo><mspace linebreak=\"newline\"></mspace><mn>3</mn><mi>&theta;</mi><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>126</mn><mo>&deg;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&rArr;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mi>&theta;</mi><mo>=</mo><mo>&nbsp;</mo><mn>42</mn><mo>&deg;</mo></math></p>\r\n<p>&nbsp;</p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "17",
                    question_en: "<p>14<span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Cambria Math;\">If <span style=\"font-weight: 400;\">tan(90 - &theta;</span><span style=\"font-weight: 400;\">)=</span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><msqrt><mn>3</mn></msqrt></mfrac></math> </span><span style=\"font-family: Cambria Math;\">, then the value of <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>2</mn><msqrt><mn>3</mn></msqrt></math><span style=\"font-weight: 400;\"> tan&theta; </span><span style=\"font-weight: 400;\">+ 1</span><span style=\"font-weight: 400;\"> </span></span><span style=\"font-family: Cambria Math;\">&nbsp;is:</span></p>\n",
                    question_hi: "<p>14<span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Cambria Math;\">&#2351;&#2342;&#2367; <span style=\"font-weight: 400;\">tan(90 - &theta;)=<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><msqrt><mn>3</mn></msqrt></mfrac></math></span></span><span style=\"font-family: Cambria Math;\">&nbsp;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>2</mn><msqrt><mn>3</mn></msqrt><mo>&nbsp;</mo><mi>tan&theta;</mi><mo>+</mo><mn>1</mn><mo>&nbsp;</mo></math></span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2332;&#2381;&#2334;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;&#2332;&#2367;&#2319;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                    options_en: ["<p>4</p>\n", "<p>5</p>\n", 
                                "<p>3</p>\n", "<p>6</p>\n"],
                    options_hi: ["<p>4</p>\n", "<p>5</p>\n",
                                "<p>3</p>\n", "<p>6</p>\n"],
                    solution_en: "<p>14<span style=\"font-family: Cambria Math;\">.(a)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>c</mi><mi>o</mi><mi>t</mi><mi mathvariant=\"normal\">&theta;</mi><mo>=</mo><mfrac><mrow><mo>&nbsp;</mo><mn>2</mn></mrow><msqrt><mn>3</mn></msqrt></mfrac><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&rArr;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mi>tan&theta;</mi><mo>=</mo><mfrac><mrow><msqrt><mo>&nbsp;</mo><mn>3</mn></msqrt><mo>&nbsp;</mo></mrow><mn>2</mn></mfrac><mspace linebreak=\"newline\"></mspace><mi>Now</mi><mo>&nbsp;</mo><mo>,</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mn>2</mn><msqrt><mn>3</mn></msqrt><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mi>tan&theta;</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>1</mn><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>2</mn><msqrt><mn>3</mn></msqrt><mo>&nbsp;</mo><mo>&times;</mo><mfrac><msqrt><mn>3</mn></msqrt><mn>2</mn></mfrac><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>1</mn><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mo>=</mo><mo>&nbsp;</mo><mn>3</mn><mo>&nbsp;</mo><mo>+</mo><mn>1</mn><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>4</mn></math></p>\n",
                    solution_hi: "<p>14<span style=\"font-family: Cambria Math;\">.(a)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><math style=\"font-family: Verdana;\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>c</mi><mi>o</mi><mi>t</mi><mi mathvariant=\"normal\">&theta;</mi><mo>=</mo><mfrac><mrow><mo>&nbsp;</mo><mn>2</mn></mrow><msqrt><mn>3</mn></msqrt></mfrac><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&rArr;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mi>tan&theta;</mi><mo>=</mo><mfrac><mrow><msqrt><mo>&nbsp;</mo><mn>3</mn></msqrt><mo>&nbsp;</mo></mrow><mn>2</mn></mfrac><mspace linebreak=\"newline\"></mspace><mi>&#2309;&#2348;</mi><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>,</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mn>2</mn><msqrt><mn>3</mn></msqrt><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mi>tan&theta;</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>1</mn><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>2</mn><msqrt><mn>3</mn></msqrt><mo>&nbsp;</mo><mo>&times;</mo><mfrac><msqrt><mn>3</mn></msqrt><mn>2</mn></mfrac><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>1</mn><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mo>=</mo><mo>&nbsp;</mo><mn>3</mn><mo>&nbsp;</mo><mo>+</mo><mn>1</mn><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>4</mn></math></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "misc",
                    question_en: "<p>15<span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Cambria Math;\">If <span style=\"font-weight: 400;\">sec &theta; </span><span style=\"font-weight: 400;\">+ tan&theta; </span><span style=\"font-weight: 400;\">= 5</span><span style=\"font-weight: 400;\">, then Sin&theta;&nbsp;</span><span style=\"font-weight: 400;\"> =</span></span><span style=\"font-family: Cambria Math;\">&nbsp;________.</span></p>\n",
                    question_hi: "<p>15<span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Cambria Math;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">sec&theta;</span><span style=\"font-weight: 400;\">+tan&theta; </span><span style=\"font-weight: 400;\">= 5</span><span style=\"font-weight: 400;\">, &#2340;&#2379; Sin&theta;&nbsp;</span><span style=\"font-weight: 400;\"> = </span></span><span style=\"font-family: Cambria Math;\">&nbsp;________</span><span style=\"font-family: Cambria Math;\">&#2404;</span></p>\n",
                    options_en: ["<p>0</p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>13</mn><mn>12</mn></mfrac></math></p>\n", 
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>12</mn><mn>13</mn></mfrac></math></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>5</mn></mfrac></math></p>\n"],
                    options_hi: ["<p>0</p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>13</mn><mn>12</mn></mfrac></math></p>\n",
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>12</mn><mn>13</mn></mfrac></math></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>5</mn></mfrac></math></p>\n"],
                    solution_en: "<p>15<span style=\"font-family: Cambria Math;\">.(c)</span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>If</mi><mo>&nbsp;</mo><mi>sec&theta;</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mi>tan&theta;</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>5</mn><mo>&nbsp;</mo><mo>&hellip;</mo><mo>.</mo><mo>.</mo><mi>Eq</mi><mo>.</mo><mn>1</mn><mo>&nbsp;</mo><mo>,</mo><mo>&nbsp;</mo><mi>Then</mi><mspace linebreak=\"newline\"></mspace><mi>sec&theta;</mi><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mi>tan&theta;</mi><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mfrac><mn>1</mn><mn>5</mn></mfrac><mo>&nbsp;</mo><mo>&hellip;</mo><mo>.</mo><mo>.</mo><mi>Eq</mi><mo>.</mo><mn>2</mn><mspace linebreak=\"newline\"></mspace><mi>Adding</mi><mo>&nbsp;</mo><mi>both</mi><mo>&nbsp;</mo><mi>the</mi><mo>&nbsp;</mo><mi>equation</mi><mo>&nbsp;</mo><mo>,</mo><mo>&nbsp;</mo><mi>we</mi><mo>&nbsp;</mo><mi>get</mi><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mn>2</mn><mi>sec&theta;</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>5</mn><mo>+</mo><mfrac><mn>1</mn><mn>5</mn></mfrac><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&rArr;</mo><mo>&nbsp;</mo><mi>sec&theta;</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mfrac><mn>13</mn><mn>5</mn></mfrac><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mfrac><mi>Hypotenuse</mi><mi>Base</mi></mfrac><mspace linebreak=\"newline\"></mspace><mi>Perpendicular</mi><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>12</mn><mspace linebreak=\"newline\"></mspace><mi>sin&theta;</mi><mo>&nbsp;</mo><mo>=</mo><mfrac><mi>Perpendicular</mi><mi>Hypotenuse</mi></mfrac><mo>=</mo><mfrac><mn>12</mn><mn>13</mn></mfrac></math></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                    solution_hi: "<p>15<span style=\"font-family: Cambria Math;\">.(c)</span></p>\r\n<p><math style=\"font-family:Verdana\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#2351;&#2342;&#2367;</mi><mo>&nbsp;</mo><mo>&nbsp;</mo><mi>sec&theta;</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mi>tan</mi><mo>&nbsp;</mo><mi mathvariant=\"normal\">&theta;</mi><mo>=</mo><mo>&nbsp;</mo><mn>5</mn><mo>&nbsp;</mo><mo>&hellip;</mo><mo>.</mo><mo>.</mo><mi>&#2360;&#2350;&#2368;</mi><mo>.</mo><mn>1</mn><mo>&nbsp;</mo><mo>&nbsp;</mo><mi>&#2361;&#2376;</mi><mo>,</mo><mo>&nbsp;</mo><mi>&#2340;&#2379;</mi><mspace linebreak=\"newline\"></mspace><mi>sec&theta;</mi><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mi>tan&theta;</mi><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mfrac><mn>1</mn><mn>5</mn></mfrac><mo>&nbsp;</mo><mo>&hellip;</mo><mo>.</mo><mo>.</mo><mi>&#2360;&#2350;&#2368;</mi><mo>.</mo><mn>2</mn><mspace linebreak=\"newline\"></mspace><mi>&#2342;&#2379;&#2344;&#2379;</mi><mo>&nbsp;</mo><mi>&#2360;&#2350;&#2368;&#2325;&#2352;&#2339;&#2379;&#2306;</mi><mo>&nbsp;</mo><mi>&#2325;&#2379;</mi><mo>&nbsp;</mo><mi>&#2332;&#2379;&#2337;&#2364;&#2344;&#2375;</mi><mo>&nbsp;</mo><mi>&#2346;&#2352;</mi><mo>,</mo><mo>&nbsp;</mo><mi>&#2361;&#2350;&#2375;&#2306;</mi><mo>&nbsp;</mo><mi>&#2346;&#2381;&#2352;&#2366;&#2346;&#2381;&#2340;</mi><mo>&nbsp;</mo><mi>&#2361;&#2379;&#2340;&#2366;</mi><mo>&nbsp;</mo><mi>&#2361;&#2376;</mi><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mn>2</mn><mi>sec&theta;</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>5</mn><mo>+</mo><mfrac><mn>1</mn><mn>5</mn></mfrac><mo>&nbsp;</mo><mo>&rArr;</mo><mo>&nbsp;</mo><mi>sec&theta;</mi><mo>&nbsp;</mo><mo>=</mo><mfrac><mrow><mo>&nbsp;</mo><mn>13</mn></mrow><mn>5</mn></mfrac><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mfrac><mrow><mi>&#2325;&#2352;&#2381;&#2339;</mi><mo>&nbsp;</mo></mrow><mi>&#2310;&#2343;&#2366;&#2352;</mi></mfrac><mspace linebreak=\"newline\"></mspace><mi>&#2354;&#2350;&#2381;&#2348;</mi><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>12</mn><mspace linebreak=\"newline\"></mspace><mi>sin</mi><mi>&theta;</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mfrac><mi>&#2354;&#2350;&#2381;&#2348;</mi><mrow><mi>&#2325;&#2352;&#2381;&#2339;</mi><mo>&nbsp;</mo></mrow></mfrac><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mfrac><mn>12</mn><mn>13</mn></mfrac></math></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>