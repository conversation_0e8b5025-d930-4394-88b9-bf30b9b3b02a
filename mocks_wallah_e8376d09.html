<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">10:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 10</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">10</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 10 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["31"] = {
                name: "Computer Knowledge",
                start: 0,
                end: 8
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="31">Computer Knowledge</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 9,
                end: 9
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "31",
                    question_en: "<p>1. In MS Excel 365, which of the following is the correct sequence for deleting a column in MS-Excel spreadsheet?</p>",
                    question_hi: "<p>1. एमएस एक्सेल 365 (MS Excel 365) में स्प्रेडशीट में किसी कॉलम को डिलीट करने के लिए निम्नलिखित में से कौन-सा क्रम सही है?</p>",
                    options_en: ["<p>Select the cell&lt;&lt;Right click&lt;&lt;Delete&lt;&lt;Entire Row<br><right click<<delete<<entire=\"\" row<=\"\" body=\"\"></right></p>", "<p>Select the cell&lt;&lt;Left click&lt;&lt;Delete&lt;&lt;Entire Row<br><left click<<delete<<entire=\"\" row<=\"\" body=\"\"></left></p>", 
                                "<p>Select the cell&lt;&lt;Right click&lt;&lt;Delete&lt;&lt;Entire Column<right click<<delete<<entire=\"\" column<=\"\" body=\"\"></right></p>", "<p>Select the cell&lt;&lt;Left click&lt;&lt;Delete&lt;&lt;Entire Column<left click<<delete<<entire=\"\" column<=\"\" body=\"\"></left></p>"],
                    options_hi: ["<p>सेल को सेलेक्ट करें &lt;&lt; राईट क्लिक करें &lt;&lt; डिलीट &lt;&lt; एंटायर रो</p>", "<p>सेल को सेलेक्ट करें &lt;&lt; लेफ्ट क्लिक करें &lt;&lt; डिलीट &lt;&lt; एंटायर रो</p>",
                                "<p>सेल को सेलेक्ट करें &lt;&lt; राईट क्लिक करें &lt;&lt; डिलीट &lt;&lt; एंटायर कॉलम</p>", "<p>सेल को सेलेक्ट करें &lt;&lt; लेफ्ट क्लिक करें &lt;&lt; डिलीट &lt;&lt; एंटायर कॉलम</p>"],
                    solution_en: "<p>1.(c) In MS Excel 365, the correct sequence for deleting a Column in MS-Excel spreadsheet - Select the cell&lt;&lt;Right click&lt;&lt;Delete&lt;&lt;Entire Column</p>",
                    solution_hi: "<p>1.(c) एमएस एक्सेल 365 (MS Excel 365) में, एमएस-एक्सेल स्प्रेडशीट (MS-Excel spreadsheet) में एक कॉलम (Column) को हटाने का सही क्रम - सेल को सेलेक्ट करें &lt;&lt; राईट क्लिक करें &lt;&lt; डिलीट &lt;&lt; एंटायर कॉलम (Entire Column)</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "2",
                    section: "31",
                    question_en: "<p>2. Which of the following is a shortcut key for Save as option in MS Word 365?</p>",
                    question_hi: "<p>2. एमएस वर्ड 365 (MS Word 365) में सेव एज़ (Save as) ऑप्शन के लिए निम्नलिखित में से कौन-सी शॉर्टकट कुंजी (key) है?</p>",
                    options_en: ["<p>F1</p>", "<p>F11</p>", 
                                "<p>F12</p>", "<p>F2</p>"],
                    options_hi: ["<p>F1</p>", "<p>F11</p>",
                                "<p>F12</p>", "<p>F2</p>"],
                    solution_en: "<p>2.(c) <strong>F12.</strong> While F12 is the keyboard shortcut for \"Save As,\" the more common way to save our work quickly is by using Ctrl + S. The F1 key is used to open the help center. F2 key is used to rename a folder or file. F11 key is used for full screen.</p>",
                    solution_hi: "<p>2.(c) <strong>F12.</strong> जबकि F12 \"Save As\" के लिए कीबोर्ड शॉर्टकट है, हमारे काम को जल्दी से सहेजने का अधिक सामान्य तरीका Ctrl + S का उपयोग करना है। हेल्प सेंटर (Help center) खोलने के लिए F1 कुंजी का उपयोग किया जाता है। F2 कुंजी का उपयोग किसी फ़ोल्डर या फ़ाइल का नाम बदलने के लिए किया जाता है। फुल स्क्रीन (full screen) के लिए F11 कुंजी का उपयोग किया जाता है।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "3",
                    section: "31",
                    question_en: "<p>3. What is the purpose of the &lsquo;Font Color&rsquo; option in Microsoft Word 365?</p>",
                    question_hi: "<p>3. माइक्रोसॉफ्ट वर्ड 365 में \'फॉन्ट कलर\' ऑप्शन का उद्देश्य क्या होता है?</p>",
                    options_en: ["<p>Changing the font style</p>", "<p>Changing the font size</p>", 
                                "<p>Applying a background colour to text</p>", "<p>Changing the colour of the text</p>"],
                    options_hi: ["<p>फ़ॉन्ट स्टाइल बदलना</p>", "<p>फ़ॉन्ट का साइज़ बदलना</p>",
                                "<p>टेक्स्ट पर बैकग्राउंड का कलर अप्लाई करना</p>", "<p>टेक्स्ट का कलर बदलना</p>"],
                    solution_en: "<p>3.(d) <strong>Changing the colour of the text.</strong> Other Font Group (Home tab) options in Word 365 - Font Name (for change the font style), Font Size (For change the font size), Highlight (change the background colour of text), Italic, Bold, Underline, etc.</p>",
                    solution_hi: "<p>3.(d) <strong>टेक्स्ट का कलर बदलना ।</strong> Word 365 में अन्य फ़ॉन्ट ग्रुप (होम टैब) विकल्प - फ़ॉन्ट नेम (Font Name) (फ़ॉन्ट स्टाइल बदलने के लिए), फ़ॉन्ट साइज़ (Font Size) (फ़ॉन्ट का आकार बदलने के लिए), हाइलाइट (Highlight) (टेक्स्ट का बैकग्राउंड रंग बदलने के लिए), इटैलिक, बोल्ड, अंडरलाइन, आदि।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "4",
                    section: "31",
                    question_en: "4.  Which menu tab on Microsoft Word\'s 365 ribbon contains options for text formatting and editing?",
                    question_hi: "<p>4. माइक्रोसॉफ्ट वर्ड 365 के रिबन पर किस मेनू टैब में टेक्स्ट फ़ॉर्मेटिंग और एडिट के विकल्प होते हैं?</p>",
                    options_en: [" File", " Home", 
                                " View", "<p>Insert</p>"],
                    options_hi: ["<p>फ़ाइल (File)</p>", "<p>होम (Home)</p>",
                                "<p>व्यू (View)</p>", "<p>इंसर्ट (Insert)</p>"],
                    solution_en: "<p>4.(b) <strong>Home.</strong> In MS word 365, Options of Home tab menu - Clipboard, Font, Paragraph, Styles, Editing, Voice, Editor, Designer.</p>",
                    solution_hi: "<p>4.(b) <strong>Home.</strong> MS वर्ड 365 में होम टैब मेनू के विकल्प (Options) - क्लिपबोर्ड, फॉन्ट, पैराग्राफ, स्टाइल, एडिटिंग (Editing), वॉयस (Voice), एडिटर (Editor), डिजाइनर।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "5",
                    section: "31",
                    question_en: "<p>5. In MS Excel 365, which of the following is NOT a method to open a spreadsheet in MS- Excel?</p>",
                    question_hi: "<p>5. एमएस एक्सेल 365 (MS Excel 365) में, निम्नलिखित में से कौन-सी एमएस-एक्सेल (MS-Excel) में स्प्रेडशीट खोलने की विधि नहीं है?</p>",
                    options_en: ["<p>Shift + Ctrl + N</p>", "<p>Alt + F + O</p>", 
                                "<p>Ctrl + N</p>", "<p>File -&gt; open</p>"],
                    options_hi: ["<p>Shift + Ctrl + N</p>", "<p>Alt + F + O</p>",
                                "<p>Ctrl + N</p>", "<p>File -&gt; open</p>"],
                    solution_en: "<p>5.(a) <strong>Shift + Ctrl + N.</strong> A spreadsheet is a computer program that can capture, display and manipulate data arranged in rows and columns. Ctrl + N is used to open a new window, file, document, etc.</p>",
                    solution_hi: "<p>5.(a) <strong>Shift + Ctrl + N.</strong> स्प्रेडशीट (spreadsheet) एक कंप्यूटर प्रोग्राम है जो पंक्तियों (rows) और स्तंभों (columns) में व्यवस्थित डेटा को कैप्चर, प्रदर्शित और हेरफेर (manipulate) कर सकता है। Ctrl + N का उपयोग नई विंडो, फ़ाइल, डॉक्यूमेंट (document) आदि खोलने के लिए किया जाता है।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "6",
                    section: "31",
                    question_en: "<p>6. Which of the following is a common element in an MS Word 365 document\'s header or footer?</p>",
                    question_hi: "<p>6. एमएस वर्ड 365 (MS Word 365) डॉक्यूमेंट के हेडर (header) या फुटर (footer) में निम्नलिखित में से कौन-सा विकल्प एक कॉमन एलिमेंट होता है?</p>",
                    options_en: ["<p>Document title</p>", "<p>Bullet points</p>", 
                                "<p>Page number</p>", "<p>Font colour</p>"],
                    options_hi: ["<p>डॉक्यूमेंट टाइटल (Document title)</p>", "<p>बुलेट पॉइंट्स (Bullet points)</p>",
                                "<p>पेज नंबर (Page number)</p>", "<p>फॉन्ट कलर (Font colour)</p>"],
                    solution_en: "<p>6.(c) <strong>Page number. </strong>Headers and footers are typically used to display certain information on every page of a document, such as the page number, document title, or author name. Bullet points and font colour are related to the body content of the document.</p>",
                    solution_hi: "<p>6.(c) <strong>पेज नंबर</strong> <strong>(Page number)।</strong> हेडर और फुटर का उपयोग आम तौर पर किसी डॉक्यूमेंट के प्रत्येक पेज पर कुछ जानकारी प्रदर्शित करने के लिए किया जाता है, जैसे पेज नंबर, डॉक्यूमेंट टाइटल, या ऑथर नेम । बुलेट पॉइंट (Bullet points) और फ़ॉन्ट कलर (font colour) डॉक्यूमेंट की मुख्य सामग्री (content) से संबंधित हैं।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "7",
                    section: "31",
                    question_en: "<p>7. What information is typically required to sign in to an email client?</p>",
                    question_hi: "<p>7. किसी ईमेल क्लाइंट (email client) में साइन इन करने के लिए सामान्यतः किस जानकारी की आवश्यकता होती है?</p>",
                    options_en: ["<p>Full name and date of birth</p>", "<p>Email address and password</p>", 
                                "<p>Social media account details</p>", "<p>Phone number and address</p>"],
                    options_hi: ["<p>पूरा नाम और जन्मतिथि</p>", "<p>ईमेल पता (Email address) और पासवर्ड</p>",
                                "<p>सोशल मीडिया अकाउंट विवरण</p>", "<p>फ़ोन नंबर (Phone number) और पता (address)</p>"],
                    solution_en: "<p>7.(b) The email address serves as the unique identifier for the user, and the password provides secure access to the account. Full name, date of birth, Social media account details, phone number like other details can be required for creating a new email account or for account recovery purposes.</p>",
                    solution_hi: "<p>7.(b) ईमेल एड्रेस (email address) उपयोगकर्ता के लिए विशिष्ट पहचानकर्ता (identifier) के रूप में कार्य करता है, और पासवर्ड खाते तक सुरक्षित पहुंच प्रदान करता है। न्यू ईमेल अकाउंट (new email account) बनाने या खाता पुनर्प्राप्ति उद्देश्यों के लिए पूरा नाम, जन्मतिथि, सोशल मीडिया अकाउंट विवरण, फ़ोन नंबर जैसे अन्य विवरण कीआवश्यकता हो सकती है।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "8",
                    section: "31",
                    question_en: "<p>8. In MS Excel 365 which of the following statements related to deleting rows in an excel sheet is correct?</p>",
                    question_hi: "<p>8. एमएस एक्सेल 365 (MS Excel 365) में, एक्सेल शीट में पंक्तियों (rows) को डिलीट करने से संबंधित निम्नलिखित में से कौन-सा कथन सही है?</p>",
                    options_en: ["<p>If you want to delete multiple rows at once, simply select the rows by clicking and dragging over the row numbers, and then press &lsquo;Ctrl&rsquo; and &lsquo;space&rsquo;.</p>", "<p>If you want to delete multiple rows at once, simply select the rows by clicking and dragging over the row numbers, and then press &lsquo;Ctrl&rsquo; and &lsquo;&minus;&lsquo;.</p>", 
                                "<p>If you want to delete multiple rows at once, simply select the rows by clicking and dragging <br>over the row numbers, and then press &lsquo;Ctrl&rsquo; and &lsquo;+&rsquo;.</p>", "<p>If you want to delete multiple rows at once, simply select the rows by clicking and dragging over the row numbers, and then press &lsquo;Ctrl&rsquo; and &lsquo;Enter&rsquo;.</p>"],
                    options_hi: ["<p>यदि आप एक साथ कई रो (rows) को डिलीट करना चाहते हैं, तो सीधे रो नंबर पर क्लिक और ड्रैग करके रो (rows) को सेलेक्ट करें, तथा फिर \'Ctrl\' और \'space\' दबाएँ।</p>", "<p>यदि आप एक साथ कई रो (rows) को डिलीट करना चाहते हैं, तो सीधे से नंबर पर क्लिक और ड्रेग करके रो (rows) को सेलेक्ट करें, तथा फिर \'Ctrl\' और &lsquo;-&rsquo;दबाएँ।</p>",
                                "<p>यदि आप एक साथ कई रो (rows) को डिलीट करना चाहते हैं, तो सीधे रो नंबर पर क्लिक और ड्रैग करके रो (rows) को सेलेक्ट करें, तथा फिर \'Ctrl\' और \'+\' दबाएँ।</p>", "<p>यदि आप एक साथ कई रो (rows) को डिलीट करना चाहते हैं, तो सीधे रो नंबर पर क्लिक और ड्रैग करके रो (rows) को सेलेक्ट करें, तथा फिर \'Ctrl\' और \'Enter\' दबाएँ।</p>"],
                    solution_en: "<p>8.(b) <strong>MS Excel 365</strong> Shortcut keys: Cut selected cell - Delete key, Cut- Ctrl + X or Shift + Delete, Delete cells, rows or columns - Ctrl + (-) or Ctrl + NUMPAD MINUS.</p>",
                    solution_hi: "<p>8.(b) <strong>MS Excel 365</strong> शॉर्टकट कुंजी: चयनित सेल को काटें - Delete कुंजी, कट करें- Ctrl + X या Shift + Delete, सेल, पंक्तियां या कॉलम हटाएं - Ctrl + (-) या Ctrl + NUMPAD माइनस।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "9",
                    section: "31",
                    question_en: "<p>9. Which of the following shortcut keys is used to copy formatting in MS Word 365?</p>",
                    question_hi: "<p>9. एमएस वर्ड 365 (MS Word 365) में फॉर्मेटिंग को कॉपी करने के लिए निम्नलिखित में से किस शॉर्टकट कुंजी (keys) का उपयोग किया जाता है ?</p>",
                    options_en: ["<p>Ctrl + Shift + A</p>", "<p>Ctrl + Shift + D</p>", 
                                "<p>Ctrl + Shift + C</p>", "<p>Ctrl + Shift + V</p>"],
                    options_hi: ["<p>Ctrl + Shift + A</p>", "<p>Ctrl + Shift + D</p>",
                                "<p>Ctrl + Shift + C</p>", "<p>Ctrl + Shift + V</p>"],
                    solution_en: "<p>9.(c) <strong>Ctrl + Shift + C.</strong> Ctrl + Shift + D - Apply double-underline formatting. Ctrl + Shift + V - Paste Text Without Formatting.</p>",
                    solution_hi: "<p>9.(c) <strong>Ctrl + Shift + C.</strong> Ctrl + Shift + D - डबल-अंडरलाइन फ़ॉर्मेटिंग (double-underline formatting) का प्रयोग करने के लिए । Ctrl + Shift + V - बिना फ़ॉर्मेटिंग के टेक्स्ट पेस्ट करें।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "10",
                    section: "misc",
                    question_en: "10. Which of the following services allows users to buy and sell products online?",
                    question_hi: "10. निम्नलिखित में से कौन-सी सेवाएं, यूज़र्स को ऑनलाइन उत्पाद खरीदने और बेचने की सुविधा देती हैं?",
                    options_en: [" Instant messaging", " E-commerce", 
                                " Video conferencing", " Streaming"],
                    options_hi: ["<p>इंस्टेंट मैसेजिंग (Instant messaging)</p>", "<p>ई-कॉमर्स (E-commerce)</p>",
                                "<p>वीडियो कॉन्फ्रेंसिंग (Video conferencing)</p>", "<p>स्ट्रीमिंग (Streaming)</p>"],
                    solution_en: "<p>10.(b) <strong>E-commerce.</strong> Instant messaging is the exchange of near-real-time messages through a standalone application or embedded software. Video conferencing is a technology that enables us to conduct a meeting online by using some platforms which offer such facilities. Live streaming is the broadcast of an event over the internet as it happens.</p>",
                    solution_hi: "<p>10.(b) <strong>E-commerce.</strong> इंस्टेंट मैसेजिंग (Instant messaging) एक स्टैंडअलोन एप्लिकेशन (standalone application) या एम्बेडेड सॉफ़्टवेयर (embedded software) के माध्यम से लगभग वास्तविक समय संदेशों का आदान-प्रदान है। वीडियो कॉन्फ्रेंसिंग (Video conferencing) एक ऐसी तकनीक है जो हमें ऐसी सुविधाएं प्रदान करने वाले कुछ प्लेटफार्मों का उपयोग करके ऑनलाइन मीटिंग आयोजित करने में सक्षम बनाती है। लाइव स्ट्रीमिंग (Live streaming) किसी घटना का इंटरनेट पर प्रसारित होना।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>