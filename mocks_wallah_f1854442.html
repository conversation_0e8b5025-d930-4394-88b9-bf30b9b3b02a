<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">20:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 25</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">25</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 20 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 0,
                end: 23
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 24,
                end: 24
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "17",
                    question_en: "<p>1. If<span style=\"font-family: Times New Roman;\"> &nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#160;</mi><mi>&#160;</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#952;</mi><mi>&#160;</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>&#160;</mi><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#952;</mi><mi>&#160;</mi><mi>&#160;</mi></mrow><mrow><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#952;</mi><mo>-</mo><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#952;</mi></mrow></mfrac><mo>=</mo><mn>8</mn><mo>,</mo></math></span><span style=\"font-family: Times New Roman;\">then the value of cot<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#952;</mi></math> </span><span style=\"font-family: Times New Roman;\"> is equal to :</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">SSC CHSL 12-10-2020 (morning shift)</span></p>",
                    question_hi: "<p>1. <span style=\"font-family: Baloo;\">यदि</span><span style=\"font-family: Times New Roman;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#160;</mi><mi>&#160;</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#952;</mi><mi>&#160;</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>&#160;</mi><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#952;</mi><mi>&#160;</mi><mi>&#160;</mi></mrow><mrow><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#952;</mi><mo>-</mo><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#952;</mi></mrow></mfrac><mo>=</mo><mn>8</mn><mo>,</mo></math></span><span style=\"font-family: Baloo;\"> है, तो</span><span style=\"font-family: Times New Roman;\"> cot<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#952;</mi></math> </span><span style=\"font-family: Baloo;\"> का मान किसके बराबर होगा ? </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">CHSL 12-10-2020 (morning shift)</span></p>",
                    options_en: ["<p><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>6</mn><mn>5</mn></mfrac></math> </span></p>", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>9</mn><mn>7</mn></mfrac></math></p>", 
                                "<p><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>6</mn></mfrac></math> </span></p>", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>8</mn><mn>7</mn></mfrac></math></p>"],
                    options_hi: ["<p><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>6</mn><mn>5</mn></mfrac></math> </span></p>", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>9</mn><mn>7</mn></mfrac></math></p>",
                                "<p><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>6</mn></mfrac></math> </span></p>", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>8</mn><mn>7</mn></mfrac></math></p>"],
                    solution_en: "<p>1.(b)</p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#160;</mi><mi>&#160;</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#952;</mi><mi>&#160;</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>&#160;</mi><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#952;</mi><mi>&#160;</mi><mi>&#160;</mi></mrow><mrow><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#952;</mi><mo>-</mo><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#952;</mi></mrow></mfrac><mo>&#160;</mo><mo>=</mo><mn>8</mn><mspace linebreak=\"newline\"/><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#952;</mi><mi>&#160;</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>&#160;</mi><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#952;</mi><mo>&#160;</mo><mo>=</mo><mn>8</mn><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#952;</mi><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mi>&#160;</mi><mn>8</mn><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#952;</mi><mspace linebreak=\"newline\"/><mn>7</mn><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#952;</mi><mo>=</mo><mi>&#160;</mi><mn>9</mn><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#952;</mi><mspace linebreak=\"newline\"/><mo>&#160;</mo><mo>&#160;</mo><mi>c</mi><mi>o</mi><mi>t</mi><mo>&#160;</mo><mi>&#952;</mi><mo>=</mo><mo>&#160;</mo><mfrac><mn>9</mn><mn>7</mn></mfrac><mspace linebreak=\"newline\"/><mspace linebreak=\"newline\"/><mspace linebreak=\"newline\"/><mspace linebreak=\"newline\"/></math></p>",
                    solution_hi: "<p>1.(b)</p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#160;</mi><mi>&#160;</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#952;</mi><mi>&#160;</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>&#160;</mi><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#952;</mi><mi>&#160;</mi><mi>&#160;</mi></mrow><mrow><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#952;</mi><mo>-</mo><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#952;</mi></mrow></mfrac><mo>&#160;</mo><mo>=</mo><mn>8</mn><mspace linebreak=\"newline\"/><mi>cos</mi><mi>&#952;</mi><mo>&#160;</mo><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mo>&#160;</mo><mi>sin</mi><mi>&#952;</mi><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>8</mn><mi>cos</mi><mi>&#952;</mi><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mo>&#160;</mo><mn>8</mn><mi>sin</mi><mi>&#952;</mi><mspace linebreak=\"newline\"/><mo>&#160;</mo><mn>7</mn><mi>cos</mi><mi>&#952;</mi><mo>=</mo><mo>&#160;</mo><mo>&#160;</mo><mn>9</mn><mi>sin</mi><mi>&#952;</mi><mspace linebreak=\"newline\"/><mo>&#160;</mo><mo>&#160;</mo><mo>&#160;</mo><mi>c</mi><mi>o</mi><mi>t</mi><mo>&#160;</mo><mi>&#952;</mi><mo>=</mo><mo>&#160;</mo><mfrac><mn>9</mn><mn>7</mn></mfrac><mspace linebreak=\"newline\"/><mspace linebreak=\"newline\"/></math></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "17",
                    question_en: "<p>2. If cot A = k, then sin A is equal to : (presume that A is an acute angle)</p>\r\n<p><span style=\"font-family: Times New Roman;\">SSC CHSL 12-10-2020 (morning shift)</span></p>",
                    question_hi: "<p>2. <span style=\"font-family: Baloo;\">यदि</span><span style=\"font-family: Times New Roman;\"> cot A = k</span><span style=\"font-family: Baloo;\"> है, तो</span><span style=\"font-family: Times New Roman;\"> sin A</span><span style=\"font-family: Baloo;\"> का मान किसके बराबर होगा ?</span></p>\r\n<p>(मान लीजिए कि A एक न्यून कोण है)</p>\r\n<p><span style=\"font-family: Times New Roman;\">CHSL 12-10-2020 (morning shift)</span></p>",
                    options_en: ["<p><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msup><mi>k</mi><mn>2</mn></msup><msqrt><mn>1</mn><mo>+</mo><msup><mi>k</mi><mn>2</mn></msup></msqrt></mfrac></math> </span></p>", "<p><span style=\"font-family: Times New Roman;\"> -<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi>k</mi></mfrac></math></span></p>", 
                                "<p><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msqrt><mn>1</mn><mo>+</mo><msup><mi>k</mi><mn>2</mn></msup></msqrt></mfrac></math> </span></p>", "<p><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi>k</mi></mfrac></math> </span></p>"],
                    options_hi: ["<p><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msup><mi>k</mi><mn>2</mn></msup><msqrt><mn>1</mn><mo>+</mo><msup><mi>k</mi><mn>2</mn></msup></msqrt></mfrac></math> </span></p>", "<p><span style=\"font-family: Times New Roman;\"> -<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi>k</mi></mfrac></math></span></p>",
                                "<p><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msqrt><mn>1</mn><mo>+</mo><msup><mi>k</mi><mn>2</mn></msup></msqrt></mfrac></math> </span></p>", "<p><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi>k</mi></mfrac></math> </span></p>"],
                    solution_en: "<p>2.(c)</p>\r\n<p><span style=\"font-family: Times New Roman;\">cot A = k =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>B</mi><mi>P</mi></mfrac></math> </span><span style=\"font-family: Times New Roman;\">, H =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mrow><mn>1</mn><mo>+</mo><msup><mi>k</mi><mn>2</mn></msup></mrow><mrow/></msup></msqrt></math> </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">sin A =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msqrt><mn>1</mn><mo>+</mo><msup><mi>k</mi><mn>2</mn></msup></msqrt></mfrac></math> </span></p>",
                    solution_hi: "<p>2.(c)</p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>c</mi><mi>o</mi><mi>t</mi><mi>A</mi><mo>=</mo><mi>k</mi><mo>=</mo><mfrac><mi>B</mi><mi>P</mi></mfrac><mo>,</mo><mi>H</mi><mo>=</mo><mo>&#8730;</mo><mn>1</mn><mo>+</mo><msup><mi>k</mi><mn>2</mn></msup><mspace linebreak=\"newline\"/><mi>sin</mi><mi>A</mi><mo>&#160;</mo><mo>=</mo><mfrac><mn>1</mn><msqrt><mn>1</mn><mo>+</mo><msup><mi>k</mi><mn>2</mn></msup></msqrt></mfrac><mspace linebreak=\"newline\"/></math></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "17",
                    question_en: "<p>3. The least value of <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mn>8</mn><mi>c</mi><mi>o</mi><mi>s</mi><mi>e</mi><mi>c</mi></mrow><mn>2</mn></msup><mi>&#952;</mi><mo>+</mo><mn>25</mn><msup><mrow><mi>s</mi><mi>i</mi><mi>n</mi></mrow><mn>2</mn></msup><mi>&#952;</mi></math><span style=\"font-family: Times New Roman;\"> is:</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">SSC CHSL 12-10-2020 (morning)</span></p>",
                    question_hi: "<p>3. The least value of<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mn>8</mn><mi>c</mi><mi>o</mi><mi>s</mi><mi>e</mi><mi>c</mi></mrow><mn>2</mn></msup><mi>&#952;</mi><mo>+</mo><mn>25</mn><msup><mrow><mi>s</mi><mi>i</mi><mi>n</mi></mrow><mn>2</mn></msup><mi>&#952;</mi></math>&nbsp;<span style=\"font-family: Times New Roman;\"> is:</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">SSC CHSL 12-10-2020 (morning)</span></p>",
                    options_en: ["<p>20<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>2</mn></msqrt></math><span style=\"font-family: Times New Roman;\"> </span></p>", "<p>10<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>2</mn></msqrt></math><span style=\"font-family: Times New Roman;\"> </span></p>", 
                                "<p>40<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>2</mn></msqrt></math><span style=\"font-family: Times New Roman;\"> </span></p>", "<p>30<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>2</mn></msqrt></math></p>"],
                    options_hi: ["<p>20<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>2</mn></msqrt></math><span style=\"font-family: Times New Roman;\"> </span></p>", "<p>10<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>2</mn></msqrt></math><span style=\"font-family: Times New Roman;\"> </span></p>",
                                "<p>40<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>2</mn></msqrt></math><span style=\"font-family: Times New Roman;\"> </span></p>", "<p>30<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>2</mn></msqrt></math></p>"],
                    solution_en: "<p>3.(a)</p>\r\n<p><span style=\"font-family: Times New Roman;\">Minimum value of </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">8 <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mi>c</mi><mi>o</mi><mi>s</mi><mi>e</mi><mi>c</mi></mrow><mn>2</mn></msup><mi>&#952;</mi><mo>+</mo><mn>25</mn><msup><mrow><mi>s</mi><mi>i</mi><mi>n</mi></mrow><mn>2</mn></msup><mi>&#952;</mi></math></span><span style=\"font-family: Times New Roman;\">= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>2</mn><msqrt><mn>25</mn><mo>&#215;</mo><mn>8</mn></msqrt></math></span><span style=\"font-family: Times New Roman;\">=<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>20</mn><msqrt><mn>2</mn></msqrt></math></span></p>",
                    solution_hi: "<p>3.(a)</p>\r\n<p><span style=\"font-family: Times New Roman;\">Minimum value of </span></p>\r\n<p><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>8</mn><msup><mrow><mi>c</mi><mi>o</mi><mi>s</mi><mi>e</mi><mi>c</mi></mrow><mn>2</mn></msup><mi>&#952;</mi><mo>+</mo><mn>25</mn><msup><mrow><mi>s</mi><mi>i</mi><mi>n</mi></mrow><mn>2</mn></msup><mi>&#952;</mi><mo>&#160;</mo><mo>=</mo><mn>22</mn><msqrt><mn>25</mn><mo>&#215;</mo><mn>8</mn></msqrt><mo>&#160;</mo><mo>=</mo><mn>20</mn><msqrt><mn>2</mn></msqrt></math></span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "17",
                    question_en: "<p>4. Find the value&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>t</mi><mi>a</mi><mi>n</mi><mi>&#160;</mi><mn>60</mn><mo>&#176;</mo><mo>-</mo><mi>t</mi><mi>a</mi><mi>n</mi><mi>&#160;</mi><mn>15</mn><mo>&#176;</mo></mrow><mrow><mn>1</mn><mo>+</mo><mi>t</mi><mi>a</mi><mi>n</mi><mi>&#160;</mi><mn>60</mn><mo>&#176;</mo><mi>t</mi><mi>a</mi><mi>n</mi><mi>&#160;</mi><mn>15</mn><mo>&#176;</mo></mrow></mfrac></math></p>\r\n<p><span style=\"font-family: Times New Roman;\">SSC CHSL 12-10-2020 (Afternoon shift)</span></p>",
                    question_hi: "<p>4. <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#160;</mo><mfrac><mrow><mi>t</mi><mi>a</mi><mi>n</mi><mi>&#160;</mi><mn>60</mn><mo>&#176;</mo><mo>-</mo><mi>t</mi><mi>a</mi><mi>n</mi><mi>&#160;</mi><mn>15</mn><mo>&#176;</mo></mrow><mrow><mn>1</mn><mo>+</mo><mi>t</mi><mi>a</mi><mi>n</mi><mi>&#160;</mi><mn>60</mn><mo>&#176;</mo><mi>t</mi><mi>a</mi><mi>n</mi><mi>&#160;</mi><mn>15</mn><mo>&#176;</mo></mrow></mfrac><mo>&#160;</mo></math><span style=\"font-family: Baloo;\">मान ज्ञात कीजिए</span><span style=\"font-family: Times New Roman;\"> </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">CHSL 12-10-2020 (Afternoon shift)</span></p>",
                    options_en: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msqrt><mn>3</mn></msqrt><mn>2</mn></mfrac></math><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Times New Roman;\"> </span></p>", "<p>1</p>", 
                                "<p><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math> </span></p>", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msqrt><mn>2</mn></msqrt></mfrac></math></p>"],
                    options_hi: ["<p><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msqrt><mn>3</mn></msqrt><mn>2</mn></mfrac></math> </span><span style=\"font-family: Times New Roman;\"> </span></p>", "<p>1</p>",
                                "<p><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math> </span></p>", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msqrt><mn>2</mn></msqrt></mfrac></math></p>"],
                    solution_en: "<p>4.(b)</p>\r\n<p><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>t</mi><mi>a</mi><mi>n</mi><mi>&#160;</mi><mo>(</mo><mi>A</mi><mo>-</mo><mi>B</mi><mo>)</mo><mi>&#160;</mi><mo>&#160;</mo><mo>=</mo><mfrac><mrow><mi>t</mi><mi>a</mi><mi>n</mi><mi>&#160;</mi><mi>A</mi><mi>&#160;</mi><mo>-</mo><mi>t</mi><mi>a</mi><mi>n</mi><mi>&#160;</mi><mi>B</mi><mi>&#160;</mi></mrow><mrow><mn>1</mn><mo>+</mo><mi>t</mi><mi>a</mi><mi>n</mi><mi>&#160;</mi><mi>A</mi><mi>&#160;</mi><mo>.</mo><mi>t</mi><mi>a</mi><mi>n</mi><mi>&#160;</mi><mi>B</mi><mi>&#160;</mi></mrow></mfrac><mspace linebreak=\"newline\"/><mfrac><mrow><mi>t</mi><mi>a</mi><mi>n</mi><mi>&#160;</mi><mn>60</mn><mo>&#176;</mo><mo>-</mo><mi>t</mi><mi>a</mi><mi>n</mi><mi>&#160;</mi><mn>15</mn><mo>&#176;</mo></mrow><mrow><mn>1</mn><mo>+</mo><mi>t</mi><mi>a</mi><mi>n</mi><mi>&#160;</mi><mn>60</mn><mo>&#176;</mo><mi>t</mi><mi>a</mi><mi>n</mi><mi>&#160;</mi><mn>15</mn><mo>&#176;</mo></mrow></mfrac><mo>&#160;</mo><mo>=</mo><mi>t</mi><mi>a</mi><mi>n</mi><mo>(</mo><mn>60</mn><mo>&#176;</mo><mo>-</mo><mn>15</mn><mo>&#176;</mo><mo>)</mo><mo>=</mo><mi>t</mi><mi>a</mi><mi>n</mi><mo>(</mo><mn>45</mn><mo>&#176;</mo><mo>)</mo><mo>=</mo><mn>1</mn><mspace linebreak=\"newline\"/></math></span></p>",
                    solution_hi: "<p>4.(b)</p>\r\n<p><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>t</mi><mi>a</mi><mi>n</mi><mi>&#160;</mi><mo>(</mo><mi>A</mi><mo>-</mo><mi>B</mi><mo>)</mo><mi>&#160;</mi><mo>&#160;</mo><mo>=</mo><mfrac><mrow><mi>t</mi><mi>a</mi><mi>n</mi><mi>&#160;</mi><mi>A</mi><mi>&#160;</mi><mo>-</mo><mi>t</mi><mi>a</mi><mi>n</mi><mi>&#160;</mi><mi>B</mi><mi>&#160;</mi></mrow><mrow><mn>1</mn><mo>+</mo><mi>t</mi><mi>a</mi><mi>n</mi><mi>&#160;</mi><mi>A</mi><mi>&#160;</mi><mo>.</mo><mi>t</mi><mi>a</mi><mi>n</mi><mi>&#160;</mi><mi>B</mi><mi>&#160;</mi></mrow></mfrac><mspace linebreak=\"newline\"/><mfrac><mrow><mi>t</mi><mi>a</mi><mi>n</mi><mi>&#160;</mi><mn>60</mn><mo>&#176;</mo><mo>-</mo><mi>t</mi><mi>a</mi><mi>n</mi><mi>&#160;</mi><mn>15</mn><mo>&#176;</mo></mrow><mrow><mn>1</mn><mo>+</mo><mi>t</mi><mi>a</mi><mi>n</mi><mi>&#160;</mi><mn>60</mn><mo>&#176;</mo><mi>t</mi><mi>a</mi><mi>n</mi><mi>&#160;</mi><mn>15</mn><mo>&#176;</mo></mrow></mfrac><mo>&#160;</mo><mo>=</mo><mi>t</mi><mi>a</mi><mi>n</mi><mo>(</mo><mn>60</mn><mo>&#176;</mo><mo>-</mo><mn>15</mn><mo>&#176;</mo><mo>)</mo><mo>=</mo><mi>t</mi><mi>a</mi><mi>n</mi><mo>(</mo><mn>45</mn><mo>&#176;</mo><mo>)</mo><mo>=</mo><mn>1</mn><mspace linebreak=\"newline\"/></math></span><span style=\"font-family: Times New Roman;\"> </span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "17",
                    question_en: "<p>5. If 3<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mi>s</mi><mi>e</mi><mi>c</mi></mrow><mn>2</mn></msup><mi>x</mi><mo>-</mo><mn>4</mn><mo>=</mo><mn>0</mn><mo>,</mo></math><span style=\"font-family: Times New Roman;\"> then the value of x (0 &lt; x &lt; 90&deg;)</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">SSC CHSL 12-10-2020 (Afternoon shift)</span></p>",
                    question_hi: "<p>5. <span style=\"font-family: Baloo;\">यदि</span><span style=\"font-family: Times New Roman;\"> 3<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mi>s</mi><mi>e</mi><mi>c</mi></mrow><mn>2</mn></msup><mi>x</mi><mo>-</mo><mn>4</mn><mo>=</mo><mn>0</mn><mo>,</mo></math></span><span style=\"font-family: Baloo;\"> है, तो </span><span style=\"font-family: Times New Roman;\">x</span><span style=\"font-family: Baloo;\"> का मान क्या होगा ? (0 &lt; x &lt; 90&deg;)</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">CHSL 12-10-2020 (Afternoon shift)</span></p>",
                    options_en: ["<p>15&deg;</p>", "<p>45&deg;</p>", 
                                "<p>30&deg;</p>", "<p>60&deg;</p>"],
                    options_hi: ["<p>15&deg;</p>", "<p>45&deg;</p>",
                                "<p>30&deg;</p>", "<p>60&deg;</p>"],
                    solution_en: "<p>5. (c)&nbsp;</p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>3</mn><msup><mrow><mi>s</mi><mi>e</mi><mi>c</mi></mrow><mn>2</mn></msup><mi>x</mi><mo>-</mo><mn>4</mn><mo>=</mo><mn>0</mn><mo>,</mo><mspace linebreak=\"newline\"/><msup><mrow><mi>s</mi><mi>e</mi><mi>c</mi></mrow><mn>2</mn></msup><mi>x</mi><mo>&#160;</mo><mo>=</mo><mfrac><mn>4</mn><mn>3</mn></mfrac><mspace linebreak=\"newline\"/><mi>s</mi><mi>e</mi><mi>c</mi><mi>x</mi><mo>&#160;</mo><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mfrac><mrow><mn>2</mn><mo>&#160;</mo></mrow><msqrt><mn>3</mn></msqrt></mfrac><mspace linebreak=\"newline\"/><mi>x</mi><mo>=</mo><msup><mn>30</mn><mn>0</mn></msup><mspace linebreak=\"newline\"/><mspace linebreak=\"newline\"/><mspace linebreak=\"newline\"/></math></p>\r\n<p>&nbsp;</p>",
                    solution_hi: "<p>5. (c)&nbsp;</p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>3</mn><mi>s</mi><mi>e</mi><msup><mi>c</mi><mn>2</mn></msup><mi>x</mi><mo>-</mo><mn>4</mn><mo>=</mo><mn>0</mn><mo>,</mo><mspace linebreak=\"newline\"/><msup><mrow><mi>s</mi><mi>e</mi><mi>c</mi></mrow><mn>2</mn></msup><mi>x</mi><mo>&#160;</mo><mo>=</mo><mfrac><mn>4</mn><mn>3</mn></mfrac><mspace linebreak=\"newline\"/><mi>s</mi><mi>e</mi><mi>c</mi><mi>x</mi><mo>=</mo><mfrac><mn>2</mn><msqrt><mn>3</mn></msqrt></mfrac><mspace linebreak=\"newline\"/><mspace linebreak=\"newline\"/><mspace linebreak=\"newline\"/></math></p>\r\n<p>&nbsp;</p>\r\n<p><span style=\"font-family: Times New Roman;\"> x = 30&deg;</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "17",
                    question_en: "<p>6. If 4<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mi>c</mi><mi>o</mi><mi>s</mi></mrow><mn>2</mn></msup><mi>&#952;</mi><mo>-</mo><mn>3</mn><msup><mrow><mi>s</mi><mi>i</mi><mi>n</mi></mrow><mn>2</mn></msup><mi>&#952;</mi><mo>+</mo><mn>2</mn><mo>=</mo><mn>0</mn><mo>,</mo></math><span style=\"font-family: Times New Roman;\"> then the value of tan<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#952;</mi></math> </span><span style=\"font-family: Times New Roman;\"> is (where 0<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8804;</mo><mi>&#952;</mi><mo>&#60;</mo><mn>90</mn><mo>&#176;</mo></math> </span><span style=\"font-family: Times New Roman;\">)</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">SSC CHSL 12-10-2020 (Afternoon shift)</span></p>",
                    question_hi: "<p>6. <span style=\"font-family: Baloo;\">यदि</span><span style=\"font-family: Times New Roman;\"> 4<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mi>c</mi><mi>o</mi><mi>s</mi></mrow><mn>2</mn></msup><mi>&#952;</mi><mo>-</mo><mn>3</mn><msup><mrow><mi>s</mi><mi>i</mi><mi>n</mi></mrow><mn>2</mn></msup><mi>&#952;</mi><mo>+</mo><mn>2</mn><mo>=</mo><mn>0</mn><mo>,</mo></math></span><span style=\"font-family: Baloo;\"> है, तो</span><span style=\"font-family: Times New Roman;\"> tan<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#952;</mi></math> </span><span style=\"font-family: Baloo;\"> का मान क्या होगा? (जहाँ</span><span style=\"font-family: Times New Roman;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>0</mn><mo>&#8804;</mo><mi>&#952;</mi><mo>&#60;</mo><mn>90</mn><mo>&#176;</mo></math></span><span style=\"font-family: Times New Roman;\">)</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">CHSL 12-10-2020 (Afternoon shift)</span></p>",
                    options_en: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>6</mn></msqrt></math></p>", "<p>1</p>", 
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>2</mn></msqrt></math></p>", "<p><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msqrt><mn>3</mn></msqrt></mfrac></math> </span></p>"],
                    options_hi: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>6</mn></msqrt></math></p>", "<p>1</p>",
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>2</mn></msqrt></math></p>", "<p><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msqrt><mn>3</mn></msqrt></mfrac></math> </span></p>"],
                    solution_en: "<p>6.(a)</p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>4</mn><msup><mrow><mi>c</mi><mi>o</mi><mi>s</mi></mrow><mn>2</mn></msup><mi>&#952;</mi><mo>-</mo><mn>3</mn><msup><mrow><mi>s</mi><mi>i</mi><mi>n</mi></mrow><mn>2</mn></msup><mi>&#952;</mi><mo>+</mo><mn>2</mn><mo>=</mo><mn>0</mn><mo>,</mo><mspace linebreak=\"newline\"/><mn>4</mn><msup><mrow><mi>c</mi><mi>o</mi><mi>s</mi></mrow><mn>2</mn></msup><mi>&#952;</mi><mo>-</mo><mn>3</mn><mo>+</mo><mn>3</mn><msup><mrow><mi>c</mi><mi>o</mi><mi>s</mi></mrow><mn>2</mn></msup><mi>&#952;</mi><mo>+</mo><mn>2</mn><mo>=</mo><mn>0</mn><mspace linebreak=\"newline\"/><mn>7</mn><msup><mrow><mi>c</mi><mi>o</mi><mi>s</mi></mrow><mn>2</mn></msup><mi>&#952;</mi><mo>-</mo><mn>1</mn><mo>=</mo><mn>0</mn><mspace linebreak=\"newline\"/><mi>cos</mi><mi>&#952;</mi><mo>&#160;</mo><mo>=</mo><mfrac><mn>1</mn><msqrt><mn>7</mn></msqrt></mfrac><mo>&#160;</mo><mo>=</mo><mfrac><mi>B</mi><mi>H</mi></mfrac><mspace linebreak=\"newline\"/><mi>B</mi><mo>=</mo><mn>1</mn><mo>,</mo><mo>&#160;</mo><mi>H</mi><mo>=</mo><mo>&#160;</mo><msqrt><mn>7</mn></msqrt><mo>&#160;</mo><mo>,</mo><mo>&#160;</mo><mi>P</mi><mo>&#160;</mo><mo>=</mo><msqrt><mn>6</mn></msqrt><mspace linebreak=\"newline\"/><mi>t</mi><mi>a</mi><mi>n</mi><mo>&#160;</mo><mi>&#952;</mi><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mfrac><mi>P</mi><mi>B</mi></mfrac><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mo>&#8730;</mo><mn>6</mn><mspace linebreak=\"newline\"/><mspace linebreak=\"newline\"/><mspace linebreak=\"newline\"/></math></p>",
                    solution_hi: "<p>6.(a)</p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>4</mn><msup><mrow><mi>c</mi><mi>o</mi><mi>s</mi></mrow><mn>2</mn></msup><mi>&#952;</mi><mo>-</mo><mn>3</mn><msup><mrow><mi>s</mi><mi>i</mi><mi>n</mi></mrow><mn>2</mn></msup><mi>&#952;</mi><mo>+</mo><mn>2</mn><mo>=</mo><mn>0</mn><mspace linebreak=\"newline\"/><mn>4</mn><msup><mrow><mi>c</mi><mi>o</mi><mi>s</mi></mrow><mn>2</mn></msup><mi>&#952;</mi><mo>-</mo><mn>3</mn><mo>+</mo><mn>3</mn><msup><mrow><mi>c</mi><mi>o</mi><mi>s</mi></mrow><mn>2</mn></msup><mi>&#952;</mi><mo>+</mo><mn>2</mn><mo>=</mo><mn>0</mn><mspace linebreak=\"newline\"/><mn>7</mn><msup><mrow><mi>c</mi><mi>o</mi><mi>s</mi></mrow><mn>2</mn></msup><mi>&#952;</mi><mo>-</mo><mn>1</mn><mo>=</mo><mn>0</mn><mspace linebreak=\"newline\"/><mi>cos</mi><mi>&#952;</mi><mo>&#160;</mo><mo>=</mo><mfrac><mn>1</mn><msqrt><mn>7</mn></msqrt></mfrac><mo>&#160;</mo><mo>=</mo><mfrac><mi>B</mi><mi>H</mi></mfrac></math></p>\r\n<p><span style=\"font-family: Times New Roman;\">B = 1, H = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>7</mn></msqrt></math></span><span style=\"font-family: Times New Roman;\"> , P =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>6</mn></msqrt></math></span></p>\r\n<p><span style=\"font-family: Times New Roman;\">tan <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#952;</mi></math></span><span style=\"font-family: Times New Roman;\"> = P/B =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>6</mn></msqrt></math> </span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "17",
                    question_en: "<p>7. What is the value of <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mi>s</mi><mi>i</mi><mi>n</mi></mrow><mn>2</mn></msup><mn>45</mn><mo>&#176;</mo><mo>+</mo><msup><mrow><mi>c</mi><mi>o</mi><mi>s</mi></mrow><mn>2</mn></msup><mn>45</mn><mo>&#176;</mo></math><span style=\"font-family: Times New Roman;\">?</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">SSC CHSL 12-10-2020 (Evening shift)</span></p>",
                    question_hi: "<p>7.&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mi>s</mi><mi>i</mi><mi>n</mi></mrow><mn>2</mn></msup><mn>45</mn><mo>&#176;</mo><mo>+</mo><msup><mrow><mi>c</mi><mi>o</mi><mi>s</mi></mrow><mn>2</mn></msup><mn>45</mn><mo>&#176;</mo></math> <span style=\"font-family: Baloo;\">का मान क्या है?</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">CHSL 12-10-2020 (Evening shift)</span></p>",
                    options_en: ["<p>1</p>", "<p>2</p>", 
                                "<p>-1</p>", "<p>0</p>"],
                    options_hi: ["<p>1</p>", "<p>2</p>",
                                "<p>-1</p>", "<p>0</p>"],
                    solution_en: "<p>7.(a)</p>\r\n<p><span style=\"font-family: Times New Roman;\">We know that,</span></p>\r\n<p><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mi>s</mi><mi>i</mi><mi>n</mi></mrow><mn>2</mn></msup><mi>&#952;</mi><mo>&#176;</mo><mo>+</mo><msup><mrow><mi>c</mi><mi>o</mi><mi>s</mi></mrow><mn>2</mn></msup><mi>&#952;</mi><mo>&#176;</mo></math>= 1,</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Therefore,</span></p>\r\n<p><span style=\"font-family: Times New Roman;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mi>s</mi><mi>i</mi><mi>n</mi></mrow><mn>2</mn></msup><mn>45</mn><mo>&#176;</mo><mo>+</mo><msup><mrow><mi>c</mi><mi>o</mi><mi>s</mi></mrow><mn>2</mn></msup><mn>45</mn><mo>&#176;</mo></math>= 1</span></p>",
                    solution_hi: "<p>7.(a)</p>\r\n<p><span style=\"font-family: Baloo;\">हम जानते हैं की</span><span style=\"font-family: Times New Roman;\">, </span></p>\r\n<p><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mi>s</mi><mi>i</mi><mi>n</mi></mrow><mn>2</mn></msup><mi>&#952;</mi><mo>&#176;</mo><mo>+</mo><msup><mrow><mi>c</mi><mi>o</mi><mi>s</mi></mrow><mn>2</mn></msup><mi>&#952;</mi><mo>&#176;</mo><mo>&#160;</mo><mo>=</mo><mn>1</mn></math></span></p>\r\n<p><span style=\"font-family: Baloo;\">इसलिए</span><span style=\"font-family: Times New Roman;\">,</span></p>\r\n<p><span style=\"font-family: Times New Roman;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mi>s</mi><mi>i</mi><mi>n</mi></mrow><mn>2</mn></msup><mn>45</mn><mo>&#176;</mo><mo>+</mo><msup><mrow><mi>c</mi><mi>o</mi><mi>s</mi></mrow><mn>2</mn></msup><mn>45</mn><mo>&#176;</mo></math>= 1</span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "17",
                    question_en: "<p>8. If <span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>tan</mi><mi>&#952;</mi><mo>&#160;</mo><mo>=</mo><mfrac><mn>20</mn><mn>21</mn></mfrac></math></span><span style=\"font-family: Times New Roman;\"> then the value of&nbsp;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#160;</mi><mi>&#952;</mi><mo>-</mo><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#160;</mi><mi>&#952;</mi></mrow><mrow><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#160;</mi><mi>&#952;</mi><mo>+</mo><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#160;</mi><mi>&#952;</mi></mrow></mfrac></math></span><span style=\"font-family: Times New Roman;\">is:</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">SSC CHSL 12-10-2020 (Evening shift)</span></p>",
                    question_hi: "<p>8. <span style=\"font-family: Baloo;\">यदि <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>t</mi><mi>a</mi><mi>n</mi><mi>&#952;</mi><mo>&#160;</mo><mo>=</mo><mfrac><mn>20</mn><mn>21</mn></mfrac></math></span><span style=\"font-family: Times New Roman;\">&nbsp;</span><span style=\"font-family: Baloo;\">है, तो</span><span style=\"font-family: Times New Roman;\"> &nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#160;</mi><mi>&#952;</mi><mo>-</mo><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#160;</mi><mi>&#952;</mi></mrow><mrow><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#160;</mi><mi>&#952;</mi><mo>+</mo><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#160;</mi><mi>&#952;</mi></mrow></mfrac><mo>&#160;</mo></math></span><span style=\"font-family: Baloo;\">का मान क्या है? </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">CHSL 12-10-2020 (Evening shift)</span></p>",
                    options_en: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>-</mo><mfrac><mn>1</mn><mn>41</mn></mfrac></math></p>", "<p><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>27</mn><mn>21</mn></mfrac></math> </span></p>", 
                                "<p><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>29</mn><mn>35</mn></mfrac></math> </span></p>", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>-</mo><mfrac><mn>29</mn><mn>31</mn></mfrac></math><span style=\"font-family: Times New Roman;\"> </span></p>"],
                    options_hi: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>-</mo><mfrac><mn>1</mn><mn>41</mn></mfrac></math></p>", "<p><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>27</mn><mn>21</mn></mfrac></math> </span></p>",
                                "<p><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>29</mn><mn>35</mn></mfrac></math> </span></p>", "<p><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>-</mo><mfrac><mn>29</mn><mn>31</mn></mfrac></math> </span></p>"],
                    solution_en: "<p>8.(a)</p>\r\n<p><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#160;</mi><mi>&#952;</mi><mo>-</mo><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#160;</mi><mi>&#952;</mi></mrow><mrow><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#160;</mi><mi>&#952;</mi><mo>+</mo><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#160;</mi><mi>&#952;</mi></mrow></mfrac><mo>=</mo><mfrac><mrow><mi>t</mi><mi>a</mi><mi>n</mi><mi>&#952;</mi><mi>&#160;</mi><mo>-</mo><mn>1</mn></mrow><mrow><mi>t</mi><mi>a</mi><mi>n</mi><mi>&#952;</mi><mi>&#160;</mi><mo>+</mo><mn>1</mn></mrow></mfrac></math></span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Put <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>tan</mi><mi>&#952;</mi></math></span><span style=\"font-family: Times New Roman;\">=<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>20</mn><mn>21</mn></mfrac></math></span><span style=\"font-family: Times New Roman;\">, we get</span></p>\r\n<p><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>-</mo><mn>1</mn></mrow><mn>41</mn></mfrac></math></span></p>",
                    solution_hi: "<p>8.(a)</p>\r\n<p><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>t</mi><mi>a</mi><mi>n</mi><mi>&#952;</mi></math>=&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>20</mn><mn>21</mn></mfrac></math> </span><span style=\"font-family: Baloo;\">रखने पर हम पाते हैं की, </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">=</span><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#160;</mi><mi>&#952;</mi><mo>-</mo><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#160;</mi><mi>&#952;</mi></mrow><mrow><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#160;</mi><mi>&#952;</mi><mo>+</mo><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#160;</mi><mi>&#952;</mi></mrow></mfrac><mo>&#160;</mo><mo>=</mo><mfrac><mrow><mi>t</mi><mi>a</mi><mi>n</mi><mi>&#952;</mi><mi>&#160;</mi><mo>-</mo><mn>1</mn></mrow><mrow><mi>t</mi><mi>a</mi><mi>n</mi><mi>&#952;</mi><mi>&#160;</mi><mo>+</mo><mn>1</mn></mrow></mfrac><mo>&#160;</mo><mo>=</mo><mo>-</mo><mfrac><mn>1</mn><mn>41</mn></mfrac></math></span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "17",
                    question_en: "<p>9. If sin x =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>12</mn><mn>37</mn></mfrac><mo>,</mo></math><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Times New Roman;\">then</span><span style=\"font-family: Times New Roman;\"> what is the value of tan x?</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">SSC CHSL 12-10-2020 (Evening shift)</span></p>",
                    question_hi: "<p>9. <span style=\"font-family: Baloo;\">यदि</span><span style=\"font-family: Times New Roman;\"> sin x =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>12</mn><mn>37</mn></mfrac><mo>,</mo></math></span><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Baloo;\">है, तो tan x का मान क्या होगा? </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">CHSL 12-10-2020 (Evening shift)</span></p>",
                    options_en: ["<p><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>35</mn><mn>37</mn></mfrac></math> </span></p>", "<p><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>12</mn><mn>35</mn></mfrac></math> </span></p>", 
                                "<p><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>37</mn><mn>12</mn></mfrac></math> </span></p>", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>35</mn><mn>12</mn></mfrac></math></p>"],
                    options_hi: ["<p><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>35</mn><mn>37</mn></mfrac></math> </span></p>", "<p><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>12</mn><mn>35</mn></mfrac></math> </span></p>",
                                "<p><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>37</mn><mn>12</mn></mfrac></math> </span></p>", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>35</mn><mn>12</mn></mfrac></math></p>"],
                    solution_en: "<p>9.(b)</p>\r\n<p><span style=\"font-family: Times New Roman;\">sin x =</span><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>P</mi><mi>H</mi></mfrac><mo>=</mo><mfrac><mn>12</mn><mn>37</mn></mfrac></math></span><span style=\"font-family: Times New Roman;\"> </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Here Perpendicular,P = 12, Hypotenuse,H = 37, then Base,B = 35 (by pythagoras theorem)</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">tan x = </span><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>P</mi><mi>B</mi></mfrac><mo>=</mo><mfrac><mn>12</mn><mn>35</mn></mfrac></math></span><span style=\"font-family: Times New Roman;\"> </span></p>",
                    solution_hi: "<p>9.(b)</p>\r\n<p><span style=\"font-family: Times New Roman;\">sin x =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>P</mi><mi>H</mi></mfrac><mo>&#160;</mo><mo>=</mo><mfrac><mn>12</mn><mn>37</mn></mfrac></math></span><span style=\"font-family: Times New Roman;\"> </span></p>\r\n<p><span style=\"font-family: Baloo;\">यंहा </span><span style=\"font-family: Baloo;\">लम्ब</span><span style=\"font-family: Times New Roman;\"> (P) = 12, </span><span style=\"font-family: Baloo;\">कर्ण</span><span style=\"font-family: Times New Roman;\"> (H) = 37 </span><span style=\"font-family: Baloo;\">तो</span><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Baloo;\">आधार</span><span style=\"font-family: Times New Roman;\"> (B) = 35 (</span><span style=\"font-family: Baloo;\">पाइथागोरस प्रमेय द्वारा</span><span style=\"font-family: Times New Roman;\">)</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">tan x = </span><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>P</mi><mi>B</mi></mfrac><mo>=</mo><mfrac><mn>12</mn><mn>35</mn></mfrac></math></span><span style=\"font-family: Times New Roman;\"> </span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "17",
                    question_en: "<p>10. If A = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>=</mo><mo>&#160;</mo><mn>2</mn><mo>(</mo><msup><mi>sin</mi><mn>6</mn></msup><mi>&#952;</mi><mo>+</mo><mo>&#160;</mo><msup><mi>cos</mi><mn>6</mn></msup><mi>&#952;</mi><mo>)</mo><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mn>3</mn><mo>(</mo><msup><mi>sin</mi><mn>4</mn></msup><mi>&#952;</mi><mo>+</mo><mo>&#160;</mo><msup><mi>cos</mi><mn>4</mn></msup><mi>&#952;</mi><mo>)</mo><mo>&#160;</mo></math><span style=\"font-family: Times New Roman;\">&nbsp;then the value of 3<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#945;</mi></math></span><span style=\"font-family: Times New Roman;\"> such that cos<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#945;</mi><mo>=</mo><msqrt><mfrac><mrow><mn>3</mn><mo>+</mo><mi>A</mi></mrow><mrow><mn>5</mn><mo>+</mo><mi>A</mi></mrow></mfrac></msqrt></math></span><span style=\"font-family: Times New Roman;\">&nbsp;</span><span style=\"font-family: Times New Roman;\">is</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">SSC CHSL 13-10-2020 (Morning Shift)</span></p>",
                    question_hi: "<p>10. <span style=\"font-family: Baloo;\">यदि </span><span style=\"font-family: Times New Roman;\">A = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>=</mo><mo>&#160;</mo><mn>2</mn><mo>(</mo><msup><mi>sin</mi><mn>6</mn></msup><mi>&#952;</mi><mo>+</mo><mo>&#160;</mo><msup><mi>cos</mi><mn>6</mn></msup><mi>&#952;</mi><mo>)</mo><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mn>3</mn><mo>(</mo><msup><mi>sin</mi><mn>4</mn></msup><mi>&#952;</mi><mo>+</mo><mo>&#160;</mo><msup><mi>cos</mi><mn>4</mn></msup><mi>&#952;</mi><mo>)</mo></math></span><span style=\"font-family: Baloo;\">&nbsp;है, तो </span><span style=\"font-family: Times New Roman;\">3<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#945;</mi></math></span><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Baloo;\">का मान इस प्रकार ज्ञात कीजिए कि</span><span style=\"font-family: Times New Roman;\"> cos<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#945;</mi><mo>&#160;</mo><mo>=</mo><msqrt><mfrac><mrow><mn>3</mn><mo>+</mo><mi>A</mi></mrow><mrow><mn>5</mn><mo>+</mo><mi>A</mi></mrow></mfrac></msqrt></math> </span><span style=\"font-family: Times New Roman;\">&nbsp;</span><span style=\"font-family: Baloo;\">हो।</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">CHSL 13-10-2020 (Morning Shift)</span></p>",
                    options_en: ["<p>45&deg;</p>", "<p>135&deg;</p>", 
                                "<p>180&deg;</p>", "<p>90&deg;</p>"],
                    options_hi: ["<p>45&deg;</p>", "<p>135&deg;</p>",
                                "<p>180&deg;</p>", "<p>90&deg;</p>"],
                    solution_en: "<p>10.(b)</p>\r\n<p><span style=\"font-family: Times New Roman;\">Short-Trick:</span></p>\r\n<p><span style=\"font-family: Times New Roman;\"> Put <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#952;</mi></math></span><span style=\"font-family: Times New Roman;\"> = 0&deg;, then</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">A = 2-3 = -1</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">cos<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#945;</mi><mo>=</mo><msqrt><mfrac><mrow><mn>3</mn><mo>+</mo><mi>A</mi></mrow><mrow><mn>5</mn><mo>+</mo><mi>A</mi></mrow></mfrac></msqrt></math></span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Put value of A, we get</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">cos<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#945;</mi><mo>=</mo><mfrac><mn>1</mn><msqrt><mn>2</mn></msqrt></mfrac></math></span></p>\r\n<p><span style=\"font-family: Times New Roman;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#945;</mi></math>= 45&deg;</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">So, 3<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#945;</mi></math></span><span style=\"font-family: Times New Roman;\"> = 3&times;</span><span style=\"font-family: Times New Roman;\">45&deg; = 135&deg;</span></p>",
                    solution_hi: "<p>10.(b)</p>\r\n<p><span style=\"font-family: Baloo;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#952;</mi></math>= 0&deg; रखने पर </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">A = 2-3 = -1</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">cos<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#945;</mi><mo>&#160;</mo><mo>=</mo><msqrt><mfrac><mrow><mn>3</mn><mo>+</mo><mi>A</mi></mrow><mrow><mn>5</mn><mo>+</mo><mi>A</mi></mrow></mfrac></msqrt></math></span></p>\r\n<p><span style=\"font-family: Times New Roman;\">A </span><span style=\"font-family: Baloo;\">का मान रखने पर हम पाते हैं की</span><span style=\"font-family: Times New Roman;\">,</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">cos&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#945;</mi><mo>=</mo><mfrac><mn>1</mn><msqrt><mn>2</mn></msqrt></mfrac></math></span></p>\r\n<p><span style=\"font-family: Times New Roman;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#945;</mi></math>= 45&deg;</span></p>\r\n<p><span style=\"font-family: Baloo;\">इसलिए</span><span style=\"font-family: Times New Roman;\">, 3</span><span style=\"font-family: Times New Roman;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#945;</mi></math>= 3<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#215;</mo></math></span><span style=\"font-family: Times New Roman;\">45&deg; = 135&deg;</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "17",
                    question_en: "<p>11. If <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>117</mn><mi>C</mi><mi>o</mi><msup><mi>s</mi><mn>2</mn></msup><mi>A</mi><mo>+</mo><mn>129</mn><mi>S</mi><mi>i</mi><msup><mi>n</mi><mn>2</mn></msup><mi>A</mi><mo>=</mo><mn>120</mn><mo>&#160;</mo><mo>,</mo><mo>&#160;</mo><mo>&#160;</mo><mn>170</mn><mi>C</mi><mi>o</mi><msup><mi>s</mi><mn>2</mn></msup><mi>B</mi><mo>+</mo><mn>158</mn><mi>S</mi><mi>i</mi><msup><mi>n</mi><mn>2</mn></msup><mi>B</mi><mo>=</mo><mn>161</mn></math><span style=\"font-family: Times New Roman;\"> then the value of <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>C</mi><mi>o</mi><mi>s</mi><mi>e</mi><msup><mi>c</mi><mn>2</mn></msup><mi>A</mi><mo>&#160;</mo><mi>S</mi><mi>e</mi><msup><mi>c</mi><mn>2</mn></msup><mi>B</mi></math></span><span style=\"font-family: Times New Roman;\">&nbsp;is:</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">SSC CHSL 13-10-2020 (Morning Shift)</span></p>",
                    question_hi: "<p>11. <span style=\"font-family: Baloo;\">यदि</span><span style=\"font-family: Times New Roman;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>117</mn><mo>&#160;</mo><mi>C</mi><mi>o</mi><msup><mi>s</mi><mn>2</mn></msup><mi>A</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>129</mn><mo>&#160;</mo><mi>S</mi><mi>i</mi><msup><mi>n</mi><mn>2</mn></msup><mi>A</mi><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>120</mn></math></span><span style=\"font-family: Times New Roman;\">&nbsp;</span><span style=\"font-family: Baloo;\">तथा</span><span style=\"font-family: Times New Roman;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>170</mn><mo>&#160;</mo><mi>C</mi><mi>o</mi><msup><mi>s</mi><mn>2</mn></msup><mi>B</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>158</mn><mo>&#160;</mo><mi>S</mi><mi>i</mi><msup><mi>n</mi><mn>2</mn></msup><mi>B</mi><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>161</mn></math></span><span style=\"font-family: Times New Roman;\">&nbsp;</span><span style=\"font-family: Baloo;\">है, तो <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#160;</mo><mi>C</mi><mi>o</mi><mi>s</mi><mi>e</mi><msup><mi>c</mi><mn>2</mn></msup><mi>A</mi><mo>&#160;</mo><mi>S</mi><mi>e</mi><msup><mi>c</mi><mn>2</mn></msup><mi>B</mi><mo>&#160;</mo></math></span><span style=\"font-family: Baloo;\">का मान क्या होगा ? </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">CHSL 13-10-2020 (Morning Shift)</span></p>",
                    options_en: ["<p>1</p>", "<p>9</p>", 
                                "<p>4</p>", "<p>16</p>"],
                    options_hi: ["<p>1</p>", "<p>9</p>",
                                "<p>4</p>", "<p>16</p>"],
                    solution_en: "<p>11.(d)</p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>117</mn><mo>&#160;</mo><mi>C</mi><mi>o</mi><msup><mi>s</mi><mn>2</mn></msup><mi>A</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>129</mn><mo>&#160;</mo><mi>S</mi><mi>i</mi><msup><mi>n</mi><mn>2</mn></msup><mi>A</mi><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>120</mn><mo>&#160;</mo><mspace linebreak=\"newline\"/><mn>117</mn><mo>&#160;</mo><mi>C</mi><mi>o</mi><msup><mi>s</mi><mn>2</mn></msup><mi>A</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>117</mn><mo>&#160;</mo><mi>S</mi><mi>i</mi><msup><mi>n</mi><mn>2</mn></msup><mi>A</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mo>&#160;</mo><mn>12</mn><mo>&#160;</mo><mi>S</mi><mi>i</mi><msup><mi>n</mi><mn>2</mn></msup><mi>A</mi><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>120</mn><mspace linebreak=\"newline\"/><mo>&#160;</mo><mo>&#8658;</mo><mo>&#160;</mo><mn>117</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>12</mn><mi>S</mi><mi>i</mi><msup><mi>n</mi><mn>2</mn></msup><mi>A</mi><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>120</mn><mspace linebreak=\"newline\"/><mo>&#160;</mo><mo>&#160;</mo><mo>&#8658;</mo><mo>&#160;</mo><mn>12</mn><mi>S</mi><mi>i</mi><msup><mi>n</mi><mn>2</mn></msup><mi>A</mi><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>3</mn><mspace linebreak=\"newline\"/><mi>S</mi><mi>i</mi><msup><mi>n</mi><mn>2</mn></msup><mi>A</mi><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mfrac><mn>1</mn><mn>4</mn></mfrac><mo>&#160;</mo><mspace linebreak=\"newline\"/><mo>&#160;</mo><mn>170</mn><mo>&#160;</mo><mi>C</mi><mi>o</mi><msup><mi>s</mi><mn>2</mn></msup><mi>B</mi><mo>+</mo><mo>&#160;</mo><mn>158</mn><mo>&#160;</mo><msup><mi>sin</mi><mn>2</mn></msup><mi>B</mi><mo>=</mo><mo>&#160;</mo><mn>161</mn><mspace linebreak=\"newline\"/><mo>&#160;</mo><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>12</mn><mo>&#160;</mo><mi>C</mi><mi>o</mi><msup><mi>s</mi><mn>2</mn></msup><mi>B</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>158</mn><mo>&#160;</mo><mi>C</mi><mi>o</mi><msup><mi>s</mi><mn>2</mn></msup><mi>B</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>158</mn><msup><mi>sin</mi><mn>2</mn></msup><mi>B</mi><mo>&#160;</mo><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>161</mn><mo>&#160;</mo><mo>&#160;</mo><mspace linebreak=\"newline\"/><mo>&#160;</mo><mn>12</mn><mo>&#160;</mo><mi>C</mi><mi>o</mi><msup><mi>s</mi><mn>2</mn></msup><mi>B</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>158</mn><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>161</mn><mo>&#160;</mo><mo>&#160;</mo><mspace linebreak=\"newline\"/><mo>&#160;</mo><mn>12</mn><mo>&#160;</mo><mi>C</mi><mi>o</mi><msup><mi>s</mi><mn>2</mn></msup><mi>B</mi><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>3</mn><mspace linebreak=\"newline\"/><mo>&#160;</mo><mo>&#160;</mo><mi>C</mi><mi>o</mi><msup><mi>s</mi><mn>2</mn></msup><mi>B</mi><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mfrac><mn>1</mn><mn>4</mn></mfrac><mspace linebreak=\"newline\"/><mi>c</mi><mi>o</mi><mi>s</mi><mi>e</mi><msup><mi>c</mi><mn>2</mn></msup><mi>A</mi><mi>S</mi><mi>e</mi><msup><mi>c</mi><mn>2</mn></msup><mi>B</mi><mo>&#160;</mo><mo>=</mo><mfrac><mrow><mi>&#160;</mi><mn>1</mn></mrow><mrow><mi>&#160;</mi><mi>S</mi><mi>i</mi><msup><mi>n</mi><mn>2</mn></msup><mi>A</mi><mi>&#160;</mi><mi>C</mi><mi>o</mi><msup><mi>s</mi><mn>2</mn></msup><mi>B</mi></mrow></mfrac><mo>=</mo><mn>4</mn><mo>&#215;</mo><mn>4</mn><mo>=</mo><mn>16</mn></math></p>",
                    solution_hi: "<p>11.(d)</p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>117</mn><mo>&#160;</mo><mi>C</mi><mi>o</mi><msup><mi>s</mi><mn>2</mn></msup><mi>A</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>129</mn><mo>&#160;</mo><mi>S</mi><mi>i</mi><msup><mi>n</mi><mn>2</mn></msup><mi>A</mi><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>120</mn><mo>&#160;</mo><mspace linebreak=\"newline\"/><mn>117</mn><mo>&#160;</mo><mi>C</mi><mi>o</mi><msup><mi>s</mi><mn>2</mn></msup><mi>A</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>117</mn><mo>&#160;</mo><mi>S</mi><mi>i</mi><msup><mi>n</mi><mn>2</mn></msup><mi>A</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mo>&#160;</mo><mn>12</mn><mo>&#160;</mo><mi>S</mi><mi>i</mi><msup><mi>n</mi><mn>2</mn></msup><mi>A</mi><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>120</mn><mspace linebreak=\"newline\"/><mo>&#160;</mo><mo>&#8658;</mo><mo>&#160;</mo><mn>117</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>12</mn><mi>S</mi><mi>i</mi><msup><mi>n</mi><mn>2</mn></msup><mi>A</mi><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>120</mn><mspace linebreak=\"newline\"/><mo>&#160;</mo><mo>&#160;</mo><mo>&#8658;</mo><mo>&#160;</mo><mn>12</mn><mi>S</mi><mi>i</mi><msup><mi>n</mi><mn>2</mn></msup><mi>A</mi><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>3</mn><mspace linebreak=\"newline\"/><mi>S</mi><mi>i</mi><msup><mi>n</mi><mn>2</mn></msup><mi>A</mi><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mfrac><mn>1</mn><mn>4</mn></mfrac><mo>&#160;</mo><mspace linebreak=\"newline\"/><mo>&#160;</mo><mn>170</mn><mo>&#160;</mo><mi>C</mi><mi>o</mi><msup><mi>s</mi><mn>2</mn></msup><mi>B</mi><mo>+</mo><mo>&#160;</mo><mn>158</mn><mo>&#160;</mo><msup><mi>sin</mi><mn>2</mn></msup><mi>B</mi><mo>=</mo><mo>&#160;</mo><mn>161</mn><mspace linebreak=\"newline\"/><mo>&#160;</mo><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>12</mn><mo>&#160;</mo><mi>C</mi><mi>o</mi><msup><mi>s</mi><mn>2</mn></msup><mi>B</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>158</mn><mo>&#160;</mo><mi>C</mi><mi>o</mi><msup><mi>s</mi><mn>2</mn></msup><mi>B</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>158</mn><msup><mi>sin</mi><mn>2</mn></msup><mi>B</mi><mo>&#160;</mo><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>161</mn><mo>&#160;</mo><mo>&#160;</mo><mspace linebreak=\"newline\"/><mo>&#160;</mo><mn>12</mn><mo>&#160;</mo><mi>C</mi><mi>o</mi><msup><mi>s</mi><mn>2</mn></msup><mi>B</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>158</mn><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>161</mn><mo>&#160;</mo><mo>&#160;</mo></math></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "17",
                    question_en: "<p>12. If <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#160;</mi><mo>(</mo><mn>1</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#952;</mi><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#952;</mi><mo>)</mo><mi>&#160;</mi></mrow><mrow><mo>(</mo><mn>1</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#952;</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#952;</mi><mo>)</mo><mi>&#160;</mi></mrow></mfrac><mo>+</mo><mfrac><mrow><mi>&#160;</mi><mo>(</mo><mn>1</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#952;</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#952;</mi><mo>)</mo><mi>&#160;</mi></mrow><mrow><mo>(</mo><mn>1</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#952;</mi><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#952;</mi><mo>)</mo><mi>&#160;</mi></mrow></mfrac><mo>&#160;</mo><mo>=</mo><mn>4</mn></math><span style=\"font-family: Times New Roman;\">, then which of the following values will be suitable for <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#952;</mi></math></span><span style=\"font-family: Times New Roman;\">?</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">SSC CHSL 13-10-2020 (Afternoon Shift)</span></p>",
                    question_hi: "<p>12. <span style=\"font-family: Baloo;\">यदि </span><span style=\"font-family: Times New Roman;\">&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#160;</mi><mo>(</mo><mn>1</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#952;</mi><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#952;</mi><mo>)</mo><mi>&#160;</mi></mrow><mrow><mo>(</mo><mn>1</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#952;</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#952;</mi><mo>)</mo><mi>&#160;</mi></mrow></mfrac><mo>&#160;</mo><mo>+</mo><mfrac><mrow><mi>&#160;</mi><mo>(</mo><mn>1</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#952;</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#952;</mi><mo>)</mo><mi>&#160;</mi></mrow><mrow><mo>(</mo><mn>1</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#952;</mi><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#952;</mi><mo>)</mo><mi>&#160;</mi></mrow></mfrac><mo>&#160;</mo><mo>=</mo><mn>4</mn></math></span><span style=\"font-family: Baloo;\">&nbsp;है, तो </span><span style=\"font-family: Baloo;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#952;</mi><mo>&#160;</mo></math>के लिए निम्नलिखित में से कौन सा मान उपयुक्त होगा? </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">CHSL 13-10-2020 (Afternoon Shift)</span></p>",
                    options_en: ["<p>90&deg;</p>", "<p>60&deg;</p>", 
                                "<p>45</p>", "<p>30&deg;</p>"],
                    options_hi: ["<p>90&deg;</p>", "<p>60&deg;</p>",
                                "<p>45</p>", "<p>30&deg;</p>"],
                    solution_en: "<p>12.(d)<span style=\"font-family: Times New Roman;\"> </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Short-Trick:</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">put <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#952;</mi></math></span><span style=\"font-family: Times New Roman;\">= 30&deg;</span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#160;</mi><mo>(</mo><mn>1</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#952;</mi><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#952;</mi><mo>)</mo><mi>&#160;</mi></mrow><mrow><mo>(</mo><mn>1</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#952;</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#952;</mi><mo>)</mo><mi>&#160;</mi></mrow></mfrac><mo>&#160;</mo><mo>+</mo><mfrac><mrow><mi>&#160;</mi><mo>(</mo><mn>1</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#952;</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#952;</mi><mo>)</mo><mi>&#160;</mi></mrow><mrow><mo>(</mo><mn>1</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#952;</mi><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#952;</mi><mo>)</mo><mi>&#160;</mi></mrow></mfrac><mo>=</mo><mn>4</mn><mspace linebreak=\"newline\"/><mfrac><mrow><mi>&#160;</mi><mo>(</mo><mn>1</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mfrac><mn>1</mn><mn>2</mn></mfrac><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mfrac><msqrt><mn>3</mn></msqrt><mn>2</mn></mfrac><mo>)</mo><mi>&#160;</mi></mrow><mrow><mo>(</mo><mn>1</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mfrac><mn>1</mn><mn>2</mn></mfrac><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mfrac><msqrt><mn>3</mn></msqrt><mn>2</mn></mfrac><mo>)</mo><mi>&#160;</mi></mrow></mfrac><mo>+</mo><mfrac><mrow><mn>1</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mfrac><mn>1</mn><mn>2</mn></mfrac><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mfrac><msqrt><mn>3</mn></msqrt><mn>2</mn></mfrac></mrow><mrow><mn>1</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mfrac><mn>1</mn><mn>2</mn></mfrac><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mfrac><msqrt><mn>3</mn></msqrt><mn>2</mn></mfrac></mrow></mfrac><mo>&#160;</mo><mo>=</mo><mn>4</mn><mspace linebreak=\"newline\"/><mfrac><mrow><msup><mrow><mo>(</mo><mn>3</mn><mo>-</mo><msqrt><mn>3</mn></msqrt><msup><mo>)</mo><mn>2</mn></msup></mrow><mrow/></msup><mo>+</mo><mo>(</mo><mn>3</mn><mo>+</mo><msqrt><mn>3</mn></msqrt><msup><mo>)</mo><mn>2</mn></msup></mrow><mrow><mo>(</mo><mn>3</mn><mo>+</mo><msqrt><mn>3</mn></msqrt><mo>)</mo><mo>(</mo><mn>3</mn><mo>-</mo><msqrt><mn>3</mn></msqrt><mo>)</mo></mrow></mfrac><mo>&#160;</mo><mo>=</mo><mn>4</mn><mspace linebreak=\"newline\"/><mfrac><mn>24</mn><mn>6</mn></mfrac><mo>=</mo><mn>4</mn><mspace linebreak=\"newline\"/></math></p>\r\n<p><span style=\"font-family: Times New Roman;\">4 = 4 (Which satisfy)</span></p>",
                    solution_hi: "<p>12.(d)<span style=\"font-family: Times New Roman;\"> </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Short-Trick:</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">put <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#952;</mi></math></span><span style=\"font-family: Times New Roman;\">= 30&deg;</span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#160;</mi><mo>(</mo><mn>1</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#952;</mi><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#952;</mi><mo>)</mo><mi>&#160;</mi></mrow><mrow><mo>(</mo><mn>1</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#952;</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#952;</mi><mo>)</mo><mi>&#160;</mi></mrow></mfrac><mo>&#160;</mo><mo>+</mo><mfrac><mrow><mi>&#160;</mi><mo>(</mo><mn>1</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#952;</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#952;</mi><mo>)</mo><mi>&#160;</mi></mrow><mrow><mo>(</mo><mn>1</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#952;</mi><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#952;</mi><mo>)</mo><mi>&#160;</mi></mrow></mfrac><mo>=</mo><mn>4</mn><mspace linebreak=\"newline\"/><mfrac><mrow><mi>&#160;</mi><mo>(</mo><mn>1</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mfrac><mn>1</mn><mn>2</mn></mfrac><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mfrac><msqrt><mn>3</mn></msqrt><mn>2</mn></mfrac><mo>)</mo><mi>&#160;</mi></mrow><mrow><mo>(</mo><mn>1</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mfrac><mn>1</mn><mn>2</mn></mfrac><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mfrac><msqrt><mn>3</mn></msqrt><mn>2</mn></mfrac><mo>)</mo><mi>&#160;</mi></mrow></mfrac><mo>+</mo><mfrac><mrow><mn>1</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mfrac><mn>1</mn><mn>2</mn></mfrac><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mfrac><msqrt><mn>3</mn></msqrt><mn>2</mn></mfrac></mrow><mrow><mn>1</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mfrac><mn>1</mn><mn>2</mn></mfrac><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mfrac><msqrt><mn>3</mn></msqrt><mn>2</mn></mfrac></mrow></mfrac><mo>&#160;</mo><mo>=</mo><mn>4</mn><mspace linebreak=\"newline\"/><mfrac><mrow><msup><mrow><mo>(</mo><mn>3</mn><mo>-</mo><msqrt><mn>3</mn></msqrt><msup><mo>)</mo><mn>2</mn></msup></mrow><mrow/></msup><mo>+</mo><mo>(</mo><mn>3</mn><mo>+</mo><msqrt><mn>3</mn></msqrt><msup><mo>)</mo><mn>2</mn></msup></mrow><mrow><mo>(</mo><mn>3</mn><mo>+</mo><msqrt><mn>3</mn></msqrt><mo>)</mo><mo>(</mo><mn>3</mn><mo>-</mo><msqrt><mn>3</mn></msqrt><mo>)</mo></mrow></mfrac><mo>&#160;</mo><mo>=</mo><mn>4</mn></math></p>\r\n<p><span style=\"font-family: Times New Roman;\">4 = 4 (Which satisfy)</span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "17",
                    question_en: "<p>13. If <span style=\"font-family: Times New Roman;\">sec A =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#160;</mi><msqrt><mn>11</mn><mi>&#160;</mi></msqrt><mi>&#160;</mi></mrow><mn>3</mn></mfrac></math></span><span style=\"font-family: Times New Roman;\"> then the value of <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#160;</mi><mfrac><mrow><mi>&#160;</mi><msup><mrow><mi>c</mi><mi>o</mi><mi>s</mi><mi>e</mi><mi>c</mi></mrow><mn>2</mn></msup><mi>A</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>t</mi><mi>a</mi><msup><mi>n</mi><mn>2</mn></msup><mi>&#160;</mi><mi>A</mi><mi>&#160;</mi></mrow><mrow><mi>s</mi><mi>i</mi><msup><mi>n</mi><mn>2</mn></msup><mi>&#160;</mi><mi>A</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>c</mi><mi>o</mi><msup><mi>t</mi><mn>2</mn></msup><mi>&#160;</mi><mi>A</mi></mrow></mfrac></math></span><span style=\"font-family: Times New Roman;\">is:</span></p>\r\n<p><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Times New Roman;\">SSC CHSL 13-10-2020 (Afternoon Shift)</span></p>",
                    question_hi: "<p>13. <span style=\"font-family: Baloo;\">यदि</span><span style=\"font-family: Times New Roman;\"> se</span><span style=\"font-family: Times New Roman;\">cA =</span><span style=\"font-family: Times New Roman;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#160;</mi><msqrt><mn>11</mn><mi>&#160;</mi></msqrt><mi>&#160;</mi></mrow><mn>3</mn></mfrac></math></span><span style=\"font-family: Baloo;\">है, </span><span style=\"font-family: Baloo;\">तो</span><span style=\"font-family: Times New Roman;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#160;</mi><mfrac><mrow><mi>&#160;</mi><msup><mrow><mi>c</mi><mi>o</mi><mi>s</mi><mi>e</mi><mi>c</mi></mrow><mn>2</mn></msup><mi>A</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>t</mi><mi>a</mi><msup><mi>n</mi><mn>2</mn></msup><mi>&#160;</mi><mi>A</mi><mi>&#160;</mi></mrow><mrow><mi>s</mi><mi>i</mi><msup><mi>n</mi><mn>2</mn></msup><mi>&#160;</mi><mi>A</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>c</mi><mi>o</mi><msup><mi>t</mi><mn>2</mn></msup><mi>&#160;</mi><mi>A</mi></mrow></mfrac></math></span><span style=\"font-family: Baloo;\">का मान क्या होगा? </span></p>\r\n<p><span style=\"font-family: Times New Roman;\"> CHSL 13-10-2020 (Afternoon Shift)</span></p>",
                    options_en: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>9</mn><mn>4</mn></mfrac></math></p>", "<p><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>9</mn></mfrac></math> </span></p>", 
                                "<p><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>11</mn><mn>9</mn></mfrac></math> </span><span style=\"font-family: Times New Roman;\"> </span></p>", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>11</mn></mfrac></math></p>"],
                    options_hi: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>9</mn><mn>4</mn></mfrac></math></p>", "<p><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>9</mn></mfrac></math> </span></p>",
                                "<p><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>11</mn><mn>9</mn></mfrac></math> </span><span style=\"font-family: Times New Roman;\"> </span></p>", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>11</mn></mfrac></math></p>"],
                    solution_en: "<p>13.(c)</p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>s</mi><mi>e</mi><mi>c</mi><mi>A</mi><mo>=</mo><mfrac><mrow><mo>&#8730;</mo><mn>11</mn></mrow><mn>3</mn></mfrac><mo>&#160;</mo><mspace linebreak=\"newline\"/><mi>P</mi><mo>=</mo><msqrt><mn>2</mn></msqrt><mspace linebreak=\"newline\"/><mfrac><mrow><msup><mrow><mo>(</mo><mfrac><msqrt><mn>11</mn></msqrt><msqrt><mn>2</mn></msqrt></mfrac><mo>)</mo></mrow><mn>2</mn></msup><mo>+</mo><msup><mrow><mo>(</mo><mfrac><msqrt><mn>2</mn></msqrt><mn>3</mn></mfrac><mo>)</mo></mrow><mn>2</mn></msup></mrow><mrow><msup><mrow><mo>(</mo><mfrac><msqrt><mn>2</mn></msqrt><msqrt><mn>11</mn></msqrt></mfrac><mo>)</mo></mrow><mn>2</mn></msup><mo>+</mo><msup><mrow><mo>(</mo><mfrac><mn>3</mn><msqrt><mn>3</mn></msqrt></mfrac><mo>)</mo></mrow><mn>2</mn></msup></mrow></mfrac><mo>&#160;</mo><mo>=</mo><mfrac><mn>11</mn><mn>9</mn></mfrac><mspace linebreak=\"newline\"/></math></p>",
                    solution_hi: "<p>13.(c)</p>\r\n<p><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>s</mi><mi>e</mi><mi>c</mi><mi>A</mi><mo>=</mo><mfrac><msqrt><mn>11</mn></msqrt><mn>3</mn></mfrac><mo>&#160;</mo><mo>=</mo><mfrac><mi>h</mi><mi>b</mi></mfrac><mspace linebreak=\"newline\"/><mi>P</mi><mo>=</mo><msqrt><mn>2</mn></msqrt><mspace linebreak=\"newline\"/><mfrac><mrow><msup><mrow><mo>(</mo><mfrac><msqrt><mn>11</mn></msqrt><msqrt><mn>2</mn></msqrt></mfrac><mo>)</mo></mrow><mn>2</mn></msup><mo>+</mo><msup><mrow><mo>(</mo><mfrac><msqrt><mn>2</mn></msqrt><mn>3</mn></mfrac><mo>)</mo></mrow><mn>2</mn></msup></mrow><mrow><msup><mrow><mo>(</mo><mfrac><msqrt><mn>2</mn></msqrt><msqrt><mn>11</mn></msqrt></mfrac><mo>)</mo></mrow><mn>2</mn></msup><mo>+</mo><msup><mrow><mo>(</mo><mfrac><mn>3</mn><msqrt><mn>3</mn></msqrt></mfrac><mo>)</mo></mrow><mn>2</mn></msup></mrow></mfrac><mo>&#160;</mo><mo>=</mo><mfrac><mn>11</mn><mn>9</mn></mfrac><mspace linebreak=\"newline\"/></math></span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "17",
                    question_en: "<p>14. If cosA, sinA, cotA are in geometric progression, then the value of <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>tan</mi><mn>6</mn></msup><mi>A</mi><mo>-</mo><msup><mi>tan</mi><mn>2</mn></msup><mi>A</mi></math><span style=\"font-family: Times New Roman;\">&nbsp;is:</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">SSC CHSL 13-10-2020 (Afternoon)</span></p>",
                    question_hi: "<p>14. If cosA, sinA, cotA are in geometric progression, then the value of <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>tan</mi><mn>6</mn></msup><mi>A</mi><mo>-</mo><msup><mi>tan</mi><mn>2</mn></msup><mi>A</mi></math><span style=\"font-family: Times New Roman;\">&nbsp;is:</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">SSC CHSL 13-10-2020 (Afternoon)</span></p>",
                    options_en: ["<p>1/2 <span style=\"font-family: Times New Roman;\"> </span></p>", "<p>3<span style=\"font-family: Times New Roman;\"> </span></p>", 
                                "<p>1/3</p>", "<p>1</p>"],
                    options_hi: ["<p>1/2 <span style=\"font-family: Times New Roman;\"> </span></p>", "<p>3<span style=\"font-family: Times New Roman;\"> </span></p>",
                                "<p>1/3</p>", "<p>1</p>"],
                    solution_en: "<p>14. (d)</p>\r\n<p><span style=\"font-family: Times New Roman;\">If CosA, SinA and CotA are in geometric progression,then</span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>s</mi><mi>i</mi><msup><mi>n</mi><mn>2</mn></msup><mi>A</mi><mo>=</mo><mi>C</mi><mi>o</mi><mi>s</mi><mi>A</mi><mo>&#215;</mo><mi>C</mi><mi>o</mi><mi>t</mi><mi>A</mi><mspace linebreak=\"newline\"/><msup><mrow><mi>s</mi><mi>i</mi><mi>n</mi></mrow><mn>2</mn></msup><mi>A</mi><mo>&#160;</mo><mo>=</mo><mi>C</mi><mi>o</mi><mi>s</mi><mi>A</mi><mo>&#215;</mo><mfrac><mrow><mi>C</mi><mi>o</mi><mi>s</mi><mi>A</mi></mrow><mrow><mi>S</mi><mi>i</mi><mi>n</mi><mi>A</mi></mrow></mfrac><mspace linebreak=\"newline\"/><mi>S</mi><mi>i</mi><msup><mi>n</mi><mn>3</mn></msup><mi>A</mi><mo>=</mo><mi>C</mi><mi>o</mi><msup><mi>s</mi><mn>2</mn></msup><mi>A</mi></math></p>\r\n<p><span style=\"font-family: Times New Roman;\">After dividing by&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mi>C</mi><mi>o</mi><mi>s</mi></mrow><mn>3</mn></msup><mi>A</mi></math></span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mrow><mi>S</mi><mi>i</mi><mi>n</mi></mrow><mn>3</mn></msup><mi>A</mi></mrow><mrow><msup><mrow><mi>C</mi><mi>o</mi><mi>s</mi></mrow><mn>3</mn></msup><mi>A</mi></mrow></mfrac><mo>=</mo><mfrac><mn>1</mn><mrow><mi>C</mi><mi>o</mi><mi>s</mi><mi>A</mi></mrow></mfrac><mspace linebreak=\"newline\"/><mi>t</mi><mi>a</mi><msup><mi>n</mi><mn>3</mn></msup><mi>A</mi><mo>=</mo><mi>S</mi><mi>e</mi><mi>c</mi><mi>A</mi><mspace linebreak=\"newline\"/><mi>t</mi><mi>a</mi><msup><mi>n</mi><mn>6</mn></msup><mi>A</mi><mo>-</mo><mi>t</mi><mi>a</mi><msup><mi>n</mi><mn>2</mn></msup><mi>A</mi><mo>=</mo><mi>S</mi><mi>e</mi><msup><mi>c</mi><mn>2</mn></msup><mi>A</mi><mo>-</mo><mi>t</mi><mi>a</mi><msup><mi>n</mi><mn>2</mn></msup><mi>A</mi><mo>=</mo><mn>1</mn><mspace linebreak=\"newline\"/><mspace linebreak=\"newline\"/></math></p>",
                    solution_hi: "<p>14. (d)</p>\r\n<p><span style=\"font-family: Times New Roman;\">If CosA, SinA and CotA are in geometric progression,then</span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>s</mi><mi>i</mi><msup><mi>n</mi><mn>2</mn></msup><mi>A</mi><mo>=</mo><mi>C</mi><mi>o</mi><mi>s</mi><mi>A</mi><mo>&#215;</mo><mi>C</mi><mi>o</mi><mi>t</mi><mi>A</mi><mspace linebreak=\"newline\"/><mi>s</mi><mi>i</mi><msup><mi>n</mi><mn>2</mn></msup><mi>A</mi><mo>=</mo><mi>C</mi><mi>o</mi><mi>s</mi><mi>A</mi><mo>&#215;</mo><mfrac><mrow><mi>C</mi><mi>o</mi><mi>s</mi><mi>A</mi></mrow><mrow><mi>S</mi><mi>i</mi><mi>n</mi><mi>A</mi></mrow></mfrac><mspace linebreak=\"newline\"/><msup><mrow><mi>S</mi><mi>i</mi><mi>n</mi></mrow><mn>3</mn></msup><mi>A</mi><mo>&#160;</mo><mo>=</mo><msup><mrow><mi>C</mi><mi>o</mi><mi>s</mi></mrow><mn>2</mn></msup><mi>A</mi></math></p>\r\n<p><span style=\"font-family: Times New Roman;\">After dividing by&nbsp;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mi>C</mi><mi>o</mi><mi>s</mi></mrow><mn>3</mn></msup><mi>A</mi></math></span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mrow><mi>S</mi><mi>i</mi><mi>n</mi></mrow><mn>3</mn></msup><mi>A</mi></mrow><mrow><msup><mrow><mi>C</mi><mi>o</mi><mi>s</mi></mrow><mn>3</mn></msup><mi>A</mi></mrow></mfrac><mo>&#160;</mo><mo>=</mo><mfrac><mn>1</mn><mrow><mi>C</mi><mi>o</mi><mi>s</mi><mi>A</mi></mrow></mfrac><mspace linebreak=\"newline\"/><mi>t</mi><mi>a</mi><msup><mi>n</mi><mn>3</mn></msup><mi>A</mi><mo>=</mo><mi>S</mi><mi>e</mi><mi>c</mi><mi>A</mi><mspace linebreak=\"newline\"/><mi>t</mi><mi>a</mi><msup><mi>n</mi><mn>6</mn></msup><mi>A</mi><mo>-</mo><mi>t</mi><mi>a</mi><msup><mi>n</mi><mn>2</mn></msup><mi>A</mi><mo>=</mo><mi>S</mi><mi>e</mi><msup><mi>c</mi><mn>2</mn></msup><mi>A</mi><mo>-</mo><mi>t</mi><mi>a</mi><msup><mi>n</mi><mn>2</mn></msup><mi>A</mi><mo>=</mo><mn>1</mn><mspace linebreak=\"newline\"/><mspace linebreak=\"newline\"/></math></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "17",
                    question_en: "<p>15. If sinx - cosx = 0, then the value of <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><msup><mi>sin</mi><mn>3</mn></msup><mi>x</mi><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><msup><mi>cos</mi><mn>3</mn></msup><mi>x</mi><mo>)</mo></math><span style=\"font-family: Times New Roman;\">&nbsp;is:</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">SSC CHSL 13-10-2020 (Evening Shift)</span></p>",
                    question_hi: "<p>15. <span style=\"font-family: Baloo;\">यदि</span><span style=\"font-family: Times New Roman;\"> sinx - cosx = 0,</span><span style=\"font-family: Baloo;\">तो </span><span style=\"font-family: Times New Roman;\">(<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>sin</mi><mn>3</mn></msup><mi>x</mi><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><msup><mi>cos</mi><mn>3</mn></msup><mi>x</mi></math></span><span style=\"font-family: Times New Roman;\">)</span><span style=\"font-family: Baloo;\"> मान होगा :</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">CHSL 13-10-2020 (Evening Shift)</span></p>",
                    options_en: ["<p>0</p>", "<p>2</p>", 
                                "<p>1</p>", "<p>4</p>"],
                    options_hi: ["<p>0</p>", "<p>2</p>",
                                "<p>1</p>", "<p>4</p>"],
                    solution_en: "<p>15.(a)</p>\r\n<p><span style=\"font-family: Times New Roman;\">Short-Trick: Put x=<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>45</mn><mn>0</mn></msup></math></span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>s</mi><mi>i</mi><msup><mi>n</mi><mn>3</mn></msup><mi>x</mi><mo>-</mo><mi>c</mi><mi>o</mi><msup><mi>s</mi><mn>3</mn></msup><mi>x</mi><mspace linebreak=\"newline\"/><mo>(</mo><msup><mrow><mfrac><mn>1</mn><msqrt><mn>2</mn></msqrt></mfrac><mo>)</mo></mrow><mn>3</mn></msup><mo>-</mo><mo>(</mo><msup><mrow><mfrac><mn>1</mn><msqrt><mn>2</mn></msqrt></mfrac><mo>)</mo></mrow><mn>3</mn></msup><mo>&#160;</mo><mo>=</mo><mn>0</mn><mspace linebreak=\"newline\"/></math></p>",
                    solution_hi: "<p>15.(a)</p>\r\n<p><span style=\"font-family: Times New Roman;\">Short-Trick: Put x=<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>45</mn><mn>0</mn></msup></math></span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>s</mi><mi>i</mi><msup><mi>n</mi><mn>3</mn></msup><mi>x</mi><mo>-</mo><mi>c</mi><mi>o</mi><msup><mi>s</mi><mn>3</mn></msup><mi>x</mi><mspace linebreak=\"newline\"/><mo>(</mo><msup><mrow><mfrac><mn>1</mn><msqrt><mn>2</mn></msqrt></mfrac><mo>)</mo></mrow><mn>3</mn></msup><mo>&#160;</mo><mo>-</mo><mo>(</mo><msup><mrow><mfrac><mn>1</mn><msqrt><mn>2</mn></msqrt></mfrac><mo>)</mo></mrow><mn>3</mn></msup><mo>&#160;</mo><mo>=</mo><mn>0</mn><mspace linebreak=\"newline\"/><mspace linebreak=\"newline\"/></math></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "16",
                    section: "17",
                    question_en: "<p>16.&nbsp; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>c</mi><mi>o</mi><mi>t</mi><mi>x</mi></mrow><mrow><mn>1</mn><mo>+</mo><mi>c</mi><mi>o</mi><mi>s</mi><mi>e</mi><mi>c</mi><mi>x</mi></mrow></mfrac><mo>&#160;</mo><mo>+</mo><mfrac><mrow><mn>1</mn><mo>+</mo><mi>c</mi><mi>o</mi><mi>s</mi><mi>e</mi><mi>c</mi><mi>x</mi></mrow><mrow><mi>c</mi><mi>o</mi><mi>t</mi><mi>x</mi></mrow></mfrac></math><span style=\"font-family: Times New Roman;\">is equal to</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">SSC CHSL 13-10-2020 (Evening Shift)</span></p>",
                    question_hi: "<p>16.&nbsp; <span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>c</mi><mi>o</mi><mi>t</mi><mi>x</mi></mrow><mrow><mn>1</mn><mo>+</mo><mi>c</mi><mi>o</mi><mi>s</mi><mi>e</mi><mi>c</mi><mi>x</mi></mrow></mfrac><mo>+</mo><mfrac><mrow><mn>1</mn><mo>+</mo><mi>c</mi><mi>o</mi><mi>s</mi><mi>e</mi><mi>c</mi><mi>x</mi></mrow><mrow><mi>c</mi><mi>o</mi><mi>t</mi><mi>x</mi></mrow></mfrac></math></span><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Baloo;\">का मान है :</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">CHSL 13-10-2020 (Evening Shift)</span></p>",
                    options_en: ["<p>2 secx</p>", "<p>2cosx</p>", 
                                "<p>2 <span style=\"font-family: Times New Roman;\">cosecx</span></p>", "<p>2sinx</p>"],
                    options_hi: ["<p>2 secx</p>", "<p>2cosx</p>",
                                "<p>2 <span style=\"font-family: Times New Roman;\">cosecx</span></p>", "<p>2sinx</p>"],
                    solution_en: "<p>16.(a)</p>\r\n<p><span style=\"font-family: Times New Roman;\">Short-Trick:</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Put <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#952;</mi></math></span><span style=\"font-family: Times New Roman;\">=30&deg;</span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msqrt><mn>3</mn></msqrt><mrow><mn>1</mn><mo>+</mo><mn>2</mn></mrow></mfrac><mo>&#160;</mo><mo>+</mo><mfrac><mrow><mn>1</mn><mo>+</mo><mn>2</mn></mrow><msqrt><mn>3</mn></msqrt></mfrac><mo>&#160;</mo><mo>=</mo><mfrac><mn>4</mn><msqrt><mn>3</mn></msqrt></mfrac></math></p>\r\n<p><span style=\"font-family: Times New Roman;\">Put the value of <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#952;</mi></math></span><span style=\"font-family: Times New Roman;\">, in option (a) which satisfies.</span></p>",
                    solution_hi: "<p>16.(a)</p>\r\n<p><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#952;</mi></math>=30&deg; </span><span style=\"font-family: Baloo;\">रखें।</span><span style=\"font-family: Times New Roman;\"> </span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msqrt><mn>3</mn></msqrt><mrow><mn>1</mn><mo>+</mo><mn>2</mn></mrow></mfrac><mo>+</mo><mfrac><mrow><mn>1</mn><mo>+</mo><mn>2</mn></mrow><msqrt><mn>3</mn></msqrt></mfrac><mo>&#160;</mo><mo>=</mo><mfrac><mn>4</mn><msqrt><mn>3</mn></msqrt></mfrac></math></p>\r\n<p><span style=\"font-family: Baloo;\">सभी विकल्पों में</span><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Baloo;\">का मान डालें, केवल विकल्प</span><span style=\"font-family: Times New Roman;\"> (a) </span><span style=\"font-family: Baloo;\">संतुष्ट करता है।</span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "17",
                    section: "17",
                    question_en: "<p>17. <span style=\"font-family: Times New Roman;\">If tanx =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>m</mi><mi>n</mi></mfrac></math> </span><span style=\"font-family: Times New Roman;\">and <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>0</mn><mo>&#176;</mo><mo>&#160;</mo><mo>&#8804;</mo><mi>x</mi><mo>&#160;</mo><mo>&#8804;</mo><mo>&#160;</mo><mn>90</mn><mo>&#176;</mo></math></span><span style=\"font-family: Times New Roman;\">, then the value of (sin x + cos x) is:</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">SSC CHSL 13-10-2020 (Evening Shift)</span></p>",
                    question_hi: "<p>17. <span style=\"font-family: Baloo;\">यदि</span><span style=\"font-family: Times New Roman;\"> tanx =</span><span style=\"font-family: Times New Roman;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>m</mi><mi>n</mi></mfrac></math></span><span style=\"font-family: Baloo;\">और</span><span style=\"font-family: Times New Roman;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>0</mn><mo>&#176;</mo><mo>&#160;</mo><mo>&#8804;</mo><mo>&#160;</mo><mi>x</mi><mo>&#8804;</mo><mo>&#160;</mo><mn>90</mn><mo>&#176;</mo></math></span><span style=\"font-family: Times New Roman;\">,(sin x + cos x)</span><span style=\"font-family: Baloo;\"> का मान है:</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">CHSL 13-10-2020 (Evening Shift)</span></p>",
                    options_en: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msqrt><msup><mi>m</mi><mn>2</mn></msup><mo>-</mo><msup><mi>n</mi><mn>2</mn></msup></msqrt></mfrac></math></p>", "<p><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msqrt><msup><mi>m</mi><mn>2</mn></msup><mo>+</mo><msup><mi>n</mi><mn>2</mn></msup></msqrt></mfrac></math> </span></p>", 
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>m</mi><mo>+</mo><mi>n</mi></mrow><msqrt><msup><mi>m</mi><mn>2</mn></msup><mo>+</mo><msup><mi>n</mi><mn>2</mn></msup></msqrt></mfrac></math></p>", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mi>m</mi><mn>2</mn></msup><mo>-</mo><msup><mi>n</mi><mn>2</mn></msup></msqrt></math></p>"],
                    options_hi: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msqrt><msup><mi>m</mi><mn>2</mn></msup><mo>-</mo><msup><mi>n</mi><mn>2</mn></msup></msqrt></mfrac></math></p>", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msqrt><msup><mi>m</mi><mn>2</mn></msup><mo>+</mo><msup><mi>n</mi><mn>2</mn></msup></msqrt></mfrac></math><span style=\"font-family: Times New Roman;\"> </span></p>",
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>m</mi><mo>+</mo><mi>n</mi></mrow><msqrt><msup><mi>m</mi><mn>2</mn></msup><mo>+</mo><msup><mi>n</mi><mn>2</mn></msup></msqrt></mfrac></math></p>", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mi>m</mi><mn>2</mn></msup><mo>-</mo><msup><mi>n</mi><mn>2</mn></msup></msqrt></math></p>"],
                    solution_en: "<p>17.(c)</p>\r\n<p><span style=\"font-family: Times New Roman;\">tanx =</span><span style=\"font-family: Times New Roman;\">&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>m</mi><mi>n</mi></mfrac><mo>&#160;</mo><mo>=</mo><mfrac><mi>p</mi><mi>b</mi></mfrac></math></span></p>\r\n<p><span style=\"font-family: Times New Roman;\">h=<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mi>m</mi><mn>2</mn></msup><mo>+</mo><msup><mi>n</mi><mn>2</mn></msup></msqrt></math></span></p>\r\n<p><span style=\"font-family: Times New Roman;\">sin x + cos x =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>m</mi><mo>+</mo><mi>n</mi></mrow><msqrt><msup><mi>m</mi><mn>2</mn></msup><mo>+</mo><msup><mi>n</mi><mn>2</mn></msup></msqrt></mfrac></math></span></p>",
                    solution_hi: "<p>17.(c)</p>\r\n<p><span style=\"font-family: Times New Roman;\">tanx =</span><span style=\"font-family: Times New Roman;\">&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>m</mi><mi>n</mi></mfrac><mo>=</mo><mfrac><mi>p</mi><mi>b</mi></mfrac></math></span></p>\r\n<p><span style=\"font-family: Times New Roman;\">h=<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mi>m</mi><mn>2</mn></msup><mo>+</mo><msup><mi>n</mi><mn>2</mn></msup></msqrt></math></span></p>\r\n<p><span style=\"font-family: Times New Roman;\">sin x + cos x =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>m</mi><mo>+</mo><mi>n</mi></mrow><msqrt><msup><mi>m</mi><mn>2</mn></msup><mo>+</mo><msup><mi>n</mi><mn>2</mn></msup></msqrt></mfrac></math></span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "18",
                    section: "17",
                    question_en: "<p>18. If cos x =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>24</mn><mn>25</mn></mfrac><mo>,</mo><mi>&#160;</mi><mn>0</mn><mo>&#8804;</mo><mi>x</mi><mo>&#8804;</mo><mn>90</mn><mo>&#176;</mo><mo>,</mo></math> <span style=\"font-family: Times New Roman;\"> then the value of cot x + cosec x is:</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">SSC CHSL 14-10-2020 (Morning shift) </span></p>",
                    question_hi: "<p>18. <span style=\"font-family: Baloo;\">यदि </span><span style=\"font-family: Times New Roman;\">cos x = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>24</mn><mn>25</mn></mfrac></math>&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#160;</mi><mn>0</mn><mo>&#8804;</mo><mi>x</mi><mo>&#8804;</mo><mn>90</mn><mo>&#176;</mo><mo>,</mo></math> &nbsp;</span><span style=\"font-family: Times New Roman;\">cot x + cosec x</span><span style=\"font-family: Baloo;\"> का मान है : CHSL 14-10-2020 (Morning shift) </span></p>",
                    options_en: ["<p>0</p>", "<p>1</p>", 
                                "<p>7</p>", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>2</mn></mfrac></math></p>"],
                    options_hi: ["<p>0</p>", "<p>1</p>",
                                "<p>7</p>", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>2</mn></mfrac></math></p>"],
                    solution_en: "<p>18.(c)</p>\r\n<p><span style=\"font-family: Times New Roman;\">cosx =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>24</mn><mn>25</mn></mfrac><mo>=</mo><mfrac><mi>B</mi><mi>H</mi></mfrac></math></span></p>\r\n<p><span style=\"font-family: Times New Roman;\">P=<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mn>25</mn><mn>2</mn></msup><mo>-</mo><msup><mn>24</mn><mn>2</mn></msup></msqrt></math> </span><span style=\"font-family: Times New Roman;\">=7</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">cosx+cosecx =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>B</mi><mi>P</mi></mfrac><mo>+</mo><mo>&#160;</mo><mfrac><mi>H</mi><mi>P</mi></mfrac><mo>=</mo><mfrac><mrow><mn>24</mn><mo>+</mo><mn>25</mn></mrow><mn>7</mn></mfrac><mo>=</mo><mn>7</mn></math></span></p>",
                    solution_hi: "<p>18.(c)</p>\r\n<p><span style=\"font-family: Times New Roman;\">cosx =</span><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>24</mn><mn>25</mn></mfrac><mo>=</mo><mfrac><mi>B</mi><mi>H</mi></mfrac></math></span></p>\r\n<p><span style=\"font-family: Times New Roman;\">P= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mn>25</mn><mn>2</mn></msup><mo>-</mo><msup><mn>24</mn><mn>2</mn></msup></msqrt></math></span><span style=\"font-family: Times New Roman;\">=7</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">cosx+cosecx =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>B</mi><mi>P</mi></mfrac><mo>+</mo><mfrac><mi>H</mi><mi>P</mi></mfrac><mo>&#160;</mo><mo>=</mo><mfrac><mrow><mn>24</mn><mo>+</mo><mn>25</mn></mrow><mn>7</mn></mfrac><mo>&#160;</mo><mo>=</mo><mn>7</mn></math></span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "19",
                    section: "17",
                    question_en: "<p>19. If tanx =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>2</mn></mfrac></math><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Times New Roman;\">, then the value of <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>3</mn><mi>s</mi><mi>i</mi><mi>n</mi><mi>x</mi><mo>+</mo><mn>2</mn><mi>c</mi><mi>o</mi><mi>s</mi><mi>x</mi></mrow><mrow><mn>3</mn><mi>s</mi><mi>i</mi><mi>n</mi><mi>x</mi><mo>-</mo><mn>2</mn><mi>c</mi><mi>o</mi><mi>s</mi><mi>x</mi></mrow></mfrac></math></span><span style=\"font-family: Times New Roman;\">is:</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">SSC CHSL 14-10-2020 (Morning shift)</span></p>",
                    question_hi: "<p>19. <span style=\"font-family: Baloo;\">यदि</span><span style=\"font-family: Times New Roman;\"> tanx =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>2</mn></mfrac></math></span><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Times New Roman;\">, <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>3</mn><mi>s</mi><mi>i</mi><mi>n</mi><mi>x</mi><mo>+</mo><mn>2</mn><mi>c</mi><mi>o</mi><mi>s</mi><mi>x</mi></mrow><mrow><mn>3</mn><mi>s</mi><mi>i</mi><mi>n</mi><mi>x</mi><mo>-</mo><mn>2</mn><mi>c</mi><mi>o</mi><mi>s</mi><mi>x</mi></mrow></mfrac></math></span><span style=\"font-family: Baloo;\">का मान है:</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">CHSL 14-10-2020 (Morning shift)</span></p>",
                    options_en: ["<p><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>5</mn></mfrac></math> </span></p>", "<p><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>13</mn></mfrac></math> </span></p>", 
                                "<p><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>13</mn><mn>5</mn></mfrac></math> </span></p>", "<p>5</p>"],
                    options_hi: ["<p><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>5</mn></mfrac></math> </span></p>", "<p><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>13</mn></mfrac></math> </span></p>",
                                "<p><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>13</mn><mn>5</mn></mfrac></math> </span></p>", "<p>5</p>"],
                    solution_en: "<p>19.(c)</p>\r\n<p><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>3</mn><mi>s</mi><mi>i</mi><mi>n</mi><mi>x</mi><mo>+</mo><mn>2</mn><mi>c</mi><mi>o</mi><mi>s</mi><mi>x</mi></mrow><mrow><mn>3</mn><mi>s</mi><mi>i</mi><mi>n</mi><mi>x</mi><mo>-</mo><mn>2</mn><mi>c</mi><mi>o</mi><mi>s</mi><mi>x</mi></mrow></mfrac></math>, on dividing by cosx</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">=<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>3</mn><mi>t</mi><mi>a</mi><mi>n</mi><mi>&#160;</mi><mi>x</mi><mo>+</mo><mn>2</mn></mrow><mrow><mn>3</mn><mi>t</mi><mi>a</mi><mi>n</mi><mi>&#160;</mi><mi>x</mi><mo>-</mo><mn>2</mn></mrow></mfrac><mo>&#160;</mo><mo>=</mo><mfrac><mrow><mfrac><mn>9</mn><mn>2</mn></mfrac><mo>+</mo><mn>2</mn></mrow><mrow><mfrac><mn>9</mn><mn>2</mn></mfrac><mo>-</mo><mn>2</mn></mrow></mfrac><mo>&#160;</mo><mo>=</mo><mfrac><mn>13</mn><mn>5</mn></mfrac></math></span><span style=\"font-family: Times New Roman;\"> </span></p>",
                    solution_hi: "<p>19.(c)</p>\r\n<p><span style=\"font-family: Times New Roman;\">cosx </span><span style=\"font-family: Baloo;\">द्वारा विभाजित करने पर</span></p>\r\n<p><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>3</mn><mi>s</mi><mi>i</mi><mi>n</mi><mi>x</mi><mo>+</mo><mn>2</mn><mi>c</mi><mi>o</mi><mi>s</mi><mi>x</mi></mrow><mrow><mn>3</mn><mi>s</mi><mi>i</mi><mi>n</mi><mi>x</mi><mo>-</mo><mn>2</mn><mi>c</mi><mi>o</mi><mi>s</mi><mi>x</mi></mrow></mfrac><mspace linebreak=\"newline\"/><mfrac><mrow><mn>3</mn><mi>t</mi><mi>a</mi><mi>n</mi><mi>&#160;</mi><mi>x</mi><mo>+</mo><mn>2</mn></mrow><mrow><mn>3</mn><mi>t</mi><mi>a</mi><mi>n</mi><mi>&#160;</mi><mi>x</mi><mo>-</mo><mn>2</mn></mrow></mfrac><mspace linebreak=\"newline\"/><mfrac><mrow><mfrac><mn>9</mn><mn>2</mn></mfrac><mo>+</mo><mn>2</mn></mrow><mrow><mfrac><mn>9</mn><mn>2</mn></mfrac><mo>-</mo><mn>2</mn></mrow></mfrac><mo>=</mo><mfrac><mn>13</mn><mn>5</mn></mfrac><mspace linebreak=\"newline\"/></math></span><span style=\"font-family: Times New Roman;\"> </span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "20",
                    section: "17",
                    question_en: "<p>20. If sec<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#952;</mi></math>&nbsp; <span style=\"font-family: Times New Roman;\">and&nbsp; sin<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#952;</mi></math> </span><span style=\"font-family: Times New Roman;\">are the roots of the equation <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>6</mn></msqrt><msup><mi>x</mi><mn>2</mn></msup><mo>-</mo><mi>k</mi><mi>x</mi><mo>+</mo><msqrt><mn>6</mn></msqrt><mo>=</mo><mn>0</mn></math></span><span style=\"font-family: Times New Roman;\">, then the value of k is:</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">SSC CHSL 14-10-2020 (Morning</span><span style=\"font-family: Times New Roman;\">)</span></p>",
                    question_hi: "<p>20. If <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>s</mi><mi>e</mi><mi>c</mi><mi>&#952;</mi><mo>&#160;</mo><mo>,</mo><mi>sin</mi><mi>&#952;</mi><mo>&#160;</mo><mo>&#160;</mo><mo>(</mo><mn>0</mn><mo>&#60;</mo><mi>&#952;</mi><mo>&#60;</mo><mn>90</mn><mo>)</mo><mo>&#160;</mo></math><span style=\"font-family: Times New Roman;\">&nbsp;</span><span style=\"font-family: Times New Roman;\">are the roots of the equation <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>6</mn></msqrt><msup><mi>x</mi><mn>2</mn></msup><mo>-</mo><mi>k</mi><mi>x</mi><mo>+</mo><msqrt><mn>6</mn></msqrt><mo>=</mo><mn>0</mn></math></span><span style=\"font-family: Times New Roman;\">, then the value of k is:</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">SSC CHSL 14-10-2020 (Morning</span><span style=\"font-family: Times New Roman;\">)</span></p>",
                    options_en: ["<p><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math> </span></p>", "<p>3<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>2</mn></msqrt></math><span style=\"font-family: Times New Roman;\"> </span></p>", 
                                "<p>2<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Times New Roman;\"> </span></p>", "<p>3<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math></p>"],
                    options_hi: ["<p><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math> </span></p>", "<p>3<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>2</mn></msqrt></math><span style=\"font-family: Times New Roman;\"> </span></p>",
                                "<p>2<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Times New Roman;\"> </span></p>", "<p>3<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math></p>"],
                    solution_en: "<p>20.(d)</p>\r\n<p><span style=\"font-family: Times New Roman;\">sum of roots = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>S</mi><mi>e</mi><mi>c</mi><mi>&#952;</mi><mo>+</mo><mi>S</mi><mi>i</mi><mi>n</mi><mi>&#952;</mi><mo>=</mo><mfrac><mrow><mo>-</mo><mi>b</mi></mrow><mi>a</mi></mfrac><mo>&#160;</mo><mo>&#160;</mo><mo>=</mo><mfrac><mi>k</mi><msqrt><mn>6</mn></msqrt></mfrac></math></span><span style=\"font-family: Times New Roman;\"> </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">product of roots =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>S</mi><mi>e</mi><mi>c</mi><mi>&#952;</mi><mo>&#215;</mo><mi>S</mi><mi>i</mi><mi>n</mi><mi>&#952;</mi></math></span></p>\r\n<p><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>tan</mi><mi>&#952;</mi><mo>=</mo><mo>&#160;</mo><mfrac><mi>c</mi><mi>a</mi></mfrac><mo>=</mo><mo>&#160;</mo><mfrac><mrow><mo>&#8730;</mo><mn>6</mn></mrow><mrow><mo>&#8730;</mo><mn>6</mn></mrow></mfrac><mo>=</mo><mo>&#160;</mo><mn>1</mn></math></span></p>\r\n<p><span style=\"font-family: Times New Roman;\">tan<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#952;</mi></math></span><span style=\"font-family: Times New Roman;\">= 1</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">it means, <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#952;</mi></math></span><span style=\"font-family: Times New Roman;\">= 45&deg;</span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>S</mi><mi>e</mi><mi>c</mi><mi>&#952;</mi><mo>+</mo><mi>S</mi><mi>i</mi><mi>n</mi><mi>&#952;</mi><mo>=</mo><mo>&#160;</mo><mfrac><mi>k</mi><mrow><mo>&#8730;</mo><mn>6</mn></mrow></mfrac><mspace linebreak=\"newline\"/><msqrt><mn>2</mn></msqrt><mo>+</mo><mfrac><mn>1</mn><msqrt><mn>2</mn></msqrt></mfrac><mo>&#160;</mo><mo>=</mo><mfrac><mi>k</mi><mrow><mo>&#8730;</mo><mn>6</mn></mrow></mfrac><mspace linebreak=\"newline\"/><mfrac><mn>3</mn><mrow><mo>&#8730;</mo><mn>2</mn></mrow></mfrac><mo>=</mo><mfrac><mrow><mo>&#160;</mo><mi>k</mi></mrow><mrow><mo>&#8730;</mo><mn>6</mn></mrow></mfrac><mspace linebreak=\"newline\"/><mi>k</mi><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>3</mn><mo>&#8730;</mo><mn>3</mn><mspace linebreak=\"newline\"/><mspace linebreak=\"newline\"/><mspace linebreak=\"newline\"/></math></p>",
                    solution_hi: "<p>20.(d)</p>\r\n<p><span style=\"font-family: Times New Roman;\">sum of roots =&nbsp;</span></p>\r\n<p><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#160;</mo><mi>S</mi><mi>e</mi><mi>c</mi><mi>&#952;</mi><mo>+</mo><mi>S</mi><mi>i</mi><mi>n</mi><mi>&#952;</mi><mo>=</mo><mfrac><mrow><mo>(</mo><mo>-</mo><mi>b</mi><mo>)</mo></mrow><mi>a</mi></mfrac><mo>=</mo><mfrac><mrow><mo>&#160;</mo><mi>k</mi></mrow><mrow><mo>&#8730;</mo><mn>6</mn></mrow></mfrac><mspace linebreak=\"newline\"/></math></span><span style=\"font-family: Times New Roman;\"> </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">product of roots =&nbsp;</span></p>\r\n<p><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>=</mo><mo>&#160;</mo><mo>&#160;</mo><mi>S</mi><mi>e</mi><mi>c</mi><mi>&#952;</mi><mo>&#215;</mo><mi>S</mi><mi>i</mi><mi>n</mi><mi>&#952;</mi><mo>=</mo><mo>&#160;</mo><mi>tan</mi><mi>&#952;</mi><mo>=</mo><mfrac><mrow><mo>&#160;</mo><mi>c</mi></mrow><mi>a</mi></mfrac><mo>=</mo><mo>&#160;</mo><mfrac><msqrt><mn>6</mn></msqrt><msqrt><mn>6</mn></msqrt></mfrac><mo>&#160;</mo><mo>=</mo><mn>1</mn></math></span></p>\r\n<p><span style=\"font-family: Times New Roman;\">tan<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#952;</mi></math></span><span style=\"font-family: Times New Roman;\">= 1</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">it means, <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#952;</mi></math></span><span style=\"font-family: Times New Roman;\">= 45&deg;</span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>S</mi><mi>e</mi><mi>c</mi><mi>&#952;</mi><mo>+</mo><mi>S</mi><mi>i</mi><mi>n</mi><mi>&#952;</mi><mo>=</mo><mo>&#160;</mo><mfrac><mi>k</mi><msqrt><mn>6</mn></msqrt></mfrac><mspace linebreak=\"newline\"/><msqrt><mn>2</mn></msqrt><mo>+</mo><mfrac><mn>1</mn><msqrt><mn>2</mn></msqrt></mfrac><mo>=</mo><mo>&#160;</mo><mo>&#160;</mo><mfrac><mi>k</mi><msqrt><mn>6</mn></msqrt></mfrac><mspace linebreak=\"newline\"/><mfrac><mn>3</mn><msqrt><mn>2</mn></msqrt></mfrac><mo>=</mo><mo>&#160;</mo><mfrac><mi>k</mi><msqrt><mn>6</mn></msqrt></mfrac><mspace linebreak=\"newline\"/><mi>k</mi><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>3</mn><mo>&#8730;</mo><mn>3</mn></math></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "21",
                    section: "17",
                    question_en: "<p>21. Which of the following values suits for A to make the equation <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>A</mi><mi>T</mi><mi>a</mi><mi>n</mi><mn>62</mn><mo>&#176;</mo><mi>S</mi><mi>e</mi><mi>c</mi><mn>28</mn><mo>&#176;</mo><mi>C</mi><mi>o</mi><mi>t</mi><mi>&#160;</mi><mn>38</mn><mo>&#176;</mo></mrow><mrow><mi>C</mi><mi>o</mi><mi>s</mi><mi>e</mi><mi>c</mi><mn>62</mn><mo>&#176;</mo><mi>T</mi><mi>a</mi><mi>n</mi><mn>11</mn><mo>&#176;</mo></mrow></mfrac><mo>=</mo><mn>1</mn></math><span style=\"font-family: Times New Roman;\">true</span><span style=\"font-family: Times New Roman;\"> ?</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">SSC CHSL 14-10-2020 (Afternoon shift)</span></p>",
                    question_hi: "<p>21. <span style=\"font-family: Baloo;\">A के लिए निम्नलिखित में से कौन सा मान समीकरण <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>A</mi><mi>T</mi><mi>a</mi><mi>n</mi><mn>62</mn><mo>&#176;</mo><mi>S</mi><mi>e</mi><mi>c</mi><mn>28</mn><mo>&#176;</mo><mi>C</mi><mi>o</mi><mi>t</mi><mi>&#160;</mi><mn>38</mn><mo>&#176;</mo></mrow><mrow><mi>C</mi><mi>o</mi><mi>s</mi><mi>e</mi><mi>c</mi><mn>62</mn><mo>&#176;</mo><mi>T</mi><mi>a</mi><mi>n</mi><mn>11</mn><mo>&#176;</mo></mrow></mfrac><mo>=</mo></math></span><span style=\"font-family: Times New Roman;\">&nbsp;1 </span><span style=\"font-family: Baloo;\">को सही बनाता है?</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">CHSL 14-10-2020 (Afternoon shift)</span></p>",
                    options_en: ["<p><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>T</mi><mi>a</mi><mi>n</mi><mn>38</mn><mo>&#176;</mo></mrow><mrow><mi>T</mi><mi>a</mi><mi>n</mi><mn>79</mn><mo>&#176;</mo><mi>T</mi><mi>a</mi><mi>n</mi><mn>28</mn><mo>&#176;</mo></mrow></mfrac></math> </span></p>", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>T</mi><mi>a</mi><mi>n</mi><mn>28</mn><mo>&#176;</mo><mi>T</mi><mi>a</mi><mi>n</mi><mn>79</mn><mo>&#176;</mo></mrow><mrow><mi>T</mi><mi>a</mi><mi>n</mi><mn>38</mn><mo>&#176;</mo></mrow></mfrac></math></p>", 
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>T</mi><mi>a</mi><mi>n</mi><mn>28</mn><mo>&#176;</mo><mi>T</mi><mi>a</mi><mi>n</mi><mn>38</mn><mo>&#176;</mo></mrow><mrow><mi>T</mi><mi>a</mi><mi>n</mi><mn>79</mn><mo>&#176;</mo></mrow></mfrac></math></p>", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>T</mi><mi>a</mi><mi>n</mi><mn>38</mn><mo>&#176;</mo><mi>T</mi><mi>a</mi><mi>n</mi><mi>&#160;</mi><mn>79</mn><mo>&#176;</mo></mrow><mrow><mi>T</mi><mi>a</mi><mi>n</mi><mn>28</mn><mo>&#176;</mo></mrow></mfrac></math></p>"],
                    options_hi: ["<p><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>T</mi><mi>a</mi><mi>n</mi><mn>38</mn><mo>&#176;</mo></mrow><mrow><mi>T</mi><mi>a</mi><mi>n</mi><mn>79</mn><mo>&#176;</mo><mi>T</mi><mi>a</mi><mi>n</mi><mn>28</mn><mo>&#176;</mo></mrow></mfrac></math> </span></p>", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>T</mi><mi>a</mi><mi>n</mi><mn>28</mn><mo>&#176;</mo><mi>T</mi><mi>a</mi><mi>n</mi><mn>79</mn><mo>&#176;</mo></mrow><mrow><mi>T</mi><mi>a</mi><mi>n</mi><mn>38</mn><mo>&#176;</mo></mrow></mfrac></math></p>",
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>T</mi><mi>a</mi><mi>n</mi><mn>28</mn><mo>&#176;</mo><mi>T</mi><mi>a</mi><mi>n</mi><mn>38</mn><mo>&#176;</mo></mrow><mrow><mi>T</mi><mi>a</mi><mi>n</mi><mn>79</mn><mo>&#176;</mo></mrow></mfrac></math></p>", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>T</mi><mi>a</mi><mi>n</mi><mn>38</mn><mo>&#176;</mo><mi>T</mi><mi>a</mi><mi>n</mi><mi>&#160;</mi><mn>79</mn><mo>&#176;</mo></mrow><mrow><mi>T</mi><mi>a</mi><mi>n</mi><mn>28</mn><mo>&#176;</mo></mrow></mfrac></math></p>"],
                    solution_en: "<p>21.(c)</p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>A</mi><mi>T</mi><mi>a</mi><mi>n</mi><mn>62</mn><mo>&#176;</mo><mi>S</mi><mi>e</mi><mi>c</mi><mn>28</mn><mo>&#176;</mo><mi>C</mi><mi>o</mi><mi>t</mi><mi>&#160;</mi><mn>38</mn><mo>&#176;</mo></mrow><mrow><mi>C</mi><mi>o</mi><mi>s</mi><mi>e</mi><mi>c</mi><mn>62</mn><mo>&#176;</mo><mi>T</mi><mi>a</mi><mi>n</mi><mn>11</mn><mo>&#176;</mo></mrow></mfrac><mo>&#160;</mo><mo>=</mo><mn>1</mn><mspace linebreak=\"newline\"/><mi>A</mi><mo>=</mo><mfrac><mrow><mi>C</mi><mi>o</mi><mi>s</mi><mi>e</mi><mi>c</mi><mn>62</mn><mo>&#176;</mo><mi>T</mi><mi>a</mi><mi>n</mi><mn>11</mn><mo>&#176;</mo></mrow><mrow><mi>T</mi><mi>a</mi><mi>n</mi><mn>62</mn><mo>&#176;</mo><mi>S</mi><mi>e</mi><mi>c</mi><mn>28</mn><mo>&#176;</mo><mi>C</mi><mi>o</mi><mi>t</mi><mi>&#160;</mi><mn>38</mn><mo>&#176;</mo></mrow></mfrac><mspace linebreak=\"newline\"/><mfrac><mrow><mi>s</mi><mi>e</mi><mi>c</mi><mi>&#160;</mi><mn>28</mn><mo>&#176;</mo><mi>c</mi><mi>o</mi><mi>t</mi><mi>&#160;</mi><mn>79</mn><mo>&#176;</mo></mrow><mrow><mi>t</mi><mi>a</mi><mi>n</mi><mi>&#160;</mi><mn>62</mn><mo>&#176;</mo><mi>&#160;</mi><mi>s</mi><mi>e</mi><mi>c</mi><mi>&#160;</mi><mn>28</mn><mo>&#176;</mo><mi>&#160;</mi><mi>c</mi><mi>o</mi><mi>t</mi><mi>&#160;</mi><mn>38</mn><mo>&#176;</mo></mrow></mfrac><mspace linebreak=\"newline\"/><mo>=</mo><mfrac><mrow><mi>t</mi><mi>a</mi><mi>n</mi><mi>&#160;</mi><mn>28</mn><mo>&#176;</mo><mi>&#160;</mi><mi>t</mi><mi>a</mi><mi>n</mi><mi>&#160;</mi><mn>38</mn><mo>&#176;</mo></mrow><mrow><mi>t</mi><mi>a</mi><mi>n</mi><mn>79</mn><mo>&#176;</mo></mrow></mfrac><mspace linebreak=\"newline\"/><mspace linebreak=\"newline\"/></math></p>",
                    solution_hi: "<p>21.(c)</p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>A</mi><mi>T</mi><mi>a</mi><mi>n</mi><mn>62</mn><mo>&#176;</mo><mi>S</mi><mi>e</mi><mi>c</mi><mn>28</mn><mo>&#176;</mo><mi>C</mi><mi>o</mi><mi>t</mi><mi>&#160;</mi><mn>38</mn><mo>&#176;</mo></mrow><mrow><mi>C</mi><mi>o</mi><mi>s</mi><mi>e</mi><mi>c</mi><mn>62</mn><mo>&#176;</mo><mi>T</mi><mi>a</mi><mi>n</mi><mn>11</mn><mo>&#176;</mo></mrow></mfrac><mo>&#160;</mo><mo>=</mo><mn>1</mn><mspace linebreak=\"newline\"/><mspace linebreak=\"newline\"/><mi>&#2351;&#2366;</mi><mo>&#160;</mo><mo>&#160;</mo><mi>A</mi><mo>&#160;</mo><mo>=</mo><mfrac><mrow><mi>C</mi><mi>o</mi><mi>s</mi><mi>e</mi><mi>c</mi><mn>62</mn><mo>&#176;</mo><mi>T</mi><mi>a</mi><mi>n</mi><mn>11</mn><mo>&#176;</mo></mrow><mrow><mi>T</mi><mi>a</mi><mi>n</mi><mn>62</mn><mo>&#176;</mo><mi>S</mi><mi>e</mi><mi>c</mi><mn>28</mn><mo>&#176;</mo><mi>C</mi><mi>o</mi><mi>t</mi><mi>&#160;</mi><mn>38</mn><mo>&#176;</mo></mrow></mfrac><mspace linebreak=\"newline\"/><mfrac><mrow><mi>s</mi><mi>e</mi><mi>c</mi><mi>&#160;</mi><mn>28</mn><mo>&#176;</mo><mi>c</mi><mi>o</mi><mi>t</mi><mi>&#160;</mi><mn>79</mn><mo>&#176;</mo></mrow><mrow><mi>t</mi><mi>a</mi><mi>n</mi><mi>&#160;</mi><mn>62</mn><mo>&#176;</mo><mi>&#160;</mi><mi>s</mi><mi>e</mi><mi>c</mi><mi>&#160;</mi><mn>28</mn><mo>&#176;</mo><mi>&#160;</mi><mi>c</mi><mi>o</mi><mi>t</mi><mi>&#160;</mi><mn>38</mn><mo>&#176;</mo></mrow></mfrac><mspace linebreak=\"newline\"/><mfrac><mrow><mi>t</mi><mi>a</mi><mi>n</mi><mi>&#160;</mi><mn>28</mn><mo>&#176;</mo><mi>&#160;</mi><mi>t</mi><mi>a</mi><mi>n</mi><mi>&#160;</mi><mn>38</mn><mo>&#176;</mo></mrow><mrow><mi>t</mi><mi>a</mi><mi>n</mi><mn>79</mn><mo>&#176;</mo></mrow></mfrac><mspace linebreak=\"newline\"/><mspace linebreak=\"newline\"/><mspace linebreak=\"newline\"/></math></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "22",
                    section: "17",
                    question_en: "<p>22. If <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>t</mi><mi>a</mi><msup><mi>n</mi><mn>4</mn></msup><mi>x</mi><mo>-</mo><msup><mrow><mi>t</mi><mi>a</mi><mi>n</mi></mrow><mn>2</mn></msup><mi>x</mi><mo>=</mo><mn>1</mn><mo>,</mo></math><span style=\"font-family: Times New Roman;\"> then the value of <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mi>s</mi><mi>i</mi><mi>n</mi></mrow><mn>4</mn></msup><mi>x</mi><mo>+</mo><msup><mrow><mi>S</mi><mi>i</mi><mi>n</mi></mrow><mn>2</mn></msup><mi>x</mi></math></span><span style=\"font-family: Times New Roman;\"> is:</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">SSC CHSL 14-10-2020 (Afternoon shift)</span></p>",
                    question_hi: "<p>22. <span style=\"font-family: Baloo;\">यदि</span><span style=\"font-family: Times New Roman;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>t</mi><mi>a</mi><msup><mi>n</mi><mn>4</mn></msup><mi>x</mi><mo>-</mo><msup><mrow><mi>t</mi><mi>a</mi><mi>n</mi></mrow><mn>2</mn></msup><mi>x</mi><mo>=</mo><mn>1</mn><mo>,</mo></math></span><span style=\"font-family: Baloo;\">तो <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mi>s</mi><mi>i</mi><mi>n</mi></mrow><mn>4</mn></msup><mi>x</mi><mo>+</mo><msup><mrow><mi>S</mi><mi>i</mi><mi>n</mi></mrow><mn>2</mn></msup><mi>x</mi></math></span><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Baloo;\">का मान है:</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">CHSL 14-10-2020 (Afternoon shift)</span></p>",
                    options_en: ["<p><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>4</mn></mfrac></math> </span></p>", "<p><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math> </span></p>", 
                                "<p>1</p>", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>2</mn></mfrac></math></p>"],
                    options_hi: ["<p><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>4</mn></mfrac></math> </span></p>", "<p><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math> </span></p>",
                                "<p>1</p>", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>2</mn></mfrac></math></p>"],
                    solution_en: "<p>22.(c)</p>\r\n<p><span style=\"font-family: Times New Roman;\"> </span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>T</mi><mi>a</mi><msup><mi>n</mi><mn>4</mn></msup><mi>x</mi><mo>-</mo><msup><mrow><mi>T</mi><mi>a</mi><mi>n</mi></mrow><mn>2</mn></msup><mi>x</mi><mo>=</mo><mn>1</mn><mspace linebreak=\"newline\"/><mi>T</mi><mi>a</mi><msup><mi>n</mi><mn>4</mn></msup><mi>x</mi><mo>=</mo><mn>1</mn><mo>+</mo><msup><mrow><mi>T</mi><mi>a</mi><mi>n</mi></mrow><mn>2</mn></msup><mi>x</mi><mspace linebreak=\"newline\"/><mi>T</mi><mi>a</mi><msup><mi>n</mi><mn>4</mn></msup><mi>x</mi><mo>=</mo><msup><mrow><mi>s</mi><mi>e</mi><mi>c</mi></mrow><mn>2</mn></msup><mi>x</mi><mspace linebreak=\"newline\"/><mi>S</mi><mi>i</mi><msup><mi>n</mi><mn>4</mn></msup><mi>x</mi><mo>=</mo><mi>c</mi><mi>o</mi><msup><mi>s</mi><mn>2</mn></msup><mi>x</mi></math></p>\r\n<p><span style=\"font-family: Times New Roman;\">Therefore,&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mi>S</mi><mi>i</mi><mi>n</mi></mrow><mn>4</mn></msup><mi>x</mi><mo>+</mo><msup><mrow><mi>S</mi><mi>i</mi><mi>n</mi></mrow><mn>2</mn></msup><mi>x</mi><mi>&#160;</mi><mo>&#160;</mo><mo>=</mo><msup><mrow><mi>c</mi><mi>o</mi><mi>s</mi></mrow><mn>2</mn></msup><mi>x</mi><mo>+</mo><msup><mrow><mi>S</mi><mi>i</mi><mi>n</mi></mrow><mn>2</mn></msup><mi>x</mi><mo>&#160;</mo><mo>=</mo><mn>1</mn></math></span></p>",
                    solution_hi: "<p>22.(c)</p>\r\n<p><span style=\"font-family: Times New Roman;\"> </span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>T</mi><mi>a</mi><msup><mi>n</mi><mn>4</mn></msup><mi>x</mi><mo>-</mo><msup><mrow><mi>T</mi><mi>a</mi><mi>n</mi></mrow><mn>2</mn></msup><mi>x</mi><mo>=</mo><mn>1</mn><mspace linebreak=\"newline\"/><mi>T</mi><mi>a</mi><msup><mi>n</mi><mn>4</mn></msup><mi>x</mi><mo>=</mo><mn>1</mn><mo>+</mo><msup><mrow><mi>T</mi><mi>a</mi><mi>n</mi></mrow><mn>2</mn></msup><mi>x</mi><mspace linebreak=\"newline\"/><mi>T</mi><mi>a</mi><msup><mi>n</mi><mn>4</mn></msup><mi>x</mi><mo>=</mo><msup><mrow><mi>s</mi><mi>e</mi><mi>c</mi></mrow><mn>2</mn></msup><mi>x</mi><mspace linebreak=\"newline\"/><mi>S</mi><mi>i</mi><msup><mi>n</mi><mn>4</mn></msup><mi>x</mi><mo>=</mo><mi>c</mi><mi>o</mi><msup><mi>s</mi><mn>2</mn></msup><mi>x</mi><mspace linebreak=\"newline\"/><msup><mrow><mi>S</mi><mi>i</mi><mi>n</mi></mrow><mn>4</mn></msup><mi>x</mi><mo>+</mo><msup><mrow><mi>S</mi><mi>i</mi><mi>n</mi></mrow><mn>2</mn></msup><mi>x</mi><mi>&#160;</mi><mo>=</mo><mi>c</mi><mi>o</mi><msup><mi>s</mi><mn>2</mn></msup><mi>x</mi><mo>+</mo><mi>S</mi><mi>i</mi><msup><mi>n</mi><mn>2</mn></msup><mi>x</mi><mo>=</mo><mn>1</mn><mspace linebreak=\"newline\"/><mspace linebreak=\"newline\"/><mspace linebreak=\"newline\"/><mspace linebreak=\"newline\"/><mspace linebreak=\"newline\"/></math></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "23",
                    section: "17",
                    question_en: "<p>23. If sin x + cosec x = 2, then <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#160;</mo><msup><mi>sin</mi><mn>17</mn></msup><mi>x</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mi>cos</mi><mi>e</mi><msup><mi>c</mi><mn>18</mn></msup><mi>x</mi><mo>&#160;</mo></math><span style=\"font-family: Times New Roman;\">is equal to: </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">SSC CHSL 14-10-2020 (Evening shift)</span></p>",
                    question_hi: "<p>23. <span style=\"font-family: Baloo;\">यदि</span><span style=\"font-family: Times New Roman;\"> sin x + cosec x = 2</span><span style=\"font-family: Baloo;\"> है, तो</span><span style=\"font-family: Times New Roman;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>sin</mi><mn>17</mn></msup><mi>x</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mi>cos</mi><mi>e</mi><msup><mi>c</mi><mn>18</mn></msup><mi>x</mi></math></span><span style=\"font-family: Times New Roman;\">&nbsp;</span><span style=\"font-family: Baloo;\">का मान किसके बराबर है? </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">CHSL 14-10-2020 (Evening shift)</span></p>",
                    options_en: ["<p>1</p>", "<p>0</p>", 
                                "<p>4</p>", "<p>2</p>"],
                    options_hi: ["<p>1</p>", "<p>0</p>",
                                "<p>4</p>", "<p>2</p>"],
                    solution_en: "<p>23.(d)<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>sin</mi><mn>17</mn></msup><mi>x</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mi>cos</mi><mi>e</mi><msup><mi>c</mi><mn>18</mn></msup><mi>x</mi></math></p>\r\n<p><span style=\"font-family: Times New Roman;\">Put x = 90&deg;</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">sin 90&deg; + cosec 90&deg; = 1+1 </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">= 2 (which satisfy) </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">&nbsp;= 1+1 = 2</span></p>",
                    solution_hi: "<p>23.(d)</p>\r\n<p><span style=\"font-family: Baloo;\">x = 90&deg; रखें। </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">sin 90&deg; + cosec 90&deg; = 1+1 </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">= 2 (which satisfy) </span></p>\r\n<p><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>sin</mi><mn>17</mn></msup><mi>x</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mi>cos</mi><mi>e</mi><msup><mi>c</mi><mn>18</mn></msup><mi>x</mi></math>&nbsp;= 1+1 = 2</span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "24",
                    section: "17",
                    question_en: "<p>24. Find x if cos x =<span style=\"font-family: Times New Roman;\"> -</span><span style=\"font-family: Times New Roman;\">&frac12; </span><span style=\"font-family: Times New Roman;\">.</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">SSC CHSL 14-10-2020 (Evening shift)</span></p>",
                    question_hi: "<p>24. <span style=\"font-family: Baloo;\">X ज्ञात कीजिए यदि</span><span style=\"font-family: Times New Roman;\"> cos x = </span><span style=\"font-family: Times New Roman;\">-&frac12;</span><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Baloo;\"> है।</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">CHSL 14-10-2020 (Evening shift)</span></p>",
                    options_en: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#160;</mi><mn>3</mn><mi>&#160;</mi><mi>&#928;</mi><mi>&#160;</mi></mrow><mn>2</mn></mfrac></math></p>", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#160;</mi><mn>2</mn><mi>&#160;</mi><mi>&#928;</mi><mi>&#160;</mi></mrow><mn>3</mn></mfrac></math></p>", 
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#160;</mi><mn>5</mn><mi>&#160;</mi><mi>&#928;</mi><mi>&#160;</mi></mrow><mn>2</mn></mfrac></math></p>", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#160;</mi><mn>4</mn><mi>&#160;</mi><mi>&#928;</mi><mi>&#160;</mi></mrow><mn>3</mn></mfrac></math></p>"],
                    options_hi: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#160;</mi><mn>3</mn><mi>&#160;</mi><mi>&#928;</mi><mi>&#160;</mi></mrow><mn>2</mn></mfrac></math></p>", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#160;</mi><mn>2</mn><mi>&#160;</mi><mi>&#928;</mi><mi>&#160;</mi></mrow><mn>3</mn></mfrac></math></p>",
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#160;</mi><mn>5</mn><mi>&#160;</mi><mi>&#928;</mi><mi>&#160;</mi></mrow><mn>2</mn></mfrac></math></p>", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#160;</mi><mn>4</mn><mi>&#160;</mi><mi>&#928;</mi><mi>&#160;</mi></mrow><mn>3</mn></mfrac></math></p>"],
                    solution_en: "<p>24.(b)</p>\r\n<p><span style=\"font-family: Times New Roman;\">cos x =</span><span style=\"font-family: Times New Roman;\"> -</span><span style=\"font-family: Times New Roman;\">&frac12; </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Cos x = Cos 120&deg;</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">x = 120&deg; or&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#160;</mi><mn>2</mn><mi>&#160;</mi><mi>&#928;</mi><mi>&#160;</mi></mrow><mn>3</mn></mfrac></math></span></p>",
                    solution_hi: "<p>24.(b)</p>\r\n<p><span style=\"font-family: Times New Roman;\">cos x =</span><span style=\"font-family: Times New Roman;\"> -</span><span style=\"font-family: Times New Roman;\">&frac12; </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Cos x = Cos 120&deg;</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">x = 120&deg; or&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#160;</mi><mn>2</mn><mi>&#928;</mi><mi>&#160;</mi></mrow><mn>3</mn></mfrac></math></span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "25",
                    section: "misc",
                    question_en: "<p>25. If sin&theta; - cos&theta; =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#160;</mi><mn>7</mn><mi>&#160;</mi></mrow><mn>17</mn></mfrac></math><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Times New Roman;\">, then find the value of sin&theta; + cos&theta;.</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">SSC CHSL 14-10-2020 (Evening)</span></p>",
                    question_hi: "<p>25. If sin&theta; - cos&theta; =<span style=\"font-family: Times New Roman;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>17</mn></mfrac></math></span><span style=\"font-family: Times New Roman;\">, then find the value of sin&theta; + cos&theta;.</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">SSC CHSL 14-10-2020 (Evening)</span></p>",
                    options_en: ["<p><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>8</mn><mn>17</mn></mfrac></math> </span></p>", "<p><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>23</mn><mn>13</mn></mfrac></math> </span></p>", 
                                "<p><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>23</mn><mn>17</mn></mfrac></math> </span></p>", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>8</mn><mn>13</mn></mfrac></math></p>"],
                    options_hi: ["<p><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>8</mn><mn>17</mn></mfrac></math> </span></p>", "<p><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>23</mn><mn>13</mn></mfrac></math> </span></p>",
                                "<p><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>23</mn><mn>17</mn></mfrac></math> </span></p>", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>8</mn><mn>13</mn></mfrac></math></p>"],
                    solution_en: "<p>25.(c)</p>\r\n<p><span style=\"font-family: Times New Roman;\">sin&theta; - cos&theta; =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>17</mn></mfrac></math></span><span style=\"font-family: Times New Roman;\"> </span></p>\r\n<p><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>p</mi><mi>h</mi></mfrac><mo>=</mo><mfrac><mi>b</mi><mi>h</mi></mfrac><mo>=</mo><mfrac><mn>7</mn><mn>17</mn></mfrac></math>&nbsp;</span><span style=\"font-family: Times New Roman;\">(here, H = 17 or its multiple)</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">We know, </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">15, 8 and 17 make right angle triangles.</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Put, p = 15 and b = 8</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">sin&theta; + cos&theta; =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>p</mi><mi>h</mi></mfrac><mo>+</mo><mfrac><mi>b</mi><mi>h</mi></mfrac></math></span></p>\r\n<p><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>15</mn><mo>+</mo><mn>8</mn></mrow><mn>17</mn></mfrac><mo>&#160;</mo><mo>=</mo><mfrac><mn>23</mn><mn>17</mn></mfrac></math></span></p>\r\n<p>&nbsp;</p>",
                    solution_hi: "<p>25.(c)</p>\r\n<p><span style=\"font-family: Times New Roman;\">sin&theta; - cos&theta; =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>17</mn></mfrac></math></span><span style=\"font-family: Times New Roman;\"> </span></p>\r\n<p><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>p</mi><mi>h</mi></mfrac><mo>-</mo><mfrac><mi>b</mi><mi>h</mi></mfrac><mo>&#160;</mo><mo>=</mo><mfrac><mn>7</mn><mn>17</mn></mfrac></math></span><span style=\"font-family: Times New Roman;\">(here, H = 17 or its multiple)</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">We know, </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">15, 8 and 17 make right angle triangles.</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Put, p = 15 and b = 8</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">sin&theta; + cos&theta;. = </span><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>p</mi><mi>h</mi></mfrac><mo>+</mo><mfrac><mi>b</mi><mi>h</mi></mfrac><mo>=</mo><mfrac><mrow><mn>15</mn><mo>+</mo><mn>8</mn></mrow><mn>17</mn></mfrac><mo>=</mo><mfrac><mn>23</mn><mn>17</mn></mfrac></math></span></p>\r\n<p>&nbsp;</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>