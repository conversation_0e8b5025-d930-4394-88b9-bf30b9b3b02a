<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">8:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 15</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">15</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 8 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["18"] = {
                name: "General Awareness",
                start: 0,
                end: 13
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="18">General Awareness</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 14,
                end: 14
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "18",
                    question_en: "<p>1. The ﬁrst Amendment to the constitution of India was made on _________</p>",
                    question_hi: "<p>1. भारत के संविधान में पहला संशोधन _________ को किया गया था।</p>",
                    options_en: ["<p>1953</p>", "<p>1951</p>", 
                                "<p>1952</p>", "<p>1950</p>"],
                    options_hi: ["<p>1953</p>", "<p>1951</p>",
                                "<p>1952</p>", "<p>1950</p>"],
                    solution_en: "<p>1.(b) <strong>1951. First Amendment to the constitution:</strong> It inserted the Ninth Schedule to the Constitution to protect the land reform and other laws present in it from judicial review. <strong>Article 368 </strong>(Part XX): It deals with the powers of Parliament to amend the Constitution and its procedure.</p>",
                    solution_hi: "<p>1.(b) <strong>1951 </strong>। <strong>संविधान में प्रथम संशोधन:</strong> इसने भूमि सुधार और इसमें मौजूद अन्य कानूनों को न्यायिक समीक्षा से बचाने के लिए संविधान में नौवीं अनुसूची जोड़ी गई। <strong>अनुच्छेद 368</strong> (भाग XX): यह संविधान की प्रक्रिया में संशोधन करने की संसद की शक्तियों से संबंधित है।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "2",
                    section: "18",
                    question_en: "<p>2. Which article of the Constitution of India gives the parliament the power to amend the constitution?</p>",
                    question_hi: "<p>2. भारत के संविधान का कौन सा अनुच्छेद संसद को संविधान में संशोधन करने की शक्ति प्रदान करता है?</p>",
                    options_en: ["<p>Article 198</p>", "<p>Article 368</p>", 
                                "<p>Article 356</p>", "<p>Article 144</p>"],
                    options_hi: ["<p>अनुच्छेद 198</p>", "<p>अनुच्छेद 368</p>",
                                "<p>अनुच्छेद 356</p>", "<p>अनुच्छेद 144</p>"],
                    solution_en: "<p>2.(b) <strong>Article 368 (Part XX).</strong> Other important articles of Indian constitution: <strong>Article 356 </strong>- Provisions in case of failure of constitutional machinery in States.<strong> Article 198 </strong>- Special procedure in respect of Money Bills. <strong>Article 144 -</strong> Civil and judicial authorities to act in aid of the Supreme Court.</p>",
                    solution_hi: "<p>2.(b) <strong>अनुच्छेद 368 (भाग XX)।</strong> भारतीय संविधान के अन्य महत्वपूर्ण अनुच्छेद: <strong>अनुच्छेद 356 </strong>- राज्यों में संवैधानिक मशीनरी की विफलता के मामले में प्रावधान। <strong>अनुच्छेद 198 </strong>- धन विधेयक के संबंध में विशेष प्रक्रिया। <strong>अनुच्छेद 144 -</strong> सिविल और न्यायिक प्राधिकारियों को सर्वोच्च न्यायालय की सहायता के लिए कार्य करना।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "3",
                    section: "18",
                    question_en: "<p>3. Which amendment in the Constitution of India deals with the abolition of Right to Property as a fundamental right?</p>",
                    question_hi: "<p>3. भारत के संविधान में कौन सा संशोधन मौलिक अधिकार के रूप में संपत्ति के अधिकार के उन्मूलन से संबंधित है?</p>",
                    options_en: ["<p>37th</p>", "<p>42nd</p>", 
                                "<p>40th</p>", "<p>44th</p>"],
                    options_hi: ["<p>37वां</p>", "<p>42वां</p>",
                                "<p>40वां</p>", "<p>44वां</p>"],
                    solution_en: "<p>3.(d) <strong>44th amendment(1978). Article 300-A,</strong> provided that &ldquo;no person shall be deprived of his property save by authority of law&rdquo;. 42nd Amendment Act, 1976 (<strong>Mini- Constitution</strong>). 61st Amendment Act ,1989 (Voting age less from 21 to 18).</p>",
                    solution_hi: "<p>3.(d) <strong>44वाँ संशोधन (1978)। अनुच्छेद 300-A </strong>प्रावधान करता है कि \"कानून के अधिकार के अलावा किसी भी व्यक्ति को उसकी संपत्ति से वंचित नहीं किया जाएगा\"। 42वां संशोधन अधिनियम, 1976 (<strong>लघु संविधान</strong>)। 61वां संशोधन अधिनियम, 1989 (मतदान की आयु 21 से घटाकर 18 वर्ष कर दी गई )।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "4",
                    section: "18",
                    question_en: "<p>4. Which constitutional Amendment led to the inclusion of Sikkim in the Indian Union?</p>",
                    question_hi: "<p>4. किस संवैधानिक संशोधन के कारण सिक्किम को भारतीय संघ में शामिल किया गया?</p>",
                    options_en: ["<p>33rd Amendment</p>", "<p>37th Amendment</p>", 
                                "<p>36th Amendment</p>", "<p>34th Amendment</p>"],
                    options_hi: ["<p>33वां संशोधन</p>", "<p>37वां संशोधन</p>",
                                "<p>36वां संशोधन</p>", "<p>34वां संशोधन</p>"],
                    solution_en: "<p>4.(c) <strong>36th Amendment.</strong> It omitted Article 2A, which had designated Sikkim as an \"associate state\" of India. Sikkim became the 22nd State of India. <strong>35th Amendment Act, 1974:</strong> It added a new <strong>Article 2A</strong> to the Constitution, which provided for the designation of Sikkim as an \"associate state\" of India.</p>",
                    solution_hi: "<p>4.(c) <strong>36वां संशोधन।</strong> इसमे अनुच्छेद 2A को हटा दिया, जिससे सिक्किम को भारत के \"सहयोगी राज्य\" के रूप में नामित किया गया। सिक्किम भारत का 22वाँ राज्य बन गया। <strong>35वां संशोधन अधिनियम, 1974:</strong> इसने संविधान में एक नया <strong>अनुच्छेद 2A </strong>जोड़ा गया , जो सिक्किम को भारत के \"सहयोगी राज्य\" के रूप में नामित करने का प्रावधान करता है।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "5",
                    section: "18",
                    question_en: "<p>5. Kesavananda Bharati Case in 1973 stemmed the question that the Preamble can_______.</p>",
                    question_hi: "<p>5. 1973 में केशवानंद भारती केस ने इस सवाल को जन्म दिया कि प्रस्तावना को________</p>",
                    options_en: ["<p>be amended</p>", "<p>not be amended</p>", 
                                "<p>be changed completely</p>", "<p>be separated into sections</p>"],
                    options_hi: ["<p>संशोधन किया जा सकता है |</p>", "<p>संशोधन नही किया जा सकता है |</p>",
                                "<p>पूर्णतयः बदला जा सकता है |</p>", "<p>वर्गों में विभाजित किया जा सकता है |</p>"],
                    solution_en: "<p>5.(a) <strong>be amended </strong>(Article 368)<strong>. The Kesavananda Bharati vs State of Kerala 1973: </strong>Parliament has unfettered power to amend the constitution, but it cannot emasculate the basic structure or fundamental features of the constitution; <strong>Preamble </strong>is the part of the constitution but not the source of any power or restriction and cannot be enforceable in a court of justice in India.</p>",
                    solution_hi: "<p>5.(a) <strong>संशोधन किया जा सकता है</strong> (अनुच्छेद 368)। <strong>केशवानंद भारती बनाम केरल राज्य 1973:</strong> संसद के पास संविधान में संशोधन करने की असीमित शक्ति है, लेकिन वह संविधान की मूल ढाँचे या मौलिक विशेषताओं को कमज़ोर नहीं कर सकती है; <strong>प्रस्तावना </strong>संविधान का भाग है लेकिन किसी शक्ति या प्रतिबंध का स्रोत नहीं है और इसे भारत में न्याय की अदालत में लागू नहीं किया जा सकता है।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "6",
                    section: "18",
                    question_en: "<p>6. Which of the following was introduced as the 101st Amendment of the constitution of India on July 1, 2017?</p>",
                    question_hi: "<p>6. निम्नलिखित में से किसे 1 जुलाई, 2017 को भारत के संविधान के 101वें संशोधन के रूप में पेश किया गया था?</p>",
                    options_en: ["<p>Goods and Services Tax</p>", "<p>Land boundary agreement between India and Bangladesh</p>", 
                                "<p>Replacing Orissa with Odisha</p>", "<p>Constitutional status of cooperatives</p>"],
                    options_hi: ["<p>वस्तु एवं सेवा कर</p>", "<p>भारत और बांग्लादेश के बीच भूमि सीमा समझौता</p>",
                                "<p>उड़ीसा को ओडिशा से बदलना</p>", "<p>सहकारी समितियों की संवैधानिक स्थिति</p>"],
                    solution_en: "<p>6.(a) <strong>Goods and Service Tax -</strong> An Indirect tax on the manufacture, sale, and consumption of goods and services throughout India. Land boundary agreement between India and Bangladesh <strong>- 100<sup>th</sup> Constitutional Amendment 2015, </strong>Replacing Orissa word with Odisha - <strong>96<sup>th</sup> Constitutional Amendment 2011,</strong> Constitutional status and protection of cooperatives societies <strong>- 97<sup>th</sup> Constitutional Amendment 2011.</strong></p>",
                    solution_hi: "<p>6.(a) <strong>वस्तु एवं सेवा कर --</strong> भारत में वस्तुओं और सेवाओं के निर्माण, बिक्री और खपत पर अप्रत्यक्ष कर। भारत और बांग्लादेश के बीच भूमि सीमा समझौता - <strong>100वां संवैधानिक संशोधन 2015</strong>, उड़ीसा शब्द के स्थान पर ओडिशा - <strong>96वां संवैधानिक संशोधन 2011, </strong>सहकारी समितियों की संवैधानिक स्थिति और सुरक्षा - <strong>97वां संवैधानिक संशोधन 2011 ।</strong></p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "7",
                    section: "18",
                    question_en: "<p>7. Which of the following is related to the 124th Constitutional Amendment Bill?</p>",
                    question_hi: "<p>7.निम्नलिखित में से कौन 124वें संविधान संशोधन विधेयक से संबंधित है?</p>",
                    options_en: ["<p>Right to free and compulsory education till the age of fourteen</p>", "<p>National Commission for Backward Class</p>", 
                                "<p>GST Bill</p>", "<p>10% reservation to economically weaker sections in the general category</p>"],
                    options_hi: ["<p>चौदह वर्ष की आयु तक निःशुल्क और अनिवार्य शिक्षा का अधिकार</p>", "<p>पिछड़ा वर्ग के लिए राष्ट्रीय आयोग</p>",
                                "<p>GST विधेयक</p>", "<p>सामान्य वर्ग में आर्थिक रूप से कमजोर वर्ग को 10% आरक्षण</p>"],
                    solution_en: "<p>7.(d) <strong>124 Amendment Bill</strong> 2019 - To provide for reservation in jobs and educational institutions to economically backward category. It took the form of the 103 Constitutional Amendment <strong>Act. 101 Amendment </strong>Act 2016- It allows both the centre and states to levy the Goods and Services Tax (GST). <strong>102 nd Constitution</strong> <strong>Amendment Act, 2018 </strong>- It provides constitutional status to the National Commission for Backward Classes (NCBC).<strong> 86<sup>th</sup> Constitution Amendment Act, 2002 -</strong> It inserted Article 21-A in the Constitution of India to provide free and compulsory education of all children in the age group of six to fourteen years.</p>",
                    solution_hi: "<p>7.(d) <strong>124वां संशोधन विधेयक 2019 -</strong> आर्थिक रूप से पिछड़े वर्ग को नौकरियों और शैक्षणिक संस्थानों में आरक्षण प्रदान करने के लिए। इसने 103वें संवैधानिक संशोधन अधिनियम का रूप लिया। <strong>101वां संशोधन अधिनियम 2016 -</strong> यह केंद्र और राज्य दोनों को वस्तु एवं सेवा कर (GST) लगाने की अनुमति देता है। <strong>102वां संविधान संशोधन अधिनियम, 2018 -</strong> राष्ट्रीय पिछड़ा वर्ग आयोग (NCBC) को संवैधानिक दर्जा प्रदान करता है। <strong>86वां संविधान संशोधन अधिनियम, 2002 - </strong>छह से चौदह वर्ष की आयु के सभी बच्चों को मुफ्त और अनिवार्य शिक्षा प्रदान करने के लिए भारत के संविधान में अनुच्छेद 21-A शामिल किया गया।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "8",
                    section: "18",
                    question_en: "<p>8. A revised version of the India Bangladesh Land Boundary Agreement was adopted by two countries Under which amendment of the constitution of India?</p>",
                    question_hi: "<p>8. भारत बांग्लादेश भूमि सीमा समझौते का एक संशोधित संस्करण दो देशों द्वारा अपनाया गया था, यह भारत के संविधान के किस संशोधन के अधीन है?</p>",
                    options_en: ["<p>98th Constitutional Amendment Bill, 2012</p>", "<p>99th Constitutional Amendment Bill, 2014</p>", 
                                "<p>101st Constitutional Amendment Bill, 2016</p>", "<p>100th Constitutional Amendment Bill, 2015</p>"],
                    options_hi: ["<p>98वां संविधान संशोधन विधेयक, 2012</p>", "<p>99वां संविधान संशोधन विधेयक, 2014</p>",
                                "<p>101वां संविधान संशोधन विधेयक, 2016</p>", "<p>100वां संविधान संशोधन विधेयक, 2015</p>"],
                    solution_en: "<p>8.(d) <strong>100th Constitutional Amendment Bill, 2015. 101st Constitutional Amendment Bill </strong>(2016) - Introduced national Goods and Service Tax. <strong>98th Constitutional Amendmen</strong>t (2012) - It aims to form the National Judicial Commission for appointment and transfer of judges in the higher judiciary. <strong>99th Constitutional Amendment Bill</strong> (2014) - Providing for the creation of an independent commission to appoint judges to the Supreme Court and high courts to replace the collegium system.</p>",
                    solution_hi: "<p>8.(d) <strong>100वां संवैधानिक संशोधन विधेयक, 2015। 101वां संवैधानिक संशोधन विधेयक</strong> (2016) - राष्ट्रीय वस्तु एवं सेवा कर (GST) पेश किया गया । <strong>98वां संवैधानिक संशोधन</strong> (2012) - इसका उद्देश्य उच्च न्यायपालिका में न्यायाधीशों की नियुक्ति और स्थानांतरण के लिए राष्ट्रीय न्यायिक आयोग का गठन करना है। <strong>99वां संवैधानिक संशोधन विधेयक</strong> (2014) - कॉलेजियम प्रणाली को बदलने के लिए सर्वोच्च न्यायालय और उच्च न्यायालयों में न्यायाधीशों की नियुक्ति के लिए एक स्वतंत्र आयोग के निर्माण का प्रावधान है।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "9",
                    section: "18",
                    question_en: "<p>9. What is the 69th amendment to the Constitution of India ?</p>",
                    question_hi: "<p>9. भारत के संविधान में 69वां संशोधन किससे सम्बंधित है?</p>",
                    options_en: ["<p>Introduced the Goods and Service Tax</p>", "<p>Bodo, Dogri included as official languages</p>", 
                                "<p>Delhi made National Capital Region and given special status</p>", "<p>Voting age lowered from 21 to18 years</p>"],
                    options_hi: ["<p>वस्तु एवं सेवा कर का प्रस्ताव</p>", "<p>बोडो, डोगरी को आधिकारिक भाषाओं के रूप में शामिल किया गया</p>",
                                "<p>दिल्ली को राष्ट्रीय राजधानी क्षेत्र बनाया गया और विशेष राज्य का दर्जा दिया गया</p>", "<p>मतदान की उम्र 21 से घटाकर 18 साल की गई</p>"],
                    solution_en: "<p>9.(c) Article 239AA was inserted in the Constitution by the 69th Amendment Act 1991. <strong>Some Constitution Amendment Act:- </strong>The 101st Amendment Act, 2016 - Goods and Services Tax. The 92nd Amendment Act of 2003 - Bodo, Dogri (Dongri), Mathilli (Maithili) and Santhali. 61st Constitutional Amendment Act, 1988 - Voting age reduced from 21 to18 years.</p>",
                    solution_hi: "<p>9.(c) अनुच्छेद 239AA को 69वें संशोधन अधिनियम 1991 द्वारा संविधान में शामिल किया गया था। <strong>कुछ संविधान संशोधन अधिनियम:-</strong> 101वां संशोधन अधिनियम, 2016 - वस्तु और सेवा कर। 92वां संशोधन अधिनियम, 2003 - बोडो, डोगरी (डोंगरी), मैथिली और संथाली भाषा जोड़ी गयी। 61वां संवैधानिक संशोधन अधिनियम, 1988 - मतदान की आयु 21 से घटाकर 18 वर्ष कर दी गई।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "10",
                    section: "18",
                    question_en: "<p>10. &lsquo;Right to Property&rsquo; was removed from the list of Fundamental Rights by which amendment to the Constitution of India?</p>",
                    question_hi: "<p>10. भारत के संविधान में किस संशोधन द्वारा \'संपत्ति के अधिकार\' को मौलिक अधिकारों की सूची से हटा दिया गया?</p>",
                    options_en: ["<p>44th</p>", "<p>48th</p>", 
                                "<p>46th</p>", "<p>42nd</p>"],
                    options_hi: ["<p>44वां</p>", "<p>48वां</p>",
                                "<p>46वां</p>", "<p>42वां</p>"],
                    solution_en: "<p>10.(a) <strong>44<sup>th</sup></strong> - becomes a simple legal right (Article 300A). 42<sup>nd </sup>Constitutional Amendment Act (1976, also called as Mini Constitution of India): 10 Fundamental Duties were added on the recommendation of Sardar Swaran Singh Committee. The 46th Amendment Act (1983) - made it possible for the State to levy Sales Tax on the price of the goods and materials used in works contracts as if there was a sale of such goods and materials.</p>",
                    solution_hi: "<p>10.(a) <strong>44वां -</strong> (अनुच्छेद 300A) के तहत एक वैधानिक अधिकार बना दिया गया । 42वां संवैधानिक संशोधन अधिनियम (1976, जिसे भारत का लघु संविधान भी कहा जाता है): सरदार स्वर्ण सिंह समिति की सिफारिश पर 10 मौलिक कर्तव्य जोड़े गए। 46वें संशोधन अधिनियम (1983) ने राज्य के लिए कार्य अनुबंधों में उपयोग की जाने वाली वस्तुओं और सामग्रियों की कीमत पर बिक्री कर लगाना संभव बना दिया, जैसे कि ऐसी वस्तुओं और सामग्रियों की बिक्री हो रही हो।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "11",
                    section: "18",
                    question_en: "<p>11. Which of the following is NOT correct with respect to amendments in the Constitution of India?</p>",
                    question_hi: "<p>11. भारत के संविधान में संशोधन के संबंध में निम्नलिखित में से कौन सा सही नहीं है?</p>",
                    options_en: ["<p>72<sup>nd</sup> amendment-to make provisions for setting up Rent Control Tribunals</p>", "<p>102<sup>nd</sup> amendment-to give constitutional status to the National Commission for Backward Classes</p>", 
                                "<p>61<sup>st</sup> amendment-to reduce the voting age from 21 to 18 years</p>", "<p>101<sup>st</sup> amendment - to introduce goods and services tax</p>"],
                    options_hi: ["<p>72वां संशोधन - किराया नियंत्रण न्यायाधिकरण की स्थापना के लिए प्रावधान करने के लिए</p>", "<p>102वां संशोधन- राष्ट्रीय पिछड़ा वर्ग आयोग को संवैधानिक दर्जा देने के लिए</p>",
                                "<p>61वां संशोधन- मतदान की आयु 21 से घटाकर 18 वर्ष करने के लिए</p>", "<p>101वां संशोधन - वस्तु एवं सेवा कर लागू करने के लिए</p>"],
                    solution_en: "<p>11.(a) <strong>72nd-amendment </strong>is to make temporary provision for the determination of the number of seats reserved for the Scheduled Tribes in the State assembly of Tripura.<strong> Amendments of the Constitution: </strong>Part XX (Article 368). Borrowed from the South African constitution. <strong>Article 368 -</strong> Parliament can amend any part of the Constitution including the Fundamental Rights but without affecting the &lsquo;basic structure&rsquo; of the Constitution&rdquo;.</p>",
                    solution_hi: "<p>11.(a) <strong>72वां संशोधन </strong>त्रिपुरा राज्य विधानसभा में अनुसूचित जनजातियों के लिए आरक्षित सीटों की संख्या के निर्धारण के लिए अस्थायी प्रावधान करना है।<strong> संविधान संशोधन: </strong>भाग XX (अनुच्छेद 368)। दक्षिण अफ़्रीकी संविधान से ग्रहण किया गया। अनुच्छेद 368 - संसद मौलिक अधिकारों सहित संविधान के किसी भी भाग में संशोधन कर सकती है लेकिन संविधान की \'मूल ढांचें \' को नहीं बदल सकती है।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "12",
                    section: "18",
                    question_en: "<p>12. When were the words &ldquo;Socialist, Secular and Democratic Republic Added in the Indian Constitutions?</p>",
                    question_hi: "<p>12. भारतीय संविधान में \"समाजवादी, धर्मनिरपेक्ष और लोकतांत्रिक गणराज्य\" शब्द कब जोड़े गए?</p>",
                    options_en: ["<p>In the 44th amendment in 1978</p>", "<p>In the 42th amendment in 1976</p>", 
                                "<p>In the 42nd amendment in 1985</p>", "<p>In the 35th amendment in 1974</p>"],
                    options_hi: ["<p>1978 में 44वें संशोधन में</p>", "<p>1976 में 42वें संशोधन में</p>",
                                "<p>1985 में 42वें संशोधन में</p>", "<p>1974 में 35वें संशोधन में</p>"],
                    solution_en: "<p>12.(b)<strong> In the 42th amendment in 1976.</strong> This amendment also changed the words \"Unity of the Nation\" to \"Unity and Integrity of the Nation\". <strong>44th amendment Act (1978) :</strong> Removed the right to property from the list of Fundamental Rights and converted it into a simple legal right under article 300 A. <strong>35th amendment Act (1974) -</strong> The status of Sikkim as a protectorate state was terminated and Sikkim was given the status of \'Associate State\' of India.</p>",
                    solution_hi: "<p>12.(b) <strong>1976 में 42वें संशोधन में।</strong> इस संशोधन ने \"राष्ट्र की एकता\" शब्दों को भी \"राष्ट्र की एकता और अखंडता\" में परिवर्तित कर दिया । <strong>44वां संशोधन अधिनियम (1978):</strong> संपत्ति के अधिकार को मौलिक अधिकारों की सूची से हटा दिया गया और इसे अनुच्छेद 300A के तहत एक संवैधानिक अधिकार बनाया गया था। <strong>35वां संशोधन अधिनियम (1974) - </strong>एक संरक्षित राज्य के रूप में सिक्किम का दर्जा समाप्त कर दिया गया और सिक्किम को भारत के \'एसोसिएट स्टेट\' का दर्जा दिया गया</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "13",
                    section: "18",
                    question_en: "<p>13. Which of the following is related to the 61st Amendment of the Constitution of India?</p>",
                    question_hi: "<p>13. निम्नलिखित में से कौन भारत के संविधान के 61वें संशोधन से संबंधित है?</p>",
                    options_en: ["<p>Advancement of economically weaker sections of citizens</p>", "<p>Lowered the voting age of elections</p>", 
                                "<p>Extended the period of reservation of seats</p>", "<p>Incorporation of Goa, Daman and Diu as a union territory</p>"],
                    options_hi: ["<p>नागरिकों के आर्थिक रूप से कमजोर वर्गों की उन्नति</p>", "<p>चुनाव की मतदान की आयु कम की</p>",
                                "<p>सीटों के आरक्षण की अवधि बढ़ाई</p>", "<p>गोवा, दमन और दीव को केंद्र शासित प्रदेश के रूप में शामिल करना</p>"],
                    solution_en: "<p>13. (b) <strong>61<sup>st</sup> Amendment Act, 1988 -</strong> Lowered the voting age of elections to the Lok Sabha and Legislative Assemblies of States from 21 years to 18 years. The <strong>103<sup>rd </sup>Amendment Act, 2019 -</strong> Offered a 10% reservation quota on government jobs and admissions to educational institutions for Economically Weaker Sections (EWS) from unreserved category. The <strong>104<sup>th</sup> Amendment Act, 2019 -</strong> Ceased the reservation of seats for the Anglo-Indians in the Lok Sabha and State Legislative Assemblies and extended the reservations for Scheduled castes (SCs) and Schedule tribes (STs) for up to ten years. The <strong>12<sup>th</sup> Amendment Act, 1962 -</strong> Incorporated Goa, Daman and Diu as a Union Territory (Schedule I).</p>",
                    solution_hi: "<p>13. (b) <strong>61 वां संशोधन अधिनियम, 1988 -</strong> लोकसभा और राज्यों की विधान सभाओं के चुनावों में मतदान की आयु 21 वर्ष से घटाकर 18 वर्ष कर दी गई। <strong>103वां संशोधन अधिनियम, 2019 - </strong>अनारक्षित श्रेणी से आर्थिक रूप से कमजोर वर्ग (EWS) के लिए सरकारी नौकरियों और शैक्षणिक संस्थानों में प्रवेश पर 10% आरक्षण कोटा की पेशकश की गई। <strong>104वां संशोधन अधिनियम, 2019 - </strong>लोकसभा और राज्य विधानसभाओं में एंग्लो-इंडियन के लिए सीटों का आरक्षण खत्म कर दिया और अनुसूचित जाति (SC) और अनुसूचित जनजाति (ST) के लिए आरक्षण को दस साल तक बढ़ा दिया। <strong>12वां संशोधन अधिनियम, 1962 -</strong> गोवा, दमन और दीव को केंद्र शासित प्रदेश (अनुसूची 1) के रूप में शामिल किया गया।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "14",
                    section: "18",
                    question_en: "<p>14. Which constitutional amendment act in 1987 made Goa a full-fledged state with a state assembly and formed Daman and Diu as a Union Territory?</p>",
                    question_hi: "<p>14. 1987 में किस संवैधानिक संशोधन अधिनियम ने गोवा को एक राज्य विधानसभा के साथ एक पूर्ण राज्य बना दिया और दमन और दीव को एक केंद्र शासित प्रदेश बना दिया?</p>",
                    options_en: ["<p>55th</p>", "<p>57th</p>", 
                                "<p>56th</p>", "<p>52nd</p>"],
                    options_hi: ["<p>55th</p>", "<p>57th</p>",
                                "<p>56th</p>", "<p>52nd</p>"],
                    solution_en: "<p>14.(c) <strong>56th</strong>. It was the 25th state of India. Arunachal Pradesh was made a full-fledged state by the 55th Constitutional Amendment (1987). 57th Constitutional Amendment.It dealt with the reservation of seats for tribal people of the North-Eastern states (Mizoram, Nagaland, Meghalaya and Arunachal Pradesh) in the Lower House of Parliament and State legislatures(1987). 52nd Constitutional Amendment (1985) - Tenth Schedule.</p>",
                    solution_hi: "<p>14.(c) <strong>56th</strong>। यह भारत का 25वाँ राज्य था। 55वें संवैधानिक संशोधन (1987) द्वारा अरुणाचल प्रदेश को पूर्ण राज्य बनाया गया था। 57वां संवैधानिक संशोधन , यह संसद के निचलें सदन और राज्य विधानसभाओं (1987) में उत्तर-पूर्वी राज्यों (मिजोरम, नागालैंड, मेघालय और अरुणाचल प्रदेश) के आदिवासी लोगों के लिए सीटों के आरक्षण से संबंधित है। 52वाँ संवैधानिक संशोधन (1985) - दसवीं अनुसूची।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "15",
                    section: "misc",
                    question_en: "<p>15. In which Amendment act of Indian constitution is the Goods and Services Tax introduced?</p>",
                    question_hi: "<p>15. भारतीय संविधान के किस संशोधन अधिनियम में वस्तु और सेवा कर पेश किया गया है?</p>",
                    options_en: ["<p>101</p>", "<p>122</p>", 
                                "<p>105</p>", "<p>103</p>"],
                    options_hi: ["<p>101</p>", "<p>122</p>",
                                "<p>105</p>", "<p>103</p>"],
                    solution_en: "<p>15.(a) <strong>101. Goods and Services Tax -</strong> Came into effect on 1st July 2017. As per Article 279A (1) of the amended Constitution, the GST Council has to be constituted by the President within 60 days of the commencement of Article 279A. <strong>France </strong>was the first country to implement the GST in 1954. The GST services four tax slabs of 5%, 12%, 18% and 28%. <strong>103 Constitutional amendment Act - </strong>Came into existence to offer 10% reservation to Economically Weaker Sections (EWS) of society.</p>",
                    solution_hi: "<p>15.(a) <strong>101 । वस्तु एवं सेवा कर -</strong> 1 जुलाई 2017 को लागू हुआ। संशोधित संविधान के अनुच्छेद 279A (1) के अनुसार, अनुच्छेद 279A के प्रारंभ होने के 60 दिनों के अंदर राष्ट्रपति द्वारा GST परिषद का गठन किया जाना है। <strong>फ्रांस </strong>1954 में GST लागू करने वाला प्रथम देश था। GST में 5%, 12%, 18% और 28% के चार कर स्लैब शामिल हैं। <strong>103वां संवैधानिक संशोधन अधिनियम -</strong> समाज के आर्थिक रूप से कमजोर वर्गों (EWS) को 10% आरक्षण प्रदान करने के लिए अस्तित्व में आया है।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>