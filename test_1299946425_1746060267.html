<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Smart Test Interface</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        .question-nav{position:fixed;top:70px;right:0;height:calc(100vh - 70px);width:320px;transition:all .3s;z-index:100;display:flex;flex-direction:column}
        .question-content{margin-right:0;transition:all .3s}
        .question-box{width:40px;height:40px;margin:5px;cursor:pointer}
        .attempted{background-color:#3498db!important;color:white}
        .correct{background-color:#2ecc71!important;color:white}
        .incorrect{background-color:#e74c3c!important;color:white}
        .current{border:3px solid #f39c12!important}
        #question-boxes{overflow-y:auto;max-height:calc(100vh - 150px);padding-bottom:15px}
        @media (max-width:768px){.question-nav{width:100%;right:-100%}.question-content{margin-right:0}}
    </style>
</head>
<body>
    <div class="container-fluid p-0">
        <!-- Header with controls -->
        <nav class="navbar navbar-expand-lg navbar-dark bg-primary sticky-top">
            <div class="container-fluid">
                <a class="navbar-brand" href="#">
                    <i class="fas fa-graduation-cap me-2"></i>Smart Test
                </a>
                <!-- Section selector -->
                <div class="navbar-nav me-auto section-tabs">
                    <select class="form-select" id="section-selector" onchange="switchSection(this.value)">
                        <option value="all">All Sections</option>
                    </select>
                </div>
                <div class="d-flex align-items-center">
                    <!-- Timer -->
                    <div class="bg-light text-primary rounded px-3 py-2 me-2 fw-bold" id="timer">
                        <i class="far fa-clock me-2"></i><span id="timer-display">100:00</span>
                    </div>
                    <!-- Submit button -->
                    <button class="btn btn-success me-2" id="submit-btn" onclick="submitTest()">
                        <i class="fas fa-check me-1"></i> Submit
                    </button>
                    <!-- Language toggle -->
                    <button class="btn btn-light me-2" id="lang-btn" onclick="toggleLanguage()">
                        <i class="fas fa-language"></i>
                    </button>
                    <!-- Menu toggle for mobile -->
                    <button class="btn btn-light d-lg-none" id="menu-toggle" onclick="toggleMenu()">
                        <i class="fas fa-th"></i>
                    </button>
                </div>
            </div>
        </nav>
        <div class="row m-0">
            <!-- Main content area -->
            <div class="col p-0 question-content" id="question-content">
                <div class="container py-4">
                    <!-- Question navigation -->
                    <div class="d-flex justify-content-between mb-4">
                        <button class="btn btn-outline-primary" onclick="prevQuestion()">
                            <i class="fas fa-chevron-left me-1"></i> Previous
                        </button>
                        <div class="fw-bold" id="question-counter">Question 1 of 100</div>
                        <button class="btn btn-outline-primary" onclick="nextQuestion()">
                            Next <i class="fas fa-chevron-right ms-1"></i>
                        </button>
                    </div>
                    <!-- Questions will be displayed here -->
                    <div id="questions-container"></div>
                </div>
            </div>
            <!-- Question navigation sidebar -->
            <div class="bg-light question-nav" id="question-nav">
                <div class="p-3">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h5 class="m-0">Questions</h5>
                        <div>
                            <span class="badge bg-secondary me-1" id="attempted-count">0</span>
                            <span class="badge bg-secondary" id="total-count">100</span>
                            <button class="btn btn-sm btn-outline-secondary ms-2 d-lg-none" onclick="toggleMenu()">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    </div>
                    <div class="d-flex flex-wrap" id="question-boxes"></div>
                </div>
            </div>
        </div>
        <!-- Results modal -->
        <div class="modal fade" id="results-modal" tabindex="-1" aria-hidden="true">
            <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content">
                    <div class="modal-header bg-primary text-white">
                        <h5 class="modal-title">Test Results</h5>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <div class="row text-center">
                            <div class="col-6 mb-3">
                                <div class="p-3 bg-primary bg-opacity-10 rounded">
                                    <h3 id="score-value">0</h3>
                                    <div class="small text-muted">Score</div>
                                </div>
                            </div>
                            <div class="col-6 mb-3">
                                <div class="p-3 bg-success bg-opacity-10 rounded">
                                    <h3 id="correct-value">0</h3>
                                    <div class="small text-muted">Correct</div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="p-3 bg-danger bg-opacity-10 rounded">
                                    <h3 id="incorrect-value">0</h3>
                                    <div class="small text-muted">Incorrect</div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="p-3 bg-secondary bg-opacity-10 rounded">
                                    <h3 id="unattempted-value">0</h3>
                                    <div class="small text-muted">Unattempted</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-primary" onclick="reviewTest()">Review Test</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 100 * 60;
        let resultsModal;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            resultsModal = new bootstrap.Modal(document.getElementById('results-modal'));
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 768) {
                document.getElementById('question-content').style.marginRight = '320px';
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["6"] = {
                name: "Reasoning",
                start: 0,
                end: 23
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="6">Reasoning</option>`;
            sections["18"] = {
                name: "General Awareness",
                start: 24,
                end: 48
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="18">General Awareness</option>`;
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 49,
                end: 73
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["7"] = {
                name: "English",
                start: 74,
                end: 98
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="7">English</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 99,
                end: 99
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "6",
                    question_en: "<p>1. Select the correct mirror image of the given figure when the mirror is placed at MN as shown below.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741669102287.png\" alt=\"rId4\" width=\"115\" height=\"95\"></p>",
                    question_hi: "<p>1. दी गई आकृति के उस सही दर्पण प्रतिबिंब को चुनिए, जो नीचे दर्शाए गए अनुसार दर्पण को MN पर रखने पर बनेगा।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741669102287.png\" alt=\"rId4\" width=\"115\" height=\"95\"></p>",
                    options_en: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741669102425.png\" alt=\"rId5\" width=\"100\" height=\"23\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741669102597.png\" alt=\"rId6\" width=\"100\" height=\"24\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741669102723.png\" alt=\"rId7\" width=\"101\" height=\"24\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741669102897.png\" alt=\"rId8\" width=\"100\" height=\"23\"></p>"
                    ],
                    options_hi: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741669102425.png\" alt=\"rId5\" width=\"100\" height=\"23\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741669102597.png\" alt=\"rId6\" width=\"100\" height=\"24\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741669102723.png\" alt=\"rId7\" width=\"102\" height=\"24\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741669102897.png\" alt=\"rId8\" width=\"100\" height=\"23\"></p>"
                    ],
                    solution_en: "<p>1.(a) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741669102425.png\" alt=\"rId5\" width=\"118\" height=\"27\"></p>",
                    solution_hi: "<p>1.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741669102425.png\" alt=\"rId5\" width=\"118\" height=\"27\"></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "6",
                    question_en: "<p>2. If \'A\' stands for &lsquo;&divide;&rsquo;, &lsquo;B\' stands for &lsquo;&times;&rsquo;, &lsquo;C&rsquo; stands for &lsquo;+&rsquo; and \'D\' stands for &lsquo;-&rsquo;, what will come in place of the question mark (?) in the following equation?<br>321 A 3 C 11 B 10 D 19 = ?</p>",
                    question_hi: "<p>2. यदि \'A\' का अर्थ &lsquo;&divide;&rsquo;, \'B\' का अर्थ \'&times;\', \'C\' का अर्थ \'+\' और \'D\' का अर्थ &lsquo;-&rsquo; है, तो निम्नलिखित समीकरण में प्रश्न चिन्ह (?) के स्थान पर क्या आएगा?<br>321 A 3 C 11 B 10 D 19 = ?</p>",
                    options_en: [
                        "<p>178</p>",
                        "<p>198</p>",
                        "<p>188</p>",
                        "<p>168</p>"
                    ],
                    options_hi: [
                        "<p>178</p>",
                        "<p>198</p>",
                        "<p>188</p>",
                        "<p>168</p>"
                    ],
                    solution_en: "<p>2.(b) <strong>Given :-</strong> 321 A 3 C 11 B 10 D 19 <br>As per given instruction after interchanging the letter with sign we get<br>321 &divide;&nbsp;3 + 11 &times; 10 - 19<br>107 + 110 - 19<br>107 + 91 = 198</p>",
                    solution_hi: "<p>2.(b) <strong>दिया गया :-</strong> 321 A 3 C 11 B 10 D 19 <br>दिए गए निर्देश के अनुसार अक्षर को चिन्ह से बदलने पर हमें प्राप्त होता है<br>321 &divide;&nbsp;3 + 11 &times; 10 - 19<br>107 + 110 - 19<br>107 + 91 = 198</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "6",
                    question_en: "<p>3. Identify the figure from among the given options which when put in place of the question mark (?) will logically complete the series.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741669103038.png\" alt=\"rId9\" width=\"325\" height=\"68\"></p>",
                    question_hi: "<p>3. दिए गए विकल्पों में से उस आकृति की पहचान कीजिए जिसे प्रश्न चिह्न (?) के स्थान पर रखने पर श्रृंखला तार्किक रूप से पूरी हो जाएगी।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741669103038.png\" alt=\"rId9\" width=\"325\" height=\"68\"></p>",
                    options_en: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741669103140.png\" alt=\"rId10\" width=\"94\" height=\"95\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741669103262.png\" alt=\"rId11\" width=\"94\" height=\"97\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741669103359.png\" alt=\"rId12\" width=\"94\" height=\"95\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741669103460.png\" alt=\"rId13\" width=\"95\" height=\"97\"></p>"
                    ],
                    options_hi: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741669103140.png\" alt=\"rId10\" width=\"95\" height=\"96\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741669103262.png\" alt=\"rId11\" width=\"96\" height=\"99\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741669103359.png\" alt=\"rId12\" width=\"94\" height=\"95\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741669103460.png\" alt=\"rId13\" width=\"95\" height=\"97\"></p>"
                    ],
                    solution_en: "<p>3.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741669103359.png\" alt=\"rId12\" width=\"97\" height=\"98\"></p>",
                    solution_hi: "<p>3.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741669103359.png\" alt=\"rId12\" width=\"95\" height=\"96\"></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "6",
                    question_en: "<p>4. In a certain code language, &lsquo;BREAK&rsquo; is coded as &lsquo;57123&rsquo; and &lsquo;BROKE&rsquo; is coded as &lsquo;91753&rsquo;. What is the code for &lsquo;A&rsquo; in that language?</p>",
                    question_hi: "<p>4. एक निश्चित कूट भाषा में, \'BREAK\' को \'57123\' के रूप में कूटबद्ध किया जाता है और \'BROKE\' को \'91753\' के रूप में कूटबद्ध किया जाता है। उसी भाषा में \'A\' के लिए क्या कूट होगा?</p>",
                    options_en: [
                        "<p>2</p>",
                        "<p>5</p>",
                        "<p>9</p>",
                        "<p>3</p>"
                    ],
                    options_hi: [
                        "<p>2</p>",
                        "<p>5</p>",
                        "<p>9</p>",
                        "<p>3</p>"
                    ],
                    solution_en: "<p>4.(a)<br>B R E A K &rarr; 5 7 1 2 3 ....(i)<br>B R O K E &rarr;&nbsp;&nbsp;9 1 7 5 3 ....(ii)<br>From&nbsp; eqn (i) and (ii)&nbsp; \'BREK\' and \'5713\' is common , Hence code for \' A \' &rarr; 2</p>\n<p>&nbsp;</p>\n<p>&nbsp;</p>",
                    solution_hi: "<p>4.(a)<br>B R E A K &rarr;&nbsp;&nbsp;5 7 1 2 3 ....(i)<br>B R O K E &rarr;&nbsp;&nbsp;9 1 7 5 3 ....(ii)<br>समीकरण (i) और (ii)&nbsp; से , \'BREK\' और&nbsp; \'5713\' उभयनिष्ट है , इसलिए \'A\' का कूट &rarr; 2&nbsp;</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "6",
                    question_en: "<p>5. Select the option figure that will replace the question mark (?) in the figure given below to complete the pattern.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741669103595.png\" alt=\"rId14\" width=\"123\" height=\"106\"></p>",
                    question_hi: "<p>5. उस विकल्प आकृति का चयन कीजिए , जो पैटर्न को पूरा करने के लिए नीचे दी गई आकृति में प्रश्न चिह्न (?) को प्रतिस्थापित करेगी।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741669103595.png\" alt=\"rId14\" width=\"123\" height=\"106\"></p>",
                    options_en: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741669103757.png\" alt=\"rId15\" width=\"110\" height=\"96\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741669103957.png\" alt=\"rId16\" width=\"111\" height=\"97\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741669104111.png\" alt=\"rId17\" width=\"110\" height=\"96\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741669104261.png\" alt=\"rId18\" width=\"110\" height=\"96\"></p>"
                    ],
                    options_hi: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741669103757.png\" alt=\"rId15\" width=\"110\" height=\"96\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741669103957.png\" alt=\"rId16\" width=\"111\" height=\"97\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741669104111.png\" alt=\"rId17\" width=\"110\" height=\"96\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741669104261.png\" alt=\"rId18\" width=\"110\" height=\"96\"></p>"
                    ],
                    solution_en: "<p>5.(a) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741669103757.png\" alt=\"rId15\" width=\"112\" height=\"98\"></p>",
                    solution_hi: "<p>5.(a) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741669103757.png\" alt=\"rId15\" width=\"112\" height=\"98\"></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "6",
                    question_en: "<p>6. In a certain code language, \'BORN\' is coded as \'8234\' and \'NORM\' is coded as \'6328\'. How is \'M\' coded in the given language?</p>",
                    question_hi: "<p>6. एक निश्चित कूट भाषा में, \'BORN\' को \'8234\' लिखा जाता है और NORM\' को \'6328\' लिखा जाता है। उसी कूट भाषा में \'M\' को कैसे लिखा जाएगा?</p>",
                    options_en: [
                        "<p>6</p>",
                        "<p>2</p>",
                        "<p>4</p>",
                        "<p>8</p>"
                    ],
                    options_hi: [
                        "<p>6</p>",
                        "<p>2</p>",
                        "<p>4</p>",
                        "<p>8</p>"
                    ],
                    solution_en: "<p>6.(a)&nbsp;<br>BORN &rarr; 8234&hellip;&hellip;.(i)<br>NORM &rarr; 6328&hellip;&hellip;.(ii)<br>From (i) and (ii) &lsquo;NOR&rsquo; and &lsquo;328&rsquo; are common. The code of &lsquo;M&rsquo; = &lsquo;6&rsquo;</p>",
                    solution_hi: "<p>6.(a) <br>BORN &rarr; 8234&hellip;&hellip;.(i)<br>NORM &rarr; 6328&hellip;&hellip;.(ii)<br>(i) और (ii) से \'NOR\' और \'328\' उभयनिष्ठ हैं। \'M\' का कोड = \'6\'</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "6",
                    question_en: "<p>7. In this question, three statements are given, followed by three conclusions numbered I, II and III. Assuming the statements to be true, even if they seem to be at variance with commonly known facts, decide which of the conclusions logically follow(s) from the statements.<br><strong>Statements :</strong><br>All cars are birds.<br>Some birds are plants.<br>All plants are leaves.<br><strong>Conclusions :</strong><br>I. Some birds are cars.<br>II. Some plants are birds.<br>III. Some leaves are plants.</p>",
                    question_hi: "<p>7. इस प्रश्न में, तीन कथन और उसके बाद तीन निष्कर्ष क्रमांक I, II और III दिए गए हैं। कथनों को सत्य मानते हुए, भले ही वे सामान्य रूप से ज्ञात तथ्यों से भिन्न प्रतीत होते हों, हों तय कीजिए कि कौन सा/से निष्कर्ष कथनों का तार्किक रूप से अनुसरण करता/करते है/हैं।<br><strong>कथन :</strong><br>सभी कारें, पक्षी हैं।<br>कुछ पक्षी, पौधे हैं।<br>सभी पौधे, पत्ते हैं।<br><strong>निष्कर्ष :</strong><br>I. कुछ पक्षी, कारें हैं।<br>II. कुछ पौधे, पक्षी हैं।<br>III. कुछ पत्ते, पौधे हैं।</p>",
                    options_en: [
                        "<p>Only conclusions I and III follow.</p>",
                        "<p>None of the conclusions follows.</p>",
                        "<p>All the conclusions follow.</p>",
                        "<p>Only conclusions II and III follow.</p>"
                    ],
                    options_hi: [
                        "<p>केवल निष्कर्ष I और III अनुसरण करते हैं।</p>",
                        "<p>कोई भी निष्कर्ष अनुसरण नहीं करता है।</p>",
                        "<p>सभी निष्कर्ष अनुसरण करते हैं।</p>",
                        "<p>केवल निष्कर्ष II और III अनुसरण करते हैं।</p>"
                    ],
                    solution_en: "<p>7.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741669104398.png\" alt=\"rId19\" width=\"187\" height=\"97\"><br>All the conclusions follow.</p>",
                    solution_hi: "<p>7.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741669104510.png\" alt=\"rId20\" width=\"221\" height=\"115\"><br>सभी निष्कर्ष अनुसरण करते हैं।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "6",
                    question_en: "<p>8. Select the option that is related to the fifth letter-cluster in the same way as the second letter-cluster is related to the first letter-cluster and the fourth letter-cluster is related to the third letter-cluster.<br>ISLAND : ANDISL :: EITHER : HEREIT :: IMPACT : ?</p>",
                    question_hi: "<p>8. उस विकल्प का चयन कीजिए, जो पाँचवें अक्षर-समूह से उसी प्रकार संबंधित है, जिस प्रकार दूसरा अक्षर-समूह, पहले अक्षर-समूह से संबंधित है और चौथा अक्षर-समूह, तीसरे अक्षर-समूह से संबंधित है।<br>ISLAND : ANDISL :: EITHER : HEREIT :: IMPACT : ?</p>",
                    options_en: [
                        "<p>ACTIMP</p>",
                        "<p>TCAPMI</p>",
                        "<p>ICAPMT</p>",
                        "<p>IPMCTA</p>"
                    ],
                    options_hi: [
                        "<p>ACTIMP</p>",
                        "<p>TCAPMI</p>",
                        "<p>ICAPMT</p>",
                        "<p>IPMCTA</p>"
                    ],
                    solution_en: "<p>8.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741669104661.png\" alt=\"rId21\" width=\"182\" height=\"104\">, <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741669104829.png\" alt=\"rId22\" width=\"174\" height=\"105\"><br>Similarly,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741669104993.png\" alt=\"rId23\" width=\"184\" height=\"117\"></p>",
                    solution_hi: "<p>8.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741669104661.png\" alt=\"rId21\" width=\"182\" height=\"104\">, <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741669104829.png\" alt=\"rId22\" width=\"174\" height=\"105\"><br>इसी प्रकार,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741669104993.png\" alt=\"rId23\" width=\"184\" height=\"117\"></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "6",
                    question_en: "<p>9. What should come in place of the question mark (?) in the given series?<br>91, 100, 109, 118, 127, ?</p>",
                    question_hi: "<p>9. दी गई श्रृंखला में प्रश्न चिह्न (?) के स्थान पर क्या आना चाहिए?<br>91, 100, 109, 118, 127, ?</p>",
                    options_en: [
                        "<p>136</p>",
                        "<p>135</p>",
                        "<p>137</p>",
                        "<p>138</p>"
                    ],
                    options_hi: [
                        "<p>136</p>",
                        "<p>135</p>",
                        "<p>137</p>",
                        "<p>138</p>"
                    ],
                    solution_en: "<p>9.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741669105211.png\" alt=\"rId24\" width=\"239\" height=\"80\"></p>",
                    solution_hi: "<p>9.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741669105211.png\" alt=\"rId24\" width=\"239\" height=\"80\"></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "6",
                    question_en: "<p>10. Three of the following four are alike in a certain way and thus form a group. Which is the one that does NOT belong to that group? <br>(<strong>Note :</strong> The odd one out is not based on the number of consonants/vowels or their position in the letter cluster.)</p>",
                    question_hi: "<p>10. निम्नलिखित चार अक्षर-समूहों में से तीन अक्षर-समूह एक निश्चित प्रकार से समान हैं और इस प्रकार एक समूह बनाते हैं। वह कौन-सा अक्षर-समूह है जो उस समूह से संबंधित नहीं है?<br>(<strong>ध्यान दें : </strong>असंगत अक्षर-समूह, उस अक्षर-समूह में व्यंजनों/स्वरों की संख्या या उनके स्थान पर आधारित नहीं है।)</p>",
                    options_en: [
                        "<p>BEG</p>",
                        "<p>JMN</p>",
                        "<p>TWY</p>",
                        "<p>MPR</p>"
                    ],
                    options_hi: [
                        "<p>BEG</p>",
                        "<p>JMN</p>",
                        "<p>TWY</p>",
                        "<p>MPR</p>"
                    ],
                    solution_en: "<p>10.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741669105333.png\" alt=\"rId25\" width=\"130\" height=\"76\">,<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741669105526.png\" alt=\"rId26\" width=\"134\" height=\"78\">,<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741669105654.png\" alt=\"rId27\" width=\"134\" height=\"78\"><br>but <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741669105787.png\" alt=\"rId28\" width=\"148\" height=\"86\"></p>",
                    solution_hi: "<p>10.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741669105333.png\" alt=\"rId25\" width=\"130\" height=\"76\">,<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741669105526.png\" alt=\"rId26\" width=\"130\" height=\"76\">,<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741669105654.png\" alt=\"rId27\" width=\"130\" height=\"76\"><br>लेकिन,<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741669105787.png\" alt=\"rId28\" width=\"148\" height=\"86\"><br><br></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "6",
                    question_en: "<p>11. Select the set in which the numbers are related in the same way as are the numbers of the following sets.<br>(<strong>NOTE : </strong>Operations should be performed on the whole numbers, without breaking&nbsp;down the numbers into its constituent digits. E.g., 13 - Operations on 13 such as&nbsp;adding/deleting/multiplying etc. to 13 can be performed. Breaking down 13 into 1 and 3&nbsp;and then performing mathematical operations on 1 and 3 is not allowed.)<br>(10, 5, 20)<br>(14, 7, 28)</p>",
                    question_hi: "<p>11. उस समुच्चय का चयन कीजिए जिसमें संख्याएं ठीक उसी प्रकार संबंधित हैं जिस प्रकार दिए गए समुच्चयों की संख्याएं संबंधित हैं।<br>(<strong>ध्यान दें : </strong>संख्याओं को उनके घटक अंकों में अलग-अलग किए बिना पूर्ण संख्याओं पर संक्रियाएं की जानी चाहिए। उदाहरण के लिए संख्या 13- संख्या 13 पर संक्रियाएं जैसे 13 को जोड़ना/घटाना/गुणा करना आदि किया जा सकता है। 13 को 1 और 3 में अलग-अलग करने की और फिर 1 और 3 पर गणितीय संक्रियाएं करने की अनुमति नहीं है।)<br>(10, 5, 20)<br>(14, 7, 28)</p>",
                    options_en: [
                        "<p>(48, 24, 58)</p>",
                        "<p>(22, 11, 44)</p>",
                        "<p>(54, 18, 72)</p>",
                        "<p>(33, 11, 44)</p>"
                    ],
                    options_hi: [
                        "<p>(48, 24, 58)</p>",
                        "<p>(22, 11, 44)</p>",
                        "<p>(54, 18, 72)</p>",
                        "<p>(33, 11, 44)</p>"
                    ],
                    solution_en: "<p>11.(b) <br><strong>Logic :-</strong> (2nd number) &times; 2 = (1st number) , (2nd number) &times; 4 = (3rd number)<br>(10, 5 ,20) :- (5) &times; 2 = 10 , (5) &times; 4 = 20<br>(14 ,7 ,28) :- (7) &times; 2 = 14, (7) &times; 4 = 28<br>Similarly,<br>(22 , 11, 44) :- (11) &times; 2 = 22 , (11) &times; 4 = 44</p>",
                    solution_hi: "<p>11.(b) <br><strong>तर्क:-</strong> (दूसरी संख्या) &times; 2 = (पहली संख्या), (दूसरी संख्या) &times; 4 = (तीसरी संख्या)<br>(10, 5 ,20) :- (5) &times; 2 = 10 , (5) &times; 4 = 20<br>(14 ,7 ,28) :- (7) &times; 2 = 14, (7) &times; 4 = 28<br>इसी प्रकार,<br>(22 , 11, 44) :- (11) &times; 2 = 22 , (11) &times; 4 = 44</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "6",
                    question_en: "<p>12. How many triangles are there in the following figure?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741669105884.png\" alt=\"rId29\" width=\"168\" height=\"92\"></p>",
                    question_hi: "<p>2. नीचे दी गई आकृति में कितने त्रिभुज हैं?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741669105884.png\" alt=\"rId29\" width=\"168\" height=\"92\"></p>",
                    options_en: [
                        "<p>6</p>",
                        "<p>5</p>",
                        "<p>7</p>",
                        "<p>10</p>"
                    ],
                    options_hi: [
                        "<p>6</p>",
                        "<p>5</p>",
                        "<p>7</p>",
                        "<p>10</p>"
                    ],
                    solution_en: "<p>12.(d)<br><img src=\"data:image/png;base64,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\" width=\"216\" height=\"126\"><br>There are 10 triangle <br>AMC , BKC , CDI , CEF , ABJ , ADH , LED , EBO , AEG , ANE</p>",
                    solution_hi: "<p>12.(d)<br><img src=\"data:image/png;base64,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\"><br>10 त्रिभुज हैं<br>AMC , BKC , CDI , CEF , ABJ , ADH , LED , EBO , AEG , ANE</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "6",
                    question_en: "<p>13. The position(s) of how many letters will remain unchanged if all the letters in the word &lsquo;MEADOW&rsquo; are arranged in English alphabetical order?</p>",
                    question_hi: "<p>13. यदि &lsquo;MEADOW&rsquo; शब्द के सभी अक्षरों को अंग्रेजी वर्णमाला क्रम में व्यवस्थित किया जाए, तो कितने अक्षरों की स्थिति में कोई बदलाव नहीं होगा?</p>",
                    options_en: [
                        "<p>None</p>",
                        "<p>Two</p>",
                        "<p>One</p>",
                        "<p>Three</p>"
                    ],
                    options_hi: [
                        "<p>एक भी नहीं</p>",
                        "<p>दो</p>",
                        "<p>एक</p>",
                        "<p>तीन</p>"
                    ],
                    solution_en: "<p>13.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741669106071.png\" alt=\"rId31\" width=\"210\" height=\"116\"><br>The position of two letters are not changed.</p>",
                    solution_hi: "<p>13.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741669106071.png\" alt=\"rId31\" width=\"210\" height=\"116\"><br>दो अक्षरों का स्थान नहीं बदला गया है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "6",
                    question_en: "<p>14. 96 is related to 16 following a certain logic. Following the same logic, 156 is related to&nbsp;26. To which of the following is 228 related following the same logic?<br>(<strong>NOTE :</strong> Operations should be performed on the whole numbers, without breaking&nbsp;down the numbers into its constituent digits. E.g. 13- Operations on 13 such as&nbsp;adding /deleting /multiplying etc., to 13 can be performed. Breaking down 13 into 1 and&nbsp;3 and then performing mathematical operations on 1 and 3 is not allowed.)</p>",
                    question_hi: "<p>14. एक निश्चित तर्क का अनुसरण करते हुए 96, 16 से संबंधित है। उसी तर्क का अनुसरण करते हुए 156, 26 से संबंधित है। उसी तर्क का अनुसरण करते हुए, 228 निम्नलिखित में से किससे संबंधित है?<br>(<strong>ध्यान दें :</strong> संख्याओं को उनके घटक अंकों में अलग-अलग किए बिना पूर्ण संख्याओं पर संक्रियाएं की जानी चाहिए। उदाहरण के लिए संख्या 13- संख्या 13 पर संक्रियाएं जैसे 13 को जोड़ना/घटाना/गुणा करना आदि किया जा सकता है। 13 को 1 और 3 में अलग-अलग करने की और फिर 1 और 3 पर गणितीय संक्रियाएं करने की अनुमति नहीं है।)</p>",
                    options_en: [
                        "<p>42</p>",
                        "<p>38</p>",
                        "<p>36</p>",
                        "<p>34</p>"
                    ],
                    options_hi: [
                        "<p>42</p>",
                        "<p>38</p>",
                        "<p>36</p>",
                        "<p>34</p>"
                    ],
                    solution_en: "<p>14.(b) <br><strong>Logic :-</strong> (<math display=\"inline\"><mfrac><mrow><msup><mrow><mn>1</mn></mrow><mrow><mi>s</mi><mi>t</mi></mrow></msup><mi>n</mi><mi>o</mi><mo>.</mo></mrow><mrow><mn>6</mn></mrow></mfrac></math>) = (2<sup>nd</sup> no.)<br>(96 , 16) :- (<math display=\"inline\"><mfrac><mrow><mn>96</mn></mrow><mrow><mn>6</mn></mrow></mfrac></math>) = 16<br>(156 ,26) :- (<math display=\"inline\"><mfrac><mrow><mn>156</mn></mrow><mrow><mn>6</mn></mrow></mfrac></math>) = 26<br>Similarly,<br>(228 , ?) :- (<math display=\"inline\"><mfrac><mrow><mn>228</mn></mrow><mrow><mn>6</mn></mrow></mfrac></math>) = 38</p>",
                    solution_hi: "<p>14.(b)<br><strong>तर्क :-</strong> (<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2346;&#2361;&#2354;&#2368;</mi><mi mathvariant=\"normal\">&#160;</mi><mi>&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</mi><mi mathvariant=\"normal\">&#160;</mi></mrow><mn>6</mn></mfrac></math>) = (दूसरी संख्या)<br>(96 , 16) :- (<math display=\"inline\"><mfrac><mrow><mn>96</mn></mrow><mrow><mn>6</mn></mrow></mfrac></math>) = 16<br>(156 ,26) :- (<math display=\"inline\"><mfrac><mrow><mn>156</mn></mrow><mrow><mn>6</mn></mrow></mfrac></math>) = 26<br>इसी प्रकार,<br>(228 , ?) :- (<math display=\"inline\"><mfrac><mrow><mn>228</mn></mrow><mrow><mn>6</mn></mrow></mfrac></math>) = 38</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "6",
                    question_en: "<p>15. What should come in place of ? in the given series based on the English alphabetical order?<br>QJC ,ATM, KDW, UNG ?</p>",
                    question_hi: "<p>15. अंग्रेजी वर्णमाला क्रम के आधार पर दी गई श्रृंखला में ?\' के स्थान पर क्या आना चाहिए?<br>QJC, ATM, KDW, UNG ?</p>",
                    options_en: [
                        "<p>SBA</p>",
                        "<p>CWO</p>",
                        "<p>BAA</p>",
                        "<p>EXQ</p>"
                    ],
                    options_hi: [
                        "<p>SBA</p>",
                        "<p>CWO</p>",
                        "<p>BAA</p>",
                        "<p>EXQ</p>"
                    ],
                    solution_en: "<p>15.(d) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741669106262.png\" alt=\"rId32\" width=\"318\" height=\"99\"></p>",
                    solution_hi: "<p>15.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741669106262.png\" alt=\"rId32\" width=\"318\" height=\"99\"></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "16",
                    section: "6",
                    question_en: "<p>16. A paper is folded and cut as shown below. How will it appear when unfolded ?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741669106378.png\" alt=\"rId33\" width=\"301\" height=\"102\"></p>",
                    question_hi: "<p>16. नीचे दर्शाए गए अनुसार एक कागज को मोड़कर काटा जाता है। खोलने पर यह कैसा दिखाई देगा?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741669106378.png\" alt=\"rId33\" width=\"295\" height=\"100\"></p>",
                    options_en: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741669106475.png\" alt=\"rId34\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741669106568.png\" alt=\"rId35\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741669106665.png\" alt=\"rId36\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741669106843.png\" alt=\"rId37\"></p>"
                    ],
                    options_hi: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741669106475.png\" alt=\"rId34\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741669106568.png\" alt=\"rId35\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741669106665.png\" alt=\"rId36\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741669106843.png\" alt=\"rId37\"></p>"
                    ],
                    solution_en: "<p>16.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741669106843.png\" alt=\"rId37\"></p>",
                    solution_hi: "<p>16.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741669106843.png\" alt=\"rId37\"></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "17",
                    section: "6",
                    question_en: "<p>17. Three statements are followed by conclusions numbered I, II. You have to consider these statements to be true, even if they seem to be at variance with commonly known facts. Decide which of the given conclusions logically follow/s from the given statement. <br><strong>Statements :</strong> <br>All fire is water. <br>Some water is earth. <br>No earth is air. <br><strong>Conclusion (I) : </strong>Some fire is earth. <br><strong>Conclusion (II) :</strong> Some fire is air.</p>",
                    question_hi: "<p>17. तीन कथनों के बाद ।, ॥ क्रमांकित निष्कर्ष दिए गए हैं। आपको इन कथनों को सत्य मानना होगा, चाहे वे समान्यतः ज्ञात तथ्यों से अलग प्रतीत होते हों। आपको निश्चय करना है कि कौन-सा/कौन-से निष्कर्ष तार्किक रूप से दिए गए कथन/कथनों के अनुसार है/हैं।<br><strong>कथनः</strong><br>सभी आग, पानी है।<br>कुछ पानी, पृथ्वी है।<br>कोई पृथ्वी, वायु नहीं है।<br><strong>निष्कर्ष (I): </strong>कुछ आग, पृथ्वी है।<br><strong>निष्कर्ष (II): </strong>कुछ आग, वायु है।</p>",
                    options_en: [
                        "<p>Only conclusion (I) follows.</p>",
                        "<p>Neither conclusion (I) nor (II) follows.</p>",
                        "<p>Only conclusion (II) follows.</p>",
                        "<p>Both conclusions (I) and (II) follow.</p>"
                    ],
                    options_hi: [
                        "<p>केवल निष्कर्ष (I) कथनों के अनुसार है।</p>",
                        "<p>न तो निष्कर्ष (I) और न ही (II) कथनों के अनुसार है।</p>",
                        "<p>केवल निष्कर्ष (II) कथनों के अनुसार है।</p>",
                        "<p>दोनों निष्कर्ष (I) और (II) कथनों के अनुसार हैं।</p>"
                    ],
                    solution_en: "<p>17.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741669107008.png\" alt=\"rId38\" width=\"387\" height=\"78\"><br>Neither conclusion I nor II follow.</p>",
                    solution_hi: "<p>17.(b) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741669107219.png\" alt=\"rId39\" width=\"383\" height=\"77\"><br>न तो निष्कर्ष I और न ही II अनुसरण करता है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "18",
                    section: "6",
                    question_en: "<p>18. Select the option figure in which the given figure is embedded as its part (rotation is&nbsp;NOT allowed).<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741669107352.png\" alt=\"rId40\" width=\"90\" height=\"125\"></p>",
                    question_hi: "<p>18. उस आकृति का चयन कीजिए जिसमें दी गई आकृति उसके भाग के रूप में निहित है (घूर्णन की&nbsp;अनुमति नहीं है)।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741669107352.png\" alt=\"rId40\" width=\"90\" height=\"125\"></p>",
                    options_en: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741669107455.png\" alt=\"rId41\" width=\"110\" height=\"103\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741669107547.png\" alt=\"rId42\" width=\"110\" height=\"111\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741669107655.png\" alt=\"rId43\" width=\"110\" height=\"107\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741669107754.png\" alt=\"rId44\" width=\"110\" height=\"107\"></p>"
                    ],
                    options_hi: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741669107455.png\" alt=\"rId41\" width=\"110\" height=\"103\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741669107547.png\" alt=\"rId42\" width=\"111\" height=\"112\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741669107655.png\" alt=\"rId43\" width=\"111\" height=\"108\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741669107754.png\" alt=\"rId44\" width=\"110\" height=\"107\"></p>"
                    ],
                    solution_en: "<p>18.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741669107859.png\" alt=\"rId45\" width=\"110\" height=\"107\"></p>",
                    solution_hi: "<p>18.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741669107859.png\" alt=\"rId45\" width=\"110\" height=\"107\"></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "19",
                    section: "6",
                    question_en: "<p>19. In a certain code language<br>A + B means &lsquo;A is the mother of B&rsquo;<br>A &ndash; B means &lsquo;A is the brother of B&rsquo;<br>A &times; B means &lsquo;A is the wife of B&rsquo;<br>A &divide; B means &lsquo;A is the father of B&rsquo;<br>Based on the above, how is G related to K if &lsquo;G + J &divide; H &times; I &minus; K&rsquo;?</p>",
                    question_hi: "<p>19. एक निश्चित कूट भाषा में<br>A + B का अर्थ A, B की मां है<br>A &ndash; B का अर्थ A, B का भाई है<br>A &times; B का अर्थ A, B की पत्नी है<br>A &divide; B का अर्थ A, B का पिता है<br>उपरोक्त केआधार पर, यदि G + J &divide; H &times; I - K है, तो G का K से क्या संबंध है?</p>",
                    options_en: [
                        "<p>Brother&rsquo;s wife&rsquo;s sister</p>",
                        "<p>Brother&rsquo;s wife&rsquo;s mother</p>",
                        "<p>Brother&rsquo;s wife&rsquo;s father&rsquo;s mother</p>",
                        "<p>Brother&rsquo;s wife&rsquo;s father&rsquo;s sister</p>"
                    ],
                    options_hi: [
                        "<p>भाई की पत्नी की बहन</p>",
                        "<p>भाई की पत्नी की मां</p>",
                        "<p>भाई की पत्नी के पिता की मां</p>",
                        "<p>भाई की पत्नी के पिता की बहन</p>"
                    ],
                    solution_en: "<p>19.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741669107977.png\" alt=\"rId46\" width=\"196\" height=\"180\"><br>G is the mother of the father of K&rsquo;s brother\'s wife.</p>",
                    solution_hi: "<p>19.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741669107977.png\" alt=\"rId46\" width=\"196\" height=\"180\"><br>G, K के भाई की पत्नी के पिता की माँ है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "20",
                    section: "6",
                    question_en: "<p>20. Six letters J, K, L, M, N and O are written on different faces of a dice. Two positions of this dice are shown in the figure below. Find the letter on the face opposite to N. <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741669108121.png\" alt=\"rId47\" width=\"252\" height=\"90\"></p>",
                    question_hi: "<p>20. एक पासे के विभिन्न फलकों पर छह अक्षर J, K, L, M, N और O लिखे गये हैं। इस पासे की दो स्थितियों को नीचे चित्र में दर्शाया गया है। N के विपरीत फलक पर आने वाले अक्षर की पहचान कीजिए।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741669108121.png\" alt=\"rId47\" width=\"252\" height=\"90\"></p>",
                    options_en: [
                        "<p>M</p>",
                        "<p>L</p>",
                        "<p>J</p>",
                        "<p>K</p>"
                    ],
                    options_hi: [
                        "<p>M</p>",
                        "<p>L</p>",
                        "<p>J</p>",
                        "<p>K</p>"
                    ],
                    solution_en: "<p>20.(b)<br>From the two dice the opposite pairs are<br>L &harr; N , J &harr; M , K &harr; O</p>",
                    solution_hi: "<p>20.(b)<br>दो पासों के विपरीत युग्म हैं<br>L &harr; N , J &harr; M , K &harr; O</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "21",
                    section: "6",
                    question_en: "<p>21. Six students - P, Q, R, S T and U - are sitting around a circular table, facing the centre.&nbsp;<br>Q is sitting second to the left of U. <br>T is sitting third to the right of P. <br>P is the immediate neighbour of Q and U. <br>S is not the neighbour of U. Who among the following is the neighbour of both U and T?</p>",
                    question_hi: "<p>21. छह विद्यार्थी - P, Q, R, S, T और U - एक गोल मेज के परित: केंद्र की ओर अभिमुख होकर बैठे हैं।<br>Q, U के बायें से दूसरे स्थान पर बैठा है।<br>T, P के दायें से तीसरे स्थान पर बैठा है।<br>Q और U का निकटतम पड़ोसी P है।<br>S, U का पड़ोसी नहीं है।<br>निम्नलिखित में से कौन U और T दोनों का पड़ोसी है?</p>",
                    options_en: [
                        "<p>Q</p>",
                        "<p>S</p>",
                        "<p>R</p>",
                        "<p>P</p>"
                    ],
                    options_hi: [
                        "<p>Q</p>",
                        "<p>S</p>",
                        "<p>R</p>",
                        "<p>P</p>"
                    ],
                    solution_en: "<p>21.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741669108219.png\" alt=\"rId48\" width=\"195\" height=\"148\"></p>",
                    solution_hi: "<p>21.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741669108219.png\" alt=\"rId48\" width=\"195\" height=\"148\"></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "22",
                    section: "6",
                    question_en: "<p>22. If A stands for &lsquo;&divide;\', \'B\' stands for \'&times;\', \'C\' stands for \'+\' and \'D\' stands for &lsquo;-&rsquo; what will come in place of the question mark (?) in the following equation ?<br>28 B 7 D 176 A 8 C 13 = ?</p>",
                    question_hi: "<p>22. यदि \'A\' का अर्थ\', &divide;\' B\' का अर्थ \'&times;\', \'C\' का अर्थ \'+\' और \'D\' का अर्थ &lsquo;-&rsquo; है, तो निम्नलिखित समीकरण में प्रश्न चिन्ह (?) के स्थान पर क्या आएगा ?<br>28 B 7 D 176 A 8 C 13 = ?</p>",
                    options_en: [
                        "<p>187</p>",
                        "<p>186</p>",
                        "<p>184</p>",
                        "<p>185</p>"
                    ],
                    options_hi: [
                        "<p>187</p>",
                        "<p>186</p>",
                        "<p>184</p>",
                        "<p>185</p>"
                    ],
                    solution_en: "<p>22.(a) <strong>Given :-</strong> 28 B 7 D 176 A 8 C 13 <br>As per given instruction after interchanging the letter with sign we get<br>28 &times; 7 - 176 &divide; 8 + 13<br>196 - 22 + 13 <br>196 - 9 = 187</p>",
                    solution_hi: "<p>22.(a) <strong>दिया गया :-</strong> 28 B 7 D 176 A 8 C 13 <br>दिए गए निर्देश के अनुसार अक्षर को चिन्ह से बदलने पर हमें प्राप्त होता है<br>28 &times; 7 - 176 &divide; 8 + 13<br>196 - 22 + 13 <br>196 - 9 = 187</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "23",
                    section: "6",
                    question_en: "<p>23. Select the correct mirror image of the given figure when the mirror is placed at MN, as shown.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741669108342.png\" alt=\"rId49\" width=\"144\" height=\"161\"></p>",
                    question_hi: "<p>23. दी गई आकृति के उस सही दर्पण प्रतिबिंब का चयन कीजिए, जो नीचे दर्शाए गए अनुसार दर्पण को MN पर रखने पर बनेगा।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741669108342.png\" alt=\"rId49\" width=\"144\" height=\"161\"></p>",
                    options_en: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741669108448.png\" alt=\"rId50\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741669108554.png\" alt=\"rId51\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741669108657.png\" alt=\"rId52\" width=\"116\" height=\"115\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741669108779.png\" alt=\"rId53\" width=\"116\" height=\"115\"></p>"
                    ],
                    options_hi: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741669108448.png\" alt=\"rId50\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741669108554.png\" alt=\"rId51\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741669108657.png\" alt=\"rId52\" width=\"116\" height=\"115\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741669108779.png\" alt=\"rId53\" width=\"114\" height=\"113\"></p>"
                    ],
                    solution_en: "<p>23.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741669108779.png\" alt=\"rId53\" width=\"115\" height=\"114\"></p>",
                    solution_hi: "<p>23.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741669108779.png\" alt=\"rId53\" width=\"115\" height=\"114\"></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "24",
                    section: "6",
                    question_en: "<p>24 The position of how many letters will change, if each of the letters in the word &lsquo;SYSTEM&rsquo; is arranged in the English alphabetical order?</p>",
                    question_hi: "<p>24. यदि शब्द \'SYSTEM\' के प्रत्येक अक्षर को अँग्रेजी वर्णमाला के क्रम में व्यवस्थित किया जाए, तो कितने अक्षरों की स्थिति बदल जाएगी?</p>",
                    options_en: [
                        "<p>Five</p>",
                        "<p>Six</p>",
                        "<p>Three</p>",
                        "<p>Four</p>"
                    ],
                    options_hi: [
                        "<p>पाँच</p>",
                        "<p>छः</p>",
                        "<p>तीन</p>",
                        "<p>चार</p>"
                    ],
                    solution_en: "<p>24.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741669108916.png\" alt=\"rId54\" width=\"138\" height=\"79\"><br>The position of five letters changes.</p>",
                    solution_hi: "<p>24.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741669108916.png\" alt=\"rId54\" width=\"138\" height=\"79\"><br>पांच अक्षरों की स्थिति बदलती है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "25",
                    section: "18",
                    question_en: "<p>25. In a certain code language<br>A + B means &lsquo;A is the mother of B&rsquo;<br>A &ndash; B means &lsquo;A is the brother of B&rsquo;<br>A &times; B means &lsquo;A is the husband of B&rsquo;<br>A &divide; B means A is the father of B&rsquo;<br>Based on the above, how is K related to L if &lsquo;K &times; Q + R &minus; S &divide; L&rsquo;?</p>",
                    question_hi: "<p>25. एक निश्चित कूट भाषा में<br>A + B का अर्थ A, B की माता है<br>A &ndash; B का अर्थ A, B का भाई है<br>A &times; B का अर्थ A, B का पति है<br>A &divide; B का अर्थ A, B का पिता है<br>उपरोक्त केआधार पर, यदि K &times; Q + R - S &divide; L है, तो K का L से क्या संबंध है?</p>",
                    options_en: [
                        "<p>Father&rsquo;s brother</p>",
                        "<p>Father&rsquo;s father&rsquo;s brother</p>",
                        "<p>Father&rsquo;s mother&rsquo;s father</p>",
                        "<p>Father&rsquo;s father</p>"
                    ],
                    options_hi: [
                        "<p>पिता का भाई</p>",
                        "<p>पिता के पिता का भाई</p>",
                        "<p>पिता की माता के पिता</p>",
                        "<p>पिता के पिता</p>"
                    ],
                    solution_en: "<p>25.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741669109041.png\" alt=\"rId55\" width=\"155\" height=\"157\"><br>K is father&rsquo;s father of L.</p>",
                    solution_hi: "<p>25.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741669109041.png\" alt=\"rId55\" width=\"156\" height=\"158\"><br>K, L के पिता का पिता है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "26",
                    section: "18",
                    question_en: "<p>26. Which country partnered with India to co-design and co-produce electric propulsion systems for future warships in November 2024 ?</p>",
                    question_hi: "<p>26. नवंबर 2024 में भविष्य के युद्धपोतों के लिए इलेक्ट्रिक प्रणोदन प्रणाली के सह-डिजाइन और सह-उत्पादन के लिए किस देश ने भारत के साथ साझेदारी की है?</p>",
                    options_en: [
                        "<p>United States</p>",
                        "<p>United Kingdom</p>",
                        "<p>France</p>",
                        "<p>Germany</p>"
                    ],
                    options_hi: [
                        "<p>संयुक्त राज्य अमेरिका</p>",
                        "<p>यूनाइटेड किंगडम</p>",
                        "<p>फ्रांस</p>",
                        "<p>जर्मनी</p>"
                    ],
                    solution_en: "<p>26.(b) <strong>United Kingdom</strong>. Signed in Portsmouth during the third joint working group meeting on electric propulsion capability partnership, this initiative underscores the commitment to indigenous development of advanced naval technologies.</p>",
                    solution_hi: "<p>26.(b) <strong>यूनाइटेड किंगडम।</strong> इलेक्ट्रिक प्रणोदन क्षमता साझेदारी पर तीसरे संयुक्त कार्य समूह की बैठक के दौरान पोर्ट्समाउथ में हस्ताक्षरित, यह पहल उन्नत नौसेना प्रौद्योगिकियों के स्वदेशी विकास के प्रति प्रतिबद्धता को रेखांकित करती है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "27",
                    section: "18",
                    question_en: "<p>27. The north-west region of India receives rainfall in winter due to :</p>",
                    question_hi: "<p>27. भारत के उत्तर-पश्चिम क्षेत्र में सर्दियों में वर्षा किसके कारण होती है ?</p>",
                    options_en: [
                        "<p>western disturbances</p>",
                        "<p>harsh cold and snow storms</p>",
                        "<p>the summer monsoon</p>",
                        "<p>eastern disturbances</p>"
                    ],
                    options_hi: [
                        "<p>पश्चिमी विक्षोभ</p>",
                        "<p>कड़ाके की ठंड और बर्फीले तूफान</p>",
                        "<p>ग्रीष्मकालीन मानसून</p>",
                        "<p>पूर्वी विक्षोभ</p>"
                    ],
                    solution_en: "<p>27.(a) <strong>Western disturbances </strong>are weather phenomena that occur during the winter months, brought in by westerly winds from the Mediterranean region. These disturbances typically affect the north and north-western parts of India. Though the amount of winter rainfall, locally known as &lsquo;mahawat,&rsquo; is small, it plays a crucial role in the cultivation of rabi crops.</p>",
                    solution_hi: "<p>27.(a) <strong>पश्चिमी विक्षोभ</strong> मौसम की घटनाएँ हैं जो सर्दियों के महीनों में होती हैं, जो भूमध्य सागर क्षेत्र से आने वाली पश्चिमी हवाओं द्वारा लायी जाती हैं। ये विक्षोभ सामान्यतः भारत के उत्तरी और उत्तर-पश्चिमी भागों को प्रभावित करते हैं। सर्दियों में होने वाली वर्षा की मात्रा, जिसे स्थानीय रूप से \'महावट\' के रूप में जाना जाता है, कम होती है, लेकिन यह रबी फसलों की खेती में महत्वपूर्ण भूमिका निभाती है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "28",
                    section: "18",
                    question_en: "<p>28. Constitutions limit the power of government in many ways. The most common way of limiting the power of government is to specify certain __________ that all of us possess as citizens and which no government can ever be allowed to violate.</p>",
                    question_hi: "<p>28. संविधान कई तरह से सरकार की शक्तियों को सीमित करता है। सरकार के शासन को सीमित करने का सबसे सामान्य तरीका कुछ _________ को निर्दिष्ट करना है, जो हम सभी के पास नागरिक के रूप में है और जिसका उल्लंघन करने की अनुमति किसी भी सरकार को नहीं दी जा सकती है।</p>",
                    options_en: [
                        "<p>Fundamental Rights</p>",
                        "<p>Directive Principles of State Policy</p>",
                        "<p>Economic Duties</p>",
                        "<p>Fundamental Duties</p>"
                    ],
                    options_hi: [
                        "<p>मौलिक अधिकारों</p>",
                        "<p>राज्य के नीति निर्देशक सिद्धांतों</p>",
                        "<p>मौलिक कर्तव्यों</p>",
                        "<p>आर्थिक कर्तव्यों</p>"
                    ],
                    solution_en: "<p>28.(a) <strong>Fundamental Rights. </strong>The Fundamental Rights are enshrined in Part III (Magna Carta of India) of the Constitution (Articles 12-35). The Constitution of India provides for six Fundamental Rights: Right to equality (Articles 14&ndash;18), Right to freedom (Articles 19&ndash;22), Right against exploitation (Articles 23&ndash;24), Right to freedom of religion (Articles 25&ndash;28), Cultural and educational rights (Articles 29&ndash;30), and Right to constitutional remedies (Article 32).</p>",
                    solution_hi: "<p>28.(a) <strong>मौलिक अधिकारों।</strong> मौलिक अधिकार संविधान के भाग III (भारत का मैग्ना कार्टा) के अनुच्छेद (12 से 35) में निहित हैं। भारत के संविधान में छह मौलिक अधिकार दिए गए हैं: समानता का अधिकार (अनुच्छेद 14-18), स्वतंत्रता का अधिकार (अनुच्छेद 19-22), शोषण के विरुद्ध अधिकार (अनुच्छेद 23-24), धार्मिक स्वतंत्रता का अधिकार (अनुच्छेद 25-28), सांस्कृतिक एवं शैक्षिक अधिकार (अनुच्छेद 29-30), तथा संवैधानिक उपचारों का अधिकार (अनुच्छेद 32)।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "29",
                    section: "18",
                    question_en: "<p>29. The Ghode Modni dance, to celebrate the victory of warriors, is performed in which of the following states of India?</p>",
                    question_hi: "<p>29. योद्धाओं की जीत का जश्न मनाने के लिए घोडे मोदनी नृत्य भारत के निम्नलिखित में से किस राज्य में किया जाता है?</p>",
                    options_en: [
                        "<p>Goa</p>",
                        "<p>West Bengal</p>",
                        "<p>Kamataka</p>",
                        "<p>Tamil Nadu</p>"
                    ],
                    options_hi: [
                        "<p>गोवा</p>",
                        "<p>पश्चिम बंगाल</p>",
                        "<p>कर्नाटक</p>",
                        "<p>तमिल नाडु</p>"
                    ],
                    solution_en: "<p>29.(a) <strong>Goa.</strong> Dances of Indian States: Goa - Dashavatara, Dekhni, Dhalo, Dhangar, Fugdi, Goff, Kunbi. West Bengal - Gaudiya Nritya, Rabindra Nritya, Dhali and Raibenshe. Karnataka - Yakshagana, Kunitha, Veeragase, Bolak-aat, Ummatt-aat and Bayalata. Tamil Nadu - Karakattam, Kummi, Mayil Aattam, Kolattam, Poikkal Kudirai Aattam, Theru Koothu and Villu Pattu.</p>",
                    solution_hi: "<p>29.(a) <strong>गोवा।</strong> भारतीय राज्यों के नृत्य: गोवा - दशावतार, देखनी, ढालो, धनगर, फुगड़ी, गोफ, कुनबी। पश्चिम बंगाल - गौड़ीय नृत्य, रवीन्द्र नृत्य, धाली और रायबेन्शे। कर्नाटक - यक्षगान, कुनिथा, वीरागासे, बोलक-आट, उम्मट-आट और बयालता। तमिलनाडु - कराकट्टम, कुम्मी, मयिल अट्टम, कोलाट्टम, पोइक्कल कुदिराई अट्टम, थेरु कूथु और विल्लू पट्टू।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "30",
                    section: "18",
                    question_en: "<p>30. Which Commission drew the boundary line between India and Pakistan?</p>",
                    question_hi: "<p>30. किस आयोग ने भारत और पाकिस्तान के बीच सीमा रेखा खींची थी?</p>",
                    options_en: [
                        "<p>Radcliffe Boundary Commission</p>",
                        "<p>Gandhi Irwin Pact</p>",
                        "<p>Hilton Young Commission</p>",
                        "<p>Bretton woods Commission</p>"
                    ],
                    options_hi: [
                        "<p>रेडक्लिफ सीमा आयोग</p>",
                        "<p>गांधी इरविन समझौता</p>",
                        "<p>हिल्टन यंग आयोग</p>",
                        "<p>ब्रेटन वुड्स आयोग</p>"
                    ],
                    solution_en: "<p>30.(a) <strong>Radcliffe Boundary Commission. </strong>The \"Radcliffe Line\" became the border between India and Pakistan the partition of India on 17 August 1947. The Border Commission headed by Sir Cyril Radcliffe. Gandhi-Irwin Pact (1931 - 32) was an agreement between Mahatma Gandhi and Lord Irwin. The Hilton Young Commission (1926) recommended the establishment of the \'Reserve Bank of India\'. The Bretton Woods Committee (BWC in 1944) created two organizations - the International Monetary Fund (IMF) and the World Bank.</p>",
                    solution_hi: "<p>30.(a) <strong>रेडक्लिफ सीमा आयोग।</strong> 17 अगस्त 1947 को भारत के विभाजन के बाद \"रेडक्लिफ़ रेखा\" भारत और पाकिस्तान के बीच की सीमा बन गई। सर सिरिल रेडक्लिफ़ की अध्यक्षता में सीमा आयोग का गठन किया गया। गांधी-इरविन समझौता (1931 - 32) महात्मा गांधी और लॉर्ड इरविन के बीच हुआ एक समझौता था। हिल्टन यंग आयोग (1926) ने \'भारतीय रिज़र्व बैंक\' की स्थापना की सिफ़ारिश की। ब्रेटन वुड्स समिति (1944 में) ने दो संगठन बनाए- अंतर्राष्ट्रीय मुद्रा कोष (IMF) और विश्व बैंक।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "31",
                    section: "18",
                    question_en: "<p>31. Which of the following festivals is celebrated by the Jain community in Pawapuri, Bihar to commemorate the attainment of Nirvana by Lord Mahavira?</p>",
                    question_hi: "<p>31. भगवान महावीर द्वारा निर्वाण प्राप्त करने के उपलक्ष्य में बिहार के पावापुरी में जैन समुदाय द्वारा निम्नलिखित में से कौन-सा त्योहार मनाया जाता है?</p>",
                    options_en: [
                        "<p>Rot Teej</p>",
                        "<p>Dev Diwali</p>",
                        "<p>Gyan Panchami</p>",
                        "<p>Paryushan</p>"
                    ],
                    options_hi: [
                        "<p>रोट तीज</p>",
                        "<p>देव दीपावली</p>",
                        "<p>ज्ञान पंचमी</p>",
                        "<p>पर्यूषण</p>"
                    ],
                    solution_en: "<p>31.(b) <strong>Dev Diwali. </strong>Jain Festivals: Paryushan parva - A jain festival of reflection and seeking forgiveness for one\'s sins. Jnan Panchami - The holy day for acquiring knowledge. Maun Ekadashi - The holy day for observing silence. Paush dashami - This day is famous as the birthday of Bhagwan Parshwanath.</p>",
                    solution_hi: "<p>31.(b) <strong>देव दिवाली ।</strong> जैन उत्सव: पर्युषण पर्व - चिंतन और अपने पापों के लिए क्षमा मांगने का एक जैन त्योहार। ज्ञान पंचमी - ज्ञान प्राप्त करने का पवित्र दिन। मौन एकादशी - मौन धारण करने का पवित्र दिन। पौष दशमी - यह दिन भगवान पार्श्वनाथ के जन्मदिन के रूप में प्रसिद्ध है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "32",
                    section: "18",
                    question_en: "<p>32. Articles 214 to 231 in Part VI of the Indian Constitution deal with the __________.</p>",
                    question_hi: "<p>32. भारतीय संविधान के भाग VI में अनुच्छेद 214 से 231 __________ से संबंधित हैं।</p>",
                    options_en: [
                        "<p>High Courts</p>",
                        "<p>District Courts</p>",
                        "<p>Subordinate Courts</p>",
                        "<p>Supreme Court</p>"
                    ],
                    options_hi: [
                        "<p>उच्च न्यायालय</p>",
                        "<p>जिला न्यायालय</p>",
                        "<p>अधीनस्थ न्यायालय</p>",
                        "<p>उच्चतम न्यायालय</p>"
                    ],
                    solution_en: "<p>32.(a) <strong>High Courts. </strong>Articles 214 to 231 in Part VI of the Constitution deal with the organization, independence, jurisdiction, powers, and procedures of the high courts. Article 214 : Establishes that there shall be a High Court for each State. Articles 226 : Power of High Courts to issue certain writs. Articles 227 : Power of superintendence over all courts by the High Court. Articles 229 : Officers and servants and the expenses of High Courts. Articles 230 : Extension of jurisdiction of High Courts to Union territories.</p>",
                    solution_hi: "<p>32.(a) <strong>उच्च न्यायालय। </strong>संविधान के भाग VI में अनुच्छेद 214 से 231 उच्च न्यायालयों के संगठन, स्वतंत्रता, अधिकार क्षेत्र, शक्तियों और प्रक्रियाओं से संबंधित हैं। अनुच्छेद 214 : यह स्थापित करता है कि प्रत्येक राज्य के लिए एक उच्च न्यायालय होगा। अनुच्छेद 226 : कुछ रिट जारी करने की उच्च न्यायालयों की शक्ति। अनुच्छेद 227 : उच्च न्यायालय द्वारा सभी न्यायालयों पर अधीक्षण की शक्ति। अनुच्छेद 229 : उच्च न्यायालयों के अधिकारी और सेवक तथा व्यय। अनुच्छेद 230 : उच्च न्यायालयों के अधिकार क्षेत्र का केंद्र शासित प्रदेशों तक विस्तार।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "33",
                    section: "18",
                    question_en: "<p>33. Who won the Best FIFA Men\'s Player award in 2024?</p>",
                    question_hi: "<p>33. 2024 में सर्वश्रेष्ठ फीफा पुरुष खिलाड़ी पुरस्कार किसे मिला?</p>",
                    options_en: [
                        "<p>Lionel Messi</p>",
                        "<p>Erling Haaland</p>",
                        "<p>Vin&iacute;cius J&uacute;nior</p>",
                        "<p>Jude Bellingham</p>"
                    ],
                    options_hi: [
                        "<p>लियोनेल मेस्सी</p>",
                        "<p>अर्लिंग हैलैंड</p>",
                        "<p>विनीसियस जूनियर</p>",
                        "<p>जूड बेलिंघम</p>"
                    ],
                    solution_en: "<p>33.(c) <strong>Vin&iacute;cius J&uacute;nior. </strong>Vin&iacute;cius J&uacute;nior was playing for Real Madrid when he received the award.</p>",
                    solution_hi: "<p>33.(c) <strong>विनीसियस जूनियर।</strong> विनीसियस जूनियर ने यह पुरस्कार रियल मैड्रिड के लिए खेलते हुए प्राप्त किया।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "34",
                    section: "18",
                    question_en: "<p>34. Which Indian musician is the first singer to get the highest civilian honour, the Bharat Ratna?</p>",
                    question_hi: "<p>34. निम्नलिखित में से कौन-सा/कौन-सी भारतीय संगीतकार, सर्वोच्च नागरिक सम्मान, भारत रत्न प्राप्त करने वाला/वाली पहला/पहली गायक/गायिका है?</p>",
                    options_en: [
                        "<p>Zakir Hussain</p>",
                        "<p>A.R. Rahman</p>",
                        "<p>M.S. Subbulakshmi</p>",
                        "<p>R.D. Burman</p>"
                    ],
                    options_hi: [
                        "<p>ज़ाकिर हुसैन</p>",
                        "<p>ए. आर. रहमान</p>",
                        "<p>एम. एस. सुब्बुलक्ष्मी</p>",
                        "<p>आर. डी. बर्मन</p>"
                    ],
                    solution_en: "<p>34.(c) <strong>M.S. Subbulakshmi. </strong>Her awards: Bharat Ratna (1998), Padma Vibhushan (1975), Padma Bhushan (1954), Sangeet Natak Akademi Award for Carnatic Music - Vocal (1956). Bharat Ratna Award was instituted in 1954.</p>",
                    solution_hi: "<p>34.(c) <strong>एम. एस. सुब्बुलक्ष्मी। </strong>उनके पुरस्कार: भारत रत्न (1998), पद्म विभूषण (1975), पद्म भूषण (1954), कर्नाटक संगीत - गायन के लिए संगीत नाटक अकादमी पुरस्कार (1956)। भारत रत्न पुरस्कार की स्थापना 1954 में की गई थी।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "35",
                    section: "18",
                    question_en: "<p>35. Which Indian personality is known as the Mozart of Madras?</p>",
                    question_hi: "<p>35. निम्नलिखित में से किस भारतीय शख़्सियत को मोजार्ट ऑफ मद्रास (Mozart of Madras) कहा जाता है?</p>",
                    options_en: [
                        "<p>K. J. Yesudas</p>",
                        "<p>Kavita Krishnamurthy</p>",
                        "<p>A.R Rahman</p>",
                        "<p>Lata Mangeshkar</p>"
                    ],
                    options_hi: [
                        "<p>के. जे. येसुदास</p>",
                        "<p>कविता कृष्णमूर्ति</p>",
                        "<p>ए. आर. रहमान</p>",
                        "<p>लता मंगेशकर</p>"
                    ],
                    solution_en: "<p>35.(c) <strong>A.R Rahman. </strong>He got this nickname from Time Magazine. Awards: Padma Shri (2000), Padma Bhushan (2010), Six National Film Awards, Two Grammy Awards, A Golden Globe Award and fifteen Filmfare Awards. Other musicians and their nicknames : Lata Mangeshkar - &ldquo;Queen of melody&rdquo;. Kishore Kumar - &ldquo;Kishore Da&rdquo;. Usha Uthup - &ldquo;The Queen of Indipop&rdquo;.</p>",
                    solution_hi: "<p>35.(c) <strong>ए.आर. रहमान।</strong> उन्हें यह उपनाम टाइम मैगज़ीन से मिला था। पुरस्कार: पद्म श्री (2000), पद्म भूषण (2010), छ: राष्ट्रीय फ़िल्म पुरस्कार, दो ग्रैमी पुरस्कार, एक गोल्डन ग्लोब पुरस्कार और पंद्रह फ़िल्मफ़ेयर पुरस्कार। अन्य संगीतकार और उनके उपनाम: लता मंगेशकर - \"स्वर कोकिला\"। किशोर कुमार - \"किशोर दा\"। उषा उत्थुप - \"क्वीन ऑफ इंडीपॉप\"।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "36",
                    section: "18",
                    question_en: "<p>36. Khudai Khidmatgar, a voluntary organisation, was established by which of the following leaders of India?</p>",
                    question_hi: "<p>36. भारत के निम्नलिखित में से किस नेता द्वारा एक स्वैच्छिक संगठन - खुदाई खिदमतगार स्थापित किया गया था?</p>",
                    options_en: [
                        "<p>Muhammad Ali Jinnah</p>",
                        "<p>Khan Abdul Ghaffar Khan</p>",
                        "<p>Hakim Ajmal Khan</p>",
                        "<p>Maulana Muhammad Ali</p>"
                    ],
                    options_hi: [
                        "<p>मुहम्मद अली जिन्ना</p>",
                        "<p>खान अब्दुल गफ्फार खान</p>",
                        "<p>हकीम अज़मल खान</p>",
                        "<p>मौलाना मुहम्मद अली</p>"
                    ],
                    solution_en: "<p>36.(b) <strong>Khan Abdul Ghaffar Khan,</strong> also known as Badshah Khan or \"Frontier Gandhi,\" founded the Khudai Khidmatgar (\"Servants of God\") movement in 1929. This group of Pashtuns used non-violent methods to fight against British rule. Maulana Muhammad Ali was a prominent Indian Muslim leader, journalist, and scholar who played a key role in the Khilafat movement. Muhammad Ali Jinnah was the leader of the All-India Muslim League and later became the Governor-General of Pakistan. He is honored with the title \'Quaid-i-Azam.\'</p>",
                    solution_hi: "<p>36.(b) <strong>खान अब्दुल गफ्फार खान, </strong>जिन्हें बादशाह खान या \"सीमांत गांधी\" के नाम से भी जाना जाता है, इन्होंने 1929 में खुदाई खिदमतगार (\"ईश्वर के सेवक\") आंदोलन की शुरुआत की थी। पश्तूनों के इस समूह ने ब्रिटिश शासन के खिलाफ लड़ने के लिए अहिंसक तरीकों का प्रयोग किया था। मौलाना मुहम्मद अली एक प्रमुख भारतीय मुस्लिम नेता, पत्रकार और विद्वान थे जिन्होंने खिलाफत आंदोलन में महत्वपूर्ण भूमिका निभाई थी। मुहम्मद अली जिन्ना अखिल भारतीय मुस्लिम लीग के नेता थे और बाद में पाकिस्तान के गवर्नर-जनरल बने। उन्हें \'कायदे-ए-आजम\' की उपाधि से सम्मानित किया गया था।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "37",
                    section: "18",
                    question_en: "<p>37. Who has been appointed as the first woman Chairperson of the Railway Board ?</p>",
                    question_hi: "<p>37. निम्नलिखित में से किसे रेलवे बोर्ड की प्रथम महिला अध्यक्ष के रूप में नियुक्त किया गया है?</p>",
                    options_en: [
                        "<p>Kalyani Chadha</p>",
                        "<p>Manju Gupta</p>",
                        "<p>Jaya Verma Sinha</p>",
                        "<p>M.Kalavathy</p>"
                    ],
                    options_hi: [
                        "<p>कल्याणी चड्ढा</p>",
                        "<p>मंजू गुप्ता</p>",
                        "<p>जया वर्मा सिन्हा</p>",
                        "<p>एम. कलावती</p>"
                    ],
                    solution_en: "<p>37.(c) <strong>Jaya Verma Sinha :</strong> Her previous work experience includes a four-year tenure as Railway Advisor in the Indian High Commission in Dhaka, Bangladesh.</p>",
                    solution_hi: "<p>37.(c) <strong>जया वर्मा सिन्हा: </strong>उनके पिछले कार्य अनुभव में बांग्लादेश के ढाका में भारतीय उच्चायोग में रेलवे सलाहकार के रूप में चार वर्ष का कार्यकाल शामिल है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "38",
                    section: "18",
                    question_en: "<p>38. Who was the chairman of the drafting committee of the Constituent Assembly of India?</p>",
                    question_hi: "<p>38. भारत की संविधान सभा की प्रारूप समिति के अध्यक्ष कौन थे?</p>",
                    options_en: [
                        "<p>Bhimrao Ambedkar</p>",
                        "<p>Vallabhbhai Patel</p>",
                        "<p>Rajendra Prasad</p>",
                        "<p>JB Kripalani</p>"
                    ],
                    options_hi: [
                        "<p>भीमराव अंबेडकर</p>",
                        "<p>वल्लभभाई पटेल</p>",
                        "<p>राजेन्द्र प्रसाद</p>",
                        "<p>जे.बी. कृपलानी</p>"
                    ],
                    solution_en: "<p>38.(a) <strong>Bhimrao Ambedkar.</strong> Major Committees and their Chairman :- Union Power Committee, Union Constituent Committee, State Committee - Jawaharlal Nehru; Rule of Procedure Committee, Steering Committee - Dr Rajendra Prasad; Provincial Constitution Committee, Advisory Committee on Fundamental Rights, Minorities, Tribal and Excluded area - Sardar Patel was the head of this committee.</p>",
                    solution_hi: "<p>38.(a) <strong>भीमराव अम्बेडकर।</strong> प्रमुख समितियां एवं उनके अध्यक्ष : - संघ शक्ति समिति, संघ संविधान समिति, राज्य समिति - जवाहरलाल नेहरू; प्रक्रिया नियम समिति, संचालन समिति - डॉ. राजेंद्र प्रसाद; प्रांतीय संविधान समिति, मौलिक अधिकार, अल्पसंख्यक, आदिवासी और बहिष्कृत क्षेत्र पर सलाहकार समिति - सरदार पटेल इस समिति के अध्यक्ष थे।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "39",
                    section: "18",
                    question_en: "<p>39. The red colour of tomatoes is due to the presence of:</p>",
                    question_hi: "<p>39. टमाटर का लाल रंग _______ की उपस्थिति के कारण होता है।</p>",
                    options_en: [
                        "<p>limonene</p>",
                        "<p>lycopene</p>",
                        "<p>beta carotene</p>",
                        "<p>alizarin</p>"
                    ],
                    options_hi: [
                        "<p>लिमोनीन (limonene)</p>",
                        "<p>लाइकोपीन (lycopene)</p>",
                        "<p>बीटा कैरोटीन (beta carotene)</p>",
                        "<p>एलिज़रीन (alizarin)</p>"
                    ],
                    solution_en: "<p>39.(b) <strong>lycopene. </strong>The red color in tomatoes, chillies, and carrots is due to lycopene, a carotenoid pigment. Lycopene helps plants withstand sunlight stress and offers health benefits like supporting heart health and reducing cancer risks. Its concentration varies with environmental factors and plant conditions.</p>",
                    solution_hi: "<p>39.(b) <strong>लाइकोपीन।</strong> टमाटर, मिर्च और गाजर का लाल रंग लाइकोपीन के कारण होता है, जो एक कैरोटीनॉयड वर्णक है। लाइकोपीन पौधों को सूर्य के प्रकाश से बचने में मदद करता है और हृदय स्वास्थ्य को बढ़ावा देने और कैंसर के जोखिम को कम करने जैसे स्वास्थ्य लाभ प्रदान करता है। इसकी सांद्रता पर्यावरणीय कारकों और पौधों की स्थितियों के साथ बदलती रहती है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "40",
                    section: "18",
                    question_en: "<p>40. In which year did the father of the Green Revolution in India die ?</p>",
                    question_hi: "<p>40. भारत में हरित क्रांति (Green Revolution) के जनक का निधन किस वर्ष में हुआ था ?</p>",
                    options_en: [
                        "<p>1993</p>",
                        "<p>2013</p>",
                        "<p>2023</p>",
                        "<p>2003</p>"
                    ],
                    options_hi: [
                        "<p>1993</p>",
                        "<p>2013</p>",
                        "<p>2023</p>",
                        "<p>2003</p>"
                    ],
                    solution_en: "<p>40.(c) <strong>2023. </strong>MS Swaminathan was born in 1925 in Kumbakonam, Tamil Nadu. He was instrumental in his pivotal role in the Green Revolution in India during the 1960s and 1970s. In 1987, he was awarded the World Food Prize. Other awards: Padma Shri in 1967, Padma Bhushan in 1972, Padma Vibhushan in 1989 and Bharat Ratna in 2024.</p>",
                    solution_hi: "<p>40.(c) <strong>2023.</strong> एम.एस. स्वामीनाथन का जन्म 1925 में तमिलनाडु के कुंभकोणम में हुआ था। उन्होंने 1960 और 1970 के दशक में भारत में हरित क्रांति में महत्वपूर्ण भूमिका निभाई थी। 1987 में उन्हें विश्व खाद्य पुरस्कार से सम्मानित किया गया। अन्य पुरस्कार : 1967 में पद्म श्री, 1972 में पद्म भूषण, 1989 में पद्म विभूषण और 2024 में भारत रत्न।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "41",
                    section: "18",
                    question_en: "<p>41. As of FY 2024-25, how many total enrollments has the Atal Pension Yojana (APY) achieved?</p>",
                    question_hi: "<p>41. वित्तीय वर्ष 2024-25 तक, अटल पेंशन योजना (APY) ने कुल कितने नामांकन हासिल किए हैं?</p>",
                    options_en: [
                        "<p>5 crore</p>",
                        "<p>6 crore</p>",
                        "<p>7 crore</p>",
                        "<p>8 crore</p>"
                    ],
                    options_hi: [
                        "<p>5 करोड़</p>",
                        "<p>6 करोड़</p>",
                        "<p>7 करोड़</p>",
                        "<p>8 करोड़</p>"
                    ],
                    solution_en: "<p>41.(c)<strong> 7 crore</strong>. The Atal Pension Yojana (APY), launched in 2015 to provide a universal social security system for underprivileged and unorganized sector workers, has surpassed 7 crore enrollments as of FY 2024-25, with over 56 lakh new enrollments added in the current financial year.</p>",
                    solution_hi: "<p>41.(c) <strong>7 करोड़। </strong>अटल पेंशन योजना (APY), जो 2015 में असंगठित क्षेत्र के कामगारों और गरीब वर्ग के लिए एक सार्वभौमिक सामाजिक सुरक्षा प्रणाली प्रदान करने के लिए शुरू की गई थी, ने FY 2024-25 तक 7 करोड़ से अधिक नामांकन हासिल किए हैं। वर्तमान वित्तीय वर्ष में 56 लाख नए नामांकन जुड़े हैं।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "42",
                    section: "18",
                    question_en: "<p>42. In Volleyball, if the game is tied at ______ in any set (except fifth set), the set continues till one team builds a two-point lead over the other.</p>",
                    question_hi: "<p>42. वॉलीबॉल में, यदि किसी सेट (पांचवें सेट को छोड़कर) में खेल ____ की बराबरी पर है, तो सेट तब तक जारी रहता है जब तक कि एक टीम दूसरे पर दो अंकों की बढ़त नहीं बना लेती।</p>",
                    options_en: [
                        "<p>24-24</p>",
                        "<p>11-11</p>",
                        "<p>14-14</p>",
                        "<p>18-18</p>"
                    ],
                    options_hi: [
                        "<p>24-24</p>",
                        "<p>11-11</p>",
                        "<p>14-14</p>",
                        "<p>18-18</p>"
                    ],
                    solution_en: "<p>42.(a) <strong>24-24. </strong>A set (except the deciding 5th set) is won by the team which first scores 25 points with a minimum lead of two points. In the case of a 24-24 tie, play is continued until a two point lead is achieved (26-24; 27-25; etc.).</p>",
                    solution_hi: "<p>42.(a) <strong>24-24.</strong> एक सेट (निर्णायक 5वें सेट को छोड़कर) वह टीम जीतती है जो कम से कम दो अंकों की बढ़त के साथ सबसे पहले 25 अंक हासिल करती है। 24-24 की बराबरी की स्थिति में, खेल तब तक जारी रहता है जब तक कि दो अंकों की बढ़त हासिल नहीं हो जाती (26-24; 27-25; आदि)।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "43",
                    section: "18",
                    question_en: "<p>43. \'Au\' is the symbol for which of the following elements?</p>",
                    question_hi: "<p>43. \'Au\', निम्नलिखित में से किस तत्व का प्रतीक है?</p>",
                    options_en: [
                        "<p>Gold</p>",
                        "<p>Aluminium</p>",
                        "<p>Silver</p>",
                        "<p>Argon</p>"
                    ],
                    options_hi: [
                        "<p>सोना</p>",
                        "<p>ऐलुमिनियम</p>",
                        "<p>चांदी</p>",
                        "<p>आर्गन</p>"
                    ],
                    solution_en: "<p>43.(a) <strong>Gold. </strong>It is derived from the Latin word \"Aurum.\" Aluminium : Symbol is Al (from Latin \"Alumen\"). Silver: Symbol is Ag (from Latin \"Argentum\"). Argon: Symbol is Ar.</p>",
                    solution_hi: "<p>43.(a) <strong>सोना।</strong> यह लैटिन शब्द \"ऑरम\" से लिया गया है। एल्युमिनियम: प्रतीक Al (लैटिन \"एलुमेन\" से) है। सिल्वर: प्रतीक Ag (लैटिन \"अर्जेंटम\" से) है। आर्गन: प्रतीक Ar है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "44",
                    section: "18",
                    question_en: "<p>44. Who among the following rulers introduced the &lsquo;jharokha darshan&rsquo; in the Mughal dynasty?</p>",
                    question_hi: "<p>44. निम्नलिखित में से किस शासक ने मुगल वंश में \'झरोखा दर्शन (jharokha darshan)\' की शुरुआत की थी?</p>",
                    options_en: [
                        "<p>Akbar</p>",
                        "<p>Humayun</p>",
                        "<p>Shahjahan</p>",
                        "<p>Aurangzeb</p>"
                    ],
                    options_hi: [
                        "<p>अकबर</p>",
                        "<p>हुमायूँ</p>",
                        "<p>शाहजहाँ</p>",
                        "<p>औरंगजेब</p>"
                    ],
                    solution_en: "<p>44.(a) <strong>Akbar (1556-1605). </strong>Jharokha Darshan was a practice in which Mughal emperors would address the public from the balcony of their forts and palaces. This practice was abolished by Aurangzeb (1658 to 1707) as he considered it a non-Islamic practice. Humayun (1530-1540 and 1555-1556) was the second Mughal Empire. The Shahjahan era marked a time of unparalleled architectural brilliance but also internal discord, culminating in the tragic War of Succession.</p>",
                    solution_hi: "<p>44.(a) <strong>अकबर (1556-1605)।</strong> झरोखा दर्शन एक ऐसी प्रथा थी जिसमें मुगल बादशाह अपने किलों और महलों की बालकनी से जनता को संबोधित करते थे। इस प्रथा को औरंगजेब (1658 से 1707) ने समाप्त कर दिया क्योंकि वह इसे गैर-इस्लामी प्रथा मानता था। हुमायूं (1530-1540 और 1555-1556) दूसरा मुगल साम्राज्य था। शाहजहाँ का काल अद्वितीय वास्तुशिल्पीय प्रतिभा का काल था, लेकिन साथ ही आंतरिक कलह भी थी, जिसकी परिणति उत्तराधिकार के दुखद युद्ध में हुई।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "45",
                    section: "18",
                    question_en: "<p>45. According to the 2011 Census of India, at all India levels, the adult (15+ years) literacy rate is ____________.</p>",
                    question_hi: "<p>45. भारत की 2011 की जनगणना के अनुसार, अखिल भारतीय स्तर पर, वयस्क (15+ वर्ष) साक्षरता दर __________ है।</p>",
                    options_en: [
                        "<p>65.3 percent</p>",
                        "<p>73.3 percent</p>",
                        "<p>69.3 percent</p>",
                        "<p>63.3 percent</p>"
                    ],
                    options_hi: [
                        "<p>65.3 प्रतिशत</p>",
                        "<p>73.3 प्रतिशत</p>",
                        "<p>69.3 प्रतिशत</p>",
                        "<p>63.3 प्रतिशत</p>"
                    ],
                    solution_en: "<p>45.(c) <strong>69.3 percent.</strong> According to Census 2011 : The literacy rate at all India levels is 74.04% and the literacy rate for females and males is 65.46% and 82.14% respectively. The top 5 states with the highest literacy rates are Kerala followed by Lakshadweep, Mizoram, Goa, Tripura, and Himachal Pradesh. The bottom 5 states with lowest literacy rates are Bihar followed by Arunachal Pradesh, Rajasthan, Jharkhand and Andhra Pradesh.</p>",
                    solution_hi: "<p>45.(c) <strong>69.3 प्रतिशत।</strong> जनगणना 2011 के अनुसार: अखिल भारतीय स्तर पर साक्षरता दर 74.04% है और महिलाओं और पुरुषों की साक्षरता दर क्रमशः 65.46% और 82.14% है। उच्चतम साक्षरता दर वाले शीर्ष 5 राज्य : केरल , लक्षद्वीप, मिजोरम, गोवा, त्रिपुरा और हिमाचल प्रदेश। सबसे कम साक्षरता दर वाले सबसे निचले 5 राज्य: बिहार, अरुणाचल प्रदेश, राजस्थान, झारखंड और आंध्र प्रदेश ।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "46",
                    section: "18",
                    question_en: "<p>46. Pradhan Mantri Virasat Ka Samvardhan (PM VIKAS) Scheme is a scheme of which Ministry of the Government of India?</p>",
                    question_hi: "<p>46. प्रधानमंत्री विरासत का संवर्धन (PM VIKAS) योजना भारत सरकार के किस मंत्रालय की योजना है?</p>",
                    options_en: [
                        "<p>Ministry of Tourism</p>",
                        "<p>Ministry of Tribal Affairs</p>",
                        "<p>Ministry of Minority Affairs</p>",
                        "<p>Ministry of Culture</p>"
                    ],
                    options_hi: [
                        "<p>पर्यटन मंत्रालय</p>",
                        "<p>जनजातीय कार्य मंत्रालय</p>",
                        "<p>अल्पसंख्यक कार्य मंत्रालय</p>",
                        "<p>संस्कृति मंत्रालय</p>"
                    ],
                    solution_en: "<p>46.(c) <strong>Ministry of Minority Affairs.</strong> Pradhan Mantri Kaushal Ko Kaam Karyakram (PMKKK) renamed as Pradhan Mantri Virasat Ka Samvardhan (PM VIKAS) Scheme. This integrated scheme converges five erstwhile schemes of the Ministry viz. Seekho aur Kamao, USTTAD, Hamari Dharohar, Nai Roshni and Nai Manzil. PM VIKAS, is a skilling initiative focussing on the skilling, entrepreneurship and leadership training requirements of the minority and artisan communities across the country.</p>",
                    solution_hi: "<p>46.(c) <strong>अल्पसंख्यक कार्य मंत्रालय।</strong> प्रधानमंत्री कौशल को काम कार्यक्रम (PMKKK) का नाम बदलकर प्रधानमंत्री विरासत का संवर्धन (PM VIKAS) योजना कर दिया गया है। यह एकीकृत योजना मंत्रालय की पांच पूर्ववर्ती योजनाओं अर्थात सीखो और कमाओ, USTTAD, हमारी धरोहर, नई रोशनी और नई मंजिल को एकीकृत करती है। PM VIKAS एक कौशल पहल है जो देश भर में अल्पसंख्यक और कारीगर समुदायों की कौशल, उद्यमिता और नेतृत्व प्रशिक्षण आवश्यकताओं पर केंद्रित है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "47",
                    section: "18",
                    question_en: "<p>47. Who founded the International Olympic Committee (IOC) in 1894?</p>",
                    question_hi: "<p>47. 1894 में अंतर्राष्ट्रीय ओलंपिक समिति (IOC) की स्थापना किसने की थी?</p>",
                    options_en: [
                        "<p>Konstantinos Zappas</p>",
                        "<p>Baron Pierre de Coubertin</p>",
                        "<p>George Averoff</p>",
                        "<p>Sir Ludwig Guttmann</p>"
                    ],
                    options_hi: [
                        "<p>कॉन्स्टेंटिनोज़ ज़प्पा</p>",
                        "<p>बेरोन पियरे डी कोबेर्टिन</p>",
                        "<p>जॉर्ज एवरोफ़</p>",
                        "<p>सर लुडविग गुट्टद्मन</p>"
                    ],
                    solution_en: "<p>47.(b) <strong>Baron Pierre de Coubertin.</strong> The first modern Olympics took place in Athens, Greece, during the 1896 Summer Olympics. The Olympic flag features a white background with five interlaced rings in the center : Blue, Yellow, Black, Green, and Red. Each color represents a continent: Blue for Europe, Yellow for Asia, Black for Africa, Green for Australia and Oceania, and Red for the Americas. The Olympic motto, \"Citius, Altius, Fortius- Communiter\" (\"Faster, Higher, Stronger- Together\"), while the original motto was first expressed by Dominican priest Henri Didon at a school sports event in 1891.</p>",
                    solution_hi: "<p>47.(b) <strong>बेरोन पियरे डी कोबेर्टिन</strong>। पहला आधुनिक ओलंपिक 1896 के ग्रीष्मकालीन ओलंपिक के दौरान एथेंस, ग्रीस में हुआ था। ओलंपिक ध्वज के बीच में पाँच परस्पर जुड़े हुए छल्ले (नीला, पीला, काला, हरा और लाल) के साथ एक सफ़ेद पृष्ठभूमि है । प्रत्येक रंग एक महाद्वीप का प्रतिनिधित्व करता है, जैसे नीला यूरोप के लिए, पीला एशिया के लिए, काला अफ्रीका के लिए, हरा ऑस्ट्रेलिया और ओशिनिया के लिए, और लाल अमेरिका के लिए। ओलंपिक आदर्श वाक्य, \"सिटियस, अल्टियस, फोर्टियस-कम्युनिटर\" (\"तेज़, ऊँचा, मजबूत - एक साथ\"), जबकि मूल आदर्श वाक्य को पहली बार डोमिनिकन पादरी हेनरी डिडॉन ने 1891 में एक स्कूल खेल आयोजन में व्यक्त किया था।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "48",
                    section: "18",
                    question_en: "<p>48. Which of the following statements is NOT correct?</p>",
                    question_hi: "<p>48. निम्नलिखित में से कौन-सा कथन सही नहीं है?</p>",
                    options_en: [
                        "<p>Light year is the unit of distance.</p>",
                        "<p>Light year is the distance travelled by light in one year.</p>",
                        "<p>Light year is the unit of time.</p>",
                        "<p>Angstrom is a unit of length.</p>"
                    ],
                    options_hi: [
                        "<p>प्रकाशवर्ष दूरी की इकाई है।</p>",
                        "<p>प्रकाशवर्ष एक वर्ष में प्रकाश द्वारा तय की गई दूरी है।</p>",
                        "<p>प्रकाशवर्ष समय की इकाई है।</p>",
                        "<p>एंगस्ट्रम लंबाई की इकाई</p>"
                    ],
                    solution_en: "<p>48.(c) A light year is the distance light travels in one year and serves as a convenient unit for measuring large astronomical distances, approximately 9 trillion kilometers (9 &times; 10&sup1;&sup2; km). A parsec (parallactic second) is the largest practical distance unit in astronomy, defined as the distance at which an arc of 1 astronomical unit subtends an angle of 1 second of arc, equating to 3.08 &times; 10&sup1;⁶ meters. An astronomical unit (AU) is the mean distance from the Earth to the Sun, used to measure distances to planets, and is equal to 1.496 &times; 10&sup1;&sup1; meters.</p>",
                    solution_hi: "<p>48.(c) एक प्रकाश वर्ष वह दूरी है जो प्रकाश एक वर्ष में तय करता है और बड़ी खगोलीय दूरियों को मापने के लिए एक सुविधाजनक इकाई के रूप में कार्य करता है, लगभग 9 ट्रिलियन किलोमीटर (9 &times; 10&sup1;&sup2; किमी)। एक पारसेक (पैरालैक्टिक सेकंड) खगोल विज्ञान में सबसे बड़ी व्यावहारिक दूरी इकाई है, जिसे उस दूरी के रूप में परिभाषित किया जाता है जिस पर 1 खगोलीय इकाई का चाप 1 सेकंड के चाप के कोण को घटाता है, जो 3.08 &times; 10&sup1;⁶ मीटर के बराबर होता है। एक खगोलीय इकाई (AU) पृथ्वी से सूर्य तक की औसत दूरी है, जिसका उपयोग ग्रहों की दूरी मापने के लिए किया जाता है, और यह 1.496 &times; 10&sup1;&sup1; मीटर के बराबर है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "49",
                    section: "18",
                    question_en: "<p>49. In which state, Kudankulam Nuclear Power Plant is established?</p>",
                    question_hi: "<p>49. कुडनकुलम नाभिकीय ऊर्जा संयंत्र (Kudankulam Nuclear Power Plant) निम्नलिखित में से किस राज्य में स्थापित किया गया है?</p>",
                    options_en: [
                        "<p>Kerala</p>",
                        "<p>Bihar</p>",
                        "<p>Tamil Nadu</p>",
                        "<p>Madhya Pradesh</p>"
                    ],
                    options_hi: [
                        "<p>केरल</p>",
                        "<p>बिहार</p>",
                        "<p>तमिलनाडु</p>",
                        "<p>मध्यप्रदेश</p>"
                    ],
                    solution_en: "<p>49.(c) <strong>Tamil Nadu. </strong>Other Nuclear power plants: Tarapur Atomic Power Station (Maharashtra), Rawatbhata Atomic Power Station (Rajasthan), Kaiga Atomic Power Station (Karnataka), Madras Atomic Power Station (Tamil Nadu) Chennai, Kakrapar Atomic Power Station (Gujarat), Narora Atomic Power Station (Uttar Pradesh).</p>",
                    solution_hi: "<p>49.(c) <strong>तमिलनाडु। </strong>अन्य परमाणु ऊर्जा संयंत्र: तारापुर परमाणु ऊर्जा स्टेशन (महाराष्ट्र), रावतभाटा परमाणु ऊर्जा स्टेशन (राजस्थान) , कैगा परमाणु ऊर्जा स्टेशन (कर्नाटक), मद्रास परमाणु ऊर्जा स्टेशन (तमिलनाडु) चेन्नई, काकरापार परमाणु ऊर्जा स्टेशन (गुजरात), नरोरा परमाणु ऊर्जा स्टेशन (उत्तर प्रदेश)।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "50",
                    section: "17",
                    question_en: "<p>50. NDP = _________.</p>",
                    question_hi: "<p>50. एन.डी.पी. (NDP) = _________।</p>",
                    options_en: [
                        "<p>GDP &ndash; Depreciation</p>",
                        "<p>GDP + Depreciation</p>",
                        "<p>GDP &ndash; Net factor income from abroad</p>",
                        "<p>GDP + Net factor income from abroad</p>"
                    ],
                    options_hi: [
                        "<p>जी.डी.पी. (GDP) - मूल्यह्रास</p>",
                        "<p>जी.डी.पी. (GDP) + मूल्यह्रास</p>",
                        "<p>जी.डी.पी. (GDP) - विदेशों से शुद्ध कारक आय</p>",
                        "<p>जी.डी.पी. (GDP) + विदेशों से शुद्ध कारक आय</p>"
                    ],
                    solution_en: "<p>50.(a) <strong>GDP &ndash; Depreciation. </strong>Net Domestic Product (NDP) - Aggregate value of goods and services produced within the domestic territory of a country which does not include the depreciation of capital stock. Net Domestic Product at Market Prices (NDP<sub>MP</sub>) - This measure allows policy-makers to estimate how much the country has to spend just to maintain their current GDP. NDP at Factor Cost (NDP<sub>FC</sub>) - NDP at factor cost is the income earned by the factors in the form of wages, profits, rent, interest, etc., within the domestic territory of a country. NDPFC = NDPMP - Net ProductTaxes - Net ProductionTaxes.</p>",
                    solution_hi: "<p>50.(a) <strong>जी.डी.पी.- मूल्यह्रास।</strong> शुद्ध घरेलू उत्पाद (NDP) - किसी देश के घरेलू क्षेत्र में उत्पादित वस्तुओं और सेवाओं का कुल मूल्य जिसमें पूंजी स्टॉक का मूल्यह्रास शामिल नहीं है। बाजार मूल्य पर शुद्ध घरेलू उत्पाद (NDP<sub>MP</sub>) - यह उपाय नीति-निर्माताओं को यह अनुमान लगाने की अनुमति देता है कि देश को अपने वर्तमान सकल घरेलू उत्पाद को बनाए रखने के लिए कितना खर्च करना होगा। कारक लागत पर NDP (NDP<sub>FC</sub>) - कारक लागत पर NDP किसी देश के घरेलू क्षेत्र में मजदूरी, लाभ, किराया, ब्याज आदि के रूप में कारकों द्वारा अर्जित आय है। NDPFC = NDPMP - शुद्ध उत्पादकर - शुद्ध उत्पादनकर।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "51",
                    section: "17",
                    question_en: "<p>51. The ratio of the speeds of a boat while going upstream and going downstream is 2 : 3&nbsp;and the sum of these two speeds is 15 km/h. What is the speed of the stream?</p>",
                    question_hi: "<p>51. एक नाव की धारा के प्रतिकूल और धारा के अनुकूल जाने की चाल का अनुपात 2 : 3 है और इन दोनों चालों का योग 15 km/h है। धारा की चाल क्या है?</p>",
                    options_en: [
                        "<p>3.5 km/h</p>",
                        "<p>1.5 km/h</p>",
                        "<p>3 km/h</p>",
                        "<p>2.5 km/h</p>"
                    ],
                    options_hi: [
                        "<p>3.5 km/h</p>",
                        "<p>1.5 km/h</p>",
                        "<p>3 km/h</p>",
                        "<p>2.5 km/h</p>"
                    ],
                    solution_en: "<p>51.(b) <br>Let Speed of upstream and downstream be 2x&nbsp;and 3x respectively,<br>According to the question,<br>(2x&nbsp;+ 3x) = 15<br>5x&nbsp;= 15 <br>&rArr; x = 3 km/h<br>Then, speed of upstream and downstream be 6km/h and 9 km/h respectively,<br>Now, speed of current = <math display=\"inline\"><mfrac><mrow><mn>9</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>6</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>2</mn></mfrac></math> = 1.5 km/h</p>",
                    solution_hi: "<p>51.(b) <br>माना धारा के प्रतिकूल और धारा के अनुकूल गति क्रमशः 2x&nbsp;और 3x है,<br>प्रश्न के अनुसार,<br>(2x&nbsp;+ 3x) = 15<br>5x&nbsp;= 15 <br>&rArr; x = 3 km/h<br>तो, धारा के प्रतिकूल और धारा के अनुकूल गति क्रमशः 6 किमी/घंटा और 9 किमी/घंटा है,<br>अब, धारा की गति = <math display=\"inline\"><mfrac><mrow><mn>9</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>6</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>2</mn></mfrac></math> = 1.5 km/h</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "52",
                    section: "17",
                    question_en: "<p>52. What is the present worth of ₹2,400 due in 4 years at a rate of 5% simple interest per annum?</p>",
                    question_hi: "<p>52. 5% वार्षिक साधारण ब्याज की दर से 4 वर्षों में देय ₹2,400 की धनराशि का वर्तमान मूल्य क्या है?</p>",
                    options_en: [
                        "<p>₹2,240</p>",
                        "<p>₹2,456</p>",
                        "<p>₹2,000</p>",
                        "<p>₹2,200</p>"
                    ],
                    options_hi: [
                        "<p>₹2,240</p>",
                        "<p>₹2,456</p>",
                        "<p>₹2,000</p>",
                        "<p>₹2,200</p>"
                    ],
                    solution_en: "<p>52.(c) <br>SI = <math display=\"inline\"><mfrac><mrow><mi>p</mi><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mi>r</mi><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mi>t</mi></mrow><mrow><mn>100</mn></mrow></mfrac></math><br>amount = p + <math display=\"inline\"><mfrac><mrow><mi>P</mi><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>5</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>4</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> <br>2400 &times; 100 = 100p + 20p<br>120p = 240000<br>(present worth) p = ₹ 2000</p>",
                    solution_hi: "<p>52.(c) <br>साधारण ब्याज = <math style=\"font-family:Verdana\" display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2350;&#2370;&#2354;&#2343;&#2344;</mi><mo>(</mo><mi mathvariant=\"normal\">P</mi><mo>)</mo><mo>&#215;</mo><mi>&#2342;&#2352;</mi><mo>(</mo><mi mathvariant=\"normal\">r</mi><mo>)</mo><mo>&#215;</mo><mi>&#2360;&#2350;&#2351;</mi><mo>(</mo><mi mathvariant=\"normal\">t</mi><mo>)</mo></mrow><mn>100</mn></mfrac></math><br>राशि = p + <math display=\"inline\"><mfrac><mrow><mi>P</mi><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>5</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>4</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> <br>2400 &times; 100 = 100p + 20p<br>120p = 240000<br>(वर्तमान मूल्य) p = ₹ 2000</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "53",
                    section: "17",
                    question_en: "<p>53. Find the average of natural numbers from 1 to 69. (both included)</p>",
                    question_hi: "<p>53. 1 से 69 तक की प्राकृत संख्याओं का औसत ज्ञात कीजिए। (दोनों का मिलाकर)</p>",
                    options_en: [
                        "<p>37</p>",
                        "<p>35</p>",
                        "<p>33</p>",
                        "<p>31</p>"
                    ],
                    options_hi: [
                        "<p>37</p>",
                        "<p>35</p>",
                        "<p>33</p>",
                        "<p>31</p>"
                    ],
                    solution_en: "<p>53.(b)<br>Average of n natural number = <math display=\"inline\"><mfrac><mrow><mo>(</mo><mi>n</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>1</mn><mo>)</mo></mrow><mrow><mn>2</mn></mrow></mfrac></math><br>= <math display=\"inline\"><mfrac><mrow><mo>(</mo><mn>69</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>1</mn><mo>)</mo></mrow><mrow><mn>2</mn><mi>&#160;</mi></mrow></mfrac></math><br>= <math display=\"inline\"><mfrac><mrow><mi>&#160;</mi><mn>70</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> <br>= 35</p>",
                    solution_hi: "<p>53.(b)<br>n प्राकृत संख्याओं का औसत = <math display=\"inline\"><mfrac><mrow><mo>(</mo><mi>n</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>1</mn><mo>)</mo></mrow><mrow><mn>2</mn></mrow></mfrac></math><br>= <math display=\"inline\"><mfrac><mrow><mo>(</mo><mn>69</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>1</mn><mo>)</mo></mrow><mrow><mn>2</mn><mi>&#160;</mi></mrow></mfrac></math><br>= <math display=\"inline\"><mfrac><mrow><mi>&#160;</mi><mn>70</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> <br>= 35</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "54",
                    section: "17",
                    question_en: "<p>54. If from each of the three boxes containing 3 white and 1 black, 2 white and 2 black, 1 white and 3 black balls, one ball is drawn at random, then the probability that 2 white and 1 black ball will be drawn is :-</p>",
                    question_hi: "<p>54. यदि 3 सफ़ेद और 1 काली, 2 सफ़ेद और 2 काली, 1 सफ़ेद और 3 काली गेंदों वाले तीन बक्सों में से प्रत्येक में से एक गेंद यादृच्छिक रूप से निकाली जाती है, तो 2 सफ़ेद और 1 काली गेंद निकाले जाने की प्रायिकता है</p>",
                    options_en: [
                        "<p><math display=\"inline\"><mfrac><mrow><mn>13</mn></mrow><mrow><mn>32</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>32</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>16</mn></mrow></mfrac></math></p>"
                    ],
                    options_hi: [
                        "<p><math display=\"inline\"><mfrac><mrow><mn>13</mn></mrow><mrow><mn>32</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>32</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>16</mn></mrow></mfrac></math></p>"
                    ],
                    solution_en: "<p>54.(a) <br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;Box A : Box B : Box C<br>White &rarr;&nbsp; &nbsp; 3&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp;2&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; 1 <br>Black &rarr;&nbsp; &nbsp; 1&nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp;2&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; 3 <br>Now, one ball is taken randomly :-<br>Possible case for 2 white and 1 black will be taken:-<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;Box A&nbsp; &nbsp; :&nbsp; &nbsp;Box B&nbsp; &nbsp;:&nbsp; BoxC <br>Case 1 = Black <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> : white <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>4</mn></mfrac></math> : white <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math><br>Case 2 = white <math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> : Black <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>4</mn></mfrac></math> : white <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math><br>Case 3 = white <math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> : white <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>4</mn></mfrac></math> : Black <math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math><br>Required possibility :- <br><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> &times; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>4</mn></mfrac></math> &times; <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> +&nbsp; <math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> &times; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>4</mn></mfrac></math> &times; <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> + <math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> &times; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>4</mn></mfrac></math> &times; <math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math><br>= <math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>64</mn></mrow></mfrac></math> + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>6</mn><mn>64</mn></mfrac></math> + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>18</mn><mn>64</mn></mfrac></math> = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>13</mn><mn>32</mn></mfrac></math></p>",
                    solution_hi: "<p>54.(a)&nbsp;<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;बॉक्स A : बॉक्स B : बॉक्स C<br>सफ़ेद &rarr;&nbsp; &nbsp; 3&nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp;2&nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp;1<br>काला &rarr;&nbsp; &nbsp; 1&nbsp; &nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp;2&nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp;3<br>अब एक गेंद यादृच्छया निकाली जाती है :-<br>2 सफ़ेद और 1 काला के लिए संभावित मामला लिया जाएगा:- <br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;बॉक्स A&nbsp; &nbsp;:&nbsp; बॉक्स B :&nbsp; बॉक्स C<br>केस 1 = काला <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> : सफ़ेद <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>4</mn></mfrac></math> : सफ़ेद <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math><br>केस 2 = सफ़ेद <math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> : काला <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>4</mn></mfrac></math> : सफ़ेद <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math><br>केस 3 = सफ़ेद <math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> : सफ़ेद <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>4</mn></mfrac></math> : काला <math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math><br>आवश्यक संभावना :- <br><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> &times; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>4</mn></mfrac></math> &times; <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> +&nbsp; <math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> &times; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>4</mn></mfrac></math> &times; <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> + <math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> &times; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>4</mn></mfrac></math> &times; <math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math><br>= <math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>64</mn></mrow></mfrac></math> + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>6</mn><mn>64</mn></mfrac></math> + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>18</mn><mn>64</mn></mfrac></math> = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>13</mn><mn>32</mn></mfrac></math></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "55",
                    section: "17",
                    question_en: "<p>55. Study the given table and answer the question that follows.<br>The following table shows the production of printers by four plants of a company over the five years.<br><img src=\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAcsAAAFeCAIAAABly6PgAAAgAElEQVR4Aey9cUyUSZ7/X/0s3RuazKYlQAQCTGAJHGRnZ2I7TjIoIZxhjiFq1pEh5K7hhM4yZxyy7oVJPLJe4ukA0Vyi2dFsTDb3xRsxC7mFdXFH19tRD6Ouq+6qZGYYldt2WFjFAeGau+6+p35zfn5TU/N5nqd4uul+Wniq/8Cqeqrqqc/r+dT7qaeqnkdC5U8SkAQkAUkgfgQ+//zzzz777H/+538opSR+1cqaJAFJQBKQBOj//u//Tk5OfvbZZ/Pz818pLJE/SUASkAQkgXgQiEQi09PTn332mVTYeOCUdUgCkoAkwBGAkfx//dd/YYVd2UN8ILCybZTWSQKSQBIJIJGRCpvEayFPLQlIAiuNgFTYr24qK+3aSnskAUkg2QSkwkqFTbYPyvNLAiuXgFRYqbAr17ulZZJAsglIhZUKm2wflOeXBFYuAamwUmFXrndLyySBZBOQCisVNtk+KM8vCaxcAlJhpcKuXO+WlkkCySYgFVYqbLJ9UJ5fEli5BKTCSoVdud4tLZMEkk1AKqxU2GT7oDy/JLByCUiFlQq7cr1bWiYJJJuAVFipsMn2QXl+SWDlEpAKKxV25Xp3/Cw7d+7c3//936uqGr8qZU1fETh16tQ//dM/hUKhr5JWSkgqrFTYpfrytWvX3G53ZWXl3NzcUusyUf7zpz8TGeOW5dSpU263+2//9m//+7//m1KaoAZMTEzkP/1NTEzErenPcEVDQ0OEEJ/PRykdGhr65je/2dbWtvJENkaFVVW1s7OTEFJXVxcMBtF1ZEe3bdv2LCNDxiMrll3U5/OBRfzfVatWtba2BgKBxJljpcLeu3cvNzc3PT19dHQ0cRbxNd+/f7+wsLC6unpmZoZSmrgG2FlhKaUHDhxwOBxHjhzh4a+AMBKZrwZ06IDW1LGxsezsbIfDcfr0aXT09u3bGRkZLpfrwoUL6NAzFV3UxmeqtYs2BhR21apVedwvLS2NEOJ2u0+dOrVoDbFlsFJhp6amXnrppRdeeCGh9wzGIRQKNTc3Z2Rk3L59GxIT1wCbK2wwGKyrq8vOzh4bG2P8V0AAiUwUCksp7erqIoTU1NTww1hVVdvb2wkhLS0t4XD4WWaEjH+Wm2qmbaCwQ0NDfOYvniH27NlDCCktLU2QKiVOYcHBurq6eIusDF+6dMnlcu3YsSPuM7Ba02yusJTS06dPOxyO9vb2uNO20mfQuZDIRKewU1NT5eXlaBgLA1j+to9O+exEkfHPTsNia4muwlJKg8FgTU0NIeT48eOx1SwutVIVVlXVHTt2uFyuS5cuiQnEcFQqLD8PCwBnZmbWrVuXm5t77969GJA+m0WQyESnsJTSI0eO8MNYNoBFN6JgMHjo0KGcnBxCiNPp1J0Z/O1vf1tbW6soCiEkLS1tx44dDx8+ZNRYN/7FL35RWlpKCDl48CA7GlsAGR9bJc9OKSOFpZTu27ePEAKDQQHJ+fn5np4euEyKorz66qvnz59HA4pIJPL+++/zl/IXv/gFv9KlOxabm5urrKx0u93Xrl1jxGZnZzs6OjweDyFEUZTa2to7d+7AURAguEDwF5ZEdCtXVfX06dOvvPIK5CwoKDh06BD/XAUmb9269d69e62trU6nkxCSk5PT19eHrGNto5Q+ePCgoKDA6/U+fvyYpWsb4PP5UlJSPvzww5MnT5aUlIAt9fX1f/rTn1gpPrCoaR999BHD4vF4Dhw4gBYzFr1M7BLza4/alkcikZMnT5aXlxtxo5R++umnzc3NQMzpdL7xxhuffvopMwfq9Hq94+Pj4jYHg8Genh641h6P55133nn//ffZShercPfu3YSQEydOsJTlHgC2zIqoFfbRo0dr165lw9hbt255PB40mTIzM1NdXQ0+3djYCF5YXFw8Pj7OTvzee+8pT39VVVU+nw86MP9gC07zzW9+MyUlBRq99IdHZDxrzDINCBSWHzEZkQwEAl6vlxDi8Xjq6+srKyuBz4EDB5gMhUKhtrY2SC8vL6+srHQ6nd/4xjcIIWwvgbYnU0q1Cjs+Pl5cXAxe4fP54NSKovT391NKh4eH/X7/mjVrCCFr1qzx+/3Hjh2jlGorD4VCP/zhD0HXqqqq6uvrU1NTCSGvvfYarE1RSsHk7OzsrKystLS0mpqagoICsKK3t9focp89e9bhcLS0tPAZtA0A7GvWrHE4HIwJIWTt2rWPHj3iy0JYbJrb7S4qKnI6nZWVlUz7Ojs72SUwc5nMKKyqqt3d3TDi2fz0BzLq9/uZoP/qV79yu92EEK/Xu337dui5Ho/n+vXrYAvQWLTNMzMzr732Glyjl5/+FEWBjgw3TkZJlzk7uhwDSGSiVlhK6cDAgMPhqKysnJ2d3bFjBxsrAQ42qu3s7IQrxy4tm6iF9VmPx3PlyhUoBSsMhJDDhw9DCjgN3PRmZ2fjwhoZH5c6k1iJkcKGQqEtW7awWQJdkuFwGKSzpaWFjf5GRkY8Hg+/YnnmzBlFUfgrNT09XVVVFa3ChkKhbdu2EUL2798fiUQA2gcffOB2u0tLSycnJyGFvzFAilbgBgcHFUUpLi5mKySsSUyYmMk/+MEPwDrmhLW1tQsLC7pXDc6OHpW0DQDseXl5V69ehXrg5uFwOM6ePatbM1vD4EcJUDMh5Hvf+9709DQU7O/vVxSlrKwMnudMXiYzCjs6Opqenl5YWHj//n3dZsMzO7vnUUpVVd2/fz8hhE1Mm2kze9LlB1VjY2Nwf0UKC62qqKh48uSJEbrllY5EJhaFhWk+h8Pxox/9yOPxfPvb3/7jH//IKAAyvttQSicnJ0tLS9mEy/T09M9//vORkRF2o4YtcvxDBDjNSy+9pDsuYKeLKoCMj6rsM5hZV2EjkcixY8cURWG0dUnCwwe6TJTS3t5eQkhDQ0M4HFZVtbm5mb/tAYSrV6+mpqZGNYaFNqxbt44NM6EDNzY2pqSkXLx4EWpeVGGZ76ENLbDRRWzy3bt3V69eXVhYyAQdXVNdnkYK+9Of/pQvDk+7SJ35DEamZWZmfvLJJyzn48ePvV7vc889d/PmTUqpmcvExuzsikBtqOW6Kjw4OOj3+4eHh2H6fnh4+Ne//jUb0mprhjrFbYbHF/aYy0wbGBjgO7huI1nm5RtAIhOLwlJKYWgDdaEdbcARPWqpqor6kpYgmgjXdQhtqahSkPFRlX0GM4MibNy40f/lr7q6GnZr8SMRXZLHjx8nhOzcuRPZBTIEY6gnT55UVFSw3s5yogpRT4ZsaJbA6HSsTggYyVB+fj5sy+ebx5cNh8Nbt25lo0jUQsip206+kqgUFm3h0Lacr1kwhmWmQX6T3BAHM/bCg+MXyx7/+I//aH7UgmrWZYjaHAgEcnNztXcy1MHBXrij5ObmJmjfC7oKFkSRyMSosOyhTzv3BK4GU2lfdvz/+/f5558nhDC/jEQi/f3969atg8kgaBZ/i0OXNi5okPFxqTOJlYAiMHQQSEtLa25u/uijj1jDdEkaKQLfhfgwq81oXCNWCqPT8dWakSFdW6ASXh91sxmZw9rA18AStaV0sy1qoDaDtmbt/LW2FDQMlTVp77lz52DBgxCSlZX1/e9//86dO/xzpKqq58+fr6mpgfs0cy02OkbnhcYghdVtjPYhVbcsw758A0hkYlRY1hnQrApLZ9cGBUBhQ6GQ3+8nhBQVFe3evftnT38dHR1SYaNyLN2urq1B1+PNdF3d7iQVVhe7EU92ObQZdPEitdKWggpRWd1LjPJAwVAo9MEHHzQ1NWVlZUHfbG5unp+fh0kbWArLzMx8++23T5w48bOf/ayrqyslJSVxCivHsMxDcACuvVZhjx49qvv4yZeHncZbt25layzaW5yu0/CVxBBGt5cYanimiuh2dW0LdUkaPbbzj59ylkB3M4MudiMpZJdDm0FXAZHCmrlM2nsenFS3ftYeSuknn3zy3e9+lxAC2zZgztfr9f75z39m2ZDz6NaJ2hzVLIFuhezsyzGARCb+Y1i4JNollC++XcAWkbXeBlsU5Bg2KpfS7eraGlAngQziJZTm5mb16Q/2irANHlBWd6ULTaWBOrP9sNCGRK90FRQUPHjwIGbFAbdEq1VaCdDFruvS/LXQZtDWrJ0lMHOZmL1oRR6Ujs3edHR05OXlDQwM8K0CBf/+97+vHeIIrjWrE/IghTVajdRd6ZJ7CfjL8bUweIx2DMumaPltQMFgsKWl5S//8i/h9gjXtaKigm1S+f3vf19YWCgV9muIF4vodnVtIV2F1d0GdPv27fz8fH631oULF1wuV35+PntPn22NYk+OCwsLtbW1hJCf/vSnMKkXiURglw9TWOYVbLeWqqp9fX2KovCyCw9AbG+Q7hBSu1trfn7+zTffJISg3VqshcBEV9F4XLp7M7WldLFrBZSvmVJqxjStwpq8TA8fPiwrK3O5XOfOnYPzQo8jhDA1PHHiBCGkqqqKdTq2RfLo0aOU0osXL6akpBQWFrK9DYFAYP369dHuzGM7UtatW8fewjDaraXLHKFbXtGEj2EppePj47B3Gray19fXw6sdP/zhD2EjCHslwel05uXlZWZmEkJgHzvbrqirC0tkjYxfYm1JL67b1bWtMiL5pz/9CfqP4I2DLwayP/rRj4Ab213vdrsVReH1C1SPEAJ5YJ3E4XAwhQWv4N84ePHFF+FlB7YnmlIKnwUghGRnZ7/55pvz8/NagfvCheDDC4qiiN844FuoK9aIlfl3uvg1W6hkUYU1Y5pWYSmlZi4TfKqK7fCHBWSHw8Er7NzcHGxJdjqd1dXV9fX1MBXLvqrDVkdgq192djZUSAhh+3O1l0O3zcFg8G/+5m9Ye15++WVFUdLS0hwOBxqTATf5Thdyxf+LGo1hIevs7Gx3dzdbuHzxxRd//vOfs1kCSuns7Gx7eztsJPB4PD09Pb/73e/S09PFD3o67YgmSSososW/3Gz+rdnf/e53+fn5vH6pqtrf3w+vABFCXnnlleHhYfFbs7rvUquq+t5778E7Wps2bdJVWFiTOX/+/KJvzfItNKOwut8l0GqK7o1tUYU1aRp64obrZeYyhUKhw4cPQ4+DN5J/85vf5D/9se/PhkKh//f//h+7TFlZWR0dHfzrPPyrrk6ns729/dNPPy0rK2N3Si0NXYWF3bXordkPP/zQ7XbzCgvzCWwXM3LOZRpFIhP7POxytB8ZvxxNkG1OKAEYaaKPbCT0jHaufEXSRiIjFdbOHi5txwS034fFOWQ8TgRgaj5bfh82TjyfiWrQ7eWZaJNsxDNGAP6PA93/y+MZa+nybg6sc6I3Qpe3SU9bj0RGjmFXwDWVJsSZwKlTp+Bzms/4F+XjbLaF1X344YerVq2S/0+XhcgtORW6vVhyTnmSZUng3LlzO3fulAqboIsn/6/ZBIFNcrVSYZN8AeTpJYGVTgCJjJwlWOkXXNonCUgCFhKQCvvVTcVC7PJUkoAkYAsCUmGlwtrC0aWRkkBSCEiFlQqbFMeTJ5UEbEFAKqxUWFs4ujRSEkgKAamwUmGT4njypJKALQhIhZUKawtHl0ZKAkkhIBVWKmxSHE+eVBKwBQGpsFJhbeHo0khJICkEpMICAflXEpAEJIFEEWDi/tWADk7FDqzIQKJwynolAUlAEuAIMP20o8Iy42UACIBjSBpGBCQfIzKUUgkHwUFApMIiPnaMIp+wIwKhzZKPAI+Eg+AgIFJhER87RpFP2BGB0GbJR4BHwkFwEBCpsIiPHaPIJ+yIQGiz5CPAI+EgOAiIVFjEx45R5BN2RCC0WfIR4JFwEBwERCos4mPHKPIJOyIQ2iz5CPBIOAgOAiIVFvGxYxT5hB0RCG2WfAR4JBwEBwGRCov42DGKfMKOCIQ2Sz4CPBIOgoOASIVFfOwYRT5hRwRCmyUfAR4JB8FBQKTCIj52jCKfsCMCoc2SjwCPhIPgICBSYREfO0aRT9gRgdBmyUeAR8JBcBAQqbCIjx2jyCfsiEBos+QjwCPhIDgIiFRYxMeOUeQTdkQgtFnyEeCRcBAcBEQqLOJjxyjyCTsiENos+QjwSDgIDgIiFRbxsWMU+YQdEQhtlnwEeCQcBAcBkQqL+EQdnZiYyH/6m5iYiLrws1EA+UQcG9XV1UUI6erqimOd1leVID5DQ0OEEJ/PZ71FcTxj3OEsdywIiFRYQ2fz+XwAi/+7atWq1tbWQCDAij07Cjs/P//Tn/60pKTk1KlTrHlmAsgnFi1y7do1t9vNYyGEKIryne985+TJk5FIhNWQdIWNRCInT54sKSmB1mZlZXV0dMzOzrIWmglExWdubq6yshLBIYTk5OSgUyddSlRVPX/+/KuvvqooCiEkLS0N+XYc4SwjLMjq3t5eQkh+fr7JIRTyFqmwiOdXUVDYVatW5XG/tLQ0Qojb7WYqlnSFVVX1zp07Pp8P+gkhZGho6CszTISQTyxaAhRWUZTc3FzGJjs7G+p54403ZmZmoJLkKmwoFPL7/YQQp9O5efPm+vr61NRUQkh1dTVr4aLGRvuRaSYlmZmZDE5eXp7T6SSE5OXl3bhxA06aXIVVVbW7uxtujS+//LLP58vJySGEFBcXj4+Pm8ECeUw6z3LBggwfGxsDx5YKi8joR016AxQGhUVqFQqF9uzZQwgpLS2FkWziFNZkzS0tLWBXUVFRXl6eZQpbWVk5NzfHgx4dHS0sLCSEHDhwANITp7BmaobRR1VV1fT0NLRnenq6qqqKELJnzx6+5eJwVG4DUuJ2u69du8ZXOz8//9d//deEkI0bNwK3xCmsmZovXbrkcrmKi4vHxsagnZFIZO/evYSQHTt2qKrKN14QNglnuWDhLQ2FQs3NzWCgVFiejGHYpDdAeV2FpZQGg8GamhpCyPHjxymlJnXQsE3GB0zWvGPHjvb29o8//lhVVaM2G5/k/45EhYVSCmNYrcJSSk+fPu1wOLxe7+PHjymlZnRQ3Dajo4vWvLCwUFtbm5KScvHiRb4SaGFtbe3CwgKfLghHxcdISiilk5OTpaWlrElmdFDQKsEhMzUfPHjwi9H9vn37+Hru3r27evXqsrKyhw8f8umCsEk4ywULb2lfX5/D4WhqasrNzZUKy5MxDJv0BigvUKt9+/axBRxdHZyfn+/p6YHHLkLIiy++eOrUKTYugCJer3d8fLyjo8Pj8RBCPB7PgQMHvhgjM9WG1sJf7ZhI10hBm3XzQ2JUWMQKGwgEeHfU1cHf/va3tbW1bO5vx44dfH+GIr29vefPn3/llVegbevXr//444+htZCBh6O7WKR7XcSNN0IUFR+BlKiq2tjYyB4ydHXw008/bW5uhikFp9P5xhtvfPrpp6xhcG/bunXrvXv3WltbIVtOTk5fXx94F9TJw9G9EbIKUcAIGsrGR03CWXZYxsfHi4uL165de+vWrfynPzkPy193/bBJb4DCArXihUPrlBMTEy+//DIsbvh8vqqqKlAT9uwMRdxud1FRkdPprKysLC8vh7Z1dnaqqvr555+/8847jY2Nrqe/xsbGt99++/79+/pWcamCNnO5cDAqLGKRQjR4UHDW9957T3n6q6qqYnN/bMqFDXu/853vpKSkFBQU1NTUwNx3dnY2PM8ODw/7/f41a9YQQtasWeP3+48dO4ZNMo4PDg4SQnbu3GmcBR+Jio9ASiil/AXSKuyvfvUrWEL0er3bt2+HBTqPx3P9+nVoEyhsdnZ2VlZWWlpaTU1NQUEBNK+3t5dSeuPGDb/fv3HjRkLI888/7/f7e3p6zI/WR0dH09PT2SMIBqEXNwlneWEJh8NtbW2Kopw5cwa5tB6Dr6UhIHKl62t0+AjfGfj0UCi0ZcsWwSwBTNT6/X4YkFJKL1++7Ha7S0tLJycn+SHq9773PTZF2N/frygK/4AW7aVFHZhvsziMfEKcWayw//7v/56SksK6KFLYe/fu5ebmejyeK1euwFnYVNfhw4chBYq43e7BwUEYl83MzFRXVxNCjh49ytqGambp4kAwGKyrq3O5XJcuXRLn5I9GxUcgJY8ePXrppZeMZglmZmbWrVunKEp/fz+cXVXV/fv38xOjoLCEkB/84AfBYJBSyhas+HkPrXbz5hiFg8EgzOkfOXLEKI823SSc5YVlcHBQUZS2trZwOBxtN0RApMJqfeb/T9FV2EgkcuzYMVhGv3fvHpNLfprm8uXL//Zv/8Y/+SL3gmuWmZn5ySefsNM/fvzY6/U+99xzN2/ehMRoL23SFTYQCKxfv55fR0I6OD09/fOf/3xkZIRNmFBKkRxAkY6ODj7P8ePHCSEtLS0MF6qZpQsCTIyam5vZzU+Qnx1CfYal6wbQtWZ5gsHgj370I0LIunXrYCcDMjwYDA4PD//617/m24amvCH60ksvPXr0iNUMk6eFhYVw/9YiZTl1A/BMUF9fD7NVHR0dfAN0i/CJJuEsIyxTU1Pl5eXsuSraboiASIXlveVrYVDYjRs3+r/8VVdXwxMrP9AwcwGQe+kWQXl0tftr7dOL6N4V9DJ+LQ35xNeO6UWgn2dkZDQ3NwMbn89XVFQE9fB7oczoIBIa3SIoD5tMiOpdBnhKiHY3UrQrgXAdHQ7Hli1bvnQc/7p162DOlB+/a43SwtZVWDS1qnUnMzWzcwFwuHapqalsMYBlEAdMOs9ywfLFHF17e7vD4ejr6wPDtXijAiIV1hAXqBU4EPublpbW3Nz80UcfsWK6FyAQCPzd3/0dW+mC4my1SrfIslNYxoQFysvL/+Vf/gWeXoGPVi4jkUh/fz9THFaWrVZpi+gOynSzsYuiDVy5csXz9McmKLR5jFKgkUZHUTpcR2YXC2RlZe3atYtfLdHqILwCwKaeWVkmqUhw4dRad9LWjBqpjUYikT/84Q+1tbWEEHg61ubRTTEJZ7lguXDhgsvl2rZtGxvIa/HqcmCJCIhUWEYGB0yOB7UXADqzoijf+973jh079rOf/ay3t7ekpGSFKSzr9hgcF0c6yF4BKCoq2r1798+e/jo6OviXR1ERqEwrGbrZuDN/LTg2NlZcXOzxeEZGRr52wFwE9RlxIe2d0ig/MopNYmRmZr799tsnTpz42c9+1tXVlZKSwlAnTmGhkbCfLD09fXR01KjZKN0knGWB5dGjR2vXrnU4HHV1dez5I9oFZwREKixymK+isSks7JZ1uVznzp1jdSH30ooypRTlefZnCVi3Z2ZqA0gHYS/q1q1b+XEuEhpUBOpEeaKaJYBtN/zEjrad4hTUZ8SZtdfRKD8y6tatWx6Px+v1/vnPf2ZFkKSiKGTTuhOqmdW2aIDtJxseHl40M2QwCWdZYAG8YJHuXzZIEsBBQKTCGrKKTWG17k4pffLkSUVFBbs8unm0LqibzbC5Tw+YbDOqBPkEOqqN6vZzbTatDuqq58DAQOLGsLAPQVEUtmNUt53ixKj4aK+jUeVIB1EUSl29ejU1NZXdzHTJa/1EtyrUDNg2AG/NsEPgqKmpqVevXmWJ4oBJOMsFi9ZYLV5tHj4FAZEKy8P5WtikWqELAFsCHA7H+++/D6vhbBE5WoV9+PBhWVmZx+O5devW11pmHDHZZlQB8gl0VBvV7efabFqFhS0BFRUVbI/a73//e3jRNtp52KNHj/LbmHTPzrYfdXd38zsTdDMLEqPiE7OUXLx4MSUlpbCwkO0wYXszolXYs2fPOhyOmpoa/lkBGXjixAn4CgF7a1ZV1b6+PkVR2G4HVEQ3ahLOcsGitRF1cG0GlIKASIVFfL6KmlQr7QWANWtCCHz7AxaRFUVxOBxnz541evzXumA4HG5oaICPHuXl5UHZr9qnFzLZZlQU+QQ6qo3GrLBsZ6vT6czLy8vMzCSEfOMb3yCEsO2cuuNc7aAMXqsnhGRnZ7/55pvz8/Padu7atQtelmtqamLTaiwQ9wdhaID2OmobBinIKDZJDXsB4YMj8K4K2yWtS17rgbDvGDywoqJiampK2wb+dPD2B7y8wO920JbSpph0nuWCRWugFq82D5+CgEiF5eF8LWxSrbQXQFXVc+fOsde01q9ff+fOnZ07d4pftNV1QTaKURTlgw8++Fr79CIm24yKIp9AR7VR3X6uzaYdw1JKZ2dn29vb2dalnp6e3/3ud+np6QUFBQ8ePNAtoruXQFXV9957D76VtWnTJl2FBRpgnfav+Z1eUfHRvY66cJDCwicvenp6YF+q0+lsb2//9NNPy8rK2NOPLnmtB1JKh4eH4QZWXl6uq7CU0kgkcurUKSu/XsgM0QUCiUnHgtqmixfl4aPIW6TC8nBsGkY+YVMKxmZLPsZsov5skKCqlXEIeYtU2JVxWZdkBfKJJdW1EgtLPoKrKuEgOAiIVFjEx45R5BN2RCC0WfIR4JFwEBwERCos4mPHKPIJOyIQ2iz5CPBIOAgOAiIVFvGxYxT5hB0RCG2WfAR4JBwEBwGRCov42DGKfMKOCIQ2Sz4CPBIOgoOASIVFfOwYRT5hRwRCmyUfAR4JB8FBQKTCIj52jCKfsCMCoc2SjwCPhIPgICBSYREfO0aRT9gRgdBmyUeAR8JBcBAQqbCIjx2jyCfsiEBos+QjwCPhIDgIiFRYxMeOUeQTdkQgtFnyEeCRcBAcBEQqLOJjxyjyCTsiENos+QjwSDgIDgIiFRbxsWMU+YQdEQhtlnwEeCQcBAcBkQqL+NgxinzCjgiENks+AjwSDoKDgEiFRXzsGEU+YUcEQpslHwEeCQfBQUCwwsJh+VcSkAQkAUkgZgJMdqXCxsxQFpQEJAFJQJ+AocKyAysyADBWpGlLMUpiEdOTfAR8JBwEBwHBY1iUe4VFkfErzLqYzZFYxOgkHwEfCQfBQUCkwiI+dowin7AjAqHNko8Aj4SD4CAgUmERHztGkU/YEYHQZslHgEfCQXAQEKmwiI8do8gn7IhAaLPkI8Aj4SA4CIhUWMTHjlHkE3ZEILRZ8hHgkXAQHAREKiziY/2ch4IAACAASURBVMco8gk7IhDaLPkI8Eg4CA4CIhUW8bFjFPmEHREIbZZ8BHgkHAQHAZEKi/jYMYp8wo4IhDZLPgI8Eg6Cg4BIhUV87BhFPmFHBEKbJR8BHgkHwUFApMIiPnaMIp+wIwKhzZKPAI+Eg+AgIFJhER87RpFP2BGB0GbJR4BHwkFwEBCpsIiPHaPIJ+yIQGiz5CPAI+EgOAiIVFjEx45R5BN2RCC0WfIR4JFwEBwERCos4mPHKPIJOyIQ2iz5CPBIOAgOAiIVFvGxYxT5hB0RCG2WfAR4JBwEBwGJWmEjkcjJkydLSkqgoqysrI6OjtnZWXSa2dnZ9vb2tLQ0QoiiKBs2bLhz5w7KA9FIJDIyMlJbW+v3+3UzBAKB1tZWp9NJCHE6na2trYFAQDfnoonI+EXzLzGDqqrnz59/9dVXFUUhhKSlpek2vqurCxqG/nZ1dS2xASaLW4wFWhUMBg8dOpSTkwNnLygoOHToUDAY5Ns8MTGRn5+PsBBC8vPzJyYm+JwJDSeFD7MoFApt27aNEOLz+VgiBAKBQENDA+saDQ0NMXcNVLP5aLLgCLD4fD6tzxBChoaGzNsVc04EJDqFDYVCfr8flG7z5s319fWpqamEkOrq6pmZGdamQCDwwgsvEEJycnJ8Pp/X6wWdHRwcZHkopY8ePeru7s7MzIQ2aR2IUnrz5s2MjAxCSFFRkc/nKyoqIoR4PJ4rV67wVZkMI+NNlootm6qq3d3dYPjLL7/s8/lATYqLi8fHx/k6wSEyMzPzvv47cuQIny1xYSuxgBUzMzPV1dVwKevr6zdt2gQ3Ib/fHwqFmKXXrl1zu91Op/PrYPIqKiqmpqZYtkQHrOfDW9Tb26vbQVjXKCkp8fl8MOjxeDzXr1/niyc6nCw4Rljm5uYqKysJIdnZ2chtzp49m2galFIEJDqFBauqqqqmp6ehrdPT01VVVYSQPXv2sNbv2bOHEML3lv7+fkVR1q1bx4R4eHgYmpKamrp27VrdW3Q4HG5oaCCE7N27NxKJUEojkcjevXsJIVu2bFlYWGBnNBlAxpssFVu2S5cuuVyu4uLisbExqIE1fseOHaqqQuLCwkJtbW16evro6GhsJ1p6KSuxUEpVVe3s7CSEtLS0sEHr2NhYcXExIeTEiRPMorNnzzocjp07d7KUpAQs5sPbeP/+/cLCQmgAPwRhXaO7uxt8id3RGxoawuEwX0lCw0mBY4SFUvrw4cOyp7+HDx8m1HCjyhGQKBQWtCAlJeXixYt87adPn3Y4HLW1tSB5jx8/9nq9Ho/n1q1bLBuUdTgc7Dbyy1/+8rXXXvvggw9CodDQ0JCuwsJD4urVq+/evcuqevDgQUFBQW5ubgwPRMh4VmciAgcPHvxisL9v3z6+8rt3765evbqsrIxdfrjlWvzYyzdJe9dFR+MehT6ALiul9PDhw4QQXk/BMSybLTGy1Eq34dsQCoWam5tdLld7ezvqIOBIpaWlk5OTrAiAtfhubT0cARZKKYhGZWXl3NwcI2NlAAGJQmGh6VotgEc5ZtLo6Gh6enpFRcWTJ094w/bt20cIOXjwIJ8IYSOFRTVDZpAkt9t97do1bVXiFGS8OHMijmoZJt0hrFdY3ctKKdW6AcxQWzN9JrjcyXIbePLr7OwcHBxECguj+8bGRvYwBA8HjY2NhJDh4WGBOfE9ZD0cARZKKXgXP96Pr72L1oaARKGwRlXD5WdDD7Cwurp6fn6eLzIwMEAI2b17N58IYW3XgvTJycnCwkI02IExbEFBwYMHD7RViVOQ8eLMiTgKtx+v1/v48WOoHwYj5eXlmzdvhiWLnJwc7ZpPIhrD6kw6FmgJ6OnRo0dZw3bv3u1wOJqamgoKCmBGu7a21mjJlJWKeyApfAKBQGlpaXl5+dTUlLaDQEpLSwsyFsYxx48fR+mJi1oMR4yFUnrx4sWUlJSqqqoNGzbA5H5JScnJkydhmjFxHFjNCMhSFTYYDNbV1blcrkuXLsE5tN4gTtcdvEARVVXh+Yifh92/fz8hpL29nb97M/PEAWS8OHPcjwaDwZaWFkIIv4QFNySQj9zc3FWrVkEj+VnsuLcEVZhcLNCYqamp8vLy3Nzce/fuseaxReFVq1bl5uZCh4l5nZNVG23Aej7hcLitrU1RFFgc1vYpuBtp50+M0qM12Xx+K+EsioWJCazG5+XlwXYmQgibsDZvWmw5EZAlKSybXG9ubmZLwFpvgIYapTMougP7mZmZN954A+0leOONN9iKWVQUkPFRlY058/DwsN/vr6+v93g8hJCOjg7GilL6n//5n2+//faOHTtgx5uqqr/61a8gJ7/mE/PZzRRMCha+YWyPyv79+9mNMxwO9/X1bd68+cyZM5A4OzsLT8H8kilfT4LC1vM5c+aMoihtbW2wZqXtO0ZKapSeIDIWTzEtigV2HzU1Nb377ruwiBqJRI4dO6YoCj8KTBwNLZAlKSxMiKDtR1pvAHuM0sUKGwwG3333XRi8gKMrisLwRUvK+q5CKQWnh1OnpqYeOHCAV1hdE2DNZ+vWrdYsCicFCzOc3afRnj+WgQ9MTk6WlpZql1v5PHEPW8wHhvOFhYX3798HW7R9x0hJjdLjzoRVaBkcM1hYq/iAqqo7duwwmqLkc8YljIDErrBXrlzxPP2hralab4B2G6ULFJYNbd566y1YM52cnHzrrbfQVjDzXJDx5gsuPWckEvnDH/5QW1tLCGFjE6NqYbqW33JglDMu6UnEQinVvU8L7Nq5cychhJ+uFWSOyyEr+bCtbL29vazx2r5jpKRG6ayquAesgWMSi5F1sDDI9jsZZYtLOgISo8LC7kWPxzMyMoKaZbTSdfz4caPbiNaBoE7YUlpTU8N2TVJKg8FgTU0Nv/ELNUAQRcYLciboEAzBFt1Po91ykKD2QLVJxDIyMuLxePhdw4taulJFBAy/cOGCy+XyeDxNTU3+L38bN24khDz//PN+v7+np2dhYQG6jHala/fu3YSQlbfSZRKLkfOAKLH9TkbZ4pKOelMsCjs+Pl5cXKwoSn9/v7ZN2uVyyBPDbi3dLaXsuVs7za9tDEpBxqOjFkRVVTWzn0a7bTahbUsWFqPHILGxICIrdQwL9w+4Irp/QSZgUIbmkUx6lxhvtEetcR6TWIwaDxsMlscYFt53VBSlr6+PLUrwhsEbB2i/Kgw8jabPjMawMOzl34CCE8X8nGiNN0AjYdsAGk08efKkoqIiNTX16tWrlFJ4EQOxopTCBjjUf3jI8Q1biYW1HO7Tuo9BkAdEpLm5mXcz2A1t5Eis8vgGksKHN0HbQeAejPYswhMS2t3I15OIcBLhaLHAOxcICxuT6W4VjTsTBCS6MSzbbyTe+gA3nKamJvZ0D3NtRkvAWlJg9u3btzMyMlAnhOdK9M6YSUzIeJOlYst24sQJQgj//Kuqal9fH3p7+MiRI4SQN998k20fBulB74/G1gaTpazEAk2Cl62NHoMgD6xsuN3uDz74AFLYmpiRI5m0N9ps1vNBLdR2kHA4DLdwtpGRwbHDW7PAR4uF7e/s7Oxk68nwqLQ89hLs2rULvtbBTxJ9OVnkZ2+SsO96ZGZmsi+/OJ1Oo5dztKSAoKqqBw4cAP8uLy/fvn07fESGEHLgwAF+aIM80ihqZVdhy3SKolRVVfl8Ptg2j7ZzzszMvPbaa+xjOuy9gxW8H3ZhYWHLli2EkLy8vNbWVuY/LHDjxg24ggMDA/AWBvq4CVpcNbrc8Uq30m1026zbQdidGD6KBF9+ycjIuHnzpm4lCUpMIhwjLOXl5eyjQlVVVbATSTwojCMcBCS6MSzbAQ61oL/8xOjs7GxHRwfs61QUpVb4Ko4uKbBZVdXLly+//vrr7BNtr7/++uXLl2OQV+1WtThi1a0qEomcOnVq0a8Xwnf8QH8JIQUFBYcPH2Z3YN2a45uIfCK+lWtrY18/Qv7Dovyd+M6dO+jqf/zxx9o6E5piMR+tLUYdJI4f9tSe1GRKEuEYYQHxycrKgraVl5f39/fHphgmIfDZEJDoFJavaDmGkfHL0YREtFliEVOVfAR8JBwEBwGRCov42DGKfMKOCIQ2Sz4CPBIOgoOASIVFfOwYRT5hRwRCmyUfAR4JB8FBQKTCIj52jCKfsCMCoc2SjwCPhIPgICBSYREfO0aRT9gRgdBmyUeAR8JBcBAQqbCIjx2jyCfsiEBos+QjwCPhIDgIiFRYxMeOUeQTdkQgtFnyEeCRcBAcBEQqLOJjxyjyCTsiENos+QjwSDgIDgIiFRbxsWMU+YQdEQhtlnwEeCQcBAcBkQqL+NgxinzCjgiENks+AjwSDoKDgEiFRXzsGEU+YUcEQpslHwEeCQfBQUCkwiI+dowin7AjAqHNko8Aj4SD4CAgUmERHztGkU/YEYHQZslHgEfCQXAQEKmwiI8do8gn7IhAaLPkI8Aj4SA4CIhUWMTHjlHkE3ZEILRZ8hHgkXAQHAQEKywcln8lAUlAEpAEYibAZFcqbMwMZUFJQBKQBPQJGCosO7AiAwBjRZq2FKMkFjE9yUfAR8JBcBAQPIZFuVdYFBm/wqyL2RyJRYxO8hHwkXAQHAREKiziY8co8gk7IhDaLPkI8Eg4CA4CIhUW8bFjFPmEHREIbZZ8BHgkHAQHAZEKi/jYMYp8wo4IhDZLPgI8Eg6Cg4BIhUV87BhFPmFHBEKbJR8BHgkHwUFApMIiPnaMIp+wIwKhzZKPAI+Eg+AgIFJhER87RpFP2BGB0GbJR4BHwkFwEBCpsIiPHaPIJ+yIQGiz5CPAI+EgOAiIVFjEx45R5BN2RCC0WfIR4JFwEBwERCos4mPHKPIJOyIQ2iz5CPBIOAgOAiIVFvGxYxT5hB0RCG2WfAR4JBwEBwGRCov42DGKfMKOCIQ2Sz4CPBIOgoOASIVFfOwYRT5hRwRCmyUfAR4JB8FBQKTCIj52jCKfsCMCoc2SjwCPhIPgICBSYREfO0aRT9gRgdBmyUeAR8JBcBCQqBU2EomcPHmypKQEKsrKyuro6JidnUWnmZ2dbW9vT0tLI4QoirJhw4Y7d+6gPBCNRCIjIyO1tbV+v183w507d2praxVFIYTk5OQcOnQoGAzq5lw0ERm/aP74Zujt7SWE5OfnT0xM8DXfuXPn9ddfdzqdhBCn0/n6668bseJLxTGcXCyU0kePHq1du5YQ0tXVZWRXKBTatm0bIcTn8xnlSVB6cvmYNNzIuxLEhFWbLDgCLIFAoKGhgXWohoaGQCDAGpzoAAISncKGQiG/3w9CsHnz5vr6+tTUVEJIdXX1zMwMa3ogEHjhhRdAEH0+n9frBZ0dHBxkeaBfdXd3Z2ZmQpt0e86VK1c8Hg8h5MUXX2Sn8/v9oVCIr8pkGBlvslRcso2NjWVnZ2sVtr+/H24e5eXlPp8Pbl0ej+fKlStxOa+ZSpKIhVKqqmpnZye0QaCwoCA2VFgzhht5l5mrv8Q8yXIeIyw3b97MyMgghJSUlPAd6vr160u01GRxBCQ6hQWrqqqqpqen4XzT09NVVVWEkD179rAW7NmzhxDC6yDoyLp165gQDw8PQ1NSU1Nh/KJVWBjaKIrS19enqiqllJ2ut7eXnc58ABlvvuASc4ZCoebmZjg7P4a9d+9ebm6ux+MZGRmBU6iq+v777zscjpqampiH6tG2NllYoJ2XL192u93QBiOFvX//fmFhIeTR+km09kabP4l8zBhu5F3Rmhlb/qTAMcISDocbGhoIId3d3aAYqqp2d3cTQhoaGsLhcGw2RlUKAYlCYRcWFmpra1NSUi5evMif8vTp0w6Ho7a2dmFhgVL6+PFjr9fr8Xhu3brFskFZh8Nx9uxZSPzlL3/52muvffDBB6FQaGhoSHdscvbsWYfDgdBcuHDB5XLFJkDIeNa8RAf6+vocDkdTU1Nubi6vsHCbaWxsBG+AZgDA9PT00dHRRDcM6k8WFkrpzMzM+vXrs7Oz4Q6kq7CgIC6Xq729XddPEk0pWXxMGm7kXYnGkiznEWC5e/fu6tWrS0tLJycnmfkPHz4sKyuzrEMhb4lCYScmJvKf/tA04rVr19xud2Vl5dzcHKV0dHQ0PT29oqLiyZMnzEhK6b59+wghBw8e5BMhbKSwXV1dhJCjR4/yRUCAVq9efffuXT7dTBgZb6bI0vOMj48XFxevXbv21q1b+U9/CCA6xdzcXGVlpdvtvnbtGjqUoGhSsMD8QHd3t8Ph6O3thWutq7DwANTZ2Tk4OGgrhTVjeLTeFXcXst55BFhgTIaGLKqqNjY2EkKGh4fjbr62QgQkCoXV1gUp4Pc7d+6EKAhudXX1/Pw8X2RgYIAQsnv3bj4RwkYK+/3vf58QMjQ0xBcJh8Nbt26NTYCQ8Xy1CQqHw+G2tjZFUc6cOWN0i0KnhltubLcQVJXJqPVYoGHXr1/3eDx1dXXBYNBIYQOBQGlpaXl5+dTUlJGfmDQz5mxJ4WPG8Bi8K2YIRgUthiPGAh7S0tKCWgvDu+PHj6P0REQRkKUqbDAYrKurc7lcly5dguYadQOjdEqp0SHoddphr8/n0yqvGVjIeDNFlphncHBQUZS2trZwOGxGYSORyP79+wkh7e3t/NTBEpshLm49Fkrp3Nzcxo0bPR4PLEHoKixTEFgjNfITsXVLP2o9H5OGR+tdS0ehrcFKOIti0fUiSqlRutacpacgIEtSWDaL3NzczBb3jbqBUbpAYWF0vHXrVn6KGpa/+Cld81CQ8eYLxpZzamqqvLy8tLQUNosIFPbGjRt+v9/n8+Xk5BBC3nzzTbYkGNupoyplMRZo25EjRxwOx4EDByCq2wfOnDnD7k8CP4nK2BgyW8/HjOHmvSsGk80XsRLOolh0vWgZKyxMiBQXF4+Pj7NLYqSkRumCnjM5OVlaWsrvJQgGgy0tLYSQZ3+WQFXV9vZ2h8PR19cHcAQKC3DAWRVFaW9vR3MsDG8iAlZ2Emj/7du3MzIy1q9fz24k2r4BClJYWHj//n0oJXChRGBhdVrMx4zhUXkXMyQRAcvgmMGi9SIw2SjdAiCxj2Fhp6p256ZRNzBKFygspRREHPbDbt682el0fvOb33S73c8999zNmzejBWSZN1BKYc/Dtm3b2OheoLBgiKqq9+/fhx3HGzduhJXDaG2MIb+VWCilsFfc5XJduHCBtRb1AbZJlt+WJ3AhVk8iAlbyMWl4DN6VCDKUUmvgmMSCvIiZbJTOMsQxgIDEqLBjY2PFxcX8Rk7WRKOVruPHj0e70gXLzefOnSsvL4d3Furr63//+997vd6ysrKHDx+yk5oMIONNloohG5vKqKur83/5a2xsdD39NTY2vv3222xohuoPBoM1NTWxTYOgqkxGLcMC7YFd1Xl5ea2trV+y8a9Zs4YQsmbNGr/ff+zYMVAQj8fT1NTE8mzcuJEQ8vzzz/v9/p6eHtgdaNLGpWSzko8Zwz/77LO1a9c6HI4YvGspHHTLWgPHDJaFhQW4B2tXunbv3k0IWTYrXbBBRFGU/v5+LXTYreX1eh8/fswfjWG3Fl+chS9duuRyuZqbm2NYCLLGGyilcJuB0+n+Fc9yCFgxDnEMWIYF2gwLlbpYINHn88GgQ5CH7Q6MIwejqqzkY8bwCxcusHc0dBGJvcvIzNjSrYFjBsvc3Bzs1kIrN8tst9bMzEx1dTU/N4ouDOxXRdcYxmXatxWgrPmnP3jAhM1P6LxmotZ4g1FLtLMEBw8e1I7rmUMMDAwYVRXf9ORiAVvMPMeZ95MVxseM4Vrvii8Eo9qS6DxaLPDGQUFBwYMHD1iDYTnHsu2PCEh0swRsoYm9lMbM4APQW5qamth7nzCdyr81y+fXkuKPsnAgENi0aRN6H5cdNRNAxpspEsc82j4A43E02TIyMuLxeHJzc+/duxfHswuqSi4WaJhUWMEFMtNBtN4lqDCOh5LoPFos4XAYVsL37t0biUTYWy3L461ZSumuXbsIIWh2jE2TsVcmYJxLCMnMzGRffnE6nejdAXaZtaTYIUrpjRs31q1bxz4Qs5SdTEn0Bkqptg+w7W6EEK/Xu337djbjrDsDw2OJYzi5WMAQqbCCCyruIFBQ612CCuN4KInOo4sF5jAJIUVFRezLLxkZGTEsjMdGCQGJbgwrnkHjX3mcnZ3t6OiAz2IpilJbWyv4Ip8uKWYeHF21atWbb755+fLlGKZfWVXIeJZuTUC3D6iqevnyZZt/vdDkjkWxnyTuIibXbcSbbZjVut7FjiYukEQ4Rv4QCARaW1vZ1wtbW1uXzdcLE3edrKk5id5gjYGxnUViEXOTfAR8JBwEBwGJbgyL6lp2UWT8smt/ghossYjBSj4CPhIOgoOASIVFfOwYRT5hRwRCmyUfAR4JB8FBQKTCIj52jCKfsCMCoc2SjwCPhIPgICBSYREfO0aRT9gRgdBmyUeAR8JBcBAQqbCIjx2jyCfsiEBos+QjwCPhIDgIiFRYxMeOUeQTdkQgtFnyEeCRcBAcBEQqLOJjxyjyCTsiENos+QjwSDgIDgIiFRbxsWMU+YQdEQhtlnwEeCQcBAcBkQqL+NgxinzCjgiENks+AjwSDoKDgEiFRXzsGEU+YUcEQpslHwEeCQfBQUCkwiI+dowin7AjAqHNko8Aj4SD4CAgUmERHztGkU/YEYHQZslHgEfCQXAQEKmwiI8do8gn7IhAaLPkI8Aj4SA4CIhUWMTHjlHkE3ZEILRZ8hHgkXAQHAQEKywcln8lAUlAEpAEYibAZFcqbMwMZUFJQBKQBPQJGCosO7AiAwBjRZq2FKMkFjE9yUfAR8JBcBAQPIZFuVdYFBm/wqyL2RyJRYxO8hHwkXAQHAREKiziY8co8gk7IhDaLPkI8Eg4CA4CIhUW8bFjFPmEHREIbZZ8BHgkHAQHAZEKi/jYMYp8wo4IhDZLPgI8Eg6Cg4BIhUV87BhFPmFHBEKbJR8BHgkHwUFApMIiPnaMIp+wIwKhzZKPAI+Eg+AgIFJhER87RpFP2BGB0GbJR4BHwkFwEBCpsIiPHaPIJ+yIQGiz5CPAI+EgOAiIVFjEx45R5BN2RCC0WfIR4JFwEBwERCos4mPHKPIJOyIQ2iz5CPBIOAgOAiIVFvGxYxT5hB0RCG2WfAR4JBwEBwGRCov42DGKfMKOCIQ2Sz4CPBIOgoOASIVFfOwYRT5hRwRCmyUfAR4JB8FBQKTCIj52jCKfsCMCoc2SjwCPhIPgICBSYREfO0aRT9gRgdBmyUeAR8JBcBCQqBU2EomcPHmypKQEKsrKyuro6JidnUWnmZ2dbW9vT0tLI4QoirJhw4Y7d+6gPMFg8NChQzk5OVBVQUHBoUOHgsEgyhYIBBoaGpxOJyHE6XQ2NDQEAgGUx2QUGW+yVLyy9fb2EkLy8/MnJib4OmdnZzs6OjweD7Cqra3VsuLzxz2cFCxgdVZWFpy9vLz85MmTkUiEtw452yuvvHL+/HlVVfk8FoSt5wNdo6CgAE5t1DXA9o8++qi5uXnVqlWEkK6uLguA8KewEo5JLHFUDN5Sk2EEJDqFDYVCfr8flG7z5s319fWpqamEkOrq6pmZGdaCQCDwwgsvEEJycnJ8Pp/X6wXtGBwcZHlmZmaqq6sJIR6Pp76+ftOmTYqiEEL8fn8oFGLZbt68mZGRQQgpKSnx+Xyg7B6P5/r16yyP+QAy3nzBpeccGxvLzs7WKixjlZmZ6fP5XnzxRWDV39+/9JOarMF6LOPj48XFxYSQrKys+vr6qqoquPrd3d1MQFVV7e7uBhqbNm2qrKy0ngwAtJgP6xppaWmbn/5geIG6BqU0FArt3bsXmpeWlvbd7373F7/4hcmLHq9slsExiSW+ihEDJQQkOoWFUVhVVdX09DSce3p6uqqqihCyZ88e1po9e/Ygrezv71cUZd26dSDEqqp2dnYSQlpaWtigdWxsDHrdiRMnoKpwONzQ0EAIYR2P9bqGhoZwOMzOaDKAjDdZaunZQqFQc3MznB2NYYFVU1MTcFBV9f3333c4HC+88MKjR4+WfmozNViMhV3W/fv3s0HrJ598UlhY6PF4bt26BW2+cOGCy+UqLi4eGxuDlJGREY/Hk52dzVLMWLf0PBbzAZfgu8b09HRFRYXD4Th9+jQzh/WF0tLSkZERdmdiGawJWAbHDBbmWvFSjBgYIiBRKOzCwkJtbW1KSsrFixf5E58+fdrhcNTW1i4sLFBKHz9+7PV6+a5CKYWyDofj7NmzlNKHDx+WlZWtXr367t27fFWHDx8mhOzcuRMS7969u3r16tLS0snJSZYNyqanp4+OjrJEkwFkvMlSS8/W19fncDiamppyc3N5hZ2bm6usrERIITE1NfXq1atLP7WZGizGMjk5WVhYmJubi2Z7du/eTQg5evQotHnnzp1fPLuw2y2llN2YDx8+bMaueOWxks/8/Hx1dbX26h8/fpzvGpTS27dvZ2RkFBcXj4+Px8vSGOqxBo5JLHFXjKUDiUJhJyYm8p/+0DTitWvX3G53ZWXl3NwcpXR0dDQ9Pb2iouLJkyd8+/bt20cIOXjwIKUUFWHZhoaGCCE+nw9Szp4963A4Ghsb+fuzqqqNjY2EkOHhYVbQZMAab0CNgSfitWvX3rp1K//pjwE0Qurz+QghQ0NDqKoERZOCRWtLV1cXm0mE24z2Pnrx4sWUlJStW7fG8ASjPaPJlGeBD+oaX7QcxnS9vb0mrUhQtuTCQVjirhgxQENAolBYo5MNDg7yd1dQz+rq6vn5eb7IwMAAIWT37t18IgpDH2OjGMDX0tKCsoFYHz9+HKUvGkXGL5p/6RnC4XBbW5uiKGfOnNHqKdyZdcewbrf72rVrS2+AmRqsx6LbKhi0wmWdmpoqLi7mx/tQBAYp7HauW0/cE58FPkePHuW7z5MnTyoqKlavXj04OLhhwwaYxS4vLz937hw/HIk7Cm2FyYWDsMRdMbT2LpqCgCxVIVqXFwAAIABJREFUYYPBYF1dncvlunTpEpwb3VVYg4zSWYapqany8vLc3Nx79+5BIj+oYdkopUbpfB7dMDJeN098EwcHBxVFaWtrC4fDWoWllB45coQQws/D9vX1KYpSV1fHZqjj2yRtbdZj0bYB5ljLy8unpqYopTCGfe65527evMln1mXIZ0hEOOl8YIkiIyPj9u3bYGAgEMjNzXU8/Tmdzs2bN8N68hfLyz/5yU8SAcGoziTC0WIxUgajdCOjlpKOgCxJYdlce3NzM9sAYKSkRulgDNulsH//fnYTNuJilL4oF2T8ovmXmAHuGaWlpTDhqKsOoVDohz/8ISGE30vg9XrRHOUSWyIubjEW1pjPP//8nXfe8fv9oA5FRUU3btyAo6qqwtogelKB5a+ysrKHDx+yehIdSAqfhYWFnp4ev98PGy2+9a1v8dNi8KSI1ophPZkfoySaDKXUYjhiLEbKYJSeCD4IyJIUFq4omms3UlKjdFjBgH05aNeXERej9EV5IeMXzb+UDKqqtre3OxyOvr4+qEdXYSORyL/+67+63W5oG/x96623tFuMl9IYcVkrsfAtASDM8FdeeeXjjz9mGWAFld9LwHab2GGWAEbxDE5paemHH37I4IDCvvTSS/yGE7aSjm5LrFQiAhY7jxiLkTIYpVsAJHaFvXLliufp78qVK3xDjZTUKJ1SqqvUgtmAmHlZ6Q0w2tq2bRsb3WsVlj0EbNq0aWxsTFXVmZmZ/fv3K4qCbjY84biHrcSi2/iZmZmf/OQnbreb3+nMHmsURdm0aROMc9PT0x0Oh3aWX7faeCUml8/CwsLAwEBeXp6iKGxHudFasXbLQbwgGNWTLDi6WIyUwSjdyKilpCMgMSosjCY8Hs/IyAhqjdFKF1x77UoXTMDxQxVWIYiydqULtvXEcKNGxrMTxT3w6NGjtWvXOhyOuro6/5e/xsZG19NfY2Pj22+/ff/+/Xv37uXm5qLtaKqq7tixg9+3FPfmoQotw4LOi6Jorx6lNBgM9vT0wNtuHo+np6fngw8+SElJYfv5UA0Jij4LfNCeSCOFFYxjViQchCXuihEDNOQtsSgsbD9SFEX3vSPYreX1eh8/fsy3j9+txdKNBsKQAfZeoK05y2K3FnQAYK37F7YKDA8PE0LQdjRKqcX9BPkEuzoWB65evZqamioen8IWJTaUs6aFzwIfWNoqLCyEveFGvcxoHJM4UMmFg7DEXTFi4IaARK2w8O6aoih9fX1sSYpvB7xxgDYbBYPBmpoatC0JlFp3IAwVwtacgoKCBw8esFNMTk6WlpZq31ZgGQQBZLwgZyIOaWcJYHdnTU0N2jYAe1AsG6lZjOXmzZvPPfec9h4M+/m09xt2LeDNY7bfgKUnOmAlH3gdQ+vecPth28yhQ7lcrgsXLjDz2fKglXcga+CYxBJ3xWBszQcQkOgUNhgMtrS08K+x6p4YZj3YDiQ208remqWUwuu2RgNhqDYcDsPp9u7dC69XsonL5fXWLJijVVjYbIBuVzADg16R1OUcr0TkE/Gq1qiemZmZdevWEUL4t2bZ69f8S1ysBlVVz507B3ORuk9OLGciAlbyYatV/FuzrN/x76YPDAw4HI7169f/6U9/AqthPcPiO5A1cExiibtixOBOCEh0Crtr1y74VktTU9OXs4tf/ct2k7BvNMAOJFijcDqd7CWlhYWFLVu2EELy8vJaW1u/quLLENu1wz4RUlRUxL78kpGRgbZJmgSBjDdZKl7ZtApLKR0YGIDvehQUFPh8PvYNlLa2NrZEFq8GGNVjPRaYHWLfBqqurjb6uMmPf/zjkpIS+Eib0+n88Y9/rPvkZGRaXNIt5sN8Hj6KtHnzZjAfLX6GQqG2tjb2GSbWywYGBuJitclKLINjEgvLFhfFMAmBz4aARKew8DYnVKH9y385TfxFPrTlQlsV02JKaSAQaG1tZV8vbG1tjXmvKDKe52JBWFdhKaXw9Tn2pcdXX3311KlT7JMoFjQsKVgmJiZ27dol/nohpRRcrqio6B/+4R/Y28YWMOFPYT2f2dnZ7u7uRb9eGIlE3n//ffj+p6IotZZ/99Li/bAmscRRMXg3MBlG3hKdwpo8xzObDRn/zLbT4oZJLGLgko+Aj4SD4CAgUmERHztGkU/YEYHQZslHgEfCQXAQEKmwiI8do8gn7IhAaLPkI8Aj4SA4CIhUWMTHjlHkE3ZEILRZ8hHgkXAQHAREKiziY8co8gk7IhDaLPkI8Eg4CA4CIhUW8bFjFPmEHREIbZZ8BHgkHAQHAZEKi/jYMYp8wo4IhDZLPgI8Eg6Cg4BIhUV87BhFPmFHBEKbJR8BHgkHwUFApMIiPnaMIp+wIwKhzZKPAI+Eg+AgIFJhER87RpFP2BGB0GbJR4BHwkFwEBCpsIiPHaPIJ+yIQGiz5CPAI+EgOAiIVFjEx45R5BN2RCC0WfIR4JFwEBwERCos4mPHKPIJOyIQ2iz5CPBIOAgOAiIVFvGxYxT5hB0RCG2WfAR4JBwEBwGRCov42DGKfMKOCIQ2Sz4CPBIOgoOAYIWFw/KvJCAJSAKSQMwEmOxKhY2ZoSwoCUgCkoA+AUOFZQdWZABgrEjTlmKUxCKmJ/kI+Eg4CA4CgsewKPcKiyLjV5h1MZsjsYjRST4CPhIOgoOASIVFfOwYRT5hRwRCmyUfAR4JB8FBQKTCIj52jCKfsCMCoc2SjwCPhIPgICBSYREfO0aRT9gRgdBmyUeAR8JBcBAQqbCIjx2jyCfsiEBos+QjwCPhIDgIiFRYxMeOUeQTdkQgtFnyEeCRcBAcBEQqLOJjxyjyCTsiENos+QjwSDgIDgIiFRbxsWMU+YQdEQhtlnwEeCQcBAcBkQqL+NgxinzCjgiENks+AjwSDoKDgEiFRXzsGEU+YUcEQpslHwEeCQfBQUCkwiI+dowin7AjAqHNko8Aj4SD4CAgUmERHztGkU/YEYHQZslHgEfCQXAQEKmwiI8do8gn7IhAaLPkI8Aj4SA4CIhUWMTHjlHkE3ZEILRZ8hHgkXAQHAREKiziY8co8gk7IhDaLPkI8Eg4CA4CErXCRiKRkydPlpSUQEVZWVkdHR2zs7PoNLOzs+3t7WlpaYQQRVE2bNhw584dlCcYDB46dCgnJweqKigoOHToUDAYRNkg+ujRo+7u7u985zvXrl3TzWAmERlvpshS8qiqev78+VdffVVRFEJIWlpaa2trIBBAdQYCgdbWVqfTCaxeffXV8+fPq6qKsiUuajEWrSGPHj1au3YtIaSrq4s/qqrquXPnXnzxReZsPT09Rh7CF4xv2Ho+0DUKCgrg1EZdw+fzQQb0d2hoKL4EBLVZDwcaEwqFtm3bRgjx+XyoeXyHcjqdup0OFYljFAGJTmFDoZDf7yeEOJ3OzZs319fXp6amEkKqq6tnZmZYKwOBwAsvvEAIycnJ8fl8Xq8XtGNwcJDlmZmZqa6uJoR4PJ76+vpNmzaBDPn9/lAoxLJFIpGRkZHXXnsN2u12u5eLwqqq2t3dDYa//PLLPp8P7iXFxcXj4+PMwCtXrng8HkJISUnJ9u3bQU0URenv72d5Eh1APpHo06H6VVXt7OyENiCF/ed//mcAWFVVVV9fD6A2btw4NzeHKklo1GI+rGukpaVtfvqDuy/qGnNzc5WVlYSQ7OzsvK//zp49m1AgfOUWw2Gn7u3thVMjhb1582ZGRgYhpKioyOfzFRUVgchcuXKFlU1oAAGJTmHBqqqqqunpaWjl9PR0VVUVIWTPnj2s3Xv27CGE8A7R39+vKMq6detAiFmnamlpYUOSsbGx4uLiL7TmxIkTUNX8/DyoMCHkL/7iLzIyMpaRwl66dMnlchUXF4+NjYE5kUhk7969hJAdO3bAEBXGboqi9PX1QYqqqn19fYqi5Obm3rt3jyFNaAD5RELPpa388uXLbrcb2sAr7L1793Jzcz0eD+sbTHqYh2hrS0SKxXyg+/BdY3p6uqKiwuFwnD59mhn48OHDsqe/hw8fskTrAxbDAQPv379fWFgIp+YVNhwONzQ0EEL27t0biUQopazTbdmyZWFhwQI+CEgUCruwsFBbW5uSknLx4kW+oadPn3Y4HLW1tWDA48ePvV6vx+O5desWywZlHQ4H3F3BOVavXn337l2Wh1J6+PBhQsjOnTshcW5urr6+ft++fX/84x/hjr2MFPbgwYNfDPb37dvHG3j37t3Vq1eXlZVBrzh79qzD4di6dWs4HGbZgsFgTU0NY8XSExdAPpG4E2lrnpmZWb9+fXZ2dnNzM5olOH78OH83grJAjDmbtsJEpFjJB0YVqampV69e5W0BGqxrUEonJiby8/MrKystHtHzraKUWgkHTh0KhZqbm10uV3t7O5olACZIWB48eFBQUJCbm6udoEO2xCWKgEShsND6/Pz8iYkJvinXrl1zu93sSo+Ojqanp1dUVDx58oTPtm/fPkLIwYMHKaWoCMs2NDSEkLFDy05hWcv5AGLY1dWFZAUyw/yaZbNpyCf4Bic0DBMpDoejt7dXi2Lnzp2EkIGBAb4NgUAgNze3sLBwcnKST09oOFl8eKO0XQM6ET+C4/NbFrYeDjwQd3Z2Dg4OIrnQFZa4SId5nghIFAprdA6wk91dwcjq6ur5+Xm+yMDAACFk9+7dfCIKQzc7evQoSqeUxgUTMl57lkSnwO3H6/U+fvzY6FxgaXp6+ujoqFGe+KYnC8v169c9Hk9dXV0wGNQqLNxmhoeHeWOfPHlSUVGBBil8hkSEk8WHt+Xo0aOo+1y8eDElJaWqqmrDhg2whlFSUnLy5El4OubLJjRsMZxAIFBaWlpeXj41NaW960xOThYWFiL3gDFsQUHBgwcPEooCKkdAlqqwwWCwrq7O5XJdunQJTqA1W5zObJ6amiovLzeaf1wBChsMBltaWgghR44cYVZrA2fOnFEUpaGhgZ860GaLYwryiTjWLKhqbm5u48aNHo/n+vXrlFKksEaX2yhdcKKlH0oKH77ZsESRkZFx+/Ztlg4dDZad8/LyYN8OIaS7u3ulbkQJh8NtbW2KosCauVZqVFWFqQN+Hnb//v2EkPb2dmuwIG9ZksKy5fLm5ma2AUBrNviEUTocZbsU9u/frwsiLl0LGc+cNaGB4eFhv9/PlsI7OjoYK+15x8fHi4uL3W735cuXtUcTlJIULEeOHHE4HAcOHACjpMKii7uwsNDT0+P3+6uqqhRF+da3voWG8zdv3mxqanr33XdhrTgSiRw7dkxRFH64g+pMRNRK54HBR1tbGww+dCVlZmbmjTfeQHsJ3njjDX6zUyI4sDoRkCUpLEyIoO1HumZTSo3SKaVMqdGuL9boZT1LANoB3FNTUw8cOGCksGytfAUPQ+Ca3r59OyMjY/369czvpcLy3s4cHtyGEFJaWvrhhx+iPCiqquqOHTvQZALKE/coEpS4188qhGfcwsLC+/fvQ6KupASDwXfffRemTaBtiqKw+xCrLXEBBCR2hYWNnPxmGoHZYoXVVWqEYPmOYcGQSCTyhz/8oba2lhDCbsK8jWwUz+9y4zMkLox8InEngpphr7jL5bpw4QI7l1RYhgIFFhYWBgYG8vLy2NMxysBHrd9rYY3zsP2dvb29zF6twrJO9NZbb8Fa6OTk5FtvvYU2j7IaEhFAQGJUWJgY8ng8IyMjqJVGK12w3US70jUyMuLxePh9o6hCiC53hQUrJicnS0tLtatYkUgEZov4XZC6HBKRiHwiEafg64Rd1Xl5ea2trf4vf2vWrCGErFmzxu/3Hzt2jFKqu9IFewHRUgZfeSLCFvPRNQHtidTNI9ilY5R/6enWwLlw4YLL5fJ4PE1NTV+6jH/jxo2EkOeff97v9/f09CwsLMAm9JqaGrbLnlJq8fZHBCQWhYW5QqP3joyWy/ndWuy6Gg2EWQYWWBkKq6pqY2MjIYSfUzMzScI4JCKAfCIRp+DrNHrXE5rB9t/Abq3jx4/zZW27W8uk4bDBwMr9wtY4Dz/VxvyED8BuUd1N6Np1VN6j4h5GQKJWWJgr5F9DQk2EUQZ6NQBuI+htBVBq3YEwqpNNS6FqtdnEKch4ceYlHoVtA0ggYLMR2k8OkyT8m3JLPHW0xa3EYtQ2NEtAKYWHnubmZn7lE4Zy6B0NozrjlW4lH939RpTSq1evpqamsm3m8M6OdgcSYNQ+KcYLhbYeK+Ggs2tnCcBn2DuTLD/crXW3gbI88QogINEpLNtvJF6Kgcvc1NTExuogIuytWUopvG5rNBDWWrvsxrAnTpwghPCzH+yNWJ4DmyThP1agNT+hKcgnEnouo8q1CvvHP/7x29/+ttvt/o//+A8oxVYCV/Bbs+y9T36+iPU79m4625bU2dnJFk7hiXAF7yVAzqNVWFhBRYM26GLoLVNUVRyjqDdFp7C7du2CzyjwsyFsWoQ9+bKekJmZyb784nQ62UtKCwsLW7ZsIYSgyThW1Y0bN5DNy05h2aS7oihVVVU+nw8+lcSvDX700UdZWVmEkMrKSmY7C7zzzjuff/454pCIKPKJRJxi0Tq1CksphRsz8GHb3f7qr/7K4vdELeYDz3bso0ibN2+Gva5op834+Hh5eTnLBpu6VvZ+WORFWoVVVfXAgQNwvcrLy7dv3w6fnSKEHDhwgH8YQlXFMYq8JTqFFc+g8Z/tmJ2d7ejogI8hKYpSW1vLf70Q5BKaovuXaTGzfNkpLHx14tSpU4KvF8KqoC4BQoj2BWVGI74B5BPxrdxkbboKC59/fOWVV6CFOTk5gu9bmjxRDNms5zM7O9vd3b3o1wuhl8FNmhBSXl7e399vjY4wjNbDYafWKixs/bx8+fLrr78OHyRzOp2vv/765cuXLcOCgESnsMy2ZRpAxi9TK+LebIlFjFTyEfCRcBAcBEQqLOJjxyjyCTsiENos+QjwSDgIDgIiFRbxsWMU+YQdEQhtlnwEeCQcBAcBkQqL+NgxinzCjgiENks+AjwSDoKDgEiFRXzsGEU+YUcEQpslHwEeCQfBQUCkwiI+dowin7AjAqHNko8Aj4SD4CAgUmERHztGkU/YEYHQZslHgEfCQXAQEKmwiI8do8gn7IhAaLPkI8Aj4SA4CIhUWMTHjlHkE3ZEILRZ8hHgkXAQHAREKiziY8co8gk7IhDaLPkI8Eg4CA4CIhUW8bFjFPmEHREIbZZ8BHgkHAQHAZEKi/jYMYp8wo4IhDZLPgI8Eg6Cg4BIhUV87BhFPmFHBEKbJR8BHgkHwUFApMIiPnaMIp+wIwKhzZKPAI+Eg+AgIFJhER87RpFP2BGB0GbJR4BHwkFwEBCssHBY/pUEJAFJQBKImQCTXamwMTOUBSUBSUAS0CdgqLDswIoMAIwVadpSjJJYxPQkHwEfCQfBQUDwGBblXmFRZPwKsy5mcyQWMTrJR8BHwkFwEBCpsIiPHaPIJ+yIQGiz5CPAI+EgOAiIVFjEx45R5BN2RCC0WfIR4JFwEBwERCos4mPHKPIJOyIQ2iz5CPBIOAgOAiIVFvGxYxT5hB0RCG2WfAR4JBwEBwGRCov42DGKfMKOCIQ2Sz4CPBIOgoOASIVFfOwYRT5hRwRCmyUfAR4JB8FBQKTCIj52jCKfsCMCoc2SjwCPhIPgICBSYREfO0aRT9gRgdBmyUeAR8JBcBAQqbCIjx2jyCfsiEBos+QjwCPhIDgIiFRYxMeOUeQTdkQgtFnyEeCRcBAcBEQqLOJjxyjyCTsiENos+QjwSDgIDgIiFRbxsWMU+YQdEQhtlnwEeCQcBAcBkQqL+NgxinzCjgiENks+AjwSDoKDgEStsJFI5OTJkyUlJVBRVlZWR0fH7OwsOs3s7Gx7e3taWhohRFGUDRs23LlzB+UJBoOHDh3KycmBqgoKCg4dOhQMBlG2O3fu1NbWKopCCHE6na+//rq2KlTEKIqMN8qWoPTe3l5CSH5+/sTEBH+KQCDQ2trqdDqB1auvvnr+/HlVVfk8CQ0nFwul9NGjR2vXriWEdHV1GVkaCoW2bdtGCPH5fEZ5EpRuPR/oGgUFBeKuEQgEGhoawHOcTmdDQ0MgEEgQBKNqrYcDLRH4QxwVw8hqQToCEp3ChkIhv98PSrd58+b6+vrU1FRCSHV19czMDDtrIBB44YUXCCE5OTk+n8/r9YJ2DA4OsjwzMzPV1dWEEI/HU19fv2nTJtBQv98fCoVYtv7+fkj3er3bt28vKiqCIleuXGF5zAeQ8eYLLj3n2NhYdna2VmGvXLni8XgIISUlJdu3b3/xxReBVX9//9JParKGJGKhlKqq2tnZCW0QKCzcn+ygsKxrpKWlbX76Aw1FXePmzZsZGRngOT6fDwY9Ho/n+vXrJq97XLIly3mM/CG+ihEDIgQkOoUFq6qqqqanp+Hc09PTVVVVhJA9e/aw1uzZs4cQwjsEmL1u3ToQYtapWlpa2KB1bGysuLj4C485ceIEVAWq5PF4RkZGICUSiezfv58QwqpiJzUTQMabKRKXPKFQqLm5Gc7Oj2Fh7KYoSl9fHwxaVVXt6+tTFCU3N/fevXtxOfuilSQLCzTs8uXLbrcb2mCksPfv3y8sLIQ8K34MC92H7xrT09MVFRUOh+P06dMALRwONzQ0EEK6u7uZ53R3dxNCGhoawuHwohc9XhmS4jxG/hB3xYiBEgIShcIuLCzU1tampKRcvHiRP/Hp06cdDkdtbe3CwgKl9PHjx16v1+Px3Lp1i2WDsg6H4+zZs5TShw8flpWVrV69+u7duywPpfTw4cOEkJ07d0Li0aNHCSG7d+/m80xOTpaWlqanp4+OjvLpZsLIeDNF4pKnr6/P4XA0NTXl5ubyCnv27FmHw7F161a+SwSDwZqaGsYqLg0QV5IsLJTSmZmZ9evXZ2dnwx1IV2Hh/uRyudrb21f8GHZ+fr66ujo1NfXq1av8VTt+/DjfNe7evbt69erS0tLJyUmWDbpVbF2DVRJtwHrnEfhD3BUjWhqUUgQkCoWdmJjIf/pD04jXrl1zu92VlZVzc3OU0tHR0fT09IqKiidPnvDt27dvHyHk4MGDlFJUhGUbGhriu5DP5yOEDA0NsQyU0rm5ucrKSrfbfe3aNT7dTBgZb6bI0vOMj48XFxevXbv21q1b+U9/DGBXV5fu5KOu4UtviVENScEC8wPd3d0Oh6O3t9cIBaUUHoA6OzsHBwd59zAyJ+7pyeLDG4K6BtybGxsb+fl6VVUbGxsJIcPDw3zZhIathyPwB92OsxTFiAEdAhKFwhqdDPyeDTxBPaurq+fn5/kiAwMD2gEpn4FSCt3s6NGjKJ2PPnjwoKCgoKys7OHDh3y6mTAy3kyRJeYJh8NtbW2Kopw5c8boFoVOAQ5h5UjEeixg8vXr1z0eT11dXTAYNFLYQCBQWlpaXl4+NTWFVAZxS1w0WXx4i9DoDFC0tLTweSilMI45fvw4Sk9c1GI4MfjDUhQjBm4IyFIVNhgM1tXVuVyuS5cuQWuMuoFROrNhamqqvLx80fnHI0eOoGlfVsOiAWT8ovmXnmFwcFBRlLa2tnA4bFJhz5w5oyiKlbNp1mOBZ5GNGzeylRldhWX3J1gjXdSFln69dGtICh++JbBEkZGRcfv2bUjXxcXGKLqTLXyFcQxbCSc2f1iKYsQACgFZksKqqgqT683NzWwDgFE3MEoHG9guhf379/MPPshCWHkvLCy8f/8+OmQmiow3U2QpeeCeUVpaCntozCgsTCm43e7Lly8v5dRRlbUYC7TtyJEjDofjwIEDENWVDLjZwP2JUip2oahMjipzUvgsLCz09PT4/f6qqipFUb71rW/xz/66uFa8wsbgD0tUjKj8BDIjb1mSwsKESHFx8fj4OGuKUTcwSmfzcdpdX6xOCID6KIoS804mZDyqP75RVVXb29sdDkdfXx/UvKjCsm06bIE4vk0yqs1KLNCG27dvZ2RkrF+/nm3y00oG3J/4u6nAhYxMi0u69XzYegOcmhBSWlr64YcfMnO0uOCQUTorGPeAZXBi8IelK0YMuBCQ2BUWbg4ejwdtTTXqBkbpbCkDKTWyLS7qg4xHp4hv9MKFCy6Xa9u2bWx0L1ZYNornd7nFt0lGtVmJhVIKe8VdLteFCxdYk5A0sP18vb29LI/AhVieRAQs5oNMWFhYGBgYyMvLUxSF7ShHuFgRo3SWIe4Ba+DE4A9xUYwYcCEgMSosTAzxO1VZU4xWumC7Cdp6RSkdGRnxeDzFxcVjY2OsEhQIBoMtLS2EkP3790ciEXTUfBQZb75gtDlho6vD4airq/N/+WtsbHQ9/TU2Nr799tv8RAfb58vvgoz2pDHntwwLtBB2Vefl5bW2tn7Jxr9mzRpCyJo1a/x+/7Fjx+D+5PF4mpqaWJ6NGzcSQp5//nm/39/T0wO7A2O22nxBi/noNgztiYSbjXala/fu3YSQlbfSFa0/xEsxdK+FOBF5SywKKx57w24tr9f7+PFjvin8bi2WbjQQZhlgyAMvki19cIeM588S3zDcZuB0un/53WZsOhu9GhffJglqswwLtAG21OhigUSfzwdjMUEetjtQYFe8DlnMR7fZgUAgNze3sLAQNsDq7qRewbu1ovKHJD4OLmk/LFx4GHvzryEhh4A3DngFoZTCLnr0tgIote5AmNXJ1Ccug7vkdhWjWQKYzubflGPmWxNILhaw0czjrR1mCSYnJwsLC7Uv41y9ejU1NZVtM4c3DgoKCh48eMCcBF7G0ZZlGRIRSKLz6PpDfBUjBmIISHRjWDb2Fi/FQG9pampib8SCiPCvusLrtuJlK/YKabwGd8j4GPAtpYiuwrJJEn61cClniaFscrFAg6XCAgf2Oiw/pGD9jr2bHg6HYd5s7969MG/GlMXKfX7aIVsM7hdzEa3Cxl0xYmgb6k3RKeyuXbvgwyv87BibJmO7Sdgcc2ZmJvvyi9PpZG9nLSwsbNmyhRCCJuNYVTdu3KCU/uaDOylIAAACU0lEQVQ3v1EUBc1msjwxzMQh42PAt5QiWoX96KOPsrKyCCGVlZXMLhZ45513Pv/886Wc0WTZ5GKBRkqFZRcLnu3YR5E2b94M36hD4wyWraioiH35JSMj4+bNm6wqCwJJdB6twsZdMWIAiIBEp7DiGTR+n/Ps7GxHRwd8NUpRlNraWv6Tg/DaEjRF9y9oMRDUzQCqBO/pmqeAjDdfMC45tQornq7lv2AQlwYYVfL/tXMHKQ5CURBFR+7Uzbhcd5CB8CMXKUz4JELdSfB1d2zr5KXIIPhfluOqbNjzq7Pv+7Ztd+5eOO57uSzLuq49dy+8/H709MY4vyg3j/Fu+qxhb/6Px/4Zwj/2On98YbJkcH2CjzjAAYgNC5/GETvRSBAz6xN4xAEOQGxY+DSO2IlGgphZn8AjDnAAYsPCp3HETjQSxMz6BB5xgAMQGxY+jSN2opEgZtYn8IgDHIDYsPBpHLETjQQxsz6BRxzgAMSGhU/jiJ1oJIiZ9Qk84gAHIDYsfBpH7EQjQcysT+ARBzgAsWHh0zhiJxoJYmZ9Ao84wAGIDQufxhE70UgQM+sTeMQBDkBsWPg0jtiJRoKYWZ/AIw5wAGLDwqdxxE40EsTM+gQecYADEBsWPo0jdqKRIGbWJ/CIAxyA2LDwaRyxE40EMbM+gUcc4ADEhoVP44idaCSImfUJPOIAByBs2OPXPiqggAIKfC0wateG/drQJyqggALXAhcNO37kgQIKKKDAFIH3Z9gpp/MkCiiggAJDwIYdFB4ooIACkwVs2Mmgnk4BBRQYAi/3ZSdiMxBP/AAAAABJRU5ErkJggg==\" width=\"265\" height=\"202\"><br>What is the difference between the average production of plant 1 and plant 2 during five years?</p>",
                    question_hi: "<p>55. दी गई तालिका का अध्ययन कीजिए और निम्नलिखित प्रश्न का उत्तर दीजिए।<br>निम्नलिखित तालिका पाँच वर्षों में एक कंपनी के चार संयंत्रों द्वारा प्रिंटरों के उत्पादन को दर्शाती है।<br><img src=\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAc0AAAFgCAIAAABWpbV6AAAgAElEQVR4Aeydf2wUR5r3a3qxIw8CTdDAGSzbkYmFF2tZopglUgxey+IgjgXsERyfdTe2sCfrHEt8m12RldeS94Tw2gh0K7h74XJZ3Ulmg5FsnezNwgXEXQJrBASBE35ocz7Am+EcW2AyxtaMNDPqejM8l7raqql2z68e7H78B6l6pqq6nk8//e3qquoOofiHBJAAEkAC6SRA0tk4to0EkAASQAL0T3SW4B8SQAJIAAmkggB/e0GdTQVRbAMJIAEk8KcEZtFZ/uf5lwYU888v9AgJIIGnhIAsMjHGs09JX9PUDRlBmg6EzSIBJGBPArLIoM7aMxLQaySABNJFAHWWygjSBRvbRQJIwJYEZJHB8awtAwGdRgJIIG0EUGdxPJu24MKGkQASeEIAdRZ1Fi8FJIAE0ksAdRZ1Nr0Rhq0jASSAOos6i1cBEkAC6SWQeZ29du3aunXroB8vvfTSxx9/rOt6ep3+09ZlBH/6O+aQABJAAkkRkEXG0v0Gly9fdrlcP//5zwOBgM/n2759+9mzZ5NyKP7KMoL428AaSMAiAtPT0xUVFRC0K1asOHLkSCgUMn/srq4uqJuVlVVXV/fll1+ar4slEyYgi4ylOtvU1JSbm3vnzp2EHUi+oowg+TaxBSSQbgK6rn/66acvvPBCS0sLSO3g4OAzzzyze/fucDg869FnZmba29tLSkpGR0cppX6/f8uWLc8999znn38+a10sEC8BWWSs01m4MxcUFIyNjcXb7xSWlxGksHFsCgmklcDo6GhJScnRo0cppR6PhxBSVFQ0Pj5u5qC6rre3t1dVVfn9/qtXrzqdTkLI4cOHzdTFMnERkEXGOp2llDY1NS1ZsuT27dtxdTq1hWUEqW0fW0MCaSVw5syZ3Nzca9euxTWehS49fPhw/fr13d3djx492rJli9vtHh4eTmtv7dm4LDKW6mx/f7/D4Thy5EgG6csIMtgZPPTTRuD27dtvvPHG9PT009Yx1p9wONzU1LRp06bEOgkyffnyZdYgn5ienn7jjTcyOxLi+zNH07LIWKqzgUCgpqamqKjo3r17mSIoI8hUT/C4TxuB69ev5+fnf//733/48OHT1je+Pzdv3ly2bBnMHvB2M+lQKLRz506VTE9PT7/yyitut/uTTz4x0xqWiUlAFpk4dHZwcBDqp+rfioqKxO7JMX0zaZQRmKxo22LxnneXy5VYhGRlZQkVzTdlsqTT6bx69WrMU+n3+zds2FBcXAwrRXwZ8wQWLlwouGCclV1+5plnjKsIvwoLHmNjYwUFBUIZg2xXVxfv6deTez6fr6SkpLS0dGJiQvgJsyYJAHC+cBw6y1eT0x6Phz/l7Hy/8MIL/OgAQlY+u3KDabLICNJ0IPs0e/Hixezs7N27dxtvhYY9RoODgzwZWB3l77iwROPxePhiQjquNVUIRf4QQmuUUl3XOzs7NU07c+aM/KuxxePxGMi3XDcul8+fP5+dnV1XV8dvKjDjEaVURTLmieD7eebMGU3T2tvbjU8oXwXTPAFZZNKrsw6HgxDS2trKThjqLH8+5kcahGP16tUPHjww8Cjm5R2X6LDGU66zd+/ezcvL27x5cyAQYEcxmUirzsJjvsvlunHjButPunU2EAhs3rzZ7XbfvHmTHRQT5glYrbMbNmzYs2ePpml9fX3QS9RZ82drDpUUnmZi9vxp1tkjR44QQk6cOBGz58bGtOospfT48eOEkGPHjrFupFtnKaUnTpwghHR0dLCDYsI8gdTorK7r586dW7t2LTRXWFjY3d1dU1MjzxtUVFT4fL6qqio27YU6a/5szZWSwWCwurqaP/sxe/7U6iyMjmd9g0bX9f/3//5fTk4OhD3/Lz9vcOLEiTfeeIOfKxNoxDuEh8f/pqYm1o4FOnvnzp3c3Nz169f7/X52XEyYJJACndV1vbu72+FwtLe3z8zMhEKhnp4e2PPMX2l8KNy8edPtdu/cuTMUCqHOmjxVc6gYCIEwhxgKhY4cOVJYWMjrESEksfnZqamp7u5uoTU+3gxw8aEYs9jt27eXLFlSVVU1MzMTswAYh4eHFy1aVF1dPTU1ZVDs0KFDvOwODw8/++yzAgRCCD9frJpIhaPIv87qkaoi2GPe8ASP4N65aNEi3GArkDGTTYHO3rhxw+VyNTY28u9Z9/X1aZrGx70QCn19fQsWLDh48CDqrJnzNFfKBIPB/v7+/Px8l8slbMk8ePAg3IzZgCjm5W1mcDc2Nva9733P7XafPHkyGAyq4AwODsrL90zgeF0TWoCY5AeMQgHIgt7NuoR74cKFBQsWHD9+nDVy8eJFt9vtcrmuXbtGKZ3V5aGhobVr17IVuZ6eHuvnDSile/bsIYT09/czRzBhkkAKdPbQoUOEkIGBAf6Quq7X19fzOjs+Pl5UVMSCOxwOt7S0uFyud955hxAya7Dyjac2LSNIbfvzsrVgMPjDH/7Q4/Gw9Uy4jwLMb33rWx988AHv+IMHD1avXs3OPvx07NixxMazHR0dDodj1gse5jEJIXwcUkqFWz7fT0jDDWDWmIS1sra2NrkF3gK+79ixg98hMDAwoGlaTU1NIBCYVWfh+S8/P/9f//Vff/WrXzmdzry8vLt377KjzOoRlJQHwmCPecNjjbOESSysPCYYAVlk4t5v0NXVxT8WsaaFlZBwOFxXV1dWVvbo0SMo4/f7q6qqoAezxjRrNuUJGUHKDzEvG2xqapJ3FEQikc8++6y6ujorK4vXQRACYW8WbP8S3gaET7jxiiyow8zMTFVVVV5ens/nMwYLGz/Tp7PwfYCCgoJdu3YtW7YMAmnZsmV79+4VZhI6OjqEHQKwgg+P4bPqLBwI2ieEuN3uc+fO8b7PqrMnT55k1VUJYQKHbx/ScN/av3+//BNajAkAc75M3DoL9E+dOsW3Eg6Hd+zYIYwjLl++/Oyzz8I0rq7rt27d2rRpE/QAdZanNyfSMCKL+R1LuIPyYy4Qgurqav4xPxQKeb3eoqKi69ev67ru9/uPHDkCT/qz6qwQWpTSL7/8sq2t7d133+Xnrz744AOn0yncD2ZVJfMDN3hxfOPGjZ999lnkyd8nn3xSUlLywgsv8Lv6R0ZGli9fLuw/haOcOnVqVp1lLws4HI5/+qd/4hlCqMzq0dmzZ2FLJSFEuNtRSk2OZ2E6Ra4+J8I1s51Mgc5+8cUXzz//fFNTE/9YdOnSJafTKV8Mt27d2rhxo6ZphJCsrKzNmze/8cYbOG+Q2SBI7Ojj4+MlJSXCeWdNwd2XTUqClPDKCyUDgcCBAwfYYHDlypU//vGP165da6CzMFEofH4InpYgmoVb/vT09FdffcU6lsJ5A1gaev7557/44gu+/dOnTzscDn7fFaX04MGDixYtunTpEivZ1tYGXpjRWUop3Njg81qsEUjMqrOw6xb47NmzR6huUmdxPCtwM59Ngc5SSvv7+7/e3fKLX/wC9hucOnXK7XbLz2sxu4XrYDGxzAkjTBnx2sG6LZ/Wjo4OQogwS8DKs4QZ0YGZhM7Ozpizw4LOspZZYlZVMjmeVbUDC1/CI1ooFGppaVm6dOnp06cjkcjw8HBBQUFLS0s4HDbjMryi1t7eTgjxer38mN3MnQPG+xs2bIg5pjGpsyaxMM6YYARSo7OU0lu3blVXV8NA1eVy/c3f/M33v/99eTzLDswS8gXJfrImISOw5rjz4CgPHz5ct25dzBEWnFZ+TAfPzvI+BIGDGdHRdR1mGH70ox/Bt1b/+Mc/rlixghBi5jV8lT6ynkDnZ91vAO2Ul5c/fvyY1Z2ZmXn99deF/QDwayQSOXny5KpVq+Bh7m//9m9h35gZl6EFtqTR2dkZiUTYQWf1CEoGAoH/+I//kF/SM6mzuN+AAY83IYtM3POzqkMK62CqYqizKjJzwg7LVk1NTfz7qbqu7969W1j5oZTChiT2ikpMB82Lzq1bt15//XX4UIumaS6X65lnnvn9738fs1neOKsqmdw/C/MG/CLw/fv3i4uLCSEx7z18H/i0eZe/njOZnJysrKyEV7PYqHZWj/jDyWkzOov7Z2Vu5i2os/j/uzUfLbFLXrlyJT8/f926dUNDQ5FIJBQKvfvuuwsWLOju7mbP9VDza12A2YOvJ3ZVuwXiEh2+Q7D5SX6m5stAelZVgj7M+j4YexuVqaqu6z/72c/k/QZyH3hLvC5/+eWXMANQV1cHI+JZPeIPJ6fN6Oz9+/cLCwvxfTCZnhkL6izqrJk4maXMzMzMr371K/Z21qpVq06ePMk/2LL6kUjkV7/61Y9+9CM2FmM/QSJe0WHVdV1vbW0lhPz4xz/mB9esAEuYUSWT3zeALROEkL/4i7+YnJxkh4grkYDLfr/f4/GwnXNmPDLokhmdhcU9/L6BAUaDn9KoswZHfap+khE8Vd3Dzpgn4PP51qxZs2XLFvbKmfm6Qkl4BwHeIxB+ErIzMzONjY2EkPz8/DNnzsS8uwhV5lwWtnPg97oSPnGyyKRsfjbhPllcUUZgcQfwcCkk8PjxY+PBrMljxfX92Ugk8v777y9evJgQsmbNmv/+7/82eZS5Ugw+eivs/50rnX8a+imLDOrs03BesA+ZJwD/PwUzGxigrzB58oMf/MD6/ydIWmHFyyGtnZmjjaPO4vzsHA1dK7oN/3+wV155ZZ5Jp3l2gUDA4/Hg/x/MPLGYJVFnUWdjBgYa/5fA7du3Gxoakp/wnaNA8f93m5IThzqLOpuSQMJGkAASUBJAnUWdVQYH/oAEkEBKCKDOos6mJJCwESSABJQEUGdRZ5XBgT8gASSQEgKos6izKQkkbAQJIAElAdRZ1FllcOAPSAAJpIQA6izqbEoCCRtBAkhASQB1FnVWGRz4AxJAAikhgDqLOpuSQMJGkAASUBJAnUWdVQYH/oAEkEBKCKDO/q/OAgj8FwkgASSQJgK8ZNv0e11pIovNIgEkgASAAOrsn9xdeBy2TcuRYVsUMR1HPjGxgBHhCHBkIH+iOPLPQv15kLWDjwmcJsRiDA35GPBBOAIcGQjqrIDIplk5MmwKQuE28lGAiZoRjgBHBoI6KyCyaVaODJuCULiNfBRgomaEI8CRgaDOCohsmpUjw6YgFG4jHwWYqBnhCHBkIKizAiKbZuXIsCkIhdvIRwEmakY4AhwZCOqsgMimWTkybApC4TbyUYCJmhGOAEcGgjorILJpVo4Mm4JQuI18FGCiZoQjwJGBoM4KiGyalSPDpiAUbiMfBZioGeEIcGQgqLMCIptm5ciwKQiF28hHASZqRjgCHBkI6qyAyKZZOTJsCkLhNvJRgImaEY4ARwaCOisgSjA7ODj4zDPP7N69OxwOGzQRDod37979zDPPDA4OGhSz/ic5MlLVh+np6VdeecXtdg8PDxu3OTw87Ha7X3nllenpaeOS1v+aJj7m48E8xnkAZ65jkaMFdTY1YenxeAghRUVF4+PjBi2OjY0VFBQQQpqamgyKWf+THBmp6sPVq1edTich5NChQ8ZtdnV1fV1s0aJFsyqycTvp+DVNfMzHg3mM6XDfuM2Uw5nrWGQgqLPGIWT2VxzPqkiZH4jheFbFkFJqHqNBI2n6SZaVJA+E49kkAWa+espjIvMupaIHiMWYIvIx4INwBDgyEBzPCohsmpUjw6YgFG4jHwWYqBnhCHBkIKizAiIxOz09XVFRAeBM/vtnf/ZnJku6XC4zJY0XzR4+fNjd3f2d73zn6tWrYu9N56Ebpov/b8HBwUEz/WdlnnnmGZY2TpgkU1FREXPRLBAIHD58eMWKFXCUwsLCw4cPBwKBeB1k5aEdljWTYJOMxp7yv5r0mq9ikC4oKBgbG5O7CnAKCwuhrsVwnlosPKhQKLRz505CiMfj4e0m0wCWL4w6y9OYPR0Oh+vq6lwu140bN/jSoDhdXV28ERZ2Tpw4IRuNpZNS6vF4nE6ngXRGIpGhoaEtW7bASTUuzHcgZlqOjJjFjI0XL17Mzs7evXu3ruusJNyohGv+iy++eP7559evX+/3+1lJuAJV0smKwYqQwQXg9/urqqoIIS6Xq7a2duvWrZqmEUK8Xm8oFGLtxJVIno/f71+/fn1JSYmwUgpBwsdDOBxuamrKzs6+ePEi38lZQ4JSOitDBmfhwoXbnvxlZWVlEM5TgoXnTCnt6emBM24QZkIVPitHC+osz8dU+sSJE4QQQVJj6uydO3dyc3N37NjBb/aSr6uYRzW+qGZmZkBKCCHf/va33W7306CzcM0UFhbev3+fORVTZymlbW1tCxYsuHDhAis5q0ZASWOd1XW9vb0ddnSwAezIyEhxcTEhRLjnsUPPmpCvnFmryAU6OjoIIQMDA/xPMePhwoULCxYsaGtr40sahwSUnJUh9KGpqYnBmZycLC8vdzgcp0+f5g9nPp0knKcBC+/svXv3ioqKUGd5JnGnk4wJSunt27eXLFlSXV0dDAbZ4WPqLEjM6tWrHzx4wErGvK7YryxhfFFNT0/X1tbu37//iy++gKM8DTpLKd2zZ4/D4Th79ixzRKWzQOzYsWOs5KwaASWNdfbBgwerV6/Ozc29c+cOa5lSeuTIEULInj17eKP5dPJhQyk9e/asw+EQ+hAzHgCFEGPGIQG+GDOE23NOTs6VK1d4348fP55BOBnHwqMIhUKNjY3Z2dmtra04b8CTiS+d/AXz6NGjsid/jx49Ysc20FnhkZm/rnRdHx4ebmtr++53vwuPbzU1Nffu3TMzb8AO/VTpLFyxx48fF7onQKCUysQEjZiamurp6dmxY8fSpUsJIUuXLn333XcjkYixzsKv8uQDHC6xx8BULfWYf74RUABMXmcDgcBvf/vbhoYG2I6dk5Pzi1/8IhAIxKzIzoUqkVk4TxWWvr4+TdPa29sHBgZQZ1UBM7s9YZ2NRCIw7RhzlCqrBqUUZiHLysp4RRZ0dvfu3ZqmeTyezz77LBKJfPrpp+vXrx8YGPjrv/5rk0PUp0Fn2cQIcOBHqarxLHDgFVnQiBs3brhcrpUrV/7Lv/yL3+8PBAJ/93d/t3Pnzg8//NDpdMarmHA4vmOzxwpXIuGwoZSyyAEHhVEqHw/sgHC3EGaceJ0dHx8vKSlZunRpd3f3+Ph4JBL59a9/XVFRMTQ0VFBQIN9mWMsxE8eOHSOECNMUMUvGNCYG5ynE4vP5SkpKSktLJyYmkrn3yEBwfjZm5IjGq1evfr1uAxNYjx8/Li8vF64WWWfv3r27detWeSZXuK6CT/74442Ojq5Zs8b55M9gHYxVyazOBoPBH//4x9XV1TDf19/fP+u8QSgU6unpcTqdzz///BdffMEcEXRW1/XHjx/zS2q6rnd3dy9evNjhcMSlsxMTE6WlpXl5eXfv3mWHiyshXzkmq//bv/3b888/D6umPp8vLy/PeN5A1/VPP/107dq1Doejv7+fPwqvs5TSx48fRyIRvkBfX9+SJUs0TYtLZ2Hy2u1237x5k2/NfDoBOE8hlnA43NLSomkaTKCjzpoPgBglE4gJNjLdsWNHIBA4f/58dnY2tCP8yxbHTp06BT81NjbOzMzw/RB0lv+JpS9fvuxyuebKeLarqys7O/vcuXNsN4yAhRDC5g3YCp7b7T537hxz2cxa+derZ6FQyOv1xvVAx6p0dnbyqs0fetY0eDRrMbkAjEx/9rOfRSIRtoot82H7DQ4dOgS/7tu3T9gdIeisfCy4DxFCZtXZYDB44MABr9dbWVmpadrixYtPnTolN2jSkgCcpxDLmTNnNE1raWmBhzPUWZNnP3axBGICGjp69CghJDs7+7vf/e7o6KjQujCevXv3bl5e3vbt24VLhVI6q87CM2BWVtZc0VkYLRJCnE7nwYMHBS2T5w06Ojq+9a1vffTRRwJDYTwr/ApZn88HGy1MjmeZ9FRVVfF7yGI2bmBMOGx0XYcVlZycnFdeeUXugxAPsDfupz/9qdyZWXV2amoKvrMxq87CSQGnCCElJSXy6ZA7oLIkAOdpwwIxXFRUBKsjbP3AZJgJZGQgOG8gIFJmYeMhe6wQygk6C2EkP4tNTk5WVlYSQtj4RWjH5/PBbAPI1tM/bwD9h9WDTZs2yW8NyDp78+ZNt9vd2trKK7Ku6729vQbPvJFI5De/+Q18ksb8eBY6VlxcLN8aBfLGWfnKMS7P/zo6OlpcXOxyua5du8bbIS3obCAQqKmpgSlCvjA83atuvbqunzt3Lj8/H/o5q86yloPBYH9/f35+viqwWUmDRGJwnh4sbC9gT08PcxPHswxFIonEYgKOBI/zMS9aQWcppaOjo6WlpSUlJZ988kkkEoGAXrlyJXRA1lleR37605/u2LFDdVEJbmd2fhY6w57Nu7u7efWED6BUVFSweQNKqa7rBw8e/PrdgV/84hd+v1/X9Xv37rW0tBhoBLv9rFy58h/+4R9MroMNDQ25XK7i4uKRkREBWrzZZMKGUgpyH3NMLegspfSTTz5xu91VVVUjIyO6rvv9/vfee2/x4sWqW+/U1NSbb75JCFm8ePHRo0fz8vLM6yxwOH36tMPhEJYczCNKGM5TggVmAl0uV0NDg/ebv02bNhFCnnvuOa/Xe+DAAX4T56xkZCA4np0V2p8UOHjwICFEvmBknaWUTk1N7d27l71MWVBQ8MMf/nDXrl3yePbLL7/8wQ9+QAjJz88/d+6cruuzPiSybj0NOksphbVaTdP6+vpY32LqLEjtxx9//NJLL0FELly4cP369QcOHFi6dKmgETDOBZV58803p6amYGpv1gc6uCm6XK7Lly/z/UksLV85cbUDaywxX7uSdRZgNjc3w1Y/TdOef/75n//855s2bZJvvR999BHcvLdu3erz+czMvcg9hwW6Wb/qKVcES8JwnhIscArAi5j/CjGp4sDsMhDUWQbHVIIN3Pg3athsDlsHM2hLuK54HfF4PFNTU1B3zukspZRJ29DQECMgzxuwn4SErBHs9uN2u8+cOQMjZTM6y55J+Z4Ih4srK185cVWnlLIXXjs7O/l9AkI8GDQrhMTMzMyePXsIIVlZWb/+9a+hTZkh3+D4+HhRUZH8EseVK1dycnLKy8sfP37MlzeZTgbO04Alpps4bxATi1ljMjEBx/D7/a+99hoh5K/+6q/YXoKY49mYfRKuK3j/ZPHixb29vfwTt3BRxWwKjE/JeBY688EHHzidTpfLxV4JS0ZnL126tGjRoh/84AdffvklIzCrzsIkuDyyZi0kkEg+bGCUWlZWRgjp6OhgC6RCPBj0TQgJeD103bp1n3/+OatlrLPwdQ7hpeRAINDU1AS9Yu3ElUgSjs/nyyyWmM6izsbEYtaYZEzAYZjUrl+/fmJiIpnx7Nfvqk5OTkIjvA/CRcX/JKSfKp2llILUapr2z//8z6p5A8EFyMoaoev6yMgIP/qjlBrrbDAY3L59O8zANDc3fzPb9n//vX79esyjGxtTEja81NbV1cFNOmGdhTUA9pkCFUPBLxjps4/sbNu2beHChTGnwoSKBtnk4TCpzRQW2TvUWZlJHJbkYwIOFggEfvKTnxw4cAAGoQmPZ1Vdn7s6Sym9cuXKn//5n8MSfzLj2ZhwjHUWDgdnOea/8gpkzKMIxlSFDdxW//Iv/5LNYiejs0InTe5Bnpqa6u7uztR3EeU+g2VycjKzWISOoc4KQOLLpvCCie/AT3dpxGJ8fpCPAR+EI8CRgeA6mIDIplk5MmwKQuE28lGAiZoRjgBHBoI6KyCyaVaODJuCULiNfBRgomaEI8CRgaDOCohsmpUjw6YgFG4jHwWYqBnhCHBkIKizAiKbZuXIsCkIhdvIRwEmakY4AhwZCOqsgMimWTkybApC4TbyUYCJmhGOAEcGgjorILJpVo4Mm4JQuI18FGCiZoQjwJGBoM4KiGyalSPDpiAUbiMfBZioGeEIcGQgqLMCIptm5ciwKQiF28hHASZqRjgCHBkI6qyAyKZZOTJsCkLhNvJRgImaEY4ARwaCOisgsmlWjgybglC4jXwUYKJmhCPAkYGgzgqIbJqVI8OmIBRuIx8FmKgZ4QhwZCCoswIim2blyLApCIXbyEcBJmpGOAIcGQjqrIDIplk5MmwKQuE28lGAiZoRjgBHBoI6KyCyaVaODJuCULiNfBRgomaEI8CRgcTQWSiE/yIBJIAEkEDCBHjxRZ1NGCNWRAJIAAkoCcyis/zP8y8NVOafX0l6hFiMASIfAz4IR4AjA4kxnhXqzLOsjGCeOZiYO4jFmBvyMeCDcAQ4MhDUWQGRTbNyZNgUhMJt5KMAEzUjHAGODAR1VkBk06wcGTYFoXAb+SjARM0IR4AjA0GdFRDZNCtHhk1BKNxGPgowUTPCEeDIQFBnBUQ2zcqRYVMQCreRjwJM1IxwBDgyENRZAZFNs3Jk2BSEwm3kowATNSMcAY4MBHVWQGTTrBwZNgWhcBv5KMBEzQhHgCMDQZ0VENk0K0eGTUEo3EY+CjBRM8IR4MhAUGcFRDbNypFhUxAKt5GPAkzUjHAEODIQ1FkBkU2zcmTYFITCbeSjABM1IxwBjgwEdVZAZNOsHBk2BaFwG/kowETNCEeAIwNBnRUQ2TQrR4ZNQSjcRj4KMFEzwhHgyEBQZwVENs3KkWFTEAq3kY8CTNSMcAQ4MhDUWQGRTbNyZNgUhMJt5KMAEzUjHAGODAR1VkBk06wcGTYFoXAb+SjARM0IR4AjA0lEZyORyMmTJ1etWgXNLVu2bO/evVNTU8LBpqamWltbFy5cSAjRNG3jxo23bt0SykA2EokMDQ1VV1d7vd6YBXw+X3Nzc1ZWFiEkKyurubnZ5/PFLDmrUUYwa5VkCui6/vHHH7/88suaphFCFi5cGLPzXV1d0DHh366urmSObr6uxVigY4FA4PDhwytWrICjFxYWHj58OBAI8N0eGxsrKCgQsBBCCgoKxsbG+JJpTWeED/MoFArt3LmTEOLxeJgREj6fr97/+z8AACAASURBVK6ujl0adXV1CV8aQsvms5mCY4DF4/HIMUMIGRwcNO9XwiVlIHHrbCgU8nq9oHfbtm2rra3NyckhhFRVVfn9ftYzn8+3Zs0aQsiKFSs8Hk9ZWRmo7cDAACtDKX348GF3d/fSpUuhZ3IYUUqHh4fdbjchZOXKlR6PZ+XKlYQQl8t1+fJlvimTaRmByYoJFNN1vbu7Gxz/3ve+5/F4QFOKi4tHR0f5BiEsli5dmv+nf0ePHuWLpS9tJRbwwu/3V1VVwamsra3dunUr3Iq8Xm8oFGKeXr161el0ZmVl/SmY/PLy8omJCVYs3Qnr+fAe9fT0xLxA2KWxatUqj8cDQx+Xy3Xt2jW+errTmYKjwjI9PV1RUUEIWb58uRA2Z8+eTTeNmAP8uHUWfKusrJycnIQeT05OVlZWEkI6OjqYDx0dHYQQ/prp6+vTNG39+vVMjk+dOgVnKCcnZ926dTFv1+FwuK6ujhCyb9++SCRCKY1EIvv27SOEbN++PRgMsiOaTFgZExcvXszOzi4uLh4ZGYHusc7v3r1b13UwBoPB6urqJUuW3L5926QXKS9mJRZKqa7r7e3thJCmpiY2gB0ZGSkuLiaEnDhxgjl49uxZh8OxZ88eZslIwmI+vI/37t0rKiqCDvADEXZpdHd3Qyyx+3pdXV04HOYbSWs6I3BUWCilDx48WP3k78GDB2l1XNW4DCQ+nQVFWLBgwYULF/hjnD592uFwVFdXg/A9evSorKzM5XLduHGDFYO6DoeD3VJ+97vfbdmy5cMPPwyFQoODgzF1Fh4bc3Nz79y5w5q6f/9+YWFhXl5eAo9IMgLWbMoThw4d+nrgv3//fr7lO3fu5Obmrl69mgUB3H4tfhDmuxTzDiwUSG0WrgThtFJKjxw5QgjhVRUCw7L5E5WbVoYN34dQKNTY2Jidnd3a2ipcIBBIJSUl4+PjrAqAtfiebT0cAyyUUhCNioqK6elpRsbKhAwkPp0FB2RFgIc75tjt27eXLFlSXl7++PFj3r39+/cTQg4dOsQbIa3SWaFlKAzC5HQ6r169KjdlbJERGJdP+a8yw4yHhfU6G/O0UkrlMICZa2um1QzOdabCBp4C29vbBwYGBJ2FkX59fT17MIIHhfr6ekLIqVOnDNxJ7U/WwzHAQimF6OLH/qn1d9bWZCDx6azqABAEbBgCflZVVc3MzPBV+vv7CSFtbW28EdLyBQb28fHxoqIiYeAD49nCwsL79+/LTRlbZATG5VP+K9yEysrKHj16BI3DwKS0tHTbtm2woLFixQp5RSjlPeEbzDgW6Ayo6rFjx1jf2traHA5HQ0NDYWEhzHRXV1erFlRZrZQnMsLH5/OVlJSUlpZOTEzIFwhYmpqaBGdhNHP8+HHBnr6sxXCMsVBKL1y4sGDBgsrKyo0bN8Kk/6pVq06ePAkTj+njwFqWgaRAZwOBQE1NTXZ29sWLF+FIckwY22MOZKCKruvwxMTPz3Z2dhJCWltb+Ts5c9I4ISMwLp/aXwOBQFNTEyGEX+CC2xKISF5e3rPPPgud5Ge3U9sNubXMYoH+TExMlJaW5uXl3b17l/WQLRw/++yzeXl5cNkkvArKmo03YT2fcDjc0tKiaRosHcvXFNyT5BkVlT1el82XtxLOrFiYmMBafX5+Pmx5IoSwiWzzriVWUgaSrM6yqffGxka2TCzHBHRXZWdoYg71/X7/a6+9Juw3eO2119h6WlwsZARxVU+s8KlTp7xeb21trcvlIoTs3buXsaKU/vGPf3zrrbd2794Ne+N0Xf/3f/93KMmvCCV2aJO1MoKF7xvbx9LZ2clun+FwuLe3d9u2bWfOnAHj1NQUPBfzC6p8O2lKW8/nzJkzmqa1tLTAipZ87aj0VGVPExmLJ51mxQI7lBoaGn75y1/CEmskEnnvvfc0TePHgumjERNIsjoLEyXCRiU5JsArld1YZwOBwC9/+UsYyEC4a5rGIMbLy/oLhlIKoQ+HzsnJOXjwIK+zMV2AFaEdO3ZYs3CcESzMcXa3FnYHsgJ8Ynx8vKSkRF6M5cukPG0xHxjaFxUV3bt3D3yRrx2VnqrsKWfCGrQMjhksrFd8Qtf13bt3qyYt+ZIpSctAktLZy5cvu578CVtZ5ZiA3qvsBjrLhjlvvvkmrKuOj4+/+eabwqYx83RkBObrJlkyEol89tln1dXVhBA2TlG1CdO4/LYEVcmU2DOIhVIa825t4NeePXsIIfw0rkHhlPxkJR+26a2np4d1Xr52VHqqsrOmUp6wBo5JLCrvYNmQ7YlSFUuJXQaSuM7CbkeXyzU0NCR0TrUOdvz4cdUtRQ4jaBO2oG7evJntsqSUBgKBzZs381vEhA4YZGUEBoXT8RMMx2bdeSNvS0hHZ1ibGcQyNDTkcrn4XcasV6rEfJUS8Pf8+fPZ2dkul6uhocH7zd+mTZsIIc8995zX6z1w4EAwGIRLRl4Ha2trI4TMv3Uwk1hUMQOixPZEqYqlxC5fTQnq7OjoaHFxsaZpfX19cs/kJXUok8C+rphbUNmTuLwIIHdGsMgIhALpzuq6bmbnjbzNNq0dyxQW1SORsbMgJfN1PAt3ETgjMf8FsYABmjCzZDK6jPHG+6s1wWMSi6rzsAlhLo1n4Y1JTdN6e3vZkgXvHrynIOxvhUGoalpNNZ6FITD/9hQcKOEnR2tiAjoJWwuEkcXjx4/Ly8tzcnKuXLlCKYXXNwRWlFLYKidcRTzk1KatxMJ6DnfrmI9EUAakpLGxkQ8z2D2tCiTWeGoTGeHDuyBfIHAnFnY3wtOSsA+Sbycd6QzCkbHAmxoCFjYyi7mpNOVMZCBxj2fZziTjTRJw82loaGDP+zAHp1omlnmB8zdv3nS73cKlCE+awvtmJmHJCExWTKDYiRMnCCH8E7Gu6729vcL7x0ePHiWEvP7662y7MQiQ8AZqAh0wX8VKLNAreF1b9UgEZWDdw+l0fvjhh2BhK2aqQDLvclwlrecjdE++QMLhMNzI2ZZHBscO790CHxkL2wna3t7OVpvhsWku7Td4++234dsf/OTRN5NIXvYWCvtKyNKlS9l3ZLKyslQv9si8gKOu6wcPHoQoLy0t3bVrF3yShhBy8OBBfpgjxKUqa+UFwxbxNE2rrKz0eDyw2V7Y/un3+7ds2cI+zcPeVpjH+2eDweD27dsJIfn5+c3NzSx+WOL69etwBvv7++HdDeFTKcLSq+p0p8puZdjE7HPMC4Tdj+ETS/AdGbfbPTw8HLORNBkzCEeFpbS0lH2iqLKyEnYrGQ8NUwhHBhL3eJbtG4e2hH/5CdOpqam9e/fCPlBN06oNX+OJyQs813X90qVLr776Kvv426uvvnrp0qUERDbm1rYU8pWbikQiH3zwwazfRYQvBIIKE0IKCwuPHDnC7sZysym3yJGR8kPwDbIvKgnxw7L8/fjWrVvC2f/888/51ixIW8xH9kh1gaTwk6HyQU1aMghHhQXEZ9myZdC30tLSvr6+xBTDJAS+mAwkbp3lm5uLaRnBXPQi5X1GLMZIkY8BH4QjwJGBoM4KiGyalSPDpiAUbiMfBZioGeEIcGQgqLMCIptm5ciwKQiF28hHASZqRjgCHBkI6qyAyKZZOTJsCkLhNvJRgImaEY4ARwaCOisgsmlWjgybglC4jXwUYKJmhCPAkYGgzgqIbJqVI8OmIBRuIx8FmKgZ4QhwZCCoswIim2blyLApCIXbyEcBJmpGOAIcGQjqrIDIplk5MmwKQuE28lGAiZoRjgBHBoI6KyCyaVaODJuCULiNfBRgomaEI8CRgaDOCohsmpUjw6YgFG4jHwWYqBnhCHBkIKizAiKbZuXIsCkIhdvIRwEmakY4AhwZCOqsgMimWTkybApC4TbyUYCJmhGOAEcGgjorILJpVo4Mm4JQuI18FGCiZoQjwJGBoM4KiGyalSPDpiAUbiMfBZioGeEIcGQgqLMCIptm5ciwKQiF28hHASZqRjgCHBlIDJ2FQvgvEkACSAAJJEyAF1/U2YQxYkUkgASQgJLALDrL/zz/0kBl/vmVpEeIxRgg8jHgg3AEODKQGONZoc48y8oI5pmDibmDWIy5IR8DPghHgCMDQZ0VENk0K0eGTUEo3EY+CjBRM8IR4MhAUGcFRDbNypFhUxAKt5GPAkzUjHAEODIQ1FkBkU2zcmTYFITCbeSjABM1IxwBjgwEdVZAZNOsHBk2BaFwG/kowETNCEeAIwNBnRUQ2TQrR4ZNQSjcRj4KMFEzwhHgyEBQZwVENs3KkWFTEAq3kY8CTNSMcAQ4MhDUWQGRTbNyZNgUhMJt5KMAEzUjHAGODAR1VkBk06wcGTYFoXAb+SjARM0IR4AjA0GdFRDZNCtHhk1BKNxGPgowUTPCEeDIQFBnBUQ2zcqRYVMQCreRjwJM1IxwBDgyENRZAZFNs3Jk2BSEwm3kowATNSMcAY4MBHVWQGTTrBwZNgWhcBv5KMBEzQhHgCMDQZ0VENk0K0eGTUEo3EY+CjBRM8IR4MhAUGcFRDbNypFhUxAKt5GPAkzUjHAEODKQRHQ2EomcPHly1apV0NyyZcv27t07NTUlHGxqaqq1tXXhwoWEEE3TNm7ceOvWLaEMZCORyNDQUHV1tdfrjVng1q1b1dXVmqYRQlasWHH48OFAIBCz5KxGGcGsVVJYoKenhxBSUFAwNjbGN3vr1q1XX301KyuLEJKVlfXqq6+qWPG1UpjOLBZK6cOHD9etW0cI6erqUvkVCoV27txJCPF4PKoyabJnlo9Jx1XRlSYmrNlMwTHA4vP56urq2AVVV1fn8/lYh9OdkIHErbOhUMjr9YIcbNu2rba2NicnhxBSVVXl9/uZAz6fb82aNSCLHo+nrKwM1HZgYICVgauru7t76dKl0LOY18/ly5ddLhchZO3atexwXq83FArxTZlMywhMVky+2MjIyPLly2Wd7evrg1tIaWmpx+OBG5jL5bp8+XLyBzXZQgaxUEp1XW9vb4c+GOgs6IgNddaM46roMhkAyRTLVPCosAwPD7vdbkLIqlWr+Avq2rVrybhpvq4MJG6dBd8qKysnJyfhwJOTk5WVlYSQjo4O1pWOjg5CCK+GoCbr169ncnzq1CnoUE5ODoxlZJ2FYY6mab29vbquU0rZ4Xp6etjhzCdkBObrJlMyFAo1NjbC0fnx7N27d/Py8lwu19DQELSv6/r777/vcDg2b96c8LA93q5mCgv089KlS06nE/qg0tl79+4VFRVBGTlO4vU33vIZ5GPGcVV0xetmYuUzAkeFJRwO19XVEUK6u7tBMXRd7+7uJoTU1dWFw+HEfIyrlgwkPp0NBoPV1dULFiy4cOECf+DTp087HI7q6upgMEgpffToUVlZmcvlunHjBisGdR0Ox9mzZ8H4u9/9bsuWLR9++GEoFBocHIw5Tjl79qzD4RAAnT9/Pjs7OzEZkhGwHqY10dvb63A4Ghoa8vLyeJ2Fm019fT3EBPQBAC5ZsuT27dtp7RVrPFNYKKV+v3/Dhg3Lly+H+1BMnQUdyc7Obm1tjRknzJE0JTLFx6TjquhKEw2hWevhGGC5c+dObm5uSUnJ+Pg46+eDBw9Wr15t2QUlA4lPZ8fGxgqe/AnTi1evXnU6nRUVFdPT05TS27dvL1mypLy8/PHjx8xVSun+/fsJIYcOHeKNkFbpbFdXFyHk2LFjfBWQodzc3Dt37vB2M2kZgZlaSZYZHR0tLi5et27djRs3Cp78CQCF9qenpysqKpxO59WrV4Wf0pTNCBaYMeju7nY4HD09PXCuY+osPAy1t7cPDAzYSmfNOB5vdKU8hKwPHgMsMDITBi66rtfX1xNCTp06lXL35QZlIPHprNwiWCD69+zZA1mQ3aqqqpmZGb5Kf38/IaStrY03Qlqlsz/84Q8JIYODg3yVcDi8Y8eOxGRIRsC3nI50OBxuaWnRNO3MmTOqG5VwXLj9JnYjEZoymbUeC3Ts2rVrLperpqYmEAiodNbn85WUlJSWlk5MTKjixKSbCRfLCB8zjicQXQlDUFW0GI4xFoiQpqYmobcwyDt+/LhgT0dWBpICnQ0EAjU1NdnZ2RcvXoROqy4GlZ1SqvoJrj15COzxeGT9NYNMRmCmVjJlBgYGNE1raWkJh8NmdDYSiXR2dhJCWltb+cmEZPowa13rsVBKp6enN23a5HK5YIEips4yHYEVVFWczOpgkgWs52PS8XijK0kOMatbCWdWLDGjiFKqssf0KEmjDCRZnWVzzI2NjWwDgOpiUNkNdBZGyjt27OAnsGFxjJ/qNc9FRmC+bgIlJyYmSktLS0pKYFuJgc5ev37d6/V6PJ4VK1YQQl5//XW2YJjAceOtYjEW6N7Ro0cdDsfBgwchG/NKOHPmDLtLGcRJvP7GW956PmYcNx9d8fobV3kr4cyKJWYUzXmdhYmS4uLi0dFRdm5UeqqyG1w/4+PjJSUl/H6DQCDQ1NRECHn65w10XW9tbXU4HL29vQDHQGcBDoSspmmtra3CrAvDm46ElZcK9P/mzZtut3vDhg3sdiJfIaAjRUVF9+7dg1oGIZQOLKxNi/mYcTyu6GKOpCNhGRwzWOQoApdVdmuAJDWehZ2t8k5P1cWgshvoLKUUpBz2z27bti0rK+uZZ55xOp2LFi0aHh6OF5NlMUEphX0RO3fuZCN9A50FR3Rdv3fvHuxQ3rRpE6wrxutjAuWtxEIphR3m2dnZ58+fZ70VrgS2qZbfwGcQQqyddCSs5GPS8QSiKx1kLHsfzCQWIYqYyyo7K5DChBwtievsyMhIcXExv/GTdVS1Dnb8+PF418FgSfrcuXOlpaXwpkNtbe2nn35aVla2evXqBw8esIOaTMgITFaMtxib3KipqfF+81dfX5/95K++vv6tt95iwzSh8UAgsHnz5sQmRoSmTGYtwwL9gV3Y+fn5zc3N37Dxvvjii4SQF1980ev1vvfee6AjLperoaGBldm0aRMh5LnnnvN6vQcOHIB9hCZ9TKaYlXzMOP4///M/69atczgcCURXMhxi1rUGjhkswWAQ7sTyOlhbWxshZI6tg8FWEk3T+vr6ZPSwr6usrOzRo0f8rwns6+Krs/TFixezs7MbGxsTWCayJiYopXCzgcPF/Nd43sOAFeOQwoRlWKDPsIwZEwsYPR4PDEAMyrB9hCnkoGrKSj5mHD9//jx7syMmIuPoUrmZmN0aOGawTE9Pw74uYUVnTu7r8vv9VVVV/JypcHpgf6twpmGMJr/jAHXNPw/CIydskxKOayZrTUyoeiLPGxw6dEge47Ow6O/vVzWVWntmsYAvZp7szMfJPONjxnE5ulILQdVaBoNHxgLvKRQWFt6/f591GJZ5LNsoKQOJe96ALUOx19qYM3wCrpmGhgb25ihMs/Lv3fLlZV78ryzt8/m2bt0qvNHLfjWTkBGYqZWqMvKVAGNzYfplaGjI5XLl5eXdvXs3VYc2biezWKBvqLMG58jMBSJHl0GDKfwpg8EjYwmHw7BOvm/fvkgkwt6FmUvv3VJK3377bUKIMGvGps/Y6xYw5iWELF26lH1HJisrS3jjgJ1smRf7iVJ6/fr19evXs8/NJLPnKYMxQSmVrwS2MY4QUlZWtmvXLjYTHXNOhseSwnRmsYAjqLMGJ9T4AoGKcnQZNJjCnzIYPDGxwKwmIWTlypXsOzJutzuBZfPEKMlA4h7PGs+s8S9NTk1N7d27Fz61pWladXW1wbf+YvJiTsKvzz777Ouvv37p0qUEpmVZUzIC9pMFiZhXgq7rly5dsvl3EU3ucDSOk/SdwcyGjfGGHOZ1zOhiv6YvkUE4qnjw+XzNzc3su4jNzc1z7LuI6Ttb1rScwZiwxsHEjoJYjLkhHwM+CEeAIwOJezwrtDjnsjKCOedCOjqMWIypIh8DPghHgCMDQZ0VENk0K0eGTUEo3EY+CjBRM8IR4MhAUGcFRDbNypFhUxAKt5GPAkzUjHAEODIQ1FkBkU2zcmTYFITCbeSjABM1IxwBjgwEdVZAZNOsHBk2BaFwG/kowETNCEeAIwNBnRUQ2TQrR4ZNQSjcRj4KMFEzwhHgyEBQZwVENs3KkWFTEAq3kY8CTNSMcAQ4MhDUWQGRTbNyZNgUhMJt5KMAEzUjHAGODAR1VkBk06wcGTYFoXAb+SjARM0IR4AjA0GdFRDZNCtHhk1BKNxGPgowUTPCEeDIQFBnBUQ2zcqRYVMQCreRjwJM1IxwBDgyENRZAZFNs3Jk2BSEwm3kowATNSMcAY4MBHVWQGTTrBwZNgWhcBv5KMBEzQhHgCMDQZ0VENk0K0eGTUEo3EY+CjBRM8IR4MhAYugsFMJ/kQASQAJIIGECvPiiziaMESsiASSABJQEZtFZ/uf5lwYq88+vJD1CLMYAkY8BH4QjwJGBxBjPCnXmWVZGMM8cTMwdxGLMDfkY8EE4AhwZCOqsgMimWTkybApC4TbyUYCJmhGOAEcGgjorILJpVo4Mm4JQuI18FGCiZoQjwJGBoM4KiGyalSPDpiAUbiMfBZioGeEIcGQgqLMCIptm5ciwKQiF28hHASZqRjgCHBkI6qyAyKZZOTJsCkLhNvJRgImaEY4ARwaCOisgsmlWjgybglC4jXwUYKJmhCPAkYGgzgqIbJqVI8OmIBRuIx8FmKgZ4QhwZCCoswIim2blyLApCIXbyEcBJmpGOAIcGQjqrIDIplk5MmwKQuE28lGAiZoRjgBHBoI6KyCyaVaODJuCULiNfBRgomaEI8CRgaDOCohsmpUjw6YgFG4jHwWYqBnhCHBkIKizAiKbZuXIsCkIhdvIRwEmakY4AhwZCOqsgMimWTkybApC4TbyUYCJmhGOAEcGgjorILJpVo4Mm4JQuI18FGCiZoQjwJGBJKKzkUjk5MmTq1atguaWLVu2d+/eqakp4WBTU1Otra0LFy4khGiatnHjxlu3bgllAoHA4cOHV6xYAU0VFhYePnw4EAgIxXw+X11dXVZWFiEkKyurrq7O5/MJZUxmZQQmK6akWE9PDyGkoKBgbGyMb3Bqamrv3r0ulwtYVVdXy6z48ilPZwQLeL1s2TI4emlp6cmTJyORCO+dEGwvvfTSxx9/rOs6X8aCtPV84NIoLCyEQ6suDfD9D3/4Q2Nj47PPPksI6erqsgAIfwgr4ZjEkkLF4D01mZaBxK2zoVDI6/WC3m3btq22tjYnJ4cQUlVV5ff7WT98Pt+aNWsIIStWrPB4PGVlZaAgAwMDrIzf76+qqiKEuFyu2trarVu3appGCPF6vaFQiBUbHh52u92EkFWrVnk8HtB3l8t17do1VsZ8QkZgvm6SJUdGRpYvXy7rLGO1dOlSj8ezdu1aYNXX15fkEc1Xtx7L6OhocXExIWTZsmW1tbWVlZVw9ru7u5mM6rre3d0NNLZu3VpRUWE9GWBoMR92aSxcuHDbkz8YZAiXBqU0FArt27cPurdw4cLvfve7v/3tb82f95SUtAyOSSypVYwEEMlA4tZZGJFVVlZOTk5CDyYnJysrKwkhHR0drE8dHR2CYvb19Wmatn79epBjXdfb29sJIU1NTWwAOzIyAtfeiRMnoKlwOFxXV0cIYZcfu/bq6urC4TA7osmEjMBkxSSLhUKhxsZGOLowngVWDQ0NwEHX9ffff9/hcKxZs+bhw4dJHtdkdYuxsNPa2dnJBrD/9V//VVRU5HK5bty4Ad0+f/58dnZ2cXHxyMgIWIaGhlwu1/Lly5nFpINJFrOYD4QEf2lMTk6Wl5c7HI7Tp08zX9i1UFJSMjQ0xO5PrIA1CcvgmMHCQitVipEAQxlIfDobDAarq6sXLFhw4cIF/vCnT592OBzV1dXBYJBS+ujRo7KyMv6CoZRCXYfDcfbsWUrpgwcPVq9enZube+fOHb6pI0eOEEL27NkDxjt37uTm5paUlIyPj7NiUHfJkiW3b99mRpMJGYHJikkW6+3tdTgcDQ0NeXl5vM5OT09XVFQISMGYk5Nz5cqVJI9rsrrFWMbHx4uKivLy8oT5n7a2NkLIsWPHoNt79uz5+jmG3XQppez2fOTIEZOupaSYlXxmZmaqqqrks3/8+HH+0qCU3rx50+12FxcXj46OpsTNxBqxBo5JLClXjASYyEDi09mxsbGCJ3/C9OLVq1edTmdFRcX09DSl9Pbt20uWLCkvL3/8+DHfy/379xNCDh06RCkVqrBig4ODhBCPxwOWs2fPOhyO+vp6/l6t63p9fT0h5NSpU6yiyYSMwGTFZIrBM/K6detu3LhR8OSPAVQh9Xg8hJDBwcFkjmu+bkawyN3r6upiM4xws5HvphcuXFiwYMGOHTsSeJqRj2jS8jTwES6Nr3sO47uenh6TXqSpWGbhCFhSrhgJQJOBxKezqkMODAzwd1rQ0KqqqpmZGb5Kf38/IaStrY03Cmm40tiIBiA2NTUJxUCyjx8/LthnzcoIZq2SZIFwONzS0qJp2pkzZ2RVhbt0zPGs0+m8evVqkkc3Wd16LDE7BgNYOK0TExPFxcX82B+qwICF3dRjtpNy49PA59ixY/zl8/jx4/Ly8tzc3IGBgY0bN8Lsdmlp6blz5/hBScpRyA1mFo6AJeWKIfs7q0UGkgKdDQQCNTU12dnZFy9ehB4IdxjWLZWdFZiYmCgtLc3Ly7t79y4Y+QEOK0YpVdn5MjHTMoKYxVJoHBgY0DStpaUlHA7LOkspPXr0KCGEn5/t7e3VNK2mpobNXKewPzGbsh6L3A2Yey0tLZ2YmKCUwnh20aJFw8PDfOGYDPkC6UhnnA8sXbjd7ps3b4KDPp8vLy/P8eQvKytr27ZtsNr89eLzu+++mw4IqjYzCEfGolIGlV3lVDJ2GUiyOstm4hsbG9kmAZWequzgEtvJ0NnZyW7IKjoq+6x0ZASzVkmmANw5SkpKYCIypkaEQqGf/OQnhBB+v0FZWZkwd5lMN2atazEW1p+vvvrqnXfe8Xq9iSz+4wAAIABJREFUoBErV668fv06/KrrOqwcCk8tsDi2evXqBw8esHbSncgIn2AweODAAa/XC5sxFi9ezE+UwVOjsJIMq838SCXdZKzfP2uMRaUMKns6+MjRkqzOwnkVZuJVeqqyw/oG7OAR9oep6Kjss1KTEcxaJeECuq63trY6HI7e3l5oJKbORiKR3/zmN06nE/oG/7755pvyluSEezJrRSux8J0BIMzxl1566fPPP2cFYH2V32/AdqTYYd4ARvQMTklJyUcffcTggM6+8MIL/KYUttou3JxYrXQkLA4eYywqZVDZrQGSlM5evnzZ9eTv8uXLfHdVeqqyU0pj6rXB/EDC1KyMCRh57dy5k430ZZ1lDwRbt24dGRnRdd3v93d2dmqaJtxyeMIpT1uJJWbn/X7/u+++63Q6+Z3R7BFH07StW7fCmHfJkiUOh0Oe/Y/ZbKqMmeUTDAb7+/vz8/M1TWM70FUryfK2hFRBULWTKTgxsaiUQWVXOZWMXQaSuM7CyMLlcg0NDQl9Uq2DQQTI62AwMccPW1iDIM3yOhhsAErgpi0jYMdKbeLhw4fr1q1zOBw1NTXeb/7q6+uzn/zV19e/9dZb9+7du3v3bl5enrBxTdf13bt38zucUts3uTXLsMiH5i3Crj5KaSAQOHDgALwp53K5Dhw48OGHHy5YsIDt/OOrpy/9NPARdk+qdNZgNJMmPpmFI2BJuWIkAE0GkqDOwkYlTdNivrME+7rKysoePXrE95Lf18XsqkExFIBdGsImnjmxrwsuAyAe81/YTnDq1ClCiLBxjVJq8dUiRwY7QVYmrly5kpOTYzxWhc1MbFhnTfeeBj6w8FVUVAR7yVVXmWo0kz5QmYUjYEm5YiTATQaSiM7C22+apvX29rIFK7438J6CsC0pEAhs3rxZ2MAEeh1zUAwNwiaewsLC+/fvs0OMj4+XlJTI7ziwAgYJGYFB4ZT/JM8bwG7QzZs3C1sLYLeKZaM2i7EMDw8vWrRIvhPDzj/5rsNOBLy7zPYkMHu6E1bygZc45PCGmxDblg4XVHZ29vnz55n7bPHQyvuQNXBMYkm5YjC25hMykLh1NhAINDU18S/Cxjw8zIawvUpsBpa9d0sphRd2VYNiaDYcDsPh9u3bBy9osgnNufXeLbgj6yxsSBBuWjAnI7xkGZNzqoxyZKSq5Zjt+P3+9evXE0L4927ZC9z8C2Csuq7r586dgznKmE9RrGQ6ElbyYWtZ/Hu37Lrj327v7+93OBwbNmz48ssvwWtY57D4PmQNHJNYUq4YCYSTDCRunX377bfhyy8NDQ3fzDr+33/ZvhP2xQfYqwQrGFlZWewFp2AwuH37dkJIfn5+c3Pz/zXxTYrt72EfHFm5ciX7jozb7Ra2VZrEISMwWTElxWSdpZT29/fDV0IKCws9Hg/7okpLSwtbQEvJ0Q0asR4LzBexLw1VVVWpPpXyj//4j6tWrYIPv2VlZf3jP/5jzKcoA++S/8liPizm4RNL27ZtA/eFpdFQKNTS0sI+6sSusv7+/uRdNt+CZXBMYmHFUqIY5jmwkjKQuHUW3geFhuR/+W+yGX/rT9icITfFFJlS6vP5mpub2XcRm5ubE95bKiNgdCxIxNRZSil81459Q/Lll1/+4IMP2AdWLOhYRrCMjY29/fbbxt9FpJRCyK1cufLnP/85e1/ZAib8IaznMzU11d3dPet3ESORyPvvvw9fFtU0rdryL2pavH/WJJYUKgYfBibTcrTErbMmj/TUFpMRPLVdtbJjiMWYNvIx4INwBDgyENRZAZFNs3Jk2BSEwm3kowATNSMcAY4MBHVWQGTTrBwZNgWhcBv5KMBEzQhHgCMDQZ0VENk0K0eGTUEo3EY+CjBRM8IR4MhAUGcFRDbNypFhUxAKt5GPAkzUjHAEODIQ1FkBkU2zcmTYFITCbeSjABM1IxwBjgwEdVZAZNOsHBk2BaFwG/kowETNCEeAIwNBnRUQ2TQrR4ZNQSjcRj4KMFEzwhHgyEBQZwVENs3KkWFTEAq3kY8CTNSMcAQ4MhDUWQGRTbNyZNgUhMJt5KMAEzUjHAGODAR1VkBk06wcGTYFoXAb+SjARM0IR4AjA0GdFRDZNCtHhk1BKNxGPgowUTPCEeDIQFBnBUQ2zcqRYVMQCreRjwJM1IxwBDgyENRZAZFNs3Jk2BSEwm3kowATNSMcAY4MBHVWQGTTrBwZNgWhcBv5KMBEzQhHgCMDiaGzUAj/RQJIAAkggYQJ8OKLOpswRqyIBJAAElASmEVn+Z/nXxqozD+/kvQIsRgDRD4GfBCOAEcGEmM8K9SZZ1kZwTxzMDF3EIsxN+RjwAfhCHBkIKizAiKbZuXIsCkIhdvIRwEmakY4AhwZCOqsgMimWTkybApC4TbyUYCJmhGOAEcGgjorILJpVo4Mm4JQuI18FGCiZoQjwJGBoM4KiGyalSPDpiAUbiMfBZioGeEIcGQgqLMCIptm5ciwKQiF28hHASZqRjgCHBkI6qyAyKZZOTJsCkLhNvJRgImaEY4ARwaCOisgsmlWjgybglC4jXwUYKJmhCPAkYGgzgqIbJqVI8OmIBRuIx8FmKgZ4QhwZCCoswIim2blyLApCIXbyEcBJmpGOAIcGQjqrIDIplk5MmwKQuE28lGAiZoRjgBHBoI6KyCyaVaODJuCULiNfBRgomaEI8CRgaDOCohsmpUjw6YgFG4jHwWYqBnhCHBkIKizAiKbZuXIsCkIhdvIRwEmakY4AhwZCOqsgMimWTkybApC4TbyUYCJmhGOAEcGkojORiKRkydPrlq1CppbtmzZ3r17p6amhINNTU21trYuXLiQEKJp2saNG2/duiWUCQQChw8fXrFiBTRVWFh4+PDhQCAgFIPsw4cPu7u7v/Od71y9ejVmATNGGYGZWgmX0XX9448/fvnllzVNI4QsXLiwubnZ5/MJDfp8vubm5qysLGD18ssvf/zxx7quC8XSl7UYi+zIw4cP161bRwjp6urif9V1/dy5c2vXrmXBduDAAVWE8BVTm7aeD1wahYWFcGjVpeHxeKCA8O/g4GBqCRi0Zj0c6EwoFNq5cychxOPxCN3jL6isrKyYF51QJYVZGUjcOhsKhbxeLyEkKytr27ZttbW1OTk5hJCqqiq/38/66vP51qxZQwhZsWKFx+MpKysDBRkYGGBl/H5/VVUVIcTlctXW1m7duhXEyOv1hkIhViwSiQwNDW3ZsgV673Q654rO6rre3d0Njn/ve9/zeDxwRykuLh4dHWUOXr582eVyEUJWrVq1a9cu0BRN0/r6+liZdCfkyEj3Efn2dV1vb2+HPgg6+/d///cAsLKysra2FkBt2rRpenqabyHdaYv5sEtj4cKF2578wT1YuDSmp6crKioIIcuXL8//07+zZ8+mmwlr32I47Lg9PT1waEFnh4eH3W43IWTlypUej2flypUgMpcvX2Z105qQgcSts+BbZWXl5OQk9HVycrKyspIQ0tHRwXrf0dFBCOHDoq+vT9O09evXgxyzS6upqYkNT0ZGRoqLi79WnBMnTkBTMzMzoMWEkG9/+9tut3sO6ezFixezs7OLi4tHRkbAnUgksm/fPkLI7t27YbgK4zhN03p7e8Gi63pvb6+maXl5eXfv3mVI05qQIyOthxMav3TpktPphD7wOnv37t28vDyXy8WuECZALEKEptKUtZgPXD78pTE5OVleXu5wOE6fPs18fPDgweonfw8ePGBG6xMWwwEH7927V1RUBIfmdTYcDtfV1RFC9u3bF4lEKKXsotu+fXswGLSAjwwkPp0NBoPV1dULFiy4cOEC393Tp087HI7q6mpw49GjR2VlZS6X68aNG6wY1HU4HHCnhRDJzc29c+cOK0MpPXLkCCFkz549YJyenq6trd2/f/8XX3wBd+85pLOHDh36euC/f/9+3sE7d+7k5uauXr0aro2zZ886HI4dO3aEw2FWLBAIbN68mbFi9vQl5MhI37GElv1+/4YNG5YvX97Y2CjMGxw/fpy/J0FFIMaCTWgtTVkr+cDYIicn58qVK7w7QINdGpTSsbGxgoKCiooKi0f3fK8yMj8bCoUaGxuzs7NbW1uFeQNgIgjL/fv3CwsL8/Ly5Ck7wZeUZOVoiU9nwYeCgoKxsTG+Q1evXnU6nex83759e8mSJeXl5Y8fP+aL7d+/nxBy6NAhSqlQhRUbHBwUwLGf5pzOsp7zCYFhV1eXIC5QGObdLJtlkyOD73P60jC14nA4enp6ZBR79uwhhPT39/Md8Pl8eXl5RUVF4+PjvD2t6Uzx4Z2SLw24iPjRHF/esrT1cODhuL29fWBgQJCLmMKSEukwz1MGEp/Oqo4E3rI7LbhaVVU1MzPDV+nv7yeEtLW18UYhDRfbsWPHBDulNCWwZATygdJqgZtQWVnZo0ePVAcCT5csWXL79m1VmdTaM4Xl2rVrLperpqYmEAjIOgs3m1OnTvHOPn78uLy8XBiw8AXSkc4UH96XY8eOCZfPhQsXFixYUFlZuXHjRljbWLVq1cmTJ+F5ma+b1rTFcHw+X0lJSWlp6cTEhHzvGR8fLyoqEsIDxrOFhYX3799PKwpoXAaSAp0NBAI1NTXZ2dkXL16Ew8jOG9uZ5xMTE6Wlpap5yXmgs4FAoKmpiRBy9OhR5rWcOHPmjKZpdXV1/GSCXCyFFjkyUti4qqnp6elNmza5XK5r165RSgWdVZ1ulV11lJTYM8KH7zksXbjd7ps3bzI7XGiwKJ2fnw97ewgh3d3d83WzSjgcbmlp0TQNVtRlqdF1HSYT+PnZzs5OQkhra6s1WORoSVZn2ZJ6Y2Mj2yQgOw+RobLDr2wnQ2dnZ0wcKbnAZAQsatOXOHXqlNfrZcvle/fuZazkg46OjhYXFzudzkuXLsm/psmSESxHjx51OBwHDx4Ep1BnhZMbDAYPHDjg9XorKys1TVu8eLEwtB8eHm5oaPjlL38JK8mRSOS9997TNI0f9AhtpiNrZfDAEKSlpQWGIDElxe/3v/baa8J+g9dee43fEJUODqxNGUiyOgsTJcJGpZjOU0pVdkop02thfxjr+pyeNwAFAfo5OTkHDx5U6SxbT5/HQxI4pzdv3nS73Rs2bGDRjzrLRzsLeAgbQkhJSclHH30klBGyuq7v3r1bmF4QyqQ8K8tKyg8BDcLzblFR0b1798ASU1ICgcAvf/lLmEiBvmmaxu5Gaeob36wMJCmdhY2f/LYbA+eNdTamXvNdZ2E3h/YbCP2PRCKfffZZdXU1IYTdkPkybETP74fjC6QvLUdG+o5FKYUd5tnZ2efPn2cHQp1lKIREMBjs7+/Pz89nz8tCAT5r/X4Ma4KH7QTt6elh/so6yy6iN998E1ZKx8fH33zzTWGbKWshHQkZSOI6CxNGLpdraGhI6KtqHQw2psjrYENDQy6Xi99nKjQI2bk7b8C7Mz4+XlJSIq9xRSIRmEXid03yFdOaliMjrYeDXdj5+fnNzc3eb/5efPFFQsiLL77o9Xrfe+89SmnMdTDYNSgsdKS1txnZuiR7JOyelAuAJeaCu6pwSuzWBM/58+ezs7NdLldDQ8M3IePdtGkTIeS5557zer0HDhwIBoOwaX3z5s1sVz6l1OKNkjKQBHUW5hBV7yypltT5fV3sBKsGxawAS8wPndV1vb6+nhDCz7WZmTZhHNKRkCMjHUdhbareFoVusJ06sK/r+PHjrCKl1Lb7ukw6DpsQrNxfbE3w8JNvLE74BOwrjblpXV5l5SMq5WkZSCI6C3OI/CtMQkdhxCE84MMtRXjHAfQ65qBYaHMuzhvA1gJBJmBbkrALHaZN+LfsZPfTapEjI62Hi9m4MG9AKYUHoMbGRn5dFIZ1wpsdMRtModFKPjF3JlFKr1y5kpOTw7alw5s+8l4lwCg/NaaQhtCUlXCEQ8vzBhAz7H1LVh7u2TE3jLIyqUrIQOLWWbYzyXihBk52Q0MDG72DlLD3biml8MKualAs+zznxrMnTpwghPDzIeydWp4DmzbhP3ogu59WixwZaT1czMZlnf3iiy+ef/55p9P5+9//HqqwdcJ5/N4te3OUn0Fi1x17u51tYGpvb2fLqvB0OI/3GwiRI+ssrK8KQze4xIQ3VIWmUpiVr6a4dfbtt9+GjzLwsyRsuoQ9C7PrYenSpew7MllZWewFp2AwuH37dkKIMEnHmrp+/brg+ZzTWTYlr2laZWWlx+OBzy/xK4d/+MMfli1bRgipqKhgvrPEO++889VXXwkc0pGVIyMdRzFuU9ZZSincnoEP2xj3yiuvWPymqcV84DmPfWJp27ZtsDdW2I0zOjpaWlrKisH2r/m9f1YIIVlndV0/ePAgnK/S0tJdu3bBR6wIIQcPHuQfjISmUpiVoyVunTWeWeM/AjI1NbV37174wJKmadXV1fx3EUE0oUMx/2WKzPyfczoL37D44IMPDL6LCKsWMQkQQuRXnBmN1CbkyEht+2Zai6mz8GHJl156CXq4YsUKgy9nmjlKYmWs5zM1NdXd3T3rdxHhKoNbNSGktLS0r6/PGjVhJK2Hww4t6yxsEr106dKrr74KHznLysp69dVXL126ZBkWGUjcOss8nKMJGcEcdSS13UYsxjyRjwEfhCPAkYGgzgqIbJqVI8OmIBRuIx8FmKgZ4QhwZCCoswIim2blyLApCIXbyEcBJmpGOAIcGQjqrIDIplk5MmwKQuE28lGAiZoRjgBHBoI6KyCyaVaODJuCULiNfBRgomaEI8CRgaDOCohsmpUjw6YgFG4jHwWYqBnhCHBkIKizAiKbZuXIsCkIhdvIRwEmakY4AhwZCOqsgMimWTkybApC4TbyUYCJmhGOAEcGgjorILJpVo4Mm4JQuI18FGCiZoQjwJGBoM4KiGyalSPDpiAUbiMfBZioGeEIcGQgqLMCIptm5ciwKQiF28hHASZqRjgCHBkI6qyAyKZZOTJsCkLhNvJRgImaEY4ARwaCOisgsmlWjgybglC4jXwUYKJmhCPAkYGgzgqIbJqVI8OmIBRuIx8FmKgZ4QhwZCCoswIim2blyLApCIXbyEcBJmpGOAIcGUgMnYVC+C8SQAJIAAkkTIAXX9TZhDFiRSSABJCAksAsOsv/PP/SQGX++ZWkR4jFGCDyMeCDcAQ4MpAY41mhzjzLygjmmYOJuYNYjLkhHwM+CEeAIwNBnRUQ2TQrR4ZNQSjcRj4KMFEzwhHgyEBQZwVENs3KkWFTEAq3kY8CTNSMcAQ4MhDUWQGRTbNyZNgUhMJt5KMAEzUjHAGODAR1VkBk06wcGTYFoXAb+SjARM0IR4AjA0GdFRDZNCtHhk1BKNxGPgowUTPCEeDIQFBnBUQ2zcqRYVMQCreRjwJM1IxwBDgyENRZAZFNs3Jk2BSEwm3kowATNSMcAY4MBHVWQGTTrBwZNgWhcBv5KMBEzQhHgCMDQZ0VENk0K0eGTUEo3EY+CjBRM8IR4MhAUGcFRDbNypFhUxAKt5GPAkzUjHAEODIQ1FkBkU2zcmTYFITCbeSjABM1IxwBjgwEdVZAZNOsHBk2BaFwG/kowETNCEeAIwNBnRUQ2TQrR4ZNQSjcRj4KMFEzwhHgyEBQZwVENs3KkWFTEAq3kY8CTNSMcAQ4MpBEdDYSiZw8eXLVqlXQ3LJly/bu3Ts1NSUcbGpqqrW1deHChYQQTdM2btx469YtoUwgEDh8+PCKFSugqcLCwsOHDwcCAaHYrVu3qqurNU0jhGRlZb366qtyU0IVVVZGoCqZDntPTw8hpKCgYGxsjG/f5/M1NzdnZWUBq5dffvnjjz/WdZ0vk9Z0ZrFQSh8+fLhu3TpCSFdXl8rTUCi0c+dOQojH41GVSZPdej5waRQWFhpfGj6fr66uDiInKyurrq7O5/OlCYKqWevhQE8M4iGFiqHy2sAuA4lbZ0OhkNfrBb3btm1bbW1tTk4OIaSqqsrv97Nj+3y+NWvWEEJWrFjh8XjKyspAQQYGBlgZv99fVVVFCHG5XLW1tVu3bgUl9Xq9oVCIFevr6wN7WVnZrl27Vq5cCVUuX77MyphPyAjM102y5MjIyPLly2WdvXz5ssvlIoSsWrVq165da9euBVZ9fX1JHtF89QxioZTqut7e3g59MNBZuEvZQWfZpbFw4cJtT/5ASYVLY3h42O12Q+R4PB4Y+rhcrmvXrpk/9cmXzFTwqOIhtYqRAB8ZSNw6C75VVlZOTk5CDyYnJysrKwkhHR0drE8dHR2EED4swPn169eDHLNLq6mpiQ1gR0ZGiouLv46bEydOQFOgTS6Xa2hoCCyRSKSzs5MQwppiBzWTkBGYqZV8mVAo1NjYCEfnx7MwjtM0rbe3Fwawuq739vZqmpaXl3f37t3kD22mhUxhgb5dunTJ6XRCH1Q6e+/evaKiIigz78ezcPnwl8bk5GR5ebnD4Th9+jRAC4fDdXV1hJDu7m4WOd3d3YSQurq6cDhs5rynpExGgkcVDylXjAQQyUDi09lgMFhdXb1gwYILFy7whz99+rTD4aiurg4Gg5TSR48elZWVuVyuGzdusGJQ1+FwnD17llL64MGD1atX5+bm3rlzh5WhlB45coQQsmfPHjAeO3aMENLW1saXGR8fLykpWbJkye3bt3m7mbSMwEyt5Mv09vY6HI6Ghoa8vDxeZ8+ePetwOHbs2MFfGIFAYPPmzYxV8keftYVMYaGU+v3+DRs2LF++HO5DMXUW7lLZ2dmtra3zfjw7MzNTVVWVk5Nz5coV/sQdP36cvzTu3LmTm5tbUlIyPj7OisFlldilwRqJN2F98BjEQ8oVI14aMSes49PZsbGxgid/wvTi1atXnU5nRUXF9PQ0pfT27dtLliwpLy9//Pgx38v9+/cTQg4dOkQpFaqwYoODg/yF5PF4CCGDg4OsAKV0enq6oqLC6XRevXqVt5tJWx8TlNLR0dHi4uJ169bduHGj4MkfA9jV1RVzUjKm42YcTKxMRrDAjEF3d7fD4ejp6VGhoJTCw1B7e/vAwAAfHok5m0CtTPHhuypcGnCHrq+v5+fxdV2vr68nhJw6dYqvm9a09XAM4iHmhZOMYiSATgYSn86qDgnRzwahoKFVVVUzMzN8lf7+fnlwyheglMLFduzYMcHOZ+/fv19YWLh69eoHDx7wdjNpGYGZWsmUCYfDLS0tmqadOXNGdaMS2oewsHJUYj0WcPnatWsul6umpiYQCKh01ufzlZSUlJaWTkxMCFojcEtfNlN8eI+EkRqgaGpq4stQSmE0c/z4ccGevqzFcBKIh2QUIwFuMpAU6GwgEKipqcnOzr548SL0SXUxqOzMk4mJidLS0lnnJY8ePSpMB7MWZk3ICGatkmSBgYEBTdNaWlrC4bBJnT1z5oymaVbOslmPBZ5LNm3axNZtYuosu0vBCuqsIZTkyVJVzwgfvjOwdOF2u2/evAn2mLjYSCXm9AvfYArTVsJJLB6SUYwEQMlAktVZXddh6r2xsZFtElBdDCo7eMJ2MnR2dvKPQoKfsDpfVFR079494SczWRmBmVoJl4E7R0lJCey2MaOzMMngdDovXbqU8HHjrWgxFuje0aNHHQ7HwYMHIRtTOOCWA3cpSqlxCMXrtfnyGeETDAYPHDjg9XorKys1TVu8eDE/GxAT17zX2QTiIUnFMB8krKQcLcnqLEyUFBcXj46OssOoLgaVnc3TyfvDWJuQAA3SNC3hPU8yAuEQKczqut7a2upwOHp7e6HZWXWWbehhi8gp7I9BU1ZigW7cvHnT7XZv2LCBbQeUhQPuUvw91SCEDLxL/ifr+bB1CDg0IaSkpOSjjz5ivsi44CeVnVVMecIyOAnEQ/KKkQAuGUhSOgs3CpfLJWxlVV0MKjtb6BD0WvAwJRokIxCOksLs+fPns7Ozd+7cyUb6xjrLRvT8frgU9segKSuxUEphh3l2dvb58+dZrwSBYDv/enp6WBmDEGJl0pGwmI/gQjAY7O/vz8/P1zSN7UAXcLEqKjsrkPKENXASiIeUKEYCuGQgiessTBjxO1tZh1TrYLAxRdikRSkdGhpyuVzFxcUjIyOsESERCASampoIIZ2dnZFIRPjVfFZGYL5uXCVhY6zD4aipqfF+81dfX5/95K++vv6tt97ipz7YvmB+12RcR0ymsGVYoJOwCzs/P7+5ufkbNt4XX3yREPLiiy96vd733nsP7lIul6uhoYGV2bRpEyHkueee83q9Bw4cgH2EyThusq7FfGL2Stg9CbcceR2sra2NEDL/1sHijYdUKUbMc2FslKMlQZ01Ho3Dvq6ysrJHjx7xHeL3dTG7alDMCsDwB15CS36gJyPgD5TCNNxs4HAx/+X3pbFpbuG1uhT2x7gpy7BAN2DzTUwsYPR4PDAuMyjD9hEau5aSXy3mE7PPPp8vLy+vqKgINszG3Hk9j/d1xRUPGXw0TMH+WTj9MBrnX2ESwgLeU+B1hFIKe++FdxxAr2MOilmbTINSMtDL7AWjmjeAaW7+LTvmvjWJzGIBH8088Nph3mB8fLyoqEh+hefKlSs5OTlsWzq8p1BYWHj//n0WJPAKj1yXFUhHIoPBEzMeUqsYCRCTgcQ9nmWjceOFGrhmGhoa2Du1ICX8y7Lwwq7xohZ7CTVVAz0ZQQIcE64SU2fZtAm/lpjwIRKrmFks0GfUWeDAXqjlBxbsumNvt4fDYZhJ27dvH8ykMX2xckdgzOFbYkGYQC1ZZ1OuGAn0Sr6a4tbZt99+Gz7jws+asekztu+EzUAvXbqUfUcmKyuLvdkVDAa3b99OCBEm6VhT169fp5T+53/+p6ZpwiwnK5PADJ2MIAGOCVeRdfYPf/jDsmXLCCEVFRXML5Z45513vvrqq4QPZ75iZrFAP1Fn2fmC5zz2iaVt27bBd++E0QYrtnLlSvYdGbfbPTw8zJqyIJHB4JF1NuWKkQBAGUjcOms8s8bvjp6amtq7dy8cx55/AAACLUlEQVR8iUrTtOrqav5jhvDKE3Qo5r+gyMAxZgHQJnjT1zwLGYH5usmXlHXWeBqX/xJC8kc3aCGzWKBjqLP8CZqamuru7jbzXUT2Rc2srKzm5mb7fBcx5n7qlCsGf1JMpuWrKW6dNXmkp7aYjOCp7aqVHUMsxrSRjwEfhCPAkYGgzgqIbJqVI8OmIBRuIx8FmKgZ4QhwZCCoswIim2blyLApCIXbyEcBJmpGOAIcGQjqrIDIplk5MmwKQuE28lGAiZoRjgBHBoI6KyCyaVaODJuCULiNfBRgomaEI8CRgaDOCohsmpUjw6YgFG4jHwWYqBnhCHBkIKizAiKbZuXIsCkIhdvIRwEmakY4AhwZCOqsgMimWTkybApC4TbyUYCJmhGOAEcGgjorILJpVo4Mm4JQuI18FGCiZoQjwJGBoM4KiGyalSPDpiAUbiMfBZioGeEIcGQgqLMCIptm5ciwKQiF28hHASZqRjgCHBkI6qyAyKZZOTJsCkLhNvJRgImaEY4ARwaCOisgsmn2/7db7ygAAyEUAO9/67CkMRqbxc5pwn4SwUEeqZOxFKJpm08Dc47hJJwKImcT0dJtnYylEE3bfBqYcwwn4VQQOZuIlm7rZCyFaNrm08CcYzgJp4L85Oz7kicBAgQIXAvE8JWz14w+JECAQCvQ5my8sCZAgACBEYHP/+xIRUUIECBAIArI2ahhTYAAgXkBOTtvqiIBAgSigJyNGtYECBCYF3gAGE8UXKqe6ygAAAAASUVORK5CYII=\" width=\"235\" height=\"179\"><br>पाँच वर्षों के दौरान संयंत्र 1 और संयंत्र 2 के औसत उत्पादन के बीच क्या अंतर है?</p>",
                    options_en: [
                        "<p>1600</p>",
                        "<p>3000</p>",
                        "<p>3840</p>",
                        "<p>1400</p>"
                    ],
                    options_hi: [
                        "<p>1600</p>",
                        "<p>3000</p>",
                        "<p>3840</p>",
                        "<p>1400</p>"
                    ],
                    solution_en: "<p>55.(a) Average production of plant 1 of 5 years <br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>35</mn><mo>+</mo><mn>43</mn><mo>+</mo><mn>48</mn><mo>+</mo><mn>32</mn><mo>+</mo><mn>42</mn></mrow><mn>5</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>200</mn><mn>5</mn></mfrac></math> = 40<br>Average production of plant 2 of 5 years <br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>25</mn><mo>+</mo><mn>44</mn><mo>+</mo><mn>39</mn><mo>+</mo><mn>40</mn><mo>+</mo><mn>44</mn></mrow><mn>5</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>192</mn><mn>5</mn></mfrac></math> = 38.4<br>Required difference = 40 - 38.4 <br>= 1.6 &times; 1000 = 1600</p>",
                    solution_hi: "<p>55.(a) 5 वर्षों में से संयंत्र 1 का औसत उत्पादन <br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>35</mn><mo>+</mo><mn>43</mn><mo>+</mo><mn>48</mn><mo>+</mo><mn>32</mn><mo>+</mo><mn>42</mn></mrow><mn>5</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>200</mn><mn>5</mn></mfrac></math> = 40<br>5 वर्षों में संयंत्र 2 का औसत उत्पादन <br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>25</mn><mo>+</mo><mn>44</mn><mo>+</mo><mn>39</mn><mo>+</mo><mn>40</mn><mo>+</mo><mn>44</mn></mrow><mn>5</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>192</mn><mn>5</mn></mfrac></math> = 38.4<br>आवश्यक अंतर = 40 - 38.4 <br>= 1.6 &times; 1000 = 1600</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "56",
                    section: "17",
                    question_en: "<p>56. During the first year the population of a village increased by 5% and during the second year it diminished by 5%. At the end of the second year its population was 47,880. What was the population at the beginning of the first year?</p>",
                    question_hi: "<p>56. पहले वर्ष के दौरान एक गाँव की जनसंख्या में 5% की वृद्धि हुई और दूसरे वर्ष के दौरान यह 5% कम हो गई। दूसरे वर्ष के अंत में इसकी जनसंख्या 47,880 थी। पहले वर्ष की शुरुआत में जनसंख्या कितनी थी?</p>",
                    options_en: [
                        "<p>43500</p>",
                        "<p>53000</p>",
                        "<p>48000</p>",
                        "<p>45000</p>"
                    ],
                    options_hi: [
                        "<p>43500</p>",
                        "<p>53000</p>",
                        "<p>48000</p>",
                        "<p>45000</p>"
                    ],
                    solution_en: "<p>56.(c)<br>Let Population at the beginning of the first year be x<br>According to the question,<br>= x&nbsp;&times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>21</mn><mn>20</mn></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>19</mn><mn>20</mn></mfrac></math> = 47880<br>= x&nbsp;= 47880 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>20</mn><mn>21</mn></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>20</mn><mn>19</mn></mfrac></math> = 48000</p>",
                    solution_hi: "<p>56.(c)<br>माना प्रथम वर्ष की शुरुआत में जनसंख्या x&nbsp;थी<br>प्रश्न के अनुसार,<br>= x&nbsp;&times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>21</mn><mn>20</mn></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>19</mn><mn>20</mn></mfrac></math> = 47880<br>= x&nbsp;= 47880 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>20</mn><mn>21</mn></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>20</mn><mn>19</mn></mfrac></math> = 48000</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "57",
                    section: "17",
                    question_en: "<p>57. Two pipes A and B can fill a tank in 1<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> hours and 2 hours, respectively. If both the pipes are opened simultaneously, then in how much will the empty tank be filled?</p>",
                    question_hi: "<p>57. दो पाइप A और B, किसी टंकी को क्रमशः 1<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> घंटे और 2 घंटे में भर सकते हैं। यदि दोनों पाइप एक साथ खोले जाते हैं, तो कितने समय में खाली टंकी भर जाएगी?</p>",
                    options_en: [
                        "<p>1<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> hours</p>",
                        "<p>55 minutes</p>",
                        "<p>48 minutes</p>",
                        "<p>1<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> hours</p>"
                    ],
                    options_hi: [
                        "<p>1<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> घंटा</p>",
                        "<p>55 मिनट</p>",
                        "<p>48 मिनट</p>",
                        "<p>1<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> घंटा</p>"
                    ],
                    solution_en: "<p>57.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741669109226.png\" alt=\"rId56\" width=\"175\" height=\"133\"><br>&there4; time to fill the empty tank = <math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>5</mn><mi>&#160;</mi></mrow></mfrac></math>&times; 60 minute = 48 minute</p>",
                    solution_hi: "<p>57.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741669109472.png\" alt=\"rId57\" width=\"172\" height=\"140\"><br>&there4; खाली टंकी को भरने का समय = <math display=\"inline\"><mfrac><mrow><mn>4</mn><mi>&#160;</mi></mrow><mrow><mn>5</mn><mi>&#160;</mi></mrow></mfrac></math>&times; 60 मिनट = 48 मिनट</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "58",
                    section: "17",
                    question_en: "<p>58. A policeman starts chasing a thief who is 500 m ahead of him. The speed of the thief is 18 km/h, and the speed of the policeman is 36 km/h. Find the time taken by the policeman to catch the thief.</p>",
                    question_hi: "<p>58. एक पुलिसकर्मी एक चोर का पीछा करता है जो उससे 500 m आगे है। चोर की चाल 18 km/h है, और पुलिसकर्मी की चाल 36 km/h है। पुलिसकर्मी द्वारा चोर को पकड़ने में लगने वाला समय ज्ञात कीजिए।</p>",
                    options_en: [
                        "<p>150 sec</p>",
                        "<p>250 sec</p>",
                        "<p>300 sec</p>",
                        "<p>100 sec</p>"
                    ],
                    options_hi: [
                        "<p>150 सेकंड</p>",
                        "<p>250 सेकंड</p>",
                        "<p>300 सेकंड</p>",
                        "<p>100 सेकंड</p>"
                    ],
                    solution_en: "<p>58.(d)<br>Relative speed = 36 - 18 = 18 km/h<br>&rArr; 18 km/h = 18 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>18</mn></mfrac></math> = 5 m/sec<br>Time taken to catch the thief = <math display=\"inline\"><mfrac><mrow><mn>500</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> = 100 sec</p>",
                    solution_hi: "<p>58.(d)<br>सापेक्ष गति = 36 - 18 = 18 km/h <br>&rArr; 18 km/h = 18 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>18</mn></mfrac></math> = 5m/sec<br>चोर को पकड़ने में लगा समय = <math display=\"inline\"><mi>&#160;</mi><mfrac><mrow><mn>500</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> = 100 सेकंड</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "59",
                    section: "17",
                    question_en: "<p>59. Simple interest and compound interest on a principal at a certain rate of interest for 2 years is Rs. 12800 and Rs. 14400 respectively. What is the principal ?</p>",
                    question_hi: "<p>59. 2 वर्षों के लिए एक निश्चित ब्याज दर पर, मूलधन पर साधारण ब्याज और चक्रवृद्धि ब्याज क्रमशः 12800 रुपए और 14400 रुपए है। मूलधन कितना है ?</p>",
                    options_en: [
                        "<p>Rs. 25600</p>",
                        "<p>Rs. 27800</p>",
                        "<p>Rs. 24400</p>",
                        "<p>Rs. 29900</p>"
                    ],
                    options_hi: [
                        "<p>25600 रुपए</p>",
                        "<p>27800 रुपए</p>",
                        "<p>24400 रुपए</p>",
                        "<p>29900 रुपए</p>"
                    ],
                    solution_en: "<p>59.(a)<br>ATQ, We have following diagram ;<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741669109566.png\" alt=\"rId58\" width=\"179\" height=\"156\"><br>(CI - SI) for 2 yrs = 14400 - 12800 = ₹1600<br>SI for 1 yr = <math display=\"inline\"><mfrac><mrow><mn>12800</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> = 6400<br>rate% = <math display=\"inline\"><mfrac><mrow><mn>1600</mn></mrow><mrow><mn>6400</mn></mrow></mfrac></math> &times; 100 = 25%<br>Principal amount = 6400 &times; <math display=\"inline\"><mfrac><mrow><mn>100</mn></mrow><mrow><mn>25</mn></mrow></mfrac></math> = ₹25600</p>",
                    solution_hi: "<p>59.(a)<br>प्रश्नानुसार , हमारे पास निम्नलिखित चित्र है;<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741669109676.png\" alt=\"rId59\" width=\"188\" height=\"155\"><br>(सा∘ ब्याज - चक्र∘ ब्याज) 2 साल के लिए = 14400 - 12800 = ₹1600<br>1 वर्ष के लिए सा ब्याज = <math display=\"inline\"><mfrac><mrow><mn>12800</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> = 6400<br>दर% = <math display=\"inline\"><mfrac><mrow><mn>1600</mn></mrow><mrow><mn>6400</mn></mrow></mfrac></math> &times; 100 = 25%<br>मूल राशि = 6400 &times; <math display=\"inline\"><mfrac><mrow><mn>100</mn></mrow><mrow><mn>25</mn></mrow></mfrac></math> = ₹25600</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "60",
                    section: "17",
                    question_en: "<p>60. A cylinder has a curved surface area equal to 350% of the curved surface area of another cylinder. If their radii are in the ratio 3 : 1, then the volume of the smaller cylinder is approximately _____% of the larger cylinder (the smaller cylinder has its radius and height smaller when compared to the larger).</p>",
                    question_hi: "<p>60. एक बेलन का वक्र पृष्ठीय क्षेत्रफल दूसरे बेलन के वक्र पृष्ठीय क्षेत्रफल के 350% के बराबर है। यदि उनकी त्रिज्याएँ 3 : 1 के अनुपात में हैं, तो छोटे आकार वाले बेलन का आयतन, बड़े आकार वाले बेलन का लगभग _____% है (बड़े आकार वाले बेलन की तुलना में छोटे आकार वाले बेलन की त्रिज्या और ऊँचाई कम है)।</p>",
                    options_en: [
                        "<p>10.16</p>",
                        "<p>8.86</p>",
                        "<p>7.28</p>",
                        "<p>9.52</p>"
                    ],
                    options_hi: [
                        "<p>10.16</p>",
                        "<p>8.86</p>",
                        "<p>7.28</p>",
                        "<p>9.52</p>"
                    ],
                    solution_en: "<p>60.(d)<br>According to the question,<br>CSA of the cylinder = 2&pi;rh<br>2&pi;rh &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>350</mn><mn>100</mn></mfrac></math> = 2&pi;RH<br>h : H = 6 : 7 (where radii ratio are, 3 : 1)<br>Volume of the cylinder = &pi;r<sup>2</sup>h<br>Small cylinder volume = &pi;(1)<sup>2</sup> &times; 6<br>Big cylinder volume = &pi;(3)<sup>2</sup> &times; 7<br>required% = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#960;</mi><msup><mfenced separators=\"|\"><mn>1</mn></mfenced><mn>2</mn></msup><mo>&#215;</mo><mn>6</mn></mrow><mrow><mi>&#160;</mi><mi>&#960;</mi><msup><mfenced separators=\"|\"><mn>3</mn></mfenced><mn>2</mn></msup><mo>&#215;</mo><mn>7</mn></mrow></mfrac></math> &times; 100 = 9.52%</p>",
                    solution_hi: "<p>60.(d)<br>प्रश्न के अनुसार,<br>वेलन का वक्रपृष्ठीय क्षेत्रफल = 2&pi;rh<br>2&pi;rh &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>350</mn><mn>100</mn></mfrac></math> = 2&pi;RH<br>h : H = 6 : 7 (त्रिज्याएँ , 3 : 1 के अनुपात मे है।)<br>वेलन का आयतन = &pi;r<sup>2</sup>h<br>छोटे वेलन का आयतन = &pi;(1)<sup>2</sup> &times; 6<br>बडे़ वेलन का आयतन = &pi;(3)<sup>2</sup> &times; 7<br>आवश्यक% = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#960;</mi><msup><mfenced separators=\"|\"><mn>1</mn></mfenced><mn>2</mn></msup><mo>&#215;</mo><mn>6</mn></mrow><mrow><mi>&#160;</mi><mi>&#960;</mi><msup><mfenced separators=\"|\"><mn>3</mn></mfenced><mn>2</mn></msup><mo>&#215;</mo><mn>7</mn></mrow></mfrac></math> &times; 100 = 9.52%</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "61",
                    section: "17",
                    question_en: "<p>61. The number nearest to 7658 and exactly divisible by 45 is :</p>",
                    question_hi: "<p>61. 7658 के निकटतम और 45 से पूर्ण विभाज्य संख्या ______ है।</p>",
                    options_en: [
                        "<p>7645</p>",
                        "<p>7660</p>",
                        "<p>7640</p>",
                        "<p>7650</p>"
                    ],
                    options_hi: [
                        "<p>7645</p>",
                        "<p>7660</p>",
                        "<p>7640</p>",
                        "<p>7650</p>"
                    ],
                    solution_en: "<p>61.(d)<br><math display=\"inline\"><mfrac><mrow><mn>7658</mn></mrow><mrow><mn>45</mn></mrow></mfrac></math> = 8 (remainder) <br>So the nearest number of 7658 which is exactly divisible by 8 will be 7658 - 8 = 7650</p>",
                    solution_hi: "<p>61.(d)<br><math display=\"inline\"><mfrac><mrow><mn>7658</mn></mrow><mrow><mn>45</mn></mrow></mfrac></math> = 8 (शेषफल) <br>अतः 7658 की निकटतम संख्या जो 8 से पूर्णतः विभाज्य है, 7658 - 8 = 7650 होगी</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "62",
                    section: "17",
                    question_en: "<p>62. The difference between the smallest common multiple and biggest common factor of 5, 10 and 35 is</p>",
                    question_hi: "<p>62. 5, 10 और 35 के सबसे छोटे सार्वगुणज़ और सबसे बड़े सार्वगुणनखंड का अंतर है</p>",
                    options_en: [
                        "<p>30</p>",
                        "<p>35</p>",
                        "<p>65</p>",
                        "<p>75</p>"
                    ],
                    options_hi: [
                        "<p>30</p>",
                        "<p>35</p>",
                        "<p>65</p>",
                        "<p>75</p>"
                    ],
                    solution_en: "<p>62.(c)<br>LCM of (5, 10, 35) = 70<br>HCF of (5, 10, 35) = 5<br>Difference = 70 - 5 = 65</p>",
                    solution_hi: "<p>62.(c)<br>(5, 10, 35) का लघुत्तम समापवर्त्य = 70<br>(5, 10, 35) का महत्तम समापवर्तक = 5<br>अंतर = 70 - 5 = 65</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "63",
                    section: "17",
                    question_en: "<p>63. A shopkeeper gives a 10% discount and still earns 10% profit. The marked price of the item is how many times (correct to 2 decimal places) the cost price?</p>",
                    question_hi: "<p>63. एक दुकानदार 10% की छूट देता है और फिर भी 10% का लाभ अर्जित करता है। वस्तु का अंकित मूल्य, वस्तु के क्रय मूल्य का कितना गुना (दशमलव के 2 स्थानों तक सही) है?</p>",
                    options_en: [
                        "<p>1.28</p>",
                        "<p>1.20</p>",
                        "<p>1.22</p>",
                        "<p>1.24</p>"
                    ],
                    options_hi: [
                        "<p>1.28</p>",
                        "<p>1.20</p>",
                        "<p>1.22</p>",
                        "<p>1.24</p>"
                    ],
                    solution_en: "<p>63.(c)<br><math display=\"inline\"><mfrac><mrow><mi>C</mi><mi>P</mi></mrow><mrow><mi>M</mi><mi>P</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>100</mn><mo>-</mo><mi>D</mi><mo>%</mo></mrow><mrow><mn>100</mn><mo>+</mo><mi>P</mi><mi>r</mi><mi>o</mi><mi>f</mi><mi>i</mi><mi>t</mi><mo>%</mo></mrow></mfrac></math><br><math display=\"inline\"><mfrac><mrow><mi>C</mi><mi>P</mi></mrow><mrow><mi>M</mi><mi>P</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>90</mn><mn>110</mn></mfrac></math><br>CP : MP = 9 : 11<br>Required value = <math display=\"inline\"><mfrac><mrow><mn>11</mn></mrow><mrow><mn>9</mn></mrow></mfrac></math> = 1.22 times</p>",
                    solution_hi: "<p>63.(c)<br><math style=\"font-family:Verdana\" display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2325;&#2381;&#2352;&#2351;</mi><mi>&#160;</mi><mi>&#2350;&#2370;&#2354;&#2381;&#2351;</mi><mi>&#160;</mi></mrow><mrow><mi>&#2309;&#2306;&#2325;&#2367;&#2340;</mi><mi>&#160;</mi><mi>&#2350;&#2370;&#2354;&#2381;&#2351;</mi></mrow></mfrac></math> = <math style=\"font-family:Verdana\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>100</mn><mo>-</mo><mi>&#2331;&#2370;&#2335;</mi><mo>%</mo></mrow><mrow><mn>100</mn><mo>+</mo><mi>&#2354;&#2366;&#2349;</mi><mo>%</mo></mrow></mfrac></math> <br><math style=\"font-family:Verdana\" display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2325;&#2381;&#2352;&#2351;</mi><mi mathvariant=\"normal\">&#160;</mi><mi>&#2350;&#2370;&#2354;&#2381;&#2351;</mi><mi mathvariant=\"normal\">&#160;</mi></mrow><mrow><mi>&#2309;&#2306;&#2325;&#2367;&#2340;</mi><mi mathvariant=\"normal\">&#160;</mi><mi>&#2350;&#2370;&#2354;&#2381;&#2351;</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>90</mn><mn>110</mn></mfrac></math><br>क्रय मूल्य : अंकित मूल्य = 9 : 11<br>आवश्यक मान = <math display=\"inline\"><mfrac><mrow><mn>11</mn></mrow><mrow><mn>9</mn></mrow></mfrac></math> = 1.22 गुना</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "64",
                    section: "17",
                    question_en: "<p>64. The length (in cm) of a direct common tangent to two circles of radii 14 cm and 5 cm&nbsp;whose centres are separated by a distance of 40 cm is:</p>",
                    question_hi: "<p>64. यदि 14 cm और 5 cm त्रिज्या वाले दो वृत्तों के केंद्रों के बीच की दूरी 40 cm हैं, तो दोनों वृत्तों पर खींची गई सीधी उभयनिष्ठ स्पर्श रेखा की लंबाई (cm में) क्या होगी?</p>",
                    options_en: [
                        "<p>40 cm</p>",
                        "<p>41 cm</p>",
                        "<p><math display=\"inline\"><msqrt><mn>1539</mn></msqrt></math> cm</p>",
                        "<p><math display=\"inline\"><msqrt><mn>1519</mn></msqrt><mi>&#160;</mi></math>cm</p>"
                    ],
                    options_hi: [
                        "<p>40 cm</p>",
                        "<p>41 cm</p>",
                        "<p><math display=\"inline\"><msqrt><mn>1539</mn></msqrt></math> cm</p>",
                        "<p><math display=\"inline\"><msqrt><mn>1519</mn></msqrt><mi>&#160;</mi></math>cm</p>"
                    ],
                    solution_en: "<p>64.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741669109784.png\" alt=\"rId60\" width=\"243\" height=\"109\"><br>Length of direct common tangent (LM) <br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mrow><mo>(</mo><mn>40</mn><mo>)</mo></mrow><mn>2</mn></msup><mo>-</mo><mo>(</mo><mn>14</mn><mo>-</mo><mn>5</mn><msup><mo>)</mo><mn>2</mn></msup></msqrt></math> <br>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>1600</mn><mo>-</mo><mo>(</mo><mn>81</mn><mo>)</mo></msqrt></math> <br>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>1519</mn></msqrt></math> cm</p>",
                    solution_hi: "<p>64.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741669109784.png\" alt=\"rId60\" width=\"243\" height=\"109\"><br>सीधी उभयनिष्ठ स्पर्शरेखा की लंबाई (LM) <br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mrow><mo>(</mo><mn>40</mn><mo>)</mo></mrow><mn>2</mn></msup><mo>-</mo><mo>(</mo><mn>14</mn><mo>-</mo><mn>5</mn><msup><mo>)</mo><mn>2</mn></msup></msqrt></math> <br>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>1600</mn><mo>-</mo><mo>(</mo><mn>81</mn><mo>)</mo></msqrt></math> <br>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>1519</mn></msqrt></math>सेमी</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "65",
                    section: "17",
                    question_en: "<p>65. Find the remainder, when we divide 2x<sup>3</sup> - 3x<sup>2</sup> + 6x - 4 by 2x - 3.</p>",
                    question_hi: "<p>65. जब हम 2x<sup>3</sup> - 3x<sup>2</sup> + 6x - 4 को 2x - 3 से भाग देते है तो प्राप्त शेषफल ज्ञात कीजिए।</p>",
                    options_en: [
                        "<p>6</p>",
                        "<p>-6</p>",
                        "<p>5</p>",
                        "<p>-5</p>"
                    ],
                    options_hi: [
                        "<p>6</p>",
                        "<p>-6</p>",
                        "<p>5</p>",
                        "<p>-5</p>"
                    ],
                    solution_en: "<p>65.(c)<br>2x - 3 = 0<br>x = <math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math><br>Putting the value of x in 2x<sup>3</sup> - 3x<sup>2</sup> + 6x - 4<br>&rArr; 2(<math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math>)<sup>3</sup> - 3(<math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math>)<sup>2</sup> + 6 &times; <math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> - 4<br>&rArr; 2 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>27</mn><mn>8</mn></mfrac></math> - 3 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>9</mn><mn>4</mn></mfrac></math> + 6 &times; <math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> - 4<br>&rArr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>27</mn><mn>4</mn></mfrac></math> - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>27</mn><mn>4</mn></mfrac></math> + 9 - 4 = 5</p>",
                    solution_hi: "<p>65.(c)<br>2x - 3 = 0<br>x = <math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math><br>x का मान 2x<sup>3</sup> - 3x<sup>2</sup> + 6x - 4 में रखने पर<br>&rArr; 2(<math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math>)<sup>3</sup> - 3(<math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math>)<sup>2</sup> + 6 &times; <math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> - 4<br>&rArr; 2 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>27</mn><mn>8</mn></mfrac></math> - 3 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>9</mn><mn>4</mn></mfrac></math> + 6 &times; <math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> - 4<br>&rArr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>27</mn><mn>4</mn></mfrac></math> - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>27</mn><mn>4</mn></mfrac></math> + 9 - 4 = 5</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "66",
                    section: "17",
                    question_en: "<p>66. The ratio between the monthly incomes of P and Q is 7 : 6 and the ratio between their expenditures is 6 : 5. If they save ₹480 each, then find P&rsquo;s monthly income.</p>",
                    question_hi: "<p>66. P की मासिक आय और Q की मासिक आय के बीच का अनुपात 7 : 6 है और उनके व्ययों के बीच का अनुपात 6 : 5 है। यदि उनमें से प्रत्येक ₹480 की बचत करता है, तो P की मासिक आय ज्ञात कीजिए।</p>",
                    options_en: [
                        "<p>₹2,880</p>",
                        "<p>₹2,400</p>",
                        "<p>₹6,240</p>",
                        "<p>₹3,360</p>"
                    ],
                    options_hi: [
                        "<p>₹2,880</p>",
                        "<p>₹2,400</p>",
                        "<p>₹6,240</p>",
                        "<p>₹3,360</p>"
                    ],
                    solution_en: "<p>66.(d)<br>Ratio&nbsp; &nbsp; &nbsp;&rarr; P : Q<br>Income&nbsp; &rarr; 7 : 6<br>Expend. &rarr; 6 : 5<br>&mdash;-------------------------------<br>Saving&nbsp; &nbsp;&rarr; 1 : 1<br>Saving (1 unit) = ₹ 480<br>P&rsquo;s monthly Income (7 unit) = 7 &times; 480 = ₹ 3,360</p>",
                    solution_hi: "<p>66.(d)<br>अनुपात &rarr; P : Q<br>आय&nbsp; &nbsp; &nbsp;&rarr; 7 : 6<br>व्यय&nbsp; &nbsp; &nbsp;&rarr; 6 : 5<br>&mdash;-------------------------------<br>बचत&nbsp; &nbsp; &rarr; 1 : 1<br>बचत (1 इकाई) = ₹ 480<br>P की मासिक आय (7 इकाई) = 7 &times; 480 = ₹ 3,360</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "67",
                    section: "17",
                    question_en: "<p>67. In a mixture of 110 litre the ratio of water and milk is 4 : 7 respectively. How much water must be added to make the ratio of water and milk as 8 : 7 respectively ?</p>",
                    question_hi: "<p>67. 110 लीटर के एक मिश्रण में पानी और दूध का अनुपात क्रमशः 4 : 7 है। पानी और दूध का अनुपात क्रमशः 8 : 7 करने के लिए मिश्रण में कितना पानी मिलाना होगा?</p>",
                    options_en: [
                        "<p>30 litre</p>",
                        "<p>20 litre</p>",
                        "<p>40 litre</p>",
                        "<p>10 litre</p>"
                    ],
                    options_hi: [
                        "<p>30 लीटर</p>",
                        "<p>20 लीटर</p>",
                        "<p>40 लीटर</p>",
                        "<p>10 लीटर</p>"
                    ],
                    solution_en: "<p>67.(c)<br>Ratio - Water&nbsp; &nbsp;Milk<br>&nbsp;Old&nbsp; &nbsp;-&nbsp; &nbsp; &nbsp;4&nbsp; &nbsp; :&nbsp; &nbsp; 7<br>&nbsp;New -&nbsp; &nbsp; &nbsp;8&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; 7<br>According to the question,<br>4 + 7 = 11 unit = 110 litres&nbsp;<br>Then, 8 - 4 = 4 unit = <math display=\"inline\"><mfrac><mrow><mn>110</mn></mrow><mrow><mn>11</mn></mrow></mfrac></math>&times; 4 = 40 litres</p>",
                    solution_hi: "<p>67.(c)<br>अनुपात - पानी&nbsp; &nbsp; दूध<br>पुराना&nbsp; &nbsp; -&nbsp; &nbsp;4&nbsp; &nbsp;:&nbsp; &nbsp;7<br>नया&nbsp; &nbsp; &nbsp; -&nbsp; &nbsp; 8&nbsp; &nbsp;:&nbsp; &nbsp;7<br>प्रश्न के अनुसार, <br>4 + 7 = 11 यूनिट = 110 लीटर<br>तो, 8 - 4 = 4 यूनिट = <math display=\"inline\"><mfrac><mrow><mn>110</mn></mrow><mrow><mn>11</mn></mrow></mfrac></math> &times; 4 = 40 लीटर</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "68",
                    section: "17",
                    question_en: "<p>68. Fifteen persons working 12 hours a day earn ₹18,000 per week. How many rupees will 18 persons working 9 hours a day earn per week?</p>",
                    question_hi: "<p>68. पंद्रह व्यक्ति प्रतिदिन 12 घंटे काम करके प्रति सप्ताह ₹18,000 कमाते हैं। 18 व्यक्ति प्रतिदिन 9 घंटे काम करके प्रति सप्ताह कितने रुपए कमाएंगे ?</p>",
                    options_en: [
                        "<p>₹16,020</p>",
                        "<p>₹16,200</p>",
                        "<p>₹12,060</p>",
                        "<p>₹12,600</p>"
                    ],
                    options_hi: [
                        "<p>₹16,020</p>",
                        "<p>₹16,200</p>",
                        "<p>₹12,060</p>",
                        "<p>₹12,600</p>"
                    ],
                    solution_en: "<p>68.(b) <br>Let required rupee = Rs. x<br>According to the question,<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>15</mn><mo>&#215;</mo><mn>12</mn></mrow><mn>18000</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>18</mn><mo>&#215;</mo><mn>9</mn></mrow><mi>x</mi></mfrac></math><br>x = ₹16,200</p>",
                    solution_hi: "<p>68.(b) <br>माना आवश्यक रुपया = रु. x<br>प्रश्न के अनुसार,<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>15</mn><mo>&#215;</mo><mn>12</mn></mrow><mn>18000</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>18</mn><mo>&#215;</mo><mn>9</mn></mrow><mi>x</mi></mfrac></math><br>x = ₹16,200</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "69",
                    section: "17",
                    question_en: "<p>69. Simplify 20 &divide; 5 of 4 &times; 8 + 6 &divide; 24 of 3 &times; 48 - 6 &divide; 18 &times; 9.</p>",
                    question_hi: "<p>69. 20 &divide; 5 of 4 &times; 8 + 6 &divide; 24 of 3 &times; 48 - 6 &divide; 18 &times; 9 को सरल कीजिए।</p>",
                    options_en: [
                        "<p>9</p>",
                        "<p>6</p>",
                        "<p>4</p>",
                        "<p>1.125</p>"
                    ],
                    options_hi: [
                        "<p>9</p>",
                        "<p>6</p>",
                        "<p>4</p>",
                        "<p>1.125</p>"
                    ],
                    solution_en: "<p>69.(a)<br>20 &divide; 5 of 4 &times; 8 + 6 &divide; 24 of 3 &times; 48 - 6 &divide; 18 &times; 9<br>20 &divide; 20 &times; 8 + 6 &divide; 72 &times; 48 - <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> &times; 9<br>1 &times; 8 + <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>12</mn></mrow></mfrac></math> &times; 48 - <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> &times; 9<br>8 + 4 - 3 = 9</p>",
                    solution_hi: "<p>69.(a)<br>20 &divide; 5 of 4 &times; 8 + 6 &divide; 24 of 3 &times; 48 - 6 &divide; 18 &times; 9<br>20 &divide; 20 &times; 8 + 6 &divide; 72 &times; 48 - <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> &times; 9<br>1 &times; 8 + <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>12</mn></mrow></mfrac></math> &times; 48 - <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> &times; 9<br>8 + 4 - 3 = 9</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "70",
                    section: "17",
                    question_en: "<p>70. If m + 5 = sec<sup>2</sup>Acosec<sup>2</sup>A(1 - cos<sup>2</sup>A) (1 - sin<sup>2</sup>A) + 5, then find the value of m:</p>",
                    question_hi: "<p>70. यदि m + 5 = sec<sup>2</sup>Acosec<sup>2</sup>A(1 - cos<sup>2</sup>A) (1 - sin<sup>2</sup>A) + 5, है, तो m का मान ज्ञात कीजिए।</p>",
                    options_en: [
                        "<p>10</p>",
                        "<p>0</p>",
                        "<p>1</p>",
                        "<p>5</p>"
                    ],
                    options_hi: [
                        "<p>10</p>",
                        "<p>0</p>",
                        "<p>1</p>",
                        "<p>5</p>"
                    ],
                    solution_en: "<p>70.(c)<br>&rArr; m + 5 = sec<sup>2</sup>Acosec<sup>2</sup>A(1 - cos<sup>2</sup>A) (1 - sin<sup>2</sup>A) + 5<br>&rArr; m + 5 = sec<sup>2</sup>A.cosec<sup>2</sup>A .sin<sup>2</sup>A.cos<sup>2</sup>A + 5<br>&rArr; m + 5 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><msup><mi>cos</mi><mn>2</mn></msup><mi>A</mi></mrow></mfrac></math>.<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><msup><mi>sin</mi><mn>2</mn></msup><mi>A</mi></mrow></mfrac><mo>&#160;</mo></math>.sin<sup>2</sup>A.cos<sup>2</sup>A + 5<br>&rArr; m = 1</p>",
                    solution_hi: "<p>70.(c)<br>&rArr; m + 5 = sec<sup>2</sup>Acosec<sup>2</sup>A(1 - cos<sup>2</sup>A) (1 - sin<sup>2</sup>A) + 5<br>&rArr; m + 5 = sec<sup>2</sup>A.cosec<sup>2</sup>A .sin<sup>2</sup>A.cos<sup>2</sup>A + 5<br>&rArr; m + 5 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><msup><mi>cos</mi><mn>2</mn></msup><mi>A</mi></mrow></mfrac></math>.<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><msup><mi>sin</mi><mn>2</mn></msup><mi>A</mi></mrow></mfrac><mo>&#160;</mo></math>.sin<sup>2</sup>A.cos<sup>2</sup>A + 5<br>&rArr; m = 1</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "71",
                    section: "17",
                    question_en: "<p>71. A covered wooden box has the inner measures as 128 cm, 90 cm, 25 cm and the thickness of wood is 5.5 cm. Find the volume of the wood.</p>",
                    question_hi: "<p>71. एक ढके हुए लकड़ी के बॉक्स की भीतरी माप 128 cm, 90 cm, 25 cm है और लकड़ी की मोटाई 5.5 cm है। लकड़ी का आयतन ज्ञात करें।</p>",
                    options_en: [
                        "<p>192392 cm<sup>3</sup></p>",
                        "<p>819832 cm<sup>3</sup></p>",
                        "<p>329431 cm<sup>3</sup></p>",
                        "<p>217404 cm<sup>3</sup></p>"
                    ],
                    options_hi: [
                        "<p>192392 cm<sup>3</sup></p>",
                        "<p>819832 cm<sup>3</sup></p>",
                        "<p>329431 cm<sup>3</sup></p>",
                        "<p>217404 cm<sup>3</sup></p>"
                    ],
                    solution_en: "<p>71.(d)<br>Dimension of inner part of wooden box = 128 &times; 90 &times; 25 <br>Volume of inner part = 128 &times; 90 &times; 25 = 288000 cm<sup>3</sup><br>Dimension of outer part of wooden box = (128 + 11) &times; (90 + 11) &times; (25 + 11) <br>Volume of outer part of wooden box = 139 &times; 101 &times; 36 = 505404 cm<sup>3</sup><br>Volume of wood = volume of outer part - volume of inner part <br>= 505404 - 288000 = 217404 cm<sup>3</sup></p>",
                    solution_hi: "<p>71.(d)<br>लकड़ी के बक्से के भीतरी भाग का आयाम = 128 &times; 90 &times; 25 <br>आंतरिक भाग का आयतन = 128 &times; 90 &times; 25 = 288000 सेमी<sup>3</sup><br>लकड़ी के बक्से के बाहरी भाग का आयाम = (128 + 11) &times; (90 + 11) &times; (25 + 11) <br>लकड़ी के बक्से के बाहरी भाग का आयतन = 139 &times; 101 &times; 36 = 505404 सेमी<sup>3</sup><br>लकड़ी का आयतन = बाहरी भाग का आयतन - भीतरी भाग का आयतन <br>= 505404 - 288000 = 217404 सेमी<sup>3</sup></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "72",
                    section: "17",
                    question_en: "<p>72. To reach at a destination, D takes 45 minutes if he drives at a speed of 60 km/h. Due to some urgency, he is to reach the destination in 30 minutes. What should be his speed in km/h?</p>",
                    question_hi: "<p>72. यदि D, 60 km/h की चाल से गाड़ी चलाता है तो किसी गंतव्य पर पहुंचने के लिए उसे 45 मिनट का समय लगता है। कुछ अत्यावश्यकता के कारण, उसे 30 मिनट में गंतव्य तक पहुंचना है। km/h में उसकी चाल क्या होनी चाहिए?</p>",
                    options_en: [
                        "<p>90</p>",
                        "<p>75</p>",
                        "<p>60</p>",
                        "<p>80</p>"
                    ],
                    options_hi: [
                        "<p>90</p>",
                        "<p>75</p>",
                        "<p>60</p>",
                        "<p>80</p>"
                    ],
                    solution_en: "<p>72.(a)<br>Distance = speed &times; time<br>Distance = 60 &times; <math display=\"inline\"><mfrac><mrow><mn>45</mn></mrow><mrow><mn>60</mn></mrow></mfrac></math> = 45 km<br>Required speed = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>45</mn><mo>&#215;</mo><mn>60</mn></mrow><mn>30</mn></mfrac></math> = 90 km/h</p>",
                    solution_hi: "<p>72.(a)<br>दूरी = गति &times; समय<br>दूरी = 60 &times; <math display=\"inline\"><mfrac><mrow><mn>45</mn></mrow><mrow><mn>60</mn></mrow></mfrac></math> = 45 km<br>आवश्यक गति = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>45</mn><mo>&#215;</mo><mn>60</mn></mrow><mn>30</mn></mfrac></math>&nbsp;= 90 km/h</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "73",
                    section: "17",
                    question_en: "<p>73. Kamlesh offers the following two discount schemes to his customers on the sale of an article:<br>(i) Two successive discounts of 25% and 5%<br>(ii) Two successive discounts of 20% and 10%<br>What is the difference between the selling price (in ₹) of the article under these two discount schemes if its marked price is ₹1,000?</p>",
                    question_hi: "<p>73. कमलेश अपने ग्राहकों को एक बस्तु की बिक्री पर निम्नलिखित दो छूट योजनाएं प्रदान करता है।<br>(i) 25% और 5% की दो क्रमिक छूट<br>(ii) 20% और 10% की दो क्रमिक छूट<br>यदि वस्तु का अंकित मूल्य ₹1,000 है तो इन दो छूट योजनाओं के तहत वस्तु के विक्रय मूल्य ( ₹ में) के बीच का अंतर क्या है?</p>",
                    options_en: [
                        "<p>7.0</p>",
                        "<p>7.5</p>",
                        "<p>8.0</p>",
                        "<p>6.5</p>"
                    ],
                    options_hi: [
                        "<p>7.0</p>",
                        "<p>7.5</p>",
                        "<p>8.0</p>",
                        "<p>6.5</p>"
                    ],
                    solution_en: "<p>73.(b)<br>(i) SP = 1000 &times; <math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>19</mn><mn>20</mn></mfrac></math> = 712.5<br>(ii) SP = 1000 &times; <math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>9</mn><mn>10</mn></mfrac></math> = 720<br>Difference between SP = 720 - 712.5 = 7.5</p>",
                    solution_hi: "<p>73.(b)<br>(i) विक्रय मूल्य = 1000 &times;&nbsp;<math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>19</mn><mn>20</mn></mfrac></math> = 712.5<br>(ii) विक्रय मूल्य = 1000 &times; <math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>9</mn><mn>10</mn></mfrac></math> = 720<br>विक्रय मूल्यो के बीच का अंतर = 720 - 712.5 = 7.5</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "74",
                    section: "17",
                    question_en: "<p>74. Given below is the observed data of the ages of various children.<br><img src=\"data:image/png;base64,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\" width=\"246\" height=\"118\"><br>What is the mean of the ages of the children?</p>",
                    question_hi: "<p>74. नीचे विभिन्न बच्चों की आयु का अवलोकन किया गया डेटा दिया गया है। <br><img src=\"data:image/png;base64,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\" width=\"246\" height=\"124\"><br>बच्चों की आयु का माध्य कितना है?</p>",
                    options_en: [
                        "<p>7.575 years</p>",
                        "<p>6.575 years</p>",
                        "<p>8.575 years</p>",
                        "<p>5.575 years</p>"
                    ],
                    options_hi: [
                        "<p>7.575 वर्ष</p>",
                        "<p>6.575 वर्ष</p>",
                        "<p>8.575 वर्ष</p>",
                        "<p>5.575 वर्ष</p>"
                    ],
                    solution_en: "<p>74.(c)<br>Sum of age of children <br>= (6 &times; 8) + (7 &times; 3) + (8 &times; 7) + (9 &times; 2) + (10 &times; 20)&nbsp;<br>= 48 + 21 + 56 + 18 + 200 <br>= 343 <br>Required mean = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>343</mn><mrow><mn>8</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>3</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>7</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>2</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>20</mn></mrow></mfrac></math> <br>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>343</mn><mn>40</mn></mfrac></math> = 8.575 year</p>",
                    solution_hi: "<p>74.(c)<br>बच्चों की आयु का योग <br>= (6 &times; 8) + (7 &times; 3) + (8 &times; 7) + (9 &times; 2) + (10 &times; 20)&nbsp;<br>= 48 + 21 + 56 + 18 + 200 <br>= 343<br>अभीष्ट माध्य = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>343</mn><mrow><mn>8</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>3</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>7</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>2</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>20</mn></mrow></mfrac></math> <br>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>343</mn><mn>40</mn></mfrac></math> = 8.575 वर्ष</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "75",
                    section: "7",
                    question_en: "<p>75. Find the area of the quadrilateral ABCD, the coordinates of whose vertices are A(6, - 3), B(-4, - 2), C(3, 1) and D(5, 0)?</p>",
                    question_hi: "<p>75. उस चतुर्भुज ABCD का क्षेत्रफल ज्ञात कीजिए जिसके शीर्षों के निर्देशांक A(6, - 3),B(- 4, - 2),C(3, 1) और D(5, 0) हैं?</p>",
                    options_en: [
                        "<p>21 unit<sup>2</sup></p>",
                        "<p>30 unit<sup>2</sup></p>",
                        "<p>25 unit<sup>2</sup></p>",
                        "<p>16 unit<sup>2</sup></p>"
                    ],
                    options_hi: [
                        "<p>21 इकाई<sup>2</sup></p>",
                        "<p>30 इकाई<sup>2</sup></p>",
                        "<p>25 इकाई<sup>2</sup></p>",
                        "<p>16 इकाई<sup>2</sup></p>"
                    ],
                    solution_en: "<p>75.(a)<br>Area of quadrilateral = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math>|[(x<sub>1</sub>y<sub>2 </sub>+ x<sub>2</sub>y<sub>3</sub> + x<sub>3</sub>y<sub>4 </sub>+ x<sub>4</sub>y<sub>1</sub>) - (y<sub>1</sub>x<sub>2 </sub>+ y<sub>2</sub>x<sub>3</sub> + y<sub>3</sub>x<sub>4 </sub>+ y<sub>4</sub>x<sub>1</sub>]|<br>Given, A(x<sub>1</sub>y<sub>1</sub>) = (6, - 3) , B (x<sub>2</sub>,y<sub>2</sub>) = (- 4, - 2) , C (x<sub>3</sub>,y<sub>3</sub>) = (3, 1) and D(x<sub>4</sub>,y<sub>4</sub>) =(5, 0)<br>Area of quadrilateral <br>= <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> &times; [{6 &times; (- 2) + (-4) &times; 1 + 3 &times; 0 + 5 &times; (- 3)} - {(-3) &times; (- 4) + (-2) &times; 3 + 1 &times; 5 + 0 &times; 6} ]<br>= <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> &times; [{ - 12 - 4 + 0 - 15} - {12 - 6 + 5 + 0}]<br>= <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> &times; [{ - 31} - {11}]<br>= <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> &times; [{ - 31} - {11}]<br>= <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> &times; |- 42|<br>= <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> &times; 42 = 21 unit<sup>2</sup></p>",
                    solution_hi: "<p>75.(a)<br>चतुर्भुज का क्षेत्रफल = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math>|[(x<sub>1</sub>y<sub>2 </sub>+ x<sub>2</sub>y<sub>3</sub> + x<sub>3</sub>y<sub>4 </sub>+ x<sub>4</sub>y<sub>1</sub>) - (y<sub>1</sub>x<sub>2 </sub>+ y<sub>2</sub>x<sub>3</sub> + y<sub>3</sub>x<sub>4 </sub>+ y<sub>4</sub>x<sub>1</sub>]|<br>दिया गया, A(x<sub>1</sub>y<sub>1</sub>) = (6, - 3) , B (x<sub>2</sub>,y<sub>2</sub>) = (- 4, - 2) , C (x<sub>3</sub>,y<sub>3</sub>) = (3, 1) और D(x<sub>4</sub>,y<sub>4</sub>) =(5, 0)<br>चतुर्भुज का क्षेत्रफल <br>= <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> &times; [{6 &times; (- 2) + (-4) &times; 1 + 3 &times; 0 + 5 &times; (- 3)} - {(-3) &times; (- 4) + (-2) &times; 3 + 1 &times; 5 + 0 &times; 6} ]<br>= <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> &times; [{ - 12 - 4 + 0 - 15} - {12 - 6 + 5 + 0}]<br>= <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> &times; [{ - 31} - {11}]<br>= <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> &times; [{ - 31} - {11}]<br>= <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> &times; |- 42|<br>= <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> &times; 42 = 21 इकाई<sup>2</sup></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "76",
                    section: "7",
                    question_en: "<p>76. Identify the segment in the sentence which contains grammatical error. Mark &lsquo;no error&rsquo; in case the sentence given is correct. <br>I was laying down when the doorbell rang.</p>",
                    question_hi: "<p>76. Identify the segment in the sentence which contains grammatical error. Mark &lsquo;no error&rsquo; in case the sentence given is correct. <br>I was laying down when the doorbell rang.</p>",
                    options_en: [
                        "<p>I was</p>",
                        "<p>laying down</p>",
                        "<p>when the doorbell rang</p>",
                        "<p>No error</p>"
                    ],
                    options_hi: [
                        "<p>I was</p>",
                        "<p>laying down</p>",
                        "<p>when the doorbell rang</p>",
                        "<p>No error</p>"
                    ],
                    solution_en: "<p>76.(b) laying down<br>Incorrect word is used in part b. Replace &lsquo;laying&rsquo; by &lsquo;lying&rsquo;. Lay means to to put or set down. Lying means in a horizontal position, like lying on bed,grass etc.</p>",
                    solution_hi: "<p>76.(b) laying down<br>Part b में गलत शब्द का प्रयोग किया गया है। &lsquo;laying&rsquo; के स्थान पर &lsquo;lying&rsquo; का प्रयोग होगा । Lay का अर्थ है रखना या लगाना। Lying का अर्थ है बिस्तर, घास आदि पर लेटना।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "77",
                    section: "7",
                    question_en: "<p>77. Select the most appropriate synonym of the given word.<br>Pacify</p>",
                    question_hi: "<p>77. Select the most appropriate synonym of the given word.<br>Pacify</p>",
                    options_en: [
                        "<p>Soothe</p>",
                        "<p>Strengthen</p>",
                        "<p>Blame</p>",
                        "<p>Rebuke</p>"
                    ],
                    options_hi: [
                        "<p>Soothe</p>",
                        "<p>Strengthen</p>",
                        "<p>Blame</p>",
                        "<p>Rebuke</p>"
                    ],
                    solution_en: "<p>77.(a) <strong>Soothe -</strong> to comfort somebody.<br><strong>Pacify- </strong>to make somebody who is angry or upset be calm or quiet.<br><strong>Strengthen- </strong>to make something or someone stronger.<br><strong>Blame-</strong> responsibility for something bad.<br><strong>Rebuke-</strong> to speak angrily to somebody.</p>",
                    solution_hi: "<p>77.(a) <strong>Soothe</strong> (शांत करना) - to comfort somebody.<br><strong>Pacify</strong> (शांत करना) - to make somebody who is angry or upset be calm or quiet.<br><strong>Strengthen</strong> (मज़बूत बनाना) - to make something or someone stronger.<br><strong>Blame </strong>(दोष) - responsibility for something bad.<br><strong>Rebuke </strong>(डाँटना) - to speak angrily to somebody.</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "78",
                    section: "7",
                    question_en: "<p>78. Parts of the following sentence have been underlined and given as options. Select the&nbsp;option that contains an error.<br><span style=\"text-decoration: underline;\">Policeman</span> came to <span style=\"text-decoration: underline;\">my village</span> and asked about <span style=\"text-decoration: underline;\">a person</span> who burgled <span style=\"text-decoration: underline;\">a house</span> last night.</p>",
                    question_hi: "<p>78. Parts of the following sentence have been underlined and given as options. Select the&nbsp;option that contains an error.<br><span style=\"text-decoration: underline;\">Policeman</span> came to <span style=\"text-decoration: underline;\">my village</span> and asked about <span style=\"text-decoration: underline;\">a person</span> who burgled <span style=\"text-decoration: underline;\">a house</span> last night.</p>",
                    options_en: [
                        "<p>policeman</p>",
                        "<p>a house</p>",
                        "<p>my village</p>",
                        "<p>a person</p>"
                    ],
                    options_hi: [
                        "<p>policeman</p>",
                        "<p>a house</p>",
                        "<p>my village</p>",
                        "<p>a person</p>"
                    ],
                    solution_en: "<p>78.(a) policeman<br>Article &lsquo;a&rsquo; must be used before the noun &lsquo;policeman&rsquo; as it is singular and begins with a consonant sound. Hence, &lsquo;A policeman&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>78.(a) policeman<br>Noun &lsquo;policeman&rsquo; से पहले article &lsquo;a&rsquo; का प्रयोग किया जाएगा क्योंकि यह एक singular है तथा एक consonant sound से प्रारंभ होता है। अतः, &lsquo;A policeman&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "79",
                    section: "7",
                    question_en: "<p>79. Select the most appropriate idiom that can substitute the underlined segment in the given sentence.<br>Even after becoming bankrupt, Charlie is able to <span style=\"text-decoration: underline;\">maintain an outward show</span>.</p>",
                    question_hi: "<p>79. Select the most appropriate idiom that can substitute the underlined segment in the given sentence.<br>Even after becoming bankrupt, Charlie is able to maintain an outward show.</p>",
                    options_en: [
                        "<p>bell the cat</p>",
                        "<p>cut a sorry figure</p>",
                        "<p>bring down the house</p>",
                        "<p>keep up appearance</p>"
                    ],
                    options_hi: [
                        "<p>bell the cat</p>",
                        "<p>cut a sorry figure</p>",
                        "<p>bring down the house</p>",
                        "<p>keep up appearance</p>"
                    ],
                    solution_en: "<p>79.(d) <strong>Keep up appearance-</strong> maintain an outward show.<br>E.g.- Even though he was worried, he kept up appearances and smiled.</p>",
                    solution_hi: "<p>79.(d) <strong>Keep up appearance -</strong> maintain an outward show./बाहरी दिखावा बनाए रखना <br>E.g.- Even though he was worried, he kept up appearances and smiled.</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "80",
                    section: "7",
                    question_en: "<p>80. Select the option that expresses the following sentence in active voice.<br>I am not interested in this assignment.</p>",
                    question_hi: "<p>80. Select the option that expresses the following sentence in active voice.<br>I am not interested in this assignment.</p>",
                    options_en: [
                        "<p>This assignment has not interested me.</p>",
                        "<p>This assignment is not interesting me.</p>",
                        "<p>This assignment did not interest me.</p>",
                        "<p>This assignment does not interest me.</p>"
                    ],
                    options_hi: [
                        "<p>This assignment has not interested me.</p>",
                        "<p>This assignment is not interesting me.</p>",
                        "<p>This assignment did not interest me.</p>",
                        "<p>This assignment does not interest me.</p>"
                    ],
                    solution_en: "<p>80.(d) This assignment does not interest me.(Correct)<br>(a) This assignment has not interested me. (Meaning of sentence changed)<br>(b) This assignment <span style=\"text-decoration: underline;\">is not interesting</span> me. (Incorrect Tense structure)<br>(c) This assignment <span style=\"text-decoration: underline;\">did not interest</span> me. (Incorrect Tense)</p>",
                    solution_hi: "<p>80.(d) This assignment does not interest me.(Correct)<br>(a) This assignment has not interested me. (Sentence का अर्थ बदल गया)<br>(b) This assignment <span style=\"text-decoration: underline;\">is not interesting</span> me. (ग़लत Tense structure)<br>(c) This assignment <span style=\"text-decoration: underline;\">did not interest</span> me. (ग़लत Tense)</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "81",
                    section: "7",
                    question_en: "<p>81. Select the option that expresses the following sentence in active voice.<br>Tara&rsquo;s painting was stolen from the museum.</p>",
                    question_hi: "<p>81. Select the option that expresses the following sentence in active voice.<br>Tara&rsquo;s painting was stolen from the museum.</p>",
                    options_en: [
                        "<p>Someone stole Tara&rsquo;s painting from the museum.</p>",
                        "<p>Someone has stolen Tara&rsquo;s painting from the museum.</p>",
                        "<p>Someone from the museum stole Tara&rsquo;s painting.</p>",
                        "<p>Tara&rsquo;s painting has been stolen from the museum.</p>"
                    ],
                    options_hi: [
                        "<p>Someone stole Tara&rsquo;s painting from the museum.</p>",
                        "<p>Someone has stolen Tara&rsquo;s painting from the museum.</p>",
                        "<p>Someone from the museum stole Tara&rsquo;s painting.</p>",
                        "<p>Tara&rsquo;s painting has been stolen from the museum.</p>"
                    ],
                    solution_en: "<p>81.(a) Someone stole Tara&rsquo;s painting from the museum. (Correct)<br>(b) Someone has stolen Tara&rsquo;s painting from the museum. (Incorrect Tense)<br>(c) Someone from the museum stole Tara&rsquo;s painting. (Incorrect Sentence Structure)<br>(d) Tara&rsquo;s painting has been stolen from the museum. (Incorrect Sentence Structure)</p>",
                    solution_hi: "<p>81.(a) Someone stole Tara&rsquo;s painting from the museum. (Correct)<br>(b) Someone has stolen Tara&rsquo;s painting from the museum. (गलत Tense)<br>(c) Someone from the museum stole Tara&rsquo;s painting. (गलत Sentence Structure)<br>(d) Tara&rsquo;s painting has been stolen from the museum. (गलत Sentence Structure)</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "82",
                    section: "7",
                    question_en: "<p>82. Given below are four jumbled sentences. Pick the option that gives their correct order.<br>A. In the middle of a lake they built their capital city.<br>B. In what is now Mexico there was an older Civilization and some 700 years ago they organized themselves into an Empire.<br>C. They also grew fine cotton and wove beautiful clothes and had plentiful gold to make jewellery.<br>D. They had developed a rich agriculture growing corn, Chilly pepper, avocado, and other fruits.</p>",
                    question_hi: "<p>82. Given below are four jumbled sentences. Pick the option that gives their correct order.<br>A. In the middle of a lake they built their capital city.<br>B. In what is now Mexico there was an older Civilization and some 700 years ago they organized themselves into an Empire.<br>C. They also grew fine cotton and wove beautiful clothes and had plentiful gold to make jewellery.<br>D. They had developed a rich agriculture growing corn, Chilly pepper, avocado, and other fruits.</p>",
                    options_en: [
                        "<p>BDCA</p>",
                        "<p>BDAC</p>",
                        "<p>DCAB</p>",
                        "<p>CABD</p>"
                    ],
                    options_hi: [
                        "<p>BDCA</p>",
                        "<p>BDAC</p>",
                        "<p>DCAB</p>",
                        "<p>CABD</p>"
                    ],
                    solution_en: "<p>82. (a) BDCA<br>Sentence B will be the starting line as it contains the main subject of the parajumble i.e.Older Civilization. And, Sentence D states about their agriculture growing. So, D will follow B. Further, Sentence C states that they made clothes and jewellery and Sentence A states where their capital city was. So, A will follow C. Going through the options, option (a) has the correct sequence.</p>",
                    solution_hi: "<p>82. (a) BDCA<br>Sentence B starting line होगी क्योंकि इसमें parajumble का मुख्य विषय &lsquo;Older Civilization&rsquo; शामिल है। Sentence D उनकी कृषि के बारे में बताता है। तो, B के बाद D आएगा। आगे, Sentence C बताता है कि उन्होंने कपड़े और आभूषण बनाए और Sentence A बताता है कि उनकी राजधानी कहाँ थी। इसलिए, C के बाद A आएगा । options के माध्यम से जाने पर , option (a) में सही sequence है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "83",
                    section: "7",
                    question_en: "<p>83. Select the word which means the same as the group of words given.<br>A person difficult to please</p>",
                    question_hi: "<p>83. Select the word which means the same as the group of words given.<br>A person difficult to please</p>",
                    options_en: [
                        "<p>Fastidious</p>",
                        "<p>Callous</p>",
                        "<p>Sadist</p>",
                        "<p>Ferocious</p>"
                    ],
                    options_hi: [
                        "<p>Fastidious</p>",
                        "<p>Callous</p>",
                        "<p>Sadist</p>",
                        "<p>Ferocious</p>"
                    ],
                    solution_en: "<p>83.(a) Fastidious<br><strong>Fastidious -</strong> A person difficult to please.<br><strong>Callous - </strong>showing or having an insensitive and cruel disregard for others.<br><strong>Sadist -</strong> a person who derives pleasure, especially sexual gratification, from inflicting pain or humiliation on others.<br><strong>Ferocious -</strong> savagely fierce, cruel, or violent.</p>",
                    solution_hi: "<p>83.(a) Fastidious<br><strong>Fastidious&nbsp; ( </strong><span class=\"Y2IQFc\" lang=\"hi\">दुराराध्य )</span><strong>-</strong> A person difficult to please.<br><strong>Callous ( </strong><span class=\"Y2IQFc\" lang=\"hi\">निर्दयी )</span><strong>- </strong>showing or having an insensitive and cruel disregard for others.<br><strong>Sadist (&nbsp;</strong>परपीड़क<strong> ) - </strong>a person who derives pleasure, especially sexual gratification, from inflicting pain or humiliation on others.<br><strong>Ferocious ( </strong><span class=\"Y2IQFc\" lang=\"hi\">क्रूर ) </span><strong>-&nbsp;</strong>savagely fierce, cruel, or violent.</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "84",
                    section: "7",
                    question_en: "<p>84. Underlined word in the sentence is not spelt correctly. Identify the correct spelling from the options given below.<br>Raman had been living a <span style=\"text-decoration: underline;\">reklusive</span> life since he had lost his job.</p>",
                    question_hi: "<p>84. Underlined word in the sentence is not spelt correctly. Identify the correct spelling from the options given below.<br>Raman had been living a <span style=\"text-decoration: underline;\">reklusive</span> life since he had lost his job.</p>",
                    options_en: [
                        "<p>Recslusive</p>",
                        "<p>Reclucive</p>",
                        "<p>Reslusive</p>",
                        "<p>Reclusive</p>"
                    ],
                    options_hi: [
                        "<p>Recslusive</p>",
                        "<p>Reclucive</p>",
                        "<p>Reslusive</p>",
                        "<p>Reclusive</p>"
                    ],
                    solution_en: "<p>84.(d) Reclusive<br>&lsquo;Reclusive&rsquo; is the correct spelling.</p>",
                    solution_hi: "<p>84.(d) Reclusive<br>&lsquo;Reclusive&rsquo; सही spelling है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "85",
                    section: "7",
                    question_en: "<p>85.&nbsp;Select the most appropriate option to fill in the blank.<br>For the unity of the country discipline among the people is called__________</p>",
                    question_hi: "<p>85.&nbsp;Select the most appropriate option to fill in the blank.<br>For the unity of the country discipline among the people is called__________</p>",
                    options_en: [
                        "<p>forth</p>",
                        "<p>for</p>",
                        "<p>out</p>",
                        "<p>None</p>"
                    ],
                    options_hi: [
                        "<p>forth</p>",
                        "<p>for</p>",
                        "<p>out</p>",
                        "<p>None</p>"
                    ],
                    solution_en: "<p>85.(b) for<br>&lsquo;Called for&rsquo; means required or demanded. The given sentence states that discipline among the people is called for (required) for the unity of the country. Hence, &lsquo;for&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>85.(b) for<br>\'Called for\' का अर्थ है required या demanded है। दिए गए वाक्य में कहा गया है कि देश की एकता के लिए लोगों में अनुशासन आवश्यक (required) है। इसलिए, \'for\' सबसे उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "86",
                    section: "7",
                    question_en: "<p>86. Select the sentence that uses the given idiom correctly.<br>Skating on thin ice</p>",
                    question_hi: "<p>86. Select the sentence that uses the given idiom correctly.<br>Skating on thin ice</p>",
                    options_en: [
                        "<p>He was on thin ice after making a controversial statement.</p>",
                        "<p>They were on thin ice due to the icy conditions on the road.</p>",
                        "<p>They were on thin ice when they skated on the frozen lake.</p>",
                        "<p>She was on thin ice while trying to balance on a tightrope.</p>"
                    ],
                    options_hi: [
                        "<p>He was on thin ice after making a controversial statement.</p>",
                        "<p>They were on thin ice due to the icy conditions on the road.</p>",
                        "<p>They were on thin ice when they skated on the frozen lake.</p>",
                        "<p>She was on thin ice while trying to balance on a tightrope.</p>"
                    ],
                    solution_en: "<p>86.(a) He was on thin ice after making a controversial statement.<br>Skating on thin ice - doing something risky which may have serious consequences.</p>",
                    solution_hi: "<p>86.(a) He was on thin ice after making a controversial statement.<br>Skating on thin ice - doing something risky which may have serious consequences./कोई जोखिम भरा काम करना जिसके गंभीर परिणाम हो सकते हैं।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "87",
                    section: "7",
                    question_en: "<p>87. Choose the best alternative which best expresses the meaning of the idiom/ phrase given below as underlined in the sentence.<br>Despite the highs and lows, life tends to <span style=\"text-decoration: underline;\"><strong>average itself out.</strong></span></p>",
                    question_hi: "<p>87. Choose the best alternative which best expresses the meaning of the idiom/ phrase given below as underlined in the sentence.<br>Despite the highs and lows, life tends to <span style=\"text-decoration: underline;\"><strong>average itself out.</strong></span></p>",
                    options_en: [
                        "<p>balance itself</p>",
                        "<p>be unvarying</p>",
                        "<p>show equal value</p>",
                        "<p>get compatible</p>"
                    ],
                    options_hi: [
                        "<p>balance itself</p>",
                        "<p>be unvarying</p>",
                        "<p>show equal value</p>",
                        "<p>get compatible</p>"
                    ],
                    solution_en: "<p>87.(a) balance itself</p>",
                    solution_hi: "<p>87.(a) balance itself./ खुद को संयमित करना</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "88",
                    section: "7",
                    question_en: "<p>88. Select the word which means the same as the group of words given.<br>A society or company provides these convenient features</p>",
                    question_hi: "<p>88. Select the word which means the same as the group of words given.<br>A society or company provides these convenient features</p>",
                    options_en: [
                        "<p>Equipment</p>",
                        "<p>Facilitate</p>",
                        "<p>Amenities</p>",
                        "<p>System</p>"
                    ],
                    options_hi: [
                        "<p>Equipment</p>",
                        "<p>Facilitate</p>",
                        "<p>Amenities</p>",
                        "<p>System</p>"
                    ],
                    solution_en: "<p>88.(c) <strong>Amenities-</strong> a society or company provides these convenient features.<br><strong>Equipment- </strong>the set of necessary tools.<br><strong>Facilitate- </strong>to make something possible or easier.<br><strong>System-</strong> a set of things working together as parts of a mechanism or an interconnecting network.</p>",
                    solution_hi: "<p>88.(c) <strong>Amenities</strong> (सुख-सुविधाएँ) - a society or company provides these convenient features.<br><strong>Equipment </strong>(उपकरण) - the set of necessary tools.<br><strong>Facilitate </strong>(सुविधाजनक बनाना) - to make something possible or easier.<br><strong>System </strong>(व्यवस्था) - a set of things working together as parts of a mechanism or an interconnecting network.</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "89",
                    section: "7",
                    question_en: "<p>89. Select the most appropriate ANTONYM of the underlined word.<br>The government is planning to <span style=\"text-decoration: underline;\">abolish</span> child care leave for female employees.</p>",
                    question_hi: "<p>89. Select the most appropriate ANTONYM of the underlined word.<br>The government is planning to <span style=\"text-decoration: underline;\">abolish</span> child care leave for female employees.</p>",
                    options_en: [
                        "<p>Ensure</p>",
                        "<p>Eradicate</p>",
                        "<p>Encourage</p>",
                        "<p>Establish</p>"
                    ],
                    options_hi: [
                        "<p>Ensure</p>",
                        "<p>Eradicate</p>",
                        "<p>Encourage</p>",
                        "<p>Establish</p>"
                    ],
                    solution_en: "<p>89.(d) <strong>Establish-</strong> to begin something.<br><strong>Abolish-</strong> to end a law or system officially.<br><strong>Ensure-</strong> to make something certain to happen.<br><strong>Eradicate-</strong> to get rid of something completely.<br><strong>Encourage-</strong> to give support, confidence, or motivation to someone.</p>",
                    solution_hi: "<p>89.(d) <strong>Establish</strong> (स्थापित करना) - to begin something.<br><strong>Abolish</strong> (समाप्त करना) - to end a law or system officially.<br><strong>Ensure </strong>(सुनिश्चित करना) - to make something certain to happen.<br><strong>Eradicate </strong>(जड़ से मिटाना) - to get rid of something completely.<br><strong>Encourage</strong> (प्रोत्साहित करना) - to give support, confidence, or motivation to someone.</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "90",
                    section: "7",
                    question_en: "<p>90. Find a word that is the synonym of- <br>LUXURIANT</p>",
                    question_hi: "<p>90. Find a word that is the synonym of- <br>LUXURIANT</p>",
                    options_en: [
                        "<p>Luxury-loving</p>",
                        "<p>Lovely</p>",
                        "<p>Rich</p>",
                        "<p>Abundant</p>"
                    ],
                    options_hi: [
                        "<p>Luxury-loving</p>",
                        "<p>Lovely</p>",
                        "<p>Rich</p>",
                        "<p>Abundant</p>"
                    ],
                    solution_en: "<p>90.(d) Abundant.<br><strong>Luxuriant-</strong> rich and profuse in growth; lush.<br><strong>Abundant- </strong>existing in very large quantities.<br><strong>Luxury-loving- </strong>fond of luxury or self-indulgence luxurious tastes luxurious feeling.<br><strong>Lovely-</strong> beautiful or attractive</p>",
                    solution_hi: "<p>90.(d) Abundant.<br><strong>Luxuriant - </strong>प्रचुर मात्रा में विकास, वनस्पति के रूप में।<br><strong>Abundant- </strong>बहुत अधिक मात्रा में विद्यमान है<br><strong>Luxury-</strong>ऐशो आराम, विशेष रूप से महंगी और सुंदर चीजों द्वारा प्रदान किया गया<br><strong>Lovely-</strong> सुंदर या आकर्षक</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "91",
                    section: "7",
                    question_en: "<p>91. Given below are four jumbled sentences. Pick the option that gives their correct order.<br>(A) Even their livelihood and their patterns of social interaction greatly depended on the climatic factors.<br>(B) So the kind of food they ate or the clothes they wore, were all determined by the physical and climatic conditions of the desert.&nbsp;<br>(C) People living in a desert environment were unable to practise settled agriculture<br>(D) Settled agriculture was possible in the plains, near rivers and so on but not in a desert.</p>",
                    question_hi: "<p>91. Given below are four jumbled sentences. Pick the option that gives their correct order.<br>(A) Even their livelihood and their patterns of social interaction greatly depended on the climatic factors.<br>(B) So the kind of food they ate or the clothes they wore, were all determined by the physical and climatic conditions of the desert.&nbsp;<br>(C) People living in a desert environment were unable to practise settled agriculture<br>(D) Settled agriculture was possible in the plains, near rivers and so on but not in a desert.</p>",
                    options_en: [
                        "<p>ACDB</p>",
                        "<p>CADB</p>",
                        "<p>CDBA</p>",
                        "<p>BCDA</p>"
                    ],
                    options_hi: [
                        "<p>ACDB</p>",
                        "<p>CADB</p>",
                        "<p>CDBA</p>",
                        "<p>BCDA</p>"
                    ],
                    solution_en: "<p>91.(c) CDBA&nbsp;<br>Sentence C will be the starting line as it contains the main idea of the parajumble i.e. &ldquo;people living in a desert environment&rdquo;. Sentence D states that settled agriculture was possible in the plains, near rivers and so on but not in a desert . So, D will follow C. Further, Sentence B states that the kind of food they ate or the clothes they wore, were all determined by the physical and climatic conditions of the desert and Sentence A states that their livelihood and their patterns of social interaction also depended on climatic factors . So, A will follow B. Going through the options, option (c) CDBA has the correct sequence.</p>",
                    solution_hi: "<p>91.(c) CDBA&nbsp;<br>Sentence C starting line होगी क्योंकि इसमें parajumble का मुख्य विचार &ldquo;people living in a desert environment&rdquo; शामिल है। Sentence D में कहा गया है कि मैदानी इलाकों में, नदियों के पास आदि में स्थिर कृषि संभव थी, लेकिन रेगिस्तान में नहीं। तो, C के बाद Dआएगा । आगे, Sentence B बताता है कि जिस तरह का भोजन उन्होंने खाया या जो कपड़े उन्होंने पहने थे, वे सभी रेगिस्तान की भौतिक और जलवायु परिस्थितियों द्वारा निर्धारित किए गए थे और Sentence A में कहा गया है कि उनकी आजीविका और सामाजिक संपर्क के पैटर्न जलवायु कारकों पर निर्भर करते हैं । इसलिए, B के बाद A आएगा । options के माध्यम से जाने पर, option (c) CDBA में सही sequence है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "92",
                    section: "7",
                    question_en: "<p>92. Pick a word opposite in meaning to&nbsp;Desecration</p>",
                    question_hi: "<p>92. Pick a word opposite in meaning to&nbsp;Desecration</p>",
                    options_en: [
                        "<p>Consecration</p>",
                        "<p>Discouragement</p>",
                        "<p>Despondency</p>",
                        "<p>Expectation</p>"
                    ],
                    options_hi: [
                        "<p>Consecration</p>",
                        "<p>Discouragement</p>",
                        "<p>Despondency</p>",
                        "<p>Expectation</p>"
                    ],
                    solution_en: "<p>92.(a) <strong>Consecration -</strong> to make or declare sacred.<br><strong>Desecration - </strong>to damage or show no respect towards something holy or very much respected.<br><strong>Discouragement -</strong> a loss of confidence or enthusiasm.<br><strong>Despondency -</strong> low spirits from loss of hope or courage; dejection.<br><strong>Expectation -</strong> a strong belief that something will happen or be the case.</p>",
                    solution_hi: "<p>92.(a) <strong>Consecration </strong>का अर्थ है- पवित्र बनाना या घोषित करना ।<br><strong>Desecration </strong>अर्थ है - किसी पवित्र या बहुत सम्मानित वस्तु को नुकसान पहुँचाना या उसके प्रति कोई सम्मान न दिखाना।<br><strong>Discouragement </strong>का अर्थ है - आत्मविश्वास या उत्साह की कमी।<br><strong>Despondency </strong>का अर्थ है - आशा या साहस की कमी; निराशा।<br><strong>Expectation </strong>का अर्थ है - विश्वास कि कुछ होगा।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "93",
                    section: "7",
                    question_en: "<p>93. Select the most appropriate option to fill in the blank. <br>Very carefully I ______ the tree I have referred to, and avoiding contact with the creepers, the upper _______ and leaves of which might have been visible from where the tiger was lying, I climbed to the fork, where I had a comfortable seat and perfect _________.</p>",
                    question_hi: "<p>93. Select the most appropriate option to fill in the blank. <br>Very carefully I ______ the tree I have referred to, and avoiding contact with the creepers, the upper _______ and leaves of which might have been visible from where the tiger was lying, I climbed to the fork, where I had a comfortable seat and perfect _________.</p>",
                    options_en: [
                        "<p>stalled, roots, visibility</p>",
                        "<p>starved, stem, disclosure</p>",
                        "<p>stormed, fruits, revelation</p>",
                        "<p>stalked, tendrils, concealment</p>"
                    ],
                    options_hi: [
                        "<p>stalled, roots, visibility</p>",
                        "<p>starved, stem, disclosure</p>",
                        "<p>stormed, fruits, revelation</p>",
                        "<p>stalked, tendrils, concealment</p>"
                    ],
                    solution_en: "<p>93.(d) The words written in option d will perfectly fit in the blanks according to the grammatical context of the sentence. <br><strong>Stalk -</strong> to follow someone<br><strong>Concealment -</strong> hide<br><strong>Tendrils - </strong>a long thin part that grows from a climbing plant</p>",
                    solution_hi: "<p>93.(d) Option (d) में लिखे गए शब्द sentence के grammatical संदर्भ के अनुसार रिक्त स्थान में पूरी तरह से फिट होंगे।<br><strong>Stalk - </strong>पीछा करना<br><strong>Concealment -</strong> आड़<br><strong>Tendrils - </strong>लता का वह हिस्सा जिसके सहारे वह दीवार आदि पर चढ़ती है</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "94",
                    section: "7",
                    question_en: "<p>94. Identify the best way to improve the underlined part of the given sentence. If there is no improvement required, select &lsquo;no improvement&rsquo;.<br>The affection of her husband <span style=\"text-decoration: underline;\"><strong>carried her up</strong></span> in the midst of all her problems.</p>",
                    question_hi: "<p>94. Identify the best way to improve the underlined part of the given sentence. If there is no improvement required, select &lsquo;no improvement&rsquo;.<br>The affection of her husband <span style=\"text-decoration: underline;\"><strong>carried her up</strong></span> in the midst of all her problems.</p>",
                    options_en: [
                        "<p>tore her up</p>",
                        "<p>bore her up</p>",
                        "<p>stood her up</p>",
                        "<p>drove her up</p>"
                    ],
                    options_hi: [
                        "<p>tore her up</p>",
                        "<p>bore her up</p>",
                        "<p>stood her up</p>",
                        "<p>drove her up</p>"
                    ],
                    solution_en: "<p>94.(b) &lsquo;Bore up&rsquo; means to help one to endure or persevere through emotional distress. The given sentence states that the affection of her husband helped her to endure the emotional distress in the midst of all her problems. Hence, &lsquo;bore her up&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>94.(b) &lsquo;Bore up&rsquo; का अर्थ to help one to endure or persevere through emotional distress है। दिया गया वाक्य the affection of her husband helped her to endure the emotional distress in the midst of all her problems है। इसलिए, &lsquo;bore her up&rsquo; सबसे उपयुक्त है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "95",
                    section: "7",
                    question_en: "<p>95. Select the most appropriate homonyms from the given alternatives to fill in the blanks.<br>He ________ imitate the playful movement of the ________.<br><strong>Alternatives:</strong><br>A.does (female deer)<br>B.does<br>C.dose</p>",
                    question_hi: "<p>95. Select the most appropriate homonyms from the given alternatives to fill in the blanks.<br>He ________ imitate the playful movement of the ________.<br><strong>Alternatives:</strong><br>A.does (female deer)<br>B.does<br>C.dose</p>",
                    options_en: [
                        "<p>C, B</p>",
                        "<p>B, A</p>",
                        "<p>A, B</p>",
                        "<p>A, C</p>"
                    ],
                    options_hi: [
                        "<p>C, B</p>",
                        "<p>B, A</p>",
                        "<p>A, B</p>",
                        "<p>A, C</p>"
                    ],
                    solution_en: "<p>95.(b) <strong>does, does(female deer)</strong><br>&lsquo;Does&rsquo; is the helping verb of simple present tense and &lsquo;does&rsquo; is the plural form of female deer. The given sentence states that he does imitate the playful movement of the does. Hence, option (b) is the most appropriate answer.</p>",
                    solution_hi: "<p>95.(b)<strong> does, does(female deer)</strong><br>&lsquo;Does&rsquo;, simple present tense का helping verb है और &lsquo;does&rsquo; female deer का plural form है। दिए गए sentence में बताया गया है कि वह हिरणियों (does) की चंचल हरकतों की नकल करता है। अतः, option (b) सबसे उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "96",
                    section: "7",
                    question_en: "<p>96. <strong>Cloze test</strong> <br>&ldquo;My new machine is so (96) ______ that it will even suck out ground-in dust and dirt (97) _____________ this\". As he spoke, the salesman used his (98)____________ to press the soot and dust into the carpet. The old lady (99)_________ again, but without (100)_________ success as the young man had quickly gone out of the front door.<br>Select the most appropriate option to fill in the blank no. 96</p>",
                    question_hi: "<p>96. <strong>Cloze test</strong> <br>&ldquo;My new machine is so (96) ______ that it will even suck out ground-in dust and dirt (97) _____________ this\". As he spoke, the salesman used his (98)____________ to press the soot and dust into the carpet. The old lady (99)_________ again, but without (100)_________ success as the young man had quickly gone out of the front door.<br>Select the most appropriate option to fill in the blank no. 96</p>",
                    options_en: [
                        "<p>expensive</p>",
                        "<p>strong&nbsp;&nbsp;</p>",
                        "<p>organized&nbsp;</p>",
                        "<p>effective</p>"
                    ],
                    options_hi: [
                        "<p>expensive</p>",
                        "<p>strong&nbsp;&nbsp;</p>",
                        "<p>organized&nbsp;</p>",
                        "<p>effective</p>"
                    ],
                    solution_en: "<p>96.(d) <strong>effective</strong><br>&lsquo;Effective&rsquo; means successfully producing the result that you want. The given passage talks about an effective machine. Hence, &lsquo;effective&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>96.(d) <strong>effective</strong><br>&lsquo;Effective&rsquo; इसका अर्थ है कि आप जो परिणाम चाहते हैं उसे सफलतापूर्वक तैयार करना। दिया गया passage एक प्रभावी मशीन (effective machine) के बारे में बात करता है। इसलिए, &lsquo;Effective&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "97",
                    section: "7",
                    question_en: "<p>97. <strong>Cloze test :</strong><br>&ldquo;My new machine is so (96) ______ that it will even suck out ground-in dust and dirt (97) _____________ this\". As he spoke, the salesman used his (98)____________ to press the soot and dust into the carpet. The old lady (99)_________ again, but without (100)_________ success as the young man had quickly gone out of the front door.<br>Select the most appropriate option to fill in the blank no.97</p>",
                    question_hi: "<p>97. <strong>Cloze test :</strong><br>&ldquo;My new machine is so (96) ______ that it will even suck out ground-in dust and dirt (97) _____________ this\". As he spoke, the salesman used his (98)____________ to press the soot and dust into the carpet. The old lady (99)_________ again, but without (100)_________ success as the young man had quickly gone out of the front door.<br>Select the most appropriate option to fill in the blank no.97</p>",
                    options_en: [
                        "<p>like</p>",
                        "<p>such&nbsp;</p>",
                        "<p>as&nbsp;</p>",
                        "<p>similar</p>"
                    ],
                    options_hi: [
                        "<p>like</p>",
                        "<p>such&nbsp;</p>",
                        "<p>as&nbsp;</p>",
                        "<p>similar</p>"
                    ],
                    solution_en: "<p>97.(a) like<br>&lsquo;Like&rsquo; means something similar to somebody/something. The given passage states that the machine can even suck out ground-in dust and dirt like this. Hence, &lsquo;like&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>97.(a) like<br>&lsquo;Like&rsquo; - किसी के समान। दिया गया passage बताता है कि मशीन इस तरह जमीन में जमी धूल और गंदगी को भी सोख सकती है। इसलिए, &lsquo;Like&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "98",
                    section: "7",
                    question_en: "<p>98. <strong>Cloze test :</strong><br>&ldquo;My new machine is so (96) ______ that it will even suck out ground-in dust and dirt (97) _____________ this\". As he spoke, the salesman used his (98)____________ to press the soot and dust into the carpet. The old lady (99)_________ again, but without (100)_________ success as the young man had quickly gone out of the front door.<br>Select the most appropriate option to fill in the blank no.98</p>",
                    question_hi: "<p>98. <strong>Cloze test :</strong><br>&ldquo;My new machine is so (96) ______ that it will even suck out ground-in dust and dirt (97) _____________ this\". As he spoke, the salesman used his (98)____________ to press the soot and dust into the carpet. The old lady (99)_________ again, but without (100)_________ success as the young man had quickly gone out of the front door.<br>Select the most appropriate option to fill in the blank no.98</p>",
                    options_en: [
                        "<p>heel</p>",
                        "<p>ankle</p>",
                        "<p>knee</p>",
                        "<p>shoulder</p>"
                    ],
                    options_hi: [
                        "<p>heel</p>",
                        "<p>ankle</p>",
                        "<p>knee</p>",
                        "<p>shoulder</p>"
                    ],
                    solution_en: "<p>98.(a) heel <br>&lsquo;Heel&rsquo; means the back part of your foot. The given passage states that the salesman used the back part of his foot to press the soot and dust into the carpet. Hence, &lsquo;heel&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>98.(a) heel <br>&lsquo;Heel&rsquo; का अर्थ है आपके पैर का पिछला भाग। दिए गए passage में कहा गया है कि salesman ने अपने पैर के पिछले हिस्से का इस्तेमाल कालीन में कालिख और धूल को झाड़ने के लिए किया। इसलिए, &lsquo;heel&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "99",
                    section: "7",
                    question_en: "<p>99. <strong>Cloze test :</strong><br>&ldquo;My new machine is so (96) ______ that it will even suck out ground-in dust and dirt (97) _____________ this\". As he spoke, the salesman used his (98)____________ to press the soot and dust into the carpet. The old lady (99)_________ again, but without (100)_________ success as the young man had quickly gone out of the front door.<br>Select the most appropriate option to fill in the blank no.99</p>",
                    question_hi: "<p>99<strong>. Cloze test :</strong><br>&ldquo;My new machine is so (96) ______ that it will even suck out ground-in dust and dirt (97) _____________ this\". As he spoke, the salesman used his (98)____________ to press the soot and dust into the carpet. The old lady (99)_________ again, but without (100)_________ success as the young man had quickly gone out of the front door.<br>Select the most appropriate option to fill in the blank no.99</p>",
                    options_en: [
                        "<p>questioned</p>",
                        "<p>spoke&nbsp;</p>",
                        "<p>tried&nbsp;</p>",
                        "<p>stated</p>"
                    ],
                    options_hi: [
                        "<p>questioned</p>",
                        "<p>spoke&nbsp;</p>",
                        "<p>tried&nbsp;</p>",
                        "<p>stated</p>"
                    ],
                    solution_en: "<p>99.(c) tried<br>&lsquo;Try&rsquo; means to do, use or test something in order to see how good or successful it is. The given passage states that the old lady tested the machine again. Hence, &lsquo;tried&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>99.(c) tried<br>&lsquo;Try&rsquo; - कोशिश करना, किसी चीज को करना, प्रयोग करना या परखना ताकि यह देखा जा सके कि वह कितनी अच्छी या सफल है। दिए गए passage में कहा गया है कि बुढ़िया ने फिर से मशीन का परीक्षण किया। इसलिए, &lsquo;tried&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "100",
                    section: "misc",
                    question_en: "<p>100. <strong>Cloze test :</strong><br>&ldquo;My new machine is so (96) ______ that it will even suck out ground-in dust and dirt (97) _____________ this\". As he spoke, the salesman used his (98)____________ to press the soot and dust into the carpet. The old lady (99)_________ again, but without (100)_________ success as the young man had quickly gone out of the front door.<br>Select the most appropriate option to fill in the blank no.100</p>",
                    question_hi: "<p>100.<strong> Cloze test</strong> <br>&ldquo;My new machine is so (96) ______ that it will even suck out ground-in dust and dirt (97) _____________ this\". As he spoke, the salesman used his (98)____________ to press the soot and dust into the carpet. The old lady (99)_________ again, but without (100)_________ success as the young man had quickly gone out of the front door.<br>Select the most appropriate option to fill in the blank no.100</p>",
                    options_en: [
                        "<p>much</p>",
                        "<p>some</p>",
                        "<p>no</p>",
                        "<p>little</p>"
                    ],
                    options_hi: [
                        "<p>much</p>",
                        "<p>some</p>",
                        "<p>no</p>",
                        "<p>little</p>"
                    ],
                    solution_en: "<p>100.(a) much<br>&lsquo;Much&rsquo; is used with uncountable nouns, mainly in negative sentences and questions. The given passage states that the old lady tested the machine again but without(negative sense) much success. Hence, &lsquo;much&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>100.(a) much<br>&lsquo;Much&rsquo; का प्रयोग uncountable nouns के साथ किया जाता है, मुख्यतः negative वाक्यों और questions में। कि बुढ़िया ने फिर से मशीन का परीक्षण किया लेकिन (negative sense) ज्यादा सफलता नहीं मिली। इसलिए, &lsquo;much&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'question-box d-flex align-items-center justify-content-center border rounded';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.question-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            const html = `
                <div class="card shadow-sm mb-4">
                    <div class="card-body">
                        <div class="question-text mb-4">
                            <div class="en">${question.question_en}</div>
                            <div class="hi" style="display:none">${question.question_hi}</div>
                        </div>
                        <div class="options">
                            ${['a', 'b', 'c', 'd'].map((opt, i) => `
                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="radio" name="q${index}" 
                                        id="opt-${index}-${opt}" value="${opt}" 
                                        ${answers[question.id] === opt ? 'checked' : ''}
                                        onclick="selectAnswer('${question.id}', '${opt}')">
                                    <label class="form-check-label w-100 py-2 px-3 rounded hover-bg-light" for="opt-${index}-${opt}">
                                        <div class="en">${question.options_en[i]}</div>
                                        <div class="hi" style="display:none">${question.options_hi[i]}</div>
                                    </label>
                                </div>
                            `).join('')}
                        </div>
                        ${submitted ? `
                            <div class="alert ${answers[question.id] === question.correct ? 'alert-success' : 'alert-danger'} mt-3">
                                ${answers[question.id] ? (answers[question.id] === question.correct ? 'Correct!' : 'Incorrect!') : 'Not attempted'}
                                <div class="mt-2">
                                    <strong>Solution:</strong>
                                    <div class="en">${question.solution_en}</div>
                                    <div class="hi" style="display:none">${question.solution_hi}</div>
                                </div>
                            </div>
                        ` : ''}
                    </div>
                </div>
            `;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // If in mobile view, close the menu
            if (window.innerWidth < 768) {
                toggleMenu(false);
            }
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                document.getElementById(`box-${index}`).classList.add('attempted');
            }
        }
        // Navigate to previous question
        function prevQuestion() {
            if (currentQuestion > 0) {
                showQuestion(currentQuestion - 1);
            }
        }
        // Navigate to next question
        function nextQuestion() {
            if (currentQuestion < questions.length - 1) {
                showQuestion(currentQuestion + 1);
            }
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) {
                showQuestion(section.start);
            }
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update button text
            document.getElementById('lang-btn').innerHTML = currentLang === 'en' ? 
                '<i class="fas fa-language"></i> हिंदी' : 
                '<i class="fas fa-language"></i> English';
        }
        // Toggle menu for mobile
        function toggleMenu(show) {
            const nav = document.getElementById('question-nav');
            const content = document.getElementById('question-content');
            if (show === undefined) {
                show = nav.style.right === '-100%' || nav.style.right === '';
            }
            if (show) {
                nav.style.right = '0';
                if (window.innerWidth >= 768) {
                    content.style.marginRight = '320px';
                }
            } else {
                nav.style.right = window.innerWidth < 768 ? '-100%' : '0';
                content.style.marginRight = window.innerWidth < 768 ? '0' : '320px';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
            document.getElementById('total-count').textContent = questions.length;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.remove('attempted');
                    box.classList.add('bg-secondary', 'text-white');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.remove('attempted');
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.remove('attempted');
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            resultsModal.show();
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check-circle me-1"></i> Submitted';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            resultsModal.hide();
            showQuestion(0);
        }
    </script>
</body>
</html>