<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">25:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 40</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">40</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 25 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["18"] = {
                name: "General Awareness",
                start: 0,
                end: 38
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="18">General Awareness</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 39,
                end: 39
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "18",
                    question_en: "1. What is the height of the geostationary orbit ?",
                    question_hi: "1. भूस्थिर कक्षा की ऊँचाई कितनी होती है?",
                    options_en: [" 22,150 km", " 33,638 km", 
                                " 42,164 km", " 35,786 km"],
                    options_hi: [" 22,150 km", " 33,638 km",
                                " 42,164 km", " 35,786 km"],
                    solution_en: "1.(d) The height of the geostationary orbit is 35,786 km. Geostationary orbit is  above Earth\'s Equator in which a satellite\'s orbital period is equal to Earth\'s rotation period of 23 hours and 56 minutes",
                    solution_hi: "1.(d) भूस्थिर कक्षा की ऊंचाई 35,786 किमी है। भूस्थैतिक कक्षा पृथ्वी की भूमध्य रेखा के ऊपर होती है, जिसमें एक उपग्रह की कक्षीय अवधि पृथ्वी के 23 घंटे 56 मिनट के घूर्णन काल के बराबर होती है।.",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "2",
                    section: "18",
                    question_en: "2. To which family does the ‘Touch-me-not‘ plant belong?",
                    question_hi: "2. \'टच-मी-नॉट\' पौधा किस परिवार से संबंधित है?",
                    options_en: [" Mimosaceae ", " Cyperaceae ", 
                                " Acanthaceae ", " Malvaceae "],
                    options_hi: [" मिमोसैसी", " साइपरेसी",
                                " अकैंथेसी", " मालवेसी"],
                    solution_en: "2.(a) The ‘Touch-me-not‘ plant belongs to Mimosaceae. Examples of Cyperaceae are cotton grass, spike-rush, sawgrass, nut grass. Examples of Acanthaceae are Justicia, Rueillia, clockvines, zebra plant etc, Examples of Malvaceae are Mallow, Hibiscus, Hollyhock, Durian, marsh mallow etc.",
                    solution_hi: "2.(a) \'टच-मी-नॉट\' पौधा मिमोसैसी का है। साइपरेसी के उदाहरण कपास घास, स्पाइक-रश, चूरा, अखरोट घास हैं। एकेंथेसी के उदाहरण हैं जस्टिसिया, रुइलिया, क्लॉकवाइन्स, ज़ेबरा प्लांट आदि, मालवेसी के उदाहरण हैं मल्लो, हिबिस्कस, होलीहॉक, ड्यूरियन, मार्श मैलो आदि।",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "3",
                    section: "18",
                    question_en: "3. What are the criteria to be considered to get Maharatna status?",
                    question_hi: "3. महारत्न का दर्जा पाने के लिए किन मानदंडों पर विचार करना चाहिए?",
                    options_en: [" Having positive net proﬁt to net worth ratio ", " Having signiﬁcant global presence/international operations ", 
                                " Having inter-sectoral performance ", " Having Miniratna category-l status "],
                    options_hi: [" सकारात्मक निवल लाभ से निवल मूल्य अनुपात", " महत्वपूर्ण वैश्विक उपस्थिति/अंतर्राष्ट्रीय संचालन होने के कारण",
                                " अंतर-क्षेत्रीय प्रदर्शन होना", " मिनीरत्न श्रेणी- I का दर्जा प्राप्त करना"],
                    solution_en: "3.(b) The criteria to be considered to get Maharatna status is having signiﬁcant global presence/international operations. There are a total of 10 Maharatna Companies in India. These include NTPC, ONGC, SAIL, BHEL, IOCL, HPCL, CIL, GAIL, BPCL and Power Grid Corporation of India.",
                    solution_hi: "3.(b) महारत्न का दर्जा पाने के लिए जिन मानदंडों पर विचार किया जाना है, वे महत्वपूर्ण वैश्विक उपस्थिति /अंतर्राष्ट्रीय संचालन हैं। भारत में कुल 10 महारत्न कंपनियां हैं। इनमें NTPC, ONGC, SAIL, BHEL, IOCL, HPCL, CIL, GAIL, BPCL और पावर ग्रिड कॉर्पोरेशन ऑफ इंडिया शामिल हैं।",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "4",
                    section: "18",
                    question_en: "4. Who won the Man Booker Prize in Nov 2020?",
                    question_hi: "4. नवंबर 2020 में मैन बुकर पुरस्कार किसने जीता?",
                    options_en: ["  Hilary Mantel", " Douglas Stuart ", 
                                " Anna Burns ", " Arundhati Roy  "],
                    options_hi: [" हिलेरी मेंटल", " डगलस स्टुअर्ट",
                                " अन्ना बर्न्स", " अरुंधति रॉय"],
                    solution_en: "4.(b) Douglas Stuart Won Man Booker Prize for \'Shuggie Bain’ in Nov 2020.",
                    solution_hi: "4.(b)  डगलस स्टुअर्ट ने नवंबर 2020 में \'शुगी बैन\' के लिए मैन बुकर पुरस्कार जीता।",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "5",
                    section: "18",
                    question_en: "5. Which state has the highest percentage of its geographical area covered by forests, as per IFSR, 2019?",
                    question_hi: "5. IFSR, 2019 के अनुसार, किस राज्य के भौगोलिक क्षेत्र का सबसे अधिक प्रतिशत वनों से आच्छादित है?",
                    options_en: [" Nagaland ", " Assam ", 
                                " Meghalaya ", " Mizoram <br /> "],
                    options_hi: [" नगालैंड", " असम",
                                " मेघालय", " मिजोरम"],
                    solution_en: "5.(d) Top Three states in forest cover areas are Mizoram (85.41%) , Arunachal Pradesh (79.63%) , Meghalaya (76.33%). as per IFSR, 2019.",
                    solution_hi: "5.(d) वन आवरण क्षेत्रों में शीर्ष तीन राज्य मिजोरम (85.41%), अरुणाचल प्रदेश (79.63%), मेघालय (76.33%) हैं। IFSR, 2019 के अनुसार।",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "6",
                    section: "18",
                    question_en: "<p>6. Which material was used to make beads in the Harappan Civilisation?</p>",
                    question_hi: "<p>6. हड़प्पा सभ्यता में मोतियों को बनाने के लिए किस सामग्री का उपयोग किया जाता था?</p>",
                    options_en: ["<p>Wood</p>", "<p>Plastic</p>", 
                                "<p>Limestone</p>", "<p>Carnelian stone</p>"],
                    options_hi: ["<p>लकड़ी</p>", "<p>प्लास्टिक</p>",
                                "<p>चूना पत्थर</p>", "<p>कारेलियन पत्थर</p>"],
                    solution_en: "<p>6.(d) Carnelian stone was used to make beads in the Harappan Civilisation. Carnelian stone is one of the luckiest good luck charms known to humans. Chemical formula SiO<sub>2</sub>.</p>",
                    solution_hi: "<p>6.(d) हड़प्पा सभ्यता में मोती बनाने के लिए कारेलियन पत्थर का उपयोग किया जाता था। कारेलियन पत्थर&nbsp;मनुष्यों के लिए ज्ञात सबसे भाग्यशाली सौभाग्य आकर्षणों में से एक है। रासायनिक सूत्र SiO<sub>2</sub> है।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "7",
                    section: "18",
                    question_en: "7. In 2018, which nuclear reactor did BARC re-commission with double capacity compared to its earlier version?",
                    question_hi: "7. 2018 में, BARC ने अपने पहले के संस्करण की तुलना में किस परमाणु रिएक्टर को दोगुनी क्षमता के साथ फिर से चालू किया?",
                    options_en: [" Agni ", " Brahmos ", 
                                " Apsara ", " Arihant "],
                    options_hi: [" अग्नि", " ब्रह्मोस",
                                " अप्सरा ", " अरिहंत"],
                    solution_en: "7.(c)  In 2018, Apsara nuclear reactor was re-commission with double capacity compared to its earlier version by BARC.",
                    solution_hi: "7.(c) 2018 में, BARC द्वारा अपने पुराने संस्करण की तुलना में अप्सरा परमाणु रिएक्टर को दोगुनी क्षमता के साथ फिर से चालू किया गया था।",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "8",
                    section: "18",
                    question_en: "8. In which city was the 23rd National Conference on e-Governance held?",
                    question_hi: "8. ई-गवर्नेंस पर 23वां राष्ट्रीय सम्मेलन किस शहर में आयोजित किया गया था?",
                    options_en: [" Mumbai ", " Lucknow ", 
                                " Gurgaon ", " New Delhi  "],
                    options_hi: [" मुंबई", " लखनऊ ",
                                " गुडगाँव ", " नई दिल्ली<br /> "],
                    solution_en: "8.(a) ई-गवर्नेंस पर 23वां राष्ट्रीय सम्मेलन मुंबई में आयोजित किया गया।",
                    solution_hi: "8.(a) The 23rd National Conference on e- Governance held in Mumbai.",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "9",
                    section: "18",
                    question_en: "9. Since 2008, the United Nations is running a program REDD to create awareness for the importance of carbon stored in the forests, reduction of carbon emissions and investment in low carbon development. What does REDD stand for ?",
                    question_hi: "9. 2008 से, संयुक्त राष्ट्र जंगलों में संग्रहीत कार्बन के महत्व, कार्बन उत्सर्जन में कमी और कम कार्बन विकास में निवेश के बारे में जागरूकता पैदा करने के लिए REDD कार्यक्रम चला रहा है। REDD का पूर्ण रूप क्या है?",
                    options_en: [" Recycling Energy from Deforestation and Forest Degradation ", " Reducing Emission from Deforestation and Forest Degradation ", 
                                " Reducing Emission from Disintegration and Forest Degradation ", " Renewable Energy  from Disintegration and Forest Degradation"],
                    options_hi: [" Recycling Energy from Deforestation and Forest Degradation ", " Reducing Emission from Deforestation and Forest Degradation ",
                                " Reducing Emission from Disintegration and Forest Degradation ", " Renewable Energy  from Disintegration and Forest Degradation"],
                    solution_en: "9.(b) Since 2008, the United Nations has been running a program REDD to create awareness for the importance of carbon stored in the forests, reduction of carbon emissions and investment in low carbon development.REDD stands for Reducing Emission from Deforestation and Forest Degradation. It is a framework created by the UNFCCC Conference of the Parties (COP).",
                    solution_hi: "9.(b) 2008 से, संयुक्त राष्ट्र वनों में संग्रहीत कार्बन के महत्व, कार्बन उत्सर्जन में कमी और निम्न कार्बन विकास में निवेश के बारे में जागरूकता पैदा करने के लिए REDD कार्यक्रम चला रहा है। REDD का अर्थ वनों की कटाई और वन क्षरण से उत्सर्जन को कम करना है। यह पार्टियों के UNFCCC सम्मेलन (COP) द्वारा बनाया गया एक ढांचा है।",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "10",
                    section: "18",
                    question_en: "10. Which polymer is used for the manufacturing of water pipes?",
                    question_hi: "10. पानी के पाइप के निर्माण के लिए किस बहुलक का उपयोग किया जाता है?",
                    options_en: [" Polystyrene ", " Bakelite ", 
                                " Glyptal ", " PVC "],
                    options_hi: [" इम्तिहान", "एक प्रकार का प्लास्टिक",
                                " ग्लाइप्टल", " पीवीसी"],
                    solution_en: "10.(d) Polyvinyl Chloride( PVC), is used for the manufacturing of water pipes. Polystyrene used in packaging, consumer electronics, Building and construction. Bakelite is used in automotive components and Industrial applications. Glyptal is used in paints. ",
                    solution_hi: "10.(d) पॉलीविनाइल क्लोराइड (PVC) का उपयोग पानी के पाइप के निर्माण के लिए किया जाता है। पॉलीस्टाइनिन का उपयोग पैकेजिंग, उपभोक्ता इलेक्ट्रॉनिक्स, भवन और निर्माण में किया जाता है। बैकलाइट का उपयोग ऑटोमोटिव घटकों और औद्योगिक अनुप्रयोगों में किया जाता है। ग्लाइप्टल का उपयोग पेंट में किया जाता है।<br /> ",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "11",
                    section: "18",
                    question_en: "11. In 1939, which Viceroy of India announced that India had entered the war along with Britain?",
                    question_hi: "11. 1939 में, भारत के किस वायसराय ने घोषणा की कि भारत ने ब्रिटेन के साथ युद्ध में प्रवेश किया है?",
                    options_en: [" Lord Wavell     ", " Lord Mountbatten ", 
                                " Lord Linlithgow ", " Lord Willingdon "],
                    options_hi: [" लॉर्ड वेवेल", " लॉर्ड माउंटबेटन",
                                " लॉर्ड लिनलिथगो", " लॉर्ड विलिंगडन"],
                    solution_en: "11.(c) In 1939, Lord Linlithgow, viceroy of India announced that India had entered the war along with Britain. ",
                    solution_hi: "11.(c) 1939 में, भारत के वायसराय लॉर्ड लिनलिथगो ने घोषणा की, कि भारत ने ब्रिटेन के साथ युद्ध में प्रवेश किया है।",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "12",
                    section: "18",
                    question_en: "<p>12. What is the common name of the compound <math display=\"inline\"><msub><mrow><mi>N</mi><mi>a</mi><mi>H</mi><mi>C</mi><mi>O</mi></mrow><mrow><mn>3</mn></mrow></msub></math>?</p>",
                    question_hi: "<p>12. <math display=\"inline\"><msub><mrow><mi>N</mi><mi>a</mi><mi>H</mi><mi>C</mi><mi>O</mi></mrow><mrow><mn>3</mn></mrow></msub></math>का सामान्य नाम क्या है ?</p>",
                    options_en: ["<p>Washing Soda</p>", "<p>Bleaching powder</p>", 
                                "<p>Caustic Soda</p>", "<p>Baking Soda</p>"],
                    options_hi: ["<p>धावन सोडा</p>", "<p>ब्लीचिंग पाउडर</p>",
                                "<p>कास्टिक सोडा</p>", "<p>बेकिंग सोडा</p>"],
                    solution_en: "<p>12.(d) The common name of the compound NaHCO<sub>3</sub> is Baking Soda. Washing Soda(Na<sub>2</sub>CO<sub>3</sub>), Bleaching Powder,Ca(ClO)<sub>2</sub>, Caustic Soda (NaOH).</p>",
                    solution_hi: "<p>12.(d) यौगिक NaHCO<sub>3</sub> का सामान्य नाम बेकिंग सोडा है। वाशिंग सोडा (Na<sub>2</sub>CO<sub>3</sub>), ब्लीचिंग पाउडर, Ca(ClO)<sub>2</sub>, कास्टिक सोडा (NaOH)</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "13",
                    section: "18",
                    question_en: "13. Which of the following was a para-military force of the Nizam of Hyderabad?",
                    question_hi: "13. निम्नलिखित में से कौन हैदराबाद के निजाम का अर्धसैनिक बल था?",
                    options_en: [" Hizbul ", " Mujahideen  ", 
                                " Ansars ", " Razakars <br /> "],
                    options_hi: [" हिजबुल", " मुजाहिदीन",
                                " अंसार", " रजाकार"],
                    solution_en: "13.(d) Razakars  was a para-military force of the Nizam of Hyderabad. Razakar means Volunteer.",
                    solution_hi: "13.(d) रजाकार हैदराबाद के निजाम का अर्धसैनिक बल था। रजाकर का अर्थ है स्वयंसेवी।",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "14",
                    section: "18",
                    question_en: "14. Who got the purple cap for taking the maximum number of wickets in IPL 13 session?",
                    question_hi: "14. IPL 13 सत्र में सर्वाधिक विकेट लेने के लिए बैंगनी टोपी किसने प्राप्त की?",
                    options_en: [" Kagiso Rabada ", " Andrew Tye ", 
                                " Dwayne Bravo ", " Bhuvneshwar Kumar "],
                    options_hi: [" कगिसो रबाडा ", " एंड्रयू टाय",
                                " ड्वेन ब्रावो", " भुवनेश्वर कुमार<br /> "],
                    solution_en: "14.(a) Kagiso Rabada got the purple cap for taking the maximum number of wickets in IPL 13. Harshal Patel won the purple cap in the edition of IPL 14.",
                    solution_hi: "14.(a)  IPL, 13 में सबसे अधिक विकेट लेने के लिए कगिसो रबाडा को बैंगनी टोपी मिली। हर्षल पटेल ने  IPL  14 के संस्करण में बैंगनी टोपी  जीती।<br /> ",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "15",
                    section: "18",
                    question_en: "15. In which of the following states is the Mathuri folk dance practiced?",
                    question_hi: "15. निम्नलिखित में से किस राज्य में माथुरी लोक नृत्य का अभ्यास किया जाता है?",
                    options_en: [" Odisha ", " Meghalaya ", 
                                " Telangana  ", " Nagaland "],
                    options_hi: [" उड़ीसा ", " मेघालय",
                                " तेलंगाना", " नगालैंड "],
                    solution_en: "15.(c) Mathuri folk dance is practiced in Telangana.The Mathuri dance was performed by two Kuchipudi dancers, a Kathak dancer and a Bharatanatyam dancer. ",
                    solution_hi: "15.(c) तेलंगाना में माथुरी लोक नृत्य का अभ्यास किया जाता है। माथुरी नृत्य दो कुचिपुड़ी नर्तकियों, एक कथक नर्तक और एक भरतनाट्यम नर्तक द्वारा किया गया था।",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "16",
                    section: "18",
                    question_en: "16. Who among the following flight lieutenants is the woman pilot of the IAF to qualify to undertake combat missions on a fighter jet?",
                    question_hi: "16. निम्नलिखित में से कौन फ्लाइट लेफ्टिनेंट भारतीय वायुसेना की महिला पायलट है जो एक लड़ाकू जेट पर लड़ाकू मिशन करने के लिए अर्हता प्राप्त करती है?",
                    options_en: [" Kalpana Chawla  ", " Bhawana Kanth  ", 
                                " Gunjan Saxena   ", " Sunita Williams "],
                    options_hi: [" कल्पना चावला", " भावना कान्त ",
                                " गुंजन सक्सेना   ", " सुनीता विलियम्स"],
                    solution_en: "16.(b) Bhawana Kanth is the woman pilot of the IAF to qualify to undertake a combat mission on a fighter jet.",
                    solution_hi: " 16.(b) भावना  कान्त  भारतीय वायुसेना की महिला पायलट हैं, जिन्होंने लड़ाकू जेट पर एक लड़ाकू मिशन शुरू करने के लिए अर्हता प्राप्त की है।",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "17",
                    section: "18",
                    question_en: "17. Who won the Wildlife Photographer of the Year 2018 award in the 10 years and under category?",
                    question_hi: "17. 10 साल और उससे कम श्रेणी में वाइल्डलाइफ़ फ़ोटोग्राफ़र ऑफ़ द ईयर 2018 का पुरस्कार किसने जीता?",
                    options_en: [" Randeep Singh", " Arshdeep Singh ", 
                                "Tanishq Abraham", " Aadithyan Rajesh  "],
                    options_hi: [" रणदीप सिंह", " अर्शदीप सिंह",
                                " तनिष्क अब्राहम", " आदित्यन राजेश"],
                    solution_en: "17.(b)  Arshdeep Singh won the Wildlife Photographer of the Year 2018 award in the 10 years and under category.  Vidyun R Hebbar won the Wildlife Photographer of the Year 2018 award in the 10 years and under category.",
                    solution_hi: "17.(b) अर्शदीप सिंह ने 10 साल और उससे कम श्रेणी में वाइल्डलाइफ़ फ़ोटोग्राफ़र ऑफ़ द ईयर 2018 का पुरस्कार जीता। विद्युत आर हेब्बार ने 10 साल और उससे कम श्रेणी में वाइल्डलाइफ़ फ़ोटोग्राफ़र ऑफ़ द ईयर 2018 का पुरस्कार जीता। ",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "18",
                    section: "18",
                    question_en: "18. What is the range of the supersonic Akash missile?",
                    question_hi: "18. सुपरसोनिक आकाश मिसाइल की सीमा क्या है?",
                    options_en: [" Around 25 Km ", " Around 20 Km ", 
                                " Around 35 Km ", " Around 30 Km  "],
                    options_hi: [" लगभग 25 Km", " लगभग 20 Km",
                                " लगभग 35 km", " लगभग 30 km"],
                    solution_en: "18.(a) The supersonic Akash missiles have an operational range of 25-27 km and a flight altitude of around 18 km.",
                    solution_hi: "18.(a) सुपरसोनिक आकाश मिसाइलों की परिचालन सीमा 25-27 किमी और उड़ान की ऊंचाई लगभग 18 किमी है।",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "19",
                    section: "18",
                    question_en: "19. What is the IUPAC name of glycerol?",
                    question_hi: "19. ग्लिसरॉल का IUPAC नाम क्या है?",
                    options_en: [" Ethane-1,2 diol ", " 2-Methy;propan -1-ol ", 
                                " Propan-1-ol ", " Propane-1,2,3 -triol <br /> "],
                    options_hi: [" ईथेन-1,2 डायोल", " 2-मेथी; प्रोपेन -1-ओएल",
                                " प्रोपेन-1-ओएल", " प्रोपेन-1,2,3 -ट्राईओल"],
                    solution_en: "19.(d) The IUPAC name of glycerol is Propane-1,2,3- triol. Glycerol is most commonly used for constipation, improving hydration and performance in athletes, and for certain skin conditions.",
                    solution_hi: "19.(d) ग्लिसरॉल का IUPAC नाम प्रोपेन-1,2,3-ट्रायल है। ग्लिसरॉल का उपयोग आमतौर पर कब्ज, एथलीटों में जलयोजन और प्रदर्शन में सुधार और त्वचा की कुछ स्थितियों के लिए किया जाता है।",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "20",
                    section: "18",
                    question_en: "20. In a human body, salts are transported by:",
                    question_hi: "20. मानव शरीर में लवणों का परिवहन किसके द्वारा होता है ?",
                    options_en: [" Plasma ", " Oxygen  ", 
                                " Hemoglobin  ", " Blood "],
                    options_hi: [" प्लाज्मा", " ऑक्सीजन",
                                " हीमोग्लोबिन", " खून<br /> "],
                    solution_en: "20.(d) In a human body, salts are transported by Blood. Plasma is responsible for transporting food, carbon dioxide, and nitrogenous wastes in dissolved form. ",
                    solution_hi: "20.(d) मानव शरीर में लवणों का परिवहन रक्त द्वारा होता है। प्लाज्मा भोजन, कार्बन डाइऑक्साइड और नाइट्रोजनयुक्त अपशिष्टों को घुलित रूप में ले जाने के लिए जिम्मेदार है।",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "21",
                    section: "18",
                    question_en: "21. Real-time video chatting uses the following internet application:",
                    question_hi: "21. वास्तविक समय में वीडियो चैटिंग के इंटरनेट किस एप्लिकेशन का उपयोग करता  है?",
                    options_en: [" Internet Publish Chat (IPC) ", " Internet Broadcast Chat (IBC) ", 
                                " Internet Transfer Chat (ITC) ", " Internet Relay Chat (IRC)"],
                    options_hi: [" इंटरनेट पब्लिश चैट(IPC) ", " इंटरनेट ब्रॉडकास्ट चैट(IBC) ",
                                " इंटरनेट ट्रांसफर चैट(ITC) ", " इंटरनेट रिले चैट (IRC)"],
                    solution_en: " 21.(d)  Internet Relay Chat (IRC) is used for Real-time video chatting. IRC (Internet Relay Chat) is a protocol for real-time text messaging between internet-connected computers created in 1988.",
                    solution_hi: " 21.(d) इंटरनेट रिले चैट (IRC) का उपयोग रीयल टाइम वीडियो चैटिंग के लिए किया जाता है। IRC (इंटरनेट रिले चैट) 1988 में बनाए गए इंटरनेट से जुड़े कंप्यूटरों के बीच रीयल-टाइम टेक्स्ट मैसेजिंग के लिए एक प्रोटोकॉल है।",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "22",
                    section: "18",
                    question_en: "22. Which was the first Asian country in the world to enter Mars orbit in its first attempt?",
                    question_hi: "22. अपने पहले प्रयास में मंगल की कक्षा में प्रवेश करने वाला विश्व का पहला एशियाई देश कौन सा था?",
                    options_en: [" Philippines ", " China ", 
                                " India ", " Japan  <br />  "],
                    options_hi: [" फिलीपींस", " चीन",
                                " भारत ", " जापान"],
                    solution_en: "22.(c) The first Asian country in the world to enter Mars orbit in its first attempt is India. The Mars Orbiter Mission (MOM), commonly referred to as Mangalyaan-1, is a space probe launched by the Indian Space Research Organization (ISRO) on November 5, 2013.",
                    solution_hi: "22.(c)  अपने पहले प्रयास में मंगल की कक्षा में प्रवेश करने वाला दुनिया का पहला एशियाई देश भारत है। मार्स ऑर्बिटर मिशन (MOM), जिसे आमतौर पर मंगलयान -1 के रूप में जाना जाता है, भारतीय अंतरिक्ष अनुसंधान संगठन (ISRO) द्वारा 5 नवंबर, 2013 को शुरू की गई एक अंतरिक्ष जांच है।",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "23",
                    section: "18",
                    question_en: "23. India Joined the UNO in the year:",
                    question_hi: "23. भारत किस वर्ष में UNO में शामिल हुआ था ?",
                    options_en: [" 1950", " 1945", 
                                " 1952", " 1947"],
                    options_hi: [" 1950", " 1945",
                                " 1952", " 1947"],
                    solution_en: "23.(b) India is a founding member of the United Nations. On June 26, 1945, India was among 50 countries to sign the UN charter and joined the United Nations Organization.",
                    solution_hi: "23.(b) भारत संयुक्त राष्ट्र संघ का संस्थापक सदस्य है। 26 जून, 1945 को, भारत संयुक्त राष्ट्र चार्टर पर हस्ताक्षर करने वाले 50 देशों में शामिल हो गया और संयुक्त राष्ट्र संगठन में शामिल हो गया।",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "24",
                    section: "18",
                    question_en: "<p>24. According to the PBL Netherlands Environment Assessment Agency report 2018, what was the rate of greenhouse gas(GHG) emissions in India?</p>",
                    question_hi: "<p>24. PBL नीदरलैंड पर्यावरण आकलन एजेंसी की रिपोर्ट 2018 के अनुसार, भारत में ग्रीनहाउस गैस (GHG) उत्सर्जन की दर क्या थी?</p>",
                    options_en: ["<p>18%</p>", "<p>7%</p>", 
                                "<p>5%</p>", "<p>13%</p>"],
                    options_hi: ["<p>18%</p>", "<p>7%</p>",
                                "<p>5%</p>", "<p>13%</p>"],
                    solution_en: "<p>24.(b) According to the PBL Netherlands Environmental Assessment Agency report 2018, 7 percent was the rate of greenhouse gas(GHG) emissions in India</p>",
                    solution_hi: "<p>24.(b) PBL नीदरलैंड पर्यावरण आकलन एजेंसी रिपोर्ट 2018 के मुताबिक, 7 प्रतिशत भारत में ग्रीनहाउस गैस (GHG) उत्सर्जन की दर थी।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "25",
                    section: "18",
                    question_en: "25. At which of the following places did Gautam Buddha attain enlightenment ?",
                    question_hi: "25. निम्नलिखित में से किस स्थान पर गौतम बुद्ध को ज्ञान की प्राप्ति हुई थी?",
                    options_en: [" Sarnath ", " Pavapuri ", 
                                " Bodh Gaya ", " Kushinagar "],
                    options_hi: [" सारनाथ", " पावापुरी",
                                " बोध गया", " कुशीनगर"],
                    solution_en: "25.(c) Gautam Buddha attained enlightenment at Bodh Gaya. He was born in Lumbini, he died at Kushinagar.",
                    solution_hi: "25.(c) बोधगया में गौतम बुद्ध को ज्ञान की प्राप्ति हुई थी। उनका जन्म लुंबिनी में हुआ था, उनकी मृत्यु कुशीनगर में हुई थी।",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "26",
                    section: "18",
                    question_en: "26. Which state achieved the highest literacy through its literacy program ‘Athulyam’ in 2018?",
                    question_hi: "26. किस राज्य ने 2018 में अपने साक्षरता कार्यक्रम ‘अत्युल्म ’के माध्यम से उच्चतम साक्षरता प्राप्त की?",
                    options_en: [" Mizoram ", " Kerala ", 
                                " Bihar ", " Arunachal Pradesh "],
                    options_hi: [" मिजोरम", " केरल",
                                " बिहार", " अरुणाचल प्रदेश"],
                    solution_en: "26.(b) Kerala achieved the highest literacy through its literacy program  ‘Athulyam’ in 2018.",
                    solution_hi: "26.(b) केरल ने 2018 में अपने साक्षरता कार्यक्रम \'अत्युल्म\' के माध्यम से सर्वोच्च साक्षरता हासिल की।",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "27",
                    section: "18",
                    question_en: "27. Who designed the new Indian rupee symbol?",
                    question_hi: "27. भारतीय रुपये का नया प्रतीक चिन्ह किसने डिजाइन किया था?",
                    options_en: [" D Uday Kumar ", " Nandita Correa Mehrota ", 
                                " Udit Rajest Hitesh ", " Padmashali "],
                    options_hi: [" डी उदय कुमार", " नंदिता कोरिया मेहरोत्रा",
                                " उदित राजेस्ट हितेश", " पद्मशाली"],
                    solution_en: "27.(a) D Uday Kumar designed the new Indian rupee symbol in 2010.",
                    solution_hi: "27.(a) डी उदय कुमार ने 2010 में नए भारतीय रुपये के प्रतीक को डिजाइन किया था।",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "28",
                    section: "18",
                    question_en: "28. IRNSS - IG is the : ",
                    question_hi: "28. IRNSS - IG क्या है?",
                    options_en: [" third navigation satellite ", " seventh navigation satellite ", 
                                " tenth navigation satellite ", " first navigation satellite "],
                    options_hi: [" तीसरा नेविगेशन उपग्रह", " सातवां नेविगेशन उपग्रह",
                                " दसवां नेविगेशन उपग्रह", " पहला नेविगेशन उपग्रह"],
                    solution_en: "28.(b) IRNSS- IG is the seventh navigation satellite and final of the Indian Regional Navigation Satellite System (IRNSS) series of satellites.",
                    solution_hi: "28.(b) IRNSS- IG भारतीय क्षेत्रीय नेविगेशन सैटेलाइट सिस्टम (IRNSS) श्रृंखला के उपग्रहों का सातवां नेविगेशन उपग्रह और अंतिम है।",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "29",
                    section: "18",
                    question_en: "29. What is the 69th amendment to the Constitution of India ?",
                    question_hi: "29. भारत के संविधान में 69वां संशोधन क्या है?",
                    options_en: [" Introduced the Goods and Service Tax ", " Bodo, Dogri included as official languages ", 
                                " Delhi made National Capital Region and given special status ", " Voting age lowered from 21 to 18 years "],
                    options_hi: [" वस्तु एवं सेवा कर का प्रस्ताव", " बोडो, डोगरी को आधिकारिक भाषाओं के रूप में शामिल किया गया",
                                " दिल्ली को राष्ट्रीय राजधानी क्षेत्र बनाया और दिया विशेष दर्जा", " मतदान की उम्र 21 से घटाकर 18 साल की गई"],
                    solution_en: "29.(c) Delhi was made National Capital Region and was given special status in the 69th amendment Act 1992 to the constitution of India. It inserted Article 239AA.",
                    solution_hi: "29.(c) दिल्ली ने राष्ट्रीय राजधानी क्षेत्र बनाया और भारत के संविधान के 69वें संशोधन अधिनियम 1992 में विशेष दर्जा दिया। इसमें अनुच्छेद 239AA सम्मिलित किया गया।",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "30",
                    section: "18",
                    question_en: "30. Which of the following temples of India is called the Great Living Chola Temple?",
                    question_hi: "30. भारत के निम्नलिखित में से किस मंदिर को महान जीवित चोल मंदिर कहा जाता है?",
                    options_en: [" Kanchi Kailasanathar temple in Kanchipuram ", " Somnath temple in Saurashtra ", 
                                " Brihadisvara temple in Thanjavur ", " Shree Jagannath temple in Puri "],
                    options_hi: [" कांचीपुरम में कांची कैलासनाथर मंदिर ", " सौराष्ट्र में सोमनाथ मंदिर",
                                " तंजावुर में बृहदीश्वर मंदिर", " पुरी में श्री जगन्नाथ मंदिर"],
                    solution_en: "30.(c) Brihadisvara temple in Thanjavur is called the Great Living Chola Temple. The founder of the Chola Empire was Vijayalaya.",
                    solution_hi: "30.(c) तंजावुर में बृहदेश्वर मंदिर को महान जीवित चोल मंदिर कहा जाता है। चोल साम्राज्य का संस्थापक विजयालय था।",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "31",
                    section: "18",
                    question_en: "31. The position of earth in its orbit when it is at its greatest distance from the Sun is called   ",
                    question_hi: "31. पृथ्वी की वह स्थिति जब वह सूर्य से अपनी अधिकतम दूरी पर होती है,वह________ कहलाती है:",
                    options_en: [" Aphelion ", " Perigee ", 
                                " Perihelion ", " Apogee "],
                    options_hi: [" अपसौरिका ", " भू-समीपक",
                                " उपसौर", " अपभू"],
                    solution_en: "31.(a) The position of the Earth when it is at the greatest distance from the sun is called the Aphelion(July 4). The closest point to the Sun in a planet\'s orbit is called perihelion(January 4).",
                    solution_hi: "31.(a) पृथ्वी की वह स्थिति जब वह सूर्य से सबसे अधिक दूरी पर होती है (जुलाई 4) ,अपसौरिका (Aphelion) कहलाती है। पृथ्वी की कक्षा से सूर्य के निकटतम बिंदु (4 जनवरी) को उपसौर (perihelion) कहा जाता है।",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "32",
                    section: "18",
                    question_en: "32. In which state are the Jog waterfalls situated?",
                    question_hi: "32. जोग जलप्रपात किस राज्य में स्थित है?",
                    options_en: [" Telangana ", " Arunachal Pradesh ", 
                                " Nagaland ", " Karnataka "],
                    options_hi: [" तेलंगाना   ", " अरुणाचल प्रदेश",
                                " नगालैंड   ", " कर्नाटक"],
                    solution_en: "32.(d) Jog Falls is a waterfall on the Sharavathi river located in the Western Ghats near Sagar Taluk of Shivamogga District, Karnataka, India.",
                    solution_hi: "32.(d) जोग जलप्रपात भारत के कर्नाटक राज्य के शिवमोग्गा जिले के सागर तालुक के पास पश्चिमी घाट में स्थित शरवती नदी पर एक झरना है।",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "33",
                    section: "18",
                    question_en: "33. Which type of natural vegetation is found in the Ganges-Brahmaputra delta?",
                    question_hi: "33. गंगा-ब्रह्मपुत्र डेल्टा में किस प्रकार की प्राकृतिक वनस्पति पाई जाती है?",
                    options_en: [" Mulberry trees ", " Indian chestnut trees ", 
                                " Mangrove trees ", " Rosewood trees "],
                    options_hi: [" शहतूत के पेड़", " भारतीय शाहबलूत के पेड़",
                                " मैंग्रोव पेड़", " शीशम के पेड़"],
                    solution_en: "33.(c) Mangrove trees are a type of natural vegetation found in the Ganges-Brahmaputra delta. Largest Mangrove Forest in India West Bengal (Sundarban). ",
                    solution_hi: "33.(c) मैंग्रोव वृक्ष एक प्रकार की प्राकृतिक वनस्पति हैं जो गंगा-ब्रह्मपुत्र डेल्टा में पाई जाती हैं। भारत में सबसे बड़ा मैंग्रोव वन पश्चिम बंगाल (सुंदरबन) है।<br /> ",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "34",
                    section: "18",
                    question_en: "34. Which function key is used to change the name of a specific file?",
                    question_hi: "34. किसी विशिष्ट फ़ाइल का नाम बदलने के लिए किस फ़ंक्शन कुंजी का उपयोग किया जाता है?",
                    options_en: [" ALT + S", " F4", 
                                " CTRL + E", " F2"],
                    options_hi: [" ALT + S", " F4",
                                " CTRL + E", " F2"],
                    solution_en: "34.(d) F2 function key is used to change the name of a specific file. Alt+S is a keyboard shortcut most often used to open the slide show settings in PowerPoint. F4 is used to close the window in a program. Ctrl+E aligns the line or selected text to the center of the page.<br /> ",
                    solution_hi: "34.(d) F2 फंक्शन की (कीबोर्ड) का उपयोग किसी विशिष्ट फाइल का नाम बदलने के लिए किया जाता है। Alt+S एक कीबोर्ड शॉर्टकट है जिसका उपयोग अक्सर पावर प्वाइंट में स्लाइड शो सेटिंग्स को खोलने के लिए किया जाता है। किसी प्रोग्राम में विंडो को बंद करने के लिए F4 का उपयोग किया जाता है। Ctrl+E लाइन या चयनित टेक्स्ट को पृष्ठ के केंद्र में संरेखित करता है। ",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "35",
                    section: "18",
                    question_en: "35. What does ASCII stand for ? ",
                    question_hi: "35. ASCII का पूर्ण रूप क्या है?",
                    options_en: [" Asian Schema Code for Information Interchange", " Asian Standard Code for Internet Interchange", 
                                " American Standard Code for Information Interchange", " American Standard Code for Internet Information "],
                    options_hi: [" Asian Schema Code for Information Interchange", " Asian Standard Code for Internet Interchange",
                                " American Standard Code for Information Interchange", " American Standard Code for Internet Information "],
                    solution_en: "35.(c) ASCII, an abbreviation of American Standard Code For Information Interchange. An ASCII File is a file that contains unformatted ASCII text: only characters, numbers, punctuation, tabs, and carriage return characters.",
                    solution_hi: "35.(c)  ASCII, abbreviation of American Standard Code For Information Interchange.( सूचना इंटरचेंज के लिए अमेरिकी मानक कोड का संक्षिप्त नाम।)",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "36",
                    section: "18",
                    question_en: "36. Which committee adopted the cost of living as the basis for identifying poverty?",
                    question_hi: "36. गरीबी की पहचान के लिए किस समिति ने जीवन यापन की लागत को आधार के रूप में अपनाया?",
                    options_en: [" Tendulkar Committee ", " Lakdawala Committee ", 
                                " YK Alagh Committee ", " Shah Committee "],
                    options_hi: [" तेंदुलकर समिति", " लकड़ावाला समिति",
                                " वाईके अलघ समिति", " शाह समिति"],
                    solution_en: "36.(a) The Tendulkar Committee adopted the cost of living as the basis for identifying poverty. Lakdawala Committee defined the poverty line on the basis of household per capita consumption expenditure. The YK Alagh Committee is related to measuring poverty precisely as starvation. Shah committee Reforms Relating To Non-Banking Financial Companies (NFBCs). ",
                    solution_hi: "36.(a) तेंदुलकर समिति ने गरीबी की पहचान के लिए जीवन यापन की लागत को आधार के रूप में अपनाया। लकड़ावाला समिति ने परिवार प्रति व्यक्ति उपभोग व्यय के आधार पर गरीबी रेखा को परिभाषित किया। वाईके अलघ समिति गरीबी को भुखमरी के रूप में मापने से संबंधित है। शाह समिति- गैर बैंकिंग वित्तीय कंपनियों (NFBCs) से संबंधित है।",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "37",
                    section: "18",
                    question_en: "37. Who organized the Independent Labour Party and Scheduled Castes Federation ?",
                    question_hi: "37. स्वतंत्र लेबर पार्टी और अनुसूचित जाति संघ का गठन किसने किया?",
                    options_en: [" Dr. Zakir Hussain ", " Dr. Rajendra Prasad ", 
                                " Dr. Bhimrao Ramji Ambedkar ", " Chandra Shekhar Azad <br /> "],
                    options_hi: [" डॉ. जाकिर हुसैन", " डॉ राजेंद्र प्रसाद",
                                " डॉ भीमराव रामजी अम्बेडकर", " चंद्रशेखर आजाद"],
                    solution_en: "37.(c) Dr. Bhimrao Ramji Ambedkar organized the Independent Labour Party and Scheduled Castes Federation.",
                    solution_hi: "37.(c) डॉ. भीमराव रामजी अम्बेडकर ने स्वतंत्र लेबर पार्टी और अनुसूचित जाति संघ का गठन किया।",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "38",
                    section: "18",
                    question_en: "38. Which of the following programmes is implemented by NABARD?",
                    question_hi: "38. निम्नलिखित में से कौन सा कार्यक्रम NABARD द्वारा कार्यान्वित किया जाता है?",
                    options_en: [" Prime Minister Rojgar Yojna ", " Pradhan Mantri Jeevan Jyoti Bima Yojana ", 
                                " Umbrella Programme for Natural Resource Management ", " Mid day Meal Scheme "],
                    options_hi: [" प्रधानमंत्री रोजगार योजना", " प्रधानमंत्री जीवन ज्योति बीमा योजना",
                                " प्राकृतिक संसाधन प्रबंधन के लिए छाता कार्यक्रम", " मध्याह्न भोजन योजना"],
                    solution_en: "38.(c) Umbrella Programme for Natural Resource Management programmes is implemented by NABARD. National Bank for Agriculture and Rural Development is an apex regulatory body for overall regulation of regional rural banks and apex cooperative banks in India. Founded 12 July 1982, Headquarters, Mumbai. Harsh Kumar Bhanwala is the current(Nov 2021) chairperson.",
                    solution_hi: "38.(c)  प्राकृतिक संसाधन प्रबंधन कार्यक्रमों के लिए छाता कार्यक्रम नाबार्ड द्वारा कार्यान्वित किया जाता है।राष्ट्रीय कृषि और ग्रामीण विकास बैंक भारत में क्षेत्रीय ग्रामीण बैंकों और शीर्ष सहकारी बैंकों के समग्र विनियमन के लिए एक शीर्ष नियामक निकाय है। 12 जुलाई, 1982 को स्थापित, मुख्यालय- मुंबई, हर्ष कुमार भनवाला वर्तमान (नवंबर 2021) अध्यक्ष हैं।",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "39",
                    section: "18",
                    question_en: "39. In May 2019, Naveen Patnaik was sworn in as the Chief Minister Of Odisha for the ____ time.",
                    question_hi: "39.मई 2019 में, नवीन पटनायक ने _____ बार ओडिशा के मुख्यमंत्री के रूप में शपथ ली।",
                    options_en: [" third ", " fifth ", 
                                " fourth ", " sixth "],
                    options_hi: [" तीसरी ", " पांचवी ",
                                " चौथी ", " छट्ठी "],
                    solution_en: "39.(b) In May 2019, Naveen Patnaik was sworn in as the Chief Minister Of Odisha for the fifth time. He is from Biju Janata Dal Party founded by himself in 1997.",
                    solution_hi: "39.(b) मई 2019 में, नवीन पटनायक ने पांचवीं बार ओडिशा के मुख्यमंत्री के रूप में शपथ ली। वह 1997 में स्वयं द्वारा स्थापित बीजू जनता दल पार्टी से हैं।",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "40",
                    section: "misc",
                    question_en: "40. Wanawan is folk music from ____ which is sung during wedding ceremonies.",
                    question_hi: "40. वानावन ____ का लोक संगीत है जिसे विवाह समारोहों के दौरान गाया जाता है।",
                    options_en: [" Haryana        ", "  Maharashtra ", 
                                " Madhya Pradesh    ", "  Kashmir "],
                    options_hi: [" हरियाणा ", " महाराष्ट्र",
                                " मध्य प्रदेश", " कश्मीर"],
                    solution_en: "40.(d)  Wanawan is folk music from Kashmir which is sung during wedding ceremonies.",
                    solution_hi: "40.(d) वानावन कश्मीर का लोक संगीत है, जिसे विवाह समारोहों के दौरान गाया जाता है।",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>