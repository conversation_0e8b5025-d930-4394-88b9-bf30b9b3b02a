<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">9:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 25</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">25</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 9 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["7"] = {
                name: "English",
                start: 0,
                end: 23
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="7">English</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 24,
                end: 24
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "7",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">1. </span><span style=\"font-family: Cambria Math;\">Select the most appropriate option that can substitute the underlined segment in the </span><span style=\"font-family: Cambria Math;\">given sentence.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">The clock that we have in our town square is</span><span style=\"font-family: Cambria Math;\"> <strong><span style=\"text-decoration: underline;\">a big</span></strong></span><span style=\"font-family: Cambria Math;\"> in the whole town</span></p>\n",
                    question_hi: "<p>1. <span style=\"font-family: Cambria Math;\">Select the most appropriate option that can substitute the underlined segment in the </span><span style=\"font-family: Cambria Math;\">given sentence.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">The clock that we have in our town square is</span><span style=\"font-family: Cambria Math;\"> <span style=\"text-decoration: underline;\"><strong>a big</strong></span></span><span style=\"font-family: Cambria Math;\"> in the whole town</span></p>\n",
                    options_en: ["<p>the bigger</p>\n", "<p>the big</p>\n", 
                                "<p>the biggest</p>\n", "<p>a biggest</p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\n"],
                    options_hi: ["<p>the bigger</p>\n", "<p>the big</p>\n",
                                "<p>the biggest</p>\n", "<p>a biggest</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">1.(</span><span style=\"font-family: Cambria Math;\">c)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">The superlative degree will be used as the clock is the biggest in the whole town. Option c is the correct answer.</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">1.(</span><span style=\"font-family: Cambria Math;\">c)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">The superlative degree will be used as the clock is the biggest in the whole town. Option c is the correct answer.</span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "7",
                    question_en: " <p>2. </span><span style=\"font-family:Cambria Math\">Select the most appropriate ANTONYM of the given word.</span></p> <p><span style=\"font-family:Cambria Math\">Comic</span></p>",
                    question_hi: " <p>2. </span><span style=\"font-family:Cambria Math\">Select the most appropriate ANTONYM of the given word.</span></p> <p><span style=\"font-family:Cambria Math\">Comic</span></p>",
                    options_en: [" <p> Native</span></p>", " <p> Costly</span></p>", 
                                " <p> Tragic</span></p>", " <p> Empty</span></p> <p><span style=\"font-family:Cambria Math\"> </span></p>"],
                    options_hi: [" <p> Native</span></p>", " <p> Costly</span></p>",
                                " <p> Tragic</span></p>", " <p> Empty</span></p>"],
                    solution_en: " <p><span style=\"font-family:Cambria Math\">2.(</span><span style=\"font-family:Cambria Math\">c)</span></p> <p><span style=\"font-family:Cambria Math\">Comic means funny. The opposite will be tragic which means something that causes distress or sorrow. Option c is the most appropriate answer.</span></p>",
                    solution_hi: " <p><span style=\"font-family:Cambria Math\">2.(</span><span style=\"font-family:Cambria Math\">c)</span></p> <p><span style=\"font-family:Cambria Math\">Comic means funny. The opposite will be tragic which means something that causes distress or sorrow. Option c is the most appropriate answer.</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "7",
                    question_en: "<p>3. <span style=\"font-family: Cambria Math;\">Complete the sentence with an appropriate ANTONYM of the word mentioned in the </span><span style=\"font-family: Cambria Math;\">bracket.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">My fascination with Tyler\'s items ____________(approached) into distant memories.</span></p>\n",
                    question_hi: "<p>3. <span style=\"font-family: Cambria Math;\">Complete the sentence with an appropriate ANTONYM of the word mentioned in the </span><span style=\"font-family: Cambria Math;\">bracket.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">My fascination with Tyler\'s items ____________(approached) into distant memories.</span></p>\n",
                    options_en: ["<p>receded</p>\n", "<p>escalated</p>\n", 
                                "<p>expanded</p>\n", "<p>intensified</p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\n"],
                    options_hi: ["<p>receded</p>\n", "<p>escalated</p>\n",
                                "<p>expanded</p>\n", "<p>intensified</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">3.(</span><span style=\"font-family: Cambria Math;\">a)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Approach means to come near to something in distance or in </span><span style=\"font-family: Cambria Math;\">time.Eg</span><span style=\"font-family: Cambria Math;\">- The train is approaching Delhi.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Receded means to go or move back or further away from a previous </span><span style=\"font-family: Cambria Math;\">position.Option</span><span style=\"font-family: Cambria Math;\"> a is the most appropriate answer.</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">3.(</span><span style=\"font-family: Cambria Math;\">a)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Approach means to come near to something in distance or in </span><span style=\"font-family: Cambria Math;\">time.Eg</span><span style=\"font-family: Cambria Math;\">- The train is approaching Delhi.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Receded means to go or move back or further away from a previous </span><span style=\"font-family: Cambria Math;\">position.Option</span><span style=\"font-family: Cambria Math;\"> a is the most appropriate answer.</span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "7",
                    question_en: "<p>4.<span style=\"font-family: Cambria Math;\"> Sentences of a paragraph are given below in jumbled order. Arrange the sentences in </span><span style=\"font-family: Cambria Math;\">the correct order to form a meaningful and coherent paragraph.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">A. These carriers of bacteria and viruses cause many diseases like Yellow Fever, </span><span style=\"font-family: Cambria Math;\">Malaria etc. so panchayats and municipalities are taking a lot of care to protect people </span><span style=\"font-family: Cambria Math;\">from them.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">B. There are thousands of species of mosquitoes that are known to the people of the </span><span style=\"font-family: Cambria Math;\">world. They mainly live in still water and other places where water is stored.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">C. Mosquitos are everywhere now. Villagers, as well as city dwellers are afraid of these </span><span style=\"font-family: Cambria Math;\">small creatures.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">D. Usually, female and male mosquitoes take feed on </span><span style=\"font-family: Cambria Math;\">plant&rsquo;s</span><span style=\"font-family: Cambria Math;\"> or fruit honey, however </span><span style=\"font-family: Cambria Math;\">female mosquitoes mostly suck the blood of humans.</span></p>\n",
                    question_hi: "<p>4.<span style=\"font-family: Cambria Math;\"> Sentences of a paragraph are given below in jumbled order. Arrange the sentences in </span><span style=\"font-family: Cambria Math;\">the correct order to form a meaningful and coherent paragraph.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">A. These carriers of bacteria and viruses cause many diseases like Yellow Fever, </span><span style=\"font-family: Cambria Math;\">Malaria etc. so panchayats and municipalities are taking a lot of care to protect people </span><span style=\"font-family: Cambria Math;\">from them.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">B. There are thousands of species of mosquitoes that are known to the people of the </span><span style=\"font-family: Cambria Math;\">world. They mainly live in still water and other places where water is stored.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">C. Mosquitos are everywhere now. Villagers, as well as city dwellers are afraid of these </span><span style=\"font-family: Cambria Math;\">small creatures.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">D. Usually, female and male mosquitoes take feed on </span><span style=\"font-family: Cambria Math;\">plant&rsquo;s</span><span style=\"font-family: Cambria Math;\"> or fruit honey, however </span><span style=\"font-family: Cambria Math;\">female mosquitoes mostly suck the blood of humans.</span></p>\n",
                    options_en: ["<p>ADCB</p>\n", "<p>DABC</p>\n", 
                                "<p>CBAD</p>\n", "<p>ACBD</p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\n"],
                    options_hi: ["<p>ADCB</p>\n", "<p>DABC</p>\n",
                                "<p>CBAD</p>\n", "<p>ACBD</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">4.(</span><span style=\"font-family: Cambria Math;\">c)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">The </span><span style=\"font-family: Cambria Math;\">parajumble</span><span style=\"font-family: Cambria Math;\"> is about mosquitoes and that is mentioned in sentence c. So C will be the first </span><span style=\"font-family: Cambria Math;\">sentence.Next</span><span style=\"font-family: Cambria Math;\"> will be B which mentions about the species and where mosquitoes </span><span style=\"font-family: Cambria Math;\">live.With</span><span style=\"font-family: Cambria Math;\"> CB beginning only option c is </span><span style=\"font-family: Cambria Math;\">available.Option</span><span style=\"font-family: Cambria Math;\"> c is the most appropriate answer.</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">4.(</span><span style=\"font-family: Cambria Math;\">c)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">The </span><span style=\"font-family: Cambria Math;\">parajumble</span><span style=\"font-family: Cambria Math;\"> is about mosquitoes and that is mentioned in sentence c. So C will be the first </span><span style=\"font-family: Cambria Math;\">sentence.Next</span><span style=\"font-family: Cambria Math;\"> will be B which mentions about the species and where mosquitoes </span><span style=\"font-family: Cambria Math;\">live.With</span><span style=\"font-family: Cambria Math;\"> CB beginning only option c is </span><span style=\"font-family: Cambria Math;\">available.Option</span><span style=\"font-family: Cambria Math;\"> c is the most appropriate answer.</span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "7",
                    question_en: "<p>5. <span style=\"font-family: Cambria Math;\">Select the option that can be used as a one-word substitute for the given group of </span><span style=\"font-family: Cambria Math;\">words.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Easily provoked</span></p>\n",
                    question_hi: "<p>5. <span style=\"font-family: Cambria Math;\">Select the option that can be used as a one-word substitute for the given group of </span><span style=\"font-family: Cambria Math;\">words.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Easily provoked</span></p>\n",
                    options_en: ["<p>Invincible</p>\n", "<p>Irascible</p>\n", 
                                "<p>Iridescent</p>\n", "<p>Irrational</p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\n"],
                    options_hi: ["<p>Invincible</p>\n", "<p>Irascible</p>\n",
                                "<p>Iridescent</p>\n", "<p>Irrational</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">5.(</span><span style=\"font-family: Cambria Math;\">b)</span></p>\r\n<p>Invincible- Too powerful to be defeated.</p>\r\n<p>Irascible - Easily angered</p>\r\n<p>Iridescent-showing luminous <span style=\"font-family: Cambria Math;\">colours</span><span style=\"font-family: Cambria Math;\"> that seem to change when seen from different angles.</span></p>\r\n<p>Irrational- Not logical</p>\r\n<p><span style=\"font-family: Cambria Math;\">Option b is the most appropriate answer.</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">5.(</span><span style=\"font-family: Cambria Math;\">b)</span></p>\r\n<p>Invincible- Too powerful to be defeated.</p>\r\n<p>Irascible - Easily angered</p>\r\n<p>Iridescent-showing luminous <span style=\"font-family: Cambria Math;\">colours</span><span style=\"font-family: Cambria Math;\"> that seem to change when seen from different angles.</span></p>\r\n<p>Irrational- Not logical</p>\r\n<p><span style=\"font-family: Cambria Math;\">Option b is the most appropriate answer.</span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "7",
                    question_en: "<p>6.<span style=\"font-family: Cambria Math;\"> Select the most appropriate option that can substitute the underlined segment in the </span><span style=\"font-family: Cambria Math;\">given sentence.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Mr. Pramod couldn&rsquo;t </span><span style=\"text-decoration: underline;\"><strong><span style=\"font-family: Cambria Math;\">wait to trying out</span></strong></span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">his new recipe.</span></p>\n",
                    question_hi: "<p>6.<span style=\"font-family: Cambria Math;\"> Select the most appropriate option that can substitute the underlined segment in the </span><span style=\"font-family: Cambria Math;\">given sentence.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Mr. Pramod couldn&rsquo;t </span><span style=\"text-decoration: underline;\"><strong><span style=\"font-family: Cambria Math;\">wait to trying out</span><span style=\"font-family: Cambria Math;\"> </span></strong></span><span style=\"font-family: Cambria Math;\">his new recipe.</span></p>\n",
                    options_en: ["<p>waited to try out</p>\n", "<p>wait for try out</p>\n", 
                                "<p>waited to try</p>\n", "<p>wait to try out</p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\n"],
                    options_hi: ["<p>waited to try out</p>\n", "<p>wait for try out</p>\n",
                                "<p>waited to try</p>\n", "<p>wait to try out</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">6.(</span><span style=\"font-family: Cambria Math;\">d)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">With could the first form of the verb is used, so wait will be used. And after &ldquo;to&rdquo; the first form of the verb is used. </span><span style=\"font-family: Cambria Math;\">So</span><span style=\"font-family: Cambria Math;\"> option d &ldquo;wait to try out&rdquo; is the most appropriate answer.</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">6.(</span><span style=\"font-family: Cambria Math;\">d)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">With could the first form of the verb is used, so wait will be used. And after &ldquo;to&rdquo; the first form of the verb is used. </span><span style=\"font-family: Cambria Math;\">So</span><span style=\"font-family: Cambria Math;\"> option d &ldquo;wait to try out&rdquo; is the most appropriate answer.</span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "7",
                    question_en: " <p>7. </span><span style=\"font-family:Cambria Math\">Select the most appropriate option to fill in the blank.</span></p> <p><span style=\"font-family:Cambria Math\">Funny stories made me and my brother_________.</span></p>",
                    question_hi: " <p>7. </span><span style=\"font-family:Cambria Math\">Select the most appropriate option to fill in the blank.</span></p> <p><span style=\"font-family:Cambria Math\">Funny stories made me and my brother_________.</span></p>",
                    options_en: [" <p> conscious</span></p>", " <p> determined</span></p>", 
                                " <p> giggly</span></p>", " <p> gritty</span></p> <p><span style=\"font-family:Cambria Math\"> </span></p>"],
                    options_hi: [" <p> conscious</span></p>", " <p> determined</span></p>",
                                " <p> giggly</span></p>", " <p> gritty</span></p>"],
                    solution_en: " <p><span style=\"font-family:Cambria Math\">7.(</span><span style=\"font-family:Cambria Math\">c)</span></p> <p><span style=\"font-family:Cambria Math\">Giggly is </span><span style=\"font-family:Cambria Math\">having a tendency to laugh in an excited, nervous, or silly way. The stories are funny so the word giggly will be </span><span style=\"font-family:Cambria Math\">used.</span><span style=\"font-family:Cambria Math\">So</span><span style=\"font-family:Cambria Math\"> option c is the most appropriate answer.</span></p>",
                    solution_hi: " <p><span style=\"font-family:Cambria Math\">7.(</span><span style=\"font-family:Cambria Math\">c)</span></p> <p><span style=\"font-family:Cambria Math\">Giggly is </span><span style=\"font-family:Cambria Math\">having a tendency to laugh in an excited, nervous, or silly way. The stories are funny so the word giggly will be </span><span style=\"font-family:Cambria Math\">used.</span><span style=\"font-family:Cambria Math\">So</span><span style=\"font-family:Cambria Math\"> option c is the most appropriate answer.</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "7",
                    question_en: "<p>8.<span style=\"font-family: Cambria Math;\"> Select the option that expresses the given sentence in passive voice.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">I know her.</span></p>\n",
                    question_hi: "<p>8.<span style=\"font-family: Cambria Math;\"> Select the option that expresses the given sentence in passive voice.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">I know her.</span></p>\n",
                    options_en: ["<p>She is whom I know.</p>\n", "<p>She is known to me.</p>\n", 
                                "<p>She knows me.</p>\n", "<p>I am known by her.</p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\n"],
                    options_hi: ["<p>She is whom I know.</p>\n", "<p>She is known to me.</p>\n",
                                "<p>She knows me.</p>\n", "<p>I am known by her.</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">8.(</span><span style=\"font-family: Cambria Math;\">b)</span></p>\r\n<p><span style=\"font-weight: 400;\">(a) She </span><strong>is whom</strong><span style=\"font-weight: 400;\"> I know.(Usage of whom is incorrect)</span></p>\r\n<p><span style=\"font-weight: 400;\">(b) She is known to me.(Correct)</span></p>\r\n<p><span style=\"font-weight: 400;\">(c) </span><strong>She knows me. (meaning changed)</strong></p>\r\n<p><span style=\"font-weight: 400;\">(d) </span><strong>I am known by her.(Meaning changed)</strong></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">8.(</span><span style=\"font-family: Cambria Math;\">b)</span></p>\r\n<p><span style=\"font-weight: 400;\">(a) She </span><strong>is whom</strong><span style=\"font-weight: 400;\"> I know.(Usage of whom is incorrect)</span></p>\r\n<p><span style=\"font-weight: 400;\">(b) She is known to me.(Correct)</span></p>\r\n<p><span style=\"font-weight: 400;\">(c) </span><strong>She knows me. (meaning changed)</strong></p>\r\n<p><span style=\"font-weight: 400;\">(d) </span><strong>I am known by her.(Meaning changed)</strong></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "7",
                    question_en: " <p>9.</span><span style=\"font-family:Cambria Math\"> Select the most appropriate ANTONYM of the given word.</span></p> <p><span style=\"font-family:Cambria Math\">Uniform</span></p>",
                    question_hi: " <p>9.</span><span style=\"font-family:Cambria Math\"> Select the most appropriate ANTONYM of the given word.</span></p> <p><span style=\"font-family:Cambria Math\">Uniform</span></p>",
                    options_en: [" <p> Inflexible</span></p>", " <p> Variable</span></p>", 
                                " <p> Orderly</span></p>", " <p> Irrelevant</span></p> <p><span style=\"font-family:Cambria Math\"> </span></p>"],
                    options_hi: [" <p> Inflexible</span></p>", " <p> Variable</span></p>",
                                " <p> Orderly</span></p>", " <p> Irrelevant</span></p>"],
                    solution_en: " <p><span style=\"font-family:Cambria Math\">9.(</span><span style=\"font-family:Cambria Math\">b)</span></p> <p><span style=\"font-family:Cambria Math\">Uniform means same throughout. Variable means something that is changing, not having a fixed </span><span style=\"font-family:Cambria Math\">pattern.So</span><span style=\"font-family:Cambria Math\"> option b is the most appropriate answer.</span></p>",
                    solution_hi: " <p><span style=\"font-family:Cambria Math\">9.(</span><span style=\"font-family:Cambria Math\">b)</span></p> <p><span style=\"font-family:Cambria Math\">Uniform means same throughout. Variable means something that is changing, not having a fixed </span><span style=\"font-family:Cambria Math\">pattern.So</span><span style=\"font-family:Cambria Math\"> option b is the most appropriate answer.</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "7",
                    question_en: "<p>10. <span style=\"font-family: Cambria Math;\">The given sentence contains an error in tense form. Identify the error and choose the </span><span style=\"font-family: Cambria Math;\">option that correctly rectifies the error.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Bakshi will </span><span style=\"font-family: Cambria Math;\">going</span><span style=\"font-family: Cambria Math;\"> to call you later in the evening.</span></p>\n",
                    question_hi: "<p>10. <span style=\"font-family: Cambria Math;\">The given sentence contains an error in tense form. Identify the error and choose the </span><span style=\"font-family: Cambria Math;\">option that correctly rectifies the error.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Bakshi will </span><span style=\"font-family: Cambria Math;\">going</span><span style=\"font-family: Cambria Math;\"> to call you later in the evening.</span></p>\n",
                    options_en: ["<p>Bakshi was gone to call you later in the evening.</p>\n", "<p>Bakshi will go to call you later in the evening.</p>\n", 
                                "<p>Bakshi is going to call you later in the evening.</p>\n", "<p>Bakshi goes to call you later in the evening.</p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\n"],
                    options_hi: ["<p>Bakshi was gone to call you later in the evening.</p>\n", "<p>Bakshi will go to call you later in the evening.</p>\n",
                                "<p>Bakshi is going to call you later in the evening.</p>\n", "<p>Bakshi goes to call you later in the evening.</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">10.(</span><span style=\"font-family: Cambria Math;\">c)</span></p>\r\n<p><span style=\"font-weight: 400;\">(a) Bakshi </span><strong>was gone</strong><span style=\"font-weight: 400;\"> to call you later in the evening.(Incorrect tense)</span></p>\r\n<p><span style=\"font-weight: 400;\">(b) Bakshi</span><strong> will go</strong><span style=\"font-weight: 400;\"> to call you later in the evening.(Incorrect tense)</span></p>\r\n<p><span style=\"font-weight: 400;\">(c) Bakshi is going to call you later in the evening.(Correct)</span></p>\r\n<p><span style=\"font-weight: 400;\">(d) Bakshi </span><strong>goes to</strong><span style=\"font-weight: 400;\"> call you later in the evening.(Incorrect tense)</span></p>\r\n<p>&nbsp;</p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">10.(</span><span style=\"font-family: Cambria Math;\">c)</span></p>\r\n<p><span style=\"font-weight: 400;\">(a) Bakshi </span><strong>was gone</strong><span style=\"font-weight: 400;\"> to call you later in the evening.(Incorrect tense)</span></p>\r\n<p><span style=\"font-weight: 400;\">(b) Bakshi</span><strong> will go</strong><span style=\"font-weight: 400;\"> to call you later in the evening.(Incorrect tense)</span></p>\r\n<p><span style=\"font-weight: 400;\">(c) Bakshi is going to call you later in the evening.(Correct)</span></p>\r\n<p><span style=\"font-weight: 400;\">(d) Bakshi </span><strong>goes to</strong><span style=\"font-weight: 400;\"> call you later in the evening.(Incorrect tense)</span></p>\r\n<p>&nbsp;</p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "7",
                    question_en: " <p>11.</span><span style=\"font-family:Cambria Math\"> Select the most appropriate meaning of the given idiom.</span></p> <p><span style=\"font-family:Cambria Math\">Upset the apple-cart</span></p>",
                    question_hi: " <p>11.</span><span style=\"font-family:Cambria Math\"> Select the most appropriate meaning of the given idiom.</span></p> <p><span style=\"font-family:Cambria Math\">Upset the apple-cart</span></p>",
                    options_en: [" <p> Withdraw</span></p>", " <p> Spoil careful plans</span></p>", 
                                " <p> Consider unsatisfactory</span></p>", " <p> To be offended</span></p> <p><span style=\"font-family:Cambria Math\"> </span></p>"],
                    options_hi: [" <p> Withdraw</span></p>", " <p> Spoil careful plans</span></p>",
                                " <p> Consider unsatisfactory</span></p>", " <p> To be offended</span></p>"],
                    solution_en: " <p><span style=\"font-family:Cambria Math\">11.(</span><span style=\"font-family:Cambria Math\">b)</span></p> <p><span style=\"font-family:Cambria Math\">Upset the apple-cart - Spoil careful plans</span></p> <p><span style=\"font-family:Cambria Math\">Eg</span><span style=\"font-family:Cambria Math\">- The rain has upset the apple cart for the outdoor party.</span></p>",
                    solution_hi: " <p><span style=\"font-family:Cambria Math\">11.(</span><span style=\"font-family:Cambria Math\">b)</span></p> <p><span style=\"font-family:Cambria Math\">Upset the apple-cart - Spoil careful plans</span></p> <p><span style=\"font-family:Cambria Math\">Eg</span><span style=\"font-family:Cambria Math\">- The rain has upset the apple cart for the outdoor party.</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "7",
                    question_en: "<p>12.<span style=\"font-family: Cambria Math;\"> The following sentence has been split into four segments. Identify the segment that </span><span style=\"font-family: Cambria Math;\">contains a grammatical error.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">They attend / a meeting / in Chicago / this weekend.</span></p>\n",
                    question_hi: "<p>12.<span style=\"font-family: Cambria Math;\"> The following sentence has been split into four segments. Identify the segment that </span><span style=\"font-family: Cambria Math;\">contains a grammatical error.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">They attend / a meeting / in Chicago / this weekend.</span></p>\n",
                    options_en: ["<p>a meeting</p>\n", "<p>this weekend</p>\n", 
                                "<p>in Chicago</p>\n", "<p>They attend</p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\n"],
                    options_hi: ["<p>a meeting</p>\n", "<p>this weekend</p>\n",
                                "<p>in Chicago</p>\n", "<p>They attend</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">12.(</span><span style=\"font-family: Cambria Math;\">d)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">The future indefinite tense will be used in this sentence as it mentions an action that will take place in the </span><span style=\"font-family: Cambria Math;\">future.The</span><span style=\"font-family: Cambria Math;\"> correct sentence will be- They will attend a meeting in Chicago this weekend.</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">12.(</span><span style=\"font-family: Cambria Math;\">d)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">The future indefinite tense will be used in this sentence as it mentions an action that will take place in the </span><span style=\"font-family: Cambria Math;\">future.The</span><span style=\"font-family: Cambria Math;\"> correct sentence will be- They will attend a meeting in Chicago this weekend.</span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "7",
                    question_en: "<p>13.<span style=\"font-family: Cambria Math;\"> Select the most appropriate meaning of the underlined word in the given sentence.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">His </span><span style=\"text-decoration: underline;\"><strong><span style=\"font-family: Cambria Math;\">perverse</span></strong></span><span style=\"font-family: Cambria Math;\"><span style=\"text-decoration: underline;\"><strong> </strong></span>thoughts were very disturbing so he decided to try seeing a therapist.</span></p>\n",
                    question_hi: "<p>13.<span style=\"font-family: Cambria Math;\"> Select the most appropriate meaning of the underlined word in the given sentence.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">His </span><span style=\"text-decoration: underline;\"><strong><span style=\"font-family: Cambria Math;\">perverse</span></strong></span><span style=\"font-family: Cambria Math;\"><span style=\"text-decoration: underline;\"><strong> </strong></span>thoughts were very disturbing so he decided to try seeing a therapist.</span></p>\n",
                    options_en: ["<p>Convergent</p>\n", "<p>Deviant</p>\n", 
                                "<p>Cynical</p>\n", "<p>Logical</p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\n"],
                    options_hi: ["<p>Convergent</p>\n", "<p>Deviant</p>\n",
                                "<p>Cynical</p>\n", "<p>Logical</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">13.(</span><span style=\"font-family: Cambria Math;\">b)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Perverse means unreasonable or </span><span style=\"font-family: Cambria Math;\">unacceptable.It</span><span style=\"font-family: Cambria Math;\"> is something contrary to popular beliefs and what is </span><span style=\"font-family: Cambria Math;\">acceptable.So</span><span style=\"font-family: Cambria Math;\"> b deviant will be the most appropriate </span><span style=\"font-family: Cambria Math;\">answer.Deviant</span><span style=\"font-family: Cambria Math;\"> means departing from usual or accepted standards of </span><span style=\"font-family: Cambria Math;\">behaviour</span><span style=\"font-family: Cambria Math;\">.</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">13.(</span><span style=\"font-family: Cambria Math;\">b)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Perverse means unreasonable or </span><span style=\"font-family: Cambria Math;\">unacceptable.It</span><span style=\"font-family: Cambria Math;\"> is something contrary to popular beliefs and what is </span><span style=\"font-family: Cambria Math;\">acceptable.So</span><span style=\"font-family: Cambria Math;\"> b deviant will be the most appropriate </span><span style=\"font-family: Cambria Math;\">answer.Deviant</span><span style=\"font-family: Cambria Math;\"> means departing from usual or accepted standards of </span><span style=\"font-family: Cambria Math;\">behaviour</span><span style=\"font-family: Cambria Math;\">.</span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "7",
                    question_en: " <p>14. </span><span style=\"font-family:Cambria Math\">Select the INCORRECTLY spelt word.</span></p>",
                    question_hi: " <p>14. </span><span style=\"font-family:Cambria Math\">Select the INCORRECTLY spelt word.</span></p>",
                    options_en: [" <p> Certificate</span></p>", " <p> Channel</span></p>", 
                                " <p> </span><span style=\"font-family:Cambria Math\">Champeon</span></p>", " <p> Challenge</span></p> <p><span style=\"font-family:Cambria Math\"> </span></p>"],
                    options_hi: [" <p> Certificate</span></p>", " <p> Channel</span></p>",
                                " <p> </span><span style=\"font-family:Cambria Math\">Champeon</span></p>", " <p> Challenge</span></p>"],
                    solution_en: " <p><span style=\"font-family:Cambria Math\">14.(</span><span style=\"font-family:Cambria Math\">c)</span></p> <p><span style=\"font-family:Cambria Math\">Champion is the correct spelling.</span></p>",
                    solution_hi: " <p><span style=\"font-family:Cambria Math\">14.(</span><span style=\"font-family:Cambria Math\">c)</span></p> <p><span style=\"font-family:Cambria Math\">Champion is the correct spelling.</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "7",
                    question_en: "<p>15.<span style=\"font-family: Cambria Math;\"> Select the INCORRECTLY spelt word.</span></p>\n",
                    question_hi: " <p>15.</span><span style=\"font-family:Cambria Math\"> Select the INCORRECTLY spelt word.</span></p>",
                    options_en: ["<p>Tryst</p>\n", "<p>Yoghurt</p>\n", 
                                "<p>Serrene</p>\n", "<p>Shopaholic</p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\n"],
                    options_hi: [" <p> Tryst</span></p>", " <p> Yoghurt</span></p>",
                                " <p> Serrene</span></p>", " <p> Shopaholic</span></p>"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">15.(</span><span style=\"font-family: Cambria Math;\">c)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Serene is the correct spelling.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Tryst - meeting place</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Serene - calm and peaceful</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Shopaholic&nbsp; - a person who enjoys shopping very much and does it a lot</span></p>\n",
                    solution_hi: " <p><span style=\"font-family:Cambria Math\">15.(</span><span style=\"font-family:Cambria Math\">c)</span></p> <p><span style=\"font-family:Cambria Math\">Serene is the correct spelling.</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "16",
                    section: "7",
                    question_en: "<p>16. <span style=\"font-family: Cambria Math;\">Select the option that can be used as a one-word substitute for the underlined group </span><span style=\"font-family: Cambria Math;\">of words.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">The rich man was known all throughout the country for his noble deeds, kindness and </span><span style=\"font-family: Cambria Math;\">as </span><span style=\"text-decoration: underline;\"><strong><span style=\"font-family: Cambria Math;\">one who makes an active effort to promote human welfare.</span></strong></span></p>\n",
                    question_hi: "<p>16. <span style=\"font-family: Cambria Math;\">Select the option that can be used as a one-word substitute for the underlined group </span><span style=\"font-family: Cambria Math;\">of words.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">The rich man was known all throughout the country for his noble deeds, kindness and </span><span style=\"font-family: Cambria Math;\">as<span style=\"text-decoration: underline;\"> </span></span><strong><span style=\"text-decoration: underline;\"><span style=\"font-family: Cambria Math;\">one who makes an active effort to promote human welfare.</span></span></strong></p>\n",
                    options_en: ["<p>Squanderer</p>\n", "<p>Spendthrift</p>\n", 
                                "<p>Pacifist</p>\n", "<p>Philanthropist</p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\n"],
                    options_hi: ["<p>Squanderer</p>\n", "<p>Spendthrift</p>\n",
                                "<p>Pacifist</p>\n", "<p>Philanthropist</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">16.(</span><span style=\"font-family: Cambria Math;\">d)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Philanthropist - One who makes an active effort to promote human welfare.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Squanderer- One who spends extravagantly or foolishly </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Spendthrift-a person who spends money in an extravagant, irresponsible way.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Pacifist-a person who believes that war and violence are unjustifiable.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">So</span><span style=\"font-family: Cambria Math;\"> option d is the most appropriate answer.</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">16.(</span><span style=\"font-family: Cambria Math;\">d)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Philanthropist - One who makes an active effort to promote human welfare.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Squanderer- One who spends extravagantly or foolishly </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Spendthrift-a person who spends money in an extravagant, irresponsible way.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Pacifist-a person who believes that war and violence are unjustifiable.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">So</span><span style=\"font-family: Cambria Math;\"> option d is the most appropriate answer.</span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "17",
                    section: "7",
                    question_en: " <p>17. </span><span style=\"font-family:Cambria Math\">Select the most appropriate meaning of the given idiom.</span></p> <p><span style=\"font-family:Cambria Math\">Pocket an insult</span></p>",
                    question_hi: " <p>17. </span><span style=\"font-family:Cambria Math\">Select the most appropriate meaning of the given idiom.</span></p> <p><span style=\"font-family:Cambria Math\">Pocket an insult</span></p>",
                    options_en: [" <p> Insult someone strongly</span></p>", " <p> Endure insult and protest</span></p>", 
                                " <p> Tolerate insult without protest</span></p>", " <p> Rebel against insult</span></p> <p><span style=\"font-family:Cambria Math\"> </span></p>"],
                    options_hi: [" <p> Insult someone strongly</span></p>", " <p> Endure insult and protest</span></p>",
                                " <p> Tolerate insult without protest</span></p>", " <p> Rebel against insult</span></p>"],
                    solution_en: " <p><span style=\"font-family:Cambria Math\">17.(</span><span style=\"font-family:Cambria Math\">c)</span></p> <p><span style=\"font-family:Cambria Math\">Pocket an insult - Tolerate insult without protest</span></p> <p><span style=\"font-family:Cambria Math\">Eg</span><span style=\"font-family:Cambria Math\">- The students had no other choice but to keep quiet and pocket the insult.</span></p>",
                    solution_hi: " <p><span style=\"font-family:Cambria Math\">17.(</span><span style=\"font-family:Cambria Math\">c)</span></p> <p><span style=\"font-family:Cambria Math\">Pocket an insult - Tolerate insult without protest</span></p> <p><span style=\"font-family:Cambria Math\">Eg</span><span style=\"font-family:Cambria Math\">- The students had no other choice but to keep quiet and pocket the insult.</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "18",
                    section: "7",
                    question_en: " <p>18.</span><span style=\"font-family:Cambria Math\"> Select the most appropriate option to fill in the blank.</span></p> <p><span style=\"font-family:Cambria Math\">Settlers ______ a new colony in the early 18th century.</span></p>",
                    question_hi: " <p>18.</span><span style=\"font-family:Cambria Math\"> Select the most appropriate option to fill in the blank.</span></p> <p><span style=\"font-family:Cambria Math\">Settlers ______ a new colony in the early 18th century.</span></p>",
                    options_en: [" <p> fixed</span></p>", " <p> extended</span></p>", 
                                " <p> established</span></p>", " <p> entrenched</span></p> <p><span style=\"font-family:Cambria Math\"> </span></p>"],
                    options_hi: [" <p> fixed</span></p>", " <p> extended</span></p>",
                                " <p> established</span></p>", " <p> entrenched</span></p>"],
                    solution_en: " <p><span style=\"font-family:Cambria Math\">18.(</span><span style=\"font-family:Cambria Math\">c)</span></p> <p><span style=\"font-family:Cambria Math\">When a new colony is set up the verb establish is </span><span style=\"font-family:Cambria Math\">used.Settlers</span><span style=\"font-family:Cambria Math\"> establish new colonies.</span></p>",
                    solution_hi: " <p><span style=\"font-family:Cambria Math\">18.(</span><span style=\"font-family:Cambria Math\">c)</span></p> <p><span style=\"font-family:Cambria Math\">When a new colony is set up the verb establish is </span><span style=\"font-family:Cambria Math\">used.Settlers</span><span style=\"font-family:Cambria Math\"> establish new colonies.</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "19",
                    section: "7",
                    question_en: "<p>19.<span style=\"font-family: Cambria Math;\"> In the following questions a statement has been given with highlighted text. Select the </span><span style=\"font-family: Cambria Math;\">options that can replace the text with correct idiom or phrase.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Reginald </span><strong><span style=\"text-decoration: underline;\"><span style=\"font-family: Cambria Math;\">betrayed his closest friend.</span><span style=\"font-family: Cambria Math;\"> It was big shock for all.</span></span></strong></p>\n",
                    question_hi: "<p>19.<span style=\"font-family: Cambria Math;\"> In the following questions a statement has been given with highlighted text. Select the </span><span style=\"font-family: Cambria Math;\">options that can replace the text with correct idiom or phrase.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Reginald </span><strong><span style=\"text-decoration: underline;\"><span style=\"font-family: Cambria Math;\">betrayed his closest friend.</span><span style=\"font-family: Cambria Math;\"> It was big shock for all.</span></span></strong></p>\n",
                    options_en: ["<p>Stab someone in the back</p>\n", "<p>Looking to your laurels</p>\n", 
                                "<p>A black sheep</p>\n", "<p>Every cloud has a silver lining</p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\n"],
                    options_hi: ["<p>Stab someone in the back</p>\n", "<p>Looking to your laurels</p>\n",
                                "<p>A black sheep</p>\n", "<p>Every cloud has a silver lining</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">19.(</span><span style=\"font-family: Cambria Math;\">a)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Stab someone in the back means to betray someone and to harm someone who trusted you.</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">19.(</span><span style=\"font-family: Cambria Math;\">a)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Stab someone in the back means to betray someone and to harm someone who trusted you.</span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "20",
                    section: "7",
                    question_en: "<p>20.<span style=\"font-family: Cambria Math;\"> Select the INCORRECTLY spelt word.</span></p>\n",
                    question_hi: " <p>20.</span><span style=\"font-family:Cambria Math\"> Select the INCORRECTLY spelt word.</span></p>",
                    options_en: ["<p>Hilarious</p>\n", "<p>Demolish</p>\n", 
                                "<p><span style=\"font-family: Cambria Math;\">Magnificient</span></p>\n", "<p>Gargantuan</p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\n"],
                    options_hi: [" <p> Hilarious</span></p>", " <p> Demolish</span></p>",
                                " <p> </span><span style=\"font-family:Cambria Math\">Magnificient</span></p>", " <p> Gargantuan</span></p>"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">20.(</span><span style=\"font-family: Cambria Math;\">c)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Magnificent is the correct spelling.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Hilarious - extremely amusing&nbsp;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Demolish - huge&nbsp;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Magnificent - extremely beautiful, elaborate, or impressive</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Gargantuan - very huge</span></p>\n",
                    solution_hi: " <p><span style=\"font-family:Cambria Math\">20.(</span><span style=\"font-family:Cambria Math\">c)</span></p> <p><span style=\"font-family:Cambria Math\">Magnificent is the correct spelling. </span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "21",
                    section: "7",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">21.</span><strong><span style=\"font-family: Cambria Math;\">Cloze</span></strong><span style=\"font-family: Cambria Math;\"><strong> Test :</strong> </span><span style=\"font-family: Cambria Math;\">In the following passage, some words have been deleted. Read the passage carefully and </span><span style=\"font-family: Cambria Math;\">select the most appropriate option to fill in each blank.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">There was once a young man who wished to (</span><span style=\"font-family: Cambria Math;\">21)_</span><span style=\"font-family: Cambria Math;\">________ his heart&rsquo;s desire. And while that </span><span style=\"font-family: Cambria Math;\">is, as beginnings go, there was much about this young man and what happened to him that </span><span style=\"font-family: Cambria Math;\">was unusual, (</span><span style=\"font-family: Cambria Math;\">22)_</span><span style=\"font-family: Cambria Math;\">________ even he never knew the whole of it. The tale started. The town of </span><span style=\"font-family: Cambria Math;\">Wall stands today as it has (</span><span style=\"font-family: Cambria Math;\">23)_</span><span style=\"font-family: Cambria Math;\">________ for six hundred years, on a high (24)_________ of </span><span style=\"font-family: Cambria Math;\">granite slab amidst small forest woodland. There is a road from Wall, a winding track rising </span><span style=\"font-family: Cambria Math;\">sharply up from the forest, where it is (</span><span style=\"font-family: Cambria Math;\">25)_</span><span style=\"font-family: Cambria Math;\">________with rocks and small stones.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Select the most appropriate option to fill in blank number 21.</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">21. </span><strong><span style=\"font-family: Cambria Math;\">Cloze</span></strong><span style=\"font-family: Cambria Math;\"><strong> Test :</strong> </span><span style=\"font-family: Cambria Math;\">In the following passage, some words have been deleted. Read the passage carefully and </span><span style=\"font-family: Cambria Math;\">select the most appropriate option to fill in each blank.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">There was once a young man who wished to (</span><span style=\"font-family: Cambria Math;\">21)_</span><span style=\"font-family: Cambria Math;\">________ his heart&rsquo;s desire. And while that </span><span style=\"font-family: Cambria Math;\">is, as beginnings go, there was much about this young man and what happened to him that </span><span style=\"font-family: Cambria Math;\">was unusual, (</span><span style=\"font-family: Cambria Math;\">22)_</span><span style=\"font-family: Cambria Math;\">________ even he never knew the whole of it. The tale started. The town of </span><span style=\"font-family: Cambria Math;\">Wall stands today as it has (</span><span style=\"font-family: Cambria Math;\">23)_</span><span style=\"font-family: Cambria Math;\">________ for six hundred years, on a high (24)_________ of </span><span style=\"font-family: Cambria Math;\">granite slab amidst small forest woodland. There is a road from Wall, a winding track rising </span><span style=\"font-family: Cambria Math;\">sharply up from the forest, where it is (</span><span style=\"font-family: Cambria Math;\">25)_</span><span style=\"font-family: Cambria Math;\">________with rocks and small stones.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Select the most appropriate option to fill in blank number 21.</span></p>\n",
                    options_en: ["<p>appreciate</p>\n", "<p>achieve</p>\n", 
                                "<p>prevail</p>\n", "<p>value</p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\n"],
                    options_hi: ["<p>appreciate</p>\n", "<p>achieve</p>\n",
                                "<p>prevail</p>\n", "<p>value</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">21.(</span><span style=\"font-family: Cambria Math;\">b)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">To achieve one&rsquo;s heart&rsquo;s desire is to get what one wants.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">So</span><span style=\"font-family: Cambria Math;\"> option b is the most appropriate answer.</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">21.(</span><span style=\"font-family: Cambria Math;\">b)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">To achieve one&rsquo;s heart&rsquo;s desire is to get what one wants.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">So</span><span style=\"font-family: Cambria Math;\"> option b is the most appropriate answer.</span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "22",
                    section: "7",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">22. </span><strong><span style=\"font-family: Cambria Math;\">Cloze</span></strong><span style=\"font-family: Cambria Math;\"><strong> Test :</strong> </span><span style=\"font-family: Cambria Math;\">In the following passage, some words have been deleted. Read the passage carefully and </span><span style=\"font-family: Cambria Math;\">select the most appropriate option to fill in each blank.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">There was once a young man who wished to (</span><span style=\"font-family: Cambria Math;\">21)_</span><span style=\"font-family: Cambria Math;\">________ his heart&rsquo;s desire. And while that </span><span style=\"font-family: Cambria Math;\">is, as beginnings go, there was much about this young man and what happened to him that </span><span style=\"font-family: Cambria Math;\">was unusual, (</span><span style=\"font-family: Cambria Math;\">22)_</span><span style=\"font-family: Cambria Math;\">________ even he never knew the whole of it. The tale started. The town of </span><span style=\"font-family: Cambria Math;\">Wall stands today as it has (</span><span style=\"font-family: Cambria Math;\">23)_</span><span style=\"font-family: Cambria Math;\">________ for six hundred years, on a high (24)_________ of </span><span style=\"font-family: Cambria Math;\">granite slab amidst small forest woodland. There is a road from Wall, a winding track rising </span><span style=\"font-family: Cambria Math;\">sharply up from the forest, where it is (</span><span style=\"font-family: Cambria Math;\">25)_</span><span style=\"font-family: Cambria Math;\">________with rocks and small stones.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Select the most appropriate option to fill in blank number 22.</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">22. </span><strong><span style=\"font-family: Cambria Math;\">Cloze</span></strong><span style=\"font-family: Cambria Math;\"><strong> Test :</strong> </span><span style=\"font-family: Cambria Math;\">In the following passage, some words have been deleted. Read the passage carefully and </span><span style=\"font-family: Cambria Math;\">select the most appropriate option to fill in each blank.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">There was once a young man who wished to (</span><span style=\"font-family: Cambria Math;\">21)_</span><span style=\"font-family: Cambria Math;\">________ his heart&rsquo;s desire. And while that </span><span style=\"font-family: Cambria Math;\">is, as beginnings go, there was much about this young man and what happened to him that </span><span style=\"font-family: Cambria Math;\">was unusual, (</span><span style=\"font-family: Cambria Math;\">22)_</span><span style=\"font-family: Cambria Math;\">________ even he never knew the whole of it. The tale started. The town of </span><span style=\"font-family: Cambria Math;\">Wall stands today as it has (</span><span style=\"font-family: Cambria Math;\">23)_</span><span style=\"font-family: Cambria Math;\">________ for six hundred years, on a high (24)_________ of </span><span style=\"font-family: Cambria Math;\">granite slab amidst small forest woodland. There is a road from Wall, a winding track rising </span><span style=\"font-family: Cambria Math;\">sharply up from the forest, where it is (</span><span style=\"font-family: Cambria Math;\">25)_</span><span style=\"font-family: Cambria Math;\">________with rocks and small stones.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Select the most appropriate option to fill in blank number 22.</span></p>\n",
                    options_en: ["<p>despite</p>\n", "<p>even though</p>\n", 
                                "<p>although</p>\n", "<p>in spite</p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\n"],
                    options_hi: ["<p>despite</p>\n", "<p>even though</p>\n",
                                "<p>although</p>\n", "<p>in spite</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">22.(</span><span style=\"font-family: Cambria Math;\">c)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Although is used to show contrast.</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">22.(</span><span style=\"font-family: Cambria Math;\">c)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Although is used to show contrast.</span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "23",
                    section: "7",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">23. </span><strong><span style=\"font-family: Cambria Math;\">Cloze</span></strong><span style=\"font-family: Cambria Math;\"><strong> Test :</strong> </span><span style=\"font-family: Cambria Math;\">In the following passage, some words have been deleted. Read the passage carefully and </span><span style=\"font-family: Cambria Math;\">select the most appropriate option to fill in each blank.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">There was once a young man who wished to (</span><span style=\"font-family: Cambria Math;\">21)_</span><span style=\"font-family: Cambria Math;\">________ his heart&rsquo;s desire. And while that </span><span style=\"font-family: Cambria Math;\">is, as beginnings go, there was much about this young man and what happened to him that </span><span style=\"font-family: Cambria Math;\">was unusual, (</span><span style=\"font-family: Cambria Math;\">22)_</span><span style=\"font-family: Cambria Math;\">________ even he never knew the whole of it. The tale started. The town of </span><span style=\"font-family: Cambria Math;\">Wall stands today as it has (</span><span style=\"font-family: Cambria Math;\">23)_</span><span style=\"font-family: Cambria Math;\">________ for six hundred years, on a high (24)_________ of </span><span style=\"font-family: Cambria Math;\">granite slab amidst small forest woodland. There is a road from Wall, a winding track rising </span><span style=\"font-family: Cambria Math;\">sharply up from the forest, where it is (</span><span style=\"font-family: Cambria Math;\">25)_</span><span style=\"font-family: Cambria Math;\">________with rocks and small stones.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Select the most appropriate option to fill in blank number 23.</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">23. </span><strong><span style=\"font-family: Cambria Math;\">Cloze</span></strong><span style=\"font-family: Cambria Math;\"><strong> Test :</strong> </span><span style=\"font-family: Cambria Math;\">In the following passage, some words have been deleted. Read the passage carefully and </span><span style=\"font-family: Cambria Math;\">select the most appropriate option to fill in each blank.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">There was once a young man who wished to (</span><span style=\"font-family: Cambria Math;\">21)_</span><span style=\"font-family: Cambria Math;\">________ his heart&rsquo;s desire. And while that </span><span style=\"font-family: Cambria Math;\">is, as beginnings go, there was much about this young man and what happened to him that </span><span style=\"font-family: Cambria Math;\">was unusual, (</span><span style=\"font-family: Cambria Math;\">22)_</span><span style=\"font-family: Cambria Math;\">________ even he never knew the whole of it. The tale started. The town of </span><span style=\"font-family: Cambria Math;\">Wall stands today as it has (</span><span style=\"font-family: Cambria Math;\">23)_</span><span style=\"font-family: Cambria Math;\">________ for six hundred years, on a high (24)_________ of </span><span style=\"font-family: Cambria Math;\">granite slab amidst small forest woodland. There is a road from Wall, a winding track rising </span><span style=\"font-family: Cambria Math;\">sharply up from the forest, where it is (</span><span style=\"font-family: Cambria Math;\">25)_</span><span style=\"font-family: Cambria Math;\">________with rocks and small stones.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Select the most appropriate option to fill in blank number 23.</span></p>\n",
                    options_en: ["<p>established</p>\n", "<p>stood</p>\n", 
                                "<p>erected</p>\n", "<p>edified</p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\n"],
                    options_hi: ["<p>established</p>\n", "<p>stood</p>\n",
                                "<p>erected</p>\n", "<p>edified</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">23.(</span><span style=\"font-family: Cambria Math;\">b)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">The words stands has already been used in the sentence so stood which is the past tense of stands will be </span><span style=\"font-family: Cambria Math;\">used.So</span><span style=\"font-family: Cambria Math;\"> option b is the most appropriate answer.</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">23.(</span><span style=\"font-family: Cambria Math;\">b)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">The words stands has already been used in the sentence so stood which is the past tense of stands will be </span><span style=\"font-family: Cambria Math;\">used.So</span><span style=\"font-family: Cambria Math;\"> option b is the most appropriate answer.</span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "24",
                    section: "7",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">24. </span><strong><span style=\"font-family: Cambria Math;\">Cloze</span></strong><span style=\"font-family: Cambria Math;\"><strong> Test :</strong> </span><span style=\"font-family: Cambria Math;\">In the following passage, some words have been deleted. Read the passage carefully and </span><span style=\"font-family: Cambria Math;\">select the most appropriate option to fill in each blank.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">There was once a young man who wished to (</span><span style=\"font-family: Cambria Math;\">21)_</span><span style=\"font-family: Cambria Math;\">________ his heart&rsquo;s desire. And while that </span><span style=\"font-family: Cambria Math;\">is, as beginnings go, there was much about this young man and what happened to him that </span><span style=\"font-family: Cambria Math;\">was unusual, (</span><span style=\"font-family: Cambria Math;\">22)_</span><span style=\"font-family: Cambria Math;\">________ even he never knew the whole of it. The tale started. The town of </span><span style=\"font-family: Cambria Math;\">Wall stands today as it has (</span><span style=\"font-family: Cambria Math;\">23)_</span><span style=\"font-family: Cambria Math;\">________ for six hundred years, on a high (24)_________ of </span><span style=\"font-family: Cambria Math;\">granite slab amidst small forest woodland. There is a road from Wall, a winding track rising </span><span style=\"font-family: Cambria Math;\">sharply up from the forest, where it is (</span><span style=\"font-family: Cambria Math;\">25)_</span><span style=\"font-family: Cambria Math;\">________with rocks and small stones.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Select the most appropriate option to fill in blank number 24.</span></p>\n",
                    question_hi: "<p>24.&nbsp;<strong><span style=\"font-family: Cambria Math;\">Cloze</span></strong><span style=\"font-family: Cambria Math;\"><strong> Test :</strong> </span><span style=\"font-family: Cambria Math;\">In the following passage, some words have been deleted. Read the passage carefully and </span><span style=\"font-family: Cambria Math;\">select the most appropriate option to fill in each blank.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">There was once a young man who wished to (</span><span style=\"font-family: Cambria Math;\">21)_</span><span style=\"font-family: Cambria Math;\">________ his heart&rsquo;s desire. And while that </span><span style=\"font-family: Cambria Math;\">is, as beginnings go, there was much about this young man and what happened to him that </span><span style=\"font-family: Cambria Math;\">was unusual, (</span><span style=\"font-family: Cambria Math;\">22)_</span><span style=\"font-family: Cambria Math;\">________ even he never knew the whole of it. The tale started. The town of </span><span style=\"font-family: Cambria Math;\">Wall stands today as it has (</span><span style=\"font-family: Cambria Math;\">23)_</span><span style=\"font-family: Cambria Math;\">________ for six hundred years, on a high (24)_________ of </span><span style=\"font-family: Cambria Math;\">granite slab amidst small forest woodland. There is a road from Wall, a winding track rising </span><span style=\"font-family: Cambria Math;\">sharply up from the forest, where it is (</span><span style=\"font-family: Cambria Math;\">25)_</span><span style=\"font-family: Cambria Math;\">________with rocks and small stones.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Select the most appropriate option to fill in blank number 24.</span></p>\n",
                    options_en: ["<p>furrow</p>\n", "<p>trench</p>\n", 
                                "<p>jut</p>\n", "<p>line</p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\n"],
                    options_hi: ["<p>furrow</p>\n", "<p>trench</p>\n",
                                "<p>jut</p>\n", "<p>line</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">24.(</span><span style=\"font-family: Cambria Math;\">c)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Jut</span><span style=\"font-family: Cambria Math;\"> means to extend out, beyond the main </span><span style=\"font-family: Cambria Math;\">body.The</span><span style=\"font-family: Cambria Math;\"> town stands on a jut of granite slab.</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">24.(</span><span style=\"font-family: Cambria Math;\">c)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Jut</span><span style=\"font-family: Cambria Math;\"> means to extend out, beyond the main </span><span style=\"font-family: Cambria Math;\">body.The</span><span style=\"font-family: Cambria Math;\"> town stands on a jut of granite slab.</span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "25",
                    section: "misc",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">25. </span><strong><span style=\"font-family: Cambria Math;\">Cloze</span></strong><span style=\"font-family: Cambria Math;\"><strong> Test :</strong> </span><span style=\"font-family: Cambria Math;\">In the following passage, some words have been deleted. Read the passage carefully and </span><span style=\"font-family: Cambria Math;\">select the most appropriate option to fill in each blank.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">There was once a young man who wished to (</span><span style=\"font-family: Cambria Math;\">21)_</span><span style=\"font-family: Cambria Math;\">________ his heart&rsquo;s desire. And while that </span><span style=\"font-family: Cambria Math;\">is, as beginnings go, there was much about this young man and what happened to him that </span><span style=\"font-family: Cambria Math;\">was unusual, (</span><span style=\"font-family: Cambria Math;\">22)_</span><span style=\"font-family: Cambria Math;\">________ even he never knew the whole of it. The tale started. The town of </span><span style=\"font-family: Cambria Math;\">Wall stands today as it has (</span><span style=\"font-family: Cambria Math;\">23)_</span><span style=\"font-family: Cambria Math;\">________ for six hundred years, on a high (24)_________ of </span><span style=\"font-family: Cambria Math;\">granite slab amidst small forest woodland. There is a road from Wall, a winding track rising </span><span style=\"font-family: Cambria Math;\">sharply up from the forest, where it is (</span><span style=\"font-family: Cambria Math;\">25)_</span><span style=\"font-family: Cambria Math;\">________with rocks and small stones.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Select the most appropriate option to fill in blank number 25.</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">25. </span><strong><span style=\"font-family: Cambria Math;\">Cloze</span></strong><span style=\"font-family: Cambria Math;\"><strong> Test :</strong> </span><span style=\"font-family: Cambria Math;\">In the following passage, some words have been deleted. Read the passage carefully and </span><span style=\"font-family: Cambria Math;\">select the most appropriate option to fill in each blank.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">There was once a young man who wished to (</span><span style=\"font-family: Cambria Math;\">21)_</span><span style=\"font-family: Cambria Math;\">________ his heart&rsquo;s desire. And while that </span><span style=\"font-family: Cambria Math;\">is, as beginnings go, there was much about this young man and what happened to him that </span><span style=\"font-family: Cambria Math;\">was unusual, (</span><span style=\"font-family: Cambria Math;\">22)_</span><span style=\"font-family: Cambria Math;\">________ even he never knew the whole of it. The tale started. The town of </span><span style=\"font-family: Cambria Math;\">Wall stands today as it has (</span><span style=\"font-family: Cambria Math;\">23)_</span><span style=\"font-family: Cambria Math;\">________ for six hundred years, on a high (24)_________ of </span><span style=\"font-family: Cambria Math;\">granite slab amidst small forest woodland. There is a road from Wall, a winding track rising </span><span style=\"font-family: Cambria Math;\">sharply up from the forest, where it is (</span><span style=\"font-family: Cambria Math;\">25)_</span><span style=\"font-family: Cambria Math;\">________with rocks and small stones.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Select the most appropriate option to fill in blank number 25.</span></p>\n",
                    options_en: ["<p>sequenced</p>\n", "<p>lined</p>\n", 
                                "<p>queued</p>\n", "<p>furnished</p>\n"],
                    options_hi: ["<p>sequenced</p>\n", "<p>lined</p>\n",
                                "<p>queued</p>\n", "<p>furnished</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">25.(</span><span style=\"font-family: Cambria Math;\">b)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">The rocks and small stones are lined. </span><span style=\"font-family: Cambria Math;\">So</span><span style=\"font-family: Cambria Math;\"> option b is the most appropriate answer.</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">25.(</span><span style=\"font-family: Cambria Math;\">b)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">The rocks and small stones are lined. </span><span style=\"font-family: Cambria Math;\">So</span><span style=\"font-family: Cambria Math;\"> option b is the most appropriate answer.</span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>