<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Smart Test Interface</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        .question-nav{position:fixed;top:70px;right:0;height:calc(100vh - 70px);width:320px;transition:all .3s;z-index:100;display:flex;flex-direction:column}
        .question-content{margin-right:0;transition:all .3s}
        .question-box{width:40px;height:40px;margin:5px;cursor:pointer}
        .attempted{background-color:#3498db!important;color:white}
        .correct{background-color:#2ecc71!important;color:white}
        .incorrect{background-color:#e74c3c!important;color:white}
        .current{border:3px solid #f39c12!important}
        #question-boxes{overflow-y:auto;max-height:calc(100vh - 150px);padding-bottom:15px}
        @media (max-width:768px){.question-nav{width:100%;right:-100%}.question-content{margin-right:0}}
    </style>
</head>
<body>
    <div class="container-fluid p-0">
        <!-- Header with controls -->
        <nav class="navbar navbar-expand-lg navbar-dark bg-primary sticky-top">
            <div class="container-fluid">
                <a class="navbar-brand" href="#">
                    <i class="fas fa-graduation-cap me-2"></i>Smart Test
                </a>
                <!-- Section selector -->
                <div class="navbar-nav me-auto section-tabs">
                    <select class="form-select" id="section-selector" onchange="switchSection(this.value)">
                        <option value="all">All Sections</option>
                    </select>
                </div>
                <div class="d-flex align-items-center">
                    <!-- Timer -->
                    <div class="bg-light text-primary rounded px-3 py-2 me-2 fw-bold" id="timer">
                        <i class="far fa-clock me-2"></i><span id="timer-display">100:00</span>
                    </div>
                    <!-- Submit button -->
                    <button class="btn btn-success me-2" id="submit-btn" onclick="submitTest()">
                        <i class="fas fa-check me-1"></i> Submit
                    </button>
                    <!-- Language toggle -->
                    <button class="btn btn-light me-2" id="lang-btn" onclick="toggleLanguage()">
                        <i class="fas fa-language"></i>
                    </button>
                    <!-- Menu toggle for mobile -->
                    <button class="btn btn-light d-lg-none" id="menu-toggle" onclick="toggleMenu()">
                        <i class="fas fa-th"></i>
                    </button>
                </div>
            </div>
        </nav>
        <div class="row m-0">
            <!-- Main content area -->
            <div class="col p-0 question-content" id="question-content">
                <div class="container py-4">
                    <!-- Question navigation -->
                    <div class="d-flex justify-content-between mb-4">
                        <button class="btn btn-outline-primary" onclick="prevQuestion()">
                            <i class="fas fa-chevron-left me-1"></i> Previous
                        </button>
                        <div class="fw-bold" id="question-counter">Question 1 of 100</div>
                        <button class="btn btn-outline-primary" onclick="nextQuestion()">
                            Next <i class="fas fa-chevron-right ms-1"></i>
                        </button>
                    </div>
                    <!-- Questions will be displayed here -->
                    <div id="questions-container"></div>
                </div>
            </div>
            <!-- Question navigation sidebar -->
            <div class="bg-light question-nav" id="question-nav">
                <div class="p-3">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h5 class="m-0">Questions</h5>
                        <div>
                            <span class="badge bg-secondary me-1" id="attempted-count">0</span>
                            <span class="badge bg-secondary" id="total-count">100</span>
                            <button class="btn btn-sm btn-outline-secondary ms-2 d-lg-none" onclick="toggleMenu()">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    </div>
                    <div class="d-flex flex-wrap" id="question-boxes"></div>
                </div>
            </div>
        </div>
        <!-- Results modal -->
        <div class="modal fade" id="results-modal" tabindex="-1" aria-hidden="true">
            <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content">
                    <div class="modal-header bg-primary text-white">
                        <h5 class="modal-title">Test Results</h5>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <div class="row text-center">
                            <div class="col-6 mb-3">
                                <div class="p-3 bg-primary bg-opacity-10 rounded">
                                    <h3 id="score-value">0</h3>
                                    <div class="small text-muted">Score</div>
                                </div>
                            </div>
                            <div class="col-6 mb-3">
                                <div class="p-3 bg-success bg-opacity-10 rounded">
                                    <h3 id="correct-value">0</h3>
                                    <div class="small text-muted">Correct</div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="p-3 bg-danger bg-opacity-10 rounded">
                                    <h3 id="incorrect-value">0</h3>
                                    <div class="small text-muted">Incorrect</div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="p-3 bg-secondary bg-opacity-10 rounded">
                                    <h3 id="unattempted-value">0</h3>
                                    <div class="small text-muted">Unattempted</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-primary" onclick="reviewTest()">Review Test</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 100 * 60;
        let resultsModal;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            resultsModal = new bootstrap.Modal(document.getElementById('results-modal'));
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 768) {
                document.getElementById('question-content').style.marginRight = '320px';
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["6"] = {
                name: "Reasoning",
                start: 0,
                end: 23
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="6">Reasoning</option>`;
            sections["18"] = {
                name: "General Awareness",
                start: 24,
                end: 48
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="18">General Awareness</option>`;
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 49,
                end: 73
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["7"] = {
                name: "English",
                start: 74,
                end: 98
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="7">English</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 99,
                end: 99
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "6",
                    question_en: "<p>1. Select the option that represents the letters that, when sequentially placed from left to right in the blanks, will complete the letter series.<br>_cb_ _amcc_eaa_cc_e_ _m</p>",
                    question_hi: "<p>1. उस विकल्प का चयन करें, जो उन अक्षरों का प्रतिनिधित्व करता है, जो रिक्त स्थानों में क्रमिक रूप से बाएँ से दाएँ रखे जाने पर दी गई अक्षर शृंखला को पूरा करेंगे।<br>_cb_ _amcc_eaa_cc_e_ _m</p>",
                    options_en: [
                        "<p>ceabmbea</p>",
                        "<p>caebmbea</p>",
                        "<p>caabmbaa</p>",
                        "<p>ceabmbaa</p>"
                    ],
                    options_hi: [
                        "<p>ceabmbea</p>",
                        "<p>caebmbea</p>",
                        "<p>caabmbaa</p>",
                        "<p>ceabmbaa</p>"
                    ],
                    solution_en: "<p>1.(d)<br><span style=\"text-decoration: underline;\"><strong>c</strong></span>cb<span style=\"text-decoration: underline;\"><strong>ea</strong></span>am / cc<strong><span style=\"text-decoration: underline;\">b</span></strong>eaa<span style=\"text-decoration: underline;\"><strong>m</strong></span> / cc<span style=\"text-decoration: underline;\"><strong>b</strong></span>e<span style=\"text-decoration: underline;\"><strong>aa</strong></span>m</p>",
                    solution_hi: "<p>1.(d)<br><span style=\"text-decoration: underline;\"><strong>c</strong></span>cb<span style=\"text-decoration: underline;\"><strong>ea</strong></span>am / cc<strong><span style=\"text-decoration: underline;\">b</span></strong>eaa<span style=\"text-decoration: underline;\"><strong>m</strong></span> / cc<span style=\"text-decoration: underline;\"><strong>b</strong></span>e<span style=\"text-decoration: underline;\"><strong>aa</strong></span>m</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "6",
                    question_en: "<p>2. &lsquo;Conceal&rsquo; is related to &lsquo;Disclose&rsquo; in the same way as &lsquo;Identical&rsquo; is related to &lsquo;________&rsquo;. <br>(The words must be considered as meaningful English words and must not be related to each other based on the number of letters/number of consonants/vowels in the word.)</p>",
                    question_hi: "<p>2. \'छिपाना\' (Conceal), \'प्रकट करना\' (Disclose) से उसी प्रकार संबंधित है, जिस प्रकार \'समरूप\' (Identical) \'________\' से संबंधित है। <br>(शब्दों को सार्थक हिंदी शब्द माना जाना चाहिए और शब्&zwj;दों को अक्षरों की संख्या/व्यंजनों/स्वरों की संख्या के आधार पर एक दूसरे से संबंधित नहीं किया जाना चाहिए।</p>",
                    options_en: [
                        "<p>Analogous</p>",
                        "<p>Homogeneous</p>",
                        "<p>Similar</p>",
                        "<p>Distinct</p>"
                    ],
                    options_hi: [
                        "<p>अनुरूप (Analogous)</p>",
                        "<p>सजातीय (Homogeneous)</p>",
                        "<p>समान (Similar)</p>",
                        "<p>भिन्&zwj;न (Distinct)</p>"
                    ],
                    solution_en: "<p>2.(d)<br>As &lsquo;Conceal&rsquo; is the antonym of &lsquo;Disclose&rsquo; similarly &lsquo;Identical&rsquo; is the antonym of &lsquo;Distinct&rsquo;</p>",
                    solution_hi: "<p>2.(d)<br>जिस प्रकार \'छिपाना\' &lsquo; (Conceal), \'प्रकट करने \' (&lsquo;Disclose&rsquo;) का विपरीतार्थी है उसी प्रकार \'समरूप\' (&lsquo;Identical&rsquo;), भिन्&zwj;न &lsquo;Distinct&rsquo;का विपरीतार्थी है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "6",
                    question_en: "<p>3. Select the option figure in which the given figure (X) is embedded as its part (rotation is NOT allowed).<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739263413082.png\" alt=\"rId4\" width=\"90\"></p>",
                    question_hi: "<p>3. उस विकल्प आकृति का चयन कीजिए जिसमें दी गई आकृति (X) उसके एक भाग के रूप में सन्निहित है (घूर्णन की अनुमति नहीं है)।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739263413082.png\" alt=\"rId4\" width=\"90\"></p>",
                    options_en: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739263413325.png\" alt=\"rId5\" width=\"90\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739263413533.png\" alt=\"rId6\" width=\"90\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739263413730.png\" alt=\"rId7\" width=\"90\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739263413983.png\" alt=\"rId8\" width=\"90\"></p>"
                    ],
                    options_hi: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739263413325.png\" alt=\"rId5\" width=\"90\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739263413533.png\" alt=\"rId6\" width=\"90\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739263413730.png\" alt=\"rId7\" width=\"90\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739263413983.png\" alt=\"rId8\" width=\"90\"></p>"
                    ],
                    solution_en: "<p>3.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739263414167.png\" alt=\"rId9\" width=\"90\"></p>",
                    solution_hi: "<p>3.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739263414167.png\" alt=\"rId9\" width=\"90\"></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "6",
                    question_en: "<p>4. Select the correct combination of mathematical signs to sequentially place from left to right in the blanks below to balance the given equation. <br>8 _ 27 _ 9 _ 6 _ 5 = 0</p>",
                    question_hi: "<p>4. उन गणितीय चिह्नों के सही संयोजन का चयन करें जिन्&zwj;हें नीचे दिए गए रिक्त स्थानों में क्रमिक रूप से बाएँ से दाएँ रखने पर दिया गया समीकरण संतुलित हो जाएगा। <br>8 _ 27 _ 9 _ 6 _ 5 = 0</p>",
                    options_en: [
                        "<p>&divide;, +, &minus;, +</p>",
                        "<p>+, &divide;, &minus;, &minus;</p>",
                        "<p>&minus;, &divide;, +, +</p>",
                        "<p>&minus;, +, &divide;, +</p>"
                    ],
                    options_hi: [
                        "<p>&divide;, +, &minus;, +</p>",
                        "<p>+, &divide;, &minus;, &minus;</p>",
                        "<p>&minus;, &divide;, +, +</p>",
                        "<p>&minus;, +, &divide;, +</p>"
                    ],
                    solution_en: "<p>4.(b) <strong>Given:&nbsp; &nbsp;</strong> 8 _ 27 _ 9 _ 6 _ 5 = 0<br>After checking all the options, only option (b) satisfied.<br>8 + 27 &divide; 9 - 6 - 5 = 0<br>8 + 3 - 6 - 5 = 0<br>11 - 6 - 5 = 0<br>11 - 11 = 0<br>L.H.S = R.H.S</p>",
                    solution_hi: "<p>4.(b) <strong>दिया गया</strong>:&nbsp; &nbsp; 8 _ 27 _ 9 _ 6 _ 5 = 0<br>सभी विकल्पों की जांच करने के बाद, केवल विकल्प (b) संतुष्ट करता है।<br>8 + 27 &divide; 9 - 6 - 5 = 0<br>8 + 3 - 6 - 5 = 0<br>11 - 6 - 5 = 0<br>11 - 11 = 0<br>L.H.S = R.H.S</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "6",
                    question_en: "<p>5. Four letter-clusters have been given, out of which three are alike in some manner and one is different. Select the letter-cluster that is different. <br>(Note : The odd one out is not based on the number of consonants/vowels or their position in the letter cluster.)</p>",
                    question_hi: "<p>5. निम्नलिखित चार अक्षर-समूहों में से तीन अक्षर-समूह एक निश्चित प्रकार से समान हैं और इस प्रकार एक समूह बनाते हैं। वह कौन-सा अक्षर-समूह है जो उस समूह से संबंधित नहीं है?<br>(ध्यान दें : असंगत अक्षर-समूह, उस अक्षर-समूह में व्यंजनों/स्वरों की संख्या या उनके स्थान पर आधारित नहीं है।)</p>",
                    options_en: [
                        "<p>AFED</p>",
                        "<p>RWVU</p>",
                        "<p>PUTS</p>",
                        "<p>IMNL</p>"
                    ],
                    options_hi: [
                        "<p>AFED</p>",
                        "<p>RWVU</p>",
                        "<p>PUTS</p>",
                        "<p>IMNL</p>"
                    ],
                    solution_en: "<p>5.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739263414278.png\" alt=\"rId10\" width=\"100\">&nbsp; ,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739263414457.png\" alt=\"rId11\" width=\"100\">&nbsp; &nbsp;,&nbsp;<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739263414571.png\" alt=\"rId12\" width=\"100\"><br>But,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739263414774.png\" alt=\"rId13\" width=\"100\"><br><br></p>",
                    solution_hi: "<p>5.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739263414278.png\" alt=\"rId10\" width=\"100\">&nbsp; ,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739263414457.png\" alt=\"rId11\" width=\"100\">&nbsp; &nbsp;,&nbsp;<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739263414571.png\" alt=\"rId12\" width=\"100\"><br>लेकिन,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739263414774.png\" alt=\"rId13\" width=\"100\"></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "6",
                    question_en: "<p>6. The question contains pairs of words that are related to each other in a certain way. Three of the following four word pairs are alike as these have the same relationship and thus form a group. Which word pair is the one that DOES NOT belong to that group?<br>(The words must be considered as meaningful English words and must not be related to each other based on the number of letters / number of consonants/vowels in the word.)</p>",
                    question_hi: "<p>6. प्रश्न में ऐसे शब्द युग्म हैं जो एक निश्चित तरीके से एक-दूसरे से संबंधित हैं। निम्नलिखित चार शब्द युग्मों में से तीन शब्द युग्म एक समान हैं क्योंकि इनमें समान संबंध है और इस प्रकार वे एक समूह बनाते हैं। निम्नलिखित में से कौन-सा शब्द युग्म उस समूह से संबंधित नहीं है? <br>(शब्दों को अर्थपूर्ण अंग्रेजी/हिंदी शब्द माना जाना चाहिए और शब्द में अक्षरों /व्यंजनों /स्वरों की संख्या के आधार पर एक दूसरे से संबंधित नहीं होने चाहिए।)</p>",
                    options_en: [
                        "<p>Shoes &ndash; feet</p>",
                        "<p>Book &ndash; pages</p>",
                        "<p>Spectacles &ndash; lens</p>",
                        "<p>Flowers &ndash; petals</p>"
                    ],
                    options_hi: [
                        "<p>जूते &ndash; पैर</p>",
                        "<p>पुस्तक &ndash; पृष्ठ</p>",
                        "<p>चश्मा &ndash; लेंस</p>",
                        "<p>फूल &ndash; पंखुड़ियाँ</p>"
                    ],
                    solution_en: "<p>6.(a)<br>Books contain pages, spectacles are made up of lenses, and flowers have petals but we wear shoes on our feet.</p>",
                    solution_hi: "<p>6.(a)<br>किताबों में पन्ने होते हैं, चश्मा लेंस से बना होता है, और फूलों में पंखुड़ियाँ होती हैं लेकिन हम अपने पैरों में जूते पहनते हैं।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "6",
                    question_en: "<p>7. In a certain code language, &lsquo;TERRAIN&rsquo; is written as &lsquo;VETTAIP&rsquo; and &lsquo;TRAFFIC&rsquo; is written as &lsquo;VTAHHIE&rsquo;. How will &lsquo;MOTOR&rsquo; be written in that language?</p>",
                    question_hi: "<p>7. एक निश्चित कोड भाषा में, \'TERRAIN\' को \'VETTAIP\' लिखा जाता है और \'TRAFFIC\' को \'VTAHHIE\' लिखा जाता है। उसी भाषा में \'MOTOR\' को कैसे लिखा जाएगा?</p>",
                    options_en: [
                        "<p>OOVOT</p>",
                        "<p>VOOOT</p>",
                        "<p>OOTVO</p>",
                        "<p>OVOTO</p>"
                    ],
                    options_hi: [
                        "<p>OOVOT</p>",
                        "<p>VOOOT</p>",
                        "<p>OOTVO</p>",
                        "<p>OVOTO</p>"
                    ],
                    solution_en: "<p>7.(a) <strong>Logic</strong> :- consonants increased by 2 and vowels remain the same.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739263414931.png\" alt=\"rId14\" width=\"159\" height=\"85\">&nbsp;and<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739263415093.png\" alt=\"rId15\" width=\"170\" height=\"91\"><br>Similarly, <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739263415196.png\" alt=\"rId16\" width=\"134\" height=\"98\"></p>",
                    solution_hi: "<p>7.(a)&nbsp; <strong>तर्क</strong> : - व्यंजनों में 2 की वृद्धि होती है और स्वर वही रहते हैं।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739263414931.png\" alt=\"rId14\" width=\"159\" height=\"85\">&nbsp;और<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739263415093.png\" alt=\"rId15\" width=\"170\" height=\"91\"><br>उसी प्रकार,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739263415196.png\" alt=\"rId16\" width=\"145\" height=\"106\"></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "6",
                    question_en: "<p>8. In a certain code language, \'EXCESS\' is written as \'726572121\', \'ENERGY\' is written as \'716720927\', how will \'ESCAPE\' be written in that language?</p>",
                    question_hi: "<p>8. एक निश्चित कूट भाषा में, \'EXCESS\' को \'726572121\' लिखा जाता है, \'ENERGY\' को \'716720927\' लिखा जाता है, उसी कूट भाषा में \'ESCAPE\' को कैसे लिखा जाएगा?</p>",
                    options_en: [
                        "<p>72153165</p>",
                        "<p>72151167</p>",
                        "<p>72153187</p>",
                        "<p>71933187</p>"
                    ],
                    options_hi: [
                        "<p>72153165</p>",
                        "<p>72151167</p>",
                        "<p>72153187</p>",
                        "<p>71933187</p>"
                    ],
                    solution_en: "<p>8.(c)<br><strong>Logic:-</strong> place value of each letter + 2<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739263415316.png\" alt=\"rId17\"> ,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739263415466.png\" alt=\"rId18\"><br>Similarly, <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739263415601.png\" alt=\"rId19\"></p>",
                    solution_hi: "<p>8.(c) <strong>तर्क</strong> :- प्रत्येक अक्षर का स्थानीय मान + 2<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739263415731.png\" alt=\"rId20\">,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739263415845.png\" alt=\"rId21\"><br>इसी प्रकार , <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739263415971.png\" alt=\"rId22\"></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "6",
                    question_en: "<p>9. In a certain code language, &lsquo;derive the formula&rsquo; is written as &lsquo;le ro da&rsquo; and &lsquo;formula to win&rsquo; is written as &lsquo;ka wb ro&rsquo;. How will &lsquo;formula&rsquo; be written in the given language?</p>",
                    question_hi: "<p>9. किसी निश्चित कूट भाषा में, &lsquo;derive the formula&rsquo; को &lsquo;le ro da&rsquo; लिखा जाता है और &lsquo;formula to win&rsquo; को &lsquo;ka wb ro&rsquo; लिखा जाता है। इसी भाषा में &lsquo;formula&rsquo; को कैसे लिखा जाएगा?</p>",
                    options_en: [
                        "<p>wb</p>",
                        "<p>ro</p>",
                        "<p>le</p>",
                        "<p>da</p>"
                    ],
                    options_hi: [
                        "<p>wb</p>",
                        "<p>ro</p>",
                        "<p>le</p>",
                        "<p>da</p>"
                    ],
                    solution_en: "<p>9.(b) derive the formula :- le ro da&hellip;&hellip;(i)<br>formula to win :- ka wb ro&hellip;&hellip;..(ii)<br>From (i) and (ii) the code of &lsquo;formula&rsquo; = &lsquo;ro&rsquo;.</p>",
                    solution_hi: "<p>9.(b) derive the formula :- le ro da&hellip;&hellip;(i)<br>formula to win :- ka wb ro&hellip;&hellip;..(ii)<br>(i) और (ii) से \'formula\' का कोड = \'ro\'।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "6",
                    question_en: "10. What should come in place of the question mark (?) in the given series based on the English alphabetical order?<br />CVF, GTJ, KRN, ?, SNV",
                    question_hi: "10. अंग्रेजी वर्णमाला क्रम के आधार पर दी गई शृंखला में प्रश्न चिह्न (?) के स्थान पर क्या आना चाहिए?<br />CVF, GTJ, KRN, ?, SNV",
                    options_en: [
                        " OPR",
                        " ROQ",
                        " OPS",
                        " PRO"
                    ],
                    options_hi: [
                        " OPR",
                        " ROQ",
                        " OPS",
                        " PRO"
                    ],
                    solution_en: "<p>10.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739263416137.png\" alt=\"rId23\" width=\"359\" height=\"91\"></p>",
                    solution_hi: "<p>10.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739263416137.png\" alt=\"rId23\" width=\"359\" height=\"91\"></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "6",
                    question_en: "<p>11. Three statements are given followed by three conclusions numbered I, II and III. Assuming the statements to be true, even if they seem to be at variance with commonly known facts, decide which of the conclusions logically follow(s) from the statements.<br><strong>Statements:</strong><br>Some cups are glasses. <br>No glass is a stone. <br>All stones are metals.<br><strong>Conclusions:</strong><br>I. All cups can never be stones. <br>II. No cup is a metal. <br>III. All metals can never be glasses.</p>",
                    question_hi: "<p>11. तीन कथन और उसके बाद तीन निष्कर्ष I, II और III दिए गए हैं। कथनों को सत्य मानते हुए, भले ही वे सामान्य रूप से ज्ञात तथ्यों से भिन्न प्रतीत होते हों, तय कीजिए कि कौन-सा/से निष्कर्ष इन कथनों का तार्किक रूप से अनुसरण करता है/करते हैं। <br><strong>कथन:</strong> <br>कुछ कप, गिलास हैं। <br>कोई भी गिलास, पत्थर नहीं है। <br>सभी पत्थर, धातुएं हैं। <br><strong>निष्कर्ष:</strong> <br>I. सभी कप कभी भी पत्थर नहीं हो सकते हैं। <br>II. कोई भी कप, धातु नहीं है। <br>III. सभी धातुएं कभी भी गिलास नहीं हो सकती हैं।</p>",
                    options_en: [
                        "<p>Only II and III conclusion follow</p>",
                        "<p>All I, II and III conclusion follow</p>",
                        "<p>Only conclusion I follows</p>",
                        "<p>Only I and III conclusion follow</p>"
                    ],
                    options_hi: [
                        "<p>केवल निष्&zwj;कर्ष II और III अनुसरण करते हैं</p>",
                        "<p>निष्&zwj;कर्ष I, II और III सभी अनुसरण करते हैं</p>",
                        "<p>केवल निष्&zwj;कर्ष I अनुसरण करता है</p>",
                        "<p>केवल निष्&zwj;कर्ष I और III अनुसरण करते हैं</p>"
                    ],
                    solution_en: "<p>11.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739263416278.png\" alt=\"rId24\" width=\"281\" height=\"114\"><br>Hence, only I and III conclusion follow.</p>",
                    solution_hi: "<p>11.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739263416391.png\" alt=\"rId25\" width=\"288\" height=\"118\"><br>अतः, केवल I और III निष्कर्ष अनुसरण करते हैं।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "6",
                    question_en: "<p>12. What should come in place of the question mark (?) in the given series?<br>6, ?, 7, 10, 8, 11</p>",
                    question_hi: "<p>12. दी गई शृंखला में प्रश्न-चिह्न (?) के स्थान पर क्या आना चाहिए ?<br>6, ?, 7, 10, 8, 11</p>",
                    options_en: [
                        "<p>5</p>",
                        "<p>8</p>",
                        "<p>12</p>",
                        "<p>9</p>"
                    ],
                    options_hi: [
                        "<p>5</p>",
                        "<p>8</p>",
                        "<p>12</p>",
                        "<p>9</p>"
                    ],
                    solution_en: "<p>12.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739263416559.png\" alt=\"rId26\" width=\"219\" height=\"46\"></p>",
                    solution_hi: "<p>12.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739263416559.png\" alt=\"rId26\" width=\"219\" height=\"46\"></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "6",
                    question_en: "<p>13. In a certain code language,<br>\'A@ B\' means \'A is the husband of B\',<br>\'A # B\' means \'A is the mother of B\',<br>\'A &amp; B\' means \'A is the daughter of B\',<br>\'A $ B\' means \'A is the brother of B\'.<br>Based on this, how is P related to M, if \'M &amp; N @ O # P $ Q\'?</p>",
                    question_hi: "<p>13. एक निश्चित कूट भाषा में,<br>\'A @ B\' का अर्थ \'A, B का पति है\',<br>\'A # B\' का अर्थ \'A, B की माँ है\',<br>\'A &amp; B\' का अर्थ \'A, B की बेटी है\',<br>\'A $ B\' का अर्थ \'A, B का भाई है\'।<br>इसके आधार पर, यदि \'M &amp; N @ O # P $ Q\' है, तो P का M से क्या संबंध है?</p>",
                    options_en: [
                        "<p>Father</p>",
                        "<p>Son</p>",
                        "<p>Brother</p>",
                        "<p>Father\'s brother</p>"
                    ],
                    options_hi: [
                        "<p>पिता</p>",
                        "<p>बेटा</p>",
                        "<p>भाई</p>",
                        "<p>पिता के भाई</p>"
                    ],
                    solution_en: "<p>13.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739263416708.png\" alt=\"rId27\" width=\"214\" height=\"130\"><br>P is the brother of M.</p>",
                    solution_hi: "<p>13.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739263416708.png\" alt=\"rId27\" width=\"214\" height=\"130\"><br>P, M का भाई है.</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "6",
                    question_en: "<p>14. Six letters M, N, O, P, Q and R are written on different faces of a dice. Two positions of this dice are shown in the figure below. Find the letter on the face opposite to O.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739263416903.png\" alt=\"rId28\" width=\"215\" height=\"113\"></p>",
                    question_hi: "<p>14. एक पासे के विभिन्न फलकों पर छ: अक्षर M, N, O, P, Q और R लिखे गए हैं। नीचे दिए गए चित्र में इस पासे की दो स्थितियों को दर्शाया गया है। दिए गए विकल्पों में से O के विपरीत फलक पर लिखा अक्षर ज्ञात कीजिए |<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739263416903.png\" alt=\"rId28\" width=\"215\" height=\"113\"></p>",
                    options_en: [
                        "<p>R</p>",
                        "<p>M</p>",
                        "<p>Q</p>",
                        "<p>P</p>"
                    ],
                    options_hi: [
                        "<p>R</p>",
                        "<p>M</p>",
                        "<p>Q</p>",
                        "<p>P</p>"
                    ],
                    solution_en: "<p>14.(a)<br>From the two dice the opposite faces are<br>O &harr; R , N &harr; Q , M &harr; P</p>",
                    solution_hi: "<p>14.(a)<br>दो पासों के विपरीत फलक हैं<br>O &harr; R , N &harr; Q , M &harr; P</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "6",
                    question_en: "<p>15. If 2 August 2016 is a Tuesday, then what will the day of the week on 19 October 2020 ?</p>",
                    question_hi: "<p>15. यदि 2 अगस्त 2016 को मंगलवार है, तो 19 अक्टूबर 2020 को सप्ताह का कौन-सा दिन होगा ?</p>",
                    options_en: [
                        "<p>Thursday</p>",
                        "<p>Wednesday</p>",
                        "<p>Monday</p>",
                        "<p>Saturday</p>"
                    ],
                    options_hi: [
                        "<p>गुरूवार</p>",
                        "<p>बुधवार</p>",
                        "<p>सोमवार</p>",
                        "<p>शनिवार</p>"
                    ],
                    solution_en: "<p>15.(c) 2 August 2016 is Tuesday. On going to 2020 the number of odd days = + 1 + 1 + 1 + 2 = 5<br>We have reached till 2 August , but we have to reach till 19 October, the number of days between<br>29 + 30 + 19 = 78. Total number of odd days = 78 + 5 = 83. On dividing 83 by 7 the remainder is 6. Tuesday + 6 = Monday.</p>",
                    solution_hi: "<p>15.(c) 2 अगस्त 2016 को मंगलवार है. 2020 में जाने पर विषम दिनों की संख्या = + 1 + 1 + 1 + 2 = 5<br>हम 2 अगस्त तक पहुंच गए हैं, लेकिन हमें 19 अक्टूबर तक पहुंचना है, बीच में जितने दिन हैं<br>29 + 30 + 19 = 78. विषम दिनों की कुल संख्या = 78 + 5 = 83. 83 को 7 से विभाजित करने पर शेषफल 6 आता है. मंगलवार + 6 = सोमवार.</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "16",
                    section: "6",
                    question_en: "<p>16. A certain number of people are sitting in a row facing the north direction. &lsquo;T&rsquo; is sitting to the immediate right of &lsquo;U&rsquo;. &lsquo;Y&rsquo; is sitting 5th to the left of &lsquo;U&rsquo;. &lsquo;Z&rsquo; is an immediate neighbour of both &lsquo;T&rsquo; and &lsquo;P&rsquo;. &lsquo;V&rsquo; is sitting 5th to the left of &lsquo;T&rsquo;. &lsquo;O&rsquo; is sitting at one extreme end, and there is only one person sitting between &lsquo;O&rsquo; and &lsquo;Y&rsquo;. If no other person is sitting in the row, what is the total number of people seated?</p>",
                    question_hi: "<p>16. कुछ व्यक्ति उत्तर की ओर मुख करके एक पंक्ति में बैठे हैं। \'T\', \'U\' के ठीक दाएं बैठा है। \'Y\', \'U\' के बाएं से पाँचवें स्थान पर बैठा है। \'T\' और \'P\' दोनों का निकटतम पड़ोसी \'Z\' है। \'V\', \'T\' के बाएं से पाँचवें स्थान पर बैठा है। \'O\' किसी एक छोर पर बैठा है, तथा \'O\' और \'Y\' के बीच में केवल एक व्यक्ति बैठा है। यदि पंक्ति में कोई अन्य व्यक्ति नहीं बैठा है, तो पंक्ति में बैठे व्यक्तियों की कुल संख्या कितनी है?</p>",
                    options_en: [
                        "<p>14</p>",
                        "<p>11</p>",
                        "<p>13</p>",
                        "<p>12</p>"
                    ],
                    options_hi: [
                        "<p>14</p>",
                        "<p>11</p>",
                        "<p>13</p>",
                        "<p>12</p>"
                    ],
                    solution_en: "<p>16.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739263417088.png\" alt=\"rId29\" width=\"416\" height=\"53\"><br>There are total 11 people</p>",
                    solution_hi: "<p>16.(b) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739263417194.png\" alt=\"rId30\" width=\"386\" height=\"51\"><br>कुल 11 लोग हैं</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "17",
                    section: "6",
                    question_en: "<p>17. How many meaningful English words can be formed from R, O, W, D and C, using&nbsp;each letter mandatorily but only once in each word?</p>",
                    question_hi: "<p>17. R, O, W, D और C से अंग्रेजी के कितने ऐसे अर्थपूर्ण शब्द बनाए जा सकते हैं, जिनमें प्रत्येक शब्द में प्रत्येक अक्षर का अनिवार्य रूप से लेकिन केवल एक बार उपयोग किया जाए ?</p>",
                    options_en: [
                        "<p>More than two</p>",
                        "<p>Two</p>",
                        "<p>One</p>",
                        "<p>None</p>"
                    ],
                    options_hi: [
                        "<p>दो से अधिक</p>",
                        "<p>दो</p>",
                        "<p>एक</p>",
                        "<p>एक भी नहीं</p>"
                    ],
                    solution_en: "<p>17.(c) <strong>Given</strong> :- R, O, W, D and C<br>The word which can be made from the letters is CROWD. Only 1 word can be formed from these letters.</p>",
                    solution_hi: "<p>17.(c) <strong>दिया गया है:</strong>- R, O, W, D और C<br>अक्षरों से बनाया जा सकने वाला शब्द CROWD है। इन अक्षरों से केवल 1 शब्द बनाया जा सकता है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "18",
                    section: "6",
                    question_en: "<p>18. Select the figure from among the given option that can replace the question mark (?) in the following series.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739263417366.png\" alt=\"rId31\" width=\"300\"></p>",
                    question_hi: "<p>18. दिए गए विकल्पों में से उस आकृति का चयन कीजिए जो निम्नलिखित शृंखला में प्रश्न चिह्न (?) को प्रतिस्थापित कर सकती है।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739263417366.png\" alt=\"rId31\" width=\"300\"></p>",
                    options_en: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739263417522.png\" alt=\"rId32\" width=\"70\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739263417635.png\" alt=\"rId33\" width=\"70\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739263417770.png\" alt=\"rId34\" width=\"70\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739263417910.png\" alt=\"rId35\" width=\"70\"></p>"
                    ],
                    options_hi: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739263417522.png\" alt=\"rId32\" width=\"70\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739263417635.png\" alt=\"rId33\" width=\"70\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739263417770.png\" alt=\"rId34\" width=\"70\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739263417910.png\" alt=\"rId35\" width=\"70\"></p>"
                    ],
                    solution_en: "<p>18.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739263417635.png\" alt=\"rId33\" width=\"70\"></p>",
                    solution_hi: "<p>18.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739263417635.png\" alt=\"rId33\" width=\"70\"></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "19",
                    section: "6",
                    question_en: "<p>19. P got married 15 years ago. Today her age is 1<math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> times her age at the time of her marriage. At present her son\'s age is <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>5</mn></mfrac></math> of her age. What was her son\'s age 4 years ago as a fraction of P\'s age at that time?</p>",
                    question_hi: "<p>19. P की शादी 15 वर्ष पहले हुई थी। आज उसकी आयु उसके विवाह के समय की आयु का 1<math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> गुना है। वर्तमान में उसके पुत्र की आयु उसकी आयु का <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>5</mn></mfrac></math> है। 4 वर्ष पूर्व उसके पुत्र की आयु उस समय P की आयु के भिन्न के रूप में कितनी थी?</p>",
                    options_en: [
                        "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>9</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>9</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>10</mn></mrow></mfrac></math></p>"
                    ],
                    options_hi: [
                        "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>9</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>9</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>10</mn></mrow></mfrac></math></p>"
                    ],
                    solution_en: "<p>19.(a)<br>Let P&rsquo;s age when she got married = x&nbsp;years<br>According to the question,<br>Present age of P = <math display=\"inline\"><mfrac><mrow><mn>8</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> &times; x--------(i)<br>Since, it is given that she got married 15 years ago<br>Present age of P = x&nbsp;+ 15--------(ii)<br>Equate equation (i) and (ii) we get;<br><math display=\"inline\"><mfrac><mrow><mn>8</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> x = x + 15<br>8<math display=\"inline\"><mi>x</mi></math> = 5x + 75<br>3x&nbsp;= 75 &rArr; x = 25 year<br>So, present age of P (x&nbsp;+ 15) = 40 year<br>Now, present age of her son = 40 &times; <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> = 8 year<br>Hence, required fraction = <math display=\"inline\"><mfrac><mrow><mn>8</mn><mo>-</mo><mn>4</mn></mrow><mrow><mn>40</mn><mo>-</mo><mn>4</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>36</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>9</mn></mfrac></math></p>",
                    solution_hi: "<p>19.(a)<br>माना P की शादी के समय उसकी उम्र = x&nbsp;वर्ष <br>प्रश्न के अनुसार,<br>P की वर्तमान आयु = <math display=\"inline\"><mfrac><mrow><mn>8</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> &times; x -------- (i)<br>चूंकि, यह दिया गया है कि उसकी शादी 15 साल पहले हुई थी<br>P की वर्तमान आयु = x&nbsp;+ 15 -------- (ii)<br>समीकरण (i) और (ii) की तुलना करने पर हमें प्राप्त होता है;<br><math display=\"inline\"><mfrac><mrow><mn>8</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> x = x + 15<br>8x&nbsp;= 5x + 75<br>3x&nbsp;= 75 &rArr; x = 25 वर्ष<br>तो, P की वर्तमान आयु (x&nbsp;+ 15) = 40 वर्ष<br>अब, उसके बेटे की वर्तमान आयु = 40 &times; <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> = 8 वर्ष<br>अत: अभीष्ट भिन्न = <math display=\"inline\"><mfrac><mrow><mn>8</mn><mo>-</mo><mn>4</mn></mrow><mrow><mn>40</mn><mo>-</mo><mn>4</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>36</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>9</mn></mfrac></math></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "20",
                    section: "6",
                    question_en: "<p>20. How many triangles are there in the given figure?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739263418055.png\" alt=\"rId36\" width=\"169\" height=\"103\"></p>",
                    question_hi: "<p>20. दी गई आकृति में कितने त्रिभुज हैं?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739263418055.png\" alt=\"rId36\" width=\"169\" height=\"103\"></p>",
                    options_en: [
                        "<p>18</p>",
                        "<p>15</p>",
                        "<p>16</p>",
                        "<p>20</p>"
                    ],
                    options_hi: [
                        "<p>18</p>",
                        "<p>15</p>",
                        "<p>16</p>",
                        "<p>20</p>"
                    ],
                    solution_en: "<p>20.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739263418180.png\" alt=\"rId37\" width=\"166\" height=\"127\"><br>Triangles are = ABC,&nbsp; ACD , ABD.&nbsp;<br>There are 3 triangles in each figure , there are 6 figures like this. Total number of triangle = 6 &times; 3 = 18.</p>",
                    solution_hi: "<p>20.(a) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739263418180.png\" alt=\"rId37\" width=\"166\" height=\"127\"><br>त्रिभुज = ABC, ACD, ABD हैं।<br>प्रत्येक आकृति में 3 त्रिभुज हैं, इस प्रकार 6 आकृतियाँ हैं। त्रिभुज की कुल संख्या = 6 &times; 3 = 18.</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "21",
                    section: "6",
                    question_en: "<p>21. The same operation(s) are followed in all the given number pairs except one. Find that odd number pair.<br>(NOTE : Operations should be performed on the whole numbers, without breaking down the numbers into its constituent digits. E.g. 13 &ndash; Operations on 13 such as adding /deleting /multiplying etc., to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is not allowed.)</p>",
                    question_hi: "<p>21. दिए गए संख्या युग्मों में से एक को छोड़कर अन्य सभी में समान संक्रिया/संक्रियाओं का अनुसरण किया गया है। वह असंगत संख्या युग्म ज्ञात कीजिए।<br>(नोट : संख्याओं को उसके संघटक अंकों में खंडित किए बिना संक्रियाएँ पूर्णांकों पर की जानी चाहिए। उदाहरण के लिए 13- 13 पर की जाने वाली संक्रियाएं जैसे जोड़ना/घटाना/गुणा इत्यादि 13 पर ही की जानी चाहिए। 13 को 1 और 3 में खंडित करना और फिर 1 और 3 पर गणितीय संक्रियाएँ करना वर्जित है।)</p>",
                    options_en: [
                        "<p>151 : 19</p>",
                        "<p>128 : 16</p>",
                        "<p>135 : 17</p>",
                        "<p>103 : 13</p>"
                    ],
                    options_hi: [
                        "<p>151 : 19</p>",
                        "<p>128 : 16</p>",
                        "<p>135 : 17</p>",
                        "<p>103 : 13</p>"
                    ],
                    solution_en: "<p>21.(b) <strong>Logic:</strong> (2nd number &times; 8) - 1 = 1st number<br>151 : 19 :- (19 &times; 8) - 1 = 151<br>135 : 17 :- (17 &times; 8) - 1 = 135<br>103 : 13 :- (13 &times; 8) - 1 = 103<br>But<br>128 : 16 :- (16 &times; 8) - 1 = 127 (&ne; 128)</p>",
                    solution_hi: "<p>21.(b) <strong>तर्क:</strong> (दूसरी संख्या &times; 8) - 1 = पहली संख्या<br>151 : 19 :- (19 &times; 8) - 1 = 151<br>135 : 17 :- (17 &times; 8) - 1 = 135<br>103 : 13 :- (13 &times; 8) - 1 = 103<br>लेकिन<br>128 : 16 :- (16 &times; 8) - 1 = 127 ( &ne; 128)</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "22",
                    section: "6",
                    question_en: "<p>22. A paper is folded and cut as shown below. How will it appear when unfolded?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739263418288.png\" alt=\"rId38\" width=\"283\" height=\"72\"></p>",
                    question_hi: "<p>22. नीचे दर्शाए गए अनुसार एक कागज को मोड़कर काटा जाता है। खोलने पर यह कैसा दिखाई देगा?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739263418288.png\" alt=\"rId38\" width=\"283\" height=\"72\"></p>",
                    options_en: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739263418396.png\" alt=\"rId39\" width=\"90\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739263418500.png\" alt=\"rId40\" width=\"90\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739263418620.png\" alt=\"rId41\" width=\"90\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739263418734.png\" alt=\"rId42\" width=\"90\"></p>"
                    ],
                    options_hi: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739263418396.png\" alt=\"rId39\" width=\"90\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739263418500.png\" alt=\"rId40\" width=\"90\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739263418620.png\" alt=\"rId41\" width=\"90\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739263418734.png\" alt=\"rId42\" width=\"90\"></p>"
                    ],
                    solution_en: "<p>22.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739263418620.png\" alt=\"rId41\" width=\"90\"></p>",
                    solution_hi: "<p>22.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739263418620.png\" alt=\"rId41\" width=\"90\"></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "23",
                    section: "6",
                    question_en: "<p>23. If &lsquo;A&rsquo; stands for &lsquo;&divide;&rsquo;, &lsquo;B&rsquo; stands for &lsquo;&times;&rsquo;, &lsquo;C&rsquo; stands for &lsquo;+&rsquo; and &lsquo;D&rsquo; stands for &lsquo;&ndash;&rsquo;, what will come in place of the question mark (?) in the following equation? <br>96 A 4 D 5 B 7 C 20 = ?</p>",
                    question_hi: "<p>23. यदि \'A\' का अर्थ \'&divide;\' है, \'B\' का अर्थ \'&times;\' है, \'C\' का अर्थ \'+\' है और \'D\' का अर्थ \'&ndash;\' है, तो निम्नलिखित समीकरण में प्रश्न-चिह्न (?) के स्थान पर क्या आएगा?<br>96 A 4 D 5 B 7 C 20 = ?</p>",
                    options_en: [
                        "<p>9</p>",
                        "<p>7</p>",
                        "<p>4</p>",
                        "<p>2</p>"
                    ],
                    options_hi: [
                        "<p>9</p>",
                        "<p>7</p>",
                        "<p>4</p>",
                        "<p>2</p>"
                    ],
                    solution_en: "<p>23.(a) <strong>Given</strong> :- 96 A 4 D 5 B 7 C 20<br>As per given instruction after interchanging the letter with sign we get,<br>96 &divide;&nbsp;4 - 5 &times; 7 + 20<br>24 - 35 + 20<br>24 - 15 = 9</p>",
                    solution_hi: "<p>23.(a) <strong>दिया गया :- </strong>96 A 4 D 5 B 7 C 20<br>दिए गए निर्देश के अनुसार अक्षर को चिन्ह से बदलने के बाद हमें प्राप्त होता है,<br>96 &divide;&nbsp;4 - 5 &times; 7 + 20<br>24 - 35 + 20<br>24 - 15 = 9</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "24",
                    section: "6",
                    question_en: "<p>24. The second number in the given number pairs is obtained by performing certain mathematical operation(s) on the first number. Select the pair in which the numbers are related in the same way as are the numbers of the given pairs. <br>(NOTE: Operations should be performed on the whole numbers, without breaking down the numbers into their constituent digits. E.g. 13 &ndash; Operations on 13 such as adding /deleting /multiplying to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is not allowed.)<br>6, 42 <br>9, 63</p>",
                    question_hi: "<p>24. दिए गए संख्या-युग्मों में दूसरी संख्या, पहली संख्या पर निश्चित गणितीय संक्रिया/संक्रियाएँ करके प्राप्त की जाती है। उस युग्म का चयन कीजिए जिसमें संख्याएँ ठीक उसी प्रकार संबंधित हों जिस प्रकार दिए गए युग्मों की संख्याएँ संबंधित हैं। <br>(ध्यान दें: संख्याओं को उनके घटक अंकों में अलग-अलग किए बिना पूर्ण संख्याओं पर संक्रियाएं की जानी चाहिए। उदाहरण के लिए संख्या 13 - संख्या 13 पर संक्रियाएं जैसे 13 को जोड़ना/घटाना/गुणा करना आदि किया जा सकता है। 13 को 1 और 3 में अलग-अलग करने की और फिर 1 और 3 पर गणितीय संक्रियाएं करने की अनुमति नहीं है।) <br>6, 42 <br>9, 63</p>",
                    options_en: [
                        "<p>19, 123</p>",
                        "<p>13, 91</p>",
                        "<p>17, 102</p>",
                        "<p>11, 67 </p>"
                    ],
                    options_hi: [
                        "<p>19, 123</p>",
                        "<p>13, 91</p>",
                        "<p>17, 102</p>",
                        "<p>11, 67</p>"
                    ],
                    solution_en: "<p>24.(b) <strong>Logic</strong> :- (1st number &times; 7) = 2nd number<br>(6 - 42) :- (6 &times; 7) = 42<br>(9 - 63) :- (9 &times; 7) = 63<br>Similarly,<br>(13 - ?) :- (13 &times; 7) = 91</p>",
                    solution_hi: "<p>24.(b) <strong>तर्क:-</strong> (पहली संख्या &times; 7) = दूसरी संख्या<br>(6 - 42) :- (6 &times; 7) = 42<br>(9 - 63) :- (9 &times; 7) = 63<br>इसी प्रकार,<br>(13 - ?) :- (13 &times; 7) = 91</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "25",
                    section: "18",
                    question_en: "<p>25. Select the correct mirror image of the given figure when the mirror is placed at MN as shown below.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739263418838.png\" alt=\"rId43\" width=\"140\" height=\"103\"></p>",
                    question_hi: "<p>25. दी गई आकृति के उस सही दर्पण प्रतिबिंब को चुनिए, जो नीचे दर्शाए गए अनुसार दर्पण को MN पर रखने पर बनेगा। <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739263418838.png\" alt=\"rId43\" width=\"140\" height=\"103\"></p>",
                    options_en: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739263418938.png\" alt=\"rId44\" width=\"135\" height=\"33\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739263419047.png\" alt=\"rId45\" width=\"135\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739263419150.png\" alt=\"rId46\" width=\"135\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739263419308.png\" alt=\"rId47\" width=\"135\"></p>"
                    ],
                    options_hi: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739263418938.png\" alt=\"rId44\" width=\"135\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739263419047.png\" alt=\"rId45\" width=\"135\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739263419150.png\" alt=\"rId46\" width=\"135\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739263419308.png\" alt=\"rId47\" width=\"135\"></p>"
                    ],
                    solution_en: "<p>25.(c) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739263419150.png\" alt=\"rId46\" width=\"135\"></p>",
                    solution_hi: "<p>25.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739263419150.png\" alt=\"rId46\" width=\"135\"></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "26",
                    section: "18",
                    question_en: "<p>26. What is the stump height of a cricket wicket as per international standards ?</p>",
                    question_hi: "<p>26. अंतरराष्ट्रीय मानकों के अनुसार क्रिकेट में विकेट के स्टंप की ऊंचाई कितनी होती है ?</p>",
                    options_en: [
                        "<p>28 inches</p>",
                        "<p>29 inches</p>",
                        "<p>30 inches</p>",
                        "<p>31 inches</p>"
                    ],
                    options_hi: [
                        "<p>28 इंच</p>",
                        "<p>29 इंच</p>",
                        "<p>30 इंच</p>",
                        "<p>31 इंच</p>"
                    ],
                    solution_en: "<p>26.(a) <strong>28 inches.</strong> The three vertical pillars that support the bails and form the wicket in cricket are called stumps. A batsman may be dismissed by being stumped or through stumping. Except for the bail grooves, the tops of the stumps must be dome-shaped and stand 28 inches/71.12 cm above the playing surface. Apart from the domed top, a stump\'s upper section must be cylindrical, with a circular diameter between 1.38 in (3.50 cm) and 1.5 in (3.81 cm).</p>",
                    solution_hi: "<p>26.(a) <strong>28 इंच।</strong> क्रिकेट में बेल्स को सहारा देने वाले और विकेट बनाने वाले तीन ऊर्ध्वाधर खंभों को स्टंप कहा जाता है। बल्लेबाज को स्टंप करके या स्टंपिंग के ज़रिए आउट किया जा सकता है। बेल के खांचे को छोड़कर, स्टंप के शीर्ष गोलाकार के आकार के होने चाहिए और खेल की सतह से 28 इंच/71.12 सेमी ऊंचे होने चाहिए। गोलाकार शीर्ष के अलावा, स्टंप का ऊपरी भाग बेलनाकार होना चाहिए, जिसका परिधीय व्यास 1.38 इंच (3.50 सेमी) और 1.5 इंच (3.81 सेमी) के बीच होना चाहिए।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "27",
                    section: "18",
                    question_en: "<p>27. &lsquo;Ram Navami&rsquo; is celebrated in India to commemorate the :</p>",
                    question_hi: "<p>27. भारत में \'राम नवमी\' किसकी याद में मनाई जाती है?</p>",
                    options_en: [
                        "<p>death of Ravana.</p>",
                        "<p>coronation ceremony of Lord Rama as a king.</p>",
                        "<p>return of Lord Rama from forest.</p>",
                        "<p>birth of Lord Rama.</p>"
                    ],
                    options_hi: [
                        "<p>रावण की मृत्यु</p>",
                        "<p>राजा के रूप में भगवान राम का राज्याभिषेक समारोह</p>",
                        "<p>भगवान राम की वन से वापसी</p>",
                        "<p>भगवान राम का जन्म</p>"
                    ],
                    solution_en: "<p>27.(d) <strong>birth of Lord Rama</strong>. Ram Navami is celebrated on the ninth day of the Chaitra month (March or April) to mark the birth of Lord Rama. Dussehra or Vijayadashami (in Ashwin month) is celebrated to commemorate the killing of Ravana and the victory of Lord Rama. This festival symbolizes the victory of good over evil. The coronation of Rama as king after his return from exile is known as Rama Pattabhisheka. The return of Lord Rama from the Vanvas after 14 years of exile is celebrated during Diwali (in Kartik month).</p>",
                    solution_hi: "<p>27.(d) <strong>भगवान राम का जन्म।</strong> राम नवमी चैत्र माह (मार्च या अप्रैल) के नौवें दिन भगवान राम के जन्म के उपलक्ष्य में मनाई जाती है। दशहरा या विजयादशमी (आश्विन माह में) रावण के वध और भगवान राम की विजय के उपलक्ष्य में मनाई जाती है। यह पर्व बुराई पर अच्छाई की जीत का प्रतीक है। वनवास से लौटने के बाद राम का राजा के रूप में राज्याभिषेक, राम पट्टाभिषेक के रूप में जाना जाता है। 14 वर्षों के वनवास के बाद भगवान राम की वापसी का उत्सव (कार्तिक माह में) दीपावली के रूप में मनाया जाता है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "28",
                    section: "18",
                    question_en: "<p>28. _________ is a state of deprivation that reflects the inability of an individual to satisfy basic needs for a sustained, healthy and reasonably productive living.</p>",
                    question_hi: "<p>28. _______ वंचन की स्थिति है जो एक व्यक्ति के सतत, स्वस्थ और यथोचित उत्पादक जीवन के लिए बुनियादी जरूरतों को पूरा करने में असमर्थता को दर्शाती है।</p>",
                    options_en: [
                        "<p>Ignorance</p>",
                        "<p>Illiteracy</p>",
                        "<p>Poverty</p>",
                        "<p>Bondage</p>"
                    ],
                    options_hi: [
                        "<p>अज्ञानता</p>",
                        "<p>निरक्षरता</p>",
                        "<p>गरीबी</p>",
                        "<p>दासता</p>"
                    ],
                    solution_en: "<p>28.(c) <strong>Poverty.</strong> <br><strong>Ignorance</strong> : This refers to a lack of knowledge, information, or awareness about a particular subject or situation. <br><strong>Bondage</strong> : This refers to the condition of being a slave or being in a state of servitude.</p>",
                    solution_hi: "<p>28.(c) <strong>गरीबी</strong>।&nbsp;<br><strong>अज्ञानता:</strong> यह किसी विशेष विषय या स्थिति के बारे में ज्ञान, सूचना या जागरूकता की कमी को संदर्भित करता है। <br><strong>दासता:</strong> यह गुलामी की स्थिति को संदर्भित करता है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "29",
                    section: "18",
                    question_en: "<p>29. Which of the following is a correct order of basicity?</p>",
                    question_hi: "<p>29. निम्नलिखित में से कौन-सा क्षारकता का सही क्रम है?</p>",
                    options_en: [
                        "<p>LiOH &gt; NaOH &gt; KOH &gt; CsOH</p>",
                        "<p>LiOH &gt; KOH &gt; CsOH &gt; NaOH</p>",
                        "<p>KOH &gt; CsOH &gt; NaOH &gt; LiOH</p>",
                        "<p>CsOH &gt; KOH &gt; NaOH &gt; LiOH</p>"
                    ],
                    options_hi: [
                        "<p>LiOH &gt; NaOH &gt; KOH &gt; CsOH</p>",
                        "<p>LiOH &gt; KOH &gt; CsOH &gt; NaOH</p>",
                        "<p>KOH &gt; CsOH &gt; NaOH &gt; LiOH</p>",
                        "<p>CsOH &gt; KOH &gt; NaOH &gt; LiOH</p>"
                    ],
                    solution_en: "<p>29.(d) <strong>CsOH &gt; KOH &gt; NaOH &gt; LiOH.</strong> This order is due to the increasing size of the alkali metal ions, which leads to better solvation and greater basicity. As you move down the group, the basicity increases because the larger alkali metal ions (like Cs⁺) can more easily dissociate and interact with water compared to the smaller ions (like Li⁺).</p>",
                    solution_hi: "<p>29.(d) <strong>CsOH &gt; KOH &gt; NaOH &gt; LiOH. </strong>यह क्रम क्षारीय धातु आयनों के बढ़ते आकार के कारण है, जो बेहतर विलयन और अधिक क्षारीयता की ओर ले जाता है। जैसे-जैसे समूह में नीचे की ओर जाते हैं, क्षारीयता बढ़ती जाती है क्योंकि बड़े क्षारीय धातु आयन (जैसे Cs⁺) छोटे आयनों (जैसे Li⁺) की तुलना में अधिक आसानी से विघटित हो सकते हैं और जल के साथ अंतःक्रिया कर सकते हैं।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "30",
                    section: "18",
                    question_en: "<p>30. When did the United Nations declare the World Physics Year, also known as the Einstein Year, to mark the 100th anniversary of the physicist Albert Einstein?</p>",
                    question_hi: "<p>30. भौतिक विज्ञानी अल्बर्टआइंस्टीन की 100वीं वर्षगांठ मनाने के लिए संयुक्त राष्ट्र ने विश्व भौतिकी वर्ष, जिसे आइंस्टीन वर्ष भी कहा जाता है, कब घोषित किया था?</p>",
                    options_en: [
                        "<p>2005</p>",
                        "<p>2002</p>",
                        "<p>2006</p>",
                        "<p>2004</p>"
                    ],
                    options_hi: [
                        "<p>2005</p>",
                        "<p>2002</p>",
                        "<p>2006</p>",
                        "<p>2004</p>"
                    ],
                    solution_en: "<p>30.(a) <strong>2005.</strong> In 1905, Einstein wrote three of his most famous scientific papers. These legendary articles provided the basis of three fundamental fields in physics: the theory of relativity, quantum theory and the theory of Brownian motion. The World Year of Physics is a worldwide celebration of physics and its importance in our everyday lives. Aim: To raise the worldwide awareness of physics and physical science.</p>",
                    solution_hi: "<p>30.(a) <strong>2005.</strong> 1905 में, आइंस्टीन ने अपने तीन सबसे प्रसिद्ध वैज्ञानिक पत्र लिखे। इन महान लेखों ने भौतिकी के तीन मौलिक क्षेत्रों का आधार प्रदान किया: सापेक्षता का सिद्धांत, क्वांटम सिद्धांत और ब्राउनियन गति का सिद्धांत। भौतिकी का विश्व वर्ष भौतिकी और हमारे दैनिक जीवन में इसके महत्व का विश्वव्यापी उत्सव है। उद्देश्य: भौतिकी और भौतिक विज्ञान के बारे में दुनिया भर में जागरूकता बढ़ाना।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "31",
                    section: "18",
                    question_en: "<p>31. Which team won the U-19 Asia Cup final in 2024?</p>",
                    question_hi: "<p>31. 2024 में U-19 एशिया कप फाइनल किस टीम ने जीता?</p>",
                    options_en: [
                        "<p>India</p>",
                        "<p>Bangladesh</p>",
                        "<p>Sri Lanka</p>",
                        "<p>Pakistan</p>"
                    ],
                    options_hi: [
                        "<p>भारत</p>",
                        "<p>बांग्लादेश</p>",
                        "<p>श्रीलंका</p>",
                        "<p>पाकिस्तान</p>"
                    ],
                    solution_en: "<p>31.(b) <strong>Bangladesh</strong> U-19 secured an impressive 59-run victory over India in the final, successfully defending their Asia Cup title.</p>",
                    solution_hi: "<p>31.(b) <strong>बांग्लादेश।</strong><br>बांग्लादेश U-19 टीम ने फाइनल में भारत को 59 रनों से हराकर एशिया कप खिताब का सफलतापूर्वक बचाव किया।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "32",
                    section: "18",
                    question_en: "<p>32. Which of the following cities is known as the Steel City in India ?</p>",
                    question_hi: "<p>32. भारत का निम्नलिखित में से कौन-सा शहर स्टील सिटी (Steel City) के नाम से भी जाना जाता है?</p>",
                    options_en: [
                        "<p>Mumbai</p>",
                        "<p>Jamshedpur</p>",
                        "<p>Patna</p>",
                        "<p>Ranchi</p>"
                    ],
                    options_hi: [
                        "<p>मुंबई</p>",
                        "<p>जमशेदपुर</p>",
                        "<p>पटना</p>",
                        "<p>रांची</p>"
                    ],
                    solution_en: "<p>32.(b) <strong>Jamshedpur.</strong> Tata built India\'s first major steel plant in the village of Sakchi, which was renamed Jamshedpur in honor of its founder Jamshedji Tata. Other Nickname of cities : Mumbai - City of Dreams, Jamshedpur - Steel City, Pittsburgh of India. Indore - Mini Mumbai. Rourkela - Steel City of Odisha. Gurugram &ndash; Millennium City. Bhilai - Steel Capital of India. Damodar Valley - The Ruhr of India.</p>",
                    solution_hi: "<p>32.(b) <strong>जमशेदपुर।</strong> टाटा ने साकची गाँव में भारत का पहला बड़ा स्टील प्लांट का निर्माण किया, जिसका नाम इसके संस्थापक जमशेदजी टाटा के सम्मान में जमशेदपुर रखा गया। शहरों के अन्य उपनाम: मुंबई - सपनों का शहर, जमशेदपुर - स्टील सिटी, भारत का पिट्सबर्ग। इंदौर - मिनी मुंबई। राउरकेला - ओडिशा का स्टील सिटी। गुरुग्राम - मिलेनियम सिटी। भिलाई - भारत की स्टील राजधानी। दामोदर घाटी - भारत का रूर।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "33",
                    section: "18",
                    question_en: "<p>33. Which of the following is the correct match between column-A and column-B?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739263419413.png\" alt=\"rId48\" width=\"330\" height=\"189\"></p>",
                    question_hi: "<p>33. कॉलम-A और कॉलम-B के बीच निम्नलिखित में से कौन सा सही मेल है?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739263419551.png\" alt=\"rId49\" width=\"293\" height=\"190\"></p>",
                    options_en: [
                        "<p>i-b, ii-a, iii-d, iv-c</p>",
                        "<p>i-d, ii-c, iii-b, iv-a</p>",
                        "<p>i-a, ii-b, iii-c, iv-d</p>",
                        "<p>i-c, ii-d, iii-a, iv-b</p>"
                    ],
                    options_hi: [
                        "<p>i-b, ii-a, iii-d, iv-c</p>",
                        "<p>i-d, ii-c, iii-b, iv-a</p>",
                        "<p>i-a, ii-b, iii-c, iv-d</p>",
                        "<p>i-c, ii-d, iii-a, iv-b</p>"
                    ],
                    solution_en: "<p>33.(a) <strong>i-b, ii-a, iii-d, iv-c</strong>. Food chain is a linear sequence of organisms where nutrients and energy are transferred from one organism to the other. Producers - Green plants. Primary consumers feed on the producers. Example: Herbivores like cows. Secondary consumers depend on the primary consumers for energy. Example - Carnivores like Lions. Decomposers obtain energy by decomposition of dead and decaying organic matter. Example - Saprophytic fungi.</p>",
                    solution_hi: "<p>33.(a) <strong>i-b, ii-a, iii-d, iv-c । </strong>खाद्य श्रृंखला जीवों का एक रैखिक क्रम है जहां पोषक तत्व और ऊर्जा एक जीव से दूसरे जीव में स्थानांतरित होते हैं। उत्पादक - हरे पौधे। प्राथमिक उपभोक्ता, उत्पादकों पर निर्भर रहते हैं। उदाहरण: शाकाहारी जैसे गाय। द्वितीयक उपभोक्ता, ऊर्जा के लिए प्राथमिक उपभोक्ताओं पर निर्भर रहते हैं। उदाहरण - मांसाहारी जैसे शेर। अपघटक, मृत और क्षयकारी कार्बनिक पदार्थों के अपघटन से ऊर्जा प्राप्त करते हैं। उदाहरण - मृतोपजीवी कवक।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "34",
                    section: "18",
                    question_en: "<p>34. The famous English novel &lsquo;Em and the Big Hoom&rsquo; is written by:</p>",
                    question_hi: "<p>34. प्रसिद्ध अंग्रेजी उपन्यास \'एम एंड द बिग हूम (Em and the Big Hoom)\' _______ द्वारा लिखा गया है।</p>",
                    options_en: [
                        "<p>Esther David</p>",
                        "<p>Amitav Ghosh</p>",
                        "<p>Jerry Pinto</p>",
                        "<p>Shashi Tharoor</p>"
                    ],
                    options_hi: [
                        "<p>एस्थर डेविड</p>",
                        "<p>अमिताव घोष</p>",
                        "<p>जैरी पिंटो</p>",
                        "<p>शशि थरूर</p>"
                    ],
                    solution_en: "<p>34.(c) <strong>Jerry Pinto</strong> is a Mumbai-based Indian-English poet, novelist. His Other Books include &ldquo;The Education of Yuri&rdquo;, &ldquo;Helen: The Life and Times of an H-Bomb&rdquo;, &ldquo;Murder in Mahim&rdquo;. Amitav Ghosh - &ldquo;Sea of Poppies&rdquo;, &ldquo;The Glass Palace&rdquo;. Esther David - &ldquo;Book of Rachel&rdquo;, &ldquo;Shalom India Housing Society&rdquo;. Shashi Tharoor - &ldquo;The Great Indian Novel&rdquo;, &ldquo;An Era of Darkness&rdquo;.</p>",
                    solution_hi: "<p>34.(c) <strong>जैरी पिंटो</strong> एक मुंबई स्थित भारतीय-अंग्रेजी कवि और उपन्यासकार हैं। उनकी अन्य पुस्तकों में \"द एजुकेशन ऑफ यूरी\", \"हेलेन: द लाइफ एंड टाइम्स ऑफ एन एच-बम\", \"मर्डर इन माहिम\" शामिल हैं। अमिताव घोष - \"सी ऑफ पॉपीज\", \"द ग्लास पैलेस\"। एस्तेर डेविड - \"बुक ऑफ रेचेल\", \"शालोम इंडिया हाउसिंग सोसाइटी\"। शशि थरूर - \"द ग्रेट इंडियन नॉवेल\", \"एन एरा ऑफ डार्कनेस\"।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "35",
                    section: "18",
                    question_en: "<p>35. In which of the following years was the Juvenile Justice (Care and Protection of Children) Act, 2015 amended ?</p>",
                    question_hi: "<p>35. निम्नलिखित में से किस वर्ष में किशोर न्याय (बालकों की देखरेख और संरक्षण) अधिनियम, 2015 को संशोधित किया गया था?</p>",
                    options_en: [
                        "<p>2013</p>",
                        "<p>2015</p>",
                        "<p>2019</p>",
                        "<p>2021</p>"
                    ],
                    options_hi: [
                        "<p>2013</p>",
                        "<p>2015</p>",
                        "<p>2019</p>",
                        "<p>2021</p>"
                    ],
                    solution_en: "<p>35.(d) <strong>2021.</strong> The Juvenile Justice (Care and Protection of Children) Act, 2015, was passed by the Indian Parliament amid controversy and protests from child rights groups. It replaced the 2000 Act and allows juveniles aged 16&ndash;18 involved in heinous offences to be tried as adults.</p>",
                    solution_hi: "<p>35.(d) <strong>2021.</strong> किशोर न्याय (बच्चों की देखभाल और संरक्षण) अधिनियम, 2015, भारतीय संसद द्वारा विवाद और बाल अधिकार समूहों के विरोध के बीच पारित किया गया था। इसने 2000 अधिनियम को प्रतिस्थापित कर दिया और जघन्य अपराधों में शामिल 16-18 वर्ष की आयु के किशोरों पर वयस्कों के रूप में मुकदमा चलाने की अनुमति दी।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "36",
                    section: "18",
                    question_en: "<p>36. On which river in India was the Somasila Dam built?</p>",
                    question_hi: "<p>36. सोमसिला बांध (Somasila Dam) भारत में किस नदी पर बनाया गया है?</p>",
                    options_en: [
                        "<p>Krishna river</p>",
                        "<p>Tapti river</p>",
                        "<p>Pennar river</p>",
                        "<p>Sabarmati river</p>"
                    ],
                    options_hi: [
                        "<p>कृष्णा नदी</p>",
                        "<p>ताप्ती नदी</p>",
                        "<p>पेन्नार नदी</p>",
                        "<p>साबरमती नदी</p>"
                    ],
                    solution_en: "<p>36.(c) <strong>Pennar river.</strong> It originates in the Nandi Hills, Karnataka, and flows through Karnataka and Andhra Pradesh before emptying into the Bay of Bengal. The Somasila dam is situated in the Nellore district (Andhra Pradesh). Famous dams and the rivers they are built on: Hirakud Dam (Mahanadi River), Sardar Sarovar Dam (Narmada River), Tehri Dam (Bhagirathi River), Bhakra Nangal Dam (Sutlej River), Koyna Dam (Koyna River), Idukki Dam (Periyar River).</p>",
                    solution_hi: "<p>36.(c) <strong>पेन्नार नदी। </strong>यह नदी कर्नाटक के नंदी हिल्स से निकलती है और बंगाल की खाड़ी में गिरने से पहले कर्नाटक और आंध्र प्रदेश से होकर बहती है। सोमासिला बांध नेल्लोर जिले (आंध्र प्रदेश) में स्थित है। प्रसिद्ध बांध और वे नदियाँ जिन पर वे निर्मित हैं: हीराकुंड बांध (महानदी नदी), सरदार सरोवर बांध (नर्मदा नदी), टिहरी बांध (भागीरथी नदी), भाखड़ा नांगल बांध (सतलुज नदी), कोयना बांध (कोयना नदी), इडुक्की बांध (पेरियार नदी)।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "37",
                    section: "18",
                    question_en: "<p>37. The Hindi novel \'Jhutha Sach\' is written by:</p>",
                    question_hi: "<p>37. हिंदी उपन्यास \'झूठा सच\' किसने लिखा है?</p>",
                    options_en: [
                        "<p>Yashpal</p>",
                        "<p>Jaishankar Prasad</p>",
                        "<p>Shrilal Shukla</p>",
                        "<p>Premchand</p>"
                    ],
                    options_hi: [
                        "<p>यशपाल</p>",
                        "<p>जयशंकर प्रसाद</p>",
                        "<p>श्रीलाल शुक्ल</p>",
                        "<p>प्रेमचंद</p>"
                    ],
                    solution_en: "<p>37.(a) <strong>Yashpal</strong> won the Hindi-language Sahitya Akademi Award for his novel &lsquo;Meri Teri Uski Baat&rsquo; in 1976. Other notable works by him: &lsquo;Dada Kamred&rsquo;, &lsquo;Divya&rsquo;, &lsquo;Manushya Ke Roop&rsquo;, &lsquo;Barah Ghante&rsquo;, &lsquo;Apsara Ka Shaap&rsquo;, &lsquo;Sach Bolne ki Bhool&rsquo;. Authors and their notable books: Jaishankar Prasad - &lsquo;Kamayani&rsquo;, &lsquo;Iravati&rsquo;, &lsquo;Lahar&rsquo;. Premchand - &lsquo;Poos Ki Raat&rsquo;, &lsquo;Rangbhumi&rsquo;, &rsquo;Gaban&rsquo;, &lsquo;Godan&rsquo;, &lsquo;Idgah&rsquo;. Shrilal Shukla - &lsquo;Raag Darbari&rsquo;, &lsquo;Suni Ghati Ka Suraj&rsquo;, &lsquo;Aadami Ka Jahar&rsquo;.</p>",
                    solution_hi: "<p>37.(a) <strong>यशपाल</strong> ने 1976 में अपने उपन्यास &lsquo;मेरी तेरी उसकी बात&rsquo; के लिए हिंदी भाषा में साहित्य अकादमी पुरस्कार जीता। उनकी अन्य प्रमुख कृतियाँ है : &lsquo;दादा कामरेड&rsquo;, &lsquo;दिव्य&rsquo;, &lsquo;मनुष्य के रूप&rsquo;, &lsquo;बारह घंटे&rsquo;, &lsquo;अप्सरा का शाप&rsquo;, &lsquo;सच बोलने की भूल&rsquo;। लेखक और उनकी प्रमुख कृतियाँ: जयशंकर प्रसाद - &lsquo;कामायनी&rsquo;, &lsquo;इरावती&rsquo;, &lsquo;लहर&rsquo;। प्रेमचंद - &lsquo;पूस की रात&rsquo;, &lsquo;रंगभूमि&rsquo;, &lsquo;गबन&rsquo;, &lsquo;गोदान&rsquo;, &lsquo;ईदगाह&rsquo;। श्रीलाल शुक्ल - &lsquo;राग दरबारी&rsquo;, &lsquo;सुनी घाटी का सूरज&rsquo;, &lsquo;आदमी का जहर&rsquo;।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "38",
                    section: "18",
                    question_en: "<p>38. In June 2024, which Indian force inaugurated its first skin bank facility in New Delhi ?</p>",
                    question_hi: "<p>38. जून 2024 में किस भारतीय बल ने दिल्ली में अपना पहला त्वचा बैंक सुविधा उद्घाटन किया?</p>",
                    options_en: [
                        "<p>Indian Navy</p>",
                        "<p>Indian Airforce</p>",
                        "<p>Indian Army</p>",
                        "<p>Indian Coast Guard</p>"
                    ],
                    options_hi: [
                        "<p>भारतीय नौसेना</p>",
                        "<p>भारतीय वायुसेना</p>",
                        "<p>भारतीय सेना</p>",
                        "<p>भारतीय तटरक्षक बल</p>"
                    ],
                    solution_en: "<p>38.(c) <strong>Indian Army.</strong> Army Hospital (Research &amp; Referral), New Delhi, on June 18, 2024, announced the opening of a state-of-the-art skin bank facility, a first-of-its-kind to be established within the Armed Forces Medical Services. This landmark initiative aims to revolutionise the treatment of severe burn injuries and other skin-related conditions among service members and their families.</p>",
                    solution_hi: "<p>38.(c) <strong>भारतीय सेना। </strong>आर्मी हॉस्पिटल (रिसर्च एंड रेफरल), नई दिल्ली ने 18 जून, 2024 को एक अत्याधुनिक स्किन बैंक सुविधा शुरू करने की घोषणा की, जो सशस्त्र सेना चिकित्सा सेवाओं के भीतर स्थापित की जाने वाली अपनी तरह की पहली सुविधा है। इस ऐतिहासिक पहल का उद्देश्य सेवा सदस्यों और उनके परिवारों के बीच गंभीर रूप से जलने की चोटों और अन्य त्वचा संबंधी स्थितियों के उपचा</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "39",
                    section: "18",
                    question_en: "<p>39. The main worker is a person who works for at least __________ days in a year.</p>",
                    question_hi: "<p>39. मुख्य कामगार वह व्यक्ति होता है जो वर्ष में कम से कम ________ दिन कार्य करता है।</p>",
                    options_en: [
                        "<p>145</p>",
                        "<p>167</p>",
                        "<p>115</p>",
                        "<p>183</p>"
                    ],
                    options_hi: [
                        "<p>145</p>",
                        "<p>167</p>",
                        "<p>115</p>",
                        "<p>183</p>"
                    ],
                    solution_en: "<p>39.(d) <strong>183</strong> (or six months). The population of India according to their economic status is divided into three groups, namely; main workers, marginal workers (who work less than 183 days) and non-workers. The proportion of workers (both main and marginal) is only 39.8 percent (2011) leaving a vast majority of about 60 percent as non-workers.</p>",
                    solution_hi: "<p>39.(d) <strong>183</strong> (या छह महीने)। भारत की जनसंख्या को उनकी आर्थिक स्थिति के अनुसार तीन समूहों में विभाजित किया गया है, अर्थात् मुख्य श्रमिक, सीमांत श्रमिक (जो 183 दिनों से कम काम करते हैं) और गैर-श्रमिक। श्रमिकों (मुख्य और सीमांत दोनों) का अनुपात केवल 39.8 प्रतिशत (2011) है, जिसमें से लगभग 60 प्रतिशत जनसंख्या गैर-श्रमिक है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "40",
                    section: "18",
                    question_en: "<p>40. In which year was the Treaty of Amritsar signed ?</p>",
                    question_hi: "<p>40. अमृतसर की संधि पर किस वर्ष हस्ताक्षर किए गए थे ?</p>",
                    options_en: [
                        "<p>1810</p>",
                        "<p>1809</p>",
                        "<p>1808</p>",
                        "<p>1811</p>"
                    ],
                    options_hi: [
                        "<p>1810</p>",
                        "<p>1809</p>",
                        "<p>1808</p>",
                        "<p>1811</p>"
                    ],
                    solution_en: "<p>40.(b) <strong>1809.</strong> The Treaty of Amritsar was signed between the British East India Company and Maharaja Ranjit Singh. This treaty established the Sutlej River as the boundary between British territories and the Sikh Empire.</p>",
                    solution_hi: "<p>40.(b) <strong>1809.</strong> अमृतसर की संधि, ब्रिटिश ईस्ट इंडिया कंपनी और महाराजा रणजीत सिंह के बीच हुई थी। इस संधि ने सतलुज नदी को ब्रिटिश क्षेत्रों और सिख साम्राज्य के बीच सीमा के रूप में स्थापित किया।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "41",
                    section: "18",
                    question_en: "<p>41. According to Hindu calendar, Diwali falls in which of the following months?</p>",
                    question_hi: "<p>41. हिंदू कैलेंडर के अनुसार, दिवाली निम्नलिखित में से किस महीने में आती है?</p>",
                    options_en: [
                        "<p>Ashwin</p>",
                        "<p>Magha</p>",
                        "<p>Chaitra</p>",
                        "<p>Kartika</p>"
                    ],
                    options_hi: [
                        "<p>अश्विन</p>",
                        "<p>माघ</p>",
                        "<p>चैत्र</p>",
                        "<p>कार्तिक</p>"
                    ],
                    solution_en: "<p>41.(d) <strong>Kartika.</strong> Diwali, the Hindu festival of lights. It represents the spiritual \"victory of light over darkness, good over evil, and knowledge over ignorance.\" The months of the Hindu calendar in order are : Chaitra, Vaisakha, Jyaistha, Asadha, Shravana, Bhadra, Asvina, Kartika, Agrahayana, Pausa, Magha, and Phalguna.</p>",
                    solution_hi: "<p>41.(d) <strong>कार्तिक।</strong> दिवाली, रोशनी का हिंदू त्योहार है। यह आध्यात्मिक रूप से \"अंधकार पर प्रकाश की जीत, बुराई पर अच्छाई और अज्ञानता पर ज्ञान की जीत\" का प्रतीक है। हिंदू कैलेंडर के महीने क्रमशः इस प्रकार हैं : चैत्र, वैशाख, ज्येष्ठ, आषाढ़, श्रावण, भाद्र, अश्विन, कार्तिक, अग्रहायण, पौष, माघ और फाल्गुन।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "42",
                    section: "18",
                    question_en: "<p>42. Which of the following is the correct relationship?</p>",
                    question_hi: "<p>42. निम्नलिखित में से कौन सा संबंध सही है ?</p>",
                    options_en: [
                        "<p>F = ma</p>",
                        "<p>F = m/a</p>",
                        "<p>F = m<sup>2</sup>a</p>",
                        "<p>F = a/m</p>"
                    ],
                    options_hi: [
                        "<p>F = ma</p>",
                        "<p>F = m/a</p>",
                        "<p>F = m<sup>2</sup>a</p>",
                        "<p>F = a/m</p>"
                    ],
                    solution_en: "<p>42.(a)<strong> F = ma.</strong> Newton\'s second law is often stated as F = ma, which means the force (F) acting on an object is equal to the mass (m) of the object times its acceleration (a). This means the more mass an object has, the more force you need to accelerate it, and the greater the force, the greater the object\'s acceleration. Force(F) = Mass(m) &times; Acceleration(a). SI unit of Force = Newtons (N).</p>",
                    solution_hi: "<p>42.(a)<strong> F = ma. </strong>न्यूटन के दूसरे नियम को प्रायः F = ma के रूप में व्यक्त किया जाता है, जिसका अर्थ है कि किसी वस्तु पर कार्य करने वाला बल (F) वस्तु के द्रव्यमान (m) और उसके त्वरण (a) के गुणनफल के बराबर होता है। इसका अर्थ है कि किसी वस्तु का द्रव्यमान जितना अधिक होगा, उसे गति देने के लिए उतना ही अधिक बल लगाना होगा, और जितना अधिक बल होगा, वस्तु का त्वरण उतना ही अधिक होगा। बल (F) = द्रव्यमान (m) &times; त्वरण (a)। बल का SI मात्रक = न्यूटन (N)।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "43",
                    section: "18",
                    question_en: "<p>43. Sunlight is the main source of ______.</p>",
                    question_hi: "<p>43. सूर्य का प्रकाश ______ का मुख्य स्रोत है।</p>",
                    options_en: [
                        "<p>Vitamin A</p>",
                        "<p>Vitamin B</p>",
                        "<p>Vitamin D</p>",
                        "<p>Vitamin C</p>"
                    ],
                    options_hi: [
                        "<p>विटामिन A</p>",
                        "<p>विटामिन B</p>",
                        "<p>विटामिन D</p>",
                        "<p>विटामिन C</p>"
                    ],
                    solution_en: "<p>43.(c) <strong>Vitamin D (Calciferol) </strong>: Fat-soluble. Deficiency disease - Rickets. Source - Oily fish, Liver, Egg yolks, Liver oil etc. Vitamin and their deficiency diseases : Vitamin A (Retinol) - Night blindness. Vitamin B (Thiamin) - Beri beri. Vitamin B<sub>12</sub> (Cobalamin) - Anaemia. Vitamin C (Ascorbic acid) - Scurvy.</p>",
                    solution_hi: "<p>43.(c) <strong>विटामिन D (कैल्सीफेरॉल) :</strong> यह वसा में घुलनशील विटामिन है। इसकी कमी से होने वाले रोग - रिकेट्स। स्रोत -वसायुक्त मछली, लीवर, अंडे की जर्दी, लीवर ऑयल आदि। विटामिन एवं उनकी कमी से होने वाले रोग: विटामिन A (रेटिनोल) - रतौंधी। विटामिन B (थायमिन) - बेरी - बेरी। विटामिन B<sub>12</sub> (कोबालामिन) - एनीमिया। विटामिन C (एस्कॉर्बिक अम्ल) - स्कर्वी।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "44",
                    section: "18",
                    question_en: "<p>44. On whose advice does the Governor appoint the ministers ?</p>",
                    question_hi: "<p>44. राज्यपाल, किसकी सलाह पर मंत्रियों की नियुक्ति करता है ?</p>",
                    options_en: [
                        "<p>Prime Minister</p>",
                        "<p>President</p>",
                        "<p>Chief Minister</p>",
                        "<p>Vice-President</p>"
                    ],
                    options_hi: [
                        "<p>प्रधानमंत्री</p>",
                        "<p>राष्ट्रपति</p>",
                        "<p>मुख्यमंत्री</p>",
                        "<p>उप-राष्ट्रपति</p>"
                    ],
                    solution_en: "<p>44.(c) <strong>Chief Minister.</strong> According to Article 164, the Governor appoints the Chief Minister, who in turn advises the Governor on appointing other Ministers. The Ministers hold office at the pleasure of the Governor. Additionally, the Council of Ministers is collectively responsible to the State Legislative Assembly.</p>",
                    solution_hi: "<p>44.(c) <strong>मुख्यमंत्री।</strong> अनुच्छेद 164 के अनुसार, राज्यपाल मुख्यमंत्री की नियुक्ति करता है, जो अन्य मंत्रियों की नियुक्ति के संबंध में राज्यपाल को सलाह देता है। मंत्री राज्यपाल की इच्छापर्यन्त पद धारण करते हैं। इसके अतिरिक्त, मंत्रिपरिषद सामूहिक रूप से राज्य विधानसभा के प्रति उत्तरदायी होती है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "45",
                    section: "18",
                    question_en: "<p>45. In which game are terms like break, cannons, cue, potting, and kissed commonly used?</p>",
                    question_hi: "<p>45. ब्रेक, कैनन्स, क्यू, पॉटिंग, और किस्ड जैसे शब्द आमतौर पर किस खेल में उपयोग किए जाते हैं?</p>",
                    options_en: [
                        "<p>Golf</p>",
                        "<p>Polo</p>",
                        "<p>Billiards</p>",
                        "<p>Chess</p>"
                    ],
                    options_hi: [
                        "<p>गोल्फ (Golf)</p>",
                        "<p>पोलो (Polo)</p>",
                        "<p>बिलियर्ड्स (Billiards)</p>",
                        "<p>शतरंज (Chess)</p>"
                    ],
                    solution_en: "<p>45.(c) <strong>Billiards.</strong> Other Terminology of Billiards : Backspin, Cushion, Break Shot, In-Off, Scratch, Jigger, Cue, Stroke. Other Sports Terminology: Polo : Reins, Bump, Hook, Knock-in, Out-of-bounds. Golf : Ace, Albatross, Birdie, Bunker, Double eagle, Eagle, Block, Bounce Back, Club-face, Drive, Fairway, Address, Putt. Chess : Stalemate, Checkmate, Bishop, Gambit, Castling, Sacrifice, Zugzwang.</p>",
                    solution_hi: "<p>45.(c) <strong>बिलियर्ड्स।</strong> बिलियर्ड्स की अन्य शब्दावली: बैकस्पिन, कुशन, ब्रेक शॉट, इन-ऑफ, स्क्रैच, जिगर, क्यू, स्ट्रोक। अन्य खेल के शब्दावली: पोलो: रीन्स, बम्प, हुक, नॉक-इन, आउट-ऑफ-बाउंड्स। गोल्फ़: ऐस, अल्बाट्रॉस, बर्डी, बंकर, डबल ईगल, ईगल, ब्लॉक, बाउंस बैक, क्लब-फेस, ड्राइव, फेयरवे, एड्रेस, पुट। शतरंज: स्टेलमेट, चेकमेट, बिशप, गैम्बिट, कास्टलिंग, सैक्रिफाइस, ज़ुगज़्वांग।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "46",
                    section: "18",
                    question_en: "<p>46. Purvanchal Himalayas does NOT comprise of :</p>",
                    question_hi: "<p>46. पूर्वांचल हिमालय में निम्नलिखित में से कौन-सा शामिल नहीं है?</p>",
                    options_en: [
                        "<p>Naga hills</p>",
                        "<p>Pir Panjal range</p>",
                        "<p>Manipur hills</p>",
                        "<p>Mizo hills</p>"
                    ],
                    options_hi: [
                        "<p>नागा पहाड़ियाँ</p>",
                        "<p>पीर पंजाल पर्वत श्रृंखला</p>",
                        "<p>मणिपुर की पहाड़ियाँ</p>",
                        "<p>मिज़ो पहाड़ियाँ</p>"
                    ],
                    solution_en: "<p>46.(b) <strong>Pir Panjal Range </strong>in the Himalayas is a part of the Lesser Himalayas. The Pir Panjal Range starts near Patni Top in Jammu and Kashmir and runs through Himachal Pradesh to Garhwal. The Purvanchal Himalayas, also known as the Eastern Hills, are a mountain range in northeastern India. This range includes the Patkai Hills, the Naga Hills in Nagaland, the Manipur Hills in Manipur, and the Mizo Hills in Mizoram.</p>",
                    solution_hi: "<p>46.(b) हिमालय में<strong> पीर पंजाल पर्वत श्रृंखला</strong> लघु हिमालय का एक हिस्सा है। पीर पंजाल पर्वतमाला जम्मू-कश्मीर में पटनी टॉप के पास से शुरू होती है और हिमाचल प्रदेश से होते हुए गढ़वाल तक जाती है। पूर्वांचल हिमालय, जिसे पूर्वी पहाड़ियों के नाम से भी जाना जाता है, पूर्वोत्तर भारत में एक पर्वत श्रृंखला है। इस श्रृंखला में पटकाई पहाड़ियाँ, नागालैंड में नागा पहाड़ियाँ, मणिपुर में मणिपुर पहाड़ियाँ और मिज़ोरम में मिज़ो पहाड़ियाँ शामिल हैं।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "47",
                    section: "18",
                    question_en: "<p>47. World Water Week 2024 is observed from which date?</p>",
                    question_hi: "<p>47. विश्व जल सप्ताह 2024 किस तिथि से मनाया जाता है?</p>",
                    options_en: [
                        "<p>August 20-24</p>",
                        "<p>August 25-29</p>",
                        "<p>August 25-29</p>",
                        "<p>August 30 - September 3</p>"
                    ],
                    options_hi: [
                        "<p>20-24 अगस्त</p>",
                        "<p>25-29 अगस्त</p>",
                        "<p>25-29 अगस्त</p>",
                        "<p>30 अगस्त - 3 सितंबर</p>"
                    ],
                    solution_en: "<p>47.(c)<strong> August 25-29.</strong> The theme for 2024, \"Bridging Borders: Water for a Peaceful and Sustainable Future,\" emphasizes the importance of water as a shared resource and the need for international cooperation to ensure its sustainable management for peace and prosperity.</p>",
                    solution_hi: "<p>47.(c) <strong>25-29 अगस्त।</strong> 2024 का थीम, \"सीमाओं को जोड़ना: शांतिपूर्ण और सतत भविष्य के लिए जल\" साझा संसाधन के रूप में जल के महत्व और शांति और समृद्धि के लिए इसके सतत प्रबंधन को सुनिश्चित करने के लिए अंतर्राष्ट्रीय सहयोग की आवश्यकता पर जोर देता है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "48",
                    section: "18",
                    question_en: "<p>48. Into how many distinctive types of territories was the Harshvardhan\'s Dynasty divided?</p>",
                    question_hi: "<p>48. हर्षवर्द्धन का राजवंश कितने विशिष्ट प्रकार के प्रदेशों में विभाजित था ?</p>",
                    options_en: [
                        "<p>Four</p>",
                        "<p>Three</p>",
                        "<p>Two</p>",
                        "<p>Five</p>"
                    ],
                    options_hi: [
                        "<p>चार</p>",
                        "<p>तीन</p>",
                        "<p>दो</p>",
                        "<p>पांच</p>"
                    ],
                    solution_en: "<p>48.(c) <strong>Two.</strong> Harshavardhana, the last ruler of the Vardhana Empire, also known as the Pushyabhuti dynasty, divided his empire into two distinctive types of territories: areas directly under Harsha&rsquo;s rule such as Central Provinces, Gujarat, Bengal, Kalinga, Rajputana, and the states and kingdoms which had become feudatories under him including Jalandhar, Kashmir, Nepal, Sind, Kamarupa (modern-day Assam).</p>",
                    solution_hi: "<p>48.(c) <strong>दो। </strong>हर्षवर्धन, वर्धन साम्राज्य का अंतिम शासक था, जिसे पुष्यभूति वंश के नाम से भी जाना जाता है। वर्धन साम्राज्य दो विशिष्ट प्रकार के क्षेत्रों में विभाजित था: हर्ष के सीधे शासन के अंतर्गत आने वाले क्षेत्र जैसे कि मध्य प्रांत, गुजरात, बंगाल, कलिंग, राजपुताना, और वे राज्य और साम्राज्य जो उसके अधीन सामंत बन गए थे जिनमें जालंधर, कश्मीर, नेपाल, सिंध, कामरूप (आधुनिक असम) शामिल थे।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "49",
                    section: "18",
                    question_en: "<p>49. Which of the following is a straight-chain alkyl carboxylic acid with the chemical formula CH<sub>3</sub>CH<sub>2</sub>CH<sub>2</sub>CO<sub>2</sub>H ?&nbsp;</p>",
                    question_hi: "<p>49. निम्नलिखित में से कौन सा सीधी श्रृंखला वाला एल्काइल कार्बोक्सिलिक अम्ल है जिसका रासायनिक सूत्र CH<sub>3</sub>CH<sub>2</sub>CH<sub>2</sub>CO<sub>2</sub>Hहै?</p>",
                    options_en: [
                        "<p>Methanoic acid</p>",
                        "<p>Propionic acid</p>",
                        "<p>Ethanoic acid</p>",
                        "<p>Butyric acid</p>"
                    ],
                    options_hi: [
                        "<p>मेथेनोइक अम्ल</p>",
                        "<p>प्रोपियॉनिक अम्ल</p>",
                        "<p>ईथेनोइक अम्ल</p>",
                        "<p>ब्यूटाइरिक अम्ल</p>"
                    ],
                    solution_en: "<p>49.(d) <strong>Butyric acid.</strong> It is utilized in the production of various butyrate esters (e.g., methyl butyrate), which have pleasant aromas and tastes and are used as additives in foods, perfumes, flavorings, varnishes, pharmaceuticals, and disinfectants. Methanoic acid (HCOOH), Ethanoic acid (CH<sub>3</sub>COOH), Propanoic acid (CH<sub>3</sub>CH<sub>2</sub>COOH).</p>",
                    solution_hi: "<p>49.(d) <strong>ब्यूटाइरिक अम्ल।</strong> इसका उपयोग विभिन्न ब्यूटाइरेट एस्टर (जैसे, मिथाइल ब्यूटाइरेट) के उत्पादन में किया जाता है, जिनकी सुगंध व स्वाद रोचक होता है और जिनका उपयोग खाद्य पदार्थों, इत्र, स्वाद, वार्निश, फार्मास्यूटिकल्स और कीटाणुनाशक में योजक के रूप में किया जाता है। मेथेनोइक अम्ल (HCOOH), ईथेनोइक अम्ल (CH<sub>3</sub>COOH), प्रोपेनॉइक अम्ल (CH<sub>3</sub>CH<sub>2</sub>COOH)।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "50",
                    section: "17",
                    question_en: "<p>50. In which year did Roshan Mistry become the first Indian woman to win an Asian Games medal when she took the silver in the 100-m sprint ?</p>",
                    question_hi: "<p>50. रोशन मिस्त्री (Roshan Mistry) किस वर्ष 100 m स्प्रिंट (sprint) में रजत पदक जीतकर एशियाई खेलों (Asian Games) में पदक जीतने वाली पहली भारतीय महिला बनीं ?</p>",
                    options_en: [
                        "<p>1954, Asian Games</p>",
                        "<p>1951, Asian Games</p>",
                        "<p>1958, Asian Games</p>",
                        "<p>1962, Asian Games</p>"
                    ],
                    options_hi: [
                        "<p>1954, एशियन गेम्स</p>",
                        "<p>1951, एशियन गेम्स</p>",
                        "<p>1958, एशियन गेम्स</p>",
                        "<p>1962, एशियन गेम्स</p>"
                    ],
                    solution_en: "<p>50.(b) <strong>1951, Asian Games</strong> held in New Delhi. India finished second in the 1951 Asian Games medal table, behind Japan, with a total of 51 medals : 15 gold, 16 silver, and 20 bronze. India\'s first gold medal at the Asian Games was won by Sachin Nag in the men\'s 100 m freestyle swimming event at the 1951 Asian Games. Kamaljeet Kaur Sandhu is the first indian woman to win an asian games gold medal in 400 meter race.</p>",
                    solution_hi: "<p>50.(b) <strong>1951, में एशियाई खेलों</strong> का आयोजन नई दिल्ली में हुआ था। 1951 एशियाई खेलों की पदक तालिका में भारत जापान के बाद दूसरे स्थान पर रहा, जिसमें कुल 51 पदक थे : 15 स्वर्ण, 16 रजत और 20 कांस्य। एशियाई खेलों में भारत का प्रथम स्वर्ण पदक सचिन नाग ने 1951 एशियाई खेलों में पुरुषों की 100 मीटर फ़्रीस्टाइल तैराकी स्पर्धा में जीता था। कमलजीत कौर संधू 400 मीटर दौड़ में एशियाई खेलों का स्वर्ण पदक जीतने वाली प्रथम भारतीय महिला हैं।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "51",
                    section: "17",
                    question_en: "<p>51. A shopkeeper mixes 15 kg of sugar costing ₹11/kg with 22 kg of sugar costing ₹12/kg. What is the cost (in ₹) per kg of the mixture? (Rounded off to 2 decimal places)</p>",
                    question_hi: "<p>51. एक दुकानदार ₹11/kg मूल्य वाली 15 kg चीनी को ₹12/kg मूल्य वाली 22 kg चीनी के साथ मिलाता&nbsp;है। मिश्रण का प्रति kg मूल्य (₹ में) क्या है? (दो दशमलव स्थानोंतक पूर्णांकित)</p>",
                    options_en: [
                        "<p>11.38</p>",
                        "<p>11.51</p>",
                        "<p>11.84</p>",
                        "<p>11.59</p>"
                    ],
                    options_hi: [
                        "<p>11.38</p>",
                        "<p>11.51</p>",
                        "<p>11.84</p>",
                        "<p>11.59</p>"
                    ],
                    solution_en: "<p>51.(d)<br>Price of 1st sugar = 15 &times;&nbsp;11 = ₹ 165 <br>Price of 2nd sugar = 22 &times; 12 = ₹ 264 <br>Total price = 165 + 264 = ₹ 429 <br><math display=\"inline\"><mo>&#8658;</mo></math> cost per kg =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>&#160;</mo><mn>429</mn></mrow><mrow><mn>15</mn><mo>+</mo><mn>22</mn><mo>&#160;</mo></mrow></mfrac></math>=&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>429</mn><mn>37</mn></mfrac></math> = ₹ 11.59</p>",
                    solution_hi: "<p>51.(d)<br>पहली चीनी का मूल्य = 15 &times; 11 = ₹ 165 <br>दूसरी चीनी की मूल्य = 22 &times;&nbsp;12 = ₹ 264 <br>कुल मूल्य = 165 + 264 = ₹ 429&nbsp;<br><math display=\"inline\"><mo>&#8658;</mo></math> लागत प्रति किलो = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>&#160;</mo><mn>429</mn></mrow><mrow><mn>15</mn><mo>+</mo><mn>22</mn><mo>&#160;</mo></mrow></mfrac></math>=&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>429</mn><mn>37</mn></mfrac></math> = ₹ 11.59</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "52",
                    section: "17",
                    question_en: "<p>52. The sides AB and AC of △ABC are produced to points D and E, respectively. The bisectors of &ang;CBD and &ang;BCE meet at P. If &ang;A = 72&deg;, then the measure of &ang;P is:</p>",
                    question_hi: "<p>52. △ABC की भुजाओं AB और AC को क्रमशः बिंदु D और E तक बढ़ाया गया है। &ang;CBD और &ang;BCE के समद्विभाजक P पर मिलते हैं। यदि &ang;A = 72&deg; है, तो &ang;P की माप ज्ञात कीजिए।</p>",
                    options_en: [
                        "<p>65&deg;</p>",
                        "<p>54&deg;</p>",
                        "<p>55&deg;</p>",
                        "<p>35&deg;</p>"
                    ],
                    options_hi: [
                        "<p>65&deg;</p>",
                        "<p>54&deg;</p>",
                        "<p>55&deg;</p>",
                        "<p>35&deg;</p>"
                    ],
                    solution_en: "<p>52.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739263419768.png\" alt=\"rId50\" width=\"211\" height=\"156\"><br>We know that,<br>&ang;CPB = 90&deg; - <math display=\"inline\"><mfrac><mrow><mn>72</mn><mo>&#176;</mo></mrow><mrow><mn>2</mn></mrow></mfrac></math> = 90&deg; - 36&deg; = 54&deg;</p>",
                    solution_hi: "<p>52.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739263419768.png\" alt=\"rId50\" width=\"211\" height=\"156\"><br>हम जानते है कि, <br>&ang;CPB = 90&deg; - <math display=\"inline\"><mfrac><mrow><mn>72</mn><mo>&#176;</mo></mrow><mrow><mn>2</mn></mrow></mfrac></math> = 90&deg; - 36&deg; = 54&deg;</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "53",
                    section: "17",
                    question_en: "<p>53. Pipes A and B can fill a tank in 15 hours and 25 hours, respectively, whereas pipe C can empty the full tank in 40 hours. All three pipes are opened together, but pipe A is closed after 5 hours. After how many hours will the remaining part of the tank be filled ?</p>",
                    question_hi: "<p>53. पाइप A और B एक टंकी को क्रमशः 15 घंटे और 25 घंटे में भर सकते हैं, जबकि पाइप C पूरी टंकी को 40 घंटे में खाली कर सकता है। तीनों पाइप एक साथ खोले जाते हैं, लेकिन पाइप A, 5 घंटे बाद बंद हो जाता है। टंकी का शेष भाग कितने घंटे बाद भर जाएगा ?</p>",
                    options_en: [
                        "<p>41<math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>9</mn></mrow></mfrac></math></p>",
                        "<p>43<math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>9</mn></mrow></mfrac></math></p>",
                        "<p>44<math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>9</mn></mrow></mfrac></math></p>",
                        "<p>39<math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>9</mn></mrow></mfrac></math></p>"
                    ],
                    options_hi: [
                        "<p>41<math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>9</mn></mrow></mfrac></math></p>",
                        "<p>43<math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>9</mn></mrow></mfrac></math></p>",
                        "<p>44<math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>9</mn></mrow></mfrac></math></p>",
                        "<p>39<math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>9</mn></mrow></mfrac></math></p>"
                    ],
                    solution_en: "<p>53.(d) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739263420018.png\" alt=\"rId51\" width=\"323\" height=\"169\"><br>All three pipes capacity of after 5 hours = 5 (40 + 24 - 15) = 245 units <br>Remaining capacity = 600 - 245 = 355 units<br>According to the question,<br>Remaining tank can be filled = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>355</mn><mrow><mn>24</mn><mo>-</mo><mn>15</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>355</mn><mn>9</mn></mfrac></math> = 39<math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>9</mn></mrow></mfrac></math> hours</p>",
                    solution_hi: "<p>53.(d) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739263420204.png\" alt=\"rId52\" width=\"270\" height=\"151\"><br>5 घंटे के बाद तीनों पाइपों की क्षमता = 5 (40 + 24 - 15) = 245 इकाई<br>शेष क्षमता = 600 - 245 = 355 इकाई<br>प्रश्न के अनुसार,<br>शेष टंकी को भरने मे लगा समय = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>355</mn><mrow><mn>24</mn><mo>-</mo><mn>15</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>355</mn><mn>9</mn></mfrac></math> = 39<math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>9</mn></mrow></mfrac></math>&nbsp;घंटे</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "54",
                    section: "17",
                    question_en: "<p>54. K being any odd number greater than 1, k<sup>33</sup> &ndash; k is always divisible by:</p>",
                    question_hi: "<p>54. K 1 से बड़ी कोई भी विषम संख्या होने पर, k<sup>33</sup> &ndash; k हमेशा ________ से विभाज्य होगा।</p>",
                    options_en: [
                        "<p>5</p>",
                        "<p>13</p>",
                        "<p>24</p>",
                        "<p>15</p>"
                    ],
                    options_hi: [
                        "<p>5</p>",
                        "<p>13</p>",
                        "<p>24</p>",
                        "<p>15</p>"
                    ],
                    solution_en: "<p>54.(c) <strong>Given:</strong> k<sup>33</sup> &ndash; k<br>= k(k<sup>32</sup> - k)<br>= k(k<sup>16</sup> - 1)(k<sup>16</sup> + 1)<br>= k(k<sup>8</sup> - 1)(k<sup>8</sup> + 1)(k<sup>16</sup> + 1)<br>= k(k<sup>4</sup> - 1)(k<sup>4</sup> + 1)(k<sup>8</sup> + 1)(k<sup>16</sup> + 1)<br>= k(k<sup>2</sup> - 1)(k<sup>2</sup> + 1)(k<sup>4</sup> + 1)(k<sup>8</sup> + 1)(k<sup>16</sup> + 1)<br>= k(k - 1)(k + 1)(k<sup>2</sup> + 1)(k<sup>4</sup> + 1)(k<sup>8</sup> + 1)(k<sup>16</sup> + 1)<br>Put the value of k = 3, 5 ,7&hellip;..<br>= 3 &times; 2 &times; 4[(3<sup>2</sup> + 1)(3<sup>4</sup> + 1)(3<sup>8</sup> + 1)(3<sup>16</sup> + 1)]<br>= 24 &times; [(3<sup>2</sup> + 1)(3<sup>4</sup> + 1)(3<sup>8</sup> + 1)(3<sup>16</sup> + 1)]<br>From the above expression it is clear that k<sup>33</sup> - k will always be divisible by 24.</p>",
                    solution_hi: "<p>54.(c) <strong>दिया गया:</strong>&nbsp; &nbsp;k<sup>33</sup> &ndash; k<br>= k(k<sup>32</sup> - k)<br>= k(k<sup>16</sup> - 1)(k<sup>16</sup> + 1)<br>= k(k<sup>8</sup> - 1)(k<sup>8</sup> + 1)(k<sup>16</sup> + 1)<br>= k(k<sup>4</sup> - 1)(k<sup>4</sup> + 1)(k<sup>8</sup> + 1)(k<sup>16</sup> + 1)<br>= k(k<sup>2</sup> - 1)(k<sup>2</sup> + 1)(k<sup>4</sup> + 1)(k<sup>8</sup> + 1)(k<sup>16</sup> + 1)<br>= k(k - 1)(k + 1)(k<sup>2</sup> + 1)(k<sup>4</sup> + 1)(k<sup>8</sup> + 1)(k<sup>16</sup> + 1)<br>k का मान = 3, 5, 7 &hellip;.. रखने पर,<br>= 3 &times; 2 &times; 4[(3<sup>2</sup> + 1)(3<sup>4</sup> + 1)(3<sup>8</sup> + 1)(3<sup>16</sup> + 1)]<br>= 24 &times; [(3<sup>2</sup> + 1)(3<sup>4</sup> + 1)(3<sup>8</sup> + 1)(3<sup>16</sup> + 1)]<br>उपरोक्त अभिव्यक्ति से यह स्पष्ट है कि k<sup>33</sup> - k हमेशा 24 से विभाज्य होगा ।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "55",
                    section: "17",
                    question_en: "<p>55. The centres of two circles are 84 cm apart. If the radii of these two circles are 38 cm and 26 cm, respectively, then which of the following options gives the length (in cm) of a direct common tangent of these two circles?</p>",
                    question_hi: "<p>55. दो वृत्तों के केंद्रों के बीच की दूरी 84 cm है। यदि इन दोनों वृत्तों की त्रिज्याएँ क्रमशः 38 cm और 26 cm हैं, तो निम्नलिखित में से कौन-सा विकल्प इन दोनों वृत्तों की सीधी उभयनिष्ठ स्पर्श रेखा (direct common tangent) की लंबाई (cm में) देता है?</p>",
                    options_en: [
                        "<p>48<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math></p>",
                        "<p>54 <math display=\"inline\"><msqrt><mn>3</mn></msqrt></math></p>",
                        "<p>42<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math></p>",
                        "<p>63<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math></p>"
                    ],
                    options_hi: [
                        "<p>48<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math></p>",
                        "<p>54 <math display=\"inline\"><msqrt><mn>3</mn></msqrt></math></p>",
                        "<p>42<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math></p>",
                        "<p>63<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math></p>"
                    ],
                    solution_en: "<p>55.(a)<br>Direct common tangent = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mi mathvariant=\"normal\">d</mi><mn>2</mn></msup><mo>-</mo><msup><mrow><mo>(</mo><mi mathvariant=\"normal\">R</mi><mo>-</mo><mi mathvariant=\"normal\">r</mi><mo>)</mo></mrow><mn>2</mn></msup></msqrt></math><br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mrow><mo>(</mo><mn>84</mn><mo>)</mo></mrow><mn>2</mn></msup><mo>-</mo><msup><mrow><mo>(</mo><mn>38</mn><mo>-</mo><mn>26</mn><mo>)</mo></mrow><mn>2</mn></msup></msqrt></math><br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>7056</mn><mo>-</mo><mn>144</mn></msqrt></math><br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>6912</mn></msqrt></math><br>= 48<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math></p>",
                    solution_hi: "<p>55.(a)<br>उभयनिष्ठ स्पर्श रेखा की लम्बाई = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mi mathvariant=\"normal\">d</mi><mn>2</mn></msup><mo>-</mo><msup><mrow><mo>(</mo><mi mathvariant=\"normal\">R</mi><mo>-</mo><mi mathvariant=\"normal\">r</mi><mo>)</mo></mrow><mn>2</mn></msup></msqrt></math><br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mrow><mo>(</mo><mn>84</mn><mo>)</mo></mrow><mn>2</mn></msup><mo>-</mo><msup><mrow><mo>(</mo><mn>38</mn><mo>-</mo><mn>26</mn><mo>)</mo></mrow><mn>2</mn></msup></msqrt></math><br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>7056</mn><mo>-</mo><mn>144</mn></msqrt></math><br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>6912</mn></msqrt></math><br>= 48<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math><br>= 48<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math> cm</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "56",
                    section: "17",
                    question_en: "<p>56.The following bar graph shows the sales of a company (in₹ crore) in different years. Study the graph and answer the question. <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739263420335.png\" alt=\"rId53\" width=\"350\"> <br>The mean of the highest and lowest sales (in₹ crore) is:</p>",
                    question_hi: "<p>56. निम्नलिखित बार ग्राफ दिए गए वर्षों में एक कंपनी द्वारा की गई बिक्री (₹ करोड़ में) को दर्शाता है। ग्राफ का अध्ययन करें और प्रश्न का उत्तर दें। <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739263420438.png\" alt=\"rId54\" width=\"379\" height=\"251\"> <br>अधिकतम और न्यूनतम बिक्री का माध्य (₹ करोड़ में) कितना है?</p>",
                    options_en: [
                        "<p>326.7</p>",
                        "<p>394.5</p>",
                        "<p>484.2</p>",
                        "<p>412.3</p>"
                    ],
                    options_hi: [
                        "<p>326.7</p>",
                        "<p>394.5</p>",
                        "<p>484.2</p>",
                        "<p>412.3</p>"
                    ],
                    solution_en: "<p>56.(b) Mean of the highest and lowest sales = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>584</mn><mo>+</mo><mn>205</mn></mrow><mn>2</mn></mfrac></math> = 394.5</p>",
                    solution_hi: "<p>56.(b) उच्चतम और न्यूनतम बिक्री का माध्य = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>584</mn><mo>+</mo><mn>205</mn></mrow><mn>2</mn></mfrac></math>&nbsp;= 394.5</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "57",
                    section: "17",
                    question_en: "<p>57. A cooker is marked at ₹12,500. The dealer allows successive discounts of 6%, 6% and 4% on it. What is the net selling price of the cooker?</p>",
                    question_hi: "<p>57. एक कुकर पर ₹12,500 अंकित है। विक्रेता इस पर 6%, 6% और 4% की क्रमिक छूट प्रदान करता है। कुकर का शुद्ध विक्रय मूल्य क्या है?</p>",
                    options_en: [
                        "<p>₹10,603.20</p>",
                        "<p>₹10,306</p>",
                        "<p>₹12,230</p>",
                        "<p>₹12,320.60</p>"
                    ],
                    options_hi: [
                        "<p>₹10,603.20</p>",
                        "<p>₹10,306</p>",
                        "<p>₹12,230</p>",
                        "<p>₹12,320.60</p>"
                    ],
                    solution_en: "<p>57.(a) Selling price of cooker = 12500 &times; <math display=\"inline\"><mfrac><mrow><mn>47</mn></mrow><mrow><mn>50</mn></mrow></mfrac></math> &times; <math display=\"inline\"><mfrac><mrow><mn>47</mn></mrow><mrow><mn>50</mn></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>24</mn><mn>25</mn></mfrac></math> <br>=&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>47</mn><mo>&#215;</mo><mn>47</mn><mo>&#215;</mo><mn>24</mn></mrow><mn>5</mn></mfrac></math> <br>=&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>53016</mn><mn>5</mn></mfrac></math> <br>= ₹10603.20</p>",
                    solution_hi: "<p>57.(a) कुकर का विक्रय मूल्य = 12500 &times; <math display=\"inline\"><mfrac><mrow><mn>47</mn></mrow><mrow><mn>50</mn></mrow></mfrac></math> &times; <math display=\"inline\"><mfrac><mrow><mn>47</mn></mrow><mrow><mn>50</mn></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>24</mn><mn>25</mn></mfrac></math> <br>=&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>47</mn><mo>&#215;</mo><mn>47</mn><mo>&#215;</mo><mn>24</mn></mrow><mn>5</mn></mfrac></math> <br>=&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>53016</mn><mn>5</mn></mfrac></math> <br>= ₹10603.20</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "58",
                    section: "17",
                    question_en: "<p>58. The value of 5 + 24 &times; 8 &divide;&nbsp;8 of 8 - 8 &divide; 8 &times; 4 of 4 is equal to:</p>",
                    question_hi: "<p>58. 5 + 24 &times; 8 &divide; 8 का 8 - 8 &divide; 8 &times; 4 का 4, का मान ज्ञात कीजिए |</p>",
                    options_en: [
                        "<p>8</p>",
                        "<p>9</p>",
                        "<p>-8</p>",
                        "<p>-9</p>"
                    ],
                    options_hi: [
                        "<p>8</p>",
                        "<p>9</p>",
                        "<p>-8</p>",
                        "<p>-9</p>"
                    ],
                    solution_en: "<p>58.(c) 5 + 24 &times; 8 &divide;&nbsp;8 of 8 - 8 &divide; 8 &times; 4 of 4<br>= 5 + 24 &times; 8 &divide; 64 - 1 &times; 16<br>= 5 + 3 - 16 = -8</p>",
                    solution_hi: "<p>58.(c) 5 + 24 &times; 8 &divide;&nbsp;8 का 8 - 8 &divide; 8 &times; 4 का 4<br>= 5 + 24 &times; 8 &divide; 64 - 1 &times; 16<br>= 5 + 3 - 16 = -8</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "59",
                    section: "17",
                    question_en: "<p>59. A motorboat, whose speed is 15 km/h in still water goes 20 km downstream and comes back in a total of 4 hours. The speed of the stream (in km/h) is:</p>",
                    question_hi: "<p>59. एक मोटरबोट की चाल स्थिर जल में 15 km/h है। उसे धारा की दिशा में 20 km जाने और वहाँ से वापस आने में कुल 4 घंटे लगते हैं। तो धारा की चाल (km/h) क्या है?</p>",
                    options_en: [
                        "<p>4<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math></p>",
                        "<p>7<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math></p>",
                        "<p>6<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math></p>",
                        "<p>5<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math></p>"
                    ],
                    options_hi: [
                        "<p>4<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math></p>",
                        "<p>7<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math></p>",
                        "<p>6<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math></p>",
                        "<p>5<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math></p>"
                    ],
                    solution_en: "<p>59.(d)&nbsp;Let speed of stream be x<br>ATQ,<br><math display=\"inline\"><mfrac><mrow><mn>20</mn></mrow><mrow><mn>15</mn><mo>+</mo><mi>x</mi></mrow></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>20</mn><mrow><mn>15</mn><mo>-</mo><mi mathvariant=\"normal\">x</mi></mrow></mfrac></math> = 4<br>20<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><mfrac><mrow><mn>15</mn><mo>-</mo><mi mathvariant=\"normal\">x</mi><mo>+</mo><mn>15</mn><mo>+</mo><mi mathvariant=\"normal\">x</mi></mrow><mrow><mo>(</mo><mn>15</mn><mo>-</mo><mi mathvariant=\"normal\">x</mi><mo>)</mo><mo>(</mo><mn>15</mn><mo>-</mo><mi mathvariant=\"normal\">x</mi><mo>)</mo></mrow></mfrac><mo>)</mo></math> = 4<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>30</mn><mrow><mn>225</mn><mo>-</mo><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>5</mn></mfrac></math><br>225 - <math display=\"inline\"><msup><mrow><mi>x</mi></mrow><mrow><mn>2</mn></mrow></msup></math> = 150<br><math display=\"inline\"><msup><mrow><mi>x</mi></mrow><mrow><mn>2</mn></mrow></msup></math> = 75<br><math display=\"inline\"><mo>&#8658;</mo></math> x = 5<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math></p>",
                    solution_hi: "<p>59.(d)&nbsp;माना धारा की गति x है<br>प्रश्न के अनुसार,<br><math display=\"inline\"><mfrac><mrow><mn>20</mn></mrow><mrow><mn>15</mn><mo>+</mo><mi>x</mi></mrow></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>20</mn><mrow><mn>15</mn><mo>-</mo><mi mathvariant=\"normal\">x</mi></mrow></mfrac></math> = 4<br>20<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><mfrac><mrow><mn>15</mn><mo>-</mo><mi mathvariant=\"normal\">x</mi><mo>+</mo><mn>15</mn><mo>+</mo><mi mathvariant=\"normal\">x</mi></mrow><mrow><mo>(</mo><mn>15</mn><mo>-</mo><mi mathvariant=\"normal\">x</mi><mo>)</mo><mo>(</mo><mn>15</mn><mo>-</mo><mi mathvariant=\"normal\">x</mi><mo>)</mo></mrow></mfrac><mo>)</mo></math> = 4<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>30</mn><mrow><mn>225</mn><mo>-</mo><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>5</mn></mfrac></math><br>225 - <math display=\"inline\"><msup><mrow><mi>x</mi></mrow><mrow><mn>2</mn></mrow></msup></math> = 150<br><math display=\"inline\"><msup><mrow><mi>x</mi></mrow><mrow><mn>2</mn></mrow></msup></math> = 75<br><math display=\"inline\"><mo>&#8658;</mo></math> x = 5<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "60",
                    section: "17",
                    question_en: "<p>60. What is the value of cos<sup>-1</sup> (<math display=\"inline\"><mfrac><mrow><mn>7</mn></mrow><mrow><mn>25</mn></mrow></mfrac></math>) - cos<sup>-1</sup> (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>15</mn><mn>17</mn></mfrac></math>) ?</p>",
                    question_hi: "<p>60. cos<sup>-1</sup> (<math display=\"inline\"><mfrac><mrow><mn>7</mn></mrow><mrow><mn>25</mn></mrow></mfrac></math>) - cos<sup>-1</sup> (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>15</mn><mn>17</mn></mfrac></math>) का मान क्या है ?</p>",
                    options_en: [
                        "<p>cos<sup>-1</sup> (<math display=\"inline\"><mfrac><mrow><mn>304</mn></mrow><mrow><mn>425</mn></mrow></mfrac></math>)</p>",
                        "<p>cot<sup>-1</sup>(<math display=\"inline\"><mfrac><mrow><mn>297</mn></mrow><mrow><mn>425</mn></mrow></mfrac></math>)</p>",
                        "<p>tan<sup>-1</sup>(<math display=\"inline\"><mfrac><mrow><mn>297</mn></mrow><mrow><mn>425</mn></mrow></mfrac></math>)</p>",
                        "<p>sin<sup>-1</sup> (<math display=\"inline\"><mfrac><mrow><mn>304</mn></mrow><mrow><mn>425</mn></mrow></mfrac></math>)</p>"
                    ],
                    options_hi: [
                        "<p>cos<sup>-1</sup>(<math display=\"inline\"><mfrac><mrow><mn>304</mn></mrow><mrow><mn>425</mn></mrow></mfrac></math>)</p>",
                        "<p>cot<sup>-1</sup>(<math display=\"inline\"><mfrac><mrow><mn>297</mn></mrow><mrow><mn>425</mn></mrow></mfrac></math>)</p>",
                        "<p>tan<sup>-1</sup>(<math display=\"inline\"><mfrac><mrow><mn>297</mn></mrow><mrow><mn>425</mn></mrow></mfrac></math>)</p>",
                        "<p>sin<sup>-1</sup> (<math display=\"inline\"><mfrac><mrow><mn>304</mn></mrow><mrow><mn>425</mn></mrow></mfrac></math>)</p>"
                    ],
                    solution_en: "<p>60.(d)<br>Let cos<sup>-1</sup> (<math display=\"inline\"><mfrac><mrow><mn>7</mn></mrow><mrow><mn>25</mn></mrow></mfrac></math>) = x &rArr; cosx = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>25</mn></mfrac></math><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739263420590.png\" alt=\"rId55\" width=\"189\" height=\"139\"><br>Then sinx = <math display=\"inline\"><mfrac><mrow><mn>24</mn></mrow><mrow><mn>25</mn></mrow></mfrac></math><br>and cos<sup>-1</sup> (<math display=\"inline\"><mfrac><mrow><mn>15</mn></mrow><mrow><mn>17</mn></mrow></mfrac></math>) = y &rArr; cosy = <math display=\"inline\"><mfrac><mrow><mn>15</mn></mrow><mrow><mn>17</mn></mrow></mfrac></math><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739263420751.png\" alt=\"rId56\" width=\"200\"><br>Then, siny = <math display=\"inline\"><mfrac><mrow><mn>8</mn></mrow><mrow><mn>17</mn></mrow></mfrac></math><br>As we know,sin(x - y) = sinx &times; cosy - cosxsiny = <math display=\"inline\"><mfrac><mrow><mn>24</mn></mrow><mrow><mn>25</mn></mrow></mfrac></math> &times; <math display=\"inline\"><mfrac><mrow><mn>15</mn></mrow><mrow><mn>17</mn></mrow></mfrac></math> - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>25</mn></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>8</mn><mn>17</mn></mfrac></math> =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>&#160;</mo><mn>360</mn><mo>-</mo><mn>56</mn></mrow><mrow><mn>425</mn><mo>&#160;</mo></mrow></mfrac></math> = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>304</mn><mn>425</mn></mfrac></math><br>(x - y) = sin<sup>-1</sup><math display=\"inline\"><mo>(</mo><mfrac><mrow><mn>304</mn></mrow><mrow><mn>425</mn></mrow></mfrac><mo>)</mo></math><br>Then, cos<sup>-1</sup> (<math display=\"inline\"><mfrac><mrow><mn>7</mn></mrow><mrow><mn>25</mn></mrow></mfrac></math>) - cos<sup>-1</sup> (<math display=\"inline\"><mfrac><mrow><mn>15</mn></mrow><mrow><mn>17</mn></mrow></mfrac></math>) = sin<sup>-1</sup>(<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>304</mn><mn>425</mn></mfrac></math>)</p>",
                    solution_hi: "<p>60.(d)<br>माना cos<sup>-1</sup> (<math display=\"inline\"><mfrac><mrow><mn>7</mn></mrow><mrow><mn>25</mn></mrow></mfrac></math>) = x &rArr; cosx = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>25</mn></mfrac></math><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739263420590.png\" alt=\"rId55\" width=\"189\" height=\"139\"><br>तब, sinx = <math display=\"inline\"><mfrac><mrow><mn>24</mn></mrow><mrow><mn>25</mn></mrow></mfrac></math><br>और cos<sup>-1</sup> (<math display=\"inline\"><mfrac><mrow><mn>15</mn></mrow><mrow><mn>17</mn></mrow></mfrac></math>) = y &rArr; cosy = <math display=\"inline\"><mfrac><mrow><mn>15</mn></mrow><mrow><mn>17</mn></mrow></mfrac></math><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739263420751.png\" alt=\"rId56\" width=\"200\"><br>तब, siny = <math display=\"inline\"><mfrac><mrow><mn>8</mn></mrow><mrow><mn>17</mn></mrow></mfrac></math><br>जैसा कि हम जानते है sin(x - y) = sinx &times; cosy - cosxsiny = <math display=\"inline\"><mfrac><mrow><mn>24</mn></mrow><mrow><mn>25</mn></mrow></mfrac></math> &times; <math display=\"inline\"><mfrac><mrow><mn>15</mn></mrow><mrow><mn>17</mn></mrow></mfrac></math> - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>25</mn></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>8</mn><mn>17</mn></mfrac></math> =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>&#160;</mo><mn>360</mn><mo>-</mo><mn>56</mn></mrow><mrow><mn>425</mn><mo>&#160;</mo></mrow></mfrac></math> = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>304</mn><mn>425</mn></mfrac></math><br>(x - y) = sin<sup>-1</sup><math display=\"inline\"><mo>(</mo><mfrac><mrow><mn>304</mn></mrow><mrow><mn>425</mn></mrow></mfrac><mo>)</mo></math><br>फिर, cos<sup>-1</sup> (<math display=\"inline\"><mfrac><mrow><mn>7</mn></mrow><mrow><mn>25</mn></mrow></mfrac></math>) - cos<sup>-1</sup> (<math display=\"inline\"><mfrac><mrow><mn>15</mn></mrow><mrow><mn>17</mn></mrow></mfrac></math>) = sin<sup>-1</sup>(<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>304</mn><mn>425</mn></mfrac></math>)</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "61",
                    section: "17",
                    question_en: "<p>61. The ratio of the length, the breadth, and the height of a solid cuboid is given as 3 : 2 : 1, and the breadth of this cuboid is given as 12 cm. Find the volume (in cm<sup>3</sup>) of the cuboid.</p>",
                    question_hi: "<p>61. एक ठोस घनाभ की लंबाई , चौड़ाई और ऊंचाई का अनुपात 3 : 2 : 1 दिया गया है और इस घनाभ की चौड़ाई 12cm दी गई है | घनाभ का आयतन (cm<sup>3</sup> में) ज्ञात कीजिए |</p>",
                    options_en: [
                        "<p>1250</p>",
                        "<p>1296</p>",
                        "<p>1200</p>",
                        "<p>1500</p>"
                    ],
                    options_hi: [
                        "<p>1250</p>",
                        "<p>1296</p>",
                        "<p>1200</p>",
                        "<p>1500</p>"
                    ],
                    solution_en: "<p>61.(b)<br>Length : breadth : height = 3 : 2 : 1 <br>Breadth (2 unit) = 12 cm <br>Length (3 unit) = 18 cm<br>Height (1 unit) = 6 cm<br>Volume = 18 &times;&nbsp;12 &times; 6 = 1296 cm<sup>3</sup></p>",
                    solution_hi: "<p>61.(b)<br>लंबाई : चौड़ाई : ऊंचाई = 3 : 2 : 1 <br>चौड़ाई (2 इकाई) = 12 cm <br>लंबाई (3 इकाई) = 18 cm<br>ऊँचाई (1 इकाई) = 6 cm<br>आयतन = 18 &times; 12 &times; 6 = 1296 cm<sup>3</sup></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "62",
                    section: "17",
                    question_en: "<p>62. If x&nbsp;&gt; 1 and x<sup>2 </sup>+&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup></mfrac></math> = 83, then x<sup>3 </sup>-&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msup><mi mathvariant=\"normal\">x</mi><mn>3</mn></msup></mfrac></math> is:</p>",
                    question_hi: "<p>62. यदि x&nbsp;&gt; 1 और x<sup>2 </sup>+&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup></mfrac></math> = 83 है, तो x<sup>3 </sup>-&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msup><mi mathvariant=\"normal\">x</mi><mn>3</mn></msup></mfrac></math> का मान ज्ञात कीजिए।</p>",
                    options_en: [
                        "<p>884</p>",
                        "<p>876</p>",
                        "<p>754</p>",
                        "<p>756</p>"
                    ],
                    options_hi: [
                        "<p>884</p>",
                        "<p>876</p>",
                        "<p>754</p>",
                        "<p>756</p>"
                    ],
                    solution_en: "<p>62.(d) x<sup>2 </sup>+&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup></mfrac></math>&nbsp;= 83<br>Subtract both side by 2 in above equation,<br><math display=\"inline\"><mo>&#8658;</mo></math> x<sup>2 </sup>+&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup></mfrac></math> - 2 = 83 - 2<br><math display=\"inline\"><mo>&#8658;</mo></math>&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><mi mathvariant=\"normal\">x</mi><mo>-</mo><mfrac><mn>1</mn><mi mathvariant=\"normal\">x</mi></mfrac><mo>)</mo></mrow><mn>2</mn></msup></math> = 81<br><math display=\"inline\"><mo>&#8658;</mo></math> x - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi>x</mi></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>81</mn></msqrt></math> = 9<br>We know that,<br>x<sup>3 </sup>-&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msup><mi mathvariant=\"normal\">x</mi><mn>3</mn></msup></mfrac></math> = a<sup>3</sup> + 3a<br>= 9&sup3; + 3 &times; 9<br>= 729 + 27 = 756</p>",
                    solution_hi: "<p>62.(d) x<sup>2 </sup>+&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup></mfrac></math> = 83<br>उपरोक्त समीकरण में दोनों पक्षों को 2 से घटाएं,<br><math display=\"inline\"><mo>&#8658;</mo></math> x<sup>2 </sup>+&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup></mfrac></math> - 2 = 83 - 2<br><math display=\"inline\"><mo>&#8658;</mo></math>&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><mi mathvariant=\"normal\">x</mi><mo>-</mo><mfrac><mn>1</mn><mi mathvariant=\"normal\">x</mi></mfrac><mo>)</mo></mrow><mn>2</mn></msup></math> = 81<br><math display=\"inline\"><mo>&#8658;</mo></math> x - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi>x</mi></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>81</mn></msqrt></math> = 9<br>हम वह जानते हैं,<br>x<sup>3 </sup>-&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msup><mi mathvariant=\"normal\">x</mi><mn>3</mn></msup></mfrac></math> = a<sup>3</sup> + 3a<br>= 9&sup3; + 3 &times; 9<br>= 729 + 27 = 756</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "63",
                    section: "17",
                    question_en: "<p>63. The average age of a mother and her daughter is 42 years. The ratio of their ages is 9 : 5, respectively. Find the daughter\'s age (in years).</p>",
                    question_hi: "<p>63. एक माँ और उसकी पुत्री की औसत आयु 42 वर्ष है। उनकी आयु का अनुपात क्रमशः 9 : 5 है। पुत्री की आयु (वर्षों में) ज्ञात कीजिए।</p>",
                    options_en: [
                        "<p>30</p>",
                        "<p>35</p>",
                        "<p>20</p>",
                        "<p>25</p>"
                    ],
                    options_hi: [
                        "<p>30</p>",
                        "<p>35</p>",
                        "<p>20</p>",
                        "<p>25</p>"
                    ],
                    solution_en: "<p>63.(a)<br>Ratio <math display=\"inline\"><mo>&#8594;</mo></math> Daughter&nbsp; :&nbsp; Mother<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;5&nbsp; &nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; 9<br>Average = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5</mn><mo>+</mo><mn>9</mn></mrow><mn>2</mn></mfrac></math> = 7 unit<br>7 unit = 42 years<br>Daughter&rsquo;s age (5 unit) = <math display=\"inline\"><mfrac><mrow><mn>42</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> &times; 5 = 30 years</p>",
                    solution_hi: "<p>63.(a)<br>अनुपात <math display=\"inline\"><mo>&#8594;</mo></math> बेटी : माँ<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 5&nbsp; :&nbsp; 9<br>औसत = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5</mn><mo>+</mo><mn>9</mn></mrow><mn>2</mn></mfrac></math>&nbsp;= 7 इकाई<br>7 इकाई = 42 वर्ष<br>पुत्री की आयु (5 इकाई) = <math display=\"inline\"><mfrac><mrow><mn>42</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> &times; 5 = 30 वर्ष</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "64",
                    section: "17",
                    question_en: "<p>64. A can complete a piece of work alone in 200 days, while B can complete the same piece of work alone in 100 days. In every three-day cycle, both A and B work on day 1, only A works on day 2, and only B works on day 3. This cycle continues till the work is completed. How many days in all does it take the duo to complete the work?</p>",
                    question_hi: "<p>64. A अकेले किसी कार्य को 200 दिनों में पूरा कर सकता है, जबकि B उसी कार्य को अकेले 100 दिनों में पूरा कर सकता है। प्रत्येक तीन-दिवसीय चक्र में पहले दिन A और B दोनों काम करते हैं, दूसरे दिन केवल A काम करता है और तीसरे दिन केवल B काम करता है। यह चक्र, कार्य पूरा होने तक जारी रहता है। दोनों मिलकर कार्य को पूरा करने में कितने दिन लेते हैं?</p>",
                    options_en: [
                        "<p>100<math display=\"inline\"><mfrac><mrow><mn>1</mn><mi>&#160;</mi></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>",
                        "<p>99<math display=\"inline\"><mfrac><mrow><mn>2</mn><mi>&#160;</mi></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>",
                        "<p>100<math display=\"inline\"><mfrac><mrow><mn>1</mn><mi>&#160;</mi></mrow><mrow><mn>2</mn></mrow></mfrac></math></p>",
                        "<p>100</p>"
                    ],
                    options_hi: [
                        "<p>100<math display=\"inline\"><mfrac><mrow><mn>1</mn><mi>&#160;</mi></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>",
                        "<p>99<math display=\"inline\"><mfrac><mrow><mn>2</mn><mi>&#160;</mi></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>",
                        "<p>100<math display=\"inline\"><mfrac><mrow><mn>1</mn><mi>&#160;</mi></mrow><mrow><mn>2</mn></mrow></mfrac></math></p>",
                        "<p>100</p>"
                    ],
                    solution_en: "<p>64.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739263420863.png\" alt=\"rId57\" width=\"134\" height=\"182\"><br>Working pattern of A &amp; B is (A,B), (A), (B).............<br>Work done in 1 cycle(3 days) = (2 + 1) + 1 + 2 = 6 unit<br>Work done in 33 cycle (99 days) = 6 &times; 33 = 198 unit<br>Remaining work = 200 - 198 = 2 unit which is completed by (A &amp; B) together in <math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> days.<br>So, the time taken to complete the whole work in 99<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> days.</p>",
                    solution_hi: "<p>64.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739263420996.png\" alt=\"rId58\" width=\"102\" height=\"150\"><br>A और B का कार्य पैटर्न (A, B), (A), (B).............है ।<br>1 चक्र(3 दिन) में किया गया कार्य = (2 + 1) + 1 + 2 = 6 इकाई&nbsp;<br>33 चक्र (99 दिन) में किया गया कार्य = 6 &times; 33 = 198 इकाई&nbsp;<br>शेष कार्य = 200 - 198 = 2 इकाई जिसे (A और B) ने मिलकर <math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> दिनों में पूरा किया। <br>अतः, पूरा कार्य करने मे लिया गया समय = 99<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> दिन</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "65",
                    section: "17",
                    question_en: "<p>65. Two trains of lengths 310 m and 330 m, respectively, are 160 m apart. They start moving towards each other on parallel tracks, at speeds 130 km/h and 158 km/h, respectively. In how much time (in seconds) will the trains cross each other?</p>",
                    question_hi: "<p>65. क्रमशः 310 m और 330 m लंबी दो रेलगाड़ियां एक-दूसरे से 160 m की दूरी पर हैं। वे समानांतर पटरियों पर क्रमशः 130 km/h और 158 km/h की चाल से एक दूसरे की ओर बढ़ना शुरू करती हैं। दोनों रेलगाड़ियां एक-दूसरे को कितने समय में (सेकंड में) पार करेंगी?</p>",
                    options_en: [
                        "<p>10</p>",
                        "<p>18</p>",
                        "<p>8</p>",
                        "<p>12</p>"
                    ],
                    options_hi: [
                        "<p>10</p>",
                        "<p>18</p>",
                        "<p>8</p>",
                        "<p>12</p>"
                    ],
                    solution_en: "<p>65.(a) <br>Relative speed = 130 + 158 = 288 km/h or 80 m/s<br>Time = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>distance</mi><mi>speed</mi></mfrac></math><br>Time = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>310</mn><mo>+</mo><mn>330</mn><mo>+</mo><mn>160</mn></mrow><mn>80</mn></mfrac></math> = 10 seconds</p>",
                    solution_hi: "<p>65.(a) <br>सापेक्ष गति = 130 + 158 = 288 km/h या 80 m/s<br>समय = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2342;&#2370;&#2352;&#2368;</mi><mo>&#160;</mo><mi mathvariant=\"normal\">&#160;</mi></mrow><mrow><mi>&#2327;&#2340;&#2367;</mi><mo>&#160;</mo></mrow></mfrac></math><br>समय = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>310</mn><mo>+</mo><mn>330</mn><mo>+</mo><mn>160</mn></mrow><mn>80</mn></mfrac></math>&nbsp;= 10 सेकंड</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "66",
                    section: "17",
                    question_en: "<p>66. In an election between P, R and S, <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>10</mn></mrow></mfrac><mi>&#160;</mi></math>of the total votes polled are invalid. Votes polled in favour of R and S are half of the total votes polled, and they get equal votes. Due to a fault in the machine, half of the votes of S and half of the invalid votes are counted additionally in favour of R. What is the overall percentage of votes secured by R due to the fault?</p>",
                    question_hi: "<p>66. P, R और S के बीच एक चुनाव में डाले गए कुल मतों में से <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>10</mn></mrow></mfrac><mi>&#160;</mi></math>मत अमान्य हैं। R और S के पक्ष में डाले गए मत कुल डाले गए मतों के आधे हैं, और उन्हें बराबर मत मिलते हैं। मशीन में खराबी के कारण, S के आधे मत और अमान्य मतों के आधे अतिरिक्त रूप से R के पक्ष में गिने जाते हैं। गलती के कारण R द्वारा प्राप्त मतों का कुल प्रतिशत कितना है?</p>",
                    options_en: [
                        "<p>37.55%</p>",
                        "<p>41.55%</p>",
                        "<p>38.5%</p>",
                        "<p>42.5%</p>"
                    ],
                    options_hi: [
                        "<p>37.55%</p>",
                        "<p>41.55%</p>",
                        "<p>38.5%</p>",
                        "<p>42.5%</p>"
                    ],
                    solution_en: "<p>66.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739263421107.png\" alt=\"rId59\" width=\"213\" height=\"142\"><br>Due to fault <math display=\"inline\"><mo>&#8594;</mo></math> R = 25 + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"normal\">S</mi><mn>2</mn></mfrac></math> +&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>Invalid</mi><mn>2</mn></mfrac></math> = 25 + 12.5 + 5 = 42.5<br>Overall percentage of votes secured by R = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>42</mn><mo>.</mo><mn>5</mn></mrow><mn>100</mn></mfrac></math> &times;&nbsp;100 = 42.5%</p>",
                    solution_hi: "<p>66.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739263421219.png\" alt=\"rId60\" width=\"216\" height=\"155\"><br>खराबी के कारण <math display=\"inline\"><mo>&#8594;</mo></math> R = 25 + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"normal\">S</mi><mn>2</mn></mfrac></math> +&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2309;&#2350;&#2366;&#2344;&#2381;&#2351;</mi><mo>&#160;</mo><mi>&#2350;&#2340;</mi></mrow><mn>2</mn></mfrac></math> = 25 + 12.5 + 5 = 42.5<br>R द्वारा प्राप्त वोटों का कुल प्रतिशत = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>42</mn><mo>.</mo><mn>5</mn></mrow><mn>100</mn></mfrac></math> &times; 100 = 42.5%</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "67",
                    section: "17",
                    question_en: "<p>67. A merchant sold two refrigerators for ₹10,000 each. On one refrigerator, he incurred a 20% loss, and on the other, he gained 20%. What was his profit/loss percentage on the whole transaction?</p>",
                    question_hi: "<p>67. एक व्यापारी प्रत्येक ₹10,000 में दो रेफ्रिजरेटर बेचता है। एक रेफ्रिजरेटर पर उसे 20% की हानि होती है और दूसरे पर उसे 20% का लाभ होता है। पूरे लेन-देन में उसका लाभ हानि प्रतिशत क्या था?</p>",
                    options_en: [
                        "<p>Profit of 2%</p>",
                        "<p>Loss of 2%</p>",
                        "<p>Loss of 4%</p>",
                        "<p>Profit of 4%</p>"
                    ],
                    options_hi: [
                        "<p>2% का लाभ</p>",
                        "<p>2% की हानि</p>",
                        "<p>4% की हानि</p>",
                        "<p>4% का लाभ</p>"
                    ],
                    solution_en: "<p>67.(c) <br>Required % = 20% - 20% - <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>20</mn><mo>&#215;</mo><mn>20</mn></mrow><mn>100</mn></mfrac></math>% = - 4% (- sign denotes loss)</p>",
                    solution_hi: "<p>67.(c) <br>आवश्यक % = 20% - 20% - <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>20</mn><mo>&#215;</mo><mn>20</mn></mrow><mn>100</mn></mfrac></math>% = - 4% (- चिन्ह हानि दर्शाता है)</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "68",
                    section: "17",
                    question_en: "<p>68. If tanA = <math display=\"inline\"><msqrt><mn>2</mn></msqrt></math> - 1 then find the value of sinAcosA + tanA.</p>",
                    question_hi: "<p>68. यदि tanA = <math display=\"inline\"><msqrt><mn>2</mn></msqrt></math> - 1 है, तो sinAcosA + tanA का मान ज्ञात कीजिए।</p>",
                    options_en: [
                        "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>24</mn><msqrt><mn>2</mn></msqrt><mo>+</mo><mn>15</mn></mrow><mn>4</mn></mfrac></math></p>",
                        "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>24</mn><msqrt><mn>2</mn></msqrt><mo>-</mo><mn>15</mn></mrow><mn>4</mn></mfrac></math></p>",
                        "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5</mn><msqrt><mn>2</mn></msqrt><mo>+</mo><mn>4</mn></mrow><mn>4</mn></mfrac></math></p>",
                        "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5</mn><msqrt><mn>2</mn></msqrt><mo>-</mo><mn>4</mn></mrow><mn>4</mn></mfrac></math></p>"
                    ],
                    options_hi: [
                        "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>24</mn><msqrt><mn>2</mn></msqrt><mo>+</mo><mn>15</mn></mrow><mn>4</mn></mfrac></math></p>",
                        "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>24</mn><msqrt><mn>2</mn></msqrt><mo>-</mo><mn>15</mn></mrow><mn>4</mn></mfrac></math></p>",
                        "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5</mn><msqrt><mn>2</mn></msqrt><mo>+</mo><mn>4</mn></mrow><mn>4</mn></mfrac></math></p>",
                        "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5</mn><msqrt><mn>2</mn></msqrt><mo>-</mo><mn>4</mn></mrow><mn>4</mn></mfrac></math></p>"
                    ],
                    solution_en: "<p>68.(d) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739263421321.png\" alt=\"rId61\" width=\"201\" height=\"183\"><br>AC = <math display=\"inline\"><msqrt><mo>(</mo><msqrt><mn>2</mn></msqrt><mo>-</mo><mn>1</mn><msup><mrow><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup><mo>+</mo><msup><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></msup></msqrt></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn><mo>-</mo><mn>2</mn><msqrt><mn>2</mn></msqrt><mo>+</mo><mn>1</mn></msqrt></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>4</mn><mo>-</mo><mn>2</mn><msqrt><mn>2</mn></msqrt></msqrt></math><br>sinAcosA + tanA = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msqrt><mn>2</mn></msqrt><mo>-</mo><mn>1</mn></mrow><msqrt><mn>4</mn><mo>-</mo><mn>2</mn><msqrt><mn>2</mn></msqrt></msqrt></mfrac></math> &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msqrt><mn>4</mn><mo>-</mo><mn>2</mn><msqrt><mn>2</mn></msqrt></msqrt></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>2</mn></msqrt></math> - 1&nbsp;<br>= <math display=\"inline\"><mfrac><mrow><msqrt><mn>2</mn></msqrt><mo>-</mo><mn>1</mn></mrow><mrow><mn>4</mn><mo>-</mo><mn>2</mn><msqrt><mn>2</mn></msqrt></mrow></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>2</mn></msqrt></math>- 1&nbsp;<br>= <math display=\"inline\"><mfrac><mrow><msqrt><mn>2</mn></msqrt><mo>-</mo><mn>1</mn></mrow><mrow><mn>2</mn><msqrt><mn>2</mn></msqrt><mo>(</mo><msqrt><mn>2</mn></msqrt><mo>-</mo><mn>1</mn><mo>)</mo></mrow></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>2</mn></msqrt></math> - 1 <br>= <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn><msqrt><mn>2</mn></msqrt></mrow></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>2</mn></msqrt></math> - 1 <br>= <math display=\"inline\"><mfrac><mrow><msqrt><mn>2</mn></msqrt></mrow><mrow><mn>4</mn></mrow></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>2</mn></msqrt></math> - 1 =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msqrt><mn>2</mn></msqrt><mo>+</mo><mn>4</mn><msqrt><mn>2</mn></msqrt><mo>-</mo><mn>4</mn></mrow><mn>4</mn></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5</mn><msqrt><mn>2</mn></msqrt><mo>-</mo><mn>4</mn></mrow><mn>4</mn></mfrac></math></p>",
                    solution_hi: "<p>68.(d) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739263421321.png\" alt=\"rId61\" width=\"201\" height=\"183\"><br>AC = <math display=\"inline\"><msqrt><mo>(</mo><msqrt><mn>2</mn></msqrt><mo>-</mo><mn>1</mn><msup><mrow><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup><mo>+</mo><msup><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></msup></msqrt></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn><mo>-</mo><mn>2</mn><msqrt><mn>2</mn></msqrt><mo>+</mo><mn>1</mn></msqrt></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>4</mn><mo>-</mo><mn>2</mn><msqrt><mn>2</mn></msqrt></msqrt></math><br>sinAcosA + tanA = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msqrt><mn>2</mn></msqrt><mo>-</mo><mn>1</mn></mrow><msqrt><mn>4</mn><mo>-</mo><mn>2</mn><msqrt><mn>2</mn></msqrt></msqrt></mfrac></math> &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msqrt><mn>4</mn><mo>-</mo><mn>2</mn><msqrt><mn>2</mn></msqrt></msqrt></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>2</mn></msqrt></math> - 1&nbsp;<br>= <math display=\"inline\"><mfrac><mrow><msqrt><mn>2</mn></msqrt><mo>-</mo><mn>1</mn></mrow><mrow><mn>4</mn><mo>-</mo><mn>2</mn><msqrt><mn>2</mn></msqrt></mrow></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>2</mn></msqrt></math>- 1&nbsp;<br>= <math display=\"inline\"><mfrac><mrow><msqrt><mn>2</mn></msqrt><mo>-</mo><mn>1</mn></mrow><mrow><mn>2</mn><msqrt><mn>2</mn></msqrt><mo>(</mo><msqrt><mn>2</mn></msqrt><mo>-</mo><mn>1</mn><mo>)</mo></mrow></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>2</mn></msqrt></math> - 1 <br>= <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn><msqrt><mn>2</mn></msqrt></mrow></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>2</mn></msqrt></math> - 1 <br>= <math display=\"inline\"><mfrac><mrow><msqrt><mn>2</mn></msqrt></mrow><mrow><mn>4</mn></mrow></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>2</mn></msqrt></math> - 1 =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msqrt><mn>2</mn></msqrt><mo>+</mo><mn>4</mn><msqrt><mn>2</mn></msqrt><mo>-</mo><mn>4</mn></mrow><mn>4</mn></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5</mn><msqrt><mn>2</mn></msqrt><mo>-</mo><mn>4</mn></mrow><mn>4</mn></mfrac></math><br><br></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "69",
                    section: "17",
                    question_en: "<p>69. A bank charges simple interest for ₹K at the rate of&nbsp;<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi mathvariant=\"normal\">r</mi><mfrac><mn>1</mn><mn>2</mn></mfrac></msup></math> for n&sup2; years. Another bank charges simple interest for ₹L at the rate of <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#160;</mo><msup><mi mathvariant=\"normal\">r</mi><mfrac><mn>3</mn><mrow><mn>2</mn><mo>&#160;</mo></mrow></mfrac></msup></math> for n&sup3; years and gives the same interest. The ratio between K and L is:</p>",
                    question_hi: "<p>69. एक बैंक ₹K पर n&sup2; वर्षों के लिए <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi mathvariant=\"normal\">r</mi><mfrac><mn>1</mn><mn>2</mn></mfrac></msup></math>&nbsp;की दर से साधारण ब्याज लगाता है। एक अन्य बैंक ₹L पर n<sup>3</sup> वर्षों के लिए <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#160;</mo><msup><mi mathvariant=\"normal\">r</mi><mfrac><mn>3</mn><mrow><mn>2</mn><mo>&#160;</mo></mrow></mfrac></msup></math>की दर से साधारण ब्याज लगाता है और समान ब्याज देता है। K और L के बीच का अनुपात क्या है?</p>",
                    options_en: [
                        "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi mathvariant=\"normal\">r</mi><mfrac><mn>1</mn><mn>2</mn></mfrac></msup></math>&nbsp;:&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi mathvariant=\"normal\">n</mi><mfrac><mn>1</mn><mn>2</mn></mfrac></msup></math></p>",
                        "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi mathvariant=\"normal\">r</mi></mfrac></math> : <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi mathvariant=\"normal\">n</mi></mfrac></math></p>",
                        "<p>r n : 1</p>",
                        "<p>r<sup>2</sup>&nbsp;: n<sup>2</sup></p>"
                    ],
                    options_hi: [
                        "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi mathvariant=\"normal\">r</mi><mfrac><mn>1</mn><mn>2</mn></mfrac></msup></math>&nbsp;:&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi mathvariant=\"normal\">n</mi><mfrac><mn>1</mn><mn>2</mn></mfrac></msup></math></p>",
                        "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi mathvariant=\"normal\">r</mi></mfrac></math> : <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi mathvariant=\"normal\">n</mi></mfrac></math></p>",
                        "<p>r n : 1</p>",
                        "<p>r<sup>2</sup>&nbsp;: n<sup>2</sup></p>"
                    ],
                    solution_en: "<p>69.(c)<br>SI = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>PRT</mi><mn>100</mn></mfrac></math><br>According to the question,<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi mathvariant=\"normal\">K</mi><mo>&#215;</mo><msup><mi mathvariant=\"normal\">r</mi><mfrac><mn>1</mn><mn>2</mn></mfrac></msup><mo>&#215;</mo><msup><mi mathvariant=\"normal\">n</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&#160;</mi></mrow><mn>100</mn></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi mathvariant=\"normal\">L</mi><mo>&#215;</mo><msup><mi mathvariant=\"normal\">r</mi><mstyle displaystyle=\"true\"><mfrac><mn>3</mn><mn>2</mn></mfrac></mstyle></msup><mo>&#215;</mo><msup><mi mathvariant=\"normal\">n</mi><mn>3</mn></msup></mrow><mn>100</mn></mfrac></math><br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">K</mi><mo>&#215;</mo><msup><mi mathvariant=\"normal\">r</mi><mfrac><mn>1</mn><mn>2</mn></mfrac></msup><mo>&#215;</mo><msup><mi mathvariant=\"normal\">n</mi><mn>2</mn></msup></math> = L &times;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi mathvariant=\"normal\">r</mi><mfrac><mn>3</mn><mn>2</mn></mfrac></msup></math>&times; n<sup>3</sup><br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"normal\">K</mi><mi mathvariant=\"normal\">L</mi></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi mathvariant=\"normal\">r</mi><mstyle displaystyle=\"true\"><mfrac><mn>3</mn><mn>2</mn></mfrac></mstyle></msup><mo>&#215;</mo><msup><mi mathvariant=\"normal\">n</mi><mn>3</mn></msup></mrow><mrow><msup><mi mathvariant=\"normal\">r</mi><mstyle displaystyle=\"true\"><mfrac><mn>1</mn><mn>2</mn></mfrac></mstyle></msup><mo>&#215;</mo><msup><mi mathvariant=\"normal\">n</mi><mn>2</mn></msup></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi mathvariant=\"normal\">r</mi><mo>&#215;</mo><msup><mi mathvariant=\"normal\">r</mi><mstyle displaystyle=\"true\"><mfrac><mn>1</mn><mn>2</mn></mfrac></mstyle></msup><mo>&#215;</mo><msup><mi mathvariant=\"normal\">n</mi><mn>3</mn></msup></mrow><mrow><msup><mi mathvariant=\"normal\">r</mi><mfrac><mn>1</mn><mn>2</mn></mfrac></msup><mo>&#215;</mo><msup><mi mathvariant=\"normal\">n</mi><mn>2</mn></msup></mrow></mfrac></math> =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>&#160;</mo><mi mathvariant=\"normal\">r</mi><mo>&#160;</mo><mi mathvariant=\"normal\">n</mi></mrow><mrow><mn>1</mn><mo>&#160;</mo></mrow></mfrac></math><br>Hence, K : L = r n : 1</p>",
                    solution_hi: "<p>69.(c)<br>साधारण ब्याज = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2350;&#2370;&#2354;&#2343;&#2344;</mi><mo>&#215;</mo><mi>&#2342;&#2352;</mi><mi mathvariant=\"normal\">&#160;</mi><mo>&#215;</mo><mi>&#2360;&#2350;&#2351;</mi><mi mathvariant=\"normal\">&#160;</mi></mrow><mn>100</mn></mfrac></math><br>प्रश्न के अनुसार,<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi mathvariant=\"normal\">K</mi><mo>&#215;</mo><msup><mi mathvariant=\"normal\">r</mi><mfrac><mn>1</mn><mn>2</mn></mfrac></msup><mo>&#215;</mo><msup><mi mathvariant=\"normal\">n</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&#160;</mi></mrow><mn>100</mn></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi mathvariant=\"normal\">L</mi><mo>&#215;</mo><msup><mi mathvariant=\"normal\">r</mi><mstyle displaystyle=\"true\"><mfrac><mn>3</mn><mn>2</mn></mfrac></mstyle></msup><mo>&#215;</mo><msup><mi mathvariant=\"normal\">n</mi><mn>3</mn></msup></mrow><mn>100</mn></mfrac></math><br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">K</mi><mo>&#215;</mo><msup><mi mathvariant=\"normal\">r</mi><mfrac><mn>1</mn><mn>2</mn></mfrac></msup><mo>&#215;</mo><msup><mi mathvariant=\"normal\">n</mi><mn>2</mn></msup></math> = L &times;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi mathvariant=\"normal\">r</mi><mfrac><mn>3</mn><mn>2</mn></mfrac></msup></math>&times; n<sup>3</sup><br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"normal\">K</mi><mi mathvariant=\"normal\">L</mi></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi mathvariant=\"normal\">r</mi><mstyle displaystyle=\"true\"><mfrac><mn>3</mn><mn>2</mn></mfrac></mstyle></msup><mo>&#215;</mo><msup><mi mathvariant=\"normal\">n</mi><mn>3</mn></msup></mrow><mrow><msup><mi mathvariant=\"normal\">r</mi><mstyle displaystyle=\"true\"><mfrac><mn>1</mn><mn>2</mn></mfrac></mstyle></msup><mo>&#215;</mo><msup><mi mathvariant=\"normal\">n</mi><mn>2</mn></msup></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi mathvariant=\"normal\">r</mi><mo>&#215;</mo><msup><mi mathvariant=\"normal\">r</mi><mstyle displaystyle=\"true\"><mfrac><mn>1</mn><mn>2</mn></mfrac></mstyle></msup><mo>&#215;</mo><msup><mi mathvariant=\"normal\">n</mi><mn>3</mn></msup></mrow><mrow><msup><mi mathvariant=\"normal\">r</mi><mfrac><mn>1</mn><mn>2</mn></mfrac></msup><mo>&#215;</mo><msup><mi mathvariant=\"normal\">n</mi><mn>2</mn></msup></mrow></mfrac></math> =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>&#160;</mo><mi mathvariant=\"normal\">r</mi><mo>&#160;</mo><mi mathvariant=\"normal\">n</mi></mrow><mrow><mn>1</mn><mo>&#160;</mo></mrow></mfrac></math><br>इसलिए, K : L = r n : 1</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "70",
                    section: "17",
                    question_en: "<p>70. Three numbers are in the ratio 5 : 7 : 9, and their LCM is 1260. What is the difference between the largest and the smallest number?</p>",
                    question_hi: "<p>70. तीन संख्याओं का अनुपात 5 : 7 : 9 है और उनका लघुत्तम समापवर्त्य (LCM) 1260 है। सबसे बड़ी और सबसे छोटी संख्या के बीच का अंतर कितना है?</p>",
                    options_en: [
                        "<p>24</p>",
                        "<p>20</p>",
                        "<p>16</p>",
                        "<p>32</p>"
                    ],
                    options_hi: [
                        "<p>24</p>",
                        "<p>20</p>",
                        "<p>16</p>",
                        "<p>32</p>"
                    ],
                    solution_en: "<p>70.(c) Let three numbers are 5x&nbsp;, 7x ,9x<br>According to question ,<br>( 5 &times; 7 &times; 9 )x&nbsp;= 1260<br><math display=\"inline\"><mo>&#8658;</mo></math> 315 x = 1260 &rArr; x = 4 <br>&there4; Three numbers are 20 , 28 , 36&nbsp;<br>Required difference = 36 - 20 = 16</p>",
                    solution_hi: "<p>70.(c) माना तीन संख्याएँ 5x&nbsp;, 7x ,9x हैं।<br>प्रश्न के अनुसार,<br>( 5 &times; 7 &times; 9 ) x&nbsp;= 1260<br><math display=\"inline\"><mo>&#8658;</mo></math> 315 x = 1260 &rArr; x = 4 <br>&there4; तीन संख्याएँ 20, 28, 36 हैं&nbsp;<br>आवश्यक अंतर = 36 - 20 = 16</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "71",
                    section: "17",
                    question_en: "<p>71. A class of 30 students appeared in a test. The average score of 12 students is 62, and that of the rest is 72. What is the average score of the class?</p>",
                    question_hi: "<p>71. 30 छात्रों वाली एक कक्षा के सभी छात्रों ने एक टेस्ट दिया। 12 छात्रों का औसत स्कोर 62 है और बाकी छात्रों का औसत स्कोर 72 है। कक्षा का औसत स्कोर ज्ञात करें।</p>",
                    options_en: [
                        "<p>66</p>",
                        "<p>68</p>",
                        "<p>67</p>",
                        "<p>69</p>"
                    ],
                    options_hi: [
                        "<p>66</p>",
                        "<p>68</p>",
                        "<p>67</p>",
                        "<p>69</p>"
                    ],
                    solution_en: "<p>71.(b) Total score of 12 students = 12 &times; 62 = 744<br>Total score of 18 students = 18 &times; 72 = 1296<br>the average score of the class = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1296</mn><mo>+</mo><mn>744</mn></mrow><mn>30</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2040</mn><mn>30</mn></mfrac></math> = 68</p>",
                    solution_hi: "<p>71.(b) 12 छात्रों का कुल स्कोर = 12 &times; 62 = 744<br>18 छात्रों का कुल स्कोर = 18 &times; 72 = 1296<br>कक्षा का औसत अंक = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1296</mn><mo>+</mo><mn>744</mn></mrow><mn>30</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2040</mn><mn>30</mn></mfrac></math> = 68</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "72",
                    section: "17",
                    question_en: "<p>72. How many numbers less than 1,000 are multiples of both 7 and 11 ?</p>",
                    question_hi: "<p>72. 1,000 से कम वाली कितनी संख्याएं 7 और 11 दोनों की गुणज हैं?</p>",
                    options_en: [
                        "<p>13</p>",
                        "<p>11</p>",
                        "<p>12</p>",
                        "<p>10</p>"
                    ],
                    options_hi: [
                        "<p>13</p>",
                        "<p>11</p>",
                        "<p>12</p>",
                        "<p>10</p>"
                    ],
                    solution_en: "<p>72.(c)<br>7 &times; 11 = 77<br>Numbers less than 1000 which are multiples of 77 = 77, 154, &hellip;&hellip;&hellip;&hellip;&hellip;.924<br>since , the given series are in AP. so, we have ;<br>77 + (n - 1)77 = 924<br>(n - 1)77 = 847<br>n - 1 = 11<br>n = 12</p>",
                    solution_hi: "<p>72.(c)<br>7 &times; 11 = 77<br>1000 से कम संख्याएँ जो 77 के गुणज हैं = 77, 154, &hellip;&hellip;&hellip;&hellip;.924<br>चूंकि, दी गई श्रृंखला A.P में है तो, हमारे पास है;<br>77 + (n - 1)77 = 924<br>(n - 1)77 = 847<br>n - 1 = 11<br>n = 12</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "73",
                    section: "17",
                    question_en: "<p>73. Study the below table and answer the question that follows.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739263421505.png\" alt=\"rId62\" width=\"320\" height=\"169\"> <br>If 300 calories are burned by jogging 6 km, then how many calories were burnt in the given week?</p>",
                    question_hi: "<p>73. निम्न तालिका का अध्ययन कीजिए और प्रश्न का उत्तर दीजिए।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739263421626.png\" alt=\"rId63\" width=\"235\" height=\"176\"> <br>यदि 6 km जॉगिंग करने से 300 कैलोरी बर्न होती है, तो दिए गए सप्ताह में कितनी कैलोरी बर्न हुई?</p>",
                    options_en: [
                        "<p>1600</p>",
                        "<p>2200</p>",
                        "<p>1800</p>",
                        "<p>2000</p>"
                    ],
                    options_hi: [
                        "<p>1600</p>",
                        "<p>2200</p>",
                        "<p>1800</p>",
                        "<p>2000</p>"
                    ],
                    solution_en: "<p>73.(d) Total distance of the week = 6 + 5.5 + 5.5 + 7 + 5 + 3.5 + 7.5 = 40<br>According to the question,<br>6 km burn calories = 300 <br>40 km burn calories = <math display=\"inline\"><mfrac><mrow><mn>300</mn></mrow><mrow><mn>6</mn></mrow></mfrac></math> &times; 40 = 2000</p>",
                    solution_hi: "<p>73.(d) पुरे सप्ताह में तय की गयी कुल दूरी = 6 + 5.5 + 5.5 + 7 + 5 + 3.5 + 7.5 = 40<br>प्रश्न के अनुसार,<br>6 किमी कैलोरी बर्न = 300 <br>40 किमी कैलोरी बर्न = <math display=\"inline\"><mfrac><mrow><mn>300</mn></mrow><mrow><mn>6</mn></mrow></mfrac></math> &times; 40 = 2000</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "74",
                    section: "17",
                    question_en: "<p>74. The radius of the base and height of a cone are 5 cm and 6 cm, respectively, whereas the radius of the base and height of a cylinder are 2.5 cm and 3 cm, respectively. The ratio of the volume of the cone to that of the cylinder is: (Where &pi; = <math display=\"inline\"><mfrac><mrow><mn>22</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math>)</p>",
                    question_hi: "<p>74. एक शंकु के आधार की त्रिज्या और ऊंचाई क्रमशः 5 cm और 6 cm है, जबकि एक बेलन के आधार की त्रिज्या और ऊंचाई क्रमशः 2.5 cm और 3 cm है। शंकु के आयतन और बेलन के आयतन का अनुपात ज्ञात करें। (जहाँ &pi; = <math display=\"inline\"><mfrac><mrow><mn>22</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math>)</p>",
                    options_en: [
                        "<p>8 : 5</p>",
                        "<p>9 : 4</p>",
                        "<p>8 : 3</p>",
                        "<p>3 : 5</p>"
                    ],
                    options_hi: [
                        "<p>8 : 5</p>",
                        "<p>9 : 4</p>",
                        "<p>8 : 3</p>",
                        "<p>3 : 5</p>"
                    ],
                    solution_en: "<p>74.(c)<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>Volume</mi><mi mathvariant=\"normal\">&#160;</mi><mi>of</mi><mi mathvariant=\"normal\">&#160;</mi><mi>cone</mi></mrow><mrow><mi>Volume</mi><mi mathvariant=\"normal\">&#160;</mi><mi>of</mi><mi mathvariant=\"normal\">&#160;</mi><mi>cylinder</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mstyle displaystyle=\"true\"><mfrac><mn>1</mn><mn>3</mn></mfrac></mstyle><msup><mi>&#960;r</mi><mn>2</mn></msup><mi mathvariant=\"normal\">h</mi></mrow><mrow><msup><mi>&#960;R</mi><mn>2</mn></msup><mi mathvariant=\"normal\">H</mi></mrow></mfrac></math>&nbsp;=&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mstyle displaystyle=\"true\"><mfrac><mn>1</mn><mn>3</mn></mfrac></mstyle><mo>&#215;</mo><mn>5</mn><mo>&#215;</mo><mn>5</mn><mo>&#215;</mo><mn>6</mn></mrow><mrow><mo>&#160;</mo><mn>2</mn><mo>.</mo><mn>5</mn><mo>&#215;</mo><mn>2</mn><mo>.</mo><mn>5</mn><mo>&#215;</mo><mn>3</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>8</mn><mn>3</mn></mfrac></math> = 8 : 3</p>",
                    solution_hi: "<p>74.(c)<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2358;&#2306;&#2325;&#2369;</mi><mi mathvariant=\"normal\">&#160;</mi><mi>&#2325;&#2366;</mi><mi mathvariant=\"normal\">&#160;</mi><mi>&#2310;&#2351;&#2340;&#2344;</mi></mrow><mrow><mi>&#2348;&#2375;&#2354;&#2344;</mi><mi mathvariant=\"normal\">&#160;</mi><mi>&#2325;&#2366;</mi><mi mathvariant=\"normal\">&#160;</mi><mi>&#2310;&#2351;&#2340;&#2344;</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mstyle displaystyle=\"true\"><mfrac><mn>1</mn><mn>3</mn></mfrac></mstyle><msup><mi>&#960;r</mi><mn>2</mn></msup><mi mathvariant=\"normal\">h</mi></mrow><mrow><msup><mi>&#960;R</mi><mn>2</mn></msup><mi mathvariant=\"normal\">H</mi></mrow></mfrac></math>&nbsp;=&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mstyle displaystyle=\"true\"><mfrac><mn>1</mn><mn>3</mn></mfrac></mstyle><mo>&#215;</mo><mn>5</mn><mo>&#215;</mo><mn>5</mn><mo>&#215;</mo><mn>6</mn></mrow><mrow><mo>&#160;</mo><mn>2</mn><mo>.</mo><mn>5</mn><mo>&#215;</mo><mn>2</mn><mo>.</mo><mn>5</mn><mo>&#215;</mo><mn>3</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>8</mn><mn>3</mn></mfrac></math> = 8 : 3</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "75",
                    section: "7",
                    question_en: "<p>75. The angle of elevation from a point on the ground, 120 m away from the foot of a tower is 60&deg;. Find the height of the tower.</p>",
                    question_hi: "<p>75. एक टावर के पाद से 120 m दूर जमीन पर एक बिंदु से उन्नयन कोण 60&deg; है। टावर की ऊंचाई ज्ञात कीजिये ।</p>",
                    options_en: [
                        "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>120</mn><msqrt><mn>3</mn></msqrt></mfrac></math>m</p>",
                        "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>100</mn><msqrt><mn>3</mn></msqrt></mfrac></math>m</p>",
                        "<p>100<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math>m</p>",
                        "<p>120<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math>m</p>"
                    ],
                    options_hi: [
                        "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>120</mn><msqrt><mn>3</mn></msqrt></mfrac></math>m</p>",
                        "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>100</mn><msqrt><mn>3</mn></msqrt></mfrac></math>m</p>",
                        "<p>100<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math>m</p>",
                        "<p>120<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math>m</p>"
                    ],
                    solution_en: "<p>75.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739263421736.png\" alt=\"rId64\" width=\"143\" height=\"158\"><br>tan 60&deg; = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"normal\">h</mi><mn>120</mn></mfrac></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo><msqrt><mn>3</mn></msqrt><mo>=</mo><mfrac><mi mathvariant=\"normal\">h</mi><mn>120</mn></mfrac></math><br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo><mi mathvariant=\"normal\">h</mi><mo>=</mo><mn>120</mn><msqrt><mn>3</mn></msqrt></math> m</p>",
                    solution_hi: "<p>75.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739263421736.png\" alt=\"rId64\" width=\"143\" height=\"158\"><br>tan 60&deg; = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2313;&#2330;&#2366;&#2312;</mi><mo>&#160;</mo><mo>&#160;</mo><mo>(</mo><mi mathvariant=\"normal\">h</mi><mo>)</mo></mrow><mn>120</mn></mfrac></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo><msqrt><mn>3</mn></msqrt><mo>=</mo><mfrac><mi mathvariant=\"normal\">h</mi><mn>120</mn></mfrac></math><br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo><mi mathvariant=\"normal\">h</mi><mo>=</mo><mn>120</mn><msqrt><mn>3</mn></msqrt></math> मीटर</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "76",
                    section: "7",
                    question_en: "<p>76. Select the option that expresses the given sentence in active voice.<br>Tall promises were being made by each candidate.</p>",
                    question_hi: "<p>76. Select the option that expresses the given sentence in active voice.<br>Tall promises were being made by each candidate.</p>",
                    options_en: [
                        "<p>Each candidate made tall promises.</p>",
                        "<p>Each candidate can make tall promises.</p>",
                        "<p>Each candidate has made tall promises.</p>",
                        "<p>Each candidate was making tall promises.</p>"
                    ],
                    options_hi: [
                        "<p>Each candidate made tall promises.</p>",
                        "<p>Each candidate can make tall promises.</p>",
                        "<p>Each candidate has made tall promises.</p>",
                        "<p>Each candidate was making tall promises.</p>"
                    ],
                    solution_en: "<p>76.(d) Each candidate was making tall promises.(Correct)<br>(a) Each candidate <span style=\"text-decoration: underline;\">made</span> tall promises.(Incorrect Verb)<br>(b) Each candidate<span style=\"text-decoration: underline;\"> can make</span> tall promises.(Incorrect Verb)<br>(c) Each candidate <span style=\"text-decoration: underline;\">has made</span> tall promises.(Incorrect Tense)</p>",
                    solution_hi: "<p>76.(d) Each candidate was making tall promises.(Correct)<br>(a) Each candidate <span style=\"text-decoration: underline;\">made</span> tall promises.(गलत Verb)<br>(b) Each candidate <span style=\"text-decoration: underline;\">can make</span> tall promises.(गलत Verb)<br>(c) Each candidate <span style=\"text-decoration: underline;\">has made</span> tall promises.(गलत Tense)</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "77",
                    section: "7",
                    question_en: "<p>77. Identify the segment in the sentence which contains grammatical error. Mark &lsquo;no error&rsquo; in case the sentence given is correct. <br>Many a man have come to India from Bangladesh to live here permanently .</p>",
                    question_hi: "<p>77. Identify the segment in the sentence which contains grammatical error. Mark &lsquo;no error&rsquo; in case the sentence given is correct. <br>Many a man have come to India from Bangladesh to live here permanently .</p>",
                    options_en: [
                        "<p>Many a man</p>",
                        "<p>have come to India from Bangladesh</p>",
                        "<p>to live here permanently</p>",
                        "<p>No error</p>"
                    ],
                    options_hi: [
                        "<p>Many a man</p>",
                        "<p>have come to India from Bangladesh</p>",
                        "<p>to live here permanently</p>",
                        "<p>No error</p>"
                    ],
                    solution_en: "<p>77.(b) have come to India from Bangladesh. <br>&ldquo;Many a&rdquo; is always followed by singular noun and singular verb . Have will be replaced by has.</p>",
                    solution_hi: "<p>77.(b) have come to India from Bangladesh. <br>&ldquo;Many a&rdquo; के बाद हमेशा singular noun और singular verb होता है। Have को has द्वारा प्रतिस्थापित किया जाएगा।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "78",
                    section: "7",
                    question_en: "<p>78. Select the most appropriate option that can substitute the underlined segment in the given sentence.<br>No sooner had she finished her breakfast <span style=\"text-decoration: underline;\">that she had received</span> a phone call from her boss, informing her that she needed to come to work immediately.</p>",
                    question_hi: "<p>78. Select the most appropriate option that can substitute the underlined segment in the given sentence.<br>No sooner had she finished her breakfast <span style=\"text-decoration: underline;\">that she had received</span> a phone call from her boss, informing her that she needed to come to work immediately.</p>",
                    options_en: [
                        "<p>than she receives</p>",
                        "<p>than she received</p>",
                        "<p>that she receives</p>",
                        "<p>that she received</p>"
                    ],
                    options_hi: [
                        "<p>than she receives</p>",
                        "<p>than she received</p>",
                        "<p>that she receives</p>",
                        "<p>that she received</p>"
                    ],
                    solution_en: "<p>78.(b) than she received<br>&lsquo;No sooner&hellip;than&rsquo; is a fixed conjunction pair. If there are two actions in the past, the 1st action is written in past perfect tense (had + V<sub>3</sub>) and the 2nd action is written in simple past tense(V<sub>2</sub>). Hence, &lsquo;than she received(V<sub>2</sub>)&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>78.(b) than she received<br>&lsquo;No sooner&hellip;than&rsquo; एक fixed conjunction pair है। यदि past में दो actions हुए हैं, तो 1st action को past perfect tense ​​(had + V<sub>3</sub>) में और 2nd action को simple past tense (V<sub>2</sub>) में लिखा जाता है। इसलिए, &lsquo;then she received(V<sub>2</sub>)&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "79",
                    section: "7",
                    question_en: "<p>79. Select the most appropriate synonym of the given word.<br>Reckon</p>",
                    question_hi: "<p>79. Select the most appropriate synonym of the given word.<br>Reckon</p>",
                    options_en: [
                        "<p>Merit</p>",
                        "<p>Imagine</p>",
                        "<p>Assess</p>",
                        "<p>Count</p>"
                    ],
                    options_hi: [
                        "<p>Merit</p>",
                        "<p>Imagine</p>",
                        "<p>Assess</p>",
                        "<p>Count</p>"
                    ],
                    solution_en: "<p>79.(d) <strong>Count-</strong> to calculate or consider something numerically.<br><strong>Reckon-</strong> to calculate, estimate, or consider something.<br><strong>Merit-</strong> the quality of being good or worthy.<br><strong>Imagine-</strong> to form a mental image or concept.<br><strong>Assess-</strong> to evaluate or judge the value of something.</p>",
                    solution_hi: "<p>79.(d) <strong>Count</strong> (गिनना) - to calculate or consider something numerically.<br><strong>Reckon</strong> (गणना करना) - to calculate, estimate, or consider something.<br><strong>Merit</strong> (योग्यता) - the quality of being good or worthy.<br><strong>Imagine</strong> (कल्पना करना) - to form a mental image or concept.<br><strong>Assess</strong> (आकलन करना) - to evaluate or judge the value of something.</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "80",
                    section: "7",
                    question_en: "<p>80. Select the option that expresses the given sentence in passive voice.<br>Who stole my tickets?</p>",
                    question_hi: "<p>80. elect the option that expresses the given sentence in passive voice.<br>Who stole my tickets?</p>",
                    options_en: [
                        "<p>My tickets was stolen by whom?</p>",
                        "<p>My tickets got stolen by who?</p>",
                        "<p>By whom were my tickets stolen?</p>",
                        "<p>My tickets were stolen by who?</p>"
                    ],
                    options_hi: [
                        "<p>My tickets was stolen by whom?</p>",
                        "<p>My tickets got stolen by who?</p>",
                        "<p>By whom were my tickets stolen?</p>",
                        "<p>My tickets were stolen by who?</p>"
                    ],
                    solution_en: "<p>80.(c) By whom were my tickets stolen? (Correct)<br>(a) My tickets was stolen <span style=\"text-decoration: underline;\">by whom</span>? (Incorrect sentence structure) <br>(b) My tickets got stolen <span style=\"text-decoration: underline;\">by who</span>? (Incorrect sentence structure)<br>(d) My tickets were stolen <span style=\"text-decoration: underline;\">by who</span>? (Incorrect sentence structure)</p>",
                    solution_hi: "<p>80.(c) By whom were my tickets stolen? (Correct)<br>(a) My tickets was stolen <span style=\"text-decoration: underline;\">by whom</span>? (गलत sentence structure) <br>(b) My tickets got stolen <span style=\"text-decoration: underline;\">by who</span>? (गलत sentence structure)<br>(d) My tickets were stolen <span style=\"text-decoration: underline;\">by who</span>? (गलत sentence structure)</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "81",
                    section: "7",
                    question_en: "<p>81. Underlined word in the sentence is not spelt correctly. Identify the correct spelling from the options given below. <br>A <span style=\"text-decoration: underline;\">congragation</span> of monks reached Shimla for the convention.</p>",
                    question_hi: "<p>81. Underlined word in the sentence is not spelt correctly. Identify the correct spelling from the options given below. <br>A <span style=\"text-decoration: underline;\">congragation</span> of monks reached Shimla for the convention.</p>",
                    options_en: [
                        "<p>Congretettion</p>",
                        "<p>Congregation</p>",
                        "<p>Cangregation</p>",
                        "<p>Congregetion</p>"
                    ],
                    options_hi: [
                        "<p>Congretettion</p>",
                        "<p>Congregation</p>",
                        "<p>Cangregation</p>",
                        "<p>Congregetion</p>"
                    ],
                    solution_en: "<p>81.(b) Congregation <br>&lsquo;Congregation&rsquo; is the correct spelling.</p>",
                    solution_hi: "<p>81.(b) Congregation <br>&lsquo;Congregation&rsquo; सही spelling है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "82",
                    section: "7",
                    question_en: "<p>82. The following sentence has been divided into four segments. One of them contains an error. Select the segment that contains the error from the given options.<br>I am understanding;/ you like her/ because she has /a generous nature.</p>",
                    question_hi: "<p>82. The following sentence has been divided into four segments. One of them contains an error. Select the segment that contains the error from the given options.<br>I am understanding;/ you like her/ because she has /a generous nature.</p>",
                    options_en: [
                        "<p>I am understanding;</p>",
                        "<p>because she has</p>",
                        "<p>a generous nature</p>",
                        "<p>you like her</p>"
                    ],
                    options_hi: [
                        "<p>I am understanding;</p>",
                        "<p>because she has</p>",
                        "<p>a generous nature</p>",
                        "<p>you like her</p>"
                    ],
                    solution_en: "<p>82.(a) I am understanding<br>&lsquo;Understand&rsquo; is a stative verb and stative verbs are generally not written in continuous tense. Rather, simple present tense will be used here. The first form of the verb is used in the simple present tense with &lsquo;I&rsquo;. Hence, &lsquo;I understand(V<sub>1</sub>)&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>82.(a) I am understanding<br>&lsquo;Understand&rsquo; एक stative verb है और stative verbs आमतौर पर continuous tense में नहीं लिखी जाती हैं। बल्कि, यहाँ simple present tense का प्रयोग किया जाएगा। verb की first form simple present tense में &lsquo;I&rsquo; के साथ प्रयोग किया जाता है। इसलिए, &lsquo;I understand(V<sub>1</sub>)&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "83",
                    section: "7",
                    question_en: "<p>83. Sentences of a paragraph are given below in jumbled order. Arrange the sentences in the correct order to form a meaningful and coherent paragraph.<br>A. More children go to school than at any time in the past. But more children today are out of school than any time in the past.<br>B. But it is not enough to blame the high birth rate for this state of affairs.<br>C. Indeed, it can be reasonably argued that continued mass illiteracy is not the result but the cause of the high birth rate.<br>D. There are more literate people in India today than ever before. But there are also more illiterates than ever before.</p>",
                    question_hi: "<p>83. Sentences of a paragraph are given below in jumbled order. Arrange the sentences in the correct order to form a meaningful and coherent paragraph.<br>A. More children go to school than at any time in the past. But more children today are out of school than any time in the past.<br>B. But it is not enough to blame the high birth rate for this state of affairs.<br>C. Indeed, it can be reasonably argued that continued mass illiteracy is not the result but the cause of the high birth rate.<br>D. There are more literate people in India today than ever before. But there are also more illiterates than ever before.</p>",
                    options_en: [
                        "<p>ABCD</p>",
                        "<p>CBAD</p>",
                        "<p>DABC</p>",
                        "<p>BADC</p>"
                    ],
                    options_hi: [
                        "<p>ABCD</p>",
                        "<p>CBAD</p>",
                        "<p>DABC</p>",
                        "<p>BADC</p>"
                    ],
                    solution_en: "<p>83.(c) <strong>DABC</strong><br>Sentence D will be the starting line as it introduces the main idea of the parajumble, i.e. &lsquo;more number of literate and illiterate people than ever before&rsquo;. And, Sentence A states that more children go to school while at the same time more children are out of school, which is the reason for more number of literates and illiterates. So, A will follow D. Further, Sentence B states that it is not enough to blame the high birth rate for this situation &amp; Sentence C states that continued mass illiteracy is not the result but the cause of the high birth rate. So, C will follow B. Going through the options, option &lsquo;c&rsquo; has the correct sequence.</p>",
                    solution_hi: "<p>83.(c) <strong>DABC</strong><br>Sentence D प्रारम्भिक line होगी क्योंकि यह parajumble के मुख्य विचार &lsquo;more number of literate and illiterate people than ever before&rsquo; को प्रस्तुत करता है। और, Sentence A में कहा गया है कि अधिक बच्चे स्कूल जाते हैं जबकि साथ ही अधिक बच्चे स्कूल से बाहर रहते हैं, जो साक्षर (literates) और निरक्षरों(illiterates) की अधिक संख्या का कारण है। इसलिए, D के बाद A आएगा। इसके अलावा, Sentence B बताता है कि इस स्थिति के लिए उच्च जन्म दर (high birth rate) को दोष देना पर्याप्त नहीं है और Sentence C में कहा गया है कि निरंतर व्यापक निरक्षरता (mass illiteracy) उच्च जन्म दर का परिणाम नहीं, बल्कि कारण है। इसलिए, B के बाद C आएगा। अतः options के माध्यम से जाने पर, option &lsquo;c&rsquo; में सही sequence है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "84",
                    section: "7",
                    question_en: "<p>84. Select the most appropriate option that can substitute the underlined words in the given sentence. <br>Ice <span style=\"text-decoration: underline;\">melts fastly</span> in the sun.</p>",
                    question_hi: "<p>84. Select the most appropriate option that can substitute the underlined words in the given sentence. <br>Ice <span style=\"text-decoration: underline;\">melts fastly</span> in the sun.</p>",
                    options_en: [
                        "<p>melt fast</p>",
                        "<p>melts fastest</p>",
                        "<p>melts fast</p>",
                        "<p>melt fastly</p>"
                    ],
                    options_hi: [
                        "<p>melt fast</p>",
                        "<p>melts fastest</p>",
                        "<p>melts fast</p>",
                        "<p>melt fastly</p>"
                    ],
                    solution_en: "<p>84.(c) melts fast<br>&lsquo;Fastly&rsquo; is an incorrect word. &lsquo;Fast&rsquo; is an adverb which modifies the verb &lsquo;melts&rsquo;. Hence, &lsquo;melts fast&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>84.(c) melts fast<br>&lsquo;Fastly&rsquo; एक गलत word है। &lsquo;Fast&rsquo; एक adverb है जो verb &lsquo;melts&rsquo; को modify करता है। इसलिए, &lsquo;melts fast&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "85",
                    section: "7",
                    question_en: "<p>85. The following sentence has been split into four segments. Identify the segment that contains a spelling error.<br>The precarious case of juvenile / deliquency against him / was dismissed by the / jury in the first hearing.</p>",
                    question_hi: "<p>85. The following sentence has been split into four segments. Identify the segment that contains a spelling error.<br>The precarious case of juvenile / deliquency against him / was dismissed by the / jury in the first hearing.</p>",
                    options_en: [
                        "<p>deliquency against him</p>",
                        "<p>jury in the first hearing.</p>",
                        "<p>The precarious case of juvenile</p>",
                        "<p>was dismissed by the</p>"
                    ],
                    options_hi: [
                        "<p>deliquency against him</p>",
                        "<p>jury in the first hearing.</p>",
                        "<p>The precarious case of juvenile</p>",
                        "<p>was dismissed by the</p>"
                    ],
                    solution_en: "<p>85.(a) deliquency against him<br>\'Delinquency\' is the correct spelling.</p>",
                    solution_hi: "<p>85.(a) deliquency against him<br>\'Delinquency\' सही spelling है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "86",
                    section: "7",
                    question_en: "<p>86. Choose the best alternative which best expresses the meaning of the idiom/ phrase given below as underlined in the sentence.<br>His plan was so complicated that <span style=\"text-decoration: underline;\"><strong>it floored</strong></span> his listeners.</p>",
                    question_hi: "<p>86. Choose the best alternative which best expresses the meaning of the idiom/ phrase given below as underlined in the sentence.<br>His plan was so complicated that <span style=\"text-decoration: underline;\"><strong>it floored</strong></span> his listeners.</p>",
                    options_en: [
                        "<p>encouraged</p>",
                        "<p>annoyed</p>",
                        "<p>entertained</p>",
                        "<p>puzzled</p>"
                    ],
                    options_hi: [
                        "<p>encouraged</p>",
                        "<p>annoyed</p>",
                        "<p>entertained</p>",
                        "<p>puzzled</p>"
                    ],
                    solution_en: "<p>86.(d) Puzzled</p>",
                    solution_hi: "<p>86.(d) Puzzled/ हैरान करना</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "87",
                    section: "7",
                    question_en: "<p>87. Based on the situation in the sentence, select the most appropriate idiom. <br>Although Greta Thunberg gave a moving speech at the UN about environmental damage, it could _______.</p>",
                    question_hi: "<p>87. Based on the situation in the sentence, select the most appropriate idiom. <br>Although Greta Thunberg gave a moving speech at the UN about environmental damage, it could _______.</p>",
                    options_en: [
                        "<p>break the ice</p>",
                        "<p>cut no ice</p>",
                        "<p>walk on thin ice</p>",
                        "<p>be the icing on the cake</p>"
                    ],
                    options_hi: [
                        "<p>break the ice</p>",
                        "<p>cut no ice</p>",
                        "<p>walk on thin ice</p>",
                        "<p>be the icing on the cake</p>"
                    ],
                    solution_en: "<p>87.(b) <strong>Cut no ice</strong> - have no influence or effect.</p>",
                    solution_hi: "<p>87.(b) <strong>Cut no ice </strong>- have no influence or effect./कोई प्रभाव या असर न होना।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "88",
                    section: "7",
                    question_en: "<p>88.&nbsp;Out of the four alternatives choose the one which can be substituted for the given words/sentences.<br>Having the same source of origin</p>",
                    question_hi: "<p>88.&nbsp;Out of the four alternatives choose the one which can be substituted for the given words/sentences.<br>Having the same source of origin</p>",
                    options_en: [
                        "<p>Collateral</p>",
                        "<p>Coagulate</p>",
                        "<p>Cognate</p>",
                        "<p>Collusion</p>"
                    ],
                    options_hi: [
                        "<p>Collateral</p>",
                        "<p>Coagulate</p>",
                        "<p>Cognate</p>",
                        "<p>Collusion</p>"
                    ],
                    solution_en: "<p>88.(c) cognate<br>(a) <strong>Collateral</strong> - connected with something else, but in addition to it and less important<br>(b) <strong>Coagulate</strong> - (used about a liquid) to become thick and partly solid.<br>(d) <strong>Collusion</strong> - A secret agreement to do something dishonest.</p>",
                    solution_hi: "<p>88.(c) cognate<br>(a) <strong>Collateral</strong> - किसी और चीज से जुड़ा हुआ, लेकिन इसके अतिरिक्त और कम महत्वपूर्ण<br>(b) <strong>Coagulate</strong> - (एक तरल के बारे में प्रयुक्त) गाढ़ा और आंशिक रूप से ठोस बनने के लिए<br>(d) <strong>Collusion</strong> - कुछ बेईमानी करने का गुप्त समझौता।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "89",
                    section: "7",
                    question_en: "<p>89. Given below are four jumbled sentences. Pick the option that gives their correct order.<br>P: The father wanted to wean his son from bad company.<br>Q: A few days later all bananas got spoiled.<br>R: A father was much worried about his son\'s bad company.<br>S: To give a lesson to the son, his father gave him a few good bananas with a rotten one.</p>",
                    question_hi: "<p>89. Given below are four jumbled sentences. Pick the option that gives their correct order.<br>P: The father wanted to wean his son from bad company.<br>Q: A few days later all bananas got spoiled.<br>R: A father was much worried about his son\'s bad company.<br>S: To give a lesson to the son, his father gave him a few good bananas with a rotten one.</p>",
                    options_en: [
                        "<p>RPSQ</p>",
                        "<p>SQPR</p>",
                        "<p>PSQR</p>",
                        "<p>QSPR</p>"
                    ],
                    options_hi: [
                        "<p>RPSQ</p>",
                        "<p>SQPR</p>",
                        "<p>PSQR</p>",
                        "<p>QSPR</p>"
                    ],
                    solution_en: "<p>89.(a) <strong>RPSQ</strong><br>Sentence R will be the starting line as it contains the main idea of the parajumble i.e. A father was much worried about his son\'s bad company. However, Sentence P states that the father wanted to wean his son from bad company. So, P will follow R. Further, Sentence S states that to give a lesson to the son, his father gave him a few good bananas with a rotten one &amp; Sentence Q states that a few days later all bananas got spoiled. So, Q will follow S. Going through the options, only option a has the correct sequence.</p>",
                    solution_hi: "<p>89.(a) <strong>RPSQ</strong> <br>वाक्य R शुरूआती पंक्ति (line) होगी क्योंकि इसमें parajumble का मुख्य विचार है - एक पिता अपने बेटे की बुरी संगत से बहुत चिंतित था। वाक्य P कहता है कि पिता अपने बेटे को बुरी संगत से छुड़ाना चाहता था। तो, R के बाद P का प्रयोग होगा । आगे, वाक्य S कहता है कि बेटे को सबक सिखाने के लिए, उसके पिता ने उसे कुछ अच्छे केले एक सड़े हुए केले के साथ दिए और वाक्य Q कहता है कि कुछ दिनों बाद सभी केले खराब हो गए। इसलिए, S के बाद Q आयेगा । विकल्पों के माध्यम से जाने पर, केवल विकल्प a में सही क्रम है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "90",
                    section: "7",
                    question_en: "<p>90. Select the option that can be used as a one - word substitute for the given group of words.<br>An extremely abnormal fear of confined space</p>",
                    question_hi: "<p>90. Select the option that can be used as a one - word substitute for the given group of words.<br>An extremely abnormal fear of confined space</p>",
                    options_en: [
                        "<p>Claustrophobia</p>",
                        "<p>Acrophobia</p>",
                        "<p>Glossophobia</p>",
                        "<p>Selenophobia</p>"
                    ],
                    options_hi: [
                        "<p>Claustrophobia</p>",
                        "<p>Acrophobia</p>",
                        "<p>Glossophobia</p>",
                        "<p>Selenophobia</p>"
                    ],
                    solution_en: "<p>90.(a) <strong>Claustrophobia-</strong> an extremely abnormal fear of confined space. <br><strong>Acrophobia-</strong> extreme fear of heights.<br><strong>Glossophobia-</strong> an intense and irrational fear of public speaking.<br><strong>Selenophobia-</strong> an intense fear of the moon or moonlight.</p>",
                    solution_hi: "<p>90.(a) <strong>Claustrophobia</strong> (बंद जगहों से डर)- an extremely abnormal fear of confined space. <br><strong>Acrophobia</strong> (ऊंचाई का डर)- extreme fear of heights.<br><strong>Glossophobia</strong> (सार्वजनिक रूप से बोलने का डर)- an intense and irrational fear of public speaking.<br><strong>Selenophobia</strong> (चंद्रमा या उसकी रोशनी का डर)- an intense fear of the moon or moonlight.</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "91",
                    section: "7",
                    question_en: "<p>91. Select the most appropriate synonym for the given word.<br>Immune</p>",
                    question_hi: "<p>91. Select the most appropriate synonym for the given word.<br>Immune</p>",
                    options_en: [
                        "<p>Resistant</p>",
                        "<p>Variable</p>",
                        "<p>Safe</p>",
                        "<p>Asleep</p>"
                    ],
                    options_hi: [
                        "<p>Resistant</p>",
                        "<p>Variable</p>",
                        "<p>Safe</p>",
                        "<p>Asleep</p>"
                    ],
                    solution_en: "<p>91.(a) <strong>Resistant-</strong> able to withstand or fight against something.<br><strong>Immune-</strong> protected or exempt from something, especially a disease.<br><strong>Variable-</strong> likely to change or vary.<br><strong>Safe-</strong> free from harm or danger.<br><strong>Asleep-</strong> in a state of sleep or inactivity.</p>",
                    solution_hi: "<p>91.(a) <strong>Resistant</strong> (सहनशील) - able to withstand or fight against something.<br><strong>Immune</strong> (संरक्षित) - protected or exempt from something, especially a disease.<br><strong>Variable</strong> (परिवर्तनशील) - likely to change or vary.<br><strong>Safe</strong> (सुरक्षित) - free from harm or danger.<br><strong>Asleep</strong> (अचेत) - in a state of sleep or inactivity.</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "92",
                    section: "7",
                    question_en: "<p>92. Select the most appropriate ANTONYM of the underlined word from the given options. <br>The new government allowed all the corrupt ministers to feel the <span style=\"text-decoration: underline;\">sufficiency</span> of money by banning the specific currency.</p>",
                    question_hi: "<p>92. Select the most appropriate ANTONYM of the underlined word from the given options. <br>The new government allowed all the corrupt ministers to feel the <span style=\"text-decoration: underline;\">sufficiency</span> of money by banning the specific currency.</p>",
                    options_en: [
                        "<p>Dearth</p>",
                        "<p>Plenty</p>",
                        "<p>Enough</p>",
                        "<p>Indeficiency</p>"
                    ],
                    options_hi: [
                        "<p>Dearth</p>",
                        "<p>Plenty</p>",
                        "<p>Enough</p>",
                        "<p>Indeficiency</p>"
                    ],
                    solution_en: "<p>92.(a) <strong>Dearth-</strong> a lack of something<br><strong>Plenty-</strong> more than enough<br><strong>Enough-</strong> as much of something as necessary<br><strong>Indeficiency-</strong> not deficient.<br><strong>Sufficiency-</strong> an adequate amount of something.</p>",
                    solution_hi: "<p>92.(a) <strong>Dearth</strong> (अभाव) - a lack of something<br><strong>Plenty</strong> (प्रचुर) - more than enough<br><strong>Enough</strong> (पर्याप्त) - as much of something as necessary<br><strong>Indeficiency</strong> (अपर्याप्तता) - not deficient.<br><strong>Sufficiency</strong> (प्रचुरता) - an adequate amount of something.</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "93",
                    section: "7",
                    question_en: "<p>93. Select the most appropriate option to fill in the blank.&nbsp;.<br>The fear that AIDS has broken________ in India is not unfounded.</p>",
                    question_hi: "<p>93. Select the most appropriate option to fill in the blank.&nbsp;.<br>The fear that AIDS has broken________ in India is not unfounded.</p>",
                    options_en: [
                        "<p>up</p>",
                        "<p>about</p>",
                        "<p>out</p>",
                        "<p>up</p>"
                    ],
                    options_hi: [
                        "<p>up</p>",
                        "<p>about</p>",
                        "<p>out</p>",
                        "<p>up</p>"
                    ],
                    solution_en: "<p>93.(c) <strong>out</strong><br>&lsquo;Broke out&rsquo; is a phrasal verb and it means to start suddenly.</p>",
                    solution_hi: "<p>93.(c) <strong>out</strong><br>&lsquo;Broke out&rsquo; एक phrasal verb है और इसका अर्थ है &ldquo;अचानक शुरू करना&rdquo;।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "94",
                    section: "7",
                    question_en: "<p>94. Select the most appropriate option to fill in the blank.<br>The two families have been engaged in a bitter_________ for the past two decades.</p>",
                    question_hi: "<p>94. Select the most appropriate option to fill in the blank.<br>The two families have been engaged in a bitter_________ for the past two decades.</p>",
                    options_en: [
                        "<p>feud</p>",
                        "<p>argument</p>",
                        "<p>quarrel</p>",
                        "<p>fight</p>"
                    ],
                    options_hi: [
                        "<p>feud</p>",
                        "<p>argument</p>",
                        "<p>quarrel</p>",
                        "<p>fight</p>"
                    ],
                    solution_en: "<p>94.(a) feud<br>Feud - an angry and bitter argument between two people or groups of people that continues, for a long period of time</p>",
                    solution_hi: "<p>94.(a) feud<br>Feud -दो लोगों या लोगों के समूहों के बीच एक क्रोधित और द्वेषपूर्ण बहस जो लंबे समय तक चलती रहती है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "95",
                    section: "7",
                    question_en: "<p>95. Select the most appropriate ANTONYM of the underlined word in the given sentence.<br>The speaker delivered a <span style=\"text-decoration: underline;\">profound</span> speech that left the audience deeply moved.</p>",
                    question_hi: "<p>95. Select the most appropriate ANTONYM of the underlined word in the given sentence.<br>The speaker delivered a <span style=\"text-decoration: underline;\">profound</span> speech that left the audience deeply moved.</p>",
                    options_en: [
                        "<p>Deep</p>",
                        "<p>Shallow</p>",
                        "<p>Inventive</p>",
                        "<p>Occult</p>"
                    ],
                    options_hi: [
                        "<p>Deep</p>",
                        "<p>Shallow</p>",
                        "<p>Inventive</p>",
                        "<p>Occult</p>"
                    ],
                    solution_en: "<p>95.(b) <strong>Shallow-</strong> lacking depth or seriousness.<br><strong>Profound-</strong> having great depth, meaning, or seriousness.<br><strong>Deep-</strong> extending far down or in, often used as a synonym for profound.<br><strong>Inventive-</strong> showing creativity or originality.<br><strong>Occult-</strong> relating to supernatural or mystical phenomena.</p>",
                    solution_hi: "<p>95.(b) <strong>Shallow</strong> (उथला/छिछला) - lacking depth or seriousness.<br><strong>Profound</strong> (गंभीर) - having great depth, meaning, or seriousness.<br><strong>Deep</strong> (रहस्यपूर्ण) - extending far down or in, often used as a synonym for profound.<br><strong>Inventive</strong> (आविष्कारशील) - showing creativity or originality.<br><strong>Occult</strong> (रहस्यमय) - relating to supernatural or mystical phenomena.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "96",
                    section: "7",
                    question_en: "<p>96. <strong>Cloze Test</strong><br>M. Ananda Kumar, scientist, Nature Conservation Foundation says that elephants are \"engineers of the ecosystem. He explains : they are seed (96) _______. Forests without (97) ______ have been observed to not have young (98) ________ at all. This is because (99) _________ species disperse seeds only through elephants.\" The animals are phenomenal at (100) ___________ themselves to new ecological conditions and negotiating a problem.<br>Select the most appropriate option to fill in the blank no. 96</p>",
                    question_hi: "<p>96.<strong> Cloze Test</strong><br>M. Ananda Kumar, scientist, Nature Conservation Foundation says that elephants are \"engineers of the ecosystem. He explains : they are seed (96) _______. Forests without (97) ______ have been observed to not have young (98) ________ at all. This is because (99)_________ species disperse seeds only through elephants.\" The animals are phenomenal at (100) ___________ themselves to new ecological conditions and negotiating a problem.<br>Select the most appropriate option to fill in the blank no 96.</p>",
                    options_en: [
                        "<p>Collectors</p>",
                        "<p>Makers</p>",
                        "<p>Eaters</p>",
                        "<p>Dispersers</p>"
                    ],
                    options_hi: [
                        "<p>Collectors</p>",
                        "<p>Makers</p>",
                        "<p>Eaters</p>",
                        "<p>Dispersers</p>"
                    ],
                    solution_en: "<p>96.(d) <strong>Dispersers</strong> -To separate and move in different directions; scatter<br><strong>Collectors-a</strong> person who collects things of a specified type, professionally or as a hobby.<br><strong>Makers</strong> - a person or thing that makes or produces something.<br><strong>Eaters-</strong> a person or animal who consumes food in a specified way or of a specified kind.</p>",
                    solution_hi: "<p>96.(d) <strong>Dispersers</strong> - अलग करना और अलग-अलग दिशाओं में जाना; बिखराव<br><strong>Collectors</strong> - एक व्यक्ति जो पेशेवर या शौक के रूप में एक विस्तृत प्रकार की चीजें एकत्र करता है।<br><strong>Makers</strong> - वह व्यक्ति या वस्तु जो कुछ बनाता या उत्पादन करता हो।<br><strong>Eaters</strong> - एक व्यक्ति या जानवर जो एक निर्दिष्ट तरीके से या एक निर्दिष्ट प्रकार से भोजन करता है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "97",
                    section: "7",
                    question_en: "<p>97.<strong> Cloze Test</strong><br>M. Ananda Kumar, scientist, Nature Conservation Foundation says that elephants are \"engineers of the ecosystem. He explains : they are seed (96) _______. Forests without (97) ______ have been observed to not have young (98) ________ at all. This is because (99)_________ species disperse seeds only through elephants.\" The animals are phenomenal at (100) ___________ themselves to new ecological conditions and negotiating a problem.<br>Select the most appropriate option to fill in the blank no 97.</p>",
                    question_hi: "<p>97.<strong> Cloze Test</strong><br>M. Ananda Kumar, scientist, Nature Conservation Foundation says that elephants are \"engineers of the ecosystem. He explains : they are seed (96) _______. Forests without (97) ______ have been observed to not have young (98) ________ at all. This is because (99)_________ species disperse seeds only through elephants.\" The animals are phenomenal at (100) ___________ themselves to new ecological conditions and negotiating a problem.<br>Select the most appropriate option to fill in the blank no 97.</p>",
                    options_en: [
                        "<p>engineers</p>",
                        "<p>elephants</p>",
                        "<p>conservation</p>",
                        "<p>seeds</p>"
                    ],
                    options_hi: [
                        "<p>engineers</p>",
                        "<p>elephants</p>",
                        "<p>conservation</p>",
                        "<p>seeds</p>"
                    ],
                    solution_en: "<p>97.(b) elephants. <br>Conservation-prevention of wasteful use of a resource.</p>",
                    solution_hi: "<p>97.(b) elephants. <br>Conservation (संरक्षण)-किसी संसाधन के व्यर्थ उपयोग की रोकथाम</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "98",
                    section: "7",
                    question_en: "<p>98. <strong>Cloze Test</strong><br>M. Ananda Kumar, scientist, Nature Conservation Foundation says that elephants are \"engineers of the ecosystem. He explains : they are seed (96) _______. Forests without (97) ______ have been observed to not have young (98) ________ at all. This is because (99) _________ species disperse seeds only through elephants.\" The animals are phenomenal at (100) ___________ themselves to new ecological conditions and negotiating a problem.<br>Select the most appropriate option to fill in the blank no 98.</p>",
                    question_hi: "<p>98. <strong>Cloze Test</strong><br>M. Ananda Kumar, scientist, Nature Conservation Foundation says that elephants are \"engineers of the ecosystem. He explains : they are seed (96) _______. Forests without (97) ______ have been observed to not have young (98) ________ at all. This is because (99) _________ species disperse seeds only through elephants.\" The animals are phenomenal at (100) ___________ themselves to new ecological conditions and negotiating a problem.<br>Select the most appropriate option to fill in the blank no 98.</p>",
                    options_en: [
                        "<p>Saplings</p>",
                        "<p>Species</p>",
                        "<p>Seeds</p>",
                        "<p>elephants</p>"
                    ],
                    options_hi: [
                        "<p>Saplings</p>",
                        "<p>Species</p>",
                        "<p>Seeds</p>",
                        "<p>elephants</p>"
                    ],
                    solution_en: "<p>98.(a) Saplings - A young tree, especially one with a slender trunk.</p>",
                    solution_hi: "<p>98.(a) Saplings - छोटा पौधा, विशेष रूप से एक पतले तने वाला।.</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "99",
                    section: "7",
                    question_en: "<p>99. <strong>Cloze Test</strong><br>M. Ananda Kumar, scientist, Nature Conservation Foundation says that elephants are \"engineers of the ecosystem. He explains : they are seed (96) _______. Forests without (97) ______ have been observed to not have young (98) ________ at all. This is because (99) _________ species disperse seeds only through elephants.\" The animals are phenomenal at (100) ___________ themselves to new ecological conditions and negotiating a problem.<br>Select the most appropriate option to fill in the blank no 99.</p>",
                    question_hi: "<p>99. <strong>Cloze Test</strong><br>M. Ananda Kumar, scientist, Nature Conservation Foundation says that elephants are \"engineers of the ecosystem. He explains : they are seed (96) _______. Forests without (97) ______ have been observed to not have young (98) ________ at all. This is because (99) _________ species disperse seeds only through elephants.\" The animals are phenomenal at (100) ___________ themselves to new ecological conditions and negotiating a problem.<br>Select the most appropriate option to fill in the blank no 99.</p>",
                    options_en: [
                        "<p>all</p>",
                        "<p>certain</p>",
                        "<p>no</p>",
                        "<p>every</p>"
                    ],
                    options_hi: [
                        "<p>all</p>",
                        "<p>certain</p>",
                        "<p>no</p>",
                        "<p>every</p>"
                    ],
                    solution_en: "<p>99.(b) certain - some.</p>",
                    solution_hi: "<p>99.(b) certain - कुछ</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "100",
                    section: "misc",
                    question_en: "<p>100. <strong>Cloze Test</strong><br>M. Ananda Kumar, scientist, Nature Conservation Foundation says that elephants are \"engineers of the ecosystem. He explains : they are seed (96) _______. Forests without (97) ______ have been observed to not have young (98) ________ at all. This is because (99) _________ species disperse seeds only through elephants.\" The animals are phenomenal at (100) ___________ themselves to new ecological conditions and negotiating a problem.<br>Select the most appropriate option to fill in the blank no 100.</p>",
                    question_hi: "<p>100. <strong>Cloze Test</strong><br>M. Ananda Kumar, scientist, Nature Conservation Foundation says that elephants are \"engineers of the ecosystem. He explains : they are seed (96) _______. Forests without (97) ______ have been observed to not have young (98) ________ at all. This is because (99) _________ species disperse seeds only through elephants.\" The animals are phenomenal at (100) ___________ themselves to new ecological conditions and negotiating a problem.<br>Select the most appropriate option to fill in the blank no 100.</p>",
                    options_en: [
                        "<p>explaining</p>",
                        "<p>adapting</p>",
                        "<p>expanding</p>",
                        "<p>structuring</p>"
                    ],
                    options_hi: [
                        "<p>explaining</p>",
                        "<p>adapting</p>",
                        "<p>expanding</p>",
                        "<p>structuring</p>"
                    ],
                    solution_en: "<p>100.(b) <strong>Adapting-become</strong> adjusted to new conditions.<br><strong>Explaining</strong> - make (an idea or situation) clear to someone by describing it in more detail or revealing relevant facts.<br><strong>Expanding</strong> - become or make larger or more extensive.<br><strong>Structuring</strong> - construct or arrange according to a plan; give a pattern or organization to.</p>",
                    solution_hi: "<p>100.(b) <strong>Adapting</strong> - नई परिस्थितियों के अनुकूल हो जाना ।<br><strong>Explaining</strong> - किसी बात को अधिक विस्तार से समझाना।<br><strong>Expanding</strong> - विस्तार करना<br><strong>Structuring</strong> - किसी योजना के अनुसार निर्माण या व्यवस्था करना।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'question-box d-flex align-items-center justify-content-center border rounded';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.question-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            const html = `
                <div class="card shadow-sm mb-4">
                    <div class="card-body">
                        <div class="question-text mb-4">
                            <div class="en">${question.question_en}</div>
                            <div class="hi" style="display:none">${question.question_hi}</div>
                        </div>
                        <div class="options">
                            ${['a', 'b', 'c', 'd'].map((opt, i) => `
                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="radio" name="q${index}" 
                                        id="opt-${index}-${opt}" value="${opt}" 
                                        ${answers[question.id] === opt ? 'checked' : ''}
                                        onclick="selectAnswer('${question.id}', '${opt}')">
                                    <label class="form-check-label w-100 py-2 px-3 rounded hover-bg-light" for="opt-${index}-${opt}">
                                        <div class="en">${question.options_en[i]}</div>
                                        <div class="hi" style="display:none">${question.options_hi[i]}</div>
                                    </label>
                                </div>
                            `).join('')}
                        </div>
                        ${submitted ? `
                            <div class="alert ${answers[question.id] === question.correct ? 'alert-success' : 'alert-danger'} mt-3">
                                ${answers[question.id] ? (answers[question.id] === question.correct ? 'Correct!' : 'Incorrect!') : 'Not attempted'}
                                <div class="mt-2">
                                    <strong>Solution:</strong>
                                    <div class="en">${question.solution_en}</div>
                                    <div class="hi" style="display:none">${question.solution_hi}</div>
                                </div>
                            </div>
                        ` : ''}
                    </div>
                </div>
            `;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // If in mobile view, close the menu
            if (window.innerWidth < 768) {
                toggleMenu(false);
            }
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                document.getElementById(`box-${index}`).classList.add('attempted');
            }
        }
        // Navigate to previous question
        function prevQuestion() {
            if (currentQuestion > 0) {
                showQuestion(currentQuestion - 1);
            }
        }
        // Navigate to next question
        function nextQuestion() {
            if (currentQuestion < questions.length - 1) {
                showQuestion(currentQuestion + 1);
            }
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) {
                showQuestion(section.start);
            }
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update button text
            document.getElementById('lang-btn').innerHTML = currentLang === 'en' ? 
                '<i class="fas fa-language"></i> हिंदी' : 
                '<i class="fas fa-language"></i> English';
        }
        // Toggle menu for mobile
        function toggleMenu(show) {
            const nav = document.getElementById('question-nav');
            const content = document.getElementById('question-content');
            if (show === undefined) {
                show = nav.style.right === '-100%' || nav.style.right === '';
            }
            if (show) {
                nav.style.right = '0';
                if (window.innerWidth >= 768) {
                    content.style.marginRight = '320px';
                }
            } else {
                nav.style.right = window.innerWidth < 768 ? '-100%' : '0';
                content.style.marginRight = window.innerWidth < 768 ? '0' : '320px';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
            document.getElementById('total-count').textContent = questions.length;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.remove('attempted');
                    box.classList.add('bg-secondary', 'text-white');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.remove('attempted');
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.remove('attempted');
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            resultsModal.show();
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check-circle me-1"></i> Submitted';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            resultsModal.hide();
            showQuestion(0);
        }
    </script>
</body>
</html>