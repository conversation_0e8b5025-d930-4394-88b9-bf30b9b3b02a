<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">15:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 15</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">15</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 15 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 0,
                end: 13
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 14,
                end: 14
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "17",
                    question_en: "<p>1. The current population of a city is 91260. It increases at the rate of 30 percent every year. What was the population of this city 2 years ago?</p>",
                    question_hi: "<p>1. एक शहर की वर्तमान जनसंख्या 91260 है। यह हर वर्ष 30 प्रतिशत की दर से बढ़ती है। 2 वर्ष पहले इस शहर की जनसंख्या कितनी थी?</p>",
                    options_en: ["<p>65000</p>", "<p>54000</p>", 
                                "<p>70200</p>", "<p>60000</p>"],
                    options_hi: ["<p>65000</p>", "<p>54000</p>",
                                "<p>70200</p>", "<p>60000</p>"],
                    solution_en: "<p>1.(b) population of city before 2 years = 91260 <math display=\"inline\"><mo>&#215;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>10</mn><mn>13</mn></mfrac><mo>&#215;</mo><mfrac><mn>10</mn><mn>13</mn></mfrac></math> = 54000</p>",
                    solution_hi: "<p>1.(b) 2 वर्ष पूर्व शहर की जनसंख्या = 91260 <math display=\"inline\"><mo>&#215;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>10</mn><mn>13</mn></mfrac><mo>&#215;</mo><mfrac><mn>10</mn><mn>13</mn></mfrac></math> = 54000<br><br></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "2",
                    section: "17",
                    question_en: "<p>2. If L is 3 times more than M, then by what percent is M less than L?</p>",
                    question_hi: "<p>2. यदि L, M से 3 गुना अधिक है, तो M, L से कितने प्रतिशत कम है?</p>",
                    options_en: ["<p>81 percent</p>", "<p>75 percent</p>", 
                                "<p>78 percent</p>", "<p>72 percent</p>"],
                    options_hi: ["<p>81 प्रतिशत</p>", "<p>75 प्रतिशत</p>",
                                "<p>78 प्रतिशत</p>", "<p>72 प्रतिशत</p>"],
                    solution_en: "<p>2.(b) L : M = 4 : 1 <br><math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> &times; 100 = 75 %</p>",
                    solution_hi: "<p>2.(b) L : M = 4 : 1 <br><math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> &times; 100 = 75 %</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "3",
                    section: "17",
                    question_en: "<p>3. If p percent of p is 729, then what is the value of p?</p>",
                    question_hi: "<p>3. यदि p का p प्रतिशत 729 है, तो p का मान कितना है?</p>",
                    options_en: ["<p>270</p>", "<p>170</p>", 
                                "<p>330</p>", "<p>230</p>"],
                    options_hi: ["<p>270</p>", "<p>170</p>",
                                "<p>330</p>", "<p>230</p>"],
                    solution_en: "<p>3.(a)<br>According to the question,<br>p &times; <math display=\"inline\"><mfrac><mrow><mi>p</mi></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 729<br><math display=\"inline\"><msup><mrow><mi>p</mi></mrow><mrow><mn>2</mn></mrow></msup></math> = 729 &times; 100<br>p = 270</p>",
                    solution_hi: "<p>3.(a)<br>प्रश्न के अनुसार,<br>p &times; <math display=\"inline\"><mfrac><mrow><mi>p</mi></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 729<br><math display=\"inline\"><msup><mrow><mi>p</mi></mrow><mrow><mn>2</mn></mrow></msup></math> = 729 &times; 100<br>p = 270</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "4",
                    section: "17",
                    question_en: "<p>4. Instead of increasing a number successively by 40 percent and 60 percent, it is decreased by 44 percent. Resultant value is what percentage of the required correct value?</p>",
                    question_hi: "<p>4. किसी संख्या में, क्रमिक रूप से 40 प्रतिशत और 60 प्रतिशत की वृद्धि के स्थान पर 44 प्रतिशत की कमी कर दी जाती है। प्राप्त किए गए परिणाम का मान, सही आभीष्ट मान का कितना प्रतिशत है?</p>",
                    options_en: ["<p>20 percent</p>", "<p>50 percent</p>", 
                                "<p>25 percent</p>", "<p>28 percent</p>"],
                    options_hi: ["<p>20 प्रतिशत</p>", "<p>50 प्रतिशत</p>",
                                "<p>25 प्रतिशत</p>", "<p>28 प्रतिशत</p>"],
                    solution_en: "<p>4.(c)<br>Let the number be 100 units<br>When successive increment of 40% and 60% then,<br>100 &times; <math display=\"inline\"><mfrac><mrow><mn>140</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>160</mn><mn>100</mn></mfrac></math>&nbsp;= 224 units<br>When decrement of 44% then,<br>100 &times; <math display=\"inline\"><mfrac><mrow><mn>56</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 56 units<br>Required % = <math display=\"inline\"><mfrac><mrow><mn>56</mn></mrow><mrow><mn>224</mn></mrow></mfrac></math> &times; 100 = 25%</p>",
                    solution_hi: "<p>4.(c)<br>मान कि संख्या 100 यूनिट है<br>जब क्रमिक वृद्धि 40% और 60% हो तो,<br>100 &times; <math display=\"inline\"><mfrac><mrow><mn>140</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>160</mn><mn>100</mn></mfrac></math> = 224 यूनिट<br>44% की कमी होने पर<br>100 &times; <math display=\"inline\"><mfrac><mrow><mn>56</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 56 यूनिट<br>आवश्यक % = <math display=\"inline\"><mfrac><mrow><mn>56</mn></mrow><mrow><mn>224</mn></mrow></mfrac></math> &times; 100 = 25%</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "5",
                    section: "17",
                    question_en: "<p>5. If price is decreased by 24 percent, then by how much percentage consumption should be increased, so that the expenditure remains the same?</p>",
                    question_hi: "<p>5. यदि मूल्य में 24 प्रतिशत की कमी की जाती है, तो उपभोग में कितने प्रतिशत की वृद्धि की जानी चाहिए, ताकि व्यय अपरिवर्तित रहे?</p>",
                    options_en: ["<p>25.5 percent</p>", "<p>26.5 percent</p>", 
                                "<p>28.5 percent</p>", "<p>31.5 percent</p>"],
                    options_hi: ["<p>25.5 प्रतिशत</p>", "<p>26.5 प्रतिशत</p>",
                                "<p>28.5 प्रतिशत</p>", "<p>31.5 प्रतिशत</p>"],
                    solution_en: "<p>5.(d) expenditure = price <math display=\"inline\"><mo>&#215;</mo></math> consumption <br>&nbsp; &nbsp; &nbsp;Ratio <math display=\"inline\"><mo>&#8594;</mo></math>&nbsp; &nbsp; initial&nbsp; &nbsp; :&nbsp; &nbsp; final <br>&nbsp; &nbsp; &nbsp; Price <math display=\"inline\"><mo>&#8594;</mo></math>&nbsp; &nbsp; &nbsp; 25&nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp;19<br>Consumption <math display=\"inline\"><mo>&#8594;</mo></math>19&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp; 25<br>______________________________________<br>Increase % in consumption = <math display=\"inline\"><mfrac><mrow><mn>6</mn></mrow><mrow><mn>19</mn></mrow></mfrac></math> &times; 100 = 31.5 percent</p>",
                    solution_hi: "<p>5.(d) व्यय = मूल्य <math display=\"inline\"><mo>&#215;</mo></math> खपत<br>अनुपात <math display=\"inline\"><mo>&#8594;</mo></math> आरंभिक&nbsp; :&nbsp; अंतिम<br>मूल्य <math display=\"inline\"><mo>&#8594;</mo></math>&nbsp; &nbsp; &nbsp; &nbsp;&nbsp; 25&nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp;19<br>खपत <math display=\"inline\"><mo>&#8594;</mo></math>&nbsp; &nbsp; &nbsp; &nbsp; 19&nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp; 25<br>______________________________________<br>खपत में % वृद्धि = <math display=\"inline\"><mfrac><mrow><mn>6</mn></mrow><mrow><mn>19</mn></mrow></mfrac></math> &times; 100 = 31.5 प्रतिशत</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "6",
                    section: "17",
                    question_en: "<p>6. The price of a bike is firstly decreased by 25 percent and then increased by 50 percent. What is the percentage increase in the price of bike?</p>",
                    question_hi: "<p>6. एक बाइक के मूल्य में पहले 25 प्रतिशत की कमी की गई और फिर 50 प्रतिशत की वृद्धि की गई। बाइक के मूल्य में कितने प्रतिशत की वृद्धि हुई?</p>",
                    options_en: ["<p>20 percent</p>", "<p>25 percent</p>", 
                                "<p>12.5 percent</p>", "<p>10 percent</p>"],
                    options_hi: ["<p>20 प्रतिशत</p>", "<p>25 प्रतिशत</p>",
                                "<p>12.5 प्रतिशत</p>", "<p>10 प्रतिशत</p>"],
                    solution_en: "<p>6.(c) % increase = - 25 + 50 - <math display=\"inline\"><mfrac><mrow><mn>50</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>25</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> <br>= 25 - 12.5 = 12.5%</p>",
                    solution_hi: "<p>6.(c) % वृद्धि = - 25 + 50 - <math display=\"inline\"><mfrac><mrow><mn>50</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>25</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> <br>= 25 - 12.5 = 12.5%</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "7",
                    section: "17",
                    question_en: "<p>7. When the price of an item is reduced by 40%, its sales increase by 60%. What was the net effect on sales?</p>",
                    question_hi: "<p>7. जब एक वस्तु का मूल्य 40% कम कर दिया जाता तो उसकी बिक्री में 60% की वृद्धि हो जाती है। बिक्री पर निवल प्रभाव क्या था?</p>",
                    options_en: ["<p>increase of 4%</p>", "<p>increase of 6%</p>", 
                                "<p>decrease of 7%</p>", "<p>decrease of 4%</p>"],
                    options_hi: ["<p>4% की वृद्धि</p>", "<p>6% की वृद्धि</p>",
                                "<p>7% की कमी</p>", "<p>4% की कमी</p>"],
                    solution_en: "<p>7.(d)<br>According to question,<br>Ratio -&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;initial&nbsp; &nbsp;:&nbsp; &nbsp; final<br>Price -&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;5&nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp; 3<br>sales -&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;5&nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp; 8<br>------------------------------------<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;25&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp; 24<br>Required % = <math display=\"inline\"><mfrac><mrow><mn>25</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>24</mn></mrow><mrow><mn>25</mn></mrow></mfrac><mo>&#215;</mo><mn>100</mn></math> = 4% decrease</p>",
                    solution_hi: "<p>7.(d)<br>प्रश्न के अनुसार,<br>अनुपात -&nbsp; &nbsp; &nbsp; &nbsp; प्रारंभिक&nbsp; &nbsp;:&nbsp; &nbsp; अंतिम<br>मूल्य. -&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;5&nbsp; &nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp; &nbsp; 3<br>बिक्री-&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 5&nbsp; &nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp; &nbsp; 8<br>-----------------------------------------<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 25&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp; &nbsp;24<br>आवश्यक % = <math display=\"inline\"><mfrac><mrow><mn>25</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>24</mn></mrow><mrow><mn>25</mn></mrow></mfrac></math> &times; 100 = 4% कमी</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "8",
                    section: "17",
                    question_en: "<p>8. Arun got 20 percent less marks than Varun. Raju got 30 percent less marks than Varun. If Raju got 140 marks then how many marks did Arun get?</p>",
                    question_hi: "<p>8. अरुण को वरुण से 20 प्रतिशत कम अंक प्राप्त हुए | राजू को वरुण से 30 प्रतिशत कम अंक प्राप्त हुए। यदि राजू को 140 अंक प्राप्त हुए तो अरुण को कितने अंक प्राप्त हुए ?</p>",
                    options_en: ["<p>200</p>", "<p>160</p>", 
                                "<p>180</p>", "<p>150</p>"],
                    options_hi: ["<p>200</p>", "<p>160</p>",
                                "<p>180</p>", "<p>150</p>"],
                    solution_en: "<p>8.(b) According to question,<br>Ratio <math display=\"inline\"><mo>&#8594;</mo></math> Arun&nbsp; :&nbsp; Varun&nbsp; :&nbsp; Raju<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;4&nbsp; &nbsp; :&nbsp; &nbsp; &nbsp; 5&nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp;5 <br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 10&nbsp; &nbsp;:&nbsp; &nbsp; &nbsp;10&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp;7<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; ______________________<br>Final ratio <math display=\"inline\"><mo>&#8594;</mo></math> 40 : 50 : 35 or 8 : 10 : 7<br><math display=\"inline\"><mo>&#8658;</mo></math> 7 units = 140 marks <br><math display=\"inline\"><mo>&#8658;</mo></math> 8 units = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>140</mn><mn>7</mn></mfrac></math>&times; 8 = 160 marks <br>Hence, Arun\'s marks will be 160.</p>",
                    solution_hi: "<p>8.(b) प्रश्न के अनुसार,<br>अनुपात <math display=\"inline\"><mo>&#8594;</mo></math>अरुण : &nbsp;वरुण&nbsp; &nbsp; : &nbsp;राजू<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;4&nbsp; &nbsp; :&nbsp; &nbsp; &nbsp; 5&nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp;5 <br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 10&nbsp; &nbsp;:&nbsp; &nbsp; &nbsp;10&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp;7<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; ______________________&nbsp; &nbsp; &nbsp; &nbsp;&nbsp;<br>अंतिम अनुपात <math display=\"inline\"><mo>&#8594;</mo></math> 40 : 50 : 35 या 8 : 10 : 7<br><math display=\"inline\"><mo>&#8658;</mo></math> 7 इकाई = 140 अंक <br><math display=\"inline\"><mo>&#8658;</mo></math> 8 इकाई = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>140</mn><mn>7</mn></mfrac></math> &times; 8 = 160 अंक<br>अतः, अरुण के अंक 160 होंगे।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "9",
                    section: "17",
                    question_en: "<p>9. Student Raju scored 30 marks more than student Raman and Raju\'s marks are equal to 60 percent of the sum of their marks. What are the marks obtained by Raju and Raman respectively?</p>",
                    question_hi: "<p>9. छात्र राजू ने छात्र रमन से 30 अंक अधिक प्राप्त किए और राजू के अंक उन दोनों के अंकों के योग के 60 प्रतिशत के बराबर है। राजू और रमन द्वारा प्राप्त अंक क्रमशः कितने है?</p>",
                    options_en: ["<p>50, 20</p>", "<p>90, 60</p>", 
                                "<p>60, 30</p>", "<p>100, 70</p>"],
                    options_hi: ["<p>50, 20</p>", "<p>90, 60</p>",
                                "<p>60, 30</p>", "<p>100, 70</p>"],
                    solution_en: "<p>9.(b) Let the score of Raman is <math display=\"inline\"><mi>x</mi></math> and score of Raju is x + 30<br>According to the question,<br><math display=\"inline\"><mo>&#8658;</mo></math> x + 30 = (x + 30 + x) &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>60</mn><mn>100</mn></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math> 10x + 300 = 12x + 180<br><math display=\"inline\"><mo>&#8658;</mo></math> 2x = 120<br><math display=\"inline\"><mo>&#8658;</mo></math> x = 60<br>So, the score of Raman and Raju will be 60 and 90 respectively,</p>",
                    solution_hi: "<p>9.(b) माना कि रमन का स्कोर <math display=\"inline\"><mi>x</mi></math> है और राजू का स्कोर x + 30 है<br>प्रश्न के अनुसार,<br><math display=\"inline\"><mo>&#8658;</mo></math> x + 30 = (x + 30 + x) &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>60</mn><mn>100</mn></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math> 10x + 300 = 12x + 180<br><math display=\"inline\"><mo>&#8658;</mo></math> 2x = 120<br><math display=\"inline\"><mo>&#8658;</mo></math> x = 60<br>तो, रमन और राजू का स्कोर क्रमशः 60 और 90 होगा,</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "10",
                    section: "17",
                    question_en: "<p>10. If 60 is subtracted from a number, then that number becomes 85 percent of itself. What is that number?</p>",
                    question_hi: "<p>10. यदि किसी संख्या में से 60 घटा दिए जाए तो वह संख्या स्वयं की 85 प्रतिशत हो जाती है। वह संख्या क्या है?</p>",
                    options_en: ["<p>500</p>", "<p>400</p>", 
                                "<p>200</p>", "<p>300</p>"],
                    options_hi: ["<p>500</p>", "<p>400</p>",
                                "<p>200</p>", "<p>300</p>"],
                    solution_en: "<p>10.(b) Let the number be <math display=\"inline\"><mi>x</mi></math><br>According to question,<br><math display=\"inline\"><mo>&#8658;</mo></math> x - 60 = x &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>85</mn><mn>100</mn></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math> 20x - 1200 = 17x<br><math display=\"inline\"><mo>&#8658;</mo></math> 3x = 1200<br><math display=\"inline\"><mo>&#8658;</mo></math> x = 400</p>",
                    solution_hi: "<p>10.(b) माना संख्या <math display=\"inline\"><mi>x</mi></math> है<br>प्रश्न के अनुसार,<br><math display=\"inline\"><mo>&#8658;</mo></math> x - 60 = x &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>85</mn><mn>100</mn></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math> 20x - 1200 = 17x<br><math display=\"inline\"><mo>&#8658;</mo></math> 3x = 1200<br><math display=\"inline\"><mo>&#8658;</mo></math> x = 400</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "11",
                    section: "17",
                    question_en: "<p>11. The radius of a circle is increased by 35 percent. What will be the percentage increase in its area?</p>",
                    question_hi: "<p>11. एक वृत्त की त्रिज्या में 35 प्रतिशत की वृद्धि की जाती है। इसके क्षेत्रफल में कितने प्रतिशत की वृद्धि होगी?</p>",
                    options_en: ["<p>92.5 percent</p>", "<p>70 percent</p>", 
                                "<p>82.25 percent</p>", "<p>75 percent</p>"],
                    options_hi: ["<p>75 प्रतिशत</p>", "<p>70 प्रतिशत</p>",
                                "<p>82.25 प्रतिशत</p>", "<p>92.5 प्रतिशत</p>"],
                    solution_en: "<p>11.(c)<br>Area of circle = <math display=\"inline\"><mi>&#960;</mi><mi>r</mi><mo>&#178;</mo></math><br>Ratio -&nbsp; &nbsp; &nbsp; &nbsp; Initial&nbsp; &nbsp;:&nbsp; &nbsp;Final <br>Radius -&nbsp; &nbsp; &nbsp; &nbsp; 20&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; 27<br>Area -&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;400&nbsp; &nbsp; :&nbsp; &nbsp;729<br>Required % = <math display=\"inline\"><mfrac><mrow><mn>729</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>400</mn></mrow><mrow><mn>400</mn></mrow></mfrac><mo>&#215;</mo><mn>100</mn></math> = 82.25%</p>",
                    solution_hi: "<p>11.(c)<br>वृत्त का क्षेत्रफल = <math display=\"inline\"><mi>&#960;</mi><mi>r</mi><mo>&#178;</mo></math><br>अनुपात -&nbsp; &nbsp; &nbsp; प्रारंभिक&nbsp; &nbsp;:&nbsp; &nbsp;अंतिम<br>त्रिज्या -&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;20&nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp; 27<br>क्षेत्रफल -&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 400&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp;729<br>आवश्यक % = <math display=\"inline\"><mfrac><mrow><mn>729</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>400</mn></mrow><mrow><mn>400</mn></mrow></mfrac><mo>&#215;</mo><mn>100</mn></math> = 82.25%</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "12",
                    section: "17",
                    question_en: "<p>12. The income of Sunny is 15 percent more than Sonu\'s income and the income of Sonu is 30 percent more than Monu\'s income. Sunny\'s income is how much percentage more than Monu\'s income?</p>",
                    question_hi: "<p>12. सनी की आय, सोनू की आय से 15 प्रतिशत अधिक है और सोनू की आय, मोनू की आय से 30 प्रतिशत अधिक है। सनी की आय, मोनू की आय से कितने प्रतिशत अधिक है?</p>",
                    options_en: ["<p>59.5 percent</p>", "<p>49.5 percent</p>", 
                                "<p>50 percent</p>", "<p>60 percent</p>"],
                    options_hi: ["<p>59.5 प्रतिशत</p>", "<p>49.5 प्रतिशत</p>",
                                "<p>50 प्रतिशत</p>", "<p>60 प्रतिशत</p>"],
                    solution_en: "<p>12.(b)<br>According to question,<br><strong>Ratio -&nbsp; &nbsp; &nbsp; Sunny&nbsp; &nbsp;:&nbsp; &nbsp;Sonu&nbsp; &nbsp;:&nbsp; &nbsp;Monu</strong><br><strong>Income </strong>-&nbsp; &nbsp; &nbsp;29.9&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp; 26&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp; 20<br>Required % = <math display=\"inline\"><mfrac><mrow><mn>29</mn><mo>.</mo><mn>9</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>20</mn></mrow><mrow><mn>20</mn></mrow></mfrac><mo>&#215;</mo><mn>100</mn></math> = 49.5%</p>",
                    solution_hi: "<p>12.(b)<br>प्रश्न के अनुसार,<br><strong>अनुपात&nbsp; &nbsp; &nbsp; &nbsp;- सनी&nbsp; &nbsp; :&nbsp; &nbsp; सोनू&nbsp; &nbsp; :&nbsp; &nbsp; मोनू</strong><br><strong>आय</strong> -&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 29.9&nbsp; &nbsp;:&nbsp; &nbsp; &nbsp; 26&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp; 20<br>आवश्यक % = <math display=\"inline\"><mfrac><mrow><mn>29</mn><mo>.</mo><mn>9</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>20</mn></mrow><mrow><mn>20</mn></mrow></mfrac><mo>&#215;</mo><mn>100</mn></math> = 49.5%</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "13",
                    section: "17",
                    question_en: "<p>13. A company\'s revenue increased by 10% in the first quarter and decreased by 5% in the second quarter. What is the net percentage increase or decrease in revenue over these two quarters ?</p>",
                    question_hi: "<p>13. एक कंपनी की आय में, पहली तिमाही में 10% की वृद्धि होती है और दूसरी तिमाही में 5% की कमी आती है। इन दो तिमाहियों में आय में निवल प्रतिशत वृद्धि या कमी कितनी है ?</p>",
                    options_en: ["<p>3.5 decrease</p>", "<p>3.5% increase</p>", 
                                "<p>4.5% increase</p>", "<p>4.5% decrease</p>"],
                    options_hi: ["<p>3.5 की कमी</p>", "<p>3.5% की वृद्धि</p>",
                                "<p>4.5% की वृद्धि</p>", "<p>4.5% की कमी</p>"],
                    solution_en: "<p>13.(c)<br>According to question,<br>Required % = 10 - 5 - <math display=\"inline\"><mfrac><mrow><mn>10</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>5</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 4.5% increase.</p>",
                    solution_hi: "<p>13.(c)<br>प्रश्न के अनुसार,<br>आवश्यक % = 10 - 5 - <math display=\"inline\"><mfrac><mrow><mn>10</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>5</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 4.5% की वृद्धि</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "14",
                    section: "17",
                    question_en: "<p>14. A student scores 60% in an exam and gets 24 marks less than another student who scored 80%. What is the maximum marks in the exam ?</p>",
                    question_hi: "<p>14. एक विद्यार्थी ने एक परीक्षा में 60% अंक प्राप्त किए और उसे 80% अंक प्राप्त करने वाले एक दूसरे विद्यार्थी से 24 अंक कम प्राप्त हुए। परीक्षा में अधिकतम अंक कितने हैं ?</p>",
                    options_en: ["<p>150</p>", "<p>140</p>", 
                                "<p>120</p>", "<p>130</p>"],
                    options_hi: ["<p>150</p>", "<p>140</p>",
                                "<p>120</p>", "<p>130</p>"],
                    solution_en: "<p>14.(c)<br>According to question,<br>Difference (60 - 80)% = 20 % = 24 marks<br>Then, Maximum marks in the exam = <math display=\"inline\"><mfrac><mrow><mn>24</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>100</mn></mrow><mrow><mn>20</mn></mrow></mfrac></math> = 120.</p>",
                    solution_hi: "<p>14.(c)<br>प्रश्न के अनुसार,<br>अंतर (60 - 80)% = 20 % = 24 अंक<br>फिर, परीक्षा में अधिकतम अंक = <math display=\"inline\"><mfrac><mrow><mn>24</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>100</mn></mrow><mrow><mn>20</mn></mrow></mfrac></math> = 120.</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "15",
                    section: "misc",
                    question_en: "<p>15. The price of an edible oil is increased by 45%. To maintain the budget, Rekha reduces the consumption of this oil by 20%. Find the increase in expenditure due to this edible oil.</p>",
                    question_hi: "<p>15. किसी खाद्य तेल की कीमत में 45% की वृद्धि हुई। बजट को संतुलित बनाए रखने के लिए, रेखा इस तेल की खपत में 20% की कमी कर देती है। इस खाद्य तेल के कारण व्यय में होने वाली वृद्धि ज्ञात कीजिए।</p>",
                    options_en: ["<p>16%</p>", "<p>17%</p>", 
                                "<p>15%</p>", "<p>18%</p>"],
                    options_hi: ["<p>16%</p>", "<p>17%</p>",
                                "<p>15%</p>", "<p>18%</p>"],
                    solution_en: "<p>15.(a) expenditure = price <math display=\"inline\"><mo>&#215;</mo></math> consumption <br>Ratio <math display=\"inline\"><mo>&#8594;</mo></math>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; initial&nbsp; &nbsp; :&nbsp; &nbsp; &nbsp;final <br>Price <math display=\"inline\"><mo>&#8594;</mo></math>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;20&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp; &nbsp;29<br>consumption<math display=\"inline\"><mo>&#8594;</mo></math> &nbsp;5&nbsp; &nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp; &nbsp; 4<br>&mdash;--------------------------------------------------<br>Expenditure <math display=\"inline\"><mo>&#8594;</mo></math> 100&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp;116<br>Percentage increase = <math display=\"inline\"><mfrac><mrow><mn>16</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> &times; 100 = 16%</p>",
                    solution_hi: "<p>15.(a) व्यय = मूल्य <math display=\"inline\"><mo>&#215;</mo></math> खपत <br>अनुपात<math display=\"inline\"><mo>&#8594;</mo></math>&nbsp; &nbsp;आरंभिक&nbsp; &nbsp; :&nbsp; &nbsp; अंतिम<br>मूल्य <math display=\"inline\"><mo>&#8594;</mo></math>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;20&nbsp; &nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp; 29<br>खपत <math display=\"inline\"><mo>&#8594;</mo></math>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;5&nbsp; &nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp; &nbsp;4<br>&mdash;--------------------------------------------------<br>व्यय <math display=\"inline\"><mo>&#8594;</mo></math>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;100&nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp;116<br>प्रतिशत वृद्धि = <math display=\"inline\"><mfrac><mrow><mn>16</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> &times; 100 = 16%</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>