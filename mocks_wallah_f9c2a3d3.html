<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">12:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 25</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">25</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 12 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["7"] = {
                name: "English",
                start: 0,
                end: 23
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="7">English</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 24,
                end: 24
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "7",
                    question_en: "1. The following sentence has been split into four segments. Identify the segment that contains a grammatical error.<br />Mathematics help/ in developing/ the logical mindset /of students.",
                    question_hi: "1. The following sentence has been split into four segments. Identify the segment that contains a grammatical error.<br />Mathematics help/ in developing/ the logical mindset /of students.",
                    options_en: [" Mathematics help  ", " of students  ", 
                                " in developing ", " the logical mindset"],
                    options_hi: [" Mathematics help  ", " of students  ",
                                " in developing ", " the logical mindset"],
                    solution_en: "1.(a) Mathematics help<br />Some nouns (mathematics, statics, physics) seem plural but they are actually singular forms. According to the “Subject-Verb Agreement Rule”, singular subject takes singular verb. Hence, ‘helps’ is the most appropriate answer.",
                    solution_hi: "1.(a) Mathematics help<br />कुछ nouns (mathematics, statics, physics) plural जैसे दिखते हैं लेकिन वास्तव में वे singular form होते हैं। “Subject-Verb Agreement Rule” के अनुसार,  singular subject के साथ हमेशा singular verb का प्रयोग होता है। अतः, ‘helps’ सबसे उपयुक्त उत्तर है।",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "7",
                    question_en: "2. Parts of the following sentence have been given as options. Select the option that contains an error.<br />The chief guest gave out prizes to the meritorious students of the school.",
                    question_hi: "2. Parts of the following sentence have been given as options. Select the option that contains an error.<br />The chief guest gave out prizes to the meritorious students of the school.",
                    options_en: [" gave out prizes", " of the school.", 
                                " The chief guest", " to the meritorious students"],
                    options_hi: [" gave out prizes", " of the school.",
                                " The chief guest", " to the meritorious students"],
                    solution_en: "2.(a) gave out prizes<br />‘Gave out’ must be replaced with ‘gave away’. The phrasal verb ‘give away’ means to give something away as a gift. The given sentence talks about giving away gifts. Hence, ‘gave away prizes’ is the most appropriate answer.",
                    solution_hi: "2.(a) gave out prizes<br />\'Gave out\' के स्थान पर ‘gave away\' का प्रयोग किया जाएगा। Phrasal verb \'give away\' का अर्थ है gift के रूप में कुछ देना। दिया गया sentence, gift देने के बारे में बात करता है। अतः, ‘gave away prizes’ सबसे उपयुक्त उत्तर है।",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "7",
                    question_en: "3. Select the option with the correct use of article(s).",
                    question_hi: "3. Select the option with the correct use of article(s).",
                    options_en: [" William Wordsworth was a romantic poet. He did things in an unusual manner. ", " William Wordsworth was a romantic poet. He did things in a unusual manner. ", 
                                " William Wordsworth was the romantic poet. He did things in a unusual manner. ", " William Wordsworth was romantic poet. He did things in an unusual manner."],
                    options_hi: [" William Wordsworth was a romantic poet. He did things in an unusual manner. ", " William Wordsworth was a romantic poet. He did things in a unusual manner. ",
                                " William Wordsworth was the romantic poet. He did things in a unusual manner. ", " William Wordsworth was romantic poet. He did things in an unusual manner."],
                    solution_en: "3.(a)  William Wordsworth was a romantic poet. He did things in an unusual manner. <br />Article ‘A’ is used before the words starting from a consonant sound - (a conversation, a story). Whereas, the article ‘An’ is used before the words starting from a vowel sound (an imminent danger, an apple). The word ‘romantic’ starts  with a consonant sound and the word ‘unusual’ begins with a vowel sound. Hence, the sentence given in option (a) correctly uses the articles.",
                    solution_hi: "3.(a)  William Wordsworth was a romantic poet. He did things in an unusual manner. <br />Consonant sound से प्रारंभ होने वाले words से पहले article ‘A’ का प्रयोग किया जाता है - (a conversation, a story)। जबकि vowel sound से प्रारंभ होने वाले words से पहले article ‘An’ का प्रयोग किया जाता है (an imminent danger, an apple)। Word \'Romantic\' एक consonant sound से प्रारंभ होता है तथा word \'unusual\' एक vowel sound से प्रारंभ होता है। अतः, option (a) में दिए गए sentence में article का सही प्रयोग है।",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "7",
                    question_en: "4. The following sentence has been divided into four segments. Identify the segment that contains a grammatical error.<br />The manager noticed / that Pragya / was first to reach / the office on Saturday.",
                    question_hi: "4. The following sentence has been divided into four segments. Identify the segment that contains a grammatical error.<br />The manager noticed / that Pragya / was first to reach / the office on Saturday.",
                    options_en: [" that Pragya ", " was first to reach ", 
                                " The manager noticed ", " the office on Saturday."],
                    options_hi: [" that Pragya ", " was first to reach ",
                                " The manager noticed ", " the office on Saturday."],
                    solution_en: "4.(b)  was first to reach<br />We generally use article ‘the’ with a superlative degree i.e. first. Hence, ‘was the first to reach’ is the most appropriate answer.",
                    solution_hi: "4.(b)  was first to reach<br />हम आमतौर पर किसी superlative degree (first) के साथ article ‘the’ का प्रयोग करते हैं। अतः, ‘was the first to reach’ सबसे उपयुक्त उत्तर है।",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "7",
                    question_en: "<p>5. <strong>Cloze Test:</strong><br>Water is (5)________ for life and for good health. Not only is it necessary for us to be able to meet our daily needs but safe drinking water can (6)_____ many water-related diseases. India has one of the largest number of cases of (7)_____ such as diarrhoea, dysentery, and cholera. Over 1,600 Indians, most of them children below the age of five, (8)________ die every day because of water-related diseases. These deaths can be prevented if people have (9)_______ to safe drinking water.<br>Select the most appropriate option to fill in the blank 5.</p>",
                    question_hi: "<p>5. <strong>Cloze Test:</strong><br>Water is (5)________ for life and for good health. Not only is it necessary for us to be able to meet our daily needs but safe drinking water can (6)_____ many water-related diseases. India has one of the largest number of cases of (7)_____ such as diarrhoea, dysentery, and cholera. Over 1,600 Indians, most of them children below the age of five, (8)________ die every day because of water-related diseases. These deaths can be prevented if people have (9)_______ to safe drinking water.<br>Select the most appropriate option to fill in the blank 5.</p>",
                    options_en: ["<p>insistent</p>", "<p>intrinsic</p>", 
                                "<p>essential</p>", "<p>primary</p>"],
                    options_hi: ["<p>insistent</p>", "<p>intrinsic</p>",
                                "<p>essential</p>", "<p>primary</p>"],
                    solution_en: "<p>5.(c) essential <br>&lsquo;Essential&rsquo; means absolutely necessary. The given passage states that water is essential for life and for good health. Hence, &lsquo;essential&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>5.(c) essential <br>\'Essential\' का अर्थ है बिल्कुल आवश्यक। दिए गए passage में कहा गया है कि जल जीवन तथा अच्छे स्वास्थ्य (good health) के लिए आवश्यक है। अतः, \'essential\' सबसे उपयुक्त उत्तर है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "7",
                    question_en: "<p>6. <strong>Cloze Test:</strong><br>Water is (5)________ for life and for good health. Not only is it necessary for us to be able to meet our daily needs but safe drinking water can (6)_____ many water-related diseases. India has one of the largest number of cases of (7)_____ such as diarrhoea, dysentery, and cholera. Over 1,600 Indians, most of them children below the age of five, (8)________ die every day because of water-related diseases. These deaths can be prevented if people have (9)_______ to safe drinking water.<br>Select the most appropriate option to fill in blank 6.</p>",
                    question_hi: "<p>6. <strong>Cloze Test</strong>:<br>Water is (5)________ for life and for good health. Not only is it necessary for us to be able to meet our daily needs but safe drinking water can (6)_____ many water-related diseases. India has one of the largest number of cases of (7)_____ such as diarrhoea, dysentery, and cholera. Over 1,600 Indians, most of them children below the age of five, (8)________ die every day because of water-related diseases. These deaths can be prevented if people have (9)_______ to safe drinking water.<br>Select the most appropriate option to fill in blank 6.</p>",
                    options_en: [" prevent", " forbid  ", 
                                " hamper ", " restrain"],
                    options_hi: [" prevent", " forbid  ",
                                " hamper ", " restrain"],
                    solution_en: "6.(a) prevent<br />‘Prevent’ means to keep something from happening. The given passage states that safe drinking water can prevent many water-related diseases. Hence, ‘prevent’ is the most appropriate answer.",
                    solution_hi: "6.(a) prevent<br />‘Prevent’ का अर्थ है किसी चीज़ को होने से रोकना। दिए गए passage में कहा गया है कि सुरक्षित पेयजल (safe drinking water) कई जल-संबंधी बीमारियों (water-related diseases) को रोक सकता है। अतः, ‘prevent’ सबसे उपयुक्त उत्तर है।",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "7",
                    question_en: "<p>7. <strong>Cloze Test:</strong><br>Water is (5)________ for life and for good health. Not only is it necessary for us to be able to meet our daily needs but safe drinking water can (6)_____ many water-related diseases. India has one of the largest number of cases of (7)_____ such as diarrhoea, dysentery, and cholera. Over 1,600 Indians, most of them children below the age of five, (8)________ die every day because of water-related diseases. These deaths can be prevented if people have (9)_______ to safe drinking water.<br>Select the most appropriate option to fill in blank 7.</p>",
                    question_hi: "<p>7<strong>. Cloze Test:</strong><br>Water is (5)________ for life and for good health. Not only is it necessary for us to be able to meet our daily needs but safe drinking water can (6)_____ many water-related diseases. India has one of the largest number of cases of (7)_____ such as diarrhoea, dysentery, and cholera. Over 1,600 Indians, most of them children below the age of five, (8)________ die every day because of water-related diseases. These deaths can be prevented if people have (9)_______ to safe drinking water.<br>Select the most appropriate option to fill in blank 7.</p>",
                    options_en: ["<p>diseases</p>", "<p>distress</p>", 
                                "<p>laments</p>", "<p>sufferings</p>"],
                    options_hi: ["<p>diseases</p>", "<p>distress</p>",
                                "<p>laments</p>", "<p>sufferings</p>"],
                    solution_en: "<p>7.(a) diseases<br>&lsquo;Disease&rsquo; means an illness of people. The given passage states that India has the one of the largest number of cases of diseases such as diarrhoea, dysentery, and cholera. Hence, &lsquo;diseases&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>7.(a) diseases<br>&lsquo;Disease&rsquo; का अर्थ है लोगों की बीमारी। दिए गए passage में कहा गया है कि भारत में दस्त (diarrhoea), पेचिश (dysentery) और हैजा (cholera) जैसी बीमारियों के सबसे ज़्यादा मामले हैं। अतः, &lsquo;diseases&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "7",
                    question_en: "<p>8.<strong> Cloze Test:</strong><br>Water is (5)________ for life and for good health. Not only is it necessary for us to be able to meet our daily needs but safe drinking water can (6)_____ many water-related diseases. India has one of the largest number of cases of (7)_____ such as diarrhoea, dysentery, and cholera. Over 1,600 Indians, most of them children below the age of five, (8)________ die every day because of water-related diseases. These deaths can be prevented if people have (9)_______ to safe drinking water.<br>Select the most appropriate option to fill in blank 9.</p>",
                    question_hi: "<p>8<strong>. Cloze Test:</strong><br>Water is (5)________ for life and for good health. Not only is it necessary for us to be able to meet our daily needs but safe drinking water can (6)_____ many water-related diseases. India has one of the largest number of cases of (7)_____ such as diarrhoea, dysentery, and cholera. Over 1,600 Indians, most of them children below the age of five, (8)________ die every day because of water-related diseases. These deaths can be prevented if people have (9)_______ to safe drinking water.<br>Select the most appropriate option to fill in blank 9.</p>",
                    options_en: [" distinctly ", " assuredly ", 
                                " reportedly ", " ostensibly"],
                    options_hi: [" distinctly ", " assuredly ",
                                " reportedly ", " ostensibly"],
                    solution_en: "8.(c) reportedly<br />‘Reportedly’ means according to a report. The given passage states that over 1,600 Indians, most of them children below the age of five, reportedly die every day because of water-related diseases. Hence, ‘reportedly’ is the most appropriate answer.",
                    solution_hi: "8.(c) reportedly<br />‘Reportedly’ का अर्थ है एक रिपोर्ट के अनुसार। दिए गए passage में कहा गया है कि 1,600 से अधिक भारतीय, जिनमें से अधिकांश पाँच वर्ष से कम आयु के बच्चे हैं, प्रतिदिन जल-संबंधी बीमारियों (water-related diseases) के कारण मरते हैं। अतः ‘reportedly’ सबसे उपयुक्त उत्तर है।",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "7",
                    question_en: "<p>9. <strong>Cloze Test:</strong><br>Water is (5)________ for life and for good health. Not only is it necessary for us to be able to meet our daily needs but safe drinking water can (6)_____ many water-related diseases. India has one of the largest number of cases of (7)_____ such as diarrhoea, dysentery, and cholera. Over 1,600 Indians, most of them children below the age of five, (8)________ die every day because of water-related diseases. These deaths can be prevented if people have (9)_______ to safe drinking water.<br>Select the most appropriate option to fill in blank 9.</p>",
                    question_hi: "<p>9. <strong>Cloze Test:</strong><br>Water is (5)________ for life and for good health. Not only is it necessary for us to be able to meet our daily needs but safe drinking water can (6)_____ many water-related diseases. India has one of the largest number of cases of (7)_____ such as diarrhoea, dysentery, and cholera. Over 1,600 Indians, most of them children below the age of five, (8)________ die every day because of water-related diseases. These deaths can be prevented if people have (9)_______ to safe drinking water.<br>Select the most appropriate option to fill in blank 9.</p>",
                    options_en: [" accession ", " entrance ", 
                                " providence ", " access"],
                    options_hi: [" accession ", " entrance ",
                                " providence ", " access"],
                    solution_en: "9.(d)  access<br />‘Access’ means the opportunity to use something. The given passage states that these deaths can be prevented if people have access to safe drinking water. Hence, ‘access’ is the most appropriate answer.",
                    solution_hi: "9.(d)  access<br />‘Access’ का अर्थ है किसी चीज़ का उपयोग करने का अवसर (opportunity)। दिए गए Passage में कहा गया है कि अगर लोगों को सुरक्षित पेयजल (safe drinking water) उपलब्ध हो तो इन मृत्युओं (deaths) को रोका जा सकता है। अतः, ‘access’ सबसे उपयुक्त उत्तर है।",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "7",
                    question_en: "10. Select the most appropriate meaning of the given idiom.<br />Going through a sticky patch",
                    question_hi: "10. Select the most appropriate meaning of the given idiom.<br />Going through a sticky patch",
                    options_en: [" To experience a bad period in life ", " To walk on a muddy road ", 
                                " To feel sad ", " Try to paste something unsuccessfully"],
                    options_hi: [" To experience a bad period in life ", " To walk on a muddy road ",
                                " To feel sad ", " Try to paste something unsuccessfully"],
                    solution_en: "<p>10.(a) <strong>Going through a sticky patch -</strong> to experience a bad period in life. <br>E.g.- My friend is going through a sticky patch at work, but I&rsquo;m sure things will get better soon.</p>",
                    solution_hi: "<p>10.(a) <strong>Going through a sticky patch -</strong> to experience a bad period in life./जीवन में एक बुरे दौर का अनुभव करना। <br>E.g.- My friend is going through a sticky patch at work, but I&rsquo;m sure things will get better soon.</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "7",
                    question_en: "<p>11. Select the most appropriate option that can substitute the underlined segment in the following sentence.<br>This serum contains ingredients <span style=\"text-decoration: underline;\">enabling the hair to grow.</span></p>",
                    question_hi: "<p>11. Select the most appropriate option that can substitute the underlined segment in the following sentence.<br>This serum contains ingredients <span style=\"text-decoration: underline;\">enabling the hair to grow.</span></p>",
                    options_en: ["<p>for growth hair</p>", "<p>the enable grow hair</p>", 
                                "<p>for growing many hair</p>", "<p>that enable the hair to grow</p>"],
                    options_hi: ["<p>for growth hair</p>", "<p>the enable grow hair</p>",
                                "<p>for growing many hair</p>", "<p>that enable the hair to grow</p>"],
                    solution_en: "<p>11.(d) that enable the hair to grow<br>\'That\' is a relative pronoun used to introduce a relative clause, which provides additional information about a noun. Here, the clause provides additional information about the ingredients. Hence, &lsquo;that enable the hair to grow&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>11.(d) that enable the hair to grow<br>\'That\' एक relative pronoun है जिसका प्रयोग एक relative clause को introduce करने के लिए किया जाता है, जो noun के बारे में अतिरिक्त जानकारी प्रदान करता है। यहाँ clause, अवयवों (ingredients) के बारे में अतिरिक्त जानकारी प्रदान करता है। अतः, &lsquo;that enable the hair to grow&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "7",
                    question_en: "<p>12. Select the most appropriate ANTONYM of the given word.<br>Massive</p>",
                    question_hi: "<p>12. Select the most appropriate ANTONYM of the given word.<br>Massive</p>",
                    options_en: ["<p>Tall</p>", "<p>Heavy</p>", 
                                "<p>Tiny</p>", "<p>Formidable</p>"],
                    options_hi: ["<p>Tall</p>", "<p>Heavy</p>",
                                "<p>Tiny</p>", "<p>Formidable</p>"],
                    solution_en: "<p>12.(c) <strong>Tiny</strong>- extremely small.<br><strong>Massive-</strong> very large in size, amount, or number.<br><strong>Heavy-</strong> having a significant weight.<br><strong>Formidable-</strong> difficult and needing a lot of effort or thought.</p>",
                    solution_hi: "<p>12.(c) <strong>Tiny</strong> (अति-सूक्ष्म) - extremely small.<br><strong>Massive</strong> (विशाल ) - very large in size, amount, or number.<br><strong>Heavy</strong> (भारी) - having a significant weight.<br><strong>Formidable</strong> (दुर्जेय) - difficult and needing a lot of effort or thought.</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "7",
                    question_en: "<p>13. Select the most appropriate ANTONYM of the word given in brackets to fill in the blank.<br>You can&rsquo;t stop the _________ (outflow) of current.</p>",
                    question_hi: "<p>13. Select the most appropriate ANTONYM of the word given in brackets to fill in the blank.<br>You can&rsquo;t stop the _________ (outflow) of current.</p>",
                    options_en: ["<p>torrent</p>", "<p>influx</p>", 
                                "<p>income</p>", "<p>deluge</p>"],
                    options_hi: ["<p>torrent</p>", "<p>influx</p>",
                                "<p>income</p>", "<p>deluge</p>"],
                    solution_en: "<p>13.(b) <strong>Influx-</strong> the fact of a large number of people or things arriving at the same time.<br><strong>Outflow-</strong> a movement away from a place.<br><strong>Torrent-</strong> a large amount of water that is moving quickly.<br><strong>Income-</strong> a company\'s profit in a particular period of time.<br><strong>Deluge-</strong> a very large amount of rain or water.</p>",
                    solution_hi: "<p>13.(b) <strong>Influx</strong> (अंतर्प्रवाह) - the fact of a large number of people or things arriving at the same time.<br><strong>Outflow</strong> (बहिर्प्रवाह) - a movement away from a place.<br><strong>Torrent</strong> (धारा) - a large amount of water that is moving quickly.<br><strong>Income</strong> (आय) - a company\'s profit in a particular period of time.<br><strong>Deluge</strong> (जल-प्रलय) - a very large amount of rain or water.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "7",
                    question_en: "14. Select the most appropriate ANTONYM of the given word.<br />Impulsive",
                    question_hi: "14. Select the most appropriate ANTONYM of the given word.<br />Impulsive",
                    options_en: [" Passive", " Hasty ", 
                                " Aggressive", " Cautious"],
                    options_hi: [" Passive", " Hasty ",
                                " Aggressive", " Cautious"],
                    solution_en: "<p>14.(d) <strong>Cautious-</strong> taking great care to avoid possible danger or problems.<br><strong>Impulsive-</strong> likely to act suddenly and without thinking. <br><strong>Passive-</strong> showing no reaction, feeling or interest; not active.<br><strong>Hasty-</strong> acting with excessive speed.<br><strong>Aggressive-</strong> ready to attack or confront.</p>",
                    solution_hi: "<p>14.(d) <strong>Cautious</strong> (सावधान) - taking great care to avoid possible danger or problems.<br><strong>Impulsive</strong> (आवेगशील) - likely to act suddenly and without thinking. <br><strong>Passive</strong> (निष्क्रिय) - showing no reaction, feeling or interest; not active.<br><strong>Hasty</strong> (जल्दबाज़ी) - acting with excessive speed.<br><strong>Aggressive</strong> (आक्रामक) - ready to attack or confront.</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "7",
                    question_en: "<p>15. Select the most appropriate option that can substitute the underlined word in the given sentence.<br>She argued against the unnecessary <span style=\"text-decoration: underline;\">split</span> in the committee.</p>",
                    question_hi: "<p>15. Select the most appropriate option that can substitute the underlined word in the given sentence.<br>She argued against the unnecessary <span style=\"text-decoration: underline;\">split</span> in the committee.</p>",
                    options_en: ["<p>combination</p>", "<p>mix</p>", 
                                "<p>division</p>", "<p>blend</p>"],
                    options_hi: ["<p>combination</p>", "<p>mix</p>",
                                "<p>division</p>", "<p>blend</p>"],
                    solution_en: "<p>15.(c) <strong>Split</strong> - division.</p>",
                    solution_hi: "<p>15.(c) <strong>Split</strong> (विभाजित करना) - division.</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "16",
                    section: "7",
                    question_en: "<p>16. Select the most appropriate option that can substitute the underlined segment in the following sentence.<br>Had you been to Dubai, <span style=\"text-decoration: underline;\">I would also go</span> with you.</p>",
                    question_hi: "<p>16. Select the most appropriate option that can substitute the underlined segment in the following sentence.<br>Had you been to Dubai,<span style=\"text-decoration: underline;\"> I would also go</span> with you.</p>",
                    options_en: ["<p>I shall be gone</p>", "<p>I will have went</p>", 
                                "<p>I would have gone</p>", "<p>I would has also gone</p>"],
                    options_hi: ["<p>I shall be gone</p>", "<p>I will have went</p>",
                                "<p>I would have gone</p>", "<p>I would has also gone</p>"],
                    solution_en: "<p>16.(c) I would have gone<br>The given sentence is an example of a third conditional sentence and the correct grammatical structure for it is &ldquo;had + Subject + been &hellip;&hellip;.. would have + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>V</mi><mn>3</mn></msub></math>&rdquo;. Hence, &lsquo;I would have gone(<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>V</mi><mn>3</mn></msub></math>)&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>16.(c) I would have gone<br>दिया गया sentence, third conditional sentence का एक example है तथा &ldquo;Had + subject + been &hellip;&hellip;.. would have + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>V</mi><mn>3</mn></msub></math>&rdquo; इसके लिए सही grammatical structure है। अतः, &lsquo;I would have gone(<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>V</mi><mn>3</mn></msub></math>)&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "17",
                    section: "7",
                    question_en: "17. Select the most appropriate synonym of the given word.<br />Evasive",
                    question_hi: "17. Select the most appropriate synonym of the given word.<br />Evasive",
                    options_en: [" Cohesive ", " Ambiguous  ", 
                                " Abrasive ", " Accessible<br /> "],
                    options_hi: [" Cohesive ", " Ambiguous  ",
                                " Abrasive ", " Accessible"],
                    solution_en: "<p>17.(b) <strong>Ambiguous </strong>- having multiple possible meanings or interpretations.<br><strong>Evasive</strong> - answering questions in a way that is not clear or direct.<br><strong>Cohesive </strong>- united and working together effectively.<br><strong>Abrasive </strong>- rude and unfriendly.<br><strong>Accessible</strong> - that can be easily reached or understood.</p>",
                    solution_hi: "<p>17.(b) <strong>Ambiguous</strong> (अस्पष्ट) - having multiple possible meanings or interpretations.<br><strong>Evasive</strong> (अस्पष्ट) - answering questions in a way that is not clear or direct.<br><strong>Cohesive </strong>(एकजुट) - united and working together effectively.<br><strong>Abrasive</strong> (सख्त) - rude and unfriendly.<br><strong>Accessible</strong> (उपलब्ध) - that can be easily reached or understood.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "18",
                    section: "7",
                    question_en: "<p>18. Select the most appropriate ANTONYM of the word &lsquo;Commence&rsquo; from the given sentence.<br>Let&rsquo;s put an end to this discussion about voodoo and instead talk about something amusing.</p>",
                    question_hi: "<p>18. Select the most appropriate ANTONYM of the word &lsquo;Commence&rsquo; from the given sentence.<br>Let&rsquo;s put an end to this discussion about voodoo and instead talk about something amusing.</p>",
                    options_en: ["<p>discussion</p>", "<p>amusing</p>", 
                                "<p>end</p>", "<p>voodoo</p>"],
                    options_hi: ["<p>discussion</p>", "<p>amusing</p>",
                                "<p>end</p>", "<p>voodoo</p>"],
                    solution_en: "<p>18.(c) <strong>End</strong><br><strong>Commence-</strong> to start or begin.<br><strong>Discussion-</strong> a conversation or debate about a specific topic.<br><strong>Amusing-</strong> something entertaining and funny.<br><strong>Voodoo</strong>- magic words that are intended to bring bad luck to someone.</p>",
                    solution_hi: "<p>18.(c) <strong>End</strong><br><strong>Commence</strong> (प्रारंभ) - to start or begin<br><strong>Discussion</strong> (चर्चा) - a conversation or debate about a specific topic.<br><strong>Amusing</strong> (मनोरंजक) - something entertaining and funny.<br><strong>Voodoo </strong>(जादू करना) - magic words that are intended to bring bad luck to someone.</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "19",
                    section: "7",
                    question_en: "19. Identify the incorrectly spelt word and select its correct spelling.<br />There are a number of artists currently creating unique, collectble teapots.",
                    question_hi: "19. Identify the incorrectly spelt word and select its correct spelling.<br />There are a number of artists currently creating unique, collectble teapots.",
                    options_en: [" collectable ", " currintly ", 
                                " collectibile ", " currantly"],
                    options_hi: [" collectable ", " currintly ",
                                " collectibile ", " currantly"],
                    solution_en: "19.(a) collectble<br />‘Collectible’ is the correct spelling.",
                    solution_hi: "19.(a) collectble<br />‘Collectible’ सही spelling है। ",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "20",
                    section: "7",
                    question_en: "20. Select the most appropriate option to fill in the blank.<br />Tom is trying to ___________ as soon as possible.",
                    question_hi: "20. Select the most appropriate option to fill in the blank.<br />Tom is trying to ___________ as soon as possible.",
                    options_en: [" get a job", " learn a job ", 
                                " receive a job ", " experience a job"],
                    options_hi: [" get a job", " learn a job ",
                                " receive a job ", " experience a job"],
                    solution_en: "20.(a) get a job<br />The given sentence states that Tom is trying to get a job as soon as possible. Hence, ‘get a job’ is the most appropriate answer.",
                    solution_hi: "20.(a) get a job<br />‘Get’ का अर्थ है कुछ प्राप्त करना। दिए गए sentence में कहा गया है कि टॉम जल्द से जल्द नौकरी पाने की कोशिश कर रहा है। अतः, ‘get a job’ सबसे उपयुक्त उत्तर है।",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "21",
                    section: "7",
                    question_en: "<p>21. Select the word segment from the options that can substitute the bracketed word segment meaningfully.<br>Telangana artists (are know for the amazing bronze castings).</p>",
                    question_hi: "<p>21. Select the word segment from the options that can substitute the bracketed word segment meaningfully.<br>Telangana artists (are know for the amazing bronze castings).</p>",
                    options_en: ["<p>Have been knew to the amazing bronze castings</p>", "<p>Are known for their amazing bronze castings</p>", 
                                "<p>Is known to the amazing bronze castings</p>", "<p>Have to know the amazing bronze castings</p>"],
                    options_hi: ["<p>Have been knew to the amazing bronze castings</p>", "<p>Are known for their amazing bronze castings</p>",
                                "<p>Is known to the amazing bronze castings</p>", "<p>Have to know the amazing bronze castings</p>"],
                    solution_en: "<p>21.(b) are known for their amazing bronze castings<br>The given sentence is a passive voice of simple present tense and the subject &lsquo;Telangana artists&rsquo; is plural. &lsquo;Plural Subject + are + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>V</mi><mn>3</mn></msub></math>&rsquo; is the correct grammatical structure for it. Hence, &lsquo;are known for the amazing bronze castings&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>21.(b) are known for their amazing bronze castings<br>दिया गया sentence, simple present tense का passive voice है तथा subject &lsquo;Telangana artists&rsquo; plural है। इसके लिए &lsquo;Plural Subject + are + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>V</mi><mn>3</mn></msub></math>&rsquo; सही grammatical structure है। अतः, &lsquo;are known for the amazing bronze castings&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "22",
                    section: "7",
                    question_en: "<p>22. Select the most appropriate ANTONYM of the underlined word in the given sentence.<br>His <span style=\"text-decoration: underline;\">feeble</span> opponent was no match to him and ultimately he won the challenge.</p>",
                    question_hi: "<p>22. Select the most appropriate ANTONYM of the underlined word in the given sentence.<br>His <span style=\"text-decoration: underline;\">feeble</span> opponent was no match to him and ultimately he won the challenge.</p>",
                    options_en: ["<p>Major</p>", "<p>Strong</p>", 
                                "<p>Mystic</p>", "<p>Intelligent</p>"],
                    options_hi: ["<p>Major</p>", "<p>Strong</p>",
                                "<p>Mystic</p>", "<p>Intelligent</p>"],
                    solution_en: "<p>22.(b) <strong>Strong</strong><br><strong>Feeble-</strong> lacking strength, energy, or power.<br><strong>Major-</strong> large or important.<br><strong>Mystic</strong>- someone who attempts to be united with God through prayer.<br><strong>Intelligent-</strong> having or showing the ability to understand, learn and think.</p>",
                    solution_hi: "<p>22.(b) <strong>Strong</strong><br><strong>Feeble</strong> (कमज़ोर) - lacking strength, energy, or power.<br><strong>Major</strong> (प्रमुख) - large or important.<br><strong>Mystic</strong> (रहस्यवादी) - someone who attempts to be united with God through prayer.<br><strong>Intelligent</strong> (बुद्धिमान) - having or showing the ability to understand, learn and think.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "23",
                    section: "7",
                    question_en: "23. Select the INCORRECTLY spelt word.",
                    question_hi: "23. Select the INCORRECTLY spelt word.",
                    options_en: [" Warranty ", " Committee  ", 
                                " Dilemma", " Neice"],
                    options_hi: [" Warranty ", " Committee  ",
                                " Dilemma", " Neice"],
                    solution_en: "23.(d) Neice<br />‘Niece’ is the correct spelling.",
                    solution_hi: "23.(d) Neice<br />‘Niece’ सही spelling है। ",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "24",
                    section: "7",
                    question_en: "<p>24. Select the most appropriate meaning of the given idiom.<br>Costing an arm and a leg</p>",
                    question_hi: "<p>24. Select the most appropriate meaning of the given idiom.<br>Costing an arm and a leg</p>",
                    options_en: ["<p>Very entertaining and jovial</p>", "<p>Very costly and luxurious</p>", 
                                "<p>Very attractive and cheap</p>", "<p>Very rough and tough</p>"],
                    options_hi: ["<p>Very entertaining and jovial</p>", "<p>Very costly and luxurious</p>",
                                "<p>Very attractive and cheap</p>", "<p>Very rough and tough</p>"],
                    solution_en: "<p>24.(b) <strong>Costing an arm and a leg</strong> - very costly and luxurious <br>E.g.- The vacation to Europe was amazing, but it cost an arm and a leg.</p>",
                    solution_hi: "<p>24.(b)<strong> Costing an arm and a leg -</strong> very costly and luxurious./अत्यधिक कीमती एवं विलासितापूर्ण।<br>E.g.- The vacation to Europe was amazing, but it cost an arm and a leg.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "25",
                    section: "misc",
                    question_en: "<p>25. Select the most appropriate option that can substitute the underlined segment in the given sentence. If there is no need to substitute it, select &lsquo;No substitution required&rsquo;.<br>It has been found that the trees <span style=\"text-decoration: underline;\">are being destroyed</span> by a moth.</p>",
                    question_hi: "<p>25. Select the most appropriate option that can substitute the underlined segment in the given sentence. If there is no need to substitute it, select &lsquo;No substitution required&rsquo;.<br>It has been found that the trees <span style=\"text-decoration: underline;\">are being destroyed</span> by a moth.</p>",
                    options_en: ["<p>is being destroyed</p>", "<p>have been destroying</p>", 
                                "<p>No substitution required</p>", "<p>have destroyed</p>"],
                    options_hi: ["<p>is being destroyed</p>", "<p>have been destroying</p>",
                                "<p>No substitution required</p>", "<p>have destroyed</p>"],
                    solution_en: "<p>25.(c) No substitution required<br>The given sentence is grammatically correct.</p>",
                    solution_hi: "<p>25.(c) No substitution required <br>दिया गया sentence, grammatically सही है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>