<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">25:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 40</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">40</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 25 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["18"] = {
                name: "General Awareness",
                start: 0,
                end: 38
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="18">General Awareness</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 39,
                end: 39
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "18",
                    question_en: "<p>1. _______ was the only Indian dance from present in Michael Jacksdon&rsquo;s 1991 music video for the hit single &lsquo;Black or White&rsquo;.</p>",
                    question_hi: "<p>1. माइकल जैकसन के 1991 के संगीत वीडियो में हिट सिंगल \'ब्लैक ऑर व्हाइट\' के लिए _______ मौजूद एकमात्र भारतीय नृत्य था।</p>",
                    options_en: ["<p>Kathakali</p>", "<p>Kathak</p>", 
                                "<p>Bharatanatyam</p>", "<p>Odissi</p>"],
                    options_hi: ["<p>कथकली</p>", "<p>कत्थक</p>",
                                "<p>भरतनाट्यम</p>", "<p>ओडिसी</p>"],
                    solution_en: "<p>1.(d) Odissi was the only Indian dance form present in Michael Jackson\'s 1991 hit single Black or White. Yamuna Sangarasivam, the woman who performed the Odissi dance piece with MJ in Black or White.</p>",
                    solution_hi: "<p>1.(d) ओडिसी माइकल जैक्सन के 1991 के हिट सिंगल ब्लैक या व्हाइट में मौजूद एकमात्र भारतीय नृत्य रूप था। यमुना संगाराशिवम, वह महिला जिसने काले या सफेद रंग में MJ के साथ ओडिसी नृत्य प्रस्तुत किया।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "2",
                    section: "18",
                    question_en: "<p>2. In July 2017, India&rsquo;s first solar powered train was launched at a railway station in ______.</p>",
                    question_hi: "<p>2. जुलाई 2017 में, भारत की पहली सौर ऊर्जा संचालित ट्रेन को ________ में एक रेलवे स्टेशन पर लॉन्च किया गया था।</p>",
                    options_en: ["<p>Bengaluru</p>", "<p>Mumbai</p>", 
                                "<p>Delhi</p>", "<p>Pune</p>"],
                    options_hi: ["<p>बेंगलुरु</p>", "<p>मुंबई</p>",
                                "<p>दिल्ली</p>", "<p>पुणे</p>"],
                    solution_en: "<p>2.(c) In July 2017, India&rsquo;s first solar powered train was launched at a railway station in Delhi. Indian Railways on July 14 launched the first solar-powered DEMU (diesel electrical multiple unit) train from the Safdarjung railway station in Delhi. The train will run from Sarai Rohilla in Delhi to Farukh Nagar in Haryana.</p>",
                    solution_hi: "<p>2.(c) जुलाई 2017 में, भारत की पहली सौर ऊर्जा संचालित ट्रेन दिल्ली के एक रेलवे स्टेशन पर शुरू की गई थी। भारतीय रेलवे ने 14 जुलाई को दिल्ली के सफदरजंग रेलवे स्टेशन से पहली सौर ऊर्जा से चलने वाली DEMU (डीजल इलेक्ट्रिकल मल्टीपल यूनिट) ट्रेन लॉन्च की। यह ट्रेन दिल्ली के सराय रोहिल्ला से हरियाणा के फारुख नगर तक चलेगी।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "3",
                    section: "18",
                    question_en: "<p>3. Which of the following endemic species is NOT found in the Western Ghats?</p>",
                    question_hi: "<p>3. निम्नलिखित में से कौन सी स्थानिक प्रजाति पश्चिमी घाट में नहीं पाई जाती है?</p>",
                    options_en: ["<p>Nilgiri Langur</p>", "<p>Hispid Hare</p>", 
                                "<p>Brown Palm Civet</p>", "<p>Nilgiri Tahr</p>"],
                    options_hi: ["<p>नीलगिरि लंगूर</p>", "<p>हिस्पिड खरगोश</p>",
                                "<p>ब्राउन पाम सिवेट</p>", "<p>नीलगिरि तहर</p>"],
                    solution_en: "<p>3.(b) Species found in the Western Ghats are Nilgiri Tahr,Indian Gaur,Lion-Tailed Macaque,Nilgiri Marten,Indian Spotted Chevrotain,Asian Elephant,Nilgiri Langur. In Asia the hispid hare (Caprolagus hispidus), which is classified as an endangered species by the IUCN, occupies the dense, tall grassland (commonly referred to as elephant grass) along the southern Himalayan foothills of Nepal, Bangladesh, and India.</p>",
                    solution_hi: "<p>3.(b) पश्चिमी घाटों में पाई जाने वाली प्रजातियां नीलगिरि तहर, भारतीय गौर, शेर-पूंछ वाले मकाक, नीलगिरी मार्टन, भारतीय चित्तीदार शेवरोटेन, एशियाई हाथी, नीलगिरि लंगूर हैं। एशिया में हर्पिड हर (कैप्रोलैगस हेपिडस), जिसे आईयूसीएन द्वारा एक लुप्तप्राय प्रजाति के रूप में वर्गीकृत किया गया है, नेपाल, बांग्लादेश और भारत के दक्षिणी हिमालय की तलहटी के साथ घने, लंबे घास के मैदान (आमतौर पर हाथी घास के रूप में जाना जाता है) पर कब्जा कर लेता है।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "4",
                    section: "18",
                    question_en: "<p>4. Which National Highway connects Delhi and Mumbai?</p>",
                    question_hi: "<p>4. कौन सा राष्ट्रीय राजमार्ग दिल्ली और मुंबई को जोड़ता है?</p>",
                    options_en: ["<p>NH10</p>", "<p>NH1</p>", 
                                "<p>NH12</p>", "<p>NH8</p>"],
                    options_hi: ["<p>NH10</p>", "<p>NH1</p>",
                                "<p>NH12</p>", "<p>NH8</p>"],
                    solution_en: "<p>4.(d) National Highway 8 (NH 8), is a National Highway in India that connects the Indian capital city of New Delhi with the Indian Financial capital city of Mumbai. National Highway 44 &ndash; It is the longest national highway in India with a length of 3,745 kilometres running from Srinagar in the north to Kanyakumari in the South.</p>",
                    solution_hi: "<p>4.(d) राष्ट्रीय राजमार्ग 8 (NH 8), भारत में एक राष्ट्रीय राजमार्ग है जो भारतीय राजधानी नई दिल्ली को भारतीय वित्तीय राजधानी मुंबई से जोड़ता है। राष्ट्रीय राजमार्ग 44 - यह भारत का सबसे लंबा राष्ट्रीय राजमार्ग है जिसकी लंबाई 3,745 किलोमीटर है जो उत्तर में श्रीनगर से दक्षिण में कन्याकुमारी तक जाती है।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "5",
                    section: "18",
                    question_en: "<p>5. The Sikkimese are known for their amazing mask dance. What is this dance form called a Sikkim?</p>",
                    question_hi: "<p>5. सिक्किम के लोग अपने अद्भुत मुखौटा नृत्य के लिए जाने जाते हैं। इस नृत्य शैली को सिक्किम क्या कहते हैं?</p>",
                    options_en: ["<p>Purulia Chhau</p>", "<p>Chaam</p>", 
                                "<p>Mukha Bhaona</p>", "<p>Padayani</p>"],
                    options_hi: ["<p>पुरुलिया छाऊ</p>", "<p>छाम</p>",
                                "<p>मुख भाओना</p>", "<p>पदयानी</p>"],
                    solution_en: "<p>5.(b) The Lama dances or Chham is a masked dance performed by Buddhist lamas (monks) during special occasions like the Pang Lhabsol festival. The Chhau Dance originates in the Purulia district in West Bengal and draws inspiration from martial arts and combative training. Bhaona - Dances are Assamese dances. Padayani, also known as Padeni is a traditional folk dance and a ritual art from the central portion of the Indian state of Kerala.</p>",
                    solution_hi: "<p>5.(b) लामा नृत्य या छम बौद्ध लामाओं (भिक्षुओं) द्वारा पांग ल्हबसोल त्योहार जैसे विशेष अवसरों के दौरान किया जाने वाला एक नकाबपोश नृत्य है। छऊ नृत्य पश्चिम बंगाल के पुरुलिया जिले में उत्पन्न होता है और मार्शल आर्ट और जुझारू प्रशिक्षण से प्रेरणा लेता है। भाओना- नृत्य असमिया नृत्य हैं। पदयानी, जिसे पडेनी के नाम से भी जाना जाता है, एक पारंपरिक लोक नृत्य और भारतीय राज्य केरल के मध्य भाग से एक अनुष्ठान कला है।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "6",
                    section: "18",
                    question_en: "<p>6. In which wildlife sanctuary/national park do we find the &lsquo;Hangul&rsquo; (Kashmir Stag)</p>",
                    question_hi: "<p>6. हम किस वन्यजीव अभयारण्य/राष्ट्रीय उद्यान में \'हंगुल\' (कश्मीर हिरण) पाते हैं</p>",
                    options_en: ["<p>Kanha National Park</p>", "<p>Dachigam Sanctuary</p>", 
                                "<p>Mudumalai Sanctuary</p>", "<p>Dudhwa National Park</p>"],
                    options_hi: ["<p>कान्हा राष्ट्रीय उद्यान</p>", "<p>दाचीगाम अभयारण्य</p>",
                                "<p>मुदुमलाई अभयारण्य</p>", "<p>दुधवा राष्ट्रीय उद्यान</p>"],
                    solution_en: "<p>6.(b) Dachigam National Park, located 22 km from Srinagar, is popular as the home of the rare and critically endangered Hangul or Kashmir stag.</p>",
                    solution_hi: "<p>6.(b) श्रीनगर से 22 किमी दूर स्थित दाचीगाम राष्ट्रीय उद्यान दुर्लभ और गंभीर रूप से लुप्तप्राय हंगुल या कश्मीर हरिण के घर के रूप में लोकप्रिय है।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "7",
                    section: "18",
                    question_en: "<p>7. When was Pradhan Mantri Sansad Adarsh Gram Yojana launched?</p>",
                    question_hi: "<p>7. प्रधान मंत्री संसद आदर्श ग्राम योजना कब शुरू की गई थी?</p>",
                    options_en: ["<p>28th August 2014</p>", "<p>23rd July 2010</p>", 
                                "<p>9th May 2015</p>", "<p>11th October 2014</p>"],
                    options_hi: ["<p>28 अगस्त 2014</p>", "<p>23 जुलाई 2010</p>",
                                "<p>9 मई 2015</p>", "<p>11 अक्टूबर 2014</p>"],
                    solution_en: "<p>7.(d) Sansad Adarsh Gram Yojana (SAGY) is a village development project launched by the Government of India in October 2014, under which each Member of Parliament will take the responsibility of developing physical and institutional infrastructure in three villages by 2019. The Saansad Adarsh Gram Yojana (SAANJHI) was launched on 11th October, 2014.</p>",
                    solution_hi: "<p>7.(d) सांसद आदर्श ग्राम योजना (SAGY) भारत सरकार द्वारा अक्टूबर 2014 में शुरू की गई एक ग्राम विकास परियोजना है, जिसके तहत प्रत्येक संसद सदस्य 2019 तक तीन गांवों में भौतिक और संस्थागत बुनियादी ढांचे के विकास की जिम्मेदारी लेगा। सांसद आदर्श ग्राम योजना (SAANJHI) को 11 अक्टूबर 2014 को लॉन्च किया गया था।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "8",
                    section: "18",
                    question_en: "<p>8. What is the driving force and executive body of the European Union (EU)?</p>",
                    question_hi: "<p>8. यूरोपीय संघ (ईयू) की प्रेरक शक्ति और कार्यकारी निकाय क्या है?</p>",
                    options_en: ["<p>European Parliament</p>", "<p>Council of the European Union</p>", 
                                "<p>European Commission</p>", "<p>Court of Auditors</p>"],
                    options_hi: ["<p>यूरोपीय संसद</p>", "<p>यूरोपीय संघ की परिषद</p>",
                                "<p>यूरोपीय आयोग</p>", "<p>लेखा परीक्षकों का न्यायालय</p>"],
                    solution_en: "<p>8.(c) European Commission is the driving force and executive body of the European Union (EU)The European Commission upholds the common interest of the EU as a whole and serves as the EU\'s executive. The European Commission (EC) is the executive arm of the Union.</p>",
                    solution_hi: "<p>8.(c) यूरोपीय आयोग यूरोपीय संघ (EU) की प्रेरक शक्ति और कार्यकारी निकाय है यूरोपीय आयोग समग्र रूप से यूरोपीय संघ के सामान्य हित को कायम रखता है और यूरोपीय संघ के कार्यकारी के रूप में कार्य करता है। यूरोपीय आयोग (EC) संघ की कार्यकारी शाखा है।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "9",
                    section: "18",
                    question_en: "<p>9. Which one of the following is a form of renewable energy?</p>",
                    question_hi: "<p>9. निम्नलिखित में से कौन सा अक्षय ऊर्जा का एक रूप है?</p>",
                    options_en: ["<p>Potential energy</p>", "<p>Solar energy</p>", 
                                "<p>Electrical energy</p>", "<p>Chemical energy</p>"],
                    options_hi: ["<p>संभावित ऊर्जा</p>", "<p>सौर ऊर्जा</p>",
                                "<p>विद्युत ऊर्जा</p>", "<p>रासायनिक ऊर्जा</p>"],
                    solution_en: "<p>9.(b) Renewable sources of energy include solar energy, wind energy, geothermal energy and hydroelectric power. Renewable sources are often associated with green energy and clean energy.</p>",
                    solution_hi: "<p>9.(b) ऊर्जा के नवीकरणीय स्रोतों में सौर ऊर्जा, पवन ऊर्जा, भूतापीय ऊर्जा और जलविद्युत शक्ति शामिल हैं। अक्षय स्रोत अक्सर हरित ऊर्जा और स्वच्छ ऊर्जा से जुड़े होते हैं।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "10",
                    section: "18",
                    question_en: "<p>10. Which of the following states shares its borders with a maximum number of other States/Union territories?</p>",
                    question_hi: "<p>10. निम्नलिखित में से कौन सा राज्य अपनी सीमाओं को अन्य राज्यों / केंद्र शासित प्रदेशों की अधिकतम संख्या के साथ साझा करता है?</p>",
                    options_en: ["<p>Chhattisgarh</p>", "<p>Madhya Pradesh</p>", 
                                "<p>Rajasthan</p>", "<p>Uttar Pradesh</p>"],
                    options_hi: ["<p>छत्तीसगढ़</p>", "<p>मध्य प्रदेश</p>",
                                "<p>राजस्थान</p>", "<p>उत्तर प्रदेश</p>"],
                    solution_en: "<p>10.(d) Uttar Pradesh comes at the fourth place in terms of area, the state shares its borders with as many as 9 states/Union Territories in Uttarakhand to its north; Himachal Pradesh, Haryana, and Delhi to its north-west; Rajasthan to its west; Madhya Pradesh and Chhattisgarh on the south; Jharkhand to the south-east and Bihar to its east.</p>",
                    solution_hi: "<p>10.(d) उत्तर प्रदेश क्षेत्रफल के मामले में चौथे स्थान पर आता है, राज्य उत्तराखंड में अपने उत्तर में 9 राज्यों / केंद्र शासित प्रदेशों के साथ अपनी सीमाएं साझा करता है; इसके उत्तर-पश्चिम में हिमाचल प्रदेश, हरियाणा और दिल्ली; इसके पश्चिम में राजस्थान; दक्षिण में मध्य प्रदेश और छत्तीसगढ़; दक्षिण-पूर्व में झारखंड और इसके पूर्व में बिहार।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "11",
                    section: "18",
                    question_en: "<p>11. Archaeology - related work was started in India in 1784 AD by Europeans, and Asiatic Society of Bengal was formed. Who was its founder?</p>",
                    question_hi: "<p>11. भारत में पुरातत्व से संबंधित कार्य 1784 ई. में यूरोपियों द्वारा प्रारंभ किया गया और एशियाटिक सोसाइटी ऑफ बंगाल की स्थापना की गई। इसके संस्थापक कौन थे?</p>",
                    options_en: ["<p>William Jones</p>", "<p>George Turnour</p>", 
                                "<p>Alexander Cunningham</p>", "<p>James Prinsep</p>"],
                    options_hi: ["<p>विलियम जोन्स</p>", "<p>जॉर्ज टर्नर</p>",
                                "<p>अलेक्जेंडर कनिंघम</p>", "<p>जेम्स प्रिंसेप</p>"],
                    solution_en: "<p>11.(a) Asiatic Society of Bengal founded by William Jones in 1784 AD. The aim of the Asiatic society is Rediscovery of India\'s glorious past. Promotion of Western culture in India.</p>",
                    solution_hi: "<p>11.(a) 1784 ई. में विलियम जोन्स द्वारा एशियाटिक सोसाइटी ऑफ बंगाल की स्थापना की गई। एशियाई समाज का उद्देश्य भारत के गौरवशाली अतीत की पुनः खोज करना है। भारत में पाश्चात्य संस्कृति को बढ़ावा देना।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "12",
                    section: "18",
                    question_en: "<p>12. Where is Devi Ahilya Bai Holkar Airport situated?</p>",
                    question_hi: "<p>12. देवी अहिल्या बाई होल्कर हवाई अड्डा कहाँ स्थित है?</p>",
                    options_en: ["<p>Indore</p>", "<p>Bengaluru</p>", 
                                "<p>Nagpur</p>", "<p>Chennai</p>"],
                    options_hi: ["<p>इंदौर</p>", "<p>बेंगलुरु</p>",
                                "<p>नागपुर</p>", "<p>चेन्नई</p>"],
                    solution_en: "<p>12.(a) Devi Ahilya Bai Holkar Airport is a public airport, located 8 km away from the heart of the city of Indore in Madhya Pradesh. Kempegowda International Airport, Bengaluru (BLR Airport). Nagpur Airport name is Dr. Babasaheb Ambedkar International Airport.Madras International Meenambakkam Airport. (Chennai)</p>",
                    solution_hi: "<p>12.(a) देवी अहिल्या बाई होल्कर हवाई अड्डा एक सार्वजनिक हवाई अड्डा है, जो मध्य प्रदेश में इंदौर शहर के केंद्र से 8 किमी दूर स्थित है। केम्पेगौड़ा अंतर्राष्ट्रीय हवाई अड्डा, बेंगलुरु (BLR हवाई अड्डा)। नागपुर हवाई अड्डे का नाम डॉ बाबासाहेब अम्बेडकर अंतर्राष्ट्रीय हवाई अड्डा है। मद्रास अंतर्राष्ट्रीय मीनांबक्कम हवाई अड्डा (चेन्नई)।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "13",
                    section: "18",
                    question_en: "<p>13. ______ is one of the recipients of the Ramon Magsaysay Award.</p>",
                    question_hi: "<p>13. _____रेमन मैगसेसे पुरस्कार प्राप्त करने वालों में से एक है।</p>",
                    options_en: ["<p>CNR Rao</p>", "<p>Arvind Kejriwal</p>", 
                                "<p>Suprabha Seshan</p>", "<p>Nirmala Deshpande</p>"],
                    options_hi: ["<p>सीएनआर राव</p>", "<p>अरविंद केजरीवाल</p>",
                                "<p>सुप्रभा शेषन</p>", "<p>निर्मला देशपांडे</p>"],
                    solution_en: "<p>13.(b) Arvind Kejriwal is one of the recipients of the Ramon Magsaysay Award. Vinoba Bhave(1958) was the 1st Magsaysay Award winner. Ravish Kumar won this award in 2019.</p>",
                    solution_hi: "<p>13.(b) अरविंद केजरीवाल रेमन मैग्सेसे पुरस्कार प्राप्त करने वालों में से एक हैं। विनोबा भावे (1958) प्रथम मैग्सेसे पुरस्कार विजेता थे। रवीश कुमार ने 2019 में यह पुरस्कार जीता था।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "14",
                    section: "18",
                    question_en: "<p>14. Where and when was SAARC (South Asian Association for Regional Cooperation) formed ?</p>",
                    question_hi: "<p>14. SAARC (दक्षिण एशियाई क्षेत्रीय सहयोग संघ) का गठन कहाँ और कब किया गया था?</p>",
                    options_en: ["<p>Indian, 1987</p>", "<p>Bangladesh, 1985</p>", 
                                "<p>Sri Lanka, 1988</p>", "<p>Pakistan, 1981</p>"],
                    options_hi: ["<p>भारतीय, 1987</p>", "<p>बांग्लादेश, 1985</p>",
                                "<p>श्रीलंका, 1988</p>", "<p>पाकिस्तान, 1981</p>"],
                    solution_en: "<p>14.(b) SAARC (South Asian Association for Regional Cooperation ) was formed in the year 1985(Bangladesh, Dhaka). SAARC\'s countries include Bangladesh, Bhutan, India, Maldives, Nepal, Pakistan,Sri Lanka and Afghanistan.</p>",
                    solution_hi: "<p>14.(b) SAARC (दक्षिण एशियाई क्षेत्रीय सहयोग संघ) का गठन वर्ष 1985 (बांग्लादेश, ढाका) में किया गया था। सार्क के देशों में बांग्लादेश, भूटान, भारत, मालदीव, नेपाल, पाकिस्तान, श्रीलंका और अफगानिस्तान शामिल हैं।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "15",
                    section: "18",
                    question_en: "<p>15. ________ is the oldest Trade Union Organisations in India.</p>",
                    question_hi: "<p>15. ______ भारत में सबसे पुराना ट्रेड यूनियन संगठन है।</p>",
                    options_en: ["<p>CITU</p>", "<p>AITUC</p>", 
                                "<p>BMS</p>", "<p>INTUC</p>"],
                    options_hi: ["<p>CITU</p>", "<p>AITUC</p>",
                                "<p>BMS</p>", "<p>INTUC</p>"],
                    solution_en: "<p>15.(b) The All India Trade Union Congress (AITUC) is the oldest trade union federation in India. It is associated with the Communist Party of India, founded on 31 october, 1920.</p>",
                    solution_hi: "<p>15.(b) ऑल इंडिया ट्रेड यूनियन कांग्रेस (AITUC) भारत का सबसे पुराना ट्रेड यूनियन फेडरेशन है। यह 31 अक्टूबर, 1920 को स्थापित भारतीय कम्युनिस्ट पार्टी से जुड़ा है।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "16",
                    section: "18",
                    question_en: "<p>16. In which year was the Jawahar Gram Samridhi Yojana launched?</p>",
                    question_hi: "<p>16. जवाहर ग्राम समृद्धि योजना किस वर्ष शुरू की गई थी?</p>",
                    options_en: ["<p>1999</p>", "<p>2016</p>", 
                                "<p>1998</p>", "<p>2001</p>"],
                    options_hi: ["<p>1999</p>", "<p>2016</p>",
                                "<p>1998</p>", "<p>2001</p>"],
                    solution_en: "<p>16.(a) Jawahar Gram Samridhi Yojana (JGSY) is the restructured, streamlined, and comprehensive version of the erstwhile Jawahar Rozgar Yojana (JRY). Launched on 1st April 1999.</p>",
                    solution_hi: "<p>16.(a) जवाहर ग्राम समृद्धि योजना (JGSY) पूर्ववर्ती जवाहर रोजगार योजना (JRY) का पुनर्गठित, सुव्यवस्थित और व्यापक संस्करण है। 1 अप्रैल 1999 को लॉन्च किया गया।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "17",
                    section: "18",
                    question_en: "<p>17. Which colour deviates the least when light passes through a prism?</p>",
                    question_hi: "<p>17. प्रिज्म से प्रकाश के गुजरने पर कौन सा रंग सबसे कम विचलित होता है?</p>",
                    options_en: ["<p>Violet</p>", "<p>Blue</p>", 
                                "<p>Red</p>", "<p>Green</p>"],
                    options_hi: ["<p>बैंगनी</p>", "<p>नीला</p>",
                                "<p>लाल</p>", "<p>हरा</p>"],
                    solution_en: "<p>17.(c) Violet colour deviates most and red deviates the least because the wavelength of a red light is almost double the wavelength of violet light.</p>",
                    solution_hi: "<p>17.(c) बैंगनी रंग सबसे अधिक विचलित होता है और लाल सबसे कम विचलित होता है क्योंकि लाल प्रकाश की तरंग दैर्ध्य बैंगनी प्रकाश की तरंग दैर्ध्य से लगभग दोगुनी होती है।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "18",
                    section: "18",
                    question_en: "<p>18. Which was the last country to join BRICS?</p>",
                    question_hi: "<p>18. ब्रिक्स में शामिल होने वाला अंतिम देश कौन सा था?</p>",
                    options_en: ["<p>Russia</p>", "<p>China</p>", 
                                "<p>South Africa</p>", "<p>India</p>"],
                    options_hi: ["<p>रूस</p>", "<p>चीन</p>",
                                "<p>दक्षिण अफ्रीका</p>", "<p>भारत</p>"],
                    solution_en: "<p>18.(c) South Africa was the last country to join BRICS. BRICS (June 2006) is the acronym coined to associate five major emerging economies: Brazil, Russia, India, China, and South Africa.</p>",
                    solution_hi: "<p>18.(c) दक्षिण अफ्रीका BRICS में शामिल होने वाला अंतिम देश था। BRICS (जून 2006) पांच प्रमुख उभरती अर्थव्यवस्थाओं: ब्राजील, रूस, भारत, चीन और दक्षिण अफ्रीका को जोड़ने के लिए बनाया गया एक संक्षिप्त नाम है।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "19",
                    section: "18",
                    question_en: "<p>19. _______ is NOT a carnivorous plant.</p>",
                    question_hi: "<p>19. _______ मांसाहारी पौधा नहीं है।</p>",
                    options_en: ["<p>Sundew</p>", "<p>Corkscrew</p>", 
                                "<p>Monkeycup</p>", "<p>Tiger Lily</p>"],
                    options_hi: ["<p>सन्डू</p>", "<p>कॉर्कस्क्रू</p>",
                                "<p>मंकी कैप</p>", "<p>टाइगर लिली</p>"],
                    solution_en: "<p>19.(d) Names of Carnivorous plants are Nepenthes - the Monkey Cups, Drosophyllum, Triphyophyllum peltatum, Drosera - the Sundews, Dionaea muscipula - The Venus Flytrap,Cephalotus follicularis - the Albany Pitcher Plant, Darlingtonia californica - the Cobra Lily,Sarracenia - the Pitcher Plants. Tiger Lily is a Herbaceous perennial type of plant.</p>",
                    solution_hi: "<p>19.(d) कार्निवोरस पौधों के नाम हैं नेपेंथेस - मंकीकप्स, ड्रोसोफिलम, ट्राइफ्योफिलम पेल्टैटम, ड्रोसेरा - द सनड्यूज, डियोनिया मसिपुला - द वीनस फ्लाईट्रैप, सेफलोटस फॉलिक्युलरिस - अल्बानी पिचर प्लांट, डार्लिंगटनिया कैलिफोर्निका - कोबरा - लिली, सर्रेसेनिया। टाइगर लिली एक शाकाहारी बारहमासी प्रकार का पौधा है।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "20",
                    section: "18",
                    question_en: "<p>20. Which of the following monuments belongs to UNESCO&rsquo;s list of World Heritage Sites in India?</p>",
                    question_hi: "<p>20. निम्नलिखित में से कौन सा स्मारक भारत में यूनेस्को की विश्व धरोहर स्थलों की सूची में शामिल है?</p>",
                    options_en: ["<p>Krimchi Temple</p>", "<p>Vaishno Devi</p>", 
                                "<p>Khajuraho</p>", "<p>Akshardham Temple</p>"],
                    options_hi: ["<p>क्रिमची मंदिर</p>", "<p>वैष्णो देवी</p>",
                                "<p>खजुराहो</p>", "<p>अक्षरधाम मंदिर</p>"],
                    solution_en: "<p>20.(c) In the year 1986, UNESCO recognised Khajuraho(Madhya Pradesh) as a World Heritage Site for its \"human creativity\". There are 32 cultural, 7 natural, and 1 mixed property in the 40 UNESCO World Heritage sites in India.</p>",
                    solution_hi: "<p>20.(c) वर्ष 1986 में, UNESCO ने खजुराहो (मध्य प्रदेश) को अपनी \"मानव रचनात्मकता\" के लिए विश्व धरोहर स्थल के रूप में मान्यता दी। भारत में 40 यूनेस्को विश्व धरोहर स्थलों में 32 सांस्कृतिक, 7 प्राकृतिक और 1 मिश्रित संपत्ति हैं।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "21",
                    section: "18",
                    question_en: "<p>21. Who among the following can introduce a Government Bill ?</p>",
                    question_hi: "<p>21. निम्नलिखित में से कौन सरकारी विधेयक पेश कर सकता है?</p>",
                    options_en: ["<p>Minister in the Lok Sabha</p>", "<p>Member of the Treasury bench in the Lok Sabha</p>", 
                                "<p>Minister in any house of the Parliament</p>", "<p>Member of the Parliament who is not a Minister</p>"],
                    options_hi: ["<p>लोकसभा में मंत्री</p>", "<p>लोकसभा में ट्रेजरी बेंच के सदस्य</p>",
                                "<p>संसद के किसी भी सदन में मंत्री</p>", "<p>संसद सदस्य जो मंत्री नहीं है</p>"],
                    solution_en: "<p>21.(c) Ministers in any house of the Parliament can introduce a Government Bill. A Bill is a statute in draft and cannot become law unless it has received the approval of both the Houses of Parliament and the assent of the President of India.</p>",
                    solution_hi: "<p>21.(c) संसद के किसी भी सदन में मंत्री सरकारी विधेयक पेश कर सकते हैं। एक विधेयक मसौदा में एक क़ानून है और तब तक कानून नहीं बन सकता जब तक कि उसे संसद के दोनों सदनों और भारत के राष्ट्रपति की सहमति प्राप्त न हो।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "22",
                    section: "18",
                    question_en: "<p>22. The Dhauladhar range in the Himalayas is a part of:</p>",
                    question_hi: "<p>22. हिमालय में धौलाधार श्रेणी का एक भाग है:</p>",
                    options_en: ["<p>Central Himalayas</p>", "<p>Lesser Himalayas</p>", 
                                "<p>Trans-Himalayan</p>", "<p>Shiwalik</p>"],
                    options_hi: ["<p>मध्य हिमालय</p>", "<p>कम हिमालय</p>",
                                "<p>ट्रांस-हिमालयी</p>", "<p>शिवालिक</p>"],
                    solution_en: "<p>22.(b) The Dhauladhar range in the Himalayas is a part of Lesser Himalayas.It rises from the Shivalik hills, to the north of Kangra and Mandi. Dharamsala, the headquarters of Kangra district and the winter capital of Himachal Pradesh. Pir Panjal is the largest range of the Lesser Himalayas.</p>",
                    solution_hi: "<p>22.(b) हिमालय में धौलाधार श्रेणी लघु हिमालय का एक हिस्सा है। यह शिवालिक पहाड़ियों से कांगड़ा और मंडी के उत्तर में निकलती है। धर्मशाला, कांगड़ा जिले का मुख्यालय और हिमाचल प्रदेश की शीतकालीन राजधानी है। पीर पंजाल लघु हिमालय की सबसे बड़ी श्रेणी है।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "23",
                    section: "18",
                    question_en: "<p>23. According to the Living Planet report 2020, the largest wildlife population loss has been found in ______.</p>",
                    question_hi: "<p>23. लिविंग प्लैनेट रिपोर्ट 2020 के अनुसार, ________ में सबसे बड़ा वन्यजीव आबादी का नुकसान पाया गया है।</p>",
                    options_en: ["<p>North America</p>", "<p>Europe</p>", 
                                "<p>Asia</p>", "<p>Latin America</p>"],
                    options_hi: ["<p>उत्तरी अमेरिका</p>", "<p>यूरोप</p>",
                                "<p>एशिया</p>", "<p>लैटिन अमेरिका</p>"],
                    solution_en: "<p>23.(d) According to the Living Planet report 2020, the largest wildlife population loss has been found in Latin America. The Living Planet Report, WWF\'s flagship publication released every two years, is a comprehensive study of trends in global biodiversity and the health of the planet.</p>",
                    solution_hi: "<p>23.(d) लिविंग प्लैनेट रिपोर्ट 2020 के अनुसार, लैटिन अमेरिका में सबसे अधिक वन्यजीव आबादी का नुकसान पाया गया है। लिविंग प्लैनेट रिपोर्ट, WWF का प्रमुख प्रकाशन, जो हर दो साल में जारी किया जाता है, वैश्विक जैव विविधता और ग्रह के स्वास्थ्य के रुझानों का एक व्यापक अध्ययन है।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "24",
                    section: "18",
                    question_en: "<p>24. Who is known as the &lsquo;Father of Civil Services&rsquo; in India?</p>",
                    question_hi: "<p>24. भारत में \'सिविल सेवा के जनक\' के रूप में किसे जाना जाता है?</p>",
                    options_en: ["<p>Mahatma Gandhi</p>", "<p>Warren Hastings</p>", 
                                "<p>Charles Cornwallis</p>", "<p>Robert Clive</p>"],
                    options_hi: ["<p>महात्मा गांधी</p>", "<p>वारेन हेस्टिंग्स</p>",
                                "<p>चार्ल्स कॉर्नवालिस</p>", "<p>रॉबर्ट क्लाइव</p>"],
                    solution_en: "<p>24.(c) During the British raj, Warren Hastings laid the foundation of civil service and Charles Cornwallis reformed, modernised, and rationalised it. Hence, Charles Cornwallis is known as \'the Father of civil service in India\'.</p>",
                    solution_hi: "<p>24.(c) ब्रिटिश राज के दौरान, वारेन हेस्टिंग्स ने सिविल सेवा की नींव रखी और चार्ल्स कॉर्नवालिस ने इसका सुधार, आधुनिकीकरण और इसे युक्तिसंगत बनाया। इसलिए, चार्ल्स कॉर्नवालिस को \'भारत में सिविल सेवा के पिता\' के रूप में जाना जाता है।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "25",
                    section: "18",
                    question_en: "<p>25. Who was awarded the Bharat Ratna Award before becoming the President of India?</p>",
                    question_hi: "<p>25. भारत के राष्ट्रपति बनने से पहले भारत रत्न पुरस्कार से किसे सम्मानित किया गया था?</p>",
                    options_en: ["<p>VV Giri</p>", "<p>Pranab Mukherjee</p>", 
                                "<p>Rajendra Prasad</p>", "<p>S Radhakrishnan</p>"],
                    options_hi: ["<p>वीवी गिरि</p>", "<p>प्रणब मुखर्जी</p>",
                                "<p>राजेंद्र प्रसाद</p>", "<p>एस राधाकृष्णन</p>"],
                    solution_en: "<p>25.(d) S Radhakrishnan was awarded the Bharat Ratna Award in 1954 before becoming the President of India.</p>",
                    solution_hi: "<p>25.(d) एस राधाकृष्णन को भारत के राष्ट्रपति बनने से पहले 1954 में भारत रत्न पुरस्कार से सम्मानित किया गया था।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "26",
                    section: "18",
                    question_en: "<p>26. Bamboo flowering causes:</p>",
                    question_hi: "<p>26. बांस फूलने के कारण:</p>",
                    options_en: ["<p>an increase in the population of rats</p>", "<p>land to become infertile</p>", 
                                "<p>an increase in pests and insects</p>", "<p>a decrease in rainfall</p>"],
                    options_hi: ["<p>चूहों की आबादी में वृद्धि</p>", "<p>भूमि उपजाऊ बनने के लिए</p>",
                                "<p>कीटों और कीड़ों में वृद्धि</p>", "<p>वर्षा में कमी</p>"],
                    solution_en: "<p>26.(a) Bamboo flowering causes an increase in the population of rats. Bamboo flowering is a peculiar phenomenon. Bamboos grow vegetatively for a species-specific period before flowering, seeding and dying. Most bamboo plants flower only once in their life cycle.</p>",
                    solution_hi: "<p>26.(a) बांस के फूलने से चूहों की संख्या में वृद्धि होती है। बाँस का फूलना एक अजीबोगरीब घटना है। फूल, बोने और मरने से पहले एक प्रजाति-विशिष्ट अवधि के लिए बांस वानस्पतिक रूप से विकसित होते हैं। अधिकांश बांस के पौधे अपने जीवन चक्र में केवल एक बार फूलते हैं।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "27",
                    section: "18",
                    question_en: "<p>27. An imaginary line or a line on a map or chart that connects all points having the same depth below a water surface such as sea water is called:</p>",
                    question_hi: "<p>27. मानचित्र या चार्ट पर एक काल्पनिक रेखा या रेखा जो समुद्र के पानी जैसे पानी की सतह के नीचे समान गहराई वाले सभी बिंदुओं को जोड़ती है, कहलाती है:</p>",
                    options_en: ["<p>Isobar</p>", "<p>Isohaline</p>", 
                                "<p>Isobath</p>", "<p>Isogloss</p>"],
                    options_hi: ["<p>समताप-रेखा</p>", "<p>समलवण रेखा</p>",
                                "<p>समगभीरता रेखा</p>", "<p>समवाक</p>"],
                    solution_en: "<p>27.(c) An imaginary line or a line on a map or chart that connects all points having the same depth below a water surface such as sea water is called Isobath. Isobars are lines on a weather map joining together places of equal atmospheric pressure. Isohaline are lines (or contours) that join points of equal salinity in an aquatic system. Isogloss is a line on a map marking an area having a distinct linguistic feature.</p>",
                    solution_hi: "<p>27.(c) मानचित्र या चार्ट पर एक काल्पनिक रेखा या रेखा जो समुद्र के पानी जैसे पानी की सतह के नीचे समान गहराई वाले सभी बिंदुओं को जोड़ती है, इसोबाथ कहलाती है। समदाब रेखाएं मौसम के नक्शे पर समान वायुमंडलीय दबाव वाले स्थानों को मिलाने वाली रेखाएं होती हैं। आइसोहालीन ऐसी रेखाएं (या समोच्च रेखाएं) होती हैं जो जलीय तंत्र में समान लवणता वाले बिंदुओं को मिलाती हैं। समवाक मानचित्र पर एक विशिष्ट भाषाई विशेषता वाले क्षेत्र को चिह्नित करने वाली एक रेखा है।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "28",
                    section: "18",
                    question_en: "<p>28. __________ is a location-based F2S (Farm to Shop) trading platform that helps Indian farmers sell their produce directly to the consumer, thus eliminating the middleman.</p>",
                    question_hi: "<p>28. __________ एक स्थान-आधारित F2S (फार्म टू शॉप) ट्रेडिंग प्लेटफॉर्म है जो भारतीय किसानों को अपनी उपज सीधे उपभोक्ता को बेचने में मदद करता है, इस प्रकार बिचौलियों को समाप्त करता है।</p>",
                    options_en: ["<p>Fasal</p>", "<p>Kiran</p>", 
                                "<p>Safal</p>", "<p>Mandi Trades</p>"],
                    options_hi: ["<p>फसल</p>", "<p>किरण</p>",
                                "<p>सफल</p>", "<p>मंडी ट्रेड्स</p>"],
                    solution_en: "<p>28.(d) Mandi Trades is a location-based F2S (Farm to Shop) trading platform that helps Indian farmers sell their produce directly to the consumer, thus eliminating the middleman. Mandi in Hindi language means marketplace. Traditionally, such market places were for food and agri-commodities.</p>",
                    solution_hi: "<p>28.(d) मंडी ट्रेड्स एक स्थान-आधारित F2S (फार्म टू शॉप) ट्रेडिंग प्लेटफॉर्म है जो भारतीय किसानों को अपनी उपज सीधे उपभोक्ता को बेचने में मदद करता है, इस प्रकार बिचौलियों को खत्म करता है। हिंदी भाषा में मंडी का मतलब बाजार होता है। परंपरागत रूप से, ऐसे बाजार स्थान भोजन और कृषि-वस्तुओं के लिए थे।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "29",
                    section: "18",
                    question_en: "<p>29. In which of the following plant groups are the seeds present inside the fruit?</p>",
                    question_hi: "<p>29. निम्नलिखित में से किस पौधे के समूह में फल के अंदर बीज मौजूद होते हैं ?</p>",
                    options_en: ["<p>Gymnosperms</p>", "<p>Angiosperms</p>", 
                                "<p>Bryophytes</p>", "<p>Pteridophytes</p>"],
                    options_hi: ["<p>जिम्नोस्पर्म</p>", "<p>एंजियोस्पर्म</p>",
                                "<p>ब्रायोफाइट्स</p>", "<p>टेरिडोफाइट्स</p>"],
                    solution_en: "<p>29.(b) Angiosperms are plants that produce flowers and bear their seeds in fruits. Gymnosperms are woody plants, either shrubs, trees, or, rarely, vines (some gnetophytes). Bryophytes are small, non-vascular plants, such as mosses, liverworts and hornworts. A pteridophyte is a vascular plant (with xylem and phloem) that disperses spores.</p>",
                    solution_hi: "<p>29.(b) एंजियोस्पर्म ऐसे पौधे हैं जो फूल पैदा करते हैं और फलों में अपने बीज धारण करते हैं। जिम्नोस्पर्म लकड़ी के पौधे हैं, या तो झाड़ियाँ, पेड़, या, शायद ही कभी, लताएँ (कुछ ग्नोफाइट्स)। ब्रायोफाइट्स छोटे, गैर-संवहनी पौधे हैं, जैसे काई, लिवरवॉर्ट्स और हॉर्नवॉर्ट्स। टेरिडोफाइट एक संवहनी पौधा है (जाइलम और फ्लोएम के साथ) जो बीजाणुओं को फैलाता है।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "30",
                    section: "18",
                    question_en: "<p>30. Name the first speaker of the Indian Parliament.</p>",
                    question_hi: "<p>30. भारतीय संसद के पहले अध्यक्ष का नाम बताइए।</p>",
                    options_en: ["<p>BR Ambedkar</p>", "<p>KM Munshi</p>", 
                                "<p>GV Mavalankar</p>", "<p>Rajendra Prasad</p>"],
                    options_hi: ["<p>बीआर अम्बेडकर</p>", "<p>केएम मुंशी</p>",
                                "<p>जीवी मावलंकर</p>", "<p>राजेंद्र प्रसाद</p>"],
                    solution_en: "<p>30.(c) Ganesh Vasudev Mavalankar (1952&ndash;1956) was the first speaker of the Indian Parliament. Lok Sabha was formed on 15 May 1952.</p>",
                    solution_hi: "<p>30.(c) गणेश वासुदेव मावलंकर (1952-1956) भारतीय संसद के पहले अध्यक्ष थे। लोकसभा का गठन 15 मई 1952 को हुआ था।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "31",
                    section: "18",
                    question_en: "<p>31. Which British Prime Minister sent the Cripps Mission to India?</p>",
                    question_hi: "<p>31. किस ब्रिटिश प्रधान मंत्री ने क्रिप्स मिशन को भारत भेजा?</p>",
                    options_en: ["<p>Winston Churchill</p>", "<p>Robert Thatcher</p>", 
                                "<p>Margaret Thatcher</p>", "<p>Benjamin Disraeli</p>"],
                    options_hi: ["<p>विंस्टन चर्चिल</p>", "<p>रॉबर्ट थैचर</p>",
                                "<p>मार्गरेट थैचर</p>", "<p>बेंजामिन डिसरायली</p>"],
                    solution_en: "<p>31.(a) Winston Churchill sent the Cripps Mission to India. The Cripps Mission was a failed attempt in late March 1942 by the British government to secure full Indian cooperation and support for their efforts in World War II. The mission was headed by a senior minister Sir Stafford Cripps.</p>",
                    solution_hi: "<p>31.(a) विंस्टन चर्चिल ने क्रिप्स मिशन को भारत भेजा। क्रिप्स मिशन मार्च 1942 के अंत में ब्रिटिश सरकार द्वारा द्वितीय विश्व युद्ध में उनके प्रयासों के लिए पूर्ण भारतीय सहयोग और समर्थन हासिल करने का एक असफल प्रयास था। मिशन का नेतृत्व एक वरिष्ठ मंत्री सर स्टैफोर्ड क्रिप्स ने किया था।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "32",
                    section: "18",
                    question_en: "<p>32. When was GSAT-6A launched and from where?</p>",
                    question_hi: "<p>32. GSAT-6A को कब और कहाँ से लॉन्च किया गया था?</p>",
                    options_en: ["<p>29 June 2017, Guiana Space Centre (Kourou)</p>", "<p>29 March 2018, Satish Dhawan Space Centre (Sriharikota)</p>", 
                                "<p>6 October 2016, Guiana Space Centre (Kourou)</p>", "<p>12 January 2018, Satish Dhawan Space Centre (Sriharikota)</p>"],
                    options_hi: ["<p>29 जून 2017, गुयाना स्पेस सेंटर (कौरौ)</p>", "<p>29 मार्च 2018, सतीश धवन अंतरिक्ष केंद्र (श्रीहरिकोटा)</p>",
                                "<p>6 अक्टूबर 2016, गुयाना स्पेस सेंटर (कौरौ)</p>", "<p>12 जनवरी 2018, सतीश धवन अंतरिक्ष केंद्र (श्रीहरिकोटा)</p>"],
                    solution_en: "<p>32.(b) GSAT-6A was launched from Satish Dhawan Space Centre(Sriharikota on 29 March 2018. GSAT-6A was a communication satellite launched by the Indian Space Research Organisation It featured a 6-metre unfurlable S-band antenna similar to the one used on GSAT-6.</p>",
                    solution_hi: "<p>32.(b) GSAT-6A को 29 मार्च 2018 को सतीश धवन अंतरिक्ष केंद्र (श्रीहरिकोटा) से लॉन्च किया गया था। GSAT-6A भारतीय अंतरिक्ष अनुसंधान संगठन द्वारा लॉन्च किया गया एक संचार उपग्रह था। इसमें GSAT-6 पर इस्तेमाल किए गए एक के समान एक 6-मीटर अनफुरेबल S-बैंड एंटेना इस्तेमाल किया गया था।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "33",
                    section: "18",
                    question_en: "<p>33. In the context of computing, a &lsquo;spider&rsquo; is a/an:</p>",
                    question_hi: "<p>33. कंप्यूटिंग के संदर्भ में, एक \'मकड़ी\' एक/एक है:</p>",
                    options_en: ["<p>search engine</p>", "<p>application for viewing websites</p>", 
                                "<p>program that catalogues websites</p>", "<p>hacker who breaks into a corporate compute system</p>"],
                    options_hi: ["<p>सर्च इंजन</p>", "<p>वेबसाइटों को देखने के लिए आवेदन</p>",
                                "<p>प्रोग्राम जो वेबसाइटों को सूचीबद्ध करता है</p>", "<p>हैकर जो कॉर्पोरेट कंप्यूट सिस्टम में सेंध लगाता है</p>"],
                    solution_en: "<p>33.(c) A spider (program that catalogues websites) is a program that visits Web sites and reads their pages and other information in order to create entries for a search engine index. A web crawler, or spider, is a type of bot that is typically operated by search engines like Google and Bing.</p>",
                    solution_hi: "<p>33.(c) एक \'मकड़ी\' (प्रोग्राम जो वेबसाइटों को सूचीबद्ध करता है) एक ऐसा प्रोग्राम है जो वेब साइटों पर जाता है और खोज इंजन इंडेक्स के लिए प्रविष्टियां बनाने के लिए उनके पेज और अन्य जानकारी पढ़ता है। एक वेब क्रॉलर, या स्पाइडर, एक प्रकार का बॉट है जो आम तौर पर Google और बिंग जैसे खोज इंजनों द्वारा संचालित होता है।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "34",
                    section: "18",
                    question_en: "<p>34. Who among the following nationalist leaders founded and edited the Marathi newspaper &lsquo;Kesari&rsquo;?</p>",
                    question_hi: "<p>34. निम्नलिखित राष्ट्रवादी नेताओं में से किसने मराठी समाचार पत्र \'केसरी\' की स्थापना और संपादन किया था?</p>",
                    options_en: ["<p>Vinayak Damodar Savarkar</p>", "<p>Vishnushastri Chiplunkar</p>", 
                                "<p>Bhimrao Ambedkar</p>", "<p>Bal Gangadhar Tilak</p>"],
                    options_hi: ["<p>विनायक दामोदर सावरकर</p>", "<p>विष्णुशास्त्री चिपलूनकर</p>",
                                "<p>भीमराव अम्बेडकर</p>", "<p>बाल गंगाधर तिलक</p>"],
                    solution_en: "<p>34.(d) Bal Gangadhar Tilak founded and edited the Marathi newspaper &lsquo;Kesari&rsquo; on 4 January 1881. Bal Gangadhar Tilak is known as Lokmanya Tilak.</p>",
                    solution_hi: "<p>34.(d) बाल गंगाधर तिलक ने 4 जनवरी 1881 को मराठी अखबार \'केसरी\' की स्थापना और संपादन किया। बाल गंगाधर तिलक को लोकमान्य तिलक के नाम से जाना जाता है।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "35",
                    section: "18",
                    question_en: "<p>35. Cupcake, Donut, Eclair, Froyo and Gingerbread are codenames for different _________ versions.</p>",
                    question_hi: "<p>35. कपकेक, डोनट, एक्लेयर, फ्रायो और जिंजरब्रेड विभिन्न _________ संस्करणों के कोडनेम हैं।</p>",
                    options_en: ["<p>Piano</p>", "<p>Android</p>", 
                                "<p>Computer</p>", "<p>Ca</p>"],
                    options_hi: ["<p>पियानो</p>", "<p>एंड्रॉइड</p>",
                                "<p>कंप्यूटर</p>", "<p>सीए</p>"],
                    solution_en: "<p>35.(b) Cupcake, Donut, Eclair, Froyo and Gingerbread are code names for different Android versions.</p>",
                    solution_hi: "<p>35.(b) कपकेक, डोनट, एक्लेयर, फ्रायो और जिंजरब्रेड विभिन्न एंड्रॉइड वर्जन के लिए कोड नाम हैं।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "36",
                    section: "18",
                    question_en: "<p>36. Which of the following keyboard shortcuts will you use to switch to a previously opened program?</p>",
                    question_hi: "<p>36. पहले से खोले गए प्रोग्राम में स्विच करने के लिए आप निम्न में से किस कीबोर्ड शॉर्टकट का उपयोग करेंगे?</p>",
                    options_en: ["<p>Ctrl + Home</p>", "<p>Alt + Shift + Tab</p>", 
                                "<p>Atl + Tab</p>", "<p>Ctrl + N</p>"],
                    options_hi: ["<p>Ctrl + Home</p>", "<p>Alt + Shift + Tab</p>",
                                "<p>Atl + Tab</p>", "<p>Ctrl + N</p>"],
                    solution_en: "<p>36.(b) Shortcuts you use to switch to a previously opened program is Alt + Shift + Tab. Ctrl + Home is a shortcut key that moves the cursor to the end of a document. Pressing Alt-Tab switches between all open applications in the Taskbar. Ctrl+N is a shortcut key often used to create a new document, window, workbook, or another type of file.</p>",
                    solution_hi: "<p>36.(b) पहले से खोले गए प्रोग्राम में स्विच करने के लिए आपके द्वारा उपयोग किया जाने वाला शॉर्टकट Alt + Shift + Tab है। Ctrl+Home एक शॉर्टकट कुंजी है जो कर्सर को दस्तावेज़ के अंत में ले जाती है। Alt-Tab दबाने से टास्कबार में सभी खुले हुए एप्लिकेशन के बीच स्विच हो जाता है। Ctrl+N एक शॉर्टकट कुंजी है जिसका उपयोग अक्सर एक नया दस्तावेज़, विंडो, कार्यपुस्तिका, या किसी अन्य प्रकार की फ़ाइल बनाने के लिए किया जाता है।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "37",
                    section: "18",
                    question_en: "<p>37. In October 2018, Indian scientists developed a gel called _______ to protect farmers from toxic pesticides.</p>",
                    question_hi: "<p>37.अक्टूबर 2018 में, भारतीय वैज्ञानिकों ने किसानों को जहरीले कीटनाशकों से बचाने के लिए _______ नामक एक जेल विकसित किया।</p>",
                    options_en: ["<p>Hydroxyethyl Cellulose</p>", "<p>Sodium Polyacrylate</p>", 
                                "<p>Zinc-oxide</p>", "<p>Poly-oxime</p>"],
                    options_hi: ["<p>हाइड्रॉक्सीएथिल सेलुलोज</p>", "<p>सोडियम पॉलीएक्रिलेट</p>",
                                "<p>जिंक ऑक्साइड</p>", "<p>पॉली-ऑक्साइम</p>"],
                    solution_en: "<p>37.(d) The gel, named poly-Oxime, has been prepared by researchers at the Institute for Stem Cell Science and Regenerative Medicine (InStem), Bengaluru, from a nucleophilic polymer.</p>",
                    solution_hi: "<p>37.(d) पॉली-ऑक्साइम नाम का जेल, न्यूक्लियोफिलिक पॉलीमर से इंस्टीट्यूट फॉर स्टेम सेल साइंस एंड रीजनरेटिव मेडिसिन (InStem), बेंगलुरु के शोधकर्ताओं द्वारा तैयार किया गया है।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "38",
                    section: "18",
                    question_en: "<p>38. The soccer legend Johan Cruyff belonged to...</p>",
                    question_hi: "<p>38. फुटबॉल के दिग्गज जोहान क्रूफ का संबंध _______ से था</p>",
                    options_en: ["<p>Brazil</p>", "<p>England</p>", 
                                "<p>Netherlands</p>", "<p>Argentina</p>"],
                    options_hi: ["<p>ब्राजील</p>", "<p>इंग्लैंड</p>",
                                "<p>नीदरलैंड</p>", "<p>अर्जेंटीना</p>"],
                    solution_en: "<p>38.(c) The soccer legend Johan Cruyff belonged to Amsterdam, Netherlands.</p>",
                    solution_hi: "<p>38.(c) फुटबॉल के दिग्गज जोहान क्रूफ एम्स्टर्डम, नीदरलैंड्स के थे।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "39",
                    section: "18",
                    question_en: "<p>39. Which freedom fighter shot General Michael O&rsquo;Dwyer?</p>",
                    question_hi: "<p>39. किस स्वतंत्रता सेनानी ने जनरल माइकल ओ\'डायर को गोली मारी?</p>",
                    options_en: ["<p>Chandra Shekhar Azad</p>", "<p>Bhagat Singh</p>", 
                                "<p>Sukh Dev</p>", "<p>Udham Singh</p>"],
                    options_hi: ["<p>चंद्र शेखर आज़ादी</p>", "<p>भगत सिंह</p>",
                                "<p>सुख देवी</p>", "<p>उधम सिंह</p>"],
                    solution_en: "<p>39.(d) Indian freedom fighter and revolutionary Udham Singh belonged to the Ghadar Party and was a major part of India\'s fight against British rule. He was also behind the assassination of Michael O\'Dwyer, the former lieutenant governor of Punjab in India and a major accused behind the Jallianwala massacre.</p>",
                    solution_hi: "<p>39.(d) भारतीय स्वतंत्रता सेनानी और क्रांतिकारी उधम सिंह ग़दर पार्टी के थे और ब्रिटिश शासन के खिलाफ भारत की लड़ाई का एक प्रमुख हिस्सा थे। वह भारत में पंजाब के पूर्व लेफ्टिनेंट गवर्नर माइकल ओ\'डायर की हत्या और जलियांवाला हत्याकांड के एक प्रमुख आरोपी के पीछे भी था।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "40",
                    section: "misc",
                    question_en: "<p>40. Which of the following was discovered by Henri Becquerel?</p>",
                    question_hi: "<p>40. निम्नलिखित में से किसकी खोज हेनरी बेकरेल ने की थी?</p>",
                    options_en: ["<p>Infrared radiation</p>", "<p>Ultraviolet light</p>", 
                                "<p>X-Rays</p>", "<p>Radioactivity</p>"],
                    options_hi: ["<p>इन्फ्रारेड विकिरण</p>", "<p>पराबैंगनी प्रकाश</p>",
                                "<p>एक्स-रे</p>", "<p>रेडियोधर्मिता</p>"],
                    solution_en: "<p>40.(d) Radioactivity was discovered by Henri Becquerel. The discovery of infrared radiation is ascribed to William Herschel. Ultraviolet light was discovered by Johann Wilhelm Ritter in 1801. Wilhelm Roentgen, discovered X-rays in 1895.</p>",
                    solution_hi: "<p>40.(d) रेडियोधर्मिता की खोज हेनरी बेकरेल ने की थी। अवरक्त विकिरण की खोज का श्रेय विलियम हर्शल को दिया जाता है। पराबैंगनी प्रकाश की खोज जोहान विल्हेम रिटर ने 1801 में की थी। विल्हेम रोएंटजेन ने 1895 में एक्स-रे की खोज की थी।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>