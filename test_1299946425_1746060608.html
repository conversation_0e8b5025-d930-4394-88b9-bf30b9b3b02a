<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Smart Test Interface</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        .question-nav{position:fixed;top:70px;right:0;height:calc(100vh - 70px);width:320px;transition:all .3s;z-index:100;display:flex;flex-direction:column}
        .question-content{margin-right:0;transition:all .3s}
        .question-box{width:40px;height:40px;margin:5px;cursor:pointer}
        .attempted{background-color:#3498db!important;color:white}
        .correct{background-color:#2ecc71!important;color:white}
        .incorrect{background-color:#e74c3c!important;color:white}
        .current{border:3px solid #f39c12!important}
        #question-boxes{overflow-y:auto;max-height:calc(100vh - 150px);padding-bottom:15px}
        @media (max-width:768px){.question-nav{width:100%;right:-100%}.question-content{margin-right:0}}
    </style>
</head>
<body>
    <div class="container-fluid p-0">
        <!-- Header with controls -->
        <nav class="navbar navbar-expand-lg navbar-dark bg-primary sticky-top">
            <div class="container-fluid">
                <a class="navbar-brand" href="#">
                    <i class="fas fa-graduation-cap me-2"></i>Smart Test
                </a>
                <!-- Section selector -->
                <div class="navbar-nav me-auto section-tabs">
                    <select class="form-select" id="section-selector" onchange="switchSection(this.value)">
                        <option value="all">All Sections</option>
                    </select>
                </div>
                <div class="d-flex align-items-center">
                    <!-- Timer -->
                    <div class="bg-light text-primary rounded px-3 py-2 me-2 fw-bold" id="timer">
                        <i class="far fa-clock me-2"></i><span id="timer-display">100:00</span>
                    </div>
                    <!-- Submit button -->
                    <button class="btn btn-success me-2" id="submit-btn" onclick="submitTest()">
                        <i class="fas fa-check me-1"></i> Submit
                    </button>
                    <!-- Language toggle -->
                    <button class="btn btn-light me-2" id="lang-btn" onclick="toggleLanguage()">
                        <i class="fas fa-language"></i>
                    </button>
                    <!-- Menu toggle for mobile -->
                    <button class="btn btn-light d-lg-none" id="menu-toggle" onclick="toggleMenu()">
                        <i class="fas fa-th"></i>
                    </button>
                </div>
            </div>
        </nav>
        <div class="row m-0">
            <!-- Main content area -->
            <div class="col p-0 question-content" id="question-content">
                <div class="container py-4">
                    <!-- Question navigation -->
                    <div class="d-flex justify-content-between mb-4">
                        <button class="btn btn-outline-primary" onclick="prevQuestion()">
                            <i class="fas fa-chevron-left me-1"></i> Previous
                        </button>
                        <div class="fw-bold" id="question-counter">Question 1 of 100</div>
                        <button class="btn btn-outline-primary" onclick="nextQuestion()">
                            Next <i class="fas fa-chevron-right ms-1"></i>
                        </button>
                    </div>
                    <!-- Questions will be displayed here -->
                    <div id="questions-container"></div>
                </div>
            </div>
            <!-- Question navigation sidebar -->
            <div class="bg-light question-nav" id="question-nav">
                <div class="p-3">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h5 class="m-0">Questions</h5>
                        <div>
                            <span class="badge bg-secondary me-1" id="attempted-count">0</span>
                            <span class="badge bg-secondary" id="total-count">100</span>
                            <button class="btn btn-sm btn-outline-secondary ms-2 d-lg-none" onclick="toggleMenu()">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    </div>
                    <div class="d-flex flex-wrap" id="question-boxes"></div>
                </div>
            </div>
        </div>
        <!-- Results modal -->
        <div class="modal fade" id="results-modal" tabindex="-1" aria-hidden="true">
            <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content">
                    <div class="modal-header bg-primary text-white">
                        <h5 class="modal-title">Test Results</h5>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <div class="row text-center">
                            <div class="col-6 mb-3">
                                <div class="p-3 bg-primary bg-opacity-10 rounded">
                                    <h3 id="score-value">0</h3>
                                    <div class="small text-muted">Score</div>
                                </div>
                            </div>
                            <div class="col-6 mb-3">
                                <div class="p-3 bg-success bg-opacity-10 rounded">
                                    <h3 id="correct-value">0</h3>
                                    <div class="small text-muted">Correct</div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="p-3 bg-danger bg-opacity-10 rounded">
                                    <h3 id="incorrect-value">0</h3>
                                    <div class="small text-muted">Incorrect</div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="p-3 bg-secondary bg-opacity-10 rounded">
                                    <h3 id="unattempted-value">0</h3>
                                    <div class="small text-muted">Unattempted</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-primary" onclick="reviewTest()">Review Test</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 100 * 60;
        let resultsModal;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            resultsModal = new bootstrap.Modal(document.getElementById('results-modal'));
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 768) {
                document.getElementById('question-content').style.marginRight = '320px';
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["6"] = {
                name: "Reasoning",
                start: 0,
                end: 23
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="6">Reasoning</option>`;
            sections["18"] = {
                name: "General Awareness",
                start: 24,
                end: 48
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="18">General Awareness</option>`;
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 49,
                end: 73
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["7"] = {
                name: "English",
                start: 74,
                end: 98
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="7">English</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 99,
                end: 99
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "6",
                    question_en: "<p>1. In a certain code language, \'CURVED\' is coded as \'FWSUCA\' and \'LONDON\' is coded as \'OQOCMK\'. How will \'SIMPLE\' be coded in the same language?</p>",
                    question_hi: "<p>1. एक निश्चित कूट भाषा में, &lsquo;CURVED&rsquo; को &lsquo;FWSUCA&rsquo; के रूप में कूटबद्ध किया जाता है और &lsquo;LONDON&rsquo; को &lsquo;OQOCMK&rsquo; के रूप में कूटबद्ध किया जाता है। उसी भाषा में &lsquo;SIMPLE&rsquo; को किस प्रकार कूटबद्ध किया जाएगा?</p>",
                    options_en: [
                        "<p>VKNOJB</p>",
                        "<p>VKNPJC</p>",
                        "<p>WLMOKB</p>",
                        "<p>WLNPJD</p>"
                    ],
                    options_hi: [
                        "<p>VKNOJB</p>",
                        "<p>VKNPJC</p>",
                        "<p>WLMOKB</p>",
                        "<p>WLNPJD</p>"
                    ],
                    solution_en: "<p>1.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743393551227.png\" alt=\"rId4\" width=\"151\" height=\"70\"> ,&nbsp; <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743393551336.png\" alt=\"rId5\" width=\"147\" height=\"69\"><br>Similarly,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743393551460.png\" alt=\"rId6\" width=\"151\" height=\"69\"></p>",
                    solution_hi: "<p>1.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743393551227.png\" alt=\"rId4\" width=\"151\" height=\"70\">,&nbsp; <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743393551336.png\" alt=\"rId5\" width=\"147\" height=\"69\"><br>इसी प्रकार,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743393551460.png\" alt=\"rId6\" width=\"151\" height=\"69\"><br><br></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "6",
                    question_en: "<p>2. Which of the following terms will replace the question mark (?) in the given series?<br>RLYV, PJWT, NHUR, ? , JDQN</p>",
                    question_hi: "<p>2. निम्नलिखित में से कौन-सा पद दी गई श्रृंखला में प्रश्न चिह्न (?) को प्रतिस्थापित करेगा?<br>RLYV, PJWT, NHUR, ? , JDQN</p>",
                    options_en: [
                        "<p>LFTP</p>",
                        "<p>LGTP</p>",
                        "<p>LGSP</p>",
                        "<p>LFSP</p>"
                    ],
                    options_hi: [
                        "<p>LFTP</p>",
                        "<p>LGTP</p>",
                        "<p>LGSP</p>",
                        "<p>LFSP</p>"
                    ],
                    solution_en: "<p>2.(d)<br><strong id=\"docs-internal-guid-5f554714-7fff-9662-e758-11be8924cb42\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXchshGiIbthufA4dIcXa_v3xckBAxUZWzxvyy071N-W4q4Ne25DXFyT5e6pfuZnDHovG18H0JCJvZQ9jGFctoIpKwPnQkBI5vNGQEzlLrYVBbxPA2Z62vlWr1fdbUzzBjlehyEhMg?key=hixn9kmtyLccMNO-H_-ItRyw\" width=\"391\" height=\"90\"></strong></p>",
                    solution_hi: "<p>2.(d)<br><strong id=\"docs-internal-guid-5f554714-7fff-9662-e758-11be8924cb42\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXchshGiIbthufA4dIcXa_v3xckBAxUZWzxvyy071N-W4q4Ne25DXFyT5e6pfuZnDHovG18H0JCJvZQ9jGFctoIpKwPnQkBI5vNGQEzlLrYVBbxPA2Z62vlWr1fdbUzzBjlehyEhMg?key=hixn9kmtyLccMNO-H_-ItRyw\" width=\"391\" height=\"90\"></strong></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "6",
                    question_en: "<p>3. Read the given statements and conclusions carefully. Assuming that the information given in the statements is true, even if it appears to be at variance with commonly known facts, decide which of the given conclusions logically follow(s) from the statements.<br><strong>Statements:</strong><br>Some sweaters are jackets.<br>Some jackets are pullovers.<br>No pullover is a glove.<br><strong>Conclusions:</strong><br>(I) No jacket is a glove.<br>(II) Some sweaters are pullovers.</p>",
                    question_hi: "<p>3. दिए गए कथनों और निष्कर्षों को ध्यानपूर्वक पढ़िए। यह मानते हुए कि कथनों में दी गई जानकारी सत्य है, भले ही यह सामान्यतः ज्ञात तथ्यों से भिन्न प्रतीत होती हो और निर्णय लीजिए कि दिए गए निष्कर्षों में से कौन-सा/कौन-से निष्कर्ष कथनों का तार्किक रूप से अनुसरण करता है/करते हैं।<br><strong>कथन :</strong><br>कुछ स्वेटर, जैकेटे हैं।<br>कुछ जैकेटे, पुलओवर हैं।<br>कोई भी पुलओवर, दस्ताना नहीं है।<br><strong>निष्कर्ष :</strong><br>(I) कोई भी जैकेट, दस्ताना नहीं है।<br>(II) कुछ स्वेटर, पुलओवर हैं।</p>",
                    options_en: [
                        "<p>Only conclusion I follows</p>",
                        "<p>Both conclusions I and II follow</p>",
                        "<p>None of the conclusions follow</p>",
                        "<p>Only conclusion II follows</p>"
                    ],
                    options_hi: [
                        "<p>केवल निष्कर्ष I अनुसरण करता है</p>",
                        "<p>दोनों निष्कर्ष I और II अनुसरण करते हैं</p>",
                        "<p>कोई भी निष्कर्ष अनुसरण नहीं करता है</p>",
                        "<p>केवल निष्कर्ष II अनुसरण करता है</p>"
                    ],
                    solution_en: "<p>3.(c) <br><strong id=\"docs-internal-guid-fe1b95fd-7fff-68e9-d1d2-a9b489874f28\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfKAYS_-Z2BQb4pJGDizjeYVkx5GhTC-8E043EHBdZvKvHtJQsWGe9-xNmzmPVuOsF6UE3xq_KdYW3dWK4n_onacGt4o_pWUB0FAXd8uzxaJeOHOPxW1hc8AvrQcfFQyb-0Jrs7?key=hixn9kmtyLccMNO-H_-ItRyw\" width=\"346\" height=\"58\"></strong><br>None of the conclusions follow.</p>",
                    solution_hi: "<p>3.(c) <br><strong id=\"docs-internal-guid-119b9d40-7fff-835a-fe20-da1e24f91c00\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXeO7WbPRxLxnymrQUag8XOVeAGXR07xF_ggUiRnhLoy9Q7RExdLFgfeqHPVf5IFZJOkA2KLN2gMak2BYwFJklzf1yQ35nMHxgO8XBgwb33BnnBmuvBlgrxTKtbDgvWDW1A9SmMT?key=hixn9kmtyLccMNO-H_-ItRyw\" width=\"322\" height=\"53\"></strong><br>कोई भी निष्कर्ष अनुसरण नहीं करता है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "6",
                    question_en: "<p>4. Six letters A, B, C, D, E and F are written on different faces of a dice. Three positions of this dice are shown in the figure. Find the letter on the face opposite to A.<br><strong id=\"docs-internal-guid-86f87472-7fff-0d0f-6a9e-60150d2e48de\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXf13Co0ms_VUDtdMseBIow-tEf5AISyHgc_tqdFTHv7Q-iy1QnEAdaH6geHoNy1I-96p_dryLVbc4oZPtbxrCFNVAw_17Tx4SxT8pGS5BdPHyvBkSUkuRgVR0F3FrHpQnBa1m2sXA?key=hixn9kmtyLccMNO-H_-ItRyw\" width=\"202\" height=\"78\"></strong></p>",
                    question_hi: "<p>4. एक पासे के विभिन्न फलकों पर छ: अक्षर A, B, C, D, E और F लिखे गए हैं। नीचे चित्र में इस पासे की तीन स्थितियाँ दिखाई गई है। अक्षर A के विपरीत फलक पर कौन-सा अक्षर है?<br><strong id=\"docs-internal-guid-86f87472-7fff-0d0f-6a9e-60150d2e48de\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXf13Co0ms_VUDtdMseBIow-tEf5AISyHgc_tqdFTHv7Q-iy1QnEAdaH6geHoNy1I-96p_dryLVbc4oZPtbxrCFNVAw_17Tx4SxT8pGS5BdPHyvBkSUkuRgVR0F3FrHpQnBa1m2sXA?key=hixn9kmtyLccMNO-H_-ItRyw\" width=\"202\" height=\"78\"></strong></p>",
                    options_en: [
                        "<p>C</p>",
                        "<p>D</p>",
                        "<p>E</p>",
                        "<p>F</p>"
                    ],
                    options_hi: [
                        "<p>C</p>",
                        "<p>D</p>",
                        "<p>E</p>",
                        "<p>F</p>"
                    ],
                    solution_en: "<p>4.(c) in the dice i and ii , letter (B, C) is common<br>So, the opposite of &lsquo;A&rsquo; is &lsquo;E&rsquo;.</p>",
                    solution_hi: "<p>4.(c) पासे i और ii में, अक्षर (B, C) उभयनिष्ठ है<br>तो, \'&lsquo;A&rsquo;\' का विपरीत &lsquo;E है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "6",
                    question_en: "<p>5. Q is the mother of T. R is the husband of P. S is the father of Q . T is the brother of P. How is P related to S?</p>",
                    question_hi: "<p>5. Q, T की माता है। R, P का पति है। S, Q का पिता है। T, P का भाई है। P, S से किस प्रकार संबंधित है?</p>",
                    options_en: [
                        "<p>Daughter&rsquo;s daughter</p>",
                        "<p>Mother&rsquo;s mother</p>",
                        "<p>Mother&rsquo;s father</p>",
                        "<p>Brother&rsquo;s wife</p>"
                    ],
                    options_hi: [
                        "<p>नातिन</p>",
                        "<p>नानी</p>",
                        "<p>नाना</p>",
                        "<p>भाभी</p>"
                    ],
                    solution_en: "<p>5.(a)<br><strong id=\"docs-internal-guid-fbcf677d-7fff-ec95-1dbd-aff4e1572b2e\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfUnSP-XRjjw3B0KMucixtGV1W6RZ7ppQoo6V8Hwg6jy6eMzbySOZjajUro90WbLIyLAOjR8DToKPOuMI1Md78M5O162upnUpW-PYTkZRsEr0q15L7qqD8d0WnV6lZuH82epL6UDw?key=hixn9kmtyLccMNO-H_-ItRyw\" width=\"177\" height=\"116\"></strong><br>Hence &lsquo;P&rsquo; is the daughter&rsquo;s daughter of &lsquo;S&rsquo;.</p>",
                    solution_hi: "<p>5.(a)<br><strong id=\"docs-internal-guid-fbcf677d-7fff-ec95-1dbd-aff4e1572b2e\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfUnSP-XRjjw3B0KMucixtGV1W6RZ7ppQoo6V8Hwg6jy6eMzbySOZjajUro90WbLIyLAOjR8DToKPOuMI1Md78M5O162upnUpW-PYTkZRsEr0q15L7qqD8d0WnV6lZuH82epL6UDw?key=hixn9kmtyLccMNO-H_-ItRyw\" width=\"177\" height=\"116\"></strong><br>अतः \'P\', \'S\' की नातिन है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "6",
                    question_en: "<p>6. Which of the following numbers will replace the question mark (?) in the given series? <br>22, 23, 27, 54, 70, 195, ?</p>",
                    question_hi: "<p>6. निम्नलिखित में से कौन सी संख्&zwj;या दी गई श्रृंखला में प्रश्&zwj;न चिह्न (?) का स्&zwj;थान लेगी?<br>22, 23, 27, 54, 70, 195, ?</p>",
                    options_en: [
                        "<p>196</p>",
                        "<p>230</p>",
                        "<p>200</p>",
                        "<p>231</p>"
                    ],
                    options_hi: [
                        "<p>196</p>",
                        "<p>230</p>",
                        "<p>200</p>",
                        "<p>231</p>"
                    ],
                    solution_en: "<p>6.(d)<br><strong id=\"docs-internal-guid-1e80b058-7fff-40b6-2878-ad66d71278a0\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfCMIKmLJBBntIf766antq3iZjz5zWtXITljz2bsDgCWcCbasEl6qA8lAQBkguTBYxZFXfe_uE64rL6mDsOyHXtuF2gtBzHjzoG-Cc5x6R9-M4yevhIMxEPTFHzEAaDEFheD0XB?key=hixn9kmtyLccMNO-H_-ItRyw\" width=\"357\" height=\"73\"></strong></p>",
                    solution_hi: "<p>6.(d)<br><strong id=\"docs-internal-guid-1e80b058-7fff-40b6-2878-ad66d71278a0\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfCMIKmLJBBntIf766antq3iZjz5zWtXITljz2bsDgCWcCbasEl6qA8lAQBkguTBYxZFXfe_uE64rL6mDsOyHXtuF2gtBzHjzoG-Cc5x6R9-M4yevhIMxEPTFHzEAaDEFheD0XB?key=hixn9kmtyLccMNO-H_-ItRyw\" width=\"357\" height=\"73\"></strong></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "6",
                    question_en: "<p>7. 22 is related to 176 following a certain logic. Following the same logic, 32 is related to 256. To which of the following is 48 related, following the same logic?<br>(NOTE: Operations should be performed on the whole numbers, without breaking down the numbers into its constituent digits. E.g. 13 &ndash; Operations on 13 such as adding /deleting /multiplying etc. to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is not allowed)</p>",
                    question_hi: "<p>7. एक निश्चित तर्क का अनुसरण करते हुए 22, 176 से संबंधित है। उसी तर्क का अनुसरण करते हुए, 32, 256 से संबंधित है। उसी तर्क का अनुसरण करते हुए, 48 निम्नलिखित में से किससे संबंधित है?<br>(नोट: संख्याओं को उसके घटक अंकों में तोड़े बिना, पूर्ण संख्याओं पर संक्रियाएं की जानी चाहिए। उदाहरण के लिए संख्या 13 को लें - 13 पर संक्रियाएं, जैसे कि 13 में जोड़ना/घटाना/गुणा करना आदि, की जा सकती हैं। 13 को 1 और 3 में तोड़ने और फिर 1 और 3 पर गणितीय संक्रियाएं करने की अनुमति नहीं है)</p>",
                    options_en: [
                        "<p>374</p>",
                        "<p>384</p>",
                        "<p>380</p>",
                        "<p>370</p>"
                    ],
                    options_hi: [
                        "<p>374</p>",
                        "<p>384</p>",
                        "<p>380</p>",
                        "<p>370</p>"
                    ],
                    solution_en: "<p>7.(b)<br><strong>Logic:-</strong> 1<sup>st</sup> no.&nbsp;&times;<strong> 8 = </strong>2<sup>nd </sup>no.<br>(22, 176) :- <strong>22 &times; 8 = 176</strong><br>(32, 256) :- 32 &times; <strong>8</strong> = 256<br>Similarly<br>(48, ?) :- 48 &times; <strong>8</strong> = 384</p>",
                    solution_hi: "<p>7.(b)<br><strong>तर्क:-</strong> पहली संख्या &times; <strong>8 =</strong> दूसरी संख्या <br>(22, 176) :- <strong>22 &times; 8 = 176</strong><br>(32, 256) :- 32 &times; <strong>8</strong> = 256<br>इसी प्रकार <br>(48, ?) :- 48 &times; <strong>8</strong> = 384</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "6",
                    question_en: "<p>8. Three of the following four are alike in a certain way and thus form a group. Which is the one that does NOT belong to that group? <br><strong>(Note</strong> <strong>:</strong> The odd one out is not based on the number of consonants/vowels or their position in the letter cluster.)</p>",
                    question_hi: "<p>8. निम्नलिखित चार अक्षर-समूहों में से तीन अक्षर-समूह एक निश्चित प्रकार से समान हैं और इस प्रकार एक समूह बनाते हैं। वह कौन-सा अक्षर-समूह है जो उस समूह से संबंधित नहीं है? <br>(<strong>ध्यान दें :</strong> असंगत अक्षर-समूह, उस अक्षर-समूह में व्यंजनों/स्वरों की संख्या या उनके स्थान पर आधारित नहीं है।)</p>",
                    options_en: [
                        "<p>JNP</p>",
                        "<p>HKM</p>",
                        "<p>CGI</p>",
                        "<p>RVX</p>"
                    ],
                    options_hi: [
                        "<p>JNP</p>",
                        "<p>HKM</p>",
                        "<p>CGI</p>",
                        "<p>RVX</p>"
                    ],
                    solution_en: "<p>8.(b)<br><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXf-iXCiqlZ0z48SRU5fuU9iysf3kmAeH5SZSCppWAj_8ocdAgiOl48pVh9BzV009bzAw5HhXCvPhSpCaPhd06TA4SRGpNq6UGja1-pKxFpkmSDtcAWutQ9SKEr2X4JLIVCPSm_f-Q?key=hixn9kmtyLccMNO-H_-ItRyw\" width=\"118\" height=\"75\">, <img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcUORtnWNi7ldzNc2iG0lrB8lzDvBdDqR1w70WvLTa7MsTPz5DcYgM8zbiUTJskAZ3k3im-IDzTfnoQTBDCvOlD_nuTO5s7Ofz8q9w3GqlqkHTXjEjdA-CrKu9dTCy1DowVbh2L?key=hixn9kmtyLccMNO-H_-ItRyw\" width=\"114\" height=\"79\">, <img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcfH4kLi9ffql6orfObzwk0rrvMDyXOaRMNgyLW3HnkWY7kqK3YV0STioVkPWEMiBZXkDPZCm5Szfaj6s46Om7iJEEnu1flveKYx3jbKVmtJmCMiZ7yktbGmNSsO4C6HeTaijIaHw?key=hixn9kmtyLccMNO-H_-ItRyw\" width=\"118\" height=\"83\"><br>But,<br><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXf_fbt-EiO7c3daWFVvju7L1QxWvNrdFugToGm8jOeRScHnQmucVlKPgIehCjoFGn6UJaSIbqkCtN30EaZVEYpzIFcIp97hsWEMXCjasUj4qKMfFH-au3SYC4kViK7JQiUCNRMooQ?key=hixn9kmtyLccMNO-H_-ItRyw\" width=\"101\" height=\"58\"></p>",
                    solution_hi: "<p>8.(b)<br><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXf-iXCiqlZ0z48SRU5fuU9iysf3kmAeH5SZSCppWAj_8ocdAgiOl48pVh9BzV009bzAw5HhXCvPhSpCaPhd06TA4SRGpNq6UGja1-pKxFpkmSDtcAWutQ9SKEr2X4JLIVCPSm_f-Q?key=hixn9kmtyLccMNO-H_-ItRyw\" width=\"118\" height=\"75\">, <img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcUORtnWNi7ldzNc2iG0lrB8lzDvBdDqR1w70WvLTa7MsTPz5DcYgM8zbiUTJskAZ3k3im-IDzTfnoQTBDCvOlD_nuTO5s7Ofz8q9w3GqlqkHTXjEjdA-CrKu9dTCy1DowVbh2L?key=hixn9kmtyLccMNO-H_-ItRyw\" width=\"114\" height=\"79\">, <img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcfH4kLi9ffql6orfObzwk0rrvMDyXOaRMNgyLW3HnkWY7kqK3YV0STioVkPWEMiBZXkDPZCm5Szfaj6s46Om7iJEEnu1flveKYx3jbKVmtJmCMiZ7yktbGmNSsO4C6HeTaijIaHw?key=hixn9kmtyLccMNO-H_-ItRyw\" width=\"118\" height=\"83\"><br>लेकिन,<br><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXf_fbt-EiO7c3daWFVvju7L1QxWvNrdFugToGm8jOeRScHnQmucVlKPgIehCjoFGn6UJaSIbqkCtN30EaZVEYpzIFcIp97hsWEMXCjasUj4qKMfFH-au3SYC4kViK7JQiUCNRMooQ?key=hixn9kmtyLccMNO-H_-ItRyw\" width=\"101\" height=\"58\"></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "6",
                    question_en: "<p>9. In a certain code language,<br>&lsquo;A + B&rsquo; means &lsquo;A is the mother of B&rsquo;;<br>&lsquo;A &minus; B&rsquo; means &lsquo;A is the brother of B&rsquo;;<br>&lsquo;A &times; B&rsquo; means &lsquo;A is the sister of B&rsquo; and<br>&lsquo;A &divide; B&rsquo; means &lsquo;A is the daughter of B&rsquo;.<br>Based on the above, if &lsquo;J + K &divide; L &minus; T &times; R&rsquo;, then how is J related to L?</p>",
                    question_hi: "<p>9. एक निश्चित कूट भाषा में,<br>&lsquo;A + B&rsquo; का अर्थ है \'A, B की मां है\';<br>&lsquo;A &ndash; B&rsquo; का अर्थ है \'A, B का भाई है\';<br>&lsquo;A &times; B&rsquo; का अर्थ है \'A, B की बहन है\' और<br>&lsquo;A &divide; B&rsquo; का अर्थ है \'A, B की पुत्री है\'।<br>उपरोक्त के आधार पर, यदि &lsquo;J + K &divide; L &minus; T &times; R&rsquo; है, तो J का L से क्&zwj;या संबंध है?</p>",
                    options_en: [
                        "<p>Wife</p>",
                        "<p>Sister</p>",
                        "<p>Husband&rsquo;s sister</p>",
                        "<p>Father&rsquo;s sister</p>"
                    ],
                    options_hi: [
                        "<p>पत्&zwj;नी</p>",
                        "<p>बहन</p>",
                        "<p>पति की बहन (ननद)</p>",
                        "<p>पिता की बहन (बुआ)</p>"
                    ],
                    solution_en: "<p>9.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743393553302.png\" alt=\"rId17\" width=\"233\" height=\"104\"><br>&lsquo;J&rsquo; is the wife of &lsquo;L&rsquo;.</p>",
                    solution_hi: "<p>9.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743393553302.png\" alt=\"rId17\" width=\"233\" height=\"104\"><br>\'J\', \'L\' की पत्नी है</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "6",
                    question_en: "<p>10. What will come in the place of the question mark (?) in the following equation, if + and &lsquo;&times;&rsquo; are interchanged and \'-\' and &lsquo;&divide;&rsquo; are interchanged ?<br>35 &times; 16 + 3 &divide; 156 - 12 = ?</p>",
                    question_hi: "<p>10. यदि \'+\' और \'&times;\' को आपस में बदल दिया जाए तथा &lsquo;-&rsquo; और &lsquo;&divide;\' को आपस में बदल दिया जाए तो निम्नलिखित समीकरण में (?) के स्थान पर क्या आएगा?<br>35 &times; 16 + 3 &divide; 156 - 12 = ?</p>",
                    options_en: [
                        "<p>63</p>",
                        "<p>70</p>",
                        "<p>72</p>",
                        "<p>64</p>"
                    ],
                    options_hi: [
                        "<p>63</p>",
                        "<p>70</p>",
                        "<p>72</p>",
                        "<p>64</p>"
                    ],
                    solution_en: "<p>10.(b) <strong>Given</strong> :- 35 &times; 16 + 3 <math display=\"inline\"><mo>&#247;</mo></math> 156 - 12<br>As per given instruction after interchanging &lsquo;+&rsquo; and &lsquo;&times;&rsquo; and &lsquo;-&rsquo; and &lsquo;<math display=\"inline\"><mo>&#247;</mo></math>&rsquo; we get<br>35 + 16 &times; 3 - 156 <math display=\"inline\"><mo>&#247;</mo></math> 12<br>35 + 48 - 13<br>35 + 35 = 70</p>",
                    solution_hi: "<p>10.(b) <strong>दिया गया :- </strong>35 &times; 16 + 3 <math display=\"inline\"><mo>&#247;</mo></math> 156 - 12<br>दिए गए निर्देश के अनुसार \'+\' और \'&times;\' तथा \'-\' और \'<math display=\"inline\"><mo>&#247;</mo></math>\' को आपस में बदलने पर हमें प्राप्त होता है<br>35 + 16 &times; 3 - 156 <math display=\"inline\"><mo>&#247;</mo></math> 12<br>35 + 48 - 13<br>35 + 35 = 70</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "6",
                    question_en: "<p>11. Each of the letters in the word &lsquo;SLANTED&rsquo; is arranged in the English alphabetical order. How many letters are there in the English alphabetical series between the letter which is fourth from the left end and the one which is second from the right end in the new letter-cluster thus formed ?</p>",
                    question_hi: "<p>11. शब्द \'SLANTED\' के प्रत्येक अक्षर को अँग्रेजी वर्णमाला क्रम में व्यवस्थित किया जाता है। इस प्रकार बने नए अक्षर-समूह में बाएँ छोर से चौथे अक्षर और दाएँ छोर से दूसरे अक्षर के बीच अँग्रेजी वर्णमाला शृंखला में कितने अक्षर हैं?</p>",
                    options_en: [
                        "<p>Six</p>",
                        "<p>Eight</p>",
                        "<p>Seven</p>",
                        "<p>Five</p>"
                    ],
                    options_hi: [
                        "<p>छ:</p>",
                        "<p>आठ</p>",
                        "<p>सात</p>",
                        "<p>पाँच</p>"
                    ],
                    solution_en: "<p>11.(a) <strong>Given :</strong> SLANTED<br>After rearranging in alphabetical order - ADELNST<br>The letter fourth from the left is &lsquo;L&rsquo; and second from right end is &lsquo;S&rsquo;<br>Number of letters between &lsquo;L and &lsquo;S&rsquo; in english alphabet = six</p>",
                    solution_hi: "<p>11.(a) <strong>दिया गया :&nbsp;</strong>SLANTED<br>वर्णमाला क्रम में पुनर्व्यवस्थित करने के बाद - ADELNST<br>बायीं ओर से चौथा अक्षर \'L\' है और दायें छोर से दूसरा अक्षर \'S\' है<br>\'L और \'S\' के बीच अंग्रेजी वर्णमाला में अक्षर = छः</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "6",
                    question_en: "<p>12. Identify the figure from among the given options which when put in place of the question mark (?) will logically complete the series.<br><strong id=\"docs-internal-guid-947c4808-7fff-536e-6b3b-fc16fe84fb3a\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdB7yRJpicBkPLlCDnrIqJH4Df-rM_2H-W_wb_dFQPSNkgr03jvrrODbY47CWOiDbidDVAjY1Ml7P1n51rme8-t-m8OmrZz2mqKrpOBkfGz-hDxs_HW1gvY1IShvyVber6WUGL66g?key=hixn9kmtyLccMNO-H_-ItRyw\" width=\"308\" height=\"64\"></strong></p>",
                    question_hi: "<p>12. दिए गए विकल्पों में से उस आकृति की पहचान कीजिए जिसे प्रश्न चिह्न (?) के स्थान पर रखने पर शृंखला तार्किक रूप से पूरी हो जाएगी।<br><strong id=\"docs-internal-guid-0c9d40f5-7fff-2559-09e2-23b5828860ef\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdB7yRJpicBkPLlCDnrIqJH4Df-rM_2H-W_wb_dFQPSNkgr03jvrrODbY47CWOiDbidDVAjY1Ml7P1n51rme8-t-m8OmrZz2mqKrpOBkfGz-hDxs_HW1gvY1IShvyVber6WUGL66g?key=hixn9kmtyLccMNO-H_-ItRyw\" width=\"308\" height=\"64\"></strong></p>",
                    options_en: [
                        "<p><strong id=\"docs-internal-guid-37113b92-7fff-8c7d-a1a0-a688e8a200cb\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcSpmIRppGUh0mcIUy9z87ZW_85Ux0TEiIM4asZ-FooqgvKHSGo6wYI_M420PiTqF9nUnNgp6KYxuA_KzYTZqBTCdujgzxUE6z-kLVYBK4SI4MY0YCnsq4eb22z7RhIbTq3l42lcA?key=hixn9kmtyLccMNO-H_-ItRyw\" width=\"59\" height=\"60\"></strong></p>",
                        "<p><strong id=\"docs-internal-guid-fabe111d-7fff-bbba-d288-e86ca2c32c3a\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdIOgIrHD4Hny9s6c3uJ0SYqZJuHpSGLhC2lNm_LLNla0IGumLeSPoAA0jQ43t1DiG3te06gKPnShXnjPduC4xjXrkUGZpEdbWA9rU84HlXfokmv7aFuQU-VJkZKQTOADrwP13p9w?key=hixn9kmtyLccMNO-H_-ItRyw\" width=\"58\" height=\"60\"></strong></p>",
                        "<p><strong id=\"docs-internal-guid-8c57a45e-7fff-e72f-5ebb-153bc294dc4c\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcAc2AGuxCh7z5JNMp3psQmqjTJk5wxTQrBr9IL8NXfOdD5BvgoEGdJaxpFYZmq7u4cGBcyI7Ihz4bYSv2KAqFxiS2-zULFtWCj8afwCP3utRLdKgmLr1mnYBj4I0YReYaU03IokQ?key=hixn9kmtyLccMNO-H_-ItRyw\" width=\"59\" height=\"60\"></strong></p>",
                        "<p><strong id=\"docs-internal-guid-ad64ad16-7fff-04d5-1610-b3a07607a4c7\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXe0ebGb0u43mQ2YXovhKvMNTicCybMXB3jSux4MJxkevcqdVEqZP7p0-TuQNNyqNJwwsSRU9z8URy2LZPqeJFib7Sz5kmw82x0rPR1IkQxWuFvoLtnY57V1LmgWOjZFUt9NDuIPYg?key=hixn9kmtyLccMNO-H_-ItRyw\" width=\"59\" height=\"60\"></strong></p>"
                    ],
                    options_hi: [
                        "<p><strong id=\"docs-internal-guid-159bca90-7fff-5abb-960b-64bfde7a1137\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcSpmIRppGUh0mcIUy9z87ZW_85Ux0TEiIM4asZ-FooqgvKHSGo6wYI_M420PiTqF9nUnNgp6KYxuA_KzYTZqBTCdujgzxUE6z-kLVYBK4SI4MY0YCnsq4eb22z7RhIbTq3l42lcA?key=hixn9kmtyLccMNO-H_-ItRyw\" width=\"59\" height=\"60\"></strong></p>",
                        "<p><strong id=\"docs-internal-guid-9cf8cd6f-7fff-8549-d91a-49e4f082d568\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdIOgIrHD4Hny9s6c3uJ0SYqZJuHpSGLhC2lNm_LLNla0IGumLeSPoAA0jQ43t1DiG3te06gKPnShXnjPduC4xjXrkUGZpEdbWA9rU84HlXfokmv7aFuQU-VJkZKQTOADrwP13p9w?key=hixn9kmtyLccMNO-H_-ItRyw\" width=\"58\" height=\"60\"></strong></p>",
                        "<p><strong id=\"docs-internal-guid-9aaa09d1-7fff-a31a-7294-ef3d909d342e\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcAc2AGuxCh7z5JNMp3psQmqjTJk5wxTQrBr9IL8NXfOdD5BvgoEGdJaxpFYZmq7u4cGBcyI7Ihz4bYSv2KAqFxiS2-zULFtWCj8afwCP3utRLdKgmLr1mnYBj4I0YReYaU03IokQ?key=hixn9kmtyLccMNO-H_-ItRyw\" width=\"59\" height=\"60\"></strong></p>",
                        "<p><strong id=\"docs-internal-guid-b16b245d-7fff-c147-2754-a6b3c0a19572\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXe0ebGb0u43mQ2YXovhKvMNTicCybMXB3jSux4MJxkevcqdVEqZP7p0-TuQNNyqNJwwsSRU9z8URy2LZPqeJFib7Sz5kmw82x0rPR1IkQxWuFvoLtnY57V1LmgWOjZFUt9NDuIPYg?key=hixn9kmtyLccMNO-H_-ItRyw\" width=\"59\" height=\"60\"></strong></p>"
                    ],
                    solution_en: "<p>12.(b)<br><strong id=\"docs-internal-guid-1a06a939-7fff-e476-81c8-a1b0c8fce3ce\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXeR6Nguc37WKkMNgImNuFPVqVXpSVeFd4lLDuvNKxX3RNCU4wtQvbCAEcesaMbbgAtZXLY45HKWrmxtdBN9ZyLnRaRekTl3v1WnXmH958-8dTkgjLfJGnz3_tnKEA_4B2I1ujUE8g?key=hixn9kmtyLccMNO-H_-ItRyw\" width=\"59\" height=\"60\"></strong></p>",
                    solution_hi: "<p>12.(b)<br><strong id=\"docs-internal-guid-1a06a939-7fff-e476-81c8-a1b0c8fce3ce\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXeR6Nguc37WKkMNgImNuFPVqVXpSVeFd4lLDuvNKxX3RNCU4wtQvbCAEcesaMbbgAtZXLY45HKWrmxtdBN9ZyLnRaRekTl3v1WnXmH958-8dTkgjLfJGnz3_tnKEA_4B2I1ujUE8g?key=hixn9kmtyLccMNO-H_-ItRyw\" width=\"59\" height=\"60\"></strong></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "6",
                    question_en: "<p>13. How many triangles are there in the given figure? <br><strong id=\"docs-internal-guid-8f39f962-7fff-8e1f-2564-8be354a56722\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdvPiT6KWPs8Qg0DJB0UAfSHQ09x2VvebsNJREMFR5hixGEt5L88o7DyfpLv1qhBpQfpBNl3R2a032WGCyUxD6pk77R1i6y1EowDkFB7OWrYkjBeIgYv1Vr1yQM08swUJHorcJr-g?key=hixn9kmtyLccMNO-H_-ItRyw\" width=\"112\" height=\"86\"></strong></p>",
                    question_hi: "<p>13. दी गई आकृति में कितने त्रिभुज हैं?<br><strong id=\"docs-internal-guid-8f39f962-7fff-8e1f-2564-8be354a56722\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdvPiT6KWPs8Qg0DJB0UAfSHQ09x2VvebsNJREMFR5hixGEt5L88o7DyfpLv1qhBpQfpBNl3R2a032WGCyUxD6pk77R1i6y1EowDkFB7OWrYkjBeIgYv1Vr1yQM08swUJHorcJr-g?key=hixn9kmtyLccMNO-H_-ItRyw\" width=\"112\" height=\"86\"></strong></p>",
                    options_en: [
                        "<p>10</p>",
                        "<p>11</p>",
                        "<p>17</p>",
                        "<p>12</p>"
                    ],
                    options_hi: [
                        "<p>10</p>",
                        "<p>11</p>",
                        "<p>17</p>",
                        "<p>12</p>"
                    ],
                    solution_en: "<p>13.(c)<br><strong id=\"docs-internal-guid-ba296e37-7fff-fc41-51b1-cc6690e48643\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXci2DceTYh7RdK_k5WZTk5fV2nQY8OgBbxOv8V5_H9siy9CNQsvOllRzalKPBjLnkdaO_Y2Awx_9FlTdtcUd761NC9nk3WFoKz3LqIPNJSygv_al_7TdMt7X2zRCE4izlOh4b4Rrw?key=hixn9kmtyLccMNO-H_-ItRyw\" width=\"155\" height=\"119\"></strong><br>There are total 17 triangle<br>ABE , BCM, CDH, CMH, BDH, BCH , MGH, BEH, CGH, BFE, BFH, EIJ, EFJ, FJK, FKH, HKL, BHK,</p>",
                    solution_hi: "<p>13.(c)<br><strong id=\"docs-internal-guid-ba296e37-7fff-fc41-51b1-cc6690e48643\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXci2DceTYh7RdK_k5WZTk5fV2nQY8OgBbxOv8V5_H9siy9CNQsvOllRzalKPBjLnkdaO_Y2Awx_9FlTdtcUd761NC9nk3WFoKz3LqIPNJSygv_al_7TdMt7X2zRCE4izlOh4b4Rrw?key=hixn9kmtyLccMNO-H_-ItRyw\" width=\"155\" height=\"119\"></strong><br>कुल 17 त्रिभुज हैं<br>ABE , BCM, CDH, CMH, BDH, BCH , MGH, BEH, CGH, BFE, BFH, EIJ, EFJ, FJK, FKH, HKL, BHK,</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "6",
                    question_en: "<p>14. Select the option in which the given figure X is embedded (i.e contains figure X in the same form).<br><strong id=\"docs-internal-guid-49eea10d-7fff-e11a-7fb6-f4ed49cfa7f1\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXd_aMHB3jqzUjQWg_c2n3Mf3xBoc_OmGvI0X6U7da16wPDSmG5_0dCt5IfR1VP_BcOG-9qXu1uYZeEq8FUTABPhEacunz8CnXBD8fHqcfh5rIiC-GTETa5dYdQJFQx-EXSVCaoaBA?key=hixn9kmtyLccMNO-H_-ItRyw\" width=\"89\" height=\"100\"></strong></p>",
                    question_hi: "<p>14. उस विकल्प का चयन कीजिए जिसमें दी गई आकृति x सन्निहित है (अर्थात आकृति x उसी रूप में समाहित है)।<br><strong id=\"docs-internal-guid-1c0ff3dc-7fff-bb2f-2633-38203efe1bdc\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXd_aMHB3jqzUjQWg_c2n3Mf3xBoc_OmGvI0X6U7da16wPDSmG5_0dCt5IfR1VP_BcOG-9qXu1uYZeEq8FUTABPhEacunz8CnXBD8fHqcfh5rIiC-GTETa5dYdQJFQx-EXSVCaoaBA?key=hixn9kmtyLccMNO-H_-ItRyw\" width=\"89\" height=\"100\"></strong></p>",
                    options_en: [
                        "<p><strong id=\"docs-internal-guid-0c7a6f07-7fff-0d30-4932-adf85c28f58e\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcCjUmhhqN2y3fGrzUr1XZ2opJ2hs-u74u1Wg_gI3f92NSnKyyNjQdyh9kmQu22Klq9uXwhLxinNYklER9r_LtKH4GODgJc09WgC6cvYGemFZAJLHId41D--_-6vPf3QqVc8CJwNg?key=hixn9kmtyLccMNO-H_-ItRyw\" width=\"76\" height=\"76\"></strong></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743393555742.png\" alt=\"rId27\" width=\"75\" height=\"71\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743393555945.png\" alt=\"rId28\" width=\"76\" height=\"75\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743393556148.png\" alt=\"rId29\" width=\"76\" height=\"77\"></p>"
                    ],
                    options_hi: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743393555535.png\" alt=\"rId26\" width=\"75\" height=\"74\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743393555742.png\" alt=\"rId27\" width=\"74\" height=\"70\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743393555945.png\" alt=\"rId28\" width=\"74\" height=\"73\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743393556148.png\" alt=\"rId29\" width=\"75\" height=\"76\"></p>"
                    ],
                    solution_en: "<p>14.(c)<br><strong id=\"docs-internal-guid-12152858-7fff-8c31-f4e2-fdf6607a9c41\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfmXQ3iP0tJK6Qt6BoWFkEnUziYnDpc6ONg7KpfdTOEcsOLglGDBLs1YbS9UN0NYSjRxnYYEsnpI8Poo66sFt9ukv4gLcxeNSzrRe2_AnIorcPQyBlv8XLb-dQa3UAw9PUD9aK4ZQ?key=hixn9kmtyLccMNO-H_-ItRyw\" width=\"75\" height=\"74\"></strong></p>",
                    solution_hi: "<p>14.(c)<br><strong id=\"docs-internal-guid-12152858-7fff-8c31-f4e2-fdf6607a9c41\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfmXQ3iP0tJK6Qt6BoWFkEnUziYnDpc6ONg7KpfdTOEcsOLglGDBLs1YbS9UN0NYSjRxnYYEsnpI8Poo66sFt9ukv4gLcxeNSzrRe2_AnIorcPQyBlv8XLb-dQa3UAw9PUD9aK4ZQ?key=hixn9kmtyLccMNO-H_-ItRyw\" width=\"75\" height=\"74\"></strong></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "6",
                    question_en: "<p>15. Select the correct mirror image of the given figure when the mirror is placed at MN as shown below. <br><strong id=\"docs-internal-guid-8a6a14b4-7fff-f40a-a897-4d56ef5970dd\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfUPuaroi9DWnutNxXep4FFXKah3MlmMwRwC-xHDdwykWcHe7ZI0FNHJ_G5ri6a8pG72JzSDnddPCxl3rcmTpSXSWTkwSmtcfFuRrU0WAWRtTbO9WqHJieKNX4Vkl313Z-5GHrYCQ?key=hixn9kmtyLccMNO-H_-ItRyw\" width=\"123\" height=\"110\"></strong></p>",
                    question_hi: "<p>15. जब दर्पण को नीचे दर्शाए गए अनुसार MN पर रखा जाता है तो दी गई आकृति के सही दर्पण प्रतिबिंब का चयन कीजिए। <br><strong id=\"docs-internal-guid-8a6a14b4-7fff-f40a-a897-4d56ef5970dd\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfUPuaroi9DWnutNxXep4FFXKah3MlmMwRwC-xHDdwykWcHe7ZI0FNHJ_G5ri6a8pG72JzSDnddPCxl3rcmTpSXSWTkwSmtcfFuRrU0WAWRtTbO9WqHJieKNX4Vkl313Z-5GHrYCQ?key=hixn9kmtyLccMNO-H_-ItRyw\" width=\"123\" height=\"110\"></strong></p>",
                    options_en: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743393556760.png\" alt=\"rId32\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743393556964.png\" alt=\"rId33\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743393557184.png\" alt=\"rId34\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743393557266.png\" alt=\"rId35\"></p>"
                    ],
                    options_hi: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743393556760.png\" alt=\"rId32\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743393556964.png\" alt=\"rId33\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743393557184.png\" alt=\"rId34\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743393557266.png\" alt=\"rId35\"></p>"
                    ],
                    solution_en: "<p>15.(d) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743393557266.png\" alt=\"rId35\"></p>",
                    solution_hi: "<p>15.(d) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743393557266.png\" alt=\"rId35\"></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "16",
                    section: "6",
                    question_en: "<p>16. In the following number-pairs, the second number is obtained by applying certain mathematical operations to the first number. Select the set in which the numbers are related in the same way as are the numbers of the following sets.<br>(NOTE : Operations should be performed on the whole numbers, without breaking down the numbers into its constituent digits. E.g. 13 &ndash; Operations on 13 such as adding /deleting /multiplying etc., to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is not allowed.)<br>(26, 114)<br>(47, 198)</p>",
                    question_hi: "<p>16. निम्नलिखित संख्या-युग्&zwj;मों में, पहली संख्या पर निश्चित गणितीय संक्रियाएँ करके दूसरी संख्या प्राप्त की जाती है। उस समुच्&zwj;चय का चयन कीजिए जिसमें संख्याएँ उसी प्रकार संबंधित हैं जिस प्रकार निम्नलिखित समुच्&zwj;चय की संख्याएँ संबंधित हैं।<br>(नोट: संख्याओं को उसके घटक अंकों में तोड़े बिना, पूर्ण संख्याओं पर संक्रियाएं की जानी चाहिए। उदाहरण के लिए संख्या 13 को लें - 13 पर संक्रियाएं, जैसे कि 13 में जोड़ना/घटाना/गुणा करना आदि, की जा सकती हैं। 13 को 1 और 3 में तोड़ने और फिर 1 और 3 पर गणितीय संक्रियाएं करने की अनुमति नहीं है।)<br>(26, 114)<br>(47, 198)</p>",
                    options_en: [
                        "<p>(25, 105)</p>",
                        "<p>(18, 74)</p>",
                        "<p>(22, 88)</p>",
                        "<p>(15, 70)</p>"
                    ],
                    options_hi: [
                        "<p>(25, 105)</p>",
                        "<p>(18, 74)</p>",
                        "<p>(22, 88)</p>",
                        "<p>(15, 70)</p>"
                    ],
                    solution_en: "<p>16.(d)<br><strong>Logic :-</strong> (1<sup>st</sup> no.&nbsp;&times; 4) + 10 = 2<sup>nd </sup>no.<br>(26, 114) :- (<strong>26</strong> &times; 4) + 10 &rArr; 104 + 10 = 114<br>(47, 198) :- (<strong>47</strong> &times; 4) + 10 &rArr; 188 + 10 = 198<br>similarly<br>(15, 70) :- (<strong>15</strong> &times; 4) + 10 &rArr; 60 + 10 = 70</p>",
                    solution_hi: "<p>16.(d)<br><strong>तर्क</strong> :- (पहला नंबर&nbsp;&times; 4) + 10 = दूसरा नंबर<br>(26, 114) :- (<strong>26</strong> &times; 4) + 10 &rArr; 104 + 10 = 114<br>(47, 198) :- (<strong>47</strong> &times; 4) + 10 &rArr; 188 + 10 = 198<br>इसी प्रकार,<br>(15, 70) :- (<strong>15</strong> &times; 4) + 10 &rArr; 60 + 10 = 70</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "17",
                    section: "6",
                    question_en: "<p>17. In a certain code language, &lsquo;YOUR&rsquo; is coded as &lsquo;5627&rsquo; and &lsquo;CROY&rsquo; is coded as &lsquo;2769&rsquo;. What is the code for &lsquo;C&rsquo; in the given code language?</p>",
                    question_hi: "<p>17. एक निश्चित कूट भाषा में, &lsquo;YOUR&rsquo; को &lsquo;5627&rsquo; लिखा जाता है और &lsquo;CROY&rsquo; को &lsquo;2769&rsquo; लिखा जाता है। उसी कूट भाषा में &lsquo;C&rsquo; को कैसे लिखा जाएगा?</p>",
                    options_en: [
                        "<p>6</p>",
                        "<p>7</p>",
                        "<p>9</p>",
                        "<p>2</p>"
                    ],
                    options_hi: [
                        "<p>6</p>",
                        "<p>7</p>",
                        "<p>9</p>",
                        "<p>2</p>"
                    ],
                    solution_en: "<p>17.(c) YOUR &rarr; 5627&hellip;&hellip;(i)<br>CROY &rarr; 2769&hellip;&hellip;(ii)<br>From (i) and (ii) &lsquo;YOR&rsquo; and &lsquo;267&rsquo; are common. The code of &lsquo;C&rsquo; = &lsquo;9&rsquo;.</p>",
                    solution_hi: "<p>17.(c) YOUR &rarr; 5627&hellip;&hellip;(i)<br>CROY &rarr; 2769&hellip;&hellip;(ii)<br>(i) और (ii) से \'YOR\' और \'267\' उभयनिष्ठ हैं। \'C\' का कोड = \'9\'.</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "18",
                    section: "6",
                    question_en: "<p>18. Select the option that represents the letters that, when placed from left to right in the blanks below, will complete the letter series.<br><strong>S _ Y A _ F I K M _ S U W</strong></p>",
                    question_hi: "<p>18. उस विकल्प का चयन कीजिए जो उन अक्षरों को निरूपित करता है जिन्हें नीचे रिक्त स्थानों में बाएं से दाएं रखे जाने पर अक्षर श्रृंखला पूरी हो जाएगी।<br><strong>S _ Y A _ F I K M _ S U W</strong></p>",
                    options_en: [
                        "<p>VCP</p>",
                        "<p>VBP</p>",
                        "<p>VCQ</p>",
                        "<p>UCP</p>"
                    ],
                    options_hi: [
                        "<p>VCP</p>",
                        "<p>VBP</p>",
                        "<p>VCQ</p>",
                        "<p>UCP</p>"
                    ],
                    solution_en: "<p>18.(a)<br><strong>Logic:-</strong> + 3, + 3,+ 2, + 2<br><strong id=\"docs-internal-guid-4a4de128-7fff-f076-39c6-0be9330de516\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdlQdX0L2Yit1WkLiOUQYLXWALq13pxqO1ZJE6XfjMUfr7Gjy2X0SB2VKpGvBgxghJ32emo_CtW_MotTYuBwlCwmDsvtl2f85p5g7b6JksEeSh23LcQcB-qL8ul6xtVNnZYJvN61A?key=hixn9kmtyLccMNO-H_-ItRyw\" width=\"374\" height=\"37\"></strong></p>",
                    solution_hi: "<p>18.(a)<br><strong>तर्क</strong> :- + 3, + 3,+ 2, + 2<br><strong id=\"docs-internal-guid-4a4de128-7fff-f076-39c6-0be9330de516\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdlQdX0L2Yit1WkLiOUQYLXWALq13pxqO1ZJE6XfjMUfr7Gjy2X0SB2VKpGvBgxghJ32emo_CtW_MotTYuBwlCwmDsvtl2f85p5g7b6JksEeSh23LcQcB-qL8ul6xtVNnZYJvN61A?key=hixn9kmtyLccMNO-H_-ItRyw\" width=\"374\" height=\"37\"></strong></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "19",
                    section: "6",
                    question_en: "<p>19. If &lsquo;A&rsquo; stands for &lsquo;&divide;&rsquo;, &lsquo;B&rsquo; stands for &lsquo;&times;&rsquo;, &lsquo;C&rsquo; stands for &lsquo;+&rsquo; and &lsquo;D&rsquo; stands for &lsquo;&ndash;&rsquo;, then the&nbsp;resultant of which of the following will be 428?</p>",
                    question_hi: "<p>19. यदि &lsquo;A&rsquo; का अर्थ &lsquo;&divide;&rsquo;, &lsquo;B&rsquo; का अर्थ &lsquo;&times;&rsquo;, &lsquo;C&rsquo; का अर्थ &lsquo;+&rsquo; और &lsquo;D&rsquo; का अर्थ &lsquo;&ndash;&rsquo; है, तो निम्नलिखित में से किसका परिणाम 428 होगा?</p>",
                    options_en: [
                        "<p>113 B 4 A 62 D 2 C 7</p>",
                        "<p>113 A 4 D 62 B 2 C 7</p>",
                        "<p>113 B 4 C 62 A 2 D 7</p>",
                        "<p>113 B 4 D 62 A 2 C 7</p>"
                    ],
                    options_hi: [
                        "<p>113 B 4 A 62 D 2 C 7</p>",
                        "<p>113 A 4 D 62 B 2 C 7</p>",
                        "<p>113 B 4 C 62 A 2 D 7</p>",
                        "<p>113 B 4 D 62 A 2 C 7</p>"
                    ],
                    solution_en: "<p>19.(d) After going through all the options, option d satisfies. <br>113 B 4 D 62 A 2 C 7 <br>As per given instruction after interchanging the letter with sign we get<br>113 &times; 4 - 62 <math display=\"inline\"><mo>&#247;</mo></math> 2 + 7<br>452 - 31 + 7 <br>452 - 24 = 428</p>",
                    solution_hi: "<p>19.(d) <br>सभी विकल्पों की जांच करने पर विकल्प d संतुष्ट करता है।<br>113 B 4 D 62 A 2 C 7 <br>दिए गए निर्देश के अनुसार अक्षर को चिन्ह से बदलने पर हमें प्राप्त होता है<br>113 &times; 4 - 62 <math display=\"inline\"><mo>&#247;</mo></math> 2 + 7<br>452 - 31 + 7 <br>452 - 24 = 428</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "20",
                    section: "6",
                    question_en: "<p>20. Identify the figure from among the given options which when put in place of the question mark (?) will logically complete the series.<br><strong id=\"docs-internal-guid-10c2c056-7fff-2801-cc68-f532429182e3\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfRIHaXqajGlJi73sbnG0lNVv9D1dGOqsA6Tirf0hpzpONWTTIyYHx26VhAwOEkF5cun9Xggfdpgoujb4Eo9roRnNq7Qf9YDNfQlIEcxrLgXGIBl-_zT_naBMt19WlCpwLgLjFvZg?key=hixn9kmtyLccMNO-H_-ItRyw\" width=\"287\" height=\"67\"></strong></p>",
                    question_hi: "<p>20. दिए गए विकल्पों में से उस आकृति की पहचान कीजिए जिसे प्रश्न चिह्न (?) के स्थान पर रखने पर शृंखला तार्किक रूप से पूरी हो जाएगी।<br><strong id=\"docs-internal-guid-10c2c056-7fff-2801-cc68-f532429182e3\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfRIHaXqajGlJi73sbnG0lNVv9D1dGOqsA6Tirf0hpzpONWTTIyYHx26VhAwOEkF5cun9Xggfdpgoujb4Eo9roRnNq7Qf9YDNfQlIEcxrLgXGIBl-_zT_naBMt19WlCpwLgLjFvZg?key=hixn9kmtyLccMNO-H_-ItRyw\" width=\"287\" height=\"67\"></strong></p>",
                    options_en: [
                        "<p><strong id=\"docs-internal-guid-e84e1538-7fff-72ab-530e-06ff3dc2512b\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXe3hvN2Nv7lfgteB3Ht_1oxLv7xU8yTpvV0No9TzA_Asluq4mqOcyctBy4BN1KWUD-icd_07POT_4fxY9os19x3HvUbottZWiyOswA4Vda1ZBfkq4fo7-V3YGcWvnyK_hmKYCmAeg?key=hixn9kmtyLccMNO-H_-ItRyw\" width=\"75\" height=\"75\"></strong></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743393557811.png\" alt=\"rId39\" width=\"74\" height=\"74\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743393557925.png\" alt=\"rId40\" width=\"76\" height=\"76\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743393558100.png\" alt=\"rId41\" width=\"74\" height=\"74\"></p>"
                    ],
                    options_hi: [
                        "<p><strong id=\"docs-internal-guid-e84e1538-7fff-72ab-530e-06ff3dc2512b\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXe3hvN2Nv7lfgteB3Ht_1oxLv7xU8yTpvV0No9TzA_Asluq4mqOcyctBy4BN1KWUD-icd_07POT_4fxY9os19x3HvUbottZWiyOswA4Vda1ZBfkq4fo7-V3YGcWvnyK_hmKYCmAeg?key=hixn9kmtyLccMNO-H_-ItRyw\" width=\"75\" height=\"75\"></strong></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743393557811.png\" alt=\"rId39\" width=\"74\" height=\"74\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743393557925.png\" alt=\"rId40\" width=\"76\" height=\"76\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743393558100.png\" alt=\"rId41\" width=\"74\" height=\"74\"></p>"
                    ],
                    solution_en: "<p>20.(c)<br><strong id=\"docs-internal-guid-6721a5aa-7fff-ca9d-432e-2002848af800\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXeZ-ODrbB_pwPjc55ezsuZ9vwHsbj8QLwxtDqz93mGmOYAvlhNoVt1ML23Dsw6lvEfX8JgD1Wo_DWipux8c4XO3WDf0I_59VOgOYDjiysY4FrXwdDbdv5XiBWRwtWoVzLmx9xv0QA?key=hixn9kmtyLccMNO-H_-ItRyw\" width=\"75\" height=\"75\"></strong></p>",
                    solution_hi: "<p>20.(c)<br><strong id=\"docs-internal-guid-6721a5aa-7fff-ca9d-432e-2002848af800\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXeZ-ODrbB_pwPjc55ezsuZ9vwHsbj8QLwxtDqz93mGmOYAvlhNoVt1ML23Dsw6lvEfX8JgD1Wo_DWipux8c4XO3WDf0I_59VOgOYDjiysY4FrXwdDbdv5XiBWRwtWoVzLmx9xv0QA?key=hixn9kmtyLccMNO-H_-ItRyw\" width=\"75\" height=\"75\"></strong></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "21",
                    section: "6",
                    question_en: "<p>21. Select the correct mirror image of the given figure when the mirror is placed at MN as shown below.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743393558220.png\" alt=\"rId42\" width=\"108\" height=\"124\"></p>",
                    question_hi: "<p>21. जब दर्पण को नीचे दर्शाए गए अनुसार MN रेखा पर रखा जाता है तो दी गई आकृति के सही दर्पण प्रतिबिंब का चयन कीजिए।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743393558220.png\" alt=\"rId42\" width=\"108\" height=\"124\"></p>",
                    options_en: [
                        "<p><strong id=\"docs-internal-guid-3a313f09-7fff-abed-065b-79b8f3d8d6a3\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdxBsmy52v5C3bDbUNxcAKpStTQARjfiNeMgZ2bgSypQ-jB_t7LAjE1YqKVtw3-pHKs8mwtKUbnI8lK0O49obRVLji2sps0O4P4akm9i40rO-WnUDhhT5h_uEeoOnaHnBQpdjVoSA?key=hixn9kmtyLccMNO-H_-ItRyw\" width=\"83\" height=\"77\"></strong></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743393558487.png\" alt=\"rId44\" width=\"83\" height=\"84\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743393558618.png\" alt=\"rId45\" width=\"83\" height=\"79\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743393558714.png\" alt=\"rId46\" width=\"83\" height=\"87\"></p>"
                    ],
                    options_hi: [
                        "<p><strong id=\"docs-internal-guid-3a313f09-7fff-abed-065b-79b8f3d8d6a3\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdxBsmy52v5C3bDbUNxcAKpStTQARjfiNeMgZ2bgSypQ-jB_t7LAjE1YqKVtw3-pHKs8mwtKUbnI8lK0O49obRVLji2sps0O4P4akm9i40rO-WnUDhhT5h_uEeoOnaHnBQpdjVoSA?key=hixn9kmtyLccMNO-H_-ItRyw\" width=\"83\" height=\"77\"></strong></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743393558487.png\" alt=\"rId44\" width=\"83\" height=\"84\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743393558618.png\" alt=\"rId45\" width=\"83\" height=\"79\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743393558714.png\" alt=\"rId46\" width=\"83\" height=\"87\"></p>"
                    ],
                    solution_en: "<p>21.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743393558714.png\" alt=\"rId46\" width=\"84\" height=\"88\"></p>",
                    solution_hi: "<p>21.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743393558714.png\" alt=\"rId46\" width=\"84\" height=\"88\"></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "22",
                    section: "6",
                    question_en: "<p>22. Select the option that is related to the fifth term in the same way as the second term is related to the first term and the fourth term is related to the third term.<br>EYES : FTFZ :: MIND : OENJ :: REST : ?</p>",
                    question_hi: "<p>22. उस विकल्प का चयन कीजिए जो पाँचवें पद से उसी प्रकार संबंधित है जैसे दूसरा पद पहले पद से और चौथा पद तीसरे पद से संबंधित है।<br>EYES : FTFZ :: MIND : OENJ :: REST : ?</p>",
                    options_en: [
                        "<p>STSF</p>",
                        "<p>TUFS</p>",
                        "<p>TUSF</p>",
                        "<p>STRE</p>"
                    ],
                    options_hi: [
                        "<p>STSF</p>",
                        "<p>TUFS</p>",
                        "<p>TUSF</p>",
                        "<p>STRE</p>"
                    ],
                    solution_en: "<p>22.(c)<br><strong id=\"docs-internal-guid-6d6e3cdb-7fff-176a-1265-6a9a324ab470\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfVmJ-PP_IbnUgYA3gYahh7tuZ4jwjhIrxXer9d5EXrfxBx_ECJCevvTwehkTKcTqwzZqRghkf_vO7MqNyQfxzzWE8rERpGRXYJMB9naD1YPZcb38z2OAyO4KhH9cP6C4excUtVHQ?key=hixn9kmtyLccMNO-H_-ItRyw\" width=\"432\" height=\"79\"></strong></p>",
                    solution_hi: "<p>22.(c)<br><strong id=\"docs-internal-guid-6d6e3cdb-7fff-176a-1265-6a9a324ab470\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfVmJ-PP_IbnUgYA3gYahh7tuZ4jwjhIrxXer9d5EXrfxBx_ECJCevvTwehkTKcTqwzZqRghkf_vO7MqNyQfxzzWE8rERpGRXYJMB9naD1YPZcb38z2OAyO4KhH9cP6C4excUtVHQ?key=hixn9kmtyLccMNO-H_-ItRyw\" width=\"432\" height=\"79\"></strong></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "23",
                    section: "6",
                    question_en: "<p>23. In a code language, \'SLEEK\' is coded as 37 and \'FOLD\' is coded as 25. How will \'YATCH\' be coded in the same language?</p>",
                    question_hi: "<p>23. एक कूट भाषा में \'SLEEK\' को 37 के रूप में और \'FOLD\' को 25 के रूप में कूटबद्ध किया जाता है। उसी भाषा में \'YATCH\' को किस प्रकार कूटबद्ध किया जाएगा?</p>",
                    options_en: [
                        "<p>40</p>",
                        "<p>48</p>",
                        "<p>52</p>",
                        "<p>42</p>"
                    ],
                    options_hi: [
                        "<p>40</p>",
                        "<p>48</p>",
                        "<p>52</p>",
                        "<p>42</p>"
                    ],
                    solution_en: "<p>23.(d) <strong>Logic</strong> :- (Sum of the place value of letters) - (Number of letter) &times; 3<br>SLEEK :- (19 + 12 + 5 + 5 + 11) - (5) &times; 3 &rArr; (52) - 15 = 37<br>FOLD :- (6 + 15 + 12 + 4) - (4) &times; 3 &rArr; (37) - (12) = 25<br><strong>Similarly,</strong><br>YATCH :- (25 + 1 + 20 + 3 + 8) - (5) &times; 3 &rArr; 57 - 15 = 42</p>",
                    solution_hi: "<p>23.(d) <strong>तर्क</strong> :- (अक्षरों के स्थानीय मान का योग) - (अक्षरों की संख्या) &times; 3<br>SLEEK :- (19 + 12 + 5 + 5 + 11) - (5) &times; 3 &rArr; (52) - 15 = 37<br>FOLD :- (6 + 15 + 12 + 4) - (4) &times; 3 &rArr; (37) - (12) = 25<br><strong>इसी प्रकार,</strong><br>YATCH :- (25 + 1 + 20 + 3 + 8) - (5) &times; 3 &rArr; 57 - 15 = 42</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "24",
                    section: "6",
                    question_en: "<p>24. Three statements are given followed by three conclusions numbered I, II and III. Assuming the statements to be true, even if they seem to be at variance with commonly known facts, decide which of the conclusions logically follow(s) from the statements. <br><strong>Statements:</strong><br>No card is a parcel. <br>All cards are envelopes. <br>Some envelopes are bags.<br><strong>Conclusions:</strong> <br>I. All envelopes can never be parcels. <br>II. No card is a bag. <br>III. At least some bags are cards.</p>",
                    question_hi: "<p>24. तीन कथन और उसके बाद तीन निष्कर्ष I, II और III दिए गए हैं। कथनों को सत्य मानते हुए, भले ही वे सामान्य रूप से ज्ञात तथ्यों से भिन्न प्रतीत होते हों, हों तय कीजिए कि कौन-सा/से निष्कर्ष इन कथनों का तार्किक रूप से अनुसरण करता है/करते हैं। <br><strong>कथन:</strong> <br>कोई भी कार्ड, पार्सल नहीं है। <br>सभी कार्ड, लिफाफे हैं। <br>कुछ लिफाफे, बैग हैं। <br><strong>निष्कर्ष:</strong> <br>I. सभी लिफाफे कभी भी पार्सल नहीं हो सकते। <br>II. कोई भी कार्ड, बैग नहीं है। <br>III. कम से कम कुछ बैग, कार्ड हैं।</p>",
                    options_en: [
                        "<p>Only I follows</p>",
                        "<p>Only II and III follow</p>",
                        "<p>Only II follows</p>",
                        "<p>Only I and III follow</p>"
                    ],
                    options_hi: [
                        "<p>केवल निष्&zwj;कर्ष I अनुसरण करता हैं।</p>",
                        "<p>केवल निष्&zwj;कर्ष II और III अनुसरण करते हैं।</p>",
                        "<p>केवल निष्&zwj;कर्ष II अनुसरण करता हैं।</p>",
                        "<p>केवल निष्&zwj;कर्ष I और III अनुसरण करते हैं।</p>"
                    ],
                    solution_en: "<p>24.(a)<br><strong id=\"docs-internal-guid-b3f72895-7fff-f3c3-c795-f83937ae29c3\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdFQX2bx-3oB5ivXp0NPIMHtTHgaqAcO2OSM_D78QrAQzV4q0ZRtjSkG5AlxtrSW79xQ0EsLx6a7l1NPA-VFGx7MtK_OFinFbWi8TndrEG_Y_gCP6lb0a5zPQCH06xPt6Xnete4bg?key=hixn9kmtyLccMNO-H_-ItRyw\" width=\"289\" height=\"81\"></strong><br>Only I follows.</p>",
                    solution_hi: "<p>24.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743393559306.png\" alt=\"rId49\" width=\"274\" height=\"77\"><br>केवल I अनुसरण करता है.</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "25",
                    section: "18",
                    question_en: "<p>25. The position of how many letters will remain unchanged if each of the letters in the word &lsquo;SIGHTED&rsquo; is arranged in the English alphabetical order?</p>",
                    question_hi: "<p>25. यदि शब्द \'SIGHTED\' के प्रत्येक अक्षर को अँग्रेजी वर्णमाला क्रम में व्यवस्थित किया जाए, तो कितने अक्षरों का स्थान अपरिवर्तित रहेगा?</p>",
                    options_en: [
                        "<p>None</p>",
                        "<p>One</p>",
                        "<p>Three</p>",
                        "<p>Two</p>"
                    ],
                    options_hi: [
                        "<p>कोई नहीं</p>",
                        "<p>एक</p>",
                        "<p>तीन</p>",
                        "<p>दो</p>"
                    ],
                    solution_en: "<p>25.(d) <br><strong id=\"docs-internal-guid-e0b4f2af-7fff-c301-92a5-034779ef769b\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXeYWX8x5q09UtjhoKPW6sjPjHZNBEM9cL1ukqdpmeS8vVVdJyQgMP2QwZ16xCtUA8tUQimzoJqh9ZVkZGmkr7xC9RNX78CQ9-2oEJ1eSWOKyAwOM3bn_Lc9i5Ym-eUkBdKGyM7v?key=hixn9kmtyLccMNO-H_-ItRyw\" width=\"190\" height=\"96\"></strong><br>The position of two letters will be unchanged.</p>",
                    solution_hi: "<p>25.(d) <br><strong id=\"docs-internal-guid-e0b4f2af-7fff-c301-92a5-034779ef769b\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXeYWX8x5q09UtjhoKPW6sjPjHZNBEM9cL1ukqdpmeS8vVVdJyQgMP2QwZ16xCtUA8tUQimzoJqh9ZVkZGmkr7xC9RNX78CQ9-2oEJ1eSWOKyAwOM3bn_Lc9i5Ym-eUkBdKGyM7v?key=hixn9kmtyLccMNO-H_-ItRyw\" width=\"190\" height=\"96\"></strong><br>दो अक्षरों की स्थिति अपरिवर्तित रहेगी.</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "26",
                    section: "18",
                    question_en: "<p>26. The property of rock or soil that indicates the ability of allowing fluids to flow through it is called:</p>",
                    question_hi: "<p>26. शैल या मृदा का वह गुण जो इनमें से तरल पदार्थों के प्रवाहित हो जाने की क्षमता को इंगित करता है, _____ कहलाता है?</p>",
                    options_en: [
                        "<p>Permeability</p>",
                        "<p>Plasticity</p>",
                        "<p>Stability</p>",
                        "<p>Density</p>"
                    ],
                    options_hi: [
                        "<p>पारगम्यता (Permeability)</p>",
                        "<p>सुघट्यता (Plasticity)</p>",
                        "<p>स्थायित्व (Stability)</p>",
                        "<p>सघनता (Density)</p>"
                    ],
                    solution_en: "<p>26.(a) <strong>Permeability.</strong> Plasticity is the property of a body that allows permanent deformation when a deforming force is applied without opposition. Density is the mass of a unit volume of a substance, with the formula d = M/V, where d is density, M is mass, and V is volume. Density is typically measured in grams per cubic centimeter. The SI unit for density is kilograms per cubic meter (kg/m<sup>3</sup>).</p>",
                    solution_hi: "<p>26.(a) <strong>पारगम्यता</strong> (Permeability)। सुघट्यता (Plasticity) किसी पिंड का वह गुण है जो बिना किसी विरोध के विरूपण बल लगाने पर स्थायी विरूपण की अनुमति प्रदान करता है। घनत्व किसी पदार्थ के इकाई आयतन का द्रव्यमान है, जिसका सूत्र d = M/V है, जहाँ d घनत्व है, M द्रव्यमान है, और V आयतन है। घनत्व को सामान्यतः ग्राम प्रति घन सेंटीमीटर में मापा जाता है। घनत्व का SI मात्रक किलोग्राम प्रति घन मीटर (kg/m<sup>3</sup>) है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "27",
                    section: "18",
                    question_en: "<p>27. Which ministry started the Pradhan Mantri Rojgar Protsahan Yojana?</p>",
                    question_hi: "<p>27. प्रधानमंत्री रोजगार प्रोत्साहन योजना को किस मंत्रालय ने शुरू किया था?</p>",
                    options_en: [
                        "<p>Ministry of Labour and Employment</p>",
                        "<p>Ministry of Home Affairs</p>",
                        "<p>Ministry of Corporate Affairs</p>",
                        "<p>Ministry of Rural Development</p>"
                    ],
                    options_hi: [
                        "<p>श्रम और रोजगार मंत्रालय</p>",
                        "<p>गृह मंत्रालय</p>",
                        "<p>कॉर्पोरेट कार्य मंत्रालय</p>",
                        "<p>ग्रामीण विकास मंत्रालय</p>"
                    ],
                    solution_en: "<p>27.(a) <strong>Ministry of Labour and Employment. </strong>Pradhan Mantri Rojgar Protsahan Yojana (PMRPY) was launched on 9th August, 2016 with the objective to incentivise employers for creation of employment. Under the scheme, Government of India is paying Employer&rsquo;s full contribution i.e. 12% towards Employees\' Provident Funds (EPF) and Employees\' Pension Scheme (EPS) both.</p>",
                    solution_hi: "<p>27.(a) <strong>श्रम एवं रोजगार मंत्रालय। </strong>प्रधानमंत्री रोजगार प्रोत्साहन योजना (PMRPY) 9 अगस्त, 2016 को शुरू की गई थी जिसका उद्देश्य रोजगार सृजन के लिए नियोक्ताओं को प्रोत्साहित करना था। इस योजना के तहत, भारत सरकार कर्मचारी भविष्य निधि (EPF) और कर्मचारी पेंशन योजना (EPS) दोनों के लिए नियोक्ता का पूरा योगदान यानी 12% का भुगतान कर रही है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "28",
                    section: "18",
                    question_en: "<p>28. Who was the Viceroy of India when the Indian Arms Act, 1878, was passed?</p>",
                    question_hi: "<p>28. भारतीय शस्त्र अधिनियम (Indian Arms Act), 1878 के पारित होने के समय भारत के वायसराय कौन थे?</p>",
                    options_en: [
                        "<p>Lord Curzon</p>",
                        "<p>Lord Ripon</p>",
                        "<p>Lord Dufferin</p>",
                        "<p>Lord Lytton</p>"
                    ],
                    options_hi: [
                        "<p>लॉर्ड कर्ज़न</p>",
                        "<p>लॉर्ड रिपन</p>",
                        "<p>लॉर्ड डफ़रिन</p>",
                        "<p>लॉर्ड लिटन</p>"
                    ],
                    solution_en: "<p>28.(d) <strong>Lord Lytton (1876-1880).</strong> Key events during his tenure include the Vernacular Press Act (1878), the Arms Act (1878), and the Second Afghan War (1878-80). In 1877, Queen Victoria assumed the title of \'Kaiser-i-Hind\' or Queen Empress of India. The Indian Arms Act of 1878 made it a criminal offense for Indians to carry arms without a license, though it did not apply to the British.</p>",
                    solution_hi: "<p>28.(d) <strong>लॉर्ड लिटन (1876-1880)।</strong> उनके कार्यकाल के दौरान प्रमुख घटनाओं में वर्नाक्यूलर प्रेस एक्ट (1878), शस्त्र अधिनियम (1878) और द्वितीय अफगान युद्ध (1878-80) शामिल हैं। 1877 में, महारानी विक्टोरिया ने \'केसर-ए-हिंद\' या भारत की महारानी की उपाधि धारण की थी। 1878 के भारतीय शस्त्र अधिनियम ने भारतीयों के लिए बिना लाइसेंस के हथियार रखना एक आपराधिक अपराध बना दिया, हालाँकि यह अधिनियम अंग्रेजों पर लागू नहीं था।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "29",
                    section: "18",
                    question_en: "<p>29. The loudness of sound is measured in which unit ?</p>",
                    question_hi: "<p>29. ध्वनि की प्रबलता किस इकाई में मापी जाती है ?</p>",
                    options_en: [
                        "<p>Decibel (dB)</p>",
                        "<p>Watt/sq . Metre (w / m<sup>2</sup>)</p>",
                        "<p>Hertz (Hz)</p>",
                        "<p>Sound intensity</p>"
                    ],
                    options_hi: [
                        "<p>डेसिबल (dB)</p>",
                        "<p>वाट/वर्ग मीटर (w / m<sup>2</sup>)</p>",
                        "<p>हर्ट्ज़ (Hz)</p>",
                        "<p>ध्वनि की तीव्रता</p>"
                    ],
                    solution_en: "<p>29.(a) <strong>Decibel (dB). </strong>Other physical quantities and their Units: Joule - Unit of energy in the International System of Units, Hertz (Hz) - Unit for measuring frequency, Watt - Power, Newton - Weight or Force, Ampere - Current, Ohm - Resistance.</p>",
                    solution_hi: "<p>29.(a) <strong>डेसिबल (dB)।</strong> अन्य भौतिक राशियाँ एवं उनके मात्रक : जूल - ऊर्जा का मात्रक (अंतर्राष्ट्रीय मात्रक प्रणाली में) , हर्ट्ज़ (Hz) - आवृत्ति मापने की इकाई, वाट - शक्ति, न्यूटन - भार या बल, एम्पीयर - धारा, ओम - प्रतिरोध।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "30",
                    section: "18",
                    question_en: "<p>30. Which of the following government schemes was launched to provide financial assistance to start-ups for proof of concept, prototype development, product trials, market entry and commercialisation on 19 April 2021?</p>",
                    question_hi: "<p>30. 19 अप्रैल 2021 को अवधारणा-प्रमाण , प्रोटोटाइप विकास, उत्पाद परीक्षण, बाजार में प्रवेश और व्यावसायीकरण के लिए स्टार्ट-अप को वित्तीय सहायता प्रदान करने के लिए निम्नलिखित में से कौन-सी सरकारी योजना शुरू की गई थी?</p>",
                    options_en: [
                        "<p>Start-up India Seed Fund Scheme (SISFS)</p>",
                        "<p>Pradhan Mantri Rozgar Yojana (PMRY)</p>",
                        "<p>Procurement and Marketing Support (PMS) Scheme</p>",
                        "<p>Pradhan Mantri MUDRA Yojana (PMMY)</p>"
                    ],
                    options_hi: [
                        "<p>स्टार्ट-अप इंडिया सीड फंड स्कीम (SISFS)</p>",
                        "<p>प्रधानमंत्री रोजगार योजना (PMRY)</p>",
                        "<p>खरीद और विपणन सहायता (PMS) योजना</p>",
                        "<p>प्रधानमंत्री मुद्रा योजना (PMMY)</p>"
                    ],
                    solution_en: "<p>30.(a) <strong>Start-up India Seed Fund Scheme (SISFS) </strong>aims to provide seed funding to startups at the initial stages. Pradhan Mantri Rozgar Yojana (1993) focuses on self-employment opportunities for unemployed youth. Procurement and Marketing Support (PMS) Scheme provides marketing assistance to micro and small enterprises. Pradhan Mantri MUDRA Yojana (2015) provides loans to micro-enterprises.</p>",
                    solution_hi: "<p>30.(a) <strong>स्टार्ट-अप इंडिया सीड फंड स्कीम (SISFS) </strong>का उद्देश्य स्टार्टअप को प्रारंभिक चरणों में बीज वित्तपोषण प्रदान करना है। प्रधानमंत्री रोजगार योजना (1993) बेरोजगार युवाओं के लिए स्वरोजगार के अवसरों पर केंद्रित है। खरीद और विपणन सहायता (PMS) योजना सूक्ष्म और लघु उद्यमों को विपणन सहायता प्रदान करती है। प्रधानमंत्री मुद्रा योजना (2015) सूक्ष्म उद्यमों को ऋण प्रदान करती है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "31",
                    section: "18",
                    question_en: "<p>31. Which among the following is considered the first experiment of Satyagraha by Gandhiji in India?</p>",
                    question_hi: "<p>31. निम्नलिखित में से किसे भारत में गांधीजी द्वारा सत्याग्रह का पहला प्रयोग माना जाता है?</p>",
                    options_en: [
                        "<p>Kheda</p>",
                        "<p>Champaran</p>",
                        "<p>Rowlatt Satyagraha</p>",
                        "<p>Ahmedabad</p>"
                    ],
                    options_hi: [
                        "<p>खेड़ा</p>",
                        "<p>चंपारण</p>",
                        "<p>रॉलट सत्याग्रह</p>",
                        "<p>अहमदाबाद</p>"
                    ],
                    solution_en: "<p>31.(b) <strong>Champaran</strong> (1917). It was launched to support indigo farmers in Bihar who were forced by British planters to grow indigo under oppressive conditions. Other Movements by Gandhiji: Kheda Satyagraha (1918) - Farmers in Gujarat were unable to pay taxes due to famine and crop failure. Gandhiji led a peaceful protest demanding tax relief, which was partly granted by the British. Ahmedabad Mill Strike (1918): Gandhiji mediated a dispute between mill workers and owners, demanding better wages and working conditions. The peaceful strike succeeded with the introduction of a wage hike.</p>",
                    solution_hi: "<p>31.(b) <strong>चंपारण (1917)।</strong> यह बिहार में नील की खेती करने वाले किसानों की मदद के लिए शुरू किया गया था, जिन्हें ब्रिटिश बागान मालिकों ने दमनकारी परिस्थितियों में नील की खेती करने के लिए मजबूर किया था। गांधीजी द्वारा किए गए अन्य आंदोलन: खेड़ा सत्याग्रह (1918) - गुजरात में किसान अकाल और फसल की विफलता के कारण करों का भुगतान करने में असमर्थ थे। गांधीजी ने कर राहत की मांग करते हुए एक शांतिपूर्ण विरोध प्रदर्शन का नेतृत्व किया, जिसे अंग्रेजों ने आंशिक रूप से प्रदान किया। अहमदाबाद मिल हड़ताल (1918): गांधीजी ने बेहतर मजदूरी और कार्य करने की स्थिति की मांग करते हुए मिल श्रमिकों और मालिकों के बीच विवाद में मध्यस्थता की। वेतन वृद्धि की शुरूआत के साथ शांतिपूर्ण हड़ताल सफल हुई।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "32",
                    section: "18",
                    question_en: "<p>32. How many carbon dioxide and water molecules will be there in the product side, if the following equation is made balanced?<br>C<sub>2</sub>H<sub>5</sub>OH + O<sub>2</sub> &rarr; CO<sub>2</sub> + H<sub>2</sub>O</p>",
                    question_hi: "<p>32. यदि निम्नलिखित समीकरण को संतुलित किया जाए, तो उत्पाद पक्ष में कार्बन डाइऑक्साइड और पानी के अणुओं की संख्या कितनी होगी?<br>C<sub>2</sub>H<sub>5</sub>OH + O<sub>2</sub> &rarr; CO<sub>2</sub> + H<sub>2</sub>O</p>",
                    options_en: [
                        "<p>3 and 6, respectively</p>",
                        "<p>1 and 1, respectively</p>",
                        "<p>2 and 3, respectively</p>",
                        "<p>3 and 2, respectively</p>"
                    ],
                    options_hi: [
                        "<p>क्रमशः 3 और 6</p>",
                        "<p>क्रमशः 1 और 1</p>",
                        "<p>क्रमशः 2 और 3</p>",
                        "<p>क्रमशः 3 और 2</p>"
                    ],
                    solution_en: "<p>32.(c) <strong>2 and 3, respectively.</strong> A chemical equation represents a chemical reaction, with reactants on the left and products on the right, using their respective chemical symbols. An equation is considered balanced when the number of each atom is equal on both sides, adhering to the law of conservation of mass. This law states that atoms of the reactants are rearranged to form products, while the total mass remains constant throughout the reaction.</p>",
                    solution_hi: "<p>32.(c) <strong>क्रमशः 2 और 3.</strong> रासायनिक समीकरण रासायनिक अभिक्रिया को दर्शाता है, जिसमें अभिकारक बायी ओर और उत्पाद दायी ओर होते हैं, जो उनके संबंधित रासायनिक प्रतीकों का उपयोग करते हैं। एक समीकरण को संतुलित माना जाता है जब द्रव्यमान के संरक्षण के नियम का पालन करते हुए, प्रत्येक परमाणु की संख्या दोनों ओर बराबर होती है। यह नियम बताता है कि अभिकारकों के परमाणुओं को उत्पाद बनाने के लिए पुनर्व्यवस्थित किया जाता है, जबकि कुल द्रव्यमान पूर्ण अभिक्रिया के दौरान स्थिर रहता है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "33",
                    section: "18",
                    question_en: "<p>33. The novel &lsquo;Raavan - Enemy of Aryavarta&rsquo; is written by whom among the following writers?</p>",
                    question_hi: "<p>33. उपन्यास \'रावण - एनिमी ऑफ आर्यावर्त (Raavan - Enemy of Aryavarta)\' निम्नलिखित में से किस लेखक/लेखिका द्वारा लिखा गया है ?</p>",
                    options_en: [
                        "<p>Kiran Desai</p>",
                        "<p>Amish Tripathi</p>",
                        "<p>Ruskin Bond</p>",
                        "<p>Amitav Ghosh</p>"
                    ],
                    options_hi: [
                        "<p>किरण देसाई</p>",
                        "<p>अमीश त्रिपाठी</p>",
                        "<p>रस्किन बॉन्ड</p>",
                        "<p>अमिताव घोष</p>"
                    ],
                    solution_en: "<p>33.(b) <strong>Amish Tripathi.</strong> He is an Indian author and diplomat, best known for &ldquo;The Shiva Trilogy&rdquo; and &ldquo;Ram Chandra&rdquo; Series. His Other Books: &ldquo;The Immortals of Meluha&rdquo;; &ldquo;The Secret of the Nagas&rdquo;, \"The Oath of the Vayuputras&rdquo;; &ldquo;Scion of Ikshvaku&rdquo;; &ldquo;Sita: Warrior of Mithila&rdquo;.</p>",
                    solution_hi: "<p>33.(b) <strong>अमीश त्रिपाठी। </strong>वे एक भारतीय लेखक एवं राजनयिक हैं, जिन्हें &ldquo;शिव त्रयी&rdquo; और &ldquo;राम चंद्र&rdquo; श्रृंखला के लिए जाना जाता है। उनकी अन्य पुस्तकें : &ldquo;द इम्मोर्टल्स ऑफ़ मेलुहा&rdquo;; &ldquo;द सीक्रेट ऑफ़ द नागाज़&rdquo;, &ldquo;द ओथ ऑफ़ द वायुपुत्र&rdquo;; &ldquo;स्कियन ऑफ़ इक्ष्वाकु&rdquo;; &ldquo;सीता: वॉरियर ऑफ़ मिथिला&rdquo;।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "34",
                    section: "18",
                    question_en: "<p>34. At what latitude does the easterly jet stream blow over peninsular India during the summer months?</p>",
                    question_hi: "<p>34. गर्मियों के महीनों में पूर्वी जेट धारा प्रायद्वीपीय भारत के ऊपर किस अक्षांश पर बहती है?</p>",
                    options_en: [
                        "<p>24&deg;N</p>",
                        "<p>30&deg;N</p>",
                        "<p>14&deg;N</p>",
                        "<p>28&deg;N</p>"
                    ],
                    options_hi: [
                        "<p>24&deg;N</p>",
                        "<p>30&deg;N</p>",
                        "<p>14&deg;N</p>",
                        "<p>28&deg;N</p>"
                    ],
                    solution_en: "<p>34.(c) <strong>14&deg;N.</strong> The Jet Stream is a geostrophic wind blowing horizontally through the upper layers of the troposphere, generally from west to east. The tropical easterly jet stream, located between 8 and 35 degrees north latitude, is connected to the southwest monsoon in India.</p>",
                    solution_hi: "<p>34.(c) <strong>14&deg;N.</strong> जेट स्ट्रीम एक भूस्थैतिक वायु है जो क्षोभमंडल की ऊपरी परतों पर पश्चिम से पूर्व की ओर से होकर क्षैतिज रूप से चलती है। उष्णकटिबंधीय पूर्वी जेट स्ट्रीम, जो 8 से 35 डिग्री उत्तरी अक्षांश के बीच स्थित है, भारत में दक्षिण-पश्चिम मानसून से जुड़ी हुई है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "35",
                    section: "18",
                    question_en: "<p>35. Grignard reagent is represented as:</p>",
                    question_hi: "<p>35. ग्रिग्नार्ड अभिकर्मक को निम्न रूप में दर्शाया जाता है:</p>",
                    options_en: [
                        "<p>CH<sub>3</sub>-Ca-F</p>",
                        "<p>CH<sub>3</sub>-Be-F</p>",
                        "<p>CH<sub>3</sub>-Mg-Cl</p>",
                        "<p>H-Mg-H</p>"
                    ],
                    options_hi: [
                        "<p>CH<sub>3</sub>-Ca-F</p>",
                        "<p>CH<sub>3</sub>-Be-F</p>",
                        "<p>CH<sub>3</sub>-Mg-Cl</p>",
                        "<p>H-Mg-H</p>"
                    ],
                    solution_en: "<p>35.(c) <strong>CH<sub>3</sub>-Mg-Cl.</strong> Grignard reagent : It is an organometallic compound with the general formula RMgX, where R is an alkyl or aryl group, Mg represents magnesium, and X is a halogen (Cl, Br, I). Grignard reagents are used in organic synthesis (addition, substitution); React with carbonyl compounds, acids, and esters; Useful for forming carbon-carbon bonds.</p>",
                    solution_hi: "<p>35.(c) <strong>CH<sub>3</sub>-Mg-Cl.</strong> ग्रिग्नार्ड अभिकर्मक: यह एक कार्बनिक धातु यौगिक है जिसका सामान्य सूत्र RMgX है, जहाँ R एक एल्काइल या एरिल समूह है, Mg मैग्नीशियम को दर्शाता है, और X एक हैलोजन (Cl, Br, I) है। ग्रिग्नार्ड अभिकर्मकों का उपयोग कार्बनिक संश्लेषण (योग, प्रतिस्थापन) में किया जाता है; कार्बोनिल यौगिकों, अम्लों तथा एस्टर के साथ अभिक्रिया करते हैं; कार्बन-कार्बन बंध बनाने के लिए उपयोगी होते हैं।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "36",
                    section: "18",
                    question_en: "<p>36. Who became the first Indian woman to be inducted into the ICC Hall of Fame?</p>",
                    question_hi: "<p>36. आईसीसी हॉल ऑफ फेम (ICC Hall of Fame) में शामिल होने वाली पहली भारतीय महिला कौन थी?</p>",
                    options_en: [
                        "<p>Purnima Rau</p>",
                        "<p>Diana Edulji</p>",
                        "<p>Mithali Raj</p>",
                        "<p>Jhulan Goswami</p>"
                    ],
                    options_hi: [
                        "<p>पूर्णिमा राऊ (Purnima Rau)</p>",
                        "<p>डायना एडुल्जी (Diana Edulji)</p>",
                        "<p>मिताली राज (Mithali Raj)</p>",
                        "<p>झूलनझू गोस्वामी (Jhulan Goswami)</p>"
                    ],
                    solution_en: "<p>36.(b) <strong>Diana Edulji,</strong> a former Indian cricketer, received the Arjuna Award in 1983 and the Padma Shri in 2002.The ICC Cricket Hall of Fame was launched on 2 January 2009 in association with the Federation of International Cricketers Associations (FICA), as part of the ICC\'s centenary year celebrations.</p>",
                    solution_hi: "<p>36.(b) <strong>डायना एडुल्जी,</strong> पूर्व भारतीय क्रिकेटर को 1983 में अर्जुन पुरस्कार और 2002 में पद्मश्री से सम्मानित किया गया था। आईसीसी क्रिकेट हॉल ऑफ फेम को आईसीसी के शताब्दी वर्ष समारोह के हिस्से के रूप में फेडरेशन ऑफ इंटरनेशनल क्रिकेटर्स एसोसिएशन (FICA) के सहयोग से 2 जनवरी 2009 को शुरू किया गया था।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "37",
                    section: "18",
                    question_en: "<p>37. Which of the following crops is NOT grown during the Zaid season in India?</p>",
                    question_hi: "<p>37. निम्नलिखित में से कौन-सी फसल भारत में जायद के मौसम में नहीं उगाई जाती है?</p>",
                    options_en: [
                        "<p>Rice</p>",
                        "<p>Cucumber</p>",
                        "<p>Watermelon</p>",
                        "<p>Muskmelon</p>"
                    ],
                    options_hi: [
                        "<p>चावल</p>",
                        "<p>खीरा</p>",
                        "<p>तरबूज</p>",
                        "<p>खरबूजा</p>"
                    ],
                    solution_en: "<p>37.(a) <strong>Rice</strong>. Kharif Crops, also known as monsoon crops, they are sown at the beginning of the rainy season. Examples include rice, maize, bajra, ragi, sorghum, soybean, groundnut, and cotton. Zaid Crops : Grown in the short season between Kharif and Rabi, examples include Pumpkin, Cucumber, Watermelon, and Bitter gourd. Rabi Crops : Sown at the end of the monsoon or beginning of winter, they are known as winter crops. Examples include Wheat, Mustard, Pulses, and Barley.</p>",
                    solution_hi: "<p>37.(a) <strong>चावल।</strong> खरीफ फसलें: इन्हें मानसून फसलें भी कहा जाता है,और इन्हें वर्षा ऋतु की शुरुआत में बोया जाता है। उदाहरणों में चावल, मक्का, बाजरा, रागी, ज्वार, सोयाबीन, मूंगफली और कपास शामिल हैं। जायद फसलें: खरीफ और रबी के बीच के छोटे मौसम में उगाई जाती हैं, उदाहरणों में कद्दू, खीरा, तरबूज और करेला शामिल हैं। रबी फसलें: इन्हें मानसून के अंत या सर्दियों की शुरुआत में बोया जाता है, इन्हें सर्दियों की फसल के रूप में जाना जाता है। उदाहरणों में गेहूं, सरसों, दालें और जौ शामिल हैं।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "38",
                    section: "18",
                    question_en: "<p>38. In a dihybrid cross between two heterozygous fruit flies with brown bodies and red eyes (BbEe X BbEe), what will be the probability of getting BBEE genotype?</p>",
                    question_hi: "<p>38. भूरे रंग के शरीर और लाल आंखों वाली दो विषम युग्मजी फल मक्खियों के बीच एक द्विसंकर क्रॉस (BbEe X BbEe) में, बीबीईई जीनोटाइप (BBEE genotype) प्राप्त करने की संभावना क्या होगी?</p>",
                    options_en: [
                        "<p>1/8</p>",
                        "<p>1/4</p>",
                        "<p>1/16</p>",
                        "<p>1/2</p>"
                    ],
                    options_hi: [
                        "<p>1/8</p>",
                        "<p>1/4</p>",
                        "<p>1/16</p>",
                        "<p>1/2</p>"
                    ],
                    solution_en: "<p>38.(c) <strong>1/16.</strong> Each parent can produce four types of gametes: BE, Be, bE, and be, with equal probability (1/4 each). To obtain the BBEE genotype, the offspring must receive the B and E alleles from both parents. The probability of getting BB from Bb &times; Bb cross is 1/4, and the probability of getting EE from Ee &times; Ee cross is also 1/4. Therefore, the combined probability of getting BBEE is (1/4) * (1/4) = 1/16.</p>",
                    solution_hi: "<p>38.(c) <strong>1/16.</strong> प्रत्येक माता-पिता चार प्रकार के युग्मक उत्पन्न कर सकते हैं: BE, Be, bE, और be, समान संभावना के साथ (प्रत्येक 1/4)। BBEE जीनोटाइप प्राप्त करने के लिए, संतान को दोनों माता-पिता से B और E एलील प्राप्त होना चाहिए। Bb &times; Bb क्रॉस से BB प्राप्त करने की संभावना 1/4 है, और Ee &times; Ee क्रॉस से EE प्राप्त करने की संभावना भी 1/4 है। इसलिए, BBEE प्राप्त करने की संयुक्त संभावना (1/4) * (1/4) = 1/16 है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "39",
                    section: "18",
                    question_en: "<p>39. &lsquo;Unladylike&rsquo; is a memoir by ________.</p>",
                    question_hi: "<p>39. अनलेडीलाइक (Unladylike) किसका संस्मरण है?</p>",
                    options_en: [
                        "<p>Aditi Mittal</p>",
                        "<p>Supriya Joshi</p>",
                        "<p>Aishwarya Mohanraj</p>",
                        "<p>Radhika Vaz</p>"
                    ],
                    options_hi: [
                        "<p>अदिति मित्तल</p>",
                        "<p>सुप्रिया जोशी</p>",
                        "<p>ऐश्वर्या मोहनराज</p>",
                        "<p>राधिका वाज़</p>"
                    ],
                    solution_en: "<p>39.(d) <strong>Radhika</strong> <strong>Vaz</strong>. She is an Indian comedian and writer born in Mumbai. She worked as an advertising executive in Chennai and holds a master&rsquo;s in advertising from Syracuse University, New York. Aditi Mittal is another Indian stand-up comedian, actress, and writer. Supriya Joshi is recognized as an Indian playback singer. Aishwarya Mohanraj is also a stand-up comedian and writer, contributing to the vibrant comedy scene.</p>",
                    solution_hi: "<p>39.(d) <strong>राधिका वाज़</strong> मुंबई में जन्मी एक भारतीय हास्य कलाकार और लेखिका हैं। उन्होंने चेन्नई में एक विज्ञापन कार्यकारी के रूप में कार्य किया और सिरैक्यूज़ विश्वविद्यालय, न्यूयॉर्क से विज्ञापन में मास्टर डिग्री प्राप्त की। अदिति मित्तल एक अन्य भारतीय स्टैंड-अप कॉमेडियन, अभिनेत्री और लेखिका हैं। सुप्रिया जोशी को भारतीय पार्श्व गायिका के रूप में जाना जाता है। ऐश्वर्या मोहनराज भी एक स्टैंड-अप कॉमेडियन और लेखिका हैं, जो जीवंत कॉमेडी दृश्य में योगदान देती हैं।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "40",
                    section: "18",
                    question_en: "<p>40. In January 2025, who set the record for the fastest century by an Indian in women&rsquo;s ODIs ?</p>",
                    question_hi: "<p>40. जनवरी 2025 में, महिला वनडे में किसी भारतीय द्वारा सबसे तेज शतक का रिकॉर्ड किसने बनाया?</p>",
                    options_en: [
                        "<p>Mithali Raj</p>",
                        "<p>Harmanpreet Kaur</p>",
                        "<p>Smriti Mandhana</p>",
                        "<p>Deepti Sharma</p>"
                    ],
                    options_hi: [
                        "<p>मिताली राज</p>",
                        "<p>हरमनप्रीत कौर</p>",
                        "<p>स्मृति मंधाना</p>",
                        "<p>दीप्ति शर्मा</p>"
                    ],
                    solution_en: "<p>40.(c) <strong>Smriti Mandhana</strong> broke Harmanpreet Kaur\'s record for the fastest hundred by an Indian in women\'s one-day international cricket. Smriti smashed her hundred in just 70 deliveries against Ireland in the final ODI of a three-match series in Rajkot.</p>",
                    solution_hi: "<p>40(c) <strong>स्मृति मंधाना</strong> ने महिला वनडे अंतरराष्ट्रीय क्रिकेट में किसी भारतीय द्वारा सबसे तेज शतक का हरमनप्रीत कौर का रिकॉर्ड तोड़ दिया। स्मृति ने राजकोट में तीन मैचों की सीरीज के अंतिम वनडे में आयरलैंड के खिलाफ सिर्फ 70 गेंदों में अपना शतक जड़ा।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "41",
                    section: "18",
                    question_en: "<p>41.Which of the following is NOT a feature of the Constitution of India?</p>",
                    question_hi: "<p>41. निम्नलिखित में से कौन-सी, भारतीय संविधान की विशेषता नहीं है?</p>",
                    options_en: [
                        "<p>Universal Adult Franchise</p>",
                        "<p>Independent Judiciary</p>",
                        "<p>Dual Citizenship</p>",
                        "<p>Blend of rigidity &amp; flexibility</p>"
                    ],
                    options_hi: [
                        "<p>सार्वभौमिक वयस्क मताधिकार</p>",
                        "<p>स्वतंत्र न्यायपालिका</p>",
                        "<p>दोहरी नागरिकता</p>",
                        "<p>कठोरता और लचीलेपन का मेल</p>"
                    ],
                    solution_en: "<p>41.(c) <strong>Dual Citizenship.</strong> India&rsquo;s commitment to the rule of law is grounded in the Constitution which establishes India as a &lsquo;Sovereign Socialist Secular Democratic Republic&rsquo; with a Parliamentary form of Government. Other key features: Federal system of governance between the Union and the States; Separation of powers between the three organs of the Government; free and fair elections; Equality before the law; A secular state that recognizes freedom of conscience and religion.</p>",
                    solution_hi: "<p>41.(c) <strong>दोहरी नागरिकता।</strong> भारत का कानून के शासन के प्रति प्रतिबद्धता संविधान में निहित है जो भारत को एक \'संप्रभु समाजवादी धर्मनिरपेक्ष लोकतांत्रिक गणराज्य\' बनाता है जिसमें संसदीय शासन प्रणाली है। अन्य प्रमुख विशेषताएँ: संघ और राज्यों के बीच शासन की संघीय प्रणाली; सरकार के तीनों अंगों के बीच शक्तियों का पृथक्करण; स्वतंत्र और निष्पक्ष चुनाव; कानून के समक्ष समानता; एक धर्मनिरपेक्ष राज्य जो अंतरात्मा और धर्म की स्वतंत्रता को मान्यता देता है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "42",
                    section: "18",
                    question_en: "<p>42. Which of the following statements most accurately describes the Cyclostomata&nbsp;group?</p>",
                    question_hi: "<p>42 निम्नलिखित में से कौन-सा कथन साइक्लोस्टोमेटा समूह (Cyclostomata group) का सबसे सटीक वर्णन करता है?</p>",
                    options_en: [
                        "<p>Their skin is covered with scales/plates and their hearts have only two chambers.</p>",
                        "<p>They are ectothermic animals having mucus glands in the skin, and a three-chambered heart.</p>",
                        "<p>They are warm-blooded, oviparous, bipedal, feathered, winged and toothless vertebrates.</p>",
                        "<p>They are characterised by having an elongated eel-like body, circular mouth, slimy skin and are scaleless.</p>"
                    ],
                    options_hi: [
                        "<p>उनकी त्वचा शल्क /प्लेटों से ढकी होती है और उनके हृदयों में केवल दो कक्ष होते हैं।</p>",
                        "<p>वे बाह्ययोष्मी (ऍक्टोथर्म) जन्तु होते हैं जिनकी त्वचा में श्लेष्म ग्रंथियां होती हैं, और एक तीन-कक्षीय हृदय होता है।</p>",
                        "<p>वे गर्म रक्त वाले, अंडाकार, द्विपाद, पंख वाले, परों वाले और दांत रहित कशेरुक होते हैं।</p>",
                        "<p>वे एक लम्बे ईल जैसे शरीर, गोलाकार मुंह, पतली त्वचा वाले होते हैं और उनमें शल्क नहीं पाए जाते हैं।</p>"
                    ],
                    solution_en: "<p>42.(d) The Cyclostomata group is a subclass of jawless fishes. They are marine but migrate for spawning to fresh water. After spawning, within a few days, they die. Their larvae, after metamorphosis, return to the ocean. They have an elongated body bearing 6-15 pairs of gill slits for respiration. Examples: Petromyzon (Lamprey) and Myxine (Hagfish).</p>",
                    solution_hi: "<p>42.(d) साइक्लोस्टोमेटा समूह जबड़े रहित मछलियों का एक उपवर्ग है। यह समुद्री हैं, लेकिन अंडे देने के लिए ताजे पानी में पलायन करती हैं। जनन के कुछ दिनों के बाद, वे मर जाते हैं। उनके लार्वा, कायांतरण के बाद, समुद्र में वापस लौट जाते हैं। इनका शरीर लम्बा होता है, जिसमें श्वसन के लिए 6-15 जोड़े गिल स्लिट होते हैं। उदाहरण: पेट्रोमाइज़न (लैम्प्रे) और मिक्सीन (हैगफ़िश)।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "43",
                    section: "18",
                    question_en: "<p>43. Kojagiri Poornima is a festival celebrated primarily in _______ on the full moon day of Ashwin month.</p>",
                    question_hi: "<p>43. कोजागिरी पूर्णिमा एक त्यौहार है जो मुख्य रूप से ___________ में आश्विन माह की पूर्णिमा के दिन मनाया जाता है।</p>",
                    options_en: [
                        "<p>Maharashtra</p>",
                        "<p>Kerala</p>",
                        "<p>Andhra Pradesh</p>",
                        "<p>Bihar</p>"
                    ],
                    options_hi: [
                        "<p>महाराष्ट्र</p>",
                        "<p>केरल</p>",
                        "<p>आंध्र प्रदेश</p>",
                        "<p>बिहार</p>"
                    ],
                    solution_en: "<p>43.(a) <strong>Maharashtra.</strong> Sharad Purnima is also known as Kojagiri or Raas Purnima. It is a harvest festival. Other festivals of Maharashtra : Gudi Padwa or Chaitra Pratipada, Pola, Narali Purnima, Ratha Saptami, Marabats and Badgyas, etc,.</p>",
                    solution_hi: "<p>43.(a) <strong>महाराष्ट्र।</strong> शरद पूर्णिमा को कोजागिरी या रास पूर्णिमा के नाम से भी जाना जाता है। यह एक फसल उत्सव है। महाराष्ट्र के अन्य त्योहार: गुड़ी पड़वा या चैत्र प्रतिपदा, पोला, नारली पूर्णिमा, रथ सप्तमी, मारबत और बड़ग्या आदि।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "44",
                    section: "18",
                    question_en: "<p>44. According to Article 74(1), who is at the head of the Council of Ministers?</p>",
                    question_hi: "<p>44. अनुच्छेद 74(1) के अनुसार मंत्रिपरिषद (Council of Ministers) का प्रमुख कौन होता है?</p>",
                    options_en: [
                        "<p>Speaker of the House</p>",
                        "<p>Prime Minister</p>",
                        "<p>Chief Justice</p>",
                        "<p>President</p>"
                    ],
                    options_hi: [
                        "<p>हाउस स्पीकर</p>",
                        "<p>प्रधान मंत्री</p>",
                        "<p>मुख्य न्यायाधीश</p>",
                        "<p>राष्ट्रपति</p>"
                    ],
                    solution_en: "<p>44.(b) <strong>Prime Minister.</strong> The Council of Ministers is collectively responsible to the Lok Sabha, the House of the People. The Council of Ministers comprises Cabinet Ministers, Minister of States (independent charge or otherwise) and Deputy Ministers.</p>",
                    solution_hi: "<p>44.(b) <strong>प्रधानमंत्री।</strong> मंत्रिपरिषद सामूहिक रूप से लोक सभा के प्रति उत्तरदायी होती है। मंत्रिपरिषद में कैबिनेट मंत्री, राज्य मंत्री (स्वतंत्र प्रभार या अन्यथा) और उप मंत्री शामिल होते हैं।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "45",
                    section: "18",
                    question_en: "<p>45. Whose autobiography is titled &lsquo;A Life Apart&rsquo;?</p>",
                    question_hi: "<p>45. ए लाइफ अपार्ट (A Life Apart) किसकी आत्मकथा है?</p>",
                    options_en: [
                        "<p>Prabha Khaitan</p>",
                        "<p>Bindu Bhatt</p>",
                        "<p>Vinita Agarwal</p>",
                        "<p>Mannu Bhandari</p>"
                    ],
                    options_hi: [
                        "<p>प्रभा खेतान</p>",
                        "<p>बिंदु भट्ट</p>",
                        "<p>विनीता अग्रवाल</p>",
                        "<p>मन्नू भंडारी</p>"
                    ],
                    solution_en: "<p>45.(a) <strong>Prabha Khaitan.</strong> She was an Indian novelist, poet, entrepreneur and feminist. Bindu Bhatt is a Gujarati language novelist, storywriter, critic and translator. Awards - Sahitya Akademi Award (2003). Her books : &lsquo;Chhinnmasta&rsquo;, &lsquo;Anya Se Ananya&rsquo;, &lsquo;Peeli Aandhi&rsquo;, etc. Autobiography of Mannu Bhandari - &lsquo;Ek Kahaani Yeh Bhi&rsquo;.</p>",
                    solution_hi: "<p>45.(a) <strong>प्रभा खेतान</strong> एक भारतीय उपन्यासकार, कवयित्री, उद्यमी और नारीवादी थीं।। बिन्दु भट्ट गुजराती भाषा के उपन्यासकार, कहानीकार, आलोचक और अनुवादक हैं। पुरस्कार - साहित्य अकादमी पुरस्कार (2003)। उनकी प्रमुख पुस्तकें: \'छिन्नमस्ता\', \'अन्य से अनन्या\', \'पीली आँधी\', आदि। मन्नू भंडारी की आत्मकथा - \'एक कहानी ये भी\'।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "46",
                    section: "18",
                    question_en: "<p>46. When a person is NOT paying anything for public goods, this is known as ________.</p>",
                    question_hi: "<p>46. जब कोई व्यक्ति सार्वजनिक वस्तुओं के लिए कुछ भी भुगतान नहीं कर रहा है, तो इसे _______ के रूप में जाना जाता है।</p>",
                    options_en: [
                        "<p>Non-excludable</p>",
                        "<p>Rivalrous</p>",
                        "<p>Free rider</p>",
                        "<p>Private good</p>"
                    ],
                    options_hi: [
                        "<p>गैर-बहिष्कृत</p>",
                        "<p>प्रतिद्वंद्विता</p>",
                        "<p>फ्री राइडर</p>",
                        "<p>निजी वस्तुएं</p>"
                    ],
                    solution_en: "<p>46.(c) <strong>Free rider.</strong> In the context of public goods, a free rider is able to use or enjoy the benefits of the public good without contributing to its cost. This is possible because of the nature of public goods, which are typically non-excludable. Example: Using public parks without paying for them.</p>",
                    solution_hi: "<p>46.(c) <strong>फ्री राइडर।</strong> सार्वजनिक वस्तुओं के संदर्भ में, एक फ्री राइडर सार्वजनिक वस्तु की लागत में योगदान किए बिना उसके लाभों का उपयोग या आनंद लेने में सक्षम होता है। यह सार्वजनिक वस्तुओं की प्रकृति के कारण संभव है, जो सामान्यतः गैर-बहिष्कृत होती हैं। उदाहरण: बिना भुगतान किए सार्वजनिक पार्कों का उपयोग करना।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "47",
                    section: "18",
                    question_en: "<p>47. Which of the following categories does Gonyaulax belong to?</p>",
                    question_hi: "<p>47. निम्नलिखित में से कौन-सी श्रेणी गोनिओलेक्स (Gonyaulax) से संबंधित है?</p>",
                    options_en: [
                        "<p>Euglenoids</p>",
                        "<p>Chrysophytes</p>",
                        "<p>Protozoans</p>",
                        "<p>Dinoflagellates</p>"
                    ],
                    options_hi: [
                        "<p>यूग्लीनोएड्स (Euglenoids)</p>",
                        "<p>क्राइसोफाइट्स (Chrysophytes)</p>",
                        "<p>प्रोटोजोअन्&zwj;स (Protozoans)</p>",
                        "<p>डाइनोफ्लैजलेट्स (Dinoflagellates)</p>"
                    ],
                    solution_en: "<p>47.(d) <strong>Dinoflagellates.</strong> These organisms are mainly marine, photosynthetic organisms that can appear in various colors such as yellow, green, brown, blue, or red, depending on their pigments. Euglenoids include Euglena, Chrysophytes encompass diatoms and golden algae, while Amoeba represents protozoans.</p>",
                    solution_hi: "<p>47.(d) <strong>डाइनोफ्लैजलेट्स (Dinoflagellates)।</strong> ये जीव मुख्य रूप से समुद्री, प्रकाश संश्लेषक जीव हैं जो अपने रंगद्रव्य के आधार पर पीले, हरे, भूरे, नीले या लाल जैसे विभिन्न रंगों में दिखाई दे सकते हैं। यूग्लेनोइड्स में यूग्लेना शामिल है, क्राइसोफाइट्स में डायटम और गोल्डन शैवाल शामिल हैं, जबकि अमीबा प्रोटोजोआ को दर्शाता है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "48",
                    section: "18",
                    question_en: "<p>48. Good Friday is related to:</p>",
                    question_hi: "<p>48. गुड फ्राइडे किससे संबंधित है?</p>",
                    options_en: [
                        "<p>Jains</p>",
                        "<p>Hindus</p>",
                        "<p>Sikhs</p>",
                        "<p>Christians</p>"
                    ],
                    options_hi: [
                        "<p>जैन</p>",
                        "<p>हिंदुओं</p>",
                        "<p>सिखों</p>",
                        "<p>ईसाइयों</p>"
                    ],
                    solution_en: "<p>48.(d) <strong>Christians.</strong> Good Friday is a Christian holy day observing the crucifixion of Jesus and his death at Calvary. Other festivals and religions: Hindus - Diwali, Holi, Rakshabandhan. Christians - Christmas, Easter. Jains - Mahavir Jayanti, Paryushana, Mahamastakabhisheka festival. Sikhs - Hola Mohalla, Vaisakhi, Guru Nanak Gurpurab.</p>",
                    solution_hi: "<p>48.(d) <strong>ईसाइयों।</strong> गुड फ्राइडे ईसाइयों का पवित्र दिन है, जो ईसा मसीह के सूली पर चढ़ने और कैल्वरी में उनकी मृत्यु का स्मरण करता है। अन्य धर्म तथा त्योहार : हिंदू - दिवाली, होली, रक्षाबंधन। ईसाई - क्रिसमस, ईस्टर। जैन - महावीर जयंती, पर्यूषण, महामस्तकाभिषेक उत्सव। सिख - होला मोहल्ला, वैसाखी, गुरु नानक गुरुपर्व।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "49",
                    section: "18",
                    question_en: "<p>49. In what capacity has India joined the Eurodrone programme?</p>",
                    question_hi: "<p>49. यूरोड्रोन कार्यक्रम में भारत किस भूमिका में शामिल हुआ है?</p>",
                    options_en: [
                        "<p>Partner Country</p>",
                        "<p>Sponsor Country</p>",
                        "<p>Observer Country</p>",
                        "<p>Major Investor</p>"
                    ],
                    options_hi: [
                        "<p>भागीदार देश</p>",
                        "<p>प्रायोजक देश</p>",
                        "<p>पर्यवेक्षक देश</p>",
                        "<p>प्रमुख निवेशक</p>"
                    ],
                    solution_en: "<p>49.(c) <strong>Observer Country.</strong> India joined the Eurodrone programme as an observer in January 2025. This programme is being jointly developed by Germany, France, Italy, and Spain, under the leadership of Airbus Defence and Space. The Eurodrone is a Medium Altitude Long Endurance (MALE) Remotely Piloted Aircraft System (RPAS), designed for surveillance and defense operations. India\'s participation will help it gain insights into European defense technologies and advanced drone systems.</p>",
                    solution_hi: "<p>49.(c) <strong>पर्यवेक्षक देश</strong> ।<br>भारत जनवरी 2025 में यूरोड्रोन कार्यक्रम में पर्यवेक्षक (Observer) के रूप में शामिल हुआ। यह कार्यक्रम एयरबस डिफेंस एंड स्पेस के नेतृत्व में जर्मनी, फ्रांस, इटली और स्पेन द्वारा संयुक्त रूप से विकसित किया जा रहा है। यूरोड्रोन एक मध्यम ऊंचाई लंबी सहनशक्ति (MALE) मानव रहित विमान प्रणाली (RPAS) है, जो निगरानी और रक्षा उद्देश्यों के लिए उपयोग की जाएगी। भारत की भागीदारी उसे यूरोपियन रक्षा तकनीकों और उन्नत ड्रोन सिस्टम्स से परिचित कराएगी।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "50",
                    section: "17",
                    question_en: "<p>50. The Working Group under the chairmanship of _______ in the year_______ proposed a new intermediate monetary aggregate to be referred to as NM2.</p>",
                    question_hi: "<p>50. ______की अध्यक्षता में बने कार्य समूह ने वर्ष _______ में एक नए मध्यवर्ती मौद्रिक समुच्चय को NM2 के रूप में संदर्भित करने का प्रस्ताव दिया।</p>",
                    options_en: [
                        "<p>Dr. C Rangarajan; 1996</p>",
                        "<p>Dr. YV Reddy; 1998</p>",
                        "<p>Dr. KV Kamath; 1995</p>",
                        "<p>Dr. PK Mohanty; 1998</p>"
                    ],
                    options_hi: [
                        "<p>डॉ. सी. रंगराजन; 1996</p>",
                        "<p>डॉ. वाई.वी. रेड्डी; 1998</p>",
                        "<p>डॉ. के.वी. कामथ; 1995</p>",
                        "<p>डॉ. पी.के. मोहंती; 1998</p>"
                    ],
                    solution_en: "<p>50.(b) <strong>Dr. YV Reddy; 1998</strong>. NM2 (Narrow Money 2) measures the money supply in an economy, including currency, demand deposits, and time deposits. It indicates liquidity and helps guide monetary policy and economic regulation. Central banks, such as the RBI, use NM2 to control inflation and manage economic growth, reflecting broader money supply aspects for policy decisions.</p>",
                    solution_hi: "<p>50.(b) <strong>डॉ. वाई.वी. रेड्डी; 1998</strong>. NM2 (नैरो मनी 2) मुद्रा, मांग जमा और सावधि जमा सहित अर्थव्यवस्था में मुद्रा आपूर्ति को मापता है। यह तरलता को इंगित करता है और यह मौद्रिक नीति तथा आर्थिक विनियमन को निर्देशित करने में मदद करता है। RBI जैसे केंद्रीय बैंक मुद्रास्फीति को नियंत्रित करने और आर्थिक विकास का प्रबंधन करने के लिए NM2 का उपयोग करते हैं, जो नीतिगत निर्णयों के लिए व्यापक मुद्रा आपूर्ति पहलुओं को दर्शाता है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "51",
                    section: "17",
                    question_en: "<p>51. PQR is a triangle. MN is a line segment intersecting PQ in M and PR in N such that MN&nbsp;∥ QR and divides &Delta;PQR into two parts, which are equal in area. What is the ratio of MQ to PQ?</p>",
                    question_hi: "<p>51. PQR एक त्रिभुज है। MN एक रेखाखंड है जो PQ को M पर और PR को N पर इस प्रकार प्रतिच्छेदित&nbsp;करता है कि MN ∥ QR है और &Delta;PQR को दो भागों में विभाजित करता है, जिनका क्षेत्रफल बराबर है। MQ और PQ का अनुपात क्या है?</p>",
                    options_en: [
                        "<p><math display=\"inline\"><msqrt><mn>2</mn></msqrt></math> : (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>2</mn></msqrt></math> - 1)</p>",
                        "<p>1 : <math display=\"inline\"><msqrt><mn>2</mn></msqrt></math></p>",
                        "<p><math display=\"inline\"><msqrt><mn>2</mn></msqrt></math> : 1</p>",
                        "<p>(<math display=\"inline\"><msqrt><mn>2</mn></msqrt></math> - 1) : <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>2</mn></msqrt></math></p>"
                    ],
                    options_hi: [
                        "<p><math display=\"inline\"><msqrt><mn>2</mn></msqrt></math> : (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>2</mn></msqrt></math> - 1)</p>",
                        "<p>1 : <math display=\"inline\"><msqrt><mn>2</mn></msqrt></math></p>",
                        "<p><math display=\"inline\"><msqrt><mn>2</mn></msqrt></math> : 1</p>",
                        "<p>(<math display=\"inline\"><msqrt><mn>2</mn></msqrt></math> - 1) : <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>2</mn></msqrt></math></p>"
                    ],
                    solution_en: "<p>51.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743393559575.png\" alt=\"rId51\" width=\"202\" height=\"186\"><br>△PMN &sim; △PQR (by similar triangle rule))<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msup><mi>PM</mi><mn>2</mn></msup><msup><mi>PQ</mi><mn>2</mn></msup></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>ar</mi><mo>(</mo><mo>&#9651;</mo><mi>PMN</mi><mo>)</mo></mrow><mrow><mi>ar</mi><mo>(</mo><mo>&#9651;</mo><mi>PQR</mi><mo>)</mo></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math><br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>PM</mi><mi>PQ</mi></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msqrt><mn>2</mn></msqrt></mfrac></math><br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>PQ</mi><mo>-</mo><mi>QM</mi></mrow><mi>PQ</mi></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msqrt><mn>2</mn></msqrt></mfrac></math><br>1 - <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>MQ</mi><mi>PQ</mi></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msqrt><mn>2</mn></msqrt></mfrac></math><br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>MQ</mi><mi>PQ</mi></mfrac></math> = 1 - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msqrt><mn>2</mn></msqrt></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msqrt><mn>2</mn></msqrt><mo>-</mo><mn>1</mn></mrow><msqrt><mn>2</mn></msqrt></mfrac></math></p>",
                    solution_hi: "<p>51.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743393559575.png\" alt=\"rId51\"><br>△PMN &sim; △PQR (समरूप त्रिभुज नियम द्वारा))<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msup><mi>PM</mi><mn>2</mn></msup><msup><mi>PQ</mi><mn>2</mn></msup></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mo>&#9651;</mo><mi>PMN</mi><mo>)</mo><mo>&#160;</mo><mi>&#2325;&#2366;</mi><mo>&#160;</mo><mi>&#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;&#2347;&#2354;</mi></mrow><mrow><mo>(</mo><mo>&#9651;</mo><mi>PQR</mi><mo>)</mo><mo>&#160;</mo><mi>&#2325;&#2366;</mi><mo>&#160;</mo><mi>&#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;&#2347;&#2354;</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math><br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>PM</mi><mi>PQ</mi></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msqrt><mn>2</mn></msqrt></mfrac></math><br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>PQ</mi><mo>-</mo><mi>QM</mi></mrow><mi>PQ</mi></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msqrt><mn>2</mn></msqrt></mfrac></math><br>1 - <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>MQ</mi><mi>PQ</mi></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msqrt><mn>2</mn></msqrt></mfrac></math><br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>MQ</mi><mi>PQ</mi></mfrac></math> = 1 - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msqrt><mn>2</mn></msqrt></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msqrt><mn>2</mn></msqrt><mo>-</mo><mn>1</mn></mrow><msqrt><mn>2</mn></msqrt></mfrac></math></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "52",
                    section: "17",
                    question_en: "<p>52. If 35 is the mean proportional of 25 and x, then what is value of x?</p>",
                    question_hi: "<p>52. यदि 25 और x का मध्यानुपाती 35 है, तो x का मान ज्ञात कीजिए।</p>",
                    options_en: [
                        "<p>47</p>",
                        "<p>46</p>",
                        "<p>48</p>",
                        "<p>49</p>"
                    ],
                    options_hi: [
                        "<p>47</p>",
                        "<p>46</p>",
                        "<p>48</p>",
                        "<p>49</p>"
                    ],
                    solution_en: "<p>52.(d)<br>According to the question,<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>25</mn><mo>&#215;</mo><mi mathvariant=\"normal\">x</mi></msqrt></math> = 35<br>Taking square both sides <br>25 &times; <math display=\"inline\"><mi>x</mi></math> = (35)<sup>2</sup><br><math display=\"inline\"><mi>x</mi></math> = 49</p>",
                    solution_hi: "<p>52.(d)<br>प्रश्न के अनुसार,<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>25</mn><mo>&#215;</mo><mi mathvariant=\"normal\">x</mi></msqrt></math> = 35<br>दोनों ओर वर्ग करने पर <br>25 &times; <math display=\"inline\"><mi>x</mi></math> = (35)<sup>2</sup><br><math display=\"inline\"><mi>x</mi></math> = 49</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "53",
                    section: "17",
                    question_en: "<p>53. If 24 people can build 90 identical walls in 25 days, then how many more days will 27 people require to build 162 such walls?</p>",
                    question_hi: "<p>53. यदि 24 व्यक्ति 25 दिनों में 90 समान दीवारें बना सकते हैं, तो ऐसी 162 दीवारों को बनाने में 27 व्यक्तियों को और कितने दिन लगेंगे?</p>",
                    options_en: [
                        "<p>16</p>",
                        "<p>14</p>",
                        "<p>15</p>",
                        "<p>13</p>"
                    ],
                    options_hi: [
                        "<p>16</p>",
                        "<p>14</p>",
                        "<p>15</p>",
                        "<p>13</p>"
                    ],
                    solution_en: "<p>53.(c)<br><strong>Formula used</strong> : - <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msub><mi mathvariant=\"normal\">M</mi><mn>1</mn></msub><msub><mi mathvariant=\"normal\">D</mi><mn>1</mn></msub><msub><mi mathvariant=\"normal\">H</mi><mn>1</mn></msub></mrow><msub><mi mathvariant=\"normal\">W</mi><mn>1</mn></msub></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msub><mi mathvariant=\"normal\">M</mi><mn>2</mn></msub><msub><mi mathvariant=\"normal\">D</mi><mn>2</mn></msub><msub><mi mathvariant=\"normal\">H</mi><mn>2</mn></msub></mrow><msub><mi mathvariant=\"normal\">W</mi><mn>2</mn></msub></mfrac></math><br>According to the question,<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>24</mn><mo>&#215;</mo><mn>25</mn></mrow><mn>90</mn></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>27</mn><mo>&#215;</mo><mi mathvariant=\"normal\">x</mi></mrow><mn>162</mn></mfrac></math><br><math display=\"inline\"><mi>x</mi></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>24</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>25</mn><mo>&#215;</mo><mn>162</mn></mrow><mrow><mn>27</mn><mo>&#215;</mo><mn>90</mn></mrow></mfrac></math> &nbsp;= 40 days<br>Required more days = 40 - 25 = 15 days</p>",
                    solution_hi: "<p>53.(c)<br><strong>प्रयुक्त सूत्र</strong>:- <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msub><mi mathvariant=\"normal\">M</mi><mn>1</mn></msub><msub><mi mathvariant=\"normal\">D</mi><mn>1</mn></msub><msub><mi mathvariant=\"normal\">H</mi><mn>1</mn></msub></mrow><msub><mi mathvariant=\"normal\">W</mi><mn>1</mn></msub></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msub><mi mathvariant=\"normal\">M</mi><mn>2</mn></msub><msub><mi mathvariant=\"normal\">D</mi><mn>2</mn></msub><msub><mi mathvariant=\"normal\">H</mi><mn>2</mn></msub></mrow><msub><mi mathvariant=\"normal\">W</mi><mn>2</mn></msub></mfrac></math><br>प्रश्न के अनुसार,<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>24</mn><mo>&#215;</mo><mn>25</mn></mrow><mn>90</mn></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>27</mn><mo>&#215;</mo><mi mathvariant=\"normal\">x</mi></mrow><mn>162</mn></mfrac></math><br><math display=\"inline\"><mi>x</mi></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>24</mn><mo>&#215;</mo><mn>25</mn><mo>&#215;</mo><mn>162</mn></mrow><mrow><mn>27</mn><mo>&#215;</mo><mn>90</mn></mrow></mfrac></math>&nbsp; =&nbsp; 40 दिन<br>अधिक दिनों की आवश्यकता = 40 - 25 = 15 दिन</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "54",
                    section: "17",
                    question_en: "<p>54. Monthly salary of Jitvik in the month of December 2020 was ₹24,800. Every year his salary increases by 5% from the month of January. What was his salary (in ₹) in the month of February 2022 ?</p>",
                    question_hi: "<p>54. दिसंबर 2020 में जित्विक का मासिक वेतन ₹24,800 था। हर साल उनके वेतन में जनवरी के महीने से&nbsp;5% की वृद्धि होती है। फरवरी 2022 के महीने में उसका वेतन (₹ में) कितना था?</p>",
                    options_en: [
                        "<p>27180</p>",
                        "<p>27486</p>",
                        "<p>26485</p>",
                        "<p>27342</p>"
                    ],
                    options_hi: [
                        "<p>27180</p>",
                        "<p>27486</p>",
                        "<p>26485</p>",
                        "<p>27342</p>"
                    ],
                    solution_en: "<p>54.(d) According to question,<br>Jitvik salary in month of february 2022 = 24800 &times; <math display=\"inline\"><mfrac><mrow><mn>21</mn></mrow><mrow><mn>20</mn></mrow></mfrac></math> &times; <math display=\"inline\"><mfrac><mrow><mn>21</mn></mrow><mrow><mn>20</mn></mrow></mfrac></math><br>= ₹ 27342</p>",
                    solution_hi: "<p>54.(d) प्रश्न के अनुसार,<br>फरवरी 2022 के महीने में जित्विक का वेतन = 24800 &times; <math display=\"inline\"><mfrac><mrow><mn>21</mn></mrow><mrow><mn>20</mn></mrow></mfrac></math> &times; <math display=\"inline\"><mfrac><mrow><mn>21</mn></mrow><mrow><mn>20</mn></mrow></mfrac></math><br>= ₹ 27342</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "55",
                    section: "17",
                    question_en: "<p>55. Simple interest on a certain sum is one-fifth of the sum and the interest rate per cent per annum is 5 times the number of years. If the rate of interest increases by 3%, then how much will the simple interest (in ₹) be on ₹5,600 for 6 years?</p>",
                    question_hi: "<p>55. एक निश्चित धनराशि पर साधारण ब्याज, धनराशि का पाँचवां हिस्सा है और प्रति वर्ष ब्याज की दर वर्षों की संख्या की 5 गुनी है। यदि ब्याज की दर में 3% की वृद्धि हो जाती है, तो 6 वर्षों के लिए ₹5,600 पर साधारण ब्याज (₹ में) कितना होगा?</p>",
                    options_en: [
                        "<p>4668</p>",
                        "<p>4168</p>",
                        "<p>4268</p>",
                        "<p>4368</p>"
                    ],
                    options_hi: [
                        "<p>4668</p>",
                        "<p>4168</p>",
                        "<p>4268</p>",
                        "<p>4368</p>"
                    ],
                    solution_en: "<p>55.(d)<br>Simple Interest = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>Principal</mi><mo>&#215;</mo><mi>rate</mi><mo>&#215;</mo><mi>time</mi></mrow><mn>100</mn></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>5</mn></mfrac></math>Principal =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>Principal</mi><mo>&#215;</mo><mn>5</mn><mo>(</mo><mi mathvariant=\"normal\">t</mi><mo>)</mo><mo>&#215;</mo><mi>time</mi></mrow><mn>100</mn></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math> time = 2 years<br>And rate = 10%<br>New rate = 13 %<br>SI = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5600</mn><mo>&#215;</mo><mn>13</mn><mo>&#215;</mo><mn>6</mn></mrow><mn>100</mn></mfrac></math> = 4368</p>",
                    solution_hi: "<p>55.(d)<br>साधारण ब्याज = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2350;&#2370;&#2354;&#2343;&#2344;</mi><mo>&#215;</mo><mi mathvariant=\"normal\">&#160;</mi><mi>&#2342;&#2352;</mi><mi mathvariant=\"normal\">&#160;</mi><mo>&#215;</mo><mi>&#2360;&#2350;&#2351;</mi></mrow><mn>100</mn></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>5</mn></mfrac></math> मूलधन =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2350;&#2370;&#2354;&#2343;&#2344;</mi><mo>&#215;</mo><mn>5</mn><mo>(</mo><mi>&#2342;&#2352;</mi><mo>)</mo><mo>&#215;</mo><mi>&#2360;&#2350;&#2351;</mi></mrow><mn>100</mn></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math> समय = 2 वर्ष<br>और दर = 10%<br>नई दर = 13%<br>साधारण ब्याज = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5600</mn><mo>&#215;</mo><mn>13</mn><mo>&#215;</mo><mn>6</mn></mrow><mn>100</mn></mfrac></math>&nbsp;= 4368</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "56",
                    section: "17",
                    question_en: "<p>56. Find the average of first 125 natural numbers.</p>",
                    question_hi: "<p>56. प्रथम 125 प्राकृत संख्याओं का औसत ज्ञात कीजिए।</p>",
                    options_en: [
                        "<p>62.5</p>",
                        "<p>63</p>",
                        "<p>62</p>",
                        "<p>63.5</p>"
                    ],
                    options_hi: [
                        "<p>62.5</p>",
                        "<p>63</p>",
                        "<p>62</p>",
                        "<p>63.5</p>"
                    ],
                    solution_en: "<p>56.(b)<br>Sum of first 125 natural numbers = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>125</mn><mo>&#215;</mo><mo>(</mo><mn>125</mn><mo>+</mo><mn>1</mn><mo>)</mo></mrow><mrow><mi>&#160;</mi><mn>2</mn></mrow></mfrac></math> = 7875<br>Required average = <math display=\"inline\"><mfrac><mrow><mn>7875</mn></mrow><mrow><mn>125</mn></mrow></mfrac></math> = 63</p>",
                    solution_hi: "<p>56.(b) प्रथम 125 प्राकृत संख्याओं का योग = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>125</mn><mo>&#215;</mo><mo>(</mo><mn>125</mn><mo>+</mo><mn>1</mn><mo>)</mo></mrow><mrow><mi>&#160;</mi><mn>2</mn></mrow></mfrac></math>&nbsp;= 7875 <br>आवश्यक औसत = <math display=\"inline\"><mfrac><mrow><mn>7875</mn></mrow><mrow><mn>125</mn></mrow></mfrac></math> = 63</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "57",
                    section: "17",
                    question_en: "<p>57. Simplify the following. <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#160;</mo><mstyle displaystyle=\"true\"><mfrac><mrow><msup><mi>Sin</mi><mn>3</mn></msup><mo>-</mo><msup><mi>Cos</mi><mn>3</mn></msup><mo>&#160;</mo><mo>&#160;</mo></mrow><mrow><mi>SinA</mi><mo>-</mo><mi>CosA</mi></mrow></mfrac></mstyle></math> where A is an acute angle.</p>",
                    question_hi: "<p>57. निम्न का मान ज्ञात करें | <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"true\"><mfrac><mrow><msup><mi>Sin</mi><mn>3</mn></msup><mo>-</mo><mi>C</mi><mi>o</mi><msup><mi>s</mi><mn>3</mn></msup><mo>&#160;</mo><mo>&#160;</mo></mrow><mrow><mi>S</mi><mi>i</mi><mi>n</mi><mi>A</mi><mo>-</mo><mi>C</mi><mi>o</mi><mi>s</mi><mi>A</mi></mrow></mfrac></mstyle></math>&nbsp;,जहां A एक न्यून कोण है |</p>",
                    options_en: [
                        "<p>1 + sinA cosA</p>",
                        "<p>1- 3 sinA</p>",
                        "<p>3 cosA - 1</p>",
                        "<p>sinA + cosA</p>"
                    ],
                    options_hi: [
                        "<p>1 + sinA cosA</p>",
                        "<p>1 - 3 sinA</p>",
                        "<p>3 cosA - 1</p>",
                        "<p>sinA + cosA</p>"
                    ],
                    solution_en: "<p>57.(a)<br><strong>Identity used:</strong> a<sup>3</sup> - b<sup>3 </sup>= (a - b) (a<sup>2 </sup>+ b<sup>2</sup>&nbsp;+ ab)<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi>sin</mi><mn>3</mn></msup><mi>A</mi><mo>-</mo><msup><mi>cos</mi><mn>3</mn></msup><mi>A</mi></mrow><mrow><mi>sin</mi><mi mathvariant=\"normal\">&#160;</mi><mi mathvariant=\"normal\">A</mi><mo>-</mo><mi>cos</mi><mi mathvariant=\"normal\">&#160;</mi><mi mathvariant=\"normal\">A</mi></mrow></mfrac></math><br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mi>sinA</mi><mo>-</mo><mi>cosA</mi><mo>)</mo><mo>[</mo><msup><mi>sin</mi><mn>2</mn></msup><mi mathvariant=\"normal\">A</mi><mo>+</mo><msup><mi>cos</mi><mn>2</mn></msup><mi mathvariant=\"normal\">A</mi><mo>+</mo><mi>SinA</mi><mo>.</mo><mi mathvariant=\"normal\">&#160;</mi><mi>cosA</mi><mi mathvariant=\"normal\">&#160;</mi><mo>]</mo></mrow><mrow><mi>sinA</mi><mo>-</mo><mi>cosA</mi></mrow></mfrac></math><br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mi>sinA</mi><mo>-</mo><mi>cosA</mi><mo>)</mo><mo>[</mo><mn>1</mn><mo>+</mo><mi>SinA</mi><mo>.</mo><mi>cosA</mi><mo>]</mo></mrow><mrow><mi>sinA</mi><mo>-</mo><mi>cosA</mi></mrow></mfrac></math><br>= 1 + sinA cosA</p>",
                    solution_hi: "<p>57.(a)<br><strong>प्रयुक्त सूत्र :</strong> a<sup>3</sup> - b<sup>3 </sup>= (a - b) (a<sup>2 </sup>+ b<sup>2</sup>&nbsp;+ ab)<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi>sin</mi><mn>3</mn></msup><mi>A</mi><mo>-</mo><msup><mi>cos</mi><mn>3</mn></msup><mi>A</mi></mrow><mrow><mi>sin</mi><mi mathvariant=\"normal\">&#160;</mi><mi mathvariant=\"normal\">A</mi><mo>-</mo><mi>cos</mi><mi mathvariant=\"normal\">&#160;</mi><mi mathvariant=\"normal\">A</mi></mrow></mfrac></math><br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mi>sinA</mi><mo>-</mo><mi>cosA</mi><mo>)</mo><mo>[</mo><msup><mi>sin</mi><mn>2</mn></msup><mi mathvariant=\"normal\">A</mi><mo>+</mo><msup><mi>cos</mi><mn>2</mn></msup><mi mathvariant=\"normal\">A</mi><mo>+</mo><mi>SinA</mi><mo>.</mo><mi mathvariant=\"normal\">&#160;</mi><mi>cosA</mi><mi mathvariant=\"normal\">&#160;</mi><mo>]</mo></mrow><mrow><mi>sinA</mi><mo>-</mo><mi>cosA</mi></mrow></mfrac></math><br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mi>sinA</mi><mo>-</mo><mi>cosA</mi><mo>)</mo><mo>[</mo><mn>1</mn><mo>+</mo><mi>SinA</mi><mo>.</mo><mi>cosA</mi><mo>]</mo></mrow><mrow><mi>sinA</mi><mo>-</mo><mi>cosA</mi></mrow></mfrac></math><br>= 1 + sinA cosA</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "58",
                    section: "17",
                    question_en: "<p>58. If 0.4x + 0.16y = 1.7 and 0.3x + 0.12y = 3.4, then which of the following is correct?</p>",
                    question_hi: "<p>58. यदि 0.4x + 0.16y = 1.7 और 0.3x + 0.12y = 3.4 है, तो निम्नलिखित में से कौन सा कथन सही है?</p>",
                    options_en: [
                        "<p>The system has finitely many solutions but not unique.</p>",
                        "<p>The system has infinitely many solutions.</p>",
                        "<p>The system has no solution.</p>",
                        "<p>The system has unique solution.</p>"
                    ],
                    options_hi: [
                        "<p>निकाय में परिमित रूप से अनेक हल हैं लेकिन अद्वितीय नहीं हैं।</p>",
                        "<p>निकाय के अपरिमित रूप से अनेक हल हैं।</p>",
                        "<p>निकाय का कोई हल नहीं है।</p>",
                        "<p>निकाय का अद्वितीय हल है।</p>"
                    ],
                    solution_en: "<p>58.(c) <br>0.4x + 0.16y - 1.7 = 0<br>0.3x + 0.12y - 3.4 = 0<br>Comparing these two equations with a<sub>1</sub>x + b<sub>1</sub>y + c<sub>1 </sub>= 0 &amp; a<sub>2</sub>x + b<sub>2</sub>y + c<sub>2</sub> = 0. We have ;<br><math display=\"inline\"><mfrac><mrow><msub><mrow><mi>a</mi></mrow><mrow><mn>1</mn></mrow></msub></mrow><mrow><msub><mrow><mi>a</mi></mrow><mrow><mn>2</mn></mrow></msub></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>0</mn><mo>.</mo><mn>4</mn></mrow><mrow><mn>0</mn><mo>.</mo><mn>3</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>3</mn></mfrac></math>,<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>&#160;</mo><msub><mi mathvariant=\"normal\">b</mi><mn>1</mn></msub></mrow><msub><mi mathvariant=\"normal\">b</mi><mn>2</mn></msub></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>0</mn><mo>.</mo><mn>16</mn></mrow><mrow><mn>0</mn><mo>.</mo><mn>12</mn></mrow></mfrac></math>&nbsp; = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>3</mn></mfrac></math>, <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msub><mi mathvariant=\"normal\">c</mi><mn>1</mn></msub><msub><mi mathvariant=\"normal\">c</mi><mn>2</mn></msub></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>-</mo><mn>1</mn><mo>.</mo><mn>7</mn></mrow><mrow><mo>-</mo><mn>3</mn><mo>.</mo><mn>4</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math> <math display=\"inline\"><mfrac><mrow><msub><mrow><mi>a</mi></mrow><mrow><mn>1</mn></mrow></msub></mrow><mrow><msub><mrow><mi>a</mi></mrow><mrow><mn>2</mn></mrow></msub></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msub><mi mathvariant=\"normal\">c</mi><mn>1</mn></msub><msub><mi mathvariant=\"normal\">c</mi><mn>2</mn></msub></mfrac></math> &ne; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math><br>Clearly, we can see that the equations has no solution.</p>",
                    solution_hi: "<p>58.(c) <br>0.4x + 0.16y - 1.7 = 0<br>0.3x + 0.12y - 3.4 = 0<br>इन दोनो समीकरणों की तुलना a<sub>1</sub>x + b<sub>1</sub>y + c<sub>1</sub> = 0 और a<sub>2</sub>x + b<sub>2</sub>y + c<sub>2</sub> = 0 से करने पर, <br><math display=\"inline\"><mfrac><mrow><msub><mrow><mi>a</mi></mrow><mrow><mn>1</mn></mrow></msub></mrow><mrow><msub><mrow><mi>a</mi></mrow><mrow><mn>2</mn></mrow></msub></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>0</mn><mo>.</mo><mn>4</mn></mrow><mrow><mn>0</mn><mo>.</mo><mn>3</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>3</mn></mfrac></math>,<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>&#160;</mo><msub><mi mathvariant=\"normal\">b</mi><mn>1</mn></msub></mrow><msub><mi mathvariant=\"normal\">b</mi><mn>2</mn></msub></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>0</mn><mo>.</mo><mn>16</mn></mrow><mrow><mn>0</mn><mo>.</mo><mn>12</mn></mrow></mfrac></math>&nbsp; = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>3</mn></mfrac></math>, <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msub><mi mathvariant=\"normal\">c</mi><mn>1</mn></msub><msub><mi mathvariant=\"normal\">c</mi><mn>2</mn></msub></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>-</mo><mn>1</mn><mo>.</mo><mn>7</mn></mrow><mrow><mo>-</mo><mn>3</mn><mo>.</mo><mn>4</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math> <math display=\"inline\"><mfrac><mrow><msub><mrow><mi>a</mi></mrow><mrow><mn>1</mn></mrow></msub></mrow><mrow><msub><mrow><mi>a</mi></mrow><mrow><mn>2</mn></mrow></msub></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msub><mi mathvariant=\"normal\">c</mi><mn>1</mn></msub><msub><mi mathvariant=\"normal\">c</mi><mn>2</mn></msub></mfrac></math> &ne; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math><br>स्पष्ट रुप से देख सकते है इन दोनो समीकरणो का कोई हल नही है ।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "59",
                    section: "17",
                    question_en: "<p>59. A mixture contains acid and water in the ratio of 4 : 7. If 10 litres of water is added to it, then the ratio of acid and water becomes 8 : 19. What is the quantity of acid in the mixture?</p>",
                    question_hi: "<p>59. एक मिश्रण में अम्ल और पानी का अनुपात 4 : 7 है। यदि इसमें 10 लीटर पानी मिलाया जाए, तो अम्ल और पानी का अनुपात 8 : 19 हो जाता है। मिश्रण में अम्ल की मात्रा कितनी है?</p>",
                    options_en: [
                        "<p>8 litres</p>",
                        "<p>4 litres</p>",
                        "<p>12 litres</p>",
                        "<p>16 litres</p>"
                    ],
                    options_hi: [
                        "<p>8 लीटर</p>",
                        "<p>4 लीटर</p>",
                        "<p>12 लीटर</p>",
                        "<p>16 लीटर</p>"
                    ],
                    solution_en: "<p>59.(d)<br>Let the initial quantity of acid and water be 4a and 7a respectively.<br>According to question,<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>4</mn><mi mathvariant=\"normal\">a</mi></mrow><mrow><mn>7</mn><mi mathvariant=\"normal\">a</mi><mo>+</mo><mn>10</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>8</mn><mn>19</mn></mfrac></math><br>76a = 56a + 80<br>a = 4<br>Then, the quantity of acid = 4a = 16 litres.</p>",
                    solution_hi: "<p>59.(d)<br>माना अम्ल और पानी की प्रारंभिक मात्रा क्रमशः 4a और 7a है।<br>प्रश्न के अनुसार,<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>4</mn><mi mathvariant=\"normal\">a</mi></mrow><mrow><mn>7</mn><mi mathvariant=\"normal\">a</mi><mo>+</mo><mn>10</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>8</mn><mn>19</mn></mfrac></math><br>76a = 56a + 80<br>a = 4<br>तो, अम्ल की मात्रा = 4a = 16 लीटर.</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "60",
                    section: "17",
                    question_en: "<p>60. The sum of two&minus;digit numbers and the number obtained by interchanging the digit is 77. If the difference of digits is 1, then the number is:</p>",
                    question_hi: "<p>60. दो-अंकों की संख्या का योग और उसके अंको को आपस में बदलने पर प्राप्त संख्या 77 है। यदि अंकों का अंतर 1 है, तो संख्या ज्ञात कीजिए।</p>",
                    options_en: [
                        "<p>67</p>",
                        "<p>34</p>",
                        "<p>12</p>",
                        "<p>45</p>"
                    ],
                    options_hi: [
                        "<p>67</p>",
                        "<p>34</p>",
                        "<p>12</p>",
                        "<p>45</p>"
                    ],
                    solution_en: "<p>60.(b)<br>To solve these kinds of questions we take help of the options,<br>Option (a) 67 + 76 = 143 (not satisfied)<br><strong>Option (b) 34 + 43 = 77 (satisfied)</strong><br>Option (c)12 + 21 = 33 (not satisfied)<br>Option (d) 45 + 54 = 99 (not satisfied)<br>So number will be 34</p>",
                    solution_hi: "<p>60.(b)<br>इस प्रकार के प्रश्नों को हल करने के लिए हम विकल्पों की सहायता लेते हैं,<br>विकल्प (a) 67 + 76 = 143 (संतुष्ट नहीं)<br><strong>विकल्प (b) 34 + 43 = 77 (संतुष्ट)</strong><br>विकल्प (c)12 + 21 = 33 (संतुष्ट नहीं)<br>विकल्प (d) 45 + 54 = 99 (संतुष्ट नहीं)<br>तो संख्या 34 होगी।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "61",
                    section: "17",
                    question_en: "<p>61. The value of 64 &divide; [44 - (8 of 3 - 16) &divide; 4 &times; 20] is equal to:</p>",
                    question_hi: "<p>61. 64 &divide; [44 - (8 का 3 - 16) &divide; 4 &times; 20] का मान निम्न में से किसके बराबर होगा ?</p>",
                    options_en: [
                        "<p>12</p>",
                        "<p>18</p>",
                        "<p>16</p>",
                        "<p>15</p>"
                    ],
                    options_hi: [
                        "<p>12</p>",
                        "<p>18</p>",
                        "<p>16</p>",
                        "<p>15</p>"
                    ],
                    solution_en: "<p>61.(c)<br>64 &divide; [44 - (8 of 3 - 16) &divide; 4 &times; 20]<br>64 &divide; [44 - (24 - 16) &divide; 4 &times; 20]<br>64 &divide; [44 - 8 &divide; 4 &times; 20]<br>64 &divide; [44 - 2 &times; 20]<br>64 &divide; [44 - 40] = 16</p>",
                    solution_hi: "<p>61.(c)<br>64 &divide; [44 - (8 का 3 - 16) &divide; 4 &times; 20]<br>64 &divide; [44 - (24 - 16) &divide; 4 &times; 20]<br>64 &divide; [44 - 8 &divide; 4 &times; 20]<br>64 &divide; [44 - 2 &times; 20]<br>64 &divide; [44 - 40] = 16</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "62",
                    section: "17",
                    question_en: "<p>62. Find the value of <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>sec</mi><mi mathvariant=\"normal\">&#160;</mi><mn>35</mn><mo>&#176;</mo></mrow><mrow><mi>cosec</mi><mi mathvariant=\"normal\">&#160;</mi><mn>55</mn><mo>&#176;</mo></mrow></mfrac></math> -&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>cos</mi><mo>&#160;</mo><mn>25</mn><mo>&#176;</mo></mrow><mrow><mi>sin</mi><mo>&#160;</mo><mn>65</mn><mo>&#176;</mo></mrow></mfrac></math></p>",
                    question_hi: "<p>62. <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>sec</mi><mi mathvariant=\"normal\">&#160;</mi><mn>35</mn><mo>&#176;</mo></mrow><mrow><mi>cosec</mi><mi mathvariant=\"normal\">&#160;</mi><mn>55</mn><mo>&#176;</mo></mrow></mfrac></math> -&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>cos</mi><mo>&#160;</mo><mn>25</mn><mo>&#176;</mo></mrow><mrow><mi>sin</mi><mo>&#160;</mo><mn>65</mn><mo>&#176;</mo></mrow></mfrac></math>&nbsp;का मान ज्ञात कीजिए।</p>",
                    options_en: [
                        "<p>0</p>",
                        "<p>1</p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math></p>",
                        "<p>2</p>"
                    ],
                    options_hi: [
                        "<p>0</p>",
                        "<p>1</p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math></p>",
                        "<p>2</p>"
                    ],
                    solution_en: "<p>62.(a) <br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>sec</mi><mi mathvariant=\"normal\">&#160;</mi><mn>35</mn><mo>&#176;</mo></mrow><mrow><mi>cosec</mi><mi mathvariant=\"normal\">&#160;</mi><mn>55</mn><mo>&#176;</mo></mrow></mfrac></math> -&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>cos</mi><mo>&#160;</mo><mn>25</mn><mo>&#176;</mo></mrow><mrow><mi>sin</mi><mo>&#160;</mo><mn>65</mn><mo>&#176;</mo></mrow></mfrac></math><br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>sec</mi><mo>(</mo><mn>90</mn><mo>-</mo><mn>55</mn><mo>&#176;</mo><mo>)</mo></mrow><mrow><mi>cosec</mi><mi mathvariant=\"normal\">&#160;</mi><mn>55</mn><mo>&#176;</mo></mrow></mfrac></math>&nbsp;-&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>cos</mi><mo>(</mo><mn>90</mn><mo>-</mo><mn>65</mn><mo>&#176;</mo><mo>)</mo></mrow><mrow><mi>sin</mi><mo>&#160;</mo><mn>65</mn><mo>&#176;</mo></mrow></mfrac></math><br><math display=\"inline\"><mfrac><mrow><mi>c</mi><mi>o</mi><mi>s</mi><mi>e</mi><mi>c</mi><mi>&#160;</mi><mn>55</mn><mo>&#176;</mo></mrow><mrow><mi>c</mi><mi>o</mi><mi>s</mi><mi>e</mi><mi>c</mi><mi>&#160;</mi><mn>55</mn><mo>&#176;</mo></mrow></mfrac></math> -&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>sin</mi><mo>&#160;</mo><mn>65</mn><mo>&#176;</mo></mrow><mrow><mi>sin</mi><mo>&#160;</mo><mn>65</mn><mo>&#176;</mo></mrow></mfrac></math> = 1 - 1 = 0</p>",
                    solution_hi: "<p>62.(a) <br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>sec</mi><mi mathvariant=\"normal\">&#160;</mi><mn>35</mn><mo>&#176;</mo></mrow><mrow><mi>cosec</mi><mi mathvariant=\"normal\">&#160;</mi><mn>55</mn><mo>&#176;</mo></mrow></mfrac></math> -&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>cos</mi><mo>&#160;</mo><mn>25</mn><mo>&#176;</mo></mrow><mrow><mi>sin</mi><mo>&#160;</mo><mn>65</mn><mo>&#176;</mo></mrow></mfrac></math><br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>sec</mi><mo>(</mo><mn>90</mn><mo>-</mo><mn>55</mn><mo>&#176;</mo><mo>)</mo></mrow><mrow><mi>cosec</mi><mi mathvariant=\"normal\">&#160;</mi><mn>55</mn><mo>&#176;</mo></mrow></mfrac></math> -&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>cos</mi><mo>(</mo><mn>90</mn><mo>-</mo><mn>65</mn><mo>&#176;</mo><mo>)</mo></mrow><mrow><mi>sin</mi><mo>&#160;</mo><mn>65</mn><mo>&#176;</mo></mrow></mfrac></math><br><math display=\"inline\"><mfrac><mrow><mi>c</mi><mi>o</mi><mi>s</mi><mi>e</mi><mi>c</mi><mi>&#160;</mi><mn>55</mn><mo>&#176;</mo></mrow><mrow><mi>c</mi><mi>o</mi><mi>s</mi><mi>e</mi><mi>c</mi><mi>&#160;</mi><mn>55</mn><mo>&#176;</mo></mrow></mfrac></math> -&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>sin</mi><mo>&#160;</mo><mn>65</mn><mo>&#176;</mo></mrow><mrow><mi>sin</mi><mo>&#160;</mo><mn>65</mn><mo>&#176;</mo></mrow></mfrac></math> = 1 - 1 = 0</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "63",
                    section: "17",
                    question_en: "<p>63. If the semi-perimeter and area of a rectangular field whose length and breadth are \'x\' and \'y\' is 12 cm and 28 cm&sup2;, respectively, then find the value of x<sup>4</sup> + x&sup2;y&sup2; + y<sup>4</sup>.</p>",
                    question_hi: "<p>63. यदि एक आयताकार मैदान जिसकी लंबाई और चौड़ाई \'x\' और \'y\' है, का अर्ध-परिधि और क्षेत्रफल क्रमशः 12 सेमी और 28 सेमी&sup2; है, तो x<sup>4</sup> + x&sup2;y&sup2; + y<sup>4</sup> का मान ज्ञात करें।</p>",
                    options_en: [
                        "<p>6690</p>",
                        "<p>6960</p>",
                        "<p>6609</p>",
                        "<p>6906</p>"
                    ],
                    options_hi: [
                        "<p>6690</p>",
                        "<p>6960</p>",
                        "<p>6609</p>",
                        "<p>6906</p>"
                    ],
                    solution_en: "<p>63.(b)<br>Semiperimeter = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><mo>(</mo><mi mathvariant=\"normal\">x</mi><mo>+</mo><mi mathvariant=\"normal\">y</mi><mo>)</mo></mrow><mn>2</mn></mfrac></math> = x + y = 12&nbsp;<br>Area of rectangle = 28<br>xy = 28&nbsp;<br>Now, (x + y)<sup>2</sup> = x<sup>2</sup>&nbsp;+ y<sup>2 </sup>+ (xy + xy)<br>(x + y)<sup>2 </sup>-&nbsp; xy = x<sup>2 </sup>+ y<sup>2 </sup>+ xy<br>12<sup>2</sup> - 28 = x<sup>2 </sup>+ y<sup>2 </sup>+ xy<br>116 = x<sup>2 </sup>+ y<sup>2&nbsp; </sup>+ xy<br>Again, (x + y)<sup>2</sup> = x<sup>2</sup>&nbsp;+ y<sup>2&nbsp; </sup>+ (3xy - xy)<br>(x + y)<sup>2 </sup>- 3xy = x<sup>2</sup>&nbsp;+ y<sup>2&nbsp; </sup>- xy<br>12<sup>2</sup> - 28 &times; 3 = x<sup>2 </sup>+ y<sup>2 </sup>- xy<br>60 = x<sup>2 </sup>+ y<sup>2 </sup>- xy<br>then, x<sup>4</sup> + x&sup2;y&sup2; + y<sup>4</sup> = (x<sup>2 </sup>+ y<sup>2 </sup>+ xy)(x<sup>2</sup>&nbsp;+ y<sup>2 </sup>- xy) = 116 &times; 60 = 6960</p>",
                    solution_hi: "<p>63.(b)<br>अर्धपरिमाप = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><mo>(</mo><mi mathvariant=\"normal\">x</mi><mo>+</mo><mi mathvariant=\"normal\">y</mi><mo>)</mo></mrow><mn>2</mn></mfrac></math> = x + y = 12&nbsp;<br>आयत का क्षेत्रफल = 28<br>xy= 28 <br>अब, (x + y)<sup>2</sup> = x<sup>2</sup>&nbsp;+ y<sup>2 </sup>+ (xy + xy)<br>(x + y)<sup>2 </sup>-&nbsp; xy = x<sup>2 </sup>+ y<sup>2 </sup>+ xy<br>12<sup>2</sup> - 28 = x<sup>2 </sup>+ y<sup>2 </sup>+ xy<br>116 = x<sup>2 </sup>+ y<sup>2&nbsp; </sup>+ xy<br>फिर से, (x + y)<sup>2</sup> = x<sup>2</sup>&nbsp;+ y<sup>2&nbsp; </sup>+ (3xy - xy)<br>(x + y)<sup>2 </sup>- 3xy = x<sup>2</sup>&nbsp;+ y<sup>2&nbsp; </sup>- xy<br>12<sup>2</sup> - 28 &times; 3 = x<sup>2 </sup>+ y<sup>2 </sup>- xy<br>60 = x<sup>2 </sup>+ y<sup>2 </sup>- xy<br>तो, x<sup>4</sup> + x&sup2;y&sup2; + y<sup>4</sup> = (x<sup>2 </sup>+ y<sup>2 </sup>+ xy)(x<sup>2</sup>&nbsp;+ y<sup>2 </sup>- xy) = 116 &times; 60 = 6960</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "64",
                    section: "17",
                    question_en: "<p>64. Which of the following is NOT true regarding a circle?</p>",
                    question_hi: "<p>64. वृत्त के संदर्भ में निम्नलिखित में से कौन-सा सत्य नहीं है?</p>",
                    options_en: [
                        "<p>Given any three points in a plane, there is always a circle passing through these points</p>",
                        "<p>The largest chord in a circle has a length twice its radius</p>",
                        "<p>The ratio of the perimeter and radius of a circle is always constant</p>",
                        "<p>A triangle with vertices on a circle and one side as its diameter is a right-angled triangle</p>"
                    ],
                    options_hi: [
                        "<p>यदि किसी समतल में कोई तीन बिंदु दिए गए हैं, तो वृत्त सदैव इन बिंदुओं से होकर गुजरेगा।</p>",
                        "<p>किसी वृत्त की सबसे बड़ी जीवा की लंबाई उसकी त्रिज्या से दोगुनी होती है।</p>",
                        "<p>किसी वृत्त का परिमाप और त्रिज्या का अनुपात सदैव नियत होता है।</p>",
                        "<p>एक त्रिभुज जिसके शीर्ष एक वृत्त पर हों और एक भुजा उसका व्यास हो, तो वह एक समकोण त्रिभुज होता है।</p>"
                    ],
                    solution_en: "<p>64.(a)<br>Option (a) is not true as &ldquo;There is no circle passing through any three points in any plane&rdquo;.</p>",
                    solution_hi: "<p>64.(a)<br>विकल्प (a) सत्य नहीं है क्योंकि किसी समतल में किन्हीं तीन बिंदुओं से होकर गुजरने वाला कोई वृत्त नहीं है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "65",
                    section: "17",
                    question_en: "<p>65. Simplify (2z - 5y)<sup>2</sup> + (5z + 2y)<sup>2</sup> - 25z<sup>2</sup>.</p>",
                    question_hi: "<p>65. (2z - 5y)<sup>2</sup> + (5z + 2y)<sup>2</sup> - 25z<sup>2</sup> का मान क्या होगा ?</p>",
                    options_en: [
                        "<p>19y<sup>2</sup> - 4z<sup>2</sup></p>",
                        "<p>29y<sup>2</sup> + 4z<sup>2</sup></p>",
                        "<p>29y<sup>2</sup> - 4z<sup>2</sup></p>",
                        "<p>19y<sup>2</sup> + 4z<sup>2</sup></p>"
                    ],
                    options_hi: [
                        "<p>19y<sup>2</sup> - 4z<sup>2</sup></p>",
                        "<p>29y<sup>2</sup> + 4z<sup>2</sup></p>",
                        "<p>29y<sup>2</sup> - 4z<sup>2</sup></p>",
                        "<p>19y<sup>2</sup> + 4z<sup>2</sup></p>"
                    ],
                    solution_en: "<p>65.(b) <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><mn>2</mn><mi mathvariant=\"normal\">z</mi><mo>-</mo><mn>5</mn><mi mathvariant=\"normal\">y</mi><mo>)</mo></mrow><mn>2</mn></msup></math>+ (5z + 2y)<sup>2</sup> - 25z<sup>2</sup><br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mn>4</mn><mi mathvariant=\"normal\">z</mi></mrow><mn>2</mn></msup><mi mathvariant=\"normal\">&#160;</mi><mo>+</mo><mn>25</mn><msup><mi mathvariant=\"normal\">y</mi><mn>2</mn></msup><mo>-</mo><mn>20</mn><mi>zy</mi></math>+ 25z<sup>2</sup> + 4y<sup>2</sup> + 20zy - 25z<sup>2</sup><br>= 29y<sup>2 </sup>+&nbsp; 4z<sup>2</sup></p>",
                    solution_hi: "<p>65.(b) <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><mn>2</mn><mi mathvariant=\"normal\">z</mi><mo>-</mo><mn>5</mn><mi mathvariant=\"normal\">y</mi><mo>)</mo></mrow><mn>2</mn></msup></math>+ (5z + 2y)<sup>2</sup> - 25z<sup>2</sup><br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mn>4</mn><mi mathvariant=\"normal\">z</mi></mrow><mn>2</mn></msup><mi mathvariant=\"normal\">&#160;</mi><mo>+</mo><mn>25</mn><msup><mi mathvariant=\"normal\">y</mi><mn>2</mn></msup><mo>-</mo><mn>20</mn><mi>zy</mi></math>+ 25z<sup>2</sup> + 4y<sup>2</sup> + 20zy - 25z<sup>2</sup><br>= 29y<sup>2 </sup>+&nbsp; 4z<sup>2</sup></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "66",
                    section: "17",
                    question_en: "<p>66. The price of pulses has increased by 45%. By what percentage (rounded off to the nearest integer) the increased price of the pulses should be reduced so that the price of the pulses remains unaltered?</p>",
                    question_hi: "<p>66. दालों के मूल्य में 45% की बढ़ोतरी हुई है। दालों के बढ़े हुए मूल्य में कितने प्रतिशत (निकटतम पूर्णांक तक सन्निकटित) की कमी की जानी चाहिए ताकि दालों का मूल्य अपरिवर्तित रहे?</p>",
                    options_en: [
                        "<p>35</p>",
                        "<p>41</p>",
                        "<p>45</p>",
                        "<p>31</p>"
                    ],
                    options_hi: [
                        "<p>35</p>",
                        "<p>41</p>",
                        "<p>45</p>",
                        "<p>31</p>"
                    ],
                    solution_en: "<p>66.(d)<br>Ratio -&nbsp; &nbsp; &nbsp; &nbsp; initial : after <br>Pulses price - 20 : 29<br>Required % = <math display=\"inline\"><mfrac><mrow><mn>9</mn></mrow><mrow><mn>29</mn></mrow></mfrac></math> &times; 100 = 31.03 % or 31%</p>",
                    solution_hi: "<p>66.(d)<br>अनुपात - प्रारंभिक : पश्चात <br>दालों का भाव - 20 : 29<br>आवश्यक % = <math display=\"inline\"><mfrac><mrow><mn>9</mn></mrow><mrow><mn>29</mn></mrow></mfrac></math> &times; 100 = 31.03 % या 31 %</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "67",
                    section: "17",
                    question_en: "<p>67. A sells a bike to B at 45% profit. Then B sells it to C at 30% profit. If C pays ₹43,355,&nbsp;then what is the cost price of the bike for A?</p>",
                    question_hi: "<p>67. A, B को 45% लाभ पर एक बाइक बेचता है। फिर B इसे C को 30% लाभ पर बेच देता है। यदि C,&nbsp;₹43,355 का भुगतान करता है, तो A के लिए बाइक का क्रय मूल्य ज्ञात है?</p>",
                    options_en: [
                        "<p>₹21,000</p>",
                        "<p>₹23,000</p>",
                        "<p>₹27,000</p>",
                        "<p>₹29,000</p>"
                    ],
                    options_hi: [
                        "<p>₹21,000</p>",
                        "<p>₹23,000</p>",
                        "<p>₹27,000</p>",
                        "<p>₹29,000</p>"
                    ],
                    solution_en: "<p>67.(b)<br>CP of the bike for A = ₹x<br>x &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>145</mn><mn>100</mn></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>130</mn><mn>100</mn></mfrac></math> = 43355<br><math display=\"inline\"><mi>x</mi></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>43355</mn><mo>&#215;</mo><mn>10000</mn></mrow><mrow><mn>145</mn><mo>&#215;</mo><mn>130</mn></mrow></mfrac></math> = ₹23,000</p>",
                    solution_hi: "<p>67.(b)<br>A के लिए बाइक का क्रय मूल्य =₹<math display=\"inline\"><mi>x</mi></math><br>x &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>145</mn><mn>100</mn></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>130</mn><mn>100</mn></mfrac></math> = 43355<br><math display=\"inline\"><mi>x</mi></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>43355</mn><mo>&#215;</mo><mn>10000</mn></mrow><mrow><mn>145</mn><mo>&#215;</mo><mn>130</mn></mrow></mfrac></math> = ₹23,000</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "68",
                    section: "17",
                    question_en: "<p>68. The following table shows the marks (out of 100) obtained by five students in five different subjects.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743393559686.png\" alt=\"rId52\" width=\"439\" height=\"125\"> <br>Who obtained 79% marks in all the subjects taken together?</p>",
                    question_hi: "<p>68. निम्नलिखित तालिका पांच अलग-अलग विषयों में पांच छात्रों द्वारा प्राप्त अंक (100 में से) दर्शाती है।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743393559802.png\" alt=\"rId53\" width=\"400\"> <br>सभी विषयों को मिलाकर 79% अंक किसने प्राप्त किए?</p>",
                    options_en: [
                        "<p>Sumit</p>",
                        "<p>Mohit</p>",
                        "<p>Rohit</p>",
                        "<p>Tarun</p>"
                    ],
                    options_hi: [
                        "<p>सुमित</p>",
                        "<p>मोहित</p>",
                        "<p>रोहित</p>",
                        "<p>तरुण</p>"
                    ],
                    solution_en: "<p>68.(d) <br>Sumit obtained percentage = <math display=\"inline\"><mfrac><mrow><mn>90</mn><mo>+</mo><mn>88</mn><mo>+</mo><mn>78</mn><mo>+</mo><mn>82</mn><mo>+</mo><mn>68</mn></mrow><mrow><mn>500</mn></mrow></mfrac></math> &times; 100 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>406</mn><mn>5</mn></mfrac></math> = 81.2%<br>Mohit obtained percentage = <math display=\"inline\"><mfrac><mrow><mn>73</mn><mo>+</mo><mn>84</mn><mo>+</mo><mn>77</mn><mo>+</mo><mn>90</mn><mo>+</mo><mn>95</mn></mrow><mrow><mn>500</mn></mrow></mfrac></math> &times; 100 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>419</mn><mn>5</mn></mfrac></math> = 83.8%<br>Rohit obtained percentage = <math display=\"inline\"><mfrac><mrow><mn>69</mn><mo>+</mo><mn>76</mn><mo>+</mo><mn>80</mn><mo>+</mo><mn>88</mn><mo>+</mo><mn>94</mn></mrow><mrow><mn>500</mn></mrow></mfrac></math> &times; 100 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>407</mn><mn>5</mn></mfrac></math> = 81.4%<br>Tarun obtained percentage = <math display=\"inline\"><mfrac><mrow><mn>65</mn><mo>+</mo><mn>80</mn><mo>+</mo><mn>75</mn><mo>+</mo><mn>85</mn><mo>+</mo><mn>90</mn></mrow><mrow><mn>500</mn></mrow></mfrac></math> &times; 100 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>395</mn><mn>5</mn></mfrac></math> = 79%<br>Hence, Tarun has obtained 79% marks in all the subjects</p>",
                    solution_hi: "<p>68.(d) <br>सुमित को प्राप्त प्रतिशत = <math display=\"inline\"><mfrac><mrow><mn>90</mn><mo>+</mo><mn>88</mn><mo>+</mo><mn>78</mn><mo>+</mo><mn>82</mn><mo>+</mo><mn>68</mn></mrow><mrow><mn>500</mn></mrow></mfrac></math> &times; 100 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>406</mn><mn>5</mn></mfrac></math> = 81.2%<br>मोहित को प्राप्त प्रतिशत = <math display=\"inline\"><mfrac><mrow><mn>73</mn><mo>+</mo><mn>84</mn><mo>+</mo><mn>77</mn><mo>+</mo><mn>90</mn><mo>+</mo><mn>95</mn></mrow><mrow><mn>500</mn></mrow></mfrac></math> &times; 100 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>419</mn><mn>5</mn></mfrac></math> = 83.8%<br>रोहित को प्राप्त प्रतिशत = <math display=\"inline\"><mfrac><mrow><mn>69</mn><mo>+</mo><mn>76</mn><mo>+</mo><mn>80</mn><mo>+</mo><mn>88</mn><mo>+</mo><mn>94</mn></mrow><mrow><mn>500</mn></mrow></mfrac></math> &times; 100 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>407</mn><mn>5</mn></mfrac></math> = 81.4%<br>तरुण को प्राप्त प्रतिशत = <math display=\"inline\"><mfrac><mrow><mn>65</mn><mo>+</mo><mn>80</mn><mo>+</mo><mn>75</mn><mo>+</mo><mn>85</mn><mo>+</mo><mn>90</mn></mrow><mrow><mn>500</mn></mrow></mfrac></math> &times; 100 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>395</mn><mn>5</mn></mfrac></math> = 79%<br>अतः, तरुण ने सभी विषयों में 79% अंक प्राप्त किए हैं।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "69",
                    section: "17",
                    question_en: "<p>69. In a circular race of 1500 m, P and Q start from the same point and at the same time, at speeds of 20 km/h and 35 km/h, respectively. After what time will they meet for the first time on the track if they are running in the same direction?</p>",
                    question_hi: "<p>69. 1500 m की एक वृत्ताकार दौड़ में, P और Q एक ही बिंदु से और एक ही समय पर क्रमशः 20 km/h और 35 km/h की चाल से दौड़ना शुरू करते हैं। यदि वे एक ही दिशा में दौड़ रहे हैं तो वे ट्रैक पर पहली बार कितने समय बाद मिलेंगे?</p>",
                    options_en: [
                        "<p>340 sec</p>",
                        "<p>640 sec</p>",
                        "<p>840 sec</p>",
                        "<p>360 sec</p>"
                    ],
                    options_hi: [
                        "<p>340 सेकंड</p>",
                        "<p>640 सेकंड</p>",
                        "<p>840 सेकंड</p>",
                        "<p>360 सेकंड</p>"
                    ],
                    solution_en: "<p>69.(d) Time when the meet for the first time on track =&nbsp;<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1500</mn><mrow><mo>(</mo><mn>35</mn><mo>-</mo><mn>20</mn><mo>)</mo><mo>&#215;</mo><mfrac><mn>5</mn><mn>18</mn></mfrac></mrow></mfrac><mi>&#160;</mi></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1500</mn><mo>&#215;</mo><mn>18</mn></mrow><mrow><mn>15</mn><mo>&#215;</mo><mn>5</mn></mrow></mfrac></math> = 360 sec</p>",
                    solution_hi: "<p>69.(d) ट्रैक पर पहली बार मिलने का समय =&nbsp;<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1500</mn><mrow><mo>(</mo><mn>35</mn><mo>-</mo><mn>20</mn><mo>)</mo><mo>&#215;</mo><mfrac><mn>5</mn><mn>18</mn></mfrac></mrow></mfrac><mi>&#160;</mi></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1500</mn><mo>&#215;</mo><mn>18</mn></mrow><mrow><mn>15</mn><mo>&#215;</mo><mn>5</mn></mrow></mfrac></math> = 360 सेकंड</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "70",
                    section: "17",
                    question_en: "<p>70. There is a constant growth rate (in %) every year in the production of a company as seen the given table.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743393559936.png\" alt=\"rId54\" width=\"347\" height=\"49\"> <br>Find the value of \'X\' from the data given in the table.</p>",
                    question_hi: "<p>70. जैसा कि दी गई तालिका में दर्शाया गया है, किसी कंपनी के उत्पादन में प्रतिवर्ष सतत वृद्धि दर (% में) होती है।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743393560025.png\" alt=\"rId55\" width=\"323\" height=\"61\"> <br>तालिका में दिए गए आँकड़ों से \'X\' का मान ज्ञात कीजिए।</p>",
                    options_en: [
                        "<p>12600</p>",
                        "<p>11880</p>",
                        "<p>12000</p>",
                        "<p>12960</p>"
                    ],
                    options_hi: [
                        "<p>12600</p>",
                        "<p>11880</p>",
                        "<p>12000</p>",
                        "<p>12960</p>"
                    ],
                    solution_en: "<p>70.(d)<br>Growth % in 2019 = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>10800</mn><mo>-</mo><mn>9000</mn></mrow><mn>9000</mn></mfrac></math> &times; 100 = 20%<br>Now,<br>Growth % in 2019 = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi mathvariant=\"normal\">x</mi><mo>-</mo><mn>10800</mn></mrow><mn>10800</mn></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math> 20% =&nbsp;<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi mathvariant=\"normal\">x</mi><mo>-</mo><mn>10800</mn></mrow><mn>10800</mn></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>5</mn></mfrac></math> = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi mathvariant=\"normal\">x</mi><mo>-</mo><mn>10800</mn></mrow><mn>10800</mn></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math> x = 12960</p>",
                    solution_hi: "<p>70.(d)<br>2019 में वृद्धि % = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>10800</mn><mo>-</mo><mn>9000</mn></mrow><mn>9000</mn></mfrac></math>&nbsp;&times; 100 = 20%<br>अब,<br>2019 में वृद्धि% =<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi mathvariant=\"normal\">x</mi><mo>-</mo><mn>10800</mn></mrow><mn>10800</mn></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math> 20% =&nbsp;<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi mathvariant=\"normal\">x</mi><mo>-</mo><mn>10800</mn></mrow><mn>10800</mn></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>5</mn></mfrac></math> =&nbsp; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi mathvariant=\"normal\">x</mi><mo>-</mo><mn>10800</mn></mrow><mn>10800</mn></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math> x = 12960</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "71",
                    section: "17",
                    question_en: "<p>71. An article is marked at ₹550. If it is sold at a discount of 40%, then the selling price becomes 10% more than its cost price. What is the cost price (in ₹)?</p>",
                    question_hi: "<p>71. एक वस्तु पर ₹550 अंकित है। यदि इसे 40% की छूट पर बेचा जाता है, तो विक्रय मूल्य इसके क्रय मूल्य से 10% अधिक हो जाता है। क्रय मूल्य (₹ में) ज्ञात करें।</p>",
                    options_en: [
                        "<p>220</p>",
                        "<p>330</p>",
                        "<p>300</p>",
                        "<p>200</p>"
                    ],
                    options_hi: [
                        "<p>220</p>",
                        "<p>330</p>",
                        "<p>300</p>",
                        "<p>200</p>"
                    ],
                    solution_en: "<p>71.(c) Let the CP of an article be 100 units <br>Given: MP of an article = ₹ 550<br>SP of an article = 550 &times; <math display=\"inline\"><mfrac><mrow><mn>60</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = ₹ 330<br>According to the question,<br>110 units = 330<br>100 units = <math display=\"inline\"><mfrac><mrow><mn>330</mn></mrow><mrow><mn>110</mn></mrow></mfrac></math> &times; 100 = ₹ 300</p>",
                    solution_hi: "<p>71.(c) माना , वस्तु का क्र.मू. = 100 इकाई <br>दिया गया है: एक वस्तु का अंकित मूल्य = ₹ 550<br>एक वस्तु का विक्रय मूल्य = 550 &times; <math display=\"inline\"><mfrac><mrow><mn>60</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = ₹ 330<br>प्रश्न के अनुसार,<br>110 इकाई = 330<br>100 इकाई =&nbsp;<math display=\"inline\"><mfrac><mrow><mn>330</mn></mrow><mrow><mn>110</mn></mrow></mfrac></math> &times; 100 = ₹ 300</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "72",
                    section: "17",
                    question_en: "<p>72. Pipe X can fill a tank in 60 hours while pipe Y can fill the tank in 72 hours. Both pipes are opened together for 20 hours. How much of the tank is left empty ?</p>",
                    question_hi: "<p>72. पाइप X एक टंकी को 60 घंटे में भर सकती है जबकि पाइप Y उस टंकी को 72 घंटे में भर सकता है। दोनों पाइपों को एक साथ 20 घंटे के लिए खोला जाता है। टंकी का कितना भाग खाली बचेगा?</p>",
                    options_en: [
                        "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>243</mn></mrow><mrow><mn>360</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>7</mn></mrow><mrow><mn>18</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math></p>"
                    ],
                    options_hi: [
                        "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>243</mn></mrow><mrow><mn>360</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>7</mn></mrow><mrow><mn>18</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math></p>"
                    ],
                    solution_en: "<p>72.(c) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743393560131.png\" alt=\"rId56\" width=\"224\" height=\"167\"><br>Tank fill in 20hr by both the pipe = (6 + 5 ) &times; 20 = 220<br>Part of tank is left empty = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>360</mn><mo>-</mo><mn>220</mn></mrow><mn>360</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>140</mn><mn>360</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>18</mn></mfrac></math></p>",
                    solution_hi: "<p>72.(c) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743393560241.png\" alt=\"rId57\" width=\"190\" height=\"160\"><br>दोनों पाइपों द्वारा 20 घंटे में भरा गया टंकी का भाग&nbsp; = (6 + 5 ) &times; 20 = 220<br>टंकी का&nbsp; खाली&nbsp; बचा भाग&nbsp;<strong id=\"docs-internal-guid-92a1765b-7fff-6427-08bc-2a37a974cad7\"> </strong>=&nbsp;<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>360</mn><mo>-</mo><mn>220</mn></mrow><mn>360</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>140</mn><mn>360</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>18</mn></mfrac></math></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "73",
                    section: "17",
                    question_en: "<p>73. {Sum of multiples of 5 from 15 to 30 + HCF of 24 and 40 - smallest prime number} is equal to</p>",
                    question_hi: "<p>73. {15 से 30 तक 5 के गुणजों का योगफल + 24 और 40 का HCF - सबसे छोटी अभाज्य संख्या} बराबर है</p>",
                    options_en: [
                        "<p>92</p>",
                        "<p>93</p>",
                        "<p>96</p>",
                        "<p>97</p>"
                    ],
                    options_hi: [
                        "<p>92</p>",
                        "<p>93</p>",
                        "<p>96</p>",
                        "<p>97</p>"
                    ],
                    solution_en: "<p>73.(c)<br>Multiple of 5 from 15 to 30 = 15, 20, 25, 30.<br>HCF of (24, 40) = 8<br>Smallest prime number = 2<br>According to question,<br>[(15 + 20 + 25 + 30) + 8 - 2] = [90 + 6] = 96</p>",
                    solution_hi: "<p>73.(c)<br>15 से 30 तक 5 का गुणज = 15, 20, 25, 30.<br>(24, 40) का HCF = 8<br>सबसे छोटी अभाज्य संख्या = 2<br>प्रश्न के अनुसार,<br>[(15 + 20 + 25 + 30) + 8 - 2] = [90 + 6] = 96</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "74",
                    section: "17",
                    question_en: "<p>74. A ladder is placed against a wall such that its foot is at a distance of 2.5 m from the wall and its top reaches the base of a window 6 m above the ground. Find the length of the ladder?</p>",
                    question_hi: "<p>74. एक सीढ़ी एक दीवार के सहारे इस प्रकार खड़ी है कि दीवार से उसके पाद बिंदु की दूरी 2.5 m है और उसका शीर्ष भूमि से 6m की ऊंचाई पर स्थित एक खिड़की के आधार को स्पर्श करता है। सीढ़ी की लंबाई ज्ञात कीजिए ।</p>",
                    options_en: [
                        "<p>6.3 m</p>",
                        "<p>7.5 m</p>",
                        "<p>6.5 m</p>",
                        "<p>7.8 m</p>"
                    ],
                    options_hi: [
                        "<p>6.3 m</p>",
                        "<p>7.5 m</p>",
                        "<p>6.5 m</p>",
                        "<p>7.8 m</p>"
                    ],
                    solution_en: "<p>74.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743393560386.png\" alt=\"rId58\" width=\"144\" height=\"143\"><br>Length of ladder = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mn>6</mn><mn>2</mn></msup><mo>+</mo><msup><mrow><mo>(</mo><mn>2</mn><mo>.</mo><mn>5</mn><mo>)</mo></mrow><mn>2</mn></msup></msqrt><mo>=</mo><msqrt><mn>36</mn><mo>+</mo><mn>6</mn><mo>.</mo><mn>25</mn></msqrt><mo>=</mo><msqrt><mn>42</mn><mo>.</mo><mn>25</mn></msqrt><mo>=</mo><mn>6</mn><mo>.</mo><mn>5</mn><mi mathvariant=\"normal\">&#160;</mi><mi mathvariant=\"normal\">m</mi></math></p>",
                    solution_hi: "<p>74.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743393560520.png\" alt=\"rId59\" width=\"146\" height=\"154\"><br>सीढ़ी की लंबाई =&nbsp;<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mn>6</mn><mn>2</mn></msup><mo>+</mo><msup><mrow><mo>(</mo><mn>2</mn><mo>.</mo><mn>5</mn><mo>)</mo></mrow><mn>2</mn></msup></msqrt><mo>=</mo><msqrt><mn>36</mn><mo>+</mo><mn>6</mn><mo>.</mo><mn>25</mn></msqrt><mo>=</mo><msqrt><mn>42</mn><mo>.</mo><mn>25</mn></msqrt><mo>=</mo><mn>6</mn><mo>.</mo><mn>5</mn><mi mathvariant=\"normal\">&#160;</mi><mi mathvariant=\"normal\">m</mi></math></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "75",
                    section: "7",
                    question_en: "<p>75. Two equal circles are drawn on a square in such a way that opposite side of the square forms the diameter of each circle. If the remaining area of the square is 42 cm&sup2;, what is the measurement (in cm) of the diameter of each circle?</p>",
                    question_hi: "<p>75. एक वर्ग के अंदर दो समान वृत्त इस प्रकार बनाए जाते हैं कि वर्ग की विपरीत भुजा प्रत्येक वृत्त का व्यास बनाती है। यदि वर्ग का शेष क्षेत्रफल 42 cm&sup2; है, तो प्रत्येक वृत्त के व्यास की माप (cm में) कितनी है?</p>",
                    options_en: [
                        "<p>14</p>",
                        "<p>2.5</p>",
                        "<p>7</p>",
                        "<p>3.5</p>"
                    ],
                    options_hi: [
                        "<p>14</p>",
                        "<p>2.5</p>",
                        "<p>7</p>",
                        "<p>3.5</p>"
                    ],
                    solution_en: "<p>75.(a)<br>Area of remaining area = area of square - 2 &times; area of semicircle <br>According to the question,<br>42 = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><mn>2</mn><mi mathvariant=\"normal\">r</mi><mo>)</mo></mrow><mn>2</mn></msup></math> - 2 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math> &times; &pi; &times; (r)&sup2;<br>42 = 4r&sup2; - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>22</mn><mn>7</mn></mfrac></math> &times; (r)&sup2;<br>42 = r&sup2;(4 - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>22</mn><mn>7</mn></mfrac></math>)<br>42 = r&sup2;&nbsp;( <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>6</mn><mn>7</mn></mfrac></math>)<br>r&sup2; = 42 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>6</mn></mfrac></math>= 49<br>r = 7<br>So, diamete<math display=\"inline\"><mi>r</mi></math> of each circle (2r) = 14 cm</p>",
                    solution_hi: "<p>75.(a)<br>शेष क्षेत्र का क्षेत्रफल = वर्ग का क्षेत्रफल - 2 &times; अर्धवृत्त का क्षेत्रफल <br>प्रश्न के अनुसार,<br>42 = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><mn>2</mn><mi mathvariant=\"normal\">r</mi><mo>)</mo></mrow><mn>2</mn></msup></math> - 2 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math> &times; &pi; &times; (r)&sup2;<br>42 = 4r&sup2; - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>22</mn><mn>7</mn></mfrac></math> &times; (r)&sup2;<br>42 = r&sup2;(4 - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>22</mn><mn>7</mn></mfrac></math>)<br>42 = r&sup2;&nbsp;( <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>6</mn><mn>7</mn></mfrac></math>)<br>r&sup2; = 42 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>6</mn></mfrac></math>= 49<br>r = 7<br>अतः, प्रत्येक वृत्त का व्यास (2r) = 14 सेमी</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "76",
                    section: "7",
                    question_en: "<p>76. Find the correctly spelt word</p>",
                    question_hi: "<p>76. Find the correctly spelt word</p>",
                    options_en: [
                        "<p>capracious</p>",
                        "<p>auspicious</p>",
                        "<p>fallcious</p>",
                        "<p>dalicious</p>"
                    ],
                    options_hi: [
                        "<p>capracious</p>",
                        "<p>auspicious</p>",
                        "<p>fallcious</p>",
                        "<p>dalicious</p>"
                    ],
                    solution_en: "<p>76.(b) auspicious</p>",
                    solution_hi: "<p>76.(b) auspicious</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "77",
                    section: "7",
                    question_en: "<p>77. Choose the option that is the passive form of the sentence<br>They need 104 more runs to win the match.</p>",
                    question_hi: "<p>77. Choose the option that is the passive form of the sentence<br>They need 104 more runs to win the match.</p>",
                    options_en: [
                        "<p>104 more runs are needed for them to win the match.</p>",
                        "<p>104 more runs will need for them to win the match.</p>",
                        "<p>They are needed 104 more runs to win the match.</p>",
                        "<p>104 more runs can be needed by them to win the match.</p>"
                    ],
                    options_hi: [
                        "<p>104 more runs are needed for them to win the match.</p>",
                        "<p>104 more runs will need for them to win the match.</p>",
                        "<p>They are needed 104 more runs to win the match.</p>",
                        "<p>104 more runs can be needed by them to win the match.</p>"
                    ],
                    solution_en: "<p>77.(a) 104 more runs are needed for them to win the match. (Correct)<br>(b) 104 more runs <span style=\"text-decoration: underline;\">will need</span> for them to win the match. (Tense has changed)<br>(c) <span style=\"text-decoration: underline;\">They are needed 104 more</span> runs to win the match. (Incorrect Structure and Subject)<br>(d) 104 more runs <span style=\"text-decoration: underline;\">can be</span> needed by them to win the match. (Tense has changed)</p>",
                    solution_hi: "<p>77.(a) 104 more runs are needed for them to win the match. (Correct)<br>(b) 104 more runs <span style=\"text-decoration: underline;\">will need</span> for them to win the match. (गलत tense (simple future) का प्रयोग किया गया है | are needed (simple present) का प्रयोग होगा । ) <br>(c) <span style=\"text-decoration: underline;\">They are needed 104 more runs</span> to win the match. (Sentence Structure सही नहीं है) <br>(d) 104 more runs <span style=\"text-decoration: underline;\">can be</span> needed by them to win the match. (गलत tense (simple future) का प्रयोग किया गया है | )</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "78",
                    section: "7",
                    question_en: "<p>78. Identify the segment in the sentence which contains grammatical error. <br>The CEO of the company had gone abroad on an official visit but she is come back now.</p>",
                    question_hi: "<p>78. Identify the segment in the sentence which contains grammatical error. <br>The CEO of the company had gone abroad on an official visit but she is come back now.</p>",
                    options_en: [
                        "<p>The CEO of the company</p>",
                        "<p>had gone abroad</p>",
                        "<p>on an official visit</p>",
                        "<p>but she is come back now</p>"
                    ],
                    options_hi: [
                        "<p>The CEO of the company</p>",
                        "<p>had gone abroad</p>",
                        "<p>on an official visit</p>",
                        "<p>but she is come back now</p>"
                    ],
                    solution_en: "<p>78.(d) but she is come back now<br>The last part of the given sentence should be in the &ldquo;present perfect tense&rdquo; and the verb should be used in its &ldquo;present perfect form&rdquo;(has + V<sub>3</sub>). Hence, &lsquo;she has come back now&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>78.(d) दिए गए वाक्य का अंतिम भाग &ldquo;present perfect tense&rdquo; में होना चाहिए इसलिए क्रिया का &ldquo;present perfect form&rdquo; में उपयोग किया जाना चाहिए (has + V<sub>3</sub>)। इसलिए, \'she has come back now\' सबसे उपयुक्त उत्तर है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "79",
                    section: "7",
                    question_en: "<p>79. Select the most appropriate option that can substitute the underlined segment in the given sentence.</p>\n<p>The author&rsquo;s writing style is characterised by its&nbsp;<span style=\"text-decoration: underline;\"><strong>poetic</strong> <strong>language.</strong></span></p>",
                    question_hi: "<p>79. Select the most appropriate option that can substitute the underlined segment in the given sentence.</p>\n<p>The author&rsquo;s writing style is characterised by its&nbsp;<span style=\"text-decoration: underline;\"><strong>poetic</strong> <strong>language.</strong></span></p>",
                    options_en: [
                        "<p>simple language</p>",
                        "<p>lucid language</p>",
                        "<p>prosaic language</p>",
                        "<p>lyrical language</p>"
                    ],
                    options_hi: [
                        "<p>simple language</p>",
                        "<p>lucid language</p>",
                        "<p>prosaic language</p>",
                        "<p>lyrical language</p>"
                    ],
                    solution_en: "<p>79.(d) lyrical language<br>&lsquo;Lyrical&rsquo; means poetic and romantic. The given sentence states that the author&rsquo;s writing style is characterised by its poetic language. Hence, &lsquo;lyrical language&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>79.(d) lyrical language<br>&lsquo;Lyrical&rsquo; का अर्थ है poetic और romantic। दिए गए sentence में कहा गया है कि author की writing style की विशेषता उसकी poetic language है। इसलिए, &lsquo;lyrical language&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "80",
                    section: "7",
                    question_en: "<p>80. Select the word which means the same as the group of words given.<br>The line when the land and sky seems to meet</p>",
                    question_hi: "<p>80. Select the word which means the same as the group of words given.<br>The line when the land and sky seems to meet</p>",
                    options_en: [
                        "<p>Atmosphere</p>",
                        "<p>Milky Way</p>",
                        "<p>Horizon</p>",
                        "<p>Distant land</p>"
                    ],
                    options_hi: [
                        "<p>Atmosphere</p>",
                        "<p>Milky Way</p>",
                        "<p>Horizon</p>",
                        "<p>Distant land</p>"
                    ],
                    solution_en: "<p>80.(c) Horizon<br>Horizon - the line at which the earth\'s surface and the sky appear to meet.</p>",
                    solution_hi: "<p>80.(c) Horizon<br>Horizon - वह रेखा जिस पर पृथ्वी की सतह और आकाश मिलते हुए प्रतीत होते हैं।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "81",
                    section: "7",
                    question_en: "<p>81. Select the most appropriate idiom to fill in the blank.<br>John was always_______about becoming a millionaire without doing any hard work.</p>",
                    question_hi: "<p>81. Select the most appropriate idiom to fill in the blank.<br>John was always_______about becoming a millionaire without doing any hard work.</p>",
                    options_en: [
                        "<p>building castles in the air</p>",
                        "<p>eating like a horse</p>",
                        "<p>having an ace up his sleeve</p>",
                        "<p>a storm in a tea cup</p>"
                    ],
                    options_hi: [
                        "<p>building castles in the air</p>",
                        "<p>eating like a horse</p>",
                        "<p>having an ace up his sleeve</p>",
                        "<p>a storm in a tea cup</p>"
                    ],
                    solution_en: "<p>81.(a) <strong>building castles in the air</strong> - thinking of some impossible task.</p>",
                    solution_hi: "<p>81.(a) <strong>building castles in the air</strong> - thinking of some impossible task./किसी असंभव कार्य के बारे में सोचना।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "82",
                    section: "7",
                    question_en: "<p>82. Select the option that expresses the given sentence in active voice. <br>The room will have been cleaned by the servant.</p>",
                    question_hi: "<p>82. Select the option that expresses the given sentence in active voice. <br>The room will have been cleaned by the servant.</p>",
                    options_en: [
                        "<p>The servant will have cleaned the room.</p>",
                        "<p>The servant will have clean the room.</p>",
                        "<p>The servant will clean the room.</p>",
                        "<p>The servant should clean the room.</p>"
                    ],
                    options_hi: [
                        "<p>The servant will have cleaned the room.</p>",
                        "<p>The servant will have clean the room.</p>",
                        "<p>The servant will clean the room.</p>",
                        "<p>The servant should clean the room.</p>"
                    ],
                    solution_en: "<p>82.(a) The servant will have cleaned the room. (Correct)<br>(b) The servant will have <span style=\"text-decoration: underline;\">clean</span> the room. (Incorrect form of Verb)<br>(c) The servant <span style=\"text-decoration: underline;\">will clean</span> the room. (Incorrect Verb)<br>(d) The servant <span style=\"text-decoration: underline;\">should clean</span> the room. (Incorrect Verb)</p>",
                    solution_hi: "<p>82.(a) The servant will have cleaned the room. (Correct)<br>(b) The servant <span style=\"text-decoration: underline;\">will</span> have clean the room. ( Verb की form गलत)<br>(c) The servant <span style=\"text-decoration: underline;\">will clean</span> the room. (गलत Verb)<br>(d) The servant <span style=\"text-decoration: underline;\">should clean</span> the room. (गलत Verb)</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "83",
                    section: "7",
                    question_en: "<p>83. Select the most appropriate option to fill in the blank.<br>I have observed that there is proper natural ____________ even at 6 o\'clock on summer mornings.</p>",
                    question_hi: "<p>83. Select the most appropriate option to fill in the blank.<br>I have observed that there is proper natural ____________ even at 6 o\'clock on summer mornings.</p>",
                    options_en: [
                        "<p>light</p>",
                        "<p>gleam</p>",
                        "<p>lucent</p>",
                        "<p>sparkle</p>"
                    ],
                    options_hi: [
                        "<p>light</p>",
                        "<p>gleam</p>",
                        "<p>lucent</p>",
                        "<p>sparkle</p>"
                    ],
                    solution_en: "<p>83.(a) <strong>light</strong><br>The given sentence states that I have observed that there is proper natural light even at 6 o&rsquo;clock on summer mornings. Hence, &lsquo;light&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>83.(a) <strong>light</strong><br>दिए गए sentence में कहा गया है कि मैंने देखा है कि गर्मियों की सुबह 6 बजे भी पर्याप्त प्राकृतिक प्रकाश (proper natural light) रहता है। अतः, &lsquo;light&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "84",
                    section: "7",
                    question_en: "<p>84. Given below are four jumbled sentences. Pick the option that gives their correct order.<br>(A) He sprinkled a pinch of ashes on it, and, lo! it sprouted blossoms.<br>(B) The tree became a cloud of pink blooms that perfumed the air.&nbsp;<br>(C) On coming home, the old man took his wife into the garden.<br>(D) It being winter, their favourite cherry tree was bare.</p>",
                    question_hi: "<p>84. Given below are four jumbled sentences. Pick the option that gives their correct order.<br>(A) He sprinkled a pinch of ashes on it, and, lo! it sprouted blossoms.<br>(B) The tree became a cloud of pink blooms that perfumed the air.&nbsp;<br>(C) On coming home, the old man took his wife into the garden.<br>(D) It being winter, their favourite cherry tree was bare.</p>",
                    options_en: [
                        "<p>DCBA</p>",
                        "<p>CDAB</p>",
                        "<p>ABCD</p>",
                        "<p>BCDA</p>"
                    ],
                    options_hi: [
                        "<p>DCBA</p>",
                        "<p>CDAB</p>",
                        "<p>ABCD</p>",
                        "<p>BCDA</p>"
                    ],
                    solution_en: "<p>84.(b) <strong>CDAB</strong><br>Sentence C will be the starting line as it contains the main idea of the parajumble i.e.the old man took his wife to the garden. Sentence D states that there because of winter the cherry tree was bare . So, D will follow C . Further, Sentence A states that he sprinkled ashes on it and Sentence B states that after sprinkling the ashes, the tree sprouted blossoms . So, B will follow A. Going through the options, option (b) has the correct sequence.</p>",
                    solution_hi: "<p>84.(b) <strong>CDAB</strong><br>Sentence C प्रारंभिक line होगी क्योंकि इसमें parajumble का मुख्य विचार है यानी बूढ़ा व्यक्ति अपनी पत्नी को बगीचे में ले गया। Sentence D कहता है कि वहाँ सर्दियों के कारण cherry का पेड़ खाली था। अतः C के बाद D आएगा। इसके अलावा, Sentence A कहता है कि उसने उस पर राख छिड़की और Sentence B कहता है कि राख छिड़कने के बाद, पेड़ पर फूल उग आए । तो, A के बाद B आएगा । विकल्पों के माध्यम से, विकल्प (b) में सही क्रम है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "85",
                    section: "7",
                    question_en: "<p>85. Select the sentence that has the correct use of words and spellings from the options given below.</p>",
                    question_hi: "<p>85. Select the sentence that has the correct use of words and spellings from the options given below.</p>",
                    options_en: [
                        "<p>The professional athlete pushed her body to the limit, surpasing all expectations and acheving a new personal record.</p>",
                        "<p>The profesional athlete pushed her body to the limit, surpassing all expectations and acheiving a new personal record.</p>",
                        "<p>The professional athlete pushed her body to the limit, surpassing all expectations and achieving a new personal record.</p>",
                        "<p>The profesional athlete pushed her body to the limit, surpasing all expectasions and achieving a new personal record.</p>"
                    ],
                    options_hi: [
                        "<p>The professional athlete pushed her body to the limit, surpasing all expectations and acheving a new personal record.</p>",
                        "<p>The profesional athlete pushed her body to the limit, surpassing all expectations and acheiving a new personal record.</p>",
                        "<p>The professional athlete pushed her body to the limit, surpassing all expectations and achieving a new personal record.</p>",
                        "<p>The profesional athlete pushed her body to the limit, surpasing all expectasions and achieving a new personal record.</p>"
                    ],
                    solution_en: "<p>85.(c) The <span style=\"text-decoration: underline;\">professional</span> athlete pushed her body to the limit, <span style=\"text-decoration: underline;\">surpassing</span> all <span style=\"text-decoration: underline;\">expectations</span> and <span style=\"text-decoration: underline;\">achieving</span> a new personal record.</p>",
                    solution_hi: "<p>85.(c) The <span style=\"text-decoration: underline;\">professional</span> athlete pushed her body to the limit, <span style=\"text-decoration: underline;\">surpassing</span> all <span style=\"text-decoration: underline;\">expectations</span> and <span style=\"text-decoration: underline;\">achieving</span> a new personal record.</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "86",
                    section: "7",
                    question_en: "<p>86. Identify the best way to improve the underlined part of the given sentence. If there is no improvement required, select &lsquo;no improvement&rsquo;.<br>I want to <span style=\"text-decoration: underline;\"><strong>admit</strong></span> in a university in the US.</p>",
                    question_hi: "<p>86. Identify the best way to improve the underlined part of the given sentence. If there is no improvement required, select &lsquo;no improvement&rsquo;.<br>I want to <span style=\"text-decoration: underline;\"><strong>admit</strong></span> in a university in the US.</p>",
                    options_en: [
                        "<p>enroll</p>",
                        "<p>No improvement</p>",
                        "<p>enter</p>",
                        "<p>go</p>"
                    ],
                    options_hi: [
                        "<p>enroll</p>",
                        "<p>No improvement</p>",
                        "<p>enter</p>",
                        "<p>go</p>"
                    ],
                    solution_en: "<p>86.(a) enroll, Enroll in means to formally enter or register in a roll, list, or record, school ,college, university etc.</p>",
                    solution_hi: "<p>86.(a) enroll , Enroll का अर्थ है किसी सूची, या रिकॉर्ड, स्कूल, कॉलेज, विश्वविद्यालय आदि में औपचारिक रूप से प्रवेश या पंजीकरण करना।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "87",
                    section: "7",
                    question_en: "<p>87. Select the word which means the same as the group of words given.<br>Only on the surface of something</p>",
                    question_hi: "<p>87. Select the word which means the same as the group of words given.<br>Only on the surface of something</p>",
                    options_en: [
                        "<p>Supercilious</p>",
                        "<p>Superlative</p>",
                        "<p>Superseding</p>",
                        "<p>Superficial</p>"
                    ],
                    options_hi: [
                        "<p>Supercilious</p>",
                        "<p>Superlative</p>",
                        "<p>Superseding</p>",
                        "<p>Superficial</p>"
                    ],
                    solution_en: "<p>87.(d) <strong>Superficial-</strong> Only on the surface of something.<br><strong>Supercilious-</strong> showing that you think that you are better than other people<br><strong>Superlative-</strong> the form of an adjective or adverb that expresses its highest degree<br><strong>Superseding-</strong> to take the place of somebody/something which existed</p>",
                    solution_hi: "<p>87.(d) <strong>Superficial</strong> - केवल किसी चीज की सतह पर.<br><strong>Supercilious</strong> - आप सोचते हैं कि आप अन्य लोगों से बेहतर हैं; घमंडी<br><strong>Superlative</strong> - एक विशेषण (adjective) या क्रिया विशेषण(adverb) का रूप जो इसकी उच्चतम डिग्री को व्यक्त करता है<br><strong>Superseding</strong> - किसी ऐसे की जगह लेना जो पहले मौजूद था</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "88",
                    section: "7",
                    question_en: "<p>88. Given below are four jumbled sentences. Pick the option that gives their correct order.<br>(A) It won partly through the use of a weapon of mass destruction.<br>(B) After the Japanese surrender, the United States occupied and ruled over Japan for several years.<br>(C) The United States won a famous victory over Japan in the Second World War.<br>(D) This weapon was never seen before in human history, the nuclear bomb.</p>",
                    question_hi: "<p>88. Given below are four jumbled sentences. Pick the option that gives their correct order.<br>(A) It won partly through the use of a weapon of mass destruction.<br>(B) After the Japanese surrender, the United States occupied and ruled over Japan for several years.<br>(C) The United States won a famous victory over Japan in the Second World War.<br>(D) This weapon was never seen before in human history, the nuclear bomb.</p>",
                    options_en: [
                        "<p>CADB</p>",
                        "<p>ADBC</p>",
                        "<p>ACBD</p>",
                        "<p>CBDA</p>"
                    ],
                    options_hi: [
                        "<p>CADB</p>",
                        "<p>ADBC</p>",
                        "<p>ACBD</p>",
                        "<p>CBDA</p>"
                    ],
                    solution_en: "<p>88.(a) <strong>CADB</strong><br>Sentence C will be the starting line as it contains the main idea of the parajumble i.e. victory of the United States over Japan. Sentence A states the way it won i.e. it won through the use of weapon of mass destruction . So, A will follow C. Further, Sentence D states the name of that weapon, the nuclear bomb and Sentence B states that after the surrender of the Japanese the United States ruled over them. So, B will follow D. Going through the options, option (a) has the correct sequence.</p>",
                    solution_hi: "<p>88.(a) <strong>CADB</strong><br>Sentence C starting line होगी क्योंकि इसमें parajumble का मुख्य विचार &lsquo;victory of the United States over Japan&rsquo; शामिल है। Sentence A में यह बताया गया है कि इसने किस तरह से जीत हासिल की अर्थात सामूहिक विनाश के हथियार के इस्तेमाल से जीत हासिल की। तो, C के बाद A आएगा । आगे, Sentence D उस हथियार का नाम बताता है, परमाणु बम और Sentence B कहता है कि जापानियों के आत्मसमर्पण के बाद संयुक्त राज्य ने उन पर शासन किया। तो, D के बाद B आएगा । options के माध्यम से जाने पर , option (a) में सही sequence है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "89",
                    section: "7",
                    question_en: "<p>89. Select the sentence that has a grammatical error.</p>",
                    question_hi: "<p>89. Select the sentence that has a grammatical error.</p>",
                    options_en: [
                        "<p>It is said that Joseph was not ready to go to war.</p>",
                        "<p>Karl Marx was a renowned social scientist.</p>",
                        "<p>Indian force are known for their loyalty and integrity.</p>",
                        "<p>It is impossible to wake Christine up in the morning.</p>"
                    ],
                    options_hi: [
                        "<p>It is said that Joseph was not ready to go to war.</p>",
                        "<p>Karl Marx was a renowned social scientist.</p>",
                        "<p>Indian force are known for their loyalty and integrity.</p>",
                        "<p>It is impossible to wake Christine up in the morning.</p>"
                    ],
                    solution_en: "<p>89.(c) Indian force are known for their loyalty and integrity.<br>&lsquo;Forces&rsquo; will be used instead of &lsquo;force&rsquo; according to the plural verb &lsquo;are&rsquo; and the plural adjective &lsquo;their&rsquo;. Hence, the correct sentence is: &ldquo;Indian forces are known for their loyalty and integrity.&rdquo;</p>",
                    solution_hi: "<p>89.(c) Indian force are known for their loyalty and integrity.<br>Plural verb &lsquo;are&rsquo; और plural adjective &lsquo;their&rsquo; के अनुसार &lsquo;force&rsquo; के स्थान पर &lsquo;forces&rsquo; का use किया जाएगा। अतः, &ldquo;Indian forces are known for their loyalty and integrity&rdquo; सही sentence है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "90",
                    section: "7",
                    question_en: "<p>90. Select the correct synonym of given word.<br><strong>Opulence</strong></p>",
                    question_hi: "<p>90. Select the correct synonym of given word.<br><strong>Opulence</strong></p>",
                    options_en: [
                        "<p>View</p>",
                        "<p>Sagacity</p>",
                        "<p>Recognition</p>",
                        "<p>Prosperity</p>"
                    ],
                    options_hi: [
                        "<p>View</p>",
                        "<p>Sagacity</p>",
                        "<p>Recognition</p>",
                        "<p>Prosperity</p>"
                    ],
                    solution_en: "<p>90.(d) <strong>Opulence-</strong> the quality of being expensive and luxurious<br><strong>Prosperity-</strong> the state of being successful, especially with money<br><strong>View-</strong> an opinion or a particular way of thinking about something<br><strong>Sagacity-</strong> the quality of having or showing understanding and the ability to make good judgments<br><strong>Recognition-</strong> the fact that you can identify somebody/something that you see</p>",
                    solution_hi: "<p>90.(d) <strong>Opulence</strong> (अधिकता) - the quality of being expensive and luxurious<br><strong>Prosperity</strong> (समृद्धि)- the state of being successful, especially with money<br><strong>View</strong> (राय)- an opinion or a particular way of thinking about something<br><strong>Sagacity </strong>(बुद्धिमत्ता)- the quality of having or showing understanding and the ability to make good judgments<br><strong>Recognition</strong> (मान्यता)- the fact that you can identify somebody/something that you see</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "91",
                    section: "7",
                    question_en: "<p>91. Select the most appropriate synonym of the underlined word. <br>I will <span style=\"text-decoration: underline;\">strive</span> to win the race.</p>",
                    question_hi: "<p>91. Select the most appropriate synonym of the underlined word. <br>I will <span style=\"text-decoration: underline;\">strive</span> to win the race.</p>",
                    options_en: [
                        "<p>Attempt</p>",
                        "<p>Support</p>",
                        "<p>Assume</p>",
                        "<p>trouble</p>"
                    ],
                    options_hi: [
                        "<p>Attempt</p>",
                        "<p>Support</p>",
                        "<p>Assume</p>",
                        "<p>trouble</p>"
                    ],
                    solution_en: "<p>91.(a) <strong>Attempt-</strong> to try or make an effort to achieve something.<br><strong>Strive-</strong> to make great efforts to achieve or obtain something.<br><strong>Support-</strong> to help or assist.<br><strong>Assume-</strong> to believe something to be true, without proof.<br><strong>Trouble-</strong> difficulty or problems.</p>",
                    solution_hi: "<p>91.(a) <strong>Attempt</strong> (प्रयास करना) - to try or make an effort to achieve something.<br><strong>Strive</strong> (प्रयत्न करना) - to make great efforts to achieve or obtain something.<br><strong>Support</strong> (सहयोग करना) - to help or assist.<br><strong>Assume</strong> (मानना) - to believe something to be true, without proof.<br><strong>Trouble</strong> (समस्या) - difficulty or problems.</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "92",
                    section: "7",
                    question_en: "<p>92. Pick a word opposite in meaning to<br>Variance</p>",
                    question_hi: "<p>92. Pick a word opposite in meaning to<br><strong>Variance</strong></p>",
                    options_en: [
                        "<p>Convention</p>",
                        "<p>Equal</p>",
                        "<p>Harmony</p>",
                        "<p>Intellectual</p>"
                    ],
                    options_hi: [
                        "<p>Convention</p>",
                        "<p>Equal</p>",
                        "<p>Harmony</p>",
                        "<p>Intellectual</p>"
                    ],
                    solution_en: "<p>92.(c) <strong>Harmony-</strong> a state of agreement or of peaceful existence together. <br><strong>Variance</strong> - the amount by which something changes or is different from something else<br><strong>Convention-</strong> a traditional way of behaving or of doing something<br><strong>Equal</strong> - the same in size, amount, value, number, level, etc.<br><strong>Intellectual</strong> - connected with a person&rsquo;s ability to think in a logical way and to understand things</p>",
                    solution_hi: "<p>92.(c) <strong>Harmony-</strong> समझौते की स्थिति या एक साथ शांतिपूर्ण अस्तित्व. <br><strong>Variance</strong> - जिसके द्वारा कुछ बदलता है या किसी अन्य से भिन्न होता है<br><strong>Convention-</strong> व्यवहार करने या कुछ करने का एक पारंपरिक तरीका<br><strong>Equal</strong> - आकार, मात्रा, मान, संख्या, स्तर आदि में समान।<br><strong>Intellectual</strong> - किसी व्यक्ति की तार्किक तरीके से सोचने और चीजों को समझने की क्षमता से जुड़ा हुआ</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "93",
                    section: "7",
                    question_en: "<p>93. Select the most appropriate ANTONYM of the given word.<br>Aggravate</p>",
                    question_hi: "<p>93. Select the most appropriate ANTONYM of the given word.<br>Aggravate</p>",
                    options_en: [
                        "<p>Humiliate</p>",
                        "<p>Alleviate</p>",
                        "<p>Ignorant</p>",
                        "<p>Exasperate</p>"
                    ],
                    options_hi: [
                        "<p>Humiliate</p>",
                        "<p>Alleviate</p>",
                        "<p>Ignorant</p>",
                        "<p>Exasperate</p>"
                    ],
                    solution_en: "<p>93.(b) <strong>Alleviate-</strong> to make something less strong or bad.<br><strong>Aggravate-</strong> to make something worse or more serious.<br><strong>Humiliate-</strong> to make somebody feel very embarrassed.<br><strong>Ignorant-</strong> having no knowledge or awareness of things in general.<br><strong>Exasperate-</strong> to make somebody angry.</p>",
                    solution_hi: "<p>93.(b) <strong>Alleviate</strong> (कम करना) - to make something less strong or bad.<br><strong>Aggravate</strong> (उत्तेजित करना) - to make something worse or more serious.<br><strong>Humiliate</strong> (अपमानित करना) - to make somebody feel very embarrassed.<br><strong>Ignorant</strong> (अज्ञानी) - having no knowledge or awareness of things in general.<br><strong>Exasperate</strong> (चिढ़ाना) - to make somebody angry.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "94",
                    section: "7",
                    question_en: "<p>94. Select the most appropriate option to fill in the blank.<br>The minute you start _______ and tolerating __________, it becomes the new norm and the slide begins and therefore, we must constantly __________ to give our best and be recognised, not simply aim to do just enough to ___________ that we don&rsquo;t get found out.</p>",
                    question_hi: "<p>94. Select the most appropriate option to fill in the blank.<br>The minute you start _______ and tolerating __________, it becomes the new norm and the slide begins and therefore, we must constantly __________ to give our best and be recognised, not simply aim to do just enough to ___________ that we don&rsquo;t get found out.</p>",
                    options_en: [
                        "<p>compelling, excellence, strike, encourage</p>",
                        "<p>compromising, mediocrity, strive, ensure</p>",
                        "<p>conjecturing, transcendence, strum, envision</p>",
                        "<p>contradicting, distinction, stink, endeavour</p>"
                    ],
                    options_hi: [
                        "<p>compelling, excellence, strike, encourage</p>",
                        "<p>compromising, mediocrity, strive, ensure</p>",
                        "<p>conjecturing, transcendence, strum, envision</p>",
                        "<p>contradicting, distinction, stink, endeavour</p>"
                    ],
                    solution_en: "<p>94.(b) It can be inferred from the&nbsp;given sentence that only option b has all the correct words that will fit in the blanks.</p>",
                    solution_hi: "<p>94.(b) दिए गए sentence से यह अनुमान लगाया जा सकता है कि केवल विकल्प (b) में सभी सही शब्द हैं जो रिक्त स्थान में फिट होंगे।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "95",
                    section: "7",
                    question_en: "<p>95. Select the phrase/idiom that will improve the underlined part of the given sentence. <br>When our principal said she was leaving the company, I <span style=\"text-decoration: underline;\">played out the opportunity</span> to fill the job.</p>",
                    question_hi: "<p>95. Select the phrase/idiom that will improve the underlined part of the given sentence. <br>When our principal said she was leaving the company, I <span style=\"text-decoration: underline;\">played out the opportunity</span> to fill the job.</p>",
                    options_en: [
                        "<p>glided at the opportunity</p>",
                        "<p>ran to the opportunity</p>",
                        "<p>started at the opportunity</p>",
                        "<p>jumped at the opportunity</p>"
                    ],
                    options_hi: [
                        "<p>glided at the opportunity</p>",
                        "<p>ran to the opportunity</p>",
                        "<p>started at the opportunity</p>",
                        "<p>jumped at the opportunity</p>"
                    ],
                    solution_en: "<p>95.(d) jumped at the opportunity<br>The idiom &lsquo;jump at the opportunity&rsquo; means to quickly take advantage of an opportunity. Hence, &lsquo;jumped at the opportunity&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>95.(d) jumped at the opportunity<br>Idiom &lsquo;jump at the opportunity&rsquo; का अर्थ है किसी अवसर का तुरंत लाभ उठाना। इसलिए, &lsquo;jumped at the opportunity&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "96",
                    section: "7",
                    question_en: "<p>96. <strong>Cloze Test:-</strong><br>Cities are the great havens for knowledge, culture, and social life. Vibrant cultures are found in cities because it takes a (96).________ population to support museums, concert halls, sports teams, and night-life districts. Cities also offer rich social opportunities. People in rural areas enjoy only limited social opportunities because (97).________ the small local population. City dwellers can choose their friends and mates from among a large number of people of (98).________ interests and inclinations. We are not likely to abandon the city as a (99).________ institution, but we need to make sure that our transport arrangements do not (100).________ the city\'s other functions.<br>Select the most appropriate option to fill in blank No. 96.</p>",
                    question_hi: "<p>96. <strong>Cloze Test:-</strong><br>Cities are the great havens for knowledge, culture, and social life. Vibrant cultures are found in cities because it takes a (96).________ population to support museums, concert halls, sports teams, and night-life districts. Cities also offer rich social opportunities. People in rural areas enjoy only limited social opportunities because (97).________ the small local population. City dwellers can choose their friends and mates from among a large number of people of (98).________ interests and inclinations. We are not likely to abandon the city as a (99).________ institution, but we need to make sure that our transport arrangements do not (100).________ the city\'s other functions.<br>Select the most appropriate option to fill in blank No. 96.</p>",
                    options_en: [
                        "<p>lazy</p>",
                        "<p>small</p>",
                        "<p>large</p>",
                        "<p>dump</p>"
                    ],
                    options_hi: [
                        "<p>lazy</p>",
                        "<p>small</p>",
                        "<p>large</p>",
                        "<p>dump</p>"
                    ],
                    solution_en: "<p>96.(c) large<br>The given passage states that Vibrant cultures are found in cities because it takes a large population to support museums, concert halls, sports teams, and night-life districts. Hence &lsquo;large&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>96.(c) large<br>दिए गए passage में बताया गया है कि जीवंत संस्कृतियाँ (Vibrant cultures) शहरों में पाई जाती हैं क्योंकि संग्रहालयों (museums), concert halls, sports teams और night-life districts का समर्थन करने के लिए एक बड़ी आबादी (large population) की आवश्यकता होती है। अतः &lsquo;large&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "97",
                    section: "7",
                    question_en: "<p>97. <strong>Cloze Test:-</strong><br>Cities are the great havens for knowledge, culture, and social life. Vibrant cultures are found in cities because it takes a (96).________ population to support museums, concert halls, sports teams, and night-life districts. Cities also offer rich social opportunities. People in rural areas enjoy only limited social opportunities because (97).________ the small local population. City dwellers can choose their friends and mates from among a large number of people of (98).________ interests and inclinations. We are not likely to abandon the city as a (99).________ institution, but we need to make sure that our transport arrangements do not (100).________ the city\'s other functions.<br>Select the most appropriate option to fill in blank No. 97</p>",
                    question_hi: "<p>97. <strong>Cloze Test:-</strong><br>Cities are the great havens for knowledge, culture, and social life. Vibrant cultures are found in cities because it takes a (96).________ population to support museums, concert halls, sports teams, and night-life districts. Cities also offer rich social opportunities. People in rural areas enjoy only limited social opportunities because (97).________ the small local population. City dwellers can choose their friends and mates from among a large number of people of (98).________ interests and inclinations. We are not likely to abandon the city as a (99).________ institution, but we need to make sure that our transport arrangements do not (100).________ the city\'s other functions.<br>Select the most appropriate option to fill in blank No. 97</p>",
                    options_en: [
                        "<p>of</p>",
                        "<p>with</p>",
                        "<p>for</p>",
                        "<p>off</p>"
                    ],
                    options_hi: [
                        "<p>of</p>",
                        "<p>with</p>",
                        "<p>for</p>",
                        "<p>off</p>"
                    ],
                    solution_en: "<p>97.(a) of<br>We generally use &lsquo;of &rsquo; with &lsquo;because&rsquo; to present the reason for an action. Hence &lsquo;of&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>97.(a) of<br>हम सामान्यतः किसी Action के reason को बताने के लिए &lsquo;of&rsquo; के साथ &lsquo;because&rsquo; का प्रयोग करते हैं। अतः &lsquo;of&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "98",
                    section: "7",
                    question_en: "<p>98. <strong>Cloze Test:-</strong><br>Cities are the great havens for knowledge, culture, and social life. Vibrant cultures are found in cities because it takes a (96).________ population to support museums, concert halls, sports teams, and night-life districts. Cities also offer rich social opportunities. People in rural areas enjoy only limited social opportunities because (97).________ the small local population. City dwellers can choose their friends and mates from among a large number of people of (98).________ interests and inclinations. We are not likely to abandon the city as a (99).________ institution, but we need to make sure that our transport arrangements do not (100).________ the city\'s other functions.<br>Select the most appropriate option to fill in blank No. 98</p>",
                    question_hi: "<p>98. <strong>Cloze Test:-</strong><br>Cities are the great havens for knowledge, culture, and social life. Vibrant cultures are found in cities because it takes a (96).________ population to support museums, concert halls, sports teams, and night-life districts. Cities also offer rich social opportunities. People in rural areas enjoy only limited social opportunities because (97).________ the small local population. City dwellers can choose their friends and mates from among a large number of people of (98).________ interests and inclinations. We are not likely to abandon the city as a (99).________ institution, but we need to make sure that our transport arrangements do not (100).________ the city\'s other functions.<br>Select the most appropriate option to fill in blank No. 98</p>",
                    options_en: [
                        "<p>declining</p>",
                        "<p>feigning</p>",
                        "<p>wasteful</p>",
                        "<p>similar</p>"
                    ],
                    options_hi: [
                        "<p>declining</p>",
                        "<p>feigning</p>",
                        "<p>wasteful</p>",
                        "<p>similar</p>"
                    ],
                    solution_en: "<p>98.(d) similar<br>&lsquo;Similar&rsquo; means almost the same. The given passage states that city dwellers can choose their friends and mates from among a large number of people of similar interests and inclinations. Hence &lsquo;similar&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>98.(d) similar<br>&lsquo;Similar&rsquo; का अर्थ है लगभग समान। दिए गए passage में बताया गया है कि शहरवासी (city dwellers) बड़ी संख्या में समान रुचियों (similar interests) और प्रवृत्तियों (inclinations) वाले लोगों में से अपने मित्र और साथी चुन सकते हैं। अतः &lsquo;similar&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "99",
                    section: "7",
                    question_en: "<p>99. <strong>Cloze Test:-</strong><br>Cities are the great havens for knowledge, culture, and social life. Vibrant cultures are found in cities because it takes a (96).________ population to support museums, concert halls, sports teams, and night-life districts. Cities also offer rich social opportunities. People in rural areas enjoy only limited social opportunities because (97).________ the small local population. City dwellers can choose their friends and mates from among a large number of people of (98).________ interests and inclinations. We are not likely to abandon the city as a (99).________ institution, but we need to make sure that our transport arrangements do not (100).________ the city\'s other functions.<br>Select the most appropriate option to fill in blank No. 99</p>",
                    question_hi: "<p>99. Cloze Test:-<br>Cities are the great havens for knowledge, culture, and social life. Vibrant cultures are found in cities because it takes a (96).________ population to support museums, concert halls, sports teams, and night-life districts. Cities also offer rich social opportunities. People in rural areas enjoy only limited social opportunities because (97).________ the small local population. City dwellers can choose their friends and mates from among a large number of people of (98).________ interests and inclinations. We are not likely to abandon the city as a (99).________ institution, but we need to make sure that our transport arrangements do not (100).________ the city\'s other functions.<br>Select the most appropriate option to fill in blank No. 99</p>",
                    options_en: [
                        "<p>religious</p>",
                        "<p>psychological</p>",
                        "<p>cultural</p>",
                        "<p>educational</p>"
                    ],
                    options_hi: [
                        "<p>religious</p>",
                        "<p>psychological</p>",
                        "<p>cultural</p>",
                        "<p>educational</p>"
                    ],
                    solution_en: "<p>99.(c) cultural<br>&lsquo;Cultural&rsquo; mean relating to the ideas, customs, and social behaviour of a society. The given passage states that we are not likely to abandon the city as a cultural institution. Hence &lsquo;cultural&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>99.(c) cultural<br>&lsquo;Cultural&rsquo; का अर्थ है समाज के विचारों, रीति-रिवाजों (customs) और सामाजिक व्यवहार (social behaviour) से संबंधित। दिए गए passage में बताया गया है कि हम शहर को एक सांस्कृतिक संस्था (cultural institution) के रूप में त्यागने (abandon) की संभावना नहीं रखते हैं। अतः &lsquo;cultural&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "100",
                    section: "misc",
                    question_en: "<p>100. <strong>Cloze Test:-</strong><br>Cities are the great havens for knowledge, culture, and social life. Vibrant cultures are found in cities because it takes a (96).________ population to support museums, concert halls, sports teams, and night-life districts. Cities also offer rich social opportunities. People in rural areas enjoy only limited social opportunities because (97).________ the small local population. City dwellers can choose their friends and mates from among a large number of people of (98).________ interests and inclinations. We are not likely to abandon the city as a (99).________ institution, but we need to make sure that our transport arrangements do not (100).________ the city\'s other functions.<br>Select the most appropriate option to fill in blank No. 100.</p>",
                    question_hi: "<p>100. <strong>Cloze Test:-</strong><br>Cities are the great havens for knowledge, culture, and social life. Vibrant cultures are found in cities because it takes a (96).________ population to support museums, concert halls, sports teams, and night-life districts. Cities also offer rich social opportunities. People in rural areas enjoy only limited social opportunities because (97).________ the small local population. City dwellers can choose their friends and mates from among a large number of people of (98).________ interests and inclinations. We are not likely to abandon the city as a (99).________ institution, but we need to make sure that our transport arrangements do not (100).________ the city\'s other functions.<br>Select the most appropriate option to fill in blank No. 100.</p>",
                    options_en: [
                        "<p>support</p>",
                        "<p>boost</p>",
                        "<p>help</p>",
                        "<p>damage</p>"
                    ],
                    options_hi: [
                        "<p>support</p>",
                        "<p>boost</p>",
                        "<p>help</p>",
                        "<p>damage</p>"
                    ],
                    solution_en: "<p>100.(d) <strong>damage</strong><br>Damage means to cause harm to something or someone. The given passage states that we need to make sure that our transport arrangements do not damage the city\'s other functions. Hence &lsquo;damage&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>100.(d) damage<br>&lsquo;Damage&rsquo; का अर्थ है किसी चीज़ (something) या किसी व्यक्ति (someone) को हानि या नुकसान पहुँचाना। दिए गए passage में बताया गया है कि हमें यह सुनिश्चित करने की आवश्यकता है कि हमारी परिवहन व्यवस्था (transport arrangements) शहर के अन्य कार्यों को नुकसान न पहुँचाए। अतः &lsquo;damage&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'question-box d-flex align-items-center justify-content-center border rounded';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.question-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            const html = `
                <div class="card shadow-sm mb-4">
                    <div class="card-body">
                        <div class="question-text mb-4">
                            <div class="en">${question.question_en}</div>
                            <div class="hi" style="display:none">${question.question_hi}</div>
                        </div>
                        <div class="options">
                            ${['a', 'b', 'c', 'd'].map((opt, i) => `
                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="radio" name="q${index}" 
                                        id="opt-${index}-${opt}" value="${opt}" 
                                        ${answers[question.id] === opt ? 'checked' : ''}
                                        onclick="selectAnswer('${question.id}', '${opt}')">
                                    <label class="form-check-label w-100 py-2 px-3 rounded hover-bg-light" for="opt-${index}-${opt}">
                                        <div class="en">${question.options_en[i]}</div>
                                        <div class="hi" style="display:none">${question.options_hi[i]}</div>
                                    </label>
                                </div>
                            `).join('')}
                        </div>
                        ${submitted ? `
                            <div class="alert ${answers[question.id] === question.correct ? 'alert-success' : 'alert-danger'} mt-3">
                                ${answers[question.id] ? (answers[question.id] === question.correct ? 'Correct!' : 'Incorrect!') : 'Not attempted'}
                                <div class="mt-2">
                                    <strong>Solution:</strong>
                                    <div class="en">${question.solution_en}</div>
                                    <div class="hi" style="display:none">${question.solution_hi}</div>
                                </div>
                            </div>
                        ` : ''}
                    </div>
                </div>
            `;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // If in mobile view, close the menu
            if (window.innerWidth < 768) {
                toggleMenu(false);
            }
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                document.getElementById(`box-${index}`).classList.add('attempted');
            }
        }
        // Navigate to previous question
        function prevQuestion() {
            if (currentQuestion > 0) {
                showQuestion(currentQuestion - 1);
            }
        }
        // Navigate to next question
        function nextQuestion() {
            if (currentQuestion < questions.length - 1) {
                showQuestion(currentQuestion + 1);
            }
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) {
                showQuestion(section.start);
            }
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update button text
            document.getElementById('lang-btn').innerHTML = currentLang === 'en' ? 
                '<i class="fas fa-language"></i> हिंदी' : 
                '<i class="fas fa-language"></i> English';
        }
        // Toggle menu for mobile
        function toggleMenu(show) {
            const nav = document.getElementById('question-nav');
            const content = document.getElementById('question-content');
            if (show === undefined) {
                show = nav.style.right === '-100%' || nav.style.right === '';
            }
            if (show) {
                nav.style.right = '0';
                if (window.innerWidth >= 768) {
                    content.style.marginRight = '320px';
                }
            } else {
                nav.style.right = window.innerWidth < 768 ? '-100%' : '0';
                content.style.marginRight = window.innerWidth < 768 ? '0' : '320px';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
            document.getElementById('total-count').textContent = questions.length;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.remove('attempted');
                    box.classList.add('bg-secondary', 'text-white');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.remove('attempted');
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.remove('attempted');
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            resultsModal.show();
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check-circle me-1"></i> Submitted';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            resultsModal.hide();
            showQuestion(0);
        }
    </script>
</body>
</html>