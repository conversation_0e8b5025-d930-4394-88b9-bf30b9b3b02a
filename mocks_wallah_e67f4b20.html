<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">15:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 15</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">15</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 15 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 0,
                end: 13
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 14,
                end: 14
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "17",
                    question_en: "<p>1. <span style=\"font-family: Cambria Math;\">Find the third proportional of 25 and 45.</span></p>\n",
                    question_hi: "<p>1. <span style=\"font-family: Cambria Math;\">25 &#2324;&#2352; 45 &#2325;&#2366; &#2340;&#2371;&#2340;&#2368;&#2351; &#2360;&#2350;&#2366;&#2344;&#2369;&#2346;&#2366;&#2340;&#2368; &#2332;&#2381;&#2334;&#2366;&#2340; &#2325;&#2368;&#2332;&#2367;&#2319;&#2404;</span></p>\n",
                    options_en: ["<p>85</p>\n", "<p>65</p>\n", 
                                "<p>81</p>\n", "<p>76</p>\n"],
                    options_hi: ["<p>85</p>\n", "<p>65</p>\n",
                                "<p>81</p>\n", "<p>76</p>\n"],
                    solution_en: "<p>1.(c)</p>\r\n<p><span style=\"font-family: Cambria Math;\">Third proportional =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>45</mn><mo>&times;</mo><mn>45</mn></mrow><mn>25</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\">= 81</span></p>\n",
                    solution_hi: "<p>1.(c)</p>\r\n<p><span style=\"font-family: Cambria Math;\">&#2340;&#2368;&#2360;&#2352;&#2366; &#2310;&#2344;&#2369;&#2346;&#2366;&#2340;&#2367;&#2325; =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>45</mn><mo>&times;</mo><mn>45</mn></mrow><mn>25</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\">= 81</span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "17",
                    question_en: "<p>2. <span style=\"font-family: Cambria Math;\">If x : y : z = 3 : 4 : 5, then what will be the ratio of&nbsp; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><mfrac><mi mathvariant=\"normal\">x</mi><mi mathvariant=\"normal\">y</mi></mfrac><mo>)</mo><mo>&nbsp;</mo><mo>:</mo><mo>&nbsp;</mo><mo>(</mo><mfrac><mi mathvariant=\"normal\">y</mi><mi mathvariant=\"normal\">z</mi></mfrac><mo>)</mo><mo>&nbsp;</mo><mo>:</mo><mo>&nbsp;</mo><mo>(</mo><mfrac><mi mathvariant=\"normal\">z</mi><mi mathvariant=\"normal\">x</mi></mfrac><mo>)</mo></math></span><span style=\"font-family: Cambria Math;\">? </span></p>\n",
                    question_hi: "<p>2. <span style=\"font-family: Cambria Math;\">&#2351;&#2342;&#2367; x : y : z = 3 : 4 : 5, &#2340;&#2379; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><mfrac><mi mathvariant=\"normal\">x</mi><mi mathvariant=\"normal\">y</mi></mfrac><mo>)</mo><mo>&nbsp;</mo><mo>:</mo><mo>&nbsp;</mo><mo>(</mo><mfrac><mi mathvariant=\"normal\">y</mi><mi mathvariant=\"normal\">z</mi></mfrac><mo>)</mo><mo>&nbsp;</mo><mo>:</mo><mo>&nbsp;</mo><mo>(</mo><mfrac><mi mathvariant=\"normal\">z</mi><mi mathvariant=\"normal\">x</mi></mfrac><mo>)</mo></math></span><span style=\"font-family: Cambria Math;\"> &#2325;&#2366; &#2309;&#2344;&#2369;&#2346;&#2366;&#2340; &#2325;&#2381;&#2351;&#2366; &#2361;&#2379;&#2327;&#2366;?</span></p>\n",
                    options_en: ["<p>49 : 37 : 100</p>\n", "<p>45 : 48 : 100</p>\n", 
                                "<p>41 : 37 : 100</p>\n", "<p>37 : 47 : 100</p>\n"],
                    options_hi: ["<p>49 : 37 : 100</p>\n", "<p>45 : 48 : 100</p>\n",
                                "<p>41 : 37 : 100</p>\n", "<p>37 : 47 : 100</p>\n"],
                    solution_en: "<p>2.(b)</p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">x</mi><mo>&nbsp;</mo><mo>:</mo><mo>&nbsp;</mo><mi mathvariant=\"normal\">y</mi><mo>&nbsp;</mo><mo>:</mo><mo>&nbsp;</mo><mi mathvariant=\"normal\">z</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>3</mn><mo>&nbsp;</mo><mo>:</mo><mo>&nbsp;</mo><mn>4</mn><mo>&nbsp;</mo><mo>:</mo><mo>&nbsp;</mo><mn>5</mn><mspace linebreak=\"newline\"></mspace><mi>Now</mi><mo>&nbsp;</mo><mo>,</mo><mo>&nbsp;</mo><mo>(</mo><mfrac><mi mathvariant=\"normal\">x</mi><mi mathvariant=\"normal\">y</mi></mfrac><mo>)</mo><mo>&nbsp;</mo><mo>:</mo><mo>(</mo><mfrac><mi mathvariant=\"normal\">y</mi><mi mathvariant=\"normal\">z</mi></mfrac><mo>)</mo><mo>&nbsp;</mo><mo>:</mo><mo>(</mo><mfrac><mi mathvariant=\"normal\">z</mi><mi mathvariant=\"normal\">x</mi></mfrac><mo>)</mo><mo>&nbsp;</mo><mo>=</mo><mo>(</mo><mfrac><mn>3</mn><mn>4</mn></mfrac><mo>)</mo><mo>&nbsp;</mo><mo>:</mo><mo>&nbsp;</mo><mo>(</mo><mfrac><mn>4</mn><mn>5</mn></mfrac><mo>)</mo><mo>&nbsp;</mo><mo>:</mo><mo>&nbsp;</mo><mo>(</mo><mfrac><mn>5</mn><mn>3</mn></mfrac><mo>)</mo><mspace linebreak=\"newline\"></mspace><mo>=</mo><mn>45</mn><mo>&nbsp;</mo><mo>:</mo><mo>&nbsp;</mo><mn>48</mn><mo>&nbsp;</mo><mo>:</mo><mo>&nbsp;</mo><mn>100</mn><mo>&nbsp;</mo></math></p>\n",
                    solution_hi: "<p>2.(b)</p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">x</mi><mo>&nbsp;</mo><mo>:</mo><mo>&nbsp;</mo><mi mathvariant=\"normal\">y</mi><mo>&nbsp;</mo><mo>:</mo><mo>&nbsp;</mo><mi mathvariant=\"normal\">z</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>3</mn><mo>&nbsp;</mo><mo>:</mo><mo>&nbsp;</mo><mn>4</mn><mo>&nbsp;</mo><mo>:</mo><mo>&nbsp;</mo><mn>5</mn><mspace linebreak=\"newline\"></mspace><mi>&#2309;&#2348;</mi><mo>&nbsp;</mo><mo>,</mo><mo>&nbsp;</mo><mo>(</mo><mfrac><mi mathvariant=\"normal\">x</mi><mi mathvariant=\"normal\">y</mi></mfrac><mo>)</mo><mo>&nbsp;</mo><mo>:</mo><mo>(</mo><mfrac><mi mathvariant=\"normal\">y</mi><mi mathvariant=\"normal\">z</mi></mfrac><mo>)</mo><mo>&nbsp;</mo><mo>:</mo><mo>(</mo><mfrac><mi mathvariant=\"normal\">z</mi><mi mathvariant=\"normal\">x</mi></mfrac><mo>)</mo><mo>&nbsp;</mo><mo>=</mo><mo>(</mo><mfrac><mn>3</mn><mn>4</mn></mfrac><mo>)</mo><mo>&nbsp;</mo><mo>:</mo><mo>&nbsp;</mo><mo>(</mo><mfrac><mn>4</mn><mn>5</mn></mfrac><mo>)</mo><mo>&nbsp;</mo><mo>:</mo><mo>&nbsp;</mo><mo>(</mo><mfrac><mn>5</mn><mn>3</mn></mfrac><mo>)</mo><mspace linebreak=\"newline\"></mspace><mo>=</mo><mn>45</mn><mo>&nbsp;</mo><mo>:</mo><mo>&nbsp;</mo><mn>48</mn><mo>&nbsp;</mo><mo>:</mo><mo>&nbsp;</mo><mn>100</mn><mo>&nbsp;</mo></math></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "17",
                    question_en: "<p>3. <span style=\"font-family: Cambria Math;\">A sum of money is divided among A, B, C in the ratio 3: 2 : 5, respectively. If the share of C is &#8377;300 more than B. then how much more money will A get than B ?</span></p>\n",
                    question_hi: "<p>3. <span style=\"font-family: Cambria Math;\">&#2319;&#2325; &#2343;&#2344;&#2352;&#2366;&#2358;&#2367; &#2325;&#2379; A, B, C &#2325;&#2375; &#2348;&#2368;&#2330; &#2325;&#2381;&#2352;&#2350;&#2358;&#2307; 3: 2:5 &#2325;&#2375; &#2309;&#2344;&#2369;&#2346;&#2366;&#2340; &#2350;&#2375;&#2306; &#2348;&#2366;&#2306;&#2335;&#2366; &#2332;&#2366;&#2340;&#2366; &#2361;&#2376;&#2404; &#2351;&#2342;&#2367; C &#2325;&#2366; &#2361;&#2367;&#2360;&#2381;&#2360;&#2366; B &#2360;&#2375; &#8377;300 &#2309;&#2343;&#2367;&#2325; &#2361;&#2376;, &#2340;&#2379; A &#2325;&#2379; B &#2360;&#2375; &#2325;&#2367;&#2340;&#2344;&#2368; &#2309;&#2343;&#2367;&#2325; &#2343;&#2344;&#2352;&#2366;&#2358;&#2367; &#2350;&#2367;&#2354;&#2375;&#2327;&#2368; ?</span></p>\n",
                    options_en: ["<p>&#8377;150</p>\n", "<p>&#8377;75</p>\n", 
                                "<p>&#8377;100</p>\n", "<p>&#8377;125</p>\n"],
                    options_hi: ["<p>&#8377;150</p>\n", "<p>&#8377;75</p>\n",
                                "<p>&#8377;100</p>\n", "<p>&#8377;125</p>\n"],
                    solution_en: "<p>3.(c)</p>\r\n<p><span style=\"font-family: Cambria Math;\">A : B : C = 3 : 2 : 5 </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Difference between share of C and B = 3 units</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">3 units = </span><span style=\"font-family: Cambria Math;\">&#8377; </span><span style=\"font-family: Cambria Math;\">300</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Share of A more than B = 1 units = </span><span style=\"font-family: Cambria Math;\">&#8377; </span><span style=\"font-family: Cambria Math;\">100</span></p>\n",
                    solution_hi: "<p>3.(c)</p>\r\n<p><span style=\"font-family: Cambria Math;\">A : B : C = 3 : 2 : 5 </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">C &#2324;&#2352; B &#2325;&#2368; &#2361;&#2367;&#2360;&#2381;&#2360;&#2375;&#2342;&#2366;&#2352;&#2368; &#2325;&#2375; &#2348;&#2368;&#2330; &#2325;&#2366; &#2309;&#2306;&#2340;&#2352; = 3 &#2311;&#2325;&#2366;&#2312; </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">3 &#2311;&#2325;&#2366;&#2312; = </span><span style=\"font-family: Cambria Math;\">&#8377; </span><span style=\"font-family: Cambria Math;\">300 </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">A &#2325;&#2366; &#2361;&#2367;&#2360;&#2381;&#2360;&#2366; B &#2360;&#2375; &#2309;&#2343;&#2367;&#2325; &#2361;&#2376; = 1 &#2311;&#2325;&#2366;&#2312; = </span><span style=\"font-family: Cambria Math;\">&#8377; </span><span style=\"font-family: Cambria Math;\">100 </span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "17",
                    question_en: "<p>4. <span style=\"font-family: Cambria Math;\">Find the fourth proportion to 0.24, 0.6 and 20.</span></p>\n",
                    question_hi: "<p>4. <span style=\"font-family: Cambria Math;\">0.24, 0.6 &#2324;&#2352; 20 &#2325;&#2366; &#2330;&#2340;&#2369;&#2352;&#2381;&#2341; &#2360;&#2350;&#2366;&#2344;&#2369;&#2346;&#2366;&#2340;&#2368; &#2332;&#2381;&#2334;&#2366;&#2340; &#2325;&#2368;&#2332;&#2367;&#2319; &#2404;</span></p>\n",
                    options_en: ["<p>50</p>\n", "<p>24</p>\n", 
                                "<p>54</p>\n", "<p>48</p>\n"],
                    options_hi: ["<p>50</p>\n", "<p>24</p>\n",
                                "<p>54</p>\n", "<p>48</p>\n"],
                    solution_en: "<p>4.(a)</p>\r\n<p><span style=\"font-family: Cambria Math;\">Fourth proportional = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>0</mn><mo>.</mo><mn>6</mn><mo>&times;</mo><mn>20</mn></mrow><mrow><mn>0</mn><mo>.</mo><mn>24</mn></mrow></mfrac></math></span><span style=\"font-family: Cambria Math;\"> = 50</span></p>\n",
                    solution_hi: "<p>4.(a)</p>\r\n<p><span style=\"font-family: Cambria Math;\">&#2330;&#2340;&#2369;&#2352;&#2381;&#2341; &#2310;&#2344;&#2369;&#2346;&#2366;&#2340;&#2367;&#2325; =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>0</mn><mo>.</mo><mn>6</mn><mo>&times;</mo><mn>20</mn></mrow><mrow><mn>0</mn><mo>.</mo><mn>24</mn></mrow></mfrac></math></span><span style=\"font-family: Cambria Math;\">= 50</span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "17",
                    question_en: "<p>5. <span style=\"font-family: Cambria Math;\">The fourth proportional of 1.4, 8.4 and 4.2 is:</span></p>\n",
                    question_hi: "<p>5. <span style=\"font-family: Cambria Math;\">1.4, 8.4 &#2324;&#2352; 4.2 &#2325;&#2366; &#2330;&#2340;&#2369;&#2352;&#2381;&#2341; &#2360;&#2350;&#2366;&#2344;&#2369;&#2346;&#2366;&#2340;&#2368; &#2325;&#2381;&#2351;&#2366; &#2361;&#2376;?</span></p>\n",
                    options_en: ["<p>2.8</p>\n", "<p>0.7</p>\n", 
                                "<p>25.2</p>\n", "<p>6.4</p>\n"],
                    options_hi: ["<p>2.8</p>\n", "<p>0.7</p>\n",
                                "<p>25.2</p>\n", "<p>6.4</p>\n"],
                    solution_en: "<p>5.(c)</p>\r\n<p><span style=\"font-family: Cambria Math;\">Fourth proportional = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>8</mn><mo>.</mo><mn>4</mn><mo>&times;</mo><mn>4</mn><mo>.</mo><mn>2</mn></mrow><mrow><mn>1</mn><mo>.</mo><mn>4</mn></mrow></mfrac></math></span><span style=\"font-family: Cambria Math;\">= 25.2 </span></p>\n",
                    solution_hi: "<p>5.(c)</p>\r\n<p><span style=\"font-family: Cambria Math;\">&#2330;&#2340;&#2369;&#2352;&#2381;&#2341; &#2360;&#2350;&#2366;&#2344;&#2369;&#2346;&#2366;&#2340;&#2368; =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>8</mn><mo>.</mo><mn>4</mn><mo>&times;</mo><mn>4</mn><mo>.</mo><mn>2</mn></mrow><mrow><mn>1</mn><mo>.</mo><mn>4</mn></mrow></mfrac></math></span><span style=\"font-family: Cambria Math;\">= 25.2 </span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "17",
                    question_en: "<p>6. <span style=\"font-family: Cambria Math;\">The cost of 3 cups and 5 plates is &#8377;1,080 and the cost of 2 cups and 4 plates is &#8377;840. Find the ratio of the cost of a cup to the cost of a plate. </span></p>\n",
                    question_hi: "<p>6. <span style=\"font-family: Cambria Math;\">3 &#2325;&#2346; &#2324;&#2352; 5 &#2346;&#2381;&#2354;&#2375;&#2335;&#2379;&#2306; &#2325;&#2366; &#2350;&#2370;&#2354;&#2381;&#2351; &#8377;1,080 &#2361;&#2376; &#2324;&#2352; 2 &#2325;&#2346; &#2324;&#2352; 4 &#2346;&#2381;&#2354;&#2375;&#2335;&#2379;&#2306; &#2325;&#2366; &#2350;&#2370;&#2354;&#2381;&#2351; &#8377;840 &#2361;&#2376;&#2404; &#2319;&#2325; &#2325;&#2346; &#2325;&#2375; &#2350;&#2370;&#2354;&#2381;&#2351; &#2324;&#2352; &#2319;&#2325; &#2346;&#2381;&#2354;&#2375;&#2335; &#2325;&#2375; &#2350;&#2370;&#2354;&#2381;&#2351; &#2325;&#2366; &#2309;&#2344;&#2369;&#2346;&#2366;&#2340; &#2332;&#2381;&#2334;&#2366;&#2340; &#2325;&#2368;&#2332;&#2367;&#2351;&#2375;&#2404; </span></p>\n",
                    options_en: ["<p>1 : 3</p>\n", "<p>2 : 3</p>\n", 
                                "<p>1 : 1</p>\n", "<p>1 : 2</p>\n"],
                    options_hi: ["<p>1 : 3</p>\n", "<p>2 : 3</p>\n",
                                "<p>1 : 1</p>\n", "<p>1 : 2</p>\n"],
                    solution_en: "<p>6.(a)</p>\r\n<p><span style=\"font-family: Cambria Math;\">3C + 5P = 1080 &#8377; &hellip; e.q.(1)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">2C + 4P = 840 &#8377; &hellip; e.q.(2) </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">---------------------------</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">C + P = 240 &#8377; </span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&rArr;</mo></math>2C + 2P = 480 &#8377; &hellip;e.q.(3) </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">On solving e.q.(3) and e.q.(2) , we get</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">2P = 360&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&rArr;</mo></math> </span><span style=\"font-family: Cambria Math;\">P = 180 &#8377; and C = 60 &#8377;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Required ratio = 1 : 3</span></p>\n",
                    solution_hi: "<p>6.(a)</p>\r\n<p><span style=\"font-family: Cambria Math;\">3C + 5P = 1080 &#8377; &hellip; &#2360;&#2350;&#2368;&#2325;&#2352;&#2339; (1)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">2C + 4P = 840 &#8377; &hellip; &#2360;&#2350;&#2368;&#2325;&#2352;&#2339; (2) </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">------------------------------------------</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">C + P = 240 &#8377; </span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&rArr;</mo></math>2C + 2P = 480 &#8377; &hellip;<span style=\"font-weight: 400;\">&#2360;&#2350;&#2368;&#2325;&#2352;&#2339; </span>(3) </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&#2360;&#2350;&#2368;&#2325;&#2352;&#2339; (3) &#2324;&#2352; &#2360;&#2350;&#2368;&#2325;&#2352;&#2339; (2) &#2325;&#2379; &#2361;&#2354; &#2325;&#2352;&#2344;&#2375; &#2346;&#2352; , &#2361;&#2350;&#2375; &#2346;&#2381;&#2352;&#2366;&#2346;&#2381;&#2340; &#2361;&#2379;&#2340;&#2366; &#2361;&#2376; </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">2P = 360 <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&rArr;</mo></math></span><span style=\"font-family: Cambria Math;\"> P = 180 &#8377; &#2324;&#2352; C = 60 &#8377;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&#2310;&#2357;&#2358;&#2381;&#2351;&#2325; &#2309;&#2344;&#2369;&#2346;&#2366;&#2340; = 1 : 3</span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "17",
                    question_en: "<p>7. <span style=\"font-family: Cambria Math;\">Find the fourth proportion for 15, 55 and 81 .</span></p>\n",
                    question_hi: "<p>7. <span style=\"font-family: Cambria Math;\">15, 55 &#2324;&#2352; 81 &#2325;&#2366; &#2330;&#2340;&#2369;&#2352;&#2381;&#2341; &#2360;&#2350;&#2366;&#2344;&#2369;&#2346;&#2366;&#2340;&#2368; &#2332;&#2381;&#2334;&#2366;&#2340; &#2325;&#2368;&#2332;&#2367;&#2319;&#2404;</span></p>\n",
                    options_en: ["<p>231</p>\n", "<p>297</p>\n", 
                                "<p>335</p>\n", "<p>313</p>\n"],
                    options_hi: ["<p>231</p>\n", "<p>297</p>\n",
                                "<p>335</p>\n", "<p>313</p>\n"],
                    solution_en: "<p>7.(b)</p>\r\n<p><span style=\"font-family: Cambria Math;\">Fourth proportional =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>55</mn><mo>&times;</mo><mn>81</mn></mrow><mn>15</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\">= 297</span></p>\n",
                    solution_hi: "<p>7.(b)</p>\r\n<p><span style=\"font-family: Cambria Math;\">&#2330;&#2340;&#2369;&#2352;&#2381;&#2341; &#2360;&#2350;&#2366;&#2344;&#2369;&#2346;&#2366;&#2340;&#2368; =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>55</mn><mo>&times;</mo><mn>81</mn></mrow><mn>15</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\">= 297</span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "17",
                    question_en: "<p>8.<span style=\"font-family: Cambria Math;\"> The compound ratio of 3: 4 and 6: 7 is 36: x. Find x.</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">8.</span><span style=\"font-family: Cambria Math;\"> 3 : 4 &#2324;&#2352; 6 : 7 &#2325;&#2366; &#2350;&#2367;&#2358;&#2381;&#2352;&#2367;&#2340; &#2309;&#2344;&#2369;&#2346;&#2366;&#2340; 36: x &#2361;&#2376;&#2404; x &#2325;&#2366; &#2350;&#2366;&#2344; &#2332;&#2381;&#2334;&#2366;&#2340; &#2325;&#2368;&#2332;&#2367;&#2319; &#2404; </span></p>\n",
                    options_en: ["<p>64</p>\n", "<p>75</p>\n", 
                                "<p>56</p>\n", "<p>48</p>\n"],
                    options_hi: ["<p>64</p>\n", "<p>75</p>\n",
                                "<p>56</p>\n", "<p>48</p>\n"],
                    solution_en: "<p>8.(c)</p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>Compound</mi><mo>&nbsp;</mo><mi>ratio</mi><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>:</mo><mo>-</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mfrac><mrow><mn>3</mn><mo>&times;</mo><mn>6</mn></mrow><mrow><mn>4</mn><mo>&times;</mo><mn>7</mn></mrow></mfrac><mo>&nbsp;</mo><mo>=</mo><mfrac><mn>36</mn><mi mathvariant=\"normal\">x</mi></mfrac><mo>&nbsp;</mo><mo>&rArr;</mo><mo>&nbsp;</mo><mi mathvariant=\"normal\">x</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>56</mn></math></p>\n",
                    solution_hi: "<p>8.(c)</p>\r\n<p><span style=\"font-family: Cambria Math;\">&#2351;&#2380;&#2327;&#2367;&#2325; &#2309;&#2344;&#2369;&#2346;&#2366;&#2340; :- </span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>3</mn><mo>&times;</mo><mn>6</mn></mrow><mrow><mn>4</mn><mo>&times;</mo><mn>7</mn></mrow></mfrac><mo>=</mo><mfrac><mn>36</mn><mi mathvariant=\"normal\">x</mi></mfrac><mo>&rArr;</mo><mi mathvariant=\"normal\">x</mi><mo>=</mo><mn>56</mn></math></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">9.</span><span style=\"font-family: Cambria Math;\"> The third proportion of 1.8 and 9 is : </span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">9.</span><span style=\"font-family: Cambria Math;\">1.8 &#2324;&#2352; 9 &#2325;&#2366; &#2340;&#2371;&#2340;&#2368;&#2351; &#2360;&#2350;&#2366;&#2344;&#2369;&#2346;&#2366;&#2340;&#2368; &#2332;&#2381;&#2334;&#2366;&#2340; &#2325;&#2368;&#2332;&#2367;&#2319;&#2404; </span></p>\n",
                    options_en: ["<p>7.5</p>\n", "<p>35</p>\n", 
                                "<p>81</p>\n", "<p>45</p>\n"],
                    options_hi: ["<p>7.5</p>\n", "<p>35</p>\n",
                                "<p>81</p>\n", "<p>45</p>\n"],
                    solution_en: "<p>9.(d)</p>\r\n<p><span style=\"font-family: Cambria Math;\">Third proportional =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>9</mn><mo>&times;</mo><mn>9</mn></mrow><mrow><mn>1</mn><mo>.</mo><mn>8</mn></mrow></mfrac></math></span><span style=\"font-family: Cambria Math;\">= 45</span></p>\n",
                    solution_hi: "<p>9.(d)</p>\r\n<p><span style=\"font-family: Cambria Math;\">&#2340;&#2368;&#2360;&#2352;&#2366; &#2310;&#2344;&#2369;&#2346;&#2366;&#2340;&#2367;&#2325; =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>9</mn><mo>&times;</mo><mn>9</mn></mrow><mrow><mn>1</mn><mo>.</mo><mn>8</mn></mrow></mfrac></math></span><span style=\"font-family: Cambria Math;\">= 45</span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">10. </span><span style=\"font-family: Cambria Math;\">What is the value of x, if 3 : 8 :: x : 12 ?</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">10. </span><span style=\"font-family: Cambria Math;\">&#2351;&#2342;&#2367; 3 : 8 :: x : 12 &#2361;&#2376;, &#2340;&#2379; x &#2325;&#2366; &#2350;&#2366;&#2344; &#2332;&#2381;&#2334;&#2366;&#2340; &#2325;&#2368;&#2332;&#2367;&#2319; &#2404;</span></p>\n",
                    options_en: ["<p>9</p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>2</mn></mfrac></math></p>\n", 
                                "<p>5</p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>9</mn><mn>2</mn></mfrac></math></p>\n"],
                    options_hi: ["<p>9</p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>2</mn></mfrac></math></p>\n",
                                "<p>5</p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>9</mn><mn>2</mn></mfrac></math></p>\n"],
                    solution_en: "<p>10.(d)</p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">x</mi><mo>=</mo><mfrac><mrow><mn>3</mn><mo>&times;</mo><mn>12</mn></mrow><mn>8</mn></mfrac><mo>=</mo><mfrac><mn>9</mn><mn>2</mn></mfrac></math></p>\n",
                    solution_hi: "<p>10.(d)</p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">x</mi><mo>=</mo><mfrac><mrow><mn>3</mn><mo>&times;</mo><mn>12</mn></mrow><mn>8</mn></mfrac><mo>=</mo><mfrac><mn>9</mn><mn>2</mn></mfrac></math></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "17",
                    question_en: "<p>11. <span style=\"font-family: Cambria Math;\">Third proportion of 15 and 120 is:</span></p>\n",
                    question_hi: "<p>11. <span style=\"font-family: Cambria Math;\">15 &#2324;&#2352; 120 &#2325;&#2366; &#2340;&#2371;&#2340;&#2368;&#2351; &#2360;&#2350;&#2366;&#2344;&#2369;&#2346;&#2366;&#2340;&#2368; &#2332;&#2381;&#2334;&#2366;&#2340; &#2325;&#2368;&#2332;&#2367;&#2319;&#2404;</span></p>\n",
                    options_en: ["<p>880</p>\n", "<p>960</p>\n", 
                                "<p>900</p>\n", "<p>- 860</p>\n"],
                    options_hi: ["<p>880</p>\n", "<p>960</p>\n",
                                "<p>900</p>\n", "<p>860</p>\n"],
                    solution_en: "<p>11.(b)</p>\r\n<p><span style=\"font-family: Cambria Math;\">Third proportional = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>120</mn><mo>&times;</mo><mn>120</mn></mrow><mn>15</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> = 960</span></p>\n",
                    solution_hi: "<p>11.(b)</p>\r\n<p><span style=\"font-family: Cambria Math;\">&#2340;&#2371;&#2340;&#2368;&#2351; &#2360;&#2350;&#2366;&#2344;&#2369;&#2346;&#2366;&#2340;&#2368;</span><span style=\"font-family: Cambria Math;\"> =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>120</mn><mo>&times;</mo><mn>120</mn></mrow><mn>15</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\">= 960</span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "17",
                    question_en: "<p>12. <span style=\"font-family: Cambria Math;\">Find 2 numbers such that their mean proportional is 25 and their third proportional is 25. </span></p>\n",
                    question_hi: "<p>12. <span style=\"font-family: Cambria Math;\">&#2320;&#2360;&#2368; 2 &#2360;&#2306;&#2326;&#2381;&#2351;&#2366;&#2319;&#2305; &#2332;&#2381;&#2334;&#2366;&#2340; &#2325;&#2368;&#2332;&#2367;&#2319; &#2332;&#2367;&#2344;&#2325;&#2366; &#2350;&#2366;&#2343;&#2381;&#2351; &#2360;&#2350;&#2366;&#2344;&#2369;&#2346;&#2366;&#2340;&#2368; 25 &#2361;&#2379; &#2324;&#2352; &#2313;&#2344;&#2325;&#2366; &#2340;&#2371;&#2340;&#2368;&#2351; &#2360;&#2350;&#2366;&#2344;&#2369;&#2346;&#2366;&#2340;&#2368; 25 &#2361;&#2379;&#2404;</span></p>\n",
                    options_en: ["<p>5 and 5</p>\n", "<p>5 and 25</p>\n", 
                                "<p>25 and 125</p>\n", "<p>25 and 25</p>\n"],
                    options_hi: ["<p>5 &#2324;&#2352; 5</p>\n", "<p>5 &#2324;&#2352; 25</p>\n",
                                "<p>25 &#2324;&#2352; 125</p>\n", "<p>25 &#2324;&#2352; 25</p>\n"],
                    solution_en: "<p>12.(d)</p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>Mean</mi><mo>&nbsp;</mo><mi>proportional</mi><mo>&nbsp;</mo><mo>(</mo><mn>25</mn><mo>)</mo><mo>=</mo><msqrt><mi>ab</mi></msqrt><mo>&nbsp;</mo><mo>&rArr;</mo><mi mathvariant=\"normal\">a</mi><mo>=</mo><mfrac><mn>625</mn><mi mathvariant=\"normal\">b</mi></mfrac><mspace linebreak=\"newline\"></mspace><mi>Third</mi><mo>&nbsp;</mo><mi>proportional</mi><mo>&nbsp;</mo><mo>(</mo><mn>25</mn><mo>)</mo><mo>=</mo><mfrac><msup><mi mathvariant=\"normal\">b</mi><mn>2</mn></msup><mi mathvariant=\"normal\">a</mi></mfrac><mspace linebreak=\"newline\"></mspace><mo>&rArr;</mo><msup><mi mathvariant=\"normal\">b</mi><mn>2</mn></msup><mo>=</mo><mn>25</mn><mi mathvariant=\"normal\">a</mi><mo>=</mo><mn>25</mn><mo>&times;</mo><mfrac><mn>625</mn><mi mathvariant=\"normal\">b</mi></mfrac><mspace linebreak=\"newline\"></mspace><mo>&rArr;</mo><mi mathvariant=\"normal\">b</mi><mo>=</mo><mn>25</mn><mo>&nbsp;</mo><mo>&nbsp;</mo><mi>and</mi><mo>&nbsp;</mo><mi mathvariant=\"normal\">a</mi><mo>=</mo><mn>25</mn></math></p>\n",
                    solution_hi: "<p>12.(d)</p>\r\n<p><math style=\"font-family:Verdana\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#2350;&#2366;&#2343;&#2381;&#2351;</mi><mo>&nbsp;</mo><mo>&nbsp;</mo><mi>&#2310;&#2344;&#2369;&#2346;&#2366;&#2340;&#2367;&#2325;</mi><mo>&nbsp;</mo><mo>(</mo><mn>25</mn><mo>)</mo><mo>=</mo><msqrt><mi>ab</mi></msqrt><mo>&nbsp;</mo><mo>&rArr;</mo><mi mathvariant=\"normal\">a</mi><mo>=</mo><mfrac><mn>625</mn><mi mathvariant=\"normal\">b</mi></mfrac><mspace linebreak=\"newline\"></mspace><mi>&#2340;&#2368;&#2360;&#2352;&#2366;</mi><mo>&nbsp;</mo><mo>&nbsp;</mo><mi>&#2310;&#2344;&#2369;&#2346;&#2366;&#2340;&#2367;&#2325;</mi><mo>(</mo><mn>25</mn><mo>)</mo><mo>=</mo><mfrac><msup><mi mathvariant=\"normal\">b</mi><mn>2</mn></msup><mi mathvariant=\"normal\">a</mi></mfrac><mspace linebreak=\"newline\"></mspace><mo>&rArr;</mo><msup><mi mathvariant=\"normal\">b</mi><mn>2</mn></msup><mo>=</mo><mn>25</mn><mi mathvariant=\"normal\">a</mi><mo>=</mo><mn>25</mn><mo>&times;</mo><mfrac><mn>625</mn><mi mathvariant=\"normal\">b</mi></mfrac><mspace linebreak=\"newline\"></mspace><mo>&rArr;</mo><mi mathvariant=\"normal\">b</mi><mo>=</mo><mn>25</mn><mo>&nbsp;</mo><mo>&nbsp;</mo><mi>&#2324;&#2352;</mi><mo>&nbsp;</mo><mo>&nbsp;</mo><mi mathvariant=\"normal\">a</mi><mo>=</mo><mn>25</mn></math></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "17",
                    question_en: "<p>13. <span style=\"font-family: Cambria Math;\">A man invests &#8377;44,000 in some shares in the ratio 1 : 4 : 5 which pay dividends of 10%, 15% and 25% on his investment for one year, respectively. His total dividend income is: </span></p>\n",
                    question_hi: "<p>13. <span style=\"font-family: Cambria Math;\">&#2319;&#2325; &#2357;&#2381;&#2351;&#2325;&#2381;&#2340;&#2367; 1: 4: 5 &#2325;&#2375; &#2309;&#2344;&#2369;&#2346;&#2366;&#2340; &#2350;&#2375;&#2306; &#2325;&#2369;&#2331; &#2358;&#2375;&#2351;&#2352;&#2379;&#2306; &#2350;&#2375;&#2306; &#8377;44,000 &#2325;&#2366; &#2344;&#2367;&#2357;&#2375;&#2358; &#2325;&#2352;&#2340;&#2366; &#2361;&#2376;, &#2332;&#2379; &#2319;&#2325; &#2357;&#2352;&#2381;&#2359; &#2325;&#2375; &#2354;&#2367;&#2319; &#2313;&#2360;&#2325;&#2375; &#2344;&#2367;&#2357;&#2375;&#2358; &#2346;&#2352; &#2325;&#2381;&#2352;&#2350;&#2358;&#2307; 10%, 15% &#2324;&#2352; 25% &#2325;&#2366; &#2354;&#2366;&#2349;&#2366;&#2306;&#2358; &#2342;&#2375;&#2340;&#2366; &#2361;&#2376;&#2404; &#2313;&#2360;&#2325;&#2368; &#2325;&#2369;&#2354; &#2354;&#2366;&#2349;&#2366;&#2306;&#2358; &#2310;&#2351; &#2332;&#2381;&#2334;&#2366;&#2340; &#2325;&#2368;&#2332;&#2367;&#2319;&#2404; </span></p>\n",
                    options_en: ["<p>&#8377;7,850</p>\n", "<p>&#8377;8,580</p>\n", 
                                "<p>&#8377;7,970</p>\n", "<p>&#8377;8,510</p>\n"],
                    options_hi: ["<p>&#8377;7,850</p>\n", "<p>&#8377;8,580</p>\n",
                                "<p>&#8377;7,970</p>\n", "<p>&#8377;8,510</p>\n"],
                    solution_en: "<p>13.(b)</p>\r\n<p><span style=\"font-family: Cambria Math;\">Net % dividends =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1</mn><mo>&times;</mo><mn>10</mn><mo>%</mo><mo>+</mo><mn>4</mn><mo>&times;</mo><mn>15</mn><mo>%</mo><mo>+</mo><mn>5</mn><mo>&times;</mo><mn>25</mn><mo>%</mo></mrow><mrow><mn>1</mn><mo>+</mo><mn>4</mn><mo>+</mo><mn>5</mn></mrow></mfrac></math></span><span style=\"font-family: Cambria Math;\">= 19.5%</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Total </span><span style=\"font-family: Cambria Math;\">dividend</span><span style=\"font-family: Cambria Math;\"> income = 44,000 &times;19.5% = &#8377;8580 </span></p>\n",
                    solution_hi: "<p>13.(b)</p>\r\n<p><span style=\"font-family: Cambria Math;\">&#2358;&#2369;&#2342;&#2381;&#2343; &#2354;&#2366;&#2349;&#2366;&#2306;&#2358; % =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1</mn><mo>&times;</mo><mn>10</mn><mo>%</mo><mo>+</mo><mn>4</mn><mo>&times;</mo><mn>15</mn><mo>%</mo><mo>+</mo><mn>5</mn><mo>&times;</mo><mn>25</mn><mo>%</mo></mrow><mrow><mn>1</mn><mo>+</mo><mn>4</mn><mo>+</mo><mn>5</mn></mrow></mfrac></math></span><span style=\"font-family: Cambria Math;\">= 19.5%</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&#2325;&#2369;&#2354; &#2354;&#2366;&#2349;&#2366;&#2306;&#2358; &#2310;&#2351; = 44,000 &times;19.5% = &#8377;8580 </span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "17",
                    question_en: "<p>14.<span style=\"font-family: Cambria Math;\"> The present age of a father is twice that of his son. 8.5 years hence the ratio would be 7 : 4. Find the son\'s present age.</span></p>\n",
                    question_hi: "<p>14.<span style=\"font-family: Cambria Math;\"> &#2319;&#2325; &#2346;&#2367;&#2340;&#2366; &#2325;&#2368; &#2357;&#2352;&#2381;&#2340;&#2350;&#2366;&#2344; &#2310;&#2351;&#2369; &#2313;&#2360;&#2325;&#2375; &#2346;&#2369;&#2340;&#2381;&#2352; &#2325;&#2368; &#2357;&#2352;&#2381;&#2340;&#2350;&#2366;&#2344; &#2310;&#2351;&#2369; &#2325;&#2368; &#2342;&#2379;&#2327;&#2369;&#2344;&#2368; &#2361;&#2376;&#2404; 8.5 &#2357;&#2352;&#2381;&#2359; &#2348;&#2366;&#2342; &#2313;&#2344;&#2325;&#2368; &#2310;&#2351;&#2369; &#2325;&#2366; &#2309;&#2344;&#2369;&#2346;&#2366;&#2340; 7: 4 &#2361;&#2379;&#2327;&#2366;&#2404; &#2346;&#2369;&#2340;&#2381;&#2352; &#2325;&#2368; &#2357;&#2352;&#2381;&#2340;&#2350;&#2366;&#2344; &#2310;&#2351;&#2369; &#2332;&#2381;&#2334;&#2366;&#2340; &#2325;&#2368;&#2332;&#2367;&#2319;&#2404; </span></p>\n",
                    options_en: ["<p>25.5 years</p>\n", "<p>16.5 years</p>\n", 
                                "<p><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">20.5 years</span></p>\n", "<p>24.6 years</p>\n"],
                    options_hi: ["<p>25.5 &#2357;&#2352;&#2381;&#2359;</p>\n", "<p>16.5 &#2357;&#2352;&#2381;&#2359;</p>\n",
                                "<p><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">20.5 &#2357;&#2352;&#2381;&#2359; </span></p>\n", "<p>24.6 &#2357;&#2352;&#2381;&#2359;</p>\n"],
                    solution_en: "<p>14.(a)</p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><mi mathvariant=\"normal\">x</mi><mo>+</mo><mn>8</mn><mo>.</mo><mn>5</mn></mrow><mrow><mi mathvariant=\"normal\">x</mi><mo>+</mo><mn>8</mn><mo>.</mo><mn>5</mn></mrow></mfrac><mo>=</mo><mfrac><mn>7</mn><mn>4</mn></mfrac></math></p>\r\n<p><span style=\"font-family: Cambria Math;\">x = 8.5 &times;3 = 25.5 years</span></p>\n",
                    solution_hi: "<p>14.(a)</p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><mi mathvariant=\"normal\">x</mi><mo>+</mo><mn>8</mn><mo>.</mo><mn>5</mn></mrow><mrow><mi mathvariant=\"normal\">x</mi><mo>+</mo><mn>8</mn><mo>.</mo><mn>5</mn></mrow></mfrac><mo>=</mo><mfrac><mn>7</mn><mn>4</mn></mfrac></math></p>\r\n<p><span style=\"font-family: Cambria Math;\">x = 8.5 &times;3 = 25.5 &#2360;&#2366;&#2354; </span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "misc",
                    question_en: "<p>15.<span style=\"font-family: Cambria Math;\">The sum of the amount that Sunaina and Ranjan have is &#8377;23 and the difference of their amount is &#8377;7. The ratio of the amount that Sunaina and Ranjan have is:</span><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                    question_hi: "<p>15. <span style=\"font-family: Cambria Math;\">&#2360;&#2369;&#2344;&#2376;&#2344;&#2366; &#2324;&#2352; &#2352;&#2306;&#2332;&#2344; &#2325;&#2368; &#2343;&#2344;&#2352;&#2366;&#2358;&#2367; &#2325;&#2366; &#2351;&#2379;&#2327; &#8377;23 &#2361;&#2376; &#2324;&#2352; &#2313;&#2344;&#2325;&#2368; &#2343;&#2344;&#2352;&#2366;&#2358;&#2367; &#2325;&#2366; &#2309;&#2306;&#2340;&#2352; &#8377;7 &#2361;&#2376;&#2404; &#2360;&#2369;&#2344;&#2376;&#2344;&#2366; &#2324;&#2352; &#2352;&#2306;&#2332;&#2344; &#2325;&#2375; &#2346;&#2366;&#2360; &#2343;&#2344;&#2352;&#2366;&#2358;&#2367; &#2325;&#2366; &#2309;&#2344;&#2369;&#2346;&#2366;&#2340; &#2325;&#2381;&#2351;&#2366; &#2361;&#2376;?</span></p>\n",
                    options_en: ["<p>5 : 3</p>\n", "<p>7 : 15</p>\n", 
                                "<p>15: 8</p>\n", "<p>2 : 17</p>\n"],
                    options_hi: ["<p>5 : 3</p>\n", "<p>7 : 15</p>\n",
                                "<p>15: 8</p>\n", "<p>2 : 17</p>\n"],
                    solution_en: "<p>15.(c)</p>\r\n<p><span style=\"font-family: Cambria Math;\">Required ratio= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><mfrac><mrow><mn>23</mn><mo>+</mo><mn>7</mn></mrow><mn>2</mn></mfrac><mo>)</mo><mo>&nbsp;</mo><mo>:</mo><mo>&nbsp;</mo><mo>(</mo><mfrac><mrow><mn>23</mn><mo>-</mo><mn>7</mn></mrow><mn>2</mn></mfrac><mo>)</mo></math></span><span style=\"font-family: Cambria Math;\">&nbsp;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">= 15 : 8</span></p>\n",
                    solution_hi: "<p>15.(c)</p>\r\n<p><span style=\"font-family: Cambria Math;\">&#2310;&#2357;&#2358;&#2381;&#2351;&#2325; &#2309;&#2344;&#2369;&#2346;&#2366;&#2340; =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><mfrac><mrow><mn>23</mn><mo>+</mo><mn>7</mn></mrow><mn>2</mn></mfrac><mo>)</mo><mo>&nbsp;</mo><mo>:</mo><mo>&nbsp;</mo><mo>(</mo><mfrac><mrow><mn>23</mn><mo>-</mo><mn>7</mn></mrow><mn>2</mn></mfrac><mo>)</mo></math></span></p>\r\n<p><span style=\"font-family: Cambria Math;\">= 15 : 8</span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>