<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">15:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 15</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">15</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 15 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 0,
                end: 13
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 14,
                end: 14
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "17",
                    question_en: "<p>1. The capacity of a cylinder tank is 8316 cm<sup>3</sup>. If the radius of its base is 21 cm, then find the depth of the tank.&nbsp;</p>",
                    question_hi: "<p>1. एक बेलनाकार टंकी की धारिता (क्षमता) 8316 cm<sup>3</sup> है। यदि इसके आधार की त्रिज्या 21 cm है, तो टंकी की गहराई ज्ञात करें।</p>",
                    options_en: ["<p>6 cm&nbsp;</p>", "<p>8 cm</p>", 
                                "<p>4 cm</p>", "<p>10 cm</p>"],
                    options_hi: ["<p>6 cm</p>", "<p>8 cm</p>",
                                "<p>4 cm</p>", "<p>10 cm</p>"],
                    solution_en: "<p>1.(a)<br>Capacity of cylinder = 8316 cm<sup>3</sup><br>&pi;r<sup>2</sup>h = 8316<br><math display=\"inline\"><mfrac><mrow><mn>22</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> &times; 21 &times; 21 &times; h = 8316<br>h = 8316 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mrow><mn>22</mn><mo>&#215;</mo><mn>21</mn><mo>&#215;</mo><mn>21</mn></mrow></mfrac></math> = 6 cm</p>",
                    solution_hi: "<p>1.(a)<br>सिलेंडर की क्षमता = 8316 सेमी<sup>3</sup><br>&pi;r<sup>2</sup>h = 8316<br><math display=\"inline\"><mfrac><mrow><mn>22</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> &times; 21 &times; 21 &times; h = 8316<br>h = 8316 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mrow><mn>22</mn><mo>&#215;</mo><mn>21</mn><mo>&#215;</mo><mn>21</mn></mrow></mfrac></math> = 6 सेमी</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "17",
                    question_en: "<p>2. The volume of a cube is reduced to 72.9%. By how much is the side of the cube reduced?</p>",
                    question_hi: "<p>2. एक घन का आयतन घटाकर 72.9% कर दिया जाता है। घन की भुजा कितना प्रतिशत कम हुई है?</p>",
                    options_en: ["<p>9.5%</p>", "<p>10%</p>", 
                                "<p>9%</p>", "<p>8.5%</p>"],
                    options_hi: ["<p>9.5%</p>", "<p>10%</p>",
                                "<p>9%</p>", "<p>8.5%</p>"],
                    solution_en: "<p>2.(b)<br>72.9% = <math display=\"inline\"><mfrac><mrow><mn>729</mn></mrow><mrow><mn>1000</mn></mrow></mfrac></math><br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>final</mi><mi mathvariant=\"normal\">&#160;</mi><mi>volume</mi><mi mathvariant=\"normal\">&#160;</mi><mi>of</mi><mi mathvariant=\"normal\">&#160;</mi><mi>cube</mi><mi mathvariant=\"normal\">&#160;</mi><mo>(</mo><msubsup><mi mathvariant=\"normal\">a</mi><mn>1</mn><mn>3</mn></msubsup><mo>)</mo></mrow><mrow><mi>initial</mi><mi mathvariant=\"normal\">&#160;</mi><mi>volume</mi><mi mathvariant=\"normal\">&#160;</mi><mi>of</mi><mi mathvariant=\"normal\">&#160;</mi><mi>cube</mi><mi mathvariant=\"normal\">&#160;</mi><mo>(</mo><msubsup><mi mathvariant=\"normal\">a</mi><mrow/><mn>3</mn></msubsup><mo>)</mo><mi mathvariant=\"normal\">&#160;</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>729</mn><mn>1000</mn></mfrac></math><br><math display=\"inline\"><mfrac><mrow><msub><mrow><mi mathvariant=\"bold-italic\">a</mi></mrow><mrow><mn>1</mn></mrow></msub></mrow><mrow><mi mathvariant=\"bold-italic\">a</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>9</mn><mn>10</mn></mfrac></math> <br>Hence, required % = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>10</mn></mrow></mfrac></math> &times; 100 = 10%</p>",
                    solution_hi: "<p>2.(b)<br>72.9% = <math display=\"inline\"><mfrac><mrow><mn>729</mn></mrow><mrow><mn>1000</mn></mrow></mfrac></math><br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2328;&#2344;</mi><mo>&#160;</mo><mi>&#2325;&#2366;</mi><mo>&#160;</mo><mi>&#2309;&#2306;&#2340;&#2367;&#2350;</mi><mo>&#160;</mo><mi>&#2310;&#2351;&#2340;&#2344;</mi><mo>(</mo><msubsup><mi mathvariant=\"normal\">a</mi><mn>1</mn><mn>3</mn></msubsup><mo>)</mo></mrow><mrow><mi>&#2328;&#2344;</mi><mo>&#160;</mo><mi>&#2325;&#2366;</mi><mo>&#160;</mo><mi>&#2346;&#2381;&#2352;&#2366;&#2352;&#2306;&#2349;&#2367;&#2325;</mi><mo>&#160;</mo><mi>&#2310;&#2351;&#2340;&#2344;</mi><mo>&#160;</mo><mi mathvariant=\"normal\">&#160;</mi><mo>(</mo><msup><mi mathvariant=\"normal\">a</mi><mn>3</mn></msup><mo>)</mo><mi mathvariant=\"normal\">&#160;</mi></mrow></mfrac></math> = <math display=\"inline\"><mfrac><mrow><mn>729</mn></mrow><mrow><mn>1000</mn></mrow></mfrac></math><br><math display=\"inline\"><mfrac><mrow><msub><mrow><mi mathvariant=\"bold-italic\">a</mi></mrow><mrow><mn>1</mn></mrow></msub></mrow><mrow><mi mathvariant=\"bold-italic\">a</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>9</mn><mn>10</mn></mfrac></math><br>अतः, आवश्यक % = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>10</mn></mrow></mfrac></math> &times; 100 = 10%</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "17",
                    question_en: "<p>3. Two equal circles are drawn on a square in such a way that opposite side of the square forms the diameter of each circle. If the remaining area of the square is 42 cm<sup>2</sup>, what is the measurement (in cm) of the diameter of each circle?</p>",
                    question_hi: "<p>3. एक वर्ग के अंदर दो समान वृत्त इस प्रकार बनाए जाते हैं कि वर्ग की विपरीत भुजा प्रत्येक वृत्त का व्यास बनाती है। यदि वर्ग का शेष क्षेत्रफल 42 cm<sup>2</sup> है, तो प्रत्येक वृत्त के व्यास की माप (cm में) कितनी है?</p>",
                    options_en: ["<p>14</p>", "<p>2.5</p>", 
                                "<p>7</p>", "<p>3.5</p>"],
                    options_hi: ["<p>14</p>", "<p>2.5</p>",
                                "<p>7</p>", "<p>3.5</p>"],
                    solution_en: "<p>3.(a)<br>Area of remaining area = area of square - 2 &times; area of semicircle <br>According to the question,<br>&rArr;<strong id=\"docs-internal-guid-b9825857-7fff-3c40-7040-b59fa3bd8edd\"> </strong>42 = (2r)<sup>2</sup>&nbsp;- 2 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math> &times; &pi; &times; (r)<sup>2</sup><br>&rArr; <strong id=\"docs-internal-guid-b9825857-7fff-3c40-7040-b59fa3bd8edd\"></strong>42 = 4r<sup>2</sup>&nbsp;- <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>22</mn><mn>7</mn></mfrac></math>&times; (r)<sup>2</sup><br>&rArr; <strong id=\"docs-internal-guid-b9825857-7fff-3c40-7040-b59fa3bd8edd\"></strong>42 = r<sup>2</sup> (4 - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>22</mn><mn>7</mn></mfrac></math>)<br>&rArr; <strong id=\"docs-internal-guid-b9825857-7fff-3c40-7040-b59fa3bd8edd\"></strong>42 = r<sup>2</sup>&nbsp;( <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>6</mn><mn>7</mn></mfrac></math>)<br>&rArr; <strong id=\"docs-internal-guid-b9825857-7fff-3c40-7040-b59fa3bd8edd\"></strong>r<sup>2</sup> = 42 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>6</mn></mfrac></math> = 49<br>&rArr; <strong id=\"docs-internal-guid-b9825857-7fff-3c40-7040-b59fa3bd8edd\"></strong>r = 7<br>So, diamete<math display=\"inline\"><mi>r</mi></math> of each circle (2r) = 14 cm</p>",
                    solution_hi: "<p>3.(a)<br>शेष क्षेत्र का क्षेत्रफल = वर्ग का क्षेत्रफल - 2 &times; अर्धवृत्त का क्षेत्रफल <br>प्रश्न के अनुसार,<br>&rArr; <strong id=\"docs-internal-guid-b9825857-7fff-3c40-7040-b59fa3bd8edd\"></strong>42 = (2r)<sup>2</sup>&nbsp;- 2 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math> &times; &pi; &times; (r)<sup>2</sup><br>&rArr; <strong id=\"docs-internal-guid-b9825857-7fff-3c40-7040-b59fa3bd8edd\"></strong>42 = 4r<sup>2</sup>&nbsp;- <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>22</mn><mn>7</mn></mfrac></math>&times; (r)<sup>2</sup><br>&rArr; <strong id=\"docs-internal-guid-b9825857-7fff-3c40-7040-b59fa3bd8edd\"></strong>42 = r<sup>2</sup> (4 - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>22</mn><mn>7</mn></mfrac></math>)<br>&rArr; <strong id=\"docs-internal-guid-b9825857-7fff-3c40-7040-b59fa3bd8edd\"></strong>42 = r<sup>2</sup>&nbsp;( <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>6</mn><mn>7</mn></mfrac></math>)<br>&rArr; <strong id=\"docs-internal-guid-b9825857-7fff-3c40-7040-b59fa3bd8edd\"></strong>r<sup>2</sup> = 42 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>6</mn></mfrac></math> = 49<br>&rArr; <strong id=\"docs-internal-guid-b9825857-7fff-3c40-7040-b59fa3bd8edd\"></strong>r = 7<br>अतः, प्रत्येक वृत्त का व्यास (2r) = 14 सेमी</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "17",
                    question_en: "<p>4. A hemisphere has a radius of 6.2 cm. Find its total surface area (use &pi; = 3.14) (round off to two decimal places).</p>",
                    question_hi: "<p>4. एक अर्धगोले की त्रिज्या 6.2 cm है। इसका कुल पृष्ठीय क्षेत्रफल ज्ञात कीजिए (&pi; = 3.14 का प्रयोग कीजिए) (दशमलव के दो स्थानों तक सन्निकटित)।</p>",
                    options_en: ["<p>276.35 cm<sup>2</sup></p>", "<p>282.25 cm<sup>2</sup></p>", 
                                "<p>358.52 cm<sup>2</sup></p>", "<p>362.10 cm<sup>2</sup></p>"],
                    options_hi: ["<p>276.35 cm<sup>2</sup></p>", "<p>282.25 cm<sup>2</sup></p>",
                                "<p>358.52 cm<sup>2</sup></p>", "<p>362.10 cm<sup>2</sup></p>"],
                    solution_en: "<p>4.(d)<br>Total surface area of hemisphere = 3&pi;r<sup>2</sup><br>&rArr; 3 &times; 3.14 &times; (6.2)<sup>2</sup> = 362.10 cm<sup>2</sup>&nbsp;</p>",
                    solution_hi: "<p>4.(d)<br>गोलार्ध का कुल पृष्ठीय क्षेत्रफल = 3&pi;r<sup>2</sup><br>&rArr; 3 &times; 3.14 &times; (6.2)<sup>2</sup> = 362.10 सेमी<sup>2</sup></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "17",
                    question_en: "<p>5. If the side of a square is increased by 5%, then the percentage increase in its area will be:</p>",
                    question_hi: "<p>5. यदि किसी वर्ग की भुजा में 5% की वृद्धि की जाती है, तो उसके क्षेत्रफल में कितने प्रतिशत की वृद्धि होगी?</p>",
                    options_en: ["<p>11%</p>", "<p>10.25%</p>", 
                                "<p>10%</p>", "<p>10.5%</p>"],
                    options_hi: ["<p>11%</p>", "<p>10.25%</p>",
                                "<p>10%</p>", "<p>10.5%</p>"],
                    solution_en: "<p>5.(b)<br>Required % = 5 + 5 + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5</mn><mo>&#215;</mo><mn>5</mn></mrow><mn>100</mn></mfrac></math> = 10 + 0.25 = 10.25%</p>",
                    solution_hi: "<p>5.(b)<br>आवश्यक % = 5 + 5 + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5</mn><mo>&#215;</mo><mn>5</mn></mrow><mn>100</mn></mfrac></math>&nbsp;= 10 + 0.25 = 10.25%</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "17",
                    question_en: "<p>6. If the brick size is 25 cm &times; 12 cm &times; 9 cm, then how many bricks are required to construct a wall of length 10 m, breadth 22.5 cm and height 6 m?</p>",
                    question_hi: "<p>6. यदि ईंट का आकार 25 cm &times; 12 cm &times; 9 cm है, तो 10 m लंबी, 22.5 cm चौड़ी और 6 m ऊंची दीवार&nbsp;बनाने के लिए कितनी ईंटों की आवश्यकता होगी?</p>",
                    options_en: ["<p>4500</p>", "<p>6000</p>", 
                                "<p>8000</p>", "<p>5000</p>"],
                    options_hi: ["<p>4500</p>", "<p>6000</p>",
                                "<p>8000</p>", "<p>5000</p>"],
                    solution_en: "<p>6.(d)<br>Required bricks = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>10</mn><mo>&#215;</mo><mn>100</mn><mo>)</mo><mo>&#215;</mo><mn>22</mn><mo>.</mo><mn>5</mn><mo>&#215;</mo><mo>(</mo><mn>6</mn><mo>&#215;</mo><mn>100</mn><mo>)</mo></mrow><mrow><mn>25</mn><mo>&#215;</mo><mn>12</mn><mo>&#215;</mo><mn>9</mn></mrow></mfrac></math> = 40 &times; 2.5 &times; 50 = 5000</p>",
                    solution_hi: "<p>6.(d)<br>आवश्यक ईंटें = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>10</mn><mo>&#215;</mo><mn>100</mn><mo>)</mo><mo>&#215;</mo><mn>22</mn><mo>.</mo><mn>5</mn><mo>&#215;</mo><mo>(</mo><mn>6</mn><mo>&#215;</mo><mn>100</mn><mo>)</mo></mrow><mrow><mn>25</mn><mo>&#215;</mo><mn>12</mn><mo>&#215;</mo><mn>9</mn></mrow></mfrac></math>&nbsp;= 40 &times; 2.5 &times; 50 = 5000</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "17",
                    question_en: "<p>7. An iron box with external dimensions 60 cm, 40 cm, and 20 cm is made of 1 cm thick sheet. If 1 cm<sup>3</sup> of iron weighs 50 gm, the weight of the empty box is:</p>",
                    question_hi: "<p>7. 60 cm, 40 cm और 20 cm बाहरी आयामों वाला एक लोहे का बक्सा 1 cm मोटी चादर से बना है। यदि 1 cm<sup>3</sup> लोहे का भार 50 gm है, तो खाली बक्से का भार कितना होगा ?</p>",
                    options_en: ["<p>416.40 kg</p>", "<p>240 kg</p>", 
                                "<p>214.05 kg</p>", "<p>400 kg</p>"],
                    options_hi: ["<p>416.40 kg</p>", "<p>240 kg</p>",
                                "<p>214.05 kg</p>", "<p>400 kg</p>"],
                    solution_en: "<p>7.(a)<br>Weight of the empty box = [(60 &times; 40 &times; 20) - (58 &times; 38 &times; 18)] &times; 50<br>= [48000 - 39672] &times; 50<br>= [8328] &times; 50<br>= 416400 gm<br>Weight of the empty box = <math display=\"inline\"><mfrac><mrow><mn>416400</mn><mi>&#160;</mi></mrow><mrow><mn>1000</mn></mrow></mfrac></math> = 416.4 kg</p>",
                    solution_hi: "<p>7.(a)<br>खाली डिब्बे का वजन = [(60 &times; 40 &times; 20) - (58 &times; 38 &times; 18)] &times; 50<br>= [48000 - 39672] &times; 50<br>= [8328] &times; 50<br>= 416400 gm<br>खाली डिब्बे का वजन = <math display=\"inline\"><mfrac><mrow><mn>416400</mn><mi>&#160;</mi></mrow><mrow><mn>1000</mn></mrow></mfrac></math> = 416.4 kg</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "17",
                    question_en: "<p>8. If the diameter of a hemisphere is 42 cm, then the volume of hemisphere (in cm<sup>3</sup>) is:<br>Take &pi; = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>22</mn><mn>7</mn></mfrac></math></p>",
                    question_hi: "<p>8. यदि एक अर्धगोले का व्यास 42 cm है, तो अर्धगोले का आयतन (cm<sup>3</sup> में) _____________है।<br>( &pi; = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>22</mn><mn>7</mn></mfrac></math>&nbsp;का प्रयोग करें)</p>",
                    options_en: ["<p>19154</p>", "<p>19254</p>", 
                                "<p>19444</p>", "<p>19404</p>"],
                    options_hi: ["<p>19154</p>", "<p>19254</p>",
                                "<p>19444</p>", "<p>19404</p>"],
                    solution_en: "<p>8.(d) <br>Volume of the hemisphere = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>3</mn></mfrac></math>&pi;r<sup>3</sup><br>Volume of the hemisphere = <math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>22</mn><mn>7</mn></mfrac></math> &times; 21 &times; 21 &times; 21<br>Volume of the hemisphere = 19404 cm<sup>3</sup></p>",
                    solution_hi: "<p>8.(d) <br>अर्धगोले का आयतन = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>3</mn></mfrac></math>&pi;r<sup>3</sup><br>अर्धगोले का आयतन = <math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>22</mn><mn>7</mn></mfrac></math> &times; 21 &times; 21 &times; 21<br>अर्धगोले का आयतन = 19404 cm<sup>3</sup></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "17",
                    question_en: "<p>9. Find the difference between the curved surface area and total surface area of a right circular cylinder having a length of 68 cm and base diameter of 28 cm.</p>",
                    question_hi: "<p>9. 68 cm लंबाई और 28 cm के आधार व्यास वाले एक लंब वृत्तीय बेलन के वक्र पृष्ठीय क्षेत्रफल और संपूर्ण पृष्ठीय क्षेत्रफल के बीच का अंतर ज्ञात करें।</p>",
                    options_en: ["<p>1432 cm<sup>2</sup></p>", "<p>1332 cm<sup>2</sup></p>", 
                                "<p>1132 cm<sup>2</sup></p>", "<p>1232 cm<sup>2</sup></p>"],
                    options_hi: ["<p>1432 cm<sup>2</sup></p>", "<p>1332 cm<sup>2</sup></p>",
                                "<p>1132 cm<sup>2</sup></p>", "<p>1232 cm<sup>2</sup></p>"],
                    solution_en: "<p>9.(d)<br>Difference between TSA and CSA of cylinder = 2&pi;r(h + r) - 2&pi;rh<br>= 2&pi;r[h + r - h]<br>= 2&pi;r<sup>2</sup><br>Required difference (2&pi;r<sup>2</sup>) = 2 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>22</mn><mn>7</mn></mfrac></math> &times; 14 &times; 14 <br>= 1232 cm<sup>2</sup></p>",
                    solution_hi: "<p>9.(d)<br>बेलन के संपूर्ण पृष्ठीय क्षेत्रफल और पृष्ठीय क्षेत्रफल के बीच का अंतर =2&pi;r(h + r) - 2&pi;rh<br>= 2&pi;r[h + r - h]<br>= 2&pi;r<sup>2</sup><br>आवश्यक अंतर (2&pi;r<sup>2</sup>) = 2 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>22</mn><mn>7</mn></mfrac></math> &times; 14 &times; 14<br>= 1232 cm<sup>2</sup></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "17",
                    question_en: "<p>10. The perimeter of a triangle is 36 units. Its area cannot be more than ______ square units.</p>",
                    question_hi: "<p>10. एक त्रिभुज का परिमाप 36 इकाई है। इसका क्षेत्रफल ______ वर्ग इकाई से अधिक नहीं हो सकता।</p>",
                    options_en: ["<p>12<math display=\"inline\"><msqrt><mn>7</mn></msqrt></math></p>", "<p>36<math display=\"inline\"><msqrt><mn>2</mn></msqrt></math></p>", 
                                "<p>36<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math></p>", "<p>18<math display=\"inline\"><msqrt><mn>5</mn></msqrt></math></p>"],
                    options_hi: ["<p>12<math display=\"inline\"><msqrt><mn>7</mn></msqrt></math></p>", "<p>36<math display=\"inline\"><msqrt><mn>2</mn></msqrt></math></p>",
                                "<p>36<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math></p>", "<p>18<math display=\"inline\"><msqrt><mn>5</mn></msqrt></math></p>"],
                    solution_en: "<p>10.(c)<br>To find the maximum possible area of a triangle with a perimeter of 36 units, we assume it&rsquo;s an equilateral triangle.<br>Perimeter = 3a = 36, a = 12 units<br>Area of the equilateral triangle = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msqrt><mn>3</mn></msqrt><mn>4</mn></mfrac></math> side<sup>2</sup><br>= <math display=\"inline\"><mfrac><mrow><msqrt><mn>3</mn></msqrt></mrow><mrow><mn>4</mn></mrow></mfrac></math> &times; 12 &times; 12<br>= 36<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math>&nbsp;square units</p>",
                    solution_hi: "<p>10.(c)<br>36 इकाई की परिधि वाले त्रिभुज का अधिकतम संभव क्षेत्रफल ज्ञात करने के लिए, हम मानते हैं कि यह एक समबाहु त्रिभुज है।<br>परिमाप = 3a = 36, a = 12 इकाई<br>समबाहु त्रिभुज का क्षेत्रफल = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msqrt><mn>3</mn></msqrt><mn>4</mn></mfrac></math> भुजा<sup>2</sup><br>= <math display=\"inline\"><mfrac><mrow><msqrt><mn>3</mn></msqrt></mrow><mrow><mn>4</mn></mrow></mfrac></math> &times; 12 &times; 12<br>= 36<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math>&nbsp;वर्ग इकाई</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "17",
                    question_en: "<p>11. The outer surface of a sphere having diameter 10 m is painted at the rate of ₹ <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>80</mn><mi mathvariant=\"normal\">&#960;</mi></mfrac></math> per m<sup>2</sup>. What is the cost of painting?</p>",
                    question_hi: "<p>11. 10 m व्यास वाले एक गोले के बाह्य पृष्ठ को ₹ <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>80</mn><mi mathvariant=\"normal\">&#960;</mi></mfrac></math> प्रति वर्ग मीटर की दर से पेंट किया गया। तो पेंटिंग की लागत कितनी है?</p>",
                    options_en: ["<p>₹8,000</p>", "<p>₹9,000</p>", 
                                "<p>₹4,000</p>", "<p>₹6,000</p>"],
                    options_hi: ["<p>₹8,000</p>", "<p>₹9,000</p>",
                                "<p>₹4,000</p>", "<p>₹6,000</p>"],
                    solution_en: "<p>11.(a)<br>TSA of the sphere = 4&pi;r<sup>2</sup><br>Required cost = 4 &times; &pi; &times; 5 &times; 5 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>80</mn><mi mathvariant=\"normal\">&#960;</mi></mfrac></math> = ₹ 8000</p>",
                    solution_hi: "<p>11.(a)<br>गोले का सम्पूर्ण पृष्ठाय क्षेत्रफल = 4&pi;r<sup>2</sup><br>आवश्यक लागत = 4 &times; &pi; &times; 5 &times; 5 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>80</mn><mi mathvariant=\"normal\">&#960;</mi></mfrac></math> = ₹ 8000</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "17",
                    question_en: "<p>12. The triangle has sides 3 cm, 4 cm and 5 cm. What is the length of the perpendicular from the opposite vertex to the side whose length is 5 cm?</p>",
                    question_hi: "<p>12. एक त्रिभुज की भुजाएं 3 cm, 4 cm और 5 cm हैं। 5 cm लंबाई वाली भुजा पर विपरीत शीर्ष से डाले गए लंब की लंबाई ज्ञात कीजिए।</p>",
                    options_en: ["<p>3.5 cm</p>", "<p>2.4 cm</p>", 
                                "<p>2.2 cm</p>", "<p>1.4 cm</p>"],
                    options_hi: ["<p>3.5 cm</p>", "<p>2.4 cm</p>",
                                "<p>2.2 cm</p>", "<p>1.4 cm</p>"],
                    solution_en: "<p>12.(b)<br>3, 4, 5 are triplet of right angle triangle so,<br>Length of the perpendicular = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi mathvariant=\"normal\">p</mi><mo>&#215;</mo><mi mathvariant=\"normal\">b</mi></mrow><mi mathvariant=\"normal\">h</mi></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>3</mn><mo>&#215;</mo><mn>4</mn></mrow><mn>5</mn></mfrac></math> = 2.4 cm</p>",
                    solution_hi: "<p>12.(b)<br>3, 4, 5 समकोण त्रिभुज के त्रिक हैं इसलिए,<br>लम्ब की लंबाई = <math style=\"font-family:Verdana\" display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"true\"><mfrac><mrow><mi>&#2354;&#2306;&#2348;</mi><mo>&#215;</mo><mi>&#2310;&#2343;&#2366;&#2352;</mi></mrow><mrow><mi>&#2325;&#2352;&#2381;&#2339;</mi><mo>&#160;</mo></mrow></mfrac></mstyle></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>3</mn><mo>&#215;</mo><mn>4</mn></mrow><mn>5</mn></mfrac></math> = 2.4 cm</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "17",
                    question_en: "<p>13. The radius and height of a cylinder are in the ratio 6 : 7 and its volume is 792 cm<sup>3</sup> . Calculate its curved surface area in cm<sup>2</sup></p>",
                    question_hi: "<p>13. एक बेलन की त्रिज्या और ऊंचाई का अनुपात 6 : 7 है और इसका आयतन 792 cm<sup>3</sup> है। इसके वक्र पृष्ठीय क्षेत्रफल की गणना cm<sup>2</sup> में कीजिए।</p>",
                    options_en: ["<p>262</p>", "<p>490</p>", 
                                "<p>264</p>", "<p>226</p>"],
                    options_hi: ["<p>262</p>", "<p>490</p>",
                                "<p>264</p>", "<p>226</p>"],
                    solution_en: "<p>13.(c) <br>Volume of the cylinder = &pi;r<sup>2</sup>h<br><math display=\"inline\"><mfrac><mrow><mn>22</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> &times; 6x<sup>2</sup>(7x) = 792<br>x<sup>3</sup> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>792</mn><mo>&#215;</mo><mn>7</mn></mrow><mrow><mn>22</mn><mo>&#215;</mo><mn>36</mn><mo>&#215;</mo><mn>7</mn></mrow></mfrac></math> = 1 so x = 1<br>So, the radius and height of the cylinder will be 6 and 7<br>Now, according to the question,<br>CSA of the cylinder = 2&pi;rh<br>= 2 &times; <math display=\"inline\"><mfrac><mrow><mn>22</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> &times; 6 &times; 7 <br>= 264 cm<sup>2</sup></p>",
                    solution_hi: "<p>13.(c) <br>बेलन का आयतन = &pi;r<sup>2</sup>h<br><math display=\"inline\"><mfrac><mrow><mn>22</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> &times; 6x<sup>2</sup>(7x) = 792<br>x<sup>3</sup> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>792</mn><mo>&#215;</mo><mn>7</mn></mrow><mrow><mn>22</mn><mo>&#215;</mo><mn>36</mn><mo>&#215;</mo><mn>7</mn></mrow></mfrac></math> = 1 इसलिए x = 1<br>तो, बेलन की त्रिज्या और ऊंचाई 6 और 7 होगी<br>अब, प्रश्न के अनुसार,<br>बेलन का वक्र पृष्ठीय क्षेत्रफल = 2&pi;rh<br>= 2 &times; <math display=\"inline\"><mfrac><mrow><mn>22</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> &times; 6 &times; 7 <br>= 264 cm<sup>2</sup></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "17",
                    question_en: "<p>14. The internal length, breadth and height of a cardboard box are 6 cm, 10 cm and 12 cm, respectively. How many boxes are needed in which cubes whose volume is 2160 cm<sup>3</sup> can be packed?</p>",
                    question_hi: "<p>14. एक गत्ते के डिब्बे की आंतरिक लंबाई, चौड़ाई और ऊंचाई क्रमशः 6 cm, 10 cm और 12 cm है। ऐसे कितने डिब्बों की आवश्यकता होगी जिनमें 2160 cm3 आयतन वाले घनों को पैक किया जा सके?</p>",
                    options_en: ["<p>6</p>", "<p>5</p>", 
                                "<p>3</p>", "<p>4</p>"],
                    options_hi: ["<p>6</p>", "<p>5</p>",
                                "<p>3</p>", "<p>4</p>"],
                    solution_en: "<p>14.(c)<br>Required number of boxes = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2160</mn><mrow><mn>6</mn><mo>&#215;</mo><mn>10</mn><mo>&#215;</mo><mn>12</mn></mrow></mfrac></math> = 3</p>",
                    solution_hi: "<p>14.(c)<br>बक्सों की आवश्यक संख्या = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2160</mn><mrow><mn>6</mn><mo>&#215;</mo><mn>10</mn><mo>&#215;</mo><mn>12</mn></mrow></mfrac></math>&nbsp;= 3</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "misc",
                    question_en: "<p>15. The heights of two cones are in the ratio of 5 : 3 and their radii are in the ratio 6 : 5. Find the ratio of their volumes.</p>",
                    question_hi: "<p>15. दो शंकुओं की ऊँचाइयों का अनुपात 5 : 3 है और उनकी त्रिज्याएँ 6 : 5 के अनुपात में हैं। उनके आयतनों का अनुपात ज्ञात कीजिए।</p>",
                    options_en: ["<p>10 : 5</p>", "<p>13 : 5</p>", 
                                "<p>12 : 5</p>", "<p>11 : 5</p>"],
                    options_hi: ["<p>10 : 5</p>", "<p>13 : 5</p>",
                                "<p>12 : 5</p>", "<p>11 : 5</p>"],
                    solution_en: "<p>15.(c)<br>Volume of the cone = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>3</mn></mfrac></math>&pi;r<sup>2</sup>h<br>Required ratio =<strong> </strong>6<sup>2</sup> &times; 5&nbsp;: 5<sup>2</sup> &times; 3<br>= 12 : 5</p>",
                    solution_hi: "<p>15.(c)<br>शंकु का आयतन = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>3</mn></mfrac></math>&pi;r<sup>2</sup>h<br>आवश्यक अनुपात = 6<sup>2</sup> &times; 5&nbsp;: 5<sup>2</sup> &times; 3<br>= 12 : 5</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>