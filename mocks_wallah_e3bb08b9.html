<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">6:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 15</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">15</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 6 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["7"] = {
                name: "English",
                start: 0,
                end: 13
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="7">English</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 14,
                end: 14
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "7",
                    question_en: "<p>1. Select the option that expresses the following sentence in active voice.<br>The car was repaired by the mechanic.</p>",
                    question_hi: "<p>1. Select the option that expresses the following sentence in active voice.<br>The car was repaired by the mechanic.</p>",
                    options_en: ["<p>The mechanic repairs the car.</p>", "<p>The mechanic has repaired the car.</p>", 
                                "<p>The mechanic is repairing the car.</p>", "<p>The mechanic repaired the car.</p>"],
                    options_hi: ["<p>The mechanic repairs the car.</p>", "<p>The mechanic has repaired the car.</p>",
                                "<p>The mechanic is repairing the car.</p>", "<p>The mechanic repaired the car.</p>"],
                    solution_en: "<p>1.(d) The mechanic repaired the car. (Correct)<br>(a) The mechanic <span style=\"text-decoration: underline;\">repairs </span>the car. (Incorrect Tense)<br>(b) The mechanic <span style=\"text-decoration: underline;\">has repaired</span> the car. (Incorrect Tense)<br>(c) The mechanic <span style=\"text-decoration: underline;\">is repairing</span> the car. (Incorrect Tense)</p>",
                    solution_hi: "<p>1.(d) The mechanic repaired the car. (Correct)<br>(a) The mechanic <span style=\"text-decoration: underline;\">repairs </span>the car. (गलत Tense)<br>(b) The mechanic <span style=\"text-decoration: underline;\">has repaired</span> the car. (गलत Tense)<br>(c) The mechanic <span style=\"text-decoration: underline;\">is repairing</span> the car. (गलत Tense)</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "7",
                    question_en: "<p>2. Select the option that expresses the given sentence in active voice. <br>The letter was being typed by the secretary when the phone rang.</p>",
                    question_hi: "<p>2. Select the option that expresses the given sentence in active voice. <br>The letter was being typed by the secretary when the phone rang.</p>",
                    options_en: ["<p>The secretary will type the letter when the phone rings.</p>", "<p>The secretary types the letter when the phone rings</p>", 
                                "<p>The secretary was typing the letter when the phone rang.</p>", "<p>The secretary is typing the letter when the phone rings.</p>"],
                    options_hi: ["<p>The secretary will type the letter when the phone rings.</p>", "<p>The secretary types the letter when the phone rings</p>",
                                "<p>The secretary was typing the letter when the phone rang.</p>", "<p>The secretary is typing the letter when the phone rings.</p>"],
                    solution_en: "<p>2.(c) The secretary was typing the letter when the phone rang. (Correct)<br>(a) The secretary <span style=\"text-decoration: underline;\">will type</span> the letter when the phone <span style=\"text-decoration: underline;\">rings</span>. (Incorrect Tense)<br>(b) The secretary <span style=\"text-decoration: underline;\">types </span>the letter when the phone <span style=\"text-decoration: underline;\">rings</span>. (Incorrect Tense)<br>(d) The secretary <span style=\"text-decoration: underline;\">is typing</span> the letter when the phone <span style=\"text-decoration: underline;\">rings</span>. (Incorrect Tense)</p>",
                    solution_hi: "<p>2.(c) The secretary was typing the letter when the phone rang. (Correct)<br>(a) The secretary <span style=\"text-decoration: underline;\">will type</span> the letter when the phone <span style=\"text-decoration: underline;\">rings</span>. (गलत Tense)<br>(b) The secretary <span style=\"text-decoration: underline;\">types </span>the letter when the phone <span style=\"text-decoration: underline;\">rings</span>. (गलत Tense)<br>(d) The secretary <span style=\"text-decoration: underline;\">is typing</span> the letter when the phone <span style=\"text-decoration: underline;\">rings</span>. (गलत Tense)</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "7",
                    question_en: "<p>3. Select the option that expresses the given sentence in passive voice. <br>He must deliver pizza.</p>",
                    question_hi: "<p>3. Select the option that expresses the given sentence in passive voice. <br>He must deliver pizza.</p>",
                    options_en: ["<p>Pizza has to be delivered by him.</p>", "<p>Pizza have to be delivered by him.</p>", 
                                "<p>Pizza must be delivered by him.</p>", "<p>Pizza can be delivered by him.</p>"],
                    options_hi: ["<p>Pizza has to be delivered by him.</p>", "<p>Pizza have to be delivered by him.</p>",
                                "<p>Pizza must be delivered by him.</p>", "<p>Pizza can be delivered by him.</p>"],
                    solution_en: "<p>3.(c) Pizza must be delivered by him. (Correct)<br>(a) Pizza <span style=\"text-decoration: underline;\">has to be</span> delivered by him. (Incorrect Verb)<br>(b) Pizza <span style=\"text-decoration: underline;\">have to be</span> delivered by him. (Incorrect Verb)<br>(d) Pizza <span style=\"text-decoration: underline;\">can</span> be delivered by him. (Incorrect Modal)</p>",
                    solution_hi: "<p>3.(c) Pizza must be delivered by him. (Correct)<br>(a) Pizza <span style=\"text-decoration: underline;\">has to be</span> delivered by him. (गलत Verb)<br>(b) Pizza <span style=\"text-decoration: underline;\">have to be</span> delivered by him. (गलत Verb)<br>(d) Pizza <span style=\"text-decoration: underline;\">can </span>be delivered by him. (गलत Modal)</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "7",
                    question_en: "<p>4. Select the option that expresses the following sentence in passive voice.<br>Sonam does not like bananas.</p>",
                    question_hi: "<p>4. Select the option that expresses the following sentence in passive voice.<br>Sonam does not like bananas.</p>",
                    options_en: ["<p>Banana is not liked by Sonam.</p>", "<p>Bananas have not been liked by Sonam.</p>", 
                                "<p>Bananas are not liked by Sonam.</p>", "<p>Bananas had not been liked by Sonam.</p>"],
                    options_hi: ["<p>Banana is not liked by Sonam.</p>", "<p>Bananas have not been liked by Sonam.</p>",
                                "<p>Bananas are not liked by Sonam.</p>", "<p>Bananas had not been liked by Sonam.</p>"],
                    solution_en: "<p>4.(c) Bananas are not liked by Sonam. (Correct)<br>(a) Banana <span style=\"text-decoration: underline;\">is </span>not liked by Sonam. (Incorrect Verb)<br>(b) Bananas <span style=\"text-decoration: underline;\">have not been liked</span> by Sonam. (Incorrect Tense)<br>(d) Bananas <span style=\"text-decoration: underline;\">had not been liked</span> by Sonam. (Incorrect Tense)</p>",
                    solution_hi: "<p>4.(c) Bananas are not liked by Sonam. (Correct)<br>(a) Banana <span style=\"text-decoration: underline;\">is</span> not liked by Sonam. (गलत Verb)<br>(b) Bananas <span style=\"text-decoration: underline;\">have not been liked</span> by Sonam. (गलत Tense)<br>(d) Bananas <span style=\"text-decoration: underline;\">had not been liked</span> by Sonam. (गलत Tense)</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "7",
                    question_en: "<p>5. Select the option that expresses the given sentence in passive voice<br>Someone has taken my secret diary.</p>",
                    question_hi: "<p>5. Select the option that expresses the given sentence in passive voice<br>Someone has taken my secret diary.</p>",
                    options_en: ["<p>My secret diary had been taken by someone.</p>", "<p>My secret diary has to be taken by someone.</p>", 
                                "<p>My secret diary has been taken by someone.</p>", "<p>My secret diary will have been taken by someone.</p>"],
                    options_hi: ["<p>My secret diary had been taken by someone.</p>", "<p>My secret diary has to be taken by someone.</p>",
                                "<p>My secret diary has been taken by someone.</p>", "<p>My secret diary will have been taken by someone.</p>"],
                    solution_en: "<p>5.(c) My secret diary has been taken by someone. (Correct)<br>(a) My secret diary <span style=\"text-decoration: underline;\">had been taken</span> by someone. (Incorrect Tense)<br>(b) My secret diary <span style=\"text-decoration: underline;\">has to be</span> taken by someone. (Incorrect Verb)<br>(d) My secret diary <span style=\"text-decoration: underline;\">will have been taken</span> by someone. (Incorrect Tense)</p>",
                    solution_hi: "<p>5.(c) My secret diary has been taken by someone. (Correct)<br>(a) My secret diary <span style=\"text-decoration: underline;\">had been taken</span> by someone. (गलत Tense)<br>(b) My secret diary <span style=\"text-decoration: underline;\">has to be</span> taken by someone. (गलत Verb)<br>(d) My secret diary <span style=\"text-decoration: underline;\">will have been taken</span> by someone. (गलत Tense)</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "7",
                    question_en: "6. Select the option that expresses the given sentence in active voice.<br />The drug pedlar was arrested by the narcotics team.",
                    question_hi: "6. Select the option that expresses the given sentence in active voice.<br />The drug pedlar was arrested by the narcotics team.",
                    options_en: [" The narcotics team was arrested the drug pedlar.", " The narcotics team arrest the drug pedlar.", 
                                "The narcotics team arrested the drug pedlar.", " The narcotics team has arrested the drug pedlar."],
                    options_hi: [" The narcotics team was arrested the drug pedlar.", " The narcotics team arrest the drug pedlar.",
                                "The narcotics team arrested the drug pedlar.", " The narcotics team has arrested the drug pedlar."],
                    solution_en: "<p>6.(c) The narcotics team arrested the drug pedlar. (Correct)<br>(a) The narcotics team <span style=\"text-decoration: underline;\">was </span>arrested the drug pedlar. (Incorrect Use of &lsquo;was&rsquo;)<br>(b) The narcotics team <span style=\"text-decoration: underline;\">arrest </span>the drug pedlar. (Incorrect Tense)<br>(c) The narcotics team <span style=\"text-decoration: underline;\">has arrested</span> the drug pedlar. (Incorrect Tense)</p>",
                    solution_hi: "<p>6.(c) The narcotics team arrested the drug pedlar. (Correct)<br>(a) The narcotics team <span style=\"text-decoration: underline;\">was </span>arrested the drug pedlar. ( &lsquo;was&rsquo; का गलत प्रयोग)<br>(b) The narcotics team <span style=\"text-decoration: underline;\">arrest </span>the drug pedlar. (गलत Tense)<br>(c) The narcotics team <span style=\"text-decoration: underline;\">has arrested</span> the drug pedlar. (गलत Tense)</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "7",
                    question_en: "7. Select the option that expresses the given sentence in passive voice.<br />Scientists were conducting experiments on the newly discovered species.",
                    question_hi: "7. Select the option that expresses the given sentence in passive voice.<br />Scientists were conducting experiments on the newly discovered species.",
                    options_en: [" Experiments on the newly discovered species had been conducted by the scientists.", " The newly discovered species has been conducting experiments by scientists.", 
                                " The newly discovered species has been experimented on by scientists.", " Experiments were being conducted on the newly discovered species by scientists."],
                    options_hi: [" Experiments on the newly discovered species had been conducted by the scientists.", " The newly discovered species has been conducting experiments by scientists.",
                                " The newly discovered species has been experimented on by scientists.", " Experiments were being conducted on the newly discovered species by scientists."],
                    solution_en: "7.(d) Experiments were being conducted on the newly discovered species by scientists. (Correct)<br />(a) Experiments on the newly discovered species had been conducted by the scientists. (Incorrect Sentence Structure)<br />(b) The newly discovered species has been conducting experiments by scientists. (Incorrect Sentence Structure)<br />(c) The newly discovered species has been experimented on by scientists. (Incorrect Sentence Structure)",
                    solution_hi: "7.(d) Experiments were being conducted on the newly discovered species by scientists. (Correct)<br />(a) Experiments on the newly discovered species had been conducted by the scientists. (गलत  Sentence Structure)<br />(b) The newly discovered species has been conducting experiments by scientists. (गलत  Sentence Structure)<br />(c) The newly discovered species has been experimented on by scientists. (गलत  Sentence Structure)",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "7",
                    question_en: "<p>8. Select the option that correctly expresses the given sentence in passive voice.<br>I will never overlook this creativity.</p>",
                    question_hi: "<p>8. Select the option that correctly expresses the given sentence in passive voice.<br>I will never overlook this creativity.</p>",
                    options_en: ["<p>This creativity will never be overlooked by me.</p>", "<p>This creativity will be overlooked by me.</p>", 
                                "<p>This creativity will being overlooked by me.</p>", "<p>This creativity will not never be overlooked by me.</p>"],
                    options_hi: ["<p>This creativity will never be overlooked by me.</p>", "<p>This creativity will be overlooked by me.</p>",
                                "<p>This creativity will being overlooked by me.</p>", "<p>This creativity will not never be overlooked by me.</p>"],
                    solution_en: "<p>8.(a) This creativity will never be overlooked by me. (Correct)<br>(b) This creativity will be overlooked by me. (&lsquo;Never&rsquo; is missing)<br>(c) This creativity <span style=\"text-decoration: underline;\">will being</span> overlooked by me. (Incorrect Verb)<br>(d) This creativity will not never be overlooked by me. (Incorrect Use of &lsquo;not&rsquo;)</p>",
                    solution_hi: "<p>8.(a) This creativity will never be overlooked by me. (Correct)<br>(b) This creativity will be overlooked by me. (&lsquo;Never&rsquo; missing है )<br>(c) This creativity <span style=\"text-decoration: underline;\">will being</span> overlooked by me. (गलत Verb)<br>(d) This creativity will not never be overlooked by me. (&lsquo;not&rsquo; का गलत प्रयोग)</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "7",
                    question_en: "<p>9. Select the option that correctly expresses the following sentence in passive voice.<br>Who invited you to this party?</p>",
                    question_hi: "<p>9. Select the option that correctly expresses the following sentence in passive voice.<br>Who invited you to this party?</p>",
                    options_en: ["<p>By whom you are invited to this party?</p>", "<p>By whom are you invited to this party?</p>", 
                                "<p>By whom you were invited to this party?</p>", "<p>By whom were you invited to this party?</p>"],
                    options_hi: ["<p>By whom you are invited to this party?</p>", "<p>By whom are you invited to this party?</p>",
                                "<p>By whom you were invited to this party?</p>", "<p>By whom were you invited to this party?</p>"],
                    solution_en: "<p>9.(d) By whom were you invited to this party? (Correct)<br>(a) By whom you <span style=\"text-decoration: underline;\">are invited</span> to this party? (Incorrect Tense)<br>(b) By whom <span style=\"text-decoration: underline;\">are you invited</span> to this party? (Incorrect Tense)<br>(c) By whom you were invited to this party? (Incorrect Sentence Structure)</p>",
                    solution_hi: "<p>9.(d) By whom were you invited to this party? (Correct)<br>(a) By whom you <span style=\"text-decoration: underline;\">are invited</span> to this party? (गलत Tense)<br>(b) By whom <span style=\"text-decoration: underline;\">are you invited</span> to this party? (गलत Tense)<br>(c) By whom you were invited to this party? (गलत Sentence Structure)</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "7",
                    question_en: "<p>10. Select the option that expresses the given sentence in passive voice.<br>The team will announce the winner tomorrow.</p>",
                    question_hi: "<p>10. Select the option that expresses the given sentence in passive voice.<br>The team will announce the winner tomorrow.</p>",
                    options_en: ["<p>The team will announce tomorrow the winner.</p>", "<p>The winner will announce tomorrow by the team.</p>", 
                                "<p>The winner will be announced by the team tomorrow.</p>", "<p>Tomorrow will be announced the winner by the team.</p>"],
                    options_hi: ["<p>The team will announce tomorrow the winner.</p>", "<p>The winner will announce tomorrow by the team.</p>",
                                "<p>The winner will be announced by the team tomorrow.</p>", "<p>Tomorrow will be announced the winner by the team.</p>"],
                    solution_en: "<p>10.(c) The winner will be announced by the team tomorrow. (Correct)<br>(a) The team will announce tomorrow the winner. (Incorrect Sentence Structure)<br>(b) The winner <span style=\"text-decoration: underline;\">will announce</span> tomorrow by the team. (Incorrect Verb)<br>(d) Tomorrow will be announced the winner by the team. (Incorrect Sentence Structure)</p>",
                    solution_hi: "<p>10.(c) The winner will be announced by the team tomorrow. (Correct)<br>(a) The team will announce tomorrow the winner. (गलत Sentence Structure)<br>(b) The winner <span style=\"text-decoration: underline;\">will announce</span> tomorrow by the team. (गलत Verb)<br>(d) Tomorrow will be announced the winner by the team. (गलत Sentence Structure)</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "7",
                    question_en: "<p>11. Select the option that expresses the given sentence in passive voice.<br>Don&rsquo;t let Dylan fool you!</p>",
                    question_hi: "<p>11. Select the option that expresses the given sentence in passive voice.<br>Don&rsquo;t let Dylan fool you!</p>",
                    options_en: ["<p>Do not be fooled by Dylan.</p>", "<p>Do not ever be fooled by Dylan.</p>", 
                                "<p>Do not be fool by Dylan.</p>", "<p>Do not have been fooled by Dylan.</p>"],
                    options_hi: ["<p>Do not be fooled by Dylan.</p>", "<p>Do not ever be fooled by Dylan.</p>",
                                "<p>Do not be fool by Dylan.</p>", "<p>Do not have been fooled by Dylan.</p>"],
                    solution_en: "<p>11.(a) Do not be fooled by Dylan. (Correct)<br>(b) Do not ever be fooled by Dylan. (Incorrect use of &lsquo;ever&rsquo;)<br>(c) Do not be <span style=\"text-decoration: underline;\">fool </span>by Dylan. (Incorrect Form of the Verb)<br>(d) Do not <span style=\"text-decoration: underline;\">have been</span> fooled by Dylan. (Incorrect Verb)</p>",
                    solution_hi: "<p>11.(a) Do not be fooled by Dylan. (Correct)<br>(b) Do not ever be fooled by Dylan. ( &lsquo;ever&rsquo; का गलत प्रयोग)<br>(c) Do not be <span style=\"text-decoration: underline;\">fool </span>by Dylan. (Verb की गलत Form)<br>(d) Do not <span style=\"text-decoration: underline;\">have been</span> fooled by Dylan. (गलत Verb)</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "7",
                    question_en: "<p>12. Select the option that expresses the given sentence in passive voice. <br>The children have made the box.</p>",
                    question_hi: "<p>12. Select the option that expresses the given sentence in passive voice. <br>The children have made the box.</p>",
                    options_en: ["<p>The box is being made by the children.</p>", "<p>Children had made the box.</p>", 
                                "<p>The box had been made by the children.</p>", "<p>The box has been made by the children.</p>"],
                    options_hi: ["<p>The box is being made by the children.</p>", "<p>Children had made the box.</p>",
                                "<p>The box had been made by the children.</p>", "<p>The box has been made by the children.</p>"],
                    solution_en: "<p>12.(d) The box has been made by the children. (Correct)<br>(a) The box <span style=\"text-decoration: underline;\">is being</span> made by the children. (Incorrect Verb)<br>(b) Children had made the box. (Incorrect Sentence Structure)<br>(c) The box <span style=\"text-decoration: underline;\">had been made</span> by the children. (Incorrect Tense)</p>",
                    solution_hi: "<p>12.(d) The box has been made by the children. (Correct)<br>(a) The box <span style=\"text-decoration: underline;\">is being</span> made by the children. (गलत Verb)<br>(b) Children had made the box. (गलत Sentence Structure)<br>(c) The box <span style=\"text-decoration: underline;\">had been made</span> by the children. (गलत Tense)</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "7",
                    question_en: "13. Select the option that expresses the given sentence in passive voice. <br />They are organising a concert for charity.",
                    question_hi: "13. Select the option that expresses the given sentence in passive voice. <br />They are organising a concert for charity.",
                    options_en: [" A concert is being organised by them for charity. ", " They are organising a charity concert.  ", 
                                " They are being organised a concert for charity. ", " A concert for charity is organising by them."],
                    options_hi: [" A concert is being organised by them for charity. ", " They are organising a charity concert.  ",
                                " They are being organised a concert for charity. ", " A concert for charity is organising by them."],
                    solution_en: "13.(a) A concert is being organised by them for charity. (Correct)<br />(b) They are organising a charity concert. (Incorrect Sentence Structure)<br />(c) They are being organised a concert for charity. (Incorrect Sentence Structure)<br />(d) A concert for charity is organising by them. (Incorrect Sentence Structure)",
                    solution_hi: "13.(a) A concert is being organised by them for charity. (Correct)<br />(b) They are organising a charity concert. (गलत  Sentence Structure)<br />(c) They are being organised a concert for charity. (गलत Sentence Structure)<br />(d) A concert for charity is organising by them. (गलत Sentence Structure)",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "7",
                    question_en: "<p>14. Select the option that expresses the given sentence in passive voice. <br>Martha made pancakes yesterday.</p>",
                    question_hi: "<p>14. Select the option that expresses the given sentence in passive voice. <br>Martha made pancakes yesterday.</p>",
                    options_en: ["<p>Pancakes had been made by Martha yesterday.</p>", "<p>Pancakes were being made by Martha yesterday.</p>", 
                                "<p>Pancakes were made by Martha yesterday.</p>", "<p>Pancakes was made by Martha yesterday.</p>"],
                    options_hi: ["<p>Pancakes had been made by Martha yesterday.</p>", "<p>Pancakes were being made by Martha yesterday.</p>",
                                "<p>Pancakes were made by Martha yesterday.</p>", "<p>Pancakes was made by Martha yesterday.</p>"],
                    solution_en: "<p>14.(c) Pancakes were made by Martha yesterday. (Correct)<br>(a) Pancakes <span style=\"text-decoration: underline;\">had been made</span> by Martha yesterday. (Incorrect Tense)<br>(b) Pancakes <span style=\"text-decoration: underline;\">were being made</span> by Martha yesterday. (Incorrect Tense)<br>(d) Pancakes <span style=\"text-decoration: underline;\">was</span> made by Martha yesterday. (Incorrect Helping Verb)</p>",
                    solution_hi: "<p>14.(c) Pancakes were made by Martha yesterday. (Correct)<br>(a) Pancakes <span style=\"text-decoration: underline;\">had been made</span> by Martha yesterday. (गलत Tense)<br>(b) Pancakes <span style=\"text-decoration: underline;\">were being made</span> by Martha yesterday. (गलत Tense)<br>(d) Pancakes <span style=\"text-decoration: underline;\">was </span>made by Martha yesterday. (गलत Helping Verb)</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "misc",
                    question_en: "<p>15. Select the option that expresses the given sentence in active voice. <br>I will be gifted a fancy doll by them the next day.</p>",
                    question_hi: "<p>15. Select the option that expresses the given sentence in active voice. <br>I will be gifted a fancy doll by them the next day.</p>",
                    options_en: ["<p>They gifted me a fancy doll yesterday.</p>", "<p>They are gifting me a fancy doll the next day.</p>", 
                                "<p>They gift me a fancy doll the previous day.</p>", "<p>They will gift me a fancy doll tomorrow.</p>"],
                    options_hi: ["<p>They gifted me a fancy doll yesterday.</p>", "<p>They are gifting me a fancy doll the next day.</p>",
                                "<p>They gift me a fancy doll the previous day.</p>", "<p>They will gift me a fancy doll tomorrow.</p>"],
                    solution_en: "<p>15.(d) They will gift me a fancy doll tomorrow. (Correct)<br>(a) They <span style=\"text-decoration: underline;\">gifted </span>me a fancy doll <span style=\"text-decoration: underline;\">yesterday</span>. (Incorrect Tense &amp; Adverb)<br>(b) They <span style=\"text-decoration: underline;\">are gifting</span> me a fancy doll <span style=\"text-decoration: underline;\">the next day</span>. (Incorrect Tense &amp; Adverb)<br>(c) They <span style=\"text-decoration: underline;\">gift </span>me a fancy doll <span style=\"text-decoration: underline;\">the previous day</span>. (Incorrect Tense &amp; Adverb)</p>",
                    solution_hi: "<p>15.(d) They will gift me a fancy doll tomorrow. (Correct)<br>(a) They <span style=\"text-decoration: underline;\">gifted </span>me a fancy doll <span style=\"text-decoration: underline;\">yesterday</span>. (गलत Tense एवं Adverb)<br>(b) They <span style=\"text-decoration: underline;\">are gifting</span> me a fancy doll <span style=\"text-decoration: underline;\">the next day</span>. (गलत Tense एवं Adverb)<br>(c) They <span style=\"text-decoration: underline;\">gift </span>me a fancy doll <span style=\"text-decoration: underline;\">the previous day</span>. (गलत Tense एवं Adverb)</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>