<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">10:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 15</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">15</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 10 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["6"] = {
                name: "Reasoning",
                start: 0,
                end: 13
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="6">Reasoning</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 14,
                end: 14
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "6",
                    question_en: "<p>1. \'A + B\' means \'A is the brother of B\'.<br>\'A - B\' means \'A is the wife of B\'.<br>\'A <math display=\"inline\"><mo>&#215;</mo></math> B\' means \'A is the father of B\'.<br>\'A &divide; B\' means \'A is the sister of B\'.<br>In \'T <math display=\"inline\"><mo>&#215;</mo></math> X &divide; Y + Z\', how is T related to Z using the same meaning of the mathematical&nbsp;operators as given above?</p>",
                    question_hi: "<p>1. &lsquo;A + B\' का अर्थ है \'A, B का भाई है।<br>\'A - B\' का अर्थ है \'A, B की पत्नी है\'।<br>\'A <math display=\"inline\"><mo>&#215;</mo></math> B\' का अर्थ है \'A, B के पिता है।<br>\'A <math display=\"inline\"><mo>&#247;</mo></math> B\' का अर्थ है \'A, B की बहन है।<br>ऊपर दिए गए गणितीय संकारकों के समान अर्थ का उपयोग करते हुए ज्ञात करें कि \'T <math display=\"inline\"><mo>&#215;</mo></math> X &divide; Y + Z\' में T का Z से क्या संबंध है?</p>",
                    options_en: ["<p>Father</p>", "<p>Father-in-law</p>", 
                                "<p>Brother</p>", "<p>Daughter</p>"],
                    options_hi: ["<p>पिता</p>", "<p>ससुर</p>",
                                "<p>भाई</p>", "<p>पुत्री</p>"],
                    solution_en: "<p>1.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736848945431.png\" alt=\"rId4\" width=\"204\" height=\"129\"><br>T is the father of Z.</p>",
                    solution_hi: "<p>1.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736848945431.png\" alt=\"rId4\" width=\"204\" height=\"129\"><br>T, Z का पिता है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "6",
                    question_en: "<p>2. &lsquo;Q + R&rsquo; means &lsquo;Q is the father of R&rsquo;<br>&lsquo;Q &ndash; R&rsquo; means &lsquo;Q is the wife of R&rsquo;<br>&lsquo;Q &times; R&rsquo; means &lsquo;Q is the brother of R&rsquo;<br>&lsquo;Q &divide; R&rsquo; means &lsquo;Q is the daughter of R&rsquo;<br>What does &lsquo; M &ndash; N + O&rsquo; mean?</p>",
                    question_hi: "<p>2. Q + R\' का अर्थ है \'Q, R का पिता है\' <br>\'Q - R\' का अर्थ है \'Q, R की पत्नी है\' <br>\'Q &times; R\' का अर्थ है \'Q, R का भाई है\' <br>\'Q &divide; R\' का अर्थ है \'Q, R की बेटी है\'। <br>\'M - N + O\' का अर्थ है?</p>",
                    options_en: ["<p>M is the mother of O</p>", "<p>M is the daughter of O</p>", 
                                "<p>M is the brother of O</p>", "<p>M is the father of O</p>"],
                    options_hi: ["<p>M, O की माता है</p>", "<p>M, O की बेटी है</p>",
                                "<p>M, O का भाई है</p>", "<p>M, O का पिता है</p>"],
                    solution_en: "<p>2.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736848945546.png\" alt=\"rId5\" width=\"145\" height=\"134\"><br>M is the mother of O.</p>",
                    solution_hi: "<p>2.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736848945546.png\" alt=\"rId5\" width=\"145\" height=\"134\"><br>M, O की माँ है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "6",
                    question_en: "<p>3. In a certain code language,<br>A + B means \'A is the son of B\'<br>A - B means \'A is the brother of B\'<br>A <math display=\"inline\"><mo>&#215;</mo></math> B means \'A is the wife of B\'<br>A <math display=\"inline\"><mo>&#247;</mo></math> B means \'A is the father of B\'<br>Based on the above, how is P related to T if \'P <math display=\"inline\"><mo>&#215;</mo></math> Q - R + S &divide;T &rsquo;?</p>",
                    question_hi: "<p>3. एक निश्चित कूट भाषा में<br>A + B का अर्थ A, B का बेटा है<br>A &ndash; B का अर्थ A, B का भाई है<br>A &times; B का अर्थ A, B की पत्नी है<br>A &divide; B का अर्थ A, B का पिता है<br>उपरोक्त के आधार पर, यदि P &times; Q - R + S &divide; T है, तो P का T से क्या संबंध है?</p>",
                    options_en: ["<p>Brother\'s wife\'s mother</p>", "<p>Mother</p>", 
                                "<p>Brother\'s wife</p>", "<p>Sister</p>"],
                    options_hi: ["<p>भाई की पत्नी की माँ</p>", "<p>माँ</p>",
                                "<p>भाई की पत्नी</p>", "<p>बहन</p>"],
                    solution_en: "<p>3.(c) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736848945659.png\" alt=\"rId6\" width=\"214\" height=\"105\"><br>P is the wife of T&rsquo;s brother.</p>",
                    solution_hi: "<p>3.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736848945659.png\" alt=\"rId6\" width=\"214\" height=\"105\"><br>P, T के भाई की पत्नी है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "6",
                    question_en: "<p>4. In a certain code language,<br>&lsquo;M &amp; N&rsquo; means &lsquo;M is the father of N&rsquo;,<br>&lsquo;M@N&rsquo; means &lsquo;M is the daughter of N&rsquo;,<br>&lsquo;M#N&rsquo; means &lsquo;M is the brother of N&rsquo;,<br>&lsquo;M % N&rsquo; means &lsquo;M is the mother of N&rsquo;.<br>Based on this, how is A related to E, if &lsquo;A &amp; B # C @ D % E&rsquo; ?</p>",
                    question_hi: "<p>4. एक निश्चित कूट भाषा में,<br>\'M &amp; N\' का अर्थ है \'M, N का पिता है\',<br>\'M@ N\' का अर्थ है \'M, N की बेटी है\',<br>\'M# N\' का अर्थ है \'M, N का भाई है\',<br>\'M % N\' का अर्थ है \'M, N की माता है\'।<br>इसके आधार पर, यदि \'A &amp; B # C @ D % E\' है, तो A, E से किस प्रकार संबंधित है?</p>",
                    options_en: ["<p>Father&rsquo;s brother</p>", "<p>Son</p>", 
                                "<p>Father</p>", "<p>Brother</p>"],
                    options_hi: ["<p>पिता के भाई</p>", "<p>बेटा</p>",
                                "<p>पिता</p>", "<p>भाई</p>"],
                    solution_en: "<p>4.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736848945769.png\" alt=\"rId7\" width=\"171\" height=\"102\"><br>A is the father of E.</p>",
                    solution_hi: "<p>4.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736848945769.png\" alt=\"rId7\" width=\"171\" height=\"102\"><br>A, E का पिता है.</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "6",
                    question_en: "<p>5. &lsquo;A &times; B&rsquo; means &lsquo;A is B&rsquo;s brother&rsquo;.<br>&lsquo;A &divide; B&rsquo; means &lsquo;A is B&rsquo;s sister&rsquo;.<br>&lsquo;A + B&rsquo; means &lsquo;A is B&rsquo;s father&rsquo;.<br>&lsquo;A &ndash; B&rsquo; means &lsquo;A is B&rsquo;s mother&rsquo;.<br>Using the same meaning of the mathematical operators as given above, which of the following means &lsquo;X is Z&rsquo;s mother&rsquo;s father&rsquo;?</p>",
                    question_hi: "<p>5. A &times; B\' का अर्थ है \'A, B का भाई है\'।<br>\'A &divide; B\' का अर्थ है \'A, B की बहन है\'।<br>\'A + B\' का अर्थ है \'A, B के पिता है\'।<br>\'A - B\' का अर्थ है \'A, B की माँ है\'।<br>ऊपर दिए गए गणितीय संकारकों के समान अर्थ का उपयोग करते हुए ज्ञात करें कि निम्नलिखित में से किसका अर्थ है, \'X, Z की माँ के पिता है\'?</p>",
                    options_en: ["<p>X + Y &times; Z</p>", "<p>X + Y &ndash; Z</p>", 
                                "<p>X &times; Y + Z</p>", "<p>X &ndash; Y + Z</p>"],
                    options_hi: ["<p>X + Y &times; Z</p>", "<p>X + Y &ndash; Z</p>",
                                "<p>X &times; Y + Z</p>", "<p>X &ndash; Y + Z</p>"],
                    solution_en: "<p>5.(b)<br><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdmoYASn0rlQFdT5BwNnCVYe5jzwrycNumbTZtJUgJhFo3SFDLrYdomRzD09XW92MufI25uX59NSLwd-lCj3kp1sW49xZBDzDeZqcj5pReLvEktPfBCXwIYAHTjRyvNGAgA8Jalzg?key=hTxsrjWK-dOxgqlnoz5PI_Q8\" width=\"61\" height=\"209\"><br>X is Z&rsquo;s mothers&rsquo; father.</p>",
                    solution_hi: "<p>5.(b)<br><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdmoYASn0rlQFdT5BwNnCVYe5jzwrycNumbTZtJUgJhFo3SFDLrYdomRzD09XW92MufI25uX59NSLwd-lCj3kp1sW49xZBDzDeZqcj5pReLvEktPfBCXwIYAHTjRyvNGAgA8Jalzg?key=hTxsrjWK-dOxgqlnoz5PI_Q8\" width=\"61\" height=\"209\"><br>X, Z की माँ का पिता है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "6",
                    question_en: "<p>6. In a certain code language,<br>&lsquo;M &amp; N&rsquo; means &lsquo;M is the wife of N&rsquo;,<br>&lsquo;M @ N&rsquo; means &lsquo;M is the brother of N&rsquo;,<br>&lsquo;M $ N&rsquo; means &lsquo;M is the father of N&rsquo;,<br>&lsquo;M # N&rsquo; means &lsquo;M is the sister of N&rsquo;.<br>Based on this, how is D related to H, if &lsquo;D &amp; E $ F # G @ H&rsquo;?</p>",
                    question_hi: "<p>6. एक निश्चित कूट भाषा में,<br>\'M &amp; N\' का अर्थ है \'M, N की पत्नी है\',<br>\'M @ N\' का अर्थ है \'M, N का भाई है\',<br>\'M $ N&rsquo; का अर्थ है \'M, N के पिता है\',<br>\'M # N\' का अर्थ है \'M, N की बहन है\'।<br>इसके आधार पर, यदि \'D &amp; E $ F # G @ H\' है, तो D, H से किस प्रकार संबंधित है?</p>",
                    options_en: ["<p>Sister</p>", "<p>Father&rsquo;s mother</p>", 
                                "<p>Father&rsquo;s sister</p>", "<p>Mother</p>"],
                    options_hi: ["<p>बहन</p>", "<p>पिता की माता</p>",
                                "<p>पिता की बहन</p>", "<p>माता</p>"],
                    solution_en: "<p>6.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736848946034.png\" alt=\"rId9\" width=\"221\" height=\"117\"><br>D is the mother of H.</p>",
                    solution_hi: "<p>6.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736848946034.png\" alt=\"rId9\" width=\"221\" height=\"117\"><br>D, H की माँ है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "6",
                    question_en: "<p>7. In a certain code language,<br>&lsquo;A + B&rsquo; means &lsquo;A is the mother of B&rsquo;, <br>&lsquo;A &ndash; B&rsquo; means &lsquo;A is the brother of B&rsquo;, <br>&lsquo;A &times; B&rsquo; means &lsquo;A is the father of B&rsquo;, <br>&lsquo;A &divide; B&rsquo; means &lsquo;A is the daughter of B&rsquo; <br>Based on the above, how is E related to A if &lsquo;A &times; B &ndash; C + D &divide; E&rsquo;?</p>",
                    question_hi: "<p>7. एक निश्चित कूट भाषा में, <br>&lsquo;A + B&rsquo; का अर्थ है &lsquo;A, B की माँ है&rsquo;, <br>&lsquo;A &ndash; B&rsquo; का अर्थ है &lsquo;A, B का भाई है&rsquo;,<br>&lsquo;A &times; B&rsquo; का अर्थ है &lsquo;A, B के पिता है&rsquo;, <br>&lsquo;A &divide; B&rsquo; का अर्थ है &lsquo;A, B की बेटी है&rsquo;. <br>उपरोक्त के आधार पर, यदि&lsquo;A &times; B &ndash; C + D &divide; E&rsquo; है, तो E का A से क्या संबंध है?</p>",
                    options_en: ["<p>Brother</p>", "<p>Son</p>", 
                                "<p>Daughter&rsquo;s husband</p>", "<p>Wife&rsquo;s brother</p>"],
                    options_hi: ["<p>भाई</p>", "<p>बेटा</p>",
                                "<p>बेटी का पति</p>", "<p>पत्नी का भाई</p>"],
                    solution_en: "<p>7.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736848946144.png\" alt=\"rId10\" width=\"135\" height=\"129\"><br>E is the husband of A&rsquo;s daughter.</p>",
                    solution_hi: "<p>7.(c) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736848946144.png\" alt=\"rId10\" width=\"135\" height=\"129\"><br>E, A की बेटी का पति है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "6",
                    question_en: "<p>8. &lsquo;A + B&rsquo; means &lsquo;A is the brother of B&rsquo;. <br>&lsquo;A &ndash; B&rsquo; means &lsquo;A is the sister of B&rsquo;. <br>&lsquo;A &times; B&rsquo; means &lsquo;A is the father of B&rsquo;. <br>&lsquo;A &divide; B&rsquo; means &lsquo;A is the mother of B&rsquo;. <br>Using the same meaning of the mathematical operators as given above, which of the following shows&lsquo;P is the father&rsquo;s father of S&rsquo;?</p>",
                    question_hi: "<p>8. &lsquo;A + B\' का अर्थ है \'A, B का भाई है\'।<br>\'A - B\' का अर्थ है \'A, B की बहन है\'।<br>\'A &times; B\' का अर्थ है \'A, B के पिता है\'।<br>\'A &divide; B\' का अर्थ है \'A, B की माँ है\'।<br>ऊपर दिए गए गणितीय संकारकों के समान अर्थ का उपयोग करते हुए ज्ञात करें कि निम्नलिखित में से कौन-सा विकल्प यह प्रदर्शित करता है कि \'P, S के पिता के पिता है\'?</p>",
                    options_en: ["<p>P + Q &ndash; R &divide; S</p>", "<p>P &ndash; Q + R &divide; S</p>", 
                                "<p>P &times; Q &times; R + S</p>", "<p>P + Q &divide; R &times; S</p>"],
                    options_hi: ["<p>P + Q &ndash; R &divide; S</p>", "<p>P &ndash; Q + R &divide; S</p>",
                                "<p>P &times; Q &times; R + S</p>", "<p>P + Q &divide; R &times; S</p>"],
                    solution_en: "<p>8.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736848946372.png\" alt=\"rId11\" width=\"113\" height=\"161\"><br>P is the father of S&rsquo;s father.</p>",
                    solution_hi: "<p>8.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736848946372.png\" alt=\"rId11\" width=\"113\" height=\"161\"><br>P, S के पिता का पिता है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "6",
                    question_en: "<p>9. &lsquo;Z + Y&rsquo; means &lsquo;Z is the father of Y&rsquo; <br>&lsquo;Z &ndash; Y&rsquo; means &lsquo;Z is the mother of Y&rsquo; <br>&lsquo;Z &times; Y&rsquo; means &lsquo;Z is the brother of Y&rsquo; <br>&lsquo;Z &divide; Y&rsquo; means &lsquo;Z is the sister of Y&rsquo;. <br>What does &lsquo;A &times; B &ndash; C&rsquo; mean?</p>",
                    question_hi: "<p>9. Z + Y\' का अर्थ है \'Z, Y का पिता है\'<br>\'Z - Y\' का अर्थ है \'Z, Y की माँ है\' <br>\'Z &times; Y\' का अर्थ है \'Z, Y का भाई है\' <br>\'Z &divide; Y\' का अर्थ है \'Z, Y की बहन है\'। <br>&lsquo;A &times; B &ndash; C&rsquo; का क्या अर्थ है?</p>",
                    options_en: ["<p>A is the mother&rsquo;s brother of C</p>", "<p>A is the father&rsquo;s brother of C</p>", 
                                "<p>A is the father&rsquo;s father of C</p>", "<p>A is the father of C</p>"],
                    options_hi: ["<p>A, C की माँ का भाई है</p>", "<p>A, C के पिता का भाई है</p>",
                                "<p>A, C के पिता का पिता है</p>", "<p>A, C का पिता है</p>"],
                    solution_en: "<p>9.(a) <br><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfpuZmNlSGcUNMwXMuarsxDOM6AypoeVT6NllM4h84jbjMprdzL3MkKdX_zvQMu94ddFtQPwY-amicSAsuJ0_GbRJRjMlSr1bDCObrcgk506tuTq1rTZovah4JNCX2PijTmSfLnCA?key=hTxsrjWK-dOxgqlnoz5PI_Q8\" width=\"130\" height=\"114\"><br>A is mother&rsquo;s brother of C.</p>",
                    solution_hi: "<p>9.(a) <br><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfpuZmNlSGcUNMwXMuarsxDOM6AypoeVT6NllM4h84jbjMprdzL3MkKdX_zvQMu94ddFtQPwY-amicSAsuJ0_GbRJRjMlSr1bDCObrcgk506tuTq1rTZovah4JNCX2PijTmSfLnCA?key=hTxsrjWK-dOxgqlnoz5PI_Q8\" width=\"130\" height=\"114\"><br>A, C की माँ का भाई है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "6",
                    question_en: "<p>10. In a certain code language,<br>\'A @ B\' means \'A is the husband of B\',<br>\'A # B\' means \'A is the mother of B\',<br>\'A &amp; B\' means \'A is the sister of B\'.<br>Based on the above, how is T related to P if \'P @ Q # R &amp; S @ T\'?</p>",
                    question_hi: "<p>10. एक निश्चित कूट भाषा में<br>\'A @B\' का अर्थ है\' A, B का पति है\',<br>\'A # B\' का अर्थ है \'A, B की माँ है,<br>\'A &amp; B\' का अर्थ है \'A, B की बहन है\'।<br>उपरोक्त के आधार पर, यदि \'P @ Q # R &amp; S @T\' है, तो T, P से किस प्रकार संबंधित है?</p>",
                    options_en: ["<p>Son\'s wife</p>", "<p>Daughter</p>", 
                                "<p>Wife\'s sister</p>", "<p>Sister</p>"],
                    options_hi: ["<p>बेटे की पत्नी</p>", "<p>बेटी</p>",
                                "<p>पत्नी की बहन</p>", "<p>बहन</p>"],
                    solution_en: "<p>10.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736848946660.png\" alt=\"rId13\" width=\"216\" height=\"116\"><br>T is the wife of P&rsquo;s son.</p>",
                    solution_hi: "<p>10.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736848946660.png\" alt=\"rId13\" width=\"216\" height=\"116\"><br>T, P के बेटे की पत्नी है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "6",
                    question_en: "<p>11. \'D + E\' means \'D is the father of E\'<br>\'D - E\' means \'D is the sister of E\'<br>\'D ✕ E\' means \'D is the mother of E\'<br>\'D &divide; E\' means \'D is the brother of E\'.<br>Which of the following represents \'K is the brother\'s daughter of L\'?</p>",
                    question_hi: "<p>11. D+ E\' का अर्थ है \'D, E का पिता है\'<br>\'D - E\' का अर्थ है \'D, E की बहन है\'<br>\'D ✕ E\' का अर्थ है \'D, E की मां है\'<br>\'D &divide; E\' का अर्थ है \'D, E का भाई है।<br>निम्नलिख त में से कौन-सा विकल्प यह दर्शाता है कि \'K, L की भतीजी है?</p>",
                    options_en: ["<p>U + F + K &divide; P</p>", "<p>L &divide; Z ✕ X - K</p>", 
                                "<p>K - L ✕ X &divide; U</p>", "<p>L - F + K - P</p>"],
                    options_hi: ["<p>U + F + K &divide; P</p>", "<p>L &divide; Z ✕ X - K</p>",
                                "<p>K - L ✕ X &divide; U</p>", "<p>L - F + K - P</p>"],
                    solution_en: "<p>11.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736848946761.png\" alt=\"rId14\" width=\"181\" height=\"110\"><br>K is the brother&rsquo;s daughter of L.</p>",
                    solution_hi: "<p>11.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736848946761.png\" alt=\"rId14\" width=\"181\" height=\"110\"><br>K, L के भाई की बेटी है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "6",
                    question_en: "<p>12. &lsquo;A + B&rsquo; means &lsquo;A is B&rsquo;s brother&rsquo;.<br>&lsquo;A &ndash; B&rsquo; means &lsquo;A is B&rsquo;s wife&rsquo;.<br>&lsquo;A &times; B&rsquo; means &lsquo;A is B&rsquo;s father&rsquo;.<br>&lsquo;A &divide; B&rsquo; means &lsquo;A is B&rsquo;s daughter&rsquo;.<br>Using operations with the same meaning as given above, which of the following<br>options shows &lsquo;M is the mother of O&rsquo;?</p>",
                    question_hi: "<p>12. A + B\' का अर्थ है \'A, B का भाई है\'।<br>\'A - B\' का अर्थ है \'A, B की पत्नी है\'।<br>\'A &times; B\' का अर्थ है \'A, B के पिता है\'।<br>\'A &divide; B\' का अर्थ है \'A, B की पुत्री है\'।<br>ऊपर दी गई संक्रियाओं के समान अर्थ का उपयोग करते हुए ज्ञात करें कि निम्नलिखित में से कौन-सा<br>विकल्प \'M, O की माँ है\' को दर्शाता है?</p>",
                    options_en: ["<p>M &ndash; N &times; O</p>", "<p>M &ndash; N + O</p>", 
                                "<p>M &divide; N + O</p>", "<p>M &divide; N &times; O</p>"],
                    options_hi: ["<p>M &ndash; N &times; O</p>", "<p>M &ndash; N + O</p>",
                                "<p>M &divide; N + O</p>", "<p>M &divide; N &times; O</p>"],
                    solution_en: "<p>12.(a)<br>After checking option one by one the correction option is (a)<br><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdeWqHzd03I5ISLmFp6lemg6Fbda8dVlaYjX53oT3quxFqTQdQzdZMcSFqTiei3bwbnM1HlSaG5EeX2z74ggZgEC2884_0y4I_d0BlgjMPey6_AyBLskd11BnIyuwj7weOR-CU4IQ?key=hTxsrjWK-dOxgqlnoz5PI_Q8\" width=\"165\" height=\"143\"><br>M is the mother of O.</p>",
                    solution_hi: "<p>12.(a) <br>एक-एक करके विकल्प की जांच करने के बाद सही विकल्प (a) है <br><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdeWqHzd03I5ISLmFp6lemg6Fbda8dVlaYjX53oT3quxFqTQdQzdZMcSFqTiei3bwbnM1HlSaG5EeX2z74ggZgEC2884_0y4I_d0BlgjMPey6_AyBLskd11BnIyuwj7weOR-CU4IQ?key=hTxsrjWK-dOxgqlnoz5PI_Q8\" width=\"165\" height=\"143\"><br>M, O की माँ है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "6",
                    question_en: "<p>13. In a certain language,<br>A &amp; B means A is the wife of B,<br>A &divide; B means A is the mother of B,<br>A &times; B means A is the son of B,<br>A + B means A is the sister of B and <br>A@B means A is the father of B. <br>If A @ B &amp; C @ D + K &divide; F, how is C related to K?</p>",
                    question_hi: "<p>13. एक निश्चित भाषा में, <br>A &amp; B का अर्थ A, B की पत्नी है, <br>A &divide; B का अर्थ A, B की माँ है, <br>A &times; B का अर्थ A, B का बेटा है, <br>A + B का अर्थ A, B की बहन है और <br>A @ B का अर्थ A, B का पिता है।<br>यदि A @ B &amp; C @ D + K &divide; F है, तो C का K से क्या संबंध है?</p>",
                    options_en: ["<p>Father</p>", "<p>Sister</p>", 
                                "<p>Mother</p>", "<p>Brother</p>"],
                    options_hi: ["<p>पिता</p>", "<p>बहन</p>",
                                "<p>माँ</p>", "<p>भाई</p>"],
                    solution_en: "<p>13.(a)<br><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXebp2NzDWA0ATciY9qV77Nq3r26ui86Ah5WB8SpkCEUkcws7jnzlZb8H1DnOdyQbR04FT4DE6-gqyq6xyN_6RPDkawZ7dsDue7Wa4ApYyk2Wl_n2bwEaAJGzAumAwviLnrAXdr79g?key=hTxsrjWK-dOxgqlnoz5PI_Q8\" width=\"160\" height=\"183\"><br>C is the father of K.</p>",
                    solution_hi: "<p>13.(a)<br><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXebp2NzDWA0ATciY9qV77Nq3r26ui86Ah5WB8SpkCEUkcws7jnzlZb8H1DnOdyQbR04FT4DE6-gqyq6xyN_6RPDkawZ7dsDue7Wa4ApYyk2Wl_n2bwEaAJGzAumAwviLnrAXdr79g?key=hTxsrjWK-dOxgqlnoz5PI_Q8\" width=\"160\" height=\"183\"><br>C, K का पिता है.</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "6",
                    question_en: "<p>14. &lsquo;P + F&rsquo; means &lsquo;P is the son of F&rsquo;<br>&lsquo;P &ndash; F&rsquo; means &lsquo;P is the wife of F&rsquo;<br>&lsquo;P &times; F&rsquo; means &lsquo;P is the brother of F&rsquo;<br>&lsquo;P &divide; F&rsquo; means &lsquo;P is the mother of F&rsquo;<br>&lsquo;P = F&rsquo; means &lsquo;P is the sister of F&rsquo;.<br>What does &lsquo;K = R &divide; M&rsquo; mean?</p>",
                    question_hi: "<p>14. P + F\' का अर्थ है \'P, F का पुत्र है\'<br>\'P - F\' का अर्थ है \'P, F की पत्नी है\'<br>\'P &times; F\' का अर्थ है \'P, F का भाई है\'<br>\'P &divide; F\' का अर्थ है \'P, F की माता है\'<br>\'P = F\' का अर्थ है \'P, F की बहन है\'।<br>&lsquo;K = R &divide; M&rsquo; का क्या अर्थ है?</p>",
                    options_en: ["<p>K is the daughter of M</p>", "<p>K is the aunt (mother&rsquo;s sister) of M</p>", 
                                "<p>K is the niece of M</p>", "<p>K is the sister of M</p>"],
                    options_hi: ["<p>K, M की पुत्री है</p>", "<p>K, M की मौसी है</p>",
                                "<p>K, M की भतीजी है</p>", "<p>K, M की बहन है</p>"],
                    solution_en: "<p>14.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736848947278.png\" alt=\"rId17\" width=\"171\" height=\"152\"><br>K is the aunt (mother&rsquo;s sister) of M.</p>",
                    solution_hi: "<p>14.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736848947278.png\" alt=\"rId17\" width=\"171\" height=\"152\"><br>K, M की मौसी है</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "misc",
                    question_en: "<p>15. In a certain code language,<br>&lsquo;M &amp; N&rsquo; means &lsquo;M is the wife of N&rsquo;,<br>&lsquo;M @ N&rsquo; means &lsquo;M is the brother of N&rsquo;,<br>&lsquo;M $ N&rsquo; means &lsquo;M is the father of N&rsquo;,<br>&lsquo;M # N&rsquo; means &lsquo;M is the son of N&rsquo;.<br>Based on the above, how is T related to G if &lsquo;T @ S # M &amp; N $ G&rsquo;?</p>",
                    question_hi: "<p>15. एक निश्चित कूट भाषा में,<br>M &amp; N का अर्थ है, M N की पत्नी है\',<br>\'M @ N\' का अर्थ है, \'M, N का भाई है\',<br>M $ N का अर्थ है, M, N के पिता है\',<br>M # N का अर्थ है, M, N का पुत्र है\'।<br>उपरोक्त के आधार पर, यदि \'T @ S # M &amp; N $ G\' है, तो T का G से क्या संबंध है?</p>",
                    options_en: ["<p>Father</p>", "<p>Father&rsquo;s brother</p>", 
                                "<p>Brother</p>", "<p>Son</p>"],
                    options_hi: ["<p>पिता</p>", "<p>पिता के भाई</p>",
                                "<p>भाई</p>", "<p>पुत्र</p>"],
                    solution_en: "<p>15.(c)<br><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfui_yzUHkP15IKR0nSNR9KYqtqLY2Zt3ov-o8pT0xUVtim9hZFXmSUwdvomBjxHBNRlKlBlIWh4d3jmnuMJiIiCIokqYnaHB3z1c6Mmku2uSALxUG7sOka1dOEfftgo0ElnJgeKw?key=hTxsrjWK-dOxgqlnoz5PI_Q8\" width=\"210\" height=\"118\"><br>T is the brother of G.</p>",
                    solution_hi: "<p>15.(c)<br><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfui_yzUHkP15IKR0nSNR9KYqtqLY2Zt3ov-o8pT0xUVtim9hZFXmSUwdvomBjxHBNRlKlBlIWh4d3jmnuMJiIiCIokqYnaHB3z1c6Mmku2uSALxUG7sOka1dOEfftgo0ElnJgeKw?key=hTxsrjWK-dOxgqlnoz5PI_Q8\" width=\"210\" height=\"118\"><br>T, G का भाई है.</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>