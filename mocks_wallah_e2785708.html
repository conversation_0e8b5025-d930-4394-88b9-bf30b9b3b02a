<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">15:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 15</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">15</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 15 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 0,
                end: 13
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 14,
                end: 14
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">1.</span><span style=\"font-family: Cambria Math;\"> The given expression is equal to:&nbsp; </span><span style=\"font-family: Cambria Math;\">(cotB - tanB). </span><span style=\"font-family: Cambria Math;\">sin</span><span style=\"font-family: Cambria Math;\">B. </span><span style=\"font-family: Cambria Math;\">cos</span><span style=\"font-family: Cambria Math;\">B</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">1.</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2327;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2381;&#2351;&#2306;&#2332;&#2325;</span><span style=\"font-family: Cambria Math;\"> (cot B &ndash; tan B). </span><span style=\"font-family: Cambria Math;\">sin</span><span style=\"font-family: Cambria Math;\"> B.cos B </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> _________ </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2352;&#2366;&#2348;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;&#2404;</span></p>\n",
                    options_en: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>1</mn><mo>-</mo><mn>2</mn><mi>s</mi><mi>e</mi><msup><mi>c</mi><mn>2</mn></msup><mi>B</mi></math></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>1</mn><mo>-</mo><mn>2</mn><msup><mi>cos</mi><mn>2</mn></msup><mi>B</mi></math></p>\n", 
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>2</mn><mo>&nbsp;</mo><msup><mi>cos</mi><mn>2</mn></msup><mi>B</mi><mo>-</mo><mn>1</mn></math></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>2</mn><mi>s</mi><mi>e</mi><msup><mi>c</mi><mn>2</mn></msup><mi>B</mi><mo>-</mo><mn>1</mn></math></p>\n"],
                    options_hi: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>1</mn><mo>-</mo><mn>2</mn><mi>s</mi><mi>e</mi><msup><mi>c</mi><mn>2</mn></msup><mo>&nbsp;</mo><mi>B</mi></math></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>1</mn><mo>-</mo><mn>2</mn><msup><mi>cos</mi><mn>2</mn></msup><mi>B</mi></math></p>\n",
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>2</mn><mo>&nbsp;</mo><msup><mi>cos</mi><mn>2</mn></msup><mi>B</mi><mo>-</mo><mn>1</mn></math></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>2</mn><mi>s</mi><mi>e</mi><msup><mi>c</mi><mn>2</mn></msup><mi>B</mi><mo>-</mo><mn>1</mn></math></p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">1.(</span><span style=\"font-family: Cambria Math;\">c)</span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>According</mi><mo>&nbsp;</mo><mi>to</mi><mo>&nbsp;</mo><mi>the</mi><mo>&nbsp;</mo><mi>question</mi><mo>,</mo><mspace linebreak=\"newline\"></mspace><mfrac><mi>cosB</mi><mi>sinB</mi></mfrac><mo>-</mo><mfrac><mi>sinB</mi><mi>cosB</mi></mfrac><mo>&times;</mo><mo>&nbsp;</mo><mfenced><mrow><mi>sinB</mi><mo>.</mo><mo>&nbsp;</mo><mi>cosB</mi></mrow></mfenced><mspace linebreak=\"newline\"></mspace><mfrac><mrow><msup><mrow><mo>(</mo><mi>cosB</mi><mo>)</mo></mrow><mn>2</mn></msup><mo>-</mo><msup><mrow><mo>(</mo><mi>sinB</mi><mo>)</mo></mrow><mn>2</mn></msup></mrow><mrow><mi>sinB</mi><mo>.</mo><mi>cosB</mi></mrow></mfrac><mo>&times;</mo><mo>&nbsp;</mo><mfenced><mrow><mi>sinB</mi><mo>.</mo><mo>&nbsp;</mo><mi>cosB</mi></mrow></mfenced><mo>=</mo><mo>&nbsp;</mo><mn>2</mn><msup><mi>cos</mi><mn>2</mn></msup><mi mathvariant=\"normal\">B</mi><mo>-</mo><mn>1</mn></math></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">1.(</span><span style=\"font-family: Cambria Math;\">c)</span></p>\r\n<p><math style=\"font-family:Verdana\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#2346;&#2381;&#2352;&#2358;&#2381;&#2344;</mi><mo>&nbsp;</mo><mi>&#2325;&#2375;</mi><mo>&nbsp;</mo><mi>&#2309;&#2344;&#2369;&#2360;&#2366;&#2352;</mi><mo>,</mo><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mfrac><mi>cosB</mi><mi>sinB</mi></mfrac><mo>-</mo><mfrac><mi>sinB</mi><mi>cosB</mi></mfrac><mo>&times;</mo><mo>&nbsp;</mo><mfenced><mrow><mi>sinB</mi><mo>.</mo><mo>&nbsp;</mo><mi>cosB</mi></mrow></mfenced><mspace linebreak=\"newline\"></mspace><mfrac><mrow><msup><mrow><mo>(</mo><mi>cosB</mi><mo>)</mo></mrow><mn>2</mn></msup><mo>-</mo><msup><mrow><mo>(</mo><mi>sinB</mi><mo>)</mo></mrow><mn>2</mn></msup></mrow><mrow><mi>sinB</mi><mo>.</mo><mi>cosB</mi></mrow></mfrac><mo>&times;</mo><mo>&nbsp;</mo><mfenced><mrow><mi>sinB</mi><mo>.</mo><mo>&nbsp;</mo><mi>cosB</mi></mrow></mfenced><mo>=</mo><mo>&nbsp;</mo><mn>2</mn><msup><mi>cos</mi><mn>2</mn></msup><mi mathvariant=\"normal\">B</mi><mo>-</mo><mn>1</mn></math></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">2.</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">If &theta;</span><span style=\"font-family: Cambria Math;\"> is an acute angle and </span><span style=\"font-family: Cambria Math;\">sin&theta;</span><span style=\"font-family: Cambria Math;\"> =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>43</mn><mn>47</mn></mfrac></math> </span><span style=\"font-family: Cambria Math;\"> What is the value of cot&theta; ?</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">2.</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> &theta; </span><span style=\"font-family: Cambria Math;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2344;&#2381;&#2351;&#2370;&#2344;&#2325;&#2379;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">sin&theta;</span><span style=\"font-family: Cambria Math;\"> =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>43</mn><mn>47</mn></mfrac></math> </span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">cot&theta;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>6</mn><msqrt><mn>10</mn></msqrt></mrow><mn>47</mn></mfrac></math></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>43</mn><mrow><mn>6</mn><msqrt><mn>10</mn></msqrt></mrow></mfrac></math></p>\n", 
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>47</mn><mrow><mn>6</mn><msqrt><mn>10</mn></msqrt></mrow></mfrac></math></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>6</mn><msqrt><mn>10</mn></msqrt></mrow><mn>43</mn></mfrac></math></p>\n"],
                    options_hi: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>6</mn><msqrt><mn>10</mn></msqrt></mrow><mn>47</mn></mfrac></math></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>43</mn><mrow><mn>6</mn><msqrt><mn>10</mn></msqrt></mrow></mfrac></math></p>\n",
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>47</mn><mrow><mn>6</mn><msqrt><mn>10</mn></msqrt></mrow></mfrac></math></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>6</mn><msqrt><mn>10</mn></msqrt></mrow><mn>43</mn></mfrac></math></p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">2.(</span><span style=\"font-family: Cambria Math;\">d)</span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>According</mi><mo>&nbsp;</mo><mi>to</mi><mo>&nbsp;</mo><mi>the</mi><mo>&nbsp;</mo><mi>question</mi><mo>,</mo><mspace linebreak=\"newline\"></mspace><mi>sin</mi><mi>&theta;</mi><mo>&nbsp;</mo><mo>=</mo><mfrac><mn>43</mn><mn>47</mn></mfrac><mo>=</mo><mfrac><mrow><mi>perpendicular</mi><mo>(</mo><mi mathvariant=\"normal\">p</mi><mo>)</mo></mrow><mrow><mi>hypotenuse</mi><mo>(</mo><mi mathvariant=\"normal\">h</mi><mo>)</mo></mrow></mfrac><mspace linebreak=\"newline\"></mspace><msup><mi mathvariant=\"normal\">h</mi><mn>2</mn></msup><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><msup><mi mathvariant=\"normal\">p</mi><mn>2</mn></msup><mo>+</mo><mo>&nbsp;</mo><msup><mi mathvariant=\"normal\">b</mi><mn>2</mn></msup><mspace linebreak=\"newline\"></mspace><mn>2209</mn><mo>-</mo><mn>1849</mn><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><msup><mi>b</mi><mn>2</mn></msup><mspace linebreak=\"newline\"></mspace><mi>b</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>6</mn><msqrt><mn>10</mn></msqrt><mspace linebreak=\"newline\"></mspace><mi>c</mi><mi>o</mi><mi>t</mi><mi>&theta;</mi><mo>=</mo><mfrac><mi>b</mi><mi>p</mi></mfrac><mo>=</mo><mfrac><mrow><mn>6</mn><msqrt><mn>10</mn></msqrt></mrow><mn>43</mn></mfrac></math></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">2.(</span><span style=\"font-family: Cambria Math;\">d)</span></p>\r\n<p><math style=\"font-family:Verdana\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#2346;&#2381;&#2352;&#2358;&#2381;&#2344;</mi><mo>&nbsp;</mo><mi>&#2325;&#2375;</mi><mo>&nbsp;</mo><mi>&#2309;&#2344;&#2369;&#2360;&#2366;&#2352;</mi><mo>,</mo><mspace linebreak=\"newline\"></mspace><mi>sin</mi><mi>&theta;</mi><mo>&nbsp;</mo><mo>=</mo><mfrac><mn>43</mn><mn>47</mn></mfrac><mo>=</mo><mfrac><mrow><mi>&#2354;&#2306;&#2348;</mi><mo>&nbsp;</mo><mo>(</mo><mi>p</mi><mo>)</mo></mrow><mrow><mi>&#2325;&#2352;&#2381;&#2339;</mi><mo>(</mo><mi>h</mi><mo>)</mo></mrow></mfrac><mspace linebreak=\"newline\"></mspace><msup><mi mathvariant=\"normal\">h</mi><mn>2</mn></msup><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><msup><mi mathvariant=\"normal\">p</mi><mn>2</mn></msup><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><msup><mi mathvariant=\"normal\">b</mi><mn>2</mn></msup><mspace linebreak=\"newline\"></mspace><mn>2209</mn><mo>-</mo><mn>1849</mn><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><msup><mi>b</mi><mn>2</mn></msup><mspace linebreak=\"newline\"></mspace><mi>b</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>6</mn><msqrt><mn>10</mn></msqrt><mspace linebreak=\"newline\"></mspace><mi>c</mi><mi>o</mi><mi>t</mi><mi>&theta;</mi><mo>=</mo><mfrac><mi>b</mi><mi>p</mi></mfrac><mo>=</mo><mfrac><mrow><mn>6</mn><msqrt><mn>10</mn></msqrt></mrow><mn>43</mn></mfrac></math></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">3.</span><span style=\"font-family: Cambria Math;\"> If cosec&theta;</span><span style=\"font-family: Cambria Math;\"> =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>17</mn><mn>15</mn></mfrac></math> </span><span style=\"font-family: Cambria Math;\"> , then what will be the value of cos&theta; </span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">3.</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> cosec&theta;</span><span style=\"font-family: Cambria Math;\"> =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>17</mn><mn>15</mn></mfrac></math> </span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">cos&theta;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2379;&#2327;</span><span style=\"font-family: Cambria Math;\">&#2366;</span><span style=\"font-family: Cambria Math;\"> ?</span></p>\n",
                    options_en: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>17</mn></mfrac></math></p>\n", "<p>1</p>\n", 
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>8</mn><mn>17</mn></mfrac></math></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>15</mn><mn>17</mn></mfrac></math></p>\n"],
                    options_hi: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>17</mn></mfrac></math></p>\n", "<p>1</p>\n",
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>8</mn><mn>17</mn></mfrac></math></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>15</mn><mn>17</mn></mfrac></math></p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">3.(</span><span style=\"font-family: Cambria Math;\">c)</span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>According</mi><mo>&nbsp;</mo><mi>to</mi><mo>&nbsp;</mo><mi>the</mi><mo>&nbsp;</mo><mi>question</mi><mo>,</mo><mspace linebreak=\"newline\"></mspace><mi>cos</mi><mi>e</mi><mi>c</mi><mi>&theta;</mi><mo>=</mo><mfrac><mn>17</mn><mn>15</mn></mfrac><mo>=</mo><mfrac><mrow><mi>hypotenuse</mi><mo>&nbsp;</mo><mo>(</mo><mi mathvariant=\"normal\">h</mi><mo>)</mo></mrow><mrow><mi>perpendicular</mi><mo>&nbsp;</mo><mo>(</mo><mi mathvariant=\"normal\">p</mi><mo>)</mo></mrow></mfrac><mspace linebreak=\"newline\"></mspace><mi>Triplate</mi><mo>:</mo><mo>&nbsp;</mo><mn>8</mn><mo>,</mo><mo>&nbsp;</mo><mn>15</mn><mo>&nbsp;</mo><mo>,</mo><mo>&nbsp;</mo><mn>17</mn><mspace linebreak=\"newline\"></mspace><mi>So</mi><mo>&nbsp;</mo><mi>the</mi><mo>&nbsp;</mo><mi>value</mi><mo>&nbsp;</mo><mi>of</mi><mo>&nbsp;</mo><mi>base</mi><mo>(</mo><mi mathvariant=\"normal\">b</mi><mo>)</mo><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>8</mn><mspace linebreak=\"newline\"></mspace><mi>cos</mi><mi>&theta;</mi><mo>&nbsp;</mo><mo>=</mo><mfrac><mi>b</mi><mi>h</mi></mfrac><mo>=</mo><mfrac><mn>8</mn><mn>17</mn></mfrac></math></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">3.(</span><span style=\"font-family: Cambria Math;\">c)</span></p>\r\n<p><math style=\"font-family:Verdana\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#2346;&#2381;&#2352;&#2358;&#2381;&#2344;</mi><mo>&nbsp;</mo><mi>&#2325;&#2375;</mi><mo>&nbsp;</mo><mi>&#2309;&#2344;&#2369;&#2360;&#2366;&#2352;</mi><mo>,</mo><mspace linebreak=\"newline\"></mspace><mi>cos</mi><mi>e</mi><mi>c</mi><mi>&theta;</mi><mo>=</mo><mfrac><mn>17</mn><mn>15</mn></mfrac><mo>=</mo><mfrac><mrow><mi>&#2325;&#2352;&#2381;&#2339;</mi><mo>&nbsp;</mo><mo>(</mo><mi>h</mi><mo>)</mo></mrow><mrow><mi>&#2354;&#2306;&#2348;</mi><mo>&nbsp;</mo><mo>(</mo><mi>p</mi><mo>)</mo></mrow></mfrac><mspace linebreak=\"newline\"></mspace><mi>&#2340;&#2381;&#2352;&#2367;&#2346;&#2381;&#2354;&#2375;&#2335;</mi><mo>&nbsp;</mo><mo>:</mo><mo>&nbsp;</mo><mn>8</mn><mo>,</mo><mo>&nbsp;</mo><mn>15</mn><mo>&nbsp;</mo><mo>,</mo><mo>&nbsp;</mo><mn>17</mn><mspace linebreak=\"newline\"></mspace><mi>&#2309;&#2340;&#2307;</mi><mo>&nbsp;</mo><mi>&#2310;&#2343;&#2366;&#2352;</mi><mo>(</mo><mi>b</mi><mo>)</mo><mo>&nbsp;</mo><mi>&#2325;&#2366;</mi><mo>&nbsp;</mo><mi>&#2350;&#2366;&#2344;</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>8</mn><mspace linebreak=\"newline\"></mspace><mi>cos</mi><mi>&theta;</mi><mo>&nbsp;</mo><mo>=</mo><mfrac><mi>b</mi><mi>h</mi></mfrac><mo>=</mo><mfrac><mn>8</mn><mn>17</mn></mfrac></math></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "17",
                    question_en: "<p>4. <span style=\"font-family: Cambria Math;\">If <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>sin&theta;</mi><mo>&nbsp;</mo><mo>&nbsp;</mo><mi>cos&theta;</mi><mo>&nbsp;</mo><mo>=</mo><mfrac><msqrt><mn>2</mn></msqrt><mn>3</mn></mfrac><mo>,</mo></math></span><span style=\"font-family: Cambria Math;\"> then the value of <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><msup><mi>sin</mi><mn>6</mn></msup><mi mathvariant=\"normal\">&theta;</mi><mo>+</mo><msup><mi>cos</mi><mn>6</mn></msup><mi mathvariant=\"normal\">&theta;</mi><mo>)</mo><mo>&nbsp;</mo></math> </span><span style=\"font-family: Cambria Math;\"> is : </span></p>\n",
                    question_hi: "<p>4. <span style=\"font-family: Cambria Math;\">&#2351;&#2342;</span><span style=\"font-family: Cambria Math;\">&#2367;</span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>sin&theta;</mi><mo>&nbsp;</mo><mo>&nbsp;</mo><mi>cos&theta;</mi><mo>&nbsp;</mo><mo>=</mo><mfrac><msqrt><mn>2</mn></msqrt><mn>3</mn></mfrac><mo>,</mo></math></span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><msup><mi>sin</mi><mn>6</mn></msup><mi mathvariant=\"normal\">&theta;</mi><mo>+</mo><msup><mi>cos</mi><mn>6</mn></msup><mi mathvariant=\"normal\">&theta;</mi><mo>)</mo><mo>&nbsp;</mo></math></span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>3</mn></mfrac></math></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>3</mn></mfrac></math></p>\n", 
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>3</mn></mfrac></math></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>3</mn></mfrac></math></p>\n"],
                    options_hi: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>3</mn></mfrac></math></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>3</mn></mfrac></math></p>\n",
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>3</mn></mfrac></math></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>3</mn></mfrac></math></p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">4.(</span><span style=\"font-family: Cambria Math;\">a)</span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>Given</mi><mo>:</mo><mo>&nbsp;</mo><mi>sin&theta;</mi><mo>&nbsp;</mo><mo>&nbsp;</mo><mi>cos&theta;</mi><mo>&nbsp;</mo><mo>=</mo><mfrac><msqrt><mn>2</mn></msqrt><mn>3</mn></mfrac><mo>&nbsp;</mo><mo>,</mo><mo>&nbsp;</mo><mfenced><mrow><msup><mi>sin</mi><mn>6</mn></msup><mi mathvariant=\"normal\">&theta;</mi><mo>+</mo><msup><mi>cos</mi><mn>6</mn></msup><mi mathvariant=\"normal\">&theta;</mi></mrow></mfenced><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mo>?</mo><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mfenced><mrow><msup><mi>sin</mi><mn>6</mn></msup><mi mathvariant=\"normal\">&theta;</mi><mo>+</mo><msup><mi>cos</mi><mn>6</mn></msup><mi mathvariant=\"normal\">&theta;</mi></mrow></mfenced><mo>=</mo><mo>&nbsp;</mo><msup><mrow><mo>(</mo><msup><mi>sin</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&theta;</mi><mo>)</mo></mrow><mn>3</mn></msup><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><msup><mrow><mo>(</mo><msup><mi>cos</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&theta;</mi><mo>)</mo></mrow><mn>3</mn></msup><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mo>=</mo><mfenced><mrow><msup><mi>sin</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&theta;</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><msup><mi>cos</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&theta;</mi></mrow></mfenced><mo>&nbsp;</mo><mfenced><mrow><msup><mi>sin</mi><mn>4</mn></msup><mi mathvariant=\"normal\">&theta;</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><msup><mi>cos</mi><mn>4</mn></msup><mi mathvariant=\"normal\">&theta;</mi><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><msup><mi>Sin</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&theta;</mi><mo>&nbsp;</mo><mo>.</mo><mo>&nbsp;</mo><msup><mi>cos</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&theta;</mi></mrow></mfenced><mspace linebreak=\"newline\"></mspace><mo>=</mo><msup><mfenced><mrow><msup><mi>sin</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&theta;</mi><mo>+</mo><msup><mi>cos</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&theta;</mi></mrow></mfenced><mn>2</mn></msup><mo>-</mo><mn>3</mn><msup><mi>sin</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&theta;</mi><mo>.</mo><msup><mi>cos</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&theta;</mi><mspace linebreak=\"newline\"></mspace><mo>=</mo><mn>1</mn><mo>-</mo><mn>3</mn><mi>s</mi><mi>i</mi><msup><mi>n</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&theta;</mi><mo>.</mo><mi>c</mi><mi>o</mi><msup><mi>s</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&theta;</mi><mo>&nbsp;</mo><mo>{</mo><mo>&#8757;</mo><mi>s</mi><mi>i</mi><mi>n</mi><mi mathvariant=\"normal\">&theta;</mi><mo>&nbsp;</mo><mi>c</mi><mi>o</mi><mi>s</mi><mi mathvariant=\"normal\">&theta;</mi><mo>=</mo><mfrac><msqrt><mn>2</mn></msqrt><mn>3</mn></mfrac><mo>=</mo><mi>s</mi><mi>i</mi><msup><mi>n</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&theta;</mi><mo>&nbsp;</mo><mi>c</mi><mi>o</mi><msup><mi>s</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&theta;</mi><mo>=</mo><mfrac><mn>2</mn><mn>9</mn></mfrac><mo>}</mo><mspace linebreak=\"newline\"></mspace><mo>=</mo><mn>1</mn><mo>-</mo><mn>3</mn><mo>&times;</mo><mfrac><mn>2</mn><mn>9</mn></mfrac><mo>=</mo><mfrac><mn>1</mn><mn>3</mn></mfrac></math></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">4.(</span><span style=\"font-family: Cambria Math;\">a)</span></p>\r\n<p><math style=\"font-family:Verdana\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#2342;&#2367;&#2351;&#2366;</mi><mo>&nbsp;</mo><mi>&#2327;&#2351;&#2366;</mi><mo>&nbsp;</mo><mi>&#2361;&#2376;</mi><mo>:</mo><mo>&nbsp;</mo><mi>sin&theta;</mi><mo>&nbsp;</mo><mo>&nbsp;</mo><mi>cos&theta;</mi><mo>&nbsp;</mo><mo>=</mo><mfrac><msqrt><mn>2</mn></msqrt><mn>3</mn></mfrac><mo>&nbsp;</mo><mo>,</mo><mo>&nbsp;</mo><mo>(</mo><msup><mi>sin</mi><mn>6</mn></msup><mi mathvariant=\"normal\">&theta;</mi><mo>+</mo><msup><mi>cos</mi><mn>6</mn></msup><mi mathvariant=\"normal\">&theta;</mi><mo>)</mo><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mo>?</mo><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mo>(</mo><msup><mi>sin</mi><mn>6</mn></msup><mi mathvariant=\"normal\">&theta;</mi><mo>+</mo><msup><mi>cos</mi><mn>6</mn></msup><mi mathvariant=\"normal\">&theta;</mi><mo>)</mo><mo>=</mo><mo>&nbsp;</mo><msup><mrow><mo>(</mo><msup><mi>sin</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&theta;</mi><mo>)</mo></mrow><mn>3</mn></msup><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><msup><mrow><mo>(</mo><msup><mi>cos</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&theta;</mi><mo>)</mo></mrow><mn>3</mn></msup><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mo>=</mo><mo>(</mo><msup><mi>sin</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&theta;</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><msup><mi>cos</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&theta;</mi><mo>)</mo><mo>&nbsp;</mo><mo>(</mo><msup><mi>sin</mi><mn>4</mn></msup><mi mathvariant=\"normal\">&theta;</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><msup><mi>cos</mi><mn>4</mn></msup><mi mathvariant=\"normal\">&theta;</mi><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><msup><mi>Sin</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&theta;</mi><mo>&nbsp;</mo><mo>.</mo><mo>&nbsp;</mo><msup><mi>cos</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&theta;</mi><mo>)</mo><mspace linebreak=\"newline\"></mspace><mo>=</mo><mo>{</mo><mrow><msup><mrow><mo>(</mo><msup><mi>sin</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&theta;</mi><mo>+</mo><msup><mi>cos</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&theta;</mi><mo>)</mo></mrow><mn>2</mn></msup><mo>-</mo><mn>3</mn><msup><mi>sin</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&theta;</mi><mo>.</mo><msup><mi>cos</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&theta;</mi><mo>}</mo></mrow><mspace linebreak=\"newline\"></mspace><mo>=</mo><mn>1</mn><mo>-</mo><mn>3</mn><mi>s</mi><mi>i</mi><msup><mi>n</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&theta;</mi><mo>.</mo><mi>c</mi><mi>o</mi><msup><mi>s</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&theta;</mi><mo>&nbsp;</mo><mo>{</mo><mo>&#8757;</mo><mi>s</mi><mi>i</mi><mi>n</mi><mi mathvariant=\"normal\">&theta;</mi><mo>&nbsp;</mo><mi>c</mi><mi>o</mi><mi>s</mi><mi mathvariant=\"normal\">&theta;</mi><mo>=</mo><mfrac><msqrt><mn>2</mn></msqrt><mn>3</mn></mfrac><mo>=</mo><mi>s</mi><mi>i</mi><msup><mi>n</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&theta;</mi><mo>&nbsp;</mo><mi>c</mi><mi>o</mi><msup><mi>s</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&theta;</mi><mo>=</mo><mfrac><mn>2</mn><mn>9</mn></mfrac><mo>}</mo><mspace linebreak=\"newline\"></mspace><mo>=</mo><mn>1</mn><mo>-</mo><mn>3</mn><mo>&nbsp;</mo><mo>&times;</mo><mfrac><mn>2</mn><mn>9</mn></mfrac><mo>=</mo><mfrac><mn>1</mn><mn>3</mn></mfrac></math></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "17",
                    question_en: "<p>5. <span style=\"font-family: Cambria Math;\">If A is an acute angle and 8 sec A = 17, find the value of tan A.</span></p>\n",
                    question_hi: "<p>5. <span style=\"font-family: Cambria Math;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> A </span><span style=\"font-family: Cambria Math;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2344;&#2381;&#2351;&#2370;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2379;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> 8 sec A = 17 </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> tan A </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2332;&#2381;&#2334;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;&#2332;&#2367;&#2319;&#2404;</span></p>\n",
                    options_en: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>15</mn><mn>17</mn></mfrac></math></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>15</mn><mn>8</mn></mfrac></math></p>\n", 
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>8</mn><mn>15</mn></mfrac></math></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>8</mn><mn>17</mn></mfrac></math></p>\n"],
                    options_hi: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>15</mn><mn>17</mn></mfrac></math></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>15</mn><mn>8</mn></mfrac></math></p>\n",
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>8</mn><mn>15</mn></mfrac></math></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>8</mn><mn>17</mn></mfrac></math></p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">5.(</span><span style=\"font-family: Cambria Math;\">b)</span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>Given</mi><mo>:</mo><mo>&nbsp;</mo><mi>s</mi><mi>e</mi><mi>c</mi><mi>A</mi><mo>&nbsp;</mo><mo>=</mo><mfrac><mrow><mo>&nbsp;</mo><mn>17</mn></mrow><mn>8</mn></mfrac><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mi>s</mi><mi>e</mi><msup><mi>c</mi><mn>2</mn></msup><mi>A</mi><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><msup><mi>tan</mi><mn>2</mn></msup><mi>A</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>1</mn><mspace linebreak=\"newline\"></mspace><mi>tan</mi><mi>A</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><msqrt><mi>s</mi><mi>e</mi><msup><mi>c</mi><mn>2</mn></msup><mi>A</mi><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>1</mn></msqrt><mspace linebreak=\"newline\"></mspace><mi>tan</mi><mi>A</mi><mo>&nbsp;</mo><mo>=</mo><msqrt><mo>&nbsp;</mo><msup><mrow><mo>(</mo><mfrac><mn>17</mn><mn>8</mn></mfrac><mo>)</mo></mrow><mn>2</mn></msup><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>1</mn></msqrt><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mfrac><mn>15</mn><mn>8</mn></mfrac></math></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">5.(</span><span style=\"font-family: Cambria Math;\">b)</span></p>\r\n<p><math style=\"font-family:Verdana\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#2342;&#2367;&#2351;&#2366;</mi><mo>&nbsp;</mo><mi>&#2327;&#2351;&#2366;</mi><mo>:</mo><mo>&nbsp;</mo><mi>s</mi><mi>e</mi><mi>c</mi><mi>A</mi><mo>&nbsp;</mo><mo>=</mo><mfrac><mrow><mo>&nbsp;</mo><mn>17</mn></mrow><mn>8</mn></mfrac><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mi>s</mi><mi>e</mi><msup><mi>c</mi><mn>2</mn></msup><mi>A</mi><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><msup><mi>tan</mi><mn>2</mn></msup><mi>A</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>1</mn><mspace linebreak=\"newline\"></mspace><mi>tan</mi><mi>A</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><msqrt><mi>s</mi><mi>e</mi><msup><mi>c</mi><mn>2</mn></msup><mi>A</mi><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>1</mn></msqrt><mspace linebreak=\"newline\"></mspace><mi>tan</mi><mi>A</mi><mo>&nbsp;</mo><mo>=</mo><msqrt><mo>&nbsp;</mo><msup><mrow><mo>(</mo><mfrac><mn>17</mn><mn>8</mn></mfrac><mo>)</mo></mrow><mn>2</mn></msup><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>1</mn></msqrt><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mfrac><mn>15</mn><mn>8</mn></mfrac></math></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "17",
                    question_en: "<p>6. <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>c</mi><mi>o</mi><msup><mi>t</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&theta;</mi><mo>-</mo><mi>cos</mi><mi>e</mi><msup><mi>c</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&theta;</mi></math><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">equals</span><span style=\"font-family: Cambria Math;\"> to : </span></p>\n",
                    question_hi: "<p>6. <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>c</mi><mi>o</mi><msup><mi>t</mi><mn>2</mn></msup><mi>&theta;</mi><mo>-</mo><mi>cos</mi><mi>e</mi><msup><mi>c</mi><mn>2</mn></msup><mi>&theta;</mi></math><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> ______ </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2352;&#2366;&#2348;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                    options_en: ["<p>0</p>\n", "<p>tan<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&theta;</mi></math></p>\n", 
                                "<p>1</p>\n", "<p>-1</p>\n"],
                    options_hi: ["<p>0</p>\n", "<p>tan<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&theta;</mi></math></p>\n",
                                "<p>1</p>\n", "<p>-1</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">6.(</span><span style=\"font-family: Cambria Math;\">d)</span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>Given</mi><mo>:</mo><mo>&nbsp;</mo><mi>c</mi><mi>o</mi><msup><mi>t</mi><mn>2</mn></msup><mi>&theta;</mi><mo>-</mo><mi>cos</mi><mi>e</mi><msup><mi>c</mi><mn>2</mn></msup><mi>&theta;</mi><mo>=</mo><mo>&nbsp;</mo><mo>?</mo><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mi>cos</mi><mi>e</mi><msup><mi>c</mi><mn>2</mn></msup><mi>&theta;</mi><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mi>c</mi><mi>o</mi><msup><mi>t</mi><mn>2</mn></msup><mi>&theta;</mi><mo>=</mo><mo>&nbsp;</mo><mn>1</mn><mspace linebreak=\"newline\"></mspace><mo>-</mo><mo>&nbsp;</mo><mo>(</mo><mi>c</mi><mi>o</mi><msup><mi>t</mi><mn>2</mn></msup><mi>&theta;</mi><mo>-</mo><mi>cos</mi><mi>e</mi><msup><mi>c</mi><mn>2</mn></msup><mi>&theta;</mi><mo>)</mo><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>1</mn><mspace linebreak=\"newline\"></mspace><mo>&nbsp;</mo><mi>c</mi><mi>o</mi><msup><mi>t</mi><mn>2</mn></msup><mi>&theta;</mi><mo>-</mo><mi>cos</mi><mi>e</mi><msup><mi>c</mi><mn>2</mn></msup><mi>&theta;</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mo>-</mo><mn>1</mn></math></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">6.(</span><span style=\"font-family: Cambria Math;\">d)</span></p>\r\n<p><math style=\"font-family:Verdana\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#2342;&#2367;&#2351;&#2366;</mi><mo>&nbsp;</mo><mi>&#2327;&#2351;&#2366;</mi><mo>:</mo><mo>&nbsp;</mo><mi>c</mi><mi>o</mi><msup><mi>t</mi><mn>2</mn></msup><mi>&theta;</mi><mo>-</mo><mi>cos</mi><mi>e</mi><msup><mi>c</mi><mn>2</mn></msup><mi>&theta;</mi><mo>=</mo><mo>&nbsp;</mo><mo>?</mo><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mi>cos</mi><mi>e</mi><msup><mi>c</mi><mn>2</mn></msup><mi>&theta;</mi><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mi>c</mi><mi>o</mi><msup><mi>t</mi><mn>2</mn></msup><mi>&theta;</mi><mo>=</mo><mo>&nbsp;</mo><mn>1</mn><mspace linebreak=\"newline\"></mspace><mo>-</mo><mo>&nbsp;</mo><mo>(</mo><mi>c</mi><mi>o</mi><msup><mi>t</mi><mn>2</mn></msup><mi>&theta;</mi><mo>-</mo><mi>cos</mi><mi>e</mi><msup><mi>c</mi><mn>2</mn></msup><mi>&theta;</mi><mo>)</mo><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>1</mn><mspace linebreak=\"newline\"></mspace><mo>&nbsp;</mo><mi>c</mi><mi>o</mi><msup><mi>t</mi><mn>2</mn></msup><mi>&theta;</mi><mo>-</mo><mi>cos</mi><mi>e</mi><msup><mi>c</mi><mn>2</mn></msup><mi>&theta;</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mo>-</mo><mn>1</mn></math></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">7. </span><span style=\"font-family: Cambria Math;\">If </span><span style=\"font-family: Cambria Math;\">cosecA</span><span style=\"font-family: Cambria Math;\"> + </span><span style=\"font-family: Cambria Math;\">cotA</span><span style=\"font-family: Cambria Math;\"> = a<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mi>b</mi></msqrt></math></span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">, then</span><span style=\"font-family: Cambria Math;\"> find the value of<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><msup><mi>a</mi><mn>2</mn></msup><mo>&nbsp;</mo><mi>b</mi><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>1</mn><mo>)</mo></mrow><mrow><mo>(</mo><msup><mi>a</mi><mn>2</mn></msup><mo>&nbsp;</mo><mi>b</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>1</mn><mo>)</mo></mrow></mfrac></math></span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> .</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">7. </span><span style=\"font-family: Cambria Math;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">cosecA</span><span style=\"font-family: Cambria Math;\"> + </span><span style=\"font-family: Cambria Math;\">cotA</span><span style=\"font-family: Cambria Math;\"> = </span><span style=\"font-family: Cambria Math;\">a<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mi>b</mi></msqrt></math></span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><msup><mi>a</mi><mn>2</mn></msup><mo>&nbsp;</mo><mi>b</mi><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>1</mn><mo>)</mo></mrow><mrow><mo>(</mo><msup><mi>a</mi><mn>2</mn></msup><mo>&nbsp;</mo><mi>b</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>1</mn><mo>)</mo></mrow></mfrac></math></span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2332;&#2381;&#2334;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;&#2332;&#2367;&#2319;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                    options_en: ["<p><span style=\"font-family: Cambria Math;\">cosA</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><span style=\"font-family: Cambria Math;\">tanA</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", 
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><mi>sin</mi><mi>A</mi></mrow></mfrac></math></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><mi>c</mi><mi>o</mi><mi>t</mi><mi>A</mi></mrow></mfrac></math></p>\n"],
                    options_hi: ["<p><span style=\"font-family: Cambria Math;\">cosA</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><span style=\"font-family: Cambria Math;\">tanA</span><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><mi>sin</mi><mi>A</mi></mrow></mfrac></math></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><mi>c</mi><mi>o</mi><mi>t</mi><mi>A</mi></mrow></mfrac></math></p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">7.(</span><span style=\"font-family: Cambria Math;\">a)</span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>cos</mi><mi>e</mi><mi>c</mi><mi>A</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mi>c</mi><mi>o</mi><mi>t</mi><mi>A</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mi>a</mi><msqrt><mi>b</mi></msqrt><mspace linebreak=\"newline\"></mspace><msup><mrow><mo>(</mo><mfrac><mn>1</mn><mrow><mi>sin</mi><mi>A</mi></mrow></mfrac><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mfrac><mrow><mi>cos</mi><mi>A</mi></mrow><mrow><mi>sin</mi><mi>A</mi></mrow></mfrac><mo>)</mo></mrow><mn>2</mn></msup><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><msup><mrow><mo>(</mo><mfrac><mrow><mn>1</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mi>cos</mi><mi>A</mi></mrow><mrow><mi>sin</mi><mi>A</mi></mrow></mfrac><mo>)</mo></mrow><mn>2</mn></msup><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><msup><mi>a</mi><mn>2</mn></msup><mo>&nbsp;</mo><mi>b</mi><mspace linebreak=\"newline\"></mspace><mfrac><mrow><msup><mi>a</mi><mn>2</mn></msup><mo>&nbsp;</mo><mi>b</mi><mo>-</mo><mo>&nbsp;</mo><mn>1</mn></mrow><mrow><msup><mi>a</mi><mn>2</mn></msup><mo>&nbsp;</mo><mi>b</mi><mo>+</mo><mn>1</mn></mrow></mfrac><mo>=</mo><mfrac><mrow><msup><mrow><mo>(</mo><mstyle displaystyle=\"true\"><mfrac><mrow><mn>1</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mi>cos</mi><mi>A</mi></mrow><mrow><mi>sin</mi><mi>A</mi></mrow></mfrac></mstyle><mo>)</mo></mrow><mn>2</mn></msup><mo>-</mo><mn>1</mn></mrow><mrow><mo>(</mo><mstyle displaystyle=\"true\"><mfrac><mrow><mn>1</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mi>cos</mi><mi>A</mi></mrow><mrow><mi>sin</mi><mi>A</mi></mrow></mfrac><msup><mo>)</mo><mn>2</mn></msup></mstyle><mo>+</mo><mn>1</mn></mrow></mfrac><mspace linebreak=\"newline\"></mspace><mo>=</mo><mfrac><mrow><mn>1</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><msup><mi>cos</mi><mn>2</mn></msup><mi>A</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>2</mn><mi>cos</mi><mi>A</mi><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><msup><mi>sin</mi><mn>2</mn></msup><mi>A</mi></mrow><mrow><mn>1</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><msup><mi>cos</mi><mn>2</mn></msup><mi>A</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>2</mn><mi>cos</mi><mi>A</mi><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><msup><mi>sin</mi><mn>2</mn></msup><mi>A</mi></mrow></mfrac><mspace linebreak=\"newline\"></mspace><mo>=</mo><mfrac><mrow><mn>2</mn><msup><mi>cos</mi><mn>2</mn></msup><mi>A</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>2</mn><mi>cos</mi><mi>A</mi></mrow><mrow><mo>&nbsp;</mo><mn>2</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>2</mn><mi>cos</mi><mi>A</mi></mrow></mfrac><mo>=</mo><mfrac><mrow><mn>2</mn><mi>cos</mi><mi>A</mi><mo>&nbsp;</mo><mo>(</mo><mi>cos</mi><mi>A</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>1</mn><mo>)</mo></mrow><mrow><mn>2</mn><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>(</mo><mi>cos</mi><mi>A</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>1</mn><mo>)</mo></mrow></mfrac><mo>=</mo><mo>&nbsp;</mo><mi>cos</mi><mi>A</mi></math></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">7.(</span><span style=\"font-family: Cambria Math;\">a)</span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>c</mi><mi>o</mi><mi>s</mi><mi>e</mi><mi>c</mi><mi>A</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mi>c</mi><mi>o</mi><mi>t</mi><mi>A</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mi>a</mi><msqrt><mi mathvariant=\"normal\">b</mi></msqrt><mspace linebreak=\"newline\"></mspace><msup><mrow><mo>(</mo><mfrac><mn>1</mn><mrow><mi>sin</mi><mi>A</mi></mrow></mfrac><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mfrac><mrow><mi>cos</mi><mi>A</mi></mrow><mrow><mi>sin</mi><mi>A</mi></mrow></mfrac><mo>)</mo></mrow><mn>2</mn></msup><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><msup><mrow><mo>(</mo><mfrac><mrow><mn>1</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mi>cos</mi><mi>A</mi></mrow><mrow><mi>sin</mi><mi>A</mi></mrow></mfrac><mo>)</mo></mrow><mn>2</mn></msup><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><msup><mi>a</mi><mn>2</mn></msup><mo>&nbsp;</mo><mi>b</mi><mspace linebreak=\"newline\"></mspace><mfrac><mrow><msup><mi>a</mi><mn>2</mn></msup><mo>&nbsp;</mo><mi>b</mi><mo>-</mo><mo>&nbsp;</mo><mn>1</mn></mrow><mrow><msup><mi>a</mi><mn>2</mn></msup><mo>&nbsp;</mo><mi>b</mi><mo>+</mo><mn>1</mn></mrow></mfrac><mo>=</mo><mfrac><mrow><msup><mrow><mo>(</mo><mstyle displaystyle=\"true\"><mfrac><mrow><mn>1</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mi>cos</mi><mi>A</mi></mrow><mrow><mi>sin</mi><mi>A</mi></mrow></mfrac></mstyle><mo>)</mo></mrow><mn>2</mn></msup><mo>-</mo><mn>1</mn></mrow><mstyle displaystyle=\"true\"><msup><mrow><mo>(</mo><mfrac><mrow><mn>1</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mi>cos</mi><mi>A</mi></mrow><mrow><mi>sin</mi><mi>A</mi></mrow></mfrac><mo>)</mo></mrow><mn>2</mn></msup><mo>+</mo><mn>1</mn></mstyle></mfrac><mspace linebreak=\"newline\"></mspace><mo>=</mo><mfrac><mrow><mn>1</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><msup><mi>cos</mi><mn>2</mn></msup><mi>A</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>2</mn><mi>cos</mi><mi>A</mi><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><msup><mi>sin</mi><mn>2</mn></msup><mi>A</mi></mrow><mrow><mn>1</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><msup><mi>cos</mi><mn>2</mn></msup><mi>A</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>2</mn><mi>cos</mi><mi>A</mi><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><msup><mi>sin</mi><mn>2</mn></msup><mi>A</mi></mrow></mfrac><mspace linebreak=\"newline\"></mspace><mo>=</mo><mfrac><mrow><mn>2</mn><msup><mi>cos</mi><mn>2</mn></msup><mi>A</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>2</mn><mi>cos</mi><mi>A</mi></mrow><mrow><mo>&nbsp;</mo><mn>2</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>2</mn><mi>cos</mi><mi>A</mi></mrow></mfrac><mo>=</mo><mfrac><mrow><mn>2</mn><mi>cos</mi><mi>A</mi><mo>&nbsp;</mo><mo>(</mo><mi>cos</mi><mi>A</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>1</mn><mo>)</mo></mrow><mrow><mn>2</mn><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>(</mo><mi>cos</mi><mi>A</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>1</mn><mo>)</mo></mrow></mfrac><mo>=</mo><mo>&nbsp;</mo><mi>cos</mi><mi>A</mi></math></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">8</span><span style=\"font-family: Cambria Math;\">. If tan A</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">=<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>4</mn></mfrac></math> </span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">find the value of the following expression.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>6</mn><mo>&nbsp;</mo><mi>sin</mi><mo>&nbsp;</mo><mi>A</mi></mrow><mrow><mn>1</mn><mo>-</mo><mi>sin</mi><mi>A</mi></mrow></mfrac></math></span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">8. </span><span style=\"font-family: Cambria Math;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> tan A = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>4</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2381;&#2351;&#2306;&#2332;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2332;&#2381;&#2334;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;&#2332;&#2367;&#2319;&#2404;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>6</mn><mo>&nbsp;</mo><mi>sin</mi><mo>&nbsp;</mo><mi>A</mi></mrow><mrow><mn>1</mn><mo>-</mo><mi>sin</mi><mi>A</mi></mrow></mfrac></math></span></p>\n",
                    options_en: ["<p>18</p>\n", "<p>9</p>\n", 
                                "<p>24</p>\n", "<p>12</p>\n"],
                    options_hi: ["<p>18</p>\n", "<p>9</p>\n",
                                "<p>24</p>\n", "<p>12</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">8.(</span><span style=\"font-family: Cambria Math;\">b)</span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>tan</mi><mo>&nbsp;</mo><mi>A</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mfrac><mi>perpendicular</mi><mi>base</mi></mfrac><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mfrac><mn>3</mn><mn>4</mn></mfrac><mspace linebreak=\"newline\"></mspace><mi>Hypotenuse</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><msqrt><msup><mrow><mo>(</mo><mn>3</mn><mo>)</mo></mrow><mn>2</mn></msup><mo>+</mo><msup><mrow><mo>(</mo><mn>4</mn><mo>)</mo></mrow><mn>2</mn></msup><mo>&nbsp;</mo></msqrt><mo>=</mo><mo>&nbsp;</mo><mn>5</mn><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mi>Then</mi><mo>,</mo><mo>&nbsp;</mo><mfrac><mrow><mn>6</mn><mo>&nbsp;</mo><mi>sin</mi><mo>&nbsp;</mo><mi>A</mi></mrow><mrow><mn>1</mn><mo>-</mo><mi>sin</mi><mi>A</mi></mrow></mfrac><mo>=</mo><mfrac><mrow><mn>6</mn><mo>&times;</mo><mstyle displaystyle=\"true\"><mfrac><mn>3</mn><mn>5</mn></mfrac></mstyle></mrow><mrow><mn>1</mn><mo>-</mo><mstyle displaystyle=\"true\"><mfrac><mn>3</mn><mn>5</mn></mfrac></mstyle></mrow></mfrac><mo>=</mo><mn>9</mn></math></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">8.(</span><span style=\"font-family: Cambria Math;\">b)</span></p>\r\n<p><math style=\"font-family: Verdana;\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>tan</mi><mo>&nbsp;</mo><mi>A</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mfrac><mi>&#2354;&#2306;&#2348;</mi><mi>&#2310;&#2343;&#2366;&#2352;</mi></mfrac><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mfrac><mn>3</mn><mn>4</mn></mfrac><mspace linebreak=\"newline\"></mspace><mi>&#2325;&#2352;&#2381;&#2339;</mi><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><msqrt><msup><mrow><mo>(</mo><mn>3</mn><mo>)</mo></mrow><mn>2</mn></msup><mo>+</mo><msup><mrow><mo>(</mo><mn>4</mn><mo>)</mo></mrow><mn>2</mn></msup><mo>&nbsp;</mo></msqrt><mo>=</mo><mo>&nbsp;</mo><mn>5</mn><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mi>&#2347;&#2367;&#2352;</mi><mo>&nbsp;</mo><mo>,</mo><mo>&nbsp;</mo><mfrac><mrow><mn>6</mn><mo>&nbsp;</mo><mi>sin</mi><mo>&nbsp;</mo><mi>A</mi></mrow><mrow><mn>1</mn><mo>-</mo><mi>sin</mi><mi>A</mi></mrow></mfrac><mo>=</mo><mfrac><mrow><mn>6</mn><mo>&times;</mo><mstyle displaystyle=\"true\"><mfrac><mn>3</mn><mn>5</mn></mfrac></mstyle></mrow><mrow><mn>1</mn><mo>-</mo><mstyle displaystyle=\"true\"><mfrac><mn>3</mn><mn>5</mn></mfrac></mstyle></mrow></mfrac><mo>=</mo><mn>9</mn></math></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">9. </span><span style=\"font-family: Cambria Math;\">In a right-angled triangle PQR, PQ = 5 cm, QR = 13 cm and&ang;</span><span style=\"font-family: Cambria Math;\">P = 90&deg;. Find the value of tan Q - tan R.</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">9. </span><span style=\"font-family: Cambria Math;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2350;&#2325;&#2379;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2340;&#2381;&#2352;&#2367;&#2349;&#2369;&#2332;</span><span style=\"font-family: Cambria Math;\"> PQR </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\">, PQ = 5 cm, QR = 13 cm </span><span style=\"font-family: Cambria Math;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> &ang;</span><span style=\"font-family: Cambria Math;\">P = 90&deg; </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">tanQ</span><span style=\"font-family: Cambria Math;\"> - tan R </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2332;&#2381;&#2334;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;&#2332;&#2367;&#2319;</span></p>\n",
                    options_en: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>14</mn></mfrac></math><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>119</mn><mn>60</mn></mfrac></math> </span></p>\n", 
                                "<p><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>60</mn><mn>119</mn></mfrac></math> </span></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>14</mn><mn>5</mn></mfrac></math></p>\n"],
                    options_hi: ["<p><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>14</mn></mfrac></math> </span></p>\n", "<p><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>119</mn><mrow><mn>60</mn><mo>&nbsp;</mo><mo>&nbsp;</mo></mrow></mfrac></math> </span></p>\n",
                                "<p><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>60</mn><mn>119</mn></mfrac></math> </span></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>14</mn><mn>5</mn></mfrac></math></p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">9.(</span><span style=\"font-family: Cambria Math;\">b)</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1698733937/word/media/image1.png\" width=\"124\" height=\"113\"></p>\r\n<p><span style=\"font-family: Cambria Math;\">TanQ</span><span style=\"font-family: Cambria Math;\"> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>12</mn><mn>5</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> and TanR =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>12</mn></mfrac></math> </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">So, </span><span style=\"font-family: Cambria Math;\">tanQ</span><span style=\"font-family: Cambria Math;\"> - </span><span style=\"font-family: Cambria Math;\">tanR = </span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>12</mn><mn>5</mn></mfrac><mo>-</mo><mfrac><mn>5</mn><mn>12</mn></mfrac><mo>=</mo><mfrac><mrow><mn>144</mn><mo>-</mo><mn>25</mn></mrow><mn>60</mn></mfrac><mo>=</mo><mfrac><mn>119</mn><mn>60</mn></mfrac></math><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">9.(</span><span style=\"font-family: Cambria Math;\">b)</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1698733937/word/media/image1.png\" width=\"136\" height=\"124\"></p>\r\n<p><span style=\"font-family: Cambria Math;\">TanQ</span><span style=\"font-family: Cambria Math;\"> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>12</mn><mn>5</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">TanR</span><span style=\"font-family: Cambria Math;\"> =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>12</mn></mfrac></math> </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> , </span><span style=\"font-family: Cambria Math;\">tanQ</span><span style=\"font-family: Cambria Math;\"> - </span><span style=\"font-family: Cambria Math;\">tanR = </span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>12</mn><mn>5</mn></mfrac><mo>-</mo><mfrac><mn>5</mn><mn>12</mn></mfrac><mo>=</mo><mfrac><mrow><mn>144</mn><mo>-</mo><mn>25</mn></mrow><mn>60</mn></mfrac><mo>=</mo><mfrac><mn>119</mn><mn>60</mn></mfrac></math><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">10</span><span style=\"font-family: Cambria Math;\">.</span><span style=\"font-family: Cambria Math;\"> If Cos&theta;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">=<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>5</mn></mfrac></math> </span><span style=\"font-family: Cambria Math;\">, find the value of Cot</span><span style=\"font-family: Cambria Math;\"> &theta;+ tan&theta;</span><span style=\"font-family: Cambria Math;\">.</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">10</span><span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Cambria Math;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> Cos&theta;</span><span style=\"font-family: Cambria Math;\"> =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>5</mn></mfrac></math> </span><span style=\"font-family: Cambria Math;\"> , </span><span style=\"font-family: Cambria Math;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> Cot&theta;</span><span style=\"font-family: Cambria Math;\"> + tan&theta;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2332;&#2381;&#2334;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;&#2332;&#2367;&#2319;&#2404;</span></p>\n",
                    options_en: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>12</mn><mn>25</mn></mfrac></math><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>25</mn><mn>12</mn></mfrac></math></p>\n", 
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>27</mn><mn>12</mn></mfrac></math></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>12</mn><mn>27</mn></mfrac></math></p>\n"],
                    options_hi: ["<p><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>12</mn><mn>25</mn></mfrac></math> </span></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>25</mn><mn>12</mn></mfrac></math></p>\n",
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>27</mn><mn>12</mn></mfrac></math></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>12</mn><mn>27</mn></mfrac></math></p>\n"],
                    solution_en: "<p>10<span style=\"font-family: Cambria Math;\">.(b)</span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>Cos&theta;</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mfrac><mrow><mi>base</mi><mo>(</mo><mi mathvariant=\"normal\">b</mi><mo>)</mo></mrow><mrow><mi>hypotenuse</mi><mo>(</mo><mi mathvariant=\"normal\">h</mi><mo>)</mo><mo>&nbsp;</mo><mo>&nbsp;</mo></mrow></mfrac><mo>=</mo><mo>&nbsp;</mo><mfrac><mn>4</mn><mn>5</mn></mfrac><mspace linebreak=\"newline\"></mspace><mi>Triplate</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>3</mn><mo>,</mo><mo>&nbsp;</mo><mn>4</mn><mo>&nbsp;</mo><mo>,</mo><mo>&nbsp;</mo><mn>5</mn><mspace linebreak=\"newline\"></mspace><mi>So</mi><mo>&nbsp;</mo><mi>the</mi><mo>&nbsp;</mo><mi>value</mi><mo>&nbsp;</mo><mi>of</mi><mo>&nbsp;</mo><mi>perpendicular</mi><mo>(</mo><mi mathvariant=\"normal\">p</mi><mo>)</mo><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>3</mn><mspace linebreak=\"newline\"></mspace><mi>According</mi><mo>&nbsp;</mo><mi>to</mi><mo>&nbsp;</mo><mi>the</mi><mo>&nbsp;</mo><mi>question</mi><mo>,</mo><mspace linebreak=\"newline\"></mspace><mi>Cot&theta;</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mi>tan&theta;</mi><mo>&nbsp;</mo><mo>=</mo><mfrac><mi mathvariant=\"normal\">b</mi><mi mathvariant=\"normal\">p</mi></mfrac><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mfrac><mi mathvariant=\"normal\">p</mi><mi mathvariant=\"normal\">b</mi></mfrac><mspace linebreak=\"newline\"></mspace><mfrac><mn>4</mn><mn>3</mn></mfrac><mo>+</mo><mfrac><mn>3</mn><mn>4</mn></mfrac><mo>=</mo><mfrac><mrow><mn>16</mn><mo>+</mo><mn>9</mn></mrow><mn>12</mn></mfrac><mo>=</mo><mfrac><mn>25</mn><mn>12</mn></mfrac></math></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                    solution_hi: "<p>10<span style=\"font-family: Cambria Math;\">.(b)</span></p>\r\n<p><math style=\"font-family:Verdana\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>Cos&theta;</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mfrac><mrow><mi>&#2310;&#2343;&#2366;&#2352;</mi><mo>(</mo><mi>b</mi><mo>)</mo></mrow><mrow><mi>&#2325;&#2352;&#2381;&#2339;</mi><mo>(</mo><mi>h</mi><mo>)</mo><mo>&nbsp;</mo><mo>&nbsp;</mo></mrow></mfrac><mo>=</mo><mo>&nbsp;</mo><mfrac><mn>4</mn><mn>5</mn></mfrac><mspace linebreak=\"newline\"></mspace><mi>&#2340;&#2381;&#2352;&#2367;&#2346;&#2381;&#2354;&#2375;&#2335;</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>3</mn><mo>,</mo><mo>&nbsp;</mo><mn>4</mn><mo>&nbsp;</mo><mo>,</mo><mo>&nbsp;</mo><mn>5</mn><mspace linebreak=\"newline\"></mspace><mi>&#2309;&#2340;&#2307;</mi><mo>&nbsp;</mo><mi>&#2354;&#2350;&#2381;&#2348;</mi><mo>&nbsp;</mo><mo>(</mo><mi>p</mi><mo>)</mo><mo>&nbsp;</mo><mi>&#2325;&#2366;</mi><mo>&nbsp;</mo><mi>&#2350;&#2366;&#2344;</mi><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>3</mn><mspace linebreak=\"newline\"></mspace><mi>&#2346;&#2381;&#2352;&#2358;&#2381;&#2344;</mi><mo>&nbsp;</mo><mi>&#2325;&#2375;</mi><mo>&nbsp;</mo><mi>&#2309;&#2344;&#2369;&#2360;&#2366;&#2352;</mi><mo>,</mo><mspace linebreak=\"newline\"></mspace><mi>Cot&theta;</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mi>tan&theta;</mi><mo>&nbsp;</mo><mo>=</mo><mfrac><mi mathvariant=\"normal\">b</mi><mi mathvariant=\"normal\">p</mi></mfrac><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mfrac><mi mathvariant=\"normal\">p</mi><mi mathvariant=\"normal\">b</mi></mfrac><mspace linebreak=\"newline\"></mspace><mfrac><mn>4</mn><mn>3</mn></mfrac><mo>+</mo><mfrac><mn>3</mn><mn>4</mn></mfrac><mo>=</mo><mfrac><mrow><mn>16</mn><mo>+</mo><mn>9</mn></mrow><mn>12</mn></mfrac><mo>=</mo><mfrac><mn>25</mn><mn>12</mn></mfrac><mo>&nbsp;</mo></math></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">11</span><span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Cambria Math;\">If sin&theta;</span><span style=\"font-family: Cambria Math;\"> cos&theta;</span><span style=\"font-family: Cambria Math;\"> =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msqrt><mn>3</mn></msqrt></mfrac></math> </span><span style=\"font-family: Cambria Math;\"> then the value of </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><msup><mi>sin</mi><mn>4</mn></msup><mi mathvariant=\"normal\">&theta;</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><msup><mi>cos</mi><mn>4</mn></msup><mi mathvariant=\"normal\">&theta;</mi><mo>&nbsp;</mo><mo>)</mo><mo>&nbsp;</mo></math><span style=\"font-family: Cambria Math;\">is:</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">11</span><span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> sin&theta;</span><span style=\"font-family: Cambria Math;\"> cos&theta;</span><span style=\"font-family: Cambria Math;\"> =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msqrt><mn>3</mn></msqrt></mfrac></math> </span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2340;&#2379;</span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><msup><mi>sin</mi><mn>4</mn></msup><mi mathvariant=\"normal\">&theta;</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><msup><mi>cos</mi><mn>4</mn></msup><mi mathvariant=\"normal\">&theta;</mi><mo>&nbsp;</mo><mo>)</mo><mo>&nbsp;</mo></math><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>1</p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>3</mn></mfrac></math><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> </span></p>\n", 
                                "<p><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>3</mn></mfrac></math> </span></p>\n", "<p><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>3</mn></mfrac></math> </span></p>\n"],
                    options_hi: ["<p>1</p>\n", "<p><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>3</mn></mfrac></math> </span><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                                "<p><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>3</mn></mfrac></math> </span></p>\n", "<p><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>3</mn></mfrac></math> </span></p>\n"],
                    solution_en: "<p>11<span style=\"font-family: Cambria Math;\">.(d)</span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>According</mi><mo>&nbsp;</mo><mi>to</mi><mo>&nbsp;</mo><mi>the</mi><mo>&nbsp;</mo><mi>question</mi><mo>,</mo><mspace linebreak=\"newline\"></mspace><mo>(</mo><msup><mi>sin</mi><mn>4</mn></msup><mi mathvariant=\"normal\">&theta;</mi><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><msup><mi>cos</mi><mn>4</mn></msup><mi mathvariant=\"normal\">&theta;</mi><mo>&nbsp;</mo><mo>)</mo><mo>=</mo><msup><mrow><mo>(</mo><msup><mi>sin</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&theta;</mi><mo>+</mo><msup><mi>cos</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&theta;</mi><mo>)</mo></mrow><mn>2</mn></msup><mo>-</mo><mn>2</mn><msup><mi>sin</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&theta;</mi><mo>&nbsp;</mo><mo>.</mo><msup><mi>cos</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&theta;</mi><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mi>We</mi><mo>&nbsp;</mo><mi>know</mi><mo>&nbsp;</mo><mi>that</mi><mo>&nbsp;</mo><mo>(</mo><msup><mi>sin</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&theta;</mi><mo>+</mo><msup><mi>cos</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&theta;</mi><mo>)</mo><mo>=</mo><mn>1</mn><mspace linebreak=\"newline\"></mspace><msup><mn>1</mn><mn>2</mn></msup><mo>-</mo><mn>2</mn><mo>&times;</mo><mo>&nbsp;</mo><msup><mrow><mo>(</mo><mfrac><mn>1</mn><msqrt><mn>3</mn></msqrt></mfrac><mo>)</mo></mrow><mn>2</mn></msup><mo>=</mo><mfrac><mrow><mo>&nbsp;</mo><mn>1</mn></mrow><mn>3</mn></mfrac></math></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                    solution_hi: "<p>11<span style=\"font-family: Cambria Math;\">.(d)</span></p>\r\n<p><math style=\"font-family:Verdana\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#2346;&#2381;&#2352;&#2358;&#2381;&#2344;</mi><mo>&nbsp;</mo><mi>&#2325;&#2375;</mi><mo>&nbsp;</mo><mi>&#2309;&#2344;&#2369;&#2360;&#2366;&#2352;</mi><mo>,</mo><mspace linebreak=\"newline\"></mspace><mo>(</mo><msup><mi>sin</mi><mn>4</mn></msup><mi mathvariant=\"normal\">&theta;</mi><mo>+</mo><msup><mi>cos</mi><mn>4</mn></msup><mi mathvariant=\"normal\">&theta;</mi><mo>&nbsp;</mo><mo>)</mo><mo>=</mo><msup><mrow><mo>(</mo><msup><mi>sin</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&theta;</mi><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><msup><mi>cos</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&theta;</mi><mo>)</mo></mrow><mn>2</mn></msup><mo>-</mo><mn>2</mn><msup><mi>sin</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&theta;</mi><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>.</mo><msup><mi>cos</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&theta;</mi><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mi>&#2361;&#2350;</mi><mo>&nbsp;</mo><mi>&#2332;&#2366;&#2344;&#2340;&#2375;</mi><mo>&nbsp;</mo><mi>&#2361;&#2376;&#2306;</mi><mo>&nbsp;</mo><mi>&#2325;&#2367;</mi><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>(</mo><msup><mi>sin</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&theta;</mi><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><msup><mi>cos</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&theta;</mi><mo>)</mo><mo>=</mo><mn>1</mn><mspace linebreak=\"newline\"></mspace><msup><mn>1</mn><mn>2</mn></msup><mo>-</mo><mn>2</mn><mo>&times;</mo><msup><mrow><mo>(</mo><mfrac><mn>1</mn><msqrt><mn>3</mn></msqrt></mfrac><mo>)</mo></mrow><mn>2</mn></msup><mo>=</mo><mfrac><mrow><mo>&nbsp;</mo><mn>1</mn></mrow><mn>3</mn></mfrac></math></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">12</span><span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Cambria Math;\"> Simplify the given expression.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mfrac><mrow><mn>1</mn><mo>+</mo><mi>cos</mi><mo>&nbsp;</mo><mi>P</mi></mrow><mrow><mn>1</mn><mo>-</mo><mi>cos</mi><mo>&nbsp;</mo><mi>P</mi></mrow></mfrac></msqrt></math></span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">12</span><span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Cambria Math;\">&#2342;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2327;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2381;&#2351;&#2306;&#2332;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2352;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;&#2332;&#2367;&#2319;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mfrac><mrow><mn>1</mn><mo>+</mo><mi>cos</mi><mo>&nbsp;</mo><mi>P</mi></mrow><mrow><mn>1</mn><mo>-</mo><mi>cos</mi><mo>&nbsp;</mo><mi>P</mi></mrow></mfrac></msqrt></math></span></p>\n",
                    options_en: ["<p>cosec P - cot P</p>\n", "<p>sec P - tan P</p>\n", 
                                "<p>sec P + tan P</p>\n", "<p>cosec P + cot P</p>\n"],
                    options_hi: ["<p>cosec P- cot P</p>\n", "<p>sec P - tan P</p>\n",
                                "<p>sec P + tan P</p>\n", "<p>cosec P + cot P</p>\n"],
                    solution_en: "<p>12<span style=\"font-family: Cambria Math;\">.(d)</span></p>\r\n<p><span style=\"font-weight: 400;\">According to the question,</span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mfrac><mrow><mn>1</mn><mo>+</mo><mi>cos</mi><mo>&nbsp;</mo><mi>P</mi><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mo>(</mo><mn>1</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mi>cos</mi><mi>P</mi><mo>)</mo></mrow><mrow><mn>1</mn><mo>-</mo><mi>cos</mi><mo>&nbsp;</mo><mi>P</mi><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mo>(</mo><mn>1</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mi>cos</mi><mi>P</mi><mo>)</mo><mo>&nbsp;</mo></mrow></mfrac></msqrt><mo>=</mo><mfrac><mrow><mn>1</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mi>cos</mi><mi>P</mi></mrow><mrow><mi>S</mi><mi>i</mi><mi>n</mi><mi>P</mi></mrow></mfrac><mo>=</mo><mi>cos</mi><mi>e</mi><mi>c</mi><mi>P</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mi>c</mi><mi>o</mi><mi>t</mi><mo>&nbsp;</mo><mi>P</mi></math></p>\n",
                    solution_hi: "<p>12<span style=\"font-family: Cambria Math;\">.(d)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&#2346;&#2381;&#2352;&#2358;&#2381;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2309;&#2344;&#2369;&#2360;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\">,</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mfrac><mrow><mn>1</mn><mo>+</mo><mi>cos</mi><mo>&nbsp;</mo><mi>P</mi><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mo>(</mo><mn>1</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mi>cos</mi><mi>P</mi><mo>)</mo></mrow><mrow><mn>1</mn><mo>-</mo><mi>cos</mi><mo>&nbsp;</mo><mi>P</mi><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mo>(</mo><mn>1</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mi>cos</mi><mi>P</mi><mo>)</mo><mo>&nbsp;</mo></mrow></mfrac></msqrt><mo>=</mo><mfrac><mrow><mn>1</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mi>cos</mi><mi>P</mi></mrow><mrow><mi>S</mi><mi>i</mi><mi>n</mi><mi>P</mi></mrow></mfrac><mo>=</mo><mi>cos</mi><mi>e</mi><mi>c</mi><mi>P</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mi>c</mi><mi>o</mi><mi>t</mi><mo>&nbsp;</mo><mi>P</mi></math></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">13</span><span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Cambria Math;\">Find the value of </span><span style=\"font-family: Cambria Math;\">tan(</span><span style=\"font-family: Cambria Math;\">50 + &theta;</span><span style=\"font-family: Cambria Math;\">) - Cot(40 - &theta;</span><span style=\"font-family: Cambria Math;\">).</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">13</span><span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">tan(</span><span style=\"font-family: Cambria Math;\">50 + &theta;</span><span style=\"font-family: Cambria Math;\">) - Cot(40 - &theta;</span><span style=\"font-family: Cambria Math;\">) </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2332;&#2381;&#2334;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;&#2332;&#2367;&#2319;&#2404;</span></p>\n",
                    options_en: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p>-1</p>\n", 
                                "<p>0</p>\n", "<p>1</p>\n"],
                    options_hi: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p>-1</p>\n",
                                "<p>0</p>\n", "<p>1</p>\n"],
                    solution_en: "<p>13<span style=\"font-family: Cambria Math;\">.(c)</span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>tan</mi><mo>(</mo><mn>50</mn><mo>+</mo><mi>&theta;</mi><mo>)</mo><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mi>c</mi><mi>o</mi><mi>t</mi><mo>(</mo><mn>40</mn><mo>-</mo><mi>&theta;</mi><mo>)</mo><mspace linebreak=\"newline\"></mspace><mo>=</mo><mi>t</mi><mi>a</mi><mi>n</mi><mo>(</mo><mn>50</mn><mo>+</mo><mi>&theta;</mi><mo>)</mo><mo>-</mo><mi>c</mi><mi>o</mi><mi>t</mi><mo>[</mo><mn>90</mn><mo>-</mo><mo>(</mo><mn>50</mn><mo>+</mo><mi>&theta;</mi><mo>)</mo><mo>]</mo><mo>&nbsp;</mo><mo>{</mo><mrow><mi>c</mi><mi>o</mi><mi>t</mi><mo>(</mo><mn>90</mn><mo>-</mo><mi>&theta;</mi><mo>)</mo><mo>=</mo><mi>t</mi><mi>a</mi><mi>n</mi><mi>&theta;</mi><mo>}</mo></mrow><mspace linebreak=\"newline\"></mspace><mo>=</mo><mi>tan</mi><mo>(</mo><mn>50</mn><mo>+</mo><mi>&theta;</mi><mo>)</mo><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mi>tan</mi><mo>(</mo><mn>50</mn><mo>+</mo><mi>&theta;</mi><mo>)</mo><mspace linebreak=\"newline\"></mspace><mo>=</mo><mn>0</mn></math></p>\n",
                    solution_hi: "<p>13<span style=\"font-family: Cambria Math;\">.(c)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>tan</mi><mo>(</mo><mn>50</mn><mo>+</mo><mi>&theta;</mi><mo>)</mo><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mi>c</mi><mi>o</mi><mi>t</mi><mo>(</mo><mn>40</mn><mo>-</mo><mi>&theta;</mi><mo>)</mo><mspace linebreak=\"newline\"></mspace><mo>=</mo><mi>t</mi><mi>a</mi><mi>n</mi><mo>(</mo><mn>50</mn><mo>+</mo><mi>&theta;</mi><mo>)</mo><mo>-</mo><mi>c</mi><mi>o</mi><mi>t</mi><mo>[</mo><mn>90</mn><mo>-</mo><mo>(</mo><mn>50</mn><mo>+</mo><mi>&theta;</mi><mo>)</mo><mo>]</mo><mo>&nbsp;</mo><mo>{</mo><mrow><mi>c</mi><mi>o</mi><mi>t</mi><mo>(</mo><mn>90</mn><mo>-</mo><mi>&theta;</mi><mo>)</mo><mo>=</mo><mi>t</mi><mi>a</mi><mi>n</mi><mi>&theta;</mi><mo>}</mo></mrow><mspace linebreak=\"newline\"></mspace><mo>=</mo><mi>tan</mi><mo>(</mo><mn>50</mn><mo>+</mo><mi>&theta;</mi><mo>)</mo><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mi>tan</mi><mo>(</mo><mn>50</mn><mo>+</mo><mi>&theta;</mi><mo>)</mo><mspace linebreak=\"newline\"></mspace><mo>=</mo><mn>0</mn></math></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">14</span><span style=\"font-family: Cambria Math;\">.</span><span style=\"font-family: Cambria Math;\"> What is the value of (1 - sin&sup2;A) </span><span style=\"font-family: Cambria Math;\">cosec&sup2;A ?</span><span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Given A is an acute angle.</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">14</span><span style=\"font-family: Cambria Math;\">.</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> A </span><span style=\"font-family: Cambria Math;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2344;&#2381;&#2351;&#2370;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2379;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><mo>&nbsp;</mo><mn>1</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><msup><mi>sin</mi><mn>2</mn></msup><mo>&nbsp;</mo><mi>A</mi><mo>&nbsp;</mo><mo>)</mo><mo>&nbsp;</mo><mi>cos</mi><mi>e</mi><msup><mi>c</mi><mn>2</mn></msup><mo>&nbsp;</mo><mi>A</mi></math><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>c</mi><mi>o</mi><msup><mi>t</mi><mn>2</mn></msup><mi>A</mi></math><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>cos</mi><mn>2</mn></msup><mi>A</mi></math><span style=\"font-family: Cambria Math;\"> </span></p>\n", 
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>tan</mi><mn>2</mn></msup><mi>A</mi></math><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>sin</mi><mn>2</mn></msup><mi>A</mi></math></p>\n"],
                    options_hi: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>c</mi><mi>o</mi><msup><mi>t</mi><mn>2</mn></msup><mi>A</mi></math><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>cos</mi><mn>2</mn></msup><mi>A</mi></math><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>tan</mi><mn>2</mn></msup><mi>A</mi></math><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>sin</mi><mn>2</mn></msup><mi>A</mi></math></p>\n"],
                    solution_en: "<p>14<span style=\"font-family: Cambria Math;\">.(a)</span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><mn>1</mn><mo>-</mo><mo>&nbsp;</mo><msup><mi>sin</mi><mn>2</mn></msup><mi>A</mi><mo>)</mo><mo>&nbsp;</mo><mi>cos</mi><mi>e</mi><msup><mi>c</mi><mn>2</mn></msup><mi>A</mi><mspace linebreak=\"newline\"></mspace><mo>=</mo><mo>(</mo><msup><mi>cos</mi><mn>2</mn></msup><mi>A</mi><mo>)</mo><mo>&nbsp;</mo><mi>cos</mi><mi>e</mi><msup><mi>c</mi><mn>2</mn></msup><mi>A</mi><mspace linebreak=\"newline\"></mspace><mo>=</mo><mfrac><mrow><msup><mi>cos</mi><mn>2</mn></msup><mi>A</mi></mrow><mrow><msup><mi>sin</mi><mn>2</mn></msup><mi>A</mi></mrow></mfrac><mo>=</mo><mi>c</mi><mi>o</mi><msup><mi>t</mi><mn>2</mn></msup><mi>A</mi></math></p>\n",
                    solution_hi: "<p>14<span style=\"font-family: Cambria Math;\">.(a)</span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><mn>1</mn><mo>-</mo><mo>&nbsp;</mo><msup><mi>sin</mi><mn>2</mn></msup><mi>A</mi><mo>)</mo><mo>&nbsp;</mo><mi>cos</mi><mi>e</mi><msup><mi>c</mi><mn>2</mn></msup><mi>A</mi><mspace linebreak=\"newline\"></mspace><mo>=</mo><mo>(</mo><msup><mi>cos</mi><mn>2</mn></msup><mi>A</mi><mo>)</mo><mo>&nbsp;</mo><mi>cos</mi><mi>e</mi><msup><mi>c</mi><mn>2</mn></msup><mi>A</mi><mspace linebreak=\"newline\"></mspace><mo>=</mo><mfrac><mrow><msup><mi>cos</mi><mn>2</mn></msup><mi>A</mi></mrow><mrow><msup><mi>sin</mi><mn>2</mn></msup><mi>A</mi></mrow></mfrac><mo>=</mo><mi>c</mi><mi>o</mi><msup><mi>t</mi><mn>2</mn></msup><mi>A</mi></math></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "misc",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">15</span><span style=\"font-family: Cambria Math;\">.</span><span style=\"font-family: Cambria Math;\"> If Tan 15&deg;= 2 -<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math> </span><span style=\"font-family: Cambria Math;\">, then the value of Tan 15&deg; Cot 75&deg; + Tan 75&deg; Cot 15&deg; is:</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">15</span><span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Cambria Math;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">Tan 15&deg;= 2 - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math></span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> Tan 15&deg; Cot 75&deg; + Tan 75&deg; Cot 15&deg; </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">? </span></p>\n",
                    options_en: ["<p>6</p>\n", "<p>10</p>\n", 
                                "<p>8</p>\n", "<p>14</p>\n"],
                    options_hi: ["<p>6</p>\n", "<p><span style=\"font-family: Cambria Math;\">10 </span></p>\n",
                                "<p>8</p>\n", "<p>14</p>\n"],
                    solution_en: "<p>15<span style=\"font-family: Cambria Math;\">.(d)</span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>T</mi><mi>a</mi><mi>n</mi><mo>&nbsp;</mo><mn>15</mn><mo>&deg;</mo><mo>=</mo><mo>&nbsp;</mo><mn>2</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><msqrt><mn>3</mn></msqrt><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&rArr;</mo><mi>C</mi><mi>o</mi><mi>t</mi><mo>&nbsp;</mo><mn>15</mn><mo>&deg;</mo><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mn>2</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><msqrt><mo>&nbsp;</mo><mn>3</mn></msqrt><mspace linebreak=\"newline\"></mspace><mi>T</mi><mi>a</mi><mi>n</mi><mo>&nbsp;</mo><mn>15</mn><mo>&deg;</mo><mo>&nbsp;</mo><mi>C</mi><mi>o</mi><mi>t</mi><mo>&nbsp;</mo><mn>75</mn><mo>&deg;</mo><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mi>T</mi><mi>a</mi><mi>n</mi><mo>&nbsp;</mo><mn>75</mn><mo>&deg;</mo><mo>&nbsp;</mo><mi>C</mi><mi>o</mi><mi>t</mi><mo>&nbsp;</mo><mn>15</mn><mo>&deg;</mo><mspace linebreak=\"newline\"></mspace><mo>=</mo><mo>&nbsp;</mo><mi>T</mi><mi>a</mi><mi>n</mi><mo>&nbsp;</mo><mn>15</mn><mo>&deg;</mo><mo>&nbsp;</mo><mi>C</mi><mi>o</mi><mi>t</mi><mo>(</mo><mn>90</mn><mo>&deg;</mo><mo>&nbsp;</mo><mo>-</mo><mn>15</mn><mo>&deg;</mo><mo>)</mo><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mi>T</mi><mi>a</mi><mi>n</mi><mo>&nbsp;</mo><mo>(</mo><mn>90</mn><mo>&deg;</mo><mo>-</mo><mo>&nbsp;</mo><mn>15</mn><mo>&deg;</mo><mo>)</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mi>C</mi><mi>o</mi><mi>t</mi><mo>&nbsp;</mo><mn>15</mn><mo>&deg;</mo><mspace linebreak=\"newline\"></mspace><mo>=</mo><mo>&nbsp;</mo><mi>T</mi><mi>a</mi><mi>n</mi><mo>&nbsp;</mo><mn>15</mn><mo>&deg;</mo><mo>&nbsp;</mo><mi>T</mi><mi>a</mi><mi>n</mi><mo>&nbsp;</mo><mn>15</mn><mo>&deg;</mo><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mi>C</mi><mi>o</mi><mi>t</mi><mo>&nbsp;</mo><mn>15</mn><mo>&deg;</mo><mo>&nbsp;</mo><mi>C</mi><mi>o</mi><mi>t</mi><mo>&nbsp;</mo><mn>15</mn><mo>&deg;</mo><mspace linebreak=\"newline\"></mspace><mo>=</mo><mo>&nbsp;</mo><msup><mrow><mo>(</mo><mi>T</mi><mi>a</mi><mi>n</mi><mo>&nbsp;</mo><mn>15</mn><mo>&deg;</mo><mo>&nbsp;</mo><mo>)</mo></mrow><mn>2</mn></msup><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><msup><mrow><mo>(</mo><mi>C</mi><mi>o</mi><mi>t</mi><mo>&nbsp;</mo><mn>15</mn><mo>&deg;</mo><mo>&nbsp;</mo><mo>)</mo></mrow><mn>2</mn></msup><mspace linebreak=\"newline\"></mspace><mo>=</mo><mo>&nbsp;</mo><msup><mrow><mo>(</mo><mn>2</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><msqrt><mn>3</mn></msqrt><mo>&nbsp;</mo><mo>)</mo></mrow><mn>2</mn></msup><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><msup><mrow><mo>(</mo><mn>2</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><msqrt><mn>3</mn></msqrt><mo>&nbsp;</mo><mo>)</mo></mrow><mn>2</mn></msup><mspace linebreak=\"newline\"></mspace><mo>=</mo><mo>&nbsp;</mo><mn>4</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>3</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>4</mn><msqrt><mn>3</mn></msqrt><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>4</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>3</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>4</mn><msqrt><mn>3</mn></msqrt><mspace linebreak=\"newline\"></mspace><mo>=</mo><mo>&nbsp;</mo><mn>14</mn></math></p>\n",
                    solution_hi: "<p>15<span style=\"font-family: Cambria Math;\">.(d)</span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>T</mi><mi>a</mi><mi>n</mi><mo>&nbsp;</mo><mn>15</mn><mo>&deg;</mo><mo>=</mo><mo>&nbsp;</mo><mn>2</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><msqrt><mn>3</mn></msqrt><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&rArr;</mo><mi>C</mi><mi>o</mi><mi>t</mi><mo>&nbsp;</mo><mn>15</mn><mo>&deg;</mo><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mn>2</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><msqrt><mo>&nbsp;</mo><mn>3</mn></msqrt><mspace linebreak=\"newline\"></mspace><mi>T</mi><mi>a</mi><mi>n</mi><mo>&nbsp;</mo><mn>15</mn><mo>&deg;</mo><mo>&nbsp;</mo><mi>C</mi><mi>o</mi><mi>t</mi><mo>&nbsp;</mo><mn>75</mn><mo>&deg;</mo><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mi>T</mi><mi>a</mi><mi>n</mi><mo>&nbsp;</mo><mn>75</mn><mo>&deg;</mo><mo>&nbsp;</mo><mi>C</mi><mi>o</mi><mi>t</mi><mo>&nbsp;</mo><mn>15</mn><mo>&deg;</mo><mspace linebreak=\"newline\"></mspace><mo>=</mo><mo>&nbsp;</mo><mi>T</mi><mi>a</mi><mi>n</mi><mo>&nbsp;</mo><mn>15</mn><mo>&deg;</mo><mo>&nbsp;</mo><mi>C</mi><mi>o</mi><mi>t</mi><mo>(</mo><mn>90</mn><mo>&deg;</mo><mo>&nbsp;</mo><mo>-</mo><mn>15</mn><mo>&deg;</mo><mo>)</mo><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mi>T</mi><mi>a</mi><mi>n</mi><mo>&nbsp;</mo><mo>(</mo><mn>90</mn><mo>&deg;</mo><mo>-</mo><mo>&nbsp;</mo><mn>15</mn><mo>&deg;</mo><mo>)</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mi>C</mi><mi>o</mi><mi>t</mi><mo>&nbsp;</mo><mn>15</mn><mo>&deg;</mo><mspace linebreak=\"newline\"></mspace><mo>=</mo><mo>&nbsp;</mo><mi>T</mi><mi>a</mi><mi>n</mi><mo>&nbsp;</mo><mn>15</mn><mo>&deg;</mo><mo>&nbsp;</mo><mi>T</mi><mi>a</mi><mi>n</mi><mo>&nbsp;</mo><mn>15</mn><mo>&deg;</mo><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mi>C</mi><mi>o</mi><mi>t</mi><mo>&nbsp;</mo><mn>15</mn><mo>&deg;</mo><mo>&nbsp;</mo><mi>C</mi><mi>o</mi><mi>t</mi><mo>&nbsp;</mo><mn>15</mn><mo>&deg;</mo><mspace linebreak=\"newline\"></mspace><mo>=</mo><mo>&nbsp;</mo><msup><mrow><mo>(</mo><mi>T</mi><mi>a</mi><mi>n</mi><mo>&nbsp;</mo><mn>15</mn><mo>&deg;</mo><mo>&nbsp;</mo><mo>)</mo></mrow><mn>2</mn></msup><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><msup><mrow><mo>(</mo><mi>C</mi><mi>o</mi><mi>t</mi><mo>&nbsp;</mo><mn>15</mn><mo>&deg;</mo><mo>&nbsp;</mo><mo>)</mo></mrow><mn>2</mn></msup><mspace linebreak=\"newline\"></mspace><mo>=</mo><mo>&nbsp;</mo><msup><mrow><mo>(</mo><mn>2</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><msqrt><mn>3</mn></msqrt><mo>&nbsp;</mo><mo>)</mo></mrow><mn>2</mn></msup><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><msup><mrow><mo>(</mo><mn>2</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><msqrt><mn>3</mn></msqrt><mo>&nbsp;</mo><mo>)</mo></mrow><mn>2</mn></msup><mspace linebreak=\"newline\"></mspace><mo>=</mo><mo>&nbsp;</mo><mn>4</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>3</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>4</mn><msqrt><mn>3</mn></msqrt><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>4</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>3</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>4</mn><msqrt><mn>3</mn></msqrt><mspace linebreak=\"newline\"></mspace><mo>=</mo><mo>&nbsp;</mo><mn>14</mn></math></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>