<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">9:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 25</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">25</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 9 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["7"] = {
                name: "English",
                start: 0,
                end: 23
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="7">English</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 24,
                end: 24
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "7",
                    question_en: " <p>1. </span><span style=\"font-family:Cambria Math\">Select the most appropriate option to fill in the blank.</span></p> <p><span style=\"font-family:Cambria Math\">She raced ______ towards the train.</span></p>",
                    question_hi: "",
                    options_en: [" <p> crazily        </span><span style=\"font-family:Cambria Math\">  </span></p>", " <p> berserk       </span></p>", 
                                " <p> hectically     </span><span style=\"font-family:Cambria Math\"> </span></p>", " <p> frantically</span></p>"],
                    options_hi: ["", "",
                                "", ""],
                    solution_en: " <p>1.(d) </span><span style=\"font-family:Cambria Math\">Frantically means in a</span></p> <p><span style=\"font-family:Cambria Math\">hurried, excited, or disorganized manner.</span></p>",
                    solution_hi: " <p>1.(d) </span><span style=\"font-family:Cambria Math\">Frantically means in a</span></p> <p><span style=\"font-family:Cambria Math\">hurried, excited, or disorganized manner.</span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "7",
                    question_en: "<p>2. <span style=\"font-family: Cambria Math;\">Complete the sentence with an appropriate synonym of the word mentioned in the bracket.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Diseases like </span><span style=\"font-family: Cambria Math;\">Verminosis</span><span style=\"font-family: Cambria Math;\"> and Mastitis only ___________ (</span><strong><span style=\"font-family: Cambria Math;\">damage</span></strong><span style=\"font-family: Cambria Math;\">) domestic cattle.</span></p>\n",
                    question_hi: "",
                    options_en: ["<p>delight</p>\n", "<p>afflict</p>\n", 
                                "<p>relieve</p>\n", "<p>assist</p>\n"],
                    options_hi: ["", "",
                                "", ""],
                    solution_en: "<p>2.(b) <strong>Afflict </strong><span style=\"font-family: Cambria Math;\">means to cause pain or </span><span style=\"font-family: Cambria Math;\">trouble.So</span><span style=\"font-family: Cambria Math;\"> option b will be the most appropriate answer.</span></p>\n",
                    solution_hi: "<p>2.(b) <strong>Afflict </strong><span style=\"font-family: Cambria Math;\">means to cause pain or </span><span style=\"font-family: Cambria Math;\">trouble.So</span><span style=\"font-family: Cambria Math;\"> option b will be the most appropriate answer.</span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "7",
                    question_en: "<p>3. <span style=\"font-family: Cambria Math;\">Select the most appropriate synonym of the highlighted word.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">The </span><span style=\"text-decoration: underline;\"><strong><span style=\"font-family: Cambria Math;\">hapless</span></strong></span><span style=\"font-family: Cambria Math;\"><span style=\"text-decoration: underline;\"><strong> </strong></span>incident was a result of sheer negligence by the authorities.</span></p>\n",
                    question_hi: "",
                    options_en: ["<p>triumphant<span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p>joyful</p>\n", 
                                "<p>grateful<span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p>ill- fated</p>\n"],
                    options_hi: ["", "",
                                "", ""],
                    solution_en: "<p>3.(d) <strong>Hapless </strong><span style=\"font-family: Cambria Math;\">means unfortunate. So option d, </span><span style=\"font-family: Cambria Math;\">ill fated</span><span style=\"font-family: Cambria Math;\"> will be the most appropriate answer.</span><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                    solution_hi: "<p>3.(d) <strong>Hapless </strong><span style=\"font-family: Cambria Math;\">means unfortunate. So option d, </span><span style=\"font-family: Cambria Math;\">ill fated</span><span style=\"font-family: Cambria Math;\"> will be the most appropriate answer.</span><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "7",
                    question_en: " <p>4. </span><span style=\"font-family:Cambria Math\">Select the most appropriate option to fill in the blank.</span></p> <p><span style=\"font-family:Cambria Math\">We have _____ long weekend ahead.</span></p>",
                    question_hi: "",
                    options_en: [" <p> an</span><span style=\"font-family:Cambria Math\">                </span></p>", " <p> one</span></p>", 
                                " <p> a</span><span style=\"font-family:Cambria Math\">               </span></p>", " <p> the</span></p>"],
                    options_hi: ["", "",
                                "", ""],
                    solution_en: " <p>4.(c) </span><span style=\"font-family:Cambria Math\">The word long begins with a consonant sound so article “a” will be used.</span></p>",
                    solution_hi: " <p>4.(c) </span><span style=\"font-family:Cambria Math\">The word long begins with a consonant sound so article “a” will be used.</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "7",
                    question_en: "<p>5. <span style=\"font-family: Cambria Math;\">Spot the error in the given sentence and select the correct sentence from the options given.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">There were more than thirty delegates who attend the training </span><span style=\"font-family: Cambria Math;\">programme</span><span style=\"font-family: Cambria Math;\"> last summer. </span></p>\n",
                    question_hi: "",
                    options_en: ["<p>There are more than thirty delegates who attend the training <span style=\"font-family: Cambria Math;\">programme</span><span style=\"font-family: Cambria Math;\"> last summer</span></p>\n", "<p>There were more than thirty delegates who attends the training <span style=\"font-family: Cambria Math;\">programme</span><span style=\"font-family: Cambria Math;\"> last summer. </span></p>\n", 
                                "<p>There was more than thirty delegates who attend the training <span style=\"font-family: Cambria Math;\">programme</span><span style=\"font-family: Cambria Math;\"> last summer </span></p>\n", "<p>There were more than thirty delegates who attended the training <span style=\"font-family: Cambria Math;\">programme</span><span style=\"font-family: Cambria Math;\"> last summer. </span></p>\n"],
                    options_hi: ["", "",
                                "", ""],
                    solution_en: "<p><strong>5.(d)</strong></p>\r\n<p><span style=\"font-weight: 400;\">There are more than thirty delegates who </span><strong>attend</strong><span style=\"font-weight: 400;\"> the training&nbsp; programme last summer.(Incorrect tense)</span></p>\r\n<p><span style=\"font-weight: 400;\">There were more than thirty delegates</span><strong> who attends </strong><span style=\"font-weight: 400;\">the training programme last summer. (Incorrect tense)</span></p>\r\n<p><span style=\"font-weight: 400;\">There </span><strong>was more than thirty delegate</strong><span style=\"font-weight: 400;\">s who attend the training&nbsp; programme last summer.( Incorrect verb)&nbsp;</span></p>\r\n<p><span style=\"font-weight: 400;\">There were more than thirty delegates who attended the training programme last. (Correct)</span></p>\n",
                    solution_hi: "<p><strong>5.(d)</strong></p>\r\n<p><span style=\"font-weight: 400;\">There are more than thirty delegates who </span><strong>attend</strong><span style=\"font-weight: 400;\"> the training&nbsp; programme last summer.(Incorrect tense)</span></p>\r\n<p><span style=\"font-weight: 400;\">There were more than thirty delegates</span><strong> who attends </strong><span style=\"font-weight: 400;\">the training programme last summer. (Incorrect tense)</span></p>\r\n<p><span style=\"font-weight: 400;\">There </span><strong>was more than thirty delegate</strong><span style=\"font-weight: 400;\">s who attend the training&nbsp; programme last summer.( Incorrect verb)&nbsp;</span></p>\r\n<p><span style=\"font-weight: 400;\">There were more than thirty delegates who attended the training programme last. (Correct)</span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "7",
                    question_en: " <p>6.</span><span style=\"font-family:Cambria Math\">Select the sentence which has no spelling error.</span></p>",
                    question_hi: "",
                    options_en: [" <p> There are more than 3500 types of </span><span style=\"font-family:Cambria Math\">moskuitoes</span><span style=\"font-family:Cambria Math\"> in the world.</span></p>", " <p> The common reaction to </span><span style=\"font-family:Cambria Math\">mosquiteo’s</span><span style=\"font-family:Cambria Math\">    biting is itching.</span></p>", 
                                " <p> Female </span><span style=\"font-family:Cambria Math\">mosqueteos</span><span style=\"font-family:Cambria Math\"> have a longer life than their male mates.</span></p>", " <p> Like all flies, mosquitoes also have four stages in their life.</span></p> <p><span style=\"font-family:Cambria Math\">      </span></p>"],
                    options_hi: ["", "",
                                "", ""],
                    solution_en: " <p>6.(d) </span><span style=\"font-family:Cambria Math\">Mosquitoes is the correct </span></p> <p><span style=\"font-family:Cambria Math\">spelling.It</span><span style=\"font-family:Cambria Math\"> has been correctly spelt in </span></p> <p><span style=\"font-family:Cambria Math\">option d only.</span></p>",
                    solution_hi: " <p>6.(d) </span><span style=\"font-family:Cambria Math\">Mosquitoes is the correct </span></p> <p><span style=\"font-family:Cambria Math\">spelling.It</span><span style=\"font-family:Cambria Math\"> has been correctly spelt in </span></p> <p><span style=\"font-family:Cambria Math\">option d only.</span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "7",
                    question_en: " <p>7. </span><span style=\"font-family:Cambria Math\">Select the most appropriate option that can substitute the underlined segment in the given sentence.</span></p> <p><span style=\"font-family:Cambria Math\">Simba and snowy are cute than the other puppies.</span></p>",
                    question_hi: "",
                    options_en: [" <p> more cute</span></p>", " <p> cuter</span></p>", 
                                " <p> most cute</span></p>", " <p> cutest</span></p>"],
                    options_hi: ["", "",
                                "", ""],
                    solution_en: " <p>7.(b) </span><span style=\"font-family:Cambria Math\">There is a comparison with other puppies so comparative degree will be used and cuter will be </span><span style=\"font-family:Cambria Math\">used.So</span><span style=\"font-family:Cambria Math\"> option b is the most appropriate answer.</span></p>",
                    solution_hi: " <p>7.(b) </span><span style=\"font-family:Cambria Math\">There is a comparison with other puppies so comparative degree will be used and cuter will be </span><span style=\"font-family:Cambria Math\">used.So</span><span style=\"font-family:Cambria Math\"> option b is the most appropriate answer.</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "7",
                    question_en: "<p>8. <span style=\"font-family: Cambria Math;\">Select the most appropriate option that can substitute the underlined words in the given sentence.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Upon hearing the great achievements of the historic character, he was in</span><span style=\"font-family: Cambria Math;\"> <span style=\"text-decoration: underline;\"><strong>a state of awed admiration and respect</strong></span>.</span></p>\n",
                    question_hi: "",
                    options_en: ["<p>wonder</p>\n", "<p>awed</p>\n", 
                                "<p>wondering<span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p>wonderment</p>\n"],
                    options_hi: ["", "",
                                "", ""],
                    solution_en: "<p>8.(d) <span style=\"font-family: Cambria Math;\">A state of awed admiration and respect - wonderment</span></p>\n",
                    solution_hi: "<p>8.(d) <span style=\"font-family: Cambria Math;\">A state of awed admiration and respect - wonderment</span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "7",
                    question_en: "<p>9. <span style=\"font-family: Cambria Math;\">Select the most appropriate meaning of the given idiom.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Snail&rsquo;s pace</span></strong></p>\n",
                    question_hi: "",
                    options_en: ["<p>Very fast<span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p>Very slow</p>\n", 
                                "<p>Very active<span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p>Very sharp</p>\n"],
                    options_hi: ["", "",
                                "", ""],
                    solution_en: "<p>9.(b) <span style=\"font-family: Cambria Math;\">Snail&rsquo;s pace - Very slow</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Eg</span><span style=\"font-family: Cambria Math;\">- The construction of the highway took place at snail&rsquo;s pace.</span></p>\n",
                    solution_hi: "<p>9.(b) <span style=\"font-family: Cambria Math;\">Snail&rsquo;s pace - Very slow</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Eg</span><span style=\"font-family: Cambria Math;\">- The construction of the highway took place at snail&rsquo;s pace.</span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "7",
                    question_en: " <p>10. </span><span style=\"font-family:Cambria Math\">Select the most appropriate option to fill in the blank.</span></p> <p><span style=\"font-family:Cambria Math\">Hopefully, things will return to</span></p> <p><span style=\"font-family:Cambria Math\">normal in the ________.</span></p>",
                    question_hi: "",
                    options_en: [" <p> past</span></p>", " <p> future</span></p>", 
                                " <p> present</span></p>", " <p> earlier</span></p>"],
                    options_hi: ["", "",
                                "", ""],
                    solution_en: " <p>10.(b) </span><span style=\"font-family:Cambria Math\">The helping verb “will” has been used in the sentence. So “future” will be used.</span></p>",
                    solution_hi: " <p>10.(b) </span><span style=\"font-family:Cambria Math\">The helping verb “will” has been used in the sentence. So “future” will be used.</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "7",
                    question_en: "<p>11. <span style=\"font-family: Cambria Math;\">Select the option that expresses the given sentence in passive voice.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">The teacher gave the question paper.</span></p>\n",
                    question_hi: "",
                    options_en: ["<p>The question paper was given by the teacher.</p>\n", "<p>The question paper was being given by the teacher.</p>\n", 
                                "<p>The question paper was gave by the teacher.</p>\n", "<p>The question paper has been given by the teacher.</p>\n"],
                    options_hi: ["", "",
                                "", ""],
                    solution_en: "<p><strong>11.(a)</strong></p>\r\n<p><span style=\"font-weight: 400;\">The question paper was given by the teacher.(Correct)</span></p>\r\n<p><span style=\"font-weight: 400;\">The question paper </span><strong>was being given</strong><span style=\"font-weight: 400;\"> by the teacher. (Incorrect tense)</span></p>\r\n<p><span style=\"font-weight: 400;\">The question paper </span><strong>was gave</strong><span style=\"font-weight: 400;\"> by the teacher.(Incorrect tense)</span></p>\r\n<p><span style=\"font-weight: 400;\">The question paper </span><strong>has been given</strong><span style=\"font-weight: 400;\"> by the teacher.(Incorrect tense)</span></p>\n",
                    solution_hi: "<p><strong>11.(a)</strong></p>\r\n<p><span style=\"font-weight: 400;\">The question paper was given by the teacher.(Correct)</span></p>\r\n<p><span style=\"font-weight: 400;\">The question paper </span><strong>was being given</strong><span style=\"font-weight: 400;\"> by the teacher. (Incorrect tense)</span></p>\r\n<p><span style=\"font-weight: 400;\">The question paper </span><strong>was gave</strong><span style=\"font-weight: 400;\"> by the teacher.(Incorrect tense)</span></p>\r\n<p><span style=\"font-weight: 400;\">The question paper </span><strong>has been given</strong><span style=\"font-weight: 400;\"> by the teacher.(Incorrect tense)</span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "7",
                    question_en: " <p>12. </span><span style=\"font-family:Cambria Math\">Select the most appropriate option to fill in the blank.</span></p> <p><span style=\"font-family:Cambria Math\">The young king tried to speak, but his tongue seemed to ________ the roof of his mouth, and his lips refused to move.</span></p>",
                    question_hi: "",
                    options_en: [" <p> divide to</span></p>", " <p> cleave to</span></p>", 
                                " <p> link to</span></p>", " <p> leave to</span></p>"],
                    options_hi: ["", "",
                                "", ""],
                    solution_en: " <p>12.(b) </span><span style=\"font-family:Cambria Math\">Cleave to means to stick closely to something. So option b is the most appropriate answer.</span></p>",
                    solution_hi: " <p>12.(b) </span><span style=\"font-family:Cambria Math\">Cleave to means to stick closely to something. So option b is the most appropriate answer.</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "7",
                    question_en: "<p>13. <span style=\"font-family: Cambria Math;\">Select the option that can be used as a one-word substitute for the underlined group of words.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Ipshita</span><span style=\"font-family: Cambria Math;\"> <span style=\"text-decoration: underline;\"><strong>knows many languages</strong></span></span><span style=\"font-family: Cambria Math;\"> like French, Dutch and English, and she speaks them fluently.</span></p>\n",
                    question_hi: "",
                    options_en: ["<p>Translator</p>\n", "<p>Polyglot</p>\n", 
                                "<p>Bureaucrat</p>\n", "<p>Prodigy</p>\n"],
                    options_hi: ["", "",
                                "", ""],
                    solution_en: "<p>13.(b) <span style=\"font-family: Cambria Math;\">Knows many languages</span><span style=\"font-family: Cambria Math;\">- <strong>Polyglot</strong></span></p>\n",
                    solution_hi: "<p>13.(b) <span style=\"font-family: Cambria Math;\">Knows many languages</span><span style=\"font-family: Cambria Math;\">- <strong>Polyglot</strong></span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "7",
                    question_en: "<p>14. <span style=\"font-family: Cambria Math;\">Identify the option that arranges the given parts in the correct order to form a meaningful and coherent paragraph.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">a. Jumped off the bus in front of the old woman&rsquo;s home</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">b. It was mid-morning and a very cold, bright day</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">c. Holding a potted planted before her, a girl of fourteen</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">d. On the outskirts of town</span></p>\n",
                    question_hi: "",
                    options_en: ["<p><span style=\"font-family: Cambria Math;\">a,b,c,d</span><span style=\"font-family: Cambria Math;\">,</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">a,d,c,b</span><span style=\"font-family: Cambria Math;\">,</span></p>\n", 
                                "<p><span style=\"font-family: Cambria Math;\">b,c,a,d</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">c,a,b,d</span></p>\n"],
                    options_hi: ["", "",
                                "", ""],
                    solution_en: "<p>14.(c) <strong>The whole sentence will be</strong> - <span style=\"font-family: Cambria Math;\"> It was mid-morning and a very cold, bright day, holding a potted planted before her, a girl of fourteen jumped off the bus in front of the old woman&rsquo;s home , on the outskirts of town.</span></p>\n",
                    solution_hi: "<p>14.(c) <strong>The whole sentence will be</strong> - <span style=\"font-family: Cambria Math;\"> It was mid-morning and a very cold, bright day, holding a potted planted before her, a girl of fourteen jumped off the bus in front of the old woman&rsquo;s home , on the outskirts of town.</span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "7",
                    question_en: "<p>15. <span style=\"font-family: Cambria Math;\">Select the INCORRECTLY spelt word.</span></p>\n",
                    question_hi: "",
                    options_en: ["<p><span style=\"font-family: Cambria Math;\">Knowleg</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p>Marinate</p>\n", 
                                "<p>College</p>\n", "<p>Jocular</p>\n"],
                    options_hi: ["", "",
                                "", ""],
                    solution_en: "<p>15.(a) <span style=\"font-family: Cambria Math;\">Knowledge is the correct spelling.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Marinate - meat</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Jocular - humorous or playful</span></p>\n",
                    solution_hi: "<p>15.(a) <span style=\"font-family: Cambria Math;\">Knowledge is the correct spelling.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Marinate - meat</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Jocular - humorous or playful</span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "16",
                    section: "7",
                    question_en: "<p>16. <span style=\"font-family: Cambria Math;\">From among the words given in bold, select the </span><strong><span style=\"font-family: Cambria Math;\">INCORRECTLY </span></strong><span style=\"font-family: Cambria Math;\">spelt word in the following sentence.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">The streets are crowded with tribal </span><strong><span style=\"font-family: Cambria Math;\">urchins </span></strong><span style=\"font-family: Cambria Math;\">in tattered jackets, </span><strong><span style=\"font-family: Cambria Math;\">bowlegged </span></strong><span style=\"font-family: Cambria Math;\">donkeys hauling firewood, steep mountain trails, and occasional </span><strong><span style=\"font-family: Cambria Math;\">palanquins </span></strong><span style=\"font-family: Cambria Math;\">for arthritic </span><strong><span style=\"font-family: Cambria Math;\">bussinessman</span></strong><span style=\"font-family: Cambria Math;\">.</span></p>\n",
                    question_hi: "",
                    options_en: ["<p>bowlegged<span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p>palanquins</p>\n", 
                                "<p>urchins<span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><span style=\"font-family: Cambria Math;\">bussinessman</span></p>\n"],
                    options_hi: ["", "",
                                "", ""],
                    solution_en: "<p>16.(d) <span style=\"font-family: Cambria Math;\">Businessman</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">is the correct spelling.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">bowlegged - a leg bowed outward at or below the knee</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">palanquins - a passenger conveyance, usually for one person</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">urchins - a young child who is poorly or raggedly dressed</span></p>\r\n<p>&nbsp;</p>\n",
                    solution_hi: "<p>16.(d) <span style=\"font-family: Cambria Math;\">Businessman</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">is the correct spelling.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">bowlegged - a leg bowed outward at or below the knee</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">palanquins - a passenger conveyance, usually for one person</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">urchins - a young child who is poorly or raggedly dressed</span></p>\r\n<p>&nbsp;</p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "17",
                    section: "7",
                    question_en: "<p>17. <span style=\"font-family: Cambria Math;\">Select the most appropriate meaning of the highlighted idiom.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">The employee took the manager&rsquo;s remark with </span><span style=\"font-family: Cambria Math;\">a <strong><span style=\"text-decoration: underline;\">pinch of salt</span></strong></span></p>\n",
                    question_hi: "",
                    options_en: ["<p>Doubt the truth or value of something</p>\n", "<p>Distasteful and boring</p>\n", 
                                "<p>Going for the truth</p>\n", "<p>Ever ready to condemn someone</p>\n"],
                    options_hi: ["", "",
                                "", ""],
                    solution_en: "<p>17.(a) <span style=\"font-family: Cambria Math;\">With a pinch of salt - Doubt the truth or value of something</span></p>\n",
                    solution_hi: "<p>17.(a) <span style=\"font-family: Cambria Math;\">With a pinch of salt - Doubt the truth or value of something</span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "18",
                    section: "7",
                    question_en: "<p>18. <span style=\"font-family: Cambria Math;\">Select the most appropriate option to fill in the blank.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Any resemblance of a character in this book to a living person is ______ incidental.</span></p>\n",
                    question_hi: "",
                    options_en: ["<p>absolutely<span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p>naturally</p>\n", 
                                "<p>purely<span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p>thoroughly</p>\n"],
                    options_hi: ["", "",
                                "", ""],
                    solution_en: "<p>18.(c) <span style=\"font-family: Cambria Math;\">The term &ldquo;</span><strong><span style=\"font-family: Cambria Math;\">purely </span></strong></p>\r\n<p><span style=\"font-family: Cambria Math;\">incidental&rdquo;</span><span style=\"font-family: Cambria Math;\"> implies that something is occurring merely by chance or without intention</span></p>\n",
                    solution_hi: "<p>18.(c) <span style=\"font-family: Cambria Math;\">The term &ldquo;</span><strong><span style=\"font-family: Cambria Math;\">purely </span></strong></p>\r\n<p><span style=\"font-family: Cambria Math;\">incidental&rdquo;</span><span style=\"font-family: Cambria Math;\"> implies that something is occurring merely by chance or without intention</span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "19",
                    section: "7",
                    question_en: "<p>19. <span style=\"font-family: Cambria Math;\">Select the most appropriate meaning of the given idiom.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">The movers and shakers</span></strong></p>\n",
                    question_hi: "",
                    options_en: ["<p>People who have a lot of power and influence</p>\n", "<p>Transportation company</p>\n", 
                                "<p><span style=\"font-family: Cambria Math;\">Labourers</span></p>\n", "<p>Travelling agency</p>\n"],
                    options_hi: ["", "",
                                "", ""],
                    solution_en: "<p>19.(a) <span style=\"font-family: Cambria Math;\">The movers and shakers- People who have a lot of power and influence</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Eg</span><span style=\"font-family: Cambria Math;\">- He\'s one of the movers and shakers in the decision making process of the company</span></p>\n",
                    solution_hi: "<p>19.(a) <span style=\"font-family: Cambria Math;\">The movers and shakers- People who have a lot of power and influence</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Eg</span><span style=\"font-family: Cambria Math;\">- He\'s one of the movers and shakers in the decision making process of the company</span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "20",
                    section: "7",
                    question_en: "<p>20. <span style=\"font-family: Cambria Math;\">Select the <strong>INCORRECTLY </strong>spelt word.</span></p>\n",
                    question_hi: "",
                    options_en: ["<p><span style=\"font-family: Cambria Math;\">Appetight</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p>Reliance</p>\n", 
                                "<p>Success</p>\n", "<p>Confidence</p>\n"],
                    options_hi: ["", "",
                                "", ""],
                    solution_en: "<p>20.(a) <span style=\"font-family: Cambria Math;\">Appetite</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">is the correct spelling.</span></p>\n",
                    solution_hi: "<p>20.(a) <span style=\"font-family: Cambria Math;\">Appetite</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">is the correct spelling.</span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "21",
                    section: "7",
                    question_en: " <p>21. </span><span style=\"font-family:Cambria Math\">In the following passage some words have been deleted. Fill in the blanks with the help of the alternatives given. Select the most appropriate option for each number. </span></p> <p> _________ areas are also known as the ’countryside’ or a ‘village’ in India. It has a very low population (22) _________. In rural areas, agriculture is the chief source of livelihood along with fishing, cottage (23) ___________, pottery etc. The (24) ___________ to discover the real rural India still continues in great earnest. Almost (25) ___________ economic agency today has a definition of rural India.</span></p> <p><span style=\"font-family:Cambria Math\">Select the most appropriate option to fill blank 21.</span></p>",
                    question_hi: "",
                    options_en: [" <p> Ruler</span><span style=\"font-family:Cambria Math\">   </span></p>", " <p> Country</span></p>", 
                                " <p> City</span><span style=\"font-family:Cambria Math\">   </span></p>", " <p> Rural</span></p>"],
                    options_hi: ["", "",
                                "", ""],
                    solution_en: " <p>21.(d) </span><span style=\"font-family:Cambria Math\">An adjective has to be used for the blank and out of all the four options only rural is an adjective. So d rural is the most appropriate answer.</span></p>",
                    solution_hi: " <p>21.(d) </span><span style=\"font-family:Cambria Math\">An adjective has to be used for the blank and out of all the four options only rural is an adjective. So d rural is the most appropriate answer.</span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "22",
                    section: "7",
                    question_en: " <p>22. </span><span style=\"font-family:Cambria Math\">In the following passage some words have been deleted. Fill in the blanks with the help of the alternatives given. Select the most appropriate option for each number. </span></p> <p> _________ areas are also known as the ’countryside’ or a ‘village’ in India. It has a very low population (22) _________. In rural areas, agriculture is the chief source of livelihood along with fishing, cottage (23) ___________, pottery etc. The (24) ___________ to discover the real rural India still continues in great earnest. Almost (25) ___________ economic agency today has a definition of rural India.</span></p> <p><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">Select the most appropriate option to fill blank 22.</span></p>",
                    question_hi: "",
                    options_en: [" <p> density</span><span style=\"font-family:Cambria Math\">     </span></p>", " <p> bulk             </span></p>", 
                                " <p> mass           </span></p>", " <p> thickness</span></p>"],
                    options_hi: ["", "",
                                "", ""],
                    solution_en: " <p>22.(a) </span><span style=\"font-family:Cambria Math\">Population density is a term that counts the number of people per km square of land area.</span></p>",
                    solution_hi: " <p>22.(a) </span><span style=\"font-family:Cambria Math\">Population density is a term that counts the number of people per km square of land area.</span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "23",
                    section: "7",
                    question_en: " <p>23.</span><span style=\"font-family:Cambria Math\"> In the following passage some words have been deleted. Fill in the blanks with the help of the alternatives given. Select the most appropriate option for each number. </span></p> <p> _________ areas are also known as the ’countryside’ or a ‘village’ in India. It has a very low population (22) _________. In rural areas, agriculture is the chief source of livelihood along with fishing, cottage (23) ___________, pottery etc. The (24) ___________ to discover the real rural India still continues in great earnest. Almost (25) ___________ economic agency today has a definition of rural India.</span></p> <p><span style=\"font-family:Cambria Math\">Select the most appropriate option to fill blank 23.</span></p>",
                    question_hi: "",
                    options_en: [" <p> industrialist  </span></p>", " <p> industriousness</span></p>", 
                                " <p> industrial</span></p>", " <p> industries</span></p>"],
                    options_hi: ["", "",
                                "", ""],
                    solution_en: " <p>23.(d) </span><span style=\"font-family:Cambria Math\">Cottage industries are a business or manufacturing activity carried on in people\'s homes.</span></p>",
                    solution_hi: " <p>23.(d) </span><span style=\"font-family:Cambria Math\">Cottage industries are a business or manufacturing activity carried on in people\'s homes.</span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "24",
                    section: "7",
                    question_en: " <p>24. </span><span style=\"font-family:Cambria Math\">In the following passage some words have been deleted. Fill in the blanks with the help of the alternatives given. Select the most appropriate option for each number. </span></p> <p> _________ areas are also known as the ’countryside’ or a ‘village’ in India. It has a very low population (22) _________. In rural areas, agriculture is the chief source of livelihood along with fishing, cottage (23) ___________, pottery etc. The (24) ___________ to discover the real rural India still continues in great earnest. Almost (25) ___________ economic agency today has a definition of rural India.</span></p> <p><span style=\"font-family:Cambria Math\">Select the most appropriate option to fill blank 24.</span></p>",
                    question_hi: "",
                    options_en: [" <p> resistance         </span></p>", " <p> quest    </span></p>", 
                                " <p> flight                  </span></p>", " <p> discovery</span></p>"],
                    options_hi: ["", "",
                                "", ""],
                    solution_en: " <p>24.(b) </span><span style=\"font-family:Cambria Math\">Quest is a long search for something.</span></p>",
                    solution_hi: " <p>24.(b) </span><span style=\"font-family:Cambria Math\">Quest is a long search for something.</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "25",
                    section: "misc",
                    question_en: " <p>25 </span><span style=\"font-family:Cambria Math\">In the following passage some words have been deleted. Fill in the blanks with the help of the alternatives given. Select the most appropriate option for each number. </span></p> <p> _________ areas are also known as the ’countryside’ or a ‘village’ in India. It has a very low population (22) _________. In rural areas, agriculture is the chief source of livelihood along with fishing, cottage (23) ___________, pottery etc. The (24) ___________ to discover the real rural India still continues in great earnest. Almost (25) ___________ economic agency today has a definition of rural India.</span></p> <p><span style=\"font-family:Cambria Math\">.</span><span style=\"font-family:Cambria Math\">Select the most appropriate option to fill blank 25.</span></p>",
                    question_hi: "",
                    options_en: [" <p> every</span><span style=\"font-family:Cambria Math\">       </span></p>", " <p> none</span><span style=\"font-family:Cambria Math\">    </span></p>", 
                                " <p> both               </span></p>", " <p> all</span></p>"],
                    options_hi: ["", "",
                                "", ""],
                    solution_en: " <p>25.(a) </span><span style=\"font-family:Cambria Math\">Both (option c)  and all (option d) can’t be used as they are used with a plural noun. And the word agency has been used which is singular. Option a, every is the most appropriate answer.</span></p>",
                    solution_hi: " <p>25.(a) </span><span style=\"font-family:Cambria Math\">Both (option c)  and all (option d) can’t be used as they are used with a plural noun. And the word agency has been used which is singular. Option a, every is the most appropriate answer.</span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>