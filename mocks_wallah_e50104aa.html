<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">15:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 15</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">15</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 15 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 0,
                end: 13
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 14,
                end: 14
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "17",
                    question_en: " <p>1.</span><span style=\"font-family:Cambria Math\">  The marks scored by 10 students are given below.</span></p> <p><span style=\"font-family:Cambria Math\">17, 13, 18, 11, 15, 13, 19, 18, 13, 17</span></p> <p><span style=\"font-family:Cambria Math\">The mode of the data is:</span></p>",
                    question_hi: " <p>1.</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">नीचे</span><span style=\"font-family:Cambria Math\"> 10 </span><span style=\"font-family:Nirmala UI\">विद्यार्थियों</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">प्राप्तांक</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">दिए</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">गए</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">हैं।</span></p> <p><span style=\"font-family:Cambria Math\">17, 13, 18, 11, 15, 13, 19, 18, 13, 17</span></p> <p><span style=\"font-family:Nirmala UI\">उपरोक्त</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">आंकड़ों</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">का</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">बहुलक</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">ज्ञात</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">कीजिए।</span></p>",
                    options_en: [" <p> 13</span></p>", " <p> 17</span></p>", 
                                " <p> 11</span></p>", " <p> 19</span></p>"],
                    options_hi: [" <p> 13</span></p>", " <p> 17</span></p>",
                                " <p> 11</span></p>", " <p> 19</span></p>"],
                    solution_en: " <p>1.(a)</span></p> <p><span style=\"font-family:Cambria Math\">Arranging in ascending order, </span></p> <p><span style=\"font-family:Cambria Math\">11, 13, 13, 13, 15, 17, 17, 18, 18, 19</span></p> <p><span style=\"font-family:Cambria Math\">Mode is the value which occurs the maximum number of times in the given data set.</span></p> <p><span style=\"font-family:Cambria Math\">Hence, Mode = 1</span><span style=\"font-family:Cambria Math\">3</span></p>",
                    solution_hi: " <p>1.(a)</span></p> <p><span style=\"font-family:Nirmala UI\">आरोही</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">क्रम</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">व्यवस्थित</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">करने</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">पर</span><span style=\"font-family:Cambria Math\">,</span></p> <p><span style=\"font-family:Cambria Math\">11, 13, 13, 13, 15, 17, 17, 18, 18, 19</span></p> <p><span style=\"font-family:Nirmala UI\">मोड</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">वह</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">मान</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">है</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">जो</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">दिए</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">गए</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">डेटा</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">सेट</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">अधिकतम</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">बार</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">आता</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">है।</span></p> <p><span style=\"font-family:Nirmala UI\">इसलिए</span><span style=\"font-family:Cambria Math\">, </span><span style=\"font-family:Nirmala UI\">बहुलक</span><span style=\"font-family:Cambria Math\"> = 13</span></p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "2",
                    section: "17",
                    question_en: "<p>2. <span style=\"font-family: Cambria Math;\">The arithmetic mean of 2.5, 6.5, 4.5, 16.5, 3.5 and 2.5 is:</span></p>\n",
                    question_hi: "<p>2. <span style=\"font-family: Cambria Math;\">2.5, 6.5, 4.5, 16.5, 3.5 </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> 2.5 </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2306;&#2325;&#2327;&#2339;&#2367;&#2340;&#2368;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2343;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>8.25</p>\n", "<p>5.75</p>\n", 
                                "<p>6</p>\n", "<p>7.25</p>\n"],
                    options_hi: ["<p>8.25</p>\n", "<p>5.75</p>\n",
                                "<p>6</p>\n", "<p>7.25</p>\n"],
                    solution_en: "<p>2.(c)</p>\r\n<p><span style=\"font-family: Cambria Math;\">Mean =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><mo>.</mo><mn>5</mn><mo>+</mo><mn>6</mn><mo>.</mo><mn>5</mn><mo>+</mo><mn>4</mn><mo>.</mo><mn>5</mn><mo>+</mo><mn>16</mn><mo>.</mo><mn>5</mn><mo>+</mo><mn>3</mn><mo>.</mo><mn>5</mn><mo>+</mo><mn>2</mn><mo>.</mo><mn>5</mn></mrow><mn>6</mn></mfrac><mo>=</mo><mfrac><mn>36</mn><mn>6</mn></mfrac><mo>=</mo><mn>6</mn></math></span></p>\n",
                    solution_hi: "<p>2.(c)</p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2343;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><mo>.</mo><mn>5</mn><mo>+</mo><mn>6</mn><mo>.</mo><mn>5</mn><mo>+</mo><mn>4</mn><mo>.</mo><mn>5</mn><mo>+</mo><mn>16</mn><mo>.</mo><mn>5</mn><mo>+</mo><mn>3</mn><mo>.</mo><mn>5</mn><mo>+</mo><mn>2</mn><mo>.</mo><mn>5</mn></mrow><mn>6</mn></mfrac><mo>=</mo><mfrac><mn>36</mn><mn>6</mn></mfrac><mo>=</mo><mn>6</mn></math></span></p>\n",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "3",
                    section: "17",
                    question_en: "<p>3.<span style=\"font-family: Cambria Math;\"> The correct empirical relationship between the mean, the median, and the mode of a set of data is given by:</span></p>\n",
                    question_hi: "<p>3.<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2325;&#2396;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2360;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2350;&#2369;&#2330;&#2381;&#2330;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2343;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2343;&#2381;&#2351;&#2367;&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2361;&#2369;&#2354;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2368;&#2330;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2351;&#2379;&#2327;&#2360;&#2367;&#2342;&#2381;&#2343;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2348;&#2306;&#2343;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2370;&#2340;&#2381;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2381;&#2357;&#2366;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2367;&#2352;&#2370;&#2346;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2366;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                    options_en: ["<p><span style=\"font-weight: 400;\">Mean - Mode = 4 &times; (Mean - Median)</span></p>\n", "<p>Mean - Mode = 1 &times; (Mean - Median)</p>\n", 
                                "<p><strong><span style=\"font-weight: 400;\">Mean - Mode = 2 x (Mean - Median)</span></strong></p>\n", "<p>Mean - Mode = 3 x (Mean <span style=\"font-family: Cambria Math;\">- Median) </span></p>\n"],
                    options_hi: ["<p><span style=\"font-weight: 400;\">&nbsp;&#2350;&#2366;&#2343;&#2381;&#2351; - &#2348;&#2361;&#2369;&#2354;&#2325; = 4 &times; (&#2350;&#2366;&#2343;&#2381;&#2351; - &#2350;&#2366;&#2343;&#2381;&#2351;&#2367;&#2325;&#2366;)</span></p>\n", "<p><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2343;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> - </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2361;&#2369;&#2354;&#2325;</span><span style=\"font-family: Cambria Math;\"> = 1 &times; (</span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2343;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> - </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2343;&#2381;&#2351;&#2367;&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\">) </span></p>\n",
                                "<p><span style=\"font-weight: 400;\">&nbsp;&#2350;&#2366;&#2343;&#2381;&#2351; - &#2348;&#2361;&#2369;&#2354;&#2325; = 2 x (&#2350;&#2366;&#2343;&#2381;&#2351; - &#2350;&#2366;&#2343;&#2381;&#2351;&#2367;&#2325;&#2366;)</span></p>\n", "<p><span style=\"font-weight: 400;\">&#2350;&#2366;&#2343;&#2381;&#2351; - &#2348;&#2361;&#2369;&#2354;&#2325; = 3 x (&#2350;&#2366;&#2343;&#2381;&#2351; - &#2350;&#2366;&#2343;&#2381;&#2351;&#2367;&#2325;&#2366;)</span><span style=\"font-family: Cambria Math;\"> </span></p>\n"],
                    solution_en: "<p>3.(d)</p>\r\n<p><span style=\"font-family: Cambria Math;\">Check by options, Mean - Mode = 3 x (Mean - Median)</span></p>\r\n<p><span style=\"font-weight: 400;\">&rArr;Mean - Mode = 3&nbsp; Mean - 3Median</span></p>\r\n<p><span style=\"font-weight: 400;\">&rArr;Mean- 3&nbsp; Mean+3 Median =Mode</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">&rArr;Mode=3 Median-2Mean</span></span></p>\n",
                    solution_hi: "<p>3.(d)</p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2325;&#2354;&#2381;&#2346;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2381;&#2357;&#2366;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2366;&#2305;&#2330;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\">, </span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2343;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> &ndash; </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2361;&#2369;&#2354;&#2325;</span><span style=\"font-family: Cambria Math;\"> = 3 x (</span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2343;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> &ndash; </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2343;&#2381;&#2351;&#2367;&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\">)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&nbsp;&rArr;</span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2343;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> &ndash; </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2361;&#2369;&#2354;&#2325;</span><span style=\"font-family: Cambria Math;\"> = 3 </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2343;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> &ndash; 3 </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2343;&#2381;&#2351;&#2367;&#2325;&#2366;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&nbsp;<span style=\"font-weight: 400;\">&rArr;</span></span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2343;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> &ndash; 3 </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2343;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> + 3 </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2343;&#2381;&#2351;&#2367;&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> = </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2361;&#2369;&#2354;&#2325;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&nbsp;<span style=\"font-weight: 400;\">&rArr;</span></span><span style=\"font-family: Nirmala UI;\">&#2348;&#2361;&#2369;&#2354;&#2325;</span><span style=\"font-family: Cambria Math;\"> = 3 </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2343;&#2381;&#2351;&#2367;&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> &ndash; 2 </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2343;&#2381;&#2351;</span></p>\n",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "4",
                    section: "17",
                    question_en: "<p>4. <span style=\"font-family: Cambria Math;\">If the mode of a distribution is 27 and its median is 35, then the mean of the distribution is ______(using empirical relation).</span></p>\n",
                    question_hi: "<p>4. <span style=\"font-family: Nirmala UI;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2306;&#2335;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2361;&#2369;&#2354;&#2325;</span><span style=\"font-family: Cambria Math;\"> 27 </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2311;&#2360;&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2343;&#2381;&#2351;&#2367;&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> 35 </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2306;&#2335;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2343;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2381;&#2334;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;&#2332;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> (</span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2351;&#2379;&#2327;</span><span style=\"font-family: Nirmala UI;\">&#2360;&#2367;&#2342;&#2381;&#2343;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2348;&#2306;&#2343;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2346;&#2351;&#2379;&#2327;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;&#2340;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2369;&#2319;&#2404;</span><span style=\"font-family: Cambria Math;\">) </span></p>\n",
                    options_en: ["<p>39</p>\n", "<p>62</p>\n", 
                                "<p>37.5</p>\n", "<p>43.5</p>\n"],
                    options_hi: ["<p>39</p>\n", "<p>62</p>\n",
                                "<p>37.5</p>\n", "<p>43.5</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">4.(a)&nbsp;</span></p>\r\n<p><span style=\"font-weight: 400;\">Mode = 3 Median - 2 mean</span></p>\r\n<p><span style=\"font-weight: 400;\">&rArr;27=3&times;35-2Mean</span></p>\r\n<p><span style=\"font-weight: 400;\">&rArr;2 Mean=105-27</span></p>\r\n<p><span style=\"font-weight: 400;\">&rArr;2 Mean = 78</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">&rArr;Mean=39</span></span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">4.(a)</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2348;&#2361;&#2369;&#2354;&#2325;</span><span style=\"font-family: Cambria Math;\"> = 3 </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2343;&#2381;&#2351;&#2367;&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> &ndash; 2 </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2343;&#2381;&#2351;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&nbsp;<span style=\"font-weight: 400;\">&rArr;</span>27 = 3&times;35 &ndash; 2 </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2343;&#2381;&#2351;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&nbsp;<span style=\"font-weight: 400;\">&rArr;</span>2 </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2343;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> = 105 &ndash; 27</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&nbsp;<span style=\"font-weight: 400;\">&rArr;</span>2 </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2343;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> = 78</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&nbsp;<span style=\"font-weight: 400;\">&rArr;</span></span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2343;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> = 39</span></p>\n",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "5",
                    section: "17",
                    question_en: "<p>5.<span style=\"font-family: Cambria Math;\"> If the mean of a distribution is 8.73 and its median is 11, then the mode of the distribution </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">is_______(using empirical relation). </span></p>\n",
                    question_hi: "<p>5<span style=\"font-family: Cambria Math;\">.</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2306;&#2335;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2343;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> 8.73 </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2360;&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2343;&#2381;&#2351;&#2367;&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> 11 </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2306;&#2335;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2361;&#2369;&#2354;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2381;&#2334;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;&#2332;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> (</span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2351;&#2379;&#2327;&#2360;&#2367;&#2342;&#2381;&#2343;</span><span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2348;&#2306;&#2343;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2346;&#2351;&#2379;&#2327;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;&#2340;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2369;&#2319;</span><span style=\"font-family: Cambria Math;\">)</span><span style=\"font-family: Nirmala UI;\">&#2404;</span></p>\n",
                    options_en: ["<p>22</p>\n", "<p>19.73</p>\n", 
                                "<p>15.54</p>\n", "<p>17.46</p>\n"],
                    options_hi: ["<p>22</p>\n", "<p>19.73</p>\n",
                                "<p>15.54</p>\n", "<p>17.46</p>\n"],
                    solution_en: "<p>5.(c)</p>\r\n<p><span style=\"font-weight: 400;\">Mode=3 Median-2 Mean</span></p>\r\n<p><span style=\"font-weight: 400;\">&rArr;Mode=3&times;11-2&times;8.73</span></p>\r\n<p><span style=\"font-weight: 400;\">&rArr;Mode=33-17.46=15.54</span></p>\r\n<p><span style=\"font-weight: 400;\">Hence, the Mode = 15.54</span></p>\n",
                    solution_hi: "<p>5.(c)</p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2348;&#2361;&#2369;&#2354;&#2325;</span><span style=\"font-family: Cambria Math;\">=3 </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2343;&#2381;&#2351;&#2367;&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> &ndash; 2 </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2343;&#2381;&#2351;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&nbsp;<span style=\"font-weight: 400;\">&rArr;</span></span><span style=\"font-family: Nirmala UI;\">&#2348;&#2361;&#2369;&#2354;&#2325;</span><span style=\"font-family: Cambria Math;\"> =3&times;11 &ndash; 2&times;8.73</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&nbsp;<span style=\"font-weight: 400;\">&rArr;</span></span><span style=\"font-family: Nirmala UI;\">&#2348;&#2361;&#2369;&#2354;&#2325;</span><span style=\"font-family: Cambria Math;\"> =33 &ndash; 17.46 = 15.54</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2309;&#2340;&#2307;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2361;&#2369;&#2354;&#2325;</span><span style=\"font-family: Cambria Math;\"> = 15.54</span></p>\n",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "6",
                    section: "17",
                    question_en: " <p><span style=\"font-family:Cambria Math\">6.</span><span style=\"font-family:Cambria Math\"> The mode of 5, 18, 6, 7, 6, 2, 3, 4, 24, 2, 7, 21, 2, 81 is:</span></p>",
                    question_hi: " <p><span style=\"font-family:Cambria Math\">6.</span><span style=\"font-family:Cambria Math\"> 5, 18, 6, 7, 6, 2, 3, 4, 24, 2, 7, 21, 2, 81 </span><span style=\"font-family:Nirmala UI\">का</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">बहुलक</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">क्या</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">है</span><span style=\"font-family:Cambria Math\">?</span></p>",
                    options_en: [" <p> 6</span></p>", " <p> 2</span></p>", 
                                " <p> 81</span></p>", " <p> 7</span></p>"],
                    options_hi: [" <p> 6</span></p>", " <p> 2</span></p>",
                                " <p> 81</span></p>", " <p> 7</span></p>"],
                    solution_en: " <p><span style=\"font-family:Cambria Math\">6.(b)</span></p> <p><span style=\"font-family:Cambria Math\">Series -  5, 18, 6, 7, 6, 2, 3, 4, 24, 2, 7, 21, 2, 81</span></p> <p><span style=\"font-family:Cambria Math\">On arranging - 2, 2, 2, 3, 4, 5, 6, 6, 7, 7, 18, 21, 24, 81</span></p> <p><span style=\"font-family:Cambria Math\">Here, 2 is the most repeated term.</span></p> <p><span style=\"font-family:Cambria Math\">So, mode = 2</span></p>",
                    solution_hi: " <p><span style=\"font-family:Cambria Math\">6.(b)</span></p> <p><span style=\"font-family:Nirmala UI\">श्रृंखला</span><span style=\"font-family:Cambria Math\"> - 5, 18, 6, 7, 6, 2, 3, 4, 24, 2, 7, 21, 2, 81</span></p> <p><span style=\"font-family:Nirmala UI\">व्यवस्थित</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">करने</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">पर</span><span style=\"font-family:Cambria Math\"> - 2, 2, 2, 3, 4, 5, 6, 6, 7, 7, 18, 21, 24, 81</span></p> <p><span style=\"font-family:Nirmala UI\">यहाँ</span><span style=\"font-family:Cambria Math\">, 2 </span><span style=\"font-family:Nirmala UI\">सबसे</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">अधिक</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">दोहराया</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">जाने</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">वाला</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">पद</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">है।</span></p> <p><span style=\"font-family:Nirmala UI\">अतः</span><span style=\"font-family:Cambria Math\">, </span><span style=\"font-family:Nirmala UI\">बहुलक</span><span style=\"font-family:Cambria Math\"> = 2</span></p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "7",
                    section: "17",
                    question_en: " <p><span style=\"font-family:Cambria Math\">7.</span><span style=\"font-family:Cambria Math\"> The mean of the data is 38 and its median is 46. The mode (using empirical relation) of the data is:</span></p>",
                    question_hi: " <p><span style=\"font-family:Cambria Math\">7.</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">एक</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">डेटा</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">का</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">माध्य</span><span style=\"font-family:Cambria Math\"> 38 </span><span style=\"font-family:Nirmala UI\">है</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">और</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">इसका</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">माध्यिका</span><span style=\"font-family:Cambria Math\"> 46 </span><span style=\"font-family:Nirmala UI\">है।</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">डेटा</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">का</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">बहुलक</span><span style=\"font-family:Cambria Math\"> (</span><span style=\"font-family:Nirmala UI\">अनुभवजन्य</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">संबंध</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">का</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">उपयोग</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">करके</span><span style=\"font-family:Cambria Math\">) </span><span style=\"font-family:Nirmala UI\">ज्ञात</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">कीजिए।</span><span style=\"font-family:Cambria Math\"> </span></p>",
                    options_en: [" <p> 62  </span></p>", " <p> 64</span></p>", 
                                " <p> 63</span></p>", " <p> 65 </span></p>"],
                    options_hi: [" <p> </span><span style=\"font-family:Cambria Math\">62  </span></p>", " <p> 64</span></p>",
                                " <p> 63</span></p>", " <p> 65</span></p>"],
                    solution_en: " <p>7.(a)</span></p> <p><span style=\"font-family:Cambria Math\">According to question,</span></p> <p><span style=\"font-family:Cambria Math\">3 × Median - 2 × Mean = Mode</span></p> <p><span style=\"font-family:Cambria Math\">3 × 46 - 2 × 38 = Mode</span></p> <p><span style=\"font-family:Cambria Math\">Mode = 138 - 76 = 62</span></p>",
                    solution_hi: " <p><span style=\"font-family:Cambria Math\">7.(a)</span></p> <p><span style=\"font-family:Nirmala UI\">प्रश्न</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">अनुसार</span><span style=\"font-family:Cambria Math\">,</span></p> <p><span style=\"font-family:Cambria Math\">3 × </span><span style=\"font-family:Nirmala UI\">माध्यिका</span><span style=\"font-family:Cambria Math\"> – 2 × </span><span style=\"font-family:Nirmala UI\">माध्य</span><span style=\"font-family:Cambria Math\"> = </span><span style=\"font-family:Nirmala UI\">बहुलक</span></p> <p><span style=\"font-family:Cambria Math\">3 × 46 – 2 × 38 = </span><span style=\"font-family:Nirmala UI\">बहुलक</span></p> <p><span style=\"font-family:Nirmala UI\">बहुलक</span><span style=\"font-family:Cambria Math\"> = 138 – 76 = 62</span></p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "8",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">8. </span><span style=\"font-family: Cambria Math;\">The mean marks of the following distribution is </span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/images/mceu_36122147911680328028191.png\" width=\"202\" height=\"133\"></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">8.</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2306;&#2335;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2343;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2306;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2381;&#2334;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;&#2332;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&nbsp;<img src=\"https://ssccglpinnacle.com/images/mean%208%20right.PNG\" alt=\"\" width=\"200\" height=\"151\"></span></p>\n",
                    options_en: ["<p>32</p>\n", "<p>31</p>\n", 
                                "<p>29</p>\n", "<p>30</p>\n"],
                    options_hi: ["<p>32</p>\n", "<p>31</p>\n",
                                "<p>29</p>\n", "<p>30</p>\n"],
                    solution_en: "<p>8.(d)</p>\r\n<p><span style=\"font-family: Cambria Math;\">Mean = </span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>25</mn><mo>&times;</mo><mn>5</mn><mo>+</mo><mn>30</mn><mo>&times;</mo><mn>6</mn><mo>+</mo><mn>35</mn><mo>&times;</mo><mn>3</mn><mo>+</mo><mn>40</mn><mo>&times;</mo><mn>1</mn></mrow><mrow><mn>5</mn><mo>+</mo><mn>6</mn><mo>+</mo><mn>3</mn><mo>+</mo><mn>1</mn></mrow></mfrac><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>30</mn></math></span></p>\n",
                    solution_hi: "<p>8.(d)</p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2343;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> = </span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>25</mn><mo>&times;</mo><mn>5</mn><mo>+</mo><mn>30</mn><mo>&times;</mo><mn>6</mn><mo>+</mo><mn>35</mn><mo>&times;</mo><mn>3</mn><mo>+</mo><mn>40</mn><mo>&times;</mo><mn>1</mn></mrow><mrow><mn>5</mn><mo>+</mo><mn>6</mn><mo>+</mo><mn>3</mn><mo>+</mo><mn>1</mn></mrow></mfrac><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>30</mn></math></span></p>\n",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "9",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">9. </span><span style=\"font-family: Cambria Math;\">Mahesh earned &#8377;1,23,450, &#8377;1,34,900, &#8377;98,640, &#8377;1,26,500, and &#8377;1,42,000 as his monthly salaries (inclusive of incentives) during the first five months of a year. What is the mean salary (inclusive of incentives) earned every month by Mahesh during the first </span><span style=\"font-family: Cambria Math;\">five months of the given year?</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">9. </span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2361;&#2375;&#2358;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2352;&#2381;&#2359;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2341;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2366;&#2305;&#2330;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2361;&#2368;&#2344;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2380;&#2352;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2346;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2360;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2375;&#2340;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2352;&#2370;&#2346;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> (</span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2379;&#2340;&#2381;&#2360;&#2366;&#2361;&#2344;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2361;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\">) &#8377;1,23,450, &#8377;1,34,900, &#8377;98,640, &#8377;1,26,500 </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> &#8377;1,42,000 </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2352;&#2381;&#2332;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2319;</span><span style=\"font-family: Nirmala UI;\">&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2352;&#2381;&#2359;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2341;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2366;&#2305;&#2330;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2361;&#2368;&#2344;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2380;&#2352;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2361;&#2375;&#2358;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2350;&#2366;&#2361;</span><span style=\"font-family: Cambria Math;\"> (</span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2379;&#2340;&#2381;&#2360;&#2366;&#2361;&#2344;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2361;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> ) </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2340;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2343;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2375;&#2340;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2352;&#2381;&#2332;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>&#8377;1,24,975</p>\n", "<p>&#8377; 1,24,678</p>\n", 
                                "<p>&#8377;1,25,346</p>\n", "<p>&#8377;1,25,098</p>\n"],
                    options_hi: ["<p>&#8377;1,24,975</p>\n", "<p>&#8377; 1,24,678</p>\n",
                                "<p>&#8377;1,25,346</p>\n", "<p>&#8377;1,25,098</p>\n"],
                    solution_en: "<p>9.(d)</p>\r\n<p><span style=\"font-family: Cambria Math;\">Mean salary of Mahesh in all this 5 years =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>123450</mn><mo>+</mo><mn>134900</mn><mo>+</mo><mn>98640</mn><mo>+</mo><mn>126500</mn><mo>+</mo><mn>142000</mn></mrow><mn>5</mn></mfrac><mo>=</mo><mfrac><mn>625490</mn><mn>5</mn></mfrac><mo>=</mo><mo>&#8377;</mo><mn>1</mn><mo>,</mo><mn>25</mn><mo>,</mo><mn>098</mn></math></span></p>\n",
                    solution_hi: "<p>9.(d)</p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2311;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2349;&#2368;</span><span style=\"font-family: Cambria Math;\"> 5 </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2352;&#2381;&#2359;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2361;&#2375;&#2358;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2360;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2375;&#2340;&#2344;</span><span style=\"font-family: Cambria Math;\"> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>123450</mn><mo>+</mo><mn>134900</mn><mo>+</mo><mn>98640</mn><mo>+</mo><mn>126500</mn><mo>+</mo><mn>142000</mn></mrow><mn>5</mn></mfrac><mo>=</mo><mfrac><mn>625490</mn><mn>5</mn></mfrac><mo>=</mo><mo>&#8377;</mo><mn>1</mn><mo>,</mo><mn>25</mn><mo>,</mo><mn>098</mn></math></span></p>\n",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "10",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">10. </span><span style=\"font-family: Cambria Math;\">The median of the data using an empirical formula, when it is given that mode = 37.4 and mean = 30.5 is:</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">10.</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2370;&#2354;&#2366;&#2344;&#2369;&#2346;&#2366;&#2340;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2370;&#2340;&#2381;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2346;&#2351;&#2379;&#2327;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;&#2340;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2369;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2306;&#2325;&#2337;&#2364;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2343;&#2381;&#2351;&#2367;&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2381;&#2334;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;&#2332;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2348;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2351;&#2361;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2361;&#2369;&#2354;&#2325;</span><span style=\"font-family: Cambria Math;\"> = 37.4 </span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2343;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> = 30.5 </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                    options_en: ["<p>33.4</p>\n", "<p>32.8</p>\n", 
                                "<p>31.7</p>\n", "<p>30.5</p>\n"],
                    options_hi: ["<p>33.4</p>\n", "<p>32.8</p>\n",
                                "<p>31.7</p>\n", "<p>30.5</p>\n"],
                    solution_en: "<p>10.(b)&nbsp;</p>\r\n<p><span style=\"font-weight: 400;\">Mode=3 Median-2 Mean</span></p>\r\n<p><span style=\"font-weight: 400;\">&rArr; 37.4=3 Median -2&times;30.5</span></p>\r\n<p><span style=\"font-weight: 400;\">&rArr; 3 Median=37.4+61</span></p>\r\n<p><span style=\"font-weight: 400;\">&rArr; 3 Median=98.4</span></p>\r\n<p><span style=\"font-weight: 400;\">&nbsp;&rArr; Median=32.8</span></p>\n",
                    solution_hi: "<p>10.(b)</p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2348;&#2361;&#2369;&#2354;&#2325;</span><span style=\"font-family: Cambria Math;\"> = 3 </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2343;&#2381;&#2351;&#2367;&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> &ndash; 2 </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2343;&#2381;&#2351;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&nbsp;<span style=\"font-weight: 400;\">&rArr; </span>37.4=3 </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2343;&#2381;&#2351;&#2367;&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> -2&times;30.5</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&nbsp;<span style=\"font-weight: 400;\">&rArr; </span>3 </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2343;&#2381;&#2351;&#2367;&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> = 37.4 + 61</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&nbsp;<span style=\"font-weight: 400;\">&rArr; </span>3 </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2343;&#2381;&#2351;&#2367;&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> = 98.4</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&nbsp;<span style=\"font-weight: 400;\">&rArr; </span></span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2343;&#2381;&#2351;&#2367;&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> = 32.8</span></p>\n",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "11",
                    section: "17",
                    question_en: "<p>11. <span style=\"font-family: Cambria Math;\">The median of the observations below, given in ascending order, is n-1.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">4, 8, n - 14, 17, n + 5, 30, 32 and 35</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">What is the value of n?</span></p>\n",
                    question_hi: "<p>11. <span style=\"font-family: Nirmala UI;\">&#2310;&#2352;&#2379;&#2361;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2352;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2368;&#2330;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2375;&#2325;&#2381;&#2359;&#2339;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2343;&#2381;&#2351;&#2367;&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> n-1 </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">4, 8, n - 14, 17, n + 5, 30, 32 </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> 35</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">n </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>18</p>\n", "<p>19</p>\n", 
                                "<p>24</p>\n", "<p>17</p>\n"],
                    options_hi: ["<p>18</p>\n", "<p>19</p>\n",
                                "<p>24</p>\n", "<p>17</p>\n"],
                    solution_en: "<p>11.(c)</p>\r\n<p><span style=\"font-family: Cambria Math;\">4, 8, n - 14 , 17 , n+5 , 30, 32 and 35</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>M</mi><mi>e</mi><mi>d</mi><mi>i</mi><mi>a</mi><mi>n</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mfrac><mrow><mn>17</mn><mo>+</mo><mi>n</mi><mo>+</mo><mn>5</mn></mrow><mn>2</mn></mfrac><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mo>&rArr;</mo><mfrac><mrow><mn>17</mn><mo>+</mo><mi>n</mi><mo>+</mo><mn>5</mn></mrow><mn>2</mn></mfrac><mo>=</mo><mi>n</mi><mo>-</mo><mn>1</mn><mspace linebreak=\"newline\"></mspace><mo>&rArr;</mo><mo>&nbsp;</mo><mi>n</mi><mo>+</mo><mn>22</mn><mo>=</mo><mn>2</mn><mi>n</mi><mo>-</mo><mn>2</mn><mspace linebreak=\"newline\"></mspace><mo>&nbsp;</mo><mo>&rArr;</mo><mi>n</mi><mo>=</mo><mn>24</mn></math> </span></p>\n",
                    solution_hi: "<p>11.(c)</p>\r\n<p><span style=\"font-family: Cambria Math;\">4, 8, n - 14 , 17 , n+5 , 30, 32 </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> 35</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><math style=\"font-family:Verdana\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#2350;&#2366;&#2343;&#2381;&#2351;&#2367;&#2325;&#2366;</mi><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mfrac><mrow><mn>17</mn><mo>+</mo><mi>n</mi><mo>+</mo><mn>5</mn></mrow><mn>2</mn></mfrac><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mo>&rArr;</mo><mfrac><mrow><mn>17</mn><mo>+</mo><mi>n</mi><mo>+</mo><mn>5</mn></mrow><mn>2</mn></mfrac><mo>=</mo><mi>n</mi><mo>-</mo><mn>1</mn><mspace linebreak=\"newline\"></mspace><mo>&rArr;</mo><mo>&nbsp;</mo><mi>n</mi><mo>+</mo><mn>22</mn><mo>=</mo><mn>2</mn><mi>n</mi><mo>-</mo><mn>2</mn><mspace linebreak=\"newline\"></mspace><mo>&nbsp;</mo><mo>&rArr;</mo><mi>n</mi><mo>=</mo><mn>24</mn></math></span></p>\n",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "12",
                    section: "17",
                    question_en: "<p>12. <span style=\"font-family: Cambria Math;\">Which of the following expressions is correct?</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">1. Mode - median = 2 (median - mean)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">2.<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>M</mi><mi>e</mi><mi>d</mi><mi>i</mi><mi>a</mi><mi>n</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mfrac><mrow><mi>m</mi><mi>o</mi><mi>d</mi><mi>e</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mi>m</mi><mi>e</mi><mi>d</mi><mi>i</mi><mi>a</mi><mi>n</mi></mrow><mn>2</mn></mfrac></math> </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">3. Mode = 2 median - mean </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">4.<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>M</mi><mi>e</mi><mi>a</mi><mi>n</mi><mo>=</mo><mfrac><mrow><mn>2</mn><mo>&nbsp;</mo><mi>M</mi><mi>e</mi><mi>d</mi><mi>i</mi><mi>a</mi><mi>n</mi><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mi>m</mi><mi>o</mi><mi>d</mi><mi>e</mi></mrow><mn>2</mn></mfrac></math> </span></p>\n",
                    question_hi: "<p>12. <span style=\"font-family: Nirmala UI;\">&#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2380;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2381;&#2351;&#2306;&#2332;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2361;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">1. </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2361;&#2369;&#2354;&#2325;</span><span style=\"font-family: Cambria Math;\"> - </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2343;&#2381;&#2351;&#2367;&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> = 2 (</span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2343;&#2381;&#2351;&#2367;&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> - </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2343;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\">)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">2.<math style=\"font-family:Verdana\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#2350;&#2366;&#2343;&#2381;&#2351;&#2367;&#2325;&#2366;</mi><mo>=</mo><mo>&nbsp;</mo><mfrac><mrow><mi>&#2348;&#2361;&#2369;&#2354;&#2325;</mi><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mi>&#2350;&#2366;&#2343;&#2381;&#2351;&#2367;&#2325;&#2366;</mi></mrow><mn>2</mn></mfrac></math> </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">3. </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2361;&#2369;&#2354;&#2325;</span><span style=\"font-family: Cambria Math;\"> = 2 </span><span style=\"font-family: Nirmala UI;\">&#2350;</span><span style=\"font-family: Nirmala UI;\">&#2366;&#2343;&#2381;&#2351;&#2367;&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> - </span><span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">4.<math style=\"font-family:Verdana\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#2350;&#2366;&#2343;&#2381;&#2351;</mi><mo>=</mo><mfrac><mrow><mn>2</mn><mo>&nbsp;</mo><mi>&#2350;&#2366;&#2343;&#2381;&#2351;&#2367;&#2325;&#2366;</mi><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mi>&#2348;&#2361;&#2369;&#2354;&#2325;</mi></mrow><mn>2</mn></mfrac></math> </span></p>\n",
                    options_en: ["<p>2</p>\n", "<p>4</p>\n", 
                                "<p>3</p>\n", "<p>1</p>\n"],
                    options_hi: ["<p>2</p>\n", "<p>4</p>\n",
                                "<p>3</p>\n", "<p>1</p>\n"],
                    solution_en: "<p>12.(d)</p>\r\n<p><span style=\"font-family: Cambria Math;\">Mode - Median = 2( Median -Mean)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">= Mode = 3 &times;Median - 2&times; Mean</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">So, option d is correct.</span></p>\n",
                    solution_hi: "<p>12.(d)</p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2348;&#2361;&#2369;&#2354;&#2325;</span><span style=\"font-family: Cambria Math;\"> - </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2343;&#2381;&#2351;&#2367;&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> = 2( </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2343;&#2381;&#2351;&#2367;&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> - </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2343;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\">)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">= </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2361;&#2369;&#2354;&#2325;</span><span style=\"font-family: Cambria Math;\"> = 3 &times;</span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2343;&#2381;&#2351;&#2367;&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> - 2&times; </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2343;&#2381;&#2351;</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2309;&#2340;</span><span style=\"font-family: Cambria Math;\">:, </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2325;&#2354;&#2381;&#2346;</span><span style=\"font-family: Cambria Math;\"> (d) </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2361;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span></p>\n",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "13",
                    section: "17",
                    question_en: "<p>13.<span style=\"font-family: Cambria Math;\"> The following observations are arranged in ascending order.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">29, 32, 48, 50, x, x+2, 72, 78, 84, 95</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">If the median is 63, then the value of x is:</span></p>\n",
                    question_hi: "<p>13.<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;</span><span style=\"font-family: Nirmala UI;\">&#2326;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2375;&#2325;&#2381;&#2359;&#2339;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2352;&#2379;&#2361;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2352;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2381;&#2351;&#2357;&#2360;&#2381;&#2341;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">29, 32, 48, 50, x, x + 2, 72, 78, 84, 95.</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2343;&#2381;&#2351;&#2367;&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> 63 </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> x </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>50</p>\n", "<p>31</p>\n", 
                                "<p>62</p>\n", "<p>63</p>\n"],
                    options_hi: ["<p>50</p>\n", "<p>31</p>\n",
                                "<p>62</p>\n", "<p>63</p>\n"],
                    solution_en: "<p>13.(c)</p>\r\n<p><span style=\"font-family: Cambria Math;\">29, 32, 48, 50, x, x+2 , 72, 78, 84, 95</span><span style=\"font-family: Cambria Math;\">&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>M</mi><mi>e</mi><mi>d</mi><mi>i</mi><mi>a</mi><mi>n</mi><mo>&nbsp;</mo><mo>=</mo><mfrac><mrow><mo>&nbsp;</mo><mi>x</mi><mo>+</mo><mi>x</mi><mo>+</mo><mn>2</mn></mrow><mrow><mn>2</mn><mo>&nbsp;</mo></mrow></mfrac></math></span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&rArr;</mo><mfrac><mrow><mi>x</mi><mo>+</mo><mi>x</mi><mo>+</mo><mn>2</mn></mrow><mn>2</mn></mfrac><mo>=</mo><mn>63</mn></math></span></p>\r\n<p><span style=\"font-weight: 400;\"><span style=\"font-family: Cambria Math;\">&rArr;</span>2x+2=126</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">&rArr;2x=124&rArr;</span><span style=\"font-weight: 400;\">x=62</span></span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Hence, the value of x is 62.</span></p>\n",
                    solution_hi: "<p>13.(c)</p>\r\n<p><span style=\"font-family: Cambria Math;\">29, 32, 48, 50, x, x+2 , 72, 78, 84, 95</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2343;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> =&nbsp;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>&nbsp;</mo><mi>x</mi><mo>+</mo><mi>x</mi><mo>+</mo><mn>2</mn></mrow><mrow><mn>2</mn><mo>&nbsp;</mo></mrow></mfrac></math></span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&rArr;</mo><mfrac><mrow><mi>x</mi><mo>+</mo><mi>x</mi><mo>+</mo><mn>2</mn></mrow><mn>2</mn></mfrac><mo>=</mo><mn>63</mn></math></span></p>\r\n<p><span style=\"font-weight: 400;\"><span style=\"font-family: Cambria Math;\">&rArr;</span>2x+2=126</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">&rArr;2x=124&rArr;</span><span style=\"font-weight: 400;\">x=62</span></span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2311;&#2360;&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\">, x </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> 62 </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span></p>\n",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "14",
                    section: "17",
                    question_en: " <p>14. </span><span style=\"font-family:Cambria Math\">For a certain number of observations the median is 55 and the mean is 58. Find the mode.</span></p>",
                    question_hi: " <p>14. </span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">प्रेक्षणों</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">की</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">एक</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">निश्चित</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">संख्या</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">की</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">माध्यिका</span><span style=\"font-family:Cambria Math\"> 55 </span><span style=\"font-family:Nirmala UI\">और</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">माध्य</span><span style=\"font-family:Cambria Math\"> 58 </span><span style=\"font-family:Nirmala UI\">है।</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">बहुलक</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">ज्ञात</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">कीजिए</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">।</span></p>",
                    options_en: [" <p> 49</span></p>", " <p> 52</span></p>", 
                                " <p> 51</span></p>", " <p> 50</span></p>"],
                    options_hi: [" <p> 49</span></p>", " <p> 52</span></p>",
                                " <p> 51</span></p>", " <p> 50</span></p>"],
                    solution_en: " <p>14.(a)</span></p> <p><span style=\"font-family:Cambria Math\">We know,</span></p> <p><span style=\"font-family:Cambria Math\">Mode = 3 × median - 2 × mean</span></p> <p><span style=\"font-family:Cambria Math\">Mode = 3 × 55 - 2 × 58</span></p> <p><span style=\"font-family:Cambria Math\">Mode = 165 - 116 = 49</span></p>",
                    solution_hi: " <p>14.(a)</span></p> <p><span style=\"font-family:Nirmala UI\">हम</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">जानते</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">है</span><span style=\"font-family:Cambria Math\">,</span></p> <p><span style=\"font-family:Nirmala UI\">बहुलक</span><span style=\"font-family:Cambria Math\"> = 3 × </span><span style=\"font-family:Nirmala UI\">माध्यिका</span><span style=\"font-family:Cambria Math\"> – 2 × </span><span style=\"font-family:Nirmala UI\">माध्य</span></p> <p><span style=\"font-family:Nirmala UI\">बहुलक</span><span style=\"font-family:Cambria Math\"> = 3 × 55 – 2 × 58</span></p> <p><span style=\"font-family:Nirmala UI\">बहुलक</span><span style=\"font-family:Cambria Math\"> = 165 – 116 = 49</span></p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "15",
                    section: "misc",
                    question_en: " <p>15.</span><span style=\"font-family:Cambria Math\"> First find the median of 46, 64, 87, 41, 58, 77, 35, 90, 55, 92, and 33. If 92 is replaced with 99 and 41 is replaced with 43 in the data, then find the new median. What is the difference between the old median and the new median?</span></p>",
                    question_hi: " <p>15.</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">पहले</span><span style=\"font-family:Cambria Math\"> 46, 64, 87, 41, 58, 77, 35, 90, 55, 92 </span><span style=\"font-family:Nirmala UI\">और</span><span style=\"font-family:Cambria Math\"> 33 </span><span style=\"font-family:Nirmala UI\">की</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">माध्यिका</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">ज्ञात</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">कीजिए।</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">यदि</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">आंकड़ों</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">में</span><span style=\"font-family:Cambria Math\"> 92 </span><span style=\"font-family:Nirmala UI\">को</span><span style=\"font-family:Cambria Math\"> 99 </span><span style=\"font-family:Nirmala UI\">से</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">और</span><span style=\"font-family:Cambria Math\"> 41 </span><span style=\"font-family:Nirmala UI\">को</span><span style=\"font-family:Cambria Math\"> 43 </span><span style=\"font-family:Nirmala UI\">से</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">प्रतिस्थापित</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">कर</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">दिया</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">जाए</span><span style=\"font-family:Cambria Math\">, </span><span style=\"font-family:Nirmala UI\">तो</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">नई</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">माध्यिका</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">ज्ञात</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">कीजिए।</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">पुरानी</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">माध्यिका</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">और</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">नई</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">माध्यिका</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">क्या</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">अंतर</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">है</span><span style=\"font-family:Cambria Math\">?</span></p>",
                    options_en: [" <p> 2</span></p>", " <p> 3</span></p>", 
                                " <p> 0</span></p>", " <p> 6</span></p>"],
                    options_hi: [" <p> 2</span></p>", " <p> 3</span></p>",
                                " <p> 0</span></p>", " <p> 6</span></p>"],
                    solution_en: " <p>15.(c)</span></p> <p><span style=\"font-family:Cambria Math\">Arranging in ascending order,</span></p> <p><span style=\"font-family:Cambria Math\">33, 35, 41, 46, 55, 58, 64, 77, 87, 90, 92</span></p> <p><span style=\"font-family:Cambria Math\">Median = 5th term = 58</span></p> <p><span style=\"font-family:Cambria Math\">After arranging new pattern, </span></p> <p><span style=\"font-family:Cambria Math\">33 , 35, 43, 46, 55, 58, 64, 77, 87, 90, 99</span></p> <p><span style=\"font-family:Cambria Math\">New median = 5th term = 58</span></p> <p><span style=\"font-family:Cambria Math\">Now, old median - new median = 55 - 55 = 0</span></p> <p><span style=\"font-family:Cambria Math\"> </span></p>",
                    solution_hi: " <p>15.(c)</span></p> <p><span style=\"font-family:Nirmala UI\">आरोही</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">क्रम</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">व्यवस्थित</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">करने</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">पर</span><span style=\"font-family:Cambria Math\">,</span></p> <p><span style=\"font-family:Cambria Math\">33, 35, 41, 46, 55, 58, 64, 77, 87, 90, 92</span></p> <p><span style=\"font-family:Nirmala UI\">माध्यिका</span><span style=\"font-family:Cambria Math\"> = 5</span><span style=\"font-family:Nirmala UI\">वाँ</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">पद</span><span style=\"font-family:Cambria Math\"> = 58</span></p> <p><span style=\"font-family:Nirmala UI\">नए</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">पैटर्न</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">की</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">व्यवस्था</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">करने</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">बाद</span><span style=\"font-family:Cambria Math\">,</span></p> <p><span style=\"font-family:Cambria Math\">33 , 35, 43, 46, 55, 58, 64, 77, 87, 90, 99</span></p> <p><span style=\"font-family:Nirmala UI\">नया</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">माध्यिका</span><span style=\"font-family:Cambria Math\"> = 5</span><span style=\"font-family:Nirmala UI\">वा</span><span style=\"font-family:Nirmala UI\">ँ</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">पद</span><span style=\"font-family:Cambria Math\"> = 58</span></p> <p><span style=\"font-family:Nirmala UI\">अब</span><span style=\"font-family:Cambria Math\">, </span><span style=\"font-family:Nirmala UI\">पुरानी</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">माध्यिका</span><span style=\"font-family:Cambria Math\"> - </span><span style=\"font-family:Nirmala UI\">नई</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">माध्यिका</span><span style=\"font-family:Cambria Math\"> = 55 - 55 = 0</span></p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>