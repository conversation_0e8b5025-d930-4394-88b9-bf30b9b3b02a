<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">15:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 15</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">15</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 15 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 0,
                end: 13
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 14,
                end: 14
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "17",
                    question_en: "<p>1. If p : q = 1 : 3 and r: s = 2p : 3q, then what is the value of pr : qs ?</p>",
                    question_hi: "<p>1. यदि p : q = 1 : 3 और r : s = 2p : 3q है, तो pr : qs का मान कितना है?</p>",
                    options_en: ["<p>27 : 2</p>", "<p>2 : 25</p>", 
                                "<p>2 : 29</p>", "<p>2 : 27</p>"],
                    options_hi: ["<p>27 : 2</p>", "<p>2 : 25</p>",
                                "<p>2 : 29</p>", "<p>2 : 27</p>"],
                    solution_en: "<p>1.(d) according to question,<br><math display=\"inline\"><mfrac><mrow><mi>p</mi></mrow><mrow><mi>q</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>3</mn></mfrac></math>&nbsp;and <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>r</mi><mi>s</mi></mfrac><mo>=</mo><mfrac><mrow><mn>2</mn><mi>p</mi></mrow><mrow><mn>3</mn><mi>q</mi></mrow></mfrac></math><br><math display=\"inline\"><mfrac><mrow><mi>r</mi></mrow><mrow><mi>s</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><mo>&#215;</mo><mn>1</mn></mrow><mrow><mn>3</mn><mo>&#215;</mo><mn>3</mn></mrow></mfrac><mo>=</mo><mfrac><mn>2</mn><mn>9</mn></mfrac></math><br>Therefore , <math display=\"inline\"><mfrac><mrow><mi>p</mi><mi>r</mi></mrow><mrow><mi>q</mi><mi>s</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>9</mn></mfrac><mo>&#215;</mo><mfrac><mn>1</mn><mn>3</mn></mfrac><mo>=</mo><mfrac><mn>2</mn><mn>27</mn></mfrac></math><br>Hence required ratio = 2 : 27</p>",
                    solution_hi: "<p>1.(d) प्रश्न के अनुसार,<br>.<math display=\"inline\"><mfrac><mrow><mi>p</mi></mrow><mrow><mi>q</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>3</mn></mfrac></math>&nbsp;और&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>r</mi><mi>s</mi></mfrac><mo>=</mo><mfrac><mrow><mn>2</mn><mi>p</mi></mrow><mrow><mn>3</mn><mi>q</mi></mrow></mfrac></math><br><math display=\"inline\"><mfrac><mrow><mi>r</mi></mrow><mrow><mi>s</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><mo>&#215;</mo><mn>1</mn></mrow><mrow><mn>3</mn><mo>&#215;</mo><mn>3</mn></mrow></mfrac><mo>=</mo><mfrac><mn>2</mn><mn>9</mn></mfrac></math><br>इसलिए , <math display=\"inline\"><mfrac><mrow><mi>p</mi><mi>r</mi></mrow><mrow><mi>q</mi><mi>s</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>9</mn></mfrac><mo>&#215;</mo><mfrac><mn>1</mn><mn>3</mn></mfrac><mo>=</mo><mfrac><mn>2</mn><mn>27</mn></mfrac></math><br>अतः आवश्यक अनुपात = 2 : 27</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "2",
                    section: "17",
                    question_en: "<p>2. If G : H : J = 5 : 3 : 2 and J : K = 2 : 5, then what is the value of G : K?</p>",
                    question_hi: "<p>2. यदि G : H : J = 5 : 3 : 2 और J : K = 2 : 5 है, तो G : K का मान कितना है?</p>",
                    options_en: ["<p>1 : 1</p>", "<p>1 : 2</p>", 
                                "<p>3 : 1</p>", "<p>2 : 1</p>"],
                    options_hi: ["<p>1 : 1</p>", "<p>1 : 2</p>",
                                "<p>3 : 1</p>", "<p>2 : 1</p>"],
                    solution_en: "<p>2.(a)<br><strong>G&nbsp; &nbsp; :&nbsp; &nbsp; H&nbsp; &nbsp; :&nbsp; &nbsp; J&nbsp; &nbsp; :&nbsp; &nbsp; k</strong><br>5&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; 3&nbsp; &nbsp; :&nbsp; &nbsp; &nbsp;2&nbsp; &nbsp;:&nbsp; &nbsp;<strong> 2</strong><br><strong>2&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; 2</strong>&nbsp; &nbsp; :&nbsp; &nbsp; &nbsp;2&nbsp; &nbsp;:&nbsp; &nbsp; 5<br>---------------------------------<br>10&nbsp; &nbsp;:&nbsp; &nbsp; 6&nbsp; &nbsp;:&nbsp; &nbsp; &nbsp; 4&nbsp; &nbsp;:&nbsp; &nbsp;10<br>So, G : K = 10 : 10 = 1 : 1</p>",
                    solution_hi: "<p>2.(a)<br><strong>G&nbsp; &nbsp; :&nbsp; &nbsp; H&nbsp; &nbsp; :&nbsp; &nbsp; J&nbsp; &nbsp; :&nbsp; &nbsp; k</strong><br>5&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; 3&nbsp; &nbsp; :&nbsp; &nbsp; &nbsp;2&nbsp; &nbsp;:&nbsp; &nbsp;<strong> 2</strong><br><strong>2&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; 2</strong>&nbsp; &nbsp; :&nbsp; &nbsp; &nbsp;2&nbsp; &nbsp;:&nbsp; &nbsp; 5<br>---------------------------------<br>10&nbsp; &nbsp;:&nbsp; &nbsp; 6&nbsp; &nbsp;:&nbsp; &nbsp; &nbsp; 4&nbsp; &nbsp;:&nbsp; &nbsp;10<br>So, G : K = 10 : 10 = 1 : 1</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "3",
                    section: "17",
                    question_en: "<p>3. Which of the following ratios is the smallest?<br>A) 8 : 9<br>B) 2 : 5<br>C) 7 : 11</p>",
                    question_hi: "<p>3. निम्नलिखित में से कौन-सा अनुपात सबसे छोटा है?<br>A) 8 : 9<br>B) 2 : 5<br>C) 7 : 11</p>",
                    options_en: ["<p>Both A and C</p>", "<p>A</p>", 
                                "<p>B</p>", "<p>C</p>"],
                    options_hi: ["<p>A और C दोनों</p>", "<p>A</p>",
                                "<p>B</p>", "<p>C</p>"],
                    solution_en: "<p>3.(c)<br>8 : 9 = <math display=\"inline\"><mfrac><mrow><mn>8</mn></mrow><mrow><mn>9</mn></mrow></mfrac></math> = 0.88<br>2 : 5 = <math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> = 0.4<br>7 : 11 = <math display=\"inline\"><mfrac><mrow><mn>7</mn></mrow><mrow><mn>11</mn></mrow></mfrac></math> = 0.63<br>So, least ratio is 2 : 5</p>",
                    solution_hi: "<p>3.(c)<br>8 : 9 = <math display=\"inline\"><mfrac><mrow><mn>8</mn></mrow><mrow><mn>9</mn></mrow></mfrac></math> = 0.88<br>2 : 5 = <math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> = 0.4<br>7 : 11 = <math display=\"inline\"><mfrac><mrow><mn>7</mn></mrow><mrow><mn>11</mn></mrow></mfrac></math> = 0.63<br>तो, न्यूनतम अनुपात 2 : 5 है</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "4",
                    section: "17",
                    question_en: "<p>4. The ratio of income of Ramesh and Suresh are 3 : 4. The ratio of expenditure of Ramesh and Suresh is 2 : 3 and each saves Rs 200. Find the income of Ramesh and Suresh.</p>",
                    question_hi: "<p>4. रमेश और सुरेश की आय का अनुपात 3 : 4 है। रमेश और सुरेश के व्यय का अनुपात 2 : 3 है और प्रत्येक 200 रुपए बचाता है। रमेश और सुरेश की आय ज्ञात कीजिए।</p>",
                    options_en: ["<p>Rs. 700 and Rs. 800</p>", "<p>Rs. 600 and Rs. 800</p>", 
                                "<p>Rs. 800 and Rs. 500</p>", "<p>Rs. 750 and Rs. 400</p>"],
                    options_hi: ["<p>700 रुपए और 800 रुपए</p>", "<p>600 रुपए और 800 रुपए</p>",
                                "<p>800 रुपए और 500 रुपए</p>", "<p>750 रुपए और 400 रुपए</p>"],
                    solution_en: "<p>4.(b)<br>Ratio - Ramesh : Suresh<br>Income -&nbsp; &nbsp; &nbsp; &nbsp; 3&nbsp; :&nbsp; &nbsp; 4<br>Expenditure - 2&nbsp; :&nbsp; &nbsp; 3<br>&mdash;------------------------------<br>Savings -&nbsp; &nbsp; &nbsp; &nbsp; 1&nbsp; :&nbsp; &nbsp; 1 (when savings ratio is equal then)<br>1 units = 200 Rs<br>Ramesh&rsquo;s income (3 units) = <math display=\"inline\"><mfrac><mrow><mn>200</mn></mrow><mrow><mn>1</mn></mrow></mfrac></math> &times; 3 = Rs. 600<br>Suresh&rsquo;s income (4 units) = <math display=\"inline\"><mfrac><mrow><mn>200</mn></mrow><mrow><mn>1</mn></mrow></mfrac></math> &times; 4 = Rs. 800</p>",
                    solution_hi: "<p>4.(b)<br>अनुपात - रमेश&nbsp; :&nbsp; सुरेश<br>आय -&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;3&nbsp; &nbsp; :&nbsp; &nbsp;4<br>व्यय -&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;2&nbsp; &nbsp; :&nbsp; &nbsp;3<br>-----------------------------<br>बचत -&nbsp; &nbsp; &nbsp; &nbsp; 1&nbsp; &nbsp; :&nbsp; &nbsp;1 (जब बचत का अनुपात बराबर हो तो)<br>1 इकाई = 200 Rs<br>रमेश की आय (3 इकाई ) = <math display=\"inline\"><mfrac><mrow><mn>200</mn></mrow><mrow><mn>1</mn></mrow></mfrac></math> &times; 3 = Rs. 600<br>सुरेश की आय (4 इकाई ) = <math display=\"inline\"><mfrac><mrow><mn>200</mn></mrow><mrow><mn>1</mn></mrow></mfrac></math> &times; 4 = Rs. 800</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "5",
                    section: "17",
                    question_en: "<p>5. Marks of two candidates J and K are the in ratio of 15 : 17. If the marks of J are 300, then what will be the marks of K ?</p>",
                    question_hi: "<p>5. दो परीक्षार्थियों, J और K के अंक 15 : 17 के अनुपात में हैं। यदि J के अंक 300 हैं, तो K के अंक कितने होंगे?</p>",
                    options_en: ["<p>310</p>", "<p>320</p>", 
                                "<p>340</p>", "<p>300</p>"],
                    options_hi: ["<p>310</p>", "<p>320</p>",
                                "<p>340</p>", "<p>300</p>"],
                    solution_en: "<p>5.(c)<br>According to the question,<br>(J&rsquo;s marks) 15 units = 300<br>1 units = 20<br>(K&rsquo;s marks) 17 units = 20 &times; 17 = 340 </p>",
                    solution_hi: "<p>5.(c)<br>प्रश्न के अनुसार,<br>(J के अंक) 15 इकाई = 300<br>1 इकाई = 20<br>(K के अंक) 17 इकाई = 20 &times; 17 = 340</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "6",
                    section: "17",
                    question_en: "<p>6. The respective ratio of income and expenditure of Raman is 9 : 7. If his income is increased by 20 percent and expenditure is decreased by 70 percent, then what will be the new respective ratio of his income and expenditure?</p>",
                    question_hi: "<p>6. रमन की आय और व्यय का क्रमशः अनुपात 9 : 7 है। यदि उसकी आय में 20 प्रतिशत की वृद्धि होती है और व्यय में 70 प्रतिशत की कमी होती है, तो उसकी आय और व्यय का नया क्रमशः अनुपात क्या होगा?</p>",
                    options_en: ["<p>35 : 7</p>", "<p>36 : 7</p>", 
                                "<p>7 : 36</p>", "<p>7 : 35</p>"],
                    options_hi: ["<p>35 : 7</p>", "<p>36 : 7</p>",
                                "<p>7 : 36</p>", "<p>7 : 35</p>"],
                    solution_en: "<p>6.(b) <br>Ratio - income&nbsp; : expenditure <br>Before -&nbsp; &nbsp; &nbsp;9&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp; 7 <br>After -&nbsp; &nbsp; &nbsp;10.8&nbsp; &nbsp;:&nbsp; &nbsp; &nbsp;2.1&nbsp;&nbsp;<br>New ratio of income and expenditure = 10.8 : 2.1 = 36 : 7</p>",
                    solution_hi: "<p>6.(b) <br>अनुपात - आय&nbsp; :&nbsp; व्यय<br>पहले-&nbsp; &nbsp; &nbsp; &nbsp; 9&nbsp; &nbsp; :&nbsp; &nbsp;7 <br>बाद में - 10.8&nbsp; &nbsp;:&nbsp; &nbsp;2.1<br>आय और व्यय का नया क्रमशः अनुपात = 10.8 : 2.1 = 36 : 7</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "7",
                    section: "17",
                    question_en: "<p>7. Find the fraction which bears the same ratio to <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>64</mn></mrow></mfrac></math> that&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>7</mn></mfrac></math> does to <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>16</mn></mfrac></math>.</p>",
                    question_hi: "<p>7. निम्नलिखित में से वह भिन्न ज्ञात कीजिए जो <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>64</mn></mrow></mfrac></math> से उसी अनुपात में है, जिस अनुपात में&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>7</mn></mfrac></math>, <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>16</mn></mfrac></math> से अनुपात में है।</p>",
                    options_en: ["<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>25</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>20</mn></mrow></mfrac></math></p>", 
                                "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>35</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>40</mn></mrow></mfrac></math></p>"],
                    options_hi: ["<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>25</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>20</mn></mrow></mfrac></math></p>",
                                "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>35</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>40</mn></mrow></mfrac></math></p>"],
                    solution_en: "<p>7.(c) according to question, <br><math display=\"inline\"><mo>&#8658;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mstyle displaystyle=\"true\"><mfrac><mi>x</mi><mn>1</mn></mfrac></mstyle><mstyle displaystyle=\"true\"><mfrac><mn>1</mn><mn>64</mn></mfrac></mstyle></mfrac><mo>=</mo><mfrac><mstyle displaystyle=\"true\"><mfrac><mn>4</mn><mn>7</mn></mfrac></mstyle><mstyle displaystyle=\"true\"><mfrac><mn>5</mn><mn>16</mn></mfrac></mstyle></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math> x = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mfrac><mn>1</mn><mn>64</mn></mfrac><mo>&#215;</mo><mfrac><mn>4</mn><mn>7</mn></mfrac></mrow><mstyle displaystyle=\"true\"><mfrac><mn>5</mn><mn>16</mn></mfrac></mstyle></mfrac><mo>=</mo><mfrac><mn>1</mn><mn>35</mn></mfrac></math> .</p>",
                    solution_hi: "<p>7.(c) प्रश्न के अनुसार, <br><math display=\"inline\"><mo>&#8658;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mstyle displaystyle=\"true\"><mfrac><mi>x</mi><mn>1</mn></mfrac></mstyle><mstyle displaystyle=\"true\"><mfrac><mn>1</mn><mn>64</mn></mfrac></mstyle></mfrac><mo>=</mo><mfrac><mstyle displaystyle=\"true\"><mfrac><mn>4</mn><mn>7</mn></mfrac></mstyle><mstyle displaystyle=\"true\"><mfrac><mn>5</mn><mn>16</mn></mfrac></mstyle></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math> x = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mfrac><mn>1</mn><mn>64</mn></mfrac><mo>&#215;</mo><mfrac><mn>4</mn><mn>7</mn></mfrac></mrow><mstyle displaystyle=\"true\"><mfrac><mn>5</mn><mn>16</mn></mfrac></mstyle></mfrac><mo>=</mo><mfrac><mn>1</mn><mn>35</mn></mfrac></math>&nbsp;</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "8",
                    section: "17",
                    question_en: "<p>8. In a muffin recipe, the ratio of flour and sugar is 3 : 2, and that of sugar and butter is 4 : 3. What is the ratio of flour and butter in the recipe ?</p>",
                    question_hi: "<p>8. मफिन की एक रेसिपी में, आटे और चीनी का अनुपात 3 : 2 है, तथा चीनी और मक्खन का अनुपात 4 : 3 है। रेसिपी में आटे और मक्खन का अनुपात कितना है?</p>",
                    options_en: ["<p>1 : 3</p>", "<p>2 : 1</p>", 
                                "<p>1 : 2</p>", "<p>3 : 1</p>"],
                    options_hi: ["<p>1 : 3</p>", "<p>2 : 1</p>",
                                "<p>1 : 2</p>", "<p>3 : 1</p>"],
                    solution_en: "<p>8.(b)<br>flour : sugar : butter<br>3&nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp;2&nbsp; &nbsp; :&nbsp; &nbsp; <strong>2</strong><br><strong>4</strong>&nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp;4&nbsp; &nbsp; :&nbsp; &nbsp; 3<br>---------------------------------<br><strong>12&nbsp; &nbsp; :&nbsp; &nbsp; &nbsp;8&nbsp; &nbsp; :&nbsp; &nbsp; 6</strong><br>Hence, required ratio = 12 : 6 = 2 : 1</p>",
                    solution_hi: "<p>8.(b)<br>आटा&nbsp; :&nbsp; &nbsp;चीनी&nbsp; &nbsp;:&nbsp; मक्खन<br>&nbsp;3&nbsp; &nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp;2&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; <strong>2</strong><br><strong>&nbsp;4</strong>&nbsp; &nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp;4&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; 3<br>---------------------------------<br><strong>&nbsp;12&nbsp; &nbsp; :&nbsp; &nbsp; &nbsp;8&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; 6</strong><br>अतः, आवश्यक अनुपात = 12 : 6 = 2 : 1</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "9",
                    section: "17",
                    question_en: "<p>9. Two numbers are in the ratio 6 : 17. If the sum of the two numbers is 92, then what is the difference between the two numbers ?</p>",
                    question_hi: "<p>9. दो संख्याएं 6 : 17 के अनुपात में हैं। यदि दोनों संख्याओं का योग 92 है, तो दोनों संख्याओं के बीच का अंतर कितना है ?</p>",
                    options_en: ["<p>66</p>", "<p>77</p>", 
                                "<p>44</p>", "<p>55</p>"],
                    options_hi: ["<p>66</p>", "<p>77</p>",
                                "<p>44</p>", "<p>55</p>"],
                    solution_en: "<p>9.(c)<br>Let numbers be 6<math display=\"inline\"><mi>x</mi></math> and 17x<br>According to the question,<br>6<math display=\"inline\"><mi>x</mi></math> + 17x = 92 <br>23<math display=\"inline\"><mi>x</mi></math> = 92 &rArr; x = 4<br>Hence, required difference = 11<math display=\"inline\"><mi>x</mi></math> = 44</p>",
                    solution_hi: "<p>9.(c)<br>माना संख्याएँ 6<math display=\"inline\"><mi>x</mi></math> और 17x हैं<br>प्रश्न के अनुसार,<br>6<math display=\"inline\"><mi>x</mi></math> + 17x = 92 <br>23<math display=\"inline\"><mi>x</mi></math> = 92 &rArr; x = 4<br>अतः, अभीष्ट अंतर = 11<math display=\"inline\"><mi>x</mi></math> = 44</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "10",
                    section: "17",
                    question_en: "<p>10. If X : Y = 8 : 5 and X - Y = 18, find the value of X + Y.</p>",
                    question_hi: "<p>10. यदि X : Y = 8 : 5 और X - Y = 18 है, तो X + Y का मान ज्ञात कीजिए।</p>",
                    options_en: ["<p>70</p>", "<p>74</p>", 
                                "<p>78</p>", "<p>72</p>"],
                    options_hi: ["<p>70</p>", "<p>74</p>",
                                "<p>78</p>", "<p>72</p>"],
                    solution_en: "<p>10.(c)<br>Let the X and Y = 8a and 5a<br>8a - 5a = 18<br>a = 6<br>So the value of X and Y = 48 and 30<br>Then X + Y = 48 + 30 = 78</p>",
                    solution_hi: "<p>10.(c)<br>माना कि X और Y = 8a और 5a हैं<br>8a - 5a = 18<br>a = 6<br>तो X और Y का मान = 48 और 30<br>तब X + Y = 48 + 30 = 78</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "11",
                    section: "17",
                    question_en: "<p>11. Rs. 7700 is divided among A, B and C such that 4 times of A&rsquo;s share is equal to 2 times of B&rsquo;s share which is equal to 6 times of C&rsquo;s share. What is the share of B ?</p>",
                    question_hi: "<p>11. 7700 रुपए को A, B और C के बीच इस प्रकार बांटा जाता है कि A के हिस्से का 4 गुना B के हिस्से के 2 गुना के बराबर है, जो कि C के हिस्से के 6 गुना के बराबर है। B का हिस्सा कितना है?</p>",
                    options_en: ["<p>Rs.2100</p>", "<p>Rs.1400</p>", 
                                "<p>Rs.4200</p>", "<p>Rs.5600</p>"],
                    options_hi: ["<p>2100 रुपए</p>", "<p>1400 रुपए</p>",
                                "<p>4200 रुपए</p>", "<p>5600 रुपए</p>"],
                    solution_en: "<p>11.(c)<br>According to the question,<br>4A = 2B = 6C<br>On compare of A and B<br><math display=\"inline\"><mfrac><mrow><mi>A</mi></mrow><mrow><mi>B</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>4</mn></mfrac><mo>=</mo><mfrac><mn>1</mn><mn>2</mn></mfrac></math><br>On compare of B and C<br><math display=\"inline\"><mfrac><mrow><mi>B</mi></mrow><mrow><mi>C</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>6</mn><mn>2</mn></mfrac><mo>=</mo><mfrac><mn>3</mn><mn>1</mn></mfrac></math><br>Ratio - A&nbsp; :&nbsp; B&nbsp; : C<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 1&nbsp; :&nbsp; 2&nbsp; : 2<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 3&nbsp; :&nbsp; 3&nbsp; : 1<br>----------------------------<br>Final -&nbsp; 3&nbsp; &nbsp;:&nbsp; 6&nbsp; : 2<br>Share of B = 7700 &times; <math display=\"inline\"><mfrac><mrow><mn>6</mn></mrow><mrow><mo>(</mo><mn>3</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>6</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>2</mn><mo>)</mo></mrow></mfrac></math> = 4200 Rs</p>",
                    solution_hi: "<p>11.(c)<br>प्रश्र के अनुसार,<br>4A = 2B = 6C<br>A और B की तुलना करने पर<br><math display=\"inline\"><mfrac><mrow><mi>A</mi></mrow><mrow><mi>B</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>4</mn></mfrac><mo>=</mo><mfrac><mn>1</mn><mn>2</mn></mfrac></math><br>B और C की तुलना करने पर<br><math display=\"inline\"><mfrac><mrow><mi>B</mi></mrow><mrow><mi>C</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>6</mn><mn>2</mn></mfrac><mo>=</mo><mfrac><mn>3</mn><mn>1</mn></mfrac></math>,<br>अनुपात - A&nbsp; :&nbsp; B&nbsp; : C<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 1&nbsp; :&nbsp; 2&nbsp; : 2<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 3&nbsp; :&nbsp; 3&nbsp; : 1<br>----------------------------<br>अंतिम -&nbsp; &nbsp;3&nbsp; &nbsp;:&nbsp; 6&nbsp; : 2<br>B का हिस्सा = 7700 &times; <math display=\"inline\"><mfrac><mrow><mn>6</mn></mrow><mrow><mo>(</mo><mn>3</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>6</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>2</mn><mo>)</mo></mrow></mfrac></math> = 4200 Rs</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "12",
                    section: "17",
                    question_en: "<p>12. If a : b = 4 : 5 and b : c = 3 : 8, then find a : b : c.</p>",
                    question_hi: "<p>12. यदि a : b = 4 : 5 और b : c = 3 : 8 है, तो a : b : c ज्ञात कीजिए I</p>",
                    options_en: ["<p>12 : 15 : 28</p>", "<p>12 : 10 : 25</p>", 
                                "<p>12 : 15 : 40</p>", "<p>10 : 15 : 22</p>"],
                    options_hi: ["<p>12 : 15 : 28</p>", "<p>12 : 10 : 25</p>",
                                "<p>12 : 15 : 40</p>", "<p>10 : 15 : 22</p>"],
                    solution_en: "<p>12.(c)<br>a&nbsp; &nbsp;:&nbsp; &nbsp; b&nbsp; &nbsp; :&nbsp; &nbsp;c<br>4&nbsp; &nbsp;:&nbsp; &nbsp; 5&nbsp; &nbsp; :&nbsp; &nbsp;<strong>5</strong><br><strong>3&nbsp; &nbsp;</strong>:&nbsp; &nbsp; 3&nbsp; &nbsp; :&nbsp; &nbsp;8<br>--------------------<br><strong>12&nbsp; :&nbsp; 15&nbsp; &nbsp;:&nbsp; 40</strong><br>Hence, required ratio = 12 : 15 : 40</p>",
                    solution_hi: "<p>12.(c)<br>a&nbsp; &nbsp;:&nbsp; &nbsp; b&nbsp; &nbsp; :&nbsp; &nbsp;c<br>4&nbsp; &nbsp;:&nbsp; &nbsp; 5&nbsp; &nbsp; :&nbsp; &nbsp;<strong>5</strong><br><strong>3&nbsp; &nbsp;</strong>:&nbsp; &nbsp; 3&nbsp; &nbsp; :&nbsp; &nbsp;8<br>--------------------<br><strong>12&nbsp; :&nbsp; 15&nbsp; &nbsp;:&nbsp; 40</strong><br>अतः, आवश्यक अनुपात = 12 : 15 : 40</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "13",
                    section: "17",
                    question_en: "<p>13. If p : q : r = 2 : 3 : 5, then what is the value of (<math display=\"inline\"><mfrac><mrow><mi>p</mi></mrow><mrow><mn>2</mn></mrow></mfrac></math>) : (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>q</mi><mn>6</mn></mfrac></math>) : (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>r</mi><mn>5</mn></mfrac></math>) ?</p>",
                    question_hi: "<p>13. यदि p : q : r = 2 : 3 : 5, है, तो (<math display=\"inline\"><mfrac><mrow><mi>p</mi></mrow><mrow><mn>2</mn></mrow></mfrac></math>) : (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>q</mi><mn>6</mn></mfrac></math>) : (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>r</mi><mn>5</mn></mfrac></math>) का मान कितना है ?</p>",
                    options_en: ["<p>4 : 2 : 5</p>", "<p>1 : 1 : 1</p>", 
                                "<p>2 : 1 : 2</p>", "<p>3 : 1 : 3</p>"],
                    options_hi: ["<p>4 : 2 : 5</p>", "<p>1 : 1 : 1</p>",
                                "<p>2 : 1 : 2</p>", "<p>3 : 1 : 3</p>"],
                    solution_en: "<p>13.(c)<br>Required value = (<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math>) : (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>6</mn></mfrac></math>) : (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>5</mn></mfrac></math>) = (1 :&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math>: 1)<br>= 2 : 1 : 2</p>",
                    solution_hi: "<p>13.(c)<br>अभीष्ट मान = (<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math>) : (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>6</mn></mfrac></math>) : (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>5</mn></mfrac></math>) = (1 :&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math>: 1)<br>= 2 : 1 : 2</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "14",
                    section: "17",
                    question_en: "<p>14. Three numbers are in the ratio 4 : 5 : 6. If the sum of the largest and the smallest number is 6000, then what is the difference between the largest and the smallest number ?</p>",
                    question_hi: "<p>14. तीन संख्याएं 4 : 5 : 6 के अनुपात में हैं। यदि सबसे बड़ी और सबसे छोटी संख्या का योग 6000 है, तो सबसे बड़ी और सबसे छोटी संख्या का अंतर कितना है ?</p>",
                    options_en: ["<p>600</p>", "<p>2400</p>", 
                                "<p>1800</p>", "<p>1200</p>"],
                    options_hi: ["<p>600</p>", "<p>2400</p>",
                                "<p>1800</p>", "<p>1200</p>"],
                    solution_en: "<p>14.(d) <br>Let numbers be 4<math display=\"inline\"><mi>x</mi></math> , 5x and 6x <br>According to the question,<br>6<math display=\"inline\"><mi>x</mi></math> + 4x = 6000<br>10<math display=\"inline\"><mi>x</mi></math> = 6000 &rArr; x = 600<br>Hence, required difference = 6<math display=\"inline\"><mi>x</mi></math> - 4x = 2x = 1200</p>",
                    solution_hi: "<p>14.(d) <br>माना संख्याएँ 4<math display=\"inline\"><mi>x</mi></math> , 5x और 6x हैं<br>प्रश्न के अनुसार,<br>6<math display=\"inline\"><mi>x</mi></math> + 4x = 6000<br>10<math display=\"inline\"><mi>x</mi></math> = 6000 &rArr; x = 600<br>अतः, आवश्यक अंतर = 6<math display=\"inline\"><mi>x</mi></math> - 4x = 2x = 1200</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "15",
                    section: "misc",
                    question_en: "<p>15. If a : b, is 5 : 6 and b : c is 2 : 3 then find the value of a : b : c ?</p>",
                    question_hi: "<p>15. यदि a : b, 5 : 6 है और b: c, 2 : 3 है, तो a : b : c ज्ञात कीजिए।</p>",
                    options_en: ["<p>2 : 6 : 5</p>", "<p>5 : 6 : 3</p>", 
                                "<p>5 : 6 : 9</p>", "<p>5 : 6 : 7</p>"],
                    options_hi: ["<p>2 : 6 : 5</p>", "<p>5 : 6 : 3</p>",
                                "<p>5 : 6 : 9</p>", "<p>5 : 6 : 7</p>"],
                    solution_en: "<p>15.(c)<br>a&nbsp; &nbsp; :&nbsp; &nbsp; b&nbsp; &nbsp; :&nbsp; &nbsp;c<br>5&nbsp; &nbsp; :&nbsp; &nbsp; 6&nbsp; &nbsp; :&nbsp; &nbsp;<strong>6</strong><br><strong>2&nbsp; &nbsp; </strong>:&nbsp; &nbsp; 2&nbsp; &nbsp; :&nbsp; &nbsp;3<br>----------------------<br><strong>10&nbsp; :&nbsp; &nbsp;12&nbsp; &nbsp;:&nbsp; &nbsp;18</strong><br>Hence, required ratio = 10 : 12 : 18 = 5 : 6 : 9</p>",
                    solution_hi: "<p>15.(c)<br>a&nbsp; &nbsp; :&nbsp; &nbsp; b&nbsp; &nbsp; :&nbsp; &nbsp;c<br>5&nbsp; &nbsp; :&nbsp; &nbsp; 6&nbsp; &nbsp; :&nbsp; &nbsp;<strong>6</strong><br><strong>2&nbsp; &nbsp; </strong>:&nbsp; &nbsp; 2&nbsp; &nbsp; :&nbsp; &nbsp;3<br>----------------------<br><strong>10&nbsp; :&nbsp; &nbsp;12&nbsp; &nbsp;:&nbsp; &nbsp;18</strong><br>अतः, आवश्यक अनुपात = 10 : 12 : 18 = 5 : 6 : 9</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>