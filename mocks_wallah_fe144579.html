<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">15:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 15</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">15</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 15 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 0,
                end: 13
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 14,
                end: 14
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "17",
                    question_en: "<p>1.<span style=\"font-family: Cambria Math;\"> If 23.5% of a number is 11.75, then what is the number?</span></p>\n",
                    question_hi: "<p>1. <span style=\"font-family: Nirmala UI;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> 23.5%, 11.75 </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2361;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>50</p>\n", "<p>60</p>\n", 
                                "<p>40</p>\n", "<p>48</p>\n"],
                    options_hi: ["<p>50</p>\n", "<p>60</p>\n",
                                "<p>40</p>\n", "<p>48</p>\n"],
                    solution_en: "<p>1.(a)</p>\r\n<p><span style=\"font-family: Cambria Math;\">Let the number be n.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">According to the question,</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">23.5% of n = 11.75</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>23</mn><mo>.</mo><mn>5</mn></mrow><mrow><mn>100</mn><mo>&nbsp;</mo></mrow></mfrac><mo>&times;</mo></math>n = 11.75</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">n = 50</span></p>\n",
                    solution_hi: "<p>1.(a)</p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> n </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2358;&#2381;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2344;&#2369;&#2360;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\">,</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">n </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> 23.5% = 11.75</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>23</mn><mo>.</mo><mn>5</mn></mrow><mrow><mn>100</mn><mo>&nbsp;</mo></mrow></mfrac><mo>&times;</mo></math>n = 11.75</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">n = 50</span></p>\n",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "2",
                    section: "17",
                    question_en: "<p>2. <span style=\"font-family: Cambria Math;\">Shubham secured 345 marks out of 600 in the annual examination. Find the percenta</span><span style=\"font-family: Cambria Math;\">ge of marks obtained by him in the examination.</span></p>\n",
                    question_hi: "<p>2. <span style=\"font-family: Nirmala UI;\">&#2358;&#2369;&#2349;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2366;&#2352;&#2381;&#2359;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;&#2368;&#2325;&#2381;&#2359;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> 600 </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> 345 </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2306;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2366;&#2346;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;&#2368;&#2325;&#2381;&#2359;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2360;&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2381;&#2357;&#2366;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2366;&#2346;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2306;&#2325;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2381;&#2334;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;&#2332;&#2367;&#2351;&#2375;&#2404;</span></p>\n",
                    options_en: ["<p>57.5</p>\n", "<p>57.25</p>\n", 
                                "<p>58.5</p>\n", "<p>58.25</p>\n"],
                    options_hi: ["<p>57.5</p>\n", "<p>57.25</p>\n",
                                "<p>58.5</p>\n", "<p>58.25</p>\n"],
                    solution_en: "<p>2.(a)</p>\r\n<p><span style=\"font-family: Cambria Math;\">Required percentage =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>345</mn><mn>600</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\">&times;100 = 57.5</span></p>\n",
                    solution_hi: "<p>2.(a)</p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2310;&#2357;&#2358;&#2381;&#2351;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340;</span><span style=\"font-family: Cambria Math;\"> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>345</mn><mn>600</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\">&times;100 = 57.5</span></p>\n",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "3",
                    section: "17",
                    question_en: " <p>3.</span><span style=\"font-family:Cambria Math\"> Ravi has to go from Hyderabad to Delhi. The distance between Hyderabad and Delhi is 1,200 kms. He decides to travel 25% of the d</span><span style=\"font-family:Cambria Math\">istance on foot , 30% of the distance by bus,and 15% of the distance by train and remaining distance by an airplane. What is the distance travelled by Ravi by an Airplane?</span></p>",
                    question_hi: " <p>3.</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">रवि</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">को</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">हैदराबाद</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">से</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">दिल्ली</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">जा</span><span style=\"font-family:Nirmala UI\">ना</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">है।</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">हैदराबाद</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">और</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">दिल्ली</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">बीच</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">की</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">दूरी</span><span style=\"font-family:Cambria Math\"> 1,200 kms </span><span style=\"font-family:Nirmala UI\">है।</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">वह</span><span style=\"font-family:Cambria Math\"> 25% </span><span style=\"font-family:Nirmala UI\">दूरी</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">पैदल</span><span style=\"font-family:Cambria Math\">, 30% </span><span style=\"font-family:Nirmala UI\">दूरी</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">बस</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">से</span><span style=\"font-family:Cambria Math\">, 15% </span><span style=\"font-family:Nirmala UI\">दूरी</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">रेलगाड़ी</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">से</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">और</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">शेष</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">दूरी</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">हवाई</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">जहाज</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">से</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">तय</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">करने</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">का</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">निर्णय</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">करता</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">है।</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">रवि</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">द्वारा</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">हवाई</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">जहाज</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">से</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">तय</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">की</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">गई</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">दूरी</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">कितनी</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">है</span><span style=\"font-family:Cambria Math\">?</span></p>",
                    options_en: [" <p> 300 km</span></p>", " <p> 360 km</span></p>", 
                                " <p> 580 km</span></p>", " <p> 425 km</span></p>"],
                    options_hi: [" <p> 300 km</span></p>", " <p> 360 km</span></p>",
                                " <p> 580 km</span></p>", " <p> 425 km</span></p>"],
                    solution_en: " <p>3.(b)</span></p> <p><span style=\"font-family:Cambria Math\">Distance = 1200 km</span></p> <p><span style=\"font-family:Cambria Math\">Distance travelled by foot = 25% of 1200 = 300 km</span></p> <p><span style=\"font-family:Cambria Math\">Distance travelled by bus = 30% of 1200 = 360 km</span></p> <p><span style=\"font-family:Cambria Math\">Distance travelled by train = 15% of 1200 = 180 km</span></p> <p><span style=\"font-family:Cambria Math\">Remaining distance = 1200 - 300 - 360 - 180 = 360 km</span></p> <p><span style=\"font-family:Cambria Math\">So</span><span style=\"font-family:Cambria Math\">, distance travelled by Airplane = 360 km</span></p>",
                    solution_hi: " <p>3.(b)</span></p> <p><span style=\"font-family:Nirmala UI\">दूरी</span><span style=\"font-family:Cambria Math\"> = 1200 </span><span style=\"font-family:Nirmala UI\">किमी</span></p> <p><span style=\"font-family:Nirmala UI\">पैदल</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">तय</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">की</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">गई</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">दूरी</span><span style=\"font-family:Cambria Math\"> = 1200 </span><span style=\"font-family:Nirmala UI\">का</span><span style=\"font-family:Cambria Math\"> 25% = 300 </span><span style=\"font-family:Nirmala UI\">किमी</span></p> <p><span style=\"font-family:Nirmala UI\">बस</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">द्वारा</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">तय</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">की</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">गई</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">दूरी</span><span style=\"font-family:Cambria Math\"> = 1200 </span><span style=\"font-family:Nirmala UI\">का</span><span style=\"font-family:Cambria Math\"> 30% = 360 </span><span style=\"font-family:Nirmala UI\">किमी</span></p> <p><span style=\"font-family:Nirmala UI\">ट्रेन</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">द्वारा</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">तय</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">की</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">गई</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">दूरी</span><span style=\"font-family:Cambria Math\"> = 1200 </span><span style=\"font-family:Nirmala UI\">का</span><span style=\"font-family:Cambria Math\"> 15% = 180 </span><span style=\"font-family:Nirmala UI\">किमी</span></p> <p><span style=\"font-family:Nirmala UI\">शेष</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">दूरी</span><span style=\"font-family:Cambria Math\"> = 1200 - 300 - 360 - 180 = 36</span><span style=\"font-family:Cambria Math\">0 </span><span style=\"font-family:Nirmala UI\">किमी</span></p> <p><span style=\"font-family:Nirmala UI\">अत</span><span style=\"font-family:Cambria Math\">: </span><span style=\"font-family:Nirmala UI\">हवाई</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">जहाज</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">द्वारा</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">तय</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">की</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">गई</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">दूरी</span><span style=\"font-family:Cambria Math\"> = 360  </span><span style=\"font-family:Nirmala UI\">किमी</span></p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "4",
                    section: "17",
                    question_en: " <p>4. Vimal secured 46% marks in the exam and failed to qualify in the exam by 10 marks. If he secured 52% marks, he would have secured 8 marks more than what was the minimum qualifying marks. What wer</span><span style=\"font-family:Cambria Math\">e the minimum qualifying marks. What were the minimum marks one had to score to qualify in the exam?</span></p>",
                    question_hi: " <p>4. </span><span style=\"font-family:Nirmala UI\">विमल</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">किसी</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">परीक्षा</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">में</span><span style=\"font-family:Cambria Math\"> 46% </span><span style=\"font-family:Nirmala UI\">अंक</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">प्राप्त</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">करता</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">है</span><span style=\"font-family:Cambria Math\">, </span><span style=\"font-family:Nirmala UI\">फिर</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">भी</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">परीक्षा</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">में</span><span style=\"font-family:Cambria Math\"> 10 </span><span style=\"font-family:Nirmala UI\">अंक</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">से</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">अनुत्तीर्ण</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">हो</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">जाता</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">है।</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">यदि</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">वह</span><span style=\"font-family:Cambria Math\"> 52% </span><span style=\"font-family:Nirmala UI\">अंक</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">प्राप्</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">करता</span><span style=\"font-family:Cambria Math\">, </span><span style=\"font-family:Nirmala UI\">तो</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">उसे</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">न्यूनतम</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">योग्यता</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">अंकों</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">से</span><span style=\"font-family:Cambria Math\"> 8 </span><span style=\"font-family:Nirmala UI\">अंक</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">अधिक</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">प्राप्त</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">होते</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">।</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">परीक्षा</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">उत्तीर्ण</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">होने</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">लिए</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">आवश्यक</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">न्यूनतम</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">अंक</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">कितने</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">थे</span><span style=\"font-family:Cambria Math\">?</span></p>",
                    options_en: [" <p> 156</span></p>", " <p> 146</span></p>", 
                                " <p> 148</span></p>", " <p> 138</span></p>"],
                    options_hi: [" <p> 156</span></p>", " <p> 146</span></p>",
                                " <p> 148</span></p>", " <p> 138</span></p>"],
                    solution_en: " <p>4.(c) </span></p> <p><span style=\"font-family:Cambria Math\">Let the total marks = x</span></p> <p><span style=\"font-family:Cambria Math\">Passed marks = 46% of x + 10</span></p> <p><span style=\"font-family:Cambria Math\">Also,</span></p> <p><span style=\"font-family:Cambria Math\">Passed marks = 52% of x - 8</span></p> <p><span style=\"font-family:Cambria Math\">Now,</span></p> <p><span style=\"font-family:Cambria Math\">46% of x + 10 = 52% of x - 8</span></p> <p><span style=\"font-family:Cambria Math\">18 = 6% of x</span></p> <p><span style=\"font-family:Cambria Math\">x = 300</span></p> <p><span style=\"font-family:Cambria Math\">Qualifying marks = 46% of 300 + 10 = 148</span></p>",
                    solution_hi: " <p>4.(c)</span></p> <p><span style=\"font-family:Nirmala UI\">माना</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">कुल</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">अंक</span><span style=\"font-family:Cambria Math\"> = x</span></p> <p><span style=\"font-family:Nirmala UI\">उत्तीर्ण</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">अंक</span><span style=\"font-family:Cambria Math\"> = x  </span><span style=\"font-family:Nirmala UI\">का</span><span style=\"font-family:Cambria Math\"> 46% + 10</span></p> <p><span style=\"font-family:Nirmala UI\">ये</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">भी</span><span style=\"font-family:Cambria Math\">,</span></p> <p><span style=\"font-family:Nirmala UI\">उत्तीर्ण</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">अंक</span><span style=\"font-family:Cambria Math\"> = x </span><span style=\"font-family:Nirmala UI\">का</span><span style=\"font-family:Cambria Math\"> 52% - 8</span></p> <p><span style=\"font-family:Nirmala UI\">अब</span><span style=\"font-family:Cambria Math\">,</span></p> <p><span style=\"font-family:Cambria Math\">x </span><span style=\"font-family:Nirmala UI\">का</span><span style=\"font-family:Cambria Math\"> 46% + 10 = x  </span><span style=\"font-family:Nirmala UI\">का</span><span style=\"font-family:Cambria Math\"> 52% – 8</span></p> <p><span style=\"font-family:Cambria Math\">18 = x </span><span style=\"font-family:Nirmala UI\">का</span><span style=\"font-family:Cambria Math\"> 6% </span></p> <p><span style=\"font-family:Cambria Math\">x = 300</span></p> <p><span style=\"font-family:Nirmala UI\">अर्हक</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">अंक</span><span style=\"font-family:Cambria Math\"> = 300 </span><span style=\"font-family:Nirmala UI\">का</span><span style=\"font-family:Cambria Math\"> 46% + 10 = 148</span></p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "5",
                    section: "17",
                    question_en: "<p>5. 29.5% of a number is 0.59. What is that number?</p>\n",
                    question_hi: "<p>5. <span style=\"font-family: Nirmala UI;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> 29.5% </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> 0.59 </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2361;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2381;&#2334;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;&#2332;&#2367;&#2319;&#2404;</span></p>\n",
                    options_en: ["<p>2.4</p>\n", "<p>2</p>\n", 
                                "<p>2.5</p>\n", "<p>1.8</p>\n"],
                    options_hi: ["<p>2.4</p>\n", "<p>2</p>\n",
                                "<p>2.5</p>\n", "<p>1.8</p>\n"],
                    solution_en: "<p>5.(b)</p>\r\n<p><span style=\"font-family: Cambria Math;\">Let the number = x</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">29.5% of x = 0.59</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">x =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>0</mn><mo>.</mo><mn>59</mn></mrow><mrow><mn>29</mn><mo>.</mo><mn>5</mn></mrow></mfrac><mo>&times;</mo><mn>100</mn></math>&nbsp;</span><span style=\"font-family: Cambria Math;\"> = 2</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">So, required number = 2</span></p>\n",
                    solution_hi: "<p>5.(b)</p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> = x</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">x </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> 29.5% = 0.59</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">x =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>0</mn><mo>.</mo><mn>59</mn></mrow><mrow><mn>29</mn><mo>.</mo><mn>5</mn></mrow></mfrac><mo>&times;</mo><mn>100</mn></math> </span><span style=\"font-family: Cambria Math;\">= 2</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2309;&#2340;</span><span style=\"font-family: Cambria Math;\">: </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2349;&#2368;&#2359;&#2381;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> = 2</span></p>\n",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "6",
                    section: "17",
                    question_en: "<p>6.<span style=\"font-family: Cambria Math;\"> A and B are numbers such that A is 40% larger than B. If A is decreased by 20% and B is increased by 38% then what is the percentage increase in the total value of A and B? </span></p>\n",
                    question_hi: "<p>6.<span style=\"font-family: Cambria Math;\"> A </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> B </span><span style=\"font-family: Nirmala UI;\">&#2320;&#2360;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;&#2319;&#2305;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;</span><span style=\"font-family: Cambria Math;\"> A, B </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> 40% </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2343;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> A </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> 20% </span><span style=\"font-family: Nirmala UI;\">&#2328;&#2335;&#2366;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2366;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> B </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> 38% </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2338;&#2364;&#2366;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2366;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> A </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> B </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2369;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2340;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2371;&#2342;&#2381;&#2343;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2379;&#2340;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>5 %</p>\n", "<p>4<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>6</mn></mfrac></math><span style=\"font-family: Cambria Math;\"> %</span></p>\n", 
                                "<p>8<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>3</mn></mfrac></math><span style=\"font-family: Cambria Math;\"> %</span></p>\n", "<p>10 %</p>\n"],
                    options_hi: ["<p>5 %</p>\n", "<p>4<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>6</mn></mfrac></math><span style=\"font-family: Cambria Math;\"> %</span></p>\n",
                                "<p>8<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>3</mn></mfrac></math><span style=\"font-family: Cambria Math;\"> %</span></p>\n", "<p>10 %</p>\n"],
                    solution_en: "<p>6.(b)</p>\r\n<p><span style=\"font-family: Cambria Math;\">A </span><span style=\"font-family: Cambria Math;\">: B = 7 : 5 or 700 : 500</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Total value of A + B = 1200</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">New value of A = 700 &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>80</mn><mn>100</mn></mfrac></math> </span><span style=\"font-family: Cambria Math;\">= 560</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">New value of B = 500 &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>138</mn><mn>100</mn></mfrac></math>&nbsp;</span><span style=\"font-family: Cambria Math;\"> = 690</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">New value of A + B = 560 + 690 = 1250</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Required percentage =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1250</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>1200</mn></mrow><mn>1200</mn></mfrac><mo>&times;</mo><mn>100</mn><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>4</mn><mfrac><mn>1</mn><mn>6</mn></mfrac><mo>%</mo></math></span></p>\n",
                    solution_hi: "<p>6.(b)</p>\r\n<p><span style=\"font-family: Cambria Math;\">A : B = 7 : 5 </span><span style=\"font-family: Nirmala UI;\">&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> 700 : 500</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">A + </span><span style=\"font-family: Cambria Math;\">B </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2369;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> = 1200</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">A </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> = 700 &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>80</mn><mn>100</mn></mfrac></math>&nbsp;</span><span style=\"font-family: Cambria Math;\"> = 560</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">B </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> = 500 &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>138</mn><mn>100</mn></mfrac></math> </span><span style=\"font-family: Cambria Math;\">= 690</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">A + B </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> = 560 + 690 = 1250</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2310;&#2357;&#2358;&#2381;&#2351;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340;</span><span style=\"font-family: Cambria Math;\"> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1250</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>1200</mn></mrow><mn>1200</mn></mfrac><mo>&times;</mo><mn>100</mn><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>4</mn><mfrac><mn>1</mn><mn>6</mn></mfrac><mo>%</mo></math></span></p>\n",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "7",
                    section: "17",
                    question_en: "<p>7. 12.5% of the first number is 37.5% of the second number. If the second number is subtracted from the first number, we get an answer of 1428. Find the sum of the two numbers.</p>\n",
                    question_hi: "<p>7. <span style=\"font-family: Nirmala UI;\">&#2346;&#2361;&#2354;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> 12.5% &#8203;&#8203;</span><span style=\"font-family: Nirmala UI;\">&#2342;&#2370;&#2360;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> 37.5% </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2361;&#2354;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2370;&#2360;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2328;&#2335;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2366;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2340;&#2381;&#2340;&#2352;</span><span style=\"font-family: Cambria Math;\"> 1428 </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2366;&#2346;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2379;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2379;&#2344;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;&#2323;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2351;&#2379;&#2327;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2381;&#2334;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;&#2332;&#2367;&#2319;&#2404;</span></p>\n",
                    options_en: ["<p>2846</p>\n", "<p>2856</p>\n", 
                                "<p>2716</p>\n", "<p>2936</p>\n"],
                    options_hi: ["<p>2846</p>\n", "<p>2856</p>\n",
                                "<p>2716</p>\n", "<p>2936</p>\n"],
                    solution_en: "<p>7.(b)</p>\r\n<p><span style=\"font-family: Cambria Math;\">Let the first number = x and second number = y</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">As per question,</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">12.5% of x = 37.5% of y</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>x</mi><mn>8</mn></mfrac><mo>=</mo><mfrac><mrow><mn>3</mn><mi>y</mi></mrow><mn>8</mn></mfrac></math></span></p>\r\n<p><span style=\"font-family: Cambria Math;\">So, x : y = 3 : 1</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Difference between 1st and 2nd number = 3 - 1 = 2 unit</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Sum of 1st number and 2nd number = 3 + 1 = 4 unit</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Again, as per question,</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">2 unit = 1428</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">4 unit = 2856</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">So, sum of the two number = 2856</span></p>\n",
                    solution_hi: "<p>7.(b)</p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2361;&#2354;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> = x </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2370;&#2360;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> = y</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2358;&#2381;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2344;&#2369;&#2360;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\">,</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">x </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> 12.5% &#8203;&#8203;= y </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> 37.5%</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>x</mi><mn>8</mn></mfrac><mo>=</mo><mfrac><mrow><mn>3</mn><mi>y</mi></mrow><mn>8</mn></mfrac></math></span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2309;&#2340;</span><span style=\"font-family: Cambria Math;\">, x : y = 3 : 1</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2346;&#2361;&#2354;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2370;&#2360;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2368;&#2330;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2306;&#2340;&#2352;</span><span style=\"font-family: Cambria Math;\"> = 3 - 1 = 2 </span><span style=\"font-family: Nirmala UI;\">&#2311;&#2325;&#2366;&#2312;</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2346;&#2361;&#2354;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2370;&#2360;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2351;&#2379;&#2327;</span><span style=\"font-family: Cambria Math;\"> = 3 + 1 = 4 </span><span style=\"font-family: Nirmala UI;\">&#2311;&#2325;&#2366;&#2312;</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2358;&#2381;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2344;&#2369;&#2360;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\">,</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">2 </span><span style=\"font-family: Nirmala UI;\">&#2311;&#2325;&#2366;&#2312;</span><span style=\"font-family: Cambria Math;\"> = 1428</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">4 </span><span style=\"font-family: Nirmala UI;\">&#2311;&#2325;&#2366;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">= 2856</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2309;&#2340;</span><span style=\"font-family: Cambria Math;\">: </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2379;&#2344;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;&#2323;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2351;&#2379;&#2327;</span><span style=\"font-family: Cambria Math;\"> = 2856</span></p>\n",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "8",
                    section: "17",
                    question_en: "<p>8.<span style=\"font-family: Cambria Math;\"> If 75% of a number is added to 75, then the result is the number itself , The number is:</span></p>\n",
                    question_hi: "<p>8.<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2360;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> 75% 75 </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2379;&#2337;&#2364;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2366;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;&#2367;&#2339;&#2366;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2381;&#2357;&#2351;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2379;&#2340;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">:</span></p>\n",
                    options_en: ["<p>300</p>\n", "<p><span style=\"font-family: Cambria Math;\">200</span></p>\n", 
                                "<p><span style=\"font-family: Cambria Math;\">250</span></p>\n", "<p><span style=\"font-family: Cambria Math;\"> 350</span></p>\n"],
                    options_hi: ["<p>300</p>\n", "<p>200</p>\n",
                                "<p>250</p>\n", "<p>350</p>\n"],
                    solution_en: "<p>8.(a)</p>\r\n<p><span style=\"font-family: Cambria Math;\">Let the number = x</span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>75</mn><mo>+</mo><mo>&nbsp;</mo><mi>x</mi><mo>&nbsp;</mo><mo>&times;</mo><mfrac><mn>75</mn><mrow><mn>100</mn><mo>&nbsp;</mo></mrow></mfrac><mo>=</mo><mo>&nbsp;</mo><mi>x</mi><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mn>75</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mfrac><mrow><mn>3</mn><mi>x</mi></mrow><mn>4</mn></mfrac><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mi>x</mi></math></p>\r\n<p><span style=\"font-family: Cambria Math;\">x = 300</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">So, number = 300</span></p>\n",
                    solution_hi: "<p>8.(a)</p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> = x</span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>75</mn><mo>+</mo><mo>&nbsp;</mo><mi>x</mi><mo>&nbsp;</mo><mo>&times;</mo><mfrac><mn>75</mn><mrow><mn>100</mn><mo>&nbsp;</mo></mrow></mfrac><mo>=</mo><mo>&nbsp;</mo><mi>x</mi><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mn>75</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mfrac><mrow><mn>3</mn><mi>x</mi></mrow><mn>4</mn></mfrac><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mi>x</mi></math></p>\r\n<p><span style=\"font-family: Cambria Math;\">x = 300</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> = 300</span></p>\n",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "9",
                    section: "17",
                    question_en: "<p>9. The marks obtained by A in a test are 20% more than the marks obtained by B and C obtained 85% of the total marks obtained by A and B. By what percentage are the marks obtained by A less than the marks obtained by C?(Correct to one decimal place)</p>\n",
                    question_hi: "<p>9. <span style=\"font-family: Nirmala UI;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;&#2368;&#2325;&#2381;&#2359;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> A </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2381;&#2357;&#2366;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2366;&#2346;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2306;&#2325;</span><span style=\"font-family: Cambria Math;\">, B </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2381;&#2357;&#2366;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2366;&#2346;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2306;&#2325;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> 20% </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2343;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> C </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> A </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> B </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2381;&#2357;&#2366;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2366;&#2346;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2369;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2306;&#2325;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> 85% </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2366;&#2346;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> A </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2381;&#2357;&#2366;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2366;&#2346;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2306;&#2325;</span><span style=\"font-family: Cambria Math;\">, C </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2381;&#2357;&#2366;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2366;&#2346;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2306;&#2325;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2340;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2306;</span><span style=\"font-family: Cambria Math;\">? (</span><span style=\"font-family: Nirmala UI;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2358;&#2350;&#2354;&#2357;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2381;&#2341;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2361;&#2368;</span><span style=\"font-family: Cambria Math;\">)</span></p>\n",
                    options_en: ["<p>35.8%</p>\n", "<p>34.6%</p>\n", 
                                "<p>55.8%</p>\n", "<p>37.2%</p>\n"],
                    options_hi: ["<p>35.8%</p>\n", "<p>34.6%</p>\n",
                                "<p>55.8%</p>\n", "<p>37.2%</p>\n"],
                    solution_en: "<p>9.(a)</p>\r\n<p><span style=\"font-family: Cambria Math;\">As per question,</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">A : B = 120 : 100</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">A + B : C = 220 : 187</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">C = 187</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">A = 120</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Required percentage =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>187</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>120</mn></mrow><mn>187</mn></mfrac><mo>&times;</mo><mn>100</mn><mo>&nbsp;</mo></math>&nbsp;</span><span style=\"font-family: Cambria Math;\"> = 35.82%</span></p>\n",
                    solution_hi: "<p>9.(a)</p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2358;&#2381;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2344;&#2369;&#2360;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\">, </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">A : B = 120 : 100</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">A + B : C = 220 : 187</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">C = 187</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">A = 120</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2310;&#2357;&#2358;&#2381;&#2351;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340;</span><span style=\"font-family: Cambria Math;\"> =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>187</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>120</mn></mrow><mn>187</mn></mfrac><mo>&times;</mo><mn>100</mn><mo>&nbsp;</mo></math></span><span style=\"font-family: Cambria Math;\">= 35.82%</span></p>\n",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "10",
                    section: "17",
                    question_en: "<p>10. A family&rsquo;s monthly income is &#8377;45,000. Out of the monthly income 20% is spent on food, 20% is spent on the house rent, 5% is spent on shopping, 10% is spent on personal expenses and the rest is saved. If they decided to donate 12% of the monthly savi<span style=\"font-family: Cambria Math;\">ngs to the poor, what is the amount that they donate to the poor?</span></p>\n",
                    question_hi: "<p>10. <span style=\"font-family: Nirmala UI;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;&#2367;&#2357;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2360;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2351;</span><span style=\"font-family: Cambria Math;\"> &#8377;45,000 </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2360;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> 20% </span><span style=\"font-family: Nirmala UI;\">&#2349;&#2379;&#2332;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2326;&#2352;&#2381;&#2330;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2366;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">, 20% </span><span style=\"font-family: Nirmala UI;\">&#2328;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2352;&#2366;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2326;&#2352;&#2381;&#2330;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2366;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">, 5% </span><span style=\"font-family: Nirmala UI;\">&#2326;&#2352;&#2368;&#2342;&#2366;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2326;&#2352;&#2381;&#2330;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2366;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">, 10% </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2381;&#2351;&#2325;&#2381;&#2340;&#2367;&#2327;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2326;&#2352;&#2381;&#2330;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2326;&#2352;&#2381;&#2330;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2366;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2366;&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2330;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2379;&#2340;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2344;&#2381;&#2361;&#2379;&#2306;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2346;&#2344;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2360;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2330;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> 12% </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2352;&#2368;&#2348;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2367;&#2352;&#2381;&#2339;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2352;&#2368;&#2348;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2340;&#2344;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2352;&#2366;&#2358;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;&#2340;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2306;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>&#8377;2750</p>\n", "<p>&#8377;3230</p>\n", 
                                "<p>&#8377;3500</p>\n", "<p>&#8377;2430</p>\n"],
                    options_hi: ["<p>&#8377;2750</p>\n", "<p>&#8377;3230</p>\n",
                                "<p>&#8377;3500</p>\n", "<p>&#8377;2430</p>\n"],
                    solution_en: "<p>10.(d)</p>\r\n<p><span style=\"font-family: Cambria Math;\">Expenditure on food + house rent + shopping + personal expenses = 20% + 20% + 5% + 10% = 55%</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Savings = 100 - 55 = 45%</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Savings = 45000 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>45</mn><mn>100</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\">&nbsp;= 20250</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Donation = 20250 &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>12</mn><mn>100</mn></mfrac></math>&nbsp;</span><span style=\"font-family: Cambria Math;\"> = &#8377;2430</span></p>\n",
                    solution_hi: "<p>10.(d)</p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2326;&#2366;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\">-</span><span style=\"font-family: Nirmala UI;\">&#2346;&#2368;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2326;&#2364;&#2352;&#2381;&#2330;</span><span style=\"font-family: Cambria Math;\"> + </span><span style=\"font-family: Nirmala UI;\">&#2328;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2352;&#2366;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> + </span><span style=\"font-family: Nirmala UI;\">&#2326;&#2364;&#2352;&#2368;&#2342;&#2342;&#2366;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2326;&#2364;&#2352;&#2381;&#2330;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">+ </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2381;&#2351;&#2325;&#2381;&#2340;&#2367;&#2327;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2326;&#2364;&#2352;&#2381;&#2330;</span><span style=\"font-family: Cambria Math;\"> = 20% + 20% + 5% + 10% = 55%</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2348;&#2330;&#2340;</span><span style=\"font-family: Cambria Math;\"> = 100 - 55 = 45%</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2348;&#2330;&#2340;</span><span style=\"font-family: Cambria Math;\"> = 45000 &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>45</mn><mn>100</mn></mfrac></math>&nbsp;</span><span style=\"font-family: Cambria Math;\"> = 20250</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2342;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> = 20250 &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>12</mn><mn>100</mn></mfrac></math>&nbsp;</span><span style=\"font-family: Cambria Math;\"> = &#8377;2430</span></p>\n",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "11",
                    section: "17",
                    question_en: "<p>11. <span style=\"font-family: Cambria Math;\">Data was gathered from a survey as to what sport was liked by the people surveyed. The data was represented by a pie chart,</span><span style=\"font-family: Cambria Math;\"> and the sector representing people who liked soccer had a central angle of 54&deg;. What percentage of the people surveyed liked soccer?</span></p>\n",
                    question_hi: "<p>11. <span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2360;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2352;&#2381;&#2357;&#2375;&#2325;&#2381;&#2359;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2381;&#2357;&#2366;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2311;&#2360;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2348;&#2306;&#2343;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2306;&#2325;&#2337;&#2364;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2319;&#2325;&#2340;&#2381;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2352;&#2381;&#2357;&#2375;&#2325;&#2381;&#2359;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2358;</span><span style=\"font-family: Nirmala UI;\">&#2366;&#2350;&#2367;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2379;&#2327;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2380;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2326;&#2375;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2360;&#2306;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2341;&#2366;</span><span style=\"font-family: Cambria Math;\"> ? </span><span style=\"font-family: Nirmala UI;\">&#2311;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2306;&#2325;&#2337;&#2364;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2366;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2330;&#2366;&#2352;&#2381;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2381;&#2357;&#2366;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2352;&#2381;&#2358;&#2366;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2341;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2347;&#2369;&#2335;&#2348;&#2366;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2360;&#2306;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2366;&#2354;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2379;&#2327;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2367;&#2352;&#2370;&#2346;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2366;&#2354;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;&#2306;&#2342;&#2381;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;&#2339;</span><span style=\"font-family: Cambria Math;\"> 54&deg; </span><span style=\"font-family: Nirmala UI;\">&#2341;&#2366;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2352;&#2381;&#2357;&#2375;&#2325;&#2381;&#2359;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2358;&#2366;&#2350;&#2367;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2379;&#2327;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2340;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2379;&#2327;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2347;&#2369;&#2335;&#2348;&#2366;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2360;&#2306;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2341;&#2366;</span><span style=\"font-family: Cambria Math;\"> ?</span></p>\n",
                    options_en: ["<p>10%</p>\n", "<p><span style=\"font-family: Cambria Math;\">12.5%</span></p>\n", 
                                "<p><span style=\"font-family: Cambria Math;\">20%</span></p>\n", "<p><span style=\"font-family: Cambria Math;\"> 15%</span></p>\n"],
                    options_hi: ["<p>10%</p>\n", "<p><span style=\"font-family: Cambria Math;\">12</span><span style=\"font-family: Cambria Math;\">.5%</span></p>\n",
                                "<p><span style=\"font-family: Cambria Math;\">20%</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">15%</span></p>\n"],
                    solution_en: "<p>11.(d)</p>\r\n<p><span style=\"font-family: Cambria Math;\">Required percentage =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>54</mn><mn>360</mn></mfrac><mo>&times;</mo><mn>100</mn></math> </span><span style=\"font-family: Cambria Math;\">= 15%</span></p>\n",
                    solution_hi: "<p>11.(d)</p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2310;&#2357;&#2358;&#2381;&#2351;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340;</span><span style=\"font-family: Cambria Math;\"> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>54</mn><mn>360</mn></mfrac><mo>&times;</mo><mn>100</mn></math> </span><span style=\"font-family: Cambria Math;\">= 15%</span></p>\n",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "12",
                    section: "17",
                    question_en: "<p>12.<span style=\"font-family: Cambria Math;\"> If 15% of A : 25% of B :: 8 : 11, then A : B is equal to:</span></p>\n",
                    question_hi: "<p>12.<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> A </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> 15% : B </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> 25% :: 8 : 11 </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> A: B,_________ </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2352;&#2366;&#2348;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span></p>\n",
                    options_en: ["<p>4 : 3</p>\n", "<p><span style=\"font-family: Cambria Math;\">33 : 32</span></p>\n", 
                                "<p><span style=\"font-family: Cambria Math;\">5 : 4</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">40 : 33</span></p>\n"],
                    options_hi: ["<p>4 : 3</p>\n", "<p><span style=\"font-family: Cambria Math;\">33 : 32</span></p>\n",
                                "<p><span style=\"font-family: Cambria Math;\">5 : 4</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">40 : 33</span></p>\n"],
                    solution_en: "<p>12.(d)</p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>15</mn><mo>%</mo><mo>&nbsp;</mo><mi>o</mi><mi>f</mi><mo>&nbsp;</mo><mi>A</mi></mrow><mrow><mn>25</mn><mo>%</mo><mo>&nbsp;</mo><mi>o</mi><mi>f</mi><mo>&nbsp;</mo><mi>B</mi></mrow></mfrac><mo>=</mo><mfrac><mn>8</mn><mn>11</mn></mfrac><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mfrac><mi>A</mi><mi>B</mi></mfrac><mo>=</mo><mfrac><mn>40</mn><mn>33</mn></mfrac></math></p>\n",
                    solution_hi: "<p>12.(d)</p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>15</mn><mo>%</mo><mo>&nbsp;</mo><mi>o</mi><mi>f</mi><mo>&nbsp;</mo><mi>A</mi></mrow><mrow><mn>25</mn><mo>%</mo><mo>&nbsp;</mo><mi>o</mi><mi>f</mi><mo>&nbsp;</mo><mi>B</mi></mrow></mfrac><mo>=</mo><mfrac><mn>8</mn><mn>11</mn></mfrac><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mfrac><mi>A</mi><mi>B</mi></mfrac><mo>=</mo><mfrac><mn>40</mn><mn>33</mn></mfrac></math></p>\n",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "13",
                    section: "17",
                    question_en: "<p>13.<span style=\"font-family: Cambria Math;\"> Find the value of K, if 18%</span><span style=\"font-family: Cambria Math;\"> of 450 = 30% of K.</span></p>\n",
                    question_hi: "<p>13. <span style=\"font-family: Nirmala UI;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> 450 </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> 18% = k </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> 30% </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> k </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2381;&#2334;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;&#2332;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2404;</span></p>\n",
                    options_en: ["<p>320</p>\n", "<p>750</p>\n", 
                                "<p><span style=\"font-family: Cambria Math;\">270</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">250</span></p>\n"],
                    options_hi: ["<p>320</p>\n", "<p>750</p>\n",
                                "<p><span style=\"font-family: Cambria Math;\">270</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">250</span></p>\n"],
                    solution_en: "<p>13.(c)</p>\r\n<p><span style=\"font-family: Cambria Math;\">18% of 450 = 30% of K.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&rArr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>18</mn><mo>%</mo><mo>&nbsp;</mo><mi>o</mi><mi>f</mi><mo>&nbsp;</mo><mn>450</mn></mrow><mrow><mn>30</mn><mo>%</mo></mrow></mfrac><mo>&nbsp;</mo></math>= K</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&rArr; K = 270</span></p>\n",
                    solution_hi: "<p>13.(c)</p>\r\n<p><span style=\"font-family: Cambria Math;\">450 </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> 18% =K </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> 30% </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&rArr; <math style=\"font-family:Verdana\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>450</mn><mo>&nbsp;</mo><mo>&nbsp;</mo><mi>&#2325;&#2366;</mi><mo>&nbsp;</mo><mn>18</mn><mo>%</mo></mrow><mrow><mn>30</mn><mo>%</mo></mrow></mfrac><mo>&nbsp;</mo></math>= K</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&rArr; K = 270</span></p>\n",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "14",
                    section: "17",
                    question_en: "<p>14.<span style=\"font-family: Cambria Math;\"> A reduction of 20% in the price of wheat enables a housewife to obtain 4 kgs more for &#8377;1200, what is the reduced price per kg(in&#8377;)?</span></p>\n",
                    question_hi: "<p>14.<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2375;&#2361;&#2370;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> 20% </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2350;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;&#2352;&#2339;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2371;&#2361;&#2367;&#2339;&#2368;</span><span style=\"font-family: Cambria Math;\"> &#8377;1200 </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> 4 kg </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2343;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2375;&#2361;&#2370;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2326;&#2352;&#2368;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2366;&#2340;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\"> kg </span><span style=\"font-family: Nirmala UI;\">&#2328;&#2335;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2369;&#2310;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> (&#8377; </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\">) </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2381;&#2334;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;&#2332;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2404;</span></p>\n",
                    options_en: ["<p>40</p>\n", "<p>60</p>\n", 
                                "<p>75</p>\n", "<p>50</p>\n"],
                    options_hi: ["<p>40</p>\n", "<p>60</p>\n",
                                "<p>75</p>\n", "<p>50</p>\n"],
                    solution_en: "<p>14.(b)</p>\r\n<p><span style=\"font-family: Cambria Math;\">20% =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>5</mn></mfrac></math></span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; Original&nbsp; &nbsp;:&nbsp; &nbsp;Reduced </span><span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">price&rarr; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; </span><span style=\"font-family: Cambria Math;\">5&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp; &nbsp; 4</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">consumption&rarr; &nbsp; &nbsp;</span><span style=\"font-family: Cambria Math;\">4&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp; &nbsp;5</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">1 unit = 4 </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">5 unit = 20</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">So, reduced price = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1200</mn><mn>20</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\">&nbsp;= 60</span></p>\n",
                    solution_hi: "<p>14.(b)</p>\r\n<p><span style=\"font-family: Cambria Math;\">20%=&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>5</mn></mfrac></math></span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&#2350;&#2370;&#2354;&nbsp;&nbsp;</span><span style=\"font-family: Cambria Math;\"> :&nbsp; &nbsp; </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2369;&#2310;</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2350;&#2370;&#2354;&#2381;&#2351;<span style=\"font-family: Cambria Math;\">&rarr; </span></span><span style=\"font-family: Cambria Math;\">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;</span><span style=\"font-family: Cambria Math;\">5&nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 4</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2326;&#2346;&#2340;<span style=\"font-family: Cambria Math;\">&rarr; </span></span><span style=\"font-family: Cambria Math;\">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;</span><span style=\"font-family: Cambria Math;\">4&nbsp; &nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 5</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">1 </span><span style=\"font-family: Nirmala UI;\">&#2311;&#2325;&#2366;&#2312;</span><span style=\"font-family: Cambria Math;\"> = 4&nbsp;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">5 </span><span style=\"font-family: Nirmala UI;\">&#2311;&#2325;&#2366;&#2312;</span><span style=\"font-family: Cambria Math;\"> = 20</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2311;&#2360;&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2328;&#2335;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2369;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;&#2350;&#2340;</span><span style=\"font-family: Cambria Math;\"> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1200</mn><mn>20</mn></mfrac></math> </span><span style=\"font-family: Cambria Math;\">= 60</span></p>\n",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "15",
                    section: "misc",
                    question_en: "<p>15.<span style=\"font-family: Cambria Math;\"> The product of 15% of A and 35% of B, when divided by 21% of C gives 37 as the answer. What will be the answer if the product o</span><span style=\"font-family: Cambria Math;\">f A and B is divided by C?</span></p>\n",
                    question_hi: "<p>15. <span style=\"font-family: Cambria Math;\">A </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> 15% </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> B </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> 35% </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2369;&#2339;&#2344;&#2347;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> C </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> 21% </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2349;&#2366;&#2332;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2340;&#2381;&#2340;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2352;&#2370;&#2346;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> 37 </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2366;&#2346;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2379;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> A </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> B </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2369;&#2339;&#2344;&#2347;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> C </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2349;&#2366;&#2332;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2366;&#2319;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2340;&#2381;&#2340;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2340;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2379;&#2327;&#2366;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>111</p>\n", "<p>185</p>\n", 
                                "<p>148</p>\n", "<p>74</p>\n"],
                    options_hi: ["<p>111</p>\n", "<p>185</p>\n",
                                "<p>148</p>\n", "<p>74</p>\n"],
                    solution_en: "<p>15.(c)</p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>15</mn><mn>100</mn></mfrac><mi>A</mi><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mfrac><mn>35</mn><mn>100</mn></mfrac><mi>B</mi><mo>&nbsp;</mo><mo>&divide;</mo><mo>&nbsp;</mo><mfrac><mn>21</mn><mn>100</mn></mfrac><mi>C</mi><mo>&nbsp;</mo><mo>=</mo><mn>37</mn><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mfrac><mrow><mn>21</mn><mi>A</mi><mi>B</mi></mrow><mn>400</mn></mfrac><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mfrac><mn>100</mn><mrow><mn>21</mn><mi>C</mi></mrow></mfrac><mo>=</mo><mn>37</mn><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mfrac><mrow><mi>A</mi><mi>B</mi></mrow><mi>C</mi></mfrac><mo>=</mo><mn>37</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>4</mn><mo>=</mo><mn>148</mn></math></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                    solution_hi: "<p>15.(c)</p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>15</mn><mn>100</mn></mfrac><mi>A</mi><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mfrac><mn>35</mn><mn>100</mn></mfrac><mi>B</mi><mo>&nbsp;</mo><mo>&divide;</mo><mo>&nbsp;</mo><mfrac><mn>21</mn><mn>100</mn></mfrac><mi>C</mi><mo>&nbsp;</mo><mo>=</mo><mn>37</mn><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mfrac><mrow><mn>21</mn><mi>A</mi><mi>B</mi></mrow><mn>400</mn></mfrac><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mfrac><mn>100</mn><mrow><mn>21</mn><mi>C</mi></mrow></mfrac><mo>=</mo><mn>37</mn><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mfrac><mrow><mi>A</mi><mi>B</mi></mrow><mi>C</mi></mfrac><mo>=</mo><mn>37</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>4</mn><mo>=</mo><mn>148</mn></math></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>