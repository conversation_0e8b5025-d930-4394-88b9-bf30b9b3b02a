<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">15:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 15</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">15</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 15 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 0,
                end: 13
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 14,
                end: 14
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">1.</span><span style=\"font-family: Cambria Math;\"> What is the value of&nbsp; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><mi>k</mi><mo>-</mo><mfrac><mn>1</mn><mi>k</mi></mfrac><mo>)</mo><mo>(</mo><msup><mi>k</mi><mn>2</mn></msup><mo>+</mo><mfrac><mn>1</mn><msup><mi>k</mi><mn>2</mn></msup></mfrac><mo>)</mo><mo>(</mo><msup><mi>k</mi><mn>4</mn></msup><mo>+</mo><mfrac><mn>1</mn><msup><mi>k</mi><mn>4</mn></msup></mfrac><mo>)</mo><mo>(</mo><msup><mi>k</mi><mn>8</mn></msup><mo>+</mo><mfrac><mn>1</mn><msup><mi>k</mi><mn>8</mn></msup></mfrac><mo>)</mo><mo>(</mo><msup><mi>k</mi><mn>16</mn></msup><mo>+</mo><mfrac><mn>1</mn><msup><mi>k</mi><mn>16</mn></msup></mfrac><mo>)</mo></math>&nbsp;</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">1.&nbsp; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><mi>k</mi><mo>-</mo><mfrac><mn>1</mn><mi>k</mi></mfrac><mo>)</mo><mo>(</mo><msup><mi>k</mi><mn>2</mn></msup><mo>+</mo><mfrac><mn>1</mn><msup><mi>k</mi><mn>2</mn></msup></mfrac><mo>)</mo><mo>(</mo><msup><mi>k</mi><mn>4</mn></msup><mo>+</mo><mfrac><mn>1</mn><msup><mi>k</mi><mn>4</mn></msup></mfrac><mo>)</mo><mo>(</mo><msup><mi>k</mi><mn>8</mn></msup><mo>+</mo><mfrac><mn>1</mn><msup><mi>k</mi><mn>8</mn></msup></mfrac><mo>)</mo><mo>(</mo><msup><mi>k</mi><mn>16</mn></msup><mo>+</mo><mfrac><mn>1</mn><msup><mi>k</mi><mn>16</mn></msup></mfrac><mo>)</mo></math></span><span style=\"font-family: Cambria Math;\">&nbsp;</span><span style=\"font-family: Kokila;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>k</mi><mn>64</mn></msup><mo>-</mo><mo>&nbsp;</mo><mfrac><mn>1</mn><msup><mi>k</mi><mn>64</mn></msup></mfrac></math></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi>k</mi><mn>32</mn></msup><mo>-</mo><mfrac><mrow><mo>&nbsp;</mo><mn>1</mn></mrow><msup><mi>k</mi><mn>32</mn></msup></mfrac></mrow><mrow><mi>k</mi><mo>-</mo><mstyle displaystyle=\"true\"><mfrac><mrow><mo>&nbsp;</mo><mn>1</mn></mrow><mi>k</mi></mfrac></mstyle></mrow></mfrac></math></p>\n", 
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&nbsp;</mo><msup><mi>k</mi><mn>32</mn></msup><mo>-</mo><mo>&nbsp;</mo><mfrac><mn>1</mn><msup><mi>k</mi><mn>32</mn></msup></mfrac></math></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi>k</mi><mn>32</mn></msup><mo>-</mo><mo>&nbsp;</mo><mfrac><mn>1</mn><msup><mi>k</mi><mn>32</mn></msup></mfrac></mrow><mrow><mi>k</mi><mo>+</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mstyle displaystyle=\"true\"><mfrac><mn>1</mn><mi>k</mi></mfrac></mstyle></mrow></mfrac><mo>&nbsp;</mo></math></p>\n"],
                    options_hi: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>k</mi><mn>64</mn></msup><mo>-</mo><mo>&nbsp;</mo><mfrac><mn>1</mn><msup><mi>k</mi><mn>64</mn></msup></mfrac></math></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi>k</mi><mn>32</mn></msup><mo>-</mo><mfrac><mrow><mo>&nbsp;</mo><mn>1</mn></mrow><msup><mi>k</mi><mn>32</mn></msup></mfrac></mrow><mrow><mi>k</mi><mo>-</mo><mstyle displaystyle=\"true\"><mfrac><mrow><mo>&nbsp;</mo><mn>1</mn></mrow><mi>k</mi></mfrac></mstyle></mrow></mfrac></math></p>\n",
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&nbsp;</mo><msup><mi>k</mi><mn>32</mn></msup><mo>-</mo><mo>&nbsp;</mo><mfrac><mn>1</mn><msup><mi>k</mi><mn>32</mn></msup></mfrac></math></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi>k</mi><mn>32</mn></msup><mo>-</mo><mo>&nbsp;</mo><mfrac><mn>1</mn><msup><mi>k</mi><mn>32</mn></msup></mfrac></mrow><mrow><mi>k</mi><mo>+</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mstyle displaystyle=\"true\"><mfrac><mn>1</mn><mi>k</mi></mfrac></mstyle></mrow></mfrac><mo>&nbsp;</mo></math></p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">1.(</span><span style=\"font-family: Cambria Math;\">d)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mfrac><mrow><mo>(</mo><mi>k</mi><mo>+</mo><mfrac><mn>1</mn><mi>k</mi></mfrac><mo>)</mo></mrow><mrow><mo>&nbsp;</mo><mo>(</mo><mi>k</mi><mo>+</mo><mfrac><mn>1</mn><mi>k</mi></mfrac><mo>)</mo><mo>&nbsp;</mo></mrow></mfrac><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mo>(</mo><mi>k</mi><mo>-</mo><mfrac><mn>1</mn><mi>k</mi></mfrac><mo>)</mo><mo>(</mo><msup><mi>k</mi><mn>2</mn></msup><mo>+</mo><mfrac><mn>1</mn><msup><mi>k</mi><mn>2</mn></msup></mfrac><mo>)</mo><mo>(</mo><msup><mi>k</mi><mn>4</mn></msup><mo>+</mo><mfrac><mn>1</mn><msup><mi>k</mi><mn>4</mn></msup></mfrac><mo>)</mo><mo>(</mo><msup><mi>k</mi><mn>8</mn></msup><mo>+</mo><mfrac><mn>1</mn><msup><mi>k</mi><mn>8</mn></msup></mfrac><mo>)</mo><mo>(</mo><msup><mi>k</mi><mn>16</mn></msup><mo>+</mo><mfrac><mn>1</mn><msup><mi>k</mi><mn>16</mn></msup></mfrac><mo>)</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mi>M</mi><mi>u</mi><mi>l</mi><mi>t</mi><mi>i</mi><mi>p</mi><mi>l</mi><mi>y</mi><mo>&nbsp;</mo><mi>b</mi><mi>y</mi><mo>&nbsp;</mo><mfrac><mrow><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>(</mo><mi>k</mi><mo>+</mo><mfrac><mn>1</mn><mi>k</mi></mfrac><mo>)</mo></mrow><mrow><mo>&nbsp;</mo><mo>(</mo><mi>k</mi><mo>+</mo><mstyle displaystyle=\"true\"><mfrac><mn>1</mn><mi>k</mi></mfrac></mstyle><mo>)</mo></mrow></mfrac><mspace linebreak=\"newline\"></mspace><mo>&nbsp;</mo><mfrac><mrow><mo>&nbsp;</mo><mo>(</mo><msup><mi>k</mi><mn>2</mn></msup><mo>-</mo><mfrac><mn>1</mn><msup><mi>k</mi><mn>2</mn></msup></mfrac><mo>)</mo><mo>(</mo><msup><mi>k</mi><mn>2</mn></msup><mo>+</mo><mfrac><mn>1</mn><msup><mi>k</mi><mn>2</mn></msup></mfrac><mo>)</mo><mo>(</mo><msup><mi>k</mi><mn>4</mn></msup><mo>+</mo><mfrac><mn>1</mn><msup><mi>k</mi><mn>4</mn></msup></mfrac><mo>)</mo><mo>(</mo><msup><mi>k</mi><mn>8</mn></msup><mo>+</mo><mfrac><mn>1</mn><msup><mi>k</mi><mn>8</mn></msup></mfrac><mo>)</mo><mo>(</mo><msup><mi>k</mi><mn>16</mn></msup><mo>+</mo><mfrac><mn>1</mn><msup><mi>k</mi><mn>16</mn></msup></mfrac><mo>,</mo><mo>)</mo></mrow><mrow><mo>(</mo><mi>k</mi><mo>+</mo><mstyle displaystyle=\"true\"><mfrac><mn>1</mn><mi>k</mi></mfrac></mstyle><mo>)</mo></mrow></mfrac><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mo>&nbsp;</mo><mfrac><mrow><mo>(</mo><msup><mi>k</mi><mn>4</mn></msup><mo>-</mo><mfrac><mn>1</mn><msup><mi>k</mi><mn>4</mn></msup></mfrac><mo>)</mo><mo>(</mo><msup><mi>k</mi><mn>4</mn></msup><mo>+</mo><mfrac><mn>1</mn><msup><mi>k</mi><mn>4</mn></msup></mfrac><mo>)</mo><mo>(</mo><msup><mi>k</mi><mn>8</mn></msup><mo>+</mo><mfrac><mn>1</mn><msup><mi>k</mi><mn>8</mn></msup></mfrac><mo>)</mo><mo>(</mo><msup><mi>k</mi><mn>16</mn></msup><mo>+</mo><mfrac><mn>1</mn><msup><mi>k</mi><mn>16</mn></msup></mfrac><mo>,</mo><mo>)</mo></mrow><mrow><mo>(</mo><mi>k</mi><mo>+</mo><mstyle displaystyle=\"true\"><mfrac><mn>1</mn><mi>k</mi></mfrac></mstyle><mo>)</mo></mrow></mfrac><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mo>&nbsp;</mo><mfrac><mrow><mo>(</mo><msup><mi>k</mi><mn>8</mn></msup><mo>-</mo><mfrac><mn>1</mn><msup><mi>k</mi><mn>8</mn></msup></mfrac><mo>)</mo><mo>(</mo><msup><mi>k</mi><mn>8</mn></msup><mo>+</mo><mfrac><mn>1</mn><msup><mi>k</mi><mn>8</mn></msup></mfrac><mo>)</mo><mo>(</mo><msup><mi>k</mi><mn>16</mn></msup><mo>+</mo><mfrac><mn>1</mn><msup><mi>k</mi><mn>16</mn></msup></mfrac><mo>,</mo><mo>)</mo></mrow><mrow><mo>(</mo><mi>k</mi><mo>+</mo><mstyle displaystyle=\"true\"><mfrac><mn>1</mn><mi>k</mi></mfrac></mstyle><mo>)</mo></mrow></mfrac><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mfrac><mrow><mo>&nbsp;</mo><mo>(</mo><msup><mi>k</mi><mn>16</mn></msup><mo>-</mo><mfrac><mn>1</mn><msup><mi>k</mi><mn>16</mn></msup></mfrac><mo>)</mo><mo>(</mo><msup><mi>k</mi><mn>16</mn></msup><mo>+</mo><mfrac><mn>1</mn><msup><mi>k</mi><mn>16</mn></msup></mfrac><mo>,</mo><mo>)</mo></mrow><mrow><mo>(</mo><mi>k</mi><mo>+</mo><mstyle displaystyle=\"true\"><mfrac><mn>1</mn><mi>k</mi></mfrac></mstyle><mo>)</mo></mrow></mfrac><mo>&nbsp;</mo><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mfrac><mrow><mo>&nbsp;</mo><mo>(</mo><msup><mi>k</mi><mn>32</mn></msup><mo>-</mo><mo>&nbsp;</mo><mfrac><mn>1</mn><msup><mi>k</mi><mn>32</mn></msup></mfrac><mo>)</mo></mrow><mrow><mo>(</mo><mi>k</mi><mo>+</mo><mstyle displaystyle=\"true\"><mfrac><mn>1</mn><mi>k</mi></mfrac></mstyle><mo>)</mo></mrow></mfrac><mo>&nbsp;</mo></math></span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">1.(</span><span style=\"font-family: Cambria Math;\">d)</span></p>\r\n<p><math style=\"font-family: Verdana;\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mfrac><mrow><mo>(</mo><mi>k</mi><mo>+</mo><mfrac><mn>1</mn><mi>k</mi></mfrac><mo>)</mo></mrow><mrow><mo>&nbsp;</mo><mo>(</mo><mi>k</mi><mo>+</mo><mfrac><mn>1</mn><mi>k</mi></mfrac><mo>)</mo><mo>&nbsp;</mo></mrow></mfrac><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mo>(</mo><mi>k</mi><mo>-</mo><mfrac><mn>1</mn><mi>k</mi></mfrac><mo>)</mo><mo>(</mo><msup><mi>k</mi><mn>2</mn></msup><mo>+</mo><mfrac><mn>1</mn><msup><mi>k</mi><mn>2</mn></msup></mfrac><mo>)</mo><mo>(</mo><msup><mi>k</mi><mn>4</mn></msup><mo>+</mo><mfrac><mn>1</mn><msup><mi>k</mi><mn>4</mn></msup></mfrac><mo>)</mo><mo>(</mo><msup><mi>k</mi><mn>8</mn></msup><mo>+</mo><mfrac><mn>1</mn><msup><mi>k</mi><mn>8</mn></msup></mfrac><mo>)</mo><mo>(</mo><msup><mi>k</mi><mn>16</mn></msup><mo>+</mo><mfrac><mn>1</mn><msup><mi>k</mi><mn>16</mn></msup></mfrac><mo>)</mo><mo>&nbsp;</mo><mo>,</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mfrac><mrow><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>(</mo><mi>k</mi><mo>+</mo><mfrac><mn>1</mn><mi>k</mi></mfrac><mo>)</mo></mrow><mrow><mo>&nbsp;</mo><mo>(</mo><mi>k</mi><mo>+</mo><mstyle displaystyle=\"true\"><mfrac><mn>1</mn><mi>k</mi></mfrac></mstyle><mo>)</mo></mrow></mfrac><mo>&nbsp;</mo><mi>&#2360;&#2375;</mi><mo>&nbsp;</mo><mi>&#2327;&#2369;&#2339;&#2366;</mi><mo>&nbsp;</mo><mi>&#2325;&#2352;&#2344;&#2375;</mi><mo>&nbsp;</mo><mi>&#2346;&#2352;</mi><mspace linebreak=\"newline\"></mspace><mo>&nbsp;</mo><mfrac><mrow><mo>&nbsp;</mo><mo>(</mo><msup><mi>k</mi><mn>2</mn></msup><mo>-</mo><mfrac><mn>1</mn><msup><mi>k</mi><mn>2</mn></msup></mfrac><mo>)</mo><mo>(</mo><msup><mi>k</mi><mn>2</mn></msup><mo>+</mo><mfrac><mn>1</mn><msup><mi>k</mi><mn>2</mn></msup></mfrac><mo>)</mo><mo>(</mo><msup><mi>k</mi><mn>4</mn></msup><mo>+</mo><mfrac><mn>1</mn><msup><mi>k</mi><mn>4</mn></msup></mfrac><mo>)</mo><mo>(</mo><msup><mi>k</mi><mn>8</mn></msup><mo>+</mo><mfrac><mn>1</mn><msup><mi>k</mi><mn>8</mn></msup></mfrac><mo>)</mo><mo>(</mo><msup><mi>k</mi><mn>16</mn></msup><mo>+</mo><mfrac><mn>1</mn><msup><mi>k</mi><mn>16</mn></msup></mfrac><mo>,</mo><mo>)</mo></mrow><mrow><mo>(</mo><mi>k</mi><mo>+</mo><mstyle displaystyle=\"true\"><mfrac><mn>1</mn><mi>k</mi></mfrac></mstyle><mo>)</mo></mrow></mfrac><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mo>&nbsp;</mo><mfrac><mrow><mo>(</mo><msup><mi>k</mi><mn>4</mn></msup><mo>-</mo><mfrac><mn>1</mn><msup><mi>k</mi><mn>4</mn></msup></mfrac><mo>)</mo><mo>(</mo><msup><mi>k</mi><mn>4</mn></msup><mo>+</mo><mfrac><mn>1</mn><msup><mi>k</mi><mn>4</mn></msup></mfrac><mo>)</mo><mo>(</mo><msup><mi>k</mi><mn>8</mn></msup><mo>+</mo><mfrac><mn>1</mn><msup><mi>k</mi><mn>8</mn></msup></mfrac><mo>)</mo><mo>(</mo><msup><mi>k</mi><mn>16</mn></msup><mo>+</mo><mfrac><mn>1</mn><msup><mi>k</mi><mn>16</mn></msup></mfrac><mo>,</mo><mo>)</mo></mrow><mrow><mo>(</mo><mi>k</mi><mo>+</mo><mstyle displaystyle=\"true\"><mfrac><mn>1</mn><mi>k</mi></mfrac></mstyle><mo>)</mo></mrow></mfrac><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mo>&nbsp;</mo><mfrac><mrow><mo>(</mo><msup><mi>k</mi><mn>8</mn></msup><mo>-</mo><mfrac><mn>1</mn><msup><mi>k</mi><mn>8</mn></msup></mfrac><mo>)</mo><mo>(</mo><msup><mi>k</mi><mn>8</mn></msup><mo>+</mo><mfrac><mn>1</mn><msup><mi>k</mi><mn>8</mn></msup></mfrac><mo>)</mo><mo>(</mo><msup><mi>k</mi><mn>16</mn></msup><mo>+</mo><mfrac><mn>1</mn><msup><mi>k</mi><mn>16</mn></msup></mfrac><mo>,</mo><mo>)</mo></mrow><mrow><mo>(</mo><mi>k</mi><mo>+</mo><mstyle displaystyle=\"true\"><mfrac><mn>1</mn><mi>k</mi></mfrac></mstyle><mo>)</mo></mrow></mfrac><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mfrac><mrow><mo>&nbsp;</mo><mo>(</mo><msup><mi>k</mi><mn>16</mn></msup><mo>-</mo><mfrac><mn>1</mn><msup><mi>k</mi><mn>16</mn></msup></mfrac><mo>)</mo><mo>(</mo><msup><mi>k</mi><mn>16</mn></msup><mo>+</mo><mfrac><mn>1</mn><msup><mi>k</mi><mn>16</mn></msup></mfrac><mo>,</mo><mo>)</mo></mrow><mrow><mo>(</mo><mi>k</mi><mo>+</mo><mstyle displaystyle=\"true\"><mfrac><mn>1</mn><mi>k</mi></mfrac></mstyle><mo>)</mo></mrow></mfrac><mo>&nbsp;</mo><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mfrac><mrow><mo>&nbsp;</mo><mo>(</mo><msup><mi>k</mi><mn>32</mn></msup><mo>-</mo><mo>&nbsp;</mo><mfrac><mn>1</mn><msup><mi>k</mi><mn>32</mn></msup></mfrac><mo>)</mo></mrow><mrow><mo>(</mo><mi>k</mi><mo>+</mo><mstyle displaystyle=\"true\"><mfrac><mn>1</mn><mi>k</mi></mfrac></mstyle><mo>)</mo></mrow></mfrac><mo>&nbsp;</mo></math></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">2.</span><span style=\"font-family: Cambria Math;\"> If&nbsp; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mfenced><mrow><mi>x</mi><mo>-</mo><mn>8</mn></mrow></mfenced><mn>2</mn></msup><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><msup><mrow><mo>(</mo><mi>x</mi><mo>+</mo><mn>8</mn><mo>)</mo></mrow><mn>2</mn></msup></math></span><span style=\"font-family: Cambria Math;\">=0. What is the value of </span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">2. </span><span style=\"font-family: Kokila;\">&#2309;&#2327;&#2352;</span><span style=\"font-family: Cambria Math;\"> &nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mfenced><mrow><mi>x</mi><mo>-</mo><mn>8</mn></mrow></mfenced><mn>2</mn></msup><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><msup><mrow><mo>(</mo><mi>x</mi><mo>+</mo><mn>8</mn><mo>)</mo></mrow><mn>2</mn></msup></math></span><span style=\"font-family: Cambria Math;\">&nbsp;</span><span style=\"font-family: Cambria Math;\">=0. </span><span style=\"font-family: Cambria Math;\">X </span><span style=\"font-family: Kokila;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>-<span style=\"font-family: Cambria Math;\">32</span></p>\n", "<p>32</p>\n", 
                                "<p>0</p>\n", "<p>1</p>\n"],
                    options_hi: ["<p><span style=\"font-family: Cambria Math;\">-32</span></p>\n", "<p>32</p>\n",
                                "<p>0<span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p>1</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">2.(</span><span style=\"font-family: Cambria Math;\">c)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mfenced><mrow><mi>x</mi><mo>-</mo><mn>8</mn></mrow></mfenced><mn>2</mn></msup><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><msup><mrow><mo>(</mo><mi>x</mi><mo>+</mo><mn>8</mn><mo>)</mo></mrow><mn>2</mn></msup><mo>=</mo><mn>0</mn><mspace linebreak=\"newline\"></mspace><mo>&rArr;</mo><mo>&nbsp;</mo><mo>(</mo><msup><mi>x</mi><mn>2</mn></msup><mo>+</mo><mn>64</mn><mo>-</mo><mn>16</mn><mi>x</mi><mo>)</mo><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mo>(</mo><msup><mi>x</mi><mn>2</mn></msup><mo>+</mo><mn>64</mn><mo>+</mo><mn>16</mn><mi>x</mi><mo>)</mo><mo>=</mo><mn>0</mn><mo>&nbsp;</mo><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mo>&rArr;</mo><mo>-</mo><mn>16</mn><mi>x</mi><mo>&nbsp;</mo><mo>-</mo><mn>16</mn><mi>x</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>0</mn><mspace linebreak=\"newline\"></mspace><mo>&rArr;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mi>x</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>0</mn></math></span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">2.(</span><span style=\"font-family: Cambria Math;\">c)</span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mfenced><mrow><mi>x</mi><mo>-</mo><mn>8</mn></mrow></mfenced><mn>2</mn></msup><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><msup><mrow><mo>(</mo><mi>x</mi><mo>+</mo><mn>8</mn><mo>)</mo></mrow><mn>2</mn></msup><mo>=</mo><mn>0</mn><mspace linebreak=\"newline\"></mspace><mo>&rArr;</mo><mo>&nbsp;</mo><mo>(</mo><msup><mi>x</mi><mn>2</mn></msup><mo>+</mo><mn>64</mn><mo>-</mo><mn>16</mn><mi>x</mi><mo>)</mo><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mo>(</mo><msup><mi>x</mi><mn>2</mn></msup><mo>+</mo><mn>64</mn><mo>+</mo><mn>16</mn><mi>x</mi><mo>)</mo><mo>=</mo><mn>0</mn><mo>&nbsp;</mo><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mo>&rArr;</mo><mo>-</mo><mn>16</mn><mi>x</mi><mo>&nbsp;</mo><mo>-</mo><mn>16</mn><mi>x</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>0</mn><mspace linebreak=\"newline\"></mspace><mo>&rArr;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mi>x</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>0</mn></math></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">3. </span><span style=\"font-family: Cambria Math;\">If p + q = 7 and <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>p</mi><mrow><mn>2</mn><mo>&nbsp;</mo></mrow></msup><mo>+</mo><mo>&nbsp;</mo><msup><mi>q</mi><mn>2</mn></msup></math></span><span style=\"font-family: Cambria Math;\">&nbsp;</span><span style=\"font-family: Cambria Math;\">= 25, then find the value of </span><span style=\"font-family: Cambria Math;\">pq</span><span style=\"font-family: Cambria Math;\"> .</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">3. </span><span style=\"font-family: Kokila;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">p + q = 7 </span><span style=\"font-family: Kokila;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>p</mi><mrow><mn>2</mn><mo>&nbsp;</mo></mrow></msup><mo>+</mo><mo>&nbsp;</mo><msup><mi>q</mi><mn>2</mn></msup></math></span><span style=\"font-family: Cambria Math;\">&nbsp;</span><span style=\"font-family: Cambria Math;\">= 25, </span><span style=\"font-family: Kokila;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">pq</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2332;&#2381;&#2334;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2368;&#2332;&#2367;&#2319;</span><span style=\"font-family: Kokila;\">&#2404;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                    options_en: ["<p>12</p>\n", "<p>24</p>\n", 
                                "<p>36</p>\n", "<p>18</p>\n"],
                    options_hi: ["<p>12</p>\n", "<p>24</p>\n",
                                "<p>36</p>\n", "<p>18</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">3.(</span><span style=\"font-family: Cambria Math;\">a)</span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>p</mi><mn>2</mn></msup><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><msup><mi>q</mi><mn>2</mn></msup><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>25</mn><mo>&nbsp;</mo><mo>[</mo><mi>g</mi><mi>i</mi><mi>v</mi><mi>e</mi><mi>n</mi><mo>]</mo><mspace linebreak=\"newline\"></mspace><mo>&nbsp;</mo><mi>p</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mi>q</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>7</mn><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&rArr;</mo><mo>&nbsp;</mo><msup><mrow><mo>(</mo><mi>p</mi><mo>+</mo><mi>q</mi><mo>)</mo></mrow><mn>2</mn></msup><mo>=</mo><mo>&nbsp;</mo><msup><mn>7</mn><mn>2</mn></msup><mo>&nbsp;</mo><mo>&rArr;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><msup><mi>p</mi><mn>2</mn></msup><mo>+</mo><mo>&nbsp;</mo><msup><mi>q</mi><mn>2</mn></msup><mo>+</mo><mn>2</mn><mi>p</mi><mi>q</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>49</mn><mo>&nbsp;</mo><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mo>&rArr;</mo><mn>2</mn><mi>p</mi><mi>q</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>49</mn><mo>-</mo><mn>25</mn><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>24</mn><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mo>&rArr;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mi>p</mi><mi>q</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>12</mn></math></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">3.(</span><span style=\"font-family: Cambria Math;\">a)</span></p>\r\n<p><math style=\"font-family: Verdana;\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>p</mi><mn>2</mn></msup><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><msup><mi>q</mi><mn>2</mn></msup><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>25</mn><mo>&nbsp;</mo><mo>[</mo><mi>&#2342;&#2367;&#2351;&#2366;</mi><mo>&nbsp;</mo><mi>&#2327;&#2351;&#2366;</mi><mo>]</mo><mspace linebreak=\"newline\"></mspace><mo>&nbsp;</mo><mi>p</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mi>q</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>7</mn><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&rArr;</mo><mo>&nbsp;</mo><msup><mrow><mo>(</mo><mi>p</mi><mo>+</mo><mi>q</mi><mo>)</mo></mrow><mn>2</mn></msup><mo>=</mo><mo>&nbsp;</mo><msup><mn>7</mn><mn>2</mn></msup><mo>&nbsp;</mo><mo>&rArr;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><msup><mi>p</mi><mn>2</mn></msup><mo>+</mo><mo>&nbsp;</mo><msup><mi>q</mi><mn>2</mn></msup><mo>+</mo><mn>2</mn><mi>p</mi><mi>q</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>49</mn><mo>&nbsp;</mo><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mo>&rArr;</mo><mn>2</mn><mi>p</mi><mi>q</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>49</mn><mo>-</mo><mn>25</mn><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>24</mn><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mo>&rArr;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mi>p</mi><mi>q</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>12</mn></math></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">4.</span><span style=\"font-family: Cambria Math;\"> If&nbsp; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>a</mi><mn>4</mn></msup><mo>+</mo><mo>&nbsp;</mo><mfrac><mn>1</mn><msup><mi>a</mi><mn>4</mn></msup></mfrac></math></span><span style=\"font-family: Cambria Math;\">&nbsp;</span><span style=\"font-family: Cambria Math;\">= 194, then what is the value of&nbsp; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>a</mi><mo>&sup3;</mo><mo>&nbsp;</mo><mo>+</mo><mfrac><mrow><mo>&nbsp;</mo><mn>1</mn></mrow><msup><mi>a</mi><mn>3</mn></msup></mfrac></math>&nbsp;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">4. </span><span style=\"font-family: Kokila;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>a</mi><mn>4</mn></msup><mo>+</mo><mo>&nbsp;</mo><mfrac><mn>1</mn><msup><mi>a</mi><mn>4</mn></msup></mfrac></math></span><span style=\"font-family: Cambria Math;\">&nbsp;</span><span style=\"font-family: Cambria Math;\">= 194, </span><span style=\"font-family: Kokila;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>a</mi><mo>&sup3;</mo><mo>&nbsp;</mo><mo>+</mo><mfrac><mrow><mo>&nbsp;</mo><mn>1</mn></mrow><msup><mi>a</mi><mn>3</mn></msup></mfrac></math></span><span style=\"font-family: Cambria Math;\">&nbsp;</span><span style=\"font-family: Kokila;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2379;&#2327;&#2366;</span><span style=\"font-family: Cambria Math;\"> ?</span></p>\n",
                    options_en: ["<p>50</p>\n", "<p>52</p>\n", 
                                "<p>48</p>\n", "<p>44</p>\n"],
                    options_hi: ["<p>50</p>\n", "<p>52</p>\n",
                                "<p>48</p>\n", "<p>44</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">4.(</span><span style=\"font-family: Cambria Math;\">b)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>L</mi><mi>e</mi><mi>t</mi><mo>&nbsp;</mo><mi>a</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mfrac><mn>1</mn><mi>a</mi></mfrac><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mi>x</mi><mspace linebreak=\"newline\"></mspace><mo>&nbsp;</mo><msup><mi>a</mi><mn>4</mn></msup><mo>+</mo><mo>&nbsp;</mo><mfrac><mn>1</mn><msup><mi>a</mi><mn>4</mn></msup></mfrac><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>194</mn><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><msup><mrow><mo>[</mo><msup><mi>x</mi><mn>2</mn></msup><mo>-</mo><mn>2</mn><mo>]</mo></mrow><mn>2</mn></msup><mo>-</mo><mo>&nbsp;</mo><mn>2</mn><mspace linebreak=\"newline\"></mspace><mo>&rArr;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><msup><mrow><mo>[</mo><msup><mi>x</mi><mn>2</mn></msup><mo>-</mo><mn>2</mn><mo>]</mo></mrow><mn>2</mn></msup><mo>=</mo><mo>&nbsp;</mo><mn>196</mn><mo>&nbsp;</mo><mo>&rArr;</mo><mo>&nbsp;</mo><mi>x</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>4</mn><mspace linebreak=\"newline\"></mspace><mo>&nbsp;</mo><mi>a</mi><mo>&sup3;</mo><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mfrac><mn>1</mn><msup><mi>a</mi><mn>3</mn></msup></mfrac><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><msup><mi>x</mi><mn>3</mn></msup><mo>-</mo><mo>&nbsp;</mo><mn>3</mn><mi>x</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><msup><mn>4</mn><mn>3</mn></msup><mo>-</mo><mo>&nbsp;</mo><mn>3</mn><mo>&times;</mo><mn>4</mn><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>52</mn></math></span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">4.(</span><span style=\"font-family: Cambria Math;\">b)</span></p>\r\n<p><math style=\"font-family: Verdana;\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#2350;&#2366;&#2344;&#2366;</mi><mo>&nbsp;</mo><mi>a</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mfrac><mn>1</mn><mi>a</mi></mfrac><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mi>x</mi><mspace linebreak=\"newline\"></mspace><mo>&nbsp;</mo><msup><mi>a</mi><mn>4</mn></msup><mo>+</mo><mo>&nbsp;</mo><mfrac><mn>1</mn><msup><mi>a</mi><mn>4</mn></msup></mfrac><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>194</mn><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><msup><mrow><mo>[</mo><msup><mi>x</mi><mn>2</mn></msup><mo>-</mo><mn>2</mn><mo>]</mo></mrow><mn>2</mn></msup><mo>-</mo><mo>&nbsp;</mo><mn>2</mn><mspace linebreak=\"newline\"></mspace><mo>&rArr;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><msup><mrow><mo>[</mo><msup><mi>x</mi><mn>2</mn></msup><mo>-</mo><mn>2</mn><mo>]</mo></mrow><mn>2</mn></msup><mo>=</mo><mo>&nbsp;</mo><mn>196</mn><mo>&nbsp;</mo><mo>&rArr;</mo><mo>&nbsp;</mo><mi>x</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>4</mn><mspace linebreak=\"newline\"></mspace><mo>&nbsp;</mo><mi>a</mi><mo>&sup3;</mo><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mfrac><mn>1</mn><msup><mi>a</mi><mn>3</mn></msup></mfrac><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><msup><mi>x</mi><mn>3</mn></msup><mo>-</mo><mo>&nbsp;</mo><mn>3</mn><mi>x</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><msup><mn>4</mn><mn>3</mn></msup><mo>-</mo><mo>&nbsp;</mo><mn>3</mn><mo>&times;</mo><mn>4</mn><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>52</mn></math></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">5</span><span style=\"font-family: Cambria Math;\"> If </span><span style=\"font-family: Cambria Math;\">a+b</span><span style=\"font-family: Cambria Math;\">=3 and ab = 2, then what is the value of&nbsp; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>2</mn><msup><mi>a</mi><mn>3</mn></msup><mo>&nbsp;</mo><mo>+</mo><mn>2</mn><msup><mi>b</mi><mn>3</mn></msup></math>&nbsp; </span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">5.</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> a + b = 3 </span><span style=\"font-family: Kokila;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> ab = 2, </span><span style=\"font-family: Kokila;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>2</mn><msup><mi>a</mi><mn>3</mn></msup><mo>&nbsp;</mo><mo>+</mo><mn>2</mn><msup><mi>b</mi><mn>3</mn></msup></math></span><span style=\"font-family: Cambria Math;\">&nbsp;</span><span style=\"font-family: Kokila;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>25</p>\n", "<p>27</p>\n", 
                                "<p>18</p>\n", "<p>20</p>\n"],
                    options_hi: ["<p>25</p>\n", "<p>27</p>\n",
                                "<p>18</p>\n", "<p>20</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">5.(</span><span style=\"font-family: Cambria Math;\">c) </span><span style=\"font-family: Cambria Math;\">a+b</span><span style=\"font-family: Cambria Math;\">=3(cubing both side)</span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>a</mi><mn>3</mn></msup><mo>&nbsp;</mo><mo>+</mo><msup><mi>b</mi><mn>3</mn></msup><mo>+</mo><mn>3</mn><mi>a</mi><mi>b</mi><mo>(</mo><mi>a</mi><mo>+</mo><mi>b</mi><mo>)</mo><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>27</mn><mspace linebreak=\"newline\"></mspace><msup><mi>a</mi><mn>3</mn></msup><mo>&nbsp;</mo><mo>+</mo><msup><mi>b</mi><mn>3</mn></msup><mo>+</mo><mn>3</mn><mo>&times;</mo><mn>2</mn><mo>(</mo><mn>3</mn><mo>)</mo><mo>=</mo><mo>&nbsp;</mo><mn>27</mn><mspace linebreak=\"newline\"></mspace><mo>&nbsp;</mo><msup><mi>a</mi><mn>3</mn></msup><mo>&nbsp;</mo><mo>+</mo><msup><mi>b</mi><mn>3</mn></msup><mo>=</mo><mo>&nbsp;</mo><mn>9</mn></math></p>\r\n<p><span style=\"font-family: Cambria Math;\">According to the question, </span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> 2a&sup3; + 2b&sup3;&rArr;2(a&sup3;+b</span><span style=\"font-family: Cambria Math;\">&sup3; )</span><span style=\"font-family: Cambria Math;\"> &rArr;2(9) = 18</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Short </span><span style=\"font-family: Cambria Math;\">trick :</span><span style=\"font-family: Cambria Math;\">-</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Putting a = 2 and b = 1,</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> 2(</span><span style=\"font-family: Cambria Math;\">8)+</span><span style=\"font-family: Cambria Math;\"> 2(1)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&nbsp;&rArr; 16 + 2 = 18</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">5.(</span><span style=\"font-family: Cambria Math;\">c) </span><span style=\"font-family: Cambria Math;\">a+b</span><span style=\"font-family: Cambria Math;\">=3(</span><span style=\"font-family: Kokila;\">&#2342;&#2379;&#2344;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2340;&#2352;&#2347;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2328;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2352;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\">)</span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>a</mi><mn>3</mn></msup><mo>&nbsp;</mo><mo>+</mo><msup><mi>b</mi><mn>3</mn></msup><mo>+</mo><mn>3</mn><mi>a</mi><mi>b</mi><mo>(</mo><mi>a</mi><mo>+</mo><mi>b</mi><mo>)</mo><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>27</mn><mspace linebreak=\"newline\"></mspace><msup><mi>a</mi><mn>3</mn></msup><mo>&nbsp;</mo><mo>+</mo><msup><mi>b</mi><mn>3</mn></msup><mo>+</mo><mn>3</mn><mo>&times;</mo><mn>2</mn><mo>(</mo><mn>3</mn><mo>)</mo><mo>=</mo><mo>&nbsp;</mo><mn>27</mn><mspace linebreak=\"newline\"></mspace><mo>&nbsp;</mo><msup><mi>a</mi><mn>3</mn></msup><mo>&nbsp;</mo><mo>+</mo><msup><mi>b</mi><mn>3</mn></msup><mo>=</mo><mo>&nbsp;</mo><mn>9</mn></math></p>\r\n<p><span style=\"font-family: Kokila;\">&#2346;&#2381;&#2352;&#2358;&#2381;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2309;&#2344;&#2369;&#2360;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\">,</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> 2a&sup3; + 2b&sup3;&rArr;2(a&sup3;+b</span><span style=\"font-family: Cambria Math;\">&sup3; )</span><span style=\"font-family: Cambria Math;\"> &rArr;2(9) = 18</span></p>\r\n<p><span style=\"font-family: Kokila;\">&#2358;&#2366;&#2352;&#2381;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2335;&#2381;&#2352;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> :</span><span style=\"font-family: Cambria Math;\">-</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">a = 2 </span><span style=\"font-family: Kokila;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> b = 1 </span><span style=\"font-family: Kokila;\">&#2352;&#2326;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> ,</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> 2(</span><span style=\"font-family: Cambria Math;\">8)+</span><span style=\"font-family: Cambria Math;\"> 2(1)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">16 + 2 = 18</span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">6 </span><span style=\"font-family: Cambria Math;\">If P +&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi>P</mi></mfrac></math> </span><span style=\"font-family: Cambria Math;\">= 5, then what is the value of </span><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>p</mi><mn>3</mn></msup><mo>+</mo><mfrac><mn>1</mn><msup><mi>p</mi><mn>3</mn></msup></mfrac></math></span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">6. </span><span style=\"font-family: Kokila;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> P</span><span style=\"font-family: Cambria Math;\"> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi>P</mi></mfrac></math></span><span style=\"font-family: Cambria Math;\">= 5, </span><span style=\"font-family: Kokila;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>p</mi><mn>3</mn></msup><mo>+</mo><mfrac><mn>1</mn><msup><mi>p</mi><mn>3</mn></msup></mfrac></math></span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>110</p>\n", "<p>95</p>\n", 
                                "<p>125</p>\n", "<p>120</p>\n"],
                    options_hi: ["<p>110</p>\n", "<p>95</p>\n",
                                "<p>125</p>\n", "<p>120</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">6.(</span><span style=\"font-family: Cambria Math;\">a)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> P +&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi>P</mi></mfrac></math> </span><span style=\"font-family: Cambria Math;\">= 5 (cubing both sides)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>p</mi><mn>3</mn></msup><mo>+</mo><mfrac><mn>1</mn><msup><mi>p</mi><mn>3</mn></msup></mfrac></math></span><span style=\"font-family: Cambria Math;\"> = 125 - </span><span style=\"font-family: Cambria Math;\">15 = 110</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">6.(</span><span style=\"font-family: Cambria Math;\">a)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> P +&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi>P</mi></mfrac></math> </span><span style=\"font-family: Cambria Math;\">= </span><span style=\"font-family: Cambria Math;\">5 (</span><span style=\"font-family: Kokila;\">&#2342;&#2379;&#2344;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2325;&#2381;&#2359;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2328;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2352;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\">)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>p</mi><mn>3</mn></msup><mo>+</mo><mfrac><mn>1</mn><msup><mi>p</mi><mn>3</mn></msup></mfrac></math></span><span style=\"font-family: Cambria Math;\"> = 125 - </span><span style=\"font-family: Cambria Math;\">15 = 110</span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">7.</span><span style=\"font-family: Cambria Math;\"> If K +&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi>k</mi></mfrac></math> </span><span style=\"font-family: Cambria Math;\">= -3, then what is the value of&nbsp; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><mo>&nbsp;</mo><mfrac><mrow><mo>&nbsp;</mo><msup><mi>k</mi><mn>6</mn></msup><mo>&nbsp;</mo><mo>+</mo><mn>1</mn></mrow><msup><mi>k</mi><mn>3</mn></msup></mfrac><mo>)</mo><mo>+</mo><mo>(</mo><mo>&nbsp;</mo><mfrac><mrow><msup><mi>k</mi><mn>4</mn></msup><mo>+</mo><mn>1</mn></mrow><msup><mi>k</mi><mn>2</mn></msup></mfrac><mo>)</mo></math>&nbsp;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">7.</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> K + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi>k</mi></mfrac></math></span><span style=\"font-family: Cambria Math;\">= -</span><span style=\"font-family: Cambria Math;\">3,</span><span style=\"font-family: Kokila;\">&#2340;&#2379;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><mo>&nbsp;</mo><mfrac><mrow><mo>&nbsp;</mo><msup><mi>k</mi><mn>6</mn></msup><mo>&nbsp;</mo><mo>+</mo><mn>1</mn></mrow><msup><mi>k</mi><mn>3</mn></msup></mfrac><mo>)</mo><mo>+</mo><mo>(</mo><mo>&nbsp;</mo><mfrac><mrow><msup><mi>k</mi><mn>4</mn></msup><mo>+</mo><mn>1</mn></mrow><msup><mi>k</mi><mn>2</mn></msup></mfrac><mo>)</mo></math></span><span style=\"font-family: Cambria Math;\"> &nbsp;</span><span style=\"font-family: Kokila;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>27</p>\n", "<p>-29</p>\n", 
                                "<p>29</p>\n", "<p>-27</p>\n"],
                    options_hi: ["<p>27</p>\n", "<p>-29</p>\n",
                                "<p>29</p>\n", "<p>-27</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">7.(</span><span style=\"font-family: Cambria Math;\">b)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&nbsp;</mo><mi>K</mi><mo>&nbsp;</mo><mo>+</mo><mfrac><mn>1</mn><mi>k</mi></mfrac><mo>=</mo><mo>&nbsp;</mo><mo>-</mo><mn>3</mn><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>(</mo><mi>c</mi><mi>u</mi><mi>b</mi><mi>i</mi><mi>n</mi><mi>g</mi><mo>&nbsp;</mo><mi>b</mi><mi>o</mi><mi>t</mi><mi>h</mi><mo>&nbsp;</mo><mi>s</mi><mi>i</mi><mi>d</mi><mi>e</mi><mi>s</mi><mo>)</mo><mspace linebreak=\"newline\"></mspace><mo>&nbsp;</mo><msup><mi>k</mi><mn>3</mn></msup><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mfrac><mn>1</mn><msup><mi>k</mi><mn>3</mn></msup></mfrac><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mo>-</mo><mn>27</mn><mo>-</mo><mn>9</mn><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mo>-</mo><mn>36</mn><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mi>K</mi><mo>&nbsp;</mo><mo>+</mo><mfrac><mn>1</mn><mi>k</mi></mfrac><mo>=</mo><mo>&nbsp;</mo><mo>-</mo><mn>3</mn><mo>&nbsp;</mo><mo>(</mo><mi>s</mi><mi>q</mi><mi>u</mi><mi>a</mi><mi>r</mi><mi>i</mi><mi>n</mi><mi>g</mi><mo>&nbsp;</mo><mi>b</mi><mi>o</mi><mi>t</mi><mi>h</mi><mo>&nbsp;</mo><mi>s</mi><mi>i</mi><mi>d</mi><mi>e</mi><mi>s</mi><mo>)</mo><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><msup><mi>k</mi><mn>2</mn></msup><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mfrac><mn>1</mn><msup><mi>k</mi><mn>2</mn></msup></mfrac><mo>=</mo><mo>&nbsp;</mo><mn>9</mn><mo>&nbsp;</mo><mo>-</mo><mn>2</mn><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mn>7</mn><mo>&nbsp;</mo><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mo>(</mo><mo>&nbsp;</mo><mfrac><mrow><msup><mi>k</mi><mn>6</mn></msup><mo>&nbsp;</mo><mo>+</mo><mn>1</mn></mrow><msup><mi>k</mi><mn>3</mn></msup></mfrac><mo>&nbsp;</mo><mo>)</mo><mo>+</mo><mo>(</mo><mfrac><mrow><mo>&nbsp;</mo><msup><mi>k</mi><mn>4</mn></msup><mo>+</mo><mn>1</mn></mrow><msup><mi>k</mi><mn>2</mn></msup></mfrac><mo>)</mo><mspace linebreak=\"newline\"></mspace><mo>&nbsp;</mo><msup><mi>k</mi><mn>3</mn></msup><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mfrac><mn>1</mn><msup><mi>k</mi><mn>3</mn></msup></mfrac><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><msup><mi>k</mi><mn>2</mn></msup><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mfrac><mn>1</mn><msup><mi>k</mi><mn>2</mn></msup></mfrac><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mo>-</mo><mn>36</mn><mo>&nbsp;</mo><mo>+</mo><mn>7</mn><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mo>-</mo><mn>29</mn></math></span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">7.(</span><span style=\"font-family: Cambria Math;\">b)</span></p>\r\n<p><math style=\"font-family: Verdana;\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&nbsp;</mo><mi>K</mi><mo>&nbsp;</mo><mo>+</mo><mfrac><mn>1</mn><mi>k</mi></mfrac><mo>=</mo><mo>&nbsp;</mo><mo>-</mo><mn>3</mn><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>(</mo><mi>&#2342;&#2379;&#2344;&#2379;&#2306;</mi><mo>&nbsp;</mo><mi>&#2346;&#2325;&#2381;&#2359;&#2379;&#2306;</mi><mo>&nbsp;</mo><mi>&#2325;&#2379;</mi><mo>&nbsp;</mo><mi>&#2328;&#2344;</mi><mo>&nbsp;</mo><mi>&#2325;&#2352;&#2344;&#2375;</mi><mo>&nbsp;</mo><mi>&#2346;&#2352;</mi><mo>)</mo><mspace linebreak=\"newline\"></mspace><mo>&nbsp;</mo><msup><mi>k</mi><mn>3</mn></msup><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mfrac><mn>1</mn><msup><mi>k</mi><mn>3</mn></msup></mfrac><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mo>-</mo><mn>27</mn><mo>-</mo><mn>9</mn><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mo>-</mo><mn>36</mn><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mi>K</mi><mo>&nbsp;</mo><mo>+</mo><mfrac><mn>1</mn><mi>k</mi></mfrac><mo>=</mo><mo>&nbsp;</mo><mo>-</mo><mn>3</mn><mo>&nbsp;</mo><mo>(</mo><mi>&#2342;&#2379;&#2344;&#2379;&#2306;</mi><mo>&nbsp;</mo><mi>&#2346;&#2325;&#2381;&#2359;&#2379;&#2306;</mi><mo>&nbsp;</mo><mi>&#2325;&#2366;</mi><mo>&nbsp;</mo><mi>&#2357;&#2352;&#2381;&#2327;</mi><mo>&nbsp;</mo><mi>&#2325;&#2352;&#2344;&#2375;</mi><mo>&nbsp;</mo><mi>&#2346;&#2352;</mi><mo>)</mo><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><msup><mi>k</mi><mn>2</mn></msup><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mfrac><mn>1</mn><msup><mi>k</mi><mn>2</mn></msup></mfrac><mo>=</mo><mo>&nbsp;</mo><mn>9</mn><mo>&nbsp;</mo><mo>-</mo><mn>2</mn><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mn>7</mn><mo>&nbsp;</mo><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mo>(</mo><mo>&nbsp;</mo><mfrac><mrow><msup><mi>k</mi><mn>6</mn></msup><mo>&nbsp;</mo><mo>+</mo><mn>1</mn></mrow><msup><mi>k</mi><mn>3</mn></msup></mfrac><mo>&nbsp;</mo><mo>)</mo><mo>+</mo><mo>(</mo><mfrac><mrow><mo>&nbsp;</mo><msup><mi>k</mi><mn>4</mn></msup><mo>+</mo><mn>1</mn></mrow><msup><mi>k</mi><mn>2</mn></msup></mfrac><mo>)</mo><mspace linebreak=\"newline\"></mspace><mo>&nbsp;</mo><msup><mi>k</mi><mn>3</mn></msup><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mfrac><mn>1</mn><msup><mi>k</mi><mn>3</mn></msup></mfrac><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><msup><mi>k</mi><mn>2</mn></msup><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mfrac><mn>1</mn><msup><mi>k</mi><mn>2</mn></msup></mfrac><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mo>-</mo><mn>36</mn><mo>&nbsp;</mo><mo>+</mo><mn>7</mn><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mo>-</mo><mn>29</mn></math></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">8.</span><span style=\"font-family: Cambria Math;\">If&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>a</mi><mn>2</mn></msup><mo>&nbsp;</mo><mo>+</mo><msup><mi>b</mi><mn>2</mn></msup><mo>&nbsp;</mo><mo>+</mo><msup><mi>c</mi><mn>2</mn></msup></math></span><span style=\"font-family: Cambria Math;\"> &nbsp;</span><span style=\"font-family: Cambria Math;\">= 62 and a+b+c= 12, then what is the value of 4ab + 4bc + 4ca?</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">8.</span><span style=\"font-family: Kokila;\">&#2351;&#2342;&#2367;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>a</mi><mn>2</mn></msup><mo>&nbsp;</mo><mo>+</mo><msup><mi>b</mi><mn>2</mn></msup><mo>&nbsp;</mo><mo>+</mo><msup><mi>c</mi><mn>2</mn></msup></math></span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> = 62 </span><span style=\"font-family: Kokila;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> a + b + c = 12, </span><span style=\"font-family: Kokila;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> 4ab + 4bc + 4ca </span><span style=\"font-family: Kokila;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>164</p>\n", "<p>148</p>\n", 
                                "<p>152</p>\n", "<p>160</p>\n"],
                    options_hi: ["<p>164</p>\n", "<p>148</p>\n",
                                "<p>152</p>\n", "<p>160</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">8.(</span><span style=\"font-family: Cambria Math;\">a) </span><span style=\"font-family: Cambria Math;\">a&sup2;+b&sup2;+c&sup2;=62</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>a</mi><mo>+</mo><mi>b</mi><mo>+</mo><mi>c</mi><mo>=</mo><mo>&nbsp;</mo><mn>12</mn><mo>&nbsp;</mo><mo>&rArr;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><msup><mrow><mo>(</mo><mi>a</mi><mo>+</mo><mi>b</mi><mo>+</mo><mi>c</mi><mo>)</mo></mrow><mn>2</mn></msup><mo>=</mo><mo>&nbsp;</mo><msup><mrow><mo>(</mo><mn>12</mn><mo>)</mo></mrow><mn>2</mn></msup><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mo>&rArr;</mo><mo>&nbsp;</mo><mo>(</mo><msup><mi>a</mi><mn>2</mn></msup><mo>+</mo><msup><mi>b</mi><mn>2</mn></msup><mo>+</mo><msup><mi>c</mi><mn>2</mn></msup><mo>+</mo><mn>2</mn><mi>a</mi><mi>b</mi><mo>+</mo><mn>2</mn><mi>b</mi><mi>c</mi><mo>+</mo><mn>2</mn><mi>c</mi><mi>a</mi><mo>)</mo><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>144</mn><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mo>&rArr;</mo><mo>&nbsp;</mo><mo>(</mo><mn>2</mn><mi>a</mi><mi>b</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>2</mn><mi>b</mi><mi>c</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>2</mn><mi>c</mi><mi>a</mi><mo>)</mo><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>144</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>62</mn><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>82</mn><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mo>&rArr;</mo><mo>&nbsp;</mo><mo>(</mo><mi>a</mi><mi>b</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mi>b</mi><mi>c</mi><mo>&nbsp;</mo><mo>+</mo><mi>c</mi><mi>a</mi><mo>)</mo><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>41</mn><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mi>S</mi><mi>o</mi><mo>,</mo><mo>&nbsp;</mo><mn>4</mn><mi>a</mi><mi>b</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>4</mn><mi>b</mi><mi>c</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>4</mn><mi>c</mi><mi>a</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>41</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>4</mn><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>164</mn></math></span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">8.(</span><span style=\"font-family: Cambria Math;\">a) </span><span style=\"font-family: Cambria Math;\">a&sup2;+b&sup2;+c&sup2;=62 </span></p>\r\n<p><math style=\"font-family: Verdana;\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>a</mi><mo>+</mo><mi>b</mi><mo>+</mo><mi>c</mi><mo>=</mo><mo>&nbsp;</mo><mn>12</mn><mo>&nbsp;</mo><mo>&rArr;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><msup><mrow><mo>(</mo><mi>a</mi><mo>+</mo><mi>b</mi><mo>+</mo><mi>c</mi><mo>)</mo></mrow><mn>2</mn></msup><mo>=</mo><mo>&nbsp;</mo><msup><mrow><mo>(</mo><mn>12</mn><mo>)</mo></mrow><mn>2</mn></msup><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mo>&rArr;</mo><mo>&nbsp;</mo><mo>(</mo><msup><mi>a</mi><mn>2</mn></msup><mo>+</mo><msup><mi>b</mi><mn>2</mn></msup><mo>+</mo><msup><mi>c</mi><mn>2</mn></msup><mo>+</mo><mn>2</mn><mi>a</mi><mi>b</mi><mo>+</mo><mn>2</mn><mi>b</mi><mi>c</mi><mo>+</mo><mn>2</mn><mi>c</mi><mi>a</mi><mo>)</mo><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>144</mn><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mo>&rArr;</mo><mo>&nbsp;</mo><mo>(</mo><mn>2</mn><mi>a</mi><mi>b</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>2</mn><mi>b</mi><mi>c</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>2</mn><mi>c</mi><mi>a</mi><mo>)</mo><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>144</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>62</mn><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>82</mn><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mo>&rArr;</mo><mo>&nbsp;</mo><mo>(</mo><mi>a</mi><mi>b</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mi>b</mi><mi>c</mi><mo>&nbsp;</mo><mo>+</mo><mi>c</mi><mi>a</mi><mo>)</mo><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>41</mn><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mi>&#2311;&#2360;&#2354;&#2367;&#2319;</mi><mo>,</mo><mo>&nbsp;</mo><mn>4</mn><mi>a</mi><mi>b</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>4</mn><mi>b</mi><mi>c</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>4</mn><mi>c</mi><mi>a</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>41</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>4</mn><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>164</mn></math></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">9. </span><span style=\"font-family: Cambria Math;\">If&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>x</mi><mo>&nbsp;</mo><mo>+</mo><mfrac><mn>1</mn><mi>x</mi></mfrac><mo>=</mo><mfrac><mi>K</mi><mn>2</mn></mfrac></math> &nbsp;</span><span style=\"font-family: Cambria Math;\">, then what is the value of&nbsp; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&nbsp;</mo><mfrac><mrow><msup><mi>x</mi><mn>8</mn></msup><mo>+</mo><mn>1</mn></mrow><msup><mi>x</mi><mn>4</mn></msup></mfrac></math>&nbsp;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">9. </span><span style=\"font-family: Kokila;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>x</mi><mo>&nbsp;</mo><mo>+</mo><mfrac><mn>1</mn><mi>x</mi></mfrac><mo>=</mo><mfrac><mi>K</mi><mn>2</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\">&nbsp;, </span><span style=\"font-family: Kokila;\">&#2340;&#2379;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi>x</mi><mn>8</mn></msup><mo>+</mo><mn>1</mn></mrow><msup><mi>x</mi><mn>4</mn></msup></mfrac></math></span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi>K</mi><mn>4</mn></msup><mo>-</mo><mn>16</mn><msup><mi>K</mi><mn>2</mn></msup><mo>+</mo><mn>32</mn></mrow><mn>16</mn></mfrac></math></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi>K</mi><mn>4</mn></msup><mo>-</mo><mn>8</mn><msup><mi>K</mi><mn>2</mn></msup><mo>+</mo><mn>36</mn></mrow><mn>32</mn></mfrac></math></p>\n", 
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi>K</mi><mn>4</mn></msup><mo>-</mo><mn>8</mn><msup><mi>K</mi><mn>2</mn></msup><mo>-</mo><mn>32</mn></mrow><mn>16</mn></mfrac></math></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi>K</mi><mn>4</mn></msup><mo>-</mo><mn>8</mn><msup><mi>K</mi><mn>2</mn></msup><mo>+</mo><mn>32</mn></mrow><mn>16</mn></mfrac></math></p>\n"],
                    options_hi: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi>K</mi><mn>4</mn></msup><mo>-</mo><mn>16</mn><msup><mi>K</mi><mn>2</mn></msup><mo>+</mo><mn>32</mn></mrow><mn>16</mn></mfrac></math></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi>K</mi><mn>4</mn></msup><mo>-</mo><mn>8</mn><msup><mi>K</mi><mn>2</mn></msup><mo>+</mo><mn>36</mn></mrow><mn>32</mn></mfrac></math></p>\n",
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi>K</mi><mn>4</mn></msup><mo>-</mo><mn>8</mn><msup><mi>K</mi><mn>2</mn></msup><mo>-</mo><mn>32</mn></mrow><mn>16</mn></mfrac></math></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi>K</mi><mn>4</mn></msup><mo>-</mo><mn>8</mn><msup><mi>K</mi><mn>2</mn></msup><mo>+</mo><mn>32</mn></mrow><mn>16</mn></mfrac></math></p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">9.(</span><span style=\"font-family: Cambria Math;\">a)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>x</mi><mo>+</mo><mfrac><mn>1</mn><mi>x</mi></mfrac><mo>=</mo><mfrac><mi>K</mi><mn>2</mn></mfrac><mspace linebreak=\"newline\"></mspace><mfrac><mrow><msup><mi>x</mi><mn>8</mn></msup><mo>+</mo><mn>1</mn></mrow><msup><mi>x</mi><mn>4</mn></msup></mfrac><mo>[</mo><mi>d</mi><mi>i</mi><mi>v</mi><mi>i</mi><mi>d</mi><mi>i</mi><mi>n</mi><mi>g</mi><mi>b</mi><mi>y</mi><mo>&nbsp;</mo><msup><mi>x</mi><mn>4</mn></msup><mo>]</mo><mspace linebreak=\"newline\"></mspace><msup><mi>x</mi><mn>4</mn></msup><mo>+</mo><mfrac><mn>1</mn><msup><mi>x</mi><mn>4</mn></msup></mfrac><mo>=</mo><mo>[</mo><msup><mrow><mo>{</mo><mrow><msup><mrow><mo>(</mo><mfrac><mi>K</mi><mn>2</mn></mfrac><mo>)</mo></mrow><mn>2</mn></msup><mo>-</mo><mn>2</mn></mrow><mo>}</mo></mrow><mn>2</mn></msup><mo>-</mo><mn>2</mn><mo>]</mo><mspace linebreak=\"newline\"></mspace><mo>=</mo><mo>[</mo><msup><mrow><mo>{</mo><mfrac><mrow><msup><mi>K</mi><mn>2</mn></msup><mo>-</mo><mn>8</mn></mrow><mn>4</mn></mfrac><mo>}</mo></mrow><mn>2</mn></msup><mo>-</mo><mn>2</mn><mo>]</mo><mspace linebreak=\"newline\"></mspace><mo>=</mo><mfrac><mrow><msup><mi>K</mi><mn>4</mn></msup><mo>-</mo><mn>16</mn><msup><mi>K</mi><mn>2</mn></msup><mo>+</mo><mn>32</mn></mrow><mn>16</mn></mfrac></math></span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">9.(</span><span style=\"font-family: Cambria Math;\">a)</span></p>\r\n<p><math style=\"font-family: Verdana;\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>x</mi><mo>+</mo><mfrac><mn>1</mn><mi>x</mi></mfrac><mo>=</mo><mfrac><mi>K</mi><mn>2</mn></mfrac><mspace linebreak=\"newline\"></mspace><mfrac><mrow><msup><mi>x</mi><mn>8</mn></msup><mo>+</mo><mn>1</mn></mrow><msup><mi>x</mi><mn>4</mn></msup></mfrac><mo>[</mo><mo>&nbsp;</mo><msup><mi>x</mi><mn>4</mn></msup><mo>&nbsp;</mo><mi>&#2360;&#2375;</mi><mo>&nbsp;</mo><mi>&#2349;&#2366;&#2327;</mi><mo>&nbsp;</mo><mi>&#2342;&#2375;&#2344;&#2375;</mi><mo>&nbsp;</mo><mi>&#2346;&#2352;</mi><mo>]</mo><mspace linebreak=\"newline\"></mspace><msup><mi>x</mi><mn>4</mn></msup><mo>+</mo><mfrac><mn>1</mn><msup><mi>x</mi><mn>4</mn></msup></mfrac><mo>=</mo><mo>[</mo><msup><mrow><mo>{</mo><msup><mrow><mo>(</mo><mfrac><mi>K</mi><mn>2</mn></mfrac><mo>)</mo></mrow><mn>2</mn></msup><mo>-</mo><mn>2</mn><mo>}</mo></mrow><mn>2</mn></msup><mo>-</mo><mn>2</mn><mo>]</mo><mspace linebreak=\"newline\"></mspace><mo>=</mo><mo>[</mo><msup><mrow><mo>{</mo><mfrac><mrow><msup><mi>K</mi><mn>2</mn></msup><mo>-</mo><mn>8</mn></mrow><mn>4</mn></mfrac><mo>}</mo></mrow><mn>2</mn></msup><mo>-</mo><mn>2</mn><mo>]</mo><mspace linebreak=\"newline\"></mspace><mo>=</mo><mfrac><mrow><msup><mi>K</mi><mn>4</mn></msup><mo>-</mo><mn>16</mn><msup><mi>K</mi><mn>2</mn></msup><mo>+</mo><mn>32</mn></mrow><mn>16</mn></mfrac></math></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">10.</span><span style=\"font-family: Cambria Math;\"> If (p - </span><span style=\"font-family: Cambria Math;\">q)=</span><span style=\"font-family: Cambria Math;\">6, ( r - q) = 5 and (r - p) = 3, then find the value of (p&sup3; + q&sup3; + r&sup3; - 3pqr)/(p + q + r).</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">10. </span><span style=\"font-family: Kokila;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> (p - q) = 6, (r - q) = 5 </span><span style=\"font-family: Kokila;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> (r - p) = 3, </span><span style=\"font-family: Kokila;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> (p&sup3; + q</span><span style=\"font-family: Cambria Math;\">&sup3; +</span><span style=\"font-family: Cambria Math;\"> r&sup3; - 3pqr)/(p + q + r) </span><span style=\"font-family: Kokila;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2332;&#2381;&#2334;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2368;&#2332;&#2367;&#2319;</span><span style=\"font-family: Kokila;\">&#2404;</span></p>\n",
                    options_en: ["<p>35</p>\n", "<p>45</p>\n", 
                                "<p>30</p>\n", "<p>40</p>\n"],
                    options_hi: ["<p>35</p>\n", "<p>45</p>\n",
                                "<p>30</p>\n", "<p>40</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">10.(</span><span style=\"font-family: Cambria Math;\">a) </span><span style=\"font-family: Cambria Math;\">Formula :</span><span style=\"font-family: Cambria Math;\">-</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfenced><mrow><msup><mi>a</mi><mn>3</mn></msup><mo>+</mo><msup><mi>b</mi><mn>3</mn></msup><mo>+</mo><msup><mi>c</mi><mn>3</mn></msup><mo>-</mo><mn>3</mn><mi>a</mi><mi>b</mi><mi>c</mi></mrow></mfenced><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mfrac><mrow><mi>a</mi><mo>+</mo><mi>b</mi><mo>+</mo><mi>c</mi></mrow><mn>2</mn></mfrac><mo>{</mo><msup><mrow><mo>(</mo><mi>a</mi><mo>-</mo><mi>b</mi><mo>)</mo></mrow><mn>2</mn></msup><mo>+</mo><msup><mrow><mo>(</mo><mi>b</mi><mo>-</mo><mi>c</mi><mo>)</mo></mrow><mn>2</mn></msup><mo>+</mo><msup><mrow><mo>(</mo><mi>c</mi><mo>-</mo><mi>a</mi><mo>)</mo></mrow><mn>2</mn></msup><mo>&nbsp;</mo><mo>}</mo><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mfrac><mfenced><mrow><msup><mi>p</mi><mn>3</mn></msup><mo>+</mo><msup><mi>q</mi><mn>3</mn></msup><mo>+</mo><msup><mi>r</mi><mn>3</mn></msup><mo>-</mo><mn>3</mn><mi>p</mi><mi>q</mi><mi>r</mi></mrow></mfenced><mrow><mo>&nbsp;</mo><mo>(</mo><mi>p</mi><mo>+</mo><mi>q</mi><mo>+</mo><mi>r</mi><mo>)</mo></mrow></mfrac><mo>&nbsp;</mo><mo>=</mo><mfrac><mrow><mo>{</mo><mo>&nbsp;</mo><msup><mfenced><mn>6</mn></mfenced><mn>2</mn></msup><mo>+</mo><msup><mfenced><mrow><mo>-</mo><mn>5</mn></mrow></mfenced><mn>2</mn></msup><mo>+</mo><msup><mfenced><mn>3</mn></mfenced><mn>2</mn></msup><mo>}</mo></mrow><mn>2</mn></mfrac><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>35</mn></math></span></p>\r\n<p>&nbsp;</p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">10.(</span><span style=\"font-family: Cambria Math;\">a) </span><span style=\"font-family: Kokila;\">&#2360;&#2370;&#2340;&#2381;&#2352;</span><span style=\"font-family: Cambria Math;\"> :</span><span style=\"font-family: Cambria Math;\">-</span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfenced><mrow><msup><mi>a</mi><mn>3</mn></msup><mo>+</mo><msup><mi>b</mi><mn>3</mn></msup><mo>+</mo><msup><mi>c</mi><mn>3</mn></msup><mo>-</mo><mn>3</mn><mi>a</mi><mi>b</mi><mi>c</mi></mrow></mfenced><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mfrac><mrow><mi>a</mi><mo>+</mo><mi>b</mi><mo>+</mo><mi>c</mi></mrow><mn>2</mn></mfrac><mo>{</mo><msup><mrow><mo>(</mo><mi>a</mi><mo>-</mo><mi>b</mi><mo>)</mo></mrow><mn>2</mn></msup><mo>+</mo><msup><mrow><mo>(</mo><mi>b</mi><mo>-</mo><mi>c</mi><mo>)</mo></mrow><mn>2</mn></msup><mo>+</mo><msup><mrow><mo>(</mo><mi>c</mi><mo>-</mo><mi>a</mi><mo>)</mo></mrow><mn>2</mn></msup><mo>&nbsp;</mo><mo>}</mo><mspace linebreak=\"newline\"></mspace><mfrac><mfenced><mrow><msup><mi>p</mi><mn>3</mn></msup><mo>+</mo><msup><mi>q</mi><mn>3</mn></msup><mo>+</mo><msup><mi>r</mi><mn>3</mn></msup><mo>-</mo><mn>3</mn><mi>p</mi><mi>q</mi><mi>r</mi></mrow></mfenced><mrow><mo>&nbsp;</mo><mo>(</mo><mi>p</mi><mo>+</mo><mi>q</mi><mo>+</mo><mi>r</mi><mo>)</mo></mrow></mfrac><mo>&nbsp;</mo><mo>=</mo><mfrac><mrow><mo>{</mo><mo>&nbsp;</mo><msup><mfenced><mn>6</mn></mfenced><mn>2</mn></msup><mo>+</mo><msup><mfenced><mrow><mo>-</mo><mn>5</mn></mrow></mfenced><mn>2</mn></msup><mo>+</mo><msup><mfenced><mn>3</mn></mfenced><mn>2</mn></msup><mo>}</mo></mrow><mn>2</mn></mfrac><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>35</mn></math></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">11.</span><span style=\"font-family: Cambria Math;\">If</span><span style=\"font-family: Cambria Math;\"> a = 10 and b = 3, then what is the value of&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msup><mrow><mo>(</mo><mi>a</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mi>b</mi><mo>)</mo></mrow><mn>2</mn></msup><mrow><msup><mi>a</mi><mn>2</mn></msup><mo>-</mo><mo>&nbsp;</mo><msup><mi>b</mi><mn>2</mn></msup></mrow></mfrac></math> &nbsp;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">11. </span><span style=\"font-family: Kokila;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> a = 10 </span><span style=\"font-family: Kokila;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> b = 3 </span><span style=\"font-family: Kokila;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Kokila;\">&#2340;&#2379;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msup><mrow><mo>(</mo><mi>a</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mi>b</mi><mo>)</mo></mrow><mn>2</mn></msup><mrow><msup><mi>a</mi><mn>2</mn></msup><mo>-</mo><mo>&nbsp;</mo><msup><mi>b</mi><mn>2</mn></msup></mrow></mfrac></math></span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>182</mn><mn>93</mn></mfrac></math></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>169</mn><mn>91</mn></mfrac></math></p>\n", 
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>185</mn><mn>91</mn></mfrac></math><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>185</mn><mn>93</mn></mfrac></math></p>\n"],
                    options_hi: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>182</mn><mn>93</mn></mfrac></math></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>169</mn><mn>91</mn></mfrac></math></p>\n",
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>185</mn><mn>91</mn></mfrac></math><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>185</mn><mn>93</mn></mfrac></math></p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">11.(</span><span style=\"font-family: Cambria Math;\">b)</span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msup><mrow><mo>(</mo><mi>a</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mi>b</mi><mo>)</mo></mrow><mn>2</mn></msup><mrow><msup><mi>a</mi><mn>2</mn></msup><mo>-</mo><mo>&nbsp;</mo><msup><mi>b</mi><mn>2</mn></msup></mrow></mfrac><mo>&nbsp;</mo><mo>=</mo><mfrac><mrow><mo>&nbsp;</mo><msup><mrow><mo>(</mo><mi>a</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mi>b</mi><mo>)</mo></mrow><mn>2</mn></msup></mrow><mrow><mo>(</mo><mi>a</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mi>b</mi><mo>)</mo><mo>(</mo><mi>a</mi><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mi>b</mi><mo>)</mo></mrow></mfrac><mo>&nbsp;</mo><mo>=</mo><mfrac><mrow><mo>&nbsp;</mo><mo>(</mo><mi>a</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mi>b</mi><mo>)</mo></mrow><mrow><mo>(</mo><mi>a</mi><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mi>b</mi><mo>)</mo></mrow></mfrac><mspace linebreak=\"newline\"></mspace><mo>&nbsp;</mo><mi>N</mi><mi>o</mi><mi>w</mi><mo>,</mo><mfrac><mrow><mo>&nbsp;</mo><mo>(</mo><mi>a</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mi>b</mi><mo>)</mo></mrow><mrow><mo>(</mo><mi>a</mi><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mi>b</mi><mo>)</mo></mrow></mfrac><mo>&nbsp;</mo><mo>=</mo><mfrac><mrow><mo>&nbsp;</mo><mn>13</mn></mrow><mn>7</mn></mfrac><mo>&nbsp;</mo><mo>&nbsp;</mo><mi>o</mi><mi>r</mi><mfrac><mrow><mo>&nbsp;</mo><mn>13</mn><mo>&times;</mo><mn>13</mn></mrow><mrow><mn>7</mn><mo>&times;</mo><mn>13</mn></mrow></mfrac><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mfrac><mn>169</mn><mn>91</mn></mfrac></math></p>\r\n<p><strong>&nbsp;Short trick:-&nbsp;</strong></p>\r\n<p><strong>Put the value of a and b.</strong></p>\r\n<p><strong><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&nbsp;</mo><mo>&rArr;</mo><mfrac><mrow><mo>&nbsp;</mo><msup><mrow><mo>(</mo><mi>a</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mi>b</mi><mo>)</mo></mrow><mn>2</mn></msup></mrow><mrow><mo>&nbsp;</mo><msup><mi>a</mi><mn>2</mn></msup><mo>-</mo><mo>&nbsp;</mo><msup><mi>b</mi><mn>2</mn></msup></mrow></mfrac><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mfrac><mrow><mo>&nbsp;</mo><msup><mrow><mo>(</mo><mn>10</mn><mo>+</mo><mo>&nbsp;</mo><mn>3</mn><mo>)</mo></mrow><mn>2</mn></msup></mrow><mrow><msup><mn>10</mn><mn>2</mn></msup><mo>-</mo><mo>&nbsp;</mo><msup><mn>3</mn><mn>2</mn></msup></mrow></mfrac><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mfrac><mrow><mo>&nbsp;</mo><mn>169</mn></mrow><mn>91</mn></mfrac></math></strong></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">11.(</span><span style=\"font-family: Cambria Math;\">b)</span></p>\r\n<p><math style=\"font-family: Verdana;\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msup><mrow><mo>(</mo><mi>a</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mi>b</mi><mo>)</mo></mrow><mn>2</mn></msup><mrow><msup><mi>a</mi><mn>2</mn></msup><mo>-</mo><mo>&nbsp;</mo><msup><mi>b</mi><mn>2</mn></msup></mrow></mfrac><mo>&nbsp;</mo><mo>=</mo><mfrac><mrow><mo>&nbsp;</mo><msup><mrow><mo>(</mo><mi>a</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mi>b</mi><mo>)</mo></mrow><mn>2</mn></msup></mrow><mrow><mo>(</mo><mi>a</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mi>b</mi><mo>)</mo><mo>(</mo><mi>a</mi><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mi>b</mi><mo>)</mo></mrow></mfrac><mo>&nbsp;</mo><mo>=</mo><mfrac><mrow><mo>&nbsp;</mo><mo>(</mo><mi>a</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mi>b</mi><mo>)</mo></mrow><mrow><mo>(</mo><mi>a</mi><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mi>b</mi><mo>)</mo></mrow></mfrac><mspace linebreak=\"newline\"></mspace><mo>&nbsp;</mo><mi>&#2309;&#2348;</mi><mo>,</mo><mfrac><mrow><mo>&nbsp;</mo><mo>(</mo><mi>a</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mi>b</mi><mo>)</mo></mrow><mrow><mo>(</mo><mi>a</mi><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mi>b</mi><mo>)</mo></mrow></mfrac><mo>&nbsp;</mo><mo>=</mo><mfrac><mrow><mo>&nbsp;</mo><mn>13</mn></mrow><mn>7</mn></mfrac><mo>&nbsp;</mo><mo>&nbsp;</mo><mi>o</mi><mi>r</mi><mfrac><mrow><mo>&nbsp;</mo><mn>13</mn><mo>&times;</mo><mn>13</mn></mrow><mrow><mn>7</mn><mo>&times;</mo><mn>13</mn></mrow></mfrac><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mfrac><mn>169</mn><mn>91</mn></mfrac></math></p>\r\n<p><span style=\"font-family: Kokila;\">&#2358;&#2366;&#2352;&#2381;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2335;&#2381;&#2352;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> :</span><span style=\"font-family: Cambria Math;\">-</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">a </span><span style=\"font-family: Kokila;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> b </span><span style=\"font-family: Kokila;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2352;&#2326;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> ,</span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&rArr;</mo><mfrac><mrow><mo>&nbsp;</mo><msup><mrow><mo>(</mo><mi>a</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mi>b</mi><mo>)</mo></mrow><mn>2</mn></msup></mrow><mrow><mo>&nbsp;</mo><msup><mi>a</mi><mn>2</mn></msup><mo>-</mo><mo>&nbsp;</mo><msup><mi>b</mi><mn>2</mn></msup></mrow></mfrac><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mfrac><mrow><mo>&nbsp;</mo><msup><mrow><mo>(</mo><mn>10</mn><mo>+</mo><mo>&nbsp;</mo><mn>3</mn><mo>)</mo></mrow><mn>2</mn></msup></mrow><mrow><msup><mn>10</mn><mn>2</mn></msup><mo>-</mo><mo>&nbsp;</mo><msup><mn>3</mn><mn>2</mn></msup></mrow></mfrac><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mfrac><mrow><mo>&nbsp;</mo><mn>169</mn></mrow><mn>91</mn></mfrac></math></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">12. </span><span style=\"font-family: Cambria Math;\">What is the value of (a + </span><span style=\"font-family: Cambria Math;\">b)&sup2;</span><span style=\"font-family: Cambria Math;\"> + (a - b)&sup2;?</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">12. </span><span style=\"font-family: Cambria Math;\">(a + </span><span style=\"font-family: Cambria Math;\">b)&sup2;</span><span style=\"font-family: Cambria Math;\"> + (a - b)&sup2; </span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>8<span style=\"font-family: Cambria Math;\">ab</span></p>\n", "<p>4<span style=\"font-family: Cambria Math;\">ab</span></p>\n", 
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&nbsp;</mo><mn>4</mn><mo>(</mo><mi>a</mi><mo>&sup2;</mo><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mi>b</mi><mo>&sup2;</mo><mo>)</mo></math></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>2</mn><mo>(</mo><mi>a</mi><mo>&sup2;</mo><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mi>b</mi><mo>&sup2;</mo><mo>)</mo></math></p>\n"],
                    options_hi: ["<p>8<span style=\"font-family: Cambria Math;\">ab</span></p>\n", "<p>4<span style=\"font-family: Cambria Math;\">ab</span></p>\n",
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&nbsp;</mo><mn>4</mn><mo>(</mo><mi>a</mi><mo>&sup2;</mo><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mi>b</mi><mo>&sup2;</mo><mo>)</mo></math></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>2</mn><mo>(</mo><mi>a</mi><mo>&sup2;</mo><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mi>b</mi><mo>&sup2;</mo><mo>)</mo></math></p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">12.(</span><span style=\"font-family: Cambria Math;\">d)</span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><mi>a</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mi>b</mi><mo>)</mo><mo>&sup2;</mo><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mo>(</mo><mi>a</mi><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mi>b</mi><mo>)</mo><mo>&sup2;</mo><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>2</mn><mo>(</mo><msup><mi>a</mi><mn>2</mn></msup><mo>+</mo><msup><mi>b</mi><mn>2</mn></msup><mo>)</mo></math></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">12.(</span><span style=\"font-family: Cambria Math;\">d)</span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><mi>a</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mi>b</mi><mo>)</mo><mo>&sup2;</mo><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mo>(</mo><mi>a</mi><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mi>b</mi><mo>)</mo><mo>&sup2;</mo><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>2</mn><mo>(</mo><msup><mi>a</mi><mn>2</mn></msup><mo>+</mo><msup><mi>b</mi><mn>2</mn></msup><mo>)</mo></math></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">13. </span><span style=\"font-family: Cambria Math;\">If A +&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi>A</mi></mfrac></math>&nbsp;</span><span style=\"font-family: Cambria Math;\"> = -1, then find the value of&nbsp;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi>A</mi><mn>6</mn></msup><mo>+</mo><msup><mi>A</mi><mn>3</mn></msup><mo>-</mo><mn>1</mn></mrow><mrow><msup><mi>A</mi><mn>9</mn></msup><mo>+</mo><msup><mi>A</mi><mn>3</mn></msup><mo>-</mo><mn>1</mn></mrow></mfrac></math></span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">13. </span><span style=\"font-family: Kokila;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> A +&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi>A</mi></mfrac></math>&nbsp;</span><span style=\"font-family: Cambria Math;\"> = -1, </span><span style=\"font-family: Kokila;\">&#2340;&#2379;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi>A</mi><mn>6</mn></msup><mo>+</mo><msup><mi>A</mi><mn>3</mn></msup><mo>-</mo><mn>1</mn></mrow><mrow><msup><mi>A</mi><mn>9</mn></msup><mo>+</mo><msup><mi>A</mi><mn>3</mn></msup><mo>-</mo><mn>1</mn></mrow></mfrac></math></span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2332;&#2381;&#2334;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2368;&#2332;&#2367;&#2319;</span><span style=\"font-family: Kokila;\">&#2404;</span></p>\n",
                    options_en: ["<p>0</p>\n", "<p>-1</p>\n", 
                                "<p>2</p>\n", "<p>1</p>\n"],
                    options_hi: ["<p>0</p>\n", "<p>-1</p>\n",
                                "<p>2</p>\n", "<p>1</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">13.(</span><span style=\"font-family: Cambria Math;\">d)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">If A</span><span style=\"font-family: Cambria Math;\"> +&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi>A</mi></mfrac></math> &nbsp;</span><span style=\"font-family: Cambria Math;\"> = -1 , then&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>A</mi><mn>3</mn></msup></math>&nbsp;</span><span style=\"font-family: Cambria Math;\"> = 1</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">According to the question,</span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&nbsp;</mo><mfrac><mrow><msup><mi>A</mi><mn>6</mn></msup><mo>+</mo><msup><mi>A</mi><mn>3</mn></msup><mo>-</mo><mn>1</mn></mrow><mrow><msup><mi>A</mi><mn>9</mn></msup><mo>+</mo><msup><mi>A</mi><mn>3</mn></msup><mo>-</mo><mn>1</mn></mrow></mfrac><mo>&nbsp;</mo><mo>&rArr;</mo><mo>&nbsp;</mo><mfrac><mrow><mn>1</mn><mo>&nbsp;</mo><mo>+</mo><mn>1</mn><mo>&nbsp;</mo><mo>-</mo><mn>1</mn></mrow><mrow><mn>1</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>1</mn><mo>-</mo><mn>1</mn></mrow></mfrac><mo>&nbsp;</mo><mo>&rArr;</mo><mo>&nbsp;</mo><mn>1</mn></math></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">13.(</span><span style=\"font-family: Cambria Math;\">d)</span></p>\r\n<p><span style=\"font-family: Kokila;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> A +&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi>A</mi></mfrac></math>&nbsp;</span><span style=\"font-family: Cambria Math;\"> = -</span><span style=\"font-family: Cambria Math;\">1 ,</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2340;&#2379;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>A</mi><mn>3</mn></msup></math></span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> = 1</span></p>\r\n<p><span style=\"font-family: Kokila;\">&#2346;&#2381;&#2352;&#2358;&#2381;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2309;&#2344;&#2369;&#2360;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\">,</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&nbsp;</mo><mfrac><mrow><msup><mi>A</mi><mn>6</mn></msup><mo>+</mo><msup><mi>A</mi><mn>3</mn></msup><mo>-</mo><mn>1</mn></mrow><mrow><msup><mi>A</mi><mn>9</mn></msup><mo>+</mo><msup><mi>A</mi><mn>3</mn></msup><mo>-</mo><mn>1</mn></mrow></mfrac><mo>&nbsp;</mo><mo>&rArr;</mo><mo>&nbsp;</mo><mfrac><mrow><mn>1</mn><mo>&nbsp;</mo><mo>+</mo><mn>1</mn><mo>&nbsp;</mo><mo>-</mo><mn>1</mn></mrow><mrow><mn>1</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>1</mn><mo>-</mo><mn>1</mn></mrow></mfrac><mo>&nbsp;</mo><mo>&rArr;</mo><mo>&nbsp;</mo><mn>1</mn></math></span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">14.</span><span style=\"font-family: Cambria Math;\"> If (x + </span><span style=\"font-family: Cambria Math;\">1)&sup2;</span><span style=\"font-family: Cambria Math;\"> + (x + 2)&sup2; = 16, then what is the value of 4x&sup2; + 12x + 40?</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">14.</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> (</span><span style=\"font-family: Cambria Math;\">x + 1)&sup2; + (x + 2)&sup2; = 16, </span><span style=\"font-family: Kokila;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> 4x&sup2; + 12x + 40 </span><span style=\"font-family: Kokila;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>52</p>\n", "<p>62</p>\n", 
                                "<p>56</p>\n", "<p>74</p>\n"],
                    options_hi: ["<p>52</p>\n", "<p>62</p>\n",
                                "<p>56</p>\n", "<p>74</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">14.(</span><span style=\"font-family: Cambria Math;\">b)</span></p>\r\n<p>(x+1)&sup2;<span style=\"font-family: Cambria Math;\"> + (x + 2)&sup2; = 16</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&nbsp;x&sup2;+ 1+ 2x</span><span style=\"font-family: Cambria Math;\"> + x&sup2; </span><span style=\"font-family: Cambria Math;\">+ 4 + 4x</span><span style=\"font-family: Cambria Math;\"> = 16</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">2&nbsp;x&sup2;</span><span style=\"font-family: Cambria Math;\"> + 6x</span><span style=\"font-family: Cambria Math;\"> + 5 = 16 </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">4x&sup2; + 12x</span><span style=\"font-family: Cambria Math;\"> + 10 = </span><span style=\"font-family: Cambria Math;\">32 (</span><span style=\"font-family: Cambria Math;\">multiply by 2 both side)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">4x&sup2; + 12x</span><span style=\"font-family: Cambria Math;\"> + 10 +30 = 32 +30 (add 30 both side)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">4x&sup2; + 12x</span><span style=\"font-family: Cambria Math;\"> + 40 = 62</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">14.(</span><span style=\"font-family: Cambria Math;\">b)</span></p>\r\n<p>(x+1)&sup2;<span style=\"font-family: Cambria Math;\"> + (x + 2)&sup2; = 16</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&nbsp;x&sup2;+ 1+ 2x</span><span style=\"font-family: Cambria Math;\"> + x&sup2; </span><span style=\"font-family: Cambria Math;\">+ 4 + 4x</span><span style=\"font-family: Cambria Math;\"> = 16</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">2x&sup2;</span><span style=\"font-family: Cambria Math;\"> + 6x</span><span style=\"font-family: Cambria Math;\"> + 5 = 16 </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">4x&sup2; + 12x</span><span style=\"font-family: Cambria Math;\"> + 10 = 32 (</span><span style=\"font-family: Kokila;\">&#2342;&#2379;&#2344;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2340;&#2352;&#2347;</span><span style=\"font-family: Cambria Math;\"> 2 </span><span style=\"font-family: Kokila;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2327;&#2369;&#2339;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2352;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> )</span><span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">4x&sup2; + 12x</span><span style=\"font-family: Cambria Math;\"> + 10 +30 = 32 +</span><span style=\"font-family: Cambria Math;\">30 (</span><span style=\"font-family: Kokila;\">&#2342;&#2379;&#2344;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2325;&#2381;&#2359;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> 30 </span><span style=\"font-family: Kokila;\">&#2332;&#2379;&#2396;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> )</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">4x&sup2; + 12x</span><span style=\"font-family: Cambria Math;\"> + 40 = 62</span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "misc",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">15. </span><span style=\"font-family: Cambria Math;\">What is the expansion of (x + 11) (x - 11</span><span style=\"font-family: Cambria Math;\">) ?</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">15. (</span><span style=\"font-family: Cambria Math;\">x + 11) (x - 11) </span><span style=\"font-family: Kokila;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2381;&#2352;&#2360;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> ?</span></p>\n",
                    options_en: ["<p><span style=\"font-family: Cambria Math;\">&nbsp;x<span style=\"font-weight: 400;\">&sup2;</span>+ 11 </span></p>\n", "<p><span style=\"font-family: Cambria Math;\">x<span style=\"font-weight: 400;\">&sup2;&nbsp;</span> - 121 </span></p>\n", 
                                "<p><span style=\"font-family: Cambria Math;\">x<span style=\"font-weight: 400;\">&sup2;&nbsp;</span> + 121 </span></p>\n", "<p><span style=\"font-family: Cambria Math;\">&nbsp;x<span style=\"font-weight: 400;\">&sup2; </span>- 11</span></p>\n"],
                    options_hi: ["<p><span style=\"font-family: Cambria Math;\">&nbsp;x<span style=\"font-weight: 400;\">&sup2; </span>+ 11 </span></p>\n", "<p><span style=\"font-family: Cambria Math;\">&nbsp;x<span style=\"font-weight: 400;\">&sup2; </span>- 121 </span></p>\n",
                                "<p><span style=\"font-family: Cambria Math;\">&nbsp;x<span style=\"font-weight: 400;\">&sup2; </span>+ 121 </span></p>\n", "<p><span style=\"font-family: Cambria Math;\">&nbsp;x<span style=\"font-weight: 400;\">&sup2; </span>- 11</span></p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">15.(</span><span style=\"font-family: Cambria Math;\">b)</span></p>\r\n<p>(<span style=\"font-family: Cambria Math;\"> x + 11) </span>(<span style=\"font-family: Cambria Math;\"> x- 11) </span><span style=\"font-family: Cambria Math;\"> [</span><span style=\"font-family: Cambria Math;\">(a+b)(a- </span><span style=\"font-family: Cambria Math;\">b) = (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>a</mi><mn>2</mn></msup><mo>-</mo><msup><mi>b</mi><mn>2</mn></msup></math></span><span style=\"font-family: Cambria Math;\">)]</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">&nbsp;&rArr; </span><strong>(&nbsp;x<span style=\"font-weight: 400;\">&sup2;</span></strong><span style=\"font-weight: 400;\">-121</span><strong>)</strong></span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">15.(</span><span style=\"font-family: Cambria Math;\">b)</span></p>\r\n<p>(<span style=\"font-family: Cambria Math;\"> x + 11)</span>(<span style=\"font-family: Cambria Math;\"> x - 11) </span><span style=\"font-family: Cambria Math;\"> [</span><span style=\"font-family: Cambria Math;\">(a+b)(a-</span><span style=\"font-family: Cambria Math;\">b) = (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>a</mi><mn>2</mn></msup><mo>-</mo><msup><mi>b</mi><mn>2</mn></msup></math> </span><span style=\"font-family: Cambria Math;\">)]</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">&rArr; </span><strong>(&nbsp;x<span style=\"font-weight: 400;\">&sup2;</span></strong><span style=\"font-weight: 400;\">-121</span><strong>)</strong></span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>