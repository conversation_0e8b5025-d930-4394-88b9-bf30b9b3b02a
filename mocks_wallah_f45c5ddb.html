<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">30:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 26</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">26</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 30 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["6"] = {
                name: "Reasoning",
                start: 0,
                end: 24
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="6">Reasoning</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 25,
                end: 25
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "6",
                    question_en: "<p>1. Study the given pattern carefully and select the number that can replace the question mark (?) in it,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729590790254.png\" alt=\"rId5\" width=\"271\" height=\"68\"></p>",
                    question_hi: "<p>1. दिए गए पैटर्न का ध्यानपूर्वक अध्ययन करें और उस संख्या का चयन करें जो उसमें प्रश्नवाचक चिह्न (?) को प्रतिस्थापित कर सके<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729590790254.png\" alt=\"rId5\" width=\"271\" height=\"68\"></p>",
                    options_en: ["<p>3</p>", "<p>10</p>", 
                                "<p>16</p>", "<p>12</p>"],
                    options_hi: ["<p>3</p>", "<p>10</p>",
                                "<p>16</p>", "<p>12</p>"],
                    solution_en: "<p>1.(c)<br>4<math display=\"inline\"><mo>&#215;</mo><mn>5</mn><mo>=</mo><mn>8</mn><mo>+</mo><mn>12</mn></math><br>5<math display=\"inline\"><mo>&#215;</mo><mn>3</mn><mo>=</mo><mn>7</mn><mo>+</mo><mn>8</mn></math><br>6<math display=\"inline\"><mo>&#215;</mo><mn>4</mn><mo>=</mo><mn>8</mn><mo>+</mo><mn>16</mn></math></p>",
                    solution_hi: "<p>1.(c)<br>4<math display=\"inline\"><mo>&#215;</mo><mn>5</mn><mo>=</mo><mn>8</mn><mo>+</mo><mn>12</mn></math><br>5<math display=\"inline\"><mo>&#215;</mo><mn>3</mn><mo>=</mo><mn>7</mn><mo>+</mo><mn>8</mn></math><br>6<math display=\"inline\"><mo>&#215;</mo><mn>4</mn><mo>=</mo><mn>8</mn><mo>+</mo><mn>16</mn></math></p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "2",
                    section: "6",
                    question_en: "2. Four words have been given, out of which three are alike in some manner and one is different. Select the odd one.",
                    question_hi: "2. चार शब्द दिए गए हैं, जिनमें से तीन किसी तरह से एक जैसे हैं और एक अलग है। विजातीय का चयन कीजिये ।",
                    options_en: [" Pyramid ", " Cylinder ", 
                                " Cuboid ", " Circle "],
                    options_hi: [" पिरामिड", " बेलन ",
                                " घनाभ", " वृत्त"],
                    solution_en: "2.(d)<br />All are 3 d figures except the circle.",
                    solution_hi: "2.(d)<br />वृत्त को छोड़कर सभी 3 d अंक हैं।",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "3",
                    section: "6",
                    question_en: "<p>3. In a certain code language, &lsquo;MUFASA&rsquo; is written as &lsquo;UMAFAS&rsquo;. How will &lsquo;SONNET&rsquo; be written as in that code language?</p>",
                    question_hi: "<p>3. एक निश्चित कूट भाषा में, \'MUFASA\' को \'UMAFAS\' लिखा जाता है। उसी कोड भाषा में \'SONNET\' को किस प्रकार लिखा जाएगा?</p>",
                    options_en: ["<p>NNOSTE</p>", "<p>TENNOS</p>", 
                                "<p>OSENTE</p>", "<p>OSNNTE</p>"],
                    options_hi: ["<p>NNOSTE</p>", "<p>TENNOS</p>",
                                "<p>OSENTE</p>", "<p>OSNNTE</p>"],
                    solution_en: "<p>3.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729590790375.png\" alt=\"rId6\" width=\"282\" height=\"54\"></p>",
                    solution_hi: "<p>3.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729590790375.png\" alt=\"rId6\" width=\"282\" height=\"54\"></p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "4",
                    section: "6",
                    question_en: "<p>4. Six family members, A, B, C, D, E, F are working as a team on a family project of renovating a house. Each person has been given a single task. A is B&rsquo;s son and is in charge of flooring. B is C&rsquo;s sister and is in charge of water issues. C is D&rsquo;s wife and is in charge of electric works. D is in charge of foundation related work and his son E is in charge of interior decoration. F, who is D&rsquo;s brother, is in charge of roof. What work is assigned to A&lsquo;s cousin?</p>",
                    question_hi: "<p>4. परिवार के छह सदस्य, A, B, C, D, E, F घर के नवीनीकरण की पारिवारिक परियोजना पर एक टीम के रूप में काम कर रहे हैं। प्रत्येक व्यक्ति को एक ही कार्य दिया गया है। A, B का पुत्र है और फर्श का प्रभारी है। B, C की बहन है और पानी के मुद्दों का प्रभारी है। C, D की पत्नी है और विद्युत कार्यों का प्रभारी है। D नींव संबंधी कार्य का प्रभारी है और उसका पुत्र E आंतरिक सज्जा का प्रभारी है। F, जो D का भाई है, छत का प्रभारी है। A के चचेरे भाई को क्या कार्य सौंपा गया है?</p>",
                    options_en: ["<p>Interior decoration</p>", "<p>Roof</p>", 
                                "<p>Electrical work</p>", "<p>Foundation related work</p>"],
                    options_hi: ["<p>आंतरिक सजावट</p>", "<p>छत</p>",
                                "<p>विद्युत कार्य</p>", "<p>नीवं से संबंधित कार्य</p>"],
                    solution_en: "<p>4.(a)<br><img src=\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAALIAAADECAYAAAA78Xu0AAAVUElEQVR4nO2dTWgbx9/Hvw4tVGqTkEiQgEAoPlR7aoLxwWFtCjpUusaBhCCrVwUbWlyiXIJ1sOhJpsKUpFZuqbSFBOKQm52DoVhLfDDGOZRKDdjCIHDKKvm7L1JP3efgZ6a7q1dbK0ua/D4gcHZXO+PVV6PfrKPPDOm6roMgBox///0Xb968gdvtxocffoghCjIxqFQqFfzxxx84c+YMPgCAoaGhXveJII7FP//8g7///vtwRB4aGkI/D8z93j+iNxhzcerPP//scXcIonNOVavVXveBIDrm1P/+979e94EgOubUJ5980us+EETHnOp1BwjCDijIhBBQkAkhoCATQkBBJoTA1iAXCgVcuXIFoVAI5XLZzlP3PaqqYmhoqO6DXQ9FUXD79m106959uVxGKBSCqqpdOX8/Y2uQNzc3ceHCBezv72NlZcXOUw8EwWAQmqZB13XTY2VlBS6Xq+vtu1wurKysQJblrrfVb9gW5Gq1ivX1dXz55Ze4fv061tfXuzbyEIQV24K8tbWFjY0NjI6OIhAIYGNjA3t7e3adXkiq1Spu375dU4IYSSQSfP+VK1dQKBQAHJYRt27dwsOHD/lzX79+bSotEokEf7BzWEsb6767d+8ikUic3EWwCduCvLa2hrGxMXi9XoyMjGBsbAxPnjyx6/TCUS6Xce3aNXg8Hl6CyLKMcDiMcrnMQ14qlVCpVKDrOmKxGG7evMnD/O7dO2xtbaFSqWBlZQXnz5+vaScejyMQCEDXdWiahmKxiOXlZQCAoihQVZWXQxMTE0gmkyd6HezCliAXCgU8ffoUkUgEDocDDocDExMTUFX1vZr0ra6uwu12myZ6xlHUSD6fBwBMT0/zbeznfD6Pvb097O7uYnZ2Fg6HAwAwOTmJsbExbG5u8udMTEzw/fWIRqMYGRkBcFhDy7KMnZ0dlMtlZDIZzM3N8fp9cnIS0Wi0w6vQG2wJ8ubmJi5evAhJkvi20dFR7O/v8xfsfaDeZG97ext+v7/m2GKxCJ/PB6fTybexoBWLRWiahqGhIbjdbr6fDRA7Ozt8m8/na9onj8dTN+isn9bzezyeI/3O/cIHnZ6ATfLYaGQlk8lgZGSk6ahBHFKtVlEqlTA8PNzwGGOIif/oeERmk7x8Pl9z2ymXy9GkrwE+nw/FYhGVSoVvq1QqfKR2u928rmW0E/R2YSVQvfMPIh0H2TjJsyJJEi5evGiq6YhDWBn24MEDvo39LEkSvF4vLl26hFQqxe8yLC8v8ztDneJyuRCJRJBIJPg8Znl5Gel0uuNz94KOgmyd5FlhFyuTybxXk752cLlcePbsGUqlEp8YqqoKRVHgcrngcDiwtLQEj8cDp9OJoaEhJJNJPH78uG7NfRzC4TBkWeaj8/r6OmKxmC3nPmmGSqWSzm4B9Sv05dOTI5FIYHh4GOFwuNddaYnpy6c97gvRI9j/y1AUhW9TVRVPnz61pXQ5aWhEfo8pFAq4efMmXr16BQC4fPmyraVLtzHmgoJMDCxUWhDCQUEmhIAELYQQnDpz5kyv+0AQHXOK/g8EIQJk4yQGFmMuPjBu7Gf6vX9Eb+FB7ucRj0Zkoh7GwY1uvxFCQEEmhICCTAgBBZkQAgoyIQQUZEIIKMiEEFCQCSGgIBNC0HGQrSI+42OQPL2JRKKuu1hRlLraK1VV2/ZAb29v22om7bZneRCxbUTOZrM1cpbx8fGBCXMgEKgRplSrVbx69QoXLlyocXOsra1BluWW3mNFUbC0tGRrX8PhMJaWlsjeZKBrpcXIyAii0SiKxWK3mrCVemYfZkiy+p7tNP4Q9kA18v/DzD7GkXdzcxOnT5/G559/joODAz5aM1Pm6Oho3dLKuNTC1NQU0uk0rl69yssTtkQFO97oI1YUBXfv3uXnNH5d33iMsbRQFMXUvvUr/sZ91rasJYp1W7O+tmr7JOlakLe2tgAcqkoHAWaiZJJAJmcMBALwer04e/YsN4saTZkLCwsmxzEb0R88eIBwOIxsNotoNIqXL1/C7/dDVVXcvHkTjx8/hq7rqFQqKJVKpoAkk0lEIhHout5SlKKqKjKZDLdr5vN5/PjjjygUCigUCpifn+dePk3ToKpq2+Veq742a/uksS3IU1NTpnfm+Ph4Tc3Z7wQCAZRKJVSrVd5vSZJ4yNfW1gCY6+O5uTnMzc3xczA1bCPW1tYQi8W4O8LhcGB2dha5XI4HIBgMmhS9zbCWbn6/H6urq/D7/dA0DW/evDH17ShrjLTqa7O2T5quTfbYyHTv3r2BmV1LkoSDgwPs7e0hn8/D4/HwyRwLeblcRqlUQiAQMD3X+BEbj8frnp/V1tY3vSRJpsBZvcnNCIVCAP6zaxo/2tnKAZIk1S0LmtFOX5u1fdJ0rbRgo9UgjcpOpxNnz56FpmlYW1szhZWFPJ/P4+DggLug2Rocxo/Y+fn5uudn2ljrm76ZELwVbJTVdR3ZbJYHT1EULkJkd5Hi8Xjbga5Wqy372qztk4YmewaYEf758+cAYPp4ZyFfW1vD2bNn4fV6US6XoaoqcrkcX4KsmWPY6XTC5/N1TdYdDod5qKyrasmyzAPdbEkM1jeHw3GkvjZr+yToWpCr1SoymQwikciJrDFnFz6fjy8IY+w3C3k8Hq9ZzsBYKy4sLJgcw0aht8PhQCQSQTweN024FEU59iKb1ueySerExAS2trZq/phjrO99Ph/S6TSfmDNNMPt9W/W1Wdsnfo9bP/wynH5cKpWKHo1GdQA1j2w2e+zzGumkf0dF0zQ9GAzW7Xs+n9cvX76s53K5mm3sd56fn9ez2aweDAZ1TdP4+QDw5+VyOdN1Ysfquq5ns1k9Go3qlUqlYR+tx8zPz5vONz8/bzrWuM96buP+aDSqp9Np0zHN+tqq7W5jzAXpAIiBhSSGhHBQkAkhoCATQkBBJoSAgkwIAQWZEAIKMiEEFGRCCCjIhBCQH5kQAvIjEwML+ZEJ4aAgE0JAQSaEgIJMCAEFmRACCjIhBBRkQggoyIQQUJAJIbA1yExWwh6D5vC1Cv+sDyYeSSQSR7L2NKNarWJ7e7vh/nK5jFAo1Jd6XjuvQ6fYEmRmbARgMtJ4PB5cu3btWL6GXhEMBrkxyPpoJRQ8KtVqFbOzs/jll18aHnNUX9v7ii3G+lQqhevXr5tkfgBw584d+Hw+PHjwoNNmCKIpHQeZuYJv3LhRs4+5x6wBF41WDmHjfmb+YaNxOp3G1NQUbt++jVKphFu3buHhw4fcs/z69eua0sJYwjX6aK+3NIS1FCgUCggGg9xEZC2tjA63RCKBVCqFUCjUcFkN1q+BdL8ZXcHvI60cwuVyGV9//TXu378PXdcRi8WQSqUAAKlUCtFoFNlsFktLS/joo4/w7t07bG1toVKpYGVlBefPnze1pygKSqUSKpVKU98x89YxpzPz1DFtLnAoMr906RK8Xi8URcHMzAx3KefzeSSTSVMoHz16hMXFRei6XlPqJBIJqKoKTdNsL8HawZYa+Sga1H5ndXWVa1LrWeittHII5/N5+Hw+jIyMAGhv/Y9G7jTmVotEInA4HE3rZ+ZpZk7nfD6Pzz77DGfOnMHe3p7J0wYA6+vrpt/D7/cjFouZhIRjY2Pwer01bbEQK4rSM8+fLUEeJHVsKxpN9pht00g7DuHjrKHi8/nqbmda2nYxisuLxSIuX76M06dPY3NzE5VKBQcHBxgdHeXntbY7OjpqWnLCKm8EgHg8jng83vPBrOMg11tExoiqqgN3G65duuE7thO3243d3V0UCgW8evUKo6OjCAQC2NnZQT6f53rcRmiahnfv3jVtg9XYxWIRy8vLdv8KbdNxkP1+P8bHx/HkyZOafUwtW++dLALt+I4bja6dtNcubIGfn376if9bkiSUSiXT68LOax3t2SjdbKSVZRmffvopIpEIkslkT9YPAWwqLW7cuIGnT5/WzKAXFhZQLBYxPT1tRzN9RzsOYUmSUCwWuYPYeDfBugBPO+1NTEwgk8mgWq3yFaUa3SVgxyeTSZw+fZqHFgDS6TQ38huPM648lUwm23YdT05OYmxsDKlUqiefvrYE2e/34+XLlyiVSqZasVQq4dmzZwMl+m402Wt0q0uWZb44Jjsuk8nwiY/L5cLi4iJmZmYwNDSEmZkZLC4umtYmicfjCIVCePv2bcv+hcNheDweOJ1OOJ1OeDyepncJRkdHcfny5ZrQWhfcCYfDuH//Pl9vRJIkxGKxtu9AsEnuxsYGFhYW2nqOnZAfmRhYyI9MCAcFmRACCjIhBBRkQggoyIQQUJAJIaAgE0JAQSaEgIJMCAH5kQkhID8yMbCQH5kQDgoyIQQUZEIIKMiEEFCQCSGgIBNCQEEmhICCTAgBBZkQgo6DzPy99b513I9O36OiKEpDX3IvxTNWAWErjB7mfnYuHxfbRuRcLmcy7eTzeczMzPTEzGg30WgUlUqlxibUyuHWL1g9zCI6l7tWWvj9fty/f7+n9hni/aGrNfLIyAjGxsawubnZzWb6gmZuYUVRasoQ4zb2M/Mi17N/Wh3MP//8s6l9634mlGnkYbaWFu30v1n/ek1Xg3xUJdSg0o5buBXpdBoff/wxdywbTf/MsRyLxbgwcnl5mRs/rQ5mXdeRy+UQj8extbVV18N81P43618/QHct2iCdTsPpdNb1JTPPcCu3cCuCwSBCoRCA/7RWTAm7srICn8+HyclJAIc1rnEVgHo1ryRJCAaDLdttt//N+tcPUJDboN5kj/mS23ULt6KZ9XJnZ6fGaOp2u3HhwgXTcUxqyFYQWF1dbdluu/3vtf+4FV0NMhNhM4He+0Q7bmG7YLfTmNSQlR/tjMiNOMn+20FXg7y1tYVisWiyPorGcd3CR5k3DA8P13yMa5rGa2S2Toimabzk0DQN+/v7Xet/v9G1IBcKBczMzCASiQyUVvaotOMW9vl8SKfT3JFcKBTw9OnTttsIhUImI3y5XK5R3O7v7/NVA9jk79WrV7yPjSbddriR+4EPWh/SHuPj4zXbcrmcUDfdGxEOh+Hz+UyfPNlslruFZVlGNpvl1ygajWJ6epoHuxUulwuKoiAcDmNqagrA4QSUvRlkWUYsFjO1/+LFC/h8PqytrUGWZQQCAYyPj0NVVXz//fdH6v8gQH5kYmAhPzIhHBRkQggoyIQQUJAJIaAgE0JAQSaEgIJMCAEFmRAC0soSQkBaWWJgIa0sIRwUZEIIKMiEEFCQCSGgIBNCQEEmhICCTAgBBZkQAgoyIQQUZEIIOg6y0W5T73FSsrtEIlHzFflWbG9vd6R8Ok6bR6WeALHf+O233/hr3Kv+2jYiZ7PZGn+wUS3VbyiKgqWlpY7OMTc3Z3KwvY+oqoqvvvqK/zscDvfEG02lBSEEQgaZudCeP39uWhaCaVIVRcHU1BTS6TSuXr3KDTvNHMGJRAKpVIqfT1XVmtLC6ii27rt16xZSqVTTZRuMSz2EQiH8/vvvpv3WUs5aulmXwrAur9DKg3z37l1+fkVR6paOrE1VVTE+Po7V1VW43W6oqlpTWjTrb6vX6SgIGWTGDz/8AEVRuC+YaaHC4TCy2Syi0ShevnwJv98PRVGQSCSgaRqXAGYyGdNFffToERYXF6Hreo1BSVVVSJLEHcWapvGwM3799Vf89ddfDZdtUFUVyWSSe4rn5ubwzTff8P3lchnXrl3DxMQEL90ikQjC4TBX3N67dw+RSIT/zolEwlS/tvIgJ5NJ/vxwOIyFhQUuRmS/FwA8ePAAsiwjl8shGAxC07Saa8L6a3y+LMu8v61ep6NgW5CnpqbqTvZ6uYaI0TsnSRIuXrzIXwgjzBE8NzfHj2cO4kwmwy/62NgYvF5v3bbW1tYwPz/PX0z2fFVVTS9aIzNptVpFJpMxeYplWcb8/Dw/Jp/PmzzJADA5OQmfz4eVlRXs7e3h4OCAe4xlWeZzlKN4kI3qLOs8wOVyta1BY3LF6elpvo39zPYB7b9Ozej6ZK+X/jCr87cRzBE8Pj5uehNafXZWRzGD6XOHh4dN2yVJwrlz5/iLcuHCBbjd7qZ9sPbZeM5isVgjHXc6nUin0wCaq2A79SAbS554PF63DSv1jJ7sjWC0f7b7OjVD6NKiXZiC1boyVad3XTRNw9u3b23r587OTsMVpo47YLTyICcSCQwNDSGTyfCyy/gpcVTYm95uKMg4tL9fvHixxhHcLo20rZqmcXt8Kxp5io3nHB4eRrFYbGjBd7vdOHfu3JHO38yDzCZ0uVzOVKK0G0TWnrG/jT4ZOuW9DbLxIrtcLkQikZpJRiKRaPvmfiAQQDwe53cJmMNYluW2RnSHw1HTB1VVTR/jrPY1LkLD7pSoqgqv14uzZ89iZWWF72OLSnbiQTaGf2FhgZcywOGbxzgJNMJqbWN/2c92y9+7Ptm7cuVKX66zxy4ku20UDoe5Y5j1vVQqIZVKtXVzX5ZlvkgmG4VlWT7SH0xkWcb9+/d5HxKJBL777ju+n3mSjbfQ2J0SWZbhcDjw7bffIpPJ8H3xeJxP7sLhsOn8kiQhFos1LEtcLhcWFxeRTCZ5e8DhfIhNYr1eLy5dugRJkmom9i6XC8+ePUOpVDLdDlQUxfY/kpEfmRhYyI9MCAcFmRACCjIhBBRkQggoyIQQUJAJIaAgE0JAQSaEgPzIhBCQH5kYWMiPTAgHBZkQAgoyIQQUZEIIKMiEEFCQCSGgIBNCQEEmhICCTAgBBZkQAluCbBXj9cKPPEg0u17dcAszS1C/fqPdDmwbkZnIblD8yL2m3vViIpPZ2Vnbwlwul5HJZJDL5bC9vc3VAKJBpUUf4XA4MDs7i93dXezt7dlyTvZmacd2NMhQkPsMTdPw5s0b0zbmX2tUHjRyHjPV7YsXLyBJUteXieglFOQ+gqllr1+/Dr/fzyXZpVKJiwtjsRhu3rzJw9zMeczsR1988QXy+bzQy0TYFmRmLR8EXVY/UO96OZ1OeDweHri9vT3s7u5idnaWa7smJycxNjaGzc3Ntp3H7wNdneyJPLnoFOP1qlQqiEajiEajuHPnDj+mns2TyQh3dnbadh6/D1Bp0Qcw+WCxWGzrjoVVX2ullfNYRCjIfQJbqiGdTmN5eRlAfWWr0Y5/HOexqFCQ+wi2Zghbt4QpW1OpFB+ll5eXsbGxgdHR0Y6cx6JBQe4zbty4gf39fdy7dw8AsLS0BI/Hw9cNSSaTePz48bGdx6JCfmRiYCE/MiEcFGRCCCjIhBBQkAkhoCATQkBBJoSAgkwIAQWZEALyIxNC8AHQ325kgmgHKi0IIfg/YW5kSiKoxYsAAAAASUVORK5CYII=\"></p>\n<p><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729590790663.png\" alt=\"rId8\" width=\"230\" height=\"84\"></p>",
                    solution_hi: "<p>4.(a)<br><strong id=\"docs-internal-guid-b79de4d7-7fff-642e-c934-9dfe863bd6ef\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXeeXISaHxjXceVgwi3Pzwzc5E7HMpIMcaiSRX3BjTSfmLyOzgRd-YVvW2TcypR-GlJJkDPTdMF3dRKlN8lCWCaTxekzGjKBA-sv9oonkX-SsOBEoYb8cF1FRvPBbR8-rGEOeBHF94xLQj-xG8t1B_2x3YEE?key=DBQU7BGjxSUULhVjAKIs-w\" width=\"187\" height=\"201\"></strong></p>\n<p><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729590790663.png\" alt=\"rId8\" width=\"230\" height=\"84\"></p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "5",
                    section: "6",
                    question_en: "<p>5. Four words have been given, out of which three are alike in some manner and one is different. Select the odd one.</p>",
                    question_hi: "<p>5. चार शब्द दिए गए हैं, जिनमें से तीन किसी तरह से एक जैसे हैं और एक अलग है। विजातीय का चयन कीजिये ।</p>",
                    options_en: ["<p>Pedestal</p>", "<p>Road</p>", 
                                "<p>Highway</p>", "<p>Street</p>"],
                    options_hi: ["<p>आसन</p>", "<p>सड़क</p>",
                                "<p>राजमार्ग</p>", "<p>सड़क</p>"],
                    solution_en: "<p>5.(a)<br>Except pedestals, all are ways at which people can walk.</p>",
                    solution_hi: "<p>5.(a)<br>आसन को छोड़कर, सभी ऐसे तरीके हैं जिन पर लोग चल सकते हैं।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "6",
                    section: "6",
                    question_en: "6. Study the given pattern carefully and select the numbers from the given option that can replace the question mark (?)<br /> <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729590791079.png\" alt=\"rId10\" /> ",
                    question_hi: "6. दिए गए पैटर्न का ध्यान से अध्ययन करें और दिए गए विकल्प से संख्याओं का चयन करें जो प्रश्न चिह्न को बदल सकते हैं <br /> <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729590791079.png\" alt=\"rId10\" /> ",
                    options_en: [" 7 ", " 25", 
                                " 12", " 13"],
                    options_hi: [" 7 ", " 25",
                                " 12", " 13"],
                    solution_en: "6.(d)<br /><math display=\"block\"><msup><mrow><mn>3</mn></mrow><mrow><mn>2</mn></mrow></msup><mo>+</mo><msup><mrow><mn>2</mn></mrow><mrow><mn>2</mn></mrow></msup><mo>=</mo><mn>7</mn><mo>+</mo><mn>6</mn></math><br /><math display=\"block\"><msup><mrow><mn>2</mn></mrow><mrow><mn>2</mn></mrow></msup><mo>+</mo><msup><mrow><mn>6</mn></mrow><mrow><mn>2</mn></mrow></msup><mo>=</mo><mn>25</mn><mo>+</mo><mn>15</mn></math><br /><math display=\"block\"><msup><mrow><mn>2</mn></mrow><mrow><mn>2</mn></mrow></msup><mo>+</mo><msup><mrow><mn>5</mn></mrow><mrow><mn>2</mn></mrow></msup><mo>=</mo><mn>20</mn><mo>+</mo><mn>9</mn></math><br /><math display=\"block\"><msup><mrow><mn>3</mn></mrow><mrow><mn>2</mn></mrow></msup><mo>+</mo><msup><mrow><mn>4</mn></mrow><mrow><mn>2</mn></mrow></msup><mo>=</mo><mn>12</mn><mo>+</mo><mn>13</mn></math>",
                    solution_hi: "6.(d)<br /><math display=\"block\"><msup><mrow><mn>3</mn></mrow><mrow><mn>2</mn></mrow></msup><mo>+</mo><msup><mrow><mn>2</mn></mrow><mrow><mn>2</mn></mrow></msup><mo>=</mo><mn>7</mn><mo>+</mo><mn>6</mn></math><br /><math display=\"block\"><msup><mrow><mn>2</mn></mrow><mrow><mn>2</mn></mrow></msup><mo>+</mo><msup><mrow><mn>6</mn></mrow><mrow><mn>2</mn></mrow></msup><mo>=</mo><mn>25</mn><mo>+</mo><mn>15</mn></math><br /><math display=\"block\"><msup><mrow><mn>2</mn></mrow><mrow><mn>2</mn></mrow></msup><mo>+</mo><msup><mrow><mn>5</mn></mrow><mrow><mn>2</mn></mrow></msup><mo>=</mo><mn>20</mn><mo>+</mo><mn>9</mn></math><br /><math display=\"block\"><msup><mrow><mn>3</mn></mrow><mrow><mn>2</mn></mrow></msup><mo>+</mo><msup><mrow><mn>4</mn></mrow><mrow><mn>2</mn></mrow></msup><mo>=</mo><mn>12</mn><mo>+</mo><mn>13</mn></math>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "7",
                    section: "6",
                    question_en: "<p>7. A, B and C are well known authors in Hindi, English and Punjabi not necessarily in the same order. Read the given statement and decide which of the following conclusions logically follows from the statements. <br><strong>Statements:</strong> <br>1. C does not write in English. <br>2. The book of essays is in Punjabi <br>3. The book of poems in English has won an award.<br>4. A is a novelist. <br>5. B is not an essayist.</p>",
                    question_hi: "<p>7. A, B और C हिंदी, अंग्रेजी और पंजाबी में जाने-माने लेखक हैं, जरूरी नहीं कि इसी क्रम में हों। दिए गए कथन को पढ़ें और बताइये कि निम्नलिखित में से कौन सा निष्कर्ष कथनों का तार्किक रूप से अनुसरण करता है<br><strong>कथन:</strong> <br>1. C अंग्रेजी में नहीं लिखता<br>2. निबंध की किताब पंजाबी में है<br>3. अंग्रेजी में कविताओं की पुस्तक ने एक पुरस्कार जीता है <br>4. A उपन्यासकार हैं।<br>5. B एक निबंधकार नहीं है</p>",
                    options_en: ["<p>C writes poems in English.</p>", "<p>B writes poems in Hindi.</p>", 
                                "<p>B writes poems in English.</p>", "<p>A writes essays in Punjabi.</p>"],
                    options_hi: ["<p>C अंग्रेजी में कविता लिखता है</p>", "<p>B हिन्दी में कविताएँ लिखता है</p>",
                                "<p>B हिन्दी में कविताएँ लिखता है</p>", "<p>A पंजाबी में निबंध लिखता है</p>"],
                    solution_en: "<p>7.(c)<br><img src=\"data:image/png;base64,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\" width=\"247\" height=\"124\"></p>",
                    solution_hi: "<p>7.(c)<br><img src=\"data:image/png;base64,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\" width=\"245\" height=\"126\"></p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "8",
                    section: "6",
                    question_en: "<p>8. Study the given pattern carefully and select the option that can replace the question mark (?) in it.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729590791462.png\" alt=\"rId13\" width=\"177\" height=\"198\"></p>",
                    question_hi: "<p>8. दिए गए पैटर्न का ध्यानपूर्वक अध्ययन कीजिये और उस विकल्प का चयन करें जो उसमें प्रश्नवाचक चिह्न (?) को प्रतिस्थापित कर सके।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729590791462.png\" alt=\"rId13\" width=\"177\" height=\"198\"></p>",
                    options_en: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729590791574.png\" alt=\"rId14\" width=\"224\" height=\"44\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729590791677.png\" alt=\"rId15\" width=\"124\" height=\"49\"></p>", 
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729590791790.png\" alt=\"rId16\" width=\"149\" height=\"44\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729590791889.png\" alt=\"rId17\" width=\"172\" height=\"41\"></p>"],
                    options_hi: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729590791574.png\" alt=\"rId14\" width=\"224\" height=\"44\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729590791677.png\" alt=\"rId15\" width=\"124\" height=\"49\"></p>",
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729590791790.png\" alt=\"rId16\" width=\"149\" height=\"44\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729590791889.png\" alt=\"rId17\" width=\"172\" height=\"41\"></p>"],
                    solution_en: "<p>8.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729590791574.png\" alt=\"rId14\" width=\"275\" height=\"54\"></p>",
                    solution_hi: "<p>8.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729590791574.png\" alt=\"rId14\" width=\"275\" height=\"54\"></p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "9",
                    section: "6",
                    question_en: "<p>9. A and B are brothers, and their maternal uncles are C and D. E is D&rsquo;s wife and the sister-in-law of F. F is the only daughter of G. G is B&rsquo;s maternal grandmother. How is F related to A.</p>",
                    question_hi: "<p>9. A और B भाई हैं, और उनके मामा C और D हैं। E, D की पत्नी है और F की साली है। F, G की इकलौती बेटी है। G, B की नानी है। F, A से किस प्रकार संबंधित है?</p>",
                    options_en: ["<p>Maternal Grandmother</p>", "<p>Mother</p>", 
                                "<p>Maternal Aunt</p>", "<p>Sister</p>"],
                    options_hi: ["<p>नानी</p>", "<p>मां</p>",
                                "<p>मामी</p>", "<p>बहन</p>"],
                    solution_en: "<p>9.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729590791995.png\" alt=\"rId18\" width=\"227\" height=\"155\"></p>",
                    solution_hi: "<p>9.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729590791995.png\" alt=\"rId18\" width=\"227\" height=\"155\"></p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "10",
                    section: "6",
                    question_en: "10. In a certain language, ‘WET’ is coded as ‘VXDFSU’. Which of the given options will be coded as ‘MOHJAC’ in that language?",
                    question_hi: "10. एक निश्चित भाषा में, \'WET\' को \'VXDFSU\' के रूप में कोडित किया जाता है। दिए गए विकल्पों में से किस भाषा में \'MOHJAC\' के रूप में कूटबद्ध किया जाएगा?",
                    options_en: [" TIP", " VEX", 
                                " JET", " NIB"],
                    options_hi: [" TIP", " VEX",
                                " JET", " NIB"],
                    solution_en: "10.(d)<br />W - 1 = V and W + 1 = X<br />E - 1 = D and E + 1 = F<br />T -1 = S and T+ 1 = U<br />Similarly the code  MOHJAC  is of NIB",
                    solution_hi: "10.(d)<br />W -1 = V और W + 1 = X<br />E -1 = D  और E + 1 = F<br />T -1 = S  और T+ 1 = U<br />इसी तरह कोड MOHJAC NIB का है।",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "11",
                    section: "6",
                    question_en: "<p>11. Consider the given statements and the following assumptions and decide which of the assumption(s) is/are implicit in the statement. <br><strong>Statement:</strong> <br>Opening of a few more hospitals is crucial in the village X <br><strong>Assumptions:</strong> <br>I. The number of hospitals in the village X is not enough to meet the needs of its inhabitants. <br>II. Having more hospitals will aid in the increasing needs of healthcare institutes in the village X.</p>",
                    question_hi: "<p>11. दिए गए कथनों और निम्नलिखित धारणाओं पर विचार कीजिये और बताइये कि कौन सी धारणा कथन में निहित हैं।<br><strong>कथन:</strong> <br>गांव X में कुछ और अस्पतालों का खुलना महत्वपूर्ण है<br><strong>धारणा:</strong> <br>I. गांव X में अस्पतालों की संख्या इसके निवासियों की जरूरतों को पूरा करने के लिए पर्याप्त नहीं है।<br>II. अधिक अस्पताल होने से गांव X में स्वास्थ्य संस्थानों की बढ़ती जरूरतों को पूरा करने में मदद मिलेगी</p>",
                    options_en: ["<p>Only assumption Ⅱ is implicit.</p>", "<p>Neither assumption Ⅰ nor Ⅱ is implicit.</p>", 
                                "<p>Only assumption Ⅰ is implicit.</p>", "<p>Both assumption Ⅰ and Ⅱ are implicit.</p>"],
                    options_hi: ["<p>केवल धारणा Ⅱ निहित है।</p>", "<p>न तो धारणा और न ही निहित है।</p>",
                                "<p>केवल धारणा Ⅰ निहित है।</p>", "<p>दोनों धारणा Ⅰ और निहित हैं।</p>"],
                    solution_en: "<p>11.(c)<br>From the given statements it can be observed that the number of hospitals in village X is not enough to meet the needs of its inhabitants.</p>",
                    solution_hi: "<p>11.(c)<br>दिए गए कथनों से यह देखा जा सकता है कि गाँव X में अस्पतालों की संख्या इसके निवासियों की जरूरतों को पूरा करने के लिए पर्याप्त नहीं है।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "12",
                    section: "6",
                    question_en: "12. Among the following words, which one will come last if they are arranged as per their order in an English dictionary?",
                    question_hi: "12. निम्नलिखित शब्दों में से कौन सा शब्द अंग्रेजी शब्दकोश में उनके क्रम के अनुसार व्यवस्थित होने पर अंतिम आएगा?",
                    options_en: [" Abode", " Aback", 
                                " Abide", " Aboriginal"],
                    options_hi: [" Abode", " Aback",
                                " Abide", " Aboriginal"],
                    solution_en: "12.(d) according to english dictionary last word is Aboriginal",
                    solution_hi: "12.(d)अंग्रेजी शब्दकोश के अनुसार अंतिम शब्द Aboriginal है।",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "13",
                    section: "6",
                    question_en: "<p>13. Read the given statements and conclusions carefully. Assuming that the information given in the statements is true, even if it appears to be at variance with commonly known facts, decide which of the given conclusions logically follow(s) from the statement.<br><strong>Statements:</strong><br>A. All plastics are fruits. <br>B. All fruits are books. <br>C. No books are trousers. <br><strong>Conclusions:</strong><br>I.Some books are plastics <br>II. No trouser is a plastic. <br>III.Some fruits are plastics. <br>IV. Some trousers are fruits.</p>",
                    question_hi: "<p>13. दिए गए कथनों और निष्कर्षों को ध्यानपूर्वक पढ़िए। यह मानते हुए कि कथनों में दी गई जानकारी सत्य है, भले ही वह सामान्य रूप से ज्ञात तथ्यों से भिन्न प्रतीत होती हो, बताइये कि दिए गए निष्कर्षों में से कौन सा कथन का तार्किक रूप से अनुसरण करता है।<br><strong>कथन:</strong><br>A. सभी प्लास्टिक फल हैं।<br>B. सभी फल किताबें हैं<br>C. कोई किताब ट्राउजर नहीं है<br><strong>निष्कर्ष:</strong><br>I. कुछ किताबें प्लास्टिक हैं<br>II. कोई ट्राउजर प्लास्टिक नहीं है<br>III. कुछ फल प्लास्टिक हैं।<br>IV. कुछ ट्राउजर फल हैं।</p>",
                    options_en: ["<p>Only conclusions Ⅰ, Ⅲ and Ⅳ follow.</p>", "<p>Only conclusions Ⅰ and Ⅲ follow.</p>", 
                                "<p>Only conclusions Ⅰ, Ⅱ and Ⅲ follow.</p>", "<p>Only conclusions Ⅰ and Ⅱ follow</p>"],
                    options_hi: ["<p>केवल निष्कर्ष Ⅰ, Ⅲ और Ⅳ अनुसरण करते हैं।</p>", "<p>केवल निष्कर्ष Ⅰ और अनुसरण करते हैं।</p>",
                                "<p>केवल निष्कर्ष Ⅰ, Ⅱ और Ⅲ अनुसरण करते हैं।</p>", "<p>केवल निष्कर्ष Ⅰ और Ⅱ अनुसरण करते हैं</p>"],
                    solution_en: "<p>13.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729590792105.png\" alt=\"rId19\" width=\"314\" height=\"106\"><br>From the given diagram it is clear that Some books are plastic, No trousers are plastic and some fruits are plastics.</p>",
                    solution_hi: "<p>13.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729590792105.png\" alt=\"rId19\" width=\"314\" height=\"106\"><br>दिए गए आरेख से यह स्पष्ट है कि कुछ किताबें प्लास्टिक हैं, कोई पतलून प्लास्टिक नहीं है और कुछ फल प्लास्टिक हैं।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "14",
                    section: "6",
                    question_en: "<p>14. Select the alphanumeric- cluster that can replace the question mark (?) in the following series.<br>B9U, E16R, H25O, ?</p>",
                    question_hi: "<p>14. अक्षरांकीय समूह का चयन कीजिये जो निम्नलिखित श्रृंखला में प्रश्न चिह्न (?) को प्रतिस्थापित कर सकता है<br>B9U, E16R, H25O, ?</p>",
                    options_en: ["<p>M38N</p>", "<p>K36L</p>", 
                                "<p>K36M</p>", "<p>J34L</p>"],
                    options_hi: ["<p>M38N</p>", "<p>K36L</p>",
                                "<p>K36M</p>", "<p>J34L</p>"],
                    solution_en: "<p>14.(b)<br>B + 3 = E, E + 3 = H, H + 3 = K<br>U - 3 = R, R - 3 = O, O - 3 = L<br><math display=\"inline\"><msup><mrow><mn>3</mn></mrow><mrow><mn>2</mn></mrow></msup><mo>=</mo><mn>9</mn><mo>,</mo><mi>&#160;</mi><msup><mrow><mn>4</mn></mrow><mrow><mn>2</mn></mrow></msup><mo>=</mo><mn>16</mn><mo>,</mo><mi>&#160;</mi><msup><mrow><mn>5</mn></mrow><mrow><mn>2</mn></mrow></msup><mo>=</mo><mn>25</mn></math>, 6<sup>2</sup>=36<br>Next will be = K36L</p>",
                    solution_hi: "<p>14.(b)<br>B + 3 = E, E + 3 = H, H + 3 = K<br>U - 3 = R, R - 3 = O, O - 3 = L<br><math display=\"inline\"><msup><mrow><mn>3</mn></mrow><mrow><mn>2</mn></mrow></msup><mo>=</mo><mn>9</mn><mo>,</mo><mi>&#160;</mi><msup><mrow><mn>4</mn></mrow><mrow><mn>2</mn></mrow></msup><mo>=</mo><mn>16</mn><mo>,</mo><mi>&#160;</mi><msup><mrow><mn>5</mn></mrow><mrow><mn>2</mn></mrow></msup><mo>=</mo><mn>25</mn></math>, 6<sup>2</sup>=36<br>अगला होगा = K36L</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "15",
                    section: "6",
                    question_en: "<p>15. Select the Venn diagram that best represents the relationship between the given set of classes.<br>Cobbler, Man, Shoe, Footwear</p>",
                    question_hi: "<p>15. वेन आरेख का चयन करें जो दिए गए वर्गों के बीच संबंध का सबसे अच्छा प्रतिनिधित्व करता है।<br>मोची, आदमी, जूता, पदत्राण</p>",
                    options_en: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729590792222.png\" alt=\"rId20\" width=\"97\" height=\"90\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729590792327.png\" alt=\"rId21\" width=\"142\" height=\"91\"></p>", 
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729590792438.png\" alt=\"rId22\" width=\"176\" height=\"80\"><br><br></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729590792563.png\" alt=\"rId23\" width=\"168\" height=\"60\"></p>"],
                    options_hi: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729590792222.png\" alt=\"rId20\" width=\"97\" height=\"90\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729590792327.png\" alt=\"rId21\" width=\"142\" height=\"91\"></p>",
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729590792438.png\" alt=\"rId22\" width=\"176\" height=\"80\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729590792563.png\" alt=\"rId23\" width=\"168\" height=\"60\"><br><br><br></p>"],
                    solution_en: "<p>15.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729590792438.png\" alt=\"rId22\" width=\"198\" height=\"90\"></p>",
                    solution_hi: "<p>15.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729590792438.png\" alt=\"rId22\" width=\"198\" height=\"90\"></p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "16",
                    section: "6",
                    question_en: "<p>16. Select the Venn diagram that best represents the relationship between the given set of classes.<br>London, Paris, France</p>",
                    question_hi: "<p>16. वेन आरेख का चयन करें जो दिए गए वर्गों के बीच संबंध का सबसे अच्छा प्रतिनिधित्व करता है।<br>लंदन, पेरिस, फ्रांस</p>",
                    options_en: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729590792814.png\" alt=\"rId24\" width=\"108\" height=\"80\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729590792937.png\" alt=\"rId25\" width=\"103\" height=\"90\"><br><br></p>", 
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729590793039.png\" alt=\"rId26\" width=\"103\" height=\"92\"><br><br></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729590793143.png\" alt=\"rId27\" width=\"130\" height=\"73\"></p>"],
                    options_hi: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729590792814.png\" alt=\"rId24\" width=\"108\" height=\"80\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729590792937.png\" alt=\"rId25\" width=\"103\" height=\"90\"></p>",
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729590793039.png\" alt=\"rId26\" width=\"103\" height=\"92\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729590793143.png\" alt=\"rId27\" width=\"130\" height=\"73\"><br><br><br></p>"],
                    solution_en: "<p>16.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729590793143.png\" alt=\"rId27\" width=\"143\" height=\"80\"></p>",
                    solution_hi: "<p>16.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729590793143.png\" alt=\"rId27\" width=\"143\" height=\"80\"></p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "17",
                    section: "6",
                    question_en: "<p>17. Consider the given statements and the following assumptions and decide which of the assumption(s) is/are implicit in the statement. <br><strong>Statements:</strong> <br>Children share their feelings more with their friends than with their parents <br><strong>Assumptions:</strong><br>I.Children feel that their friends will be able to understand their feelings better than their parents. <br>II Children are not friendly with their parents nowadays.</p>",
                    question_hi: "<p>17. दिए गए कथनों और निम्नलिखित धारणाओं पर विचार करें और तय करें कि कौन सी धारणाएं कथन में निहित हैं<br><strong>कथन:</strong> <br>बच्चे अपनी भावनाओं को अपने माता-पिता से ज्यादा अपने दोस्तों के साथ साझा करते हैं।<br><strong>धारणा:</strong><br>I. बच्चों को लगता है कि उनके दोस्त उनकी भावनाओं को उनके माता-पिता से बेहतर समझ पाएंगे<br>II बच्चे आजकल अपने माता-पिता के साथ दोस्ताना नहीं हैं</p>",
                    options_en: ["<p>Only assumptions Ⅱ is implicit.</p>", "<p>Both assumptions Ⅰ and Ⅱ are implicit.</p>", 
                                "<p>Neither assumptions Ⅰ nor Ⅱ is implicit.</p>", "<p>Only assumptions Ⅰ is implicit.</p>"],
                    options_hi: ["<p>केवल धारणाएँ Ⅱ निहित हैं।</p>", "<p>दोनों धारणाएँ Ⅰ और Ⅱ निहित हैं।</p>",
                                "<p>न तो Ⅰ धारणाएँ और न ही Ⅱ निहित हैं।</p>", "<p>केवल धारणाएँ Ⅰ निहित हैं</p>"],
                    solution_en: "<p>17(d)<br>From the given statements it is clear that Children share their feelings more with their friends than with their parents because Children feel that their friends will be able to understand their feelings better than their parents.</p>",
                    solution_hi: "<p>17.(d)<br>दिए गए कथनों से यह स्पष्ट है कि बच्चे अपनी भावनाओं को अपने माता-पिता की तुलना में अपने दोस्तों के साथ अधिक साझा करते हैं क्योंकि बच्चों को लगता है कि उनके दोस्त उनके माता-पिता की तुलना में उनकी भावनाओं को बेहतर ढंग से समझ पाएंगे।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "18",
                    section: "6",
                    question_en: "18. In a certain code language, TENSION is written as 183121771312 , How will MARUTHI be written as in that language?",
                    question_hi: "18. एक निश्चित कूट भाषा में TENSION को 183121771312 लिखा जाता है, MARUTHI को उसी भाषा में किस प्रकार लिखा जाएगा?",
                    options_en: [" 112516191867", " 671125161918", 
                                " 111625911867", " 111925161786"],
                    options_hi: [" 112516191867", " 671125161918",
                                " 111625911867", " 111925161786"],
                    solution_en: "18.(a)<br />Logic:- Place value of letter - 2<br />The code for MARUTHI = 112516191867",
                    solution_hi: "18.(a)<br />तर्क:- अक्षर का स्थानीय मान - 2<br />मारुति के लिए कोड = 112516191867",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "19",
                    section: "6",
                    question_en: "19. Four words have been given, out of which three are alike in some manner and one is different. Select the odd one.",
                    question_hi: "19. चार शब्द दिए गए हैं, जिनमें से तीन किसी तरह से एक जैसे हैं और एक अलग है। विजातीय का चयन कीजिये ",
                    options_en: [" Weight ", " Strength ", 
                                " Mass ", " Volume "],
                    options_hi: [" वज़न", " ताकत",
                                " द्रव्यमान", " आयतन"],
                    solution_en: "19.(b)<br />Strength can not be measured but all others can be measured.",
                    solution_hi: "19.(b)<br />ताकत को मापा नहीं जा सकता लेकिन बाकी सभी को मापा जा सकता है।",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "20",
                    section: "6",
                    question_en: "<p>20. Select the number from among the given options that can replace the question mark (?) in the following series.<br>7: 56:: 8: 72:: 9: ?</p>",
                    question_hi: "<p>20. दिए गए विकल्पों में से वह संख्या चुनिए जो निम्नलिखित श्रृंखला में प्रश्नवाचक चिन्ह (?) को प्रतिस्थापित कर सके।<br>7: 56:: 8: 72:: 9: ?</p>",
                    options_en: ["<p>100</p>", "<p>90</p>", 
                                "<p>99</p>", "<p>81</p>"],
                    options_hi: ["<p>100</p>", "<p>90</p>",
                                "<p>99</p>", "<p>81</p>"],
                    solution_en: "<p>20.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729590793300.png\" alt=\"rId28\" width=\"283\" height=\"68\"></p>",
                    solution_hi: "<p>20.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729590793300.png\" alt=\"rId28\" width=\"283\" height=\"68\"></p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "21",
                    section: "6",
                    question_en: "21. Select the option in which the words share the same relationship as that shared by the given pair of words.<br />Relaxation  : Sleep ",
                    question_hi: "21. उस विकल्प का चयन करें जिसमें शब्द वही संबंध साझा करते हैं जो दिए गए शब्दों के जोड़े द्वारा साझा किए गए हैं।<br /> विश्राम : नींद",
                    options_en: [" Cure  : Medicine ", " Happiness  : Feeling ", 
                                " Minute  : Time ", " Sky  : Rocket "],
                    options_hi: [" इलाज :  दवा", " ख़ुशी :  भावना",
                                " मिनट :  समय", " आकाश :  राकेट"],
                    solution_en: "21.(a)<br />As relaxation is related to Sleep in the same way cure is related to Medicine.",
                    solution_hi: "21.(a)<br />जिस प्रकार विश्राम का सम्बन्ध निद्रा से है, उसी प्रकार चिकित्सा का सम्बन्ध औषधि से है।",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "22",
                    section: "6",
                    question_en: "22. Six cousins, A, B, C, D, E and F are going on a bike trip to Laddakh. Each one is riding a different bike. E is riding slower than F. A is riding faster than C but slower then D. D is riding slower than B but faster than F, C is riding faster than F, who among these six is riding the fastest? ",
                    question_hi: "22. छह चचेरे भाई, A, B, C, D, E और F लद्दाख की बाइक यात्रा पर जा रहे हैं। हर कोई अलग-अलग बाइक चला रहा है। E, F से धीमी सवारी कर रहा है। A, C से तेज सवारी कर रहा है, लेकिन फिर धीमा है। D, B से धीमी सवारी कर रहा है लेकिन F से तेज है, C, F से तेज सवारी कर रहा है, इन छह में से कौन सबसे तेज सवारी कर रहा है?",
                    options_en: [" A", " D", 
                                " C", " B"],
                    options_hi: [" A", " D",
                                " C", " B"],
                    solution_en: "22.(d)<br />B > D > A > C > F > E ",
                    solution_hi: "22.(d)<br />B > D > A > C > F > E ",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "23",
                    section: "6",
                    question_en: "23. If every 4th letter going from left to right of the English alphabet is deleted, then what would be the 5th letter from the right in the new series obtained?",
                    question_hi: "23. यदि अंग्रेजी वर्णमाला के बाएँ से दाएँ जाने वाले प्रत्येक चौथे अक्षर को हटा दिया जाए, तो प्राप्त नई श्रृंखला में दाएँ से पाँचवाँ अक्षर क्या होगा?",
                    options_en: [" E", " F", 
                                " U", " V"],
                    options_hi: [" E", " F",
                                " U", " V"],
                    solution_en: "23.(c)<br />If every 4th letter going from left to right of the English alphabet is deleted, then the 24th letter is deleted also. <br />5th from the right = 26 - 5 = 21 = U",
                    solution_hi: "23.(c)<br />यदि अंग्रेजी वर्णमाला के बाएँ से दाएँ जाने वाले प्रत्येक चौथे अक्षर को हटा दिया जाए, तो 24वें अक्षर को भी हटा दिया जाता है।<br />दायें से 5वां = 26 - 5 = 21 = U",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "24",
                    section: "6",
                    question_en: "24. Select the number that can replace the question mark(?) in the following series.<br />             112, 147, 182, 217, ?",
                    question_hi: "24. उस संख्या का चयन करें जो निम्नलिखित श्रृंखला में प्रश्न चिह्न (?) को प्रतिस्थापित कर सकती है।<br />             112, 147, 182, 217, ?",
                    options_en: [" 242", " 250", 
                                " 248", " 252"],
                    options_hi: [" 242", " 250",
                                " 248", " 252"],
                    solution_en: "24.(d)<br />112 + 35 = 147<br />147 + 35 = 182<br />182 + 35 = 217<br />217 + 35 = 252",
                    solution_hi: "24.(d)<br />112 + 35 = 147<br />147 + 35 = 182<br />182 + 35 = 217<br />217 + 35 = 252",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "25",
                    section: "6",
                    question_en: "<p>25. Read the given statements and conclusions carefully. Assuming that the information given in the statements is true, even if it appears to be at variance with commonly known facts, decide which of the given conclusions logically follow(s) from the statement.<br><strong>Statements:</strong> <br>I Some blankets are scarves. <br>II. All rooms are a blanket. <br><strong>Conclusions:</strong><br>(I) Some scarves are blankets. <br>(II) Some rooms are scarves.</p>",
                    question_hi: "<p>25. दिए गए कथनों और निष्कर्षों को ध्यानपूर्वक पढ़िए। यह मानते हुए कि कथनों में दी गई जानकारी सत्य है, भले ही वह सामान्य रूप से ज्ञात तथ्यों से भिन्न प्रतीत होती हो, बताइये कि दिए गए निष्कर्षों में से कौन सा कथन का तार्किक रूप से अनुसरण करता है। <br><strong>कथन:</strong> <br>I. कुछ कंबल स्कार्फ हैं<br>II. सभी कमरे कंबल हैं।<br><strong>निष्कर्ष:</strong><br>(I) कुछ स्कार्फ कंबल हैं।<br>(II) कुछ कमरे स्कार्फ हैं</p>",
                    options_en: ["<p>Only conclusions (ii) follows.</p>", "<p>Only conclusions (i) follows.</p>", 
                                "<p>Neither conclusion (i) nor (ii) follows.</p>", "<p>Both conclusions (i) and (ii) follow.</p>"],
                    options_hi: ["<p>केवल निष्कर्ष (ii) अनुसरण करता है।</p>", "<p>केवल निष्कर्ष (i) अनुसरण करता है</p>",
                                "<p>न तो निष्कर्ष (i) और न ही (ii) अनुसरण करता है।</p>", "<p>दोनों निष्कर्ष (i) और (ii) अनुसरण करते हैं।</p>"],
                    solution_en: "<p>25.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729590793412.png\" alt=\"rId29\"><br>From the above diagram it is clear that some scarves are blankets.</p>",
                    solution_hi: "<p>25.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729590793412.png\" alt=\"rId29\"><br>ऊपर दिए गए आरेख से यह स्पष्ट है कि कुछ स्कार्फ कंबल हैं।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "26",
                    section: "misc",
                    question_en: "<p>26. Select the Venn diagram that best represents the relationship between the given set of classes.<br>Planet, Earth, Earthlings</p>",
                    question_hi: "<p>26. वेन आरेख का चयन करें जो दिए गए वर्गों के बीच संबंध का सबसे अच्छा प्रतिनिधित्व करता है।<br>ग्रह, पृथ्वी, पृथ्वीवासी</p>",
                    options_en: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729590793143.png\" alt=\"rId27\" width=\"118\" height=\"66\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729590793580.png\" alt=\"rId30\" width=\"93\" height=\"65\"></p>", 
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729590793039.png\" alt=\"rId26\" width=\"77\" height=\"69\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729590792814.png\" alt=\"rId24\" width=\"95\" height=\"71\"></p>"],
                    options_hi: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729590793143.png\" alt=\"rId27\" width=\"118\" height=\"66\"></p>", "<p><strong><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729590793580.png\" alt=\"rId30\" width=\"93\" height=\"65\"></strong><br><br></p>",
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729590793039.png\" alt=\"rId26\" width=\"77\" height=\"69\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729590792814.png\" alt=\"rId24\" width=\"95\" height=\"71\"></p>"],
                    solution_en: "<p>26.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729590793143.png\" alt=\"rId27\" width=\"136\" height=\"76\"></p>",
                    solution_hi: "<p>26.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729590793143.png\" alt=\"rId27\" width=\"136\" height=\"76\"></p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>