<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">25:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 25</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">25</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 25 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 0,
                end: 23
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 24,
                end: 24
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "17",
                    question_en: "<p>1. A pair of straight lines from an external point F intersects a circle at A and B (FA &lt; FB), and touches the circle at C. O is the centre of the circle. Given that &ang;ACF = 50&deg; and &ang;AFC = 30&deg;, find &ang;AOB.</p>",
                    question_hi: "<p>1. बाह्य बिंदु F से सीधी रेखाओं का एक युग्&zwj;म वृत्त को A और B (FA &lt; FB) पर प्रतिच्&zwj;छेद करता है, और C पर वृत्त को स्पर्श करता है। O वृत्त का केंद्र है। यदि &ang;ACF = 50&deg; और &ang;AFC = 30&deg; दिया गया है, तो &ang;AOB ज्ञात करें।</p>",
                    options_en: ["<p>80&deg;</p>", "<p>90&deg;</p>", 
                                "<p>100&deg;</p>", "<p>40&deg;</p>"],
                    options_hi: ["<p>80&deg;</p>", "<p>90&deg;</p>",
                                "<p>100&deg;</p>", "<p>40&deg;</p>"],
                    solution_en: "<p>1.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730963554108.png\" alt=\"rId4\" width=\"300\" height=\"167\"><br>In <math display=\"inline\"><mi>&#916;</mi><mi>A</mi><mi>C</mi><mi>F</mi></math><br>&ang;CAF = <math display=\"inline\"><msup><mrow><mn>180</mn></mrow><mrow><mo>&#176;</mo></mrow></msup></math> - 50&deg; - 30&deg; = 100&deg;<br><math display=\"inline\"><mi>z</mi></math> = &ang;ACF + &ang;CFA &hellip;. (exterior angle property)<br>= <math display=\"inline\"><msup><mrow><mn>50</mn></mrow><mrow><mo>&#176;</mo></mrow></msup></math> + 30&deg; = 80&deg;<br><math display=\"inline\"><mi>z</mi></math> = x = 80&deg; &hellip;(alternate segment theorem)<br><math display=\"inline\"><mi>y</mi></math> = 180&deg; - x - &ang;ACF &hellip;. (linear pair)<br>= <math display=\"inline\"><msup><mrow><mn>180</mn></mrow><mrow><mo>&#176;</mo></mrow></msup></math> - 80&deg; - 50&deg; = 50&deg;<br>&ang;AOB = 2 <math display=\"inline\"><mo>&#215;</mo></math> y = 2 &times; 50&deg; = 100&deg;</p>",
                    solution_hi: "<p>1.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730963554108.png\" alt=\"rId4\" width=\"300\" height=\"167\"><br>In <math display=\"inline\"><mi>&#916;</mi><mi>A</mi><mi>C</mi><mi>F</mi></math><br>&ang;CAF = <math display=\"inline\"><msup><mrow><mn>180</mn></mrow><mrow><mo>&#176;</mo></mrow></msup></math> - 50&deg; - 30&deg; = 100&deg;<br><math display=\"inline\"><mi>z</mi></math> = &ang;ACF + &ang;CFA &hellip;. (बाह्य कोण प्रमेय)<br>= <math display=\"inline\"><msup><mrow><mn>50</mn></mrow><mrow><mo>&#176;</mo></mrow></msup></math> + 30&deg; = 80&deg;<br><math display=\"inline\"><mi>z</mi></math> = x = 80&deg; &hellip;(एकातंर खंड प्रमेय)<br><math display=\"inline\"><mi>y</mi></math> = 180&deg; - x - &ang;ACF &hellip;. (रैखिक युग्म)<br>= <math display=\"inline\"><msup><mrow><mn>180</mn></mrow><mrow><mo>&#176;</mo></mrow></msup></math> - 80&deg; - 50&deg; = 50&deg;<br>&ang;AOB = 2 <math display=\"inline\"><mo>&#215;</mo></math> y = 2 &times; 50&deg; = 100&deg;</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "17",
                    question_en: "<p>2. The difference between a discount of 25% and two successive discounts of 15% and 20% on a certain bill was ₹28. Find the amount of the bill.</p>",
                    question_hi: "<p>2. एक निश्चित बिल पर 25% की छूट तथा 15% और 20% की दो क्रमिक छूटों के बीच का अंतर ₹28 था। बिल की राशि ज्ञात करें।</p>",
                    options_en: ["<p>₹500</p>", "<p>₹400</p>", 
                                "<p>₹450</p>", "<p>₹550</p>"],
                    options_hi: ["<p>₹500</p>", "<p>₹400</p>",
                                "<p>₹450</p>", "<p>₹550</p>"],
                    solution_en: "<p>2.(b) Let the amount of bill = 100 unit <br>Given first discount = 25%<br>and two successive discount = 15% and 20% <br>= (15 + 20 - <math display=\"inline\"><mfrac><mrow><mn>15</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>20</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math>)%<br>= 32 % <br>Now , According to question <br><math display=\"inline\"><mo>&#8658;</mo></math>(32 - 25 )% =₹ 28<br><math display=\"inline\"><mo>&#8658;</mo></math> 7% = 28 <br><math display=\"inline\"><mo>&#8658;</mo></math> 1% = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>28</mn><mn>7</mn></mfrac></math><br>100% = <math display=\"inline\"><mn>4</mn></math> &times; 100 = ₹400</p>",
                    solution_hi: "<p>2.(b) माना , बिल की राशि = 100 यूनिट <br>पहली छूट दी गई = 25%<br>और दो क्रमिक छूट = 15% और 20% <br>= (15 + 20 - <math display=\"inline\"><mfrac><mrow><mn>15</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>20</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math>)%<br>= 32 % <br>अब, प्रश्न के अनुसार <br>(32 - 25 )% = ₹ 28<br>7% = 28 <br><math display=\"inline\"><mo>&#8658;</mo></math> 1% = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>28</mn><mn>7</mn></mfrac></math><br>100% = 4 &times; 100 = ₹400</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "17",
                    question_en: "3. Which of the following numbers is divisible by 2, 5, 10 and 11 ?",
                    question_hi: "3. निम्नलिखित में से कौन सी संख्या 2, 5, 10 और 11 से विभाज्य है?",
                    options_en: [" 203467 ", " 830942", 
                                " 589270", " 1234560"],
                    options_hi: [" 203467 ", " 830942",
                                " 589270", " 1234560"],
                    solution_en: "3.(c) Divisibility rule of 2 : Last number should be an even number <br />By checking options  , option (b) , (c) , (d) satisfy this condition <br />Divisibility rule of 5 : Last number should be 0 or 5 <br />By checking option , option (c) , (d) satisfy this condition<br />Divisibility rule  of 10 :  Number should be divisible by 2 and 5 <br />Divisibility rule of 11 : Difference between sum of digit odd place and sum of digit  at even place should be  0 or multiple of 11 <br />By checking option , option ( c) satisfy this condition<br />(5 + 9 + 7 ) - (8 + 2 + 0)<br />21 - 10 = 11 (multiple of 11)<br />So  , option (c) i.e 589270 is divisible by 2 , 5 , 10 and 11",
                    solution_hi: "3.(c) 2 की विभाज्यता नियम: अंतिम संख्या एक सम संख्या होनी चाहिए <br />विकल्पों की जाँच करके, विकल्प (b) , (c) , (d)  इस शर्त को पूरा करते हैं <br />5 की विभाज्यता नियम: अंतिम संख्या 0 या 5 होनी चाहिए <br />विकल्प, विकल्प (c), (d) की जांच करके इस शर्त को पूरा करें<br />10 की विभाज्यता नियम: संख्या 2 और 5 से विभाज्य होनी चाहिए <br />11 का विभाज्यता नियम: विषम स्थान के अंकों के योग और सम स्थान के अंकों के योग के बीच अंतर 0 या 11 का गुणक होना चाहिए <br />विकल्प की जाँच करने पर , विकल्प (c) संतुष्ट हो रहा है । <br />(5 + 9 + 7 ) - (8 + 2 + 0)<br />21 - 10 = 11 (11 का गुणक)<br />तो, विकल्प (c) यानी 589270  2 , 5 , 10 और 11 से विभाज्य है।  ",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "17",
                    question_en: "<p>4. Which of the following is the sum of the values of a and b if the equations 2x + y = a, 8x + by = 12 have infinite solutions ?</p>",
                    question_hi: "<p>4. यदि समीकरणों 2x + y = a, 8x + by = 12 के अनंत हल है, तो a और b के मान का योग निम्नलिखित में से कौन-सा है ?</p>",
                    options_en: ["<p>16</p>", "<p>9</p>", 
                                "<p>7</p>", "<p>18</p>"],
                    options_hi: ["<p>16</p>", "<p>9</p>",
                                "<p>7</p>", "<p>18</p>"],
                    solution_en: "<p>4.(c) <strong>Given Equation : </strong>2x + y = a and 8x + by = 12<br>For infinite solution <math display=\"inline\"><mfrac><mrow><msub><mrow><mi>a</mi></mrow><mrow><mn>1</mn></mrow></msub></mrow><mrow><msub><mrow><mi>a</mi></mrow><mrow><mn>2</mn></mrow></msub></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msub><mi>b</mi><mn>1</mn></msub><mrow><msub><mi>b</mi><mn>2</mn></msub><mo>&#160;</mo></mrow></mfrac><mo>=</mo><mfrac><msub><mi>c</mi><mn>1</mn></msub><msub><mi>c</mi><mn>2</mn></msub></mfrac></math> &hellip;(i)<br>Here<math display=\"inline\"><mo>,</mo><mi>&#160;</mi><msub><mrow><mi>a</mi></mrow><mrow><mn>1</mn></mrow></msub></math> = 2 , a<sub>2</sub> = 8 , b<sub>1</sub> = 1, b<sub>2</sub> = b , c<sub>1</sub> = - a , c<sub>2</sub> = -12<br>Put this value in eqn( i)<br><math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi>b</mi></mfrac><mo>&#160;</mo></math>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>-</mo><mi>a</mi></mrow><mrow><mo>-</mo><mn>12</mn></mrow></mfrac></math><br>By equating this equation , <br>a = 3 , b = 4<br>Now , Sum of a and b = 4 + 3 = 7</p>",
                    solution_hi: "<p>4.(c) दिया गया समीकरण &rarr; 2x + y = a और 8x + by = 12<br>अनंत हल के लिए <math display=\"inline\"><mfrac><mrow><msub><mrow><mi>a</mi></mrow><mrow><mn>1</mn></mrow></msub></mrow><mrow><msub><mrow><mi>a</mi></mrow><mrow><mn>2</mn></mrow></msub></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msub><mi>b</mi><mn>1</mn></msub><mrow><msub><mi>b</mi><mn>2</mn></msub><mo>&#160;</mo></mrow></mfrac><mo>=</mo><mfrac><msub><mi>c</mi><mn>1</mn></msub><msub><mi>c</mi><mn>2</mn></msub></mfrac></math> &hellip;(i)<br>यहाँ<math display=\"inline\"><mo>,</mo><mi>&#160;</mi><msub><mrow><mi>a</mi></mrow><mrow><mn>1</mn></mrow></msub></math> = 2 , a<sub>2</sub> = 8 , b<sub>1</sub> = 1, b<sub>2</sub> = b , c<sub>1</sub> = - a , c<sub>2</sub> = -12<br>इस मान को समीकरण (i) में रखने पर -<br><math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi>b</mi></mfrac><mo>&#160;</mo></math>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>-</mo><mi>a</mi></mrow><mrow><mo>-</mo><mn>12</mn></mrow></mfrac></math><br>इस समीकरण को समतुल्य करने पर , <br>a = 3 , b = 4<br>अब, a और b का योग = 4 + 3 = 7</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "17",
                    question_en: "5. How much simple interest will ₹6,700 earn in 13 months at a 12% interest rate per annum?",
                    question_hi: "5. 12% प्रति वर्ष ब्याज दर से 13 महीनों में ₹6,700 पर कितना साधारण ब्याज अर्जित होगा?",
                    options_en: [" ₹871", " ₹889  ", 
                                " ₹821", " ₹813"],
                    options_hi: [" ₹871", " ₹889  ",
                                " ₹821", " ₹813"],
                    solution_en: "5.(a) Given , Principal = ₹6700 , Time = 13 Month , Rate = 12% <br />Simple interest = <math display=\"inline\"><mfrac><mrow><mn>6700</mn><mi>&nbsp;</mi><mo>×</mo><mi>&nbsp;</mi><mn>13</mn><mi>&nbsp;</mi><mo>×</mo><mi>&nbsp;</mi><mn>12</mn></mrow><mrow><mn>100</mn><mi>&nbsp;</mi><mo>×</mo><mi>&nbsp;</mi><mn>12</mn></mrow></mfrac></math> = 67 × 13 = ₹ 871",
                    solution_hi: "5.(a) दिया गया है, मूलधन = ₹6700 , समय = 13 महीना, दर = 12% <br />साधारण ब्याज = <math display=\"inline\"><mfrac><mrow><mn>6700</mn><mi>&nbsp;</mi><mo>×</mo><mi>&nbsp;</mi><mn>13</mn><mi>&nbsp;</mi><mo>×</mo><mi>&nbsp;</mi><mn>12</mn></mrow><mrow><mn>100</mn><mi>&nbsp;</mi><mo>×</mo><mi>&nbsp;</mi><mn>12</mn></mrow></mfrac></math> = 67 × 13 = ₹ 871",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "17",
                    question_en: "<p>6. If tan 4<math display=\"inline\"><mi>&#952;</mi></math>. tan6&theta; = 1, where 6&theta; is an acute angle, then find the value of Cot5&theta;.</p>",
                    question_hi: "<p>6. यदि tan 4<math display=\"inline\"><mi>&#952;</mi></math>. tan6&theta; = 1 है, जहाँ 6&theta; एक न्यूनकोण है, तो Cot5&theta; का मान ज्ञात कीजिए।</p>",
                    options_en: ["<p><math display=\"inline\"><msqrt><mn>3</mn></msqrt></math></p>", "<p><math display=\"inline\"><mo>-</mo><msqrt><mn>3</mn></msqrt></math></p>", 
                                "<p><math display=\"inline\"><mo>-</mo><mn>1</mn></math></p>", "<p>1</p>"],
                    options_hi: ["<p><math display=\"inline\"><msqrt><mn>3</mn></msqrt></math></p>", "<p><math display=\"inline\"><mo>-</mo><msqrt><mn>3</mn></msqrt></math></p>",
                                "<p><math display=\"inline\"><mo>-</mo><mn>1</mn></math></p>", "<p>1</p>"],
                    solution_en: "<p>6.(d) <strong>Given:-</strong> tan 4<math display=\"inline\"><mi>&#952;</mi></math>. tan6&theta; = 1<br><math display=\"inline\"><mo>&#8658;</mo></math> tan 4&theta; = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><mi>tan</mi><mn>6</mn><mi>&#952;</mi></mrow></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math> tan 4&theta; = cot 6&theta;<br><math display=\"inline\"><mo>&#8658;</mo></math> tan 4&theta; = tan(90&deg; - 6&theta;)<br><math display=\"inline\"><mo>&#8658;</mo></math> 4&theta; = 90&deg; - 6&theta; <br><math display=\"inline\"><mo>&#8658;</mo></math> 10&theta; = 90&deg;<br><math display=\"inline\"><mo>&#8658;</mo></math> &theta; = 9&deg;<br>Put the value of<math display=\"inline\"><mi>&#952;</mi></math> in Cot5&theta; <br>cot 45&deg; = 1</p>",
                    solution_hi: "<p>6.(d) <strong>दिया गया है:-</strong> tan 4<math display=\"inline\"><mi>&#952;</mi></math>. tan6&theta; = 1<br><math display=\"inline\"><mo>&#8658;</mo></math> tan 4&theta; = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><mi>tan</mi><mn>6</mn><mi>&#952;</mi></mrow></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math> tan 4&theta; = cot 6&theta;<br><math display=\"inline\"><mo>&#8658;</mo></math> tan 4&theta; = tan(90&deg; - 6&theta;)<br><math display=\"inline\"><mo>&#8658;</mo></math> 4&theta; = 90&deg; - 6&theta; <br><math display=\"inline\"><mo>&#8658;</mo></math> 10&theta; = 90&deg;<br><math display=\"inline\"><mo>&#8658;</mo></math> &theta; = 9&deg;<br><math display=\"inline\"><mi>&#952;</mi></math> का मान Cot5&theta; रखने पर <br>cot 45&deg; = 1</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "17",
                    question_en: "<p>7. Study the given chart and answer the question that follows.<br>The following chart shows the production of textbooks in thousands in the years<br>2018, 2020 and 2022 from the print houses A, B and C, respectively.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730963554228.png\" alt=\"rId5\" width=\"335\" height=\"253\"> <br>The ratio of textbooks produced by Print house B in the year 2018 to the textbooks produced by Print house C in the year 2020 is ______.</p>",
                    question_hi: "<p>7. दिए गए चार्ट का अध्ययन करें और निम्नलिखित प्रश्न का उत्तर दें।<br>निम्नलिखित चार्ट क्रमशः मुद्रणालय A, B और C से वर्ष 2018, 2020 और 2022 में हजारों की संख्या में पाठ्यपुस्तकों के प्रकाशन को दर्शाता है।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730963554330.png\" alt=\"rId6\" width=\"237\" height=\"223\"> <br>वर्ष 2018 में मुद्रणालय B द्वारा प्रकाशित पाठ्यपुस्तकों और वर्ष 2020 में मुद्रणालय C द्वारा प्रकाशित पाठ्यपुस्तकों का अनुपात ______ है।</p>",
                    options_en: ["<p>4 : 5</p>", "<p>3 : 4</p>", 
                                "<p>4 : 3</p>", "<p>2 : 3</p>"],
                    options_hi: ["<p>4 : 5</p>", "<p>3 : 4</p>",
                                "<p>4 : 3</p>", "<p>2 : 3</p>"],
                    solution_en: "<p>7.(c) The ratio of textbooks produced by Print house B in the year 2018 to the textbooks produced by Print house C in the year 2020 = 12 : 9 = 4 : 3</p>",
                    solution_hi: "<p>7.(c) वर्ष 2018 में मुद्रणालय B द्वारा उत्पादित पाठ्यपुस्तकों का वर्ष 2020 में मुद्रणालय C द्वारा उत्पादित पाठ्यपुस्तकों से अनुपात = 12 : 9 = 4 : 3 </p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "17",
                    question_en: "<p>8. In <math display=\"inline\"><mi>&#916;</mi></math>ABD and &Delta;FEC, &ang;BAD = 60&deg;, l(BD) = l(EC), &ang;ABD = &ang;FEC = 90&deg;, and&nbsp;l(AB) = l(FE). Find the ratio of <math display=\"inline\"><mfrac><mrow><mi>&#8736;</mi><mi>B</mi><mi>A</mi><mi>D</mi></mrow><mrow><mi>&#8736;</mi><mi>F</mi><mi>C</mi><mi>E</mi></mrow></mfrac></math>.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730963554444.png\" alt=\"rId7\" width=\"206\" height=\"131\"></p>",
                    question_hi: "<p>8. △ABD और △FEC में, &ang;BAD = 60&deg;, I(BD) = I(EC), &ang;ABD = &ang;FEC = 90&deg;, और I(AB) = I(FE) है।&nbsp;<math display=\"inline\"><mfrac><mrow><mi>&#8736;</mi><mi>B</mi><mi>A</mi><mi>D</mi></mrow><mrow><mi>&#8736;</mi><mi>F</mi><mi>C</mi><mi>E</mi></mrow></mfrac></math> का अनुपात ज्ञात कीजिए।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730963554444.png\" alt=\"rId7\" width=\"206\" height=\"131\"></p>",
                    options_en: ["<p>2 : 3</p>", "<p>1 : 2</p>", 
                                "<p>2 : 5</p>", "<p>2 : 1</p>"],
                    options_hi: ["<p>2 : 3</p>", "<p>1 : 2</p>",
                                "<p>2 : 5</p>", "<p>2 : 1</p>"],
                    solution_en: "<p>8.(d) <strong>Given </strong>, In <math display=\"inline\"><mi>&#916;</mi></math>ABD and &Delta;FEC, &ang;BAD = 60&deg;, l(BD) = l(EC), &ang;ABD = &ang;FEC = 90&deg;, and&nbsp;l(AB) = l(FE)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730963554545.png\" alt=\"rId8\" width=\"176\" height=\"96\"><br>Now in <math display=\"inline\"><mi>&#916;</mi></math>ABD ,<br><math display=\"inline\"><mi>&#8736;</mi><mi>A</mi></math> + &ang;B + &ang;D = 180&deg;<br>60&deg; + 90&deg; + <math display=\"inline\"><mi>&#8736;</mi></math>D = 180&deg;<br><math display=\"inline\"><mi>&#8736;</mi></math>D = 30&deg;<br>Now, According to question , <br>In <math display=\"inline\"><mi>&#916;</mi></math>ABD and &Delta;FEC<br>AB = FE (Given)<br><math display=\"inline\"><mi>&#8736;</mi></math>B = &ang;E (Each 90&deg;)<br>BD = CE (Given)<br>By SAS , <math display=\"inline\"><mi>&#916;</mi></math>ABD &cong; &Delta;FEC <br>∵ <math display=\"inline\"><mi>&#8736;</mi></math>D =&ang;C = 30&deg; <br>Now, ratio of <math display=\"inline\"><mfrac><mrow><mi>&#8736;</mi><mi>B</mi><mi>A</mi><mi>D</mi></mrow><mrow><mi>&#8736;</mi><mi>F</mi><mi>C</mi><mi>E</mi></mrow></mfrac></math> = 60&deg; : 30&deg; = 2 : 1</p>",
                    solution_hi: "<p>8.(d) <strong>दिया गया है, </strong><math display=\"inline\"><mi>&#916;</mi></math>ABD और &Delta;FEC में, &ang;BAD = 60&deg;, l(BD) = l(EC), &ang;ABD = &ang;FEC = 90&deg;, और<br>l(AB) = l(FE)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730963554545.png\" alt=\"rId8\" width=\"176\" height=\"96\"><br>अब <math display=\"inline\"><mi>&#916;</mi></math>ABD ,में,<br>&ang;A + &ang;B + &ang;D = 180&deg;<br>60&deg; + 90&deg; + &ang;D = 180&deg;<br>&ang;D = 30&deg;<br>अब, प्रश्न के अनुसार, <br><math display=\"inline\"><mi>&#916;</mi></math>ABD और &Delta;FEC में<br>AB = FE (दिया गया है)<br><math display=\"inline\"><mi>&#8736;</mi></math>B = &ang;E (प्रत्येक 90&deg;)<br>BD = CE (दिया गया है)<br>∵ &ang;D =&ang;C = 30&deg; <br>अतः , <math display=\"inline\"><mfrac><mrow><mi>&#8736;</mi><mi>B</mi><mi>A</mi><mi>D</mi></mrow><mrow><mi>&#8736;</mi><mi>F</mi><mi>C</mi><mi>E</mi></mrow></mfrac></math> का अनुपात = 60&deg; : 30&deg; = 2 : 1</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "17",
                    question_en: "<p>9. The following pie chart shows the percentage distribution of a total of 1600 employees in different departments of a company. The table shows the ratio of male to female employees in different departments. Study the information and answer the question that follows.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730963554670.png\" alt=\"rId9\" width=\"253\" height=\"235\"> <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730963554777.png\" alt=\"rId10\" width=\"207\" height=\"120\"> <br>What is the percentage of the number of employees in production department to the number of female employees of all the departments taken together ? (correct to one decimal place)</p>",
                    question_hi: "<p>9. निम्नलिखित पाई-चार्ट एक कंपनी के विभिन्न विभागों में कुल 1600 कर्मचारियों का प्रतिशत वितरण दर्शाता है। तालिका विभिन्न विभागों में पुरुष से महिला कर्मचारियों का अनुपात दर्शाती है। इस सूचना का अध्ययन करें और निम्नलिखित प्रश्न का उत्तर दें।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730963554872.png\" alt=\"rId11\" width=\"268\" height=\"236\"> <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730963554992.png\" alt=\"rId12\" width=\"215\" height=\"174\"> <br>उत्पादन विभाग में कर्मचारियों की संख्या, सभी विभागों की महिला कर्मचारियों की संख्या का कितना प्रतिशत है ? (दशमलव के एक स्थान तक सन्निकटित)<br>संदर्भ:-<br>department - विभाग<br>male - पुरुष<br>female - महिला<br>HR - मानव संसाधन<br>IT - आईटी<br>Accounts - लेखा<br>Marketing - विपणन<br>production - उत्पादन</p>",
                    options_en: ["<p>64.3%</p>", "<p>50.7%</p>", 
                                "<p>44.9%</p>", "<p>59.1%</p>"],
                    options_hi: ["<p>64.3%</p>", "<p>50.7%</p>",
                                "<p>44.9%</p>", "<p>59.1%</p>"],
                    solution_en: "<p>9.(d) The total number of employees in production department = 1600 &times; <math display=\"inline\"><mfrac><mrow><mn>30</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 480<br>Number of female employees in HR department = 1600 &times; <math display=\"inline\"><mfrac><mrow><mn>8</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>8</mn></mfrac></math> = 80<br>Number of female employees in IT department = 1600 &times; <math display=\"inline\"><mfrac><mrow><mn>15</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>5</mn></mfrac></math> = 144<br>Number of female employees in Account department = 1600 &times;&nbsp;<math display=\"inline\"><mfrac><mrow><mn>25</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mrow><mn>10</mn><mo>&#160;</mo></mrow></mfrac></math> = 280<br>Number of female employees in Marketing department = 1600 &times;&nbsp;<math display=\"inline\"><mfrac><mrow><mn>22</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>11</mn></mfrac></math> = 128<br>Number of female employees in Production department = 1600 &times;&nbsp;<math display=\"inline\"><mfrac><mrow><mn>30</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>8</mn></mfrac><mo>&#160;</mo></math>= 180<br>Number of female employees of all the departments taken together = 80 + 144 + 280 + 128 + 180 = 812<br><math display=\"inline\"><mo>&#8756;</mo><mi>&#160;</mi></math>the percentage of the number of employees in production department to the number of female employees of all the departments taken together = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>480</mn><mn>812</mn></mfrac></math>&times; 100 = 59.1%</p>",
                    solution_hi: "<p>9.(d) उत्पादन विभाग में कर्मचारियों की कुल संख्या = 1600 &times; <math display=\"inline\"><mfrac><mrow><mn>30</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 480<br>मानव संसाधन विभाग में महिला कर्मचारियों की संख्या = 1600 &times; <math display=\"inline\"><mfrac><mrow><mn>8</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>8</mn></mfrac></math> = 80<br>आई.टी. विभाग में महिला कर्मचारियों की संख्या = 1600 &times; <math display=\"inline\"><mfrac><mrow><mn>15</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>5</mn></mfrac></math> = 144<br>लेखा विभाग में महिला कर्मचारियों की संख्या = 1600 &times;&nbsp;<math display=\"inline\"><mfrac><mrow><mn>25</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mrow><mn>10</mn><mo>&#160;</mo></mrow></mfrac></math> = 280<br>विपणन विभाग में महिला कर्मचारियों की संख्या = 1600 &times;&nbsp;<math display=\"inline\"><mfrac><mrow><mn>22</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>11</mn></mfrac></math> = 128<br>उत्पादन विभाग में महिला कर्मचारियों की संख्या = 1600 &times;&nbsp;<math display=\"inline\"><mfrac><mrow><mn>30</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>8</mn></mfrac><mo>&#160;</mo></math>= 180<br>सभी विभागों में महिला कर्मचारियों की कुल संख्या = 80 + 144 + 280 + 128 + 180 = 812<br>&there4; सभी विभागों को मिलाकर उत्पादन विभाग में कर्मचारियों की संख्या का महिला कर्मचारियों की संख्या से प्रतिशत = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>480</mn><mn>812</mn></mfrac></math>&times; 100 = 59.1%</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "17",
                    question_en: "<p>10. Find the smallest value that must be assigned to number &lsquo;a&rsquo; in order for 91876a2 to be divisible by 8.</p>",
                    question_hi: "<p>10. 91876a2 को 8 से विभाज्य बनाने के लिए संख्या \'a\' को दिया जाने वाला सबसे छोटा मान ज्ञात कीजिए।</p>",
                    options_en: ["<p>3</p>", "<p>0</p>", 
                                "<p>1</p>", "<p>2</p>"],
                    options_hi: ["<p>3</p>", "<p>0</p>",
                                "<p>1</p>", "<p>2</p>"],
                    solution_en: "<p>10.(a) <strong>Divisibility rule of 8 :</strong> Last three digit should be divisible by 8<br>Now given , 91876a2 <br>&there4; <math display=\"inline\"><mfrac><mrow><mn>6</mn><mi>a</mi><mn>2</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math> , by checking option one by one , option (a) satisfy this condition <br><math display=\"inline\"><mfrac><mrow><mn>632</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math> = 79 <br>Hence , smallest value of a = 3</p>",
                    solution_hi: "<p>10.(a) <strong>8 की विभाज्यता नियम :- </strong>अंतिम तीन अंक 8 से विभाज्य होने चाहिए<br>अब दिया गया , 91876a2 <br>&there4; <math display=\"inline\"><mfrac><mrow><mn>6</mn><mi>a</mi><mn>2</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math> , एक-एक करके विकल्प की जाँच करके, विकल्प (a) इस शर्त को पूरा करता है <br><math display=\"inline\"><mfrac><mrow><mn>632</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math>= 79 <br>अतः, a का सबसे छोटा मान = 3</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "17",
                    question_en: "<p>11. Two pipes A and B can fill a tank in 1<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> hours and 2 hours, respectively. If both the pipes are opened simultaneously, then in how much will the empty tank be filled ?</p>",
                    question_hi: "<p>11. दो पाइप A और B, किसी टंकी को क्रमशः 1<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> घंटे और 2 घंटे में भर सकते हैं। यदि दोनों पाइप एक साथ खोले जाते हैं, तो कितने समय में खाली टंकी भर जाएगी ?</p>",
                    options_en: ["<p>1<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> hours</p>", "<p>55 minutes</p>", 
                                "<p>48 minutes</p>", "<p>1<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> hours</p>"],
                    options_hi: ["<p>1<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> घंटा</p>", "<p>55 मिनट</p>",
                                "<p>48 मिनट</p>", "<p>1<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> घंटा</p>"],
                    solution_en: "<p>11.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730963555102.png\" alt=\"rId13\" width=\"174\" height=\"130\"><br>&there4; time to fill the empty tank = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>5</mn></mfrac></math> &times; 60 minute = 48 minute</p>",
                    solution_hi: "<p>11.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730963555239.png\" alt=\"rId14\" width=\"156\" height=\"126\"><br>&there4; खाली टंकी को भरने का समय = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>5</mn></mfrac></math> &times; 60 मिनट = 48 मिनट</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "17",
                    question_en: "<p>12. If a&sup2; + b&sup2; + c&sup2; = 2(a + c -1), then the value of a&sup3; + b&sup3; + c&sup3; = ?</p>",
                    question_hi: "<p>12. यदि a&sup2; + b&sup2; + c&sup2; = 2(a + c - 1) है, तो a&sup3; + b&sup3; + c&sup3; = ?</p>",
                    options_en: ["<p>0</p>", "<p>2</p>", 
                                "<p>4</p>", "<p>1</p>"],
                    options_hi: ["<p>0</p>", "<p>2</p>",
                                "<p>4</p>", "<p>1</p>"],
                    solution_en: "<p>12.(b) <strong>Given </strong>, a&sup2; + b&sup2; + c&sup2; = 2(a + c -1) <br>Here , a&sup2; + b&sup2; + c&sup2; - 2a - 2c + 2 = 0<br><math display=\"inline\"><mo>&#8658;</mo><mi mathvariant=\"bold-italic\">&#160;</mi></math>a&sup2; - 2a + 1 + b&sup2; + c&sup2; - 2c + 1 = 0<br><math display=\"inline\"><mo>&#8658;</mo><mi mathvariant=\"bold-italic\">&#160;</mi></math>(a - 1)&sup2; + b&sup2; + (c - 1)&sup2; = 0<br>Now , <math display=\"inline\"><msup><mrow><mo>(</mo><mi mathvariant=\"bold-italic\">a</mi><mi mathvariant=\"bold-italic\">&#160;</mi><mo>-</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mn>1</mn><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup></math> <strong>= 0 , a = 1</strong><br>b&sup2; = 0 , b =0 and <math display=\"inline\"><msup><mrow><mo>(</mo><mi>c</mi><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>1</mn><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup></math> = 0 , c = 1<br>&there4; a&sup3; + b&sup3; + c&sup3; = 2</p>",
                    solution_hi: "<p>12.(b)&nbsp;<strong>दिया गया है:</strong><strong id=\"docs-internal-guid-03335b80-7fff-834f-69f3-43d78c126194\"></strong>, a&sup2; + b&sup2; + c&sup2; = 2(a + c -1)<br>अब , a&sup2; + b&sup2; + c&sup2; - 2a - 2c + 2 = 0<br><math display=\"inline\"><mo>&#8658;</mo><mi mathvariant=\"bold-italic\">&#160;</mi></math>a&sup2; - 2a + 1 + b&sup2; + c&sup2; - 2c + 1 = 0<br><math display=\"inline\"><mo>&#8658;</mo><mi mathvariant=\"bold-italic\">&#160;</mi></math>(a - 1)&sup2; + b&sup2; + (c - 1)&sup2; = 0<br>अतः , <math display=\"inline\"><msup><mrow><mo>(</mo><mi mathvariant=\"bold-italic\">a</mi><mi mathvariant=\"bold-italic\">&#160;</mi><mo>-</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mn>1</mn><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup></math> <strong>= 0 , a = 1</strong><br>b&sup2; = 0 , b = 0 and&nbsp;<math display=\"inline\"><msup><mrow><mo>(</mo><mi>c</mi><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>1</mn><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup></math> = 0 , c = 1<br>इसलिए &there4; a&sup3; + b&sup3; + c&sup3; = 2</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "17",
                    question_en: "<p>13. A boatman goes 2 km against the current of the stream in 1 hour and goes 1 km along with the current in 10 minutes. How long will it take to go 6 km in stationary water ?</p>",
                    question_hi: "<p>13. एक नाविक धारा के प्रतिकूल 1 घंटे में 2 km जाता है और धारा के अनुकूल 10 मिनट में 1 km जाता है। उसे स्थिर जल में 6 km जाने में कितना समय लगेगा ?</p>",
                    options_en: ["<p>1 hr 30 min</p>", "<p>1 hr 15 min</p>", 
                                "<p>2 hr 30 min</p>", "<p>1 hr 45 min</p>"],
                    options_hi: ["<p>1 घंटा 30 मिनट</p>", "<p>1 घंटा 15 मिनट</p>",
                                "<p>2 घंटे 30 मिनट</p>", "<p>1 घंटा 45 मिनट</p>"],
                    solution_en: "<p>13.(a) Let speed of boat in stationary water = x <br>And speed of stream = y <br>According to question , <br><math display=\"inline\"><mo>&#8658;</mo><mi>&#160;</mi><mfrac><mrow><mn>2</mn></mrow><mrow><mi>x</mi><mo>-</mo><mi>&#160;</mi><mi>y</mi><mi>&#160;</mi></mrow></mfrac></math> = 60 &hellip; (i)<br><math display=\"inline\"><mo>&#8658;</mo><mfrac><mrow><mn>1</mn></mrow><mrow><mi>x</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>y</mi><mi>&#160;</mi></mrow></mfrac></math> = 10 &hellip;..(ii)<br><math display=\"inline\"><mo>&#8658;</mo></math> x - y =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>30</mn></mfrac></math>&hellip;..(iii)<br><math display=\"inline\"><mo>&#8658;</mo></math> x + y =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>10</mn></mfrac></math> &hellip;. (iv)<br>Adding eqn (iii) and (iv)<br>2x = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>30</mn></mrow></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>10</mn></mfrac></math><br>x = <math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>30</mn></mrow></mfrac></math><br>Now , time taking to go 6 km in stationary water = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>6</mn><mo>&#215;</mo><mn>30</mn></mrow><mn>2</mn></mfrac></math> = 90min = 1hr 30min</p>",
                    solution_hi: "<p>13.(a) माना स्थिर पानी में नाव की गति = x <br>और धारा की गति = y <br>प्रश्न के अनुसार, <br><math display=\"inline\"><mo>&#8658;</mo><mi>&#160;</mi><mfrac><mrow><mn>2</mn></mrow><mrow><mi>x</mi><mo>-</mo><mi>&#160;</mi><mi>y</mi><mi>&#160;</mi></mrow></mfrac></math> = 60 &hellip; (i)<br><math display=\"inline\"><mo>&#8658;</mo><mfrac><mrow><mn>1</mn></mrow><mrow><mi>x</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>y</mi><mi>&#160;</mi></mrow></mfrac></math> = 10 &hellip;..(ii)<br><math display=\"inline\"><mo>&#8658;</mo></math> x - y = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>30</mn></mfrac></math>&hellip;..(iii)<br><math display=\"inline\"><mo>&#8658;</mo></math> x + y = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>10</mn></mfrac></math> &hellip;. (iv)<br>समीकरण (iii) और (iv) जोड़ने पर<br>2x = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>30</mn></mrow></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>10</mn></mfrac></math><br>x = <math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>30</mn></mrow></mfrac></math><br>अब, स्थिर पानी में 6 किमी चलने में लगने वाला समय = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>6</mn><mo>&#215;</mo><mn>30</mn></mrow><mn>2</mn></mfrac></math> = 90min = 1hr 30min</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "17",
                    question_en: "<p>14. Find the values of x, y and z, so as to satisfy the equations given below:<br>5x &ndash; 3y + 7z = 22; 3x &ndash; 5y &ndash; 2z = &ndash; 46; 2x &ndash; 2y + 5z = 24</p>",
                    question_hi: "<p>14. x, y और z के वे मान ज्ञात करें, जिससे नीचे दिए गए समीकरणों को संतुष्ट किया जा सके:<br>5x &ndash; 3y + 7z = 22; 3x &ndash; 5y &ndash; 2z = &ndash; 46; 2x &ndash; 2y + 5z = 24</p>",
                    options_en: ["<p>x = &ndash; 5, y = 3, z = &ndash; 8</p>", "<p>x = 5, y = &ndash; 8, z = 3</p>", 
                                "<p>x = &ndash; 5, y = 3, z = 8</p>", "<p>x = &ndash; 5, y = &ndash; 3, z = 8</p>"],
                    options_hi: ["<p>x = &ndash; 5, y = 3, z = &ndash; 8</p>", "<p>x = 5, y = &ndash; 8, z = 3</p>",
                                "<p>x = &ndash; 5, y = 3, z = 8</p>", "<p>x = &ndash; 5, y = &ndash; 3, z = 8</p>"],
                    solution_en: "<p>14.(c) <strong>Given:</strong><br>5x &ndash; 3y + 7z = 22; 3x &ndash; 5y &ndash; 2z = &ndash; 46; 2x &ndash; 2y + 5z = 24<br>By checking option one by one, option (c) satisfy<br>5x &ndash; 3y + 7z = 22<br>Put x = &ndash; 5, y = 3, z = 8<br>-25 - 9 + 56 = 22</p>",
                    solution_hi: "<p>14.(c) <strong>दिया गया है:</strong><br>5x &ndash; 3y + 7z = 22; 3x &ndash; 5y &ndash; 2z = &ndash; 46; 2x &ndash; 2y + 5z = 24<br>एक-एक करके विकल्प की जाँच करने पर विकल्प (c) संतुष्ट होता है। <br>5x &ndash; 3y + 7z = 22<br>x = &ndash; 5, y = 3, z = 8 रखें <br>-25 - 9 + 56 = 22</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "17",
                    question_en: "<p>15. In 2010, a company\'s total expenditure was categorised under different expense heads, and their percentage distribution is illustrated in the bar graph given below.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730963555346.png\" alt=\"rId15\" width=\"299\" height=\"265\"> <br>The total amount of expenditure of the company is ______ times that of expenditure on R &amp; D.</p>",
                    question_hi: "<p>15. 2010 में एक कंपनी के कुल व्यय को विभिन्न व्यय मदों के तहत वर्गीकृत किया गया था और उनका प्रतिशत बंटन नीचे दिए गए बार- ग्राफ में दर्शाया गया है।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730963555469.png\" alt=\"rId16\" width=\"297\" height=\"248\"> <br>कंपनी के व्यय की कुल राशि आर एंड डी पर व्यय की _______गुना है।</p>",
                    options_en: ["<p>20</p>", "<p>38</p>", 
                                "<p>32</p>", "<p>28</p>"],
                    options_hi: ["<p>20</p>", "<p>38</p>",
                                "<p>32</p>", "<p>28</p>"],
                    solution_en: "<p>15.(a) The total amount of expenditure of the company = (20 + 12.5 + 15 + 10 + 5 + 20 + 17.5)% = 100%<br>expenditure on R &amp; D = 5%<br>So ,The total amount of expenditure of the company is 20 times that of expenditure on R &amp; D.</p>",
                    solution_hi: "<p>15.(a) कंपनी के व्यय की कुल राशि = (20 + 12.5 + 15 + 10 + 5 + 20 + 17.5)% = 100%<br>R &amp; D पर व्यय = 5%<br>तो, कंपनी के व्यय की कुल राशि अनुसंधान एवं विकास पर व्यय का 20 गुना है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "16",
                    section: "17",
                    question_en: "<p>16. The circumference of the base of a conical tent of height 8 m is 32 &pi;m. Find its curved surface area.</p>",
                    question_hi: "<p>16. 8 m ऊँचाई वाले एक शंक्वाकार तम्बू के आधार की परिधि 32 &pi;m है। इसका वक्र पृष्ठीय क्षेत्रफल ज्ञात कीजिए।</p>",
                    options_en: ["<p>164<math display=\"inline\"><msqrt><mn>5</mn></msqrt></math> &pi;m&sup2;</p>", "<p>162<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math> &pi;m&sup2;</p>", 
                                "<p>1<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math>23 &pi;m&sup2;</p>", "<p>128<math display=\"inline\"><msqrt><mn>5</mn></msqrt></math> &pi;m&sup2;</p>"],
                    options_hi: ["<p>164<math display=\"inline\"><msqrt><mn>5</mn></msqrt></math> &pi;m&sup2;</p>", "<p>162<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math> &pi;m&sup2;</p>",
                                "<p>1<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math>23 &pi;m&sup2;</p>", "<p>128<math display=\"inline\"><msqrt><mn>5</mn></msqrt></math> &pi;m&sup2;</p>"],
                    solution_en: "<p>16.(d) Given height = 8m<br>Circumference of the base of a conical tent = 2&pi;r = 32 &pi;m&nbsp;<br>&there4; r = 16m<br>Slant height <math display=\"inline\"><mi>l</mi><mi>&#160;</mi></math>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mi>r</mi><mn>2</mn></msup><mo>+</mo><msup><mi>h</mi><mn>2</mn></msup></msqrt><mo>=</mo><msqrt><mn>256</mn><mo>+</mo><mn>64</mn></msqrt><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><msqrt><mn>320</mn></msqrt><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>8</mn><msqrt><mn>5</mn></msqrt></math>m<br>Now, curved surface area(&pi;r<math display=\"inline\"><mi>l</mi></math>) = &pi; &times; 16 &times; 8<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>5</mn></msqrt></math> = 128<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>5</mn></msqrt></math> &pi;m&sup2;</p>",
                    solution_hi: "<p>16.(d) <br>दी गई ऊंचाई = 8 मी.<br>शंक्वाकार तंबू के आधार की परिधि = 2&pi;r =32 &pi;m <br>&there4; r = 16 मी.<br>तिरछी ऊँचाई (<math display=\"inline\"><mi>l</mi></math>) = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mi>r</mi><mn>2</mn></msup><mo>+</mo><msup><mi>h</mi><mn>2</mn></msup></msqrt><mo>=</mo><msqrt><mn>256</mn><mo>+</mo><mn>64</mn></msqrt><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><msqrt><mn>320</mn></msqrt><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>8</mn><msqrt><mn>5</mn></msqrt></math>m<br>अब, वक्र पृष्ठ क्षेत्रफल(&pi;rl) = &pi; &times; 16 &times; 8<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>5</mn></msqrt></math> = 128<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>5</mn></msqrt></math> &pi;m&sup2;</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "17",
                    section: "17",
                    question_en: "<p>17. Find the exact value of sin 150&deg;.</p>",
                    question_hi: "<p>17. sin 150&deg; का सटीक मान ज्ञात कीजिए।</p>",
                    options_en: ["<p>1.5</p>", "<p>0.5</p>", 
                                "<p>0.75</p>", "<p>- 0.5</p>"],
                    options_hi: ["<p>1.5</p>", "<p>0.5</p>",
                                "<p>0.75</p>", "<p>- 0.5</p>"],
                    solution_en: "<p>17.(b) Sin 150&deg; = Sin (180 - 30&deg;)<br>Sin 30&deg; = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> = 0.5</p>",
                    solution_hi: "<p>17.(b) Sin 150&deg; = Sin (180 - 30&deg;)<br>Sin 30&deg; = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> = 0.5</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "18",
                    section: "17",
                    question_en: "<p>18. The mean proportional of 2 and 32 is _____.</p>",
                    question_hi: "<p>18. 2 और 32 का माध्&zwj;यानुपाती_____ है।</p>",
                    options_en: ["<p>16</p>", "<p>8</p>", 
                                "<p>12</p>", "<p>6</p>"],
                    options_hi: ["<p>16</p>", "<p>8</p>",
                                "<p>12</p>", "<p>6</p>"],
                    solution_en: "<p>18.(b) mean proportional of 2 and 32 = <math display=\"inline\"><msqrt><mn>2</mn><mi>&#160;</mi><mo>&#215;</mo><mn>32</mn></msqrt></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>64</mn></msqrt></math> = 8</p>",
                    solution_hi: "<p>18.(b) 2 और 32 का माध्य आनुपातिक =&nbsp;<math display=\"inline\"><msqrt><mn>2</mn><mi>&#160;</mi><mo>&#215;</mo><mn>32</mn></msqrt></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>64</mn></msqrt></math> = 8</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "19",
                    section: "17",
                    question_en: "19. The profit gained on selling a certain product is 320% of the cost. If the cost increases by 10% but the selling price remains constant, then what percentage of the selling price is the profit (correct to one decimal place)?",
                    question_hi: "19. एक निश्चित उत्पाद को बेचने पर प्राप्त लाभ लागत का 320% है। यदि लागत में 10% की वृद्धि होती है, लेकिन विक्रय मूल्य स्थिर रहता है, तो विक्रय मूल्य का कितना प्रतिशत लाभ (एक दशमलव स्थान तक सही) होगा?",
                    options_en: [" 70.2% ", " 73.8% ", 
                                " 75.7% ", " 63.8%"],
                    options_hi: [" 70.2% ", " 73.8% ",
                                " 75.7% ", " 63.8%"],
                    solution_en: "19.(b) Let cost price = 100 <br />   Then selling price = 420<br />    After  increasing 10 % , cost price = 110<br />    But  selling price remain same <br />    Then , profit = 310<br />     Profit percent on selling price =<math display=\"inline\"><mfrac><mrow><mn>310</mn></mrow><mrow><mn>420</mn></mrow></mfrac></math> × 100 = 73.8%",
                    solution_hi: "19.(b) माना क्रय मूल्य = 100 <br />   तो विक्रय मूल्य = 420<br />    10% वृद्धि के बाद लागत मूल्य = 110<br />    लेकिन विक्रय मूल्य वही रहता है <br />    तो, लाभ = 310<br />     विक्रय मूल्य पर लाभ प्रतिशत = <math display=\"inline\"><mfrac><mrow><mn>310</mn></mrow><mrow><mn>420</mn></mrow></mfrac></math> × 100 = 73.8%",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "20",
                    section: "17",
                    question_en: "20. A circular arc whose radius is 12 cm makes an angle of 30° at the centre. Find the perimeter (in cm) of the sector formed. (Use π = 3.14)",
                    question_hi: "20. एक वृत्ताकार चाप जिसकी त्रिज्या 12 cm है, केंद्र पर 30° का कोण बनाता है। बने हुए त्रिज्यखंड का परिमाप (cm में) ज्ञात कीजिए। (π = 3.14 का उपयोग कीजिए)",
                    options_en: [" 32.38 ", " 30.28 ", 
                                " 28.64  ", " 26.24"],
                    options_hi: [" 32.38 ", " 30.28 ",
                                " 28.64  ", " 26.24"],
                    solution_en: "20.(b) Length of arc = <math display=\"inline\"><mfrac><mrow><mi>θ</mi></mrow><mrow><mn>360</mn></mrow></mfrac></math> × 2πr<br />= <math display=\"inline\"><mfrac><mrow><mn>30</mn></mrow><mrow><mn>360</mn></mrow></mfrac></math> × 2 × 3.14 × 12 <br />= 6.28cm<br />Perimeter of the sector = 6.28 + 12 + 12 = 30.28cm",
                    solution_hi: "20.(b) चाप की लंबाई = <math display=\"inline\"><mfrac><mrow><mi>θ</mi></mrow><mrow><mn>360</mn></mrow></mfrac></math> × 2πr<br />= <math display=\"inline\"><mfrac><mrow><mn>30</mn></mrow><mrow><mn>360</mn></mrow></mfrac></math> × 2 × 3.14 × 12 <br />= 6.28cm<br />त्रिज्यखंड का परिमाप  = 6.28 + 12 + 12 = 30.28cm",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "21",
                    section: "17",
                    question_en: "<p>21. A merchant mixes two varieties of sugar of 5 kg and 3 kg, which costs him ₹4 and ₹6 per kg, respectively. What is the cost price (in ₹) of the mixture per kg?</p>",
                    question_hi: "<p>21. एक व्यापारी क्रमशः ₹4 और ₹6 प्रति kg मूल्&zwj;य वाली दो प्रकार की चीनी की 5 kg और 3 kg मात्रा मिलाता है। प्रति kg मिश्रण का लागत मूल्य (₹ में) ज्ञात करें।</p>",
                    options_en: ["<p>4.35</p>", "<p>4.45</p>", 
                                "<p>4.75</p>", "<p>4.21</p>"],
                    options_hi: ["<p>4.35</p>", "<p>4.45</p>",
                                "<p>4.75</p>", "<p>4.21</p>"],
                    solution_en: "<p>21.(c) By using alligation method <br>Let cost price of mixture = ₹x<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; ₹4&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;₹6 <br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; x<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;5kg&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 3kg<br>x = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>4</mn><mo>&#215;</mo><mn>5</mn><mo>+</mo><mn>6</mn><mo>&#215;</mo><mn>3</mn></mrow><mrow><mn>5</mn><mo>+</mo><mn>3</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>38</mn><mn>8</mn></mfrac></math> = ₹4.75</p>",
                    solution_hi: "<p>21.(c) एलीगेशन विधि का प्रयोग करके <br>मान लीजिए मिश्रण का लागत मूल्य = ₹x<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; ₹4&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;₹6 <br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; x<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;5kg&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 3kg<br>x = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>4</mn><mo>&#215;</mo><mn>5</mn><mo>+</mo><mn>6</mn><mo>&#215;</mo><mn>3</mn></mrow><mrow><mn>5</mn><mo>+</mo><mn>3</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>38</mn><mn>8</mn></mfrac></math> = ₹4.75</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "22",
                    section: "17",
                    question_en: "<p>22. The proportion of Suresh\'s spending to saving is 3 : 1. His salary rises by 25%. What percentage of his expenditure will increase if his saving grows by 10% ?</p>",
                    question_hi: "<p>22. सुरेश के खर्च और बचत का अनुपात 3 : 1 है। उसका वेतन 25% बढ़ जाता है। यदि उसकी बचत 10% बढ़ जाए तो उसका खर्च किस अनुपात में बढ़ेगा ?</p>",
                    options_en: ["<p>40%</p>", "<p>20%</p>", 
                                "<p>10%</p>", "<p>30%</p>"],
                    options_hi: ["<p>40%</p>", "<p>20%</p>",
                                "<p>10%</p>", "<p>30%</p>"],
                    solution_en: "<p>22.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730963555610.png\" alt=\"rId17\" width=\"241\" height=\"79\"><br>Now , percentage increase in expenditure = <math display=\"inline\"><mfrac><mrow><mn>3</mn><mo>.</mo><mn>9</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>3</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> &times; 100 = 30% </p>",
                    solution_hi: "<p>22.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730963555745.png\" alt=\"rId18\" width=\"215\" height=\"86\"><br>अब, व्यय में प्रतिशत वृद्धि = <math display=\"inline\"><mfrac><mrow><mn>3</mn><mo>.</mo><mn>9</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>3</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> &times; 100 = 30%</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "23",
                    section: "17",
                    question_en: "<p>23. The table below shows the production of three types of cars (A, B and C) manufactured (in thousands) by an automobile company over the years.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730963555870.png\" alt=\"rId19\" width=\"298\" height=\"100\"> <br>The average number of A-type cars produced by the company during 2012 to 2014 is what percent (rounded off to the 1 decimal place) less than the combined average number of B-type and C-type cars produced by the company from 2013 to 2015?</p>",
                    question_hi: "<p>23. नीचे दी गई तालिका पिछले कुछ वर्षों में एक ऑटोमोबाइल कंपनी द्वारा निर्मित तीन प्रकार की कारों (A, B और C) के उत्पादन (हजार में) को दर्शाती है।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730963556022.png\" alt=\"rId20\" width=\"300\" height=\"107\"> <br>2012 से 2014 के दौरान कंपनी द्वारा उत्पादित A-प्रकार की कारों की औसत संख्या, 2013 से 2015 तक कंपनी द्वारा उत्पादित B-प्रकार और C-प्रकार की कारों की संयुक्त औसत संख्या से कितने प्रतिशत (1दशमलव स्थान तक पूर्णांकित) कम है ?</p>",
                    options_en: ["<p>36.6%</p>", "<p>26.6%</p>", 
                                "<p>28.4%</p>", "<p>25.5%</p>"],
                    options_hi: ["<p>36.6%</p>", "<p>26.6%</p>",
                                "<p>28.4%</p>", "<p>25.5%</p>"],
                    solution_en: "<p>23.(b) The average number of A-type cars produced by the company during 2012 to 2014<br>= <math display=\"inline\"><mfrac><mrow><mn>1750</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>1800</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>2950</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>6500</mn><mrow><mn>3</mn><mo>&#160;</mo></mrow></mfrac></math> = 2166.7<br>combined average number of B-type and C-type cars produced by the company from 2013 to 2015<br>= <math display=\"inline\"><mfrac><mrow><mn>2950</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>3020</mn><mi>&#160;</mi><mo>+</mo><mn>3240</mn><mi>&#160;</mi><mo>+</mo><mn>2070</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>3150</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>3290</mn></mrow><mrow><mn>6</mn></mrow></mfrac></math> = 2953.4<br>Required percentage = <math display=\"inline\"><mfrac><mrow><mn>2953</mn><mo>.</mo><mn>4</mn><mi>&#160;</mi><mo>-</mo><mn>2166</mn><mo>.</mo><mn>7</mn><mi>&#160;</mi></mrow><mrow><mn>2953</mn><mo>.</mo><mn>4</mn></mrow></mfrac></math> &times; 100<br>= <math display=\"inline\"><mfrac><mrow><mn>786</mn><mo>.</mo><mi>&#160;</mi><mn>7</mn></mrow><mrow><mn>2953</mn><mo>.</mo><mn>4</mn></mrow></mfrac></math> &times; 100 = 26.6</p>",
                    solution_hi: "<p>23.(b) 2012 से 2014 के दौरान कंपनी द्वारा उत्पादित A-प्रकार की कारों की औसत संख्या<br>= <math display=\"inline\"><mfrac><mrow><mn>1750</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>1800</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>2950</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>6500</mn><mrow><mn>3</mn><mo>&#160;</mo></mrow></mfrac></math> = 2166.7<br>2013 से 2015 तक कंपनी द्वारा उत्पादित B-टाइप और C-टाइप कारों की संयुक्त औसत संख्या<br>= <math display=\"inline\"><mfrac><mrow><mn>2950</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>3020</mn><mi>&#160;</mi><mo>+</mo><mn>3240</mn><mi>&#160;</mi><mo>+</mo><mn>2070</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>3150</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>3290</mn></mrow><mrow><mn>6</mn></mrow></mfrac></math> = 2953.4<br>आवश्यक प्रतिशत = <math display=\"inline\"><mfrac><mrow><mn>2953</mn><mo>.</mo><mn>4</mn><mi>&#160;</mi><mo>-</mo><mn>2166</mn><mo>.</mo><mn>7</mn><mi>&#160;</mi></mrow><mrow><mn>2953</mn><mo>.</mo><mn>4</mn></mrow></mfrac></math> &times; 100 <br>= <math display=\"inline\"><mfrac><mrow><mn>786</mn><mo>.</mo><mi>&#160;</mi><mn>7</mn></mrow><mrow><mn>2953</mn><mo>.</mo><mn>4</mn></mrow></mfrac></math> &times; 100 = 26.6</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "24",
                    section: "17",
                    question_en: "<p>24. If 7 cosA = 6, then the numerical value of <math display=\"inline\"><mfrac><mrow><mi>c</mi><mi>o</mi><mi>s</mi><mi>e</mi><mi>c</mi><mi>A</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>c</mi><mi>o</mi><mi>t</mi><mi>A</mi></mrow><mrow><mi>c</mi><mi>o</mi><mi>s</mi><mi>e</mi><mi>c</mi><mi>A</mi><mo>-</mo><mi>c</mi><mi>o</mi><mi>t</mi><mi>A</mi></mrow></mfrac></math> is:</p>",
                    question_hi: "<p>24. यदि 7 cosA = 6 है, तो <math display=\"inline\"><mfrac><mrow><mi>c</mi><mi>o</mi><mi>s</mi><mi>e</mi><mi>c</mi><mi>A</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>c</mi><mi>o</mi><mi>t</mi><mi>A</mi></mrow><mrow><mi>c</mi><mi>o</mi><mi>s</mi><mi>e</mi><mi>c</mi><mi>A</mi><mo>-</mo><mi>c</mi><mi>o</mi><mi>t</mi><mi>A</mi></mrow></mfrac></math> का संख्&zwj;यात्&zwj;मक मान कितना होगा ?</p>",
                    options_en: ["<p>1</p>", "<p>0</p>", 
                                "<p>13</p>", "<p>-13</p>"],
                    options_hi: ["<p>1</p>", "<p>0</p>",
                                "<p>13</p>", "<p>-13</p>"],
                    solution_en: "<p>24.(c) <strong>Given </strong>, 7 cosA = 6 <br>Cos A = <math display=\"inline\"><mfrac><mrow><mn>6</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math><br>Here B = 6 , H = 7 , P = <math display=\"inline\"><msqrt><mn>49</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>36</mn></msqrt></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>13</mn></msqrt></math><br>Now , <math display=\"inline\"><mfrac><mrow><mi>c</mi><mi>o</mi><mi>s</mi><mi>e</mi><mi>c</mi><mi>A</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>c</mi><mi>o</mi><mi>t</mi><mi>A</mi></mrow><mrow><mi>c</mi><mi>o</mi><mi>s</mi><mi>e</mi><mi>c</mi><mi>A</mi><mo>-</mo><mi>c</mi><mi>o</mi><mi>t</mi><mi>A</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mstyle displaystyle=\"true\"><mfrac><mn>7</mn><msqrt><mn>13</mn></msqrt></mfrac></mstyle><mo>+</mo><mstyle displaystyle=\"true\"><mfrac><mn>6</mn><msqrt><mn>13</mn></msqrt></mfrac></mstyle></mrow><mrow><mstyle displaystyle=\"true\"><mfrac><mn>7</mn><msqrt><mn>13</mn></msqrt></mfrac></mstyle><mo>-</mo><mstyle displaystyle=\"true\"><mfrac><mn>6</mn><msqrt><mn>13</mn></msqrt></mfrac></mstyle></mrow></mfrac><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mfrac><mn>13</mn><mn>1</mn></mfrac></math> = 13</p>",
                    solution_hi: "<p>24.(c) <strong>दिया गया है, </strong>7 cosA = 6 <br>Cos A = <math display=\"inline\"><mfrac><mrow><mn>6</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math><br>यहां , B = 6 , H = 7 , P = <math display=\"inline\"><msqrt><mn>49</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>36</mn></msqrt></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>13</mn></msqrt></math><br>अब, <math display=\"inline\"><mfrac><mrow><mi>c</mi><mi>o</mi><mi>s</mi><mi>e</mi><mi>c</mi><mi>A</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>c</mi><mi>o</mi><mi>t</mi><mi>A</mi></mrow><mrow><mi>c</mi><mi>o</mi><mi>s</mi><mi>e</mi><mi>c</mi><mi>A</mi><mo>-</mo><mi>c</mi><mi>o</mi><mi>t</mi><mi>A</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mstyle displaystyle=\"true\"><mfrac><mn>7</mn><msqrt><mn>13</mn></msqrt></mfrac></mstyle><mo>+</mo><mstyle displaystyle=\"true\"><mfrac><mn>6</mn><msqrt><mn>13</mn></msqrt></mfrac></mstyle></mrow><mrow><mstyle displaystyle=\"true\"><mfrac><mn>7</mn><msqrt><mn>13</mn></msqrt></mfrac></mstyle><mo>-</mo><mstyle displaystyle=\"true\"><mfrac><mn>6</mn><msqrt><mn>13</mn></msqrt></mfrac></mstyle></mrow></mfrac><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mfrac><mn>13</mn><mn>1</mn></mfrac></math> = 13</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "25",
                    section: "misc",
                    question_en: "<p>25. If the sectorial angle with radius 9.6 units is 75&deg;, what is the length of the arc ?</p>",
                    question_hi: "<p>25. यदि 9.6 इकाई त्रिज्या वाला त्रिज्यखंडीय कोण 75&deg; है, तो चाप की लंबाई कितनी है ?</p>",
                    options_en: ["<p>2&pi;</p>", "<p>4&pi;</p>", 
                                "<p>&pi;</p>", "<p>3&pi;</p>"],
                    options_hi: ["<p>2&pi;</p>", "<p>4&pi;</p>",
                                "<p>&pi;</p>", "<p>3&pi;</p>"],
                    solution_en: "<p>25.(b) Length of arc = <math display=\"inline\"><mfrac><mrow><mi>&#952;</mi></mrow><mrow><mn>360</mn></mrow></mfrac></math> &times; 2&pi;r <br>= <math display=\"inline\"><mfrac><mrow><mn>75</mn></mrow><mrow><mn>360</mn></mrow></mfrac></math> &times; 2&pi; &times; 9.6<br>= 4&pi;</p>",
                    solution_hi: "<p>25.(b) चाप की लंबाई = <math display=\"inline\"><mfrac><mrow><mi>&#952;</mi></mrow><mrow><mn>360</mn></mrow></mfrac></math> &times; 2&pi;r <br>= <math display=\"inline\"><mfrac><mrow><mn>75</mn></mrow><mrow><mn>360</mn></mrow></mfrac></math> &times; 2&pi; &times; 9.6<br>= 4&pi;</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>