<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #7209b7;
            --secondary: #4361ee;
            --accent: #f72585;
            --success: #06d6a0;
            --warning: #ffd166;
            --danger: #ef476f;
            --light: #f8f9fa;
            --dark: #212529;
        }
        body {
            background-color: #f0f2f5;
            font-size: 16px;
            padding-bottom: 60px; /* Space for fixed navigation */
        }
        .navbar {
            background: linear-gradient(135deg, var(--primary), var(--secondary)) !important;
            box-shadow: 0 2px 10px rgba(0,0,0,.1);
            padding: 0.5rem 1rem;
        }
        .navbar-brand {
            font-size: 1.1rem;
            margin-right: 0;
        }
        .navbar .container-fluid {
            justify-content: space-between;
        }
        .controls-group {
            display: flex;
            align-items: center;
            flex-wrap: nowrap;
        }
        .timer-display {
            background: linear-gradient(135deg, #4361ee, #3a0ca3);
            color: white;
            border-radius: 50px;
            box-shadow: 0 3px 8px rgba(0,0,0,.1);
            padding: 0.25rem 0.75rem;
            font-size: 0.875rem;
            white-space: nowrap;
        }
        .nav-btn {
            padding: 0.25rem 0.75rem;
            font-size: 0.875rem;
            margin-left: 0.25rem;
        }
        .question-nav {
            position: fixed;
            top: 56px;
            right: 0;
            height: calc(100vh - 56px);
            width: 280px;
            transform: translateX(280px);
            transition: transform .3s;
            z-index: 100;
            background: #fff;
            box-shadow: -5px 0 15px rgba(0,0,0,.1);
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .main-content {
            transition: margin .3s;
            min-height: calc(100vh - 116px);
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.5;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(32px, 1fr));
            gap: 6px;
        }
        .q-box {
            width: 35px;
            height: 35px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 5px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid #dee2e6;
            background: #fff;
            transition: all .2s;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: 0 3px 5px rgba(0,0,0,.1);
        }
        .q-box.attempted {
            background-color: var(--secondary);
            color: white;
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,.15);
        }
        .card {
            border-radius: 12px;
            overflow: hidden;
            border: none;
            box-shadow: 0 4px 12px rgba(0,0,0,.08);
        }
        .option-label {
            border-radius: 8px;
            transition: all .2s;
            border: 1px solid #dee2e6;
            padding: 0.75rem 1rem;
            font-size: 1rem;
        }
        .option-label:hover {
            background-color: #f8f9fa;
            transform: translateY(-2px);
            box-shadow: 0 3px 8px rgba(0,0,0,.05);
        }
        .form-check-input:checked + .option-label {
            border-color: var(--primary);
            background-color: rgba(114,9,183,.05);
        }
        .btn-primary {
            background-color: var(--primary);
            border-color: var(--primary);
        }
        .btn-outline-primary {
            color: var(--primary);
            border-color: var(--primary);
        }
        .btn-outline-primary:hover {
            background-color: var(--primary);
            color: white;
        }
        /* Bottom fixed navigation for mobile */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: white;
            box-shadow: 0 -2px 10px rgba(0,0,0,.1);
            padding: 10px 15px;
            display: flex;
            justify-content: space-between;
            z-index: 90;
        }
        @media (min-width: 992px) {
            .main-content.with-nav {
                margin-right: 280px;
            }
            .question-nav {
                transform: translateX(0);
            }
            .nav-toggle-btn {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(30px, 1fr));
            }
            .option-label {
                padding: 0.625rem 0.875rem;
                font-size: 0.95rem;
            }
            .question-text {
                font-size: 1rem;
            }
            .navbar-brand {
                font-size: 1rem;
            }
            .timer-display {
                font-size: 0.8125rem;
                padding: 0.2rem 0.6rem;
            }
            .nav-btn {
                padding: 0.2rem 0.6rem;
                font-size: 0.8125rem;
            }
            .container {
                padding-left: 10px;
                padding-right: 10px;
            }
        }
    </style>
</head>
<body>
    <!-- Header navbar -->
    <nav class="navbar navbar-dark sticky-top">
        <div class="container-fluid px-2">
            <a class="navbar-brand fw-bold" href="#"><i class="fas fa-graduation-cap me-1"></i></a>
            <div class="controls-group">
                <!-- Timer -->
                <div class="timer-display me-1 fw-bold" id="timer">
                    <i class="far fa-clock me-1"></i><span id="timer-display">90:00</span>
                </div>
                <!-- Submit button -->
                <button class="btn btn-light btn-sm nav-btn me-1" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Language toggle -->
                <button class="btn btn-light btn-sm nav-btn me-1" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="btn btn-light btn-sm nav-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </nav>
    <!-- Main content -->
    <div class="main-content" id="main-content">
        <div class="container py-3">
            <!-- Question counter -->
            <div class="fw-bold text-center mb-3" id="question-counter">Question 1 of 25</div>
            <!-- Questions will be displayed here -->
            <div id="questions-container"></div>
        </div>
    </div>
    <!-- Bottom navigation for mobile -->
    <div class="bottom-nav d-lg-none">
        <button class="btn btn-outline-primary px-3" onclick="prevQuestion()">
            <i class="fas fa-chevron-left me-1"></i> Previous
        </button>
        <button class="btn btn-outline-primary px-3" onclick="nextQuestion()">
            Next <i class="fas fa-chevron-right ms-1"></i>
        </button>
    </div>
    <!-- Question navigation sidebar -->
    <div class="question-nav" id="question-nav">
        <div class="p-3">
            <div class="d-flex justify-content-between align-items-center mb-3">
                <h5 class="m-0">Questions</h5>
                <div>
                    <span class="badge bg-secondary me-1" id="attempted-count">0</span>
                    <span class="badge bg-secondary" id="total-count">25</span>
                    <button class="btn btn-sm btn-outline-secondary ms-2 d-lg-none" onclick="toggleNav()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
            <!-- Section selector moved here -->
            <div class="mb-3">
                <label for="section-selector" class="form-label small text-muted">Jump to Section</label>
                <select class="form-select form-select-sm" id="section-selector" onchange="switchSection(this.value)">
                    <option value="all">All Sections</option>
                </select>
            </div>
            <div class="question-grid" id="question-boxes"></div>
        </div>
    </div>
    <!-- Results modal -->
    <div class="modal fade" id="results-modal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content border-0">
                <div class="modal-header border-0" style="background:linear-gradient(135deg,var(--primary),var(--secondary));color:white">
                    <h5 class="modal-title">Test Results</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body p-4">
                    <div class="row text-center g-2">
                        <div class="col-6">
                            <div class="p-3 rounded shadow-sm" style="background:linear-gradient(135deg,#4361ee,#3a0ca3);color:white">
                                <h3 id="score-value" class="mb-0 fw-bold">0</h3>
                                <div class="small">Score</div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="p-3 rounded shadow-sm" style="background:linear-gradient(135deg,#06d6a0,#1b9aaa);color:white">
                                <h3 id="correct-value" class="mb-0 fw-bold">0</h3>
                                <div class="small">Correct</div>
                            </div>
                        </div>
                        <div class="col-6 mt-2">
                            <div class="p-3 rounded shadow-sm" style="background:linear-gradient(135deg,#ef476f,#f72585);color:white">
                                <h3 id="incorrect-value" class="mb-0 fw-bold">0</h3>
                                <div class="small">Incorrect</div>
                            </div>
                        </div>
                        <div class="col-6 mt-2">
                            <div class="p-3 rounded shadow-sm" style="background:linear-gradient(135deg,#6c757d,#495057);color:white">
                                <h3 id="unattempted-value" class="mb-0 fw-bold">0</h3>
                                <div class="small">Unattempted</div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer border-0">
                    <button type="button" class="btn btn-primary" onclick="reviewTest()">Review Test</button>
                </div>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 90 * 60;
        let resultsModal;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            resultsModal = new bootstrap.Modal(document.getElementById('results-modal'));
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["6"] = {
                name: "Reasoning",
                start: 0,
                end: 23
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="6">Reasoning</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 24,
                end: 24
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "6",
                    question_en: " <p>1.</span><span style=\"font-family:Times New Roman\"> Nirmala introduced Rahul as the son of the sister of her mother. How is Nirmala related to Rahul? </span></p>",
                    question_hi: " <p>1.</span><span style=\"font-family:Baloo\"> निर्मला ने राहुल का परिचय अपनी मां की बहन के पुत्र के रूप में कराया। निर्मला का राहुल से क्‍या रिश्‍ता है? </span></p>",
                    options_en: [" <p>  Brother</span><span style=\"font-family:Times New Roman\">     </span></p>", " <p>  Brother-in-law</span></p>", 
                                " <p>  Cousin </span><span style=\"font-family:Times New Roman\">     </span></p>", " <p>  Maternal uncle </span></p>"],
                    options_hi: [" <p> भाई </span></p>", " <p> देवर</span></p>",
                                " <p> मौसेरा भाई </span></p>", " <p> मामा</span></p>"],
                    solution_en: " <p>1.(c)</span></p> <p><span style=\"font-family:Times New Roman\">According to the following family chart, Rahul is the cousin of Nirmala.</span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646807695/word/media/image29.png\"/></p>",
                    solution_hi: " <p>1.(c)</span></p> <p><span style=\"font-family:Baloo\">निम्नलिखित पारिवारिक चार्ट के अनुसार, राहुल निर्मला का मौसेरा भाई है।</span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646807695/word/media/image29.png\"/></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "6",
                    question_en: " <p>2.</span><span style=\"font-family:Times New Roman\"> Which two signs should be interchanged to balance the following equation? </span></p> <p><span style=\"font-family:Times New Roman\">646 ÷ 19 – 746 + 20 × 34 = 100 </span></p>",
                    question_hi: " <p>2.</span><span style=\"font-family:Baloo\"> निम्न समीकरण को संतुलित करने के लिए, कौन सेदो चिह्नों को परस्पर प्रतिस्थापित करना होगा? </span></p> <p><span style=\"font-family:Times New Roman\">646 ÷ 19 – 746 + 20 × 34 = 100 </span></p>",
                    options_en: [" <p>   − and × </span></p>", " <p>  × and+ </span></p>", 
                                " <p>  − and+ </span></p>", " <p>  × and ÷ </span></p>"],
                    options_hi: [" <p>   − तथा × </span></p>", " <p>  × तथा + </span></p>",
                                " <p>  − तथा + </span></p>", " <p>  × तथा ÷ </span></p>"],
                    solution_en: " <p>2.(c)</span></p> <p><span style=\"font-family:Times New Roman\">646 ÷ 19 – 746 + 20 × 34 = 100 </span></p> <p><span style=\"font-family:Times New Roman\">Apply hit and trial method and change the signs according to option (c),</span></p> <p><span style=\"font-family:Times New Roman\">646 ÷ 19 + 746 – 20 × 34 = 100 </span></p> <p><span style=\"font-family:Times New Roman\">34 + 746 - 680 = 100</span></p> <p><span style=\"font-family:Times New Roman\">780 - 680 = 100</span></p> <p><span style=\"font-family:Times New Roman\">100  =  100</span></p>",
                    solution_hi: " <p>2.(c)</span></p> <p><span style=\"font-family:Times New Roman\">646 ÷ 19 – 746 + 20 × 34 = 100 </span></p> <p><span style=\"font-family:Baloo\">हिट एंड ट्रायल विधि लागू करें और विकल्प (c) के अनुसार संकेतों को बदलें,</span></p> <p><span style=\"font-family:Times New Roman\">646 ÷ 19 + 746 – 20 × 34 = 100 </span></p> <p><span style=\"font-family:Times New Roman\">34 + 746 - 680 = 100</span></p> <p><span style=\"font-family:Times New Roman\">780 - 680 = 100</span></p> <p><span style=\"font-family:Times New Roman\">100  =  100</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "6",
                    question_en: "<p>3.<span style=\"font-family: Times New Roman;\"> Select the correct option that indicates the arrangement of the given words in a logical and meaningful order. </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">1. Prescription&nbsp; &nbsp;</span><span style=\"font-family: Times New Roman;\">2. Diagnosis </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">3. Illness&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; </span><span style=\"font-family: Times New Roman;\">4. Follow-up </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">5. Recovery</span></p>",
                    question_hi: "<p>3.<span style=\"font-family: Baloo;\"> उस विकल्प का चयन कीजिए जो दिए गए शब्दों के तार्किक और अर्थपूर्णक्रम - विन्यास को दर्शाता है। </span></p>\r\n<p><span style=\"font-family: Baloo;\">1. नुस्&zwj;खा&nbsp; &nbsp; &nbsp; </span><span style=\"font-family: Baloo;\">2. निदान </span></p>\r\n<p><span style=\"font-family: Baloo;\">3. बीमारी&nbsp; &nbsp; </span><span style=\"font-family: Baloo;\">4. फॉलो-अप (अनुवर्तन) </span></p>\r\n<p><span style=\"font-family: Baloo;\">5. स्&zwj;वास्&zwj;थ्&zwj;य-लाभ </span></p>",
                    options_en: ["<p>3-4-1-2-5</p>", "<p>1-2-5-3-4</p>", 
                                "<p>3-2-1-5-4</p>", "<p>2-3-1-4-5</p>"],
                    options_hi: ["<p>3-4-1-2-5</p>", "<p>1-2-5-3-4</p>",
                                "<p>3-2-1-5-4</p>", "<p>2-3-1-4-5</p>"],
                    solution_en: "<p>3.(c)<span style=\"font-family: Times New Roman;\"> The most logical and meaningful order of the words is - </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Illness &rarr;</span><span style=\"font-family: Times New Roman;\"> Diagnosis &rarr;&nbsp;</span><span style=\"font-family: Times New Roman;\"> Prescription &rarr;</span><span style=\"font-family: Times New Roman;\">Recovery&nbsp; &rarr;</span><span style=\"font-family: Times New Roman;\"> Follow-up </span></p>",
                    solution_hi: "<p><span style=\"font-family: Baloo;\">3. (c) शब्दों का सर्वाधिक तार्किक और अर्थपूर्ण क्रम है-</span></p>\r\n<p><span style=\"font-family: Baloo;\">बीमारी &rArr;</span><span style=\"font-family: Baloo;\"> निदान &rArr;&nbsp;</span><span style=\"font-family: Baloo;\"> नुस्&zwj;खा &rArr;</span><span style=\"font-family: Baloo;\">स्&zwj;वास्&zwj;थ्&zwj;य-लाभ &rArr;&nbsp;</span><span style=\"font-family: Baloo;\"> फॉलो-अप </span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "6",
                    question_en: " <p>4.</span><span style=\"font-family:Times New Roman\"> Four letter-clusters have been given, out of which three are alike in some manner and one is different. Select the letter-cluster that is different. </span></p>",
                    question_hi: " <p>4. </span><span style=\"font-family:Baloo\">चार अक्षर-समूह दिए गए हैं, जिनमें से तीन एक निश्चित प्रकार से समान हैं, और एक उनसे असंगत है। उस असंगत अक्षर-समूह का चयन कीजिए। </span></p>",
                    options_en: [" <p>  PVBH </span></p>", " <p>  CIOU </span></p>", 
                                " <p>  SYDJ </span></p>", " <p>  KQWC </span></p>"],
                    options_hi: [" <p>  PVBH </span></p>", " <p>  CIOU </span></p>",
                                " <p>  SYDJ </span></p>", " <p>  KQWC </span></p>"],
                    solution_en: " <p>4.(c)</span></p> <p><span style=\"font-family:Times New Roman\">Logic :- The difference between the place values of two consecutive alphabets is 6.</span></p> <p><span style=\"font-family:Times New Roman\">Eg -  PVBH </span></p> <p><span style=\"font-family:Times New Roman\">P + 6 = V, V + 6 = B, B + 6 = H</span></p> <p><span style=\"font-family:Times New Roman\">But option (c) does not follow the pattern.</span></p>",
                    solution_hi: " <p>4.(c)</span></p> <p><span style=\"font-family:Baloo\">Logic :- दो क्रमागत वर्णों के स्थानीय मानों का अंतर 6 है।</span></p> <p><span style=\"font-family:Times New Roman\">Eg -  PVBH </span></p> <p><span style=\"font-family:Times New Roman\">P + 6 = V, V + 6 = B, B + 6 = H</span></p> <p><span style=\"font-family:Baloo\">लेकिन विकल्प (c) पैटर्न का पालन नहीं करता है।</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "6",
                    question_en: " <p>5.</span><span style=\"font-family:Times New Roman\"> In a certain code language, CANDIDATE is coded as 846292417 and ACTION is coded as 481936. How will DICTATION be coded in that language? </span></p>",
                    question_hi: " <p>5. </span><span style=\"font-family:Baloo\">एक निश्चित कूट भाषा में, CANDIDATE को 846292417 के रूप मेंकूटबद्ध किया जाता है और ACTION को 481936 के रूप मेंकूटबद्ध किया जाता है। उसी भाषा में, DICTATION को किस रूप में कूटबद्ध किया जाएगा? </span></p>",
                    options_en: [" <p>  493212965 </span><span style=\"font-family:Times New Roman\">         </span></p>", " <p>  298242956 </span></p>", 
                                " <p>  493212975            </span></p>", " <p>  298141936 </span></p>"],
                    options_hi: [" <p>  493212965 </span><span style=\"font-family:Times New Roman\">        </span></p>", " <p>  298242956 </span></p>",
                                " <p>  493212975             </span></p>", " <p>  298141936 </span></p>"],
                    solution_en: " <p>5.(d)</span></p> <p><span style=\"font-family:Times New Roman\">Logic :- Here direct coding is used.</span></p> <p><span style=\"font-family:Cardo\">Ex :- C→8, A→4, N→6, D→2, I→9 and so on. So, the code for DICTATION will be 298141936.</span></p>",
                    solution_hi: " <p>5.(d)</span></p> <p><span style=\"font-family:Baloo\">Logic :- यहां डायरेक्ट कोडिंग का इस्तेमाल किया जाता है।</span></p> <p><span style=\"font-family:Arial Unicode MS\">Ex :- C→8, A→4, N→6, D→2, I→9 और इसी तरह। तो, DICTATION का कोड 298141936 होगा।</span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "6",
                    question_en: " <p>6. </span><span style=\"font-family:Times New Roman\">Select the venn diagram that best represents the relationship between the following classes.</span></p> <p><span style=\"font-family:Times New Roman\">Teachers , Graduates , Tennis players</span></p>",
                    question_hi: " <p>6. </span><span style=\"font-family:Baloo\">उस वेन आरेख का चयन करें जो निम्नलिखित वर्गों के बीच के संबंध को सर्वोत्तम रूप से दर्शाता है।</span></p> <p><span style=\"font-family:Baloo\">शिक्षक, स्नातक, टेनिस खिलाड़ी</span></p>",
                    options_en: [" <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646807695/word/media/image34.png\"/></p>", " <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646807695/word/media/image19.png\"/></p>", 
                                " <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646807695/word/media/image24.png\"/></p>", " <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646807695/word/media/image21.png\"/></p>"],
                    options_hi: [" <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646807695/word/media/image34.png\"/></p>", " <p> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646807695/word/media/image19.png\"/></p>",
                                " <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646807695/word/media/image24.png\"/></p>", " <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646807695/word/media/image21.png\"/></p>"],
                    solution_en: " <p>6.(c)</span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646807695/word/media/image15.png\"/></p>",
                    solution_hi: " <p>6.(c)</span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646807695/word/media/image15.png\"/></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "6",
                    question_en: " <p>7. </span><span style=\"font-family:Times New Roman\"> Three different positions of the same dice are shown. Select the number that will be the face opposite to the face having 6.</span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646807695/word/media/image16.png\"/></p>",
                    question_hi: " <p>7. </span><span style=\"font-family:Baloo\">एक ही पासे की तीन अलग-अलग स्थितियों को दिखाया गया है। उस संख्या का चयन करें जो 6 वाले फलक के विपरीत फलक होगा।</span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646807695/word/media/image16.png\"/></p>",
                    options_en: [" <p> 2</span></p>", " <p> 4</span></p>", 
                                " <p> 3</span></p>", " <p> 1</span></p>"],
                    options_hi: [" <p> 2</span></p>", " <p> 4</span></p>",
                                " <p> 3</span></p>", " <p> 1</span></p>"],
                    solution_en: " <p>7.(a)</span></p> <p><span style=\"font-family:Times New Roman\">In the 2nd and 3rd cube, 4 and 5 are common. So 2 will be on the opposite side of 6. </span></p>",
                    solution_hi: " <p>7.(a)</span></p> <p><span style=\"font-family:Baloo\">दूसरे और तीसरे घन में, 4 और 5 उभयनिष्ठ हैं। अतः 2, 6 के विपरीत दिशा में होगा।</span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "6",
                    question_en: " <p>8. </span><span style=\"font-family:Times New Roman\">Select the figure from among the given options that can replace the question mark (?) in the following series.</span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646807695/word/media/image3.png\"/></p>",
                    question_hi: " <p>8.</span><span style=\"font-family:Baloo\"> दिए गए विकल्पों में से उस आकृति का चयन करें जो निम्नलिखित श्रृंखला में प्रश्नवाचक चिन्ह (?) को प्रतिस्थापित कर सकता है।</span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646807695/word/media/image3.png\"/></p>",
                    options_en: [" <p>  </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646807695/word/media/image22.png\"/></p>", " <p> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646807695/word/media/image10.png\"/></p>", 
                                " <p> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646807695/word/media/image17.png\"/></p>", " <p> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646807695/word/media/image13.png\"/></p>"],
                    options_hi: [" <p>  </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646807695/word/media/image22.png\"/></p>", " <p> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646807695/word/media/image10.png\"/></p>",
                                " <p> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646807695/word/media/image17.png\"/></p>", " <p> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646807695/word/media/image13.png\"/></p>"],
                    solution_en: " <p>8.(b)</span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646807695/word/media/image14.png\"/></p>",
                    solution_hi: " <p>8.(b)</span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646807695/word/media/image14.png\"/></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "6",
                    question_en: " <p>9.</span><span style=\"font-family:Times New Roman\"> Select the option that is related to the third word in the same way as the second word is related to the first word. </span></p> <p><span style=\"font-family:Times New Roman\">Galvanometer : Electric current : : Graphometer : ? </span></p>",
                    question_hi: " <p>9.</span><span style=\"font-family:Baloo\"> उस विकल्प का चयन करें, जो तीसरे शब्द से ठीक उसी प्रकार संबंधित है, जिस प्रकार दूसरा शब्द पहले शब्द से संबंधित है। </span></p> <p><span style=\"font-family:Baloo\">गैल्वेनोमीटर : विद्युत धारा : : ग्राफोमीटर :? </span></p>",
                    options_en: [" <p> Angles </span></p>", " <p> Oscillations</span></p>", 
                                " <p> Altitudes </span></p>", " <p> Humidity </span></p>"],
                    options_hi: [" <p> कोण</span></p>", " <p> दोलन </span></p>",
                                " <p>  ऊंचाई </span></p>", " <p> आर्द्रता</span></p>"],
                    solution_en: " <p>9.(a) </span></p> <p><span style=\"font-family:Times New Roman\">A Galvanometer is used to measure electric current. Similarly, A graphometer is used to measure angles.</span></p>",
                    solution_hi: " <p>9.(a) </span></p> <p><span style=\"font-family:Baloo\">विद्युत धारा मापने के लिए गैल्वेनोमीटर का प्रयोग किया जाता है। इसी तरह, कोणों को मापने के लिए एक ग्राफोमीटर का उपयोग किया जाता है।</span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "6",
                    question_en: " <p>10.</span><span style=\"font-family:Times New Roman\"> In a certain code language, PROUD is written as WYJPK. How will GUILTY be written in </span><span style=\"font-family:Times New Roman\">that</span><span style=\"font-family:Times New Roman\"> language? </span></p>",
                    question_hi: "<p>10.<span style=\"font-family: Baloo;\"> &#2325;&#2367;&#2360;&#2368; &#2344;&#2367;&#2358;&#2381;&#2330;&#2367;&#2340; &#2325;&#2370;&#2335; &#2349;&#2366;&#2359;&#2366; &#2350;&#2375;&#2306;, PROUD &#2325;&#2379; WYJPK &#2354;&#2367;&#2326;&#2366; &#2332;&#2366;&#2340;&#2366; &#2361;&#2376;&#2404; &#2313;&#2360;&#2368; &#2325;&#2370;&#2335; &#2349;&#2366;&#2359;&#2366; &#2350;&#2375;&#2306; GUILTY &#2325;&#2379; &#2325;&#2367;&#2360; &#2346;&#2381;&#2352;&#2325;&#2366;&#2352; &#2354;&#2367;&#2326;&#2366; &#2332;&#2366;&#2319;&#2327;&#2366;? </span></p>\n",
                    options_en: [" <p> BPBOGT </span></p>", " <p> NDPASF </span></p>", 
                                " <p> NPDSAF </span></p>", " <p> </span><span style=\"font-family:Times New Roman\">BBPGOT</span><span style=\"font-family:Times New Roman\"> </span></p>"],
                    options_hi: ["<p>BPBOGT</p>\n", "<p>NDPASF</p>\n",
                                "<p>NPDSAF</p>\n", "<p><span style=\"font-family: Times New Roman;\">BBPGOT</span><span style=\"font-family: Times New Roman;\"> </span></p>\n"],
                    solution_en: " <p>10.(c)</span></p> <p><span style=\"font-family:Times New Roman\">Logic :- (i) For consonants - ‘+7’ rule is used.</span></p> <p> For vowels - ‘-5’ rule is used.</span></p> <p><span style=\"font-family:Times New Roman\">In the word “GUILTY”</span></p> <p><span style=\"font-family:Times New Roman\">The code for the consonants are </span></p> <p><span style=\"font-family:Times New Roman\">G + 7 = N</span></p> <p><span style=\"font-family:Times New Roman\">L + 7 = S</span></p> <p><span style=\"font-family:Times New Roman\">T + 7 = A</span></p> <p><span style=\"font-family:Times New Roman\">Y + 7 = F</span></p> <p><span style=\"font-family:Times New Roman\">The code for the vowels are </span></p> <p><span style=\"font-family:Times New Roman\">U - 5 = P</span></p> <p><span style=\"font-family:Times New Roman\">I - 5 = D</span></p> <p><span style=\"font-family:Times New Roman\">The final code for the word “GUILTY” will be NPDSAF.</span></p>",
                    solution_hi: "<p>10.(c)</p>\r\n<p><span style=\"font-family: Baloo;\">Logic :- (i) &#2357;&#2381;&#2351;&#2306;&#2332;&#2344; &#2325;&#2375; &#2354;&#2367;&#2319; - \'+7\' &#2344;&#2367;&#2351;&#2350; &#2325;&#2366; &#2346;&#2381;&#2352;&#2351;&#2379;&#2327; &#2325;&#2367;&#2351;&#2366; &#2332;&#2366;&#2340;&#2366; &#2361;&#2376;&#2404;</span></p>\r\n<p>&#2360;&#2381;&#2357;&#2352;&#2379;&#2306; &#2325;&#2375; &#2354;&#2367;&#2319; - \'-5\' &#2344;&#2367;&#2351;&#2350; &#2325;&#2366; &#2346;&#2381;&#2352;&#2351;&#2379;&#2327; &#2325;&#2367;&#2351;&#2366; &#2332;&#2366;&#2340;&#2366; &#2361;&#2376;&#2404;</p>\r\n<p><span style=\"font-family: Baloo;\">\"GUILTY\" &#2358;&#2348;&#2381;&#2342; &#2350;&#2375;&#2306;</span></p>\r\n<p><span style=\"font-family: Baloo;\">&#2357;&#2381;&#2351;&#2306;&#2332;&#2344; &#2325;&#2375; &#2354;&#2367;&#2319; &#2325;&#2379;&#2337; &#2361;&#2376;&#2306; :</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">G + 7 = N</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">L + 7 = S</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">T + 7 = A</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Y + 7 = F</span></p>\r\n<p><span style=\"font-family: Baloo;\">&#2360;&#2381;&#2357;&#2352;&#2379;&#2306; &#2325;&#2375; &#2325;&#2370;&#2335; &#2361;&#2376;&#2306; :</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">U - 5 = P</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">I - 5 = D</span></p>\r\n<p><span style=\"font-family: Baloo;\">\"GUILTY\" &#2358;&#2348;&#2381;&#2342; &#2325;&#2366; &#2309;&#2306;&#2340;&#2367;&#2350; &#2325;&#2379;&#2337; NPDSAF &#2361;&#2379;&#2327;&#2366;&#2404;</span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "6",
                    question_en: " <p>11.</span><span style=\"font-family:Times New Roman\"> From the given options choose the one in which the set of numbers do not follow the same Logic/Rule/Relation as the numbers given in below set. </span></p> <p> </span></p>",
                    question_hi: "<p>11.<span style=\"font-family: Baloo;\"> दिए गए विकल्पों में से वह संख्या-समूह चुनें, जिसकी संख्याएं, दिए गए संख्या-समूह की संख्याओं के समान ही तर्क/ नियम/संबंध का अनुसरण नहीं करती हैं। </span></p>\r\n<p><span style=\"font-weight: 400;\">(32, 16, 96) </span></p>",
                    options_en: [" <p>   (42, 21, 126)         </span></p>", " <p>  (26, 13, 78) </span></p>", 
                                " <p>  (36, 18, 108) </span><span style=\"font-family:Times New Roman\">       </span></p>", " <p>  (22, 11, 68) </span></p>"],
                    options_hi: ["<p>(42, 21, 126)</p>", "<p>(26, 13, 78)</p>",
                                "<p>(36, 18, 108) <span style=\"font-family: Times New Roman;\"> </span></p>", "<p>(22, 11, 68)</p>"],
                    solution_en: " <p>11.(d)</span></p> <p><span style=\"font-family:Times New Roman\">Logic :- (2 × n , n , 6 × n)</span></p> <p><span style=\"font-family:Times New Roman\">Eg :- (42, 21, 126) </span><span style=\"font-family:Times New Roman\">(2×21 , 21 , 6×21)</span></p> <p><span style=\"font-family:Times New Roman\">But Option (d) does not follow the pattern.</span></p>",
                    solution_hi: "<p>11.(d)</p>\r\n<p><span style=\"font-family: Times New Roman;\">Logic :- (2 &times; n , n , 6 &times; n)</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Eg :- (42, 21, 126) </span><span style=\"font-family: Times New Roman;\">(2&times;21 , 21 , 6&times;21)</span></p>\r\n<p><span style=\"font-family: Baloo;\">लेकिन विकल्प (d) पैटर्न का पालन नहीं करता है।</span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "6",
                    question_en: "<p>12. <span style=\"font-family: Times New Roman;\">Select the option in which the numbers share the same relationship as that shared by the given pair of numbers. </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">185 : 199 </span></p>\n",
                    question_hi: "<p>12. <span style=\"font-family: Baloo;\">&#2313;&#2360; &#2357;&#2367;&#2325;&#2354;&#2381;&#2346; &#2325;&#2366; &#2330;&#2351;&#2344; &#2325;&#2368;&#2332;&#2367;&#2319; &#2332;&#2367;&#2360;&#2325;&#2368; &#2360;&#2306;&#2326;&#2381;&#2351;&#2366;&#2323;&#2306; &#2325;&#2375; &#2350;&#2343;&#2381;&#2351; &#2357;&#2361;&#2368; &#2360;&#2306;&#2348;&#2306;&#2343; &#2361;&#2376;&#2332;&#2379; &#2342;&#2367;&#2319; &#2327;&#2319; &#2360;&#2306;&#2326;&#2381;&zwj;&#2351;&#2366;&ndash;&#2351;&#2369;&#2327;&#2381;&zwj;&#2350; &#2325;&#2368;&nbsp; </span><span style=\"font-family: Baloo;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;&#2323;&#2306; &#2325;&#2375; &#2350;&#2343;&#2381;&#2351; &#2361;&#2376;&#2404; </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">185 : 199 </span></p>\n",
                    options_en: ["<p>138 : 152</p>\n", "<p>174 : 187</p>\n", 
                                "<p>126 : 134</p>\n", "<p>89 : 106</p>\n"],
                    options_hi: ["<p>138: 152</p>\n", "<p>174 : 187</p>\n",
                                "<p>126 : 134</p>\n", "<p>89 : 106</p>\n"],
                    solution_en: "<p>12.(d)</p>\r\n<p><span style=\"font-family: Times New Roman;\">1+8+5 = 14&nbsp; </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">&rArr; 185+14 = 199</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Option (d) 8+9 = 17</span></p>\r\n<p><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Times New Roman;\">&nbsp;&rArr; 89+17 = 106</span></p>\n",
                    solution_hi: "<p>12.(d)</p>\r\n<p><span style=\"font-family: Times New Roman;\">1+8+5 = 14 </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">&rArr;</span><span style=\"font-family: Times New Roman;\"> 185+14 = 199</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Option (d) 8+9 = 17 </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">&rArr;</span><span style=\"font-family: Times New Roman;\"> 89+17 = 106</span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "6",
                    question_en: " <p>13.</span><span style=\"font-family:Times New Roman\"> Select the option in which the words share the same relationship as that shared by the given pair of words. </span></p> <p><span style=\"font-family:Times New Roman\">Power : Watt </span></p>",
                    question_hi: " <p>13. </span><span style=\"font-family:Baloo\">उस विकल्प का चयन कीजिए जिसके शब्दों के मध्य वही संबंध है, जो दिए गए शब्द–युग्म के शब्दों के मध्य है। </span></p> <p><span style=\"font-family:Baloo\">विद्युत शक्ति : वॉट </span></p>",
                    options_en: [" <p> Current : Volt </span></p>", " <p> Force : Pascal </span></p>", 
                                " <p> Volume : Pound </span></p>", " <p> Resistance : Ohm</span></p>"],
                    options_hi: [" <p> धारा : वोल्‍ट </span><span style=\"font-family:Baloo\">         </span></p>", " <p> बल : पास्‍कल </span></p>",
                                " <p> आयतन : पाउंड       </span></p>", " <p> प्रतिरोध : ओम </span></p>"],
                    solution_en: " <p>13.(d)</span></p> <p><span style=\"font-family:Times New Roman\">‘Watt’ is the SI unit of power. Similarly, ‘Ohm’ is the SI unit of resistance.</span></p>",
                    solution_hi: " <p>13.(d)</span></p> <p><span style=\"font-family:Baloo\">\'वाट\' शक्ति की SI इकाई है। इसी तरह, \'ओम\' प्रतिरोध की SI इकाई है।</span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "6",
                    question_en: "<p>14.<span style=\"font-family: Times New Roman;\"> Select the number from among the given options that can replace the question mark (?) in the following series. </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">4, 5, 13, 22, 86, 111, ? </span></p>",
                    question_hi: "<p>14. <span style=\"font-family: Baloo;\">दिए गए विकल्पों में से उस संख्या का चयन करें, जो निम्न श्रेणी में प्रश्नवाचक चिह्न (?) को प्रतिस्थापित कर सकती है। </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">4, 5, 13, 22, 86, 111, ? </span></p>",
                    options_en: ["<p>327</p>", "<p>427</p>", 
                                "<p>247</p>", "<p>237</p>"],
                    options_hi: ["<p>327</p>", "<p>427</p>",
                                "<p>247</p>", "<p>237</p>"],
                    solution_en: "<p>14.(a)</p>\r\n<p><span style=\"font-family: Times New Roman;\">Logic :- From left to right, the difference between the two consecutive numbers is <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>1</mn><mn>2</mn></msup><mo>&#160;</mo><mo>,</mo><mo>&#160;</mo><msup><mn>2</mn><mn>3</mn></msup><mo>&#160;</mo><mo>,</mo><mo>&#160;</mo><msup><mn>3</mn><mn>2</mn></msup><mo>&#160;</mo><mo>,</mo><msup><mn>4</mn><mn>3</mn></msup><mo>&#160;</mo><mo>,</mo><mo>&#160;</mo><msup><mn>5</mn><mn>2</mn></msup><mo>&#160;</mo><mi>a</mi><mi>n</mi><mi>d</mi><mo>&#160;</mo><msup><mn>6</mn><mn>3</mn></msup></math>.</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">4 +&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>1</mn><mn>2</mn></msup></math> = 5</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">5 +&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>2</mn><mn>3</mn></msup></math> = 13</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">13 + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>3</mn><mn>2</mn></msup></math>= 22</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">22 + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>4</mn><mn>3</mn></msup></math>= 86</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">86 +&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>5</mn><mn>2</mn></msup></math> = 111</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">111 +&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>6</mn><mn>3</mn></msup></math> = 327</span></p>",
                    solution_hi: "<p>14.(a)</p>\r\n<p><span style=\"font-family: Baloo;\">Logic :- बाएं से दाएं, दो क्रमागत संख्याओं के बीच का अंतर&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>1</mn><mn>2</mn></msup><mo>,</mo><msup><mn>2</mn><mn>3</mn></msup><mo>,</mo><msup><mn>3</mn><mn>2</mn></msup><mo>,</mo><mo>&#160;</mo><msup><mn>4</mn><mn>3</mn></msup><mo>,</mo><mo>&#160;</mo><msup><mn>5</mn><mn>2</mn></msup><mo>&#160;</mo><mi>&#2324;&#2352;</mi><mo>&#160;</mo><msup><mn>6</mn><mn>3</mn></msup></math> है।</span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>4</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><msup><mn>1</mn><mn>2</mn></msup><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>5</mn><mo>&#160;</mo><mo>&#160;</mo><mspace linebreak=\"newline\"/><mn>5</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><msup><mn>2</mn><mn>3</mn></msup><mo>=</mo><mo>&#160;</mo><mn>13</mn><mo>&#160;</mo><mo>&#160;</mo><mspace linebreak=\"newline\"/><mn>13</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><msup><mn>3</mn><mn>2</mn></msup><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>22</mn><mo>&#160;</mo><mspace linebreak=\"newline\"/><mo>&#160;</mo><mn>22</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><msup><mn>4</mn><mn>3</mn></msup><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>86</mn><mo>&#160;</mo><mspace linebreak=\"newline\"/><mo>&#160;</mo><mn>86</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><msup><mn>5</mn><mn>2</mn></msup><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>111</mn><mo>&#160;</mo><mo>&#160;</mo><mspace linebreak=\"newline\"/><mn>111</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><msup><mn>6</mn><mn>3</mn></msup><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>327</mn></math></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "6",
                    question_en: " <p>15.</span><span style=\"font-family:Times New Roman\"> Four numbers have been given, out of which three are alike in some manner and one is different. Select the number that is different. </span></p>",
                    question_hi: " <p>15. </span><span style=\"font-family:Baloo\">चार संख्याएँ दी गयी हैं, जिनमें से तीन में एक निश्चित प्रकार की समानता है और एक असंगत है। उस असंगत संख्या का चयन कीजिए। </span></p>",
                    options_en: [" <p> 348 </span></p>", " <p> 231 </span></p>", 
                                " <p> 252 </span></p>", " <p> 217 </span></p>"],
                    options_hi: [" <p> 348 </span></p>", " <p> 231 </span></p>",
                                " <p> 252 </span></p>", " <p> 217 </span></p>"],
                    solution_en: " <p>15.(a)</span></p> <p><span style=\"font-family:Times New Roman\">Logic :- All other options except option (a) are divisible by 7.</span></p>",
                    solution_hi: " <p>15.(a)</span></p> <p><span style=\"font-family:Baloo\">Logic :-विकल्प (a) को छोड़कर अन्य सभी विकल्प 7 से विभाज्य हैं।</span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "16",
                    section: "6",
                    question_en: "<p>16. <span style=\"font-family: Times New Roman;\">A driver increases the speed of his vehicle by 5 km/h after every one hour. If the initial speed of the vehicle was 75 km/h, then what was the average speed of the vehicle in a journey of 4 hours? </span></p>",
                    question_hi: "<p>16.<span style=\"font-family: Baloo;\"> एक ड्राइवर प्रत्&zwj;येक घंटे के बाद अपने वाहन की चाल में 5 km/h की वृद्धि करता है। यदि वाहन की आरंभिक चाल 75 km/h थी, तो 4 घंटे की यात्रा में उसकी औसत चाल कितनी थी? </span></p>",
                    options_en: ["<p>82.5 km/h <span style=\"font-family: Times New Roman;\"> </span></p>", "<p>83 .75 km/h</p>", 
                                "<p>83 km/h <span style=\"font-family: Times New Roman;\"> </span></p>", "<p>82km/h</p>"],
                    options_hi: ["<p>82.5 km/h <span style=\"font-family: Times New Roman;\"> </span></p>", "<p>83.75 km/h</p>",
                                "<p>83 km/h <span style=\"font-family: Times New Roman;\"> </span></p>", "<p>82km/h</p>"],
                    solution_en: "<p>16.(a)</p>\r\n<p><span style=\"font-family: Times New Roman;\">Drivers speed in 1st hour = 75 km/h</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">In 2nd hour = 80 km/h</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">In 3rd hour = 85 km/h</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">In 4th hour = 90 km/h</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Hence, the average speed </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>75</mn><mo>+</mo><mn>80</mn><mo>+</mo><mn>85</mn><mo>+</mo><mn>90</mn></mrow><mn>4</mn></mfrac></math></span><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Times New Roman;\">= 82.5 km/h</span></p>",
                    solution_hi: "<p>16.(a)</p>\r\n<p><span style=\"font-family: Baloo;\">1 घंटे में चालक की गति = 75 किमी/घंटा</span></p>\r\n<p><span style=\"font-family: Baloo;\">दूसरे घंटे में = 80 किमी/घंटा</span></p>\r\n<p><span style=\"font-family: Baloo;\">तीसरे घंटे में = 85 किमी/घंटा</span></p>\r\n<p><span style=\"font-family: Baloo;\">चौथे घंटे में = 90 किमी/घंटा</span></p>\r\n<p><span style=\"font-family: Baloo;\">अत: औसत चाल =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>75</mn><mo>+</mo><mn>80</mn><mo>+</mo><mn>85</mn><mo>+</mo><mn>90</mn></mrow><mn>4</mn></mfrac></math> </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">= 82.5 km/h</span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "17",
                    section: "6",
                    question_en: " <p>17.</span><span style=\"font-family:Times New Roman\"> Read the given statements and conclusions carefully. Assuming that the information given in the statements is true, even if it appears to be at variance with commonly known facts, decide which of the given conclusions logically follow(s) from the statements. </span></p> <p><span style=\"font-family:Times New Roman\">Statements:</span></p> <p><span style=\"font-family:Times New Roman\">All plots are flats.  </span></p> <p><span style=\"font-family:Times New Roman\">All flats are hotels. </span></p> <p><span style=\"font-family:Times New Roman\">Some hotels are restaurants.  </span></p> <p><span style=\"font-family:Times New Roman\">Conclusions:</span></p> <p><span style=\"font-family:Times New Roman\">I. Some restaurants are flats. </span></p> <p><span style=\"font-family:Times New Roman\">II. Some hotels are plots.</span></p> <p><span style=\"font-family:Times New Roman\">III. No plot is a restaurant.</span></p>",
                    question_hi: " <p>17.</span><span style=\"font-family:Baloo\"> दिए गए कथनों और निष्कर्षों को ध्यानपूर्वक पढ़ें। यह मानते हुए कि कथनों में दी गई जानकारी सत्य है, </span></p> <p><span style=\"font-family:Baloo\">चाहे वह ज्ञात तथ्यों से असंगत हो, निर्धारित करें कि कौन सा/से निष्कर्ष कथनों का तार्किक रूप से अनुसरण करता/ते है/हैं? </span></p> <p><span style=\"font-family:Baloo\">कथन:</span></p> <p><span style=\"font-family:Baloo\">सभी प्लॉट, फ्लैट हैं।  </span></p> <p><span style=\"font-family:Baloo\">सभी फ्लैट, होटल हैं। </span></p> <p><span style=\"font-family:Baloo\">कुछ होटल, रेस्तरां हैं।  </span></p> <p><span style=\"font-family:Baloo\">निष्कर्ष:</span><span style=\"font-family:Times New Roman\"> </span></p> <p><span style=\"font-family:Baloo\">I. कुछ रेस्तरां, फ्लैट हैं। </span></p> <p><span style=\"font-family:Baloo\">II. कुछ होटल, प्लॉट हैं। </span></p> <p><span style=\"font-family:Baloo\">III. कोई भी प्लॉट, रेस्तरां नहीं है। </span></p>",
                    options_en: [" <p>  Only conclusion I follow.</span></p>", " <p>  Only conclusion II follows.</span></p>", 
                                " <p>  Both conclusions I and II follow.</span></p>", " <p>  Both conclusions II and III follow.</span></p>"],
                    options_hi: [" <p> केवल निष्कर्ष I अनुसरण करता है।  </span></p>", " <p> केवल निष्कर्ष II अनुसरण करता है। </span></p>",
                                " <p> निष्कर्ष I और II दोनों अनुसरण करते हैं। </span></p>", " <p> निष्कर्ष II और III दोनों अनुसरण करते  हैं। </span></p>"],
                    solution_en: " <p>17.(b)</span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646807695/word/media/image28.png\"/></p> <p><span style=\"font-family:Times New Roman\">According to the above diagram, Only conclusion II follows. As there is a relation of SOME between hotels and plots.</span></p>",
                    solution_hi: " <p>17.(b)</span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646807695/word/media/image28.png\"/></p> <p><span style=\"font-family:Baloo\">उपरोक्त आरेख के अनुसार, केवल निष्कर्ष II अनुसरण करता है। जैसा कि होटल और भूखंडों के बीच कुछ का संबंध है।</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "18",
                    section: "6",
                    question_en: "<p>18. <span style=\"font-family: Times New Roman;\">Select the option that is related to the third term in the same way as the second term is related to the first term. </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">AMIDST : </span><span style=\"font-family: Times New Roman;\">AIMDST</span><span style=\"font-family: Times New Roman;\"> : : SYNTAX : ? </span></p>",
                    question_hi: "<p>18.<span style=\"font-family: Baloo;\"> &#2313;&#2360; &#2357;&#2367;&#2325;&#2354;&#2381;&#2346; &#2325;&#2366; &#2330;&#2351;&#2344; &#2325;&#2352;&#2375;&#2306;, &#2332;&#2379; &#2340;&#2368;&#2360;&#2352;&#2375; &#2346;&#2342; &#2360;&#2375; &#2336;&#2368;&#2325; &#2313;&#2360;&#2368; &#2346;&#2381;&#2352;&#2325;&#2366;&#2352; &#2360;&#2306;&#2348;&#2306;&#2343;&#2367;&#2340; &#2361;&#2376;, &#2332;&#2367;&#2360; &#2346;&#2381;&#2352;&#2325;&#2366;&#2352; &#2342;&#2370;&#2360;&#2352;&#2366; &#2346;&#2342; &#2346;&#2361;&#2354;&#2375; &#2346;&#2342; &#2360;&#2375; &#2360;&#2306;&#2348;&#2306;&#2343;&#2367;&#2340; &#2361;&#2376;&#2404; </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">AMIDST : </span><span style=\"font-family: Times New Roman;\">AIMDST</span><span style=\"font-family: Times New Roman;\"> : : SYNTAX : ? </span></p>\n",
                    options_en: ["<p><span style=\"font-family: Times New Roman;\">SNYATX</span><span style=\"font-family: Times New Roman;\"> </span></p>", "<p><span style=\"font-family: Times New Roman;\">NYSTAX</span><span style=\"font-family: Times New Roman;\"> </span></p>", 
                                "<p>SANYTX</p>", "<p>NSYATX</p>"],
                    options_hi: ["<p><span style=\"font-family: Times New Roman;\">SNYATX</span><span style=\"font-family: Times New Roman;\"> </span></p>\n", "<p><span style=\"font-family: Times New Roman;\">NYSTAX</span><span style=\"font-family: Times New Roman;\"> </span></p>\n",
                                "<p>SANYTX</p>\n", "<p>NSYATX</p>\n"],
                    solution_en: "<p>18.(d)</p>\r\n<p><span style=\"font-family: Times New Roman;\">Logic :- Arrange the 1st 3 alphabets and last 3 alphabets of the word separately in the alphabetical order.</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">For the word &lsquo;SYNTAX&rsquo;</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">SYN &rArr;</span><span style=\"font-family: Times New Roman;\"> NSY and TAX &rArr;</span><span style=\"font-family: Times New Roman;\"> ATX</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">So, the final code for &lsquo;SYNTAX&rsquo; is &lsquo;NSYATX&rsquo;.</span></p>",
                    solution_hi: "<p>18.(d)</p>\r\n<p><span style=\"font-family: Baloo;\">Logic :- &#2358;&#2348;&#2381;&#2342; &#2325;&#2375; &#2346;&#2361;&#2354;&#2375; 3 &#2309;&#2325;&#2381;&#2359;&#2352; &#2324;&#2352; &#2309;&#2306;&#2340;&#2367;&#2350; 3 &#2309;&#2325;&#2381;&#2359;&#2352; &#2309;&#2354;&#2327;-&#2309;&#2354;&#2327; &#2357;&#2352;&#2381;&#2339;&#2366;&#2344;&#2369;&#2325;&#2381;&#2352;&#2350; &#2350;&#2375;&#2306; &#2357;&#2381;&#2351;&#2357;&#2360;&#2381;&#2341;&#2367;&#2340; &#2325;&#2352;&#2375;&#2306;&#2404;</span></p>\r\n<p><span style=\"font-family: Baloo;\">&lsquo;SYNTAX&rsquo; &#2358;&#2348;&#2381;&#2342; &#2325;&#2375; &#2354;&#2367;&#2319;</span></p>\r\n<p><span style=\"font-family: Baloo;\">SYN &rArr;NSY &#2324;&#2352; TAX &rArr;ATX</span></p>\r\n<p><span style=\"font-family: Baloo;\">&#2340;&#2379;, \'SYNTAX\' &#2325;&#2375; &#2354;&#2367;&#2319; &#2309;&#2306;&#2340;&#2367;&#2350; &#2325;&#2379;&#2337; \'NSYATX\' &#2361;&#2376;&#2404;</span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "19",
                    section: "6",
                    question_en: " <p>19. </span><span style=\"font-family:Times New Roman\">The sequence of the folding piece of paper and the manner in which the folded paper has been cut is shown in the following figures. How would this paper look when unfolded?</span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646807695/word/media/image9.png\"/></p>",
                    question_hi: " <p>19. </span><span style=\"font-family:Baloo\">कागज के एक टुकड़े को मोड़ने का क्रम और मुड़े हुए कागज को काटने की विधि, निम्न आकृतियो में दर्शाया गया है। खोलने पर कागज  कैसा दिखाइ देगा ?</span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646807695/word/media/image9.png\"/></p>",
                    options_en: [" <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646807695/word/media/image25.png\"/></p>", " <p> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646807695/word/media/image23.png\"/></p>", 
                                " <p> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646807695/word/media/image5.png\"/></p>", " <p> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646807695/word/media/image30.png\"/></p>"],
                    options_hi: [" <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646807695/word/media/image25.png\"/></p>", " <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646807695/word/media/image23.png\"/></p>",
                                " <p> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646807695/word/media/image5.png\"/></p>", " <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646807695/word/media/image30.png\"/></p>"],
                    solution_en: " <p>19.(d)</span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646807695/word/media/image8.png\"/></p>",
                    solution_hi: " <p>19.(d)</span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646807695/word/media/image8.png\"/></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "20",
                    section: "6",
                    question_en: " <p>20.</span><span style=\"font-family:Times New Roman\"> Select the combination of letters that when sequentially placed in the blanks of the given letter series will complete the series. </span></p> <p><span style=\"font-family:Times New Roman\">p l _ p p _ s p _ l s p p l _ p </span></p>",
                    question_hi: " <p>20. </span><span style=\"font-family:Baloo\">अक्षरों के उस संयोजन का चयन करें जिसे दी गई श्रेणी में रिक्त स्थानों पर क्रमिक रूप से रखने पर श्रेणी पूर्ण हो जाएगी। </span></p> <p><span style=\"font-family:Times New Roman\">p l _ p p _ s p _ l s p p l _ p </span></p>",
                    options_en: [" <p>  s p p l </span></p>", " <p>  s l p s </span></p>", 
                                " <p>  l s l p </span></p>", " <p>  p l s p </span></p>"],
                    options_hi: [" <p>  s p p l </span></p>", " <p>  s l p s </span></p>",
                                " <p>  l s l p </span></p>", " <p>  p l s p </span></p>"],
                    solution_en: " <p>20.(b)</span></p> <p><span style=\"font-family:Times New Roman\">Logic :- ‘plsp’ is a repetitive series.</span></p> <p><span style=\"font-family:Times New Roman\">p l </span><span style=\"font-family:Times New Roman\">s</span><span style=\"font-family:Times New Roman\"> p</span><span style=\"font-family:Times New Roman\"> / </span><span style=\"font-family:Times New Roman\">p </span><span style=\"font-family:Times New Roman\">l</span><span style=\"font-family:Times New Roman\"> s p</span><span style=\"font-family:Times New Roman\"> / </span><span style=\"font-family:Times New Roman\">p</span><span style=\"font-family:Times New Roman\"> l s p</span><span style=\"font-family:Times New Roman\"> / </span><span style=\"font-family:Times New Roman\">p l </span><span style=\"font-family:Times New Roman\">s</span><span style=\"font-family:Times New Roman\"> p</span><span style=\"font-family:Times New Roman\"> is the complete series.</span></p>",
                    solution_hi: " <p>20.(b)</span></p> <p><span style=\"font-family:Baloo\">Logic :- \'plsp\' एक दोहराव वाली श्रृंखला है।</span></p> <p><span style=\"font-family:Times New Roman\">p l </span><span style=\"font-family:Times New Roman\">s</span><span style=\"font-family:Times New Roman\"> p</span><span style=\"font-family:Times New Roman\"> / </span><span style=\"font-family:Times New Roman\">p </span><span style=\"font-family:Times New Roman\">l</span><span style=\"font-family:Times New Roman\"> s p</span><span style=\"font-family:Times New Roman\"> / </span><span style=\"font-family:Times New Roman\">p</span><span style=\"font-family:Times New Roman\"> l s p</span><span style=\"font-family:Times New Roman\"> / </span><span style=\"font-family:Times New Roman\">p l </span><span style=\"font-family:Times New Roman\">s</span><span style=\"font-family:Times New Roman\"> p</span><span style=\"font-family:Baloo\"> पूरी श्रृंखला है।</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "21",
                    section: "6",
                    question_en: " <p>21.</span><span style=\"font-family:Times New Roman\"> Four words have been given, out of which three are alike in some manner and one is different. Select the word that is different. </span></p>",
                    question_hi: " <p>21.</span><span style=\"font-family:Baloo\"> चार शब्द दिए गए हैं, जिनमेंसेतीन किसी प्रकार समान हैंऔर एक असंगत है। उस असंगत शब्द का </span></p> <p><span style=\"font-family:Baloo\">चयन करें। </span></p>",
                    options_en: [" <p> Fluster </span></p>", " <p> Confound </span></p>", 
                                " <p> Agitate</span></p>", " <p> Calm </span></p>"],
                    options_hi: [" <p> भड़काना </span><span style=\"font-family:Baloo\">     </span></p>", " <p> उन्मादित करना </span></p>",
                                " <p> उकसाना </span><span style=\"font-family:Baloo\">     </span></p>", " <p> शांत करना</span></p>"],
                    solution_en: " <p>21.(d)</span></p> <p><span style=\"font-family:Times New Roman\">Agitate, Fluster and Confound are synonymous words but calm is their antonym.</span></p>",
                    solution_hi: " <p>21.(d)</span><span style=\"font-family:Times New Roman\"> </span></p> <p><span style=\"font-family:Baloo\">एगेटेट, फ्लस्टर और कन्फाउंड पर्यायवाची शब्द हैं लेकिन शांत इनका विलोम शब्द है।</span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "22",
                    section: "6",
                    question_en: " <p>22. </span><span style=\"font-family:Times New Roman\">How many rectangles are there in the given figure?</span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646807695/word/media/image6.png\"/></p>",
                    question_hi: " <p>22.</span><span style=\"font-family:Baloo\"> दी गई आकृति में कितने आयत हैं?</span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646807695/word/media/image6.png\"/></p>",
                    options_en: [" <p> 18</span></p>", " <p> 20</span></p>", 
                                " <p> 16</span></p>", " <p> 19</span></p>"],
                    options_hi: [" <p> 18</span></p>", " <p> 20</span></p>",
                                " <p> 16</span></p>", " <p> 19</span></p>"],
                    solution_en: " <p>22.(a)</span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646807695/word/media/image18.png\"/></p> <p><span style=\"font-family:Times New Roman\">There are 6 individual rectangles numbered from 1 to 6.</span></p> <p><span style=\"font-family:Times New Roman\">Other 12 are the combination of (1 & 2), (2 & 3), (1,2 & 3) , (4 & 5), (5 & 6), (4, 5 & 6), (1 & 4), (2 & 5), (3 & 6), (1, 2, 4 & 5), (2, 3, 5 & 6) and (1, 2, 3, 4, 5 & 6).</span></p>",
                    solution_hi: " <p>22.(a)</span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646807695/word/media/image18.png\"/></p> <p><span style=\"font-family:Baloo\">1 से 6 तक गिने गए 6 आयत हैं।</span></p> <p><span style=\"font-family:Baloo\">अन्य 12 के संयोजन हैं = (1 & 2), (2 & 3), (1,2 & 3) , (4 & 5), (5 & 6), (4, 5 & 6), (1 & 4), (2 & 5), (3 & 6), (1, 2, 4 & 5), (2, 3, 5 & 6) and (1, 2, 3, 4, 5 & 6).</span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "23",
                    section: "6",
                    question_en: "<p>23.<span style=\"font-family: Times New Roman;\"> Select the option in which the numbers are related in the same way as are the numbers of the following set. </span></p>\r\n<p><span style=\"font-weight: 400;\">(2194, 1328, 726)&nbsp;</span></p>",
                    question_hi: "<p>23.<span style=\"font-family: Baloo;\"> उस विकल्प का चयन कीजिए जिसकी संख्याओं के मध्य वही संबंध है जो दिए गए समुच्चय की संख्याओं के मध्य है। </span></p>\r\n<p>&nbsp;</p>",
                    options_en: ["<p>(1170, 570, 228)</p>", "<p>(1560, 1131, 551)</p>", 
                                "<p>(1406, 962, 390)</p>", "<p>(1725, 997, 509)</p>"],
                    options_hi: ["<p>(1170, 570, 228)</p>", "<p>(1560, 1131, 551)</p>",
                                "<p>(1406, 962, 390)</p>", "<p>(1725, 997, 509)</p>"],
                    solution_en: "<p>23.(d)</p>\r\n<p><span style=\"font-family: Times New Roman;\">Logic :- [ (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>n</mi><mn>3</mn></msup></math>-3), {<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><mi>n</mi><mo>-</mo><mn>2</mn><mo>)</mo></mrow><mn>3</mn></msup></math>-3}, { <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><mi>n</mi><mo>-</mo><mn>4</mn><mo>)</mo></mrow><mn>3</mn></msup></math>-3}]</span></p>\r\n<p><span style=\"font-family: Times New Roman;\"> {( <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>13</mn><mn>3</mn></msup></math>-3), (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>11</mn><mn>3</mn></msup></math>-3), ( <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>9</mn><mn>3</mn></msup></math>-3)}</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Similarly, (1725, 997, 509) </span><span style=\"font-family: Times New Roman;\"> {(<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>12</mn><mn>3</mn></msup></math>-3), (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>10</mn><mn>3</mn></msup></math>-3), (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>8</mn><mn>3</mn></msup></math>-3)}</span></p>",
                    solution_hi: "<p>23. (d)</p>\r\n<p><span style=\"font-family: Times New Roman;\">Logic :- <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>[</mo><mo>(</mo><msup><mi>n</mi><mn>3</mn></msup><mo>-</mo><mn>3</mn><mo>)</mo><mo>,</mo><mrow><msup><mrow><mo>(</mo><mi>n</mi><mo>-</mo><mn>2</mn><mo>)</mo></mrow><mn>3</mn></msup><mo>-</mo><mn>3</mn></mrow><mo>,</mo><mrow><msup><mrow><mo>(</mo><mi>n</mi><mo>-</mo><mn>4</mn><mo>)</mo></mrow><mn>3</mn></msup><mo>-</mo><mn>3</mn></mrow><mo>]</mo></math></span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><msup><mn>13</mn><mn>3</mn></msup><mo>-</mo><mn>3</mn><mo>)</mo><mo>,</mo><mo>(</mo><msup><mn>11</mn><mn>3</mn></msup><mo>-</mo><mn>3</mn><mo>)</mo><mo>,</mo><mo>(</mo><msup><mn>9</mn><mn>3</mn></msup><mo>-</mo><mn>3</mn><mo>)</mo></math></p>\r\n<p><span style=\"font-family: Baloo;\">उसी प्रकार, (1725, 997, 509) </span><span style=\"font-family: Times New Roman;\"> {<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><msup><mn>12</mn><mn>3</mn></msup><mo>-</mo><mn>3</mn><mo>)</mo><mo>,</mo><mo>&#160;</mo><mo>(</mo><msup><mn>10</mn><mn>3</mn></msup><mo>-</mo><mn>3</mn><mo>)</mo><mo>,</mo><mo>&#160;</mo><mo>(</mo><msup><mn>8</mn><mn>3</mn></msup><mo>-</mo><mn>3</mn><mo>)</mo></math>}</span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "24",
                    section: "6",
                    question_en: " <p>24.</span><span style=\"font-family:Times New Roman\"> Select the correct mirror image of the  given combination when the mirror is placed at ‘PQ’ as shown.</span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646807695/word/media/image1.png\"/></p>",
                    question_hi: " <p>24. </span><span style=\"font-family:Baloo\">दिए गए संयोजन की सही दर्पण छवि का चयन करें जब दर्पण को \'PQ\' पर दिखाया गया है।</span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646807695/word/media/image1.png\"/></p>",
                    options_en: [" <p> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646807695/word/media/image2.png\"/><span style=\"font-family:Times New Roman\">       </span></p>", " <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646807695/word/media/image11.png\"/></p>", 
                                " <p> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646807695/word/media/image31.png\"/></p>", " <p> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646807695/word/media/image20.png\"/></p>"],
                    options_hi: [" <p> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646807695/word/media/image2.png\"/><span style=\"font-family:Times New Roman\">       </span></p>", " <p> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646807695/word/media/image11.png\"/></p>",
                                " <p> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646807695/word/media/image31.png\"/></p>", " <p> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646807695/word/media/image20.png\"/></p>"],
                    solution_en: " <p>24. (d)</span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646807695/word/media/image4.png\"/></p>",
                    solution_hi: " <p>24.(d)</span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646807695/word/media/image4.png\"/></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "25",
                    section: "misc",
                    question_en: "<p>25.<span style=\"font-family: Times New Roman;\"> Select the option in which the given figure is embedded (rotation is not allowed ).</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646807695/word/media/image7.png\" width=\"114\" height=\"133\" /></p>",
                    question_hi: " <p>25. </span><span style=\"font-family:Baloo\">उस विकल्प का चयन करें जिसमें दी गई आकृति अंतःस्थापित है (घूमने की अनुमति नहीं है)।</span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646807695/word/media/image7.png\"/></p>",
                    options_en: ["<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646807695/word/media/image26.png\" /></p>", "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646807695/word/media/image33.png\" /></p>", 
                                "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646807695/word/media/image12.png\" /></p>", "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646807695/word/media/image27.png\" /></p>"],
                    options_hi: [" <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646807695/word/media/image26.png\"/></p>", " <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646807695/word/media/image33.png\"/></p>",
                                " <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646807695/word/media/image12.png\"/></p>", " <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646807695/word/media/image27.png\"/></p>"],
                    solution_en: "<p>25.(a)</p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646807695/word/media/image32.png\" width=\"153\" height=\"144\" /></p>",
                    solution_hi: " <p>25.(a)</span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646807695/word/media/image32.png\"/></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML - more compact
            let html = `
                <div class="card mb-3">
                    <div class="card-body">
                        <div class="question-text mb-3">
                            <div class="en">${question.question_en}</div>
                            <div class="hi" style="display:none">${question.question_hi}</div>
                        </div>
                        <div class="options">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = 'option-label';
                // When reviewing, highlight user's choice and correct answer
                if (submitted) {
                    if (opt === question.correct) {
                        optionClass += ' border-success bg-success bg-opacity-10';
                    }
                    if (answers[question.id] === opt && opt !== question.correct) {
                        optionClass += ' border-danger bg-danger bg-opacity-10';
                    }
                }
                html += `
                    <div class="form-check mb-2">
                        <input class="form-check-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${answers[question.id] === opt ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                        <label class="form-check-label w-100 ${optionClass}" for="opt-${index}-${opt}">
                            ${submitted && opt === question.correct ? '<i class="fas fa-check-circle text-success me-1"></i>' : ''}
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const alertClass = isCorrect ? 'alert-success' : 'alert-danger';
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="alert ${alertClass} mt-3 py-2">
                        <strong>${resultText}</strong>
                        <div class="mt-1 small">
                            <strong>Solution:</strong>
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                document.getElementById(`box-${index}`).classList.add('attempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { if (currentQuestion > 0) showQuestion(currentQuestion - 1); }
        function nextQuestion() { if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
            // Update button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const content = document.getElementById('main-content');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                if (window.innerWidth >= 992) {
                    content.classList.add('with-nav');
                }
            } else {
                nav.classList.remove('show');
                if (window.innerWidth >= 992) {
                    content.classList.remove('with-nav');
                }
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer - compact
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            if (confirm(`You have attempted ${attempted} of ${questions.length} questions.
${remaining} questions are unattempted.
Are you sure you want to submit?`)) {
                submitTest();
            }
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('bg-secondary', 'text-white');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            resultsModal.show();
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.classList.replace('btn-light', 'btn-success');
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            resultsModal.hide();
            showQuestion(0);
        }
        // Add event listener for window resize
        window.addEventListener('resize', function() {
            if (window.innerWidth >= 992) {
                if (document.getElementById('question-nav').classList.contains('show')) {
                    document.getElementById('main-content').classList.add('with-nav');
                }
            } else {
                document.getElementById('main-content').classList.remove('with-nav');
            }
        });
    </script>
</body>
</html>