<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">60:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 100</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">100</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 60 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["6"] = {
                name: "Reasoning",
                start: 0,
                end: 23
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="6">Reasoning</option>`;
            sections["18"] = {
                name: "General Awareness",
                start: 24,
                end: 48
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="18">General Awareness</option>`;
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 49,
                end: 73
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["7"] = {
                name: "English",
                start: 74,
                end: 98
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="7">English</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 99,
                end: 99
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "6",
                    question_en: "<p>1. Select the set in which the numbers are related in the same way as are the numbers of the following set.<br>3 &mdash; 20 &mdash; 7<br>5 &mdash; 26 &mdash; 8<br><strong>NOTE</strong> : Operations should be performed on the whole numbers, without breaking down the numbers into its constituent digits. E.g. 13 &ndash; Operations on 13 such as adding / subtracting / multiplying etc. to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is not allowed)</p>",
                    question_hi: "<p>1. उस समुच्चय का चयन कीजिए जिसमें संख्याएँ उसी प्रकार से संबंधित हैं जिस प्रकार निम्नलिखित समुच्चय की संख्याएँ संबंधित हैं।<br>3 &mdash; 20 &mdash; 7<br>5 &mdash; 26 &mdash; 8<br><strong>नोट</strong> : संख्याओं को उनके घटक अंकों में तोड़े बिना, पूर्ण संख्याओं पर संक्रियाएँ की जानी चाहिए। उदाहरण के लिए 13 को लें - 13 पर संक्रियाएं जैसे 13 में जोड़ना/घटाना/गुणा करना आदि की जा सकती हैं। 13 को 1 और 3 में तोड़ने और फिर 1 और 3 पर गणितीय संक्रियाएँ करने की अनुमति नहीं है)</p>",
                    options_en: ["<p>18 &mdash; 70 &mdash; 6</p>", "<p>5 &mdash; 20 &mdash; 8</p>", 
                                "<p>8 &mdash; 28 &mdash; 6</p>", "<p>17 &mdash; 20 &mdash; 13</p>"],
                    options_hi: ["<p>18 &mdash; 70 &mdash; 6</p>", "<p>5 &mdash; 20 &mdash; 8</p>",
                                "<p>8 &mdash; 28 &mdash; 6</p>", "<p>17 &mdash; 20 &mdash; 13</p>"],
                    solution_en: "<p>1.(c) <strong>Logic:</strong> (1st no. + 3rd no.) &times; 2 = 2nd no.<br>3 &mdash; 20 &mdash; 7 :- (3 + 7) &times; 2 = 20 <br>5 &mdash; 26 &mdash; 8 :- (5 + 8) &times; 2 = 26<br>Similarly<br>8 &mdash; 28 &mdash; 6 :- (8 + 6) &times; 2 = 28</p>",
                    solution_hi: "<p>1.(c) <strong>तर्क:</strong> (पहली संख्या + तीसरी संख्या) &times; 2 = दूसरी संख्या <br>3 &mdash; 20 &mdash; 7 :- (3 + 7) &times; 2 = 20 <br>5 &mdash; 26 &mdash; 8 :- (5 + 8) &times; 2 = 26<br>इसी प्रकार <br>8 &mdash; 28 &mdash; 6 :- (8 + 6) &times; 2 = 28</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "6",
                    question_en: "<p>2. In a certain code language, &lsquo;she is girl&rsquo; is coded as &lsquo;7 3 9&rsquo;; &lsquo;he is boy&rsquo; is coded as &lsquo;9 6 5&rsquo; and &lsquo;girl is tall&rsquo; is coded as &lsquo;9 3 8&rsquo;. How is &lsquo;she&rsquo; coded in that language?</p>",
                    question_hi: "<p>2. एक निश्चित कूट भाषा में \'she is girl\' को \'7 3 9\' के रूप में कूटबद्ध किया गया है; \'he is boy\' को \'9 6 5\' के रूप में कूटबद्ध किया गया है और \'girl is tall\' को \'9 3 8\' के रूप में कूटबद्ध किया गया है। उसी भाषा में \'she\' को किस प्रकार कूटबद्ध किया गया है?</p>",
                    options_en: ["<p>7</p>", "<p>3</p>", 
                                "<p>8</p>", "<p>9</p>"],
                    options_hi: ["<p>7</p>", "<p>3</p>",
                                "<p>8</p>", "<p>9</p>"],
                    solution_en: "<p>2.(a) &lsquo;she is girl&rsquo; &rarr;&nbsp;&lsquo;7 3 9&rsquo; &hellip;&hellip;&hellip; (i)<br>&lsquo;girl is tall&rsquo; &rarr;&nbsp;&lsquo;9 3 8&rsquo; &hellip;&hellip;&hellip;(ii)<br>in equation i and ii , (girl is) is common.<br>So the code of &lsquo;she&rsquo; is 7.</p>",
                    solution_hi: "<p>2.(a) &lsquo;she is girl&rsquo; &rarr;&nbsp;&lsquo;7 3 9&rsquo; &hellip;&hellip;&hellip; (i)<br>&lsquo;girl is tall&rsquo; &rarr;&nbsp;&lsquo;9 3 8&rsquo; &hellip;&hellip;&hellip;(ii)<br>समीकरण I और II में , (girl is) उभयनिष्ठ है।<br>अतः \'she\' का कोड 7 है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "6",
                    question_en: "<p>3. Select the number from among the given options that can replace the question mark (?) in the following series.<br>300, 152, 80, 48, 40, 52, ?</p>",
                    question_hi: "<p>3. दिए गए विकल्पों में से उस संख्या का चयन कीजिए, जो निम्नलिखित शृंखला में प्रश्न-चिह्न (?) को प्रतिस्थापित कर सकती है।<br>300, 152, 80, 48, 40, 52, ?</p>",
                    options_en: ["<p>60</p>", "<p>80</p>", 
                                "<p>90</p>", "<p>100</p>"],
                    options_hi: ["<p>60</p>", "<p>80</p>",
                                "<p>90</p>", "<p>100</p>"],
                    solution_en: "<p>3.(c)<br><strong id=\"docs-internal-guid-4c936aae-7fff-3bdb-21e4-597c702b5c33\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXeXOdhqlRqAIE5PLDlUC7Qcx61b2aKgYym93gkbBnU2VxGghlQrlDSilrDnPyCAmh9nnZi3vMf0LfWTCpwvP9xQqRxIeIM2asHq7JehTLFfftAZ_fmi5eMYUrQATOaFsjlsTIRoJmg-vKBZIFZAxk8ZBVA?key=DeDRJyT6seelEyAeFidXXWTi\" width=\"343\" height=\"50\"></strong></p>",
                    solution_hi: "<p>3.(c)<br><strong id=\"docs-internal-guid-4c936aae-7fff-3bdb-21e4-597c702b5c33\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXeXOdhqlRqAIE5PLDlUC7Qcx61b2aKgYym93gkbBnU2VxGghlQrlDSilrDnPyCAmh9nnZi3vMf0LfWTCpwvP9xQqRxIeIM2asHq7JehTLFfftAZ_fmi5eMYUrQATOaFsjlsTIRoJmg-vKBZIFZAxk8ZBVA?key=DeDRJyT6seelEyAeFidXXWTi\" width=\"343\" height=\"50\"></strong></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "6",
                    question_en: "<p>4. Select the correct mirror image of the given figure when the mirror is placed at MN as shown.<br><strong id=\"docs-internal-guid-f9a72b60-7fff-9c4d-0630-c25350f40d1a\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdaoWj2fSnSYtgdrxS29FN9ff5FUend-LoP_MYZZ-qtO2WSSk-70qQ_bRLKx962bjd-0xlaVtfeLZvhGYjNsLQ1omxsz19DvUnzxO7KgNVxF22m5t1NW37H0NEb2pHEEn7nK3pR2zR8foCUzVNRbTfxGAJl?key=DeDRJyT6seelEyAeFidXXWTi\" width=\"99\" height=\"95\"></strong></p>",
                    question_hi: "<p>4. जब दर्पण को दर्शाए गए अनुसार MN पर रखा जाता है, तो दी गई आकृति के सही दर्पण प्रतिबिंब का चयन करें।<br><strong id=\"docs-internal-guid-3c756e70-7fff-1deb-f6fd-9e0107059edd\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdaoWj2fSnSYtgdrxS29FN9ff5FUend-LoP_MYZZ-qtO2WSSk-70qQ_bRLKx962bjd-0xlaVtfeLZvhGYjNsLQ1omxsz19DvUnzxO7KgNVxF22m5t1NW37H0NEb2pHEEn7nK3pR2zR8foCUzVNRbTfxGAJl?key=DeDRJyT6seelEyAeFidXXWTi\" width=\"99\" height=\"95\"></strong></p>",
                    options_en: ["<p><strong id=\"docs-internal-guid-1d437cd7-7fff-ba93-d893-acf753d5dd6c\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcZ1N143KElhnBD4rEQcxVPxj3pu-Q7JmGHwCunEFDG3mgfVonMICp3nvl_QQlUrrHEfyFRwrymdC4jl6RibF54_ukNWgL-J9dY7oiOHtRb4bkqJ_PTQsyDaMm2HYYF0qfDME84z4njFH9TL48s_MIDpLg?key=DeDRJyT6seelEyAeFidXXWTi\" width=\"104\" height=\"20\"></strong></p>", "<p><strong id=\"docs-internal-guid-2ac3cec6-7fff-d024-e810-fdf2e6dff72e\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXeM9Fyw8lq1zzz7tnQLvuJF9BxzL0bMWLc5UF-pT2ZxuoGMgEjwDakf8y0FfGRxzkDiKhv7J2l-EIhr1A4vP7OeKDdMV7RdONlgeU2GJqzlOVTKTUZObI_EdGKuCAthx6FHOpNF3i4QQaNCW_RfOBVDl-c?key=DeDRJyT6seelEyAeFidXXWTi\" width=\"105\" height=\"20\"></strong></p>", 
                                "<p><strong id=\"docs-internal-guid-1788e3a1-7fff-e657-f970-69ec81519782\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXctGzMRH4J56jF2e9K5mYacE2GKQC_YMLIWk7WbzwKoi9iw9suwYzUS3p7nupgbhjHYJl1IR_Kmqd1BZybwMBq6DvTzleqs5Xwji-VjO9K2YGTDPqxb0SH-1HP4CO24L0rygs0itpURY09e-UcDXybZI5QN?key=DeDRJyT6seelEyAeFidXXWTi\" width=\"105\" height=\"20\"></strong></p>", "<p><strong id=\"docs-internal-guid-627df383-7fff-009d-8a6d-b10357a3c9fd\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXd4BLx5Ia1kaoOfzch6VvGtcXiR4UZmqMozd5Zj_e59ebs6NPun--YMgyMXNfHa0nMJW0L9bH8Vc2GvD2IpSP7rvQ8QYyl0wEN8bHDs33LUgUm85S81SCmwF-tCNWM0rTb5jjzG-1TKpvwwvnbXZLD_lky3?key=DeDRJyT6seelEyAeFidXXWTi\" width=\"105\" height=\"20\"></strong></p>"],
                    options_hi: ["<p><strong id=\"docs-internal-guid-69719f24-7fff-37c8-b927-9beba1065a2c\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcZ1N143KElhnBD4rEQcxVPxj3pu-Q7JmGHwCunEFDG3mgfVonMICp3nvl_QQlUrrHEfyFRwrymdC4jl6RibF54_ukNWgL-J9dY7oiOHtRb4bkqJ_PTQsyDaMm2HYYF0qfDME84z4njFH9TL48s_MIDpLg?key=DeDRJyT6seelEyAeFidXXWTi\" width=\"104\" height=\"20\"></strong></p>", "<p><strong id=\"docs-internal-guid-dcc0a965-7fff-4d0a-9d63-b44b4a484fb5\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXeM9Fyw8lq1zzz7tnQLvuJF9BxzL0bMWLc5UF-pT2ZxuoGMgEjwDakf8y0FfGRxzkDiKhv7J2l-EIhr1A4vP7OeKDdMV7RdONlgeU2GJqzlOVTKTUZObI_EdGKuCAthx6FHOpNF3i4QQaNCW_RfOBVDl-c?key=DeDRJyT6seelEyAeFidXXWTi\" width=\"105\" height=\"20\"></strong></p>",
                                "<p><strong id=\"docs-internal-guid-8d5877ae-7fff-6321-6e72-5eb089616797\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXctGzMRH4J56jF2e9K5mYacE2GKQC_YMLIWk7WbzwKoi9iw9suwYzUS3p7nupgbhjHYJl1IR_Kmqd1BZybwMBq6DvTzleqs5Xwji-VjO9K2YGTDPqxb0SH-1HP4CO24L0rygs0itpURY09e-UcDXybZI5QN?key=DeDRJyT6seelEyAeFidXXWTi\" width=\"105\" height=\"20\"></strong></p>", "<p><strong id=\"docs-internal-guid-7087ca2f-7fff-78cf-7704-6c2acdd815ae\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXd4BLx5Ia1kaoOfzch6VvGtcXiR4UZmqMozd5Zj_e59ebs6NPun--YMgyMXNfHa0nMJW0L9bH8Vc2GvD2IpSP7rvQ8QYyl0wEN8bHDs33LUgUm85S81SCmwF-tCNWM0rTb5jjzG-1TKpvwwvnbXZLD_lky3?key=DeDRJyT6seelEyAeFidXXWTi\" width=\"105\" height=\"20\"></strong></p>"],
                    solution_en: "<p>4.(d)<br><strong id=\"docs-internal-guid-7087ca2f-7fff-78cf-7704-6c2acdd815ae\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXd4BLx5Ia1kaoOfzch6VvGtcXiR4UZmqMozd5Zj_e59ebs6NPun--YMgyMXNfHa0nMJW0L9bH8Vc2GvD2IpSP7rvQ8QYyl0wEN8bHDs33LUgUm85S81SCmwF-tCNWM0rTb5jjzG-1TKpvwwvnbXZLD_lky3?key=DeDRJyT6seelEyAeFidXXWTi\" width=\"105\" height=\"20\"></strong></p>",
                    solution_hi: "<p>4.(d)<br><strong id=\"docs-internal-guid-7087ca2f-7fff-78cf-7704-6c2acdd815ae\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXd4BLx5Ia1kaoOfzch6VvGtcXiR4UZmqMozd5Zj_e59ebs6NPun--YMgyMXNfHa0nMJW0L9bH8Vc2GvD2IpSP7rvQ8QYyl0wEN8bHDs33LUgUm85S81SCmwF-tCNWM0rTb5jjzG-1TKpvwwvnbXZLD_lky3?key=DeDRJyT6seelEyAeFidXXWTi\" width=\"105\" height=\"20\"></strong></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "6",
                    question_en: "<p>5. Six letters A, B, C, D, E and F are written on different faces of a dice. Three positions of this dice are shown in the figure. Find the letter on the face opposite to A.<br><strong id=\"docs-internal-guid-5c41234d-7fff-e7fb-a22a-e68d9ad1b74f\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXc6fD25vCE2YQAIApRntR6omNr-VYvt6oMZIcM9Dt2l2hD6kR-v4N3nOGkc8FDiWT50a0KuHDQgaSqgYW-DXhA_yVYiVQhKFgHFK8Mha92qKmy5LDj23dvwYYUd7cvCBYY_z8rqMWAz2aF9r_KL2pPYnso?key=DeDRJyT6seelEyAeFidXXWTi\" width=\"228\" height=\"88\"></strong></p>",
                    question_hi: "<p>5. एक पासे के विभिन्न फलकों पर छ: अक्षर A, B, C, D, E और F लिखे गए हैं। नीचे चित्र में इस पासे की तीन स्थितियाँ दिखाई गई है। अक्षर A के विपरीत फलक पर कौन-सा अक्षर है?<br><strong id=\"docs-internal-guid-5c41234d-7fff-e7fb-a22a-e68d9ad1b74f\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXc6fD25vCE2YQAIApRntR6omNr-VYvt6oMZIcM9Dt2l2hD6kR-v4N3nOGkc8FDiWT50a0KuHDQgaSqgYW-DXhA_yVYiVQhKFgHFK8Mha92qKmy5LDj23dvwYYUd7cvCBYY_z8rqMWAz2aF9r_KL2pPYnso?key=DeDRJyT6seelEyAeFidXXWTi\" width=\"228\" height=\"88\"></strong></p>",
                    options_en: ["<p>C</p>", "<p>D</p>", 
                                "<p>E</p>", "<p>F</p>"],
                    options_hi: ["<p>C</p>", "<p>D</p>",
                                "<p>E</p>", "<p>F</p>"],
                    solution_en: "<p>5.(c) in the dice i and ii , letter (B , C) is common<br>So, the opposite of &lsquo;A&rsquo; is &lsquo;E&rsquo;.</p>",
                    solution_hi: "<p>5.(c) पासे i और ii में, अक्षर (B, C) उभयनिष्ठ है<br>तो, \'&lsquo;A&rsquo;\' का विपरीत &lsquo;E है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "6",
                    question_en: "<p>6. Which of the following letter-clusters will replace the question mark (?) in the given series to make it logically complete?<br>WIX, VHW, UGV, TFU, ?</p>",
                    question_hi: "<p>6. निम्नलिखित में से कौन-सा अक्षर-समूह दी गई श्रृंखला को तार्किक रूप से पूर्ण बनाने के लिए इसमें प्रश्न चिह्न (?) को प्रतिस्थापित करेगा?<br>WIX, VHW, UGV, TFU, ?</p>",
                    options_en: ["<p>TEU</p>", "<p>SEU</p>", 
                                "<p>SET</p>", "<p>SFT</p>"],
                    options_hi: ["<p>TEU</p>", "<p>SEU</p>",
                                "<p>SET</p>", "<p>SFT</p>"],
                    solution_en: "<p>6.(c)<br><strong id=\"docs-internal-guid-4dd3c525-7fff-6de1-04c9-1c7307e968d6\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXe1JelwYazXWoVq7lyRknDB92peHYrts9JnLun4nBBnIhUyY-nYHgrt_Pd7ppsjXfgSO-wHnApOnZQ819yLzAKNd6ACXK8MoenEu919VqqD9fwr19UvxQDM4PqR92ZWRSXAket2AY4eOxhLXU6ei0r62yzM?key=DeDRJyT6seelEyAeFidXXWTi\" width=\"246\" height=\"91\"></strong></p>",
                    solution_hi: "<p>6.(c)<br><strong id=\"docs-internal-guid-4dd3c525-7fff-6de1-04c9-1c7307e968d6\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXe1JelwYazXWoVq7lyRknDB92peHYrts9JnLun4nBBnIhUyY-nYHgrt_Pd7ppsjXfgSO-wHnApOnZQ819yLzAKNd6ACXK8MoenEu919VqqD9fwr19UvxQDM4PqR92ZWRSXAket2AY4eOxhLXU6ei0r62yzM?key=DeDRJyT6seelEyAeFidXXWTi\" width=\"246\" height=\"91\"></strong></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "6",
                    question_en: "<p>7. Select the option that is related to the third word in the same way as the second word is related to the first word.<br>(The words must be considered as meaningful English words and must not be related to each other based on the number of letters/number of consonants/vowels in the word)<br>Sugar : Sweet : : Habanero : ?</p>",
                    question_hi: "<p>7. उस विकल्प का चयन कीजिए जो तीसरे शब्द से उसी प्रकार संबंधित है जिस प्रकार दूसरा शब्द, पहले शब्द से संबंधित है।<br>(शब्दों को सार्थक हिंदी शब्द माना जाना चाहिए और शब्द में अक्षरों की संख्या/व्यंजनों/स्वरों की संख्या के आधार पर एक दूसरे से संबंधित नहीं होनी चाहिए)<br>शक्कर : मीठी : : शिमला मिर्च : ?</p>",
                    options_en: ["<p>Salty</p>", "<p>Bitter</p>", 
                                "<p>Sour</p>", "<p>Spicy</p>"],
                    options_hi: ["<p>नमकीन</p>", "<p>कड़वी</p>",
                                "<p>खट्टी</p>", "<p>तीखी</p>"],
                    solution_en: "<p>7.(d)<br>The relationship between Sugar and Sweet is one of characteristic taste&mdash;sugar is known for its sweetness. In the same way, Habanero is a type of chili pepper, and its distinctive taste is spicy.</p>",
                    solution_hi: "<p>7.(d)<br>चीनी और मीठी के बीच का संबंध विशिष्ट स्वाद का है- चीनी अपनी मिठास के लिए जानी जाती है। उसी तरह, शिमला मिर्च एक प्रकार की मिर्च है, और इसका विशिष्ट स्वाद मसालेदार (तीखी) होता है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "6",
                    question_en: "<p>8. Identify the figure given in the options which when put in place of ? will logically complete the series.<br><strong id=\"docs-internal-guid-bda494ad-7fff-3b7f-79eb-403a05b5a0f1\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXe4hGDFXjs_mSlpBmW9uJMKYzC5RmbLJQpMQpcUfk647-S0dB88ejm6hkw9CZqi48scmK2OXip5qwVfFhcA6WM2jWJsjL4SQLRpok1BudrnhyVm_BH5rRDOVvXEAkiH5UzJLWonn4K0Q_x0f4X9P1plHh2M?key=DeDRJyT6seelEyAeFidXXWTi\" width=\"346\" height=\"73\"></strong></p>",
                    question_hi: "<p>8. विकल्पों में दी गई उस आकृति की पहचान कीजिए जिसे ? के स्थान पर रखने पर श्रृंखला तर्कसंगत रूप से पूर्ण हो जाएगी।<br><strong id=\"docs-internal-guid-35cafc28-7fff-b4d9-63a1-10213be9757f\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXe4hGDFXjs_mSlpBmW9uJMKYzC5RmbLJQpMQpcUfk647-S0dB88ejm6hkw9CZqi48scmK2OXip5qwVfFhcA6WM2jWJsjL4SQLRpok1BudrnhyVm_BH5rRDOVvXEAkiH5UzJLWonn4K0Q_x0f4X9P1plHh2M?key=DeDRJyT6seelEyAeFidXXWTi\" width=\"346\" height=\"73\"></strong></p>",
                    options_en: ["<p><strong id=\"docs-internal-guid-702238ac-7fff-56b7-a8d8-e109423a4587\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXftWSKVhqx2GXKY4iIW6tC1-NVhe-lqsHxNKzywqyydMMVfsq0K9JUKEwnAW9IDcF0jon7ZOBUWFlEgQ7le9Po_w1gyoNyB61iJuOMbMmCKOSsjDuVCpHhn2CXTEeGYu_qHsG_Nrz1p4jAKOMe4bKwSg5C2?key=DeDRJyT6seelEyAeFidXXWTi\" width=\"72\" height=\"72\"></strong></p>", "<p><strong id=\"docs-internal-guid-a1d752af-7fff-719b-92c4-481a0b55e2af\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXd_2D4wjmPE7UcL0OwwNO8vjG0v2dYRXu7h0JCB4j1C9LAc0U3hdPwlDELI1Z65MSdQmD2wCXNJ1HhnG7jZkOaBxpGphM28qsopCqR3XgtFSJBT8JzmuIzzKTGy4ntWJGJxmUFj-0sUeXBqWhS2E70irEwE?key=DeDRJyT6seelEyAeFidXXWTi\" width=\"72\" height=\"72\"></strong></p>", 
                                "<p><strong id=\"docs-internal-guid-3d8cf79a-7fff-89ea-b37e-2412c7627da0\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfM8TWwut8sKAS8hePvJzkiLMt3MqYn5BEBH-fVWqt7HgYlYxoqlKj28E1Jv2lDuyUnRoFxwpUKDUJcX6yplM-2sLgIztJx-hDFDqbfcvq5os7FRVkd5Bo7stxe9cTvdO6CQT1b?key=DeDRJyT6seelEyAeFidXXWTi\" width=\"72\" height=\"72\"></strong></p>", "<p><strong id=\"docs-internal-guid-5fa54805-7fff-9eea-a4c7-95a8b19d0c3d\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXd-ndTgXCd2HvOj7hh67uGAzNUcyPHgWXrAZWhxI6WrZyEzmLbU1yXOiGE_bx041NiajGKZ7RPRoNV1tqX_l20JsdjF9N4gOZ5ZOBxcsEQYfzUoBdHd31tWFIp1ZSrrOay3dbA_emKBJh55svVYflKDA0L4?key=DeDRJyT6seelEyAeFidXXWTi\" width=\"72\" height=\"72\"></strong></p>"],
                    options_hi: ["<p><strong id=\"docs-internal-guid-9540045a-7fff-cc6a-ceaa-da044b9ee297\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXftWSKVhqx2GXKY4iIW6tC1-NVhe-lqsHxNKzywqyydMMVfsq0K9JUKEwnAW9IDcF0jon7ZOBUWFlEgQ7le9Po_w1gyoNyB61iJuOMbMmCKOSsjDuVCpHhn2CXTEeGYu_qHsG_Nrz1p4jAKOMe4bKwSg5C2?key=DeDRJyT6seelEyAeFidXXWTi\" width=\"72\" height=\"72\"></strong></p>", "<p><strong id=\"docs-internal-guid-fa6ca713-7fff-e600-6162-15b317eae78b\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXd_2D4wjmPE7UcL0OwwNO8vjG0v2dYRXu7h0JCB4j1C9LAc0U3hdPwlDELI1Z65MSdQmD2wCXNJ1HhnG7jZkOaBxpGphM28qsopCqR3XgtFSJBT8JzmuIzzKTGy4ntWJGJxmUFj-0sUeXBqWhS2E70irEwE?key=DeDRJyT6seelEyAeFidXXWTi\" width=\"72\" height=\"72\"></strong></p>",
                                "<p><strong id=\"docs-internal-guid-52dd7417-7fff-02e9-fb56-609ca6f08c9f\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfM8TWwut8sKAS8hePvJzkiLMt3MqYn5BEBH-fVWqt7HgYlYxoqlKj28E1Jv2lDuyUnRoFxwpUKDUJcX6yplM-2sLgIztJx-hDFDqbfcvq5os7FRVkd5Bo7stxe9cTvdO6CQT1b?key=DeDRJyT6seelEyAeFidXXWTi\" width=\"72\" height=\"72\"></strong></p>", "<p><strong id=\"docs-internal-guid-f34874d2-7fff-17c1-98de-895a4811217e\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXd-ndTgXCd2HvOj7hh67uGAzNUcyPHgWXrAZWhxI6WrZyEzmLbU1yXOiGE_bx041NiajGKZ7RPRoNV1tqX_l20JsdjF9N4gOZ5ZOBxcsEQYfzUoBdHd31tWFIp1ZSrrOay3dbA_emKBJh55svVYflKDA0L4?key=DeDRJyT6seelEyAeFidXXWTi\" width=\"72\" height=\"72\"></strong></p>"],
                    solution_en: "<p>8.(a)<br><strong id=\"docs-internal-guid-1683fa52-7fff-33ba-f006-1aee7696f6f3\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXftWSKVhqx2GXKY4iIW6tC1-NVhe-lqsHxNKzywqyydMMVfsq0K9JUKEwnAW9IDcF0jon7ZOBUWFlEgQ7le9Po_w1gyoNyB61iJuOMbMmCKOSsjDuVCpHhn2CXTEeGYu_qHsG_Nrz1p4jAKOMe4bKwSg5C2?key=DeDRJyT6seelEyAeFidXXWTi\" width=\"72\" height=\"72\"></strong></p>",
                    solution_hi: "<p>8.(a)<br><strong id=\"docs-internal-guid-1683fa52-7fff-33ba-f006-1aee7696f6f3\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXftWSKVhqx2GXKY4iIW6tC1-NVhe-lqsHxNKzywqyydMMVfsq0K9JUKEwnAW9IDcF0jon7ZOBUWFlEgQ7le9Po_w1gyoNyB61iJuOMbMmCKOSsjDuVCpHhn2CXTEeGYu_qHsG_Nrz1p4jAKOMe4bKwSg5C2?key=DeDRJyT6seelEyAeFidXXWTi\" width=\"72\" height=\"72\"></strong></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "6",
                    question_en: "<p>9. Select the set in which the numbers are related in the same way as are the numbers of the following sets.<br><strong>(NOTE</strong> : Operations should be performed on the whole numbers, without breaking down the numbers into its constituent digits. E.g. 13 &ndash; Operations on 13 such as adding /deleting /multiplying etc., to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is not allowed.)<br>(5, 80, 16)<br>(18, 108, 6)</p>",
                    question_hi: "<p>9. उस समुच्&zwj;चय का चयन कीजिए जिसमें संख्याएँ उसी प्रकार संबंधित हैं जिस प्रकार निम्नलिखित समुच्&zwj;चय की संख्याएँ संबंधित हैं।<br><strong>(नोट</strong> : संख्याओं को उसके घटक अंकों में तोड़े बिना, पूर्ण संख्याओं पर संक्रियाएं की जानी चाहिए। उदाहरण के लिए संख्या 13 को लें - 13 पर संक्रियाएं, जैसे कि 13 में जोड़ना/घटाना/गुणा करना आदि, की जा सकती हैं। 13 को 1 और 3 में तोड़ने और फिर 1 और 3 पर गणितीय संक्रियाएं करने की अनुमति नहीं है।)<br>(5, 80, 16)<br>(18, 108, 6)</p>",
                    options_en: ["<p>(15, 185, 14)</p>", "<p>(13, 91, 7)</p>", 
                                "<p>(17, 8, 135)</p>", "<p>(9, 81, 8)</p>"],
                    options_hi: ["<p>(15, 185, 14)</p>", "<p>(13, 91, 7)</p>",
                                "<p>(17, 8, 135)</p>", "<p>(9, 81, 8)</p>"],
                    solution_en: "<p>9.(b) <strong>Logic:</strong> 1st no. &times; 3rd no. = 2nd no.<br>(5, 80, 16) :- 5 &times; 16 = 80<br>(18, 108, 6) :- 18 &times; 6 = 108<br>Similarly,<br>(13, 91, 7) :- 13 &times; 7 = 91</p>",
                    solution_hi: "<p>9.(b) <strong>Logic:</strong> तर्क: पहली संख्या &times; तीसरी संख्या = दूसरी संख्या<br>(5, 80, 16) :- 5 &times; 16 = 80<br>(18, 108, 6) :- 18 &times; 6 = 108<br>इसी प्रकार,<br>(13, 91, 7) :- 13 &times; 7 = 91</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "6",
                    question_en: "<p>10. In a certain code language,<br>&lsquo;A + B&rsquo; means &lsquo;A is the mother of B&rsquo;;<br>&lsquo;A &minus; B&rsquo; means &lsquo;A is the brother of B&rsquo;;<br>&lsquo;A &times; B&rsquo; means &lsquo;A is the wife of B&rsquo; and<br>&lsquo;A &divide; B&rsquo; means &lsquo;A is the father of B&rsquo;.<br>Based on the above, how is S related to P if &lsquo;P &times; Q &divide; R &minus; S &times; A&rsquo;?</p>",
                    question_hi: "<p>10. एक निश्चित कूट भाषा में,<br>\'A + B\' का अर्थ है \'A, B की माता है\';<br>\'A &minus; B\' का अर्थ है \'A, B का भाई है\';<br>\'A &times; B\' का अर्थ है \'A, B की पत्नी है\' और<br>\'A &divide; B\' का अर्थ है \'A, B का पिता है\'।<br>उपर्युक्त के आधार पर, यदि \'P &times; Q &divide; R &minus; S &times; A\' है, तो S, P से किस प्रकार संबंधित है?</p>",
                    options_en: ["<p>Mother</p>", "<p>Daughter</p>", 
                                "<p>Son</p>", "<p>Father</p>"],
                    options_hi: ["<p>माता</p>", "<p>पुत्री</p>",
                                "<p>पुत्र</p>", "<p>पिता</p>"],
                    solution_en: "<p>10.(b)<br><strong id=\"docs-internal-guid-02cbab1b-7fff-d723-0a76-baefa7e172d5\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdFPGE8fx7ta_f26X0Y6QsqOYMyWFZAQJroSsjV5laD1thWN2Z6IOy4DCgkR7jK78gGxdLgJIWGKtTZZdColNlkdz38qGbcXgWUbdNIo57JY_UHh1kNP6TKw3i1q8nze5_UheWKSfmz86lhfxcTyew99yki?key=DeDRJyT6seelEyAeFidXXWTi\" width=\"218\" height=\"98\"></strong><br>&lsquo;S&rsquo; is the daughter of &lsquo;P&rsquo;.</p>",
                    solution_hi: "<p>10.(b)<br><strong id=\"docs-internal-guid-02cbab1b-7fff-d723-0a76-baefa7e172d5\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdFPGE8fx7ta_f26X0Y6QsqOYMyWFZAQJroSsjV5laD1thWN2Z6IOy4DCgkR7jK78gGxdLgJIWGKtTZZdColNlkdz38qGbcXgWUbdNIo57JY_UHh1kNP6TKw3i1q8nze5_UheWKSfmz86lhfxcTyew99yki?key=DeDRJyT6seelEyAeFidXXWTi\" width=\"218\" height=\"98\"></strong><br>\'S\', \'P\' की पुत्री है ।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "6",
                    question_en: "<p>11. Identify the figure given in the options, which when put in place of the question mark (?) will logically complete the series.<br><strong id=\"docs-internal-guid-b96ac1c2-7fff-192c-11ec-3cdd55d72f56\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdN7EQ2UW9SNVV70K2GSDcuZgE8lSp1NexvltAkAoNbdH5KKrVdXLZxDSKrhqtl2Du-inDf7lTp8vLTdYIrCbC50UbFYTnnhpD66DRnCs8tJ9Po7SjrwZr72l1iZjszyXc92dhWkH8Yn6g1apDrc7dzQQ4?key=DeDRJyT6seelEyAeFidXXWTi\" width=\"340\" height=\"70\"></strong></p>",
                    question_hi: "<p>11. विकल्पों में दी गई उस आकृति की पहचान कीजिए जिसे प्रश्न-चिह्न (?) के स्थान पर रखने पर श्रृंखला तार्किक रूप से पूर्ण हो जाएगी।<br><strong id=\"docs-internal-guid-f637b0d3-7fff-8bf1-6805-13126d38e7cd\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdN7EQ2UW9SNVV70K2GSDcuZgE8lSp1NexvltAkAoNbdH5KKrVdXLZxDSKrhqtl2Du-inDf7lTp8vLTdYIrCbC50UbFYTnnhpD66DRnCs8tJ9Po7SjrwZr72l1iZjszyXc92dhWkH8Yn6g1apDrc7dzQQ4?key=DeDRJyT6seelEyAeFidXXWTi\" width=\"340\" height=\"70\"></strong></p>",
                    options_en: ["<p><strong id=\"docs-internal-guid-1101fdc4-7fff-3284-8a82-c6b7975ee35c\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXerqLHDRri7zypZPWDQKTm6n4R6abiS21eimcACROL4sOpTnQ4YSySNTUTRr_jULENxRbvOwf6gjOeZJ5w3Fb7378pQSywgeH3fh3liNvabuCbd4Rr0jPkdKp3TdogfpMsIXHFERssFpmpGzRdyLzDLpH3s?key=DeDRJyT6seelEyAeFidXXWTi\" width=\"72\" height=\"72\"></strong></p>", "<p><strong id=\"docs-internal-guid-f8b621d2-7fff-4847-d870-157f2a85c803\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdIq83922ca3zp5UDZ8j5vWybZbzyW2eH9kjxW4VlX0cxo2HftqUMzZAChvJb2rwaXsbLARbHf8Wmh8w5teUCJnlAOtt4Pvm4K9prwp3N7s6WyFicnlLX8GjzWgZPh1OuUQQBBQO_5dTHFU116MlsLgdI7J?key=DeDRJyT6seelEyAeFidXXWTi\" width=\"72\" height=\"72\"></strong></p>", 
                                "<p><strong id=\"docs-internal-guid-6a03c7eb-7fff-5d37-a216-45ff1d49ed0d\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXekQcCj_EoHEk-exKCKK20Wf8BH0WA_kMAk765-4PnUvRsC8iwUYNknK-JbbNCfL3DdOwWQ3atlT_MiOG2Z5qDE1ZyJ7bM2gbPeOpWyTZ664HRsswa4pMaoATkxA_aBdfcFfgJCNgr5y9clHPRfCQNN5Clg?key=DeDRJyT6seelEyAeFidXXWTi\" width=\"72\" height=\"72\"></strong></p>", "<p><strong id=\"docs-internal-guid-41dbd8d0-7fff-739f-90de-f6c08a2d62dd\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdl4K6V_5xSgHLCUwx56enQYlAgjJoPpwQ1I5iM8GT9cDb_qSPVqTzgVEOwxvQ72LPsm_1BLzy_-lVz-EspMG8YeUCo-5sccjRX4lLty7zupNL0pQIxT5L6iFAVrxRBecxnH6wI2DE78glOWV0qmFwLtqFN?key=DeDRJyT6seelEyAeFidXXWTi\" width=\"72\" height=\"72\"></strong></p>"],
                    options_hi: ["<p><strong id=\"docs-internal-guid-7cc8bfef-7fff-a298-12fb-2fed9d63a14f\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXerqLHDRri7zypZPWDQKTm6n4R6abiS21eimcACROL4sOpTnQ4YSySNTUTRr_jULENxRbvOwf6gjOeZJ5w3Fb7378pQSywgeH3fh3liNvabuCbd4Rr0jPkdKp3TdogfpMsIXHFERssFpmpGzRdyLzDLpH3s?key=DeDRJyT6seelEyAeFidXXWTi\" width=\"72\" height=\"72\"></strong></p>", "<p><strong id=\"docs-internal-guid-8e368612-7fff-b76e-1f80-0a7613eb0bb2\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdIq83922ca3zp5UDZ8j5vWybZbzyW2eH9kjxW4VlX0cxo2HftqUMzZAChvJb2rwaXsbLARbHf8Wmh8w5teUCJnlAOtt4Pvm4K9prwp3N7s6WyFicnlLX8GjzWgZPh1OuUQQBBQO_5dTHFU116MlsLgdI7J?key=DeDRJyT6seelEyAeFidXXWTi\" width=\"72\" height=\"72\"></strong></p>",
                                "<p><strong id=\"docs-internal-guid-44bd15ac-7fff-d43e-e9d0-39b5ab20c1e4\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXekQcCj_EoHEk-exKCKK20Wf8BH0WA_kMAk765-4PnUvRsC8iwUYNknK-JbbNCfL3DdOwWQ3atlT_MiOG2Z5qDE1ZyJ7bM2gbPeOpWyTZ664HRsswa4pMaoATkxA_aBdfcFfgJCNgr5y9clHPRfCQNN5Clg?key=DeDRJyT6seelEyAeFidXXWTi\" width=\"72\" height=\"72\"></strong></p>", "<p><strong id=\"docs-internal-guid-6fb8f90b-7fff-eb6f-3802-9b3d4e4a89c2\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdl4K6V_5xSgHLCUwx56enQYlAgjJoPpwQ1I5iM8GT9cDb_qSPVqTzgVEOwxvQ72LPsm_1BLzy_-lVz-EspMG8YeUCo-5sccjRX4lLty7zupNL0pQIxT5L6iFAVrxRBecxnH6wI2DE78glOWV0qmFwLtqFN?key=DeDRJyT6seelEyAeFidXXWTi\" width=\"72\" height=\"72\"></strong></p>"],
                    solution_en: "<p>11.(d)<br><strong id=\"docs-internal-guid-6fb8f90b-7fff-eb6f-3802-9b3d4e4a89c2\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdl4K6V_5xSgHLCUwx56enQYlAgjJoPpwQ1I5iM8GT9cDb_qSPVqTzgVEOwxvQ72LPsm_1BLzy_-lVz-EspMG8YeUCo-5sccjRX4lLty7zupNL0pQIxT5L6iFAVrxRBecxnH6wI2DE78glOWV0qmFwLtqFN?key=DeDRJyT6seelEyAeFidXXWTi\" width=\"72\" height=\"72\"></strong></p>",
                    solution_hi: "<p>11.(d)<br><strong id=\"docs-internal-guid-6fb8f90b-7fff-eb6f-3802-9b3d4e4a89c2\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdl4K6V_5xSgHLCUwx56enQYlAgjJoPpwQ1I5iM8GT9cDb_qSPVqTzgVEOwxvQ72LPsm_1BLzy_-lVz-EspMG8YeUCo-5sccjRX4lLty7zupNL0pQIxT5L6iFAVrxRBecxnH6wI2DE78glOWV0qmFwLtqFN?key=DeDRJyT6seelEyAeFidXXWTi\" width=\"72\" height=\"72\"></strong></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "6",
                    question_en: "<p>12. Select the option figure in which the given figure (X) is embedded (rotation is NOT allowed).<br><strong id=\"docs-internal-guid-bedd7bb4-7fff-e380-23ae-c180970c7d5a\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfpohzzPG4NbOtA9HE1DnHnzY1mPXRJ1atJxuQ5CYM_VafBd_8SXHDCEoMftIUknE07oLPb4fjFE563UJT3qua82_t1A164wkiSRmQhkPXsPM4Cjwv6qCFcC_tB-jnevsQxqVERLkTTX6S9hRQD1DeF6XOc?key=DeDRJyT6seelEyAeFidXXWTi\" width=\"76\" height=\"92\"></strong></p>",
                    question_hi: "<p>12. उस विकल्प आकृति का चयन करें जिसमें दी गई आकृति (X) सन्निहित है (घुमाने की अनुमति नहीं है)।<br><strong id=\"docs-internal-guid-75fd9ca0-7fff-5b7e-a200-252e34308621\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfpohzzPG4NbOtA9HE1DnHnzY1mPXRJ1atJxuQ5CYM_VafBd_8SXHDCEoMftIUknE07oLPb4fjFE563UJT3qua82_t1A164wkiSRmQhkPXsPM4Cjwv6qCFcC_tB-jnevsQxqVERLkTTX6S9hRQD1DeF6XOc?key=DeDRJyT6seelEyAeFidXXWTi\" width=\"76\" height=\"92\"></strong></p>",
                    options_en: ["<p><strong id=\"docs-internal-guid-fd483324-7fff-0334-07f1-00eca6579e1d\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfECxjd51lt5PdQRuRVS1w6KEE6ceGCDDABSjiBu9RnTiytGruOodX5RaV30zqcOk7AAsW1nDMmgg_T84mIQC83ODY20b-d20MFgNajNdV6gnV1-GB5cyo29w7SOmQxLv8vsRNw2vwLffPNh37JVmjk8Cg?key=DeDRJyT6seelEyAeFidXXWTi\" width=\"72\" height=\"72\"></strong></p>", "<p><strong id=\"docs-internal-guid-c0ff5f83-7fff-1ac0-1aec-ff7d96f59140\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXem-Cy-AqxzK_mYc4VIBLU7jvsjGGok0Xx4LRSbMFJuMR9tO5RMqj521makUAvOTr16ldcI4IxMGqVzfudoXn4264NblxBU7l9NASqv_DHtKS82PVv75IQ_P9JxUimAdsEUFTownTKJFsgKuK_c5fc9mw6W?key=DeDRJyT6seelEyAeFidXXWTi\" width=\"72\" height=\"72\"></strong></p>", 
                                "<p><strong id=\"docs-internal-guid-b576b45a-7fff-2fae-66c2-a36c6fd8943c\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXctzzVJXKTUQRmJ8JMISIkfwaY5y7PKF-YcWn9tGkSlXuU68d27CP1lOgSe_ke4xxZzE3TxmtYv9q_fYc0pcamiTYZml1_0hryqd5DMom7wsyg64D8uDG8qmma6vuTVEbwo0A_Sa8MuciMFpqBGDe94ud8O?key=DeDRJyT6seelEyAeFidXXWTi\" width=\"72\" height=\"72\"></strong></p>", "<p><strong id=\"docs-internal-guid-a249dee2-7fff-45e8-2c1f-647d288e57d2\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdgP1bmUyhfENrsGFM08kktfMmYXa_KXX648pY2KXHSILYG2H5i6_ueweXorprBXzw8JAFqW7pQQWh5IrHo6CZo3vy8W40JqqbH1D0YeOYc348Ghqn3lgW9zCmI6blV5jaiyOAOO12ZZVn1Cra_-NxZabIV?key=DeDRJyT6seelEyAeFidXXWTi\" width=\"72\" height=\"72\"></strong></p>"],
                    options_hi: ["<p><strong id=\"docs-internal-guid-e5587173-7fff-76a7-0379-696dff4b00f4\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfECxjd51lt5PdQRuRVS1w6KEE6ceGCDDABSjiBu9RnTiytGruOodX5RaV30zqcOk7AAsW1nDMmgg_T84mIQC83ODY20b-d20MFgNajNdV6gnV1-GB5cyo29w7SOmQxLv8vsRNw2vwLffPNh37JVmjk8Cg?key=DeDRJyT6seelEyAeFidXXWTi\" width=\"72\" height=\"72\"></strong></p>", "<p><strong id=\"docs-internal-guid-bd5d7211-7fff-8c0d-7a34-304faae0d8af\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXem-Cy-AqxzK_mYc4VIBLU7jvsjGGok0Xx4LRSbMFJuMR9tO5RMqj521makUAvOTr16ldcI4IxMGqVzfudoXn4264NblxBU7l9NASqv_DHtKS82PVv75IQ_P9JxUimAdsEUFTownTKJFsgKuK_c5fc9mw6W?key=DeDRJyT6seelEyAeFidXXWTi\" width=\"72\" height=\"72\"></strong></p>",
                                "<p><strong id=\"docs-internal-guid-3a3a9d13-7fff-a1b1-b446-ec8d81711d6c\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXctzzVJXKTUQRmJ8JMISIkfwaY5y7PKF-YcWn9tGkSlXuU68d27CP1lOgSe_ke4xxZzE3TxmtYv9q_fYc0pcamiTYZml1_0hryqd5DMom7wsyg64D8uDG8qmma6vuTVEbwo0A_Sa8MuciMFpqBGDe94ud8O?key=DeDRJyT6seelEyAeFidXXWTi\" width=\"72\" height=\"72\"></strong></p>", "<p><strong id=\"docs-internal-guid-ba7a22a2-7fff-b9f1-fd6c-91e7de240a0b\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdgP1bmUyhfENrsGFM08kktfMmYXa_KXX648pY2KXHSILYG2H5i6_ueweXorprBXzw8JAFqW7pQQWh5IrHo6CZo3vy8W40JqqbH1D0YeOYc348Ghqn3lgW9zCmI6blV5jaiyOAOO12ZZVn1Cra_-NxZabIV?key=DeDRJyT6seelEyAeFidXXWTi\" width=\"72\" height=\"72\"></strong></p>"],
                    solution_en: "<p>12.(a)<br><strong id=\"docs-internal-guid-4ff8f0f9-7fff-d01a-89e4-ff154ce29979\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfECxjd51lt5PdQRuRVS1w6KEE6ceGCDDABSjiBu9RnTiytGruOodX5RaV30zqcOk7AAsW1nDMmgg_T84mIQC83ODY20b-d20MFgNajNdV6gnV1-GB5cyo29w7SOmQxLv8vsRNw2vwLffPNh37JVmjk8Cg?key=DeDRJyT6seelEyAeFidXXWTi\" width=\"72\" height=\"72\"></strong></p>",
                    solution_hi: "<p>12.(a)<br><strong id=\"docs-internal-guid-4ff8f0f9-7fff-d01a-89e4-ff154ce29979\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfECxjd51lt5PdQRuRVS1w6KEE6ceGCDDABSjiBu9RnTiytGruOodX5RaV30zqcOk7AAsW1nDMmgg_T84mIQC83ODY20b-d20MFgNajNdV6gnV1-GB5cyo29w7SOmQxLv8vsRNw2vwLffPNh37JVmjk8Cg?key=DeDRJyT6seelEyAeFidXXWTi\" width=\"72\" height=\"72\"></strong></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "6",
                    question_en: "<p>13. Select the option that represents the letters that when placed from left to right in the blanks below will complete the letter series.<br>_ Y Z K X K X _ Z K X _ X Y _ K X K X Y Z _ X K X Y</p>",
                    question_hi: "<p>13. उस विकल्प का चयन करें जो उन अक्षरों को निरूपित करता है जिन्&zwj;हें नीचे दिए गए रिक्त स्थानों में बाएँ से दाएँ रखे जाने पर अक्षर श्रृंखला पूरी हो जाएगी।<br>_ Y Z K X K X _ Z K X _ X Y _ K X K X Y Z _ X K X Y</p>",
                    options_en: ["<p>XYZKK</p>", "<p>XYKZK</p>", 
                                "<p>KXYKZ</p>", "<p>KYXZK</p>"],
                    options_hi: ["<p>XYZKK</p>", "<p>XYKZK</p>",
                                "<p>KXYKZ</p>", "<p>KYXZK</p>"],
                    solution_en: "<p>13.(b)<br><strong><span style=\"text-decoration: underline;\">X</span></strong> Y Z K X K/ X <strong><span style=\"text-decoration: underline;\">Y</span></strong> Z K X <strong><span style=\"text-decoration: underline;\">K</span></strong> /X Y <strong><span style=\"text-decoration: underline;\">Z</span></strong> K X K/ X Y Z <strong><span style=\"text-decoration: underline;\">K</span></strong> X K /X Y</p>",
                    solution_hi: "<p>13.(b)<br><strong><span style=\"text-decoration: underline;\">X</span></strong> Y Z K X K/ X <strong><span style=\"text-decoration: underline;\">Y</span></strong> Z K X <strong><span style=\"text-decoration: underline;\">K</span></strong> /X Y <strong><span style=\"text-decoration: underline;\">Z</span></strong> K X K/ X Y Z <strong><span style=\"text-decoration: underline;\">K</span></strong> X K /X Y</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "6",
                    question_en: "<p>14. Three statements are given, followed by three conclusions numbered I, II and III. Assuming the statements to be true, even if they seem to be at variance with commonly known facts, decide which of the conclusions logically follow(s) from the statements.<br><strong>Statements</strong> :<br>Some pineapples are oranges.<br>All oranges are bananas.<br>All bananas are plums.<br><strong>Conclusions</strong> :<br>I. Some pineapples are plums.<br>II. All oranges are plums.<br>III. No pineapple is a banana.</p>",
                    question_hi: "<p>14. तीन कथन दिए गए हैं, जिसके बाद तीन निष्कर्ष I, II और III दिए गए हैं। कथनों को सत्य मानते हुए, भले ही वे सामान्य रूप से ज्ञात तथ्यों से भिन्न प्रतीत होते हों, निर्धारित करें कि कौन सा/से निष्कर्ष कथनों का तार्किक रूप से अनुसरण करता/करते है/हैं।<br><strong>कथन</strong> :<br>कुछ अनानास, संतरे हैं।<br>सभी संतरे, केले हैं।<br>सभी केले, आलूबुखारा हैं।<br><strong>निष्&zwj;कर्ष</strong> :<br>I. कुछ अनानास, आलूबुखारा हैं।<br>II. सभी संतरे, आलूबुखारा हैं।<br>III. कोई भी अनानास, केला नहीं है।</p>",
                    options_en: ["<p>Only conclusions I and III follow</p>", "<p>Only conclusions I and II follow</p>", 
                                "<p>Only conclusion III follows</p>", "<p>Only conclusions II and III follow</p>"],
                    options_hi: ["<p>केवल निष्कर्ष I और III अनुसरण करते हैं</p>", "<p>केवल निष्कर्ष I और II अनुसरण करते हैं</p>",
                                "<p>केवल निष्कर्ष III अनुसरण करते हैं</p>", "<p>केवल निष्कर्ष II और III अनुसरण करते हैं</p>"],
                    solution_en: "<p>14.(b)<br><strong id=\"docs-internal-guid-943a9311-7fff-f8cb-801e-baf47132a990\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXemLL8aUKSWhkueU6PvCFdagnLTS546DE9ctnk6XITgp5jf3_acE6qdhEafl_dy6Bm_iM4hP9Nr2A-ZNIb6x9P7Qqd_ig8NL5OXyne4m1j4RyB6txDFCg6-sUoumEB1YSIsTQ_vKYDR1Izx2b5UQ1xKHWRp?key=DeDRJyT6seelEyAeFidXXWTi\" width=\"263\" height=\"98\"></strong><br>Only conclusions I and II follow</p>",
                    solution_hi: "<p>14.(b)<br><strong id=\"docs-internal-guid-d2772101-7fff-105c-a768-97b69adbf114\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfkzsrbegHkeWQwvHCW4iTNk46LQeeE-tZTq0BmTgP-YxODUHbsVbLB259ywztdO1Tq5D4fuYUTGtgS2pjkYqosmNEoze9qew5g8TKELCo0Xtts4LtBV-bFyotylMS-pV_MvNALV42KeNbQtzaScYwhVXSH?key=DeDRJyT6seelEyAeFidXXWTi\" width=\"241\" height=\"81\"></strong><br>केवल निष्कर्ष I और II अनुसरण करते हैं</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "6",
                    question_en: "<p>15. What will come in place of the question mark (?) in the following equation, if &lsquo;+&rsquo; and &lsquo;&ndash;&rsquo; are interchanged and &lsquo;&times;&rsquo; and &lsquo;&divide;&rsquo; are interchanged?<br>1064 &times; 2 &ndash; 653 &divide; 2 + 89 = ?</p>",
                    question_hi: "<p>15. यदि &lsquo;+&rsquo; और &lsquo;&ndash;&rsquo; को आपस में बदल दिया जाए और &lsquo;&times;&rsquo; और &lsquo;&divide;&rsquo; को आपस में बदल दिया जाए, तो निम्नलिखित समीकरण में प्रश्न चिह्न (?) के स्थान पर क्या आएगा?<br>1064 &times; 2 &ndash; 653 &divide; 2 + 89 = ?</p>",
                    options_en: ["<p>1974</p>", "<p>1749</p>", 
                                "<p>1947</p>", "<p>1497</p>"],
                    options_hi: ["<p>1974</p>", "<p>1749</p>",
                                "<p>1947</p>", "<p>1497</p>"],
                    solution_en: "<p>15.(b) <strong>Given:</strong> 1064 &times; 2 &ndash; 653 &divide; 2 + 89 = ?<br>As per the instructions given in the question, after interchanging the symbols &lsquo;+&rsquo; and &lsquo;&ndash;&rsquo; and &lsquo;&times;&rsquo; and &lsquo;&divide;&rsquo; we get.<br>1064 &divide; 2 + 653 &times; 2 - 89 = ?<br>532 + 653 &times; 2 - 89 <br>532 + 1306 - 89<br>1838 - 89 = 1749</p>",
                    solution_hi: "<p>15.(b)<strong> दिया गया है</strong>: 1064 &times; 2 &ndash; 653 &divide; 2 + 89 = ?<br>प्रश्न में दिए गए निर्देशों के अनुसार, प्रतीकों \'+\' और \'-\' तथा \'&times;\' और \'&divide;\' को आपस में बदलने पर हमें प्राप्त होता है।<br>1064 &divide; 2 + 653 &times; 2 - 89 = ?<br>532 + 653 &times; 2 - 89 <br>532 + 1306 - 89<br>1838 - 89 =1749</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "16",
                    section: "6",
                    question_en: "<p>16. Three of the following numbers are alike in a certain way and one is different. Pick the odd one out.<br><strong>(NOTE</strong> : Operations should be performed on the whole numbers, without breaking down the numbers into its constituent digits. E.g. 13 &ndash; Operations on 13 such as adding /deleting /multiplying etc., to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is not allowed.)</p>",
                    question_hi: "<p>16. निम्नलिखित संख्&zwj;याओं में से तीन किसी प्रकार से एक समान हैं और एक उनसे असंगत है। उस असंगत का चयन कीजिए।<br><strong>(नोट</strong> : गणितीय संक्रियाएं संख्&zwj;याओं को उनके घटक अंकों में तोड़े बिना पूर्ण संख्&zwj;याओं पर की जानी चाहिए। उदाहरण के लिए 13 - 13 पर की जाने वाली संक्रियाएं, जैसे जोड़ना, घटाना, गुणा करना इत्&zwj;यादि, केवल 13 पर की जा सकती हैं। 13 को 1 और 3 में तोड़ना और तब 1 और 3 पर गणितीय संक्रियाएँ करने की अनुमति नहीं है।)</p>",
                    options_en: ["<p>16 &ndash; 9 &ndash; 139</p>", "<p>25 &ndash; 7 &ndash; 175</p>", 
                                "<p>12 &ndash; 7 &ndash; 79</p>", "<p>17 &ndash; 5 &ndash; 80</p>"],
                    options_hi: ["<p>16 &ndash; 9 &ndash; 139</p>", "<p>25 &ndash; 7 &ndash; 175</p>",
                                "<p>12 &ndash; 7 &ndash; 79</p>", "<p>17 &ndash; 5 &ndash; 80</p>"],
                    solution_en: "<p>16.(b) <strong>Logic:</strong> (1st no. &times; 2nd no.) - 5 = 3rd no.<br>16 &ndash; 9 &ndash; 139 :- (16 &times; 9) - 5 = 139<br>12 &ndash; 7 &ndash; 79 :- (12 &times; 7) - 5 = 79<br>17 &ndash; 5 &ndash; 80 :- (17 &times; 5) - 5 = 80<br>But<br>25 &ndash; 7 &ndash; 175 :- (25 &times; 7) - 5 = 170 (<math display=\"inline\"><mo>&#8800;</mo></math>175)</p>",
                    solution_hi: "<p>16.(b) <strong>तर्क:</strong> (पहली संख्या &times; दूसरी संख्या) - 5 = तीसरी संख्या <br>16 &ndash; 9 &ndash; 139 :- (16 &times; 9) - 5 = 139<br>12 &ndash; 7 &ndash; 79 :- (12 &times; 7) - 5 = 79<br>17 &ndash; 5 &ndash; 80 :- (17 &times; 5) - 5 = 80<br>लेकिन<br>25 &ndash; 7 &ndash; 175 :- (25 &times; 7) - 5 = 170 (<math display=\"inline\"><mo>&#8800;</mo></math>175)</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "17",
                    section: "6",
                    question_en: "<p>17. 51 is related to 256 by certain logic. Following the same logic, 53 is related to 266. To which of the following is 55 related, following the same logic?</p>\n<p><strong>(NOTE</strong> : Operations should be performed on the whole numbers, without breaking down the numbers into its constituent digits. E.g. 13 &ndash; Operations on 13 such as adding /deleting /multiplying etc., to 13 can be&nbsp;performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is not allowed.)</p>",
                    question_hi: "<p>17. 51 एक निश्चित तर्क का अनुसरण करते हुए 256 से संबंधित है। इसी तर्क का अनुसरण करते हुए 53, 266 से संबंधित है। समान तर्क का अनुसरण करते हुए, 55 निम्नलिखित में से किससे संबंधित है?<br><strong>(नोट</strong> : संख्याओं को उनके घटक अंकों में विभक्त किए बिना पूर्ण संख्याओं पर संक्रियाएं की जानी चाहिए। उदाहरण के लिए 13 को लें- 13 पर संक्रियाएं, जैसे कि जोड़ना/घटाना/गुणा आदि 13 में की जा सकती हैं। 13 को 1 और 3 में विभक्त करना और फिर 1 और 3 पर गणितीय संक्रियाएं करने की अनुमति नहीं है।)</p>",
                    options_en: ["<p>276</p>", "<p>290</p>", 
                                "<p>249</p>", "<p>213</p>"],
                    options_hi: ["<p>276</p>", "<p>290</p>",
                                "<p>249</p>", "<p>213</p>"],
                    solution_en: "<p>17.(a) <strong>Logic:</strong> (1st number &times; 5) + 1 = 2nd number <br>(51 : 256) :- (51 &times; 5) + 1 = 256<br>(53 : 266) :- (53 &times; 5) + 1 = 266<br>Similarly<br>(55 : x) :- (55 &times; 5) + 1 = 276</p>",
                    solution_hi: "<p>17.(a) <strong>तर्क:</strong> (पहली संख्या &times; 5) +1 = दूसरी संख्या <br>(51 : 256) :- (51 &times; 5) + 1 = 256<br>(53 : 266) :- (53 &times; 5) + 1 = 266<br>उसी प्रकार<br>(55 : <math display=\"inline\"><mi>x</mi></math>) :- (55 &times; 5) + 1 = 276</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "18",
                    section: "6",
                    question_en: "<p>18. In a certain code language, \'TABU\' is written as \'SBAV\' and \'TANK&rsquo; is written as \'SBML\'. How will \'TAIL&rsquo; be written in that language?</p>",
                    question_hi: "<p>18. एक निश्चित कूट भाषा में, \'TABU\' को \'SBAV\' लिखा जाता है और &lsquo;TANK&rsquo; को \'SBML\' लिखा जाता है। इसी कूट भाषा में \'TAIL&rsquo; को किस प्रकार लिखा जाएगा?</p>",
                    options_en: ["<p>SMBH</p>", "<p>SMHB</p>", 
                                "<p>SBHM</p>", "<p>SBMH</p>"],
                    options_hi: ["<p>SMBH</p>", "<p>SMHB</p>",
                                "<p>SBHM</p>", "<p>SBMH</p>"],
                    solution_en: "<p>18.(c)<br><strong id=\"docs-internal-guid-bd05670d-7fff-25ca-5caf-b50a87e6f7fa\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXejq1JHN1498cqys42E6DByN6_McrD3IuRFhRlX2y2i_Vc20I83O7AknSwu5-LS0tGAUs9uFvpjNaJeKXNYEVpO44w6ajkDFwDsCWI372epqr3Aj3AQ1ey06vZzXS27kq8l6l4iThWBeva55R7FnyJ8pKZ5?key=DeDRJyT6seelEyAeFidXXWTi\" width=\"103\" height=\"91\"></strong><br><strong id=\"docs-internal-guid-b752060d-7fff-324e-07a0-df424d7b6bce\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcB3RNuGMQTWWCDn0VluRIyJHxuTeulyNwaw-ls1ptaDXUeu3bYCL3I1Gi4f5kD0_uUgZaJeTPRwuce1ZA2qjgLfgrxQFgrf2z16NOCNZJwVoFuQBSrL7FVs9-OmUj4SEAiluDn5T0eDZCMYfhKo5tupvig?key=DeDRJyT6seelEyAeFidXXWTi\" width=\"103\" height=\"90\"></strong><br>Similarly<br><strong id=\"docs-internal-guid-e355cdae-7fff-406d-af46-8da8b2db5b3a\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXd4T2ODzb1fq6uMBDVFsRg0jvod2jHsBUIPQQnjRFFEFjWDr_LHxvhhDs1L3aeeWfbOO21csyAgKXaNwe15l1qq63WbxWDxIbvo8_DuV-Lms8QJca4gCWMRByoeV7N0QB9-lMnkXOLWlovrGmCtsLhr5Sbq?key=DeDRJyT6seelEyAeFidXXWTi\" width=\"103\" height=\"91\"></strong></p>",
                    solution_hi: "<p>18.(c)<br><strong id=\"docs-internal-guid-9e46063f-7fff-7513-d593-6324001e33dd\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXejq1JHN1498cqys42E6DByN6_McrD3IuRFhRlX2y2i_Vc20I83O7AknSwu5-LS0tGAUs9uFvpjNaJeKXNYEVpO44w6ajkDFwDsCWI372epqr3Aj3AQ1ey06vZzXS27kq8l6l4iThWBeva55R7FnyJ8pKZ5?key=DeDRJyT6seelEyAeFidXXWTi\" width=\"103\" height=\"91\"></strong><br><strong id=\"docs-internal-guid-0ca4caab-7fff-818d-a7e1-536efa9da7d5\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcB3RNuGMQTWWCDn0VluRIyJHxuTeulyNwaw-ls1ptaDXUeu3bYCL3I1Gi4f5kD0_uUgZaJeTPRwuce1ZA2qjgLfgrxQFgrf2z16NOCNZJwVoFuQBSrL7FVs9-OmUj4SEAiluDn5T0eDZCMYfhKo5tupvig?key=DeDRJyT6seelEyAeFidXXWTi\" width=\"103\" height=\"90\"></strong><br>उसी प्रकार<br><strong id=\"docs-internal-guid-98cdc07e-7fff-8ee6-5902-74cf2d92676a\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXd4T2ODzb1fq6uMBDVFsRg0jvod2jHsBUIPQQnjRFFEFjWDr_LHxvhhDs1L3aeeWfbOO21csyAgKXaNwe15l1qq63WbxWDxIbvo8_DuV-Lms8QJca4gCWMRByoeV7N0QB9-lMnkXOLWlovrGmCtsLhr5Sbq?key=DeDRJyT6seelEyAeFidXXWTi\" width=\"103\" height=\"91\"></strong></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "19",
                    section: "6",
                    question_en: "<p>19. The position of how many letters will remain unchanged if all the letters in the word MENTOR are arranged in English alphabetical order?</p>",
                    question_hi: "<p>19. यदि MENTOR शब्द के सभी अक्षरों को अंग्रेजी वर्णानुक्रम में व्यवस्थित किया जाए, तो कितने अक्षरों की स्थिति अपरिवर्तित बनी रहेगी?</p>",
                    options_en: ["<p>Two</p>", "<p>One</p>", 
                                "<p>None</p>", "<p>Three</p>"],
                    options_hi: ["<p>दो</p>", "<p>एक</p>",
                                "<p>किसी की भी नहीं</p>", "<p>तीन</p>"],
                    solution_en: "<p>19.(b)<br><strong id=\"docs-internal-guid-f461f559-7fff-f4f0-4af3-8f1f8ee5b2f4\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXd-z0E69DcJRNapwLR2t9unpPVIAvTFqN0Mc-MuzVjYCRONDxRYSpo2g6JDO5XBp3wseXb61nDnSjJVgXjpxXJCEGJXiPqFzjWaqUUF2cBZsdj-p6Da8yJKxfONin0x0nOax_whP0vYfxOij7Qnoi9rqf4x?key=DeDRJyT6seelEyAeFidXXWTi\" width=\"154\" height=\"98\"></strong><br>Position of only one letter will be unchanged.</p>",
                    solution_hi: "<p>19.(b)<br><strong id=\"docs-internal-guid-f461f559-7fff-f4f0-4af3-8f1f8ee5b2f4\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXd-z0E69DcJRNapwLR2t9unpPVIAvTFqN0Mc-MuzVjYCRONDxRYSpo2g6JDO5XBp3wseXb61nDnSjJVgXjpxXJCEGJXiPqFzjWaqUUF2cBZsdj-p6Da8yJKxfONin0x0nOax_whP0vYfxOij7Qnoi9rqf4x?key=DeDRJyT6seelEyAeFidXXWTi\" width=\"154\" height=\"98\"></strong><br>केवल एक अक्षर की स्थिति अपरिवर्तित रहेगी ।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "20",
                    section: "6",
                    question_en: "<p>20. What will come in the place of &lsquo;?&rsquo; in the following equation, if &lsquo;&divide;&rsquo; and &lsquo;&ndash;&lsquo; are interchanged and &lsquo;&times;&rsquo; and &lsquo;+&rsquo; are interchanged?<br>37 &divide; 56 &ndash; 7 &times; 2 + 6 = ?</p>",
                    question_hi: "<p>20. निम्नलिखित समीकरण में, यदि \'&divide;\' और \'&ndash;\' को आपस में बदल दिया जाए तथा \'&times;\' और \'+\' को आपस में बदल दिया जाए, तो \'?\' के स्थान पर क्या आएगा?<br>37 &divide; 56 &ndash; 7 &times; 2 + 6 = ?</p>",
                    options_en: ["<p>41</p>", "<p>55</p>", 
                                "<p>37</p>", "<p>47</p>"],
                    options_hi: ["<p>41</p>", "<p>55</p>",
                                "<p>37</p>", "<p>47</p>"],
                    solution_en: "<p>20.(a) <strong>Given:</strong> 37 &divide; 56 &ndash; 7 &times; 2 + 6 = ?<br>As per the instructions given in question , after interchanging the symbol &lsquo;&divide;&rsquo; and &lsquo;&ndash;&lsquo;and &lsquo;&times;&rsquo; and &lsquo;+&rsquo; we get.<br>37 - 56 &divide; 7 + 2 &times; 6 = ?<br>37 - 8 + 2 &times; 6 <br>37 - 8 + 12<br>49 - 8 = 41</p>",
                    solution_hi: "<p>20.(a) <strong>दिया गया है: </strong>37 &divide; 56 &ndash; 7 &times; 2 + 6 = ?<br>प्रश्न में दिए गए निर्देशों के अनुसार, प्रतीक \'&divide;\' और \'-\' तथा \'&times;\' और \'+\' को आपस में बदलने पर हमें प्राप्त होता है।<br>37 - 56 &divide; 7 + 2 &times; 6 = ?<br>37 - 8 + 2 &times; 6 <br>37 - 8 + 12<br>49 - 8 = 41</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "21",
                    section: "6",
                    question_en: "<p>21. How many triangles are there in the figure shown below?<br><strong id=\"docs-internal-guid-cb5e083b-7fff-1dec-6625-65997081d63d\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdp1W17WakAyAP4X6V4UWsfF-ZYS187dkOIpLGeRsn87nw-l7C_HfV0y53PYFcYkRrI_QM0rfcWh-6_my_0wx45PCBELsZ2mHeaKOYEUXIqaelu_7ri9lggV4jWYT-iUAhXvEPViIV7oLsObO5moIpYH3I?key=DeDRJyT6seelEyAeFidXXWTi\" width=\"138\" height=\"131\"></strong></p>",
                    question_hi: "<p>21. नीचे दर्शायी गयी आकृति में कितने त्रिभुज हैं?<br><strong id=\"docs-internal-guid-cb5e083b-7fff-1dec-6625-65997081d63d\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdp1W17WakAyAP4X6V4UWsfF-ZYS187dkOIpLGeRsn87nw-l7C_HfV0y53PYFcYkRrI_QM0rfcWh-6_my_0wx45PCBELsZ2mHeaKOYEUXIqaelu_7ri9lggV4jWYT-iUAhXvEPViIV7oLsObO5moIpYH3I?key=DeDRJyT6seelEyAeFidXXWTi\" width=\"138\" height=\"131\"></strong></p>",
                    options_en: ["<p>12</p>", "<p>14</p>", 
                                "<p>9</p>", "<p>11</p>"],
                    options_hi: ["<p>12</p>", "<p>14</p>",
                                "<p>9</p>", "<p>11</p>"],
                    solution_en: "<p>21.(b)<br><strong id=\"docs-internal-guid-a9b50a0e-7fff-535b-0319-7aec8609e52f\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdpj3XqVRJK681JCHHMbO5tIkd6w_NrKz08LNlNljBCqXpK3CT1IYnLZxTpjf3poKzJcyYCKiG9N7YWopJWmCQ1JKoHlUz0YP4wIlsWWtYnmHzusoTSRXriSGL7b7WD-UWP88tVeKt9oTC0Z9L_YChCM0IF?key=DeDRJyT6seelEyAeFidXXWTi\" width=\"178\" height=\"168\"></strong><br>ABC, CDE, EFG , AHG, IJK, ILK, IKM, LKM, MNK, NOU, UVO, OPQ, RQS, TUS<br>There are 14 triangles.</p>",
                    solution_hi: "<p>21.(b)<br><strong id=\"docs-internal-guid-a9b50a0e-7fff-535b-0319-7aec8609e52f\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdpj3XqVRJK681JCHHMbO5tIkd6w_NrKz08LNlNljBCqXpK3CT1IYnLZxTpjf3poKzJcyYCKiG9N7YWopJWmCQ1JKoHlUz0YP4wIlsWWtYnmHzusoTSRXriSGL7b7WD-UWP88tVeKt9oTC0Z9L_YChCM0IF?key=DeDRJyT6seelEyAeFidXXWTi\" width=\"178\" height=\"168\"></strong><br>ABC, CDE, EFG , AHG, IJK, ILK, IKM, LKM, MNK, NOU, UVO, OPQ, RQS, TUS<br>14 त्रिभुज हैं ।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "22",
                    section: "6",
                    question_en: "<p>22. In a certain code language, &lsquo;DRIVE&rsquo; is coded as &lsquo;62749&rsquo; and &lsquo;RIDES&rsquo; is coded as &lsquo;76284&rsquo;. What is the code for &lsquo;V&rsquo; in that language?</p>",
                    question_hi: "<p>22. एक निश्चित कूट भाषा में, \'DRIVE\' को \'62749\' के रूप में कूटबद्ध किया गया है और \'RIDES\' को \'76284\' के रूप में कूटबद्ध किया गया है। उस भाषा में \'V\' के लिए कूट क्या है?</p>",
                    options_en: ["<p>8</p>", "<p>7</p>", 
                                "<p>9</p>", "<p>6</p>"],
                    options_hi: ["<p>8</p>", "<p>7</p>",
                                "<p>9</p>", "<p>6</p>"],
                    solution_en: "<p>22.(c) \'DRIVE\' &rarr;&nbsp;\'62749\' &hellip;&hellip;&hellip;(i)<br>\'RIDES\' &rarr;&nbsp;\'76284\' &hellip;&hellip;&hellip;. (ii)<br>In both equations letters(R, I, D, E) are common.<br>So, from equation (i) the code of &lsquo;V&rsquo; is 9.</p>",
                    solution_hi: "<p>22.(c) \'DRIVE\' &rarr;&nbsp;\'62749\' &hellip;&hellip;&hellip;(i)<br>\'RIDES\' &rarr;&nbsp;\'76284\' &hellip;&hellip;&hellip;. (ii)<br>दोनों समीकरणों में अक्षर (R, I, D, E)उभयनिष्ठ हैं<br>तो, समीकरण (i) से \'V\' का कोड 9 है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "23",
                    section: "6",
                    question_en: "23. The question contains pairs of words that are related to each other in a certain way. Three of the following four word pairs are alike as these have the same relationship and thus form a group. Which word pair is the one that DOES NOT belong to that group?<br />(The words must be considered as meaningful English words and must not be related to each other based on the number of letters/number of consonants/vowels in the word.)",
                    question_hi: "23. प्रश्न में ऐसे शब्द युग्म हैं जो एक निश्चित तरीके से एक-दूसरे से संबंधित हैं। निम्नलिखित चार शब्द युग्मों में से तीन शब्द युग्म एक समान हैं क्योंकि इनमें संबंध समान है और इस प्रकार वे एक समूह बनाते हैं। निम्नलिखित में से कौन-सा शब्द युग्म उस समूह से संबंधित नहीं है?<br />(शब्दों को अर्थपूर्ण अंग्रेजी/हिंदी शब्द माना जाना चाहिए और शब्द में अक्षरों / व्यंजनों / स्वरों की संख्या के आधार पर एक-दूसरे से संबंधित नहीं होने चाहिए।)",
                    options_en: [" Tired - exhausted", " Loud - noisy", 
                                " Close - open", " Honest - truthful"],
                    options_hi: [" थका - थका माँदा", " ऊँचा स्वर - शोरगुल",
                                " बंद - खुला", " ईमानदार - सत्यनिष्ठ"],
                    solution_en: "23.(c) words (Tired - exhausted , Loud - noisy, Honest - truthful) are synonym each other  but (Close - open)<br /> is an antonym. ",
                    solution_hi: "23.(c) <br />शब्द (थका - थका माँदा, ऊँचा स्वर - शोरगुल, ईमानदार - सत्यनिष्ठ) एक दूसरे के पर्यायवाची हैं लेकिन (बंद - खुला)<br /> एक दूसरे का विलोम है",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "24",
                    section: "6",
                    question_en: "<p>24. A square sheet of paper is folded along the dotted line successively along the directions shown and is then punched in the last as shown. How would the paper look when unfolded?<br><strong id=\"docs-internal-guid-46dabe72-7fff-46f8-2806-57b1ac800522\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdm6FVomHiuYut924SyxxAQW5mGo1sfVlLZktxHm_sBUT73-gQGS_952wo9Z0649ZsSKpW45coFAhjnkbmG8m5kTN0x95TUS7bX_rgPLKuLnvUY1ul1v0t76SwBa2-kaG_OBng-_NmQpwWHzOdgehLye5f7?key=DeDRJyT6seelEyAeFidXXWTi\" width=\"215\" height=\"67\"></strong></p>",
                    question_hi: "<p>24. कागज की एक वर्गाकार शीट को बिंदीदार रेखा के साथ दिखाई गई दिशाओं के अनुसार क्रमिक रूप से मोड़ा जाता है और फिर दिखाए गए चित्र के अनुसार अंत में पंच किया जाता है। खुलने पर कागज कैसा दिखेगा?<br><strong id=\"docs-internal-guid-f098c64f-7fff-b5cc-aa84-a94af2929f27\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdm6FVomHiuYut924SyxxAQW5mGo1sfVlLZktxHm_sBUT73-gQGS_952wo9Z0649ZsSKpW45coFAhjnkbmG8m5kTN0x95TUS7bX_rgPLKuLnvUY1ul1v0t76SwBa2-kaG_OBng-_NmQpwWHzOdgehLye5f7?key=DeDRJyT6seelEyAeFidXXWTi\" width=\"215\" height=\"67\"></strong></p>",
                    options_en: ["<p><strong id=\"docs-internal-guid-44733222-7fff-5ed7-74f1-8bfaf5228fc2\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfYtenX8nyksRO46nkq2ArzNyluIhW105jWso8TUQKOCY73vEhY5Ti4j9jkZsneEnnL9L97ozCSEcij8qDuk58WR5-tINLUhwaEB1dQ2R6rIeODJy0IB_b6Yg_ZRoIXbagD-uMB_uh4Oa3jiZ6ePZBYp8Q?key=DeDRJyT6seelEyAeFidXXWTi\" width=\"72\" height=\"72\"></strong></p>", "<p><strong id=\"docs-internal-guid-5aec7442-7fff-75e2-8e54-949293b80549\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXeeWcW-RwS3WNob9FlpuLL2-rvbIyjLxiZlj_eyoobSLHPr5bgEl5DlFcx7zc8AvY2jezUARsGok2nsI9dIqVu1GuizZRr-OEwPKawkJd5C6BEeHTvyYc89xlNOjlJjC6yZcIELzf3jfER4jju2-mk77wde?key=DeDRJyT6seelEyAeFidXXWTi\" width=\"72\" height=\"72\"></strong></p>", 
                                "<p><strong id=\"docs-internal-guid-7848b0ff-7fff-8c4d-5229-4cef0157cf99\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfBUxUWKcuYJIWggklAkv6F8DPFkvA_I1wfJaJcPbN_ONHsGrrypwpzP6Va5RSw5_GiYTHO0Cir9uH0ZXzh7KKGp2fN_18qXui54Lwdo9e7NYBiIM99_VmHqKR4OPCccMXTDRQWbiRbtrFj5ihFmdcGB1kI?key=DeDRJyT6seelEyAeFidXXWTi\" width=\"72\" height=\"72\"></strong></p>", "<p><strong id=\"docs-internal-guid-d299de78-7fff-a2d0-7c37-abfeb2c7b5b4\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcHmxvz-qTzUeLHLIrjGSjOowFQQ-4W-fVsL7u8BH51Gh-1Q6kUWZVk8szbo7JB1c8GMpEeGLGD9W2mnQpSa1aCqBw-BVWU3G-lzh8UOOPbwdKC8wLTqnecl3gSY7jT3P_bNxT-EI3iKkEUflDhgDcL6XJr?key=DeDRJyT6seelEyAeFidXXWTi\" width=\"72\" height=\"72\"></strong></p>"],
                    options_hi: ["<p><strong id=\"docs-internal-guid-c0b37afc-7fff-d4fd-392c-a5c1d3bc4678\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfYtenX8nyksRO46nkq2ArzNyluIhW105jWso8TUQKOCY73vEhY5Ti4j9jkZsneEnnL9L97ozCSEcij8qDuk58WR5-tINLUhwaEB1dQ2R6rIeODJy0IB_b6Yg_ZRoIXbagD-uMB_uh4Oa3jiZ6ePZBYp8Q?key=DeDRJyT6seelEyAeFidXXWTi\" width=\"72\" height=\"72\"></strong></p>", "<p><strong id=\"docs-internal-guid-f2334774-7fff-f170-815f-48ac30ba70a7\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXeeWcW-RwS3WNob9FlpuLL2-rvbIyjLxiZlj_eyoobSLHPr5bgEl5DlFcx7zc8AvY2jezUARsGok2nsI9dIqVu1GuizZRr-OEwPKawkJd5C6BEeHTvyYc89xlNOjlJjC6yZcIELzf3jfER4jju2-mk77wde?key=DeDRJyT6seelEyAeFidXXWTi\" width=\"72\" height=\"72\"></strong></p>",
                                "<p><strong id=\"docs-internal-guid-4e241cc4-7fff-0a26-8aa7-5b9bfb267927\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfBUxUWKcuYJIWggklAkv6F8DPFkvA_I1wfJaJcPbN_ONHsGrrypwpzP6Va5RSw5_GiYTHO0Cir9uH0ZXzh7KKGp2fN_18qXui54Lwdo9e7NYBiIM99_VmHqKR4OPCccMXTDRQWbiRbtrFj5ihFmdcGB1kI?key=DeDRJyT6seelEyAeFidXXWTi\" width=\"72\" height=\"72\"></strong></p>", "<p><strong id=\"docs-internal-guid-d8e311eb-7fff-27e7-a057-3900c78ef03a\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcHmxvz-qTzUeLHLIrjGSjOowFQQ-4W-fVsL7u8BH51Gh-1Q6kUWZVk8szbo7JB1c8GMpEeGLGD9W2mnQpSa1aCqBw-BVWU3G-lzh8UOOPbwdKC8wLTqnecl3gSY7jT3P_bNxT-EI3iKkEUflDhgDcL6XJr?key=DeDRJyT6seelEyAeFidXXWTi\" width=\"72\" height=\"72\"></strong></p>"],
                    solution_en: "<p>24.(b)<br><strong id=\"docs-internal-guid-4e301a09-7fff-4305-5a94-45b17b731241\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXeeWcW-RwS3WNob9FlpuLL2-rvbIyjLxiZlj_eyoobSLHPr5bgEl5DlFcx7zc8AvY2jezUARsGok2nsI9dIqVu1GuizZRr-OEwPKawkJd5C6BEeHTvyYc89xlNOjlJjC6yZcIELzf3jfER4jju2-mk77wde?key=DeDRJyT6seelEyAeFidXXWTi\" width=\"72\" height=\"72\"></strong></p>",
                    solution_hi: "<p>24.(b)<br><strong id=\"docs-internal-guid-4e301a09-7fff-4305-5a94-45b17b731241\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXeeWcW-RwS3WNob9FlpuLL2-rvbIyjLxiZlj_eyoobSLHPr5bgEl5DlFcx7zc8AvY2jezUARsGok2nsI9dIqVu1GuizZRr-OEwPKawkJd5C6BEeHTvyYc89xlNOjlJjC6yZcIELzf3jfER4jju2-mk77wde?key=DeDRJyT6seelEyAeFidXXWTi\" width=\"72\" height=\"72\"></strong></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "25",
                    section: "18",
                    question_en: "<p>25. Select the option that is related to the fifth letter-cluster in the same way as the second letter-cluster is related to the first letter-cluster and the fourth letter-cluster is related to the third letter-cluster.<br>JAP : BSH : : NDZ : FVR : : LCX : ?</p>",
                    question_hi: "<p>25. उस विकल्प का चयन करें जो पाँचवें अक्षर-समूह से उसी प्रकार संबंधित है जिस प्रकार दूसरा अक्षर-समूह पहले अक्षर-समूह से संबंधित है और चौथा अक्षर-समूह तीसरे अक्षर-समूह से संबंधित है।<br>JAP : BSH : : NDZ : FVR : : LCX : ?</p>",
                    options_en: ["<p>EVP</p>", "<p>DUP</p>", 
                                "<p>DVP</p>", "<p>EUP</p>"],
                    options_hi: ["<p>EVP</p>", "<p>DUP</p>",
                                "<p>DVP</p>", "<p>EUP</p>"],
                    solution_en: "<p>25.(b)<br><strong id=\"docs-internal-guid-f701fd58-7fff-edd9-ba30-d138bfb7e5ba\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXd2xuTEFPYwIB7_282yzMKa2k7yHr6bAjJ7az2_FJ58Q3H2vzGGSShuDH4WfYQQqOqjUp0HgKwx4-_8N4lI_2mX_6BFPIgODiOoslnxhW8EnC9nXSzorYP0_FY-NV4rRfA6sgVA?key=DeDRJyT6seelEyAeFidXXWTi\" width=\"120\" height=\"93\"></strong><br><strong id=\"docs-internal-guid-ed509124-7fff-f650-823f-ccdee382cade\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXf0qo-noO1W6gLwYo_4Hx3VHRYwcSm18BFsGf7WgclAPMAYT6sFwAxB8Q2rV-1KYaPd8PpMD1GgNZZknwmV2VGqaemZgg1fTW0JQuW3jXd0Vk-cdw7lOms_cxlsBoPGY76PQSrmVg?key=DeDRJyT6seelEyAeFidXXWTi\" width=\"119\" height=\"93\"></strong><br>Similarly<br><strong id=\"docs-internal-guid-30eb1e7a-7fff-0097-8130-6bcec2518085\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdKN5oYbLUwHzg84pN-p2QiMnuYz0uWASQq4Zos9P2mVeN6hhXeFMiQkGEnDpkuvuhH0_3TAf3WbiHmWQ2Jc6A9fvIEnjzoWbrPIpM6POmx40-Oe342rje0QAKgBULhO7iWaaLh?key=DeDRJyT6seelEyAeFidXXWTi\" width=\"119\" height=\"93\"></strong></p>",
                    solution_hi: "<p>25.(b)<br><strong id=\"docs-internal-guid-7243a071-7fff-1d63-e510-f6fab58020ff\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXd2xuTEFPYwIB7_282yzMKa2k7yHr6bAjJ7az2_FJ58Q3H2vzGGSShuDH4WfYQQqOqjUp0HgKwx4-_8N4lI_2mX_6BFPIgODiOoslnxhW8EnC9nXSzorYP0_FY-NV4rRfA6sgVA?key=DeDRJyT6seelEyAeFidXXWTi\" width=\"120\" height=\"93\"></strong><br><strong id=\"docs-internal-guid-7ad224e1-7fff-2647-cccf-d6da5128f610\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXf0qo-noO1W6gLwYo_4Hx3VHRYwcSm18BFsGf7WgclAPMAYT6sFwAxB8Q2rV-1KYaPd8PpMD1GgNZZknwmV2VGqaemZgg1fTW0JQuW3jXd0Vk-cdw7lOms_cxlsBoPGY76PQSrmVg?key=DeDRJyT6seelEyAeFidXXWTi\" width=\"119\" height=\"93\"></strong><br>इसी तरह<br><strong id=\"docs-internal-guid-f8710743-7fff-31a9-4db9-edd9d77bc212\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdKN5oYbLUwHzg84pN-p2QiMnuYz0uWASQq4Zos9P2mVeN6hhXeFMiQkGEnDpkuvuhH0_3TAf3WbiHmWQ2Jc6A9fvIEnjzoWbrPIpM6POmx40-Oe342rje0QAKgBULhO7iWaaLh?key=DeDRJyT6seelEyAeFidXXWTi\" width=\"119\" height=\"93\"></strong></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "26",
                    section: "18",
                    question_en: "26. Find out why white silver chloride turns grey in sunlight.",
                    question_hi: "26. पता कीजिए कि श्वेत रंग का सिल्वर क्लोराइड सूर्य के प्रकाश में धूसर रंग का क्यों हो जाता है।",
                    options_en: [" Due to rusting of silver in presence of oxygen", " Due to redox reaction", 
                                " Due to the decomposition of silver chloride into silver and chlorine by light", " Due to the displacement of silver chloride to silver oxide"],
                    options_hi: [" ऑक्सीजन की उपस्थिति में सिल्वर में जंग लगने के कारण", " अपचयोपचय (रेडॉक्स) अभिक्रिया के कारण",
                                " प्रकाश द्वारा सिल्वर क्लोराइड के सिल्वर और क्लोरीन में अपघटन के कारण", " सिल्वर क्लोराइड के सिल्वर ऑक्साइड में विस्थापन के कारण"],
                    solution_en: "26.(c) Silver chloride (AgCl) is a photosensitive compound, which means it reacts to light by decomposing. When silver chloride is exposed to sunlight, the energy from the light causes the compound to break down into silver and chlorine ions. The silver ions combine with other substances in the environment to form a white precipitate, which gives the appearance of the silver chloride turning white.",
                    solution_hi: "26.(c) सिल्वर क्लोराइड  (AgCl) एक प्रकाश-संवेदनशील यौगिक है, जिसका अर्थ है कि  यह प्रकाश के प्रति अपघटन द्वारा अभिक्रिया करता है। जब सिल्वर क्लोराइड सूर्य के प्रकाश के संपर्क में  लाया जाता है, तो प्रकाश से प्राप्त ऊर्जा यौगिक को सिल्वर और क्लोरीन आयनों में विघटित कर देती है। सिल्वर  आयन वातावरण में मौजूद अन्य पदार्थों के साथ संयोजित होकर एक सफेद अवक्षेप बनाते हैं, जिससे ऐसा प्रतीत होता है कि सिल्वर  क्लोराइड सफेद हो गया है।",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "27",
                    section: "18",
                    question_en: "<p>27. Who founded the Brahmo Samaj in Calcutta in 1828?</p>",
                    question_hi: "<p>27. 1828 में कलकत्ता में ब्रह्म-समाज की स्थापना किसने की थी?</p>",
                    options_en: ["<p>Ram Mohan Roy</p>", "<p>Swami Vivekanand</p>", 
                                "<p>Gopal Krishna Gokhale</p>", "<p>Ramabai Ranade</p>"],
                    options_hi: ["<p>राम मोहन राय</p>", "<p>स्वामी विवेकानंद</p>",
                                "<p>गोपाल कृष्ण गोखले</p>", "<p>रमाबाई रानाडे</p>"],
                    solution_en: "<p>27.(a) <strong>Ram Mohan Roy.</strong> He was dedicated to abolishing practices like Sati and the caste system. He is known as the \"Father of the Indian Renaissance\" for his role in modernizing Indian society. Important organizations and their founders: Arya Samaj - Swami Dayanand Saraswati, Ramakrishna Mission - Swami Vivekanand, Prarthana Samaj - Dadoba Pandurang and Atmaram Pandurang.</p>",
                    solution_hi: "<p>27.(a) <strong>राम मोहन राय।</strong> वे सती प्रथा और जाति व्यवस्था जैसी प्रथाओं को समाप्त करने के लिए समर्पित थे। भारतीय समाज को आधुनिक बनाने में उनकी भूमिका के लिए उन्हें \"भारतीय पुनर्जागरण के जनक\" के रूप में जाना जाता है। महत्त्वपूर्ण संगठन एवं उनके संस्थापक: आर्य समाज - स्वामी दयानंद सरस्वती, रामकृष्ण मिशन - स्वामी विवेकानंद, प्रार्थना समाज - दादोबा पांडुरंग और आत्माराम पांडुरंग।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "28",
                    section: "18",
                    question_en: "<p>28. Which of the following are west flowing rivers?</p>",
                    question_hi: "<p>28. निम्नलिखित में से कौन-सी पश्चिम की ओर बहने वाली नदियाँ हैं?</p>",
                    options_en: ["<p>Godavari and Mahanadi</p>", "<p>Krishna and Kaveri</p>", 
                                "<p>Ganga and Yamuna</p>", "<p>Mahi and Sabarmati</p>"],
                    options_hi: ["<p>गोदावरी और महानदी</p>", "<p>कृष्णा और कावेरी</p>",
                                "<p>गंगा और यमुना</p>", "<p>माही और साबरमती</p>"],
                    solution_en: "<p>28.(d) <strong>Mahi and Sabarmati.</strong> Mahi River originates from Vindhyachal Hills, Madhya Pradesh and meets in Bay of Khambhat. Sabarmati river originates from Aravalli Hills, Rajasthan and meets in Bay of Khambhat in Arabian sea. Major West-flowing rivers - Indus, Narmada, and Tapi. Major East-flowing rivers: Ganga, Brahmaaputra, Mahanadi, Krishna, and Godavari. Major peninsular rivers - Mahanadi River, Godavari River, Krishna River, Kaveri, Narmada River, Tapti River.</p>",
                    solution_hi: "<p>28.(d) <strong>माही और साबरमती</strong>। माही नदी मध्य प्रदेश के विंध्याचल पहाड़ियों से निकलती है और खंभात की खाड़ी में मिलती है। साबरमती नदी राजस्थान के अरावली पहाड़ियों से निकलती है और अरब सागर में खंभात की खाड़ी में मिलती है। पश्चिम की ओर बहने वाली प्रमुख नदियाँ - सिंधु, नर्मदा और तापी। पूर्व की ओर बहने वाली प्रमुख नदियाँ: गंगा, ब्रह्मपुत्र, महानदी, कृष्णा और गोदावरी। मुख्य प्रायद्वीपीय नदियाँ - महानदी, गोदावरी, कृष्णा, कावेरी, नर्मदा, ताप्ती।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "29",
                    section: "18",
                    question_en: "<p>29. Part IV A of the Indian Constitution was inserted in the Indian Constitution through _____________ Constitutional Amendment Act.</p>",
                    question_hi: "<p>29. भारतीय संविधान के भाग IV A को भारतीय संविधान में _____________ संवैधानिक संशोधन अधिनियम के माध्यम से शामिल किया गया था।</p>",
                    options_en: ["<p>44th</p>", "<p>46th</p>", 
                                "<p>48th</p>", "<p>42nd</p>"],
                    options_hi: ["<p>44 वें</p>", "<p>46 वें</p>",
                                "<p>48 वें</p>", "<p>42 वें</p>"],
                    solution_en: "<p>29.(d) <strong>42nd.</strong> The fundamental duties were incorporated in Part IV-A of the Constitution by the 42nd Constitutional Amendment Act, 1976 on the recommendations of the Swaran Singh Committee. Initially, there were 10 duties, but an additional duty was added by the 86th Constitutional Amendment Act, 2002, bringing the total to 11 duties.</p>",
                    solution_hi: "<p>29.(d) <strong>42 वें</strong>। स्वर्ण सिंह समिति की सिफारिशों पर 42वें संविधान संशोधन अधिनियम, 1976 द्वारा संविधान के भाग IV-A में मौलिक कर्तव्यों को शामिल किया गया था। प्रारंभ में, 10 मौलिक कर्तव्य थे, लेकिन 86वें संविधान संशोधन अधिनियम, 2002 के द्वारा एक अतिरिक्त मौलिक कर्तव्य को जोड़ा गया, जिससे मौलिक कर्तव्यों की कुल संख्या 11 हो गई है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "30",
                    section: "18",
                    question_en: "<p>30. Sido and Kanhu were leaders of which of the following tribal rebellions?</p>",
                    question_hi: "<p>30. सिद्धू और कान्हू निम्नलिखित में से कौन-से आदिवासी विद्रोह के नेता थे?</p>",
                    options_en: ["<p>Moplah Rebellion</p>", "<p>Santhal Rebellion</p>", 
                                "<p>Kol Rebellion</p>", "<p>Munda Rebellion</p>"],
                    options_hi: ["<p>मोपला विद्रोह</p>", "<p>संथाल विद्रोह</p>",
                                "<p>कोल विद्रोह</p>", "<p>मुंडा विद्रोह</p>"],
                    solution_en: "<p>30.(b) <strong>Santhal Rebellion</strong> (Hul revolt) began in 1855, led by Sidhu, Kanhu, Chand, Bhairav, and their sisters Phulo and Jhano. The Malabar rebellion (Moplah rebellion) was an armed uprising by the Mappila Muslims of Kerala in 1921. The Kol rebellion (1829-39) was led by Buddhu Bhagat, Madara Mahato, and Joa Bhagat in the Chota Nagpur region. The Munda Rebellion, led by Birsa Munda, took place in the Ranchi area in 1899-1900.</p>",
                    solution_hi: "<p>30.(b) <strong>संथाल विद्रोह</strong> (हूल विद्रोह) 1855 में शुरू हुआ, जिसका नेतृत्व सिद्धू, कान्हू, चांद, भैरव और उनकी बहनों फूलो और झानो ने किया। मालाबार विद्रोह (मोपला विद्रोह) 1921 में केरल के मप्पिला मुसलमानों द्वारा किया गया एक सशस्त्र विद्रोह था। कोल विद्रोह (1829-39) का नेतृत्व छोटा नागपुर क्षेत्र में बुद्धू भगत, मदारा महतो और जोआ भगत ने किया था। बिरसा मुंडा के नेतृत्व में मुंडा विद्रोह 1899-1900 में रांची क्षेत्र में हुआ था।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "31",
                    section: "18",
                    question_en: "<p>31. The outermost part of the Earth is known as __________.</p>",
                    question_hi: "<p>31. पृथ्वी के सबसे बाहरी भाग को __________ के रूप में जाना जाता है।</p>",
                    options_en: ["<p>volcano</p>", "<p>the crust</p>", 
                                "<p>the core</p>", "<p>the mantle</p>"],
                    options_hi: ["<p>ज्वालामुखी (Volcano)</p>", "<p>भूपर्पटी (Crust)</p>",
                                "<p>क्रोड (Core)</p>", "<p>आच्छादन (Mantle)</p>"],
                    solution_en: "<p>31.(b) <strong>the crust.</strong> The Earth\'s crust is the thinnest of all its layers, about 35 km thick on the continental masses and only 5 km on the ocean floors. The main minerals in the continental crust are silica and alumina, while the oceanic crust consists primarily of silica and magnesium. Beneath the crust lies the mantle, which extends to a depth of 2900 km. The innermost layer is the core, with a radius of about 3500 km, made mostly of nickel and iron.</p>",
                    solution_hi: "<p>31.(b) <strong>भूपर्पटी (Crust)।</strong> पृथ्वी की भूपर्पटी इसकी सभी परतों में सबसे पतली है, महाद्वीपीय भागों पर इसकी मोटाई लगभग 35 किमी तथा महासागरीय तल पर केवल 5 किमी है। महाद्वीपीय भूपर्पटी में मुख्य खनिज सिलिका और एल्यूमिना हैं, जबकि महासागरीय भूपर्पटी में मुख्य रूप से सिलिका और मैग्नीशियम पाए जाते हैं। भूपर्पटी के नीचे मेंटल है, जो 2900 किमी की गहराई तक फैला हुआ है। सबसे भीतरी परत कोर है, जिसकी त्रिज्या लगभग 3500 किमी है, जो मुख्यतः निकेल और आयरन से बनी है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "32",
                    section: "18",
                    question_en: "<p>32. The Upper House of a State Legislature is known as the ____________.</p>",
                    question_hi: "<p>32. राज्य विधानमंडल के ऊपरी सदन को __________ के रूप में जाना जाता है।</p>",
                    options_en: ["<p>Vidhan Parishad</p>", "<p>Rajya Sabha</p>", 
                                "<p>Lok Sabha</p>", "<p>Vidhan Sabha</p>"],
                    options_hi: ["<p>विधान परिषद</p>", "<p>राज्य सभा</p>",
                                "<p>लोक सभा</p>", "<p>विधान सभा</p>"],
                    solution_en: "<p>32.(a) <strong>Vidhan Parishad.</strong> A State Legislature that has two houses includes: Lower House (State Legislative Assembly or Vidhan Sabha) and Upper House (State Council or Vidhan Parishad). State Legislative Council (Vidhan Parishad): Article 169 of the Constitution provides the provisions for abolition or creation of Legislative Councils in States. Article 171: Composition of the Legislative Councils in a state should have: No more than one-third of the total strength of the State Assembly and At least 40 members.</p>",
                    solution_hi: "<p>32.(a) <strong>विधान परिषद।</strong> एक राज्य विधानमंडल जिसमें दो सदन होते हैं, उनमें निम्न सदन (राज्य विधान सभा या विधान सभा) और उच्च सदन (राज्य परिषद या विधान परिषद) शामिल हैं। राज्य विधान परिषद (विधान परिषद): संविधान का अनुच्छेद 169 राज्यों में विधान परिषदों के उन्मूलन या निर्माण के लिए प्रावधान प्रदान करता है। अनुच्छेद 171: किसी राज्य में विधान परिषदों की संरचना: राज्य विधानसभा की कुल संख्या के एक तिहाई से अधिक तथा 40 सदस्यों से कम नहीं होना चाहिए।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "33",
                    section: "18",
                    question_en: "<p>33. According to the Ministry of Road Transport and Highways&rsquo; annual report (2020-2021), which position does India have in terms of road networks in the world?</p>",
                    question_hi: "<p>33. सड़क परिवहन और राजमार्ग मंत्रालय की वार्षिक रिपोर्ट (2020-2021) के अनुसार विश्व में सड़क नेटवर्क के संदर्भ में भारत का कौन-सा स्थान है?</p>",
                    options_en: ["<p>First</p>", "<p>Second</p>", 
                                "<p>Fourth</p>", "<p>Third</p>"],
                    options_hi: ["<p>चौथा</p>", "<p>दूसरा</p>",
                                "<p>चतुर्थ</p>", "<p>तीसरा</p>"],
                    solution_en: "<p>33.(b) <strong>Second.</strong> India has the second largest road network in the world after the USA. India&rsquo;s road network stands at over 63,71,847 km. This comprises National Highways, Expressways, State Highways, Major District Roads, other District Roads and Village Roads (Ministry of Road Transport &amp; Highways Annual Report FY 2021-22).</p>",
                    solution_hi: "<p>33.(b) ) <strong>दूसरा।</strong> भारत में अमेरिका के बाद विश्व का दूसरा सबसे बड़ा सड़क नेटवर्क है। भारत का सड़क नेटवर्क 63,71,847 किलोमीटर से अधिक लंबा है। इसमें राष्ट्रीय राजमार्ग, एक्सप्रेसवे, राज्य राजमार्ग, प्रमुख ज़िला सड़कें, अन्य ज़िला सड़कें और ग्रामीण सड़कें शामिल हैं (सड़क परिवहन और राजमार्ग मंत्रालय की वार्षिक रिपोर्ट वित्त वर्ष 2021-22)।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "34",
                    section: "18",
                    question_en: "<p>34. Why does the milkman add a very small amount of baking soda to fresh milk?</p>",
                    question_hi: "<p>34. दूधवाला ताजे दूध में बहुत कम मात्रा में बेकिंग सोडा क्यों मिलाता है?</p>",
                    options_en: ["<p>To reduce the pH of the fresh milk from 6 to slightly more acidic</p>", "<p>To increase the pH of the fresh milk from 6 to slightly alkaline</p>", 
                                "<p>To maintain the pH of the fresh milk at 6 for a longer time</p>", "<p>To reduce the pH of the fresh milk from 6 to slightly alkaline</p>"],
                    options_hi: ["<p>ताजे दूध का pH, 6 से घटाकर थोड़ा अधिक अम्लीय करने के लिए</p>", "<p>ताजे दूध का pH, 6 से बढ़ाकर थोड़ा क्षारीय करने के लिए</p>",
                                "<p>ताजे दूध का pH लंबे समय तक 6 पर बनाए रखने के लिए</p>", "<p>ताजे दूध का pH, 6 से घटाकर थोड़ा क्षारीय करने के लिए</p>"],
                    solution_en: "<p>34.(b) A milkman adds a small amount of baking soda to fresh milk to prevent its acidification, allowing him to store it for a longer time. This is because milk in an alkaline condition does not curdle easily. Sodium bicarbonate, commonly known as baking soda or cooking soda, has the chemical formula NaHCO<sub>3</sub>. Uses: Cooking - Acts as a leavening agent in baking, helping dough rise. Fire extinguishing: Used in some types of fire extinguishers to put out small grease or electrical fires.</p>",
                    solution_hi: "<p>34.(b) एक दूधवाला ताजे दूध को अम्लीय होने से बचाने के लिए उसमें थोड़ी मात्रा में बेकिंग सोडा मिला देता है, जिससे वह उसे लम्बे समय तक संग्रहीत कर सकता है। ऐसा इसलिए है क्योंकि क्षारीय अवस्था में दूध आसानी से नहीं फटता है। सोडियम बाइकार्बोनेट, जिसे आमतौर पर बेकिंग सोडा या कुकिंग सोडा के रूप में जाना जाता है, का रासायनिक सूत्र NaHCO<sub>3</sub> है। उपयोग: खाना बनाना - बेकिंग में एक खमीर घटक के रूप में कार्य करता है, जिससे आटा फूलने में मदद मिलती है। आग बुझाना : कुछ प्रकार के अग्निशामक यंत्रों में इसका उपयोग छोटी-मोटी ग्रीस या बिजली की आग को बुझाने के लिए किया जाता है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "35",
                    section: "18",
                    question_en: "<p>35. In the context of periodicity, a unit called picometre is used to measure the __________.</p>",
                    question_hi: "<p>35. आवर्तता (पीरियोडिसिटी) के संदर्भ में ________ को मापने के लिए पिकोमीटर नामक इकाई का उपयोग किया जाता है।</p>",
                    options_en: ["<p>atomic radius</p>", "<p>molar mass</p>", 
                                "<p>atomic density</p>", "<p>spin quantum number</p>"],
                    options_hi: ["<p>परमाणु त्रिज्या</p>", "<p>मोलर द्रव्यमान</p>",
                                "<p>परमाणु घनत्व</p>", "<p>स्पिन क्वांटम संख्या</p>"],
                    solution_en: "<p>35.(a) <strong>atomic radius</strong> (Atomic Radii). It is the total distance from the nucleus of an atom to the outermost orbital of its electron. In chemistry, the molar mass of a chemical compound is defined as the ratio between the mass and the amount of substance of any sample of the compound. The atomic number density is the number of atoms of a given type per unit volume (V, cm<sup>3</sup>) of the material.</p>",
                    solution_hi: "<p>35.(a) <strong>परमाणु त्रिज्या।</strong> यह किसी परमाणु के नाभिक से उसके इलेक्ट्रॉन की सबसे बाहरी कक्षा तक की कुल दूरी है। रसायन विज्ञान में, किसी रासायनिक यौगिक के मोलर द्रव्यमान को यौगिक के किसी भी सैम्पल के द्रव्यमान और पदार्थ की मात्रा के बीच के अनुपात के रूप में परिभाषित किया जाता है। परमाणु संख्या घनत्व पदार्थ के प्रति इकाई आयतन (V, cm<sup>3</sup>) में किसी दिए गए प्रकार के परमाणुओं की संख्या है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "36",
                    section: "18",
                    question_en: "<p>36. Who among the following was the Former Secretary (HRD), Information and Broadcasting (I&amp;B) and has been appointed as the advisor to the Prime Minister of India in October 2021?</p>",
                    question_hi: "<p>36. निम्नलिखित में से कौन पूर्व सचिव (HRD), सूचना और प्रसारण (I&amp;B) थे जिन्&zwj;हें अक्टूबर 2021 में भारत के प्रधान मंत्री के सलाहकार के रूप में नियुक्त किया गया है?</p>",
                    options_en: ["<p>Shri Apurva Chandra</p>", "<p>PK Sinha</p>", 
                                "<p>Amarjeet Sinha</p>", "<p>Amit Khare</p>"],
                    options_hi: ["<p>श्री अपूर्व चंद्र</p>", "<p>पी.के. सिन्हा</p>",
                                "<p>अमरजीत सिन्हा</p>", "<p>अमित खरे</p>"],
                    solution_en: "<p>36.(d)<strong> Amit Khare</strong>. He is an Indian Administrative Service officer (1985 batch) of Jharkhand Cadre.</p>",
                    solution_hi: "<p>36.(d)<strong> अमित खरे</strong> । वह झारखंड कैडर के भारतीय प्रशासनिक सेवा के अधिकारी (1985 बैच) हैं।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "37",
                    section: "18",
                    question_en: "<p>37. Harrod Domar model was the main idea behind the __________ Five Year Plan.</p>",
                    question_hi: "<p>37. _______ पंचवर्षीय योजना के पीछे हैरोड डोमर मॉडल मुख्य विचार था।</p>",
                    options_en: ["<p>Third</p>", "<p>First</p>", 
                                "<p>Second</p>", "<p>Fourth</p>"],
                    options_hi: ["<p>तृतीय</p>", "<p>प्रथम</p>",
                                "<p>द्वितीय</p>", "<p>चतुर्थ</p>"],
                    solution_en: "<p>37.(b) <strong>First</strong> Five-Year Plan (1951-1956) aimed for a growth rate of 2.1% but achieved 3.6%. It focused on agriculture, investing in dams and irrigation, with significant funds allocated for the Bhakra Nangal Dam. The Second Five-Year Plan (1956-1961) focused on fast industrialization and emphasized the role of the public sector. It was developed under the leadership of P.C. Mahalanobis. Third Five Year Plan (1961-66) aimed to establish India as a self-reliant and self-generating economy.</p>",
                    solution_hi: "<p>37.(b) <strong>प्रथम</strong> पंचवर्षीय योजना (1951-1956) में 2.1% की विकास दर का लक्ष्य रखा गया था, लेकिन यह 3.6% हासिल किया गया। इसमें कृषि पर ध्यान केंद्रित किया गया, बांधों और सिंचाई में निवेश किया गया, जिसमें भाखड़ा नांगल बांध के लिए पर्याप्त धनराशि आवंटित की गई थी। दूसरी पंचवर्षीय योजना (1956-1961) में तेजी से औद्योगिकीकरण पर ध्यान केंद्रित किया गया और सार्वजनिक क्षेत्र की भूमिका पर जोर दिया गया। इस योजना को पी.सी. महालनोबिस के नेतृत्व में विकसित किया गया था। तीसरी पंचवर्षीय योजना (1961-66) का उद्देश्य भारत को एक आत्मनिर्भर और स्व-उत्पादक अर्थव्यवस्था के रूप में स्थापित करना था।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "38",
                    section: "18",
                    question_en: "<p>38. When was the Rashtriya Swasthya Bima Yojana introduced to provide health insurance to people below the poverty line?</p>",
                    question_hi: "<p>38. गरीबी रेखा से नीचे के लोगों को स्वास्थ्य बीमा प्रदान करने के लिए राष्ट्रीय स्वास्थ्य बीमा योजना कब शुरू की गई थी?</p>",
                    options_en: ["<p>2017</p>", "<p>2009</p>", 
                                "<p>2014</p>", "<p>2008</p>"],
                    options_hi: ["<p>2017 में</p>", "<p>2009 में</p>",
                                "<p>2014 में</p>", "<p>2008 में</p>"],
                    solution_en: "<p>38.(d) <strong>2008.</strong> Rashtriya Swasthya Bima Yojana (RSBY) has been launched by the Ministry of Labour and Employment. Objective: To provide protection to BPL households from financial liabilities arising out of health shocks that involve hospitalization. Other related schemes in India aimed at improving healthcare access: Ayushman Bharat Pradhan Mantri Jan Arogya Yojana (2018), and National Rural Health mission (2005).</p>",
                    solution_hi: "<p>38.(d) <strong>2008</strong> <strong>में।</strong> श्रम एवं रोजगार मंत्रालय द्वारा राष्ट्रीय स्वास्थ्य बीमा योजना (RSBY) शुरू की गई है। उद्देश्य: BPL परिवारों को अस्पताल में भर्ती होने से उत्पन्न होने वाली स्वास्थ्य संबंधी परेशानियों से होने वाली वित्तीय देनदारियों से सुरक्षा प्रदान करना। भारत में स्वास्थ्य सेवा तक पहुँच में सुधार लाने के उद्देश्य से अन्य संबंधित योजनाएँ: आयुष्मान भारत प्रधानमंत्री जन आरोग्य योजना (2018), और राष्ट्रीय ग्रामीण स्वास्थ्य मिशन (2005)।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "39",
                    section: "18",
                    question_en: "<p>39. Money supply is a _____ concept.</p>",
                    question_hi: "<p>39. मुद्रा आपूर्ति एक ________ अवधारणा है।</p>",
                    options_en: ["<p>stock</p>", "<p>measurable</p>", 
                                "<p>non-measurable</p>", "<p>flow</p>"],
                    options_hi: ["<p>स्&zwj;टॉक (stock)</p>", "<p>परिमेय (measurable)</p>",
                                "<p>अपरिमेय (non-measurable)</p>", "<p>प्रवाह (flow)</p>"],
                    solution_en: "<p>39.(a) <strong>stock.</strong> Money supply, like money demand, is a stock variable. The total stock of money in circulation among the public at a particular point of time is called money supply. RBI publishes figures for four alternative measures of money supply: M1, M2, M3 and M4.</p>",
                    solution_hi: "<p>39.(a) <strong>स्&zwj;टॉक (stock)।</strong> मुद्रा आपूर्ति, मुद्रा मांग की तरह, एक स्टॉक चर है। किसी विशेष समय पर जनता के बीच प्रचलन में मुद्रा के कुल स्टॉक को मुद्रा आपूर्ति कहा जाता है। RBI मुद्रा आपूर्ति के चार वैकल्पिक उपायों के लिए आंकड़े प्रकाशित करता है: M1, M2, M3 और M4 ।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "40",
                    section: "18",
                    question_en: "<p>40. The ________ of every state unfurls the flag in its capital on the Republic Day.</p>",
                    question_hi: "<p>40. प्रत्येक राज्य का ________ गणतंत्र दिवस पर अपनी राजधानी में झंडा फहराता है।</p>",
                    options_en: ["<p>Judge of High Court</p>", "<p>Chief Minister</p>", 
                                "<p>Governor</p>", "<p>Speaker</p>"],
                    options_hi: ["<p>उच्च न्यायालय के न्यायाधीश</p>", "<p>मुख्यमंत्री</p>",
                                "<p>राज्यपाल</p>", "<p>अध्यक्ष</p>"],
                    solution_en: "<p>40.(c) <strong>Governor</strong> is the constitutional head of a state in India, appointed by the President. They represent the central government at the state level. Article 153 - Governors of States.</p>",
                    solution_hi: "<p>40.(c) <strong>राज्यपाल</strong> भारत में किसी राज्य का संवैधानिक प्रमुख होता है, जिसे राष्ट्रपति द्वारा नियुक्त किया जाता है। वे राज्य स्तर पर केंद्र सरकार का प्रतिनिधित्व करते हैं। अनुच्छेद 153 - राज्यों के लिए राज्यपाल का प्रावधान।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "41",
                    section: "18",
                    question_en: "<p>41. Who among the following is one of the Deputy Chief Ministers of Uttar Pradesh as of July 2023?</p>",
                    question_hi: "<p>41. जुलाई 2023 तक की स्थिति के अनुसार, निम्नलिखित में से कौन उत्तर प्रदेश के उप मुख्यमंत्रियों में से एक है?</p>",
                    options_en: ["<p>Narottam Mishra</p>", "<p>Yogi Adityanath</p>", 
                                "<p>Manoj Sinha</p>", "<p>Keshav Prasad Maurya</p>"],
                    options_hi: ["<p>नरोत्तम मिश्रा</p>", "<p>योगी आदित्यनाथ</p>",
                                "<p>मनोज सिन्हा</p>", "<p>केशव प्रसाद मौर्य</p>"],
                    solution_en: "<p>41.(d) <strong>Keshav Prasad Maurya</strong> is an Indian politician and a member of the Bharatiya Janata Party (BJP). Yogi Adityanath: He is an Indian politician and a prominent member of the Bharatiya Janata Party (BJP). He serves as the Chief Minister of Uttar Pradesh (as of October 2024).</p>",
                    solution_hi: "<p>41.(d) <strong>केशव प्रसाद मौर्य</strong> एक भारतीय राजनेता और भारतीय जनता पार्टी (BJP) के सदस्य हैं। योगी आदित्यनाथ: वे एक भारतीय राजनेता और भारतीय जनता पार्टी (BJP) के एक प्रमुख सदस्य हैं। वे उत्तर प्रदेश के मुख्यमंत्री (अक्टूबर 2024 तक) के रूप में कार्यरत हैं।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "42",
                    section: "18",
                    question_en: "<p>42. Who among the following is/was a famous sitarist?</p>",
                    question_hi: "<p>42. निम्नलिखित में से कौन एक प्रसिद्ध सितार वादक हैं/थे?</p>",
                    options_en: ["<p>Ravi Shankar</p>", "<p>Ustad Bismillah Khan</p>", 
                                "<p>Pandit Bhimsen Joshi</p>", "<p>Zakir Hussain</p>"],
                    options_hi: ["<p>रवि शंकर</p>", "<p>उस्ताद बिस्मिल्ला खाँ</p>",
                                "<p>पंडित भीमसेन जोशी</p>", "<p>ज़ाकिर हुसैन</p>"],
                    solution_en: "<p>42.(a) <strong>Ravi Shankar.</strong> He was an Indian musician and founder of the National Orchestra of India, who was influential in stimulating Western appreciation of Indian music. His Awards: Bharat Ratna (1999), Padma Vibhushan (1981), Padma Bhushan (1967), and Grammy Award (1968) for Best Chamber Music Performance for his work \"West Meets East.\"</p>",
                    solution_hi: "<p>42.(a) <strong>रवि शंकर।</strong> वे एक भारतीय संगीतकार और नेशनल ऑर्केस्ट्रा ऑफ़ इंडिया के संस्थापक थे, जिन्होंने भारतीय संगीत के प्रति पश्चिमी देशों की प्रशंसा को प्रोत्साहित करने में महत्त्वपूर्ण भूमिका निभाई। उनके पुरस्कार: उन्हें अपने कार्य \"वेस्ट मीट्स ईस्ट\" के लिए भारत रत्न (1999), पद्म विभूषण (1981), पद्म भूषण (1967) और सर्वश्रेष्ठ चैम्बर संगीत प्रदर्शन के लिए ग्रैमी पुरस्कार (1968) से सम्मानित किया गया।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "43",
                    section: "18",
                    question_en: "<p>43. Three Poets Pampa, Ponna and Ranna are called as the &lsquo;Three Jewels&rsquo; of:</p>",
                    question_hi: "<p>43. तीन कवियों पम्पा, पोन्ना और रन्ना को ________ के तीन रत्न कहा जाता है।</p>",
                    options_en: ["<p>Kannada Literature</p>", "<p>Telugu Literature</p>", 
                                "<p>Tamil Literature</p>", "<p>Sanskrit Literature</p>"],
                    options_hi: ["<p>कन्नड़ साहित्य</p>", "<p>तेलुगु साहित्य</p>",
                                "<p>तमिल साहित्य</p>", "<p>संस्कृत साहित्य</p>"],
                    solution_en: "<p>43.(a) <strong>Kannada Literature.</strong> Pampa, also known as Adi Pampa, is renowned for his literary works, including \"Vikramarjuna Vijaya\" and \"Adi Purana.\" Ponna, often referred to as Ponna Kavishwara, served as a court poet for Rashtrakuta king Krishna III and is best known for his epic \"Shantipurana.\" Ranna was a court poet of the Western Chalukya Empire, and his most celebrated work is \"Gadayuddha.\"</p>",
                    solution_hi: "<p>43.(a) <strong>कन्नड़ साहित्य।</strong> पम्पा, जिन्हें आदि पम्पा के नाम से भी जाना जाता है, अपने साहित्यिक कृतियों के लिए प्रसिद्ध हैं, जिनमें \"विक्रमार्जुन विजया\" और \"आदि पुराण\" शामिल हैं। पोन्ना, जिन्हें अक्सर पोन्ना कविश्वर के नाम से जाना जाता है, राष्ट्रकूट राजा कृष्ण तृतीय के दरबारी कवि थे और उन्हें उनके महाकाव्य \"शांतिपुराण\" के लिए जाना जाता है। रन्ना पश्चिमी चालुक्य साम्राज्य के दरबारी कवि थे और उनकी सबसे प्रसिद्ध रचना \"गदायुद्ध\" है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "44",
                    section: "18",
                    question_en: "<p>44. Indian classical violinist Annavarapu Rama Swamy was awarded with the _____________ in 2021.</p>",
                    question_hi: "<p>44. भारतीय शास्त्रीय वायलिन वादक अन्नवरपु रामा स्वामी को 2021 में _____________ से सम्मानित किया गया।</p>",
                    options_en: ["<p>Padma Shri</p>", "<p>Sangeet Natak Akademi</p>", 
                                "<p>Padma Vibhushan</p>", "<p>Padma Bhushan</p>"],
                    options_hi: ["<p>पद्म श्री</p>", "<p>संगीत नाटक अकादमी</p>",
                                "<p>पद्म विभूषण</p>", "<p>पद्म भूषण</p>"],
                    solution_en: "<p>44.(a) <strong>Padma Shri</strong> (2021). Dr. Annavarapu Ramaswamy, a renowned Indian classical violinist from Andhra Pradesh, is noted for his contributions to music, including the invention of new Ragas and Talas such as Vandana Raga, Sri Durga Raga, Tinetradi Tala, and Vedadi Tala.</p>",
                    solution_hi: "<p>44.(a)<strong> पद्म श्री (2021)। </strong>डॉ. ए.एस. आंध्र प्रदेश के प्रसिद्ध भारतीय शास्त्रीय वायलिन वादक अन्नवरपु रामास्वामी को संगीत में उनके योगदान के लिए जाना जाता है, जिसमें वंदना राग, श्री दुर्गा राग, तिनेत्रादि ताल और वेदादि ताल जैसे नए रागों और तालों का आविष्कार शामिल है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "45",
                    section: "18",
                    question_en: "<p>45. Under which of the following Acts, the Board of Control was established in England to control and supervise the administration of British India?</p>",
                    question_hi: "<p>45. निम्नलिखित में से किस अधिनियम के तहत, ब्रिटिश भारत के प्रशासन को नियंत्रित और संचालित करने के लिए इंग्लैंड में नियंत्रण बोर्ड की स्थापना की गई थी?</p>",
                    options_en: ["<p>Charter Act of 1813</p>", "<p>Regulating Act of 1773</p>", 
                                "<p>Charter Act of 1793</p>", "<p>Pitt&rsquo;s India Act of 1784</p>"],
                    options_hi: ["<p>चार्टर अधिनियम 1813</p>", "<p>विनियमन अधिनियम 1773</p>",
                                "<p>चार्टर अधिनियम 1793</p>", "<p>पिट्स इंडिया अधिनियम 1784</p>"],
                    solution_en: "<p>45.(d) <strong>Pitt&rsquo;s India Act of 1784</strong>. It was introduced during the tenure of Lord Warren Hastings against the backdrop of growing concerns about the East India Company\'s mismanagement and corruption in India. Provisions: It gave the British government control over the East India Company.</p>",
                    solution_hi: "<p>45.(d) <strong>पिट्स इंडिया अधिनियम 1784.</strong> इसे लॉर्ड वॉरेन हेस्टिंग्स के कार्यकाल के दौरान भारत में ईस्ट इंडिया कंपनी के कुप्रबंधन और भ्रष्टाचार के बारे में बढ़ती चिंताओं की पृष्ठभूमि में पेश किया गया था। प्रावधान: इसने ब्रिटिश सरकार को ईस्ट इंडिया कंपनी पर नियंत्रण दिया।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "46",
                    section: "18",
                    question_en: "<p>46. Which of the following states was the overall champion in both men and women categories in the first Janjatiya Khel Mahotsav?</p>",
                    question_hi: "<p>46. प्रथम जनजातीय खेल महोत्सव में निम्नलिखित में से कौन-सा राज्य पुरुष और महिला दोनों श्रेणियों में समग्र चैंपियन था?</p>",
                    options_en: ["<p>Maharashtra</p>", "<p>Chhattisgarh</p>", 
                                "<p>Jharkhand</p>", "<p>Odisha</p>"],
                    options_hi: ["<p>महाराष्ट्र</p>", "<p>छत्तीसगढ़</p>",
                                "<p>झारखंड</p>", "<p>ओडिशा</p>"],
                    solution_en: "<p>46.(d) <strong>Odisha.</strong> The first Janjatiya Khel Mahotsav, a grand sporting event jointly hosted by the Ministry of Culture, the Odisha Government, and KIIT University, concluded in Bhubaneswar. The first edition attracted the participation of 5,000 tribal athletes and 1,000 officials from 26 states.</p>",
                    solution_hi: "<p>46.(d) <strong>ओडिशा।</strong> संस्कृति मंत्रालय, ओडिशा सरकार और KIIT विश्वविद्यालय द्वारा संयुक्त रूप से आयोजित एक भव्य खेल आयोजन, प्रथम जनजातीय खेल महोत्सव भुवनेश्वर में संपन्न हुआ। पहले संस्करण में 26 राज्यों के 5,000 आदिवासी एथलीटों और 1,000 अधिकारियों ने भाग लिया था।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "47",
                    section: "18",
                    question_en: "<p>47. According to the Varna system that was prevalent in the Vedic period, which of the following was NOT a Varna?</p>",
                    question_hi: "<p>47. वैदिक काल में प्रचलित वर्ण व्यवस्था के अनुसार, निम्नलिखित में से कौन-सा एक वर्ण नहीं था?</p>",
                    options_en: ["<p>Brahmanas</p>", "<p>Kshatriyas</p>", 
                                "<p>Samiti</p>", "<p>Vaishyas</p>"],
                    options_hi: ["<p>ब्राह्मण</p>", "<p>क्षत्रिय</p>",
                                "<p>समिति</p>", "<p>वैश्य</p>"],
                    solution_en: "<p>47.(c) <strong>Samiti.</strong> The traditional caste system consists of a hierarchy of four castes (varnas) - Brahmins (priests and teachers), Kshatriyas (rulers and warriors), Vaishyas (merchants and cultivators), and Shudras (servants).</p>",
                    solution_hi: "<p>47.(c) <strong>समिति।</strong> पारंपरिक जाति व्यवस्था में चार जातियों (वर्णों) का पदानुक्रम शामिल है - ब्राह्मण (पुजारी और शिक्षक), क्षत्रिय (शासक और योद्धा), वैश्य (व्यापारी और कृषक), और शूद्र (सेवक)।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "48",
                    section: "18",
                    question_en: "<p>48. Who was the first Indian woman wrestler to represent the country at the 2012 London Olympic Games?</p>",
                    question_hi: "<p>48. 2012 लंदन ओलंपिक खेलों में देश का प्रतिनिधित्व करने वाली पहली भारतीय महिला पहलवान कौन थी?</p>",
                    options_en: ["<p>Babita Phogat</p>", "<p>Sakshi Malik</p>", 
                                "<p>Vinesh Phogat</p>", "<p>Geeta Phogat</p>"],
                    options_hi: ["<p>बबीता फोगाट</p>", "<p>साक्षी मलिक</p>",
                                "<p>विनेश फोगाट</p>", "<p>गीता फोगाट</p>"],
                    solution_en: "<p>48.(d) <strong>Geeta Phogat.</strong> She is a freestyle wrestler who won India\'s first-ever gold medal in wrestling in the 55kg freestyle category at the 2010 Commonwealth Games for India. Her Award: Arjuna Award (2012).</p>",
                    solution_hi: "<p>48.(d) <strong>गीता फोगाट।</strong> वह एक फ्रीस्टाइल पहलवान हैं, जिन्होंने 2010 के राष्ट्रमंडल खेलों में 55 किलोग्राम फ्रीस्टाइल वर्ग में कुश्ती में भारत को प्रथम स्वर्ण पदक दिलाया था। उनके पुरस्कार: अर्जुन पुरस्कार (2012)।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "49",
                    section: "18",
                    question_en: "<p>49. Which marine carotenoid is abundant in brown seaweed, macroalgae and diatoms?</p>",
                    question_hi: "<p>49. भूरे समुद्री शैवाल, मैक्रोएल्गी और द्विपरमाणुक (डायटम) में कौन-सा समुद्री कैरोटिनॉइड प्रचुर मात्रा में पाया जाता है?</p>",
                    options_en: ["<p>Fucoxanthin</p>", "<p>Astaxanthin</p>", 
                                "<p>Neoxanthin</p>", "<p>&beta;-cryptoxanthin</p>"],
                    options_hi: ["<p>फ्यूकोजैन्थिन (Fucoxanthin)</p>", "<p>ऐस्टैजैन्थिन (Astaxanthin)</p>",
                                "<p>निओजैन्थिन (Neoxanthin)</p>", "<p>&beta; - क्रिप्टोजैन्थिन (&beta;-cryptoxanthin)</p>"],
                    solution_en: "<p>49.(a) <strong>Fucoxanthin.</strong> It is an orange-colored pigment, along with chlorophylls a and c and &beta;-carotene, present in Chromophyta, including brown seaweeds (Phaeophyceae) and diatoms (Bacillariophyta). Astaxanthin: It is a reddish pigment that belongs to a group of chemicals called carotenoids. Neoxanthin: It is a carotenoid and xanthophyll pigment found in plants, algae, and microorganisms. &beta;-Cryptoxanthin, a carotenoid found in fruits and vegetables such as tangerines, red peppers, and pumpkin.</p>",
                    solution_hi: "<p>49.(a) <strong>फ्यूकोजैन्थिन</strong> (Fucoxanthin)। यह एक नारंगी रंग का वर्णक है, जिसमें क्लोरोफिल a और c तथा &beta;-कैरोटीन भी शामिल है, जो भूरे समुद्री शैवाल (फियोफाइसी) और डायटम (बैसिलेरियोफाइटा) सहित क्रोमोफाइटा में मौजूद है। ऐस्टैजैन्थिन : यह एक लाल रंग का वर्णक है जो कैरोटीनॉयड नामक रसायनों के समूह से संबंधित है। निओजैन्थिन : यह एक कैरोटीनॉयड और ज़ैंथोफिल वर्णक है जो पौधों, शैवाल और सूक्ष्मजीवों में पाया जाता है। &beta; - क्रिप्टोजैन्थिन, एक कैरोटीनॉयड है जो कीनू, लाल मिर्च और कद्दू जैसे फलों और सब्जियों में पाया जाता है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "50",
                    section: "17",
                    question_en: "<p>50. The Parliament passed the Essential Defence Services Bill, 2021 in August 2021. Which of the following is an objective behind passing it?</p>",
                    question_hi: "<p>50. संसद ने अगस्त 2021 में आवश्यक रक्षा सेवा विधेयक, 2021 पारित किया। इसे पारित करने के पीछे निम्नलिखित में से कौन सा उद्देश्य है?</p>",
                    options_en: ["<p>To encourage more FDI in defence services</p>", "<p>To prohibit strikes, lockouts and layoffs in units engaged in essential defence services</p>", 
                                "<p>To appoint more women in defence services</p>", "<p>To implement one rank one pension</p>"],
                    options_hi: ["<p>रक्षा सेवाओं में अधिक FDI (प्रत्यक्ष विदेशी निवेश) को प्रोत्साहित करना</p>", "<p>आवश्यक रक्षा सेवाओं में लगी इकाइयों में हड़ताल, तालाबंदी और छंटनी पर रोक लगाना</p>",
                                "<p>रक्षा सेवाओं में अधिक महिलाओं की नियुक्ति करना</p>", "<p>वन रैंक वन पेंशन लागू करना</p>"],
                    solution_en: "<p>50.(b) The Essential Defence Services Bill, 2021, introduced by Defence Minister Rajnath Singh on July 22, 2021, defines essential defence services as those in establishments producing defence goods or in the armed forces and related areas. It also amends the Industrial Disputes Act, 1947, to classify these services as public utility services.</p>",
                    solution_hi: "<p>50.(b) रक्षा मंत्री राजनाथ सिंह द्वारा 22 जुलाई, 2021 को लागू किया गया आवश्यक रक्षा सेवा विधेयक, 2021, आवश्यक रक्षा सेवाओं को रक्षा सामान बनाने वाले प्रतिष्ठानों या सशस्त्र बलों और संबंधित क्षेत्रों में परिभाषित करता है। यह इन सेवाओं को सार्वजनिक उपयोगिता सेवाओं के रूप में वर्गीकृत करने के लिए औद्योगिक विवाद अधिनियम, 1947 में भी संशोधन करता है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "51",
                    section: "17",
                    question_en: "<p>51. A certain sum of money is given at a certain rate of simple interest for 6 years. Had it been given at 7% higher rate, it would have fetched ₹1,512 more. Find the sum (in ₹).</p>",
                    question_hi: "<p>51. एक निश्चित मूलधन 6 वर्षों के लिए साधारण ब्याज की एक निश्चित दर पर दिया जाता है। यदि इसे 7% अधिक दर पर दिया जाता, तो ₹1,512 अधिक प्राप्त होते। वह मूलधन (₹ में) ज्ञात कीजिए।</p>",
                    options_en: ["<p>3,200</p>", "<p>3,600</p>", 
                                "<p>3,100</p>", "<p>4,200</p>"],
                    options_hi: ["<p>3,200</p>", "<p>3,600</p>",
                                "<p>3,100</p>", "<p>4,200</p>"],
                    solution_en: "<p>51.(b) According to question <br><math display=\"inline\"><mo>&#8658;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>P</mi><mo>&#215;</mo><mo>(</mo><mi>R</mi><mo>+</mo><mn>7</mn><mo>&#160;</mo><mo>)</mo><mo>&#215;</mo><mn>6</mn></mrow><mn>100</mn></mfrac></math> - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>P</mi><mo>&#215;</mo><mi>R</mi><mo>&#215;</mo><mn>6</mn></mrow><mn>100</mn></mfrac></math> = 1512<br><math display=\"inline\"><mo>&#8658;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>6</mn><mi>P</mi></mrow><mn>100</mn></mfrac></math> ( R + 7 - R) = 1512<br><math display=\"inline\"><mo>&#8658;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>42</mn><mo>&#160;</mo><mi>P</mi></mrow><mn>100</mn></mfrac></math> = 1512<br><math display=\"inline\"><mo>&#8658;</mo></math> 42P = 151200 &rArr; P = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>151200</mn><mn>42</mn></mfrac></math> = 3600</p>",
                    solution_hi: "<p>51.(b) प्रश्नानुसार <br><math display=\"inline\"><mo>&#8658;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>P</mi><mo>&#215;</mo><mo>(</mo><mi>R</mi><mo>+</mo><mn>7</mn><mo>&#160;</mo><mo>)</mo><mo>&#215;</mo><mn>6</mn></mrow><mn>100</mn></mfrac></math> - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>P</mi><mo>&#215;</mo><mi>R</mi><mo>&#215;</mo><mn>6</mn></mrow><mn>100</mn></mfrac></math> = 1512<br><math display=\"inline\"><mo>&#8658;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>6</mn><mi>P</mi></mrow><mn>100</mn></mfrac></math> ( R + 7 - R) = 1512<br><math display=\"inline\"><mo>&#8658;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>42</mn><mo>&#160;</mo><mi>P</mi></mrow><mn>100</mn></mfrac></math> = 1512<br><math display=\"inline\"><mo>&#8658;</mo></math> 42P = 151200 &rArr; P = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>151200</mn><mn>42</mn></mfrac></math> = 3600</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "52",
                    section: "17",
                    question_en: "<p>52. A chord of length 32 cm is drawn at a distance of 12 cm from the centre of a circle. Find the radius of the circle.</p>",
                    question_hi: "<p>52. एक वृत्त के केंद्र से 12 cm की दूरी पर 32 cm लंबाई वाली एक जीवा खींची जाती है। वृत्त की त्रिज्या ज्ञात कीजिए।</p>",
                    options_en: ["<p>18 cm</p>", "<p>22 cm</p>", 
                                "<p>20 cm</p>", "<p>28 cm</p>"],
                    options_hi: ["<p>18 cm</p>", "<p>22 cm</p>",
                                "<p>20 cm</p>", "<p>28 cm</p>"],
                    solution_en: "<p>52.(c)<br><strong id=\"docs-internal-guid-c4018b33-7fff-fa89-3780-9e7ebff5d761\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfEabP6y7Xg5SdWEK-35do1MzbNB0CWafUN6_blA3tTkH6ZXTVjPPXgseM6dl0BWqu5FwZlpQL3o3oiTtRyVYhiZVX1TB1O1JPTjTkViMUOCyA5lzKSfT30qf8psFCxfIV8z3MfyJSS7I2TiqH6QGWeqim9?key=DeDRJyT6seelEyAeFidXXWTi\" width=\"127\" height=\"120\"></strong><br>&there4; Radius (OB) = <math display=\"inline\"><msqrt><msup><mrow><mn>16</mn></mrow><mrow><mn>2</mn></mrow></msup><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><msup><mrow><mn>12</mn></mrow><mrow><mn>2</mn></mrow></msup></msqrt></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>400</mn></msqrt></math> = 20cm</p>",
                    solution_hi: "<p>52.(c)<br><strong id=\"docs-internal-guid-c4018b33-7fff-fa89-3780-9e7ebff5d761\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfEabP6y7Xg5SdWEK-35do1MzbNB0CWafUN6_blA3tTkH6ZXTVjPPXgseM6dl0BWqu5FwZlpQL3o3oiTtRyVYhiZVX1TB1O1JPTjTkViMUOCyA5lzKSfT30qf8psFCxfIV8z3MfyJSS7I2TiqH6QGWeqim9?key=DeDRJyT6seelEyAeFidXXWTi\" width=\"127\" height=\"120\"></strong><br>&there4; त्रिज्या (OB) = <math display=\"inline\"><msqrt><msup><mrow><mn>16</mn></mrow><mrow><mn>2</mn></mrow></msup><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><msup><mrow><mn>12</mn></mrow><mrow><mn>2</mn></mrow></msup></msqrt></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>400</mn></msqrt></math> = 20 सेमी</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "53",
                    section: "17",
                    question_en: "<p>53. A shopkeeper offers the following discount schemes for buyers.<br>I. Two successive discounts of 15% and 20%<br>II. Successive discount of 25 % and 10%<br>III. A discount of 33%<br>The selling price will be minimum under the scheme:</p>",
                    question_hi: "<p>53. एक दुकानदार खरीदारों के लिए निम्नलिखित छूट योजनाएं प्रदान करता है।<br>I. 15% और 20% की दो क्रमिक छूट<br>II. 25% और 10% की क्रमिक छूट<br>III. 33% की छूट<br>किस योजना के तहत विक्रय मूल्य न्यूनतम होगा?</p>",
                    options_en: ["<p>I</p>", "<p>I or II</p>", 
                                "<p>III</p>", "<p>II</p>"],
                    options_hi: ["<p>I</p>", "<p>I or II</p>",
                                "<p>III</p>", "<p>II</p>"],
                    solution_en: "<p>53.(c) Scheme I : Successive discounts of 15% and 20% = (15 + 20 - <math display=\"inline\"><mfrac><mrow><mn>15</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>20</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math>)% = 32%<br>Scheme II : Successive discount of 25 % and 10% = (25 + 10 - <math display=\"inline\"><mfrac><mrow><mn>25</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>10</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math>)% = 32.5%<br>Scheme III : Discount of 33% <br>As , we can say if there is maximum discount percent , there will be minimum selling price <br>&there4; Scheme III has minimum selling price</p>",
                    solution_hi: "<p>53.(c) योजना I :- 15% और 20% की क्रमिक छूट = (15 + 20 - <math display=\"inline\"><mfrac><mrow><mn>15</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>20</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math>)% = 32%<br>योजना II :- 25% और 10% की क्रमिक छूट = (25 + 10 - <math display=\"inline\"><mfrac><mrow><mn>25</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>10</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math>)% = 32.5%<br>योजना II :- 33% की छूट<br>जैसा कि, हम कह सकते हैं कि यदि अधिकतम छूट प्रतिशत है, तो न्यूनतम विक्रय मूल्य होगा <br>&there4; योजना III में न्यूनतम विक्रय मूल्य है</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "54",
                    section: "17",
                    question_en: "<p>54. The perimeter of a rectangular garden is 48 m. If the length is 6 m more than the breadth, the area (in m<sup>2</sup>) of the garden is:</p>",
                    question_hi: "<p>54. एक आयताकार बगीचे का परिमाप 48 m है। यदि लंबाई, चौड़ाई से 6 m अधिक है, तो बगीचे का क्षेत्रफल (m<sup>2</sup> में) क्या है?</p>",
                    options_en: ["<p>135</p>", "<p>96</p>", 
                                "<p>84</p>", "<p>112</p>"],
                    options_hi: ["<p>135</p>", "<p>96</p>",
                                "<p>84</p>", "<p>112</p>"],
                    solution_en: "<p>54.(a) Let breadth of the rectangular garden = x m <br>Length of the rectangular garden = (x + 6) m<br>Perimeter of rectangular garden [ 2 ( l + b) ] = 48 m <br><math display=\"inline\"><mo>&#8658;</mo></math> 2 (x + 6 + x ) = 48 <br><math display=\"inline\"><mo>&#8658;</mo></math> 2x + 6 = 24 <br><math display=\"inline\"><mo>&#8658;</mo></math> 2x = 18 &rArr; x = 9m <br>Length = 15 m and breadth = 9 m <br>Area of rectangular garden = l &times; b = 15 &times; 9 = 135 m<sup>2</sup></p>",
                    solution_hi: "<p>54.(a) माना , आयताकार बगीचे की चौड़ाई = x मीटर <br>आयताकार बगीचे की लंबाई = (x + 6) मीटर <br>आयताकार बगीचे का परिमाप [ 2 ( l + b) ] = 48 मीटर <br><math display=\"inline\"><mo>&#8658;</mo></math> 2 (x + 6 + x ) = 48 <br><math display=\"inline\"><mo>&#8658;</mo></math> 2x + 6 = 24 <br><math display=\"inline\"><mo>&#8658;</mo></math> 2x = 18 &rArr; x = 9 मीटर <br>लंबाई = 15 m और चौड़ाई = 9 मीटर <br>आयताकार बगीचे का क्षेत्रफल = l &times; b = 15 &times; 9 = 135 मीटर<sup>2</sup></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "55",
                    section: "17",
                    question_en: "<p>55. If three cubes of volume 512 cm<sup>3</sup> each are joined end to end, find the surface area of the resulting cuboid.</p>",
                    question_hi: "<p>55. यदि आयतन 512 cm<sup>3</sup> के तीन घनों को एक सिरे से दूसरे सिरे तक जोड़ा जाए, तो परिणामी घनाभ का पृष्ठीय क्षेत्रफल ज्ञात कीजिए।</p>",
                    options_en: ["<p>896 cm<sup>2</sup></p>", "<p>986 cm<sup>2</sup></p>", 
                                "<p>895 cm<sup>2</sup></p>", "<p>869 cm<sup>2</sup></p>"],
                    options_hi: ["<p>896 cm<sup>2</sup></p>", "<p>986 cm<sup>2</sup></p>",
                                "<p>895 cm<sup>2</sup></p>", "<p>869 cm<sup>2</sup></p>"],
                    solution_en: "<p>55.(a) Volume of each cube = 512 <math display=\"inline\"><msup><mrow><mi>c</mi><mi>m</mi></mrow><mrow><mn>3</mn></mrow></msup></math><br>&there4; Side of cube = <math display=\"inline\"><mroot><mrow><mn>512</mn></mrow><mrow><mn>3</mn></mrow></mroot></math> = 8<br>Now by joining three cube , length of the cuboid = 8 + 8 + 8 = 24 cm&nbsp;<br>Breadth = 8 cm and height = 8cm <br>&there4; Surface area = <math display=\"inline\"><mn>2</mn><mi>&#160;</mi><mo>(</mo><mi>l</mi><mi>b</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>b</mi><mi>h</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>h</mi><mi>l</mi><mi>&#160;</mi><mo>)</mo></math><br>= 2 (24 &times; 8 + 8 &times; 8 + 8 &times; 24)&nbsp;<br>= 2 (192 + 64 + 192 )<br>= 896 cm<sup>2</sup></p>",
                    solution_hi: "<p>55.(a) प्रत्येक घन का आयतन = 512 सेमी<sup>3</sup><br>&there4; घन की भुजा = <math display=\"inline\"><mroot><mrow><mn>512</mn></mrow><mrow><mn>3</mn></mrow></mroot></math> = 8<br>अब तीन घनों को जोड़कर, घनाभ की लंबाई = 8 + 8 + 8 = 24 सेमी&nbsp;<br>चौड़ाई = 8 सेमी और ऊंचाई = 8 सेमी <br>&there4; पृष्ठीय क्षेत्रफल = <math display=\"inline\"><mn>2</mn><mi>&#160;</mi><mo>(</mo><mi>l</mi><mi>b</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>b</mi><mi>h</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>h</mi><mi>l</mi><mi>&#160;</mi><mo>)</mo></math><br>= 2 (24 &times; 8 + 8 &times; 8 +8 &times; 24) <br>= 2 (192 + 64 + 192 )<br>= 896 cm<sup>2</sup></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "56",
                    section: "17",
                    question_en: "<p>56. A number r when divided by 8 leaves remainder 3. What will be the remainder when (r<sup>2</sup> + 6r + 7) is divided by 8?</p>",
                    question_hi: "<p>56. एक संख्या r को 8 से विभाजित करने पर 3 शेष बचता है। (r<sup>2</sup> + 6r + 7) को 8 से विभाजित करने पर शेषफल क्या होगा?</p>",
                    options_en: ["<p>2</p>", "<p>3</p>", 
                                "<p>1</p>", "<p>4</p>"],
                    options_hi: ["<p>2</p>", "<p>3</p>",
                                "<p>1</p>", "<p>4</p>"],
                    solution_en: "<p>56.(a) Number (r ) &divide;&nbsp;8 &rarr; 3 (rem)<br>Number (<math display=\"inline\"><msup><mrow><mi>r</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>6</mn><mi>r</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>7</mn><mi>&#160;</mi><mo>)</mo></math> &divide; 8 &rarr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mn>3</mn><mn>2</mn></msup><mo>+</mo><mn>6</mn><mo>(</mo><mn>3</mn><mo>)</mo><mo>+</mo><mn>7</mn></mrow><mn>8</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>34</mn><mn>8</mn></mfrac></math> &rarr; 2 (rem )</p>",
                    solution_hi: "<p>56.(a) संख्या (r ) &divide;&nbsp;8 &rarr; 3 ( शेषफल )<br>संख्या (<math display=\"inline\"><msup><mrow><mi>r</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>6</mn><mi>r</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>7</mn><mi>&#160;</mi><mo>)</mo></math> &divide; 8 &rarr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mn>3</mn><mn>2</mn></msup><mo>+</mo><mn>6</mn><mo>(</mo><mn>3</mn><mo>)</mo><mo>+</mo><mn>7</mn></mrow><mn>8</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>34</mn><mn>8</mn></mfrac></math> &rarr; 2 (शेषफल )</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "57",
                    section: "17",
                    question_en: "<p>57. If sec(t) = <math display=\"inline\"><mfrac><mrow><mi>x</mi></mrow><mrow><mi>y</mi></mrow></mfrac></math> , then cot(t) is equal to:</p>",
                    question_hi: "<p>57. यदि sec(t) = <math display=\"inline\"><mfrac><mrow><mi>x</mi></mrow><mrow><mi>y</mi></mrow></mfrac></math> है, तो cot(t) किसके बराबर है?</p>",
                    options_en: ["<p><math display=\"inline\"><mfrac><mrow><mi>y</mi></mrow><mrow><msqrt><mi>&#160;</mi><msup><mrow><mi>x</mi></mrow><mrow><mn>2</mn></mrow></msup><mo>-</mo><msup><mrow><mi>y</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>&#160;</mi></msqrt></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mi>x</mi></mrow><mrow><msqrt><mi>&#160;</mi><msup><mrow><mi>x</mi></mrow><mrow><mn>2</mn></mrow></msup><mo>+</mo><msup><mrow><mi>y</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>&#160;</mi></msqrt></mrow></mfrac></math></p>", 
                                "<p><math display=\"inline\"><mfrac><mrow><mi>x</mi></mrow><mrow><msqrt><mi>&#160;</mi><msup><mrow><mi>x</mi></mrow><mrow><mn>2</mn></mrow></msup><mo>-</mo><msup><mrow><mi>y</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>&#160;</mi></msqrt></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mi>y</mi></mrow><mrow><msqrt><mi>&#160;</mi><msup><mrow><mi>x</mi></mrow><mrow><mn>2</mn></mrow></msup><mo>+</mo><msup><mrow><mi>y</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>&#160;</mi></msqrt></mrow></mfrac></math></p>"],
                    options_hi: ["<p><math display=\"inline\"><mfrac><mrow><mi>y</mi></mrow><mrow><msqrt><mi>&#160;</mi><msup><mrow><mi>x</mi></mrow><mrow><mn>2</mn></mrow></msup><mo>-</mo><msup><mrow><mi>y</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>&#160;</mi></msqrt></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mi>x</mi></mrow><mrow><msqrt><mi>&#160;</mi><msup><mrow><mi>x</mi></mrow><mrow><mn>2</mn></mrow></msup><mo>+</mo><msup><mrow><mi>y</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>&#160;</mi></msqrt></mrow></mfrac></math></p>",
                                "<p><math display=\"inline\"><mfrac><mrow><mi>x</mi></mrow><mrow><msqrt><mi>&#160;</mi><msup><mrow><mi>x</mi></mrow><mrow><mn>2</mn></mrow></msup><mo>-</mo><msup><mrow><mi>y</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>&#160;</mi></msqrt></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mi>y</mi></mrow><mrow><msqrt><mi>&#160;</mi><msup><mrow><mi>x</mi></mrow><mrow><mn>2</mn></mrow></msup><mo>+</mo><msup><mrow><mi>y</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>&#160;</mi></msqrt></mrow></mfrac></math></p>"],
                    solution_en: "<p>57.(a) sec(t) = <math display=\"inline\"><mfrac><mrow><mi>x</mi></mrow><mrow><mi>y</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>Hypotenuse</mi><mi>Base</mi></mfrac></math><br>Perpendicular = <math display=\"inline\"><msqrt><msup><mrow><mi>x</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><msup><mrow><mi>y</mi></mrow><mrow><mn>2</mn></mrow></msup></msqrt></math><br>cot(t) = <math display=\"inline\"><mfrac><mrow><mi>B</mi><mi>a</mi><mi>s</mi><mi>e</mi></mrow><mrow><mi>P</mi><mi>e</mi><mi>r</mi><mi>p</mi><mi>e</mi><mi>n</mi><mi>d</mi><mi>i</mi><mi>c</mi><mi>u</mi><mi>l</mi><mi>a</mi><mi>r</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"normal\">y</mi><msqrt><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup><mo>-</mo><msup><mi mathvariant=\"normal\">y</mi><mn>2</mn></msup></msqrt></mfrac></math></p>",
                    solution_hi: "<p>57.(a) sec(t) = <math display=\"inline\"><mfrac><mrow><mi>x</mi></mrow><mrow><mi>y</mi></mrow></mfrac></math> = <math style=\"font-family:Verdana\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>&#2325;&#2352;&#2381;&#2339;</mi><mi>&#2310;&#2343;&#2366;&#2352;</mi></mfrac></math> <br>लंब = <math display=\"inline\"><msqrt><msup><mrow><mi>x</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><msup><mrow><mi>y</mi></mrow><mrow><mn>2</mn></mrow></msup></msqrt></math><br>cot(t) = <math style=\"font-family:Verdana\" display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>&#2310;&#2343;&#2366;&#2352;</mi><mi>&#2354;&#2306;&#2348;</mi></mfrac></math> =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"normal\">y</mi><msqrt><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup><mo>-</mo><msup><mi mathvariant=\"normal\">y</mi><mn>2</mn></msup></msqrt></mfrac></math></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "58",
                    section: "17",
                    question_en: "<p>58. Simplify the following:<br>[(5<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> - 3<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>3</mn></mfrac></math>) of 1<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>13</mn></mfrac></math> + 1 - 2 &times; 3<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>2</mn></mfrac></math> &divide; 2<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>4</mn></mfrac></math>]</p>",
                    question_hi: "<p>58. निम्नलिखित का सरलीकरण करें:<br>[(5<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> - 3<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>3</mn></mfrac></math>) का<strong id=\"docs-internal-guid-3dcb8d37-7fff-26fc-4f59-b3c1ce082f42\"> </strong>1<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>13</mn></mfrac></math> + 1 - 2 &times; 3<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>2</mn></mfrac></math> &divide; 2<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>4</mn></mfrac></math>]</p>",
                    options_en: ["<p>0</p>", "<p><math display=\"inline\"><mfrac><mrow><mn>11</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math></p>", 
                                "<p>-2</p>", "<p>3<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math></p>"],
                    options_hi: ["<p>0</p>", "<p><math display=\"inline\"><mfrac><mrow><mn>11</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math></p>",
                                "<p>-2</p>", "<p>3<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math></p>"],
                    solution_en: "<p>58.(a)[(5<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> - 3<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>3</mn></mfrac></math>) of 1<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>13</mn></mfrac></math> + 1 - 2 &times; 3<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>2</mn></mfrac></math> &divide; 2<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>4</mn></mfrac></math>]<br>= [ (<math display=\"inline\"><mfrac><mrow><mn>13</mn></mrow><mrow><mn>6</mn></mrow></mfrac></math>) of <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>18</mn><mn>13</mn></mfrac></math> + 1 - 2 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>11</mn><mn>2</mn></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>11</mn></mfrac></math>]<br>= [ 3 + 1 - 4 ] <br>= 0</p>",
                    solution_hi: "<p>58.(a) [(5<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> - 3<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>3</mn></mfrac></math>) का<strong id=\"docs-internal-guid-3dcb8d37-7fff-26fc-4f59-b3c1ce082f42\"> </strong>1<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>13</mn></mfrac></math> + 1 - 2 &times; 3<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>2</mn></mfrac></math> &divide; 2<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>4</mn></mfrac></math>]<br>= [ (<math display=\"inline\"><mfrac><mrow><mn>13</mn></mrow><mrow><mn>6</mn></mrow></mfrac></math>) का<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>18</mn><mn>13</mn></mfrac></math> + 1 - 2 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>11</mn><mn>2</mn></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>11</mn></mfrac></math>]<br>= [ 3 + 1 - 4 ] <br>= 0</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "59",
                    section: "17",
                    question_en: "<p>59. Find the mean proportional of 36 and 100.</p>",
                    question_hi: "<p>59. 36 और 100 का माध्यानुपाती ज्ञात कीजिए।</p>",
                    options_en: ["<p>65</p>", "<p>60</p>", 
                                "<p>55</p>", "<p>70</p>"],
                    options_hi: ["<p>65</p>", "<p>60</p>",
                                "<p>55</p>", "<p>70</p>"],
                    solution_en: "<p>59.(b)mean proportional of 36 and 100 = <math display=\"inline\"><msqrt><mn>36</mn><mi>&#160;</mi><mo>&#215;</mo><mn>100</mn></msqrt></math><br>= 6 <math display=\"inline\"><mo>&#215;</mo></math> 10 = 60</p>",
                    solution_hi: "<p>59.(b) 36 और 100 का औसत आनुपातिक = <math display=\"inline\"><msqrt><mn>36</mn><mi>&#160;</mi><mo>&#215;</mo><mn>100</mn></msqrt></math><br>= 6 <math display=\"inline\"><mo>&#215;</mo></math> 10 = 60</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "60",
                    section: "17",
                    question_en: "<p>60. In a constituency, 45% of the total number of voters are males and the rest are females. If 60% of the males are illiterate and 60% of the females are literate, then by what percent is the number of illiterate females more than that of literate males?&nbsp;(Rounded off to 2 decimal places)</p>",
                    question_hi: "<p>60. एक निर्वाचन क्षेत्र में कुल मतदाताओं में से 45% पुरुष हैं और शेष महिलाएँ हैं। यदि 60% पुरुष निरक्षर हैं और 60% महिलाएँ साक्षर हैं, तो निरक्षर महिलाओं की संख्या साक्षर पुरुषों की तुलना में कितने प्रतिशत अधिक है? (दशमलव के दो स्थानों तक पूर्णांकित)</p>",
                    options_en: ["<p>19.11%</p>", "<p>27.23%</p>", 
                                "<p>30.24%</p>", "<p>22.22%</p>"],
                    options_hi: ["<p>19.11%</p>", "<p>27.23%</p>",
                                "<p>30.24%</p>", "<p>22.22%</p>"],
                    solution_en: "<p>60.(d) Let Total voters = 100 <br><strong id=\"docs-internal-guid-64537a6e-7fff-ad3d-136a-fe2787d6cb42\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXeixvJqlcwBWTXS8j7rUB8a1OsGdDWluE7wgpnrCvj4122Vm9H18lUOQKDs7QEAULNu63q_12ashASQmlJCy8gbxoYKTxzzoTzXB6HIB7-4yR-RaFb-aU5m82N8plFTTREMdbPQBl2aH1L66mJUYLHmYH1i?key=DeDRJyT6seelEyAeFidXXWTi\" width=\"208\" height=\"187\"></strong><br>Now , Percentage in which number of illiterate female more than that of literate male = <math display=\"inline\"><mfrac><mrow><mn>22</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>18</mn></mrow><mrow><mn>18</mn></mrow></mfrac></math> &times; 100<br>= 22.22%</p>",
                    solution_hi: "<p>60.(d) माना , कुल मतदाता = 100 <br><strong id=\"docs-internal-guid-2bac7340-7fff-da81-144f-361a784de315\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdZFrXh4edKCyxYPEHlETbUjbZ5k7Fed2CiylLZGnTLlkr9VQhnpqzuKhIKi6tmNL2xs0OkmlO169ZPUi6rD3O0keqiMn199vZtJzDzBDJCvyLWRNaJWWSfChLD3Al-5xeFI_RaRjC8yeMVh4V4PsN12bM?key=DeDRJyT6seelEyAeFidXXWTi\" width=\"198\" height=\"196\"></strong><br>अब, निरक्षर महिलाओं की संख्या साक्षर पुरुषों से अधिक होने का प्रतिशत = <math display=\"inline\"><mfrac><mrow><mn>22</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>18</mn></mrow><mrow><mn>18</mn></mrow></mfrac></math> &times; 100 = 22.22%</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "61",
                    section: "17",
                    question_en: "<p>61. The sum of five consecutive numbers is 80. Find the largest number.</p>",
                    question_hi: "<p>61. पाँच क्रमागत संख्याओं का योग 80 है। सबसे बड़ी संख्या ज्ञात कीजिए।</p>",
                    options_en: ["<p>18</p>", "<p>15</p>", 
                                "<p>19</p>", "<p>14</p>"],
                    options_hi: ["<p>18</p>", "<p>15</p>",
                                "<p>19</p>", "<p>14</p>"],
                    solution_en: "<p>61.(a) Let the five consecutive numbers are x - 2 , x - 1 , x , x + 1 , x + 2<br>According to question<br>x - 2 + x -1 + x + x + 1 + x + 2 = 80<br><math display=\"inline\"><mo>&#8658;</mo></math> 5x = 80&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&rArr; x = 16<br>Largest number = x + 2 = 16 + 2 = 18</p>",
                    solution_hi: "<p>61.(a) मान लीजिए कि पाँच क्रमागत संख्याएँ x - 2 , x -1 , x , x + 1 , x + 2 हैं<br>प्रश्न के अनुसार , <br>x - 2 + x -1 + x + x + 1 + x + 2 = 80<br><math display=\"inline\"><mo>&#8658;</mo></math> 5x = 80&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &rArr; x = 16<br>सबसे बड़ी संख्या = x + 2 = 16 + 2 = 18</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "62",
                    section: "17",
                    question_en: "<p>62. A shopkeeper sold <math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math> of his articles at a gain of 20% and the remaining at the cost price. What is his gain percentage in the whole transaction?</p>",
                    question_hi: "<p>62. एक दुकानदार ने अपनी <math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math> वस्तुओं को 20% के लाभ पर और शेष को क्रय मूल्य पर बेचा। पूरे सौदे में उसका लाभ प्रतिशत कितना है?</p>",
                    options_en: ["<p>16<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> %</p>", "<p>14<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> %</p>", 
                                "<p>13<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> %</p>", "<p>12<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math>&nbsp;%</p>"],
                    options_hi: ["<p>16<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> %</p>", "<p>14<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> %</p>",
                                "<p>13<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> %</p>", "<p>12<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math>&nbsp;%</p>"],
                    solution_en: "<p>62.(d) Let the total no. of article be 8 <br>According to question,<br><math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math> article sold At the profit of 20% = (8 &times; <math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math>) &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>6</mn><mn>5</mn></mfrac></math> = 6 units<br>SP of 5 articles = 6 units<br>Remaining article sold at the cost price = 3 units<br>SP of 3 articles = 3 units<br>Total SP of the article = 9 units<br>Required profit = <math display=\"inline\"><mfrac><mrow><mn>9</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>8</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math> &times; 100 = 12<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math>%</p>",
                    solution_hi: "<p>62.(d) माना वस्तुओं की कुल संख्या 8 हैं<br>प्रश्न के अनुसार,<br><math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math> वस्तुएँ 20% के लाभ पर बेची गईं = (8 &times; <math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math>) &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>6</mn><mn>5</mn></mfrac></math> = 6 इकाई<br>5 वस्तुओं का विक्रय मूल्य = 6 इकाई<br>शेष वस्तु लागत मूल्य पर बेची गई = 3 इकाई<br>3 वस्तुओं का विक्रय मूल्य = 3 इकाई<br>कुल वस्तुओं का विक्रय मूल्य = 9 इकाई<br>आवश्यक लाभ = <math display=\"inline\"><mfrac><mrow><mn>9</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>8</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math> &times; 100 = 12<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math>%</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "63",
                    section: "17",
                    question_en: "<p>63. Pipe X can fill a tank in 60 hours while pipe Y can fill the tank in 72 hours. Both pipes are opened together for 20 hours. How much of the tank is left empty ?</p>",
                    question_hi: "<p>63. पाइप X एक टंकी को 60 घंटे में भर सकती है जबकि पाइप Y उस टंकी को 72 घंटे में भर सकता है। दोनों पाइपों को एक साथ 20 घंटे के लिए खोला जाता है। टंकी का कितना भाग खाली बचेगा?</p>",
                    options_en: ["<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>243</mn></mrow><mrow><mn>360</mn></mrow></mfrac></math></p>", 
                                "<p><math display=\"inline\"><mfrac><mrow><mn>7</mn></mrow><mrow><mn>18</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math></p>"],
                    options_hi: ["<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>243</mn></mrow><mrow><mn>360</mn></mrow></mfrac></math></p>",
                                "<p><math display=\"inline\"><mfrac><mrow><mn>7</mn></mrow><mrow><mn>18</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math></p>"],
                    solution_en: "<p>63.(c) <br><strong id=\"docs-internal-guid-e4b83283-7fff-ea51-a907-b039649231d1\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfF-liDqBzO8RccPg2bxE4epgCauUTvOsO8qH4B1f9QjBuRo43Ruf-VkUxZnagcSykMy9V92f4dDumEPLgOOHzfVLatEs20ol7utkfO80-8G6GXMLQoKKvzOilbNeidULUoaoyylUonb_5Is5e3tRkR1Kcy?key=DeDRJyT6seelEyAeFidXXWTi\" width=\"185\" height=\"138\"></strong><br>Tank fill in 20hr by both the pipe = (6 + 5 ) &times; 20 = 220<br>Part of tank is left empty = <math display=\"inline\"><mfrac><mrow><mn>360</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>220</mn></mrow><mrow><mn>360</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>140</mn><mn>360</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>18</mn></mfrac></math></p>",
                    solution_hi: "<p>63.(c) <br><strong id=\"docs-internal-guid-2644719d-7fff-a057-f959-4b71cd4d8ae0\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfDYpWsi4MEM313_sPa-RCIWHr25v78fCztYoG0Tvy7gFyW0XJ0dyk-Zjushbmc8V-Quq7589f0JrhsggBrzpfs7cYOh2RZpbpbLb60ro9dZAPjWvKDRzttSYb8goABG8SHdxwLHWkwZAtFJOoU3SdTBfWj?key=DeDRJyT6seelEyAeFidXXWTi\" width=\"190\" height=\"160\"></strong><br>दोनों पाइपों द्वारा 20 घंटे में टंकी भरना = (6 + 5 ) &times; 20 = 220<br>जितनी टंकी का भाग खाली छोड़ा गया = <math display=\"inline\"><mfrac><mrow><mn>360</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>220</mn></mrow><mrow><mn>360</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>140</mn><mn>360</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>18</mn></mfrac></math></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "64",
                    section: "17",
                    question_en: "<p>64. If x = 7 - 4<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math>, then find the value of (x<sup>2</sup> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup></mfrac></math>).</p>",
                    question_hi: "<p>64. यदि x = 7 - 4<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math> है, तो (x<sup>2</sup> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup></mfrac></math>) का मान ज्ञात कीजिए।</p>",
                    options_en: ["<p>188</p>", "<p>194</p>", 
                                "<p>186</p>", "<p>196</p>"],
                    options_hi: ["<p>188</p>", "<p>194</p>",
                                "<p>186</p>", "<p>196</p>"],
                    solution_en: "<p>64.(b) <strong>Given x</strong> = 7 - 4<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math><br><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mi>x</mi></mrow></mfrac></math> = 7 + 4<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math><br><math display=\"inline\"><mo>&#8658;</mo></math> x + <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mi>x</mi></mrow></mfrac></math> = 14<br>x<sup>2</sup> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup></mfrac></math> = 14<sup>2</sup> - 2 = 196 - 2 = 194</p>",
                    solution_hi: "<p>64.(b) <strong>Given</strong> <strong>x</strong> = 7 - 4<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math><br><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mi>x</mi></mrow></mfrac></math> = 7 + 4<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math><br><math display=\"inline\"><mo>&#8658;</mo></math> x + <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mi>x</mi></mrow></mfrac></math> = 14<br>x<sup>2</sup> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup></mfrac></math> = 14<sup>2</sup> - 2 = 196 - 2 = 194</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "65",
                    section: "17",
                    question_en: "<p>65. The following table shows the expenditures (in thousands of rupees) of a company in different categories from 2015 to 2019.<br><strong id=\"docs-internal-guid-f1f3464d-7fff-be0f-6498-fdfc61658952\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXel8aYXJnQX7sXQo5urKAw-DSPw_CmLuHsLyJCsDF_3jGSSNZjJV4ceDmQIrLP4B_n1bpbu74wpWwaPdeYAr30vxpfkgH1pMJMvR9zggEwIbtYlbYPYB0WnfZxmGw_4tiXim3grfkYosMg6GSulseTo7DEq?key=DeDRJyT6seelEyAeFidXXWTi\" width=\"531\" height=\"207\"></strong><br>What is the average amount (in ₹) paid by the company on fuel and transport per year from 2015 to 2019?</p>",
                    question_hi: "<p>65. निम्नलिखित तालिका 2015 से 2019 तक विभिन्न श्रेणियों में किसी कंपनी के व्यय (हजार रुपयों में) को दर्शाती है।<br><strong id=\"docs-internal-guid-e5a2daf4-7fff-dea4-21dc-21707883d297\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdLLpZz0n5ppydwvz3L-8qxRnw2KV3DVAeORsgCpwux6dbDlr5Kqq6iL4w0bYGhvzbZq_H2pphrBnJDJVpitD1kPdBwi6h6AkM9Q5R_SLBsSvAPd1YK6Jr6GVR7M2z-q_fEypuz5C_Uq7oF5gaEJLD6aiEw?key=DeDRJyT6seelEyAeFidXXWTi\" width=\"518\" height=\"202\"></strong><br>2015 से 2019 तक प्रति वर्ष ईंधन और परिवहन पर कंपनी द्वारा भुगतान की गई औसत राशि (₹ में) ज्ञात कीजिए।</p>",
                    options_en: ["<p>1,17,200</p>", "<p>1,16,500</p>", 
                                "<p>1,18,200</p>", "<p>1,15,200</p>"],
                    options_hi: ["<p>1,17,200</p>", "<p>1,16,500</p>",
                                "<p>1,18,200</p>", "<p>1,15,200</p>"],
                    solution_en: "<p>65.(a) the average amount paid by the company on fuel and transport per year from 2015 to 2019<br>= <math display=\"inline\"><mfrac><mrow><mn>98</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>112</mn><mi>&#160;</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>101</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>133</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>142</mn><mi>&#160;</mi></mrow><mrow><mn>5</mn><mi>&#160;</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>586</mn><mn>5</mn></mfrac></math> &times; 1000 = ₹1,17,200</p>",
                    solution_hi: "<p>65.(a) 2015 से 2019 तक प्रति वर्ष कंपनी द्वारा ईंधन और परिवहन पर भुगतान की गई औसत राशि<br>= <math display=\"inline\"><mfrac><mrow><mn>98</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>112</mn><mi>&#160;</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>101</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>133</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>142</mn><mi>&#160;</mi></mrow><mrow><mn>5</mn><mi>&#160;</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>586</mn><mn>5</mn></mfrac></math> &times; 1000 = ₹1,17,200</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "66",
                    section: "17",
                    question_en: "<p>66. If the system of the following equations has the value of the variables as three consecutive integers, then the value of a is ____.<br>x - y + z = 2a<br>x + 4y - 2z = 3(4 - a)<br>2x - 3y + 4z = 6 - 2a</p>",
                    question_hi: "<p>66. यदि निम्नलिखित समीकरणों की प्रणाली में चरों का मान लगातार तीन पूर्णांकों के रूप में है, तो a का मान ____ है।<br>x - y + z = 2a<br>x + 4y - 2z = 3(4 - a)<br>2x - 3y + 4z = 6 - 2a</p>",
                    options_en: ["<p>2</p>", "<p>4</p>", 
                                "<p>1</p>", "<p>3</p>"],
                    options_hi: ["<p>2</p>", "<p>4</p>",
                                "<p>1</p>", "<p>3</p>"],
                    solution_en: "<p>66.(c) Let the three consecutive numbers <math display=\"inline\"><mi>x</mi></math>, y, and z be 3 , 2 and 1 respectively,<br>x - y + z = 2a &hellip;. (i)<br>Put the value x, y and z, we get<br><math display=\"inline\"><mo>&#8658;</mo></math> 3 - 2 + 1 = 2a<br><math display=\"inline\"><mo>&#8658;</mo></math> a = 1<br>x + 4y - 2z = 3(4 - a) &hellip;. (ii)<br><math display=\"inline\"><mo>&#8658;</mo></math> 3 + 8 - 2 = 3(4 - a)<br><math display=\"inline\"><mo>&#8658;</mo></math> 9 = 3(4 - a)<br><math display=\"inline\"><mo>&#8658;</mo></math> a = 1<br>2x - 3y + 4z = 6 - 2a &hellip;. (iii)<br><math display=\"inline\"><mo>&#8658;</mo></math> 6 - 6 + 4 = 6 - 2a<br><math display=\"inline\"><mo>&#8658;</mo></math> 4 = 6 - 2a<br><math display=\"inline\"><mo>&#8658;</mo></math> a = 1<br>It is clear form in all above equations satisfies the value of x, y and z, so the value of a = 1</p>",
                    solution_hi: "<p>66.(c) माना , तीन क्रमागत संख्याएँ x, y, और z क्रमशः 3, 2 और 1 हैं<br>x - y + z = 2a &hellip;. (i)<br>x, y और z का मान रखने पर <br><math display=\"inline\"><mo>&#8658;</mo></math> 3 - 2 + 1 = 2a<br><math display=\"inline\"><mo>&#8658;</mo></math> a = 1<br>x + 4y - 2z = 3(4 - a) &hellip;. (ii)<br><math display=\"inline\"><mo>&#8658;</mo></math> 3 + 8 - 2 = 3(4 - a)<br><math display=\"inline\"><mo>&#8658;</mo></math> 9 = 3(4 - a)<br><math display=\"inline\"><mo>&#8658;</mo></math> a = 1<br>2x - 3y + 4z = 6 - 2a &hellip;. (iii)<br><math display=\"inline\"><mo>&#8658;</mo></math> 6 - 6 + 4 = 6 - 2a<br><math display=\"inline\"><mo>&#8658;</mo></math> 4 = 6 - 2a<br><math display=\"inline\"><mo>&#8658;</mo></math> a = 1<br>यह स्पष्ट है कि उपरोक्त सभी समीकरणों में x, y और z का मान संतुष्ट होता है, अतः a का मान = 1</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "67",
                    section: "17",
                    question_en: "<p>67. In 2010, a company\'s total expenditure was categorised under different expense heads, and their percentage distribution is illustrated in the bar graph given below.<br><strong id=\"docs-internal-guid-11be5646-7fff-4e92-a46e-c3dabfdce0d7\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXeOawje0eSeu9pjQ0AQ2PDsPZuxkSvK2f-a97h1--6pZ2m-PLfA_jfJwEHm0dYHYfvmEGJW2FCKi5I6tAlIQgvNuKztPXXIB5YhrE2nIH3v5JSDZiOIjFE9pkmytvlq58aoG3nSjc3sDZxhyy5g3BVgHENf?key=DeDRJyT6seelEyAeFidXXWTi\" width=\"313\" height=\"261\"></strong><br>If the expenditure on an advertisement is ₹ 2.1 crore, then the difference between the is? expenditure on transport and taxes</p>",
                    question_hi: "<p>67. 2010 में एक कंपनी के कुल व्यय को विभिन्न व्यय मदों के तहत वर्गीकृत किया गया था और उनका प्रतिशत बंटन नीचे दिए गए बार- ग्राफ में दर्शाया गया है।<br><strong id=\"docs-internal-guid-f40ca092-7fff-29f9-3395-27d9de544631\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfPQv7G7zIS74vpWqXS8MA3WlNMpLS2ZLGU16XbDsUjs6WAgbnkM7HDGaQllxigRe-8O0dsF121UvMj---oifMps4u6SKRL2Ryet1_tkW9-wxsppfuw8P32PiCNuJ7VtWzrTia5gdxqXRxMjeCGl6ZtUvA5?key=DeDRJyT6seelEyAeFidXXWTi\" width=\"370\" height=\"309\"></strong><br>यदि विज्ञापन पर व्यय ₹2.1 करोड़ है, तो परिवहन ओर करो पर व्यय के बीच का अंतर है।</p>",
                    options_en: ["<p>₹32 lakh</p>", "<p>₹42 lakh</p>", 
                                "<p>₹30 lakh</p>", "<p>₹35 lakh</p>"],
                    options_hi: ["<p>₹32 lakh</p>", "<p>₹42 lakh</p>",
                                "<p>₹30 lakh</p>", "<p>₹35 lakh</p>"],
                    solution_en: "<p>67.(d) Expenditure on an advertisement (15%) = 2.1 crore<br><math display=\"inline\"><mo>&#8658;</mo></math> 15 % = 21000000<br><math display=\"inline\"><mo>&#8658;</mo></math> 1 % = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>21000000</mn><mn>15</mn></mfrac></math><br>2.5 % = <math display=\"inline\"><mfrac><mrow><mn>21000000</mn></mrow><mrow><mn>15</mn></mrow></mfrac></math> &times; 2.5 = 3500000 (35 Lakh)</p>",
                    solution_hi: "<p>67.(d) एक विज्ञापन पर व्यय (15%)= 2.1 करोड़<br><math display=\"inline\"><mo>&#8658;</mo></math> 15 % = 21000000<br><math display=\"inline\"><mo>&#8658;</mo></math> 1 % = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>21000000</mn><mn>15</mn></mfrac></math><br>2.5 % = <math display=\"inline\"><mfrac><mrow><mn>21000000</mn></mrow><mrow><mn>15</mn></mrow></mfrac></math> &times; 2.5 = 3500000 (35 लाख)</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "68",
                    section: "17",
                    question_en: "<p>68. At what angle &theta;, the value of sin (&theta;) and cos (&theta;) will have the same value?</p>",
                    question_hi: "<p>68. किस &theta; कोण पर, sin (&theta;) और cos (&theta;) का मान समान होगा?</p>",
                    options_en: ["<p>45&deg;</p>", "<p>30&deg;</p>", 
                                "<p>60&deg;</p>", "<p>75&deg;</p>"],
                    options_hi: ["<p>45&deg;</p>", "<p>30&deg;</p>",
                                "<p>60&deg;</p>", "<p>75&deg;</p>"],
                    solution_en: "<p>68.(a) As we know that , Sin 45&deg; and Cos 45&deg; have the same value i.e <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><msqrt><mn>2</mn></msqrt></mrow></mfrac></math> .</p>",
                    solution_hi: "<p>68.(a) जैसा कि हम जानते हैं कि, Sin 45&deg; और Cos 45&deg; का मान समान यानी <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><msqrt><mn>2</mn></msqrt></mrow></mfrac></math> है ।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "69",
                    section: "17",
                    question_en: "<p>69. The given table shows the number of televisions that were sold by different showrooms in years 2015 to 2019.<br><strong id=\"docs-internal-guid-f28e0ca7-7fff-0fbe-ddf8-93e2e51ceb2b\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcuJxwvzBU8TbRkA_D8iwtHjyp7Ew-Ny-NbZt6eMHU4UaEMPxnTKLWVzZfxci4ihxiHTX09lpJuG15fLZvVMUGhSe7MWNl9K8enNYebnPvEzwW3mIYs9YkWXAhJVD0Q3jgMTk9u-kNlp8y7Z3ORwSLwZPl0?key=DeDRJyT6seelEyAeFidXXWTi\" width=\"339\" height=\"227\"></strong><br>Find the difference between the sale of televisions from showrooms A, B and C in 2017 and the sale of televisions from showrooms B, C and D in 2019?</p>",
                    question_hi: "<p>69. दी गई तालिका वर्ष 2015 से 2019 तक विभिन्न शोरूमों द्वारा बेचे गए टेलीविज़नों की संख्या को दर्शाती है।<br><strong id=\"docs-internal-guid-85bcad29-7fff-3791-ced2-25236f264e9a\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdQ4NUClG8QJ--J0vAFOmgmThZD3UrWhu7w-eMZjdQjApBO3MBEEvW45-vpVE0y_Cpz3Nwl2CUAAm-mIpQvLN0PvEFIwh9MRarbzP4JGjrxRdpTfeg7AytT70XEpzWEOmXHqvnPWAKiqmGHZnwiizjC2uNk?key=DeDRJyT6seelEyAeFidXXWTi\" width=\"332\" height=\"223\"></strong><br>2017 में शोरूम A, B और C से टेलीविज़नों की बिक्री और 2019 में शोरूम B, C और D से टेलीविज़नों की बिक्री के बीच अंतर ज्ञात कीजिए?</p>",
                    options_en: ["<p>8</p>", "<p>5</p>", 
                                "<p>6</p>", "<p>3</p>"],
                    options_hi: ["<p>8</p>", "<p>5</p>",
                                "<p>6</p>", "<p>3</p>"],
                    solution_en: "<p>69.(c) Sale of televisions from showrooms A, B and C in 2017 = 190 + 204 + 212 = 606<br>Sale of televisions from showrooms B, C and D in 2019 = 192 + 205 + 203 = 600<br>Required difference = 606 - 600 = 6</p>",
                    solution_hi: "<p>69.(c) 2017 में शोरूम A, B और C से टेलीविज़न की बिक्री = 190 + 204 + 212 = 606<br>2019 में शोरूम B, C और D से टेलीविजन की बिक्री = 192 + 205 + 203 = 600<br>आवश्यक अंतर = 606 - 600 = 6</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "70",
                    section: "17",
                    question_en: "<p>70. In a circle with centre O, AOC is the diameter. B is a point on the circumference of the circle such that arc AB is <math display=\"inline\"><mfrac><mrow><mn>1</mn><mi>&#160;</mi></mrow><mrow><mn>5</mn></mrow></mfrac></math> of the arc BC. What is the degree measure of &ang;BOC?</p>",
                    question_hi: "<p>70. केंद्र O वाले किसी वृत्त में, AOC व्यास है। B, वृत्त की परिधि पर एक बिंदु इस प्रकार है कि चाप AB, चाप BC का <math display=\"inline\"><mfrac><mrow><mn>1</mn><mi>&#160;</mi></mrow><mrow><mn>5</mn></mrow></mfrac></math> है। &ang;BOC की माप (डिग्री में) कितनी है?</p>",
                    options_en: ["<p>120&deg;</p>", "<p>150&deg;</p>", 
                                "<p>80&deg;</p>", "<p>30&deg;</p>"],
                    options_hi: ["<p>120&deg;</p>", "<p>150&deg;</p>",
                                "<p>80&deg;</p>", "<p>30&deg;</p>"],
                    solution_en: "<p>70.(b) <br><strong id=\"docs-internal-guid-84e3c0ac-7fff-df5b-cb71-3f5ab777413f\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdscrUucalxbCl7oFYAzyZLbodGeCRZtLLcEdJNSGXJaPVYUsD2Y9XvjbguJJkeItJNvtoKDyl3fu26uJGGxHYO5CR9HGYxtMWSlFIcvY16XEgE_mrb9nFLyNgcdTfdocawPxHUTIB_BeDBxRKdzuxpgUY?key=DeDRJyT6seelEyAeFidXXWTi\" width=\"138\" height=\"112\"></strong><br>Here AOC is diameter , and Arc AB = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>5</mn></mfrac></math> &times; arc BC<br>&there4;&ang;AOB = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>5</mn></mfrac></math> &ang;BOC&nbsp;<br><math display=\"inline\"><mo>&#8658;</mo></math> &ang;BOC = 5&ang;AOB <br>Now , &ang;AOB + &ang;BOC = 180&deg;<br><math display=\"inline\"><mo>&#8658;</mo></math> &ang;AOB + 5&ang;AOB = 180&deg;<br><math display=\"inline\"><mo>&#8658;</mo></math> &ang;AOB = 30&deg;<br>&there4;&nbsp;&nbsp;&ang;BOC = 5 &times; 30&deg; = 150&deg;</p>",
                    solution_hi: "<p>70.(b) <br><strong id=\"docs-internal-guid-ac4c97dc-7fff-b102-6a36-70190b70b391\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdscrUucalxbCl7oFYAzyZLbodGeCRZtLLcEdJNSGXJaPVYUsD2Y9XvjbguJJkeItJNvtoKDyl3fu26uJGGxHYO5CR9HGYxtMWSlFIcvY16XEgE_mrb9nFLyNgcdTfdocawPxHUTIB_BeDBxRKdzuxpgUY?key=DeDRJyT6seelEyAeFidXXWTi\" width=\"138\" height=\"112\"></strong><br>यहां AOC व्यास है, और चाप AB = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> &times; arc BC<br>&there4;&ang;AOB = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>5</mn></mfrac></math> &ang;BOC&nbsp;<br><math display=\"inline\"><mo>&#8658;</mo></math> &ang;BOC = 5&ang;AOB <br>अब, &ang;AOB + &ang;BOC = 180&deg;<br><math display=\"inline\"><mo>&#8658;</mo></math> &ang;AOB + 5&ang;AOB = 180&deg;<br><math display=\"inline\"><mo>&#8658;</mo></math> &ang;AOB = 30&deg;<br>&there4;&nbsp;&nbsp;&ang;BOC = 5 &times; 30&deg; = 150&deg;</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "71",
                    section: "17",
                    question_en: "<p>71. Study the given pie chart and select the most appropriate option to fill in the blank.<br>The given pie chart shows the sales of different fruits in a day for a shop.<br>If the total number of fruits sold by a fruit seller in a day is 1200, then the number of bananas sold is ___.<br><strong id=\"docs-internal-guid-5785fe2c-7fff-36fb-52be-2dd5fa0b997b\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXckFS_zCh0t1IuQxVPUOKWd0fT_wwaaR_0J7NnUtl1s9JnpujdJLvBg-1vJpHlu1vlrpyz04-bfiCFld39mwtKUuLLfyM5PRexr2uj3vgLtTdc8gNEknS6bUjSZYo5nBB5IQOg4rlIVi_YT3eXznROOOKw?key=DeDRJyT6seelEyAeFidXXWTi\" width=\"297\" height=\"287\"></strong></p>",
                    question_hi: "<p>71. दिए गए पाई-चार्ट का अध्ययन करें और रिक्त स्थान को भरने के लिए सबसे उपयुक्त विकल्प का चयन करें।<br>दिया गया पाई-चार्ट एक दुकान की एक दिन में विभिन्न फलों की बिक्री दर्शाता है।<br>यदि एक फल विक्रेता द्वारा एक दिन में बेचे गए फलों की कुल संख्या 1200 है, तो बेचे गए केलों की संख्या ___ है।.<br><strong id=\"docs-internal-guid-f55dba5a-7fff-780e-6c94-cfb2a56c959e\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXe5TpETRWf4zMFsI1GMsrjK7hvcAN4JjeKtDnwNxPkq7sUQJBjdLAAPpLx-65ECOZsPShv6PWp0ZMm8w7eImYUEXMJF7ymBzgQVrYy6mMhzPGr4LgQoIHMBJAR3UxCP8Gq1AZWX4N3AeLG7E1NiKVdv44RH?key=DeDRJyT6seelEyAeFidXXWTi\" width=\"261\" height=\"248\"></strong></p>",
                    options_en: ["<p>360</p>", "<p>520</p>", 
                                "<p>240</p>", "<p>480</p>"],
                    options_hi: ["<p>360</p>", "<p>520</p>",
                                "<p>240</p>", "<p>480</p>"],
                    solution_en: "<p>71.(d) Total number of fruits sold in a day = 1200<br><math display=\"inline\"><mo>&#8658;</mo></math> 100 % = 1200 <br>40% = 480</p>",
                    solution_hi: "<p>71.(d) एक दिन में बेचे गए फलों की कुल संख्या = 1200<br><math display=\"inline\"><mo>&#8658;</mo></math> 100 % = 1200 <br>40% = 480</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "72",
                    section: "17",
                    question_en: "<p>72. A circular arc whose radius is 24 cm makes an angle of 60&deg; at the centre. The perimeter of the sector formed is: <br>(Use &pi; = 3.14)</p>",
                    question_hi: "<p>72. 24 cm त्रिज्या वाला एक वृत्तीय चाप केंद्र पर 60&deg; का कोण बनाता है। निर्मित त्रिज्&zwj;यखंड का परिमाप क्&zwj;या होगा?<br>(&pi; = 3.14 का उपयोग कीजिए)</p>",
                    options_en: ["<p>73.22 cm</p>", "<p>63.12 cm</p>", 
                                "<p>74.32 cm</p>", "<p>73.12 cm</p>"],
                    options_hi: ["<p>73.22 cm</p>", "<p>63.12 cm</p>",
                                "<p>74.32 cm</p>", "<p>73.12 cm</p>"],
                    solution_en: "<p>72.(d) Length of arc = <math display=\"inline\"><mfrac><mrow><mi>&#952;</mi></mrow><mrow><mn>360</mn></mrow></mfrac></math> &times; 2&pi;r<br>= <math display=\"inline\"><mfrac><mrow><mn>60</mn></mrow><mrow><mn>360</mn></mrow></mfrac></math> &times; 2 &times; 3.14 &times; 24 = 25.12<br>Perimeter of sector = 24 + 24 + 25.12 = 73.12cm</p>",
                    solution_hi: "<p>72.(d) चाप की लम्बाई = <math display=\"inline\"><mfrac><mrow><mi>&#952;</mi></mrow><mrow><mn>360</mn></mrow></mfrac></math> &times; 2&pi;r<br>= <math display=\"inline\"><mfrac><mrow><mn>60</mn></mrow><mrow><mn>360</mn></mrow></mfrac></math> &times; 2 &times; 3.14 &times; 24 = 25.12<br>त्रिज्यखंड का परिमाप = 24 + 24 + 25.12 = 73.12cm</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "73",
                    section: "17",
                    question_en: "<p>73. If 3 sin A + 4 cos A = 5, then the value of tan A is:</p>",
                    question_hi: "<p>73. यदि 3 sin A + 4 cos A = 5 है, तो tan A का मान ज्ञात कीजिए।</p>",
                    options_en: ["<p><math display=\"inline\"><mfrac><mrow><mn>3</mn><mi>&#160;</mi></mrow><mrow><mn>4</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>3</mn><mi>&#160;</mi></mrow><mrow><mn>5</mn></mrow></mfrac></math></p>", 
                                "<p><math display=\"inline\"><mfrac><mrow><mn>5</mn><mi>&#160;</mi></mrow><mrow><mn>4</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>4</mn><mi>&#160;</mi></mrow><mrow><mn>5</mn></mrow></mfrac></math></p>"],
                    options_hi: ["<p><math display=\"inline\"><mfrac><mrow><mn>3</mn><mi>&#160;</mi></mrow><mrow><mn>4</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>3</mn><mi>&#160;</mi></mrow><mrow><mn>5</mn></mrow></mfrac></math></p>",
                                "<p><math display=\"inline\"><mfrac><mrow><mn>5</mn><mi>&#160;</mi></mrow><mrow><mn>4</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>4</mn><mi>&#160;</mi></mrow><mrow><mn>5</mn></mrow></mfrac></math></p>"],
                    solution_en: "<p>73.(a) <strong>Given:</strong> 3 sin <math display=\"inline\"><mi>A</mi></math> + 4 cos A = 5<br>Squaring both side , we have <br><math display=\"inline\"><msup><mrow></mrow><mrow></mrow></msup><mo>&#8658;</mo><msup><mrow><mi>&#160;</mi><mo>(</mo><mn>3</mn><mi>S</mi><mi>i</mi><mi>n</mi><mi>A</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>4</mn><mi>C</mi><mi>o</mi><mi>s</mi><mi>A</mi><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup></math> = 5<sup>2</sup><br><math display=\"inline\"><mo>&#8658;</mo></math> 9 Sin<sup>2</sup>A + 16Cos<sup>2</sup>A + 24 SinACosA = 25<br><math display=\"inline\"><mo>&#8658;</mo></math> 9 (1 - Cos<sup>2</sup>A) + 16 (1 - Sin<sup>2</sup>A) + 24 SinACosA = 25<br><math display=\"inline\"><mo>&#8658;</mo></math> 16 Sin<sup>2</sup>A + 9Cos<sup>2</sup>A - 24 SinACosA = 0&nbsp;<br><math display=\"inline\"><mo>&#8658;</mo></math> (4 SinA - 3CosA)<sup>2</sup> = 0<br><math display=\"inline\"><mo>&#8658;</mo></math> 4 SinA = 3 CosA<br><math display=\"inline\"><mo>&#8658;</mo></math> tan A = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>4</mn></mfrac></math><br><strong>Short trick: Given: </strong>3 Sin<math display=\"inline\"><mi>A</mi></math> + 4 CosA = 5<br>By using pythagorean triplet = (3, 4 , 5)<br>tan A = <math display=\"inline\"><mfrac><mrow><mi>P</mi><mi>e</mi><mi>r</mi><mi>p</mi><mi>e</mi><mi>n</mi><mi>d</mi><mi>i</mi><mi>c</mi><mi>u</mi><mi>l</mi><mi>a</mi><mi>r</mi></mrow><mrow><mi>B</mi><mi>a</mi><mi>s</mi><mi>e</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>4</mn></mfrac></math></p>",
                    solution_hi: "<p>73.(a) <strong>दिया गया है</strong>, 3 sin <math display=\"inline\"><mi>A</mi></math> + 4 cos A = 5<br>दोनों पक्षों का वर्ग करने पर -<br><math display=\"inline\"><msup><mrow></mrow><mrow></mrow></msup><mo>&#8658;</mo><msup><mrow><mi>&#160;</mi><mo>(</mo><mn>3</mn><mi>S</mi><mi>i</mi><mi>n</mi><mi>A</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>4</mn><mi>C</mi><mi>o</mi><mi>s</mi><mi>A</mi><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup></math> = 5<sup>2</sup><br><math display=\"inline\"><mo>&#8658;</mo></math> 9 Sin<sup>2</sup>A + 16Cos<sup>2</sup>A + 24 SinACosA = 25<br><math display=\"inline\"><mo>&#8658;</mo></math> 9 (1 - Cos<sup>2</sup>A) + 16 (1 - Sin<sup>2</sup>A) + 24 SinACosA = 25<br><math display=\"inline\"><mo>&#8658;</mo></math> 16 Sin<sup>2</sup>A + 9Cos<sup>2</sup>A - 24 SinACosA = 0&nbsp;<br><math display=\"inline\"><mo>&#8658;</mo></math> (4 SinA - 3CosA)<sup>2</sup> = 0<br><math display=\"inline\"><mo>&#8658;</mo></math> 4 SinA = 3 CosA<br><math display=\"inline\"><mo>&#8658;</mo></math> tan A = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>4</mn></mfrac></math><br><strong>शार्ट ट्रिक: दिया गया है, </strong>3 sin <math display=\"inline\"><mi>A</mi></math> + 4 cos A = 5<br>पायथागॉरियन त्रिक (3, 4, 5) का उपयोग करने पर,<br>tan A = <math style=\"font-family: Verdana;\" display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"true\"><mfrac><mi>&#2354;&#2350;&#2381;&#2348;</mi><mi>&#2310;&#2343;&#2366;&#2352;</mi></mfrac></mstyle></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>4</mn></mfrac></math></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "74",
                    section: "17",
                    question_en: "<p>74. A motorboat travelling at some speed can cover 24 km upstream and 40 km downstream in 17 hours. At the same speed it can travel 32 km downstream and 12 km upstream in 10 hours. The speed of the stream is:</p>",
                    question_hi: "<p>74. किसी चाल से चलती हुई एक मोटरबोट धारा की विपरीत दिशा में 24 km और धारा की दिशा में 40 km की दूरी 17 घंटे में तय कर सकती है। समान चाल से यह 10 घंटे में धारा की दिशा में 32 km और धारा की विपरीत दिशा में 12 km की यात्रा कर सकती है। धारा की चाल क्या है?</p>",
                    options_en: ["<p>5 km/h</p>", "<p>4 km/h</p>", 
                                "<p>2 km/h</p>", "<p>3 km/h</p>"],
                    options_hi: ["<p>5 km/h</p>", "<p>4 km/h</p>",
                                "<p>2 km/h</p>", "<p>3 km/h</p>"],
                    solution_en: "<p>74.(d) Let speed of boat in still water = xkm/h <br>Speed of stream = y km/h <br>According to question , <br><math display=\"inline\"><mo>&#8658;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>24</mn><mrow><mi>x</mi><mo>-</mo><mi>y</mi></mrow></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>40</mn><mrow><mi mathvariant=\"normal\">x</mi><mo>+</mo><mi mathvariant=\"normal\">y</mi></mrow></mfrac></math> = 17 &hellip;&hellip; (i)<br><math display=\"inline\"><mo>&#8658;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>12</mn><mrow><mi>x</mi><mo>-</mo><mi>y</mi></mrow></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>32</mn><mrow><mi>x</mi><mo>+</mo><mi>y</mi></mrow></mfrac></math> = 10 &hellip;.(ii)<br>Multiply eq (ii ) by 2 <br><math display=\"inline\"><mo>&#8658;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>24</mn><mrow><mi>x</mi><mo>-</mo><mi>y</mi></mrow></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>64</mn><mrow><mi>x</mi><mo>+</mo><mi>y</mi></mrow></mfrac></math> = 20 &hellip;.(iii)<br>Subtract eq (i) from eq (iii) we get <br><math display=\"inline\"><mo>&#8658;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>24</mn><mrow><mi>x</mi><mo>+</mo><mi>y</mi></mrow></mfrac></math> = 3<br><math display=\"inline\"><mo>&#8658;</mo></math> x + y = 8 &hellip;.(iv)<br>Put this value in eqn (ii) we get <br><math display=\"inline\"><mo>&#8658;</mo></math> x -y = 2 &hellip;.(v)<br>After solving eqn (iv) and (v) we get <br>x = 5 km/h , y = 3 km/h <br>speed of the stream = 3km/h</p>",
                    solution_hi: "<p>74.(d) माना , शांत पानी में नाव की गति = xkm/h <br>धारा की गति = y km/h <br>प्रश्न के अनुसार , <br><math display=\"inline\"><mo>&#8658;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>24</mn><mrow><mi>x</mi><mo>-</mo><mi>y</mi></mrow></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>40</mn><mrow><mi mathvariant=\"normal\">x</mi><mo>+</mo><mi mathvariant=\"normal\">y</mi></mrow></mfrac></math> = 17 &hellip;&hellip; (i)<br><math display=\"inline\"><mo>&#8658;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>12</mn><mrow><mi>x</mi><mo>-</mo><mi>y</mi></mrow></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>32</mn><mrow><mi>x</mi><mo>+</mo><mi>y</mi></mrow></mfrac></math> = 10 &hellip;.(ii)<br>समीकरण (ii) को 2 से गुणा करने पर <br><math display=\"inline\"><mo>&#8658;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>24</mn><mrow><mi>x</mi><mo>-</mo><mi>y</mi></mrow></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>64</mn><mrow><mi>x</mi><mo>+</mo><mi>y</mi></mrow></mfrac></math> = 20 &hellip;.(iii)<br>समीकरण (iii) से समीकरण (i) घटाने पर <br><math display=\"inline\"><mo>&#8658;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>24</mn><mrow><mi>x</mi><mo>+</mo><mi>y</mi></mrow></mfrac></math> = 3&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &rArr; x + y = 8 &hellip;.(iv)<br>इस मान को समीकरण (ii) में रखने पर हमें प्राप्त होता है <br><math display=\"inline\"><mo>&#8658;</mo></math> x - y = 2 &hellip;.(v)<br>समीकरण (iv) और (v) को हल करने के बाद<br>x = 5 km/h , y = 3 km/h <br>धारा की गति = 3km/h</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "75",
                    section: "7",
                    question_en: "<p>75. If ∆ABC and ∆QPR are congruent triangles such that AB = 7 cm, AC = 8 cm, &ang;B = 65&deg; and &ang;C = 75&deg;, then which of the following is true?</p>",
                    question_hi: "<p>75. यदि ∆ABC और ∆QPR सर्वांगसम त्रिभुज इस प्रकार हैं कि AB = 7 cm है, AC = 8 cm है, &ang;B = 65&deg; और &ang;C = 75&deg; है, तो निम्नलिखित में से कौन-सा सत्य है?</p>",
                    options_en: ["<p>&ang;R = 75&deg; and PQ = 8 cm</p>", "<p>PQ = 7 cm and &ang;R = 75&deg;</p>", 
                                "<p>&ang;Q = 40&deg; and RQ = 7 cm</p>", "<p>AC = PR and &ang;P = 65&deg;</p>"],
                    options_hi: ["<p>&ang;R = 75&deg; और PQ = 8 cm</p>", "<p>PQ = 7 cm और &ang;R = 75&deg;</p>",
                                "<p>&ang;Q = 40&deg; और RQ = 7 cm</p>", "<p>AC = PR और &ang;P = 65&deg;</p>"],
                    solution_en: "<p>75.(b) if ∆ABC and ∆QPR are congruent triangles , then their corresponding side and angles are similar<br><strong id=\"docs-internal-guid-466edbc7-7fff-fd61-9892-5b3f3f688889\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXeY2ZJlkqoWt5rR7NlUcVoLvlxA7RVbPkfSdmlCzdrVwODtdqIzlJuLsPJYq3x0oaDG-KeFMk9WjWoLDbY0FwytW0by1u-B4SMUGno9nA108oPykSl2LBTV-EEsXCv7a309Q3HjvQ9FpeqId3rE0mZwn7ot?key=DeDRJyT6seelEyAeFidXXWTi\" width=\"260\" height=\"138\"></strong><br>&there4; PQ = 7 cm and &ang;R = 75&deg; is true .</p>",
                    solution_hi: "<p>75.(b) यदि ∆ABC और ∆QPR सर्वांगसम त्रिभुज हैं, तो उनकी संगत भुजाएँ और कोण समरूप हैं। <br><strong id=\"docs-internal-guid-466edbc7-7fff-fd61-9892-5b3f3f688889\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXeY2ZJlkqoWt5rR7NlUcVoLvlxA7RVbPkfSdmlCzdrVwODtdqIzlJuLsPJYq3x0oaDG-KeFMk9WjWoLDbY0FwytW0by1u-B4SMUGno9nA108oPykSl2LBTV-EEsXCv7a309Q3HjvQ9FpeqId3rE0mZwn7ot?key=DeDRJyT6seelEyAeFidXXWTi\" width=\"260\" height=\"138\"></strong><br>&there4; PQ = 7 सेमी और &ang;R = 75&deg; सत्य है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "76",
                    section: "7",
                    question_en: "76.  Select the INCORRECTLY spelt word.",
                    question_hi: "76.  Select the INCORRECTLY spelt word.",
                    options_en: [" Conscientious", " Mischievious", 
                                " Ostentatious", " Accommodate<br /> "],
                    options_hi: [" Conscientious", " Mischievious",
                                " Ostentatious", " Accommodate"],
                    solution_en: "76.(b) Mischievious<br />\'Mischievous\' is the correct spelling.",
                    solution_hi: "76.(b) Mischievious<br />\'Mischievous\' सही  spelling है। ",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "77",
                    section: "7",
                    question_en: "77. Select the correctly spelt word to fill in the blank.<br />Helium is used with oxygen for asthma treatment because it ____________ very easily.",
                    question_hi: "77. Select the correctly spelt word to fill in the blank.<br />Helium is used with oxygen for asthma treatment because it ____________ very easily.",
                    options_en: [" difusez", " difuses", 
                                "  diffuses", " diffuzes"],
                    options_hi: [" difusez", " difuses",
                                " diffuses", " diffuzes"],
                    solution_en: "77.(c) diffuses<br />‘Diffuses’ is the correct spelling.",
                    solution_hi: "77.(c) diffuses<br />‘Diffuses’ सही  spelling है। ",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "78",
                    section: "7",
                    question_en: "<p>78. Select the most appropriate synonym of the given word.<br>Exceptional</p>",
                    question_hi: "<p>78. Select the most appropriate synonym of the given word.<br>Exceptional</p>",
                    options_en: ["<p>Trivial</p>", "<p>Emotional</p>", 
                                "<p>Total</p>", "<p>Outstanding</p>"],
                    options_hi: ["<p>Trivial</p>", "<p>Emotional</p>",
                                "<p>Total</p>", "<p>Outstanding</p>"],
                    solution_en: "<p>78.(d) <strong>Outstanding-</strong> remarkably good.<br><strong>Exceptional-</strong> unusually excellent.<br><strong>Trivial-</strong> of little importance.<br><strong>Emotional-</strong> relating to feelings.<br><strong>Total-</strong> complete or absolute.</p>",
                    solution_hi: "<p>78.(d) <strong>Outstanding</strong> (उत्कृष्ट)- remarkably good.<br><strong>Exceptional</strong> (असाधारण)- unusually excellent.<br><strong>Trivial</strong> (तुच्छ)- of little importance.<br><strong>Emotional</strong> (भावनात्मक)- relating to feelings.<br><strong>Total</strong> (सम्पूर्ण)- complete or absolute.</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "79",
                    section: "7",
                    question_en: "<p>79. Select the sentence in which the &lsquo;-ing&rsquo; form of verb expresses the continuity of an action/event.</p>",
                    question_hi: "<p>79. Select the sentence in which the &lsquo;-ing&rsquo; form of verb expresses the continuity of an action/event.</p>",
                    options_en: ["<p>Reading novels is my favourite pastime.</p>", "<p>I am fond of reading science fiction.</p>", 
                                "<p>Please do not disturb me. I am preparing for exams.</p>", "<p>The optician advised me to use reading glasses.</p>"],
                    options_hi: ["<p>Reading novels is my favourite pastime.</p>", "<p>I am fond of reading science fiction.</p>",
                                "<p>Please do not disturb me. I am preparing for exams.</p>", "<p>The optician advised me to use reading glasses.</p>"],
                    solution_en: "<p>79.(c) Please do not disturb me. I am preparing for exams.<br>Present continuous tense (is/am/are + V<sub>ing</sub>) is used to denote the continuity of an action / event.</p>",
                    solution_hi: "<p>79.(c) Please do not disturb me. I am preparing for exams.<br>Present continuous tense (is/am/are + V<sub>ing</sub>) का प्रयोग किसी action / event की निरंतरता को दर्शाने के लिए किया जाता है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "80",
                    section: "7",
                    question_en: "<p>80. Select the option that can be used as a one-word substitute for the given group of words.<br>One who eats the flesh of his own species</p>",
                    question_hi: "<p>80. Select the option that can be used as a one-word substitute for the given group of words.<br>One who eats the flesh of his own species</p>",
                    options_en: ["<p>Non-vegetarian</p>", "<p>Vegan</p>", 
                                "<p>Cannibal</p>", "<p>Mammal</p>"],
                    options_hi: ["<p>Non-vegetarian</p>", "<p>Vegan</p>",
                                "<p>Cannibal</p>", "<p>Mammal</p>"],
                    solution_en: "<p>80.(c) <strong>Cannibal-</strong> one who eats the flesh of his own species.<br><strong>Non-vegetarian-</strong> a person who eats meat.<br><strong>Vegan-</strong> a strict vegetarian who does not use animals or dairy products for food.<br><strong>Mammal-</strong> any animal of which the female feeds her young on milk from her own body.</p>",
                    solution_hi: "<p>80.(c) <strong>Cannibal</strong> (नरभक्षी)- one who eats the flesh of his own species.<br><strong>Non-vegetarian</strong> (मांसाहारी)- a person who eats meat.<br><strong>Vegan</strong> (शुद्ध शाकाहारी)- a strict vegetarian who does not use animals or dairy products for food.<br><strong>Mammal</strong> (स्तनपायी)- any animal of which the female feeds her young on milk from her own body.</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "81",
                    section: "7",
                    question_en: "<p>81. Select the most appropriate ANTONYM of the underlined word in the given sentence.<br>The appearance of most of the animals in jungle is <span style=\"text-decoration: underline;\">deceptive</span> in nature.</p>",
                    question_hi: "<p>81. Select the most appropriate ANTONYM of the underlined word in the given sentence.<br>The appearance of most of the animals in jungle is <span style=\"text-decoration: underline;\">deceptive</span> in nature.</p>",
                    options_en: ["<p>honest</p>", "<p>illusive</p>", 
                                "<p>ugly</p>", "<p>beautiful</p>"],
                    options_hi: ["<p>honest</p>", "<p>illusive</p>",
                                "<p>ugly</p>", "<p>beautiful</p>"],
                    solution_en: "<p>81.(a) <strong>Honest</strong><br><strong>Deceptive-</strong> misleading or dishonest<br><strong>Illusive-</strong> based on illusion.<br><strong>Ugly-</strong> unattractive or displeasing.</p>",
                    solution_hi: "<p>81.(a) <strong>Honest</strong><br><strong>Deceptive</strong> (छलपूर्ण)- misleading or dishonest<br><strong>Illusive</strong> (भ्रामक)- based on illusion.<br><strong>Ugly</strong> (अनाकर्षक)- unattractive or displeasing.</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "82",
                    section: "7",
                    question_en: "82. Select the option that expresses the given sentence in passive voice.<br />Dipesh will send Rakesh away to college for certain reasons.",
                    question_hi: "82. Select the option that expresses the given sentence in passive voice.<br />Dipesh will send Rakesh away to college for certain reasons.",
                    options_en: [" Rakesh will have to be sent away to college for certain reasons by Dipesh.", " Rakesh will be sent away to college for certain reasons by Dipesh.", 
                                " Rakesh would have been sent away to college for certain reasons by Dipesh.", " Rakesh would be sent away to college for certain reasons by Dipesh."],
                    options_hi: [" Rakesh will have to be sent away to college for certain reasons by Dipesh.", " Rakesh will be sent away to college for certain reasons by Dipesh.",
                                " Rakesh would have been sent away to college for certain reasons by Dipesh.", " Rakesh would be sent away to college for certain reasons by Dipesh."],
                    solution_en: "<p>82.(b) Rakesh will be sent away to college for certain reasons by Dipesh. (Correct)<br>(a) Rakesh <span style=\"text-decoration: underline;\">will have to</span> be sent away to college for certain reasons by Dipesh. (Incorrect Verb)<br>(b) Rakesh <span style=\"text-decoration: underline;\">would have been</span> sent away to college for certain reasons by Dipesh. (Incorrect Verb)<br>(c) Rakesh <span style=\"text-decoration: underline;\">would</span> be sent away to college for certain reasons by Dipesh. (Incorrect Modal)</p>",
                    solution_hi: "<p>82.(b) Rakesh will be sent away to college for certain reasons by Dipesh. (Correct)<br>(a) Rakesh <span style=\"text-decoration: underline;\">will have to</span> be sent away to college for certain reasons by Dipesh. (गलत Verb)<br>(b) Rakesh <span style=\"text-decoration: underline;\">would have been</span> sent away to college for certain reasons by Dipesh. (गलत Verb)<br>(c) Rakesh <span style=\"text-decoration: underline;\">would</span> be sent away to college for certain reasons by Dipesh. (गलत Modal)</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "83",
                    section: "7",
                    question_en: "<p>83. Select the most appropriate ANTONYM of the underlined word.<br>The long-standing <span style=\"text-decoration: underline;\">feud</span> between the two villages was settled due to the intervention of the two chiefs.</p>",
                    question_hi: "<p>83. Select the most appropriate ANTONYM of the underlined word.<br>The long-standing <span style=\"text-decoration: underline;\">feud</span> between the two villages was settled due to the intervention of the two chiefs.</p>",
                    options_en: ["<p>Humility</p>", "<p>Harmony</p>", 
                                "<p>Humble</p>", "<p>Honesty</p>"],
                    options_hi: ["<p>Humility</p>", "<p>Harmony</p>",
                                "<p>Humble</p>", "<p>Honesty</p>"],
                    solution_en: "<p>83.(b) <strong>Harmony-</strong> a state of peaceful existence and agreement.<br><strong>Feud-</strong> a prolonged and bitter quarrel.<br><strong>Humility-</strong> the quality of having a modest or low view of one\'s importance.<br><strong>Humble-</strong> not arrogant or assertive.</p>",
                    solution_hi: "<p>83.(b) <strong>Harmony</strong> (सद्भाव)- a state of peaceful existence and agreement.<br><strong>Feud</strong> (झगड़ा)- a prolonged and bitter quarrel.<br><strong>Humility</strong> (विनम्रता)- the quality of having a modest or low view of one\'s importance.<br><strong>Humble</strong> (विनम्र)- not arrogant or assertive.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "84",
                    section: "7",
                    question_en: "<p>84. Select the most appropriate synonym of the given word.<br>Forsake</p>",
                    question_hi: "<p>84. Select the most appropriate synonym of the given word.<br>Forsake</p>",
                    options_en: ["<p>Accelerate</p>", "<p>Reveal</p>", 
                                "<p>Abandon</p>", "<p>Clutch</p>"],
                    options_hi: ["<p>Accelerate</p>", "<p>Reveal</p>",
                                "<p>Abandon</p>", "<p>Clutch</p>"],
                    solution_en: "<p>84.(c) <strong>Abandon-</strong> to give up completely.<br><strong>Forsake-</strong> abandon or leave.<br><strong>Accelerate-</strong> to increase in speed.<br><strong>Reveal-</strong> to make known or disclose.<br><strong>Clutch-</strong> to grasp or seize tightly.</p>",
                    solution_hi: "<p>84.(c) <strong>Abandon</strong> (त्यागना)- to give up completely.<br><strong>Forsake</strong> (त्यागना)- abandon or leave.<br><strong>Accelerate</strong> (त्वरित करना)- to increase in speed.<br><strong>Reveal</strong> (प्रकट करना)- to make known or disclose.<br><strong>Clutch</strong> (कसके पकड़ना)- to grasp or seize tightly.</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "85",
                    section: "7",
                    question_en: "85. Select the most appropriate homophone to fill in the blank.<br />He ________ his bike as fast as he could to reach home.",
                    question_hi: "85. Select the most appropriate homophone to fill in the blank.<br />He ________ his bike as fast as he could to reach home.",
                    options_en: [" rode", " road", 
                                " rowed", " rote"],
                    options_hi: [" rode", " road",
                                " rowed", " rote"],
                    solution_en: "85.(a) rode<br />‘Rode’ is the past form of ‘ride’ which means to travel by sitting on a horse, bicycle, or motorcycle. The given sentence states that he rode his bike as fast as he could to reach home. Hence, ‘rode’ is the most appropriate answer.",
                    solution_hi: "85.(a) rode<br />‘Ride’ का past form ‘Rode’ है जिसका अर्थ है घोड़े, साइकिल या मोटरसाइकिल पर बैठकर यात्रा करना। दिए गए sentence में कहा गया है कि वह घर पहुँचने के लिए जितनी तेज़ी से हो सकता था, अपनी bike चलाता रहा। अतः, ‘rode’ सबसे उपयुक्त उत्तर है।",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "86",
                    section: "7",
                    question_en: "<p>86. Select the most appropriate option that can substitute the underlined segment in the given sentence.<br>Sivamani <span style=\"text-decoration: underline;\">is working</span> as an electrician for the past 22 years.</p>",
                    question_hi: "<p>86. Select the most appropriate option that can substitute the underlined segment in the given sentence.<br>Sivamani <span style=\"text-decoration: underline;\">is working</span> as an electrician for the past 22 years.</p>",
                    options_en: ["<p>had been worked</p>", "<p>has been working</p>", 
                                "<p>have been working</p>", "<p>would work</p>"],
                    options_hi: ["<p>had been worked</p>", "<p>has been working</p>",
                                "<p>have been working</p>", "<p>would work</p>"],
                    solution_en: "<p>86.(b) has been working<br>Perfect continuous tense is used when the time period is given with &lsquo;for/since&rsquo;. &lsquo;Singular subject + has been + V<sub>ing</sub>&rsquo; is the correct grammatical structure for this sentence. Hence, &lsquo;has been working&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>86.(b) has been working<br>जब &lsquo;for/since&rsquo; के साथ time period दी जाती है तो Perfect continuous tense का प्रयोग किया जाता है। &lsquo;Singular subject + has been + V<sub>ing</sub>&rsquo; इस sentence के लिए सही grammatical structure है। अतः, &lsquo;has been working&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "87",
                    section: "7",
                    question_en: "87. Select the most appropriate idiom to fill the blank in the given situation.<br />I thought Sadhna would always stick by me, but when I got into trouble, she turned out to be a/an ___________.",
                    question_hi: "87. Select the most appropriate idiom to fill the blank in the given situation.<br />I thought Sadhna would always stick by me, but when I got into trouble, she turned out to be a/an ___________.",
                    options_en: [" hard nut to crack", " open secret", 
                                " fair-weather friend", " white elephant"],
                    options_hi: [" hard nut to crack", " open secret",
                                " fair-weather friend", " white elephant"],
                    solution_en: "87.(c) fair-weather friend<br />‘Fair-weather friend’ is an idiom which means ‘a person who is dependable in good times but is not in times of trouble’. The given sentence states that I thought Sadhna would always stick by me, but when I got into trouble, she turned out to be a fair-weather friend. Hence, ‘fair-weather friend’ is the most appropriate answer.",
                    solution_hi: "87.(c) fair-weather friend<br />‘Fair-weather friend’  एक idiom है जिसका अर्थ है ऐसा व्यक्ति जो अच्छे समय में भरोसेमंद हो लेकिन मुसीबत के समय में साथ न दे। दिए गए sentence में कहा गया है कि मुझे लगा कि साधना हमेशा मेरा साथ देगी, लेकिन जब मैं मुसीबत में पड़ा, तो वह एक स्वार्थी मित्र निकली। अतः,  ‘fair-weather friend’ सबसे उपयुक्त उत्तर है।",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "88",
                    section: "7",
                    question_en: "<p>88. Select the most appropriate option to substitute the underlined word segment in the given sentence.<br>Her thinning grey hair was <span style=\"text-decoration: underline;\">hanging on</span> her bony forehead.</p>",
                    question_hi: "<p>88. Select the most appropriate option to substitute the underlined word segment in the given sentence.<br>Her thinning grey hair was <span style=\"text-decoration: underline;\">hanging on</span> her bony forehead.</p>",
                    options_en: ["<p>falling about</p>", "<p>urging ahead</p>", 
                                "<p>straggling over</p>", "<p>sinking from</p>"],
                    options_hi: ["<p>falling about</p>", "<p>urging ahead</p>",
                                "<p>straggling over</p>", "<p>sinking from</p>"],
                    solution_en: "<p>88.(c) straggling over<br>&lsquo;Straggling over&rsquo; means spread out in an irregular way. The given sentence states that her thinning grey hair was spread out irregularly over her bony forehead. Hence, &lsquo;straggling over&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>88.(c) straggling over<br>&lsquo;Straggling over&rsquo; का अर्थ है अनियमित तरीके से बिखरे हुआ। दिए गए sentence में कहा गया है कि उसके पतले grey hair उसके bony forehead पर अनियमित रूप से बिखरे हुए थे। अतः, &lsquo;straggling over&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "89",
                    section: "7",
                    question_en: "<p>89. Select the option that can be used as a one-word substitute for the underlined segment.<br>My father always tells <span style=\"text-decoration: underline;\">amusing and true stories</span> from his childhood at bedtime.</p>",
                    question_hi: "<p>89. Select the option that can be used as a one-word substitute for the underlined segment.<br>My father always tells <span style=\"text-decoration: underline;\">amusing and true stories</span> from his childhood at bedtime.</p>",
                    options_en: ["<p>anecdotes</p>", "<p>sonnets</p>", 
                                "<p>fables</p>", "<p>allegories</p>"],
                    options_hi: ["<p>anecdotes</p>", "<p>sonnets</p>",
                                "<p>fables</p>", "<p>allegories</p>"],
                    solution_en: "<p>89.(a) <strong>Anecdotes-</strong> amusing and true stories.<br><strong>Sonnets-</strong> a poem that has 14 lines and a particular pattern of rhyme and word arrangement.<br><strong>Fables-</strong> a short story, typically with animals as characters, conveying a moral.<br><strong>Allegories-</strong> a story, poem, or picture that can be interpreted to reveal a hidden meaning, typically a moral or political one.</p>",
                    solution_hi: "<p>89.(a) <strong>Anecdotes</strong> (उपाख्यान)- amusing and true stories.<br><strong>Sonnets</strong> (चौदह पंक्तियों का लघुकाव्य)- a poem that has 14 lines and a particular pattern of rhyme and word arrangement.<br><strong>Fables</strong> (दंतकथाएँ)- a short story, typically with animals as characters, conveying a moral.<br><strong>Allegories</strong> (द्दष्टान्तकथा)- a story, poem, or picture that can be interpreted to reveal a hidden meaning, typically a moral or political one.</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "90",
                    section: "7",
                    question_en: "<p>90. Select the option that spots the error in the use of preposition in the given sentence.<br>Deepa and Seema are both good with running, but Deepa is also good at catching the ball and throwing it to the right corner in time.</p>",
                    question_hi: "<p>90. Select the option that spots the error in the use of preposition in the given sentence.<br>Deepa and Seema are both good with running, but Deepa is also good at catching the ball and throwing it to the right corner in time.</p>",
                    options_en: ["<p>to the right corner.</p>", "<p>at catching the ball</p>", 
                                "<p>good with running</p>", "<p>in time</p>"],
                    options_hi: ["<p>to the right corner.</p>", "<p>at catching the ball</p>",
                                "<p>good with running</p>", "<p>in time</p>"],
                    solution_en: "<p>90.(c) good with running<br>Preposition &lsquo;at&rsquo; is used to show that someone is good at doing something. &lsquo;Good + at + V<sub>ing</sub>&rsquo; is the correct structure. Hence, &lsquo;good at running&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>90.(c) good with running<br>Preposition &lsquo;at&rsquo; का प्रयोग यह दर्शाने के लिए किया जाता है कि कोई व्यक्ति किसी काम को करने में अच्छा है। &lsquo;Good + at + V<sub>ing</sub>&rsquo; सही structure है। अतः, &lsquo;good at running&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "91",
                    section: "7",
                    question_en: "<p>91. Select the most appropriate option that can substitute the underlined word in the given sentence.<br>An <span style=\"text-decoration: underline;\">intense</span> wish to learn something keeps the nerves active.</p>",
                    question_hi: "<p>91. Select the most appropriate option that can substitute the underlined word in the given sentence.<br>An <span style=\"text-decoration: underline;\">intense</span> wish to learn something keeps the nerves active.</p>",
                    options_en: ["<p>powerful</p>", "<p>fixed</p>", 
                                "<p>lazy</p>", "<p>half-hearted</p>"],
                    options_hi: ["<p>powerful</p>", "<p>fixed</p>",
                                "<p>lazy</p>", "<p>half-hearted</p>"],
                    solution_en: "<p>91.(a) powerful<br>&lsquo;Intense&rsquo; means the same as powerful. The given sentence states that a powerful wish to learn something keeps the nerves active. Hence, &lsquo;powerful&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>91.(a) powerful<br>&lsquo;Intense&rsquo; का अर्थ शक्तिशाली(powerful) के समान है। दिए गए sentence में कहा गया है कि कुछ सीखने की प्रबल इच्छा nerves को सक्रिय रखती है। अतः, &lsquo;powerful&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "92",
                    section: "7",
                    question_en: "<p>92. Select the most appropriate option that can substitute the idiom in the given brackets.<br>His PhD degree seems to be (Penelope&rsquo;s web).</p>",
                    question_hi: "<p>92. Select the most appropriate option that can substitute the idiom in the given brackets.<br>His PhD degree seems to be (Penelope&rsquo;s web).</p>",
                    options_en: ["<p>An important task</p>", "<p>A difficult task</p>", 
                                "<p>A secret task</p>", "<p>An endless task</p>"],
                    options_hi: ["<p>An important task</p>", "<p>A difficult task</p>",
                                "<p>A secret task</p>", "<p>An endless task</p>"],
                    solution_en: "<p>92.(d) <strong>Penelope&rsquo;s web-</strong> an endless task.</p>",
                    solution_hi: "<p>92.(d) <strong>Penelope&rsquo;s web-</strong> an endless task./ अंतहीन कार्य।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "93",
                    section: "7",
                    question_en: "<p>93. Select the option that can be used as a one-word substitute for the underlined group of words.<br><span style=\"text-decoration: underline;\">Humour that describes the weaknesses of the other person</span> is itself a crime among peer groups.</p>",
                    question_hi: "<p>93. Select the option that can be used as a one-word substitute for the underlined group of words.<br><span style=\"text-decoration: underline;\">Humour that describes the weaknesses of the other person</span> is itself a crime among peer groups.</p>",
                    options_en: ["<p>A joke</p>", "<p>Attire</p>", 
                                "<p>Satire</p>", "<p>A curse</p>"],
                    options_hi: ["<p>A joke</p>", "<p>Attire</p>",
                                "<p>Satire</p>", "<p>A curse</p>"],
                    solution_en: "<p>93.(c) <strong>Satire-</strong> humour that describes the weaknesses of the other person.<br><strong>A</strong> <strong>joke-</strong> something said or done to provoke laughter.<br><strong>Attire-</strong> clothes, especially of a particular or formal type.<br><strong>A curse-</strong> magic words that are intended to bring bad luck to someone.</p>",
                    solution_hi: "<p>93.(c) <strong>Satire</strong> (व्यंग्य)- humour that describes the weaknesses of the other person.<br><strong>A joke </strong>(मज़ाक)- something said or done to provoke laughter.<br><strong>Attire</strong> (पोशाक)- clothes, especially of a particular or formal type.<br><strong>A curse</strong> (अभिशाप)- magic words that are intended to bring bad luck to someone.</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "94",
                    section: "7",
                    question_en: "<p>94. Select the option that expresses the given sentence in active voice.<br>Why are you repeatedly called by her?</p>",
                    question_hi: "<p>94. Select the option that expresses the given sentence in active voice.<br>Why are you repeatedly called by her?</p>",
                    options_en: ["<p>Why she calls you repeatedly?</p>", "<p>Why had she called you repeatedly?</p>", 
                                "<p>Why was she calling you repeatedly?</p>", "<p>Why does she call you repeatedly?</p>"],
                    options_hi: ["<p>Why she calls you repeatedly?</p>", "<p>Why had she called you repeatedly?</p>",
                                "<p>Why was she calling you repeatedly?</p>", "<p>Why does she call you repeatedly?</p>"],
                    solution_en: "<p>94.(d) Why does she call you repeatedly? (Correct)<br>(a) Why she calls you repeatedly? (Incorrect Sentence Structure)<br>(b) Why <span style=\"text-decoration: underline;\">had she called</span> you repeatedly? (Incorrect Tense)<br>(c) Why <span style=\"text-decoration: underline;\">was she calling</span> you repeatedly? (Incorrect Tense)</p>",
                    solution_hi: "<p>94.(d) Why does she call you repeatedly? (Correct)<br>(a) Why she calls you repeatedly? (गलत Sentence Structure)<br>(b) Why <span style=\"text-decoration: underline;\">had she called</span> you repeatedly? (गलत Tense)<br>(c) Why <span style=\"text-decoration: underline;\">was she calling</span> you repeatedly? (गलत Tense)</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "95",
                    section: "7",
                    question_en: "<p>95. Select the most appropriate ANTONYM of the given word.<br>Autonomy</p>",
                    question_hi: "<p>95. Select the most appropriate ANTONYM of the given word.<br>Autonomy</p>",
                    options_en: ["<p>Dependence</p>", "<p>Permanence</p>", 
                                "<p>Independence</p>", "<p>Economical</p>"],
                    options_hi: ["<p>Dependence</p>", "<p>Permanence</p>",
                                "<p>Independence</p>", "<p>Economical</p>"],
                    solution_en: "<p>95.(a) <strong>Dependence-</strong> reliance on something or someone.<br><strong>Autonomy-</strong> self-governance or independence.<br><strong>Permanence-</strong> the state of lasting indefinitely.<br><strong>Independence-</strong> freedom from outside control or support.<br><strong>Economical-</strong> being careful about spending or using resources efficiently.</p>",
                    solution_hi: "<p>95.(a) <strong>Dependence</strong> (निर्भरता)- reliance on something or someone.<br><strong>Autonomy</strong> (स्वशासन)- self-governance or independence.<br><strong>Permanence</strong> (स्थायित्व)- the state of lasting indefinitely.<br><strong>Independence</strong> (स्वतंत्रता)- freedom from outside control or support.<br><strong>Economical</strong> (मितव्ययी)- being careful about spending or using resources efficiently.</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "96",
                    section: "7",
                    question_en: "<p>96. <strong>Cloze Test:</strong><br>In the following passage, some words have been deleted. Read the passage carefully and select the most appropriate option to fill in each blank.<br>The limbs of the flying squirrel are interlinked with a membrane. When the creature (96)________ into the air, the membrane opens and the squirrel seems to (97)_______ swiftly downwards. This movement gives the (98)_______ of flight. However, it is not flying really. It is a (99)_______ in the air from a higher level. It is also noteworthy that a squirrel cannot (100)_____ its direction while it is in flying mode.<br>Select the most appropriate option to fill in blank number 96.</p>",
                    question_hi: "<p>96. <strong>Cloze Test:</strong><br>In the following passage, some words have been deleted. Read the passage carefully and select the most appropriate option to fill in each blank.<br>The limbs of the flying squirrel are interlinked with a membrane. When the creature (96)________ into the air, the membrane opens and the squirrel seems to (97)_______ swiftly downwards. This movement gives the (98)_______ of flight. However, it is not flying really. It is a (99)_______ in the air from a higher level. It is also noteworthy that a squirrel cannot (100)_____ its direction while it is in flying mode.<br>Select the most appropriate option to fill in blank number 96.</p>",
                    options_en: ["<p>spring</p>", "<p>cross</p>", 
                                "<p>hurdle</p>", "<p>leaps</p>"],
                    options_hi: ["<p>spring</p>", "<p>cross</p>",
                                "<p>hurdle</p>", "<p>leaps</p>"],
                    solution_en: "<p>96.(d) leaps<br>&lsquo;Leap&rsquo; means jump or spring a long way, to a great height. The given passage states that when the creature leaps into the air, the membrane opens. Hence, &lsquo;leaps&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>96.(d) leaps<br>&lsquo;Leap&rsquo; का अर्थ है बहुत दूर, बहुत ऊँचाई तक कूदना या उछलना। दिए गए passage में बताया गया है कि जब creature हवा में छलांग लगाता है, तो membrane खुल जाती है। अतः , &lsquo;leaps&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "97",
                    section: "7",
                    question_en: "<p>97. <strong>Cloze Test:</strong><br>In the following passage, some words have been deleted. Read the passage carefully and select the most appropriate option to fill in each blank.<br>The limbs of the flying squirrel are interlinked with a membrane. When the creature (96)________ into the air, the membrane opens and the squirrel seems to (97)_______ swiftly downwards. This movement gives the (98)_______ of flight. However, it is not flying really. It is a (99)_______ in the air from a higher level. It is also noteworthy that a squirrel cannot (100)_____ its direction while it is in flying mode.<br>Select the most appropriate option to fill in blank number 97.</p>",
                    question_hi: "<p>97. <strong>Cloze Test:</strong><br>In the following passage, some words have been deleted. Read the passage carefully and select the most appropriate option to fill in each blank.<br>The limbs of the flying squirrel are interlinked with a membrane. When the creature (96)________ into the air, the membrane opens and the squirrel seems to (97)_______ swiftly downwards. This movement gives the (98)_______ of flight. However, it is not flying really. It is a (99)_______ in the air from a higher level. It is also noteworthy that a squirrel cannot (100)_____ its direction while it is in flying mode.<br>Select the most appropriate option to fill in blank number 97.</p>",
                    options_en: ["<p>skate</p>", "<p>skim</p>", 
                                "<p>wheel</p>", "<p>glide</p>"],
                    options_hi: ["<p>skate</p>", "<p>skim</p>",
                                "<p>wheel</p>", "<p>glide</p>"],
                    solution_en: "<p>97.(d) glide<br>&lsquo;Glide&rsquo; means move with a smooth, quiet continuous motion. The given passage states that when the creature leaps into the air, the membrane opens and the squirrel seems to glide swiftly downwards. Hence, &lsquo;glide&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>97.(d) glide<br>&lsquo;Glide&rsquo; का अर्थ है एक सहज, शांत निरंतर गति के साथ चलना। दिए गए passage में कहा गया है कि जब creature हवा में उछलता है, तो membrane खुल जाती है और गिलहरी (squirrel) तेज़ी से नीचे की ओर फिसलती हुई प्रतीत होती है। अतः, &lsquo;glide&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "98",
                    section: "7",
                    question_en: "<p>98. <strong>Cloze Test:</strong><br>In the following passage, some words have been deleted. Read the passage carefully and select the most appropriate option to fill in each blank.<br>The limbs of the flying squirrel are interlinked with a membrane. When the creature (96)________ into the air, the membrane opens and the squirrel seems to (97)_______ swiftly downwards. This movement gives the (98)_______ of flight. However, it is not flying really. It is a (99)_______ in the air from a higher level. It is also noteworthy that a squirrel cannot (100)_____ its direction while it is in flying mode.<br>Select the most appropriate option to fill in blank number 98.</p>",
                    question_hi: "<p>98. <strong>Cloze Test:</strong><br>In the following passage, some words have been deleted. Read the passage carefully and select the most appropriate option to fill in each blank.<br>The limbs of the flying squirrel are interlinked with a membrane. When the creature (96)________ into the air, the membrane opens and the squirrel seems to (97)_______ swiftly downwards. This movement gives the (98)_______ of flight. However, it is not flying really. It is a (99)_______ in the air from a higher level. It is also noteworthy that a squirrel cannot (100)_____ its direction while it is in flying mode.<br>Select the most appropriate option to fill in blank number 98.</p>",
                    options_en: [" affect", " suspicion", 
                                " fancy", " impression"],
                    options_hi: [" affect", " suspicion",
                                " fancy", " impression"],
                    solution_en: "98.(d) impression<br />‘Impression’ means the way that something seems to a person. The given passage states that this movement gives the impression of flight. Hence, ‘impression’ is the most appropriate answer.",
                    solution_hi: "98.(d) impression<br />‘Impression’ का अर्थ है जैसे किसी व्यक्ति को कुछ प्रतीत होता है। दिए गए passage में कहा गया है कि यह गति flight का आभास देती है। अतः, ‘impression’ सबसे उपयुक्त उत्तर है।",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "99",
                    section: "7",
                    question_en: "<p>99. <strong>Cloze Test:</strong><br>In the following passage, some words have been deleted. Read the passage carefully and select the most appropriate option to fill in each blank.<br>The limbs of the flying squirrel are interlinked with a membrane. When the creature (96)________ into the air, the membrane opens and the squirrel seems to (97)_______ swiftly downwards. This movement gives the (98)_______ of flight. However, it is not flying really. It is a (99)_______ in the air from a higher level. It is also noteworthy that a squirrel cannot (100)_____ its direction while it is in flying mode.<br>Select the most appropriate option to fill in blank number 99.</p>",
                    question_hi: "<p>99. <strong>Cloze Test:</strong><br>In the following passage, some words have been deleted. Read the passage carefully and select the most appropriate option to fill in each blank.<br>The limbs of the flying squirrel are interlinked with a membrane. When the creature (96)________ into the air, the membrane opens and the squirrel seems to (97)_______ swiftly downwards. This movement gives the (98)_______ of flight. However, it is not flying really. It is a (99)_______ in the air from a higher level. It is also noteworthy that a squirrel cannot (100)_____ its direction while it is in flying mode.<br>Select the most appropriate option to fill in blank number 99.</p>",
                    options_en: ["<p>progress</p>", "<p>motion</p>", 
                                "<p>sign</p>", "<p>wave</p>"],
                    options_hi: ["<p>progress</p>", "<p>motion</p>",
                                "<p>sign</p>", "<p>wave</p>"],
                    solution_en: "<p>99.(b) motion<br>The given passage states that it is a motion in the air from a higher level. Hence, &lsquo;motion&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>99.(b) motion<br>दिए गए passage में कहा गया है कि यह उच्च स्तर(higher level) से हवा में होने वाली गति है। अतः, &lsquo;motion&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "100",
                    section: "misc",
                    question_en: "<p>100. <strong>Cloze Test:</strong><br>In the following passage, some words have been deleted. Read the passage carefully and select the most appropriate option to fill in each blank.<br>The limbs of the flying squirrel are interlinked with a membrane. When the creature (96)________ into the air, the membrane opens and the squirrel seems to (97)_______ swiftly downwards. This movement gives the (98)_______ of flight. However, it is not flying really. It is a (99)_______ in the air from a higher level. It is also noteworthy that a squirrel cannot (100)_____ its direction while it is in flying mode.<br>Select the most appropriate option to fill in blank number 100.</p>",
                    question_hi: "<p>100. <strong>Cloze Test:</strong><br>In the following passage, some words have been deleted. Read the passage carefully and select the most appropriate option to fill in each blank.<br>The limbs of the flying squirrel are interlinked with a membrane. When the creature (96)________ into the air, the membrane opens and the squirrel seems to (97)_______ swiftly downwards. This movement gives the (98)_______ of flight. However, it is not flying really. It is a (99)_______ in the air from a higher level. It is also noteworthy that a squirrel cannot (100)_____ its direction while it is in flying mode.<br>Select the most appropriate option to fill in blank number 100.</p>",
                    options_en: ["<p>adopt</p>", "<p>replace</p>", 
                                "<p>adept</p>", "<p>swap</p>"],
                    options_hi: ["<p>adopt</p>", "<p>replace</p>",
                                "<p>adept</p>", "<p>swap</p>"],
                    solution_en: "<p>100.(d) swap<br>&lsquo;Swap&rsquo; means to change. The given passage states that it is also noteworthy that a squirrel cannot swap its direction while it is in flying mode. Hence, &lsquo;swap&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>100.(d) swap<br>&lsquo;Swap&rsquo; का अर्थ है बदलना। दिए गए passage में यह भी ध्यान देने योग्य बात है कि गिलहरी flying mode में अपनी दिशा नहीं बदल सकती है। अतः , &lsquo;swap&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>