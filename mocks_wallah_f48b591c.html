<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #7209b7;
            --secondary: #4361ee;
            --accent: #f72585;
            --success: #06d6a0;
            --warning: #ffd166;
            --danger: #ef476f;
            --light: #f8f9fa;
            --dark: #212529;
        }
        body {
            background-color: #f0f2f5;
            font-size: 16px;
            padding-bottom: 60px; /* Space for fixed navigation */
        }
        .navbar {
            background: linear-gradient(135deg, var(--primary), var(--secondary)) !important;
            box-shadow: 0 2px 10px rgba(0,0,0,.1);
            padding: 0.5rem 1rem;
        }
        .navbar-brand {
            font-size: 1.1rem;
            margin-right: 0;
        }
        .navbar .container-fluid {
            justify-content: space-between;
        }
        .controls-group {
            display: flex;
            align-items: center;
            flex-wrap: nowrap;
        }
        .timer-display {
            background: linear-gradient(135deg, #4361ee, #3a0ca3);
            color: white;
            border-radius: 50px;
            box-shadow: 0 3px 8px rgba(0,0,0,.1);
            padding: 0.25rem 0.75rem;
            font-size: 0.875rem;
            white-space: nowrap;
        }
        .nav-btn {
            padding: 0.25rem 0.75rem;
            font-size: 0.875rem;
            margin-left: 0.25rem;
        }
        .question-nav {
            position: fixed;
            top: 56px;
            right: 0;
            height: calc(100vh - 56px);
            width: 280px;
            transform: translateX(280px);
            transition: transform .3s;
            z-index: 100;
            background: #fff;
            box-shadow: -5px 0 15px rgba(0,0,0,.1);
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .main-content {
            transition: margin .3s;
            min-height: calc(100vh - 116px);
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.5;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(32px, 1fr));
            gap: 6px;
        }
        .q-box {
            width: 35px;
            height: 35px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 5px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid #dee2e6;
            background: #fff;
            transition: all .2s;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: 0 3px 5px rgba(0,0,0,.1);
        }
        .q-box.attempted {
            background-color: var(--secondary);
            color: white;
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,.15);
        }
        .card {
            border-radius: 12px;
            overflow: hidden;
            border: none;
            box-shadow: 0 4px 12px rgba(0,0,0,.08);
        }
        .option-label {
            border-radius: 8px;
            transition: all .2s;
            border: 1px solid #dee2e6;
            padding: 0.75rem 1rem;
            font-size: 1rem;
        }
        .option-label:hover {
            background-color: #f8f9fa;
            transform: translateY(-2px);
            box-shadow: 0 3px 8px rgba(0,0,0,.05);
        }
        .form-check-input:checked + .option-label {
            border-color: var(--primary);
            background-color: rgba(114,9,183,.05);
        }
        .btn-primary {
            background-color: var(--primary);
            border-color: var(--primary);
        }
        .btn-outline-primary {
            color: var(--primary);
            border-color: var(--primary);
        }
        .btn-outline-primary:hover {
            background-color: var(--primary);
            color: white;
        }
        /* Bottom fixed navigation for mobile */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: white;
            box-shadow: 0 -2px 10px rgba(0,0,0,.1);
            padding: 10px 15px;
            display: flex;
            justify-content: space-between;
            z-index: 90;
        }
        @media (min-width: 992px) {
            .main-content.with-nav {
                margin-right: 280px;
            }
            .question-nav {
                transform: translateX(0);
            }
            .nav-toggle-btn {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(30px, 1fr));
            }
            .option-label {
                padding: 0.625rem 0.875rem;
                font-size: 0.95rem;
            }
            .question-text {
                font-size: 1rem;
            }
            .navbar-brand {
                font-size: 1rem;
            }
            .timer-display {
                font-size: 0.8125rem;
                padding: 0.2rem 0.6rem;
            }
            .nav-btn {
                padding: 0.2rem 0.6rem;
                font-size: 0.8125rem;
            }
            .container {
                padding-left: 10px;
                padding-right: 10px;
            }
        }
    </style>
</head>
<body>
    <!-- Header navbar -->
    <nav class="navbar navbar-dark sticky-top">
        <div class="container-fluid px-2">
            <a class="navbar-brand fw-bold" href="#"><i class="fas fa-graduation-cap me-1"></i></a>
            <div class="controls-group">
                <!-- Timer -->
                <div class="timer-display me-1 fw-bold" id="timer">
                    <i class="far fa-clock me-1"></i><span id="timer-display">8:00</span>
                </div>
                <!-- Submit button -->
                <button class="btn btn-light btn-sm nav-btn me-1" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Language toggle -->
                <button class="btn btn-light btn-sm nav-btn me-1" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="btn btn-light btn-sm nav-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </nav>
    <!-- Main content -->
    <div class="main-content" id="main-content">
        <div class="container py-3">
            <!-- Question counter -->
            <div class="fw-bold text-center mb-3" id="question-counter">Question 1 of 25</div>
            <!-- Questions will be displayed here -->
            <div id="questions-container"></div>
        </div>
    </div>
    <!-- Bottom navigation for mobile -->
    <div class="bottom-nav d-lg-none">
        <button class="btn btn-outline-primary px-3" onclick="prevQuestion()">
            <i class="fas fa-chevron-left me-1"></i> Previous
        </button>
        <button class="btn btn-outline-primary px-3" onclick="nextQuestion()">
            Next <i class="fas fa-chevron-right ms-1"></i>
        </button>
    </div>
    <!-- Question navigation sidebar -->
    <div class="question-nav" id="question-nav">
        <div class="p-3">
            <div class="d-flex justify-content-between align-items-center mb-3">
                <h5 class="m-0">Questions</h5>
                <div>
                    <span class="badge bg-secondary me-1" id="attempted-count">0</span>
                    <span class="badge bg-secondary" id="total-count">25</span>
                    <button class="btn btn-sm btn-outline-secondary ms-2 d-lg-none" onclick="toggleNav()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
            <!-- Section selector moved here -->
            <div class="mb-3">
                <label for="section-selector" class="form-label small text-muted">Jump to Section</label>
                <select class="form-select form-select-sm" id="section-selector" onchange="switchSection(this.value)">
                    <option value="all">All Sections</option>
                </select>
            </div>
            <div class="question-grid" id="question-boxes"></div>
        </div>
    </div>
    <!-- Results modal -->
    <div class="modal fade" id="results-modal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content border-0">
                <div class="modal-header border-0" style="background:linear-gradient(135deg,var(--primary),var(--secondary));color:white">
                    <h5 class="modal-title">Test Results</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body p-4">
                    <div class="row text-center g-2">
                        <div class="col-6">
                            <div class="p-3 rounded shadow-sm" style="background:linear-gradient(135deg,#4361ee,#3a0ca3);color:white">
                                <h3 id="score-value" class="mb-0 fw-bold">0</h3>
                                <div class="small">Score</div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="p-3 rounded shadow-sm" style="background:linear-gradient(135deg,#06d6a0,#1b9aaa);color:white">
                                <h3 id="correct-value" class="mb-0 fw-bold">0</h3>
                                <div class="small">Correct</div>
                            </div>
                        </div>
                        <div class="col-6 mt-2">
                            <div class="p-3 rounded shadow-sm" style="background:linear-gradient(135deg,#ef476f,#f72585);color:white">
                                <h3 id="incorrect-value" class="mb-0 fw-bold">0</h3>
                                <div class="small">Incorrect</div>
                            </div>
                        </div>
                        <div class="col-6 mt-2">
                            <div class="p-3 rounded shadow-sm" style="background:linear-gradient(135deg,#6c757d,#495057);color:white">
                                <h3 id="unattempted-value" class="mb-0 fw-bold">0</h3>
                                <div class="small">Unattempted</div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer border-0">
                    <button type="button" class="btn btn-primary" onclick="reviewTest()">Review Test</button>
                </div>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 8 * 60;
        let resultsModal;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            resultsModal = new bootstrap.Modal(document.getElementById('results-modal'));
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["7"] = {
                name: "English",
                start: 0,
                end: 23
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="7">English</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 24,
                end: 24
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "7",
                    question_en: "1. The following sentence has been divided into parts. One of them contains an error. Select the part that contains the error from the given options. <br />Mr. Rao asked the newcomer / to his office / if he will minded / working late that day. ",
                    question_hi: "1. The following sentence has been divided into parts. One of them contains an error. Select the part that contains the error from the given options. <br />Mr. Rao asked the newcomer / to his office / if he will minded / working late that day. ",
                    options_en: [" Mr. Rao asked the newcomer ", " working late that day ", 
                                " to his office ", " if he will minded "],
                    options_hi: [" Mr. Rao asked the newcomer ", " working late that day ",
                                " to his office ", " if he will minded "],
                    solution_en: "1.(d) if he will minded.<br />The given sentence is in the past tense(asked) so the verb must be used in its simple past form(would) and not in the future tense(will). Hence, ‘if he would mind’ is the most appropriate answer.",
                    solution_hi: "1.(d) if he will minded.<br />दिया गया sentence, past tense(asked) में है, इसलिए verb का प्रयोग भी simple past form(would) में होना चाहिए, न कि future tense(will) में। अतः, ‘if he would mind’ सबसे उपयुक्त उत्तर है।",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "7",
                    question_en: "2. Select the most appropriate option to fill in the blank. <br />If you ______ to newsgroups, you often get hundreds of messages. ",
                    question_hi: "2. Select the most appropriate option to fill in the blank. <br />If you ______ to newsgroups, you often get hundreds of messages. ",
                    options_en: [" offend ", " subscribe ", 
                                " describe ", " practice "],
                    options_hi: [" offend ", " subscribe ",
                                " describe ", " practice "],
                    solution_en: "2.(b) subscribe.<br />Subscribe means to pay for a newspaper or magazine to be sent to you regularly. The given sentence states that if you pay(subscribe to) for newsgroups, you often get hundreds of messages. Hence, ‘subscribe’ is the most appropriate answer.",
                    solution_hi: "2.(b) subscribe.<br />‘Subscribe’ का अर्थ है नियमित रूप से भेजे जाने के लिए किसी newspaper या magazine के लिए भुगतान करना। दिए गए sentence में कहा गया है कि यदि आप newsgroups के लिए भुगतान करते हैं,(सदस्यता लेते हैं), तो आपको अक्सर सैकड़ों messages प्राप्त होते हैं। अतः, ‘subscribe’ सबसे उपयुक्त उत्तर है।",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "7",
                    question_en: "<p>3. Select the option that can be used as a one-word substitute for the given group of words. <br>A secret or disguised way of writing</p>",
                    question_hi: "<p>3. Select the option that can be used as a one-word substitute for the given group of words. <br>A secret or disguised way of writing</p>",
                    options_en: ["<p>Emblem</p>", "<p>Cypher</p>", 
                                "<p>Symbol</p>", "<p>Puzzle</p>"],
                    options_hi: ["<p>Emblem</p>", "<p>Cypher</p>",
                                "<p>Symbol</p>", "<p>Puzzle</p>"],
                    solution_en: "<p>3.(b) <br><strong>Cypher </strong>- a secret or disguised way of writing <br><strong>Emblem </strong>- an object or symbol that represents something<br><strong>Symbol </strong>- a sign, object, etc. which represents something<br><strong>Puzzle </strong>- something that is difficult to understand or explain</p>",
                    solution_hi: "<p>3.(b) <br><strong>Cypher </strong>(कूटलिपि/गुप्त संकेत) - a secret or disguised way of writing <br><strong>Emblem </strong>(प्रतीक/चिह्न) - an object or symbol that represents something<br><strong>Symbol </strong>(प्रतीक/चिह्न) - a sign, object, etc. which represents something<br><strong>Puzzle </strong>(पहेली) - something that is difficult to understand or explain</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "7",
                    question_en: "<p>4. Select the most appropriate ANTONYM of the given word. <br>Expunge</p>",
                    question_hi: "<p>4. Select the most appropriate ANTONYM of the given word. <br>Expunge</p>",
                    options_en: ["<p>Draw</p>", "<p>Omit</p>", 
                                "<p>Wipe</p>", "<p>Add</p>"],
                    options_hi: ["<p>Draw</p>", "<p>Omit</p>",
                                "<p>Wipe</p>", "<p>Add</p>"],
                    solution_en: "<p>4.(d) <br><strong>Add </strong>- put in (an additional element, ingredient, etc.)<br><strong>Expunge </strong>- obliterate or remove completely <br><strong>Draw </strong>- to do a picture or diagram of something with a pencil, pen, etc<br><strong>Omit </strong>- to not include something <br><strong>Wipe </strong>- to clean or dry something by rubbing it with a cloth</p>",
                    solution_hi: "<p>4.(d) <br><strong>Add </strong>(जोड़ना) - put in (an additional element, ingredient, etc.)<br><strong>Expunge </strong>(मिटाना) - obliterate or remove completely <br><strong>Draw </strong>(चित्र बनाना) - to do a picture or diagram of something with a pencil, pen, etc<br><strong>Omit </strong>(छोड़ देना) - to not include something <br><strong>Wipe </strong>(पोंछना) - to clean or dry something by rubbing it with a cloth</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "7",
                    question_en: "<p>5. Select the option that expresses the given sentence in indirect speech. <br>&ldquo;Alas, I have broken my brother&rsquo;s watch!&rdquo; said he.</p>",
                    question_hi: "<p>5. Select the option that expresses the given sentence in indirect speech. <br>&ldquo;Alas, I have broken my brother&rsquo;s watch!&rdquo; said he.</p>",
                    options_en: ["<p>He exclaimed with sorrow that he had broken his brother&rsquo;s watch.</p>", "<p>He exclaimed with sorrow that he has broken his brother&rsquo;s watch.</p>", 
                                "<p>He exclaimed with sorrow I have broken my brother&rsquo;s watch.</p>", "<p>He exclaimed with sorrow that he may have broken his brother&rsquo;s watch.</p>"],
                    options_hi: ["<p>He exclaimed with sorrow that he had broken his brother&rsquo;s watch.</p>", "<p>He exclaimed with sorrow that he has broken his brother&rsquo;s watch.</p>",
                                "<p>He exclaimed with sorrow I have broken my brother&rsquo;s watch.</p>", "<p>He exclaimed with sorrow that he may have broken his brother&rsquo;s watch.</p>"],
                    solution_en: "<p>5.(a) He exclaimed with sorrow that <strong>he had broken his</strong> brother&rsquo;s watch. (Correct)<br>(b) He exclaimed with sorrow that he <span style=\"text-decoration: underline;\">has broken</span> his brother&rsquo;s watch. (Incorrect Tense)<br>(c) He exclaimed with sorrow <span style=\"text-decoration: underline;\">I have broken my</span> brother&rsquo;s watch. (Incorrect Tense)<br>(d) He exclaimed with sorrow that he <span style=\"text-decoration: underline;\">may have broken</span> his brother&rsquo;s watch. (Incorrect Tense)</p>",
                    solution_hi: "<p>5.(a) He exclaimed with sorrow that <strong>he had broken his </strong>brother&rsquo;s watch. (Correct)<br>(b) He exclaimed with sorrow that he <span style=\"text-decoration: underline;\">has broken</span> his brother&rsquo;s watch. (गलत Tense)<br>(c) He exclaimed with sorrow <span style=\"text-decoration: underline;\">I have broken my</span> brother&rsquo;s watch. (गलत Tense)<br>(d) He exclaimed with sorrow that he <span style=\"text-decoration: underline;\">may have broken</span> his brother&rsquo;s watch. (गलत Tense)</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "7",
                    question_en: "6. Select the most appropriate option to fill in the blank. <br />Can you ______ between these two shades of green? ",
                    question_hi: "6. Select the most appropriate option to fill in the blank. <br />Can you ______ between these two shades of green? ",
                    options_en: [" detect ", " distinguish ", 
                                " distract ", " divert "],
                    options_hi: [" detect ", " distinguish ",
                                " distract ", " divert "],
                    solution_en: "6.(b) distinguish. <br />Distinguish means to recognize the difference between two things or people. The given sentence has a question: can you recognize the difference between(distinguish) these two shades of green. Hence, ‘distinguish’ is the most appropriate answer.",
                    solution_hi: "6.(b) distinguish. <br />‘Distinguish’ का अर्थ है दो वस्तुओं या व्यक्तियों के बीच अंतर पहचानना। दिए गए sentence में एक question है: क्या आप हरे रंग के इन दो shades के बीच अंतर (distinguish) पहचान सकते हैं। अतः, ‘distinguish’ सबसे उपयुक्त उत्तर है।",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "7",
                    question_en: "<p>7. Select the most appropriate synonym of the given word. <br>Bang</p>",
                    question_hi: "<p>7. Select the most appropriate synonym of the given word. <br>Bang</p>",
                    options_en: ["<p>Beat</p>", "<p>Sang</p>", 
                                "<p>Ride</p>", "<p>Rang</p>"],
                    options_hi: ["<p>Beat</p>", "<p>Sang</p>",
                                "<p>Ride</p>", "<p>Rang</p>"],
                    solution_en: "<p>7.(a) <br><strong>Beat </strong>- strike or hit (a person or an animal) repeatedly and violently so as to hurt or injure them<br><strong>Bang</strong> - to hit or strike something hard noisily<br><strong>Sang </strong>- to make musical sounds with your voice<br><strong>Ride </strong>- a short journey on a horse or bicycle, or in a car, bus, etc.<br><strong>Rang </strong>- make a clear resonant or vibrating sound.</p>",
                    solution_hi: "<p>7.(a)<br><strong>Beat </strong>(पीटना) - strike or hit (a person or an animal) repeatedly and violently so as to hurt or injure them<br><strong>Bang </strong>(ठोंकना/जोर से मारना) - to hit or strike something hard noisily<br><strong>Sang </strong>(गाया/गायी) - to make musical sounds with your voice<br><strong>Ride </strong>(सवारी करना) - a short journey on a horse or bicycle, or in a car, bus, etc.<br><strong>Rang </strong>(बजा/बजाना) - make a clear resonant or vibrating sound.</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "7",
                    question_en: "<p>8. Select the most appropriate option to substitute the underlined segment in the given sentence. If no substitution is required, select &lsquo;No substitution&rsquo;. <br>The warden punished the students <span style=\"text-decoration: underline;\"><strong>who violates</strong></span> the rules of the hostel.</p>",
                    question_hi: "<p>8. Select the most appropriate option to substitute the underlined segment in the given sentence. If no substitution is required, select &lsquo;No substitution&rsquo;. <br>The warden punished the students <span style=\"text-decoration: underline;\"><strong>who violates</strong></span> the rules of the hostel.</p>",
                    options_en: ["<p>who is violating</p>", "<p>who violated</p>", 
                                "<p>No substitution</p>", "<p>who are violate</p>"],
                    options_hi: ["<p>who is violating</p>", "<p>who violated</p>",
                                "<p>No substitution</p>", "<p>who are violate</p>"],
                    solution_en: "<p>8.(b) who violated.<br>The given sentence is in the past tense(punished) so the verb must be used in its simple past form(violated) and not in the base/present form(violates). Hence, &lsquo;who violated&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>8.(b) who violated.<br>दिया गया sentence, past tense(punished) में है, इसलिए verb का प्रयोग भी simple past form(violated) में होना चाहिए, न कि base/present form(violates) में। अतः, &lsquo;who violated&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "7",
                    question_en: "<p>9. Select the most appropriate option to substitute the underlined segment in the given sentence. If no substitution is required, select &lsquo;No substitution&rsquo;. <br>The minister, along with his team members, <span style=\"text-decoration: underline;\"><strong>is expected to arrive</strong></span> shortly.</p>",
                    question_hi: "<p>9. Select the most appropriate option to substitute the underlined segment in the given sentence. If no substitution is required, select &lsquo;No substitution&rsquo;. <br>The minister, along with his team members, <span style=\"text-decoration: underline;\"><strong>is expected to arrive</strong></span> shortly.</p>",
                    options_en: ["<p>are expect to arrive</p>", "<p>are being expected to arrive</p>", 
                                "<p>are expected to arrive</p>", "<p>No substitution</p>"],
                    options_hi: ["<p>are expect to arrive</p>", "<p>are being expected to arrive</p>",
                                "<p>are expected to arrive</p>", "<p>No substitution</p>"],
                    solution_en: "<p>9.(d) No substitution. <br>The sentence is grammatically correct.</p>",
                    solution_hi: "<p>9.(d) No substitution. <br>दिया गया sentence, grammatically सही है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "7",
                    question_en: "10. Sentences of a paragraph are given below in jumbled order. Arrange the sentences in the right order to form a meaningful and coherent paragraph. <br />A. A fox, hearing the cock and thinking to make a meal of him, came and stood under the tree. <br />B. As the night passed away and the day dawned, the cock, according to his custom, set up a shrill crowing. <br />C. A dog and a cock having struck up an acquaintance, went out on their travels together. <br />D. Nightfall found them in a forest, so the cock, flying up on a tree, perched among the branches, while the dog dozed below at the foot. ",
                    question_hi: "10. Sentences of a paragraph are given below in jumbled order. Arrange the sentences in the right order to form a meaningful and coherent paragraph. <br />A. A fox, hearing the cock and thinking to make a meal of him, came and stood under the tree. <br />B. As the night passed away and the day dawned, the cock, according to his custom, set up a shrill crowing. <br />C. A dog and a cock having struck up an acquaintance, went out on their travels together. <br />D. Nightfall found them in a forest, so the cock, flying up on a tree, perched among the branches, while the dog dozed below at the foot. ",
                    options_en: [" DCAB ", " CDBA ", 
                                " BCDA ", " CABD "],
                    options_hi: [" DCAB ", " CDBA ",
                                " BCDA ", " CABD "],
                    solution_en: "10.(b) CDBA.<br />Sentence C will be the starting line as it contains the main idea of the parajumble i.e. a dog and a cock went out on their travels together. However, Sentence D states that nightfall found them in a forest, so the cock, flying up on a tree, perched among the branches, while the dog dozed below at the foot. So, D will follow C. Further, Sentence B states that as the night passed away and the day dawned, the cock, set up a shrill crowing & Sentence A states that a fox, heard the cock and thought to make a meal of him, came and stood under the tree. So, A will follow B. Going through the options, option (b) has the correct sequence. ",
                    solution_hi: "10.(b) CDBA.<br />Sentence C प्रारंभिक line होगी, क्योंकि इसमें parajumble का main idea ‘a dog and a cock went out on their travels together’ शामिल है। हालाँकि, sentence D बताता है कि रात होने पर वे एक जंगल (forest) में थे, इसलिए मुर्गा (cock) उड़कर एक पेड़ पर चढ़ गया और शाखाओं (branches) के बीच बैठ गया, जबकि कुत्ता (dog) नीचे जड़ के पास ऊँघता रहा (dozed)। इसलिए, D, C के बाद आएगा। इसके अलावा, sentence B बताता है कि जैसे ही रात बीती और दिन निकला, मुर्गे ने तीखी बांग (shrill crowing) लगाई तथा sentence A बताता है कि एक लोमड़ी (fox) ने मुर्गे को सुना और उसे अपना भोजन बनाने की सोचकर, पेड़ के नीचे आकर खड़ी हो गई। इसलिए, A, B के बाद आएगा। Options के माध्यम से जाने पर, option (b) में सही sequence है।",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "7",
                    question_en: "11. Select the INCORRECTLY spelt word. ",
                    question_hi: "11. Select the INCORRECTLY spelt word. ",
                    options_en: [" Preshious ", " Barricade ", 
                                " Innovation ", " Infection "],
                    options_hi: [" Preshious ", " Barricade ",
                                " Innovation ", " Infection "],
                    solution_en: "11.(a) Preshious.<br />‘Precious’ is the correct spelling.   ",
                    solution_hi: "11.(a) Preshious.<br />‘Precious’ सही spelling है।   ",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "7",
                    question_en: "12. Select the most appropriate meaning of the following idiom. <br />A fish out of water ",
                    question_hi: "12. Select the most appropriate meaning of the following idiom. <br />A fish out of water ",
                    options_en: [" Feel as if you are unable to breathe ", " Feel like going to an unfamiliar and new place ", 
                                " Feel comfortable in any kind of place ", " Feel uncomfortable in unfamiliar surroundings "],
                    options_hi: [" Feel as if you are unable to breathe ", " Feel like going to an unfamiliar and new place ",
                                " Feel comfortable in any kind of place ", " Feel uncomfortable in unfamiliar surroundings "],
                    solution_en: "12.(d) A fish out of water- feel uncomfortable in unfamiliar surroundings. <br />Example- When Raj shifted to the USA from a small village in India, he was like a fish out of water. ",
                    solution_hi: "12.(d) A fish out of water- feel uncomfortable in unfamiliar surroundings./अपरिचित परिवेश में असहज महसूस करना। <br />Example- When Raj shifted to the USA from a small village in India, he was like a fish out of water. ",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "7",
                    question_en: "<p>13. Select the option that can be used as a one-word substitute for the given group of words. <br>A person who entertains people by doing difficult feats</p>",
                    question_hi: "<p>13. Select the option that can be used as a one-word substitute for the given group of words. <br>A person who entertains people by doing difficult feats</p>",
                    options_en: ["<p>Acrobat</p>", "<p>Cosmonaut</p>", 
                                "<p>Astronaut</p>", "<p>Performer comedian</p>"],
                    options_hi: ["<p>Acrobat</p>", "<p>Cosmonaut</p>",
                                "<p>Astronaut</p>", "<p>Performer comedian</p>"],
                    solution_en: "<p>13.(a) <br><strong>Acrobat </strong>- a person who entertains people by doing difficult feats (gymnastic stunts) <br><strong>Cosmonaut </strong>- a Russian astronaut ; Astronaut<br><strong>Astronaut </strong>- a person who travels in a spacecraft<br><strong>Performer comedian</strong> - a professional entertainer who amuses by relating anecdotes, acting out comical situations</p>",
                    solution_hi: "<p>13.(a) <br><strong>Acrobat </strong>(कलाबाज) - a person who entertains people by doing difficult feats (gymnastic stunts) <br><strong>Cosmonaut </strong>(रूसी अंतरिक्ष यात्री) - a Russian astronaut ; Astronaut<br><strong>Astronaut </strong>(अंतरिक्ष यात्री) - a person who travels in a spacecraft<br><strong>Performer comedian </strong>(हास्य कलाकार) - a professional entertainer who amuses by relating anecdotes, acting out comical situations</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "7",
                    question_en: "14. Select the most appropriate meaning of the following idiom. <br />To be light years away ",
                    question_hi: "14. Select the most appropriate meaning of the following idiom. <br />To be light years away ",
                    options_en: [" An extremely long time from now in the past or future ", " To be too distant for human beings to reach ", 
                                " An unlikely event that happened suddenly ", " When something seems like it is about to happen in the near future "],
                    options_hi: [" An extremely long time from now in the past or future ", " To be too distant for human beings to reach ",
                                " An unlikely event that happened suddenly ", " When something seems like it is about to happen in the near future "],
                    solution_en: "14.(b) To be light years away - to be too distant for human beings to reach. <br />Example- The nearest star to our sun, Proxima Centauri, is 4.2 light years away.",
                    solution_hi: "14.(b) To be light years away- to be too distant for human beings to reach./इंसानों की पहुंच से परे।  <br />Example- The nearest star to our sun, Proxima Centauri, is 4.2 light years away.",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "7",
                    question_en: "15. Sentences of a paragraph are given below in jumbled order. Arrange the sentences in the right order to form a meaningful and coherent paragraph. <br />A. Though they had never ever heard Subha utter their names, they unfailingly recognized her footsteps as she came into their shed.  <br />B. They even understood the precious moments between them as she loved and cuddled them, scolded them, and pleaded to them. <br />C. Subha did really have a close circle of friends and companions; they were Sarbasi and Panguli, the two cows living in their cow-shed. <br />D. They understood the silent, melancholy tune of her unspoken words during those moments, and the intensity of her expressions more easily than the spoken language of other humans. ",
                    question_hi: "15. Sentences of a paragraph are given below in jumbled order. Arrange the sentences in the right order to form a meaningful and coherent paragraph. <br />A. Though they had never ever heard Subha utter their names, they unfailingly recognized her footsteps as she came into their shed.  <br />B. They even understood the precious moments between them as she loved and cuddled them, scolded them, and pleaded to them. <br />C. Subha did really have a close circle of friends and companions; they were Sarbasi and Panguli, the two cows living in their cow-shed. <br />D. They understood the silent, melancholy tune of her unspoken words during those moments, and the intensity of her expressions more easily than the spoken language of other humans. ",
                    options_en: [" ADBC ", " DABC ", 
                                " CADB ", " CBDA "],
                    options_hi: [" ADBC ", " DABC ",
                                " CADB ", " CBDA "],
                    solution_en: "15.(c) CADB.<br />Sentence C will be the starting line as it contains the main idea of the parajumble i.e. a close circle of friends of Subha. However, Sentence A states that though her friends(the two cows) had never ever heard Subha utter their names, they unfailingly recognized her footsteps. So, A will follow C. Further, Sentence D states that her friends understood the silent, melancholy tune of her unspoken words during those moments & Sentence B states that they even understood the precious moments between them as she loved and cuddled them, scolded them, and pleaded to them. So, B will follow D. Going through the options, option (c) has the correct sequence. ",
                    solution_hi: "15.(c) CADB.<br />Sentence C प्रारंभिक line होगी, क्योंकि इसमें parajumble का main idea ‘a close circle of friends of Subha’ शामिल है। हालाँकि, sentence A बताता है कि उसके दोस्तों(दो गायों) ने Subha को कभी उनका नाम लेते हुए नहीं सुना था, वे अचूक रूप (unfailingly) से उसके कदमों की आहाट पहचान लेते थे। इसलिए, A, C के बाद आएगा। इसके अलावा, sentence D बताता है कि उसके दोस्त उन क्षणों (moments) के दौरान उसके अनकहे शब्दों (unspoken words) की खामोश, उदास धुन (melancholy tune) को समझते थे, तथा sentence B बताता है कि उनके बीच के उन अनमोल क्षणों (precious moments) को भी समझते थे, जब वह उन्हे स्नेह (love) करती थी और गले लगाती (cuddled) थी, उन्हें डांटती (scolded) थी तथा उनसे विनती करती थी।  इसलिए, B, D के बाद आएगा। Options के माध्यम से जाने पर, option (c) में सही sequence है।",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "16",
                    section: "7",
                    question_en: "<p>16. Select the most appropriate synonym of the given word. <br>Ponder</p>",
                    question_hi: "<p>16. Select the most appropriate synonym of the given word. <br>Ponder</p>",
                    options_en: ["<p>Terminate</p>", "<p>Meditate</p>", 
                                "<p>Celebrate</p>", "<p>Agitate</p>"],
                    options_hi: ["<p>Terminate</p>", "<p>Meditate</p>",
                                "<p>Celebrate</p>", "<p>Agitate</p>"],
                    solution_en: "<p>16.(b) <br><strong>Meditate </strong>- to make your mind calm; think deeply about something<br><strong>Ponder </strong>- to think about something carefully or for a long time <br><strong>Terminate </strong>- to end or to make something end<br><strong>Celebrate </strong>- to do something to show that you are happy about something that has happened<br><strong>Agitate </strong>- to disturb, excite, or anger someone</p>",
                    solution_hi: "<p>16.(b) <br><strong>Meditate </strong>(ध्यान करना) - to make your mind calm; think deeply about something<br><strong>Ponder </strong>(विचार करना) - to think about something carefully or for a long time <br><strong>Terminate </strong>(समाप्त करना) - to end or to make something end<br><strong>Celebrate </strong>(उत्सव मनाना) - to do something to show that you are happy about something that has happened<br><strong>Agitate </strong>(उत्तेजित करना) - to disturb, excite, or anger someone</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "17",
                    section: "7",
                    question_en: "<p>17. Select the most appropriate ANTONYM of the given word. <br>Obese</p>",
                    question_hi: "<p>17 Select the most appropriate ANTONYM of the given word. <br>Obese</p>",
                    options_en: ["<p>Greasy</p>", "<p>Slim</p>", 
                                "<p>Large</p>", "<p>Stout</p>"],
                    options_hi: ["<p>Greasy</p>", "<p>Slim</p>",
                                "<p>Large</p>", "<p>Stout</p>"],
                    solution_en: "<p>17.(b) <br><strong>Slim </strong>- to become thinner and lighter<br><strong>Obese </strong>- very fat<br><strong>Greasy </strong>- containing a lot of grease<br><strong>Large </strong>- greater in size, amount, etc. than usual<br><strong>Stout </strong>- strong and thick</p>",
                    solution_hi: "<p>17.(b) <br><strong>Slim </strong>(पतला) - to become thinner and lighter<br><strong>Obese </strong>(मोटा) - very fat<br><strong>Greasy </strong>(चिकना ) - containing a lot of grease<br><strong>Large </strong>(बड़ा) - greater in size, amount, etc. than usual<br><strong>Stout </strong>(हट्टा-कट्टा) - strong and thick</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "18",
                    section: "7",
                    question_en: "<p>18. Select the INCORRECTLY spelt word.</p>",
                    question_hi: "<p>18. Select the INCORRECTLY spelt word.</p>",
                    options_en: ["<p>Detective</p>", "<p>Evidence</p>", 
                                "<p>Priority</p>", "<p>Vacine</p>"],
                    options_hi: ["<p>Detective</p>", "<p>Evidence</p>",
                                "<p>Priority</p>", "<p>Vacine</p>"],
                    solution_en: "<p>18.(d) Vacine. <br>&lsquo;Vaccine&rsquo; is the correct spelling</p>",
                    solution_hi: "<p>18.(d) Vacine. <br>&lsquo;Vaccine&rsquo; सही spelling है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "19",
                    section: "7",
                    question_en: "<p>19. Select the option that expresses the given sentence in passive voice. <br>The spider on the wall frightened Jiya.</p>",
                    question_hi: "<p>19. Select the option that expresses the given sentence in passive voice. <br>The spider on the wall frightened Jiya.</p>",
                    options_en: ["<p>The spider on the wall was frightened by Jiya.</p>", "<p>Jiya was frightened by the spider on the wall.</p>", 
                                "<p>Jiya has been frightened by the spider on the wall.</p>", "<p>Jiya had been frightened of the spider on the wall.</p>"],
                    options_hi: ["<p>The spider on the wall was frightened by Jiya.</p>", "<p>Jiya was frightened by the spider on the wall.</p>",
                                "<p>Jiya has been frightened by the spider on the wall.</p>", "<p>Jiya had been frightened of the spider on the wall.</p>"],
                    solution_en: "<p>19.(b) Jiya <strong>was frightened</strong> by the spider on the wall. (Correct)<br>(a) <span style=\"text-decoration: underline;\">The spider</span> on the wall was frightened by <span style=\"text-decoration: underline;\">Jiya</span>. (Incorrect sentence structure)<br>(c) Jiya <span style=\"text-decoration: underline;\">has been</span> frightened by the spider on the wall. (Incorrect Tense)<br>(d) Jiya <span style=\"text-decoration: underline;\">had been</span> frightened of the spider on the wall. (Incorrect Tense)</p>",
                    solution_hi: "<p>19.(b) Jiya <strong>was frightened </strong>by the spider on the wall. (Correct)<br>(a) <span style=\"text-decoration: underline;\">The spider</span> on the wall was frightened by <span style=\"text-decoration: underline;\">Jiya</span>. (गलत sentence structure)<br>(c) Jiya <span style=\"text-decoration: underline;\">has been</span> frightened by the spider on the wall. (गलत Tense)<br>(d) Jiya <span style=\"text-decoration: underline;\">had been</span> frightened of the spider on the wall. (गलत Tense)</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "20",
                    section: "7",
                    question_en: "20. The following sentence has been divided into parts. One of them contains an error. Select the part that contains the error from the given options. <br />The concept became / too much clear to me / after my tutor / showed me the diagram. ",
                    question_hi: "20. The following sentence has been divided into parts. One of them contains an error. Select the part that contains the error from the given options. <br />The concept became / too much clear to me / after my tutor / showed me the diagram. ",
                    options_en: [" too much clear to me ", " showed me the diagram ", 
                                " The concept became ", " after my tutor "],
                    options_hi: [" too much clear to me ", " showed me the diagram ",
                                " The concept became ", " after my tutor "],
                    solution_en: "20.(a) too much clear to me.<br />The use of the phrase ‘too much’ is not necessary for the sentence because it is used before uncountable nouns like too much money. ‘Clear’ is not a noun. Hence, ‘became clear to me’ is the most appropriate answer.",
                    solution_hi: "20.(a) too much clear to me.<br />इस sentence में phrase ‘too much’ का प्रयोग अनावश्यक है, क्योंकि इसका प्रयोग uncountable nouns से पहले किया जाता है, जैसे ‘too much money’. ‘Clear’ एक noun नहीं है। अतः, ‘became clearer to me’  सबसे उपयुक्त उत्तर है।",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "21",
                    section: "7",
                    question_en: "<p>21. <strong>Cloze Test:</strong><br>Essaouira\'s origins stretch back to prehistoric times, and this spot on Morocco\'s Atlantic coast (21)______ a centre of culture and commerce ever since. (22)______ centuries, the harbour town was an important trading post (23)______ both a seaport and as the north-western terminus of a caravan (24)______ stretching across the desert to Timbuktu (25)______ Africa&rsquo;s interior. <br>Select the most appropriate option to fill in blank number 21.</p>",
                    question_hi: "<p>21. <strong>Cloze Test:</strong><br>Essaouira\'s origins stretch back to prehistoric times, and this spot on Morocco\'s Atlantic coast (21)______ a centre of culture and commerce ever since. (22)______ centuries, the harbour town was an important trading post (23)______ both a seaport and as the north-western terminus of a caravan (24)______ stretching across the desert to Timbuktu (25)______ Africa&rsquo;s interior.<br>Select the most appropriate option to fill in blank number 21.</p>",
                    options_en: ["<p>have been</p>", "<p>been</p>", 
                                "<p>had be</p>", "<p>has been</p>"],
                    options_hi: ["<p>have been</p>", "<p>been</p>",
                                "<p>had be</p>", "<p>has been</p>"],
                    solution_en: "<p>21.(d) has been.<br>The phrase &lsquo;ever since&rsquo; is used to describe an action or situation that began in the past and continues in the present &amp; for that type of situation we use &lsquo;has/have been&rsquo;. However, &lsquo;the spot&rsquo; is a singular subject so it will take &lsquo;has&rsquo; as a singular verb. Hence, &lsquo;has been&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>21.(d) has been.<br>Phrase &lsquo;ever since&rsquo; का प्रयोग किसी ऐसे action या situation का describe करने के लिए किया जाता है जो past में हुई तथा present में जारी है, और ऐसी situation के लिए हम &lsquo;has/have been&rsquo; का प्रयोग करते हैं। हालाँकि, &lsquo;the spot&rsquo; singular subject है इसलिए यहाँ पर &lsquo;has&rsquo; singular verb का प्रयोग होगा। अतः, &lsquo;has been&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "22",
                    section: "7",
                    question_en: "<p>22. <strong>Cloze Test:</strong><br>Essaouira\'s origins stretch back to prehistoric times, and this spot on Morocco\'s Atlantic coast (21)______ a centre of culture and commerce ever since. (22)______ centuries, the harbour town was an important trading post (23)______ both a seaport and as the north-western terminus of a caravan (24)______ stretching across the desert to Timbuktu (25)______ Africa&rsquo;s interior.<br>Select the most appropriate option to fill in blank number 22.</p>",
                    question_hi: "<p>22.<strong>Cloze Test:</strong><br>Essaouira\'s origins stretch back to prehistoric times, and this spot on Morocco\'s Atlantic coast (21)______ a centre of culture and commerce ever since. (22)______ centuries, the harbour town was an important trading post (23)______ both a seaport and as the north-western terminus of a caravan (24)______ stretching across the desert to Timbuktu (25)______ Africa&rsquo;s interior.<br>Select the most appropriate option to fill in blank number 22.</p>",
                    options_en: ["<p>For</p>", "<p>At</p>", 
                                "<p>Above</p>", "<p>In</p>"],
                    options_hi: ["<p>For</p>", "<p>At</p>",
                                "<p>Above</p>", "<p>In</p>"],
                    solution_en: "<p>22.(a) For. <br>&lsquo;For&rsquo; refers to a specific period of time or a numerical value of time, for example for 2 years, for decades, for centuries, etc. Hence, &lsquo;for&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>22.(a) For. <br>&lsquo;For&rsquo; का प्रयोग &lsquo;समय की एक निश्चित अवधि&rsquo; या &lsquo;समय के संख्यात्मक मान&rsquo; को दर्शाने के लिए किया जाता है, उदाहरण के लिए for 2 years, for decades, for centuries, आदि। अतः, &lsquo;for&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "23",
                    section: "7",
                    question_en: "<p>23. <strong>Cloze Test:</strong><br>Essaouira\'s origins stretch back to prehistoric times, and this spot on Morocco\'s Atlantic coast (21)______ a centre of culture and commerce ever since. (22)______ centuries, the harbour town was an important trading post (23)______ both a seaport and as the north-western terminus of a caravan (24)______ stretching across the desert to Timbuktu (25)______ Africa&rsquo;s interior.<br>Select the most appropriate option to fill in blank number 23.</p>",
                    question_hi: "<p>23. <strong>Cloze Test:</strong><br>Essaouira\'s origins stretch back to prehistoric times, and this spot on Morocco\'s Atlantic coast (21)______ a centre of culture and commerce ever since. (22)______ centuries, the harbour town was an important trading post (23)______ both a seaport and as the north-western terminus of a caravan (24)______ stretching across the desert to Timbuktu (25)______ Africa&rsquo;s interior.<br>Select the most appropriate option to fill in blank number 23.</p>",
                    options_en: ["<p>from</p>", "<p>at</p>", 
                                "<p>as</p>", "<p>by</p>"],
                    options_hi: ["<p>from</p>", "<p>at</p>",
                                "<p>as</p>", "<p>by</p>"],
                    solution_en: "<p>23.(c) as. <br>&lsquo;As&rsquo; is used for talking about somebody/something&rsquo;s job, role or function. The given passage states that the harbour town was an important trading post as a seaport. Hence, &lsquo;as&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>23.(c) as. <br>&lsquo;As&rsquo; का प्रयोग किसी व्यक्ति/वस्तु के job, role या function के बारे में बात करने के लिए किया जाता है। दिए गए passage में कहा गया है कि हार्बर टाउन एक समुद्री बंदरगाह (seaport) के रूप में एक महत्वपूर्ण व्यापारिक चौकी (trading post) था। अतः, &lsquo;as&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "24",
                    section: "7",
                    question_en: "<p>24. <strong>Cloze Test:</strong><br>Essaouira\'s origins stretch back to prehistoric times, and this spot on Morocco\'s Atlantic coast (21)______ a centre of culture and commerce ever since. (22)______ centuries, the harbour town was an important trading post (23)______ both a seaport and as the north-western terminus of a caravan (24)______ stretching across the desert to Timbuktu (25)______ Africa&rsquo;s interior.<br>Select the most appropriate option to fill in blank number 24.</p>",
                    question_hi: "<p>24. <strong>Cloze Test:</strong><br>Essaouira\'s origins stretch back to prehistoric times, and this spot on Morocco\'s Atlantic coast (21)______ a centre of culture and commerce ever since. (22)______ centuries, the harbour town was an important trading post (23)______ both a seaport and as the north-western terminus of a caravan (24)______ stretching across the desert to Timbuktu (25)______ Africa&rsquo;s interior.<br>Select the most appropriate option to fill in blank number 24.</p>",
                    options_en: ["<p>highway</p>", "<p>direction</p>", 
                                "<p>route</p>", "<p>way</p>"],
                    options_hi: ["<p>highway</p>", "<p>direction</p>",
                                "<p>route</p>", "<p>way</p>"],
                    solution_en: "<p>24.(c) route. <br>&lsquo;Route&rsquo; means a particular way from one place to another. The given passage states that the harbour town is the north-western terminus of a caravan route stretching across the desert to Timbuktu. Hence, &lsquo;route&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>24.(c) route .<br>&lsquo;Route&rsquo; का अर्थ है एक स्थान से दूसरे स्थान तक जाने का एक विशेष मार्ग। दिए गए passage में कहा गया है कि हार्बर टाउन रेगिस्तान में Timbuktu तक फैले हुए एक कारवाँ मार्ग (caravan route) का उत्तर-पश्चिमी अंतिम छोर (terminus) है। अतः, &lsquo;route&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "25",
                    section: "misc",
                    question_en: "<p>25. <strong>Cloze Test:</strong><br>Essaouira\'s origins stretch back to prehistoric times, and this spot on Morocco\'s Atlantic coast (21)______ a centre of culture and commerce ever since. (22)______ centuries, the harbour town was an important trading post (23)______ both a seaport and as the north-western terminus of a caravan (24)______ stretching across the desert to Timbuktu (25)______ Africa&rsquo;s interior.<br>Select the most appropriate option to fill in blank number 25.</p>",
                    question_hi: "<p>25.<strong>Cloze Test:</strong><br>Essaouira\'s origins stretch back to prehistoric times, and this spot on Morocco\'s Atlantic coast (21)______ a centre of culture and commerce ever since. (22)______ centuries, the harbour town was an important trading post (23)______ both a seaport and as the north-western terminus of a caravan (24)______ stretching across the desert to Timbuktu (25)______ Africa&rsquo;s interior.<br>Select the most appropriate option to fill in blank number 25.</p>",
                    options_en: ["<p>above</p>", "<p>on</p>", 
                                "<p>in</p>", "<p>over</p>"],
                    options_hi: ["<p>above</p>", "<p>on</p>",
                                "<p>in</p>", "<p>over</p>"],
                    solution_en: "<p>25.(c) in. <br>The given passage states that the caravan route stretches across the desert to Timbuktu in Africa&rsquo;s interior. Hence, &lsquo;in&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>25.(c) in. <br>दिए गए passage में कहा गया है कि caravan route, रेगिस्तान से होते हुए Africa के आंतरिक क्षेत्र में स्थित Timbuktu तक फैला हुआ है। अतः, &lsquo;in&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML - more compact
            let html = `
                <div class="card mb-3">
                    <div class="card-body">
                        <div class="question-text mb-3">
                            <div class="en">${question.question_en}</div>
                            <div class="hi" style="display:none">${question.question_hi}</div>
                        </div>
                        <div class="options">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = 'option-label';
                // When reviewing, highlight user's choice and correct answer
                if (submitted) {
                    if (opt === question.correct) {
                        optionClass += ' border-success bg-success bg-opacity-10';
                    }
                    if (answers[question.id] === opt && opt !== question.correct) {
                        optionClass += ' border-danger bg-danger bg-opacity-10';
                    }
                }
                html += `
                    <div class="form-check mb-2">
                        <input class="form-check-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${answers[question.id] === opt ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                        <label class="form-check-label w-100 ${optionClass}" for="opt-${index}-${opt}">
                            ${submitted && opt === question.correct ? '<i class="fas fa-check-circle text-success me-1"></i>' : ''}
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const alertClass = isCorrect ? 'alert-success' : 'alert-danger';
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="alert ${alertClass} mt-3 py-2">
                        <strong>${resultText}</strong>
                        <div class="mt-1 small">
                            <strong>Solution:</strong>
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                document.getElementById(`box-${index}`).classList.add('attempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { if (currentQuestion > 0) showQuestion(currentQuestion - 1); }
        function nextQuestion() { if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
            // Update button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const content = document.getElementById('main-content');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                if (window.innerWidth >= 992) {
                    content.classList.add('with-nav');
                }
            } else {
                nav.classList.remove('show');
                if (window.innerWidth >= 992) {
                    content.classList.remove('with-nav');
                }
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer - compact
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            if (confirm(`You have attempted ${attempted} of ${questions.length} questions.
${remaining} questions are unattempted.
Are you sure you want to submit?`)) {
                submitTest();
            }
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('bg-secondary', 'text-white');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            resultsModal.show();
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.classList.replace('btn-light', 'btn-success');
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            resultsModal.hide();
            showQuestion(0);
        }
        // Add event listener for window resize
        window.addEventListener('resize', function() {
            if (window.innerWidth >= 992) {
                if (document.getElementById('question-nav').classList.contains('show')) {
                    document.getElementById('main-content').classList.add('with-nav');
                }
            } else {
                document.getElementById('main-content').classList.remove('with-nav');
            }
        });
    </script>
</body>
</html>