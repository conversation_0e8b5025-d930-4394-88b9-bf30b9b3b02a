<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MOCKS WALLAH</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        /* Enhanced styling with better mobile support */
        :root {
            --nav-width: 280px;
        }
        body {
            overflow-x: hidden;
        }
        .question-nav {
            position: fixed;
            top: 70px;
            right: 0;
            height: calc(100vh - 70px);
            width: var(--nav-width);
            transition: all .3s;
            z-index: 100;
            display: flex;
            flex-direction: column;
            background-color: #f8f9fa;
            box-shadow: -2px 0 5px rgba(0,0,0,0.1);
        }
        .question-content {
            transition: all .3s;
            padding-bottom: 60px;
        }
        .question-box {
            width: 38px;
            height: 38px;
            margin: 4px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 500;
            transition: all 0.2s;
        }
        .question-box:hover {
            transform: scale(1.05);
        }
        .attempted { background-color: #3498db !important; color: white; }
        .correct { background-color: #2ecc71 !important; color: white; }
        .incorrect { background-color: #e74c3c !important; color: white; }
        .current { border: 3px solid #f39c12 !important; }
        #question-boxes {
            overflow-y: auto;
            max-height: calc(100vh - 140px);
            padding: 5px;
        }
        .navbar-brand {
            font-weight: 600;
            letter-spacing: 0.5px;
        }
        .navbar .form-select {
            max-width: 160px;
        }
        .form-check-label {
            cursor: pointer;
            transition: background-color 0.15s;
            border: 1px solid #dee2e6;
        }
        .form-check-label:hover {
            background-color: #f8f9fa;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.5;
        }
        .options .form-check {
            margin-left: 10px;
        }
        /* Mobile-specific styles */
        @media (max-width: 991px) {
            .navbar-nav.me-auto {
                margin-bottom: 10px !important;
                margin-top: 10px;
            }
            .navbar .d-flex {
                flex-wrap: wrap;
                gap: 8px;
                justify-content: center;
            }
            #timer {
                min-width: 80px;
                text-align: center;
            }
            .navbar .form-select {
                max-width: 100%;
                width: 100%;
            }
        }
        @media (max-width: 767px) {
            .question-nav {
                width: 100%;
                right: -100%;
            }
            .question-content {
                margin-right: 0 !important;
            }
            #question-counter {
                font-size: 0.9rem;
            }
            .navbar-brand {
                font-size: 1.1rem;
            }
            .question-text {
                font-size: 1rem;
            }
        }
        /* Fix for proper line breaks in questions and options */
        .preserve-breaks {
            white-space: pre-line;
        }
    </style>
</head>
<body>
    <div class="container-fluid p-0">
        <!-- Header with controls -->
        <nav class="navbar navbar-expand navbar-dark bg-primary sticky-top">
            <div class="container-fluid flex-column flex-lg-row">
                <a class="navbar-brand mb-2 mb-lg-0" href="#">
                    <i class="fas fa-graduation-cap me-2"></i>MOCKS WALLAH
                </a>
                <!-- Section selector -->
                <div class="navbar-nav me-auto mb-2 mb-lg-0 w-100 w-lg-auto">
                    <select class="form-select" id="section-selector" onchange="switchSection(this.value)">
                        <option value="all">All Sections</option>
                    </select>
                </div>
                <div class="d-flex align-items-center flex-wrap gap-2 justify-content-center">
                    <!-- Timer -->
                    <div class="bg-light text-primary rounded px-3 py-2 fw-bold" id="timer">
                        <i class="far fa-clock me-1"></i><span id="timer-display">18:00</span>
                    </div>
                    <!-- Submit button -->
                    <button class="btn btn-success" id="submit-btn" onclick="submitTest()">
                        <i class="fas fa-check me-1"></i> Submit
                    </button>
                    <!-- Language toggle -->
                    <button class="btn btn-light" id="lang-btn" onclick="toggleLanguage()">
                        <i class="fas fa-language"></i>
                    </button>
                    <!-- Menu toggle for mobile -->
                    <button class="btn btn-light d-md-none" id="menu-toggle" onclick="toggleMenu()">
                        <i class="fas fa-th"></i>
                    </button>
                </div>
            </div>
        </nav>
        <div class="row m-0">
            <!-- Main content area -->
            <div class="col p-0 question-content" id="question-content">
                <div class="container py-4">
                    <!-- Question navigation -->
                    <div class="d-flex justify-content-between mb-4">
                        <button class="btn btn-outline-primary" onclick="prevQuestion()">
                            <i class="fas fa-chevron-left me-1"></i> Previous
                        </button>
                        <div class="fw-bold" id="question-counter">Question 1 of 25</div>
                        <button class="btn btn-outline-primary" onclick="nextQuestion()">
                            Next <i class="fas fa-chevron-right ms-1"></i>
                        </button>
                    </div>
                    <!-- Questions will be displayed here -->
                    <div id="questions-container"></div>
                </div>
            </div>
            <!-- Question navigation sidebar -->
            <div class="bg-light question-nav" id="question-nav">
                <div class="p-3">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h5 class="m-0">Questions</h5>
                        <div>
                            <span class="badge bg-secondary me-1" id="attempted-count">0</span>
                            <span class="badge bg-secondary" id="total-count">25</span>
                            <button class="btn btn-sm btn-outline-secondary ms-2 d-md-none" onclick="toggleMenu()">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    </div>
                    <div class="d-flex flex-wrap" id="question-boxes"></div>
                </div>
            </div>
        </div>
        <!-- Results modal -->
        <div class="modal fade" id="results-modal" tabindex="-1" aria-hidden="true">
            <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content">
                    <div class="modal-header bg-primary text-white">
                        <h5 class="modal-title">Test Results</h5>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <div class="row text-center">
                            <div class="col-6 mb-3">
                                <div class="p-3 bg-primary bg-opacity-10 rounded">
                                    <h3 id="score-value">0</h3>
                                    <div class="small text-muted">Score</div>
                                </div>
                            </div>
                            <div class="col-6 mb-3">
                                <div class="p-3 bg-success bg-opacity-10 rounded">
                                    <h3 id="correct-value">0</h3>
                                    <div class="small text-muted">Correct</div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="p-3 bg-danger bg-opacity-10 rounded">
                                    <h3 id="incorrect-value">0</h3>
                                    <div class="small text-muted">Incorrect</div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="p-3 bg-secondary bg-opacity-10 rounded">
                                    <h3 id="unattempted-value">0</h3>
                                    <div class="small text-muted">Unattempted</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-primary" onclick="reviewTest()">Review Test</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 18 * 60;
        let resultsModal;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            resultsModal = new bootstrap.Modal(document.getElementById('results-modal'));
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 768) {
                document.getElementById('question-content').style.marginRight = 'var(--nav-width)';
            }
            // Add window resize handler for responsive layout
            window.addEventListener('resize', () => {
                if (window.innerWidth >= 768) {
                    if (document.getElementById('question-nav').style.right === '0px') {
                        document.getElementById('question-content').style.marginRight = 'var(--nav-width)';
                    }
                } else {
                    document.getElementById('question-content').style.marginRight = '0';
                }
            });
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["6"] = {
                name: "Reasoning",
                start: 0,
                end: 23
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="6">Reasoning</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 24,
                end: 24
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "6",
                    question_en: " <p>1</span><span style=\"font-family:Times New Roman\">. Y is in the East of X which is in the North of Z. If P is in the South of Z, then in which direction of Y, is P?  ",
                    question_hi: "<p><span style=\"font-family: Baloo;\">1. Y, X के पूरब में है जो Z के उत्तर में है | यदि P, Z के दक्षिण में है, तो P, Y से किस दिशा में है ? </span></p>",
                    options_en: [
                        " <p> North </span></p>",
                        " <p> South  </span></p>",
                        " <p> South east </span></p>",
                        " <p> None of these </span></p>"
                    ],
                    options_hi: [
                        "<p>उत्तर</p>",
                        "<p>दक्षिण</p>",
                        "<p>दक्षिण पूर्व</p>",
                        "<p>इनमें से कोई नहीं</p>"
                    ],
                    solution_en: " <p><span style=\"font-family:Times New Roman\">(</span><span style=\"font-family:Times New Roman\">d)  P is in the South-West of Y.</span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1626691090/word/media/image17.png\"/></p>",
                    solution_hi: "<p><span style=\"font-family: Times New Roman;\">(</span><span style=\"font-family: Times New Roman;\">d) P, Y के दक्षिण-पश्चिम में है।</span></p>\r\n<p><img src=\"admin/resources/qdocs/test-1-EN-1/1626691090/word/media/image17.png\" /></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "6",
                    question_en: " <p>2. Look at this series. What number should come next?  </span></p> <p><span style=\"font-family:Times New Roman\">36, 34, 30, 28, 24, ...    ",
                    question_hi: "<p><span style=\"font-family: Baloo;\">2. इस श्रृंखला का अध्ययन करें| अगले स्थान पर कौन सी संख्या आनी चाहिए ? </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">36, 34, 30, 28, 24, ... </span></p>",
                    options_en: [
                        " <p> 20</span></p>",
                        " <p> 22</span></p>",
                        " <p> 23</span></p>",
                        " <p> 26</span></p>"
                    ],
                    options_hi: [
                        "<p><span style=\"font-family: Times New Roman;\">20</span></p>",
                        "<p>22</p>",
                        "<p>23</p>",
                        "<p>26</p>"
                    ],
                    solution_en: " <p><span style=\"font-family:Times New Roman\">(b)  This is an alternating number subtraction series. First, 2 is subtracted, then 4, then 2, and so on. Therefore : 24 - 2 = 22</span></p>",
                    solution_hi: "<p><span style=\"font-family: Times New Roman;\">(b) यह एक वैकल्पिक संख्या घटाव श्रृंखला है। पहले 2 घटाया जाता है, फिर 4, फिर 2, और इसी तरह। इसलिए: 24 - 2 = 22</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "6",
                    question_en: " <p>3. Select the option that is related to the third term in the same way as the second term is related to the first term. </span></p> <p><span style=\"font-family:Times New Roman\">Peacock : India :: Bear : ?    ",
                    question_hi: "<p><span style=\"font-family: Baloo;\">उस विकल्प का चयन करें जो तीसरे शब्द से ठीक उसी प्रकार संबंधित है जिस प्रकार दूसरा शब्द पहले शब्द से संबंधित है | </span></p>\r\n<p><span style=\"font-family: Baloo;\">मोर : भारत :: भालू : ? </span></p>",
                    options_en: [
                        " <p> Australia </span></p>",
                        " <p> America </span></p>",
                        " <p> Russia </span></p>",
                        " <p> England</span></p>"
                    ],
                    options_hi: [
                        "<p><span style=\"font-family: Baloo;\">ऑस्ट्रेलिया </span></p>",
                        "<p><span style=\"font-family: Baloo;\">अमेरिका </span></p>",
                        "<p><span style=\"font-family: Baloo;\">रूस </span></p>",
                        "<p><span style=\"font-family: Baloo;\"> इंग्लैंड </span></p>"
                    ],
                    solution_en: " <p><span style=\"font-family:Times New Roman\">(c)  As Peacock is the national bird of India, similarly Bear is the national animal of Russia.</span></p>",
                    solution_hi: "<p><span style=\"font-family: Times New Roman;\">(c) जिस प्रकार मोर भारत का राष्ट्रीय पक्षी है, उसी प्रकार भालू रूस का राष्ट्रीय पशु है।</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "6",
                    question_en: "<p>4. In the given question, interchange the two numbers to form a correct equation.</p>\r\n<p><span style=\"font-family: Times New Roman;\">8&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#215;</mo></math> (4/3) + 9 &ndash; 5 = 10 </span></p>",
                    question_hi: "<p><span style=\"font-family: Baloo;\">दिए गए प्रश्न में, समीकरण को सही करने के लिए किन दो संख्याओं को आपस में बदल देने की आवश्यकता है ? </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">8 x (4/3) + 9 &ndash; 5 = 10</span></p>",
                    options_en: [
                        "<p><span style=\"font-family: Times New Roman;\">5, 3</span><span style=\"font-family: Times New Roman;\"> </span></p>",
                        "<p><span style=\"font-family: Times New Roman;\">8, 4</span><span style=\"font-family: Times New Roman;\"> </span></p>",
                        "<p><span style=\"font-family: Times New Roman;\">9, 3</span><span style=\"font-family: Times New Roman;\"> </span></p>",
                        "<p><span style=\"font-family: Times New Roman;\">4, 3</span></p>"
                    ],
                    options_hi: [
                        "<p>5, 3</p>",
                        "<p>8, 4</p>",
                        "<p>9, 3</p>",
                        "<p>4, 3</p>"
                    ],
                    solution_en: "<p><span style=\"font-family: Times New Roman;\">(d) It is clear that on solving </span><span style=\"font-family: Times New Roman;\">8&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#215;</mo></math> (3/4) we get a decimal solution so on interchanging 3 and 4 a whole number is obtained and the equation becomes correct.</span></p>",
                    solution_hi: "<p><span style=\"font-family: Times New Roman;\">(d) यह स्पष्ट है कि 8 &times; (3/4) को हल करने पर हमें एक दशमलव हल मिलता है, इसलिए 3 और 4 को आपस में बदलने पर एक पूर्ण संख्या प्राप्त होती है और समीकरण सही हो जाता है।</span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "6",
                    question_en: "<p>5<span style=\"font-family: Times New Roman;\">. Choose that set of numbers from the four alternative sets, that is similar to the given set.</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Given set : (1, 8, 27) </span></p>",
                    question_hi: "<p><span style=\"font-family: Baloo;\">5. उस समूह का चयन करें जिसमें संख्याओं के बीच ठीक वही संबंध है जो संबंध नीचे दिए गए समूह की संख्याओं में है |</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Given set : (1, 8, 27)</span></p>",
                    options_en: [
                        "<p>(5, 12, 32)</p>",
                        "<p><span style=\"font-weight: 400;\">&nbsp;(32, 39, 59)</span></p>",
                        "<p><span style=\"font-weight: 400;\">(60, 79, 86)</span><span style=\"font-weight: 400;\"> </span></p>",
                        "<p><span style=\"font-weight: 400;\">(64, 125, 216)</span></p>"
                    ],
                    options_hi: [
                        "<p><span style=\"font-weight: 400;\">&nbsp;(5, 12, 32)</span><span style=\"font-weight: 400;\"> </span></p>",
                        "<p><span style=\"font-weight: 400;\">(32, 39, 59)</span></p>",
                        "<p><span style=\"font-weight: 400;\">&nbsp;(60, 79, 86)</span><span style=\"font-weight: 400;\"> </span></p>",
                        "<p><span style=\"font-family: Times New Roman;\"> (64, 125, 216)</span><span style=\"font-family: Times New Roman;\"> </span></p>"
                    ],
                    solution_en: "<p><span style=\"font-family: Times New Roman;\">(d) Logic is : cube of three consecutive numbers </span></p>\r\n<p><span style=\"font-weight: 400;\">(64, 125, 216) </span>follows the above logic as it is a cube of 4, 5 and 6.</p>",
                    solution_hi: "<p><span style=\"font-family: Times New Roman;\">(d) तर्क है : तीन क्रमागत संख्याओं का घन</span></p>\r\n<p><span style=\"font-weight: 400;\">(64, 125, 216) उपरोक्त तर्क का अनुसरण करता है क्योंकि यह 4, 5 और 6 का घन है।</span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "6",
                    question_en: " <p>6. Three of th</span><span style=\"font-family:Times New Roman\">e following four letter-clusters are alike in a certain way and one is different. Find the odd one out.  ",
                    question_hi: "<p><span style=\"font-family: Baloo;\">6. निम्नलिखित चार अक्षर-समूहों में से तीन एक निश्चित तरीके से एक जैसे हैं और एक अलग है। विजातीय को चुनें।</span></p>",
                    options_en: [
                        " <p> A B Vajpayee   </span></p>",
                        " <p> Pratibha Patil</span></p>",
                        " <p> Rajiv Gandhi </span></p>",
                        " <p> Manmohan Singh </span></p>"
                    ],
                    options_hi: [
                        "<p>अटल विहारी वाजपेयी</p>",
                        "<p>प्रतिभा पाटिल</p>",
                        "<p>राजीव गाँधी</p>",
                        "<p>मनमोहन सिंह</p>"
                    ],
                    solution_en: " <p><span style=\"font-family:Times New Roman\">(b)  Except Pratibha Patil(who was President of India), the rest are Ex Prime ministers of India.</span></p>",
                    solution_hi: "<p><span style=\"font-family: Times New Roman;\">(b) प्रतिभा पाटिल (जो भारत के राष्ट्रपति थे) को छोड़कर, बाकी भारत के पूर्व प्रधान मंत्री हैं।</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "6",
                    question_en: " <p>7</span><span style=\"font-family:Times New Roman\">. If A + B means A is the mother of B; A - B means A is the brother B; A % B means A is the father of B and A x B means A is the sister of B, which of the following shows that P is the maternal uncle of Q?   ",
                    question_hi: "<p><span style=\"font-family: Baloo;\">7. यदि A+B का अर्थ है, A, B की माँ है ; A-B का अर्थ है A, B का भाई है ; A% B का अर्थ है A, B का पिता है तथा </span><span style=\"font-family: Times New Roman;\">AxB</span><span style=\"font-family: Baloo;\"> का अर्थ है A, B की बहन है, तो निम्न में से कौन यह दर्शाता है कि P, Q का मामा है ? </span></p>",
                    options_en: [
                        " <p> Q - N + M x P</span></p>",
                        " <p> P + S x N - Q</span></p>",
                        " <p> P - M + N x Q</span></p>",
                        " <p> Q - S % P</span></p>"
                    ],
                    options_hi: [
                        "<p><span style=\"font-family: Times New Roman;\">Q - N + M x P</span></p>",
                        "<p><span style=\"font-family: Times New Roman;\"> P + S x N - Q</span></p>",
                        "<p><span style=\"font-family: Times New Roman;\"> P - M + N x Q</span></p>",
                        "<p><span style=\"font-family: Times New Roman;\">Q - S % P</span></p>"
                    ],
                    solution_en: " <p><span style=\"font-family:Times New Roman\">(c</span><span style=\"font-family:Times New Roman\">)  Option a : P’s sister’s mother’s brother is Q : Q is maternal uncle of P(Wrong)</span></p> <p><span style=\"font-family:Times New Roman\">Option b : Q’s brother’s sister’s mother is P : P is mother of Q(Wrong)</span></p> <p><span style=\"font-family:Times New Roman\">Option c :  Q’s sister’s mother’s brother is P : P is maternal uncle of Q(Right)</span></p> <p><span style=\"font-family:Times New Roman\">Option d : P’s father’s brother is Q : Q is uncle of P (Wrong)</span></p>",
                    solution_hi: "<p><span style=\"font-family: Times New Roman;\">(c</span><span style=\"font-family: Times New Roman;\">)&nbsp;</span></p>\r\n<p>विकल्प a : P की बहन की माँ का भाई Q है : Q, P का मामा है (गलत)</p>\r\n<p>विकल्प b: Q के भाई की बहन की मां P है: P क्यू की मां है (गलत)</p>\r\n<p>विकल्प c : Q की बहन की माँ का भाई P है: P, Q का मामा है (सही&nbsp;)</p>\r\n<p>विकल्प d : P के पिता का भाई Q है : Q, P का अंकल है (गलत)</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "6",
                    question_en: "<p>8<span style=\"font-family: Times New Roman;\">. In the following figure small square represents the persons who know English, triangle to those who know Marathi, big square to those who know Telugu and circle to those who know Hindi. In the different regions of the figures from 1 to 12 are given. </span></p>\n<p><strong id=\"docs-internal-guid-02bfa630-7fff-93d5-183b-afc731d099fd\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfUnqrem1NgXOZjZglz-zYi9KjelGtcP4rnoCCiQkEwV3jvlLDXmmGEjMVUuOryuM-1ORSYoj4wJ2gfAhkaJ25unHAgNt6UTgS4gDm_movEA0m7f01n6uoE_bBIy_kyhnWNcrL9sQ?key=u3-Gnt1USFjFx8h4QpKY6y93\" width=\"181\" height=\"162\"></strong></p>\n<p><span style=\"font-family: Times New Roman;\">How many people can speak Marathi and Telugu both ? </span></p>",
                    question_hi: "<p><span style=\"font-family: Baloo;\">8. नीचे दी गयी आकृति में, छोटा वर्ग उन लोगों को दर्शाता है जो अंग्रेजी जानते हैं | त्रिभुज उन्हें दर्शाता है जो मराठी जानते हैं | बड़ा वर्ग उन्हें दर्शाता है जो तेलुगू जानते हैं तथा वृत्त उन लोगों को दर्शाता है जो हिन्दी जानते हैं | आकृति के अलग-अलग क्षेत्रों में 1 से 12 तक की संख्याएँ दी गयी हैं |</span></p>\n<p><strong id=\"docs-internal-guid-c9652235-7fff-bb5d-4410-855f8698a81b\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfjT0BBkgtUT3RWbFXY5P9e1ystt91natc3hec9LRFq4wUdgQSe0cDqonDxGvLACDuup1Zs3KZC1aAzRYWNNe_S_khELA78Lxb2hZ-lKJ9yEssqnqWolgGqe4ohYfxbLVhMhEGu7w?key=u3-Gnt1USFjFx8h4QpKY6y93\" width=\"191\" height=\"173\"></strong></p>\n<p><span style=\"font-family: Baloo;\">कितने व्यक्ति मराठी और तेलुगु दोनों बोल सकते हैं ? </span></p>",
                    options_en: [
                        "<p>10<span style=\"font-family: Times New Roman;\"> </span></p>",
                        "<p>11</p>",
                        "<p>13<span style=\"font-family: Times New Roman;\"> </span></p>",
                        "<p>None of these</p>"
                    ],
                    options_hi: [
                        "<p>10</p>",
                        "<p>11</p>",
                        "<p>13</p>",
                        "<p>इनमें से कोई नहीं</p>"
                    ],
                    solution_en: "<p><span style=\"font-family: Times New Roman;\">(c) Marathi + Telugu = 6</span></p>\n<p><span style=\"font-family: Times New Roman;\">Marathi + Telugu + Hindi = 7</span></p>\n<p><span style=\"font-family: Times New Roman;\">Total = 6 + 7 = 13</span></p>",
                    solution_hi: "<p><span style=\"font-family: Times New Roman;\">(c)&nbsp;</span></p>\n<p>मराठी +तेलुगु = 6</p>\n<p>मराठी+तेलुगु +हिंदी = 7</p>\n<p>कुल = 6 +7 = 13</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "6",
                    question_en: " <p>9</span><span style=\"font-family:Times New Roman\">. In a certain code \'ROAR\' is written as \'URDU\'. How is \'URDU\' written in that code?  ",
                    question_hi: "<p><span style=\"font-family: Baloo;\">9. किसी निश्चित कूट भाषा में, &lsquo;ROAR&rsquo; को &lsquo;URDU&rsquo; लिखा जाता है | इस कूट में &lsquo;URDU&rsquo; को कैसे लिखा जाएगा ? </span></p>",
                    options_en: [
                        " <p> VXDQ</span></p>",
                        " <p> </span><span style=\"font-family:Times New Roman\">XUGX         </span></p>",
                        " <p> </span><span style=\"font-family:Times New Roman\">RAOR</span></p>",
                        " <p><span style=\"font-family:Times New Roman\">VSOV</span></p>"
                    ],
                    options_hi: [
                        "<p><span style=\"font-family: Times New Roman;\">VXDQ</span></p>",
                        "<p><span style=\"font-family: Times New Roman;\">XUGX </span></p>",
                        "<p><span style=\"font-family: Times New Roman;\">RAOR</span></p>",
                        "<p><span style=\"font-family: Times New Roman;\">VSOV</span></p>"
                    ],
                    solution_en: " <p><span style=\"font-family:Times New Roman\">(b</span><span style=\"font-family:Times New Roman\">) Logic is : +3 rule is applied</span></p> <p><span style=\"font-family:Times New Roman\">So, U +3 = X, R +3 = U, D +3 = G and U +3 = X.</span></p> <p><span style=\"font-family:Times New Roman\">So, the code of URDU will be </span><span style=\"font-family:Times New Roman\">XUGX</span><span style=\"font-family:Times New Roman\">.</span></p>",
                    solution_hi: "<p><span style=\"font-family: Times New Roman;\">(b</span><span style=\"font-family: Times New Roman;\">) तर्क है : +3 का नियम लागू होता है</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">इसलिए,U +3 = X, R +3 = U, D +3 = Gतथा U +3 = X.</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">इसलिए,&nbsp; \'URDU\' का कोड&nbsp; \'</span><span style=\"font-family: Times New Roman;\">XUGX\' </span><span style=\"font-family: Times New Roman;\">होगा&nbsp;</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "6",
                    question_en: " <p>10.  Which option will replace the question mark (?) in the </span><span style=\"font-family:Times New Roman\">following series? </span></p> <p><span style=\"font-family:Times New Roman\">FAG, GAF, HAI, IAH, ____    ",
                    question_hi: "<p><span style=\"font-family: Baloo;\">10. नीचे दी गयी श्रृंखला में प्रश्न चिन्ह ( ? ) के स्थान पर कौन सा विकल्प आएगा ? </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">FAG, GAF, HAI, IAH, ____</span></p>",
                    options_en: [
                        " <p> JAK</span><span style=\"font-family:Times New Roman\">        </span></p>",
                        " <p> HAL  </span><span style=\"font-family:Times New Roman\">  </span></p>",
                        " <p> JAI</span><span style=\"font-family:Times New Roman\">                  </span></p>",
                        " <p> HAK</span></p>"
                    ],
                    options_hi: [
                        "<p><span style=\"font-family: Times New Roman;\">JAK</span><span style=\"font-family: Times New Roman;\"> </span></p>",
                        "<p><span style=\"font-family: Times New Roman;\">HAL </span></p>",
                        "<p><span style=\"font-family: Times New Roman;\">JAI</span></p>",
                        "<p><span style=\"font-family: Times New Roman;\">HAK</span></p>"
                    ],
                    solution_en: " <p>(a)</span><span style=\"font-family:Times New Roman\"> Logic is : First letter follows +1, Second letter A remains same and Third letter follows -1, +3, -1, +3  rule. </span><span style=\"font-family:Times New Roman\">So, I + 1 = J, A, H +3 = K So, JAK is the correct answer.</span></p>",
                    solution_hi: "<p>(a)तर्क है: पहला अक्षर +1 का अनुसरण करता है, दूसरा अक्षर A वही रहता है और तीसरा अक्षर -1, +3, -1, +3 नियम का पालन करता है। तो, I +1 = J, A, H +3 = K तो, JAK सही उत्तर है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "6",
                    question_en: " <p>11. Select the option that is related to the third number in the same way as the second number is related to the first number.</span></p> <p><span style=\"font-family:Times New Roman\">97 : 89 :: 43 : __   ",
                    question_hi: "<p><span style=\"font-family: Baloo;\">11. उस विकल्प का चयन करें जो तीसरी संख्या से उसी तरह से संबंधित है जैसे दूसरी </span><span style=\"font-family: Baloo;\">संख्या पहली संख्या से संबंधित है?</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">97 : 89 :: 43 : __</span></p>",
                    options_en: [
                        " <p><span style=\"font-family:Times New Roman\"> 41</span><span style=\"font-family:Times New Roman\">         </span></p>",
                        " <p> 39</span><span style=\"font-family:Times New Roman\">      </span></p>",
                        " <p> 31</span><span style=\"font-family:Times New Roman\">           </span></p>",
                        " <p> 40</span></p>"
                    ],
                    options_hi: [
                        "<p><span style=\"font-family: Times New Roman;\">41</span></p>",
                        "<p><span style=\"font-family: Times New Roman;\">39</span><span style=\"font-family: Times New Roman;\"> </span></p>",
                        "<p><span style=\"font-family: Times New Roman;\">31</span></p>",
                        "<p><span style=\"font-family: Times New Roman;\">40</span><span style=\"font-family: Times New Roman;\"> </span></p>"
                    ],
                    solution_en: " <p>(a)</span><span style=\"font-family:Times New Roman\">  Previous prime number to 97 is 89. Similarly for 43 the previous prime is 41.</span></p>",
                    solution_hi: "<p>(a)<span style=\"font-family: Times New Roman;\"> 97 से पहले की अभाज्य संख्या 89 है। इसी तरह 43 के लिए पिछला अभाज्य संख्या 41 है।</span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "6",
                    question_en: " <p>12. A statement is given, followed by two conclusions numbered I, and II. Assuming the statements to be true even if they seem to be at variance with commonly known facts, decide which of the </span><span style=\"font-family:Times New Roman\">conclusions logically follow(s) from the statements. </span></p> <p><span style=\"font-family:Times New Roman\">Statements: Government has spoiled many top ranking financial institutions by appointing bureaucrats as Directors of these institutions.</span></p> <p><span style=\"font-family:Times New Roman\">Conclusions</span></p> <p><span style=\"font-family:Times New Roman\">I. Government should appoint Directors of the financial institutions taking into consideration the expertise of the person in the area of finance.</span></p> <p><span style=\"font-family:Times New Roman\">II.The Director of the financial institution should have expertise commensurate with the financial work carried out by the institute.    ",
                    question_hi: "<p><span style=\"font-family: Baloo;\">12. एक कथन दिया गया है, जिसके बाद दो निष्कर्ष I और II हैं | इन कथनों को सही मानें भले ही वे सामान्य रूप से ज्ञात तथ्यों से भिन्न प्रतीत होते हों | यह तय करें कि इन कथनों से तर्कपूर्ण ढंग से इनमें से कौन सा निष्कर्ष निकाला जा सकता है |</span></p>\r\n<p><span style=\"font-family: Baloo;\">कथन : सरकार ने नौकरशाहों को कई शीर्ष वित्तीय संस्थानों का निदेशक नियुक्त करके इन शीर्ष वित्तीय संस्थानों को खराब कर दिया है | </span></p>\r\n<p><span style=\"font-family: Baloo;\">निष्कर्ष :</span></p>\r\n<p><span style=\"font-family: Baloo;\">I.सरकार को वित्तीय संस्थानों के निदेशकों की नियुक्ति वित्त के क्षेत्र में उस व्यक्ति की विशेषज्ञता को ध्यान में रखकर करनी चाहिए | </span></p>\r\n<p><span style=\"font-family: Baloo;\">II.वित्तीय संस्थानों के निदेशक के पास संस्थान के द्वारा किये जाने वाले वित्तीय कार्य के साथ तालमेल बिठाने की विशेषज्ञता होनी चाहिए | </span></p>",
                    options_en: [
                        " <p> Only conclusion I follows </span></p>",
                        " <p> Either I or II follows </span></p>",
                        " <p> Both I and II follow </span></p>",
                        " <p> Only conclusion II follows </span></p>"
                    ],
                    options_hi: [
                        "<p><span style=\"font-family: Baloo;\">केवल निष्कर्ष I सही है | </span></p>",
                        "<p><span style=\"font-family: Baloo;\"> या तो I या II सही है |</span></p>",
                        "<p><span style=\"font-family: Baloo;\">I और II दोनों सही है | </span></p>",
                        "<p><span style=\"font-family: Baloo;\">केवल निष्कर्ष II सही है | </span></p>"
                    ],
                    solution_en: " <p><span style=\"font-family:Times New Roman\">(c)  According to the statement, </span><span style=\"font-family:Times New Roman\">Government</span><span style=\"font-family:Times New Roman\"> has spoiled financial institutions by appointing bureaucrats as directors. This means that only those persons should be appointed as directors who are experts in finance and are acquainted with the financial work of the institute. So, both I and II follow.</span></p>",
                    solution_hi: "<p><span style=\"font-family: Times New Roman;\">(c) कथन &nbsp;के अनुसार : सरकार ने नौकरशाहों को निदेशक बनाकर वित्तीय संस्थानों को खराब किया है. इसका अर्थ यह हुआ कि निदेशक के रूप में केवल उन्हीं व्यक्तियों को नियुक्त किया जाना चाहिए जो वित्त के विशेषज्ञ हों और संस्थान के वित्तीय कार्यों से परिचित हों। अत: I और II दोनों अनुसरण करते हैं।</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "6",
                    question_en: " <p>1</span><span style=\"font-family:Times New Roman\">3. Look at this series.What number should come next? </span></p> <p><span style=\"font-family:Times New Roman\">22, 21, 23, 22, 24, 23, ...     ",
                    question_hi: "<p><span style=\"font-family: Baloo;\">13. इस श्रृंखला का अध्ययन करें| अगले स्थान पर कौन सी संख्या आनी चाहिए ? </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">22, 21, 23, 22, 24, 23, ... </span></p>",
                    options_en: [
                        " <p> 22</span><span style=\"font-family:Times New Roman\">      </span></p>",
                        " <p> 24</span><span style=\"font-family:Times New Roman\">             </span></p>",
                        " <p> 25</span><span style=\"font-family:Times New Roman\">                  </span></p>",
                        " <p> 26</span></p>"
                    ],
                    options_hi: [
                        "<p>22</p>",
                        "<p>24</p>",
                        "<p>25</p>",
                        "<p>26</p>"
                    ],
                    solution_en: " <p><span style=\"font-family:Times New Roman\">(</span><span style=\"font-family:Times New Roman\">c)  Logic is : -1, +2 rule is followed </span></p> <p><span style=\"font-family:Times New Roman\">So, 23 +2 = 25.</span></p>",
                    solution_hi: "<p><span style=\"font-family: Times New Roman;\">(</span><span style=\"font-family: Times New Roman;\">c)तर्क है: -1, +2 नियम का पालन किया जाता है</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">इसलिए,23 +2 = 25.</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "6",
                    question_en: " <p>14. Three of the following four letter-clusters are alike in a certain way and one is different. Find the odd one out.  ",
                    question_hi: "<p><span style=\"font-family: Baloo;\">14. निम्नलिखित चार अक्षर-समूहों में से तीन एक निश्चित तरीके से एक जैसे हैं और एक </span><span style=\"font-family: Baloo;\">अलग है। विजातीय को चुनें।</span></p>",
                    options_en: [
                        " <p>Copper </span></p>",
                        " <p>Iron     </span></p>",
                        " <p>Iodine    </span></p>",
                        " <p>Tin </span></p>"
                    ],
                    options_hi: [
                        "<p><span style=\"font-family: Baloo;\">तांबा </span></p>",
                        "<p><span style=\"font-family: Baloo;\">लोहा</span></p>",
                        "<p><span style=\"font-family: Baloo;\">आयोडीन </span></p>",
                        "<p><span style=\"font-family: Baloo;\"> टिन</span></p>"
                    ],
                    solution_en: " <p><span style=\"font-family:Times New Roman\">(c)  Except iodine rest are metals while iodine is non metal.</span></p>",
                    solution_hi: "<p><span style=\"font-family: Times New Roman;\">(c) आयोडीन को छोड़कर शेष धातुएँ हैं जबकि आयोडीन अधातु है।</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "6",
                    question_en: "<p>1<span style=\"font-family: Times New Roman;\">5. Which figure can be placed in place of a question mark?</span></p>\n<p><strong id=\"docs-internal-guid-a4181590-7fff-1b75-cb35-65ae88453d86\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfBmx01nnUmyqhc82NXGNXutsDYNa2VJH5hX11okHXbQuTU7BMdXkLDM2awmye7asuMix2Awei-BD3VaAhqVasK_ENzUhd07F7fVNQn6MNvd7O820UxynoE3667DjkBWn9-uoBK4w?key=u3-Gnt1USFjFx8h4QpKY6y93\" width=\"385\" height=\"133\"></strong></p>",
                    question_hi: "<p><span style=\"font-family: Baloo;\">15. प्रश्न चिन्ह ( ? ) के स्थान पर किस आकृति को रखा जा सकता है ? </span></p>\n<p><strong id=\"docs-internal-guid-43a9c22d-7fff-ced6-fdea-692e8004c05b\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXeeYrBQWgojye_YqKtJ2quoCJTask3Q9Une58Au6EdIyrI2XOC5blDOYNYbDfQMQ5qPNiyJM2tnMSwnxuQtUGdReDu7XPmpOxwlYqgfxBKTvZPEOP3q9tYwB9P4KIb1cSAt6uPkBA?key=u3-Gnt1USFjFx8h4QpKY6y93\" width=\"364\" height=\"132\"></strong></p>",
                    options_en: [
                        "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1626691090/word/media/image11.png\"></p>",
                        "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1626691090/word/media/image9.png\"></p>",
                        "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1626691090/word/media/image1.png\"></p>",
                        "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1626691090/word/media/image10.png\"></p>"
                    ],
                    options_hi: [
                        "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1626691090/word/media/image11.png\"></p>",
                        "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1626691090/word/media/image9.png\"></p>",
                        "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1626691090/word/media/image1.png\"></p>",
                        "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1626691090/word/media/image10.png\"></p>"
                    ],
                    solution_en: "<p>(a)<span style=\"font-family: Times New Roman;\"> </span></p>\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1626691090/word/media/image11.png\"></p>",
                    solution_hi: "<p>(a)<span style=\"font-family: Times New Roman;\"> </span></p>\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1626691090/word/media/image11.png\"></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "16",
                    section: "6",
                    question_en: " <p>1</span><span style=\"font-family:Times New Roman\">6. A dice is numbered from 1 to 6 in different ways. If 1 is adjacent to 2, 4 and 6, then which of the following statements is necessarily true?   ",
                    question_hi: "<p><span style=\"font-family: Baloo;\">16. एक पासे पर 1 से 6 तक की संख्याएँ लिखी हुई हैं | यदि 1 2,4 और 6 के सन्निकट है, तो निम्न में से कौन सा कथन निश्चित रूप से सही होगा ? </span></p>",
                    options_en: [
                        " <p> 2 is opposite to 6</span></p>",
                        " <p> 1 is adjacent to 3 </span></p>",
                        " <p> 3 is adjacent to 5 </span></p>",
                        " <p> 3 is opposite to 5 </span></p>"
                    ],
                    options_hi: [
                        "<p><span style=\"font-family: Baloo;\"> 2, 6 के विपरीत है | </span></p>",
                        "<p><span style=\"font-family: Baloo;\">1, 3 के सन्निकट है | </span></p>",
                        "<p><span style=\"font-family: Baloo;\">3, 5 के सन्निकट है | </span></p>",
                        "<p><span style=\"font-family: Baloo;\"> 3, 5 के विपरीत है |</span></p>"
                    ],
                    solution_en: " <p><span style=\"font-family:Times New Roman\">(</span><span style=\"font-family:Times New Roman\">c)  If 1 is adjacent to 2, 4 and 6 then either 3 or 5 lies opposite to 1. So, the numbers 3 and 5 cannot lie opposite to each other. Hence, 3 is adjacent to 5 (necessarily).</span></p>",
                    solution_hi: "<p><span style=\"font-family: Times New Roman;\">(</span><span style=\"font-family: Times New Roman;\">c)यदि 1या तो &nbsp;2, 4 और 6 के निकट है। &nbsp; &nbsp;3 या 5 ,1 के विपरीत स्थित है, इसलिए, संख्या 3 और 5 एक दूसरे के विपरीत नहीं हो सकते। अत: 3,आवश्य ही 5 &nbsp;के निकट है।</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "17",
                    section: "6",
                    question_en: "<p>17. How many rectangles are there in the given figure?</p>\n<p dir=\"ltr\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfcBjSWIuyuv-W-WelBll0CenW0Y3NBTl4yQvW36D3isTn_pmHGiR1y6yRSHJwVYxtwjNqriKf9tdlK9sdH4XAI-hUQCtnntbuGvQPZe6SXaSN_IGTNm9X_JULOZ_m1mqWg1Jl9bw?key=gJNpbfpUUvLwjsO-Le6-VpY3\" width=\"159\" height=\"150\"></p>\n<p>&nbsp;</p>",
                    question_hi: "<p><span style=\"font-family: Baloo;\">17.&nbsp; </span>दी गई आकृति में कितने आयत हैं ?</p>\n<p dir=\"ltr\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfcBjSWIuyuv-W-WelBll0CenW0Y3NBTl4yQvW36D3isTn_pmHGiR1y6yRSHJwVYxtwjNqriKf9tdlK9sdH4XAI-hUQCtnntbuGvQPZe6SXaSN_IGTNm9X_JULOZ_m1mqWg1Jl9bw?key=gJNpbfpUUvLwjsO-Le6-VpY3\" width=\"159\" height=\"150\"></p>\n<p>&nbsp;</p>",
                    options_en: [
                        "<p>8</p>",
                        "<p>7</p>",
                        "<p>6</p>",
                        "<p>5</p>"
                    ],
                    options_hi: [
                        "<p>8</p>",
                        "<p>7</p>",
                        "<p>6</p>",
                        "<p>5</p>"
                    ],
                    solution_en: "<p><span style=\"font-family: Times New Roman;\">(c)&nbsp;<strong><br></strong></span></p>\n<p dir=\"ltr\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfosYMK9XC8ms3JyjXq1vKnEatPvuiBkF98X2gwFLYsack83BsD2hv3aw-0D73nB7qDxAK9bYjFnIxc3UYClnu-w29aMdUvgbWDIgCCZVEmywZaq5f1AtkcToIy7vBiOshdPbud?key=gJNpbfpUUvLwjsO-Le6-VpY3\" width=\"190\" height=\"183\"></p>\n<p dir=\"ltr\">Total number of rectangle = 5 + ABCD = 6</p>\n<p>&nbsp;</p>",
                    solution_hi: "<p><span style=\"font-family: Times New Roman;\">(c)<strong><br></strong></span></p>\n<p dir=\"ltr\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfosYMK9XC8ms3JyjXq1vKnEatPvuiBkF98X2gwFLYsack83BsD2hv3aw-0D73nB7qDxAK9bYjFnIxc3UYClnu-w29aMdUvgbWDIgCCZVEmywZaq5f1AtkcToIy7vBiOshdPbud?key=gJNpbfpUUvLwjsO-Le6-VpY3\" width=\"190\" height=\"183\"></p>\n<p dir=\"ltr\">आयतो की कुल संख्या = 5 + ABCD = 6</p>\n<p>&nbsp;</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "18",
                    section: "6",
                    question_en: "<p>18.Select the correct mirror image of the given combination when the mirror is placed at MN as shown below.</p>\n<p><strong id=\"docs-internal-guid-73ca0e56-7fff-3f7f-dcbc-76ce2af99909\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXf6Z_e1NN5wLBOQ8Kd3F05DiYWOXJsaZQX5-AkNfPTEItUjOqOW92ynLvuCJLVlWwrsKuLFzXm-S5NJbySUkkQEb8A8x4mVJoLZBEoQ4GZ_okx-wryTTlP3fbLnx--mxj1ADTlDJg?key=HdXF7DP_JORvxyyajZRjmcjz\" width=\"157\" height=\"148\"></strong></p>",
                    question_hi: "<p><span style=\"font-family: Baloo;\">18. दर्पण को नीचे दर्शाए अनुसार MN पर रखे जाने पर दिए गए संयोजन के सही दर्पण प्रतिबिंब का चयन कीजिए।</span></p>\n<p><strong id=\"docs-internal-guid-6e73fee8-7fff-2398-0e9c-ec5083c963de\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXf6Z_e1NN5wLBOQ8Kd3F05DiYWOXJsaZQX5-AkNfPTEItUjOqOW92ynLvuCJLVlWwrsKuLFzXm-S5NJbySUkkQEb8A8x4mVJoLZBEoQ4GZ_okx-wryTTlP3fbLnx--mxj1ADTlDJg?key=HdXF7DP_JORvxyyajZRjmcjz\" width=\"157\" height=\"148\"></strong></p>",
                    options_en: [
                        "<p><strong id=\"docs-internal-guid-e5053aa0-7fff-5e3f-224e-992b433ed1db\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfXIcnhVhJ3qdCoKmChqLA6wKMPI50PZskrSy_4YW1TnhhVrApJNYG7Cqr2IDdHxG0VAYsEdjYFtiTqxVBGIoJvchdnN7Im3iTBoqBJIFi29B2w0IycRNARL05BGU9JDh-FF-jU?key=HdXF7DP_JORvxyyajZRjmcjz\" width=\"102\" height=\"30\"></strong></p>",
                        "<p><strong id=\"docs-internal-guid-336cf804-7fff-7b11-aa22-afa0569fa1e7\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcoB6ub5Gg7IoAgtE3d_jv1oxWqC1FiiAfV-w52F_J79JhmplzDpCoZYU12eoDOxlk91xCYbpP_uaLEN1Siej_47bo5K0Vg7r-8BpkjTpFX0Z09EzkOFhW-dDINhAqMR3PYh2senQ?key=HdXF7DP_JORvxyyajZRjmcjz\" width=\"98\" height=\"25\"></strong></p>",
                        "<p><strong id=\"docs-internal-guid-d5080dcf-7fff-950f-b3b1-c3490b5ca9e6\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcHd6lbkoXcgsrLfvuBtyR58zMQtIpsSh0dHx5DA0xKKyJqIqtgp2f_Z-c32yPb8UlAMc0ExsRTiNuX68GgEnrVVo7plq_biVq90l4NivuNWShvA6jlABnz5HdF89R9YBk32eZ2lw?key=HdXF7DP_JORvxyyajZRjmcjz\" width=\"101\" height=\"30\"></strong></p>",
                        "<p><strong id=\"docs-internal-guid-9ffe0852-7fff-b55c-23ba-21ee51df5612\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXf-i2xReq1EvJuhObbaqJHQkBcohGqT6t8EmnpGJgVqIlVqnlL5HYCldWrwkFP-SOGbvEQjMJrEXkHN2TvbvlFeZA1LtALkYOKUtW7zvv1WjCVbRyHhLJzVzjKA8_PJ2ipVk41K?key=HdXF7DP_JORvxyyajZRjmcjz\" width=\"104\" height=\"31\"></strong></p>"
                    ],
                    options_hi: [
                        "<p><strong id=\"docs-internal-guid-d5d3d849-7fff-44ec-aa43-661facf1949a\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfXIcnhVhJ3qdCoKmChqLA6wKMPI50PZskrSy_4YW1TnhhVrApJNYG7Cqr2IDdHxG0VAYsEdjYFtiTqxVBGIoJvchdnN7Im3iTBoqBJIFi29B2w0IycRNARL05BGU9JDh-FF-jU?key=HdXF7DP_JORvxyyajZRjmcjz\" width=\"102\" height=\"30\"></strong></p>",
                        "<p><strong id=\"docs-internal-guid-bd793059-7fff-5e1b-fdcc-bb6920c44f59\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcoB6ub5Gg7IoAgtE3d_jv1oxWqC1FiiAfV-w52F_J79JhmplzDpCoZYU12eoDOxlk91xCYbpP_uaLEN1Siej_47bo5K0Vg7r-8BpkjTpFX0Z09EzkOFhW-dDINhAqMR3PYh2senQ?key=HdXF7DP_JORvxyyajZRjmcjz\" width=\"98\" height=\"25\"></strong></p>",
                        "<p><strong id=\"docs-internal-guid-4084f36e-7fff-83af-7bc8-a335773edf08\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcHd6lbkoXcgsrLfvuBtyR58zMQtIpsSh0dHx5DA0xKKyJqIqtgp2f_Z-c32yPb8UlAMc0ExsRTiNuX68GgEnrVVo7plq_biVq90l4NivuNWShvA6jlABnz5HdF89R9YBk32eZ2lw?key=HdXF7DP_JORvxyyajZRjmcjz\" width=\"101\" height=\"30\"></strong></p>",
                        "<p><strong id=\"docs-internal-guid-39c1a482-7fff-aa86-b04c-6942668afa37\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXf-i2xReq1EvJuhObbaqJHQkBcohGqT6t8EmnpGJgVqIlVqnlL5HYCldWrwkFP-SOGbvEQjMJrEXkHN2TvbvlFeZA1LtALkYOKUtW7zvv1WjCVbRyHhLJzVzjKA8_PJ2ipVk41K?key=HdXF7DP_JORvxyyajZRjmcjz\" width=\"104\" height=\"31\"></strong></p>"
                    ],
                    solution_en: "<p><span style=\"font-family: Times New Roman;\">(a)&nbsp;</span></p>\n<p><strong id=\"docs-internal-guid-9ef0a095-7fff-c883-0e93-079def7c0eab\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdQ5qJxhL2mGNBYlUTcPgibCEZNayrvbxI8hMhOxeTvIhCCBmLYU9r3Y2xNXPrzKEan3_2fFpfPxOQJbRwJ8p8wWIVzkmmZ1xmeVzvQUb66cxpjgtjQCwejrV8530lDLZ513zkySQ?key=HdXF7DP_JORvxyyajZRjmcjz\" width=\"102\" height=\"30\"></strong></p>",
                    solution_hi: "<p><span style=\"font-family: Times New Roman;\">(a)&nbsp;</span></p>\n<p><strong id=\"docs-internal-guid-9ef0a095-7fff-c883-0e93-079def7c0eab\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdQ5qJxhL2mGNBYlUTcPgibCEZNayrvbxI8hMhOxeTvIhCCBmLYU9r3Y2xNXPrzKEan3_2fFpfPxOQJbRwJ8p8wWIVzkmmZ1xmeVzvQUb66cxpjgtjQCwejrV8530lDLZ513zkySQ?key=HdXF7DP_JORvxyyajZRjmcjz\" width=\"102\" height=\"30\"></strong></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "19",
                    section: "6",
                    question_en: " <p>19. </span><span style=\"font-family:Times New Roman\">In a certain code \'LIMCA\' is written as \'KJLDZ’. Which of the following words is written as \'LFWJBP\'?  ",
                    question_hi: "<p><span style=\"font-family: Baloo;\">19. किसी निश्चित कूट भाषा में, &lsquo;LIMCA&rsquo; को \'</span><span style=\"font-family: Times New Roman;\">KJLDZ</span><span style=\"font-family: Baloo;\">&rsquo; लिखा जाता है | निम्न में से किस शब्द को &lsquo;LFWJBP&rsquo; के रूप में लिखा जाएगा ? </span></p>",
                    options_en: [
                        " <p><span style=\"font-family:Times New Roman\">MEXICO</span><span style=\"font-family:Times New Roman\">      </span></p>",
                        " <p><span style=\"font-family:Times New Roman\">MERCURY</span></p>",
                        " <p><span style=\"font-family:Times New Roman\">JAPAN    </span></p>",
                        " <p><span style=\"font-family:Times New Roman\">MIDNIGHT</span></p>"
                    ],
                    options_hi: [
                        "<p><span style=\"font-family: Times New Roman;\">MEXICO</span></p>",
                        "<p><span style=\"font-family: Times New Roman;\">MERCURY</span></p>",
                        "<p><span style=\"font-family: Times New Roman;\">JAPAN </span></p>",
                        "<p><span style=\"font-family: Times New Roman;\">MIDNIGHT</span></p>"
                    ],
                    solution_en: " <p>(a)</span><span style=\"font-family:Times New Roman\"> Logic is : +1 , -1  rule is used.</span></p> <p><span style=\"font-family:Times New Roman\">So, L</span><span style=\"font-family:Times New Roman\"> +1 = M, F -1 = E, W +1 = X, J -1 = I, B +1 = C and P</span><span style=\"font-family:Times New Roman\"> -1 = O.</span></p> <p><span style=\"font-family:Times New Roman\">So, MEXICO is the correct answer.</span></p>",
                    solution_hi: "<p>(a)<span style=\"font-family: Times New Roman;\"> तर्क है : +1, -1 नियम का प्रयोग किया जाता है।</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">इसलिए,L</span><span style=\"font-family: Times New Roman;\"> +1 = M, F -1 = E, W +1 = X, J -1 = I, B +1 = C और&nbsp; P</span><span style=\"font-family: Times New Roman;\"> -1 = O.</span></p>\r\n<p>तो, MEXICO सही उत्तर है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "20",
                    section: "6",
                    question_en: "<p>20. Select the option that is related to the third number in the same way as the second number is related to the first number.</p>\r\n<p><span style=\"font-family: Times New Roman;\">343 : 729 :: 125 : __ </span></p>",
                    question_hi: "<p><span style=\"font-family: Baloo;\">20. उस विकल्प का चयन करें जो तीसरी संख्या से उसी तरह से संबंधित है जैसे दूसरी संख्या पहली संख्या से संबंधित है?</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">343 : 729 :: 125 : __</span></p>",
                    options_en: [
                        "<p><span style=\"font-family: Times New Roman;\">27</span></p>",
                        "<p><span style=\"font-family: Times New Roman;\">343</span></p>",
                        "<p><span style=\"font-family: Times New Roman;\">216</span></p>",
                        "<p><span style=\"font-family: Times New Roman;\">512</span></p>"
                    ],
                    options_hi: [
                        "<p>27</p>",
                        "<p>343</p>",
                        "<p>216</p>",
                        "<p>512</p>"
                    ],
                    solution_en: "<p><span style=\"font-family: Times New Roman;\">(b)<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#160;</mo><mi>L</mi><mi>o</mi><mi>g</mi><mi>i</mi><mi>c</mi><mo>&#160;</mo><mi>i</mi><mi>s</mi><mo>&#160;</mo><mo>:</mo><mo>&#160;</mo><msup><mi>n</mi><mn>3</mn></msup><mo>&#160;</mo><mo>:</mo><msup><mrow><mo>(</mo><mi>n</mi><mo>+</mo><mn>2</mn><mo>)</mo></mrow><mn>3</mn></msup><mo>&#160;</mo><mo>&#160;</mo><mspace linebreak=\"newline\"/><mn>125</mn><mo>&#160;</mo><mi>i</mi><mi>s</mi><mo>&#160;</mo><msup><mn>5</mn><mn>3</mn></msup><mo>&#160;</mo><mi>s</mi><mi>o</mi><mo>,</mo><mo>&#160;</mo><msup><mn>7</mn><mn>3</mn></msup><mo>&#160;</mo><mi>i</mi><mi>s</mi><mo>&#160;</mo><mo>&#160;</mo><mn>343</mn></math></span></p>",
                    solution_hi: "<p><span style=\"font-family: Times New Roman;\">(b)<math style=\"font-family:Verdana\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#160;</mo><mi>&#2340;&#2352;&#2381;&#2325;</mi><mo>&#160;</mo><mi>&#2361;&#2376;</mi><mo>&#160;</mo><mo>:</mo><mo>&#160;</mo><msup><mi>n</mi><mn>3</mn></msup><mo>&#160;</mo><mo>:</mo><msup><mrow><mo>(</mo><mi>n</mi><mo>+</mo><mn>2</mn><mo>)</mo></mrow><mn>3</mn></msup><mo>&#160;</mo><mo>&#160;</mo><mspace linebreak=\"newline\"/><mn>125</mn><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><msup><mn>5</mn><mn>3</mn></msup><mo>&#160;</mo><mi>&#2311;&#2360;&#2354;&#2367;&#2319;</mi><mo>,</mo><mo>&#160;</mo><msup><mn>7</mn><mn>3</mn></msup><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mo>&#160;</mo><mn>343</mn><mo>&#160;</mo><mi>&#2361;&#2379;&#2327;&#2366;</mi><mo>&#160;</mo></math></span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "21",
                    section: "6",
                    question_en: " <p>21. Three of the following four letter-clusters are alike in a certain way and one is different. Find the odd one out. ",
                    question_hi: "<p>21. निम्नलिखित चार अक्षर-समूहों में से तीन एक निश्चित तरीके से एक जैसे हैं और एक अलग है। विजातीय को चुनें।</p>",
                    options_en: [
                        " <p> M H Ansari  </span></p>",
                        " <p> Abdul Kalam  </span></p>",
                        " <p> Pranab Mukherjee  </span></p>",
                        " <p> Dr. Rajendra Prasad</span></p>"
                    ],
                    options_hi: [
                        "<p><span style=\"font-family: Baloo;\">एम. एच अंसारी</span></p>",
                        "<p><span style=\"font-family: Baloo;\">अब्दुल कलाम</span></p>",
                        "<p><span style=\"font-family: Baloo;\">प्रणब मुखर्जी</span></p>",
                        "<p><span style=\"font-family: Baloo;\"> डॉ राजेंद्र प्रसाद </span></p>"
                    ],
                    solution_en: " <p>(a)</span><span style=\"font-family:Times New Roman\">  Except M.H.Ansari (who is a Vice President of India), </span><span style=\"font-family:Times New Roman\">rest</span><span style=\"font-family:Times New Roman\"> all are Ex-Presidents of India.</span></p>",
                    solution_hi: "<p>(a)<span style=\"font-family: Times New Roman;\"> एम एच अंसारी (जो भारत के उपराष्ट्रपति हैं) को छोड़कर बाकी सभी भारत के पूर्व राष्ट्रपति हैं।</span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "22",
                    section: "6",
                    question_en: "<p>22. Choose that set of numbers from the four alternative sets, that is similar to the given set.</p>\r\n<p><span style=\"font-family: Times New Roman;\">Given set : (3, 18, 36) </span></p>",
                    question_hi: "<p><span style=\"font-family: Baloo;\">22. उस समूह का चयन करें जिसमें संख्याओं के बीच ठीक वही संबंध है जो संबंध नीचे दिए गए समूह की संख्याओं में है |</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Given set : (3, 18, 36)</span></p>",
                    options_en: [
                        "<p>(2, 10, 16)</p>",
                        "<p>(4, 24, 48)</p>",
                        "<p>(6, 42, 48)</p>",
                        "<p><span style=\"font-weight: 400;\">(12, 72, 96)</span></p>"
                    ],
                    options_hi: [
                        "<p><span style=\"font-family: Times New Roman;\">(2, 10, 16) </span></p>",
                        "<p><span style=\"font-family: Times New Roman;\">(4, 24, 48) </span></p>",
                        "<p><span style=\"font-family: Times New Roman;\">(6, 42, 48) </span></p>",
                        "<p><span style=\"font-weight: 400;\">(12, 72, 96)</span></p>"
                    ],
                    solution_en: "<p><span style=\"font-family: Times New Roman;\">(</span><span style=\"font-family: Times New Roman;\">b) Logic is : n : 6n : 12n</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">So, 4, 6<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#215;</mo></math></span><span style=\"font-family: Times New Roman;\">4 = 24 and 12<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#215;</mo></math></span><span style=\"font-family: Times New Roman;\">4 = 48</span></p>",
                    solution_hi: "<p><span style=\"font-family: Times New Roman;\">(</span><span style=\"font-family: Times New Roman;\">b) तर्क है : n : 6n : 12n</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">इसलिए4, 6<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#215;</mo></math></span><span style=\"font-family: Times New Roman;\">4 = 24 और&nbsp;12<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#215;</mo></math></span><span style=\"font-family: Times New Roman;\">4 = 48</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "23",
                    section: "6",
                    question_en: " <p>23. Arrange the following words in a logical sequence. </span></p> <p><span style=\"font-family:Times New Roman\">1. Gold  2. Iron   3. Sand  4. Platinum    5. Diamond  ",
                    question_hi: "<p><span style=\"font-family: Baloo;\">23. निम्नलिखित शब्दों को एक अर्थपूर्ण क्रम में व्यवस्थित करें | </span></p>\r\n<p><span style=\"font-family: Baloo;\">1.सोना 2.लोहा 3.बालू 4.प्लैटिनम 5.हीरा </span></p>",
                    options_en: [
                        " <p> 2, 4, 3, 5, 1</span></p>",
                        " <p> 3, 2, 1, 4, 5</span></p>",
                        " <p> 4, 5, 1, 3, 2</span></p>",
                        " <p> 3, 2, 1, 5, 4 </span></p>"
                    ],
                    options_hi: [
                        "<p><span style=\"font-family: Times New Roman;\">2, 4, 3, 5, 1</span></p>",
                        "<p><span style=\"font-family: Times New Roman;\">3, 2, 1, 4, 5</span></p>",
                        "<p><span style=\"font-family: Times New Roman;\">4, 5, 1, 3, 2</span></p>",
                        "<p><span style=\"font-family: Times New Roman;\">3, 2, 1, 5, 4 </span></p>"
                    ],
                    solution_en: " <p><span style=\"font-family:Times New Roman\">(b) Arranging in terms of increasing cost is :</span></p> <p><span style=\"font-family:Times New Roman\">Sand : Iron : Gold : Platinum : Diamond</span></p>",
                    solution_hi: "<p><span style=\"font-family: Times New Roman;\">(b)&nbsp;</span></p>\r\n<p>बढ़ती लागत के क्रम में व्यवस्थित करने पर&nbsp;</p>\r\n<p>रेत : लोहा : सोना : प्लेटिनम : हीरा</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "24",
                    section: "6",
                    question_en: " <p>24. Which option will replace the question mark (?) in the following </span><span style=\"font-family:Times New Roman\">series?</span></p> <p><span style=\"font-family:Times New Roman\">SCD, TEF, UGH, ?, WKL   ",
                    question_hi: "<p><span style=\"font-family: Baloo;\">24. नीचे दी गयी श्रृंखला में प्रश्न चिन्ह ( ? ) के स्थान पर कौन सा विकल्प आएगा ? </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">SCD, TEF, UGH, ?, WKL</span></p>",
                    options_en: [
                        " <p> CMN</span></p>",
                        " <p> UJI</span><span style=\"font-family:Times New Roman\">       </span></p>",
                        " <p> VIJ</span><span style=\"font-family:Times New Roman\">       </span></p>",
                        " <p> IJT</span></p>"
                    ],
                    options_hi: [
                        "<p><span style=\"font-family: Times New Roman;\">CMN</span><span style=\"font-family: Times New Roman;\"> </span></p>",
                        "<p><span style=\"font-family: Times New Roman;\">UJI</span></p>",
                        "<p><span style=\"font-family: Times New Roman;\">VIJ</span></p>",
                        "<p><span style=\"font-family: Times New Roman;\">IJT</span></p>"
                    ],
                    solution_en: " <p><span style=\"font-family:Times New Roman\">(c)  Logic is : +1, +2, +2 rule is used.</span></p> <p><span style=\"font-family:Times New Roman\">So, U +1 = V, G +2 = I and  H +2 = J</span></p> <p><span style=\"font-family:Times New Roman\">So, VIJ is the correct answer.</span></p>",
                    solution_hi: "<p><span style=\"font-family: Times New Roman;\">(c) तर्क है: +1, +2, +2 नियम का प्रयोग किया जाता है।</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">इसलिए U +1 = V, G +2 = I और&nbsp; H +2 = J</span></p>\r\n<p>इसलिए , VIJ सही उत्तर है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "25",
                    section: "misc",
                    question_en: "<p>25. select the one in which the question figure is hidden/embedded.</p>\n<p><span style=\"font-family: Times New Roman;\"> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1626691090/word/media/image6.png\"><span style=\"font-family: Times New Roman;\"> </span></p>",
                    question_hi: "<p><span style=\"font-family: Baloo;\">25.उस एक का चयन करें जिसमें प्रश्न की आकृति अंतर्निहित/ छिपी हुई है|</span></p>\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1626691090/word/media/image6.png\"></p>",
                    options_en: [
                        "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1626691090/word/media/image12.png\"></p>",
                        "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1626691090/word/media/image5.png\"></p>",
                        "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1626691090/word/media/image16.png\"></p>",
                        "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1626691090/word/media/image7.png\"></p>"
                    ],
                    options_hi: [
                        "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1626691090/word/media/image12.png\"></p>",
                        "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1626691090/word/media/image5.png\"></p>",
                        "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1626691090/word/media/image16.png\"></p>",
                        "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1626691090/word/media/image7.png\"></p>"
                    ],
                    solution_en: "<p><span style=\"font-family: Times New Roman;\">(b) </span></p>\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1626691090/word/media/image5.png\"></p>",
                    solution_hi: "<p><span style=\"font-family: Times New Roman;\">(b) </span></p>\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1626691090/word/media/image5.png\"></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'question-box d-flex align-items-center justify-content-center border rounded';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.question-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML with proper newline handling
            const html = `
                <div class="card shadow-sm mb-4">
                    <div class="card-body">
                        <div class="question-text mb-4">
                            <div class="en preserve-breaks">${question.question_en}</div>
                            <div class="hi preserve-breaks" style="display:none">${question.question_hi}</div>
                        </div>
                        <div class="options">
                            ${['a', 'b', 'c', 'd'].map((opt, i) => `
                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="radio" name="q${index}" 
                                        id="opt-${index}-${opt}" value="${opt}" 
                                        ${answers[question.id] === opt ? 'checked' : ''}
                                        onclick="selectAnswer('${question.id}', '${opt}')">
                                    <label class="form-check-label w-100 py-2 px-3 rounded hover-bg-light" for="opt-${index}-${opt}">
                                        <div class="en preserve-breaks">${question.options_en[i]}</div>
                                        <div class="hi preserve-breaks" style="display:none">${question.options_hi[i]}</div>
                                    </label>
                                </div>
                            `).join('')}
                        </div>
                        ${submitted ? `
                            <div class="alert ${answers[question.id] === question.correct ? 'alert-success' : 'alert-danger'} mt-3">
                                ${answers[question.id] ? (answers[question.id] === question.correct ? 'Correct!' : 'Incorrect!') : 'Not attempted'}
                                <div class="mt-2">
                                    <strong>Solution:</strong>
                                    <div class="en preserve-breaks">${question.solution_en}</div>
                                    <div class="hi preserve-breaks" style="display:none">${question.solution_hi}</div>
                                </div>
                            </div>
                        ` : ''}
                    </div>
                </div>
            `;
            document.getElementById('questions-container').innerHTML = html;
            // Apply MathJax to new content
            if (typeof MathJax !== 'undefined') {
                MathJax.typesetPromise && MathJax.typesetPromise();
            }
            // Apply language settings
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // If in mobile view, close the menu
            if (window.innerWidth < 768) {
                toggleMenu(false);
            }
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                document.getElementById(`box-${index}`).classList.add('attempted');
            }
        }
        // Navigate to previous question
        function prevQuestion() {
            if (currentQuestion > 0) {
                showQuestion(currentQuestion - 1);
            }
        }
        // Navigate to next question
        function nextQuestion() {
            if (currentQuestion < questions.length - 1) {
                showQuestion(currentQuestion + 1);
            }
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) {
                showQuestion(section.start);
            }
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update button text
            document.getElementById('lang-btn').innerHTML = currentLang === 'en' ? 
                '<i class="fas fa-language"></i> हिंदी' : 
                '<i class="fas fa-language"></i> English';
        }
        // Toggle menu for mobile
        function toggleMenu(show) {
            const nav = document.getElementById('question-nav');
            const content = document.getElementById('question-content');
            if (show === undefined) {
                show = nav.style.right === '-100%' || nav.style.right === '';
            }
            if (show) {
                nav.style.right = '0';
                if (window.innerWidth >= 768) {
                    content.style.marginRight = 'var(--nav-width)';
                }
            } else {
                nav.style.right = window.innerWidth < 768 ? '-100%' : '0';
                content.style.marginRight = window.innerWidth < 768 ? '0' : 'var(--nav-width)';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
            document.getElementById('total-count').textContent = questions.length;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.remove('attempted');
                    box.classList.add('bg-secondary', 'text-white');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.remove('attempted');
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.remove('attempted');
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            resultsModal.show();
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check-circle me-1"></i> Submitted';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            resultsModal.hide();
            showQuestion(0);
        }
    </script>
</body>
</html>