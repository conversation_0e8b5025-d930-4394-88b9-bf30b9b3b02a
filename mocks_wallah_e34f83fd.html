<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">8:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 15</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">15</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 8 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["18"] = {
                name: "General Awareness",
                start: 0,
                end: 13
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="18">General Awareness</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 14,
                end: 14
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "18",
                    question_en: "<p>1. Which of the following shows a symbiotic relationship?</p>",
                    question_hi: "<p>1. निम्नलिखित में से कौन सहजीवी संबंध दर्शाता है</p>",
                    options_en: ["<p>Funaria</p>", "<p>Lichen</p>", 
                                "<p>Marsilea</p>", "<p>Ulothrix</p>"],
                    options_hi: ["<p>फनेरिया</p>", "<p>लाइकेन</p>",
                                "<p>मार्सिलिया</p>", "<p>उलोथ्रिक</p>"],
                    solution_en: "<p>1.(b)<strong> lichen. Symbiotic relationship:</strong> A relationship where organisms live together and share shelter and nutrients. Example - The fungus provides shelter, water and minerals to the alga and the alga provides food to the fungus which it prepares by photosynthesis.</p>",
                    solution_hi: "<p>1.(b) <strong>लाइकेन। सहजीवी संबंध:</strong> एक ऐसा संबंध जहां जीव एक साथ रहते हैं और आश्रय और पोषक तत्व साझा करते हैं। उदाहरण - कवक शैवाल को आश्रय, जल और खनिज प्रदान करता है और शैवाल कवक को भोजन प्रदान करता है जिसे वह प्रकाश संश्लेषण द्वारा तैयार करता है।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "2",
                    section: "18",
                    question_en: "<p>2. Lichen is an organism which monitors</p>",
                    question_hi: "<p>2. लाइकेन एक ऐसा जीव है जो__________ की निगरानी करता है।</p>",
                    options_en: ["<p>water pollution</p>", "<p>gas pollution</p>", 
                                "<p>air pollution</p>", "<p>soil pollution</p>"],
                    options_hi: ["<p>जल प्रदूषण</p>", "<p>गैस प्रदूषण</p>",
                                "<p>वायु प्रदूषण</p>", "<p>मृदा प्रदूषण</p>"],
                    solution_en: "<p>2.(c) <strong>Air pollution. Lichens</strong> (Bioindicators) - They are useful bioindicators for air pollution (especially sulfur dioxide pollution) because they obtain their water and essential nutrients primarily from the atmosphere, not soil. <strong>Bioindicators </strong>- They are organisms used to assess the health and pollution levels of an ecosystem, reflecting its overall environmental quality.</p>",
                    solution_hi: "<p>2.(c) <strong>वायु प्रदूषण।</strong> लाइकेन (जैव संकेतक) - ये वायु प्रदूषण (विशेष रूप से सल्फर डाइऑक्साइड प्रदूषण) के लिए उपयोगी जैव संकेतक हैं क्योंकि वे अपना पानी और आवश्यक पोषक तत्व मुख्य रूप से वायुमंडल से प्राप्त करते हैं, मिट्टी से नहीं। <strong>बायोइंडिकेटर(जैव संकेतक) -</strong> वे जीव हैं जिनका उपयोग किसी पारिस्थितिकी तंत्र के स्वास्थ्य और प्रदूषण के स्तर का आकलन करने के लिए किया जाता है, जो संपूर्ण पर्यावरणीय गुणवत्ता को दर्शाता है।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "3",
                    section: "18",
                    question_en: "<p>3. Which of the following plants belongs to Bryophyta?</p>",
                    question_hi: "<p>3. निम्नलिखित में से कौन सा पौधा ब्रायोफाइटा का है?</p>",
                    options_en: ["<p>Deodar</p>", "<p>Marchantia</p>", 
                                "<p>Marsilea</p>", "<p>Cladophora</p>"],
                    options_hi: ["<p>देवदार</p>", "<p>मर्चेंटिया</p>",
                                "<p>मार्सिलिया</p>", "<p>क्लैडोफोरा</p>"],
                    solution_en: "<p>3.(b) <strong>Marchantia. Bryophytes -</strong> The taxonomic division containing three groups of non-vascular land plants: the liverworts, hornworts and mosses. They characteristically prefer moist habitats. The 5 classifications in the plant kingdom are thallophyta, bryophyta, pteridophyta, gymnosperms, and angiosperms. <strong>Deodar </strong>- Gymnosperms. <strong>Marsilea </strong>- Pteridophyta. <strong>Cladophora </strong>- Algae.</p>",
                    solution_hi: "<p>3.(b) <strong>मर्चेंटिया। ब्रायोफाइट्स </strong>- वर्गीकरण प्रभाग जिसमें गैर-संवहनी भूमि पौधों के तीन समूह शामिल हैं: लिवरवॉर्ट्स, हॉर्नवॉर्ट्स और मॉस। वे विशेष रूप से नम आवास पसंद करते हैं। पादप साम्राज्य में 5 वर्गीकरण हैं थैलोफाइटा, ब्रायोफाइटा, टेरिडोफाइटा, जिम्नोस्पर्म और एंजियोस्पर्म। <strong>देवदार </strong>- जिम्नोस्पर्म। <strong>मार्सिलिया </strong>- टेरिडोफाइटा। <strong>क्लैडोफोरा </strong>- शैवाल।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "4",
                    section: "18",
                    question_en: "<p>4. Which of the following medicinal plants can be used to treat blood pressure?</p>",
                    question_hi: "<p>4. निम्न में से किस औषधीय पौधे का उपयोग रक्तचाप के उपचार के लिए किया जा सकता है?</p>",
                    options_en: ["<p>Jamun</p>", "<p>Sarpagandha</p>", 
                                "<p>Tulsi</p>", "<p>Babool</p>"],
                    options_hi: ["<p>जामुन</p>", "<p>सर्पगंधा</p>",
                                "<p>तुलसी</p>", "<p>बबूल</p>"],
                    solution_en: "<p>4.(b) <strong>Sarpagandha </strong>is used to treat Blood pressure, Asthma, Insomnia. It is taken from the root of Rauvolfia serpentina plant or Indian snakeroot (Ayurvedic). They grow widely in the sub-Himalayan moist forests in Sikkim and Assam.<strong> Jamun fruits </strong>(good source of iron) are useful in the troubles of the heart and liver. <strong>Tulsi </strong>(Antimicrobial, Mosquito repellent, Anti-diarrheal, Anti-oxidant) is used to treat heart disease and fever. <strong>Babool Bark</strong> is helpful in the treatment of cold symptoms.</p>",
                    solution_hi: "<p>4.(b) <strong>सर्पगंधा </strong>का उपयोग रक्तचाप, अस्थमा, अनिद्रा के इलाज के लिए किया जाता है। इसे राउवोल्फिया सर्पेंटिना पौधे या भारतीय स्नैकरूट (आयुर्वेदिक) की जड़ से लिया जाता है। वे सिक्किम और असम में उप-हिमालयी नम जंगलों में व्यापक रूप से उगते हैं।<strong> जामुन के फल</strong> (आयरन का अच्छा स्रोत) हृदय और लीवर की समस्याओं में उपयोगी होते हैं। <strong>तुलसी </strong>(रोगाणुरोधी, मच्छर निरोधक, डायरिया रोधी, एंटी-ऑक्सीडेंट) का उपयोग हृदय रोग और बुखार के इलाज के लिए किया जाता है। <strong>बबूल की छाल </strong>सर्दी के लक्षणों के उपचार में सहायक है।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "5",
                    section: "18",
                    question_en: "<p>5. What is the scientiﬁc name of the \'Touch-Me-Not\' plant?</p>",
                    question_hi: "<p>5. छुईमुई के पौधे का वैज्ञानिक नाम क्या है?</p>",
                    options_en: ["<p>Mimosa townsendii</p>", "<p>Mimosa verrucosa</p>", 
                                "<p>Mimosa loxensis</p>", "<p>Mimosa pudica</p>"],
                    options_hi: ["<p>मिमोसा टाउनसेंडी (Mimosa townsendii)</p>", "<p>मिमोसा वेरुकोसा (Mimosa verrucosa)</p>",
                                "<p>मिमोसा लॉक्सेंसिस (Mimosa loxensis)</p>", "<p>मिमोसा पुडिका (Mimosa pudica)</p>"],
                    solution_en: "<p>5.(d) <strong>Mimosa Pudica.</strong> It helps in the treatment of many disorders like piles, dysentery, sinus, insomnia, diarrhea, alopecia and is also applied to cure wounds from ages. Some<strong> common plants with their Scientific Name -</strong> Banyan - Ficus benghalensis, Bamboo - Bamboosa aridinarifolia, Neem - Azadirachta Indica, Sandalwood - Santalum album, Tulsi - Ocimum sanctum.</p>",
                    solution_hi: "<p>5.(d) <strong>मिमोसा पुडिका</strong> (Mimosa pudica)। यह बवासीर, पेचिश, साइनस, अनिद्रा, दस्त, गंजापन (alopecia) जैसे कई विकारों के इलाज में मदद करता है और पुराने घावों को ठीक करने के लिए भी इसका उपयोग किया जाता है। कुछ <strong>सामान्य पौधे जिनके वैज्ञानिक नाम:- </strong>बरगद - फाइकस बेंघालेंसिस, बांस - बम्बूसा एरिडिनारिफोलिया, नीम - अज़ादिराक्टा इंडिका, चंदन - सैंटालम एल्बम, तुलसी - ओसीमम सैंक्टम।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "6",
                    section: "18",
                    question_en: "<p>6. Plants are divided into ﬁve groups. Which of the following is <strong>NOT </strong>one?</p>",
                    question_hi: "<p>6. पौधों को पांच समूहों में बांटा गया है। निम्नलिखित में से कौन सा इनमे से एक <strong>नहीं </strong>है?</p>",
                    options_en: ["<p>Bryophytes</p>", "<p>Gymnosperms</p>", 
                                "<p>Pteridophytes</p>", "<p>Protochordata</p>"],
                    options_hi: ["<p>ब्रायोफाइट</p>", "<p>जिम्नोस्पर्म</p>",
                                "<p>टेरिडोफाइट</p>", "<p>प्रोटोकॉर्डेटा</p>"],
                    solution_en: "<p>6.(d) <strong>Protochordata </strong>- refers to a subphylum of the animal kingdom, specifically including marine animals like tunicates, lancelets. Division of Plants: <strong>Thallophytes </strong>- characterized by the absence of true roots, stems, or leaves. Example - algae and fungi. <strong>Bryophytes </strong>- Non-vascular seedless plant. Example - Liverworts and Hornworts. <strong>Pteridophytes </strong>- Vascular plants that reproduce using spores. Example - Ferns and Horsetail. <strong>Gymnosperms </strong>- seed-producing plants that do not bear flowers. Example - Conifers trees. <strong>Angiosperms </strong>- seed-producing plants that produce flowers. Example - Roses and Broccoli.</p>",
                    solution_hi: "<p>6.(d) <strong>प्रोटोकॉर्डेटा </strong>- जन्तु जगत के एक उपसंघ को संदर्भित करता है, जिसमें विशेष रूप से समुद्री जानवर शामिल हैं, जैसे ट्यूनिकेट्स, लांसलेट्स। पादप का विभाजन: <strong>थैलोफाइट्स</strong> - मूल जड़ों, तनों या पत्तियों की अनुपस्थिति के रूप में वर्णित है। उदाहरण - शैवाल एवं कवक। <strong>ब्रायोफाइट्स</strong> - असंवहनी बीज रहित पौधा। उदाहरण - लिवरवॉर्ट्स और हॉर्नवॉर्ट्स। <strong>टेरिडोफाइट्स </strong>- संवहनी पौधे जो बीजाणुओं का उपयोग करके प्रजनन करते हैं। उदाहरण - फ़र्न और हॉर्सटेल। <strong>जिम्नोस्पर्म </strong>- बीज उत्पन्न करने वाले पौधे जिनमें फूल नहीं लगते। उदाहरण- शंकुधारी वृक्ष। <strong>एंजियोस्पर्म </strong>- बीज उत्पन्न करने वाले पौधे जिसमे फूल लगते हैं। उदाहरण - गुलाब और ब्रोकोली।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "7",
                    section: "18",
                    question_en: "<p>7. Sugarcane plants are one of the most efficient convertors of ____ into chemical energy.</p>",
                    question_hi: "<p>7. गन्ने के पौधे ________ को रासायनिक ऊर्जा में परिवर्तित करने वाले सबसे कुशल परिवर्तकों में से एक हैं।</p>",
                    options_en: ["<p>air</p>", "<p>Juice</p>", 
                                "<p>Sunlight</p>", "<p>water</p>"],
                    options_hi: ["<p>वायु</p>", "<p>जूस</p>",
                                "<p>सूर्यप्रकाश</p>", "<p>जल</p>"],
                    solution_en: "<p>7.(c) <strong>Sunlight. Photosynthesis</strong> is the biological process by which green plants, including sugarcane, use sunlight, carbon dioxide (from the air), and water (from the soil) to produce glucose and oxygen. Sugarcane (Saccharum officinarum) is a tall, perennial grass cultivated for its high sugar content in the stalks. It belongs to the <strong>grass family</strong> (Poaceae).</p>",
                    solution_hi: "<p>7.(c) <strong>सूर्यप्रकाश। प्रकाश संश्लेषण</strong> वह जैविक प्रक्रिया है जिसके द्वारा गन्ने सहित हरे पौधे ग्लूकोज और ऑक्सीजन का उत्पादन करने के लिए सूर्य के प्रकाश, कार्बन डाइऑक्साइड (हवा से) और पानी (मिट्टी से) का उपयोग करते हैं। गन्ना (सैकेरम ऑफ़िसिनारम) एक लंबी, बारहमासी घास है जिसकी खेती डंठल में अधिक चीनी सामग्री के कारण की जाती है। यह <strong>घास परिवार</strong> (Poaceae) से संबंधित है।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "8",
                    section: "18",
                    question_en: "<p>8. Which of the following plant groups bear naked seeds?</p>",
                    question_hi: "<p>8. निम्नलिखित में से किस पादप समूह में नग्न बीज होते हैं?</p>",
                    options_en: ["<p>Angiosperms</p>", "<p>Gymnosperms</p>", 
                                "<p>Pteridophytes</p>", "<p>Thallophytes</p>"],
                    options_hi: ["<p>आवृतबीजी</p>", "<p>अनावृतबीजी</p>",
                                "<p>टेरिडोफाइट</p>", "<p>थैलोफाइट</p>"],
                    solution_en: "<p>8.(b) <strong>Gymnosperms </strong>(Acrogymnospermae) are a group of seed-producing plants that includes conifers, cycads, ginkgo and gnetophytes. <strong>Thallophytes </strong>are plants that include the category of lichens, fungus, algae, and slime moulds. <strong>Pteridophytes </strong>are the most primitive vascular plants, having a simple reproductive system lacking flowers and seed. <strong>Angiosperms </strong>are vascular plants. They have stems, roots and leaves.</p>",
                    solution_hi: "<p>8.(b) <strong>अनावृतबीजी </strong>(एक्रोजिमनोस्पर्मे) बीज उत्पादक पौधों का एक समूह है जिसमें कॉनिफ़र, साइकैड, जिन्कगो और गनेटोफाइट्स शामिल हैं। <strong>थैलोफाइट्स </strong>वे पौधे हैं जिनमें लाइकेन, कवक, शैवाल और कीचड़ के सांचे की श्रेणी शामिल है। <strong>टेरिडोफाइट्स </strong>सबसे प्राचीन संवहनी पौधे हैं, जिनमें फूल और बीज की कमी वाली एक सरल प्रजनन प्रणाली होती है। <strong>आवृतबीजी </strong>संवहनी पौधे हैं। इनमें तने, जड़ें और पत्तियाँ होती हैं।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "9",
                    section: "18",
                    question_en: "<p>9. ___________ is a plant which grows in the wild and has stinging hair on its leaves which cause painful stings when touched accidentally.</p>",
                    question_hi: "<p>9. ___________ एक पौधा है जो जंगल में उगता है और इसकी पत्तियों पर चुभने वाले बाल होते हैं जो गलती से छूने पर दर्दनाक डंक मारते हैं।</p>",
                    options_en: ["<p>Nettle</p>", "<p>Pigweed</p>", 
                                "<p>Miner&rsquo;s Lettuce</p>", "<p>Clovers</p>"],
                    options_hi: ["<p>बिच्छू बूटी</p>", "<p>पिगवीड</p>",
                                "<p>खनिक का काहू</p>", "<p>तिपतिया घास</p>"],
                    solution_en: "<p>9.(a)<strong> Nettle. Pigweed</strong> (Family: Amaranthaceae) are tall, erect to bushy plants, oval to diamond shaped, alternate leaves. They emerge, grow, flower, set seed, and die within the frost-free growing season. <strong>Miner\'s lettuce</strong> (Claytonia perfoliata) - flowering plant, edible, fleshy, herbaceous native to western Mountain and Coastal regions of North America. <strong>Clovers </strong>(Genus Trifolium) - Short lived herbs usually with three toothed leaflets.</p>",
                    solution_hi: "<p>9.(a) <strong>बिच्छू बूटी।</strong> पिगवीड (परिवार: अमरैंथेसी) लंबे, सीधे झाड़ीदार पौधे, अंडाकार से लेकर हीरे के आकार के, वैकल्पिक पत्ते वाले होते हैं। वे उभरते हैं, बढ़ते हैं, फूलते हैं, बीज लगाते हैं, और पाले से मुक्त बढ़ते मौसम के भीतर मर जाते हैं। माइनर्स लेट्यूस (क्लेटोनिया परफोलिएटा) - उत्तरी अमेरिका के पश्चिमी पर्वतीय और तटीय क्षेत्रों का मूल निवासी फूलदार पौधा, खाने योग्य, मांसल, शाकाहारी। तिपतिया घास (जीनस ट्राइफोलियम) - आमतौर पर तीन दांतेदार पत्तों वाली अल्पकालिक जड़ी-बूटियाँ।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "10",
                    section: "18",
                    question_en: "<p>10. The movement of a sunflower facing the sun is called.</p>",
                    question_hi: "<p>10. सूर्य की ओर मुख किए हुए सूरजमुखी की गति को क्या कहा जाता है।</p>",
                    options_en: ["<p>Locomotion</p>", "<p>Rotation</p>", 
                                "<p>Phototropism</p>", "<p>Movement</p>"],
                    options_hi: ["<p>संचलन</p>", "<p>घूर्णन</p>",
                                "<p>प्रकाशानुवर्तन(Phototropism)</p>", "<p>दोलन</p>"],
                    solution_en: "<p>10.(c) <strong>Phototropism</strong>: Ability of a plant to grow in the direction of light. The plant\'s stem grows towards the sun, so that the flower head can always face the sun. This helps the plant to maximize its exposure to sunlight, which is necessary for photosynthesis. <strong>Charles Darwin</strong> studied phototropism in canary grass and oat coleoptiles in <strong>1880</strong>, and published his findings in the book The Power of Movement in Plants.</p>",
                    solution_hi: "<p>10.(c) <strong>प्रकाशानुवर्तन(Phototropism): </strong>किसी पौधे का प्रकाश की दिशा में बढ़ने की क्षमता। पौधे का तना सूर्य की ओर बढ़ता है, ताकि फूल का सिर हमेशा सूर्य की ओर रहे। इससे पौधे को सूर्य के प्रकाश के संपर्क में आने में मदद मिलती है जो प्रकाश संश्लेषण के लिए आवश्यक है। <strong>चार्ल्स डार्विन</strong> ने <strong>1880 </strong>में कैनरी घास और ओट कोलोप्टाइल्स में फोटोट्रोपिज्म का अध्ययन किया और अपने निष्कर्षों को &lsquo;द पावर ऑफ मूवमेंट इन प्लांट्स&rsquo; नामक पुस्तक में प्रकाशित किया।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "11",
                    section: "18",
                    question_en: "<p>11. The ephedra plant is grouped under:</p>",
                    question_hi: "<p>11. एफेड्रा के पौधे को _______ अधीन समूहीकृत किया गया है</p>",
                    options_en: ["<p>bryophytes</p>", "<p>gymnosperm</p>", 
                                "<p>pteridophytes</p>", "<p>angiosperm</p>"],
                    options_hi: ["<p>ब्रायोफाइट्स</p>", "<p>अनावृतबीजी</p>",
                                "<p>टेरिडोफाइट</p>", "<p>आवृतबीजी</p>"],
                    solution_en: "<p>11.(b) <strong>Gymnosperm </strong>(seed-producing plants). <strong>Ephedra</strong>: Its branches and tops are used to make medicine. <strong>Bryophytes</strong>: A taxonomic division containing three groups of non-vascular land plants: the liverworts, hornworts, and mosses. <strong>Pteridophytes</strong>: They are vascular plants that reproduce using spores. They do not produce flowers and seeds and hence are also known as cryptogams. <strong>Angiosperms </strong>are vascular plants with stems, roots, and leaves.</p>",
                    solution_hi: "<p>11.(b) <strong>अनावृतबीजी </strong>(बीज उत्पादक पौधे)। <strong>एफेड्रा</strong>: इसकी शाखाओं और शीर्षों का उपयोग औषधि बनाने में किया जाता है। <strong>ब्रायोफाइट्स</strong>: एक वर्गीकरण प्रभाग जिसमें गैर-संवहनी भूमि पौधों के तीन समूह शामिल हैं: लिवरवॉर्ट्स, हॉर्नवॉर्ट्स और मॉस। <strong>टेरिडोफाइट्स</strong>: वे संवहनी पौधे हैं जो बीजाणुओं का उपयोग करके प्रजनन करते हैं। ये फूल और बीज उत्पादित नहीं करते हैं और इसलिए इन्हें क्रिप्टोगैम के रूप में भी जाना जाता है। <strong>आवृतबीजी </strong>संवहनी पौधे हैं जिनमें तने, जड़ें और पत्तियां होती हैं।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "12",
                    section: "18",
                    question_en: "<p>12. _______ is NOT a carnivorous plant.</p>",
                    question_hi: "<p>12. _______ मांसाहारी पौधा नहीं है।</p>",
                    options_en: ["<p>Sundew</p>", "<p>Corkscrew</p>", 
                                "<p>Monkeycup</p>", "<p>Tiger Lily</p>"],
                    options_hi: ["<p>सनड्यू</p>", "<p>कॉर्कस्क्रू</p>",
                                "<p>मंकी कैप</p>", "<p>टाइगर लिली</p>"],
                    solution_en: "<p>12.(d) <strong>Tiger Lily. </strong>It is a herbaceous perennial with green leaves. Herbivorous plants - Plants that only eat vegetation such as grasses, fruits, leaves, vegetables, and roots. Example : Marine algae, deer. <strong>Carnivorous plants - </strong>Plants that derive some or most of their nutrients from trapping and consuming animals or protozoans, typically insects and other arthropods. Examples : Venus flytrap, Drosera, Pitcher plant, Waterwheel plant.</p>",
                    solution_hi: "<p>12.(d) <strong>टाइगर लिली</strong>। यह हरे पत्तों वाला एक शाकाहारी बारहमासी पौधा है।<strong> शाकाहारी पौधे -</strong> ऐसे पौधे जो केवल घास, फल, पत्तियाँ, सब्जियाँ और जड़ें जैसी वनस्पति ग्रहण करते हैं। <strong>उदाहरण </strong>: समुद्री शैवाल, हिरण। मांसाहारी पौधे - ऐसे पौधे जो अपने कुछ या अधिकांश पोषक तत्व जानवरों या प्रोटोजोआ, आमतौर पर कीड़े और अन्य आर्थ्रोपोड को फंसाने और खाने से प्राप्त करते हैं। <strong>उदाहरण</strong>: वीनस फ्लाईट्रैप, ड्रोसेरा, पिचर प्लांट, वॉटरव्हील प्लांट।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "13",
                    section: "18",
                    question_en: "<p>13. In which of the following plant groups are the seeds present inside the fruit?</p>",
                    question_hi: "<p>13. निम्नलिखित में से किस पौधे के समूह में फल के अंदर बीज मौजूद होते हैं?</p>",
                    options_en: ["<p>Gymnosperms</p>", "<p>Angiosperms</p>", 
                                "<p>Bryophytes</p>", "<p>Pteridophytes</p>"],
                    options_hi: ["<p>जिम्नोस्पर्म</p>", "<p>एंजियोस्पर्म</p>",
                                "<p>ब्रायोफाइट्स</p>", "<p>टेरिडोफाइट्स</p>"],
                    solution_en: "<p>13.(b) <strong>Angiosperms </strong>(Magnoliophyta). These are vascular plants which have stems, roots, and leaves and produce flowers and bear their seeds in fruits. Example: Rice, Corn, Wheat. <strong>Gymnosperms </strong>(naked seeds) - Woody trees, shrubs, and climbers. Example: Pinus, Sea grape, Ginkgo. Bryophytes - Small, non-vascular plants, such as mosses, liverworts and hornworts. Pteridophyte - A vascular plant (with xylem and phloem) that disperses spores.</p>",
                    solution_hi: "<p>13.(b) <strong>एंजियोस्पर्म </strong>(मैग्नोलियोफाइटा)। ये संवहनी पौधे हैं जिनमें तना, जड़ें और पत्तियां होती हैं और फूल उत्पन्न करते हैं और उनके बीज फल के रूप में धारण करते हैं। उदाहरण: चावल, मक्का, गेहूँ। <strong>जिम्नोस्पर्म </strong>(नग्न बीज) - लकड़ी के पेड़, झाड़ियाँ और लताएँ। उदाहरण: पाइनस, समुद्री अंगूर, जिन्कगो। ब्रायोफाइट्स - छोटे, गैर-संवहनी पौधे, जैसे मॉस, लिवरवॉर्ट्स और हॉर्नवॉर्ट्स। टेरिडोफाइट - एक संवहनी पौधा (जाइलम और फ्लोएम के साथ) जो बीजाणुओं को फैलाता है।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "14",
                    section: "18",
                    question_en: "<p>14. In which of the following is the plant body NOT differentiated into root, stem and leaves?</p>",
                    question_hi: "<p>14. निम्नलिखित में से किसमें पौधे का शरीर जड़, तना और पत्तियों में विभेदित नहीं होता है?</p>",
                    options_en: ["<p>Fern</p>", "<p>Thuja</p>", 
                                "<p>Spirogyra</p>", "<p>Marsilea</p>"],
                    options_hi: ["<p>फ़र्न</p>", "<p>थूजा</p>",
                                "<p>स्पाइरोगाइरा</p>", "<p>मार्सिलिया</p>"],
                    solution_en: "<p>14.(c) <strong>Spirogyra </strong>(water silk or pond silk) - Free-floating green algae present in freshwater habitats such as ponds, lakes. <strong>Ferns </strong>- Plants that do not have flowers, they generally reproduce by producing spores. <strong>Thuja </strong>- A genus of coniferous trees in the Cupressaceae. <strong>Marsilea </strong>- Belongs to Division Pteridophyta.</p>",
                    solution_hi: "<p>14.(c) <strong>स्पाइरोगाइरा </strong>(जल रेशम या तालाब रेशम) - तालाबों, झीलों जैसे मीठे पानी के उत्पत्तिस्थान में मौजूद मुक्त-तैरने वाले हरे शैवाल है। <strong>फ़र्न </strong>- ऐसे पौधे जिनमें फूल नहीं होते, वे आम तौर पर बीजाणु उत्पन्न करके प्रजनन करते हैं। <strong>थूजा </strong>- क्यूप्रेसेसी (Cupressaceae) में शंकुधारी पेड़ों की एक प्रजाति है । <strong>मार्सिलिया </strong>- टेरिडोफाइटा (Pteridophyta) प्रभाग से संबंधित है।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "15",
                    section: "misc",
                    question_en: "<p>15. Which of the following plants is NOT a thallophyte?</p>",
                    question_hi: "<p>15. निम्नलिखित में से कौन सा पौधा थैलोफाइट नहीं है?</p>",
                    options_en: ["<p>Ulva</p>", "<p>Funaria</p>", 
                                "<p>Chara</p>", "<p>Ulothrix</p>"],
                    options_hi: ["<p>उलवा</p>", "<p>फ्यूनेरिया</p>",
                                "<p>चरा</p>", "<p>यूलोथ्रिक्स</p>"],
                    solution_en: "<p>15.(b) <strong>Funaria</strong>: It is a moss. Family - Funariaceae. <strong>Thallophytes</strong>: Plants whose body is not differentiated into distinct roots, stems and leaves. <strong>Ulva </strong>(sea lettuce): It is an edible green alga in the family Ulvaceae. <strong>Chara</strong>: A fresh water, green alga found submerged in shallow water ponds, tanks, lakes and slow running water. <strong>Ulothrix</strong>: A genus of filamentous green algae (family Ulotrichaceae) found in marine and fresh waters.</p>",
                    solution_hi: "<p>15.(b) <strong>फ्यूनेरिया</strong>: यह एक प्रकार का शैवाल है। वर्ग - फ्यूनेरिया। <strong>थैलोफाइट्स</strong>: ऐसे पौधे जिनका शरीर अलग-अलग जड़ों, तनों और पत्तियों में विभेदित नहीं होता है। <strong>उलवा </strong>(समुद्री लेटुस ): यह उलवेसी वर्ग का खाद्य हरी शैवाल है। <strong>चरा </strong>: ताजे पानी का हरी शैवाल जो उथले पानी के तालाबों, टैंकों, झीलों और धीमी गति से बहते पानी में डूबा हुआ पाया जाता है। <strong>यूलोथ्रिक्स </strong>: ससमुद्री और ताजे पानी में पाए जाने वाले फिलामेंटस, हरे शैवाल (परिवार उलोट्रिचेसी) की एक प्रजाति है।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>