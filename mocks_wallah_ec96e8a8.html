<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">15:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 15</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">15</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 15 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 0,
                end: 13
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 14,
                end: 14
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "17",
                    question_en: "<p>1. A dishonest fruit-seller sells fruits at 20% profit but he uses 950 gm weight in place of 1 kg. What is the profit per cent?</p>",
                    question_hi: "<p>1. एक बेईमान फल-विक्रेता 20% लाभ पर फल बेचता है लेकिन वह 1 kg के स्थान पर 950 gm बाट(weight) का उपयोग करता है। लाभ प्रतिशत क्या है?</p>",
                    options_en: ["<p><math display=\"inline\"><mfrac><mrow><mn>500</mn></mrow><mrow><mn>17</mn></mrow></mfrac></math>%</p>", "<p><math display=\"inline\"><mfrac><mrow><mn>500</mn></mrow><mrow><mn>19</mn></mrow></mfrac></math>%</p>", 
                                "<p><math display=\"inline\"><mfrac><mrow><mn>500</mn></mrow><mrow><mn>32</mn></mrow></mfrac></math>%</p>", "<p><math display=\"inline\"><mfrac><mrow><mn>700</mn></mrow><mrow><mn>19</mn></mrow></mfrac></math>%</p>"],
                    options_hi: ["<p><math display=\"inline\"><mfrac><mrow><mn>500</mn></mrow><mrow><mn>17</mn></mrow></mfrac></math>%</p>", "<p><math display=\"inline\"><mfrac><mrow><mn>500</mn></mrow><mrow><mn>19</mn></mrow></mfrac></math>%</p>",
                                "<p><math display=\"inline\"><mfrac><mrow><mn>500</mn></mrow><mrow><mn>32</mn></mrow></mfrac></math>%</p>", "<p><math display=\"inline\"><mfrac><mrow><mn>700</mn></mrow><mrow><mn>19</mn></mrow></mfrac></math>%</p>"],
                    solution_en: "<p>1.(b) 20% = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math><br><math display=\"inline\"><mfrac><mrow><mi>S</mi><mi>P</mi><mi>&#160;</mi></mrow><mrow><mi>C</mi><mi>P</mi><mi>&#160;</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>6</mn><mn>5</mn></mfrac><mo>&#215;</mo><mfrac><mn>1000</mn><mn>950</mn></mfrac><mo>=</mo><mfrac><mn>600</mn><mn>475</mn></mfrac></math><br>profit percent = <math display=\"inline\"><mfrac><mrow><mn>600</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>475</mn><mi>&#160;</mi></mrow><mrow><mn>475</mn></mrow></mfrac></math> &times; 100<br>= <math display=\"inline\"><mfrac><mrow><mn>125</mn></mrow><mrow><mn>475</mn></mrow></mfrac></math> &times; 100 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>500</mn><mn>19</mn></mfrac></math>%</p>",
                    solution_hi: "<p>1.(b) 20% = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math><br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2357;&#2367;&#2325;&#2381;&#2352;&#2351;</mi><mo>&#160;</mo><mo>&#160;</mo><mi>&#2350;&#2370;&#2354;&#2381;&#2351;</mi><mo>&#160;</mo><mo>&#160;</mo><mo>&#160;</mo></mrow><mrow><mo>&#160;</mo><mi>&#2325;&#2381;&#2352;&#2351;</mi><mo>&#160;</mo><mo>&#160;</mo><mi>&#2350;&#2370;&#2354;&#2381;&#2351;</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>6</mn><mn>5</mn></mfrac><mo>&#215;</mo><mfrac><mn>1000</mn><mn>950</mn></mfrac><mo>=</mo><mfrac><mn>600</mn><mn>475</mn></mfrac></math><br>लाभ प्रतिशत = <math display=\"inline\"><mfrac><mrow><mn>600</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>475</mn><mi>&#160;</mi></mrow><mrow><mn>475</mn></mrow></mfrac></math> &times; 100<br>= <math display=\"inline\"><mfrac><mrow><mn>125</mn></mrow><mrow><mn>475</mn></mrow></mfrac></math> &times; 100 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>500</mn><mn>19</mn></mfrac></math>%</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "17",
                    question_en: "<p>2. If a shopkeeper sells sugar at ₹44.8 per kg, he is able to make a 12% profit. Due to water seepage, <math display=\"inline\"><mfrac><mrow><mn>1</mn><mi>&#160;</mi></mrow><mrow><mn>5</mn></mrow></mfrac></math> of the sugar is damaged. What should now be the selling price per kg of the rest of the sugar to have a 5% profit?</p>",
                    question_hi: "<p>2. यदि एक दुकानदार चीनी को ₹44.8 प्रति kg की दर से बेचता है, तो वह 12% लाभ अर्जित करता है। पानी के रिसाव के कारण चीनी का <math display=\"inline\"><mfrac><mrow><mn>1</mn><mi>&#160;</mi></mrow><mrow><mn>5</mn></mrow></mfrac></math> हिस्सा खराब हो जाता है। 5% लाभ प्राप्त करने के लिए शेष चीनी का प्रति किलोग्राम विक्रय मूल्य क्या होना चाहिए?</p>",
                    options_en: ["<p>₹49.5</p>", "<p>₹52.5</p>", 
                                "<p>₹48.5</p>", "<p>₹51.8</p>"],
                    options_hi: ["<p>₹49.5</p>", "<p>₹52.5</p>",
                                "<p>₹48.5</p>", "<p>₹51.8</p>"],
                    solution_en: "<p>2.(b) SP of 1 kg sugar = ₹44.8 <br>CP of 1 kg sugar = ₹44.8 &times; <math display=\"inline\"><mfrac><mrow><mn>100</mn></mrow><mrow><mn>112</mn></mrow></mfrac></math> = ₹40 <br>According to the question,<br>C.P.Remaining sugar = ₹40 &times; <math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> = ₹50 <br>New SP of 1 kg sugar after profit of 5% = ₹ 50 &times; <math display=\"inline\"><mfrac><mrow><mn>105</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = ₹52.5</p>",
                    solution_hi: "<p>2.(b) 1 किलो चीनी का विक्रय मूल्य = ₹44.8<br>1 किलो चीनी का क्रय मूल्य = ₹44.8 &times; <math display=\"inline\"><mfrac><mrow><mn>100</mn></mrow><mrow><mn>112</mn></mrow></mfrac></math> = ₹40 <br>प्रश्न के अनुसार,<br>शेष चीनी का क्रय मूल्य = ₹40 &times; <math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> = ₹50 <br>5% लाभ के बाद 1 किलो चीनी का नया विक्रय मूल्य = ₹ 50 &times; <math display=\"inline\"><mfrac><mrow><mn>105</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = ₹52.5</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "17",
                    question_en: "<p>3. A shopkeeper marked the price 20% more than its cost price. If he allows a discount of 30%, then find his loss percent.</p>",
                    question_hi: "<p>3. एक दुकानदार अपने क्रय मूल्य से 20% अधिक मूल्य अंकित करता है। यदि वह 30% की छूट देता है, तो उसका हानि प्रतिशत ज्ञात कीजिए।</p>",
                    options_en: ["<p>20%</p>", "<p>16%</p>", 
                                "<p>25%</p>", "<p>15%</p>"],
                    options_hi: ["<p>20%</p>", "<p>16%</p>",
                                "<p>25%</p>", "<p>15%</p>"],
                    solution_en: "<p>3.(b) Let the CP of the article be 100<br>MP of the article = 100 &times; <math display=\"inline\"><mfrac><mrow><mn>120</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 120 <br>SP of the article = 120 &times; <math display=\"inline\"><mfrac><mrow><mn>70</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 84<br>Loss % = <math display=\"inline\"><mfrac><mrow><mn>100</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>84</mn><mi>&#160;</mi></mrow><mrow><mn>100</mn></mrow></mfrac></math> &times; 100 = 16 %</p>",
                    solution_hi: "<p>3.(b) माना , वस्तु का क्रय मूल्य = 100<br>वस्तु का अंकित मूल्य = 100 &times; <math display=\"inline\"><mfrac><mrow><mn>120</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 120 <br>वस्तु का विक्रय मूल्य= 120 &times; <math display=\"inline\"><mfrac><mrow><mn>70</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 84<br>हानि % = <math display=\"inline\"><mfrac><mrow><mn>100</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>84</mn><mi>&#160;</mi></mrow><mrow><mn>100</mn></mrow></mfrac></math> &times; 100 = 16 %</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "17",
                    question_en: "<p>4. In a bank, 6% interest is given on the deposited amount. The bank then invests the money in the stock market and is able to earn ₹30,000 for every ₹3,00,000. How much money is the bank earning finally if the total amount deposited in the bank is ₹60,00,000?</p>",
                    question_hi: "<p>4. एक बैंक में जमा राशि पर 6% ब्याज दिया जाता है। बैंक फिर शेयर बाजार में पैसा निवेश करता है और प्रत्येक ₹3,00,000 पर ₹30,000 अर्जित करता है। यदि बैंक में कुल जमा राशि ₹60,00,000 है, तो बैंक अंत में कितना लाभ अर्जित कर रहा है?</p>",
                    options_en: ["<p>₹3,00,000</p>", "<p>₹6,40,000</p>", 
                                "<p>₹2,40,000</p>", "<p>₹3,60,000</p>"],
                    options_hi: ["<p>₹3,00,000</p>", "<p>₹6,40,000</p>",
                                "<p>₹2,40,000</p>", "<p>₹3,60,000</p>"],
                    solution_en: "<p>4.(c) Interest gained by bank on deposited amount = <math display=\"inline\"><mfrac><mrow><mn>30000</mn></mrow><mrow><mn>300000</mn></mrow></mfrac></math> &times; 100 = 10%<br>Bank give by bank on deposited amount = 6%<br>Required profit to earn by Bank = 6000000 &times; (10 - 6)% = ₹240000</p>",
                    solution_hi: "<p>4.(c) जमा राशि पर बैंक द्वारा प्राप्त ब्याज = <math display=\"inline\"><mfrac><mrow><mn>30000</mn></mrow><mrow><mn>300000</mn></mrow></mfrac></math> &times; 100 = 10%<br>जमा राशि पर बैंक द्वारा दिया जाने वाला शुल्क = 6%<br>बैंक द्वारा अर्जित करने के लिए आवश्यक लाभ = 6000000 &times; (10 - 6)% = ₹240000</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "17",
                    question_en: "<p>5. Ajay spends 66<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math>% of his income on different household items. If his income increases by 25% and expenditure increase by 20%, then the effect on his savings will be:</p>",
                    question_hi: "<p>5. अजय अपनी आय का 66<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math>% विभिन्न घरेलू मदों पर खर्च करता है। यदि उसकी आय 25% बढ़ जाती है और व्यय 20% बढ़ जाता है, तो उसकी बचत पर कितना प्रभाव पड़ेगा?</p>",
                    options_en: ["<p>28%</p>", "<p>61%</p>", 
                                "<p>49%</p>", "<p>35%</p>"],
                    options_hi: ["<p>28%</p>", "<p>61%</p>",
                                "<p>49%</p>", "<p>35%</p>"],
                    solution_en: "<p>5.(d) 66<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math>% = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>3</mn></mfrac></math><br>Let the initial income of Ajay be 300<br>Ratio -&nbsp; &nbsp; &nbsp; initial&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp; final <br>Income -&nbsp; &nbsp;300&nbsp; &nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp; 375<br>Expendi. - 200&nbsp; &nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp; 240<br>--------------------------------------<br>Savings - 100&nbsp; &nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp; 135<br>Effect on saving (increased) = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>135</mn><mo>-</mo><mn>100</mn></mrow><mn>100</mn></mfrac></math> &times; 100 = 35%</p>",
                    solution_hi: "<p>5.(d) 66<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math>% = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>3</mn></mfrac></math><br>माना अजय की प्रारंभिक आय 300 है<br>अनुपात -&nbsp; &nbsp; &nbsp; प्रारंभिक&nbsp; &nbsp; :&nbsp; &nbsp; अंतिम<br>आय -&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;300&nbsp; &nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp;375<br>व्यय -&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;200&nbsp; &nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp;240<br>------------------------------------------<br>बचत -&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;100&nbsp; &nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp;135<br>बचत पर प्रभाव (बढ़ा हुआ) = <math display=\"inline\"><mfrac><mrow><mn>135</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>100</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> &times; 100 = 35%</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "17",
                    question_en: "<p>6. A shopkeeper sold <math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math> of his articles at a gain of 20% and the remaining at the cost price. What is his gain percentage in the whole transaction?</p>",
                    question_hi: "<p>6. एक दुकानदार ने अपनी <math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math> वस्तुओं को 20% के लाभ पर और शेष को क्रय मूल्य पर बेचा। पूरे सौदे में उसका लाभ प्रतिशत कितना है?</p>",
                    options_en: ["<p>16<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> %</p>", "<p>14<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> %</p>", 
                                "<p>13<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> %</p>", "<p>12<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math>%</p>"],
                    options_hi: ["<p>16<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> %</p>", "<p>14<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> %</p>",
                                "<p>13<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> %</p>", "<p>12<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math>%</p>"],
                    solution_en: "<p>6.(d) Let the total no. of article be 8 <br>According to question,<br><math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math> article sold At the profit of 20% = (8 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>8</mn></mfrac></math>) &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>6</mn><mn>5</mn></mfrac></math> = 6 units<br>SP of 5 articles = 6 units<br>Remaining article sold at the cost price = 3 units<br>SP of 3 articles = 3 units<br>Total SP of the article = 9 units<br>Required profit = <math display=\"inline\"><mfrac><mrow><mn>9</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>8</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math> &times; 100 = 12<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math>%</p>",
                    solution_hi: "<p>6.(d) माना वस्तुओं की कुल संख्या 8 हैं<br>प्रश्न के अनुसार,<br><math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math> वस्तुएँ 20% के लाभ पर बेची गईं = (8 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>8</mn></mfrac></math>) &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>6</mn><mn>5</mn></mfrac></math> = 6 इकाई<br>5 वस्तुओं का विक्रय मूल्य = 6 इकाई<br>शेष वस्तु लागत मूल्य पर बेची गई = 3 इकाई<br>3 वस्तुओं का विक्रय मूल्य = 3 इकाई<br>कुल वस्तुओं का विक्रय मूल्य = 9 इकाई<br>आवश्यक लाभ = <math display=\"inline\"><mfrac><mrow><mn>9</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>8</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math> &times; 100 = 12<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math>%</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "17",
                    question_en: "<p>7. Find the gain percentage, given that Sidhi sold her scooter for ₹71785 gaining <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math>th of the selling price.</p>",
                    question_hi: "<p>7. सिद्धि अपने स्कूटर को ₹71785 में बेचकर, विक्रय मूल्य के <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> भाग के बराबर लाभ अर्जित करती है। उसका लाभ प्रतिशत ज्ञात कीजिए।</p>",
                    options_en: ["<p>20%</p>", "<p>25%</p>", 
                                "<p>10%</p>", "<p>15%</p>"],
                    options_hi: ["<p>20%</p>", "<p>25%</p>",
                                "<p>10%</p>", "<p>15%</p>"],
                    solution_en: "<p>7.(b) SP of the scooter = ₹71785<br>Profit = 71785 &times; <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> = ₹14357<br>CP of the scooter = 71785 - 14357 = ₹ 57428<br>Now,<br>Profit % = <math display=\"inline\"><mfrac><mrow><mn>14357</mn></mrow><mrow><mn>57428</mn></mrow></mfrac></math> &times; 100 = 25 %<br><strong>Short trick:-</strong><br>Let S.P. = 5 unit and Profit = 1 unit<br>then C.P. = 4 unit<br>Profit % = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> &times; 100 = 25%</p>",
                    solution_hi: "<p>7.(b) स्कूटर का S.P = ₹71785<br>लाभ = 71785 &times; <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> = ₹14357<br>स्कूटर का C.P. = 71785 - 14357 = ₹ 57428<br>अब,<br>लाभ % = <math display=\"inline\"><mfrac><mrow><mn>14357</mn></mrow><mrow><mn>57428</mn></mrow></mfrac></math> &times; 100 = 25 %<br><strong>शॉर्ट ट्रिक:-</strong><br>माना S.P. = 5 इकाई और लाभ = 1 इकाई<br>फिर C.P. = 4 इकाई<br>लाभ % = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> &times; 100 = 25%</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "17",
                    question_en: "<p>8. From the given data, find the ratio of the cost prices of a shirt and a trouser.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731987451780.png\" alt=\"rId4\" width=\"217\" height=\"106\"></p>",
                    question_hi: "<p>8. दिए गए आँकड़ों से शर्ट और ट्राउज़र के क्रय मूल्य का अनुपात ज्ञात कीजिए।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731987451906.png\" alt=\"rId5\" width=\"191\" height=\"105\"></p>",
                    options_en: ["<p>2 : 1</p>", "<p>1 : 3</p>", 
                                "<p>3 : 4</p>", "<p>5 : 3</p>"],
                    options_hi: ["<p>2 : 1</p>", "<p>1 : 3</p>",
                                "<p>3 : 4</p>", "<p>5 : 3</p>"],
                    solution_en: "<p>8.(d) According to the question,<br>CP of the shirt = 1200 &times; <math display=\"inline\"><mfrac><mrow><mn>100</mn></mrow><mrow><mn>120</mn></mrow></mfrac></math> = ₹1000<br>CP of the trouser = 660 &times; <math display=\"inline\"><mfrac><mrow><mn>100</mn></mrow><mrow><mn>110</mn></mrow></mfrac></math> = ₹600<br>Required ratio = 1000 : 600 = 5 : 3</p>",
                    solution_hi: "<p>8.(d) प्रश्न के अनुसार,<br>शर्ट का C.P. = 1200 &times; <math display=\"inline\"><mfrac><mrow><mn>100</mn></mrow><mrow><mn>120</mn></mrow></mfrac></math> = ₹1000<br>ट्राउज़र का C.P. = 660 &times; <math display=\"inline\"><mfrac><mrow><mn>100</mn></mrow><mrow><mn>110</mn></mrow></mfrac></math> = ₹600<br>आवश्यक अनुपात = 1000 : 600 = 5 : 3</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "17",
                    question_en: "<p>9. A seller sold 19 cookies for ₹95 instead of 20 cookies in one kg, cheating a customer. What is the gain (in ₹) if the seller bought the cookies for ₹76?</p>",
                    question_hi: "<p>9. एक विक्रेता ने एक ग्राहक को धोखा देकर एक Kg में 20 कुकीज के बजाय 19 कुकीज देकर ₹95 में बेच दीं। यदि विक्रेता ने ₹76 में कुकीज़ खरीदीं, तो लाभ (₹ में) क्या होगा?</p>",
                    options_en: ["<p>28.2</p>", "<p>26.2</p>", 
                                "<p>22.8</p>", "<p>25.8</p>"],
                    options_hi: ["<p>28.2</p>", "<p>26.2</p>",
                                "<p>22.8</p>", "<p>25.8</p>"],
                    solution_en: "<p>9.(c) <br>CP of 20 cookies for seller = <math display=\"inline\"><mi>&#8377;</mi></math> 76<br>CP of 1 cookies for seller = <math display=\"inline\"><mi>&#8377;</mi></math> 3.8<br>SP of 19 cookies for seller = <math display=\"inline\"><mi>&#8377;</mi></math>95<br>Profit = 95 - 76 = <math display=\"inline\"><mi>&#8377;</mi></math>19<br>Total profit = 19 + 3.8 (remaining 1 cookies price ) = <math display=\"inline\"><mi>&#8377;</mi></math> 22.8</p>",
                    solution_hi: "<p>9.(c) <br>विक्रेता के लिए 20 कुकीज़ का क्रय० मू० = ₹ 76<br>विक्रेता के लिए 1 कुकीज़ का क्रय० मू० = ₹ 3.8<br>विक्रेता के लिए 19 कुकीज़ का वि० मू० = ₹95<br>लाभ = 95 - 76 = ₹19<br>कुल लाभ = 19 + 3.8 (शेष 1 कुकीज़ की कीमत) = ₹ 22.8</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "17",
                    question_en: "<p>10. A vendor started selling vegetables at ₹10 per kg, but couldn&rsquo;t find buyers at this rate. So he reduced the price to ₹7.2 per kg, but uses a faulty weight of 900 g instead of 1 kg. Find the percentage change in the actual price.</p>",
                    question_hi: "<p>10. एक विक्रेता ने ₹10 प्रति kg के हिसाब से सब्जियाँ बेचना शुरू किया, लेकिन इस दर पर खरीदार नहीं मिला। इसलिए उसने कीमत घटाकर ₹7.2 प्रति kg कर दी, लेकिन 1 kg के बजाय 900 g के दोषपूर्ण वजन का उपयोग किया। वास्तविक मूल्य में प्रतिशत परिवर्तन ज्ञात कीजिए।</p>",
                    options_en: ["<p>10%</p>", "<p>15%</p>", 
                                "<p>20%</p>", "<p>25%</p>"],
                    options_hi: ["<p>10%</p>", "<p>15%</p>",
                                "<p>20%</p>", "<p>25%</p>"],
                    solution_en: "<p>10.(c) <math display=\"inline\"><mfrac><mrow><mi>S</mi><mi>P</mi></mrow><mrow><mi>A</mi><mi>c</mi><mi>t</mi><mi>u</mi><mi>a</mi><mi>l</mi><mi>&#160;</mi><mi>P</mi><mi>r</mi><mi>i</mi><mi>c</mi><mi>e</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>7</mn><mo>.</mo><mn>2</mn></mrow><mn>10</mn></mfrac><mo>&#215;</mo><mfrac><mn>1000</mn><mn>900</mn></mfrac><mo>=</mo><mfrac><mn>72</mn><mrow><mn>90</mn><mo>&#160;</mo></mrow></mfrac></math><br>Percentage change in the actual price = <math display=\"inline\"><mfrac><mrow><mn>90</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>72</mn></mrow><mrow><mn>90</mn></mrow></mfrac></math> &times; 100 = 20%</p>",
                    solution_hi: "<p>10.(c) <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2357;&#2367;&#2325;&#2381;&#2352;&#2351;</mi><mo>&#160;</mo><mo>&#160;</mo><mi>&#2350;&#2370;&#2354;&#2381;&#2351;</mi></mrow><mrow><mo>&#160;</mo><mi>&#2357;&#2366;&#2360;&#2381;&#2340;&#2357;&#2367;&#2325;</mi><mo>&#160;</mo><mo>&#160;</mo><mi>&#2325;&#2368;&#2350;&#2340;</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>7</mn><mo>.</mo><mn>2</mn></mrow><mn>10</mn></mfrac><mo>&#215;</mo><mfrac><mn>1000</mn><mn>900</mn></mfrac><mo>=</mo><mfrac><mn>72</mn><mrow><mn>90</mn><mo>&#160;</mo></mrow></mfrac></math><br>वास्तविक कीमत में प्रतिशत परिवर्तन = <math display=\"inline\"><mfrac><mrow><mn>90</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>72</mn></mrow><mrow><mn>90</mn></mrow></mfrac></math> &times; 100 = 20%</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "17",
                    question_en: "<p>11. A shopkeeper sells rice at the rate of ₹44 per kg whose cost price is ₹40 per kg. Not satisfied with this, he tries to increase his profit by removing 200 grams of rice from each packet. What is the shopkeeper&rsquo;s overall gain percentage?</p>",
                    question_hi: "<p>11. एक दुकानदार ₹44 प्रति kg की दर से चावल बेचता है, जिसका क्रय मूल्य ₹40 प्रति kg है। इससे संतुष्ट नहीं होने पर वह हर पैकेट से 200 ग्राम चावल निकालकर अपना लाभ बढ़ाने की कोशिश करता है। दुकानदार के कुल लाभ प्रतिशत की गणना करें।</p>",
                    options_en: ["<p>35%</p>", "<p>37.5%</p>", 
                                "<p>37%</p>", "<p>35.5%</p>"],
                    options_hi: ["<p>35%</p>", "<p>37.5%</p>",
                                "<p>37%</p>", "<p>35.5%</p>"],
                    solution_en: "<p>11.(b) <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>S</mi><mi>P</mi><mi>&#160;</mi></mrow><mrow><mi>C</mi><mi>P</mi><mi>&#160;</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>44</mn><mn>40</mn></mfrac><mo>&#215;</mo><mfrac><mn>1000</mn><mn>800</mn></mfrac><mo>=</mo><mfrac><mn>44</mn><mn>32</mn></mfrac></math><br>overall gain percentage = <math display=\"inline\"><mfrac><mrow><mn>44</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>32</mn></mrow><mrow><mn>32</mn></mrow></mfrac></math> &times; 100 = 37.5%</p>",
                    solution_hi: "<p>11.(b) <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>S</mi><mi>P</mi><mi>&#160;</mi></mrow><mrow><mi>C</mi><mi>P</mi><mi>&#160;</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>44</mn><mn>40</mn></mfrac><mo>&#215;</mo><mfrac><mn>1000</mn><mn>800</mn></mfrac><mo>=</mo><mfrac><mn>44</mn><mn>32</mn></mfrac></math><br>कुल लाभ प्रतिशत = <math display=\"inline\"><mfrac><mrow><mn>44</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>32</mn></mrow><mrow><mn>32</mn></mrow></mfrac></math> &times; 100 = 37.5%</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "17",
                    question_en: "<p>12. Find the gain percentage, given that Anubha sold her scooter for <math display=\"inline\"><mi>&#8377;</mi></math>23358 gaining <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>6</mn></mfrac></math>th of the selling price.</p>",
                    question_hi: "<p>12. अनुभा अपने स्कूटर को ₹23358 में बेचकर, विक्रय मूल्य के <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>6</mn></mrow></mfrac></math> भाग के बराबर लाभ अर्जित करती है। उसका लाभ प्रतिशत ज्ञात कीजिए।</p>",
                    options_en: ["<p>25%</p>", "<p>35%</p>", 
                                "<p>20%</p>", "<p>30%</p>"],
                    options_hi: ["<p>25%</p>", "<p>35%</p>",
                                "<p>20%</p>", "<p>30%</p>"],
                    solution_en: "<p>12.(c) According to the question,<br>Selling price of a scooter = ₹ 23358<br>Gain = 23358 &times; <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>6</mn></mrow></mfrac></math> = ₹ 3893<br>Cost price a scooter = 23358 - 3893 = ₹ 19465<br>Gain% = <math display=\"inline\"><mfrac><mrow><mn>3893</mn></mrow><mrow><mn>19465</mn></mrow></mfrac></math> &times; 100 = 20 %</p>",
                    solution_hi: "<p>12.(c) प्रश्न के अनुसार,<br>एक स्कूटर का विक्रय मूल्य = ₹ 23358<br>लाभ = 23358 &times; <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>6</mn></mrow></mfrac></math> = ₹ 3893<br>एक स्कूटर का विक्रय मूल्य = 23358 - 3893 = ₹ 19465<br>लाभ % = <math display=\"inline\"><mfrac><mrow><mn>3893</mn></mrow><mrow><mn>19465</mn></mrow></mfrac></math> &times; 100 = 20 %</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "17",
                    question_en: "<p>13. The marked price of 55 items was equal to the cost price of 99 items. The selling price of 56 items was equal to the marked price of 35 items. Calculate the profit or loss percentage from the sale of each item.</p>",
                    question_hi: "<p>13. 55 वस्तुओं का अंकित मूल्य 99 वस्तुओं के क्रय मूल्य के बराबर था। 56 वस्तुओं का विक्रय मूल्य 35 वस्तुओं के अंकित मूल्य के बराबर था। प्रत्येक वस्तु की बिक्री से होने वाले लाभ या हानि प्रतिशत की गणना करें।</p>",
                    options_en: ["<p>15% profit</p>", "<p>12.25% profit</p>", 
                                "<p>12.5% profit</p>", "<p>12.5% loss</p>"],
                    options_hi: ["<p>15% लाभ</p>", "<p>12.25% लाभ</p>",
                                "<p>12.5% लाभ</p>", "<p>12.5% हानि</p>"],
                    solution_en: "<p>13.(c) MP of 55 items = CP of 99 items<br><math display=\"inline\"><mo>&#8658;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>M</mi><mi>P</mi></mrow><mrow><mi>C</mi><mi>P</mi></mrow></mfrac><mo>=</mo><mfrac><mn>99</mn><mn>55</mn></mfrac><mo>=</mo><mfrac><mn>9</mn><mn>5</mn></mfrac></math><br>SP of 56 items = MP of 35 items<br><math display=\"inline\"><mo>&#8658;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>S</mi><mi>P</mi></mrow><mrow><mi>M</mi><mi>P</mi></mrow></mfrac><mo>=</mo><mfrac><mn>35</mn><mn>56</mn></mfrac><mo>=</mo><mfrac><mn>5</mn><mn>8</mn></mfrac></math><br>Ratio <math display=\"inline\"><mo>&#8594;</mo></math> CP&nbsp; &nbsp; :&nbsp; &nbsp;MP&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp;SP<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;5&nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; 9&nbsp; &nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp; 9<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;8&nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; 8&nbsp; &nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp; 5 <br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;______________________<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 40&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; 72&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp; 45<br>Required profit % = <math display=\"inline\"><mfrac><mrow><mn>45</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>40</mn></mrow><mrow><mn>40</mn></mrow></mfrac></math> &times; 100 = 12.5%</p>",
                    solution_hi: "<p>13.(c) 55 वस्तुओं का अंकित मूल्य = 99 वस्तुओं का CP<br><math display=\"inline\"><mo>&#8658;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>M</mi><mi>P</mi></mrow><mrow><mi>C</mi><mi>P</mi></mrow></mfrac><mo>=</mo><mfrac><mn>99</mn><mn>55</mn></mfrac><mo>=</mo><mfrac><mn>9</mn><mn>5</mn></mfrac></math><br>56 वस्तुओं का विक्रय मूल्य = 35 वस्तुओं का अंकित मूल्य <br><math display=\"inline\"><mo>&#8658;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>S</mi><mi>P</mi></mrow><mrow><mi>M</mi><mi>P</mi></mrow></mfrac><mo>=</mo><mfrac><mn>35</mn><mn>56</mn></mfrac><mo>=</mo><mfrac><mn>5</mn><mn>8</mn></mfrac></math><br>अनुपात <math display=\"inline\"><mo>&#8594;</mo></math> क्रय मूल्य : अंकित मूल्य&nbsp; : विक्रय मूल्य<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;5&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;9&nbsp; &nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp; 9<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;8&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;8&nbsp; &nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp; &nbsp;5 <br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; ______________________<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;40&nbsp; &nbsp; :&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;72&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp; 45<br><br>आवश्यक लाभ % = <math display=\"inline\"><mfrac><mrow><mn>45</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>40</mn></mrow><mrow><mn>40</mn></mrow></mfrac></math> &times; 100 = 12.5%</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "17",
                    question_en: "<p>14. A customer pays ₹975 in instalments. The payment is done each month ₹5 less than the previous month. If the first instalment is ₹100, how much time will be taken to pay the entire amount?</p>",
                    question_hi: "<p>14. एक ग्राहक ₹975 का भुगतान किस्तों में करता है। हर महीने, पिछले महीने की तुलना में ₹5 कम का भुगतान किया जाता है। यदि पहली किस्त ₹100 है, तो पूरी राशि का भुगतान करने में कितना समय लगेगा?</p>",
                    options_en: ["<p>14 months</p>", "<p>15 months</p>", 
                                "<p>27 months</p>", "<p>26 months</p>"],
                    options_hi: ["<p>14 महीने</p>", "<p>15 महीने</p>",
                                "<p>27 महीने</p>", "<p>26 महीने</p>"],
                    solution_en: "<p>14.(b) According to the question,<br>100, 95, 90, 85, &hellip;&hellip;..nth term <br><math display=\"inline\"><msub><mrow><mi>s</mi></mrow><mrow><mi>n</mi></mrow></msub></math> = 975, a = 100 and d = - 5<br><math display=\"inline\"><mo>&#8658;</mo></math> s<sub>n</sub> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>n</mi><mn>2</mn></mfrac></math>[2a + (n - 1)d]<br><math display=\"inline\"><mo>&#8658;</mo></math> 975 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>n</mi><mn>2</mn></mfrac></math>[2 &times; 100 + (n - 1) - 5]<br><math display=\"inline\"><mo>&#8658;</mo></math> 1950 = 200n - 5n<sup>2</sup> + 5n<br><math display=\"inline\"><mo>&#8658;</mo></math> 5n<sup>2</sup> - 205n&nbsp; 1950 = 0<br><math display=\"inline\"><mo>&#8658;</mo></math> n<sup>2 </sup>- 41n + 390 = 0<br>On solving above equation, we get<br><math display=\"inline\"><mo>&#8658;</mo></math> n = 15 and 26<br>Now, if n = 26, the number of installments will become negative (after 20 installments), hence it is not possible. <br>Total installments = 15 months</p>",
                    solution_hi: "<p>14.(b) प्रश्न के अनुसार,<br>100, 95, 90, 85, &hellip;&hellip;..n वां पद<br><math display=\"inline\"><msub><mrow><mi>s</mi></mrow><mrow><mi>n</mi></mrow></msub></math> = 975, a = 100 and d = - 5<br><math display=\"inline\"><mo>&#8658;</mo></math> s<sub>n</sub> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>n</mi><mn>2</mn></mfrac></math>[2a + (n - 1)d]<br><math display=\"inline\"><mo>&#8658;</mo></math> 975 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>n</mi><mn>2</mn></mfrac></math>[2 &times; 100 + (n - 1) - 5]<br><math display=\"inline\"><mo>&#8658;</mo></math> 1950 = 200n - 5n<sup>2</sup> + 5n<br><math display=\"inline\"><mo>&#8658;</mo></math> 5n<sup>2</sup> - 205n&nbsp; 1950 = 0<br><math display=\"inline\"><mo>&#8658;</mo></math> n<sup>2 </sup>- 41n + 390 = 0<br>उपरोक्त समीकरण को हल करने पर, हमें प्राप्त होता है<br><math display=\"inline\"><mo>&#8658;</mo></math> n = 15 and 26<br>अब, यदि n = 26 है, तो किस्तों की संख्या ऋणात्मक हो जाएगी (20 किश्तों के बाद), इसलिए यह संभव नहीं है। <br>कुल किश्तें = 15 माह</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "misc",
                    question_en: "<p dir=\"ltr\">15. Find the gain percentage, given that Anubha sold her scooter for 31524 gaining <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>6</mn></mfrac></math>&nbsp;th of the selling price .&nbsp;</p>\n<p>&nbsp;</p>",
                    question_hi: "<p>15. अनुभा अपने स्कूटर को ₹31524 में बेचकर, विक्रय मूल्य के <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>6</mn></mrow></mfrac></math> भाग के बराबर लाभ अर्जित करती है। उसका लाभ प्रतिशत ज्ञात कीजिए।</p>",
                    options_en: ["<p>5%&nbsp;</p>", "<p>20%</p>", 
                                "<p>30%</p>", "<p>35%</p>"],
                    options_hi: ["<p>5%</p>", "<p>20%</p>",
                                "<p>30%</p>", "<p>35%</p>"],
                    solution_en: "<p>15.(b) Selling price of a scooter = 31524 <br>Gain = 31524 &times; <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>6</mn></mrow></mfrac></math> = 5254<br>CP of a scooter = 31524 - 5254 = 26270<br>Gain % = <math display=\"inline\"><mfrac><mrow><mn>5254</mn></mrow><mrow><mn>26270</mn></mrow></mfrac></math> &times; 100 = 20 %</p>",
                    solution_hi: "<p>15.(b) स्कूटर का विक्रय मूल्य = 31524 <br>लाभ = 31524 &times; <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>6</mn></mrow></mfrac></math> = 5254<br>एक स्कूटर का CP = 31524 - 5254 = 26270<br>लाभ % = <math display=\"inline\"><mfrac><mrow><mn>5254</mn></mrow><mrow><mn>26270</mn></mrow></mfrac></math> &times; 100 = 20 %</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>