<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">5:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 15</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">15</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 5 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["18"] = {
                name: "General Awareness",
                start: 0,
                end: 13
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="18">General Awareness</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 14,
                end: 14
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "18",
                    question_en: "<p>1. The northern plain of the Indian subcontinent has been formed by the interplay of the three major river systems along with their tributaries. Choose the odd one out of the options given.</p>",
                    question_hi: "<p>1. भारतीय उपमहाद्वीप के उत्तरी मैदान का निर्माण तीन प्रमुख नदी प्रणालियों और उनकी सहायक नदियों की परस्पर क्रिया से हुआ है। दिए गए विकल्पों में से किसी असंगत का चयन कीजिए।</p>",
                    options_en: ["<p>The Brahmaputra</p>", "<p>The Indus</p>", 
                                "<p>The Ganga</p>", "<p>The Godavari</p>"],
                    options_hi: ["<p>ब्रह्मपुत्र</p>", "<p>सिंधु</p>",
                                "<p>गंगा</p>", "<p>गोदावरी</p>"],
                    solution_en: "<p>1.(d) <strong>Godavari River </strong>(Dakshin Ganga) rises from Trimbakeshwar in the Nashik district of Maharashtra. It is the second-longest river in India and one of the country\'s oldest rivers. The Northern Plains of India include the Punjab Plain, formed by the Indus River and its tributaries, the Gangetic Plain between the Ghaggar and Teesta rivers, and the Brahmaputra Plain primarily in Assam.</p>",
                    solution_hi: "<p>1.(d) <strong>गोदावरी नदी</strong> (दक्षिण गंगा) महाराष्ट्र के नासिक जिले के त्र्यंबकेश्वर से निकलती है। यह भारत की दूसरी सबसे लंबी नदी है और देश की सबसे पुरानी नदियों में से एक है। भारत के उत्तरी मैदानों में सिंधु नदी और उसकी सहायक नदियों द्वारा निर्मित पंजाब का मैदान, घग्घर और तीस्ता नदियों के बीच गंगा का मैदान और मुख्य रूप से असम में ब्रह्मपुत्र का मैदान शामिल है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "18",
                    question_en: "<p>2. Which of the following statements is/are correct ?<br>i. The first green revolution led to over-exploitation of natural resources.<br>ii. The second Green Revolution was more focused on Southern India.<br>iii. The second green revolution ensured in achieving sustainable livelihood security.</p>",
                    question_hi: "<p>2. निम्नलिखित में से कौन-सा/से कथन सही है/हैं ?<br>1. प्रथम हरित क्रांति के कारण प्राकृतिक संसाधनों का अत्यधिक दोहन हुआ।<br>॥. द्वितीय हरित क्रांति दक्षिणी भारत पर अधिक केंद्रित थी।<br>iii. द्वितीय हरित क्रांति ने स्थायी आजीविका सुरक्षा प्राप्त करना सुनिश्चित किया।</p>",
                    options_en: ["<p>i and iii</p>", "<p>Only ii</p>", 
                                "<p>ii and iii</p>", "<p>i and ii</p>"],
                    options_hi: ["<p>i और iii</p>", "<p>केवल ii</p>",
                                "<p>ii और iii</p>", "<p>i और ii</p>"],
                    solution_en: "<p>2.(a)<strong> i and iii. </strong>The Second Green Revolution aims to promote sustainable agricultural growth across the entire country, focusing on eastern India, rainfed areas, and small and marginal farmers. It emphasizes climate-resilient agriculture while ensuring livelihood security.</p>",
                    solution_hi: "<p>2.(a) <strong>i और iii. </strong>दूसरी हरित क्रांति का उद्देश्य पूरे देश में सतत कृषि विकास को बढ़ावा देना है, जिसमें पूर्वी भारत, वर्षा आधारित क्षेत्र और छोटे और सीमांत किसानों पर ध्यान केंद्रित किया गया है। यह आजीविका सुरक्षा सुनिश्चित करते हुए जलवायु-अनुकूल कृषि पर जोर देता है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "18",
                    question_en: "<p>3. Which of the following is NOT a Kharif crop ?</p>",
                    question_hi: "<p>3. निम्नलिखित में से कौन सी खरीफ की फसल नहीं है ?</p>",
                    options_en: ["<p>Paddy</p>", "<p>Soybean</p>", 
                                "<p>Wheat</p>", "<p>Cotton<br><br></p>"],
                    options_hi: ["<p>चावल</p>", "<p>सोयाबीन</p>",
                                "<p>गेहूँ</p>", "<p>कपास</p>"],
                    solution_en: "<p>3.(c) <strong>Wheat. </strong>Rabi crops are sown in October or mid-November. Examples: Gram, pea, mustard and linseed. Kharif Crops: The crops which are sown in the rainy season. Examples: Paddy, maize, soyabean, groundnut and cotton.</p>",
                    solution_hi: "<p>3.(c)<strong> गेहूँ।</strong> रबी की फसलें अक्टूबर या मध्य नवंबर में बोई जाती हैं। उदाहरण: चना, मटर, सरसों और अलसी। खरीफ फसलें: वे फसलें जो वर्षा के मौसम में बोई जाती हैं। उदाहरण: धान, मक्का, सोयाबीन, मूंगफली और कपास।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "18",
                    question_en: "<p>4. Which of the following is NOT a tributary of the Ganga ?</p>",
                    question_hi: "<p>4. निम्नलिखित में से कौन-सी गंगा की सहायक नदी नहीं है ?</p>",
                    options_en: ["<p>Yamuna</p>", "<p>Gandak</p>", 
                                "<p>Kosi</p>", "<p>Lohit</p>"],
                    options_hi: ["<p>यमुना</p>", "<p>गंडक</p>",
                                "<p>कोसी</p>", "<p>लोहित</p>"],
                    solution_en: "<p>4.(d) <strong>Lohit</strong> is a tributary to the Brahmaputra River. Ganga river: It originates in the Gangotri Glacier in the Himalayas in the Uttarkashi district of Uttarakhand. Tributaries include: Yamuna, Son, Gomti, Ghaghra, Gandak, and Kosi rivers.</p>",
                    solution_hi: "<p>4.(d) <strong>लोहित </strong>ब्रह्मपुत्र नदी की एक सहायक नदी है। गंगा नदी: इसका उद्गम उत्तराखंड के उत्तरकाशी जिले में हिमालय के गंगोत्री ग्लेशियर से होता है। सहायक नदियों में शामिल हैं: यमुना, सोन, गोमती, घाघरा, गंडक और कोसी नदियाँ।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "18",
                    question_en: "<p>5. Who published the logistic equation model of population growth in 1838 ?</p>",
                    question_hi: "<p>5. 1838 में किसने जनसंख्या वृद्धि के लॉजिस्टिक समीकरण मॉडल को प्रकाशित किया था ?</p>",
                    options_en: ["<p>Howard Thomas Odum</p>", "<p>Alfred Russel Wallace</p>", 
                                "<p>Pierre Fran&ccedil;ois Verhulst</p>", "<p>George Evelyn Hutchinson</p>"],
                    options_hi: ["<p>हावर्ड थॉमस ओडुम (Howard Thomas Odum)</p>", "<p>अल्फ्रेड रसेल वॉलेस (Alfred Russel Wallace)</p>",
                                "<p>पियरे फ़्राँस्वा वेरहल्स्ट (Pierre Fran&ccedil;ois Verhulst)</p>", "<p>जॉर्ज एवलिन हचिंसन (George Evelyn Hutchinson)</p>"],
                    solution_en: "<p>5.(c) <strong>Pierre Fran&ccedil;ois Verhulst.</strong> He was a Belgian mathematician who earned a doctorate in number theory from the University of Ghent in 1825. He is renowned for the logistic growth model, which is widely applied in population growth modeling. This model, first proposed by Pierre-Fran&ccedil;ois Verhulst in 1838, states that the reproduction rate is proportional to both the existing population and the available resources, assuming all else is equal.</p>",
                    solution_hi: "<p>5.(c) <strong>पियरे फ़्राँस्वा वेरहल्स्ट (Pierre Fran&ccedil;ois Verhulst)। </strong>वे बेल्जियम के गणितज्ञ थे, जिन्होंने 1825 में गेन्ट विश्वविद्यालय से संख्या सिद्धांत में डॉक्टरेट की उपाधि प्राप्त की थी। वे लॉजिस्टिक ग्रोथ मॉडल के लिए प्रसिद्ध हैं, जिसका व्यापक रूप से जनसंख्या वृद्धि मॉडलिंग में उपयोग किया जाता है। यह मॉडल, जिसे सर्वप्रथम 1838 में पियरे फ़्राँस्वा वेरहल्स्ट ने प्रस्तावित किया था, जिसमें कहा गया था कि प्रजनन दर मौजूदा जनसंख्या और उपलब्ध संसाधनों दोनों के समानुपाती है, यह मानते हुए कि अन्य सभी समान है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "18",
                    question_en: "<p>6. As per the Census of India 2011, the increase in literacy rates for males and females, respectively, as compared to 2001 Census was:</p>",
                    question_hi: "<p>6. भारत की जनगणना 2011 के अनुसार, 2001 की जनगणना की तुलना में पुरुषों और महिलाओं की साक्षरता दर में क्रमशः कितनी वृद्धि हुई है ?</p>",
                    options_en: ["<p>3.88% and 13.79%</p>", "<p>5.88% and 12.79%</p>", 
                                "<p>6.88% and 11.79%</p>", "<p>4.88% and 10.79%</p>"],
                    options_hi: ["<p>3.88% और 13.79%</p>", "<p>5.88% और 12.79%</p>",
                                "<p>6.88% और 11.79%</p>", "<p>4.88% और 10.79%</p>"],
                    solution_en: "<p>6.(c) <strong>6.88% and 11.79%.</strong> Literacy is the ability to read and write with comprehension for those aged seven and above. According to the 2011 Census, India&rsquo;s overall literacy rate was 74.04%, which indeed represents an increase of approximately 14% from the previous census in 2001, with rural women showing the highest rise at 26%. Kerala had the highest literacy rate at 93.91%, and Bihar the lowest at 63.82%.</p>",
                    solution_hi: "<p>6.(c) <strong>6.88% और 11.79%. </strong>साक्षरता सात वर्ष या उससे अधिक आयु के बच्चों के लिए समझ के साथ पढ़ने और लिखने की क्षमता है। 2011 की जनगणना के अनुसार, भारत की कुल साक्षरता दर 74.04% थी, जो वास्तव में 2001 की पिछली जनगणना से लगभग 14% की वृद्धि दर्शाती है, जिसमें ग्रामीण महिलाओं में सर्वाधिक 26% की वृद्धि देखी गई। केरल में साक्षरता दर सबसे अधिक 93.91% थी, और बिहार में सबसे कम 63.82% थी।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "18",
                    question_en: "<p>7. What is the salinity of the Dead Sea (per litre of water) ?</p>",
                    question_hi: "<p>7. मृत सागर की लवणता (प्रति लीटर जल की) कितनी है ?</p>",
                    options_en: ["<p>440 grams</p>", "<p>240 grams</p>", 
                                "<p>340 grams</p>", "<p>390 grams</p>"],
                    options_hi: ["<p>440 ग्राम</p>", "<p>240 ग्राम</p>",
                                "<p>340 ग्राम</p>", "<p>390 ग्राम</p>"],
                    solution_en: "<p>7.(c)<strong> 340 grams. </strong>The Dead Sea has an extremely high salinity level, with approximately 340 grams of dissolved salts per liter of water. This is roughly 8-9 times saltier than regular seawater, which has an average salinity of around 35 grams per liter. No outlet: The Dead Sea has no outlet to the ocean, causing salts to accumulate. High evaporation: Water evaporates quickly due to the hot desert climate. Mineral-rich inflows: Rivers and streams flowing into the Dead Sea carry high concentrations of minerals.</p>",
                    solution_hi: "<p>7.(c) <strong>340 ग्राम। </strong>मृत सागर में लवणता का स्तर सर्वाधिक है, प्रति लीटर जल में लगभग 340 ग्राम घुलित लवण हैं। यह सामान्य समुद्री जल की तुलना में लगभग 8-9 गुना अधिक खारा है, जिसकी औसत लवणता लगभग 35 ग्राम प्रति लीटर है। कोई निकास नहीं: मृत सागर का समुद्र में कोई निकास नहीं है, जिससे लवण जमा हो जाते हैं। उच्च वाष्पीकरण: गर्म रेगिस्तानी जलवायु के कारण जल जल्दी वाष्पित हो जाता है। खनिज-समृद्ध अंतर्वाह: मृत सागर में प्रवाहित होने वाली नदियाँ और धाराएँ खनिजों की उच्च सांद्रता ले जाती हैं।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "18",
                    question_en: "<p>8. Which of the following is NOT a major climate control of a place ?</p>",
                    question_hi: "<p>8. निम्नलिखित में से कौन सा किसी स्थान का प्रमुख जलवायु नियंत्रण नहीं है ?</p>",
                    options_en: ["<p>Altitude</p>", "<p>Latitude</p>", 
                                "<p>Interior of the earth</p>", "<p>Ocean currents</p>"],
                    options_hi: ["<p>उन्नतांश</p>", "<p>अक्षांश</p>",
                                "<p>भूगर्भ</p>", "<p>महासागरीय धाराएँ</p>"],
                    solution_en: "<p>8.(c)<strong> Interior of the earth.</strong> Climate refers to the long-term weather patterns of a region, influenced by its atmospheric and environmental conditions. Six major factors control the climate of any place: latitude, altitude, pressure and wind systems, distance from the sea, continentality, ocean currents, and relief features.</p>",
                    solution_hi: "<p>8.(c) <strong>भूगर्भ।</strong> जलवायु किसी क्षेत्र के दीर्घकालिक मौसम प्रतिरूपों को संदर्भित करता है, जो उसके वायुमंडलीय और पर्यावरणीय स्थितियों से प्रभावित होता है। किसी भी स्थान की जलवायु को नियंत्रित करने वाले छह प्रमुख कारक हैं: अक्षांश, ऊँचाई, दाब और वायु प्रणाली, समुद्र से दूरी, महाद्वीपीयता, महासागरीय धाराएँ और राहत विशेषताएँ।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "18",
                    question_en: "<p>9. The Aryans lived in the land known as &lsquo;Sapta Sindhu&rsquo; (Land of the Seven Rivers). Which of the following was NOT a part of it ?</p>",
                    question_hi: "<p>9. आर्य उस भूमि पर रहते थे जिसे सप्त सिंधु (सात नदियों की भूमि) कहा जाता था। निम्नलिखित में से कौन-सा इसका भाग नहीं था ?</p>",
                    options_en: ["<p>Indus</p>", "<p>Shipra</p>", 
                                "<p>Ravi</p>", "<p>Jhelum</p>"],
                    options_hi: ["<p>सिंधु</p>", "<p>शिप्रा</p>",
                                "<p>रावी</p>", "<p>झेलम</p>"],
                    solution_en: "<p>9.(b) <strong>Shipra. </strong>The Shipra (Kshipra), a 195 km tributary of the Chambal River in Madhya Pradesh, flows across the Malwa Plateau, passing through Ujjain, Ratlam, and Mandsaur before joining the Chambal. The Sapta Sindhu, mentioned in Vedic literature, refers to the \"Land of Seven Rivers\" in Punjab, home to early Aryan settlements. The seven rivers include: Indus, Sutlej (Sutudri), Ravi (Parusni), Chenab (Asikni), Jhelum (Vitasta), Beas (Vipas), and Saraswati.</p>",
                    solution_hi: "<p>9.(b) <strong>शिप्रा। </strong>यह मध्य प्रदेश में चंबल नदी की 195 किलोमीटर लंबी सहायक नदी शिप्रा (क्षिप्रा) मालवा पठार से होकर गुजरती है, जो चंबल में मिलने से पहले उज्जैन, रतलाम और मंदसौर से होकर गुजरती है। वैदिक साहित्य में वर्णित सप्त सिंधु, पंजाब में \"सात नदियों की भूमि\" को संदर्भित करता है, जो प्रारंभिक आर्य बस्तियों का निवास स्थान है। सात नदियों में सिंधु, सतलुज (सुतुद्री), रावी (परुषणी), चिनाब (अस्किनी), झेलम (वितस्ता), ब्यास (विपाशा) और सरस्वती शामिल हैं।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "18",
                    question_en: "<p>10. By which National Waterways (NW) of India, are the delta channels of Mahanadi and Brahmani rivers and East Coast Canal connected ?</p>",
                    question_hi: "<p>10. भारत के किस राष्ट्रीय जलमार्ग (NW) द्वारा महानदी और ब्राह्मणी नदी के डेल्टा चैनल और पूर्वी तटीय चैनल जुड़े हुए हैं ?</p>",
                    options_en: ["<p>NW 1</p>", "<p>NW 3</p>", 
                                "<p>NW 2</p>", "<p>NW 5</p>"],
                    options_hi: ["<p>NW 1</p>", "<p>NW 3</p>",
                                "<p>NW 2</p>", "<p>NW 5</p>"],
                    solution_en: "<p>10.(d) <strong>NW 5.</strong> It connects the states of Odisha and West Bengal, stretching 623 km in total, with 91 km in West Bengal and 532 km in Odisha. National Waterway-1 (NW-1) spans the Ganga-Bhagirathi-Hooghly river system from Prayagraj, Uttar Pradesh, to Haldia, West Bengal. National Waterway-2 covers 891 km of the Brahmaputra River between the Bangladesh border near Dhubri and Sadiya in Assam. National Waterway-3, also known as the West Coast Canal, is a 168 km route in Kerala from Kollam to Kottapuram.</p>",
                    solution_hi: "<p>10.(d) <strong>NW 5. </strong>यह ओडिशा और पश्चिम बंगाल राज्यों को जोड़ता है, जिसका विस्तार कुल 623 किलोमीटर तक है, जिसमें पश्चिम बंगाल में 91 किलोमीटर और ओडिशा में 532 किलोमीटर शामिल हैं। राष्ट्रीय जलमार्ग-1 (NW-1) प्रयागराज, उत्तर प्रदेश, हल्दिया से पश्चिम बंगाल तक गंगा-भागीरथी-हुगली नदी प्रणाली को जोड़ता है। राष्ट्रीय जलमार्ग-2 धुबरी के पास बांग्लादेश सीमा और असम में सदिया के बीच ब्रह्मपुत्र नदी के 891 किलोमीटर हिस्से को क्षेत्ररक्षित करता है। राष्ट्रीय जलमार्ग-3, जिसे वेस्ट कोस्ट नहर के रूप में भी जाना जाता है, केरल में कोल्लम से कोट्टापुरम तक 168 किलोमीटर का मार्ग है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "18",
                    question_en: "<p>11. In which state of India is the Tadoba National Park located ?</p>",
                    question_hi: "<p>11. ताडोबा राष्ट्रीय उद्यान भारत के किस राज्य में स्थित है?</p>",
                    options_en: ["<p>Maharashtra</p>", "<p>Gujarat</p>", 
                                "<p>Madhya Pradesh</p>", "<p>Kerala</p>"],
                    options_hi: ["<p>महाराष्ट्र</p>", "<p>गुजरात</p>",
                                "<p>मध्य प्रदेश</p>", "<p>केरल</p>"],
                    solution_en: "<p>11.(a)<strong> Maharashtra.</strong> Tadoba National Park is located in the Chandrapur District. In 1955, it was given the designation of a National Park. Other National Parks of Maharashtra are Chandoli National Park, Navegaon National Park, Gugamal National Park, Sanjay Gandhi National Park. Gujarat - Gir National Park and Wildlife Sanctuary (Sasan Gir), Blackbuck National Park. Madhya Pradesh - Bandhavgarh National Park. Kerala - Silent Valley National Park, Anamudi Shola National Park.</p>",
                    solution_hi: "<p>11.(a) <strong>महाराष्ट्र। </strong>ताडोबा राष्ट्रीय उद्यान चंद्रपुर जिले में स्थित है। 1955 में इसे राष्ट्रीय उद्यान का दर्जा प्रदान किया गया था। महाराष्ट्र के अन्य राष्ट्रीय उद्यान चंदौली राष्ट्रीय उद्यान, नवेगांव राष्ट्रीय उद्यान, गुगामल राष्ट्रीय उद्यान, संजय गांधी राष्ट्रीय उद्यान। गुजरात - गिर राष्ट्रीय उद्यान और वन्यजीव अभयारण्य (सासन गिर), ब्लैकबक राष्ट्रीय उद्यान। मध्य प्रदेश - बांधवगढ़ राष्ट्रीय उद्यान। केरल - साइलेंट वैली राष्ट्रीय उद्यान, अनामुडी शोला राष्ट्रीय उद्यान।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "18",
                    question_en: "<p>12. According to the Census of India 2011, which group of Union Territories has the highest density of population ?</p>",
                    question_hi: "<p>12. भारत की जनगणना 2011 के अनुसार, केन्द्र शासित प्रदेशों के किस समूह का जनसंख्या घनत्व सर्वाधिक है ?</p>",
                    options_en: ["<p>Lakshadweep, Daman and Diu and Puducherry</p>", "<p>Delhi, Chandigarh and Daman and Diu</p>", 
                                "<p>Delhi, Chandigarh and Puducherry</p>", "<p>Lakshadweep, Daman and Diu and Chandigarh</p>"],
                    options_hi: ["<p>लक्षद्वीप, दमन और दीव तथा पुदुचेरी</p>", "<p>दिल्ली, चंडीगढ़ तथा दमन और दीव</p>",
                                "<p>दिल्ली, चंडीगढ़ और पुदुचेरी</p>", "<p>लक्षद्वीप, दमन और दीव तथा चंडीगढ़</p>"],
                    solution_en: "<p>12.(c)<strong> Delhi, Chandigarh and Puducherry.</strong> In 2011, the population density of India was 382 persons per square kilometer, with Arunachal Pradesh having the lowest density at 17 persons per square kilometer; Bihar had the highest density at 1,106 persons per square kilometer, followed by West Bengal (1028), Kerala (860), Uttar Pradesh (829), and Haryana (573).</p>",
                    solution_hi: "<p>12.(c) <strong>दिल्ली, चंडीगढ़ और पुदुचेरी।</strong> 2011 में, भारत का जनसंख्या घनत्व 382 व्यक्ति प्रति वर्ग किलोमीटर था, जिसमें अरुणाचल प्रदेश का घनत्व सबसे कम 17 व्यक्ति प्रति वर्ग किलोमीटर था; बिहार में सबसे अधिक 1,106 व्यक्ति प्रति वर्ग किलोमीटर घनत्व था, उसके बाद पश्चिम बंगाल (1028), केरल (860), उत्तर प्रदेश (829) और हरियाणा (573) का स्थान था।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "18",
                    question_en: "<p>13. Which of the following group of months is the coldest in North India ?</p>",
                    question_hi: "<p>13. निम्नलिखित में से कौन-से महीनों का समूह उत्तर भारत में सबसे ठंडा होता है ?</p>",
                    options_en: ["<p>February and March</p>", "<p>September and October</p>", 
                                "<p>December and January</p>", "<p>October and November</p>"],
                    options_hi: ["<p>फरवरी और मार्च</p>", "<p>सितंबर और अक्टूबर</p>",
                                "<p>दिसंबर और जनवरी</p>", "<p>अक्टूबर और नवंबर</p>"],
                    solution_en: "<p>13.(c) In <strong>December and January,</strong> temperatures decrease from south to north, with the Northern Plains averaging 10&deg;C to 15&deg;C, while Chennai on the eastern coast sees averages of 24&deg;C to 25&deg;C. Days are warm, and nights are cold during this period.</p>",
                    solution_hi: "<p>13.(c) <strong>दिसंबर और जनवरी </strong>में, दक्षिण से उत्तर की ओर तापमान कम हो जाता है, उत्तरी मैदानों में औसत तापमान 10&deg;C से 15&deg;C तक होता है, जबकि पूर्वी तट पर चेन्नई में औसत तापमान 24&deg;C से 25&deg;C तक होता है। इस अवधि के दौरान दिन गर्म और रातें ठंडी होती हैं।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "18",
                    question_en: "<p>14. Which ocean is encircling the continent of Antarctica and extends northward to 60 degrees south latitude ?</p>",
                    question_hi: "<p>14. कौन-सा महासागर अंटार्कटिका महाद्वीप को घेरे हुए है और उत्तर की ओर 60 डिग्री दक्षिण अक्षांश तक फैला हुआ है ?</p>",
                    options_en: ["<p>Atlantic Ocean</p>", "<p>Indian Ocean</p>", 
                                "<p>Pacific Ocean</p>", "<p>Southern Ocean</p>"],
                    options_hi: ["<p>अटलांटिक महासागर</p>", "<p>हिंद महासागर</p>",
                                "<p>प्रशांत महासागर</p>", "<p>दक्षिणी महासागर</p>"],
                    solution_en: "<p>14.(d) <strong>Southern Ocean.</strong> It is the newest of the world\'s five oceans, officially designated as a separate ocean by the International Hydrographic Organization (IHO) in 2000. The Atlantic Ocean is between the Americas and Europe/Africa. The Indian Ocean is between Africa, Asia, and Australia. The Pacific Ocean is between Asia and the Americas.</p>",
                    solution_hi: "<p>14.(d) <strong>दक्षिणी महासागर। </strong>यह दुनिया के पाँच महासागरों में सबसे नया है, जिसे आधिकारिक तौर पर 2000 में अंतर्राष्ट्रीय जल सर्वेक्षण संगठन (IHO) द्वारा एक अलग महासागर के रूप में नामित किया गया था। अटलांटिक महासागर अमेरिका और यूरोप/अफ्रीका के बीच है। हिंद महासागर अफ्रीका, एशिया और ऑस्ट्रेलिया के बीच है। प्रशांत महासागर एशिया और अमेरिका के बीच है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "misc",
                    question_en: "<p>15. About what percentage of passenger traffic is carried by roads in India ?</p>",
                    question_hi: "<p>15. भारत में लगभग कितने प्रतिशत यात्री यातायात सड़कों द्वारा किया जाता है ?</p>",
                    options_en: ["<p>77%</p>", "<p>87%</p>", 
                                "<p>57%</p>", "<p>67%</p>"],
                    options_hi: ["<p>77%</p>", "<p>87%</p>",
                                "<p>57%</p>", "<p>67%</p>"],
                    solution_en: "<p>15.(b) <strong>87%. </strong>The Road Transport Sector handles about 60% of freight traffic in India. For passenger traffic, roads account for 87%, railways for 12%, and both air and waterways contribute approximately 0.5% each, as per the Ministry of Road Transport and Highways.</p>",
                    solution_hi: "<p>15.(b) <strong>87%.</strong> सड़क परिवहन क्षेत्र भारत में माल ढुलाई का लगभग 60% संभालता है। सड़क परिवहन एवं राजमार्ग मंत्रालय के अनुसार, यात्री यातायात के लिए, सड़क का योगदान 87%, रेलवे का 12% और हवाई तथा जलमार्ग दोनों का योगदान लगभग 0.5% है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>