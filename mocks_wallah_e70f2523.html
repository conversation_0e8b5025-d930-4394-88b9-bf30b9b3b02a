<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">5:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 15</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">15</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 5 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["18"] = {
                name: "General Awareness",
                start: 0,
                end: 13
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="18">General Awareness</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 14,
                end: 14
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "18",
                    question_en: "<p>1. What challenge does foreign investment often face in India?</p>",
                    question_hi: "<p>1. भारत में विदेशी निवेश को अधिकतर किस चुनौती का सामना करना पड़ता है?</p>",
                    options_en: ["<p>Excessive foreign competition</p>", "<p>Inconsistent regulatory environment</p>", 
                                "<p>Lack of skilled labour</p>", "<p>Lack of consumer base</p>"],
                    options_hi: ["<p>अत्यधिक विदेशी प्रतिस्पर</p>", "<p>अस्थिर विनियामक परिवेश</p>",
                                "<p>कुशल श्रमिक अभाव</p>", "<p>उपभोक्ता आधार अभाव</p>"],
                    solution_en: "<p>1.(b) <strong>Inconsistent regulatory environment</strong> can lead to difficulties in compliance and planning for investors, ultimately affecting their confidence and willingness to invest in the country. Foreign investment refers to when an investor from one country purchases ownership of an asset or business in another country.</p>",
                    solution_hi: "<p>1.(b) <strong>अस्थिर विनियामक परिवेश</strong> निवेशकों के लिए अनुपालन और योजना बनाने में कठिनाइयों का कारण बन सकता है, जो अंततः उनके आत्मविश्वास और देश में निवेश करने की इच्छा को प्रभावित कर सकता है। विदेशी निवेश से तात्पर्य तब होता है जब एक देश का निवेशक किसी दूसरे देश में किसी परिसंपत्ति या व्यवसाय का स्वामित्व खरीदता है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "18",
                    question_en: "<p>2. ___________ is an industry association and self-regulatory organisation (SRO) whose primary objective is to work towards the robust development of the microfinance sector.</p>",
                    question_hi: "<p>2. ___________ एक उद्योग संघ और स्व-विनियामक संगठन (SRO) है जिसका प्राथमिक उद्देश्य सूक्ष्&zwj;मवित्&zwj;त क्षेत्र के सुदृढ़ विकास की दिशा में काम करना है।</p>",
                    options_en: ["<p>Self-help Group Association</p>", "<p>Microfinance Institutions Network</p>", 
                                "<p>Microfinance and Investments Regulatory Authority</p>", "<p>NABARD</p>"],
                    options_hi: ["<p>स्वयं सहायता समूह संघ</p>", "<p>सूक्ष्म वित्त संस्था नेटवर्क</p>",
                                "<p>सूक्ष्म वित्त एवं निवेश विनियामक प्राधिकरण</p>", "<p>नाबार्ड (NABARD)</p>"],
                    solution_en: "<p>2.(b) <strong>Microfinance Institutions Network</strong> (MFIN) was established in 2009. A microfinance institution is a provider of credit. However, the size of the loans are smaller than those granted by traditional banks. Reserve Bank of India (RBI) oversees microfinance institutions in India. MFIN examples Equitas Small Finance, ESAF Microfinance and Investments (P) Ltd, Fusion Microfinance Pvt Ltd, Annapurna Microfinance Pvt Ltd, BSS Microfinance Limited, Asirvad Microfinance Limited, Cashpor Micro Credit, Bandhan Financial Services Limited.</p>",
                    solution_hi: "<p>2.(b) <strong>माइक्रोफाइनेंस इंस्टीट्यूशंस नेटवर्क (MFIN) </strong>की स्थापना 2009 में की गई थी। माइक्रोफाइनेंस संस्थान ऋण प्रदाता है। हालाँकि, ऋणों का आकार पारंपरिक बैंकों द्वारा दिए जाने वाले ऋणों से छोटा होता है। भारतीय रिज़र्व बैंक (RBI) भारत में माइक्रोफाइनेंस संस्थानों की देखरेख करता है। MFIN के उदाहरण इक्विटास स्मॉल फाइनेंस, ESAF माइक्रोफाइनेंस एंड इन्वेस्टमेंट्स (P) लिमिटेड, फ्यूजन माइक्रोफाइनेंस प्राइवेट लिमिटेड, अन्नपूर्णा माइक्रोफाइनेंस प्राइवेट लिमिटेड, BSS माइक्रोफाइनेंस लिमिटेड, आशीर्वाद माइक्रोफाइनेंस लिमिटेड, कैशपोर माइक्रो क्रेडिट, बंधन फाइनेंशियल सर्विसेज लिमिटेड।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "18",
                    question_en: "<p>3. What is the relationship between interest rate and demand for money?</p>",
                    question_hi: "<p>3. ब्याज दर और मुद्रा की मांग के बीच क्या संबंध है?</p>",
                    options_en: ["<p>No relationship exists</p>", "<p>Inverse</p>", 
                                "<p>Direct</p>", "<p>Proportionate</p>"],
                    options_hi: ["<p>कोई संबंध नहीं है</p>", "<p>प्रतिलोम</p>",
                                "<p>प्रत्यक्ष</p>", "<p>समानुपातिक</p>"],
                    solution_en: "<p>3.(b) <strong>Inverse</strong>. Interest rate and demand for money have an inverse relationship, meaning that demand for money decreases as interest rates increase, and increases as interest rates decrease.</p>",
                    solution_hi: "<p>3.(b) <strong>प्रतिलोम </strong>: ब्याज दर और मुद्रा की मांग में प्रतिलोम संबंध होता है, जिसका अर्थ है कि ब्याज दरों में वृद्धि होने पर मुद्रा की मांग कम हो जाती है, और ब्याज दरों में कमी होने पर बढ़ जाती है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "18",
                    question_en: "4. Which of the following statements is correct ?",
                    question_hi: "4. निम्नलिखित कथनों में से कौन सही है ?",
                    options_en: [" The five-year plan focuses mostly on private intervention in the economy.  ", " The five-year plan always focused on the growth of the tertiary sector. ", 
                                " The five-year plan permits long-term intervention by the government in the economy.  ", " A five-year plan always stresses market-oriented activities. "],
                    options_hi: [" पंचवर्षीय योजना अर्थव्यवस्था में मुख्यतः निजी हस्तक्षेप पर केंद्रित है।", " पंचवर्षीय योजना सदैव तृतीयक क्षेत्र के विकास पर केंद्रित रहती है",
                                " पंचवर्षीय योजना सरकार को अर्थव्यवस्था में दीर्घकालिक हस्तक्षेप की सुविधा देती है।", " पंचवर्षीय योजना सदैव बाजार-उन्मुख गतिविधियों पर जोर देती है।"],
                    solution_en: "4.(c) The Five Year Plans were formulated, implemented and regulated by a body known as the Planning Commission. The National Development Council (NDC) was the final authority to approve Five Year Plans (FYPs). The Five-Year Plans in India were primarily focused on government intervention, not private intervention, although some plans did encourage private sector participation. It focused on the growth of all sectors depending on the needs of the economy.  Five-Year Plans were not always focused on market-oriented activities.  ",
                    solution_hi: "4.(c) पंचवर्षीय योजनाओं को योजना आयोग नामक निकाय द्वारा तैयार, कार्यान्वित और विनियमित किया जाता था। राष्ट्रीय विकास परिषद (NDC) पंचवर्षीय योजनाओं (FYP) को मंजूरी देने वाला अंतिम प्राधिकरण था। भारत में पंचवर्षीय योजनाएँ मुख्य रूप से सरकारी हस्तक्षेप पर केंद्रित थीं, न कि निजी हस्तक्षेप पर, हालाँकि कुछ योजनाओं ने निजी क्षेत्र की भागीदारी को प्रोत्साहित किया। इसने अर्थव्यवस्था की ज़रूरतों के आधार पर सभी क्षेत्रों के विकास पर ध्यान केंद्रित किया। पंचवर्षीय योजनाएँ हमेशा बाज़ार-उन्मुख गतिविधियों पर केंद्रित नहीं थीं। ",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "18",
                    question_en: "<p>5. Which of the following goods needs further transformation in the economic process?</p>",
                    question_hi: "<p>5. निम्नलिखित में से किस वस्तु को आर्थिक प्रक्रिया में और परिवर्तन की आवश्यकता है?</p>",
                    options_en: ["<p>Consumer durable goods</p>", "<p>Finished goods</p>", 
                                "<p>Intermediate goods</p>", "<p>Capital goods</p>"],
                    options_hi: ["<p>उपभोक्ता टिकाऊ माल</p>", "<p>अंतिम माल</p>",
                                "<p>मध्यवर्ती माल</p>", "<p>पूंजीगत माल</p>"],
                    solution_en: "<p>5.(c) <strong>Intermediate goods : </strong>The raw materials that a firm buys from another firm which are completely used up in the process of production. Consumer durable goods can be used repeatedly and have a long life. An item that is meant for final use and will not pass through any more stages of production or transformations is called a final good. The capital goods add to, or maintain, the capital stock of an economy and thus make production of other commodities possible.</p>",
                    solution_hi: "<p>5.(c) <strong>मध्यवर्ती माल :</strong> जिस कच्चे माल को एक फर्म दूसरी फर्म से खरीदती है और जो उत्पादन की प्रक्रिया में पूरी तरह से उपयोग हो जाता है। उपभोक्ता टिकाऊ वस्तुएं बार-बार उपयोग की जा सकती हैं और उनका जीवनकाल लंबा होता है। वह वस्तु जिसे अंतिम उपयोग के लिए बनाया गया है और जो उत्पादन या परिवर्तन के किसी और चरण से नहीं गुजरेगी, उसे अंतिम वस्तु कहा जाता है। पूंजीगत माल अर्थव्यवस्था के पूंजी स्टॉक को बढ़ाती हैं या बनाए रखती हैं तथा इस प्रकार अन्य वस्तुओं का उत्पादन संभव बनाती हैं।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "18",
                    question_en: "<p>6. Which Five-Year Plan primarily focused on the \'Garibi Hatao\' initiative?</p>",
                    question_hi: "<p>6. कौन-सी पंचवर्षीय योजना मुख्य रूप से \'गरीबी हटाओ\' पहल पर केंद्रित थी?</p>",
                    options_en: ["<p>Sixth Five-Year Plan</p>", "<p>Fourth Five-Year Plan</p>", 
                                "<p>Fifth Five-Year Plan</p>", "<p>Third Five-Year Plan</p>"],
                    options_hi: ["<p>छठी पंचवर्षीय योजना</p>", "<p>चौथी पंचवर्षीय योजना</p>",
                                "<p>पाँचवीं पंचवर्षीय योजना</p>", "<p>तीसरी पंचवर्षीय योजना</p>"],
                    solution_en: "<p>6.(c) <strong>Fifth Five-Year Plan. </strong>\"Garibi Hatao\" was a slogan coined by Indira Gandhi, aimed at poverty alleviation and job creation for a better life. The 5th Five Year Plan (1974-1979) had a targeted growth rate of 4.4% and achieved 4.8%. It was terminated a year early, in March 1978. The plan\'s final draft, prepared by D.P. Dhar, focused on eliminating poverty and achieving self-reliance.</p>",
                    solution_hi: "<p>6.(c) <strong>पाँचवीं पंचवर्षीय योजना।</strong> \"गरीबी हटाओ\" इंदिरा गांधी द्वारा दिया गया नारा था, जिसका उद्देश्य गरीबी उन्मूलन और बेहतर जीवन के लिए रोजगार सृजन करना था। 5वीं पंचवर्षीय योजना (1974-1979) में 4.4% की विकास दर का लक्ष्य रखा गया था और 4.8% हासिल किया गया। इसे एक वर्ष पहले, मार्च 1978 में समाप्त कर दिया गया था। डी.पी. धर द्वारा तैयार योजना का अंतिम मसौदा गरीबी को खत्म करने और आत्मनिर्भरता हासिल करने पर केंद्रित था।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "18",
                    question_en: "7. Which of the following is NOT correct with regards to the history of calculating National Income (NI) in India?",
                    question_hi: "7. भारत में राष्ट्रीय आय  (NI)की गणना के इतिहास के संबंध में निम्नलिखित में से कौन-सा सही नहीं है?",
                    options_en: [" First attempt to compute NI was made by Dadabhai Naoroji.", " First official attempt to compute NI was made by PC Mahalanobis. ", 
                                " Dadabhai Naoroji divided the Indian economy into two parts: primary sector and secondary sector. ", " First scientific method to compute NI was used by Dr VKRV Rao.<br /> "],
                    options_hi: ["  NI की गणना करने का पहला प्रयास दादाभाई नौरोजी ने किया था।", " NI की गणना करने का पहला आधिकारिक प्रयास पीसी महालनोबिस द्वारा किया गया था। ",
                                " दादाभाई नौरोजी ने भारतीय अर्थव्यवस्था को दो भागों में विभाजित किया: प्राथमिक क्षेत्र और द्वितीयक क्षेत्र। ", " NI की गणना करने के लिए पहली वैज्ञानिक पद्धति का उपयोग डॉ वीकेआरवी राव ने किया था।"],
                    solution_en: "7.(c) Dr. VKRV Rao in 1931, divided the Indian economy into two sectors: the agricultural sector and the corporate sector. The agricultural sector encompassed fishing, hunting, forests, and agriculture, while the corporate sector included industries, business, transport, construction, and public services. National income represents the total value of all goods and services produced in a country during a specific period. In 1867, Dadabhai Naoroji, known as the Grand Old Man of India, proposed the drain theory, also referred to as the \"drain of wealth\" theory.",
                    solution_hi: "7.(c) डॉ. वी.के.आर.वी. राव ने 1931 में भारतीय अर्थव्यवस्था को दो क्षेत्रों में विभाजित किया: कृषि क्षेत्र और कॉर्पोरेट क्षेत्र। कृषि क्षेत्र में मछली पकड़ना, शिकार करना, जंगल और कृषि शामिल थे, जबकि कॉर्पोरेट क्षेत्र में उद्योग, व्यवसाय, परिवहन, निर्माण और सार्वजनिक सेवाएँ शामिल थीं। राष्ट्रीय आय एक विशिष्ट अवधि के दौरान किसी देश में उत्पादित सभी वस्तुओं और सेवाओं के कुल मूल्य का प्रतिनिधित्व करती है। 1867 में, दादाभाई नौरोजी, जिन्हें भारत के ग्रैंड ओल्ड मैन के रूप में जाना जाता है, ने ड्रेन थ्योरी का प्रस्ताव रखा, जिसे \"धन की निकासी\" सिद्धांत भी कहा जाता है।",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "18",
                    question_en: "<p>8. The CSO adopted the concept of Gross Value Added in ___________ .</p>",
                    question_hi: "<p>8. CSO ने ___________ में सकल वर्धित मूल्य (Gross Value Added) की अवधारणा को अपनाया।</p>",
                    options_en: ["<p>January 2012</p>", "<p>January 2015</p>", 
                                "<p>January 2017</p>", "<p>January 2013</p>"],
                    options_hi: ["<p>जनवरी 2012</p>", "<p>जनवरी 2015</p>",
                                "<p>जनवरी 2017</p>", "<p>जनवरी 2013</p>"],
                    solution_en: "<p>8.(b) <strong>January 2015.</strong> Central Statistics Office (CSO) : The CSO is a government agency under the Ministry of Statistics and Programme Implementation. It coordinates statistical activities in India and maintains statistical standards. Gross Value Added (GVA) : GVA is a productivity metric that measures the contribution of a corporate subsidiary, company, sector, or region to the economy.</p>",
                    solution_hi: "<p>8.(b) <strong>जनवरी 2015</strong>. केंद्रीय सांख्यिकी कार्यालय (CSO): CSO सांख्यिकी और कार्यक्रम कार्यान्वयन मंत्रालय के तहत एक सरकारी एजेंसी है। यह भारत में सांख्यिकीय गतिविधियों का समन्वय करता है और सांख्यिकीय मानकों को बनाए रखता है। सकल मूल्य वर्धित (GVA): GVA एक उत्पादकता मीट्रिक है जो अर्थव्यवस्था में सहायक कॉर्पोरेट ,कंपनी, क्षेत्र के योगदान को मापता है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "18",
                    question_en: "<p>9. If assets of a bank are greater than liabilities, it will be recorded as:</p>",
                    question_hi: "<p>9. यदि किसी बैंक की परिसंपत्ति उसकी देनदारियों से अधिक है, तो इसे किस प्रकार दर्ज किया जाएगा ?</p>",
                    options_en: ["<p>liabilities</p>", "<p>net worth</p>", 
                                "<p>reserves</p>", "<p>assets</p>"],
                    options_hi: ["<p>देनदारियां</p>", "<p>शुद्ध संपत्ति (नेट वर्थ)</p>",
                                "<p>रिज़र्व</p>", "<p>परिसंपत्तियां</p>"],
                    solution_en: "<p>9.(b) <strong>net worth. </strong>The amount by which the value of the assets exceed the liabilities is the net worth (equity) of the business. The net worth reflects the amount of ownership of the business by the owners. The formula for computing net worth is: Assets - Liabilities = Net Worth.</p>",
                    solution_hi: "<p>9.(b) <strong>शुद्ध संपत्ति (नेट वर्थ)। </strong>वह राशि जिससे परिसंपत्तियों का मूल्य देनदारियों से अधिक होता है, व्यवसाय का नेट वर्थ (इक्विटी) है। नेट वर्थ मालिकों द्वारा व्यवसाय के स्वामित्व की मात्रा को दर्शाता है। नेट वर्थ की गणना करने का सूत्र है: परिसंपत्तियाँ - देनदारियाँ = नेट वर्थ।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "18",
                    question_en: "<p>10. When was the Duty-Free Tariff Preference (DFTP) Scheme for Least Developed Countries (LDCs) announced by India?</p>",
                    question_hi: "<p>10. भारत द्वारा अल्प विकसित देशों (LDCs) के लिए शुल्क-मुक्त टैरिफ वरीयता (DFTP) योजना की घोषणा कब की गई थी?</p>",
                    options_en: ["<p>2010</p>", "<p>2009</p>", 
                                "<p>2006</p>", "<p>2008</p>"],
                    options_hi: ["<p>2010</p>", "<p>2009</p>",
                                "<p>2006</p>", "<p>2008</p>"],
                    solution_en: "<p>10.(d) <strong>2008</strong>. Duty Free Tariff Preference (DFTP) scheme of 2008 : Prime Minister Manmohan Singh announced the DFTP scheme at the India-Africa Forum Summit on April 8, 2008. India became the first developing country to extend this facility to Least Developed Countries (LDCs). Objective : To grant tariff preferences on the exports of the Least Developed Countries on imports to India.</p>",
                    solution_hi: "<p>10.(d) <strong>2008</strong>. शुल्क-मुक्त टैरिफ वरीयता (DFTP) योजना 2008: प्रधानमंत्री मनमोहन सिंह ने 8 अप्रैल, 2008 को भारत-अफ्रीका फोरम शिखर सम्मेलन में DFTP योजना की घोषणा की थी। भारत सबसे कम विकसित देशों (LDC) को यह सुविधा देने वाला पहला विकासशील देश बन गया। उद्देश्य: भारत में आयात पर अल्प विकसित देशों के निर्यात पर टैरिफ वरीयता प्रदान करना।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "18",
                    question_en: "<p>11. Match the concepts in column A with their respective descriptions in column B.<br><img src=\"data:image/png;base64,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\" width=\"530\" height=\"125\"></p>",
                    question_hi: "<p>11. स्तंभ A में दी गई अवधारणाओं को स्तंभ B में दिए गए उनसे संबंधित विवरण के साथ सुमेलित कीजिए।<br><img src=\"data:image/png;base64,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\" width=\"514\" height=\"281\"></p>",
                    options_en: ["<p>a-5, b-1, c-2, d-3</p>", "<p>a-2, b-3, c-4, d-1</p>", 
                                "<p>a-2, b-1, c-4, d-3</p>", "<p>a-5, b-3, c-2, d-1</p>"],
                    options_hi: ["<p>a-5, b-1, c-2, d-3</p>", "<p>a-2, b-3, c-4, d-1</p>",
                                "<p>a-2, b-1, c-4, d-3</p>", "<p>a-5, b-3, c-2, d-1</p>"],
                    solution_en: "<p>11.(a) <strong>a-5, b-1, c-2, d-3.</strong> Devaluation refers to the intentional downward adjustment of a country\'s currency value relative to another currency or standard. Depreciation, on the other hand, allows a business to allocate the cost of a tangible asset over its useful life for accounting and tax purposes. Deflation occurs when prices decrease over time, contrasting with inflation, which signifies rising prices. Exchange controls are governmental restrictions placed on the buying and selling of currencies.</p>",
                    solution_hi: "<p>11.(a) <strong>a-5, b-1, c-2, d-3.</strong> अवमूल्यन से तात्पर्य किसी देश की मुद्रा मूल्य को किसी अन्य मुद्रा या मानक के सापेक्ष जानबूझकर नीचे की ओर समायोजित करना है। दूसरी ओर, मूल्यह्रास, किसी व्यवसाय को लेखांकन और कर उद्देश्यों के लिए उसके उपयोगी जीवन पर एक मूर्त संपत्ति की लागत आवंटित करने की अनुमति देता है। अपस्फीति तब होती है जब समय के साथ कीमतें घटती हैं, मुद्रास्फीति के विपरीत, जो बढ़ती कीमतों का संकेत देती है। विनिमय नियंत्रण खरीददारी पर लगाए गए सरकारी प्रतिबंध हैं ।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "18",
                    question_en: "<p>12. The Government of India allowed automatic approval of _____% of FDI by Reserve Bank of India in nine categories of industries.</p>",
                    question_hi: "<p>12. भारत सरकार ने उद्योगों की नौ श्रेणियों में भारतीय रिज़र्व बैंक द्वारा ______% तक की एफडीआई (FDI) की स्वचालित स्वीकृति की अनुमति दी।</p>",
                    options_en: ["<p>Up to 51</p>", "<p>Up to 60</p>", 
                                "<p>Up to 41</p>", "<p>Up to 74</p>"],
                    options_hi: ["<p>51</p>", "<p>60</p>",
                                "<p>41</p>", "<p>74</p>"],
                    solution_en: "<p>12.(d)<strong> Up to 74.</strong> Foreign Direct Investment (FDI) refers to an investment made by a company or individual in one country into business operations or assets in another country. The Foreign Investment Facilitation Portal (FIFP) provides single-window clearance for FDI applications under the approval route. It is managed by the Department for Promotion of Industry and Internal Trade (DPIIT), under the Ministry of Commerce and Industry. The two routes for Foreign Direct Investment (FDI) in India are the Automatic Route (Without government approval) and the Government Route (With prior approval of Government).</p>",
                    solution_hi: "<p>12.(d) <strong>74. </strong>प्रत्यक्ष विदेशी निवेश (FDI) से तात्पर्य किसी देश की कंपनी या व्यक्ति द्वारा दूसरे देश में व्यवसाय संचालन या परिसंपत्तियों में किए गए निवेश से है। विदेशी निवेश सुविधा पोर्टल (FIFP) अनुमोदन मार्ग के तहत FDI आवेदनों के लिए एकल-खिड़की मंजूरी प्रदान करता है। इसका प्रबंधन वाणिज्य एवं उद्योग मंत्रालय के तहत उद्योग एवं आंतरिक व्यापार संवर्धन विभाग (DPIIT) द्वारा किया जाता है। भारत में प्रत्यक्ष विदेशी निवेश (FDI) के लिए दो मार्ग हैं स्वचालित मार्ग (सरकारी अनुमोदन के बिना) तथा सरकारी मार्ग (सरकार की पूर्व स्वीकृति के साथ)।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "18",
                    question_en: "13. Which of the following definitions best describes the concept of microfinance?",
                    question_hi: "13. निम्नलिखित में से कौन-सी परिभाषा माइक्रोफाइनेंस की अवधारणा का सर्वोत्तम वर्णन करती है? ",
                    options_en: [" A microfinance institution is a company registered under the Companies Act, 1956, and engaged in the business of loans and advances and acquisition of shares/stocks/bonds/debentures/securities issued by the government. ", " Microfinance is a banking service provided to unemployed or low-income individuals or groups, who otherwise would have no other access to financial services. ", 
                                " A microfinance institution is a digital-only and mobile-first bank ", "  Microfinance refers to providing banking services to individuals living in rural areas. "],
                    options_hi: [" एक माइक्रोफाइनेंस संस्था कंपनी अधिनियम, 1956 के तहत पंजीकृत एक कंपनी है, और सरकार द्वारा जारी किए गए ऋणों एवं अग्रिमों और शेयरों/स्टॉक्स/बांडों/डिबेंचर/प्रतिभूतियों के अधिग्रहण के कारोबार में लगी हुई है। ", " माइक्रोफाइनेंस ऐसी बैंकिंग सेवा है, जो उन बेरोजगार या कम आय वाले व्यक्तियों या समूहों को प्रदान की जाती है, जिनके पास अन्य वित्तीय सेवाओं तक कोई पहुँच न हो। ",
                                " एक माइक्रोफाइनेंस संस्था सिर्फ डिजिटल और प्राथमिकत: मोबाइल केंद्रित बैंक है। ", " माइक्रोफाइनेंस का तात्पर्य ग्रामीण क्षेत्रों में रहने वाले व्यक्तियों को बैंकिंग सेवाएँ प्रदान करना है।"],
                    solution_en: "13.(b) Microfinance loans are small, unsecured loans given to low-income borrowers for starting or expanding small businesses or for basic needs like education and healthcare. The Reserve Bank of India regulates microfinance in India. Types of Microfinance Institutions (MFIs) include credit unions, NGOs, and commercial banks. Notable MFIs include Bandhan Bank, Ujjivan Small Finance Bank, Annapurna Finance, and Muthoot Microfin.",
                    solution_hi: "13.(b) माइक्रोफाइनेंस ऋण छोटे, असुरक्षित ऋण होते हैं जो कम आय वाले उधारकर्ताओं को छोटे व्यवसाय शुरू करने या विस्तार करने या शिक्षा और स्वास्थ्य सेवा जैसी बुनियादी ज़रूरतों के लिए दिए जाते हैं। भारतीय रिज़र्व बैंक भारत में माइक्रोफाइनेंस को नियंत्रित करता है। माइक्रोफाइनेंस संस्थानों (MFIs) के प्रकारों में क्रेडिट यूनियन, NGO और वाणिज्यिक बैंक शामिल हैं। उल्लेखनीय MFI  में बंधन बैंक, उज्जीवन स्मॉल फाइनेंस बैंक, अन्नपूर्णा फाइनेंस और मुथूट माइक्रोफिन शामिल हैं।",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "18",
                    question_en: "<p>14. The Mahalanobis model was the basis for which Five-Year Plan?</p>",
                    question_hi: "<p>14. महालनोबिस मॉडल किस पंचवर्षीय योजना का आधार था?</p>",
                    options_en: ["<p>Fourth Five-Year Plan</p>", "<p>Second Five-Year Plan</p>", 
                                "<p>Sixth Five-Year Plan</p>", "<p>First Five-Year Plan</p>"],
                    options_hi: ["<p>चौथी पंचवर्षीय योजना</p>", "<p>दूसरी पंचवर्षीय योजना</p>",
                                "<p>छठी पंचवर्षीय योजना</p>", "<p>पहली पंचवर्षीय योजना</p>"],
                    solution_en: "<p>14.(b) <strong>Second Five-Year Plan</strong> (1956 - 61): Target Growth rate - 4.5%. Actual Growth rate - 4.3%. The Plan Focussed on rapid industrialization- heavy &amp; basic industries. Advocated huge imports through foreign loans.</p>",
                    solution_hi: "<p>14.(b) <strong>दूसरी पंचवर्षीय योजना (1956 - 61):</strong> लक्ष्य वृद्धि दर - 4.5%, वास्तविक वृद्धि दर - 4.3%। यह योजना तीव्र औद्योगिकीकरण (भारी और बुनियादी उद्योग) पर केंद्रित थी। विदेशी ऋणों के माध्यम से भारी आयात का समर्थन किया गया।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "misc",
                    question_en: "<p>15. Which of the following types of unemployment is found in India when people are employed for a certain period of a year in tourist resorts and various tourism-related activities in the mountain regions, but are unemployed in off-seasons?</p>",
                    question_hi: "<p>15. भारत में निम्न में से किस प्रकार की बेरोजगारी तब पाई जाती है जब लोग एक वर्ष की एक निश्चित अवधि के लिए पर्वतीय क्षेत्रों में पर्यटन रिसॉर्ट्स और विभिन्न पर्यटन संबंधी गतिविधियों में कार्यरत होते हैं, लेकिन ऑफ-सीजन में बेरोजगार होते हैं?</p>",
                    options_en: ["<p>Seasonal unemployment</p>", "<p>Under employment</p>", 
                                "<p>Disguised unemployment</p>", "<p>Cyclical unemployment</p>"],
                    options_hi: ["<p>मौसमी बेरोजगारी</p>", "<p>अल्प रोजगार</p>",
                                "<p>प्रच्छन्न बेरोजगारी</p>", "<p>चक्रीय बेरोजगारी</p>"],
                    solution_en: "<p>15.(a) <strong>Seasonal unemployment</strong>. Cyclical unemployment - When there is not enough aggregate demand in the economy to provide jobs for everyone. Disguised unemployment - A kind of unemployment in which there are people who are visibly employed but are actually unemployed. Underemployment - A condition in which workers are employed in less than full-time or regular jobs or insufficient jobs for their training or economic needs.</p>",
                    solution_hi: "<p>15.(a)<strong> मौसमी बेरोज़गारी।</strong> चक्रीय बेरोज़गारी - जब अर्थव्यवस्था में सभी को रोज़गार प्रदान करने के लिए पर्याप्त कुल मांग नहीं होती है। प्रच्छन्न बेरोज़गारी - एक प्रकार की ऐसी बेरोज़गारी जिसमें ऐसे लोग होते हैं जो दिखने में तो रोज़गार में होते हैं लेकिन वास्तव में बेरोज़गार होते हैं। अल्परोज़गार - एक ऐसी स्थिति जिसमें श्रमिक पूर्णकालिक या नियमित नौकरियों से कम या अपने प्रशिक्षण या आर्थिक ज़रूरतों के लिए अपर्याप्त नौकरियों में कार्यरत होते हैं।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>