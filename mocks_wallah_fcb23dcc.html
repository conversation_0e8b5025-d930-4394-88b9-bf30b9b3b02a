<!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Team SPY</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
        <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
        <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
        <style>
            .question-nav{position:fixed;top:70px;right:0;height:calc(100vh - 70px);width:320px;transition:all .3s;z-index:100;display:flex;flex-direction:column}
            .question-content{margin-right:0;transition:all .3s}
            .question-box{width:40px;height:40px;margin:5px;cursor:pointer}
            .attempted{background-color:#3498db!important;color:white}
            .correct{background-color:#2ecc71!important;color:white}
            .incorrect{background-color:#e74c3c!important;color:white}
            .current{border:3px solid #f39c12!important}
            #question-boxes{overflow-y:auto;max-height:calc(100vh - 150px);padding-bottom:15px}
            @media (max-width:768px){.question-nav{width:100%;right:-100%}.question-content{margin-right:0}}
        </style>
    </head>
    <body>
        <div class="container-fluid p-0">
            <!-- Header with controls -->
            <nav class="navbar navbar-expand-lg navbar-dark bg-primary sticky-top">
                <div class="container-fluid">
                    <a class="navbar-brand" href="#">
                        <i class="fas fa-graduation-cap me-2"></i>MOCKS WALLAH
                    </a>
                    <!-- Section selector -->
                    <div class="navbar-nav me-auto section-tabs">
                        <select class="form-select" id="section-selector" onchange="switchSection(this.value)">
                            <option value="all">All Sections</option>
                        </select>
                    </div>
                    <div class="d-flex align-items-center">
                        <!-- Timer -->
                        <div class="bg-light text-primary rounded px-3 py-2 me-2 fw-bold" id="timer">
                            <i class="far fa-clock me-2"></i><span id="timer-display">60:00</span>
                        </div>
                        <!-- Submit button -->
                        <button class="btn btn-success me-2" id="submit-btn" onclick="submitTest()">
                            <i class="fas fa-check me-1"></i> Submit
                        </button>
                        <!-- Language toggle -->
                        <button class="btn btn-light me-2" id="lang-btn" onclick="toggleLanguage()">
                            <i class="fas fa-language"></i>
                        </button>
                        <!-- Menu toggle for mobile -->
                        <button class="btn btn-light d-lg-none" id="menu-toggle" onclick="toggleMenu()">
                            <i class="fas fa-th"></i>
                        </button>
                    </div>
                </div>
            </nav>
            <div class="row m-0">
                <!-- Main content area -->
                <div class="col p-0 question-content" id="question-content">
                    <div class="container py-4">
                        <!-- Question navigation -->
                        <div class="d-flex justify-content-between mb-4">
                            <button class="btn btn-outline-primary" onclick="prevQuestion()">
                                <i class="fas fa-chevron-left me-1"></i> Previous
                            </button>
                            <div class="fw-bold" id="question-counter">Question 1 of 100</div>
                            <button class="btn btn-outline-primary" onclick="nextQuestion()">
                                Next <i class="fas fa-chevron-right ms-1"></i>
                            </button>
                        </div>
                        <!-- Questions will be displayed here -->
                        <div id="questions-container"></div>
                    </div>
                </div>
                <!-- Question navigation sidebar -->
                <div class="bg-light question-nav" id="question-nav">
                    <div class="p-3">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h5 class="m-0">Questions</h5>
                            <div>
                                <span class="badge bg-secondary me-1" id="attempted-count">0</span>
                                <span class="badge bg-secondary" id="total-count">100</span>
                                <button class="btn btn-sm btn-outline-secondary ms-2 d-lg-none" onclick="toggleMenu()">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                        </div>
                        <div class="d-flex flex-wrap" id="question-boxes"></div>
                    </div>
                </div>
            </div>
            <!-- Results modal -->
            <div class="modal fade" id="results-modal" tabindex="-1" aria-hidden="true">
                <div class="modal-dialog modal-dialog-centered">
                    <div class="modal-content">
                        <div class="modal-header bg-primary text-white">
                            <h5 class="modal-title">Test Results</h5>
                            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            <div class="row text-center">
                                <div class="col-6 mb-3">
                                    <div class="p-3 bg-primary bg-opacity-10 rounded">
                                        <h3 id="score-value">0</h3>
                                        <div class="small text-muted">Score</div>
                                    </div>
                                </div>
                                <div class="col-6 mb-3">
                                    <div class="p-3 bg-success bg-opacity-10 rounded">
                                        <h3 id="correct-value">0</h3>
                                        <div class="small text-muted">Correct</div>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="p-3 bg-danger bg-opacity-10 rounded">
                                        <h3 id="incorrect-value">0</h3>
                                        <div class="small text-muted">Incorrect</div>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="p-3 bg-secondary bg-opacity-10 rounded">
                                        <h3 id="unattempted-value">0</h3>
                                        <div class="small text-muted">Unattempted</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-primary" onclick="reviewTest()">Review Test</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
        <script>
            // Questions data
            const questions = [];
            const sections = {};
            let currentLang = "en";
            let currentQuestion = 0;
            let answers = {};
            let submitted = false;
            let totalTime = 60 * 60;
            let resultsModal;
            // Initialize the test
            window.onload = function() {
                initializeTest();
                startTimer();
                resultsModal = new bootstrap.Modal(document.getElementById('results-modal'));
                // If on desktop, show the question navigation by default
                if (window.innerWidth >= 768) {
                    document.getElementById('question-content').style.marginRight = '320px';
                }
            };
            // Initialize test data
            function initializeTest() {
                // Add sections and questions
                sections["6"] = {
                    name: "Reasoning",
                    start: 0,
                    end: 23
                };
                document.getElementById('section-selector').innerHTML += 
                    `<option value="6">Reasoning</option>`;
                sections["18"] = {
                    name: "General Awareness",
                    start: 24,
                    end: 48
                };
                document.getElementById('section-selector').innerHTML += 
                    `<option value="18">General Awareness</option>`;
                sections["17"] = {
                    name: "Quantitative Aptitude",
                    start: 49,
                    end: 73
                };
                document.getElementById('section-selector').innerHTML += 
                    `<option value="17">Quantitative Aptitude</option>`;
                sections["7"] = {
                    name: "English",
                    start: 74,
                    end: 98
                };
                document.getElementById('section-selector').innerHTML += 
                    `<option value="7">English</option>`;
                sections["misc"] = {
                    name: "Miscellaneous",
                    start: 99,
                    end: 99
                };
                document.getElementById('section-selector').innerHTML += 
                    `<option value="misc">Miscellaneous</option>`;
                    questions.push({
                        id: "1",
                        section: "6",
                        question_en: "<p>1. In a certain code language, &lsquo;EAGLE&rsquo; is written as &lsquo;DZFKD&rsquo; and &lsquo;DREAM&rsquo; is written as &lsquo;CQDZL&rsquo;. How will &lsquo;SINCE&rsquo; be written in that language?</p>",
                        question_hi: "<p>1. एक निश्चित कूट भाषा में, \'EAGLE\' को \'DZFKD\' के रूप में लिखा जाता है और \'DREAM\' को \'CQDZL\' के रूप में लिखा जाता है। उसी भाषा में \'SINCE\' को किस प्रकार लिखा जाएगा?</p>",
                        options_en: [
                            "<p>RJOBD</p>",
                            "<p>THMDF</p>",
                            "<p>RHMBD</p>",
                            "<p>TJODF</p>"
                        ],
                        options_hi: [
                            "<p>RJOBD</p>",
                            "<p>THMDF</p>",
                            "<p>RHMBD</p>",
                            "<p>TJODF</p>"
                        ],
                        solution_en: "<p>1.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741246693399.png\" alt=\"rId4\" width=\"146\" height=\"77\"> ,&nbsp; <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741246693624.png\" alt=\"rId5\" width=\"153\" height=\"75\">,Similarly<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741246693726.png\" alt=\"rId6\" width=\"143\" height=\"77\"></p>",
                        solution_hi: "<p>1.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741246693399.png\" alt=\"rId4\" width=\"146\" height=\"77\"> ,&nbsp; <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741246693624.png\" alt=\"rId5\" width=\"153\" height=\"75\">,इसी प्रकार<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741246693726.png\" alt=\"rId6\" width=\"143\" height=\"77\"></p>",
                        correct: "c",
                        pos_marks: 2.0,
                        neg_marks: 0.5
                    });
                    questions.push({
                        id: "2",
                        section: "6",
                        question_en: "<p>2. Which of the following letter-clusters can replace the question mark (?) in the given series to make it logically complete?<br>PQR, RTV, TWZ, VZD, ?</p>",
                        question_hi: "<p>2. दी गई श्रृंखला को तार्किक रूप से पूरा करने के लिए निम्नलिखित में से कौन-सा अक्षर-समूह प्रश्न चिह्न (?) के स्थान पर आ सकता है?<br>PQR, RTV, TWZ, VZD, ?</p>",
                        options_en: [
                            "<p>YCZ</p>",
                            "<p>OPS</p>",
                            "<p>WXT</p>",
                            "<p>XCH</p>"
                        ],
                        options_hi: [
                            "<p>YCZ</p>",
                            "<p>OPS</p>",
                            "<p>WXT</p>",
                            "<p>XCH</p>"
                        ],
                        solution_en: "<p>2.(d)<br><img src=\"data:image/png;base64,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\" width=\"388\" height=\"130\"></p>",
                        solution_hi: "<p>2.(d)<br><img src=\"data:image/png;base64,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\" width=\"385\" height=\"129\"></p>",
                        correct: "d",
                        pos_marks: 2.0,
                        neg_marks: 0.5
                    });
                    questions.push({
                        id: "3",
                        section: "6",
                        question_en: "<p>3. A paper is folded and cut as shown below. How will it appear when unfolded?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741246693936.png\" alt=\"rId8\" width=\"249\" height=\"81\"></p>",
                        question_hi: "<p>3. नीचे दर्शाए गए अनुसार एक कागज को मोड़कर काटा जाता है। खोलने पर यह कैसा दिखाई देगा?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741246693936.png\" alt=\"rId8\" width=\"249\" height=\"81\"></p>",
                        options_en: [
                            "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741246694030.png\" alt=\"rId9\" width=\"102\" height=\"106\"></p>",
                            "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741246694143.png\" alt=\"rId10\" width=\"102\" height=\"96\"></p>",
                            "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741246694345.png\" alt=\"rId11\" width=\"103\" height=\"98\"></p>",
                            "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741246694448.png\" alt=\"rId12\" width=\"100\" height=\"97\"></p>"
                        ],
                        options_hi: [
                            "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741246694030.png\" alt=\"rId9\" width=\"103\" height=\"107\"></p>",
                            "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741246694143.png\" alt=\"rId10\" width=\"104\" height=\"97\"></p>",
                            "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741246694345.png\" alt=\"rId11\" width=\"101\" height=\"96\"></p>",
                            "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741246694448.png\" alt=\"rId12\" width=\"103\" height=\"100\"></p>"
                        ],
                        solution_en: "<p>3.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741246694448.png\" alt=\"rId12\" width=\"103\" height=\"100\"></p>",
                        solution_hi: "<p>3.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741246694448.png\" alt=\"rId12\" width=\"103\" height=\"100\"></p>",
                        correct: "d",
                        pos_marks: 2.0,
                        neg_marks: 0.5
                    });
                    questions.push({
                        id: "4",
                        section: "6",
                        question_en: "<p>4. A, B, C, D, E and F are sitting around a circular table facing the centre. Only two people sit between A and F. Only one person sits between F and E. C sits to the immediate left of D. C is not an immediate neighbour of A. Who is sitting to the immediate left of F?</p>",
                        question_hi: "<p>4. A, B, C, D, E और F एक वृत्ताकार मेज के चारों ओर केंद्र की ओर मुख करके बैठे हैं। A और F के बीच केवल दो व्यक्ति बैठे हैं। F और E के बीच केवल एक व्यक्ति बैठा है। C, D के ठीक बाएं बैठा है। C, A का निकटतम पड़ोसी नहीं है। F के ठीक बाएं कौन बैठा है?</p>",
                        options_en: [
                            "<p>E</p>",
                            "<p>B</p>",
                            "<p>D</p>",
                            "<p>A</p>"
                        ],
                        options_hi: [
                            "<p>E</p>",
                            "<p>B</p>",
                            "<p>D</p>",
                            "<p>A</p>"
                        ],
                        solution_en: "<p>4.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741246694648.png\" alt=\"rId13\" width=\"156\" height=\"116\"></p>",
                        solution_hi: "<p>4.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741246694648.png\" alt=\"rId13\" width=\"156\" height=\"116\"></p>",
                        correct: "b",
                        pos_marks: 2.0,
                        neg_marks: 0.5
                    });
                    questions.push({
                        id: "5",
                        section: "6",
                        question_en: "<p>5. If \'T\' stands for \'+\', \'P\' stands for \'&divide;\', \'Q\' stands for \'&times;\' and \'R\' stands for \'-\', what will come in place of the question mark (?) in the following equation? <br>24 T 36 P 48 Q 32 R 40 T 53 = ?</p>",
                        question_hi: "<p>5. यदि \'T\' का अर्थ \'+\', \'P\' का अर्थ \'&divide;&rsquo;, \'Q\' का अर्थ \'&times;\' और \'R\' का अर्थ &lsquo;-&rsquo; है, तो निम्नलिखित समीकरण मे प्रश्न चिन्ह (?) के स्थान पर क्या आएगा ?<br>24 T 36 P 48 Q 32 R 40 T 53 = ?</p>",
                        options_en: [
                            "<p>73</p>",
                            "<p>61</p>",
                            "<p>48</p>",
                            "<p>31</p>"
                        ],
                        options_hi: [
                            "<p>73</p>",
                            "<p>61</p>",
                            "<p>48</p>",
                            "<p>31</p>"
                        ],
                        solution_en: "<p>5.(b)<strong> Given :-</strong> 24 T 36 P 48 Q 32 R 40 T 53<br>As per given instruction after interchanging the letter with sign we get<br>24 + 36 &divide; 48 &times; 32 - 40 + 53<br>24 + <math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> &times; 32 - 40 + 53<br>24 + 24 - 40 + 53<br>48 - 40 + 53 = 61</p>",
                        solution_hi: "<p>5.(b)<strong> दिया गया :-</strong> 24 T 36 P 48 Q 32 R 40 T 53<br>दिए गए निर्देश के अनुसार अक्षर को चिन्ह से बदलने पर हमें प्राप्त होता है<br>24 + 36 &divide; 48 &times; 32 - 40 + 53<br>24 + <math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> &times; 32 - 40 + 53<br>24 + 24 - 40 + 53<br>48 - 40 + 53 = 61</p>",
                        correct: "b",
                        pos_marks: 2.0,
                        neg_marks: 0.5
                    });
                    questions.push({
                        id: "6",
                        section: "6",
                        question_en: "<p>6. Select the option in which the given figure is embedded (rotation is NOT allowed).<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741246694763.png\" alt=\"rId14\" width=\"114\" height=\"111\"></p>",
                        question_hi: "<p>6. उस विकल्प का चयन कीजिए, जिसमें दी गई आकृति सन्निहित है (घूर्णन की अनुमति नहीं है)।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741246694763.png\" alt=\"rId14\" width=\"110\" height=\"107\"></p>",
                        options_en: [
                            "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741246694906.png\" alt=\"rId15\" width=\"101\" height=\"106\"></p>",
                            "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741246695023.png\" alt=\"rId16\" width=\"99\" height=\"97\"></p>",
                            "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741246695136.png\" alt=\"rId17\" width=\"101\" height=\"94\"></p>",
                            "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741246695264.png\" alt=\"rId18\" width=\"100\" height=\"98\"></p>"
                        ],
                        options_hi: [
                            "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741246694906.png\" alt=\"rId15\" width=\"100\" height=\"105\"></p>",
                            "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741246695023.png\" alt=\"rId16\" width=\"101\" height=\"99\"></p>",
                            "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741246695136.png\" alt=\"rId17\" width=\"100\" height=\"100\"></p>",
                            "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741246695264.png\" alt=\"rId18\" width=\"100\" height=\"98\"></p>"
                        ],
                        solution_en: "<p>6.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741246695406.png\" alt=\"rId19\" width=\"111\" height=\"106\"></p>",
                        solution_hi: "<p>6.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741246695406.png\" alt=\"rId19\" width=\"111\" height=\"106\"></p>",
                        correct: "d",
                        pos_marks: 2.0,
                        neg_marks: 0.5
                    });
                    questions.push({
                        id: "7",
                        section: "6",
                        question_en: "<p>7. The position of how many letters will change if each of the letters in the word SKYLINE is arranged in alphabetical order?</p>",
                        question_hi: "<p>7. यदि शब्द SKYLINE के प्रत्येक अक्षर को वर्णमाला के क्रम में व्यवस्थित किया जाए, तो कितने अक्षरों का स्थान परिवर्तित होगा?</p>",
                        options_en: [
                            "<p>Seven</p>",
                            "<p>Five</p>",
                            "<p>Four</p>",
                            "<p>Six</p>"
                        ],
                        options_hi: [
                            "<p>सात</p>",
                            "<p>पाँच</p>",
                            "<p>चार</p>",
                            "<p>छ:</p>"
                        ],
                        solution_en: "<p>7.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741246695614.png\" alt=\"rId20\" width=\"194\" height=\"88\"><br>The position of 6 letters is changed.</p>",
                        solution_hi: "<p>7.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741246695614.png\" alt=\"rId20\" width=\"194\" height=\"88\"><br>6 अक्षरों का स्थान बदल दिया गया है।</p>",
                        correct: "d",
                        pos_marks: 2.0,
                        neg_marks: 0.5
                    });
                    questions.push({
                        id: "8",
                        section: "6",
                        question_en: "<p>8. Identify the figure given in the options which when put in place of ? will logically complete the series.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741246695871.png\" alt=\"rId21\" width=\"305\" height=\"64\"></p>",
                        question_hi: "<p>8. विकल्पों में दी गई उस आकृति की पहचान कीजिए जिसे ? के स्थान पर रखने पर श्रृंखला तर्कसंगत रूप से पूर्ण हो जाएगी।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741246695871.png\" alt=\"rId21\" width=\"305\" height=\"64\"></p>",
                        options_en: [
                            "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741246695994.png\" alt=\"rId22\" width=\"95\" height=\"95\"></p>",
                            "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741246696180.png\" alt=\"rId23\" width=\"98\" height=\"98\"></p>",
                            "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741246696298.png\" alt=\"rId24\" width=\"96\" height=\"96\"></p>",
                            "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741246696432.png\" alt=\"rId25\" width=\"95\" height=\"95\"></p>"
                        ],
                        options_hi: [
                            "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741246695994.png\" alt=\"rId22\" width=\"95\" height=\"96\"></p>",
                            "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741246696180.png\" alt=\"rId23\" width=\"95\" height=\"95\"></p>",
                            "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741246696298.png\" alt=\"rId24\" width=\"95\" height=\"96\"></p>",
                            "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741246696432.png\" alt=\"rId25\" width=\"95\" height=\"95\"></p>"
                        ],
                        solution_en: "<p>8.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741246696432.png\" alt=\"rId25\" width=\"95\" height=\"95\"></p>",
                        solution_hi: "<p>8.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741246696432.png\" alt=\"rId25\" width=\"95\" height=\"95\"></p>",
                        correct: "d",
                        pos_marks: 2.0,
                        neg_marks: 0.5
                    });
                    questions.push({
                        id: "9",
                        section: "6",
                        question_en: "<p>9. Study the given Venn diagram and answer the question that follows.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741246696561.png\" alt=\"rId26\" width=\"161\" height=\"158\"> <br>How many women are either smart or brave or both?</p>",
                        question_hi: "<p>9. दिए गए वेन आरेख का अध्ययन करें और नीचे दिए गए प्रश्न का उत्तर दें।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741246696813.png\" alt=\"rId27\" width=\"168\" height=\"167\"> <br>कितनी महिलाएं या तो चतुर है या बहादुर हैं या दोनों हैं?</p>",
                        options_en: [
                            "<p>27</p>",
                            "<p>23</p>",
                            "<p>30</p>",
                            "<p>26</p>"
                        ],
                        options_hi: [
                            "<p>27</p>",
                            "<p>23</p>",
                            "<p>30</p>",
                            "<p>26</p>"
                        ],
                        solution_en: "<p>9.(c) women are either smart or brave or both = 13 + 14 + 3 = 30</p>",
                        solution_hi: "<p>9.(c) महिलाएं या तो चतुर या बहादुर या दोनों हैं = 13 + 14 + 3 = 30</p>",
                        correct: "c",
                        pos_marks: 2.0,
                        neg_marks: 0.5
                    });
                    questions.push({
                        id: "10",
                        section: "6",
                        question_en: "<p>10. Select the figure from among the given option that can replace the question mark (?) in the following series. <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741246696989.png\" alt=\"rId28\" width=\"387\" height=\"80\"></p>",
                        question_hi: "<p>10. दिए गए विकल्पों में से उस आकृति का चयन कीजिए जो निम्नलिखित शृंखला में प्रश्न चिह्न (?) को प्रतिस्थापित कर सकती है। <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741246696989.png\" alt=\"rId28\" width=\"387\" height=\"80\"></p>",
                        options_en: [
                            "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741246697120.png\" alt=\"rId29\" width=\"95\" height=\"97\"></p>",
                            "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741246697240.png\" alt=\"rId30\" width=\"95\" height=\"97\"></p>",
                            "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741246697377.png\" alt=\"rId31\" width=\"95\" height=\"97\"></p>",
                            "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741246697529.png\" alt=\"rId32\" width=\"95\" height=\"97\"></p>"
                        ],
                        options_hi: [
                            "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741246697120.png\" alt=\"rId29\" width=\"96\" height=\"98\"></p>",
                            "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741246697240.png\" alt=\"rId30\" width=\"95\" height=\"97\"></p>",
                            "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741246697377.png\" alt=\"rId31\" width=\"94\" height=\"96\"></p>",
                            "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741246697529.png\" alt=\"rId32\" width=\"94\" height=\"95\"></p>"
                        ],
                        solution_en: "<p>10.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741246697529.png\" alt=\"rId32\" width=\"95\" height=\"96\"></p>",
                        solution_hi: "<p>10.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741246697529.png\" alt=\"rId32\" width=\"95\" height=\"97\"></p>",
                        correct: "d",
                        pos_marks: 2.0,
                        neg_marks: 0.5
                    });
                    questions.push({
                        id: "11",
                        section: "6",
                        question_en: "<p>11. Three statements are followed by conclusions numbered I, II. You have to consider these statements to be true, even if they seem to be at variance with commonly known facts. Decide which of the given conclusions logically follow/s from the given statement. <br><strong>Statements :</strong> <br>Some bottles are pens. <br>All pens are tables. <br>All tables are glasses. <br><strong>Conclusion (I) : </strong>Some glasses are bottles. <br><strong>Conclusion (II) : </strong>Some pens are glasses.</p>",
                        question_hi: "<p>11. तीन कथनों के बाद ।, ।I क्रमांकित निष्कर्ष दिए गए हैं। आपको इन कथनों को सत्य मानना होगा, चाहे वे समान्यतः ज्ञात तथ्यों से अलग प्रतीत होते हों। आपको निश्चय करना है कि कौन-सा/कौन-से निष्कर्ष तार्किक रूप से दिए गए कथन/कथनों के अनुसार है/हैं।<br><strong>कथन :</strong><br>कुछ बोतलें, कलमें हैं।<br>सभी कलमें, मेजें हैं।<br>सभी मेजें, ग्लास हैं।<br><strong>निष्कर्ष (I) :</strong> कुछ ग्लास, बोतलें हैं।<br><strong>निष्कर्ष (II) :</strong> कुछ कलमें, ग्लास हैं।</p>",
                        options_en: [
                            "<p>Neither conclusion (I) nor (II) follows.</p>",
                            "<p>Only conclusion (II) follows.</p>",
                            "<p>Only conclusion (I) follows</p>",
                            "<p>Both conclusions (I) and (II) follow.</p>"
                        ],
                        options_hi: [
                            "<p>न तो निष्कर्ष (I) और न ही (II) कथनों के अनुसार है।</p>",
                            "<p>केवल निष्कर्ष (II) कथनों के अनुसार है।</p>",
                            "<p>केवल निष्कर्ष (I) कथनों के अनुसार है।</p>",
                            "<p>दोनों निष्कर्ष (I) और (II) कथनों के अनुसार हैं।</p>"
                        ],
                        solution_en: "<p>11.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741246697738.png\" alt=\"rId33\" width=\"264\" height=\"88\"><br>Both conclusions (I) and (II) follow. .</p>",
                        solution_hi: "<p>11.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741246697882.png\" alt=\"rId34\" width=\"288\" height=\"94\"><br>दोनों निष्कर्ष (I) और (II) कथनों के अनुसार हैं।</p>",
                        correct: "d",
                        pos_marks: 2.0,
                        neg_marks: 0.5
                    });
                    questions.push({
                        id: "12",
                        section: "6",
                        question_en: "<p>12. 36 is related to 70 following a certain logic. Following the same logic, 41 is related to 80. Which of the following numbers is related to 76 related using the same logic?</p>",
                        question_hi: "<p>12. एक निश्चित तर्क का अनुसरण करते हुए 36, 70 से संबंधित है। उसी तर्क का अनुसरण करते हुए, 41, 80 से संबंधित है। उसी तर्क का उपयोग करते हुए निम्नलिखित में से कौन सी संख्या 76 से संबंधित है?</p>",
                        options_en: [
                            "<p>42</p>",
                            "<p>39</p>",
                            "<p>38</p>",
                            "<p>37</p>"
                        ],
                        options_hi: [
                            "<p>42</p>",
                            "<p>39</p>",
                            "<p>38</p>",
                            "<p>37</p>"
                        ],
                        solution_en: "<p>12.(b)&nbsp;<br><strong>Logic :-&nbsp;</strong>(1st number &times; 2) - 2 = 2nd number<br>(36, 70) :- (36 &times; 2) - 2 &rArr; (72) - 2 = 70<br>(41 , 80) :- (41 &times; 2) - 2 &rArr; (82) - 2 = 80<br>Similarly,<br>(? , 76) :- (39 &times; 2) - 2 &rArr; (78) - 2 = 76</p>",
                        solution_hi: "<p>12.(b)<strong> <br>तर्क:- </strong>(पहली संख्या &times; 2) - 2 = दूसरी संख्या<br>(36, 70) :- (36 &times; 2) - 2 &rArr; (72) - 2 = 70<br>(41 , 80) :- (41 &times; 2) - 2 &rArr; (82) - 2 = 80<br>इसी प्रकार,<br>(? , 76) :- (39 &times; 2) - 2 &rArr; (78) - 2 = 76</p>",
                        correct: "b",
                        pos_marks: 2.0,
                        neg_marks: 0.5
                    });
                    questions.push({
                        id: "13",
                        section: "6",
                        question_en: "<p>13. Select the set in which the numbers are related in the same way as are the numbers of the given sets.<br>(<strong>NOTE :</strong> Operations should be performed on the whole numbers, without breaking down the numbers into its constituent digits. E.g. 13 - Operations on 13 such as adding/deleting/multiplying, etc. to 13 can be performed. Breaking down 13 into 1&nbsp;and 3 and then performing mathematical operations on 1 and 3 is not allowed.)<br>(16, 4, 20)<br>(28, 7, 35)</p>",
                        question_hi: "<p>13. उस समुच्चय का चयन करें जिसकी संख्याएं उसी तरह से संबंधित हैं जिस तरह निम्नलिखित समुच्चयों की संख्याएं संबंधित हैं।<br>(<strong>नोट : </strong>संख्याओं को उसके घटक अंकों में तोड़े बिना, पूर्ण संख्याओं पर संक्रियाएं की जानी चाहिए। उदाहरण के लिए संख्या 13 को लें - 13 पर संक्रियाएं, जैसे कि 13 में जोड़ना/घटाना/गुणा करना आदि, की जा सकती हैं। 13 को 1 और 3 में तोड़ने और फिर 1 और 3 पर गणितीय संक्रियाएं करने की अनुमति नहीं है)<br>(16, 4, 20)<br>(28, 7, 35)</p>",
                        options_en: [
                            "<p>(52, 13, 60)</p>",
                            "<p>(50, 13, 65)</p>",
                            "<p>(52, 13,65)</p>",
                            "<p>(50, 13,60)</p>"
                        ],
                        options_hi: [
                            "<p>(52, 13, 60)</p>",
                            "<p>(50, 13, 65)</p>",
                            "<p>(52, 13, 65)</p>",
                            "<p>(50, 13, 60)</p>"
                        ],
                        solution_en: "<p>13.(c) <br><strong>Logic :-</strong> (1st number + 2nd number) = 3rd number<br>(16, 4 ,20) :- (16 + 4) = 20<br>(28, 7, 35) :- (28 + 7) = 35<br>Similarly,<br>(52, 13, 65) :- (52 + 13) = 65</p>",
                        solution_hi: "<p>13.(c) <br><strong>तर्क :-</strong> (पहली संख्या + दूसरी संख्या) = तीसरी संख्या<br>(16, 4 ,20) :- (16 + 4) = 20<br>(28, 7, 35) :- (28 + 7) = 35<br>इसी प्रकार,<br>(52, 13, 65) :- (52 + 13) = 65</p>",
                        correct: "c",
                        pos_marks: 2.0,
                        neg_marks: 0.5
                    });
                    questions.push({
                        id: "14",
                        section: "6",
                        question_en: "<p>14. Select the correct mirror image of the given figure when the mirror is placed at MN as shown below.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741246697989.png\" alt=\"rId35\" width=\"216\" height=\"67\"></p>",
                        question_hi: "<p>14. नीचे दी गई आकृति के उस सही दर्पण प्रतिबिंब को चुनिए, जो नीचे दर्शाए गए अनुसार दर्पण को MN पर रखने पर बनेगा।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741246697989.png\" alt=\"rId35\" width=\"216\" height=\"67\"></p>",
                        options_en: [
                            "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741246698120.png\" alt=\"rId36\" width=\"134\" height=\"29\"></p>",
                            "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741246698226.png\" alt=\"rId37\" width=\"132\" height=\"30\"></p>",
                            "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741246698339.png\" alt=\"rId38\" width=\"136\" height=\"31\"></p>",
                            "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741246698478.png\" alt=\"rId39\" width=\"136\" height=\"33\"></p>"
                        ],
                        options_hi: [
                            "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741246698120.png\" alt=\"rId36\" width=\"134\" height=\"29\"></p>",
                            "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741246698226.png\" alt=\"rId37\" width=\"137\" height=\"31\"></p>",
                            "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741246698339.png\" alt=\"rId38\" width=\"141\" height=\"32\"></p>",
                            "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741246698478.png\" alt=\"rId39\" width=\"136\" height=\"33\"></p>"
                        ],
                        solution_en: "<p>14.(c) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741246698339.png\" alt=\"rId38\" width=\"136\" height=\"31\"></p>",
                        solution_hi: "<p>14.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741246698339.png\" alt=\"rId38\" width=\"136\" height=\"31\"></p>",
                        correct: "c",
                        pos_marks: 2.0,
                        neg_marks: 0.5
                    });
                    questions.push({
                        id: "15",
                        section: "6",
                        question_en: "<p>15. Four letter-clusters have been given, out of which three are alike in some manner and one is different. Select the one that is different.<br>(<strong>Note : </strong>The odd one out is not based on the number of consonants/vowels or their position in the letter cluster)<br>1) FGNOP<br>2) IJQRS<br>3) OPWXY<br>4) RSXYZ</p>",
                        question_hi: "<p>15. चार अक्षर-समूह दिए गए हैं, जिनमें से तीन किसी न किसी रूप में एक जैसे हैं और एक भिन्न है। दिए गए विकल्पों में उस भिन्न अक्षर-समूह को चुनिए।<br>1) FGNOP<br>2) IJQRS<br>3) OPWXY<br>4) RSXYZ</p>",
                        options_en: [
                            "<p>RSXYZ</p>",
                            "<p>IJQRS</p>",
                            "<p>OPWXY</p>",
                            "<p>FGNOP</p>"
                        ],
                        options_hi: [
                            "<p>RSXYZ</p>",
                            "<p>IJQRS</p>",
                            "<p>OPWXY</p>",
                            "<p>FGNOP</p>"
                        ],
                        solution_en: "<p>15.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741246698739.png\" alt=\"rId40\" width=\"150\" height=\"47\">,&nbsp; <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741246698988.png\" alt=\"rId41\" width=\"153\" height=\"48\"><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741246699198.png\" alt=\"rId42\" width=\"154\" height=\"47\"> But, <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741246699451.png\" alt=\"rId43\" width=\"155\" height=\"48\"></p>",
                        solution_hi: "<p>15.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741246698739.png\" alt=\"rId40\" width=\"150\" height=\"47\">,&nbsp; <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741246698988.png\" alt=\"rId41\" width=\"153\" height=\"48\"><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741246699198.png\" alt=\"rId42\" width=\"154\" height=\"47\"> लेकिन, <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741246699451.png\" alt=\"rId43\" width=\"155\" height=\"48\"></p>",
                        correct: "a",
                        pos_marks: 2.0,
                        neg_marks: 0.5
                    });
                    questions.push({
                        id: "16",
                        section: "6",
                        question_en: "<p>16. In a certain code language, \'WJSL\' is coded as \'25-12-21-14\' and \'DUOH\' is coded as \'6-23-17-10\'. What is the code for \'PFKR\' in the given language?</p>",
                        question_hi: "<p>16. एक निश्चित कूट भाषा में, &lsquo;WJSL&rsquo; को &lsquo;25-12-21-14&rsquo; लिखा जाता है और &lsquo;DUOH&rsquo; को &lsquo;6-23-17-10&rsquo; लिखा जाता है। उसी कूट भाषा में &lsquo;PFKR&rsquo; को कैसे लिखा जाएगा?</p>",
                        options_en: [
                            "<p>20-10-14-21</p>",
                            "<p>20-9-12-20</p>",
                            "<p>19-9-12-19</p>",
                            "<p>18-8-13-20</p>"
                        ],
                        options_hi: [
                            "<p>20-10-14-21</p>",
                            "<p>20-9-12-20</p>",
                            "<p>19-9-12-19</p>",
                            "<p>18-8-13-20</p>"
                        ],
                        solution_en: "<p>16.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741246699668.png\" alt=\"rId44\" width=\"169\" height=\"96\">,&nbsp; <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741246699857.png\" alt=\"rId45\" width=\"183\" height=\"97\"><br>similarly ,<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741246700021.png\" alt=\"rId46\" width=\"162\" height=\"101\"></p>",
                        solution_hi: "<p>16.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741246700290.png\" alt=\"rId47\" width=\"174\" height=\"100\"> ,&nbsp; <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741246700405.png\" alt=\"rId48\" width=\"192\" height=\"104\"><br>इसी प्रकार <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741246700495.png\" alt=\"rId49\" width=\"164\" height=\"98\"></p>",
                        correct: "d",
                        pos_marks: 2.0,
                        neg_marks: 0.5
                    });
                    questions.push({
                        id: "17",
                        section: "6",
                        question_en: "<p>17. &lsquo;A &times; B&rsquo; means &lsquo;A is B&rsquo;s mother&rsquo;. <br>&lsquo;A &divide; B&rsquo; means &lsquo;A is B&rsquo;s son&rsquo;. <br>&lsquo;A + B&rsquo; means &lsquo;A is B&rsquo;s husband&rsquo;. <br>&lsquo;A &ndash; B&rsquo; means &lsquo;A is B&rsquo;s sister&rsquo;. <br>Using the same meaning of the mathematical operators as given above, in &lsquo;D + E &times; F &ndash; G&rsquo; what is the relation of F with D?</p>",
                        question_hi: "<p>17. A &times; B\' का अर्थ है \'A, B की माँ है\'। <br>\'A &divide; B\' का अर्थ है \'A, B का पुत्र है\'। <br>\'A + B\' का अर्थ है \'A, B का पति है\'। <br>\'A - B\' का अर्थ है \'A, B की बहन है\'। <br>ऊपर दिए गए गणितीय संकारकों के समान अर्थ का उपयोग करते हुए ज्ञात करें कि \'D + E &times; F - G\' में F का D से क्या संबंध है?</p>",
                        options_en: [
                            "<p>Daughter</p>",
                            "<p>Mother</p>",
                            "<p>Son</p>",
                            "<p>Sister&rsquo;s daughter</p>"
                        ],
                        options_hi: [
                            "<p>पुत्री</p>",
                            "<p>माँ</p>",
                            "<p>पुत्र</p>",
                            "<p>बहन की पुत्री</p>"
                        ],
                        solution_en: "<p>17.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741246700680.png\" alt=\"rId50\" width=\"210\" height=\"119\"><br>F is D&rsquo;s daughter.</p>",
                        solution_hi: "<p>17.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741246700680.png\" alt=\"rId50\" width=\"210\" height=\"119\"><br>F, D की बेटी है.</p>",
                        correct: "a",
                        pos_marks: 2.0,
                        neg_marks: 0.5
                    });
                    questions.push({
                        id: "18",
                        section: "6",
                        question_en: "<p>18. What should come in place of the question mark (?) in the given series? <br>3977, 3877, 3777, 3677, 3577, ?</p>",
                        question_hi: "<p>18. दी गई श्रृंखला में प्रश्न चिह्न (?) के स्थान पर क्या आना चाहिए?<br>3977, 3877, 3777, 3677, 3577, ?</p>",
                        options_en: [
                            "<p>3477</p>",
                            "<p>3277</p>",
                            "<p>3333</p>",
                            "<p>3377</p>"
                        ],
                        options_hi: [
                            "<p>3477</p>",
                            "<p>3277</p>",
                            "<p>3333</p>",
                            "<p>3377</p>"
                        ],
                        solution_en: "<p>18.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741246700875.png\" alt=\"rId51\" width=\"308\" height=\"95\"></p>",
                        solution_hi: "<p>18.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741246700875.png\" alt=\"rId51\" width=\"308\" height=\"95\"></p>",
                        correct: "a",
                        pos_marks: 2.0,
                        neg_marks: 0.5
                    });
                    questions.push({
                        id: "19",
                        section: "6",
                        question_en: "<p>19. Three statements are followed by conclusions numbered I, II. You have to consider these statements to be true, even if they seem to be at variance with commonly known facts. Decide which of the given conclusions logically follow/s from the given statement.<br><strong>Statements :</strong><br>All shirts are pants.<br>Some dresses are pants.<br>Some pants are ties.<br><strong>Conclusion (I):</strong> Some dresses are ties.<br><strong>Conclusion (II):</strong> Some ties are shirts.</p>",
                        question_hi: "<p>19. तीन कथनों के बाद I, II क्रमांकित निष्कर्ष दिए गए हैं। आपको इन कथनों को सत्य मानना होगा, चाहे वे<br>समान्यतः ज्ञात तथ्यों से अलग प्रतीत होते हों। आपको निश्चय करना है कि कौन-सा/कौन-से निष्कर्ष तार्किक रूप से दिए गए कथन/कथनों के अनुसार है/हैं।<br><strong>कथन :</strong><br>सभी शर्ट, पैंट हैं।<br>कुछ ड्रेस, पैंट हैं।<br>कुछ पैंट, टाई हैं।<br><strong>निष्कर्ष (I):</strong> कुछ ड्रेस, टाई हैं।<br><strong>निष्कर्ष (II): </strong>कुछ टाई, शर्ट हैं।</p>",
                        options_en: [
                            "<p>Both conclusions (I) and (II) follow.</p>",
                            "<p>Only conclusion (II) follows.</p>",
                            "<p>Only conclusion (I) follows.</p>",
                            "<p>Neither conclusion (I) nor (II) follows.</p>"
                        ],
                        options_hi: [
                            "<p>दोनों निष्कर्ष (I) और (II) कथनों के अनुसार हैं।</p>",
                            "<p>केवल निष्कर्ष (II) कथनों के अनुसार है।</p>",
                            "<p>केवल निष्कर्ष (I) कथनों के अनुसार है।</p>",
                            "<p>न तो निष्कर्ष (I) और न ही (II) कथनों के अनुसार है।</p>"
                        ],
                        solution_en: "<p>19.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741246701013.png\" alt=\"rId52\" width=\"222\" height=\"117\"><br>Neither conclusion I nor II follow.</p>",
                        solution_hi: "<p>19.(d) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741246701380.png\" alt=\"rId53\" width=\"249\" height=\"129\"><br>न तो निष्कर्ष I और न ही II अनुसरण करता है।</p>",
                        correct: "d",
                        pos_marks: 2.0,
                        neg_marks: 0.5
                    });
                    questions.push({
                        id: "20",
                        section: "6",
                        question_en: "<p>20. Six words &lsquo;Eat&rsquo;, &lsquo;Play&rsquo;, &lsquo;Cry&rsquo;, &lsquo;Sleep&rsquo;, &lsquo;Dance&rsquo; and &lsquo;Run&rsquo; are written on different faces of a dice. Two positions of this dice are shown in the figure below. Find the word on the face opposite to &lsquo;Eat&rsquo;.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741246701618.png\" alt=\"rId54\" width=\"236\" height=\"124\"></p>",
                        question_hi: "<p>20. एक पासे के विभिन्न फलकों पर छह शब्द \'खाना\' (\'Eat\'), \'खेलना\' (\'Play\'), \'रोना\'(\'Cry\') , \'सोना\' (\'Sleep\'), \'नाचना\' (\'Dance\') और \'दौड़ना\' (\'Run\') लिखे हुए हैं। नीचे चित्र में इस पासे की दो स्थितियों को दिखाया गया है। \'खाना\' (\'Eat\') शब्द के विपरीत फलक पर कौन-सा शब्द होगा?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741246701898.png\" alt=\"rId55\" width=\"243\" height=\"128\"> <br>Eat = खाना <br>Play = खेलना <br>Cry = रोना <br>Sleep = सोना <br>Dance = नाचना <br>Run = दौड़ना</p>",
                        options_en: [
                            "<p>Cry</p>",
                            "<p>Play</p>",
                            "<p>Dance</p>",
                            "<p>Sleep</p>"
                        ],
                        options_hi: [
                            "<p>रोना (Cry)</p>",
                            "<p>खेलना (Play)</p>",
                            "<p>नाचना (Dance)</p>",
                            "<p>सोना (Sleep)</p>"
                        ],
                        solution_en: "<p>20.(c)<br>From the two dice we can see that play and cry are common.<br>Hence, &lsquo;Dance&rsquo; will be opposite to &lsquo;Eat&rsquo;.</p>",
                        solution_hi: "<p>20.(c)<br>दोनों पासों से हम देख सकते हैं कि &lsquo;खेलना&rsquo; (play) और रोना (cry) उभयनिष्ठ &nbsp;है।<br>अतः, \'नाचना\', \'खाना\' के विपरीत होगा।</p>",
                        correct: "c",
                        pos_marks: 2.0,
                        neg_marks: 0.5
                    });
                    questions.push({
                        id: "21",
                        section: "6",
                        question_en: "<p>21. Two different positions of the same dice are given below. Which is the number on the face opposite the face containing 5?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741246702156.png\" alt=\"rId56\" width=\"187\" height=\"92\"></p>",
                        question_hi: "<p>21. नीचे एक ही पासे की दो अलग-अलग स्थितियाँ दी गई हैं। संख्&zwj;या 5 वाले फलक के विपरीत फलक पर कौन-सी संख्या है? <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741246702156.png\" alt=\"rId56\" width=\"187\" height=\"92\"></p>",
                        options_en: [
                            "<p>6</p>",
                            "<p>2</p>",
                            "<p>1</p>",
                            "<p>3</p>"
                        ],
                        options_hi: [
                            "<p>6</p>",
                            "<p>2</p>",
                            "<p>1</p>",
                            "<p>3</p>"
                        ],
                        solution_en: "<p>21.(c)<br>From the two dice the opposite pairs are<br>2 &harr; 6 , 3 &harr; 4 , 1 &harr; 5</p>",
                        solution_hi: "<p>21.(c)<br>दो पासों के विपरीत युग्म इस प्रकार हैं,<br>2 &harr; 6 , 3 &harr; 4 , 1 &harr; 5</p>",
                        correct: "c",
                        pos_marks: 2.0,
                        neg_marks: 0.5
                    });
                    questions.push({
                        id: "22",
                        section: "6",
                        question_en: "<p>22. Six friends - A, B, C, D, E and F - are sitting around a circular table, facing towards the centre of the table.&nbsp;D and C are immediate neighbours of A. B is second to the right of A. F is an immediate neighbour of E , and fourth to the left of D. What is the position of E ?</p>",
                        question_hi: "<p>22. छ: मित्र - A, B, C, D, E और F - किसी वृत्ताकार मेज के परितः मेज के केंद्राभिमुख होकर बैठे हैं। D और C, A के ठीक समीपस्थ हैं। B, A के दाएँ से दूसरा है। F, E के ठीक समीपस्थ और D के बाएँ से चौथा है। E की स्थिति कहाँ है?</p>",
                        options_en: [
                            "<p>Third to the left of A</p>",
                            "<p>Immediate left of C</p>",
                            "<p>Second to the right of A</p>",
                            "<p>Immediate neighbour of D and F</p>"
                        ],
                        options_hi: [
                            "<p>A के बाएँ से तीसरा</p>",
                            "<p>C के ठीक बाएँ</p>",
                            "<p>A के दाएँ से दूसरा</p>",
                            "<p>D और F के ठीक पास</p>"
                        ],
                        solution_en: "<p>22.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741246702316.png\" alt=\"rId57\" width=\"158\" height=\"136\"><br>E is immediate to the left of C</p>",
                        solution_hi: "<p>22.(b) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741246702316.png\" alt=\"rId57\" width=\"158\" height=\"136\"><br>E, C के ठीक बाएँ है</p>",
                        correct: "b",
                        pos_marks: 2.0,
                        neg_marks: 0.5
                    });
                    questions.push({
                        id: "23",
                        section: "6",
                        question_en: "<p>23. If \'A\' stands for \'&divide;\', \'B\' stands for,&rsquo;&times;&rsquo; \'C\' stands for &lsquo;+&rsquo; and \'D\' stands for &lsquo;-&rsquo;, what will come in place of the question mark (?) in the following equation?<br>96 B 3 D 285 A 5 C 11 = ?</p>",
                        question_hi: "<p>23. यदि \'A\' का अर्थ \'&divide;&rsquo;, \'B\' का अर्थ \'&times;\', \'C\' का अर्थ \'+\' और \'D\' का अर्थ &lsquo;-&rsquo; है, तो निम्नलिखित समीकरण में प्रश्न चिन्ह (?) के स्थान पर क्या आएगा ?<br>96 B 3 D 285 A 5 C 11 = ?</p>",
                        options_en: [
                            "<p>272</p>",
                            "<p>252</p>",
                            "<p>242</p>",
                            "<p>262</p>"
                        ],
                        options_hi: [
                            "<p>272</p>",
                            "<p>252</p>",
                            "<p>242</p>",
                            "<p>262</p>"
                        ],
                        solution_en: "<p>23.(c) <strong>Given :- </strong>96 B 3 D 285 A 5 C 11<br>As per given instruction after interchanging the letter with sign we get<br>96 &times; 3 - 285 &divide; 5 + 11<br>288 - 57 + 11 = 242</p>",
                        solution_hi: "<p>23.(c) <strong>दिया गया :- </strong>96 B 3 D 285 A 5 C 11<br>दिए गए निर्देश के अनुसार अक्षर को चिन्ह से बदलने पर हमें प्राप्त होता है<br>96 &times; 3 - 285 &divide; 5 + 11<br>288 - 57 + 11 = 242</p>",
                        correct: "c",
                        pos_marks: 2.0,
                        neg_marks: 0.5
                    });
                    questions.push({
                        id: "24",
                        section: "6",
                        question_en: "<p>24. Select the option in which the given figure is embedded (rotation is not allowed). <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741246702464.png\" alt=\"rId58\" width=\"114\" height=\"105\"></p>",
                        question_hi: "<p>24. उस विकल्प का चयन कीजिए जिसमें दी गई आकृति निहित है (घूर्णन की अनुमति नहीं है)।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741246702464.png\" alt=\"rId58\" width=\"114\" height=\"105\"></p>",
                        options_en: [
                            "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741246702620.png\" alt=\"rId59\" width=\"100\" height=\"96\"></p>",
                            "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741246702757.png\" alt=\"rId60\" width=\"112\" height=\"99\"></p>",
                            "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741246702872.png\" alt=\"rId61\" width=\"101\" height=\"92\"></p>",
                            "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741246702991.png\" alt=\"rId62\" width=\"102\" height=\"93\"></p>"
                        ],
                        options_hi: [
                            "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741246702620.png\" alt=\"rId59\"></p>",
                            "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741246702757.png\" alt=\"rId60\" width=\"113\" height=\"99\"></p>",
                            "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741246702872.png\" alt=\"rId61\" width=\"113\" height=\"102\"></p>",
                            "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741246702991.png\" alt=\"rId62\" width=\"110\" height=\"101\"></p>"
                        ],
                        solution_en: "<p>24.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741246703127.png\" alt=\"rId63\" width=\"126\" height=\"110\"></p>",
                        solution_hi: "<p>24.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741246703127.png\" alt=\"rId63\" width=\"126\" height=\"110\"></p>",
                        correct: "d",
                        pos_marks: 2.0,
                        neg_marks: 0.5
                    });
                    questions.push({
                        id: "25",
                        section: "18",
                        question_en: "<p>25. Three of the following four letter-clusters are alike in a certain way and thus form a group. Select the letter-cluster that does NOT belong to that group (Note: The odd one out is not based on the number of consonants/vowels or their position in the letter&nbsp;cluster)</p>",
                        question_hi: "<p>25. निम्नलिखित चार अक्षर-समूहों में से तीन एक निश्चित तरीके से एक जैसे हैं और इस प्रकार एक समूह बनाते हैं। उस अक्षर-समूह का चयन कीजिए जो उस समूह से संबंधित नहीं है।</p>",
                        options_en: [
                            "<p>FJN</p>",
                            "<p>XBF</p>",
                            "<p>QUY</p>",
                            "<p>LHJ</p>"
                        ],
                        options_hi: [
                            "<p>FJN</p>",
                            "<p>XBF</p>",
                            "<p>QUY</p>",
                            "<p>LHJ</p>"
                        ],
                        solution_en: "<p>25.(d)<br><strong id=\"docs-internal-guid-3226a702-7fff-f712-afc9-be804e01ac46\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXeF5FigG_ItmLpgT_v1VoaKbbw88HADctVfkfd2AKR04KLZpDy5kWzDGexF_dO_wq7laAiG6eNicBNqxE4VeE83wPSh5i65Jnm5qzsJyamlGshyVi5EIRMSGi37qTxUxzFYwX1Lng?key=eWUVE7OPS7j_nFsbVYl2FdPg\" width=\"96\" height=\"73\"></strong>,&nbsp; <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741246703359.png\" alt=\"rId65\" width=\"110\" height=\"71\">, <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741246703579.png\" alt=\"rId66\" width=\"121\" height=\"74\"><br>But,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741246703734.png\" alt=\"rId67\" width=\"111\" height=\"84\"></p>",
                        solution_hi: "<p>25.(d) <br><strong id=\"docs-internal-guid-3226a702-7fff-f712-afc9-be804e01ac46\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXeF5FigG_ItmLpgT_v1VoaKbbw88HADctVfkfd2AKR04KLZpDy5kWzDGexF_dO_wq7laAiG6eNicBNqxE4VeE83wPSh5i65Jnm5qzsJyamlGshyVi5EIRMSGi37qTxUxzFYwX1Lng?key=eWUVE7OPS7j_nFsbVYl2FdPg\" width=\"96\" height=\"73\"></strong>,&nbsp; <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741246703359.png\" alt=\"rId65\" width=\"110\" height=\"71\">, <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741246703579.png\" alt=\"rId66\" width=\"121\" height=\"74\"><br>लेकिन,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741246703734.png\" alt=\"rId67\" width=\"111\" height=\"84\"></p>",
                        correct: "d",
                        pos_marks: 2.0,
                        neg_marks: 0.5
                    });
                    questions.push({
                        id: "26",
                        section: "18",
                        question_en: "<p>26. Which of the following is NOT correctly matched?</p>",
                        question_hi: "<p>26. निम्नलिखित में से कौन-सा सही ढंग से सुमेलित नहीं है?</p>",
                        options_en: [
                            "<p>Nucleolus &ndash; Synthesis of DNA</p>",
                            "<p>Mitochondria &ndash; Production of chemical energy</p>",
                            "<p>Ribosome &ndash; Assembly of ribosomes</p>",
                            "<p>Nucleus &ndash; Storage of genetic information</p>"
                        ],
                        options_hi: [
                            "<p>केंद्रिका - डीएनए का संश्लेषण</p>",
                            "<p>सूत्र कणिकाएँ - रासायनिक ऊर्जा का उत्पादन</p>",
                            "<p>राइबोसोम - राइबोसोम का संयोजन</p>",
                            "<p>केंद्रक - अनुवांशिक जानकारी का भंडारण</p>"
                        ],
                        solution_en: "<p>26.(a) <strong>Nucleolus &ndash; Synthesis of DNA.</strong> The interphase nucleus contains extended chromatin fibers, a nuclear matrix, and one or more nucleoli. Mitochondria, known as the cell\'s powerhouses, produce energy through aerobic respiration. Ribosomes are non-membrane bound organelles found in both eukaryotic and prokaryotic cells.</p>",
                        solution_hi: "<p>26.(a) <strong>केंद्रिका - DNA का संश्लेषण। </strong>अंतरावस्था नाभिक में विस्तारित क्रोमैटिन तंतु, एक नाभिकीय आधात्री, और एक या अधिक केंद्रक होते हैं। माइटोकॉन्ड्रिया, जो कोशिका के ऊर्जागृह के रूप में जाने जाते हैं, वायवीय श्वसन के माध्यम से ऊर्जा का उत्पादन करते हैं। राइबोसोम गैर-झिल्ली बद्ध कोशिकांग हैं जो यूकैरियोटिक और प्रोकैरियोटिक दोनों कोशिकाओं में पाए जाते हैं।</p>",
                        correct: "a",
                        pos_marks: 2.0,
                        neg_marks: 0.5
                    });
                    questions.push({
                        id: "27",
                        section: "18",
                        question_en: "<p>27. Match the concepts in column A with their respective descriptions in column B.<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; <strong>&nbsp;Column A&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;Column B</strong><br>a. Gross national product&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;1. Annual income received by the individual\'s <br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;production in a given period<br>b. Net national product&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;2. After the tax of personal income<br>c. Personal income&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;3. Money value of the national output<br>d. Disposable personal income&nbsp; &nbsp; 4. Net production of goods services during the year</p>",
                        question_hi: "<p>27. स्तंभ A में दी गई अवधारणाओं को स्तंभ B में दिए गए उनसे संबंधित विवरण के साथ सुमेलित कीजिए।<br>&nbsp; &nbsp; &nbsp; <strong>&nbsp; &nbsp; &nbsp; &nbsp;स्तंभ A&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;स्तंभ B</strong><br>a. सकल राष्ट्रीय उत्पाद&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 1. किसी निश्चित अवधि में व्यक्ति के उत्पादन द्वारा प्राप्त वार्षिक आय<br>b. कुल राष्ट्रीय उत्पाद&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;2. व्यक्तिगत आय के कर के बाद<br>c. व्यक्तिगत आय&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;3. राष्ट्रीय उत्पादन का मौद्रिक मूल्य<br>d. प्रयोज्य व्यक्तिगत आय&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;4. वर्ष के दौरान माल सेवाओं का कुल उत्पादन</p>",
                        options_en: [
                            "<p>a-2, b-1, c-3, d-4</p>",
                            "<p>a-3, b-1, c-2, d-4</p>",
                            "<p>a-3, b-4, c-1, d-2</p>",
                            "<p>a-4, b-3, c-2, d-1</p>"
                        ],
                        options_hi: [
                            "<p>a-2, b-1, c-3, d-4</p>",
                            "<p>a-3, b-1, c-2, d-4</p>",
                            "<p>a-3, b-4, c-1, d-2</p>",
                            "<p>a-4, b-3, c-2, d-1</p>"
                        ],
                        solution_en: "<p>27.(c) <strong>a-3, b-4, c-1, d-2.</strong> Gross National Product (GNP) = Gross Domestic Product (GDP) + Net income from abroad. Net national product (NNP) = GNP &minus; Depreciation. Personal Income = National Income &minus; Corporate Taxes &minus; Retained Earnings + Transfer Payments. Disposable personal income (DPI) = Personal Income &minus; Personal Taxes.</p>",
                        solution_hi: "<p>27.(c) <strong>a-3, b-4, c-1, d-2.</strong> सकल राष्ट्रीय उत्पाद (GNP) = सकल घरेलू उत्पाद (GDP) + विदेश से प्राप्त शुद्ध आय। निवल राष्ट्रीय उत्पाद (NNP) = GNP - मूल्यह्रास। व्यक्तिगत आय = राष्ट्रीय आय - कॉर्पोरेट कर - प्रतिधारित आय + हस्तांतरण भुगतान। प्रयोज्य व्यक्तिगत आय (DPI) = व्यक्तिगत आय - व्यक्तिगत कर।</p>",
                        correct: "c",
                        pos_marks: 2.0,
                        neg_marks: 0.5
                    });
                    questions.push({
                        id: "28",
                        section: "18",
                        question_en: "<p>28.The students in a physics lab have been asked to measure the amount of electric current in a circuit. Which device should they use?</p>",
                        question_hi: "<p>28. भौतिकी प्रयोगशाला में विद्यार्थियों को किसी परिपथ में विद्युत धारा की मात्रा मापने के लिए कहा गया है। उन्हें किस डिवाइस का उपयोग करना चाहिए?</p>",
                        options_en: [
                            "<p>Altimeter</p>",
                            "<p>Ammeter</p>",
                            "<p>Ohmmeter</p>",
                            "<p>Voltmeter</p>"
                        ],
                        options_hi: [
                            "<p>अल्टीमीटर</p>",
                            "<p>ऐमीटर</p>",
                            "<p>ओममीटर</p>",
                            "<p>वोल्टमीटर</p>"
                        ],
                        solution_en: "<p>28.(b) <strong>Ammeter.</strong> The current is the flow of electrons whose unit is ampere. Altimeter: This device measures altitude. Ohmmeter: This device measures resistance. Voltmeter: This device measures voltage.</p>",
                        solution_hi: "<p>28. (b) <strong>ऐमीटर।</strong> धारा इलेक्ट्रॉनों का प्रवाह है जिसकी इकाई एम्पीयर है। अल्टीमीटर: यह उपकरण ऊंचाई मापता है। ओममीटर: यह उपकरण प्रतिरोध मापता है। वोल्टमीटर: यह उपकरण वोल्टेज मापता है।</p>",
                        correct: "b",
                        pos_marks: 2.0,
                        neg_marks: 0.5
                    });
                    questions.push({
                        id: "29",
                        section: "18",
                        question_en: "<p>29. Which of the following mountain peaks is a part of the Himalayas?</p>",
                        question_hi: "<p>29. निम्नलिखित में से कौन-सा पर्वत शिखर (mountain peak) हिमालय का एक भाग है?</p>",
                        options_en: [
                            "<p>Kamet</p>",
                            "<p>Dhupgarh</p>",
                            "<p>Kalsubai</p>",
                            "<p>Taramati</p>"
                        ],
                        options_hi: [
                            "<p>कामेट (Kamet)</p>",
                            "<p>धूपगढ़ (Dhupgarh)</p>",
                            "<p>कलसूबाई (Kalsubai)</p>",
                            "<p>तारामती (Taramati)</p>"
                        ],
                        solution_en: "<p>29.(a) <strong>Kamet</strong> (Chamoli district of Uttarakhand, 7,756 m). After Nanda Devi, it is considered to be the second tallest peak in the Garhwal region of Uttarakhand. Some Highest Peaks of the Himalayas: Mt. Everest (Nepal, 8848.86 m), Kanchenjunga (India, 8598 m), Makalu (Nepal, 8481 m), Dhaulagiri (Nepal, 8172 m). Mount Dhupgarh is located in the Satpura Range of Madhya Pradesh. Mount Kalsubai is part of the Sahyadri mountain range.</p>",
                        solution_hi: "<p>29.(a) <strong>कामेट</strong> (उत्तराखंड का चमोली जिला, 7,756 मीटर)। नंदा देवी के बाद, इसे उत्तराखंड के गढ़वाल क्षेत्र में दूसरी सबसे ऊंची चोटी माना जाता है। हिमालय की कुछ सबसे ऊंची चोटियाँ: माउंट एवरेस्ट (नेपाल, 8848.86 मीटर), कंचनजंगा (भारत, 8598 मीटर), मकालू (नेपाल, 8481 मीटर), धौलागिरी (नेपाल, 8172 मीटर)। पर्वत धूपगढ़ मध्य प्रदेश की सतपुड़ा श्रेणी में स्थित है। पर्वत कलसुबाई सह्याद्री पर्वत श्रेणी का हिस्सा है।</p>",
                        correct: "a",
                        pos_marks: 2.0,
                        neg_marks: 0.5
                    });
                    questions.push({
                        id: "30",
                        section: "18",
                        question_en: "<p>30. The federal scheme, Judiciary and Governors system of the Constitution of India are drawn from the ___________________.</p>",
                        question_hi: "<p>30. भारत के संविधान की संघीय योजना, न्यायपालिका और राज्यपाल प्रणाली ___________ से ली गई है।</p>",
                        options_en: [
                            "<p>Government of India Act, 1919</p>",
                            "<p>Government of India Act, 1935</p>",
                            "<p>Government of India Act of 1942</p>",
                            "<p>Government of India Act of 1945</p>"
                        ],
                        options_hi: [
                            "<p>भारत सरकार अधिनियम, 1919</p>",
                            "<p>भारत सरकार अधिनियम, 1935</p>",
                            "<p>1942 का भारत सरकार अधिनियम</p>",
                            "<p>1945 का भारत सरकार अधिनियम</p>"
                        ],
                        solution_en: "<p>30.(b) <strong>Government of India Act, 1935. </strong>The Act ended the Diarchy and provided for the establishment of the All India Federation. This was the longest Act passed under the British Act of Parliament. The Act introduced provincial autonomy (the powers between the center and provinces were divided in terms of three lists &ndash; Federal list, Provincial list and Concurrent list).</p>",
                        solution_hi: "<p>30.(b) <strong>भारत सरकार अधिनियम, 1935। </strong>इस अधिनियम ने द्वैध शासन को समाप्त कर दिया और अखिल भारतीय संघ की स्थापना का प्रावधान किया। यह अगस्त 1935 में संसद के ब्रिटिश अधिनियम के तहत पारित सबसे लंबा अधिनियम था। इस अधिनियम ने प्रांतीय स्वायत्तता की शुरुआत की (केंद्र और प्रांतों के बीच शक्तियों को तीन सूचियों - संघीय सूची, प्रांतीय सूची और समवर्ती सूची के अनुसार विभाजित किया गया था)।</p>",
                        correct: "b",
                        pos_marks: 2.0,
                        neg_marks: 0.5
                    });
                    questions.push({
                        id: "31",
                        section: "18",
                        question_en: "<p>31. Kalamandalam Gopi was awarded the Padma Shri for which form of dance?</p>",
                        question_hi: "<p>31. कलामंडलम गोपी को किस प्रकार के नृत्य के लिए पद्म श्री से सम्मानित किया गया था?</p>",
                        options_en: [
                            "<p>Bharatanatyam</p>",
                            "<p>Kuchipudi</p>",
                            "<p>Kathakali</p>",
                            "<p>Kathak</p>"
                        ],
                        options_hi: [
                            "<p>भरतनाट्यम</p>",
                            "<p>कुचिपुड़ी</p>",
                            "<p>कथकली</p>",
                            "<p>कथक</p>"
                        ],
                        solution_en: "<p>31.(c) <strong>Kathakali. </strong>Kalamandalam Gopi&rsquo;s Awards: Padma Shri (2009), and Sangeet Natak Akademi Awards (1987). Other Exponents in Kathakali - Kalamandalam Krishna Prasad, Kalamandalam Vasu Pisharody, Kalamandalam Kesavan Namboodiri and Kalanilayam Balakrishnan.</p>",
                        solution_hi: "<p>31.(c) <strong>कथकली।</strong> कलामंडलम गोपी के पुरस्कार: पद्म श्री (2009), और संगीत नाटक अकादमी पुरस्कार (1987)। कथकली के अन्य प्रतिपादक - कलामंडलम कृष्ण प्रसाद, कलामंडलम वासु पिशारोडी, कलामंडलम केसवन नंबूदिरी और कलानिलयम बालकृष्णन।</p>",
                        correct: "c",
                        pos_marks: 2.0,
                        neg_marks: 0.5
                    });
                    questions.push({
                        id: "32",
                        section: "18",
                        question_en: "<p>32. Which of the following soil varieties is favorable for the growth of mangroves and deltaic vegetation ?</p>",
                        question_hi: "<p>32. निम्नलिखित में से कौन-सी मृदा की किस्म मैंग्रोव और डेल्टाई वनस्पति के विकास के लिए अनुकूल है?</p>",
                        options_en: [
                            "<p>Desert soil</p>",
                            "<p>Fine clay and alluvial soil</p>",
                            "<p>Black soil</p>",
                            "<p>Laterite soil</p>"
                        ],
                        options_hi: [
                            "<p>मरू मृदा</p>",
                            "<p>महीन कणों वाली मृदा और जलोढ़ मृदा</p>",
                            "<p>काली मृदा</p>",
                            "<p>लेटराइट मृदा</p>"
                        ],
                        solution_en: "<p>32.(b) <strong>Fine clay and alluvial soil. </strong>Black Soil (also known as &lsquo;Regur Soil&rsquo; or the &lsquo;Cotton Soil&rsquo;) - Covers: Maharashtra, Madhya Pradesh, Gujarat, Andhra Pradesh and some parts of Tamil Nadu. It is rich in Lime, Iron, Magnesia, Alumina and Potash. Crops: Cotton, Wheat, Jowar, Linseed, Virginia tobacco, and Millet. Laterite soil - Red laterite soils in Tamil Nadu, Andhra Pradesh and Kerala are more suitable for tree crops like cashew nuts . It is rich in iron oxide and aluminum compounds.</p>",
                        solution_hi: "<p>32.(b) <strong>महीन कणों वाली मृदा और जलोढ़ मृदा।</strong> काली मृदा (\'रेगुर मिट्टी\' या \'काली कपास मिट्टी) - स्थान: महाराष्ट्र, मध्य प्रदेश, गुजरात, आंध्र प्रदेश और तमिलनाडु के कुछ हिस्से। समृद्ध: चूना, लोहा, मैग्नेशिया, एल्यूमिना और पोटाश। फसलें: कपास, गेहूं, ज्वार, अलसी, वर्जीनिया तंबाकू और बाजरा। लेटराइट मृदा- तमिलनाडु, आंध्र प्रदेश और केरल में लाल लेटराइट मृदा काजू जैसी वृक्ष फसलों के लिए अधिक उपयुक्त है। यह आयरन ऑक्साइड और एल्यूमीनियम यौगिकों से भरपूर है।</p>",
                        correct: "b",
                        pos_marks: 2.0,
                        neg_marks: 0.5
                    });
                    questions.push({
                        id: "33",
                        section: "18",
                        question_en: "<p>33. Which of the following schemes come under the Ministry of Commerce and Industry?</p>",
                        question_hi: "<p>33. निम्नलिखित में से कौन-सी योजना वाणिज्य और उद्योग मंत्रालय के तहत आती है?</p>",
                        options_en: [
                            "<p>Swachh Bharat Abhiyan</p>",
                            "<p>National Digital Health Mission</p>",
                            "<p>National Bal Swachhta</p>",
                            "<p>Make in India</p>"
                        ],
                        options_hi: [
                            "<p>स्वच्छ भारत अभियान</p>",
                            "<p>राष्ट्रीय डिजिटल स्वास्थ्य मिशन</p>",
                            "<p>राष्ट्रीय बाल स्वच्छता</p>",
                            "<p>मेक इन इंडिया</p>"
                        ],
                        solution_en: "<p>33.(d) <strong>Make in India</strong>: Launched - 25 September 2014. Other Schemes and projects: National Digital Health Mission: Launched - 15th August 2020 under Ministry of Health and Family Welfare. National Bal Swachhta: 14th November 2014 under Women and Child Development ministry.</p>",
                        solution_hi: "<p>33.(d) <strong>मेक इन इंडिया: </strong>प्रारंभ - 25 सितंबर 2014। अन्य योजनाएं और परियोजनाएं: राष्ट्रीय डिजिटल स्वास्थ्य मिशन: स्वास्थ्य और परिवार कल्याण मंत्रालय के तहत 15 अगस्त 2020 को शुरू किया गया। राष्ट्रीय बाल स्वच्छता: 14 नवंबर 2014 महिला एवं बाल विकास मंत्रालय के अंतर्गत।</p>",
                        correct: "d",
                        pos_marks: 2.0,
                        neg_marks: 0.5
                    });
                    questions.push({
                        id: "34",
                        section: "18",
                        question_en: "<p>34. Direct change of gas to solid without changing into liquid is called __________.</p>",
                        question_hi: "<p>34. गैस अवस्था से द्रव अवस्था में परिवर्तित हुए बिना सीधे ठोस अवस्था में परिवर्तन __________ कहलाता है?</p>",
                        options_en: [
                            "<p>Vaporization</p>",
                            "<p>Deposition</p>",
                            "<p>Evaporation</p>",
                            "<p>Sublimation</p>"
                        ],
                        options_hi: [
                            "<p>वाष्पीकरण</p>",
                            "<p>निक्षेपण</p>",
                            "<p>वाष्पीकरण</p>",
                            "<p>उर्ध्वपातन</p>"
                        ],
                        solution_en: "<p>34.(b) <strong>Deposition.</strong> Vaporization - It is the process of changing a substance from liquid to gas. Evaporation - It is a process by which water is transformed from liquid to gaseous state. Sublimation - The process of converting of a substance from solid to gas directly, without passing through the liquid phase.</p>",
                        solution_hi: "<p>34.(b) <strong>निक्षेपण।</strong> वाष्पीकरण - यह किसी पदार्थ को द्रव से गैस में बदलने की प्रक्रिया है। वाष्पन - यह एक ऐसी प्रक्रिया है जिसके द्वारा जल , द्रव से गैसीय अवस्था में परिवर्तित होता है। उर्ध्वपातन - किसी पदार्थ को तरल अवस्था से गुजरे बिना, सीधे ठोस से <br>गैस में परिवर्तित करने की प्रक्रिया है।</p>",
                        correct: "b",
                        pos_marks: 2.0,
                        neg_marks: 0.5
                    });
                    questions.push({
                        id: "35",
                        section: "18",
                        question_en: "<p>35. Who among the following established the Ramakrishna Mission in 1897?</p>",
                        question_hi: "<p>35. निम्नलिखित में से किसने 1897 में रामकृष्ण मिशन की स्थापना की थी?</p>",
                        options_en: [
                            "<p>Swami Dayananda Saraswati</p>",
                            "<p>Swami Shraddananda</p>",
                            "<p>Swami Vivekananda</p>",
                            "<p>Swami Acchutananda</p>"
                        ],
                        options_hi: [
                            "<p>स्वामी दयानंद सरस्वती</p>",
                            "<p>स्वामी श्रद्धानंद</p>",
                            "<p>स्वामी विवेकानंद</p>",
                            "<p>स्वामी अच्युतानंद</p>"
                        ],
                        solution_en: "<p>35.(c) <strong>Swami Vivekananda - </strong>His real name is Narendranath Dutt. He participated in the parliament of the World&rsquo;s Religions in 1893 AD at Chicago (USA). In 1899, he established the Belur Math (West Bengal). The spiritual Guru Of Swami Vivekanand - Ramakrishna Paramahansa. Netaji Subhas Chandra Bose had called Vivekananda the &ldquo;maker of modern India.&rdquo; Books - &lsquo;Raja-yoga&rsquo;, &lsquo;Karma-yoga&rsquo;, &lsquo;Jnana-yoga&rsquo; and &lsquo;Bhakti-yoga&rsquo;.</p>",
                        solution_hi: "<p>35.(c) <strong>स्वामी विवेकानन्द - </strong>उनका वास्तविक नाम नरेन्द्रनाथ दत्त है। उन्होंने 1893 ई. में शिकागो (अमेरिका) में आयोजित विश्व धर्म संसद में भाग लिया था। 1899 में उन्होंने बेलूर मठ (पश्चिम बंगाल) की स्थापना की थी। स्वामी विवेकानन्द के आध्यात्मिक गुरु - रामकृष्ण परमहंस जी थे। नेताजी सुभाष चंद्र बोस ने स्वामी विवेकानन्द जी को \"आधुनिक भारत का निर्माता\" कहा था। उनकी प्रसिद्ध पुस्तकें - \'राज-योग\', \'कर्म-योग\', \'ज्ञान-योग\' और \'भक्ति-योग\'।</p>",
                        correct: "c",
                        pos_marks: 2.0,
                        neg_marks: 0.5
                    });
                    questions.push({
                        id: "36",
                        section: "18",
                        question_en: "<p>36. Which of the following Articles of the Indian Constitution are related to the Comptroller and Auditor General of India?</p>",
                        question_hi: "<p>36. भारतीय संविधान के निम्नलिखित में से कौन-से अनुच्छेद भारत के नियंत्रक और महालेखा परीक्षक से संबंधित हैं?</p>",
                        options_en: [
                            "<p>Article 147-150</p>",
                            "<p>Article 148-154</p>",
                            "<p>Article 148-152</p>",
                            "<p>Article 148-151</p>"
                        ],
                        options_hi: [
                            "<p>अनुच्छेद 147-150</p>",
                            "<p>अनुच्छेद 148-154</p>",
                            "<p>अनुच्छेद 148-152</p>",
                            "<p>अनुच्छेद 148-151</p>"
                        ],
                        solution_en: "<p>36.(d) <strong>Article 148-151.</strong> The CAG handles the audit of the accounts of the Union and State governments. Article 148: Establishes the office of the Comptroller and Auditor General of India. Article 149: Duties and powers of the Comptroller and Auditor-General. Article 150: Form of accounts of the Union and of the States. Article 151: Mandates the presentation of audit reports by the Comptroller and Auditor General to the President of India, which are then laid before Parliament.</p>",
                        solution_hi: "<p>36.(d) <strong>अनुच्छेद 148-151.</strong> CAG संघ और राज्य सरकारों के खातों की लेखापरीक्षा का कार्य करता है। अनुच्छेद 148: भारत के नियंत्रक एवं महालेखा परीक्षक के कार्यालय की स्थापना करता है। अनुच्छेद 149: नियंत्रक एवं महालेखा परीक्षक के कर्तव्य और शक्तियाँ। अनुच्छेद 150: संघ और राज्यों के लेखाओं का प्रारूप। अनुच्छेद 151: नियंत्रक एवं महालेखा परीक्षक द्वारा भारत के राष्ट्रपति को लेखापरीक्षा रिपोर्ट प्रस्तुत करने का आदेश देता है, जिसे बाद में संसद के समक्ष रखा जाता है।</p>",
                        correct: "d",
                        pos_marks: 2.0,
                        neg_marks: 0.5
                    });
                    questions.push({
                        id: "37",
                        section: "18",
                        question_en: "<p>37. Who was the famous Venetian traveller of the 13th century who visited Kerala?</p>",
                        question_hi: "<p>37. 13वीं शताब्दी का वह प्रसिद्ध विनीशियन (Venetian) यात्री कौन था, जिसने केरल की यात्रा की थी?</p>",
                        options_en: [
                            "<p>Abdur Razzaq</p>",
                            "<p>Marco Polo</p>",
                            "<p>Barthema</p>",
                            "<p>Nicolo Conti</p>"
                        ],
                        options_hi: [
                            "<p>अब्दुर रज्जाक</p>",
                            "<p>मार्को पोलो</p>",
                            "<p>बारथेमा</p>",
                            "<p>निकोलो कोंटी</p>"
                        ],
                        solution_en: "<p>37.(b) <strong>Marco Polo. </strong>He visited Southern India during the reigns of Rudramma Devi of the Kakatiyas and Pandyan ruler Madverman. Abdur Razzaq, a Persian ambassador sent by Shah Rukh of the Timurid Empire, visited Calicut in 1442 CE during the Zamorin\'s reign. Nicolo Conti, a Venetian merchant and explorer, spent around 25 years travelling in the Indian Ocean region during the 15th century.</p>",
                        solution_hi: "<p>37.(b) <strong>मार्को पोलो।</strong> उन्होंने काकतीय शासक रुद्रम्मा देवी और पांड्य शासक मदवर्मन के शासनकाल के दौरान दक्षिण भारत का दौरा किया। तिमुरी साम्राज्य के शाहरुख द्वारा भेजे गए फारसी राजदूत अब्दुर रज्जाक ने ज़मोरिन के शासनकाल के दौरान 1442 ई. में कालीकट का दौरा किया। वेनिस के व्यापारी और खोजकर्ता निकोलो कोंटी ने 15वीं शताब्दी के दौरान हिंद महासागर क्षेत्र में लगभग 25 वर्ष यात्रा की।</p>",
                        correct: "b",
                        pos_marks: 2.0,
                        neg_marks: 0.5
                    });
                    questions.push({
                        id: "38",
                        section: "18",
                        question_en: "<p>38. Which of the following states is associated with the devotional folk dance \'Gambhira\'?</p>",
                        question_hi: "<p>38. निम्नलिखित में से कौन सा राज्य भक्ति लोक नृत्य \'गम्भीरा\' से संबंधित है?</p>",
                        options_en: [
                            "<p>Rajasthan</p>",
                            "<p>Bihar</p>",
                            "<p>Odisha</p>",
                            "<p>West Bengal</p>"
                        ],
                        options_hi: [
                            "<p>राजस्थान</p>",
                            "<p>बिहार</p>",
                            "<p>ओडिशा</p>",
                            "<p>पश्चिम बंगाल</p>"
                        ],
                        solution_en: "<p>38.(d) <strong>West Bengal.</strong> The Gambhira dance is performed during the festival of Chaitra Sankranti. Dance and their states : West Bengal - Baul, Fakir, Bitra, Lathi. Rajasthan - Ghoomar, Suisini, Ghapal, Kalbeliya. Odisha - Odissi (Classical Nritya), Savari, Ghumara, Painka. Bihar - Jata-Jatin, Bakho-Bakhain, Panwariya, Sama Chakwa.</p>",
                        solution_hi: "<p>38.(d) <strong>पश्चिम बंगाल।</strong> गंभीरा नृत्य चैत्र संक्रांति के त्योहार के दौरान आयोजित किया जाता है। नृत्य और उनके राज्य: पश्चिम बंगाल - बाउल, फकीर, बितरा, लाठी। राजस्थान - घूमर, सुइसिनी, घपाल, कालबेलिया। ओडिशा - ओडिसी (शास्त्रीय नृत्य), सावरी, घुमरा, पैंका। बिहार - जट-जटिन, बखो-बखैन, पंवरिया, सामा चकवा।</p>",
                        correct: "d",
                        pos_marks: 2.0,
                        neg_marks: 0.5
                    });
                    questions.push({
                        id: "39",
                        section: "18",
                        question_en: "<p>39. In cricket, the field is oval with a rectangular area in the middle, known as &lsquo;pitch&rsquo;, which is ______ wide.</p>",
                        question_hi: "<p>39. क्रिकेट में, मैदान अंडाकार होता है जिसके बीच में एक आयताकार क्षेत्र होता है, जिसे \'पिच\' के रूप में जाना जाता है, जिसका आकार ______ होता है।</p>",
                        options_en: [
                            "<p>22 yards (20.12 m) by 10 feet (3.04 m)</p>",
                            "<p>15 yards (15.11 m) by 8 feet (2.04 m)</p>",
                            "<p>11 yards (10.97 m) by 9 feet (2.26 m)</p>",
                            "<p>13 yards (13.52 m) by 7 feet (1.84 m)</p>"
                        ],
                        options_hi: [
                            "<p>22 गज (20.12 m) &times;10 फीट (3.04 m)</p>",
                            "<p>15 गज (15.11 m) &times; 8 फीट (2.04 m)</p>",
                            "<p>11 गज (10.97 m) &times; 9 फीट (2.26 m)</p>",
                            "<p>13 गज (13.52 m) &times; 7 फीट (1.84 m)</p>"
                        ],
                        solution_en: "<p>39.(a) <strong>22 yards (20.12 m) by 10 feet (3.04 m). </strong>Turf cricket pitch: Dimensions - 20.12 m long (from stump to stump) plus a minimum of 1.22 m behind the stumps. Synthetic pitches: Dimensions - from 25 m to 28 m long and 2.4 m to 2.8 m wide. Bowling crease: 2.64 m long. Cricket Ball: Diameter between 2.8 inch -2.86 inch (7.1-7.26 cm) and circumference from 8.81 inch -9 inch (224-229 mm). The mass of a Cricket Ball is between 156-163 g.</p>",
                        solution_hi: "<p>39.(a) <strong>22 गज (20.12 m) &times; 10 फीट (3.04 m)।</strong> टर्फ क्रिकेट पिच: आयाम - 20.12 मीटर लंबा (स्टंप से स्टंप तक) और स्टंप के पीछे न्यूनतम 1.22 मीटर। सिंथेटिक पिचें: आयाम - 25 मीटर से 28 मीटर तक लंबी और 2.4 मीटर से 2.8 मीटर तक चौड़ी। बॉलिंग क्रीज़: 2.64 मीटर लंबा। क्रिकेट बॉल: व्यास 2.8 इंच -2.86 इंच (7.1-7.26 सेमी) और परिधि 8.81 इंच - 9 इंच (224-229 मिमी) के बीच। क्रिकेट बॉल का द्रव्यमान 156-163 ग्राम के बीच होता है।</p>",
                        correct: "a",
                        pos_marks: 2.0,
                        neg_marks: 0.5
                    });
                    questions.push({
                        id: "40",
                        section: "18",
                        question_en: "<p>40. To which group do the alkaline earth metals such as radium, barium and strontium belong?</p>",
                        question_hi: "<p>40. रेडियम, बेरियम और स्ट्रोंशियम जैसी क्षारीय मृदा धातुएं किस समूह से संबंधित हैं?</p>",
                        options_en: [
                            "<p>Group 5A</p>",
                            "<p>Group 2A</p>",
                            "<p>Group 1A</p>",
                            "<p>Group 3A</p>"
                        ],
                        options_hi: [
                            "<p>समूह 5A</p>",
                            "<p>समूह 2A</p>",
                            "<p>समूह 1A</p>",
                            "<p>समूह 3A</p>"
                        ],
                        solution_en: "<p>40.(b) <strong>Group 2A.</strong> Alkaline earth metals belong to Group 2A (or Group II) of the periodic table, including : Radium (Ra), Barium (Ba), Strontium (Sr), Calcium (Ca), Magnesium (Mg), Beryllium (Be). Group 1A (Alkali Metals) : This group contains elements such as lithium (Li), sodium (Na), and potassium (K). Group 3A (Boron Group) : This group includes elements like boron (B), aluminum (Al), and gallium (Ga). Group 5A (Nitrogen Group) : This group includes elements like nitrogen (N), phosphorus (P), and arsenic (As).</p>",
                        solution_hi: "<p>40.(b) <strong>समूह 2A.</strong> क्षारीय मृदा धातुएँ आवर्त सारणी के समूह 2A (या समूह II) से संबंधित हैं, जिनमें शामिल हैं: रेडियम (Ra), बेरियम (Ba), स्ट्रोंटियम (Sr), कैल्शियम (Ca), मैग्नीशियम (Mg), बेरिलियम (Be)। समूह 1A (क्षार धातुएँ): इस समूह में लिथियम (Li), सोडियम (Na), और पोटेशियम (K) जैसे तत्व शामिल हैं। समूह 3A (बोरॉन समूह): इस समूह में बोरॉन (B), एल्युमिनियम (Al), और गैलियम (Ga) जैसे तत्व शामिल हैं। समूह 5A (नाइट्रोजन समूह): इस समूह में नाइट्रोजन (N), फॉस्फोरस (P), और आर्सेनिक (As) जैसे तत्व शामिल हैं।</p>",
                        correct: "b",
                        pos_marks: 2.0,
                        neg_marks: 0.5
                    });
                    questions.push({
                        id: "41",
                        section: "18",
                        question_en: "<p>41. Bankim Chandra Chatterjee wrote a novel Anand Math based on which of the following&nbsp;rebellions/revolts?</p>",
                        question_hi: "<p>41. बंकिमचंद्र चटर्जी ने निम्नलिखित में से किस आंदोलन/विद्रोह पर आधारित उपन्यास आनंद मठ लिखा?</p>",
                        options_en: [
                            "<p>Paika Rebellion</p>",
                            "<p>Sanyasi Rebellion</p>",
                            "<p>Kuki Revolt</p>",
                            "<p>Mappila Rebellion</p>"
                        ],
                        options_hi: [
                            "<p>पाइका विद्रोह</p>",
                            "<p>संन्यासी विद्रोह</p>",
                            "<p>कुकी विद्रोह</p>",
                            "<p>मोपला विद्रोह</p>"
                        ],
                        solution_en: "<p>41.(b) <strong>Sanyasi Rebellion.</strong> The Sanyasi Uprisings took place in Bengal between the periods of 1770- 1820s. The Sanyasis rose in rebellion after the great famine of 1770 in Bengal which caused acute chaos and misery. However, the immediate cause of the rebellion was the restrictions imposed by the British upon pilgrims visiting holy places among both Hindus and Muslims. Bankim Chandra Chatterjee notable work: Durgeshnandini, Kapalkundala, Devi Chaudhurani, Bishabriksha.</p>",
                        solution_hi: "<p>41.(b) <strong>संन्यासी विद्रोह। </strong>संन्यासी विद्रोह बंगाल में 1770-1820 के बीच हुआ था। बंगाल में 1770 के भीषण अकाल के बाद संन्यासियों ने विद्रोह कर दिया था, जिससे भयंकर अराजकता और दुख उत्पन्न हो गया था। हालाँकि, विद्रोह का तात्कालिक कारण अंग्रेजों द्वारा हिंदुओं और मुसलमानों दोनों के पवित्र स्थानों पर जाने वाले तीर्थयात्रियों पर लगाए गए प्रतिबंध थे। बंकिम चंद्र चटर्जी की उल्लेखनीय कृतियाँ: दुर्गेशनंदिनी, कपालकुंडला, देवी चौधुरानी, ​​बिशबृक्षा।</p>",
                        correct: "b",
                        pos_marks: 2.0,
                        neg_marks: 0.5
                    });
                    questions.push({
                        id: "42",
                        section: "18",
                        question_en: "<p>42. Which south-eastern Dravidian language has been approved by Odisha state to be included in the Eighth Schedule of the Constitution of India, on 10th July 2023 ?</p>",
                        question_hi: "<p>42. 10 जुलाई 2023 को ओडिशा राज्य ने किस दक्षिण-पूर्वी द्रविड़ भाषा को भारत के संविधान की आठवीं&nbsp;अनुसूची में शामिल करने की मंजूरी दे दी है?</p>",
                        options_en: [
                            "<p>Kui</p>",
                            "<p>Malto</p>",
                            "<p>Tulu</p>",
                            "<p>Kodagu</p>"
                        ],
                        options_hi: [
                            "<p>कुई (Kui)</p>",
                            "<p>माल्टो (Malto)</p>",
                            "<p>तुलु (Tulu)</p>",
                            "<p>कोडागु (Kodagu)</p>"
                        ],
                        solution_en: "<p>42.(a) <strong>Kui. </strong>It is spoken by the Kandhas community. It is closely related to the Gondi and Kuvi languages. Some other languages included in the eighth schedule are: Sindhi was introduced in 1967. Konkani, Manipuri, and Nepali in 1992. Santali, Dogri, Maithili, and Bodo in 2003.</p>",
                        solution_hi: "<p>42.(a) <strong>कुई।</strong> यह कंधस समुदाय द्वारा बोली जाती है। यह गोंडी और कुवी भाषाओं से निकटता से संबंधित है। आठवीं अनुसूची में शामिल कुछ अन्य भाषाएँ: सिंधी को 1967 में शामिल किया गया था। कोंकणी, मणिपुरी और नेपाली को 1992 में शामिल किया गया। संथाली, डोगरी, मैथिली और बोडो को 2003 में शामिल किया गया।</p>",
                        correct: "a",
                        pos_marks: 2.0,
                        neg_marks: 0.5
                    });
                    questions.push({
                        id: "43",
                        section: "18",
                        question_en: "<p>43. What is the target number of solar installations under the Pradhan Mantri Surya Ghar Muft Bijli Yojana by 2027?</p>",
                        question_hi: "<p>43. प्रधानमंत्री सूर्या घर मुफ्त बिजली योजना के तहत 2027 तक कितनी सौर ऊर्जा प्रणालियों की स्थापना का लक्ष्य है?</p>",
                        options_en: [
                            "<p>50 lakh</p>",
                            "<p>75 lakh</p>",
                            "<p>1 crore</p>",
                            "<p>1.5 crore</p>"
                        ],
                        options_hi: [
                            "<p>50 लाख</p>",
                            "<p>75 लाख</p>",
                            "<p>1 करोड़</p>",
                            "<p>1.5 करोड़</p>"
                        ],
                        solution_en: "<p>43.(c) <strong>1 crore.</strong> The Pradhan Mantri Surya Ghar Muft Bijli Yojana aims to achieve 1 crore solar installations by 2027, providing up to 300 units of free electricity to households.</p>",
                        solution_hi: "<p>43.(c) <strong>1 करोड़। </strong>प्रधानमंत्री सूर्या घर मुफ्त बिजली योजना का उद्देश्य 2027 तक 1 करोड़ सौर ऊर्जा प्रणालियों की स्थापना करना है, जिससे घरों को 300 यूनिट तक मुफ्त बिजली मिल सके।</p>",
                        correct: "c",
                        pos_marks: 2.0,
                        neg_marks: 0.5
                    });
                    questions.push({
                        id: "44",
                        section: "18",
                        question_en: "<p>44. Varahamihira\'s work Panchasiddhantika deals with __________ astronomical systems.</p>",
                        question_hi: "<p>44. वराहमिहिर की रचना पंचसिद्धांतिका _______खगोलीय सिद्धांतों (astronomical systems) से संबंधित है।</p>",
                        options_en: [
                            "<p>three</p>",
                            "<p>two</p>",
                            "<p>four</p>",
                            "<p>five</p>"
                        ],
                        options_hi: [
                            "<p>तीन</p>",
                            "<p>दो</p>",
                            "<p>चार</p>",
                            "<p>पांच</p>"
                        ],
                        solution_en: "<p>44.(d) <strong>five.</strong> Varahamihira was an Indian philosopher, astronomer, and mathematician who served in the court of Chandragupta II. He was born in Ujjain. Varahamihira is famous for his works, including Brihat Jataka, Brihat Samhita, and Pancha-Siddhantika.</p>",
                        solution_hi: "<p>44.(d) <strong>पाँच। </strong>वराहमिहिर एक भारतीय दार्शनिक, खगोलशास्त्री और गणितज्ञ थे, जो चंद्रगुप्त द्वितीय के दरबार में सेवा करते थे। इनका जन्म उज्जैन में हुआ था। वराहमिहिर अपनी रचनाओं के लिए प्रसिद्ध हैं, जिनमें बृहत् जातक, बृहत् संहिता, और पंच-सिद्धांतिका शामिल हैं।</p>",
                        correct: "d",
                        pos_marks: 2.0,
                        neg_marks: 0.5
                    });
                    questions.push({
                        id: "45",
                        section: "18",
                        question_en: "<p>45. Which vitamin prevents the neural tube defect in new-born babies?</p>",
                        question_hi: "<p>45. कौन-सा विटामिन नवजात शिशुओं में तंत्रिका ट्यूब दोष को रोकता है?</p>",
                        options_en: [
                            "<p>Ascorbic acid</p>",
                            "<p>Riboflavin</p>",
                            "<p>Niacin</p>",
                            "<p>Folic acid</p>"
                        ],
                        options_hi: [
                            "<p>एस्कॉर्बिक अम्ल</p>",
                            "<p>राइबोफ्लेविन</p>",
                            "<p>नियासिन</p>",
                            "<p>फोलिक अम्ल</p>"
                        ],
                        solution_en: "<p>45.(d) <strong>Folic acid. </strong>It is also known as vitamin B9, plays a crucial role in preventing neural tube defects (NTDs) in newborn babies. NTDs are birth defects that affect the brain and spine, such&nbsp;as Spina bifida, Anencephaly. Ascorbic acid (Vitamin C) is a water soluble vitamin well known for its role in supporting a healthy immune system. Riboflavin (Vitamin B2) and Niacin (Vitamin B3) are water soluble vitamins essential for energy production and metabolism.</p>",
                        solution_hi: "<p>45.(d) <strong>फोलिक अम्ल।</strong> इसे विटामिन B9 के नाम से भी जाना जाता है, यह नवजात शिशुओं में न्यूरल ट्यूब दोष (NTD) को रोकने में महत्वपूर्ण भूमिका निभाता है। NTD जन्म दोष हैं जो मस्तिष्क और रीढ़ को प्रभावित करते हैं, जैसे कि स्पाइना बिफिडा, एनेनसेफली। एस्कॉर्बिक अम्ल (विटामिन C) जल में घुलनशील विटामिन है जो स्वस्थ प्रतिरक्षा प्रणाली का समर्थन करने में अपनी भूमिका के लिए जाना जाता है। राइबोफ्लेविन (विटामिन B2) और नियासिन (विटामिन B3) जल में घुलनशील विटामिन हैं जो ऊर्जा उत्पादन और उपापचय के लिए आवश्यक हैं।</p>",
                        correct: "d",
                        pos_marks: 2.0,
                        neg_marks: 0.5
                    });
                    questions.push({
                        id: "46",
                        section: "18",
                        question_en: "<p>46. In 1991, India met with an economic crisis where the _________reserves fell so low that they were not sufficient for even a _________ .</p>",
                        question_hi: "<p>46. 1991 में, भारत एक आर्थिक संकट से जूझ रहा था, जिस समय _________ भंडार इतना कम हो गया था कि वह एक _________ के लिए भी पर्याप्त नहीं था।</p>",
                        options_en: [
                            "<p>foreign exchange; quarter</p>",
                            "<p>gold; month</p>",
                            "<p>gold; year</p>",
                            "<p>foreign exchange; fortnight</p>"
                        ],
                        options_hi: [
                            "<p>विदेशी मुद्रा; तिमाही</p>",
                            "<p>स्वर्ण; माह</p>",
                            "<p>स्वर्ण; वर्ष</p>",
                            "<p>विदेशी मुद्रा; पखवाड़े</p>"
                        ],
                        solution_en: "<p>46.(d) <strong>foreign exchange; fortnight.</strong> India&rsquo;s balance of payments in 1990-91 also suffered from capital account problems due to a loss of investor confidence. The average rate of inflation was 10% in the year 1990-91. It crossed 13 percent in 1991-92. The GDP growth rate which was 6.5 percent in 1989-90, went down to 5.5 percent in 1990-91.</p>",
                        solution_hi: "<p>46.(d) <strong>विदेशी मुद्रा; पखवाड़ा।</strong> 1990-91 में भारत का भुगतान संतुलन भी निवेशकों के विश्वास की हानि के कारण पूंजी खाते की समस्याओं से ग्रस्त था। वर्ष 1990-91 में मुद्रास्फीति की औसत दर 10% थी। 1991-92 में यह 13% को पार कर गया। सकल घरेलू उत्पाद की वृद्धि दर जो 1989-90 में 6.5% थी, 1990-91 में घटकर 5.5% हो गई।</p>",
                        correct: "d",
                        pos_marks: 2.0,
                        neg_marks: 0.5
                    });
                    questions.push({
                        id: "47",
                        section: "18",
                        question_en: "<p>47. In which year was the \'Prime Minister Street Vendor\'s AtmaNirbhar Nidhi\' (PM SVANidhi) scheme launched by the Government of India with the objective of providing an affordable working capital loan to street vendors to resume their livelihoods ?</p>",
                        question_hi: "<p>47. भारत सरकार द्वारा स्ट्रीट वेंडर्स को अपनी आजीविका फिर से शुरू करने के लिए एक किफायती कार्यशील पूंजी ऋण प्रदान करने के उद्देश्य से प्रधान मंत्री स्ट्रीट वेंडर्स आत्मनिर्भर निधि (पी.एम. स्वनिधि-PM SVANidhi) योजना किस वर्ष में शुरू की गई थी?</p>",
                        options_en: [
                            "<p>2022</p>",
                            "<p>2020</p>",
                            "<p>2021</p>",
                            "<p>2019</p>"
                        ],
                        options_hi: [
                            "<p>2022</p>",
                            "<p>2020</p>",
                            "<p>2021</p>",
                            "<p>2019</p>"
                        ],
                        solution_en: "<p>47.(b) <strong>2020.</strong> Prime Minister Street Vendor&rsquo;s AtmaNirbhar Nidhi (PM-SVANidhi) fully funded by the Ministry of Housing and Urban Affairs. The scheme facilitates a working capital collateral-free loan of ₹10,000, with subsequent loans of ₹20,000 and ₹50,000 with a 7% interest subsidy.</p>",
                        solution_hi: "<p>47.(b) <strong>2020.</strong> प्रधानमंत्री स्ट्रीट वेंडर आत्मनिर्भर निधि (PM-SVANidhi) पूरी तरह से आवास और शहरी मामलों के मंत्रालय द्वारा वित्त पोषित है। इस योजना के तहत ₹10,000 का कार्यशील पूंजी संपार्श्विक-मुक्त ऋण दिया जाता है, जिसके बाद 7% ब्याज सब्सिडी के साथ 20,000 और 50,000 रुपये के ऋण उपलब्ध कराए जाते हैं।</p>",
                        correct: "b",
                        pos_marks: 2.0,
                        neg_marks: 0.5
                    });
                    questions.push({
                        id: "48",
                        section: "18",
                        question_en: "<p>48. Where was the 112th International Labour Organization (ILC) held in 2024?</p>",
                        question_hi: "<p>48. 2024 में 112वां अंतर्राष्ट्रीय श्रम संगठन (ILC) कहाँ आयोजित किया गया था?</p>",
                        options_en: [
                            "<p>New York, USA</p>",
                            "<p>Geneva, Switzerland</p>",
                            "<p>London, UK</p>",
                            "<p>Paris, France</p>"
                        ],
                        options_hi: [
                            "<p>न्यूयॉर्क, यूएसए</p>",
                            "<p>जिनेवा, स्विटजरलैंड</p>",
                            "<p>लंदन, यूके</p>",
                            "<p>पेरिस, फ्रांस</p>"
                        ],
                        solution_en: "<p>48.(b) <strong>Geneva, Switzerland.</strong> The 112th ILC in June 2024 was attended by more than 4,900 delegates representing Governments, Employers\' organizations, and Workers\' organizations. The International Labour Organization (ILC) primarily focuses on labour rights, workforce-related issues, and the promotion of decent work conditions for all workers globally.</p>",
                        solution_hi: "<p>48.(b) <strong>जिनेवा, स्विटजरलैंड।</strong> जून 2024 में 112वें ILC में सरकारों, नियोक्ता संगठनों और श्रमिक संगठनों का प्रतिनिधित्व करने वाले 4,900 से अधिक प्रतिनिधियों ने भाग लिया था। अंतर्राष्ट्रीय श्रम संगठन (ILC) मुख्य रूप से श्रम अधिकारों, कार्यबल से संबंधित मुद्दों और वैश्विक स्तर पर सभी श्रमिकों के लिए सभ्य कार्य स्थितियों को बढ़ावा देने पर ध्यान केंद्रित करता है।</p>",
                        correct: "b",
                        pos_marks: 2.0,
                        neg_marks: 0.5
                    });
                    questions.push({
                        id: "49",
                        section: "18",
                        question_en: "<p>49. The National Youth Day or the &lsquo;Yuwa Diwas&rsquo; is celebrated on which day in India ?</p>",
                        question_hi: "<p>49. राष्ट्रीय युवा दिवस या \'युवा दिवस\' भारत में किस दिन मनाया जाता है ?</p>",
                        options_en: [
                            "<p>26 January</p>",
                            "<p>12 January</p>",
                            "<p>14 April</p>",
                            "<p>26 November</p>"
                        ],
                        options_hi: [
                            "<p>26 जनवरी को</p>",
                            "<p>12 जनवरी को</p>",
                            "<p>14 अप्रैल को</p>",
                            "<p>26 नवंबर को</p>"
                        ],
                        solution_en: "<p>49.(b) <strong>12 January.</strong> The day is observed to commemorate the birth anniversary of Swami Vivekananda. Important Dates and Days : 31st October - National Unity Day (Celebrates the birth of Sardar Vallabhbhai Patel), 5th September - Teachers\' Day (Celebrates the birth of Dr. Sarvepalli Radhakrishnan), 14th November - Children\'s Day (Celebrates the birth of Jawaharlal Nehru), 30th January - Martyrs\' Day (Observed to commemorate the death of Mahatma Gandhi).</p>",
                        solution_hi: "<p>49.(b) <strong>12 जनवरी।</strong> यह दिवस स्वामी विवेकानंद की जयंती के उपलक्ष्य में मनाया जाता है। महत्वपूर्ण तिथियाँ और दिवस : 31 अक्टूबर - राष्ट्रीय एकता दिवस (सरदार वल्लभभाई पटेल की जयंती), 5 सितंबर - शिक्षक दिवस (डॉ. सर्वपल्ली राधाकृष्णन की जयंती), 14 नवंबर - बाल दिवस (जवाहरलाल नेहरू की जयंती), 30 जनवरी - शहीद दिवस (महात्मा गांधी की पुण्यतिथि)।</p>",
                        correct: "b",
                        pos_marks: 2.0,
                        neg_marks: 0.5
                    });
                    questions.push({
                        id: "50",
                        section: "17",
                        question_en: "<p>50. Which condition, also known as icterus, causes a yellowing of your skin and the whites of your eyes?</p>",
                        question_hi: "<p>50. कौन सी स्थिति, जिसे पीलिया (इक्टेरस) के रूप में भी जाना जाता है, आपकी त्वचा के पीलेपन और आपकी आंखों के सफेद होने का कारण बनती है?</p>",
                        options_en: [
                            "<p>Ichthyosis</p>",
                            "<p>Jaundice</p>",
                            "<p>Eczema</p>",
                            "<p>Pemphigus</p>"
                        ],
                        options_hi: [
                            "<p>इक्थियोसिस</p>",
                            "<p>जॉन्डिस</p>",
                            "<p>एक्जिमा</p>",
                            "<p>पेम्फीगस</p>"
                        ],
                        solution_en: "<p>50.(b) <strong>Jaundice.</strong> This yellowing occurs due to high levels of bilirubin in the blood. Bilirubin is a yellow pigment produced during the normal breakdown of red blood cells. Ichthyosis is a group of genetic skin disorders that cause dry, scaly, thickened skin. Eczema is a common skin condition that causes itchiness, rashes, dry patches, and infection. Pemphigus is a rare group of autoimmune diseases that cause blisters on the skin and mucous membranes.</p>",
                        solution_hi: "<p>50.(b)<strong> जॉन्डिस।</strong> यह पीलापन रक्त में बिलिरुबिन के उच्च स्तर के कारण होता है। बिलिरुबिन एक पीला वर्णक है जो लाल रक्त कोशिकाओं के सामान्य टूटने के दौरान उत्पन्न होता है। इक्थियोसिस आनुवंशिक त्वचा विकारों का एक समूह है जो शुष्क, परतदार, मोटी त्वचा का कारण बनता है। एक्जिमा एक सामान्य त्वचा की स्थिति है जो खुजली, दाने, सूखे धब्बे, और संक्रमण का कारण बनती है। पेम्फिगस ऑटोइम्यून बीमारियों का एक दुर्लभ समूह है जो त्वचा और श्लेष्म झिल्ली पर छाले उत्पन्न करता है।</p>",
                        correct: "b",
                        pos_marks: 2.0,
                        neg_marks: 0.5
                    });
                    questions.push({
                        id: "51",
                        section: "17",
                        question_en: "<p>51. Shankar covers a certain distance by a car driving at 40 km/h and he returns to the starting point riding on a scooter with a speed of 20 km/h. Find the average speed of the whole journey.</p>",
                        question_hi: "<p>51. शंकर कार द्वारा 40 km/h की चाल से एक निश्चित दूरी तय करता है और वह स्कूटर पर सवार होकर 20&nbsp;km/h की चाल से प्रारंभिक बिंदु पर लौटता है। पूरी यात्रा की औसत चाल बताएं |</p>",
                        options_en: [
                            "<p>22<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> km/h</p>",
                            "<p>29<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> km/h</p>",
                            "<p>24<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> km/h</p>",
                            "<p>26<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> km/h</p>"
                        ],
                        options_hi: [
                            "<p>22<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> km/h</p>",
                            "<p>29<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> km/h</p>",
                            "<p>24<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> km/h</p>",
                            "<p>26<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> km/h</p>"
                        ],
                        solution_en: "<p>51.(d)<br>Average speed = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><mi>x</mi><mi>y</mi></mrow><mrow><mi>x</mi><mo>+</mo><mi>y</mi></mrow></mfrac></math><br>Average speed = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><mo>&#215;</mo><mn>40</mn><mo>&#215;</mo><mn>20</mn></mrow><mrow><mn>40</mn><mo>+</mo><mn>20</mn></mrow></mfrac></math> <br>= <math display=\"inline\"><mfrac><mrow><mn>1600</mn></mrow><mrow><mn>60</mn></mrow></mfrac></math><br>= 26<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> km/h</p>",
                        solution_hi: "<p>51.(d)<br>औसत चाल = <math display=\"inline\"><mfrac><mrow><mn>2</mn><mi>x</mi><mi>y</mi></mrow><mrow><mi>x</mi><mo>+</mo><mi>y</mi></mrow></mfrac></math><br>औसत चाल = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><mo>&#215;</mo><mn>40</mn><mo>&#215;</mo><mn>20</mn></mrow><mrow><mn>40</mn><mo>+</mo><mn>20</mn></mrow></mfrac></math><br>= <math display=\"inline\"><mfrac><mrow><mn>1600</mn></mrow><mrow><mn>60</mn></mrow></mfrac></math><br>= 26<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> km/h</p>",
                        correct: "d",
                        pos_marks: 2.0,
                        neg_marks: 0.5
                    });
                    questions.push({
                        id: "52",
                        section: "17",
                        question_en: "<p>52. Joey completed the school project in 15 days. How many days will Tim take to complete the same work if he is 25% more efficient than Joey?</p>",
                        question_hi: "<p>52. जॉय ने स्कूल प्रोजेक्ट को 15 दिनों में पूरा किया। यदि टिम जॉय से 25% अधिक कुशल है तो उसी काम को पूरा करने में टिम को कितने दिन लगेंगे?</p>",
                        options_en: [
                            "<p>12 days</p>",
                            "<p>16 days</p>",
                            "<p>8 days</p>",
                            "<p>10 days</p>"
                        ],
                        options_hi: [
                            "<p>12 दिन</p>",
                            "<p>16 दिन</p>",
                            "<p>8 दिन</p>",
                            "<p>10 दिन</p>"
                        ],
                        solution_en: "<p>52.(a)<br>25% = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math><br>Ratio&nbsp; &nbsp; &nbsp; &nbsp; - Tim : Joy<br>Efficiency -&nbsp; &nbsp; 5&nbsp; :&nbsp; 4<br>Time&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;-&nbsp; &nbsp;4&nbsp; &nbsp;:&nbsp; 5&nbsp; &nbsp; &nbsp;(∵Time and efficiency is inversely proportional to each other)<br>(Time taken by Joy) 5 units = 15 days<br>(time taken by Tim) 4 units = <math display=\"inline\"><mfrac><mrow><mn>15</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> &times; 4 = 12 days</p>",
                        solution_hi: "<p>52.(a)<br>25% = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math><br>अनुपात - टिम : जॉय<br>दक्षता&nbsp; &nbsp;-&nbsp; &nbsp; 5&nbsp; :&nbsp; &nbsp;4<br>समय&nbsp; &nbsp; -&nbsp; &nbsp; 4&nbsp; :&nbsp; &nbsp;5&nbsp; &nbsp; &nbsp; (∵ समय और दक्षता एक दूसरे के व्युत्क्रमानुपाती हैं)<br>(जॉय द्वारा लिया गया समय) 5 इकाई = 15 दिन<br>(टिम द्वारा लिया गया समय) 4 इकाई = <math display=\"inline\"><mfrac><mrow><mn>15</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> &times; 4 = 12 दिन</p>",
                        correct: "a",
                        pos_marks: 2.0,
                        neg_marks: 0.5
                    });
                    questions.push({
                        id: "53",
                        section: "17",
                        question_en: "<p>53. In an 800 m race, the ratio of the speeds of two contestants Ankur and Neha is 5: 6. If Ankur has a head-start of 200 m, then Ankur will win by_______.</p>",
                        question_hi: "<p>53. 800 m की एक दौड़ में, दो प्रतियोगियों अंकुर और नेहा की चाल का अनुपात 5: 6 है। यदि अंकुर को 200 m की बढ़त दी जाती है, तो अंकुर________ से जीत जाएगा।</p>",
                        options_en: [
                            "<p>76 m</p>",
                            "<p>69 m</p>",
                            "<p>80 m</p>",
                            "<p>89 m</p>"
                        ],
                        options_hi: [
                            "<p>76 m</p>",
                            "<p>69 m</p>",
                            "<p>80 m</p>",
                            "<p>89 m</p>"
                        ],
                        solution_en: "<p>53.(c) <br>Ratio&nbsp; &nbsp; &nbsp; &nbsp;-&nbsp; Ankur : Neha<br>Distance -&nbsp; &nbsp;600&nbsp; &nbsp; :&nbsp; &nbsp;800<br>Speed&nbsp; &nbsp; &nbsp;-&nbsp; &nbsp;5&nbsp; &nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp;6<br>-------------------------------<br>Time taken by Ankur to complete 600 meter = <math display=\"inline\"><mfrac><mrow><mn>600</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> = 120 m/unit<br>Both Ankur and Neha completed the race at same time<br>So, distance covered by Neha in 120 m/unit = 120 m/unit &times; 6 unit = 720 meter.<br>Hence, Ankur won by (800 - 720) = 80 meters.</p>",
                        solution_hi: "<p>53.(c) <br>अनुपात - अंकुर : नेहा<br>दूरी&nbsp; &nbsp; &nbsp; -&nbsp; &nbsp;600 : 800<br>गति&nbsp; &nbsp; &nbsp; -&nbsp; &nbsp;5&nbsp; &nbsp; &nbsp;: 6<br>--------------------------------<br>अंकुर द्वारा 600 मीटर पूरा करने में लिया गया समय = <math display=\"inline\"><mfrac><mrow><mn>600</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> = 120 मीटर/इकाई<br>अंकुर और नेहा दोनों ने एक ही समय में दौड़ पूरी की<br>तो, नेहा द्वारा 120 मीटर/इकाई में तय की गई दूरी = 120 मीटर/इकाई &times; 6 इकाई = 720 मीटर<br>अतः, अंकुर (800 - 720) = 80 मीटर से जीत जाएगा I</p>",
                        correct: "c",
                        pos_marks: 2.0,
                        neg_marks: 0.5
                    });
                    questions.push({
                        id: "54",
                        section: "17",
                        question_en: "<p>54. Which of the following numbers is divisible by 22?</p>",
                        question_hi: "<p>54. निम्नलिखित में से कौन-सी संख्या 22 से विभाज्य है?</p>",
                        options_en: [
                            "<p>654320</p>",
                            "<p>893002</p>",
                            "<p>602351</p>",
                            "<p>645372</p>"
                        ],
                        options_hi: [
                            "<p>654320</p>",
                            "<p>893002</p>",
                            "<p>602351</p>",
                            "<p>645372</p>"
                        ],
                        solution_en: "<p>54.(b)<br>Checking option one by one the correct option is (b)<br>Divisibility rule of 11 - difference of sum of odd and even places sum is equal to 0 or multiple of 11.<br>Now, 893002 &rarr;<br>Even places&rsquo; digit = 9 + 0 + 2 = 11<br>Odd places&rsquo; digit = 8 + 3 + 0 = 11<br>11 - 11 = 0<br>So the number 893002 is completely divisible by 11.</p>",
                        solution_hi: "<p>54.(b)<br>विकल्पों की एक-एक करके जाँच करने पर (b) सही विकल्प है<br>11 का विभाज्यता नियम - विषम और सम स्थानों के योग का अंतर 0 या 11 के गुणज के बराबर होता है।<br>अब, 893002 &rarr;<br>सम स्थान के अंक = 9 + 0 + 2 = 11<br>विषम स्थान के अंक = 8 + 3 + 0 = 11<br>11 - 11 = 0<br>अतः संख्या 893002 11 से पूर्णतः विभाज्य है।</p>",
                        correct: "b",
                        pos_marks: 2.0,
                        neg_marks: 0.5
                    });
                    questions.push({
                        id: "55",
                        section: "17",
                        question_en: "<p>55. The value of 5<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> - [3<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math> - {<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>6</mn></mfrac></math> - (<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>5</mn></mfrac></math> &times; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>10</mn></mfrac></math> &divide; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>15</mn></mfrac></math>)}] is:</p>",
                        question_hi: "<p>55. 5<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> - [3<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math> - {<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>6</mn></mfrac></math> - (<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>5</mn></mfrac></math> &times; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>10</mn></mfrac></math> &divide; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>15</mn></mfrac></math>)}] का मान कितना होगा ?</p>",
                        options_en: [
                            "<p>1<math display=\"inline\"><mfrac><mrow><mn>81</mn></mrow><mrow><mn>120</mn></mrow></mfrac></math></p>",
                            "<p>1<math display=\"inline\"><mfrac><mrow><mn>97</mn></mrow><mrow><mn>120</mn></mrow></mfrac></math></p>",
                            "<p>2<math display=\"inline\"><mfrac><mrow><mn>43</mn></mrow><mrow><mn>120</mn></mrow></mfrac></math></p>",
                            "<p>2<math display=\"inline\"><mfrac><mrow><mn>47</mn></mrow><mrow><mn>120</mn></mrow></mfrac></math></p>"
                        ],
                        options_hi: [
                            "<p>1<math display=\"inline\"><mfrac><mrow><mn>81</mn></mrow><mrow><mn>120</mn></mrow></mfrac></math></p>",
                            "<p>1<math display=\"inline\"><mfrac><mrow><mn>97</mn></mrow><mrow><mn>120</mn></mrow></mfrac></math></p>",
                            "<p>2<math display=\"inline\"><mfrac><mrow><mn>43</mn></mrow><mrow><mn>120</mn></mrow></mfrac></math></p>",
                            "<p>2<math display=\"inline\"><mfrac><mrow><mn>47</mn></mrow><mrow><mn>120</mn></mrow></mfrac></math></p>"
                        ],
                        solution_en: "<p>55.(c)<br>5<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> - [3<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math> - {<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>6</mn></mfrac></math> - (<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>5</mn></mfrac></math> &times; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>10</mn></mfrac></math> &divide; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>15</mn></mfrac></math>)}]<br>&rArr; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>21</mn><mn>4</mn></mfrac></math> - [<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>2</mn></mfrac></math> - {<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>6</mn></mfrac></math> - (<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>5</mn></mfrac></math> &times; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>10</mn></mfrac></math> &times; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>15</mn><mn>4</mn></mfrac></math>)}]<br>&rArr; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>21</mn><mn>4</mn></mfrac></math> - [<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>2</mn></mfrac></math> - {<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>6</mn></mfrac></math> - <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>9</mn><mn>40</mn></mfrac></math>}]<br>&rArr; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>21</mn><mn>4</mn></mfrac></math> - [<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>2</mn></mfrac></math> - {<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>100</mn><mo>-</mo><mn>27</mn></mrow><mn>120</mn></mfrac></math>}]<br>&rArr; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>21</mn><mn>4</mn></mfrac></math> - [<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>2</mn></mfrac></math> - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>73</mn><mn>120</mn></mfrac></math>]<br>&rArr; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>21</mn><mn>4</mn></mfrac></math> - [<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>420</mn><mo>-</mo><mn>73</mn></mrow><mn>120</mn></mfrac></math>]<br>&rArr; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>21</mn><mn>4</mn></mfrac></math> - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>347</mn><mn>120</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>630</mn><mo>-</mo><mn>347</mn></mrow><mn>120</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>283</mn><mn>120</mn></mfrac></math> = 2<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>43</mn><mn>120</mn></mfrac></math></p>",
                        solution_hi: "<p>55.(c)<br>5<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> - [3<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math> - {<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>6</mn></mfrac></math> - (<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>5</mn></mfrac></math> &times; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>10</mn></mfrac></math> &divide; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>15</mn></mfrac></math>)}]<br>&rArr; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>21</mn><mn>4</mn></mfrac></math> - [<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>2</mn></mfrac></math> - {<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>6</mn></mfrac></math> - (<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>5</mn></mfrac></math> &times; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>10</mn></mfrac></math> &times; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>15</mn><mn>4</mn></mfrac></math>)}]<br>&rArr; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>21</mn><mn>4</mn></mfrac></math> - [<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>2</mn></mfrac></math> - {<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>6</mn></mfrac></math> - <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>9</mn><mn>40</mn></mfrac></math>}]<br>&rArr; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>21</mn><mn>4</mn></mfrac></math> - [<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>2</mn></mfrac></math> - {<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>100</mn><mo>-</mo><mn>27</mn></mrow><mn>120</mn></mfrac></math>}]<br>&rArr; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>21</mn><mn>4</mn></mfrac></math> - [<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>2</mn></mfrac></math> - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>73</mn><mn>120</mn></mfrac></math>]<br>&rArr; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>21</mn><mn>4</mn></mfrac></math> - [<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>420</mn><mo>-</mo><mn>73</mn></mrow><mn>120</mn></mfrac></math>]<br>&rArr; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>21</mn><mn>4</mn></mfrac></math> - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>347</mn><mn>120</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>630</mn><mo>-</mo><mn>347</mn></mrow><mn>120</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>283</mn><mn>120</mn></mfrac></math> = 2<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>43</mn><mn>120</mn></mfrac></math></p>",
                        correct: "c",
                        pos_marks: 2.0,
                        neg_marks: 0.5
                    });
                    questions.push({
                        id: "56",
                        section: "17",
                        question_en: "<p>56. If cot<sup>2</sup>&theta; - 2cos<sup>2</sup>&theta; = 0, (0&deg;&lt; &theta; &lt; 90&deg;), then the value of &theta; is:</p>",
                        question_hi: "<p>56. यदि cot<sup>2</sup>&theta; - 2cos<sup>2</sup>&theta; = 0 है, जहां (0&deg;&lt; &theta; &lt; 90&deg;), तो &theta;<strong id=\"docs-internal-guid-4f340a64-7fff-fcbd-8ab0-8dd6398d39f3\"> </strong>का मान ज्ञात कीजिए।</p>",
                        options_en: [
                            "<p>45&deg;</p>",
                            "<p>60&deg;</p>",
                            "<p>90&deg;</p>",
                            "<p>30&deg;</p>"
                        ],
                        options_hi: [
                            "<p>45&deg;</p>",
                            "<p>60&deg;</p>",
                            "<p>90&deg;</p>",
                            "<p>30&deg;</p>"
                        ],
                        solution_en: "<p>56.(a)<br>cot<sup>2</sup>&theta; - 2cos<sup>2</sup>&theta; = 0<br>cot<sup>2</sup>&theta; = 2cos<sup>2</sup>&theta; <br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi>cos</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&#952;</mi></mrow><mrow><msup><mi>sin</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&#952;</mi></mrow></mfrac></math> = 2cos<sup>2</sup>&theta;<br>sin<sup>2</sup>&theta; = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math><br>sin&theta; = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msqrt><mn>2</mn></msqrt></mfrac></math><br>&theta; = 45&deg;</p>",
                        solution_hi: "<p>56.(a)<br>cot<sup>2</sup>&theta; - 2cos<sup>2</sup>&theta; = 0<br>cot<sup>2</sup>&theta; = 2cos<sup>2</sup>&theta; <br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi>cos</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&#952;</mi></mrow><mrow><msup><mi>sin</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&#952;</mi></mrow></mfrac></math> = 2cos<sup>2</sup>&theta;<br>sin<sup>2</sup>&theta; = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math><br>sin&theta; = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msqrt><mn>2</mn></msqrt></mfrac></math><br>&theta; = 45&deg;</p>",
                        correct: "a",
                        pos_marks: 2.0,
                        neg_marks: 0.5
                    });
                    questions.push({
                        id: "57",
                        section: "17",
                        question_en: "<p>57. The LCM of two numbers is 20 times of their HCF and LCM + HCF = 2520. What will be the difference between LCM and HCF ?</p>",
                        question_hi: "<p>57. दो संख्याओं का LCM उनके HCF का 20 गुना है तथा LCM + HCF = 2520 है। LCM और HCF का अंतर क्या है ?</p>",
                        options_en: [
                            "<p>2420</p>",
                            "<p>2380</p>",
                            "<p>2320</p>",
                            "<p>2280</p>"
                        ],
                        options_hi: [
                            "<p>2420</p>",
                            "<p>2380</p>",
                            "<p>2320</p>",
                            "<p>2280</p>"
                        ],
                        solution_en: "<p>57.(d) <br>Let the H. C .F of these two numbers = x<br>Then LCM of these two numbers = 20x<br>From the given condition <br>x + 20x = 2520<br>21x&nbsp;= 2520<br>x = 120<br>HCF = 120 and LCM = 120 &times; 20 = 2400<br>Difference between LCM and HCF = 2400 - 120 = 2280</p>",
                        solution_hi: "<p>57.(d) <br>माना इन दोनों संख्याओं का H. C .F = x<br>तब इन दोनों संख्याओं का LCM = 20x<br>दी गई शर्त के अनुसार <br>x + 20x = 2520<br>21x&nbsp;= 2520<br>x = 120<br>HCF = 120 और LCM = 120 &times; 20 = 2400<br>LCM और HCF के बीच अंतर = 2400 - 120 = 2280</p>",
                        correct: "d",
                        pos_marks: 2.0,
                        neg_marks: 0.5
                    });
                    questions.push({
                        id: "58",
                        section: "17",
                        question_en: "<p>58. In a right angle triangle with sides 12 cm and 16 cm and hypotenuse 20 cm, the length of altitude drawn on the hypotenuse from the opposite vertex is K. Find the value of K.</p>",
                        question_hi: "<p>58. 12 cm और 16 cm की भुजाओं और 20 cm कर्ण वाले एक समकोण त्रिभुज में, सम्मुख शीर्ष से कर्ण पर खींचा गया शीर्षलंब K है। K का मान ज्ञात करें।</p>",
                        options_en: [
                            "<p>6.8 cm</p>",
                            "<p>9.6 cm</p>",
                            "<p>8.6 cm</p>",
                            "<p>6.9 cm</p>"
                        ],
                        options_hi: [
                            "<p>6.8 cm</p>",
                            "<p>9.6 cm</p>",
                            "<p>8.6 cm</p>",
                            "<p>6.9 cm</p>"
                        ],
                        solution_en: "<p>58.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741246703854.png\" alt=\"rId68\" width=\"162\" height=\"166\"><br>Theorem :- AC &times; DB = AB &times; BC<br>20 &times; DB = 12 &times; 16<br>DB = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>12</mn><mo>&#215;</mo><mn>16</mn></mrow><mn>20</mn></mfrac></math> = 9.6 cm</p>",
                        solution_hi: "<p>58.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741246703854.png\" alt=\"rId68\" width=\"162\" height=\"166\"><br>प्रमेय :- AC &times; DB = AB &times; BC<br>20 &times; DB = 12 &times; 16<br>DB = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>12</mn><mo>&#215;</mo><mn>16</mn></mrow><mn>20</mn></mfrac></math>&nbsp;= 9.6 cm</p>",
                        correct: "b",
                        pos_marks: 2.0,
                        neg_marks: 0.5
                    });
                    questions.push({
                        id: "59",
                        section: "17",
                        question_en: "<p>59. The sum of two numbers x&nbsp;and y (where x is greater than y) is equal to four times of their difference. Find the value of <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5</mn><mi>x</mi><mi>y</mi></mrow><mrow><mn>3</mn><mo>(</mo><msup><mi>x</mi><mn>2</mn></msup><mo>-</mo><msup><mi>y</mi><mn>2</mn></msup><mo>)</mo></mrow></mfrac></math></p>",
                        question_hi: "<p>59. दो संख्याओं <math display=\"inline\"><mi>x</mi></math> और y (जहां x, y से बड़ी है) का योग उनके अंतर के चार गुने के बराबर है। <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5</mn><mi>x</mi><mi>y</mi></mrow><mrow><mn>3</mn><mo>(</mo><msup><mi>x</mi><mn>2</mn></msup><mo>-</mo><msup><mi>y</mi><mn>2</mn></msup><mo>)</mo></mrow></mfrac></math> का मान ज्ञात करें।</p>",
                        options_en: [
                            "<p><math display=\"inline\"><mfrac><mrow><mn>25</mn><mi>&#160;</mi></mrow><mrow><mn>16</mn></mrow></mfrac></math></p>",
                            "<p>16</p>",
                            "<p>2<math display=\"inline\"><mfrac><mrow><mn>3</mn><mi>&#160;</mi></mrow><mrow><mn>16</mn></mrow></mfrac></math></p>",
                            "<p>25</p>"
                        ],
                        options_hi: [
                            "<p><math display=\"inline\"><mfrac><mrow><mn>25</mn><mi>&#160;</mi></mrow><mrow><mn>16</mn></mrow></mfrac></math></p>",
                            "<p>16</p>",
                            "<p>2<math display=\"inline\"><mfrac><mrow><mn>3</mn><mi>&#160;</mi></mrow><mrow><mn>16</mn></mrow></mfrac></math></p>",
                            "<p>25</p>"
                        ],
                        solution_en: "<p>59.(a)<br>x + y = 4(x - y)<br>x + y = 4x - 4y<br>5y = 3<math display=\"inline\"><mi>x</mi></math><br><math display=\"inline\"><mfrac><mrow><mi>y</mi></mrow><mrow><mi>x</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>5</mn></mfrac></math><br>Let x&nbsp;= 5a and y = 3a<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#160;</mi><mn>5</mn><mi>x</mi><mi>y</mi></mrow><mrow><mn>3</mn><mo>(</mo><msup><mi>x</mi><mn>2</mn></msup><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><msup><mi>y</mi><mn>2</mn></msup><mo>)</mo></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5</mn><mo>&#215;</mo><mn>5</mn><mi>a</mi><mo>&#215;</mo><mn>3</mn><mi>a</mi></mrow><mrow><mn>3</mn><mo>(</mo><mn>25</mn><msup><mi>a</mi><mn>2</mn></msup><mo>-</mo><mn>9</mn><msup><mi>a</mi><mn>2</mn></msup><mo>)</mo></mrow></mfrac></math> <br>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>75</mn><msup><mi>a</mi><mn>2</mn></msup></mrow><mrow><mn>3</mn><mo>&#215;</mo><mn>16</mn><msup><mi>a</mi><mn>2</mn></msup></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>25</mn><mn>16</mn></mfrac></math></p>",
                        solution_hi: "<p>59.(a)<br>x + y = 4(x - y)<br>x + y = 4x - 4y<br>5y = 3<math display=\"inline\"><mi>x</mi></math><br><math display=\"inline\"><mfrac><mrow><mi>y</mi></mrow><mrow><mi>x</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>5</mn></mfrac></math><br>माना x&nbsp;= 5a और y = 3a<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#160;</mi><mn>5</mn><mi>x</mi><mi>y</mi></mrow><mrow><mn>3</mn><mo>(</mo><msup><mi>x</mi><mn>2</mn></msup><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><msup><mi>y</mi><mn>2</mn></msup><mo>)</mo></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5</mn><mo>&#215;</mo><mn>5</mn><mi>a</mi><mo>&#215;</mo><mn>3</mn><mi>a</mi></mrow><mrow><mn>3</mn><mo>(</mo><mn>25</mn><msup><mi>a</mi><mn>2</mn></msup><mo>-</mo><mn>9</mn><msup><mi>a</mi><mn>2</mn></msup><mo>)</mo></mrow></mfrac></math> <br>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>75</mn><msup><mi>a</mi><mn>2</mn></msup></mrow><mrow><mn>3</mn><mo>&#215;</mo><mn>16</mn><msup><mi>a</mi><mn>2</mn></msup></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>25</mn><mn>16</mn></mfrac></math></p>",
                        correct: "a",
                        pos_marks: 2.0,
                        neg_marks: 0.5
                    });
                    questions.push({
                        id: "60",
                        section: "17",
                        question_en: "<p>60. For making a toy, a hemisphere is attached at one end of a cylinder and a cone is attached at the other end of the cylinder. The cylinder, the cone and the hemisphere have a common radius of 4.2 cm. The height of the cylinder and that of the cone is 7 cm. Find the volume (in cm&sup3;) of the toy. (Use &pi; =<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>22</mn><mn>7</mn></mfrac></math>)</p>",
                        question_hi: "<p>60. एक खिलौना बनाने के लिए, एक बेलन के एक सिरे पर एक अर्धगोला जुड़ा हुआ है और बेलन के दूसरे सिरे पर एक शंकु जुड़ा हुआ है। बेलन, शंकु और अर्धगोले की उभयनिष्ठ त्रिज्या 4.2 cm है। बेलन की ऊँचाई और शंकु की ऊँचाई 7 cm है। खिलौने का आयतन (cm&sup3; में) ज्ञात कीजिए। (&pi; = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>22</mn><mn>7</mn></mfrac></math>&nbsp;का उपयोग कीजिए)</p>",
                        options_en: [
                            "<p>358.8</p>",
                            "<p>762.255</p>",
                            "<p>672.672</p>",
                            "<p>863.25</p>"
                        ],
                        options_hi: [
                            "<p>358.8</p>",
                            "<p>762.255</p>",
                            "<p>672.672</p>",
                            "<p>863.25</p>"
                        ],
                        solution_en: "<p>60.(c) Volume of toy = Volume of hemisphere + volume of cylinder + volume of cone <br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>3</mn></mfrac></math>&pi;r<sup>3</sup> + &pi;r<sup>2</sup>h + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>3</mn></mfrac></math>&pi;r<sup>2</sup>h<br>= &pi;r<sup>2</sup> [ <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>3</mn></mfrac></math>r + h + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>3</mn></mfrac></math>h ]<br>= <math display=\"inline\"><mfrac><mrow><mn>22</mn></mrow><mrow><mn>7</mn><mi>&#160;</mi></mrow></mfrac></math>&times; 4.2 &times; 4.2 [<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>3</mn></mfrac></math> &times; 4.2 + 7 + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>3</mn></mfrac></math>]<br>= 22 &times; 0.6 &times; 4.2 &times; <math display=\"inline\"><mfrac><mrow><mn>36</mn><mo>.</mo><mn>4</mn></mrow><mrow><mn>3</mn><mi>&#160;</mi></mrow></mfrac></math><br>= 672.672 cm<sup>3</sup></p>",
                        solution_hi: "<p>60.(c) खिलौने का आयतन = अर्धगोले का आयतन + बेलन का आयतन + शंकु का आयतन <br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>3</mn></mfrac></math>&pi;r<sup>3</sup> + &pi;r<sup>2</sup>h + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>3</mn></mfrac></math>&pi;r<sup>2</sup>h<br>= &pi;r<sup>2</sup> [ <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>3</mn></mfrac></math>r + h + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>3</mn></mfrac></math>h ]<br>= <math display=\"inline\"><mfrac><mrow><mn>22</mn></mrow><mrow><mn>7</mn><mi>&#160;</mi></mrow></mfrac></math>&times; 4.2 &times; 4.2 [<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>3</mn></mfrac></math> &times; 4.2 + 7 + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>3</mn></mfrac></math>]<br>= 22 &times; 0.6 &times; 4.2 &times; <math display=\"inline\"><mfrac><mrow><mn>36</mn><mo>.</mo><mn>4</mn></mrow><mrow><mn>3</mn><mi>&#160;</mi></mrow></mfrac></math><br>= 672.672 cm<sup>3</sup></p>",
                        correct: "c",
                        pos_marks: 2.0,
                        neg_marks: 0.5
                    });
                    questions.push({
                        id: "61",
                        section: "17",
                        question_en: "<p>61. If <math display=\"inline\"><msqrt><mn>5</mn></msqrt></math> : <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>42</mn></msqrt></math> :: <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>7</mn></msqrt></math> : x, then what is the value of x ?</p>",
                        question_hi: "<p>61. यदि <math display=\"inline\"><msqrt><mn>5</mn></msqrt></math> : <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>42</mn></msqrt></math> :: <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>7</mn></msqrt></math> : x है, तो x का मान ज्ञात कीजिए।</p>",
                        options_en: [
                            "<p><math display=\"inline\"><mfrac><mrow><mi>&#160;</mi><mn>7</mn></mrow><mrow><mn>5</mn></mrow></mfrac><msqrt><mn>42</mn></msqrt></math></p>",
                            "<p><math display=\"inline\"><mfrac><mrow><mi>&#160;</mi><mn>6</mn></mrow><mrow><mn>5</mn></mrow></mfrac><msqrt><mn>42</mn></msqrt></math></p>",
                            "<p><math display=\"inline\"><mfrac><mrow><mi>&#160;</mi><mn>6</mn></mrow><mrow><mn>5</mn></mrow></mfrac><msqrt><mn>30</mn></msqrt></math></p>",
                            "<p><math display=\"inline\"><mfrac><mrow><mi>&#160;</mi><mn>7</mn></mrow><mrow><mn>5</mn></mrow></mfrac><msqrt><mn>30</mn></msqrt></math></p>"
                        ],
                        options_hi: [
                            "<p><math display=\"inline\"><mfrac><mrow><mi>&#160;</mi><mn>7</mn></mrow><mrow><mn>5</mn></mrow></mfrac><msqrt><mn>42</mn></msqrt></math></p>",
                            "<p><math display=\"inline\"><mfrac><mrow><mi>&#160;</mi><mn>6</mn></mrow><mrow><mn>5</mn></mrow></mfrac><msqrt><mn>42</mn></msqrt></math></p>",
                            "<p><math display=\"inline\"><mfrac><mrow><mi>&#160;</mi><mn>6</mn></mrow><mrow><mn>5</mn></mrow></mfrac><msqrt><mn>30</mn></msqrt></math></p>",
                            "<p><math display=\"inline\"><mfrac><mrow><mi>&#160;</mi><mn>7</mn></mrow><mrow><mn>5</mn></mrow></mfrac><msqrt><mn>30</mn></msqrt></math></p>"
                        ],
                        solution_en: "<p>61.(d) According to question,<br><math display=\"inline\"><mfrac><mrow><msqrt><mn>5</mn></msqrt></mrow><mrow><msqrt><mn>42</mn></msqrt></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msqrt><mn>7</mn></msqrt><mi>x</mi></mfrac></math><br>x = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msqrt><mn>7</mn></msqrt><mo>&#215;</mo><msqrt><mn>42</mn></msqrt></mrow><msqrt><mn>5</mn></msqrt></mfrac></math> <br>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>7</mn><msqrt><mn>6</mn></msqrt></mrow><msqrt><mn>5</mn></msqrt></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msqrt><mn>5</mn></msqrt><msqrt><mn>5</mn></msqrt></mfrac></math> <br>= <math display=\"inline\"><mfrac><mrow><mi>&#160;</mi><mn>7</mn></mrow><mrow><mn>5</mn></mrow></mfrac><msqrt><mn>30</mn></msqrt></math></p>",
                        solution_hi: "<p>61.(d) प्रश्न के अनुसार,<br><math display=\"inline\"><mfrac><mrow><msqrt><mn>5</mn></msqrt></mrow><mrow><msqrt><mn>42</mn></msqrt></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msqrt><mn>7</mn></msqrt><mi>x</mi></mfrac></math><br>x = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msqrt><mn>7</mn></msqrt><mo>&#215;</mo><msqrt><mn>42</mn></msqrt></mrow><msqrt><mn>5</mn></msqrt></mfrac></math> <br>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>7</mn><msqrt><mn>6</mn></msqrt></mrow><msqrt><mn>5</mn></msqrt></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msqrt><mn>5</mn></msqrt><msqrt><mn>5</mn></msqrt></mfrac></math> <br>= <math display=\"inline\"><mfrac><mrow><mi>&#160;</mi><mn>7</mn></mrow><mrow><mn>5</mn></mrow></mfrac><msqrt><mn>30</mn></msqrt></math></p>",
                        correct: "d",
                        pos_marks: 2.0,
                        neg_marks: 0.5
                    });
                    questions.push({
                        id: "62",
                        section: "17",
                        question_en: "<p>62. A mixture contains acid and water in the ratio of 7 : 4. If 2 litres of water is added to it, then the ratio of acid and water becomes 7 : 5. What is the quantity of acid in the mixture?</p>",
                        question_hi: "<p>62. एक मिश्रण में अम्ल और पानी का अनुपात 7:4 है। यदि इसमें 2 लीटर पानी मिला दिया जाए, तो अम्ल और पानी का अनुपात 7:5 हो जाता है। मिश्रण में अम्ल की मात्रा कितनी है?</p>",
                        options_en: [
                            "<p>20 litres</p>",
                            "<p>21 litres</p>",
                            "<p>7 litres</p>",
                            "<p>14 litres</p>"
                        ],
                        options_hi: [
                            "<p>20 लीटर</p>",
                            "<p>21 लीटर</p>",
                            "<p>7 लीटर</p>",
                            "<p>14 लीटर</p>"
                        ],
                        solution_en: "<p>62.(d) <br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; Acid&nbsp; &nbsp; Water<br>Initial mixture&nbsp; &nbsp;7&nbsp; &nbsp; :&nbsp; &nbsp; 4<br>Final mixture&nbsp; &nbsp; 7&nbsp; &nbsp; :&nbsp; &nbsp; 5<br>Quantity of water added = 5 - 4 = 1 unit which corresponds to 2 litres<br>Then, quantity of acid in the mixture = 7 unit = 7 &times; 2 = 14 litres</p>",
                        solution_hi: "<p>62.(d)<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; अम्ल&nbsp; &nbsp;पानी <br>प्रारंभिक मिश्रण&nbsp; &nbsp; 7&nbsp; &nbsp;:&nbsp; &nbsp;4<br>अंतिम मिश्रण&nbsp; &nbsp; &nbsp; &nbsp;7&nbsp; &nbsp;:&nbsp; &nbsp;5<br>मिलाए गए पानी की मात्रा = 5 - 4 = 1 इकाई, जो 2 लीटर के अनुरूप है<br>फिर, मिश्रण में अम्ल की मात्रा = 7 इकाई = 7 &times; 2 = 14 लीटर</p>",
                        correct: "d",
                        pos_marks: 2.0,
                        neg_marks: 0.5
                    });
                    questions.push({
                        id: "63",
                        section: "17",
                        question_en: "<p>63. A and B can do a piece of work in 9 days and 15 days, respectively. If they work together, then the work will be completed in</p>",
                        question_hi: "<p>63. A और B एक काम क्रमशः 9 दिनों और 15 दिनों में कर सकते हैं। यदि वे साथ मिलकर काम करते हैं, तो काम कितने दिनों में पूरा हो जाएगा?</p>",
                        options_en: [
                            "<p>5 <math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math> days</p>",
                            "<p>2 <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math> days</p>",
                            "<p>3 <math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math> days</p>",
                            "<p>4 <math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math> days</p>"
                        ],
                        options_hi: [
                            "<p>5 <math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math> दिन</p>",
                            "<p>2 <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math> दिन</p>",
                            "<p>3 <math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math> दिन</p>",
                            "<p>4 <math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math> दिन</p>"
                        ],
                        solution_en: "<p>63.(a) <br>Required time = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>9</mn><mo>&#215;</mo><mn>15</mn></mrow><mrow><mn>9</mn><mo>+</mo><mn>15</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>135</mn><mn>24</mn></mfrac></math> <br>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>45</mn><mn>8</mn></mfrac></math> = 5 <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>8</mn></mfrac></math> days</p>",
                        solution_hi: "<p>63.(a) <br>आवश्यक समय = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>9</mn><mo>&#215;</mo><mn>15</mn></mrow><mrow><mn>9</mn><mo>+</mo><mn>15</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>135</mn><mn>24</mn></mfrac></math> <br>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>45</mn><mn>8</mn></mfrac></math> = 5 <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>8</mn></mfrac></math>दिन</p>",
                        correct: "a",
                        pos_marks: 2.0,
                        neg_marks: 0.5
                    });
                    questions.push({
                        id: "64",
                        section: "17",
                        question_en: "<p>64. Pipe A usually fills a tank in 6 hours. But due to a leak at the bottom of the tank, it takes extra 2 hours to fill the tank. If the tank is full, then how much time will it take to get emptied due to the leak?</p>",
                        question_hi: "<p>64. पाइप A आमतौर पर एक टैंक को 6 घंटे में भरता है। लेकिन टैंक के निचले भाग में रिसाव के कारण, टैंक को भरने में 2 घंटे अतिरिक्त लगते हैं। यदि टैंक भरा हुआ है तो रिसाव के कारण इसे खाली होने में कितना समय लगेगा?</p>",
                        options_en: [
                            "<p>16 hours</p>",
                            "<p>20 hours</p>",
                            "<p>12 hours</p>",
                            "<p>24 hours</p>"
                        ],
                        options_hi: [
                            "<p>16 घंटे</p>",
                            "<p>20 घंटे</p>",
                            "<p>12 घंटे</p>",
                            "<p>24 घंटे</p>"
                        ],
                        solution_en: "<p>64.(d)<br>Let L be the leakage pipe.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741246704071.png\" alt=\"rId69\" width=\"170\" height=\"145\"><br>Efficiency of L = 3 - 4 = - 1 unit<br>Time taken to empty the tank due to leak = <math display=\"inline\"><mfrac><mrow><mn>24</mn></mrow><mrow><mn>1</mn></mrow></mfrac></math> = 24 hrs</p>",
                        solution_hi: "<p>64.(d)<br>माना , लीकेज पाइप = L <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741246704299.png\" alt=\"rId70\" width=\"129\" height=\"114\"><br>L की दक्षता = 3 - 4 = - 1 इकाई<br>रिसाव के कारण टैंक को खाली करने में लगा समय = <math display=\"inline\"><mfrac><mrow><mn>24</mn></mrow><mrow><mn>1</mn></mrow></mfrac></math> = 24 घंटे</p>",
                        correct: "d",
                        pos_marks: 2.0,
                        neg_marks: 0.5
                    });
                    questions.push({
                        id: "65",
                        section: "17",
                        question_en: "<p>65. Moving at <math display=\"inline\"><mfrac><mrow><mn>6</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> of its usual speed, a bus is 20 minutes late. What is its usual time to cover the journey?</p>",
                        question_hi: "<p>65. अपनी सामान्य चाल की <math display=\"inline\"><mfrac><mrow><mn>6</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> चाल से चलती हुई एक बस 20 मिनट देरी से पहुँचती है। बस का इस यात्रा को पूरा करने का सामान्य समय क्या है?</p>",
                        options_en: [
                            "<p>3 hours</p>",
                            "<p>4 hours</p>",
                            "<p>1.5 hours</p>",
                            "<p>2 hours</p>"
                        ],
                        options_hi: [
                            "<p>3 घंटे</p>",
                            "<p>4 घंटे</p>",
                            "<p>1.5 घंटा</p>",
                            "<p>2 घंटे</p>"
                        ],
                        solution_en: "<p>65.(d) <br><strong>Ratio&nbsp; - Usual : After</strong> <br>Speed -&nbsp; &nbsp; &nbsp;7&nbsp; &nbsp; :&nbsp; &nbsp; 6<br>Time&nbsp; &nbsp;-&nbsp; &nbsp; &nbsp; 6&nbsp; &nbsp; :&nbsp; &nbsp; 7<br>-----------------------------<br>Time difference (7 - 6)unit = 20 min<br>So, usual time (6 unit) = 6 &times; 20 = 120 min = 2 hours.</p>",
                        solution_hi: "<p>65.(d) <br><strong>अनुपात - सामान्य&nbsp; : बाद में</strong> <br>गति&nbsp; &nbsp; &nbsp; -&nbsp; &nbsp; &nbsp;7&nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; 6<br>समय&nbsp; &nbsp; -&nbsp; &nbsp; &nbsp;6&nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; 7<br>--------------------------------<br>समय अंतर (7 - 6) इकाई = 20 मिनट<br>तो, सामान्य समय (6 इकाई) = 6 &times; 20 = 120 मिनट = 2 घंटे।</p>",
                        correct: "d",
                        pos_marks: 2.0,
                        neg_marks: 0.5
                    });
                    questions.push({
                        id: "66",
                        section: "17",
                        question_en: "<p>66. If the selling price of 100 pens is equal to the cost price of 140 pens, find the profit percentage.</p>",
                        question_hi: "<p>66. यदि 100 कलमों का विक्रय मूल्य, 140 कलमों के क्रय मूल्य के बराबर है, तो लाभ प्रतिशत ज्ञात कीजिए।</p>",
                        options_en: [
                            "<p>30%</p>",
                            "<p>40%</p>",
                            "<p>45%</p>",
                            "<p>36%</p>"
                        ],
                        options_hi: [
                            "<p>30%</p>",
                            "<p>40%</p>",
                            "<p>45%</p>",
                            "<p>36%</p>"
                        ],
                        solution_en: "<p>66.(b)<br>According to the question,<br>100 &times; SP = 140 &times; CP<br><math display=\"inline\"><mfrac><mrow><mi>S</mi><mi>P</mi></mrow><mrow><mi>C</mi><mi>P</mi></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>14</mn><mn>10</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>5</mn></mfrac></math><br>profit = <math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> &times; 100 = 40%</p>",
                        solution_hi: "<p>66.(b)<br>प्रश्न के अनुसार,<br>100 &times; विक्रय मूल्य = 140 &times; क्रय मूल्य<br><math style=\"font-family:Verdana\" display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2357;&#2367;&#2325;&#2381;&#2352;&#2351;</mi><mi mathvariant=\"normal\">&#160;</mi><mi>&#2350;&#2370;&#2354;&#2381;&#2351;</mi></mrow><mrow><mi>&#2325;&#2381;&#2352;&#2351;</mi><mi mathvariant=\"normal\">&#160;</mi><mi>&#2350;&#2370;&#2354;&#2381;&#2351;</mi></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>14</mn><mn>10</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>5</mn></mfrac></math><br>लाभ = <math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> &times; 100 = 40 %</p>",
                        correct: "b",
                        pos_marks: 2.0,
                        neg_marks: 0.5
                    });
                    questions.push({
                        id: "67",
                        section: "17",
                        question_en: "<p>67. A dozen pairs of gloves of ₹180 are available at a discount of 10%. How many pairs of gloves can be bought for ₹54?</p>",
                        question_hi: "<p>67. ₹180 के एक दर्जन दस्तानों की जोड़ियां 10% की छूट पर उपलब्ध हैं। ₹54 में कितने जोड़ी दस्ताने खरीदे जा सकते हैं?</p>",
                        options_en: [
                            "<p>2</p>",
                            "<p>8</p>",
                            "<p>6</p>",
                            "<p>4</p>"
                        ],
                        options_hi: [
                            "<p>2</p>",
                            "<p>8</p>",
                            "<p>6</p>",
                            "<p>4</p>"
                        ],
                        solution_en: "<p>67.(d)<br>Selling price of a dozen pairs of gloves = 180 &times; 90% = Rs. 162<br>According to the question,<br><math display=\"inline\"><mfrac><mrow><mn>162</mn></mrow><mrow><mn>12</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>54</mn><mi>x</mi></mfrac></math> &rArr; x = 4<br>Hence, pairs of gloves can be bought for Rs. 54 is 4 pairs.</p>",
                        solution_hi: "<p>67.(d)<br>एक दर्जन जोड़ी दस्तानों का विक्रय मूल्य = 180 &times; 90% = रु. 162<br>प्रश्न के अनुसार,<br><math display=\"inline\"><mfrac><mrow><mn>162</mn></mrow><mrow><mn>12</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>54</mn><mi>x</mi></mfrac></math> &rArr; x = 4<br>इसलिए, 54 रुपये में 4 जोड़े दस्ताने खरीदे जा सकते हैं।</p>",
                        correct: "d",
                        pos_marks: 2.0,
                        neg_marks: 0.5
                    });
                    questions.push({
                        id: "68",
                        section: "17",
                        question_en: "<p>68. The simple interest on the sum for 12 years is three-fifth of the sum. The rate of&nbsp;interest per annum is:</p>",
                        question_hi: "<p>68. 12 वर्षों के लिए एक धनराशि पर साधारण ब्याज, धनराशि का तीन-पांचवां भाग है। वार्षिक ब्याज दर क्या है ?</p>",
                        options_en: [
                            "<p>3%</p>",
                            "<p>6%</p>",
                            "<p>4%</p>",
                            "<p>5%</p>"
                        ],
                        options_hi: [
                            "<p>3%</p>",
                            "<p>6%</p>",
                            "<p>4%</p>",
                            "<p>5%</p>"
                        ],
                        solution_en: "<p>68.(d)<br>Let principal = Rs. x<br>According to the question,<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>5</mn></mfrac></math>x = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>x</mi><mo>&#215;</mo><mi>R</mi><mo>&#215;</mo><mn>12</mn></mrow><mn>100</mn></mfrac></math><br>R = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>3</mn><mo>&#215;</mo><mn>100</mn></mrow><mrow><mn>12</mn><mo>&#215;</mo><mn>5</mn></mrow></mfrac></math> = 5 %</p>",
                        solution_hi: "<p>68.(d)<br>माना मूलधन = रु. x<br>प्रश्न के अनुसार,<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>5</mn></mfrac></math>x = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>x</mi><mo>&#215;</mo><mi>R</mi><mo>&#215;</mo><mn>12</mn></mrow><mn>100</mn></mfrac></math><br>(दर) R = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>3</mn><mo>&#215;</mo><mn>100</mn></mrow><mrow><mn>12</mn><mo>&#215;</mo><mn>5</mn></mrow></mfrac></math> = 5 %</p>",
                        correct: "d",
                        pos_marks: 2.0,
                        neg_marks: 0.5
                    });
                    questions.push({
                        id: "69",
                        section: "17",
                        question_en: "<p>69. Rs. 9000 is lent at the rate of 80 percent per annum on compound interest (compounded half yearly). What will be the compound interest of 12 months ?</p>",
                        question_hi: "<p>69. 9000 रुपए 80 प्रतिशत की वार्षिक दर से चक्रवृद्धि ब्याज (अर्धवार्षिक रूप से संयोजित) पर उधार दिए जाते हैं। 12 महीने का चक्रवृद्धि ब्याज कितना होगा ?</p>",
                        options_en: [
                            "<p>Rs. 9000</p>",
                            "<p>Rs. 8760</p>",
                            "<p>Rs. 8700</p>",
                            "<p>Rs. 8640</p>"
                        ],
                        options_hi: [
                            "<p>9000 रुपए</p>",
                            "<p>8760 रुपए</p>",
                            "<p>8700 रुपए</p>",
                            "<p>8640 रुपए</p>"
                        ],
                        solution_en: "<p>69.(d)<br>Rate compounded half yearly,<br>Then, rate = 40% , time = 2 cycle<br>CI for 12 months(or 1 yr) = (40 + 40 + <math display=\"inline\"><mfrac><mrow><mn>40</mn><mo>&#215;</mo><mn>40</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math>)% = 96%<br>&rArr; 96% of 9000 = ₹8640</p>",
                        solution_hi: "<p>69.(d)<br>दर अर्धवार्षिक संयोजित,<br>फिर, दर = 40%, समय = 2 चक्र<br>12 महीने (या 1 वर्ष) के लिए चक्रवृद्धि ब्याज = (40 + 40 +&nbsp;<math display=\"inline\"><mfrac><mrow><mn>40</mn><mo>&#215;</mo><mn>40</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math>)% = 96%<br>&rArr; 9000 का 96% = ₹8640</p>",
                        correct: "d",
                        pos_marks: 2.0,
                        neg_marks: 0.5
                    });
                    questions.push({
                        id: "70",
                        section: "17",
                        question_en: "<p>70. The following table shows the sales during two seasons of a company. Study the table and answer the question that follows.<br><img src=\"data:image/png;base64,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\" width=\"487\" height=\"105\"><br>What is the percentage share of sales during months of June and July to the total sales?</p>",
                        question_hi: "<p>70. निम्न तालिका किसी कंपनी की दो सत्रों के दौरान बिक्री को दर्शाती है। <br>तालिका का अध्ययन कीजिए और निम्नांकित प्रश्न का उत्तर दीजिए।<br><img src=\"data:image/png;base64,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\" width=\"400\" height=\"109\"><br>कुल बिक्री में से जून और जुलाई के महीनों के दौरान बिक्री का प्रतिशत भाग कितना है?</p>",
                        options_en: [
                            "<p>28%</p>",
                            "<p>30%</p>",
                            "<p>33%</p>",
                            "<p>24%</p>"
                        ],
                        options_hi: [
                            "<p>28%</p>",
                            "<p>30%</p>",
                            "<p>33%</p>",
                            "<p>24%</p>"
                        ],
                        solution_en: "<p>70.(a) Total number of sales during the months of June and July <br>= 24000 + 18000 = 42000<br>Total number of sale during all the months <br>= 25000 + 24000 + 18000 + 35000 + 26000 + 22000 = 150000<br>Required percentage = <math display=\"inline\"><mfrac><mrow><mn>42000</mn></mrow><mrow><mn>150000</mn></mrow></mfrac></math> &times; 100 = 28%</p>",
                        solution_hi: "<p>70.(a) जून और जुलाई के महीनों के दौरान बिक्री की कुल संख्या <br>= 24000 + 18000 = 42000<br>सभी महीनों के दौरान बिक्री की कुल संख्या&nbsp;<br>= 25000 + 24000 + 18000 + 35000 + 26000 + 22000 = 150000<br>आवश्यक प्रतिशत = <math display=\"inline\"><mfrac><mrow><mn>42000</mn></mrow><mrow><mn>150000</mn></mrow></mfrac></math> &times; 100 = 28%</p>",
                        correct: "a",
                        pos_marks: 2.0,
                        neg_marks: 0.5
                    });
                    questions.push({
                        id: "71",
                        section: "17",
                        question_en: "<p>71. The perpendicular length from the origin to the line 6x + 8y - 48 = 0 is:</p>",
                        question_hi: "<p>71. मूल बिंदु से रेखा 6x + 8y - 48 = 0 तक की लंबवत लंबाई ज्ञात कीजिए।</p>",
                        options_en: [
                            "<p>4.8 unit</p>",
                            "<p>2.6 unit</p>",
                            "<p>8.3 unit</p>",
                            "<p>5.2 unit</p>"
                        ],
                        options_hi: [
                            "<p>4.8 इकाई</p>",
                            "<p>2.6 इकाई</p>",
                            "<p>8.3 इकाई</p>",
                            "<p>5.2 इकाई</p>"
                        ],
                        solution_en: "<p>71.(a)<br>Perpendicular distance from the origin to the line Ax + By + C = 0<br>d = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#160;</mo><mstyle displaystyle=\"true\"><mfrac><mstyle mathvariant=\"bold\"><mo stretchy=\"true\">|</mo><mi>A</mi><mi>x</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mi>B</mi><mi>Y</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mi>C</mi><mo stretchy=\"true\">|</mo></mstyle><msqrt><msup><mi mathvariant=\"bold\">A</mi><mn mathvariant=\"bold\">2</mn></msup><mo mathvariant=\"bold\">&#160;</mo><mo mathvariant=\"bold\">+</mo><msup><mi mathvariant=\"bold\">B</mi><mn mathvariant=\"bold\">2</mn></msup></msqrt></mfrac></mstyle></math><br>Line = 6x + 8y - 48 = 0<br>A = 6 , B = 8 , C = - 48<br>Coordinates of the origin (x<sub>1</sub>,y<sub>1</sub>) = (0 , 0)<br>d = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mstyle mathvariant=\"bold\"><mo stretchy=\"true\">|</mo><mrow><mn>6</mn><mo>(</mo><mn>0</mn><mo>)</mo><mo>+</mo><mn>8</mn><mo>(</mo><mn>0</mn><mo>)</mo><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>48</mn></mrow><mo stretchy=\"true\">|</mo></mstyle><msqrt><msup><mrow><mo mathvariant=\"bold\">(</mo><mn mathvariant=\"bold\">6</mn><mo mathvariant=\"bold\">)</mo></mrow><mn mathvariant=\"bold\">2</mn></msup><mi mathvariant=\"bold\">&#160;</mi><mo mathvariant=\"bold\">+</mo><mi mathvariant=\"bold\">&#160;</mi><msup><mrow><mo mathvariant=\"bold\">(</mo><mn mathvariant=\"bold\">8</mn><mo mathvariant=\"bold\">)</mo></mrow><mn mathvariant=\"bold\">2</mn></msup></msqrt></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>48</mn><mn>10</mn></mfrac></math> = 4.8 unit</p>",
                        solution_hi: "<p>71.(a)<br>मूल बिंदु से रेखा Ax + By + C = 0 तक की लंबवत दूरी<br>d = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"true\"><mfrac><mstyle mathvariant=\"bold\"><mo stretchy=\"true\">|</mo><mi>Ax</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mi>BY</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mi>C</mi><mo stretchy=\"true\">|</mo></mstyle><msqrt><msup><mi mathvariant=\"bold\">A</mi><mn mathvariant=\"bold\">2</mn></msup><mo mathvariant=\"bold\">&#160;</mo><mo mathvariant=\"bold\">+</mo><msup><mi mathvariant=\"bold\">B</mi><mn mathvariant=\"bold\">2</mn></msup></msqrt></mfrac></mstyle></math><br>रेखा = 6x + 8y - 48 = 0<br>A = 6 , B = 8 , C = - 48<br>मूल बिंदु के निर्देशांक (<math display=\"inline\"><msub><mrow><mi>x</mi></mrow><mrow><mn>1</mn></mrow></msub><mo>,</mo><msub><mrow><mi>y</mi></mrow><mrow><mn>1</mn></mrow></msub></math>) = (0 , 0)<br>d = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mstyle mathvariant=\"bold\"><mo stretchy=\"true\">|</mo><mrow><mn>6</mn><mo>(</mo><mn>0</mn><mo>)</mo><mo>+</mo><mn>8</mn><mo>(</mo><mn>0</mn><mo>)</mo><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>48</mn></mrow><mo stretchy=\"true\">|</mo></mstyle><msqrt><msup><mrow><mo mathvariant=\"bold\">(</mo><mn mathvariant=\"bold\">6</mn><mo mathvariant=\"bold\">)</mo></mrow><mn mathvariant=\"bold\">2</mn></msup><mi mathvariant=\"bold\">&#160;</mi><mo mathvariant=\"bold\">+</mo><mi mathvariant=\"bold\">&#160;</mi><msup><mrow><mo mathvariant=\"bold\">(</mo><mn mathvariant=\"bold\">8</mn><mo mathvariant=\"bold\">)</mo></mrow><mn mathvariant=\"bold\">2</mn></msup></msqrt></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>48</mn><mn>10</mn></mfrac></math> = 4.8 इकाई</p>",
                        correct: "a",
                        pos_marks: 2.0,
                        neg_marks: 0.5
                    });
                    questions.push({
                        id: "72",
                        section: "17",
                        question_en: "<p>72. Sudarsan and Abraham appear for an interview for two vacancies. The probability of Sudarsan&rsquo;s selection is <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> and that of Abraham&rsquo;s selection is <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>5</mn></mfrac></math>. Find the probability that none of them be selected.</p>",
                        question_hi: "<p>72. सुदर्शन और अब्राहम दो रिक्तियों के लिए साक्षात्कार के लिए उपस्थित हुए। सुदर्शन के चयन की प्रायिकता <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> है और अब्राहम के चयन की प्रायिकता <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>5</mn></mfrac></math> है। उनमें से किसी के भी चयनित न होने की प्रायिकता ज्ञात कीजिए।</p>",
                        options_en: [
                            "<p><math display=\"inline\"><mfrac><mrow><mn>8</mn></mrow><mrow><mn>15</mn></mrow></mfrac></math></p>",
                            "<p><math display=\"inline\"><mfrac><mrow><mn>14</mn></mrow><mrow><mn>15</mn></mrow></mfrac></math></p>",
                            "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>15</mn></mrow></mfrac></math></p>",
                            "<p><math display=\"inline\"><mfrac><mrow><mn>22</mn></mrow><mrow><mn>15</mn></mrow></mfrac></math></p>"
                        ],
                        options_hi: [
                            "<p><math display=\"inline\"><mfrac><mrow><mn>8</mn></mrow><mrow><mn>15</mn></mrow></mfrac></math></p>",
                            "<p><math display=\"inline\"><mfrac><mrow><mn>14</mn></mrow><mrow><mn>15</mn></mrow></mfrac></math></p>",
                            "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>15</mn></mrow></mfrac></math></p>",
                            "<p><math display=\"inline\"><mfrac><mrow><mn>22</mn></mrow><mrow><mn>15</mn></mrow></mfrac></math></p>"
                        ],
                        solution_en: "<p>72.(a) Probability of sudarsan&rsquo;s selection(P) = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math>, <br>&there4;P&rsquo; =<math display=\"inline\"><mi>&#160;</mi><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math><br>Probability of Abraham&rsquo;s selection(Q) = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math>,<br>&there4; Q&rsquo; = <math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math><br>Hence probability of neither of them selection <br>= P&rsquo; &times; Q&rsquo; = <math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> &times; <math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>8</mn><mn>15</mn></mfrac></math></p>",
                        solution_hi: "<p>72.(a) <br>सुदर्शन के चयन की प्रायिकता(P) = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math>, <br>&there4;P&rsquo; =<math display=\"inline\"><mi>&#160;</mi><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math><br>अब्राहम के चयन की प्रायिकता(Q) = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math>,<br>&there4; Q&rsquo; = <math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math><br>इसलिए उनमें से किसी के भी चयन नहीं होने कि प्रायिकता <br>= P&rsquo; &times; Q&rsquo; = <math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> &times; <math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>8</mn><mn>15</mn></mfrac></math></p>",
                        correct: "a",
                        pos_marks: 2.0,
                        neg_marks: 0.5
                    });
                    questions.push({
                        id: "73",
                        section: "17",
                        question_en: "<p>73. In an isosceles triangle ABC, AB = AC. D is a point inside the triangle such that &ang;BAD =&nbsp;20&deg; = &ang;DCB, &ang;CAD = 80&deg;. The value of &ang;ABC is:</p>",
                        question_hi: "<p>73. एक समद्विबाहु त्रिभुज ABC में, AB = AC है। त्रिभुज के भीतर एक बिंदु D इस प्रकार है कि &ang;BAD = 20&deg; = &ang;DCB, &ang;CAD = 80&deg; है। &ang;ABC का मान_______है।</p>",
                        options_en: [
                            "<p>20&deg;</p>",
                            "<p>25&deg;</p>",
                            "<p>15&deg;</p>",
                            "<p>40&deg;</p>"
                        ],
                        options_hi: [
                            "<p>20&deg;</p>",
                            "<p>25&deg;</p>",
                            "<p>15&deg;</p>",
                            "<p>40&deg;</p>"
                        ],
                        solution_en: "<p>73.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741246704520.png\" alt=\"rId71\" width=\"133\" height=\"146\"><br>&ang;A = 20 + 80 = 100&deg;<br>Let &ang;B and &ang;C be x<br>Then, &ang;A + &ang;B + &ang;C = 180&deg;<br>&rArr; 100&deg; + 2x = 180&deg;<br>&rArr; x = 40&deg;<br>Hence, &ang;ABC = 40&deg;</p>",
                        solution_hi: "<p>73.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741246704520.png\" alt=\"rId71\" width=\"133\" height=\"146\"><br>&ang;A = 20 + 80 = 100&deg;<br>माना प्रत्येक &ang;B और &ang;C का मान x&nbsp;है <br>फिर, &ang;A + &ang;B + &ang;C = 180&deg;<br>&rArr; 100&deg; + 2x = 180&deg;<br>&rArr; x = 40&deg;<br>अतः &ang;ABC = 40&deg;</p>",
                        correct: "d",
                        pos_marks: 2.0,
                        neg_marks: 0.5
                    });
                    questions.push({
                        id: "74",
                        section: "17",
                        question_en: "<p>74. The population of a city was 5000000 in 2020. The population grows 7.5% annually. The&nbsp;population in 2022 is ______.</p>",
                        question_hi: "<p>74. 2020 में एक शहर की जनसंख्या 5000000 थी। जनसंख्या में वार्षिक रूप से 7.5% की वृद्धि होती है |&nbsp;2022 में शहर की जनसंख्या _________ है।</p>",
                        options_en: [
                            "<p>5558875</p>",
                            "<p>5887125</p>",
                            "<p>5875215</p>",
                            "<p>5778125</p>"
                        ],
                        options_hi: [
                            "<p>5558875</p>",
                            "<p>5887125</p>",
                            "<p>5875215</p>",
                            "<p>5778125</p>"
                        ],
                        solution_en: "<p>74.(d)<br>7.5% = <math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>40</mn></mrow></mfrac></math><br>Required population = 5000000 &times; <math display=\"inline\"><mfrac><mrow><mn>43</mn></mrow><mrow><mn>40</mn></mrow></mfrac></math> &times; <math display=\"inline\"><mfrac><mrow><mn>43</mn></mrow><mrow><mn>40</mn></mrow></mfrac></math> <br>= 5778125</p>",
                        solution_hi: "<p>74.(d)<br>7.5% = <math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>40</mn></mrow></mfrac></math><br>आवश्यक जनसंख्या = 5000000 &times; <math display=\"inline\"><mfrac><mrow><mn>43</mn></mrow><mrow><mn>40</mn></mrow></mfrac></math> &times; <math display=\"inline\"><mfrac><mrow><mn>43</mn></mrow><mrow><mn>40</mn></mrow></mfrac></math> <br>= 5778125</p>",
                        correct: "d",
                        pos_marks: 2.0,
                        neg_marks: 0.5
                    });
                    questions.push({
                        id: "75",
                        section: "7",
                        question_en: "<p>75. If the average of 50 numbers N<sub>1</sub>, N<sub>2</sub>,________N<sub>50</sub> is M. Then the average of the numbers N<sub>1</sub>-100, N<sub>2</sub>-100,________N<sub>50</sub> - 100 will be:</p>",
                        question_hi: "<p>75. यदि 50 संख्याओं N<sub>1</sub>, N<sub>2</sub>,________N<sub>50</sub> का औसत M है, तो संख्याओं N<sub>1</sub>-100, N<sub>2</sub>-100,________N<sub>50</sub>&nbsp; - 100 का औसत ज्ञात कीजिए</p>",
                        options_en: [
                            "<p>50M</p>",
                            "<p>M-100</p>",
                            "<p>100M</p>",
                            "<p>M-50</p>"
                        ],
                        options_hi: [
                            "<p>50M</p>",
                            "<p>M-100</p>",
                            "<p>100M</p>",
                            "<p>M-50</p>"
                        ],
                        solution_en: "<p>75.(b)<br>average of 50 numbers N<sub>1</sub>, N<sub>2</sub>,________N<sub>50</sub>&nbsp;is M<br>M = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msub><mi mathvariant=\"normal\">N</mi><mn>1</mn></msub><mo>+</mo><msub><mi mathvariant=\"normal\">N</mi><mn>2</mn></msub><mo>+</mo><mo>_</mo><mo>_</mo><mo>_</mo><mo>_</mo><mo>+</mo><msub><mi mathvariant=\"normal\">N</mi><mn>50</mn></msub></mrow><mn>50</mn></mfrac></math> <br>N<sub>1 </sub>+ N<sub>2</sub>, +________+ N<sub>50</sub> = 50M------(i)<br>Now, numbers N<sub>1</sub>-100, N<sub>2</sub>-100,-----------N<sub>50</sub> - 100<br>Average = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><msub><mi mathvariant=\"normal\">N</mi><mn>1</mn></msub><mo>-</mo><mn>100</mn><mo>)</mo><mo>+</mo><mo>(</mo><msub><mi mathvariant=\"normal\">N</mi><mn>2</mn></msub><mo>-</mo><mn>100</mn><mo>)</mo><mo>_</mo><mo>_</mo><mo>_</mo><mo>_</mo><mo>(</mo><msub><mi mathvariant=\"normal\">N</mi><mn>50</mn></msub><mo>-</mo><mn>100</mn><mo>)</mo></mrow><mn>50</mn></mfrac></math><br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>50</mn><mi mathvariant=\"normal\">M</mi><mo>-</mo><mn>100</mn><mo>&#215;</mo><mn>50</mn><mo>)</mo></mrow><mn>50</mn></mfrac></math><br>= M - 100<br>Hence, required average = M - 100</p>",
                        solution_hi: "<p>75.(b)<br>50 संख्याओं N<sub>1</sub>, N<sub>2</sub>,________N<sub>50</sub> का औसत M है<br>M = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msub><mi mathvariant=\"normal\">N</mi><mn>1</mn></msub><mo>+</mo><msub><mi mathvariant=\"normal\">N</mi><mn>2</mn></msub><mo>+</mo><mo>_</mo><mo>_</mo><mo>_</mo><mo>_</mo><mo>+</mo><msub><mi mathvariant=\"normal\">N</mi><mn>50</mn></msub></mrow><mn>50</mn></mfrac></math> <br>N<sub>1 </sub>+ N<sub>2</sub>, +________+ N<sub>50</sub> = 50M------(i)<br>Now, numbers N<sub>1</sub>-100, N<sub>2</sub>-100,-----------N<sub>50</sub> - 100<br>Average =&nbsp;<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><msub><mi mathvariant=\"normal\">N</mi><mn>1</mn></msub><mo>-</mo><mn>100</mn><mo>)</mo><mo>+</mo><mo>(</mo><msub><mi mathvariant=\"normal\">N</mi><mn>2</mn></msub><mo>-</mo><mn>100</mn><mo>)</mo><mo>_</mo><mo>_</mo><mo>_</mo><mo>_</mo><mo>(</mo><msub><mi mathvariant=\"normal\">N</mi><mn>50</mn></msub><mo>-</mo><mn>100</mn><mo>)</mo></mrow><mn>50</mn></mfrac></math><br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>50</mn><mi mathvariant=\"normal\">M</mi><mo>-</mo><mn>100</mn><mo>&#215;</mo><mn>50</mn><mo>)</mo></mrow><mn>50</mn></mfrac></math><br>= M - 100<br>Hence, required average = M - 100</p>",
                        correct: "b",
                        pos_marks: 2.0,
                        neg_marks: 0.5
                    });
                    questions.push({
                        id: "76",
                        section: "7",
                        question_en: "<p>76. The following sentence has been divided into four parts. Identify the part that contains an error. <br>Pratigya arrived / to the / railway station / five hours early.</p>",
                        question_hi: "<p>76. The following sentence has been divided into four parts. Identify the part that contains an error. <br>Pratigya arrived / to the / railway station / five hours early.</p>",
                        options_en: [
                            "<p>to the</p>",
                            "<p>five hours early.</p>",
                            "<p>Pratigya arrived</p>",
                            "<p>railway station</p>"
                        ],
                        options_hi: [
                            "<p>to the</p>",
                            "<p>five hours early.</p>",
                            "<p>Pratigya arrived</p>",
                            "<p>railway station</p>"
                        ],
                        solution_en: "<p>76.(a) to the <br>&lsquo;To&rsquo; must be replaced with &lsquo;at&rsquo; . We generally use &lsquo;at&rsquo; to describe specific location or position. Hence &lsquo;at the&rsquo; is the most appropriate answer.</p>",
                        solution_hi: "<p>76.(a) to the <br>&lsquo;To&rsquo; के स्थान पर &lsquo;at&rsquo; का प्रयोग होगा। हम सामान्यतः किसी विशेष स्थान (specific location) या स्थिति (position) को describe करने के लिए &lsquo;at&rsquo; का प्रयोग करते हैं। अतः &lsquo;at the&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                        correct: "a",
                        pos_marks: 2.0,
                        neg_marks: 0.5
                    });
                    questions.push({
                        id: "77",
                        section: "7",
                        question_en: "<p>77. Select the most appropriate ANTONYM for the given word.<br>Ghastly</p>",
                        question_hi: "<p>77. Select the most appropriate ANTONYM for the given word.<br>Ghastly</p>",
                        options_en: [
                            "<p>Pleasant</p>",
                            "<p>Relaxing</p>",
                            "<p>Agitated</p>",
                            "<p>Bitter</p>"
                        ],
                        options_hi: [
                            "<p>Pleasant</p>",
                            "<p>Relaxing</p>",
                            "<p>Agitated</p>",
                            "<p>Bitter</p>"
                        ],
                        solution_en: "<p>77.(a) <strong>Pleasant-</strong> giving a sense of happy satisfaction or enjoyment.<br><strong>Ghastly- </strong>extremely unpleasant or bad.<br><strong>Relaxing- </strong>reducing tension or anxiety.<br><strong>Agitated- </strong>to make someone become nervous because of worry or fear .<br><strong>Bitter- </strong>a type of unpleasant taste.</p>",
                        solution_hi: "<p>77.(a) <strong>Pleasant</strong> (सुखद) - giving a sense of happy satisfaction or enjoyment.<br><strong>Ghastly </strong>(भयावह) - extremely unpleasant or bad.<br><strong>Relaxing</strong> (आरामदेह) - reducing tension or anxiety.<br><strong>Agitated</strong> (उत्तेजित) - to make someone become nervous because of worry or fear.<br><strong>Bitter </strong>(कड़वा) - a type of unpleasant taste.</p>",
                        correct: "a",
                        pos_marks: 2.0,
                        neg_marks: 0.5
                    });
                    questions.push({
                        id: "78",
                        section: "7",
                        question_en: "<p>78. Select the most appropriate option that can substitute the underlined segment in the&nbsp;given sentence.<br>As I have no time to discuss the points, <span style=\"text-decoration: underline;\">so I wrote a email to her</span>.</p>",
                        question_hi: "<p>78. Select the most appropriate option that can substitute the underlined segment in the&nbsp;given sentence.<br>As I have no time to discuss the points, <span style=\"text-decoration: underline;\">so I wrote a email to her.</span></p>",
                        options_en: [
                            "<p>I write an email to her</p>",
                            "<p>I wrote an email to her</p>",
                            "<p>so I wrote an e-mail for her</p>",
                            "<p>so I wrote a e-mail to her</p>"
                        ],
                        options_hi: [
                            "<p>I write an email to her</p>",
                            "<p>I wrote an email to her</p>",
                            "<p>so I wrote an e-mail for her</p>",
                            "<p>so I wrote a e-mail to her</p>"
                        ],
                        solution_en: "<p>78.(b) I wrote an e-mail for her<br>The adverb denoting reason (as, since, for) cannot be used together with the adverbs denoting the outcome (thus, so, therefore). Therefore, &lsquo;so&rsquo; must be removed from this sentence. Hence, \'I wrote an e-mail for her\' is the most appropriate answer.</p>",
                        solution_hi: "<p>78.(b) I wrote an e-mail for her<br>Reason को दर्शाने वाले adverb (as, since, for) का प्रयोग परिणाम को दर्शाने वाले adverbs (thus, so, therefore) के साथ नहीं किया जा सकता है। इसलिए, दिए गए sentence से &lsquo;so&rsquo; हटा दिया जाएगा। अतः, \'I wrote an e-mail for her\' सबसे उपयुक्त उत्तर है।</p>",
                        correct: "b",
                        pos_marks: 2.0,
                        neg_marks: 0.5
                    });
                    questions.push({
                        id: "79",
                        section: "7",
                        question_en: "<p>79. Select the most appropriate meaning of the idiom ( in the context).<br>A brave soldier will never <span style=\"text-decoration: underline;\"><strong>show the white feather</strong></span> in the face of his enemy.</p>",
                        question_hi: "<p>79. Select the most appropriate meaning of the idiom ( in the context).<br>A brave soldier will never <span style=\"text-decoration: underline;\"><strong>show the white feather</strong></span> in the face of his enemy.</p>",
                        options_en: [
                            "<p>show signs of cowardice</p>",
                            "<p>act arrogantly</p>",
                            "<p>show intimacy</p>",
                            "<p>act impudently</p>"
                        ],
                        options_hi: [
                            "<p>show signs of cowardice</p>",
                            "<p>act arrogantly</p>",
                            "<p>show intimacy</p>",
                            "<p>act impudently</p>"
                        ],
                        solution_en: "<p>79.(a) show signs of cowardice</p>",
                        solution_hi: "<p>79.(a) show signs of cowardice/ कायरता के लक्षण दिखना।</p>",
                        correct: "a",
                        pos_marks: 2.0,
                        neg_marks: 0.5
                    });
                    questions.push({
                        id: "80",
                        section: "7",
                        question_en: "<p>80. Select the correct active form of the given sentences.<br>Luckily, the machinery was not used by them.</p>",
                        question_hi: "<p>80. Select the correct active form of the given sentences.<br>Luckily, the machinery was not used by them.</p>",
                        options_en: [
                            "<p>Luckily, they had not used the machinery.</p>",
                            "<p>Luckily, they did not used the machinery.</p>",
                            "<p>Luckily, they have not used the machinery.</p>",
                            "<p>Luckily, I did not used the machinery.</p>"
                        ],
                        options_hi: [
                            "<p>Luckily, they had not used the machinery.</p>",
                            "<p>Luckily, they did not used the machinery.</p>",
                            "<p>Luckily, they have not used the machinery.</p>",
                            "<p>Luckily, I did not used the machinery.</p>"
                        ],
                        solution_en: "<p>80.(b)Luckily, they did not use the machinery. (correct)<br>(a) Luckily, they <span style=\"text-decoration: underline;\">had not used</span> the machinery. (Incorrect Tense)<br>(c) Luckily, they <span style=\"text-decoration: underline;\">have not used</span> the machinery. (Incorrect Tense)<br>(d) Luckily, I did not <span style=\"text-decoration: underline;\">used</span> the machinery. (Incorrect Verb)</p>",
                        solution_hi: "<p>80.(b) Luckily, they did not use the machinery. <br>(a) Luckily, they <span style=\"text-decoration: underline;\">had not used</span> the machinery. (गलत Tense)<br>(c) Luckily, they <span style=\"text-decoration: underline;\">have not used</span> the machinery. (गलत Tense)<br>(d) Luckily, I did not <span style=\"text-decoration: underline;\">used</span> the machinery. (गलत Verb)</p>",
                        correct: "b",
                        pos_marks: 2.0,
                        neg_marks: 0.5
                    });
                    questions.push({
                        id: "81",
                        section: "7",
                        question_en: "81. Identify the segment in the sentence which contains grammatical error. Mark ‘no error’ in case the sentence given is correct. <br />A high level meeting of officials is reporting to have discussed the issue in great details. ",
                        question_hi: "81. Identify the segment in the sentence which contains grammatical error. Mark ‘no error’ in case the sentence given is correct. <br />A high level meeting of officials is reporting to have discussed the issue in great details. ",
                        options_en: [
                            " A high level meeting of officials",
                            " is reporting to have discussed",
                            " the issue in great details.",
                            " No error"
                        ],
                        options_hi: [
                            " A high level meeting of officials",
                            " is reporting to have discussed",
                            " the issue in great details.",
                            " No error"
                        ],
                        solution_en: "81.(b) is reporting to have discussed<br />The sentence is in passive voice i.e. “is reported to have discussed” should be used. In passive voice the third form of the verb is used.",
                        solution_hi: "81.(b) is reporting to have discussed<br />वाक्य  passive voice में है अर्थात “is reported to have discussed” का प्रयोग किया जाना चाहिए। passive voice  में verb के third form का प्रयोग होता है।<br />                                          ",
                        correct: "b",
                        pos_marks: 2.0,
                        neg_marks: 0.5
                    });
                    questions.push({
                        id: "82",
                        section: "7",
                        question_en: "82. Given below are four jumbled parts of a sentence. Pick the option that gives their correct order.<br />1 The beauty and majesty <br />A. pose a great challenge<br />B. of the mountains <br />C. that mountains are a <br />D. and like many I believe <br />6 means of communion with God.",
                        question_hi: "82. Given below are four jumbled parts of a sentence. Pick the option that gives their correct order.<br />1 The beauty and majesty <br />A. pose a great challenge<br />B. of the mountains <br />C. that mountains are a <br />D. and like many I believe <br />6 means of communion with God.",
                        options_en: [
                            " BCAD",
                            " BACD",
                            " BADC",
                            " ADCB"
                        ],
                        options_hi: [
                            " BCAD",
                            " BACD",
                            " BADC",
                            " ADCB"
                        ],
                        solution_en: "82. (c) BADC<br />The sentence starts with the given phrase ‘The beauty and majesty’. Part B will follow this phrase as it completes the subject of the sentence i.e. The beauty and majesty of the mountains. However, Part A contains the verb ‘pose’ which will follow Part B. Further, Part D contains the conjunction ‘and’ which will connect Part A to it and Part C contains the subject ‘that mountains’ and the verb ‘are’ which will follow Part D.  Going through the options, option (c) has the correct sequence.  ",
                        solution_hi: "82. (c) BADC<br />वाक्य दिए गए phrase ‘The beauty and majesty’ से शुरू होता है। Part B इस phrase के बाद आएगा  क्योंकि यह वाक्य के subject यानी पहाड़ों की सुंदरता और महिमा को पूरा करता है। हालाँकि, Part A में verb ‘pose’ है जो Part B के बाद आएगा । इसके अलावा, Part D में conjunction ‘and’ है जो Part A को इससे जोड़ेगा और Part C में विषय ‘that mountains’ और verb ‘are’  शामिल हैं जो Part D के बाद आएगा । विकल्पों के माध्यम से, option (c)  में सही क्रम है।<br />       ",
                        correct: "c",
                        pos_marks: 2.0,
                        neg_marks: 0.5
                    });
                    questions.push({
                        id: "83",
                        section: "7",
                        question_en: "<p>83. Select the most appropriate homonym to fill in the blank. <br>Ravi is _______.</p>",
                        question_hi: "<p>83. Select the most appropriate homonym to fill in the blank. <br>Ravi is _______.</p>",
                        options_en: [
                            "<p>bald</p>",
                            "<p>bawled</p>",
                            "<p>baled</p>",
                            "<p>balled</p>"
                        ],
                        options_hi: [
                            "<p>bald</p>",
                            "<p>bawled</p>",
                            "<p>baled</p>",
                            "<p>balled</p>"
                        ],
                        solution_en: "<p>83.(a) bald<br>&lsquo;Bald&rsquo; means having no hair. The given sentence states that Ravi is bald. Hence &lsquo;bald&rsquo; is the most appropriate answer.</p>",
                        solution_hi: "<p>83.(a) bald<br>&lsquo;Bald&rsquo; का अर्थ है बाल न होना। दिए गए sentence में बताया गया है कि रवि गंजा (bald) है। अतः &lsquo;bald&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                        correct: "a",
                        pos_marks: 2.0,
                        neg_marks: 0.5
                    });
                    questions.push({
                        id: "84",
                        section: "7",
                        question_en: "84. Select the INCORRECTLY spelt word. ",
                        question_hi: "84. Select the INCORRECTLY spelt word. ",
                        options_en: [
                            " Ommision",
                            " License",
                            " Aspirant",
                            " Autonomous"
                        ],
                        options_hi: [
                            " Ommision",
                            " License",
                            " Aspirant",
                            " Autonomous<br />         "
                        ],
                        solution_en: "84.(a) Ommision<br />‘Omission’ is the correct spelling.",
                        solution_hi: "84.(a) Ommision<br />‘Omission’ सही spelling है। ",
                        correct: "a",
                        pos_marks: 2.0,
                        neg_marks: 0.5
                    });
                    questions.push({
                        id: "85",
                        section: "7",
                        question_en: "<p>85. From among the words given in bold, select the <strong>INCORRECTLY </strong>spelt word in the following sentence.<br>The action was unfortunately constructed not as the <strong>necessity</strong> of <strong>conscience</strong> but as deliberate <strong>imprudance</strong>, and the barrister was unofficially <strong>reprimanded.</strong></p>",
                        question_hi: "<p>85. From among the words given in bold, select the <strong>INCORRECTL</strong>Y spelt word in the following sentence.<br>The action was unfortunately constructed not as the <strong>necessity</strong> of <strong>conscience</strong> but as deliberate<strong> imprudance, </strong>and the barrister was unofficially<strong> reprimanded.</strong></p>",
                        options_en: [
                            "<p>imprudance</p>",
                            "<p>conscience</p>",
                            "<p>necessity</p>",
                            "<p>reprimanded</p>"
                        ],
                        options_hi: [
                            "<p>imprudance</p>",
                            "<p>conscience</p>",
                            "<p>necessity</p>",
                            "<p>reprimanded</p>"
                        ],
                        solution_en: "<p>85.(a) Imprudence</p>",
                        solution_hi: "<p>85.(a) Imprudence</p>",
                        correct: "a",
                        pos_marks: 2.0,
                        neg_marks: 0.5
                    });
                    questions.push({
                        id: "86",
                        section: "7",
                        question_en: "86. Select the most appropriate meaning of  the idiom<br /> Cool as a cucumber",
                        question_hi: "86. Select the most appropriate meaning of  the idiom<br /> Cool as a cucumber",
                        options_en: [
                            " Nervous and fidgety",
                            " Irritated and annoyed",
                            " Calm and composed",
                            " Happy and excited "
                        ],
                        options_hi: [
                            " Nervous and fidgety",
                            " Irritated and annoyed",
                            " Calm and composed",
                            " Happy and excited "
                        ],
                        solution_en: "86.(c) calm and composed.<br />E.g.- One should be cool as a cucumber in any difficult situation.",
                        solution_hi: "86.(c) calm and composed./ शांत होना। <br />उदाहरण - One should be cool as a cucumber in any difficult situation./व्यक्ति को किसी भी कठिन परिस्थिति में शांत रहना  चाहिए।<br />              ",
                        correct: "c",
                        pos_marks: 2.0,
                        neg_marks: 0.5
                    });
                    questions.push({
                        id: "87",
                        section: "7",
                        question_en: "<p>87. Sentences of a paragraph are given below. While the first and the last sentences (S1 and S6) are in the correct order, the sentences in between are jumbled up. Arrange the sentences in the correct order to form a meaningful and coherent paragraph.&nbsp;</p>\n<p>S1. CV Raman&rsquo;s story begins in a village near Tiruchirapalli (known in his days as Trichinopoly) in southern India.<br>P. When he was four years old, Raman&rsquo;s family moved to Vishakapatnam.<br>Q. He was born on 8 November 1888, the second child of R Chandrashekara Iyer and Parvati Ammal.<br>R. Chandrasekara Iyer was an athletic man and he took active interest in sports.<br>S. His father took up a job there, as a lecturer in Mrs. AV Narsimha Rao College, where he taught physics, mathematics and physical geography.<br>S6. His young son Raman was not physically very strong, but was very intelligent and he preferred to read books and work at his studies.</p>",
                        question_hi: "<p>87. Sentences of a paragraph are given below. While the first and the last sentences (S1 and S6) are in the correct order, the sentences in between are jumbled up. Arrange the sentences in the correct order to form a meaningful and coherent paragraph.&nbsp;</p>\n<p>S1. CV Raman&rsquo;s story begins in a village near Tiruchirapalli (known in his days as Trichinopoly) in southern India.<br>P. When he was four years old, Raman&rsquo;s family moved to Vishakapatnam.<br>Q. He was born on 8 November 1888, the second child of R Chandrashekara Iyer and Parvati Ammal.<br>R. Chandrasekara Iyer was an athletic man and he took active interest in sports.<br>S. His father took up a job there, as a lecturer in Mrs. AV Narsimha Rao College, where he taught physics, mathematics and physical geography.<br>S6. His young son Raman was not physically very strong, but was very intelligent and he preferred to read books and work at his studies.</p>",
                        options_en: [
                            "<p>SRPQ</p>",
                            "<p>PQSR</p>",
                            "<p>QPRS</p>",
                            "<p>QPSR</p>"
                        ],
                        options_hi: [
                            "<p>SRPQ</p>",
                            "<p>PQSR</p>",
                            "<p>QPRS</p>",
                            "<p>QPSR</p>"
                        ],
                        solution_en: "<p>87.(d) <strong>QPSR</strong><br>Sentence Q will follow S1 as it states that CV Raman was born on 8 November 1888. And Sentence P states that Raman&rsquo;s family moved to Vishakhapatnam when he was four years old. So, P will follow Q. Further, Sentence S states that his father took up the job as a lecturer there &amp; Sentence R states that his father, Chandrasekara Iyer was an athletic man and took active interest in sports. So, R will follow S. Going through the options, option &lsquo;d&rsquo; has the correct sequence.</p>",
                        solution_hi: "<p>87.(d) <strong>QPSR</strong><br>S1 के बाद Sentence Q आएगा क्योंकि यह बताता है कि CV Raman का जन्म 8 नवंबर 1888 को हुआ था। और Sentence P बताता है कि Raman का परिवार उनके चार साल का होने पर Visakhapatnam चला गया। इसलिए, P, Q के बाद आएगा। इसके अलावा, Sentence S बताता है कि उनके पिता ने वहां lecturer के रूप में नौकरी की और Sentence R बताता है कि उनके पिता चंद्रशेखर अय्यर एक athletic व्यक्ति थे और sports में सक्रिय रुचि लेते थे। इसलिए, R, S के बाद आएगा। Options के माध्यम से जाने पर, option (d) में सही sequence है।</p>",
                        correct: "d",
                        pos_marks: 2.0,
                        neg_marks: 0.5
                    });
                    questions.push({
                        id: "88",
                        section: "7",
                        question_en: "<p>88. Select the word which means the same as the group of words given.<br>A problem that is so difficult that it cannot be answered</p>",
                        question_hi: "<p>88. Select the word which means the same as the group of words given.<br>A problem that is so difficult that it cannot be answered</p>",
                        options_en: [
                            "<p>Impossible</p>",
                            "<p>Insoluble</p>",
                            "<p>Terrible</p>",
                            "<p>Solvable</p>"
                        ],
                        options_hi: [
                            "<p>Impossible</p>",
                            "<p>Insoluble</p>",
                            "<p>Terrible</p>",
                            "<p>Solvable</p>"
                        ],
                        solution_en: "<p>88.(b) Insoluble<br><strong>Insoluble- </strong>a problem that is so difficult that it cannot be wired <br><strong>Impossible-</strong> not able to be done or to happen <br><strong>Terrible- </strong>causing great shock or injury <br><strong>Solvable-</strong> susceptible of solution or of being solved</p>",
                        solution_hi: "<p>88.(b) Insoluble<br><strong>Insoluble- </strong>एक ऐसी समस्या जो इतनी कठिन है कि इसे solve नहीं किया जा सकता<br><strong>Impossible-</strong> कुछ होने या घटित होने में सक्षम नहीं,असंभव <br><strong>Terrible-</strong> बहुत बड़ा झटका या चोट लगना। <br><strong>Solvable-</strong>समाधान के लिए या हल होने के लिए अतिसंवेदनशील।</p>",
                        correct: "b",
                        pos_marks: 2.0,
                        neg_marks: 0.5
                    });
                    questions.push({
                        id: "89",
                        section: "7",
                        question_en: "<p>89. Identify the best way to improve the underlined part of the given sentence. If there is no improvement required, select &lsquo;no improvement&rsquo;.<br>The summer has <strong><span style=\"text-decoration: underline;\">set out,</span></strong> and the days are getting warm.</p>",
                        question_hi: "<p>89. Identify the best way to improve the underlined part of the given sentence. If there is no improvement required, select &lsquo;no improvement&rsquo;.<br>The summer has <strong><span style=\"text-decoration: underline;\">set out,</span></strong> and the days are getting warm.</p>",
                        options_en: [
                            "<p>set up</p>",
                            "<p>set in</p>",
                            "<p>set off</p>",
                            "<p>No improvement</p>"
                        ],
                        options_hi: [
                            "<p>set up</p>",
                            "<p>set in</p>",
                            "<p>set off</p>",
                            "<p>No improvement</p>"
                        ],
                        solution_en: "<p>89.(b) set in<br>The phrasal verb &lsquo;set in&rsquo; is used when we begin something and will continue it. The given sentence talks about the beginning of the summer season. Hence, &lsquo;set in&rsquo; is the most appropriate answer.</p>",
                        solution_hi: "<p>89.(b) set in<br>Phrasal verb \'set in\' का उपयोग तब किया जाता है जब हम कुछ शुरू करते हैं और इसे जारी रखते हैं । दिया गया वाक्य गर्मी के मौसम की शुरुआत के बारे में बात करता है। इसलिए, \'set in\' सबसे उपयुक्त उत्तर है।</p>",
                        correct: "b",
                        pos_marks: 2.0,
                        neg_marks: 0.5
                    });
                    questions.push({
                        id: "90",
                        section: "7",
                        question_en: "<p>90. Select the word which means the same as the group of words given.<br>Unselfish interest in the welfare of others.</p>",
                        question_hi: "<p>90. Select the word which means the same as the group of words given.<br>Unselfish interest in the welfare of others.</p>",
                        options_en: [
                            "<p>Celibacy</p>",
                            "<p>Misanthropy</p>",
                            "<p>Egoism</p>",
                            "<p>Altruism</p>"
                        ],
                        options_hi: [
                            "<p>Celibacy</p>",
                            "<p>Misanthropy</p>",
                            "<p>Egoism</p>",
                            "<p>Altruism</p>"
                        ],
                        solution_en: "<p>90.(d) <strong>Altruism</strong> <br><strong>Altruism -</strong> unselfish interest in the welfare of others<br><strong>Celibacy - </strong>the state of not being married<br><strong>Misanthropy - </strong>the fact or quality of not liking other people<br><strong>Egoism -</strong> thinking about yourself too much</p>",
                        solution_hi: "<p>90.(d) <strong>Altruism</strong> <br><strong>Altruism-</strong> दूसरों के कल्याण में निःस्वार्थ रुचि। <br><strong>Celibacy- </strong>विवाह न होने की अवस्था या भाव। <br><strong>Misanthropy- </strong>अन्य लोगों को पसंद न करना <br><strong>Egoism- </strong>अपने बारे में बहुत ज्यादा सोचना।</p>",
                        correct: "d",
                        pos_marks: 2.0,
                        neg_marks: 0.5
                    });
                    questions.push({
                        id: "91",
                        section: "7",
                        question_en: "<p>91. Select the most appropriate synonym of the given word.<br>Grisly</p>",
                        question_hi: "<p>91. Select the most appropriate synonym of the given word.<br>Grisly</p>",
                        options_en: [
                            "<p>Dire</p>",
                            "<p>Glorious</p>",
                            "<p>Pleasant</p>",
                            "<p>Atrocious</p>"
                        ],
                        options_hi: [
                            "<p>Dire</p>",
                            "<p>Glorious</p>",
                            "<p>Pleasant</p>",
                            "<p>Atrocious</p>"
                        ],
                        solution_en: "<p>91.(d) <strong>Atrocious- </strong>horrifyingly wicked or cruel.<br><strong>Grisly- </strong>causing horror or disgust.<br><strong>Dire- </strong>extremely serious or urgent.<br><strong>Glorious- </strong>having great beauty or deserving admiration.<br><strong>Pleasant- </strong>giving a sense of enjoyment or satisfaction.</p>",
                        solution_hi: "<p>91.(d) <strong>Atrocious (</strong>अतिदुष्ट) - horrifyingly wicked or cruel.<br><strong>Grisly</strong> (भयानक) - causing horror or disgust.<br><strong>Dire </strong>(सख्त) - extremely serious or urgent.<br><strong>Glorious</strong> (गौरवशाली) - having great beauty or deserving admiration.<br><strong>Pleasant </strong>(सुखद) - giving a sense of enjoyment or satisfaction.</p>",
                        correct: "d",
                        pos_marks: 2.0,
                        neg_marks: 0.5
                    });
                    questions.push({
                        id: "92",
                        section: "7",
                        question_en: "<p>92. Select the correct active form of the given sentences.<br>Our machine is shipped to ten different countries.</p>",
                        question_hi: "<p>92. Select the correct active form of the given sentences.<br>Our machine is shipped to ten different countries.</p>",
                        options_en: [
                            "<p>We shipped our machine to ten different countries.</p>",
                            "<p>We are shipping our machine to ten different countries.</p>",
                            "<p>Someone ship our machine on ten different countries.</p>",
                            "<p>We ship our machine to ten different countries.</p>"
                        ],
                        options_hi: [
                            "<p>We shipped our machine to ten different countries.</p>",
                            "<p>We are shipping our machine to ten different countries.</p>",
                            "<p>Someone ship our machine on ten different countries.</p>",
                            "<p>We ship our machine to ten different countries.</p>"
                        ],
                        solution_en: "<p>92.(d) We ship our machine to ten different countries.<br>(a) <span style=\"text-decoration: underline;\">We shipped</span> our machine to ten different countries. (Incorrect Tense)<br>(b) <span style=\"text-decoration: underline;\">We are shipping</span> our machine to ten different countries. (Incorrect Tense)<br>(c) <span style=\"text-decoration: underline;\">Someone ship</span> our machine on ten different countries. (Incorrect Sentence Structure)</p>",
                        solution_hi: "<p>92.(d) We ship our machine to ten different countries.<br>(a) <span style=\"text-decoration: underline;\">We shipped</span> our machine to ten different countries. (गलत tense)<br>(b) We <span style=\"text-decoration: underline;\">are shipping</span> our machine to ten different countries. (गलत tense)<br>(c) <span style=\"text-decoration: underline;\">Someone ship</span> our machine on ten different countries. (गलत Sentence structure)</p>",
                        correct: "d",
                        pos_marks: 2.0,
                        neg_marks: 0.5
                    });
                    questions.push({
                        id: "93",
                        section: "7",
                        question_en: "<p>93. Pick a word opposite in meaning to <strong>Sober</strong></p>",
                        question_hi: "<p>93. Pick a word opposite in meaning to <strong>Sober</strong></p>",
                        options_en: [
                            "<p>straight</p>",
                            "<p>calm</p>",
                            "<p>drunk</p>",
                            "<p>unwell</p>"
                        ],
                        options_hi: [
                            "<p>straight</p>",
                            "<p>calm</p>",
                            "<p>drunk</p>",
                            "<p>unwell</p>"
                        ],
                        solution_en: "<p>93.(c) drunk <br><strong>Sober - </strong>not affected by alcohol <br><strong>Drunk - </strong>having drunk too much alcohol<br><strong>Straight -</strong> with no bends or curves, going in one direction only <br><strong>Calm - </strong>not excited, worried, or angry<br><strong>Unwell-</strong> sick</p>",
                        solution_hi: "<p>93.(c) drunk <br><strong>Sober-</strong> शराब से प्रभावित नही । <br><strong>Drunk-</strong> बहुत अधिक शराब पीना। <br><strong>Straight-</strong> बिना मोड़ या घुमाव के, केवल एक दिशा में ,सीधा। <br><strong>Calm- </strong>उत्तेजित, चिंतित या क्रोधित नहीं, शांत। <br><strong>Unwell - </strong>बीमार</p>",
                        correct: "c",
                        pos_marks: 2.0,
                        neg_marks: 0.5
                    });
                    questions.push({
                        id: "94",
                        section: "7",
                        question_en: "<p>94. Sentences are given with blanks to be filled in with an appropriate word(s).Four alternatives are suggested for each question. Choose the correct alternative out of the four.<br>Riya did not care _______ me at all.</p>",
                        question_hi: "<p>94. Sentences are given with blanks to be filled in with an appropriate word(s).Four alternatives are suggested for each question. Choose the correct alternative out of the four.<br>Riya did not care _______ me at all.</p>",
                        options_en: [
                            "<p>of</p>",
                            "<p>in</p>",
                            "<p>beside</p>",
                            "<p>about</p>"
                        ],
                        options_hi: [
                            "<p>of</p>",
                            "<p>in</p>",
                            "<p>beside</p>",
                            "<p>about</p>"
                        ],
                        solution_en: "<p>94.(d) about. <br>&ldquo;Care about&rdquo; is a phrasal verb which means to have a strong feeling of love or affection for someone or something.</p>",
                        solution_hi: "<p>94.(d) about. <br>&ldquo;Care about&rdquo; एक phrasal verb है जिसका अर्थ है किसी के लिए या किसी चीज़ के लिए प्यार या स्नेह की एक मजबूत भावना होना।</p>",
                        correct: "d",
                        pos_marks: 2.0,
                        neg_marks: 0.5
                    });
                    questions.push({
                        id: "95",
                        section: "7",
                        question_en: "<p>95.Find a word that is the synonym of&nbsp;<strong>Allay</strong></p>",
                        question_hi: "<p>95.Find a word that is the synonym of&nbsp;<strong>Allay</strong></p>",
                        options_en: [
                            "<p>Relieve</p>",
                            "<p>Release</p>",
                            "<p>Arrange</p>",
                            "<p>Allure</p>"
                        ],
                        options_hi: [
                            "<p>Relieve</p>",
                            "<p>Release</p>",
                            "<p>Arrange</p>",
                            "<p>Allure</p>"
                        ],
                        solution_en: "<p>95.(a) Relieve<br><strong>Allay-</strong> to make something less strong<br><strong>Relieve-</strong> to make an unpleasant feeling or situation stop or get better<br><strong>Release- </strong>to allow somebody/something to be free<br><strong>Arrange-</strong> to put something in order or in a particular pattern<br><strong>Allure-</strong> the quality of being attractive and exciting</p>",
                        solution_hi: "<p>95.(a) Relieve<br><strong>Allay -</strong> तीव्रता या प्रबलता कम करना। <br><strong>Relieve-</strong> एक अप्रिय भावना या स्थिति को रोकने या बेहतर बनाने के लिए। <br><strong>Release- </strong>किसी को/कुछ मुक्त होने की अनुमति देना। <br><strong>Arrange-</strong> किसी चीज को क्रम में या किसी विशेष पैटर्न में रखना। <br><strong>Allure- </strong>आकर्षक और रोमांचक होने का गुण।</p>",
                        correct: "a",
                        pos_marks: 2.0,
                        neg_marks: 0.5
                    });
                    questions.push({
                        id: "96",
                        section: "7",
                        question_en: "<p>96. <strong>Cloze test:</strong><br>There are (96)______ different types of toys that children play with to keep themselves entertained like dolls, trucks and even video games. Another toy that your kids (97)_______ have fun with when they are not able to go outside is the children\'s tent. These types of tents provide (98)_______ opportunities for your children to have fun inside of the home whenever the (99)_______ conditions are not the best. This will (100)______ the risk of them getting muddy outside and tracking the mud on your clean floors and carpets.<br>Select the most appropriate option to fill in the blank number 96</p>",
                        question_hi: "<p>96. <strong>Cloze test:</strong><br>There are (96)______ different types of toys that children play with to keep themselves entertained like dolls, trucks and even video games. Another toy that your kids (97)_______ have fun with when they are not able to go outside is the children\'s tent. These types of tents provide (98)_______ opportunities for your children to have fun inside of the home whenever the (99)_______ conditions are not the best. This will (100)______ the risk of them getting muddy outside and tracking the mud on your clean floors and carpets.<br>Select the most appropriate option to fill in the blank number 96</p>",
                        options_en: [
                            "<p>Much</p>",
                            "<p>Little</p>",
                            "<p>Many</p>",
                            "<p>A Little</p>"
                        ],
                        options_hi: [
                            "<p>Much</p>",
                            "<p>Little</p>",
                            "<p>Many</p>",
                            "<p>A Little</p>"
                        ],
                        solution_en: "<p>96.(c) Many<br>&lsquo;Many&rsquo; is used with countable nouns like shoes, toys, books, etc. The given passage talks about different types of toys. Hence, &lsquo;many&rsquo; is the most appropriate answer.</p>",
                        solution_hi: "<p>96.(c) Many<br>&lsquo;Many&rsquo; का प्रयोग countable noun जैसे - जूते, खिलौने, किताबें आदि के साथ किया जाता है। दिए गए passage में विभिन्न प्रकार के खिलौनों के बारे में बताया गया है। इसलिए &lsquo;Many&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                        correct: "c",
                        pos_marks: 2.0,
                        neg_marks: 0.5
                    });
                    questions.push({
                        id: "97",
                        section: "7",
                        question_en: "<p>97. <strong>Cloze test:</strong><br>There are (96)______ different types of toys that children play with to keep themselves entertained like dolls, trucks and even video games. Another toy that your kids (97)_______ have fun with when they are not able to go outside is the children\'s tent. These types of tents provide (98)_______ opportunities for your children to have fun inside of the home whenever the (99)_______ conditions are not the best. This will (100)______ the risk of them getting muddy outside and tracking the mud on your clean floors and carpets.<br>Select the most appropriate option to fill in the blank number 97</p>",
                        question_hi: "<p>97. <strong>Cloze test:</strong><br>There are (96)______ different types of toys that children play with to keep themselves entertained like dolls, trucks and even video games. Another toy that your kids (97)_______ have fun with when they are not able to go outside is the children\'s tent. These types of tents provide (98)_______ opportunities for your children to have fun inside of the home whenever the (99)_______ conditions are not the best. This will (100)______ the risk of them getting muddy outside and tracking the mud on your clean floors and carpets.<br>Select the most appropriate option to fill in the blank number 97</p>",
                        options_en: [
                            "<p>Can</p>",
                            "<p>Need</p>",
                            "<p>Would</p>",
                            "<p>Ought</p>"
                        ],
                        options_hi: [
                            "<p>Can</p>",
                            "<p>Need</p>",
                            "<p>Would</p>",
                            "<p>Ought</p>"
                        ],
                        solution_en: "<p>97. (a) Can<br>&lsquo;Can&rsquo; is used for showing that it is possible for somebody/something to do something. The given passage talks about the possibility that kids can have fun with another toy. Hence, &lsquo;can&rsquo; is the most appropriate answer.</p>",
                        solution_hi: "<p>97. (a) Can<br>&lsquo;Can&rsquo; का प्रयोग किसी समय पर कुछ करने की संभावना के बारे में बात करने के लिए किया जाता है । दिया गया passage इस संभावना के बारे में बात करता है कि बच्चे दूसरे खिलौने के साथ मनोरंजन कर सकते हैं। इसलिए,&lsquo;can&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                        correct: "a",
                        pos_marks: 2.0,
                        neg_marks: 0.5
                    });
                    questions.push({
                        id: "98",
                        section: "7",
                        question_en: "<p>98. <strong>Cloze test:</strong><br>There are (96)______ different types of toys that children play with to keep themselves entertained like dolls, trucks and even video games. Another toy that your kids (97)_______ have fun with when they are not able to go outside is the children\'s tent. These types of tents provide (98)_______ opportunities for your children to have fun inside of the home whenever the (99)_______ conditions are not the best. This will (100)______ the risk of them getting muddy outside and tracking the mud on your clean floors and carpets.<br>Select the most appropriate option to fill in the blank number 98</p>",
                        question_hi: "<p>98. <strong>Cloze test:</strong><br>There are (96)______ different types of toys that children play with to keep themselves entertained like dolls, trucks and even video games. Another toy that your kids (97)_______ have fun with when they are not able to go outside is the children\'s tent. These types of tents provide (98)_______ opportunities for your children to have fun inside of the home whenever the (99)_______ conditions are not the best. This will (100)______ the risk of them getting muddy outside and tracking the mud on your clean floors and carpets.<br>Select the most appropriate option to fill in the blank number 98</p>",
                        options_en: [
                            "<p>End</p>",
                            "<p>Endlessly</p>",
                            "<p>Endless</p>",
                            "<p>Ending</p>"
                        ],
                        options_hi: [
                            "<p>End</p>",
                            "<p>Endlessly</p>",
                            "<p>Endless</p>",
                            "<p>Ending</p>"
                        ],
                        solution_en: "<p>98.(c) <strong>Endless</strong><br>&lsquo;Endless&rsquo; means lasting for a long time and seeming to have no end. The given passage states that these types of tents provide never-ending(endless) opportunities for your children to have fun. Hence, &lsquo;endless&rsquo; is the most appropriate answer.</p>",
                        solution_hi: "<p>98.(c) <strong>Endless</strong><br>&lsquo;Endless&rsquo; का अर्थ है लंबे समय तक चलने वाला और जिसका कोई अंत न हो। दिए गए passage में कहा गया है कि इस प्रकार के tents आपके बच्चों को मौज-मस्ती करने के कभी न खत्म होने वाले (अन्तहीन) अवसर प्रदान करते हैं। इसलिए, &lsquo;Endless&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                        correct: "c",
                        pos_marks: 2.0,
                        neg_marks: 0.5
                    });
                    questions.push({
                        id: "99",
                        section: "7",
                        question_en: "<p>99. <strong>Cloze test:</strong><br>There are (96)______ different types of toys that children play with to keep themselves entertained like dolls, trucks and even video games. Another toy that your kids (97)_______ have fun with when they are not able to go outside is the children\'s tent. These types of tents provide (98)_______ opportunities for your children to have fun inside of the home whenever the (99)_______ conditions are not the best. This will (100)______ the risk of them getting muddy outside and tracking the mud on your clean floors and carpets.<br>Select the most appropriate option to fill in the blank number 99</p>",
                        question_hi: "<p>99. <strong>Cloze test:</strong><br>There are (96)______ different types of toys that children play with to keep themselves entertained like dolls, trucks and even video games. Another toy that your kids (97)_______ have fun with when they are not able to go outside is the children\'s tent. These types of tents provide (98)_______ opportunities for your children to have fun inside of the home whenever the (99)_______ conditions are not the best. This will (100)______ the risk of them getting muddy outside and tracking the mud on your clean floors and carpets.<br>Select the most appropriate option to fill in the blank number 99</p>",
                        options_en: [
                            "<p>Weather</p>",
                            "<p>Whether</p>",
                            "<p>Wither</p>",
                            "<p>Winsome</p>"
                        ],
                        options_hi: [
                            "<p>Weather</p>",
                            "<p>Whether</p>",
                            "<p>Wither</p>",
                            "<p>Winsome</p>"
                        ],
                        solution_en: "<p>99.(a) Weather<br>&lsquo;Weather&rsquo; means the climate at a certain place and time, how much wind, rain, sun, etc. The given passage states that these types of tents provide endless opportunities for your children whenever the weather conditions are not the best. Hence, &lsquo;weather&rsquo; is the most appropriate answer.</p>",
                        solution_hi: "<p>99.(a) Weather<br>&lsquo;Weather&rsquo; का अर्थ है किसी समय किसी स्&zwj;थान का तापमान, पवन-गति, वर्षा-मात्रा, आर्द्रता आदि। दिए गए passage में कहा गया है कि जब भी मौसम की स्थिति अच्छी नहीं होती है, तब इस प्रकार के tents आपके बच्चों को मौज - मस्ती के लिए अवसर प्रदान करते हैं। इसलिए, &lsquo;Weather&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                        correct: "a",
                        pos_marks: 2.0,
                        neg_marks: 0.5
                    });
                    questions.push({
                        id: "100",
                        section: "misc",
                        question_en: "<p>100. <strong>Cloze test:</strong><br>There are (96)______ different types of toys that children play with to keep themselves entertained like dolls, trucks and even video games. Another toy that your kids (97)_______ have fun with when they are not able to go outside is the children\'s tent. These types of tents provide (98)_______ opportunities for your children to have fun inside of the home whenever the (99)_______ conditions are not the best. This will (100)______ the risk of them getting muddy outside and tracking the mud on your clean floors and carpets.<br>Select the most appropriate option to fill in the blank number 100</p>",
                        question_hi: "<p>100. <strong>Cloze test:</strong><br>There are (96)______ different types of toys that children play with to keep themselves entertained like dolls, trucks and even video games. Another toy that your kids (97)_______ have fun with when they are not able to go outside is the children\'s tent. These types of tents provide (98)_______ opportunities for your children to have fun inside of the home whenever the (99)_______ conditions are not the best. This will (100)______ the risk of them getting muddy outside and tracking the mud on your clean floors and carpets.<br>Select the most appropriate option to fill in the blank number 100</p>",
                        options_en: [
                            "<p>Minimum</p>",
                            "<p>Reduce</p>",
                            "<p>Increase</p>",
                            "<p>Rise</p>"
                        ],
                        options_hi: [
                            "<p>Minimum</p>",
                            "<p>Reduce</p>",
                            "<p>Increase</p>",
                            "<p>Rise</p>"
                        ],
                        solution_en: "<p>100.(b) <strong>Reduce</strong> <br>&lsquo;Reduce&rsquo; means to make something less or smaller in quantity, price, size, etc. The given passage states that playing in tents will reduce the risk of them getting muddy outside. Hence, &lsquo;reduce&rsquo; is the most appropriate answer.</p>",
                        solution_hi: "<p>100.(b) <strong>Reduce</strong> <br>&lsquo;Reduce&rsquo; का अर्थ मात्रा, मूल्य, आकार आदि में कुछ कम या छोटा करना है। दिए गए passage में कहा गया है कि tents में खेलने से उनको बाहरी कीचड़ का खतरा कम हो जाएगा। इसलिए, &lsquo;Reduce&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                        correct: "b",
                        pos_marks: 2.0,
                        neg_marks: 0.5
                    });
                // Generate question boxes
                const boxesContainer = document.getElementById('question-boxes');
                questions.forEach((q, index) => {
                    const box = document.createElement('div');
                    box.className = 'question-box d-flex align-items-center justify-content-center border rounded';
                    box.textContent = index + 1;
                    box.onclick = () => showQuestion(index);
                    box.id = `box-${index}`;
                    boxesContainer.appendChild(box);
                });
                // Show first question
                showQuestion(0);
                updateProgress();
            }
            // Show a specific question
            function showQuestion(index) {
                if (index < 0 || index >= questions.length) return;
                currentQuestion = index;
                const question = questions[index];
                // Update question counter
                document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
                // Update question boxes
                document.querySelectorAll('.question-box').forEach(box => box.classList.remove('current'));
                document.getElementById(`box-${index}`).classList.add('current');
                // Generate question HTML
                const html = `
                    <div class="card shadow-sm mb-4">
                        <div class="card-body">
                            <div class="question-text mb-4">
                                <div class="en">${question.question_en}</div>
                                <div class="hi" style="display:none">${question.question_hi}</div>
                            </div>
                            <div class="options">
                                ${['a', 'b', 'c', 'd'].map((opt, i) => `
                                    <div class="form-check mb-3">
                                        <input class="form-check-input" type="radio" name="q${index}" 
                                            id="opt-${index}-${opt}" value="${opt}" 
                                            ${answers[question.id] === opt ? 'checked' : ''}
                                            onclick="selectAnswer('${question.id}', '${opt}')">
                                        <label class="form-check-label w-100 py-2 px-3 rounded hover-bg-light" for="opt-${index}-${opt}">
                                            <div class="en">${question.options_en[i]}</div>
                                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                                        </label>
                                    </div>
                                `).join('')}
                            </div>
                            ${submitted ? `
                                <div class="alert ${answers[question.id] === question.correct ? 'alert-success' : 'alert-danger'} mt-3">
                                    ${answers[question.id] ? (answers[question.id] === question.correct ? 'Correct!' : 'Incorrect!') : 'Not attempted'}
                                    <div class="mt-2">
                                        <strong>Solution:</strong>
                                        <div class="en">${question.solution_en}</div>
                                        <div class="hi" style="display:none">${question.solution_hi}</div>
                                    </div>
                                </div>
                            ` : ''}
                        </div>
                    </div>
                `;
                document.getElementById('questions-container').innerHTML = html;
                // Apply language settings
                document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
                document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
                // If in mobile view, close the menu
                if (window.innerWidth < 768) {
                    toggleMenu(false);
                }
            }
            // Select an answer
            function selectAnswer(qId, option) {
                if (submitted) return;
                answers[qId] = option;
                updateProgress();
                // Mark question as attempted in the navigation
                const index = questions.findIndex(q => q.id === qId);
                if (index >= 0) {
                    document.getElementById(`box-${index}`).classList.add('attempted');
                }
            }
            // Navigate to previous question
            function prevQuestion() {
                if (currentQuestion > 0) {
                    showQuestion(currentQuestion - 1);
                }
            }
            // Navigate to next question
            function nextQuestion() {
                if (currentQuestion < questions.length - 1) {
                    showQuestion(currentQuestion + 1);
                }
            }
            // Switch section
            function switchSection(sectionId) {
                if (sectionId === 'all') {
                    showQuestion(0);
                    return;
                }
                const section = sections[sectionId];
                if (section) {
                    showQuestion(section.start);
                }
            }
            // Toggle language
            function toggleLanguage() {
                currentLang = currentLang === 'en' ? 'hi' : 'en';
                document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
                document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
                // Update button text
                document.getElementById('lang-btn').innerHTML = currentLang === 'en' ? 
                    '<i class="fas fa-language"></i> हिंदी' : 
                    '<i class="fas fa-language"></i> English';
            }
            // Toggle menu for mobile
            function toggleMenu(show) {
                const nav = document.getElementById('question-nav');
                const content = document.getElementById('question-content');
                if (show === undefined) {
                    show = nav.style.right === '-100%' || nav.style.right === '';
                }
                if (show) {
                    nav.style.right = '0';
                    if (window.innerWidth >= 768) {
                        content.style.marginRight = '320px';
                    }
                } else {
                    nav.style.right = window.innerWidth < 768 ? '-100%' : '0';
                    content.style.marginRight = window.innerWidth < 768 ? '0' : '320px';
                }
            }
            // Update progress
            function updateProgress() {
                const attempted = Object.keys(answers).length;
                document.getElementById('attempted-count').textContent = attempted;
                document.getElementById('total-count').textContent = questions.length;
            }
            // Start timer
            function startTimer() {
                const timerDisplay = document.getElementById('timer-display');
                let timeLeft = totalTime;
                const timerInterval = setInterval(() => {
                    if (timeLeft <= 0 || submitted) {
                        clearInterval(timerInterval);
                        if (!submitted) submitTest();
                        return;
                    }
                    const minutes = Math.floor(timeLeft / 60);
                    const seconds = timeLeft % 60;
                    timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                    timeLeft--;
                }, 1000);
            }
            // Submit test
            function submitTest() {
                if (submitted) return;
                submitted = true;
                let score = 0;
                let correctCount = 0;
                let incorrectCount = 0;
                let unattemptedCount = 0;
                questions.forEach((q, index) => {
                    const userAns = answers[q.id];
                    const box = document.getElementById(`box-${index}`);
                    if (!userAns) {
                        unattemptedCount++;
                        box.classList.remove('attempted');
                        box.classList.add('bg-secondary', 'text-white');
                    } else if (userAns === q.correct) {
                        correctCount++;
                        score += q.pos_marks;
                        box.classList.remove('attempted');
                        box.classList.add('correct');
                    } else {
                        incorrectCount++;
                        score -= q.neg_marks;
                        box.classList.remove('attempted');
                        box.classList.add('incorrect');
                    }
                });
                // Update result values
                document.getElementById('score-value').textContent = score.toFixed(2);
                document.getElementById('correct-value').textContent = correctCount;
                document.getElementById('incorrect-value').textContent = incorrectCount;
                document.getElementById('unattempted-value').textContent = unattemptedCount;
                // Show the results modal
                resultsModal.show();
                // Disable submit button
                const submitBtn = document.getElementById('submit-btn');
                submitBtn.disabled = true;
                submitBtn.innerHTML = '<i class="fas fa-check-circle me-1"></i> Submitted';
                // Refresh current question view to show solution
                showQuestion(currentQuestion);
            }
            // Review test
            function reviewTest() {
                resultsModal.hide();
                showQuestion(0);
            }
        </script>
    </body>
    </html>