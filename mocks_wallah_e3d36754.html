<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">15:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 15</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">15</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 15 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 0,
                end: 13
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 14,
                end: 14
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "17",
                    question_en: "<p>1. <span style=\"font-family: Cambria Math;\">What is the mean proportional of 9.61 and 3.61 ?</span></p>\n",
                    question_hi: "<p>1. <span style=\"font-family: Cambria Math;\">9.61 </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> 3.61 </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2343;&#2381;&#2351;&#2366;&#2344;&#2369;&#2346;&#2366;&#2340;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> ?</span></p>\n",
                    options_en: ["<p>3.17</p>\n", "<p>5.89</p>\n", 
                                "<p>4.87</p>\n", "<p>6.78</p>\n"],
                    options_hi: ["<p>3.17</p>\n", "<p>5.89</p>\n",
                                "<p>4.87</p>\n", "<p>6.78</p>\n"],
                    solution_en: "<p>1.(b) <span style=\"font-family: Cambria Math;\">According to the question,</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Formula</strong> : - Mean proportional of a and b =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mi>a</mi><mi>b</mi></msqrt></math></span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Mean proportional of 9.61 and 3.61 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>9</mn><mo>.</mo><mn>61</mn><mo>&times;</mo><mn>3</mn><mo>.</mo><mn>61</mn></msqrt></math></span><span style=\"font-family: Cambria Math;\"> = 5.89</span></p>\n",
                    solution_hi: "<p>1.(b) <span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2358;&#2381;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2344;&#2369;&#2360;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\">,</span></p>\r\n<p><strong><span style=\"font-family: Nirmala UI;\">&#2360;&#2370;&#2340;&#2381;&#2352;</span></strong><span style=\"font-family: Cambria Math;\"><strong>:-</strong> a </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> b </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2343;&#2381;&#2351;&#2366;&#2344;&#2369;&#2346;&#2366;&#2340;&#2368;</span><span style=\"font-family: Cambria Math;\"> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mi>a</mi><mi>b</mi></msqrt></math></span></p>\r\n<p><span style=\"font-family: Cambria Math;\">9.61 </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> 3.61 </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2343;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2344;&#2369;&#2346;&#2366;&#2340;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>9</mn><mo>.</mo><mn>61</mn><mo>&times;</mo><mn>3</mn><mo>.</mo><mn>61</mn></msqrt></math></span><span style=\"font-family: Cambria Math;\"> = 5.89</span></p>\n",
                    correct: "b",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "2",
                    section: "17",
                    question_en: "<p>2. <span style=\"font-family: Cambria Math;\">Two numbers A and B are</span><span style=\"font-family: Cambria Math;\"> such that the sum of 6% of A and 4% of B is two-fifth of the sum of 7% of A and 11% of B, then the ratio A : B is:</span></p>\n",
                    question_hi: "<p>2. <span style=\"font-family: Nirmala UI;\">&#2342;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;&#2319;&#2306;</span><span style=\"font-family: Cambria Math;\"> A </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> B </span><span style=\"font-family: Nirmala UI;\">&#2311;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2325;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;</span><span style=\"font-family: Cambria Math;\"> A </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> 6% </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> B </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> 4% </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2351;&#2379;&#2327;</span><span style=\"font-family: Cambria Math;\">, A </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> 7% </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> B </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> 11% </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2351;&#2379;&#2327;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>5</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2344;&#2369;&#2346;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> A : B </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> ?</span></p>\n",
                    options_en: ["<p>3 : 7</p>\n", "<p>5 : 8</p>\n", 
                                "<p>1 : 8</p>\n", "<p>1 : 7</p>\n"],
                    options_hi: ["<p>3 : 7</p>\n", "<p>5 : 8</p>\n",
                                "<p>1 : 8</p>\n", "<p>1 : 7</p>\n"],
                    solution_en: "<p>2.(c) <span style=\"font-family: Cambria Math;\">According to the question,</span></p>\r\n<p><span style=\"font-weight: 400;\">(A &times; 6% + B &times; 4%) </span>= (A &times; 7% + B <span style=\"font-family: Cambria Math;\">&times; 11%) &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>5</mn></mfrac></math></span></p>\r\n<p><span style=\"font-family: Cambria Math;\">A &times; 30% + B &times; 20% = A &times; 14% + B &times; 22%</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">A &times; 16% = B &times; 2%</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">A : B = 1 : 8</span></p>\n",
                    solution_hi: "<p>2.(c) <span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2358;&#2381;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2344;&#2369;&#2360;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\">,</span></p>\r\n<p><span style=\"font-weight: 400;\">(A &times; 6% + B &times; 4%) </span>= (A &times; 7% + B &times; 11%) &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>5</mn></mfrac></math></p>\r\n<p><span style=\"font-family: Cambria Math;\">A &times; 30% + B &times; 20% = A &times; 14% + B &times; 22%</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">A &times; 16% = B &times; 2%</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">A : B = 1 : 8</span></p>\n",
                    correct: "c",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "3",
                    section: "17",
                    question_en: "<p>3. <span style=\"font-family: Cambria Math;\">Find the fourth proportional to <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><mi mathvariant=\"normal\">a</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mi mathvariant=\"normal\">b</mi><mo>)</mo></mrow><mn>2</mn></msup><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mo>(</mo><msup><mi mathvariant=\"normal\">a</mi><mn>3</mn></msup><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><msup><mi mathvariant=\"normal\">b</mi><mn>3</mn></msup><mo>)</mo><mo>,</mo><mo>(</mo><msup><mi mathvariant=\"normal\">a</mi><mn>3</mn></msup><mo>+</mo><mo>&nbsp;</mo><msup><mi mathvariant=\"normal\">b</mi><mn>3</mn></msup><mo>)</mo><mo>,</mo><mo>(</mo><msup><mi mathvariant=\"normal\">a</mi><mn>2</mn></msup><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><msup><mi mathvariant=\"normal\">b</mi><mn>2</mn></msup><mo>)</mo><mo>.</mo></math></span></p>\n",
                    question_hi: "<p>3. <span style=\"font-family: Cambria Math;\">&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><mi mathvariant=\"normal\">a</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mi mathvariant=\"normal\">b</mi><mo>)</mo></mrow><mn>2</mn></msup><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mo>(</mo><msup><mi mathvariant=\"normal\">a</mi><mn>3</mn></msup><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><msup><mi mathvariant=\"normal\">b</mi><mn>3</mn></msup><mo>)</mo><mo>,</mo><mo>(</mo><msup><mi mathvariant=\"normal\">a</mi><mn>3</mn></msup><mo>+</mo><mo>&nbsp;</mo><msup><mi mathvariant=\"normal\">b</mi><mn>3</mn></msup><mo>)</mo><mo>,</mo><mo>(</mo><msup><mi mathvariant=\"normal\">a</mi><mn>2</mn></msup><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><msup><mi mathvariant=\"normal\">b</mi><mn>2</mn></msup><mo>)</mo></math> </span><span style=\"font-weight: 400;\">&#2325;&#2366; &#2330;&#2340;&#2369;&#2352;&#2381;&#2341; &#2360;&#2350;&#2366;&#2344;&#2369;&#2346;&#2366;&#2340;&#2368; &#2332;&#2381;&#2334;&#2366;&#2340; &#2325;&#2368;&#2332;&#2367;&#2319;&#2404;</span></p>\n",
                    options_en: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><msup><mi mathvariant=\"normal\">a</mi><mn>2</mn></msup><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mi>ab</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><msup><mi mathvariant=\"normal\">b</mi><mn>2</mn></msup><mo>)</mo></mrow><mrow><mo>(</mo><mi mathvariant=\"normal\">a</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mi mathvariant=\"normal\">b</mi><mo>)</mo></mrow></mfrac></math></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><msup><mi mathvariant=\"normal\">a</mi><mrow><mn>2</mn><mo>&nbsp;</mo></mrow></msup><mo>+</mo><mo>&nbsp;</mo><mi>ab</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><msup><mi mathvariant=\"normal\">b</mi><mn>2</mn></msup><mo>)</mo></mrow><mrow><mo>(</mo><mi mathvariant=\"normal\">a</mi><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mi mathvariant=\"normal\">b</mi><mo>)</mo></mrow></mfrac></math></p>\n", 
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><msup><mi mathvariant=\"normal\">a</mi><mrow><mn>2</mn><mo>&nbsp;</mo></mrow></msup><mo>-</mo><mo>&nbsp;</mo><mi>ab</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><msup><mi mathvariant=\"normal\">b</mi><mn>2</mn></msup><mo>)</mo></mrow><mrow><mo>(</mo><msup><mi mathvariant=\"normal\">a</mi><mn>2</mn></msup><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mi>ab</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><msup><mi mathvariant=\"normal\">b</mi><mn>2</mn></msup><mo>)</mo></mrow></mfrac></math></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><msup><mi mathvariant=\"normal\">a</mi><mrow><mn>2</mn><mo>&nbsp;</mo></mrow></msup><mo>+</mo><mo>&nbsp;</mo><mi>ab</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><msup><mi mathvariant=\"normal\">b</mi><mn>2</mn></msup><mo>)</mo></mrow><mrow><mo>(</mo><msup><mi mathvariant=\"normal\">a</mi><mn>2</mn></msup><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><msup><mi mathvariant=\"normal\">b</mi><mn>2</mn></msup><mo>)</mo></mrow></mfrac></math></p>\n"],
                    options_hi: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><msup><mi mathvariant=\"normal\">a</mi><mn>2</mn></msup><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mi>ab</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><msup><mi mathvariant=\"normal\">b</mi><mn>2</mn></msup><mo>)</mo></mrow><mrow><mo>(</mo><mi mathvariant=\"normal\">a</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mi mathvariant=\"normal\">b</mi><mo>)</mo></mrow></mfrac></math></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><msup><mi mathvariant=\"normal\">a</mi><mrow><mn>2</mn><mo>&nbsp;</mo></mrow></msup><mo>+</mo><mo>&nbsp;</mo><mi>ab</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><msup><mi mathvariant=\"normal\">b</mi><mn>2</mn></msup><mo>)</mo></mrow><mrow><mo>(</mo><mi mathvariant=\"normal\">a</mi><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mi mathvariant=\"normal\">b</mi><mo>)</mo></mrow></mfrac></math></p>\n",
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><msup><mi mathvariant=\"normal\">a</mi><mrow><mn>2</mn><mo>&nbsp;</mo></mrow></msup><mo>-</mo><mo>&nbsp;</mo><mi>ab</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><msup><mi mathvariant=\"normal\">b</mi><mn>2</mn></msup><mo>)</mo></mrow><mrow><mo>(</mo><msup><mi mathvariant=\"normal\">a</mi><mn>2</mn></msup><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mi>ab</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><msup><mi mathvariant=\"normal\">b</mi><mn>2</mn></msup><mo>)</mo></mrow></mfrac></math></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><msup><mi mathvariant=\"normal\">a</mi><mrow><mn>2</mn><mo>&nbsp;</mo></mrow></msup><mo>+</mo><mo>&nbsp;</mo><mi>ab</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><msup><mi mathvariant=\"normal\">b</mi><mn>2</mn></msup><mo>)</mo></mrow><mrow><mo>(</mo><msup><mi mathvariant=\"normal\">a</mi><mn>2</mn></msup><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><msup><mi mathvariant=\"normal\">b</mi><mn>2</mn></msup><mo>)</mo></mrow></mfrac></math></p>\n"],
                    solution_en: "<p>3.(c) <span style=\"font-family: Cambria Math;\">Fourth proportional =</span><span style=\"font-family: Cambria Math;\">&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><msup><mi mathvariant=\"normal\">a</mi><mn>3</mn></msup><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><msup><mi mathvariant=\"normal\">b</mi><mn>3</mn></msup><mo>&nbsp;</mo><mo>)</mo><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mo>(</mo><msup><mi mathvariant=\"normal\">a</mi><mrow><mn>2</mn><mo>&nbsp;</mo></mrow></msup><mo>-</mo><mo>&nbsp;</mo><msup><mi mathvariant=\"normal\">b</mi><mn>2</mn></msup><mo>&nbsp;</mo><mo>)</mo></mrow><mrow><msup><mrow><mo>(</mo><mi mathvariant=\"normal\">a</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mi mathvariant=\"normal\">b</mi><mo>)</mo></mrow><mn>2</mn></msup><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>(</mo><msup><mi mathvariant=\"normal\">a</mi><mn>3</mn></msup><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><msup><mi mathvariant=\"normal\">b</mi><mn>3</mn></msup><mo>&nbsp;</mo><mo>)</mo></mrow></mfrac><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mfrac><mrow><mo>(</mo><mi mathvariant=\"normal\">a</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mi mathvariant=\"normal\">b</mi><mo>)</mo><mo>&nbsp;</mo><mo>(</mo><msup><mi mathvariant=\"normal\">a</mi><mn>2</mn></msup><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mi>ab</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><msup><mi mathvariant=\"normal\">b</mi><mn>2</mn></msup><mo>&nbsp;</mo><mo>)</mo><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mo>(</mo><mi mathvariant=\"normal\">a</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mi mathvariant=\"normal\">b</mi><mo>)</mo><mo>&nbsp;</mo><mo>(</mo><mi mathvariant=\"normal\">a</mi><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mi mathvariant=\"normal\">b</mi><mo>)</mo></mrow><mrow><msup><mrow><mo>(</mo><mi mathvariant=\"normal\">a</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mi mathvariant=\"normal\">b</mi><mo>)</mo></mrow><mn>2</mn></msup><mo>&nbsp;</mo><mo>(</mo><mi mathvariant=\"normal\">a</mi><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mi mathvariant=\"normal\">b</mi><mo>)</mo><mo>&nbsp;</mo><mo>(</mo><msup><mi mathvariant=\"normal\">a</mi><mn>2</mn></msup><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mi>ab</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><msup><mi mathvariant=\"normal\">b</mi><mn>2</mn></msup><mo>)</mo><mo>&nbsp;</mo></mrow></mfrac><mspace linebreak=\"newline\"></mspace><mo>=</mo><mfrac><mrow><mo>(</mo><msup><mi mathvariant=\"normal\">a</mi><mn>2</mn></msup><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mi>ab</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><msup><mi mathvariant=\"normal\">b</mi><mn>2</mn></msup><mo>&nbsp;</mo><mo>)</mo></mrow><mrow><mo>(</mo><msup><mi mathvariant=\"normal\">a</mi><mrow><mn>2</mn><mo>&nbsp;</mo></mrow></msup><mo>+</mo><mo>&nbsp;</mo><mi>ab</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><msup><mi mathvariant=\"normal\">b</mi><mn>2</mn></msup><mo>&nbsp;</mo><mo>)</mo></mrow></mfrac><mspace linebreak=\"newline\"></mspace></math></span></p>\n",
                    solution_hi: "<p>3.(c) <span style=\"font-weight: 400;\">&#2330;&#2340;&#2369;&#2352;&#2381;&#2341; &#2310;&#2344;&#2369;&#2346;&#2366;&#2340;&#2367;&#2325; =</span><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><msup><mi mathvariant=\"normal\">a</mi><mn>3</mn></msup><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><msup><mi mathvariant=\"normal\">b</mi><mn>3</mn></msup><mo>&nbsp;</mo><mo>)</mo><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mo>(</mo><msup><mi mathvariant=\"normal\">a</mi><mrow><mn>2</mn><mo>&nbsp;</mo></mrow></msup><mo>-</mo><mo>&nbsp;</mo><msup><mi mathvariant=\"normal\">b</mi><mn>2</mn></msup><mo>&nbsp;</mo><mo>)</mo></mrow><mrow><msup><mrow><mo>(</mo><mi mathvariant=\"normal\">a</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mi mathvariant=\"normal\">b</mi><mo>)</mo></mrow><mn>2</mn></msup><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>(</mo><msup><mi mathvariant=\"normal\">a</mi><mn>3</mn></msup><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><msup><mi mathvariant=\"normal\">b</mi><mn>3</mn></msup><mo>&nbsp;</mo><mo>)</mo></mrow></mfrac><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mfrac><mrow><mo>(</mo><mi mathvariant=\"normal\">a</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mi mathvariant=\"normal\">b</mi><mo>)</mo><mo>&nbsp;</mo><mo>(</mo><msup><mi mathvariant=\"normal\">a</mi><mn>2</mn></msup><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mi>ab</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><msup><mi mathvariant=\"normal\">b</mi><mn>2</mn></msup><mo>&nbsp;</mo><mo>)</mo><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mo>(</mo><mi mathvariant=\"normal\">a</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mi mathvariant=\"normal\">b</mi><mo>)</mo><mo>&nbsp;</mo><mo>(</mo><mi mathvariant=\"normal\">a</mi><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mi mathvariant=\"normal\">b</mi><mo>)</mo></mrow><mrow><msup><mrow><mo>(</mo><mi mathvariant=\"normal\">a</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mi mathvariant=\"normal\">b</mi><mo>)</mo></mrow><mn>2</mn></msup><mo>&nbsp;</mo><mo>(</mo><mi mathvariant=\"normal\">a</mi><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mi mathvariant=\"normal\">b</mi><mo>)</mo><mo>&nbsp;</mo><mo>(</mo><msup><mi mathvariant=\"normal\">a</mi><mn>2</mn></msup><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mi>ab</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><msup><mi mathvariant=\"normal\">b</mi><mn>2</mn></msup><mo>)</mo><mo>&nbsp;</mo></mrow></mfrac><mspace linebreak=\"newline\"></mspace><mo>=</mo><mfrac><mrow><mo>(</mo><msup><mi mathvariant=\"normal\">a</mi><mn>2</mn></msup><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mi>ab</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><msup><mi mathvariant=\"normal\">b</mi><mn>2</mn></msup><mo>&nbsp;</mo><mo>)</mo></mrow><mrow><mo>(</mo><msup><mi mathvariant=\"normal\">a</mi><mrow><mn>2</mn><mo>&nbsp;</mo></mrow></msup><mo>+</mo><mo>&nbsp;</mo><mi>ab</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><msup><mi mathvariant=\"normal\">b</mi><mn>2</mn></msup><mo>&nbsp;</mo><mo>)</mo></mrow></mfrac><mspace linebreak=\"newline\"></mspace></math></span></p>\n",
                    correct: "c",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "4",
                    section: "17",
                    question_en: "<p>4. <span style=\"font-family: Cambria Math;\">P and Q are inversely proportional to each other and P is 10, when Q is 6. What will be the value of Q when P is 15 ?</span></p>\n",
                    question_hi: "<p>4. <span style=\"font-family: Cambria Math;\">P </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> Q </span><span style=\"font-family: Nirmala UI;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\">-</span><span style=\"font-family: Nirmala UI;\">&#2342;&#2370;&#2360;&#2352;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2381;&#2351;&#2369;&#2340;&#2381;&#2325;&#2381;&#2352;&#2350;&#2366;&#2344;&#2369;&#2346;&#2366;&#2340;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2348;</span><span style=\"font-family: Cambria Math;\"> Q </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2352;&#2366;&#2348;&#2352;</span><span style=\"font-family: Cambria Math;\"> 6 </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> P </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2352;&#2366;&#2348;&#2352;</span><span style=\"font-family: Cambria Math;\"> 10 </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2348;</span><span style=\"font-family: Cambria Math;\"> P </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2352;&#2366;&#2348;&#2352;</span><span style=\"font-family: Cambria Math;\"> 15 </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> Q </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2379;&#2327;&#2366;</span><span style=\"font-family: Cambria Math;\"> ?</span></p>\n",
                    options_en: ["<p>9</p>\n", "<p><span style=\"font-family: Cambria Math;\">4</span></p>\n", 
                                "<p>10</p>\n", "<p>5</p>\n"],
                    options_hi: ["<p>9</p>\n", "<p>4</p>\n",
                                "<p>10</p>\n", "<p>5</p>\n"],
                    solution_en: "<p>4.(b)</p>\r\n<p><span style=\"font-family: Cambria Math;\">P &prop;</span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi>Q</mi></mfrac></math></span><span style=\"font-family: Cambria Math;\"> &rArr; P =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">K</mi><mo>&nbsp;</mo><mo>&times;</mo><mfrac><mn>1</mn><mi mathvariant=\"normal\">Q</mi></mfrac></math></span></p>\r\n<p><span style=\"font-family: Cambria Math;\">10 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">K</mi><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mfrac><mn>1</mn><mn>6</mn></mfrac></math> </span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> &rArr; k = 60 </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">now, </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">15 = 60 <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&times;</mo><mo>&nbsp;</mo><mfrac><mn>1</mn><mi>Q</mi></mfrac></math></span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> &rArr; Q = 4</span></p>\n",
                    solution_hi: "<p>4.(b)</p>\r\n<p><span style=\"font-family: Cambria Math;\">P &prop;</span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi>Q</mi></mfrac></math></span><span style=\"font-family: Cambria Math;\"> &rArr; P =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">K</mi><mo>&nbsp;</mo><mo>&times;</mo><mfrac><mn>1</mn><mi mathvariant=\"normal\">Q</mi></mfrac></math></span></p>\r\n<p><span style=\"font-family: Cambria Math;\">10 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">K</mi><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mfrac><mn>1</mn><mn>6</mn></mfrac></math> </span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> &rArr; k = 60 </span></p>\r\n<p><span style=\"font-weight: 400;\">&#2309;&#2348;,</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">15 = 60 <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&times;</mo><mo>&nbsp;</mo><mfrac><mn>1</mn><mi>Q</mi></mfrac></math></span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> &rArr; Q = 4</span></p>\n",
                    correct: "b",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "5",
                    section: "17",
                    question_en: "<p>5.<span style=\"font-family: Cambria Math;\"> If A : B = 3 : 4 and A : C = 9 : 8, then A : B : C is:</span></p>\n",
                    question_hi: "<p>5.<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> A : B = 3 : 4 </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> A : C = 9 : 8, </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> A : B : C </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2352;&#2366;&#2348;&#2352;</span><span style=\"font-family: Cambria Math;\"> ______ </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2379;&#2327;&#2366;&#2404;</span></p>\n",
                    options_en: ["<p>9 : 12 : 8</p>\n", "<p>3 : 4 : 6</p>\n", 
                                "<p>9 : 8 : 12</p>\n", "<p>9 : 8 : 8</p>\n"],
                    options_hi: ["<p>9 : 12 : 8</p>\n", "<p>3 : 4 : 6</p>\n",
                                "<p>9 : 8 : 12</p>\n", "<p>9 : 8 : 8</p>\n"],
                    solution_en: "<p>5.(a)</p>\r\n<p><span style=\"font-family: Cambria Math;\">A : B = 3 : 4 OR 9 : 12 , A : C = 9 : 8</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Hence A : B : C = 9 : 12 : 8</span></p>\n",
                    solution_hi: "<p>5.(a)</p>\r\n<p><span style=\"font-family: Cambria Math;\">A : B = 3 : 4 </span><span style=\"font-family: Nirmala UI;\">&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> 9 : 12 , A : C = 9 : 8</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2309;&#2340;</span><span style=\"font-family: Cambria Math;\">: A : B : C = 9 : 12 : 8</span></p>\n",
                    correct: "a",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "6",
                    section: "17",
                    question_en: "<p>6.<span style=\"font-family: Cambria Math;\"> The mean proportion of </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><mn>3</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>2</mn><msqrt><mn>2</mn></msqrt><mo>)</mo></mrow><mrow><mn>2</mn><mo>&nbsp;</mo></mrow></msup></math><span style=\"font-family: Cambria Math;\"> and </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><mn>3</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>2</mn><msqrt><mn>2</mn></msqrt><mo>)</mo></mrow><mn>2</mn></msup></math><span style=\"font-family: Cambria Math;\"> is:</span></p>\n",
                    question_hi: "<p>6.<span style=\"font-family: Cambria Math;\"> &nbsp;</span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><mn>3</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>2</mn><msqrt><mn>2</mn></msqrt><mo>)</mo></mrow><mrow><mn>2</mn><mo>&nbsp;</mo></mrow></msup></math><span style=\"font-weight: 400;\">&#2324;&#2352; </span><span style=\"font-family: Cambria Math;\"> </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><mn>3</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>2</mn><msqrt><mn>2</mn></msqrt><mo>)</mo></mrow><mn>2</mn></msup></math><span style=\"font-family: Cambria Math;\">&nbsp;</span><span style=\"font-weight: 400;\">&#2325;&#2366; &#2350;&#2366;&#2343;&#2381;&#2351;&#2366;&#2344;&#2369;&#2346;&#2366;&#2340;&#2368; __________ &#2361;&#2376;&#2404;&nbsp;</span></p>\n",
                    options_en: ["<p>3 + 2<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>2</mn></msqrt></math></p>\n", "<p>5</p>\n", 
                                "<p>2<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>2</mn></msqrt></math></p>\n", "<p>1</p>\n"],
                    options_hi: ["<p>3 + 2<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>2</mn></msqrt></math></p>\n", "<p>5</p>\n",
                                "<p>2<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>2</mn></msqrt></math></p>\n", "<p>1</p>\n"],
                    solution_en: "<p>6.(d)</p>\r\n<p><span style=\"font-family: Cambria Math;\">Mean proportion = </span><span style=\"font-family: Cambria Math;\"> </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mrow><mo>(</mo><mn>3</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>2</mn><msqrt><mn>2</mn></msqrt><mo>&nbsp;</mo><mo>)</mo></mrow><mn>2</mn></msup><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><msup><mrow><mo>(</mo><mn>3</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>2</mn><msqrt><mn>2</mn></msqrt><mo>&nbsp;</mo><mo>)</mo></mrow><mn>2</mn></msup></msqrt><mo>=</mo><mo>&nbsp;</mo><mo>(</mo><mn>3</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>2</mn><msqrt><mn>2</mn></msqrt><mo>)</mo><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mo>(</mo><mn>3</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>2</mn><msqrt><mn>2</mn></msqrt><mo>)</mo><mo>&nbsp;</mo><mo>&rArr;</mo><mo>&nbsp;</mo><mn>9</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>8</mn><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>1</mn></math></p>\n",
                    solution_hi: "<p>6.(d)</p>\r\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">&#2350;&#2366;&#2343;&#2381;&#2351; &#2309;&#2344;&#2369;&#2346;&#2366;&#2340;</span> = </span><span style=\"font-family: Cambria Math;\"> </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mrow><mo>(</mo><mn>3</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>2</mn><msqrt><mn>2</mn></msqrt><mo>&nbsp;</mo><mo>)</mo></mrow><mn>2</mn></msup><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><msup><mrow><mo>(</mo><mn>3</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>2</mn><msqrt><mn>2</mn></msqrt><mo>&nbsp;</mo><mo>)</mo></mrow><mn>2</mn></msup></msqrt><mo>=</mo><mo>&nbsp;</mo><mo>(</mo><mn>3</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>2</mn><msqrt><mn>2</mn></msqrt><mo>)</mo><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mo>(</mo><mn>3</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>2</mn><msqrt><mn>2</mn></msqrt><mo>)</mo><mo>&nbsp;</mo><mo>&rArr;</mo><mo>&nbsp;</mo><mn>9</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>8</mn><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>1</mn></math></p>\n",
                    correct: "d",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "7",
                    section: "17",
                    question_en: "<p>7. <span style=\"font-family: Cambria Math;\">If A : B = 7 : 9 and B : C = 5 : 6, Find the ratio of C and A respectively.</span></p>\n",
                    question_hi: "<p>7. <span style=\"font-family: Nirmala UI;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> A : B = 7 : 9 </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> B : C = 5 : 6 </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> C </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> A </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2352;&#2350;&#2358;&#2307;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2344;&#2369;&#2346;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2381;&#2334;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;&#2332;&#2367;&#2319;&#2404;</span></p>\n",
                    options_en: ["<p>15 : 14</p>\n", "<p>14 : 15</p>\n", 
                                "<p>54 : 35</p>\n", "<p>35 : 54</p>\n"],
                    options_hi: ["<p>15 : 14</p>\n", "<p>14 : 15</p>\n",
                                "<p>54 : 35</p>\n", "<p>35 : 54</p>\n"],
                    solution_en: "<p>7.(c)</p>\r\n<p><span style=\"font-family: Cambria Math;\">A : B = 7 : 9 or 35 : 45 </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">B : C = 5 : 6 or 45 : 54</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Hence A : B : C = 35 : 45 : 54</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Required ratio (C : A) = 54 : 35</span></p>\n",
                    solution_hi: "<p>7.(c)</p>\r\n<p><span style=\"font-family: Cambria Math;\">A : B = 7 : 9 </span><span style=\"font-family: Nirmala UI;\">&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> 35 : 45 </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">B : C = 5 : 6 </span><span style=\"font-family: Nirmala UI;\">&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> 45 : 54</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2309;&#2340;</span><span style=\"font-family: Cambria Math;\">: A : B : C = 35 : 45 : 54</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2310;&#2357;&#2358;&#2381;&#2351;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2344;&#2369;&#2346;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> (</span><span style=\"font-family: Cambria Math;\">C : A) = 54 : 35</span></p>\n",
                    correct: "c",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "8",
                    section: "17",
                    question_en: "<p>8.<span style=\"font-family: Cambria Math;\"> Two numbers are in the ratio of 6 : 7. If the difference between these numbers is 16, then find the numbers.</span></p>\n",
                    question_hi: "<p>8. <span style=\"font-family: Nirmala UI;\">&#2342;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;&#2319;&#2305;</span><span style=\"font-family: Cambria Math;\"> 6 : 7 </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2344;&#2369;&#2346;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2306;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2311;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;&#2323;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2368;&#2330;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2306;&#2340;&#2352;</span><span style=\"font-family: Cambria Math;\"> 16 </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;&#2319;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2381;&#2334;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;&#2332;&#2367;&#2319;&#2404;</span></p>\n",
                    options_en: ["<p>96, 112</p>\n", "<p>80, 96</p>\n", 
                                "<p>120, 136</p>\n", "<p>104, 120</p>\n"],
                    options_hi: ["<p>96, 112</p>\n", "<p>80, 96</p>\n",
                                "<p>120, 136</p>\n", "<p>104, 120</p>\n"],
                    solution_en: "<p>8.(a)</p>\r\n<p><span style=\"font-family: Cambria Math;\">Lets two numbers are 6<span style=\"font-weight: 400;\">x</span></span><span style=\"font-family: Cambria Math;\">&nbsp;and 7<span style=\"font-weight: 400;\">x</span><span style=\"font-weight: 400;\"> </span></span></p>\r\n<p><span style=\"font-family: Cambria Math;\">According to the question,</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">7<span style=\"font-weight: 400;\">x -&nbsp;</span></span><span style=\"font-family: Cambria Math;\"> 6x</span><span style=\"font-family: Cambria Math;\"> = 16 &rArr; x </span><span style=\"font-family: Cambria Math;\">= 16</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Numbers are 6x</span><span style=\"font-family: Cambria Math;\"> = 6 &times;</span><span style=\"font-family: Cambria Math;\"> 16 = 96 and 7x</span><span style=\"font-family: Cambria Math;\"> = 7 &times;</span><span style=\"font-family: Cambria Math;\"> 16 = 112</span></p>\n",
                    solution_hi: "<p>8.(a)</p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;&#2319;&#2305;</span><span style=\"font-family: Cambria Math;\"> 6x</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> 7x</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2306;</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2358;&#2381;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2344;&#2369;&#2360;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\">,</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">7x</span><span style=\"font-family: Cambria Math;\"> - </span><span style=\"font-family: Cambria Math;\">6x</span><span style=\"font-family: Cambria Math;\"> = 16 &rArr; x </span><span style=\"font-family: Cambria Math;\">= 16</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;&#2319;&#2305;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2306;</span><span style=\"font-family: Cambria Math;\"> 6x</span><span style=\"font-family: Cambria Math;\"> = 6 &times;&nbsp;</span><span style=\"font-family: Cambria Math;\"> 16 = 96 </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> 7x</span><span style=\"font-family: Cambria Math;\"> = 7 &times; </span><span style=\"font-family: Cambria Math;\">16 = 112</span></p>\n",
                    correct: "a",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "9",
                    section: "17",
                    question_en: "<p>9. <span style=\"font-family: Cambria Math;\">Find the mean proportional between 0.08 and 0.32.</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">9.</span><span style=\"font-family: Cambria Math;\"> 0.08 </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> 0.32 </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2368;&#2330;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2343;&#2381;&#2351;&#2366;&#2344;&#2369;&#2346;&#2366;&#2340;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2381;&#2334;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;&#2332;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2404;</span></p>\n",
                    options_en: ["<p>16</p>\n", "<p>0.016</p>\n", 
                                "<p>0.16</p>\n", "<p>1.6</p>\n"],
                    options_hi: ["<p>16</p>\n", "<p>0.016</p>\n",
                                "<p>0.16</p>\n", "<p>1.6</p>\n"],
                    solution_en: "<p>9.(c)</p>\r\n<p><span style=\"font-family: Cambria Math;\">mean proportional = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>0</mn><mo>.</mo><mn>08</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>0</mn><mo>.</mo><mn>32</mn></msqrt><mo>&nbsp;</mo></math></span><span style=\"font-family: Cambria Math;\">&nbsp; = 0.16</span></p>\n",
                    solution_hi: "<p>9.(c)</p>\r\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">&#2350;&#2366;&#2343;&#2381;&#2351; &#2310;&#2344;&#2369;&#2346;&#2366;&#2340;&#2367;&#2325; </span>&nbsp;= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>0</mn><mo>.</mo><mn>08</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>0</mn><mo>.</mo><mn>32</mn></msqrt><mo>&nbsp;</mo></math></span><span style=\"font-family: Cambria Math;\">&nbsp; = 0.16</span></p>\n",
                    correct: "c",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "10",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">10.</span><span style=\"font-family: Cambria Math;\"> &#8377;3,600 is divided such that half of the first part, one-third of the second part and one-fifth of the third part are the same. The value of the third part is:</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">10. </span><span style=\"font-family: Cambria Math;\"> &#8377;3,600 </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2311;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2325;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2366;&#2306;&#2335;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2366;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2361;&#2354;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2349;&#2366;&#2327;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;</span><span style=\"font-family: Nirmala UI;\">&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2343;&#2366;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2370;&#2360;&#2352;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2349;&#2366;&#2327;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\">-</span><span style=\"font-family: Nirmala UI;\">&#2340;&#2367;&#2361;&#2366;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2368;&#2360;&#2352;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2349;&#2366;&#2327;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2366;&#2306;&#2330;&#2357;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2349;&#2366;&#2327;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2368;&#2360;&#2352;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2349;&#2366;&#2327;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2340;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> ?</span></p>\n",
                    options_en: ["<p>1080</p>\n", "<p>1000</p>\n", 
                                "<p>2400</p>\n", "<p>1800</p>\n"],
                    options_hi: ["<p>1080</p>\n", "<p>1000</p>\n",
                                "<p>2400</p>\n", "<p>1800</p>\n"],
                    solution_en: "<p>10.(d)</p>\r\n<p><span style=\"font-family: Cambria Math;\">Let 1st, 2nd and 3rd part is a, b, c respectively</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">According to the question,</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"normal\">a</mi><mn>2</mn></mfrac><mo>&nbsp;</mo><mo>=</mo><mfrac><mrow><mo>&nbsp;</mo><mi mathvariant=\"normal\">b</mi></mrow><mn>3</mn></mfrac><mo>=</mo><mo>&nbsp;</mo><mfrac><mi mathvariant=\"normal\">c</mi><mn>5</mn></mfrac></math></span></p>\r\n<p><span style=\"font-family: Cambria Math;\">a : b : c = 2 : 3 : 5 = 10 units</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">10 units = 3600 Rs</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">5 units (c) = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3600</mn><mn>10</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\">&times; 5 = Rs. 1800 </span></p>\n",
                    solution_hi: "<p>10.(d)</p>\r\n<p><span style=\"font-weight: 400;\">&#2350;&#2366;&#2344;&#2366; &#2346;&#2361;&#2354;&#2366;, &#2342;&#2370;&#2360;&#2352;&#2366; &#2324;&#2352; &#2340;&#2368;&#2360;&#2352;&#2366; &#2349;&#2366;&#2327; &#2325;&#2381;&#2352;&#2350;&#2358;&#2307; a, b, c &#2361;&#2376;</span></p>\r\n<p><span style=\"font-weight: 400;\">&#2346;&#2381;&#2352;&#2358;&#2381;&#2344; &#2325;&#2375; &#2309;&#2344;&#2369;&#2360;&#2366;&#2352;,</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"normal\">a</mi><mn>2</mn></mfrac><mo>&nbsp;</mo><mo>=</mo><mfrac><mrow><mo>&nbsp;</mo><mi mathvariant=\"normal\">b</mi></mrow><mn>3</mn></mfrac><mo>=</mo><mo>&nbsp;</mo><mfrac><mi mathvariant=\"normal\">c</mi><mn>5</mn></mfrac></math></span></p>\r\n<p><span style=\"font-family: Cambria Math;\">a : b : c = 2 : 3 : 5 = 10 <span style=\"font-weight: 400;\">&#2311;&#2325;&#2366;&#2312; </span></span></p>\r\n<p><span style=\"font-family: Cambria Math;\">10 <span style=\"font-weight: 400;\">&#2311;&#2325;&#2366;&#2312; </span>= <span style=\"font-weight: 400;\">&#8377; </span>3600&nbsp;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">5 <span style=\"font-weight: 400;\">&#2311;&#2325;&#2366;&#2312; </span>(c) = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3600</mn><mn>10</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\">&times; 5 = <span style=\"font-weight: 400;\">&#8377; </span>1800 </span></p>\n",
                    correct: "d",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "11",
                    section: "17",
                    question_en: "<p>11.<span style=\"font-family: Cambria Math;\"> Arrange the ratios in ascending order </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>5</mn><mo>&nbsp;</mo><mo>:</mo><mo>&nbsp;</mo><mn>3</mn><mfrac><mn>1</mn><mn>3</mn></mfrac><mo>,</mo><mo>&nbsp;</mo><mn>6</mn><mfrac><mn>1</mn><mn>4</mn></mfrac><mo>&nbsp;</mo><mo>:</mo><mo>&nbsp;</mo><mn>3</mn><mfrac><mn>1</mn><mn>8</mn></mfrac></math><span style=\"font-family: Cambria Math;\"> and 3.5 : 4.5.</span></p>\n",
                    question_hi: "<p>11.<span style=\"font-family: Cambria Math;\"> </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>5</mn><mo>&nbsp;</mo><mo>:</mo><mo>&nbsp;</mo><mn>3</mn><mfrac><mn>1</mn><mn>3</mn></mfrac><mo>,</mo><mo>&nbsp;</mo><mn>6</mn><mfrac><mn>1</mn><mn>4</mn></mfrac><mo>&nbsp;</mo><mo>:</mo><mo>&nbsp;</mo><mn>3</mn><mfrac><mn>1</mn><mn>8</mn></mfrac></math><span style=\"font-family: Cambria Math;\"> <span style=\"font-weight: 400;\">&#2324;&#2352; </span>3.5 : 4.5&nbsp;</span><span style=\"font-weight: 400;\">&#2309;&#2344;&#2369;&#2346;&#2366;&#2340;&#2379;&#2306; &#2325;&#2379; &#2348;&#2338;&#2364;&#2340;&#2375; &#2325;&#2381;&#2352;&#2350; &#2350;&#2375;&#2306; &#2357;&#2381;&#2351;&#2357;&#2360;&#2381;&#2341;&#2367;&#2340; &#2325;&#2368;&#2332;&#2367;&#2319;&#2404;</span></p>\n",
                    options_en: ["<p>3.5 : 4.5 &lt; 5 : <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>3</mn><mfrac><mn>1</mn><mn>3</mn></mfrac></math><span style=\"font-family: Cambria Math;\">&lt; </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>6</mn><mfrac><mn>1</mn><mn>4</mn></mfrac></math><span style=\"font-family: Cambria Math;\">: <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>3</mn><mfrac><mn>1</mn><mn>8</mn></mfrac></math></span></p>\n", "<p><span style=\"font-family: Cambria Math;\">&nbsp;</span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>6</mn><mfrac><mn>1</mn><mn>4</mn></mfrac></math><span style=\"font-family: Cambria Math;\">: <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>3</mn><mfrac><mn>1</mn><mn>8</mn></mfrac></math></span><span style=\"font-weight: 400;\">&lt; 5 : </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>3</mn><mfrac><mn>1</mn><mn>3</mn></mfrac></math><span style=\"font-weight: 400;\">&lt; 3.5 : 4.5</span></p>\n", 
                                "<p>5 : <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>3</mn><mfrac><mn>1</mn><mn>3</mn></mfrac></math><span style=\"font-weight: 400;\"> &lt; 3.5 : 4.5&lt;</span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>6</mn><mfrac><mn>1</mn><mn>4</mn></mfrac></math><span style=\"font-family: Cambria Math;\">: <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>3</mn><mfrac><mn>1</mn><mn>8</mn></mfrac></math></span></p>\n", "<p>5 : <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>3</mn><mfrac><mn>1</mn><mn>3</mn></mfrac></math><span style=\"font-weight: 400;\">&lt;</span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>6</mn><mfrac><mn>1</mn><mn>4</mn></mfrac></math><span style=\"font-family: Cambria Math;\">: <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>3</mn><mfrac><mn>1</mn><mn>8</mn></mfrac></math><span style=\"font-weight: 400;\">&lt; 3.5 : 4.5</span></span></p>\n"],
                    options_hi: ["<p>3.5 : 4.5 &lt; 5 : <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>3</mn><mfrac><mn>1</mn><mn>3</mn></mfrac></math><span style=\"font-family: Cambria Math;\">&lt; </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>6</mn><mfrac><mn>1</mn><mn>4</mn></mfrac></math><span style=\"font-family: Cambria Math;\">: <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>3</mn><mfrac><mn>1</mn><mn>8</mn></mfrac></math></span></p>\n", "<p><span style=\"font-family: Cambria Math;\">&nbsp;</span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>6</mn><mfrac><mn>1</mn><mn>4</mn></mfrac></math><span style=\"font-family: Cambria Math;\">: <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>3</mn><mfrac><mn>1</mn><mn>8</mn></mfrac></math></span><span style=\"font-weight: 400;\">&lt; 5 : </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>3</mn><mfrac><mn>1</mn><mn>3</mn></mfrac></math><span style=\"font-weight: 400;\">&lt; 3.5 : 4.5</span></p>\n",
                                "<p>5 : <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>3</mn><mfrac><mn>1</mn><mn>3</mn></mfrac></math><span style=\"font-weight: 400;\"> &lt; 3.5 : 4.5&lt;</span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>6</mn><mfrac><mn>1</mn><mn>4</mn></mfrac></math><span style=\"font-family: Cambria Math;\">: <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>3</mn><mfrac><mn>1</mn><mn>8</mn></mfrac></math></span></p>\n", "<p>5 : <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>3</mn><mfrac><mn>1</mn><mn>3</mn></mfrac></math><span style=\"font-weight: 400;\">&lt;</span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>6</mn><mfrac><mn>1</mn><mn>4</mn></mfrac></math><span style=\"font-family: Cambria Math;\">: <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>3</mn><mfrac><mn>1</mn><mn>8</mn></mfrac></math><span style=\"font-weight: 400;\">&lt; 3.5 : 4.5</span></span></p>\n"],
                    solution_en: "<p>11.(a)</p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Given:</strong>-</span><span style=\"font-family: Cambria Math;\"> </span>5 : <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>3</mn><mfrac><mn>1</mn><mn>3</mn></mfrac></math>, <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>6</mn><mfrac><mn>1</mn><mn>4</mn></mfrac></math><span style=\"font-family: Cambria Math;\">: <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>3</mn><mfrac><mn>1</mn><mn>8</mn></mfrac></math><span style=\"font-weight: 400;\">and </span><span style=\"font-weight: 400;\">3.5 : 4.5</span></span></p>\r\n<p><span style=\"font-family: Cambria Math;\">5 :</span><span style=\"font-family: Cambria Math;\"> </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>3</mn><mfrac><mn>1</mn><mn>3</mn></mfrac></math><span style=\"font-family: Cambria Math;\">= 5 : <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>10</mn><mn>3</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\">&rArr; 3 : 2 = 1.5</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>6</mn><mfrac><mn>1</mn><mn>4</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\">: </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>3</mn><mfrac><mn>1</mn><mn>8</mn></mfrac></math><span style=\"font-family: Cambria Math;\">= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>25</mn><mn>4</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> : <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>25</mn><mn>8</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> &rArr; 2 : 1 = 2</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">3.5 : 4.5 = 35 : 45 &rArr; 7 : 9 = 0.78</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Required ascending order = 0.78 &lt; 1.5 &lt; 2 OR&nbsp;</span>3.5 : 4.5 &lt; 5 : <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>3</mn><mfrac><mn>1</mn><mn>3</mn></mfrac></math><span style=\"font-family: Cambria Math;\">&lt; </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>6</mn><mfrac><mn>1</mn><mn>4</mn></mfrac></math><span style=\"font-family: Cambria Math;\">: <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>3</mn><mfrac><mn>1</mn><mn>8</mn></mfrac></math></span></p>\n",
                    solution_hi: "<p>11.(a)</p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>&#2342;&#2367;&#2351;&#2366; &#2327;&#2351;&#2366;:</strong>-</span><span style=\"font-family: Cambria Math;\"> </span>5 : <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>3</mn><mfrac><mn>1</mn><mn>3</mn></mfrac></math>, <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>6</mn><mfrac><mn>1</mn><mn>4</mn></mfrac></math><span style=\"font-family: Cambria Math;\">: <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>3</mn><mfrac><mn>1</mn><mn>8</mn></mfrac></math><span style=\"font-weight: 400;\">&#2324;&#2352; </span><span style=\"font-weight: 400;\">3.5 : 4.5</span></span></p>\r\n<p><span style=\"font-family: Cambria Math;\">5 :</span><span style=\"font-family: Cambria Math;\"> </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>3</mn><mfrac><mn>1</mn><mn>3</mn></mfrac></math><span style=\"font-family: Cambria Math;\">= 5 : <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>10</mn><mn>3</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\">&rArr; 3 : 2 = 1.5</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>6</mn><mfrac><mn>1</mn><mn>4</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\">: </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>3</mn><mfrac><mn>1</mn><mn>8</mn></mfrac></math><span style=\"font-family: Cambria Math;\">= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>25</mn><mn>4</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> : <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>25</mn><mn>8</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> &rArr; 2 : 1 = 2</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">3.5 : 4.5 = 35 : 45 &rArr; 7 : 9 = 0.78</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">&#2310;&#2357;&#2358;&#2381;&#2351;&#2325; &#2310;&#2352;&#2379;&#2361;&#2368; &#2325;&#2381;&#2352;&#2350; </span>= 0.78 &lt; 1.5 &lt; 2 OR </span>3.5 : 4.5 &lt; 5 : <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>3</mn><mfrac><mn>1</mn><mn>3</mn></mfrac></math><span style=\"font-family: Cambria Math;\">&lt; </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>6</mn><mfrac><mn>1</mn><mn>4</mn></mfrac></math><span style=\"font-family: Cambria Math;\">: <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>3</mn><mfrac><mn>1</mn><mn>8</mn></mfrac></math></span></p>\n",
                    correct: "a",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "12",
                    section: "17",
                    question_en: "<p>12. <span style=\"font-family: Cambria Math;\">If 15x = 24y = 32z, then x : y : z is equal to:</span></p>\n",
                    question_hi: "<p>12. <span style=\"font-family: Nirmala UI;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> 15x = 24y = 32z, </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> x : y : z </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2352;&#2366;&#2348;&#2352;</span><span style=\"font-family: Cambria Math;\"> ______ </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2379;&#2327;&#2366;&#2404;</span></p>\n",
                    options_en: ["<p>32 : 15 : 20</p>\n", "<p>15 : 24 : 32</p>\n", 
                                "<p>32 : 20 : 15</p>\n", "<p>24 : 15 : 4</p>\n"],
                    options_hi: ["<p>32 : 15 : 20</p>\n", "<p>15 : 24 : 32</p>\n",
                                "<p>32 : 20 : 15</p>\n", "<p>24 : 15 : 4</p>\n"],
                    solution_en: "<p>12.(c)</p>\r\n<p><span style=\"font-family: Cambria Math;\">15x = 24y = 32z = k</span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">x</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mfrac><mi mathvariant=\"normal\">k</mi><mn>15</mn></mfrac><mo>&nbsp;</mo><mo>,</mo><mo>&nbsp;</mo><mi mathvariant=\"normal\">y</mi><mo>&nbsp;</mo><mo>=</mo><mfrac><mrow><mo>&nbsp;</mo><mi mathvariant=\"normal\">k</mi></mrow><mn>24</mn></mfrac><mo>&nbsp;</mo><mo>,</mo><mo>&nbsp;</mo><mi mathvariant=\"normal\">z</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mfrac><mi mathvariant=\"normal\">k</mi><mn>32</mn></mfrac></math><span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">x : y : z = </span><span style=\"font-family: Cambria Math;\"> </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"normal\">k</mi><mn>15</mn></mfrac><mo>&nbsp;</mo><mo>:</mo><mfrac><mrow><mo>&nbsp;</mo><mi mathvariant=\"normal\">k</mi></mrow><mn>24</mn></mfrac><mo>&nbsp;</mo><mo>:</mo><mo>&nbsp;</mo><mfrac><mi mathvariant=\"normal\">k</mi><mn>32</mn></mfrac></math><span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">x : y : z = 24 &times; 32 : 15 &times; 32 : 15 &times; 24</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">x : y : z = 32 : 20 : 15</span></p>\n",
                    solution_hi: "<p>12.(c)</p>\r\n<p><span style=\"font-family: Cambria Math;\">15x = 24y = 32z = k</span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">x</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mfrac><mi mathvariant=\"normal\">k</mi><mn>15</mn></mfrac><mo>&nbsp;</mo><mo>,</mo><mo>&nbsp;</mo><mi mathvariant=\"normal\">y</mi><mo>&nbsp;</mo><mo>=</mo><mfrac><mrow><mo>&nbsp;</mo><mi mathvariant=\"normal\">k</mi></mrow><mn>24</mn></mfrac><mo>&nbsp;</mo><mo>,</mo><mo>&nbsp;</mo><mi mathvariant=\"normal\">z</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mfrac><mi mathvariant=\"normal\">k</mi><mn>32</mn></mfrac></math><span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">x : y : z = </span><span style=\"font-family: Cambria Math;\"> </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"normal\">k</mi><mn>15</mn></mfrac><mo>&nbsp;</mo><mo>:</mo><mfrac><mrow><mo>&nbsp;</mo><mi mathvariant=\"normal\">k</mi></mrow><mn>24</mn></mfrac><mo>&nbsp;</mo><mo>:</mo><mo>&nbsp;</mo><mfrac><mi mathvariant=\"normal\">k</mi><mn>32</mn></mfrac></math><span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">x : y : z = 24 &times; 32 : 15 &times; 32 : 15 &times; 24</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">x : y : z = 32 : 20 : 15</span></p>\n",
                    correct: "c",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "13",
                    section: "17",
                    question_en: "<p>13. <span style=\"font-family: Cambria Math;\">The incomes of P, Q and R are in the ratio of 9 : 15 : 10 and their savings are in the ratio is 10: 15 :12. If R spends 60% of his income, what is the ratio of the expenditu</span><span style=\"font-family: Cambria Math;\">res of P, Q and R ?</span></p>\n",
                    question_hi: "<p>13.<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">P, Q </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> R </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2351;</span><span style=\"font-family: Cambria Math;\"> 9 : 15 : 10 </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2344;&#2369;&#2346;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2344;&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2330;&#2340;</span><span style=\"font-family: Cambria Math;\"> 10 : 15 : 12 </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2344;&#2369;&#2346;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> R </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2346;&#2344;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> 60% </span><span style=\"font-family: Nirmala UI;\">&#2326;&#2352;&#2381;&#2330;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> P, Q </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> R </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2381;&#2351;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2344;&#2369;&#2346;&#2366;</span><span style=\"font-family: Nirmala UI;\">&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> ?</span></p>\n",
                    options_en: ["<p>18 : 17 : 30</p>\n", "<p>17 : 30 : 18</p>\n", 
                                "<p>17 : 18 : 30</p>\n", "<p>18 : 30 : 17</p>\n"],
                    options_hi: ["<p>18 : 17 : 30</p>\n", "<p>17 : 30 : 18</p>\n",
                                "<p>17 : 18 : 30</p>\n", "<p>18 : 30 : 17</p>\n"],
                    solution_en: "<p>13.(b)</p>\r\n<p><span style=\"font-family: Cambria Math;\">Let income of P, Q, and R is 9x</span><span style=\"font-family: Cambria Math;\">, 15x</span><span style=\"font-family: Cambria Math;\">, 10x</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">and savings of P, Q, and R is 10y</span><span style=\"font-family: Cambria Math;\">, 15y</span><span style=\"font-family: Cambria Math;\">, 12y</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">According to the question,</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">10x -</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> 12y</span><span style=\"font-family: Cambria Math;\"> = 10x &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>60</mn><mn>100</mn></mfrac></math></span></p>\r\n<p><span style=\"font-family: Cambria Math;\">10x</span><span style=\"font-family: Cambria Math;\"> - </span><span style=\"font-family: Cambria Math;\">12y</span><span style=\"font-family: Cambria Math;\"> = 6x</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>x</mi><mi>y</mi></mfrac><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mfrac><mn>3</mn><mn>1</mn></mfrac></math> </span><span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Ratio of expenditure of P, Q, and R = (9x -</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> 10y</span><span style=\"font-family: Cambria Math;\">) : (15x -</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> 15y</span><span style=\"font-family: Cambria Math;\">) : (10x -</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> 12y</span><span style=\"font-family: Cambria Math;\">)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> = (27 - </span><span style=\"font-family: Cambria Math;\">10) : (45 - </span><span style=\"font-family: Cambria Math;\">15) : (30 - </span><span style=\"font-family: Cambria Math;\">12)</span></p>\r\n<p><span style=\"font-family: \'Cambria Math\';\">= 17 : 30 : 18</span></p>\n",
                    solution_hi: "<p>13.(b)</p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> P, Q </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> R </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2351;</span><span style=\"font-family: Cambria Math;\"> 9x</span><span style=\"font-family: Cambria Math;\">, 15x</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> 10x</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">P, Q, </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> R </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2330;&#2340;</span><span style=\"font-family: Cambria Math;\"> 10y</span><span style=\"font-family: Cambria Math;\">, 15y</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> 12y</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2358;&#2381;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2344;&#2369;&#2360;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\">,</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">10x -</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> 12y</span><span style=\"font-family: Cambria Math;\"> = 10x &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>60</mn><mn>100</mn></mfrac></math></span></p>\r\n<p><span style=\"font-family: Cambria Math;\">10x -</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> 12y</span><span style=\"font-family: Cambria Math;\"> = 6 &times; x</span><span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>x</mi><mi>y</mi></mfrac><mo>&nbsp;</mo><mo>=</mo><mfrac><mn>3</mn><mn>1</mn></mfrac></math> </span><span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">P, Q, </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> R </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2381;&#2351;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2344;&#2369;&#2346;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> = (9x -</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> 10y</span><span style=\"font-family: Cambria Math;\">) : (15x</span><span style=\"font-family: Cambria Math;\"> - </span><span style=\"font-family: Cambria Math;\">15y</span><span style=\"font-family: Cambria Math;\">) : (10x -&nbsp;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> 12y</span><span style=\"font-family: Cambria Math;\">)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">= (27 - </span><span style=\"font-family: Cambria Math;\">10) : (45 - </span><span style=\"font-family: Cambria Math;\">15) : (30 - </span><span style=\"font-family: Cambria Math;\">12)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> = 17 : 30 : 18</span></p>\n",
                    correct: "b",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "14",
                    section: "17",
                    question_en: "<p>14.<span style=\"font-family: Cambria Math;\"> Profit of &#8377;29,120 is divid</span><span style=\"font-family: Cambria Math;\">ed among A, B and C so that A\'s share is <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>2</mn><mfrac><mn>1</mn><mn>2</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> times B\'s and B\'s share is <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>1</mn><mfrac><mn>1</mn><mn>5</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> times C. Find B\'s share.</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">14. </span><span style=\"font-family: Cambria Math;\">&#8377;</span><span style=\"font-family: Cambria Math;\">29,120 </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2366;&#2349;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> A, B </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> C </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2368;&#2330;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2311;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2325;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2366;&#2306;&#2335;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2366;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;</span><span style=\"font-family: Cambria Math;\"> A </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2367;&#2360;&#2381;&#2360;&#2366;</span><span style=\"font-family: Cambria Math;\"> B </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2367;&#2360;&#2381;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>2</mn><mfrac><mn>1</mn><mn>2</mn></mfrac></math><span style=\"font-family: Nirmala UI;\">&#2327;&#2369;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> B </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2367;&#2360;&#2381;&#2360;&#2366;</span><span style=\"font-family: Cambria Math;\"> C </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2367;&#2360;&#2381;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>1</mn><mfrac><mn>1</mn><mn>5</mn></mfrac></math><span style=\"font-family: Nirmala UI;\">&#2327;&#2369;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2379;&#2404;</span><span style=\"font-family: Cambria Math;\"> B </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2367;&#2360;&#2381;&#2360;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2381;&#2334;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;&#2332;&#2367;&#2319;&#2404;</span></p>\n",
                    options_en: ["<p>&#8377;6,940</p>\n", "<p>&#8377;7,820</p>\n", 
                                "<p>&#8377;6,720</p>\n", "<p>&#8377;7,140</p>\n"],
                    options_hi: ["<p>&#8377;6,940</p>\n", "<p>&#8377;7,820</p>\n",
                                "<p>&#8377;6,720</p>\n", "<p>&#8377;7,140</p>\n"],
                    solution_en: "<p>14.(c)</p>\r\n<p><span style=\"font-family: Cambria Math;\">According to the question,</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Ratio -&nbsp; &nbsp; A&nbsp; &nbsp; :&nbsp; &nbsp; &nbsp;B&nbsp; &nbsp; :&nbsp; &nbsp;C</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Profit - 15x</span><span style=\"font-family: Cambria Math;\">&nbsp; :&nbsp; &nbsp; 6x</span><span style=\"font-family: Cambria Math;\">&nbsp; &nbsp;:&nbsp; &nbsp;5x</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">B&rsquo;s share = 29120 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>6</mn><mi mathvariant=\"normal\">x</mi></mrow><mrow><mn>15</mn><mi mathvariant=\"normal\">x</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>6</mn><mi mathvariant=\"normal\">x</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>5</mn><mi mathvariant=\"normal\">x</mi></mrow></mfrac></math></span><span style=\"font-family: Cambria Math;\"> = &#8377; 6720</span></p>\n",
                    solution_hi: "<p>14.(c)</p>\r\n<p><span style=\"font-weight: 400;\">&#2346;&#2381;&#2352;&#2358;&#2381;&#2344; &#2325;&#2375; &#2309;&#2344;&#2369;&#2360;&#2366;&#2352;,</span></p>\r\n<p><span style=\"font-weight: 400;\">&#2309;&#2344;&#2369;&#2346;&#2366;&#2340; -</span><span style=\"font-family: \'Cambria Math\';\">&nbsp; A&nbsp; &nbsp; :&nbsp; &nbsp; &nbsp; &nbsp;B&nbsp; &nbsp; :&nbsp; &nbsp; &nbsp;C</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">&#2354;&#2366;&#2349; </span>-&nbsp; &nbsp; &nbsp;15x</span><span style=\"font-family: Cambria Math;\">&nbsp; :&nbsp; &nbsp; &nbsp; 6x</span><span style=\"font-family: Cambria Math;\">&nbsp; &nbsp;:&nbsp; &nbsp; &nbsp;5x</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">B &#2325;&#2366; &#2361;&#2367;&#2360;&#2381;&#2360;&#2366;</span> = 29120 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>6</mn><mi mathvariant=\"normal\">x</mi></mrow><mrow><mn>15</mn><mi mathvariant=\"normal\">x</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>6</mn><mi mathvariant=\"normal\">x</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>5</mn><mi mathvariant=\"normal\">x</mi></mrow></mfrac></math></span><span style=\"font-family: Cambria Math;\"> = &#8377; 6720</span></p>\n",
                    correct: "c",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "15",
                    section: "misc",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">15.</span><span style=\"font-family: Cambria Math;\"> <span style=\"font-weight: 400;\">The mean proportion of </span><span style=\"font-weight: 400;\">a</span><span style=\"font-weight: 400;\"> </span>&nbsp;<span style=\"font-weight: 400;\">and </span></span><span style=\"font-family: Cambria Math;\"><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"normal\">a</mi><msup><mi mathvariant=\"normal\">b</mi><mn>2</mn></msup></mfrac></math></span><span style=\"font-family: Nirmala UI;\"> is _________ .</span></span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">15.</span><span style=\"font-family: Cambria Math;\"> a </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> &nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"normal\">a</mi><msup><mi mathvariant=\"normal\">b</mi><mn>2</mn></msup></mfrac></math></span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2343;&#2381;&#2351;&#2366;&#2344;&#2369;&#2346;&#2366;&#2340;&#2368;</span><span style=\"font-family: Cambria Math;\"> ___________ </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span></p>\n",
                    options_en: ["<p>ab</p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"normal\">b</mi><mi mathvariant=\"normal\">a</mi></mfrac></math><span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\n", 
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"normal\">a</mi><mi mathvariant=\"normal\">b</mi></mfrac></math><span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"normal\">a</mi><mrow><mn>2</mn><mi mathvariant=\"normal\">b</mi></mrow></mfrac></math><span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\n"],
                    options_hi: ["<p>ab</p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"normal\">b</mi><mi mathvariant=\"normal\">a</mi></mfrac></math><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"normal\">a</mi><mi mathvariant=\"normal\">b</mi></mfrac></math><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"normal\">a</mi><mrow><mn>2</mn><mi mathvariant=\"normal\">b</mi></mrow></mfrac></math><span style=\"font-family: Cambria Math;\"> </span></p>\n"],
                    solution_en: "<p>15.(c)</p>\r\n<p><span style=\"font-weight: 400;\">According to the question,</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\"><span style=\"font-weight: 400;\">Mean proportion of </span>a <span style=\"font-weight: 400;\">and </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"normal\">a</mi><msup><mi mathvariant=\"normal\">b</mi><mn>2</mn></msup></mfrac></math></span><span style=\"font-family: Cambria Math;\">&nbsp;</span><span style=\"font-family: Cambria Math;\"><span style=\"font-family: Nirmala UI;\">= </span></span><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mi mathvariant=\"normal\">a</mi><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mfrac><mi mathvariant=\"normal\">a</mi><msup><mi mathvariant=\"normal\">b</mi><mn>2</mn></msup></mfrac></msqrt><mo>&nbsp;</mo><mo>=</mo><mfrac><mi mathvariant=\"normal\">a</mi><mi mathvariant=\"normal\">b</mi></mfrac></math></span></p>\n",
                    solution_hi: "<p>15.(c)</p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2358;&#2381;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2344;&#2369;&#2360;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\">,</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">a &#2324;&#2352;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"normal\">a</mi><msup><mi mathvariant=\"normal\">b</mi><mn>2</mn></msup></mfrac></math></span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2343;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2344;&#2369;&#2346;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">=&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mi mathvariant=\"normal\">a</mi><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mfrac><mi mathvariant=\"normal\">a</mi><msup><mi mathvariant=\"normal\">b</mi><mn>2</mn></msup></mfrac></msqrt><mo>&nbsp;</mo><mo>=</mo><mfrac><mi mathvariant=\"normal\">a</mi><mi mathvariant=\"normal\">b</mi></mfrac></math></span></p>\n",
                    correct: "c",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>