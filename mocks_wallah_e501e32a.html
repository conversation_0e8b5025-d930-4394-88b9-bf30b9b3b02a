<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">60:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 100</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">100</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 60 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["7"] = {
                name: "English",
                start: 0,
                end: 23
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="7">English</option>`;
            sections["6"] = {
                name: "Reasoning",
                start: 24,
                end: 48
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="6">Reasoning</option>`;
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 49,
                end: 73
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["18"] = {
                name: "General Awareness",
                start: 74,
                end: 98
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="18">General Awareness</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 99,
                end: 99
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "7",
                    question_en: "1. Identify the error in the given sentence and select the correct sentence from the given options.<br />This is the story of eight round planets, the few rocks in between, and some comets.",
                    question_hi: "1.  Identify the error in the given sentence and select the correct sentence from the given options.<br />This is the story of eight round planets, the few rocks in between, and some comets.",
                    options_en: [" This is story of eight round planets, few rocks in between, and some comets.", " This is the story of eight round planets, few rocks in between, and some comets.", 
                                " This is the story of eight round planets, a few rocks in between, and some comets.", " This is a story of eight round planets, few rocks in between, and some comets."],
                    options_hi: [" This is story of eight round planets, few rocks in between, and some comets.", " This is the story of eight round planets, few rocks in between, and some comets.",
                                " This is the story of eight round planets, a few rocks in between, and some comets.", " This is a story of eight round planets, few rocks in between, and some comets."],
                    solution_en: "1.(c) This is the story of eight round planets, a few rocks in between, and some comets.<br />‘The few’ must be replaced with ‘a few’. ‘The few’ is used for something specific or particular. But the rocks are not specific. Hence, ‘a few rocks’ is the most appropriate answer.",
                    solution_hi: "1.(c) This is the story of eight round planets, a few rocks in between, and some comets.<br />‘The few’ के स्थान पर ‘a few’ का प्रयोग होगा। ‘The few’ का प्रयोग किसी Specific या Particular चीज़ के लिए किया जाता है। लेकिन चट्टानें Specific नहीं हैं। अतः, ‘a few rocks’ सबसे उपयुक्त उत्तर है।",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "7",
                    question_en: "2. The following sentence has been split into four segments. Identify the segment that contains a grammatical error.<br />It was noticed / that he truthful / answered the questions / raised by the Headmistress.",
                    question_hi: "2. The following sentence has been split into four segments. Identify the segment that contains a grammatical error.<br />It was noticed / that he truthful / answered the questions / raised by the Headmistress.",
                    options_en: [" It was noticed", " that he truthful", 
                                " raised by the Headmistress.", " answered the questions"],
                    options_hi: [" It was noticed", " that he truthful",
                                " raised by the Headmistress.", " answered the questions"],
                    solution_en: "2.(b) that he truthful<br />The given sentence needs an adverb(truthfully) to modify the verb ‘answered’, not the adjective ‘truthful’. Hence, ‘that he truthfully’ is the most appropriate answer.",
                    solution_hi: "2.(b) that he truthful<br />दिए गए Sentence में Verb ‘answered’ को Modify करने के लिए एक Adverb(truthfully) की आवश्यकता है, न कि Adjective ‘truthful’ की। अतः, ‘that he truthfully’ सबसे उपयुक्त उत्तर है।",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "7",
                    question_en: "3. The following sentence has been split into four segments. Identify the segment that contains a grammatical error.<br />Everyone have/ to join health orientation/ classes before/ going for trekking.",
                    question_hi: "3. The following sentence has been split into four segments. Identify the segment that contains a grammatical error.<br />Everyone have/ to join health orientation/ classes before/ going for trekking.",
                    options_en: [" to join health orientation", " going for trekking", 
                                " Everyone have", " classes before"],
                    options_hi: [" to join health orientation", " going for trekking",
                                " Everyone have", " classes before"],
                    solution_en: "3.(c)  Everyone have<br />‘Everyone/each/ every/ nothing/ everything/anything/either/neither’ represents a singular noun. Therefore, singular verbs will be used with them. Hence, ‘Everyone has’ is the most appropriate answer.",
                    solution_hi: "3.(c)  Everyone have<br />‘Everyone/each/every/nothing/everything/anything/either/neither’ एक Singular noun को Represent करते हैं। इसलिए, इनके साथ Singular verb का प्रयोग किया जाएगा। अतः, ‘Everyone has’ सबसे उपयुक्त उत्तर है।",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "7",
                    question_en: "4. The following sentence has been split into four segments. Identify the segment that contains a grammatical error.<br />Our plane / arrived at / the Mumbai airport / at the right time.",
                    question_hi: "4. The following sentence has been split into four segments. Identify the segment that contains a grammatical error.<br />Our plane / arrived at / the Mumbai airport / at the right time.",
                    options_en: [" arrived at", " at the right time", 
                                " Our plane", " the Mumbai airport"],
                    options_hi: [" arrived at", " at the right time",
                                " Our plane", " the Mumbai airport"],
                    solution_en: "4.(d) the Mumbai airport<br />Article ‘the\' will be removed as we generally do not use any article before proper nouns. Hence, \'Mumbai airport\' is the most appropriate answer.",
                    solution_hi: "4.(d) the Mumbai airport<br />Article ‘the\' को हटा दिया जाएगा क्योंकि हम आमतौर पर Proper noun से पहले किसी भी Article का प्रयोग नहीं करते हैं। अतः, \'Mumbai airport\' सबसे उपयुक्त उत्तर है।",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "7",
                    question_en: "<p>5.<strong> Cloze Test:</strong><br>Our country, India, is one of the 12 mega bio-diversity countries of the world. With about 47,000 plant species, India (5)_____ tenth place in the world and fourth in Asia in plant diversity. There are (6)______ 15,000 flowering plants in India, which account for 6 per cent of the world&rsquo;s (7)_____ number of flowering plants. The country has many non-flowering plants, (8)_____ as ferns, algae and fungi. India also has approximately 90,000 (9)____ of animals, as well as a rich variety of fish in its fresh and marine waters.<br>Select the most appropriate option to fill in blank number 5.</p>",
                    question_hi: "<p>5. <strong>Cloze Test:</strong><br>Our country, India, is one of the 12 mega bio-diversity countries of the world. With about 47,000 plant species, India (5)_____ tenth place in the world and fourth in Asia in plant diversity. There are (6)______ 15,000 flowering plants in India, which account for 6 per cent of the world&rsquo;s (7)_____ number of flowering plants. The country has many non-flowering plants, (8)_____ as ferns, algae and fungi. India also has approximately 90,000 (9)____ of animals, as well as a rich variety of fish in its fresh and marine waters.<br>Select the most appropriate option to fill in blank number 5.</p>",
                    options_en: ["<p>occupy</p>", "<p>had</p>", 
                                "<p>have</p>", "<p>occupies</p>"],
                    options_hi: ["<p>occupy</p>", "<p>had</p>",
                                "<p>have</p>", "<p>occupies</p>"],
                    solution_en: "<p>5.(d) occupies<br>&lsquo;Occupies&rsquo; means to assume a position. The given passage states that India occupies tenth place in the world and fourth in Asia in plant diversity. Hence, &lsquo;occupies&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>5.(d) occupies<br>&lsquo;Occupies&rsquo; का अर्थ है किसी स्थान (position) को ग्रहण करना। दिए गए Passage में कहा गया है कि भारत पादप विविधता (plant diversity) में विश्व में दसवें स्थान पर तथा एशिया में चौथे स्थान पर है। अतः, &lsquo;occupies&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "7",
                    question_en: "<p>6. <strong>Cloze Test:</strong><br>Our country, India, is one of the 12 mega bio-diversity countries of the world. With about 47,000 plant species, India (5)_____ tenth place in the world and fourth in Asia in plant diversity. There are (6)______ 15,000 flowering plants in India, which account for 6 per cent of the world&rsquo;s (7)_____ number of flowering plants. The country has many non-flowering plants, (8)_____ as ferns, algae and fungi. India also has approximately 90,000 (9)____ of animals, as well as a rich variety of fish in its fresh and marine waters.<br>Select the most appropriate option to fill in blank number 6.</p>",
                    question_hi: "<p>6. <strong>Cloze Test:</strong><br>Our country, India, is one of the 12 mega bio-diversity countries of the world. With about 47,000 plant species, India (5)_____ tenth place in the world and fourth in Asia in plant diversity. There are (6)______ 15,000 flowering plants in India, which account for 6 per cent of the world&rsquo;s (7)_____ number of flowering plants. The country has many non-flowering plants, (8)_____ as ferns, algae and fungi. India also has approximately 90,000 (9)____ of animals, as well as a rich variety of fish in its fresh and marine waters.<br>Select the most appropriate option to fill in blank number 6.</p>",
                    options_en: ["<p>a</p>", "<p>about</p>", 
                                "<p>many</p>", "<p>of</p>"],
                    options_hi: ["<p>a</p>", "<p>about</p>",
                                "<p>many</p>", "<p>of</p>"],
                    solution_en: "<p>6.(b) about<br>&lsquo;About&rsquo; means nearly or approximately. The given passage states that there are about 15,000 flowering plants in India. Hence, &lsquo;about&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>6.(b) about<br>&lsquo;About&rsquo; का अर्थ है आस-पास (nearly) या लगभग (approximately)। दिए गए Passage में कहा गया है कि भारत में लगभग 15,000 फूलदार पौधे हैं। अतः, &lsquo;about&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "7",
                    question_en: "<p>7. <strong>Cloze Test:</strong><br>Our country, India, is one of the 12 mega bio-diversity countries of the world. With about 47,000 plant species, India (5)_____ tenth place in the world and fourth in Asia in plant diversity. There are (6)______ 15,000 flowering plants in India, which account for 6 per cent of the world&rsquo;s (7)_____ number of flowering plants. The country has many non-flowering plants, (8)_____ as ferns, algae and fungi. India also has approximately 90,000 (9)____ of animals, as well as a rich variety of fish in its fresh and marine waters.<br>Select the most appropriate option to fill in blank number 7.</p>",
                    question_hi: "<p>7. <strong>Cloze Test:</strong><br>Our country, India, is one of the 12 mega bio-diversity countries of the world. With about 47,000 plant species, India (5)_____ tenth place in the world and fourth in Asia in plant diversity. There are (6)______ 15,000 flowering plants in India, which account for 6 per cent of the world&rsquo;s (7)_____ number of flowering plants. The country has many non-flowering plants, (8)_____ as ferns, algae and fungi. India also has approximately 90,000 (9)____ of animals, as well as a rich variety of fish in its fresh and marine waters.<br>Select the most appropriate option to fill in blank number 7.</p>",
                    options_en: ["<p>total</p>", "<p>exact</p>", 
                                "<p>exactly</p>", "<p>nearly</p>"],
                    options_hi: ["<p>total</p>", "<p>exact</p>",
                                "<p>exactly</p>", "<p>nearly</p>"],
                    solution_en: "<p>7.(a) total<br>&lsquo;Total&rsquo; means the whole amount. The given passage states that there are about 15,000 thousand flowering plants in India, which account for 6 percent of the world&rsquo;s total number of flowering plants. Hence, &lsquo;total&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>7.(a) total<br>&lsquo;Total&rsquo; का अर्थ है पूरी राशि (whole amount)। दिए गए Passage में कहा गया है कि भारत में लगभग 15,000 फूलदार पौधे (flowering plants) हैं, जो विश्व के कुल फूलदार पौधों की संख्या का 6 प्रतिशत है। अतः, &lsquo;total&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "7",
                    question_en: "<p>8. <strong>Cloze Test:</strong><br>Our country, India, is one of the 12 mega bio-diversity countries of the world. With about 47,000 plant species, India (5)_____ tenth place in the world and fourth in Asia in plant diversity. There are (6)______ 15,000 flowering plants in India, which account for 6 per cent of the world&rsquo;s (7)_____ number of flowering plants. The country has many non-flowering plants, (8)_____ as ferns, algae and fungi. India also has approximately 90,000 (9)____ of animals, as well as a rich variety of fish in its fresh and marine waters.<br>Select the most appropriate option to fill in blank number 8.</p>",
                    question_hi: "<p>8. <strong>Cloze Test:</strong><br>Our country, India, is one of the 12 mega bio-diversity countries of the world. With about 47,000 plant species, India (5)_____ tenth place in the world and fourth in Asia in plant diversity. There are (6)______ 15,000 flowering plants in India, which account for 6 per cent of the world&rsquo;s (7)_____ number of flowering plants. The country has many non-flowering plants, (8)_____ as ferns, algae and fungi. India also has approximately 90,000 (9)____ of animals, as well as a rich variety of fish in its fresh and marine waters.<br>Select the most appropriate option to fill in blank number 8.</p>",
                    options_en: ["<p>such</p>", "<p>in</p>", 
                                "<p>for</p>", "<p>as</p>"],
                    options_hi: ["<p>such</p>", "<p>in</p>",
                                "<p>for</p>", "<p>as</p>"],
                    solution_en: "<p>8.(a) such<br>&lsquo;Such as&rsquo; is used to introduce examples of something we mention. In the given passage, some examples of non-flowering plants are given. Hence, &lsquo;such&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>8.(a) such<br>&lsquo;Such as&rsquo; का प्रयोग हमारे द्वारा Mention की गई किसी चीज़ के Example को Introduce करने के लिए किया जाता है। दिए गए Passage में, गैर-फूल वाले पौधों के कुछ Example दिए गए हैं। अतः, &lsquo;such&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "7",
                    question_en: "<p>9. <strong>Cloze Test:</strong><br>Our country, India, is one of the 12 mega bio-diversity countries of the world. With about 47,000 plant species, India (5)_____ tenth place in the world and fourth in Asia in plant diversity. There are (6)______ 15,000 flowering plants in India, which account for 6 per cent of the world&rsquo;s (7)_____ number of flowering plants. The country has many non-flowering plants, (8)_____ as ferns, algae and fungi. India also has approximately 90,000 (9)____ of animals, as well as a rich variety of fish in its fresh and marine waters.<br>Select the most appropriate option to fill in blank number 9.</p>",
                    question_hi: "<p>9. <strong>Cloze Test:</strong><br>Our country, India, is one of the 12 mega bio-diversity countries of the world. With about 47,000 plant species, India (5)_____ tenth place in the world and fourth in Asia in plant diversity. There are (6)______ 15,000 flowering plants in India, which account for 6 per cent of the world&rsquo;s (7)_____ number of flowering plants. The country has many non-flowering plants, (8)_____ as ferns, algae and fungi. India also has approximately 90,000 (9)____ of animals, as well as a rich variety of fish in its fresh and marine waters.<br>Select the most appropriate option to fill in blank number 9.</p>",
                    options_en: ["<p>kind</p>", "<p>species</p>", 
                                "<p>type</p>", "<p>specie</p>"],
                    options_hi: ["<p>kind</p>", "<p>species</p>",
                                "<p>type</p>", "<p>specie</p>"],
                    solution_en: "<p>9.(b) species<br>&lsquo;Species&rsquo; means a set of animals or plants in which the members have similar characteristics to each other. The given passage states that India also has approximately 90,000 species of animals, as well as a rich variety of fish in its fresh and marine waters. Hence, &lsquo;species&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>9.(b) species<br>&lsquo;Species&rsquo; का अर्थ है जानवरों या पौधों का एक समूह जिसके सदस्यों में एक-दूसरे के समान गुण होते हैं। दिए गए Passage में कहा गया है कि भारत में जानवरों की लगभग 90,000 प्रजातियाँ (species) हैं, साथ ही इसके ताजे एवं समुद्री जल में मछलियों की भी समृद्ध विविधता (rich variety) है। अतः, &lsquo;species&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "7",
                    question_en: "<p>10. Select the most appropriate option that can substitute the underlined words in the following sentence.<br>I consider that every matter must be considered <span style=\"text-decoration: underline;\">in every point</span> of view.</p>",
                    question_hi: "<p>10. Select the most appropriate option that can substitute the underlined words in the following sentence.<br>I consider that every matter must be considered <span style=\"text-decoration: underline;\">in every point</span> of view.</p>",
                    options_en: ["<p>on every point</p>", "<p>of every point</p>", 
                                "<p>at every point</p>", "<p>from every point</p>"],
                    options_hi: ["<p>on every point</p>", "<p>of every point</p>",
                                "<p>at every point</p>", "<p>from every point</p>"],
                    solution_en: "<p>10.(d) from every point<br>&lsquo;From&rsquo; is used to show somebody&rsquo;s point of view. Hence, &lsquo;from every point&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>10.(d) from every point<br>&lsquo;From&rsquo; का प्रयोग किसी के दृष्टिकोण (point of view) को दर्शाने के लिए किया जाता है। अतः, &lsquo;from every point&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "7",
                    question_en: "<p>11. Select the most appropriate ANTONYM of the bracketed word in the following sentence to fill in the blank.<br>Lewis was aggressive, ________ (normal), moody, and brilliantly clever.</p>",
                    question_hi: "<p>11. Select the most appropriate ANTONYM of the bracketed word in the following sentence to fill in the blank.<br>Lewis was aggressive, ________ (normal), moody, and brilliantly clever.</p>",
                    options_en: ["<p>dogmatic</p>", "<p>eccentric</p>", 
                                "<p>selfish</p>", "<p>feeble</p>"],
                    options_hi: ["<p>dogmatic</p>", "<p>eccentric</p>",
                                "<p>selfish</p>", "<p>feeble</p>"],
                    solution_en: "<p>11.(a) <strong>Eccentric</strong>- a person of unconventional and slightly strange views or behaviour.<br><strong>Normal- </strong>ordinary or usual.<br><strong>Dogmatic</strong>- being certain that your beliefs are right and that others should accept them.<br><strong>Selfish</strong>- thinking only about your own needs or wishes and not about other people&rsquo;s.<br><strong>Feeble</strong>- lacking physical strength, especially because of age or weakness.</p>",
                    solution_hi: "<p>11.(a) <strong>Eccentric</strong> (सनकी/विचित्र) - a person of unconventional and slightly strange views or behaviour.<br><strong>Normal</strong> (सामान्य) - ordinary or usual.<br><strong>Dogmatic</strong> (हठधर्मी) - being certain that your beliefs are right and that others should accept them.<br><strong>Selfish </strong>(स्वार्थी) - thinking only about your own needs or wishes and not about other people&rsquo;s.<br><strong>Feeble </strong>(कमज़ोर) - lacking physical strength, especially because of age or weakness.</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "7",
                    question_en: "<p>12. Select the most appropriate synonym of the underlined word.<br>The sun was shining bright and <span style=\"text-decoration: underline;\">intense</span> in the sky.</p>",
                    question_hi: "<p>12. Select the most appropriate synonym of the underlined word.<br>The sun was shining bright and <span style=\"text-decoration: underline;\">intense</span> in the sky.</p>",
                    options_en: ["<p>Weak</p>", "<p>Strong</p>", 
                                "<p>Mild</p>", "<p>Subtle</p>"],
                    options_hi: ["<p>Weak</p>", "<p>Strong</p>",
                                "<p>Mild</p>", "<p>Subtle</p>"],
                    solution_en: "<p>12.(b) <strong>Strong</strong><br><strong>Intense</strong>- very great, strong or serious.<br><strong>Mild</strong>- not severe, serious, or harsh.<br><strong>Subtle</strong>- o delicate or precise as to be difficult to analyse or describe.</p>",
                    solution_hi: "<p>12.(b) <strong>Strong</strong><br><strong>Intense </strong>(गंभीर/गहन) - very great, strong or serious.<br><strong>Mild </strong>(विनम्र) - not severe, serious, or harsh.<br><strong>Subtle </strong>(सूक्ष्म) - o delicate or precise as to be difficult to analyse or describe.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "7",
                    question_en: "<p>13. Select the most appropriate synonym of the given word.<br>Attire</p>",
                    question_hi: "<p>13. Select the most appropriate synonym of the given word.<br>Attire</p>",
                    options_en: ["<p>Dress</p>", "<p>Theatre</p>", 
                                "<p>Hotel</p>", "<p>Park</p>"],
                    options_hi: ["<p>Dress</p>", "<p>Theatre</p>",
                                "<p>Hotel</p>", "<p>Park</p>"],
                    solution_en: "<p>13.(a) <strong>Dress</strong>- a piece of clothing that covers the top half of the body and hangs down over the legs.<br><strong>Attire</strong>- clothes, especially of a particular or formal type.<br><strong>Theatre</strong>- a room or hall for lectures with seats in tiers.<br><strong>Hotel</strong>- a building where you pay to have a room to sleep in, and where you can sometimes eat meals.<br><strong>Park</strong>- a large public garden or area of land used for recreation.</p>",
                    solution_hi: "<p>13.(a) <strong>Dress </strong>(पोशाक) - a piece of clothing that covers the top half of the body and hangs down over the legs.<br><strong>Attire </strong>(पोशाक) - clothes, especially of a particular or formal type.<br><strong>Theatre </strong>(रंगमंच) - a room or hall for lectures with seats in tiers.<br><strong>Hotel </strong>(विश्रामालय/भोजनालय) - a building where you pay to have a room to sleep in, and where you can sometimes eat meals.<br><strong>Park </strong>(उद्यान/ उपवन/ बगीचा) - a large public garden or area of land used for recreation.</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "7",
                    question_en: "<p>14. Select the most appropriate meaning of the given idiom.<br>Under the weather</p>",
                    question_hi: "<p>14. Select the most appropriate meaning of the given idiom.<br>Under the weather</p>",
                    options_en: ["<p>To feel unwell</p>", "<p>To work against the weather</p>", 
                                "<p>To feel secure</p>", "<p>To enjoy the weather</p>"],
                    options_hi: ["<p>To feel unwell</p>", "<p>To work against the weather</p>",
                                "<p>To feel secure</p>", "<p>To enjoy the weather</p>"],
                    solution_en: "<p>14.(a) <strong>Under the weather- </strong>to feel unwell.<br>E.g.- I am feeling a bit under the weather today, so I think I will stay home and rest.</p>",
                    solution_hi: "<p>14.(a) <strong>Under the weather-</strong> to feel unwell./अस्वस्थ महसूस करना।<br>E.g.- I am feeling a bit under the weather today, so I think I will stay home and rest.</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "7",
                    question_en: "<p>15. Select the most appropriate option that can substitute the underlined segment in the given sentence.<br>Once bitten, <span style=\"text-decoration: underline;\">thrice shy.</span></p>",
                    question_hi: "<p>15. Select the most appropriate option that can substitute the underlined segment in the given sentence.<br>Once bitten, <span style=\"text-decoration: underline;\">thrice shy.</span></p>",
                    options_en: ["<p>twice shy</p>", "<p>always shy</p>", 
                                "<p>once shy</p>", "<p>never shy</p>"],
                    options_hi: ["<p>twice shy</p>", "<p>always shy</p>",
                                "<p>once shy</p>", "<p>never shy</p>"],
                    solution_en: "<p>15.(a) twice shy<br>&lsquo;Once bitten, twice shy&rsquo; is an idiom which means you are frightened to do something again because you had an unpleasant experience doing it the first time. Hence, option (a) is the most appropriate answer.</p>",
                    solution_hi: "<p>15.(a) twice shy<br>&lsquo;Once bitten, twice shy&rsquo; एक Idiom है जिसका अर्थ है कि आप किसी काम को दोबारा करने से डरते हैं क्योंकि पहली बार ऐसा करने में आपको अप्रिय अनुभव (unpleasant experience) हुआ था। अतः, option (a) सबसे उपयुक्त उत्तर है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "16",
                    section: "7",
                    question_en: "<p>16. Select the most appropriate synonym of the given word.<br>Hypocrisy</p>",
                    question_hi: "<p>16. Select the most appropriate synonym of the given word.<br>Hypocrisy</p>",
                    options_en: ["<p>Deceit</p>", "<p>Truth</p>", 
                                "<p>Sincerity</p>", "<p>Harm</p>"],
                    options_hi: ["<p>Deceit</p>", "<p>Truth</p>",
                                "<p>Sincerity</p>", "<p>Harm</p>"],
                    solution_en: "<p>16.(a) <strong>Deceit</strong>- the action or practice of deceiving someone by concealing or misrepresenting the truth.<br><strong>Hypocrisy</strong>- a situation in which someone pretends to believe something that they do not really believe.<br><strong>Sincerity</strong>- the absence of pretense, deceit or hypocrisy.<br><strong>Harm</strong>- physical or other injury or damage.</p>",
                    solution_hi: "<p>16.(a) <strong>Deceit </strong>(छल/झूठ/धोखा) - the action or practice of deceiving someone by concealing or misrepresenting the truth.<br><strong>Hypocrisy </strong>(पाखंड) - a situation in which someone pretends to believe something that they do not really believe.<br><strong>Sincerity </strong>(ईमानदारी) - the absence of pretense, deceit or hypocrisy.<br><strong>Harm </strong>(हानि/क्षति) - physical or other injury or damage.</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "17",
                    section: "7",
                    question_en: "17. Select the option that can substitute the bracketed word segment meaningfully.<br />Writing can instill a number of emotions (as in a reader).",
                    question_hi: "17. Select the option that can substitute the bracketed word segment meaningfully.<br />Writing can instill a number of emotions (as in a reader).",
                    options_en: [" within a reader", " among a reader", 
                                " instead a reader", " until a reader"],
                    options_hi: [" within a reader", " among a reader",
                                " instead a reader", " until a reader"],
                    solution_en: "17.(a) within a reader<br />‘Within’ means inside. The given sentence states that writing can instill a number of emotions within a reader. Hence, ‘within a reader’ is the most appropriate answer.",
                    solution_hi: "17.(a) within a reader<br />‘Within’ का अर्थ है अंदर (inside)। दिए गए Sentence में कहा गया है कि लेखन (writing) पाठक के भीतर कई भावनाएँ उत्पन्न कर सकता है। अतः, ‘within a reader’ सबसे उपयुक्त उत्तर है।",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "18",
                    section: "7",
                    question_en: "<p>18. Select the most appropriate option to fill in the blank.<br>I am scared of snakes; likewise, you are ________ of lizards.</p>",
                    question_hi: "<p>18. Select the most appropriate option to fill in the blank.<br>I am scared of snakes; likewise, you are ________ of lizards.</p>",
                    options_en: ["<p>fond</p>", "<p>passionate</p>", 
                                "<p>critical</p>", "<p>fearful</p>"],
                    options_hi: ["<p>fond</p>", "<p>passionate</p>",
                                "<p>critical</p>", "<p>fearful</p>"],
                    solution_en: "<p>18.(d) fearful<br>&lsquo;Fearful&rsquo; means to be afraid of something. The given sentence states that I am scared of snakes, likewise you are fearful of lizards. Hence, &lsquo;fearful&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>18.(d) fearful<br>&lsquo;Fearful&rsquo; का अर्थ है किसी चीज़ से डरना। दिए गए Sentence में कहा गया है कि मुझे साँपों (snakes) से डर लगता है, वैसे ही आपको छिपकलियों से डर लगता है। अतः, &lsquo;fearful&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "19",
                    section: "7",
                    question_en: "19. Select the sentence which gives the most appropriate meaning of the given idiom.<br />A piece of cake",
                    question_hi: "19. Select the sentence which gives the most appropriate meaning of the given idiom.<br />A piece of cake",
                    options_en: [" After months of practice, Emily performed her piano piece flawlessly during the concert.", " Despite the heavy rain, they managed to have a successful outdoor event.", 
                                " The puzzle was so challenging that it took hours for Sam to solve it.", " The restaurant was crowded, but they found a table without any difficulty."],
                    options_hi: [" After months of practice, Emily performed her piano piece flawlessly during the concert.", " Despite the heavy rain, they managed to have a successful outdoor event.",
                                " The puzzle was so challenging that it took hours for Sam to solve it.", " The restaurant was crowded, but they found a table without any difficulty."],
                    solution_en: "19.(d) The restaurant was crowded, but they found a table without any difficulty.<br />The idiom ‘piece of cake’ means something that is very easy to do. Going through the options, sentence given in option (d) has the most appropriate meaning of the given idiom.",
                    solution_hi: "19.(d) The restaurant was crowded, but they found a table without any difficulty.<br />Idiom ‘piece of cake’ का अर्थ है कुछ ऐसा जो करना बहुत आसान है। Options के मध्यम से जाने पर option (d) में दिया गया Sentence दिए गए Idiom का सबसे उपयुक्त अर्थ देता है।",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "20",
                    section: "7",
                    question_en: "<p>20. Select the most appropriate synonym of the given word.<br>Bliss</p>",
                    question_hi: "<p>20. Select the most appropriate synonym of the given word.<br>Bliss</p>",
                    options_en: ["<p>Consecration</p>", "<p>Genesis</p>", 
                                "<p>Avail</p>", "<p>Pleasure</p>"],
                    options_hi: ["<p>Consecration</p>", "<p>Genesis</p>",
                                "<p>Avail</p>", "<p>Pleasure</p>"],
                    solution_en: "<p>20.(d) <strong>Pleasure</strong>- a feeling of happy satisfaction and enjoyment.<br><strong>Bliss</strong>- perfect happiness.<br><strong>Consecration</strong>- the act or process of officially making something holy and able to be used for religious ceremonies.<br><strong>Genesis</strong>- the origin or mode of formation of something.<br><strong>Avail</strong>- take advantage of an opportunity or available resource.</p>",
                    solution_hi: "<p>20.(d) <strong>Pleasure </strong>(सुख) - a feeling of happy satisfaction and enjoyment.<br>Bliss (परमानंद) - perfect happiness.<br><strong>Consecration </strong>(अभिषेक करना ) - the act or process of officially making something holy and able to be used for religious ceremonies.<br><strong>Genesis </strong>(उत्पत्ति) - the origin or mode of formation of something.<br><strong>Avail </strong>(लाभ उठाना) - take advantage of an opportunity or available resource.</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "21",
                    section: "7",
                    question_en: "<p>21. Select the most appropriate ANTONYM of the underlined word in the given sentence.<br>The dog was <span style=\"text-decoration: underline;\">agitated</span> and bit the child.</p>",
                    question_hi: "<p>21. Select the most appropriate ANTONYM of the underlined word in the given sentence.<br>The dog was <span style=\"text-decoration: underline;\">agitated</span> and bit the child.</p>",
                    options_en: ["<p>nerveless</p>", "<p>serene</p>", 
                                "<p>steady</p>", "<p>quiescent</p>"],
                    options_hi: ["<p>nerveless</p>", "<p>serene</p>",
                                "<p>steady</p>", "<p>quiescent</p>"],
                    solution_en: "<p>21.(b) <strong>Serene</strong>- deeply peaceful and undisturbed.<br><strong>Agitated</strong>- feeling or appearing troubled or nervous.<br><strong>Nerveless</strong>- calm and confident about something difficult that you are doing.<br><strong>Steady</strong>- firmly fixed, supported, or balanced.<br><strong>Quiescent</strong>- temporarily quiet and not active.</p>",
                    solution_hi: "<p>21.(b) <strong>Serene </strong>(शांत) - deeply peaceful and undisturbed.<br><strong>Agitated </strong>(उत्तेजित) - feeling or appearing troubled or nervous.<br><strong>Nerveless </strong>(निर्भीक) - calm and confident about something difficult that you are doing.<br><strong>Steady </strong>(स्थिर) - firmly fixed, supported, or balanced.<br><strong>Quiescent </strong>(सुप्त/नष्क्रिय) - temporarily quiet and not active.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "22",
                    section: "7",
                    question_en: "<p>22. Select the most appropriate option that can substitute the underlined segment in the given sentence.<br>She <span style=\"text-decoration: underline;\">finished her quickly breakfast</span> and rushed to school.</p>",
                    question_hi: "<p>22. Select the most appropriate option that can substitute the underlined segment in the given sentence.<br>She <span style=\"text-decoration: underline;\">finished her quickly breakfast</span> and rushed to school.</p>",
                    options_en: ["<p>her breakfast finished quickly</p>", "<p>quickly her breakfast finished</p>", 
                                "<p>finished her breakfast quickly</p>", "<p>finished breakfast hers quickly</p>"],
                    options_hi: ["<p>her breakfast finished quickly</p>", "<p>quickly her breakfast finished</p>",
                                "<p>finished her breakfast quickly</p>", "<p>finished breakfast hers quickly</p>"],
                    solution_en: "<p>22.(c) finished her breakfast quickly<br>The correct order of adverbs is SVOMPT, where S- subject, V-verb, O- object, M- adverb of manner, P- adverb of place, T- adverb of time. In the given sentence, the adverb \'quickly\' will be used after the object \'her breakfast\'. Hence, &lsquo;finished her breakfast quickly&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>22.(c) finished her breakfast quickly<br>SVOMPT, Adverb का सही Order है, जहाँ S- subject, V-verb, O- object, M- adverb of manner, P- adverb of place, T- adverb of time है। दिए गए Sentence में, Adverb \'quickly\' का प्रयोग Object \'her breakfast\' के बाद किया जाएगा। अतः, &lsquo;finished her breakfast quickly&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "23",
                    section: "7",
                    question_en: "23. Select the most appropriate option to fill in the blank.<br />I have observed that there is proper natural ____________ even at 6 o\'clock on summer mornings.",
                    question_hi: "23. Select the most appropriate option to fill in the blank.<br />I have observed that there is proper natural ____________ even at 6 o\'clock on summer mornings.",
                    options_en: [" light", " gleam", 
                                " lucent", " sparkle"],
                    options_hi: [" light", " gleam",
                                " lucent", " sparkle"],
                    solution_en: "23.(a) light<br />The given sentence states that I have observed that there is proper natural light even at 6 o’clock on summer mornings. Hence, ‘light’ is the most appropriate answer.",
                    solution_hi: "23.(a) light<br />दिए गए Sentence में कहा गया है कि मैंने देखा है कि गर्मियों की सुबह 6 बजे भी पर्याप्त प्राकृतिक प्रकाश (proper natural light) रहता है। अतः, ‘light’ सबसे उपयुक्त उत्तर है।",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "24",
                    section: "7",
                    question_en: "24.  There is a spelling error in the given sentence. Select the option that contains the INCORRECTLY spelt word.<br />He was thrilled when he heard about there journey to wonderland.",
                    question_hi: "24.  There is a spelling error in the given sentence. Select the option that contains the INCORRECTLY spelt word.<br />He was thrilled when he heard about there journey to wonderland.",
                    options_en: ["  thrilled", " heard", 
                                " there", " journey"],
                    options_hi: ["  thrilled", " heard",
                                " there", " journey"],
                    solution_en: "24.(c) there<br />‘Their’ is the correct spelling.",
                    solution_hi: "24.(c) there<br />‘Their’ सही spelling है। ",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "25",
                    section: "6",
                    question_en: "25. Select the INCORRECTLY spelt word.",
                    question_hi: "25. Select the INCORRECTLY spelt word.",
                    options_en: [" Condimnation", " Imprisonment", 
                                " Conglomerate", " Intermingled"],
                    options_hi: [" Condimnation", " Imprisonment",
                                " Conglomerate", " Intermingled"],
                    solution_en: "25.(a) Condimnation<br />‘Condemnation’ is the correct spelling.",
                    solution_hi: "25.(a) Condimnation<br />‘Condemnation’ सही spelling है। ",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "26",
                    section: "6",
                    question_en: "<p>26. If 25 October 2004 was Monday, then what was the day of the week on 2 November&nbsp;2007 ?</p>",
                    question_hi: "<p>26. यदि 25 अक्टूबर 2004 को सोमवार था, तो 2 नवंबर 2007 को सप्ताह का कौन-सा दिन रहा होगा ?</p>",
                    options_en: ["<p>Thursday</p>", "<p>Wednesday</p>", 
                                "<p>Saturday</p>", "<p>Friday</p>"],
                    options_hi: ["<p>गुरुवार</p>", "<p>बुधवार</p>",
                                "<p>शनिवार</p>", "<p>शुक्रवार</p>"],
                    solution_en: "<p>26.(d) 25 October 2004 was Monday . On going to 2007 the number of odd days = +1 + 1 + 1 = 3. We have reached till 25 October, but we have to reach till 2 November, the number of day between = 8. Total number of days = 8 + 3 = 11. On dividing 11 by 7, the remainder is = 4. Monday + 4 = Friday.</p>",
                    solution_hi: "<p>26.(d) 25 अक्टूबर 2004 को सोमवार था। 2007 में जाने पर विषम दिनों की संख्या = +1 + 1 + 1 = 3. हम 25 अक्टूबर तक पहुंच गए हैं, लेकिन हमें 2 नवंबर तक पहुंचना है, बीच के दिनों की संख्या = 8. कुल दिनों की संख्या = 8 + 3 = 11. 11 को 7 से विभाजित करने पर शेषफल = 4. सोमवार + 4 = शुक्रवार.</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "27",
                    section: "6",
                    question_en: "<p>27. Based on the alphabetical order, three of the following four letter-clusters are alike in a&nbsp;certain way and thus form a group. Which letter-cluster does not belong to that group ?<br>(Note: The odd one out is not based on the number of consonants/vowels or their<br>position in the letter cluster.)</p>",
                    question_hi: "<p>27. वर्णमाला क्रम के आधार पर, निम्नलिखित चार अक्षर-समूहों में से तीन एक निश्चित प्रकार से समान हैं&nbsp;और इस प्रकार एक समूह बनाते हैं। कौन-सा अक्षर-समूह उस समूह से संबंधित नहीं है ?<br>(ध्यान दें: असंगत अक्षर-समूह, उस अक्षर-समूह मेें व्यंजन/स्वरों की संख्या या उनके स्थान पर आधारित नहीं है।)</p>",
                    options_en: ["<p>DGK</p>", "<p>ILO</p>", 
                                "<p>NQU</p>", "<p>JMQ</p>"],
                    options_hi: ["<p>DGK</p>", "<p>ILO</p>",
                                "<p>NQU</p>", "<p>JMQ</p>"],
                    solution_en: "<p>27.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1735116563335.png\" alt=\"rId39\" width=\"114\" height=\"67\">&nbsp; &nbsp;, <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1735116563664.png\" alt=\"rId40\" width=\"112\" height=\"66\">&nbsp; &nbsp;,&nbsp; &nbsp;<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1735116563798.png\" alt=\"rId41\" width=\"111\" height=\"65\"><br>But,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1735116563943.png\" alt=\"rId42\" width=\"109\" height=\"66\"></p>",
                    solution_hi: "<p>27.(b) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1735116563335.png\" alt=\"rId39\" width=\"114\" height=\"67\">&nbsp; &nbsp;, <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1735116563664.png\" alt=\"rId40\" width=\"112\" height=\"66\">&nbsp; &nbsp;,&nbsp; &nbsp;<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1735116563798.png\" alt=\"rId41\" width=\"111\" height=\"65\"><br>लेकिन,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1735116563943.png\" alt=\"rId42\" width=\"109\" height=\"66\"></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "28",
                    section: "6",
                    question_en: "<p>28. If &lsquo;A&rsquo; stands for &lsquo;&divide;&rsquo;, &lsquo;B&rsquo; stands for &lsquo;&times;&rsquo;, &lsquo;C&rsquo; stands for &lsquo;+&rsquo; and &lsquo;D&rsquo; stands for &lsquo;&ndash;&rsquo;, then the&nbsp;resultant of which of the following will be 428?</p>",
                    question_hi: "<p>28. यदि &lsquo;A&rsquo; का अर्थ &lsquo;&divide;&rsquo;, &lsquo;B&rsquo; का अर्थ &lsquo;&times;&rsquo;, &lsquo;C&rsquo; का अर्थ &lsquo;+&rsquo; और &lsquo;D&rsquo; का अर्थ &lsquo;&ndash;&rsquo; है, तो निम्नलिखित में से किसका परिणाम 428 होगा ?</p>",
                    options_en: ["<p>113 B 4 A 62 D 2 C 7</p>", "<p>113 A 4 D 62 B 2 C 7</p>", 
                                "<p>113 B 4 C 62 A 2 D 7</p>", "<p>113 B 4 D 62 A 2 C 7</p>"],
                    options_hi: ["<p>113 B 4 A 62 D 2 C 7</p>", "<p>113 A 4 D 62 B 2 C 7</p>",
                                "<p>113 B 4 C 62 A 2 D 7</p>", "<p>113 B 4 D 62 A 2 C 7</p>"],
                    solution_en: "<p>28.(d) After going through all the options, option d satisfies. <br>113 B 4 D 62 A 2 C 7 <br>As per given instruction after interchanging the letter with sign we get<br>113 &times; 4 - 62 <math display=\"inline\"><mo>&#247;</mo></math> 2 + 7<br>452 - 31 + 7 <br>452 - 24 = 428</p>",
                    solution_hi: "<p>28.(d) <br>सभी विकल्पों की जांच करने पर विकल्प d संतुष्ट करता है।<br>113 B 4 D 62 A 2 C 7 <br>दिए गए निर्देश के अनुसार अक्षर को चिन्ह से बदलने पर हमें प्राप्त होता है<br>113 &times; 4 - 62 <math display=\"inline\"><mo>&#247;</mo></math> 2 + 7<br>452 - 31 + 7 <br>452 - 24 = 428</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "29",
                    section: "6",
                    question_en: "<p>29. How many triangles are there in the given figure ?&nbsp;<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1735116564083.png\" alt=\"rId43\" width=\"144\" height=\"111\"></p>",
                    question_hi: "<p>29. दी गई आकृति में कितने त्रिभुज हैं ?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1735116564083.png\" alt=\"rId43\" width=\"144\" height=\"111\"></p>",
                    options_en: ["<p>10</p>", "<p>11</p>", 
                                "<p>17</p>", "<p>12</p>"],
                    options_hi: ["<p>17</p>", "<p>12</p>",
                                "<p>11</p>", "<p>10</p>"],
                    solution_en: "<p>29.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1735116564246.png\" alt=\"rId44\" width=\"169\" height=\"130\"><br>There are total 17 triangle<br>ABE , BCM, CDH, CMH, BDH, BCH , MGH, BEH, CGH, BFE, BFH, EIJ, EFJ, FJK, FKH, HKL, BHK,</p>",
                    solution_hi: "<p>29.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1735116564246.png\" alt=\"rId44\" width=\"169\" height=\"130\"><br>कुल 17 त्रिभुज हैं<br>ABE , BCM, CDH, CMH, BDH, BCH , MGH, BEH, CGH, BFE, BFH, EIJ, EFJ, FJK, FKH, HKL, BHK,</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "30",
                    section: "6",
                    question_en: "<p>30. If 29 July 2001 is Sunday, then what will be the day of the week on 20 February 2015 ?</p>",
                    question_hi: "<p>30. यदि 29 जुलाई 2001 को रविवार है, तो 20 फरवरी 2015 को सप्ताह का कौन सा दिन होगा ?</p>",
                    options_en: ["<p>Tuesday</p>", "<p>Monday</p>", 
                                "<p>Sunday</p>", "<p>Friday</p>"],
                    options_hi: ["<p>मंगलवार</p>", "<p>सोमवार</p>",
                                "<p>रविवार</p>", "<p>शुक्रवार</p>"],
                    solution_en: "<p>30.(d) 29 July 2001 is Sunday. On going to 2015 the number of odd days =&nbsp;+1 + 1 + 2 + 1 + 1 + 1 + 2 + 1 + 1 + 1 + 2 + 1 +1 + 1 = 17. On dividing 17 by 7 the number remainder is 3. Sunday + 3 = Wednesday . We have reached till 29 July , but we have to go back to 20 February, the number of days between = 29 + 30 + 31 + 30 + 31 + 8 = 159. On dividing 159 by 7 the remainder = 5 . Wednesday - 5 = Friday.</p>",
                    solution_hi: "<p>30.(d) 29 जुलाई 2001 को रविवार है. 2015 में जाने पर विषम दिनों की संख्या =&nbsp;+1 + 1 + 2 + 1 + 1 + 1 + 2 + 1 + 1 + 1 + 2 + 1 +1 + 1 = 17. 17 को 7 से विभाजित करने पर शेष संख्या 3 आती है. रविवार + 3 = बुधवार. हम 29 जुलाई तक पहुंच गए हैं, लेकिन हमें 20 फरवरी तक वापस जाना है, बीच में दिनों की संख्या = 29 + 30 + 31 + 30 + 31 + 8 = 159। 159 को 7 से विभाजित करने पर शेषफल = 5। बुधवार - 5 = शुक्रवार.</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "31",
                    section: "6",
                    question_en: "<p>31. What will come in the place of the question mark (?) in the following equation, if &lsquo;+&rsquo; and &lsquo;<math display=\"inline\"><mo>&#215;</mo></math>&rsquo;are interchanged and \'-&rsquo; and &lsquo;&divide;&rsquo; are interchanged ?<br>98 - 14 <math display=\"inline\"><mo>&#215;</mo></math> 11 + 6 &divide; 23 = ?</p>",
                    question_hi: "<p>31. यदि &lsquo;+&rsquo;और \'<math display=\"inline\"><mo>&#215;</mo></math>\' को आपस में बदल दिया जाए तथा &lsquo;-&rsquo; और\' को &lsquo;&divide;&rsquo; आपस में बदल दिया जाए तो निम्नलिखित समीकरण में (?) के स्थान पर क्या आएगा ?<br>98 - 14 <math display=\"inline\"><mo>&#215;</mo></math> 11 + 6 &divide; 23 = ?</p>",
                    options_en: ["<p>56</p>", "<p>52</p>", 
                                "<p>55</p>", "<p>50</p>"],
                    options_hi: ["<p>56</p>", "<p>52</p>",
                                "<p>55</p>", "<p>50</p>"],
                    solution_en: "<p>31.(d) <strong>Given :- </strong>98 - 14 &times; 11 + 6 <math display=\"inline\"><mo>&#247;</mo></math> 23<br>As per given instruction after interchanging &lsquo;+&rsquo; and &lsquo;&times;&rsquo; and &lsquo;-&rsquo; and &lsquo;<math display=\"inline\"><mo>&#247;</mo></math>&rsquo; we get<br>98 <math display=\"inline\"><mo>&#247;</mo></math> 14 + 11 &times; 6 - 23<br>7 + 66 - 23<br>73 - 23 = 50</p>",
                    solution_hi: "<p>31.(d) <strong>दिया गया :- </strong>98 - 14 &times; 11 + 6 <math display=\"inline\"><mo>&#247;</mo></math> 23<br>दिए गए निर्देश के अनुसार \'+\' और \'&times;\' तथा \'-\' और \'<math display=\"inline\"><mo>&#247;</mo></math>\' को आपस में बदलने पर हमें प्राप्त होता है<br>98 <math display=\"inline\"><mo>&#247;</mo></math> 14 + 11 &times; 6 - 23<br>7 + 66 - 23<br>73 - 23 = 50</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "32",
                    section: "6",
                    question_en: "<p>32. [<math display=\"inline\"><mfrac><mrow><mn>17</mn></mrow><mrow><mn>41</mn></mrow></mfrac></math>] is related to [<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>34</mn><mn>41</mn></mfrac></math>] following a certain logic. Following the same logic, [<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>11</mn><mn>85</mn></mfrac></math>] is<br>related to [<math display=\"inline\"><mfrac><mrow><mn>22</mn></mrow><mrow><mn>85</mn></mrow></mfrac></math>]. To which of the following is [<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>13</mn><mn>77</mn></mfrac></math>] related, following the same logic ?&nbsp;<br>(NOTE: Operations should be performed on the whole numbers, without breaking down the numbers into its constituent digits. E.g. 13 - Operations on 13 such as adding/subtracting/multiplying etc. to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is not allowed.)</p>",
                    question_hi: "<p>32. एक निश्चित तर्क का अनुसरण करते हुए [<math display=\"inline\"><mfrac><mrow><mn>17</mn></mrow><mrow><mn>41</mn></mrow></mfrac></math>] का संबंध [<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>34</mn><mn>41</mn></mfrac></math>] से है। उसी तर्क का अनुसरण करते हुए, [<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>11</mn><mn>85</mn></mfrac></math>] का संबंध [<math display=\"inline\"><mfrac><mrow><mn>22</mn></mrow><mrow><mn>85</mn></mrow></mfrac></math>] से हैं | उसी तर्क का अनुसरण करते हुए [<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>13</mn><mn>77</mn></mfrac></math>] का संबंध निम्नलिखित में से किससे है?<br>(ध्यान देंः संख्याओं को उनके घटक अंकों में अलग-अलग किए बिना पूर्ण संख्याओं पर संक्रियाएं की जानी चाहिए। उदाहरण के लिए संख्या 13 संख्या 13 पर संक्रियाएं जैसे 13 को जोड़ना/घटाना/गुणा करना आदि किया जा सकता है। 13 को 1 और 3 में अलग-अलग करने की और फिर 1 और 3 पर गणितीय संक्रियाएं करने की अनुमति नहीं है।)</p>",
                    options_en: ["<p>[<math display=\"inline\"><mfrac><mrow><mn>24</mn></mrow><mrow><mn>75</mn></mrow></mfrac></math>]</p>", "<p>[<math display=\"inline\"><mfrac><mrow><mn>29</mn></mrow><mrow><mn>77</mn></mrow></mfrac></math>]</p>", 
                                "<p>[<math display=\"inline\"><mfrac><mrow><mn>26</mn></mrow><mrow><mn>77</mn></mrow></mfrac></math>]</p>", "<p>[<math display=\"inline\"><mfrac><mrow><mn>23</mn></mrow><mrow><mn>77</mn></mrow></mfrac></math>]</p>"],
                    options_hi: ["<p>[<math display=\"inline\"><mfrac><mrow><mn>24</mn></mrow><mrow><mn>75</mn></mrow></mfrac></math>]</p>", "<p>[<math display=\"inline\"><mfrac><mrow><mn>29</mn></mrow><mrow><mn>77</mn></mrow></mfrac></math>]</p>",
                                "<p>[<math display=\"inline\"><mfrac><mrow><mn>26</mn></mrow><mrow><mn>77</mn></mrow></mfrac></math>]</p>", "<p>[<math display=\"inline\"><mfrac><mrow><mn>23</mn></mrow><mrow><mn>77</mn></mrow></mfrac></math>]</p>"],
                    solution_en: "<p>32.(c) <strong>Logic :-</strong> (<math display=\"inline\"><mfrac><mrow><mi>N</mi><mi>u</mi><mi>m</mi><mi>e</mi><mi>r</mi><mi>a</mi><mi>t</mi><mi>o</mi><mi>r</mi><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>2</mn></mrow><mrow><mi>D</mi><mi>e</mi><mi>n</mi><mi>o</mi><mi>m</mi><mi>i</mi><mi>n</mi><mi>a</mi><mi>t</mi><mi>o</mi><mi>r</mi></mrow></mfrac></math>)<br>(<math display=\"inline\"><mfrac><mrow><mn>17</mn></mrow><mrow><mn>41</mn></mrow></mfrac></math> , <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>34</mn><mn>41</mn></mfrac></math>) :- (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>17</mn><mo>&#215;</mo><mn>2</mn></mrow><mn>41</mn></mfrac></math>) = (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>34</mn><mn>41</mn></mfrac></math>)<br>(<math display=\"inline\"><mfrac><mrow><mn>11</mn></mrow><mrow><mn>85</mn></mrow></mfrac></math> , 2285) :- (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>11</mn><mo>&#215;</mo><mn>2</mn></mrow><mn>85</mn></mfrac></math>) = (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>22</mn><mn>85</mn></mfrac></math>)<br>Similarly,<br>(<math display=\"inline\"><mfrac><mrow><mn>13</mn></mrow><mrow><mn>77</mn></mrow></mfrac></math> , ?) :- (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>13</mn><mo>&#215;</mo><mn>2</mn></mrow><mn>77</mn></mfrac></math>) = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>26</mn><mn>77</mn></mfrac></math></p>",
                    solution_hi: "<p>32.(c) <strong>तर्क :-</strong> (<math style=\"font-family:Verdana\" display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2309;&#2306;&#2358;</mi><mo>&#215;</mo><mn>2</mn></mrow><mrow><mi>&#2361;</mi><mi>&#2352;</mi></mrow></mfrac></math>)<br>(<math display=\"inline\"><mfrac><mrow><mn>17</mn></mrow><mrow><mn>41</mn></mrow></mfrac></math> , <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>34</mn><mn>41</mn></mfrac></math>) :- (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>17</mn><mo>&#215;</mo><mn>2</mn></mrow><mn>41</mn></mfrac></math>) = (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>34</mn><mn>41</mn></mfrac></math>)<br>(<math display=\"inline\"><mfrac><mrow><mn>11</mn></mrow><mrow><mn>85</mn></mrow></mfrac></math> , 2285) :- (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>11</mn><mo>&#215;</mo><mn>2</mn></mrow><mn>85</mn></mfrac></math>) = (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>22</mn><mn>85</mn></mfrac></math>)<br>इसी प्रकार,<br>(<math display=\"inline\"><mfrac><mrow><mn>13</mn></mrow><mrow><mn>77</mn></mrow></mfrac></math> , ?) :- (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>13</mn><mo>&#215;</mo><mn>2</mn></mrow><mn>77</mn></mfrac></math>) = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>26</mn><mn>77</mn></mfrac></math></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "33",
                    section: "6",
                    question_en: "<p>33. Identify the figure given in the options which when put in place of ? will logically<br>complete the series.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1735116564438.png\" alt=\"rId45\" width=\"366\" height=\"84\"></p>",
                    question_hi: "<p>33. विकल्पों में दी गई उस आकृति को पहचानिए जिसे प्रश्न चिह्न (?) के स्थान पर रखने पर श्रृंखला तार्किक रूप से पूरी हो जाएगी। <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1735116564438.png\" alt=\"rId45\" width=\"366\" height=\"84\"></p>",
                    options_en: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1735116564543.png\" alt=\"rId46\" width=\"78\" height=\"77\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1735116564642.png\" alt=\"rId47\" width=\"78\" height=\"77\"></p>", 
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1735116564737.png\" alt=\"rId48\" width=\"78\" height=\"77\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1735116564840.png\" alt=\"rId49\" width=\"79\" height=\"83\"></p>"],
                    options_hi: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1735116564543.png\" alt=\"rId46\" width=\"78\" height=\"77\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1735116564642.png\" alt=\"rId47\" width=\"78\" height=\"77\"></p>",
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1735116564737.png\" alt=\"rId48\" width=\"79\" height=\"78\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1735116564840.png\" alt=\"rId49\" width=\"78\" height=\"82\"></p>"],
                    solution_en: "<p>33.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1735116564543.png\" alt=\"rId46\" width=\"78\" height=\"77\"></p>",
                    solution_hi: "<p>33.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1735116564543.png\" alt=\"rId46\" width=\"78\" height=\"77\"></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "34",
                    section: "6",
                    question_en: "<p>34. Select the set in which the numbers are related in the same way as are the numbers of&nbsp;the given sets.<br>(<strong>NOTE </strong>: Operations should be performed on the whole numbers, without breaking<br>down the numbers into its constituent digits. E.g., 13 &ndash; Operations on 13 such as<br>adding/subtracting/multiplying etc. to 13 can be performed. Breaking down 13 into 1<br>and 3 and then performing mathematical operations on 1 and 3 is not allowed.)<br>(180, 45, 90)<br>(128, 32, 64)</p>",
                    question_hi: "<p>34. उस समुचय का चयन कीजिए जिसमें संख्याएं ठीक उसी प्रकार संबंधित हैं जिस प्रकार दिए गए<br>समुचयों की संख्याएं संबंधित हैं।<br>(<strong>ध्यान दें : </strong>संख्या अंको उनके घटक अंकों में अलग-अलग किए बिना पूर्ण संख्याओं पर संक्रियाएं की<br>जानी चाहिए। उदाहरण के लिए संख्या 13 - संख्या 13 पर संक्रियाएं जैसे 13 को जोड़ना/घटाना/गुणा<br>करना आदि किया जा सकता है। 13 को 1 और 3 में अलग-अलग करने की और फिर 1 और 3 पर<br>गणितीय संक्रियाएं करने की अनुमति नहीं है।)<br>(180, 45, 90)<br>(128, 32, 64)</p>",
                    options_en: ["<p>(224, 56, 112)</p>", "<p>(186, 46, 93)</p>", 
                                "<p>(248, 60, 124)</p>", "<p>(170, 43, 85)</p>"],
                    options_hi: ["<p>(224, 56, 112)</p>", "<p>(186, 46, 93)</p>",
                                "<p>(248, 60, 124)</p>", "<p>(170, 43, 85)</p>"],
                    solution_en: "<p>34.(a) <strong>Logic :- </strong>(1st number + 3rd number) <math display=\"inline\"><mo>&#247;</mo></math> 6 = 2nd number<br>(180 , 45 ,90) :- (180 + 90) <math display=\"inline\"><mo>&#247;</mo></math> 6 &rArr; (270) &divide; 6 = 45<br>(128, 32, 64) :- (128 + 64) <math display=\"inline\"><mo>&#247;</mo></math> 6 &rArr; (192) &divide; 6 = 32<br>Similarly,<br>(224, 56, 112) :- (224 + 112) <math display=\"inline\"><mo>&#247;</mo></math> 6 &rArr; (336) &divide; 6 = 56</p>",
                    solution_hi: "<p>34.(a) <strong>तर्क :-</strong> (पहली संख्या + तीसरी संख्या) <math display=\"inline\"><mo>&#247;</mo></math> 6 = दूसरी संख्या<br>(180 , 45 ,90) :- (180 + 90) <math display=\"inline\"><mo>&#247;</mo></math> 6 &rArr; (270) &divide; 6 = 45<br>(128, 32, 64) :- (128 + 64) <math display=\"inline\"><mo>&#247;</mo></math> 6 &rArr; (192) &divide; 6 = 32<br>इसी प्रकार,<br>(224, 56, 112) :- (224 + 112) <math display=\"inline\"><mo>&#247;</mo></math> 6 &rArr; (336) &divide; 6 = 56</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "35",
                    section: "6",
                    question_en: "<p>35. In a certain code language<br>A + B means &lsquo;A is the mother of B&rsquo;<br>A &ndash; B means &lsquo;A is the brother of B&rsquo;<br>A &times; B means &lsquo;A is the wife of B&rsquo;<br>A &divide; B means &lsquo;A is the father of B&rsquo;<br>Based on the above, how is G related to K if &lsquo;G + J &divide; H &times; I &minus; K&rsquo; ?</p>",
                    question_hi: "<p>35. एक निश्चित कूट भाषा में<br>A + B का अर्थ A, B की मां है<br>A &ndash; B का अर्थ A, B का भाई है<br>A &times; B का अर्थ A, B की पत्नी है<br>A &divide; B का अर्थ A, B का पिता है<br>उपरोक्त केआधार पर, यदि G + J &divide; H &times; I - K है, तो G का K से क्या संबंध है ?</p>",
                    options_en: ["<p>Brother&rsquo;s wife&rsquo;s sister</p>", "<p>Brother&rsquo;s wife&rsquo;s mother</p>", 
                                "<p>Brother&rsquo;s wife&rsquo;s father&rsquo;s mother</p>", "<p>Brother&rsquo;s wife&rsquo;s father&rsquo;s sister</p>"],
                    options_hi: ["<p>भाई की पत्नी की बहन</p>", "<p>भाई की पत्नी की मां</p>",
                                "<p>भाई की पत्नी के पिता की मां</p>", "<p>भाई की पत्नी के पिता की बहन</p>"],
                    solution_en: "<p>35.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1735116564948.png\" alt=\"rId50\" width=\"177\" height=\"162\"><br>G is the mother of the father of K&rsquo;s brother\'s wife.</p>",
                    solution_hi: "<p>35.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1735116564948.png\" alt=\"rId50\" width=\"177\" height=\"162\"><br>G, K के भाई की पत्नी के पिता की माँ है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "36",
                    section: "6",
                    question_en: "<p>36. Which of the following numbers will replace the question mark (?) in the given series ?<br>7 13 39 46 138 146 438 447 ?</p>",
                    question_hi: "<p>36. निम्नलिखित में से कौन-सी संख्या दी गई श्रृंखला में प्रश्न चिह्न (?) के स्थान पर आएगी ?<br>7 13 39 46 138 146 438 447 ?</p>",
                    options_en: ["<p>1138</p>", "<p>1146</p>", 
                                "<p>1341</p>", "<p>1247</p>"],
                    options_hi: ["<p>1138</p>", "<p>1146</p>",
                                "<p>1341</p>", "<p>1247</p>"],
                    solution_en: "<p>36.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1735116565119.png\" alt=\"rId51\" width=\"378\" height=\"48\"></p>",
                    solution_hi: "<p>36.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1735116565119.png\" alt=\"rId51\" width=\"378\" height=\"48\"></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "37",
                    section: "6",
                    question_en: "<p>37. Three statements are followed by conclusions numbered I, II. You have to consider&nbsp;these statements to be true, even if they seem to be at variance with commonly known&nbsp;facts. Decide which of the given conclusions logically follow/s from the given statement.<br><strong>Statements :</strong><br>All dresses are clouds.<br>Some clouds are tables.<br>Some tables are stones.<br><strong>Conclusion (I) : </strong>All stones are dresses.<br><strong>Conclusion (II) : </strong>Some dresses are tables.</p>",
                    question_hi: "<p>37. तीन कथनों के बाद I, II क्रमांकित निष्कर्ष दिए गए हैं। आपको इन कथनों को सत्य मानना होगा, चाहे वे&nbsp;समान्यतः ज्ञात तथ्यों से अलग प्रतीत होते हों। आपको निश्चय करना है कि कौन-सा/कौन-से निष्कर्ष&nbsp;तार्किक रूप से दिए गए कथन/कथनों के अनुसार है/हैं।<br><strong>कथन :</strong><br>सभी पोशाक, बादल हैं।<br>कुछ बादल, मेज हैं।<br>कुछ मेज, पत्थर हैं।<br><strong>निष्कर्ष (I) : </strong>सभी पत्थर, पोशाक हैं।<br><strong>निष्कर्ष (II) :</strong> कुछ पोशाक, मेज हैं।</p>",
                    options_en: ["<p>Only conclusion (II) follows.</p>", "<p>Neither conclusion (I) nor (II) follows.</p>", 
                                "<p>Both conclusions (I) and (II) follow.</p>", "<p>Only conclusion (I) follows.</p>"],
                    options_hi: ["<p>केवल निष्कर्ष (II) कथनों के अनुसार है।</p>", "<p>न तो निष्कर्ष (I) और न ही (II) कथनों के अनुसार है।</p>",
                                "<p>दोनों निष्कर्ष (I) और (II) कथनों के अनुसार हैं।</p>", "<p>केवल निष्कर्ष (I) कथनों के अनुसार है।</p>"],
                    solution_en: "<p>37.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1735116565334.png\" alt=\"rId52\" width=\"253\" height=\"74\"><br>Neither conclusion I nor II follows</p>",
                    solution_hi: "<p>37.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1735116565655.png\" alt=\"rId53\" width=\"290\" height=\"85\"><br>न तो निष्कर्ष I और न ही II अनुसरण करता है</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "38",
                    section: "6",
                    question_en: "<p>38. What should come in place of the question mark (?) to complete the following letter&nbsp;cluster series ?<br>UME, OPG, ISJ, EVN, ?</p>",
                    question_hi: "<p>38. निम्नलिखित अक्षर-समूह (शृंखला को पूरा करने के लिए प्रश्न चिह्न (?) के स्थान पर क्या आएगा ?<br>UME, OPG, ISJ, EVN, ?</p>",
                    options_en: ["<p>BYS</p>", "<p>AXS</p>", 
                                "<p>AYS</p>", "<p>AYR</p>"],
                    options_hi: ["<p>BYS</p>", "<p>AXS</p>",
                                "<p>AYS</p>", "<p>AYR</p>"],
                    solution_en: "<p>38.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1735116566003.png\" alt=\"rId54\" width=\"314\" height=\"96\"></p>",
                    solution_hi: "<p>38.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1735116566003.png\" alt=\"rId54\" width=\"314\" height=\"96\"></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "39",
                    section: "6",
                    question_en: "<p>39. Select the correct mirror image of the given figure when the mirror is placed at MN. <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1735116566291.png\" alt=\"rId55\" width=\"115\" height=\"111\"></p>",
                    question_hi: "<p>39. जब दर्पण को MN पर रखा जाता हैतो दी गई आकृति के सही दर्पण प्रतिबिंब का चयन कीजिए। <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1735116566291.png\" alt=\"rId55\" width=\"115\" height=\"111\"></p>",
                    options_en: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1735116566378.png\" alt=\"rId56\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1735116566472.png\" alt=\"rId57\"></p>", 
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1735116566582.png\" alt=\"rId58\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1735116566664.png\" alt=\"rId59\"></p>"],
                    options_hi: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1735116566378.png\" alt=\"rId56\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1735116566472.png\" alt=\"rId57\"></p>",
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1735116566582.png\" alt=\"rId58\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1735116566664.png\" alt=\"rId59\"></p>"],
                    solution_en: "<p>39.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1735116566472.png\" alt=\"rId57\"></p>",
                    solution_hi: "<p>39.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1735116566472.png\" alt=\"rId57\"></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "40",
                    section: "6",
                    question_en: "<p>40. Which of the following numbers will replace the question mark (?) in the given series ?<br>[<math display=\"inline\"><mfrac><mrow><mn>37</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math>], [<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>32</mn><mn>5</mn></mfrac></math>], [<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>27</mn><mn>5</mn></mfrac></math>], [<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>22</mn><mn>5</mn></mfrac></math>], [<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>17</mn><mn>5</mn></mfrac></math>] ?</p>",
                    question_hi: "<p>40. निम्नलिखित में से कौन सी संख्या दी गई श्रृंखला में प्रश्न चिह्न (?) के स्थान पर आएगी ?<br>[<math display=\"inline\"><mfrac><mrow><mn>37</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math>], [<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>32</mn><mn>5</mn></mfrac></math>], [<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>27</mn><mn>5</mn></mfrac></math>], [<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>22</mn><mn>5</mn></mfrac></math>], [<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>17</mn><mn>5</mn></mfrac></math>]</p>",
                    options_en: ["<p><math display=\"inline\"><mo>[</mo><mfrac><mrow><mn>14</mn></mrow><mrow><mn>5</mn></mrow></mfrac><mo>]</mo></math></p>", "<p><math display=\"inline\"><mo>[</mo><mfrac><mrow><mn>9</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> ]</p>", 
                                "<p><math display=\"inline\"><mo>[</mo><mfrac><mrow><mn>16</mn></mrow><mrow><mn>5</mn></mrow></mfrac><mo>]</mo></math></p>", "<p><math display=\"inline\"><mo>[</mo><mfrac><mrow><mn>12</mn></mrow><mrow><mn>5</mn></mrow></mfrac><mo>]</mo></math></p>"],
                    options_hi: ["<p><math display=\"inline\"><mo>[</mo><mfrac><mrow><mn>14</mn></mrow><mrow><mn>5</mn></mrow></mfrac><mo>]</mo></math></p>", "<p><math display=\"inline\"><mo>[</mo><mfrac><mrow><mn>9</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> ]</p>",
                                "<p><math display=\"inline\"><mo>[</mo><mfrac><mrow><mn>16</mn></mrow><mrow><mn>5</mn></mrow></mfrac><mo>]</mo></math></p>", "<p><math display=\"inline\"><mo>[</mo><mfrac><mrow><mn>12</mn></mrow><mrow><mn>5</mn></mrow></mfrac><mo>]</mo></math></p>"],
                    solution_en: "<p>40.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1735116566856.png\" alt=\"rId60\" width=\"235\" height=\"79\"></p>",
                    solution_hi: "<p>40.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1735116566856.png\" alt=\"rId60\" width=\"235\" height=\"79\"></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "41",
                    section: "6",
                    question_en: "<p>41. Select the correct mirror image of the given figure when the mirror is placed at MN.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1735116566993.png\" alt=\"rId61\" width=\"106\" height=\"109\"></p>",
                    question_hi: "<p>41. जब दर्पण को MN पर रखा जाता है तो दी गई आकृति के सही दर्पण प्रतिबिंब का चयन कीजिए।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1735116566993.png\" alt=\"rId61\" width=\"106\" height=\"109\"></p>",
                    options_en: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1735116567085.png\" alt=\"rId62\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1735116567174.png\" alt=\"rId63\"></p>", 
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1735116567270.png\" alt=\"rId64\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1735116567401.png\" alt=\"rId65\"></p>"],
                    options_hi: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1735116567085.png\" alt=\"rId62\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1735116567174.png\" alt=\"rId63\"></p>",
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1735116567270.png\" alt=\"rId64\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1735116567401.png\" alt=\"rId65\"></p>"],
                    solution_en: "<p>41.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1735116567174.png\" alt=\"rId63\"></p>",
                    solution_hi: "<p>41.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1735116567174.png\" alt=\"rId63\"></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "42",
                    section: "6",
                    question_en: "<p>42. What should come in place of the question mark (?) in the given series based on the English alphabetical order ?&nbsp;<br>ACW, FHB, KMG, PRL,?</p>",
                    question_hi: "<p>42. अंग्रेजी वर्णमाला क्रम के आधार पर दी गई श्रृंखला में प्रश्न चिह्न &lsquo;?&rsquo; के स्थान पर क्या आना चाहिए ?&nbsp;<br>ACW, FHB, KMG, PRL, ?</p>",
                    options_en: ["<p>VQW</p>", "<p>UWQ</p>", 
                                "<p>UQW</p>", "<p>VWQ</p>"],
                    options_hi: ["<p>VQW</p>", "<p>UWQ</p>",
                                "<p>UQW</p>", "<p>VWQ</p>"],
                    solution_en: "<p>42.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1735116567606.png\" alt=\"rId66\" width=\"307\" height=\"90\"></p>",
                    solution_hi: "<p>42.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1735116567606.png\" alt=\"rId66\" width=\"307\" height=\"90\"></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "43",
                    section: "6",
                    question_en: "<p>43. STEM is related to \"HGVN\' in a certain way based on the English alphabetical order. In the same way, \"QUIT\" is related to JFRG\'. To which of the following is ACCOUNTABLE related, following the same logic ?</p>",
                    question_hi: "<p>43. अंग्रेजी वर्णमाला क्रम के आधार पर \'STEM\', \'HGVN\' से एक निश्चित प्रकार से संबंधित है। उसी प्रकार, \'QUIT\', \'UFRG\' से संबंधित है। समान तर्क का अनुसरण करते हुए, \'ACCOUNTABLE निम्नलिखित में से किससे संबंधित है ?</p>",
                    options_en: ["<p>ZXXLFMZGYOV</p>", "<p>ZXXLFMGZYOU</p>", 
                                "<p>ZXXLFMGZYOV</p>", "<p>ZXXFLMGZYOV</p>"],
                    options_hi: ["<p>ZXXLFMZGYOV</p>", "<p>ZXXLFMGZYOU</p>",
                                "<p>ZXXLFMGZYOV</p>", "<p>ZXXFLMGZYOV</p>"],
                    solution_en: "<p>43.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1735116567764.png\" alt=\"rId67\" width=\"90\" height=\"131\">&nbsp; &nbsp; &nbsp; ,&nbsp; &nbsp; <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1735116567892.png\" alt=\"rId68\" width=\"89\" height=\"129\"><br>Similarly,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1735116568047.png\" alt=\"rId69\" width=\"219\" height=\"117\"></p>",
                    solution_hi: "<p>43.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1735116568231.png\" alt=\"rId70\" width=\"88\" height=\"131\">&nbsp; &nbsp; ,&nbsp; &nbsp;<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1735116568376.png\" alt=\"rId71\" width=\"88\" height=\"131\"><br>इसी प्रकार,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1735116568650.png\" alt=\"rId72\" width=\"214\" height=\"117\"></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "44",
                    section: "6",
                    question_en: "<p>44. A paper folded and cut as shown below. How will it appear when unfolded ?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1735116568881.png\" alt=\"rId73\" width=\"267\" height=\"86\"></p>",
                    question_hi: "<p>44. नीचे दर्शाए गए अनुसार एक कागज को मोड़कर काटा जाता है। खोलने पर यह कैसा दिखाई देगा?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1735116568881.png\" alt=\"rId73\" width=\"267\" height=\"86\"></p>",
                    options_en: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1735116568983.png\" alt=\"rId74\" width=\"88\" height=\"88\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1735116569120.png\" alt=\"rId75\" width=\"89\" height=\"86\"></p>", 
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1735116569274.png\" alt=\"rId76\" width=\"88\" height=\"86\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1735116569377.png\" alt=\"rId77\" width=\"89\" height=\"91\"></p>"],
                    options_hi: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1735116568983.png\" alt=\"rId74\" width=\"88\" height=\"88\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1735116569120.png\" alt=\"rId75\" width=\"97\" height=\"93\"></p>",
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1735116569274.png\" alt=\"rId76\" width=\"88\" height=\"86\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1735116569377.png\" alt=\"rId77\" width=\"88\" height=\"90\"></p>"],
                    solution_en: "<p>44.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1735116569274.png\" alt=\"rId76\" width=\"88\" height=\"86\"></p>",
                    solution_hi: "<p>44.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1735116569274.png\" alt=\"rId76\" width=\"88\" height=\"86\"></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "45",
                    section: "6",
                    question_en: "<p>45. What should come in place of the question mark (?) in the given series ?<br>58, 73, 90, 109, ?</p>",
                    question_hi: "<p>45. दी गई श्रृंखला में प्रश्न चिह्न (?) के स्थान पर क्या आना चाहिए ?<br>58, 73, 90, 109, ?</p>",
                    options_en: ["<p>301</p>", "<p>310</p>", 
                                "<p>103</p>", "<p>130</p>"],
                    options_hi: ["<p>301</p>", "<p>310</p>",
                                "<p>103</p>", "<p>130</p>"],
                    solution_en: "<p>45.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1735116569557.png\" alt=\"rId78\" width=\"206\" height=\"74\"></p>",
                    solution_hi: "<p>45.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1735116569557.png\" alt=\"rId78\" width=\"206\" height=\"74\"></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "46",
                    section: "6",
                    question_en: "46. Select the word-pair that best represents a similar relationship to the one expressed in the pair of words given below.<br />(The words must be considered as meaningful English words and must not be related to each other based on the number of letters/number of consonants/vowels in the word.)<br />Trim : Hair",
                    question_hi: "<p>46. उस सही -युग्म का चयन कीजिए जो नीचे दिए गए शब्दों के युग्म में व्यक्त किए गए समान संबंध का सबसे अच्छा निरूपण करता है।<br>(शब्दों को अर्थपूर्ण हिंदी शब्दों के रूप में माना जाना चाहिए और उन्हें शब्द में अक्षरों की संख्या /व्यंजन/&nbsp;स्वरों की संख्या केआधार पर एक दूसरे से संबंधित नहीं किया जाना चाहिए।)<br>काटना : बाल</p>",
                    options_en: [" Harvest : Corn", " Pluck : Flower", 
                                " Prune : Hedge", " Clip : Clothes"],
                    options_hi: [" फसल : मक्का ", " तोड़ना : फूल ",
                                " छंटाई : झाड़ी ", " कतरना : कपड़े"],
                    solution_en: "<p>46.(c) A Hedge is pruned just as hair is trimmed.</p>",
                    solution_hi: "46.(c) जैसे बाल काटे जाते हैं वैसे ही झाड़ी की  छंटाई की जाती है।",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "47",
                    section: "6",
                    question_en: "<p>47. What will come in the place of the question mark (?) in the following equation, if &lsquo;+&rsquo; and are &lsquo;-&rsquo; interchanged and &lsquo;<math display=\"inline\"><mo>&#215;</mo></math>&rsquo; and &lsquo;&divide;&rsquo; are interchanged ?&nbsp;<br>216 <math display=\"inline\"><mo>&#215;</mo></math> 9 + 52 - 7 &divide; 7 = ?</p>",
                    question_hi: "<p>47. यदि &lsquo;+&rsquo;ओर &lsquo;-&rsquo; को आपस में बदल दिया जाए तथा &lsquo;<math display=\"inline\"><mo>&#215;</mo></math>&rsquo;और &lsquo;&divide;&rsquo; को आपस में बदल दिया जाए तो निम्नलिखित समीकरण में प्रश्न चिन्ह (?) के स्थान पर क्या आएगा ?<br>216 <math display=\"inline\"><mo>&#215;</mo></math> 9 + 52 - 7 &divide; 7 = ?</p>",
                    options_en: ["<p>21</p>", "<p>19</p>", 
                                "<p>20</p>", "<p>22</p>"],
                    options_hi: ["<p>21</p>", "<p>19</p>",
                                "<p>20</p>", "<p>22</p>"],
                    solution_en: "<p>47.(a) <strong>Given :- </strong>216 &times; 9 + 52 - 7 <math display=\"inline\"><mo>&#247;</mo></math> 7<br>As per given instruction after interchanging &lsquo;+&rsquo; and &lsquo;-&rsquo; and &lsquo;&times;&rsquo; and &lsquo;<math display=\"inline\"><mo>&#247;</mo></math>&rsquo; .<br>216 <math display=\"inline\"><mo>&#247;</mo></math> 9 - 52 + 7 &times; 7<br>24 - 52 + 49<br>24 - 3 = 21</p>",
                    solution_hi: "<p>47.(a) <strong>दिया गया :- </strong>216 &times; 9 + 52 - 7<math display=\"inline\"><mo>&#247;</mo></math> 7<br>दिए गए निर्देश के अनुसार \'+\' और \'-\' और \'&times;\' और \'<math display=\"inline\"><mo>&#247;</mo></math>\' को आपस में बदलने के बाद।<br>216 <math display=\"inline\"><mo>&#247;</mo></math> 9 - 52 + 7 &times; 7<br>24 - 52 + 49<br>24 - 3 = 21</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "48",
                    section: "6",
                    question_en: "<p>48. Select the option figure in which the given figure (X) is embedded as its part (rotation&nbsp;is NOT allowed).<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1735116569679.png\" alt=\"rId79\" width=\"101\" height=\"103\"></p>",
                    question_hi: "<p>48. उस विकल्प आकृति का चयन कीजिए जिसमें दी गई आकृति (X) उसके भाग के रूप में सन्निहित है&nbsp;(घूर्णन की अनुमति नहीं है)।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1735116569679.png\" alt=\"rId79\" width=\"101\" height=\"103\"></p>",
                    options_en: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1735116569778.png\" alt=\"rId80\" width=\"94\" height=\"97\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1735116569900.png\" alt=\"rId81\" width=\"93\" height=\"94\"></p>", 
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1735116570006.png\" alt=\"rId82\" width=\"93\" height=\"101\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1735116570121.png\" alt=\"rId83\" width=\"100\" height=\"100\"></p>"],
                    options_hi: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1735116569778.png\" alt=\"rId80\" width=\"90\" height=\"93\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1735116569900.png\" alt=\"rId81\" width=\"92\" height=\"93\"></p>",
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1735116570006.png\" alt=\"rId82\" width=\"94\" height=\"102\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1735116570121.png\" alt=\"rId83\" width=\"94\" height=\"94\"></p>"],
                    solution_en: "<p>48.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1735116570239.png\" alt=\"rId84\" width=\"102\" height=\"109\"></p>",
                    solution_hi: "<p>48.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1735116570239.png\" alt=\"rId84\" width=\"102\" height=\"109\"></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "49",
                    section: "6",
                    question_en: "49. In a certain code language, \'get enough sleep\' is coded as \'ab de jf\' and \'go get it\' is coded as \'jk ab gh\'. How is \'get\' coded in that language ?",
                    question_hi: "49. एक निश्चित कूट भाषा में, get enough sleep को \'ab de jf लिखा जाता है और \'go get it\' को \'jk ab gh\' लिखा जाता है। उस कूट भाषा में \'get\' को कैसे लिखा जाएगा ?",
                    options_en: [" jf", " ab", 
                                " de", " jk"],
                    options_hi: [" jf", " ab",
                                " de", " jk"],
                    solution_en: "49.(b) get enough sleep → ab de jf…….(i)<br />               go get it → jk ab gh…….(ii)<br />From (i) and (ii) ‘get’ and ‘ab’ are common. The code of ‘get’ = ‘ab’.",
                    solution_hi: "49.(b) get enough sleep → ab de jf…….(i)<br />               go get it → jk ab gh…….(ii)<br />(i) और (ii) से \'get\' और \'ab\' उभयनिष्ठ हैं। \'get\' का कूट = \'ab\'।",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "50",
                    section: "17",
                    question_en: "<p>50. Select the correct mirror image of the given figure when the mirror is placed at MN as shown below.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1735116570369.png\" alt=\"rId85\" width=\"105\" height=\"109\"></p>",
                    question_hi: "<p>50. दी गई आकृति के उस सही दर्पण प्रतिबिंब को चुनिए, जो नीचे दर्शाए गए अनुसार दर्पण को MN पर रखने पर बनेगा।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1735116570369.png\" alt=\"rId85\" width=\"105\" height=\"109\"></p>",
                    options_en: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1735116570466.png\" alt=\"rId86\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1735116570571.png\" alt=\"rId87\"></p>", 
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1735116570685.png\" alt=\"rId88\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1735116570789.png\" alt=\"rId89\"></p>"],
                    options_hi: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1735116570466.png\" alt=\"rId86\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1735116570571.png\" alt=\"rId87\"></p>",
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1735116570685.png\" alt=\"rId88\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1735116570789.png\" alt=\"rId89\"></p>"],
                    solution_en: "<p>50.(b) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1735116570571.png\" alt=\"rId87\"></p>",
                    solution_hi: "<p>50.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1735116570571.png\" alt=\"rId87\"></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "51",
                    section: "17",
                    question_en: "<p>51. If the intersection point of the equations 3p &minus; 2q + 23 = 0 and &minus;2p + q &minus; 13 = 0 on the&nbsp;graph is (p, q), then the value of (3p + q) is equal to:</p>",
                    question_hi: "<p>51. यदि ग्राफ पर समीकरण 3p &minus; 2q + 23 = 0 और &minus;2p + q &minus; 13 = 0 का प्रतिच्छेदन बिंदु (p, q) है, तो&nbsp;(3p + q) का मान ______ होगा।</p>",
                    options_en: ["<p>-1</p>", "<p>-2</p>", 
                                "<p>2</p>", "<p>1</p>"],
                    options_hi: ["<p>-1</p>", "<p>-2</p>",
                                "<p>2</p>", "<p>1</p>"],
                    solution_en: "<p>51.(b)<br>3p &minus; 2q + 23 = 0 -------(i)<br>&minus;2p + q &minus; 13 = 0 -------(ii)<br>Equation (i) + 2 &times; equation(ii) we get;<br>3p &minus; 2q + 23 + 2(&minus;2p + q &minus; 13) = 0<br>- p - 3 = 0<br>p = - 3<br>Put, p = - 3 in equation (i) we get;<br>3(-3) &minus; 2q + 23 = 0<br>&minus; 2q = -14<br>q = 7<br>Then, (3p + q) = 3 &times; - 3 + 7 = - 2</p>",
                    solution_hi: "<p>51.(b)<br>3p &minus; 2q + 23 = 0 -------(i)<br>&minus;2p + q &minus; 13 = 0 -------(ii)<br>समीकरण (i) + 2 &times; समीकरण (ii) हमें मिलता है;<br>3p &minus; 2q + 23 + 2(&minus;2p + q &minus; 13) = 0<br>- p - 3 = 0<br>p = - 3<br>समीकरण (i) में p = - 3 रखने पर हमें प्राप्त होता है;<br>3(-3) &minus; 2q + 23 = 0<br>&minus; 2q = -14<br>q = 7<br>तब , (3p + q) = 3 &times; - 3 + 7 = - 2</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "52",
                    section: "17",
                    question_en: "<p>52. The total income of three families is ₹72,000. Their expenditures are 80%, 85% and 75%, respectively. If the savings are in the ratio 8 : 9 : 20, then the income of the&nbsp;second family is:</p>",
                    question_hi: "<p>52. तीन परिवारों की कुल आय ₹72,000 है। उनके व्यय क्रमश: 80%, 85% और 75% हैं। यदि उनकी&nbsp;बचतों का अनुपात 8 : 9 : 20 है, तो दूसरे परिवार की आय कितनी है ?</p>",
                    options_en: ["<p>₹40,000</p>", "<p>₹32,000</p>", 
                                "<p>₹24,000</p>", "<p>₹16,000</p>"],
                    options_hi: ["<p>₹40,000</p>", "<p>₹32,000</p>",
                                "<p>₹24,000</p>", "<p>₹16,000</p>"],
                    solution_en: "<p>52.(c)<br>Let savings of three families be 8x , 9x and 20x<br>and income of first ,second and third family be <math display=\"inline\"><msub><mrow><mi>I</mi></mrow><mrow><mn>1</mn></mrow></msub></math> , I<sub>2</sub> and I<sub>3</sub> respectively,<br>Now, according to the question,<br><math display=\"inline\"><msub><mrow><mi>I</mi></mrow><mrow><mn>1</mn></mrow></msub></math>&times; 20% = 8x<br><math display=\"inline\"><msub><mrow><mi>I</mi></mrow><mrow><mn>1</mn></mrow></msub></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>8</mn><mi>x</mi></mrow><mn>20</mn></mfrac></math> &times; 100 = 40x<br>And, <math display=\"inline\"><msub><mrow><mi>I</mi></mrow><mrow><mn>2</mn></mrow></msub></math>&times; 15% = 9x<br><math display=\"inline\"><msub><mrow><mi>I</mi></mrow><mrow><mn>2</mn></mrow></msub></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>9</mn><mi>x</mi></mrow><mn>15</mn></mfrac></math> &times; 100 = 60x<br>And, <math display=\"inline\"><msub><mrow><mi>I</mi></mrow><mrow><mn>3</mn></mrow></msub></math>&times; 25% = 20x<br><math display=\"inline\"><msub><mrow><mi>I</mi></mrow><mrow><mn>3</mn></mrow></msub></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>20</mn><mi>x</mi></mrow><mn>25</mn></mfrac></math> &times; 100 = 80x<br>Now, (<math display=\"inline\"><msub><mrow><mi>I</mi></mrow><mrow><mn>1</mn></mrow></msub></math> + I<sub>2</sub> + I<sub>3</sub>) = 40x + 60x + 80x = 180x<br>180x = 72000 <math display=\"inline\"><mo>&#8658;</mo></math> x = 400<br>So, income of second family (60x) = ₹24,000</p>",
                    solution_hi: "<p>52.(c)<br>माना कि तीन परिवारों की बचत 8x, 9x और 20x है<br>और पहले, दूसरे और तीसरे परिवार की आय क्रमश: <math display=\"inline\"><msub><mrow><mi>I</mi></mrow><mrow><mn>1</mn></mrow></msub></math> , I<sub>2 </sub>और I<sub>3 </sub>है,<br>अब, प्रश्न के अनुसार,<br><math display=\"inline\"><msub><mrow><mi>I</mi></mrow><mrow><mn>1</mn></mrow></msub></math>&times; 20% = 8x<br><math display=\"inline\"><msub><mrow><mi>I</mi></mrow><mrow><mn>1</mn></mrow></msub></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>8</mn><mi>x</mi></mrow><mn>20</mn></mfrac></math> &times; 100 = 40x<br>और, <math display=\"inline\"><msub><mrow><mi>I</mi></mrow><mrow><mn>2</mn></mrow></msub></math>&times; 15% = 9x<br><math display=\"inline\"><msub><mrow><mi>I</mi></mrow><mrow><mn>2</mn></mrow></msub></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>9</mn><mi>x</mi></mrow><mn>15</mn></mfrac></math> &times; 100 = 60x<br>और, <math display=\"inline\"><msub><mrow><mi>I</mi></mrow><mrow><mn>3</mn></mrow></msub></math>&times; 25% = 20x<br><math display=\"inline\"><msub><mrow><mi>I</mi></mrow><mrow><mn>3</mn></mrow></msub></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>20</mn><mi>x</mi></mrow><mn>25</mn></mfrac></math> &times; 100 = 80x<br>अब, (<math display=\"inline\"><msub><mrow><mi>I</mi></mrow><mrow><mn>1</mn></mrow></msub></math> + I<sub>2</sub> + I<sub>3</sub>) = 40x + 60x + 80x = 180x<br>180x = 72000 <math display=\"inline\"><mo>&#8658;</mo></math> x = 400<br>तो, दूसरे परिवार की आय(60x) = ₹24,000</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "53",
                    section: "17",
                    question_en: "<p>53. Study the given bar-graph and answer the question that follows.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1735116570883.png\" alt=\"rId90\" width=\"364\" height=\"297\"> <br>How many students obtained more than 135 marks ?</p>",
                    question_hi: "<p>53. दिए गए दंड आलेख का अध्ययन कीजिए और नीचे दिए गए प्रश्न का उत्तर दीजिए।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1735116571004.png\" alt=\"rId91\" width=\"401\" height=\"324\"> <br>No. of Students = छात्रों की संख्या<br>Marks Obtained = प्राप्त अंक<br>कितने छात्रों ने 135 से अधिक अंक प्राप्त किए ?</p>",
                    options_en: ["<p>17</p>", "<p>19</p>", 
                                "<p>13</p>", "<p>18</p>"],
                    options_hi: ["<p>17</p>", "<p>19</p>",
                                "<p>13</p>", "<p>18</p>"],
                    solution_en: "<p>53.(d)<br>Required students = (8 + 5 + 4 + 1) = 18</p>",
                    solution_hi: "<p>53.(d)<br>आवश्यक विद्यार्थी = (8 + 5 + 4 + 1) = 18</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "54",
                    section: "17",
                    question_en: "<p>54. 30 men working 5 hours a day can do work in 18 days. In how many days will 20 men working 7 hours a day do the same work ?</p>",
                    question_hi: "<p>54. 30 आदमी प्रतिदिन 5 घंटे काम करके एक काम को 18 दिनों में पूरा कर सकते हैं। 20 आदमी प्रतिदिन 7 घंटे काम करके उसी काम को कितने दिनों में पूरा करेंगे?</p>",
                    options_en: ["<p>19<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math></p>", "<p>13<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math></p>", 
                                "<p>16 <math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math></p>", "<p>11<math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math></p>"],
                    options_hi: ["<p>19<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math></p>", "<p>13<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math></p>",
                                "<p>16 <math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math></p>", "<p>11<math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math></p>"],
                    solution_en: "<p>54.(a)<br><math display=\"inline\"><msub><mrow><mi>M</mi></mrow><mrow><mn>1</mn></mrow></msub><msub><mrow><mi>D</mi></mrow><mrow><mn>1</mn></mrow></msub><msub><mrow><mi>H</mi></mrow><mrow><mn>1</mn></mrow></msub></math> = M<sub>2</sub>D<sub>2</sub>H<sub>2</sub><br>30 &times; 18 &times; 5 = 20 &times; <math display=\"inline\"><msub><mrow><mi>D</mi></mrow><mrow><mn>2</mn></mrow></msub></math> &times; 7<br><math display=\"inline\"><msub><mrow><mi>D</mi></mrow><mrow><mn>2</mn></mrow></msub></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>30</mn><mo>&#215;</mo><mn>18</mn><mo>&#215;</mo><mn>5</mn></mrow><mrow><mn>20</mn><mo>&#215;</mo><mn>7</mn></mrow></mfrac><mo>=</mo><mfrac><mn>135</mn><mn>7</mn></mfrac><mo>=</mo><mn>19</mn><mfrac><mn>2</mn><mn>7</mn></mfrac></math> Days</p>",
                    solution_hi: "<p>54.(a)<br><math display=\"inline\"><msub><mrow><mi>M</mi></mrow><mrow><mn>1</mn></mrow></msub><msub><mrow><mi>D</mi></mrow><mrow><mn>1</mn></mrow></msub><msub><mrow><mi>H</mi></mrow><mrow><mn>1</mn></mrow></msub></math> = M<sub>2</sub>D<sub>2</sub>H<sub>2</sub><br>30 &times; 18 &times; 5 = 20 &times; <math display=\"inline\"><msub><mrow><mi>D</mi></mrow><mrow><mn>2</mn></mrow></msub></math> &times; 7<br><math display=\"inline\"><msub><mrow><mi>D</mi></mrow><mrow><mn>2</mn></mrow></msub></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>30</mn><mo>&#215;</mo><mn>18</mn><mo>&#215;</mo><mn>5</mn></mrow><mrow><mn>20</mn><mo>&#215;</mo><mn>7</mn></mrow></mfrac><mo>=</mo><mfrac><mn>135</mn><mn>7</mn></mfrac><mo>=</mo><mn>19</mn><mfrac><mn>2</mn><mn>7</mn></mfrac></math> दिन</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "55",
                    section: "17",
                    question_en: "<p>55. If a sum of ₹3,260 on simple interest amounts to ₹5,420 in 6 years, then what will this&nbsp;sum amount to in 4 years at the same rate of interest ?</p>",
                    question_hi: "<p>55. यदि साधारण ब्याज पर ₹3,260 की धनराशि 6 वर्ष में ₹5,420 हो जाती है, तो यह धनराशि समान ब्याज दर पर 4 वर्ष में कितनी होगी ?</p>",
                    options_en: ["<p>₹4,500</p>", "<p>₹3,700</p>", 
                                "<p>₹4,700</p>", "<p>₹3,900</p>"],
                    options_hi: ["<p>₹4,500</p>", "<p>₹3,700</p>",
                                "<p>₹4,700</p>", "<p>₹3,900</p>"],
                    solution_en: "<p>55.(c)<br>SI in 6 years = 5420 - 3260 = ₹2160<br>SI in 4 years = <math display=\"inline\"><mfrac><mrow><mn>2160</mn></mrow><mrow><mn>6</mn></mrow></mfrac></math> &times; 4 = ₹1440<br>Sum amount to in 4 years = 3260 + 1440 = ₹4700</p>",
                    solution_hi: "<p>55.(c)<br>6 वर्षों में साधारण ब्याज = 5420 - 3260 = ₹2160<br>4 वर्ष में साधारण ब्याज = <math display=\"inline\"><mfrac><mrow><mn>2160</mn></mrow><mrow><mn>6</mn></mrow></mfrac></math> &times; 4 = ₹1440<br>4 वर्ष में कुल राशि = 3260 + 1440 = ₹4700</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "56",
                    section: "17",
                    question_en: "<p>56. What is the value of the following expression ?<br>(24 <math display=\"inline\"><mo>&#247;</mo></math> 3 of <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>6</mn></mfrac></math>) &times; (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>5</mn></mfrac><mo>&#247;</mo><mfrac><mn>9</mn><mn>8</mn></mfrac><mo>&#215;</mo><mfrac><mn>3</mn><mn>5</mn></mfrac></math>) &divide; (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>4</mn><mo>&#160;</mo></mrow><mn>7</mn></mfrac><mo>&#247;</mo><mfrac><mn>3</mn><mn>14</mn></mfrac></math> of <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>14</mn></mfrac></math>)</p>",
                    question_hi: "<p>56. निम्नलिखित व्यंजक का मान ज्ञात कीजिए।<br>(24 <math display=\"inline\"><mo>&#247;</mo></math> 3 का <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>6</mn></mfrac></math>) &times; (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>5</mn></mfrac><mo>&#247;</mo><mfrac><mn>9</mn><mn>8</mn></mfrac><mo>&#215;</mo><mfrac><mn>3</mn><mn>5</mn></mfrac></math>) &divide; (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>4</mn><mo>&#160;</mo></mrow><mn>7</mn></mfrac><mo>&#247;</mo><mfrac><mn>3</mn><mn>14</mn></mfrac></math> का<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>14</mn></mfrac></math>)</p>",
                    options_en: ["<p><math display=\"inline\"><mfrac><mrow><mn>379</mn></mrow><mrow><mn>179</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>579</mn></mrow><mrow><mn>459</mn></mrow></mfrac></math></p>", 
                                "<p><math display=\"inline\"><mfrac><mrow><mn>363</mn></mrow><mrow><mn>553</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>288</mn></mrow><mrow><mn>175</mn></mrow></mfrac></math></p>"],
                    options_hi: ["<p><math display=\"inline\"><mfrac><mrow><mn>379</mn></mrow><mrow><mn>179</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>579</mn></mrow><mrow><mn>459</mn></mrow></mfrac></math></p>",
                                "<p><math display=\"inline\"><mfrac><mrow><mn>363</mn></mrow><mrow><mn>553</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>288</mn></mrow><mrow><mn>175</mn></mrow></mfrac></math></p>"],
                    solution_en: "<p>56.(d)<br>(24 <math display=\"inline\"><mo>&#247;</mo></math> 3 of <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>6</mn></mfrac></math>) &times; (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>5</mn></mfrac><mo>&#247;</mo><mfrac><mn>9</mn><mn>8</mn></mfrac><mo>&#215;</mo><mfrac><mn>3</mn><mn>5</mn></mfrac></math>) &divide; (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>4</mn><mo>&#160;</mo></mrow><mn>7</mn></mfrac><mo>&#247;</mo><mfrac><mn>3</mn><mn>14</mn></mfrac></math> of <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>14</mn></mfrac></math>)<br>( 24 <math display=\"inline\"><mo>&#247;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math>) &times; (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>5</mn></mfrac><mo>&#247;</mo><mfrac><mn>8</mn><mn>9</mn></mfrac><mo>&#215;</mo><mfrac><mn>3</mn><mn>5</mn></mfrac></math>) &divide; (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>7</mn></mfrac><mo>&#247;</mo><mfrac><mn>9</mn><mn>196</mn></mfrac></math>)<br>( 24 <math display=\"inline\"><mo>&#215;</mo></math> 2) &times; (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>32</mn><mn>75</mn></mfrac></math>) &divide; (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>7</mn></mfrac><mo>&#215;</mo><mfrac><mn>196</mn><mn>9</mn></mfrac></math>)<br>48 <math display=\"inline\"><mo>&#215;</mo></math> (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>32</mn><mn>75</mn></mfrac></math>) &divide; (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>112</mn><mn>9</mn></mfrac></math>)<br>48 <math display=\"inline\"><mo>&#215;</mo></math> (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>32</mn><mn>75</mn></mfrac></math>) &times; (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>9</mn><mn>112</mn></mfrac></math>)<br>48 <math display=\"inline\"><mo>&#215;</mo></math> (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>25</mn></mfrac></math>) &times; (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>7</mn></mfrac></math>) = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>288</mn><mn>175</mn></mfrac></math></p>",
                    solution_hi: "<p>56.(d)<br>(24 <math display=\"inline\"><mo>&#247;</mo></math> 3 का <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>6</mn></mfrac></math>) &times; (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>5</mn></mfrac><mo>&#247;</mo><mfrac><mn>9</mn><mn>8</mn></mfrac><mo>&#215;</mo><mfrac><mn>3</mn><mn>5</mn></mfrac></math>) &divide; (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>4</mn><mo>&#160;</mo></mrow><mn>7</mn></mfrac><mo>&#247;</mo><mfrac><mn>3</mn><mn>14</mn></mfrac></math> का<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>14</mn></mfrac></math>)<br>( 24 <math display=\"inline\"><mo>&#247;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math>) &times; (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>5</mn></mfrac><mo>&#247;</mo><mfrac><mn>8</mn><mn>9</mn></mfrac><mo>&#215;</mo><mfrac><mn>3</mn><mn>5</mn></mfrac></math>) &divide; (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>7</mn></mfrac><mo>&#247;</mo><mfrac><mn>9</mn><mn>196</mn></mfrac></math>)<br>( 24 <math display=\"inline\"><mo>&#215;</mo></math> 2) &times; (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>32</mn><mn>75</mn></mfrac></math>) &divide; (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>7</mn></mfrac><mo>&#215;</mo><mfrac><mn>196</mn><mn>9</mn></mfrac></math>)<br>48 <math display=\"inline\"><mo>&#215;</mo></math> (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>32</mn><mn>75</mn></mfrac></math>) &divide; (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>112</mn><mn>9</mn></mfrac></math>)<br>48 <math display=\"inline\"><mo>&#215;</mo></math> (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>32</mn><mn>75</mn></mfrac></math>) &times; (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>9</mn><mn>112</mn></mfrac></math>)<br>48 <math display=\"inline\"><mo>&#215;</mo></math> (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>25</mn></mfrac></math>) &times; (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>7</mn></mfrac></math>) = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>288</mn><mn>175</mn></mfrac></math></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "57",
                    section: "17",
                    question_en: "<p>57. The following table shows the number of employees working in four organizations during six years. Answer the questions that follow, <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1735116571112.png\" alt=\"rId92\" width=\"443\" height=\"130\"> <br>What is the average number of employees in organizations Y, Z and T in 2013 ?</p>",
                    question_hi: "<p>57. निम्नलिखित तालिका छह वर्षों के दौरान चार संगठनों में कार्यरत कर्मचारियों की संख्या दर्शाती है।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1735116571256.png\" alt=\"rId93\" width=\"320\" height=\"139\"> <br>2013 में संगठन Y, Z और T में कर्मचारियों की औसत संख्या कितनी है ?</p>",
                    options_en: ["<p>168</p>", "<p>166</p>", 
                                "<p>170</p>", "<p>172</p>"],
                    options_hi: ["<p>168</p>", "<p>166</p>",
                                "<p>170</p>", "<p>172</p>"],
                    solution_en: "<p>57.(b) <br>Required average = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>160</mn><mo>+</mo><mn>212</mn><mo>+</mo><mn>126</mn></mrow><mn>3</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>498</mn><mn>3</mn></mfrac></math> = 166</p>",
                    solution_hi: "<p>57.(b) <br>आवश्यक औसत = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>160</mn><mo>+</mo><mn>212</mn><mo>+</mo><mn>126</mn></mrow><mn>3</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>498</mn><mn>3</mn></mfrac></math> = 166</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "58",
                    section: "17",
                    question_en: "<p>58. A closed cylindrical tank of radius 1.5 m and height 3 m is made from a sheet of metal, how much sheet of metal is required to do it ?</p>",
                    question_hi: "<p>58. 1.5 मीटर त्रिज्या और 3 मीटर ऊँचाई वाली एक बंद बेलनाकार टंकी धातु की शीट से बनाई जाती है, इसे बनाने के लिए धातु की कितनी शीट की आवश्यकता होगी ?</p>",
                    options_en: ["<p>42.24 square metre</p>", "<p>24.24 square metre</p>", 
                                "<p>42.42 square metre</p>", "<p>24.42 square metre</p>"],
                    options_hi: ["<p>42.24 वर्ग मीटर</p>", "<p>24.24 वर्ग मीटर</p>",
                                "<p>42.42 वर्ग मीटर</p>", "<p>24.42 वर्ग मीटर</p>"],
                    solution_en: "<p>58.(c)<br>Cylindrical tank is closed <br>So, total surface area cylindrical tank = 2<math display=\"inline\"><mi>&#960;</mi><mi>r</mi><mo>(</mo><mi>r</mi><mo>+</mo><mi>h</mi><mo>)</mo></math><br>2 &times; <math display=\"inline\"><mfrac><mrow><mn>22</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> &times; 1.5 (1.5 + 3) = 42.42 m<sup>2</sup><br>Hence, required metal used = 42.42 m<sup>2</sup></p>",
                    solution_hi: "<p>58.(c)<br>बेलनाकार टैंक बंद है <br>तो, बेलनाकार टैंक का कुल सतह क्षेत्रफल = 2<math display=\"inline\"><mi>&#960;</mi><mi>r</mi><mo>(</mo><mi>r</mi><mo>+</mo><mi>h</mi><mo>)</mo></math><br>2 &times; <math display=\"inline\"><mfrac><mrow><mn>22</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> &times; 1.5 (1.5 + 3) = 42.42 m<sup>2</sup><br>अतः, प्रयुक्त आवश्यक धातु = 42.42 m<sup>2</sup></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "59",
                    section: "17",
                    question_en: "<p>59. PQ is the diameter of a circle with centre O. PT is a tangent touching the circle at P.<br>The secant QT intersects the circle at S. O and S are joined. If &ang; SOP = 104&deg;, what is<br>the measure (in degrees) of &ang;PTQ ?</p>",
                    question_hi: "<p>59. केंद्र O वाले एक वृत्त का व्यास PQ है। PT वृत्त को P पर स्पर्श करने वाली एक स्पर्श रेखा है। छेदक रेखा QT वृत्त को S पर काटती है। O और S जुड़े हुए हैं। यदि &ang; SOP = 104&deg; है, तो &ang;PTQ का माप (डिग्री में) क्या है ?</p>",
                    options_en: ["<p>45</p>", "<p>62</p>", 
                                "<p>76</p>", "<p>38</p>"],
                    options_hi: ["<p>45</p>", "<p>62</p>",
                                "<p>76</p>", "<p>38</p>"],
                    solution_en: "<p>59.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1735116571455.png\" alt=\"rId94\" width=\"151\" height=\"198\"><br>In <math display=\"inline\"><mi>&#916;</mi></math> SOQ<br>&ang;QSO + &ang;SQO = 104&deg;&nbsp; &nbsp; &nbsp; &nbsp; [exterior angle is equal to sum of opposite interior angle]<br>2&ang;SQO = 104&deg;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; [opposite angle of same side (OS = OQ = Radius)]<br>&ang;SQO = 52&deg;<br>Now, in right angle <math display=\"inline\"><mi>&#916;</mi></math> TPQ<br>52&deg; + 90&deg; + &ang;PTQ = 180&deg;<br>&ang;PTQ = 38&deg;</p>",
                    solution_hi: "<p>59.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1735116571455.png\" alt=\"rId94\" width=\"151\" height=\"198\"><br><math display=\"inline\"><mi>&#916;</mi></math> SOQ में<br>&ang;QSO + &ang;SQO = 104&deg;&nbsp; &nbsp; &nbsp; [बाहरी कोण विपरीत आंतरिक कोण के योग के बराबर है]<br>2&ang;SQO = 104&deg;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; [समान भुजा का विपरीत कोण (OS = OQ = त्रिज्या)]<br>&ang;SQO = 52&deg;<br>अब, समकोण <math display=\"inline\"><mi>&#916;</mi></math> TPQ में<br>52&deg; + 90&deg; + &ang;PTQ = 180&deg;<br>&ang;PTQ = 38&deg;</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "60",
                    section: "17",
                    question_en: "<p>60. If Ramu&rsquo;s salary is 30% less than Somu&rsquo;s salary, then by how much percent is Somu&rsquo;s salary more than Ramu&rsquo;s salary ?</p>",
                    question_hi: "<p>60. यदि रामू का वेतन सोमू के वेतन से 30% कम है, तो सोमू का वेतन रामू के वेतन से कितने प्रतिशत&nbsp;अधिक है ?</p>",
                    options_en: ["<p>45.47%</p>", "<p>34.56%</p>", 
                                "<p>42.67%</p>", "<p>42.86%</p>"],
                    options_hi: ["<p>45.47%</p>", "<p>34.56%</p>",
                                "<p>42.67%</p>", "<p>42.86%</p>"],
                    solution_en: "<p>60.(d)<br>Let salary of Somu = 100<br>Then, salary of Ramu = 70<br>Hence, required % = <math display=\"inline\"><mfrac><mrow><mn>30</mn></mrow><mrow><mn>70</mn></mrow></mfrac></math> &times; 100 = 42.86%</p>",
                    solution_hi: "<p>60.(d)<br>माना सोमू का वेतन = 100 है<br>तो, रामू का वेतन = 70<br>अतः, आवश्यक % = <math display=\"inline\"><mfrac><mrow><mn>30</mn></mrow><mrow><mn>70</mn></mrow></mfrac></math> &times; 100 = 42.86%</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "61",
                    section: "17",
                    question_en: "<p>61. PQ and RS are two chords of a circle such that PQ = 12 cm and RS = 20 cm and PQ is parallel to RS. If the distance between PQ and RS is 4 cm, find the diameter of the&nbsp;circle.</p>",
                    question_hi: "<p>61. PQ और RS एक वृत्त की दो जीवाएँ इस प्रकार हैं कि PQ = 12 cm और RS = 20 cm है और PQ, RS के समानांतर है। यदि PQ और RS के बीच की दूरी 4 cm है, तो वृत्त का व्यास ज्ञात कीजिए।</p>",
                    options_en: ["<p>4 <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>34</mn></msqrt></math> cm</p>", "<p>2<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>34</mn></msqrt></math> cm</p>", 
                                "<p>3<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>34</mn></msqrt></math> cm</p>", "<p>6<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>34</mn></msqrt></math> cm</p>"],
                    options_hi: ["<p>4 <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>34</mn></msqrt></math> cm</p>", "<p>2<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>34</mn></msqrt></math> cm</p>",
                                "<p>3<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>34</mn></msqrt></math> cm</p>", "<p>6<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>34</mn></msqrt></math> cm</p>"],
                    solution_en: "<p>61.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1735116571645.png\" alt=\"rId95\" width=\"203\" height=\"188\"><br>OP<sup>2</sup> = <math display=\"inline\"><mi>x</mi></math><sup>2</sup> + 36 --------(i)<br>OR<sup>2</sup> = (4 - <math display=\"inline\"><mi>x</mi></math>)<sup>2</sup> + 100<br>OP = OR (Radius)<br>Then, <math display=\"inline\"><mi>x</mi></math><sup>2</sup> + 36 = (4 - x)<sup>2</sup> + 100<br><math display=\"inline\"><mi>x</mi></math><sup>2</sup> + 36 = 16 + x<sup>2</sup> - 8x + 100<br>-8<math display=\"inline\"><mi>x</mi></math> = - 80 <br><math display=\"inline\"><mi>x</mi></math> = 10<br>Putting x = 10 in equation (i) we get;<br>OP<sup>2</sup> = 100 + 36 = 136<br>OP = <math display=\"inline\"><msqrt><mn>136</mn></msqrt></math> = 2<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>34</mn></msqrt></math> cm<br>Diameter = 2OP = 4<math display=\"inline\"><msqrt><mn>34</mn></msqrt></math> cm</p>",
                    solution_hi: "<p>61.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1735116571645.png\" alt=\"rId95\" width=\"203\" height=\"188\"><br>OP<sup>2</sup> = <math display=\"inline\"><mi>x</mi></math><sup>2</sup> + 36 --------(i)<br>OR<sup>2</sup> = (4 - <math display=\"inline\"><mi>x</mi></math>)<sup>2</sup> + 100<br>OP = OR (त्रिज्या)<br>फिर, <math display=\"inline\"><mi>x</mi></math><sup>2</sup> + 36 = (4 - x)<sup>2</sup> + 100<br><math display=\"inline\"><mi>x</mi></math><sup>2</sup> + 36 = 16 + x<sup>2</sup> - 8x + 100<br>-8<math display=\"inline\"><mi>x</mi></math> = - 80 <br><math display=\"inline\"><mi>x</mi></math> = 10<br>समीकरण (i) में <math display=\"inline\"><mi>x</mi></math> = 10 रखने पर हमें प्राप्त होता है;<br>OP2 = 100 + 36 = 136<br>OP = <math display=\"inline\"><msqrt><mn>136</mn></msqrt></math> = 2<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>34</mn></msqrt></math> cm<br>व्यास = 2OP = 4<math display=\"inline\"><msqrt><mn>34</mn></msqrt></math> cm</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "62",
                    section: "17",
                    question_en: "<p>62. If tan <math display=\"inline\"><mi>&#952;</mi></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>4</mn></mfrac></math>, then the value of <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>4</mn><mo>&#160;</mo><mi>sin</mi><mi>&#952;</mi><mo>-</mo><mn>3</mn><mo>&#160;</mo><mi>cos</mi><mo>&#160;</mo><mi>&#952;</mi></mrow><mrow><mn>4</mn><mo>&#160;</mo><mi>sin</mi><mi>&#952;</mi><mo>+</mo><mn>3</mn><mo>&#160;</mo><mi>cos</mi><mi>&#952;</mi></mrow></mfrac></math> is equal to :</p>",
                    question_hi: "<p>62. यदि tan <math display=\"inline\"><mi>&#952;</mi></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>4</mn></mfrac></math>&nbsp;है , तो&nbsp; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>4</mn><mo>&#160;</mo><mi>sin</mi><mi>&#952;</mi><mo>-</mo><mn>3</mn><mo>&#160;</mo><mi>cos</mi><mo>&#160;</mo><mi>&#952;</mi></mrow><mrow><mn>4</mn><mo>&#160;</mo><mi>sin</mi><mi>&#952;</mi><mo>+</mo><mn>3</mn><mo>&#160;</mo><mi>cos</mi><mi>&#952;</mi></mrow></mfrac></math>का मान ज्ञात कीजिए |</p>",
                    options_en: ["<p><math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>", 
                                "<p><math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math></p>"],
                    options_hi: ["<p><math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>",
                                "<p><math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math></p>"],
                    solution_en: "<p>62.(c)<br>tan <math display=\"inline\"><mi>&#952;</mi></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>sin</mi><mi>&#952;</mi></mrow><mrow><mi>cos</mi><mi>&#952;</mi></mrow></mfrac><mo>=</mo><mfrac><mn>7</mn><mn>4</mn></mfrac></math><br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>4</mn><mo>&#215;</mo><mn>7</mn><mo>-</mo><mn>3</mn><mo>&#215;</mo><mn>4</mn></mrow><mrow><mn>4</mn><mo>&#215;</mo><mn>7</mn><mo>+</mo><mn>3</mn><mo>&#215;</mo><mn>4</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>16</mn><mn>40</mn></mfrac><mo>=</mo><mfrac><mn>2</mn><mn>5</mn></mfrac></math></p>",
                    solution_hi: "<p>62.(c)<br>tan <math display=\"inline\"><mi>&#952;</mi></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>sin</mi><mi>&#952;</mi></mrow><mrow><mi>cos</mi><mi>&#952;</mi></mrow></mfrac><mo>=</mo><mfrac><mn>7</mn><mn>4</mn></mfrac></math><br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>4</mn><mo>&#215;</mo><mn>7</mn><mo>-</mo><mn>3</mn><mo>&#215;</mo><mn>4</mn></mrow><mrow><mn>4</mn><mo>&#215;</mo><mn>7</mn><mo>+</mo><mn>3</mn><mo>&#215;</mo><mn>4</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>16</mn><mn>40</mn></mfrac><mo>=</mo><mfrac><mn>2</mn><mn>5</mn></mfrac></math></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "63",
                    section: "17",
                    question_en: "<p>63. △XYZ is similar to △PQR. If the ratio of the perimeter of △XYZ to the perimeter of<br>△PQR is 16 : 9 and PQ = 3.6 cm, then what is the length (in cm) of XY ?</p>",
                    question_hi: "<p>63. △XYZ, △PQR के समरूप है। यदि △XYZ के परिमाप और △PQR के परिमाप का अनुपात 16 : 9 है तथा PQ = 3.6 cm है, तो XY की लंबाई (cm में) कितनी है ?</p>",
                    options_en: ["<p>3<math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math></p>", "<p>2<math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math></p>", 
                                "<p>6<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math></p>", "<p>4<math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math></p>"],
                    options_hi: ["<p>3 <math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math></p>", "<p>2 <math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math></p>",
                                "<p>6 <math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math></p>", "<p>4 <math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math></p>"],
                    solution_en: "<p>63.(c)<br>△XYZ ~ △PQR<br><math display=\"inline\"><mfrac><mrow><mi>p</mi><mi>e</mi><mi>r</mi><mi>i</mi><mi>m</mi><mi>e</mi><mi>t</mi><mi>e</mi><mi>r</mi><mi>&#160;</mi><mi>o</mi><mi>f</mi><mi>&#160;</mi><mo>&#9651;</mo><mi>X</mi><mi>Y</mi><mi>Z</mi><mi>&#160;</mi></mrow><mrow><mi>p</mi><mi>e</mi><mi>r</mi><mi>i</mi><mi>m</mi><mi>e</mi><mi>t</mi><mi>e</mi><mi>r</mi><mi>&#160;</mi><mi>o</mi><mi>f</mi><mi>&#160;</mi><mo>&#9651;</mo><mi>P</mi><mi>Q</mi><mi>R</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>X</mi><mi>Y</mi></mrow><mrow><mi>P</mi><mi>Q</mi></mrow></mfrac></math><br><math display=\"inline\"><mfrac><mrow><mi>X</mi><mi>Y</mi></mrow><mrow><mn>3</mn><mo>.</mo><mn>6</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>16</mn><mn>9</mn></mfrac></math>&rArr; XY = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>64</mn><mn>10</mn></mfrac><mo>=</mo><mn>6</mn><mfrac><mn>2</mn><mn>5</mn></mfrac></math></p>",
                    solution_hi: "<p>63.(c)<br>△XYZ ~ △PQR<br><math style=\"font-family:Verdana\" display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>&#9651;</mo><mi>X</mi><mi>Y</mi><mi>Z</mi><mo>&#160;</mo><mi>&#2325;&#2366;</mi><mo>&#160;</mo><mi>&#2346;&#2352;&#2367;&#2350;&#2366;&#2346;</mi><mo>&#160;</mo></mrow><mrow><mo>&#9651;</mo><mi>P</mi><mi>Q</mi><mi>R</mi><mi>&#160;</mi><mo>&#160;</mo><mi>&#2325;&#2366;</mi><mo>&#160;</mo><mi>&#2346;&#2352;&#2367;&#2350;&#2366;&#2346;</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>X</mi><mi>Y</mi></mrow><mrow><mi>P</mi><mi>Q</mi></mrow></mfrac></math><br><math display=\"inline\"><mfrac><mrow><mi>X</mi><mi>Y</mi></mrow><mrow><mn>3</mn><mo>.</mo><mn>6</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>16</mn><mn>9</mn></mfrac></math>&rArr; XY = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>64</mn><mn>10</mn></mfrac><mo>=</mo><mn>6</mn><mfrac><mn>2</mn><mn>5</mn></mfrac></math></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "64",
                    section: "17",
                    question_en: "<p>64. Simply the following expression.<br>469 <math display=\"inline\"><mo>&#247;</mo></math> [270 &divide; (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>7</mn></mfrac></math>&times; 35) + (19 &times; 3) - (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>21</mn><mn>2</mn></mfrac><mo>-</mo><mfrac><mn>5</mn><mn>2</mn></mfrac></math>)]</p>",
                    question_hi: "<p>64. निम्नलिखित व्यंजक को सरल कीजिए। <br>469 <math display=\"inline\"><mo>&#247;</mo></math> [270 &divide; (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>7</mn></mfrac></math>&times; 35) + (19 &times; 3) - (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>21</mn><mn>2</mn></mfrac><mo>-</mo><mfrac><mn>5</mn><mn>2</mn></mfrac></math>)]</p>",
                    options_en: ["<p>7</p>", "<p>11</p>", 
                                "<p>13</p>", "<p>9</p>"],
                    options_hi: ["<p>7</p>", "<p>11</p>",
                                "<p>13</p>", "<p>9</p>"],
                    solution_en: "<p>64.(a)<br>469 <math display=\"inline\"><mo>&#247;</mo></math> [270 &divide; (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>7</mn></mfrac></math>&times; 35) + (19 &times; 3) - (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>21</mn><mn>2</mn></mfrac><mo>-</mo><mfrac><mn>5</mn><mn>2</mn></mfrac></math>)]<br>469 <math display=\"inline\"><mo>&#247;</mo></math> [270 &divide; (15) + (57) - (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>16</mn><mn>2</mn></mfrac></math>)]<br>469 <math display=\"inline\"><mo>&#247;</mo></math> [18 + (57) - (8)]<br>469 <math display=\"inline\"><mo>&#247;</mo></math> [67] = 7</p>",
                    solution_hi: "<p>64.(a)<br>469 <math display=\"inline\"><mo>&#247;</mo></math> [270 &divide; (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>7</mn></mfrac></math>&times; 35) + (19 &times; 3) - (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>21</mn><mn>2</mn></mfrac><mo>-</mo><mfrac><mn>5</mn><mn>2</mn></mfrac></math>)]<br>469 <math display=\"inline\"><mo>&#247;</mo></math> [270 &divide; (15) + (57) - (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>16</mn><mn>2</mn></mfrac></math>)]<br>469 <math display=\"inline\"><mo>&#247;</mo></math> [18 + (57) - (8)]<br>469 <math display=\"inline\"><mo>&#247;</mo></math> [67] = 7</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "65",
                    section: "17",
                    question_en: "<p>65. Study the given bar-graph and answer the question that follows.<br>The bar-graph represents the number of boys and girls in different sections of class 12.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1735116572216.png\" alt=\"rId96\" width=\"425\" height=\"303\"> <br>The average number of girls in each section is:</p>",
                    question_hi: "<p>65. दिए गए दंड-आलेख का अध्ययन कीजिए और नीचे दिए गए प्रश्न का उत्तर दीजिए। दंड आलेख, का 12 (XII) के विभिन्न वर्ग में लड़कों और लड़कियों की संख्या को दर्शाता है। <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1735116572342.png\" alt=\"rId97\" width=\"479\" height=\"337\"> <br>Girls = लड़कियाँ<br>Boys = लड़के<br>प्रत्येक वर्ग में लड़कियों की औसत संख्या कितनी है ?</p>",
                    options_en: ["<p>42</p>", "<p>40</p>", 
                                "<p>43</p>", "<p>37</p>"],
                    options_hi: ["<p>42</p>", "<p>40</p>",
                                "<p>43</p>", "<p>37</p>"],
                    solution_en: "<p>65.(b)<br>Required average = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>34</mn><mo>+</mo><mn>41</mn><mo>+</mo><mn>40</mn><mo>+</mo><mn>39</mn><mo>+</mo><mn>45</mn><mo>+</mo><mn>41</mn></mrow><mn>6</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>240</mn><mn>6</mn></mfrac></math> = 40</p>",
                    solution_hi: "<p>65.(b)<br>आवश्यक औसत = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>34</mn><mo>+</mo><mn>41</mn><mo>+</mo><mn>40</mn><mo>+</mo><mn>39</mn><mo>+</mo><mn>45</mn><mo>+</mo><mn>41</mn></mrow><mn>6</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>240</mn><mn>6</mn></mfrac></math> = 40</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "66",
                    section: "17",
                    question_en: "<p>66. A part of the journey is covered in 22.5 minutes at a speed of 68 km/h and the remaining part in 12 minutes at a speed of 72 km/h. The total distance of the journey is (to the nearest integer):</p>",
                    question_hi: "<p>66. यात्रा के एक हिस्से को 68 km/h की चाल से 22.5 मिनट में और शेष हिस्से को 72 km/h की चाल से 12 मिनट में तय किया जाता है। यात्रा की कुल दूरी कितनी है (निकटतम पूर्णांक तक) ?</p>",
                    options_en: ["<p>35 km</p>", "<p>42 km</p>", 
                                "<p>38 km</p>", "<p>40 km</p>"],
                    options_hi: ["<p>35 km</p>", "<p>42 km</p>",
                                "<p>38 km</p>", "<p>40 km</p>"],
                    solution_en: "<p>66.(d) <br>Total distance = 68 &times; <math display=\"inline\"><mfrac><mrow><mn>22</mn><mo>.</mo><mn>5</mn></mrow><mrow><mn>60</mn></mrow></mfrac></math> + 72 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>12</mn><mn>60</mn></mfrac></math> = 25.5 + 14.4 = 40 km (approx)</p>",
                    solution_hi: "<p>66.(d) <br>कुल दूरी = 68 &times; <math display=\"inline\"><mfrac><mrow><mn>22</mn><mo>.</mo><mn>5</mn></mrow><mrow><mn>60</mn></mrow></mfrac></math> + 72 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>12</mn><mn>60</mn></mfrac></math> = 25.5 + 14.4 = 40 किमी (लगभग)</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "67",
                    section: "17",
                    question_en: "<p>67. The average salary of a group of 12 employees in an institution is ₹3,950 per month&nbsp;and that of another group of employees is ₹1,850. If the average salary of all employees is ₹2,150, then the total number of employees is:</p>",
                    question_hi: "<p>67. एक संस्थान में 12 कर्मचारियों के समूह का औसत वेतन ₹3,950 प्रति माह और कर्मचारियों के एक दूसरे समूह का औसत वेतन ₹1,850 प्रति माह है। यदि सभी कर्मचारियों का औसत वेतन ₹2,150 है, तो कर्मचारियों की कुल संख्या ज्ञात कीजिए।</p>",
                    options_en: ["<p>84</p>", "<p>100</p>", 
                                "<p>88</p>", "<p>72</p>"],
                    options_hi: ["<p>84</p>", "<p>100</p>",
                                "<p>88</p>", "<p>72</p>"],
                    solution_en: "<p>67.(a) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1735116572451.png\" alt=\"rId98\" width=\"203\" height=\"167\"><br><math display=\"inline\"><mfrac><mrow><mn>12</mn></mrow><mrow><mi>x</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>6</mn></mfrac></math> &rArr; x = 72<br>Total employees = (<math display=\"inline\"><mi>x</mi></math> + 12) = 84</p>",
                    solution_hi: "<p>67.(a) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1735116572590.png\" alt=\"rId99\" width=\"203\" height=\"164\"><br><math display=\"inline\"><mfrac><mrow><mn>12</mn></mrow><mrow><mi>x</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>6</mn></mfrac></math> &rArr; x = 72<br>कुल कर्मचारी = (<math display=\"inline\"><mi>x</mi></math> + 12) = 84</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "68",
                    section: "17",
                    question_en: "<p>68. Two cubes have their volumes in the ratio 1 : 125. Find the ratio of their surface areas.</p>",
                    question_hi: "<p>68. दो घनों के आयतनों का अनुपात 1 : 125 है। उनके पृष्ठीय क्षेत्रफलों का अनुपात ज्ञात करें।</p>",
                    options_en: ["<p>1 : 125</p>", "<p>1 : 25</p>", 
                                "<p>1 : 5</p>", "<p>25 : 1</p>"],
                    options_hi: ["<p>1 : 125</p>", "<p>1 : 25</p>",
                                "<p>1 : 5</p>", "<p>25 : 1</p>"],
                    solution_en: "<p>68.(b) <br>Sides ratio = <math display=\"inline\"><mroot><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mroot></math> : <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mroot><mn>125</mn><mn>3</mn></mroot></math> = 1 : 5<br>Surface area of cube = 6a<sup>2</sup> <br>Required ratio = 6 &times; (1)<sup>2</sup> : 6 &times; (5)<sup>2</sup> = 1 : 25</p>",
                    solution_hi: "<p>68.(b) <br>भुजाओं का अनुपात = <math display=\"inline\"><mroot><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mroot></math> : <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mroot><mn>125</mn><mn>3</mn></mroot></math> = 1 : 5<br>घन का पृष्ठीय क्षेत्रफल = 6a<sup>2</sup><br>आवश्यक अनुपात = 6 &times; (1)<sup>2</sup> : 6 &times; (5)<sup>2</sup> = 1 : 25</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "69",
                    section: "17",
                    question_en: "<p>69. Consider AD is a tangent to a circle of radius 6 cm. AC is a secant meeting the circle at B and CD is a diameter. If AB is 7 cm, then the value of AC (in cm) is:</p>",
                    question_hi: "<p>69. मान लीजिए कि AD एक 6 cm त्रिज्या वाले वृत्त की स्पर्श रेखा है। AC एक छेदक रेखा है जो वृत्त से बिन्दु B पर मिलती है और CD व्यास है। यदि AB का मान 7 cm है, तो AC का मान (cm में) क्या है ?</p>",
                    options_en: ["<p>16</p>", "<p>20</p>", 
                                "<p>9</p>", "<p>18</p>"],
                    options_hi: ["<p>16</p>", "<p>20</p>",
                                "<p>9</p>", "<p>18</p>"],
                    solution_en: "<p>69.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1735116572779.png\" alt=\"rId100\" width=\"247\" height=\"126\"><br>Let BC = <math display=\"inline\"><mi>x</mi></math><br>In <math display=\"inline\"><mi>&#916;</mi></math> ADC <br><math display=\"inline\"><mi>A</mi><msup><mrow><mi>C</mi></mrow><mrow><mn>2</mn></mrow></msup></math> = AD<sup>2</sup> + CD<sup>2</sup>&nbsp; &nbsp; &nbsp; &nbsp;(pythagoras theorem)<br><math display=\"inline\"><msup><mrow><mo>(</mo><mn>7</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>x</mi><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup></math> = AD<sup>2</sup> + (12)<sup>2</sup><br><math display=\"inline\"><mi>A</mi><msup><mrow><mi>D</mi></mrow><mrow><mn>2</mn></mrow></msup></math> = (7 + x)<sup>2</sup> - (12)<sup>2</sup> -----(i)<br><math display=\"inline\"><mi>A</mi><msup><mrow><mi>D</mi></mrow><mrow><mn>2</mn></mrow></msup></math> = 7 (7 + x ) ---- (ii)&nbsp; &nbsp; &nbsp;[ ∵ AD<sup>2 </sup>= AB &times; AC ]<br>Now <br><math display=\"inline\"><msup><mrow><mo>(</mo><mn>7</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>x</mi><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup></math> - (12)<sup>2</sup> = 7 (7 + x)&nbsp;<br><math display=\"inline\"><msup><mrow><mi>&#160;</mi><mi>x</mi></mrow><mrow><mn>2</mn></mrow></msup></math> + 7x -144 = 0 <br><math display=\"inline\"><msup><mrow><mi>&#160;</mi><mi>x</mi></mrow><mrow><mn>2</mn></mrow></msup></math> + 16x - 9x - 144 = 0<br><math display=\"inline\"><mi>x</mi></math> (x + 16) - 9 (x + 16) = 0<br>(<math display=\"inline\"><mi>x</mi></math> - 9) (x + 16) = 0<br><math display=\"inline\"><mi>x</mi></math> = 9 , -16 (∵ side cannot be negative)<br><math display=\"inline\"><mi>x</mi></math> = 9 <br>AC = AB + BC = 7 + 9 = 16cm</p>",
                    solution_hi: "<p>69.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1735116572779.png\" alt=\"rId100\" width=\"247\" height=\"126\"><br>माना , BC = <math display=\"inline\"><mi>x</mi></math><br><math display=\"inline\"><mi>&#916;</mi></math> ADC में <br><math display=\"inline\"><mi>A</mi><msup><mrow><mi>C</mi></mrow><mrow><mn>2</mn></mrow></msup></math> = AD<sup>2</sup> + CD<sup>2</sup> &nbsp;&nbsp; ( पाइथागोरस प्रमेय )<br><math display=\"inline\"><msup><mrow><mo>(</mo><mn>7</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>x</mi><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup></math> = AD<sup>2</sup> + (12)<sup>2</sup><br><math display=\"inline\"><mi>A</mi><msup><mrow><mi>D</mi></mrow><mrow><mn>2</mn></mrow></msup></math> = (7 + x)<sup>2</sup> - (12)<sup>2</sup> -----(i)<br><math display=\"inline\"><mi>A</mi><msup><mrow><mi>D</mi></mrow><mrow><mn>2</mn></mrow></msup></math> = 7 (7 + x) ---- (ii)&nbsp; &nbsp; &nbsp; &nbsp;[ ∵ AD<sup>2</sup> = AB &times; AC]<br>अब <br><math display=\"inline\"><msup><mrow><mo>(</mo><mn>7</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>x</mi><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup></math> - (12)<sup>2</sup> = 7 (7 + x)&nbsp;<br><math display=\"inline\"><msup><mrow><mi>&#160;</mi><mi>x</mi></mrow><mrow><mn>2</mn></mrow></msup></math> + 7x -144 = 0 <br><math display=\"inline\"><msup><mrow><mi>&#160;</mi><mi>x</mi></mrow><mrow><mn>2</mn></mrow></msup></math> + 16x - 9x - 144 = 0<br><math display=\"inline\"><mi>x</mi></math> (x + 16) - 9 (x + 16) = 0<br>( <math display=\"inline\"><mi>x</mi></math> - 9) (x + 16) = 0<br><math display=\"inline\"><mi>x</mi></math> = 9 , -16&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;(∵ भुजा ,ऋणात्मक नहीं हो सकती )<br><math display=\"inline\"><mi>x</mi></math> = 9 <br>AC = AB + BC = 7 + 9 = 16cm</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "70",
                    section: "17",
                    question_en: "70. A shopkeeper allows a discount of 22% to his customers and still gains 36%. Find the marked price (in₹) of an article which costs ₹1,092 to the shopkeeper.",
                    question_hi: "70. एक दुकानदार अपने ग्राहकों को 22% की छूट देता है और फिर भी 36% का लाभ अर्जित करता है। यदि दुकानदार के लिए वस्तु की लागत ₹1,092 हे, तो वस्तु का अंकित मूल्य (₹ में) ज्ञात कीजिए | ",
                    options_en: [" 1,792", " 2,024", 
                                " 1,904", " 1,872"],
                    options_hi: [" 1,792", " 2,024",
                                " 1,904", " 1,872"],
                    solution_en: "70.(c) <br />Marked price = 1092 × <math display=\"inline\"><mfrac><mrow><mn>136</mn></mrow><mrow><mn>78</mn></mrow></mfrac></math> = ₹1904",
                    solution_hi: "70.(c) <br />अंकित मूल्य = 1092 × <math display=\"inline\"><mfrac><mrow><mn>136</mn></mrow><mrow><mn>78</mn></mrow></mfrac></math> = ₹1904",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "71",
                    section: "17",
                    question_en: "<p>71. How many cubes of edge 15 cm can be put in a cubical box of 3 m edge ?</p>",
                    question_hi: "<p>71. 3 m किनारे वाले एक घनाकार बक्से में 15 cm किनारे वाले कितने घन रखे जा सकते हैं ?</p>",
                    options_en: ["<p>8000</p>", "<p>2000</p>", 
                                "<p>1050</p>", "<p>9000</p>"],
                    options_hi: ["<p>8000</p>", "<p>2000</p>",
                                "<p>1050</p>", "<p>9000</p>"],
                    solution_en: "<p>71.(a) <br>Required number of cubes = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>300</mn><mo>&#215;</mo><mn>300</mn><mo>&#215;</mo><mn>300</mn></mrow><mrow><mn>15</mn><mo>&#215;</mo><mn>15</mn><mo>&#215;</mo><mn>15</mn></mrow></mfrac></math> = 8000</p>",
                    solution_hi: "<p>71.(a) <br>घनों की आवश्यक संख्या = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>300</mn><mo>&#215;</mo><mn>300</mn><mo>&#215;</mo><mn>300</mn></mrow><mrow><mn>15</mn><mo>&#215;</mo><mn>15</mn><mo>&#215;</mo><mn>15</mn></mrow></mfrac></math> = 8000</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "72",
                    section: "17",
                    question_en: "<p>72. A number is first decreased by 18% and the resulting number is then increased by&nbsp;25%. What is the overall percentage change in the original number ?</p>",
                    question_hi: "<p>72. एक संख्या में पहले 18% की कमी की जाती है और फिर परिणामी संख्या में 25% की वृद्धि की जाती है।&nbsp;मूल संख्या में कुल प्रतिशत परिवर्तन कितना है?</p>",
                    options_en: ["<p>Decrease by 3.5%</p>", "<p>Increase by 2.5%</p>", 
                                "<p>Decrease by 2.5%</p>", "<p>Increase by 3.5%</p>"],
                    options_hi: ["<p>3.5% की कमी</p>", "<p>2.5% की वृद्धि</p>",
                                "<p>2.5% की कमी</p>", "<p>3.5% की वृद्धि</p>"],
                    solution_en: "<p>72.(b) <br>% change = 25 - 18 - <math display=\"inline\"><mfrac><mrow><mn>25</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>18</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 7 - 4.5 = 2.5 (increased)</p>",
                    solution_hi: "<p>72.(b) <br>% परिवर्तन = 25 - 18 - <math display=\"inline\"><mfrac><mrow><mn>25</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>18</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 7 - 4.5 = 2.5 (वृद्धि)</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "73",
                    section: "17",
                    question_en: "<p>73. A dishonest dealer professes to sell his goods at the cost price, but he uses a false weight of 850 grams per 1 kg weight. Find the percentage of his gain. (Correct up to two decimal places)</p>",
                    question_hi: "<p>73. एक बेईमान व्यापारी अपने माल को क्रय मूल्य पर बेचने का दावा करता है, परंतु वह 1 kg के स्थान पर 850 ग्राम के खोटे बाट का उपयोग करता है। उसके लाभ का प्रतिशत ज्ञात कीजिए। (दशमलव के दो स्थानों तक सही)</p>",
                    options_en: ["<p>17.56%</p>", "<p>17.65%</p>", 
                                "<p>17.45%</p>", "<p>17.54%</p>"],
                    options_hi: ["<p>17.56%</p>", "<p>17.65%</p>",
                                "<p>17.45%</p>", "<p>17.54%</p>"],
                    solution_en: "<p>73.(b)<br>Gain % = <math display=\"inline\"><mfrac><mrow><mn>1000</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>850</mn></mrow><mrow><mn>850</mn></mrow></mfrac></math> &times; 100 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>300</mn><mn>17</mn></mfrac></math> = 17.65%</p>",
                    solution_hi: "<p>73.(b)<br>लाभ % = <math display=\"inline\"><mfrac><mrow><mn>1000</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>850</mn></mrow><mrow><mn>850</mn></mrow></mfrac></math> &times; 100 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>300</mn><mn>17</mn></mfrac></math> = 17.65%</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "74",
                    section: "17",
                    question_en: "<p>74. The pie chart below depicts the percentage distribution of the costs associated with publishing a book.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1735116572939.png\" alt=\"rId101\" width=\"390\" height=\"230\"> <br>What will be the royalty rate for these books if the publisher is required to pay Rs.30,600 in printing costs for a specific number of volumes ?</p>",
                    question_hi: "<p>74. नीचे दिया गया पाई चार्ट किसी पुस्तक के प्रकाशन से जुड़ी लागतों के प्रतिशत वितरण को दर्शाता है।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1735116573064.png\" alt=\"rId102\" width=\"440\" height=\"260\"> <br>यदि प्रकाशक को विशिष्ट संख्या में संस्करणों के लिए मुद्रण लागत में 30,600 रुपये का भुगतान करना पड़ता है तो इन पुस्तकों के लिए रॉयल्टी दर क्या होगी ?</p>",
                    options_en: ["<p>Rs.21,200</p>", "<p>Rs.19,450</p>", 
                                "<p>Rs.26,150</p>", "<p>Rs.22,950</p>"],
                    options_hi: ["<p>Rs.21,200</p>", "<p>Rs.19,450</p>",
                                "<p>Rs.26,150</p>", "<p>Rs.22,950</p>"],
                    solution_en: "<p>74.(d)<br>Royalty rate = 30600 &times; <math display=\"inline\"><mfrac><mrow><mn>15</mn></mrow><mrow><mn>20</mn></mrow></mfrac></math> = Rs.22,950</p>",
                    solution_hi: "<p>74.(d)<br>रॉयल्टी दर = 30600 &times; <math display=\"inline\"><mfrac><mrow><mn>15</mn></mrow><mrow><mn>20</mn></mrow></mfrac></math> = Rs.22,950</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "75",
                    section: "18",
                    question_en: "75. The age of a tree depends on the number of rings in the trunk. The value of a such tree is proportional to its age. If a tree with 40 rings and 50 years of age has a value of ₹50,000, then a tree with 30 rings is ________ years of age and its value is ₹_______.",
                    question_hi: "75.  एक वृक्ष की आयु उसके तने में मौजूद छल्लों की संख्या पर निर्भर करती है। ऐसे वृक्ष का मूल्य उसकी आयु के समानुपाती होता है। यदि 40 छल्लों और 50 वर्ष की आयु वाले वृक्ष का मूल्य ₹50,000 है, तो 30 छल्लों वाले वृक्ष की आयु __________ वर्ष और इसका मूल्य ₹_______है।",
                    options_en: [" 37.5, 57500", " 37.5, 37500", 
                                " 57.5, 37500", " 57.5, 57500"],
                    options_hi: [" 37.5, 57500", " 37.5, 37500",
                                " 57.5, 37500", " 57.5, 57500"],
                    solution_en: "75.(b)<br />age of a tree depends on the number of rings in the trunk<br />40 rings =  50years<br />1 rings = 1.25 years <br />Then, 30 rings = 30 × 1.25 = 37.5 years<br />The value of tree is proportional to age of tree<br />50 years = Rs. 50000<br />1 years = Rs. 1000<br />Then, 37.5 years = 37.5 × 1000 = Rs. 37500<br />Hence, tree with 30 rings is 37.5 years of age and its value is Rs. 37500",
                    solution_hi: "75.(b)<br />किसी पेड़ की उम्र तने में मौजूद छल्लों की संख्या पर निर्भर करती है<br />40 छल्ले  = 50 वर्ष<br />1 छल्ला = 1.25 वर्ष<br />फिर, 30 छल्ले = 30 × 1.25 = 37.5 वर्ष<br />पेड़ का मूल्य पेड़ की उम्र के समानुपाती होता है<br />50 वर्ष = रु. 50000<br />1 वर्ष = रु. 1000<br />फिर, 37.5 वर्ष = 37.5 × 1000 = रु. 37500<br />अतः, 30 छल्लों वाला पेड़ 37.5 वर्ष पुराना है और इसका मूल्य रु. 37500 है। ",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "76",
                    section: "18",
                    question_en: "<p>76. The Mundra Thermal Power Plant is located in which district of Gujarat ?</p>",
                    question_hi: "<p>76. मुंद्रा थर्मल पावर प्लांट (Mundra Thermal Power Plant) गुजरात के किस जिले में स्थित है ?</p>",
                    options_en: ["<p>Jamnagar</p>", "<p>Kutch</p>", 
                                "<p>Kheda</p>", "<p>Gandhinagar</p>"],
                    options_hi: ["<p>जामनगर</p>", "<p>कच्छ</p>",
                                "<p>खेड़ा</p>", "<p>गांधीनगर</p>"],
                    solution_en: "<p>76.(b) <strong>Kutch.</strong> The Mundra Thermal Power Plant in Gujarat. Other Power plants in Gujarat: Jhanor-Gandhar Thermal Power Station, Kawas Thermal Power Station, Essar Power Gujarat Limited, Kutch Lignite Thermal Power Station, Sabarmati Thermal Power Station, Sikka Thermal Power Station, Surat Lignite Thermal Power Station, Ukai Thermal Power Station, Wanakbori Thermal Power Station.</p>",
                    solution_hi: "<p>76.(b)<strong> कच्छ। </strong>मुंद्रा थर्मल पावर प्लांट गुजरात में है। गुजरात में अन्य पावर प्लांट: झानोर-गांधार थर्मल पावर स्टेशन, कावास थर्मल पावर स्टेशन, एस्सार पावर गुजरात लिमिटेड, कच्छ लिग्नाइट थर्मल पावर स्टेशन, साबरमती थर्मल पावर स्टेशन, सिक्का थर्मल पावर स्टेशन, सूरत लिग्नाइट थर्मल पावर स्टेशन, उकाई थर्मल पावर स्टेशन, वनकबोरी थर्मल पावर स्टेशन।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "77",
                    section: "18",
                    question_en: "<p>77. Select the musical instrument which is NOT played by way of percussion or NOT&nbsp;played by way of striking any object ?</p>",
                    question_hi: "<p>77. उस संगीत वाद्य यंत्र का चयन कीजिए, जो आघात के माध्यम से नहीं बजाया जाता है या किसी वस्तु पर&nbsp;प्रहार करके नहीं बजाया जाता है ?</p>",
                    options_en: ["<p>Pakhawaj</p>", "<p>Drum</p>", 
                                "<p>Tabla</p>", "<p>Shehnai</p>"],
                    options_hi: ["<p>पखावज</p>", "<p>ड्रम</p>",
                                "<p>तबला</p>", "<p>शहनाई</p>"],
                    solution_en: "<p>77.(d)<strong> Shehnai</strong>. It is a wind instrument made of wood and metal. Other wind musical Instruments: Flute, oboe, clarinet, bassoon, and saxophone. Pakhawaj, drum, and tabla are all percussion instruments that are played by striking the surface of the instrument with hands or sticks to produce sound.</p>",
                    solution_hi: "<p>77.(d) <strong>शहनाई। </strong>यह लकड़ी और धातु से निर्मित एक वायु वाद्ययंत्र है। अन्य वायु संगीत वाद्ययंत्र: बांसुरी, ओबो, शहनाई, बासून और सैक्सोफोन। पखावज, ड्रम और तबला सभी ताल वाद्ययंत्र हैं जिन्हें ध्वनि उत्पन्न करने के लिए हाथों या डंडियों से वाद्य की सतह पर प्रहार करके बजाया जाता है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "78",
                    section: "18",
                    question_en: "<p>78. BVR Subrahmanyam was appointed as the ________, in February 2023.</p>",
                    question_hi: "<p>78. बी.वी.आर. सुब्रह्मण्यम को फरवरी 2023 में _______के रूप में नियुक्त किया गया।</p>",
                    options_en: ["<p>Chairman of UPSC</p>", "<p>CEO of NITI Aayog</p>", 
                                "<p>CEO of IBRD</p>", "<p>Chairman of NITI Aayog</p>"],
                    options_hi: ["<p>यूपीएससी के अध्यक्ष</p>", "<p>नीति आयोग के सीईओ</p>",
                                "<p>आईबीआरडी के सीईओ</p>", "<p>नीति आयोग के अध्यक्ष</p>"],
                    solution_en: "<p>78.(b) <strong>CEO of NITI Aayog. </strong>NITI (National Institution for Transforming India) Aayog (instituted in 2015) was constituted to replace the Planning Commission (instituted in 1950). The former Planning Commission Secretary Sindhushree Khullar appointed its first CEO. Its Governing Council is chaired by the Prime Minister and includes Chief Ministers of all states and Union Territories with legislatures, and Lt. Governors of other Union Territories.</p>",
                    solution_hi: "<p>78.(b) <strong>नीति आयोग के सीईओ।</strong> NITI (नेशनल इंस्टीट्यूशन फॉर ट्रांसफॉर्मिंग इंडिया) आयोग (2015 में स्थापित) का गठन, योजना आयोग (1950 में स्थापित) के स्थान पर किया गया था। योजना आयोग की पूर्व सचिव सिंधुश्री खुल्लर को इसका पहला CEO नियुक्त किया गया था । इसकी शासी परिषद के अध्यक्ष प्रधानमंत्री होते हैं और इसमें सभी राज्यों और विधानसभा वाले केंद्र शासित प्रदेशों के मुख्यमंत्री तथा अन्य केंद्र शासित प्रदेशों के उपराज्यपाल शामिल होते हैं।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "79",
                    section: "18",
                    question_en: "<p>79. India\'s first indigenous Aircraft Carrier, _____, conducted its maiden sea travel in&nbsp;August 2021 marking a significant step towards making military equipment in India.</p>",
                    question_hi: "<p>79. भारत के पहले स्वदेशी विमान वाहक, ______ ने अगस्त 2021 में अपना पहला समुद्री परीक्षण&nbsp;किया, जो भारत के भीतर सैन्य उपकरण बनाने की दिशा में एक महत्वपूर्ण कदम है।</p>",
                    options_en: ["<p>INS Vayudooth</p>", "<p>INS Swades</p>", 
                                "<p>INS Kranti</p>", "<p>INS Vikrant</p>"],
                    options_hi: ["<p>आईएनएस (INS) वायुदूत</p>", "<p>आईएनएस (INS) स्वदेश</p>",
                                "<p>आईएनएस (INS) क्रांति</p>", "<p>आईएनएस (INS) विक्रांत</p>"],
                    solution_en: "<p>79.(d) <strong>INS Vikrant. </strong>It was designed and built by Cochin Shipyard Limited (CSL) Kochi, Kerala and launched into water in 2013.</p>",
                    solution_hi: "<p>79.(d) <strong>आई.एन.एस (INS) विक्रांत।</strong> इसे केरल के कोचीन शिपयार्ड लिमिटेड (CSL) कोच्चि, द्वारा डिजाइन और निर्मित किया गया था और 2013 में पानी में उतारा गया था।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "80",
                    section: "18",
                    question_en: "<p>80. Who founded the Bombay Presidency Association in 1885 along with Pherozeshah&nbsp;Mehta and KT Telang ?</p>",
                    question_hi: "<p>80. फिरोजशाह मेहता और के.टी. तेलंग के साथ 1885 में बॉम्बे प्रेसीडेंसी एसोसिएशन (Bombay&nbsp;Presidency Association) की स्थापना किसने की थी ?</p>",
                    options_en: ["<p>AK Fazlul Haq</p>", "<p>Badruddin Tyabji</p>", 
                                "<p>Behramji M Malabari</p>", "<p>Zakir Husain</p>"],
                    options_hi: ["<p>ए. के. फजलुल हक (AK Fazlul Haq)</p>", "<p>बदरुद्दीन तैयबजी (Badruddin Tyabji)</p>",
                                "<p>बेहरामजी एम मालाबारी (Behramji M Malabari)</p>", "<p>जाकिर हुसैन (Zakir Husain)</p>"],
                    solution_en: "<p>80.(b) <strong>Badruddin Tyabji </strong>was the third President (in 1887) of the Indian National Congress and the first Muslim to hold that position. Other Political organisations and founder: London Indian Society (1865) - Dadabhai Naoroji. East India Association (1866) - Dadabhai Naoroji. Poona Sarvajanik Sabha (1870) - G.V. Joshi, S.H. Sathe, S.H. Chiplonkar and Mahadev Govind Ranade.</p>",
                    solution_hi: "<p>80.(b) <strong>बदरुद्दीन तैयबजी </strong>भारतीय राष्ट्रीय कांग्रेस के तीसरे अध्यक्ष (1887 में) थे और इस पद पर आसीन होने वाले पहले मुस्लिम थे। अन्य राजनीतिक संगठन और संस्थापक: लंदन इंडियन सोसाइटी (1865) - दादाभाई नौरोजी। ईस्ट इंडिया एसोसिएशन (1866) - दादाभाई नौरोजी। पूना सार्वजनिक सभा (1870) - जी.वी. जोशी, एस.एच. साठे, एस.एच. चिपलोंकर और महादेव गोविंद रानाडे।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "81",
                    section: "18",
                    question_en: "<p>81. Which of the following is a cyclic arrangement of time units in Hindustani classical&nbsp;music ?</p>",
                    question_hi: "<p>81. निम्नलिखित में से कौन-सा हिंदुस्तानी शास्त्रीय संगीत में समय इकाइयों की चक्रीय व्यवस्था (cyclic&nbsp;arrangement) है ?</p>",
                    options_en: ["<p>Jati</p>", "<p>Nyasa</p>", 
                                "<p>Tala</p>", "<p>Raga</p>"],
                    options_hi: ["<p>जाति</p>", "<p>न्यासा</p>",
                                "<p>ताल</p>", "<p>राग</p>"],
                    solution_en: "<p>81.(c) <strong>Tala.</strong> It defines the structure and rhythm of a musical performance by organizing beats into repetitive cycles, with a specific number of beats (matras) and various patterns to maintain timing. Jati refers to the classification of ragas based on their scale. Nyasa refers to the concluding note of a phrase in a raga. Raga is the melodic framework for compositions.</p>",
                    solution_hi: "<p>81.(c) <strong>ताल। </strong>यह एक संगीत प्रदर्शन की संरचना और लय को परिभाषित करता है, जिसमें बीट्स को पुनरावर्ती चक्रों में व्यवस्थित किया जाता है, जिसमें एक निश्चित संख्या में बीट्स (मात्राएँ) और समय बनाए रखने के लिए विभिन्न पैटर्न होते हैं। जाति, रागों को उनके पैमाने के आधार पर वर्गीकृत करने को संदर्भित करती है। न्यास किसी राग में किसी पद के अंतिम स्वर को कहते हैं। राग, रचनाओं के लिए संगीतात्मक रूपरेखा है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "82",
                    section: "18",
                    question_en: "<p>82. Which keyboard key is used to jump the cursor to the next cell of the table in MS&nbsp;Word ?</p>",
                    question_hi: "<p>82. MS वर्ड में कर्सर को टेबल के अगले सेल पर ले जाने के लिए इनमें से किस कीबोर्ड कुंजी का उपयोग किया जाता है ?</p>",
                    options_en: ["<p>Tab</p>", "<p>Home</p>", 
                                "<p>Ctrl</p>", "<p>Shift</p>"],
                    options_hi: ["<p>टैब (Tab)</p>", "<p>होम (Home)</p>",
                                "<p>कंट्रोल (Ctrl)</p>", "<p>शिफ्ट (Shift)</p>"],
                    solution_en: "<p>82.(a) <strong>Tab.</strong> In MS Word, pressing Tab moves the cursor to the next cell in the row, while pressing Shift+Tab moves the cursor to the previous cell. Home key takes the cursor to the beginning of the line or cell. Shift key is used for selecting text or navigating within a cell.</p>",
                    solution_hi: "<p>82.(a) <strong>टैब। </strong>MS Word में, टैब (Tab) दबाने पर कर्सर पंक्ति में अगले सेल पर चला जाता है, जबकि Shift+Tab दबाने पर कर्सर पिछले सेल पर चला जाता है। होम कुंजी (Home key) कर्सर को लाइन या सेल की शुरुआत में ले जाती है। शिफ्ट कुंजी (Shift key) का उपयोग टेक्स्ट को सिलेक्ट करने करने या सेल के भीतर नेविगेट करने के लिए किया जाता है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "83",
                    section: "18",
                    question_en: "<p>83. Match the items in List I with those in List II.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1735116573187.png\" alt=\"rId103\" width=\"447\" height=\"119\"></p>",
                    question_hi: "<p>83. सूची I के पदों का सूची II के पदों से मिलान कीजिए।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1735116573396.png\" alt=\"rId104\" width=\"407\" height=\"142\"></p>",
                    options_en: ["<p>i-a, ii-d, iii-c, iv-b</p>", "<p>i-c, ii-b, iii-d, iv-a</p>", 
                                "<p>i-b, ii-c, iii-d, iv-a</p>", "<p>i-c, ii-d, iii-a, iv-b</p>"],
                    options_hi: ["<p>i-a, ii-d, iii-c, iv-b</p>", "<p>i-c, ii-b, iii-d, iv-a</p>",
                                "<p>i-b, ii-c, iii-d, iv-a</p>", "<p>i-c, ii-d, iii-a, iv-b</p>"],
                    solution_en: "<p>83.(a) <strong>i-a, ii-d, iii-c, iv-b. </strong>Garibi Hatao: This slogan was part of Indira Gandhi&rsquo;s agenda during the Fifth Five-Year Plan (1974-1979). LPG policies: The Liberalization, Privatization, and Globalization reforms were introduced in 1991, during the Eighth Five-Year Plan (1992-1997). Rolling Plan: The Rolling Plan was introduced during the Janata Party government in the period 1978-80. Growth with justice and equity: This was a theme of the Ninth Five-Year Plan (1997-2002).</p>",
                    solution_hi: "<p>83.(a)<strong> i-a, ii-d, iii-c, iv-b. </strong>गरीबी हटाओ: यह नारा पांचवीं पंचवर्षीय योजना (1974-1979) के दौरान इंदिरा गांधी के एजेंडे का हिस्सा था। LPG नीतियाँ: उदारीकरण (Liberalization), निजीकरण (Privatization) और वैश्वीकरण (Globalization) सुधार 1991 में आठवीं पंचवर्षीय योजना (1992-1997) के दौरान शुरू किए गए थे। रोलिंग प्लान: यह 1978-80 की अवधि में जनता पार्टी सरकार के दौरान शुरू की गई थी। न्याय और समानता के साथ विकास: यह नौवीं पंचवर्षीय योजना (1997-2002) का एक विषय था।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "84",
                    section: "18",
                    question_en: "<p>84. How many languages are included in the Eighth Schedule of the Constitution of India as scheduled languages ?</p>",
                    question_hi: "<p>84. भारत के संविधान की आठवीं अनुसूची में अनुसूचित भाषाओं के रूप में कितनी भाषाओं को शामिल किया गया है ?</p>",
                    options_en: ["<p>22</p>", "<p>24</p>", 
                                "<p>23</p>", "<p>21</p>"],
                    options_hi: ["<p>22</p>", "<p>24</p>",
                                "<p>23</p>", "<p>21</p>"],
                    solution_en: "<p>84.(a) <strong>22. </strong>Part XVII of the Indian constitution deals with the official languages in Articles 343 to 351. Initially 14 were included in the Constitution. Sindhi language was added by the 21st Amendment Act of 1967. Konkani, Manipuri and Nepali were included by the 71st Amendment Act of 1992. Subsequently Bodo, Dogri, Maithili and Santhali were added by the 92nd Amendment Act of 2003.</p>",
                    solution_hi: "<p>84.(a) <strong>22. </strong>भारतीय संविधान के भाग XVII में अनुच्छेद 343 से 351 तक राजभाषाओं का उल्लेख है। प्रारंभ में 14 भाषाओं को संविधान में शामिल किया गया था। सिंधी भाषा को 21वें संशोधन अधिनियम 1967 द्वारा जोड़ा गया। कोंकणी, मणिपुरी और नेपाली को 1992 के 71वें संशोधन अधिनियम द्वारा शामिल किया गया था। इसके बाद 2003 के 92वें संशोधन अधिनियम द्वारा बोडो, डोगरी, मैथिली और संथाली को इसमें शामिल किया गया।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "85",
                    section: "18",
                    question_en: "<p>85. What is the keyboard shortcut for opening a blank new document in MS-Word ?</p>",
                    question_hi: "<p>85. एमएस-वर्ड (MS-Word) में एक खाली नया डॉक्यूमेंट खोलने के लिए कीबोर्ड शॉर्टकट क्या है ?</p>",
                    options_en: ["<p>Ctrl + S</p>", "<p>Ctrl + N</p>", 
                                "<p>Ctrl + B</p>", "<p>Ctrl + C</p>"],
                    options_hi: ["<p>Ctrl + S</p>", "<p>Ctrl + N</p>",
                                "<p>Ctrl + B</p>", "<p>Ctrl + C</p>"],
                    solution_en: "<p>85.(b) <strong>Ctrl + N.</strong> Some other short keys: Ctrl + S, for saving a document. Ctrl + B is used to bold selected text. Ctrl + C is used to copy selected text. Ctrl+O is used to open documents. Ctrl + W is used to Close the document. Ctrl+I is used to apply italic formatting to text.</p>",
                    solution_hi: "<p>85.(b) <strong>Ctrl + N. </strong>कुछ अन्य शॉर्ट की (short keys): Ctrl + S = डॉक्यूमेंट को सेव (save) करने के लिए । Ctrl + B = चयनित टेस्ट (selected text) को बोल्ड करने के लिए । Ctrl + C = चयनित टेस्ट (selected text) को कॉपी करने के लिए। Ctrl + O = डॉक्यूमेंट ओपन (open) करने के लिए । Ctrl + W = डॉक्यूमेंट को बंद (close) करने के लिए। Ctrl + I = टेक्स्ट पर इटैलिक फ़ॉर्मेटिंग लागू (apply) करने के लिए।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "86",
                    section: "18",
                    question_en: "<p>86. The Bhotia dance form is a folk-dance from which of the following Indian states ?</p>",
                    question_hi: "<p>86. भोटिया नृत्य कला निम्नलिखित में से किस भारतीय राज्य का लोक-नृत्य है ?</p>",
                    options_en: ["<p>Odisha</p>", "<p>Goa</p>", 
                                "<p>Tamil Nadu</p>", "<p>Uttarakhand</p>"],
                    options_hi: ["<p>ओडिशा</p>", "<p>गोवा</p>",
                                "<p>तमिलनाडु</p>", "<p>उत्तराखंड</p>"],
                    solution_en: "<p>86.(d) <strong>Uttarakhand. </strong>The Bhotia dance performed by the Bhotia tribe of Uttarakhand. States and their folk dances: Uttarakhand - Jhora, Chholia, Langvir Nritya, Barada Nati, Pandav Nritya, Chancheri, Chhapeli. Odisha - Savari, Ghumara. Goa - Tarangamel, Dekhni, Fugdi, Shigmo,Ghode, Modni, Samayi nrutya, Ranmale. Tamil Nadu - Kumi, Kolattam, Kavadi.</p>",
                    solution_hi: "<p>86.(d) <strong>उत्तराखंड। </strong>भोटिया नृत्य उत्तराखंड की भोटिया जनजाति द्वारा किया जाता है। राज्य और उनके लोक नृत्य: उत्तराखंड - झोड़ा, छोलिया, लंगविर नृत्य, बरदा नाटी, पांडव नृत्य, चांचरी, छपेली। ओडिशा - सावरी, घुमरा। गोवा - तारंगामेल, देखनी, फुगड़ी, शिग्मो, घोडे, मोदनी, समयी नृत्य, रणमाले। तमिलनाडु - कुमी, कोलट्टम, कवाड़ी।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "87",
                    section: "18",
                    question_en: "<p>87. If an object of mass 2 kg is dropped from a height of 10 metres, what will be the ratio of its potential energy and kinetic energy at the height of 5 metres (g = 10 m/sec<sup>2</sup>)</p>",
                    question_hi: "<p>87. यदि 2 किलोग्राम द्रव्यमान की कोई वस्तु को 10 मीटर की ऊंचाई से गिराया जाए, तो 5 मीटर की ऊंचाई पर उसकी स्थितिज ऊर्जा और गतिज ऊर्जा का अनुपात क्या होगा? (g = 10 m/sec<sup>2</sup>)</p>",
                    options_en: ["<p>1 : 2</p>", "<p>4 : 1</p>", 
                                "<p>1 : 1</p>", "<p>1 : 4</p>"],
                    options_hi: ["<p>1 : 2</p>", "<p>4 : 1</p>",
                                "<p>1 : 1</p>", "<p>1 : 4</p>"],
                    solution_en: "<p>87.(c) <strong>1 : 1. </strong>When the object is dropped from a height of 10 meters, its potential energy (PE) is converted to kinetic energy (KE) as it falls. At the height of 5 meters, the object has lost half of its initial potential energy and has gained an equal amount of kinetic energy.<br>Initial Potential energy (PE) = mgh <br>= 2 kg &times; 10 m/s&sup2; &times; 10 m = 200 J<br>At 5 meters, Potential energy (PE) = mgh <br>= 2 kg &times; 10 m/s&sup2; &times; 5 m = 100 J<br>Kinetic energy (KE) = Initial PE - PE <br>= 200 J - 100 J = 100 J<br>Ratio of PE to KE = 100 J : 100 J = 1 : 1</p>",
                    solution_hi: "<p>87.(c) <strong>1 : 1. </strong>जब वस्तु को 10 मीटर की ऊंचाई से गिराया जाता है, तो गिरते समय इसकी स्थितिज ऊर्जा (PE) गतिज ऊर्जा (KE) में बदल जाती है। 5 मीटर की ऊंचाई पर, वस्तु अपनी प्रारंभिक स्थितिज ऊर्जा का आधा हिस्सा खो देती है और गतिज ऊर्जा की समान मात्रा प्राप्त कर लेती है। प्रारंभिक स्थितिज ऊर्जा (PE) = mgh <br>= 2 kg &times; 10 m/s&sup2; &times; 10 m = 200 J <br>5 मीटर पर, स्थितिज ऊर्जा (PE) = mgh <br>= 2 kg &times; 10 m/s&sup2; &times; 5 m = 100 J <br>गतिज ऊर्जा (KE) = प्रारंभिक PE - PE <br>= 200 J - 100 J = 100 J <br>PE से KE का अनुपात = 100 J : 100 J = 1 : 1</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "88",
                    section: "18",
                    question_en: "<p>88. In which year was the 15<sup>th</sup> National Census Survey of India conducted ?</p>",
                    question_hi: "<p>88. भारत का 15वीं राष्ट्रीय जनगणना सर्वेक्षण किस वर्ष आयोजित किया गया था ?</p>",
                    options_en: ["<p>2001</p>", "<p>2011</p>", 
                                "<p>2021</p>", "<p>1991</p>"],
                    options_hi: ["<p>2001</p>", "<p>2011</p>",
                                "<p>2021</p>", "<p>1991</p>"],
                    solution_en: "<p>88.(b) <strong>2011.</strong> The 16th census (postponed due to COVID-19) was planned for 2021. The first non-synchronous census was in 1872 under Lord Mayo, and the first synchronous census was in 1881, led by W.C. Plowden. Since 1881 a Population Census has been conducted in the country regularly every ten years.</p>",
                    solution_hi: "<p>88.(b)<strong> 2011.</strong> 16वीं जनगणना (कोविड-19 के कारण स्थगित) 2021 के लिए योजनाबद्ध थी। पहली असमकालिक जनगणना 1872 में लॉर्ड मेयो के अधीन हुई थी, और पहली समकालिक जनगणना 1881 में डब्ल्यू.सी.प्लोडेन के नेतृत्व में हुई थी। 1881 से देश में प्रत्येक दस वर्ष में नियमित रूप से जनगणना की जाती रही है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "89",
                    section: "18",
                    question_en: "<p>89. Easter is a religious festival of:</p>",
                    question_hi: "<p>89. ईस्टर किस धर्म का धार्मिक त्योहार है:</p>",
                    options_en: ["<p>Hindus</p>", "<p>Christians</p>", 
                                "<p>Buddhists</p>", "<p>Sikhs</p>"],
                    options_hi: ["<p>हिंदू धर्म</p>", "<p>ईसाई धर्म</p>",
                                "<p>बौद्ध धर्म</p>", "<p>सिख धर्म</p>"],
                    solution_en: "<p>89.(b) <strong>Christians.</strong> Easter celebrates the resurrection of Jesus Christ from the dead. It is observed on the first Sunday following the full Moon after the spring equinox. The holiday concludes the &ldquo;Passion of Christ,&rdquo; a series of events and holidays that begins with Lent (a 40-day period of fasting, prayer and sacrifice) and ends with Holy Week, which includes Holy Thursday, Good Friday, and Easter Sunday.</p>",
                    solution_hi: "<p>89.(b) <strong>ईसाई धर्म। </strong>ईस्टर, ईसा मसीह के मृत्यु से पुनर्जीवन का उत्सव है। यह वसंत विषुव के बाद पूर्णिमा के पहले रविवार को मनाया जाता है। यह अवकाश \"पैशन ऑफ क्राइस्ट\" के साथ समाप्त होता है, जो घटनाओं और अवकाश की एक श्रृंखला है जो लेंट (उपवास, प्रार्थना और बलिदान की 40 दिन की अवधि) से शुरू होती है और पवित्र सप्ताह के साथ समाप्त होती है, जिसमें पवित्र गुरुवार, गुड फ्राइडे और ईस्टर रविवार शामिल हैं। ।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "90",
                    section: "18",
                    question_en: "<p>90. Who was the founder of the Bahmani Sultanate ?</p>",
                    question_hi: "<p>90. बहमनी सल्तनत के संस्थापक कौन थे ?</p>",
                    options_en: ["<p>Muhammad Shah</p>", "<p>Feroz Shah Bahmani</p>", 
                                "<p>Muhammad Bin Tughlaq</p>", "<p>Alauddin Hasan Bahman Shah</p>"],
                    options_hi: ["<p>मुहम्मद शाह</p>", "<p>फिरोज शाह बहमनी</p>",
                                "<p>मुहम्मद बिन तुगलक</p>", "<p>अलाउद्दीन हसन बहमन शाह</p>"],
                    solution_en: "<p>90.(d) <strong>Alauddin Hasan Bahman Shah</strong> founded the Bahmani Sultanate in 1347. Its capital was Ahsanabad (Gulbarga) from 1347 to 1425, later shifted to Muhammadabad (Bidar). Qutub ud-din Aibak was the first ruler and founder of the Slave Dynasty. Muhammad Bin Tughlaq ruled the Tughlaq Dynasty from 1325 to 1351. His cousin Feroz Shah Tughluq, ascended the throne in 1351 and ruled until 1388.</p>",
                    solution_hi: "<p>90.(d) <strong>अलाउद्दीन हसन बहमन शाह </strong>ने 1347 में बहमनी सल्तनत की स्थापना की। इसकी राजधानी 1347 से 1425 तक अहसानाबाद (गुलबर्गा) थी, जिसे बाद में मुहम्मदाबाद (बीदर) में स्थानांतरित कर दिया गया। कुतुबुद्दीन ऐबक, गुलाम वंश का पहला शासक और संस्थापक था। मुहम्मद बिन तुगलक ने 1325 से 1351 ई. तक तुगलक वंश पर शासन किया। उनके चचेरे भाई फ़िरोज़ शाह तुगलक 1351 में सिंहासन पर बैठे और 1388 तक शासन किया।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "91",
                    section: "18",
                    question_en: "<p>91. Which of the following is NOT a way to acquire citizenship in India ?</p>",
                    question_hi: "<p>91. भारत में नागरिकता प्राप्त करने का निम्नलिखित में से कौन-सा एक तरीका नहीं है ?</p>",
                    options_en: ["<p>Incorporation of territory</p>", "<p>Renunciation</p>", 
                                "<p>Registration</p>", "<p>Birth</p>"],
                    options_hi: ["<p>क्षेत्र का समावेश</p>", "<p>त्याग</p>",
                                "<p>पंजीकरण</p>", "<p>जन्म</p>"],
                    solution_en: "<p>91.(b) <strong>Renunciation.</strong> There are five recognized ways to acquire citizenship in India under the Citizenship Act, 1955: By Birth - A person born in India can acquire citizenship by birth. By Descent - A person born outside India to Indian parents can acquire citizenship. By Registration - Certain persons, such as foreign nationals married to Indian citizens, can acquire citizenship through registration. By Naturalization - Foreign nationals can acquire citizenship after fulfilling certain residency requirements. By Incorporation of Territory - If a foreign territory becomes part of India.</p>",
                    solution_hi: "<p>91.(b) <strong>त्याग। </strong>नागरिकता अधिनियम, 1955 के तहत भारत में नागरिकता प्राप्त करने के पाँच मान्य तरीके: जन्म से - भारत में जन्मा व्यक्ति जन्म से नागरिकता प्राप्त कर सकता है। वंश से - भारत के बाहर भारतीय माता-पिता से जन्मा व्यक्ति नागरिकता प्राप्त कर सकता है। पंजीकरण द्वारा - कुछ व्यक्ति, जैसे कि भारतीय नागरिकों से विवाहित विदेशी नागरिक, पंजीकरण के माध्यम से नागरिकता प्राप्त कर सकते हैं। प्राकृतिककरण द्वारा - विदेशी नागरिक कुछ निवास आवश्यकताओं को पूरा करने के बाद नागरिकता प्राप्त कर सकते हैं। क्षेत्र के समावेश द्वारा - यदि कोई विदेशी क्षेत्र भारत का हिस्सा बन जाता है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "92",
                    section: "18",
                    question_en: "<p>92. With reference to the Directive Principles of State policy contained in the Constitution of India, which of the following statements is/are correct ?<br>1. Article 37 of the Constitution of India provides that the Directive Principles of State<br>Policy are enforceable in the High Courts only.<br>2. Article 38 provides that the State shall strive to minimise the inequalities in income<br>amongst individuals and groups of people.<br>3. Article 39 provides that the State shall direct its policy towards securing that there is<br>equal pay for equal work for both men and women.</p>",
                    question_hi: "<p>92. भारत के संविधान में निहित राज्य नीति के निदेशक तत्वों के संदर्भ में, निम्नलिखित में से कौन सा/से<br>कथन सही है/हैं ?<br>1. भारत के संविधान के अनुच्छेद 37 में यह प्रावधान है कि राज्य के नीति निदेशक तत्व केवल उच्च<br>न्यायालयों में लागू करने योग्य हैं।<br>2. अनुच्छेद 38 में यह प्रावधान है कि राज्य व्यक्तियों और लोगों के समूहों के बीच आय में असमानताओं<br>को कम करने का प्रयास करेगा।<br>3. अनुच्छेद 39 में यह प्रावधान है कि राज्य अपनी नीति को यह सुनिश्चित करने की दिशा में निर्देशित<br>करेगा कि पुरुषों और महिलाओं दोनों के लिए समान कार्य के लिए समान वेतन हो।</p>",
                    options_en: ["<p>Only 2 and 3</p>", "<p>Only 1 and 3</p>", 
                                "<p>Only 2</p>", "<p>Only 1</p>"],
                    options_hi: ["<p>केवल 2 और 3</p>", "<p>केवल 1 और 3</p>",
                                "<p>केवल 2</p>", "<p>केवल 1</p>"],
                    solution_en: "<p>92.(a) <strong>Only 2 and 3. </strong>Article 37 of the Constitution of India provides that the Directive Principles of State Policy (DPSP, Part IV - Article 36 to 51) are not enforceable in any court.</p>",
                    solution_hi: "<p>92.(a) <strong>केवल 2 और 3. </strong>भारत के संविधान का अनुच्छेद 37 यह प्रावधान करता है कि राज्य नीति के निर्देशक सिद्धांत (DPSP, भाग IV - अनुच्छेद 36 से 51) किसी भी न्यायालय में लागू नहीं होंगे।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "93",
                    section: "18",
                    question_en: "<p>93. Which city will host 2028 Olympics games ?</p>",
                    question_hi: "<p>93. कौन-सा शहर 2028 ओलंपिक खेलों की मेजबानी करेगा?</p>",
                    options_en: ["<p>Boston, USA</p>", "<p>Los Angles, USA</p>", 
                                "<p>San Diego, USA</p>", "<p>New York, USA</p>"],
                    options_hi: ["<p>बोस्टन, यूएसए (Boston, USA)</p>", "<p>लॉस एंजेल्स , यूएसए (Los Angles, USA)</p>",
                                "<p>सैन डिएगो, यूएसए (San Diego, USA)</p>", "<p>न्यूयॉर्क, यूएसए (New York, USA)</p>"],
                    solution_en: "<p>93.(b) <strong>Los Angeles, USA. </strong>The 2024 Summer Olympics and Paralympics Games was hosted by Paris, France. Other upcoming Olympic games and host: 2030 Winter Olympics: French Alps. 2032 Summer Olympics: Brisbane, Australia. 2034 Winter Olympics and Paralympics: Salt Lake City, USA.</p>",
                    solution_hi: "<p>93.(b) <strong>लॉस एंजिल्स, USA । </strong>2024 ग्रीष्मकालीन ओलंपिक और पैरालिंपिक खेलों की मेज़बानी पेरिस, फ्रांस द्वारा की गई । अन्य आगामी ओलंपिक खेल और मेज़बान देश: 2030 शीतकालीन ओलंपिक: फ्रेंच आल्प्स। 2032 ग्रीष्मकालीन ओलंपिक: ब्रिस्बेन, ऑस्ट्रेलिया। 2034 शीतकालीन ओलंपिक और पैरालिंपिक: साल्ट लेक सिटी, USA।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "94",
                    section: "18",
                    question_en: "<p>94. In March 2023, Rashmi Shukla was appointed as the Director General of which<br>organisation ?</p>",
                    question_hi: "<p>94. मार्च 2023 में, रश्मि शुक्ला को किस संगठन के महानिदेशक के रूप में नियुक्त किया गया था ?</p>",
                    options_en: ["<p>Sashastra Seema Bal</p>", "<p>Border Security Force</p>", 
                                "<p>Central Reserve Police Force</p>", "<p>Indo-Tibetan Border Police</p>"],
                    options_hi: ["<p>सशस्त्र सीमा बल</p>", "<p>सीमा सुरक्षा बल</p>",
                                "<p>केंद्रीय रिजर्व पुलिस बल</p>", "<p>भारत-तिब्बत सीमा पुलिस</p>"],
                    solution_en: "<p>94.(a) <strong>Sashastra Seema Bal.</strong> It was Formed on 20 December 1963. Motto : Service, Security and Brotherhood. Headquarters : New Delhi. Other organization and foundation year : Border Security Force (BSF) - 1 December 1965, Central Reserve Police Force (CRPF) - 27 July 1939, Indo-Tibetan Border Police (ITBP) - 1962.</p>",
                    solution_hi: "<p>94.(a)<strong> सशस्त्र सीमा बल। </strong>इसका गठन 20 दिसंबर 1963 को हुआ था। आदर्श वाक्य: सेवा, सुरक्षा और बंधुत्व । मुख्यालय: नई दिल्ली। अन्य संगठन और स्थापना वर्ष: सीमा सुरक्षा बल (BSF) - 1 दिसंबर 1965 , केंद्रीय रिजर्व पुलिस बल (CRPF) - 27 जुलाई 1939 , भारत-तिब्बत सीमा पुलिस (ITBP) - 1962.</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "95",
                    section: "18",
                    question_en: "<p>95. Which of the following statements is/are correct<br>I &ndash; Maximum National Parks in India are located in the state of Madhya Pradesh.<br>II &ndash; Mudumalai National Park is located in Kerala.<br>III &ndash; The Salim Ali Centre for Ornithology and Natural History is located in Tamil Nadu.</p>",
                    question_hi: "<p>95. निम्नलिखित में से कौन-सा/कौन-से कथन सही है/हैं ?<br>I - भारत में अधिकतम राष्ट्रीय उद्यान मध्य प्रदेश राज्य में स्थित हैं।<br>II- मुदुमलाई (Mudumalai) राष्ट्रीय उद्यान केरल में स्थित है।<br>III - सलीम अली सेंटर फॉर ऑर्निथोलॉजी एंड नेचुरल हिस्ट्री (Salim Ali Centre for Ornithology<br>and Natural History) तमिलनाडु में स्थित है।</p>",
                    options_en: ["<p>Both I and II are correct</p>", "<p>Both I and III are correct</p>", 
                                "<p>Only III is correct</p>", "<p>Only I is correct.</p>"],
                    options_hi: ["<p>I और II दोनों सही हैं</p>", "<p>I और III दोनों सही हैं</p>",
                                "<p>केवल III सही है</p>", "<p>केवल I सही है।</p>"],
                    solution_en: "<p>95.(b)<strong> Both I and III are correct. </strong>Madhya Pradesh has the highest (11) number of national parks in India. Mudumalai National Park is located in Tamil Nadu. The Salim Ali Centre for Ornithology and Natural History (Formed in 1990) is located in Coimbatore, Tamil Nadu.</p>",
                    solution_hi: "<p>95.(b)<strong> I और III दोनों सही हैं।</strong> भारत में सबसे ज़्यादा (11) राष्ट्रीय उद्यान मध्य प्रदेश में हैं। मुदुमलाई राष्ट्रीय उद्यान तमिलनाडु में स्थित है। सलीम अली पक्षीविज्ञान एवं प्राकृतिक इतिहास केंद्र (1990 में गठित) कोयंबटूर, तमिलनाडु में स्थित है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "96",
                    section: "18",
                    question_en: "<p>96. Which of the following is NOT a mutation-based disease ?</p>",
                    question_hi: "<p>96. निम्नलिखित में से कौन-सा उत्परिवर्तन-आधारित (mutation-based) रोग नहीं है?</p>",
                    options_en: ["<p>Malaria</p>", "<p>Down syndrome</p>", 
                                "<p>Sickle cell anaemia</p>", "<p>Phenylketonuria</p>"],
                    options_hi: ["<p>मलेरिया (Malaria)</p>", "<p>डाउन सिंड्रोम (Down syndrome)</p>",
                                "<p>सिकल सेल एनीमिया (Sickle cell anaemia)</p>", "<p>फेनाइलेक्टोनुरिया (Phenylketonuria)</p>"],
                    solution_en: "<p>96.(a) <strong>Malaria</strong> is an infectious disease caused by a parasite. Down syndrome: It is caused by a chromosomal mutation, specifically trisomy 21, where an individual has an extra copy of chromosome 21. Sickle cell anemia: It is caused by the hemoglobin-&beta; gene. Phenylketonuria (PKU): PKU is caused by a mutation in the PAH gene, which affects the enzyme phenylalanine hydroxylase.</p>",
                    solution_hi: "<p>96.(a) <strong>मलेरिया </strong>एक संक्रामक रोग है जो परजीवी के कारण होता है। डाउन सिंड्रोम: यह क्रोमोसोमल उत्परिवर्तन के कारण होता है, विशेष रूप से ट्राइसॉमी 21, जहाँ किसी व्यक्ति के पास क्रोमोसोम 21 की एक अतिरिक्त प्रतिलिपि होती है। सिकल सेल एनीमिया: यह हीमोग्लोबिन-&beta; जीन के कारण होता है। फेनाइलेक्टोनुरिया (PKU): यह PAH जीन में उत्परिवर्तन के कारण होता है, जो फेनिलएलनिन हाइड्रॉक्सिलेज एंजाइम को प्रभावित करता है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "97",
                    section: "18",
                    question_en: "<p>97. Which of the following countries was the host of the ICC World Test Championship Cricket final played between India and New Zealand from 18-23 June 2021 ?</p>",
                    question_hi: "<p>97. निम्नलिखित में से कौन-सा देश 18-23 जून 2021 तक भारत और न्यूजीलैंड के बीच खेले गए&nbsp;आई.सी.सी. (ICC) विश्व टेस्ट चैम्पियनशिप क्रिकेट फाइनल का मेजबान था ?</p>",
                    options_en: ["<p>India</p>", "<p>Australia</p>", 
                                "<p>South Africa</p>", "<p>England</p>"],
                    options_hi: ["<p>भारत</p>", "<p>ऑस्ट्रेलिया</p>",
                                "<p>दक्षिण अफ्रीका</p>", "<p>इंग्लैंड</p>"],
                    solution_en: "<p>97.(d) <strong>England</strong>. ICC World Test Championship 2021: Final match was won by New Zealand by 8 wickets. Runner up - India. The 2021&ndash;2023 ICC World Test Championship - final was held at the Oval, London. Champions - Australia, Runner up - India.</p>",
                    solution_hi: "<p>97.(d) <strong>इंग्लैंड। </strong>ICC विश्व टेस्ट चैंपियनशिप 2021: फाइनल मैच न्यूजीलैंड ने 8 विकेट से जीता। उपविजेता - भारत। 2021-2023 ICC विश्व टेस्ट चैंपियनशिप - फाइनल ओवल, लंदन में आयोजित किया गया था। विजेता - ऑस्ट्रेलिया, उपविजेता - भारत।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "98",
                    section: "18",
                    question_en: "<p>98. Rotating Savings and Credit Association (ROSCA) was introduced by Shirley Ardener&nbsp;in the year ________.</p>",
                    question_hi: "<p>98. घूर्णनशील बचत एवं क्रेडिट संघ (Rotating Savings and Credit Association - ROSCA) की&nbsp;शुरुआत शर्ली आर्डेनर (Shirley Ardener) द्वारा वर्ष ______ में की गई थी।</p>",
                    options_en: ["<p>1964</p>", "<p>1970</p>", 
                                "<p>1963</p>", "<p>1955</p>"],
                    options_hi: ["<p>1964</p>", "<p>1970</p>",
                                "<p>1963</p>", "<p>1955</p>"],
                    solution_en: "<p>98.(a) <strong>1964.</strong> ROSCA is an alternative financial vehicle in which a group of individuals fills the role of an informal financial institution.</p>",
                    solution_hi: "<p>98.(a) <strong>1964. </strong>ROSCA एक वैकल्पिक वित्तीय साधन है जिसमें व्यक्तियों का एक समूह अनौपचारिक वित्तीय संस्थान की भूमिका निभाता है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "99",
                    section: "18",
                    question_en: "<p>99. What is the penalty for a wide ball in cricket ?</p>",
                    question_hi: "<p>99. क्रिकेट में वाइड बॉल (wide ball) पर क्या पेनल्टी है ?</p>",
                    options_en: ["<p>A free hit</p>", "<p>One run</p>", 
                                "<p>Six runs</p>", "<p>Four runs</p>"],
                    options_hi: ["<p>एक फ्री हिट</p>", "<p>एक रन</p>",
                                "<p>छह रन</p>", "<p>चार रन</p>"],
                    solution_en: "<p>99.(b) <strong>One run. </strong>A free hit is awarded to the batting side when the opposing team bowls a no-ball in limited-overs cricket.</p>",
                    solution_hi: "<p>99.(b) <strong>एक रन। </strong>सीमित ओवरों के क्रिकेट में जब विरोधी टीम नो-बॉल फेंकती है तो बल्लेबाजी करने वाली टीम को फ्री हिट दी जाती है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "100",
                    section: "misc",
                    question_en: "<p>100. The point in the orbit of an object (such as a satellite) orbiting the earth that is at the greatest distance from the centre of the earth is known as:</p>",
                    question_hi: "<p>100. पृथ्वी के चक्कर लगा रहे किसी पिंड (जैसे कि किसी उपग्रह) के कक्षक में बिंदु, जो पृथ्वी के केंद्र से सबसे अधिक दूरी पर है, ________कहलाता है।</p>",
                    options_en: ["<p>perigee</p>", "<p>apogee</p>", 
                                "<p>perihelion</p>", "<p>aphelion</p>"],
                    options_hi: ["<p>पेरिगी (perigee)</p>", "<p>अपोगी (apogee)</p>",
                                "<p>पेरिहिलेन (perihelion)</p>", "<p>एफेलियन (aphelion)</p>"],
                    solution_en: "<p>100.(b) <strong>Apogee. </strong>Perigee: The moon\'s orbit closest to the Earth. Perihelion: The Earth closest to the Sun on 3rd january. Aphelion: The Earth farthest from the Sun on 4th July.</p>",
                    solution_hi: "<p>100.(b) <strong>अपोगी (apogee)।</strong> उपसौर (Perigee) : पृथ्वी के सबसे निकट चंद्रमा की कक्षा। उपसौर (Perihelion): जब पृथ्वी सूर्य के सबसे निकट हो (3 जनवरी)। अपसौर (Aphelion): जब पृथ्वी, सूर्य से सर्वाधिक दूरी पर हो (4 जुलाई)।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>