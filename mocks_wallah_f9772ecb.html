<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">20:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 15</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">15</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 20 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 0,
                end: 13
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 14,
                end: 14
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "17",
                    question_en: "<p>1.<span style=\"font-family: Times;\"> The following table shows the number of trains that originate in the five different zones of the railway network of a country.</span></p>\r\n<p><span style=\"font-family: Times;\"><img src=\"https://ssccglpinnacle.com/images/que%201%20hgccchgc.png\" alt=\"\" width=\"202\" height=\"166\"></span></p>\r\n<p><span style=\"font-family: Times;\">The total number of trains that originate in Z3 and Z4 taken together is what percentage of the total number of trains that originate in all the 5 zones taken together?(Give your answer correct to 2 decimal places).</span></p>\n",
                    question_hi: "<p>1. <span style=\"font-family: ITF Devanagari;\">&#2344;&#2367;&#2350;&#2381;&#2344;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2340;&#2366;&#2354;&#2367;&#2325;&#2366;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2325;&#2367;&#2360;&#2368;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2342;&#2375;&#2358;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2325;&#2375;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2352;&#2375;&#2354;&#2357;&#2375;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2344;&#2375;&#2335;&#2357;&#2352;&#2381;&#2325;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2325;&#2375;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2346;&#2366;&#2306;&#2330;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2309;&#2354;&#2327;</span><span style=\"font-family: Times;\">-</span><span style=\"font-family: ITF Devanagari;\">&#2309;&#2354;&#2327;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;&#2379;&#2306;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2360;&#2375;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2330;&#2354;&#2344;&#2375;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2357;&#2366;&#2354;&#2368;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2335;&#2381;&#2352;&#2375;&#2344;&#2379;&#2306;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2325;&#2368;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2325;&#2379;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2342;&#2352;&#2381;&#2358;&#2366;&#2340;&#2368;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2361;&#2376;&#2404;</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/images/que%201%20hgccchgc.png\" alt=\"\" width=\"202\" height=\"166\"></p>\r\n<p><span style=\"font-family: Times;\">Z3 </span><span style=\"font-family: ITF Devanagari;\">&#2324;&#2352;</span><span style=\"font-family: Times;\"> Z4 </span><span style=\"font-family: ITF Devanagari;\">&#2325;&#2379;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2350;&#2367;&#2354;&#2366;&#2325;&#2352;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2330;&#2354;&#2344;&#2375;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2357;&#2366;&#2354;&#2368;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2335;&#2381;&#2352;&#2375;&#2344;&#2379;&#2306;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2325;&#2368;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2325;&#2369;&#2354;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Times;\">, </span><span style=\"font-family: ITF Devanagari;\">&#2360;&#2349;&#2368;</span><span style=\"font-family: Times;\"> 5 </span><span style=\"font-family: ITF Devanagari;\">&#2332;&#2379;&#2344;&#2379;&#2306;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2325;&#2379;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2350;&#2367;&#2354;&#2366;&#2325;&#2352;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2330;&#2354;&#2344;&#2375;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2357;&#2366;&#2354;&#2368;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2335;&#2381;&#2352;&#2375;&#2344;&#2379;&#2306;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2325;&#2368;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2325;&#2369;&#2354;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2325;&#2366;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2325;&#2367;&#2340;&#2344;&#2366;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2361;&#2376;</span><span style=\"font-family: Times;\">?(</span><span style=\"font-family: ITF Devanagari;\">&#2309;&#2346;&#2344;&#2366;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2313;&#2340;&#2381;&#2340;&#2352;</span><span style=\"font-family: Times;\"> 2 </span><span style=\"font-family: ITF Devanagari;\">&#2342;&#2358;&#2350;&#2354;&#2357;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2360;&#2381;&#2341;&#2366;&#2344;&#2379;&#2306;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2340;&#2325;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2342;&#2375;&#2306;</span><span style=\"font-family: Times;\">)</span><span style=\"font-family: ITF Devanagari;\">&#2404;</span></p>\n",
                    options_en: ["<p>43.49%</p>\n", "<p>31.23%</p>\n", 
                                "<p>39.47%</p>\n", "<p>45.61%</p>\n"],
                    options_hi: ["<p>43.49%</p>\n", "<p>31.23%</p>\n",
                                "<p>39.47%</p>\n", "<p>45.61%</p>\n"],
                    solution_en: "<p>1.(c)<span style=\"font-family: Times;\"> </span></p>\r\n<p><span style=\"font-family: Times;\">Total number of trains that originate in Z3 and Z4 = 830 + 670 = 1500</span></p>\r\n<p><span style=\"font-family: Times;\">Total number of trains that originate in all the 5 zones = 690 + 710 + 830 + 670 + 900 = 3800</span></p>\r\n<p><span style=\"font-family: Times;\">Required percentage = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1500</mn><mn>3800</mn></mfrac></math>&times; 100 = 39.47%</span></p>\n",
                    solution_hi: "<p>1.(c)</p>\r\n<p><span style=\"font-family: Times;\">Z3 </span><span style=\"font-family: ITF Devanagari;\">&#2324;&#2352;</span><span style=\"font-family: Times;\"> Z4 </span><span style=\"font-family: ITF Devanagari;\">&#2360;&#2375;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2330;&#2354;&#2344;&#2375;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2357;&#2366;&#2354;&#2368;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2335;&#2381;&#2352;&#2375;&#2344;&#2379;&#2306;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2325;&#2368;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2325;&#2369;&#2354;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Times;\"> = 830 +670 = 1500</span></p>\r\n<p><span style=\"font-family: ITF Devanagari;\">&#2360;&#2349;&#2368;</span><span style=\"font-family: Times;\"> 5 </span><span style=\"font-family: ITF Devanagari;\">&#2332;&#2379;&#2344;&#2379;&#2306;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2330;&#2354;&#2344;&#2375;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2357;&#2366;&#2354;&#2368;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2335;&#2381;&#2352;&#2375;&#2344;&#2379;&#2306;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2325;&#2368;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2325;&#2369;&#2354;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Times;\"> = 690 +710 + 830 + 670 +900 = 3800</span></p>\r\n<p><span style=\"font-family: ITF Devanagari;\">&#2310;&#2357;&#2358;&#2381;&#2351;&#2325;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340;</span><span style=\"font-family: Times;\">= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1500</mn><mn>3800</mn></mfrac></math>&times; 100 = 39.47%</span></p>\n",
                    correct: "c",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "2",
                    section: "17",
                    question_en: "<p>2. <span style=\"font-family: Times;\">The following pie chart shows the expenditure of a village in a given year in the different sectors of its economy.</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/images/que2%20cgdc.png\" alt=\"\" width=\"223\" height=\"254\"></p>\r\n<p><span style=\"font-family: Times;\">If the amount spent on agriculture was 8.5 crore, then find the amount spent in the IT sector.</span></p>\n",
                    question_hi: "<p>2. <span style=\"font-family: ITF Devanagari;\">&#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2346;&#2366;&#2312;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2330;&#2366;&#2352;&#2381;&#2335;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2325;&#2367;&#2360;&#2368;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2342;&#2367;&#2319;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2327;&#2319;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2357;&#2352;&#2381;&#2359;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2313;&#2360;&#2325;&#2368;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2309;&#2352;&#2381;&#2341;&#2357;&#2381;&#2351;&#2357;&#2360;&#2381;&#2341;&#2366;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2325;&#2375;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2357;&#2367;&#2349;&#2367;&#2344;&#2381;&#2344;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;&#2379;&#2306;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2319;&#2325;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2327;&#2366;&#2305;&#2357;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2325;&#2375;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2357;&#2381;&#2351;&#2351;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2325;&#2379;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2342;&#2352;&#2381;&#2358;&#2366;&#2340;&#2366;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2361;&#2376;&#2404;</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/images/que2%20cgdc.png\" alt=\"\" width=\"223\" height=\"254\"></p>\r\n<p><span style=\"font-family: ITF Devanagari;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2325;&#2371;&#2359;&#2367;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2346;&#2352;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2326;&#2352;&#2381;&#2330;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2325;&#2368;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2327;&#2312;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2352;&#2366;&#2358;&#2367;</span><span style=\"font-family: Times;\"> 8.5 </span><span style=\"font-family: ITF Devanagari;\">&#2325;&#2352;&#2379;&#2337;&#2364;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2341;&#2368;</span><span style=\"font-family: Times;\">, </span><span style=\"font-family: ITF Devanagari;\">&#2340;&#2379;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2310;&#2312;&#2335;&#2368;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2326;&#2352;&#2381;&#2330;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2325;&#2368;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2327;&#2312;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2352;&#2366;&#2358;&#2367;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2332;&#2381;&#2334;&#2366;&#2340;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2325;&#2368;&#2332;&#2367;&#2319;&#2404;</span></p>\n",
                    options_en: ["<p>7.5 crore</p>\n", "<p>10.5 crore</p>\n", 
                                "<p>8 crore</p>\n", "<p>9 crore</p>\r\n<p>&nbsp;</p>\n"],
                    options_hi: ["<p>7.5 crore</p>\n", "<p>10.5 crore</p>\n",
                                "<p>8 crore</p>\n", "<p>9 crore</p>\n"],
                    solution_en: "<p>2.(d)<span style=\"font-family: Times;\"> </span></p>\r\n<p><span style=\"font-family: Times;\">17% = 8.5 cr</span></p>\r\n<p><span style=\"font-family: Times;\">18% = 9 cr</span></p>\n",
                    solution_hi: "<p>2.(d)<span style=\"font-family: Times;\"> </span></p>\r\n<p><span style=\"font-family: Times;\">17% = 8.5 cr</span></p>\r\n<p><span style=\"font-family: Times;\">18% = 9 cr</span></p>\n",
                    correct: "d",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "3",
                    section: "17",
                    question_en: "<p>3. <span style=\"font-family: Times;\">The bar chart given below shows the total revenue (in Rs ) earned by 5 shops by selling steel bottles.</span></p>\r\n<p>&nbsp;</p>\r\n<p><img src=\"https://ssccglpinnacle.com/images/que%203.png\" alt=\"\" width=\"262\" height=\"246\"></p>\r\n<p><span style=\"font-family: Times;\">The price of each bottle is Rs 600 in all the shops. How many bottles have been sold by S2 and S3 taken together?</span></p>\n",
                    question_hi: "<p>3. <span style=\"font-family: ITF Devanagari;\">&#2344;&#2368;&#2330;&#2375;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2342;&#2367;&#2351;&#2366;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2327;&#2351;&#2366;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2348;&#2366;&#2352;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2330;&#2366;&#2352;&#2381;&#2335;</span><span style=\"font-family: Times;\"> 5 </span><span style=\"font-family: ITF Devanagari;\">&#2342;&#2369;&#2325;&#2366;&#2344;&#2379;&#2306;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2342;&#2381;&#2357;&#2366;&#2352;&#2366;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2360;&#2381;&#2335;&#2368;&#2354;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2325;&#2368;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2348;&#2379;&#2340;&#2354;&#2379;&#2306;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2325;&#2379;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2348;&#2375;&#2330;&#2325;&#2352;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2309;&#2352;&#2381;&#2332;&#2367;&#2340;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2325;&#2369;&#2354;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2352;&#2366;&#2332;&#2360;&#2381;&#2357;</span><span style=\"font-family: Times;\"> (</span><span style=\"font-family: ITF Devanagari;\">&#2352;&#2369;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Times;\">) </span><span style=\"font-family: ITF Devanagari;\">&#2325;&#2379;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2342;&#2352;&#2381;&#2358;&#2366;&#2340;&#2366;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2361;&#2376;&#2404;</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/images/que%203.png\" alt=\"\" width=\"262\" height=\"246\"></p>\r\n<p><span style=\"font-family: ITF Devanagari;\">&#2360;&#2349;&#2368;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2342;&#2369;&#2325;&#2366;&#2344;&#2379;&#2306;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2346;&#2352;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2319;&#2325;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2348;&#2379;&#2340;&#2354;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2325;&#2368;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2325;&#2368;&#2350;&#2340;</span><span style=\"font-family: Times;\"> 600 </span><span style=\"font-family: ITF Devanagari;\">&#2352;&#2369;&#2346;&#2319;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Times;\"> S2 </span><span style=\"font-family: ITF Devanagari;\">&#2324;&#2352;</span><span style=\"font-family: Times;\"> S3 </span><span style=\"font-family: ITF Devanagari;\">&#2325;&#2379;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2350;&#2367;&#2354;&#2366;&#2325;&#2352;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2325;&#2367;&#2340;&#2344;&#2368;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2348;&#2379;&#2340;&#2354;&#2375;&#2306;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2348;&#2375;&#2330;&#2368;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2327;&#2312;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2361;&#2376;&#2306;</span><span style=\"font-family: Times;\">?</span></p>\n",
                    options_en: ["<p>120</p>\n", "<p>135</p>\n", 
                                "<p>150</p>\n", "<p>165</p>\n"],
                    options_hi: ["<p>120</p>\n", "<p>135</p>\n",
                                "<p>150</p>\n", "<p>165</p>\n"],
                    solution_en: "<p>3.(c)</p>\r\n<p><span style=\"font-family: Times;\">Total revenue = S2 + S3 = 54000 + 36000 = 90000</span></p>\r\n<p><span style=\"font-family: Times;\">Total bottle =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>90000</mn><mn>600</mn></mfrac></math> = 150</span></p>\n",
                    solution_hi: "<p>3.(c)</p>\r\n<p><span style=\"font-family: ITF Devanagari;\">&#2325;&#2369;&#2354;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2352;&#2366;&#2332;&#2360;&#2381;&#2357;</span><span style=\"font-family: Times;\"> = S2 + S3 = 54000 + 36000 = 90000</span></p>\r\n<p><span style=\"font-family: ITF Devanagari;\">&#2325;&#2369;&#2354;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2348;&#2379;&#2340;&#2354;&#2375;&#2306;</span><span style=\"font-family: Times;\"> =&nbsp; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>90000</mn><mn>600</mn></mfrac></math>= 150</span></p>\n",
                    correct: "c",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "4",
                    section: "17",
                    question_en: "<p>4.<span style=\"font-family: Times;\">The following table shows the number of cars manufactured in five consecutive weeks.</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/images/mceu_5049420211675226033315.png\"></p>\r\n<p><span style=\"font-family: Times;\">How many cars were manufactured in the first four weeks, taken together?</span></p>\n",
                    question_hi: "<p>4.<span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2344;&#2367;&#2350;&#2381;&#2344;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2340;&#2366;&#2354;&#2367;&#2325;&#2366;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2354;&#2327;&#2366;&#2340;&#2366;&#2352;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2346;&#2366;&#2306;&#2330;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2361;&#2347;&#2381;&#2340;&#2379;&#2306;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2344;&#2367;&#2352;&#2381;&#2350;&#2367;&#2340;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2325;&#2366;&#2352;&#2379;&#2306;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2325;&#2368;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2342;&#2352;&#2381;&#2358;&#2366;&#2340;&#2368;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2361;&#2376;&#2404;</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/images/mceu_12155067011675225800964.png\"></p>\r\n<p><span style=\"font-family: ITF Devanagari;\">&#2346;&#2361;&#2354;&#2375;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2330;&#2366;&#2352;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2361;&#2347;&#2381;&#2340;&#2379;&#2306;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2319;&#2325;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2360;&#2366;&#2341;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2325;&#2367;&#2340;&#2344;&#2368;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2325;&#2366;&#2352;&#2379;&#2306;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2325;&#2366;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2344;&#2367;&#2352;&#2381;&#2350;&#2366;&#2339;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2325;&#2367;&#2351;&#2366;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2327;&#2351;&#2366;</span><span style=\"font-family: Times;\">?</span></p>\n",
                    options_en: ["<p>2000</p>\n", "<p>2800</p>\n", 
                                "<p>2100</p>\n", "<p>2200</p>\n"],
                    options_hi: ["<p>2000</p>\n", "<p>2800</p>\n",
                                "<p>2100</p>\n", "<p>2200</p>\n"],
                    solution_en: "<p>4.(c)</p>\r\n<p><span style=\"font-family: Times;\">Total cars = 500 + 700 + 600 + 300 = 2100</span></p>\n",
                    solution_hi: "<p>4.(c)</p>\r\n<p><span style=\"font-family: ITF Devanagari;\">&#2325;&#2369;&#2354;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2325;&#2366;&#2352;&#2375;&#2306;</span><span style=\"font-family: Times;\">= 500 + 700 + 600 + 300 = 2100</span></p>\n",
                    correct: "c",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "5",
                    section: "17",
                    question_en: "<p>5.<span style=\"font-family: Times;\"> The following bar graph shows the number of different types of animals in a zoo.</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/images/que%205.png\" alt=\"\" width=\"226\" height=\"187\"></p>\r\n<p><span style=\"font-family: Times;\">The number of deer is what percentage of the total number of animals in the zoo?</span></p>\n",
                    question_hi: "<p>5. <span style=\"font-family: ITF Devanagari;\">&#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2348;&#2366;&#2352;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2327;&#2381;&#2352;&#2366;&#2347;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2319;&#2325;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2330;&#2367;&#2337;&#2364;&#2367;&#2351;&#2366;&#2328;&#2352;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2357;&#2367;&#2349;&#2367;&#2344;&#2381;&#2344;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2346;&#2381;&#2352;&#2325;&#2366;&#2352;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2325;&#2375;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2332;&#2366;&#2344;&#2357;&#2352;&#2379;&#2306;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2325;&#2368;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2325;&#2379;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2342;&#2352;&#2381;&#2358;&#2366;&#2340;&#2366;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2361;&#2376;&#2404;</span></p>\r\n<p><span style=\"font-family: ITF Devanagari;\"><img src=\"https://ssccglpinnacle.com/images/que%205.png\" alt=\"\" width=\"226\" height=\"187\"></span></p>\r\n<p><span style=\"font-family: ITF Devanagari;\">&#2361;&#2367;&#2352;&#2339;&#2379;&#2306;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2325;&#2368;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2330;&#2367;&#2337;&#2364;&#2367;&#2351;&#2366;&#2328;&#2352;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2332;&#2366;&#2344;&#2357;&#2352;&#2379;&#2306;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2325;&#2368;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2325;&#2369;&#2354;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2325;&#2366;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2325;&#2367;&#2340;&#2344;&#2366;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2361;&#2376;</span><span style=\"font-family: Times;\">?</span></p>\n",
                    options_en: ["<p>25%</p>\n", "<p>10%</p>\n", 
                                "<p>20%</p>\n", "<p>15%</p>\n"],
                    options_hi: ["<p>25%</p>\n", "<p>10%</p>\n",
                                "<p>20%</p>\n", "<p>15%</p>\n"],
                    solution_en: "<p>5.(a)</p>\r\n<p><span style=\"font-family: Times;\">Total number of animals = 5 + 15 + 10 + 5 + 25 = 60</span></p>\r\n<p><span style=\"font-family: Times;\">Required percentage =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>15</mn><mn>60</mn></mfrac></math> &times; 100 = 25%</span></p>\n",
                    solution_hi: "<p>5.(a)</p>\r\n<p><span style=\"font-family: ITF Devanagari;\">&#2332;&#2366;&#2344;&#2357;&#2352;&#2379;&#2306;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2325;&#2368;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2325;&#2369;&#2354;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Times;\"> = 5+15+10+5+25 = 60</span></p>\r\n<p><span style=\"font-family: ITF Devanagari;\">&#2310;&#2357;&#2358;&#2381;&#2351;&#2325;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340;</span><span style=\"font-family: Times;\">= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>15</mn><mn>60</mn></mfrac></math>&times; 100 = 25%</span></p>\n",
                    correct: "a",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "6",
                    section: "17",
                    question_en: "<p>6.<span style=\"font-family: Times;\"> The following pie chart shows the number of leaves taken during a year by each of 6 different employees as percentages of the total number of leaves taken by these 6 employees, added together.</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/images/Screenshot%202023-01-31%20at%202.36.28%20PM.png\" alt=\"\" width=\"192\" height=\"186\"></p>\r\n<p><span style=\"font-family: Times;\">If the difference between the numbers of leaves taken by E2 and E5 is 14, then what is the total number of leaves taken during the year by all the 6 employees, added together?</span></p>\n",
                    question_hi: "<p>6. <span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2346;&#2366;&#2312;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2330;&#2366;&#2352;&#2381;&#2335;</span><span style=\"font-family: Times;\"> 6 </span><span style=\"font-family: ITF Devanagari;\">&#2309;&#2354;&#2327;</span><span style=\"font-family: Times;\">-</span><span style=\"font-family: ITF Devanagari;\">&#2309;&#2354;&#2327;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2325;&#2352;&#2381;&#2350;&#2330;&#2366;&#2352;&#2367;&#2351;&#2379;&#2306;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2360;&#2375;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2346;&#2381;&#2352;&#2340;&#2381;&#2351;&#2375;&#2325;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2342;&#2381;&#2357;&#2366;&#2352;&#2366;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2319;&#2325;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2357;&#2352;&#2381;&#2359;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2325;&#2375;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2342;&#2380;&#2352;&#2366;&#2344;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2354;&#2368;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2327;&#2312;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2331;&#2369;&#2335;&#2381;&#2335;&#2367;&#2351;&#2379;&#2306;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2325;&#2368;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2325;&#2379;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2311;&#2344;</span><span style=\"font-family: Times;\"> 6 </span><span style=\"font-family: ITF Devanagari;\">&#2325;&#2352;&#2381;&#2350;&#2330;&#2366;&#2352;&#2367;&#2351;&#2379;&#2306;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2342;&#2381;&#2357;&#2366;&#2352;&#2366;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2319;&#2325;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2360;&#2366;&#2341;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2332;&#2379;&#2337;&#2364;&#2375;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2327;&#2319;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2309;&#2357;&#2325;&#2366;&#2358;&#2379;&#2306;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2325;&#2368;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2325;&#2369;&#2354;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2325;&#2375;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2325;&#2375;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2352;&#2370;&#2346;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2342;&#2352;&#2381;&#2358;&#2366;&#2340;&#2366;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2361;&#2376;&#2404;</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/images/Screenshot%202023-01-31%20at%202.36.28%20PM.png\" alt=\"\" width=\"192\" height=\"186\"></p>\r\n<p><span style=\"font-family: ITF Devanagari;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Times;\"> E2 </span><span style=\"font-family: ITF Devanagari;\">&#2324;&#2352;</span><span style=\"font-family: Times;\"> E5 </span><span style=\"font-family: ITF Devanagari;\">&#2342;&#2381;&#2357;&#2366;&#2352;&#2366;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2354;&#2368;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2327;&#2312;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2346;&#2340;&#2381;&#2340;&#2367;&#2351;&#2379;&#2306;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2325;&#2368;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2325;&#2375;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2348;&#2368;&#2330;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2325;&#2366;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2309;&#2306;&#2340;&#2352;</span><span style=\"font-family: Times;\"> 14 </span><span style=\"font-family: ITF Devanagari;\">&#2361;&#2376;</span><span style=\"font-family: Times;\">, </span><span style=\"font-family: ITF Devanagari;\">&#2340;&#2379;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2360;&#2349;&#2368;</span><span style=\"font-family: Times;\"> 6 </span><span style=\"font-family: ITF Devanagari;\">&#2325;&#2352;&#2381;&#2350;&#2330;&#2366;&#2352;&#2367;&#2351;&#2379;&#2306;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2342;&#2381;&#2357;&#2366;&#2352;&#2366;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2357;&#2352;&#2381;&#2359;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2325;&#2375;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2342;&#2380;&#2352;&#2366;&#2344;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2354;&#2368;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2327;&#2312;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2331;&#2369;&#2335;&#2381;&#2335;&#2367;&#2351;&#2379;&#2306;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2325;&#2368;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2325;&#2369;&#2354;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2325;&#2381;&#2351;&#2366;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2361;&#2379;&#2327;&#2368;</span><span style=\"font-family: Times;\">?</span></p>\n",
                    options_en: ["<p>380</p>\n", "<p>350</p>\n", 
                                "<p>320</p>\n", "<p>360</p>\n"],
                    options_hi: ["<p>380</p>\n", "<p>350</p>\n",
                                "<p>320</p>\n", "<p>360</p>\n"],
                    solution_en: "<p>6.(b)</p>\r\n<p><span style=\"font-family: Times;\">E2 - E5 = 24 - 20 = 4%</span></p>\r\n<p><span style=\"font-family: Times;\">As per question,</span></p>\r\n<p><span style=\"font-family: Times;\">4% = 14</span></p>\r\n<p><span style=\"font-family: Times;\">100% = 350</span></p>\n",
                    solution_hi: "<p>6.(b)</p>\r\n<p><span style=\"font-family: Times;\">E2 - E5 = 24 - 20 = 4%</span></p>\r\n<p><span style=\"font-family: ITF Devanagari;\">&#2346;&#2381;&#2352;&#2358;&#2381;&#2344;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2325;&#2375;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2309;&#2344;&#2369;&#2360;&#2366;&#2352;</span><span style=\"font-family: Times;\">,</span></p>\r\n<p><span style=\"font-family: Times;\">4% = 14</span></p>\r\n<p><span style=\"font-family: Times;\">100% = 350</span></p>\n",
                    correct: "b",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "7",
                    section: "17",
                    question_en: "<p>7.<span style=\"font-family: Times;\"> Study the given table and answer the question that follows.</span></p>\r\n<p><span style=\"font-family: Times;\">The table shows the population of three cities </span><span style=\"font-family: Times;\">P,Q and R, as percentages of the total population of these three cities taken together. It also shows the percentage of the population above 60 years of age in each of these cities, and also the ratio of males to females below 60 years of age, in each of these cities. The total population of all the three cities taken together is 3,00,000.</span></p>\r\n<p><span style=\"font-family: Times;\"><img src=\"https://ssccglpinnacle.com/images/Screenshot%202023-01-31%20at%202.44.02%20PM.png\" alt=\"\" width=\"418\" height=\"173\"></span></p>\r\n<p><span style=\"font-family: Times;\">Find the male population of city R that is below 60 years.</span></p>\n",
                    question_hi: "<p>7. <span style=\"font-family: ITF Devanagari;\">&#2342;&#2368;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2327;&#2312;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2340;&#2366;&#2354;&#2367;&#2325;&#2366;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2325;&#2366;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2309;&#2343;&#2381;&#2351;&#2351;&#2344;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2325;&#2352;&#2375;&#2306;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2324;&#2352;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2344;&#2368;&#2330;&#2375;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2342;&#2367;&#2319;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2327;&#2319;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2346;&#2381;&#2352;&#2358;&#2381;&#2344;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2325;&#2366;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2313;&#2340;&#2381;&#2340;&#2352;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2342;&#2375;&#2306;&#2404;&#2340;&#2366;&#2354;&#2367;&#2325;&#2366;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2340;&#2368;&#2344;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2358;&#2361;&#2352;&#2379;&#2306;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2325;&#2368;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2332;&#2344;&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2342;&#2352;&#2381;&#2358;&#2366;&#2340;&#2368;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2361;&#2376;</span></p>\r\n<p><span style=\"font-family: Times;\">P, Q </span><span style=\"font-family: ITF Devanagari;\">&#2324;&#2352;</span><span style=\"font-family: Times;\"> R, </span><span style=\"font-family: ITF Devanagari;\">&#2311;&#2344;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2340;&#2368;&#2344;&#2379;&#2306;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2358;&#2361;&#2352;&#2379;&#2306;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2325;&#2368;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2325;&#2369;&#2354;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2332;&#2344;&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2325;&#2375;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2325;&#2375;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2352;&#2370;&#2346;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2346;&#2381;&#2352;&#2342;&#2352;&#2381;&#2358;&#2367;&#2340;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2325;&#2367;&#2351;&#2366;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2332;&#2366;&#2340;&#2366;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2351;&#2361;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2311;&#2344;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2346;&#2381;&#2352;&#2340;&#2381;&#2351;&#2375;&#2325;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2358;&#2361;&#2352;&#2379;&#2306;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Times;\"> 60 </span><span style=\"font-family: ITF Devanagari;\">&#2357;&#2352;&#2381;&#2359;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2360;&#2375;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2309;&#2343;&#2367;&#2325;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2310;&#2351;&#2369;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2325;&#2368;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2332;&#2344;&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2325;&#2366;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2324;&#2352;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2311;&#2344;&#2350;&#2375;&#2306;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2360;&#2375;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2346;&#2381;&#2352;&#2340;&#2381;&#2351;&#2375;&#2325;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2358;&#2361;&#2352;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Times;\"> 60 </span><span style=\"font-family: ITF Devanagari;\">&#2357;&#2352;&#2381;&#2359;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2360;&#2375;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2325;&#2350;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2310;&#2351;&#2369;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2325;&#2375;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2346;&#2369;&#2352;&#2369;&#2359;&#2379;&#2306;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2324;&#2352;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2350;&#2361;&#2367;&#2354;&#2366;&#2323;&#2306;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2325;&#2375;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2309;&#2344;&#2369;&#2346;&#2366;&#2340;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2325;&#2379;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2349;&#2368;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2342;&#2352;&#2381;&#2358;&#2366;&#2340;&#2366;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2340;&#2368;&#2344;&#2379;&#2306;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2358;&#2361;&#2352;&#2379;&#2306;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2325;&#2368;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2325;&#2369;&#2354;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2332;&#2344;&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2325;&#2379;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2350;&#2367;&#2354;&#2366;&#2325;&#2352;</span><span style=\"font-family: Times;\"> 3,00,000 </span><span style=\"font-family: ITF Devanagari;\">&#2361;&#2376;&#2404;</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/images/mceu_57821906111675226356333.png\"></p>\r\n<p><span style=\"font-family: ITF Devanagari;\">&#2358;&#2361;&#2352;</span><span style=\"font-family: Times;\"> R </span><span style=\"font-family: ITF Devanagari;\">&#2325;&#2368;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2346;&#2369;&#2352;&#2369;&#2359;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2332;&#2344;&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2332;&#2381;&#2334;&#2366;&#2340;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2325;&#2368;&#2332;&#2367;&#2319;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2332;&#2379;</span><span style=\"font-family: Times;\"> 60 </span><span style=\"font-family: ITF Devanagari;\">&#2357;&#2352;&#2381;&#2359;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2360;&#2375;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2325;&#2350;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2361;&#2376;&#2404;</span></p>\n",
                    options_en: ["<p>19800</p>\n", "<p>24600</p>\n", 
                                "<p>21600</p>\n", "<p>30000</p>\n"],
                    options_hi: ["<p>19800</p>\n", "<p>24600</p>\n",
                                "<p>21600</p>\n", "<p>30000</p>\n"],
                    solution_en: "<p>7.(c)</p>\r\n<p><span style=\"font-family: Times;\">Total population = 3,00,000</span></p>\r\n<p><span style=\"font-family: Times;\">male population of city R below 60 = 3,00,000&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&times;</mo><mfrac><mn>25</mn><mn>100</mn></mfrac><mo>&times;</mo><mfrac><mn>60</mn><mn>100</mn></mfrac><mo>&times;</mo><mfrac><mn>12</mn><mn>25</mn></mfrac></math> = 21,600</span></p>\n",
                    solution_hi: "<p>7.(c)</p>\r\n<p><span style=\"font-family: ITF Devanagari;\">&#2325;&#2369;&#2354;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2332;&#2344;&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Times;\"> = 3,00,000</span></p>\r\n<p><span style=\"font-family: ITF Devanagari;\">&#2358;&#2361;&#2352;</span><span style=\"font-family: Times;\"> R </span><span style=\"font-family: ITF Devanagari;\">&#2325;&#2368;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2346;&#2369;&#2352;&#2369;&#2359;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2332;&#2344;&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Times;\"> 60 </span><span style=\"font-family: ITF Devanagari;\">&#2360;&#2366;&#2354;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2360;&#2375;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2325;&#2350;</span><span style=\"font-family: Times;\">= 3,00,000&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&times;</mo><mfrac><mn>25</mn><mn>100</mn></mfrac><mo>&times;</mo><mfrac><mn>60</mn><mn>100</mn></mfrac><mo>&times;</mo><mfrac><mn>12</mn><mn>25</mn></mfrac></math> = 21,600</span></p>\n",
                    correct: "c",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "8",
                    section: "17",
                    question_en: "<p>8. <span style=\"font-family: Times;\">The following bar chart shows the number of bottles of green and red colours sold by 5 different shops.</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/images/Screenshot%202023-01-31%20at%202.51.42%20PM.png\" alt=\"\" width=\"301\" height=\"272\"></p>\r\n<p><span style=\"font-family: Times;\">What is the ratio of the total number of green bottles sold by the 5 shops taken together to the total number of red bottles sold by the 5 shops taken together?</span></p>\n",
                    question_hi: "<p>8. <span style=\"font-family: ITF Devanagari;\">&#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2348;&#2366;&#2352;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2330;&#2366;&#2352;&#2381;&#2335;</span><span style=\"font-family: Times;\"> 5 </span><span style=\"font-family: ITF Devanagari;\">&#2309;&#2354;&#2327;</span><span style=\"font-family: Times;\">-</span><span style=\"font-family: ITF Devanagari;\">&#2309;&#2354;&#2327;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2342;&#2369;&#2325;&#2366;&#2344;&#2379;&#2306;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2342;&#2381;&#2357;&#2366;&#2352;&#2366;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2348;&#2375;&#2330;&#2368;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2327;&#2312;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2361;&#2352;&#2368;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2324;&#2352;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2354;&#2366;&#2354;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2352;&#2306;&#2327;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2325;&#2368;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2348;&#2379;&#2340;&#2354;&#2379;&#2306;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2325;&#2368;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2325;&#2379;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2342;&#2352;&#2381;&#2358;&#2366;&#2340;&#2366;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2361;&#2376;</span></p>\r\n<p><span style=\"font-family: Times;\"><img src=\"https://ssccglpinnacle.com/images/Screenshot%202023-01-31%20at%202.51.42%20PM.png\" alt=\"\" width=\"301\" height=\"272\"></span></p>\r\n<p><span style=\"font-family: Times;\">5 </span><span style=\"font-family: ITF Devanagari;\">&#2342;&#2369;&#2325;&#2366;&#2344;&#2379;&#2306;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2342;&#2381;&#2357;&#2366;&#2352;&#2366;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2348;&#2375;&#2330;&#2368;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2327;&#2312;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2361;&#2352;&#2368;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2348;&#2379;&#2340;&#2354;&#2379;&#2306;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2325;&#2368;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2325;&#2369;&#2354;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2325;&#2366;</span><span style=\"font-family: Times;\"> 5 </span><span style=\"font-family: ITF Devanagari;\">&#2342;&#2369;&#2325;&#2366;&#2344;&#2379;&#2306;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2342;&#2381;&#2357;&#2366;&#2352;&#2366;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2348;&#2375;&#2330;&#2368;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2327;&#2312;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2354;&#2366;&#2354;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2348;&#2379;&#2340;&#2354;&#2379;&#2306;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2325;&#2368;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2325;&#2369;&#2354;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2360;&#2375;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2309;&#2344;&#2369;&#2346;&#2366;&#2340;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2325;&#2367;&#2340;&#2344;&#2366;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2361;&#2376;</span><span style=\"font-family: Times;\">?</span></p>\n",
                    options_en: ["<p>3:5</p>\n", "<p>6:11</p>\n", 
                                "<p>4:7</p>\n", "<p>5:7</p>\n"],
                    options_hi: ["<p>3:5</p>\n", "<p>6:11</p>\n",
                                "<p>4:7</p>\n", "<p>5:7</p>\n"],
                    solution_en: "<p>8.(d)</p>\r\n<p><span style=\"font-family: Times;\">total number of green bottles = 45 + 53 + 58 + 63 + 31 = 250</span></p>\r\n<p><span style=\"font-family: Times;\">total number of red bottles = 71 + 68 + 55 + 69 + 87 = 350</span></p>\r\n<p><span style=\"font-family: Times;\">Ratio = 250 : 350 = 5 : 7</span></p>\n",
                    solution_hi: "<p>8.(d)</p>\r\n<p><span style=\"font-family: ITF Devanagari;\">&#2361;&#2352;&#2368;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2348;&#2379;&#2340;&#2354;&#2379;&#2306;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2325;&#2368;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2325;&#2369;&#2354;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Times;\">= 45 + 53 + 58 + 63 + 31 = 250</span></p>\r\n<p><span style=\"font-family: ITF Devanagari;\">&#2354;&#2366;&#2354;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2348;&#2379;&#2340;&#2354;&#2379;&#2306;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2325;&#2368;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2325;&#2369;&#2354;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Times;\"> =71 + 68 + 55 + 69 + 87 = 350</span></p>\r\n<p><span style=\"font-family: ITF Devanagari;\">&#2309;&#2344;&#2369;&#2346;&#2366;&#2340;</span><span style=\"font-family: Times;\">= 250 : 350 = 5 : 7</span></p>\n",
                    correct: "d",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "9",
                    section: "17",
                    question_en: "<p>9.<span style=\"font-family: Times;\"> The pie-chart shows the distribution od the 5,00,040 employed people of a city according to their occupation in terms of the central angles of the sectors representing people engaged in the different occupations mentioned. </span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/images/Screenshot%202023-01-31%20at%202.56.03%20PM.png\" alt=\"\" width=\"223\" height=\"224\"></p>\r\n<p><span style=\"font-family: Times;\">As per the information provided above, how many people work in the service sector when compared to the number of people who engage themselves in business?</span></p>\n",
                    question_hi: "<p>9. <span style=\"font-family: ITF Devanagari;\">&#2346;&#2366;&#2312;</span><span style=\"font-family: Times;\">-</span><span style=\"font-family: ITF Devanagari;\">&#2330;&#2366;&#2352;&#2381;&#2335;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2319;&#2325;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2358;&#2361;&#2352;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2325;&#2375;</span><span style=\"font-family: Times;\"> 5,00,040 </span><span style=\"font-family: ITF Devanagari;\">&#2344;&#2367;&#2351;&#2379;&#2332;&#2367;&#2340;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2354;&#2379;&#2327;&#2379;&#2306;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2325;&#2379;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2313;&#2344;&#2325;&#2375;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2357;&#2381;&#2351;&#2357;&#2360;&#2366;&#2351;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2325;&#2375;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2309;&#2344;&#2369;&#2360;&#2366;&#2352;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2357;&#2367;&#2349;&#2367;&#2344;&#2381;&#2344;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2357;&#2381;&#2351;&#2357;&#2360;&#2366;&#2351;&#2379;&#2306;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2354;&#2327;&#2375;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2354;&#2379;&#2327;&#2379;&#2306;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2325;&#2366;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2344;&#2367;&#2343;&#2367;&#2340;&#2381;&#2357;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2325;&#2352;&#2344;&#2375;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2357;&#2366;&#2354;&#2375;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;&#2379;&#2306;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2325;&#2375;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2325;&#2375;&#2306;&#2342;&#2381;&#2352;&#2368;&#2351;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2325;&#2379;&#2339;&#2379;&#2306;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2325;&#2375;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2360;&#2306;&#2342;&#2352;&#2381;&#2349;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2357;&#2367;&#2340;&#2352;&#2339;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2342;&#2352;&#2381;&#2358;&#2366;&#2340;&#2366;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2361;&#2376;&#2404;</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/images/Screenshot%202023-01-31%20at%202.56.03%20PM.png\" alt=\"\" width=\"223\" height=\"224\"></p>\r\n<p><span style=\"font-family: ITF Devanagari;\">&#2314;&#2346;&#2352;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2342;&#2368;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2327;&#2312;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2332;&#2366;&#2344;&#2325;&#2366;&#2352;&#2368;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2325;&#2375;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2309;&#2344;&#2369;&#2360;&#2366;&#2352;</span><span style=\"font-family: Times;\">, </span><span style=\"font-family: ITF Devanagari;\">&#2360;&#2375;&#2357;&#2366;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2325;&#2366;&#2350;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2325;&#2352;&#2344;&#2375;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2357;&#2366;&#2354;&#2375;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2354;&#2379;&#2327;&#2379;&#2306;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2325;&#2368;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2325;&#2368;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2340;&#2369;&#2354;&#2344;&#2366;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2325;&#2367;&#2340;&#2344;&#2375;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2354;&#2379;&#2327;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2357;&#2381;&#2351;&#2357;&#2360;&#2366;&#2351;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2325;&#2352;&#2340;&#2375;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2361;&#2376;&#2306;</span><span style=\"font-family: Times;\">?</span></p>\n",
                    options_en: ["<p>24,492</p>\n", "<p>25,684</p>\n", 
                                "<p>25000</p>\n", "<p>25,002</p>\n"],
                    options_hi: ["<p>24,492</p>\n", "<p>25,684</p>\n",
                                "<p>25000</p>\n", "<p>25,002</p>\n"],
                    solution_en: "<p>9.(d)</p>\r\n<p><span style=\"font-family: Times;\">Difference = 12&deg; - 7&deg; = 5&deg;</span></p>\r\n<p><span style=\"font-family: Times;\">100&deg; = 5,00,040</span></p>\r\n<p><span style=\"font-family: Times;\">5&deg; = 25,002</span></p>\n",
                    solution_hi: "<p>9.(d)</p>\r\n<p><span style=\"font-family: ITF Devanagari;\">&#2309;&#2306;&#2340;&#2352;</span><span style=\"font-family: Times;\">= 12&deg; - 7&deg; = 5&deg;</span></p>\r\n<p><span style=\"font-family: Times;\">100&deg; = 5,00,040</span></p>\r\n<p><span style=\"font-family: Times;\">5&deg; = 25,002</span></p>\n",
                    correct: "d",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "10",
                    section: "17",
                    question_en: "<p>10.<span style=\"font-family: Times;\"> The following graph shows the production of wheat (in lakh tonnes) by states A,B and C in four consecutive years.</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/images/Screenshot%202023-01-31%20at%202.59.30%20PM.png\" alt=\"\" width=\"405\" height=\"297\"></p>\r\n<p><span style=\"font-family: Times;\">Find the year which shows the maximum aggregate production of wheat.</span></p>\n",
                    question_hi: "<p>10. <span style=\"font-family: ITF Devanagari;\">&#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2327;&#2381;&#2352;&#2366;&#2347;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2352;&#2366;&#2332;&#2381;&#2351;&#2379;&#2306;</span><span style=\"font-family: Times;\"> A, B </span><span style=\"font-family: ITF Devanagari;\">&#2324;&#2352;</span><span style=\"font-family: Times;\"> C </span><span style=\"font-family: ITF Devanagari;\">&#2342;&#2381;&#2357;&#2366;&#2352;&#2366;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2354;&#2327;&#2366;&#2340;&#2366;&#2352;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2330;&#2366;&#2352;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2357;&#2352;&#2381;&#2359;&#2379;&#2306;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2327;&#2375;&#2361;&#2370;&#2306;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2325;&#2366;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2313;&#2340;&#2381;&#2346;&#2366;&#2342;&#2344;</span><span style=\"font-family: Times;\"> (</span><span style=\"font-family: ITF Devanagari;\">&#2354;&#2366;&#2326;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2335;&#2344;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Times;\">) </span><span style=\"font-family: ITF Devanagari;\">&#2342;&#2367;&#2326;&#2366;&#2340;&#2366;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2361;&#2376;&#2404;</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/images/Screenshot%202023-01-31%20at%202.59.30%20PM.png\" alt=\"\" width=\"405\" height=\"297\"></p>\r\n<p><span style=\"font-family: ITF Devanagari;\">&#2357;&#2361;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2357;&#2352;&#2381;&#2359;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2332;&#2381;&#2334;&#2366;&#2340;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2325;&#2368;&#2332;&#2367;&#2319;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2332;&#2379;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2327;&#2375;&#2361;&#2370;&#2305;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2325;&#2375;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2309;&#2343;&#2367;&#2325;&#2340;&#2350;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2325;&#2369;&#2354;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2313;&#2340;&#2381;&#2346;&#2366;&#2342;&#2344;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2325;&#2379;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2342;&#2352;&#2381;&#2358;&#2366;&#2340;&#2366;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2361;&#2376;&#2404;</span></p>\n",
                    options_en: ["<p>2005</p>\n", "<p>2008</p>\n", 
                                "<p>2006</p>\n", "<p>2007</p>\n"],
                    options_hi: ["<p>2005</p>\n", "<p>2008</p>\n",
                                "<p>2006</p>\n", "<p>2007</p>\n"],
                    solution_en: "<p>10.(a)</p>\r\n<p><span style=\"font-family: Times;\">Production of wheat in 2005 = 354 + 534 + 564 = 1452</span></p>\r\n<p><span style=\"font-family: Times;\">Production of wheat in 2006 = 253 + 244 + 254 = 751</span></p>\r\n<p><span style=\"font-family: Times;\">Production of wheat in 2007 = 264 + 245 + 234 = 743</span></p>\r\n<p><span style=\"font-family: Times;\">Production of wheat in 2008 = 364 + 532 + 353 = 1249</span></p>\r\n<p><span style=\"font-family: Times;\">So, option (a) is correct.</span></p>\n",
                    solution_hi: "<p>10.(a)</p>\r\n<p><span style=\"font-family: Times;\">2005 </span><span style=\"font-family: ITF Devanagari;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2327;&#2375;&#2361;&#2370;&#2305;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2325;&#2366;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2313;&#2340;&#2381;&#2346;&#2366;&#2342;&#2344;</span><span style=\"font-family: Times;\"> = 354 +534 +564 = 1452</span></p>\r\n<p><span style=\"font-family: Times;\">2006 </span><span style=\"font-family: ITF Devanagari;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2327;&#2375;&#2361;&#2370;&#2305;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2325;&#2366;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2313;&#2340;&#2381;&#2346;&#2366;&#2342;&#2344;</span><span style=\"font-family: Times;\"> = 253 +244 +254 = 751</span></p>\r\n<p><span style=\"font-family: Times;\">2007 </span><span style=\"font-family: ITF Devanagari;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2327;&#2375;&#2361;&#2370;&#2305;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2325;&#2366;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2313;&#2340;&#2381;&#2346;&#2366;&#2342;&#2344;</span><span style=\"font-family: Times;\"> = 264 +245 +234 = 743</span></p>\r\n<p><span style=\"font-family: Times;\">2008 </span><span style=\"font-family: ITF Devanagari;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2327;&#2375;&#2361;&#2370;&#2305;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2325;&#2366;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2313;&#2340;&#2381;&#2346;&#2366;&#2342;&#2344;</span><span style=\"font-family: Times;\"> = 364 +532 +353 = 1249</span></p>\r\n<p><span style=\"font-family: ITF Devanagari;\">&#2340;&#2379;</span><span style=\"font-family: Times;\">, </span><span style=\"font-family: ITF Devanagari;\">&#2357;&#2367;&#2325;&#2354;&#2381;&#2346;</span><span style=\"font-family: Times;\"> (a) </span><span style=\"font-family: ITF Devanagari;\">&#2360;&#2361;&#2368;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2361;&#2376;&#2404;</span></p>\n",
                    correct: "a",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "11",
                    section: "17",
                    question_en: "<p>11. <span style=\"font-family: Times;\">Study the given pie-chart and answer the question that follows.</span></p>\r\n<p><span style=\"font-family: Times;\">The pie-chart shows the number of females students in six different colleges P,Q,R,S,T and U, as percentage of the total number of female students in all these six colleges,taken together.</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/images/Screenshot%202023-01-31%20at%203.02.10%20PM.png\" alt=\"\" width=\"268\" height=\"243\"></p>\r\n<p><span style=\"font-family: Times;\">What is the sum of the measures of the central angles of the sectors corresponding to the number of girls in college Q and U, taken together?</span></p>\n",
                    question_hi: "<p>11.<span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2342;&#2367;&#2319;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2327;&#2319;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2346;&#2366;&#2312;</span><span style=\"font-family: Times;\">-</span><span style=\"font-family: ITF Devanagari;\">&#2330;&#2366;&#2352;&#2381;&#2335;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2325;&#2366;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2309;&#2343;&#2381;&#2351;&#2351;&#2344;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2325;&#2352;&#2375;&#2306;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2324;&#2352;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2344;&#2368;&#2330;&#2375;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2342;&#2367;&#2319;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2327;&#2319;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2346;&#2381;&#2352;&#2358;&#2381;&#2344;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2325;&#2366;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2313;&#2340;&#2381;&#2340;&#2352;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2342;&#2375;&#2306;&#2404;</span></p>\r\n<p><span style=\"font-family: ITF Devanagari;\">&#2346;&#2366;&#2312;</span><span style=\"font-family: Times;\">-</span><span style=\"font-family: ITF Devanagari;\">&#2330;&#2366;&#2352;&#2381;&#2335;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2331;&#2361;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2309;&#2354;&#2327;</span><span style=\"font-family: Times;\">-</span><span style=\"font-family: ITF Devanagari;\">&#2309;&#2354;&#2327;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2325;&#2377;&#2354;&#2375;&#2332;&#2379;&#2306;</span><span style=\"font-family: Times;\"> P, Q, R, S, T </span><span style=\"font-family: ITF Devanagari;\">&#2324;&#2352;</span><span style=\"font-family: Times;\"> U </span><span style=\"font-family: ITF Devanagari;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2350;&#2361;&#2367;&#2354;&#2366;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2331;&#2366;&#2340;&#2381;&#2352;&#2379;&#2306;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2325;&#2368;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2325;&#2379;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2311;&#2344;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2360;&#2349;&#2368;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2331;&#2361;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2325;&#2377;&#2354;&#2375;&#2332;&#2379;&#2306;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2325;&#2369;&#2354;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2350;&#2367;&#2354;&#2366;&#2325;&#2352;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2350;&#2361;&#2367;&#2354;&#2366;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2331;&#2366;&#2340;&#2381;&#2352;&#2379;&#2306;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2325;&#2368;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2325;&#2375;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2325;&#2375;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2352;&#2370;&#2346;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2342;&#2352;&#2381;&#2358;&#2366;&#2340;&#2366;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2361;&#2376;&#2404;</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/images/Screenshot%202023-01-31%20at%203.02.10%20PM.png\" alt=\"\" width=\"268\" height=\"243\"></p>\r\n<p><span style=\"font-family: ITF Devanagari;\">&#2325;&#2377;&#2354;&#2375;&#2332;</span><span style=\"font-family: Times;\"> Q </span><span style=\"font-family: ITF Devanagari;\">&#2324;&#2352;</span><span style=\"font-family: Times;\"> U </span><span style=\"font-family: ITF Devanagari;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2319;&#2325;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2360;&#2366;&#2341;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2354;&#2368;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2327;&#2312;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2354;&#2337;&#2364;&#2325;&#2367;&#2351;&#2379;&#2306;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2325;&#2368;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2325;&#2375;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2360;&#2306;&#2327;&#2340;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2360;&#2375;&#2325;&#2381;&#2335;&#2352;&#2379;&#2306;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2325;&#2375;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2325;&#2375;&#2306;&#2342;&#2381;&#2352;&#2368;&#2351;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2325;&#2379;&#2339;&#2379;&#2306;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2325;&#2375;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2350;&#2366;&#2346;&#2379;&#2306;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2325;&#2366;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2351;&#2379;&#2327;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2325;&#2367;&#2340;&#2344;&#2366;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2361;&#2376;</span><span style=\"font-family: Times;\">?</span></p>\n",
                    options_en: ["<p>96&deg;</p>\n", "<p>108&deg;</p>\n", 
                                "<p>97.2&deg;</p>\n", "<p>86.4&deg;</p>\n"],
                    options_hi: ["<p>96 <span style=\"font-family: ITF Devanagari;\">&#2337;&#2367;&#2327;&#2381;&#2352;&#2368;</span></p>\n", "<p>108 <span style=\"font-family: ITF Devanagari;\">&#2337;&#2367;&#2327;&#2381;&#2352;&#2368;</span></p>\n",
                                "<p>97.2 <span style=\"font-family: ITF Devanagari;\">&#2337;&#2367;&#2327;&#2381;&#2352;&#2368;</span></p>\n", "<p>86.4 <span style=\"font-family: ITF Devanagari;\">&#2337;&#2367;&#2327;&#2381;&#2352;&#2368;</span></p>\n"],
                    solution_en: "<p>11.(c)</p>\r\n<p><span style=\"font-family: Times;\">Percentage of girls in Q and U = 15% + 12% = 27%</span></p>\r\n<p><span style=\"font-family: Times;\">100% = 360</span></p>\r\n<p><span style=\"font-family: Times;\">27% = 97.2 </span></p>\n",
                    solution_hi: "<p>11.(c)</p>\r\n<p><span style=\"font-family: Times;\">Q </span><span style=\"font-family: ITF Devanagari;\">&#2324;&#2352;</span><span style=\"font-family: Times;\"> U </span><span style=\"font-family: ITF Devanagari;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2354;&#2337;&#2364;&#2325;&#2367;&#2351;&#2379;&#2306;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2325;&#2366;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340;</span><span style=\"font-family: Times;\"> = 15% +12% = 27%</span></p>\r\n<p><span style=\"font-family: Times;\">100% = 360</span></p>\r\n<p><span style=\"font-family: ITF Devanagari;\">&#2340;&#2379;</span><span style=\"font-family: Times;\">, 27% = 97.2</span></p>\n",
                    correct: "c",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "12",
                    section: "17",
                    question_en: "<p>12.<span style=\"font-family: Times;\"> The following table shows the number of trucks of 6 different brands sold from a showroom during a given period of time.</span></p>\r\n<p><span style=\"font-family: Times;\"><img src=\"https://ssccglpinnacle.com/images/Screenshot%202023-01-31%20at%203.05.22%20PM.png\" alt=\"\" width=\"184\" height=\"235\"></span></p>\r\n<p><span style=\"font-family: Times;\">There are 8 tyres on each truck, irrespective of the brand of the truck. What is the average number of tyres per brand?</span></p>\n",
                    question_hi: "<p>12.<span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2344;&#2367;&#2350;&#2381;&#2344;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2340;&#2366;&#2354;&#2367;&#2325;&#2366;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2319;&#2325;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2344;&#2367;&#2358;&#2381;&#2330;&#2367;&#2340;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2309;&#2357;&#2343;&#2367;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2325;&#2375;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2342;&#2380;&#2352;&#2366;&#2344;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2319;&#2325;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2358;&#2379;&#2352;&#2370;&#2350;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2360;&#2375;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2348;&#2375;&#2330;&#2375;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2327;&#2319;</span><span style=\"font-family: Times;\"> 6 </span><span style=\"font-family: ITF Devanagari;\">&#2357;&#2367;&#2349;&#2367;&#2344;&#2381;&#2344;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2348;&#2381;&#2352;&#2366;&#2306;&#2337;&#2379;&#2306;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2325;&#2375;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2335;&#2381;&#2352;&#2325;&#2379;&#2306;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2325;&#2368;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2325;&#2379;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2342;&#2352;&#2381;&#2358;&#2366;&#2340;&#2368;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2361;&#2376;&#2404;</span></p>\r\n<p><span style=\"font-family: ITF Devanagari;\"><img src=\"https://ssccglpinnacle.com/images/Screenshot%202023-01-31%20at%203.05.22%20PM.png\" alt=\"\" width=\"184\" height=\"235\"></span></p>\r\n<p><span style=\"font-family: ITF Devanagari;\">&#2335;&#2381;&#2352;&#2325;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2325;&#2375;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2348;&#2381;&#2352;&#2366;&#2306;&#2337;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2325;&#2368;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2346;&#2352;&#2357;&#2366;&#2361;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2325;&#2367;&#2319;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2348;&#2367;&#2344;&#2366;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2346;&#2381;&#2352;&#2340;&#2381;&#2351;&#2375;&#2325;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2335;&#2381;&#2352;&#2325;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2346;&#2352;</span><span style=\"font-family: Times;\"> 8 </span><span style=\"font-family: ITF Devanagari;\">&#2335;&#2366;&#2351;&#2352;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2361;&#2379;&#2340;&#2375;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2361;&#2376;&#2306;&#2404;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2346;&#2381;&#2352;&#2340;&#2367;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2348;&#2381;&#2352;&#2366;&#2306;&#2337;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2335;&#2366;&#2351;&#2352;&#2379;&#2306;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2325;&#2368;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2324;&#2360;&#2340;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2325;&#2381;&#2351;&#2366;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2361;&#2376;</span><span style=\"font-family: Times;\">?</span></p>\n",
                    options_en: ["<p>280</p>\n", "<p>300</p>\n", 
                                "<p>260</p>\n", "<p>240</p>\n"],
                    options_hi: ["<p>280</p>\n", "<p>300</p>\n",
                                "<p>260</p>\n", "<p>240</p>\n"],
                    solution_en: "<p>12.(a)</p>\r\n<p><span style=\"font-family: Times;\">Average =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>8</mn><mo>&times;</mo><mfenced><mrow><mn>45</mn><mo>+</mo><mn>35</mn><mo>+</mo><mn>36</mn><mo>+</mo><mn>44</mn><mo>+</mo><mn>28</mn><mo>+</mo><mn>22</mn></mrow></mfenced></mrow><mn>6</mn></mfrac></math> = 280</span></p>\n",
                    solution_hi: "<p>12.(a)</p>\r\n<p><span style=\"font-family: ITF Devanagari;\">&#2324;&#2360;&#2340;</span><span style=\"font-family: Times;\">=&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>8</mn><mo>&times;</mo><mfenced><mrow><mn>45</mn><mo>+</mo><mn>35</mn><mo>+</mo><mn>36</mn><mo>+</mo><mn>44</mn><mo>+</mo><mn>28</mn><mo>+</mo><mn>22</mn></mrow></mfenced></mrow><mn>6</mn></mfrac></math> = 280</span></p>\n",
                    correct: "a",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "13",
                    section: "17",
                    question_en: "<p>13.<span style=\"font-family: Times;\">the following bar chart shows the revenue(in thousands rupees) generated from the sale of 5 different objects O1 to O5.</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/images/Screenshot%202023-01-31%20at%204.06.04%20PM.png\" alt=\"\" width=\"325\" height=\"266\"></p>\r\n<p><span style=\"font-family: Times;\">The following bar chart shows the profit percentage gained through the sale of these 5 objects.</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/images/Screenshot%202023-01-31%20at%204.10.37%20PM.png\" alt=\"\" width=\"236\" height=\"174\"></p>\r\n<p><span style=\"font-family: Times;\">Profit percentage= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>R</mi><mi>e</mi><mi>v</mi><mi>e</mi><mi>n</mi><mi>u</mi><mi>e</mi><mo>-</mo><mi>cos</mi><mi>t</mi></mrow><mrow><mi>cos</mi><mi>t</mi></mrow></mfrac><mo>&times;</mo></math>100</span></p>\r\n<p><span style=\"font-family: Times;\">What is the cost incurred in the procurement and sale of O1</span></p>\n",
                    question_hi: "<p>13.<span style=\"font-family: ITF Devanagari;\">&#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2348;&#2366;&#2352;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2330;&#2366;&#2352;&#2381;&#2335;</span><span style=\"font-family: Times;\"> 5 </span><span style=\"font-family: ITF Devanagari;\">&#2357;&#2367;&#2349;&#2367;&#2344;&#2381;&#2344;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2357;&#2360;&#2381;&#2340;&#2369;&#2323;&#2306;</span><span style=\"font-family: Times;\"> O1 </span><span style=\"font-family: ITF Devanagari;\">&#2360;&#2375;</span><span style=\"font-family: Times;\"> O5 </span><span style=\"font-family: ITF Devanagari;\">&#2325;&#2368;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2348;&#2367;&#2325;&#2381;&#2352;&#2368;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2360;&#2375;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2313;&#2340;&#2381;&#2346;&#2344;&#2381;&#2344;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2352;&#2366;&#2332;&#2360;&#2381;&#2357;</span><span style=\"font-family: Times;\"> (</span><span style=\"font-family: ITF Devanagari;\">&#2361;&#2332;&#2366;&#2352;&#2379;&#2306;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2352;&#2369;&#2346;&#2351;&#2375;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Times;\">) </span><span style=\"font-family: ITF Devanagari;\">&#2325;&#2379;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2342;&#2352;&#2381;&#2358;&#2366;&#2340;&#2366;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2361;&#2376;&#2404;</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/images/Screenshot%202023-01-31%20at%204.06.04%20PM.png\" alt=\"\" width=\"325\" height=\"266\"></p>\r\n<p><span style=\"font-family: ITF Devanagari;\">&#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2348;&#2366;&#2352;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2330;&#2366;&#2352;&#2381;&#2335;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2311;&#2344;</span><span style=\"font-family: Times;\"> 5 </span><span style=\"font-family: ITF Devanagari;\">&#2357;&#2360;&#2381;&#2340;&#2369;&#2323;&#2306;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2325;&#2368;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2348;&#2367;&#2325;&#2381;&#2352;&#2368;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2360;&#2375;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2346;&#2381;&#2352;&#2366;&#2346;&#2381;&#2340;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2354;&#2366;&#2349;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2325;&#2379;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2342;&#2352;&#2381;&#2358;&#2366;&#2340;&#2366;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2361;&#2376;&#2404;</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/images/Screenshot%202023-01-31%20at%204.10.37%20PM.png\" alt=\"\" width=\"236\" height=\"174\"></p>\r\n<p><span style=\"font-family: ITF Devanagari;\">&#2354;&#2366;&#2349;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340;</span><span style=\"font-family: Times;\"> =&nbsp;<math style=\"font-family:Verdana\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2352;&#2366;&#2332;&#2360;&#2381;&#2357;</mi><mo>-</mo><mi>&#2354;&#2366;&#2327;&#2340;</mi></mrow><mi>&#2354;&#2366;&#2327;&#2340;</mi></mfrac><mo>&times;</mo></math>100</span></p>\r\n<p><span style=\"font-family: Times;\">O1 . </span><span style=\"font-family: ITF Devanagari;\">&#2325;&#2368;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2326;&#2352;&#2368;&#2342;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2324;&#2352;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2348;&#2367;&#2325;&#2381;&#2352;&#2368;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2325;&#2367;&#2340;&#2344;&#2368;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2354;&#2366;&#2327;&#2340;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2310;&#2312;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2361;&#2376;</span><span style=\"font-family: Times;\">?</span></p>\n",
                    options_en: ["<p>140000</p>\n", "<p>12000</p>\n", 
                                "<p>150000</p>\n", "<p>16000</p>\n"],
                    options_hi: ["<p>140000</p>\n", "<p>120000</p>\n",
                                "<p>150000</p>\n", "<p>160000</p>\n"],
                    solution_en: "<p>13.(c)</p>\r\n<p><span style=\"font-family: Times;\">Cost incurred in the procurement and sale of O1=<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>18000</mn><mn>120</mn></mfrac><mo>&times;</mo></math>100=150000</span></p>\n",
                    solution_hi: "<p>13.(c)</p>\r\n<p><span style=\"font-family: Times;\">O1 </span><span style=\"font-family: ITF Devanagari;\">&#2325;&#2368;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2326;&#2352;&#2368;&#2342;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2324;&#2352;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2348;&#2367;&#2325;&#2381;&#2352;&#2368;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2361;&#2379;&#2344;&#2375;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2357;&#2366;&#2354;&#2368;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2354;&#2366;&#2327;&#2340;</span><span style=\"font-family: Times;\">= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>180000</mn><mn>120</mn></mfrac></math>&times; 100=150000</span></p>\n",
                    correct: "c",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "14",
                    section: "17",
                    question_en: "<p>14. <span style=\"font-family: Times;\">The following table shows the number of battles of 6 different colors sold by a shop in a months.</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/images/Screenshot%202023-01-31%20at%204.15.48%20PM.png\" alt=\"\" width=\"167\" height=\"207\"></p>\r\n<p><span style=\"font-family: Times;\">The total number of bottles of C4 and C5 sold in that month is approximately what percentage of the total number of bottles of all the 6 colours taken together sold in that month?</span></p>\n",
                    question_hi: "<p>14. <span style=\"font-family: ITF Devanagari;\">&#2344;&#2367;&#2350;&#2381;&#2344;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2340;&#2366;&#2354;&#2367;&#2325;&#2366;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2319;&#2325;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2350;&#2361;&#2368;&#2344;&#2375;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2319;&#2325;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2342;&#2369;&#2325;&#2366;&#2344;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2342;&#2381;&#2357;&#2366;&#2352;&#2366;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2348;&#2375;&#2330;&#2368;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2327;&#2312;</span><span style=\"font-family: Times;\"> 6 </span><span style=\"font-family: ITF Devanagari;\">&#2309;&#2354;&#2327;</span><span style=\"font-family: Times;\">-</span><span style=\"font-family: ITF Devanagari;\">&#2309;&#2354;&#2327;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2352;&#2306;&#2327;&#2379;&#2306;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2325;&#2368;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2354;&#2337;&#2364;&#2366;&#2311;&#2351;&#2379;&#2306;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2325;&#2368;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2325;&#2379;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2342;&#2352;&#2381;&#2358;&#2366;&#2340;&#2368;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2361;&#2376;&#2404;</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/images/Screenshot%202023-01-31%20at%204.15.48%20PM.png\" alt=\"\" width=\"167\" height=\"207\"></p>\r\n<p><span style=\"font-family: ITF Devanagari;\">&#2313;&#2360;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2350;&#2361;&#2368;&#2344;&#2375;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2348;&#2375;&#2330;&#2368;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2327;&#2312;</span><span style=\"font-family: Times;\"> C4 </span><span style=\"font-family: ITF Devanagari;\">&#2324;&#2352;</span><span style=\"font-family: Times;\"> C5 </span><span style=\"font-family: ITF Devanagari;\">&#2325;&#2368;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2348;&#2379;&#2340;&#2354;&#2379;&#2306;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2325;&#2368;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2325;&#2369;&#2354;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2313;&#2360;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2350;&#2361;&#2368;&#2344;&#2375;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2348;&#2375;&#2330;&#2368;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2327;&#2312;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2360;&#2349;&#2368;</span><span style=\"font-family: Times;\"> 6 </span><span style=\"font-family: ITF Devanagari;\">&#2352;&#2306;&#2327;&#2379;&#2306;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2325;&#2368;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2348;&#2379;&#2340;&#2354;&#2379;&#2306;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2325;&#2368;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2325;&#2369;&#2354;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2325;&#2366;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2354;&#2327;&#2349;&#2327;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2325;&#2367;&#2340;&#2344;&#2366;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2361;&#2376;</span><span style=\"font-family: Times;\">?</span></p>\n",
                    options_en: ["<p>21.33%</p>\n", "<p>24.13%</p>\n", 
                                "<p>29.17%</p>\n", "<p>32.66%</p>\n"],
                    options_hi: ["<p>21.33%</p>\n", "<p><span style=\"font-family: Times;\">24.13%</span></p>\n",
                                "<p><span style=\"font-family: Times;\">29.17%</span></p>\n", "<p>32.66%</p>\n"],
                    solution_en: "<p>14.(c)</p>\r\n<p><span style=\"font-family: Times;\">C4 + C5 = 42 + 28 = 70</span></p>\r\n<p><span style=\"font-family: Times;\">C1 + C2 + C3 + C4 + C5 + C6 = 46 + 34 + 50 + 42 + 28 + 40 = 240</span></p>\r\n<p><span style=\"font-family: Times;\">Required percentage = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>70</mn><mn>240</mn></mfrac><mo>&times;</mo><mn>100</mn></math>= 29.17 %</span></p>\n",
                    solution_hi: "<p>14.(c)</p>\r\n<p><span style=\"font-family: Times;\">C4 + C5 = 42 + 28 = 70</span></p>\r\n<p><span style=\"font-family: Times;\">C1 + C2 + C3 + C4 + C5 + C6 = 46 + 34 + 50 + 42 + 28 + 40 = 240</span></p>\r\n<p><span style=\"font-family: ITF Devanagari;\">&#2310;&#2357;&#2358;&#2381;&#2351;&#2325;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340;</span><span style=\"font-family: Times;\">= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>70</mn><mn>240</mn></mfrac><mo>&times;</mo><mn>100</mn></math>= 29.17 %</span></p>\n",
                    correct: "c",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "15",
                    section: "misc",
                    question_en: "<p>15. <span style=\"font-family: Times;\">The following pie chart shows the number of emails (in hundreds) received by these 8 persons , P1 to P8.</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/images/Screenshot%202023-01-31%20at%204.22.07%20PM.png\" alt=\"\" width=\"200\" height=\"193\"></p>\r\n<p><span style=\"font-family: Times;\">P5 and P8 together have received what percentage of the total emails received by these 8 persons taken together?</span></p>\n",
                    question_hi: "<p>15.<span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2346;&#2366;&#2312;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2330;&#2366;&#2352;&#2381;&#2335;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2311;&#2344;</span><span style=\"font-family: Times;\"> 8 </span><span style=\"font-family: ITF Devanagari;\">&#2357;&#2381;&#2351;&#2325;&#2381;&#2340;&#2367;&#2351;&#2379;&#2306;</span><span style=\"font-family: Times;\">, P1 </span><span style=\"font-family: ITF Devanagari;\">&#2360;&#2375;</span><span style=\"font-family: Times;\"> P8 </span><span style=\"font-family: ITF Devanagari;\">&#2342;&#2381;&#2357;&#2366;&#2352;&#2366;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2346;&#2381;&#2352;&#2366;&#2346;&#2381;&#2340;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2312;&#2350;&#2375;&#2354;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2325;&#2368;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Times;\"> (</span><span style=\"font-family: ITF Devanagari;\">&#2360;&#2376;&#2325;&#2337;&#2364;&#2379;&#2306;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Times;\">) </span><span style=\"font-family: ITF Devanagari;\">&#2325;&#2379;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2342;&#2352;&#2381;&#2358;&#2366;&#2340;&#2366;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2361;&#2376;&#2404;</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/images/Screenshot%202023-01-31%20at%204.22.07%20PM.png\" alt=\"\" width=\"200\" height=\"193\"></p>\r\n<p><span style=\"font-family: Times;\">P5 </span><span style=\"font-family: ITF Devanagari;\">&#2324;&#2352;</span><span style=\"font-family: Times;\"> P8 </span><span style=\"font-family: ITF Devanagari;\">&#2344;&#2375;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2350;&#2367;&#2354;&#2325;&#2352;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2311;&#2344;</span><span style=\"font-family: Times;\"> 8 </span><span style=\"font-family: ITF Devanagari;\">&#2357;&#2381;&#2351;&#2325;&#2381;&#2340;&#2367;&#2351;&#2379;&#2306;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2342;&#2381;&#2357;&#2366;&#2352;&#2366;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2346;&#2381;&#2352;&#2366;&#2346;&#2381;&#2340;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2325;&#2369;&#2354;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2312;&#2350;&#2375;&#2354;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2325;&#2366;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2325;&#2367;&#2340;&#2344;&#2366;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2346;&#2381;&#2352;&#2366;&#2346;&#2381;&#2340;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2325;&#2367;&#2351;&#2366;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2361;&#2376;</span><span style=\"font-family: Times;\">?</span></p>\n",
                    options_en: ["<p>19%</p>\n", "<p>21%</p>\n", 
                                "<p>16%</p>\n", "<p>17%</p>\n"],
                    options_hi: ["<p>19%</p>\n", "<p>21%</p>\n",
                                "<p>16%</p>\n", "<p>17%</p>\n"],
                    solution_en: "<p>15.(d)</p>\r\n<p><span style=\"font-family: Times;\">Total mails = 43 + 57 + 61 + 39 + 24 + 56 + 76 + 44 = 400</span></p>\r\n<p><span style=\"font-family: Times;\">P5 + P8 = 24 + 44 = 68</span></p>\r\n<p><span style=\"font-family: Times;\">&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>68</mn><mn>400</mn></mfrac><mo>&times;</mo><mn>100</mn></math> = 17 %</span></p>\n",
                    solution_hi: "<p>15.(d)</p>\r\n<p><span style=\"font-family: ITF Devanagari;\">&#2325;&#2369;&#2354;</span><span style=\"font-family: Times;\"> </span><span style=\"font-family: ITF Devanagari;\">&#2350;&#2375;&#2354;</span><span style=\"font-family: Times;\"> = 43 + 57 + 61 + 39 + 24 + 56 + 76 + 44 = 400</span></p>\r\n<p><span style=\"font-family: Times;\">P5 + P8 = 24 + 44 = 68</span></p>\r\n<p><span style=\"font-family: Times;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>68</mn><mn>400</mn></mfrac><mo>&times;</mo><mn>100</mn></math> = 17 %</span></p>\n",
                    correct: "d",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>