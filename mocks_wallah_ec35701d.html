<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">6:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 15</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">15</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 6 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["7"] = {
                name: "English",
                start: 0,
                end: 13
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="7">English</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 14,
                end: 14
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "7",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">1. </span><span style=\"font-family: Cambria Math;\">Select the most appropriate \'one word\' for the expression given below.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> One who has persistent neurotic impulse to steal</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">1. </span><span style=\"font-family: Cambria Math;\">Select the most appropriate \'one word\' for the expression given below.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> One who has persistent neurotic impulse to steal</span></p>\n",
                    options_en: ["<p><span style=\"font-family: Cambria Math;\"> Connoisseur</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">Kleptomaniac</span></p>\n", 
                                "<p><span style=\"font-family: Cambria Math;\">Hypocrite</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><span style=\"font-family: Cambria Math;\">Iconoclast</span></p>\n"],
                    options_hi: ["<p><span style=\"font-family: Cambria Math;\"> Connoisseur</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">Kleptomaniac</span></p>\n",
                                "<p><span style=\"font-family: Cambria Math;\">Hypocrite</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><span style=\"font-family: Cambria Math;\">Iconoclast</span></p>\n"],
                    solution_en: "<p>1.(b)</p>\r\n<p><span style=\"font-family: Cambria Math;\">Kleptomaniac-</span><span style=\"font-family: Cambria Math;\"> one who has persistent neurotic im</span><span style=\"font-family: Cambria Math;\">pulse to steal</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Connoisseur - a person who knows a lot about art, good food, music, etc.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Hypocrite -a person who pretends to have moral standards or opinions which he/she does not really have. </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Iconoclast - a person who does not believe in and is opposed to</span><span style=\"font-family: Cambria Math;\"> accepted ideas, traditions, etc.</span></p>\n",
                    solution_hi: "<p>1.(b)</p>\r\n<p><span style=\"font-family: Cambria Math;\">Kleptomaniac -</span><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Cambria Math;\">&#2330;&#2379;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2352;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2368;&#2350;&#2366;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\">)- Compulsion to steal </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Connoisseur (</span><span style=\"font-family: Cambria Math;\">&#2357;&#2367;&#2358;&#2375;&#2359;&#2332;&#2381;&#2334;</span><span style=\"font-family: Cambria Math;\">) - a person who knows a lot about art, good food, music, etc.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Hypocrite (</span><span style=\"font-family: Cambria Math;\">&#2346;&#2366;&#2326;&#2306;&#2337;&#2368;</span><span style=\"font-family: Cambria Math;\">) -a person who pretends to have moral standards or opinions which he/she does not really have.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Iconoclast (</span><span style=\"font-family: Cambria Math;\">&#2350;&#2370;&#2352;&#2381;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\">)- a person who does not believe in and is opposed to accepted ideas, traditions, etc.</span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "7",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">2. </span><span style=\"font-family: Cambria Math;\">Choose the word that can substitute the given group of words. </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Study of heavenly bodies</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">2. </span><span style=\"font-family: Cambria Math;\">Choose the word that can substitute the given group of words. </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Study of heavenly bodies</span></p>\n",
                    options_en: ["<p>Numerology</p>\n", "<p>Astrology</p>\n", 
                                "<p>Stargazing</p>\n", "<p>Astronomy</p>\n"],
                    options_hi: ["<p>Nume<span style=\"font-family: Cambria Math;\">rology </span></p>\n", "<p>Astrology</p>\n",
                                "<p>Stargazing</p>\n", "<p>Astronomy</p>\n"],
                    solution_en: "<p>2.(d)</p>\r\n<p><span style=\"font-family: Cambria Math;\">Astronomy</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">- Study of heavenly bodies </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Stargazing - observe the stars.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Astrology - the study of the positions and movements of the stars and planets and the way that some people bel</span><span style=\"font-family: Cambria Math;\">ieve they affect people and events</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Numerology -the use of numbers to tell somebody what will happen in the future</span></p>\n",
                    solution_hi: "<p>2.(d)</p>\r\n<p><span style=\"font-family: Cambria Math;\">Astronomy (</span><span style=\"font-family: Cambria Math;\">&#2326;&#2327;&#2379;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2367;&#2332;&#2381;&#2334;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\">)- Study of heavenly bodies</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Stargazing (</span><span style=\"font-family: Cambria Math;\">&#2357;&#2381;&#2351;&#2352;&#2381;&#2341;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2366;&#2340;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2379;&#2330;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\">)</span><span style=\"font-family: Cambria Math;\"> - observe the stars.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Astrology (</span><span style=\"font-family: Cambria Math;\">&#2332;&#2381;&#2351;&#2379;&#2340;&#2367;&#2359;</span><span style=\"font-family: Cambria Math;\">) - the study of the positions and movements of the stars and planets and the way that some people believe they affect people and events</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Numerology (</span><span style=\"font-family: Cambria Math;\">&#2309;&#2306;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2358;&#2366;&#2360;&#2381;&#2340;&#2381;&#2352;</span><span style=\"font-family: Cambria Math;\"> )-the use of numbers to tell somebody what will happen in the future</span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "7",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">3</span><span style=\"font-family: Cambria Math;\">.C</span><span style=\"font-family: Cambria Math;\">hoose the word that can substitute the given group of words. </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">One who hates institution of marriage</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">3</span><span style=\"font-family: Cambria Math;\">.Choose the word that can substitute the given group of words. </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">One who hates institution of marriage</span></p>\n",
                    options_en: ["<p>Coquette</p>\n", "<p>Pedantic</p>\n", 
                                "<p>Polyglot</p>\n", "<p>Misogamist</p>\n"],
                    options_hi: ["<p>Coquette</p>\n", "<p>Pedantic</p>\n",
                                "<p>Polyglot</p>\n", "<p>Misogamist</p>\n"],
                    solution_en: "<p>3.(d)</p>\r\n<p><span style=\"font-family: Cambria Math;\">Misogamist - One who hates the institution of marriage.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Coquette - a flirtatious woman.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Pedantic - too worried about rules or details</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Polyglot - knowing or written in more than one language</span></p>\n",
                    solution_hi: "<p>3.(d)</p>\r\n<p><span style=\"font-family: Cambria Math;\">Misogamist (</span><span style=\"font-family: Cambria Math;\">&#2357;&#2367;&#2357;&#2366;&#2361;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2328;&#2371;&#2339;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2352;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2366;&#2354;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">) - One who hates the institution of marriage.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Coquette (</span><span style=\"font-family: Cambria Math;\">&#2344;&#2326;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2367;&#2326;&#2366;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> )- a flirtatious woman.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Pedantic (</span><span style=\"font-family: Cambria Math;\">&#2352;&#2370;&#2397;&#2367;&#2357;&#2366;&#2342;&#2368;</span><span style=\"font-family: Cambria Math;\">)</span><span style=\"font-family: Cambria Math;\"> - too worried about rules or details</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Polyglot (</span><span style=\"font-family: Cambria Math;\">&#2348;&#2361;&#2369;&#2349;&#2366;&#2359;&#2368;</span><span style=\"font-family: Cambria Math;\">) - knowing or written in more than one language</span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "7",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">4. </span><span style=\"font-family: Cambria Math;\">Choose the word that can substitute the given group of words. </span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> Compulsory enlistment for military service</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">4. </span><span style=\"font-family: Cambria Math;\">Choose the word that can substitute the given group of words. </span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> Compulsory enlistment for military service</span></p>\n",
                    options_en: ["<p>Militaristic</p>\n", "<p>Conscription</p>\n", 
                                "<p>Armament</p>\n", "<p>Abdication</p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\n"],
                    options_hi: ["<p>Militaristic</p>\n", "<p>Conscription</p>\n",
                                "<p>Armament</p>\n", "<p>Abdication</p>\n"],
                    solution_en: "<p>4.(b)</p>\r\n<p><span style=\"font-family: Cambria Math;\"> Conscription - Compulsory enlistment for military service</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> Militaristic - </span><span style=\"font-family: Cambria Math;\">supporting the ideas or policies of having powerful armed forces in a country</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> Armament - </span><span style=\"font-family: Cambria Math;\">military weapons and equipment</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> Abdication - </span><span style=\"font-family: Cambria Math;\">the formal act of stepping down from something</span><span style=\"font-family: Cambria Math;\">, especially a king or queen giving up the throne</span></p>\n",
                    solution_hi: "<p>4.(b)</p>\r\n<p><span style=\"font-family: Cambria Math;\"> Conscription (</span><span style=\"font-family: Cambria Math;\">&#2309;&#2344;&#2367;&#2357;&#2366;&#2352;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2376;&#2344;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;&#2357;&#2366;</span><span style=\"font-family: Cambria Math;\">) - Compulsory enlistment for military service</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> Militaristic (</span><span style=\"font-family: Cambria Math;\">&#2360;&#2376;&#2344;&#2381;&#2351;&#2357;&#2366;&#2342;&#2368;</span><span style=\"font-family: Cambria Math;\">) - </span><span style=\"font-family: Cambria Math;\">supporting the ideas or policies of having powerful armed forces in a country</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> Armam</span><span style=\"font-family: Cambria Math;\">ent (</span><span style=\"font-family: Cambria Math;\">&#2309;&#2360;&#2381;&#2340;&#2381;&#2352;</span><span style=\"font-family: Cambria Math;\"> - </span><span style=\"font-family: Cambria Math;\">&#2358;&#2360;&#2381;&#2340;&#2381;&#2352;</span><span style=\"font-family: Cambria Math;\">) - </span><span style=\"font-family: Cambria Math;\">military weapons and equipment</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> Abdication (</span><span style=\"font-family: Cambria Math;\">&#2340;&#2381;&#2351;&#2366;&#2327;</span><span style=\"font-family: Cambria Math;\">) - </span><span style=\"font-family: Cambria Math;\">the formal act of stepping down from something, especially a king or queen giving up the throne</span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "7",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">5. </span><span style=\"font-family: Cambria Math;\">Choose the word that can substitute the given group of words.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> A spec</span><span style=\"font-family: Cambria Math;\">ialist of kidney</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">5. </span><span style=\"font-family: Cambria Math;\">Choose the word that can substitute the given group of words.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> A specialist of kidney</span></p>\n",
                    options_en: ["<p>Obstetrician</p>\n", "<p>Nephrologist</p>\n", 
                                "<p>Pathologist</p>\n", "<p>Primatologist</p>\n"],
                    options_hi: ["<p>Obstetrician</p>\n", "<p>Nephrologist</p>\n",
                                "<p>Pathologist</p>\n", "<p>Primatologist</p>\n"],
                    solution_en: "<p>5.(b)</p>\r\n<p><span style=\"font-family: Cambria Math;\"> Nephrologist - A specialist of kidney</span><br><span style=\"font-family: Cambria Math;\"> Obstetrician - doctor with special training in taking care of pregnant women and helping in birth of babies</span><br><span style=\"font-family: Cambria Math;\"> Pathologist - an expert in the study of diseases</span><br><span style=\"font-family: Cambria Math;\"> Primatologist - One who studies mammals including apes, monkeys and humans</span></p>\n",
                    solution_hi: "<p>5.(b)</p>\r\n<p><span style=\"font-family: Cambria Math;\"> Nephrologist( </span><span style=\"font-family: Cambria Math;\">&#2325;&#2367;&#2337;&#2344;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2352;&#2379;&#2327;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2367;&#2358;&#2375;&#2359;&#2332;&#2381;&#2334;</span><span style=\"font-family: Cambria Math;\">)- A specialist of kidney</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> Obstetrician (</span><span style=\"font-family: Cambria Math;\">&#2342;&#2366;&#2312;</span><span style=\"font-family: Cambria Math;\"> )- doctor with special training in t</span><span style=\"font-family: Cambria Math;\">aking care of pregnant women and helping in birth of babies</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> Pathologist (</span><span style=\"font-family: Cambria Math;\">&#2352;&#2379;&#2327;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2361;&#2330;&#2366;&#2344;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2366;&#2354;&#2366;</span><span style=\"font-family: Cambria Math;\">) - an expert in the study of diseases </span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> Primatologist(</span><span style=\"font-family: Cambria Math;\">&#2360;&#2381;</span><span style=\"font-family: Cambria Math;\">&zwj;</span><span style=\"font-family: Cambria Math;\">&#2340;&#2344;&#2346;&#2366;&#2351;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2381;&#2352;&#2366;&#2339;&#2367;&#2351;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2309;&#2343;&#2381;&#2351;&#2351;&#2344;</span><span style=\"font-family: Cambria Math;\">)</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> - One who studies mammals including apes, monkeys and humans</span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "7",
                    question_en: "<p>6. <span style=\"font-family: Cambria Math;\">Choo</span><span style=\"font-family: Cambria Math;\">se the word that can substitute the given group of words. </span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> One who offers one&rsquo;s services without being forced </span></p>\n",
                    question_hi: "<p>6. <span style=\"font-family: Cambria Math;\">Choose the word that can substitute the given group of words. </span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> One who offers one&rsquo;s services without being forced </span></p>\n",
                    options_en: ["<p>Skeptic</p>\n", "<p>Altruist</p>\n", 
                                "<p>Volunteer</p>\n", "<p>Scholar</p>\n"],
                    options_hi: ["<p>Skeptic</p>\n", "<p>Altruist</p>\n",
                                "<p>Volunteer</p>\n", "<p>Scholar</p>\n"],
                    solution_en: "<p>6.(c)</p>\r\n<p><span style=\"font-family: Cambria Math;\">Volunteer - One who offers one&rsquo;s services without being forced</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Skeptic - a person who doubts that something is true, right, etc.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Altruist - </span><span style=\"font-family: Cambria Math;\">a person unselfishly concerned for or devoted to the welfare of others </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Scholar</span><span style=\"font-family: Cambria Math;\"> - </span><span style=\"font-family: Cambria Math;\">a person who studies and has a lot of knowledge about a particular subject</span></p>\n",
                    solution_hi: "<p>6.(c)</p>\r\n<p><span style=\"font-family: Cambria Math;\">Volunte</span><span style=\"font-family: Cambria Math;\">er (</span><span style=\"font-family: Cambria Math;\">&#2360;&#2381;&#2357;&#2351;&#2306;&#2360;&#2375;&#2357;&#2325;</span><span style=\"font-family: Cambria Math;\">)- One who offers one&rsquo;s services without being forced</span><br><span style=\"font-family: Cambria Math;\">Skeptic (</span><span style=\"font-family: Cambria Math;\">&#2360;&#2306;&#2342;&#2375;&#2361;&#2357;&#2366;&#2342;&#2368;</span><span style=\"font-family: Cambria Math;\">)- a person who doubts that something is true, right, etc.</span><br><span style=\"font-family: Cambria Math;\">Altruist (</span><span style=\"font-family: Cambria Math;\">&#2346;&#2352;&#2379;&#2346;&#2325;&#2366;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\">) - </span><span style=\"font-family: Cambria Math;\">a person unselfishly concerned for or devoted to the welfare of others</span><br><span style=\"font-family: Cambria Math;\">Scholar (</span><span style=\"font-family: Cambria Math;\">&#2357;&#2367;&#2342;&#2381;&#2357;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\">)</span><span style=\"font-family: Cambria Math;\">-</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">a person who studies and has a lot of knowledge about a particular subject</span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "7",
                    question_en: "<p>7. <span style=\"font-family: Cambria Math;\">Choose the word that can substitute the given group of words. </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">A person who is indifferent to pain and pleasures of life </span></p>\n",
                    question_hi: "<p>7. <span style=\"font-family: Cambria Math;\">Choose the word that can substitute the given group of words. </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">A person who is indifferent to pain and pleasures of life </span></p>\n",
                    options_en: ["<p>Vagabond</p>\n", "<p>Amateur</p>\n", 
                                "<p>Stoic</p>\n", "<p>Recluse</p>\n"],
                    options_hi: ["<p>Vagabond</p>\n", "<p>Amateur</p>\n",
                                "<p>Stoic</p>\n", "<p>Recluse</p>\n"],
                    solution_en: "<p>7.(c)</p>\r\n<p><span style=\"font-family: Cambria Math;\">Stoic- A person who is indiffer</span><span style=\"font-family: Cambria Math;\">ent to pain and pleasures of life.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Vagabond - </span><span style=\"font-family: Cambria Math;\">a person without a home or a job who keeps travelling from one place to an other</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Amateur-</span><span style=\"font-family: Cambria Math;\">a person who takes part in a sport or an activity for pleasure, not for money as a job</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Recluse - </span><span style=\"font-family: Cambria Math;\">a person who lives alone</span><span style=\"font-family: Cambria Math;\"> and who maintains very little contact with other people or society</span></p>\n",
                    solution_hi: "<p>7.(c)</p>\r\n<p><span style=\"font-family: Cambria Math;\">Stoic (</span><span style=\"font-family: Cambria Math;\">&#2360;&#2361;&#2344;&#2358;&#2368;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2381;&#2351;&#2325;&#2381;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\">)- A person who is indifferent to pain and pleasures of life.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Vagabond (</span><span style=\"font-family: Cambria Math;\">&#2310;&#2357;&#2366;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\">)- </span><span style=\"font-family: Cambria Math;\">a person without a home or a job who keeps travelling from one place to a</span><span style=\"font-family: Cambria Math;\">n other</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Amateur (</span><span style=\"font-family: Cambria Math;\">&#2358;&#2380;&#2325;&#2364;&#2368;&#2344;</span><span style=\"font-family: Cambria Math;\">) -</span><span style=\"font-family: Cambria Math;\">a person who takes part in a sport or an activity for pleasure, not for money as a job</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Recluse (</span><span style=\"font-family: Cambria Math;\">&#2357;&#2376;&#2352;&#2366;&#2327;&#2368;</span><span style=\"font-family: Cambria Math;\">)- </span><span style=\"font-family: Cambria Math;\">a person who lives alone and who maintains very little contact with other people or society</span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "7",
                    question_en: "<p>8. <span style=\"font-family: Cambria Math;\">Select the most appropria</span><span style=\"font-family: Cambria Math;\">te \'one word\' for the expression given below. </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">A professional rider in horse race.</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">8. </span><span style=\"font-family: Cambria Math;\">Select the most appropriate \'one word\' for the expression given below.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> A professional rider in horse race.</span></p>\n",
                    options_en: ["<p>Horse Pilot <span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p>Cellar <span style=\"font-family: Cambria Math;\"> </span></p>\n", 
                                "<p>Jockey <span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p>Autocrat<span style=\"font-family: Cambria Math;\"> </span></p>\n"],
                    options_hi: ["<p>Horse Pilot <span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> Cellar </span></p>\n",
                                "<p><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">Jockey </span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">Autocrat</span><span style=\"font-family: Cambria Math;\"> </span></p>\n"],
                    solution_en: "<p>8.(c)</p>\r\n<p><span style=\"font-family: Cambria Math;\">Jockey - A professional rider in horse race.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Horse Pilot - Horse Pilot is aimed at riders who are looking for technical riding gea</span><span style=\"font-family: Cambria Math;\">r.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Cellar- an underground room that is used for storing things</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Autocrat - a ruler who has complete power</span></p>\n",
                    solution_hi: "<p>8.(c)</p>\r\n<p><span style=\"font-family: Cambria Math;\">Jockey (</span><span style=\"font-family: Cambria Math;\">&#2328;&#2369;&#2337;&#2364;&#2360;&#2357;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\">) - A professional rider in horse race.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Horse Pilot (</span><span style=\"font-family: Cambria Math;\">&#2328;&#2379;&#2337;&#2364;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2366;&#2351;&#2354;&#2335;</span><span style=\"font-family: Cambria Math;\">) - Horse Pilot is aimed at riders who are looking for</span><span style=\"font-family: Cambria Math;\"> technical riding gear.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Cellar (</span><span style=\"font-family: Cambria Math;\">&#2340;&#2361;&#2326;&#2364;&#2366;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\">)- an underground room that is used for storing things</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Autocrat (</span><span style=\"font-family: Cambria Math;\">&#2344;&#2367;&#2352;&#2306;&#2325;&#2369;&#2358;</span><span style=\"font-family: Cambria Math;\">)- a ruler who has complete power</span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "7",
                    question_en: "<p>9.<span style=\"font-family: Cambria Math;\"> Choose the word that can substitute the given group of words.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">One who does not know reading or writing</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">9.</span><span style=\"font-family: Cambria Math;\"> Choose the word that can substitute the given group of words.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">One who does not know reading or writing</span></p>\n",
                    options_en: ["<p>Scholar</p>\n", "<p>Illiterate</p>\n", 
                                "<p>Erudite</p>\n", "<p>Fastidious</p>\n"],
                    options_hi: ["<p>Scholar</p>\n", "<p>Illiterate</p>\n",
                                "<p>Erudite</p>\n", "<p>Fastidious</p>\n"],
                    solution_en: "<p>9.(b)<span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Illiterate - One who does not know reading or writing</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Scholar - a person who studies and has a lot of knowledge about a particular subject</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Erudite - having or showing great knowledge that is based on careful study</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Fastidious - difficult to plea</span><span style=\"font-family: Cambria Math;\">se; wanting everything to be perfect</span></p>\n",
                    solution_hi: "<p>9.(b)</p>\r\n<p><span style=\"font-family: Cambria Math;\">Illiterate(</span><span style=\"font-family: Cambria Math;\">&#2344;&#2367;&#2352;&#2325;&#2381;&#2359;&#2352;</span><span style=\"font-family: Cambria Math;\">) - One who does not know reading or writing</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Scholar(</span><span style=\"font-family: Cambria Math;\">&#2357;&#2367;&#2342;&#2381;&#2357;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\">) - a person who studies and has a lot of knowledge about a particular subject</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Erudite(</span><span style=\"font-family: Cambria Math;\">&#2357;&#2367;&#2342;&#2381;&#2357;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\">) - having or showing great knowl</span><span style=\"font-family: Cambria Math;\">edge that is based on careful study</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Fastidious( </span><span style=\"font-family: Cambria Math;\">&#2340;&#2369;&#2344;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2367;&#2332;&#2366;&#2332;</span><span style=\"font-family: Cambria Math;\"> ) - difficult to please; wanting everything to be perfect</span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "7",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">10.</span><span style=\"font-family: Cambria Math;\"> Choose the word that can substitute the given group of words.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> A remedy for all ills</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">10.</span><span style=\"font-family: Cambria Math;\"> Choose the word that can substitute the given group of words.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> A remedy for all ills</span></p>\n",
                    options_en: ["<p>Panacea</p>\n", "<p>Libertine</p>\n", 
                                "<p>Convalescent</p>\n", "<p>Rustic</p>\n"],
                    options_hi: ["<p>Panacea</p>\n", "<p>Libertine</p>\n",
                                "<p>Convalescent</p>\n", "<p>Rustic</p>\n"],
                    solution_en: "<p>10.(a)</p>\r\n<p><span style=\"font-family: Cambria Math;\">Panacea - A remedy for all ills </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Libertine - a person, especially a man, who freely indulges in sensual pleasures without regard to moral principles.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Convalescent-someone who is getting better after a serious illness or injury</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Rustic-someone who comes from the countryside.</span></p>\n",
                    solution_hi: "<p>10.(a)</p>\r\n<p><span style=\"font-family: Cambria Math;\">P</span><span style=\"font-family: Cambria Math;\">anacea (</span><span style=\"font-family: Cambria Math;\">&#2352;&#2366;&#2350;&#2348;&#2366;&#2339;</span><span style=\"font-family: Cambria Math;\">) - A remedy for all ills </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Libertine (</span><span style=\"font-family: Cambria Math;\">&#2309;&#2344;&#2376;&#2340;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\">) - a person, especially a man, who freely indulges in sensual pleasures without regard to moral principles.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Convalescent (</span><span style=\"font-family: Cambria Math;\">&#2346;&#2370;&#2352;&#2381;&#2357;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2309;&#2357;&#2360;&#2381;&#2341;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2369;&#2344;&#2307;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2310;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> )-someone who is getting better after a serious i</span><span style=\"font-family: Cambria Math;\">llness or injury</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Rustic (</span><span style=\"font-family: Cambria Math;\">&#2327;&#2381;&#2352;&#2366;&#2350;&#2368;&#2339;</span><span style=\"font-family: Cambria Math;\">)-someone who comes from the countryside.</span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "7",
                    question_en: "<p>11. <span style=\"font-family: Cambria Math;\">Select the most appropriate one-word substitution for the given group of words. </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Recovering from an illness or medical treatment.</span></p>\n",
                    question_hi: "<p>11. <span style=\"font-family: Cambria Math;\">Select the most appropriate one-word substitution for the given group of words. </span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> Recovering from an illness or medical treatment.</span></p>\n",
                    options_en: ["<p>Bohemian</p>\n", "<p>Connoisseur</p>\n", 
                                "<p>Conval<span style=\"font-family: Cambria Math;\">escent </span></p>\n", "<p>Cosmopolitan</p>\n"],
                    options_hi: ["<p>Bohemian</p>\n", "<p>Connoisseur</p>\n",
                                "<p>Convalescent</p>\n", "<p>Cosmopolitan</p>\n"],
                    solution_en: "<p>11.(c)</p>\r\n<p><span style=\"font-family: Cambria Math;\">Convalescent </span><span style=\"font-family: Cambria Math;\">- </span><span style=\"font-family: Cambria Math;\"> Recovering from an illness or medical treatment</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Bohemian - </span><span style=\"font-family: Cambria Math;\">A person who disregards conventional standards of behavior</span><span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Connoisseur - </span><span style=\"font-family: Cambria Math;\">A person who has expert knowledge and keen discrimination in some field, esp. in the fine arts </span><span style=\"font-family: Cambria Math;\">or in matters of taste</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Cosmopolitan - one who belongs to all over the world</span></p>\n",
                    solution_hi: "<p>11.(c)</p>\r\n<p><span style=\"font-family: Cambria Math;\">Convalescent(</span><span style=\"font-family: Cambria Math;\">&#2309;&#2330;&#2381;&#2331;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2332;&#2366;&#2344;&#2375;&#2357;&#2366;&#2354;&#2366;</span><span style=\"font-family: Cambria Math;\">) </span><span style=\"font-family: Cambria Math;\">- </span><span style=\"font-family: Cambria Math;\"> Recovering from an illness or medical treatment</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Bohemian(</span><span style=\"font-family: Cambria Math;\">&#2352;&#2370;&#2338;&#2364;&#2367;&#2350;&#2369;&#2325;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\">) - </span><span style=\"font-family: Cambria Math;\">A person who disregards conventional standards of behavior</span><span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Connoisseur(</span><span style=\"font-family: Cambria Math;\">&#2357;&#2367;&#2358;&#2375;&#2359;&#2332;&#2381;&#2334;</span><span style=\"font-family: Cambria Math;\">) - </span><span style=\"font-family: Cambria Math;\">A person who has expert knowledge and keen discrimination in some field, esp. in the fine arts or in matters of taste</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Cosmopolitan(</span><span style=\"font-family: Cambria Math;\">&#2360;&#2366;&#2352;&#2381;&#2357;&#2354;&#2380;&#2325;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\">) - one who belongs to all over the world</span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "7",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">12. </span><span style=\"font-family: Cambria Math;\">Select the most appropriate one-word subs</span><span style=\"font-family: Cambria Math;\">titution for the given group of words.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\"> A man who is womanish in his habits</span></strong></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">12. </span><span style=\"font-family: Cambria Math;\">Select the most appropriate one-word substitution for the given group of words.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> A man who is womanish in his habits</span></p>\n",
                    options_en: ["<p>Effeminate</p>\n", "<p>Indictment</p>\n", 
                                "<p>Hooch</p>\n", "<p>Infidel</p>\n"],
                    options_hi: ["<p>Effeminate</p>\n", "<p>Indictment</p>\n",
                                "<p>Hooch</p>\n", "<p>Infidel</p>\n"],
                    solution_en: "<p>12.(a)</p>\r\n<p><span style=\"font-family: Cambria Math;\">Effeminate - A man who is womanish in his habits</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Indictment - </span><span style=\"font-family: Cambria Math;\">a written paper that officially accuses somebody of a crime</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Hooch - alcoholic drink, especially inferior or illicit whisky.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Infidel -</span><span style=\"font-family: Cambria Math;\">an offensive term used to refer to somebody who does not believe in what the speaker considers to be the true religion</span></p>\n",
                    solution_hi: "<p>12.(a)</p>\r\n<p><span style=\"font-family: Cambria Math;\">Effeminate(</span><span style=\"font-family: Cambria Math;\">&#2360;&#2381;&#2340;&#2381;&#2352;&#2368;&#2357;&#2340;</span><span style=\"font-family: Cambria Math;\">) - A man who is womanish in h</span><span style=\"font-family: Cambria Math;\">is habits</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Indictment(</span><span style=\"font-family: Cambria Math;\">&#2309;&#2349;&#2367;&#2351;&#2379;&#2327;</span><span style=\"font-family: Cambria Math;\">) - </span><span style=\"font-family: Cambria Math;\">a written paper that officially accuses somebody of a crime</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Hooch(</span><span style=\"font-family: Cambria Math;\">&#2358;&#2352;&#2366;&#2348;</span><span style=\"font-family: Cambria Math;\">) - alcoholic drink, especially inferior or illicit whisky.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Infidel(</span><span style=\"font-family: Cambria Math;\">&#2309;&#2357;&#2367;&#2358;&#2381;&#2357;&#2366;&#2360;&#2368;</span><span style=\"font-family: Cambria Math;\">) -</span><span style=\"font-family: Cambria Math;\">an offensive term used to refer to somebody who does not believe in what th</span><span style=\"font-family: Cambria Math;\">e speaker considers to be the true religion</span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "7",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">13.</span><span style=\"font-family: Cambria Math;\"> Choose the word that can substitute the given group of words.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\"> Book giving information about everything</span></strong></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">13.</span><span style=\"font-family: Cambria Math;\"> Choose the word that can substitute the given group of words.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\"> Book giving information about everything</span></strong></p>\n",
                    options_en: ["<p>Dictionary</p>\n", "<p>Encyclopedia</p>\n", 
                                "<p>Phonebook</p>\n", "<p>Thesis</p>\n"],
                    options_hi: ["<p>Dictionary</p>\n", "<p>Encyclopedia</p>\n",
                                "<p>Phonebook</p>\n", "<p>Thesis</p>\n"],
                    solution_en: "<p>13.(b)</p>\r\n<p><span style=\"font-family: Cambria Math;\">Encyclopedia - Book giving information about everything</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Dictionary - a book that contains a list of the words in a language in the order of the alphabet and that tells you what they mean, in the same or another language</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Phonebook - a book that lists the names, addresses, and phone numbers of the people and bus</span><span style=\"font-family: Cambria Math;\">inesses in a certain area.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Thesis - an idea that is discussed and presented with evidence in order to show that it is true</span></p>\n",
                    solution_hi: "<p>13.(b)</p>\r\n<p><span style=\"font-family: Cambria Math;\">Encyclopedia(</span><span style=\"font-family: Cambria Math;\">&#2357;&#2367;&#2358;&#2381;&#2357;&#2325;&#2379;&#2358;</span><span style=\"font-family: Cambria Math;\">)- Book giving information about everything</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Dictionary(</span><span style=\"font-family: Cambria Math;\">&#2358;&#2348;&#2381;&#2342;&#2325;&#2379;&#2359;</span><span style=\"font-family: Cambria Math;\">) - a book that contains a list of</span><span style=\"font-family: Cambria Math;\"> the words in a language in the order of the alphabet and that tells you what they mean, in the same or another language</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Phonebook(</span><span style=\"font-family: Cambria Math;\">&#2349;&#2369;&#2327;&#2340;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2347;&#2379;&#2344;</span><span style=\"font-family: Cambria Math;\">) - a book that lists the names, addresses, and phone numbers of the people and businesses in a certain area.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Thesis(</span><span style=\"font-family: Cambria Math;\">&#2358;&#2379;&#2343;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2381;&#2352;&#2348;&#2306;&#2343;</span><span style=\"font-family: Cambria Math;\">) - an idea that is discussed and presented with evidence in order to show that it is true</span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "7",
                    question_en: "<p>14. Choose the word that can substitute the given group of words.</p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">A lover of oneself, of one\'s advancement</span></strong></p>\n",
                    question_hi: "<p>14. Choose the word that can substitute the given group of words.</p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">A lover of oneself, of one\'s advancement</span></strong></p>\n",
                    options_en: ["<p><span style=\"font-family: Cambria Math;\"> Philistine</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">Mercenary</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", 
                                "<p><span style=\"font-family: Cambria Math;\">Cynic</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">Egoist</span></p>\n"],
                    options_hi: ["<p><span style=\"font-family: Cambria Math;\"> Philistine</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">Mercenary</span><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                                "<p><span style=\"font-family: Cambria Math;\">Cynic</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">Egoist</span></p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">14</span><span style=\"font-family: Cambria Math;\">.(d) </span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Egoist - A lover of oneself, of one&rsquo;s advancement</strong>,</span><span style=\"font-family: Cambria Math;\">a person who is preoccupied with his own interests</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Philistine - </span><span style=\"font-family: Cambria Math;\">a person who does not like, understand or enjoy the beauty of art, literature, music, etc.</span></strong></p>\r\n<p><span style=\"font-family: Cambria Math;\">Mercenary - </span><span style=\"font-family: Cambria Math;\"> </span><strong><span style=\"font-family: Cambria Math;\">working or acting merely for money or </span><span style=\"font-family: Cambria Math;\">other reward</span></strong></p>\r\n<p><span style=\"font-family: Cambria Math;\">Cynic - a person who distrusts people</span><span style=\"font-family: Cambria Math;\">.</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">14</span><span style=\"font-family: Cambria Math;\">.(d) </span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Egoist(</span><span style=\"font-family: Cambria Math;\">&#2309;&#2361;&#2306;&#2357;&#2366;&#2342;&#2368;</span><span style=\"font-family: Cambria Math;\">) - A lover of oneself, of one&rsquo;s advancement</span></strong></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Philistine(</span><span style=\"font-family: Cambria Math;\">&#2309;&#2358;&#2367;&#2325;&#2381;&#2359;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\">) - </span><span style=\"font-family: Cambria Math;\">a person who does not like, understand or enjoy the beauty of art, literature, music, etc.</span></strong></p>\r\n<p><span style=\"font-family: Cambria Math;\">Mercenary(</span><span style=\"font-family: Cambria Math;\">&#2340;&#2306;&#2393;&#2381;&#2357;&#2366;&#2361;&#2342;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\">) - </span><span style=\"font-family: Cambria Math;\"> </span><strong><span style=\"font-family: Cambria Math;\">working or acting merely for money or other reward</span></strong></p>\r\n<p><span style=\"font-family: Cambria Math;\">Cynic(</span><span style=\"font-family: Cambria Math;\">&#2344;&#2367;&#2306;&#2342;&#2325;</span><span style=\"font-family: Cambria Math;\">) - a person who distrusts people</span><span style=\"font-family: Cambria Math;\">.</span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "misc",
                    question_en: "<p>15. <span style=\"font-family: Cambria Math;\">Choose the word that can substitute the given group of words. </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">The animals of a particular region</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">15. </span><span style=\"font-family: Cambria Math;\">Choose the word that can substitute the given group of words. </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">The animals of a particular region</span></p>\n",
                    options_en: ["<p>Phylum</p>\n", "<p>Blossom</p>\n", 
                                "<p>Fauna</p>\n", "<p>Flora</p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\n"],
                    options_hi: ["<p>Phylum</p>\n", "<p>Blossom</p>\n",
                                "<p>Fauna</p>\n", "<p>Flora</p>\n"],
                    solution_en: "<p>15.(c)</p>\r\n<p><span style=\"font-family: Cambria Math;\">Fauna - </span><span style=\"font-family: Cambria Math;\">The animals of a particular region</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Phylum-</span><span style=\"font-family: Cambria Math;\">a group into which animals, plants, etc. are divided, smaller than a kingdom and larger than a class</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Blossom-</span><span style=\"font-family: Cambria Math;\">a flower or a mass of flowers, especially on a fruit tree in the spring</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Flora-</span><span style=\"font-family: Cambria Math;\">all the plants growing in a particular area</span></p>\n",
                    solution_hi: "<p>15.(c)</p>\r\n<p><span style=\"font-family: Cambria Math;\">Fauna (</span><span style=\"font-family: Cambria Math;\">&#2332;&#2344;&#2381;</span><span style=\"font-family: Cambria Math;\">&#2340;&#2369;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2352;&#2381;&#2327;</span><span style=\"font-family: Cambria Math;\">) - </span><span style=\"font-family: Cambria Math;\">The animals of a particular region</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Phylum (</span><span style=\"font-family: Cambria Math;\">&#2344;&#2360;&#2381;&#2354;</span><span style=\"font-family: Cambria Math;\">)-</span><span style=\"font-family: Cambria Math;\">a group into which animals, plants, etc. are divided, smaller than a kingdom and larger than a class</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Blossom (</span><span style=\"font-family: Cambria Math;\">&#2347;&#2354;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> - </span><span style=\"font-family: Cambria Math;\">&#2347;&#2370;&#2354;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\">)-</span><span style=\"font-family: Cambria Math;\">a flower or a mass of flowers, especially on a fruit tree in the spring</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Flora (</span><span style=\"font-family: Cambria Math;\">&#2357;&#2344;&#2360;&#2381;&#2346;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2352;&#2381;&#2327;</span><span style=\"font-family: Cambria Math;\"> ) - </span><span style=\"font-family: Cambria Math;\">all the plants growing in a particular area</span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>