<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Smart Test Interface</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        .question-nav{position:fixed;top:70px;right:0;height:calc(100vh - 70px);width:320px;transition:all .3s;z-index:100;display:flex;flex-direction:column}
        .question-content{margin-right:0;transition:all .3s}
        .question-box{width:40px;height:40px;margin:5px;cursor:pointer}
        .attempted{background-color:#3498db!important;color:white}
        .correct{background-color:#2ecc71!important;color:white}
        .incorrect{background-color:#e74c3c!important;color:white}
        .current{border:3px solid #f39c12!important}
        #question-boxes{overflow-y:auto;max-height:calc(100vh - 150px);padding-bottom:15px}
        @media (max-width:768px){.question-nav{width:100%;right:-100%}.question-content{margin-right:0}}
    </style>
</head>
<body>
    <div class="container-fluid p-0">
        <!-- Header with controls -->
        <nav class="navbar navbar-expand-lg navbar-dark bg-primary sticky-top">
            <div class="container-fluid">
                <a class="navbar-brand" href="#">
                    <i class="fas fa-graduation-cap me-2"></i>Smart Test
                </a>
                <!-- Section selector -->
                <div class="navbar-nav me-auto section-tabs">
                    <select class="form-select" id="section-selector" onchange="switchSection(this.value)">
                        <option value="all">All Sections</option>
                    </select>
                </div>
                <div class="d-flex align-items-center">
                    <!-- Timer -->
                    <div class="bg-light text-primary rounded px-3 py-2 me-2 fw-bold" id="timer">
                        <i class="far fa-clock me-2"></i><span id="timer-display">100:00</span>
                    </div>
                    <!-- Submit button -->
                    <button class="btn btn-success me-2" id="submit-btn" onclick="submitTest()">
                        <i class="fas fa-check me-1"></i> Submit
                    </button>
                    <!-- Language toggle -->
                    <button class="btn btn-light me-2" id="lang-btn" onclick="toggleLanguage()">
                        <i class="fas fa-language"></i>
                    </button>
                    <!-- Menu toggle for mobile -->
                    <button class="btn btn-light d-lg-none" id="menu-toggle" onclick="toggleMenu()">
                        <i class="fas fa-th"></i>
                    </button>
                </div>
            </div>
        </nav>
        <div class="row m-0">
            <!-- Main content area -->
            <div class="col p-0 question-content" id="question-content">
                <div class="container py-4">
                    <!-- Question navigation -->
                    <div class="d-flex justify-content-between mb-4">
                        <button class="btn btn-outline-primary" onclick="prevQuestion()">
                            <i class="fas fa-chevron-left me-1"></i> Previous
                        </button>
                        <div class="fw-bold" id="question-counter">Question 1 of 100</div>
                        <button class="btn btn-outline-primary" onclick="nextQuestion()">
                            Next <i class="fas fa-chevron-right ms-1"></i>
                        </button>
                    </div>
                    <!-- Questions will be displayed here -->
                    <div id="questions-container"></div>
                </div>
            </div>
            <!-- Question navigation sidebar -->
            <div class="bg-light question-nav" id="question-nav">
                <div class="p-3">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h5 class="m-0">Questions</h5>
                        <div>
                            <span class="badge bg-secondary me-1" id="attempted-count">0</span>
                            <span class="badge bg-secondary" id="total-count">100</span>
                            <button class="btn btn-sm btn-outline-secondary ms-2 d-lg-none" onclick="toggleMenu()">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    </div>
                    <div class="d-flex flex-wrap" id="question-boxes"></div>
                </div>
            </div>
        </div>
        <!-- Results modal -->
        <div class="modal fade" id="results-modal" tabindex="-1" aria-hidden="true">
            <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content">
                    <div class="modal-header bg-primary text-white">
                        <h5 class="modal-title">Test Results</h5>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <div class="row text-center">
                            <div class="col-6 mb-3">
                                <div class="p-3 bg-primary bg-opacity-10 rounded">
                                    <h3 id="score-value">0</h3>
                                    <div class="small text-muted">Score</div>
                                </div>
                            </div>
                            <div class="col-6 mb-3">
                                <div class="p-3 bg-success bg-opacity-10 rounded">
                                    <h3 id="correct-value">0</h3>
                                    <div class="small text-muted">Correct</div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="p-3 bg-danger bg-opacity-10 rounded">
                                    <h3 id="incorrect-value">0</h3>
                                    <div class="small text-muted">Incorrect</div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="p-3 bg-secondary bg-opacity-10 rounded">
                                    <h3 id="unattempted-value">0</h3>
                                    <div class="small text-muted">Unattempted</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-primary" onclick="reviewTest()">Review Test</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 100 * 60;
        let resultsModal;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            resultsModal = new bootstrap.Modal(document.getElementById('results-modal'));
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 768) {
                document.getElementById('question-content').style.marginRight = '320px';
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["6"] = {
                name: "Reasoning",
                start: 0,
                end: 23
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="6">Reasoning</option>`;
            sections["18"] = {
                name: "General Awareness",
                start: 24,
                end: 48
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="18">General Awareness</option>`;
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 49,
                end: 73
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["7"] = {
                name: "English",
                start: 74,
                end: 98
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="7">English</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 99,
                end: 99
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "6",
                    question_en: "<p>1. What should come in place of the question mark (?) in the given series? <br>160, 187, 216, 247, ?</p>",
                    question_hi: "<p>1. दी गई श्रृंखला में प्रश्न चिह्न (?) के स्थान पर क्या आना चाहिए ?<br>160, 187, 216, 247, ?</p>",
                    options_en: [
                        "<p>280</p>",
                        "<p>290</p>",
                        "<p>208</p>",
                        "<p>209</p>"
                    ],
                    options_hi: [
                        "<p>280</p>",
                        "<p>290</p>",
                        "<p>208</p>",
                        "<p>209</p>"
                    ],
                    solution_en: "<p>1.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742185640694.png\" alt=\"rId4\" width=\"192\" height=\"68\"></p>",
                    solution_hi: "<p>1.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742185640694.png\" alt=\"rId4\" width=\"192\" height=\"68\"></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "6",
                    question_en: "<p>2. How many triangles are there in the given figure ? <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742185640806.png\" alt=\"rId5\" width=\"106\" height=\"105\"></p>",
                    question_hi: "<p>2. दी गई आकृति में कितने त्रिभुज हैं?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742185640806.png\" alt=\"rId5\" width=\"106\" height=\"105\"></p>",
                    options_en: [
                        "<p>11</p>",
                        "<p>12</p>",
                        "<p>13</p>",
                        "<p>14</p>"
                    ],
                    options_hi: [
                        "<p>11</p>",
                        "<p>12</p>",
                        "<p>13</p>",
                        "<p>14</p>"
                    ],
                    solution_en: "<p>2.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742185641080.png\" alt=\"rId6\" width=\"152\" height=\"160\"><br>There are 13 triangles.</p>",
                    solution_hi: "<p>2.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742185641080.png\" alt=\"rId6\" width=\"152\" height=\"160\"><br>13 त्रिभुज हैं.</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "6",
                    question_en: "<p>3. Two sets of numbers are given below. In each set of numbers, certain mathematical operation(s) on the first number result(s) in the second number. Similarly, certain mathematical operation(s) on the second number result(s) in the third number and so on. Which of the given options follows the same set of operations as in the given sets?&nbsp; (NOTE: Operations should be performed on the whole numbers, without breaking down the numbers into their constituent digits. E.g. 13 &ndash; Operations on 13 such as adding/subtracting/multiplying to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is not allowed.)&nbsp;<br>48 &ndash; 50 &ndash; 46 &ndash; 52; 37 &ndash; 39 &ndash; 35 &ndash; 41</p>",
                    question_hi: "<p>3. नीचे संख्याओं के दो समुच्चय दिए गए हैं। संख्याओं के प्रत्येक समुच्चय में, पहली संख्या पर कुछ निश्चित गणितीय संक्रिया करने पर दूसरी संख्या प्राप्त होती है। इसी प्रकार, दूसरी संख्या पर कुछ निश्चित गणितीय संक्रिया करने पर तीसरी संख्या प्राप्त होती है और इसी तरह आगे भी होती है। दिए गए विकल्पों में से कौन-सा विकल्प दिए गए समुच्चयों की तरह संक्रियाओं के समान समुच्चय का अनुसरण करता है?&nbsp;(नोट: संख्याओं को उनके घटक अंकों में तोड़े बिना, पूर्ण संख्याओं पर संक्रियाएँ की जानी चाहिए। उदाहरण के लिए 13 लीजिए - 13 पर की जाने वाली संक्रियाएँ जैसे कि 13 में जोड़ना/घटाना/गुणा करना आदि केवल 13 पर किया जा सकता है। 13 को 1 और 3 में तोड़ना, तथा 1 और 3 पर गणितीय संक्रिया करने की अनुमति नहीं है।)&nbsp;<br>48 &ndash; 50 &ndash; 46 &ndash; 52; 37 &ndash; 39 &ndash; 35 &ndash; 41</p>",
                    options_en: [
                        "<p>21 &ndash; 23 &ndash; 29 &ndash; 27</p>",
                        "<p>19 &ndash; 17 &ndash; 21 &ndash; 25</p>",
                        "<p>26 &ndash; 28 &ndash; 24 &ndash; 30</p>",
                        "<p>15 &ndash; 13 &ndash; 9 &ndash; 19</p>"
                    ],
                    options_hi: [
                        "<p>21 &ndash; 23 &ndash; 29 &ndash; 27</p>",
                        "<p>19 &ndash; 17 &ndash; 21 &ndash; 25</p>",
                        "<p>26 &ndash; 28 &ndash; 24 &ndash; 30</p>",
                        "<p>15 &ndash; 13 &ndash; 9 &ndash; 19</p>"
                    ],
                    solution_en: "<p>3.(c)<br><strong>Logic;-</strong> <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>1</mn><mi mathvariant=\"bold\">st</mi></msup><mi mathvariant=\"bold\">no</mi><mo>.</mo></math> + <strong>2</strong> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>2</mn><mi>nd</mi></msup></math>no. , <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>2</mn><mi>nd</mi></msup></math>no. - <strong>4</strong> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>3</mn><mi>rd</mi></msup></math>no., <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>3</mn><mi>rd</mi></msup></math>no. + <strong>6</strong> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>4</mn><mi>th</mi></msup></math>no.&nbsp;<br>(48 <math display=\"inline\"><mo>-</mo></math> 50 - 46 - 52) :- <strong>48</strong> + 2 = <strong>50</strong>, 50 - 4 = <strong>46</strong>, 46 + 6 = <strong>52</strong><br>(37 <math display=\"inline\"><mo>-</mo></math> 39 - 35 - 41) :- <strong>37</strong> + 2 = <strong>39</strong>, 39 - 4 =<strong> 35</strong>, 35 + 6 = <strong>41</strong><br>Similarly<br>(26 <math display=\"inline\"><mo>-</mo></math> 28 - 24 - 30) :- <strong>26</strong> + 2 = <strong>28</strong>, 28 - 4 = <strong>24</strong>, 24 + 6 = <strong>30</strong></p>",
                    solution_hi: "<p>3.(c)<br><strong>तर्क:-</strong>&nbsp;पहली संख्या + 2 = दूसरी संख्या , दूसरी संख्या - 4 = तीसरी संख्या , तीसरी संख्या + 6 = चौथी संख्या <br>(48 <math display=\"inline\"><mo>-</mo></math> 50 - 46 - 52) :- <strong>48</strong> + 2 = <strong>50</strong>, 50 - 4 = <strong>46</strong>, 46 + 6 = <strong>52</strong><br>(37 <math display=\"inline\"><mo>-</mo></math> 39 - 35 - 41) :- <strong>37</strong> + 2 = <strong>39</strong>, 39 - 4 =<strong> 35</strong>, 35 + 6 = <strong>41</strong><br>इसी प्रकार <br>(26 <math display=\"inline\"><mo>-</mo></math> 28 - 24 - 30) :- <strong>26</strong> + 2 = <strong>28</strong>, 28 - 4 = <strong>24</strong>, 24 + 6 = <strong>30</strong></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "6",
                    question_en: "<p>4. Select the correct mirror image of the given figure when the mirror is placed at MN as shown below. <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742185641191.png\" alt=\"rId7\" width=\"114\" height=\"90\"></p>",
                    question_hi: "<p>4. दी गई आकृति के उस सही दर्पण प्रतिबिंब को चुनिए, जो नीचे दर्शाए गए अनुसार दर्पण को MN पर रखने पर बनेगा।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742185641191.png\" alt=\"rId7\" width=\"114\" height=\"90\"></p>",
                    options_en: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742185641432.png\" alt=\"rId8\" width=\"95\" height=\"15\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742185641531.png\" alt=\"rId9\" width=\"95\" height=\"15\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742185641646.png\" alt=\"rId10\" width=\"95\" height=\"15\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742185641744.png\" alt=\"rId11\" width=\"95\" height=\"15\"></p>"
                    ],
                    options_hi: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742185641432.png\" alt=\"rId8\" width=\"95\" height=\"15\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742185641531.png\" alt=\"rId9\" width=\"95\" height=\"15\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742185641646.png\" alt=\"rId10\" width=\"95\" height=\"15\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742185641744.png\" alt=\"rId11\" width=\"95\" height=\"15\"></p>"
                    ],
                    solution_en: "<p>4 .(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742185641531.png\" alt=\"rId9\" width=\"95\" height=\"15\"></p>",
                    solution_hi: "<p>4.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742185641531.png\" alt=\"rId9\" width=\"95\" height=\"15\"></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "6",
                    question_en: "<p>5. Select the option that represents the letters that when placed from left to right in the blanks below will complete the letter series.<br>_ Y Z K X K X _ Z K X _ X Y _ K X K X Y Z _ X K X Y</p>",
                    question_hi: "<p>5. उस विकल्प का चयन करें जो उन अक्षरों को निरूपित करता है जिन्&zwj;हें नीचे दिए गए रिक्त स्थानों में बाएँ से दाएँ रखे जाने पर अक्षर श्रृंखला पूरी हो जाएगी।<br>_ Y Z K X K X _ Z K X _ X Y _ K X K X Y Z _ X K X Y</p>",
                    options_en: [
                        "<p>XYZKK</p>",
                        "<p>XYKZK</p>",
                        "<p>KXYKZ</p>",
                        "<p>KYXZK</p>"
                    ],
                    options_hi: [
                        "<p>XYZKK</p>",
                        "<p>XYKZK</p>",
                        "<p>KXYKZ</p>",
                        "<p>KYXZK</p>"
                    ],
                    solution_en: "<p>5.(b)<br><span style=\"text-decoration: underline;\"><strong>X</strong></span> Y Z K X K/ X <strong><span style=\"text-decoration: underline;\">Y</span></strong> Z K X <strong><span style=\"text-decoration: underline;\">K</span></strong> /X Y <strong><span style=\"text-decoration: underline;\">Z</span></strong> K X K/ X Y Z <strong><span style=\"text-decoration: underline;\">K</span></strong> X K /X Y</p>",
                    solution_hi: "<p>5.(b)<br><span style=\"text-decoration: underline;\"><strong>X</strong></span> Y Z K X K/ X <strong><span style=\"text-decoration: underline;\">Y</span></strong> Z K X <strong><span style=\"text-decoration: underline;\">K</span></strong> /X Y <strong><span style=\"text-decoration: underline;\">Z</span></strong> K X K/ X Y Z <strong><span style=\"text-decoration: underline;\">K</span></strong> X K /X Y</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "6",
                    question_en: "<p>6. In this question, three statements are given, followed by three conclusions numbered&nbsp;I, II and III. Assuming the statements to be true, even if they seem to be at variance&nbsp;with commonly known facts, decide which of the conclusion(s) logically follows/follow from the statements.<br><strong>Statements :</strong><br>Some strawberries are oranges.<br>Some oranges are kiwis.<br>All kiwis are melons.<br><strong>Conclusions :</strong><br>I. No strawberry is a melon.<br>II. All strawberries are melons.<br>III. Some strawberries are kiwis.</p>",
                    question_hi: "<p>6. इस प्रश्न में तीन कथन दिए गए हैं, जिनके बाद तीन निष्कर्ष I, II और III दिए गए हैं। कथनों को सत्य मानते हुए भले ही वे सामान्य रूप से ज्ञात तथ्यों से अलग प्रतीत होते हों, निर्णय करें कि कौन-सा/कौन-से निष्कर्ष दिए गए कथनों का तार्किक रूप सेअनुसरण करता है/करतेहैं।<br><strong>कथन :</strong><br>कुछ स्ट्रॉबेरियां, संतरे हैं।<br>कुछ संतरे, कीवी हैं।<br>सभी कीवी, खरबूजे हैं।<br><strong>निष्कर्ष :</strong><br>I. कोई स्ट्रॉबेरी, खरबूजा नहीं है।<br>II. सभी स्ट्रॉबेरियां, खरबूजे हैं।<br>III. कुछ स्ट्रॉबेरियां, कीवी हैं।</p>",
                    options_en: [
                        "<p>Both conclusions I and II follow.</p>",
                        "<p>Both conclusions II and III follow.</p>",
                        "<p>All conclusions I, II and III follow.</p>",
                        "<p>Neither conclusions I, II nor III follows.</p>"
                    ],
                    options_hi: [
                        "<p>निष्कर्ष I और II, दोनों अनुसरण करते हैं।</p>",
                        "<p>निष्कर्ष II और III, दोनों अनुसरण करते हैं।</p>",
                        "<p>I, II और III, सभी निष्कर्ष अनुसरण करते हैं।</p>",
                        "<p>न तो निष्कर्ष I, II और न ही निष्कर्ष III अनुसरण करता है।</p>"
                    ],
                    solution_en: "<p>6.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742185641847.png\" alt=\"rId12\" width=\"362\" height=\"81\"><br>Neither conclusion I, II nor III follows.</p>",
                    solution_hi: "<p>6.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742185641955.png\" alt=\"rId13\" width=\"406\" height=\"81\"><br>न तो निष्कर्ष I, II और न ही III अनुसरण करता है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "6",
                    question_en: "<p>7. Select the word-pair that best represents the same relationship expressed in the pair of words given below.&nbsp;(The words should be treated as meaningful Hindi words and should not be related to each other on the basis of number of letters/consonants/vowels in the word.)<br>honest : corrupt</p>",
                    question_hi: "<p>7. उस शब्द-युग्म का चयन कीजिए जो नीचे दिए गए शब्दों के युग्म में व्यक्त किए गए समान संबंध का सबसे अच्छा निरूपण करता है।&nbsp;(शब्दों को अर्थपूर्ण हिंदी शब्दों के रूप में माना जाना चाहिए और उन्हें शब्द में अक्षरों की संख्या /व्यंजन/स्वरों की संख्या के आधार पर एक दूसरे से संबंधित नहीं किया जाना चाहिए।)<br>ईमानदार : भ्रष्ट</p>",
                    options_en: [
                        "<p>Inadequate : Rare</p>",
                        "<p>Strong : Fragile</p>",
                        "<p>Inspection : Seeing</p>",
                        "<p>Praise : Respect</p>"
                    ],
                    options_hi: [
                        "<p>अपर्याप्त : दुर्लभ</p>",
                        "<p>मजबूत : नाजुक</p>",
                        "<p>निरीक्षण : देखना</p>",
                        "<p>प्रशंसा : सम्मान</p>"
                    ],
                    solution_en: "<p>7.(b) As honest and corrupt are opposite of each other similarly strong and fragile are opposite of each other.</p>",
                    solution_hi: "<p>7.(b) जिस प्रकार ईमानदार और भ्रष्ट एक दूसरे के विपरीत हैं उसी प्रकार मजबूत और नाजुक एक दूसरे के विपरीत हैं।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "6",
                    question_en: "<p>8. In a certain code language, <br>&lsquo;M &amp; N&rsquo; means &lsquo;M is the wife of N&rsquo;, <br>&lsquo;M @ N&rsquo; means &lsquo;M is the brother of N&rsquo;, <br>&lsquo;M $ N&rsquo; means &lsquo;M is the mother of N&rsquo;,<br>&lsquo;M # N&rsquo; means &lsquo; M is the sister of N&rsquo;. <br>Based on this, how is Q related to U, if &lsquo;P &amp; Q @ R $ T # U&rsquo;?</p>",
                    question_hi: "<p>8. एक निश्चित कूट भाषा में, <br>\'M &amp; N\' का अर्थ है \'M, N की पत्नी है\', <br>\'M @ N\' का अर्थ है \'M, N का भाई है\', <br>\'M $ N\' का अर्थ है \'M, N की मां है\',<br>\'M # N\' का अर्थ है \'M, N की बहन है\'। <br>इसके आधार पर, , यदि \'P &amp; Q @ R $ T # U\' है, तो Q, U से किस प्रकार संबंधित है?</p>",
                    options_en: [
                        "<p>Father</p>",
                        "<p>Father&rsquo;s brother</p>",
                        "<p>Mother&rsquo;s brother</p>",
                        "<p>Brother</p>"
                    ],
                    options_hi: [
                        "<p>पिता</p>",
                        "<p>पिता के भाई</p>",
                        "<p>मां के भाई</p>",
                        "<p>भाई</p>"
                    ],
                    solution_en: "<p>8.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742185642095.png\" alt=\"rId14\" width=\"210\" height=\"87\"><br>Q is the Mother&rsquo;s brother of U.</p>",
                    solution_hi: "<p>8.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742185642095.png\" alt=\"rId14\" width=\"210\" height=\"87\"><br>Q, U की माँ का भाई है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "6",
                    question_en: "<p>9. Three of the following numbers are alike in a certain way and one is different. Pick the odd one out. (NOTE: Operations should be performed on the whole numbers, without breaking down the numbers into its constituent digits. E.g. 13-Operations on 13 such as adding /deleting /multiplying etc., to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is not allowed.)</p>",
                    question_hi: "<p>9. निम्नलिखित संख्&zwj;याओं में से तीन किसी प्रकार से एक समान हैं और एक उनसे असंगत है। उस असंगत का चयन कीजिए।&nbsp;(नोट: गणितीय संक्रियाएं संख्&zwj;याओं को उनके घटक अंकों में तोड़े बिना पूर्ण संख्&zwj;याओं पर की जानी चाहिए। उदाहरण के लिए 13 - 13 पर की जाने वाली संक्रियाएं, जैसे जोड़ना, घटाना, गुणा करना इत्&zwj;यादि, केवल 13 पर की जा सकती हैं। 13 को 1 और 3 में तोड़ना और तब 1 और 3 पर गणितीय संक्रियाएँ करने की अनुमति नहीं है।)</p>",
                    options_en: [
                        "<p>15-3-108</p>",
                        "<p>18-11-63</p>",
                        "<p>12-8-36</p>",
                        "<p>21-14-72</p>"
                    ],
                    options_hi: [
                        "<p>15-3-108</p>",
                        "<p>18-11-63</p>",
                        "<p>12-8-36</p>",
                        "<p>21-14-72</p>"
                    ],
                    solution_en: "<p>9.(d)<br><strong>Logic:-</strong> (<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>1</mn><mi mathvariant=\"bold\">st</mi></msup><mi mathvariant=\"bold\">no</mi><mo>.</mo><mo>-</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>2</mn><mi>nd</mi></msup><mi>no</mi><mo>.</mo></math>) &times; <strong>9</strong> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>3</mn><mi>rd</mi></msup></math> no.<br>(15 <math display=\"inline\"><mo>-</mo></math> 3 - 108) :- (<strong>15 - 3</strong>) &times; <strong>9</strong> &rArr; <strong>12</strong> &times; <strong>9 </strong>= <strong>108</strong><br>(18 <math display=\"inline\"><mo>-</mo></math> 11 - 63) :- (<strong>18 -11</strong>) &times; <strong>9</strong> &rArr; <strong>7</strong> &times; <strong>9</strong> = <strong>63</strong><br>(12 <math display=\"inline\"><mo>-</mo></math> 8 - 36) :- (<strong>12 - 8</strong>) &times; <strong>9</strong> &rArr; <strong>4</strong> &times; <strong>9</strong> = <strong>36</strong> <br>But<br>(21 <math display=\"inline\"><mo>-</mo></math> 14 - 72) :- (<strong>21 - 14</strong>) &times; <strong>9</strong> &rArr; <strong>7</strong> &times; <strong>9</strong> = <strong>63</strong> &ne; <strong>72</strong></p>",
                    solution_hi: "<p>9.(d)<br><strong>तर्क :-</strong> (<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>1</mn><mi mathvariant=\"bold\">st</mi></msup><mi mathvariant=\"bold\">no</mi><mo>.</mo><mo>-</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>2</mn><mi>nd</mi></msup><mi>no</mi></math>.) &times; 9 =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>3</mn><mi>rd</mi></msup></math> no.<br>(15 <math display=\"inline\"><mo>-</mo></math> 3 - 108) :- (<strong>15 - 3</strong>) &times; <strong>9</strong> &rArr; <strong>12</strong> &times; <strong>9 </strong>= <strong>108</strong><br>(18 <math display=\"inline\"><mo>-</mo></math> 11 - 63) :- (<strong>18 -11</strong>) &times; <strong>9</strong> &rArr; <strong>7</strong> &times; <strong>9</strong> = <strong>63</strong><br>(12 <math display=\"inline\"><mo>-</mo></math> 8 - 36) :- (<strong>12 - 8</strong>) &times; <strong>9</strong> &rArr; <strong>4</strong> &times; <strong>9</strong> = <strong>36</strong> <br>लेकिन<strong id=\"docs-internal-guid-10ea120c-7fff-b99f-6f46-08c5c9f0ee39\"> </strong><br>(21 <math display=\"inline\"><mo>-</mo></math> 14 - 72) :- (<strong>21 - 14</strong>) &times; <strong>9</strong> &rArr; <strong>7</strong> &times; <strong>9</strong> = <strong>63</strong> &ne; <strong>72</strong></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "6",
                    question_en: "<p>10. Select the Venn diagram that best illustrates the relationship between the following classes.<br>Mechanic, Peon, Lawyer</p>",
                    question_hi: "<p>10. उस वेन आरेख का चयन कीजिए जो निम्नलिखित वर्गों के बीच संबंध को सर्वोत्तम रूप से दर्शाता है।<br>मैकेनिक, चपरासी, वकील</p>",
                    options_en: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742185642214.png\" alt=\"rId15\" width=\"84\" height=\"40\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742185642319.png\" alt=\"rId16\" width=\"114\" height=\"40\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742185642494.png\" alt=\"rId17\" width=\"77\" height=\"40\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742185642603.png\" alt=\"rId18\" width=\"91\" height=\"40\"></p>"
                    ],
                    options_hi: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742185642214.png\" alt=\"rId15\" width=\"84\" height=\"40\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742185642319.png\" alt=\"rId16\" width=\"114\" height=\"40\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742185642494.png\" alt=\"rId17\" width=\"77\" height=\"40\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742185642603.png\" alt=\"rId18\" width=\"91\" height=\"40\"></p>"
                    ],
                    solution_en: "<p>10.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742185642766.png\" alt=\"rId19\" width=\"241\" height=\"45\"></p>",
                    solution_hi: "<p>10.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742185642958.png\" alt=\"rId20\" width=\"259\" height=\"70\"></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "6",
                    question_en: "<p>11. HF 16 is related to KI 8 in a certain way. In the same way, RP 10 is related to US 5. To&nbsp;which of the following is NL 6 related, following the same logic?</p>",
                    question_hi: "<p>11. HF 16, KI 8 से एक निश्चित प्रकार से संबंधित है। उसी प्रकार, RP 10, US 5 से संबंधित है। समान तर्क का अनुसरण करते हुए NL 6 निम्नलिखित में से किससे संबंधित है?</p>",
                    options_en: [
                        "<p>LJ 3</p>",
                        "<p>QS 2</p>",
                        "<p>OQ 2</p>",
                        "<p>QO 3</p>"
                    ],
                    options_hi: [
                        "<p>LJ 3</p>",
                        "<p>QS 2</p>",
                        "<p>OQ 2</p>",
                        "<p>QO 3</p>"
                    ],
                    solution_en: "<p>11.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742185643038.png\" alt=\"rId21\" width=\"90\" height=\"90\">&nbsp; &nbsp;,&nbsp; &nbsp;<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742185643135.png\" alt=\"rId22\" width=\"84\" height=\"90\"><br>Similarly,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742185643219.png\" alt=\"rId23\" width=\"81\" height=\"90\"></p>",
                    solution_hi: "<p>11.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742185643038.png\" alt=\"rId21\" width=\"90\" height=\"90\">&nbsp; &nbsp;,&nbsp; &nbsp;<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742185643135.png\" alt=\"rId22\" width=\"84\" height=\"90\"><br>इसी प्रकार,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742185643219.png\" alt=\"rId23\" width=\"81\" height=\"90\"></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "6",
                    question_en: "<p>12. What would be the symbol on the opposite side of <math display=\"inline\"><mo>&#8800;</mo></math> if the given sheet is folded to form a cube?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742185643363.png\" alt=\"rId24\" width=\"122\" height=\"160\"></p>",
                    question_hi: "<p>12. यदि नीचे दी गई शीट को मोड़कर एक घन बनाया जाए तो <math display=\"inline\"><mo>&#8800;</mo></math> के विपरीत फ़लक पर कौन-सा चिन्ह होगा?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742185643363.png\" alt=\"rId24\" width=\"122\" height=\"160\"></p>",
                    options_en: [
                        "<p><math display=\"inline\"><mi>&#8734;</mi></math></p>",
                        "<p>%</p>",
                        "<p><math display=\"inline\"><mi>&#956;</mi></math></p>",
                        "<p><math display=\"inline\"><mi>&#65509;</mi></math></p>"
                    ],
                    options_hi: [
                        "<p><math display=\"inline\"><mi>&#8734;</mi></math></p>",
                        "<p>%</p>",
                        "<p><math display=\"inline\"><mi>&#956;</mi></math></p>",
                        "<p><math display=\"inline\"><mi>&#65509;</mi></math></p>"
                    ],
                    solution_en: "<p>12.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742185643483.png\" alt=\"rId25\" width=\"142\" height=\"161\"><br>The opposite faces are % &harr; &ne; , <math display=\"inline\"><mi>&#956;</mi></math> &harr; &yen; , &infin; &harr;&pi;</p>",
                    solution_hi: "<p>12.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742185643483.png\" alt=\"rId25\" width=\"142\" height=\"161\"><br>विपरीत फलक % &harr; &ne; , <math display=\"inline\"><mi>&#956;</mi></math> &harr; &yen; , &infin; &harr;&pi;</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "6",
                    question_en: "<p>13. What should come in place of ? in the given series based on the English alphabetical order?<br>VOH&nbsp; YPN&nbsp; BQT&nbsp; ?&nbsp; HSF</p>",
                    question_hi: "<p>13. अंग्रेजी वर्णमाला क्रम के आधार पर दी गई श्रृंखला में &lsquo;?&rsquo; के स्थान पर क्या आना चाहिए?<br>VOH&nbsp; YPN&nbsp; BQT&nbsp; ?&nbsp; HSF</p>",
                    options_en: [
                        "<p>AXY</p>",
                        "<p>ERZ</p>",
                        "<p>MOJ</p>",
                        "<p>JHI</p>"
                    ],
                    options_hi: [
                        "<p>AXY</p>",
                        "<p>ERZ</p>",
                        "<p>MOJ</p>",
                        "<p>JHI</p>"
                    ],
                    solution_en: "<p>13.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742185643679.png\" alt=\"rId26\" width=\"327\" height=\"100\"></p>",
                    solution_hi: "<p>13.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742185643679.png\" alt=\"rId26\" width=\"327\" height=\"100\"></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "6",
                    question_en: "<p>14. Six students Amit, Shivam, Dinesh, Kamlesh, Nihar and Roshan are sitting around a circular table, facing the centre (but not necessarily in the same order). Amit is sitting to the immediate right of Shivam. Dinesh is sitting second to the right of Shivam. Nihar is an immediate neighbour of both Dinesh and Roshan. Who is sitting to the immediate right of Roshan?</p>",
                    question_hi: "<p>14. छ: छात्र अमित, शिवम, दिनेश, कमलेश, निहार और रोशन एक वृत्ताकार मेज के चारों और केंद्र की और मुख करके बैठे हैं (परंतु जरूरी नहीं कि वे इसी क्रम में बैठे हों)। अमित, शिवम के ठीक दाएँ बैठा है। दिनेश, शिवम के दाएँ से दूसरे स्थान पर बैठा है। निहार, दिनेश और रोशन दोनों का निकटतम पड़ोसी है। रोशन के ठीक दाएँ कौन बैठा है?</p>",
                    options_en: [
                        "<p>Nihar</p>",
                        "<p>Shivam</p>",
                        "<p>Amit</p>",
                        "<p>Kamlesh</p>"
                    ],
                    options_hi: [
                        "<p>निहार</p>",
                        "<p>शिवम</p>",
                        "<p>अमित</p>",
                        "<p>कमलेश</p>"
                    ],
                    solution_en: "<p>14.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742185643911.png\" alt=\"rId27\" width=\"180\" height=\"80\"></p>",
                    solution_hi: "<p>14.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742185644072.png\" alt=\"rId28\" width=\"153\" height=\"80\"></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "6",
                    question_en: "<p>15. If \'A\' stands for \'&divide;\', \'B\' stands for \'&times;\', \'C\' stands for \'+\' and \'D\' stands for \'-\', what will come in place of the question mark (?) in the following equation?<br>404 B 2 D 101 A 101 C 6 = ?</p>",
                    question_hi: "<p>15. यदि \'A\' का अर्थ \'&divide;\', \'B\' का अर्थ \'&times;\', \'C\' का अर्थ \'+\' और \'D\' का अर्थ &lsquo;-&rsquo; है, तो निम्नलिखित समीकरण में प्रश्न चिन्ह (?) के स्थान पर क्या आएगा?<br>404 B 2 D 101 A 101 C 6 = ?</p>",
                    options_en: [
                        "<p>803</p>",
                        "<p>830</p>",
                        "<p>831</p>",
                        "<p>813</p>"
                    ],
                    options_hi: [
                        "<p>803</p>",
                        "<p>830</p>",
                        "<p>831</p>",
                        "<p>813</p>"
                    ],
                    solution_en: "<p>15.(d) <br><strong>Given :-</strong> 404 B 2 D 101 A 101 C 6<br>As per given instruction after interchanging the letter with sign we get<br>404 &times; 2 - 101 <math display=\"inline\"><mo>&#247;</mo></math> 101 + 6<br>808 - 1 + 6 = 813</p>",
                    solution_hi: "<p>15.(d) <br><strong>दिया गया है:-</strong> 404 B 2 D 101 A 101 C 6<br>दिए गए निर्देश के अनुसार अक्षर को चिन्ह से बदलने पर हमें प्राप्त होता है<br>404 &times; 2 - 101 <math display=\"inline\"><mo>&#247;</mo></math> 101 + 6<br>808 - 1 + 6 = 813</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "16",
                    section: "6",
                    question_en: "<p>16. In a certain code language, &lsquo;print the cards&rsquo; is written as &lsquo;cd fm px&rsquo; and &lsquo;cards for sale&rsquo; is written as &lsquo;nt px ym&rsquo;. How is &lsquo;cards&rsquo; written in the given language ?</p>",
                    question_hi: "<p>16. एक निश्चित कूट भाषा में \'print the cards\' को \'cd fm px\' और \'cards for sale\' को \'nt px ym\' लिखा जाता है। दी गई भाषा में \'cards\' कैसे लिखा जाता है ?</p>",
                    options_en: [
                        "<p>fm</p>",
                        "<p>cd</p>",
                        "<p>nt</p>",
                        "<p>px</p>"
                    ],
                    options_hi: [
                        "<p>fm</p>",
                        "<p>cd</p>",
                        "<p>nt</p>",
                        "<p>px</p>"
                    ],
                    solution_en: "<p>16.(d) print the cards &rarr; cd fm px&hellip;&hellip;.(i)<br>cards for sale &rarr; nt px ym&hellip;..(ii)<br>From (i) and (ii) &lsquo;cards&rsquo; and &lsquo;px&rsquo; are common. The code of &lsquo;cards&rsquo; =&rsquo;px&rsquo;</p>",
                    solution_hi: "<p>16.(d) print the cards &rarr; cd fm px&hellip;&hellip;.(i)<br>cards for sale &rarr; nt px ym&hellip;..(ii)<br>(i) और (ii) से \'card\' और \'px\' उभय-निष्ठ हैं। \'card\' का कोड =\'px\'</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "17",
                    section: "6",
                    question_en: "<p>17. If 16 July 2001 is Monday, then what will be the day of the week on 23 December 2006 ?</p>",
                    question_hi: "<p>17. यदि 16 जुलाई, 2001 को सोमवार हो, तो 23 दिसंबर, 2006 को सप्ताह का कौन-सा दिन होगा?</p>",
                    options_en: [
                        "<p>Saturday</p>",
                        "<p>Thursday</p>",
                        "<p>Wednesday</p>",
                        "<p>Monday</p>"
                    ],
                    options_hi: [
                        "<p>शनिवार</p>",
                        "<p>गुरुवार</p>",
                        "<p>बुधवार</p>",
                        "<p>सोमवार</p>"
                    ],
                    solution_en: "<p>17.(a) 16 July 2001 was Monday. On moving to 2006 the number of odd days are <br>+1 +1 + 2 + 1 + 1 = 6. We have reached till 16 July 2006. But we have to reach till 23 December, the number of days between = 15 + 31 + 30 + 31 + 30 + 23 = 160. Total number of days = 160 + 6 = 166. On dividing by 7 remainder = 5. Monday + 5 = Saturday.</p>",
                    solution_hi: "<p>17.(a) 16 जुलाई 2001 को सोमवार था. 2006 में जाने पर विषम दिनों की संख्या है <br>+1 +1 + 2 + 1 + 1 = 6.&nbsp;हम 16 जुलाई 2006 तक पहुँच चुके हैं। लेकिन हमें 23 दिसम्बर तक पहुँचना है, <br>बीच में विषम दिनों की संख्या = +15 + 31 + 30 + 31 + 30 + 23 = 160. <br>कुल दिनों की संख्या = 160 + 6 = 166. <br>7 से विभाजित करने पर शेषफल = 5, <br>सोमवार + 5 = शनिवार.</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "18",
                    section: "6",
                    question_en: "<p>18. Identify the option figure that when put in place of the question mark (?) will logically complete the series.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742185644243.png\" alt=\"rId29\" width=\"302\" height=\"60\"></p>",
                    question_hi: "<p>18. उस विकल्प आकृति की पहचान कीजिए जिसे प्रश्न चिह्न (?) के स्थान पर रखने पर शृंखला तार्किक रूप से पूरी हो जाएगी।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742185644243.png\" alt=\"rId29\" width=\"302\" height=\"60\"></p>",
                    options_en: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742185644345.png\" alt=\"rId30\" width=\"61\" height=\"60\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742185644452.png\" alt=\"rId31\" width=\"61\" height=\"60\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742185644597.png\" alt=\"rId32\" width=\"60\" height=\"59\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742185644711.png\" alt=\"rId33\" width=\"62\" height=\"60\"></p>"
                    ],
                    options_hi: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742185644345.png\" alt=\"rId30\" width=\"60\" height=\"60\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742185644452.png\" alt=\"rId31\" width=\"61\" height=\"60\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742185644597.png\" alt=\"rId32\" width=\"61\" height=\"60\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742185644711.png\" alt=\"rId33\" width=\"61\" height=\"60\"></p>"
                    ],
                    solution_en: "<p>18.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742185644597.png\" alt=\"rId32\" width=\"61\" height=\"60\"></p>",
                    solution_hi: "<p>18.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742185644597.png\" alt=\"rId32\" width=\"61\" height=\"60\"></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "19",
                    section: "6",
                    question_en: "<p>19. In a certain code language, &lsquo;BELT&rsquo; is written as &lsquo;78&rsquo;, and &lsquo;CASH&rsquo; is written as &lsquo;62&rsquo;. How will &lsquo;DISC&rsquo; be written in that language ?</p>",
                    question_hi: "<p>19. एक निश्चित कूट भाषा में \'BELT\' को \'78\' के रूप में लिखा जाता है और \'CASH\' को \'62\' के रूप में लिखा जाता है। उसी भाषा में \'DISC\' को किस प्रकार लिखा जाएगा ?</p>",
                    options_en: [
                        "<p>82</p>",
                        "<p>86</p>",
                        "<p>70</p>",
                        "<p>92</p>"
                    ],
                    options_hi: [
                        "<p>82</p>",
                        "<p>86</p>",
                        "<p>70</p>",
                        "<p>92</p>"
                    ],
                    solution_en: "<p>19.(c) <strong>Logic :- </strong>(Sum of the place value of letter) &times; 2 <br>BELT :- (2 + 5 + 12 + 20) &times; 2 &rArr; (39) &times; 2 = 78<br>CASH :- (3 + 1 +19 + 8) &times; 2 &rArr; (31) &times; 2 = 62<br>Similarly,<br>DISC :- (4 + 9 + 19 + 3) &times; 2 &rArr; (35) &times; 2 = 70</p>",
                    solution_hi: "<p>19.(c)<strong> तर्क :- </strong>(अक्षर के स्थानीय मान का योग) &times; 2<br>BELT :- (2 + 5 + 12 + 20) &times; 2 &rArr; (39) &times; 2 = 78<br>CASH :- (3 + 1 +19 + 8) &times; 2 &rArr; (31) &times; 2 = 62<br>इसी प्रकार,<br>DISC :- (4 + 9 + 19 + 3) &times; 2 &rArr; (35) &times; 2 = 70</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "20",
                    section: "6",
                    question_en: "<p>20. Three of the following numbers are alike in a certain way and one is different. Pick the odd one out. (NOTE : Operations should be performed on the whole numbers, without breaking down the numbers into its constituent digits. E.g. 13 &ndash; Operations on 13 such as adding /deleting /multiplying etc., to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is not allowed.)</p>",
                    question_hi: "<p>20. निम्नलिखित संख्&zwj;याओं में से तीन किसी प्रकार से एक समान हैं और एक उनसे असंगत है। उस असंगत का चयन कीजिए।&nbsp;(नोट: गणितीय संक्रियाएं संख्&zwj;याओं को उनके घटक अंकों में तोड़े बिना पूर्ण संख्&zwj;याओं पर की जानी चाहिए। उदाहरण के लिए 13 - 13 पर की जाने वाली संक्रियाएं, जैसे जोड़ना, घटाना, गुणा करना इत्&zwj;यादि, केवल 13 पर की जा सकती हैं। 13 को 1 और 3 में तोड़ना और तब 1 और 3 पर गणितीय संक्रियाएँ करने की अनुमति नहीं है।)</p>",
                    options_en: [
                        "<p>11 &ndash; 882</p>",
                        "<p>3 &ndash; 72</p>",
                        "<p>4 &ndash; 128</p>",
                        "<p>7 &ndash; 392</p>"
                    ],
                    options_hi: [
                        "<p>11 &ndash; 882</p>",
                        "<p>3 &ndash; 72</p>",
                        "<p>4 &ndash; 128</p>",
                        "<p>7 &ndash; 392</p>"
                    ],
                    solution_en: "<p>20.(a) <strong>Logic :</strong> (1st number)&sup2; &times; 8 = 2nd number <br>3 &ndash; 72 : - 3&sup2; &times; 8 = 9 &times; 8 = 72 <br>4 &ndash; 128 ;- 4&sup2; &times; 8 = 16 &times; 8 = 128 <br>7 &ndash; 392 :- 7&sup2; &times; 8 = 49 &times; 8 = 392<br>But<br>11 - 882 :- 11&sup2; &times; 8 = 121 &times; 8 = 968 (<math display=\"inline\"><mo>&#8800;</mo></math>882)</p>",
                    solution_hi: "<p>20.(a) <strong>तर्क : </strong>(पहली संख्या)&sup2; &times; 8 = दूसरी संख्या <br>3 &ndash; 72 : - 3&sup2; &times; 8 = 9 &times; 8 = 72 <br>4 &ndash; 128 ;- 4&sup2; &times; 8 = 16 &times; 8 = 128 <br>7 &ndash; 392 :- 7&sup2; &times; 8 = 49 &times; 8 = 392<br>लेकिन<br>11 - 882 :- 11&sup2; &times; 8 = 121 &times; 8 = 968 (<math display=\"inline\"><mo>&#8800;</mo></math>882)</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "21",
                    section: "6",
                    question_en: "<p>21. A paper is folded and cut as shown below. How will it appear when unfolded?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742185644874.png\" alt=\"rId34\" width=\"299\" height=\"80\"></p>",
                    question_hi: "<p>21. एक कागज को नीचे दिए गए चित्र के अनुसार मोड़ा और काटा जाता है। यह कागज खोलने पर कैसा दिखाई देगा?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742185644874.png\" alt=\"rId34\" width=\"299\" height=\"80\"></p>",
                    options_en: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742185644973.png\" alt=\"rId35\" width=\"78\" height=\"69\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742185645091.png\" alt=\"rId36\" width=\"80\" height=\"70\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742185645279.png\" alt=\"rId37\" width=\"76\" height=\"70\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742185645520.png\" alt=\"rId38\" width=\"73\" height=\"70\"></p>"
                    ],
                    options_hi: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742185644973.png\" alt=\"rId35\" width=\"79\" height=\"70\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742185645091.png\" alt=\"rId36\" width=\"80\" height=\"70\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742185645279.png\" alt=\"rId37\" width=\"75\" height=\"69\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742185645520.png\" alt=\"rId38\" width=\"73\" height=\"70\"></p>"
                    ],
                    solution_en: "<p>21.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742185645279.png\" alt=\"rId37\" width=\"76\" height=\"70\"></p>",
                    solution_hi: "<p>21.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742185645279.png\" alt=\"rId37\" width=\"76\" height=\"70\"></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "22",
                    section: "6",
                    question_en: "<p>22. Select the option in which the given figure (X) is embedded. (Rotation is NOT allowed). <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742185645619.png\" alt=\"rId39\" width=\"79\" height=\"80\"></p>",
                    question_hi: "<p>22. उस विकल्प का चयन करें, जिसमें दी गई आकृति (X) सन्निहित है। (घूर्णन की अनुमति नहीं है)।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742185645619.png\" alt=\"rId39\" width=\"79\" height=\"80\"></p>",
                    options_en: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742185645736.png\" alt=\"rId40\" width=\"106\" height=\"104\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742185645850.png\" alt=\"rId41\" width=\"107\" height=\"110\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742185645952.png\" alt=\"rId42\" width=\"104\" height=\"104\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742185646035.png\" alt=\"rId43\" width=\"106\" height=\"108\"></p>"
                    ],
                    options_hi: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742185645736.png\" alt=\"rId40\" width=\"106\" height=\"104\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742185645850.png\" alt=\"rId41\" width=\"106\" height=\"110\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742185645952.png\" alt=\"rId42\" width=\"106\" height=\"106\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742185646035.png\" alt=\"rId43\" width=\"105\" height=\"107\"></p>"
                    ],
                    solution_en: "<p>22.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742185646136.png\" alt=\"rId44\" width=\"129\" height=\"131\"></p>",
                    solution_hi: "<p>22.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742185646136.png\" alt=\"rId44\" width=\"128\" height=\"130\"></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "23",
                    section: "6",
                    question_en: "<p>23. In a certain code language, &lsquo;QUOD&rsquo; is written as &lsquo;GCIT&rsquo; and &lsquo;CRY&rsquo; is written as &lsquo;UFY&rsquo;. How will &lsquo;QUIP&rsquo; be written in that language ?</p>",
                    question_hi: "<p>23. एक निश्चित कूट भाषा में, \'QUOD\' को \'GCIT\' लिखा जाता है और \'CRY\' को \'UFY\' लिखा जाता है। उस भाषा में \'QUIP\' को किस प्रकार लिखा जाएगा ?</p>",
                    options_en: [
                        "<p>GHCO</p>",
                        "<p>GCOH</p>",
                        "<p>GCHO</p>",
                        "<p>GHOC</p>"
                    ],
                    options_hi: [
                        "<p>GHCO</p>",
                        "<p>GCOH</p>",
                        "<p>GCHO</p>",
                        "<p>GHOC</p>"
                    ],
                    solution_en: "<p>23.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742185646240.png\" alt=\"rId45\" width=\"77\" height=\"150\">&nbsp; &nbsp;,&nbsp; &nbsp;<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742185646347.png\" alt=\"rId46\" width=\"58\" height=\"150\"><br>Similarly,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742185646481.png\" alt=\"rId47\" width=\"77\" height=\"150\"></p>",
                    solution_hi: "<p>23.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742185646588.png\" alt=\"rId48\" width=\"66\" height=\"151\">&nbsp; ,&nbsp; &nbsp;<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742185646706.png\" alt=\"rId49\" width=\"50\" height=\"149\"><br>इसी प्रकार,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742185646960.png\" alt=\"rId50\" width=\"65\" height=\"150\"></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "24",
                    section: "6",
                    question_en: "<p>24. Ajay starts from Point A and drives 20 km towards South. He then takes a left turn, drives 5 km, turns left and drives 30 km. He then takes a right turn and drives 8 km. He takes a final right turn, drives 10 km and stops at Point Q. How far (shortest distance) and towards which direction should he now drive to reach Point A again? (All turns are 90&deg; turns only.)</p>",
                    question_hi: "<p>24. अजय बिंदु A से गाड़ी चलाना शुरू करता है और दक्षिण की ओर 20 km गाड़ी चलाता है। फिर वह बाएँ मुड़ता है, 5 km गाड़ी चलाता है, बाएँ मुड़ता है और 30 km गाड़ी चलाता है। फिर वह दाएँ मुड़ता है और 8 km गाड़ी चलाता है। अंतिम बार वह दाएँ मुड़ता है, 10 km गाड़ी चलाता है और बिंदु Q पर रुकता है। अब बिंदु A पर दोबारा पहुँचने के लिए उसे कितनी दूरी (न्यूनतम दूरी) और किस दिशा की ओर गाड़ी चलानी चाहिए? (सभी मोड़ केवल 90&deg; वाले मोड़ हैं।)</p>",
                    options_en: [
                        "<p>5 km towards West</p>",
                        "<p>13 km towards West</p>",
                        "<p>8 km towards West</p>",
                        "<p>13 km towards East</p>"
                    ],
                    options_hi: [
                        "<p>पश्चिम की ओर 5 km</p>",
                        "<p>पश्चिम की ओर 13 km</p>",
                        "<p>पश्चिम की ओर 8 km</p>",
                        "<p>पूर्व की ओर 13 km</p>"
                    ],
                    solution_en: "<p>24.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742185647058.png\" alt=\"rId51\" width=\"203\" height=\"139\"><br>He should drive 13 km toward the west to reach point A.</p>",
                    solution_hi: "<p>24.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742185647058.png\" alt=\"rId51\" width=\"203\" height=\"139\"><br>बिंदु A तक पहुंचने के लिए उसे पश्चिम की ओर 13 km गाड़ी चलानी चाहिए।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "25",
                    section: "18",
                    question_en: "<p>25. Based on the position in the English alphabetical order, three of the following letter-clusters are alike in some manner and one is different. Select the odd letter-cluster.<br>Note: The odd one out is not based on the number of consonants/vowels or their position in the letter cluster.</p>",
                    question_hi: "<p>25. अंग्रेजी वर्णमाला क्रम में स्थिति के आधार पर, निम्नलिखित में से तीन अक्षर-समूह किसी न किसी रूप में संगत हैं और एक असंगत है। असंगत अक्षर- समूह का चयन करें।<br>(नोट: असंगत, अक्षर-समूह में व्यंजनों/स्वरों की संख्या या उनकी स्थिति पर आधारित नहीं है।)</p>",
                    options_en: [
                        "<p>TWGD</p>",
                        "<p>PSKI</p>",
                        "<p>MPNK</p>",
                        "<p>JMQN</p>"
                    ],
                    options_hi: [
                        "<p>TWGD</p>",
                        "<p>PSKI</p>",
                        "<p>MPNK</p>",
                        "<p>JMQN</p>"
                    ],
                    solution_en: "<p>25.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742185647207.png\" alt=\"rId52\" width=\"132\" height=\"85\">&nbsp; &nbsp;,&nbsp; &nbsp; <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742185647695.png\" alt=\"rId55\" width=\"125\" height=\"80\">&nbsp; &nbsp;<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742185647524.png\" alt=\"rId54\" width=\"127\" height=\"80\"> But,<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742185647363.png\" alt=\"rId53\" width=\"125\" height=\"85\"><br><br></p>",
                    solution_hi: "<p>25.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742185647870.png\" alt=\"rId56\" width=\"125\" height=\"80\">&nbsp; &nbsp;,&nbsp; &nbsp; <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742185648335.png\" alt=\"rId59\" width=\"126\" height=\"80\">&nbsp; &nbsp; <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742185648181.png\" alt=\"rId58\" width=\"128\" height=\"80\">,</p>\n<p>लेकिन<br>&nbsp;&nbsp;<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742185648032.png\" alt=\"rId57\" width=\"117\" height=\"80\"></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "26",
                    section: "18",
                    question_en: "<p>26. Which of the following is the first event in a triathlon ?</p>",
                    question_hi: "<p>26. निम्नलिखित में से कौन-सी ट्रायथलॉन (triathlon) में पहली खेल प्रतिस्&zwj;पर्धा है ?</p>",
                    options_en: [
                        "<p>Putting the shot</p>",
                        "<p>Cycling</p>",
                        "<p>Running</p>",
                        "<p>Swimming</p>"
                    ],
                    options_hi: [
                        "<p>निशाना लगाना</p>",
                        "<p>साइकिलिंग</p>",
                        "<p>दौड़</p>",
                        "<p>तैराकी</p>"
                    ],
                    solution_en: "<p>26.(d) <strong>Swimming. </strong>A triathlon is a multi-sport event consisting of three consecutive events : Swimming - The first event, in which participants swim along a set water course. Cycling - The second event, in which participants cycle along a set route. Running - The final event, in which participants complete a race route.</p>",
                    solution_hi: "<p>26.(d)<strong> तैराकी।</strong> ट्रायथलॉन एक बहु-खेल प्रतियोगिता है जिसमें तीन निरंतर प्रतियोगिताएँ होती हैं : तैराकी (Swimming) - पहला इवेंट, जिसमें प्रतिभागी एक निर्धारित जलपथ पर तैरते हैं। साइक्लिंग (Cycling) - दूसरा इवेंट, जिसमें प्रतिभागी एक निर्धारित मार्ग पर साइकिल चलाते हैं। दौड़ (Running) - अंतिम इवेंट, जिसमें प्रतिभागी एक दौड़ का मार्ग पूरा करते हैं।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "27",
                    section: "18",
                    question_en: "<p>27. Mahendravarman I was the ruler of which of the following dynasties?</p>",
                    question_hi: "<p>27. महेंद्रवर्मन प्रथम, निम्नलिखित में से किस राजवंश का शासक था?</p>",
                    options_en: [
                        "<p>Pandya</p>",
                        "<p>Chola</p>",
                        "<p>Chalukya</p>",
                        "<p>Pallava</p>"
                    ],
                    options_hi: [
                        "<p>पंड्या</p>",
                        "<p>चोल</p>",
                        "<p>चालुक्य</p>",
                        "<p>पल्लव</p>"
                    ],
                    solution_en: "<p>27.(d) <strong>Pallava.</strong> Mahendravarman I ruled the southern portions of present-day Andhra region and northern regions of what forms present-day Tamil Nadu in India. He took up the title &lsquo;Chitrakarapuli&rsquo;, &lsquo;Matavilasa&rsquo;, and &lsquo;Vichirachita&rsquo;. The Pallava Dynasty was founded by Simhavishnu.</p>",
                    solution_hi: "<p>27.(d) <strong>पल्लव। </strong>महेंद्रवर्मन प्रथम ने वर्तमान आंध्र क्षेत्र के दक्षिणी भागों और भारत में वर्तमान तमिलनाडु के उत्तरी क्षेत्रों पर शासन किया। उन्होंने &lsquo;चित्रकारपुली&rsquo;, &lsquo;मातविलास&rsquo; और &lsquo;विचिरचिता&rsquo; की उपाधि धारण की थी। पल्लव राजवंश की स्थापना सिंहविष्णु ने की थी।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "28",
                    section: "18",
                    question_en: "<p>28. Which of the following books is INCORRECTLY paired with its respective author ?</p>",
                    question_hi: "<p>28. निम्नलिखित में से कौन-सी पुस्तक अपने संबंधित लेखक के साथ गलत रूप से युग्मित है ?</p>",
                    options_en: [
                        "<p>Hind Swaraj &ndash; Mahatma Gandhi</p>",
                        "<p>Gitanjali &ndash; Rabindranath Tagore</p>",
                        "<p>The Discovery of India &ndash; Subhash Chandra Bose</p>",
                        "<p>Why I am an Atheist &ndash; Bhagat Singh</p>"
                    ],
                    options_hi: [
                        "<p>हिन्द स्वराज - महात्मा गांधी</p>",
                        "<p>गीतांजलि - रवीन्द्रनाथ टैगोर</p>",
                        "<p>द डिस्कवरी ऑफ इंडिया - सुभाष चंद्र बोस</p>",
                        "<p>व्हाई आई एम एन एथीस्ट - भगत सिंह</p>"
                    ],
                    solution_en: "<p>28.(c) &lsquo;&rsquo;The Discovery of India&rsquo;&rsquo; is a book by Jawaharlal Nehru. Books and Authors: <AUTHORS>
                    solution_hi: "<p>28.(c) &lsquo;द डिस्कवरी ऑफ इंडिया&rsquo; जवाहरलाल नेहरू द्वारा लिखी गई पुस्तक है। लेखक एवं उनकी पुस्तकें और : सुभाष चंद्र बोस - &lsquo;एन इंडियन पिल्ग्रिम: एन अनफ़िनीश्ड आटोबायोग्राफी। महात्मा गांधी - &lsquo;नॉन वायलेन्ट</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "29",
                    section: "18",
                    question_en: "<p>29. Goga Navami, which is primarily celebrated in the Indian state of_______is dedicated to the snake God, Gogaji.</p>",
                    question_hi: "<p>29. गोगा नवमी, जो नाग देवता, गोगाजी को समर्पित है, मुख्य रूप से भारतीय राज्य_____में मनाई जाती है।</p>",
                    options_en: [
                        "<p>Rajasthan</p>",
                        "<p>Andhra Pradesh</p>",
                        "<p>Odisha</p>",
                        "<p>Karnataka</p>"
                    ],
                    options_hi: [
                        "<p>राजस्थान</p>",
                        "<p>आंध्र प्रदेश</p>",
                        "<p>ओडिशा</p>",
                        "<p>कर्नाटक</p>"
                    ],
                    solution_en: "<p>29.(a)<strong> Rajasthan</strong>. Other festivals of state: Gangaur Festival (Jaipur), Desert Festival (Jaisalmer), Summer Festival (Mount Abu), Pushkar Fair (Pushkar), Mewar Festival (Udaipur). Andhra Pradesh - Ratha Saptami, Ugadi. Odisha - Ratha Yatra, Bali Jatra. Karnataka - Pattadakal, Ugadi.</p>",
                    solution_hi: "<p>29.(a)<strong> राजस्थान। </strong>राज्य के अन्य त्योहार: गणगौर महोत्सव (जयपुर), रेगिस्तान महोत्सव (जैसलमेर), ग्रीष्म महोत्सव (माउंट आबू), पुष्कर मेला (पुष्कर), मेवाड़ महोत्सव (उदयपुर)। आंध्र प्रदेश - रथ सप्तमी, उगादि। ओडिशा - रथ यात्रा, बाली यात्रा। कर्नाटक - पट्टडकल, उगादि।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "30",
                    section: "18",
                    question_en: "<p>30. Delhi ridge is the water divide between which of the following rivers?</p>",
                    question_hi: "<p>30. दिल्ली रिज (Delhi ridge) निम्नलिखित में से किस नदी के बीच जल विभाजक है ?</p>",
                    options_en: [
                        "<p>Ganges and Sind</p>",
                        "<p>Indus and Mahi</p>",
                        "<p>Ganges and Indus</p>",
                        "<p>Ganges and Yamuna</p>"
                    ],
                    options_hi: [
                        "<p>गंगा और सिंध</p>",
                        "<p>सिंधु और माही</p>",
                        "<p>गंगा और सिंधु</p>",
                        "<p>गंगा और यमुना</p>"
                    ],
                    solution_en: "<p>30.(c) <strong>Ganges and Indus</strong>. The uplift of the Potwar Plateau during the Pleistocene upheaval in the western Himalayas rises the Delhi ridge. The major water dividers of India: Vindya range: Divided the Ganga drainage basin and Narmada river valley. Satpuda range: Divided Narmada river and tapi river valley.</p>",
                    solution_hi: "<p>30.(c) <strong>गंगा और सिंधु।</strong> पश्चिमी हिमालय में प्लेइस्टोसिन के ऊपर उठने के दौरान पोटवार पठार के उत्थान से दिल्ली रिज का उत्थान हुआ। भारत के प्रमुख जल विभाजक: विंध्य पर्वतमाला: गंगा जल निकासी बेसिन और नर्मदा नदी घाटी को विभाजित करती है। सतपुड़ा पर्वतमाला: नर्मदा नदी और तापी नदी घाटी को विभाजित करती है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "31",
                    section: "18",
                    question_en: "<p>31. The term \'Libero\' is used in _____ .</p>",
                    question_hi: "<p>31. लिबेरो (libero) शब्द का प्रयोग _________ में किया जाता है।</p>",
                    options_en: [
                        "<p>snooker</p>",
                        "<p>basketball</p>",
                        "<p>volleyball</p>",
                        "<p>badminton</p>"
                    ],
                    options_hi: [
                        "<p>स्नूकर</p>",
                        "<p>बास्केटबाल</p>",
                        "<p>वॉलीबॉल</p>",
                        "<p>बैडमिंटन</p>"
                    ],
                    solution_en: "<p>31.(c) <strong>volleyball. </strong>In volleyball, the libero is a specialized defensive player responsible for receiving serves, digging attacks, and improving ball control. They wear a different-colored jersey and are not allowed to serve, block, or attack the ball above the net. Other Terminologies - Cut Shot, Dig, Dink, Double Hit, Penetration, Sidearm, Booster, Spikers.</p>",
                    solution_hi: "<p>31.(c) <strong>वॉलीबॉल</strong> में, लिबरो एक विशेष रक्षात्मक खिलाड़ी होता है जो सर्व प्राप्त करने, डिगिंग अटैक और बॉल कंट्रोल में सुधार करने के लिए जिम्मेदार होता है। वह एक अलग रंग की जर्सी पहनता हैं और उसे नेट के ऊपर बॉल को सर्व करने, ब्लॉक करने या अटैक करने की अनुमति नहीं होती है। इस खेल से संबंधित शब्दावली - कट शॉट, डिग, डिंक, डबल हिट, पेनेट्रेशन, साइडआर्म, बूस्टर, स्पाइकर्स।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "32",
                    section: "18",
                    question_en: "<p>32. Match the items in List I with those in List II.<br><img src=\"data:image/png;base64,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\" width=\"311\" height=\"150\"></p>",
                    question_hi: "<p>32. सूची I के मदों का सूची II के मदों से मिलान कीजिए।<br><img src=\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAU8AAACiCAYAAADBYE8aAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAABqkSURBVHhe7Z19bBTFH8ZHoLEtYglvFoiVQpsKNKGUooBJC6SEVLElpCoQUiMkagppAkSBRPgHEksUjU1p+EeSQkir8lprqqGm0DQCipIWLRTQAkEEolhA4A+K97tn3Nnf3nb3bm97Pe56zyeZ3M7O7t5e79lnvvNyncc8XgQhhJCgGKC9EkIICQKaJyGEuIDmSQghLvDp83zssce0LUIIIQqroaEe5ml1ECHhghokkYadJtlsJ4QQF9A8CSHEBTRPQghxAc2TEEJcQPMkhBAX0DwJIcQFNM8+4PXXX5fTG5CeeeYZ8ccff2glhIQHarDvoXmGmK1bt4pdu3aJuro6OTds9uzZYsaMGRQvCRvUYHigeYaY9vZ2kZKSInJycmS+rKxM/Pnnn1LMhIQDajA80DxDzKRJk6RQr169KvMZGRli+vTpoqGhQfzzzz9yHyF9CTUYHmieISY/P1++VlRUyNcnnnhCFBQUiM7OTnHnzh25j5C+hBoMEx4DpmzM4RWWJy8vT/4dAqWRI0da7jemhIQET3Z2ts++kpIS7d3+4+TJk57ExMQe+2MV/I1imWA0mJycLDVmVWZMmZmZPvlAGqQmfcHfzApGngZQQx85ckR2siOhwx2ojnevqIRXVMIrKnHjxg39OGNCze4Vv0w45scffxTl5eXyPJxfXV0tr0mIFcFoEANA9+7d0481JpSj3xNN99OnT1ODfQDN0w/ocIcA9+7dq+0hJLxQg5ELzdOCL7/8Uk73QK2Njnc7cIyaS6cSanhCeotTDeI4swYxx5P0PTRPE2gKrVq1Sqxfv17W+mgWYYoHRKnyinXr1unNKJgmmks4j5DeEIwGX375ZWmwiE7RVIcG2SwPDzRPE6NHjxb79++Xhqj6jFQ/kjJKIyg3irmjo0P88MMPWo6Q4HGjQWN0CvNFvynpW2ieFkybNk1s2rRJXL58OeDEYtT8iDoRJVhFBoS4IRgN4tja2lo9Oh0zZow8j/QtNE8bVq5cKUfMYYroV/IHmu8qMlCj7YT0lmA0iEpcaRAJTXjSt9A8bcCUkZqaGtls2rZtG3+ZQcIONRjZcAE4h2C+Zm5urt4kR0RQX18vBU5CBzVoDzX4aLDTJCNPB6DGX7t2rfx9MJrl+EPi33xNnjyZ/6mGhAVqMPJg5OkQ9DkVFhbKbfQnbd++XSxYsEDmWfuHDmrQHmrw0WCnSZpnL0CNP2TIEIo2hFCDwUEN9j00TxIVUIMk0rDTJPs8CSHEBTRPQghxAc2TEEJcQPMkhBAX9BgwIoQQ4ovVgBFH20lEQQ2SSIOj7YQQEkJonoQQ4gKaJyGEuIDmSQghLqB5EkKIC2iehBDiAponIYS4gOZpAOtdY04XktO1r92cEwj8mzH8o1u1bg3+Ee7s2bP19xk8eLD8r+Ik9lDaUFrANv8Z8qOB5qkBM2ptbZXLuOI/dV+6dEls3bpVK7XGzTluwD+9xUOCibpIFy5cEIsWLZKGynVtYgssBldaWqprAUsUp6WlhaziJs6heRoYN26c/o9lseDWsWPHApqTm3MCgXW7YcRYERG0t7eLSZMmyW2gyrEsA5dhiB2gK3zvRi1g2eG7d+/KfaxMw0u/M8/79++LtrY28fDhQ22PMzIyMuRrR0eHfEU+KSlJRpR2uDnHDcaHxQjMFZFHQUEBDTQGQAWNFogVWP4alSmW5aCBhglv6K9jykYlJ06c8KSnp3vOnj2r7elJXV2d/Kx2aeTIkT758ePH++StUmZmpk++vLxcezePp6SkxKfMX0pOTvYkJCTo+QkTJujb8fHxMqm8OSUmJnpOnjypvWt0gs9BeuKtkD15eXn6d23WqDmZdWSXAmk7Ozvbcr9K0HZ/B5/Tin5nnr0BhqfEcPXqVc+UKVMCmhHOUUbp9BxgfC8z6kGByQO8Io/9VuD9ioqKbMujiVjXYCD86QZAK4EMDTqBXux0Ch1Db3i1wqzP/o6dJvtdsx2DOFlZWSFvxqIppEa80TmPkXBsowPfCcbzMVqOvtFbt27p+9hfFVtcu3ZNvPrqq/K7HzhwoNiwYYN48OCBVho68DxAb3gfaBbaRR/94cOHtSN8j0EaM2aM6Ozs1EqJHTE7YIRRcSUWlfwZoXHEG2BwyFsDC28kIPOBWLlypX7+9evXRVdXlzh06JDsp8I+lOE9nGCcHoWUk5Mjr0eig+7ubrF69WoxYcIEuf3rr7+KvXv3ioaGBu0IZxgrZJXU0sQA5dBXbW2tnBGyatUq2X8O3c6bN087SojGxkZRXFwsdagSBqYwMGn1HjDfo0ePamfHLjFrnvn5+SIxMVGuf60E488IMeINgQG8wuyCXe5VnY/zMMiD91Yj6ijDewQCUUJTU5PwNrn0+8b20KFDtSNIpDNo0CBRU1Mj3n//fRl1YsbGrFmzxJkzZ7QjnAEdIEKEMSoteJvSWqnQBy5RucIIYYJWg494FmDeRoNUU+6UVgGujffAdb3NdrkvlolZ88QUj+bmZvHGG29oe/wD0Slzg+FVV1fLbafgfAgUoEuhqqpKbgPU7ohk7UbVzWRnZ+sj/SQ6wYyQmTNnilGjRomUlBRppm6AIcIYrUCEiEoV5gqgWVVZG0EzPT09Xa+QYY6IgtWPNDCSv2fPHmnC5P/ErHkCGCjE5wREiRA4Ij83QIAAtTrEqiJFGCemlwA07QNhnh5Fog/0daMJXVZWJrtwLl++LJYsWaKVOgdmhua1Xf8+osYVK1bIpjt0ZodquShtWU2JWrp0qa1Jxyr92jzRAd/S0iJu376t7XEPhLN582aRm5vr2kARWaKrAGKFGBWLFy8WR44ccdQNoB4I/MKIczujE+jy5s2b4t69e3I+8vHjx2UTXAFDxC/XEAX6A5pEkxrGa2eOiDRhhP7mf5orcwQU0CMjTf/0a/OEQJcvXy6+++47bU/vgBDVTyNVk8YpOB5NIUQaiHgVMMO3335byzkD91FZWen3oSGRy4gRI8QHH3wgR9jj4uLEu+++K39iqSr5ffv2ycFLmGsg0KKBgfprtaC57u/XaIg4MeCIAUvoEcapBoyIPVwAjkQU1OCjAaY6Y8YMkZqaKurr6x21gmIFO03GdJ8nIeQ/EGUi2kQUy594OoORJ4koqEESaTDyJISQEELzJIQQF9A8CSHEBTRPQghxAc2TEEJcQPMkhBAX9JiqRAghxBerqUqc50kiCmqQRBqc50kIISGE5kkIIS6geRJCiAtonoQQ4gKaJyGEuIDmSQghLqB5EkKIC2ieJvAftbHmC+Z2ITlZbsPNOU7A8q9YT0b9Y1pcV70HEtZvJySawPpfgwcPlvrFq9v1wCIBmqeJXbt2ScPCpFgs1Ia1YQJ9wW7OCRYY9MaNG/XlYZGwoFwozZqQvqaiokJs2rRJ6re2tjaqFzKkeVqg1k/HQm0wwsbGRpn3h5tzAoHFvdSqmmrtbax0qEA51tjGmu+IUgmJBtSzgoUMEXQgIIhG+o15YgnXtrY2cf/+fZm/ePGiqxotPz9fHDt2TG8qI3/lyhW5bYebc4LFuDysEbXaYXt7Ow20j8H3i4edkb57iouLxd69e7Xcf3loNyrxhs86pmxUcenSJU96erqnqanJ093d7Vm2bJlnzZo1WmlPvBGbJy8vT35muzR37lx9+/HHH/cps0vGcxITEz3eWtXx+xlTZmamTz4uLk7fnjBhgk+ZOZWUlMj3jEZw/5GK+g7r6uq0Pc7A92H+jswpOTnZk5CQYFmmkpNjjPeGbatjVIqPj5dJ5QPpyqxJczKeb762OeFzZGdn6/mUlBSPt3Wl3Xlkgfuzot+Yp1vKy8t9zAZf4JQpU3TTgwDxwODBsQPn4Bj15Ts5R4H3xj1YYbwXJw8uruWvPBqIZA26NU8jZr2ZtWLWH8Ax/jRq1l8gcF5RUZF8T6vPZL5H5O00Coy6M38eM+Zy83tFInaa7DfNdjTRs7Ky9IEajET3RTNWjXhjpBDNZTTj0KTu7OzUjvCPcWQer1juFU18tY8j6JHP119/LcaOHSu/r/nz54tr165pJYFBl86tW7fCurSvcYQb6cCBA1qJO4zXUxqORThgFATGEe/m5mbx0ksvibVr18rBnNTUVO0oe/DALFmyRJSWlqIqE8ePH5emu3PnTrF//345+AMhOulTMz8QSBj1J33PmTNnxOnTp6UJJiUliffee090d3drpdbs2LEjJDMwcI20tDRx/vx5bY9/oFmMaN+7d094oz6p1d27d4uuri7tiOBQ18NIOTRcWVkpjh49qpX6Yp5ah1RYWKiVRj80zyCA8MaNGycyMjJkmjdvns/otxMw6IPoA4wePVpGrpi6gVF6DP4UFBQ46kDHlA90tkPAKnmbP1op6UtWr14thg0bJp588klRVlYmvv/+e/HXX39ppdZgQDEnJ0emQ4cOiSFDhrgyE+hu+vTpWi4wqOhRsZ87d07OygCoqK0GHwFaPuvXr9dyPVHXmzNnjsxjxNxOd/isKSkpwttMl4EBNAoD7y/EvHli2gSiPdWMQvRmV6vDKFFjQwgwuoMHD0rTcwrOQaSipjEhijCOPKJWr6qq0qdyBALmSR4tCQkJYsCAAeLff//V9lizfft2aSLl5eU+FZ7RTBCpQWOtra3SeKyiNTW74quvvpJGCs3MmDEjYLcRKmq0kmCMCALsIk9UBomJiVqud+A90bp688035X33N2LePFFzot9GRQIQ+fDhw7VSXyAGRIb+auZA4PoNDQ3yvXJzc/X3Ug8BIlHcUyDMUz7IowFT42CcMFB/wDxqampk5WjXLaOiOJgXIjwrg1VAJ01NTdJsN2zYYNttpCJEHIttBAr+ol0EA3fv3hXV1dXaHl9g6jBqXA/gs/jrLsIzs3TpUi3Xv4gJ84RgUJtDiFZAKEqoJ06ckE0yOzABHtdzO7CDhwhmjebMhQsXdHEj8kX/kZ1ozagHgXM7w8/HH38sbty4IW7fvi27T1AJjho1SppOS0uLPtfYDIwETebFixfbGii+f1wnUIsG+lu1apWMIouKirS9PVGmjWMRIKB/EsfbNdsDoaJJXA8BAF4RUcciMWGe+/btk9EiOs17i2o2oWmN2j/YUVNldjA+CFGRnp7uKOJU4D7q6+tlFGv3IJK+YeLEiWLq1KmyCwYDRRgwgpGgMly+fLnsX7QDpojBRgw8orXhBvV9mzVkB47BsSpAwHv3BuP18IpgIBbhAnAkoqAGSaRhp8mY7/MkhBA30DwJIcQFNE9CCHEBzZMQQlxA8ySEEBfQPAkhxAU0T0IIcQHNkxBCXNBjkjwhhBBfrCbJ8xdGJKKgBkmkwV8YEUJICKF5EkKIC2iehBDiAponIYS4gOZJCCEuoHkSQogLaJ6EEOICmqcfsMQGltrAPC83S270FqyuibWO1HrfWLYBSx7gfpCw7XYpB0ICYdYbl3vxhebpB7VeESbIYtlWLP72KME6TKWlpfJ+kLCYWFpamuvF6AjxB1bFRNAArWE1T+hfVeSE5ukYtThbOGtfLBZ2+fJl+YqoF4ttGdd0V8vEul2MjpBAKL1BazDPxsZGmSf9yDwfPnwo2tra9GVfL168qDdp8Yp8bykvL5drpT8Kk1JLFluxbt06GRkvWLCABkpCRn5+vjh27JiuKeSvXLkit4kXb0iuY8pGFd6ozJOenu5pamrydHd3e5YtW+ZZs2aNLNuyZYtn4cKFHq+xyrwVJSUl8vPbpezsbE9iYqKef+qpp3zKrVJeXp7nzp078vp4Rd7qOKuUmZnpkx8/fry+PXjwYJ8yc8JniVZw/8Q/gbSKBL1a7Ten5ORkT0JCgmWZMRmvh+fA24zX7qb/g89sRb8xz1DhjS59zAd5JKAMsK6uTubtMJ4TCLyX3bFXr171TJkyRRcqjvNnjCgLdG+RDjXoHLMeoM+ioiK/xoYyaAraCoT5etCWMSCIFew02W+a7WiaZ2Vl6R3aGETZunWr3MZrXwyqoDmjRuNxffSHYhsDO3YYRzDxin5MNIXUPg7+xB4NDQ1i7Nix8vvHa319PZ5WrdQ9eBa8rRR5XTwD0N6iRYtEa2urHGg0Dv4Yj4WOocMhQ4aIw4cPa0cQMxww6gXbt2+XpqeEvm3bNuGtlWXfqBUw2yVLlugj5sePHxednZ1i586dcuQc58JMnQxKGcWuEkZHSXTxyy+/yP7qL774Qmri888/F6tXrxZnz57VjnBPRUWF2LRpk7wu+sW9EaRITU2VOsN+lAPoEvdQW1srvBGpWLVqlRwownHz5s2Tx5Ce0DwNoLb1FzWaaW9vF8XFxXIbrzBSDOz4Y+jQobLjHYwePVpGrhAyRjNxbkFBgbxuICB8vCceDJW8TTitlEQL33zzjZg6daqYOXOmzL/wwgvi/PnzYuLEiTLfG2CARi2NGTNGdHV1SVM0ztpAHuTk5OiaNJYTa2ieBsrKykRiYqKWC4xRnJjKVF1dLbftgDkmJSXp0z0QPWL0XoFmVVVVlWPhKuMm0cuDBw/EoEGDZMvBKaq7CBU9Whuq5WFuZiPahJZQhood5rl582b5WlhYqB0l5Hmo1BF1AuhYTc0j9tA8Dah5k4FMUIFIr6amxqfvKBBo6qOPC4LOzc0Vw4cPl/thnDNmzJAPhRPhwjiNxkuik7i4ONHd3S1bDk5BJYwuIlT00KBqedg1s9GNhO4gRJXQFo4rKiqSwQLA9VasWCGb7mpaEglMzJknzLGlpUWfD9obIEbU5DBBpwYKoSIKwM8uL1y4IObMmSP3Q7SVlZWOjRvn4YFQg2IkOpk/f744deqUnE8JA2xubpZN9o6ODr9aDaaiNzbdUUlPnjxZdhPgGgqYKnTJucLOiTnzhGEtX75cnDt3TtvTOyA6XBOjmE4GepTZqUhAkZ6eHlRTCSaMUVlEseH81RMJLTAyRJGvvPKKGDBggHjrrbfEjh07REZGRki0iqY7UE17jLJjcFLtNwIjRvSJe4LJEv9wATgSUVCDJNKw0yT7PAkhxAU0T0IIcQHNkxBCXEDzJIQQF9A8CSHEBTRPQghxAc2TEEJcQPMkhBAX9JgkTwghxBerSfL8hRGJKKhBEmnwF0aEEBJCaJ6EEOICmichhLiA5kkIIS6geRJCiAtonoQQ4gKaZwhR67Yj8b+7E9K/oXk6QK1WqIwR66Wb1yxCfuPGjXIFwlAt8eEPLOeBe1LrzRiNGwnLKBNC+g6apwOw4iUWx8JEWSS1ZpHRvGCaWL4Vy7gGux5Rb8F6MzDukydP6veolpxlBExI30DzdABWHzSupY6F27CAm3GxLKyF3dXVJZd1DQdYwOvIkSNyITi13jbuQYFy3AsWF+MKm4SEnqgzz4sXL4Z9ZT+jcRpBdImVCAsKCqRxTZkyRezatUsrDR94b0S9ZmCsMFiYPw00OoHWs7KyHC9tTcKIt4mnY8pGHN3d3Z5ly5Z51qxZo+1xjrdJ60lMTJSfMZg0d+5cfTs+Pl4mY7lVyszMtNxvTHl5eR5vZOjq3rKzs/Vt8z1NmDBB37ZKJSUl8j0jFdwj+T/eVoXHWylLjTihvLxc/65TUlLk+eb9xmTUt10yXicWwd/Aiqgyz1ARSJAoh7kpwdTV1elmh4Rt7AO4Bq6FYyFQJIWxTGE+xo5A9+jvnqyAaforjxRiRYNOCaQDf+A7V1rDq6o4cU2jvu2AXiK9sg0HdpqMumY7RpHRBP3555/FxIkTxZkzZ+T++/fvixdffFHU1tbKvD/QZ+kVpN5XCHBNNVK9ZMkSbW94MY6Yq3s4cOCAvo+DP/0f7zMpPvvsMzFq1Cj5nb/22mvi5s2bWqlzMJCJfnm7LiczeK7wfhgERVcPZpQUFhZqpcSKqB0wSktLE+PHjxc//fSTzKMv9MaNG+L555+XeSvUqLQZmFJVVZU0U4gXfZidnZ1aaXhAn9bKlSv1EXMMRrW2toqWlhY58IP9uHcn/b24FsSvTBfpUfTFkuCBnt955x35fXV3d0tNIDAIBgQCmPWBGSJOZn1A/zBa6Azah5FiRok38tSOIFZErXnGx8eLpUuXysgMImtubpZThJ5++mntiJ7AHD/66CPdTFCzYhuvpaWlMiIF3qaKSE1NldvhJDs7W2RkZMjtnJwcGR1jtBwDP9g/btw4n2jZjoqKClFcXCxNWCV8JhL5HD16VDz33HMiPz9fDBw4UOTm5oqxY8dqpfagwly4cKGMODHTAt+5mq6GtH79eu3InmBAEaYJneF9p06dKs2X+CeqpyrNmjVLXLlyRfz222/i22+/lU3dQYMGaaU9mTZtmmzWp6Sk6FFmsMYCgaFGh+AArqPmdxpB7Q8DRPSI0fBAIsYxiJ47OjpkHuZ+/vx5uQ2amprEqVOnfKYj+QPmSaKPBw8eyFaDPx1bgcoVU+WgEwWMsKioSEaU5eXl2t6ewGSVnvGMHDx4UOqc+CeqzRNRJiLEPXv2iL///ttvk12BZgyiTBitmuAOo0GzXTWJYXD+mu0QIo6HGS5evFiPDo2guZWXl9fDqO1EjKj3008/lZEGrouJ+cOHD5dlaFYhOq6srNSjY3/g8+zdu1fLkWgiLi5O3L17V7amggH6gw6NXTuNjY0iKSkpoBHOmTNHNtvZpx4cUW2eqJ1hglu2bJE1LzrZwenTp2WfjR1o1iB6XLBggTRQGCqMSUWIEJK/ZruaJA8zhNBRW5uBYNHxjuOcGB7A+48YMUJGwidOnBDDhg3TSoT8yafTXy2ph4FzO6OP+fPni7a2Nml8Dx8+lN1Rv//+u1bqX9vQISpgjAdAx6jg/UWcCmW8CARooM6JavMEiDYzMzNlfw8EAz755BPx4Ycfym07qqurZT8PIkQAY1LRYU1NjdwXTmDiqAhg4rg3I7g39Oc6BQ9DfX29aGho4MMQZaAJjX55VKAIDnbv3i2effZZrTSwtmGgqNCh42Aqbpx3/fp1/iItCLgAHIkoqMHIQHUVKTDy7rTl09+w02TUR56EkNCCVhCa/2raHAacGJH2hOZJCPEB3T4zZ86Us0Uw8o8ZIOwG6gmb7SSioAZJpMFmOyGEhBCaJyGEuIDmSQghLqB5EkKIC2iehBDigh6j7YQQQnyxGm33MU9CCCHOYLOdEEKCRoj/AcdwK33namdJAAAAAElFTkSuQmCC\"></p>",
                    options_en: [
                        "<p>i-b, ii-c, iii-d, iv-a</p>",
                        "<p>i-d, ii-a, iii-b, iv-c</p>",
                        "<p>i-c, ii-a, iii -d, iv-b</p>",
                        "<p>i-b, ii-d, iii-a, iv-c</p>"
                    ],
                    options_hi: [
                        "<p>i-b, ii-c, iii-d, iv-a</p>",
                        "<p>i-d, ii-a, iii-b, iv-c</p>",
                        "<p>i-c, ii-a, iii -d, iv-b</p>",
                        "<p>i-b, ii-d, iii-a, iv-c</p>"
                    ],
                    solution_en: "<p>32.(c)<strong> i-c, ii-a, iii -d, iv-b. </strong>Agricultural Revolutions in India: Green Revolution - To increase agricultural productivity, White Revolution - Milk production, Black Revolution - Petroleum products, Brown Revolution - Leather / Cocoa / Non-Conventional Products, Golden Fiber Revolution - Jute Production, Silver Revolution - Egg Production / Poultry Production, Red Revolution - Meat Production / Tomato Production.</p>",
                    solution_hi: "<p>32.(c)<strong> i-c, ii-a, iii -d, iv-b.</strong> भारत में कृषि क्रांतियाँ: हरित क्रांति - कृषि उत्पादकता बढ़ाने के लिए, श्वेत क्रांति - दुग्ध उत्पादन, काली क्रांति - पेट्रोलियम उत्पाद, भूरी क्रांति - चमड़ा / कोको / गैर-पारंपरिक उत्पाद, गोल्डन फाइबर क्रांति - जूट उत्पादन, रजत क्रांति - अंडा उत्पादन / मुर्गी उत्पादन, लाल क्रांति - मांस / टमाटर उत्पादन।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "33",
                    section: "18",
                    question_en: "<p>33. Which of the following is correct regarding Conditions of Governor\'s office ?<br>I . The Governor can hold any other office of profit<br>II. The Governor shall be entitled without payment of rent to the use of his official residences</p>",
                    question_hi: "<p>33. राज्यपाल के कार्यालय की शर्तों के संबंध में निम्नलिखित में से कौन-सा कथन सही है ?<br>I. राज्यपाल अन्य कोई लाभ का पद धारण कर सकता है।<br>॥. राज्यपाल, बिना किराया दिए, अपने शासकीय निवासों के उपयोग का हकदार होगा।</p>",
                    options_en: [
                        "<p>Only II</p>",
                        "<p>Neither I nor II</p>",
                        "<p>Both I and II</p>",
                        "<p>Only I</p>"
                    ],
                    options_hi: [
                        "<p>केवल ॥</p>",
                        "<p>न तो । और न ही ॥</p>",
                        "<p>। और ॥ दोनों</p>",
                        "<p>केवल ।</p>"
                    ],
                    solution_en: "<p>33.(a) <strong>Only II.</strong> Conditions of Governor\'s office (Article 158): (1) The Governor shall not be a member of either House of Parliament or of a House of the Legislature of any State; (2) The Governor shall not hold any other office of profit; (#) The Governor shall be entitled without payment of rent to the use of his official residences and shall be also entitled to such emoluments, allowances and privileges as may be determined by Parliament by law; (4)The emoluments and allowances of the Governor shall not be diminished during his term of office.</p>",
                    solution_hi: "<p>33.(a)<strong> केवल ॥. </strong>राज्यपाल पद की शर्तें (अनुच्छेद 158): (1) राज्यपाल संसद के किसी भी सदन या किसी राज्य के विधानमंडल के किसी सदन का सदस्य नहीं होगा; (2) राज्यपाल अन्य कोई लाभ का पद धारण नहीं करेगा; (3) राज्यपाल बिना किराया दिए अपने सरकारी आवासों के उपयोग का हकदार होगा और संसद विधि द्वारा निर्धारित किए जाने वाले ऐसे परिलब्धियों, भत्तों और विशेषाधिकारों का भी हकदार होगा; (4) राज्यपाल की परिलब्धियां और भत्ते उसके पदावधि के दौरान कम नहीं किए जाएंगे।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "34",
                    section: "18",
                    question_en: "<p>34. &lsquo;Alarippu&rsquo; is the first dance item of which of the following Indian classical dance forms ?</p>",
                    question_hi: "<p>34. \'अलारिप्पु (Alarippu)\' निम्नलिखित में से किस भारतीय शास्त्रीय नृत्य शैली का प्रथम नृत्य पद (dance item) है?</p>",
                    options_en: [
                        "<p>Bharatanatyam</p>",
                        "<p>Kuchipudi</p>",
                        "<p>Kathakali</p>",
                        "<p>Odissi</p>"
                    ],
                    options_hi: [
                        "<p>भरतनाट्यम</p>",
                        "<p>कुचिपुड़ी</p>",
                        "<p>कथकली</p>",
                        "<p>ओडिसी</p>"
                    ],
                    solution_en: "<p>34.(a) <strong>Bharatanatyam,</strong> a classical Indian dance form from Tamil Nadu, is regarded as the mother of many Indian classical dances. Alarippu, traditionally the first piece dancers learn, is typically performed before other dances. Bharatnatyam dance is known to be ekaharya, where one dancer takes on many roles in a single performance. The Jatisvaram is a short pure dance piece performed to the accompaniment of musical notes of any raga of Carnatic music.</p>",
                    solution_hi: "<p>34.(a) <strong>भरतनाट्यम, </strong>तमिलनाडु का एक शास्त्रीय भारतीय नृत्य रूप है, जिसे कई भारतीय शास्त्रीय नृत्यों की जननी माना जाता है। अलारिप्पु, पारंपरिक रूप से नर्तकियों द्वारा सीखा जाने वाला पहला नृत्य है, जिसे आम तौर पर अन्य नृत्यों से पहले प्रदर्शित किया जाता है। भरतनाट्यम नृत्य को एकहार्य के रूप में जाना जाता है, जहाँ एक नर्तक एक ही प्रदर्शन में कई भूमिकाएँ निभाता है। जतिस्वरम एक लघु शुद्ध नृत्य खंड है जिसे कर्नाटक संगीत के किसी भी राग के संगीतमय स्वरों के साथ प्रदर्शित किया जाता है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "35",
                    section: "18",
                    question_en: "<p>35. Which of the following statements are true regarding the Delhi Municipal Corporation (Amendment) Bill, 2022?<br>1. The Delhi Municipal Corporation (Amendment) Bill, 2022 was introduced in Lok Sabha on 25 March 2022.<br>2. The Bill seeks to amend the Delhi Municipal Corporation Act, 1967 passed by the Parliament.<br>3. The Bill replaces the three municipal corporations under the Act with one corporation named the Municipal Corporation of Delhi.<br>4. The Bill states that the total number of seats in the new corporation should not be more than 250.</p>",
                    question_hi: "<p>35. दिल्ली नगर निगम (संशोधन) विधेयक, 2022 के संबंध में निम्नलिखित में से कौन से कथन सत्य हैं ?<br>1. दिल्ली नगर निगम (संशोधन) विधेयक, 2022 को 25 मार्च 2022 को लोकसभा में पेश किया गया था।<br>2. यह विधेयक, संसद द्वारा पारित दिल्ली नगर निगम अधिनियम, 1967 में संशोधन करना चाहता है।<br>3. यह विधेयक, अधिनियम के तहत तीन नगर निगमों को दिल्ली नगर निगम नामक एक निगम से प्रतिस्थापित करता है।<br>4. इस विधेयक में कहा गया है कि नए निगम में कुल सीटों की संख्या 250 से अधिक नहीं होनी चाहिए।</p>",
                    options_en: [
                        "<p>2, 3 and 4</p>",
                        "<p>1, 3 and 4</p>",
                        "<p>1, 2 and 3</p>",
                        "<p>1, 2 and 4</p>"
                    ],
                    options_hi: [
                        "<p>2, 3 और 4</p>",
                        "<p>1, 3 और 4</p>",
                        "<p>1, 2 और 3</p>",
                        "<p>1, 2 और 4</p>"
                    ],
                    solution_en: "<p>35.(b) <strong>1, 3 and 4.</strong> The Delhi Municipal Corporation (Amendment) Bill, 2022, introduced in Lok Sabha on 25 March 2022, amends the Delhi Municipal Corporation Act, 1957. The Act was amended in 2011 by the Parliament of India to trifurcate the erstwhile Municipal Corporation of Delhi into: (i) North Delhi Municipal Corporation, (ii) South Delhi Municipal Corporation, and (iii) East Delhi Municipal Corporation. The 2022 amendment limits the total number of seats in the unified corporation to 250.</p>",
                    solution_hi: "<p>35.(b) <strong>1, 3 और 4. </strong>25 मार्च 2022 को लोकसभा में पेश किया गया दिल्ली नगर निगम (संशोधन) विधेयक, 2022, दिल्ली नगर निगम अधिनियम, 1957 में संशोधन करता है। इस अधिनियम को 2011 में भारत की संसद द्वारा संशोधित किया गया था जिससे पूर्ववर्ती दिल्ली नगर निगम को तीन भागों में विभाजित किया जा सके : (i) उत्तरी दिल्ली नगर निगम, (ii) दक्षिणी दिल्ली नगर निगम और (iii) पूर्वी दिल्ली नगर निगम। 2022 के संशोधन में एकीकृत निगम में सीटों की कुल संख्या 250 तक सीमित कर दी गई है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "36",
                    section: "18",
                    question_en: "<p>36. The process of privatisation covers three sets of measures. Which of the following is NOT one of those?</p>",
                    question_hi: "<p>36. निजीकरण की प्रक्रिया में मापन के तीन समुच्चय शामिल हैं। निम्नलिखित में से कौन उनमें से एक नहीं है ?</p>",
                    options_en: [
                        "<p>Operational measures</p>",
                        "<p>Ownership measures</p>",
                        "<p>Organisational measures</p>",
                        "<p>Accounting measures</p>"
                    ],
                    options_hi: [
                        "<p>परिचालन मापन</p>",
                        "<p>स्वामित्व मापन</p>",
                        "<p>संगठनात्मक मापन</p>",
                        "<p>लेखांकन मापन</p>"
                    ],
                    solution_en: "<p>36.(d) <strong>Accounting measures.</strong> Ownership measures - The transformation of the ownership of public enterprises to private owners. Organizational measures - The limitation of state control in public companies. These involve the employment of methods for the leasing and restructuring of the enterprises. Operational measures concern the way to improve the profitability and efficiency of public enterprises.</p>",
                    solution_hi: "<p>36.(d) <strong>लेखांकन मापन। </strong>स्वामित्व के मापन- सार्वजनिक उद्यमों के स्वामित्व को निजी मालिकों में बदलना। संगठनात्मक मापन- सार्वजनिक कंपनियों में राज्य नियंत्रण की सीमा। इनमें उद्यमों को पट्टे पर देने और पुनर्गठन के तरीकों का इस्तेमाल शामिल है। परिचालन मापनसार्वजनिक उद्यमों की लाभप्रदता और दक्षता में सुधार के तरीके से संबंधित हैं।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "37",
                    section: "18",
                    question_en: "<p>37. Thetakudi Harihara Vinayakram, who became the first Carnatic musician to win a Grammy, is known for playing which musical instrument?</p>",
                    question_hi: "<p>37. ग्रैमी जीतने वाले पहले कर्नाटक संगीतकार, थेटाकुडी हरिहर विनायकराम (Thetakudi Harihara Vinayakram) को किस संगीत वाद्ययंत्र को बजाने के लिए जाना जाता है?</p>",
                    options_en: [
                        "<p>Kanjira</p>",
                        "<p>Ghatam</p>",
                        "<p>Shehnai</p>",
                        "<p>Mridangam</p>"
                    ],
                    options_hi: [
                        "<p>कंजरा</p>",
                        "<p>घटम</p>",
                        "<p>शहनाई</p>",
                        "<p>मृदंगम</p>"
                    ],
                    solution_en: "<p>37.(b) <strong>Ghatam.</strong> Thetakudi Harihara Vinayakram, also known as Vikku Vinayakram. He was the first Carnatic musician to win a Grammy in 1991. Ghatam is a traditional percussion instrument in Carnatic music. Other Grammy-winning Instrumentalists: Ravi Shankar (Sitar), Zakir Hussain (Tabla) and Vishwa Mohan Bhatt (Mohan Veena).</p>",
                    solution_hi: "<p>37.(b)<strong> घटम।</strong> थेटाकुडी हरिहर विनायकराम, जिन्हें विक्कू विनायकराम के नाम से भी जाना जाता है। वे 1991 में ग्रैमी जीतने वाले प्रथम कर्नाटक संगीतकार थे। घटम कर्नाटक संगीत में एक पारंपरिक ताल वाद्य है। अन्य ग्रैमी विजेता - रवि शंकर (सितार), जाकिर हुसैन (तबला) और विश्व मोहन भट्ट (मोहन वीणा)।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "38",
                    section: "18",
                    question_en: "<p>38. ATM booths come under which sector of the economic activity ?</p>",
                    question_hi: "<p>38. एटीएम बूथ (ATM booths), आर्थिक गतिविधि के निम्नलिखित में से किस क्षेत्रक के अंतर्गत आते हैं ?</p>",
                    options_en: [
                        "<p>Primary sector</p>",
                        "<p>Quaternary sector</p>",
                        "<p>Tertiary Sector</p>",
                        "<p>Secondary sector</p>"
                    ],
                    options_hi: [
                        "<p>प्राथमिक क्षेत्रक</p>",
                        "<p>चतुर्धातुक क्षेत्रक</p>",
                        "<p>तृतीयक क्षेत्रक</p>",
                        "<p>द्वितीयक क्षेत्रक</p>"
                    ],
                    solution_en: "<p>38.(c) <strong>Tertiary Sector:</strong> This segment of the economy provides services to consumers and businesses rather than producing tangible goods. Examples include transport, storage, communication, banking, and trade. Since these activities generate services rather than goods, the tertiary sector is also known as the service sector.</p>",
                    solution_hi: "<p>38.(c) <strong>तृतीयक क्षेत्रक। </strong>अर्थव्यवस्था का यह खंड मूर्त वस्तुओं के उत्पादन के बजाय उपभोक्ताओं और व्यवसायों को सेवाएँ प्रदान करता है। उदाहरणों में परिवहन, भंडारण, संचार, बैंकिंग और व्यापार शामिल हैं। चूँकि ये गतिविधियाँ वस्तुओं के बजाय सेवाएँ उत्पन्न करती हैं, इसलिए तृतीयक क्षेत्र को सेवा क्षेत्र के रूप में भी जाना जाता है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "39",
                    section: "18",
                    question_en: "<p>39. Which type of radiation has very short (&lt;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>10</mn><mrow><mo>&#8211;</mo><mn>3</mn></mrow></msup></math> nm) wavelengths, produced by nuclear explosions, lightning and less dramatic activity of radioactive decay?</p>",
                    question_hi: "<p>39. नाभिकीय विस्फोटों, आकाशीय तड़ित और रेडियोधर्मी क्षय की कम उत्तेजक गतिविधि द्वारा निर्मित किस प्रकार के विकिरण में बहुत छोटी (&lt;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>10</mn><mrow><mo>&#8211;</mo><mn>3</mn></mrow></msup></math> nm) तरंगदैर्ध्य होती है?</p>",
                    options_en: [
                        "<p>Gamma</p>",
                        "<p>Infra-red</p>",
                        "<p>Microwave</p>",
                        "<p>Ultraviolet</p>"
                    ],
                    options_hi: [
                        "<p>गामा</p>",
                        "<p>अवरक्त</p>",
                        "<p>सूक्ष्मतरंग</p>",
                        "<p>पराबैंगनी</p>"
                    ],
                    solution_en: "<p>39.(a) <strong>Gamma.</strong> These rays possess the smallest wavelengths and the highest energy of all waves in the electromagnetic spectrum. They are generated by some of the most extreme and energetic phenomena in the universe, such as neutron stars, and supernova explosions. The electromagnetic spectrum, ordered from long to short wavelengths, includes the following types of waves : Radio waves, microwaves, infrared, visible light, ultraviolet, X-rays, and gamma rays.</p>",
                    solution_hi: "<p>39.(a) <strong>गामा।</strong> इन किरणों में विद्युत चुम्बकीय स्पेक्ट्रम में सभी तरंगों की तुलना में सबसे छोटी तरंगदैर्ध्य और सबसे अधिक ऊर्जा होती है। वे ब्रह्मांड में कुछ सबसे चरम और ऊर्जावान घटनाओं, जैसे न्यूट्रॉन तारे और सुपरनोवा विस्फोटों द्वारा उत्पन्न होते हैं। लंबी से छोटी तरंग दैर्ध्य तक क्रमबद्ध विद्युत चुम्बकीय स्पेक्ट्रम में निम्नलिखित प्रकार की तरंगें शामिल हैं: रेडियो तरंगें, माइक्रोवेव, अवरक्त, दृश्य प्रकाश, पराबैंगनी, एक्स-रे और गामा किरणें।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "40",
                    section: "18",
                    question_en: "<p>40. Which actor\'s autobiography is titled \'Bad Man\' ?</p>",
                    question_hi: "<p>40. बैड मैन (Bad Man\') किस अभिनेता की आत्मकथा है?</p>",
                    options_en: [
                        "<p>Gulshan Grover</p>",
                        "<p>Amrish Puri</p>",
                        "<p>Raza Murad</p>",
                        "<p>Ashish Vidyarthi</p>"
                    ],
                    options_hi: [
                        "<p>गुलशन ग्रोवर</p>",
                        "<p>अमरीश पुरी</p>",
                        "<p>रज़ा मुराद</p>",
                        "<p>आशीष विद्यार्थी</p>"
                    ],
                    solution_en: "<p>40.(a) <strong>Gulshan Grover</strong>. He is an Indian actor and film producer who has appeared in over 100 films. Some famous Autobiographies : Kishore Kumar - The Ultimate Biography, Dilip Kumar - The Substance and the Shadow, Manoj Bajpayee - The Definitive Biography.</p>",
                    solution_hi: "<p>40.(a) <strong>गुलशन ग्रोवर। </strong>वह एक भारतीय अभिनेता और फिल्म निर्माता हैं, जिन्होंने 100 से अधिक फिल्मों में काम किया है। कुछ प्रसिद्ध आत्मकथाएँ : किशोर कुमार - द अल्टीमेट बायोग्राफी, दिलीप कुमार - द सब्सटेंस एंड द शैडो, मनोज बाजपेयी - द डेफिनिटिव बायोग्राफी।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "41",
                    section: "18",
                    question_en: "<p>41. In the context of vernier calliper, an internal jaw is used to measure:</p>",
                    question_hi: "<p>41. वर्नियर कैलीपर के संदर्भ में आंतरिक जबड़े (जॉ) का उपयोग निम्न के मापन के लिए किया जाता है:</p>",
                    options_en: [
                        "<p>the length correct up to 1 mm</p>",
                        "<p>the depth of a beaker</p>",
                        "<p>the length of a rod and diameter of a sphere</p>",
                        "<p>the internal diameter of a hollow cylinder and pipes</p>"
                    ],
                    options_hi: [
                        "<p>लंबाई, जो 1 mm तक सटीक हो</p>",
                        "<p>बीकर की गहराई</p>",
                        "<p>एक छड़ की लंबाई और एक गोले का व्यास</p>",
                        "<p>एक खोखले बेलन और पाइप का आंतरिक व्यास</p>"
                    ],
                    solution_en: "<p>41.(d) A Vernier Caliper is a precision instrument used for accurate measurements in various applications. Key parts include- Depth Probe: Used for measuring the depth of objects or holes. Main Scale: Provides measurements in millimeters. Vernier Scale: Offers measurements with an accuracy of up to one decimal place in millimeters.</p>",
                    solution_hi: "<p>41.(d) वर्नियर कैलिपर एक सटीक उपकरण है जिसका उपयोग विभिन्न अनुप्रयोगों में सटीक माप के लिए किया जाता है। मुख्य भागों में शामिल हैं- गहराई की जांच: वस्तुओं या छिद्रों की गहराई मापने के लिए उपयोग किया जाता है। मुख्य पैमाना: मिलीमीटर में माप प्रदान करता है। वर्नियर स्केल: मिलीमीटर में एक दशमलव स्थान तक की सटीकता के साथ माप प्रदान करता है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "42",
                    section: "18",
                    question_en: "<p>42. Which ministry launched the &lsquo;Ek Ped Maa Ke Naam&rsquo; mobile application in September 2024, allowing users to plant a tree in honour of their mothers?</p>",
                    question_hi: "<p>42. किस मंत्रालय ने सितंबर 2024 में &lsquo;एक पेड़ माँ के नाम&rsquo; मोबाइल एप्लीकेशन लॉन्च किया, जिससे उपयोगकर्ता अपनी माताओं के सम्मान में एक पेड़ लगा सकते हैं?</p>",
                    options_en: [
                        "<p>Ministry of Communications</p>",
                        "<p>Ministry of Science and Technology</p>",
                        "<p>Ministry of Agriculture and Farmers&rsquo; Welfare</p>",
                        "<p>Ministry of Environment, Forest, and Climate Change</p>"
                    ],
                    options_hi: [
                        "<p>संचार मंत्रालय</p>",
                        "<p>विज्ञान और प्रौद्योगिकी मंत्रालय</p>",
                        "<p>कृषि और किसान कल्याण मंत्रालय</p>",
                        "<p>पर्यावरण, वन और जलवायु परिवर्तन मंत्रालय</p>"
                    ],
                    solution_en: "<p>42.(a) <strong>Ministry of Communications. </strong>On 23rd September 2024, Union Minister Jyotiraditya M. Scindia launched the &lsquo;Ek Ped Maa Ke Naam&rsquo; mobile application (app) at the National Media Centre (NMC) in New Delhi (Delhi). The Government of India (GoI) launched Ek Ped Maa Ke Naam, a nationwide tree- plantation campaign on 5 June 2024 to commemorate World Environment Day (WED).</p>",
                    solution_hi: "<p>42.(a) <strong>संचार मंत्रालय।</strong> 23 सितंबर 2024 को केंद्रीय मंत्री ज्योतिरादित्य एम. सिंधिया ने नई दिल्ली (दिल्ली) में राष्ट्रीय मीडिया केंद्र (NMC) में &lsquo;एक पेड़ माँ के नाम&rsquo; मोबाइल एप्लीकेशन (ऐप) लॉन्च किया। भारत सरकार (GoI) ने 5 जून 2024 को विश्व पर्यावरण दिवस (WED) के उपलक्ष्य में एक राष्ट्रव्यापी वृक्षारोपण अभियान &lsquo;एक पेड़ माँ के नाम&rsquo; लॉन्च किया।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "43",
                    section: "18",
                    question_en: "<p>43. Which famous chemist refuted the theory of \'vitalism\' by saying that organic substances can only be produced from living things?</p>",
                    question_hi: "<p>43. किस प्रसिद्ध रसायनज्ञ ने \'जीवनवाद\' के सिद्धांत का यह कहकर खंडन किया कि कार्बनिक पदार्थ केवल जीवित वस्तुओं से ही उत्पादित किए जा सकते हैं?</p>",
                    options_en: [
                        "<p>John Dalton</p>",
                        "<p>Antoine Lavoisier</p>",
                        "<p>Friedrich Wohler</p>",
                        "<p>Marcellin Berthelot</p>"
                    ],
                    options_hi: [
                        "<p>जॉन डाल्टन</p>",
                        "<p>एंटोनी लवॉज़ियर</p>",
                        "<p>फ्रेडरिक वोहलर</p>",
                        "<p>मार्सेलिन बर्थेलॉट</p>"
                    ],
                    solution_en: "<p>43.(c) <strong>Friedrich Wohler: </strong>Renowned German chemist known for synthesizing urea from inorganic salts, disproving vitalism theory that organic compounds can only be produced by living organisms. John Dalton: Inventor of atomic theory, provided fundamental insights into the nature of matter. Antoine Lavoisier: Discovered the role of oxygen in combustion and respiration. Marcellin Berthelot: The first scientists to synthesize organic compounds such as formic acid, methane and acetylene from their elements.</p>",
                    solution_hi: "<p>43.(c) <strong>फ्रेडरिक वोहलर: </strong>प्रसिद्ध जर्मन रसायनज्ञ जो अकार्बनिक लवणों से यूरिया का संश्लेषण करने के लिए जाने जाते हैं, उन्होंने जीवनवाद सिद्धांत का खंडन किया कि कार्बनिक यौगिक केवल जीवित जीवों द्वारा ही उत्पादित किए जा सकते हैं। जॉन डाल्टन: परमाणु सिद्धांत के आविष्कारक, ने पदार्थ की प्रकृति में मौलिक अंतर्दृष्टि प्रदान की। एंटोनी लेवॉज़ियर: दहन और श्वसन में ऑक्सीजन की भूमिका की खोज की। मार्सेलिन बर्थेलॉट: फॉर्मिक एसिड, मीथेन और एसिटिलीन जैसे कार्बनिक यौगिकों को उनके तत्वों से संश्लेषित करने वाले पहले वैज्ञानिक।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "44",
                    section: "18",
                    question_en: "<p>44. Tanras Khan is associated with which of the following gharanas?</p>",
                    question_hi: "<p>44. तानरास खान (Tanras Khan) निम्नलिखित में से किस घराने से संबंधित हैं?</p>",
                    options_en: [
                        "<p>Delhi gharana</p>",
                        "<p>Benaras gharana</p>",
                        "<p>Agra gharana</p>",
                        "<p>Indore gharana</p>"
                    ],
                    options_hi: [
                        "<p>दिल्ली घराना</p>",
                        "<p>बनारस घराना</p>",
                        "<p>आगरा घराना</p>",
                        "<p>इंदौर घराना</p>"
                    ],
                    solution_en: "<p>44.(a) <strong>Delhi gharana.</strong> Tanras Khan was an Indian musician of the Hindustani Classical tradition known for being a luminary of the House of Delhi classical musicians. He was a court musician and music teacher to the last Mughal emperor Bahadur Shah Zafar II. Gharanas and Their Associated Musicians : Delhi - Vidushi Krishna Bisht, Ustad Zahoor Ahmed Khan. Benaras Gharana - Pandit Kishan Maharaj. Agra Gharana - Ustad Faiyaz Khan. Indore Gharana - Ustad Amir Khan.</p>",
                    solution_hi: "<p>44.(a) <strong>दिल्ली घराना।</strong> तानरास खान हिंदुस्तानी शास्त्रीय परंपरा के एक भारतीय संगीतकार थे, जिन्हें दिल्ली घराना के शास्त्रीय संगीतकारों के दिग्गज के रूप में जाना जाता है। वह अंतिम मुगल सम्राट बहादुर शाह जफर द्वितीय के दरबारी संगीतकार और संगीत शिक्षक थे। घराने एवं उनसे संबंधित संगीतकार : दिल्ली - विदुषी कृष्णा बिष्ट, उस्ताद जहूर अहमद खान। बनारस घराना - पंडित किशन महाराज। आगरा घराना - उस्ताद फ़ैयाज़ खान। इंदौर घराना - उस्ताद अमीर खान।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "45",
                    section: "18",
                    question_en: "<p>45. Which of the following is a part of apical meristem found in roots.</p>",
                    question_hi: "<p>45. निम्नलिखित में से कौन जड़ों में पाए जाने वाले शिखर विभज्यो तक (apical meristem) का एक भाग है।</p>",
                    options_en: [
                        "<p>Differentiating vascular tissue</p>",
                        "<p>Axillary bud</p>",
                        "<p>Protoderm</p>",
                        "<p>Leaf primordium</p>"
                    ],
                    options_hi: [
                        "<p>विभेदक संवहनी ऊतक (Differentiating vascular tissue)</p>",
                        "<p>अक्षीय कली (Axillary bud)</p>",
                        "<p>अधित्वक (Protoderm)</p>",
                        "<p>लीफ प्रिमोर्डियम (Leaf primordium)</p>"
                    ],
                    solution_en: "<p>45.(c) <strong>Protoderm.</strong> Apical meristem is the region of plant tissue where growth occurs, and it\'s found in both roots and shoots. In roots, the apical meristem consists of three primary meristematic tissues: Protoderm (outermost layer) - Gives rise to the epidermis. Ground meristem (middle layer) - Gives rise to the cortex and endodermis. Procambium (innermost layer) - Gives rise to the vascular tissue.</p>",
                    solution_hi: "<p>45.(c) <strong>अधित्वक ​​(प्रोटोडर्म)। </strong>शीर्षस्थ विभज्योतक (एपिकल मेरिस्टेम) पौधे के ऊतकों का वह क्षेत्र है जहाँ वृद्धि होती है, और यह जड़ों और टहनियों दोनों में पाया जाता है। जड़ों में, शीर्षस्थ विभज्योतक तीन प्राथमिक विभज्योतक ऊतकों से बना होता है: अधित्वक (सबसे बाहरी परत) - उपत्वक को जन्म देता है। भू-विभज्योतक (मध्य परत) - कॉर्टेक्स और एंडोडर्मिस को जन्म देता है। प्रोकैम्बियम (सबसे भीतरी परत) - संवहनी ऊतक को जन्म देता है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "46",
                    section: "18",
                    question_en: "<p>46. What is celebrated on October 15 in honor of Dr. A.P.J. Abdul Kalam\'s birth anniversary?</p>",
                    question_hi: "<p>46. डॉ. ए.पी.जे. अब्दुल कलाम की जयंती के उपलक्ष्य में 15 अक्टूबर को क्या मनाया जाता है?</p>",
                    options_en: [
                        "<p>National Science Day</p>",
                        "<p>World Student\'s Day</p>",
                        "<p>Teacher\'s Day</p>",
                        "<p>National Technology Day</p>"
                    ],
                    options_hi: [
                        "<p>राष्ट्रीय विज्ञान दिवस</p>",
                        "<p>विश्व छात्र दिवस</p>",
                        "<p>शिक्षक दिवस</p>",
                        "<p>राष्ट्रीय प्रौद्योगिकी दिवस</p>"
                    ],
                    solution_en: "<p>46.(b) <strong>World Student\'s Day, </strong>celebrated on the birth anniversary of Dr. A.P.J. Abdul Kalam, honors his remarkable legacy as India\'s \"Missile Man\" and a visionary leader. The theme for 2024, \"Empowering Students to be Agents of Change&rdquo;.</p>",
                    solution_hi: "<p>46.(b) <strong>विश्व छात्र दिवस,</strong> डॉ. ए.पी.जे. अब्दुल कलाम की जयंती पर मनाया जाने वाला भारत के \"मिसाइल मैन\" और दूरदर्शी नेता के रूप में उनकी उल्लेखनीय विरासत का सम्मान करता है। 2024 का थीम, \"छात्रों को बदलाव के एजेंट बनने के लिए सशक्त बनाना\"।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "47",
                    section: "18",
                    question_en: "<p>47. The largest inland salt lake &lsquo;Sambhar&rsquo; is located in the _________ of India.</p>",
                    question_hi: "<p>47. खारे पानी की सबसे बड़ी अंतर्देशीय झील \'सांभर (Sambhar)\' भारत के __________ में स्थित है?</p>",
                    options_en: [
                        "<p>north-east</p>",
                        "<p>north-west</p>",
                        "<p>south-west</p>",
                        "<p>south-east</p>"
                    ],
                    options_hi: [
                        "<p>उत्तर-पूर्व</p>",
                        "<p>उत्तर-पश्चिम</p>",
                        "<p>दक्षिण-पश्चिम</p>",
                        "<p>दक्षिण-पूर्व</p>"
                    ],
                    solution_en: "<p>47.(b) <strong>north-west. </strong>Sambhar Salt Lake (located in Rajasthan) spans 230 square kilometers. It is a significant depression in the Aravalli Range and is commonly referred to as the salt lake of Rajasthan. The lake receives water from six rivers: Mantha, Rupangarh, Khari, Khandela, Medtha and Samod.</p>",
                    solution_hi: "<p>47.(b) <strong>उत्तर-पश्चिम ।</strong> सांभर साल्ट लेक (राजस्थान में स्थित) 230 वर्ग किलोमीटर में फैली हुई है। यह अरावली पर्वतमाला में एक महत्वपूर्ण अवपात का प्रतिनिधित्व करता है और इसे आमतौर पर राजस्थान की साल्ट लेक के रूप में भी जाना जाता है। इस झील को छह नदियों से जल मिलता है: मंथा, रूपनगढ़, खारी, खंडेला, मेदथा और समोद।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "48",
                    section: "18",
                    question_en: "<p>48. Which of the following pairs is INCORRECTLY matched?.</p>",
                    question_hi: "<p>48. निम्नलिखित में से कौन-सा युग्म गलत सुमेलित है?</p>",
                    options_en: [
                        "<p>Lactose: Milk</p>",
                        "<p>Starch: Egg Yolk</p>",
                        "<p>Fructose: Grapes</p>",
                        "<p>Maltose: Wheat, cornmeal and barley</p>"
                    ],
                    options_hi: [
                        "<p>लैक्टोज: दूध</p>",
                        "<p>स्टार्च: अंडे की जर्द</p>",
                        "<p>फ्रक्टोज: अंगूर</p>",
                        "<p>माल्टोज: गेहूँ, कॉर्नमील और जौ</p>"
                    ],
                    solution_en: "<p>48.(b) <strong>Starch : Egg Yolk. </strong>Starch is the main storage polysaccharide of plants. It is the most important dietary source for human beings. High content of starch is found in cereals, roots, tubers and some vegetables but not in egg yolk. It is a polymer of &alpha;-glucose and consists of two components&mdash; Amylose and Amylopectin.</p>",
                    solution_hi: "<p>48.(b) <strong>स्टार्च: अंडे की जर्द। </strong>स्टार्च पौधों का मुख्य भंडारण पॉलीसैकेराइड है। यह मनुष्यों के लिए सबसे महत्वपूर्ण आहार स्रोत है। स्टार्च की उच्च मात्रा अनाज, जड़ों, कंद और कुछ सब्जियों में पाई जाती है, लेकिन अंडे की जर्द में नहीं। यह &alpha;-ग्लूकोज का बहुलक है और इसमें दो घटक होते हैं- एमाइलोज और एमाइलोपेक्टिन।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "49",
                    section: "18",
                    question_en: "<p>49. Which political party launched the Dr. Ambedkar Samman Yojana in December 2024 to support Dalit students pursuing higher education abroad?</p>",
                    question_hi: "<p>49. किस राजनीतिक पार्टी ने दिसंबर 2024 में डॉ. आंबेडकर सम्मान योजना शुरू की, जो विदेश में उच्च शिक्षा प्राप्त कर रहे दलित छात्रों को समर्थन प्रदान करती है?</p>",
                    options_en: [
                        "<p>Bharatiya Janata Party (BJP)</p>",
                        "<p>Indian National Congress (INC)</p>",
                        "<p>Aam Aadmi Party (AAP)</p>",
                        "<p>Bahujan Samaj Party (BSP)</p>"
                    ],
                    options_hi: [
                        "<p>भारतीय जनता पार्टी (BJP)</p>",
                        "<p>भारतीय राष्ट्रीय कांग्रेस (INC)</p>",
                        "<p>आम आदमी पार्टी (AAP)</p>",
                        "<p>बहुजन समाज पार्टी (BSP)</p>"
                    ],
                    solution_en: "<p>49.(c) <strong>Aam Aadmi Party (AAP).</strong><br>AAP leader Arvind Kejriwal announced the &lsquo;Dr. Ambedkar Samman Scholarship Yojana&rsquo; to fully fund travel and stay for Delhi\'s Dalit students studying at top global universities. The scheme aims to empower education and honor Dr. B.R. Ambedkar\'s vision.</p>",
                    solution_hi: "<p>49.(c) <strong>आम आदमी पार्टी (AAP)।</strong><br>AAP नेता अरविंद केजरीवाल ने \'डॉ. आंबेडकर सम्मान छात्रवृत्ति योजना\' की घोषणा की, जिसके तहत दिल्ली के दलित छात्रों को शीर्ष वैश्विक विश्वविद्यालयों में पढ़ाई के लिए यात्रा और रहने का पूरा खर्च दिया जाएगा। इस योजना का उद्देश्य शिक्षा को सशक्त बनाना और डॉ. बी. आर. आंबेडकर के दृष्टिकोण को सम्मानित करना है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "50",
                    section: "17",
                    question_en: "<p>50. _____________ is the first Indian dancer, a transgender, to be honoured with Padma Shri.</p>",
                    question_hi: "<p>50. ______________पहली ट्रांसजेंडर भारतीय नर्तकी है , जिन्हे पद्म श्री से सम्मानित किया गया है</p>",
                    options_en: [
                        "<p>Protima Bedi</p>",
                        "<p>Alarmel Valli</p>",
                        "<p>Narthaki Nataraj</p>",
                        "<p>Yamini Krishnamurthy</p>"
                    ],
                    options_hi: [
                        "<p>प्रोतिमा बेदी</p>",
                        "<p>अलार्मेल वल्ली</p>",
                        "<p>नर्तकी नटराज</p>",
                        "<p>यामिनी कृष्णमूर्ति</p>"
                    ],
                    solution_en: "<p>50.(c)<strong> Narthaki Nataraj</strong> (Bharatanatyam) has received the Sangeet Natak Akademi Award (2011) and the Padma Shri (2019). Alarmel Valli (Bharatanatyam) has been honored with the Padma Bhushan (2004), the Sangeet Natak Akademi Award (2001), and the Padma Shri (1991). Yamini Krishnamurthy (Bharatanatyam and Kuchipudi) has been awarded the Padma Shri (1968), the Padma Bhushan (2001), the Padma Vibhushan (2016), and the Sangeet Natak Akademi Award (1977).</p>",
                    solution_hi: "<p>50.(c) <strong>नर्तकी नटराज</strong> (भरतनाट्यम) को संगीत नाटक अकादमी पुरस्कार (2011) और पद्म श्री (2019) से सम्मानित किया गया है। अलार्मेल वल्ली (भरतनाट्यम) को पद्म भूषण (2004), संगीत नाटक अकादमी पुरस्कार (2001), और पद्म श्री (1991) से सम्मानित किया गया है। यामिनी कृष्णमूर्ति (भरतनाट्यम और कुचिपुड़ी) को पद्म श्री (1968), पद्म भूषण (2001), पद्म विभूषण (2016) और संगीत नाटक अकादमी पुरस्कार (1977) से सम्मानित किया गया है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "51",
                    section: "17",
                    question_en: "<p>51. A shopkeeper, on the eve of Diwali, allowed a series of discounts on television sets. Find the selling price of a television set, if the marked price of television is ₹1,200 and successive discounts are 15% and 10%.</p>",
                    question_hi: "<p>51. एक दुकानदार ने दिवाली की पूर्व संध्या पर टेलीविजन सेटों पर कई छूटें दीं। यदि टेलीविज़न सेट का अंकित मूल्य ₹1,200 है और क्रमागत छूटें 15% और 10% हैं तो टेलीविज़न का विक्रय मूल्य ज्ञात कीजिए।</p>",
                    options_en: [
                        "<p>₹945</p>",
                        "<p>₹918</p>",
                        "<p>₹975</p>",
                        "<p>₹965</p>"
                    ],
                    options_hi: [
                        "<p>₹945</p>",
                        "<p>₹918</p>",
                        "<p>₹975</p>",
                        "<p>₹965</p>"
                    ],
                    solution_en: "<p>51.(b) <br>Selling price of television = 1200 &times; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>17</mn><mn>20</mn></mfrac></math> &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>9</mn><mn>10</mn></mfrac></mstyle></math> = ₹918</p>",
                    solution_hi: "<p>51.(b) <br>टेलिविजन का विक्रय मूल्य = 1200 &times; <math display=\"inline\"><mfrac><mrow><mn>17</mn></mrow><mrow><mn>20</mn></mrow></mfrac></math> &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>9</mn><mn>10</mn></mfrac></mstyle></math> = ₹918</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "52",
                    section: "17",
                    question_en: "<p>52. If p(<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math> + cot 30&deg;) = tan&sup3;60&deg; - 2sin60&deg;, then, find the value of P .</p>",
                    question_hi: "<p>52. यदि p(<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math> + cot 30&deg;) = tan&sup3;60&deg; - 2sin60&deg;, है, तो P का मान ज्ञात करें |</p>",
                    options_en: [
                        "<p>-1</p>",
                        "<p>2<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math></p>",
                        "<p>1</p>",
                        "<p><math display=\"inline\"><mi mathvariant=\"bold-italic\">&#160;</mi><msqrt><mn>3</mn></msqrt></math></p>"
                    ],
                    options_hi: [
                        "<p>-1</p>",
                        "<p>2<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math></p>",
                        "<p>1</p>",
                        "<p><math display=\"inline\"><mi mathvariant=\"bold-italic\">&#160;</mi><msqrt><mn>3</mn></msqrt></math></p>"
                    ],
                    solution_en: "<p>52.(c) p( <math display=\"inline\"><msqrt><mn>3</mn></msqrt></math> + cot 30&deg;) = tan&sup3;60&deg; - 2sin60&deg;<br>Put the value of cot 30&deg; =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math>, tan 60&deg; =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math> and sin 60&deg; =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><msqrt><mn>3</mn></msqrt><mn>2</mn></mfrac></mstyle></math><br><math display=\"inline\"><mo>&#8658;</mo></math> p(<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math>) = (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math>)&sup3; - 2 &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><msqrt><mn>3</mn></msqrt><mn>2</mn></mfrac></mstyle></math><br><math display=\"inline\"><mo>&#8658;</mo></math> 2<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math>p = 3<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math> -&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math><br><math display=\"inline\"><mo>&#8658;</mo></math> 2<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math>p = 2<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math><br><math display=\"inline\"><mo>&#8658;</mo></math> p = 1</p>",
                    solution_hi: "<p>52.(c) p( <math display=\"inline\"><msqrt><mn>3</mn></msqrt></math> + cot 30&deg;) = tan&sup3;60&deg; - 2sin60&deg;<br>cot 30&deg; =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math> , tan 60&deg; =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math> और Sin 60&deg; =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><msqrt><mn>3</mn></msqrt><mn>2</mn></mfrac></mstyle></math> के मान रखने पर<br><math display=\"inline\"><mo>&#8658;</mo></math> p(<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math>) = (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math>)&sup3; - 2 &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><msqrt><mn>3</mn></msqrt><mn>2</mn></mfrac></mstyle></math><br><math display=\"inline\"><mo>&#8658;</mo></math> 2<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math>p = 3<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math> -&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math><br><math display=\"inline\"><mo>&#8658;</mo></math> 2<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math>p = 2<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math><br><math display=\"inline\"><mo>&#8658;</mo></math> p = 1</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "53",
                    section: "17",
                    question_en: "<p>53. If <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><mn>1</mn><mo>-</mo><mi>sin</mi><mi mathvariant=\"normal\">&#160;</mi><mi mathvariant=\"normal\">&#952;</mi></mrow></mfrac></math> +&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>1</mn><mrow><mn>1</mn><mo>+</mo><mi>sin</mi><mi mathvariant=\"normal\">&#160;</mi><mi mathvariant=\"normal\">&#952;</mi></mrow></mfrac></mstyle></math> = 4 sec &theta;, (0 &lt; &theta; &lt; 90&deg;), then the value of (cot&theta; + cosec&theta; ) is:</p>",
                    question_hi: "<p>53. यदि <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><mn>1</mn><mo>-</mo><mi>sin</mi><mi mathvariant=\"normal\">&#160;</mi><mi mathvariant=\"normal\">&#952;</mi></mrow></mfrac></math> +&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>1</mn><mrow><mn>1</mn><mo>+</mo><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#160;</mi><mi>&#952;</mi></mrow></mfrac></mstyle></math> = 4 sec &theta;, (0 &lt; &theta; &lt; 90&deg;), तो (cot&theta; + cosec&theta; ) का मान होगा:</p>",
                    options_en: [
                        "<p><math display=\"inline\"><msqrt><mn>3</mn></msqrt></math></p>",
                        "<p><math display=\"inline\"><msqrt><mn>6</mn></msqrt></math></p>",
                        "<p><math display=\"inline\"><msqrt><mn>2</mn></msqrt></math></p>",
                        "<p><math display=\"inline\"><msqrt><mn>5</mn></msqrt></math></p>"
                    ],
                    options_hi: [
                        "<p><math display=\"inline\"><msqrt><mn>3</mn></msqrt></math></p>",
                        "<p><math display=\"inline\"><msqrt><mn>6</mn></msqrt></math></p>",
                        "<p><math display=\"inline\"><msqrt><mn>2</mn></msqrt></math></p>",
                        "<p><math display=\"inline\"><msqrt><mn>5</mn></msqrt></math></p>"
                    ],
                    solution_en: "<p>53.(a)</p>\n<p dir=\"ltr\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><mn>1</mn><mo>-</mo><mi>sin</mi><mi>&#952;</mi></mrow></mfrac><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mfrac><mn>1</mn><mrow><mn>1</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mi>sin</mi><mi>&#952;</mi></mrow></mfrac><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>4</mn><mi>s</mi><mi>e</mi><mi>c</mi><mi>&#952;</mi></math></p>\n<p dir=\"ltr\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mi>sin</mi><mi>&#952;</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>1</mn><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mi>sin</mi><mi>&#952;</mi></mrow><mrow><mo>(</mo><mn>1</mn><mo>-</mo><mi>sin</mi><mi>&#952;</mi><mo>)</mo><mo>(</mo><mn>1</mn><mo>-</mo><mi>sin</mi><mi>&#952;</mi><mo>)</mo></mrow></mfrac><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>4</mn><mi>s</mi><mi>e</mi><mi>c</mi><mi>&#952;</mi></math></p>\n<p dir=\"ltr\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mrow><mn>1</mn><mo>-</mo><msup><mi>sin</mi><mn>2</mn></msup><mi>&#952;</mi></mrow></mfrac><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>4</mn><mi>s</mi><mi>e</mi><mi>c</mi><mi>&#952;</mi></math></p>\n<p dir=\"ltr\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mrow><msup><mi>cos</mi><mn>2</mn></msup><mi>&#952;</mi></mrow></mfrac><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>4</mn><mi>s</mi><mi>e</mi><mi>c</mi><mi>&#952;</mi></math></p>\n<p dir=\"ltr\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>2</mn><mi>s</mi><mi>e</mi><msup><mi>c</mi><mn>2</mn></msup><mi>&#952;</mi><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>4</mn><mi>s</mi><mi>e</mi><mi>c</mi><mi>&#952;</mi></math></p>\n<p dir=\"ltr\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#952;</mi></math> = 60&deg;</p>\n<p dir=\"ltr\">(cot<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#952;</mi></math> +cosec<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#952;</mi></math>) = cot60&deg; + cosec60&deg;</p>\n<p dir=\"ltr\">= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msqrt><mn>3</mn></msqrt></mfrac><mo>&#160;</mo><mo>+</mo><mfrac><mn>2</mn><msqrt><mn>3</mn></msqrt></mfrac><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mfrac><mn>3</mn><msqrt><mn>3</mn></msqrt></mfrac><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><msqrt><mn>3</mn></msqrt></math></p>\n<p>&nbsp;</p>",
                    solution_hi: "<p>53.(a)&nbsp;</p>\n<p dir=\"ltr\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><mn>1</mn><mo>-</mo><mi>sin</mi><mi>&#952;</mi></mrow></mfrac><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mfrac><mn>1</mn><mrow><mn>1</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mi>sin</mi><mi>&#952;</mi></mrow></mfrac><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>4</mn><mi>s</mi><mi>e</mi><mi>c</mi><mi>&#952;</mi></math></p>\n<p dir=\"ltr\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mi>sin</mi><mi>&#952;</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>1</mn><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mi>sin</mi><mi>&#952;</mi></mrow><mrow><mo>(</mo><mn>1</mn><mo>-</mo><mi>sin</mi><mi>&#952;</mi><mo>)</mo><mo>(</mo><mn>1</mn><mo>-</mo><mi>sin</mi><mi>&#952;</mi><mo>)</mo></mrow></mfrac><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>4</mn><mi>s</mi><mi>e</mi><mi>c</mi><mi>&#952;</mi></math></p>\n<p dir=\"ltr\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mrow><mn>1</mn><mo>-</mo><msup><mi>sin</mi><mn>2</mn></msup><mi>&#952;</mi></mrow></mfrac><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>4</mn><mi>s</mi><mi>e</mi><mi>c</mi><mi>&#952;</mi></math></p>\n<p dir=\"ltr\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mrow><msup><mi>cos</mi><mn>2</mn></msup><mi>&#952;</mi></mrow></mfrac><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>4</mn><mi>s</mi><mi>e</mi><mi>c</mi><mi>&#952;</mi></math></p>\n<p dir=\"ltr\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>2</mn><mi>s</mi><mi>e</mi><msup><mi>c</mi><mn>2</mn></msup><mi>&#952;</mi><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>4</mn><mi>s</mi><mi>e</mi><mi>c</mi><mi>&#952;</mi></math></p>\n<p dir=\"ltr\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#952;</mi></math> = 60&deg;</p>\n<p dir=\"ltr\">(cot<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#952;</mi></math> +cosec<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#952;</mi></math>) = cot60&deg; + cosec60&deg;</p>\n<p dir=\"ltr\">= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msqrt><mn>3</mn></msqrt></mfrac><mo>&#160;</mo><mo>+</mo><mfrac><mn>2</mn><msqrt><mn>3</mn></msqrt></mfrac><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mfrac><mn>3</mn><msqrt><mn>3</mn></msqrt></mfrac><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><msqrt><mn>3</mn></msqrt></math></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "54",
                    section: "17",
                    question_en: "<p>54. If <math display=\"inline\"><msup><mrow><mi>x</mi></mrow><mrow><mn>2</mn></mrow></msup><mo>+</mo><mfrac><mrow><mn>1</mn></mrow><mrow><msup><mrow><mi>x</mi></mrow><mrow><mn>2</mn></mrow></msup></mrow></mfrac><mo>=</mo><mn>119</mn><mi>&#160;</mi><mo>(</mo><mi>x</mi><mo>&#62;</mo><mn>0</mn><mo>)</mo></math>, then the value of (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi mathvariant=\"normal\">x</mi><mn>3</mn></msup><mo>+</mo><mstyle displaystyle=\"false\"><mfrac><mn>1</mn><msup><mi mathvariant=\"normal\">x</mi><mn>3</mn></msup></mfrac></mstyle></math>) is :</p>",
                    question_hi: "<p>54. यदि <math display=\"inline\"><msup><mrow><mi>x</mi></mrow><mrow><mn>2</mn></mrow></msup><mo>+</mo><mfrac><mrow><mn>1</mn></mrow><mrow><msup><mrow><mi>x</mi></mrow><mrow><mn>2</mn></mrow></msup></mrow></mfrac><mo>=</mo><mn>119</mn><mi>&#160;</mi><mo>(</mo><mi>x</mi><mo>&#62;</mo><mn>0</mn><mo>)</mo></math> है, तो (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>x</mi><mn>3</mn></msup><mo>+</mo><mstyle displaystyle=\"false\"><mfrac><mn>1</mn><msup><mi>x</mi><mn>3</mn></msup></mfrac></mstyle></math>) का मान ज्ञात कीजिए I</p>",
                    options_en: [
                        "<p>1331</p>",
                        "<p>1298</p>",
                        "<p>1125</p>",
                        "<p>1920</p>"
                    ],
                    options_hi: [
                        "<p>1331</p>",
                        "<p>1298</p>",
                        "<p>1125</p>",
                        "<p>1920</p>"
                    ],
                    solution_en: "<p>54.(b)<br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>x</mi><mn>2</mn></msup><mo>+</mo><mfrac><mn>1</mn><msup><mi>x</mi><mn>2</mn></msup></mfrac><mo>=</mo><mn>119</mn><mi>&#160;</mi><mo>(</mo><mi>x</mi><mo>&#62;</mo><mn>0</mn><mo>)</mo></math><br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo><msup><mi>x</mi><mn>2</mn></msup><mo>+</mo><mfrac><mn>1</mn><msup><mi>x</mi><mn>2</mn></msup></mfrac></math> + 2 = 119 + 2 = 121<br><math display=\"inline\"><mo>&#8658;</mo></math>(x + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>1</mn><mi>x</mi></mfrac></mstyle></math>)&sup2; = 121<br><math display=\"inline\"><mo>&#8658;</mo></math>(x + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>1</mn><mi>x</mi></mfrac></mstyle></math>) = 11<br>Now, <br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><mi>x</mi><mo>+</mo><mfrac><mn>1</mn><mi>x</mi></mfrac><mo>)</mo></mrow><mn>3</mn></msup></math> = x&sup3; +&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>1</mn><msup><mi>x</mi><mn>3</mn></msup></mfrac></mstyle></math> + 3 (x + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>1</mn><mi>x</mi></mfrac></mstyle></math>) .<br><math display=\"inline\"><mo>&#8658;</mo></math>1331 = x&sup3; +&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>1</mn><msup><mi>x</mi><mn>3</mn></msup></mfrac></mstyle></math> + 3 &times; 11<br><math display=\"inline\"><mo>&#8658;</mo></math>1331 - 33 = x&sup3; +&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>1</mn><msup><mi>x</mi><mn>3</mn></msup></mfrac></mstyle></math><br><math display=\"inline\"><mo>&#8658;</mo></math>x&sup3; +&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>1</mn><msup><mi>x</mi><mn>3</mn></msup></mfrac></mstyle></math> = 1298</p>",
                    solution_hi: "<p>54.(b)<br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup><mo>+</mo><mfrac><mn>1</mn><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup></mfrac><mo>=</mo><mn>119</mn><mi mathvariant=\"normal\">&#160;</mi><mo>(</mo><mi mathvariant=\"normal\">x</mi><mo>&#62;</mo><mn>0</mn><mo>)</mo></math><br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup><mo>+</mo><mfrac><mn>1</mn><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup></mfrac></math> + 2 = 119 + 2 = 121<br><math display=\"inline\"><mo>&#8658;</mo></math>(x + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>1</mn><mi>x</mi></mfrac></mstyle></math>)&sup2; = 121<br><math display=\"inline\"><mo>&#8658;</mo></math>(x + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>1</mn><mi>x</mi></mfrac></mstyle></math>) = 11<br>अब, <br><math display=\"inline\"><msup><mrow><mo>(</mo><mi>x</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mfrac><mrow><mn>1</mn></mrow><mrow><mi>x</mi></mrow></mfrac><mo>)</mo></mrow><mrow><mn>3</mn></mrow></msup></math> = x&sup3; +&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>1</mn><msup><mi>x</mi><mn>3</mn></msup></mfrac></mstyle></math> + 3 (x + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>1</mn><mi>x</mi></mfrac></mstyle></math>) .<br><math display=\"inline\"><mo>&#8658;</mo></math>1331 = x&sup3; +&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>1</mn><msup><mi>x</mi><mn>3</mn></msup></mfrac></mstyle></math> + 3 &times; 11<br><math display=\"inline\"><mo>&#8658;</mo></math>1331 - 33 = x&sup3; +&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>1</mn><msup><mi>x</mi><mn>3</mn></msup></mfrac></mstyle></math><br><math display=\"inline\"><mo>&#8658;</mo></math><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>x</mi><mn>3</mn></msup></math> +&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>1</mn><msup><mi>x</mi><mn>3</mn></msup></mfrac></mstyle></math> = 1298</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "55",
                    section: "17",
                    question_en: "<p>55. Raj divides ₹1,200 in the ratio 2 : 1 : 3 among three of his friends. The amount equal to the sum of three times the largest share and two times the smallest share is:</p>",
                    question_hi: "<p>55. राज ₹1,200 को अपने तीन मित्रों के बीच 2 : 1 : 3 के अनुपात में बांटता है। सबसे बड़े हिस्से के तीन गुना और सबसे छोटे हिस्से के दो गुना के योग के बराबर धनराशि ज्ञात करें।</p>",
                    options_en: [
                        "<p>₹2,400</p>",
                        "<p>₹1,800</p>",
                        "<p>₹2,200</p>",
                        "<p>₹2,000</p>"
                    ],
                    options_hi: [
                        "<p>₹2,400</p>",
                        "<p>₹1,800</p>",
                        "<p>₹2,200</p>",
                        "<p>₹2,000</p>"
                    ],
                    solution_en: "<p>55.(c)<br>2x&nbsp;+ x + 3x = 1200<br><math display=\"inline\"><mo>&#8658;</mo></math>6x = 1200<br><math display=\"inline\"><mo>&#8658;</mo></math>x = 200<br>Now,<br>3(3x) + 2(x) = 11 x = 11 &times; 200 = 2200</p>",
                    solution_hi: "<p>55.(c)<br>2x&nbsp;+ x + 3x = 1200<br><math display=\"inline\"><mo>&#8658;</mo></math> 6x = 1200<br><math display=\"inline\"><mo>&#8658;</mo></math> x = 200<br>अब ,<br>3(3x) + 2(x) = 11 x = 11 &times; 200 = 2200</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "56",
                    section: "17",
                    question_en: "<p>56. A mixture contains alcohol and water in the ratio of 12 : 5. On adding 14 liters of water, the ratio of alcohol to water becomes 4 : 3. The quantity of alcohol in the mixture is:</p>",
                    question_hi: "<p>56. एक मिश्रण में ऐल्कोहॉल और पानी का अनुपात 12 : 5 है। इस मिश्रण में 14 लीटर पानी मिलाने पर ऐल्कोहॉल और पानी का अनुपात 4 : 3 हो जाता है। मिश्रण में ऐल्कोहॉल की मात्रा कितनी है?</p>",
                    options_en: [
                        "<p>42 L</p>",
                        "<p>18 L</p>",
                        "<p>28 L</p>",
                        "<p>30 L</p>"
                    ],
                    options_hi: [
                        "<p>42 लीटर</p>",
                        "<p>18 लीटर</p>",
                        "<p>28 लीटर</p>",
                        "<p>30 लीटर</p>"
                    ],
                    solution_en: "<p>56.(a)<br>Let the quantity of alcohol and water in the mi<math display=\"inline\"><mi>x</mi></math>ture be 12x and 5x respectively.<br>According to the question,<br><math display=\"inline\"><mfrac><mrow><mn>12</mn><mi>x</mi></mrow><mrow><mn>5</mn><mi>x</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>14</mn></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>4</mn><mn>3</mn></mfrac></mstyle></math><br>36<math display=\"inline\"><mi>x</mi></math> = 20x + 56<br>16<math display=\"inline\"><mi>x</mi></math> = 56, x =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>7</mn><mn>2</mn></mfrac></mstyle></math><br>Quantity of alcohol in the mixture = 12 &times; <math display=\"inline\"><mfrac><mrow><mn>7</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> = 42 liter</p>",
                    solution_hi: "<p>56.(a)<br>माना मिश्रण में अल्कोहल और पानी की मात्रा क्रमशः 12x&nbsp;और 5x है।<br>प्रश्न के अनुसार,<br><math display=\"inline\"><mfrac><mrow><mn>12</mn><mi>x</mi></mrow><mrow><mn>5</mn><mi>x</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>14</mn></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>4</mn><mn>3</mn></mfrac></mstyle></math><br>36<math display=\"inline\"><mi>x</mi></math> = 20x + 56<br>16x&nbsp;= 56, x =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>7</mn><mn>2</mn></mfrac></mstyle></math><br>मिश्रण में अल्कोहल की मात्रा = 12 &times; <math display=\"inline\"><mfrac><mrow><mn>7</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> = 42 लीटर</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "57",
                    section: "17",
                    question_en: "<p>57. A group of men decided to complete a job in 4 days. However, since 10 men dropped out every day, the job got completed at the end of the 7th day. How many men were there in the beginning ?</p>",
                    question_hi: "<p>57. पुरुषों के एक समूह ने 4 दिनों में एक काम पूरा करने का फैसला किया। हालाँकि, चूँकि हर दिन 10 पुरुष बाहर हो गए, इसलिए काम 7वें दिन के अंत में पूरा हो पाया । शुरुआत में कितने पुरुष थे ?</p>",
                    options_en: [
                        "<p>140</p>",
                        "<p>70</p>",
                        "<p>35</p>",
                        "<p>90</p>"
                    ],
                    options_hi: [
                        "<p>140</p>",
                        "<p>70</p>",
                        "<p>35</p>",
                        "<p>90</p>"
                    ],
                    solution_en: "<p>57.(b)<br>Let the no of men in the beginning be x and efficiency of each men be 1 unit<br>Total work = 4&times;x = 4x<br>Now, work done in 1st, 2nd, 3rd, 4th day are x, (x-10), (x-20),(x-30) &hellip;&hellip;&hellip;(given series in AP)<br>Then, Work done in 7th day = a+6d = x +6(-10) = x-60<br>ATQ,<br>x+(x-10)+(x-20)+(x-30)+.......(x-60) = 4x<br>7x - 210 = 4x<br>3x = 210<br>x = 70</p>",
                    solution_hi: "<p>57.(b)<br>माना कि शुरुआत में पुरुषों की संख्या = x, और प्रत्येक व्यक्ति की दक्षता = 1 इकाई<br>कुल कार्य = 4&times;x = 4x<br>अब, पहले, दूसरे, तीसरे, चौथे दिन में किया गया कार्य x, (x-10), (x-20),(x-30) &hellip;&hellip;&hellip;( AP श्रृंखला)<br>फिर, 7वें दिन में किया गया कार्य = a + 6d = x + 6(-10) = x - 60<br>प्रश्न के अनुसार ,<br>x+(x-10)+(x-20)+(x-30)+.......(x-60) = 4x<br>7x - 210 = 4x<br>3x = 210<br>x = 70</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "58",
                    section: "17",
                    question_en: "<p>58. Two circles with centres M and N have radii 5 cm and 8 cm, respectively. The circles touch each other externally at point T. A line PR is drawn such that the points M, T and N lie on PR, P being closer to M. From P, a tangent PQ = 12 cm is drawn to the circle with centre M touching at Q, and from R, another tangent RS = 15 cm is drawn to the circle with centre N touching at S. What is the length (in cm) of PR ?</p>",
                    question_hi: "<p>58. M और N केंद्र वाले दो वृत्तों की त्रिज्याएँ क्रमशः 5 सेमीऔर 8 सेमी हैं। वृत्त बिंदु T पर एक दूसरे को बाह्य रूप से स्पर्श करते हैं। एक रेखा PR इस प्रकार खींची जाती है कि बिंदु M, T और N, PR पर स्थित हों, और P, M के निकट हो। P से, केंद्र M वाले वृत्त पर, एक स्पर्श रेखा PQ = 12 सेमी खींची जाती है, जो Q पर स्पर्श करती है ,और R से, केंद्र N वाले वृत्त पर, एक अन्य स्पर्श रेखा RS = 15 सेमी खींची जाती है, जो S पर स्पर्श करती है। PR की लंबाई (सेमी में) कितनी है ?</p>",
                    options_en: [
                        "<p>53</p>",
                        "<p>43</p>",
                        "<p>26</p>",
                        "<p>37</p>"
                    ],
                    options_hi: [
                        "<p>53</p>",
                        "<p>43</p>",
                        "<p>26</p>",
                        "<p>37</p>"
                    ],
                    solution_en: "<p>58.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742185648484.png\" alt=\"rId60\" width=\"274\" height=\"130\"><br>(8 , 15 , 17) are pythagorean triplet <br>RN = 17 cm<br>(5 , 12 , 13) are pythagorean triplet<br>MP = 13 cm<br>Now, PR = RN + NT + TM + MP = 17 + 8 + 5 + 13 = 43 cm</p>",
                    solution_hi: "<p>58.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742185648484.png\" alt=\"rId60\" width=\"274\" height=\"130\"><br>(8 , 15 , 17) पाइथागोरस त्रिक हैं <br>RN = 17 सेमी<br>(5, 12, 13) पाइथागोरस त्रिक हैं<br>MP = 13 सेमी<br>अब, PR = RN + NT + TM + MP = 17 + 8 + 5 + 13 = 43 सेमी</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "59",
                    section: "17",
                    question_en: "<p>59. Two pipes P and Q can fill a tank in 36 minutes and 45 minutes, respectively. If both pipes are opened together, the time taken to fill the tank is:</p>",
                    question_hi: "<p>59. दो पाइप P और Q एक टंकी को क्रमशः 36 मिनट और 45 मिनट में भर सकते हैं। यदि दोनों पाइपों को एक साथ खोल दिया जाए, तो टंकी को भरने में कितना समय लगेगा?</p>",
                    options_en: [
                        "<p>81 minutes</p>",
                        "<p>20 minutes</p>",
                        "<p>40.5 minutes</p>",
                        "<p>10 minutes</p>"
                    ],
                    options_hi: [
                        "<p>81 मिनट</p>",
                        "<p>20 मिनट</p>",
                        "<p>40.5 मिनट</p>",
                        "<p>10 मिनट</p>"
                    ],
                    solution_en: "<p>59.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742185648602.png\" alt=\"rId61\" width=\"190\" height=\"170\"><br>When both pipes are open , then time to fill the tank = <math display=\"inline\"><mfrac><mrow><mn>180</mn></mrow><mrow><mn>9</mn></mrow></mfrac></math> = 20 min</p>",
                    solution_hi: "<p>59.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742185648759.png\" alt=\"rId62\" width=\"190\" height=\"170\"><br>जब दोनों पाइप खुले हों, तो टैंक भरने मे लगा समय = <math display=\"inline\"><mfrac><mrow><mn>180</mn></mrow><mrow><mn>9</mn></mrow></mfrac></math> = 20 मिनट</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "60",
                    section: "17",
                    question_en: "<p>60. A person travels from one place to another at 60 km/hr and returns at 75 km/hr. If the total time taken is 6 hours, then find the total distance travelled.</p>",
                    question_hi: "<p>60. एक व्यक्ति एक स्थान से दूसरे स्थान की यात्रा 60 km/hr की चाल से और वापसी की यात्रा 75 km/hr की चाल से तय करता है। यदि यात्रा में लिया गया कुल समय 6 घंटे है, तो तय की गई कुल दूरी ज्ञात कीजिए।</p>",
                    options_en: [
                        "<p>250 km</p>",
                        "<p>240 km</p>",
                        "<p>400 km</p>",
                        "<p>300 km</p>"
                    ],
                    options_hi: [
                        "<p>250 km</p>",
                        "<p>240 km</p>",
                        "<p>400 km</p>",
                        "<p>300 km</p>"
                    ],
                    solution_en: "<p>60.(c) Let distance be <math display=\"inline\"><mi>x</mi></math> km <br>According to question,<br><math display=\"inline\"><mo>&#8658;</mo></math><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>x</mi><mn>60</mn></mfrac><mo>+</mo><mfrac><mi>x</mi><mn>75</mn></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5</mn><mi>x</mi><mo>+</mo><mn>4</mn><mi>x</mi></mrow><mn>300</mn></mfrac><mo>=</mo><mn>6</mn></math><br><math display=\"inline\"><mo>&#8658;</mo></math> 9x = 1800<br><math display=\"inline\"><mo>&#8658;</mo></math> x = 200 km<br>Hence, total distance travelled = 2 &times; 200 = 400 km</p>",
                    solution_hi: "<p>60.(c) माना दूरी <math display=\"inline\"><mi>x</mi></math> किमी है <br>प्रश्न के अनुसार,<br><math display=\"inline\"><mo>&#8658;</mo></math><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>x</mi><mn>60</mn></mfrac><mo>+</mo><mfrac><mi>x</mi><mn>75</mn></mfrac></math>&nbsp;= 6<br><math display=\"inline\"><mo>&#8658;</mo></math><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5</mn><mi>x</mi><mo>+</mo><mn>4</mn><mi>x</mi></mrow><mn>300</mn></mfrac><mo>=</mo><mn>6</mn></math><br><math display=\"inline\"><mo>&#8658;</mo></math>9x = 1800<br><math display=\"inline\"><mo>&#8658;</mo></math>x = 200 km<br>अतः तय की गई कुल दूरी = 2 &times; 200 = 400 km</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "61",
                    section: "17",
                    question_en: "<p>61. From a point T, a tangent TP at point P, is drawn to a circle with centre O. A secant TQR (point Q is near to point T) is drawn from the point T. ∆PQR is inscribed into the circle by joining the points P, Q and R. Draw lines OQ and OR. If &ang;PTQ is 27&deg; and &ang; TPQ = 55&deg;, what is the degree measure of &ang;ROQ ?</p>",
                    question_hi: "<p>61. केंद्र O वाले एक वृत्त पर बिंदु T से, बिंदु P तक एक स्पर्श रेखा TP खींची जाती है। एक छेदक रेखा TQR (बिंदु Q बिंदु T के समीप है) बिंदु T से खींची जाती है। बिंदुओं P , Q और R को स्पर्श करते हुए वृत्त के अंदर एक ∆PQR बनाया गया है। रेखाएँ OQ और OR खींचिए। यदि &ang;PTQ = 27&deg; है और &ang;TPQ = 55&deg; है, तो &ang;ROQ का डिग्री माप क्या है ?</p>",
                    options_en: [
                        "<p>86</p>",
                        "<p>82</p>",
                        "<p>98</p>",
                        "<p>94</p>"
                    ],
                    options_hi: [
                        "<p>86</p>",
                        "<p>82</p>",
                        "<p>98</p>",
                        "<p>94</p>"
                    ],
                    solution_en: "<p>61.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742185648846.png\" alt=\"rId63\" width=\"207\" height=\"115\"><br>In <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#916;PTQ</mi></math><br>&ang;PQR = 55&deg;&nbsp;+ 27&deg; = 82&deg; &hellip; (exterior angle property)<br>&ang;RPS = &ang;PQR <br>&ang;RPS = 82&deg;<br>Now,<br>&ang;RPS + &ang;RPQ + &ang;QPT = 180&deg;&nbsp;&hellip; (linear pair)<br>82&deg; + &ang;RPQ + 55&deg; = 180&deg;<br>&ang;RPQ = 180&deg;&nbsp;- 137&deg; = 43&deg;<br>Now,<br>&ang;ROQ = 2&ang;RPQ = 2 <math display=\"inline\"><mo>&#215;</mo></math> 43&deg; = 86&deg;</p>",
                    solution_hi: "<p>61.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742185648846.png\" alt=\"rId63\" width=\"207\" height=\"115\"><br>In <math display=\"inline\"><mi>&#916;</mi><mi>P</mi><mi>T</mi><mi>Q</mi></math><br>&ang;PQR = 55&deg; + 27&deg; = 82&deg; &hellip; (बाहरी कोण गुण)<br>&ang;RPS = &ang;PQR <br>&ang;RPS = 82&deg;<br>अब,<br>&ang;RPS + &ang;RPQ + &ang;QPT = 180&deg; &hellip; (रैखिक युग्म)<br>82&deg; + &ang;RPQ + 55&deg; = 180&deg;<br>&ang;RPQ = <math display=\"inline\"><msup><mrow><mn>180</mn></mrow><mrow><mo>&#176;</mo></mrow></msup></math> - 137&deg; = 43&deg;<br>अब,<br>&ang;ROQ = 2&ang;RPQ = 2 <math display=\"inline\"><mo>&#215;</mo></math> 43&deg; = 86&deg;</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "62",
                    section: "17",
                    question_en: "<p>62. A boat covers 24 km upstream and 36 km downstream in 10 hours, and 36 km upstream and 24 km downstream in 12 hours. The speed of the current is:</p>",
                    question_hi: "<p>62. एक नाव धारा के प्रतिकूल 24 km की दूरी और धारा के अनुकूल 36 km की दूरी 10 घंटे में तय करती है, और धारा के प्रतिकूल 36 km की दूरी और धारा के अनुकूल 24 km की दूरी 12 घंटे में तय करती है। धारा की चाल ज्ञात करें।</p>",
                    options_en: [
                        "<p><math display=\"inline\"><mfrac><mrow><mn>26</mn></mrow><mrow><mn>9</mn><mi>&#160;</mi></mrow></mfrac></math> km/h</p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>33</mn></mrow><mrow><mn>13</mn></mrow></mfrac></math> km/h</p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>25</mn></mrow><mrow><mn>8</mn><mi>&#160;</mi></mrow></mfrac></math> km/h</p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>24</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> km/h</p>"
                    ],
                    options_hi: [
                        "<p><math display=\"inline\"><mfrac><mrow><mn>26</mn></mrow><mrow><mn>9</mn><mi>&#160;</mi></mrow></mfrac></math> km/h</p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>33</mn></mrow><mrow><mn>13</mn></mrow></mfrac></math> km/h</p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>25</mn></mrow><mrow><mn>8</mn><mi>&#160;</mi></mrow></mfrac></math> km/h</p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>24</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> km/h</p>"
                    ],
                    solution_en: "<p>62.(c)<br>Let the speed of boat be <math display=\"inline\"><mi>x</mi></math> and speed of stream be y <br>Upstream speed (U)= x&nbsp;- y<br>Downstream speed (D) = x&nbsp;+ y<br>According to question <br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>24</mn><mi>U</mi></mfrac></math> +&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>36</mn><mi>D</mi></mfrac></mstyle></math> = 10 ------------- (i)<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>36</mn><mi>U</mi></mfrac></math> +&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>24</mn><mi>D</mi></mfrac></mstyle></math> = 12 ------------- (ii)<br>Solving eqn(i) and (ii) we get ;<br><math display=\"inline\"><mfrac><mrow><mn>108</mn></mrow><mrow><mi>D</mi></mrow></mfrac></math> -&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>48</mn><mi>D</mi></mfrac></mstyle></math> = 6<br><math display=\"inline\"><mfrac><mrow><mn>60</mn></mrow><mrow><mi>D</mi></mrow></mfrac></math> = 6 <br>D = 10<br>Now,putting D = 10 in eqn (i), we get ; <br><math display=\"inline\"><mfrac><mrow><mn>24</mn></mrow><mrow><mi>U</mi></mrow></mfrac></math> +&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>18</mn><mn>5</mn></mfrac></mstyle></math> = 10<br><math display=\"inline\"><mfrac><mrow><mn>24</mn></mrow><mrow><mi>U</mi></mrow></mfrac></math> = 10 -&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>18</mn><mn>5</mn></mfrac></mstyle></math><br><math display=\"inline\"><mfrac><mrow><mn>24</mn></mrow><mrow><mi>U</mi></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>32</mn><mn>5</mn></mfrac></mstyle></math><br>U = <math display=\"inline\"><mfrac><mrow><mn>24</mn><mo>&#215;</mo><mn>5</mn></mrow><mrow><mn>32</mn></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>15</mn><mn>4</mn></mfrac></mstyle></math><br>So, speed of current = <math display=\"inline\"><mfrac><mrow><mi>D</mi><mo>-</mo><mi>U</mi></mrow><mrow><mn>2</mn></mrow></mfrac></math> = (10 - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>15</mn><mn>4</mn></mfrac></mstyle></math>) &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>1</mn><mn>2</mn></mfrac></mstyle></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>25</mn><mn>8</mn></mfrac></mstyle></math>km/hr</p>",
                    solution_hi: "<p>62.(c)<br>माना नाव की गति <math display=\"inline\"><mi>x</mi></math> है और धारा की गति y है <br>धारा के प्रतिकूल गति (U)= x&nbsp;- y<br>धारा के अनुकूल गति (D) = x&nbsp;+ y<br>प्रश्न के अनुसार , <br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>24</mn><mi>U</mi></mfrac></math>+<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>36</mn><mi>D</mi></mfrac></mstyle></math>= 10 ------------- (i)<br><math display=\"inline\"><mfrac><mrow><mn>36</mn></mrow><mrow><mi>U</mi></mrow></mfrac></math>+<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>24</mn><mi>D</mi></mfrac></mstyle></math> = 12 ------------- (ii)<br>समीकरण (i) और (ii) को हल करने पर हमें प्राप्त होता है;<br><math display=\"inline\"><mfrac><mrow><mn>108</mn></mrow><mrow><mi>D</mi></mrow></mfrac></math> -&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>48</mn><mi>D</mi></mfrac></mstyle></math> = 6<br><math display=\"inline\"><mfrac><mrow><mn>60</mn></mrow><mrow><mi>D</mi></mrow></mfrac></math> = 6 <br>D = 10<br>अब, समीकरण (i) में D = 10 रखने पर, हमें प्राप्त होता है; <br><math display=\"inline\"><mfrac><mrow><mn>24</mn></mrow><mrow><mi>U</mi></mrow></mfrac></math> +&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>18</mn><mn>5</mn></mfrac></mstyle></math> = 10<br><math display=\"inline\"><mfrac><mrow><mn>24</mn></mrow><mrow><mi>U</mi></mrow></mfrac></math> = 10 -&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>18</mn><mn>5</mn></mfrac></mstyle></math><br><math display=\"inline\"><mfrac><mrow><mn>24</mn></mrow><mrow><mi>U</mi></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>32</mn><mn>5</mn></mfrac></mstyle></math><br>U = <math display=\"inline\"><mfrac><mrow><mn>24</mn><mo>&#215;</mo><mn>5</mn></mrow><mrow><mn>32</mn></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>15</mn><mn>4</mn></mfrac></mstyle></math><br>तो, धारा की गति = <math display=\"inline\"><mfrac><mrow><mi>D</mi><mo>-</mo><mi>U</mi></mrow><mrow><mn>2</mn></mrow></mfrac></math> = (10 - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>15</mn><mn>4</mn></mfrac></mstyle></math>) &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>1</mn><mn>2</mn></mfrac></mstyle></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>25</mn><mn>8</mn></mfrac></mstyle></math> किमी/घंटा</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "63",
                    section: "17",
                    question_en: "<p>63. A number r when divided by 8 leaves remainder 3. What will be the remainder when (r&sup2; + 6r + 7) is divided by 8?</p>",
                    question_hi: "<p>63. एक संख्या r को 8 से विभाजित करने पर 3 शेष बचता है। (r&sup2; + 6r + 7) को 8 से विभाजित करने पर शेषफल क्या होगा?</p>",
                    options_en: [
                        "<p>2</p>",
                        "<p>3</p>",
                        "<p>1</p>",
                        "<p>4</p>"
                    ],
                    options_hi: [
                        "<p>2</p>",
                        "<p>3</p>",
                        "<p>1</p>",
                        "<p>4</p>"
                    ],
                    solution_en: "<p>63.(a) Number (r ) <math display=\"inline\"><mo>&#247;</mo></math> 8 &rarr; 3 (rem)<br>Number (r&sup2; + 6r + 7) &divide; 8 &rarr;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mrow><msup><mn>3</mn><mn>2</mn></msup><mo>+</mo><mn>6</mn><mo>(</mo><mn>3</mn><mo>)</mo><mo>+</mo><mn>7</mn></mrow><mn>8</mn></mfrac></mstyle></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>34</mn><mn>8</mn></mfrac></mstyle></math> &rarr; 2 (rem )</p>",
                    solution_hi: "<p>63.(a) संख्या (r ) <math display=\"inline\"><mo>&#247;</mo></math> 8 &rarr; 3 ( शेषफल )<br>संख्या (r&sup2; + 6r + 7) &divide; 8 &rarr;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mrow><msup><mn>3</mn><mn>2</mn></msup><mo>+</mo><mn>6</mn><mo>(</mo><mn>3</mn><mo>)</mo><mo>+</mo><mn>7</mn></mrow><mn>8</mn></mfrac></mstyle></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>34</mn><mn>8</mn></mfrac></mstyle></math>&rarr; 2 (शेषफल )</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "64",
                    section: "17",
                    question_en: "<p>64. In an election between two candidates, one got 60% of the total valid votes and 15% of the votes were invalid. If the total number of votes were 8400, the number of valid votes that the other candidate got were:</p>",
                    question_hi: "<p>64. दो उम्मीदवारों के बीच एक चुनाव में, एक को कुल वैध मतों का 60% प्राप्त हुआ और 15% मत अवैध थे। यदि मतों की कुल संख्या 8400 थी, तो दूसरे उम्मीदवार को मिले वैध मतों की संख्या कितनी थी ?</p>",
                    options_en: [
                        "<p>3213</p>",
                        "<p>2856</p>",
                        "<p>3117</p>",
                        "<p>2998</p>"
                    ],
                    options_hi: [
                        "<p>3213</p>",
                        "<p>2856</p>",
                        "<p>3117</p>",
                        "<p>2998</p>"
                    ],
                    solution_en: "<p>64.(b)<br>Let total valid votes be x<br>According to the question,<br>x = 8400 &times; <math display=\"inline\"><mfrac><mrow><mn>85</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 7140<br>Now, valid votes got by other candidate = 7140 &times; <math display=\"inline\"><mfrac><mrow><mn>40</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 2856</p>",
                    solution_hi: "<p>64.(b)<br>माना कि कुल वैध वोट x हैं<br>प्रश्न के अनुसार,<br>x = 8400 &times; <math display=\"inline\"><mfrac><mrow><mn>85</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 7140<br>अब, दूसरे उम्मीदवार को मिले वैध वोट = 7140 &times; <math display=\"inline\"><mfrac><mrow><mn>40</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 2856</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "65",
                    section: "17",
                    question_en: "<p>65. A shopkeeper cheats 25% in weight while buying rice and cheats 25% while selling it. If he sells the rice at 12<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math>% profit, then find his net profit percentage.</p>",
                    question_hi: "<p>65. एक दुकानदार चावल खरीदते समय भार में 25% की बेईमानी करता है और बेचते समय 25% की बेईमानी करता है। यदि वह चावल को 12<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math>% लाभ पर बेचता है, तो उसका निवल लाभ प्रतिशत ज्ञात कीजिए।</p>",
                    options_en: [
                        "<p>92.5%</p>",
                        "<p>82.5%</p>",
                        "<p>87.5%</p>",
                        "<p>62.5%</p>"
                    ],
                    options_hi: [
                        "<p>92.5%</p>",
                        "<p>82.5%</p>",
                        "<p>87.5%</p>",
                        "<p>62.5%</p>"
                    ],
                    solution_en: "<p>65.(c) According to question,<br>Ratio <math display=\"inline\"><mo>&#8594;</mo></math> C.P&nbsp; : S.P<br>Case-1&nbsp; &nbsp; &nbsp; &nbsp;3&nbsp; &nbsp;:&nbsp; 4<br>Case-2&nbsp; &nbsp; &nbsp; &nbsp;4&nbsp; &nbsp;:&nbsp; 5<br>Case-3&nbsp; &nbsp; &nbsp; &nbsp;8&nbsp; &nbsp;:&nbsp; 9<br>&mdash;---------------------------<br>Final ratio <math display=\"inline\"><mo>&#8594;</mo></math> 8 : 15<br>Required profit % = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>15</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>8</mn></mrow><mn>8</mn></mfrac></math> &times; 100 =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>700</mn><mn>8</mn></mfrac></mstyle></math> = 87.5 %</p>",
                    solution_hi: "<p>65.(c) प्रश्न के अनुसार,<br>अनुपात <math display=\"inline\"><mo>&#8594;</mo></math> C.P : S.P<br>स्तिथि-1&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;3&nbsp; :&nbsp; 4<br>स्तिथि-2&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;4&nbsp; :&nbsp; 5<br>स्तिथि-3&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;8&nbsp; :&nbsp; 9<br>&mdash;---------------------------<br>अंतिम अनुपात <math display=\"inline\"><mo>&#8594;</mo></math> 8 : 15<br>आवाश्क अनुपात % = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>15</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>8</mn></mrow><mn>8</mn></mfrac></math> &times; 100 =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>700</mn><mn>8</mn></mfrac></mstyle></math> = 87.5 %</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "66",
                    section: "17",
                    question_en: "<p>66. Two cubes, each of edge 5 cm, are joined face to face. Find the surface area of the cuboid thus formed.</p>",
                    question_hi: "<p>66. प्रत्येक 5 cm किनारे (edge) वाले दो घनों को उनके फलकों (face) की तरफ से एक-दूसरे के साथ जोड़ा जाता है। इस प्रकार बने घनाभ का पृष्ठीय क्षेत्रफल ज्ञात कीजिए।</p>",
                    options_en: [
                        "<p>250 cm&sup2;</p>",
                        "<p>300 cm&sup2;</p>",
                        "<p>125 cm&sup2;</p>",
                        "<p>200 cm&sup2;</p>"
                    ],
                    options_hi: [
                        "<p>250 cm&sup2;</p>",
                        "<p>300 cm&sup2;</p>",
                        "<p>125 cm&sup2;</p>",
                        "<p>200 cm&sup2;</p>"
                    ],
                    solution_en: "<p>66.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742185649149.png\" alt=\"rId65\" width=\"163\" height=\"95\"><br>Length of cuboid (l) = (5 + 5)cm = 10 cm<br>Breadth of cuboid (b) = 5 cm<br>Height of cuboid (h) = 5 cm<br>Surface area of cuboid = 2(lb + bh + hl)<br>= 2 &times; (10 &times; 5 + 5 &times; 5 + 5 &times; 10) = 2(50 + 25 + 50) = 250 cm&sup2;</p>",
                    solution_hi: "<p>66.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742185649149.png\" alt=\"rId65\" width=\"163\" height=\"95\"><br>घनाभ की लंबाई (l) = (5 + 5)cm = 10 सेमी<br>घनाभ की चौड़ाई (b) = 5 सेमी<br>घनाभ की ऊँचाई (h) = 5 सेमी<br>घनाभ का पृष्ठीय क्षेत्रफल = 2(lb + bh + hl)<br>= 2 &times; (10 &times; 5 + 5 &times; 5 + 5 &times; 10) = 2(50 + 25 + 50) = 250 सेमी&sup2;</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "67",
                    section: "17",
                    question_en: "<p>67. Ramu invested some money at 8% simple interest per annum, and it was grown up to ₹820 in 8 years. If the same sum was invested at 20% simple interest per annum for a period of 10 years, find the amount.</p>",
                    question_hi: "<p>67. रामू ने 8% वार्षिक साधारण ब्याज पर कुछ धनराशि का निवेश किया, और यह 8 वर्षों में बढ़कर ₹820 हो गई। यदि समान राशि का 10 वर्ष की अवधि के लिए 20% वार्षिक साधारण ब्याज पर निवेश किया जाता है, तो वह धनराशि ज्ञात कीजिए ?</p>",
                    options_en: [
                        "<p>₹1,200</p>",
                        "<p>₹1,320</p>",
                        "<p>₹1,000</p>",
                        "<p>₹1,500</p>"
                    ],
                    options_hi: [
                        "<p>₹1,200</p>",
                        "<p>₹1,320</p>",
                        "<p>₹1,000</p>",
                        "<p>₹1,500</p>"
                    ],
                    solution_en: "<p>67.(d)<br>Let the principal be 100%<br>SI for 8 yrs = 8 &times; 8 = 64%<br>164% = ₹820<br>100% = <math display=\"inline\"><mfrac><mrow><mn>820</mn></mrow><mrow><mn>164</mn></mrow></mfrac></math>&times;100 = ₹500<br>Required amount = 500 + 500 &times;10 &times; 20 % = 500 +1000 = ₹1500</p>",
                    solution_hi: "<p>67.(d)<br>माना कि मूलधन 100% है।<br>8 साल के लिए साधारण ब्याज = 8 &times; 8 = 64%<br>164% = ₹820<br>100% =<math display=\"inline\"><mfrac><mrow><mn>820</mn></mrow><mrow><mn>164</mn></mrow></mfrac></math>&times;100 = ₹500<br>आवश्यक राशि = 500 + 500 &times; 10 &times; 20% = 500 + 1000 = ₹1500</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "68",
                    section: "17",
                    question_en: "<p>68. A packet has to consist of three types of jellies - orange, mango and strawberry. The price per unit of orange, mango and strawberry jelly is ₹2.00, ₹3.00 and ₹3.50, respectively. In a packet, there are 80 orange jellies, 50 mango jellies and 120 strawberry jellies. What is the average cost (in ₹) of a jelly in a mixed packet ?</p>",
                    question_hi: "<p>68. एक पैकेट में तीन तरह की जेली - संतरा, आम और स्ट्रॉबेरी है। संतरे, आम और स्ट्रॉबेरी जेली की प्रति इकाई कीमत क्रमशः ₹2.00, ₹3.00 और ₹3.50 है। एक पैकेट में 80 संतरा जेली, 50 आम जेली और 120 स्ट्रॉबेरी जेली हैं। मिश्रित पैकेट में जेली की औसत कीमत (₹ में) क्या है ?</p>",
                    options_en: [
                        "<p>2.80</p>",
                        "<p>2.96</p>",
                        "<p>2.92</p>",
                        "<p>3.02</p>"
                    ],
                    options_hi: [
                        "<p>2.80</p>",
                        "<p>2.96</p>",
                        "<p>2.92</p>",
                        "<p>3.02</p>"
                    ],
                    solution_en: "<p>68.(c)<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; Orange&nbsp; &nbsp; &nbsp;Mango&nbsp; &nbsp; &nbsp;Strawberry<br>Quantity&nbsp; &nbsp; &nbsp; 80&nbsp; &nbsp; :&nbsp; &nbsp; 50&nbsp; &nbsp; &nbsp; &nbsp;:&nbsp; &nbsp;120 = 8 : 5 : 12<br>Price/unit&nbsp; &nbsp; &nbsp; 2&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 3&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;3.50<br>Average cost = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>8</mn><mo>&#215;</mo><mn>2</mn><mo>+</mo><mn>5</mn><mo>&#215;</mo><mn>3</mn><mo>+</mo><mn>12</mn><mo>&#215;</mo><mn>3</mn><mo>.</mo><mn>50</mn></mrow><mrow><mn>8</mn><mo>+</mo><mn>5</mn><mo>+</mo><mn>12</mn></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mrow><mn>16</mn><mo>+</mo><mn>15</mn><mo>+</mo><mn>42</mn></mrow><mn>25</mn></mfrac></mstyle><mo>=</mo><mstyle displaystyle=\"false\"><mfrac><mn>73</mn><mn>25</mn></mfrac></mstyle></math> = ₹2.92</p>",
                    solution_hi: "<p>68.(c)<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;संतरा&nbsp; &nbsp; आम&nbsp; &nbsp; &nbsp;स्ट्रॉबेरी <br>&nbsp; &nbsp; &nbsp; मात्रा&nbsp; &nbsp; &nbsp; &nbsp;80&nbsp; &nbsp; :&nbsp; &nbsp;50&nbsp; &nbsp; :&nbsp; 120 = 8 : 5 : 12<br>कीमत/किग्रा&nbsp; &nbsp;2&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 3&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;3.50&nbsp;<br>औसत कीमत = <math display=\"inline\"><mfrac><mrow><mn>8</mn><mo>&#215;</mo><mn>2</mn><mo>+</mo><mn>5</mn><mo>&#215;</mo><mn>3</mn><mo>+</mo><mn>12</mn><mo>&#215;</mo><mn>3</mn><mo>.</mo><mn>50</mn></mrow><mrow><mn>8</mn><mo>+</mo><mn>5</mn><mo>+</mo><mn>12</mn></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>16</mn><mo>+</mo><mn>15</mn><mo>+</mo><mn>42</mn></mrow><mn>25</mn></mfrac><mo>=</mo><mstyle displaystyle=\"false\"><mfrac><mn>73</mn><mn>25</mn></mfrac></mstyle></math> = ₹2.92</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "69",
                    section: "17",
                    question_en: "<p>69. The value of <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><mfrac><mrow><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#952;</mi><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><msup><mrow><mn>2</mn><mi>s</mi><mi>i</mi><mi>n</mi></mrow><mn>3</mn></msup><mi>&#952;</mi></mrow><mrow><mn>2</mn><msup><mrow><mi>c</mi><mi>o</mi><mi>s</mi></mrow><mn>3</mn></msup><mi>&#952;</mi><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#952;</mi></mrow></mfrac><mo>)</mo></mrow><mn>3</mn></msup><mfrac><mn>1</mn><mrow><mi>t</mi><mi>a</mi><mi>n</mi><mi>&#952;</mi></mrow></mfrac></math> - sec&sup2;&theta; is:</p>",
                    question_hi: "<p>69. <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><mfrac><mrow><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#952;</mi><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><msup><mrow><mn>2</mn><mi>s</mi><mi>i</mi><mi>n</mi></mrow><mn>3</mn></msup><mi>&#952;</mi></mrow><mrow><mn>2</mn><msup><mrow><mi>c</mi><mi>o</mi><mi>s</mi></mrow><mn>3</mn></msup><mi>&#952;</mi><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#952;</mi></mrow></mfrac><mo>)</mo></mrow><mn>3</mn></msup><mfrac><mn>1</mn><mrow><mi>t</mi><mi>a</mi><mi>n</mi><mi>&#952;</mi></mrow></mfrac></math> - sec&sup2;&theta; का मान ज्ञात कीजिए।</p>",
                    options_en: [
                        "<p>2</p>",
                        "<p>1</p>",
                        "<p>0</p>",
                        "<p>-1</p>"
                    ],
                    options_hi: [
                        "<p>2</p>",
                        "<p>1</p>",
                        "<p>0</p>",
                        "<p>-1</p>"
                    ],
                    solution_en: "<p>69.(d) <br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><msup><mrow><mo>(</mo><mfrac><mrow><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#952;</mi><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><msup><mrow><mn>2</mn><mi>s</mi><mi>i</mi><mi>n</mi></mrow><mn>3</mn></msup><mi>&#952;</mi></mrow><mrow><mn>2</mn><msup><mrow><mi>c</mi><mi>o</mi><mi>s</mi></mrow><mn>3</mn></msup><mi>&#952;</mi><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#952;</mi></mrow></mfrac><mo>)</mo></mrow><mn>3</mn></msup><mfrac><mn>1</mn><mrow><mi>t</mi><mi>a</mi><mi>n</mi><mi>&#952;</mi></mrow></mfrac></mstyle></math>&nbsp;- sec&sup2;&theta;&nbsp;<br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><mfrac><mrow><mi>sin&#952;</mi><mi mathvariant=\"normal\">&#160;</mi><mo>(</mo><mn>1</mn><mi mathvariant=\"normal\">&#160;</mi><mo>-</mo><mi mathvariant=\"normal\">&#160;</mi><msup><mrow><mn>2</mn><mi>sin</mi></mrow><mn>2</mn></msup><mi mathvariant=\"normal\">&#952;</mi><mo>)</mo></mrow><mrow><mi>cos&#952;</mi><mi mathvariant=\"normal\">&#160;</mi><mo>(</mo><mn>2</mn><msup><mi>cos</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&#952;</mi><mo>-</mo><mn>1</mn><mo>)</mo></mrow></mfrac><mo>)</mo></mrow><mn>3</mn></msup><mfrac><mn>1</mn><mi>tan&#952;</mi></mfrac></math> - sec&sup2;&theta; [ &there4; cos2&theta; = (2cos&sup2;&theta; - 1)=(1 - 2sin&sup2;&theta;) ]<br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><mfrac><mrow><mi>sin&#952;</mi><mi mathvariant=\"normal\">&#160;</mi><mo>.</mo><mi mathvariant=\"normal\">&#160;</mi><mi>cos</mi><mn>2</mn><mi mathvariant=\"normal\">&#952;</mi></mrow><mrow><mi>cos&#952;</mi><mi mathvariant=\"normal\">&#160;</mi><mo>.</mo><mi>cos</mi><mn>2</mn><mi>&#952;</mi></mrow></mfrac><mo>)</mo></mrow><mn>3</mn></msup></math>&times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>1</mn><mi>tan&#952;</mi></mfrac></mstyle></math> - sec&sup2;&theta; <br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><mfrac><mrow><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#952;</mi><mi>&#160;</mi></mrow><mrow><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#952;</mi><mi>&#160;</mi></mrow></mfrac><mo>)</mo></mrow><mn>3</mn></msup></math>&times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>1</mn><mi>tan&#952;</mi></mfrac></mstyle></math> - sec&sup2;&theta;<br>= tan&sup3;&theta; &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>1</mn><mi>tan&#952;</mi></mfrac></mstyle></math> - sec&sup2;&theta;<br>= tan&sup2;&theta; - sec&sup2;&theta;<br>= -1</p>",
                    solution_hi: "<p>69.(d) <br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><mfrac><mrow><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#952;</mi><mo>-</mo><msup><mrow><mn>2</mn><mi>s</mi><mi>i</mi><mi>n</mi></mrow><mn>3</mn></msup><mi>&#952;</mi></mrow><mrow><mn>2</mn><msup><mrow><mi>c</mi><mi>o</mi><mi>s</mi></mrow><mn>3</mn></msup><mi>&#952;</mi><mo>-</mo><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#952;</mi></mrow></mfrac><mo>)</mo></mrow><mn>3</mn></msup><mfrac><mn>1</mn><mrow><mi>t</mi><mi>a</mi><mi>n</mi><mi>&#952;</mi></mrow></mfrac></math> - sec&sup2;&theta;&nbsp;<br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><mfrac><mrow><mi>sin&#952;</mi><mi mathvariant=\"normal\">&#160;</mi><mo>(</mo><mn>1</mn><mi mathvariant=\"normal\">&#160;</mi><mo>-</mo><mi mathvariant=\"normal\">&#160;</mi><msup><mrow><mn>2</mn><mi>sin</mi></mrow><mn>2</mn></msup><mi mathvariant=\"normal\">&#952;</mi><mo>)</mo></mrow><mrow><mi>cos&#952;</mi><mi mathvariant=\"normal\">&#160;</mi><mo>(</mo><mn>2</mn><msup><mi>cos</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&#952;</mi><mo>-</mo><mn>1</mn><mo>)</mo></mrow></mfrac><mo>)</mo></mrow><mn>3</mn></msup><mfrac><mn>1</mn><mi>tan&#952;</mi></mfrac></math> - sec&sup2;&theta; [&there4; cos2&theta; = (2cos&sup2;&theta; - 1)= (1 - 2sin&sup2;&theta;) ]<br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><mfrac><mrow><mi>sin&#952;</mi><mi mathvariant=\"normal\">&#160;</mi><mo>.</mo><mi mathvariant=\"normal\">&#160;</mi><mi>cos</mi><mn>2</mn><mi mathvariant=\"normal\">&#952;</mi></mrow><mrow><mi>cos&#952;</mi><mi mathvariant=\"normal\">&#160;</mi><mo>.</mo><mi>cos</mi><mn>2</mn><mi>&#952;</mi></mrow></mfrac><mo>)</mo></mrow><mn>3</mn></msup></math>&times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>1</mn><mi>tan&#952;</mi></mfrac></mstyle></math> - sec&sup2;&theta;&nbsp;<br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><mfrac><mrow><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#952;</mi><mi>&#160;</mi></mrow><mrow><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#952;</mi><mi>&#160;</mi></mrow></mfrac><mo>)</mo></mrow><mn>3</mn></msup></math>&times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>1</mn><mi>tan&#952;</mi></mfrac></mstyle></math> - sec&sup2;&theta;<br>= tan&sup3;&theta; &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>1</mn><mi>tan&#952;</mi></mfrac></mstyle></math> - sec&sup2;&theta;<br>= tan&sup2;&theta; - sec&sup2;&theta;<br>= -1</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "70",
                    section: "17",
                    question_en: "<p>70. The table shows the percentage distribution of the population (only male and female) according to Gender and Literacy.<br><img src=\"data:image/png;base64,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\" width=\"280\" height=\"179\"><br>The total population in state C, if the illiterate female count in state C is 4,40,000, is:</p>",
                    question_hi: "<p>70. दी गई तालिका लिंग और साक्षरता के अनुसार जनसंख्या (केवल पुरुष और महिला) का प्रतिशत बंटन दर्शाती है।<br><img src=\"data:image/png;base64,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\" width=\"330\" height=\"180\"><br>यदि राज्य C में निरक्षर महिलाओं की संख्या 4,40,000 है, तो राज्य C की कुल जनसंख्या ज्ञात करें।</p>",
                    options_en: [
                        "<p>3600000</p>",
                        "<p>5000000</p>",
                        "<p>4600000</p>",
                        "<p>6000000</p>"
                    ],
                    options_hi: [
                        "<p>3600000</p>",
                        "<p>5000000</p>",
                        "<p>4600000</p>",
                        "<p>6000000</p>"
                    ],
                    solution_en: "<p>70.(b)<br>Illiterate (male + female) in state C = <math display=\"inline\"><mfrac><mrow><mn>440000</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> &times; 5 = 1100000<br>Illiterate (male + female) in state C = (100 - 78)% = 22%<br>22% = 1100000<br>Hence, total population of state C = 100% = <math display=\"inline\"><mfrac><mrow><mn>1100000</mn></mrow><mrow><mn>22</mn></mrow></mfrac></math> &times; 100 = 5000000</p>",
                    solution_hi: "<p>70.(b)<br>राज्य C में निरक्षर (पुरुष + महिला) = <math display=\"inline\"><mfrac><mrow><mn>440000</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> &times; 5 = 1100000<br>राज्य C में निरक्षर (पुरुष + महिला) = (100 - 78)% = 22%<br>22% = 1100000<br>अतः, राज्य C की कुल जनसंख्या = 100% = <math display=\"inline\"><mfrac><mrow><mn>1100000</mn></mrow><mrow><mn>22</mn></mrow></mfrac></math> &times; 100 = 5000000</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "71",
                    section: "17",
                    question_en: "<p>71. A cylindrical iron rod whose height is twelve times its radius is melted and cast into spherical balls, the radius of each of which is one third of the radius of the cylinder. Find the number of spherical balls.</p>",
                    question_hi: "<p>71. लोहे की एक बेलनाकार छड़ जिसकी ऊंचाई इसकी त्रिज्या से बारह गुना है, को पिघलाकर गोलाकार गेंदों में ढाला जाता है, जिनमें से प्रत्येक की त्रिज्या , बेलन की त्रिज्या की एक तिहाई है। गोलाकार गेंदों की संख्या ज्ञात कीजिए।</p>",
                    options_en: [
                        "<p>243</p>",
                        "<p>198</p>",
                        "<p>224</p>",
                        "<p>212</p>"
                    ],
                    options_hi: [
                        "<p>243</p>",
                        "<p>198</p>",
                        "<p>224</p>",
                        "<p>212</p>"
                    ],
                    solution_en: "<p>71.(a) <br>Let radius of cylinder = r<br>Then according to question,<br>Height of cylinder = 12r<br>And , radius of each spherical ball = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>r</mi><mn>3</mn></mfrac></math><br>Volume of cylindrical iron rod = <math display=\"inline\"><mi>x</mi></math> &times; volume of each spherical balls<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>&#960;r</mi><mn>2</mn></msup><mi mathvariant=\"normal\">h</mi></math> = x &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>4</mn><mn>3</mn></mfrac></mstyle></math>&pi;r&sup3;<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>&#960;r</mi><mn>2</mn></msup><mo>&#215;</mo><mi mathvariant=\"normal\">&#160;</mi><mn>12</mn><mi mathvariant=\"normal\">r</mi></math> = x &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>4</mn><mn>3</mn></mfrac></mstyle></math>&pi;(<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mi>r</mi><mn>3</mn></mfrac></mstyle></math>)&sup3;<br>12<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi mathvariant=\"normal\">r</mi><mn>3</mn></msup></math> = x &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>4</mn><mn>3</mn></mfrac></mstyle></math> &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><msup><mi>r</mi><mn>3</mn></msup><mn>27</mn></mfrac></mstyle></math><br><math display=\"inline\"><mi>x</mi></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mrow><mn>12</mn><mo>&#215;</mo><mn>3</mn><mo>&#215;</mo><mn>27</mn></mrow><mn>4</mn></mfrac></mstyle></math> = 243<br>Hence, number of spherical ball (<math display=\"inline\"><mi>x</mi></math>) = 243</p>",
                    solution_hi: "<p>71.(a) <br>माना बेलन की त्रिज्या = r<br>फिर प्रश्न के अनुसार,<br>बेलन की ऊँचाई = 12r<br>और, प्रत्येक गोलाकार गेंद की त्रिज्या = <math display=\"inline\"><mfrac><mrow><mi>r</mi></mrow><mrow><mn>3</mn></mrow></mfrac></math><br>बेलनाकार लोहे की छड़ का आयतन = <math display=\"inline\"><mi>x</mi></math> &times; प्रत्येक गोलाकार गेंदों का आयतन<br><math display=\"inline\"><mi>&#960;</mi><msup><mrow><mi>r</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>h</mi></math> = x &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>4</mn><mn>3</mn></mfrac></mstyle></math>&pi;r&sup3;<br><math display=\"inline\"><mi>&#960;</mi><msup><mrow><mi>r</mi></mrow><mrow><mn>2</mn></mrow></msup><mo>&#215;</mo><mi>&#160;</mi><mn>12</mn><mi>r</mi></math> = x &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>4</mn><mn>3</mn></mfrac></mstyle></math>&pi;(<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mi>r</mi><mn>3</mn></mfrac></mstyle></math>)&sup3;<br>12<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi mathvariant=\"normal\">r</mi><mn>3</mn></msup></math> = x &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>4</mn><mn>3</mn></mfrac></mstyle></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><msup><mi>r</mi><mn>3</mn></msup><mn>27</mn></mfrac></mstyle></math><br>x = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mrow><mn>12</mn><mo>&#215;</mo><mn>3</mn><mo>&#215;</mo><mn>27</mn></mrow><mn>4</mn></mfrac></mstyle></math>= 243<br>अतः, गोलाकार गेंद की संख्या (<math display=\"inline\"><mi>x</mi></math>) = 243</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "72",
                    section: "17",
                    question_en: "<p>72. Three friends receive message on their mobile phones at an interval of 20 minutes, 30 minutes and 45 minutes respectively. They start receiving message together at a particular time. After how much time will they receive message again on their mobile phone altogether?</p>",
                    question_hi: "<p>72. तीन मित्र क्रमशः 20 मिनट, 30 मिनट और 45 मिनट के अंतराल पर अपने मोबाइल फोन पर संदेश प्राप्त करते हैं। उन्हें संदेश एक निर्धारित समय पर एक साथ प्राप्त होना शुरू हुआ। वे कितने समय के बाद पुनः एक साथ संदेश प्राप्त करेंगे?</p>",
                    options_en: [
                        "<p>1.5 hours</p>",
                        "<p>2 hours</p>",
                        "<p>2.5 hours</p>",
                        "<p>3 hours</p>"
                    ],
                    options_hi: [
                        "<p>1.5 घंटे</p>",
                        "<p>2 घंटे</p>",
                        "<p>2.5 घंटे</p>",
                        "<p>3 घंटे</p>"
                    ],
                    solution_en: "<p>72.(d)<br>LCM of (20, 30, 45) = 180 minutes<br>On converting it into hours = <math display=\"inline\"><mfrac><mrow><mn>180</mn></mrow><mrow><mn>60</mn></mrow></mfrac></math> = 3 hours</p>",
                    solution_hi: "<p>72.(d)<br>(20, 30, 45) का लघुत्तम समापवर्त्य = 180 मिनट<br>इसे घंटों में बदलने पर <math display=\"inline\"><mfrac><mrow><mn>180</mn></mrow><mrow><mn>60</mn></mrow></mfrac></math> = 3 घंटे</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "73",
                    section: "17",
                    question_en: "<p>73. A basket contains 10 apples and 20 oranges out of which 3 apples and 5 oranges are defective. If we choose two fruits at random, what is the probability that either both are oranges or both are non defective ?</p>",
                    question_hi: "<p>73. एक टोकरी में 10 सेब और 20 संतरे हैं जिनमें से 3 सेब और 5 संतरे खराब हैं। यदि हम दो फलों को यादृच्छिक रूप से चुनते हैं, तो इसकी क्या प्रायिकता है कि या तो दोनों संतरे हैं या दोनों दोषपूर्ण नहीं हैं ?</p>",
                    options_en: [
                        "<p><math display=\"inline\"><mfrac><mrow><mn>316</mn></mrow><mrow><mn>435</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>361</mn></mrow><mrow><mn>435</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>391</mn></mrow><mrow><mn>435</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>319</mn></mrow><mrow><mn>435</mn></mrow></mfrac></math></p>"
                    ],
                    options_hi: [
                        "<p><math display=\"inline\"><mfrac><mrow><mn>316</mn></mrow><mrow><mn>435</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>361</mn></mrow><mrow><mn>435</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>391</mn></mrow><mrow><mn>435</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>319</mn></mrow><mrow><mn>435</mn></mrow></mfrac></math></p>"
                    ],
                    solution_en: "<p>73.(a) Probability of both are oranges <math display=\"inline\"><mfenced open=\"{\" close=\"}\" separators=\"|\"><mrow><mi>n</mi><mo>(</mo><mi>A</mi><mo>)</mo></mrow></mfenced></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><msub><mn>20</mn><msub><mi>c</mi><mn>2</mn></msub></msub><msub><mn>30</mn><msub><mi>c</mi><mn>2</mn></msub></msub></mfrac></mstyle></math><br>Probability of both are non-defective <math display=\"inline\"><mfenced open=\"{\" close=\"}\" separators=\"|\"><mrow><mi>n</mi><mo>(</mo><mi>B</mi><mo>)</mo></mrow></mfenced></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><msub><mn>22</mn><msub><mi>c</mi><mn>2</mn></msub></msub><msub><mn>30</mn><msub><mi>c</mi><mn>2</mn></msub></msub></mfrac></mstyle></math><br>Probability of both are non-defective oranges <math display=\"inline\"><mfenced open=\"{\" close=\"}\" separators=\"|\"><mrow><mi>n</mi><mo>(</mo><mi>A</mi><mo>&#8898;</mo><mi>B</mi><mo>)</mo></mrow></mfenced></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><msub><mn>15</mn><msub><mi>c</mi><mn>2</mn></msub></msub><msub><mn>30</mn><msub><mi>c</mi><mn>2</mn></msub></msub></mfrac></mstyle></math><br>Now, n(A<math display=\"inline\"><mo>&#8746;</mo></math>B) =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><msub><mn>20</mn><msub><mi>c</mi><mn>2</mn></msub></msub><msub><mn>30</mn><msub><mi>c</mi><mn>2</mn></msub></msub></mfrac></mstyle><mo>+</mo><mstyle displaystyle=\"false\"><mfrac><msub><mn>22</mn><msub><mi>c</mi><mn>2</mn></msub></msub><msub><mn>30</mn><msub><mi>c</mi><mn>2</mn></msub></msub></mfrac></mstyle><mo>-</mo><mstyle displaystyle=\"false\"><mfrac><msub><mn>15</mn><msub><mi>c</mi><mn>2</mn></msub></msub><msub><mn>30</mn><msub><mi>c</mi><mn>2</mn></msub></msub></mfrac></mstyle></math> = <math display=\"inline\"><mfrac><mrow><mn>316</mn></mrow><mrow><mn>435</mn></mrow></mfrac></math></p>",
                    solution_hi: "<p>73.(a)<br>दोनों संतरे होने की प्रायिकता <math display=\"inline\"><mfenced open=\"{\" close=\"}\" separators=\"|\"><mrow><mi>n</mi><mo>(</mo><mi>A</mi><mo>)</mo></mrow></mfenced></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><msub><mn>20</mn><msub><mi>c</mi><mn>2</mn></msub></msub><msub><mn>30</mn><msub><mi>c</mi><mn>2</mn></msub></msub></mfrac></mstyle></math><br>दोनों के दोषपूर्ण ना होने की प्रायिकता <math display=\"inline\"><mfenced open=\"{\" close=\"}\" separators=\"|\"><mrow><mi>n</mi><mo>(</mo><mi>B</mi><mo>)</mo></mrow></mfenced></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><msub><mn>22</mn><msub><mi>c</mi><mn>2</mn></msub></msub><msub><mn>30</mn><msub><mi>c</mi><mn>2</mn></msub></msub></mfrac></mstyle></math><br>दोनों संतरो के दोषपूर्ण ना होने की प्रायिकता <math display=\"inline\"><mfenced open=\"{\" close=\"}\" separators=\"|\"><mrow><mi>n</mi><mo>(</mo><mi>A</mi><mo>&#8898;</mo><mi>B</mi><mo>)</mo></mrow></mfenced></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><msub><mn>15</mn><msub><mi>c</mi><mn>2</mn></msub></msub><msub><mn>30</mn><msub><mi>c</mi><mn>2</mn></msub></msub></mfrac></mstyle></math><br>अब , n(A<math display=\"inline\"><mo>&#8746;</mo></math>B) =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><msub><mn>20</mn><msub><mi>c</mi><mn>2</mn></msub></msub><msub><mn>30</mn><msub><mi>c</mi><mn>2</mn></msub></msub></mfrac><mo>+</mo><mfrac><msub><mn>22</mn><msub><mi>c</mi><mn>2</mn></msub></msub><msub><mn>30</mn><msub><mi>c</mi><mn>2</mn></msub></msub></mfrac><mo>-</mo><mfrac><msub><mn>15</mn><msub><mi>c</mi><mn>2</mn></msub></msub><msub><mn>30</mn><msub><mi>c</mi><mn>2</mn></msub></msub></mfrac></mstyle></math> = <math display=\"inline\"><mfrac><mrow><mn>316</mn></mrow><mrow><mn>435</mn></mrow></mfrac></math></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "74",
                    section: "17",
                    question_en: "<p>74. If the 9-digit number 5x79856y6 is divisible by 36, then what is the negative value of<math display=\"inline\"><mi>&#160;</mi><msqrt><mo>(</mo><mn>2</mn><mi>x</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>y</mi><mo>)</mo></msqrt></math> for the largest possible value of y, given x and y are natural numbers ?</p>",
                    question_hi: "<p>74. यदि 9 अंकों की संख्या 5x79856y6, 36 से विभाज्य है, तो y के सबसे बड़े संभावित मान के लिए<math display=\"inline\"><msqrt><mo>(</mo><mn>2</mn><mi>x</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>y</mi><mo>)</mo></msqrt></math>का ऋणात्मक मान क्या है, दिया गया है कि x और y प्राकृतिक संख्याएँ हैं?</p>",
                    options_en: [
                        "<p>-7</p>",
                        "<p>-2</p>",
                        "<p>-4</p>",
                        "<p>-5</p>"
                    ],
                    options_hi: [
                        "<p>-7</p>",
                        "<p>-2</p>",
                        "<p>-4</p>",
                        "<p>-5</p>"
                    ],
                    solution_en: "<p>74.(d)<br>Number = 5<math display=\"inline\"><mi>x</mi></math>79856y6<br>For divisibility of 36 a number must be divisible by 9 and 4.<br><strong>For divisibility of 4 :-</strong> last 2 digit must be divisible by 4.<br>Hence , possible value of <math display=\"inline\"><mi>y</mi></math> = 1, 3,5,7, 9<br>We need largest possible value of <math display=\"inline\"><mi>y</mi></math> = 9 <br><strong>For divisibility of 9 :-</strong> sum of number must be divisible by 9.<br>Hence, <br>sum of number = 5 + <math display=\"inline\"><mi>x</mi></math> + 7 + 9 + 8 + 5 + 6 + y + 6 <br>= 46 + <math display=\"inline\"><mi>x</mi></math> + y<br>On putting largest value of <math display=\"inline\"><mi>y</mi></math> = 9<br>= 46 + <math display=\"inline\"><mi>x</mi></math> + 9 = 55 + x<br>Now, <br>Number next to 55 that is divisible by 9 is 63<br>Hence,<br>Required value of <math display=\"inline\"><mi>x</mi></math> = 63 - 55 = 8<br>Now,<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#160;</mi><msqrt><mo>(</mo><mn>2</mn><mi>x</mi><mo>+</mo><mi>y</mi><mo>)</mo></msqrt></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mo>(</mo><mn>2</mn><mo>&#215;</mo><mn>8</mn><mo>+</mo><mn>9</mn><mo>)</mo></msqrt></math> = &plusmn;5<br>Hence negative value of <math display=\"inline\"><msqrt><mo>(</mo><mn>2</mn><mi>x</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>y</mi><mo>)</mo></msqrt></math> = -5</p>",
                    solution_hi: "<p>74.(d)<br>संख्या = 5<math display=\"inline\"><mi>x</mi></math>79856y6<br>36 की विभाज्यता के लिए संख्या 9 और 4 से विभाज्य होनी चाहिए।<br><strong>4 की विभाज्यता के लिए </strong>:- अंतिम 2 अंक 4 से विभाज्य होने चाहिए।<br>अत: , <math display=\"inline\"><mi>y</mi></math> का संभावित मान = 1, 3,5,7, 9<br>हमें <math display=\"inline\"><mi>y</mi></math> = 9 का सबसे बड़ा संभावित मान चाहिए <br><strong>9 की विभाज्यता के लिए :</strong>- संख्या का योग 9 से विभाज्य होना चाहिए।<br>अब , <br>संख्या का योग = 5 + <math display=\"inline\"><mi>x</mi></math> + 7 + 9 + 8 + 5 + 6 + y + 6 <br>= 46 + <math display=\"inline\"><mi>x</mi></math> + y<br><math display=\"inline\"><mi>y</mi></math> = 9 (सबसे बड़ा मान) रखने पर<br>संख्या का योग = 46 + <math display=\"inline\"><mi>x</mi></math> + 9 = 55 + x<br>अब, <br>55 के आगे की संख्या जो 9 से विभाज्य है वह 63 है<br>इस तरह,<br><math display=\"inline\"><mi>x</mi></math> का अभीष्ट मान = 63 - 55 = 8<br>अब,<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#160;</mi><msqrt><mo>(</mo><mn>2</mn><mi>x</mi><mo>+</mo><mi>y</mi><mo>)</mo></msqrt></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mo>(</mo><mn>2</mn><mo>&#215;</mo><mn>8</mn><mo>+</mo><mn>9</mn><mo>)</mo></msqrt></math> = &plusmn;5<br>अतः <math display=\"inline\"><msqrt><mo>(</mo><mn>2</mn><mi>x</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>y</mi><mo>)</mo></msqrt></math> का ऋणात्मक मान = -5</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "75",
                    section: "7",
                    question_en: "<p>75. A policeman was asked to chase a thief. Before the policeman started the chase, he realised that the thief was 200 metres ahead of him and was running at a speed of 16 km/h. The policeman started the chase at a speed of 20 km/h. How far will the thief run before he is overtaken by the policeman ?</p>",
                    question_hi: "<p>75. एक पुलिसकर्मी को एक चोर का पीछा करने के लिए कहा गया। इससे पहले कि पुलिसकर्मी पीछा करना शुरू करता, उसे ज्ञात होता है कि चोर उससे 200 मीटर आगे था और 16 km/h की चाल से भाग रहा था। पुलिसकर्मी ने 20 km/h की चाल से पीछा करना शुरू किया। पुलिसकर्मी द्वारा पकड़े जाने से पहले चोर कितनी दूर तक भागेगा?</p>",
                    options_en: [
                        "<p>600 m</p>",
                        "<p>700 m</p>",
                        "<p>800 m</p>",
                        "<p>1000 m</p>"
                    ],
                    options_hi: [
                        "<p>600 m</p>",
                        "<p>700 m</p>",
                        "<p>800 m</p>",
                        "<p>1000 m</p>"
                    ],
                    solution_en: "<p>75.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742185649263.png\" alt=\"rId66\" width=\"291\" height=\"123\"><br>Distance between police and thief = 200 meter<br>Relative speed = (20 - 16) &times; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>18</mn></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>10</mn><mn>9</mn></mfrac></mstyle></math> m/sec<br>Time taken by police to catch the thief = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>200</mn><mi>&#160;</mi></mrow><mrow><mfrac><mn>10</mn><mn>9</mn></mfrac><mi>&#160;</mi></mrow></mfrac></math> = 180 second<br>Time taken by police = time taken by thief<br>Speed of thief = 16 km/h = 16 &times; <math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>18</mn></mrow></mfrac></math> m/sec<br>Distance travel by thief in 180 second = 180 &times; 16 &times; <math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>18</mn></mrow></mfrac></math> = 800 meter</p>",
                    solution_hi: "<p>75.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742185649372.png\" alt=\"rId67\" width=\"298\" height=\"123\"><br>पुलिस और चोर के बीच की दूरी = 200 मीटर<br>सापेक्ष गति = (20 - 16) &times; <math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>18</mn></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>10</mn><mn>9</mn></mfrac></mstyle></math> मीटर/सेकंड<br>पुलिस द्वारा चोर को पकड़ने में लगा समय = <math display=\"inline\"><mfrac><mrow><mn>200</mn><mi>&#160;</mi></mrow><mrow><mfrac><mrow><mn>10</mn></mrow><mrow><mn>9</mn></mrow></mfrac><mi>&#160;</mi></mrow></mfrac></math> = 180 सेकंड<br>पुलिस द्वारा लिया गया समय = चोर द्वारा लिया गया समय<br>चोर की गति = 16 किमी/घंटा = 16 &times; <math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>18</mn></mrow></mfrac></math> मीटर/सेकंड<br>चोर द्वारा 180 सेकंड में तय की गयी दूरी = 180 &times; 16 &times; <math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>18</mn></mrow></mfrac></math> = 800 मीटर</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "76",
                    section: "7",
                    question_en: "<p>76. Hold the rope firmly</p>",
                    question_hi: "<p>76. Hold the rope firmly</p>",
                    options_en: [
                        "<p>Let the rope hold you firmly.</p>",
                        "<p>Let the rope firmly hold you.</p>",
                        "<p>Let the rope be hold firmly</p>",
                        "<p>Let the rope be held firmly.</p>"
                    ],
                    options_hi: [
                        "<p>Let the rope hold you firmly.</p>",
                        "<p>Let the rope firmly hold you.</p>",
                        "<p>Let the rope be hold firmly</p>",
                        "<p>Let the rope be held firmly.</p>"
                    ],
                    solution_en: "<p>76.(d) Let the rope be held firmly. (Correct)<br>(a) Let the rope hold you firmly. (Meaning has changed)<br>(b) Let the rope firmly hold you. (Meaning has changed)<br>(c) Let the rope be <span style=\"text-decoration: underline;\">hold</span> firmly. (Incorrect Verb)</p>",
                    solution_hi: "<p>76.(d) Let the rope be held firmly. (Correct)<br>(a) Let the rope hold you firmly. (Sentence का meaning original sentence से different है)<br>(b) Let the rope firmly hold you. (Sentence का meaning original sentence से different है)<br>(c) Let the rope be <span style=\"text-decoration: underline;\">hold</span> firmly. (गलत verb (hold) का प्रयोग किया गया है। (held) का प्रयोग होगा। ) </p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "77",
                    section: "7",
                    question_en: "<p>77. Identify the segment in the sentence, which contains error.<br>The recently hike / in the price of petroleum products / will hit / household budgets severely.</p>",
                    question_hi: "<p>77. Identify the segment in the sentence, which contains error.<br>The recently hike / in the price of petroleum products / will hit / household budgets severely.</p>",
                    options_en: [
                        "<p>will hit</p>",
                        "<p>The recently hike</p>",
                        "<p>household budgets severely</p>",
                        "<p>in the price of petroleum products</p>"
                    ],
                    options_hi: [
                        "<p>will hit</p>",
                        "<p>The recently hike</p>",
                        "<p>household budgets severely</p>",
                        "<p>in the price of petroleum products</p>"
                    ],
                    solution_en: "<p>77.(b) We need an adjective after the definite article &lsquo;The&rsquo; in the given sentence and not an adverb. Hence, &lsquo;recently&rsquo; will be replaced with &lsquo;recent&rsquo;, and &lsquo;The recent hike&rsquo; becomes the most appropriate structure.</p>",
                    solution_hi: "<p>77.(b) हमें दिए गए वाक्य (sentence ) में adverb की बजाय definite article &lsquo;The&rsquo; का प्रयोग होगा । इसलिए, &lsquo;recently&rsquo; को &lsquo;recent&rsquo;से बदल दिया जाएगा, और &lsquo;The recent hike&rsquo; सबसे उपयुक्त होगा।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "78",
                    section: "7",
                    question_en: "<p>78.Find a word that is the synonym of:<br>LICENTIOUS</p>",
                    question_hi: "<p>78.Find a word that is the synonym of:<br>LICENTIOUS</p>",
                    options_en: [
                        "<p>immoral</p>",
                        "<p>intellectual</p>",
                        "<p>moral</p>",
                        "<p>without licence</p>"
                    ],
                    options_hi: [
                        "<p>immoral</p>",
                        "<p>intellectual</p>",
                        "<p>moral</p>",
                        "<p>without licence</p>"
                    ],
                    solution_en: "<p>78.(a) <strong>immoral</strong><br><strong>Licentious </strong>- Promiscuous and unprincipled in sexual matters.</p>",
                    solution_hi: "<p>78.(a)<strong> immoral</strong><br><strong>Licentious </strong>- यौन मामलों में विचित्र और सिद्धांतहीन।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "79",
                    section: "7",
                    question_en: "<p>79. Select the most appropriate option that can substitute the underlined segment in the given sentence. If there is no need to substitute it, select \'No substitution\'.<br><span style=\"text-decoration: underline;\">If I was very rich,</span> I would spend all my time and money for the poor.</p>",
                    question_hi: "<p>79. Select the most appropriate option that can substitute the underlined segment in the given sentence. If there is no need to substitute it, select \'No substitution\'.<br><span style=\"text-decoration: underline;\">If I was very rich,</span> I would spend all my time and money for the poor.</p>",
                    options_en: [
                        "<p>If I am very rich</p>",
                        "<p>No substitution</p>",
                        "<p>If I have much riches</p>",
                        "<p>If I were very rich</p>"
                    ],
                    options_hi: [
                        "<p>If I am very rich</p>",
                        "<p>No substitution</p>",
                        "<p>If I have much riches</p>",
                        "<p>If I were very rich</p>"
                    ],
                    solution_en: "<p>79.(d) If I were very rich<br>The phrase &lsquo;If I were&rsquo; is used to refer to an imaginary situation. &lsquo;If I were &hellip; would + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi mathvariant=\"normal\">v</mi><mn>1</mn></msub></math>&rsquo; is the correct grammatical structure. Hence, &lsquo;If I were very rich&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>79.(d) If I were very rich<br>\'If I were\' का प्रयोग काल्पनिक (imaginary) स्थिति को व्यक्त करने के लिए किया जाता है। \'If I were &hellip; would + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi mathvariant=\"normal\">V</mi><mn>1</mn></msub></math>&rsquo; सही grammatical structure है। इसलिए, \"If I were very rich\" सबसे उपयुक्त उत्तर है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "80",
                    section: "7",
                    question_en: "<p>80. Identify the word that is correctly spelt.</p>",
                    question_hi: "<p>80. Identify the word that is correctly spelt.</p>",
                    options_en: [
                        "<p>idiosynancracy</p>",
                        "<p>satellites</p>",
                        "<p>slugishly</p>",
                        "<p>inteligible</p>"
                    ],
                    options_hi: [
                        "<p>idiosynancracy</p>",
                        "<p>satellites</p>",
                        "<p>slugishly</p>",
                        "<p>inteligible</p>"
                    ],
                    solution_en: "<p>80.(b) &lsquo;Satellites&rsquo;<br>Other words- idiosyncrasy, sluggishly, intelligible</p>",
                    solution_hi: "<p>80.(b) &lsquo;Satellites&rsquo; <br>Other words- idiosyncrasy, sluggishly, intelligible</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "81",
                    section: "7",
                    question_en: "<p>81. Select the option that can be used as a one-word substitute for the given group of words.<br>Consonance of sounds.</p>",
                    question_hi: "<p>81. Select the option that can be used as a one-word substitute for the given group of words.<br>Consonance of sounds.</p>",
                    options_en: [
                        "<p>Orchestration</p>",
                        "<p>Woodwinds</p>",
                        "<p>Symphony</p>",
                        "<p>Troupe</p>"
                    ],
                    options_hi: [
                        "<p>Orchestration</p>",
                        "<p>Woodwinds</p>",
                        "<p>Symphony</p>",
                        "<p>Troupe</p>"
                    ],
                    solution_en: "<p>81.(c)<strong> Symphony</strong>- consonance of sounds.<br><strong>Orchestration</strong>- an arrangement of a piece of music to be played.<br><strong>Woodwinds- </strong>a type of musical instrument that you play by blowing into its mouthpiece.<br><strong>Troupe-</strong> a group of actors, dancers, singers, etc. who work and travel together.</p>",
                    solution_hi: "<p>81.(c) <strong>Symphony (</strong>स्वर की समता) - consonance of sounds.<br><strong>Orchestration</strong> (वाद्य-स्थान) - an arrangement of a piece of music to be played.<br><strong>Woodwinds</strong> (काष्ठ वाद्य) - a type of musical instrument that you play by blowing into its mouthpiece.<br><strong>Troupe</strong> (मंडली) - a group of actors, dancers, singers, etc. who work and travel together.</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "82",
                    section: "7",
                    question_en: "<p>82. The disaster management team rescued the flood victims.</p>",
                    question_hi: "<p>82. The disaster management team rescued the flood victims.</p>",
                    options_en: [
                        "<p>The flood victims have been rescued by the disaster management team.</p>",
                        "<p>The flood victims are being rescued by the disaster management team.</p>",
                        "<p>The flood victims are rescued by the disaster management team.</p>",
                        "<p>The flood victims were rescued by the disaster management team.</p>"
                    ],
                    options_hi: [
                        "<p>The flood victims have been rescued by the disaster management team.</p>",
                        "<p>The flood victims are being rescued by the disaster management team.</p>",
                        "<p>The flood victims are rescued by the disaster management team.</p>",
                        "<p>The flood victims were rescued by the disaster management team.</p>"
                    ],
                    solution_en: "<p>82.(d) The flood victims were rescued by the disaster&nbsp;management team. (Correct)<br>(a) The flood victims <span style=\"text-decoration: underline;\">have been</span> rescued by the disaster management team. (Incorrect Tense)<br>(b) The flood victims <span style=\"text-decoration: underline;\">are being</span> rescued by the disaster management team. (Incorrect Tense)<br>(c) The flood victims <span style=\"text-decoration: underline;\">are</span> rescued by the disaster management team. (Incorrect Tense)</p>",
                    solution_hi: "<p>82.(d) The flood victims were rescued by the disaster management team. (Correct)<br>(a) The flood victims <span style=\"text-decoration: underline;\">have been</span> rescued by the disaster management team. (गलत tense (Present perfect का प्रयोग किया गया है | were rescued (simple past) का प्रयोग होगा | )&nbsp;<br>(b) The flood victims <span style=\"text-decoration: underline;\">are being</span> rescued by the disaster management team. (गलत tense (Present continuous) का प्रयोग किया गया है | were rescued (simple past) का प्रयोग होगा | )&nbsp;<br>(c) The flood victims <span style=\"text-decoration: underline;\">are</span> rescued by the disaster management Team. (गलत tense (Present simple) का प्रयोग किया गया है | were rescued (simple past) का प्रयोग होगा | )&nbsp;</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "83",
                    section: "7",
                    question_en: "<p>83. Given below are four jumbled sentences. Pick the option that gives their correct order.<br>A. Venkat had no money for a ticket but luckily the ticket collector didn&rsquo;t&nbsp;come to the unreserved compartment.&nbsp;<br>B. Venkat had walked for most of the first day to Kochi and then got on&nbsp;the train to Coimbatore. <br>C. A group of men next to him had played cards and shouted all night.<br>D. He had tried to sleep on the floor near the door.</p>",
                    question_hi: "<p>83. Given below are four jumbled sentences. Pick the option that gives their correct order.<br>A. Venkat had no money for a ticket but luckily the ticket collector didn&rsquo;t&nbsp;come to the unreserved compartment. <br>B. Venkat had walked for most of the first day to Kochi and then got on&nbsp;the train to Coimbatore. <br>C. A group of men next to him had played cards and shouted all night.<br>D. He had tried to sleep on the floor near the door.</p>",
                    options_en: [
                        "<p>DCBA</p>",
                        "<p>CBAD</p>",
                        "<p>ABDC</p>",
                        "<p>BADC</p>"
                    ],
                    options_hi: [
                        "<p>DCBA</p>",
                        "<p>CBAD</p>",
                        "<p>ABDC</p>",
                        "<p>BADC</p>"
                    ],
                    solution_en: "<p>83.(d) BADC<br>Sentence B will be the starting line as it contains the main idea of the parajumble i.e. Venkat who got on a train. And, Sentence A states that he didn&rsquo;t have the ticket. So, A will follow B. Further, Sentence D states that then he tried to sleep and Sentence C states that a group of men shouted all night. So, C will follow D. Going through the options, option (d) has the correct sequence.</p>",
                    solution_hi: "<p>83.(d) BADC<br>Sentence B शुरुआती line होगी क्योंकि इसमें parajumble का मुख्य विचार वेंकट शामिल है जो ट्रेन में चढ़ गया। Sentence A कहता है कि उसके पास टिकट नहीं था। तो, B के बाद A आएगा । आगे, Sentence D कहता है कि फिर उसने सोने की कोशिश की और Sentence C कहता है कि पुरुषों का एक समूह पूरी रात चिल्लाता रहा। इसलिए, D के बाद C आएगा। विकल्पों के माध्यम से, विकल्प (d) में सही क्रम है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "84",
                    section: "7",
                    question_en: "<p>84. Select the most appropriate option to fill in the blank.<br>Masked men ________ a security van on the motorway.</p>",
                    question_hi: "<p>84. Select the most appropriate option to fill in the blank.<br>Masked men ________ a security van on the motorway.</p>",
                    options_en: [
                        "<p>held out</p>",
                        "<p>held up</p>",
                        "<p>held forth</p>",
                        "<p>held on</p>"
                    ],
                    options_hi: [
                        "<p>held out</p>",
                        "<p>held up</p>",
                        "<p>held forth</p>",
                        "<p>held on</p>"
                    ],
                    solution_en: "<p>84.(b) held up<br>All these are phrasal verbs.<br><strong>Held out</strong> means extended out, <br><strong>Held up </strong>- attacked<br><strong>Held forth</strong> means proclaimed. <br><strong>Held on </strong>means to have kept in one&rsquo;s grasp.</p>",
                    solution_hi: "<p>84.(b) held up<br>ये सभी phrasal verbs हैं।<br><strong>Held out </strong>- बढ़ाया हुआ,<br><strong>Held up</strong> - हमला करना <br><strong>Held forth</strong> -घोषित किया <br><strong>Held on</strong>- अपनी मुट्ठी में रखना</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "85",
                    section: "7",
                    question_en: "<p>85. Select the INCORRECTLY spelt word&hellip;</p>",
                    question_hi: "<p>85. Select the INCORRECTLY spelt word&hellip;</p>",
                    options_en: [
                        "<p>Reciept</p>",
                        "<p>Hindrance</p>",
                        "<p>Necessitate</p>",
                        "<p>Irritate</p>"
                    ],
                    options_hi: [
                        "<p>Reciept</p>",
                        "<p>Hindrance</p>",
                        "<p>Necessitate</p>",
                        "<p>Irritate</p>"
                    ],
                    solution_en: "<p>85.(a) Reciept<br>&lsquo;Receipt&rsquo; is the correct spelling.</p>",
                    solution_hi: "<p>85.(a) Reciept<br>&lsquo;Receipt&rsquo; सही spelling है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "86",
                    section: "7",
                    question_en: "<p>86. The following sentence has been split into four segments. Identify the segment that contains a grammatical/spelling error. <br>The sons of the old farmer / fell off after their father / breathed his last / in the village.</p>",
                    question_hi: "<p>86. The following sentence has been split into four segments. Identify the segment that contains a grammatical/spelling error. <br>The sons of the old farmer / fell off after their father / breathed his last / in the village.</p>",
                    options_en: [
                        "<p>breathed his last</p>",
                        "<p>fell off after their father</p>",
                        "<p>The sons of the old farmer</p>",
                        "<p>in the village.</p>"
                    ],
                    options_hi: [
                        "<p>breathed his last</p>",
                        "<p>fell off after their father</p>",
                        "<p>The sons of the old farmer</p>",
                        "<p>in the village.</p>"
                    ],
                    solution_en: "<p>86.(b) fell off after their father<br>&lsquo;Fell off&rsquo; must be replaced with &lsquo;fell apart&rsquo;. The phrasal verb &lsquo;fall apart&rsquo; means to become separated. The given passage talks about separation of the sons. Hence, &lsquo;fell apart after their father&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>86.(b) fell off after their father<br>&lsquo;Fell off&rsquo; के स्थान पर &lsquo;fell apart&rsquo; का प्रयोग किया जाना चाहिए। phrasal verb &lsquo;fall apart&rsquo; का अर्थ अलग होना है। दिया गया passage बेटों (sons) के अलग होने के बारे में बात करता है। इसलिए, &lsquo;fell apart after their father&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "87",
                    section: "7",
                    question_en: "<p>87. Select the most appropriate meaning of the underlined segment in the following sentence. <br>Don&rsquo;t you think that the <span style=\"text-decoration: underline;\">cat&rsquo;s in the cradle</span>? You never make time for me.</p>",
                    question_hi: "<p>87. Select the most appropriate meaning of the underlined segment in the following sentence. <br>Don&rsquo;t you think that the <span style=\"text-decoration: underline;\">cat&rsquo;s in the cradle</span>? You never make time for me.</p>",
                    options_en: [
                        "<p>Busy in your work</p>",
                        "<p>Complicated relationship</p>",
                        "<p>Busy in minting money</p>",
                        "<p>Newly married life</p>"
                    ],
                    options_hi: [
                        "<p>Busy in your work</p>",
                        "<p>Complicated relationship</p>",
                        "<p>Busy in minting money</p>",
                        "<p>Newly married life</p>"
                    ],
                    solution_en: "<p>87.(b) <strong>Cat&rsquo;s in the cradle </strong>- complicated relationship.</p>",
                    solution_hi: "<p>87.(b) <strong>Cat&rsquo;s in the cradle -</strong> complicated relationship./मुश्किल रिश्ता</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "88",
                    section: "7",
                    question_en: "<p>88. Choose the alternative which best expresses the meaning of the idiom/phrase.<br>Chew the cud</p>",
                    question_hi: "<p>88. Choose the alternative which best expresses the meaning of the idiom/phrase.<br>Chew the cud</p>",
                    options_en: [
                        "<p>Chew grass</p>",
                        "<p>think over</p>",
                        "<p>Chew something</p>",
                        "<p>get into trouble</p>"
                    ],
                    options_hi: [
                        "<p>Chew grass</p>",
                        "<p>think over</p>",
                        "<p>Chew something</p>",
                        "<p>get into trouble</p>"
                    ],
                    solution_en: "<p>88.(b) think over</p>",
                    solution_hi: "<p>88.(b) think over/विचार करना</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "89",
                    section: "7",
                    question_en: "<p>89. Given below are four jumbled sentences. Pick the option that gives their correct order.<br>(A) That was given to him in a feeding bottle by our cook.<br>(B) At first, the tiger-cub was brought up entirely on milk.<br>(C) Later the diet was of pigeons and rabbits.<br>(D) But the milk proved too rich for him, and he was put on a diet of raw mutton and cod-liver oil.</p>",
                    question_hi: "<p>89. Given below are four jumbled sentences. Pick the option that gives their correct order.<br>(A) That was given to him in a feeding bottle by our cook.<br>(B) At first, the tiger-cub was brought up entirely on milk.<br>(C) Later the diet was of pigeons and rabbits.<br>(D) But the milk proved too rich for him, and he was put on a diet of raw mutton and cod-liver oil.</p>",
                    options_en: [
                        "<p>BADC</p>",
                        "<p>BDAC</p>",
                        "<p>CADB</p>",
                        "<p>ACDB</p>"
                    ],
                    options_hi: [
                        "<p>BADC</p>",
                        "<p>BDAC</p>",
                        "<p>CADB</p>",
                        "<p>ACDB</p>"
                    ],
                    solution_en: "<p>89.(a) <strong>BADC</strong><br>Sentence B will be the first sentence as it mentions the subject of the parajumble &ldquo;the tiger cub.&rdquo; The given parajumble is about the food habits of a tiger cub. Sentence B will be the starting line as it explains that first the tiger cub was brought up entirely on milk. A will follow sentence B as it tells us how the milk was given to the tiger cub. Sentence D states the reason for changing his diet. Sentence A will be followed by sentence D. Sentence C will come at the end as it states his last diet i.e. later the diet was of pigeons and rabbits. So, option (a) is the most suitable answer.</p>",
                    solution_hi: "<p>89.(a) <strong>BADC</strong><br>Sentence B first sentence होगा क्योंकि यह parajumble के विषय &ldquo;the tiger cub.&rdquo; का उल्लेख करता है। दिया गया parajumble एक बाघ शावक के भोजन की आदतों के बारे में है। Sentence B starting line होगी क्योंकि यह बताती है कि पहले बाघ शावक को पूरी तरह से दूध पर पाला गया था। Sentence B के बाद sentence A आएगा क्योंकि यह हमें बताता है कि बाघ शावक को दूध कैसे दिया गया। Sentence D आहार को बदलने का कारण बताता है। Sentence A के बाद sentence D होगा। Sentence C अंत में आएगा क्योंकि यह उसके अंतिम आहार को बताता है अर्थात बाद में आहार कबूतरों और खरगोशों का था। इसलिए, option (a) सबसे उपयुक्त उत्तर है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "90",
                    section: "7",
                    question_en: "<p>90. Select the word which means the same as the group of words given.<br>A group of three novels or plays, each complete in itself</p>",
                    question_hi: "<p>90. Select the word which means the same as the group of words given.<br>A group of three novels or plays, each complete in itself</p>",
                    options_en: [
                        "<p>Triplet</p>",
                        "<p>Triumvir</p>",
                        "<p>Trivet</p>",
                        "<p>Trilogy</p>"
                    ],
                    options_hi: [
                        "<p>Triplet</p>",
                        "<p>Triumvir</p>",
                        "<p>Trivet</p>",
                        "<p>Trilogy</p>"
                    ],
                    solution_en: "<p>90.(d) <strong>Trilogy - </strong>a group of three novels or plays, each complete in itself <br><strong>Triplet</strong> - one of three children or animals that are born to one mother at the same time<br><strong>Triumvir </strong>- each of three public officers jointly responsible for overseeing any of the administrative departments.<br><strong>Trivet -</strong> an iron tripod or bracket for a cooking pot or kettle to stand on.</p>",
                    solution_hi: "<p>90.(d) <strong>Trilogy </strong>(त्रयी) - a group of three novels or plays, each complete in itself <br><strong>Triplet</strong> (त्रिक) - one of three children or animals that are born to one mother at the same time<br><strong>Triumvir </strong>(त्रिशासक) - each of three public officers jointly responsible for overseeing any of the administrative departments.<br><strong>Trivet</strong> (लोहे की तिपाई) - an iron tripod or bracket for a cooking pot or kettle to stand on.</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "91",
                    section: "7",
                    question_en: "<p>91. Select the most appropriate synonym of the underlined word.<br>The <span style=\"text-decoration: underline;\">hypothesis</span> that the police believed in led to a lot of chaos.</p>",
                    question_hi: "<p>91. Select the most appropriate synonym of the underlined word.<br>The <span style=\"text-decoration: underline;\">hypothesis</span> that the police believed in led to a lot of chaos.</p>",
                    options_en: [
                        "<p>speculation</p>",
                        "<p>trepidation</p>",
                        "<p>breakthrough</p>",
                        "<p>encounter</p>"
                    ],
                    options_hi: [
                        "<p>speculation</p>",
                        "<p>trepidation</p>",
                        "<p>breakthrough</p>",
                        "<p>encounter</p>"
                    ],
                    solution_en: "<p>91.(a) <strong>Speculation- </strong>a theory or guess based on incomplete information.<br><strong>Hypothesis-</strong> a proposed explanation or assumption made on the basis of limited evidence.<br><strong>Trepidation-</strong> a feeling of fear or anxiety.<br><strong>Breakthrough-</strong> a significant development or discovery.<br><strong>Encounter-</strong> an unexpected meeting or confrontation.</p>",
                    solution_hi: "<p>91.(a)<strong> Speculation</strong> (परिकल्पना) - a theory or guess based on incomplete information.<br><strong>Hypothesis</strong> (परिकल्पना) - a proposed explanation or assumption made on the basis of limited evidence.<br><strong>Trepidation</strong> (घबराहट) - a feeling of fear or anxiety.<br><strong>Breakthrough</strong> (सफलता) - a significant development or discovery.<br><strong>Encounter</strong> (सामना करना) - an unexpected meeting or confrontation.</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "92",
                    section: "7",
                    question_en: "<p>92. Identify the best way to improve the underlined part of the given sentence. If there is no improvement required, select &lsquo;no improvement&rsquo;.<br>I will buy the house <span style=\"text-decoration: underline;\"><strong>provided</strong></span> it is quite sound.</p>",
                    question_hi: "<p>92. Identify the best way to improve the underlined part of the given sentence. If there is no improvement required, select &lsquo;no improvement&rsquo;.<br>I will buy the house <strong><span style=\"text-decoration: underline;\">provided</span></strong> it is quite sound.</p>",
                    options_en: [
                        "<p>unless</p>",
                        "<p>whether</p>",
                        "<p>until</p>",
                        "<p>No Improvement</p>"
                    ],
                    options_hi: [
                        "<p>unless</p>",
                        "<p>whether</p>",
                        "<p>until</p>",
                        "<p>No Improvement</p>"
                    ],
                    solution_en: "<p>92.(d) No Improvement.</p>",
                    solution_hi: "<p>92.(d) No Improvement./ कोई सुधार नहीं ।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "93",
                    section: "7",
                    question_en: "<p>93. Choose the word opposite in meaning to the given word as your answer.<br>Descent</p>",
                    question_hi: "<p>93. Choose the word opposite in meaning to the given word as your answer.<br>Descent</p>",
                    options_en: [
                        "<p>elevation</p>",
                        "<p>increase</p>",
                        "<p>level</p>",
                        "<p>ascent</p>"
                    ],
                    options_hi: [
                        "<p>elevation</p>",
                        "<p>increase</p>",
                        "<p>level</p>",
                        "<p>ascent</p>"
                    ],
                    solution_en: "<p>93.(d)<strong> ascent</strong><br><strong>Descent </strong>- an act of moving downwards, dropping, or falling.<br><strong>Ascent</strong>- an act of moving upwards.</p>",
                    solution_hi: "<p>93.(d) <strong>ascent</strong><br><strong>Descent </strong>- नीचे की ओर बढ़ने, या गिरने की क्रिया।<br><strong>Ascent </strong>- ऊपर की ओर बढ़ने की क्रिया।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "94",
                    section: "7",
                    question_en: "<p>94. Select the most appropriate option to fill in the blank. <br>For more than a decade now, Indian villages have been _______ to television programmes.</p>",
                    question_hi: "<p>94. Select the most appropriate option to fill in the blank. <br>For more than a decade now, Indian villages have been _______ to television programmes.</p>",
                    options_en: [
                        "<p>accustomed</p>",
                        "<p>exposed</p>",
                        "<p>open</p>",
                        "<p>attracted</p>"
                    ],
                    options_hi: [
                        "<p>accustomed</p>",
                        "<p>exposed</p>",
                        "<p>open</p>",
                        "<p>attracted</p>"
                    ],
                    solution_en: "<p>94.(b) exposed.<br><strong>Exposed</strong> - make (something) visible by uncovering it.<br><strong>Accustomed </strong>- customary; usual.</p>",
                    solution_hi: "<p>94.(b) exposed<br><strong>Exposed</strong> - उजागर करना <br><strong>Accustomed</strong> - सामान्य,परिचित</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "95",
                    section: "7",
                    question_en: "<p>95. Identify the most appropriate ANTONYM of the underlined word in the given sentence.<br>The door was ajar and a faint <span style=\"text-decoration: underline;\">wisp</span> of smoke floated through it.</p>",
                    question_hi: "<p>95. Identify the most appropriate ANTONYM of the underlined word in the given sentence.<br>The door was ajar and a faint <span style=\"text-decoration: underline;\">wisp</span> of smoke floated through it.</p>",
                    options_en: [
                        "<p>Trickle</p>",
                        "<p>Itchy</p>",
                        "<p>Wind</p>",
                        "<p>Lot</p>"
                    ],
                    options_hi: [
                        "<p>Trickle</p>",
                        "<p>Itchy</p>",
                        "<p>Wind</p>",
                        "<p>Lot</p>"
                    ],
                    solution_en: "<p>95.(d) <strong>Lot-</strong> a large amount or quantity.<br><strong>Wisp</strong>- a small, thin, or delicate amount.<br><strong>Trickle-</strong> a small flow or stream.<br><strong>Itchy- </strong>having or causing an irritating sensation on the skin.<br><strong>Wind</strong>- a natural fast movement of air.</p>",
                    solution_hi: "<p>95.(d) <strong>Lot</strong> (खेप) - a large amount or quantity.<br><strong>Wisp</strong> (फीका) - a small, thin, or delicate amount.<br><strong>Trickle</strong> (टपकना) - a small flow or stream.<br><strong>Itchy</strong> (खुजलीदार) - having or causing an irritating sensation on the skin.<br><strong>Wind</strong> (हवा) - a natural fast movement of air.</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "96",
                    section: "7",
                    question_en: "<p>96.<strong>Cloze Test:-</strong><br>It was a crime of passion of such disfiguring violence that Victorian London ____96____ scandalized. In November 1865, Felix Deacon, a young lithographer who had_____97____ for a married woman, Sarah Ann Peacock, was attacked _____98____ the street. His assailant was not her husband but another man, a dentist who was equally____99____ with Mrs. Peacock and therefore saw Deacon as a ____100____ .<br>Select the most appropriate option to fill in blank No.96.</p>",
                    question_hi: "<p>96.<strong> Cloze Test:-</strong><br>It was a crime of passion of such disfiguring violence that Victorian London ____96____ scandalized. In November 1865, Felix Deacon, a young lithographer who had_____97____ for a married woman, Sarah Ann Peacock, was attacked _____98____ the street. His assailant was not her husband but another man, a dentist who was equally____99____ with Mrs. Peacock and therefore saw Deacon as a ____100____ .<br>Select the most appropriate option to fill in blank No.96.</p>",
                    options_en: [
                        "<p>is left</p>",
                        "<p>left</p>",
                        "<p>has left</p>",
                        "<p>was left</p>"
                    ],
                    options_hi: [
                        "<p>is left</p>",
                        "<p>left</p>",
                        "<p>has left</p>",
                        "<p>was left</p>"
                    ],
                    solution_en: "<p>96.(d) was left<br>The given passage is in the past tense &amp; the sentence is in the passive voice(was/were + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi mathvariant=\"normal\">V</mi><mn>3</mn></msub></math>). Hence, &lsquo;was left(<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi mathvariant=\"normal\">V</mi><mn>3</mn></msub></math>)&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>96.(d) was left<br>दिया गया passage, past tense में है और वाक्य passive voice (was/were + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi mathvariant=\"normal\">V</mi><mn>3</mn></msub></math>) में है। इसलिए, &lsquo;was left(<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi mathvariant=\"normal\">V</mi><mn>3</mn></msub></math>)&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "97",
                    section: "7",
                    question_en: "<p>97.<strong> Cloze Test:-</strong><br>It was a crime of passion of such disfiguring violence that Victorian London ____96____ scandalized. In November 1865, Felix Deacon, a young lithographer who had_____97____ for a married woman, Sarah Ann Peacock, was attacked _____98____ the street. His assailant was not her husband but another man, a dentist who was equally____99____ with Mrs. Peacock and therefore saw Deacon as a ____100____ .<br>Select the most appropriate option to fill in blank No.97.</p>",
                    question_hi: "<p>97. <strong>Cloze Test:-</strong><br>It was a crime of passion of such disfiguring violence that Victorian London ____96____ scandalized. In November 1865, Felix Deacon, a young lithographer who had_____97____ for a married woman, Sarah Ann Peacock, was attacked _____98____ the street. His assailant was not her husband but another man, a dentist who was equally____99____ with Mrs. Peacock and therefore saw Deacon as a ____100____ .<br>Select the most appropriate option to fill in blank No.97.</p>",
                    options_en: [
                        "<p>tripped</p>",
                        "<p>fallen</p>",
                        "<p>cared</p>",
                        "<p>come</p>"
                    ],
                    options_hi: [
                        "<p>tripped</p>",
                        "<p>fallen</p>",
                        "<p>cared</p>",
                        "<p>come</p>"
                    ],
                    solution_en: "<p>97.(b) fallen<br>&lsquo;Fall for&rsquo; is the correct phrase which means fall in love with someone. The given passage states that Felix Deacon fall in love with someone. However, &lsquo;had + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi mathvariant=\"normal\">V</mi><mn>3</mn></msub></math>&rsquo; is the correct grammatical structure for the given sentence. Hence, &lsquo;fallen(<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi mathvariant=\"normal\">V</mi><mn>3</mn></msub></math>)&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>97.(b) fallen<br>&lsquo;Fall for&rsquo; सही phrase है जिसका अर्थ है किसी के प्यार में पड़ना। दिए गए passage में कहा गया है कि Felix Deacon को किसी से प्यार हो जाता हैं। हालाँकि, दिए गए वाक्य के लिए \'has + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi mathvariant=\"normal\">V</mi><mn>3</mn></msub></math>\' सही grammatical संरचना है। इसलिए, &lsquo;fallen(<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi mathvariant=\"normal\">V</mi><mn>3</mn></msub></math>)&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "98",
                    section: "7",
                    question_en: "<p>98. <strong>Cloze Test:-</strong><br>It was a crime of passion of such disfiguring violence that Victorian London ____96____ scandalized. In November 1865, Felix Deacon, a young lithographer who had_____97____ for a married woman, Sarah Ann Peacock, was attacked _____98____ the street. His assailant was not her husband but another man, a dentist who was equally____99____ with Mrs. Peacock and therefore saw Deacon as a ____100____ .<br>Select the most appropriate option to fill in blank No.98.</p>",
                    question_hi: "<p>98. <strong>Cloze Test:-</strong><br>It was a crime of passion of such disfiguring violence that Victorian London ____96____ scandalized. In November 1865, Felix Deacon, a young lithographer who had_____97____ for a married woman, Sarah Ann Peacock, was attacked _____98____ the street. His assailant was not her husband but another man, a dentist who was equally____99____ with Mrs. Peacock and therefore saw Deacon as a ____100____ .<br>Select the most appropriate option to fill in blank No.98.</p>",
                    options_en: [
                        "<p>into</p>",
                        "<p>in</p>",
                        "<p>through</p>",
                        "<p>at</p>"
                    ],
                    options_hi: [
                        "<p>into</p>",
                        "<p>in</p>",
                        "<p>through</p>",
                        "<p>at</p>"
                    ],
                    solution_en: "<p>98.(d) at <br>The preposition &lsquo;at&rsquo; will perfectly fit in the context of the given sentence. The given passage states that Sarah Ann Peacock, was attacked at the street. Hence, &lsquo;at&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>98.(d) at <br>Preposition &lsquo;at&rsquo; दिए गए वाक्य के संदर्भ में सही उपयोग होगा। दिए गए passage में कहा गया है कि Sarah Ann Peacock पर सड़क पर हमला किया गया था। इसलिए, &lsquo;at&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "99",
                    section: "7",
                    question_en: "<p>99. <strong>Cloze Test:-</strong><br>It was a crime of passion of such disfiguring violence that Victorian London ____96____ scandalized. In November 1865, Felix Deacon, a young lithographer who had_____97____ for a married woman, Sarah Ann Peacock, was attacked _____98____ the street. His assailant was not her husband but another man, a dentist who was equally____99____ with Mrs. Peacock and therefore saw Deacon as a ____100____ .<br>Select the most appropriate option to fill in blank No.99.</p>",
                    question_hi: "<p>99.<strong> Cloze Test:-</strong><br>It was a crime of passion of such disfiguring violence that Victorian London ____96____ scandalized. In November 1865, Felix Deacon, a young lithographer who had_____97____ for a married woman, Sarah Ann Peacock, was attacked _____98____ the street. His assailant was not her husband but another man, a dentist who was equally____99____ with Mrs. Peacock and therefore saw Deacon as a ____100____ .<br>Select the most appropriate option to fill in blank No.99.</p>",
                    options_en: [
                        "<p>hurt</p>",
                        "<p>bent</p>",
                        "<p>working</p>",
                        "<p>smitten</p>"
                    ],
                    options_hi: [
                        "<p>hurt</p>",
                        "<p>bent</p>",
                        "<p>working</p>",
                        "<p>smitten</p>"
                    ],
                    solution_en: "<p>99.(d) smitten<br>&lsquo;Smitten&rsquo; means to be strongly attracted to someone or something. The given passage talks about a dentist who was equally strongly attracted with Mrs. Peacock. Hence, &lsquo;smitten&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>99.(d) smitten<br>&lsquo;Smitten&rsquo; का अर्थ है किसी व्यक्ति या वस्तु के प्रति बहुत अधिक आकर्षित होना। दिए गए Passage में एक दंत चिकित्सक के बारे में बात की गई है जो Mrs. Peacock के साथ रूप से आकर्षित था। इसलिए, &lsquo;smitten&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "100",
                    section: "misc",
                    question_en: "<p>100. <strong>Cloze Test:-</strong><br>It was a crime of passion of such disfiguring violence that Victorian London ____96____ scandalized. In November 1865, Felix Deacon, a young lithographer who had_____97____ for a married woman, Sarah Ann Peacock, was attacked _____98____ the street. His assailant was not her husband but another man, a dentist who was equally____99____ with Mrs. Peacock and therefore saw Deacon as a ____100____ .<br>Select the most appropriate option to fill in blank No.100.</p>",
                    question_hi: "<p>100. <strong>Cloze Test:-</strong><br>It was a crime of passion of such disfiguring violence that Victorian London ____96____ scandalized. In November 1865, Felix Deacon, a young lithographer who had_____97____ for a married woman, Sarah Ann Peacock, was attacked _____98____ the street. His assailant was not her husband but another man, a dentist who was equally____99____ with Mrs. Peacock and therefore saw Deacon as a ____100____ .<br>Select the most appropriate option to fill in blank No.100.</p>",
                    options_en: [
                        "<p>mediator</p>",
                        "<p>rival</p>",
                        "<p>opponent</p>",
                        "<p>friend</p>"
                    ],
                    options_hi: [
                        "<p>mediator</p>",
                        "<p>rival</p>",
                        "<p>opponent</p>",
                        "<p>friend</p>"
                    ],
                    solution_en: "<p>100.(b) rival<br>&lsquo;Rival&rsquo; means a person, group, etc. competing with others for the same thing. The given passage talks about a dentist who was equally smitten with Mrs. Peacock and therefore saw Deacon as a rival. Hence, &lsquo;rival&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>100.(b) rival<br>&lsquo;Rival&rsquo; का अर्थ एक व्यक्ति, समूह आदि से है जो एक ही चीज़ के लिए दूसरों के साथ प्रतिस्पर्धा(competing) करता है। दिया गया passage एक दंत चिकित्सक के बारे में बात करता है जो Mrs. Peacock के साथ समान रूप से प्यार करता था और इसलिए डीकन को एक प्रतिद्वंद्वी के रूप में देखता था। इसलिए, &lsquo;rival&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'question-box d-flex align-items-center justify-content-center border rounded';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.question-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            const html = `
                <div class="card shadow-sm mb-4">
                    <div class="card-body">
                        <div class="question-text mb-4">
                            <div class="en">${question.question_en}</div>
                            <div class="hi" style="display:none">${question.question_hi}</div>
                        </div>
                        <div class="options">
                            ${['a', 'b', 'c', 'd'].map((opt, i) => `
                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="radio" name="q${index}" 
                                        id="opt-${index}-${opt}" value="${opt}" 
                                        ${answers[question.id] === opt ? 'checked' : ''}
                                        onclick="selectAnswer('${question.id}', '${opt}')">
                                    <label class="form-check-label w-100 py-2 px-3 rounded hover-bg-light" for="opt-${index}-${opt}">
                                        <div class="en">${question.options_en[i]}</div>
                                        <div class="hi" style="display:none">${question.options_hi[i]}</div>
                                    </label>
                                </div>
                            `).join('')}
                        </div>
                        ${submitted ? `
                            <div class="alert ${answers[question.id] === question.correct ? 'alert-success' : 'alert-danger'} mt-3">
                                ${answers[question.id] ? (answers[question.id] === question.correct ? 'Correct!' : 'Incorrect!') : 'Not attempted'}
                                <div class="mt-2">
                                    <strong>Solution:</strong>
                                    <div class="en">${question.solution_en}</div>
                                    <div class="hi" style="display:none">${question.solution_hi}</div>
                                </div>
                            </div>
                        ` : ''}
                    </div>
                </div>
            `;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // If in mobile view, close the menu
            if (window.innerWidth < 768) {
                toggleMenu(false);
            }
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                document.getElementById(`box-${index}`).classList.add('attempted');
            }
        }
        // Navigate to previous question
        function prevQuestion() {
            if (currentQuestion > 0) {
                showQuestion(currentQuestion - 1);
            }
        }
        // Navigate to next question
        function nextQuestion() {
            if (currentQuestion < questions.length - 1) {
                showQuestion(currentQuestion + 1);
            }
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) {
                showQuestion(section.start);
            }
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update button text
            document.getElementById('lang-btn').innerHTML = currentLang === 'en' ? 
                '<i class="fas fa-language"></i> हिंदी' : 
                '<i class="fas fa-language"></i> English';
        }
        // Toggle menu for mobile
        function toggleMenu(show) {
            const nav = document.getElementById('question-nav');
            const content = document.getElementById('question-content');
            if (show === undefined) {
                show = nav.style.right === '-100%' || nav.style.right === '';
            }
            if (show) {
                nav.style.right = '0';
                if (window.innerWidth >= 768) {
                    content.style.marginRight = '320px';
                }
            } else {
                nav.style.right = window.innerWidth < 768 ? '-100%' : '0';
                content.style.marginRight = window.innerWidth < 768 ? '0' : '320px';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
            document.getElementById('total-count').textContent = questions.length;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.remove('attempted');
                    box.classList.add('bg-secondary', 'text-white');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.remove('attempted');
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.remove('attempted');
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            resultsModal.show();
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check-circle me-1"></i> Submitted';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            resultsModal.hide();
            showQuestion(0);
        }
    </script>
</body>
</html>