<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">60:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 100</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">100</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 60 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["6"] = {
                name: "Reasoning",
                start: 0,
                end: 23
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="6">Reasoning</option>`;
            sections["18"] = {
                name: "General Awareness",
                start: 24,
                end: 48
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="18">General Awareness</option>`;
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 49,
                end: 73
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["7"] = {
                name: "English",
                start: 74,
                end: 98
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="7">English</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 99,
                end: 99
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "6",
                    question_en: "<p>1. What will come in the place of the question mark (?) in the following equation, if &lsquo;+&rsquo; and &lsquo;&times;&rsquo; are interchanged and &lsquo;&minus;&rsquo; and &lsquo;&divide;&rsquo; are interchanged ?&nbsp;<br>78 &minus; 6 &divide; 6 + 2 &times; 8 = ?</p>",
                    question_hi: "<p>1. यदि \'+\' और \'&times;\' को आपस में बदल दिया जाए तथा \'&minus;\' और \'&divide;\' को आपस में बदल दिया जाए तो निम्नलिखित समीकरण में प्रश्न चिह्न (?) के स्थान पर क्या आएगा ?<br>78 &minus; 6 &divide; 6 + 2 &times; 8 = ?</p>",
                    options_en: ["<p>9</p>", "<p>7</p>", 
                                "<p>4</p>", "<p>0</p>"],
                    options_hi: ["<p>9</p>", "<p>7</p>",
                                "<p>4</p>", "<p>0</p>"],
                    solution_en: "<p>1.(a)<br>78 - 6 &divide; 6 + 2 &times; 8 <br>By interchanging the signs as per given instruction, we get<br>78 &divide; 6 - 6 &times; 2 + 8<br>13 - 12 + 8 = 9</p>",
                    solution_hi: "<p>1.(a)<br>78 - 6 &divide; 6 + 2 &times; 8 <br>दिए गए निर्देश के अनुसार चिन्हों को आपस में बदलने पर हमें प्राप्त होता है,<br>78 &divide; 6 - 6 &times; 2 + 8<br>13 - 12 + 8 = 9</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "6",
                    question_en: "<p>2. In a certain language, &lsquo;he may eat rice is written as ki mi ji li\' and \' she will like rice\' is written as \'ni qi mi pi\'. How is \'rice\' written in the given language</p>",
                    question_hi: "<p>2. एक निश्चित कूट भाषा में, \'he may eat rice\' को \'ki mi ji li\' लिखा जाता है और \'she will like rice\' को \'ni qi mi pi\' लिखा जाता है। उसी कूट भाषा में \'rice\' को कैसे लिखा जाएगा ?</p>",
                    options_en: ["<p>qi</p>", "<p>ni</p>", 
                                "<p>ji</p>", "<p>mi</p>"],
                    options_hi: ["<p>qi</p>", "<p>ni</p>",
                                "<p>ji</p>", "<p>mi</p>"],
                    solution_en: "<p>2.(d)<br>&lsquo;he may eat rice &rarr;&nbsp;ki mi ji li\' &hellip;.(i)<br>\'she will like rice\' &rarr; \'ni qi mi pi\' &hellip;.(ii)<br>From (i) and (ii) &lsquo;rice&rsquo; and &lsquo;mi&rsquo; are common.<br>So, the code of &lsquo;rice&rsquo; is &lsquo;mi&rsquo;.</p>",
                    solution_hi: "<p>2.(d)<br>&lsquo;he may eat rice &rarr; ki mi ji li\' &hellip;.(i)<br>\'she will like rice\' &rarr; \'ni qi mi pi\' &hellip;.(ii)<br>(i) और (ii) से \'rice\' और \'mi\' उभयनिष्ठ हैं।<br>तो, \'rice\' का कोड \'mi\' है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "6",
                    question_en: "<p>3. Select the option that is related to the fifth word in the same way as the fourth word is related to the third word and the second word is related to the first word.<br>CABBAGE : VEGETABLE :: CUSTARD : DESSERT :: PEACH : ?</p>",
                    question_hi: "<p>3. निम्नलिखित विकल्पों में से उस विकल्प का चयन कीजिए जो पांचवें शब्द से ठीक उसी प्रकार संबंधित है जिस प्रकार चौथा शब्द तीसरे शब्द से संबंधित है और दूसरा शब्द पहले शब्द से संबंधित है।<br>पत्तागोभी : सब्जी :: कस्टर्ड : मिठाई :: आडू : ?</p>",
                    options_en: ["<p>DRINK</p>", "<p>DESSERT</p>", 
                                "<p>FRUIT</p>", "<p>VEGETABLE</p>"],
                    options_hi: ["<p>शरबत</p>", "<p>मिठाई</p>",
                                "<p>फल</p>", "<p>सब्ज़ी</p>"],
                    solution_en: "<p>3.(c)<br>Cabbage is a type of vegetable, similarly, Peach is a round fruit with orange-red skin.</p>",
                    solution_hi: "<p>3.(c)<br>पत्ता गोभी एक प्रकार की सब्जी है, इसी तरह, आड़ू , नारंगी - लाल त्वचा वाला एक गोल फल है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "6",
                    question_en: "<p>4. Three Statements are given followed by two conclusions numbered I and II. Assuming the statements to be true, even if they seem to be at variance with commonly known facts, decide which of the conclusions logically follow(s) from the statements.<br><strong>Statement I: </strong>All bicycles are wheels.<br><strong>Statement II:</strong> Some bicycles are vehicles.<br><strong>Statement III:</strong> Some vehicles are not tyres.<br><strong>Conclusion I:</strong> Some bicycles are tyres.<br><strong>Conclusion II: </strong>Some wheels are not vehicles.</p>",
                    question_hi: "<p>4. निम्नलिखित प्रश्न में, तीन कथन और उसके बाद । और ॥ क्रमांकित दो निष्कर्ष दिए गए हैं। दिए गए कथनों को सत्य मानते हुए, चाहे वे सामान्यतः ज्ञात तथ्यों से भिन्न प्रतीत होते हो, निश्वय कीजिए कि दिए गए निष्कर्षों में से कौन-सा निष्कर्ष कथनों का तार्किक रूप से अनुसरण करता है।<br><strong>कथन ।:</strong> सभी साइकिल, पहिए हैं।<br><strong>कथन ।।: </strong>कुछ साइकिल, वाहन है।<br><strong>कथन ।।।:</strong> कुछ वाहन, टायर नहीं हैं।<br><strong>निष्कर्ष।: </strong>कुछ साइकिल, टायर है।<br><strong>निष्कर्ष ॥:</strong> कुछ पहिए, वाहन नहीं है।</p>",
                    options_en: ["<p>Both conclusions I and II follow</p>", "<p>Neither conclusion I nor II follows</p>", 
                                "<p>Only conclusion I follows</p>", "<p>Only conclusion II follows</p>"],
                    options_hi: ["<p>निष्कर्ष। और ॥ दोनों अनुसरण करते हैं</p>", "<p>न तो निष्कर्ष । और न ही ॥ अनुसरण करता है</p>",
                                "<p>केवल निष्कर्ष । अनुसरण करता है</p>", "<p>केवल निष्कर्ष ॥ अनुसरण करता है</p>"],
                    solution_en: "<p>4.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742789941448.png\" alt=\"rId4\" width=\"249\" height=\"175\"><br>Clearly, we can conclude that Neither conclusion I nor II follows.</p>",
                    solution_hi: "<p>4.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742789941707.png\" alt=\"rId5\" width=\"217\" height=\"158\"><br>स्पष्ट रूप से, हम यह निष्कर्ष निकाल सकते हैं कि न तो निष्कर्ष I और न ही II अनुसरण करता है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "6",
                    question_en: "<p>5. Four letter-clusters have been given, out of which three are alike in some manner and one is different. Select the letter-cluster that is different. <br>Note: The odd one out is not based on the number of consonants/vowels or their position in the letter cluster.</p>",
                    question_hi: "<p>5. चार अक्षर-समूह दिए गए हैं, जिनमें से तीन किसी न किसी रूप में एकसमान हैं और एक उनसे असंगत है। उस असंगत अक्षर-समूह का चयन कीजिए।<br>नोट: अक्षर समूह में, असंगत व्यंजनों/स्वरों की संख्या या उनकी स्थिति पर आधारित नहीं है।.</p>",
                    options_en: ["<p>BEHI</p>", "<p>TWYZ</p>", 
                                "<p>PSUV</p>", "<p>FIKL</p>"],
                    options_hi: ["<p>BEHI</p>", "<p>TWYZ</p>",
                                "<p>PSUV</p>", "<p>FIKL</p>"],
                    solution_en: "<p>5.(a) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742789941872.png\" alt=\"rId6\" width=\"138\" height=\"77\"> , <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742789942010.png\" alt=\"rId7\" width=\"143\" height=\"75\"> , <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742789942202.png\" alt=\"rId8\" width=\"118\" height=\"74\"><br>But,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742789942352.png\" alt=\"rId9\" width=\"136\" height=\"73\"></p>",
                    solution_hi: "<p>5.(a) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742789941872.png\" alt=\"rId6\" width=\"138\" height=\"77\"> , <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742789942010.png\" alt=\"rId7\" width=\"143\" height=\"75\"> , <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742789942202.png\" alt=\"rId8\" width=\"118\" height=\"74\"><br>लेकिन,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742789942352.png\" alt=\"rId9\" width=\"136\" height=\"73\"></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "6",
                    question_en: "<p>6. In a certain code language, if HOUSE is coded as 67 and GROUND is coded as 83, then how will KITCHEN be coded ?</p>",
                    question_hi: "<p>6. एक निश्चित कूट भाषा में, यदि \'HOUSE\' को \'67\' लिखा जाता है तथा \'GROUND\' को \'83\' लिखा जाता है, तो उसी कूट भाषा में \'KITCHEN\' को कैसे लिखा जाएगा ?</p>",
                    options_en: ["<p>119</p>", "<p>110</p>", 
                                "<p>130</p>", "<p>122</p>"],
                    options_hi: ["<p>119</p>", "<p>110</p>",
                                "<p>130</p>", "<p>122</p>"],
                    solution_en: "<p>6.(a) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742789942649.png\" alt=\"rId10\" width=\"195\" height=\"114\"></p>\n<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742789943139.png\" alt=\"rId11\" width=\"240\" height=\"119\"></p>\n<p>Similarly,</p>\n<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742789943798.png\" alt=\"rId12\" width=\"285\" height=\"118\"></p>",
                    solution_hi: "<p>6.(a) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742789944024.png\" alt=\"rId13\" width=\"205\" height=\"120\"></p>\n<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742789944275.png\" alt=\"rId14\" width=\"271\" height=\"135\"></p>\n<p>इसी प्रकार,</p>\n<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742789944508.png\" alt=\"rId15\" width=\"280\" height=\"117\"></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "6",
                    question_en: "<p>7. In the following question, select the related letters from the given alternatives.<br>PTX : RVZ :: CGK : ?</p>",
                    question_hi: "<p>7. निम्नलिखित प्रश्न में, दिए गए विकल्पों में से संबंधित अक्षरों का चयन कीजिए।<br>PTX : RVZ :: CGK : ?</p>",
                    options_en: ["<p>EIM</p>", "<p>ABC</p>", 
                                "<p>VVW</p>", "<p>MNL</p>"],
                    options_hi: ["<p>EIM</p>", "<p>ABC</p>",
                                "<p>VVW</p>", "<p>MNL</p>"],
                    solution_en: "<p>7.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742789944722.png\" alt=\"rId16\" width=\"131\" height=\"103\"></p>\n<p>Similarly,&nbsp;<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742789944968.png\" alt=\"rId17\" width=\"130\" height=\"102\"></p>",
                    solution_hi: "<p>7.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742789944722.png\" alt=\"rId16\" width=\"131\" height=\"103\"></p>\n<p>इसी तरह,&nbsp;<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742789944968.png\" alt=\"rId17\" width=\"130\" height=\"102\"></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "6",
                    question_en: "<p>8. Pointing to a man, Karishma said, \"He is the husband of only daughter of the father of my brother.\"<br>How is that man related to Karishma ?</p>",
                    question_hi: "<p>8. एक आदमी की ओर इशारा करते हुए, करिश्मा ने कहा, \"वह मेरे भाई के पिता की इकलौती बेटी का पति है। उस आदमी का करिश्मा से क्या संबंध है ?</p>",
                    options_en: ["<p>Son</p>", "<p>Husband</p>", 
                                "<p>wife\'s father</p>", "<p>Brother</p>"],
                    options_hi: ["<p>बेटा</p>", "<p>पति</p>",
                                "<p>पत्नी के पिता</p>", "<p>भाई</p>"],
                    solution_en: "<p>8.(b)<br>Applying the direction given in this question, we have following family tree :<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742789945197.png\" alt=\"rId18\" width=\"342\" height=\"177\"><br>Clearly, we can see that Man is the husband of Karishma.</p>",
                    solution_hi: "<p>8.(b)<br>इस प्रश्न में दिए गए निर्देश को लागू करने पर, हमारे पास निम्नलिखित वंश-वृक्ष हैं:<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742789945351.png\" alt=\"rId19\" width=\"289\" height=\"151\"><br>स्पष्ट रूप से, हम देख सकते हैं कि आदमी करिश्मा का पति है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "6",
                    question_en: "<p>9. Select the correct mirror image of the given figure when the mirror PQ is placed to the right side of the figure.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742789945515.png\" alt=\"rId20\" width=\"133\" height=\"145\"></p>",
                    question_hi: "<p>9. दी गई आकृति के उस सही दर्पण प्रतिबिंब का चयन कीजिए, जो दर्पण PQ को उस आकृति के दाईं ओर रखने पर बनेगा। <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742789945515.png\" alt=\"rId20\" width=\"133\" height=\"145\"></p>",
                    options_en: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742789945674.png\" alt=\"rId21\" width=\"98\" height=\"97\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742789945823.png\" alt=\"rId22\" width=\"97\" height=\"100\"></p>", 
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742789946007.png\" alt=\"rId23\" width=\"99\" height=\"96\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742789946161.png\" alt=\"rId24\" width=\"98\" height=\"99\"></p>"],
                    options_hi: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742789945674.png\" alt=\"rId21\" width=\"98\" height=\"97\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742789945823.png\" alt=\"rId22\" width=\"97\" height=\"100\"></p>",
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742789946007.png\" alt=\"rId23\" width=\"98\" height=\"95\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742789946161.png\" alt=\"rId24\" width=\"98\" height=\"99\"></p>"],
                    solution_en: "<p>9.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742789946321.png\" alt=\"rId25\" width=\"99\" height=\"96\"></p>",
                    solution_hi: "<p>9.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742789946321.png\" alt=\"rId25\" width=\"99\" height=\"96\"></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "6",
                    question_en: "<p>10. Study the given Venn diagram and answer the question that follows.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742789946658.png\" alt=\"rId26\" width=\"233\" height=\"229\"> <br>How many women entrepreneurs are NOT Indian ?</p>",
                    question_hi: "<p>10. दिए गए वेन आरेख का अध्ययन करें और नीचे दिए गए प्रश्न का उत्तर दें<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742789946928.png\" alt=\"rId27\" width=\"223\" height=\"239\"> <br>कितनी महिला उद्यमी भारतीय नहीं हैं ?</p>",
                    options_en: ["<p>11</p>", "<p>17</p>", 
                                "<p>29</p>", "<p>18</p>"],
                    options_hi: ["<p>11</p>", "<p>17</p>",
                                "<p>29</p>", "<p>18</p>"],
                    solution_en: "<p>10.(d) The number of women entrepreneurs who are not Indian = 18</p>",
                    solution_hi: "<p>10.(d) महिला उद्यमियों की संख्या जो भारतीय नहीं हैं = 18</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "6",
                    question_en: "<p>11. Select the number from among the given options that can replace the question mark (?) in the following series. <br>10, ?, 46, 94, 190, 382, 766</p>",
                    question_hi: "<p>11. दिए गए विकल्पों में से उस संख्या का चयन कीजिए, जो निम्नलिखित शृंखला में प्रश्न चिह्न (?) के स्थान पर आ सकती है।<br>10, ?, 46, 94, 190, 382, 766</p>",
                    options_en: ["<p>18</p>", "<p>24</p>", 
                                "<p>22</p>", "<p>20</p>"],
                    options_hi: ["<p>18</p>", "<p>24</p>",
                                "<p>22</p>", "<p>20</p>"],
                    solution_en: "<p>11.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742789947151.png\" alt=\"rId28\" width=\"377\" height=\"71\"></p>",
                    solution_hi: "<p>11.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742789947151.png\" alt=\"rId28\" width=\"377\" height=\"71\"></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "6",
                    question_en: "<p>12. In the following question below are given some statements followed by some conclusions based on those statements. Taking the given statements to be true even if they seem to be at variance from commonly known facts. Read all the conclusions and then decide which of the given conclusion(s) logically follows the given statements.<br><strong>Statements :</strong><br>I. Some namkeen are pizza.<br>II. No pizza is biscuit.<br><strong>Conclusions :</strong><br>I. No biscuit is namkeen.<br>II. No biscuit is pizza.<br>III. Some pizza are not biscuit.</p>",
                    question_hi: "<p>12. निम्नलिखित प्रश्न में कुछ कथन और उन कथनों के आधार पर कुछ निष्कर्ष दिए गए हैं। आपको दिए गए कथनों को सत्य मानना है, चाहे वे सामान्यतः ज्ञात तथ्यों से भिन्न प्रतीत होते हों। सभी निष्कर्षों को ध्यानपूर्वक पढ़िए और निश्चय कीजिए कि दिए गए निष्कर्षों में से कौन-सा/कौन-से निष्कर्ष कथनों का तार्किक रूप से अनुसरण करता है/करते हैं।<br><strong>कथनः</strong><br>I. कुछ नमकीन, पिज़्ज़ा है।<br>II. कोई पिज़्ज़ा, बिस्किट नहीं है।<br><strong>निष्कर्षः</strong><br>I. कोई बिस्किट, नमकीन नहीं है।<br>॥. कोई बिस्किट, पिज़्ज़ा नहीं है।<br>III. कुछ पिज़्ज़ा, बिस्किट नहीं हैं।</p>",
                    options_en: ["<p>Only conclusion III follows</p>", "<p>Both conclusions II and III follows</p>", 
                                "<p>Both conclusions I and III follows</p>", "<p>All conclusion follows</p>"],
                    options_hi: ["<p>केवल निष्कर्ष III कथनों का अनुसरण करता है I</p>", "<p>निष्कर्ष II और III दोनों कथनों का अनुसरण करते हैं I</p>",
                                "<p>निष्कर्ष I और III दोनों कथनों का अनुसरण करते हैं I</p>", "<p>सभी निष्कर्ष कथनों का अनुसरण करते हैं I</p>"],
                    solution_en: "<p>12.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742789947358.png\" alt=\"rId29\" width=\"244\" height=\"66\"><br>Clearly, we can see that both conclusions II and III follow.</p>",
                    solution_hi: "<p>12.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742789947542.png\" alt=\"rId30\" width=\"249\" height=\"67\"><br>स्पष्ट रूप से, हम देख सकते हैं कि निष्कर्ष II और III दोनों अनुसरण करते हैं।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "6",
                    question_en: "<p>13. Three of the following four options are alike in a certain way and thus form a group. Which is the option that does NOT belong to that group ?<br>(<strong>NOTE :</strong> Operations should be performed on the whole numbers, without breaking down the numbers into its constituent digits. E.g. 13 &ndash; Operations on 13 such as adding /deleting /multiplying etc., to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is not allowed).</p>",
                    question_hi: "<p>13. निम्नलिखित चार विकल्पों में से तीन एक निश्चित तरीके से समान हैं और इस प्रकार एक समूह बनाते हैं। वह कौन सा विकल्प है जो उस समूह से संबंधित नहीं है ?<br><strong>नोट :</strong> संख्याओं को उनके घटक अंकों में विभक्त किए बिना पूर्ण संख्याओं पर संक्रियाएं की जानी चाहिए। उदाहरण के लिए 13 को लें- 13 पर संक्रियाएं, जैसे कि जोड़ना/घटाना/गुणा आदि 13 में की जा सकती हैं। 13 को 1 और 3 में विभक्त करना और फिर 1 और 3 पर गणितीय संक्रियाएं करने की अनुमति नहीं है।</p>",
                    options_en: ["<p>2 &ndash; 4 &ndash; 8</p>", "<p>2 &ndash; 2 &ndash; 4</p>", 
                                "<p>3 &ndash; 2 &ndash; 5</p>", "<p>1 &ndash; 3 &ndash; 3</p>"],
                    options_hi: ["<p>2 &ndash; 4 &ndash; 8</p>", "<p>2 &ndash; 2 &ndash; 4</p>",
                                "<p>3 &ndash; 2 &ndash; 5</p>", "<p>1 &ndash; 3 &ndash; 3</p>"],
                    solution_en: "<p>13.(c)<strong> Logic :-</strong> (1st number &times; 2nd number) = 3rd number <br>(2 - 4 - 8):- (2 &times; 4) = 8<br>(2 - 2 - 4) :- (2 &times; 2) = 4<br>(1 - 3 - 3) :- (1 &times; 3) = 3<br>But,<br>(3 - 2 - 5) :- (3 &times; 2) = 6 (Not 5)</p>",
                    solution_hi: "<p>13.(c) <strong>तर्क :-</strong> (पहली संख्या &times; दूसरी संख्या) = तीसरी संख्या<br>(2 - 4 - 8):- (2 &times; 4) = 8<br>(2 - 2 - 4) :- (2 &times; 2) = 4<br>(1 - 3 - 3) :- (1 &times; 3) = 3<br>लेकिन,<br>(3 - 2 - 5) :- (3 &times; 2) = 6 (5 नहीं)</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "6",
                    question_en: "<p>14. P + Q means P is the brother of Q, P &times; Q means P is the mother of Q, P &divide; Q means P is the father of Q and P - Q means P is the sister of Q . If A &times; B &divide; C - D + E - F, then how is A related to F ?</p>",
                    question_hi: "<p>14. P + Q का अर्थ है, P, Q का भाई है, P &times; Q का अर्थ है, P, Q की मां है, P &divide; Q का अर्थ है, P, Q का पिता है और P - Q का अर्थ है P, Q की बहन है। यदि A &times; B &divide; C - D + E - F, तो A का F से क्या संबंध है ?</p>",
                    options_en: ["<p>Father\'s sister</p>", "<p>Mother</p>", 
                                "<p>Mother\'s mother</p>", "<p>Father\'s mother</p>"],
                    options_hi: ["<p>पिता की बहन</p>", "<p>मां</p>",
                                "<p>मां की मां</p>", "<p>पिता की मां</p>"],
                    solution_en: "<p>14.(d)<br>Applying the directions given in the question, we get following family tree ;<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742789947733.png\" alt=\"rId31\" width=\"219\" height=\"160\"><br>Clearly, we can see that A is father&rsquo;s mothers of F.</p>",
                    solution_hi: "<p>14.(d)<br>प्रश्न में दिए गए निर्देशों को लागू करने पर, हमें निम्नलिखित वंश-वृक्ष मिलते हैं;<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742789947733.png\" alt=\"rId31\" width=\"219\" height=\"160\"><br>स्पष्ट रूप से, हम देख सकते हैं कि A, F के पिता की माँ है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "6",
                    question_en: "<p>15. From the given option figures, select the one in which the question figure is hidden/embedded. (rotation is not allowed)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742789947977.png\" alt=\"rId32\" width=\"112\" height=\"81\"> </p>",
                    question_hi: "<p>15. नीचे दी गई विकल्प आकृतियों में से, उस आकृति का चयन कीजिए जिसमें प्रश्न आकृति छिपी/निहित है। (घूर्णन की अनुमति नहीं है)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742789947977.png\" alt=\"rId32\" width=\"112\" height=\"81\"></p>",
                    options_en: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742789948167.png\" alt=\"rId33\" width=\"88\" height=\"88\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742789948424.png\" alt=\"rId34\" width=\"88\" height=\"89\"></p>", 
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742789948708.png\" alt=\"rId35\" width=\"87\" height=\"84\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742789948958.png\" alt=\"rId36\" width=\"89\" height=\"87\"></p>"],
                    options_hi: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742789948167.png\" alt=\"rId33\" width=\"87\" height=\"87\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742789948424.png\" alt=\"rId34\" width=\"88\" height=\"89\"></p>",
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742789948708.png\" alt=\"rId35\" width=\"88\" height=\"85\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742789948958.png\" alt=\"rId36\" width=\"88\" height=\"86\"></p>"],
                    solution_en: "<p>15.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742789949268.png\" alt=\"rId37\" width=\"100\" height=\"101\"></p>",
                    solution_hi: "<p>15.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742789949268.png\" alt=\"rId37\" width=\"100\" height=\"101\"></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "16",
                    section: "6",
                    question_en: "<p>16. In the following question, select the missing number from the given series.<br>2673, 891, 297, 99, ?, 11</p>",
                    question_hi: "<p>16. निम्नलिखित विकल्पों में से दी गई श्रृंखला से लुप्त संख्या का चयन कीजिए।<br>2673, 891, 297, 99, ?, 11</p>",
                    options_en: ["<p>10</p>", "<p>23</p>", 
                                "<p>33</p>", "<p>43</p>"],
                    options_hi: ["<p>10</p>", "<p>23</p>",
                                "<p>33</p>", "<p>43</p>"],
                    solution_en: "<p>16.(c)<br><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdz_SwL0GJQGehdPPVSlMFq99A72DnkbqypKPy3LQwiwx0iTLYpkWPEq-T527FzoRYRkMDum7JzsKbt7490CFVqHXhlXzoAyn0C-5CQ3ytcZOWMQw1ZfEGF0Ibck8nLcovOrRNAAQ?key=KNTRjJTnqotHVjYexcEVIbsf\" width=\"331\" height=\"82\"></p>",
                    solution_hi: "<p dir=\"ltr\">16.(c)<br><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdz_SwL0GJQGehdPPVSlMFq99A72DnkbqypKPy3LQwiwx0iTLYpkWPEq-T527FzoRYRkMDum7JzsKbt7490CFVqHXhlXzoAyn0C-5CQ3ytcZOWMQw1ZfEGF0Ibck8nLcovOrRNAAQ?key=KNTRjJTnqotHVjYexcEVIbsf\" width=\"331\" height=\"82\"></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "17",
                    section: "6",
                    question_en: "<p>17. Select the venn diagram that best illustrates the relationship between the following classes.<br>Prime minister, Finance minister, Union Government</p>",
                    question_hi: "<p>17. उस वेन आरेख का चयन करें जो निम्नलिखित वर्गों के बीच के संबंध को सर्वोत्तम रूप से दर्शाता है।<br>प्रधानमंत्री, वित्त मंत्री, केंद्र सरकार</p>",
                    options_en: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742789949738.png\" alt=\"rId39\" width=\"87\" height=\"85\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742789949933.png\" alt=\"rId40\" width=\"87\" height=\"84\"></p>", 
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742789950141.png\" alt=\"rId41\" width=\"87\" height=\"81\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742789950317.png\" alt=\"rId42\" width=\"127\" height=\"78\"></p>"],
                    options_hi: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742789949738.png\" alt=\"rId39\" width=\"87\" height=\"85\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742789949933.png\" alt=\"rId40\" width=\"87\" height=\"84\"></p>",
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742789950141.png\" alt=\"rId41\" width=\"87\" height=\"81\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742789950317.png\" alt=\"rId42\" width=\"127\" height=\"78\"></p>"],
                    solution_en: "<p>17.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742789949933.png\" alt=\"rId40\" width=\"92\" height=\"89\"></p>",
                    solution_hi: "<p>17.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742789949933.png\" alt=\"rId40\" width=\"92\" height=\"89\"></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "18",
                    section: "6",
                    question_en: "<p>18. Three different positions of the same dice are given below. Find the number opposite of 7.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742789950477.png\" alt=\"rId43\" width=\"267\" height=\"96\"></p>",
                    question_hi: "<p>18. नीचे एक ही पासे की तीन स्थितियां दी गई हैं। \'7\' वाले फलक के विपरीत फलक पर क्या आएगा ?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742789950477.png\" alt=\"rId43\" width=\"267\" height=\"96\"></p>",
                    options_en: ["<p>12</p>", "<p>20</p>", 
                                "<p>15</p>", "<p>3</p>"],
                    options_hi: ["<p>12</p>", "<p>20</p>",
                                "<p>15</p>", "<p>3</p>"],
                    solution_en: "<p>18.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742789950682.png\" alt=\"rId44\" width=\"267\" height=\"90\"><br>In figure 1 and 3, we get &lsquo;8&rsquo; as common. So moving clockwise from common no &lsquo;8&rsquo;.we get<br>3 and 7 are opposite pairs.<br>Hence, 3 is opposite to 7.</p>",
                    solution_hi: "<p>18.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742789950682.png\" alt=\"rId44\" width=\"267\" height=\"90\"><br>आकृति 1 और 3 में, हमें \'8\' उभयनिष्ट के रूप में मिलता है। तो उभयनिष्ट संख्या \'8\' से दक्षिणावर्त चलती है।<br>3 और 7 विपरीत जोड़े हैं।<br>अतः 3, 7 के विपरीत है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "19",
                    section: "6",
                    question_en: "<p>19. What should come in place of the question mark (?) in the given series based on the English alphabetical order ?<br>CHK, GKO, ? , OQW, STA</p>",
                    question_hi: "<p>19. अंग्रेजी वर्णमाला क्रम के आधार पर दी गई श्रृंखला में प्रश्न चिह्न (?) के स्थान पर क्या आना चाहिए ?<br>CHK, GKO, ? , OQW, STA</p>",
                    options_en: ["<p>KNT</p>", "<p>LOT</p>", 
                                "<p>LOS</p>", "<p>KNS</p>"],
                    options_hi: ["<p>KNT</p>", "<p>LOT</p>",
                                "<p>LOS</p>", "<p>KNS</p>"],
                    solution_en: "<p>19.(d)<br><img src=\"data:image/png;base64,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\" width=\"314\" height=\"120\"></p>",
                    solution_hi: "<p>19.(d)<br><img src=\"data:image/png;base64,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\" width=\"314\" height=\"120\"></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "20",
                    section: "6",
                    question_en: "<p>20. Seven people A, B, C, D, E, F and G are sitting around a circular table, facing the centre (but not necessarily in the same order). A is sitting second to the right of E. Only one person is sitting between D and A. B is sitting third to the right of G. F is an immediate neighbour of B. How many people are sitting between D and E when counted from the left of E ?</p>",
                    question_hi: "<p>20. सात व्यक्ति A, B, C, D, E, F और G एक वृत्ताकार मेज के चारों ओर केंद्र की ओर मुख करके बैठे हैं (परंतु जरूरी नहीं कि वे इसी क्रम में बैठे हों)। A, E के दाईं ओर दूसरे स्थान पर बैठा है। D और A के बीच में केवल एक व्यक्ति बैठा है। B, G के दाईं ओर तीसरे स्थान पर बैठा है। F, B का निकटतम पड़ोसी है। E के बाईं ओर से गिनती करने पर D और E के बीच में कितने व्यक्ति बैठे हैं ?</p>",
                    options_en: ["<p>Two</p>", "<p>One</p>", 
                                "<p>Four</p>", "<p>Three</p>"],
                    options_hi: ["<p>दो</p>", "<p>एक</p>",
                                "<p>चार</p>", "<p>तीन</p>"],
                    solution_en: "<p>20.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742789951217.png\" alt=\"rId46\" width=\"185\" height=\"159\"><br>Two people sit between D and E.</p>",
                    solution_hi: "<p>20.(a) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742789951217.png\" alt=\"rId46\" width=\"185\" height=\"159\"><br>D और E के बीच दो व्यक्ति बैठे हैं।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "21",
                    section: "6",
                    question_en: "<p>21. By interchanging which two signs and two numbers (not digits) the equation will be correct ?<br>8 - 4 &times; 9 + 6 &divide;&nbsp;3 = 29</p>",
                    question_hi: "<p>21. किन दो चिन्हों और दो संख्याओं (न कि अंकों) को आपस में बदलने से समीकरण सही होगा?<br>8 - 4 &times; 9 + 6 &divide; 3 = 29</p>",
                    options_en: ["<p>+ and &times;, 4 and 6</p>", "<p>- and &times;, 6 and 9</p>", 
                                "<p>&times; and &divide;, 8 and 4</p>", "<p>- and &times;, 9 and 3</p>"],
                    options_hi: ["<p>+ और &times;, 4 और 6</p>", "<p>- और &times;, 6 और 9</p>",
                                "<p>&times; और &divide;, 8 और 4</p>", "<p>- और &times;, 9 और 3</p>"],
                    solution_en: "<p>21.(b)<br><strong>Given : </strong>8 - 4 &times; 9 + 6 &divide; 3 = 29<br>After going through all the options, option(b) gets satisfied. Now, Interchanging &lsquo;-&rsquo; and &lsquo;&times;&rsquo; , 6 and 9 we get ;<br>8 &times; 4 - 6 + 9 &divide; 3&nbsp;<br>32 - 6 + 3 = 29<br>29 = 29<br>LHS = RHS</p>",
                    solution_hi: "<p>21.(b)<br><strong>दिया है :&nbsp;</strong>8 - 4 &times; 9 + 6 &divide; 3 = 29<br>सभी विकल्पों की जांच करने पर, विकल्प (b) संतुष्ट हो जाता है। अब, \'-\' और \'&times;\', 6 और 9 को परस्पर बदलने पर हमें प्राप्त होता है;<br>8 &times; 4 - 6 + 9 &divide; 3&nbsp;<br>32 - 6 + 3 = 29<br>29 = 29<br>LHS = RHS</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "22",
                    section: "6",
                    question_en: "<p>22. Arrange the given words in the sequence in which they occur in the dictionary.<br>1. Clamber<br>2. Clam<br>3. Claim<br>4. Clag<br>5. Clad</p>",
                    question_hi: "<p>22. दिए गए शब्दों को उसी क्रम में व्यवस्थित कीजिए जिस क्रम में वे अंग्रेजी शब्दकोश में आते हैं। <br>1. Clamber<br>2. Clam<br>3. Claim<br>4. Clag<br>5. Clad</p>",
                    options_en: ["<p>1, 2, 3, 4, 5</p>", "<p>5, 4, 3, 2, 1</p>", 
                                "<p>1, 2, 3, 5, 4</p>", "<p>5, 4, 3, 1, 2</p>"],
                    options_hi: ["<p>1, 2, 3, 4, 5</p>", "<p>5, 4, 3, 2, 1</p>",
                                "<p>1, 2, 3, 5, 4</p>", "<p>5, 4, 3, 1, 2</p>"],
                    solution_en: "<p>22.(b)<br>Correct dictionary order is : <br>Clad(5) &rarr; Clag(4) &rarr; Claim(3) &rarr; Clam(2) &rarr; Clamber(1)</p>",
                    solution_hi: "<p>22.(b)<br>सही शब्दकोश क्रम है: <br>Clad(5) &rarr; Clag(4) &rarr; Claim(3) &rarr; Clam(2) &rarr; Clamber(1)</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "23",
                    section: "6",
                    question_en: "<p>23. The numbers in each set are related to each other in a certain way. <br>(12, 18, 3), (20, 30, 5), (36, 54, ?) <br>Based on the relationship among the numbers in the first two sets, select the number that can replace the question mark (?) in the third set. </p>",
                    question_hi: "<p>23. प्रत्येक समुच्चय की संख्याएँ एक निश्चित तरीके से एक दूसरे से संबंधित हैं। <br>(12, 18, 3), (20, 30, 5), (36, 54, ?)<br>पहले दो समुच्चयों में संख्याओं के बीच संबंध के आधार पर, उस संख्या का चयन कीजिए जो तीसरे समुच्चय में प्रश्न चिह्न (?) को प्रतिस्थापित कर सके।</p>",
                    options_en: ["<p>8</p>", "<p>6</p>", 
                                "<p>7</p>", "<p>9</p>"],
                    options_hi: ["<p>8</p>", "<p>6</p>",
                                "<p>7</p>", "<p>9</p>"],
                    solution_en: "<p>23.(d) <strong>Logic :- </strong>(2<sup>nd</sup> number - 1<sup>st&nbsp; </sup>number)&divide; 2 = 3<sup>rd</sup> number<br>(12, 18, 3) :- (18 - 12) &divide; 2 &rArr; (6) &divide; 2 = 3<br>(20, 30, 5) :- (30 - 20) &divide; 2 &rArr; (10) &divide; 2 = 5<br>Similarly,<br>(36, 54, ?) :- (54 - 36) &divide; 2 &rArr; (18) &divide; 2 = 9</p>",
                    solution_hi: "<p>23.(d) <strong>तर्क:- </strong>(दूसरी संख्या - पहली संख्या) &divide; 2 = तीसरी संख्या<br>(12, 18, 3) :- (18 - 12) &divide; 2 &rArr; (6) &divide; 2 = 3<br>(20, 30, 5) :- (30 - 20) &divide; 2 &rArr; (10)&divide; 2 = 5<br>इसी प्रकार,<br>(36, 54, ?) :- (54 - 36) &divide; 2 &rArr; (18)&divide; 2 = 9</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "24",
                    section: "6",
                    question_en: "<p>24. In the following question, select the figure which can be placed at the sign of question mark (?) from the given alternatives.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742789951406.png\" alt=\"rId47\" width=\"337\" height=\"75\"> </p>",
                    question_hi: "<p>24. निम्नलिखित प्रश्न में, विकल्पों में दी गई उस आकृति का चयन कीजिए जिसे प्रश्न चिह्न (?) के स्थान पर रखा जा सकता है।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742789951406.png\" alt=\"rId47\" width=\"337\" height=\"75\"></p>",
                    options_en: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742789951546.png\" alt=\"rId48\" width=\"91\" height=\"89\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742789951787.png\" alt=\"rId49\" width=\"91\" height=\"90\"></p>", 
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742789951943.png\" alt=\"rId50\" width=\"91\" height=\"88\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742789952087.png\" alt=\"rId51\" width=\"91\" height=\"89\"></p>"],
                    options_hi: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742789951546.png\" alt=\"rId48\" width=\"91\" height=\"89\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742789951787.png\" alt=\"rId49\" width=\"93\" height=\"92\"></p>",
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742789951943.png\" alt=\"rId50\" width=\"91\" height=\"88\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742789952087.png\" alt=\"rId51\" width=\"91\" height=\"89\"></p>"],
                    solution_en: "<p>24.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742789951546.png\" alt=\"rId48\" width=\"91\" height=\"89\"></p>",
                    solution_hi: "<p>24.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742789951546.png\" alt=\"rId48\" width=\"91\" height=\"89\"></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "25",
                    section: "18",
                    question_en: "<p>25. A piece of paper is folded and punched as shown below in the question figures. From the given option figures, indicate how it will appear when opened.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742789952262.png\" alt=\"rId52\" width=\"227\" height=\"69\"> <br><br></p>",
                    question_hi: "<p>25. निम्नलिखित प्रश्न आकृतियों में दर्शाए गए अनुसार कागज के एक टुकड़े को मोड़कर उसमें छेद किया जाता है। दी गईं विकल्प आकृतियों में से बताइए कि खोले जाने पर यह कैसा दिखाई देगा ?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742789952262.png\" alt=\"rId52\" width=\"227\" height=\"69\"></p>",
                    options_en: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742789952544.png\" alt=\"rId53\" width=\"85\" height=\"85\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742789952808.png\" alt=\"rId54\" width=\"85\" height=\"85\"></p>", 
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742789953042.png\" alt=\"rId55\" width=\"85\" height=\"85\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742789953257.png\" alt=\"rId56\" width=\"84\" height=\"84\"></p>"],
                    options_hi: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742789952544.png\" alt=\"rId53\" width=\"85\" height=\"85\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742789952808.png\" alt=\"rId54\" width=\"85\" height=\"85\"></p>",
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742789953042.png\" alt=\"rId55\" width=\"85\" height=\"85\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742789953257.png\" alt=\"rId56\" width=\"85\" height=\"85\"></p>"],
                    solution_en: "<p>25.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742789952808.png\" alt=\"rId54\" width=\"84\" height=\"84\"></p>",
                    solution_hi: "<p>25.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742789952808.png\" alt=\"rId54\" width=\"84\" height=\"84\"></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "26",
                    section: "18",
                    question_en: "<p>26. Which of the following belongs to the family of &lsquo;Hominidae&rsquo; ?</p>",
                    question_hi: "<p>26. निम्नलिखित में से कौन \'होमिनिडे\' के परिवार से संबंधित है ?</p>",
                    options_en: ["<p>Mango</p>", "<p>Housefly</p>", 
                                "<p>Wheat</p>", "<p>Man</p>"],
                    options_hi: ["<p>आम</p>", "<p>हाउसफ्लाई</p>",
                                "<p>गेहूं</p>", "<p>मानव</p>"],
                    solution_en: "<p>26.(d)&nbsp;<strong data-start=\"66\" data-end=\"75\">Human</strong> (Homo sapiens). <strong data-start=\"92\" data-end=\"101\">Mango</strong> (Mangifera indica) belongs to the cashew family (Anacardiaceae). <strong data-start=\"167\" data-end=\"187\">Muscovy housefly</strong> (Musca domestica) belongs to the Muscidae family. <strong data-start=\"238\" data-end=\"247\">Wheat</strong> (Triticum aestivum) is related to the Poaceae family.</p>",
                    solution_hi: "<p>26.(d) <strong>मनुष्य </strong>(होमो सेपियन्स)। <strong>आम </strong>(मैंगिफेरा इंडिका) काजू परिवार (एनाकार्डियासी) से संबंधित है। मस्किडे हाउसफ्लाई (मुस्का डोमेस्टिका) मस्किडी परिवार से संबंधित है। <strong>गेहूं </strong>(ट्रिटिकम एस्टिवम) पोएसी के परिवार से संबंधित है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "27",
                    section: "18",
                    question_en: "<p>27. Who was sworn in as the 23rd Governor of Kerala in January 2025 ?</p>",
                    question_hi: "<p>27. जनवरी 2025 में केरल के 23वें राज्यपाल के रूप में किसने शपथ ली ?</p>",
                    options_en: ["<p>Rajendra Vishwanath Arlekar</p>", "<p>Arif Mohammed Khan</p>", 
                                "<p>V. P. Singh</p>", "<p>Keshari Nath Tripathi</p>"],
                    options_hi: ["<p>राजेंद्र विश्वनाथ आर्लेकर</p>", "<p>आरिफ मोहम्मद खान</p>",
                                "<p>वी. पी. सिंह</p>", "<p>केशरी नाथ त्रिपाठी</p>"],
                    solution_en: "<p>27.(a) <strong>Rajendra Vishwanath Arlekar.</strong> He was appointed as the 21st Governor of Himachal Pradesh, a position he held until 2023. In February 2023, he assumed office as the 30th Governor of Bihar.</p>",
                    solution_hi: "<p>27.(a) <strong>राजेंद्र विश्वनाथ आर्लेकर। </strong>वह इससे पहले हिमाचल प्रदेश के 21वें राज्यपाल के रूप में नियुक्त हुए थे और इस पद पर 2023 तक रहे। फरवरी 2023 में, उन्होंने बिहार के 30वें राज्यपाल के रूप में पदभार ग्रहण किया।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "28",
                    section: "18",
                    question_en: "<p>28. Who among the following headed the National Income Committee ?</p>",
                    question_hi: "<p>28. निम्नलिखित में से कौन राष्ट्रीय आय समिति के अध्यक्ष थे ?</p>",
                    options_en: ["<p>PC Mahalanobis</p>", "<p>VKRV Rao</p>", 
                                "<p>DR Gadgil</p>", "<p>BR Ambedkar</p>"],
                    options_hi: ["<p>पी. सी. महालनोबिस</p>", "<p>वी. के. आर. वी. राव</p>",
                                "<p>डी. आर. गाडगिल</p>", "<p>बी. आर. अम्बेडकर</p>"],
                    solution_en: "<p>28.(a) <strong>PC Mahalanobis</strong> (chairman of Indian Statistical Institute). The<strong> National Income Committee</strong> (1949) calculated the National Income of India and compiled statistics.</p>",
                    solution_hi: "<p>28.(a) <strong>पीसी महालनोबिस</strong> (भारतीय सांख्यिकी संस्थान के अध्यक्ष)। <strong>राष्ट्रीय आय समिति</strong> (1949) ने भारत की राष्ट्रीय आय की गणना की और आकंड़े संकलित किये।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "29",
                    section: "18",
                    question_en: "<p>29. Offenses for which the police can arrest a person without an order from the court are called ________.</p>",
                    question_hi: "<p>29. ऐसे अपराध, जिनके लिए पुलिस न्यायालय के आदेश के बिना किसी व्यक्ति को गिरफ्तार कर सकती है, उसे _______ कहते हैं।</p>",
                    options_en: ["<p>Cognizable offences</p>", "<p>Detention offences</p>", 
                                "<p>Non Cognizable offences</p>", "<p>Cross offences</p>"],
                    options_hi: ["<p>संज्ञेय अपराध (Cognizable offences)</p>", "<p>नजरबंदी अपराध (Detention offences)</p>",
                                "<p>असंज्ञेय अपराध (Non Cognizable offences)</p>", "<p>क्रॉस अपराध (Cross offences)</p>"],
                    solution_en: "<p>29.(a) <strong>Cognizable offenses - </strong>It includes murder, rape, theft, kidnapping, counterfeiting, etc. <strong>Detention - </strong>The act of temporarily holding individuals in custody or confinement, typically by a government or governing authority by removing their freedom or liberty at that time. <strong>Non-Cognizable offenses -</strong> An offense for which a Police officer has no authority to arrest without warrant. These include offenses like cheating, assault, defamation etc.</p>",
                    solution_hi: "<p>29.(a) <strong>संज्ञेय अपराध</strong> (Cognizable offences) - इसमें हत्या, बलात्कार, चोरी, अपहरण, जालसाजी आदि शामिल हैं। <strong>नजरबंदी (Detention) - </strong>वह प्रक्रिया जिसके तहत कोई राज्य या निजी नागरिक उस समय उनकी स्वतंत्रता को हटाकर किसी व्यक्ति को कानूनी रूप से पकड़ कर रखता है। <strong>असंज्ञेय अपराध -</strong> ऐसा अपराध जिसके लिए पुलिस को बिना वारंट के गिरफ्तार करने का अधिकार नहीं है। इनमें धोखाधड़ी, हमला, मानहानि आदि जैसे अपराध शामिल हैं।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "30",
                    section: "18",
                    question_en: "<p>30. Which of the following statements are correct regarding the Union Public Service Commission ?<br>A. The Parliament has the power to make regulations as to conditions of service of the Chairman, members and staff of the commission.<br>B. The Government of India Act, 1935, envisaged a Public Service Commission for the Federation and a Provincial Public Service Commission for each Province or group of Provinces.<br>C. The Federal Public Service Commission came to be known as the Union Public Service Commission by virtue of Clause (1) of Article 378 of the Constitution in 1950.</p>",
                    question_hi: "<p>30. संघ लोक सेवा आयोग के संबंध में निम्नलिखित में से कौन सा/से कथन सत्य है/हैं?<br>A. संसद के पास आयोग के अध्यक्ष, सदस्यों और कर्मचारियों की सेवा की शर्तों के रूप में नियम बनाने की शक्ति है।<br>B. भारत सरकार अधिनियम, 1935 में संघ के लिए एक लोक सेवा आयोग और प्रत्येक प्रांत या प्रांतों के समूह के लिए एक प्रांतीय लोक सेवा आयोग की परिकल्पना की गई थी।<br>C. संघीय लोक सेवा आयोग को 1950 में संविधान के अनुच्छेद 378 के खंड (1) के आधार पर संघ लोक सेवा आयोग के नाम से जाना जाने लगा।</p>",
                    options_en: ["<p>A and B only</p>", "<p>A, B and C</p>", 
                                "<p>B and C only</p>", "<p>A and C only</p>"],
                    options_hi: ["<p>केवल A और B</p>", "<p>A, B और C</p>",
                                "<p>केवल B और C</p>", "<p>केवल A और C</p>"],
                    solution_en: "<p>30.(c) <strong>B and C only.</strong> The President has the power to make regulations as to conditions of service of the Chairman, members and staff of the commission. Union public service commission (UPSC) : Established on - 1 October 1926. Articles 315 to 323 (Part XIV) - Provides for the establishment of a Public Service Commission for the Union and a Public Service Commission for each State. Work - Recruiting civil servants. The first chairman of UPSC in independent India was H. K. Kripalani.</p>",
                    solution_hi: "<p>30.(c) <strong>केवल B और C।</strong> राष्ट्रपति के पास आयोग के अध्यक्ष, सदस्यों और कर्मचारियों की सेवा शर्तों के संबंध में कानून बनाने का अधिकार है। लोक सेवा आयोग (UPSC): स्थापना - 1 अक्टूबर 1926। अनुच्छेद 315 से 323 (भाग XIV) - संघ के लिए लोक सेवा आयोग और प्रत्येक राज्य के लिए लोक सेवा आयोग की स्थापना का प्रावधान करता है। कार्य - सिविल सेवकों की भर्ती करना। स्वतंत्र भारत में UPSC के पहले अध्यक्ष एच. के. कृपलानी थे।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "31",
                    section: "18",
                    question_en: "<p>31. Who was named ICC Men&rsquo;s Test Cricketer of the Year in 2024 ?</p>",
                    question_hi: "<p>31. 2024 में ICC मेन्स टेस्ट क्रिकेटर ऑफ द ईयर किसे चुना गया ?</p>",
                    options_en: ["<p>Virat Kohli</p>", "<p>Jasprit Bumrah</p>", 
                                "<p>Ravichandran Ashwin</p>", "<p>Joe Root</p>"],
                    options_hi: ["<p>विराट कोहली</p>", "<p>जसप्रीत बुमराह</p>",
                                "<p>रविचंद्रन अश्विन</p>", "<p>जो रूट</p>"],
                    solution_en: "<p>31.(b) <strong>Jasprit Bumrah. </strong>Jasprit Bumrah was named ICC Men&rsquo;s Test Cricketer of the Year for his exceptional performances in 2024. After recovering from a back injury in late 2023, he made a remarkable comeback and played a crucial role in India\'s success both at home and overseas, establishing himself as one of the most formidable Test bowlers.</p>",
                    solution_hi: "<p>31.(b)<strong> जसप्रीत बुमराह। </strong>जसप्रीत बुमराह को 2024 में टेस्ट क्रिकेट में उनके असाधारण प्रदर्शन के लिए ICC मेन्स टेस्ट क्रिकेटर ऑफ द ईयर चुना गया। पीठ की चोट से उबरने के बाद, उन्होंने घरेलू और विदेशी दोनों श्रृंखलाओं में भारत की सफलता में महत्वपूर्ण भूमिका निभाई और टेस्ट क्रिकेट के सबसे प्रभावी&nbsp;गेंदबाजों में से एक बने।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "32",
                    section: "18",
                    question_en: "<p>32. Ryotwari system of revenue collection in India, introduced by the British, was based on the_______.</p>",
                    question_hi: "<p>32. भारत में अंग्रेजों द्वारा शुरू की गई राजस्व संग्रह की रैयतवाड़ी व्यवस्था______पर आधारित थी।</p>",
                    options_en: ["<p>Smith\'s theory of rent</p>", "<p>Ricardian theory of rent</p>", 
                                "<p>Malthusian theory of rent</p>", "<p>Marx\'s theory of rent</p>"],
                    options_hi: ["<p>स्मिथ के लगान सिद्धांत (Smith\'s theory of rent)</p>", "<p>रिकार्ड के लगान सिद्धांत (Ricardian theory of rent)</p>",
                                "<p>माल्थस के लगान सिद्धांत (Malthusian theory of rent)</p>", "<p>मार्क्स के लगान सिद्धांत (Marx\'s theory of rent)</p>"],
                    solution_en: "<p>32.(b)<strong> Ricardian theory of rent -</strong> The land of higher fertility gets higher rent than less fertile land. <strong>Ryotwari System -</strong> The peasants or cultivators were regarded as the owners of the land. The cultivators had to pay annual taxes directly to the government. Introduced by Thomas Munro and Alexander Read in 1820 in the Madras and Bombay presidency. <strong>Mahalwari system -</strong> The land revenue was collected from the farmers by the village headmen on behalf of the whole village. Introduced by - Holt Mackenzie in 1822 in areas of Uttar Pradesh.</p>",
                    solution_hi: "<p>32.(b) <strong>रिकार्ड के लगान सिद्धांत </strong>(Ricardian theory of rent) - अधिक उर्वरता वाली भूमि का लगान कम उपजाऊ भूमि की तुलना में अधिक होता है।<strong> रैयतवाड़ी व्यवस्था - </strong>किसानों या खेतिहरों को भूमि का मालिक माना जाता था। कृषकों को सीधे सरकार को वार्षिक कर देना पड़ता था। थॉमस मुनरो और अलेक्जेंडर रीड द्वारा 1820 में मद्रास और बम्बई प्रांत में शुरु की गई ।&nbsp;<strong>महालवाड़ी व्यवस्था - </strong>सम्पूर्ण गाँव से ग्राम प्रधानों द्वारा किसानों से भू-राजस्व वसूल किया जाता था। उत्तर प्रदेश के क्षेत्रों में 1822 में होल्ट मैकेंज़ी द्वारा शुरू किया गया था।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "33",
                    section: "18",
                    question_en: "<p>33. Which among the following Acts discarded the \'dyarchy&rsquo; system at the provincial level and allowed for the emergence of popularly elected provincial legislatures ?</p>",
                    question_hi: "<p>33. निम्नलिखित में से किस अधिनियम ने प्रांतीय स्तर पर \'द्वैध शासन&rsquo; प्रणाली को त्याग दिया और लोकप्रिय रूप से निर्वाचित प्रांतीय विधानसभाओं के उद्गमन में सहायता की ?</p>",
                    options_en: ["<p>Charter Act, 1793</p>", "<p>Montague-Chelmsford Reform (Government of India Act), 1919</p>", 
                                "<p>(Government of India Act), 1935</p>", "<p>Pitt\'s India Act, 1784</p>"],
                    options_hi: ["<p>चार्टर अधिनियम, 1793</p>", "<p>मांटेग्यू-चेम्सफोर्ड सुधार (भारत शासन अधिनियम), 1919</p>",
                                "<p>भारत सरकार अधिनियम, 1935</p>", "<p>पिट्स इंडिया एक्ट, 1784</p>"],
                    solution_en: "<p>33.(c) <strong>Government of India Act, 1935. </strong>The act proposed constitutional proposals and reforms and had many features which included the bicameral legislature, provincial autonomy, separate communal electorate and the creation of an all India federation.</p>",
                    solution_hi: "<p>33.(c) <strong>भारत सरकार अधिनियम, 1935 । </strong>इस अधिनियम में संवैधानिक प्रस्ताव और सुधार प्रस्तावित थे और इसमें कई विशेषताएं थीं जिनमें द्विसदनीय विधायिका, प्रांतीय स्वायत्तता, अलग सांप्रदायिक निर्वाचन क्षेत्र और एक अखिल भारतीय महासंघ का निर्माण शामिल था।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "34",
                    section: "18",
                    question_en: "<p>34. Match Column-A with Column-B<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742789953426.png\" alt=\"rId57\" width=\"357\" height=\"139\"></p>",
                    question_hi: "<p>34. कॉलम -A को कॉलम-B के साथ सुमेलित करें।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742789953614.png\" alt=\"rId58\" width=\"433\" height=\"141\"></p>",
                    options_en: ["<p>i-c, ii-a, iii-d, iv-c</p>", "<p>i-b, ii-a, iii-c, iv-d</p>", 
                                "<p>i-a, ii-b, iii-c, iv-d</p>", "<p>i-d, ii-a, iii-b, iv-c</p>"],
                    options_hi: ["<p>i-c, ii-a, iii-d, iv-c</p>", "<p>i-b, ii-a, iii-c, iv-d</p>",
                                "<p>i-a, ii-b, iii-c, iv-d</p>", "<p>i-d, ii-a, iii-b, iv-c</p>"],
                    solution_en: "<p>34.(d) <strong>i-d, ii-a, iii-b, iv-c.</strong> Mitochondria {discovered by Albert von Kolliker (1857)}. Ribosomes {George E. Palade (1955)}. Nucleus { Robert Brown (1831)}. Lysosomes {Christian de Duve (1955)}.</p>",
                    solution_hi: "<p>34.(d) <strong>i-d, ii-a, iii-b, iv-c ।</strong> माइटोकॉन्ड्रिया {अल्बर्ट वॉन कोलिकर (1857) द्वारा खोजा गया}। राइबोसोम {जॉर्ज ई. पलाडे (1955)}। नाभिक {रॉबर्ट ब्राउन (1831)}। लाइसोसोम {क्रिश्चियन डी ड्यूवे (1955)}।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "35",
                    section: "18",
                    question_en: "<p>35. Name the high-intensity tri-services exercise conducted by the Indian Army from 10th to 18th November 2024.</p>",
                    question_hi: "<p>35. 10 से 18 नवंबर 2024 तक भारतीय सेना द्वारा आयोजित उच्च-तीव्रता त्रिसेवा अभ्यास का नाम क्या था ?</p>",
                    options_en: ["<p>Tiger Triumph</p>", "<p>INDRA</p>", 
                                "<p>Poorvi Prahar</p>", "<p>Vajra Prahar</p>"],
                    options_hi: ["<p>टाइगर ट्रायंफ</p>", "<p>इंद्र</p>",
                                "<p>पूर्वी प्रहार</p>", "<p>वज्र प्रहार</p>"],
                    solution_en: "<p>35.(c) <strong>Poorvi Prahar. </strong>This joint exercise aims to sharpen the combat effectiveness of the Indian Army, Navy, and Air Force in implementing Integrated Joint Operations in the difficult mountainous terrain of the region. The exercise, Poorvi Prahar, will involve innovative technological advancements such as, First Person View (FPV) Drones, and Loiter Munitions.</p>",
                    solution_hi: "<p>35.(c)<strong> पूर्वी प्रहार ।</strong> यह संयुक्त अभ्यास भारतीय सेना, नौसेना और वायु सेना की मुकाबला क्षमता को बढ़ाने के लिए आयोजित किया गया था। यह विशेष रूप से कठिन पर्वतीय क्षेत्र में एकीकृत संयुक्त संचालन की क्षमता को बेहतर बनाने का उद्देश्य था।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "36",
                    section: "18",
                    question_en: "<p>36. Which of the following cities became the capital of Bahamani sultanate immediately after its foundation ?</p>",
                    question_hi: "<p>36. निम्नलिखित में से कौन सा शहर इसकी स्थापना के तुरंत बाद बहमनी सल्तनत की राजधानी बन गया ?</p>",
                    options_en: ["<p>Devagiri</p>", "<p>Daulatabad</p>", 
                                "<p>Aḥsanabad</p>", "<p>Berar</p>"],
                    options_hi: ["<p>देवगिरी</p>", "<p>दौलताबाद</p>",
                                "<p>आसनाबाद</p>", "<p>बरार</p>"],
                    solution_en: "<p>36.(c) <strong>Ahsanabad. Bahamani Sultanate </strong>(Sunni Muslim empire that ruled the Deccan) - Founded in 1347 by ʿAlaʾ al-Din Bahman Shah. Between 1490 and 1518 dissolved into the five successor powers of Bijapur, Ahmadnagar, Golconda, Berar, and Bidar.</p>",
                    solution_hi: "<p>36.(c) <strong>आसनाबाद। </strong>बहमनी सल्तनत (दक्कन पर शासन करने वाला सुन्नी मुस्लिम साम्राज्य) - 1347 में अलाउद्दीन बहमन शाह द्वारा स्थापित। 1490 और 1518 के बीच बीजापुर, अहमदनगर, गोलकुंडा, बरार और बीदर की पांच उत्तराधिकारी शक्तियों में समाहित हो गया।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "37",
                    section: "18",
                    question_en: "<p>37. Which of the following is NOT an application of the third law of motion ?</p>",
                    question_hi: "<p>37. निम्नलिखित में से कौन सा गति के तीसरे नियम का एक अनुप्रयोग नहीं है ?</p>",
                    options_en: ["<p>A fielder pulls his hands gradually with the moving ball while holding a catch.</p>", "<p>Colliding with a player while kicking a football and feeling hurt.</p>", 
                                "<p>As the sailor jumps in the forward direction, the boat moves backward.</p>", "<p>A forward force on the bullet and recoil of the gun.</p>"],
                    options_hi: ["<p>क्रिकेट के खेल में कैच लपकने के लिए क्षेत्ररक्षक गेंद के साथ अपने हाथों को धीरे-धीरे पीछे की ओर खींचता है।</p>", "<p>फुटबॉल को किक मारते समय विपक्षी टीम के खिलाड़ियों से टकराकर चोटिल होना ।</p>",
                                "<p>नाविक के आगे की ओर कूदने की स्थिति में नाव पीछे की ओर गति करती है।</p>", "<p>गोली पर लगने वाला त्वरित बल और बंदूक का प्रतिक्षेपण।</p>"],
                    solution_en: "<p>37.(a) <strong>Newton&rsquo;s Laws of Motion: Third law - </strong>States that to every action, there is an equal and opposite reaction. <strong>Force: </strong>The push or pull on an object with mass causes it to change its velocity. SI unit of Force - <strong>Newton. </strong>Dimension of Force - <strong>MLT<sup>-2</sup>.</strong> CGS unit of Force - <strong>dyne</strong>.</p>",
                    solution_hi: "<p>37.(a) <strong>न्यूटन के गति के नियम: तीसरा नियम - </strong>यह बताता है कि प्रत्येक क्रिया के बराबर और विपरीत प्रतिक्रिया होती है।<strong> बल : </strong>द्रव्यमान के साथ किसी वस्तु पर दबाव या खिंचाव उसके वेग को बदलने का कारण बनता है। बल का SI मात्रक - <strong>न्यूटन।</strong> बल की विमा - <strong>MLT<sup>-2</sup></strong>. बल की CGS इकाई - <strong>डाइन।</strong></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "38",
                    section: "18",
                    question_en: "<p>38. Which of the following cities became the capital of Bengal in 1704 ?</p>",
                    question_hi: "<p>38. निम्नलिखित में से कौन सा शहर 1704 में बंगाल की राजधानी बना ?</p>",
                    options_en: ["<p>Howrah</p>", "<p>Murshidabad</p>", 
                                "<p>Kharagpur</p>", "<p>Kolkata</p>"],
                    options_hi: ["<p>हावड़ा</p>", "<p>मुर्शिदाबाद</p>",
                                "<p>खड़गपुर</p>", "<p>कोलकाता</p>"],
                    solution_en: "<p>38.(b) <strong>Murshidabad.</strong> <strong>Murshid Quli Khan </strong>(Zamin Ali Quli) transferred his capital to the then Mukhsusabad later came to be known after Murshid Quli Khan as Murshidabad in 1704 A.D. Murshidabad (West Bengal) on the banks of the Bhagirathi became the center of silk. Murshid Quli Khan (1717-1727) was the<strong> first Nawab </strong>of Bengal.</p>",
                    solution_hi: "<p>38.(b) <strong>मुर्शिदाबाद ।</strong> <strong>मुर्शिद कुली खान</strong> (ज़मीन अली कुली) ने अपनी राजधानी को तत्कालीन मुखसुसाबाद में स्थानांतरित कर दिया, जो बाद में (1704 ई.) मुर्शिद कुली खान के नाम पर मुर्शिदाबाद के नाम से प्रसिद्ध हो गया। भागीरथी के तट पर स्थित मुर्शिदाबाद (पश्चिम बंगाल) रेशम का केंद्र बन गया। मुर्शिद कुली खान (1717-1727) बंगाल के <strong>पहले नवाब</strong> थे।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "39",
                    section: "18",
                    question_en: "<p>39. Who wrote the Panchatantra, a collection of fables ?</p>",
                    question_hi: "<p>39. दंत कथाओं के संग्रह, पंचतंत्र की रचना किसने की थी ?</p>",
                    options_en: ["<p>Rumi</p>", "<p>Amar Singh</p>", 
                                "<p>Jaya Dev</p>", "<p>Vishnu Sharma</p>"],
                    options_hi: ["<p>रूमी</p>", "<p>अमर सिंह</p>",
                                "<p>जया देव</p>", "<p>विष्णु शर्मा</p>"],
                    solution_en: "<p>39.(d) <strong>Vishnu Sharma </strong>was an Indian scholar and author. The <strong>Panchatantra </strong>is a collection of interrelated animal fables in Sanskrit verse and prose, arranged within a frame story.</p>",
                    solution_hi: "<p>39.(d) <strong>विष्णु शर्मा </strong>एक भारतीय विद्वान और लेखक थे। <strong>पंचतंत्र </strong>संस्कृत पद्य और गद्य में परस्पर संबंधित पशु दंतकथाओं का एक संग्रह है, जो एक फ्रेम कहानी के भीतर व्यवस्थित है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "40",
                    section: "18",
                    question_en: "<p>40. The Pudhumai Penn scheme is launched by which state government ?</p>",
                    question_hi: "<p>40. पुधुमई पेन्न (Pudhumai Penn) योजना किस राज्य सरकार द्वारा शुरू की गई है ?</p>",
                    options_en: ["<p>Tamil Nadu</p>", "<p>Maharashtra</p>", 
                                "<p>Andhra Pradesh</p>", "<p>Kerala</p>"],
                    options_hi: ["<p>तमिलनाडु</p>", "<p>महाराष्ट्र</p>",
                                "<p>आंध्र प्रदेश</p>", "<p>केरल</p>"],
                    solution_en: "<p>40.(a) <strong>Tamil Nadu. The Pudhumai Penn scheme:</strong> Tamil Nadu Chief minister M K Stalin launched this Scheme on 5 September 2022. <strong>Aim : </strong>To enhance the enrolment ratio of girls from Government schools to Higher Education Institutions. <strong>Beneficiaries : </strong>Girl students studying from class VI to XII in Government school. <strong>Benefits : </strong>₹1000 will be deposited in the bank account of the girl student</p>",
                    solution_hi: "<p>40.(a) <strong>तमिलनाडु। पुधुमई पेन योजना:</strong> तमिलनाडु के मुख्यमंत्री एम के स्टालिन ने 5 सितंबर 2022 को इस योजना की शुरुआत की।<strong> उद्देश्य: </strong>सरकारी स्कूलों से उच्च शिक्षा संस्थानों में लड़कियों के नामांकन अनुपात को बढ़ाना। <strong>लाभार्थी : </strong>सरकारी स्कूल में कक्षा छह से 12वीं तक पढ़ने वाली छात्राएं। <strong>फायदें :</strong> छात्रा के बैंक&nbsp;खाते में ₹1000 जमा किए जाएंगे।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "41",
                    section: "18",
                    question_en: "<p>41. Languages like Nyishi, Apatani and Bokar are widely spoken in ______.</p>",
                    question_hi: "<p>41. नयिशी, अपतानी तथा बोकर भाषाएँ व्यापक रूप से ______में बोली जाती हैं।</p>",
                    options_en: ["<p>Mizoram</p>", "<p>Arunachal Pradesh</p>", 
                                "<p>Nagaland</p>", "<p>Assam</p>"],
                    options_hi: ["<p>मिजोरम</p>", "<p>अरुणाचल प्रदेश</p>",
                                "<p>नागालैंड</p>", "<p>असम</p>"],
                    solution_en: "<p>41.(b) <strong>Arunachal Pradesh. </strong>Nyishi, Apatani and Bokar fall under the Tani dialect which is a type of Tibeto-Burman Language and is widely spoken in Arunachal Pradesh.</p>",
                    solution_hi: "<p>41.(b) <strong>अरुणाचल प्रदेश । </strong>नयिशी, अपतानी और बोकार तानी बोली के अंतर्गत आते हैं जो एक प्रकार की तिब्बती-बर्मन भाषा है और अरुणाचल प्रदेश में व्यापक रूप से बोली जाती है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "42",
                    section: "18",
                    question_en: "<p>42. In which year was the Indian Parliamentary Group established ?</p>",
                    question_hi: "<p>42. भारतीय संसदीय समूह की स्थापना किस वर्ष हुई थी ?</p>",
                    options_en: ["<p>1950</p>", "<p>1949</p>", 
                                "<p>1948</p>", "<p>1951</p>"],
                    options_hi: ["<p>1950</p>", "<p>1949</p>",
                                "<p>1948</p>", "<p>1951</p>"],
                    solution_en: "<p>42.(b) In <strong>1949,</strong> The <strong>Indian Parliamentary Group</strong> was an autonomous body formed in the year 1949 in pursuance of a motion adopted by the Constituent Assembly (Legislative)&nbsp;on 16th August 1948. Speaker of Lok Sabha is its ex-officio President.</p>",
                    solution_hi: "<p>42.(b) <strong>1949,</strong> <strong>भारतीय संसदीय समूह</strong> एक स्वायत्त निकाय है जिसका गठन वर्ष 1949 में ,16 अगस्त 1948 को संविधान सभा (विधायी) द्वारा अपनाए गए प्रस्ताव के अनुसरण में किया गया था। लोकसभा अध्यक्ष इसका पदेन अध्यक्ष होता है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "43",
                    section: "18",
                    question_en: "<p>43. Asansol, Howrah, Malda and Sealdah division come under which railway zone of India ?</p>",
                    question_hi: "<p>43. आसनसोल, हावड़ा, मालदा और सियालदह मंडल भारत के किस रेलवे जोन के अंतर्गत आते हैं ?</p>",
                    options_en: ["<p>West Central Railway</p>", "<p>Northern Railway</p>", 
                                "<p>Central Railway</p>", "<p>Eastern Railway</p>"],
                    options_hi: ["<p>पश्चिम मध्य रेलवे</p>", "<p>उत्तर रेलवे</p>",
                                "<p>मध्य रेलवे</p>", "<p>पूर्व रेलवे</p>"],
                    solution_en: "<p>43.(d) <strong>Eastern Railway. </strong>Formed on - 14 April 1952 by amalgamating three lower divisions of the East Indian Railway: Howrah, Asansol and Danapur. Headquarters - Kolkata. Kalka Mail of Eastern Railway is the oldest running train in the history of Indian Railways.</p>",
                    solution_hi: "<p>43.(d) <strong>पूर्व रेलवे।</strong> पूर्वी रेलवे के तीन अवर मण्डल: हावड़ा, आसनसोल और दानापुर को मिलाकर 14 अप्रैल 1952 को गठित किया गया था । मुख्यालय - कोलकाता। पूर्वी रेलवे की कालका मेल भारतीय रेलवे के इतिहास की सबसे पुरानी चलने वाली ट्रेन है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "44",
                    section: "18",
                    question_en: "<p>44. Which of the following organic compounds is synonymous with olefiant gas which is used to make anaesthetics, refrigerants and other chemicals ?</p>",
                    question_hi: "<p>44. निम्नलिखित में से कौन-सा कार्बनिक यौगिक ओलेफिएंट गैस का पर्याय है, जिसका इस्तेमाल संवेदनाहरक, प्रशीतक और अन्य रसायनों को बनाने के लिए किया जाता है ?</p>",
                    options_en: ["<p>Ethylene</p>", "<p>1-Pentene</p>", 
                                "<p>Methane</p>", "<p>Butane</p>"],
                    options_hi: ["<p>एथिलीन</p>", "<p>1- पेंटीन</p>",
                                "<p>मीथेन</p>", "<p>ब्यूटेन</p>"],
                    solution_en: "<p>44.(a) <strong>Ethylene (C<sub>2</sub>H<sub>4</sub>): </strong>A colourless flammable gas with a faint &lsquo;sweet and musky&rsquo; odour when pure, Simplest form of organic compounds and used in fruit ripening. <strong>1-Pentene (C<sub>5</sub>H<sub>10</sub>) -</strong> A colourless liquid with an odour of gasoline, Insoluble in water and less dense than water.<strong> Butane (C<sub>4</sub>H<sub>10</sub>) -</strong> A highly flammable, colourless, odourless, easily liquefied gas which is used as fuel for cigarette lighters and portable stoves, a propellant in aerosols, a heating fuel, a refrigerant.</p>",
                    solution_hi: "<p>44.(a) <strong>एथिलीन (C<sub>2</sub>H<sub>4</sub>): </strong>शुद्ध होने पर हल्की \'मीठी और मांसल\' गंध वाली एक रंगहीन ज्वलनशील गैस, कार्बनिक यौगिकों का सबसे सरल रूप और फलों को पकाने में उपयोग की जाती है। <strong>1-पेंटीन (C<sub>5</sub>H<sub>10</sub>) -</strong> गैसोलीन की गंध वाला एक रंगहीन तरल, पानी में अघुलनशील और पानी से कम घना होता है। <strong>ब्यूटेन (C<sub>4</sub>H<sub>10</sub>) -</strong> एक अत्यधिक ज्वलनशील, रंगहीन, गंधहीन, आसानी से तरलीकृत गैस जिसका उपयोग सिगरेट लाइटर और पोर्टेबल स्टोव के लिए ईंधन, एरोसोल में एक प्रणोदक, एक हीटिंग ईंधन, एक रेफ्रिजरेंट के रूप में किया जाता है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "45",
                    section: "18",
                    question_en: "<p>45. Mario de Miranda was a ______ from the state of Goa.</p>",
                    question_hi: "<p>45. मारियो डी मिरांडा गोवा राज्य का एक ______था।</p>",
                    options_en: ["<p>designer</p>", "<p>vocalist</p>", 
                                "<p>cartoonist</p>", "<p>poet</p>"],
                    options_hi: ["<p>डिजाइनर</p>", "<p>गायक</p>",
                                "<p>कार्टूनिस्ट</p>", "<p>कवि</p>"],
                    solution_en: "<p>45.(c) <strong>Cartoonist. </strong>Mario de Miranda : Awards - Padma Shri (1988), Padma Bhushan (2002), Padma Vibhushan (2012).</p>",
                    solution_hi: "<p>45.(c) <strong>कार्टूनिस्ट । </strong>मारियो डी मिरांडा: पुरस्कार - पद्म श्री (1988), पद्म भूषण (2002), पद्म विभूषण (2012)।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "46",
                    section: "18",
                    question_en: "<p>46. What is the theme for World Tourism Day 2024 ?</p>",
                    question_hi: "<p>46. विश्व पर्यटन दिवस 2024 का थीम क्या है ?</p>",
                    options_en: ["<p>Sustainable Tourism for All</p>", "<p>Tourism and Peace</p>", 
                                "<p>Explore the World</p>", "<p>Tourism for Global Unity</p>"],
                    options_hi: ["<p>सभी के लिए सतत पर्यटन</p>", "<p>पर्यटन और शांति</p>",
                                "<p>दुनिया की खोज</p>", "<p>वैश्विक एकता के लिए पर्यटन</p>"],
                    solution_en: "<p>46.(b)<strong> Tourism and Peace. </strong>The UNWTO established World Tourism Day in 1980, marking a significant milestone in recognizing the global impact of tourism.</p>",
                    solution_hi: "<p>46.(b) <strong>पर्यटन और शांति ।</strong> UNWTO ने 1980 में विश्व पर्यटन दिवस की स्थापना की, जो पर्यटन के वैश्विक प्रभाव को पहचानने में एक महत्वपूर्ण मील का पत्थर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "47",
                    section: "18",
                    question_en: "<p>47. Minati Mishra was associated with which classical dance of India ?</p>",
                    question_hi: "<p>47. मिनाती मिश्रा भारत के किस शास्त्रीय नृत्य से जुड़ी थीं ?</p>",
                    options_en: ["<p>kathak</p>", "<p>kuchipudi</p>", 
                                "<p>odissi</p>", "<p>manipuri</p>"],
                    options_hi: ["<p>कथक</p>", "<p>कुचिपुड़ी</p>",
                                "<p>ओडिसी</p>", "<p>मणिपुरी</p>"],
                    solution_en: "<p>47.(c) <strong>Odissi.</strong> Minati Mishra was a recipient of the 1975 Orissa Sangeet Natak Akademi Award. She also received the Kalinga Shastriya Sangeet Parishad Award and in 2000, she received the Sangeet Natak Akademi Award. In 2012, the Government of India awarded her the Padma Shri, the fourth-highest civilian award of India.</p>",
                    solution_hi: "<p>47.(c) <strong>ओडिसी ।</strong> मिनाती मिश्रा 1975 के उड़ीसा संगीत नाटक अकादमी पुरस्कार के प्राप्तकर्ता थे। उन्हें कलिंग शास्त्री संगीत परिषद पुरस्कार भी मिला और 2000 में, उन्हें संगीत नाटक अकादमी पुरस्कार मिला। 2012 में, भारत सरकार ने उन्हें भारत के चौथे सर्वोच्च नागरिक पुरस्कार पद्म श्री से भी सम्मानित किया।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "48",
                    section: "18",
                    question_en: "<p>48. How much percentage of oxygen is present in the atmosphere ?&nbsp;</p>",
                    question_hi: "<p>48. वायुमंडल में कितने प्रतिशत ऑक्सीजन मौजूद है ?</p>",
                    options_en: ["<p>39%</p>", "<p>79%</p>", 
                                "<p>10%</p>", "<p>21%</p>"],
                    options_hi: ["<p>39%</p>", "<p>79%</p>",
                                "<p>10%</p>", "<p>21%</p>"],
                    solution_en: "<p>48.(d) <strong>21%.</strong> Percentage of Gases by Volume in Atmosphere: Nitrogen (78.08%), Oxygen (20.95%), Argon (0.93%), Carbon dioxide (0.036%), Neon (0.002%), Helium (0.0005%), Hydrogen (0.00005%), etc. The atmosphere contains a mixture of gases and huge numbers of Aerosols (solid and liquid particles).</p>",
                    solution_hi: "<p>48.(d) <strong>21%। </strong>वायुमंडल में आयतन के अनुसार गैसों का प्रतिशत: नाइट्रोजन (78.08%), ऑक्सीजन (20.95%), आर्गन (0.93%), कार्बन डाइऑक्साइड (0.036%), नियॉन (0.002%), हीलियम (0.0005%) , हाइड्रोजन (0.00005%), आदि। वायुमंडल में गैसों और बड़ी संख्या में एरोसोल (ठोस और तरल कण) का मिश्रण होता है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "49",
                    section: "18",
                    question_en: "<p>49. Which among the following statements is incorrect about Central Processing Unit ?</p>",
                    question_hi: "<p>49. निम्नलिखित में से कौन सा सेंट्रल प्रोसेसिंग यूनिट (CPU) के बारे में गलत है ?</p>",
                    options_en: ["<p>Control Unit controls the order in which instructions enter and leave the processor and how the instructions are executed.</p>", "<p>Arithmetic Logic Unit is responsible for taking the input, converting it into a signal, and storing it for further processing.</p>", 
                                "<p>Arithmetic Logic Unit is also known as the mathematical brain of a computer.</p>", "<p>Control Unit directs and manages operation sequences.</p>"],
                    options_hi: ["<p>कंट्रोल यूनिट उस क्रम को नियंत्रित करती है जिसमें निर्देश डालते है और प्रोसेसर से निकलते और निर्देशों को कैसे निष्पादित किया जाता है।</p>", "<p>अरिथमेटिक लॉजिक यूनिट (ALU) इनपुट लेने इसे संकेत में बदलने और आगे के संचालन के लिए इसे संग्रहीत करने के लिए जिम्मेदार है।</p>",
                                "<p>अरिथमेटिक लॉजिक यूनिट (ALU) को कम्प्यूटर का गणितीय मस्तिष्क भी कहा जाता है।</p>", "<p>कंट्रोल यूनिट संचालन क्रम को निर्देशित और प्रबंधित करती है।</p>"],
                    solution_en: "<p>49.(b)<strong> Arithmetic - logic unit </strong>is the part of a central processing unit that carries out arithmetic and logic operations on the operands in computer instruction words. In some processors, the ALU is divided into two units: an arithmetic unit (AU) and a logic unit (LU). <strong>Control Unit </strong>coordinates the flow of data out of, into, and between the various subunits of a processor.</p>",
                    solution_hi: "<p>49.(b) <strong>अरिथमैटिक - लॉजिक यूनिट </strong>एक सेंट्रल प्रोसेसिंग यूनिट का हिस्सा है जो कंप्यूटर निर्देश शब्दों में ऑपरेंड पर अरिथमैटिक और तर्क संचालन करती है। कुछ प्रोसेसरों में, ALU को दो इकाइयों में विभाजित किया जाता है: एक अरिथमैटिक यूनिट (AU) और एक लॉजिक यूनिट (LU)। <strong>कंट्रोल यूनिट</strong> एक प्रोसेसर के विभिन्न उपइकाइयों के बीच, बाहर और बाहर डेटा के प्रवाह का समन्वय करता है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "50",
                    section: "17",
                    question_en: "<p>50. Chapchar kut festival is a famous festival of _______ state of India.</p>",
                    question_hi: "<p>50. चपचार कुट (Chapchar kut) महोत्सव, भारत के ______राज्य का एक प्रसिद्ध त्योहार है।</p>",
                    options_en: ["<p>Tamil Nadu</p>", "<p>Mizoram</p>", 
                                "<p>Punjab</p>", "<p>Uttar Pradesh</p>"],
                    options_hi: ["<p>तमिलनाडु</p>", "<p>मिजोरम</p>",
                                "<p>पंजाब</p>", "<p>उत्तर प्रदेश</p>"],
                    solution_en: "<p>50.(b) <strong>Mizoram. Chapchar Kut</strong> is celebrated every year in the month of March after the completion of the Jhum Cultivation. It is a spring festival. <strong>Other festivals of Mizoram </strong>- Mim Kut (harvest Festival), Pawl Kut (harvest Festival), Lyuva Khutla (Related to Mara tribes), Anthurium Festival (Related to Paite community), Khuado Kut, Hlukhla Kut.<strong> Tamil Nadu - </strong>Pongal Festival, Natyanjali Dance Festival, Karthigai Deepam, Jallikattu Bull Festival. <strong>Punjab </strong>- Lohri, Baisakhi, Hola Mohalla.</p>",
                    solution_hi: "<p>50.(b) <strong>मिजोरम।</strong> झूम खेती के पूरा होने के बाद हर साल मार्च के महीने में चपचार कुट मनाया जाता है। <strong>मिजोरम के अन्य त्योहार -</strong> मीम कुट (फसल उत्सव), पावल कुट (फसल त्योहार), ल्युवा खुटला (मारा जनजातियों से संबंधित), एन्थ्यूरियम महोत्सव (पाइट समुदाय से संबंधित), खुआडो कुट, ह्लुखला कुट। <strong>तमिलनाडु </strong>- पोंगल महोत्सव, नाट्यांजलि नृत्य महोत्सव, कार्तिगई दीपम, जल्लीकट्टू बैल महोत्सव। <strong>पंजाब </strong>- लोहड़ी, बैसाखी, होला मोहल्ला।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "51",
                    section: "17",
                    question_en: "<p>51. During a division, Pranjla mistakenly took as the dividend a number that was 10% more than the original dividend. He also mistakenly took as the divisor a number that was 25% more than the original divisor. If the correct quotient of the original division problem was 25 and the remainder was 0, what was the quotient that Pranjal obtained, assuming his calculations had no error ?</p>",
                    question_hi: "<p>51. भाग का एक प्रश्न हल करते समय, प्रांजल ने गलती से भाज्य के रूप में एक संख्या ले ली जो मूल भाज्य से 10% अधिक थी। उसने गलती से भाजक के रूप में एक संख्या ले ली जो मूल भाजक से 25% अधिक थी । यदि भाग के मूल प्रश्न का सही भागफल 25 था और शेष 0 था, तो यह मानते हुए कि उसकी गणना में कोई त्रुटि नहीं है, प्रांजल ने कितना भागफल प्राप्त किया ?</p>",
                    options_en: ["<p>21.75</p>", "<p>21.25</p>", 
                                "<p>28.75</p>", "<p>22</p>"],
                    options_hi: ["<p>21.75</p>", "<p>21.25</p>",
                                "<p>28.75</p>", "<p>22</p>"],
                    solution_en: "<p>51.(d) <br>Ratio :-&nbsp; &nbsp; &nbsp;initial : final <br>Divisor :-&nbsp; &nbsp; &nbsp;4&nbsp; &nbsp; :&nbsp; &nbsp; 5<br>Dividend :- 10&nbsp; &nbsp;:&nbsp; &nbsp;11<br>Initial quotient = 25 <br>Initial dividend (10 units) = 25 &times; 4 = 100<br>Then final dividend (11 units) = 110 <br>Final quotient = <math display=\"inline\"><mfrac><mrow><mn>110</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> = 22</p>",
                    solution_hi: "<p>51.(d) <br>अनुपात :-&nbsp; प्रारंभिक : अंतिम<br>भाजक :-&nbsp; &nbsp; &nbsp; &nbsp;4&nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp;5<br>भाज्य :-&nbsp; &nbsp; &nbsp; &nbsp; 10&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; 11<br>प्रारंभिक भागफल = 25<br>प्रारंभिक भाज्य (10 इकाई) = 25 &times; 4 = 100<br>तब अंतिम भाज्य (11 इकाई) = 110<br>अंतिम भागफल = <math display=\"inline\"><mfrac><mrow><mn>110</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> = 22</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "52",
                    section: "17",
                    question_en: "<p>52. ₹4600 was invested at 8% per annum simple interest. If after 5 years that amount is withdrawn and half of the amount is invested in the stock market, then what will be the remaining amount (in ₹) ?</p>",
                    question_hi: "<p>52. 4600 रूपए, 8% वार्षिक साधारण ब्याज दर पर निवेश किये गए | यदि 5 वर्षों बाद वह राशि निकाल ली जाती है और आधी राशि को शेयर बाज़ार में निवेश कर दिया जाता है, तो शेष बची राशि (रू. में) कितनी होगी ?</p>",
                    options_en: ["<p>2880</p>", "<p>4220</p>", 
                                "<p>3220</p>", "<p>3660</p>"],
                    options_hi: ["<p>2880</p>", "<p>4220</p>",
                                "<p>3220</p>", "<p>3660</p>"],
                    solution_en: "<p>52.(c) Simple Interest = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>P</mi><mo>&#215;</mo><mi>R</mi><mo>&#215;</mo><mi>T</mi></mrow><mn>100</mn></mfrac></math> <br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>4600</mn><mo>&#215;</mo><mn>8</mn><mo>&#215;</mo><mn>5</mn></mrow><mn>100</mn></mfrac></math> = 1840<br>Total amount = 4600 + 1840 = 6440<br>Amount invested in share market <br>= <math display=\"inline\"><mfrac><mrow><mn>6440</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> = 3220<br>Remaining amount = 6440 - 3220 = 3220</p>",
                    solution_hi: "<p>52. (c) साधारण ब्याज = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>P</mi><mo>&#215;</mo><mi>R</mi><mo>&#215;</mo><mi>T</mi></mrow><mn>100</mn></mfrac></math> <br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>4600</mn><mo>&#215;</mo><mn>8</mn><mo>&#215;</mo><mn>5</mn></mrow><mn>100</mn></mfrac></math> = 1840<br>मिश्रधन (कुल राशि) = 4600 + 1840 = 6440<br>शेयर बाज़ार में निवेश की गई राशि <br>= <math display=\"inline\"><mfrac><mrow><mn>6440</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> = 3220<br>शेष बची राशि = 6440 - 3220 = 3220</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "53",
                    section: "17",
                    question_en: "<p>53. Find the value of x and y from the given equation:<br>6x + 4y = 16, 16x + 10y = 36.</p>",
                    question_hi: "<p>53. दिए गए समीकरण से x और y का मान ज्ञात करें:<br>6x + 4y = 16, 16x + 10y = 36.</p>",
                    options_en: ["<p>{(4, 10)}</p>", "<p>{(- 2, 5)}</p>", 
                                "<p>{(- 4 , 10)}</p>", "<p>{(1, 3 )}</p>"],
                    options_hi: ["<p>{(4, 10)}</p>", "<p>{(-2, 5)}</p>",
                                "<p>{(-4 , 10)}</p>", "<p>{(1, 3 )}</p>"],
                    solution_en: "<p>53.(c) 6x + 4y = 16 &hellip;&hellip;..(i)<br>16x + 10y = 36 &hellip;&hellip;..(ii)<br>On multiplying equation (i) by 5 and equation (ii) by 2 we get<br>30x + 20y = 80 &hellip;&hellip;.(a)<br>32x + 20y = 72 &hellip;&hellip;..(b)<br>On subtracting (b) from (a) we get<br>-2x = 8 &rArr; x = - 4<br>On putting the value of x in equation (i) we get<br>6 &times; (- 4) + 4y= 16&nbsp;<br>- 24 + 4y = 16 <br>&rArr; 4y = 40<br>&rArr; y = 10</p>",
                    solution_hi: "<p>53.(c)<br>6x + 4y = 16 &hellip;&hellip;..(i)<br>16x + 10y = 36 &hellip;&hellip;..(ii)<br>समीकरण (i) को 5 से और समीकरण (ii) को 2 से गुणा करने पर हमें प्राप्त होता है<br>30x + 20y = 80 &hellip;&hellip;.(a)<br>32x + 20y = 72 &hellip;&hellip;..(b)<br>(a) में से (b) घटाने पर हमें प्राप्त होता है<br>- 2x = 8 &rArr; x = - 4<br>समीकरण (i) में x का मान रखने पर हमें प्राप्त होता है<br>6 &times; (- 4) + 4y= 16&nbsp;<br>- 24 + 4y = 16 <br>&rArr; 4y = 40<br>&rArr; y = 10</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "54",
                    section: "17",
                    question_en: "<p>54. Find the least number of square tiles required to completely pave the floor of a room of 15 m 17 cm long and 9 m 2 cm broad.</p>",
                    question_hi: "<p>54. 15 मीटर 17 सेमी लंबे और 9 मीटर 2 सेमी चौड़े एक कमरे के फर्श को पक्का करने के लिए आवश्यक वर्गाकार टाइलों की न्यूनतम संख्या ज्ञात कीजिए।</p>",
                    options_en: ["<p>824</p>", "<p>784</p>", 
                                "<p>764</p>", "<p>814</p>"],
                    options_hi: ["<p>824</p>", "<p>784</p>",
                                "<p>764</p>", "<p>814</p>"],
                    solution_en: "<p>54.(d) Length of floor = 15 Meter 17 cm = 1517 cm<br>Width of floor = 9 meter 2 cm = 902 cm<br>Now, length of largest tiles <br>= HCF of (1517, 902) = 41<br>Area of largest tiles = 41 &times; 41 cm<sup>2</sup><br>Total area of floor = 1517 &times; 902 cm<sup>2</sup><br>Required minimum tiles <br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1517</mn><mo>&#215;</mo><mn>902</mn></mrow><mrow><mn>41</mn><mo>&#215;</mo><mn>41</mn></mrow></mfrac></math> = 37 &times; 22 = 814</p>",
                    solution_hi: "<p>54.(d) फर्श की लंबाई = 15 मीटर 17 सेमी = 1517 सेमी<br>फर्श की चौड़ाई = 9 मीटर 2 सेमी = 902 सेमी<br>अब, सबसे बड़ी टाइलों की लंबाई = (1517, 902) का HCF = 41<br>सबसे बड़ी टाइल्स का क्षेत्रफल = 41 &times; 41 सेमी<sup>2</sup><br>फर्श का कुल क्षेत्रफल = 1517 &times; 902 सेमी<sup>2</sup><br>आवश्यक न्यूनतम टाइलें <br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1517</mn><mo>&#215;</mo><mn>902</mn></mrow><mrow><mn>41</mn><mo>&#215;</mo><mn>41</mn></mrow></mfrac></math> = 37 &times; 22 = 814</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "55",
                    section: "17",
                    question_en: "<p>55. A train starts at 3:00 pm from Mumbai and moves towards Ahmedabad at the speed of 20 km/hr. Another train starts from Ahmedabad at 8 pm and moves towards Mumbai at the speed of 60 km/hr. If the distance between Mumbai and Ahmedabad is 600 km, then at what time both the trains will meet ?</p>",
                    question_hi: "<p>55. एक रेलगाड़ी मुंबई से 3:00 pm पर निकलती है और 20 km/hr की चाल से अहमदाबाद की ओर चलती है। एक दूसरी रेलगाड़ी अहमदाबाद से 8pm पर निकलती है और 60 km/hr की चाल से मुंबई की ओर चलती है। यदि मुंबई और अहमदाबाद के बीच की दूरी 600 km है, तो दोनों रेलगाड़ियाँ किस समय मिलेंगी ?</p>",
                    options_en: ["<p>1:30 am</p>", "<p>12:30 am</p>", 
                                "<p>2:00 am</p>", "<p>2:15 am</p>"],
                    options_hi: ["<p>1:30 am</p>", "<p>12:30 am</p>",
                                "<p>2:00 am</p>", "<p>2:15 am</p>"],
                    solution_en: "<p>55.(d) Distance covered by train A in 5 hours = 5 &times; 20 = 100 km<br>Remaining distance = 600 -&nbsp;100 = 500 km<br>Remaining distance will be covered by both trains.<br>Time = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>500</mn><mrow><mn>20</mn><mo>+</mo><mn>60</mn></mrow></mfrac></math> = 6 hours 15 min.<br>Meeting time = 3 : 00 pm + 5 hours + 6 hours 15 min. = 2 : 15 am</p>",
                    solution_hi: "<p>55.(d) ट्रेन A द्वारा 5 घंटे में तय की गई दूरी = 5 &times; 20 = 100 km<br>शेष दूरी = 600 -&nbsp;100 = 500 km<br>शेष दूरी दोनों ट्रेनों द्वारा तय की जाएगी <br>समय = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>500</mn><mrow><mn>20</mn><mo>+</mo><mn>60</mn></mrow></mfrac></math>&nbsp;= 6 घंटे 15 मिनट।<br>मिलने का समय = 3 : 00 pm + 5 घंटे + 6 घंटे 15 मिनट = 2 : 15 am</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "56",
                    section: "17",
                    question_en: "<p>56. If sin&theta; cos&theta; = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msqrt><mn>3</mn></msqrt></mfrac></math> then the value of (sin<sup>4</sup>&theta; + cos<sup>4</sup>&theta;) is:</p>",
                    question_hi: "<p>56. यदि sin&theta; cos&theta; = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msqrt><mn>3</mn></msqrt></mfrac></math> है, तो (sin<sup>4</sup>&theta; + cos<sup>4</sup>&theta;) का मान क्या है ?</p>",
                    options_en: ["<p>1</p>", "<p><math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>", 
                                "<p><math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>"],
                    options_hi: ["<p>1</p>", "<p><math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>",
                                "<p><math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>"],
                    solution_en: "<p>56.(d) According to the question,<br>(sin<sup>4</sup>&theta; + cos<sup>4</sup>&theta;)&nbsp;= (sin<sup>2</sup>&theta; + cos<sup>2</sup>&theta;)<sup>2 </sup>- 2sin<sup>2</sup>&theta; .cos<sup>2</sup>&theta;<br>We know that (sin<sup>2</sup>&theta; + cos<sup>2</sup>&theta;) = 1<br>1<sup>2 </sup>- 2 &times; (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msqrt><mn>3</mn></msqrt></mfrac></math>)<sup>2</sup> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>3</mn></mfrac></math></p>",
                    solution_hi: "<p>56.(d) प्रश्न के अनुसार,<br>(sin<sup>4</sup>&theta; + cos<sup>4</sup>&theta;)&nbsp;= (sin<sup>2</sup>&theta; + cos<sup>2</sup>&theta;)<sup>2 </sup>- 2sin<sup>2</sup>&theta; .cos<sup>2</sup>&theta;<br>हम जानते हैं कि (sin<sup>2</sup>&theta; + cos<sup>2</sup>&theta;) = 1<br>1<sup>2 </sup>- 2 &times; (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msqrt><mn>3</mn></msqrt></mfrac></math>)<sup>2</sup> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>3</mn></mfrac></math></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "57",
                    section: "17",
                    question_en: "<p>57. The digits of a two-digit number are in the ratio of 3 : 2 and the number obtained by interchanging the digits is lesser than the original number by 27. What is the original number ?</p>",
                    question_hi: "<p>57. दो अंकों की संख्या के अंक 3 : 2 के अनुपात में हैं और अंकों को आपस में बदलने पर प्राप्त संख्या मूल संख्या से 27 कम है। मूल संख्या क्या है ?</p>",
                    options_en: ["<p>63</p>", "<p>48</p>", 
                                "<p>96</p>", "<p>69</p>"],
                    options_hi: ["<p>63</p>", "<p>48</p>",
                                "<p>96</p>", "<p>69</p>"],
                    solution_en: "<p>57.(c) Lets the digit at tens and ones place be 3x and 2x<br>So, original number = 3x &times; 10 + 2x<br>After interchanging number will be <br>= 2x &times; 10 + 3x<br>According to question <br>[3x &times; 10 + 2x] - [2x &times; 10 + 3x] = 27<br>9x = 27<br>&nbsp;x = 3<br>So, original number = 3x &times; 10 + 2x = 96</p>",
                    solution_hi: "<p>57.(c) माना दहाई और इकाई के स्थान पर अंक 3x और 2x हैं<br>तो, मूल संख्या = 3x &times; 10 + 2x <br>परस्पर बदलने के बाद संख्या = 2x &times; 10 + 3x <br>होगी<br>प्रश्न के अनुसार ,<br>[3x &times; 10 + 2x] - [2x &times; 10 + 3x] = 27<br>9x = 27<br>&nbsp;x = 3<br>अत: मूल संख्या = 3x &times; 10 + 2x = 96</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "58",
                    section: "17",
                    question_en: "<p>58. AB is a chord in a circle with centre O. AB is produced to C such that BC is equal to the radius of the circle. C is joined to O and produced to meet the circle at D. If &ang;ACD = 32&deg;, then the measure of &ang;AOD is ______ .</p>",
                    question_hi: "<p>58. AB केंद्र O वाले वृत्त की एक जीवा है। AB को C तक इस प्रकार बढ़ाया जाता है कि BC वृत्त की त्रिज्या के बराबर हो। C को O से जोड़ा जाता है और O को वृत्त पर D से मिलने के लिए बढ़ाया जाता है। यदि &ang;ACD = 32&deg; है, तो &ang;AOD का माप ______है।</p>",
                    options_en: ["<p>48&deg;</p>", "<p>108&deg;</p>", 
                                "<p>80&deg;</p>", "<p>96&deg;</p>"],
                    options_hi: ["<p>48&deg;</p>", "<p>108&deg;</p>",
                                "<p>80&deg;</p>", "<p>96&deg;</p>"],
                    solution_en: "<p>58.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742789953877.png\" alt=\"rId59\" width=\"178\" height=\"92\"><br>OB = r = BC<br>&ang;ACD = 32&deg;,<br>&ang;ACD = &ang;BOC = 32&deg;<br><strong id=\"docs-internal-guid-49028539-7fff-44c1-12eb-bfbcb2180dad\"></strong>Thus, &ang;ABO = 64&deg; (exterior angle property)<br>&ang;ABO = &ang;OAB = 64&deg;<br>&ang;AOD = &ang;ACO + &ang;OAC = 32&deg; + 64&deg;<br>= 96&deg;</p>",
                    solution_hi: "<p>58.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742789953877.png\" alt=\"rId59\" width=\"178\" height=\"92\"><br>OB = r = BC<br>&ang;ACD = 32&deg;,<br>&ang;ACD = &ang;BOC = 32&deg;<br>इस प्रकार,&ang;ABO = 64&deg; (बाहरी कोण से,)<br>&ang;ABO = &ang;OAB = 64&deg;<br>&ang;AOD = &ang;ACO + &ang;OAC = 32&deg; + 64&deg;<br>= 96&deg;</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "59",
                    section: "17",
                    question_en: "<p>59. An alloy contains 40% of silver, 30% of copper and 30% of nickel. How much silver (in kg) should be added to 25 kg of the alloy so that the new alloy&nbsp;contains 50% of silver ?</p>",
                    question_hi: "<p>59. एक मिश्र धातु में 40% चांदी, 30% तांबा और 30% निकल होता है। 25 kg मिश्र धातु में कितनी चांदी (kg में) मिलायी जानी चाहिए ताकि नए मिश्र धातु में 50% चांदी हो ?</p>",
                    options_en: ["<p>5</p>", "<p>10</p>", 
                                "<p>12</p>", "<p>20</p>"],
                    options_hi: ["<p>5</p>", "<p>10</p>",
                                "<p>12</p>", "<p>20</p>"],
                    solution_en: "<p>59.(a) Let the quantit<math display=\"inline\"><mi>y</mi></math> of silver to be added be y<br>According to the question,<br>&rArr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>25</mn><mo>&#215;</mo><mstyle displaystyle=\"true\"><mfrac><mn>40</mn><mn>100</mn></mfrac></mstyle><mo>)</mo><mo>+</mo><mi>y</mi></mrow><mrow><mn>25</mn><mo>+</mo><mi>y</mi></mrow></mfrac></math> &times; 100 = 50<br>&rArr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>10</mn><mo>+</mo><mi>y</mi></mrow><mrow><mn>25</mn><mo>+</mo><mi>y</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math> <br>&rArr; 20 + 2y = 25 + y<br>&rArr; y = 5 kg</p>",
                    solution_hi: "<p>59.(a) <br>माना चांदी की मात्रा y(kg) मिलाई जाती है<br>प्रश्न के अनुसार,<br>&rArr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>25</mn><mo>&#215;</mo><mstyle displaystyle=\"true\"><mfrac><mn>40</mn><mn>100</mn></mfrac></mstyle><mo>)</mo><mo>+</mo><mi>y</mi></mrow><mrow><mn>25</mn><mo>+</mo><mi>y</mi></mrow></mfrac></math> &times; 100 = 50<br>&rArr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>10</mn><mo>+</mo><mi>y</mi></mrow><mrow><mn>25</mn><mo>+</mo><mi>y</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math> <br>&rArr; 20 + 2y = 25 + y<br>&rArr; y = 5 kg</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "60",
                    section: "17",
                    question_en: "<p>60. From the given diagram, determine the difference between the total number of cars sold in the first three years and in the last three years.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742789954282.png\" alt=\"rId61\" width=\"382\" height=\"273\"></p>",
                    question_hi: "<p>60. दिए गए आरेख से, पहले तीन वर्षों में और अगले तीन वर्षों में बेची गई कारों की कुल संख्या के बीच का अंतर ज्ञात कीजिये ?<br>2013 - 2018 अवधि के दौरान किसी कंपनी की कार की बिक्री<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742789954477.png\" alt=\"rId62\" width=\"398\" height=\"302\"></p>",
                    options_en: ["<p>1200</p>", "<p>200</p>", 
                                "<p>700</p>", "<p>150</p>"],
                    options_hi: ["<p>1200</p>", "<p>200</p>",
                                "<p>700</p>", "<p>150</p>"],
                    solution_en: "<p>60.(b) The total number of cars sold in the first three years<br>= (150 + 100 + 250) = 500<br>and in the last three years = (100 + 300 + 300) = 700<br>So, the difference between the total number of cars sold in the first three years and in the last three years&nbsp;= 700 - 500 = 200.</p>",
                    solution_hi: "<p>60.(b) पहले तीन वर्षों में बेची गई कारों की कुल संख्या = (150 + 100 + 250) = 500<br>और अगले तीन वर्षों में = (100 + 300 + 300) = 700<br>तो, पहले तीन वर्षों में और अगले तीन वर्षों में बेची गई कारों की कुल संख्या के बीच का अंतर <br>= 700 - 500 = 200</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "61",
                    section: "17",
                    question_en: "<p>61. Rani can do a work in 10 days, Priya can do the same work in 15 days and Guddu can do the same work in 12 days. If they do that work together and they are paid Rs. 9000, then what is the share of Priya ?</p>",
                    question_hi: "<p>61. रानी एक काम को 10 दिन में कर सकती है, प्रिया उसी काम को 15 दिन में कर सकती है और गुड्डू उसी काम को 12 दिन में कर सकता है। अगर वे उस काम को एक साथ करते हैं और उन्हें Rs. 9000 का भुगतान किया जाता है, तो प्रिया का हिस्सा कितना है ?</p>",
                    options_en: ["<p>Rs.2400</p>", "<p>Rs.3000</p>", 
                                "<p>Rs.3500</p>", "<p>Rs.3600</p>"],
                    options_hi: ["<p>Rs.2400</p>", "<p>Rs.3000</p>",
                                "<p>Rs.3500</p>", "<p>Rs.3600</p>"],
                    solution_en: "<p>61.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742789954669.png\" alt=\"rId63\" width=\"221\" height=\"113\"><br>Share of priya in the money = 9000 &times; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mrow><mo>(</mo><mn>6</mn><mo>+</mo><mn>4</mn><mo>+</mo><mn>5</mn><mo>)</mo></mrow></mfrac></math> = 2400 Rs.</p>",
                    solution_hi: "<p>61.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742789954856.png\" alt=\"rId64\" width=\"206\" height=\"117\"><br>रुपयों में प्रिया का हिस्सा = 9000 &times; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mrow><mo>(</mo><mn>6</mn><mo>+</mo><mn>4</mn><mo>+</mo><mn>5</mn><mo>)</mo></mrow></mfrac></math>&nbsp;= 2400 Rs.</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "62",
                    section: "17",
                    question_en: "<p>62. The perimeter of a triangle with sides of integer values are equal to 13.<br>How many such triangles are possible ?</p>",
                    question_hi: "<p>62. पूर्णांक मानों की भुजाओं वाले त्रिभुज का परिमाप 13 के बराबर है। ऐसे कितने त्रिभुज बनना संभव हैं ?</p>",
                    options_en: ["<p>5</p>", "<p>8</p>", 
                                "<p>7</p>", "<p>6</p>"],
                    options_hi: ["<p>5</p>", "<p>8</p>",
                                "<p>7</p>", "<p>6</p>"],
                    solution_en: "<p>62.(a) <br>Let, a, b and c be the sides of a triangle <br>Then :- (a + b) &gt; c &gt; (a - b)<br>Perimeter of the triangle is equal to 13.<br>Possible triangle are five having sides (1, 6, 6), (2, 5, 6), (3, 5, 5), (3, 4, 6) and (4, 4, 5)</p>",
                    solution_hi: "<p>62.(a) <br>मान लीजिए, a, b और c एक त्रिभुज की भुजाएँ हैं<br>तब:- (a + b) &gt; c &gt; (a - b)<br>त्रिभुज का परिमाप 13 के बराबर है।<br>संभावित पाँच त्रिभुज की भुजाएँ हैं: <br>(1, 6, 6), (2, 5, 6), (3, 5, 5), (3, 4, 6) और (4, 4, 5)</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "63",
                    section: "17",
                    question_en: "<p>63. X can do a certain work in 40 hours. Y is 25% more efficient than X, and Z is 28% more efficient than Y. Working together, X,Y and Z will complete the same work in:</p>",
                    question_hi: "<p>63. X एक निश्चित कार्य को 40 घंटे में कर सकता है। Y, X से 25% अधिक कुशल है, और Z, Y से 28% अधिक कुशल है। एक साथ कार्य करने पर, X, Y और Z समान कार्य को कितने समय में पूरा करेंगे:</p>",
                    options_en: ["<p><math display=\"inline\"><mfrac><mrow><mn>800</mn></mrow><mrow><mn>77</mn></mrow></mfrac></math> hours</p>", "<p><math display=\"inline\"><mfrac><mrow><mn>160</mn></mrow><mrow><mn>9</mn></mrow></mfrac></math> hours</p>", 
                                "<p><math display=\"inline\"><mfrac><mrow><mn>400</mn></mrow><mrow><mn>11</mn></mrow></mfrac></math> hours</p>", "<p><math display=\"inline\"><mfrac><mrow><mn>200</mn></mrow><mrow><mn>13</mn></mrow></mfrac></math> hours</p>"],
                    options_hi: ["<p><math display=\"inline\"><mfrac><mrow><mn>800</mn></mrow><mrow><mn>77</mn></mrow></mfrac></math> घंटे</p>", "<p><math display=\"inline\"><mfrac><mrow><mn>160</mn></mrow><mrow><mn>9</mn></mrow></mfrac><mi>&#160;</mi></math>घंटे</p>",
                                "<p><math display=\"inline\"><mfrac><mrow><mn>400</mn></mrow><mrow><mn>11</mn></mrow></mfrac></math> घंटे</p>", "<p><math display=\"inline\"><mfrac><mrow><mn>200</mn></mrow><mrow><mn>13</mn></mrow></mfrac></math> घंटे</p>"],
                    solution_en: "<p>63.(a) Y = <math display=\"inline\"><mfrac><mrow><mn>125</mn></mrow><mrow><mi>&#160;</mi><mn>100</mn></mrow></mfrac></math> X so, X : Y = 4 : 5<br>Z = <math display=\"inline\"><mfrac><mrow><mn>128</mn></mrow><mrow><mi>&#160;</mi><mn>100</mn></mrow></mfrac></math> Y so, Y : Z = 25 : 32<br>On balancing above ratios, we get the Efficiency of X : Y : Z = 20 : 25 : 32<br>Total work = 20a &times;&nbsp;40 = 800a<br>Number of hours to complete the work&nbsp;together = <math display=\"inline\"><mfrac><mrow><mn>800</mn><mi>a</mi></mrow><mrow><mi>&#160;</mi><mn>77</mn><mi>a</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>800</mn><mn>77</mn></mfrac></math> hours</p>",
                    solution_hi: "<p>63.(a)<br>Y = <math display=\"inline\"><mfrac><mrow><mn>125</mn></mrow><mrow><mi>&#160;</mi><mn>100</mn></mrow></mfrac></math> X तो, X : Y = 4 : 5<br>Z = <math display=\"inline\"><mfrac><mrow><mn>128</mn></mrow><mrow><mi>&#160;</mi><mn>100</mn></mrow></mfrac></math> Y तो, Y : Z = 25 : 32<br>उपरोक्त अनुपातों को संतुलित करने पर, हमें <br>X : Y : Z = 20 : 25 : 32 प्राप्त होता है<br>कुल कार्य = 20a &times; 40 = 800a<br>एक साथ कार्य को पूरा करने के लिए घंटों की संख्या = <math display=\"inline\"><mfrac><mrow><mn>800</mn><mi>a</mi></mrow><mrow><mi>&#160;</mi><mn>77</mn><mi>a</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>800</mn><mn>77</mn></mfrac></math> घंटे</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "64",
                    section: "17",
                    question_en: "<p>64. At the same time A and B start moving towards each other from two different places, 240 km apart. The ratio of the speeds of A and B is 5 : 7, and the speed of B is 84 km/h . After how many minutes will A and B meet each other ?</p>",
                    question_hi: "<p>64. एक ही समय पर A और B, 240 किमी दूर दो अलग-अलग स्थानों से एक दूसरे की ओर चलना शुरू करते हैं। A और B की गति का अनुपात 5 : 7 है, और B की गति 84 किमी/घंटा है। कितने मिनट बाद A और B एक दूसरे से मिलेंगे ?</p>",
                    options_en: ["<p>80 minutes</p>", "<p>100 minutes</p>", 
                                "<p>120 minutes</p>", "<p>90 minutes</p>"],
                    options_hi: ["<p>80 मिनट</p>", "<p>100 मिनट</p>",
                                "<p>120 मिनट</p>", "<p>90 मिनट</p>"],
                    solution_en: "<p>64.(b) Distance = 240 km<br>Speed ratio of A and B = 5 : 7<br>As per question,<br>7 unit = 84 km/h&nbsp;<br>5 unit = 60 km/h<br>Speed of A = 60 km/h<br>Relative speed = 84 + 60 = 144 km/h<br>Required time = <math display=\"inline\"><mfrac><mrow><mn>240</mn></mrow><mrow><mn>144</mn></mrow></mfrac></math> <br>= <math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> hours or 100 min</p>",
                    solution_hi: "<p>64.(b) दूरी = 240 किमी<br>A और B का गति अनुपात = 5 : 7<br>प्रश्न के अनुसार,<br>7 इकाई = 84 किमी/घंटा<br>5 इकाई = 60 किमी/घंटा<br>A की गति = 60 किमी/घंटा<br>सापेक्ष गति = 84 + 60 = 144 किमी/घंटा<br>आवश्यक समय = <math display=\"inline\"><mfrac><mrow><mn>240</mn></mrow><mrow><mn>144</mn></mrow></mfrac></math> <br>= <math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> घंटे या 100 मिनट</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "65",
                    section: "17",
                    question_en: "<p>65. Simplify: (a<sup>-1</sup> + b<sup>-1</sup>) &divide; (a<sup>-3</sup> + b<sup>-3</sup>)</p>",
                    question_hi: "<p>65. सरल करें (a<sup>-1</sup> + b<sup>-1</sup>) &divide; (a<sup>-3</sup> + b<sup>-3</sup>)</p>",
                    options_en: ["<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>a</mi><mi>b</mi></mrow><mrow><msup><mi>a</mi><mn>2</mn></msup><mo>-</mo><mi>a</mi><mi>b</mi><mo>+</mo><msup><mi>b</mi><mn>2</mn></msup></mrow></mfrac></math></p>", "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi>a</mi><mn>3</mn></msup><msup><mi>b</mi><mn>3</mn></msup></mrow><mrow><msup><mi>a</mi><mn>2</mn></msup><mo>-</mo><mi>a</mi><mi>b</mi><mo>+</mo><msup><mi>b</mi><mn>2</mn></msup></mrow></mfrac></math></p>", 
                                "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi>a</mi><mn>2</mn></msup><msup><mi>b</mi><mn>2</mn></msup></mrow><mrow><msup><mi>a</mi><mn>2</mn></msup><mo>+</mo><mi>a</mi><mi>b</mi><mo>+</mo><msup><mi>b</mi><mn>2</mn></msup></mrow></mfrac></math></p>", "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi>a</mi><mn>2</mn></msup><msup><mi>b</mi><mn>2</mn></msup></mrow><mrow><msup><mi>a</mi><mn>2</mn></msup><mo>-</mo><mi>a</mi><mi>b</mi><mo>+</mo><msup><mi>b</mi><mn>2</mn></msup></mrow></mfrac></math></p>"],
                    options_hi: ["<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>a</mi><mi>b</mi></mrow><mrow><msup><mi>a</mi><mn>2</mn></msup><mo>-</mo><mi>a</mi><mi>b</mi><mo>+</mo><msup><mi>b</mi><mn>2</mn></msup></mrow></mfrac></math></p>", "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi>a</mi><mn>3</mn></msup><msup><mi>b</mi><mn>3</mn></msup></mrow><mrow><msup><mi>a</mi><mn>2</mn></msup><mo>-</mo><mi>a</mi><mi>b</mi><mo>+</mo><msup><mi>b</mi><mn>2</mn></msup></mrow></mfrac></math></p>",
                                "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi>a</mi><mn>2</mn></msup><msup><mi>b</mi><mn>2</mn></msup></mrow><mrow><msup><mi>a</mi><mn>2</mn></msup><mo>+</mo><mi>a</mi><mi>b</mi><mo>+</mo><msup><mi>b</mi><mn>2</mn></msup></mrow></mfrac></math></p>", "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi>a</mi><mn>2</mn></msup><msup><mi>b</mi><mn>2</mn></msup></mrow><mrow><msup><mi>a</mi><mn>2</mn></msup><mo>-</mo><mi>a</mi><mi>b</mi><mo>+</mo><msup><mi>b</mi><mn>2</mn></msup></mrow></mfrac></math></p>"],
                    solution_en: "<p>65.(d) (a<sup>-1</sup> + b<sup>-1</sup>) &divide; (a<sup>-3</sup> + b<sup>-3</sup>)<br>= (<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi>a</mi></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi>b</mi></mfrac></math>) &divide; (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msup><mi>a</mi><mn>3</mn></msup></mfrac></math> +<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msup><mi>b</mi><mn>3</mn></msup></mfrac></math>)<br>= (<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>b</mi><mo>+</mo><mi>a</mi></mrow><mrow><mi>a</mi><mi>b</mi></mrow></mfrac></math>) &divide; (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi>b</mi><mn>3</mn></msup><mo>+</mo><msup><mi>a</mi><mn>3</mn></msup></mrow><mrow><msup><mi>a</mi><mn>3</mn></msup><msup><mi>b</mi><mn>3</mn></msup></mrow></mfrac></math>)<br>= (<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>b</mi><mo>+</mo><mi>a</mi></mrow><mrow><mi>a</mi><mi>b</mi></mrow></mfrac></math>) &times; (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi>a</mi><mn>3</mn></msup><msup><mi>b</mi><mn>3</mn></msup></mrow><mrow><msup><mi>b</mi><mn>3</mn></msup><mo>+</mo><msup><mi>a</mi><mn>3</mn></msup></mrow></mfrac></math>)<br>=&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi>a</mi><mn>2</mn></msup><msup><mi>b</mi><mn>2</mn></msup></mrow><mrow><msup><mi>a</mi><mn>2</mn></msup><mo>-</mo><mi>a</mi><mi>b</mi><mo>+</mo><msup><mi>b</mi><mn>2</mn></msup></mrow></mfrac></math></p>",
                    solution_hi: "<p>65.(d) (a<sup>-1</sup> + b<sup>-1</sup>) &divide; (a<sup>-3</sup> + b<sup>-3</sup>)<br>= (<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi>a</mi></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi>b</mi></mfrac></math>) &divide; (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msup><mi>a</mi><mn>3</mn></msup></mfrac></math> +<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msup><mi>b</mi><mn>3</mn></msup></mfrac></math>)<br>= (<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>b</mi><mo>+</mo><mi>a</mi></mrow><mrow><mi>a</mi><mi>b</mi></mrow></mfrac></math>) &divide; (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi>b</mi><mn>3</mn></msup><mo>+</mo><msup><mi>a</mi><mn>3</mn></msup></mrow><mrow><msup><mi>a</mi><mn>3</mn></msup><msup><mi>b</mi><mn>3</mn></msup></mrow></mfrac></math>)<br>= (<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>b</mi><mo>+</mo><mi>a</mi></mrow><mrow><mi>a</mi><mi>b</mi></mrow></mfrac></math>) &times; (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi>a</mi><mn>3</mn></msup><msup><mi>b</mi><mn>3</mn></msup></mrow><mrow><msup><mi>b</mi><mn>3</mn></msup><mo>+</mo><msup><mi>a</mi><mn>3</mn></msup></mrow></mfrac></math>)<br>=&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi>a</mi><mn>2</mn></msup><msup><mi>b</mi><mn>2</mn></msup></mrow><mrow><msup><mi>a</mi><mn>2</mn></msup><mo>-</mo><mi>a</mi><mi>b</mi><mo>+</mo><msup><mi>b</mi><mn>2</mn></msup></mrow></mfrac></math></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "66",
                    section: "17",
                    question_en: "<p>66. A trader sells an item for ₹805 after offering a discount of 12.5% on its marked price. Had he not offered the discount, he would have earned a profit of 15%. What is the cost price of the item ?</p>",
                    question_hi: "<p>66. किसी वस्तु के अंकित मूल्य पर 12.5% की छूट देते हुए कोई दुकानदार इसे ₹805 में बेचता है। यदि उसने यह छूट ना दी होती तो उसे इस पर 15% का लाभ प्राप्त होता । वस्तु का क्रय मूल्य (₹ में) कितना है ?</p>",
                    options_en: ["<p>780</p>", "<p>700</p>", 
                                "<p>800</p>", "<p>750</p>"],
                    options_hi: ["<p>780</p>", "<p>700</p>",
                                "<p>800</p>", "<p>750</p>"],
                    solution_en: "<p>66.(c)<br>MP of an article = 805 &times; <math display=\"inline\"><mfrac><mrow><mn>8</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> = ₹920<br>If no discount offered on an article then, MP = SP = ₹920<br>So, CP of an article to gain 15% = 920 &times; <math display=\"inline\"><mfrac><mrow><mn>20</mn></mrow><mrow><mn>23</mn></mrow></mfrac></math> = ₹800</p>",
                    solution_hi: "<p>66.(c)<br>वस्तु का अंकित मूल्य&nbsp; = 805 &times; <math display=\"inline\"><mfrac><mrow><mn>8</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> = ₹920<br>यदि किसी वस्तु पर कोई छूट नहीं दी जाती है तो, अंकित मूल्य = विक्रय मूल्य&nbsp; = ₹920<br>अत: 15% लाभ प्राप्त करने के लिए क्रय मूल्य&nbsp; = 920 &times; <math display=\"inline\"><mfrac><mrow><mn>20</mn></mrow><mrow><mn>23</mn></mrow></mfrac></math> = ₹800</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "67",
                    section: "17",
                    question_en: "<p>67. A semicircle has been drawn on the length of a rectangle. The area of the shaded region in the figure is:<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742789955072.png\" alt=\"rId65\" width=\"160\" height=\"95\"></p>",
                    question_hi: "<p>67. एक आयत की लंबाई पर एक अर्धवृत्त खींचा गया है। आकृति में छायांकित क्षेत्र का क्षेत्रफल है:<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742789955072.png\" alt=\"rId65\" width=\"160\" height=\"95\"></p>",
                    options_en: ["<p>129 cm<sup>2</sup></p>", "<p>63 cm<sup>2</sup></p>", 
                                "<p>14 cm<sup>2</sup></p>", "<p>77 cm<sup>2</sup></p>"],
                    options_hi: ["<p>129 cm&sup2;</p>", "<p>63 cm&sup2;</p>",
                                "<p>14 cm&sup2;</p>", "<p>77 cm&sup2;</p>"],
                    solution_en: "<p>67.(b)<br>Radius of the semicircle = 7 cm<br>Area of the shaded region = Area of the rectangle - Area of the semicircle<br>= 14 &times; 10 - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>22</mn><mn>7</mn></mfrac></math> &times; 7 &times; 7<br>= 140 - 77 = 63 cm<sup>2</sup></p>",
                    solution_hi: "<p>67.(b) अर्धवृत्त की त्रिज्या = 7 cm<br>छायांकित क्षेत्र का क्षेत्रफल = आयत का क्षेत्रफल - अर्धवृत्त का क्षेत्रफल <br>= 14 &times; 10 - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>22</mn><mn>7</mn></mfrac></math> &times; 7 &times; 7<br>= 140 - 77 = 63 cm<sup>2</sup></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "68",
                    section: "17",
                    question_en: "<p>68. Marked price of an article is ₹ 28000. It can be sold at one of the following ways: Way A: A single discount of 24 percent. Way B: Two successive discounts of 16 percent and 10 percent. Which way will have lowest selling price&nbsp;and what will be the value of it ?</p>",
                    question_hi: "<p>68. एक वस्तु का अंकित मूल्य ₹ 28000 है। इसे निम्नलिखित में से किसी एक तरीके से बेचा जा सकता है: तरीका A : 24 प्रतिशत की एकल छूट। तरीका B : 16 प्रतिशत और 10 प्रतिशत की लगातार दो क्रमिक छूटों पर , कौन से तरीके में विक्रय मूल्य न्यूनतम होगा तथा उसका मान क्या है ?</p>",
                    options_en: ["<p>Way A, <math display=\"inline\"><mi>&#8377;</mi></math> 21168</p>", "<p>Way B, ₹ 21280</p>", 
                                "<p>Way A, ₹ 21280</p>", "<p>Way B,₹ 21168</p>"],
                    options_hi: ["<p>तरीका A ₹ 21168</p>", "<p>तरीका B, ₹ 21280</p>",
                                "<p>तरीका A, ₹ 21280</p>", "<p>तरीका B,₹ 21168</p>"],
                    solution_en: "<p>68.(d)<br>For Way A : SP = 28000 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>100</mn><mo>-</mo><mn>24</mn></mrow><mn>100</mn></mfrac></math> <br>= 28000 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>&#160;</mo><mn>76</mn><mo>&#160;</mo></mrow><mn>100</mn></mfrac></math> = ₹21,280 <br>For Way B : SP = <br>28000 &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>100</mn><mo>-</mo><mn>16</mn></mrow><mn>100</mn></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>100</mn><mo>-</mo><mn>10</mn></mrow><mn>100</mn></mfrac></math> <br>= 28000 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>84</mn><mn>100</mn></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>90</mn><mn>100</mn></mfrac></math> = ₹21,168<br>Clearly, we can see that Way B has lowest SP and it is equal to ₹ 21,168.<br>So, option (d) is the correct answer.</p>",
                    solution_hi: "<p>68.(d)<br>तरीका A के लिए : विक्रय मूल्य = 28000 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>100</mn><mo>-</mo><mn>24</mn></mrow><mn>100</mn></mfrac></math> <br>= 28000 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>&#160;</mo><mn>76</mn><mo>&#160;</mo></mrow><mn>100</mn></mfrac></math> = ₹21,280<br>तरीका B के लिए : विक्रय मूल्य<br>= 28000 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>100</mn><mo>-</mo><mn>16</mn></mrow><mn>100</mn></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>100</mn><mo>-</mo><mn>10</mn></mrow><mn>100</mn></mfrac></math> <br>= 28000 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>84</mn><mn>100</mn></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>90</mn><mn>100</mn></mfrac></math> = ₹21,168<br>स्पष्ट रूप से, हम देख सकते हैं कि तरीका B का&nbsp;विक्रय मूल्य सबसे कम है और यह ₹ 21,168 के बराबर है। तो, विकल्प (d) सही उत्तर है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "69",
                    section: "17",
                    question_en: "<p>69. A boat has speed of 15 m/s in still water, but while going upstream its speed is half the speed as compared to the speed of the boat going downstream. A ship while going downstream crosses the boat in 15 seconds. The length of the ship is double the length of the boat. Find the time the ship will take to cover a distance of 144 kilometres while going upstream if the length of the ship is 100 metres.</p>",
                    question_hi: "<p>69. शांत पानी में एक नाव की गति 15 मीटर/सेकेंड है, लेकिन धारा के प्रतिकूल जाते समय इसकी गति धारा के अनुकूल जाने वाली नाव की गति की तुलना में आधी है। एक जहाज धारा की दिशा में जाते समय नाव को 15 सेकंड में पार कर जाता है। जहाज की लंबाई नाव की लंबाई से दोगुनी है। यदि जहाज की लंबाई 100 मीटर है तो धारा के प्रतिकूल जाते समय जहाज को 144 किलोमीटर की दूरी तय करने में कितना समय लगेगा ?</p>",
                    options_en: ["<p>1 hour</p>", "<p>2 hours</p>", 
                                "<p>1.33 hours</p>", "<p>1.6 hours</p>"],
                    options_hi: ["<p>1 घंटे</p>", "<p>2 घंटे</p>",
                                "<p>1.33 घंटे</p>", "<p>1.6 घंटे</p>"],
                    solution_en: "<p>69.(b) Length of ship = 100 m <br>Length of boat = 50 m<br>Speed of boat in still water = 15 m/s<br>Let speed of stream = x&nbsp;m/s<br>Downstream speed of Boat = (15 + x) m/s<br>Upstream speed of Boat = (15 - x) m/s<br>According to question,<br>(15 + x) m/s = 2 (15 - x) m/s<br>15 + x&nbsp;= 30 - 2x<br>3x&nbsp;= 15 &rArr; x = 5<br>Downstream speed of Boat = 20 m/s<br>Upstream speed of Boat = 10 m/s<br>Ship while going downstream crosses the boat in 15 seconds.<br>Therefore,<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>50</mn><mo>+</mo><mn>100</mn></mrow><mrow><mo>(</mo><mi>s</mi><mi>p</mi><mi>e</mi><mi>e</mi><mi>d</mi><mi>&#160;</mi><mi>o</mi><mi>f</mi><mi>&#160;</mi><mi>s</mi><mi>h</mi><mi>i</mi><mi>p</mi><mo>+</mo><mn>5</mn><mo>)</mo><mo>-</mo><mn>20</mn></mrow></mfrac></math> = 15 <br>150 = 15 (Speed of ship - 15)<br>Speed of ship = 25 m/s<br>Speed of ship Upstream <br>= 20 m/s or 72 km/h<br>Time taken by ship to cross 144 km<br>= <math display=\"inline\"><mfrac><mrow><mi>&#160;</mi><mn>144</mn></mrow><mrow><mn>72</mn></mrow></mfrac></math> hour = 2 hour</p>",
                    solution_hi: "<p>69.(b) जहाज की लम्बाई = 100 m <br>नाव की लम्बाई = 50 m<br>शांत जल में नाव की गति = 15 m/s<br>माना धारा की गति = x&nbsp;m/s<br>धारा के अनुकूल नाव की गति = (15 + x) m/s<br>धारा के प्रतिकूल नाव की गति = (15 - x) m/s<br>प्रश्न के अनुसार,<br>(15 + x) m/s = 2 (15 - x) m/s<br>15 + x&nbsp;= 30 - 2x<br>3x&nbsp;= 15 &rArr; x = 5<br>धारा के अनुकूल नाव की गति = 20 m/s<br>धारा के प्रतिकूल नाव की गति = 10 m/s<br>जहाज धारा की दिशा में जाते समय नाव को 15 सेकंड में पार कर जाता है।<br>इसलिए,<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>50</mn><mo>+</mo><mn>100</mn></mrow><mrow><mo>(</mo><mi>&#2332;&#2361;&#2366;&#2332;</mi><mo>&#160;</mo><mi>&#2325;&#2368;</mi><mo>&#160;</mo><mi>&#2327;&#2340;&#2367;</mi><mo>+</mo><mn>5</mn><mo>)</mo><mo>-</mo><mn>20</mn></mrow></mfrac></math> = 15<br>150 = 15 (जहाज की गति - 15)<br>जहाज की गति = 25 m/s<br>धारा के विपरीत जहाज की गति <br>= 20 मीटर/सेकंड या 72 किमी/घंटा<br>जहाज को 144 किमी पार करने में लगा समय<br>= <math display=\"inline\"><mfrac><mrow><mi>&#160;</mi><mn>144</mn></mrow><mrow><mn>72</mn></mrow></mfrac></math> घंटा = 2 घंटा</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "70",
                    section: "17",
                    question_en: "<p>70. Which of the following statements is true about the nature of roots for the equation x(x + 3) - 8 = 2(x + 3) (x &minus; 3) ?</p>",
                    question_hi: "<p>70. समीकरण x(x + 3) - 8 = 2(x + 3) (x &minus; 3) के मूलों की प्रकृति के संबंध में इनमें से कौन सा कथन सत्य है</p>",
                    options_en: ["<p>Roots are irrational</p>", "<p>Two imaginary or non-real roots</p>", 
                                "<p>Two real and distinct roots</p>", "<p>Roots are real and equal</p>"],
                    options_hi: ["<p>मूल, अपरिमय है</p>", "<p>दो काल्पनिक या अवास्तविक मूल</p>",
                                "<p>दो वास्तविक या भिन्न मूल</p>", "<p>मूल वास्तविक और समान हैं</p>"],
                    solution_en: "<p>70.(c) x(x + 3) - 8 = 2(x + 3)(x -3)<br>x&sup2; + 3x - 8 = 2x&sup2; -18<br>x&sup2; - 3x -10 = 0<br>For the nature of roots <br>b&sup2; - 4ac = 9 - 4 &times; - 10 &times; 1<br>b&sup2; - 4ac = 49 <br>when b&sup2; - 4ac &gt;&nbsp;0 than Nature of roots are real and distinct <br>Therefore option c is the correct answer</p>",
                    solution_hi: "<p>70.(c) x(x + 3) - 8 = 2(x + 3)(x -3)<br>x&sup2; + 3x - 8 = 2x&sup2; -18<br>x&sup2; - 3x -10 = 0<br>मूलों की प्रकृति के लिए (For the nature of roots)<br>b&sup2; - 4ac = 9 - 4 &times; -10 &times; 1<br>b&sup2; - 4ac = 49<br>जब b&sup2; - 4ac &gt; 0&nbsp;<br>तब मूलों की प्रकृति वास्तविक और भिन्न होती है <br>इसलिए विकल्प c सही उत्तर है</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "71",
                    section: "17",
                    question_en: "<p>71. A man purchased two varieties of apples at the rate of ₹2 for 3 apples and ₹1 per apple, respectively. If he purchased an equal number of apples of each variety and then sold all his apples at ₹1 per apple, what was his profit percentage ?</p>",
                    question_hi: "<p>71. एक व्यक्ति ने क्रमशः ₹2 में 3 सेब, और ₹1 प्रति सेब की दर से सेब की दो किस्में खरीदीं। यदि उसने प्रत्येक किस्म के सेब समान संख्या में खरीदे और फिर सभी सेबों को ₹1 प्रति सेब की दर से बेचा, तो उसे प्राप्त प्रतिशत लाभ ज्ञात कीजिए।</p>",
                    options_en: ["<p>22%</p>", "<p>20%</p>", 
                                "<p>15%</p>", "<p>18%</p>"],
                    options_hi: ["<p>22%</p>", "<p>20%</p>",
                                "<p>15%</p>", "<p>18%</p>"],
                    solution_en: "<p>71.(b) He purchased 3 apples of both the varieties.<br>Total Cost price of 6 apples = 2 + 3 = 5<br>Now, Selling price of 6 apples = 6<br>His profit percentage = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> &times; 100 = 20%</p>",
                    solution_hi: "<p>71.(b) उसने दोनों किस्मों के 3 सेब खरीदे<br>6 सेबों का कुल क्रय मूल्य = 2 + 3 = 5<br>अब, 6 सेबों का विक्रय मूल्य = 6<br>उसका लाभ प्रतिशत = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> &times; 100 = 20%</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "72",
                    section: "17",
                    question_en: "<p>72. A sum of Rs 3000 is lent out in two parts in such a way that the simple interest on one part at 10% per annum for 5 years is equal to that on another part at 12.5% per annum for 4 years. The sum lent out at 12.5% is:</p>",
                    question_hi: "<p>72. 3000 रुपये की राशि को दो भागों में इस प्रकार उधार दिया जाता है कि एक भाग पर 5 वर्षों के लिए 10% प्रति वर्ष की दर से साधारण ब्याज दूसरे भाग पर 4 वर्षों के लिए 12.5% ​​प्रति वर्ष की दर के बराबर हो। 12.5% ​​पर उधार दी गई राशि है:</p>",
                    options_en: ["<p>Rs 500</p>", "<p>Rs 1000</p>", 
                                "<p>Rs 750</p>", "<p>Rs 1500</p>"],
                    options_hi: ["<p>Rs 500</p>", "<p>Rs 1000</p>",
                                "<p>Rs 750</p>", "<p>Rs 1500</p>"],
                    solution_en: "<p>72.(d)<br>Let the amount lent at 12.5% is <math display=\"inline\"><mi>x</mi></math><br>According to the question <br>(12.5 &times; 4)% of x = (10 &times; 5)% of (3000 - x) <br><math display=\"inline\"><mfrac><mrow><mi>x</mi></mrow><mrow><mn>2</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>3000</mn><mo>-</mo><mi>x</mi><mo>)</mo></mrow><mn>2</mn></mfrac></math><br>2<math display=\"inline\"><mi>x</mi></math> = 3000<br>&rArr; x = 1500</p>",
                    solution_hi: "<p>72.(d)<br>माना 12.5% ​​पर उधार दी गई राशि <math display=\"inline\"><mi>x</mi></math> है<br>प्रश्न के अनुसार<br>(12.5 &times; 4)% का x = (10 &times; 5)% का (3000 - x)&nbsp;<br><math display=\"inline\"><mfrac><mrow><mi>x</mi></mrow><mrow><mn>2</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>3000</mn><mo>-</mo><mi>x</mi><mo>)</mo></mrow><mn>2</mn></mfrac></math><br>2<math display=\"inline\"><mi>x</mi></math> = 3000<br>&rArr; x = 1500</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "73",
                    section: "17",
                    question_en: "<p>73. Study the given pie-chart carefully and answer the following question. <br>What amount (in Rs.) of the fund is acquired by the school from internal sources ? The entire fund that school gets from different sources is equal to 10 Lakh<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742789955388.png\" alt=\"rId66\" width=\"234\" height=\"175\"> <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742789955789.png\" alt=\"rId67\" width=\"215\" height=\"176\"></p>",
                    question_hi: "<p>73. दिए गए पाई- चार्ट का ध्यानपूर्वक अध्ययन कीजिए और निम्नलिखित प्रश्न का उत्तर दीजिए। स्कूल द्वारा आंतरिक स्रोतों से कितनी राशि (Rs. में) प्राप्त की जाती है ?<br>स्कूल को विभिन्न स्रोतों से मिलने वाला पूरा फंड Rs.10 लाख के बराबर है<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742789956211.png\" alt=\"rId68\" width=\"242\" height=\"197\"> &nbsp;<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742789956578.png\" alt=\"rId69\" width=\"237\" height=\"199\"></p>",
                    options_en: ["<p>1,45, 000</p>", "<p>1,50,000</p>", 
                                "<p>1,32,000</p>", "<p>1,60,000</p>"],
                    options_hi: ["<p>1,45, 000</p>", "<p>1,50,000</p>",
                                "<p>1,32,000</p>", "<p>1,60,000</p>"],
                    solution_en: "<p>73.(b) Fund acquired by the school from internal sources = <math display=\"inline\"><mfrac><mrow><mn>15</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> &times; 10,00,000 = ₹1,50,000</p>",
                    solution_hi: "<p>73.(b) विद्यालय द्वारा आंतरिक स्रोतों से अर्जित धनराशि = <math display=\"inline\"><mfrac><mrow><mn>15</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> &times; 10,00,000 = ₹1,50,000</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "74",
                    section: "17",
                    question_en: "<p>74. A family decided to spend 19% of their monthly income on travelling, 21% of their monthly income on household expenses, and 25% of their monthly income on family\'s medical expenses, and 5% on insurance. The family has the remaining amount of Rs. 13200 as cash. What is the monthly income of the family ?</p>",
                    question_hi: "<p>74. एक परिवार ने अपनी मासिक आय का 19% यात्रा पर, अपनी मासिक आय का 21% घरेलू खर्चों पर, और अपनी मासिक आय का 25% परिवार के चिकित्सा खर्चों पर और 5% बीमा पर खर्च करने का निर्णय लिया। परिवार के पास 13200 रुपये की शेष राशि है। तो परिवार की मासिक आय क्या है ?</p>",
                    options_en: ["<p>Rs.55,000</p>", "<p>Rs.44,000</p>", 
                                "<p>Rs.45,000</p>", "<p>Rs.34,000</p>"],
                    options_hi: ["<p>Rs 55,000</p>", "<p>Rs 44,000</p>",
                                "<p>Rs 45,000</p>", "<p>Rs 34,000</p>"],
                    solution_en: "<p>74.(b)<br>Total spend = (19 + 21 + 25 + 5)% = 70%<br>Remaining amount (100 -&nbsp;70)% = 30%<br>30% = 13,200<br>Then, 100% = <math display=\"inline\"><mfrac><mrow><mn>13200</mn></mrow><mrow><mn>30</mn></mrow></mfrac></math> &times; 100 = 44,000<br>Hence, Monthly income of the family = Rs.44,000</p>",
                    solution_hi: "<p>74.(b)<br>कुल खर्च = (19 + 21 + 25 + 5)% =&nbsp;70%<br>शेष बची हुई राशि = (100 - 70)% = 30%<br>30% = 13,200<br>तब, 100% = <math display=\"inline\"><mfrac><mrow><mn>13200</mn></mrow><mrow><mn>30</mn></mrow></mfrac></math> &times; 100 = 44,000<br>अत: परिवार की मासिक आय = Rs. 44,000</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "75",
                    section: "7",
                    question_en: "<p>75. A and B walk along a circular path in opposite directions with speeds of 2 rounds per hour and 3 rounds per hour respectively. If they start at the same time from the same point at 8 AM, how many times will they cross each other by 9:30AM ?</p>",
                    question_hi: "<p>75. A और B एक वृत्ताकार मार्ग पर विपरीत दिशाओं में क्रमशः 2 चक्कर प्रति घंटे और 3 चक्कर प्रति घंटे की चाल से चलते हैं। यदि वे एक साथ समान बिंदु से सुबह 8 बजे से चलना शुरू करते हैं, सुबह के 9:30 बजे तक वे एक-दूसरे को कितनी बार पार करेंगे ?</p>",
                    options_en: ["<p>6</p>", "<p>7</p>", 
                                "<p>4</p>", "<p>5</p>"],
                    options_hi: ["<p>6</p>", "<p>7</p>",
                                "<p>4</p>", "<p>5</p>"],
                    solution_en: "<p>75.(b) According to question,<br>Speed of A = 2 rounds per hours <br>Speed of B = 3 rounds per hours <br>Relative speed = (3 + 2) = 5 rounds per hour<br>So they will cross each other 5 times in one hours , <br>Total time from 8AM to 9.30 AM = <math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> hours<br>Total no. of time they cross each other = <math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> &times; 5 = 7.5 &asymp; 7 times</p>",
                    solution_hi: "<p>75.(b) प्रश्न के अनुसार,<br>A की चाल = 2 चक्कर प्रति घंटे <br>B की चाल = 3 चक्कर प्रति घंटे <br>सापेक्ष गति = (3 + 2) = 5 चक्कर प्रति घंटे <br>तो वे एक घंटे में 5 बार एक दूसरे को पार करेंगे, <br>सुबह 8 बजे से 9.30 बजे तक का कुल समय = <math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> hours<br>वे एक दूसरे को कुल बार कितनी पार करेंगे = <math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> &times; 5 = 7.5 &asymp; 7 बार</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "76",
                    section: "7",
                    question_en: "<p>76. Identify the segment in the sentence which contains a grammatical error. If there is no error, select&nbsp;&lsquo;No error&rsquo;<br>If your kids sleep well, they are likely to behave better.</p>",
                    question_hi: "<p>76.Identify the segment in the sentence which contains a grammatical error. If there is no error, select&nbsp;&lsquo;No error&rsquo;<br>If your kids sleep well, they are likely to behave better.</p>",
                    options_en: ["<p>No error</p>", "<p>to behave better</p>", 
                                "<p>they are likely</p>", "<p>If your kids sleep well</p>"],
                    options_hi: ["<p>No error</p>", "<p>to behave better</p>",
                                "<p>they are likely</p>", "<p>If your kids sleep well</p>"],
                    solution_en: "<p>76.(a) <strong>No error.</strong><br>The given sentence is grammatically correct.</p>",
                    solution_hi: "<p>76.(a) <strong>No error.</strong> <br>दिया गया वाक्य grammatically सही है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "77",
                    section: "7",
                    question_en: "<p>77. Select the most appropriate option to fill in the blank.<br>Choosing the right software for any purpose can be a/an ______ task for any business.</p>",
                    question_hi: "<p>77. Select the most appropriate option to fill in the blank.<br>Choosing the right software for any purpose can be a/an ______ task for any business.</p>",
                    options_en: ["<p>intimating</p>", "<p>challenging</p>", 
                                "<p>flickering</p>", "<p>attributing</p>"],
                    options_hi: ["<p>intimating</p>", "<p>challenging</p>",
                                "<p>flickering</p>", "<p>attributing</p>"],
                    solution_en: "<p>77.(b) &lsquo;Challenging&rsquo; means forcing you to make a lot of effort. The given sentence states that choosing the right software for any purpose can be a challenging task for any business. Hence, &lsquo;challenging&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>77.(b) &lsquo;Challenging&rsquo; का अर्थ है आपको बहुत अधिक प्रयास करने के लिए मजबूर करना। दिए गए sentence में कहा गया है कि किसी भी उद्देश्य के लिए सही सॉफ्टवेयर चुनना किसी भी व्यवसाय के लिए एक चुनौतीपूर्ण कार्य हो सकता है। इसलिए, &lsquo;challenging&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "78",
                    section: "7",
                    question_en: "<p>78. Select the most appropriate One-Word Substitution for the given situation focusing on the underlined segment<br>I <span style=\"text-decoration: underline;\"><strong>like to meeting of people</strong></span> who have travelled widely.</p>",
                    question_hi: "<p>78. Select the most appropriate One-Word Substitution for the given situation focusing on the underlined segment<br>I <span style=\"text-decoration: underline;\"><strong>like to meeting of people</strong></span> who have travelled widely.</p>",
                    options_en: ["<p>like meeting people</p>", "<p>like meet to people</p>", 
                                "<p>like to be meeting people</p>", "<p>will like meeting with people</p>"],
                    options_hi: ["<p>like meeting people</p>", "<p>like meet to people</p>",
                                "<p>like to be meeting people</p>", "<p>will like meeting with people</p>"],
                    solution_en: "<p>78.(a) The given sentence needs a gerund(V-ing) and not an infinitive(preposition &lsquo;to&rsquo;). Hence, <strong>&lsquo;like meeting(V-ing) people&rsquo;</strong> is the most appropriate answer.</p>",
                    solution_hi: "<p>78.(a) दिए गए sentence में एक gerund (V-ing) की जरूरत है, न कि an infinitive (preposition \'to\')। इसलिए,<strong> \'like meeting (V-ing) people\' </strong>सबसे उपयुक्त उत्तर है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "79",
                    section: "7",
                    question_en: "<p>79. Select the correct indirect / direct form of the given sentence.<br>The doctor told his patient, \"Continue the same course of medicines for the next week.\"</p>",
                    question_hi: "<p>79. Select the correct indirect / direct form of the given sentence.<br>The doctor told his patient, \"Continue the same course of medicines for the next week.\"</p>",
                    options_en: ["<p>The doctor told his patient to continued the same course of medicines in the next week.</p>", "<p>The doctor told his patient for continuing the same course of medicines for the next week.</p>", 
                                "<p>The doctor told his patient that he may continue the same course of medicines in the following week.</p>", "<p>The doctor told his patient to continue the same course medicines for the following week.</p>"],
                    options_hi: ["<p>The doctor told his patient to continued the same course of medicines in the next week.</p>", "<p>The doctor told his patient for continuing the same course of medicines for the next week.</p>",
                                "<p>The doctor told his patient that he may continue the same course of medicines in the following week.</p>", "<p>The doctor told his patient to continue the same course of medicines for the following week.</p>"],
                    solution_en: "<p>79.(d) The doctor told his patient to continue the same course of medicines for the following week. (Correct) <br>(a) The doctor told his patient to <span style=\"text-decoration: underline;\">continued</span> the same course of medicines in the next week. (Verb I form should be used)<br>(b) The doctor told his patient <span style=\"text-decoration: underline;\">for continuing</span> the same course of medicines for the next week. (Incorrect preposition for is used)<br>(c) The doctor told his patient <span style=\"text-decoration: underline;\">that he may continue</span> the same course of medicines in the following week. (Incorrect word)</p>",
                    solution_hi: "<p>79.(d) The doctor told his patient to continue the same course of medicines for the following week. (Correct) <br>(a) The doctor told his patient to <span style=\"text-decoration: underline;\">continued</span> the same course of medicines in the next week. (गलत verb (<span style=\"text-decoration: underline;\">continued</span>) का प्रयोग किया गया है। (continue) का प्रयोग होगा।)<br>(b) The doctor told his patient <span style=\"text-decoration: underline;\">for continuing</span> the same course of medicines for the next week. <span style=\"text-decoration: underline;\">for continuing</span> के स्थान पर to continue का प्रयोग होगा)<br>(c) The doctor told his patient <span style=\"text-decoration: underline;\">that he may continue</span> the same course of medicines in the following week. (Sentence का meaning original sentence से different है)</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "80",
                    section: "7",
                    question_en: "<p>80. Select the most appropriate antonym of the given word.<br><strong>Former</strong></p>",
                    question_hi: "<p>80. Select the most appropriate antonym of the given word.<br><strong>Former</strong></p>",
                    options_en: ["<p>External</p>", "<p>Additional</p>", 
                                "<p>Featured</p>", "<p>Latter</p>"],
                    options_hi: ["<p>External</p>", "<p>Additional</p>",
                                "<p>Featured</p>", "<p>Latter</p>"],
                    solution_en: "<p>80.(d) <strong>Former-</strong> of an earlier time<br><strong>Latter- </strong>nearer to the end of a period of time<br><strong>External</strong>- connected with the outside of something<br><strong>Additional-</strong> more than is usual or expected<br><strong>Featured</strong> - to include somebody/something as an important&nbsp;part</p>",
                    solution_hi: "<p>80.(d) <strong>Former (भूतपूर्व) -</strong> of an earlier time<br><strong>Latter (समापन) -</strong> nearer to the end of a period of time<br><strong>External (बाहय) -</strong> connected with the&nbsp;outside of something<br><strong>Additional (अतिरिक्त) -</strong> more than is usual or expected<br><strong>Featured (महत्वपूर्ण) - </strong>to include somebody/something as an important part</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "81",
                    section: "7",
                    question_en: "<p>81. Select the correct indirect / direct form of the given sentence.<br>Shanti asked me, \"Why did you keep this smartphone in the bin ?\"</p>",
                    question_hi: "<p>81. Select the correct indirect / direct form of the given sentence.<br>Shanti asked me, \"Why did you keep this smartphone in the bin ?\"</p>",
                    options_en: ["<p>Shanti asked me why I had been keeping that smartphone in the bin.</p>", "<p>Shanti asked me why I kept that smartphone in the bin.</p>", 
                                "<p>Shanti asked me why I was keeping that smartphone in the bin.</p>", "<p>Shanti asked me why I had kept that smartphone in the bin.</p>"],
                    options_hi: ["<p>Shanti asked me why I had been keeping that smartphone in the bin.</p>", "<p>Shanti asked me why I kept that smartphone in the bin.</p>",
                                "<p>Shanti asked me why I was keeping that smartphone in the bin.</p>", "<p>Shanti asked me why I had kept that smartphone in the bin.</p>"],
                    solution_en: "<p>81.(d) Shanti asked me why I had kept that smartphone in the bin. (Correct) <br>Simple past (V<sub>2</sub>) &rarr; past perfect (had + V<sub>3</sub>)<br>(a) Shanti asked me why I <span style=\"text-decoration: underline;\">had been keeping</span> that smartphone in the bin.(Incorrect tense)<br>(b) Shanti asked me why I <span style=\"text-decoration: underline;\">kept</span> that smartphone in the bin. (Incorrect tense)<br>(c) Shanti asked me why <span style=\"text-decoration: underline;\">I was keeping</span> that smartphone in the bin. (Incorrect tense)</p>",
                    solution_hi: "<p>81.(d) Shanti asked me why I had kept that smartphone in the bin.(Correct) Simple past (V<sub>2</sub>) &rarr; past perfect (had + V<sub>3</sub>)<br>(a) Shanti asked me why I <span style=\"text-decoration: underline;\">had been keeping</span> that smartphone in the bin.(गलत tense <span style=\"text-decoration: underline;\">had been keeping</span> (past perfect continuous) का प्रयोग किया गया है); had kept (past perfect) का प्रयोग होगा<br>(b) Shanti asked me why I <span style=\"text-decoration: underline;\">kept</span> that smartphone in the bin.(गलत tense kept (simple past) का प्रयोग किया गया है; had kept (past perfect) का प्रयोग होगा<br>(c) Shanti asked me why <span style=\"text-decoration: underline;\">I was keeping</span> that smartphone in the bin.(गलत tense was keeping (past continuous ) का प्रयोग किया गया है ; had kept (past perfect ) का प्रयोग होगा</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "82",
                    section: "7",
                    question_en: "<p>82. Select the option that expresses the given sentence in a passive voice.<br>Imran has found two good jobs in the advertisements of the newspaper.</p>",
                    question_hi: "<p>82. Select the option that expresses the given sentence in a passive voice.<br>Imran has found two good jobs in the advertisements of the newspaper.</p>",
                    options_en: ["<p>Two good jobs are being found by Imran in the advertisements of the newspaper.</p>", "<p>Two good jobs had found by Imran in the advertisements of the newspaper.</p>", 
                                "<p>Two good jobs have been found by Imran in the advertisements of the newspaper.</p>", "<p>Two good jobs has found by Imran in the advertisements of the newspaper.</p>"],
                    options_hi: ["<p>Two good jobs are being found by Imran in the advertisements of the newspaper.</p>", "<p>Two good jobs had found by Imran in the advertisements of the newspaper.</p>",
                                "<p>Two good jobs have been found by Imran in the advertisements of the newspaper.</p>", "<p>Two good jobs has found by Imran in the advertisements of the newspaper.</p>"],
                    solution_en: "<p>82.(c) Two good jobs have been found by Imran in the advertisements of the newspaper. (Correct)<br>(a) Two good <span style=\"text-decoration: underline;\">jobs are being found</span> by Imran in the advertisements of the newspaper. (Incorrect Tense)<br>(b) Two good jobs <span style=\"text-decoration: underline;\">had found</span> by Imran in the advertisements of the newspaper. (Incorrect Tense)<br>(d) Two good jobs <span style=\"text-decoration: underline;\">has found</span> by Imran in the advertisements of the newspaper. (Incorrect Tense)</p>",
                    solution_hi: "<p>82.(c) Two good jobs have been found by Imran in the advertisements of the newspaper. (Correct)<br>(a) Two good jobs <span style=\"text-decoration: underline;\">are being found</span> by Imran in the advertisements of the newspaper. (गलत tense (present continuous) का प्रयोग किया गया है । have been found (present perfect) का प्रयोग होगा |)<br>(b) Two good jobs <span style=\"text-decoration: underline;\">had found</span> by Imran in the advertisements of the newspaper. (गलत tense (past perfect) का प्रयोग किया गया है । have been found (present perfect) का प्रयोग होगा |)<br>(d) Two good jobs <span style=\"text-decoration: underline;\">has found</span> by Imran in the advertisements of<br>the newspaper. (गलत verb(<span style=\"text-decoration: underline;\">has found</span>) का प्रयोग किया गया है।) (have been found) का प्रयोग होगा |)</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "83",
                    section: "7",
                    question_en: "<p>83. Select the most appropriate option to fill in the blank.<br>A better plan is <strong><span style=\"text-decoration: underline;\">to going and has</span></strong> a lavish dinner at a fine restaurant.</p>",
                    question_hi: "<p>83. Select the most appropriate option to fill in the blank.<br>A better plan is <strong><span style=\"text-decoration: underline;\">to going and has</span></strong> a lavish dinner at a fine restaurant.</p>",
                    options_en: ["<p>is to go and have</p>", "<p>No substitution required</p>", 
                                "<p>is to gone and have</p>", "<p>was to going and have</p>"],
                    options_hi: ["<p>is to go and have</p>", "<p>No substitution required</p>",
                                "<p>is to gone and have</p>", "<p>was to going and have</p>"],
                    solution_en: "<p>83.(a) <strong>is to go and have</strong><br>We generally use the first form of the verb(V<sub>1</sub>) after the <br>preposition &lsquo;to&rsquo;. Hence, &lsquo; is to go(V<sub>1</sub>) and have&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>83.(a)<strong> is to go and have</strong><br>हम आम तौर पर verb के first form (V<sub>1</sub>) का उपयोग preposition \'to\' के बाद करते हैं। इसलिए, &lsquo;to go(V<sub>1</sub>) and have&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "84",
                    section: "7",
                    question_en: "<p>84. Directions: Sentences of a paragraph are given below in jumbled order. Arrange the sentences in the right order to form a meaningful and coherent paragraph.<br>A. There lived a foolish king in a large kingdom. <br>B. He thought that it will be a good joke. <br>C. He did not realise that what he thought of as a joke would&nbsp;cost him heavily. <br>D. The king once decided to throw all his ministers in prison for a&nbsp;day.</p>",
                    question_hi: "<p>84. Directions: Sentences of a paragraph are given below in jumbled order. Arrange the sentences in the right order to form a meaningful and coherent paragraph.<br>A. There lived a foolish king in a large kingdom. <br>B. He thought that it will be a good joke. <br>C. He did not realise that what he thought of as a joke would&nbsp;cost him heavily. <br>D. The king once decided to throw all his ministers in prison for a&nbsp;day.</p>",
                    options_en: ["<p>ABCD</p>", "<p>ACBD</p>", 
                                "<p>DABC</p>", "<p>ADBC</p>"],
                    options_hi: ["<p>ABCD</p>", "<p>ACBD</p>",
                                "<p>DABC</p>", "<p>ADBC</p>"],
                    solution_en: "<p>84.(d) <strong>ADBC</strong> <br>Sentence A will be the starting line as it contains the main idea of the passage i.e. a foolish king. However, Sentence D states that the king once decided to throw all his ministers in prison for a day. So, D will follow A. Further, Sentence B states that the king thought that it will be a good joke &amp; Sentence C states that he did not realise that what he thought of as a joke would cost him heavily. So, C will follow B. Going through the options, option d has the correct sequence.</p>",
                    solution_hi: "<p>84.(d) <strong>ADBC</strong> <br>वाक्य A शुरुआती line होगी क्योंकि इसमें parajumble का मुख्य पात्र है अर्थात् foolish king । हालांकि, वाक्य D बताता है कि राजा ने एक बार अपने सभी मंत्रियों को एक दिन के लिए prison में डालने का फैसला किया। तो A के बाद D होगा। इसके बाद , B बताता है कि राजा ने सोचा कि यह एक अच्छा joke होगा और C में बताता है कि उसे इस बात का एहसास नहीं था कि वह जो मजाक के रूप में सोचता है, उसकी कीमत उसे भारी पड़ेगी। तो B के बाद C होगा। सभी विकल्पों को देखते हुए, Option D का sequence सही है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "85",
                    section: "7",
                    question_en: "<p>85. In the given sentence the underlined word may have been incorrectly spelt. Identify the option that rectifies the spelling. If there is no error in spelling, select \'No error\'.<br>Siraz-ud-Daula <strong><span style=\"text-decoration: underline;\">beseiged</span></strong> Calcutta to take punitive actions against the East India Company.</p>",
                    question_hi: "<p>85. In the given sentence the underlined word may have been incorrectly spelt. Identify the option that rectifies the spelling. If there is no error in spelling, select \'No error\'.<br>Siraz-ud-Daula <strong><span style=\"text-decoration: underline;\">beseiged</span></strong> Calcutta to take punitive actions against the East India Company.</p>",
                    options_en: ["<p>besiezed</p>", "<p>besiejed</p>", 
                                "<p>besieged</p>", "<p>No error</p>"],
                    options_hi: ["<p>besiezed</p>", "<p>besiejed</p>",
                                "<p>besieged</p>", "<p>No error</p>"],
                    solution_en: "<p>85.(c) Besieged<br>&lsquo;Besieged&rsquo; is the correct spelling.</p>",
                    solution_hi: "<p>85.(c) besieged<br>&lsquo;Besieged&rsquo; सही spelling है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "86",
                    section: "7",
                    question_en: "<p>86. Select the option that can be used as a one-word substitute for the given group of words<br>Something that is pertaining to controversy</p>",
                    question_hi: "<p>86. Select the option that can be used as a one-word substitute for the given group of words<br>Something that is pertaining to controversy</p>",
                    options_en: ["<p>Poignancy</p>", "<p>laudatory</p>", 
                                "<p>Putative</p>", "<p>Polemical</p>"],
                    options_hi: ["<p>Poignancy</p>", "<p>laudatory</p>",
                                "<p>Putative</p>", "<p>Polemical</p>"],
                    solution_en: "<p>86.(d) <strong>Polemical -</strong> something that is pertaining to the controversy<br><strong>Poignancy -</strong> a very sharp feeling of sadness<br><strong>Laudatory -</strong> expressing praise and commendation<br><strong>Putative -</strong> generally considered or reputed to be.</p>",
                    solution_hi: "<p>86.(d) <strong>Polemical (विवादात्मक)- </strong>something that is pertaining to the controversy<br><strong>Poignancy (मार्मिकता)-</strong> a very sharp feeling of sadness<br><strong>Laudatory (सराहने योग्य)-&nbsp;</strong>expressing praise and commendation<br><strong>Putative (तथाकथित)-</strong> generally considered or reputed to be.</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "87",
                    section: "7",
                    question_en: "<p>87. Select the most appropriate ANTONYM of the underlined word<br>The reputed officer was removed from his post as he was having a <strong><span style=\"text-decoration: underline;\">clandestine</span></strong> affair.</p>",
                    question_hi: "<p>87. Select the most appropriate ANTONYM of the underlined word<br>The reputed officer was removed from his post as he was having a <span style=\"text-decoration: underline;\"><strong>clandestine</strong></span> affair.</p>",
                    options_en: ["<p>Surreptitious</p>", "<p>Overt</p>", 
                                "<p>Amorous</p>", "<p>Casual</p>"],
                    options_hi: ["<p>Surreptitious</p>", "<p>Overt</p>",
                                "<p>Amorous</p>", "<p>Casual</p>"],
                    solution_en: "<p>87.(b) <strong>Overt </strong>- something done in an open way and not secretly<br><strong>Clandestine</strong> - something planned or done in secret<br><strong>Surreptitious </strong>- something done secretly<br><strong>Amorous </strong>- strongly moved by love and&nbsp;especially sexual love<br><strong>Casual </strong>- relaxed and not worried</p>",
                    solution_hi: "<p>87.(b) <strong>Overt (प्रत्यक्ष) - </strong>something done in an open way and not secretly<br><strong>Clandestine (गुप्त) - </strong>something planned or done in secret<br><strong>Surreptitious (गुप्त) - </strong>something done secretly<br><strong>Amorous (कामुक) -</strong> strongly moved by love and especially sexual love<br><strong>Casual (लापरवाह) -</strong> relaxed and not worried</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "88",
                    section: "7",
                    question_en: "<p>88. Select the option that can be used as a one-word substitute for the given group of words<br>An imaginary ideal society where people live in perfect conditions</p>",
                    question_hi: "<p>88. Select the option that can be used as a one-word substitute for the given group of words<br>An imaginary ideal society where people live in perfect conditions</p>",
                    options_en: ["<p>Idyll</p>", "<p>Myopia</p>", 
                                "<p>Countryside</p>", "<p>Utopia</p>"],
                    options_hi: ["<p>Idyll</p>", "<p>Myopia</p>",
                                "<p>Countryside</p>", "<p>Utopia</p>"],
                    solution_en: "<p>88.(d) Utopia <br><strong>Utopia</strong>- An imaginary ideal society where people live in perfect conditions <br><strong>Idyll-</strong> an extremely happy, peaceful, or picturesque period or situation <br><strong>Myopia</strong>- the inability to see things clearly when they are far away <br><strong>Countryside-</strong> the land and scenery of a rural area.</p>",
                    solution_hi: "<p>88.(d) Utopia <br><strong>Utopia (आदर्शलोक)-</strong> An imaginary ideal society where people live in perfect conditions&nbsp;<br><strong>Idyll (सुखद जीवन)-</strong> an extremely happy, peaceful, or picturesque period or situation&nbsp;<br><strong>Myopia (दृष्टि दोष)-</strong> the inability to see things clearly when they are far away <br><strong>Countryside(ग्रामीण</strong> <strong>क्षेत्र)-</strong> the land and scenery of a rural area.</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "89",
                    section: "7",
                    question_en: "<p>89. Select the most appropriate synonym of the given word.<br><strong>Oblate</strong></p>",
                    question_hi: "<p>89. Select the most appropriate synonym of the given word.<br><strong>Oblate</strong></p>",
                    options_en: ["<p>Mendicant</p>", "<p>Heathen</p>", 
                                "<p>Layperson</p>", "<p>Secular</p>"],
                    options_hi: ["<p>Mendicant</p>", "<p>Heathen</p>",
                                "<p>Layperson</p>", "<p>Secular</p>"],
                    solution_en: "<p>89.(a) Mendicant <br><strong>Oblate </strong>- a person living in or associated with a religious community but not bound by vows<br><strong>Heathen </strong>- a person regarded as lacking culture or moral principles<br><strong>Layperson </strong>- One who is not familiar with a given subject or activity<br><strong>Secular </strong>- not connected with religious or spiritual matters</p>",
                    solution_hi: "<p>89.(a) Mendicant<br><strong>Oblate </strong>- a person living in or associated with a religious community but not bound by vows/एक धार्मिक समुदाय में रहने वाला या उससे जुड़ा व्यक्ति लेकिन प्रतिज्ञा से बंधा नहीं<br><strong>Heathen(असभ्य) -</strong> a person regarded as lacking culture or moral principles<br><strong>Layperson(साधारण आदमी) - </strong>One who is not familiar with a given subject or activity<br><strong>Secular(धर्मनिरपेक्ष) -</strong> not connected with religious or spiritual matters</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "90",
                    section: "7",
                    question_en: "<p>90. Select the most appropriate idiom for the given situation focusing on the underlined segment.<br>By the end of the football match, feelings had <strong><span style=\"text-decoration: underline;\">reached fever pitch.</span></strong></p>",
                    question_hi: "<p>90. Select the most appropriate idiom for the given situation focusing on the underlined segment.<br>By the end of the football match, feelings had <span style=\"text-decoration: underline;\"><strong>reached fever pitch.</strong></span></p>",
                    options_en: ["<p>in or to many places</p>", "<p>to get so strong that one cannot control them</p>", 
                                "<p>be extremely serious and worrying</p>", "<p>start well</p>"],
                    options_hi: ["<p>in or to many places</p>", "<p>to get so strong that one cannot control them</p>",
                                "<p>be extremely serious and worrying</p>", "<p>start well</p>"],
                    solution_en: "<p>90.(b) <strong>to get so strong that one cannot control them</strong><br>&lsquo;Fever pitch&rsquo; is a phrase that means a state of extreme excitement. The given sentence states that by the end of the football match, feelings had reached a state of extreme excitement. Hence, &lsquo;fever pitch&rsquo; is the most appropriate <br>answer.</p>",
                    solution_hi: "<p>90.(b) <strong>to get so strong that one cannot control them</strong><br>&lsquo;Fever pitch&rsquo; एक phrase है जिसका अर्थ है अत्यधिक उत्तेजना की स्थिति। दिए गए sentence में कहा गया है कि फुटबॉल मैच के अंत तक भावनाएँ अत्यधिक उत्तेजना की स्थिति में पहुँच चुकी थीं। इसलिए, &lsquo;fever pitch&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "91",
                    section: "7",
                    question_en: "<p>91.<strong> Cloze test:-</strong><br>Salvador Dali (91) ______a prominent Spanish surrealist painter (92) _____ in Figueres, Catalonia, on 11 May 1904. Dali\'s father was strict in the education of his children unlike (93) ______mother. Dali had a brother named Salvador (94) _____ was born nine months before him and died of gastroenteritis. When he was five, Dali was taken to his brother\'s grave and was told that he was his brother\'s (95) ______ .<br>Select the most appropriate option to fill in blank no.91</p>",
                    question_hi: "<p>91. <strong>Cloze test:-</strong><br>Salvador Dali (91) ______a prominent Spanish surrealist painter (92) _____ in Figueres, Catalonia, on 11 May 1904. Dali\'s father was strict in the education of his children unlike (93) ______mother. Dali had a brother named Salvador (94) _____ was born nine months before him and died of gastroenteritis. When he was five, Dali was taken to his brother\'s grave and was told that he was his brother\'s (95) ______ .<br>Select the most appropriate option to fill in blank no.91</p>",
                    options_en: ["<p>was</p>", "<p>were</p>", 
                                "<p>has</p>", "<p>is</p>"],
                    options_hi: ["<p>was</p>", "<p>were</p>",
                                "<p>has</p>", "<p>is</p>"],
                    solution_en: "<p>91.(a) <strong>Was</strong><br>The given passage is in past tense and the subject &lsquo;Salvodaor Dali&rsquo; is singular. Hence, &lsquo;was&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>91.(a) <strong>Was</strong><br>दिया गया passage past tense में है और subject \'Salvador Dali\' singular है। अतः, \'was\' सबसे उपयुक्त उत्तर है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "92",
                    section: "7",
                    question_en: "<p>92. <strong>Cloze test:-</strong><br>Salvador Dali (91) ______a prominent Spanish surrealist painter (92) _____ in Figueres, Catalonia, on 11 May 1904. Dali\'s father was strict in the education of his children unlike (93) ______mother. Dali had a brother named Salvador (94) _____ was born nine months before him and died of gastroenteritis. When he was five, Dali was taken to his brother\'s grave and was told that he was his brother\'s (95) ______ .<br>Select the most appropriate option to fill in blank no.92</p>",
                    question_hi: "<p>92. <strong>Cloze test:-</strong><br>Salvador Dali (91) ______a prominent Spanish surrealist painter (92) _____ in Figueres, Catalonia, on 11 May 1904. Dali\'s father was strict in the education of his children unlike (93) ______mother. Dali had a brother named Salvador (94) _____ was born nine months before him and died of gastroenteritis. When he was five, Dali was taken to his brother\'s grave and was told that he was his brother\'s (95) ______ .<br>Select the most appropriate option to fill in blank no.92</p>",
                    options_en: ["<p>rooted</p>", "<p>bought</p>", 
                                "<p>brought</p>", "<p>born</p>"],
                    options_hi: ["<p>rooted</p>", "<p>bought</p>",
                                "<p>brought</p>", "<p>born</p>"],
                    solution_en: "<p>92.(d) <strong>Born</strong><br>The given passage states that Salvador Dali was a prominent Spanish surrealist painter born, Catalonia, on 11 May, 1904. Hence, &lsquo;Born&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>92.(d) <strong>Born</strong><br>दिए गए Passage में कहा गया है कि साल्वाडोर डाली एक प्रमुख स्पेनिश चित्रकार थे, जिनका जन्म 11 मई, 1904 को कैटेलोनिया में हुआ था। इसलिए, \'Born\' सबसे उपयुक्त उत्तर है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "93",
                    section: "7",
                    question_en: "<p>93. <strong>Cloze test:-</strong><br>Salvador Dali (91) ______a prominent Spanish surrealist painter (92) _____ in Figueres, Catalonia, on 11 May 1904. Dali\'s father was strict in the education of his children unlike (93) ______mother. Dali had a brother named Salvador (94) _____ was born nine months before him and died of gastroenteritis. When he was five, Dali was taken to his brother\'s grave and was told that he was his brother\'s (95) ______ .<br>Select the most appropriate option to fill in blank no.93</p>",
                    question_hi: "<p>93. <strong>Cloze test:-</strong><br>Salvador Dali (91) ______a prominent Spanish surrealist painter (92) _____ in Figueres, Catalonia, on 11 May 1904. Dali\'s father was strict in the education of his children unlike (93) ______mother. Dali had a brother named Salvador (94) _____ was born nine months before him and died of gastroenteritis. When he was five, Dali was taken to his brother\'s grave and was told that he was his brother\'s (95) ______ .<br>Select the most appropriate option to fill in blank no.93</p>",
                    options_en: ["<p>their</p>", "<p>him</p>", 
                                "<p>her</p>", "<p>his</p>"],
                    options_hi: ["<p>their</p>", "<p>him</p>",
                                "<p>her</p>", "<p>his</p>"],
                    solution_en: "<p>93.(d) <strong>His</strong> <br>Possessive case &lsquo;his&rsquo; will be used for the subject Dali. Hence, &lsquo;his&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>93.(d) <strong>His</strong> <br>Possessive case \'his\' का प्रयोग Subject Dali के लिए किया जाएगा। अतः, \'his\' सबसे उपयुक्त उत्तर है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "94",
                    section: "7",
                    question_en: "<p>94. <strong>Cloze test:-</strong><br>Salvador Dali (91) ______a prominent Spanish surrealist painter (92) _____ in Figueres, Catalonia, on 11 May 1904. Dali\'s father was strict in the education of his children unlike (93) ______mother. Dali had a brother named Salvador (94) _____ was born nine months before him and died of gastroenteritis. When he was five, Dali was taken to his brother\'s grave and was told that he was his brother\'s (95) ______ .<br>Select the most appropriate option to fill in blank no.94</p>",
                    question_hi: "<p>94. <strong>Cloze test:-</strong><br>Salvador Dali (91) ______a prominent Spanish surrealist painter (92) _____ in Figueres, Catalonia, on 11 May 1904. Dali\'s father was strict in the education of his children unlike (93) ______mother. Dali had a brother named Salvador (94) _____ was born nine months before him and died of gastroenteritis. When he was five, Dali was taken to his brother\'s grave and was told that he was his brother\'s (95) ______ .<br>Select the most appropriate option to fill in blank no.94</p>",
                    options_en: ["<p>whose</p>", "<p>who</p>", 
                                "<p>whom</p>", "<p>that</p>"],
                    options_hi: ["<p>whose</p>", "<p>who</p>",
                                "<p>whom</p>", "<p>that</p>"],
                    solution_en: "<p>94.(b) <strong>Who</strong> <br>Relative pronoun &lsquo;Who&rsquo; is used for the antecedent &lsquo;Salvador&rsquo;. Hence, &lsquo;Who&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>94.(b) <strong>Who</strong> <br>Relative pronoun \'Who\' का प्रयोग antecedent \'Salvador\' के लिए किया जाता है। अतः, \'Who\' सबसे उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "95",
                    section: "7",
                    question_en: "<p>95. <strong>Cloze test:-</strong><br>Salvador Dali (91) ______a prominent Spanish surrealist painter (92) _____ in Figueres, Catalonia, on 11 May 1904. Dali\'s father was strict in the education of his children unlike (93) ______mother. Dali had a brother named Salvador (94) _____ was born nine months before him and died of gastroenteritis. When he was five, Dali was taken to his brother\'s grave and was told that he was his brother\'s (95) ______ .<br>Select the most appropriate option to fill in blank no.95</p>",
                    question_hi: "<p>95. <strong>Cloze test:-</strong><br>Salvador Dali (91) ______a prominent Spanish surrealist painter (92) _____ in Figueres, Catalonia, on 11 May 1904. Dali\'s father was strict in the education of his children unlike (93) ______mother. Dali had a brother named Salvador (94) _____ was born nine months before him and died of gastroenteritis. When he was five, Dali was taken to his brother\'s grave and was told that he was his brother\'s (95) ______ .<br>Select the most appropriate option to fill in blank no.95</p>",
                    options_en: ["<p>ordination</p>", "<p>detonation</p>", 
                                "<p>reincarnation</p>", "<p>stagnation</p>"],
                    options_hi: ["<p>ordination</p>", "<p>detonation</p>",
                                "<p>reincarnation</p>", "<p>stagnation</p>"],
                    solution_en: "<p>95.(c)<strong> Reincarnation</strong><br>Reincarnation means the rebirth of a soul in another body. The given passage states that Dali was taken to his brother&rsquo;s grave and was told that he was his brother&rsquo;s reincarnation. Hence, &lsquo;Reincarnation&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>95.(c) <strong>Reincarnation</strong><br>Reincarnation का अर्थ है एक आत्मा का दूसरे शरीर में पुनर्जन्म। दिए गए passage में कहा गया है कि डाली को उसके भाई की कब्र पर ले जाया गया और उसे बताया गया कि वह उसके भाई का पुनर्जन्म था। अतः, \'reincarnation\' सबसे उपयुक्त उत्तर है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "96",
                    section: "7",
                    question_en: "<p>96. <strong>Comprehension :-</strong><br>Between him and Darcy there was a very steady friendship, in spite of great opposition of character. Bingley was endeared to Darcy by the easiness, openness, and ductility of his temper, though no disposition could offer a greater contrast to his own, and though with his own he never appeared dissatisfied. On the strength of Darcy&rsquo;s regard Bingley had the firmest reliance, and of his judgment the highest opinion. In understanding, Darcy was the superior.<br>Bingley was by no means deficient, but Darcy was clever. He was at the same time haughty, reserved, and fastidious, and his manners, though well bred, were not inviting. In that respect his friend had greatly the advantage. Bingley was sure of being liked wherever he appeared, Darcy was continually giving offence.<br>The manner in which they spoke of the Meryton assembly was sufficiently characteristic.<br>Bingley had never met with pleasanter people or prettier girls in&nbsp;his life; everybody had been most kind and attentive to him; there had been no formality, no stiffness; he had soon felt acquainted with all the room; and as to Miss Bennet, he could not conceive an angel more beautiful. Darcy, on the contrary, had seen a collection of people in whom there was little beauty and no fashion, for none of whom he had felt the smallest interest, and from none received either attention or pleasure. Miss Bennet he acknowledged to be pretty, but she smiled too much.<br>Mrs. Hurst and her sister allowed it to be so&mdash;but still they admired her and liked her, and pronounced her to be a sweet girl, and one whom they should not object to know more of.<br>Miss Bennet was therefore established as a sweet girl, and their brother felt authorised by such commendation to think of her as he chose.<br>Select the most appropriate title for the passage from the following options.</p>",
                    question_hi: "<p>96. <strong>Comprehension :-</strong><br>Between him and Darcy there was a very steady friendship, in spite of great opposition of character. Bingley was endeared to Darcy by the easiness, openness, and ductility of his temper, though no disposition could offer a greater contrast to his own, and though with his own he never appeared dissatisfied. On the strength of Darcy&rsquo;s regard Bingley had the firmest reliance, and of his judgment the highest opinion. In understanding, Darcy was the superior.<br>Bingley was by no means deficient, but Darcy was clever. He was at the same time haughty, reserved, and fastidious, and his manners, though well bred, were not inviting. In that respect his friend had greatly the advantage. Bingley was sure of being liked wherever he appeared, Darcy was continually giving offence.<br>The manner in which they spoke of the Meryton assembly was sufficiently characteristic.<br>Bingley had never met with pleasanter people or prettier girls in&nbsp;his life; everybody had been most kind and attentive to him; there had been no formality, no stiffness; he had soon felt acquainted with all the room; and as to Miss Bennet, he could not conceive an angel more beautiful. Darcy, on the contrary, had seen a collection of people in whom there was little beauty and no fashion, for none of whom he had felt the smallest interest, and from none received either attention or pleasure. Miss Bennet he acknowledged to be pretty, but she smiled too much.<br>Mrs. Hurst and her sister allowed it to be so&mdash;but still they admired her and liked her, and pronounced her to be a sweet girl, and one whom they should not object to know more of.<br>Miss Bennet was therefore established as a sweet girl, and their brother felt authorised by such commendation to think of her as he chose.<br>Select the most appropriate title for the passage from the following options.</p>",
                    options_en: ["<p>Darcy and Bingley</p>", "<p>Darcy and Friends</p>", 
                                "<p>Bingley and Friends</p>", "<p>The Town of Meryton</p>"],
                    options_hi: ["<p>Darcy and Bingley</p>", "<p>Darcy and Friends</p>",
                                "<p>Bingley and Friends</p>", "<p>The Town of Meryton</p>"],
                    solution_en: "<p>96.(a) Darcy and Bingley <br>It can be inferred from the passage that the passage is about the friendship between Darcy and Bingley. Hence, &lsquo;Darcy and Bingley&rsquo; is the most suitable title for the passage.</p>",
                    solution_hi: "<p>96.(a) Darcy and Bingley <br>इस passage से अनुमान लगाया जा सकता है कि यह passage Darcy और Bingley की दोस्ती के बारे में है। इसलिए, &lsquo;Darcy and Bingley&rsquo; passage के लिए सबसे उपयुक्त title है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "97",
                    section: "7",
                    question_en: "<p>97. <strong>Comprehension :-</strong><br>Between him and Darcy there was a very steady friendship, in spite of great opposition of character. Bingley was endeared to Darcy by the easiness, openness, and ductility of his temper, though no disposition could offer a greater contrast to his own, and though with his own he never appeared dissatisfied. On the strength of Darcy&rsquo;s regard Bingley had the firmest reliance, and of his judgment the highest opinion. In understanding, Darcy was the superior.<br>Bingley was by no means deficient, but Darcy was clever. He was at the same time haughty, reserved, and fastidious, and his manners, though well bred, were not inviting. In that respect his friend had greatly the advantage. Bingley was sure of being liked wherever he appeared, Darcy was continually giving offence.<br>The manner in which they spoke of the Meryton assembly was sufficiently characteristic.<br>Bingley had never met with pleasanter people or prettier girls in&nbsp;his life; everybody had been most kind and attentive to him; there had been no formality, no stiffness; he had soon felt acquainted with all the room; and as to Miss Bennet, he could not conceive an angel more beautiful. Darcy, on the contrary, had seen a collection of people in whom there was little beauty and no fashion, for none of whom he had felt the smallest interest, and from none received either attention or pleasure. Miss Bennet he acknowledged to be pretty, but she smiled too much.<br>Mrs. Hurst and her sister allowed it to be so&mdash;but still they admired her and liked her, and pronounced her to be a sweet girl, and one whom they should not object to know more of.<br>Miss Bennet was therefore established as a sweet girl, and their brother felt authorised by such commendation to think of her as he chose.<br>Select the most appropriate meaning of &lsquo;fastidious&rsquo; from the following options.</p>",
                    question_hi: "<p>97. <strong>Comprehension :-</strong><br>Between him and Darcy there was a very steady friendship, in spite of great opposition of character. Bingley was endeared to Darcy by the easiness, openness, and ductility of his temper, though no disposition could offer a greater contrast to his own, and though with his own he never appeared dissatisfied. On the strength of Darcy&rsquo;s regard Bingley had the firmest reliance, and of his judgment the highest opinion. In understanding, Darcy was the superior.<br>Bingley was by no means deficient, but Darcy was clever. He was at the same time haughty, reserved, and fastidious, and his manners, though well bred, were not inviting. In that respect his friend had greatly the advantage. Bingley was sure of being liked wherever he appeared, Darcy was continually giving offence.<br>The manner in which they spoke of the Meryton assembly was sufficiently characteristic.<br>Bingley had never met with pleasanter people or prettier girls in&nbsp;his life; everybody had been most kind and attentive to him; there had been no formality, no stiffness; he had soon felt acquainted with all the room; and as to Miss Bennet, he could not conceive an angel more beautiful. Darcy, on the contrary, had seen a collection of people in whom there was little beauty and no fashion, for none of whom he had felt the smallest interest, and from none received either attention or pleasure. Miss Bennet he acknowledged to be pretty, but she smiled too much.<br>Mrs. Hurst and her sister allowed it to be so&mdash;but still they admired her and liked her, and pronounced her to be a sweet girl, and one whom they should not object to know more of.<br>Miss Bennet was therefore established as a sweet girl, and their brother felt authorised by such commendation to think of her as he chose.<br>Select the most appropriate meaning of &lsquo;fastidious&rsquo; from the following options.</p>",
                    options_en: ["<p>Undemanding</p>", "<p>Concerned about accuracy and detail</p>", 
                                "<p>Queasy</p>", "<p>Prissy</p>"],
                    options_hi: ["<p>Undemanding</p>", "<p>Concerned about accuracy and detail</p>",
                                "<p>Queasy</p>", "<p>Prissy</p>"],
                    solution_en: "<p>97.(b) Concerned about accuracy and detail<br>Fastidious means Concerned about accuracy and detail.</p>",
                    solution_hi: "<p>97.(b) Concerned about accuracy and detail<br>Fastidious का अर्थ है- Concerned about accuracy and detail (सटीकता और विस्तार के बारे में चिंतित होना।)</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "98",
                    section: "7",
                    question_en: "<p>98. <strong>Comprehension :-</strong><br>Between him and Darcy there was a very steady friendship, in spite of great opposition of character. Bingley was endeared to Darcy by the easiness, openness, and ductility of his temper, though no disposition could offer a greater contrast to his own, and though with his own he never appeared dissatisfied. On the strength of Darcy&rsquo;s regard Bingley had the firmest reliance, and of his judgment the highest opinion. In understanding, Darcy was the superior.<br>Bingley was by no means deficient, but Darcy was clever. He was at the same time haughty, reserved, and fastidious, and his manners, though well bred, were not inviting. In that respect his friend had greatly the advantage. Bingley was sure of being liked wherever he appeared, Darcy was continually giving offence.<br>The manner in which they spoke of the Meryton assembly was sufficiently characteristic.<br>Bingley had never met with pleasanter people or prettier girls in&nbsp;his life; everybody had been most kind and attentive to him; there had been no formality, no stiffness; he had soon felt acquainted with all the room; and as to Miss Bennet, he could not conceive an angel more beautiful. Darcy, on the contrary, had seen a collection of people in whom there was little beauty and no fashion, for none of whom he had felt the smallest interest, and from none received either attention or pleasure. Miss Bennet he acknowledged to be pretty, but she smiled too much.<br>Mrs. Hurst and her sister allowed it to be so&mdash;but still they admired her and liked her, and pronounced her to be a sweet girl, and one whom they should not object to know more of.<br>Miss Bennet was therefore established as a sweet girl, and their brother felt authorised by such commendation to think of her as he chose.<br>Who was understanding, clever, haughty and reserved ?</p>",
                    question_hi: "<p>98. <strong>Comprehension :-</strong><br>Between him and Darcy there was a very steady friendship, in spite of great opposition of character. Bingley was endeared to Darcy by the easiness, openness, and ductility of his temper, though no disposition could offer a greater contrast to his own, and though with his own he never appeared dissatisfied. On the strength of Darcy&rsquo;s regard Bingley had the firmest reliance, and of his judgment the highest opinion. In understanding, Darcy was the superior.<br>Bingley was by no means deficient, but Darcy was clever. He was at the same time haughty, reserved, and fastidious, and his manners, though well bred, were not inviting. In that respect his friend had greatly the advantage. Bingley was sure of being liked wherever he appeared, Darcy was continually giving offence.<br>The manner in which they spoke of the Meryton assembly was sufficiently characteristic.<br>Bingley had never met with pleasanter people or prettier girls in&nbsp;his life; everybody had been most kind and attentive to him; there had been no formality, no stiffness; he had soon felt acquainted with all the room; and as to Miss Bennet, he could not conceive an angel more beautiful. Darcy, on the contrary, had seen a collection of people in whom there was little beauty and no fashion, for none of whom he had felt the smallest interest, and from none received either attention or pleasure. Miss Bennet he acknowledged to be pretty, but she smiled too much.<br>Mrs. Hurst and her sister allowed it to be so&mdash;but still they admired her and liked her, and pronounced her to be a sweet girl, and one whom they should not object to know more of.<br>Miss Bennet was therefore established as a sweet girl, and their brother felt authorised by such commendation to think of her as he chose.<br>Who was understanding, clever, haughty and reserved ?</p>",
                    options_en: ["<p>Miss Bennett</p>", "<p>Bingley</p>", 
                                "<p>Mrs. Hurst</p>", "<p>Darcy</p>"],
                    options_hi: ["<p>Miss Bennett</p>", "<p>Bingley</p>",
                                "<p>Mrs. Hurst</p>", "<p>Darcy</p>"],
                    solution_en: "<p>98.(d) Darcy<br>It can be inferred from the passage that Darcy was understanding, clever, haughty and reserved.<br>(Line/s from the Passage- Darcy was clever. He was at the same time haughty, reserved, and fastidious, and his manners, though well bred, were not inviting.)</p>",
                    solution_hi: "<p>98.(d) Darcy<br>Passage से यह अनुमान लगाया जा सकता है कि Darcy समझदार, चतुर और घमंडी था।<br>(Passage से ली गई Line/s- Darcy was clever. He was at the same time haughty, reserved, and fastidious, and his manners, though well bred, were not inviting./Darcy चतुर था। वह एक ही समय में घमंडी, और तेजतर्रार था, और उसके शिष्टाचार, अच्छी नस्ल के थे ।)</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "99",
                    section: "7",
                    question_en: "<p>99. <strong>Comprehension :-</strong><br>Between him and Darcy there was a very steady friendship, in spite of great opposition of character. Bingley was endeared to Darcy by the easiness, openness, and ductility of his temper, though no disposition could offer a greater contrast to his own, and though with his own he never appeared dissatisfied. On the strength of Darcy&rsquo;s regard Bingley had the firmest reliance, and of his judgment the highest opinion. In understanding, Darcy was the superior.<br>Bingley was by no means deficient, but Darcy was clever. He was at the same time haughty, reserved, and fastidious, and his manners, though well bred, were not inviting. In that respect his friend had greatly the advantage. Bingley was sure of being liked wherever he appeared, Darcy was continually giving offence.<br>The manner in which they spoke of the Meryton assembly was sufficiently characteristic.<br>Bingley had never met with pleasanter people or prettier girls in&nbsp;his life; everybody had been most kind and attentive to him; there had been no formality, no stiffness; he had soon felt acquainted with all the room; and as to Miss Bennet, he could not conceive an angel more beautiful. Darcy, on the contrary, had seen a collection of people in whom there was little beauty and no fashion, for none of whom he had felt the smallest interest, and from none received either attention or pleasure. Miss Bennet he acknowledged to be pretty, but she smiled too much.<br>Mrs. Hurst and her sister allowed it to be so&mdash;but still they admired her and liked her, and pronounced her to be a sweet girl, and one whom they should not object to know more of.<br>Miss Bennet was therefore established as a sweet girl, and their brother felt authorised by such commendation to think of her as he chose.<br>Select the synonym of &lsquo;dependence&rsquo; from the passage.</p>",
                    question_hi: "<p>99. <strong>Comprehension :-</strong><br>Between him and Darcy there was a very steady friendship, in spite of great opposition of character. Bingley was endeared to Darcy by the easiness, openness, and ductility of his temper, though no disposition could offer a greater contrast to his own, and though with his own he never appeared dissatisfied. On the strength of Darcy&rsquo;s regard Bingley had the firmest reliance, and of his judgment the highest opinion. In understanding, Darcy was the superior.<br>Bingley was by no means deficient, but Darcy was clever. He was at the same time haughty, reserved, and fastidious, and his manners, though well bred, were not inviting. In that respect his friend had greatly the advantage. Bingley was sure of being liked wherever he appeared, Darcy was continually giving offence.<br>The manner in which they spoke of the Meryton assembly was sufficiently characteristic.<br>Bingley had never met with pleasanter people or prettier girls in&nbsp;his life; everybody had been most kind and attentive to him; there had been no formality, no stiffness; he had soon felt acquainted with all the room; and as to Miss Bennet, he could not conceive an angel more beautiful. Darcy, on the contrary, had seen a collection of people in whom there was little beauty and no fashion, for none of whom he had felt the smallest interest, and from none received either attention or pleasure. Miss Bennet he acknowledged to be pretty, but she smiled too much.<br>Mrs. Hurst and her sister allowed it to be so&mdash;but still they admired her and liked her, and pronounced her to be a sweet girl, and one whom they should not object to know more of.<br>Miss Bennet was therefore established as a sweet girl, and their brother felt authorised by such commendation to think of her as he chose.<br>Select the synonym of &lsquo;dependence&rsquo; from the passage.</p>",
                    options_en: ["<p>Misgiving</p>", "<p>Reliance</p>", 
                                "<p>Relay</p>", "<p>Skepticism</p>"],
                    options_hi: ["<p>Misgiving</p>", "<p>Reliance</p>",
                                "<p>Relay</p>", "<p>Skepticism</p>"],
                    solution_en: "<p>99.(b) Reliance<br><strong>Dependence-</strong> Reliance<br><strong>Skepticism-</strong> the theory that certain knowledge is impossible.<br><strong>Relay-</strong> to tell something you heard.<br><strong>Misgiving-</strong> a feeling of doubt or worry about a future event.</p>",
                    solution_hi: "<p>99.(b) Reliance<br><strong>Dependence</strong>- निर्भरता-Reliance<br><strong>Skepticism-</strong> संदेहवाद- the theory that certain knowledge is impossible.<br><strong>Relay-</strong> प्रसारण- to tell something you heard.<br><strong>Misgiving</strong>- आशंका- a feeling of doubt or worry about a future event.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "100",
                    section: "misc",
                    question_en: "<p>100. <strong>Comprehension :-</strong><br>Between him and Darcy there was a very steady friendship, in spite of great opposition of character. Bingley was endeared to Darcy by the easiness, openness, and ductility of his temper, though no disposition could offer a greater contrast to his own, and though with his own he never appeared dissatisfied. On the strength of Darcy&rsquo;s regard Bingley had the firmest reliance, and of his judgment the highest opinion. In understanding, Darcy was the superior.<br>Bingley was by no means deficient, but Darcy was clever. He was at the same time haughty, reserved, and fastidious, and his manners, though well bred, were not inviting. In that respect his friend had greatly the advantage. Bingley was sure of being liked wherever he appeared, Darcy was continually giving offence.<br>The manner in which they spoke of the Meryton assembly was sufficiently characteristic.<br>Bingley had never met with pleasanter people or prettier girls in&nbsp;his life; everybody had been most kind and attentive to him; there had been no formality, no stiffness; he had soon felt acquainted with all the room; and as to Miss Bennet, he could not conceive an angel more beautiful. Darcy, on the contrary, had seen a collection of people in whom there was little beauty and no fashion, for none of whom he had felt the smallest interest, and from none received either attention or pleasure. Miss Bennet he acknowledged to be pretty, but she smiled too much.<br>Mrs. Hurst and her sister allowed it to be so&mdash;but still they admired her and liked her, and pronounced her to be a sweet girl, and one whom they should not object to know more of.<br>Miss Bennet was therefore established as a sweet girl, and their brother felt authorised by such commendation to think of her as he chose.<br>What is the central theme of the passage ?</p>",
                    question_hi: "<p>100. <strong>Comprehension :-</strong><br>Between him and Darcy there was a very steady friendship, in spite of great opposition of character. Bingley was endeared to Darcy by the easiness, openness, and ductility of his temper, though no disposition could offer a greater contrast to his own, and though with his own he never appeared dissatisfied. On the strength of Darcy&rsquo;s regard Bingley had the firmest reliance, and of his judgment the highest opinion. In understanding, Darcy was the superior.<br>Bingley was by no means deficient, but Darcy was clever. He was at the same time haughty, reserved, and fastidious, and his manners, though well bred, were not inviting. In that respect his friend had greatly the advantage. Bingley was sure of being liked wherever he appeared, Darcy was continually giving offence.<br>The manner in which they spoke of the Meryton assembly was sufficiently characteristic.<br>Bingley had never met with pleasanter people or prettier girls in&nbsp;his life; everybody had been most kind and attentive to him; there had been no formality, no stiffness; he had soon felt acquainted with all the room; and as to Miss Bennet, he could not conceive an angel more beautiful. Darcy, on the contrary, had seen a collection of people in whom there was little beauty and no fashion, for none of whom he had felt the smallest interest, and from none received either attention or pleasure. Miss Bennet he acknowledged to be pretty, but she smiled too much.<br>Mrs. Hurst and her sister allowed it to be so&mdash;but still they admired her and liked her, and pronounced her to be a sweet girl, and one whom they should not object to know more of.<br>Miss Bennet was therefore established as a sweet girl, and their brother felt authorised by such commendation to think of her as he chose.<br>What is the central theme of the passage ?</p>",
                    options_en: ["<p>Darcy and his unlikeable qualities</p>", "<p>The contrast between Bingley and Darcy</p>", 
                                "<p>The similarities between Darcy and Bingley</p>", "<p>Bingley and his attractive qualities</p>"],
                    options_hi: ["<p>Darcy and his unlikeable qualities</p>", "<p>The contrast between Bingley and Darcy</p>",
                                "<p>The similarities between Darcy and Bingley</p>", "<p>Bingley and his attractive qualities</p>"],
                    solution_en: "<p>100.(b) The contrast between Bingley and Darcy<br>It can be inferred that the Central Theme of the Passage is &ldquo;The contrast between Bingley and Darcy&rdquo;.</p>",
                    solution_hi: "<p>100.(b) The contrast between Bingley and Darcy<br>यह अनुमान लगाया जा सकता है कि Passage का केंद्रीय विषय &ldquo;The contrast between Bingley and Darcy&rdquo; है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>