<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">8:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 15</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">15</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 8 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["18"] = {
                name: "General Awareness",
                start: 0,
                end: 13
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="18">General Awareness</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 14,
                end: 14
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "18",
                    question_en: "<p>1. Which of the following passes connects Srinagar and Leh?</p>",
                    question_hi: "<p>1. निम्नलिखित में से कौन सा दर्रा श्रीनगर और लेह को जोड़ता है?</p>",
                    options_en: ["<p>Jelep La</p>", "<p>Nathu La</p>", 
                                "<p>Bara La</p>", "<p>Zoji La</p>"],
                    options_hi: ["<p>जेलेपला दर्रा</p>", "<p>नाथूला दर्रा</p>",
                                "<p>बारा ला दर्रा</p>", "<p>ज़ोजीला दर्रा</p>"],
                    solution_en: "<p>1.(d) <strong>Zoji La pass</strong>. <strong>Nathula</strong> (mountain pass in the Himalayas) - connects Sikkim state of India and Chumbi valley in South Tibet. <strong>Baralacha La Pass</strong> - connects Lahaul region of Himachal Pradesh with Leh district of Ladakh region. <strong>Jelep La </strong>(mountain pass in the Himalayas) - connects the Indian state of Sikkim to the Chumbi Valley in southern Tibet.</p>",
                    solution_hi: "<p>1.(d) <strong>ज़ोजीला दर्रा । नाथुला </strong>(हिमालय में पर्वत दर्रा) - भारत के सिक्किम राज्य और दक्षिण तिब्बत में चुम्बी घाटी को जोड़ता है। <strong>बारालाचा दर्रा -</strong> हिमाचल प्रदेश के लाहौल क्षेत्र को लद्दाख क्षेत्र के लेह जिले को जोड़ता है। <strong>जेलेप ला</strong> (हिमालय में पर्वत दर्रा) - भारतीय राज्य सिक्किम को दक्षिणी तिब्बत में चुम्बी घाटी को जोड़ता है।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "2",
                    section: "18",
                    question_en: "<p>2. Which of the following is the highest peak in India?</p>",
                    question_hi: "<p>2. निम्नलिखित में से कौन भारत की सबसे ऊँची चोटी है?</p>",
                    options_en: ["<p>Kamet</p>", "<p>Anamudi</p>", 
                                "<p>Mt. Everest</p>", "<p>Kanchenjunga</p>"],
                    options_hi: ["<p>कामेत</p>", "<p>अनाईमुडी</p>",
                                "<p>माउंट एवरेस्ट</p>", "<p>कंचनजंगा</p>"],
                    solution_en: "<p>2.(d) <strong>Kanchenjunga</strong> (8,598 m) - 3rd highest mountain in the world situated in Sikkim. <strong>Anamudi</strong> (2,695 m) - The highest peak in the western ghats. Mount Everest (8,848 m) - World&rsquo;s highest mountain. <strong>Kamet</strong> (7,756) - Highest peak in the Zanskar range.</p>",
                    solution_hi: "<p>2.(d) <strong>कंचनजंगा</strong> (8,598 मीटर) - यह विश्व का तीसरा सबसे ऊंचा पर्वत है जो कि सिक्किम में स्थित है। अनाईमुडी (2,695 मीटर) - पश्चिमी घाट की सबसे ऊँची चोटी है । <strong>माउंट एवरेस्ट </strong>(8,848 मीटर) - विश्व का सबसे ऊँचा पर्वत है । <strong>कामेत</strong> (7,756) - ज़ांस्कर पर्वतमाला की सबसे ऊँची चोटी है ।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "3",
                    section: "18",
                    question_en: "<p>3. Which among the following hills has Indian desert to its western margin?</p>",
                    question_hi: "<p>3. निम्नलिखित पहाड़ियों में से किसकी पश्चिमी सीमा पर भारतीय रेगिस्तान है?</p>",
                    options_en: ["<p>Palani</p>", "<p>Kanchenjunga</p>", 
                                "<p>Aravali</p>", "<p>Dhupgarh</p>"],
                    options_hi: ["<p>पलानी</p>", "<p>कंचनजंगा</p>",
                                "<p>अरावली</p>", "<p>धूपगढ़</p>"],
                    solution_en: "<p>3.(c)<strong> Aravali hills - </strong>Oldest mountain range of India and its tallest peak is Guru Shikhar (1722 m).<strong> Palani hills (Tamil Nadu) </strong>-Kodaikanal is a popular hill station located , Eastward extension of the Western Ghats, in southwestern. <strong>Kanchenjunga </strong>(Kumbhkaran Lungu), 3rd highest (8598 m) mountain peak in the world of the Himalayas range, located - Sikkim and Nepal. <strong>Dhupgarh</strong> (1350 m) - Highest peak of Satpura Range.</p>",
                    solution_hi: "<p>3.(c) <strong>अरावली पहाड़ियाँ</strong> - भारत की सबसे पुरानी पर्वत श्रृंखला और इसकी सबसे ऊँची चोटी गुरु शिखर (1722 मीटर) है। <strong>पलानी हिल्स</strong> (तमिलनाडु) -कोडाईकनाल एक लोकप्रिय हिल स्टेशन है, जो पश्चिमी घाट के पूर्वी विस्तार, दक्षिण-पश्चिम में स्थित है। <strong>कंचनजंगा (कुंभकरण लुंगु), </strong>हिमालय पर्वत श्रृंखला की दुनिया की तीसरी सबसे ऊंची (8598 मीटर) पर्वत चोटी है, स्थित - सिक्किम और नेपाल । <strong>धूपगढ़</strong> (1350 मीटर) - सतपुड़ा पर्वतमाला की सबसे ऊँची चोटी है।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "4",
                    section: "18",
                    question_en: "<p>4. Which of the following hills in India are known as the Blue Mountains?</p>",
                    question_hi: "<p>4. भारत में निम्नलिखित में से कौन सी पहाड़ियों को &lsquo;ब्लू माउंटेन&rsquo; के नाम से जाना जाता है?</p>",
                    options_en: ["<p>Garo Hills</p>", "<p>Khasi Hills</p>", 
                                "<p>Nilgiri Hills</p>", "<p>Shivalik Hills</p>"],
                    options_hi: ["<p>गारो की पहाड़ियां</p>", "<p>खासी की पहाड़ियां</p>",
                                "<p>नीलगिरी की पहाड़ियां</p>", "<p>शिवालिक की पहाड़ियां</p>"],
                    solution_en: "<p>4.(c) <strong>Nilgiri hills. </strong>The Nilgiri Mountains are a range of mountains spanning the states of Tamil Nadu and Kerala in southern India as part of the Western Ghats. Highest peak Doddabetta (2623m). Khasi Hills - Part of the Garo-Khasi-Jaintia range Meghalaya. Highest peak - Lum Shillong (1968m). Garo Hills - It is also a part of Garo-Khasi-Jaintia range Meghalaya. Highest Peak - Nokrek (1412m) The Shivalik hills (Churia hill) - are a low mountain range in the southern foothills of the Himalayas.</p>",
                    solution_hi: "<p>4.(c) <strong>नीलगिरि की पहाड़ियाँ</strong>। नीलगिरि पर्वत पश्चिमी घाट के हिस्से के रूप में दक्षिणी भारत में तमिलनाडु और केरल राज्यों में फैले पहाड़ों की एक श्रृंखला है। <strong>सबसे ऊंची</strong> चोटी डोड्डाबेट्टा (2623 मीटर)। <strong>खासी पहाड़ियाँ</strong> - गारो-खासी-जयंतिया पर्वत श्रृंखला मेघालय का हिस्सा। सबसे ऊंची चोटी - लुम शिलांग (1968 मीटर)।<strong> गारो हिल्स -</strong> यह भी गारो-खासी-जयंतिया श्रेणी मेघालय का एक हिस्सा है। सबसे ऊँची चोटी - नोकरेक (1412 मीटर) <strong>शिवालिक पहाड़ियाँ</strong> (चुरिया पहाड़ी) - हिमालय की दक्षिणी तराई में एक निचली पर्वत श्रृंखला है।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "5",
                    section: "18",
                    question_en: "5. Girnar Hills of Junagadh, Gujarat is famous for ____ Mangoes.  ",
                    question_hi: "5. जूनागढ़, गुजरात की गिरनार पहाड़ियाँ _____ आमों के लिए प्रसिद्ध हैं।",
                    options_en: [" Alphonso ", " Totapuri ", 
                                " Dasheri ", " Kesar "],
                    options_hi: [" अल्फांसो", " तोतापुरी",
                                " दशेरी", " केसर "],
                    solution_en: "<p>5.(d) <strong>Kesar (Gir Kesar mango) -</strong> It originated in the foothills of the Girnar mountains in the Junagadh district of Gujarat. <strong>Alphonso mangoes </strong>were introduced to India by the Portuguese military strategist Afonso de Albuquerque and it is produced in Ratnagiri (Maharashtra).<strong> The Dasheri mango</strong> is famous in Lucknow (Uttar Pradesh). <strong>Totapuri</strong> (Sandersha) - famous in Bangalore. <strong>Safeda</strong> (Banganpali or Benishan Mango) produced in Andhra Pradesh.</p>",
                    solution_hi: "<p>5.(d) <strong>केसर (गिर केसर आम) - </strong>इसकी उत्पत्ति गुजरात के जूनागढ़ जिले में गिरनार पहाड़ों की तराई में हुई थी। <strong>अल्फांसो आम</strong> को पुर्तगाली सैन्य रणनीतिकार अफोंसो डी अल्बुकर्क द्वारा भारत में लाया गया था और इसका उत्पादन रत्नागिरी (महाराष्ट्र) में किया जाता है। द<strong>शहरी आम लखनऊ </strong>(उत्तर प्रदेश) में प्रसिद्ध है। <strong>तोतापुरी</strong> (संदेरशा) - बेंगलुरु में प्रसिद्ध है। सफेदा (बंगानपाली या बेनीशान आम) आंध्र प्रदेश में उत्पाद होता है।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "6",
                    section: "18",
                    question_en: "6. Which one of the following passes is located at the crest of the Western Ghats and connects Mumbai and Pune. ",
                    question_hi: "6. निम्नलिखित में से कौन सा दर्रा पश्चिमी घाट के शिखर पर स्थित है और मुंबई और पुणे को जोड़ता है।",
                    options_en: [" Asirgarh Fort Pass ", " Bhor Ghat Pass ", 
                                " Amba Ghat Pass", " Tamhini Ghat Pass "],
                    options_hi: [" असीरगढ़ किला दर्रा ", " भोर घाट दर्रा",
                                " अम्बा घाट दर्रा", " तम्हिनी घाट दर्रा"],
                    solution_en: "<p>6.(b) <strong>Bhor Ghat Pass i</strong>s a mountain pass between Palasdari and Khandala located at the crest of the Western Ghats. <strong>Thal Ghat Pass i</strong>s between Nasik and Mumbai.<strong> Palghat Pass i</strong>s located between the Nilgiri Hills (Tamil Nadu) and the Anaimalai Hills (Kerala).</p>",
                    solution_hi: "<p>6.(b)<strong> भोर घाट दर्रा</strong> पश्चिमी घाट के शिखर पर स्थित पलासदारी और खंडाला के बीच एक पहाड़ी दर्रा है।<strong> थाल घाट दर्रा</strong> नासिक और मुंबई के बीच है। <strong>पालघाट दर्रा</strong> नीलगिरि की पहाड़ियों (तमिलनाडु) और अन्नामलाई की पहाड़ियों (केरल) के बीच स्थित है।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "7",
                    section: "18",
                    question_en: "<p>7. Which of the following is known as the highest battlefield of the world?</p>",
                    question_hi: "<p>7. निम्नलिखित में से किसे विश्व के सबसे ऊंचे युद्धक्षेत्र के रूप में जाना जाता है?</p>",
                    options_en: ["<p>Nanda Devi glacier</p>", "<p>Siachen glacier</p>", 
                                "<p>Rathong glacier</p>", "<p>Gangotri glacier</p>"],
                    options_hi: ["<p>नंदा देवी ग्लेशियर</p>", "<p>सियाचिन ग्लेशियर</p>",
                                "<p>राथोंग ग्लेशियर</p>", "<p>गंगोत्री ग्लेशियर</p>"],
                    solution_en: "<p>7.(b<strong>) Siachen glacier</strong>: Located in the eastern Karakoram range (Himalayas) where the Line of control between India and Pakistan ends. <strong>Nubra river originates</strong> from it. Second-Longest glacier in the World\'s Non-Polar. <strong>Nanda Devi Glacier -</strong> Chamoli (Uttarakhand). <strong>Rathong Glacie</strong>r (Sikkim) - It is the source of Rathong river Extends from Rathong La (North) to the top of the Chourikiang valley (South). <strong>Gangotri Glacier</strong> - Uttarkashi (Uttarakhand).</p>",
                    solution_hi: "<p>7.(b) <strong>सियाचिन ग्लेशियर: </strong>पूर्वी काराकोरम रेंज (हिमालय) में स्थित है जहां भारत और पाकिस्तान के बीच नियंत्रण रेखा समाप्त होती है।<strong> नुब्रा नदी </strong>यहीं से निकलती है। विश्व के गैर-ध्रुवीय क्षेत्र में दूसरा सबसे लंबा ग्लेशियर। नंदा देवी ग्लेशियर - चमोली (उत्तराखंड)।<strong> राथोंग ग्लेशियर</strong> (सिक्किम) - यह राथोंग नदी का स्रोत है जो<strong> रथोंग दर्रा </strong>(उत्तर) से चौरीकियांग घाटी (दक्षिण) के शीर्ष तक फैली है। <strong>गंगोत्री ग्लेशियर</strong> - उत्तरकाशी (उत्तराखंड)।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "8",
                    section: "18",
                    question_en: "8. Which one of the following is the second highest peak in the Himalayas?",
                    question_hi: "8. निम्नलिखित में से कौन हिमालय की दूसरी सबसे ऊँची चोटी है?",
                    options_en: [" Mount Everest ", " K2 ", 
                                " Dhaulagiri", " Makalu "],
                    options_hi: [" माउंट एवरेस्ट", " K2 ",
                                " धौलागिरी", " मकालु"],
                    solution_en: "<p>8.(b) <strong>K2 (</strong>Godwin Austen - 8,611m)<strong>. Mount Everest </strong>(called Sagarmatha in Nepal) - Highest peak, 8,849m, located in Nepal. <strong>Kanchenjunga</strong> - Third highest peak, 8,586m, located in India (Sikkim). Mount Makalu - 8481m, located in the Mahalangur range of Nepal. <strong>Dhaulagiri</strong> - 8167m, situated in Eastern Nepal.</p>",
                    solution_hi: "<p>8.(b) <strong>K2</strong> (गॉडविन ऑस्टेन - 8,611 मी)।<strong> माउंट एवरेस्ट (</strong>नेपाल में सागरमाथा कहा जाता है) - सबसे ऊंची चोटी, 8,849 मीटर, नेपाल में स्थित है। <strong>कंचनजंगा</strong> - तीसरी सबसे ऊंची चोटी, 8,586 मीटर, भारत (सिक्किम) में स्थित है। <strong>माउंट मकालू -</strong> 8481 मीटर, नेपाल के महलंगुर रेंज में स्थित है। <strong>धौलागिरि</strong> - 8167 मीटर, पूर्वी नेपाल में स्थित है।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "9",
                    section: "18",
                    question_en: "<p>9. Which of the following is the highest peak of the Nilgiri hills?</p>",
                    question_hi: "<p>9. निम्नलिखित में से कौन सी नीलगिरि पहाड़ियों की सबसे ऊँची चोटी है?</p>",
                    options_en: ["<p>Kalsubai</p>", "<p>Arma konda</p>", 
                                "<p>Anamudi</p>", "<p>Doddabetta</p>"],
                    options_hi: ["<p>कालसूबाई</p>", "<p>अरमा कोंडा</p>",
                                "<p>अनामुदी</p>", "<p>डोड्डाबेट्टा</p>"],
                    solution_en: "<p>9.(d) <strong>Doddabetta (2,637 metres)</strong> - Tamil Nadu. <strong>Kalsubai</strong> (1646 metres) - Western Ghats, Maharashtra. <strong>Arma konda</strong> (Sitamma konda, 1680 metres) - Godavari River basin, Eastern ghats, Andhra Pradesh. <strong>Anamudi</strong> (2695 metres) - Highest peak in Western Ghats, Kerala.<strong> Highest Mountain peak</strong>: Kanchenjunga (8586 metres) - Sikkim (India), Mount Everest (8848.86 metres) - Nepal <strong>(World)</strong></p>",
                    solution_hi: "<p>9.(d) <strong>डोड्डाबेट्टा</strong> (2,637 मीटर) - तमिलनाडु। <strong>कलसुबाई</strong> (1646 मीटर) - पश्चिमी घाट, महाराष्ट्र। अरमा कोंडा (सीतम्मा कोंडा, 1680 मीटर) - गोदावरी नदी बेसिन, पूर्वी घाट, आंध्र प्रदेश। <strong>अनाइमुडी</strong> (2695 मीटर) - पश्चिमी घाट, केरल की<strong> सबसे ऊँची चोटी</strong> है। <strong>सबसे ऊँची पर्वत चोटी:</strong> कंचनजंगा (8586 मीटर) - सिक्किम <strong>(भारत),</strong> माउंट एवरेस्ट (8848.86 मीटर) - नेपाल (<strong>विश्व</strong>)</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "10",
                    section: "18",
                    question_en: "<p>10. Kamet, Namcha Barwa, Gurla Mandhata are the names of :</p>",
                    question_hi: "<p>10. कामेट, नमचा बरवा, गुरला मांधाता किसके नाम हैं?</p>",
                    options_en: ["<p>volcanoes in Himalayas</p>", "<p>mountain peaks in Himalayas</p>", 
                                "<p>tribes living in Himalayas</p>", "<p>rivers flowing through Himalayas</p>"],
                    options_hi: ["<p>हिमालय में ज्वालामुखी</p>", "<p>हिमालय की पर्वत चोटियाँ</p>",
                                "<p>हिमालय में रहने वाली जनजातियाँ</p>", "<p>हिमालय से बहने वाली नदियाँ</p>"],
                    solution_en: "<p>10.(b) <strong>Mountain peaks in Himalayas</strong>. The Himalayas are divided into three ranges: The Inner Himalayas, The Middle Himalayas, and The Outer Himalayas. <strong>Himalaya mountain peaks in India: </strong>Nanda Devi (7,816m) and Kamet (7,756m) in Uttarakhand, Namcha Barwa (7,756m) - Arunachal Pradesh, Kanchenjunga (8598m) - Sikkim.</p>",
                    solution_hi: "<p>10.(b) <strong>हिमालय की पर्वत चोटियाँ।</strong> हिमालय को तीन श्रेणियों में विभाजित किया गया है: आंतरिक हिमालय, मध्य हिमालय और बाह्य हिमालय। <strong>भारत में हिमालय पर्वत की चोटियाँ:</strong> उत्तराखंड में नंदा देवी (7,816 मीटर) और कामेत (7,756 मीटर), नमचा बरवा (7,756 मीटर) - अरुणाचल प्रदेश, कंचनजंगा (8598 मीटर) - सिक्किम।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "11",
                    section: "18",
                    question_en: "<p>11. The Aravalli Range does not pass through_____ state of India.</p>",
                    question_hi: "<p>11. अरावली पर्वत श्रृंखला भारत के ______ राज्य से होकर नहीं गुजरती है।</p>",
                    options_en: ["<p>Haryana</p>", "<p>Rajasthan</p>", 
                                "<p>Gujarat</p>", "<p>Maharashtra</p>"],
                    options_hi: ["<p>हरियाणा</p>", "<p>राजस्थान</p>",
                                "<p>गुजरात</p>", "<p>महाराष्ट्र</p>"],
                    solution_en: "<p>11.(d) <strong>Maharashtra.</strong> Aravalli Range (Residual fold mountain) - A mountain range in Northern-Western India (south-west direction) starting near Delhi, passing through southern Haryana, Rajasthan and ending in Gujarat. Highest peak - Guru Shikhar (1,722 m) on Mount Abu (Rajasthan).</p>",
                    solution_hi: "<p>11.(d) <strong>महाराष्ट्र।</strong> अरावली पर्वत श्रृंखला (अवशिष्ट वलित पर्वत) - उत्तरी-पश्चिमी भारत (दक्षिण-पश्चिम दिशा) में एक पर्वत श्रृंखला जो दिल्ली के पास से प्रारम्भ होकर दक्षिणी हरियाणा, राजस्थान से होकर गुजरात में समाप्त होती है। सबसे ऊँची चोटी - गुरु शिखर (1,722 मीटर) जो माउंट आबू (राजस्थान) पर स्थित है।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "12",
                    section: "18",
                    question_en: "12. The Tibetan Plateau is the best example of a/an __________.",
                    question_hi: "12. तिब्बत का  पठार एक ___________ का सबसे अच्छा उदाहरण है।",
                    options_en: [" Volcanic Plateau", " Erosional Plateau", 
                                " Doomed Plateau", " Intermontane Plateau"],
                    options_hi: [" ज्वालामुखीय पठार", " अपरदन  पठार",
                                " पहाड़ी पठार", " अंतर-पर्वतीय पठार"],
                    solution_en: "<p>12.(d)<strong> Intermontane Plateau. </strong>Plateau is an elevated flat land. It is a flat - topped tableland standing above the surrounding area. The Deccan plateau is one of the oldest plateaus in India. The Tibet Plateau is the highest plateau in the world with a height of 4000 to 6000 meters above sea level.</p>",
                    solution_hi: "<p>12.(d)<strong> अंतर-पर्वतीय पठार ।</strong> पठार एक ऊँची समतल भूमि है। यह आसपास के क्षेत्र के ऊपर स्थित एक सपाट शीर्ष वाला पठार है। दक्कन का पठार भारत के सबसे पुराने पठारों में से एक है। तिब्बत का पठार दुनिया का सबसे ऊँचा पठार है जिसकी समुद्र तल से ऊँचाई 4000 से 6000 मीटर है।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "13",
                    section: "18",
                    question_en: "<p>13. The highest point of the Aravalli Range is:</p>",
                    question_hi: "<p>13. अरावली पर्वतमाला का उच्चतम बिंदु कौन सा है?</p>",
                    options_en: ["<p>Kamet</p>", "<p>Guru Shikhar</p>", 
                                "<p>Om Parvat</p>", "<p>Angle Peak</p>"],
                    options_hi: ["<p>कामेत</p>", "<p>गुरु शिखर</p>",
                                "<p>ओम पर्वत</p>", "<p>एंगल चोटी</p>"],
                    solution_en: "<p>13.(b) Guru Shikhar (1722 m) - peak on Mount Abu (Rajasthan). <strong>Kamet</strong> (7756 m) - the second highest mountain in the Garhwal region of Uttarakhand, India. Om Parvat (6191 m) - situated in Malpa - Mansarovar route of Uttarakhand. Some important mountains in India - Nanda Devi - Uttrakhand, Dhupgarh - Madhya Pradesh, Trishul - Uttarakhand, Mahendragiri - Odisha.</p>",
                    solution_hi: "<p>13.(b) <strong>गुरु शिखर (1722 मीटर)</strong> - माउंट आबू (राजस्थान) की चोटी है। <strong>कामेत</strong> (7756 मीटर) - भारत के उत्तराखंड के गढ़वाल क्षेत्र का दूसरा सबसे ऊँचा पर्वत है। <strong>ओम पर्वत</strong> (6191 मीटर) - उत्तराखंड के मालपा - मानसरोवर मार्ग पर स्थित है। भारत में कुछ महत्वपूर्ण पर्वत - नंदा देवी - उत्तराखंड, धूपगढ़ - मध्य प्रदेश, त्रिशूल - उत्तराखंड, महेंद्रगिरि - उड़ीसा।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "14",
                    section: "18",
                    question_en: "<p>14. Dhupgarh is the highest peak of the___range.</p>",
                    question_hi: "<p>14. धूपगढ़ ___श्रेणी की सबसे ऊँची चोटी है।</p>",
                    options_en: ["<p>Vindhya</p>", "<p>Aravalli</p>", 
                                "<p>Satpura</p>", "<p>Ajanta</p>"],
                    options_hi: ["<p>विंध्य</p>", "<p>अरावली</p>",
                                "<p>सतपुड़ा</p>", "<p>अजंता</p>"],
                    solution_en: "<p>14.(c) <strong>Satpura. Mount Dhupgarh </strong>is the highest point (1,350 metres) in the Mahadeo Hills (Satpura Range), Madhya Pradesh. Located in Pachmarhi in the Hoshangabad district. Highest peak of the Vindhya Range - <strong>Sad-Bhawna Shikhar (Goodwill Peak) </strong>in Madhya Pradesh. The highest point of the Aravalli Range - <strong>Guru Shikhar </strong>(Rajasthan).</p>",
                    solution_hi: "<p>14.(c) <strong>सतपुड़ा। माउंट धूपगढ़, </strong>मध्य प्रदेश मे स्थित महादेव पहाड़ियों (सतपुड़ा श्रेणी) का उच्चतम बिंदु (1,350 मीटर) है। यह होशंगाबाद जिले के पचमढ़ी में स्थित है। विंध्य पर्वतमाला की सबसे ऊँची चोटी - <strong>सद-भावना शिखर </strong>(Goodwill Peak) मध्य प्रदेश में है । अरावली पर्वतमाला का उच्चतम बिंदु <strong>- गुरु शिखर</strong> (राजस्थान)।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "15",
                    section: "misc",
                    question_en: "<p>15. Diphu pass is situated in:</p>",
                    question_hi: "<p>15. दीफू दर्रा अवस्थित कहाँ है?</p>",
                    options_en: ["<p>Arunachal Himalayas</p>", "<p>Pir Panjal range in Himalayas</p>", 
                                "<p>Darjeeling and Sikkim Himalayas</p>", "<p>Kashmir or North western Himalayas</p>"],
                    options_hi: ["<p>अरुणाचल हिमालय</p>", "<p>हिमालय में पीर पंजाल रेंज</p>",
                                "<p>दार्जिलिंग और सिक्किम हिमालय</p>", "<p>कश्मीर या उत्तर पश्चिमी हिमालय</p>"],
                    solution_en: "<p>15.(a)<strong> Arunachal Himalayas. Diphu Pass: </strong>It lies on the McMahon Line. Other Passes in Arunachal Pradesh: Bom Di La, Dihang Pass, Yong Yap Pass, Kumajwang Pass, Hapungan Pass, Chankan Pass. Rohtang Pass (Himachal Pradesh) - Situated in the Pir Panjal range of the great Himalayas. <strong>Major Passes: </strong>Leh-Ladakh - Bara-lacha La, Khardung La, Zoji La. Jammu and Kashmir - Karakoram Pass, Pir Panjal Pass, Burzil Pass.</p>",
                    solution_hi: "<p>15.(a) <strong>अरुणाचल हिमालय।</strong> <strong>दीफू दर्रा</strong>: यह मैकमोहन रेखा पर स्थित है। अरुणाचल प्रदेश में अन्य दर्रे: बोमडिला, दिहांग दर्रा, योंग याप दर्रा, कुमजवांग दर्रा, हापुंगन दर्रा, चानकन दर्रा। रोहतांग दर्रा (हिमाचल प्रदेश) - महान हिमालय की पीर पंजाल श्रृंखला में स्थित है। <strong>प्रमुख दर्रे: </strong>लेह-लद्दाख - बारा-लाचा ला, खारदुंग ला, ज़ोजी ला। जम्मू और कश्मीर - काराकोरम दर्रा, पीर पंजाल दर्रा, बुर्जिल दर्रा।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>