<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">15:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 15</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">15</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 15 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 0,
                end: 13
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 14,
                end: 14
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "17",
                    question_en: "<p>1. A ring is sold at three successive discounts of 10%, 20% and 30%. If the marked price of the ring is ₹20,000, find its net selling price.</p>",
                    question_hi: "<p>1. एक अंगूठी 10%, 20% और 30% की तीन क्रमिक छूट पर बेची जाती है। यदि अंगूठी का अंकित मूल्य ₹20,000 है, तो इसका निवल विक्रय मूल्य ज्ञात कीजिए।</p>",
                    options_en: ["<p>Rs. 10000</p>", "<p>Rs. 10020</p>", 
                                "<p>Rs. 10080</p>", "<p>Rs. 9000</p>"],
                    options_hi: ["<p>10000 रुपए</p>", "<p>10020 रुपए</p>",
                                "<p>10080 रुपए</p>", "<p>9000 रुपए</p>"],
                    solution_en: "<p>1.(c)<br>According to question,<br>Selling price of the ring = 20000 &times; <math display=\"inline\"><mfrac><mrow><mn>90</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>80</mn><mn>100</mn></mfrac><mo>&#215;</mo><mfrac><mn>70</mn><mn>100</mn></mfrac></math> = 10080 Rs.</p>",
                    solution_hi: "<p>1.(c)<br>प्रश्न के अनुसार,<br>अंगूठी का विक्रय मूल्य = 20000 &times; <math display=\"inline\"><mfrac><mrow><mn>90</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>80</mn><mn>100</mn></mfrac><mo>&#215;</mo><mfrac><mn>70</mn><mn>100</mn></mfrac></math> = 10080 रु.</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "2",
                    section: "17",
                    question_en: "2. Deepak marks the price of his table 50 percent above the cost price. He gives some discount on it and earns profit of 10 percent. What is the discount percentage ?",
                    question_hi: "2. दीपक अपनी मेज का मूल्य, क्रय मूल्य से 50 प्रतिशत अधिक अंकित करता है। वह इस पर कुछ छूट देता है और 10 प्रतिशत का लाभ कमाता है। छूट प्रतिशत कितना है ?",
                    options_en: [" 26.67 percent", " 20 percent", 
                                " 16.67 percent", " 33.33 percent"],
                    options_hi: [" 26.67 प्रतिशत", " 20 प्रतिशत",
                                " 16.67 प्रतिशत", " 33.33 प्रतिशत"],
                    solution_en: "2.(a)<br />Let the cost price of the table be 100 Rs.<br />According to question,<br />Marked price = <math display=\"inline\"><mfrac><mrow><mn>150</mn></mrow><mrow><mn>100</mn></mrow></mfrac><mo>×</mo><mn>100</mn></math> = 150 Rs.<br />Selling price of table = 100 × <math display=\"inline\"><mfrac><mrow><mn>110</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 110 Rs.<br />Therefore, discount % = <math display=\"inline\"><mfrac><mrow><mn>150</mn><mi>&nbsp;</mi><mo>-</mo><mi>&nbsp;</mi><mn>110</mn></mrow><mrow><mn>150</mn></mrow></mfrac><mo>×</mo><mn>100</mn></math> = 26.67%",
                    solution_hi: "2.(a)<br />माना मेज का लागत मूल्य 100 रुपये है।<br />प्रश्न के अनुसार,<br />अंकित मूल्य = <math display=\"inline\"><mfrac><mrow><mn>150</mn></mrow><mrow><mn>100</mn></mrow></mfrac><mo>×</mo><mn>100</mn></math> = 150 रु.<br />मेज का विक्रय मूल्य = 100 × <math display=\"inline\"><mfrac><mrow><mn>110</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 110 रु.<br />इसलिए, छूट % = <math display=\"inline\"><mfrac><mrow><mn>150</mn><mi>&nbsp;</mi><mo>-</mo><mi>&nbsp;</mi><mn>110</mn></mrow><mrow><mn>150</mn></mrow></mfrac><mo>×</mo><mn>100</mn></math> = 26.67%",
                    correct: "a<br /> ",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "3",
                    section: "17",
                    question_en: "<p>3. Pankaj purchases a watch for Rs. 800. He sells the watch at a discount of 20 percent and earns a profit of 20 percent . What is the marked price of the watch ?</p>",
                    question_hi: "<p>3. पंकज ने 800 रुपए में एक घड़ी खरीदी। वह घड़ी को 20 प्रतिशत की छूट पर बेचता है और 20 प्रतिशत का लाभ कमाता है। घड़ी का अंकित मूल्य कितना है ?</p>",
                    options_en: ["<p>Rs. 1000</p>", "<p>Rs. 840</p>", 
                                "<p>Rs. 1200</p>", "<p>Rs. 900</p>"],
                    options_hi: ["<p>1000 रुपए</p>", "<p>840 रुपए</p>",
                                "<p>1200 रुपए</p>", "<p>900 रुपए</p>"],
                    solution_en: "<p>3.(c) <math display=\"inline\"><mfrac><mrow><mi mathvariant=\"bold-italic\">&#160;</mi><mi mathvariant=\"bold-italic\">C</mi><mo>.</mo><mi mathvariant=\"bold-italic\">P</mi><mo>.</mo></mrow><mrow><mi mathvariant=\"bold-italic\">M</mi><mo>.</mo><mi mathvariant=\"bold-italic\">P</mi><mo>.</mo></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>100</mn><mo>-</mo><mi>D</mi><mo>%</mo></mrow><mrow><mn>100</mn><mo>+</mo><mi>P</mi><mo>%</mo></mrow></mfrac></math><br><math display=\"inline\"><mfrac><mrow><mi mathvariant=\"bold-italic\">&#160;</mi><mn>800</mn></mrow><mrow><mi mathvariant=\"bold-italic\">M</mi><mo>.</mo><mi mathvariant=\"bold-italic\">P</mi><mo>.</mo></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>100</mn><mo>-</mo><mn>20</mn><mo>%</mo></mrow><mrow><mn>100</mn><mo>+</mo><mn>20</mn><mo>%</mo></mrow></mfrac><mo>&#8658;</mo><mfrac><mn>800</mn><mrow><mi>M</mi><mo>.</mo><mi>P</mi><mo>.</mo></mrow></mfrac><mo>=</mo><mfrac><mrow><mn>80</mn><mo>%</mo></mrow><mrow><mn>120</mn><mo>%</mo></mrow></mfrac></math><br>So, marked price = <math display=\"inline\"><mfrac><mrow><mn>120</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>800</mn></mrow><mrow><mn>80</mn></mrow></mfrac></math> = Rs.1200</p>",
                    solution_hi: "<p>3.(c) <math style=\"font-family: Verdana;\" display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2325;&#2381;&#2352;&#2351;</mi><mo>&#160;</mo><mi>&#2350;&#2370;&#2354;&#2381;&#2351;</mi></mrow><mrow><mi>&#2309;&#2306;&#2325;&#2367;&#2340;</mi><mo>&#160;</mo><mi>&#2350;&#2370;&#2354;&#2381;&#2351;</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>100</mn><mo>-</mo><mi>D</mi><mo>%</mo></mrow><mrow><mn>100</mn><mo>+</mo><mi>P</mi><mo>%</mo></mrow></mfrac></math><br><math style=\"font-family: Verdana;\" display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi mathvariant=\"bold-italic\">&#160;</mi><mn>800</mn></mrow><mrow><mi>&#2309;&#2306;&#2325;&#2367;&#2340;</mi><mo>&#160;</mo><mi>&#2350;&#2370;&#2354;&#2381;&#2351;</mi><mo>.</mo></mrow></mfrac></math> = <math style=\"font-family: Verdana;\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>100</mn><mo>-</mo><mn>20</mn><mo>%</mo></mrow><mrow><mn>100</mn><mo>+</mo><mn>20</mn><mo>%</mo></mrow></mfrac><mo>&#8658;</mo><mfrac><mn>800</mn><mrow><mi>&#2309;&#2306;&#2325;&#2367;&#2340;</mi><mo>&#160;</mo><mi>&#2350;&#2370;&#2354;&#2381;&#2351;</mi><mo>.</mo></mrow></mfrac><mo>=</mo><mfrac><mrow><mn>80</mn><mo>%</mo></mrow><mrow><mn>120</mn><mo>%</mo></mrow></mfrac></math><br>अतः, अंकित मूल्य = <math display=\"inline\"><mfrac><mrow><mn>120</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>800</mn></mrow><mrow><mn>80</mn></mrow></mfrac></math> = Rs.1200</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "4",
                    section: "17",
                    question_en: "4. The selling price of an article is Rs. 3410. If discount of 45 percent is given, then what will be the marked price of the article ?",
                    question_hi: "4. एक वस्तु का विक्रय मूल्य 3410 रुपए है। यदि उस वस्तु पर 45 प्रतिशत की छूट दी जाती है, तो उस वस्तु का अंकित मूल्य कितना होगा ?",
                    options_en: [" Rs. 6400", " Rs. 6000", 
                                " Rs. 6500", " Rs. 6200"],
                    options_hi: [" 6400 रुपए", " 6000 रुपए",
                                " 6500 रुपए", " 6200 रुपए"],
                    solution_en: "4.(d) marked price = <math display=\"inline\"><mfrac><mrow><mn>3410</mn><mi>&nbsp;</mi><mo>×</mo><mi>&nbsp;</mi><mn>100</mn></mrow><mrow><mn>55</mn></mrow></mfrac></math> = Rs. 6200",
                    solution_hi: "4.(d) अंकित मूल्य = <math display=\"inline\"><mfrac><mrow><mn>3410</mn><mi>&nbsp;</mi><mo>×</mo><mi>&nbsp;</mi><mn>100</mn></mrow><mrow><mn>55</mn></mrow></mfrac></math> = Rs. 6200",
                    correct: "d<br /> ",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "5",
                    section: "17",
                    question_en: "<p>5. If two successive discounts of 50 percent and 10 percent are given, then what will be the ratio of marked price to selling price ?</p>",
                    question_hi: "<p>5. यदि 50 प्रतिशत और 10 प्रतिशत की दो क्रमिक छूटें दी जाती हैं, तो अंकित मूल्य और विक्रय मूल्य का अनुपात कितना होगा ?</p>",
                    options_en: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>9</mn><mn>20</mn></mfrac></math></p>", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>11</mn><mn>20</mn></mfrac></math></p>", 
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>20</mn><mn>11</mn></mfrac></math></p>", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>20</mn><mn>9</mn></mfrac></math></p>"],
                    options_hi: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>9</mn><mn>20</mn></mfrac></math></p>", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>11</mn><mn>20</mn></mfrac></math></p>",
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>20</mn><mn>11</mn></mfrac></math></p>", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>20</mn><mn>9</mn></mfrac></math></p>"],
                    solution_en: "<p>5.(d)<br>Let the marked price be <math display=\"inline\"><mi>x</mi></math> units.<br>SP = <math display=\"inline\"><mi>x</mi></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac><mo>&#215;</mo><mfrac><mn>9</mn><mn>10</mn></mfrac></math> <br>Required ratio = <math display=\"inline\"><mi>x</mi></math> :&nbsp;<math display=\"inline\"><mi>x</mi></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac><mo>&#215;</mo><mfrac><mn>9</mn><mn>10</mn></mfrac></math> = 20 : 9</p>",
                    solution_hi: "<p>5.(d)<br>माना अंकित मूल्य <math display=\"inline\"><mi>x</mi></math> इकाई है।<br>विक्रय मूल्य = <math display=\"inline\"><mi>x</mi></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac><mo>&#215;</mo><mfrac><mn>9</mn><mn>10</mn></mfrac></math><br>आवश्यक अनुपात = <math display=\"inline\"><mi>x</mi></math> :&nbsp;<math display=\"inline\"><mi>x</mi></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac><mo>&#215;</mo><mfrac><mn>9</mn><mn>10</mn></mfrac></math> = 20 : 9</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "6",
                    section: "17",
                    question_en: "6. After giving a discount of 45 percent a cycle is sold for Rs. 5060. What is the marked price of the cycle ?",
                    question_hi: "6. 45 प्रतिशत की छूट के बाद एक साइकिल 5060 रुपये में बेची जाती है। साइकिल का अंकित मूल्य कितना है ?",
                    options_en: [" Rs. 8000", " Rs. 9200", 
                                " Rs. 9600", " Rs. 8500"],
                    options_hi: [" 8000 रुपए", " 9200 रुपए",
                                " 9600 रुपए", " 8500 रुपए"],
                    solution_en: "6.(b)<br />According to question,<br />Marked price = 5060 × <math display=\"inline\"><mfrac><mrow><mn>100</mn></mrow><mrow><mo>(</mo><mn>100</mn><mi>&nbsp;</mi><mo>-</mo><mi>&nbsp;</mi><mn>45</mn><mo>)</mo></mrow></mfrac></math> = 9200 Rs.",
                    solution_hi: "6.(b)<br />प्रश्न के अनुसार,<br />अंकित मूल्य = 5060 × <math display=\"inline\"><mfrac><mrow><mn>100</mn></mrow><mrow><mo>(</mo><mn>100</mn><mi>&nbsp;</mi><mo>-</mo><mi>&nbsp;</mi><mn>45</mn><mo>)</mo></mrow></mfrac></math> = 9200 रु.",
                    correct: "b<br /> ",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "7",
                    section: "17",
                    question_en: "7. Find a single discount equivalent to two successive discounts of 25% and 10%.",
                    question_hi: "7. 25% और 10% की दो क्रमिक छूटों के समतुल्य एकल छूट ज्ञात कीजिए।",
                    options_en: [" 31.9%", " 35.5% ", 
                                " 32.5% ", " 38.4%"],
                    options_hi: [" 31.9%", " 35.5% ",
                                " 32.5% ", " 38.4%"],
                    solution_en: "7.(c) Single discount = 25 + 10 - <math display=\"inline\"><mfrac><mrow><mn>25</mn><mi>&nbsp;</mi><mo>×</mo><mi>&nbsp;</mi><mn>10</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math><br />                                        = 35 - 2.5 = 32.5%",
                    solution_hi: "7.(c) एकल छूट = 25 + 10 - <math display=\"inline\"><mfrac><mrow><mn>25</mn><mi>&nbsp;</mi><mo>×</mo><mi>&nbsp;</mi><mn>10</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math><br />                                = 35 - 2.5 = 32.5%",
                    correct: "c<br /> ",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "8",
                    section: "17",
                    question_en: "8. After giving a discount of 25 percent an article is sold for Rs 5550. What is the marked price of the article?",
                    question_hi: "8. 25 प्रतिशत की छूट देने के बाद एक वस्तु 5550 रुपए में बेची जाती है। वस्तु का अंकित मूल्य कितना है?",
                    options_en: [" Rs 7200", " Rs 7400", 
                                " Rs 7500", " Rs 7000"],
                    options_hi: [" 7200 रुपए ", " 7400 रुपए",
                                " 7500 रुपए", " 7000 रुपए"],
                    solution_en: "8.(b)<br />Marked price = 5550 × <math display=\"inline\"><mfrac><mrow><mn>100</mn></mrow><mrow><mn>75</mn></mrow></mfrac></math> = Rs. 7400",
                    solution_hi: "8.(b)<br />अंकित मूल्य = 5550 × <math display=\"inline\"><mfrac><mrow><mn>100</mn></mrow><mrow><mn>75</mn></mrow></mfrac></math> =  रु. 7400",
                    correct: "b<br /> ",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "9",
                    section: "17",
                    question_en: "<p>9. A shopkeeper allows a discount of 18% on the marked price of an article such that the selling price becomes ₹574. Find the marked price of the article.</p>",
                    question_hi: "<p>9. एक दुकानदार एक वस्तु के अंकित मूल्य पर 18% की छूट इस प्रकार देता है कि विक्रय मूल्य ₹574 हो जाता है। वस्तु का अंकित मूल्य ज्ञात कीजिए।</p>",
                    options_en: ["<p>₹700</p>", "<p>₹850</p>", 
                                "<p>₹840</p>", "<p>₹780</p>"],
                    options_hi: ["<p>₹700</p>", "<p>₹850</p>",
                                "<p>₹840</p>", "<p>₹780</p>"],
                    solution_en: "<p>9.(a) Let the Marked price be <math display=\"inline\"><mi>x</mi></math><br>According to question,<br><math display=\"inline\"><mo>&#8658;</mo></math> x &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>41</mn><mn>50</mn></mfrac></math> = 574<br><math display=\"inline\"><mo>&#8658;</mo></math> x = 574 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>50</mn><mn>41</mn></mfrac></math>= ₹700</p>",
                    solution_hi: "<p>9.(a) माना अंकित मूल्य <math display=\"inline\"><mi>x</mi></math> है<br>प्रश्न के अनुसार,<br><math display=\"inline\"><mo>&#8658;</mo></math> x &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>41</mn><mn>50</mn></mfrac></math> = 574<br><math display=\"inline\"><mo>&#8658;</mo></math> x = 574 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>50</mn><mn>41</mn></mfrac></math>= ₹700</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "10",
                    section: "17",
                    question_en: "10. The cost of manufacture of a tape recorder is ₹3500. The manufacturer fixes the marked price 30% above the cost price and allows a discount in such as to get a profit of 5%. Find the discount percentage.",
                    question_hi: "10. एक टेप रिकॉर्डर की निर्माण लागत ₹ 3500 है। निर्माता टेप रिकॉर्डर को क्रय मूल्य से 30% अधिक पर अंकित करता है और 5% का लाभ प्राप्त करने के लिए छूट देता है। छूट प्रतिशत ज्ञात कीजिए।",
                    options_en: [" 17.5% ", " 19.23% ", 
                                " 15.14% ", " 21.15%"],
                    options_hi: [" 17.5% ", " 19.23% ",
                                " 15.14% ", " 21.15%"],
                    solution_en: "10.(b)<br />Let cost price of tape recorder = 100<br />Marked price = 100 × 130% = 130<br />For 5% profit <br />Selling price = 100 × 105% = 105<br />Then, discount = 130 - 105 = 25<br />Hence, required discount % = <math display=\"inline\"><mfrac><mrow><mn>25</mn></mrow><mrow><mn>130</mn></mrow></mfrac></math> × 100 = 19.23%",
                    solution_hi: "10.(b)<br />माना टेप रिकॉर्डर का लागत मूल्य = 100 है<br />अंकित मूल्य  = 100 × 130% = 130<br />5% लाभ के लिए<br />विक्रय मूल्य = 100 × 105% = 105<br />फिर, छूट = 130 - 105 = 25<br />अतः, आवश्यक छूट %  = <math display=\"inline\"><mfrac><mrow><mn>25</mn></mrow><mrow><mn>130</mn></mrow></mfrac></math> × 100 = 19.23%",
                    correct: "b<br /> ",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "11",
                    section: "17",
                    question_en: "11. If the ratio of marked price and selling price of an article is 3 : 2, then what is the discount percentage?",
                    question_hi: "11. यदि एक वस्तु के अंकित मूल्य और विक्रय मूल्य का अनुपात 3 : 2 है, तो छूट प्रतिशत कितना है?",
                    options_en: [" 100 percent ", " 33.33 percent ", 
                                " 50 percent ", " 75 percent"],
                    options_hi: [" 100 प्रतिशत", " 33.33 प्रतिशत",
                                " 50 प्रतिशत", " 75 प्रतिशत"],
                    solution_en: "11.(b)<br />discount % = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> ×100 = 33.33%",
                    solution_hi: "11.(b)<br />छूट % = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> ×100 = 33.33%",
                    correct: "b<br /> ",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "12",
                    section: "17",
                    question_en: "12. After a discount of 33 percent an article is sold for Rs. 2546. What is the marked price of the article?",
                    question_hi: "12. 33 प्रतिशत की छूट के बाद एक वस्तु 2546 रुपए में बेची जाती है। वस्तु का अंकित मूल्य कितना है?",
                    options_en: [" Rs. 3800", " Rs. 3500 ", 
                                " Rs. 4000", " Rs. 3600"],
                    options_hi: [" 3800 रुपए", " 3500 रुपए",
                                " 4000 रुपए", " 3600 रुपए"],
                    solution_en: "12.(a)<br />Marked price = <math display=\"inline\"><mfrac><mrow><mn>2546</mn><mi>&nbsp;</mi><mo>×</mo><mi>&nbsp;</mi><mn>100</mn></mrow><mrow><mo>(</mo><mn>100</mn><mi>&nbsp;</mi><mo>-</mo><mi>&nbsp;</mi><mn>33</mn><mo>)</mo></mrow></mfrac></math> = 3800 Rs.",
                    solution_hi: "12.(a)<br />अंकित मूल्य = <math display=\"inline\"><mfrac><mrow><mn>2546</mn><mi>&nbsp;</mi><mo>×</mo><mi>&nbsp;</mi><mn>100</mn></mrow><mrow><mo>(</mo><mn>100</mn><mi>&nbsp;</mi><mo>-</mo><mi>&nbsp;</mi><mn>33</mn><mo>)</mo></mrow></mfrac></math> = 3800 रुपए",
                    correct: "a<br /> ",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "13",
                    section: "17",
                    question_en: "<p>13. The selling price of a car is Rs 11440. If the car is sold at a discount of 45 percent, what was the marked price of the car ?</p>",
                    question_hi: "<p>13. एक कार का विक्रय मूल्य 11440 रुपए है। यदि कार को 45 प्रतिशत की छूट पर बेचा जाता है, तो कार का अंकित मूल्य कितना था|</p>",
                    options_en: ["<p>Rs 20300</p>", "<p>Rs 21050</p>", 
                                "<p>Rs 20800</p>", "<p>Rs 19850</p>"],
                    options_hi: ["<p>20300 रुपए</p>", "<p>21050 रुपए</p>",
                                "<p>20800 रुपए</p>", "<p>19850 रुपए</p>"],
                    solution_en: "<p>13.(c)<br>According to question,<br>Marked price of car = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>11440</mn><mo>&#215;</mo><mn>100</mn></mrow><mrow><mo>(</mo><mn>100</mn><mo>-</mo><mn>45</mn><mo>)</mo></mrow></mfrac></math> = 20800 Rs.</p>",
                    solution_hi: "<p>13.(c)<br>प्रश्न के अनुसार,<br>कार का अंकित मूल्य = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>11440</mn><mo>&#215;</mo><mn>100</mn></mrow><mrow><mo>(</mo><mn>100</mn><mo>-</mo><mn>45</mn><mo>)</mo></mrow></mfrac></math> = 20800 रु.</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "14",
                    section: "17",
                    question_en: "14. The selling price of an article is Rs. 1482. If discount of 22 percent is given, then what will be the marked price of the article ?",
                    question_hi: "14.  एक वस्तु का विक्रय मूल्य 1482 रुपए। है। यदि 22 प्रतिशत की छूट दी जाती है, तो वस्तु का अंकित मूल्य कितना होगा ?",
                    options_en: [" Rs.2000", " Rs.2100", 
                                " Rs.1800", " Rs.1900"],
                    options_hi: [" 2000 रुपए ", " 2100 रुपए",
                                " 1800 रुपए", " 1900 रुपए"],
                    solution_en: "14.(d)<br />According to question,<br />Marked price of the article = <math display=\"inline\"><mfrac><mrow><mn>1482</mn><mi>&nbsp;</mi><mo>×</mo><mi>&nbsp;</mi><mn>100</mn></mrow><mrow><mo>(</mo><mn>100</mn><mi>&nbsp;</mi><mo>-</mo><mi>&nbsp;</mi><mn>22</mn><mo>)</mo></mrow></mfrac></math> = 1900 Rs.",
                    solution_hi: "14.(d)<br />प्रश्न के अनुसार,<br />वस्तु का अंकित मूल्य = <math display=\"inline\"><mfrac><mrow><mn>1482</mn><mi>&nbsp;</mi><mo>×</mo><mi>&nbsp;</mi><mn>100</mn></mrow><mrow><mo>(</mo><mn>100</mn><mi>&nbsp;</mi><mo>-</mo><mi>&nbsp;</mi><mn>22</mn><mo>)</mo></mrow></mfrac></math> = 1900 Rs.",
                    correct: "d<br /> ",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "15",
                    section: "misc",
                    question_en: "<p>15. The marked price of a computer is Rs 25000 and the discount percentage is 15 percent. If the selling price of the computer is Rs 12750, what is the additional discount percentage ?</p>",
                    question_hi: "<p>15. एक कंप्यूटर का अंकित मूल्य 25000 रुपए और छूट प्रतिशत 15 प्रतिशत है। यदि कंप्यूटर का विक्रय मूल्य 12750 रुपए है, तो अतिरिक्त छूट प्रतिशत कितना है ?</p>",
                    options_en: ["<p>35 percent</p>", "<p>40 percent</p>", 
                                "<p>30 percent</p>", "<p>25 percent</p>"],
                    options_hi: ["<p>35 प्रतिशत</p>", "<p>40 प्रतिशत</p>",
                                "<p>30 प्रतिशत</p>", "<p>25 प्रतिशत</p>"],
                    solution_en: "<p>15.(b)<br>According to the question,<br>25000 &times; <math display=\"inline\"><mfrac><mrow><mn>17</mn></mrow><mrow><mn>20</mn></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>100</mn><mo>-</mo><mi>x</mi></mrow><mn>100</mn></mfrac></math> = 12750<br>100 - <math display=\"inline\"><mi>x</mi></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><mo>&#215;</mo><mn>12750</mn></mrow><mn>425</mn></mfrac></math><br>100 - <math display=\"inline\"><mi>x</mi></math> = 60 <br><math display=\"inline\"><mi>x</mi></math> = 40<br>Hence, required discount % = 40%</p>",
                    solution_hi: "<p>15.(b)<br>प्रश्न के अनुसार,<br>25000 &times; <math display=\"inline\"><mfrac><mrow><mn>17</mn></mrow><mrow><mn>20</mn></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>100</mn><mo>-</mo><mi>x</mi></mrow><mn>100</mn></mfrac></math> = 12750<br>100 - <math display=\"inline\"><mi>x</mi></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><mo>&#215;</mo><mn>12750</mn></mrow><mn>425</mn></mfrac></math> <br>100 - <math display=\"inline\"><mi>x</mi></math> = 60 <br><math display=\"inline\"><mi>x</mi></math> = 40<br>अतः, आवश्यक छूट % = 40%</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>