<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">5:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 15</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">15</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 5 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["18"] = {
                name: "General Awareness",
                start: 0,
                end: 13
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="18">General Awareness</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 14,
                end: 14
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "18",
                    question_en: "<p>1. Which of the following rivers is an east flowing river?</p>",
                    question_hi: "<p>1. निम्नलिखित में से कौन-सी नदी पूर्व की ओर बहने वाली नदी है?</p>",
                    options_en: ["<p>Mahi river</p>", "<p>Narmada river</p>", 
                                "<p>Godavari river</p>", "<p>Tapi river</p>"],
                    options_hi: ["<p>माही नदी</p>", "<p>नर्मदा नदी</p>",
                                "<p>गोदावरी नदी</p>", "<p>तापी नदी</p>"],
                    solution_en: "<p>1.(c) <strong>Godavari river.</strong> It is the largest Peninsular river system. It is also called the Dakshin Ganga. It rises in the Nasik district of Maharashtra and discharges its water into the Bay of Bengal. It is 1,465 km long with a catchment area spreading over 3.13 lakh sq . km. The Penganga, the Indravati, the Pranhita, and the Manjra are its principal tributaries. The Mahi (Origin, Northern slope of Vindhyas), Narmada (Origin, Amarkantak), and Tapi (Origin, Betul) rivers flow westward, draining into the Arabian Sea.</p>",
                    solution_hi: "<p>1.(c) <strong>गोदावरी नदी। </strong>यह सबसे बड़ी प्रायद्वीपीय नदी प्रणाली है। इसे दक्षिण गंगा भी कहा जाता है। इसका उद्गम महाराष्ट्र के नासिक जिले से होता है और अपना पानी बंगाल की खाड़ी में गिराती है। यह 1,465 किलोमीटर लंबी है और इसका जलग्रहण क्षेत्र 3.13 लाख वर्ग किलोमीटर में फैला हुआ है। पेनगंगा, इंद्रावती, प्राणहिता और मंजरा इसकी प्रमुख सहायक नदियाँ हैं। माही (उत्पत्ति, विंध्य की उत्तरी ढलान), नर्मदा (उत्पत्ति, अमरकंटक) और तापी (उत्पत्ति, बैतूल) नदियाँ पश्चिम की ओर प्रवाहित होती हैं और अरब सागर में मिल जाती हैं।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "18",
                    question_en: "<p>2. &lsquo;Bhagirathi&rsquo; and &lsquo;Alaknanda&rsquo; rivers confluence at:</p>",
                    question_hi: "<p>2. \'भागीरथी\' और \'अलकनंदा\' नदियों का संगम कहाँ होता है?</p>",
                    options_en: ["<p>Joshimath</p>", "<p>Haridwar</p>", 
                                "<p>Karanprayag</p>", "<p>Devaprayag</p>"],
                    options_hi: ["<p>जोशीमठ</p>", "<p>हरिद्वार</p>",
                                "<p>कर्णप्रयाग</p>", "<p>देवप्रयाग</p>"],
                    solution_en: "<p>2.(d) <strong>Devaprayag. </strong>The Five Prayags are sacred river confluences in Uttarakhand. At Devaprayag, the Bhagirathi and Alaknanda rivers meet. At Rudraprayag, the Mandakini and Alaknanda rivers converge. At Nandaprayag, the Nandakini joins the Alaknanda. At Karnaprayag, the Pindar meets the Alaknanda, and at Vishnuprayag, the Dhauliganga merges with the Alaknanda.</p>",
                    solution_hi: "<p>2.(d) <strong>देवप्रयाग।</strong> पंच प्रयाग उत्तराखंड में पवित्र नदी संगम हैं। देवप्रयाग में भागीरथी और अलकनंदा नदियाँ मिलती हैं। रुद्रप्रयाग में मंदाकिनी और अलकनंदा नदियों का संगम हैं। नंदप्रयाग में नंदाकिनी, अलकनंदा से मिलती है। कर्णप्रयाग में पिंडार, अलकनंदा से मिलती है और विष्णुप्रयाग में धौलीगंगा, अलकनंदा में मिलती है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "18",
                    question_en: "<p>3. Which of the following is an inosilicate amphibole mineral used in highway construction and as railroad ballast?</p>",
                    question_hi: "<p>3. निम्नलिखित में से कौन सा एक इनोसिलिकेट ऐम्फिबोल खनिज है जिसका उपयोग राजमार्ग निर्माण और रेलमार्ग बैलास्ट के रूप में किया जाता है?</p>",
                    options_en: ["<p>Shale</p>", "<p>Olivine</p>", 
                                "<p>Geyserite</p>", "<p>Hornblende</p>"],
                    options_hi: ["<p>शेल (Shale)</p>", "<p>ऑलिवीन (Olivine)</p>",
                                "<p>गाइजराइट (Geyserite)</p>", "<p>हॉर्नब्लेन्ड (Hornblende)</p>"],
                    solution_en: "<p>3.(d) <strong>Hornblende: </strong>It is an inosilicate amphibole mineral. Uses: Highway construction: As an aggregate for road bases and surfaces. Railroad ballast: To provide drainage and stability under railroad tracks.</p>",
                    solution_hi: "<p>3.(d) <strong>हॉर्नब्लेन्ड </strong>(Hornblende): यह एक इनोसिलिकेट ऐम्फिबोल खनिज है। उपयोग: राजमार्ग निर्माण: सड़क के आधार और सतहों के लिए एक समुच्चय के रूप में। रेलमार्ग बैलास्ट: रेल की पटरियों के नीचे जल निकासी और स्थिरता प्रदान करने के लिए।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "18",
                    question_en: "<p>4. According to Census 2011, which district displays the highest population?</p>",
                    question_hi: "<p>4. जनगणना 2011 के अनुसार, सर्वाधिक जनसंख्या वाला जिला कौन सा है ?</p>",
                    options_en: ["<p>Kollam</p>", "<p>Ajmer</p>", 
                                "<p>Thane</p>", "<p>Pune</p>"],
                    options_hi: ["<p>कोल्लम</p>", "<p>अजमेर</p>",
                                "<p>ठाणे</p>", "<p>पुणे</p>"],
                    solution_en: "<p>4.(c) <strong>Thane</strong> is a district located in the state of Maharashtra. It is often referred to as the \"City of Lakes\" due to its large number of water bodies. According to the 2011 census, the population of India was 1,210,854,977. Of this total population, 51.54% were male and 48.46% were female. Furthermore, 68.84% of the population lived in rural areas, while 31.16% resided in urban areas.</p>",
                    solution_hi: "<p>4.(c) <strong>ठाणे </strong>महाराष्ट्र राज्य में स्थित एक जिला है। इसे अक्सर अपनी बड़ी संख्या में जल निकायों के कारण \"झीलों का शहर\" कहा जाता है। 2011 की जनगणना के अनुसार, भारत की जनसंख्या 1,210,854,977 थी। इस कुल जनसंख्या में से 51.54% पुरुष और 48.46% महिलाएँ थीं। इसके अलावा, 68.84% आबादी ग्रामीण क्षेत्रों में रहती थी, जबकि 31.16% शहरी क्षेत्रों में रहती थी।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "18",
                    question_en: "<p>5. Which state of India has the maximum number of large dams?</p>",
                    question_hi: "<p>5. भारत के किस राज्य में बड़े बांधों की संख्या सबसे अधिक है?</p>",
                    options_en: ["<p>Maharashtra</p>", "<p>Madhya Pradesh</p>", 
                                "<p>Odisha</p>", "<p>Rajasthan</p>"],
                    options_hi: ["<p>महाराष्ट्र</p>", "<p>मध्य प्रदेश</p>",
                                "<p>ओडिशा</p>", "<p>राजस्थान</p>"],
                    solution_en: "<p>5.(a) <strong>Maharashtra</strong> leads in the number of large dams, followed by Madhya Pradesh and Gujarat. Major Dams in Maharashtra: Jayakwadi Dam, Koyna Dam, Radhanagari Dam, Bhivpuri dam, and Warna dam. India&rsquo;s first dam is the Kallanai built on the Cauvery river by King Karikalan of the Chola dynasty around 2,000 years ago. After independence, the country focused on large dams, with notable projects including the Hirakud dam (1957), Gandhisagar dam (1960), Bhakra-Nangal dam (1963), and Nagarjuna Sagar dam (1967).</p>",
                    solution_hi: "<p>5.(a) <strong>महाराष्ट्र </strong>बड़े बांधों की संख्या में अग्रणी है, इसके बाद मध्य प्रदेश और गुजरात का स्थान है। महाराष्ट्र में प्रमुख बांध: जयकवाड़ी बांध, कोयना बांध, राधानगरी बांध, भिवपुरी बांध और वार्ना बांध। भारत का पहला बांध कल्लनई है जिसे चोल वंश के राजा करिकालन ने करीब 2,000 वर्ष पहले कावेरी नदी पर बनवाया था। स्वतंत्रता के बाद, देश ने बड़े बांधों पर ध्यान केंद्रित किया, जिनमें हीराकुंड बांध (1957), गांधीसागर बांध (1960), भाखड़ा-नांगल बांध (1963) और नागार्जुन सागर बांध (1967) जैसी प्रसिद्ध परियोजनाएं शामिल हैं।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "18",
                    question_en: "<p>6. The Wardha is a tributary of which of the following rivers?</p>",
                    question_hi: "<p>6. वर्धा निम्नलिखित में से किस नदी की सहायक नदी है?</p>",
                    options_en: ["<p>Mahanadi river</p>", "<p>Tapi river</p>", 
                                "<p>Godavari river</p>", "<p>Narmada river</p>"],
                    options_hi: ["<p>महानदी</p>", "<p>तापी नदी</p>",
                                "<p>गोदावरी नदी</p>", "<p>नर्मदा नदी</p>"],
                    solution_en: "<p>6.(c) <strong>Godavari river. </strong>The Godavari, India\'s second-largest river, originates near Trimbakeshwar in Nashik, Maharashtra, and flows east across the Deccan Plateau to the Bay of Bengal. Known as Dakshin Ganga. Its main tributaries are the Pravara, Purna, Manjra and Indravati rivers. The Wardha River rises in the Satpura Range near Multai, Betul district in Madhya Pradesh before entering Maharashtra.</p>",
                    solution_hi: "<p>6.(c) <strong>गोदावरी नदी।</strong> गोदावरी, भारत की दूसरी सबसे बड़ी नदी, नासिक, महाराष्ट्र में त्र्यंबकेश्वर के निकट उत्पन्न होती है और दक्कन पठार के पार पूर्व की ओर बहकर बंगाल की खाड़ी में मिलती है। इसे दक्षिण गंगा के नाम से जाना जाता है। इसकी मुख्य सहायक नदियाँ प्रवरा, पूर्णा, मंजरा और इंद्रावती नदियाँ हैं। वर्धा नदी महाराष्ट्र में प्रवेश करने से पहले मध्य प्रदेश के बैतूल जिले के मुलताई के पास सतपुड़ा पर्वत श्रृंखला से निकलती है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "18",
                    question_en: "<p>7. As of Financial year 2019, which of the following states has the highest road density in India?</p>",
                    question_hi: "<p>7. वित्तीय वर्ष 2019 तक प्राप्त जानकारी के अनुसार, भारत में निम्नलिखित में से किस राज्य का सड़क घनत्व सबसे अधिक है?</p>",
                    options_en: ["<p>Haryana</p>", "<p>Tamil Nadu</p>", 
                                "<p>Kerala</p>", "<p>Punjab</p>"],
                    options_hi: ["<p>हरियाणा</p>", "<p>तमिलनाडु</p>",
                                "<p>केरल</p>", "<p>पंजाब</p>"],
                    solution_en: "<p>7.(c) <strong>Kerala.</strong> As of the financial year 2019, Kerala had the highest road density among states, with 6.7 thousand kilometers of roads per one thousand square kilometers. Among union territories, Chandigarh had the highest road density in India, with over 22.6 thousand kilometers per one thousand square kilometers. Road density refers to the total length of all roads in a country, including highways, national roads, and city or rural roads, compared to the country\'s land area.</p>",
                    solution_hi: "<p>7.(c) <strong>केरल।</strong> वित्तीय वर्ष 2019 तक, केरल में राज्यों के बीच सबसे अधिक सड़क घनत्व था, जहाँ प्रति एक हज़ार वर्ग किलोमीटर में 6.7 हज़ार किलोमीटर सड़कें थीं। केंद्र शासित प्रदेशों में, चंडीगढ़ में भारत में सबसे अधिक सड़क घनत्व था, जहाँ प्रति एक हज़ार वर्ग किलोमीटर में 22.6 हज़ार किलोमीटर से अधिक सड़कें थीं। सड़क घनत्व देश के भूमि क्षेत्र की तुलना में राजमार्गों, राष्ट्रीय सड़कों और शहर या ग्रामीण सड़कों सहित देश में सभी सड़कों की कुल लंबाई को संदर्भित करता है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "18",
                    question_en: "8. Which of the following is the best definition of ecological efficiency? ",
                    question_hi: "8. निम्नलिखित में से पारिस्थितिक दक्षता की सबसे अच्छी परिभाषा कौन-सी है?",
                    options_en: [" The amount of energy utilised at different trophic levels in a food chain  ", " The amount of energy stored at different trophic levels in a food chain ", 
                                " The ratio between the mass and the energy flow at different trophic levels in a food chain  ", " The ratio between energy flows at different points in a food chain"],
                    options_hi: [" खाद्य शृंखला में विभिन्न पोषी स्तरों पर संचित ऊर्जा की मात्रा ", " खाद्य शृंखला में विभिन्न पोषी स्तरों पर उपयोग की जाने वाली ऊर्जा की मात्रा ",
                                " खाद्य शृंखला में विभिन्न पोषी स्तरों पर द्रव्यमान और ऊर्जा प्रवाह के बीच का अनुपात ", " खाद्य शृंखला में विभिन्न बिंदुओं पर ऊर्जा प्रवाह के बीच का अनुपात"],
                    solution_en: "8.(d) Ecological efficiency describes the efficiency with which energy is transferred from one trophic level to the next. The number of trophic levels in the grazing food chain is restricted as the transfer of energy follows 10 percent law - only 10 percent of the energy is transferred to each trophic level from the lower trophic level.",
                    solution_hi: "8.(d) पारिस्थितिक दक्षता उस दक्षता का वर्णन करती है जिसके साथ ऊर्जा एक पोषी स्तर से दूसरे पोषी स्तर तक स्थानांतरित होती है। चरागाह खाद्य श्रृंखला में पोषी स्तरों की संख्या को सीमित करता है क्योंकि ऊर्जा का हस्तांतरण 10 प्रतिशत नियम का पालन करता है - केवल 10 प्रतिशत ऊर्जा निचले पोषी स्तर से प्रत्येक पोषी स्तर पर स्थानांतरित होती है।",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "18",
                    question_en: "<p>9. In which group of states does the Luni River flow?</p>",
                    question_hi: "<p>9. लूनी नदी किस राज्य-समूह में बहती है?</p>",
                    options_en: ["<p>Bihar and West Bengal</p>", "<p>Andhra Pradesh and Telangana</p>", 
                                "<p>Rajasthan and Gujarat</p>", "<p>Maharashtra and Karnataka</p>"],
                    options_hi: ["<p>बिहार और पश्चिम बंगाल</p>", "<p>आंध्र प्रदेश और तेलंगाना</p>",
                                "<p>राजस्थान और गुजरात</p>", "<p>महाराष्ट्र और कर्नाटक</p>"],
                    solution_en: "<p>9.(c) <strong>Rajasthan and Gujarat. </strong>The Luni River begins near Pushkar valley in two branches called the Saraswati and the Sabarmati. These two branches merge at Govindgarh, and from there, the river flows out of the Aravalli as the Luni. It continues westward until Telwara, then turns southwest to join the Rann of Kutch (Gujarat).</p>",
                    solution_hi: "<p>9.(c) <strong>राजस्थान और गुजरात। </strong>लूनी नदी पुष्कर घाटी के पास से दो शाखाएँ निकलती है जिन्हें सरस्वती और साबरमती कहा जाता है। ये दोनों शाखाएँ गोविंदगढ़ में मिल जाती हैं और वहाँ से अरावली नदी से लूनी के रूप में निकलती है। यह तेलवाड़ा तक पश्चिम की ओर प्रवाहित होती है, इसके बाद दक्षिण-पश्चिम की ओर मुड़कर कच्छ के रण (गुजरात) में मिल जाती है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "18",
                    question_en: "<p>10. Which of the following is the largest inhabited riverine island in the world?</p>",
                    question_hi: "<p>10. निम्नलिखित में से कौन-सा विश्व का सबसे बड़ा आबाद नदी द्वीप है?</p>",
                    options_en: ["<p>Peacock</p>", "<p>Majuli</p>", 
                                "<p>Bhavani</p>", "<p>Lakshadweep</p>"],
                    options_hi: ["<p>पिकॉक</p>", "<p>माजुली</p>",
                                "<p>भवानी</p>", "<p>लक्षद्वीप</p>"],
                    solution_en: "<p>10.(b) <strong>Majuli </strong>is a famous island in the River Brahmaputra, flowing in the state of Assam. It is the biggest district island in the country and is quite close to Jorhat. The island is formed by the Brahmaputra River in the south and the Kherkutia Xuti, an anabranch of the Brahmaputra, joined by the Subansiri River in the north.</p>",
                    solution_hi: "<p>10.(b) <strong>माजुली </strong>असम राज्य में प्रवाहित होने वाली ब्रह्मपुत्र नदी पर स्थित एक प्रसिद्ध नदी द्वीप है। यह देश का सबसे बड़ा द्वीप जिला है और यह जोरहाट के काफी नजदीक है। यह द्वीप दक्षिण में ब्रह्मपुत्र नदी और उत्तर में सुबनसिरी नदी से मिलने वाली ब्रह्मपुत्र की एक शाखा खेरकुटिया ज़ुती द्वारा निर्मित है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "18",
                    question_en: "<p>11. What was the main objective of the Second Green Revolution in India?</p>",
                    question_hi: "<p>11. भारत में द्वितीय हरित क्रांति का प्रमुख उद्देश्य क्या था?</p>",
                    options_en: ["<p>Ensuring food security in the western states</p>", "<p>Promoting conventional agricultural practices</p>", 
                                "<p>Focusing on holistic development in agriculture</p>", "<p>Increasing the use of chemical fertilizers and pesticides</p>"],
                    options_hi: ["<p>पश्चिमी राज्यों में खाद्य सुरक्षा सुनिश्चित करना</p>", "<p>पारंपरिक कृषि पद्धतियों को बढ़ावा देना</p>",
                                "<p>कृषि में समग्र विकास पर ध्यान केंद्रित करना</p>", "<p>रासायनिक उर्वरकों एवं कीटनाशकों का उपयोग बढ़ाना</p>"],
                    solution_en: "<p>11.(c) <strong>Focusing on holistic development in agriculture.</strong> In the second phase of the green revolution (mid-1970s to mid-1980s), the HYV technology spread to a larger number of states and benefited more variety of crops. The spread of green revolution technology enabled India to achieve self-sufficiency in food grains.</p>",
                    solution_hi: "<p>11.(c) <strong>कृषि में समग्र विकास पर ध्यान केंद्रित करना।</strong> हरित क्रांति के दूसरे चरण (1970 के दशक के मध्य से 1980 के दशक के मध्य तक) में, HYV तकनीक अत्यधिक संख्या में राज्यों में विस्तार हुआ और इससे कई तरह की फसलों को लाभ हुआ। हरित क्रांति तकनीक के प्रसार ने भारत को खाद्यान्न में आत्मनिर्भरता हासिल करने में सक्षम बनाया।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "18",
                    question_en: "<p>12. A dense mass of water drops on smoke or dust particles in the lower atmosphere layers is referred to as:</p>",
                    question_hi: "<p>12. वायुमंडल की निचली परतों में धुएं या धूल के कणों पर जल की जल की बूंदों के सघन द्रव्यमान को क्या कहा जाता है?</p>",
                    options_en: ["<p>mist</p>", "<p>blizzard</p>", 
                                "<p>frost</p>", "<p>smog</p>"],
                    options_hi: ["<p>कुहासा</p>", "<p>बर्फानी तूफान</p>",
                                "<p>पाला</p>", "<p>धूम कोहरा</p>"],
                    solution_en: "<p>12.(d) <strong>smog. </strong>Fog forms when an air mass with high water vapor suddenly cools, causing condensation on fine dust particles. It is essentially a cloud at or near the ground, leading to poor visibility, often reduced to zero. Fog is denser and thicker than mist. Fog reduces visibility to less than one kilometer, while mist reduces visibility to one to two kilometers. Frost forms on cold surfaces when condensation takes place below freezing point (00C), i.e. the dew point is at or below the freezing point.</p>",
                    solution_hi: "<p>12.(d) <strong>धूम कोहरा।</strong> कोहरा तब बनता है जब अधिक जलवाष्प वाली वायु अचानक ठंडी हो जाती है, जिससे सूक्ष्म धूल के कणों पर संघनन होता है। यह अनिवार्य रूप से स्थल पर या उसके पास एक बादल सा बन जाता है, जिससे दृश्यता खराब हो जाती है, जो अक्सर शून्य तक कम हो जाती है। कोहरा धुंध की तुलना में अधिक घना होता है। कोहरा दृश्यता को एक किलोमीटर से कम तक कम कर सकता है, जबकि धुंध दृश्यता को एक से दो किलोमीटर तक कम कर सकती है। ठण्डी सतहों पर पाला तब जमता है जब संघनन हिमांक बिंदु (0&deg;C) से नीचे होता है, अर्थात ओसांक बिंदु हिमांक बिंदु पर या उससे नीचे होता है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "18",
                    question_en: "<p>13. Which of the following movements created regional disparities between large and small farmers in India?</p>",
                    question_hi: "<p>13. निम्नलिखित में से किस आंदोलन ने भारत में बड़े और छोटे किसानों के बीच क्षेत्रीय असमानताएँ पैदा कीं?</p>",
                    options_en: ["<p>Green revolution</p>", "<p>Yellow revolution</p>", 
                                "<p>Pink revolution</p>", "<p>Blue revolution</p>"],
                    options_hi: ["<p>हरित क्रांति</p>", "<p>पीली क्रांति</p>",
                                "<p>गुलाबी क्रांति</p>", "<p>नीली क्रांति</p>"],
                    solution_en: "<p>13.(a) <strong>Green revolution.</strong> Green Revolution: Initiated by - Norman Borlaug (1960). He is known as the &lsquo;Father of Green Revolution&rsquo; in world. M.S. Swaminathan (Father of Green Revolution in India). The Green revolution came under the third five year plan (1961-66). The Green Revolution in India started with the aim of increasing India\'s agricultural production to make India self-sufficient in food grains.</p>",
                    solution_hi: "<p>13.(a) <strong>हरित क्रांति। </strong>हरित क्रांति की शुरुआत नॉर्मन बोरलॉग (1960) द्वारा की गई थी। उन्हें विश्व में \'हरित क्रांति के जनक\' के रूप में जाना जाता है। एम.एस. स्वामीनाथन (भारत में हरित क्रांति के जनक)। हरित क्रांति तीसरी पंचवर्षीय योजना (1961-66) के दौरान हुई थी। भारत में हरित क्रांति की शुरुआत भारत के कृषि उत्पादन को बढ़ाने के उद्देश्य से हुई ताकि भारत खाद्यान्न के मामले में आत्मनिर्भर बन सके।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "18",
                    question_en: "<p>14. Which national park in the Andaman and Nicobar Islands is home to species like Megapode, crab-eating Macaque, Nicobar Pigeon, and Giant Robber Crab?</p>",
                    question_hi: "<p>14. महापद पक्षी, केकड़ाभक्षी मकाक (बंदर), निकोबार कबूतर और विशालकाय दस्यु केकड़े जैसी प्रजातियों का घर है?</p>",
                    options_en: ["<p>Raimona National Park</p>", "<p>Campbell Bay National Park</p>", 
                                "<p>Mouling National Park</p>", "<p>Papikonda National Park</p>"],
                    options_hi: ["<p>रायमोना राष्ट्रीय उद्यान</p>", "<p>कैंपबेल बे राष्ट्रीय उद्यान</p>",
                                "<p>मौलिंग राष्ट्रीय उद्यान</p>", "<p>पापिकोंडा राष्ट्रीय उद्यान</p>"],
                    solution_en: "<p>14.(b) <strong>Campbell Bay National Park</strong>, established in 1992, spans 426.23 sq km and is part of the Great Nicobar Biosphere Reserve. Other notable parks include Raimona National Park in Assam (famous for elephants and tigers), Mouling National Park in Arunachal Pradesh (known for temperate forests and Himalayan wildlife), and Papikonda National Park in Andhra Pradesh (noted for Eastern Ghats biodiversity).</p>",
                    solution_hi: "<p>14.(b) <strong>कैंपबेल बे राष्ट्रीय उद्यान, </strong>1992 में स्थापित, 426.23 वर्ग किलोमीटर में फैला है और ग्रेट निकोबार बायोस्फीयर रिजर्व का हिस्सा है। अन्य उल्लेखनीय उद्यानों में असम में रायमोना राष्ट्रीय उद्यान (हाथियों और बाघों के लिए प्रसिद्ध), अरुणाचल प्रदेश में मौलिंग राष्ट्रीय उद्यान (शीतोष्ण वनों और हिमालयी वन्यजीवों के लिए जाना जाता है) और आंध्र प्रदेश में पापिकोंडा राष्ट्रीय उद्यान (पूर्वी घाट जैव विविधता के लिए प्रसिद्ध) शामिल हैं।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "misc",
                    question_en: "<p>15. What is the duration of monsoon in India?</p>",
                    question_hi: "<p>15. भारत में मानसून की अवधि कितनी होती है?</p>",
                    options_en: ["<p>100-120 days</p>", "<p>40-60 days</p>", 
                                "<p>150-200 days</p>", "<p>50-80 days</p>"],
                    options_hi: ["<p>100 - 120 दिन</p>", "<p>40 - 60 दिन</p>",
                                "<p>150 - 200 दिन</p>", "<p>50 - 80 दिन</p>"],
                    solution_en: "<p>15.(a) <strong>100-120 days.</strong> The monsoon season in India lasts from June to September. The southwest monsoon (summer monsoon) brings the majority of annual rainfall, blowing from the sea to land across the Indian Ocean, Arabian Sea, and Bay of Bengal. In contrast, the northeast monsoon (winter monsoon) blows from land to sea.</p>",
                    solution_hi: "<p>15.(a) <strong>100-120 दिन। </strong>भारत में मानसून का मौसम जून से सितंबर तक रहता है। दक्षिण-पश्चिमी मानसून (ग्रीष्मकालीन मानसून) वार्षिक वर्षा का अधिकांश भाग लाता है, जो हिंद महासागर, अरब सागर और बंगाल की खाड़ी के पार समुद्र से ज़मीन की ओर बहता है। इसके विपरीत, पूर्वोत्तर मानसून (शीतकालीन मानसून) भूमि से समुद्र की ओर बहता है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>