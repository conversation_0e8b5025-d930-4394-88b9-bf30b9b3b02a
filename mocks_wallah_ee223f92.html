<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">15:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 15</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">15</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 15 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 0,
                end: 13
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 14,
                end: 14
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "17",
                    question_en: "<p>1. Study the given three-dimensional chart and answer the question that follows. The chart details the sale of fruits in different months.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736836027924.png\" alt=\"rId4\" width=\"346\" height=\"272\"> <br>Which fruit was the most sold in the month of May and how much more than the lowest-sold fruit in that month ?</p>",
                    question_hi: "<p>1. दिए गए त्रि-विमीय चार्ट का अध्ययन कीजिए और नीचे दिए गए प्रश्नों का उत्तर दीजिए। <br>चार्ट विभिन्न महीनों में फलों की बिक्री का विवरण देता है।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736836028060.png\" alt=\"rId5\" width=\"381\" height=\"299\"> <br>मई के महीने में कौन-सा फल सबसे ज्यादा बिका और सबसे कम बिकने वाले फल से कितना ज्यादा बिका ?</p>",
                    options_en: ["<p>Kiwi, 23</p>", "<p>Orange, 23</p>", 
                                "<p>Apple, 28</p>", "<p>Kiwi, 22</p>"],
                    options_hi: ["<p>कीवी, 23</p>", "<p>संतरा, 23</p>",
                                "<p>सेब, 28</p>", "<p>कीवी, 22</p>"],
                    solution_en: "<p>1.(c)<br>Most sold fruit in may = apple (70 fruits)<br>Lowest sold fruit in may = kiwi (42 fruits)<br>Required difference = 70 - 42 = 28 fruits</p>",
                    solution_hi: "<p>1.(c)<br>मई में सर्वाधिक बिकने वाला फल = सेब (70 फल)<br>मई में सबसे कम बिकने वाला फल = कीवी (42 फल)<br>आवश्यक अंतर = 70 - 42 = 28 फल</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "17",
                    question_en: "<p>2. The following table shows admitted students in an institute during the academic years 2015 to 2020 in different courses.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736836028185.png\" alt=\"rId6\" width=\"457\" height=\"146\"> <br>Which of the following courses had either a consistent growth or a consistent decline in the admissions from 2015 to 2020 ?</p>",
                    question_hi: "<p>2. निम्न तालिका शैक्षणिक वर्ष 2015 से 2020 के दौरान एक संस्थान में विभिन्न पाठ्यक्रमों में प्रवेश लेने वाले छात्रों को दर्शाती है।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736836028381.png\" alt=\"rId7\" width=\"372\" height=\"155\"> <br>निम्नलिखित में से किस पाठ्यक्रम के प्रवेश में 2015 से 2020 तक या तो लगातार वृद्धि हुई या लगातार गिरावट आई ?&nbsp;</p>",
                    options_en: ["<p>Management</p>", "<p>Engineering</p>", 
                                "<p>Arts</p>", "<p>Science</p>"],
                    options_hi: ["<p>प्रबंधन</p>", "<p>अभियांत्रिकी</p>",
                                "<p>कला</p>", "<p>विज्ञान</p>"],
                    solution_en: "<p>2.(a)<br>Clearly, from given data <br>During 2015 to 2020 in <strong>&ldquo;management&rdquo;</strong> courses admissions have consistent growth.</p>",
                    solution_hi: "<p>2.(a)<br>स्पष्ट रूप से, दिए गए डेटा से <br>2015 से 2020 के दौरान<strong> &ldquo;प्रबंधन&rdquo; </strong>पाठ्यक्रमों में प्रवेश में लगातार वृद्धि हुई है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "17",
                    question_en: "<p>3. Study the given histogram and answer the questions that follow.<br>The histogram shows the distribution of marks obtained by 115 students in a college.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736836028496.png\" alt=\"rId8\" width=\"400\" height=\"229\"> <br>A student is marked as qualified if he/she obtains marks above 40.5. Here, the ratio of&nbsp;the number of qualified students to that of students who are not qualified is:</p>",
                    question_hi: "<p>3. दिए गए आयत चित्र का अध्ययन करें और नीचे दिए गए प्रश्नों के उत्तर दें।<br>आयत चित्र एक कॉलेज में 115 छात्रों द्वारा प्राप्त अंकों का वितरण दर्शाता है।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736836028592.png\" alt=\"rId9\" width=\"327\" height=\"250\"> <br>यदि कोई छात्र/छात्रा 40.5 से अधिक अंक प्राप्त करता/करती है, तो उसे अर्हताप्राप्त के रूप में चिह्नित किया जाता है। यहां अर्हताप्राप्त छात्रों की संख्या तथा अर्हता प्राप्त न करने वाले छात्रों की संख्या का अनुपात क्या है ?</p>",
                    options_en: ["<p>5 : 1</p>", "<p>17 : 4</p>", 
                                "<p>9 : 2</p>", "<p>19 : 4</p>"],
                    options_hi: ["<p>5 : 1</p>", "<p>17 : 4</p>",
                                "<p>9 : 2</p>", "<p>19 : 4</p>"],
                    solution_en: "<p>3.(d)<br>Class width = 10<br>Number of not qualified student (marks obtained less than 40.5)<br>= 0.5 &times; 10 + 1.5 &times; 10 = 20<br>Number of qualified student = 2.5 &times; 10 + 3 &times; 10 + 2 &times; 10 + 1 &times; 10 + 1 &times; 10 = 95<br>Hence, asked ratio = 95 : 20 = 19 : 4</p>",
                    solution_hi: "<p>3.(d)<br>कक्षा की चौड़ाई = 10<br>अर्हता प्राप्त न करने वाले छात्रों की संख्या (40.5 से कम प्राप्त अंक)<br>= 0.5 &times; 10 + 1.5 &times; 10 = 20<br>अर्हताप्राप्त छात्रों की संख्या = 2.5 &times; 10 + 3 &times; 10 + 2 &times; 10 + 1 &times; 10 + 1 &times; 10 = 95<br>अतः, पूछा गया अनुपात = 95 : 20 = 19 : 4</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "17",
                    question_en: "<p>4. The given graph shows the higher secondary result of a particular school, from 2019 to 2021. Study the graph and answer the question that follows.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736836028717.png\" alt=\"rId10\" width=\"401\" height=\"294\"> <br>The ratio of the number of students who passed with first division in all the 3 years to the total number of students in 2020 is:</p>",
                    question_hi: "<p>4. दिया गया ग्राफ़ 2019 से 2021 तक किसी विशेष स्कूल के उच्च माध्यमिक परिणाम को दर्शाता है। ग्राफ़ का अध्ययन कीजिए और निम्नलिखित प्रश्न का उत्तर दीजिए।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736836028850.png\" alt=\"rId11\" width=\"462\" height=\"339\"> <br>सभी 3 वर्षों में प्रथम श्रेणी के साथ उत्तीर्ण होने वाले विद्यार्थियों की संख्या का 2020 में विद्यार्थियों की कुल संख्या से अनुपात कितना है ?</p>",
                    options_en: ["<p>7 : 10</p>", "<p>17 : 10</p>", 
                                "<p>7 : 13</p>", "<p>7 : 15</p>"],
                    options_hi: ["<p>7 : 10</p>", "<p>17 : 10</p>",
                                "<p>7 : 13</p>", "<p>7 : 15</p>"],
                    solution_en: "<p>4.(a)<br>No. of student with 1<sup>st </sup>division (all three years) = 20 + 70 + 85 = 175<br>No. of students in 2020 = 70 + 80 + 95 + 5 = 250<br>Hence, required ratio = 175 : 250 = 7 : 10</p>",
                    solution_hi: "<p>4.(a)<br>प्रथम श्रेणी वाले विद्यार्थियों की संख्या (तीनों वर्ष में) = 20 + 70 + 85 = 175<br>2020 में छात्रों की संख्या = 70 + 80 + 95 + 5 = 250<br>अतः, आवश्यक अनुपात = 175 : 250 = 7 : 10</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "17",
                    question_en: "<p>5. Answer the following question on the basis of the bar graph given.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736836028992.png\" alt=\"rId12\" width=\"461\" height=\"309\"> <br>The number of years in which there was an increase in the revenue from at least two categories is:</p>",
                    question_hi: "<p>5. दिए गए बार ग्राफ के आधार पर निम्नलिखित प्रश्न का उत्तर दें। <br>प्रकाशक को समाचार-पत्रो, पत्रिकाओं और पुस्तकों से हुआ आमदनी<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736836029146.png\" alt=\"rId13\" width=\"444\" height=\"288\"> <br>उन वर्षों की संख्या ज्ञात करें जिनमें कम से कम दो श्रेणियों से आमदनी में वृ&zwnj;द्धि हुई।</p>",
                    options_en: ["<p>1</p>", "<p>2</p>", 
                                "<p>3</p>", "<p>4</p>"],
                    options_hi: ["<p>1</p>", "<p>2</p>",
                                "<p>3</p>", "<p>4</p>"],
                    solution_en: "<p>5.(b)<br>By checking the data of bar graph<br>In 2 years (1990 and 1991) revenue increased at least two categories.</p>",
                    solution_hi: "<p>5.(b)<br>बार ग्राफ़ के डेटा की जाँच करके<br>2 वर्षों (1990 और 1991) में राजस्व में कम से कम दो श्रेणियों में वृद्धि हुई।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "17",
                    question_en: "<p>6. The table shows the percentage distribution of the population (only male and female) according to Gender and Literacy.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736836029258.png\" alt=\"rId14\" width=\"341\" height=\"134\"> <br>The total population in state C, if the illiterate female count in state C is 4,40,000, is:</p>",
                    question_hi: "<p>6. दी गई तालिका लिंग और साक्षरता के अनुसार जनसंख्या (केवल पुरुष और महिला) का प्रतिशत बंटन दर्शाती है।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736836029364.png\" alt=\"rId15\" width=\"332\" height=\"127\"> <br>यदि राज्य C में निरक्षर महिलाओं की संख्या 4,40,000 है, तो राज्य C की कुल जनसंख्या ज्ञात करें।</p>",
                    options_en: ["<p>3600000</p>", "<p>5000000</p>", 
                                "<p>4600000</p>", "<p>6000000</p>"],
                    options_hi: ["<p>3600000</p>", "<p>5000000</p>",
                                "<p>4600000</p>", "<p>6000000</p>"],
                    solution_en: "<p>6.(b)<br>Illiterate (male + female) in state C = <math display=\"inline\"><mfrac><mrow><mn>440000</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> &times; 5 = 1100000<br>Illiterate (male + female) in state C = (100 - 78)% = 22%<br>22% = 1100000<br>Hence, total population of state C = 100% = <math display=\"inline\"><mfrac><mrow><mn>1100000</mn></mrow><mrow><mn>22</mn></mrow></mfrac></math> &times; 100 = 5000000</p>",
                    solution_hi: "<p>6.(b)<br>राज्य C में निरक्षर (पुरुष + महिला) = <math display=\"inline\"><mfrac><mrow><mn>440000</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> &times; 5 = 1100000<br>राज्य C में निरक्षर (पुरुष + महिला) = (100 - 78)% = 22%<br>22% = 1100000<br>अतः, राज्य C की कुल जनसंख्या = 100% = <math display=\"inline\"><mfrac><mrow><mn>1100000</mn></mrow><mrow><mn>22</mn></mrow></mfrac></math> &times; 100 = 5000000</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "17",
                    question_en: "<p>7. The following graph shows the demand and production of buses of five companies in 2022 (in thousands). Study the given graph carefully and answer the question that follows<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736836029467.png\" alt=\"rId16\" width=\"431\" height=\"297\"> <br>The number of companies whose production of buses is equal to or more than the average demand of buses of all five companies is:</p>",
                    question_hi: "<p>7. दिया गया ग्राफ, 2022 में पांच कंपनियों की बसों की मांग और उत्पादन (हजारों में) को दर्शाता है। दिए गए ग्राफ का ध्यानपूर्वक अध्ययन कीजिए और दिए गए प्रश्न का उत्तर दीजिए।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736836029569.png\" alt=\"rId17\" width=\"444\" height=\"306\"> <br>उन कंपनियों की संख्या ज्ञात कीजिए, जिनकी बसों का उत्पादन सभी पांच कंपनियों की बसों की औसत मांग के बराबर या उससे अधिक है।</p>",
                    options_en: ["<p>1</p>", "<p>2</p>", 
                                "<p>4</p>", "<p>3</p>"],
                    options_hi: ["<p>1</p>", "<p>2</p>",
                                "<p>4</p>", "<p>3</p>"],
                    solution_en: "<p>7.(d)<br>Average demand of buses of all five companies = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>150</mn><mo>+</mo><mn>145</mn><mo>+</mo><mn>160</mn><mo>+</mo><mn>165</mn><mo>+</mo><mn>155</mn></mrow><mn>5</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>775</mn><mn>5</mn></mfrac></math> = 155<br>A, C and D(3 Companies) have production equal to or more than average demand</p>",
                    solution_hi: "<p>7.(d)<br>पांचों कंपनियों की बसों की औसत मांग = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>150</mn><mo>+</mo><mn>145</mn><mo>+</mo><mn>160</mn><mo>+</mo><mn>165</mn><mo>+</mo><mn>155</mn></mrow><mn>5</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>775</mn><mn>5</mn></mfrac></math> = 155<br>A, C और D(3 कंपनियां) का उत्पादन औसत मांग के बराबर या उससे अधिक है</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "17",
                    question_en: "<p>8. A battery manufacturer manufactures five different types of batteries. The total revenue for the year 2020 is ₹25,00,000 and 20,000 units were exported in 2020. The distribution of revenue and units for the five different types of batteries is shown in the charts.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736836029747.png\" alt=\"rId18\" width=\"278\" height=\"213\"><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736836029913.png\" alt=\"rId19\" width=\"261\" height=\"221\"> <br>Which type of the battery provides the highest revenue per unit of export ?</p>",
                    question_hi: "<p>8. एक बैटरी निर्माता पांच अलग-अलग प्रकार की बैटरियों का निर्माण करता है। वर्ष 2020 के लिए कुल राजस्व ₹25,00,000 है और 2020 में 20,000 इकाइयों का निर्यात किया गया था। पांच अलग-अलग प्रकार की बैटरियों के लिए राजस्व और इकाइयों का वितरण चार्ट में दिखाया गया है।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736836030079.png\" alt=\"rId20\" width=\"318\" height=\"246\"> <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736836030197.png\" alt=\"rId21\" width=\"277\" height=\"247\"> <br>किस प्रकार की बैटरी निर्यात की प्रति इकाई उच्चतम राजस्व प्रदान करती है ?</p>",
                    options_en: ["<p>Lead acid</p>", "<p>Li-ion</p>", 
                                "<p>Sodium</p>", "<p>M-Air</p>"],
                    options_hi: ["<p>लेड ऐसिड</p>", "<p>ली-आयन</p>",
                                "<p>सोडियम</p>", "<p>एम-एयर</p>"],
                    solution_en: "<p>8.(c) <br>By checking options one by one<br><strong>For lead acid :-</strong> revenue = 2500000 &times; 25% = 625000 <br>Export = 20000 &times; 35% = 7000<br>Revenue per unit = <math display=\"inline\"><mfrac><mrow><mn>625000</mn></mrow><mrow><mn>7000</mn></mrow></mfrac></math> = 89.28<br><strong>For li - ion :- </strong>revenue = 2500000 &times; 35% = 875000 <br>Export = 20000 &times; 30% = 6000<br>Revenue per unit = <math display=\"inline\"><mfrac><mrow><mn>875000</mn></mrow><mrow><mn>6000</mn></mrow></mfrac></math> = 145.83<br><strong>For Sodium :- </strong>revenue = 2500000 &times; 5% = 125000 <br>Export = 20000 &times; 2% = 400<br>Revenue per unit = <math display=\"inline\"><mfrac><mrow><mn>125000</mn></mrow><mrow><mn>400</mn></mrow></mfrac></math> = 312.5<br><strong>For M - Air :-</strong> revenue = 2500000 &times; 20% = 500000 <br>Export = 20000 &times; 20% = 4000<br>Revenue per unit = <math display=\"inline\"><mfrac><mrow><mn>500000</mn></mrow><mrow><mn>4000</mn></mrow></mfrac></math> = 125<br>Hence, Sodium battery provides the highest revenue per unit of export.</p>",
                    solution_hi: "<p>8.(c) <br>एक-एक करके विकल्पों की जाँच करके<br><strong>लेड एसिड के लिए :-</strong> राजस्व = 2500000 &times; 25% = 625000 <br>निर्यात = 20000 &times; 35% = 7000<br>प्रति इकाई राजस्व = <math display=\"inline\"><mfrac><mrow><mn>625000</mn></mrow><mrow><mn>7000</mn></mrow></mfrac></math> = 89.28<br><strong>ली-आयन के लिए:- </strong>राजस्व = 2500000 &times; 35% = 875000 <br>निर्यात = 20000 &times; 30% = 6000<br>प्रति इकाई राजस्व = <math display=\"inline\"><mfrac><mrow><mn>875000</mn></mrow><mrow><mn>6000</mn></mrow></mfrac></math> = 145.83<br><strong>सोडियम के लिए:- </strong>राजस्व = 2500000 &times; 5% = 125000 <br>निर्यात = 20000 &times; 2% = 400<br>प्रति इकाई राजस्व = <math display=\"inline\"><mfrac><mrow><mn>125000</mn></mrow><mrow><mn>400</mn></mrow></mfrac></math> = 312.5<br><strong>एम-एयर के लिए :- </strong>राजस्व = 2500000 &times; 20% = 500000 <br>निर्यात = 20000 &times; 20% = 4000<br>प्रति इकाई राजस्व = <math display=\"inline\"><mfrac><mrow><mn>500000</mn></mrow><mrow><mn>4000</mn></mrow></mfrac></math> = 125<br>इसलिए, सोडियम बैटरी निर्यात की प्रति यूनिट सबसे अधिक राजस्व प्रदान करती है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "17",
                    question_en: "<p>9. Study the given bar-graph and answer the question that follows.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736836030277.png\" alt=\"rId22\" width=\"375\" height=\"306\"> <br>How many students obtained more than 135 marks ?</p>",
                    question_hi: "<p>9. दिए गए दंड आलेख का अध्ययन कीजिए और नीचे दिए गए प्रश्न का उत्तर दीजिए।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736836030390.png\" alt=\"rId23\" width=\"334\" height=\"270\"> <br>कितने छात्रों ने 135 से अधिक अंक प्राप्त किए ?</p>",
                    options_en: ["<p>17</p>", "<p>19</p>", 
                                "<p>13</p>", "<p>18</p>"],
                    options_hi: ["<p>17</p>", "<p>19</p>",
                                "<p>13</p>", "<p>18</p>"],
                    solution_en: "<p>9.(d)<br>Required students = (8 + 5 + 4 + 1) = 18</p>",
                    solution_hi: "<p>9.(d)<br>आवश्यक विद्यार्थी = (8 + 5 + 4 + 1) = 18</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "17",
                    question_en: "<p>10. The following table shows the number of employees working in four organizations during six years. Answer the questions that follow,&nbsp;<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736836030502.png\" alt=\"rId24\" width=\"381\" height=\"128\"> <br>What is the average number of employees in organizations Y, Z and T in 2013 ?</p>",
                    question_hi: "<p>10. निम्नलिखित तालिका छह वर्षों के दौरान चार संगठनों में कार्यरत कर्मचारियों की संख्या दर्शाती है।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736836030622.png\" alt=\"rId25\" width=\"295\" height=\"132\"> <br>2013 में संगठन Y, Z और T में कर्मचारियों की औसत संख्या कितनी है ?</p>",
                    options_en: ["<p>168</p>", "<p>166</p>", 
                                "<p>170</p>", "<p>172</p>"],
                    options_hi: ["<p>168</p>", "<p>166</p>",
                                "<p>170</p>", "<p>172</p>"],
                    solution_en: "<p>10.(b) <br>Required average = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>160</mn><mo>+</mo><mn>212</mn><mo>+</mo><mn>126</mn></mrow><mn>3</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>498</mn><mn>3</mn></mfrac></math> = 166</p>",
                    solution_hi: "<p>10.(b) <br>आवश्यक औसत = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>160</mn><mo>+</mo><mn>212</mn><mo>+</mo><mn>126</mn></mrow><mn>3</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>498</mn><mn>3</mn></mfrac></math> = 166</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "17",
                    question_en: "<p>11. Study the given bar-graph and answer the question that follows.<br>The bar-graph represents the number of boys and girls in different sections of class 12.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736836030733.png\" alt=\"rId26\" width=\"460\" height=\"327\"> <br>The average number of girls in each section is:</p>",
                    question_hi: "<p>11. दिए गए दंड-आलेख का अध्ययन कीजिए और नीचे दिए गए प्रश्न का उत्तर दीजिए। दंड आलेख, का 12 (XII) के विभिन्न वर्ग में लड़कों और लड़कियों की संख्या को दर्शाता है। <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736836030838.png\" alt=\"rId27\" width=\"411\" height=\"289\"> <br>प्रत्येक वर्ग में लड़कियों की औसत संख्या कितनी है ?</p>",
                    options_en: ["<p>42</p>", "<p>40</p>", 
                                "<p>43</p>", "<p>37</p>"],
                    options_hi: ["<p>42</p>", "<p>40</p>",
                                "<p>43</p>", "<p>37</p>"],
                    solution_en: "<p>11.(b)<br>Required average = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>34</mn><mo>+</mo><mn>41</mn><mo>+</mo><mn>40</mn><mo>+</mo><mn>39</mn><mo>+</mo><mn>45</mn><mo>+</mo><mn>41</mn></mrow><mn>6</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>240</mn><mn>6</mn></mfrac></math> = 40</p>",
                    solution_hi: "<p>11.(b)<br>आवश्यक औसत = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>34</mn><mo>+</mo><mn>41</mn><mo>+</mo><mn>40</mn><mo>+</mo><mn>39</mn><mo>+</mo><mn>45</mn><mo>+</mo><mn>41</mn></mrow><mn>6</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>240</mn><mn>6</mn></mfrac></math> = 40</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "17",
                    question_en: "<p>12. The pie chart below depicts the percentage distribution of the costs associated with publishing a book.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736836030977.png\" alt=\"rId28\" width=\"435\" height=\"257\"> <br>What will be the royalty rate for these books if the publisher is required to pay Rs.30,600 in printing costs for a specific number of volumes ?</p>",
                    question_hi: "<p>12. नीचे दिया गया पाई चार्ट किसी पुस्तक के प्रकाशन से जुड़ी लागतों के प्रतिशत वितरण को दर्शाता है।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736836031086.png\" alt=\"rId29\" width=\"419\" height=\"248\"> <br>यदि प्रकाशक को विशिष्ट संख्या में संस्करणों के लिए मुद्रण लागत में 30,600 रुपये का भुगतान करना पड़ता है तो इन पुस्तकों के लिए रॉयल्टी दर क्या होगी ?</p>",
                    options_en: ["<p>Rs.21,200</p>", "<p>Rs.19,450</p>", 
                                "<p>Rs.26,150</p>", "<p>Rs.22,950</p>"],
                    options_hi: ["<p>Rs.21,200</p>", "<p>Rs.19,450</p>",
                                "<p>Rs.26,150</p>", "<p>Rs.22,950</p>"],
                    solution_en: "<p>12(d)<br>Royalty rate = 30600 &times; <math display=\"inline\"><mfrac><mrow><mn>15</mn></mrow><mrow><mn>20</mn></mrow></mfrac></math> = Rs.22,950</p>",
                    solution_hi: "<p>12.(d)<br>रॉयल्टी दर = 30600 &times; <math display=\"inline\"><mfrac><mrow><mn>15</mn></mrow><mrow><mn>20</mn></mrow></mfrac></math> = Rs.22,950</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "17",
                    question_en: "<p>13. Read the given information and answer the question that follows.<br>The following table gives the percentage of marks obtained by seven students in six different subjects in an examination.<br>The number in the brackets gives the maximum marks in each subject.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736836031218.png\" alt=\"rId30\" width=\"506\" height=\"184\"> <br>What is the median raw score of the marks obtained by the seven students in Chemistry ?</p>",
                    question_hi: "<p>13. दी गई जानकारी को ध्यानपूर्वक पढ़ें और आगे दिए गए प्रश्न का उत्तर दें।<br>नीचे दी गई तालिका में एक परीक्षा के छह अलग-अलग विषयों में सात विद्यार्थियों द्वारा प्राप्त अंकों का प्रतिशत दर्शाया गया है।<br>कोष्ठक में दी गई संख्या प्रत्येक विषय के अधिकतम अंक को दर्शाती है।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736836031347.png\" alt=\"rId31\" width=\"420\" height=\"211\"> <br>रसायन में सात विद्यार्थियों द्वारा प्राप्त अंकों का माध्यिका यथाप्राप्त समंक (Raw Score) ज्ञात करें।</p>",
                    options_en: ["<p>65</p>", "<p>84.5</p>", 
                                "<p>81.25</p>", "<p>62.5</p>"],
                    options_hi: ["<p>65</p>", "<p>84.5</p>",
                                "<p>81.25</p>", "<p>62.5</p>"],
                    solution_en: "<p>13.(b)<br>If a data set has an odd number of values, you should first arrange the values in ascending order. <br>35, 50, 60, <strong>65</strong>, 65, 75, 80<br>Median = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>n</mi><mo>+</mo><mn>1</mn></mrow><mn>2</mn></mfrac></math> (for odd number of values)<br>Median = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>7</mn><mo>+</mo><mn>1</mn></mrow><mn>2</mn></mfrac></math> = 4th term<br>Median = 130 &times; <math display=\"inline\"><mfrac><mrow><mn>65</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 84.5</p>",
                    solution_hi: "<p>13.(b)<br>यदि किसी डेटा सेट में विषम संख्या में मान हैं, तो आपको पहले मानों को आरोही क्रम में व्यवस्थित करना चाहिए।<br>35, 50, 60, 65, 65, 75, 80<br>माध्यिका = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>n</mi><mo>+</mo><mn>1</mn></mrow><mn>2</mn></mfrac></math>&nbsp;(मूल्यों की विषम संख्या के लिए)<br>माध्यिका = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>7</mn><mo>+</mo><mn>1</mn></mrow><mn>2</mn></mfrac></math> = चौथा पद<br>माध्यिका = 130 &times; <math display=\"inline\"><mfrac><mrow><mn>65</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 84.5</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "17",
                    question_en: "<p>14. Study the following bar chart, which represents the income and expenditure of a company.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736836031454.png\" alt=\"rId32\" width=\"371\" height=\"289\"> <br>In how many years is the income of company less than the average income ?</p>",
                    question_hi: "<p>14. निम्नलिखित बार चार्ट का अध्ययन कीजिए, जो एक कंपनी की आय और व्यय को निरूपित करता है।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736836031606.png\" alt=\"rId33\" width=\"382\" height=\"303\"> <br>कितने वर्षों में कंपनी की आय, औसत आय से कम रही है ?</p>",
                    options_en: ["<p>4</p>", "<p>5</p>", 
                                "<p>3</p>", "<p>2</p>"],
                    options_hi: ["<p>4</p>", "<p>5</p>",
                                "<p>3</p>", "<p>2</p>"],
                    solution_en: "<p>14.(d)<br>Average income of the company = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>60</mn><mo>+</mo><mn>60</mn><mo>+</mo><mn>40</mn><mo>+</mo><mn>50</mn><mo>+</mo><mn>30</mn></mrow><mn>5</mn></mfrac></math> = 48<br>We can clearly see that 2 companies (in 2002 and 2004) have less income than average income.</p>",
                    solution_hi: "<p>14.(d)<br>कंम्पनी की औसत आय = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>60</mn><mo>+</mo><mn>60</mn><mo>+</mo><mn>40</mn><mo>+</mo><mn>50</mn><mo>+</mo><mn>30</mn></mrow><mn>5</mn></mfrac></math> = 48<br>हम स्पष्ट रूप से देख सकते है कि 2 कंपनियों ( 2002 और 2004 मे ) की आय औसत आय से कम है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "misc",
                    question_en: "<p>15. The given bar graph shows the percentage of production of three companies C1, C2 and C3 over five years. Study the bar graph and answer the question that follows.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736836031716.png\" alt=\"rId34\" width=\"363\" height=\"286\"> <br>If the ratio of the total production in the year 2019 and 2022 is 3 : 5 and the total production in C1 in 2022 is 4125, then the total production of C3 in the year 2019 is:</p>",
                    question_hi: "<p>15. दिए गए बार ग्राफ में पाँच वर्षों में तीन कंपनियों C1, C2 और C3 के प्रतिशत उत्पादन को दर्शाया गया है। बार ग्राफ का अध्ययन करें और आगे दिए गए प्रश्न का उत्तर दें।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736836031716.png\" alt=\"rId34\" width=\"343\" height=\"270\"> <br>यदि वर्ष 2019 और 2022 में कुल उत्पादन का अनुपात 3 : 5 है और 2022 में C1 में कुल उत्पादन 4125 है, तो वर्ष 2019 में C3 का कुल उत्पादन ज्ञात करें।</p>",
                    options_en: ["<p>9000</p>", "<p>1575</p>", 
                                "<p>2250</p>", "<p>5175</p>"],
                    options_hi: ["<p>9000</p>", "<p>1575</p>",
                                "<p>2250</p>", "<p>5175</p>"],
                    solution_en: "<p>15.(b) <br>According to the question,<br>Let the total production in the year 2019 and 2022 is 3 and 5 units respectively.<br>Total production in C1 in 2022 = 5 &times; <math display=\"inline\"><mfrac><mrow><mn>27</mn><mo>.</mo><mn>5</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 1.375 units<br>1.375 units = 4125<br>Total production of C3 in 2019 = <math display=\"inline\"><mfrac><mrow><mn>4125</mn></mrow><mrow><mn>1</mn><mo>.</mo><mn>375</mn></mrow></mfrac></math> &times; (3 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>17</mn><mo>.</mo><mn>5</mn></mrow><mn>100</mn></mfrac></math>) = 1575</p>",
                    solution_hi: "<p>15.(b) <br>प्रश्न के अनुसार,<br>माना वर्ष 2019 और 2022 में कुल उत्पादन क्रमशः 3 और 5 इकाई है।<br>2022 में C1 में कुल उत्पादन = 5 &times; <math display=\"inline\"><mfrac><mrow><mn>27</mn><mo>.</mo><mn>5</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 1.375 इकाई <br>1.375 इकाई = 4125<br>2019 में C3 का कुल उत्पादन = <math display=\"inline\"><mfrac><mrow><mn>4125</mn></mrow><mrow><mn>1</mn><mo>.</mo><mn>375</mn></mrow></mfrac></math> &times; (3 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>17</mn><mo>.</mo><mn>5</mn></mrow><mn>100</mn></mfrac></math>) = 1575</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>